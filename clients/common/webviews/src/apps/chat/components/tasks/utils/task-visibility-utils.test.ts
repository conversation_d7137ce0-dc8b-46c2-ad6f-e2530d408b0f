import { describe, it, expect } from "vitest";
import {
  TaskState,
  type HydratedTask,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import {
  preprocessTaskTreeVisibility,
  getVisibleSubTasks,
  type VisibleHydratedTask,
} from "./task-visibility-utils";

// Helper function to create a mock task
function createMockTask(
  uuid: string,
  state: TaskState,
  name: string = "Test Task",
  subTasks: HydratedTask[] = [],
): HydratedTask {
  return {
    uuid,
    name,
    description: `Description for ${name}`,
    state,
    subTasks: subTasks.map((t) => t.uuid),
    subTasksData: subTasks,
    lastUpdated: Date.now(),
    lastUpdatedBy: "USER" as any,
  };
}

// Helper function to create a mock VisibleHydratedTask
function createMockVisibleTask(
  uuid: string,
  state: TaskState,
  isVisible: boolean,
  name: string = "Test Task",
  subTasks: VisibleHydratedTask[] = [],
): VisibleHydratedTask {
  return {
    uuid,
    name,
    description: `Description for ${name}`,
    state,
    subTasks: subTasks.map((t) => t.uuid),
    subTasksData: subTasks,
    lastUpdated: Date.now(),
    lastUpdatedBy: "USER" as any,
    isVisible,
  };
}

describe("task-visibility-utils", () => {
  describe("preprocessTaskTreeVisibility", () => {
    describe("empty filter set (show all)", () => {
      it("should show all tasks when filter set is empty", () => {
        const task = createMockTask("1", TaskState.NOT_STARTED);
        const result = preprocessTaskTreeVisibility(task, new Set());

        expect(result.isVisible).toBe(true);
        expect(result.uuid).toBe("1");
        expect(result.state).toBe(TaskState.NOT_STARTED);
      });

      it("should show all tasks in a tree when filter set is empty", () => {
        const subtask1 = createMockTask("2", TaskState.IN_PROGRESS);
        const subtask2 = createMockTask("3", TaskState.COMPLETE);
        const rootTask = createMockTask("1", TaskState.NOT_STARTED, "Root", [subtask1, subtask2]);

        const result = preprocessTaskTreeVisibility(rootTask, new Set());

        expect(result.isVisible).toBe(true);
        expect(result.subTasksData).toHaveLength(2);
        expect(result.subTasksData![0].isVisible).toBe(true);
        expect(result.subTasksData![1].isVisible).toBe(true);
      });
    });

    describe("single filter", () => {
      it("should show only matching tasks with single filter", () => {
        const subtask1 = createMockTask("2", TaskState.IN_PROGRESS);
        const subtask2 = createMockTask("3", TaskState.COMPLETE);
        const rootTask = createMockTask("1", TaskState.NOT_STARTED, "Root", [subtask1, subtask2]);

        const filterSet = new Set([TaskState.IN_PROGRESS]);
        const result = preprocessTaskTreeVisibility(rootTask, filterSet);

        // Root should be visible because it has a visible descendant
        expect(result.isVisible).toBe(true);
        expect(result.subTasksData).toHaveLength(2);
        expect(result.subTasksData![0].isVisible).toBe(true); // IN_PROGRESS matches
        expect(result.subTasksData![1].isVisible).toBe(false); // COMPLETE doesn't match
      });

      it("should hide parent when no descendants match", () => {
        const subtask1 = createMockTask("2", TaskState.COMPLETE);
        const subtask2 = createMockTask("3", TaskState.CANCELLED);
        const rootTask = createMockTask("1", TaskState.NOT_STARTED, "Root", [subtask1, subtask2]);

        const filterSet = new Set([TaskState.IN_PROGRESS]);
        const result = preprocessTaskTreeVisibility(rootTask, filterSet);

        // Root should be hidden because no descendants match and root doesn't match
        expect(result.isVisible).toBe(false);
        expect(result.subTasksData![0].isVisible).toBe(false);
        expect(result.subTasksData![1].isVisible).toBe(false);
      });
    });

    describe("multiple filters", () => {
      it("should show tasks matching any filter in the set", () => {
        const subtask1 = createMockTask("2", TaskState.IN_PROGRESS);
        const subtask2 = createMockTask("3", TaskState.COMPLETE);
        const subtask3 = createMockTask("4", TaskState.CANCELLED);
        const rootTask = createMockTask("1", TaskState.NOT_STARTED, "Root", [
          subtask1,
          subtask2,
          subtask3,
        ]);

        const filterSet = new Set([TaskState.IN_PROGRESS, TaskState.COMPLETE]);
        const result = preprocessTaskTreeVisibility(rootTask, filterSet);

        expect(result.isVisible).toBe(true); // Has visible descendants
        expect(result.subTasksData![0].isVisible).toBe(true); // IN_PROGRESS matches
        expect(result.subTasksData![1].isVisible).toBe(true); // COMPLETE matches
        expect(result.subTasksData![2].isVisible).toBe(false); // CANCELLED doesn't match
      });
    });

    describe("nested task trees", () => {
      it("should handle deeply nested task trees", () => {
        const deepSubtask = createMockTask("4", TaskState.COMPLETE);
        const midSubtask = createMockTask("3", TaskState.NOT_STARTED, "Mid", [deepSubtask]);
        const topSubtask = createMockTask("2", TaskState.IN_PROGRESS, "Top", [midSubtask]);
        const rootTask = createMockTask("1", TaskState.NOT_STARTED, "Root", [topSubtask]);

        const filterSet = new Set([TaskState.COMPLETE]);
        const result = preprocessTaskTreeVisibility(rootTask, filterSet);

        // All ancestors should be visible because of the deep matching task
        expect(result.isVisible).toBe(true);
        expect(result.subTasksData![0].isVisible).toBe(true);
        expect(result.subTasksData![0].subTasksData![0].isVisible).toBe(true);
        expect(result.subTasksData![0].subTasksData![0].subTasksData![0].isVisible).toBe(true);
      });

      it("should handle multiple branches with different visibility", () => {
        const branch1Deep = createMockTask("4", TaskState.COMPLETE);
        const branch1Mid = createMockTask("3", TaskState.NOT_STARTED, "Branch1", [branch1Deep]);

        const branch2Deep = createMockTask("6", TaskState.CANCELLED);
        const branch2Mid = createMockTask("5", TaskState.NOT_STARTED, "Branch2", [branch2Deep]);

        const rootTask = createMockTask("1", TaskState.NOT_STARTED, "Root", [
          branch1Mid,
          branch2Mid,
        ]);

        const filterSet = new Set([TaskState.COMPLETE]);
        const result = preprocessTaskTreeVisibility(rootTask, filterSet);

        expect(result.isVisible).toBe(true); // Has visible descendants in branch1
        expect(result.subTasksData![0].isVisible).toBe(true); // Branch1 has visible descendant
        expect(result.subTasksData![1].isVisible).toBe(false); // Branch2 has no visible descendants
      });
    });

    describe("edge cases", () => {
      it("should handle task with no subtasks", () => {
        const task = createMockTask("1", TaskState.IN_PROGRESS);
        const filterSet = new Set([TaskState.COMPLETE]);
        const result = preprocessTaskTreeVisibility(task, filterSet);

        expect(result.isVisible).toBe(false);
        expect(result.subTasksData).toEqual([]);
      });

      it("should handle task with empty subtasks array", () => {
        const task = createMockTask("1", TaskState.IN_PROGRESS);
        task.subTasksData = [];
        const filterSet = new Set([TaskState.COMPLETE]);
        const result = preprocessTaskTreeVisibility(task, filterSet);

        expect(result.isVisible).toBe(false);
        expect(result.subTasksData).toEqual([]);
      });

      it("should preserve original task properties", () => {
        const task = createMockTask("1", TaskState.IN_PROGRESS, "Original Name");
        const result = preprocessTaskTreeVisibility(task, new Set());

        expect(result.uuid).toBe(task.uuid);
        expect(result.name).toBe(task.name);
        expect(result.description).toBe(task.description);
        expect(result.state).toBe(task.state);
        expect(result.lastUpdated).toBe(task.lastUpdated);
        expect(result.lastUpdatedBy).toBe(task.lastUpdatedBy);
      });
    });
  });

  describe("getVisibleSubTasks", () => {
    it("should return empty array for task with no subtasks", () => {
      const visibleTask = createMockVisibleTask("1", TaskState.NOT_STARTED, true);

      const result = getVisibleSubTasks(visibleTask);
      expect(result).toEqual([]);
    });

    it("should return empty array for task with undefined subtasks", () => {
      const visibleTask = createMockVisibleTask("1", TaskState.NOT_STARTED, true);
      visibleTask.subTasksData = undefined;

      const result = getVisibleSubTasks(visibleTask);
      expect(result).toEqual([]);
    });

    it("should filter out invisible subtasks", () => {
      const visibleTask = createMockVisibleTask("1", TaskState.NOT_STARTED, true, "Test Task", [
        createMockVisibleTask("2", TaskState.IN_PROGRESS, true),
        createMockVisibleTask("3", TaskState.COMPLETE, false),
        createMockVisibleTask("4", TaskState.CANCELLED, true),
      ]);

      const result = getVisibleSubTasks(visibleTask);
      expect(result).toHaveLength(2);
      expect(result[0].uuid).toBe("2");
      expect(result[1].uuid).toBe("4");
    });

    it("should return all subtasks when all are visible", () => {
      const visibleTask = createMockVisibleTask("1", TaskState.NOT_STARTED, true, "Test Task", [
        createMockVisibleTask("2", TaskState.IN_PROGRESS, true),
        createMockVisibleTask("3", TaskState.COMPLETE, true),
      ]);

      const result = getVisibleSubTasks(visibleTask);
      expect(result).toHaveLength(2);
      expect(result[0].uuid).toBe("2");
      expect(result[1].uuid).toBe("3");
    });

    it("should return empty array when no subtasks are visible", () => {
      const visibleTask = createMockVisibleTask("1", TaskState.NOT_STARTED, true, "Test Task", [
        createMockVisibleTask("2", TaskState.IN_PROGRESS, false),
        createMockVisibleTask("3", TaskState.COMPLETE, false),
      ]);

      const result = getVisibleSubTasks(visibleTask);
      expect(result).toEqual([]);
    });
  });

  describe("property-based tests", () => {
    // Generate random task states
    function randomTaskState(): TaskState {
      const states = Object.values(TaskState);
      return states[Math.floor(Math.random() * states.length)];
    }

    // Generate random filter sets
    function randomFilterSet(): Set<TaskState> {
      const states = Object.values(TaskState);
      const filterSet = new Set<TaskState>();

      // Randomly include each state (0-100% chance)
      states.forEach((state) => {
        if (Math.random() > 0.5) {
          filterSet.add(state);
        }
      });

      return filterSet;
    }

    // Generate random task trees
    function generateRandomTaskTree(depth: number = 0, maxDepth: number = 3): HydratedTask {
      const uuid = crypto.randomUUID();
      const state = randomTaskState();
      const name = `Task-${uuid.slice(0, 8)}`;

      let subTasks: HydratedTask[] = [];
      if (depth < maxDepth && Math.random() > 0.6) {
        const numSubTasks = Math.floor(Math.random() * 4); // 0-3 subtasks
        for (let i = 0; i < numSubTasks; i++) {
          subTasks.push(generateRandomTaskTree(depth + 1, maxDepth));
        }
      }

      return createMockTask(uuid, state, name, subTasks);
    }

    it("should maintain tree structure integrity", () => {
      // Run multiple iterations with random data
      for (let i = 0; i < 50; i++) {
        const randomTree = generateRandomTaskTree();
        const randomFilter = randomFilterSet();

        const result = preprocessTaskTreeVisibility(randomTree, randomFilter);

        // Basic integrity checks
        expect(result.uuid).toBe(randomTree.uuid);
        expect(result.state).toBe(randomTree.state);
        expect(result.name).toBe(randomTree.name);
        expect(typeof result.isVisible).toBe("boolean");

        // If original had subtasks, result should have same number (but with visibility flags)
        if (randomTree.subTasksData) {
          expect(result.subTasksData).toHaveLength(randomTree.subTasksData.length);
        } else {
          // If original had no subtasks, result should have empty array
          expect(result.subTasksData).toEqual([]);
        }
      }
    });

    it("should respect filter set logic consistently", () => {
      for (let i = 0; i < 30; i++) {
        const randomTree = generateRandomTaskTree();
        const randomFilter = randomFilterSet();

        const result = preprocessTaskTreeVisibility(randomTree, randomFilter);

        // If filter is empty, root should always be visible
        if (randomFilter.size === 0) {
          expect(result.isVisible).toBe(true);
        }

        // If root matches filter and has no subtasks, it should be visible
        if (
          randomFilter.has(randomTree.state) &&
          (!randomTree.subTasksData || randomTree.subTasksData.length === 0)
        ) {
          expect(result.isVisible).toBe(true);
        }
      }
    });

    it("should ensure parent visibility when child is visible", () => {
      // Check the parent-child visibility relationship
      function checkParentChildVisibility(task: VisibleHydratedTask, filterSet: Set<TaskState>) {
        if (task.subTasksData) {
          const hasVisibleChild = task.subTasksData.some((child) => child.isVisible);
          const matchesFilter = filterSet.size === 0 || filterSet.has(task.state);

          // If task has visible children OR matches filter, it should be visible
          if (hasVisibleChild || matchesFilter) {
            expect(task.isVisible).toBe(true);
          }

          // Recursively check children
          task.subTasksData.forEach((child) => checkParentChildVisibility(child, filterSet));
        }
      }

      for (let i = 0; i < 20; i++) {
        const randomTree = generateRandomTaskTree(0, 2); // Limit depth for this test
        const randomFilter = randomFilterSet();

        const result = preprocessTaskTreeVisibility(randomTree, randomFilter);

        checkParentChildVisibility(result, randomFilter);
      }
    });

    it("should handle all possible filter combinations", () => {
      const allStates = Object.values(TaskState);
      const simpleTask = createMockTask("test", TaskState.IN_PROGRESS);

      // Test all possible filter combinations (2^4 = 16 combinations)
      for (let mask = 0; mask < 1 << allStates.length; mask++) {
        const filterSet = new Set<TaskState>();

        for (let j = 0; j < allStates.length; j++) {
          if (mask & (1 << j)) {
            filterSet.add(allStates[j]);
          }
        }

        const result = preprocessTaskTreeVisibility(simpleTask, filterSet);

        // Task should be visible if filter is empty OR contains the task's state
        const shouldBeVisible = filterSet.size === 0 || filterSet.has(simpleTask.state);
        expect(result.isVisible).toBe(shouldBeVisible);
      }
    });

    it("should be deterministic with same inputs", () => {
      const fixedTree = createMockTask("fixed", TaskState.IN_PROGRESS, "Fixed Task", [
        createMockTask("child1", TaskState.COMPLETE),
        createMockTask("child2", TaskState.CANCELLED),
      ]);
      const fixedFilter = new Set([TaskState.COMPLETE, TaskState.IN_PROGRESS]);

      // Run the same test multiple times
      const results = [];
      for (let i = 0; i < 10; i++) {
        results.push(preprocessTaskTreeVisibility(fixedTree, fixedFilter));
      }

      // All results should be identical
      for (let i = 1; i < results.length; i++) {
        expect(results[i].isVisible).toBe(results[0].isVisible);
        expect(results[i].subTasksData![0].isVisible).toBe(results[0].subTasksData![0].isVisible);
        expect(results[i].subTasksData![1].isVisible).toBe(results[0].subTasksData![1].isVisible);
      }
    });

    it("should handle getVisibleSubTasks with random data", () => {
      for (let i = 0; i < 30; i++) {
        const randomTree = generateRandomTaskTree(0, 1); // Only one level for this test
        const randomFilter = randomFilterSet();

        const processedTree = preprocessTaskTreeVisibility(randomTree, randomFilter);
        const visibleSubTasks = getVisibleSubTasks(processedTree);

        // All returned subtasks should be visible
        visibleSubTasks.forEach((subtask) => {
          expect(subtask.isVisible).toBe(true);
        });

        // Count should match the number of visible subtasks in the processed tree
        if (processedTree.subTasksData) {
          const expectedCount = processedTree.subTasksData.filter((t) => t.isVisible).length;
          expect(visibleSubTasks).toHaveLength(expectedCount);
        } else {
          expect(visibleSubTasks).toHaveLength(0);
        }
      }
    });
  });
});

<script lang="ts">
  import { getContext } from "svelte";
  import CollapseButtonAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapseButtonAugment.svelte";
  import SwitchModeButton from "../../buttons/SwitchModeButton.svelte";
  import TaskListHeaderActions from "./TaskListHeaderActions.svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../../models/task-store";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import TaskStatusBadge from "../task-status/TaskStatusBadge.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Play from "$common-webviews/src/design-system/icons/augment/play.svelte";
  import Plus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import TaskFilterDropdown, {
    type TaskFilter,
    type TaskFilterOption,
  } from "../task-filters/TaskFilterDropdown.svelte";
  import { getTaskStatusLabel } from "../utils/task-status-utils";
  import ButtonChaserAugment from "$common-webviews/src/design-system/components/ButtonChaserAugment.svelte";

  // Props
  export let notStartedTasksCount: number = 0;
  export let inProgressTasksCount: number = 0;
  export let completedTasksCount: number = 0;
  export let cancelledTasksCount: number = 0;
  export let isEnhancing: boolean = false;
  export let hasNoTasks: boolean = false;

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { rootTask } = taskStore;

  let overallTaskState = TaskState.NOT_STARTED;

  async function handleAddNewTask() {
    if ($rootTask) {
      const uuid = await taskStore.createTask("", "", $rootTask.uuid);
      // Focus the new task
      setTimeout(() => {
        const taskItem = document.getElementById(`task-${uuid}`);
        const taskName = taskItem?.querySelector(".c-task-tree-item__name-editable");
        const input = taskName?.querySelector("input");
        input?.focus();
      }, 100);
    }
  }

  // Filter state - make it available to parent components - now supports multiple selections
  export let activeTaskFilter: Set<TaskState> = new Set();

  // Create filter options based on task counts (removed "all" option)
  $: taskFilters = [
    {
      value: TaskState.NOT_STARTED as TaskFilter,
      label: getTaskStatusLabel(TaskState.NOT_STARTED),
      count: notStartedTasksCount,
    },
    {
      value: TaskState.IN_PROGRESS as TaskFilter,
      label: getTaskStatusLabel(TaskState.IN_PROGRESS),
      count: inProgressTasksCount,
    },
    {
      value: TaskState.COMPLETE as TaskFilter,
      label: getTaskStatusLabel(TaskState.COMPLETE),
      count: completedTasksCount,
    },
    {
      value: TaskState.CANCELLED as TaskFilter,
      label: getTaskStatusLabel(TaskState.CANCELLED),
      count: cancelledTasksCount,
    },
  ] as TaskFilterOption[];

  $: {
    // Determine the overall task state based on counts
    if (inProgressTasksCount > 0) {
      overallTaskState = TaskState.IN_PROGRESS;
    } else if (notStartedTasksCount > 0) {
      overallTaskState = TaskState.NOT_STARTED;
    } else if (completedTasksCount > 0) {
      overallTaskState = TaskState.COMPLETE;
    } else if (cancelledTasksCount > 0) {
      overallTaskState = TaskState.CANCELLED;
    } else {
      overallTaskState = TaskState.NOT_STARTED;
    }
  }
</script>

<div class="c-task-list-header">
  <div class="c-task-list-header__content">
    <div class="c-task-list-header__collapse-btn">
      <CollapseButtonAugment />
    </div>
    <div class="c-task-list-header__switch-btn">
      <SwitchModeButton />
    </div>
    <TaskStatusBadge
      hideDropdown
      taskUuid={"overall"}
      taskState={overallTaskState}
      {taskStore}
      size={1}
    />
  </div>
  <div class="c-task-list-header__actions">
    <TextTooltipAugment content="Run all tasks" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="accent"
        disabled={isEnhancing}
        on:click={() => taskStore.runAllTasks()}
      >
        <Play />
      </IconButtonAugment>
    </TextTooltipAugment>
    <TextTooltipAugment content="Add new task" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        disabled={isEnhancing || !$rootTask}
        on:click={handleAddNewTask}
      >
        {#if hasNoTasks}
          <ButtonChaserAugment startOnMount repeat={20} speed={3} tailLength={15} />
        {/if}
        <Plus />
      </IconButtonAugment>
    </TextTooltipAugment>
    <TaskFilterDropdown {taskFilters} bind:activeTaskFilter />
    <TaskListHeaderActions {isEnhancing} />
  </div>
</div>

<style>
  .c-task-list-header {
    display: flex;
    align-items: center;
    width: 100%;
    min-width: 0;
    max-width: 100%;
    overflow: hidden;
    padding: var(--ds-spacing-1);
    gap: var(--ds-spacing-2);
    justify-content: space-between;
  }

  .c-task-list-header__content {
    display: flex;
    gap: var(--ds-spacing-1);
    min-width: 0;
    max-width: 100%;
    overflow: hidden;
    flex: 1 1 auto;
    align-items: center;
    justify-content: flex-start;
  }

  .c-task-list-header__collapse-btn,
  .c-task-list-header__switch-btn {
    flex-shrink: 0;
  }

  .c-task-list-header__actions {
    display: flex;
    align-items: center;
    min-width: max-content;
    flex-shrink: 0;
  }
</style>

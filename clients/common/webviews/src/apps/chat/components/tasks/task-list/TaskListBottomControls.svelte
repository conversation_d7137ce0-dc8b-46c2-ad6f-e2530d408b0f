<script lang="ts">
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import CirclePlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-plus.svg?component";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import { getContext } from "svelte";
  import { CurrentConversationTaskStore } from "../../../models/task-store";

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { rootTask, isEnhancing } = taskStore;

  async function handleAddTask() {
    if ($rootTask) {
      const uuid = await taskStore.createTask("", "", $rootTask.uuid);
      // Focus the new task
      const taskItem = document.getElementById(`task-${uuid}`);
      const taskName = taskItem?.querySelector(".c-task-tree-item__name-editable");

      const input = taskName?.querySelector("input");
      input?.focus();
    }
  }
</script>

<div class="c-task-list-bottom-controls">
  <ButtonAugment
    class="c-task-list-bottom-controls__add-btn"
    variant="soft"
    color="neutral"
    size={1}
    disabled={!$rootTask || $isEnhancing}
    on:click={handleAddTask}
  >
    <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} content="Add a new task">
      <CirclePlusIcon />
      Create New Task
    </TextTooltipAugment>
  </ButtonAugment>
</div>

<style>
  .c-task-list-bottom-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-1);
    gap: var(--ds-spacing-1);

    & .l-tooltip-trigger {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--ds-spacing-1);
    }

    & .c-task-list-bottom-controls__add-btn {
      & .c-button--content {
        width: 100%;
      }
      flex: 1;
    }
  }
</style>

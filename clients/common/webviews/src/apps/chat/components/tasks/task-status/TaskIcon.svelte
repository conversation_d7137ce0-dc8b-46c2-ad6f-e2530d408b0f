<script lang="ts">
  import type { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import type { ButtonSize } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import { getTaskStatusColor, getTaskStatusIcon } from "../utils/task-status-utils";

  /** The current state of the task */
  export let taskState: TaskState;
  export let size: ButtonSize = 1;

  /* eslint-disable @typescript-eslint/naming-convention */
  $: width = {
    1: "14px",
    2: "16px",
    3: "18px",
    4: "20px",
  }[size];
  /* eslint-enable @typescript-eslint/naming-convention */
  $: height = width;
  $: StatusIcon = getTaskStatusIcon(taskState);
</script>

<span
  class="c-task-icon"
  style="--icon-color: var(--ds-color-{getTaskStatusColor(taskState)}-9); --icon-size: {width};"
>
  <svelte:component this={StatusIcon} {width} {height} />
</span>

<style>
  .c-task-icon {
    display: contents;
    color: var(--icon-color, currentColor);
    --button-icon-size: var(--icon-size);
  }
</style>

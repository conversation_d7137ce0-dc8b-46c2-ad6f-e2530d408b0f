<script lang="ts">
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Trash from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import TaskTreeItemActions from "./TaskTreeItemActions.svelte";

  // Props
  export let taskUuid: string;
  export let taskStore: ICurrentConversationTaskStore;
  export let editable: boolean = true;

  $: uuidToTask = taskStore.uuidToTask;
  $: task = $uuidToTask.get(taskUuid);

  async function handleDeleteTask() {
    if (!editable) return;
    await taskStore.deleteTask(taskUuid);
  }
</script>

<div class="c-task-action-buttons">
  <!-- Delete Task Button, always shown -->
  <TextTooltipAugment content="Delete task" triggerOn={[TooltipTriggerOn.Hover]}>
    <IconButtonAugment
      size={1}
      variant="ghost"
      color="error"
      disabled={!editable}
      on:click={handleDeleteTask}
      class="c-task-action-button c-task-action-button--delete"
    >
      <Trash />
    </IconButtonAugment>
  </TextTooltipAugment>

  <!-- Task Tree Item Actions Dropdown -->
  {#if task}
    <TaskTreeItemActions {task} {taskStore} {editable} />
  {/if}
</div>

<style>
  .c-task-action-buttons {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-right: var(--ds-spacing-1);
  }
</style>

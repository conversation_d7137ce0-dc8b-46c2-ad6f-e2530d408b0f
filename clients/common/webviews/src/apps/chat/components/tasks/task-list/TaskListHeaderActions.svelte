<script lang="ts">
  import { getContext } from "svelte";
  import DotsVertical from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis-vertical.svg?component";
  import FileImport from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-import.svg?component";
  import FileExport from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-export.svg?component";
  import MessagePlus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-plus.svg?component";
  import BroomWide from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/broom-wide.svg?component";
  import Sparkles from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/sparkles.svg?component";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../../models/task-store";
  import type { ChatModel } from "../../../models/chat-model";
  import type { AgentConversationModel } from "../../../models/agent-conversation-model";
  import { getMarkdownRepresentation } from "@augment-internal/sidecar-libs/src/agent/task/task-utils";
  import { ExchangeStatus } from "../../../types/chat-message";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  // Props
  export let isEnhancing: boolean = false;

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { isImportingExporting, rootTask } = taskStore;
  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");

  let requestCloseDropdown: (() => void) | undefined = undefined;

  async function exportTasksToMarkdown() {
    if ($isImportingExporting) return;
    await taskStore.exportTasksToMarkdown();
    requestCloseDropdown?.();
  }

  async function importTasksFromMarkdown() {
    if ($isImportingExporting) return;
    await taskStore.importTasksFromMarkdown();
    requestCloseDropdown?.();
  }

  async function exportToNewChat() {
    if (!$rootTask) {
      return;
    }

    const currConversationId = chatModel.currentConversationId;
    const newTaskTree = await taskStore.cloneHydratedTask($rootTask);

    // If the conversation has not changed, let's create a new conversation
    if (currConversationId === chatModel.currentConversationId && newTaskTree) {
      await chatModel.setCurrentConversation(undefined, true, { newTaskUuid: newTaskTree.uuid });
    }
    requestCloseDropdown?.();
  }

  async function cleanupEmptyAndCancelledTasks() {
    await taskStore.cleanupEmptyAndCancelledTasks();
    requestCloseDropdown?.();
  }

  async function enhanceTaskList() {
    if (isEnhancing) return;

    try {
      // Set isEnhancing to true to disable UI elements
      taskStore.setEnhancing(true);

      if (!$rootTask) return;

      // Get the markdown representation of the task list
      const taskListMarkdown = getMarkdownRepresentation($rootTask);

      // Create a message asking to enhance the task list
      const message =
        "Please enhance this task list by adding more details, subtasks, or improving the organization:\n\n" +
        taskListMarkdown;

      // Send the message as a user message
      await agentConversationModel.interruptAgent();
      await chatModel.currentConversationModel.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: message,
        status: ExchangeStatus.draft,
        model_id: chatModel.currentConversationModel.selectedModelId ?? undefined,
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    } finally {
      // Set isEnhancing back to false
      taskStore.setEnhancing(false);
    }
    requestCloseDropdown?.();
  }
</script>

<!-- Task list menu dropdown -->
<DropdownMenuAugment.Root bind:requestClose={requestCloseDropdown}>
  <DropdownMenuAugment.Trigger>
    <TextTooltipAugment content="More actions" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        disabled={$isImportingExporting}
      >
        {#if $isImportingExporting}
          <SpinnerAugment size={1} />
        {:else}
          <DotsVertical />
        {/if}
      </IconButtonAugment>
    </TextTooltipAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content size={1} side="bottom" align="end">
    <!-- Import/Export Section -->
    <DropdownMenuAugment.Label>Import/Export</DropdownMenuAugment.Label>
    <DropdownMenuAugment.Item onSelect={exportTasksToMarkdown} disabled={$isImportingExporting}>
      <FileExport slot="iconLeft" />
      Export to Markdown
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={importTasksFromMarkdown} disabled={$isImportingExporting}>
      <FileImport slot="iconLeft" />
      Import from Markdown
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={exportToNewChat} disabled={isEnhancing}>
      <MessagePlus slot="iconLeft" />
      Export to New Chat
    </DropdownMenuAugment.Item>

    <!-- Organize Section -->
    <DropdownMenuAugment.Separator />
    <DropdownMenuAugment.Label>Organize</DropdownMenuAugment.Label>
    <DropdownMenuAugment.Item onSelect={cleanupEmptyAndCancelledTasks} disabled={isEnhancing}>
      <BroomWide slot="iconLeft" />
      Clean Up Tasks
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={enhanceTaskList} disabled={isEnhancing}>
      {#if isEnhancing}
        <SpinnerAugment slot="iconLeft" size={1} />
        Enhancing Task List...
      {:else}
        <Sparkles slot="iconLeft" />
        Enhance Task List
      {/if}
    </DropdownMenuAugment.Item>
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>

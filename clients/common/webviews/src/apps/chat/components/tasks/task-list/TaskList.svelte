<script lang="ts">
  import { getContext } from "svelte";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../../models/task-store";
  import type { ChatModel } from "../../../models/chat-model";
  import { type AgentConversationModel } from "../../../models/agent-conversation-model";
  import CollapsibleAugment from "$common-webviews/src/design-system/components/CollapsibleAugment/CollapsibleAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    type HydratedTask,
    TaskState,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import TaskListHeader from "./TaskListHeader.svelte";
  import TaskTree from "../task-tree/TaskTree.svelte";
  import { preprocessTaskTreeVisibility } from "../utils/task-visibility-utils";

  // Get required context models
  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);

  // Extract required reactive properties
  const { isCurrConversationAgentic } = agentConversationModel;
  const { rootTask, isEnhancing } = taskStore;

  // State for collapsible UI. Keep chat model notified of changes
  $: collapsed = $chatModel.isPanelCollapsed;

  // Filter state - will be bound to TaskListHeader
  let activeTaskFilter: Set<TaskState> = new Set();

  // Preprocess the task tree with visibility flags
  $: preprocessedRootTask = $rootTask
    ? preprocessTaskTreeVisibility($rootTask, activeTaskFilter)
    : undefined;

  $: hasNoTasks = !$rootTask?.subTasksData?.length;

  // Count tasks by state
  $: allTasks = countAllTasks($rootTask);
  $: notStartedTasksCount = allTasks.notStarted;
  $: inProgressTasksCount = allTasks.inProgress;
  $: completedTasksCount = allTasks.completed;
  $: cancelledTasksCount = allTasks.cancelled;

  // Function to recursively count all tasks by state
  function countAllTasks(
    task: HydratedTask | undefined,
    level = 0,
  ): {
    notStarted: number;
    inProgress: number;
    completed: number;
    cancelled: number;
  } {
    let counts = { notStarted: 0, inProgress: 0, completed: 0, cancelled: 0 };
    if (!task) {
      return counts;
    }

    // Initialize counts with the current task. If level is 0, don't include root task counts;
    if (level !== 0) {
      if (task.state === TaskState.NOT_STARTED) {
        counts.notStarted++;
      } else if (task.state === TaskState.IN_PROGRESS) {
        counts.inProgress++;
      } else if (task.state === TaskState.COMPLETE) {
        counts.completed++;
      } else if (task.state === TaskState.CANCELLED) {
        counts.cancelled++;
      }
    }

    // Recursively count subtasks
    if (task.subTasksData && task.subTasksData.length > 0) {
      for (const subtask of task.subTasksData) {
        const subtaskCounts = countAllTasks(subtask, level + 1);
        counts.notStarted += subtaskCounts.notStarted;
        counts.inProgress += subtaskCounts.inProgress;
        counts.completed += subtaskCounts.completed;
        counts.cancelled += subtaskCounts.cancelled;
      }
    }

    return counts;
  }
</script>

{#if $isCurrConversationAgentic && $rootTask}
  <div class="c-task-list-container">
    <CollapsibleAugment class="c-task-list-container__block-container" bind:collapsed={$collapsed}>
      <TaskListHeader
        {notStartedTasksCount}
        {inProgressTasksCount}
        {completedTasksCount}
        {cancelledTasksCount}
        isEnhancing={$isEnhancing}
        {hasNoTasks}
        slot="header"
        bind:activeTaskFilter
      />

      {#if preprocessedRootTask?.isVisible && preprocessedRootTask.subTasksData?.length}
        <div class="c-tasks-list">
          <TaskTree task={preprocessedRootTask} {taskStore} editable={!$isEnhancing} />
        </div>
      {:else}
        <div class="c-tasks-list c-tasks-list--empty">
          <TextAugment size={1} color="neutral">No tasks to show</TextAugment>
        </div>
      {/if}
    </CollapsibleAugment>
  </div>
{/if}

<style>
  .c-task-list-container {
    max-width: 100%;
    --task-list-max-height: min(400px, 30vh);
  }

  .c-tasks-list {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-1);
    max-height: var(--task-list-max-height);
    overflow-y: auto;
    border-radius: var(--ds-radius-2);

    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: var(--ds-scrollbar-thumb) var(--ds-scrollbar-track);
  }

  /* Webkit scrollbar styling */
  .c-tasks-list::-webkit-scrollbar {
    width: 6px;
  }

  .c-tasks-list::-webkit-scrollbar-track {
    background: var(--ds-scrollbar-track, transparent);
    border-radius: 3px;
  }

  .c-tasks-list::-webkit-scrollbar-thumb {
    background-color: var(--ds-scrollbar-thumb, rgba(255, 255, 255, 0.2));
    border-radius: 3px;
  }

  .c-tasks-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--ds-scrollbar-thumb-hover, rgba(255, 255, 255, 0.3));
  }

  .c-tasks-list--empty {
    opacity: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-2);
    max-height: var(--task-list-max-height);
    /* Disable selection */
    user-select: none;
  }
</style>

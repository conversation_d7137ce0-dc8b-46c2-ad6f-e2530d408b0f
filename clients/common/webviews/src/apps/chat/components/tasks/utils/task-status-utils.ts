/**
 * @file task-status-utils.ts
 * Utility functions for task status handling
 */

import { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import CircleBlank from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle.svg?component";
import CircleCheck from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";
import TimesCircle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-xmark.svg?component";
import CircleHalfStroke from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-half-stroke.svg?component";

import type { ComponentType, SvelteComponent } from "svelte";

/**
 * Maps a task state to a color for UI components
 * @param taskState The current state of the task
 * @returns A color string that can be used with design system components
 */
export function getTaskStatusColor(
  taskState: TaskState,
): "neutral" | "info" | "success" | "error" | "warning" {
  switch (taskState) {
    case TaskState.IN_PROGRESS:
      return "info";
    case TaskState.COMPLETE:
      return "success";
    case TaskState.CANCELLED:
      return "error";
    case TaskState.NOT_STARTED:
    default:
      return "neutral";
  }
}

/**
 * Maps a task state to a human-readable label
 * @param taskState The current state of the task
 * @returns A string representing the task state
 */
export function getTaskStatusLabel(taskState: TaskState): string {
  switch (taskState) {
    case TaskState.IN_PROGRESS:
      return "In Progress";
    case TaskState.COMPLETE:
      return "Completed";
    case TaskState.CANCELLED:
      return "Cancelled";
    case TaskState.NOT_STARTED:
    default:
      return "Not Started";
  }
}

/**
 * Returns the appropriate SVG component for a given task state
 * @param taskState The current state of the task
 * @returns A Svelte component representing the task state icon
 */
export function getTaskStatusIcon(taskState: TaskState): ComponentType<SvelteComponent> {
  switch (taskState) {
    case TaskState.IN_PROGRESS:
      return CircleHalfStroke;
    case TaskState.COMPLETE:
      return CircleCheck;
    case TaskState.CANCELLED:
      return TimesCircle;
    case TaskState.NOT_STARTED:
    default:
      return CircleBlank;
  }
}

<script lang="ts">
  import type { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import type { ButtonSize } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import type { ICurrentConversationTaskStore } from "../../../models/task-store";
  import { getTaskStatusColor, getTaskStatusLabel } from "../utils/task-status-utils";
  import TaskStatusDropdown from "./TaskStatusDropdown.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  // Props
  export let taskUuid: string;
  export let taskState: TaskState;
  export let taskStore: ICurrentConversationTaskStore;
  export let disabled: boolean = false;
  export let hideDropdown: boolean = false;
  export let size: ButtonSize = 1;

  // Calculate status badge color and label based on task state
  $: statusBadgeColor = getTaskStatusColor(taskState);
  $: statusLabel = getTaskStatusLabel(taskState);
</script>

{#if !hideDropdown}
  <div class="c-task-status-badge">
    <TaskStatusDropdown {taskUuid} {taskState} {taskStore} {disabled}>
      <div class="c-task-status-badge__badge">
        <StatusBadgeAugment color={statusBadgeColor} {size}>
          <TextAugment class="c-task-status-badge__label" {size}>{statusLabel}</TextAugment>
        </StatusBadgeAugment>
      </div>
    </TaskStatusDropdown>
  </div>
{:else}
  <div class="c-task-status-badge">
    <StatusBadgeAugment color={statusBadgeColor} {size}>
      <TextAugment class="c-task-status-badge__label" {size}>{statusLabel}</TextAugment>
    </StatusBadgeAugment>
  </div>
{/if}

<style>
  .c-task-status-badge {
    display: flex;
    width: 100%;

    & .c-task-status-badge__label {
      margin-left: var(--ds-spacing-1);
    }
  }

  .c-task-status-badge__badge {
    width: 100%;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &--no-dropdown {
      cursor: default;
    }

    &:hover {
      opacity: 0.9;
    }
  }
</style>

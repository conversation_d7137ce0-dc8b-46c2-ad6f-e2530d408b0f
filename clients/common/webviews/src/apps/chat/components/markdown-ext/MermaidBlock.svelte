<script lang="ts">
  import { themeStore } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ChevronDownIcon from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import ChevronLeftIcon from "$common-webviews/src/design-system/icons/chevron-left.svelte";
  import ChevronRightIcon from "$common-webviews/src/design-system/icons/chevron-right.svelte";
  import ChevronUpIcon from "$common-webviews/src/design-system/icons/chevron-up.svelte";
  import ReloadIcon from "$common-webviews/src/design-system/icons/reload.svelte";
  import ZoomInIcon from "$common-webviews/src/design-system/icons/zoom-in.svelte";
  import ZoomOutIcon from "$common-webviews/src/design-system/icons/zoom-out.svelte";
  import { ChatMetricName } from "$vscode/src/metrics/types";
  import type { Tokens } from "marked";
  import { getContext, onMount } from "svelte";
  import type panzoom from "svg-pan-zoom";
  import { type ChatModel } from "../../models/chat-model";
  import Codeblock from "./Codeblock.svelte";
  import { MERMAID_CONTAINER_HEIGHT, mermaidDiagram } from "./mermaid";
  import {
    createNewFile,
    type ICodeblockActionButton,
    writeToClipboard,
    type ICodeblockMetadata,
  } from "./utils";
  import { CodeblockActionType } from "./codeblock/types";

  export let token: Tokens.Code;
  export let codeblockMetadata: ICodeblockMetadata | undefined;

  const PAN_AMOUNT = 32;

  const chatModel: ChatModel | undefined = getContext("chatModel");
  const client = chatModel?.extensionClient;

  onMount(() => {
    client?.reportWebviewClientEvent(ChatMetricName.chatMermaidblockInitialize);
  });

  let showDiagram = false;
  let isLoading = true;
  let error = false;
  let panZoomController: typeof panzoom | undefined;
  let containerElement: HTMLDivElement;
  let svg: string | undefined;

  function canRender() {
    return showDiagram && !isLoading && !error;
  }

  let postedToggleMetric = false;
  function toggleDiagram() {
    showDiagram = !showDiagram;
    if (!postedToggleMetric) {
      postedToggleMetric = true;
      client?.reportWebviewClientEvent(ChatMetricName.chatMermaidblockToggle);
    }
  }

  function setError(hasError: boolean) {
    if (hasError) {
      showDiagram = false;
      isLoading = false;
      if (!error) {
        client?.reportWebviewClientEvent(ChatMetricName.chatMermaidBlockError);
      }
    }
    error = hasError;
  }

  function setContainerPosition(distance: number) {
    containerElement.style.top = `${distance}px`;
  }

  function setPanZoomController(controller: typeof panzoom) {
    panZoomController = controller;
    panZoomController.setOnPan(reportInteraction);
    panZoomController.setOnZoom(reportInteraction);
  }

  let hasInteracted = false;
  function reportInteraction() {
    if (!hasInteracted) {
      client?.reportWebviewClientEvent(ChatMetricName.chatMermaidblockInteract);
      hasInteracted = true;
    }
  }

  function setSvg(value: string) {
    svg = value;
  }

  $: themeCategory = $themeStore?.category;

  $: {
    if (token.raw.trim().endsWith("```") && !error) {
      // Only start rendering the diagram when the stream finishes the codeblock
      isLoading = false;
      showDiagram = true;
    }
  }

  let actionButtons: ICodeblockActionButton[] | undefined;
  $: {
    actionButtons = undefined;
    if (!error && !isLoading) {
      actionButtons = [
        {
          codeblockActionType: CodeblockActionType.goTo,
          title: showDiagram ? "Show Code" : "Show Diagram",
          onClick: toggleDiagram,
          iconName: showDiagram ? "data_object" : "account_tree",
          buttonText: showDiagram ? "Code" : "Diagram",
          primary: true,
        },
      ];
      if (showDiagram) {
        // when the diagram is shown, copy and create the SVG instead of the
        // mermaid code
        actionButtons?.push(
          {
            codeblockActionType: CodeblockActionType.copy,
            title: "Copy SVG",
            onClick: () => {
              if (svg) {
                writeToClipboard(svg);
              }
            },
            iconName: "file_copy",
            buttonText: "Copy SVG",
            onSuccessMessage: "Copied SVG",
          },
          {
            codeblockActionType: CodeblockActionType.insert,
            title: "Create SVG",
            onClick: () => {
              if (svg && chatModel) {
                // replace the extension of the file with .svg
                const relPath = codeblockMetadata?.relPath?.replace(/\.[^/.]+$/, ".svg");
                createNewFile(svg, chatModel.extensionClient, relPath);
              }
            },
            iconName: "new_window",
            buttonText: "Create SVG",
            onSuccessMessage: "Created SVG",
          },
        );
      }
    }
  }
</script>

<div class="c-mermaid-block">
  <div style="position: relative">
    <Codeblock
      token={showDiagram ? { ...token, text: "" } : token}
      {codeblockMetadata}
      height={MERMAID_CONTAINER_HEIGHT}
      {actionButtons}
      removeDefaultButtons={showDiagram}
    />
    <div
      class="c-mermaid-diagram-container"
      class:c-mermaid-diagram-hidden={!showDiagram}
      bind:this={containerElement}
    >
      <div
        class="c-mermaid-diagram"
        use:mermaidDiagram={{
          text: isLoading || error ? "" : token.text,
          canRender,
          setError,
          setContainerPosition,
          setPanZoomController,
          themeCategory,
          setSvg,
        }}
      />
    </div>
    <div class="c-mermaid-controls-container" class:c-mermaid-diagram-hidden={!showDiagram}>
      <IconButtonAugment
        on:click={() => panZoomController?.zoomIn()}
        color="neutral"
        variant="ghost"
      >
        <ZoomInIcon />
      </IconButtonAugment>
      <IconButtonAugment
        on:click={() => panZoomController?.panBy({ x: 0, y: PAN_AMOUNT })}
        color="neutral"
        variant="ghost"
      >
        <ChevronUpIcon />
      </IconButtonAugment>
      <IconButtonAugment
        on:click={() => panZoomController?.zoomOut()}
        color="neutral"
        variant="ghost"
      >
        <ZoomOutIcon />
      </IconButtonAugment>
      <IconButtonAugment
        on:click={() => panZoomController?.panBy({ x: PAN_AMOUNT, y: 0 })}
        color="neutral"
        variant="ghost"
      >
        <ChevronLeftIcon />
      </IconButtonAugment>
      <IconButtonAugment on:click={panZoomController?.reset} color="neutral" variant="ghost">
        <ReloadIcon />
      </IconButtonAugment>
      <IconButtonAugment
        on:click={() => panZoomController?.panBy({ x: -PAN_AMOUNT, y: 0 })}
        color="neutral"
        variant="ghost"
      >
        <ChevronRightIcon />
      </IconButtonAugment>
      <div class="c-mermaid-controls-container__down">
        <IconButtonAugment
          on:click={() => panZoomController?.panBy({ x: 0, y: -PAN_AMOUNT })}
          color="neutral"
          variant="ghost"
        >
          <ChevronDownIcon />
        </IconButtonAugment>
      </div>
    </div>
  </div>
</div>

<style>
  .c-mermaid-block {
    padding: 4px;
    width: 100%;
  }

  .c-mermaid-diagram {
    display: flex;
    justify-content: center;
  }

  .c-mermaid-controls-container {
    position: absolute;
    right: 18px;
    bottom: 18px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
  }

  .c-mermaid-controls-container__down {
    grid-column: 2;
    grid-row: 3;
  }

  .c-mermaid-diagram-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }

  .c-mermaid-diagram-hidden {
    display: none;
    visibility: hidden;
    opacity: 0;
  }
</style>

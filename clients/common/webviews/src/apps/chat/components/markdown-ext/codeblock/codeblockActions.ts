import { type ChatModel } from "../../../models/chat-model";
import {
  createNewFile,
  type ICodeblockMetadata,
  isNotebook,
  writeToClipboard,
  type ICodeblockActionButton,
} from "../utils";
import { ChatMetricName } from "$vscode/src/metrics/types";
import { CodeblockActionType } from "./types";

// Codeblock action buttons

/**
 * Creates a button to copy code to clipboard
 *
 * @param getCode A function to get the current code from the codeblock
 * @param primary Whether this is a primary button
 */
export function createCopyToClipboardAction(
  getCode: () => string | undefined,
  primary?: boolean,
): ICodeblockActionButton {
  function copyToClipboard() {
    const code = getCode();
    if (code) {
      writeToClipboard(code);
    }
  }
  return {
    codeblockActionType: CodeblockActionType.copy,
    title: "Copy to clipboard",
    onClick: copyToClipboard,
    onSuccessMessage: "Copied",
    iconName: "file_copy",
    buttonText: "Copy",
    primary: !!primary,
  };
}

/**
 * Creates a button to insert code into a new file with the given path or with a generic name
 *
 * @param code The code to insert into a new file
 * @param codeblockMetadata The metadata of the codeblock
 * @param chatModel The chat model
 * @param primary Whether this is a primary button
 */
export function createInsertIntoNewFileAction(
  getCode: () => string | undefined,
  codeblockMetadata: ICodeblockMetadata | undefined,
  chatModel: ChatModel | undefined,
  primary?: boolean,
): ICodeblockActionButton {
  function insertIntoNewFile() {
    const code = getCode();
    if (!code || !chatModel?.extensionClient) {
      return;
    }
    createNewFile(code, chatModel.extensionClient, codeblockMetadata?.relPath);
  }

  return {
    codeblockActionType: CodeblockActionType.insert,
    title: codeblockMetadata?.relPath
      ? `Create new file at ${codeblockMetadata.relPath}`
      : "Create new file",
    onClick: insertIntoNewFile,
    onSuccessMessage: "Creating new file...",
    iconName: "new_window",
    buttonText: "Create",
    primary: !!primary,
  };
}

/**
 * Creates a button to go to the file based on the state of the codeblock
 *
 * When clicked, this button will report a webview client event.
 *
 * @param code The code to go to the file for
 * @param isExcerpt Whether the code is an excerpt
 * @param codeblockMetadata The metadata of the codeblock
 * @param chatModel The chat model
 */
export function createGoToFileAction(
  code: string,
  isExcerpt: boolean,
  codeblockMetadata: ICodeblockMetadata | undefined,
  chatModel: ChatModel | undefined,
): ICodeblockActionButton {
  function goToFile() {
    if (codeblockMetadata?.relPath) {
      chatModel?.extensionClient.reportWebviewClientEvent(ChatMetricName.chatCodeblockGoToFile);
      chatModel?.extensionClient.openFile({
        repoRoot: "",
        pathName: codeblockMetadata?.relPath ?? "",
        // If this is an excerpt, we want to open to the snippet
        snippet: isExcerpt ? code : undefined,
      });
    }
  }
  return {
    codeblockActionType: CodeblockActionType.goTo,
    title: `Go to ${codeblockMetadata?.relPath}`,
    onClick: goToFile,
    onSuccessMessage: "Opening file...",
    iconName: "open_in_new",
    buttonText: "Go to",
    primary: false,
  };
}

/**
 * Get the button type that is shown as the primary button on the codeblock
 *
 * @param codeblockMetadata The metadata of the codeblock
 * @param canTriggerSmartPaste Whether smart paste can be triggered
 * @param targetPathAttemptedAndFailed Whether the target path resolution was attempted and failed
 * @param enableDirectApply Whether direct apply is enabled
 * @returns the button type that is shown as the primary button on the codeblock
 */
export function getPrimaryButtonType(
  codeblockMetadata: ICodeblockMetadata | undefined,
  canTriggerSmartPaste: boolean,
  targetPathAttemptedAndFailed: boolean,
  enableDirectApply: boolean = false,
): CodeblockActionType {
  const hasCodepath = !!codeblockMetadata?.relPath;
  const isFromNotebook = hasCodepath && isNotebook(codeblockMetadata?.relPath ?? "");

  // Prioritize direct apply when it's enabled
  if (enableDirectApply && canTriggerSmartPaste && !isFromNotebook) {
    return CodeblockActionType.directApply;
  } else if (canTriggerSmartPaste && !isFromNotebook) {
    return CodeblockActionType.smartPaste;
  } else if (hasCodepath && targetPathAttemptedAndFailed) {
    return CodeblockActionType.insert;
  }
  return CodeblockActionType.copy;
}

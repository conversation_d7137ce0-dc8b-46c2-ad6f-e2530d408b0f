<script lang="ts">
  import { DEFAULT_MONACO_OPTIONS } from "$common-webviews/src/common/components/markdown/codeblock/monaco";
  import type { Tokens } from "marked";
  import { getContext, onDestroy } from "svelte";
  import { writable, type Writable } from "svelte/store";
  import { type ChatModel } from "../../../models/chat-model";
  import { ExchangeStatus } from "../../../types/chat-message";
  import { type ICodeblockActionButton, type ICodeblockMetadata } from "../utils";
  import {
    createCopyToClipboardAction,
    createGoToFileAction,
    createInsertIntoNewFileAction,
    getPrimaryButtonType,
  } from "./codeblockActions";
  import { createSmartPasteAction, smartPasteObserver } from "./smartPaste";
  import { CodeblockActionType } from "./types";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import CodeblockContainer from "./CodeblockContainer.svelte";

  /** Top-level state used as entry points/data across the rest of codeblock logic */
  export let token: Tokens.Code;
  export let codeblockMetadata: ICodeblockMetadata | undefined;
  export let collapsed: boolean = false;
  /** The line height of the Monaco codeblock */
  export let lineHeight = DEFAULT_MONACO_OPTIONS.lineHeight ?? 18;
  /** Number of codeblock lines to show when truncated */
  export let truncateLines = 10;
  /**
   * The action buttons displayed in the code block. The default value includes
   * all buttons
   */
  export let defaultActionButtons: CodeblockActionType[] = [
    CodeblockActionType.smartPaste,
    CodeblockActionType.copy,
    CodeblockActionType.insert,
    CodeblockActionType.goTo,
  ];

  const chatModel: ChatModel | undefined = getContext("chatModel");
  const requestIdModel: Writable<string> = getContext("requestId");
  $: requestId = $requestIdModel;
  const remoteAgentsModel = getContext<RemoteAgentsModel | undefined>(RemoteAgentsModel.key);
  $: isRemoteAgentMode = !!remoteAgentsModel?.isActive;

  $: {
    if (isRemoteAgentMode) {
      defaultActionButtons = [CodeblockActionType.copy];
    }
  }

  $: isExcerpt = codeblockMetadata?.mode === "EXCERPT";

  async function canResolveTargetPath() {
    targetPathResolutionAttempted = true;
    if (codeblockMetadata?.relPath) {
      targetPathResolved = !!(await chatModel?.extensionClient.resolvePath({
        rootPath: "",
        relPath: codeblockMetadata?.relPath,
      }));
    }
  }
  let targetPathResolved = false;
  let targetPathResolutionAttempted = false;
  $: codeblockMetadata?.relPath && canResolveTargetPath();

  $: conversationModel = $chatModel?.currentConversationModel;
  $: requestStatus = conversationModel?.exchangeWithRequestId(requestId)?.status;
  $: isSuccessful = requestStatus === ExchangeStatus.success;
  // Whether this message request is still loading
  $: isMessageLoading = requestStatus === ExchangeStatus.sent;
  // Whether this instance of the codeblock is streaming
  $: isStreaming = !token.raw.trimEnd().endsWith("```");

  // Actions
  $: primaryButtonType = getPrimaryButtonType(
    codeblockMetadata,
    canTriggerSmartPaste,
    targetPathResolutionAttempted && !targetPathResolved,
    !!chatModel?.flags.enableDirectApply && !!directApplyButton,
  );

  $: goToFileButton = createGoToFileAction(token.text, isExcerpt, codeblockMetadata, chatModel);

  $: copyButton = createCopyToClipboardAction(() => token.text);

  $: createButton = createInsertIntoNewFileAction(() => token.text, codeblockMetadata, chatModel);

  let element: HTMLDivElement | undefined;
  let isVisibleAndStable = false;
  let isHoveredStable = false;

  let destroySmartPasteObservers: () => void | undefined;
  // Will only attach an observer if the element is already mounted, and the exchange is successful
  $: if (element && isSuccessful && chatModel?.flags.enableSmartPaste) {
    destroySmartPasteObservers = smartPasteObserver(
      element,
      (val: boolean) => (isVisibleAndStable = val),
      (val: boolean) => (isHoveredStable = val),
    );
  }

  // create immutable functons around reactive variables to stop the smart paste
  // button from re-rending which breaks the successful button
  const getHasClickedSmartPaste = () => $hasClickedSmartPaste;
  const setHasClickedSmartPaste = (val: boolean) => ($hasClickedSmartPaste = val);

  $: ({
    smartPaste,
    canTriggerSmartPaste,
    canPrecomputeSmartPaste,
    smartPasteButton,
    directApplyButton,
  } = createSmartPasteAction(
    chatModel,
    codeblockMetadata,
    token,
    requestId,
    isSuccessful,
    targetPathResolved,
    isVisibleAndStable,
    isHoveredStable,
    getHasClickedSmartPaste,
    setHasClickedSmartPaste,
  ));

  const hasClickedSmartPaste = writable(false);
  $: if (canPrecomputeSmartPaste) {
    smartPaste({ dryRun: true });
  }

  $: defaultButtons = {
    [CodeblockActionType.smartPaste]: { ...smartPasteButton, disabled: isMessageLoading },
    ...(chatModel?.flags.enableDirectApply && directApplyButton && !isRemoteAgentMode
      ? {
          [CodeblockActionType.directApply]: { ...directApplyButton, disabled: isMessageLoading },
        }
      : {}),
    [CodeblockActionType.copy]: { ...copyButton, disabled: isStreaming && isMessageLoading },
    [CodeblockActionType.insert]: { ...createButton, disabled: isStreaming && isMessageLoading },
    [CodeblockActionType.goTo]: { ...goToFileButton },
  } as Record<CodeblockActionType, ICodeblockActionButton>;
  $: primaryButton = defaultButtons[primaryButtonType];

  // Include directApply in the list of buttons when it's enabled
  $: actionButtonTypes =
    chatModel?.flags.enableDirectApply && directApplyButton
      ? [CodeblockActionType.directApply, ...defaultActionButtons]
      : defaultActionButtons;

  $: allButtons = actionButtonTypes
    .map((buttonType) => defaultButtons[buttonType])
    .filter((item) => item !== undefined);
  $: otherButtons = allButtons.filter((button) => button.codeblockActionType !== primaryButtonType);

  let truncate = false;
  /** Whether the user has manually opened the codeblock */
  let opened = false;
  let codeContainerResizeObserver: ResizeObserver | undefined;
  let codeContainerElement: HTMLDivElement | undefined;

  $: if (
    codeContainerElement &&
    !codeContainerResizeObserver &&
    // only enable with debug flag enabled
    chatModel?.flags.enableDebugFeatures
  ) {
    codeContainerResizeObserver = new ResizeObserver((entries) => {
      if (!truncate && !opened && codeblockMetadata?.relPath) {
        // only truncate the codeblock if there's a filepath, there are more than
        // truncateLines lines, it's not already truncated, and the user hasn't
        // manually opened it
        const height = entries[0].contentRect.height;
        truncate = height > truncateLines * lineHeight;
      }
    });
    codeContainerResizeObserver.observe(codeContainerElement);
  }

  onDestroy(() => {
    destroySmartPasteObservers?.();
    codeContainerResizeObserver?.disconnect();
    codeContainerResizeObserver = undefined;
  });
</script>

<CodeblockContainer
  bind:element
  bind:codeContainerElement
  {truncate}
  {opened}
  {token}
  {codeblockMetadata}
  {collapsed}
  {lineHeight}
  {truncateLines}
  {requestStatus}
  {primaryButton}
  buttons={isMessageLoading ? allButtons : otherButtons}
  {goToFileButton}
/>

<style>
</style>

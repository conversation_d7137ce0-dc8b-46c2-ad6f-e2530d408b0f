<script lang="ts">
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment/BadgeRoot.svelte";
  import type { BadgeColor } from "$common-webviews/src/design-system/components/BadgeAugment/badge-types";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import AugmentLogo from "$common-webviews/src/design-system/icons/augment/augment-logo.svelte";
  import TimestampDisplay from "$common-webviews/src/design-system/components/TimestampDisplay.svelte";
  import { getContext } from "svelte";
  import { type ChatModel } from "../models/chat-model";
  import { ExchangeStatus, type ExchangeWithStatus } from "../types/chat-message";

  export let isAugment: boolean = false;
  export let showName: boolean = false;
  export let timestamp: string | undefined = undefined;
  export let turn: ExchangeWithStatus | undefined = undefined;
  const chatModel = getContext<ChatModel | undefined>("chatModel");
  const flags = chatModel?.flags;

  let badgeColor: BadgeColor;
  $: badgeColor = isAugment ? "accent" : "neutral";

  $: isTurnCompleted =
    turn?.status === ExchangeStatus.success ||
    turn?.status === ExchangeStatus.failed ||
    turn?.status === ExchangeStatus.cancelled;
  $: isCompletedMessage = !isAugment || isTurnCompleted;
  $: shouldShowTimestamp = timestamp && isCompletedMessage;
</script>

<div class="c-chat-message" class:c-chat-message--augment={isAugment}>
  {#if showName}
    <div class="c-chat-message__badge" class:is-agument={isAugment}>
      {#if isAugment}
        <BadgeAugment color={badgeColor} size={1} variant="soft">
          <div class="c-chat-message__badge-content">
            <AugmentLogo />
            Augment
          </div>
        </BadgeAugment>
        {#if shouldShowTimestamp}
          <TimestampDisplay {timestamp} />
        {/if}
      {/if}
    </div>
  {/if}

  {#if !isAugment}
    {#if shouldShowTimestamp && !showName}
      <TimestampDisplay {timestamp} />
    {/if}
    <div class="c-chat-message--user">
      <!-- Ensure that we don't show 2 timestamps -->
      <CalloutAugment
        color={badgeColor}
        size={1}
        variant="soft"
        class={`c-chat-message__content ${flags?.enableDesignSystemRichTextEditor ? "c-chat-message__content--rich-text" : ""}`}
      >
        <slot name="content" />
      </CalloutAugment>
    </div>
  {:else}
    <div class="c-chat-message__content">
      <slot name="content" />
    </div>
  {/if}
</div>

<slot name="footer" />
<slot name="edit" />

<style>
  .c-chat-message {
    display: flex;
    flex-direction: column;
    gap: 0;

    width: 100%;

    align-items: end;
    padding-top: var(--ds-spacing-4);
    padding-right: var(--ds-spacing-1);
  }

  .c-chat-message--user {
    margin-left: 36px;
    display: flex;
    flex-direction: row;
    gap: 0;
    align-items: flex-start;
  }

  .c-chat-message--augment {
    align-items: start;
  }

  .c-chat-message__content {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--ds-spacing-4);
  }

  .c-chat-message :global(.c-chat-message__content .ProseMirror) {
    word-break: break-word;
    /* 12px */
    font-size: 0.75rem;
    /* 16px */
    line-height: 1rem;
    letter-spacing: 0.0025em;
  }

  /* Model response font size */
  :global(.c-chat-message__content .c-callout-body .c-text) {
    /* 12px */
    font-size: 0.75rem;
    /* 16px */
    line-height: 1rem;
    letter-spacing: 0.0025em;
  }

  :not(.c-chat-message--augment) :global(.c-chat-message__content) {
    padding: var(--ds-spacing-1);
    border-radius: var(--ds-radius-2);
  }

  :not(.c-chat-message--augment)
    :global(.c-chat-message__content.c-chat-message__content--rich-text) {
    padding: 0;
    border-radius: var(--ds-radius-4);
  }
  .c-chat-message__badge {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
  }

  .c-chat-message__badge-content :global(svg) {
    width: var(--ds-spacing-4);
    height: var(--ds-spacing-4);
  }

  .c-chat-message__badge-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1) var(--ds-spacing-2);
    padding: var(--ds-spacing-1);
  }
  .is-agument {
    padding-left: var(--ds-radius-4);
    padding-bottom: var(--ds-spacing-2);
  }
</style>

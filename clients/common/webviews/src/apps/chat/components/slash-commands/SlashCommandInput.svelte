<script lang="ts">
  import { onMount, getContext } from "svelte";
  import FileSelection from "$common-webviews/src/apps/chat/components/slash-commands/FileSelection.svelte";
  import type { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { getSlashCommand } from "$common-webviews/src/apps/chat/types/slash-command-list";
  import { type SlashCommandModel } from "../../models/slash-command-model";

  let chatModel = getContext("chatModel") as ChatModel;
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
  let slashCommandModel = getContext<SlashCommandModel>("slashCommandModel");

  const focusInput = () => {
    const input = document.querySelector("input");
    if (input) {
      input.focus();
      input.select();
    }
  };

  $: specialContextInputModel = chatModel.specialContextInputModel;
  $: selections = Object.values($specialContextInputModel.selections).sort((a, b) => {
    return a.selection.pathName.localeCompare(b.selection.pathName);
  });
  $: slashCommandModel.setSelections(selections);

  $: actionId = slashCommandModel.activeCommand;
  $: command = getSlashCommand($actionId);
  $: isActiveCommandRunnableReadable = slashCommandModel.isActiveCommandRunnable;

  let inputText = "";
  let enterKeyDownOccurred = false;
  $: slashCommandModel.setInputText(inputText);

  function cancelCommand() {
    slashCommandModel.cancelActiveCommand();
  }

  function handleInputKeyDown(e: KeyboardEvent): boolean {
    if (e.shiftKey || e.ctrlKey || e.metaKey) {
      return false; // Not handled
    }

    e.stopPropagation();

    if (e.key === "Enter") {
      enterKeyDownOccurred = true;
      return true;
    } else if (e.key === "Escape") {
      cancelCommand();
      return true;
    } else if (e.key === "Backspace") {
      if (inputText === "") {
        cancelCommand();
      }
      return true;
    }

    return false;
  }

  function handleFakeInputKeyDown(e: KeyboardEvent): boolean {
    if (e.ctrlKey || e.metaKey || e.key === "Tab") {
      return false; // Not handled
    }

    e.stopPropagation();

    if (!e.shiftKey) {
      if (e.key === "Escape" || e.key === "Backspace") {
        cancelCommand();
        return true;
      }

      if (e.key === "Enter" && $isActiveCommandRunnableReadable) {
        slashCommandModel.runActiveCommand();
        return true;
      }
    }

    // don't allow any keys to be typed, apart from Escape/Backspace to cancel
    // and Enter to run
    e.preventDefault();
    return true;
  }

  function handleInputKeyUp(e: KeyboardEvent): boolean {
    if (e.shiftKey) {
      return false; // Not handled
    }

    if (e.key === "Enter") {
      if (enterKeyDownOccurred && $isActiveCommandRunnableReadable) {
        enterKeyDownOccurred = false;
        slashCommandModel.runActiveCommand();
        return true;
      }
    }

    return false; // Not handled
  }

  // ==== Lifecycle ====
  // When the element is first rendered and goes from undefined => an HTMLElement,
  // we need to focus it so that the user can start typing
  onMount(focusInput);
</script>

<div class="c-action" on:click={focusInput} on:keydown={(_e) => {}} role="button" tabindex="-1">
  <div class="c-action-row">
    {#if $actionId === "find"}
      <p class="c-action-label">/{$actionId}</p>
      <input
        type="text"
        class="c-max-element-inline"
        bind:value={inputText}
        placeholder={command?.placeholderText}
        on:keydown={handleInputKeyDown}
        on:keyup={handleInputKeyUp}
      />
    {:else}
      <p class="c-action-label">/{$actionId}</p>
      <div class="c-max-element-inline">
        {#if selections !== undefined && selections?.length > 0}
          {#each selections as s}
            <FileSelection fileSelection={s.selection} />
          {/each}
        {:else}
          <span class="c-select-code-msg">{command?.selectionReminderMessage}</span>
        {/if}
        <input
          type="text"
          class="c-tiny-input-inline"
          on:keydown={handleFakeInputKeyDown}
          on:keyup={handleInputKeyUp}
        />
      </div>
    {/if}
  </div>
</div>

<style>
  .c-action {
    display: flex;
    flex-direction: column;
    align-items: baseline;
    width: 100%;
    outline-width: 0;
  }

  .c-action-label {
    margin-top: 0;
    margin-right: var(--ds-spacing-2);
    margin-bottom: 0;
  }

  .c-action-row {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    width: 100%;
  }

  .c-action input {
    color: var(--vscode-editor-foreground);
    background-color: transparent;
    min-width: 0;
    padding: 0 var(--ds-spacing-1);
    border-width: 0;
    outline: none;
  }

  .c-action .c-max-element-inline {
    flex-grow: 1;
    width: auto;
  }

  .c-action .c-tiny-input-inline {
    flex-grow: 0;
    width: var(--ds-spacing-4);
    max-width: var(--ds-spacing-4);
  }

  .c-max-element-inline :global(.c-select-code-msg) {
    background-color: transparent;
    border-radius: var(--ds-radius-1);
    padding-inline: var(--ds-spacing-1);
  }
</style>

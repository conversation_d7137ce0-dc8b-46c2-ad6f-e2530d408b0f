<script lang="ts">
  import { createEventDispatcher } from "svelte";

  // Track the variables associated with the draggable
  export let inputArea: HTMLElement;

  const dispatch = createEventDispatcher<{
    dragEnd: void;
  }>();

  // If `dragInfo` is defined, we are currently dragging the divider
  interface DragInfo {
    startY: number;
    initialHeight: number;
  }
  let dragInfo: DragInfo | undefined;

  const startDrag = (event: MouseEvent) => {
    dragInfo = { startY: event.clientY, initialHeight: inputArea.offsetHeight };
    inputArea?.classList.add("is-dragging");
  };

  const duringDrag = (event: MouseEvent) => {
    if (!dragInfo) {
      return;
    }
    const deltaY = event.clientY - dragInfo.startY;
    inputArea.style.height = `${Math.max(80, dragInfo.initialHeight - deltaY)}px`;
  };

  const endDrag = () => {
    if (!dragInfo) {
      return;
    }
    dispatch("dragEnd");
    dragInfo = undefined;
    inputArea?.classList.remove("is-dragging");
  };
</script>

<!-- During lifecycle of the component, we want to listen for mouse events on the document -->
<svelte:document on:mousemove={duringDrag} on:mouseup={endDrag} />

<!-- Ignore the below because the default behavior is to expand the input area.
This is purely for a bit of UX polish for the user-->
<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
<div class="c-chat-input-dialog-hr-handle" on:mousedown={startDrag} role="separator">
  <div class="c-chat-input-dialog-hr" />
</div>

<style>
  .c-chat-input-dialog-hr-handle {
    cursor: ns-resize;
    position: relative;
  }

  /* Add a pseudo-element to the handle to make it easier to grab */
  .c-chat-input-dialog-hr-handle::before {
    content: ""; /* Required to render the pseudo-element */

    position: absolute; /* Position the pseudo-element relative to the handle */

    /* Define a variable for the handle height */
    --handle-height: var(--ds-spacing-2);

    height: var(--handle-height);
    bottom: calc(
      -1 * (var(--handle-height) / 2)
    ); /* Position the pseudo-element right on top of the handle */
    width: 100%; /* Make the pseudo-element the same width as the handle */
    z-index: var(--z-bottom-input-drag-handle);

    margin: 0px;
    cursor: ns-resize;
  }

  .c-chat-input-dialog-hr {
    height: 1px;
    width: 100%;
    cursor: pointer;
    background-color: var(--vscode-editorWidget-border);
  }

  .c-chat-input-dialog-hr-handle:hover .c-chat-input-dialog-hr {
    background-color: var(--vscode-sash-hoverBorder);
  }
</style>

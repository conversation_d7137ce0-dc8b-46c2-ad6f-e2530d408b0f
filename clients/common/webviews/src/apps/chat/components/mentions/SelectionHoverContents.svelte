<script lang="ts">
  import type { editor } from "monaco-editor";
  export let originalCode: string | undefined;

  // We only want to import this in production/development, not in tests. This is because
  // monaco-based components are unhappy in jsdom environments, so we only import this when needed.
  // eslint-disable-next-line no-undef
  const isBuild = process.env.NODE_ENV === "production" || process.env.NODE_ENV === "development";
  const c = isBuild
    ? import("$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte")
    : undefined;

  let editorInstance: editor.IStandaloneCodeEditor | undefined;

  // let heightPx: number = 0;
  let style: string = "";
  let initializedMonacoSize = false;
  const updateSize = async (widthPx: number, heightPx: number): Promise<void> => {
    initializedMonacoSize = true;
    style = `
         width: ${widthPx}px;
         height: min(${heightPx}px, 40vh);
    `;
  };
  $: editorInstance?.onDidContentSizeChange((e: editor.IContentSizeChangedEvent) =>
    updateSize(e.contentWidth, e.contentHeight),
  );

  $: {
    if (editorInstance && !initializedMonacoSize) {
      updateSize(editorInstance?.getContentWidth(), editorInstance?.getContentHeight());
    }
  }
</script>

<div class="c-selection-hover-contents" {style}>
  <!-- We only want to import this in production/development, not in tests -->
  {#if c}
    {#await c}
      <pre class="c-selection-preview__code"><code>{originalCode ?? ""}</code></pre>
    {:then component}
      <svelte:component
        this={component.default}
        bind:editorInstance
        text={originalCode ?? ""}
        options={{
          automaticLayout: true,
          lineNumbers: "off",
          wrappingIndent: "same",
          padding: { top: 0, bottom: 0 },
          wordWrap: "off",
          contextmenu: false,
          wordBasedSuggestions: "off",
          renderLineHighlight: "none",
          occurrencesHighlight: "off",
          selectionHighlight: false,
          codeLens: false,
          links: false,
          hover: { enabled: false },
          hideCursorInOverviewRuler: true,
          renderWhitespace: "none",
          renderFinalNewline: "on",
        }}
      />
    {:catch}
      <pre class="c-selection-preview__code"><code>{originalCode ?? ""}</code></pre>
    {/await}
  {/if}
</div>

<style>
  .c-selection-hover-contents {
    max-width: 100%;
  }

  .c-selection-preview__code {
    overflow: scroll;
  }
</style>

<script lang="ts" generics="TOption extends IMentionable">
  import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_RULES_FOLDER,
  } from "@augment-internal/sidecar-libs/src/utils/rules-parser";

  import CursorText from "$common-webviews/src/design-system/icons/cursor-text.svelte";

  import Ruler from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ruler-vertical.svg?component";
  import GuidelinesFilespan from "$common-webviews/src/apps/chat/components/context/GuidelinesFilespan.svelte";
  import Filespan from "$common-webviews/src/common/components/Filespan.svelte";
  import type { IMentionable } from "$common-webviews/src/common/components/inputs/types";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/exclamation-triangle.svelte";
  import AugmentLogoAnimated from "$common-webviews/src/apps/main-panel/components/AugmentLogoAnimated.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Book from "$common-webviews/src/design-system/icons/vscode/book.svelte";
  import FileIcon from "$common-webviews/src/design-system/icons/vscode/file.svelte";
  import FolderOpened from "$common-webviews/src/design-system/icons/vscode/folder-opened.svelte";
  import RootFolder from "$common-webviews/src/design-system/icons/vscode/root-folder.svelte";
  import "@radix-ui/colors/yellow.css";
  import "@radix-ui/colors/yellow-dark.css";
  import {
    isItemExternalSource,
    isItemFile,
    isItemFolder,
    isItemPersonality,
    isItemRecentFile,
    isItemRule,
    isItemSelection,
    isItemSourceFolder,
    isItemUserGuidelines,
  } from "../../types/mention-option";
  import { getPersonalityIconComponent } from "../../utils/personality-icon-mapper";

  export let option: TOption;
</script>

<div class="c-mention-hover-contents">
  {#if option && isItemFile(option)}
    <FileIcon />
    <Filespan filepath={option.file.pathName} />
  {:else if option && isItemRecentFile(option)}
    <FileIcon />
    <Filespan filepath={option.recentFile.pathName} />
  {:else if option && isItemFolder(option)}
    <FolderOpened />
    <Filespan filepath={option.folder.pathName} />
  {:else if option && isItemExternalSource(option)}
    <Book />
    <Filespan filepath={option.externalSource.name} />
  {:else if option && isItemSourceFolder(option)}
    <div class="l-mention-hover-contents__source-folder">
      <div class="l-source-folder-name">
        <RootFolder />
        <Filespan class="c-source-folder-item" filepath={option.sourceFolder.folderRoot} />
      </div>
      <GuidelinesFilespan class="guidelines-filespan" sourceFolder={option.sourceFolder} />
    </div>
  {:else if option && isItemUserGuidelines(option) && option.userGuidelines.enabled}
    {#if option.userGuidelines.overLimit}
      <ExclamationTriangle class="c-mention-hover-contents__guidelines-warning-icon" />
      <Filespan
        filepath={`Guidelines exceeded length limit of ${option.userGuidelines.lengthLimit} characters`}
      />
    {/if}
  {:else if option && isItemSelection(option)}
    <CursorText />
    <Filespan
      filepath={`${option.selection.pathName}:L${option.selection.fullRange?.startLineNumber}-${option.selection.fullRange?.endLineNumber}`}
    />
  {:else if option && isItemPersonality(option)}
    <div class="c-mention-hover-contents__personality-icon">
      <AugmentLogoAnimated heightPx={32} floatHeight={4} animationDuration={2.25}>
        <svelte:component this={getPersonalityIconComponent(option.personality.type)} />
      </AugmentLogoAnimated>
    </div>
    <div class="c-mention-hover-contents__personality">
      <TextAugment size={2} weight="medium">{option.label}</TextAugment>
      <TextAugment size={1}>
        {option.personality.description}
      </TextAugment>
    </div>
  {:else if option && isItemRule(option)}
    <Ruler />
    <Filespan filepath={`${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/${option.rule.path}`} />
  {:else}
    {option.label}
  {/if}
</div>

<style>
  :global(.guidelines-filespan) {
    margin-top: var(--ds-spacing-1);
  }

  :global(.c-mention-hover-contents__guidelines-warning-icon) {
    color: var(--yellow-7);
  }

  .c-mention-hover-contents,
  .l-source-folder-name {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);

    & .l-mention-hover-contents__source-folder {
      flex-direction: column;
    }
  }

  .c-mention-hover-contents__personality {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .c-mention-hover-contents__personality-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--ds-spacing-1);
  }

  .c-mention-hover-contents__personality-icon :global(.c-augment-logo-animated) {
    height: 32px;
  }

  .c-mention-hover-contents__personality-icon :global(.c-augment-logo-animated__icon) :global(svg) {
    --augment-logo-icon-size: 32px;
    width: var(--augment-logo-icon-size);
    height: var(--augment-logo-icon-size);
    color: var(--ds-text-primary);
  }

  .c-mention-hover-contents__personality-icon :global(.c-augment-logo-animated__shadow) {
    display: none;
  }
</style>

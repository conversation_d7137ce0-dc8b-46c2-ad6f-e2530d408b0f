import { type IChatFlags } from "./models/types";
import { type ChatUserMessageData } from "$vscode/src/webview-providers/webview-messages";
import { ExchangeStatus, type ExchangeWithStatus, SeenState } from "./types/chat-message";

/**
 * Yields the time remaining, on the interval.
 * @param timeoutMs - how long to keep this going
 * @param intervalMs - the interval to emit at.
 */
async function* countdown(timeoutMs: number, intervalMs = 1000) {
  while (timeoutMs > 0) {
    yield timeoutMs;
    await new Promise((res) => setTimeout(res, Math.min(intervalMs, timeoutMs)));
    timeoutMs -= intervalMs;
  }
}
/**
 * Represents a chat stream with retry capability.
 */
export class ChatStreamWithRetry {
  private _isCancelled = false;

  /**
   * Creates a new ChatStreamWithRetry instance.
   *
   * @param requestId - The ID of the chat request
   * @param chatMessage - The chat message data
   * @param startStreamFn - Function to start a chat stream
   * @param maxRetries - Maximum number of retry attempts
   * @param baseDelay - Base delay between retries in milliseconds
   */
  constructor(
    private readonly requestId: string,
    private readonly chatMessage: ChatUserMessageData,
    private readonly startStreamFn: (
      message: ChatUserMessageData,
      opts?: { flags: IChatFlags },
    ) => AsyncGenerator<Partial<ExchangeWithStatus>>,
    private readonly maxRetries: number = 5,
    private readonly baseDelay: number = 4000,
    private readonly flags?: IChatFlags,
  ) {}

  /**
   * Cancels the stream.
   */
  public cancel(): void {
    this._isCancelled = true;
  }

  /**
   * Returns the stream of chat exchange updates.
   *
   * Handles retriable errors on starting the stream. Once the stream has
   * started, any errors are passed through to the caller.
   */
  public async *getStream(): AsyncGenerator<Partial<ExchangeWithStatus>> {
    let retryCount = 0;
    let hasStarted = false;

    try {
      while (!this._isCancelled) {
        const generator = this.startStreamFn(
          { ...this.chatMessage, createdTimestamp: Date.now() },
          this.flags ? { flags: this.flags } : undefined,
        );

        // We'll break out of the loop on retriable errors
        let isRetriableError = false;
        let errorMessage = "";
        let innerRequestId: string | undefined = undefined;

        for await (const chunk of generator) {
          if (chunk.status === ExchangeStatus.failed) {
            // If the underlying error is retriable and the stream has not yet
            // been started: break out, notify the caller, and then retry.  If
            // the stream has been cancelled, we expect to see a failed status chunk
            // that is not retriable.
            if (chunk.isRetriable === true && !hasStarted) {
              // Save the error message in case we can't retry.
              isRetriableError = true;
              errorMessage = chunk.display_error_message || "Service is currently unavailable";
              innerRequestId = chunk.request_id;
              break;
            } else {
              // Either it's not retriable or the stream has already started
              return yield chunk;
            }
          }

          // Successful chunk
          hasStarted = true;
          yield chunk;
        }
        // We'll exit the loop under one of two conditions:
        // 1. We've consumed the stream to completion and we're done, or
        // 2. We encountered a retriable error

        if (!isRetriableError) {
          // Done
          return;
        }

        // Handle retriable errors here.
        // Check for cancellation before attempting to retry, since the delay can be long
        if (this._isCancelled) {
          return yield this.createCancelledStatus();
        }

        // If we've exhausted retries, return a failed exchange
        retryCount++;
        if (retryCount > this.maxRetries) {
          yield {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_id: innerRequestId ?? this.requestId,
            seen_state: SeenState.unseen,
            status: ExchangeStatus.failed,
            display_error_message: errorMessage,
            /* eslint-enable @typescript-eslint/naming-convention */
            isRetriable: false,
          };
          return;
        }

        // Before sleeping, yield a status update to the client.
        const delayMs = this.baseDelay * 2 ** (retryCount - 1);
        for await (const remaining of countdown(delayMs)) {
          yield {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_id: this.requestId,
            status: ExchangeStatus.sent,
            display_error_message: `Service temporarily unavailable. Retrying in ${Math.floor(remaining / 1000)} seconds... (Attempt ${retryCount} of ${this.maxRetries})`,
            /* eslint-enable @typescript-eslint/naming-convention */
            isRetriable: true,
          };
        }

        // Update the client to indicate we're trying again
        yield {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: this.requestId,
          status: ExchangeStatus.sent,
          display_error_message: `Generating response...`,
          /* eslint-enable @typescript-eslint/naming-convention */
          isRetriable: true,
        };
      }

      // Should be here only if we've cancelled the stream
      if (this._isCancelled) {
        yield this.createCancelledStatus();
      }
    } catch (error) {
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: this.requestId,
        seen_state: SeenState.unseen,
        status: ExchangeStatus.failed,
        display_error_message: error instanceof Error ? error.message : String(error),
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    }
  }

  /**
   * Creates a cancelled status exchange object.
   */
  private createCancelledStatus(): Partial<ExchangeWithStatus> {
    return {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_id: this.requestId,
      seen_state: SeenState.unseen,
      status: ExchangeStatus.cancelled,
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  }
}

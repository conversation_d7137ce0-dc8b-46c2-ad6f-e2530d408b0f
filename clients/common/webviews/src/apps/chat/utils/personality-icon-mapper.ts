import { PersonaType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type { ComponentType } from "svelte";
import AgentAuggie from "$common-webviews/src/design-system/icons/augment/auggie/agentAuggie.svelte";
import BrainstormAuggie from "$common-webviews/src/design-system/icons/augment/auggie/brainstormAuggie.svelte";
import PrototyperAuggie from "$common-webviews/src/design-system/icons/augment/auggie/prototyperAuggie.svelte";
import ReviewerAuggie from "$common-webviews/src/design-system/icons/augment/auggie/reviewerAuggie.svelte";

/**
 * Maps a PersonaType to its corresponding icon component
 * @param personaType The personality type
 * @returns The Svelte component for the icon
 */
export function getPersonalityIconComponent(personaType: PersonaType): ComponentType {
  switch (personaType) {
    case PersonaType.DEFAULT:
      return AgentAuggie;
    case PersonaType.PROTOTYPER:
      return PrototyperAuggie;
    case PersonaType.BRAINSTORM:
      return BrainstormAuggie;
    case PersonaType.REVIEWER:
      return ReviewerAuggie;
    default:
      // Default to AgentAuggie for unknown types
      return Agent<PERSON>uggie;
  }
}

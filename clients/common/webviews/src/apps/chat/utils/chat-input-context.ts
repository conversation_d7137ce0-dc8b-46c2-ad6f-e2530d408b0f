import { get } from "svelte/store";
import type { AgentConversationModel } from "../models/agent-conversation-model";
import type { ChatModel } from "../models/chat-model";
import type { ToolsWebviewModel } from "../models/tools-webview-model";
import type { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";

import { ImageController } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Image/controller";
import { type SlashCommandModel } from "../models/slash-command-model";
import { type GitReferenceModel } from "../../remote-agent-manager/models/git-reference-model";
import { type ConversationModel } from "../models/conversation-model";
import { AGENT_LIMIT_MSG } from "../../remote-agent-manager/constants";
import { DEFAULT_MAX_CHAT_INPUT_CHARS } from "$common-webviews/src/apps/chat/models/chat-flags-model";
import { RemoteAgentStatus } from "$vscode/src/remote-agent-manager/types";

export interface ChatInputContext {
  /** Name of the current state (for debugging purposes only) */
  stateName: string;

  /** Whether to show the cancel button instead of send button */
  showCancelButton: boolean;

  /** Whether the action button should be disabled (does not apply to Cancel) */
  isDisabled: boolean;

  /** Action to perform when the send button is clicked */
  action: () => boolean;

  /** Action to perform when the cancel button is clicked */
  cancelAction: () => boolean;

  /** Reason to show for why the button is disabled */
  disabledReason?: string;
}

/**
 * Returns the action for the Remote Agents mode.
 */
export const getRemoteAgentsAction = (
  remoteAgentsModel: RemoteAgentsModel,
  conversationModel: ConversationModel,
  gitRefModel?: GitReferenceModel,
  richTextEditorRoot?: RichTextEditorRoot,
  isInputDisabled?: boolean,
) => {
  return () => {
    if (isInputDisabled) {
      // If the input is already disabled, we should not allow the user to send a message
      // with the Enter key
      return false;
    }
    // Store the message before potentially clearing it
    const message = conversationModel.draftExchange?.request_message ?? "";
    const richTextJson = conversationModel.draftExchange?.rich_text_json_repr;

    // If the message fails to send, we'll restore the draft
    const handleSendFailure = () => {
      // Only restore if there's no current draft (to avoid overwriting a new message)
      if (!conversationModel.draftExchange) {
        conversationModel.saveDraftExchange(message, richTextJson);
      }
    };

    if (!remoteAgentsModel.currentAgentId) {
      // If we're already creating an agent, don't allow another creation
      if (remoteAgentsModel.isCreatingAgent) {
        return false;
      }

      // Set creating agent flag to true
      remoteAgentsModel.setIsCreatingAgent(true);

      // Clear the draft exchange immediately
      conversationModel.clearDraftExchange();

      const createRemoteAgent = async () => {
        try {
          const agentId = await remoteAgentsModel.createRemoteAgentFromDraft(
            message,
            conversationModel.selectedModelId ?? undefined,
          );
          if (agentId) {
            remoteAgentsModel?.setCurrentAgent(agentId);
          } else {
            // Agent creation failed, restore the draft
            handleSendFailure();
          }
        } catch (error) {
          console.error("Failed to create remote agent: ", error);
          // Agent creation failed with an error, restore the draft
          handleSendFailure();
        } finally {
          // Reset creating agent flag regardless of success/failure
          remoteAgentsModel.setIsCreatingAgent(false);
        }
      };

      // If no gitRefModel, we can't check for auth so just assume it's fine
      if (!gitRefModel) {
        createRemoteAgent();
        return true;
      }

      // Check if the user is authenticated with GitHub, if not, we don't create the agent
      gitRefModel
        ?.isGithubAuthenticated()
        .then((isAuthenticated) => {
          if (!isAuthenticated) {
            // Reset creating agent flag if not authenticated
            remoteAgentsModel.setIsCreatingAgent(false);
            // Restore the draft since we're not creating an agent
            handleSendFailure();
            return;
          }
          createRemoteAgent();
        })
        .catch((error) => {
          console.error("Failed to check GitHub authentication status:", error);
          // Reset creating agent flag on error
          remoteAgentsModel.setIsCreatingAgent(false);
          // Restore the draft since we failed to check authentication
          handleSendFailure();
        });
      return true;
    }

    const draftExchange = conversationModel.draftExchange;
    if (draftExchange && remoteAgentsModel) {
      // Clear the draft exchange immediately
      conversationModel.clearDraftExchange();

      const selectedModelId = conversationModel.selectedModelId;
      remoteAgentsModel
        .sendMessage(draftExchange.request_message, selectedModelId ?? undefined)
        .then((success) => {
          if (!success) {
            // Message failed to send, restore the draft
            handleSendFailure();
          }
        })
        .catch(() => {
          // Exception occurred, restore the draft
          handleSendFailure();
        });
    }

    // Blur the editor after sending a message
    // Use setTimeout to ensure the blur happens after the action completes
    setTimeout(() => richTextEditorRoot?.blur(), 50);

    return true;
  };
};

export function getChatInputRemoteAgentContext(
  remoteAgentsModel: RemoteAgentsModel,
  conversationModel: ConversationModel,
  gitRefModel?: GitReferenceModel,
  richTextEditorRoot?: RichTextEditorRoot,
) {
  // We are in the Remote Agent mode
  // Check if agent limit has been reached
  let agentLimitReached = false;
  if (remoteAgentsModel.agentOverviews) {
    agentLimitReached =
      !!remoteAgentsModel.maxRemoteAgents &&
      remoteAgentsModel.agentOverviews.length >= remoteAgentsModel.maxRemoteAgents;
  }
  /** True if the action button should be disabled */
  let isDisabled = false;
  const isRemoteAgentStarting =
    remoteAgentsModel.isCreatingAgent ||
    (remoteAgentsModel?.isActive &&
      remoteAgentsModel?.currentAgent?.status === RemoteAgentStatus.agentStarting);

  const isRemoteAgentFailed =
    remoteAgentsModel?.currentAgent?.status === RemoteAgentStatus.agentFailed;

  if (!remoteAgentsModel.currentAgentId) {
    const newDraft = remoteAgentsModel.newAgentDraft;
    isDisabled =
      !conversationModel.draftExchange?.request_message ||
      remoteAgentsModel.isCreatingAgent ||
      agentLimitReached ||
      !newDraft?.commitRef?.github_commit_ref?.repository_url ||
      !newDraft.selectedBranch?.name;
  } else {
    isDisabled =
      !conversationModel.draftExchange?.request_message ||
      isRemoteAgentStarting ||
      isRemoteAgentFailed;
  }
  const showCancelButton = remoteAgentsModel.isCurrentAgentRunning && !isRemoteAgentStarting;

  let disabledReason = undefined;
  if (isDisabled) {
    if (!conversationModel.draftExchange?.request_message) {
      disabledReason = "Please enter a message";
    } else if (remoteAgentsModel.isCreatingAgent) {
      disabledReason = "Creating agent...";
    } else if (isRemoteAgentStarting) {
      disabledReason = "Agent is starting...";
    } else if (isRemoteAgentFailed) {
      disabledReason = "Agent has failed and cannot accept messages";
    } else if (agentLimitReached) {
      disabledReason = AGENT_LIMIT_MSG.replace(
        "%MAX_AGENTS%",
        remoteAgentsModel.maxRemoteAgents.toString(),
      );
    }
  }
  return {
    stateName: "remoteAgentMode",
    showCancelButton,
    isDisabled,
    disabledReason,
    // Send message to remote agent
    action: getRemoteAgentsAction(
      remoteAgentsModel,
      conversationModel,
      gitRefModel,
      richTextEditorRoot,
      isDisabled,
    ),
    // Interrupt the remote agent
    cancelAction: () => {
      remoteAgentsModel.interruptAgent();
      return true;
    },
  };
}

/**
 * Returns the complete chat input context directly based on the application state
 */
export function getChatInputContext(
  chatModel: ChatModel,
  agentConversationModel: AgentConversationModel,
  toolsWebviewModel: ToolsWebviewModel,
  slashCommandModel: SlashCommandModel,
  remoteAgentsModel?: RemoteAgentsModel,
  gitRefModel?: GitReferenceModel,
  richTextEditorRoot?: RichTextEditorRoot,
): ChatInputContext {
  // Collect the properties we need from the models
  const conversationModel = chatModel.currentConversationModel;
  const activeSlashCommand = get(slashCommandModel.activeCommand);
  const isCurrConversationAgentic = get(agentConversationModel.isCurrConversationAgentic);
  const messageLength = conversationModel.draftExchange?.request_message?.length || 0;
  const isOverCharLimit = messageLength > DEFAULT_MAX_CHAT_INPUT_CHARS;

  const hasLoadingImages = (() => {
    if (chatModel.flags.enableChatMultimodal) {
      const json = conversationModel.draftExchange?.rich_text_json_repr;
      if (!json) {
        return false;
      }
      return ImageController.hasLoadingImages(json);
    }
    return false;
  })();

  if (isOverCharLimit) {
    return {
      stateName: "overCharLimit",
      showCancelButton: false,
      isDisabled: true,
      disabledReason: `Message exceeds the ${DEFAULT_MAX_CHAT_INPUT_CHARS} character limit`,
      action: () => false,
      cancelAction: () => false,
    };
  } else if (remoteAgentsModel?.isActive) {
    return getChatInputRemoteAgentContext(
      remoteAgentsModel,
      conversationModel,
      gitRefModel,
      richTextEditorRoot,
    );
  } else if (hasLoadingImages) {
    // Images are still loading - disable input until they're loaded
    return {
      stateName: "imagesLoading",
      showCancelButton: false,
      isDisabled: true, // Button should be disabled while images load
      // Can't send while images are loading
      action: () => false,
      // Default cancel action
      cancelAction: () => {
        if (isCurrConversationAgentic) {
          agentConversationModel.interruptAgent();
        } else {
          toolsWebviewModel.interruptToolsConversation();
        }
        return true;
      },
    };
  } else if (activeSlashCommand) {
    // A slash command is active
    return {
      stateName: "slashCommandActive",
      showCancelButton: false, // Show send button
      isDisabled: false, // Button should be enabled
      // Run the active slash command
      action: () => {
        slashCommandModel.runActiveCommand();
        conversationModel.clearDraftExchange();
        return true;
      },
      // No cancel action
      cancelAction: () => false,
    };
  } else if (isCurrConversationAgentic) {
    // We are in the Agent mode and there is no special circumstance
    return {
      stateName: "agentMode",
      showCancelButton: false, // Always show send button
      isDisabled: !conversationModel.hasDraft,
      // Interrupt the agent and send the draft
      action: () => {
        agentConversationModel.interruptAgent().then(() => {
          conversationModel.sendDraftExchange();
        });
        return true;
      },
      // Interrupt the agent
      cancelAction: () => {
        agentConversationModel.interruptAgent();
        return true;
      },
    };
  } else {
    // We are in the Chat mode and there is no special circumstance
    return {
      stateName: "chatMode",
      showCancelButton: conversationModel.canCancelMessage,
      isDisabled: !conversationModel.canSendDraft,
      // Send the current message
      action: () => {
        conversationModel.sendDraftExchange();
        return true;
      },
      // Interrupt the conversation
      cancelAction: () => {
        toolsWebviewModel.interruptToolsConversation();
        return true;
      },
    };
  }
}

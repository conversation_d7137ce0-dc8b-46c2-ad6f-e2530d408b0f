import { isChatItemExchangeWithStatus, type ExchangeWithStatus } from "../types/chat-message";
import {
  getOrgRepoFromUrl,
  getRepoNameFromUrl,
} from "../../remote-agent-manager/utils/repository-utils";
import type { IRemoteAgentConversation } from "../../remote-agent-manager/models/remote-agents-model";
import type {
  ChatThread,
  LocalAgentThread,
  RemoteAgentThread,
} from "../components/list/ThreadsList.svelte";

export type SearchableThread = ChatThread | LocalAgentThread | RemoteAgentThread;

/**
 * Extracts searchable text from a thread's chat history user messages
 */
export function getThreadChatHistoryText(
  thread: SearchableThread,
  currentRemoteAgentId?: string,
  currentRemoteAgentConversation?: IRemoteAgentConversation,
): string {
  if (thread.type === "remoteAgent") {
    // For remote agents, we can only search the current conversation if it's loaded
    // This is a limitation since we don't have access to all remote agent conversations
    // in the current architecture. The search will work best for the currently active agent.
    const agentId = thread.id;
    const conversation =
      currentRemoteAgentId === agentId ? currentRemoteAgentConversation : undefined;

    if (conversation) {
      return conversation.exchanges
        .slice(0, 20) // make sure we don't take too long
        .map((exchange) => exchange.exchange.request_message || "")
        .join(" ");
    }

    // If no conversation is loaded, we can still search the session summary and turn summaries
    // which may contain relevant information about user requests
    const agent = thread.agent;
    if (agent) {
      const summaryText = [agent.session_summary || "", ...(agent.turn_summaries || [])].join(" ");
      return summaryText;
    }

    return "";
  } else {
    // For chat and local agent threads, get messages from the conversation
    if (thread.conversation && thread.conversation.chatHistory) {
      return thread.conversation.chatHistory
        .slice(0, 20) // make sure we don't take too long
        .filter((item) => isChatItemExchangeWithStatus(item))
        .map((item) => (item as ExchangeWithStatus).request_message || "")
        .join(" ");
    }
    return "";
  }
}

/**
 * Extracts searchable text from a remote agent's repository information
 */
export function getRemoteAgentRepoText(thread: RemoteAgentThread): string {
  if (!thread.agent || !thread.agent.workspace_setup?.starting_files?.github_commit_ref) {
    return "";
  }

  const repoUrl = thread.agent.workspace_setup.starting_files.github_commit_ref.repository_url;
  const repoName = getRepoNameFromUrl(repoUrl);
  const orgRepo = getOrgRepoFromUrl(repoUrl);

  return [repoUrl, repoName, orgRepo].filter(Boolean).join(" ");
}

/**
 * Performs comprehensive search across thread title, repository info, and chat history
 */
export function getThreadsWithSearchQuery(
  thread: SearchableThread,
  query: string,
  currentRemoteAgentId?: string,
  currentRemoteAgentConversation?: IRemoteAgentConversation,
): boolean {
  if (!query.trim()) {
    return true;
  }

  const searchTerms = query
    .toLowerCase()
    .split(/\s+/)
    .filter((term) => term.length > 0);

  // Collect all searchable text
  const searchableTexts = [
    thread.title,
    thread.type === "remoteAgent" ? getRemoteAgentRepoText(thread) : "",
    getThreadChatHistoryText(thread, currentRemoteAgentId, currentRemoteAgentConversation),
  ].filter(Boolean);

  const combinedText = searchableTexts.join(" ").toLowerCase();

  // Check if all search terms are found in the combined text
  return searchTerms.every((term) => combinedText.includes(term));
}

/**
 * Filters threads based on search query
 */
export function filterThreadsBySearch(
  threads: SearchableThread[],
  searchQuery: string,
  currentRemoteAgentId?: string,
  currentRemoteAgentConversation?: IRemoteAgentConversation,
): SearchableThread[] {
  if (!searchQuery.trim()) {
    return threads;
  }

  return threads.filter((thread) =>
    getThreadsWithSearchQuery(
      thread,
      searchQuery,
      currentRemoteAgentId,
      currentRemoteAgentConversation,
    ),
  );
}

import {
  type AutofixIteration,
  AutofixIterationStage,
  AutofixMessages,
} from "$vscode/src/autofix/autofix-state";
import { describe, expect, test } from "vitest";
import { getIterationMessage } from "./autofix-utils";

describe("getIterationMessage", () => {
  const iteration: AutofixIteration = {
    id: "test-id",
    isFirstIteration: true,
    currentStage: AutofixIterationStage.runTest,
  };

  test("returns null when iteration is undefined", () => {
    expect(getIterationMessage(undefined, AutofixIterationStage.runTest, 1, true)).toBeNull();
  });

  test("returns testRunning message when in runTest stage and command not failed", () => {
    expect(getIterationMessage(iteration, AutofixIterationStage.runTest, 1, true)).toBe(
      AutofixMessages.testRunning,
    );
  });

  test("returns testFailed message when in runTest stage and command failed", () => {
    const failedIteration = { ...iteration, commandFailed: true };
    expect(getIterationMessage(failedIteration, AutofixIterationStage.runTest, 1, false)).toBe(
      AutofixMessages.testFailed,
    );
  });

  test("returns testPassed message when in runTest stage and command not failed", () => {
    const passedIteration = { ...iteration, commandFailed: false };
    expect(getIterationMessage(passedIteration, AutofixIterationStage.runTest, 1, false)).toBe(
      AutofixMessages.testPassed,
    );
  });
});

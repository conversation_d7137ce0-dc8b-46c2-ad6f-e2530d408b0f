import { type SharedWebviewStoreModel } from "$common-webviews/src/common/models/shared-webview-store-model";
import { type ChatHomeWebviewState } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
import type { RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import type { ChatModeModel } from "../models/chat-mode-model";
import type { ChatModel } from "../models/chat-model";

export const subscribeSharedStoreToRemoteAgentsModel = (
  sharedWebviewStore: SharedWebviewStoreModel<ChatHomeWebviewState>,
  remoteAgentsModel: RemoteAgentsModel,
  initialSharedState: ChatHomeWebviewState,
) => {
  if (remoteAgentsModel) {
    sharedWebviewStore.update((state) => {
      if (!state) return initialSharedState;
      const activeWebviews = new Set(state.activeWebviews);
      if (remoteAgentsModel.isActive) {
        activeWebviews.add("chat");
      } else {
        activeWebviews.delete("chat");
      }
      return {
        ...state,
        activeWebviews: Array.from(activeWebviews),
        agentOverviews: remoteAgentsModel.agentOverviews,
        selectedAgentId: remoteAgentsModel.currentAgentId,
        pinnedAgents: remoteAgentsModel.pinnedAgents,
      };
    });
  }
};

export const subscribeRemoteAgentsModelToSharedStore = (
  prevStoreState: ChatHomeWebviewState | undefined,
  sharedWebviewStore: SharedWebviewStoreModel<ChatHomeWebviewState>,
  remoteAgentsModel: RemoteAgentsModel,
  chatModel: ChatModel,
  chatModeModel: ChatModeModel,
) => {
  if (JSON.stringify(prevStoreState) === JSON.stringify(sharedWebviewStore.state)) {
    return;
  }
  if (sharedWebviewStore) {
    const { selectedAgentId, activeWebviews, pinnedAgents } = sharedWebviewStore.state || {};
    if (selectedAgentId !== remoteAgentsModel.currentAgentId) {
      chatModel.extensionClient.showAugmentPanel();
      remoteAgentsModel.setIsActive(true);
      chatModeModel.setToRemoteAgent(selectedAgentId);
      remoteAgentsModel.setCurrentAgent(selectedAgentId);
    }

    // Sync pinned agents from shared store to remote agents model
    if (
      pinnedAgents &&
      JSON.stringify(pinnedAgents) !== JSON.stringify(remoteAgentsModel.pinnedAgents)
    ) {
      remoteAgentsModel.setPinnedAgents(pinnedAgents);
    }

    const nonChatWebviews = (activeWebviews || []).filter((view) => view !== "chat");
    // If the chat view of remote agents is closed, we still want it to poll
    // if there are other active webviews using the remote agents state
    remoteAgentsModel.setExternalRefCount(nonChatWebviews.length);
  }
};

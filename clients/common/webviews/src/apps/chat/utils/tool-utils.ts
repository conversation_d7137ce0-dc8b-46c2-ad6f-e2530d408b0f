import { ImageFormatType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  type ToolResponseContentNode,
  ToolResponseContentNodeType,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import {
  type ChatRequestContentNode,
  ChatRequestContentNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

/**
 * Convert a media type string to an ImageFormatType enum value
 *
 * @param mediaType The media type string (e.g., "image/png", "image/jpeg")
 * @returns The corresponding ImageFormatType enum value
 */
export function getImageFormatFromMediaType(mediaType?: string): ImageFormatType {
  if (!mediaType) {
    return ImageFormatType.IMAGE_FORMAT_UNSPECIFIED;
  }

  const format = mediaType.split("/")[1]?.toLowerCase();
  switch (format) {
    case "jpeg":
    case "jpg":
      return ImageFormatType.JPEG;
    case "png":
      return ImageFormatType.PNG;
    default:
      return ImageFormatType.IMAGE_FORMAT_UNSPECIFIED;
  }
}

/**
 * Convert tool response content nodes to chat request content nodes
 *
 * @param contentNodes The tool response content nodes to convert
 * @param enableDebugFeatures Whether debug features are enabled (required for image support)
 * @returns The converted chat request content nodes
 */
export function convertToolResponseToRequestContentNodes(
  contentNodes: ToolResponseContentNode[],
  enableDebugFeatures: boolean,
): ChatRequestContentNode[] {
  return contentNodes.map((content) => {
    if (content.type === ToolResponseContentNodeType.ContentText) {
      return {
        /* eslint-disable @typescript-eslint/naming-convention */
        type: ChatRequestContentNodeType.CONTENT_TEXT,
        text_content: content.text_content,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    } else if (
      content.type === ToolResponseContentNodeType.ContentImage &&
      content.image_content &&
      enableDebugFeatures
    ) {
      return {
        /* eslint-disable @typescript-eslint/naming-convention */
        type: ChatRequestContentNodeType.CONTENT_IMAGE,
        image_content: {
          image_data: content.image_content.image_data,
          format: getImageFormatFromMediaType(content.image_content.media_type),
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    }
    return {
      /* eslint-disable @typescript-eslint/naming-convention */
      type: ChatRequestContentNodeType.CONTENT_TEXT,
      text_content: "[Error: Invalid content node]",
      /* eslint-enable @typescript-eslint/naming-convention */
    };
  });
}

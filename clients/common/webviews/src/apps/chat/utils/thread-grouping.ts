import { ConversationAge } from "../components/menu/ThreadsMenu.svelte";
import {
  type ChatThread,
  type LocalAgentThread,
  type RemoteAgentThread,
  isActiveThread,
  isUnpausedRemoteAgent,
} from "../components/list/ThreadsList.svelte";

// Re-export types and enums for convenience
export { ConversationAge };
export type {
  ChatThread,
  LocalAgentThread,
  RemoteAgentThread,
} from "../components/list/ThreadsList.svelte";

/**
 * The display order for conversation age groups.
 * This determines the order in which groups appear in the UI.
 */
export const displayOrder: ConversationAge[] = [
  ConversationAge.Pinned,
  ConversationAge.Active,
  ConversationAge.Today,
  ConversationAge.ThisWeek,
  ConversationAge.ThisMonth,
  ConversationAge.Older,
];

/**
 * Represents a group of threads organized by age.
 */
export interface ThreadGroup<T = any> {
  groupTitle: ConversationAge;
  threads: T[];
}

/**
 * Groups threads by their age category (Today, This Week, etc.).
 * Pinned threads are always placed in the Pinned category regardless of their date.
 *
 * @param threads - Array of threads to group
 * @returns Array of thread groups in display order
 */
export function groupThreadsByAge<T extends { sortTimestamp: Date; isPinned?: boolean }>(
  threads: T[],
): ThreadGroup<T>[] {
  const newGroups: Map<ConversationAge, T[]> = new Map();
  const now = new Date();
  const today = new Date(now.setHours(0, 0, 0, 0));
  const timeRanges = [
    { age: ConversationAge.Today, date: today },
    { age: ConversationAge.ThisWeek, date: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000) },
    {
      age: ConversationAge.ThisMonth,
      date: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000),
    },
  ];

  for (const thread of threads) {
    const date = thread.sortTimestamp;
    let age = timeRanges.find((range) => date >= range.date)?.age || ConversationAge.Older;
    if (thread.isPinned) {
      age = ConversationAge.Pinned;
    }

    newGroups.set(age, [...(newGroups.get(age) || []), thread]);
  }

  return displayOrder
    .map((age) => ({
      groupTitle: age,
      threads: sortThreads(newGroups.get(age) || []),
    }))
    .filter((group) => group.threads.length > 0);
}

function sortThreads<T extends { sortTimestamp: Date; isPinned?: boolean }>(threads: T[]): T[] {
  return threads.sort((a, b) => b.sortTimestamp.getTime() - a.sortTimestamp.getTime());
}

/**
 * Groups threads by their age category, with special handling for active remote agents.
 * Active remote agents (running or has_updates) are placed in the Active category.
 * Active remote agents are always included, even if they don't match other filters.
 *
 * @param threads - Array of threads to group (filtered threads)
 * @param allThreads - Array of all threads (unfiltered, for finding active agents)
 * @param currentlySelectedThreadId - The currently selected thread ID to consider as active
 * @returns Array of thread groups in display order
 */
export function groupThreadsByAgeWithActive(
  threads: (ChatThread | LocalAgentThread | RemoteAgentThread)[],
  allThreads: (ChatThread | LocalAgentThread | RemoteAgentThread)[],
  currentlySelectedThreadId?: string,
): ThreadGroup[] {
  // Get all active remote agents from the full list, regardless of filters
  const activeThreads = allThreads.filter((thread) =>
    isActiveThread(thread, currentlySelectedThreadId),
  );

  // Combine filtered threads with active remote agents (remove duplicates)
  const threadIds = new Set(threads.map((t: any) => t.id));
  const additionalActiveAgents = activeThreads.filter((agent: any) => !threadIds.has(agent.id));
  const combinedThreads = [...threads, ...additionalActiveAgents];

  const newGroups: Map<ConversationAge, (ChatThread | LocalAgentThread | RemoteAgentThread)[]> =
    new Map();
  const now = new Date();
  const today = new Date(now.setHours(0, 0, 0, 0));
  const timeRanges = [
    { age: ConversationAge.Today, date: today },
    { age: ConversationAge.ThisWeek, date: new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000) },
    {
      age: ConversationAge.ThisMonth,
      date: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000),
    },
  ];

  for (const thread of combinedThreads) {
    const date = thread.sortTimestamp;
    let age = timeRanges.find((range) => date >= range.date)?.age || ConversationAge.Older;

    // Override with special categories
    if (thread.isPinned) {
      age = ConversationAge.Pinned;
    } else if (isActiveThread(thread, currentlySelectedThreadId)) {
      age = ConversationAge.Active;
    }

    newGroups.set(age, [...(newGroups.get(age) || []), thread]);
  }

  return displayOrder
    .map((age) => ({
      groupTitle: age,
      threads: sortThreads(newGroups.get(age) || []),
    }))
    .filter((group) => group.threads.length > 0);
}

/**
 * Groups threads for display when the list is collapsed.
 * Shows ONLY unpaused remote agents when collapsed.
 *
 * @param threads - Array of threads to group
 * @param currentlySelectedThreadId - The currently selected thread ID to consider as active
 * @returns Array of thread groups containing only active remote agents
 */
export function groupThreadsForCollapsedViewWithActive(
  threads: (ChatThread | LocalAgentThread | RemoteAgentThread)[],
  currentlySelectedThreadId?: string,
): ThreadGroup<(ChatThread | LocalAgentThread | RemoteAgentThread)[]>[] {
  // When collapsed, show ONLY active remote agents
  const activeRemoteAgents = threads.filter(isUnpausedRemoteAgent);

  // If there are no active remote agents, show an empty list
  if (activeRemoteAgents.length === 0) {
    return [];
  }

  // Re-group only the active remote agents
  return groupThreadsByAgeWithActive(activeRemoteAgents, threads, currentlySelectedThreadId);
}

/**
 * Groups threads for display when the list is collapsed.
 * Takes only the first `maxThreads` threads and re-groups them.
 *
 * @param threads - Array of threads to group
 * @param maxThreads - Maximum number of threads to include
 * @returns Array of thread groups for the limited threads
 */
export function groupThreadsForCollapsedView<T extends { sortTimestamp: Date; isPinned?: boolean }>(
  threads: T[],
  maxThreads: number,
): ThreadGroup<T>[] {
  // When collapsed, take only the first maxThreads threads
  const limitedThreads = threads.slice(0, maxThreads);

  // Re-group the limited threads
  return groupThreadsByAge(limitedThreads);
}

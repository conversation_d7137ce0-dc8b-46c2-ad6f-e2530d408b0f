import { describe, test, expect, vi, beforeEach } from "vitest";
import {
  calculateLoadingThreshold,
  calculateBatchSize,
  distanceFromBottom,
  isScrollNearBottom,
  isScrollNearTop,
  debounce,
  throttle,
} from "./scroll-utils";

describe("scroll-utils", () => {
  describe("calculateLoadingThreshold", () => {
    test("should return base threshold for low velocity", () => {
      // For low velocities, the threshold should be the base threshold (200)
      expect(calculateLoadingThreshold(0)).toBe(200);
      expect(calculateLoadingThreshold(100)).toBe(200);
    });

    test("should calculate threshold based on velocity and predict time", () => {
      // For velocity 400, with predictTime 500ms, we would travel 200px
      // So threshold should be max(200, 200) = 200
      expect(calculateLoadingThreshold(400)).toBe(200);

      // For velocity 600, with predictTime 500ms, we would travel 300px
      // So threshold should be max(200, 300) = 300
      expect(calculateLoadingThreshold(600)).toBe(300);

      // For velocity 1200, with predictTime 500ms, we would travel 600px
      // So threshold should be max(200, 600) = 600
      expect(calculateLoadingThreshold(1200)).toBe(600);
    });

    test("should use custom base threshold as number", () => {
      // With baseThreshold 100 and low velocity, should return 100
      expect(calculateLoadingThreshold(0, 100)).toBe(100);

      // With baseThreshold 100 and velocity 1200, should return max(100, 600) = 600
      expect(calculateLoadingThreshold(1200, 100)).toBe(600);
    });

    test("should use custom options object", () => {
      // Custom baseThreshold only
      expect(calculateLoadingThreshold(0, { baseThreshold: 100 })).toBe(100);

      // Custom predictTime only - for velocity 1000, with predictTime 200ms, we would travel 200px
      // So threshold should be max(200, 200) = 200
      expect(calculateLoadingThreshold(1000, { predictTime: 200 })).toBe(200);

      // Both custom baseThreshold and predictTime
      // For velocity 1000, with predictTime 300ms, we would travel 300px
      // So threshold should be max(150, 300) = 300
      expect(calculateLoadingThreshold(1000, { baseThreshold: 150, predictTime: 300 })).toBe(300);
    });
  });

  describe("calculateBatchSize", () => {
    test("should use smaller batch size for low velocity", () => {
      expect(calculateBatchSize(0)).toBe(5); // 10 * 0.5
      expect(calculateBatchSize(100)).toBe(5); // 10 * 0.5
    });

    test("should use base batch size for moderate velocity", () => {
      expect(calculateBatchSize(250)).toBe(10); // base batch size
    });

    test("should increase batch size for fast velocity", () => {
      expect(calculateBatchSize(600)).toBe(15); // 10 * 1.5
    });

    test("should double batch size for very fast velocity", () => {
      expect(calculateBatchSize(1200)).toBe(20); // 10 * 2
    });

    test("should use custom base batch size", () => {
      expect(calculateBatchSize(0, 5)).toBe(2.5); // 5 * 0.5
      expect(calculateBatchSize(1200, 5)).toBe(10); // 5 * 2
    });
  });

  describe("distanceFromBottom", () => {
    test("should calculate distance from bottom", () => {
      const element = {
        scrollTop: 50,
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      expect(distanceFromBottom(element)).toBe(50); // 200 - 50 - 100
    });
  });

  describe("isScrollNearBottom", () => {
    test("should return true when near bottom", () => {
      const element = {
        scrollTop: 150,
        clientHeight: 100,
        scrollHeight: 270,
      } as HTMLElement;

      // Distance from bottom: 270 - 150 - 100 = 20, which is < default buffer (40)
      expect(isScrollNearBottom(element)).toBe(true);
    });

    test("should return false when not near bottom", () => {
      const element = {
        scrollTop: 50,
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      // Distance from bottom: 200 - 50 - 100 = 50, which is > default buffer (40)
      expect(isScrollNearBottom(element)).toBe(false);
    });

    test("should use custom buffer", () => {
      const element = {
        scrollTop: 50,
        clientHeight: 100,
        scrollHeight: 200,
      } as HTMLElement;

      // Distance from bottom: 200 - 50 - 100 = 50
      expect(isScrollNearBottom(element, 60)).toBe(true); // Buffer 60 > 50
      expect(isScrollNearBottom(element, 30)).toBe(false); // Buffer 30 < 50
    });
  });

  describe("isScrollNearTop", () => {
    test("should return true when near top", () => {
      const element = { scrollTop: 20 } as HTMLElement;

      // 20 < default buffer (40)
      expect(isScrollNearTop(element)).toBe(true);
    });

    test("should return false when not near top", () => {
      const element = { scrollTop: 50 } as HTMLElement;

      // 50 > default buffer (40)
      expect(isScrollNearTop(element)).toBe(false);
    });

    test("should use custom buffer", () => {
      const element = { scrollTop: 50 } as HTMLElement;

      expect(isScrollNearTop(element, 60)).toBe(true); // Buffer 60 > 50
      expect(isScrollNearTop(element, 30)).toBe(false); // Buffer 30 < 50
    });
  });

  describe("debounce", () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    test("should debounce function calls", () => {
      const func = vi.fn();
      const debouncedFunc = debounce(func, 100);

      debouncedFunc();
      expect(func).not.toBeCalled();

      debouncedFunc();
      expect(func).not.toBeCalled();

      vi.advanceTimersByTime(50);
      expect(func).not.toBeCalled();

      vi.advanceTimersByTime(50);
      expect(func).toBeCalledTimes(1);
    });

    test("should use latest arguments", () => {
      const func = vi.fn();
      const debouncedFunc = debounce(func, 100);

      debouncedFunc(1);
      debouncedFunc(2);

      vi.advanceTimersByTime(100);
      expect(func).toBeCalledWith(2);
    });
  });

  describe("throttle", () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    test("should throttle function calls", () => {
      const func = vi.fn();
      const throttledFunc = throttle(func, 100);

      throttledFunc(1);
      expect(func).toBeCalledTimes(1);

      throttledFunc(2);
      expect(func).toBeCalledTimes(1); // Still only called once

      vi.advanceTimersByTime(100);
      expect(func).toBeCalledTimes(2); // Called with latest args after throttle period
      expect(func).toHaveBeenLastCalledWith(2);
    });

    test("should call immediately on first invocation", () => {
      const func = vi.fn();
      const throttledFunc = throttle(func, 100);

      throttledFunc(1);
      expect(func).toBeCalledTimes(1);
      expect(func).toBeCalledWith(1);
    });
  });
});

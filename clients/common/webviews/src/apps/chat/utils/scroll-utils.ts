/**
 * Shared utilities for scroll-related operations.
 *
 * Includes:
 * - Threshold calculation based on velocity
 * - Batch size calculation
 * - Performance optimized scroll operations
 */

/**
 * Calculates the loading threshold based on scroll velocity
 * Higher velocity = larger threshold (load earlier)
 *
 * @param velocityPPS Velocity in pixels per second
 * @param baseThresholdOrOptions Either a number for the base threshold or an options object
 * @param baseThresholdOrOptions.baseThreshold Base threshold in pixels (default: 200)
 * @param baseThresholdOrOptions.predictTime Time in ms to predict future scroll position (default: 500)
 * @returns Adjusted threshold in pixels
 */
export function calculateLoadingThreshold(
  velocityPPS: number,
  baseThresholdOrOptions?:
    | number
    | {
        baseThreshold?: number;
        predictTime?: number;
      },
): number {
  const absVelocity = Math.abs(velocityPPS);

  // Handle both number and options object for backward compatibility
  let baseThreshold = 200;
  let predictTime = 500;

  if (typeof baseThresholdOrOptions === "number") {
    baseThreshold = baseThresholdOrOptions;
  } else if (baseThresholdOrOptions) {
    baseThreshold = baseThresholdOrOptions.baseThreshold ?? 200;
    predictTime = baseThresholdOrOptions.predictTime ?? 500;
  }

  // How far we would travel in the predict time if we continue
  // at the current velocity. If we will hit the edge within `predictTime`,
  // this means at our velocity we are within this limit
  const limit = (absVelocity * predictTime) / 1000;

  // So if we are scrolling at 1000 pps, and predictTime is 500ms,
  // we will travel 500px in that time. So we should load when we are
  // within 500px of the edge.
  return Math.max(baseThreshold, limit);
}

/**
 * Calculates the batch size based on scroll velocity
 * Higher velocity = larger batches
 *
 * @param velocityPPS Velocity in pixels per second
 * @param baseBatchSize Base batch size (default: 10)
 * @returns Adjusted batch size
 */
export function calculateBatchSize(velocityPPS: number, baseBatchSize = 10): number {
  const absVelocity = Math.abs(velocityPPS);

  if (absVelocity > 1000) {
    // Very fast scroll - load much more
    return baseBatchSize * 2;
  } else if (absVelocity > 500) {
    // Fast scroll - load more
    return baseBatchSize * 1.5;
  } else if (absVelocity > 200) {
    // Moderate scroll - use base batch size
    return baseBatchSize;
  }

  // Slow or no scroll - use smaller batch size
  return baseBatchSize * 0.5;
}

/**
 * Calculates the distance from the bottom of a scrollable element
 *
 * @param element Scrollable element
 * @returns Distance from bottom in pixels
 */
export function distanceFromBottom(element: HTMLElement): number {
  const { scrollTop, clientHeight, scrollHeight } = element;
  return scrollHeight - scrollTop - clientHeight;
}

/**
 * Determines if the scroll position is near the bottom of the element
 *
 * @param element Scrollable element
 * @param buffer Buffer distance in pixels (default: 40)
 * @returns True if scroll is near bottom
 */
export function isScrollNearBottom(element: HTMLElement, buffer = 40): boolean {
  return distanceFromBottom(element) <= buffer;
}

/**
 * Determines if the scroll position is near the top of the element
 *
 * @param element Scrollable element
 * @param buffer Buffer distance in pixels (default: 40)
 * @returns True if scroll is near top
 */
export function isScrollNearTop(element: HTMLElement, buffer = 40): boolean {
  return element.scrollTop <= buffer;
}

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds
 *
 * @param func Function to debounce
 * @param wait Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait = 100,
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function (...args: Parameters<T>): void {
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      func(...args);
      timeout = null;
    }, wait);
  };
}

/**
 * Creates a throttled function that only invokes func at most once per every limit milliseconds
 *
 * @param func Function to throttle
 * @param limit Limit in milliseconds
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit = 100,
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  let lastArgs: Parameters<T> | null = null;

  return function (...args: Parameters<T>): void {
    lastArgs = args;

    if (!inThrottle) {
      func(...args);
      inThrottle = true;

      setTimeout(() => {
        inThrottle = false;
        if (lastArgs) {
          func(...lastArgs);
          lastArgs = null;
        }
      }, limit);
    }
  };
}

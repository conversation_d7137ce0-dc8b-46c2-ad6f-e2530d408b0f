import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import {
  ChatItemType,
  ExchangeStatus,
  isChatItemSummaryResponse,
  type ExchangeWithStatus,
} from "../types/chat-message";
import type { ChatFlagsModel } from "./chat-flags-model";
import { type ChatModel } from "./chat-model";

export const SUMMARY_PROMPT = `Provide a short summary of this project`;
export const NEW_QUESTIONS_PROMPT =
  "Give me the five most important questions a developer would need answered about this codebase. These questions should be separated by a newline. I want you to say absolutely nothing else.";
export const SUMMARY_PREAMBLE = `Augment just finished syncing with your codebase! Here is a summary of what we saw:\n\n`;

/**
 * This class is used to track new workspaces
 * in the users current session.
 */
export class OnboardingWorkspaceModel implements MessageConsumer {
  constructor(
    private readonly _chatModel: ChatModel,
    private readonly _flagsModel: ChatFlagsModel,
  ) {}

  public handleMessageFromExtension = (e: MessageEvent<any>) => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.shouldShowSummary: {
        this.showProjectSummary();
        break;
      }
    }
    return true;
  };

  async retryProjectSummary(turn: ExchangeWithStatus): Promise<void> {
    const [suggestedQuestions] = await Promise.all([
      this.generateQuestions(),
      this._chatModel.currentConversationModel.resendTurn(turn),
    ]);

    await this.attachQuestions(suggestedQuestions);
  }

  private async showProjectSummary() {
    const generateQuestionsPromise = this.generateQuestions();
    await this.generateSummary();
    const suggestedQuestions = await generateQuestionsPromise;

    this.attachQuestions(suggestedQuestions);
  }

  private async generateQuestions(): Promise<Array<string>> {
    const { responseText: text } =
      await this._chatModel.currentConversationModel.sendSilentExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: NEW_QUESTIONS_PROMPT,
        model_id: this._flagsModel.modelDisplayNameToId["Overview"] ?? undefined,
        disableSelectedCodeDetails: true,
        chatHistory: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    const questions = text.split("\n").filter((q) => q.trim() !== "");
    return questions;
  }

  private async generateSummary(): Promise<void> {
    this._chatModel.extensionClient.showAugmentPanel();
    await this._chatModel.currentConversationModel.sendExchange(
      {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.summaryResponse,
        request_message: SUMMARY_PROMPT,
        model_id: this._flagsModel.modelDisplayNameToId["Overview"] ?? undefined,
        status: ExchangeStatus.draft,
        disableSelectedCodeDetails: true,
        chatHistory: [],
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      true, // this conversation will be in the chat history, but will
      // not count towards billing
    );
  }

  private async attachQuestions(suggestedQuestions: Array<string>) {
    const projectSummaryMsg =
      this._chatModel.currentConversationModel.chatHistory.findLast(isChatItemSummaryResponse);
    if (projectSummaryMsg && projectSummaryMsg.request_id) {
      if (suggestedQuestions) {
        this._chatModel.currentConversationModel.updateChatItem(projectSummaryMsg.request_id, {
          questionsLoaded: true,
          questions: suggestedQuestions,
        });
      }
    }
  }
}

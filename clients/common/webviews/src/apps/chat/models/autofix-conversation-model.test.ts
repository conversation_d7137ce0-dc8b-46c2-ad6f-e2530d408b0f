import { AutofixIterationStage } from "$vscode/src/autofix/autofix-state";
import { describe, expect, test, vi } from "vitest";
import type { IExtensionClient } from "../extension-client";
import { type ChatItem, ChatItemType } from "../types/chat-message";
import { AutofixConversationModel } from "./autofix-conversation-model";
import { type ConversationModel } from "./conversation-model";

class TestKit {
  private _conversationModel: ConversationModel;
  private _autofixConversationModel: AutofixConversationModel;
  private _extensionClient: IExtensionClient;

  constructor() {
    this._conversationModel = {
      extraData: {},
      subscribe: vi.fn(),
      chatHistory: [],
      id: "test-conversation-id",
      addChatItem: (item: ChatItem) => {
        this._conversationModel.chatHistory.push(item);
      },
    } as unknown as ConversationModel;

    this._extensionClient = {
      executeCommand: vi.fn().mockResolvedValue({ output: "test output", returnCode: 1 }),
      launchAutofixPanel: vi.fn().mockResolvedValue({}),
      autofixPlan: vi.fn().mockResolvedValue({
        summary: "Test fix summary",
        originalReplacements: [],
      }),
      sendAutofixStateUpdate: vi.fn().mockResolvedValue(undefined),
    } as unknown as IExtensionClient;

    this._autofixConversationModel = new AutofixConversationModel(
      this._conversationModel,
      this._extensionClient,
    );
  }

  get autofixConversationModel(): AutofixConversationModel {
    return this._autofixConversationModel;
  }

  get conversationModel(): ConversationModel {
    return this._conversationModel;
  }

  get extensionClient(): IExtensionClient {
    return this._extensionClient;
  }
}

describe("AutofixConversationModel", () => {
  test("initializeAutofixConversation sets up initial state correctly", () => {
    const testKit = new TestKit();

    // Act
    testKit.autofixConversationModel.initializeAutofixConversation();

    // Assert
    // Check intro message
    expect(testKit.conversationModel.chatHistory.length).toBe(1);
    expect(testKit.conversationModel.chatHistory[0].chatItemType).toBe(ChatItemType.autofixMessage);

    // Check iterations
    expect(testKit.autofixConversationModel.extraData.autofixIterations).toEqual([]);
  });

  test("updateAutofixIteration updates existing iteration", async () => {
    const testKit = new TestKit();
    testKit.autofixConversationModel.setCommand("test command");

    const iterationId = await testKit.autofixConversationModel.startNewAutofixIteration();

    testKit.autofixConversationModel.updateAutofixIteration({
      id: iterationId,
      currentStage: AutofixIterationStage.applyFix,
    });

    expect(testKit.autofixConversationModel.extraData.autofixIterations![0]).toEqual({
      id: iterationId,
      currentStage: AutofixIterationStage.applyFix,
      commandOutput: "test output",
      commandFailed: true,
      isFirstIteration: true,
      suggestedSolutions: [
        {
          originalReplacements: [],
          summary: "Test fix summary",
        },
      ],
    });
  });

  test("subscribers are notified when updateAutofixIteration is called", async () => {
    const testKit = new TestKit();
    testKit.autofixConversationModel.initializeAutofixConversation();

    testKit.autofixConversationModel.setCommand("test command");
    const iterationId = await testKit.autofixConversationModel.startNewAutofixIteration();

    const subscriber = vi.fn();
    testKit.autofixConversationModel.subscribe(subscriber);

    // Clear initial subscription call
    subscriber.mockClear();

    testKit.autofixConversationModel.updateAutofixIteration({
      id: iterationId,
      currentStage: AutofixIterationStage.applyFix,
    });

    expect(subscriber).toHaveBeenCalledTimes(1);
    expect(subscriber).toHaveBeenCalledWith(testKit.autofixConversationModel);
  });

  test("startNewAutofixIteration creates new iteration and executes command", async () => {
    const testKit = new TestKit();
    testKit.autofixConversationModel.setCommand("test command");

    await testKit.autofixConversationModel.startNewAutofixIteration();

    expect(testKit.extensionClient.executeCommand).toHaveBeenCalledWith(
      expect.any(String), // iteration ID
      "test",
      ["command"],
    );
    expect(testKit.extensionClient.launchAutofixPanel).toHaveBeenCalled();
  });
});

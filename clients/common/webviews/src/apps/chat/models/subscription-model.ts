import { writable, type Writable } from "svelte/store";
import type { IExtensionClient } from "../extension-client";
import type { GetSubscriptionInfoResponse } from "$vscode/src/webview-providers/webview-messages";
import { differenceInDays } from "date-fns";

export enum SubscriptionType {
  enterprise = "enterprise",
  active = "active_subscription",
  inactive = "inactive_subscription",
  unknown = "unknown",
}

export interface SubscriptionInfo {
  type: SubscriptionType;
  daysRemaining?: number;
  usageBalanceDepleted?: boolean;
}

export class SubscriptionModel {
  private static readonly pollIntervalMs = 30000; // 30 sec
  private static readonly warningThresholdDays = 14;
  private static readonly localStorageKey = "subscription-warning-dismissed";

  private pollInterval: ReturnType<typeof setInterval> | null = null;
  private subscriptionInfo: Writable<SubscriptionInfo | null> = writable(null);
  private isDismissed: Writable<boolean> = writable(false);

  constructor(
    private readonly extensionClient: IExtensionClient,
    initialInfo?: SubscriptionInfo,
    initialDismissed?: boolean,
  ) {
    if (initialDismissed !== undefined) {
      this.isDismissed.set(initialDismissed);
    } else {
      // Check if the warning was previously dismissed from localStorage
      try {
        const dismissedData = localStorage.getItem(SubscriptionModel.localStorageKey);
        if (dismissedData) {
          const { dismissed, timestamp } = JSON.parse(dismissedData);
          const dismissedDate = new Date(timestamp);
          const now = new Date();

          // Check if it's a different day (comparing dates without time)
          const isDifferentDay = dismissedDate.toDateString() !== now.toDateString();

          if (dismissed && !isDifferentDay) {
            this.isDismissed.set(true);
          } else {
            // Clear the stored dismissal if it's expired
            localStorage.removeItem(SubscriptionModel.localStorageKey);
          }
        }
      } catch (e) {
        console.error("Failed to read from localStorage:", e);
      }
    }

    if (initialInfo !== undefined) {
      this.subscriptionInfo.set(initialInfo);
    } else {
      // Start polling for subscription info if not provided
      this.fetchSubscriptionInfo();
      this.startPolling();
    }
  }

  public get info(): Writable<SubscriptionInfo | null> {
    return this.subscriptionInfo;
  }

  public get dismissed(): Writable<boolean> {
    return this.isDismissed;
  }

  public dismiss(): void {
    this.isDismissed.set(true);
    try {
      // Store both the dismissal and the timestamp
      localStorage.setItem(
        SubscriptionModel.localStorageKey,
        JSON.stringify({
          dismissed: true,
          timestamp: new Date().toISOString(),
        }),
      );
    } catch (e) {
      console.error("Failed to write to localStorage:", e);
    }
  }

  public shouldShowWarning(info: SubscriptionInfo | null): boolean {
    if (info == null) return false;

    if (info.type === SubscriptionType.inactive || info.usageBalanceDepleted) {
      return true;
    }

    // Only check dismissed state for expiration warnings
    if (info.type === SubscriptionType.active && info.daysRemaining !== undefined) {
      let dismissedValue = false;
      this.isDismissed.subscribe((value) => (dismissedValue = value))();

      return !dismissedValue && info.daysRemaining <= SubscriptionModel.warningThresholdDays;
    }
    return false;
  }

  private async fetchSubscriptionInfo(): Promise<void> {
    try {
      const data = await this.extensionClient.getSubscriptionInfo();
      const info = this.parseSubscriptionInfo(data);
      this.subscriptionInfo.set(info);
    } catch (error) {
      console.error("Error fetching subscription info:", error);
    }
  }

  private parseSubscriptionInfo(data: GetSubscriptionInfoResponse): SubscriptionInfo {
    let type: SubscriptionType = SubscriptionType.unknown;
    let daysRemaining: number | undefined;
    let usageBalanceDepleted: boolean | undefined;

    if (data.data.enterprise) {
      type = SubscriptionType.enterprise;
    } else if (data.data.activeSubscription) {
      type = SubscriptionType.active;
      if (data.data.activeSubscription.endDate) {
        const endDate = new Date(data.data.activeSubscription.endDate);
        if (endDate.getFullYear() > 2000) {
          // Ensure it's not an invalid/zero date
          daysRemaining = Math.max(0, differenceInDays(endDate, new Date()));
        }
      }
      usageBalanceDepleted = data.data.activeSubscription.usageBalanceDepleted;
    } else if (data.data.inactiveSubscription) {
      type = SubscriptionType.inactive;
    }

    return {
      type,
      daysRemaining,
      usageBalanceDepleted,
    };
  }

  private startPolling(): void {
    this.pollInterval = setInterval(() => {
      this.fetchSubscriptionInfo();
    }, SubscriptionModel.pollIntervalMs);
  }

  public dispose = (): void => {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }
  };
}

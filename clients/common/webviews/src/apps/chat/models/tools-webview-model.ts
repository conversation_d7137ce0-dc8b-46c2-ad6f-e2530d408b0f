import { ToolUsePhase } from "../types/tool-use-state";
import { type ToolUseState } from "../types/tool-use-state";
import { type ConversationModel } from "./conversation-model";
import { type IExtensionClient } from "../extension-client";
import {
  ChatItemType,
  ExchangeStatus,
  type ExchangeWithStatus,
  isChatItemAgenticTurnDelimiter,
  isChatItemSuccessfulExchange,
} from "../types/chat-message";
import {
  ChatResultNodeType,
  ChatRequestNodeType,
  type Exchange,
  type ChatRequestContentNode,
  type ChatRequestToolResult,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  ToolHostName,
  type ToolUseResponse,
  ToolResponseContentNodeType,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { truncateMiddle } from "@augment-internal/sidecar-libs/src/utils/strings";
import { type IDisposable } from "monaco-editor";
import { formatHistory } from "./conversation-model";
import { type ChatModel, AgentExecutionMode } from "./chat-model";
import { get } from "svelte/store";
import { cancelLastRunnableToolUse } from "./agent-conversation-utils";
import { convertToolResponseToRequestContentNodes } from "../utils/tool-utils";
import { processToolResultImages } from "../components/conversation/blocks/tools/components/sidecar/image-scaling-util";

/**
 * Manages the lifecycle and state of tool executions in the chat webview.
 *
 * This class is responsible for:
 * - Processing tool use requests from the conversation model
 * - Managing the safety checks and approval flow for tool executions
 * - Handling tool execution state transitions (new → checking safety → running/runnable → completed/error)
 * - Cancelling and terminating tool executions when needed (e.g., conversation switches)
 * - Tracking files that have been modified by tool executions
 *
 * The class maintains an active tool state and ensures only one tool can be active at a time.
 * It also enforces size limits on tool results to prevent oversized responses from overwhelming
 * the UI or context window.
 *
 * Tool execution follows this general flow:
 * 1. New tool use detected in conversation
 * 2. Safety check performed
 * 3. If safe, execute immediately; if unsafe, mark as requiring approval
 * 4. On completion/error, update conversation state and send result
 *
 * @implements {IDisposable} - Cleanup of subscriptions and resources
 * @implements {MessageConsumer} - Handles messages from the extension
 */
export class ToolsWebviewModel implements IDisposable {
  private _disposables: IDisposable[] = [];

  /**
   * Maximum allowed size of the result text in bytes.
   */
  private static readonly maxResultBytes = 64 * 1024; // 64KiB

  constructor(
    private _conversationModel: ConversationModel,
    private _extensionClient: IExtensionClient,
    private _chatModel: ChatModel,
  ) {
    // Avoid repeated processing of the last successful request
    // Note: this is meant to be an optimization only. Spurious duplicate calls are
    // still possible, e.g., as the user switches conversations back and forth.
    let lastSuccessfulRequestId: string | undefined = undefined;

    // Update the tool use state when the last exchange changes.
    const unsub = this._conversationModel.subscribe((updatedModel: ConversationModel) => {
      const lastExchange = updatedModel.lastExchange;
      if (isChatItemSuccessfulExchange(lastExchange) && lastExchange.request_id !== undefined) {
        const requestId = lastExchange.request_id;
        if (lastSuccessfulRequestId !== requestId) {
          this._processLastExchange(requestId, lastExchange);
          lastSuccessfulRequestId = requestId;
        }
      }
    });

    // Cancel any tool runs when switching conversations.
    const unsubOnNewConversation = this._conversationModel.onNewConversation(() => {
      void this._extensionClient.closeAllToolProcesses();
    });

    const unsubHistoryDelete = this._conversationModel.onHistoryDelete(() => {
      void this.cancelActiveToolRun();
    });

    this._disposables.push(
      { dispose: unsub },
      { dispose: unsubOnNewConversation },
      { dispose: unsubHistoryDelete },
    );
  }

  private _activeTool: { id: string; requestId: string; phase: ToolUsePhase } | undefined =
    undefined;
  private _activeToolCancelled: boolean = false;

  /**
   * Process the most recent exchange, updating the tool use state if necessary.
   */
  private _processLastExchange = (requestId: string, exchange: ExchangeWithStatus) => {
    const toolUseNodes = exchange.structured_output_nodes?.filter(
      (node) => node.type === ChatResultNodeType.TOOL_USE,
    );
    for (const toolUseNode of toolUseNodes ?? []) {
      const toolUseContent = toolUseNode.tool_use;
      if (toolUseContent === undefined) {
        continue;
      }
      const toolUseState = this._conversationModel.getToolUseState(
        requestId,
        toolUseContent.tool_use_id,
      );

      // If the tool use is new, validate it exists and check if it is safe to run without user
      // approval. This will trigger the next phase: either running the tool or marking it
      // as runnable, giving the user the opportunity to approve.
      if (toolUseState.phase === ToolUsePhase.new) {
        void this._validateAndCheckTool(
          requestId,
          toolUseContent.tool_use_id,
          toolUseContent.tool_name,
          JSON.parse(toolUseContent.input_json),
          this._conversationModel.chatHistory
            .filter((m): m is ExchangeWithStatus => isChatItemSuccessfulExchange(m))
            .map(formatHistory),
        );
      }

      // If the tool is in an active state but there is no ongoing operation, mark it as
      // cancelled. This happens on reloads or restarts.
      this._markCancelledIfStale(requestId, toolUseContent.tool_use_id, toolUseState.phase);
    }
  };

  /**
   * Validate that a tool exists and check if it's safe to run.
   * This handles the full validation flow for a new tool use.
   */
  private _validateAndCheckTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
  ) => {
    const conversationId = this._conversationModel.id;
    if (this._activeTool !== undefined) {
      console.error("_validateAndCheckTool called while another tool is active", this._activeTool);
      return;
    }

    // Mark the tool as active and checking safety
    this._activeTool = { id: toolUseId, requestId, phase: ToolUsePhase.checkingSafety };
    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.checkingSafety,
    });

    const toolIdentifierResult = await this._extensionClient.getToolIdentifier(toolName);
    if (!toolIdentifierResult.found) {
      // Mark tool as no longer active before reporting error
      this._activeTool = undefined;
      this._reportToolError(requestId, toolUseId, `Tool "${toolName}" does not exist`);
      return;
    }

    // If this is an MCP tool, set the mcp_server_name and mcp_tool_name from the response
    if (toolIdentifierResult.toolIdentifier.hostName === ToolHostName.mcpHost) {
      // Find the tool use node in the conversation model
      const lastExchange = this._conversationModel.lastExchange;
      if (lastExchange?.structured_output_nodes) {
        const toolUseNode = lastExchange.structured_output_nodes.find(
          (node) =>
            node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_use_id === toolUseId,
        );

        if (
          toolUseNode?.tool_use &&
          toolIdentifierResult.mcpToolName &&
          toolIdentifierResult.mcpServerName
        ) {
          // Update the tool use content with the values from the response
          toolUseNode.tool_use.mcp_server_name = toolIdentifierResult.mcpServerName;
          toolUseNode.tool_use.mcp_tool_name = toolIdentifierResult.mcpToolName;
        }
      }
    }

    // In Agent Auto mode, we skip the safety check for non-remote tools. For
    // remote tools however we still want user approval as otherwise the agent
    // can create effects visible to other developers without approval, e.g.:
    // creating a bunch of Linear tickets.
    if (
      get(this._chatModel.agentExecutionMode) === AgentExecutionMode.auto &&
      this._conversationModel.extraData?.isAgentConversation &&
      toolIdentifierResult.toolIdentifier.hostName !== ToolHostName.remoteToolHost
    ) {
      this._activeTool = undefined;
      await this.callTool(requestId, toolUseId, toolName, toolInput, chatHistory, conversationId);
      return;
    }

    // Otherwise, proceed with normal safety check
    const isSafe = await this._extensionClient.checkSafe(toolName, toolInput);

    // Mark the tool as no longer active
    this._activeTool = undefined;

    // Transition to the next state: run the tool or mark it as runnable
    if (isSafe) {
      // This tool call does not need user approval, so we run it right away
      await this.callTool(
        requestId,
        toolUseId,
        toolName,
        toolInput,
        chatHistory,
        this._conversationModel.id,
      );
    } else {
      // This tool call needs user approval, so we mark it as runnable
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: ToolUsePhase.runnable,
      });
    }
  };

  /**
   * Report a tool error by updating the tool state and sending an error exchange.
   */
  private _reportToolError = (requestId: string, toolUseId: string, errorMessage: string) => {
    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.error,
      result: {
        isError: true,
        text: errorMessage,
      },
    });

    // Send the error as a new exchange
    this._sendToolResultExchange(requestId, toolUseId, {
      /* eslint-disable @typescript-eslint/naming-convention */
      content: errorMessage,
      is_error: true,
      tool_use_id: toolUseId,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
  };

  private _sendToolResultExchange = (
    requestId: string,
    toolUseId: string,
    toolResult: ChatRequestToolResult,
  ) => {
    // This condition is a catch-all for any remaining races and is a stronger
    // check than just confirming that we're still in the same conversation.
    // If the most recent exchange in the conversation does not contain the tool
    // use, then sending the tool result will result in at best one failed
    // request, and at worst a permanently corrupted conversation.
    const lastToolUseState = this._conversationModel.getLastToolUseState() as ToolUseState;
    if (lastToolUseState?.requestId === requestId && lastToolUseState?.toolUseId === toolUseId) {
      this._conversationModel.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "",
        status: ExchangeStatus.draft,
        structured_request_nodes: [
          {
            id: 1,
            type: ChatRequestNodeType.TOOL_RESULT,
            tool_result_node: toolResult,
          },
        ],
        model_id: this._conversationModel.selectedModelId ?? undefined,
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    } else {
      this._extensionClient.reportError({
        originalRequestId: requestId,
        sanitizedMessage: `Tried to send tool result after conversation advanced`,
        stackTrace: "",
        diagnostics: [
          {
            key: "tool_use_id",
            value: toolUseId,
          },
        ],
      });
    }
  };

  /**
   * Call a tool.
   */
  callTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
    conversationId: string,
  ) => {
    if (this._activeTool !== undefined) {
      console.error(
        "callTool called while another tool is active",
        "tried to run:",
        toolUseId,
        "active tool:",
        this._activeTool,
      );
      return;
    }

    // Mark the tool as running
    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.running,
    });
    this._activeTool = { id: toolUseId, requestId, phase: ToolUsePhase.running };

    let toolResultNode: ChatRequestToolResult | undefined = undefined;
    try {
      // Call the tool
      const resultClipped = clipResultIfLarge(
        await this._extensionClient.callTool(
          requestId,
          toolUseId,
          toolName,
          toolInput,
          chatHistory,
          conversationId,
        ),
        ToolsWebviewModel.maxResultBytes,
      );
      const resultProcessed = await processToolResultImages(
        resultClipped,
        this._chatModel.flags.enableDebugFeatures,
      );

      if (this._activeToolCancelled) {
        this._activeToolCancelled = false;
        this._activeTool = undefined;
        this._conversationModel.updateToolUseState({
          requestId,
          toolUseId,
          phase: ToolUsePhase.cancelled,
          result: resultProcessed,
        });
        return;
      }

      // Update the tool use state based on the result
      let nextPhase = resultProcessed.isError ? ToolUsePhase.error : ToolUsePhase.completed;
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: nextPhase,
        result: resultProcessed,
      });

      let toolResultContentNodes: ChatRequestContentNode[] | undefined = undefined;
      if (resultProcessed.contentNodes) {
        // Convert the content nodes to the format expected by the chat API
        toolResultContentNodes = convertToolResponseToRequestContentNodes(
          resultProcessed.contentNodes,
          this._chatModel.flags.enableDebugFeatures,
        );

        toolResultNode = {
          /* eslint-disable @typescript-eslint/naming-convention */
          content: "", // Only for backward compatibility
          is_error: resultProcessed.isError,
          request_id: resultProcessed.requestId,
          tool_use_id: toolUseId,
          content_nodes: toolResultContentNodes,
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      } else {
        toolResultNode = {
          /* eslint-disable @typescript-eslint/naming-convention */
          content: resultProcessed.text,
          is_error: resultProcessed.isError,
          request_id: resultProcessed.requestId,
          tool_use_id: toolUseId,
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      }
    } catch (error) {
      if (this._activeToolCancelled) {
        this._activeToolCancelled = false;
        this._activeTool = undefined;
        this._conversationModel.updateToolUseState({
          requestId,
          toolUseId,
          phase: ToolUsePhase.cancelled,
        });
        return;
      }

      // If we weren't able to invoke the tool (e.g., the tool doesn't exist or we can't
      // run it for some other reason), mark it as an error.
      const errorMessage = error instanceof Error ? error.message : String(error);
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: ToolUsePhase.error,
        result: {
          isError: true,
          text: errorMessage,
        },
      });
      toolResultNode = {
        /* eslint-disable @typescript-eslint/naming-convention */
        content: errorMessage,
        is_error: true,
        tool_use_id: toolUseId,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    }

    this._activeTool = undefined;
    this._sendToolResultExchange(requestId, toolUseId, toolResultNode);
  };

  /**
   * Cancel a tool run.
   */
  cancelToolRun = async (requestId: string, toolUseId: string) => {
    if (
      this._activeTool === undefined ||
      this._activeTool.requestId !== requestId ||
      this._activeTool.id !== toolUseId
    ) {
      console.error(
        "cancelToolRun called while another tool is active",
        "tried to cancel:",
        toolUseId,
        "active tool:",
        this._activeTool,
      );
      return;
    }
    this._activeTool = { id: toolUseId, requestId, phase: ToolUsePhase.cancelling };

    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.cancelling,
    });

    this._activeToolCancelled = true;
    await this._extensionClient.cancelToolRun(requestId, toolUseId);

    // Mark the tool as no longer active
    this._activeTool = undefined;
  };

  skipToolRun = async (requestId: string, toolUseId: string) => {
    if (this._activeTool !== undefined) {
      console.error(
        "skipToolRun called while another tool is active",
        "tried to cancel:",
        toolUseId,
        "active tool:",
        this._activeTool,
      );
      return;
    }

    this._conversationModel.updateToolUseState({
      requestId,
      toolUseId,
      phase: ToolUsePhase.cancelled,
    });
    this._sendToolResultExchange(requestId, toolUseId, {
      /* eslint-disable @typescript-eslint/naming-convention */
      content: "Tool did not run. User clicked `Skip` to cancel.",
      is_error: true,
      tool_use_id: toolUseId,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
  };

  /**
   * Mark a tool as cancelled if it is in an active state but there is no ongoing operation.
   *
   * This should only happen in scenarios like restart or extension reload.
   */
  private _markCancelledIfStale = (requestId: string, toolUseId: string, phase: ToolUsePhase) => {
    // If there is an ongoing operation for this tool, nothing to do
    if (
      this._activeTool !== undefined &&
      this._activeTool.id === toolUseId &&
      this._activeTool.requestId === requestId
    ) {
      return;
    }

    // If the tool has no ongoing operation but it is in an active state, mark it as cancelled
    if (
      phase === ToolUsePhase.new ||
      phase === ToolUsePhase.checkingSafety ||
      phase === ToolUsePhase.cancelling ||
      phase === ToolUsePhase.running
    ) {
      this._conversationModel.updateToolUseState({
        requestId,
        toolUseId,
        phase: ToolUsePhase.cancelled,
      });
    }
  };

  cancelActiveToolRun = async () => {
    if (this._activeTool !== undefined) {
      await this.cancelToolRun(this._activeTool.requestId, this._activeTool.id);
    }
  };

  /**
   * Interrupt all tools, including the active tool and any runnable tools.
   */
  interruptAllTools = async () => {
    cancelLastRunnableToolUse(this._conversationModel);
    await this.cancelActiveToolRun();

    // Get last exchange. If it is an agentic turn delimiter, we don't need to do anything.
    const lastExchange = this._conversationModel.lastExchange;
    if (!lastExchange || isChatItemAgenticTurnDelimiter(lastExchange)) {
      return;
    }

    // Add in a fake, cancelled exchange to the end
    this._conversationModel.addChatItem({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_id: crypto.randomUUID(),
      status: ExchangeStatus.cancelled,
      chatItemType: ChatItemType.agenticTurnDelimiter,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
  };

  /**
   * Interrupt all tools and the current message.
   *
   * This is used when the user wants to interrupt the agent's current task
   * or switch to a different conversation.
   */
  interruptToolsConversation = async () => {
    await this._conversationModel.cancelMessage();
    await this.interruptAllTools();
  };

  /**
   * Gets the checkpoint number created by a specific tool call
   * @param requestId The request ID
   * @param toolUseId The tool use ID
   * @returns The checkpoint number or undefined if not found
   */
  public async getToolCallCheckpoint(requestId: string): Promise<number | undefined> {
    try {
      const response = await this._extensionClient.getToolCallCheckpoint(requestId);
      return response;
    } catch (e) {
      console.error("Failed to get tool call checkpoint:", e);
      return undefined;
    }
  }

  public dispose = (): void => {
    this._disposables.forEach((disposable) => disposable.dispose());
  };
}

/**
 * Process a tool result and shorten its output if it is too large.
 *
 * If the result is too large, returns a new response with the middle of
 * the output removed. The error status and other fields are preserved.
 *
 * @param response the tool use response being processed
 * @param maxResultBytes maximum tool output desired, in bytes
 */
export function clipResultIfLarge(
  response: ToolUseResponse,
  maxResultBytes: number,
): ToolUseResponse {
  // Handle structured responses
  if (response.contentNodes && response.contentNodes.length > 0) {
    // Only truncate text content items, leave images as is
    const clippedContents = response.contentNodes.map((content) => {
      if (content.type === ToolResponseContentNodeType.ContentText) {
        let truncTextContent = "";
        if (content.text_content) {
          truncTextContent = truncateMiddle(
            content.text_content,
            maxResultBytes / response.contentNodes!.length,
          );
        }

        return {
          ...content,
          /* eslint-disable @typescript-eslint/naming-convention */
          text_content: truncTextContent,
          /* eslint-enable @typescript-eslint/naming-convention */
        };
      }
      return content;
    });

    return {
      ...response,
      contentNodes: clippedContents,
    };
  }

  // Handle regular text responses
  return {
    ...response,
    text: truncateMiddle(response.text, maxResultBytes),
  };
}

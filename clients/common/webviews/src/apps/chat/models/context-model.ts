import { LRUCache } from "lru-cache";

import {
  SyncingStatus,
  type ISourceFolderInfo,
  type SyncingStatusEvent,
} from "$vscode/src/workspace/types";
import { SyncingEnabledState } from "$vscode/src/workspace/types";
import { ContextStatus, type IContextInfo, type WorkspaceSyncingOverview } from "./types";
import {
  type GuidelinesStates,
  WebViewMessageType,
  type FileDetails,
  type IChatActiveContext,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { type Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import {
  AGENT_MEMORIES,
  fileDetailsToMentionable,
  isItemExternalSource,
  isItemFile,
  isItemFolder,
  isItemPersonality,
  isItemRecentFile,
  isItemRule,
  isItemSelection,
  isItemSourceFolder,
  isItemUserGuidelines,
  ruleToMentionable,
  type IChatMentionable,
  type IChatMentionableItem,
} from "../types/mention-option";
import type { KeyboardShortcutCommand } from "@tiptap/core";
import type { Editor } from "@tiptap/core";
import {
  type UserGuidelinesState,
  type WorkspaceGuidelinesState,
} from "$vscode/src/chat/guidelines-types";

export const syncProgressEventName = "sync-progress-event";

export class SpecialContextInputModel implements MessageConsumer {
  private _syncStatus: SyncingStatusEvent = {
    status: SyncingStatus.done,
    foldersProgress: [],
  };
  private _syncEnabledState: SyncingEnabledState = SyncingEnabledState.initializing;
  private _workspaceGuidelines: WorkspaceGuidelinesState[] = [];
  private _openUserGuidelinesInput: boolean = false;
  private _userGuidelines: UserGuidelinesState | undefined;

  private _contextStore: ContextInfoStore<IChatMentionable> = new ContextInfoStore();
  private _prevOpenFiles: IChatMentionable[] = [];
  private _disableContext: boolean = false;
  private _enableAgentMemories: boolean = false;

  private subscribers: Set<(model: SpecialContextInputModel) => void> = new Set();

  constructor() {
    this.clearFiles();
  }

  subscribe = (sub: (model: SpecialContextInputModel) => void): (() => void) => {
    this.subscribers.add(sub);
    sub(this);
    return () => {
      this.subscribers.delete(sub);
    };
  };

  handleMessageFromExtension = (e: MessageEvent<WebViewMessage>): boolean => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.sourceFoldersUpdated: {
        this.onSourceFoldersUpdated(msg.data.sourceFolders);
        break;
      }
      case WebViewMessageType.sourceFoldersSyncStatus: {
        this.onSyncStatusUpdated(msg.data);
        break;
      }
      case WebViewMessageType.fileRangesSelected: {
        this.updateSelections(msg.data);
        break;
      }
      case WebViewMessageType.currentlyOpenFiles: {
        this.setCurrentlyOpenFiles(msg.data);
        break;
      }
      case WebViewMessageType.syncEnabledState: {
        this.onSyncEnabledStateUpdate(msg.data);
        break;
      }
      case WebViewMessageType.updateGuidelinesState: {
        this.onGuidelinesStateUpdate(msg.data);
        break;
      }
      default:
        return false;
    }
    return true;
  };

  get files(): (IChatMentionableItem<"file"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this._contextStore.values.filter(
      (f: IChatMentionable): f is IChatMentionableItem<"file"> & IContextInfo =>
        isItemFile(f) && !isItemRecentFile(f),
    );
  }

  get recentFiles(): (IChatMentionableItem<"recentFile"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this._contextStore.values.filter(isItemRecentFile);
  }

  get userGuidelinesText(): string {
    return this._userGuidelines?.contents ?? "";
  }

  get selections(): (IChatMentionableItem<"selection"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this._contextStore.values.filter(isItemSelection);
  }

  get folders(): (IChatMentionableItem<"folder"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this._contextStore.values.filter(isItemFolder);
  }

  get sourceFolders(): (IChatMentionableItem<"sourceFolder"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this._contextStore.values.filter(isItemSourceFolder);
  }

  get externalSources(): (IChatMentionableItem<"externalSource"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this._contextStore.values.filter(isItemExternalSource);
  }

  get userGuidelines(): (IChatMentionableItem<"userGuidelines"> & IContextInfo)[] {
    return this._contextStore.values.filter(isItemUserGuidelines);
  }

  get agentMemories(): (IChatMentionableItem<"agentMemories"> & IContextInfo)[] {
    return [
      {
        ...AGENT_MEMORIES,
        status: this._enableAgentMemories ? ContextStatus.active : ContextStatus.inactive,
        referenceCount: 1,
      },
    ];
  }

  get rules(): (IChatMentionableItem<"rule"> & IContextInfo)[] {
    return this._contextStore.values.filter(
      (item: IChatMentionable): item is IChatMentionableItem<"rule"> & IContextInfo =>
        isItemRule(item),
    );
  }

  get activeFiles(): (IChatMentionableItem<"file"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this.files.filter((f) => f.status === ContextStatus.active);
  }

  get activeRecentFiles(): (IChatMentionableItem<"recentFile"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this.recentFiles.filter((f) => f.status === ContextStatus.active);
  }

  get activeExternalSources(): (IChatMentionableItem<"externalSource"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this.externalSources.filter(
      (f: IChatMentionableItem<"externalSource"> & IContextInfo) =>
        f.status === ContextStatus.active,
    );
  }

  get activeSelections(): (IChatMentionableItem<"selection"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this.selections.filter((f) => f.status === ContextStatus.active);
  }

  get activeSourceFolders(): (IChatMentionableItem<"sourceFolder"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this.sourceFolders.filter((f) => f.status === ContextStatus.active);
  }

  get activeRules(): (IChatMentionableItem<"rule"> & IContextInfo)[] {
    if (this._disableContext) return [];
    return this.rules.filter((f) => f.status === ContextStatus.active);
  }

  get syncStatus(): SyncingStatus {
    return this._syncStatus.status;
  }

  get syncEnabledState(): SyncingEnabledState {
    return this._syncEnabledState;
  }

  get syncProgress(): WorkspaceSyncingOverview | undefined {
    if (this.syncEnabledState === SyncingEnabledState.disabled) {
      return undefined;
    }

    if (!this._syncStatus.foldersProgress) {
      return undefined;
    }

    const folderProgresses = this._syncStatus.foldersProgress.filter(
      (fp) => fp.progress !== undefined,
    );
    if (folderProgresses.length === 0) {
      // We have no progresses defined, meaning the workspace manager is not
      // yet initialized.
      return undefined;
    }

    const totalFilesRaw = folderProgresses.reduce((acc, curr) => {
      return acc + (curr?.progress?.trackedFiles ?? 0);
    }, 0);
    const backlogSizeRaw = folderProgresses.reduce((acc, curr) => {
      return acc + (curr?.progress?.backlogSize ?? 0);
    }, 0);

    // Ensure the values are valid
    const totalFiles = Math.max(totalFilesRaw, 0);
    const backlogSize = Math.min(Math.max(backlogSizeRaw, 0), totalFiles);

    const syncedCount = totalFiles - backlogSize;
    const newlyTrackedFolders = [];
    for (const folderProgress of folderProgresses) {
      if (folderProgress?.progress?.newlyTracked) {
        newlyTrackedFolders.push(folderProgress.folderRoot);
      }
    }

    return {
      status: this._syncStatus.status,
      totalFiles,
      syncedCount,
      backlogSize,
      newlyTrackedFolders,
    };
  }

  get contextCounts(): number {
    return this._contextStore.values.length ?? 0;
  }

  /**
   * Get the active context for the chat model.
   */
  get chatActiveContext(): IChatActiveContext {
    return {
      userSpecifiedFiles: [
        ...this.activeFiles.map((f: IChatMentionableItem<"file">) => ({
          rootPath: f.file.repoRoot,
          relPath: f.file.pathName,
        })),
      ],
      ruleFiles: this.activeRules.map((f: IChatMentionableItem<"rule">) => f.rule),
      recentFiles: this.activeRecentFiles.map((f: IChatMentionableItem<"recentFile">) => ({
        rootPath: f.recentFile.repoRoot,
        relPath: f.recentFile.pathName,
      })),
      externalSources: this.activeExternalSources.map((f) => f.externalSource),
      selections: this.activeSelections.map((f) => f.selection),
      sourceFolders: this.activeSourceFolders.map((f) => ({
        rootPath: f.sourceFolder.folderRoot,
        relPath: "",
      })),
    };
  }

  onSourceFoldersUpdated = (sourceFolders: ISourceFolderInfo[]) => {
    const currSourceFolders = this.sourceFolders;
    sourceFolders = this.updateSourceFoldersWithGuidelines(sourceFolders);
    this._contextStore.update(
      sourceFolders.map((f: ISourceFolderInfo) => ({
        sourceFolder: f,
        status: ContextStatus.active,
        label: f.folderRoot,
        showWarning: f.guidelinesOverLimit,
        id: f.folderRoot + String(f.guidelinesEnabled) + String(f.guidelinesOverLimit),
      })),
      currSourceFolders,
      (f) => f.id,
    );
    this.notifySubscribers();
  };

  onSyncStatusUpdated = (syncStatus: SyncingStatusEvent) => {
    this._syncStatus = syncStatus;
    this.notifySubscribers();
  };

  get recentItems(): (IChatMentionable & IContextInfo)[] {
    if (this._disableContext) {
      // Only return user guidelines
      return this.userGuidelines;
    }

    // Get all regular items (files, selections, etc.) excluding source folders, guidelines, and rules
    const regularItems = this._contextStore.values.filter(
      (item: IChatMentionable) =>
        !isItemSourceFolder(item) &&
        !isItemUserGuidelines(item) &&
        !isItemPersonality(item) &&
        !isItemRule(item),
    );

    // Return in the order: regular items, source folders, rules, user guidelines, agent memories
    // Note: This will be reversed in ContextBar.svelte, resulting in:
    // Memories -> User Guidelines -> Rules -> Repo Guidelines -> Codebase -> Specific files/references
    return [
      ...regularItems,
      ...this.sourceFolders,
      ...this.rules,
      ...this.userGuidelines,
      ...this.agentMemories,
    ];
  }

  get recentActiveItems(): (IChatMentionable & IContextInfo)[] {
    return this.recentItems.filter((f) => f.status === ContextStatus.active);
  }

  get recentInactiveItems(): (IChatMentionable & IContextInfo)[] {
    return this.recentItems.filter((f) => f.status === ContextStatus.inactive);
  }

  disableContext = () => {
    this._disableContext = true;
    this.notifySubscribers();
  };

  enableContext = () => {
    this._disableContext = false;
    this.notifySubscribers();
  };

  get isContextDisabled(): boolean {
    return this._disableContext;
  }

  addFile = (file: FileDetails) => {
    this.addFiles([file]);
  };

  addFiles = (files: FileDetails[]) => {
    this.updateFiles(files, []);
  };

  removeFile = (file: FileDetails) => {
    this.removeFiles([file]);
  };

  removeFiles = (files: FileDetails[]) => {
    this.updateFiles([], files);
  };

  updateItems = (added: IChatMentionable[], removed: IChatMentionable[]) => {
    this.updateItemsInplace(added, removed);
    this.notifySubscribers();
  };

  private updateItemsInplace = (added: IChatMentionable[], removed: IChatMentionable[]) => {
    this._contextStore.update(added, removed, (f) => f.id);
  };

  updateFiles = (added: FileDetails[], removed: FileDetails[]) => {
    const toIChatMentionableItem = (f: FileDetails) => ({
      file: f,
      ...fileDetailsToMentionable(f),
    });
    const addItems = added.map(toIChatMentionableItem);
    const removeItems = removed.map(toIChatMentionableItem);
    this._contextStore.update(addItems, removeItems, (f) => f.id);
    this.notifySubscribers();
  };

  updateRules = (added: Rule[], removed: Rule[]) => {
    const toIChatMentionableItem = (rule: Rule) => ({
      rule,
      ...ruleToMentionable(rule),
    });
    const addItems = added.map(toIChatMentionableItem);
    const removeItems = removed.map(toIChatMentionableItem);
    this._contextStore.update(addItems, removeItems, (f) => f.id);
    this.notifySubscribers();
  };

  enableAgentMemories = () => {
    this._enableAgentMemories = true;
    this.notifySubscribers();
  };

  disableAgentMemories = () => {
    this._enableAgentMemories = false;
    this.notifySubscribers();
  };

  setCurrentlyOpenFiles = (files: FileDetails[]) => {
    const toIChatMentionableItem = (f: FileDetails) => ({
      recentFile: f,
      ...fileDetailsToMentionable(f),
    });

    const addItems = files.map(toIChatMentionableItem);
    const removeItems = this._prevOpenFiles;
    this._prevOpenFiles = addItems;

    this._contextStore.update(addItems, removeItems, (f) => f.id);
    // If an open file was removed, move `recentFile` to `file` if it still exists
    removeItems.forEach((f) => {
      const maybeItem = this._contextStore.peekKey(f.id);
      if (maybeItem?.recentFile) {
        maybeItem.file = maybeItem.recentFile;
        delete maybeItem.recentFile;
      }
    });
    // If an open file was added, move `file` to `recentFile` if it still exists
    addItems.forEach((f) => {
      const maybeItem = this._contextStore.peekKey(f.id);
      if (maybeItem?.file) {
        maybeItem.recentFile = maybeItem.file;
        delete maybeItem.file;
      }
    });
    this.notifySubscribers();
  };

  onSyncEnabledStateUpdate = (state: SyncingEnabledState) => {
    this._syncEnabledState = state;
    this.notifySubscribers();
  };

  updateUserGuidelines = (userGuidelinesState: UserGuidelinesState) => {
    const existingUserGuidelines = this.userGuidelines;
    const userGuidelinesValue: IChatMentionableItem<"userGuidelines"> & IContextInfo = {
      userGuidelines: userGuidelinesState,
      label: "User Guidelines",
      id: "userGuidelines",
      status: ContextStatus.active,
      referenceCount: 1,
      showWarning: userGuidelinesState.overLimit,
    };
    this._contextStore.update(
      [userGuidelinesValue],
      existingUserGuidelines,
      (f) => f.id + String(f.userGuidelines?.enabled) + String(f.userGuidelines?.overLimit),
    );
    this.notifySubscribers();
  };

  onGuidelinesStateUpdate = (state: GuidelinesStates) => {
    this._userGuidelines = state.userGuidelines;
    this._workspaceGuidelines = state.workspaceGuidelines ?? [];
    const userGuidelinesState = state.userGuidelines;
    if (userGuidelinesState) {
      this.updateUserGuidelines(userGuidelinesState);
    }
    this.onSourceFoldersUpdated(this.sourceFolders.map((f) => f.sourceFolder));
  };

  private updateSourceFoldersWithGuidelines = (
    sourceFolders: ISourceFolderInfo[],
  ): ISourceFolderInfo[] => {
    return sourceFolders.map((f) => {
      const guidelinesState = this._workspaceGuidelines.find(
        (s) => s.workspaceFolder === f.folderRoot,
      );
      return {
        ...f,
        guidelinesEnabled: guidelinesState?.enabled ?? false,
        guidelinesOverLimit: guidelinesState?.overLimit ?? false,
        guidelinesLengthLimit: guidelinesState?.lengthLimit ?? 2000,
      };
    });
  };

  toggleStatus = (item: IChatMentionable) => {
    this._contextStore.toggleStatus(item.id);
    this.notifySubscribers();
  };

  updateExternalSources = (
    added: IChatMentionableItem<"externalSource">[],
    removed: IChatMentionableItem<"externalSource">[],
  ) => {
    this._contextStore.update(added, removed, (s) => s.id);
    this.notifySubscribers();
  };

  clearFiles = () => {
    this._contextStore.update([], this.files, (f) => f.id);
    this.notifySubscribers();
  };

  updateSelections = (ranges: FileDetails[]) => {
    // We need all selections even if context is disabled/inactive.
    // This is because the context model stores all selections, even if context is disabled.
    // Using the external API to get selections will result in us *not* removing selections
    // that the context store is tracking
    const currSelections = this._contextStore.values.filter(isItemSelection);
    this._contextStore.update(
      ranges.map((r: FileDetails) => ({
        selection: r,
        ...fileDetailsToMentionable(r),
      })),
      // We want to remove all selections and replace them with the ones we just added
      currSelections,
      (f) => f.id,
    );
    this.notifySubscribers();
  };

  maybeHandleDelete: KeyboardShortcutCommand = ({ editor }: { editor: Editor }) => {
    if (
      editor.state.selection.empty &&
      editor.state.selection.$anchor.pos === 1 &&
      this.recentActiveItems.length > 0
    ) {
      const mostRecentItem = this.recentActiveItems[0];
      this.markInactive(mostRecentItem);
      return true;
    }
    return false;
  };

  markInactive = (item: IChatMentionable) => {
    this.markItemsInactive([item]);
  };

  markItemsInactive = (items: IChatMentionable[]) => {
    items.forEach((item) => {
      this._contextStore.setStatus(item.id, ContextStatus.inactive);
    });
    this.notifySubscribers();
  };

  markAllInactive = () => {
    this.markItemsInactive(this.recentActiveItems);
  };

  markActive = (item: IChatMentionable) => {
    this.markItemsActive([item]);
  };

  markItemsActive = (items: IChatMentionable[]) => {
    items.forEach((item) => {
      this._contextStore.setStatus(item.id, ContextStatus.active);
    });
    this.notifySubscribers();
  };

  markAllActive = () => {
    this.markItemsActive(this.recentInactiveItems);
  };

  unpin = (item: IChatMentionable) => {
    this._contextStore.unpin(item.id);
    this.notifySubscribers();
  };

  togglePinned = (item: IChatMentionable) => {
    this._contextStore.togglePinned(item.id);
    this.notifySubscribers();
  };

  private notifySubscribers = () => {
    this.subscribers.forEach((sub) => sub(this));
  };
}

/**
 * This class is used to store context information.
 *
 * It is used to store the context information for files, selections, and source folders, and any other type that
 * can be represented by some unique key. We use this to track different types of context in the same way.
 */
class ContextInfoStore<TBase> {
  private _cache: LRUCache<string, TBase & IContextInfo, unknown> = new LRUCache({ max: 1000 });

  constructor() {}

  get store(): Record<string, TBase & IContextInfo> {
    return Object.fromEntries(this._cache.entries());
  }

  get values(): (TBase & IContextInfo)[] {
    return [...this._cache.values()];
  }

  peekKey = (key: string): (TBase & IContextInfo) | undefined => {
    return this._cache.get(key, { updateAgeOnGet: false });
  };

  clear = () => {
    this._cache.clear();
  };

  /**
   * Updates the store with the given added and removed items.
   *
   * @param added - The items to be added to the store.
   * @param removed - The items to be removed from the store.
   * @param keyFn - A function that generates a key for each item.
   */
  update = (
    added: (TBase & Partial<IContextInfo>)[],
    removed: TBase[],
    keyFn: (t: TBase) => string,
  ) => {
    added.forEach((item: TBase & Partial<IContextInfo>) => this.addInPlace(item, keyFn));
    removed.forEach((item: TBase) => this.removeInPlace(item, keyFn));
  };

  /**
   * Removes the given item from the store.
   *
   * @param item - The item to be removed from the store.
   * @param keyFn - A function that generates a key for the item.
   */
  removeFromStore = (item: TBase, keyFn: (t: TBase) => string) => {
    const key = keyFn(item);
    this._cache.delete(key);
  };

  /**
   * Adds the given item to the store in-place.
   *
   * @param item - The item to be added to the store.
   * @param keyFn - A function that generates a key for the item.
   */
  private addInPlace = (item: TBase & Partial<IContextInfo>, keyFn: (t: TBase) => string) => {
    const key = keyFn(item);

    // Get the defaults for reference count and status
    const newRefs: number = item.referenceCount ?? 1;
    const oldItem = this._cache.get(key);
    // Priority of status goes:
    // - Explicitly set status in item
    // - Existing status in store
    // - Default status
    const newStatus: ContextStatus = item.status ?? oldItem?.status ?? ContextStatus.active;
    if (oldItem) {
      oldItem.referenceCount += newRefs;
      oldItem.status = newStatus;
      // preserve pinned status
      oldItem.pinned = item.pinned ?? oldItem.pinned;
      // preserve showWarning status
      oldItem.showWarning = item.showWarning ?? oldItem.showWarning;
    } else {
      this._cache.set(key, {
        ...item,
        pinned: undefined,
        referenceCount: newRefs,
        status: newStatus,
      });
    }
  };

  /**
   * Removes the given item from the store in-place.
   *
   * @param item - The item to be removed from the store.
   * @param keyFn - A function that generates a key for the item.
   */
  private removeInPlace = (item: TBase, keyFn: (t: TBase) => string) => {
    const key = keyFn(item);
    const oldItem = this._cache.get(key);
    if (!oldItem) {
      return;
    }
    oldItem.referenceCount -= 1;
    if (oldItem.referenceCount === 0) {
      this._cache.delete(key);
    }
  };

  /**
   * Sets the status of the item with the given key to the given status.
   *
   * @param key - The key of the item to be updated.
   * @param status - The new status to be set.
   */
  setStatus = (key: string, status: ContextStatus) => {
    const oldItem = this._cache.get(key);
    if (!oldItem) {
      return;
    }
    oldItem.status = status;
  };

  /**
   * Toggles the pinned status of the item with the given key. If an item is unpinned
   * and has no other references, it will be removed from the store.
   *
   * @param key - The key of the item to be toggled.
   */
  togglePinned = (key: string) => {
    const oldItem = this._cache.peek(key);
    if (!oldItem) {
      return;
    }
    if (oldItem.pinned) {
      this.unpin(key);
    } else {
      this.pin(key);
    }
  };

  /**
   * Pins the item with the given key.
   *
   * @param key - The key of the item to be pinned.
   */
  pin = (key: string) => {
    const oldItem = this._cache.peek(key);
    if (!oldItem || oldItem.pinned) {
      return;
    }
    oldItem.pinned = true;
    oldItem.referenceCount += 1;
  };

  /**
   * Unpins the item with the given key. If an item is unpinned
   * and has no other references, it will be removed from the store.
   *
   * @param key - The key of the item to be unpinned.
   */
  unpin = (key: string) => {
    const oldItem = this._cache.peek(key);
    if (!oldItem || !oldItem.pinned) {
      return;
    }
    oldItem.pinned = false;
    oldItem.referenceCount -= 1;

    // If unpinning and reference count is 0, remove the item
    if (oldItem.referenceCount === 0) {
      this._cache.delete(key);
    }
  };

  /**
   * Toggles the status of the item with the given key.
   *
   * @param key - The key of the item to be toggled.
   */
  toggleStatus = (key: string) => {
    const oldItem = this._cache.get(key);
    if (!oldItem) {
      return;
    }
    oldItem.status =
      oldItem.status === ContextStatus.active ? ContextStatus.inactive : ContextStatus.active;
  };
}

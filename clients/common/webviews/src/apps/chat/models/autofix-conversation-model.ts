import {
  type AutofixIteration,
  AutofixIterationStage,
  type IConversationAutofixExtraData,
} from "$vscode/src/autofix/autofix-state";
import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
import {
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { type IExtensionClient } from "../extension-client";
import {
  type AutofixMessage,
  ChatItemType,
  ExchangeStatus,
  type ChatItem,
} from "../types/chat-message";
import { type ConversationModel } from "./conversation-model";
import { isAutofixConversation } from "./types";

export const DEFAULT_CONVERSATION_NAME = "New Chat";

export class AutofixConversationModel {
  private subscribers: Set<(chatHistory: AutofixConversationModel) => void> = new Set();

  constructor(
    private _conversationModel: ConversationModel,
    private _extensionClient: IExtensionClient,
  ) {
    this._conversationModel.subscribe((_) => this.notifySubscribers());
  }

  public get conversationId(): string {
    return this._conversationModel.id;
  }
  public isAutofixConversation = (): boolean => {
    return isAutofixConversation(this._conversationModel);
  };

  subscribe = (sub: (autofixConversationModel: AutofixConversationModel) => void): (() => void) => {
    this.subscribers.add(sub);
    sub(this);
    return () => {
      this.subscribers.delete(sub);
    };
  };

  private notifySubscribers = () => {
    this.subscribers.forEach((sub) => sub(this));
  };
  // update can take a partial conversation and merge it with the current state
  private update = (extraData: Partial<IConversationAutofixExtraData>) => {
    this._conversationModel.extraData = { ...this.extraData, ...extraData };
    this.notifySubscribers();
    this.sendAutofixStateUpdate();
  };

  public async sendAutofixStateUpdate() {
    await this._extensionClient.sendAutofixStateUpdate(
      this._conversationModel.extraData as IConversationAutofixExtraData,
    );
  }

  public setCommand(command: string) {
    this.update({ autofixCommand: command });
  }

  get extraData(): IConversationAutofixExtraData {
    return this._conversationModel.extraData as IConversationAutofixExtraData;
  }

  public shouldShowAutofixCommandInput = (): boolean => {
    return !this.extraData.autofixIterations?.length;
  };

  public addChatItem(item: ChatItem) {
    this._conversationModel.addChatItem(item);
  }

  getAutofixIteration(iterationId: string): AutofixIteration | undefined {
    return this.extraData.autofixIterations?.find(
      (iteration: AutofixIteration) => iteration.id === iterationId,
    );
  }

  private insertNewAutofixIteration(iteration: AutofixIteration) {
    this.update({
      autofixIterations: [...(this.extraData.autofixIterations || []), iteration],
    });
  }

  updateAutofixIteration(iteration: Partial<AutofixIteration> & { id: string }) {
    const autofixState = this.extraData;

    const existingIterations = autofixState.autofixIterations || [];
    const index = existingIterations.findIndex((it: AutofixIteration) => it.id === iteration.id);

    if (index === -1) {
      throw new Error(`No autofix iteration found with id ${iteration.id}`);
    }

    this.update({
      autofixIterations: existingIterations.map((it: AutofixIteration, i: number) =>
        i === index ? { ...it, ...iteration } : it,
      ),
    });
  }

  async handleFixWasApplied(selectedSolutions: IEditSuggestion[]) {
    const latestIterationId = this.extraData.autofixIterations?.at(-1)?.id;
    if (!latestIterationId) {
      throw new Error("No latest iteration found");
    }

    this.updateAutofixIteration({
      id: latestIterationId,
      selectedSolutions,
    });

    await this.startNewAutofixIteration();
  }

  async handleUserSteering(userInput: string): Promise<void> {
    const latestIteration = this.extraData.autofixIterations?.at(-1);
    if (!latestIteration) {
      throw new Error("No latest iteration found");
    }

    const latestSuggestedSolution = latestIteration.suggestedSolutions?.at(-1);
    if (!latestSuggestedSolution) {
      throw new Error("No latest suggested solution found");
    }

    this.addChatItem({
      chatItemType: ChatItemType.autofixSteeringMessage,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_id: crypto.randomUUID(),
      status: ExchangeStatus.sent,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      response_text: userInput,
    });

    this.update({
      autofixSteeringHistory: [
        ...(this.extraData.autofixSteeringHistory || []),
        {
          requestId: crypto.randomUUID(),
          requestMessage: userInput,
          summary: latestSuggestedSolution.summary,
          replacements: latestSuggestedSolution.originalReplacements,
        },
      ],
    });

    await this.planSolution(latestIteration.id);
  }

  async startNewAutofixIteration(): Promise<string> {
    const command = this.extraData.autofixCommand;

    if (!command) {
      throw new Error("No command set");
    }

    const autofixState = this.extraData;
    const iterationId = crypto.randomUUID();

    const isFirstIteration = !autofixState.autofixIterations?.length;

    this.insertNewAutofixIteration({
      id: iterationId,
      currentStage: AutofixIterationStage.runTest,
      commandOutput: "",
      isFirstIteration,
    });

    this.addChatItem({
      chatItemType: ChatItemType.autofixStage,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_id: crypto.randomUUID(),
      iterationId,
      status: ExchangeStatus.sent,
      stage: AutofixIterationStage.runTest,
    });

    await this.launchAutofixPanel(iterationId, AutofixIterationStage.runTest);

    try {
      // TODO: Implement with shlex
      const cmdParts = command.split(" ");
      const cmd = cmdParts[0];
      const args = cmdParts.slice(1);

      const commandOutput = await this._extensionClient.executeCommand(iterationId, cmd, args);
      const commandFailed = commandOutput.returnCode !== 0;

      // Update iteration with command results
      this.updateAutofixIteration({
        id: iterationId,
        commandOutput: commandOutput.output,
        commandFailed,
      });

      if (!commandFailed) {
        return iterationId;
      }
    } catch (error) {
      // TODO: Should this be handled differently?
      console.error(`[ConversationModel] Error executing command:`, error);
      this.updateAutofixIteration({
        id: iterationId,
        commandOutput: String(error),
        commandFailed: true,
      });

      return iterationId;
    }

    await this.planSolution(iterationId);
    return iterationId;
  }

  async planSolution(iterationId: string) {
    const command = this.extraData.autofixCommand;
    if (!command) {
      throw new Error("No command set");
    }

    const existingIteration = this.getAutofixIteration(iterationId);
    if (!existingIteration) {
      throw new Error(`No iteration found with id ${iterationId}`);
    }

    this.updateAutofixIteration({
      id: iterationId,
      currentStage: AutofixIterationStage.applyFix,
    });

    this.addChatItem({
      chatItemType: ChatItemType.autofixStage,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_id: crypto.randomUUID(),
      iterationId,
      status: ExchangeStatus.sent,
      stage: AutofixIterationStage.applyFix,
      stageCount: existingIteration.suggestedSolutions?.length || 0,
    });

    const plan = await this._extensionClient.autofixPlan(
      {
        input: command,
        output: this.getAutofixIteration(iterationId)?.commandOutput || "",
      },
      this.extraData.autofixSteeringHistory,
    );

    this.addChatItem({
      chatItemType: ChatItemType.autofixMessage,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_id: crypto.randomUUID(),
      status: ExchangeStatus.sent,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      response_text: plan.summary,
    });

    this.updateAutofixIteration({
      id: iterationId,
      suggestedSolutions: [...(existingIteration.suggestedSolutions || []), plan],
    });

    await this.launchAutofixPanel(iterationId, AutofixIterationStage.applyFix);
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.chatAutofixStateUpdateRequest:
        this.sendAutofixStateUpdate();
        return true;
      case WebViewMessageType.chatAutofixSuggestionsApplied:
        this.handleFixWasApplied(msg.data.selectedSolutions);
        return true;
      default:
        return false;
    }
  }

  public initializeAutofixConversation() {
    this.update({
      isAutofix: true,
      autofixIterations: [],
    });

    const introMessage: AutofixMessage = {
      chatItemType: ChatItemType.autofixMessage,
      /* eslint-disable @typescript-eslint/naming-convention */
      request_id: crypto.randomUUID(),
      status: ExchangeStatus.success,
      response_text:
        "Autofix enables you to run your tests and suggests fixes for you. Start by typing a command to run your test such as `pytest path/to/test`",
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    this._conversationModel.chatHistory.push(introMessage);
  }

  launchAutofixPanel = async (iterationId: string, stage: AutofixIterationStage) => {
    await this._extensionClient.launchAutofixPanel(this.conversationId, iterationId, stage);
  };
}

import {
  type ChatResultN<PERSON>,
  ChatResultNodeType,
  type ChatResultToolUse,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  AgentSessionEventName,
  ClassifyAndDistillData,
  ClassifyAndDistillDebugFlag,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import { get } from "svelte/store";
import { marked } from "marked";
import {
  AgentExchangeStatus,
  type ChatItem,
  ExchangeStatus,
  type ExchangeWithStatus,
  isChatItemExchangeWithStatus,
} from "../types/chat-message";
import { ToolUsePhase } from "../types/tool-use-state";
import type { ConversationModel } from "./conversation-model";
import { isAgentConversation } from "./types";

/**
 * Checks if a chat item represents a user message
 */
export const isUserMessage = (chatItem: ChatItem): boolean => {
  return chatItem.chatItemType === undefined;
};

/**
 * Sends a silent exchange to classify and distill memories without context
 */
export const classifyAndDistillMemories = async (
  conversationModel: ConversationModel,
  exchange: ExchangeWithStatus,
): Promise<void> => {
  if (!isAgentConversation(conversationModel)) return;
  // Don't classify and distill memories for non-user messages
  if (!isUserMessage(exchange)) return;
  // No pending user message
  if (!exchange?.request_message) return;

  const trace = ClassifyAndDistillData.create();
  trace.setFlag(ClassifyAndDistillDebugFlag.start);

  try {
    await _classifyAndDistillMemories(conversationModel, exchange, trace);
  } catch (e) {
    trace.setFlag(ClassifyAndDistillDebugFlag.exceptionThrown);
    console.error("Failed to classify and distill memories", e);
  } finally {
    trace.setFlag(ClassifyAndDistillDebugFlag.end);
    conversationModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.classifyAndDistill,
      conversationId: conversationModel.id,
      eventData: {
        classifyAndDistillData: trace,
      },
    });
  }
};

const _classifyAndDistillMemories = async (
  conversationModel: ConversationModel,
  exchange: ExchangeWithStatus,
  trace: ClassifyAndDistillData,
): Promise<void> => {
  // Used to link the chain of classifyAndDistill => flush memories => remember-tool-call
  const memoriesRequestId = crypto.randomUUID();
  trace.setRequestId(ClassifyAndDistillDebugFlag.memoriesRequestId, memoriesRequestId);

  // Store this so we can check if conversation has changed
  const currConversationId = get(conversationModel).id;

  trace.setFlag(ClassifyAndDistillDebugFlag.startSendSilentExchange);
  const { responseText: rawResponse, requestId } = await conversationModel.sendSilentExchange({
    /* eslint-disable @typescript-eslint/naming-convention */
    // Message is wrapped into a special prompt on extension side
    model_id: conversationModel.selectedModelId ?? undefined,
    request_message: exchange.request_message,
    disableRetrieval: true,
    disableSelectedCodeDetails: true,
    memoriesInfo: {
      isClassifyAndDistill: true,
    },
    /* eslint-enable @typescript-eslint/naming-convention */
  });
  trace.setStringStats(ClassifyAndDistillDebugFlag.sendSilentExchangeResponseStats, rawResponse);
  if (requestId) {
    trace.setRequestId(ClassifyAndDistillDebugFlag.sendSilentExchangeRequestId, requestId);
  } else {
    trace.setFlag(ClassifyAndDistillDebugFlag.noRequestId);
  }

  // If conversation has changed, do nothing
  if (get(conversationModel).id !== currConversationId) {
    trace.setFlag(ClassifyAndDistillDebugFlag.conversationChanged);
    return;
  }

  let json: { explanation: string; worthRemembering: boolean; content: string };
  try {
    let parsableResponse = rawResponse;
    try {
      const tokens = marked.lexer(rawResponse);
      if (tokens.length === 1 && tokens[0].type === "code" && tokens[0].text) {
        parsableResponse = tokens[0].text;
      }
      // If it's not a single code block, parsableResponse remains rawResponse
    } catch (lexError) {
      // If lexing fails, log it but proceed with rawResponse,
      // as it might be plain JSON that confused the lexer.
      console.warn(
        "Markdown lexing failed during response parsing, attempting to parse as raw string:",
        lexError,
      );
    }
    json = JSON.parse(parsableResponse);
  } catch (e) {
    trace.setFlag(ClassifyAndDistillDebugFlag.invalidResponse);
    throw new Error("Invalid response from classify and distill");
  }

  if (
    typeof json.explanation !== "string" ||
    typeof json.content !== "string" ||
    typeof json.worthRemembering !== "boolean"
  ) {
    trace.setFlag(ClassifyAndDistillDebugFlag.invalidResponse);
    throw new Error("Invalid response from classify and distill");
  }
  trace.setStringStats(ClassifyAndDistillDebugFlag.explanationStats, json.explanation);
  trace.setStringStats(ClassifyAndDistillDebugFlag.contentStats, json.content);
  trace.setFlag(ClassifyAndDistillDebugFlag.worthRemembering, json.worthRemembering);

  const response = json.worthRemembering ? json.content : undefined;
  if (response) {
    updateUserExchangeWithMemory(conversationModel, response, memoriesRequestId, trace);
  }
};

/**
 * Computes the current status of the agent turn.
 * Used to determine the state of the current agent interaction.
 *
 * @returns The current AgentExchangeStatus
 */
export const computeAgentExchangeStatus = (
  conversationModel: ConversationModel,
): AgentExchangeStatus => {
  const lastExchange = conversationModel.chatHistory.at(-1);

  // If no last exchange or not a proper exchange with status, agent is not running
  if (!lastExchange || !isChatItemExchangeWithStatus(lastExchange)) {
    return AgentExchangeStatus.notRunning;
  }

  // Check if exchange itself is complete (success or failed)
  const isExchangeComplete =
    lastExchange.status === ExchangeStatus.success ||
    lastExchange.status === ExchangeStatus.failed ||
    lastExchange.status === ExchangeStatus.cancelled;
  if (!isExchangeComplete) {
    return AgentExchangeStatus.running;
  }

  // Beyond this point, normal exchanges should already be complete. So everything below should be
  // checking tool uses

  // Check the last exchange for its status and tool use state
  // General logic is as follows:
  // - If there is no tool use at all, this is likely a bug. We'll just default to reporting not running,
  //     since no tool uses in a returned response block should mean the agent is not running.
  // - If there is a tool use, we check the tool use state to determine the agent status.
  // - If the tool use is runnable, the agent is awaiting user action.
  // - If the tool use is aborted, the agent is not running. This happens when cancellations occur but the
  //     agent is not expected to continue the conversation.
  // - If the tool use is cancelled, the agent is still running -- it is expected to continue the conversation.
  // - If the tool use is anything else, the agent is still running.

  const toolUseNodes =
    lastExchange.structured_output_nodes?.filter(
      // eslint-disable-next-line @typescript-eslint/naming-convention
      (node): node is ChatResultNode & { tool_use: ChatResultToolUse } =>
        node.type === ChatResultNodeType.TOOL_USE && !!node.tool_use,
    ) ?? [];

  const lastToolUseNode = toolUseNodes.at(-1);
  if (!lastToolUseNode) {
    // If there is no tool use node, normal exchange status should have already returned.
    // If we somehow enter this block, we are not running -- likely, we cancelled or errored somewhere.
    return AgentExchangeStatus.notRunning;
  }

  // Check the status based on the last tool use node in our last exchange
  const toolState = conversationModel.getToolUseState(
    lastExchange.request_id,
    lastToolUseNode.tool_use.tool_use_id,
  );

  switch (toolState.phase) {
    case ToolUsePhase.runnable:
      return AgentExchangeStatus.awaitingUserAction;
    case ToolUsePhase.cancelled:
      return AgentExchangeStatus.notRunning;
    default:
      return AgentExchangeStatus.running;
  }
};

/**
 * Checks if a chat item is a user exchange (has a request message)
 *
 * @param chatItem The chat item to check
 * @returns True if the chat item is a user exchange
 */
export const isUserExchange = (chatItem: ChatItem) => {
  return isChatItemExchangeWithStatus(chatItem) && !!chatItem.request_message;
};

/**
 * Filters a chat history to only include user exchanges.
 *
 * In an agent conversation, this would exclude tool calls so it would not be a
 * complete representation of the chat history. But this can be useful to count
 * how many times the user has sent a message.
 *
 * @param chatHistory The chat history to filter
 * @returns Array of exchanges that are user exchanges
 */
export const filterByUserExchange = (chatHistory: ChatItem[]) => {
  return chatHistory.filter((exchange): exchange is ExchangeWithStatus => isUserExchange(exchange));
};

/**
 * Gets the last exchange with a message (i.e. a user message)
 * This is used to append the computed memory to the last user message, as opposed to automated messages
 * sent as tool responses, which we send with user messages in order to get tool responses
 * over to the agent.
 *
 * @param conversationModel
 * @returns
 */
export const getLastUserExchange = (
  conversationModel: ConversationModel,
): ExchangeWithStatus | undefined => {
  return conversationModel.chatHistory.findLast((exchange): exchange is ExchangeWithStatus =>
    isUserExchange(exchange),
  );
};

/**
 * Gets the last user exchange's memory, if it exists
 *
 * @param conversationModel
 * @returns
 */
export const getLastUserExchangeMemory = (
  conversationModel: ConversationModel,
): { memoriesRequestId: string; memory: string } | undefined => {
  const lastUserExchange = getLastUserExchange(conversationModel);
  if (lastUserExchange?.structured_output_nodes) {
    const agentMemoryNode = lastUserExchange.structured_output_nodes.find(
      (node) => node.type === ChatResultNodeType.AGENT_MEMORY,
    );
    if (agentMemoryNode) {
      try {
        const { memoriesRequestId, memory } = JSON.parse(agentMemoryNode.content);
        return { memoriesRequestId, memory };
      } catch (e) {
        console.error("Failed to parse JSON from agent memory node", e);
        return undefined;
      }
    }
  }
};

/**
 * Checks if there was a remember tool call in the current agentive turn.
 *
 * @param conversationModel - The current conversation model
 * @returns {boolean} True if a remember tool call was found, false otherwise
 */
export const currentAgenticTurnHasRemember = (conversationModel: ConversationModel): boolean => {
  const exchanges = getCurrentAgenticTurnExchanges(conversationModel, (exchange) => {
    return !!exchange.structured_output_nodes?.some(
      (node) =>
        node.type === ChatResultNodeType.TOOL_USE && node.tool_use?.tool_name === "remember",
    );
  });
  return exchanges.length > 0;
};

/**
 * Gets all exchanges since the last user message (inclusive)
 * This is used to determine if the current turn is complete
 * by checking if all tool uses are complete
 *
 * @param conversationModel
 * @returns
 */
export const getCurrentAgenticTurnExchanges = (
  conversationModel: ConversationModel,
  filter?: (exchange: ExchangeWithStatus) => boolean,
): ExchangeWithStatus[] => {
  const lastUserExchange = getLastUserExchange(conversationModel);
  if (!lastUserExchange?.request_id) return [];
  const predicate = (exchange: ChatItem): exchange is ExchangeWithStatus => {
    return isChatItemExchangeWithStatus(exchange) && (!filter || filter(exchange));
  };
  const exchanges = conversationModel
    .historyFrom(lastUserExchange.request_id, true)
    .filter(predicate);
  return exchanges;
};

/**
 * Cancels the last runnable tool use in the current agent turn, if any.
 *
 * @param conversationModel - The current conversation model
 * @returns {boolean} True if a tool use was cancelled, false otherwise
 */
export const cancelLastRunnableToolUse = (conversationModel: ConversationModel): boolean => {
  const lastExchange = conversationModel.chatHistory.at(-1);
  if (!lastExchange?.request_id || !isChatItemExchangeWithStatus(lastExchange)) {
    return false;
  }
  const toolUseNodes =
    lastExchange.structured_output_nodes?.filter(
      (node) => node.type === ChatResultNodeType.TOOL_USE,
    ) ?? [];

  for (const node of toolUseNodes) {
    if (!node.tool_use) continue;
    const toolState = conversationModel.getToolUseState(
      lastExchange.request_id,
      node.tool_use.tool_use_id,
    );
    if (toolState.phase === ToolUsePhase.runnable) {
      conversationModel.updateToolUseState({
        requestId: lastExchange.request_id,
        toolUseId: node.tool_use.tool_use_id,
        phase: ToolUsePhase.cancelled,
      });
      return true;
    }
  }
  return false;
};

/**
 * Updates the last user exchange with the given memory string
 *
 * @param conversationModel
 * @param memoryString
 */
export const updateUserExchangeWithMemory = (
  conversationModel: ConversationModel,
  memoryString: string,
  memoriesRequestId: string,
  trace: ClassifyAndDistillData,
): void => {
  const content = JSON.stringify({
    memoriesRequestId,
    memory: memoryString,
  });

  const lastUserExchange = getLastUserExchange(conversationModel);
  if (lastUserExchange?.request_id) {
    trace.setRequestId(
      ClassifyAndDistillDebugFlag.lastUserExchangeRequestId,
      lastUserExchange.request_id,
    );
    conversationModel.updateChatItem(lastUserExchange.request_id, {
      ...lastUserExchange,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      structured_output_nodes: [
        ...(lastUserExchange.structured_output_nodes ?? []),
        {
          id: 0,
          type: ChatResultNodeType.AGENT_MEMORY,
          content,
        },
      ],
    });
  } else {
    trace.setFlag(ClassifyAndDistillDebugFlag.noLastUserExchangeRequestId);
  }
};

/**
 * Removes memory from a specific user exchange.
 *
 * @param conversationModel The conversation model to update
 * @param requestId The request ID of the exchange to update
 * @returns true if memory was removed, false otherwise
 */
export const removeUserExchangeMemory = (
  conversationModel: ConversationModel,
  requestId: string,
): boolean => {
  const lastUserExchange = getLastUserExchange(conversationModel);
  if (!lastUserExchange?.request_id || lastUserExchange.request_id !== requestId) {
    return false;
  }

  // Filter out any AGENT_MEMORY nodes
  const updatedNodes = (lastUserExchange.structured_output_nodes || []).filter(
    (node) => node.type !== ChatResultNodeType.AGENT_MEMORY,
  );

  // Update only if we actually removed something
  if (updatedNodes.length !== (lastUserExchange.structured_output_nodes || []).length) {
    conversationModel.updateChatItem(requestId, {
      ...lastUserExchange,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      structured_output_nodes: updatedNodes,
    });
    return true;
  }

  return false;
};

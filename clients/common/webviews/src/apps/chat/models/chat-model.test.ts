import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { HostClientType, type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import {
  ChatRequestNodeType,
  PersonaType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { afterEach, beforeEach, describe, expect, test, vi, type MockInstance } from "vitest";
import { ExchangeStatus } from "../types/chat-message";
import { ChatModel } from "./chat-model";
import { SpecialContextInputModel } from "./context-model";
import { ConversationModel } from "./conversation-model";
import type { IConversation } from "./types";
import { type NewThread, type NewThreadData } from "$vscode/src/webview-providers/webview-messages";
import { type ChatModeModel } from "./chat-mode-model";
import { AgentExecutionMode } from "./chat-model";

describe("ChatModel", () => {
  let host: HostInterface;
  let contextModel: SpecialContextInputModel;

  function mockUUID(id: number | string): `${string}-${string}-${string}-${string}-${string}` {
    return `mocked-uuid-a-b-${id}`;
  }

  beforeEach(() => {
    host = {
      clientType: HostClientType.vscode,
      postMessage: vi.fn(),
      getState: vi.fn(),
      setState: vi.fn(),
    };
    contextModel = new SpecialContextInputModel();

    let counter = 0;
    vi.spyOn(crypto, "randomUUID").mockImplementation(() => {
      return mockUUID(counter++);
    });
  });

  test("should initialize", async () => {
    // Mock the decidePersonaType method to return a synchronous result
    vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
      PersonaType.DEFAULT,
    );

    const chatModel = new ChatModel(
      new AsyncMsgSender((message) => host.postMessage(message)),
      host,
      contextModel,
      {
        initialFlags: {
          doUseNewDraftFunctionality: true,
          enableBackgroundAgents: true,
        },
      },
    );

    // Wait for any pending promises to resolve
    await new Promise(process.nextTick);

    expect(chatModel).toBeDefined();
    expect(chatModel.currentConversationId).not.toBeUndefined();
    expect(chatModel.flags).toBeDefined();
    //  Ensure the model passed in is the one used by the chat model
    expect(chatModel.specialContextInputModel).toBe(contextModel);
  });

  describe("initial conversation", () => {
    beforeEach(() => {
      // Mock the decidePersonaType method for all tests in this describe block
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );
    });

    test("should initialize with initial conversation", async () => {
      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      // Verify we have a new conversation that's not equal to the very first "empty"
      // conversation.
      expect(chatModel.currentConversationId).not.toEqual(mockUUID(0));
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [chatModel.currentConversationId!]: {
          id: chatModel.currentConversationId,
          name: undefined,
          createdAtIso: expect.any(String),
          lastInteractedAtIso: expect.any(String),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          selectedModelId: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          toolUseStates: {},
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      });
      expect(ConversationModel.prototype.decidePersonaType).toHaveBeenCalled();
    });

    test("should load previous conversation", async () => {
      const prevConvo = {
        id: mockUUID("prev"),
        name: "Example",
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [{}],
        feedbackStates: {},
        toolUseStates: {},
        draftExchange: undefined,
        requestIds: [],
        isShareable: false,
        extraData: {},
        personaType: PersonaType.DEFAULT,
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: prevConvo.id,
        conversations: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [prevConvo.id]: prevConvo,
        },
      });
      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      expect(chatModel.currentConversationId).toEqual(prevConvo.id);
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [prevConvo.id]: prevConvo,
      });
      expect(ConversationModel.prototype.decidePersonaType).not.toHaveBeenCalled();
    });

    test("should ignore invalid previous conversation", async () => {
      const invalidConvo = {
        id: mockUUID("prev"),
        name: undefined,
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        selectedModelId: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        isShareable: false,
        extraData: {},
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: invalidConvo.id,
        conversations: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [invalidConvo.id]: invalidConvo,
        },
      });
      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      expect(chatModel.currentConversationId).not.toEqual(invalidConvo.id);
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [chatModel.currentConversationId!]: {
          id: chatModel.currentConversationId,
          name: undefined,
          createdAtIso: expect.any(String),
          lastInteractedAtIso: expect.any(String),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          toolUseStates: {},
          personaType: PersonaType.DEFAULT,
          extraData: {},
        },
      });
      expect(ConversationModel.prototype.decidePersonaType).toHaveBeenCalled();
    });

    test("should use initial conversation when provided", async () => {
      const prevConvo = {
        id: mockUUID("prev"),
        name: "Example",
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [{}],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        isShareable: false,
        toolUseStates: {},
        extraData: {},
        personaType: PersonaType.DEFAULT,
      };
      const initialConvo: IConversation = {
        id: mockUUID("initial"),
        name: "Initial",
        createdAtIso: expect.any(String),
        lastInteractedAtIso: expect.any(String),
        chatHistory: [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Exmaple",
            response_text: "Example",
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        isPinned: false,
        lastUrl: undefined,
        // There is a shareable message here.
        isShareable: true,
        extraData: {},
        personaType: PersonaType.DEFAULT,
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: prevConvo.id,
        conversations: {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          [prevConvo.id]: prevConvo,
        },
      });
      const chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
        {
          initialConversation: initialConvo,
        },
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      expect(chatModel.currentConversationId).toEqual(initialConvo.id);
      expect(chatModel.conversations).toEqual({
        // eslint-disable-next-line @typescript-eslint/naming-convention
        [prevConvo.id]: prevConvo,
        [initialConvo.id]: initialConvo,
      });
      expect(ConversationModel.prototype.decidePersonaType).not.toHaveBeenCalled();
    });
  });

  describe("conversation navigation", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;

    beforeEach(async () => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: vi.fn(),
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();

      // Mock the decidePersonaType method
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );

      // Mock conversations with different timestamps
      const now = new Date();
      const conversations = {
        [mockUUID(0)]: {
          id: mockUUID(0),
          name: "First",
          createdAtIso: new Date(now.getTime() - 3000).toISOString(), // Created 3 seconds ago
          lastInteractedAtIso: new Date(now.getTime() - 1000).toISOString(), // Interacted 1 second ago
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(1)]: {
          id: mockUUID(1),
          name: "Second",
          createdAtIso: new Date(now.getTime() - 2000).toISOString(), // Created 2 seconds ago
          lastInteractedAtIso: new Date(now.getTime() - 3000).toISOString(), // Interacted 3 seconds ago
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(2)]: {
          id: mockUUID(2),
          name: "Third",
          createdAtIso: new Date(now.getTime() - 1000).toISOString(), // Created 1 second ago
          lastInteractedAtIso: new Date(now.getTime() - 2000).toISOString(), // Interacted 2 seconds ago
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(1),
        conversations,
      });

      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);
    });

    describe("conversation ordering", () => {
      test("orderedConversations returns conversations ordered by creation time", () => {
        const ordered = chatModel.orderedConversations("createdAt", "desc");
        expect(ordered.map((c) => c.id)).toEqual([mockUUID(2), mockUUID(1), mockUUID(0)]);
      });

      test("orderedConversations with lastMessageTimestamp returns conversations ordered by last message time", () => {
        // Current conversation ID is 1, since we set it in the beforeEach
        expect(chatModel.currentConversationId).toBe(mockUUID(1));

        // Add message timestamps to the conversations
        const now = new Date();
        const conversations = chatModel.conversations;

        // Add a message with timestamp to conversation 0 (3 seconds ago)
        const convo0 = { ...conversations[mockUUID(0)] };
        convo0.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-0",
            timestamp: new Date(now.getTime() - 3000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 1 (1 second ago)
        const convo1 = { ...conversations[mockUUID(1)] };
        convo1.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-1",
            timestamp: new Date(now.getTime() - 1000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 2 (2 seconds ago)
        const convo2 = { ...conversations[mockUUID(2)] };
        convo2.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-2",
            timestamp: new Date(now.getTime() - 2000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Update the conversations in the chat model
        (chatModel as any)._state.conversations = {
          [mockUUID(0)]: convo0,
          [mockUUID(1)]: convo1,
          [mockUUID(2)]: convo2,
        };

        // Order should be: 1 (1 second ago), 2 (2 seconds ago), 0 (3 seconds ago)
        const ordered = chatModel.orderedConversations("lastMessageTimestamp", "desc");
        expect(ordered.map((c: IConversation) => c.id)).toEqual([
          mockUUID(1),
          mockUUID(2),
          mockUUID(0),
        ]);
      });

      test("orderedConversations allows custom sorting parameters", () => {
        // Test sorting by createdAt in ascending order (oldest first)
        const orderedByCreatedAsc = chatModel.orderedConversations("createdAt", "asc");
        expect(orderedByCreatedAsc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(0),
          mockUUID(1),
          mockUUID(2),
        ]);

        // Test sorting by createdAt in descending order (newest first)
        const orderedByCreatedDesc = chatModel.orderedConversations("createdAt", "desc");
        expect(orderedByCreatedDesc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(2),
          mockUUID(1),
          mockUUID(0),
        ]);

        // Test with filter function
        const filterFn = (conversation: IConversation) => conversation.id !== mockUUID(1);
        const filteredConversations = chatModel.orderedConversations("createdAt", "asc", filterFn);
        expect(filteredConversations.map((c: IConversation) => c.id)).toEqual([
          mockUUID(0),
          mockUUID(2),
        ]);
      });

      test("orderedConversations sorts by lastMessageTimestamp", () => {
        // Add message timestamps to the conversations
        const now = new Date();
        const conversations = chatModel.conversations;

        // Add a message with timestamp to conversation 0 (3 seconds ago)
        const convo0 = { ...conversations[mockUUID(0)] };
        convo0.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-0",
            timestamp: new Date(now.getTime() - 3000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 1 (1 second ago)
        const convo1 = { ...conversations[mockUUID(1)] };
        convo1.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-1",
            timestamp: new Date(now.getTime() - 1000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 2 (2 seconds ago)
        const convo2 = { ...conversations[mockUUID(2)] };
        convo2.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-2",
            timestamp: new Date(now.getTime() - 2000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Update the conversations in the chat model
        (chatModel as any)._state.conversations = {
          [mockUUID(0)]: convo0,
          [mockUUID(1)]: convo1,
          [mockUUID(2)]: convo2,
        };

        // Test sorting by lastMessageTimestamp in descending order (most recent message first)
        const orderedByMessageDesc = chatModel.orderedConversations("lastMessageTimestamp", "desc");
        expect(orderedByMessageDesc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(1), // 1 second ago
          mockUUID(2), // 2 seconds ago
          mockUUID(0), // 3 seconds ago
        ]);

        // Test sorting by lastMessageTimestamp in ascending order (oldest message first)
        const orderedByMessageAsc = chatModel.orderedConversations("lastMessageTimestamp", "asc");
        expect(orderedByMessageAsc.map((c: IConversation) => c.id)).toEqual([
          mockUUID(0), // 3 seconds ago
          mockUUID(2), // 2 seconds ago
          mockUUID(1), // 1 second ago
        ]);
      });

      test("next and previous conversation use lastMessageTimestamp order", () => {
        // By default, orderedConversations sorts by lastMessageTimestamp in descending order
        // In the test setup, the current conversation is mockUUID(1)
        // We need to add message timestamps to test this properly

        // Add message timestamps to the conversations
        const now = new Date();
        const conversations = chatModel.conversations;

        // Add a message with timestamp to conversation 0 (3 seconds ago)
        const convo0 = { ...conversations[mockUUID(0)] };
        convo0.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-0",
            timestamp: new Date(now.getTime() - 3000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 1 (1 second ago)
        const convo1 = { ...conversations[mockUUID(1)] };
        convo1.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-1",
            timestamp: new Date(now.getTime() - 1000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Add a message with timestamp to conversation 2 (2 seconds ago)
        const convo2 = { ...conversations[mockUUID(2)] };
        convo2.chatHistory = [
          {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_message: "Hello",
            response_text: "Hi",
            request_id: "msg-2",
            timestamp: new Date(now.getTime() - 2000).toISOString(),
            /* eslint-enable @typescript-eslint/naming-convention */
            status: ExchangeStatus.success,
          },
        ];

        // Update the conversations in the chat model
        (chatModel as any)._state.conversations = {
          [mockUUID(0)]: convo0,
          [mockUUID(1)]: convo1,
          [mockUUID(2)]: convo2,
        };

        // Now the next conversation should be mockUUID(2) (2 seconds ago)
        // since we're sorting by lastMessageTimestamp and the current conversation is mockUUID(1) (1 second ago)
        expect(chatModel.nextConversation?.id).toBe(mockUUID(2));
        expect(chatModel.previousConversation?.id).toBe(undefined);
      });
    });

    describe("no conversations", () => {
      test("returns undefined for both when there are no conversations", () => {
        (host.getState as any as MockInstance).mockReturnValue({
          currentConversationId: undefined,
          conversations: [],
        });
        chatModel = new ChatModel(
          new AsyncMsgSender((message) => host.postMessage(message)),
          host,
          contextModel,
        );
        expect(chatModel.nextConversation).toBeUndefined();
        expect(chatModel.previousConversation).toBeUndefined();
      });
    });
  });

  describe("lastMessageTimestamp", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;

    beforeEach(async () => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: vi.fn(),
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();

      // Mock the decidePersonaType method
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );

      // Create a mock timestamp for testing
      const mockTimestamp = "2023-05-15T10:30:00.000Z";

      // Create a conversation with a message that has a timestamp
      const conversations = {
        [mockUUID(0)]: {
          id: mockUUID(0),
          name: "Conversation with timestamp",
          createdAtIso: new Date().toISOString(),
          lastInteractedAtIso: new Date().toISOString(),
          chatHistory: [
            {
              /* eslint-disable @typescript-eslint/naming-convention */
              request_message: "Hello",
              response_text: "Hi there",
              request_id: "test-request-id",
              timestamp: mockTimestamp,
              /* eslint-enable @typescript-eslint/naming-convention */
              status: ExchangeStatus.success,
            },
          ],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: true,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(0),
        conversations,
      });

      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);
    });

    test("returns the timestamp of the last message", () => {
      expect(chatModel.lastMessageTimestamp).toBe("2023-05-15T10:30:00.000Z");
    });

    test("returns undefined when there are no messages", async () => {
      // Create a new chat model with an empty conversation
      const emptyConversations = {
        [mockUUID(1)]: {
          id: mockUUID(1),
          name: "Empty conversation",
          createdAtIso: new Date().toISOString(),
          lastInteractedAtIso: new Date().toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(1),
        conversations: emptyConversations,
      });

      const emptyChatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      expect(emptyChatModel.lastMessageTimestamp).toBeUndefined();
    });
  });

  describe("popConversation", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;
    beforeEach(() => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: vi.fn(),
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();

      // Mock the decidePersonaType method
      vi.spyOn(ConversationModel.prototype, "decidePersonaType").mockResolvedValue(
        PersonaType.DEFAULT,
      );

      // Mock conversations with different timestamps
      const conversations = {
        [mockUUID(0)]: {
          id: mockUUID(0),
          name: "First",
          createdAtIso: new Date("2023-01-01").toISOString(),
          lastInteractedAtIso: new Date("2023-01-01").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(1)]: {
          id: mockUUID(1),
          name: "Second",
          createdAtIso: new Date("2023-01-02").toISOString(),
          lastInteractedAtIso: new Date("2023-01-02").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
        [mockUUID(2)]: {
          id: mockUUID(2),
          name: "Third",
          createdAtIso: new Date("2023-01-03").toISOString(),
          lastInteractedAtIso: new Date("2023-01-03").toISOString(),
          chatHistory: [],
          feedbackStates: {},
          draftExchange: undefined,
          requestIds: [],
          isPinned: false,
          lastUrl: undefined,
          isShareable: false,
          extraData: {},
          personaType: PersonaType.DEFAULT,
        },
      };

      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(1),
        conversations,
      });
      mockConfirmationModalAccept(host);

      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );
    });

    test("removes current conversation and updates state", async () => {
      const initialConversations = chatModel.conversations;
      const currentId = chatModel.currentConversationId;

      await chatModel.popCurrentConversation();

      // Verify conversation was removed
      expect(currentId).toBe(mockUUID(1));
      expect(chatModel.conversations).not.toHaveProperty(currentId!);
      expect(Object.keys(chatModel.conversations).length).toBe(
        Object.keys(initialConversations).length - 1,
      );
      // Wait for debounce to trigger state update
      await new Promise((resolve) => setTimeout(resolve, 1500));
      expect(host.setState).toHaveBeenCalled();
    });

    test("selects next conversation when available", async () => {
      // Add message timestamps to the conversations to test with lastMessageTimestamp sorting
      const now = new Date();
      const conversations = chatModel.conversations;

      // Add a message with timestamp to conversation 0 (3 seconds ago)
      const convo0 = { ...conversations[mockUUID(0)] };
      convo0.chatHistory = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "Hello",
          response_text: "Hi",
          request_id: "msg-0",
          timestamp: new Date(now.getTime() - 3000).toISOString(),
          /* eslint-enable @typescript-eslint/naming-convention */
          status: ExchangeStatus.success,
        },
      ];

      // Add a message with timestamp to conversation 1 (1 second ago)
      const convo1 = { ...conversations[mockUUID(1)] };
      convo1.chatHistory = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "Hello",
          response_text: "Hi",
          request_id: "msg-1",
          timestamp: new Date(now.getTime() - 1000).toISOString(),
          /* eslint-enable @typescript-eslint/naming-convention */
          status: ExchangeStatus.success,
        },
      ];

      // Add a message with timestamp to conversation 2 (2 seconds ago)
      const convo2 = { ...conversations[mockUUID(2)] };
      convo2.chatHistory = [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "Hello",
          response_text: "Hi",
          request_id: "msg-2",
          timestamp: new Date(now.getTime() - 2000).toISOString(),
          /* eslint-enable @typescript-eslint/naming-convention */
          status: ExchangeStatus.success,
        },
      ];

      // Update the conversations in the chat model
      (chatModel as any)._state.conversations = {
        [mockUUID(0)]: convo0,
        [mockUUID(1)]: convo1,
        [mockUUID(2)]: convo2,
      };

      // In the orderedConversations by lastMessageTimestamp, mockUUID(2) is the next one after mockUUID(1)
      await chatModel.popCurrentConversation();

      expect(chatModel.currentConversationId).toBe(mockUUID(2));
    });

    test("selects previous conversation when no next available", async () => {
      // Set current conversation to the last one in the ordered list (mockUUID(0))
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(0),
        conversations: chatModel.conversations,
      });
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      await chatModel.popCurrentConversation();

      // Should select conversation 1 since we're deleting 0 and there's no next conversation
      expect(chatModel.currentConversationId).toBe(mockUUID(1));
    });

    test("creates new conversation when removing last one", async () => {
      // Setup single conversation state
      const singleConversation = {
        [mockUUID(0)]: chatModel.conversations[mockUUID(0)],
      };
      (host.getState as any as MockInstance).mockReturnValue({
        currentConversationId: mockUUID(0),
        conversations: singleConversation,
      });
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );

      // Wait for any pending promises to resolve
      await new Promise(process.nextTick);

      await chatModel.popCurrentConversation();

      // Wait for any pending promises to resolve after popCurrentConversation
      await new Promise(process.nextTick);

      // Should create a new conversation
      expect(Object.keys(chatModel.conversations)).toHaveLength(1);
      expect(chatModel.currentConversationId).not.toBe(mockUUID(0));
    });
  });

  describe("image handling", () => {
    let host: HostInterface;
    let contextModel: SpecialContextInputModel;
    let chatModel: ChatModel;

    beforeEach(() => {
      host = {
        clientType: HostClientType.vscode,
        postMessage: vi.fn(),
        getState: vi.fn(),
        setState: vi.fn(),
      };
      contextModel = new SpecialContextInputModel();
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );
    });

    describe("findImagesInJson", () => {
      test("finds images in simple JSON", () => {
        const json = {
          type: "doc",
          content: [
            {
              type: "image",
              attrs: { src: "image1.png" },
            },
          ],
        };
        const images = (chatModel as any).findImagesInJson(json);
        expect(images).toEqual(["image1.png"]);
      });

      test("finds images in nested JSON", () => {
        const json = {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "image",
                  attrs: { src: "image1.png" },
                },
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "image",
                      attrs: { src: "image2.png" },
                    },
                  ],
                },
              ],
            },
          ],
        };
        const images = (chatModel as any).findImagesInJson(json);
        expect(images).toEqual(["image1.png", "image2.png"]);
      });

      test("handles JSON with no images", () => {
        const json = {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [{ type: "text", text: "Hello" }],
            },
          ],
        };
        const images = (chatModel as any).findImagesInJson(json);
        expect(images).toEqual([]);
      });
    });

    describe("findImagesInStructuredRequest", () => {
      test("finds image IDs in structured request", () => {
        const nodes = [
          // eslint-disable-next-line @typescript-eslint/naming-convention
          { type: ChatRequestNodeType.IMAGE_ID, image_id_node: { image_id: "image1.png" } },
          { type: ChatRequestNodeType.TEXT, text: "some text" },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          { type: ChatRequestNodeType.IMAGE_ID, image_id_node: { image_id: "image2.png" } },
        ];
        const images = (chatModel as any).findImagesInStructuredRequest(nodes);
        expect(images).toEqual(["image1.png", "image2.png"]);
      });

      test("handles empty node list", () => {
        const nodes: any[] = [];
        const images = (chatModel as any).findImagesInStructuredRequest(nodes);
        expect(images).toEqual([]);
      });

      test("handles nodes with no images", () => {
        const nodes = [
          { type: ChatRequestNodeType.TEXT, text: "text1" },
          { type: ChatRequestNodeType.TEXT, text: "text2" },
        ];
        const images = (chatModel as any).findImagesInStructuredRequest(nodes);
        expect(images).toEqual([]);
      });
    });

    describe("deleteImagesInExchange", () => {
      test("deletes images from both rich text and structured request", async () => {
        const deleteImageSpy = vi.spyOn(chatModel, "deleteImage").mockResolvedValue(undefined);
        const exchange = {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          rich_text_json_repr: {
            type: "doc",
            content: [
              {
                type: "image",
                attrs: { src: "image1.png" },
              },
            ],
          },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          structured_request_nodes: [
            // eslint-disable-next-line @typescript-eslint/naming-convention
            { type: ChatRequestNodeType.IMAGE_ID, image_id_node: { image_id: "image2.png" } },
          ],
        };

        await (chatModel as any).deleteImagesInExchange(exchange);

        expect(deleteImageSpy).toHaveBeenCalledTimes(2);
        expect(deleteImageSpy).toHaveBeenCalledWith("image1.png");
        expect(deleteImageSpy).toHaveBeenCalledWith("image2.png");
      });

      test("handles exchange with no images", async () => {
        const deleteImageSpy = vi.spyOn(chatModel, "deleteImage").mockResolvedValue(undefined);
        const exchange = {
          // eslint-disable-next-line @typescript-eslint/naming-convention
          rich_text_json_repr: {
            type: "doc",
            content: [{ type: "text", text: "Hello" }],
          },
          // eslint-disable-next-line @typescript-eslint/naming-convention
          structured_request_nodes: [{ type: ChatRequestNodeType.TEXT, text: "text" }],
        };

        await (chatModel as any).deleteImagesInExchange(exchange);

        expect(deleteImageSpy).not.toHaveBeenCalled();
      });

      test("handles exchange with missing fields", async () => {
        const deleteImageSpy = vi.spyOn(chatModel, "deleteImage").mockResolvedValue(undefined);
        const exchange = {};

        await (chatModel as any).deleteImagesInExchange(exchange);

        expect(deleteImageSpy).not.toHaveBeenCalled();
      });
    });
  });

  describe("handleMessageFromExtension", () => {
    let chatModel: ChatModel;
    let mockChatModeModel: ChatModeModel;
    let setCurrentConversationSpy: MockInstance;
    let consoleWarnSpy: MockInstance;

    beforeEach(async () => {
      // Basic setup for ChatModel, similar to other test blocks
      // host and contextModel are available from the outer describe's beforeEach
      chatModel = new ChatModel(
        new AsyncMsgSender((message) => host.postMessage(message)),
        host,
        contextModel,
      );
      // Wait for initial conversation to be set up
      await new Promise(process.nextTick);

      // Mock ChatModeModel
      mockChatModeModel = {
        setToAgent: vi.fn().mockResolvedValue(undefined),
        setToChat: vi.fn(),
      } as any;

      setCurrentConversationSpy = vi
        .spyOn(chatModel, "setCurrentConversation")
        .mockResolvedValue(undefined as any);
      consoleWarnSpy = vi.spyOn(console, "warn").mockImplementation(() => {});
    });

    afterEach(() => {
      consoleWarnSpy.mockRestore();
    });

    const simulateNewThreadMessage = (messageData?: NewThreadData) => {
      const message: NewThread = {
        type: WebViewMessageType.newThread,
        data: messageData ?? {}, // Ensure data is always an object
      };

      chatModel.handleMessageFromExtension({ data: message } as MessageEvent<NewThread>);
      // Wait for async operations within handleMessageFromExtension to complete
      return new Promise(process.nextTick);
    };

    test("should handle newThread message with mode 'agent'", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "agent" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(mockChatModeModel.setToAgent).toHaveBeenCalledWith(AgentExecutionMode.manual);
      expect(mockChatModeModel.setToChat).not.toHaveBeenCalled();
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message with mode 'chat'", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "chat" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(mockChatModeModel.setToChat).toHaveBeenCalledTimes(1);
      expect(mockChatModeModel.setToAgent).not.toHaveBeenCalled();
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message with an invalid mode", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "unknown" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(mockChatModeModel.setToAgent).not.toHaveBeenCalled();
      expect(mockChatModeModel.setToChat).not.toHaveBeenCalled();
      expect(consoleWarnSpy).toHaveBeenCalledWith("Unknown chat mode:", "unknown");
    });

    test("should handle newThread message without mode in data", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({}); // Data is an empty object, so mode is undefined

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(mockChatModeModel.setToAgent).not.toHaveBeenCalled();
      expect(mockChatModeModel.setToChat).not.toHaveBeenCalled();
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message without data payload", async () => {
      chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage(undefined); // No data payload, simulateNewThreadMessage ensures data: {}

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(mockChatModeModel.setToAgent).not.toHaveBeenCalled();
      expect(mockChatModeModel.setToChat).not.toHaveBeenCalled();
      expect(consoleWarnSpy).not.toHaveBeenCalled();
    });

    test("should handle newThread message with mode when ChatModeModel is not set", async () => {
      // DO NOT call chatModel.setChatModeModel(mockChatModeModel);
      await simulateNewThreadMessage({ mode: "agent" });

      expect(setCurrentConversationSpy).toHaveBeenCalledTimes(1);
      expect(mockChatModeModel.setToAgent).not.toHaveBeenCalled(); // mockChatModeModel is not on chatModel
      expect(mockChatModeModel.setToChat).not.toHaveBeenCalled(); // mockChatModeModel is not on chatModel
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        "ChatModeModel not available, cannot set mode:",
        "agent",
      );
    });
  });
});

function mockConfirmationModalAccept(host: HostInterface): void {
  (host.postMessage as any as MockInstance).mockImplementation((msg) => {
    switch (msg.type) {
      case WebViewMessageType.asyncWrapper: {
        switch (msg.baseMsg?.type) {
          case WebViewMessageType.openConfirmationModal: {
            window.dispatchEvent(
              new MessageEvent("message", {
                data: {
                  type: WebViewMessageType.asyncWrapper,
                  requestId: msg.requestId,
                  error: null,
                  baseMsg: {
                    type: WebViewMessageType.confirmationModalResponse,
                    data: {
                      ok: true,
                    },
                  },
                  streamCtx: undefined,
                },
              }),
            );
            break;
          }
        }
        break;
      }
    }
  });
}

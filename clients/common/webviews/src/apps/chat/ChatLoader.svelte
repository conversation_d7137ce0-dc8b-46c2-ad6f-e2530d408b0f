<script lang="ts">
  import type { IChatFlags, IConversation } from "./models/types";
  import type { SubscriptionInfo } from "./models/subscription-model";
  import ComponentLoader from "$common-webviews/src/common/components/component-loader/ComponentLoader.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  export let initialConversation: IConversation | undefined = undefined;
  export let initialFlags: Partial<IChatFlags> | undefined = undefined;
  export let initialSubscriptionInfo: SubscriptionInfo | undefined = undefined;
  export let initialSubscriptionDismissed: boolean | undefined = undefined;

  export let loader = async () => {
    await import("./chat.css");
    return (await import("./Chat.svelte")).default as any;
  };
  const props = {
    initialConversation,
    initialFlags,
    initialSubscriptionInfo,
    initialSubscriptionDismissed,
  };
</script>

<MonacoProvider.Root>
  <ComponentLoader {loader} {props} />
</MonacoProvider.Root>

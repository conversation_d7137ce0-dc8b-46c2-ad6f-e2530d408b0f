import { onKeyDown } from "./chat-keybindings";
import type { ChatModel } from "./models/chat-model";
import type { RemoteAgentsModel } from "../remote-agent-manager/models/remote-agents-model";
import type { ChatModeModel } from "./models/chat-mode-model";
import type RichTextEditorRoot from "../../design-system/components/RichTextEditorAugment/Root.svelte";
import { writable } from "svelte/store";
import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock the dependencies
const mockChatModel = {
  flags: {
    enableDebugFeatures: false,
  },
} as ChatModel;
const mockRemoteAgentsModel = {
  isActive: false,
} as RemoteAgentsModel;
const mockChatModeModel = {} as ChatModeModel;

// Mock RichTextEditorRoot with focus state
const createMockRichTextEditorRoot = (isFocused: boolean) => {
  const mockRoot = {
    forceFocus: vi.fn(),
    isFocused: () => writable(isFocused),
  } as unknown as RichTextEditorRoot;
  return mockRoot;
};

describe("chat-keybindings", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Enter key handling", () => {
    it("should focus editor when Enter is pressed and editor is not focused", () => {
      const mockRichTextEditorRoot = createMockRichTextEditorRoot(false);
      const mockEvent = {
        key: "Enter",
        shiftKey: false,
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      onKeyDown(
        mockEvent,
        mockChatModel,
        mockRemoteAgentsModel,
        mockChatModeModel,
        mockRichTextEditorRoot,
      );

      expect(mockRichTextEditorRoot.forceFocus).toHaveBeenCalledOnce();
    });

    it("should NOT focus editor when Shift+Enter is pressed and editor is not focused", () => {
      const mockRichTextEditorRoot = createMockRichTextEditorRoot(false);
      const mockEvent = {
        key: "Enter",
        shiftKey: true, // This is the key part of the fix
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      onKeyDown(
        mockEvent,
        mockChatModel,
        mockRemoteAgentsModel,
        mockChatModeModel,
        mockRichTextEditorRoot,
      );

      expect(mockRichTextEditorRoot.forceFocus).not.toHaveBeenCalled();
    });

    it("should NOT focus editor when Enter is pressed and editor is already focused", () => {
      const mockRichTextEditorRoot = createMockRichTextEditorRoot(true);
      const mockEvent = {
        key: "Enter",
        shiftKey: false,
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      onKeyDown(
        mockEvent,
        mockChatModel,
        mockRemoteAgentsModel,
        mockChatModeModel,
        mockRichTextEditorRoot,
      );

      expect(mockRichTextEditorRoot.forceFocus).not.toHaveBeenCalled();
    });

    it("should NOT focus editor when Shift+Enter is pressed and editor is already focused", () => {
      const mockRichTextEditorRoot = createMockRichTextEditorRoot(true);
      const mockEvent = {
        key: "Enter",
        shiftKey: true,
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      onKeyDown(
        mockEvent,
        mockChatModel,
        mockRemoteAgentsModel,
        mockChatModeModel,
        mockRichTextEditorRoot,
      );

      expect(mockRichTextEditorRoot.forceFocus).not.toHaveBeenCalled();
    });

    it("should handle case when richTextEditorRoot is undefined", () => {
      const mockEvent = {
        key: "Enter",
        shiftKey: false,
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      // Should not throw an error
      expect(() => {
        onKeyDown(mockEvent, mockChatModel, mockRemoteAgentsModel, mockChatModeModel, undefined);
      }).not.toThrow();
    });
  });

  describe("Other key handling", () => {
    it("should handle non-Enter keys without focusing editor", () => {
      const mockRichTextEditorRoot = createMockRichTextEditorRoot(false);
      const mockEvent = {
        key: "Space",
        shiftKey: false,
        preventDefault: vi.fn(),
      } as unknown as KeyboardEvent;

      onKeyDown(
        mockEvent,
        mockChatModel,
        mockRemoteAgentsModel,
        mockChatModeModel,
        mockRichTextEditorRoot,
      );

      expect(mockRichTextEditorRoot.forceFocus).not.toHaveBeenCalled();
    });
  });
});

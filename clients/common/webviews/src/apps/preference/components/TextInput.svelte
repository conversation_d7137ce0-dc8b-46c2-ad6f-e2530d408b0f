<script lang="ts">
  export let value: string = "";
  export let question: string | null = null;
  export let placeholder: string = "";
</script>

<div class="container">
  {#if question}
    <div class="question">{question}</div>
  {/if}
  <textarea class="input" {placeholder} bind:value rows="3" />
</div>

<style>
  .container {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .question {
    font-size: 14px;
  }
  .input {
    padding: 10px;
    border: 2px solid #4a47f5;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    background-color: white;
    color: black;
  }
  .input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 71, 245, 0.5);
  }
</style>

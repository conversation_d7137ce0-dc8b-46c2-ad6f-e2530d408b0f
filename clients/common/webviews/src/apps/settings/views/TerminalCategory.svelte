<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";

  import type { ShellConfig } from "@augment-internal/sidecar-libs/src/tools/tool-types";

  export let supportedShells: ShellConfig[] = [];
  export let selectedShell: string | undefined = undefined;
  export let startupScript: string | undefined = undefined;
  export let onShellSelect: (shell: string) => void;
  export let onStartupScriptChange: (script: string) => void;

  function findShellByFriendlyName(friendlyName: string): ShellConfig | undefined {
    return supportedShells.find((s) => s.friendlyName === friendlyName);
  }

  // Reference to the dropdown close function
  let requestClose: () => void;

  // Handle startup script changes
  function handleStartupScriptChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    onStartupScriptChange(target.value);
  }
</script>

<div class="terminal-settings">
  <div class="section-heading">
    <TextAugment size={1} weight="regular" color="secondary">
      <div class="section-heading-text">Terminal</div>
    </TextAugment>
  </div>

  <div class="terminal-content">
    <div class="shell-selector">
      <TextAugment size={1} weight="medium">Shell</TextAugment>
      <div class="dropdown-container">
        <DropdownMenuAugment.Root bind:requestClose>
          <DropdownMenuAugment.Trigger>
            <div class="shell-dropdown-trigger" class:disabled={supportedShells.length === 0}>
              <div class="dropdown-content">
                <div class="shell-display">
                  <div class="shell-name">
                    {#if selectedShell && supportedShells.length > 0}
                      {#if findShellByFriendlyName(selectedShell)}
                        {findShellByFriendlyName(selectedShell)?.friendlyName || selectedShell}
                      {:else}
                        {selectedShell}
                      {/if}
                    {:else if supportedShells.length === 0}
                      No shells available
                    {:else}
                      Select a shell
                    {/if}
                  </div>
                  {#if selectedShell && supportedShells.length > 0 && findShellByFriendlyName(selectedShell)?.supportString}
                    <div class="shell-description">
                      {findShellByFriendlyName(selectedShell)?.supportString}
                    </div>
                  {/if}
                </div>
              </div>
              <div class="dropdown-icon">
                <ChevronDown />
              </div>
            </div>
          </DropdownMenuAugment.Trigger>
          <DropdownMenuAugment.Content side="bottom" align="start">
            {#if supportedShells.length > 0}
              {#each supportedShells as shell (shell.friendlyName)}
                <DropdownMenuAugment.Item
                  onSelect={() => {
                    onShellSelect(shell.friendlyName);
                    requestClose();
                  }}
                  highlight={selectedShell === shell.friendlyName}
                >
                  <div class="shell-display">
                    <div class="shell-name">{shell.friendlyName}</div>
                    {#if shell.supportString}
                      <div class="shell-description">{shell.supportString}</div>
                    {/if}
                  </div>
                </DropdownMenuAugment.Item>
              {/each}
            {:else}
              <DropdownMenuAugment.Label>No shells available</DropdownMenuAugment.Label>
            {/if}
          </DropdownMenuAugment.Content>
        </DropdownMenuAugment.Root>
      </div>
    </div>

    <div class="startup-script-container">
      <TextAugment size={1} weight="medium">Startup script</TextAugment>
      <div class="startup-script-description">
        <TextAugment size={1} color="secondary">
          Code to run whenever a new terminal is opened
        </TextAugment>
      </div>
      <textarea
        class="startup-script-textarea"
        placeholder="Enter shell commands to run on terminal startup"
        value={startupScript || ""}
        on:change={handleStartupScriptChange}
      ></textarea>
    </div>
  </div>
</div>

<style>
  .terminal-settings {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .section-heading {
    color: var(--ds-text);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    padding: var(--ds-spacing-2) 0;
  }

  .terminal-content {
    background-color: var(--settings-section-background);
    border-radius: var(--ds-border-radius-2);
    padding: var(--ds-spacing-3);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-4);
  }

  .shell-selector {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .dropdown-container {
    width: 100%;
  }

  .shell-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--ds-spacing-2);
    background-color: var(--ds-surface-1);
    border: 1px solid var(--ds-border);
    border-radius: var(--ds-radius-2);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;
    position: relative;
  }

  .shell-dropdown-trigger:hover {
    background-color: var(--ds-surface-2);
    border-color: var(--ds-border-hover);
  }

  .shell-dropdown-trigger::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    box-shadow: 0 0 0 1px var(--ds-border-hover);
    border-radius: var(--ds-radius-2);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .shell-dropdown-trigger:hover::after {
    opacity: 1;
  }

  .shell-dropdown-trigger:focus-within {
    border-color: var(--ds-focus-border);
    box-shadow: 0 0 0 2px var(--ds-focus-ring);
    outline: none;
  }

  .shell-dropdown-trigger.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  .dropdown-content {
    flex: 1;
  }

  .dropdown-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--ds-text);
    margin-left: var(--ds-spacing-2);
  }

  .dropdown-icon :global(svg) {
    width: 15px;
    height: 15px;
  }

  .shell-display {
    display: flex;
    flex-direction: column;
  }

  .shell-name {
    font-size: var(--ds-font-size-1);
    line-height: var(--ds-line-height-1);
  }

  .shell-description {
    font-size: calc(var(--ds-font-size-1) * 0.9);
    line-height: var(--ds-line-height-1);
    color: var(--ds-text-secondary);
  }

  .startup-script-container {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .startup-script-description {
    margin-bottom: var(--ds-spacing-1);
  }

  .startup-script-textarea {
    width: 100%;
    min-height: 120px;
    padding: var(--ds-spacing-2);
    color: var(--gray-12);
    font-family: var(--ds-font-mono);
    font-size: var(--ds-font-size-1);
    line-height: var(--ds-line-height-1);
    resize: vertical;
    transition: all 0.2s ease;

    /* Match MCP input styling */
    background-color: var(--ds-surface);
    border-radius: var(--ds-radius-2);
    box-shadow: inset 0 0 0 1px var(--gray-a7);
    border: none;
  }

  .startup-script-textarea:hover {
    box-shadow: inset 0 0 0 1px var(--gray-a9);
  }

  .startup-script-textarea:focus {
    outline: 2px solid var(--ds-color-8);
    outline-offset: -1px;
    box-shadow: none;
  }
</style>

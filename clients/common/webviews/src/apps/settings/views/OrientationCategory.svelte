<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import {
    OrientationState,
    type OrientationStatus,
  } from "$vscode/src/webview-providers/webview-messages";
  import Play from "$common-webviews/src/design-system/icons/augment/play.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import CheckIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-check.svg?component";

  export let onRunInitialOrientation: () => void;
  export let orientationInProgress = false;
  export let disabled = false;
  export let orientationStatus: OrientationStatus = { state: OrientationState.idle };
</script>

<div
  class="c-orientation-settings c-orientation-settings__{orientationStatus.state.replace('-', '_')}"
>
  <div class="c-orientation-settings-header">
    <IconButtonAugment
      size={1}
      variant="solid"
      color="accent"
      disabled={disabled || orientationInProgress}
      loading={orientationInProgress}
      on:click={onRunInitialOrientation}
    >
      <Play />
    </IconButtonAugment>
    {#if orientationInProgress}
      <TextAugment size={1} color="primary" class="c-orientation-settings-header-text">
        Codebase Orientation in progress: {orientationStatus.progress}%
      </TextAugment>
    {:else}
      <TextAugment size={1} color="primary" class="c-orientation-settings-header-text">
        Run Orientation
      </TextAugment>
    {/if}
  </div>
  {#if orientationStatus.state === OrientationState.succeeded || orientationStatus.state === OrientationState.failed}
    <CalloutAugment
      variant="soft"
      color={orientationStatus.state === OrientationState.succeeded
        ? "success"
        : orientationStatus.state === OrientationState.failed
          ? "error"
          : undefined}
      size={1}
      class="c-orientation-settings-callout"
    >
      <CheckIcon slot="icon" />
      {orientationStatus.state === OrientationState.succeeded
        ? "Succeeded"
        : orientationStatus.state === OrientationState.failed
          ? "Failed"
          : ""} at {orientationStatus.lastRunTimestamp
        ? new Date(orientationStatus.lastRunTimestamp).toLocaleTimeString()
        : "unknown time"}
      {#if orientationStatus.state === OrientationState.failed}
        <div>
          <TextAugment size={1} type="monospace">{orientationStatus.errorMessage}</TextAugment>
        </div>
      {/if}
    </CalloutAugment>
  {/if}
</div>

<style>
  .c-orientation-settings {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-orientation-settings-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .c-orientation-settings :global(.c-orientation-settings-header-text) {
    display: inline-block;
  }
  .c-orientation-settings :global(.c-orientation-settings-callout) {
    opacity: 0;
    transition: opacity 0.3s ease-in;
  }
  .c-orientation-settings.c-orientation-settings__idle :global(.c-orientation-settings-callout),
  .c-orientation-settings.c-orientation-settings__in-progress
    :global(.c-orientation-settings-callout) {
    transition: none;
  }
  .c-orientation-settings__failed :global(.c-orientation-settings-callout),
  .c-orientation-settings__succeeded :global(.c-orientation-settings-callout) {
    opacity: 0.99;
  }
</style>

<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import Logout from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/right-from-bracket.svg?component";
  export let onSignOut: () => void;
  let loading = false;
  function handleSignOut() {
    onSignOut();
    loading = true;
  }
</script>

<ButtonAugment {loading} variant="soft" on:click={handleSignOut} data-testid="sign-out-button">
  <Logout slot="iconLeft" />
  Sign Out
</ButtonAugment>

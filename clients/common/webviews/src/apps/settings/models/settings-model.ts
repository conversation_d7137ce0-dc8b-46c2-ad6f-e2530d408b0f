import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";
import type { MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { LocalToolType } from "$vscode/src/webview-providers/tool-types";
import {
  type GuidelinesStates,
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import {
  RemoteToolId,
  type ToolDefinitionWithSettings,
  ToolHostName,
  type ToolIdentifier,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import type { ComponentType } from "svelte";
import { derived, writable, type Writable } from "svelte/store";
import { PollingManager, type PollingParams } from "./polling-manager";
import { type UserTier } from "$vscode/src/main-panel/action-cards/types";

// Remote tool icons
import ConfluenceIcon from "$common-webviews/src/design-system/icons/confluence.svelte";
import GithubIcon from "$common-webviews/src/design-system/icons/github.svelte";
import GlobeIcon from "$common-webviews/src/design-system/icons/globe.svelte";
import JiraIcon from "$common-webviews/src/design-system/icons/jira.svelte";
import LinearIcon from "$common-webviews/src/design-system/icons/linear.svelte";
import NotionIcon from "$common-webviews/src/design-system/icons/notion.svelte";
import SupabaseIcon from "$common-webviews/src/design-system/icons/supabase.svelte";
import GleanIcon from "$common-webviews/src/design-system/icons/glean.svelte";
// Local tool icons
import FileTextIcon from "$common-webviews/src/design-system/icons/file-text.svelte";
import FileMinusIcon from "$common-webviews/src/design-system/icons/file-minus.svelte";
import FilePlusIcon from "$common-webviews/src/design-system/icons/file-plus.svelte";
import MagnifyingGlassIcon from "$common-webviews/src/design-system/icons/magnifying-glass.svelte";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import type { SettingsComponentSupported as SettingsComponentSupported } from "$vscode/src/webview-panels/settings-panel-types";
import DiagramIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/diagram-nested.svg?component";

export interface ConfigBlock {
  // Extra configuration for a tool
  config: any;
  // True if the tool has been set up
  isConfigured: boolean;

  // Identifies the tool
  identifier: ToolIdentifier;
  name: string;

  // Display properties
  displayName: string;
  description: string;
  icon?: ComponentType;

  // Authentication status
  requiresAuthentication: boolean;
  authUrl?: string; // URL to launch for authentication

  // UI-specific fields
  configString?: string;
  showStatus?: boolean;
  statusMessage?: string;
  statusType?: "info" | "error";
}

export class ToolConfigModel implements MessageConsumer {
  private configs: Writable<ConfigBlock[]> = writable([]);
  private pollingManager: PollingManager;
  private _enableDebugFeatures: Writable<boolean> = writable(false);
  private _settingsComponentSupported: Writable<SettingsComponentSupported> = writable({
    workspaceContext: false,
    mcpServerList: false,
    mcpServerImport: false,
    orientation: false,
    remoteTools: false,
    userGuidelines: false,
    terminal: false,
    rules: false,
  });
  private _enableAgentMode: Writable<boolean> = writable(false);
  private _enableInitialOrientation: Writable<boolean> = writable(false);
  private _userTier: Writable<UserTier> = writable("unknown");
  private _guidelines: Writable<GuidelinesStates> = writable({});

  constructor(private readonly _host: HostInterface) {
    // I set the polling interval to 2 seconds because the callback is currently
    // taking ~400ms to retrieve and process the tool definitions. The callback
    // is asynchronous and does not block the timer launching a new request.
    // Setting the interval to 2 seconds provides a 5x safety margin in
    // preventing a backlog of requests. I will revisit this implementation in
    // the future, to prevent a backlog from being possible.
    const pollingParams: PollingParams = {
      maxMS: 0, // Set to 0 to stop polling after acceleration period
      initialMS: 2 * 1000, // accelerated interval
      mult: 1, // flat interval
      maxSteps: 150, // Number of steps: poll for 300 seconds (5 minutes)
    };

    // Initialize polling manager with callback to request tool definitions
    this.pollingManager = new PollingManager(() => this.requestToolStatus(false), pollingParams);
    // Initial request to populate the panel with tool definitions
    this.requestToolStatus(false);
  }

  private transformToolDisplay(
    tool: ToolDefinitionWithSettings,
  ): Pick<
    ConfigBlock,
    "displayName" | "description" | "icon" | "requiresAuthentication" | "authUrl"
  > {
    // Determine if the tool requires authentication based on its configuration status
    const requiresAuthentication = !tool.isConfigured;

    // Get authentication URL from tool definition if available
    const authUrl = tool.oauthUrl;

    if (tool.identifier.hostName === ToolHostName.remoteToolHost) {
      let toolId = tool.identifier.toolId as RemoteToolId;
      if (typeof toolId === "string" && /^\d+$/.test(toolId)) {
        // If toolId is a string that looks like a number, convert it to a number
        toolId = Number(toolId) as RemoteToolId;
      }
      switch (toolId) {
        case RemoteToolId.GitHubApi:
          return {
            displayName: "GitHub",
            description: "Configure GitHub API access for repository operations",
            icon: GithubIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.Linear:
          return {
            displayName: "Linear",
            description: "Configure Linear API access for issue tracking",
            icon: LinearIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.Jira:
          return {
            displayName: "Jira",
            description: "Configure Jira API access for issue tracking",
            icon: JiraIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.Notion:
          return {
            displayName: "Notion",
            description: "Configure Notion API access",
            icon: NotionIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.Confluence:
          return {
            displayName: "Confluence",
            description: "Configure Confluence API access",
            icon: ConfluenceIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.WebSearch:
          return {
            displayName: "Web Search",
            description: "Configure web search capabilities",
            icon: GlobeIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.Supabase:
          return {
            displayName: "Supabase",
            description: "Configure Supabase API access",
            icon: SupabaseIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.Glean:
          return {
            displayName: "Glean",
            description: "Configure Glean API access",
            icon: GleanIcon,
            requiresAuthentication,
            authUrl,
          };
        case RemoteToolId.Unknown:
          return {
            displayName: "Unknown",
            description: "Unknown tool",
            requiresAuthentication,
            authUrl,
          };
        default: {
          const exhaustiveCheck: never = toolId;
          throw new Error(`Unhandled RemoteToolId: ${exhaustiveCheck}`);
        }
      }
    } else if (tool.identifier.hostName === ToolHostName.localToolHost) {
      const toolId = tool.identifier.toolId as LocalToolType;
      switch (toolId) {
        case LocalToolType.readFile:
        case LocalToolType.editFile:
        case LocalToolType.saveFile:
        case LocalToolType.launchProcess:
        case LocalToolType.killProcess:
        case LocalToolType.readProcess:
        case LocalToolType.writeProcess:
        case LocalToolType.listProcesses:
        case LocalToolType.waitProcess:
        case LocalToolType.openBrowser:
        case LocalToolType.clarify:
        case LocalToolType.onboardingSubAgent:
        case LocalToolType.strReplaceEditor:
        case LocalToolType.remember:
        case LocalToolType.diagnostics:
        case LocalToolType.setupScript:
        case LocalToolType.readTerminal:
        case LocalToolType.gitCommitRetrieval:
          return {
            displayName: tool.definition.name.toString(),
            description: "Local tool",
            icon: FileTextIcon,
            requiresAuthentication,
            authUrl,
          };
        default: {
          const exhaustiveCheck: never = toolId;
          throw new Error(`Unhandled LocalToolType: ${exhaustiveCheck}`);
        }
      }
    } else if (tool.identifier.hostName === ToolHostName.sidecarToolHost) {
      const toolId = tool.identifier.toolId as SidecarToolType;
      switch (toolId) {
        case SidecarToolType.codebaseRetrieval:
          return {
            displayName: "Code Search",
            description: "Configure codebase search capabilities",
            icon: MagnifyingGlassIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.shell:
          return {
            displayName: "Shell",
            description: "Shell",
            icon: MagnifyingGlassIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.strReplaceEditor:
          return {
            displayName: "File Edit",
            description: "File Editor",
            icon: MagnifyingGlassIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.view:
          return {
            displayName: "File View",
            description: "File Viewer",
            icon: MagnifyingGlassIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.webFetch:
          return {
            displayName: "Web Fetch",
            description: "Retrieve information from the web",
            icon: MagnifyingGlassIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.removeFiles:
          return {
            displayName: "Remove Files",
            description: "Remove files from the codebase",
            icon: FileMinusIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.remember:
          return {
            displayName: tool.definition.name.toString(),
            description: "Remember",
            icon: FileTextIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.saveFile:
          return {
            displayName: "Save File",
            description: "Save a new file",
            icon: FilePlusIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.viewTaskList:
          return {
            displayName: "View Task List",
            description: "View the current task list",
            icon: FileTextIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.updateTaskList:
          return {
            displayName: "Update Task List",
            description: "Update the current task list",
            icon: FileTextIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.renderMermaid:
          return {
            displayName: "View Mermaid Diagram",
            description: "View a mermaid diagram",
            icon: DiagramIcon,
            requiresAuthentication,
            authUrl,
          };
        case SidecarToolType.grepSearch:
          return {
            displayName: "Grep search",
            description: "Run grep search",
            icon: MagnifyingGlassIcon,
            requiresAuthentication,
            authUrl,
          };
        default: {
          const exhaustiveCheck: never = toolId;
          throw new Error(`Unhandled SidecarToolType: ${exhaustiveCheck}`);
        }
      }
    }

    // Default fallback
    return {
      displayName: tool.definition.name.toString(),
      description: tool.definition.description || "",
      requiresAuthentication,
      authUrl,
    };
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.toolConfigInitialize: {
        // Create initial configs from host tools
        /* eslint-disable-next-line @typescript-eslint/no-unused-vars */
        const initialConfigs = this.createConfigsFromHostTools(
          msg.data.hostTools,
          msg.data.toolConfigs,
        );
        // Note(rich): Loading from persistent state is disabled for now.
        // this.configs.set(initialConfigs);

        // Update the debug features flag if it's included in the message
        if (msg.data && msg.data.enableDebugFeatures !== undefined) {
          this._enableDebugFeatures.set(msg.data.enableDebugFeatures);
        }

        if (msg.data && msg.data.settingsComponentSupported !== undefined) {
          this._settingsComponentSupported.set(msg.data.settingsComponentSupported);
        }

        if (msg.data.enableAgentMode !== undefined) {
          this._enableAgentMode.set(msg.data.enableAgentMode);
        }

        if (msg.data.enableInitialOrientation !== undefined) {
          this._enableInitialOrientation.set(msg.data.enableInitialOrientation);
        }

        if (msg.data.userTier !== undefined) {
          this._userTier.set(msg.data.userTier);
        }

        if (msg.data.guidelines !== undefined) {
          this._guidelines.set(msg.data.guidelines);
        }

        return true;
      }
      case WebViewMessageType.toolConfigDefinitionsResponse: {
        // Update configs with new tool definitions while preserving existing state
        this.configs.update((currentConfigs) => {
          const updatedConfigs = this.createConfigsFromHostTools(
            msg.data.hostTools,
            [], // We don't get saved configs in the response, use empty array
          );

          // Merge with existing configs to preserve UI state
          return updatedConfigs.map((newConfig) => {
            const existingConfig = currentConfigs.find((c) => c.name === newConfig.name);
            if (existingConfig) {
              // Preserve UI state but update definition-based properties
              return {
                ...existingConfig,
                // Update properties that might have changed
                displayName: newConfig.displayName,
                description: newConfig.description,
                icon: newConfig.icon,
                requiresAuthentication: newConfig.requiresAuthentication,
                authUrl: newConfig.authUrl,
                // Always use the latest isConfigured value from the server
                isConfigured: newConfig.isConfigured,
              };
            }
            return newConfig;
          });
        });
        return true;
      }
    }
    return false;
  }

  /**
   * Create config blocks from host tools
   */
  private createConfigsFromHostTools(
    hostTools: ToolDefinitionWithSettings[],
    savedToolConfigs: any[],
  ): ConfigBlock[] {
    return hostTools.map((tool): ConfigBlock => {
      const display = this.transformToolDisplay(tool);
      const savedConfig = savedToolConfigs.find((c) => c.name === tool.definition.name);

      // If the tool doesn't require authentication, it should be automatically configured
      const isConfigured =
        savedConfig?.isConfigured ?? (display.requiresAuthentication ? false : true);

      return {
        config: savedConfig?.config ?? {},
        configString: JSON.stringify(savedConfig?.config ?? {}, null, 2),
        isConfigured,
        name: tool.definition.name.toString(),
        displayName: display.displayName,
        description: display.description,
        identifier: tool.identifier,
        icon: display.icon,
        requiresAuthentication: display.requiresAuthentication,
        authUrl: display.authUrl,
        showStatus: false,
        statusMessage: "",
        statusType: "info",
      };
    });
  }

  getConfigs() {
    return this.configs;
  }

  // Filter tools to only show GitHub, Linear, Notion, Jira, Confluence, and Supabase
  isDisplayableTool(config: ConfigBlock): boolean {
    // Check if the tool is one of the specified ones
    const displayableTools = ["github", "linear", "notion", "jira", "confluence", "supabase"];
    return displayableTools.includes(config.displayName.toLowerCase());
  }

  // Get filtered, sorted, and deduplicated tools
  getDisplayableTools() {
    // Use Svelte's derived store
    return derived(this.configs, (configs) => {
      // Filter tools
      const filtered = configs.filter((config) => this.isDisplayableTool(config));

      // Deduplicate by displayName using a Map for O(n) performance
      const toolMap = new Map<string, ConfigBlock>();
      for (const tool of filtered) {
        // Always use the latest tool with a given displayName
        toolMap.set(tool.displayName, tool);
      }
      const uniqueTools = Array.from(toolMap.values());

      // Note: We want to show GitHub, Linear, and Notion prominently in the list.
      // Custom sort: GitHub, Linear, Notion, then alphabetical
      const sortedTools = uniqueTools.sort((a, b) => {
        /* eslint-disable @typescript-eslint/naming-convention */
        const priorityOrder: { [key: string]: number } = {
          GitHub: 1,
          Linear: 2,
          Notion: 3,
        };
        /* eslint-enable @typescript-eslint/naming-convention */

        // Use MAX_SAFE_INTEGER for non-priority items
        const MAX_PRIORITY = Number.MAX_SAFE_INTEGER;
        const priorityA = priorityOrder[a.displayName] || MAX_PRIORITY;
        const priorityB = priorityOrder[b.displayName] || MAX_PRIORITY;

        // If both are in the priority list or both are not in the priority list
        if (
          (priorityA < MAX_PRIORITY && priorityB < MAX_PRIORITY) ||
          (priorityA === MAX_PRIORITY && priorityB === MAX_PRIORITY)
        ) {
          // If both are priority items, sort by priority
          if (priorityA !== priorityB) {
            return priorityA - priorityB;
          }
          // If both are non-priority or have the same priority, sort alphabetically
          return a.displayName.localeCompare(b.displayName);
        }

        // If only one is in the priority list, it comes first
        // Note: This subtraction is safe because at least one value is small
        return priorityA - priorityB;
      });

      return sortedTools;
    });
  }

  saveConfig(_config: ConfigBlock) {
    // Start polling after configuration changes
    this.startPolling();
  }

  notifyLoaded() {
    this._host.postMessage({
      type: WebViewMessageType.toolConfigLoaded,
    });
  }

  /**
   * Start polling after user interaction
   */
  startPolling() {
    this.pollingManager.startPolling();
  }

  /**
   * Request tool status from the extension
   */
  requestToolStatus(useCache: boolean = true) {
    this._host.postMessage({
      type: WebViewMessageType.toolConfigGetDefinitions,
      data: {
        useCache: useCache,
      },
    });
  }

  /**
   * Clean up resources when the component is destroyed
   */
  dispose() {
    this.pollingManager.dispose();
  }

  /**
   * Get the enableDebugFeatures store
   */
  getEnableDebugFeatures() {
    return this._enableDebugFeatures;
  }

  /**
   * Get the enableAgentMode store
   */
  getEnableAgentMode() {
    return this._enableAgentMode;
  }

  /**
   * Get the enableInitialOrientation store
   */
  getEnableInitialOrientation() {
    return this._enableInitialOrientation;
  }

  getUserTier() {
    return this._userTier;
  }

  getGuidelines() {
    return this._guidelines;
  }

  /**
   * Update the local user guidelines state without waiting for a response from the extension
   * This ensures the state is immediately available when switching tabs
   */
  updateLocalUserGuidelines(content: string) {
    this._guidelines.update((currentGuidelines) => {
      if (!currentGuidelines.userGuidelines) {
        return currentGuidelines;
      }

      return {
        ...currentGuidelines,
        userGuidelines: {
          ...currentGuidelines.userGuidelines,
          contents: content,
          enabled: content.length > 0,
        },
      };
    });
  }

  /**
   * Get the showWorkspaceContext store
   */
  getSettingsComponentSupported() {
    return this._settingsComponentSupported;
  }
}

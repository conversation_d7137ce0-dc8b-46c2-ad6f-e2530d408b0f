<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import type { ComponentType } from "svelte";

  export let className: string = "";
  export { className as class };

  // New props for specialized card
  export let icon: ComponentType | undefined = undefined;
  export let title: string | undefined = undefined;
  export let isClickable: boolean = false;

  // Extract class from restProps and keep the rest
  $: ({ class: propClass, ...restProps } = $$restProps);
  $: combinedClass = `settings-card ${className} ${propClass || ""}`;
</script>

<!-- svelte-ignore a11y-interactive-supports-focus -->
<div role="button" on:click class={combinedClass} class:clickable={isClickable} {...restProps}>
  <div class="settings-card-content">
    <div class="settings-card-left">
      {#if icon || title}
        <!-- Use icon and title props if provided -->
        {#if icon}
          <div class="icon-wrapper">
            <svelte:component this={icon} />
          </div>
        {/if}
        {#if title}
          <TextAugment color="neutral" size={1} weight="light" class="card-title">
            {title}
          </TextAugment>
        {/if}
      {:else}
        <!-- Fallback to slot if props not provided -->
        <slot name="header-left" />
      {/if}
    </div>
    <div class="settings-card-right">
      <slot name="header-right" />
    </div>
  </div>
  {#if $$slots.default}
    <div class="settings-card-body">
      <slot />
    </div>
  {/if}
</div>

<style>
  .settings-card {
    padding: var(--ds-spacing-1);
    background-color: var(--settings-section-background);
    border-radius: var(--ds-radius-2);

    display: flex;
    flex-direction: column;

    overflow: hidden;
  }
  .settings-card.clickable:hover {
    background-color: var(--ds-color-neutral-a3);
  }

  .settings-card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .settings-card-left {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
    overflow: hidden;
  }

  .settings-card-right {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    font-size: 1.5em;
  }

  .icon-wrapper :global(svg) {
    width: 1.08rem;
    height: 1.08rem;
  }
</style>

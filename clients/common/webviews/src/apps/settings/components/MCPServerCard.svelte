<script lang="ts">
  import { isMCPS<PERSON>r<PERSON>rror, MCPServerError, MCPServerModel } from "../models/mcp-server-model";
  import type { MCPServer } from "$vscode/src/webview-providers/webview-messages";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import McpIcon from "$common-webviews/src/design-system/icons/mcp-logo.svelte";
  import CalloutAugment from "$common-webviews/src/design-system/components/CalloutAugment.svelte";
  import CircleXMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/circle-xmark.svg?component";
  import McpServerEnvironmentVariables from "./MCPServerEnvironmentVariables.svelte";
  import DotsHorizontalIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import TrashCan from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash-can.svg?component";
  import PenToSquare from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/pen-to-square.svg?component";
  import SettingsCard from "./SettingsCard.svelte";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import type { MCPCardModeType, MCPEnvVarEntry } from "./types";
  import ClipboardCopy from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/copy.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ToggleAugment from "$common-webviews/src/design-system/components/ToggleAugment.svelte";

  // Props
  export let server: MCPServer | null = null;
  export let onDelete: (id: string) => unknown;
  export let onAdd: (server: Omit<MCPServer, "id">) => unknown;
  export let onSave: (server: MCPServer) => unknown;
  export let onEdit: (server: MCPServer) => unknown;
  export let onToggleDisableServer: (id: string) => unknown;
  export let onJSONImport: (jsonString: string) => Promise<number>;
  export let onCancel: () => unknown;
  export let disabledText: string | undefined = undefined;
  export let warningText: string | undefined = undefined;

  export let mode: MCPCardModeType = "view";
  export let mcpServerError = "";

  let name = server?.name ?? "";
  let command = server?.command ?? "";
  let envVars: Record<string, string> = server?.env ?? {};
  let jsonInput = "";

  // Local form state for environment variables that is bound/accessed in this component
  let envVarEntries: MCPEnvVarEntry[] = [];
  setLocalEnvVarFormState(); // initialize local form state

  /*
   * Updates the local form state for environment variables with updated values.
   * with unique IDs to prevent reordering
   */
  export function setLocalEnvVarFormState() {
    envVarEntries = Object.entries(envVars).map(([key, value]) => ({
      id: crypto.randomUUID(),
      key,
      value,
    }));
  }

  // For dropdown menu
  let requestClose: () => void = () => {};
  $: {
    if (name && command) {
      mcpServerError = "";
    }
  }

  $: isFormInvalid = mode === "add" && (!name.trim() || !command.trim());
  $: isJsonInputInvalid = mode === "addJson" && !jsonInput.trim();
  $: isButtonDisabled = isFormInvalid || mode === "view" || isJsonInputInvalid;

  $: headerText = mode === "add" || mode === "addJson" ? "New MCP Server" : "Edit MCP Server";

  function handleEnterEditMode() {
    if (server && mode === "view") {
      mode = "edit";
      onEdit(server);
      requestClose();
    }
  }

  export let busy = false;

  // Basic validation for env vars: filter out empty entries
  function validateEnvVars({ key, value }: MCPEnvVarEntry) {
    return key.trim() && value.trim();
  }

  function onCopyJSON() {
    if (server) {
      const jsonText = MCPServerModel.convertServerToJSON(server);
      navigator.clipboard.writeText(jsonText);
    }
  }

  async function onCommit() {
    // Clear the error first
    mcpServerError = "";
    busy = true;
    // Get the updated env vars from the child component to be saved and sanitize them
    // and then save to local form state
    const validEnvVarEntries = envVarEntries.filter(validateEnvVars);
    envVars = Object.fromEntries(
      validEnvVarEntries.map(({ key, value }) => [key.trim(), value.trim()]),
    );
    setLocalEnvVarFormState();
    try {
      if (mode === "add") {
        await onAdd({
          name: name.trim(),
          command: command.trim(),
          arguments: "", // Keep empty for backward compatibility
          useShellInterpolation: true, // New servers use shell interpolation
          env: Object.keys(envVars).length > 0 ? envVars : undefined,
        });
      } else if (mode === "addJson") {
        try {
          JSON.parse(jsonInput);
        } catch (jsonError) {
          // Handle JSON parsing errors specifically
          const errorMessage = jsonError instanceof Error ? jsonError.message : String(jsonError);
          throw new MCPServerError(`Invalid JSON format: ${errorMessage}`);
        }
        await onJSONImport(jsonInput);
      } else if (mode === "edit" && server) {
        await onSave({
          ...server,
          name: name.trim(),
          command: command.trim(),
          arguments: "", // Keep empty for backward compatibility
          env: Object.keys(envVars).length > 0 ? envVars : undefined,
        });
      }
    } catch (e) {
      mcpServerError = isMCPServerError(e) ? e.message : "Failed to save server";
      console.warn(e);
    } finally {
      busy = false;
    }
  }
  function handleCancel() {
    busy = false;
    mcpServerError = "";
    onCancel?.();
    // Reset the form state to original values
    jsonInput = "";
    name = server?.name ?? "";
    command = server?.command ?? "";
    envVars = server?.env ? { ...server.env } : {};
    setLocalEnvVarFormState();
  }
</script>

<!-- Collapsed single line view of MCP server name + command -->
{#if mode === "view" && server}
  <SettingsCard>
    <div slot="header-left" class="l-header">
      <TextTooltipAugment content={disabledText || warningText}>
        <div
          class="c-dot"
          class:c-green={!disabledText}
          class:c-warning={!disabledText && !!warningText}
          class:c-red={!!disabledText}
          class:c-disabled={server.disabled}
        />
      </TextTooltipAugment>
      <TextTooltipAugment content={server.name} side="top" align="start">
        <div class="server-name">
          <TextAugment size={1} weight="medium">{server.name}</TextAugment>
        </div>
      </TextTooltipAugment>
      <TextTooltipAugment content={server.command} side="top" align="start">
        <div class="command-text">
          <TextAugment color="secondary" size={1} weight="regular">
            {server.command}
          </TextAugment>
        </div>
      </TextTooltipAugment>
    </div>
    <div class="server-actions" slot="header-right">
      <div class="status-controls">
        <ToggleAugment
          size={1}
          checked={!server.disabled}
          on:change={() => {
            if (server) {
              onToggleDisableServer(server.id);
            }
            requestClose();
          }}
        />
        <DropdownMenu.Root bind:requestClose>
          <DropdownMenu.Trigger>
            <IconButtonAugment size={1} variant="ghost-block" color="neutral">
              <DotsHorizontalIcon />
            </IconButtonAugment>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content side="bottom" align="end">
            <DropdownMenu.Item onSelect={handleEnterEditMode}>
              <div class="status-controls-button">
                <PenToSquare />
                <TextAugment size={1} weight="medium">Edit</TextAugment>
              </div>
            </DropdownMenu.Item>
            <DropdownMenu.Item
              onSelect={() => {
                onCopyJSON();
                requestClose();
              }}
            >
              <div class="status-controls-button">
                <ClipboardCopy />
                <TextAugment size={1} weight="medium">Copy JSON</TextAugment>
              </div>
            </DropdownMenu.Item>

            <DropdownMenu.Item
              color="error"
              onSelect={() => {
                onDelete(server.id);
                requestClose();
              }}
            >
              <div class="status-controls-button">
                <TrashCan />
                <TextAugment size={1} weight="medium">Delete</TextAugment>
              </div>
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </div>
    </div>
  </SettingsCard>
{:else}
  <form
    class="c-mcp-server-card {mode === 'add' || mode === 'addJson'
      ? 'add-server-section'
      : 'server-item'}"
    on:submit|preventDefault={onCommit}
  >
    <div class="server-edit-form">
      <div class="server-header">
        <div class="server-title">
          <div class="server-icon">
            <McpIcon />
          </div>
          <TextAugment color="secondary" size={1} weight="medium">{headerText}</TextAugment>
        </div>
      </div>

      {#if mode === "addJson"}
        <div class="form-row">
          <div class="input-field">
            <TextAugment size={1} weight="medium">Code Snippet</TextAugment>
          </div>
        </div>
        <div class="form-row">
          <div class="input-field">
            <TextAreaAugment bind:value={jsonInput} size={1} placeholder="Paste JSON here..." />
          </div>
        </div>
      {:else if mode === "add" || mode === "edit"}
        <div class="form-row">
          <div class="input-field">
            <TextFieldAugment
              size={1}
              bind:value={name}
              on:focus={handleEnterEditMode}
              placeholder="Enter a name for your MCP server (e.g., 'Server Memory')"
            >
              <TextAugment slot="label" size={1} weight="medium">Name</TextAugment>
            </TextFieldAugment>
          </div>
        </div>
        <div class="form-row">
          <div class="input-field">
            <TextFieldAugment
              size={1}
              bind:value={command}
              on:focus={handleEnterEditMode}
              placeholder="Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')"
            >
              <TextAugment slot="label" size={1} weight="medium">Command</TextAugment>
            </TextFieldAugment>
          </div>
        </div>
      {/if}

      {#if mode === "add" || mode === "edit"}
        <McpServerEnvironmentVariables bind:envVarEntries {handleEnterEditMode} />
      {/if}

      <div class="form-actions-row">
        <div class="error-container" class:is-error={!!mcpServerError}>
          <CalloutAugment variant="soft" color="error" size={1}>
            <CircleXMark slot="icon" />
            {mcpServerError}
          </CalloutAugment>
        </div>
        <div class="form-actions">
          <ButtonAugment
            size={1}
            variant="ghost"
            color="neutral"
            on:click={handleCancel}
            type="button"
          >
            Cancel
          </ButtonAugment>
          <ButtonAugment
            size={1}
            variant="solid"
            color="accent"
            loading={busy}
            type="submit"
            disabled={isButtonDisabled}
          >
            {#if mode === "addJson"}
              Import
            {:else if mode === "add"}
              Add
            {:else if mode === "edit"}
              Save
            {/if}
          </ButtonAugment>
        </div>
      </div>
    </div>
  </form>
{/if}

<style>
  .server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-3);
    background-color: var(--settings-section-background);
    border-radius: var(--ds-radius-2);
    margin-bottom: var(--ds-spacing-3);
  }

  .server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--ds-spacing-3);
    padding-bottom: var(--ds-spacing-2);
  }

  .server-title {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .server-icon {
    display: flex;
    align-items: center;
  }

  .server-icon :global(svg) {
    width: 20px;
    height: 20px;
  }

  .server-actions {
    display: flex;
    gap: var(--ds-spacing-1);
  }

  .status-controls {
    display: flex;
    align-items: center;
    padding-left: var(--ds-spacing-2);
    gap: var(--ds-spacing-2);
  }

  .server-edit-form {
    width: 100%;
  }

  .add-server-section {
    padding: var(--ds-spacing-3);
    background-color: var(--settings-section-background);
    border-radius: var(--ds-radius-2);
  }

  .form-row {
    margin-bottom: var(--ds-spacing-3);
  }

  .input-field {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--ds-spacing-2);
  }

  .form-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .error-container {
    flex-grow: 1;
    margin-right: var(--ds-spacing-2);
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  .error-container.is-error {
    opacity: 1;
  }
  .status-controls :global(svg) {
    width: 14px;
    height: 14px;
    fill: var(--icon-color, currentColor);
  }
  .status-controls-button {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
  .l-header {
    display: flex;
    gap: var(--ds-spacing-1);
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }

  .c-green {
    background-color: var(--ds-color-success-a11);
  }
  .c-red {
    background-color: var(--ds-color-error-a11);
  }
  .c-warning {
    background-color: var(--ds-color-warning-a11);
  }
  .c-disabled {
    background-color: transparent;
    border: 1px solid var(--ds-color-neutral-a11);
  }
  .c-dot {
    margin-left: var(--ds-spacing-1);
    margin-right: var(--ds-spacing-1);
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .server-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .command-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>

<script lang="ts">
  import type { ConfigBlock } from "../models/settings-model";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import SettingsCard from "./SettingsCard.svelte";
  import BadgeAugment from "$common-webviews/src/design-system/components/BadgeAugment";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import Disconnect from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/disconnect.svg?component";

  export let config: ConfigBlock;
  export let onAuthenticate: (url: string) => void;
  export let onRevokeAccess: (config: ConfigBlock) => void;

  let requestClose: () => void = () => {
    // Reset the disconnect button state when the dropdown is closed
    disconnectButtonActive = false;
  };
  let isConnecting = false;
  let connectingTimeout: ReturnType<typeof setTimeout> | null = null;
  let disconnectButtonActive = false;

  // Reset connecting state when config changes (e.g., when tool becomes configured)
  $: if (config.isConfigured && isConnecting) {
    // Immediately reset the connecting state when the tool becomes configured
    isConnecting = false;
    if (connectingTimeout) {
      clearTimeout(connectingTimeout);
      connectingTimeout = null;
    }
  }

  // Handle connect button click - toggles between connecting and canceling states
  function handleConnectClick() {
    if (isConnecting) {
      // Cancel the connecting state
      isConnecting = false;
      if (connectingTimeout) {
        clearTimeout(connectingTimeout);
        connectingTimeout = null;
      }
    } else {
      // Set connecting state
      isConnecting = true;

      // The if condition above ensures authUrl is defined
      const authUrl = config.authUrl || "";
      onAuthenticate(authUrl);

      // Don't immediately update the status - let the polling handle it
      // This allows the spinner to remain visible until authentication completes

      // Reset connecting state after a timeout (60 seconds)
      // This is a fallback in case the user abandons the oauth flow.
      connectingTimeout = setTimeout(() => {
        isConnecting = false;
        connectingTimeout = null;
      }, 60000);
    }
  }
</script>

<div
  class="config-wrapper"
  role="group"
  aria-label="Connection status controls"
  on:mouseenter={() => (disconnectButtonActive = true)}
  on:mouseleave={() => (disconnectButtonActive = false)}
>
  <SettingsCard icon={config.icon} title={config.displayName}>
    <div slot="header-right">
      {#if !config.isConfigured && config.authUrl}
        <ButtonAugment
          variant="ghost-block"
          color={isConnecting ? "neutral" : "accent"}
          size={1}
          on:click={handleConnectClick}
        >
          <div class="connect-button-content">
            {#if isConnecting}
              <div class="connect-button-spinner">
                <SpinnerAugment size={1} useCurrentColor={true} />
              </div>
              <span>Cancel</span>
            {:else}
              <span>Connect</span>
            {/if}
          </div>
        </ButtonAugment>
      {:else if config.isConfigured}
        <div class="status-controls">
          <div class="icon-container">
            <DropdownMenu.Root
              bind:requestClose
              onOpenChange={(open) => {
                if (!open) {
                  disconnectButtonActive = false;
                }
              }}
            >
              <DropdownMenu.Trigger>
                <div class="connection-status">
                  <div
                    class="icon-button-wrapper"
                    class:active={disconnectButtonActive}
                  >
                    <TextTooltipAugment
                      triggerOn={[TooltipTriggerOn.Hover]}
                      content="Revoke Access"
                    >
                      <IconButtonAugment
                        color="neutral"
                        variant="ghost"
                        size={1}
                        on:click={() =>
                          (disconnectButtonActive = !disconnectButtonActive)}
                      >
                        <Disconnect />
                      </IconButtonAugment>
                    </TextTooltipAugment>
                  </div>
                </div>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content size={1} side="bottom" align="end">
                <DropdownMenu.Item
                  onSelect={() => {
                    onRevokeAccess(config);
                    disconnectButtonActive = false;
                    requestClose();
                  }}
                >
                  <TextAugment size={1} weight="medium"
                    >Revoke Access</TextAugment
                  >
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
            <BadgeAugment.Root color="success" size={1} variant="soft"
              >Connected</BadgeAugment.Root
            >
          </div>
        </div>
      {/if}
    </div>
  </SettingsCard>

  {#if config.showStatus}
    <div class="status-message {config.statusType}">
      {config.statusMessage}
    </div>
  {/if}
</div>

<style>
  .icon-container :global(svg) {
    fill: var(--icon-color, currentColor);
    height: 16px;
    aspect-ratio: 1/1;
  }
  .connect-button-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .connect-button-spinner {
    display: flex;
    align-items: center;
  }

  .status-controls {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
  }

  .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-container--success {
    color: var(--ds-color-success-11);
  }

  .connection-status {
    display: flex;
    align-items: center;
    position: relative;
  }

  .icon-button-wrapper {
    opacity: 0;
    transition: opacity 0.2s ease;
    padding-right: var(--ds-spacing-2);
  }

  .connection-status:hover .icon-button-wrapper,
  .icon-button-wrapper.active {
    opacity: 1;
  }

  .status-message {
    margin: var(--ds-spacing-2) 0;
    padding: var(--ds-spacing-2);
    border-radius: var(--ds-radius-2);
  }

  .status-message.error {
    background-color: var(--ds-color-error-3);
    color: var(--ds-color-error-11);
  }

  .status-message.info {
    background-color: var(--ds-color-info-3);
    color: var(--ds-color-info-11);
  }
</style>

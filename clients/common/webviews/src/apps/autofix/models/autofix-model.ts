import { type FileReader } from "$common-webviews/src/common/components/code-roll/types";
import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { host } from "$common-webviews/src/common/hosts/host";
import { createAsyncMessageFileReader } from "$common-webviews/src/common/utils/file-reader";
import {
  type AutofixIterationStage,
  type IConversationAutofixExtraData,
} from "$vscode/src/autofix/autofix-state";
import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
import {
  type AutofixPanelApplyAndRetestRequestMessage,
  type AutofixPanelDetailsInitRequestMessage,
  type AutofixPanelOpenSpecificStageMessage,
  WebViewMessageType,
  type EmptyMessage,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { type IDisposable } from "monaco-editor";

export class AutofixModel implements IDisposable {
  private _asyncMsgSender: AsyncMsgSender;
  private _latestData?: IConversationAutofixExtraData;
  private subscribers: Set<(chatHistory: AutofixModel) => void> = new Set();
  private _readFile: FileReader;

  constructor(
    private onOpenSpecificStage: (iterationId: string, stage: AutofixIterationStage) => void,
  ) {
    this._asyncMsgSender = new AsyncMsgSender((message) => host.postMessage(message));

    this._readFile = createAsyncMessageFileReader(this._asyncMsgSender);

    void this.initialize();
  }

  get latestData(): IConversationAutofixExtraData | undefined {
    return this._latestData;
  }

  get readFile(): FileReader {
    return this._readFile;
  }

  public dispose = (): void => {
    this.subscribers.clear();
  };

  private notifySubscribers = () => {
    this.subscribers.forEach((sub) => sub(this));
  };

  subscribe = (sub: (autofixModel: AutofixModel) => void): (() => void) => {
    this.subscribers.add(sub);
    sub(this);
    return () => {
      this.subscribers.delete(sub);
    };
  };

  private initialize = async (): Promise<void> => {
    const result = await this._asyncMsgSender.send<
      AutofixPanelDetailsInitRequestMessage,
      AutofixPanelOpenSpecificStageMessage
    >(
      {
        type: WebViewMessageType.autofixPanelDetailsInitRequest,
      },
      2000 /* 2 second timeout */,
    );

    this.onOpenSpecificStage(result.data.iterationId, result.data.stage);
  };

  private handlePartialOutput(iterationId: string, output: string) {
    this._latestData?.autofixIterations?.forEach((iteration) => {
      if (iteration.id === iterationId) {
        iteration.commandOutput = output;
      }
    });

    this.notifySubscribers();
  }

  public applyAndRetest = async (selectedSolutions: IEditSuggestion[]) => {
    await this._asyncMsgSender.send<AutofixPanelApplyAndRetestRequestMessage, EmptyMessage>(
      {
        type: WebViewMessageType.autofixPanelApplyAndRetestRequest,
        data: { selectedSolutions },
      },
      2000 /* 2 second timeout */,
    );
  };

  handleMessageFromExtension = async (e: MessageEvent<WebViewMessage>) => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.autofixPanelStateUpdate: {
        this._latestData = msg.data;
        this.notifySubscribers();
        break;
      }
      case WebViewMessageType.autofixPanelExecuteCommandPartialOutput: {
        this.handlePartialOutput(msg.data.iterationId, msg.data.output);
        break;
      }
      case WebViewMessageType.autofixPanelOpenSpecificStage:
        this.onOpenSpecificStage(msg.data.iterationId, msg.data.stage);
        break;
      case WebViewMessageType.empty: {
        break;
      }
      default:
        console.warn("AutofixModel got unexpected message: ", msg);
        break;
    }
  };
}

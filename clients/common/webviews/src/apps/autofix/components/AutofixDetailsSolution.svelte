<script lang="ts">
  import CodeRoll from "$common-webviews/src/common/components/code-roll/CodeRoll.svelte";
  import { type FileReader } from "$common-webviews/src/common/components/code-roll/types";
  import type {
    AutofixIteration,
    IConversationAutofixExtraData,
  } from "$vscode/src/autofix/autofix-state";
  import type { IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { createEventDispatcher } from "svelte";
  import AutofixDetailsFooter from "./AutofixDetailsFooter.svelte";
  export let iterationId: string;
  export let autofixData: IConversationAutofixExtraData;
  export let readFile: FileReader;

  const dispatch = createEventDispatcher<{ applyAndRetest: IEditSuggestion[] }>();

  let iteration: AutofixIteration | undefined;

  $: iteration = autofixData.autofixIterations?.filter((it) => it.id === iterationId)[0];
  $: suggestSolutions = iteration?.suggestedSolutions?.at(-1)?.replacements;
  $: currentIterationIsLatest = iterationId === autofixData.autofixIterations?.at(-1)?.id;
</script>

<div class="c-autofix-details-solution">
  <div class="c-autofix-details-solution-coderoll-container">
    {#if suggestSolutions}
      <CodeRoll suggestions={suggestSolutions} {readFile} loading={false} />
    {/if}
  </div>
  {#if currentIterationIsLatest}
    <AutofixDetailsFooter
      on:applyAndRetestClick={() =>
        suggestSolutions && dispatch("applyAndRetest", suggestSolutions)}
    />
  {/if}
</div>

<style>
  .c-autofix-details-solution {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .c-autofix-details-solution-coderoll-container {
    flex: 1;
  }
</style>

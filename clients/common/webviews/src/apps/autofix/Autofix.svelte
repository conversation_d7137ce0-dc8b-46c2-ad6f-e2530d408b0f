<script lang="ts">
  import { onDestroy } from "svelte";

  import {
    AutofixIterationStage,
    type IConversationAutofixExtraData,
  } from "$vscode/src/autofix/autofix-state";
  import AutofixDetailsHeader from "./components/AutofixDetailsHeader.svelte";
  import AutofixDetailsLog from "./components/AutofixDetailsLog.svelte";
  import AutofixDetailsSolution from "./components/AutofixDetailsSolution.svelte";
  import { AutofixModel } from "./models/autofix-model";
  import { AutofixDetailsHeaderTab } from "./models/types";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  let latestData: IConversationAutofixExtraData | undefined;
  let currentIterationId: string;
  let currentTab: AutofixDetailsHeaderTab = AutofixDetailsHeaderTab.testLog;

  const handleOpenSpecificStage = (iterationId: string, stage: AutofixIterationStage) => {
    currentIterationId = iterationId;
    currentTab =
      stage === AutofixIterationStage.runTest
        ? AutofixDetailsHeaderTab.testLog
        : AutofixDetailsHeaderTab.solution;
  };

  let autofixModel: AutofixModel = new AutofixModel(handleOpenSpecificStage);

  onDestroy(() => {
    autofixModel.dispose();
  });

  $: latestData = $autofixModel.latestData;
</script>

<svelte:window on:message={autofixModel?.handleMessageFromExtension} />

<MonacoProvider.Root>
  <div class="autofix-container">
    {#if latestData}
      <AutofixDetailsHeader autofixData={latestData} bind:currentIterationId bind:currentTab />

      {#if currentTab === AutofixDetailsHeaderTab.testLog}
        <AutofixDetailsLog iterationId={currentIterationId} autofixData={latestData} />
      {:else if currentTab === AutofixDetailsHeaderTab.solution}
        <AutofixDetailsSolution
          autofixData={latestData}
          iterationId={currentIterationId}
          readFile={autofixModel.readFile}
          on:applyAndRetest={(e) => autofixModel?.applyAndRetest(e.detail)}
        />
      {/if}
    {/if}
  </div>
</MonacoProvider.Root>

<style>
  .autofix-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }
</style>

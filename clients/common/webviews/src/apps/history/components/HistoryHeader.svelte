<script lang="ts">
  import { format } from "date-fns";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import { onKey } from "$common-webviews/src/common/utils/keypress";

  export let occuredAt: Date;
  export let requestID: string;
  export let pathName: string = "";
  export let repoRoot: string;
  export let prompt: string = "";
  export let others: string[] = [];

  function onRequestIDClick() {
    host.postMessage({
      type: WebViewMessageType.copyRequestID,
      data: requestID,
    });
  }

  function onFileClick() {
    host.postMessage({
      type: WebViewMessageType.openFile,
      data: {
        repoRoot: repoRoot,
        pathName: pathName,
      },
    });
  }
</script>

<div class="c-history-header">
  <div>{format(occuredAt, "p 'on' P")}</div>
  <ul>
    <li>
      <div
        class="c-history-header__link c-history-header--ellipsis"
        role="button"
        tabindex="0"
        on:click={onRequestIDClick}
        on:keydown={onKey("Enter", onRequestIDClick)}
      >
        Request ID: {requestID}
      </div>
    </li>
    {#if pathName}
      <li>
        <div
          class="c-history-header__link c-history-header--ellipsis-left"
          role="button"
          tabindex="0"
          on:click={onFileClick}
          on:keydown={onKey("Enter", onFileClick)}
        >
          &lrm;{pathName}
        </div>
      </li>
    {/if}
    {#if prompt}
      <li>
        <div class="c-history-header--ellipsis">Instruction: {prompt}</div>
      </li>
    {/if}
    {#each others as other}
      <li>
        <div class="c-history-header--ellipsis">
          {other}
        </div>
      </li>
    {/each}
  </ul>
</div>

<style>
  .c-history-header {
    line-height: 1.2rem;
  }
  .c-history-header ul {
    font-family: var(--monaco-monospace-font);
    list-style-type: "--";
    padding: 0;
    padding-left: 2ch;
    margin: 0;
    color: var(--vscode-textPreformat-foreground);
  }

  .c-history-header ul li {
    padding-left: 1ch;
  }

  .c-history-header__link {
    outline: none;
    cursor: pointer;
  }

  .c-history-header__link:focus,
  .c-history-header__link:hover {
    color: var(--vscode-textLink-activeForeground);
  }

  .c-history-header__link:hover {
    text-decoration: underline;
  }

  .c-history-header--ellipsis,
  .c-history-header--ellipsis-left {
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
  }

  .c-history-header--ellipsis-left {
    direction: rtl;
    text-align: left;
  }
</style>

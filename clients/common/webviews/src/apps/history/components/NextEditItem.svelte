<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import {
    ChangeType,
    type NextEditResultInfo,
    type IEditSuggestion,
  } from "$vscode/src/next-edit/next-edit-types";
  import HistoryHeader from "./HistoryHeader.svelte";
  import { onKey } from "$common-webviews/src/common/utils/keypress";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import RatingButton from "./RatingButton.svelte";
  import { getThankYouNote } from "../utils/thankyou-note";
  import { webviewState } from "../models/webview-state";
  import type { FeedbackState } from "../types/feedback-state";
  import { FeedbackRating } from "$vscode/src/types/feedback-rating";

  export let result: NextEditResultInfo;
  let lastClicked: IEditSuggestion | null = null;

  // Get the initial feedback state
  let feedbackState: FeedbackState = webviewState.getFeedback(result.requestId);
  let primaryIssue: string = "";
  let sendingFeedback: boolean = false;
  let thankYouNote: string = "";
  let thankYouTimeoutID: ReturnType<typeof setTimeout> | undefined;
  let previousRating: FeedbackRating;

  function handleMessage(e: MessageEvent<WebViewMessage>) {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.nextEditRatingDone: {
        const { requestId } = msg.data;
        if (requestId !== result.requestId) {
          return;
        }
        sendingFeedback = false;
        if (!msg.data.success) {
          feedbackState.selectedRating = previousRating;
          webviewState.setFeedback(requestId, feedbackState);
        }
        break;
      }
    }
  }

  function onFileClick(suggestion: IEditSuggestion) {
    host.postMessage({
      type: WebViewMessageType.openFile,
      data: {
        repoRoot: suggestion.qualifiedPathName.rootPath,
        pathName: suggestion.result.path,
        range: suggestion.lineRange,
        differentTab: true,
      },
    });
    lastClicked = suggestion;
  }

  function showThankYouNote() {
    thankYouNote = getThankYouNote();
    clearTimeout(thankYouTimeoutID);
    thankYouTimeoutID = setTimeout(() => {
      thankYouNote = "";
    }, 4000);
  }

  function handleRating(rating: FeedbackRating) {
    showThankYouNote();

    previousRating = feedbackState.selectedRating;
    if (rating !== FeedbackRating.unset) {
      feedbackState.selectedRating = rating;
    }

    // Prepend the primary issue to the feedback note if it is selected
    let note = feedbackState.feedbackNote;
    if (primaryIssue) {
      note = `${feedbackState.feedbackNote} #${primaryIssue}`;
    }

    webviewState.setFeedback(result.requestId, feedbackState);

    sendingFeedback = true;
    host.postMessage({
      type: WebViewMessageType.nextEditRating,
      data: {
        requestId: result.requestId,
        rating: rating,
        note: note.trim(),
      },
    });
  }

  function onNoteChange(e: CustomEvent) {
    const textarea = e.target as HTMLTextAreaElement;
    feedbackState.feedbackNote = textarea.value;
  }

  let changedLocations: IEditSuggestion[] = [];
  let unchangedLocations: IEditSuggestion[] = [];
  $: {
    changedLocations = result.suggestions.filter((s) => s.changeType !== ChangeType.noop);
    unchangedLocations = result.suggestions.filter((s) => s.changeType === ChangeType.noop);
  }
</script>

<svelte:window on:message={handleMessage} />

<div class="c-next-edit-item">
  <HistoryHeader
    occuredAt={result.occurredAt}
    requestID={result.requestId}
    repoRoot={result.qualifiedPathName?.rootPath ?? ""}
    others={[`Request type: ${result.mode}/${result.scope}`]}
  />

  <section>
    {#each changedLocations as item}
      <div
        class="c-completion-code-block"
        role="button"
        tabindex="0"
        on:click={() => onFileClick(item)}
        on:keydown={onKey("Enter", () => onFileClick(item))}
      >
        <!-- VScode displays line numbers as 1-based, so we add 1 to the start.
                     But we keep the end range so that the displayed ranges are inclusive. -->
        <pre data-language="plaintext"><code
            ><span
              class="c-next-edit-addition"
              class:c-next-edit-addition-clicked={lastClicked === item}
              >{item.qualifiedPathName.relPath}: {item.lineRange.start +
                (item.lineRange.start < item.lineRange.stop ? 1 : 0)}-{item.lineRange.stop}</span
            ></code
          ></pre>
      </div>
      <div>
        {item.result.changeDescription}
      </div>
      <section>
        original:
        <SimpleMonaco
          text={item.result.existingCode}
          pathName={item.qualifiedPathName.relPath}
          options={{ lineNumbers: "off" }}
        />
        modified:
        <SimpleMonaco
          text={item.result.suggestedCode}
          pathName={item.qualifiedPathName.relPath}
          options={{ lineNumbers: "off" }}
        />
      </section>
    {/each}
    <div class="c-next-edit-item__no-modifications">Unchanged locations:</div>
    {#each unchangedLocations as item}
      <div
        class="c-completion-code-block"
        role="button"
        tabindex="0"
        on:click={() => onFileClick(item)}
        on:keydown={onKey("Enter", () => onFileClick(item))}
      >
        <!-- VScode displays line numbers as 1-based, so we add 1 to the start.
                    But we keep the end range so that the displayed ranges are inclusive. -->
        <pre data-language="plaintext" class="c-next-edit-addition"><code
            ><span
              class="c-next-edit-addition"
              class:c-next-edit-addition-clicked={lastClicked === item}
              >{item.qualifiedPathName.relPath}: {item.lineRange.start +
                (item.lineRange.start < item.lineRange.stop ? 1 : 0)}-{item.lineRange.stop}</span
            ></code
          ></pre>
      </div>
    {/each}
  </section>

  <vscode-text-area
    rows="3"
    placeholder="Add feedback on these suggestions..."
    resize="none"
    value={feedbackState.feedbackNote}
    on:input={onNoteChange}
  ></vscode-text-area>

  <div class="c-completion-item__ratings">
    <RatingButton
      selected={feedbackState.selectedRating}
      rating={FeedbackRating.negative}
      disabled={sendingFeedback}
      click={() => handleRating(FeedbackRating.negative)}
    />
    <RatingButton
      selected={feedbackState.selectedRating}
      rating={FeedbackRating.positive}
      disabled={sendingFeedback}
      click={() => handleRating(FeedbackRating.positive)}
    />
    <div class="c-completion-item__thankyou">
      {thankYouNote}
    </div>
    <RatingButton
      rating={FeedbackRating.unset}
      click={() => handleRating(FeedbackRating.unset)}
      disabled={sendingFeedback}
      hide={!(feedbackState.feedbackNote.trim().length > 0)}
    />
  </div>
</div>

<style>
  .c-next-edit-item {
    display: flex;
    flex-direction: column;
    color: var(--vscode-descriptionForeground);
    gap: var(--p-2);
    margin: var(--p-4) 0;
  }

  .c-next-edit-item__no-modifications {
    gap: var(--p-2);
    margin: var(--p-4) 0;
  }

  .c-next-edit-addition {
    margin: 0;
    color: var(--vscode-gitDecoration-addedResourceForeground);
  }

  .c-next-edit-addition-clicked {
    color: var(--vscode-gitDecoration-modifiedResourceForeground);
  }
</style>

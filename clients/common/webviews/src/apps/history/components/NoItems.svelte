<script lang="ts">
  import { type LocalCompletionRequest } from "../types/local-history-completion";
  import CompletionItem from "./CompletionItem.svelte";

  const exampleCompletion: LocalCompletionRequest = {
    occuredAt: new Date(),
    requestId: "12345678-1234-1234-1234-123456789123",
    repoRoot: "/home/<USER>/projects/example-project",
    pathName: "src/example.js",
    prefix: "co",
    completions: [
      {
        text: 'nsole.log("Hello World.");',
        skippedSuffix: "",
        suffixReplacementText: "",
      },
    ],
    suffix: "\n\n",
  };
</script>

<div class="l-no-items">
  <div class="l-no-items__msg">
    <h2>History.</h2>
    <p>
      As you use Augment, we'll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.
    </p>

    <p>
      Below is an example of the information and feedback form we'll display for each suggestion.
    </p>
  </div>

  <div class="l-no-items__divider"></div>

  <div class="l-no-items__example">
    <CompletionItem completion={exampleCompletion} demo={true} />
  </div>
</div>

<style>
  .l-no-items {
    display: grid;

    grid-template-rows: auto auto 1fr;
    grid-template-columns: 1fr;

    gap: 0px;
    height: 100%;

    flex: 1;
  }
  .l-no-items__divider {
    height: 1px;
    background-color: var(--augment-border-color);
    margin: var(--p-2) 0;
  }

  .l-no-items__msg {
    max-width: 340px;
  }

  .l-no-items__example {
    overflow: hidden;
  }
</style>

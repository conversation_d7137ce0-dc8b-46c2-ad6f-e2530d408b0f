<script lang="ts">
  import RatingButton from "./RatingButton.svelte";
  import { getThankYouNote } from "../utils/thankyou-note";
  import { FeedbackRating } from "$vscode/src/types/feedback-rating";
  import HistoryHeader from "./HistoryHeader.svelte";
  import CompletionCodeBlock from "./CompletionCodeBlock.svelte";
  import type { FeedbackState } from "../types/feedback-state";
  import { webviewState } from "../models/webview-state";
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    type WebViewMessage,
    WebViewMessageType,
  } from "$vscode/src/webview-providers/webview-messages";
  import type { LocalCompletionRequest } from "../types/local-history-completion";
  import TextAreaAugment from "$common-webviews/src/design-system/components/TextAreaAugment.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let completion: LocalCompletionRequest;
  export let debug: boolean = false;
  export let demo: boolean = false;

  // Get the initial feedback state
  let feedbackState: FeedbackState = webviewState.getFeedback(completion.requestId);
  let primaryIssue: string = "";
  let sendingFeedback: boolean = false;
  let thankYouNote: string = "";
  let thankYouTimeoutID: ReturnType<typeof setTimeout> | undefined;
  let previousRating: FeedbackRating;

  function handleMessage(e: MessageEvent<WebViewMessage>) {
    if (demo) {
      return;
    }

    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.completionRatingDone: {
        const { requestId } = msg.data;
        if (requestId !== completion.requestId) {
          return;
        }
        sendingFeedback = false;
        if (!msg.data.success) {
          feedbackState.selectedRating = previousRating;
          webviewState.setFeedback(requestId, feedbackState);
        }
        break;
      }
    }
  }

  function showThankYouNote() {
    thankYouNote = getThankYouNote();
    clearTimeout(thankYouTimeoutID);
    thankYouTimeoutID = setTimeout(() => {
      thankYouNote = "";
    }, 4000);
  }

  function handleRating(rating: FeedbackRating) {
    showThankYouNote();

    previousRating = feedbackState.selectedRating;
    if (rating !== FeedbackRating.unset) {
      feedbackState.selectedRating = rating;
    }

    if (demo) {
      return;
    }

    // Prepend the primary issue to the feedback note if it is selected
    let note = feedbackState.feedbackNote;
    if (primaryIssue) {
      note = `${feedbackState.feedbackNote} #${primaryIssue}`;
    }

    webviewState.setFeedback(completion.requestId, feedbackState);

    sendingFeedback = true;
    host.postMessage({
      type: WebViewMessageType.completionRating,
      data: {
        requestId: completion.requestId,
        rating: rating,
        note: note.trim(),
      },
    });
  }

  function onPrimaryIssueChange(e: string) {
    primaryIssue = e;
    closeDropdownMenu();
  }

  let closeDropdownMenu = () => {};
</script>

<svelte:window on:message={handleMessage} />

<div class="c-completion-item" class:c-completion-item--sending-feedback={sendingFeedback}>
  <HistoryHeader
    occuredAt={completion.occuredAt}
    requestID={completion.requestId}
    pathName={completion.pathName}
    repoRoot={completion.repoRoot}
  />

  {#each completion.completions as item}
    <CompletionCodeBlock completion={item} prefix={completion.prefix} suffix={completion.suffix} />
  {/each}

  {#if debug}
    <DropdownMenu.Root bind:requestClose={closeDropdownMenu} size={1}>
      <DropdownMenu.Trigger>
        <ButtonAugment size={1}>Primary Issue</ButtonAugment>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content>
        <DropdownMenu.Item onSelect={() => onPrimaryIssueChange("")}
          >(Optional) Primary issue</DropdownMenu.Item
        >
        <DropdownMenu.Item onSelect={() => onPrimaryIssueChange("bad-formatting")}
          >Bad formatting</DropdownMenu.Item
        >
        <DropdownMenu.Item onSelect={() => onPrimaryIssueChange("hallucination")}
          >Hallucination</DropdownMenu.Item
        >
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  {/if}

  <TextAreaAugment
    rows="3"
    placeholder="Add feedback on this completion..."
    resize="none"
    bind:value={feedbackState.feedbackNote}
  />

  <div class="c-completion-item__ratings">
    <RatingButton
      selected={feedbackState.selectedRating}
      rating={FeedbackRating.negative}
      disabled={sendingFeedback}
      click={() => handleRating(FeedbackRating.negative)}
    />
    <RatingButton
      selected={feedbackState.selectedRating}
      rating={FeedbackRating.positive}
      disabled={sendingFeedback}
      click={() => handleRating(FeedbackRating.positive)}
    />
    <div class="c-completion-item__thankyou">
      {thankYouNote}
    </div>
    <RatingButton
      rating={FeedbackRating.unset}
      click={() => handleRating(FeedbackRating.unset)}
      disabled={sendingFeedback}
      hide={!(feedbackState.feedbackNote.trim().length > 0)}
    />
  </div>
</div>

<style>
  .c-completion-item {
    display: flex;
    flex-direction: column;
    color: var(--vscode-descriptionForeground);
    gap: var(--p-2);
    margin: var(--p-4) 0;
  }

  .c-completion-item__ratings {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--p-4);
  }

  .c-completion-item__thankyou {
    text-align: right;
    flex: 1;
  }
</style>

<script lang="ts">
  import type { HistoryAugmentCompletion } from "$vscode/src/webview-providers/webview-messages";
  import Monaco from "$common-webviews/src/design-system/components/MonacoProvider/Monaco.svelte";
  import { Range, editor } from "monaco-editor";
  import { onD<PERSON><PERSON> } from "svelte";
  import { MONACO_OPTIONS } from "../utils/monacto-opts";

  export let prefix: string;
  export let suffix: string;
  export let completion: HistoryAugmentCompletion;

  const MAX_LINES_FOR_PREFIX_AND_SUFFIX = 6;

  // trimPrefix removes any empty lines at the start of the prefix.
  function trimPrefix(prefix: string): string {
    const lines = prefix.split("\n").slice(-MAX_LINES_FOR_PREFIX_AND_SUFFIX);
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim().length > 0) {
        return lines.slice(i).join("\n");
      }
    }
    // All lines were empty
    return "";
  }

  // trimSuffix removes any empty lines at the end of the suffix.
  function trimSuffix(suffix: string): string {
    const lines = suffix.split("\n").slice(0, MAX_LINES_FOR_PREFIX_AND_SUFFIX);
    for (let i = lines.length - 1; i >= 0; i--) {
      if (lines[i].trim().length > 0) {
        return lines.slice(0, i + 1).join("\n");
      }
    }
    // All lines were empty
    return "";
  }

  function hasSkippedSuffix(suffix: string, skippedSuffix: string): boolean {
    return !!skippedSuffix && suffix.indexOf(skippedSuffix) === 0;
  }

  // Remove the skipped suffix from the suffix if defined.
  function removeSkippedSuffix(suffix: string, skippedSuffix: string): string {
    if (!skippedSuffix) {
      return suffix;
    }
    if (suffix.indexOf(skippedSuffix) !== 0) {
      return suffix;
    }
    return suffix.slice(skippedSuffix.length);
  }

  const trimmedPrefix = trimPrefix(prefix);

  // The extension does some handling of skipped suffixes that means we
  // may not necessarily use it, so we have to check if it's used rather than
  // just using it.
  const useSkippedSuffix = hasSkippedSuffix(suffix, completion.skippedSuffix);
  const untrimmedSuffix = useSkippedSuffix
    ? removeSkippedSuffix(suffix, completion.skippedSuffix)
    : suffix;
  const trimmedSuffix = trimSuffix(untrimmedSuffix);
  const completeText = completion.text;
  const skippedText = useSkippedSuffix ? completion.skippedSuffix : "";
  const suffixReplacementText = completion.suffixReplacementText;

  const allCode =
    trimmedPrefix + completeText + skippedText + suffixReplacementText + trimmedSuffix;

  const model = editor.createModel(allCode, "plaintext");

  const prefixStart = model.getPositionAt(0);
  const prefixEnd = model.getPositionAt(trimmedPrefix.length);

  const completionTextStart = model.getPositionAt(trimmedPrefix.length);
  const completionTextEnd = model.getPositionAt(trimmedPrefix.length + completeText.length);

  const skippedStart = model.getPositionAt(trimmedPrefix.length + completeText.length);
  const skippedEnd = model.getPositionAt(
    trimmedPrefix.length + completeText.length + skippedText.length,
  );

  const suffixReplacementStart = model.getPositionAt(
    trimmedPrefix.length + completeText.length + skippedText.length,
  );
  const suffixReplacementEnd = model.getPositionAt(
    trimmedPrefix.length + completeText.length + skippedText.length + suffixReplacementText.length,
  );

  const suffixStart = model.getPositionAt(
    trimmedPrefix.length + completeText.length + skippedText.length + suffixReplacementText.length,
  );
  const suffixEnd = model.getPositionAt(allCode.length);

  const decorations = [
    // Prefix
    {
      range: new Range(
        prefixStart.lineNumber,
        prefixStart.column,
        prefixEnd.lineNumber,
        prefixEnd.column,
      ),
      options: {
        inlineClassName: "c-completion-code-block--dull",
      },
    },
    // Suffix
    {
      range: new Range(
        suffixStart.lineNumber,
        suffixStart.column,
        suffixEnd.lineNumber,
        suffixEnd.column,
      ),
      options: {
        inlineClassName: "c-completion-code-block--dull",
      },
    },
    // Completion Text
    {
      range: new Range(
        completionTextStart.lineNumber,
        completionTextStart.column,
        completionTextEnd.lineNumber,
        completionTextEnd.column,
      ),
      options: {
        inlineClassName: "c-completion-code-block--addition",
      },
    },
    // Suffix Replacement Text
    {
      range: new Range(
        suffixReplacementStart.lineNumber,
        suffixReplacementStart.column,
        suffixReplacementEnd.lineNumber,
        suffixReplacementEnd.column,
      ),
      options: {
        inlineClassName: "c-completion-code-block--addition",
      },
    },
    // Skipped
    {
      range: new Range(
        skippedStart.lineNumber,
        skippedStart.column,
        skippedEnd.lineNumber,
        skippedEnd.column,
      ),
      options: {
        inlineClassName: "c-completion-code-block--strikethrough",
      },
    },
  ];

  onDestroy(() => {
    model?.dispose();
  });
</script>

<div class="c-completion-code-block">
  <Monaco options={MONACO_OPTIONS} {model} {decorations} />
</div>

<style>
  :global(.c-completion-code-block .monaco-editor .c-completion-code-block--dull) {
    color: #768390;
  }
  :global(.c-completion-code-block .monaco-editor .c-completion-code-block--addition) {
    color: var(--vscode-gitDecoration-addedResourceForeground);
  }
  :global(.c-completion-code-block .monaco-editor .c-completion-code-block--strikethrough) {
    text-decoration: line-through;
    color: var(--vscode-gitDecoration-deletedResourceForeground);
  }
</style>

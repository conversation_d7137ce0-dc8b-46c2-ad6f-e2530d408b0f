<script lang="ts">
  import { FeedbackRating } from "$vscode/src/types/feedback-rating";
  import MaterialIcon from "$common-webviews/src/common/components/MaterialIcon.svelte";

  export let selected: FeedbackRating | undefined = undefined;
  export let rating: FeedbackRating;
  export let disabled: boolean = false;
  export let hide: boolean = false;
  export let click: (rating: FeedbackRating) => void;

  let title: string;
  let icon: string;
  let className: string;
  switch (rating) {
    case FeedbackRating.negative:
      title = "Report completion as bad";
      icon = "thumb_down";
      className = "c-rating-btn--negative";
      break;
    case FeedbackRating.positive:
      title = "Report completion as good";
      icon = "thumb_up";
      className = "c-rating-btn--positive";
      break;
    case FeedbackRating.unset:
      title = "Send feedback about this completion";
      icon = "send";
      className = "c-rating-btn--send";
      break;
  }
</script>

<button
  class="c-rating-btn {className}"
  class:c-rating-btn--selected={selected === rating}
  class:c-rating-btn--hide={hide}
  {title}
  {disabled}
  on:click={() => click(rating)}
>
  <MaterialIcon iconName={icon} fill={selected === rating} class="c-rating-btn__icon" />
</button>

<style>
  .c-rating-btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;

    width: 48px;
    aspect-ratio: 1;
    padding: 0;
    border: none;
    border-radius: 100%;
    color: black;
    cursor: pointer;
  }

  .c-rating-btn :global(.c-rating-btn__icon) {
    font-size: 1.2rem;
  }

  .c-rating-btn:disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  .c-rating-btn--hide {
    display: none;
  }

  /* Colors from: https://colorkit.co/color-shades-generator/f35959/ */
  .c-rating-btn--negative {
    background: #973434;
  }
  .c-rating-btn--negative:hover {
    background: #b44040;
  }
  .c-rating-btn--negative:active,
  .c-rating-btn--negative.c-rating-btn--selected {
    background: #f35959;
  }

  /* Colors from: https://colorkit.co/color-shades-generator/b5d46e/ */
  .c-rating-btn--positive {
    background: #6f8341;
  }
  .c-rating-btn--positive:hover {
    background: #869d50;
  }
  .c-rating-btn--positive:active,
  .c-rating-btn--positive.c-rating-btn--selected {
    background: #b5d46e;
  }

  /* Colors from: https://colorkit.co/color-shades-generator/868686/ */
  .c-rating-btn--send {
    background: #868686;
  }

  .c-rating-btn--send:hover {
    background: #9d9d9d;
  }
</style>

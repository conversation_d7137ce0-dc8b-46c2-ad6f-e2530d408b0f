<script lang="ts">
  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    WebViewMessageType,
    type WebViewMessage,
    type HistoryCompletionRequest,
    type HistoryConfig,
  } from "$vscode/src/webview-providers/webview-messages";
  import { parseISO } from "date-fns";
  import CompletionItem from "./components/CompletionItem.svelte";
  import NoItems from "./components/NoItems.svelte";
  import InstructionItem from "./components/InstructionItem.svelte";
  import { webviewState } from "./models/webview-state";
  import type { LocalCompletionRequest } from "./types/local-history-completion";
  import NextEditItem from "./components/NextEditItem.svelte";
  import type { NextEditResultInfo } from "$vscode/src/next-edit/next-edit-types";
  import type { AugmentInstruction } from "$vscode/src/code-edit-types";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  type InternalEditSuggestions = {
    requestId: string; // We need a request id at the top-level.
    occuredAt: Date; // We need to insert the typo back.
    result: NextEditResultInfo;
  };

  let completions: { [requestId: string]: LocalCompletionRequest } = {};
  let instructions: { [requestId: string]: AugmentInstruction } = {};
  let nextEdits: { [requestId: string]: InternalEditSuggestions } = {};
  let config: HistoryConfig | undefined;

  function appendCompletions(newCompletions: HistoryCompletionRequest[]) {
    for (const c of newCompletions) {
      if (completions[c.requestId]) {
        continue;
      }

      completions[c.requestId] = {
        ...c,
        occuredAt: parseISO(c.occuredAt),
      };
    }
    webviewState.cleanupFeedback(completions);
  }

  function appendInstructions(newInstructions: any[]) {
    for (const e of newInstructions) {
      if (instructions[e.requestId]) {
        continue;
      }

      // When the date is posted to the webview, it's converted
      // to a string, so we need to convert it back to a date.
      if (typeof e.occuredAt === "string") {
        const dateString = e.occuredAt as unknown as string;
        e.occuredAt = parseISO(dateString);
      }

      instructions[e.requestId] = {
        ...e,
      };
    }
  }

  function appendNextEdits(allResults: NextEditResultInfo[]) {
    for (const result of allResults) {
      if (result.suggestions.length === 0) {
        continue;
      }
      // Overwrite previous entries with the same request id for streaming.
      nextEdits[result.requestId] = {
        requestId: result.requestId,
        occuredAt: parseISO(result.occurredAt as unknown as string),
        result,
      };
    }
  }

  function handleMessage(e: MessageEvent<WebViewMessage>) {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.historyInitialize:
        config = msg.data.config;
        appendInstructions(msg.data.instructions);
        appendCompletions(msg.data.completionRequests);
        appendNextEdits(msg.data.nextEdits);
        break;
      case WebViewMessageType.completions:
        appendCompletions(msg.data);
        break;
      case WebViewMessageType.instructions:
        appendInstructions(msg.data);
        break;
      case WebViewMessageType.nextEditSuggestions:
        appendNextEdits([msg.data]);
        break;
      case WebViewMessageType.historyConfig:
        config = msg.data;
        break;
    }
  }

  host.postMessage({
    type: WebViewMessageType.historyLoaded,
  });

  let sortedItems: (LocalCompletionRequest | AugmentInstruction | InternalEditSuggestions)[] = [];
  $: sortedItems = [
    ...Object.values(instructions),
    ...Object.values(completions),
    ...Object.values(nextEdits),
  ].sort((a, b) => b.occuredAt.getTime() - a.occuredAt.getTime());

  function asAugmentInstruction(
    itm: LocalCompletionRequest | AugmentInstruction | InternalEditSuggestions,
  ): AugmentInstruction {
    if (!("prompt" in itm)) {
      throw new Error(`wrong type`);
    }
    if ("completions" in itm) {
      throw new Error(`wrong type`);
    }
    return itm;
  }
</script>

<svelte:window on:message={handleMessage} />

<MonacoProvider.Root>
  <main class="l-items-list">
    {#if !sortedItems.length}
      <NoItems />
    {:else}
      {#each sortedItems as item (item.requestId)}
        {#if "completions" in item}
          <CompletionItem
            debug={config?.enableDebugFeatures || config?.enableReviewerWorkflows}
            completion={item}
          />
        {:else if "prompt" in item}
          <InstructionItem instruction={asAugmentInstruction(item)} />
        {:else if "result" in item}
          <NextEditItem result={item.result} />
        {/if}

        <div class="l-items-list__divider"></div>
      {/each}
    {/if}
  </main>
</MonacoProvider.Root>

<style>
  .l-items-list {
    display: flex;
    flex-direction: column;
    max-width: 680px;
    width: 100%;
    margin: 0 auto;
  }

  .l-items-list__divider {
    height: 1px;
    background-color: var(--augment-border-color);
    margin: var(--p-2) 0;
  }
</style>

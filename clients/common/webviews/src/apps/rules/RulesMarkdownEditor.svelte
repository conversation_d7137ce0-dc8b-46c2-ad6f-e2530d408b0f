<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import OpenFileButton from "../chat/components/conversation/blocks/tools/components/OpenFileButton.svelte";
  import { ExtensionClient } from "../chat/extension-client";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { ChatFlagsModel } from "../chat/models/chat-flags-model";
  import MarkdownEditor from "$common-webviews/src/design-system/components/MarkdownEditor.svelte";
  import { RulesParser } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
  import RulesDropdown from "./RulesDropdown.svelte";

  export let text: string;
  export let path: string;

  // Create extension client directly
  const msgBroker = new MessageBroker(host);
  const flagsModel = new ChatFlagsModel();
  const extensionClient = new ExtensionClient(host, msgBroker, flagsModel);

  export let alwaysApply: boolean;

  const onSave = async (_alwaysApply: boolean) => {
    const newText = RulesParser.updateAlwaysApplyFrontmatterKey(text, _alwaysApply);
    extensionClient.saveFile({
      repoRoot: "",
      pathName: path,
      content: newText,
    });
  };
</script>

<MarkdownEditor
  bind:value={text}
  saveFunction={() => onSave(alwaysApply)}
  variant="surface"
  size={2}
  resize="vertical"
  class="markdown-editor"
>
  <div class="l-file-controls" slot="header">
    <div class="l-file-controls-left">
      <div class="c-dropdown-with-label">
        <TextAugment size={1}>Rules Trigger Mode:</TextAugment>
        <RulesDropdown {onSave} {alwaysApply} />
      </div>
    </div>
    <OpenFileButton
      size={1}
      {path}
      onOpenLocalFile={async () => {
        extensionClient.openFile({
          repoRoot: "",
          pathName: path,
        });
        return "success";
      }}
    >
      <TextAugment slot="text" size={1}>Open file</TextAugment>
    </OpenFileButton>
  </div>
</MarkdownEditor>

<style>
  .l-file-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-2);
    min-height: 32px;
    flex-shrink: 0; /* Prevent the controls from shrinking */
  }
  .l-file-controls-left {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
  }
  .c-dropdown-with-label {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }
</style>

<script lang="ts">
  import { type Readable } from "svelte/store";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";

  export let onSave: (value: boolean) => void;
  export let alwaysApply: boolean;

  // Dropdown options
  const alwaysOption = {
    label: "Always",
    description:
      "Rules will always be followed, though individual context will still be respected.",
  };
  const manualOption = {
    label: "Manual",
    description:
      "Rules will only be followed when manually triggered. You can trigger them from the prompt by @ tagging the active file.",
  };

  const dropdownOptions = [alwaysOption, manualOption] as const;
  $: selectedOption = alwaysApply ? alwaysOption : manualOption;

  type DropdownOption = (typeof dropdownOptions)[number];
  // Track text selection state
  let focusedIndex: Readable<number | undefined> | undefined = undefined;
  let requestClose: () => void = () => {};

  // Handle dropdown selection
  function handleDropdownSelect(option: DropdownOption): void {
    alwaysApply = option.label === "Always";
    // Save the updated text
    onSave(alwaysApply);
    requestClose();
  }
</script>

<DropdownMenuAugment.Root bind:requestClose bind:focusedIndex>
  <DropdownMenuAugment.Trigger>
    <ButtonAugment color={alwaysApply ? "accent" : "neutral"} size={1}
      >{alwaysApply ? "Always" : "Manual"}
      <ChevronDown slot="iconRight" />
    </ButtonAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content side="bottom" align="start">
    {#each dropdownOptions as option}
      <DropdownMenuAugment.Item
        onSelect={() => handleDropdownSelect(option)}
        highlight={selectedOption.label === option.label}
      >
        {option.label}
      </DropdownMenuAugment.Item>
    {/each}
    {#if $focusedIndex !== undefined || selectedOption}
      <DropdownMenuAugment.Separator />
      <DropdownMenuAugment.Label>
        {$focusedIndex !== undefined
          ? dropdownOptions[$focusedIndex].description
          : selectedOption.description}
      </DropdownMenuAugment.Label>
    {/if}
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>

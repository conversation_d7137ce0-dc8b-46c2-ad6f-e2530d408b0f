<script lang="ts">
  import KeyboardShortcutHint from "$common-webviews/src/common/components/KeyboardShortcutHint.svelte";
  import type { DiffViewModel } from "../models/diff-view-model";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import ClipboardCopy from "$common-webviews/src/design-system/icons/clipboard-copy.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  export let diffViewModel: DiffViewModel;

  const keybindingsStore = diffViewModel.keybindings;
  const requestId = diffViewModel.requestId;
  $: disableResolution = $diffViewModel.disableResolution;
  $: disableApply = $diffViewModel.disableApply;

  let userReadableCurrentChunkIdx: string = "x";
  $: if ($diffViewModel.currFocusedChunkIdx !== undefined) {
    userReadableCurrentChunkIdx = ($diffViewModel.currFocusedChunkIdx + 1).toString();
  } else {
    userReadableCurrentChunkIdx = "x";
  }
  $: isLoadingStore = $diffViewModel.isLoading;
  $: hasLeaves = !!$diffViewModel.leaves?.length;

  let tooltipTimer: ReturnType<typeof setTimeout> | undefined = undefined;
  let tooltipText = "Copy request ID";
  let requestClose: () => void = () => {};

  function onOpenChange(open: boolean) {
    if (!open) {
      clearTimeout(tooltipTimer);
      tooltipTimer = undefined;
      tooltipText = "Copy request ID";
    }
  }

  async function copyRequestId() {
    if (!$requestId) return;
    await navigator.clipboard.writeText($requestId);
    tooltipText = "Copied!";
    clearTimeout(tooltipTimer);
    tooltipTimer = setTimeout(requestClose, 1500);
  }
</script>

<div class="c-top-action-panel-anchor">
  <div class="c-button-container">
    {#if $requestId}
      <TextTooltipAugment
        bind:requestClose
        {onOpenChange}
        content={tooltipText}
        triggerOn={[TooltipTriggerOn.Hover]}
      >
        <IconButtonAugment variant="ghost" color="neutral" size={1} on:click={copyRequestId}>
          <ClipboardCopy />
        </IconButtonAugment>
      </TextTooltipAugment>
    {/if}
    {#if !hasLeaves && $isLoadingStore}
      <span class="c-diff-page-counter">
        Generating changes
        <SpinnerAugment size={1} loading={$isLoadingStore} />
      </span>
    {:else if hasLeaves}
      <span class="c-diff-page-counter">
        {userReadableCurrentChunkIdx} of {$diffViewModel?.leaves?.length}
        <SpinnerAugment size={1} loading={$isLoadingStore} />
      </span>
    {:else}
      <span class="c-diff-page-counter">No changes</span>
    {/if}
    {#if hasLeaves}
      <ButtonAugment
        size={1}
        variant="ghost"
        color="neutral"
        on:click={diffViewModel.focusPrevChunk}
      >
        <KeyboardShortcutHint keybinding={$keybindingsStore.focusPrevChunk} />
        Back
      </ButtonAugment>
      <ButtonAugment
        size={1}
        variant="ghost"
        color="neutral"
        on:click={diffViewModel.focusNextChunk}
      >
        <KeyboardShortcutHint keybinding={$keybindingsStore.focusNextChunk} />
        Next
      </ButtonAugment>
      {#if !$disableResolution}
        {#if !$disableApply}
          <ButtonAugment
            size={1}
            variant="ghost"
            color="success"
            on:click={diffViewModel.acceptAllChunks}
          >
            <KeyboardShortcutHint keybinding={$keybindingsStore.acceptAllChunks} />
            Accept All
          </ButtonAugment>
        {/if}
        <ButtonAugment
          size={1}
          variant="ghost"
          color="error"
          on:click={diffViewModel.rejectAllChunks}
        >
          <KeyboardShortcutHint keybinding={$keybindingsStore.rejectAllChunks} />
          Reject All
        </ButtonAugment>
      {/if}
    {/if}
  </div>
</div>

<style>
  .c-top-action-panel-anchor {
    width: 100vw;
    z-index: var(--z-diff-view-top-actions);
  }

  .c-button-container {
    width: 100vw;

    gap: var(--ds-spacing-1);
    /* This centers the container horizontally */
    position: relative;
    background-color: var(--ds-color-neutral-a2);
    display: flex;
    flex-direction: row;

    /* Center the buttons vertically and horizontally */
    align-items: center;
    justify-content: center;

    padding: var(--ds-spacing-1) 0;
    border-bottom: 1px solid var(--augment-border-color);
    overflow: hidden;
  }

  .c-diff-page-counter {
    display: inline-flex;
    align-items: center;
    font-weight: 500;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1);
  }

  .c-diff-page-counter {
    text-wrap: nowrap;
  }
</style>

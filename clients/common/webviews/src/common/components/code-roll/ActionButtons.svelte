<script lang="ts" generics="T extends string, V extends ActionValue<T>">
  import { stop } from "./code-roll-util";
  import type { ActionConfig, ActionValue, OnAction } from "./types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  export let actions: ActionConfig<T>[];
  export let onAction: OnAction<V>;
  export let value: V;
  export let compact = true;
</script>

<div class="c-action-buttons">
  {#each actions as action}
    {#if action === "|"}
      <div class="c-action-buttons__separator"></div>
    {:else}
      <IconButtonAugment
        variant="ghost"
        color="neutral"
        size={compact ? 1 : 2}
        title={action.label}
        on:click={stop(onAction, action.action, value)}
      >
        <i
          class="c-action-buttons__icon"
          style="--augment-action-button-icon-url:url({JSON.stringify(action.icon)})"
        />
      </IconButtonAugment>
    {/if}
  {/each}
</div>

<style>
  .c-action-buttons {
    display: flex;
    flex-direction: row;
    gap: 0;
    align-items: center;
    height: 100%;
  }
  .c-action-buttons__icon {
    display: inline-block;
    width: 16px;
    min-width: 16px;
    aspect-ratio: 1 / 1;
    mask-image: var(--augment-action-button-icon-url);
    mask-repeat: no-repeat;
    mask-position: center;
    background-color: currentColor;
  }
  .c-action-buttons :global(.c-icon-btn) {
    display: flex;
    flex-direction: row;
    gap: 0;
    width: unset;
    align-items: center;
    justify-content: center;
    height: 100%;
    aspect-ratio: 1 / 1;
    cursor: pointer;
    padding: 1px;
  }

  .c-action-buttons__separator {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.25);
    height: 1em;
    width: 1px;
    margin: 0 0.25em;
  }
  .c-action-buttons.c-action-buttons :global(.c-icon-btn .c-base-btn) {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0 1px;
    --base-btn-hover-bg-color: rgba(255, 255, 255, 0.3);
    --base-btn-active-bg-color: rgba(255, 255, 255, 0.7);
  }
</style>

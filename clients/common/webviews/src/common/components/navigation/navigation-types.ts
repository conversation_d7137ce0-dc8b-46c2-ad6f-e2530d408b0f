import type { ComponentType, SvelteComponent } from "svelte";

export type Props = Record<string, unknown>;

type NavigationItemBase = {
  name: string;
  description: string;
  icon: ComponentType<SvelteComponent>;
  /** An ID used for scrolling to the item */
  id: string;
  group?: string;
  header?: boolean;
};

export type WithComponent<T extends Props> = NavigationItemBase & {
  component: ComponentType<SvelteComponent<T>>;
  props: T;
};

export type NavigationItem<T extends Props | undefined = undefined> = T extends Props
  ? WithComponent<T>
  : NavigationItemBase;

export function isNavigationItem<T extends Props | undefined = undefined>(
  item: unknown,
): item is NavigationItem<T> {
  return isProps(item) && "name" in item;
}

export function isWithComponent<T extends Props>(item: unknown): item is WithComponent<T> {
  return isNavigationItem(item) && "component" in item;
}

export function isProps(item: unknown): item is Props {
  switch (typeof item) {
    case "object":
      return item != null;
    case "function":
      return true;
    default:
      return false;
  }
}

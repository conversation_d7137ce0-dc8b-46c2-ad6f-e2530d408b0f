<script lang="ts">
  export let color: string;
</script>

<span class="color-preview">
  {color}
  <span class="color-square" style:background-color={color} />
</span>

<style>
  .color-preview {
    display: inline-flex;
    align-items: center;
    gap: 0.25em;
  }

  .color-square {
    display: inline-block;
    width: 1em;
    height: 1em;
    border: 1px solid var(--vscode-editor-foreground);
  }
</style>

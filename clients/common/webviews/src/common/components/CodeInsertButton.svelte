<script lang="ts">
  import type {
    ButtonSize,
    ButtonVariant,
    ButtonColor,
  } from "$common-webviews/src/design-system/_primitives/BaseButton.svelte";
  import SuccessfulButton from "$common-webviews/src/apps/chat/components/buttons/SuccessfulButton.svelte";
  import FilePlus from "$common-webviews/src/design-system/icons/file-plus.svelte";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import Diff from "$common-webviews/src/design-system/icons/diff.svelte";
  import { type ButtonState } from "$common-webviews/src/apps/chat/components/buttons/types";
  import { CodeblockActionType } from "$common-webviews/src/apps/chat/components/markdown-ext/codeblock/types";
  export let size: ButtonSize = 1;
  export let codeblockActionType:
    | CodeblockActionType.directApply
    | CodeblockActionType.insert
    | CodeblockActionType.smartPaste;
  export let variant: ButtonVariant = "ghost-block";
  export let color: ButtonColor = "neutral";
  export let successText: string | undefined = undefined;
  export let tooltip = "Insert";
  export let stickyColor = false;
  export let onClick: () => Promise<ButtonState> | ButtonState | undefined;
  let loading = false;
</script>

<span class="c-code-insert-button">
  <SuccessfulButton
    defaultColor={color}
    {size}
    {variant}
    {loading}
    {stickyColor}
    tooltip={{ neutral: tooltip, success: successText }}
    stateVariant={{ success: "soft", neutral: "ghost-block", failure: "soft" }}
    {onClick}
  >
    <svelte.fragment slot="iconLeft">
      {#if codeblockActionType === CodeblockActionType.insert}
        <FilePlus />
      {:else if codeblockActionType === CodeblockActionType.smartPaste}
        <Diff />
      {:else if codeblockActionType === CodeblockActionType.directApply}
        <Check />
      {/if}
    </svelte.fragment>
    <slot name="text" />
  </SuccessfulButton>
</span>

<style>
  .c-code-insert-button {
    display: contents;
  }
</style>

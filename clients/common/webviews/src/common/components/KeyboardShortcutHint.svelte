<script lang="ts">
  let clazz: string = "";
  export { clazz as class };

  export let keybinding: string | undefined = undefined;
  export let icons = keybinding?.split("-") ?? [];
</script>

<span class={`c-keyboard-shortcut-hint ${clazz}`}>
  {#each icons as icon}
    <span class="c-keyboard-shortcut-hint__icon">
      {icon}
    </span>
  {/each}
</span>

<style>
  .c-keyboard-shortcut-hint {
    display: inline-flex;
    gap: 2px;
  }

  .c-keyboard-shortcut-hint__icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--ds-spacing-1);
    border: 1px solid var(--keyboard-shortcut-hint-border, var(--ds-color-neutral-a8));
    border-radius: var(--ds-spacing-1);
    color: var(--keyboard-shortcut-hint-color, var(--ds-color-neutral-a8));
    font-size: 0.7rem;
    line-height: 1.5;
    background: var(--keyboard-shortcut-hint-bg, unset);
  }
</style>

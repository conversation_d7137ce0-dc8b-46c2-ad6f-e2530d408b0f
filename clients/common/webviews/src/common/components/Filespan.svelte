<script lang="ts">
  import TextAugment, {
    type TextSize,
  } from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getFileDirectory, getFilename, normalizeFilePath } from "../utils/file-paths";

  let clazz = "";
  export { clazz as class };
  export let filepath: string;
  export let size: TextSize = 1;
  export let nopath: boolean = false;

  $: normalizedFilepath = normalizeFilePath(filepath);

  // Remove all empty parts from the path
  $: filename = getFilename(normalizedFilepath);
  $: dir = getFileDirectory(normalizedFilepath);
</script>

<TextAugment {size}>
  <div class={`c-filespan ${clazz}`}>
    <slot name="leftIcon" />
    <span class="c-filespan__filename">{filename}</span>
    {#if !nopath}
      <div class="c-filespan__dir">
        <!-- We wrap directory in RTL for ellipses on left, but the actual
                text contents are LTR, so we need to wrap the contents -->
        <div class="c-filespan__dir-text">{dir}</div>
      </div>
    {/if}
    <slot name="rightIcon" />
  </div>
</TextAugment>

<style>
  .c-filespan {
    display: flex;
    gap: var(--ds-spacing-1);
    width: 100%;
    overflow: hidden;
    flex-direction: row;
    align-items: center;
  }
  .c-filespan > .c-filespan__filename,
  .c-filespan > .c-filespan__dir {
    /* This will always attempt to take up any available space (grow 0)
        but if it needs to shrink, it will do so after the filename since it has
        a non-zero flex-basis */
    flex: 0 1 auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .c-filespan > .c-filespan__dir {
    /* This will allow the directory to grow after the filename,
        and will shrink before the filename */
    flex: 1 1 0px;

    overflow: hidden;
    display: inline-block;

    /* We want the ellipsis to be on the left side, so we do RTL */
    text-overflow: ellipsis;
    direction: rtl;
    text-align: left;

    /* Lighter color for the directory path */
    opacity: 50%;
  }

  .c-filespan > .c-filespan__dir > .c-filespan__dir-text {
    display: inline;
    width: fit-content;
    direction: ltr;
  }

  .c-filespan > :global(:first-child),
  .c-filespan > :global(.material-symbols-outlined:first-child) {
    margin-left: 0;
  }

  .c-filespan > :global(:last-child),
  .c-filespan > :global(.material-symbols-outlined:last-child) {
    margin-right: 0;
  }
</style>

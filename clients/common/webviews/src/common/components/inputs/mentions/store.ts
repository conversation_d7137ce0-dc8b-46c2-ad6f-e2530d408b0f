import type { SvelteComponent } from "svelte";
import { get, writable, type Writable } from "svelte/store";
import type { DOMOutputSpec, Node as ProseMirrorNode } from "prosemirror-model";
import type { Editor, Node } from "@tiptap/core";
import { Mention, type MentionOptions } from "@tiptap/extension-mention";
import type { SuggestionKeyDownProps, SuggestionProps } from "@tiptap/suggestion";
import Fuse from "fuse.js";
import debounce from "lodash.debounce";

import type { GetSuggestionsFn } from "$common-webviews/src/apps/chat/models/types";
import type {
  CommandArgs,
  IMentionable,
  ISvelteRenderActionProvider,
  ITipTapExtensionProvider,
} from "../types";
import { Plugin, PluginKey, type Transaction } from "@tiptap/pm/state";
import { findNewMentions, getMentionNodes } from "./utils";
import { type Action } from "svelte/action";
import MentionComponent from "./Mention.svelte";

/**
 * This file implements the core functionality for the @mention feature in our rich text editor.
 * It manages the state and behavior of mentions, including suggestion popups and mention chips.
 *
 * The main challenges addressed here are:
 * 1. Integrating with TipTap's mention and suggestion plugins
 * 2. Managing the state of suggestions and selected mentions
 * 3. Handling user interactions (keyboard navigation, selection, etc.)
 * 4. Rendering mention chips and suggestion popups
 *
 * Key considerations:
 * - Performance: We use debounce for suggestion updates to avoid excessive API calls
 * - Flexibility: The store is generic, allowing different types of mentionable items
 * - Reactivity: We use Svelte stores to manage state and trigger UI updates
 * - Accessibility: Keyboard navigation is supported for suggestion lists
 *
 * Data Flow Diagram:
 *
 *                  +--------+
 *                  | TipTap |
 *                  +--------+
 *                      |
 *                      | Events (keystrokes, selections)
 *                      v
 *              +----------------+
 *              | MentionStore   |
 *              |                |
 *              | - Manages state|
 *              | - Handles      |
 *              |   interactions |
 *              +----------------+
 *                 |          |
 *    Updates      |          | Updates
 *    suggestion   |          | chip data
 *    data         |          |
 *                 v          v
 *    +-----------------+ +-----------------+
 *    | Mention Dropdown| | Mention Chip    |
 *    |                 | | Hover           |
 *    | - Displays      | | - Displays      |
 *    |   suggestions   | |   hover content |
 *    +-----------------+ +-----------------+
 *
 * - Events triggered by user typing are sent by TipTap to the MentionStore
 * - The MentionStore caches information from TipTap and computes what
 *     *we* should be rendering
 * - It sets some Svelte reactive variables that will eventually be consumed
 *     by a Svelte component that renders the dropdown/chip
 */
export interface IRenderOptions<TOption extends IMentionable> {
  mentionHoverContents: typeof SvelteComponent<{ option: TOption }>;
  suggestionItem: typeof SvelteComponent<{ option: TOption }>;
}

export interface IMentionChipData<TOption extends IMentionable> {
  data: TOption;
  clientRect: DOMRect;
}

// A top-level interface describing the mention suggestions pop-up
// information
export interface IMentionSuggestionData<TOption extends IMentionable> {
  lastQuery?: string | undefined;
  // The current visible options for the mention
  items?: TOption[] | undefined;
  // The current props for the mention, set by the lifecycle hooks in TipTap
  props?: SuggestionProps<TOption>;
  // The index of the currently selected item
  selectedIdx: number;
  // If items is empty, show this message
  emptyMessage?: string | undefined;
}

export interface IMentionOptionData<TOption extends IMentionable> {
  id: string;
  label: string;
  data: TOption;
}

export interface IMentionStoreProps<TOption extends IMentionable> {
  render: IRenderOptions<TOption>;
  onSelectItem: (item: TOption) => boolean;
  getSuggestions: GetSuggestionsFn<TOption>;
  onKeyboardDown?: (props: SuggestionKeyDownProps) => boolean;
  onExit?: () => void;
  // Add new optional callback
  onMentionAdded?: (option: IMentionOptionData<TOption>) => void;
  onMentionRemoved?: (option: IMentionOptionData<TOption>) => void;
  onMentionItemsUpdated?: (
    added: IMentionOptionData<TOption>[],
    removed: IMentionOptionData<TOption>[],
  ) => void;
}

/**
 * A class that stores the state of the @mention component and manages its behavior.
 * This class is responsible for:
 * 1. Creating and configuring the TipTap Mention node
 * 2. Managing the state of mention suggestions and selected mentions
 * 3. Handling user interactions with mentions and suggestions
 * 4. Providing methods for programmatically inserting and cancelling mentions
 */
export class MentionStore<TOption extends IMentionable>
  implements ITipTapExtensionProvider, ISvelteRenderActionProvider
{
  // A TipTap `Mention` node that is passed into TipTap to render the mention.
  private _mention: Node<MentionOptions, IMentionOptionData<TOption>>;
  private _editor: Editor | undefined;
  private _triggerCharacter: string = "@";

  // A cache point for the active hover filepath and client rect
  private _mentionChipData: Writable<IMentionChipData<TOption> | null> = writable(null);

  // The current options for the mention, as well as the canceller for the current fetch.
  private _mentionSuggestionData: Writable<IMentionSuggestionData<TOption> | null> = writable(null);
  private _cancelGetCurrOptions: (() => void) | undefined = undefined;
  private _isActive: boolean = false;

  constructor(private props: IMentionStoreProps<TOption>) {
    // Pull out const references for all of the functions we want to use in the mention extension
    const createMentionChip = this.createMentionChip;
    const updateCurrOptions = ({ query }: { query: string }): IMentionOptionData<TOption>[] => {
      void this.updateCurrOptions({ query });
      return [];
    };
    const setSuggestionProps = this.setSuggestionProps;
    const onStartSuggestion = this.onStartSuggestion;
    const onExitSuggestion = this.onExitSuggestion;
    const onSelectItem = this.props.onSelectItem;
    const onSuggestionKeyDown = this.onSuggestionKeyDown;
    const updateEditor = (editor: Editor | undefined): void => {
      this._editor = editor;
    };

    // Configure the TipTap Mention node with our custom behavior
    this._mention = Mention.extend({
      onCreate(): void {
        updateEditor(this.editor);
        const allMentions = getMentionNodes(this.editor).map(
          (node) => node.attrs.data as IMentionOptionData<TOption>,
        );
        allMentions.forEach((mention) => props.onMentionAdded?.(mention));
        props.onMentionItemsUpdated?.(allMentions, []);
      },
      // Add a `data` attribute type to this node with a default value of null.
      // This allows us to store additional data with each mention.
      addAttributes(): Record<string, unknown> {
        return { ...this.parent?.(), data: { default: null } };
      },
      addProseMirrorPlugins() {
        return [
          ...(this.parent?.() ?? []),
          new Plugin({
            key: new PluginKey("mentionListener"),
            view: () => ({
              update: (view, prevState) => {
                const previousDoc = prevState.doc;
                const currentDoc = view.state.doc;

                // Skip if documents are the same
                if (previousDoc === currentDoc) {
                  return;
                }

                let removedMentions: IMentionOptionData<TOption>[] = [];
                let addedMentions: IMentionOptionData<TOption>[] = [];
                // Find old mentions that aren't in the new state
                if (props.onMentionRemoved || props.onMentionItemsUpdated) {
                  findNewMentions(previousDoc, currentDoc, (node) => {
                    removedMentions.push(node.attrs.data as IMentionOptionData<TOption>);
                    props.onMentionRemoved?.(node.attrs.data as IMentionOptionData<TOption>);
                  });
                }

                // Find new mentions that weren't in the previous state
                if (props.onMentionAdded || props.onMentionItemsUpdated) {
                  findNewMentions(currentDoc, previousDoc, (node) => {
                    addedMentions.push(node.attrs.data as IMentionOptionData<TOption>);
                    props.onMentionAdded?.(node.attrs.data as IMentionOptionData<TOption>);
                  });
                }
                props.onMentionItemsUpdated?.(addedMentions, removedMentions);
              },
            }),
          }),
        ];
      },
      // Add additional options to the mention object
      addOptions(): MentionOptions {
        const parentOpts = this.parent?.();
        return {
          ...parentOpts,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          HTMLAttributes: { class: "c-context-chip" },
          renderHTML: createMentionChip, // Callback to create a DOM node for the mention chip
          // Add the suggestion plugin options to the mention plugin
          suggestion: {
            ...parentOpts?.suggestion,
            allowedPrefixes: [" ", "\t", "\n"],
            items: updateCurrOptions, // Always return empty array, because we render independently
            command: ({ editor, range, props: optionItem }: CommandArgs<TOption>) => {
              if (onSelectItem(optionItem) && editor && range) {
                parentOpts.suggestion?.command?.({
                  editor,
                  range,
                  props: {
                    id: optionItem.id,
                    name: optionItem.name ?? optionItem.id,
                    label: optionItem.label,
                    data: optionItem,
                  },
                });
              }
            },
            // These are hooks that are called by TipTap whenever renders need to occur.
            // These effectively notify our system when certain events are happening in TipTap,
            // and allow us to update the actual visual state accordingly
            render: () => ({
              onStart: onStartSuggestion,
              onUpdate: setSuggestionProps,
              onKeyDown: onSuggestionKeyDown,
              onExit: onExitSuggestion,
            }),
          },
        };
      },
    });
  }

  /**
   * Returns the TipTap Mention node extension.
   * This extension is used by TipTap to render and manage mentions in the editor.
   *
   * @implements ITipTapExtensionProvider
   * @returns The configured Mention node extension
   */
  public get tipTapExtension(): Node<MentionOptions, IMentionOptionData<TOption>> {
    return this._mention;
  }

  public svelteRenderAction: Action<HTMLDivElement> = (element: HTMLDivElement) => {
    const mentionComponent = new MentionComponent({
      target: element,
      props: {
        mentionStore: this,
      },
    });

    return {
      destroy: () => {
        mentionComponent.$destroy();
      },
    };
  };

  public getAllMentions(): IMentionOptionData<TOption>[] {
    const allMentions = this._editor ? getMentionNodes(this._editor) : [];
    return allMentions.map((node) => node.attrs.data as IMentionOptionData<TOption>);
  }

  public get mentionChipData(): Writable<IMentionChipData<TOption> | null> {
    return this._mentionChipData;
  }

  public get mentionSuggestionsData(): Writable<IMentionSuggestionData<TOption> | null> {
    return this._mentionSuggestionData;
  }

  public get suggestionItem(): typeof SvelteComponent<{ option: TOption }> {
    return this.props.render.suggestionItem;
  }

  public get mentionHoverContents(): typeof SvelteComponent<{
    option: TOption;
  }> {
    return this.props.render.mentionHoverContents;
  }

  // Methods for managing suggestion state and user interactions

  /**
   * Updates the focus in the suggestion list.
   * This is used for keyboard navigation.
   */
  public focusSuggestionIdx = (idx: number): void => {
    this._mentionSuggestionData.update((currData) => {
      return {
        ...currData,
        selectedIdx: idx,
      };
    });
  };

  /**
   * Programmatically selects an item from the suggestion list.
   * This is used when a user clicks on a suggestion or presses enter/tab.
   */
  public selectItem = (item: TOption): void => {
    // This will trigger the command defined in the constructor -- the props are just a way
    // to get the handles on all of the information in the suggestions
    get(this._mentionSuggestionData)?.props?.command?.(item);
  };

  /**
   * Refreshes the current options for the mention suggestion list.
   * This is useful when the underlying data might have changed.
   */
  public refreshCurrOptions = async (): Promise<void> => {
    const query = get(this._mentionSuggestionData)?.lastQuery ?? "";
    await this.updateCurrOptions({ query });
  };

  /**
   * Sets the current options for the mention suggestion list.
   * This is used when we want to update the suggestions without fetching new data.
   */
  public setCurrOptions = (options: TOption[]): void => {
    this._mentionSuggestionData.update((currData) => {
      return {
        selectedIdx: 0,
        ...currData,
        items: options,
      };
    });
  };

  /**
   * Updates the current options for the mention, potentially cancelling any outstanding fetches.
   * This method is debounced to avoid excessive API calls when the user is typing quickly.
   *
   * @param param0 An object containing the query string
   */
  private updateCurrOptions = debounce(
    async ({ query }: { query: string }): Promise<void> => {
      // Update the last query sent, in case we want to reload the options
      this._mentionSuggestionData.update((currData) => {
        return {
          selectedIdx: 0,
          ...currData,
          lastQuery: query,
        };
      });

      // Cancel any outstanding fetches and set up new one
      this._cancelGetCurrOptions?.();
      let cancelled: boolean = false;
      this._cancelGetCurrOptions = () => (cancelled = true);

      // Fetch the suggestions
      const suggestions = await this.props.getSuggestions(query);

      // Only apply the suggestions if we haven't been cancelled or closed
      if (cancelled || !this._isActive) {
        return;
      }
      const sortedSuggestions = suggestions;

      // Update the mention data
      this._mentionSuggestionData.update((currData) => {
        return {
          selectedIdx: 0,
          ...currData,
          items: sortedSuggestions,
          emptyMessage: sortedSuggestions.length === 0 ? "No results found" : undefined,
        };
      });
    },
    300,
    { leading: true, trailing: true, maxWait: 300 },
  );

  /**
   * Sets the suggestion props for the mention.
   * This is needed because TipTap needs a way to notify us of changes,
   * and we need to store them for our own renderers to decide what to render and how to show things.
   */
  private setSuggestionProps = (props: SuggestionProps<TOption>): void => {
    this._mentionSuggestionData.update((currData) => {
      return {
        selectedIdx: 0,
        ...currData,
        props,
      };
    });
  };

  /**
   * Handles the exit of a suggestion.
   * This is called when TipTap tells us to stop rendering.
   */
  private onExitSuggestion = (): void => {
    this._cancelGetCurrOptions?.();
    this._mentionSuggestionData.set(null);
    this.props.onExit?.();
    this.setActive(false);
  };

  /**
   * Handles the start of a suggestion.
   * This is called when TipTap tells us to start rendering.
   */
  private onStartSuggestion = (props: SuggestionProps<TOption>): void => {
    this.setSuggestionProps(props);
    this.setActive(true);
  };

  /**
   * Sets the active state of the mention store.
   * This is used to determine whether we should be showing suggestions.
   */
  public setActive = (active: boolean): void => {
    this._isActive = active;
  };

  /**
   * Handles keyboard events for navigating through the suggestion menu.
   * This function provides keyboard navigation (up/down arrows),
   * selection (enter/tab), and dismissal (escape) of suggestions.
   *
   * @param props The event props, passed in from TipTap's suggestions callbacks
   * @returns A boolean indicating whether the event was handled
   */
  private onSuggestionKeyDown = (props: SuggestionKeyDownProps): boolean => {
    const suggestionData = get(this._mentionSuggestionData);
    if (suggestionData === null) {
      return false;
    }

    const { items, props: suggestionProps, selectedIdx } = suggestionData ?? {};

    // Don't do anything if we have no suggestions
    if (!items || items.length === 0 || !suggestionProps) {
      return false;
    }

    const selectedItem = items?.[selectedIdx];
    if (this.props.onKeyboardDown !== undefined && this.props.onKeyboardDown(props)) {
      return true;
    }

    switch (props.event.key) {
      case "ArrowDown":
        this._mentionSuggestionData.set({
          ...suggestionData,
          selectedIdx: (selectedIdx + 1) % items.length,
        });
        return true;
      case "ArrowUp":
        this._mentionSuggestionData.set({
          ...suggestionData,
          selectedIdx: (items.length + selectedIdx - 1) % items.length,
        });
        return true;
      case "Enter":
      case "Tab": {
        props.event.stopPropagation();
        suggestionProps?.command(selectedItem);
        return true;
      }
      case "Escape":
        this.setCurrOptions([]);
        return true;
    }
    return false;
  };

  /**
   * Creates the HTML element (span) that represents the mention chip.
   * This method is called by TipTap when rendering a mention in the editor.
   * It sets up event listeners for hover effects and popups.
   *
   * @param options The mention options from TipTap
   * @param node The ProseMirror node containing the mention data
   * @returns A DOMOutputSpec representing the chip span
   */
  private createMentionChip = ({
    options,
    node,
  }: {
    options: MentionOptions;
    node: ProseMirrorNode;
  }): DOMOutputSpec => {
    const span = document.createElement("span");
    span.innerText = `@${node.attrs.label}`; // Add @ symbol here
    for (const [key, value] of Object.entries(options.HTMLAttributes)) {
      span.setAttribute(key, value);
    }

    const showChipPopup = () => {
      this._mentionChipData.set({
        data: node.attrs.data,
        clientRect: span.getBoundingClientRect(),
      });
    };

    // When a mouse enters the chip, we set our current mention chip data
    span.addEventListener("mouseenter", showChipPopup);
    span.addEventListener("mouseover", showChipPopup);
    // When a mouse leaves the chip, we clear our current mention chip data
    span.addEventListener("mouseleave", this.closeMentionChipPopup);
    return span;
  };

  /**
   * Closes the mention chip popup.
   * This is called when the mouse leaves a mention chip.
   */
  public closeMentionChipPopup = (): void => {
    this._mentionChipData.set(null);
  };

  /**
   * Inserts a suggestion trigger character into the editor.
   * If there's text immediately before the cursor, it adds a space before the trigger character.
   * This method is used to programmatically start a mention suggestion.
   */
  public insertSuggestion = (): void => {
    if (!this._editor) {
      return;
    }

    const charData: ICharBeforeCursorData = getCharBeforeCursor(this._editor);
    let tr: Transaction;
    if (charData.char === this._triggerCharacter) {
      // Remove the character and add it back in to
      // re-trigger the tooltip to show up
      tr = this._editor.state.tr.delete(charData.offsetStart, charData.offsetEnd);
      this._editor.view.dispatch(tr);
      tr = this._editor.state.tr;
    } else if (charData.char.trim() !== "") {
      // Insert a space before the trigger character
      tr = this._editor.state.tr.insertText(" ");
    } else {
      tr = this._editor.state.tr;
    }

    tr = tr.insertText(this._triggerCharacter);
    this._editor.view.dispatch(tr);
  };

  /**
   * Cancels the current suggestion by removing the trigger character if it's
   * immediately before the cursor.
   * This method is used to programmatically cancel a mention suggestion.
   */
  public cancelSuggestion = (): void => {
    if (!this._editor) {
      return;
    }

    const charData: ICharBeforeCursorData = getCharBeforeCursor(this._editor);
    let tr: Transaction;
    if (charData.char === this._triggerCharacter) {
      // Remove the trigger character
      tr = this._editor.state.tr.delete(charData.offsetStart, charData.offsetEnd);
    } else {
      tr = this._editor.state.tr;
    }
    this._editor.view.dispatch(tr);
  };

  /**
   * Inserts a mention node into the editor.
   * This method is used to programmatically insert a mention into the editor.
   *
   * @param option The mention option to insert
   * @returns A boolean indicating whether the insertion was successful
   */
  public insertMentionNode(option: IMentionOptionData<TOption>): boolean {
    if (!this._editor) {
      return false;
    }

    this.removeCurrMentionNode();
    this._editor.commands.insertContent({
      type: "mention",
      attrs: option,
    });
    return true;
  }

  /**
   * Removes the current mention node from the editor.
   * This method is used to programmatically remove a mention from the editor.
   *
   * @returns A boolean indicating whether the removal was successful
   */
  public removeCurrMentionNode(): boolean {
    if (!this._editor) {
      return false;
    }

    // If the trigger character is before the cursor, remove it
    const charBeforeCursor = getCharBeforeCursor(this._editor);
    if (charBeforeCursor.char === "@") {
      this._editor.commands.deleteRange({
        from: charBeforeCursor.offsetStart,
        to: charBeforeCursor.offsetEnd,
      });
      return true;
    }
    return false;
  }
}

/**
 * Use Fuse.js to sort our suggestions based on a query and keys
 * to search through.
 *
 * @param suggestions The list of suggestions to sort
 * @param query The search query
 * @param keys The keys to search through in each suggestion
 * @returns sorted suggestions
 */
export function sortSuggestions(
  suggestions: IMentionable[],
  query: string,
  keys: string[],
): IMentionable[] {
  if (query === "" || keys.length === 0) {
    return suggestions;
  }

  const fuse = new Fuse(suggestions, {
    keys,
    threshold: 1.0,
    minMatchCharLength: 1,
    includeScore: true,
    useExtendedSearch: false,
    isCaseSensitive: false,
    ignoreLocation: true,
  });
  // fuse.js will order the results for us
  return fuse.search(query).map((x) => x.item);
}

interface ICharBeforeCursorData {
  char: string;
  offsetStart: number;
  offsetEnd: number;
}

/**
 * Gets the character immediately before the cursor in the editor.
 * This is used to determine whether we need to insert a space before
 * the trigger character when inserting a suggestion.
 *
 * @param editor The TipTap editor instance
 * @returns An object containing the character and its position
 */
function getCharBeforeCursor(editor: Editor): ICharBeforeCursorData {
  // Check the reference position before the current selection to
  // see if it is empty or not. If it is, we want to just insert the trigger
  // character. If it is not, we want to insert a space before the trigger character.
  let referencePos: number;
  if (editor.state.selection.empty) {
    referencePos = editor.state.selection.anchor;
  } else {
    referencePos = Math.min(editor.state.selection.anchor, editor.state.selection.head);
  }
  return {
    char: editor.state.doc.textBetween(referencePos - 1, referencePos),
    offsetStart: referencePos - 1,
    offsetEnd: referencePos,
  };
}

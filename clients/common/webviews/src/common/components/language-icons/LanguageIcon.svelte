<script lang="ts">
  import { UserThemeCategory } from "../../hosts/user-themes/augment-theme-attributes";
  import { themeStore } from "../../hosts/user-themes/theme-store";
  import { createCssVarsForFile } from "./language-icon";
  export let filename: string;
  export let theme: UserThemeCategory = UserThemeCategory.dark;

  $: {
    const themeDetails = $themeStore;
    theme = themeDetails?.category ?? theme;
  }
</script>

<span style={createCssVarsForFile(filename, theme)} class:c-language-icon={true} {...$$restProps}
  ><slot /></span
>

<style type="text/css">
  @font-face {
    src: url("./seti.woff") format("woff");
    font-family: "seti";
    font-weight: normal;
    font-style: normal;
    font-display: block;
    font-size: 150%;
  }
  .c-language-icon::before {
    font-family: "seti";
    font-size: var(--augment-file-icon-font-size, 16px);
    color: var(--augment-file-icon-font-color);
    content: var(--augment-file-icon-font-character);
    background-image: unset;
    vertical-align: middle;
    display: inline-block;
  }
</style>

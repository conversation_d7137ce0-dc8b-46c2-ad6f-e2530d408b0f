<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { fly } from "svelte/transition";
  import { resize } from "../code-roll/resize-observer";
  import { onMount } from "svelte";
  import Ellipsis from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  // Initial width of the left panel
  export let initialWidth = 300;
  /** the minimum width the left side can be in expanded state */
  export let expandedMinWidth = 50;
  /** the width the left side will be when minimized */
  export let minimizedWidth = 0;
  export let minimized = false;
  let clazz = "";
  export { clazz as class };
  export let showButton = true;
  export let deadzone = 0;
  /** Width threshold at which to switch to column layout */
  export let columnLayoutThreshold = 600;
  /** Force a specific layout mode. If undefined, layout mode is automatic based on width */
  export let layoutMode: "row" | "column" | undefined = undefined;

  let leftPanel: HTMLElement;
  let containerElement: HTMLElement;
  let isDragging = false;
  let startX: number;
  let startWidth: number;
  let newWidth = initialWidth;
  let currentWidth = initialWidth;
  let useColumnLayout = false;

  onMount(updateLayout);

  function startDrag(event: MouseEvent) {
    if (useColumnLayout) {
      return;
    }
    isDragging = true;
    startX = event.clientX;
    startWidth = leftPanel.offsetWidth;
    // Prevent text selection during drag
    event.preventDefault();
  }

  function doDrag(event: MouseEvent) {
    if (!isDragging || !leftPanel || useColumnLayout) {
      return;
    }

    /** the distance we have dragged */
    const delta = event.clientX - startX;
    /** the maximum width the left side can be */
    const maxWidth = containerElement.clientWidth - 200;
    /** the width we would have if we dragged without any constraints */
    const proposedWidth = startWidth + delta;

    // If the user tries to drag the drawer below expandedMinWidth, apply a deadzone
    if (proposedWidth < expandedMinWidth) {
      /** the width at which we will fully close the drawer */
      const minimizeThreshold = expandedMinWidth - deadzone;
      // If dragged past the deadzone, fully close
      if (proposedWidth < minimizeThreshold) {
        minimized = true;
      } else {
        // Otherwise lock at expandedMinWidth
        newWidth = expandedMinWidth;
        minimized = false;
      }
    } else if (proposedWidth > maxWidth) {
      newWidth = maxWidth;
      minimized = false;
    } else {
      newWidth = proposedWidth;
      minimized = false;
    }
  }

  function stopDrag() {
    isDragging = false;
    newWidth = Math.max(newWidth, expandedMinWidth);
  }

  function toggleDrawer() {
    minimized = !minimized;
  }

  function updateLayout() {
    if (!containerElement) {
      return;
    }

    // If layoutMode is explicitly set, use that
    if (layoutMode !== undefined) {
      useColumnLayout = layoutMode === "column";
      if (useColumnLayout) {
        isDragging = false;
      }
      return;
    }

    // Otherwise use automatic layout based on width
    useColumnLayout = containerElement.clientWidth < columnLayoutThreshold;
    if (useColumnLayout) {
      isDragging = false;
    }
  }

  // Watch for layoutMode changes
  $: if (layoutMode !== undefined) {
    useColumnLayout = layoutMode === "column";
    if (useColumnLayout) {
      isDragging = false;
    }
  }

  // Add reactive statement to force row layout when minimized
  $: if (minimized) {
    layoutMode = "row";
    useColumnLayout = false;
  } else if (layoutMode === "row" && !minimized) {
    // Reset to automatic layout when un-minimizing (only if it was previously in row mode)
    layoutMode = undefined;
    updateLayout();
  }

  $: {
    currentWidth = minimized ? minimizedWidth : newWidth;
  }
</script>

<svelte:window on:mousemove={doDrag} on:mouseup={stopDrag} />

<div
  class="c-drawer {clazz}"
  class:is-dragging={isDragging}
  class:is-hidden={!currentWidth}
  class:is-column={useColumnLayout}
  bind:this={containerElement}
  use:resize={{
    onResize: () => layoutMode === undefined && updateLayout(),
  }}
>
  <div
    class="c-drawer__left"
    style="--augment-drawer-width:{currentWidth}px;"
    bind:this={leftPanel}
  >
    <div
      class="c-drawer__left-content"
      style:width="var(--augment-drawer-width)"
      style:min-width="var(--augment-drawer-width)"
      style:max-width="var(--augment-drawer-width)"
      inert={isDragging}
    >
      <slot name="left" />
    </div>
  </div>

  <!--
  Drag handle
  Disabling aria here as we do not want focus going here.   This does not
  really have a non-mouse affordance.
  -->
  <div
    aria-hidden="true"
    class="c-drawer__handle"
    class:is-locked={useColumnLayout}
    on:mousedown={startDrag}
    on:dblclick={toggleDrawer}
  />

  <div class="c-drawer__right">
    <slot name="right" />
  </div>

  <!-- Hidden state indicator -->
  {#if minimized && showButton}
    <div transition:fly={{ y: 0, x: 0, duration: 200 }} class="c-drawer__hidden-indicator">
      <IconButtonAugment
        variant="solid"
        color="accent"
        size={2}
        radius={"full"}
        title="Show panel"
        on:click={toggleDrawer}
      >
        <Ellipsis />
      </IconButtonAugment>
    </div>
  {/if}
</div>

<style>
  .c-drawer {
    display: flex;
    flex-direction: row;
    flex: 1;
    width: 100%;
    height: 100%;
    position: relative;
    --augment-drawer-active-color: var(--ds-color-accent-9);
  }

  .c-drawer.is-column {
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    justify-content: start;
  }

  .c-drawer__handle:hover {
    background-color: var(--augment-drawer-active-color);
  }

  .is-dragging .c-drawer__handle {
    background-color: var(--augment-drawer-active-color);
  }

  .c-drawer__left-content {
    height: 100%;
  }

  .c-drawer.is-dragging {
    cursor: col-resize;
    user-select: none;
  }

  .c-drawer.is-hidden .c-drawer__handle {
    display: none;
  }

  .c-drawer__left {
    list-style: none;
    height: 100%;
    overflow: auto;
    scrollbar-color: var(--augment-scrollbar-color) rgba(0, 0, 0, 0.3);
  }

  .c-drawer.is-column .c-drawer__left {
    width: 100% !important;
    overflow-y: auto;
    flex-shrink: 1;
    min-height: 100px;
    height: fit-content;
  }

  .c-drawer.is-column .c-drawer__left-content {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    height: auto;
  }

  .c-drawer__right {
    display: flex;
    flex-direction: row;
    margin: 0;
    gap: 0.25em;
    flex: 1;
    overflow: auto;
    scrollbar-color: var(--augment-scrollbar-color) rgba(0, 0, 0, 0.3);
  }

  .c-drawer.is-column .c-drawer__right {
    width: 100%;
    overflow-y: auto;
    min-height: 0;
    flex-basis: fit-content;
    flex-grow: 0;
    flex-shrink: 0;
  }

  .c-drawer__handle {
    width: 4px;
    cursor: col-resize;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 3px;
    border-left: 1px solid var(--ds-color-neutral-a4);
    transition: background-color 50ms ease 0ms;
  }

  .c-drawer__handle.is-locked {
    cursor: default;
    opacity: 0.5;
  }

  .c-drawer.is-column .c-drawer__handle {
    width: 100%;
    height: 4px;
    margin: 3px 0;
    border-left: none;
    border-top: 1px solid var(--ds-color-neutral-a4);
  }

  .c-drawer__handle:hover {
    transition-delay: 400ms;
    background-color: var(--augment-drawer-active-color);
  }

  .c-drawer__handle.is-locked:hover {
    background-color: transparent;
  }

  .c-drawer__handle:not(:hover) {
    transition-delay: 0ms;
  }

  .c-drawer__left {
    margin: 0;
    gap: 0.25em;
  }

  .c-drawer__right {
    flex: 1;
  }

  .c-drawer__hidden-indicator {
    position: absolute;
    bottom: var(--ds-spacing-3);
    left: var(--ds-spacing-3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0.99;
    transition: opacity 0.2s ease;
    border-radius: 100%;
    box-shadow: var(--ds-shadow-no-border-3);
    --icon-size: 20px;
  }

  .c-drawer__hidden-indicator:hover {
    opacity: 0.5;
  }

  .c-drawer__hidden-indicator-ellipsis {
    font-size: 1rem;
    font-weight: bold;
    color: white;
  }
</style>

import { type WebViewMessageTypes } from "$vscode/src/utils/webviews/types";
import {
  WebViewMessageType,
  type AsyncWebViewMessage,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import { type WebViewMessage as SidecarWebViewMessage } from "@augment-internal/sidecar-libs/src/webview-messages/common-webview-messages";
import { type MessageConsumerTypes } from "@augment-internal/sidecar-libs/src/webview-messages/webview-messaging";

/**
 * AsyncMsgSender is a class that enables sending asynchronous messages to a webview and handling responses.
 * It provides a way to send requests to the the other side of a webview <=> extension channel, wait for
 * responses, and handle timeouts.
 */
export class AsyncMsgSender {
  /**
   * A map to store promise functions for each request ID.
   * This map is used to resolve or reject promises when a response is received.
   */
  private _idToPromiseFns = new Map<string, PromiseFns<WebViewMessage>>();

  /**
   * Constructs an AsyncMsgSender instance.
   *
   * @param _postMsgFn A function to post messages to the webview.
   * @param _timeoutMs The timeout duration in milliseconds for message responses. Defaults to 1000ms.
   */
  constructor(
    private _postMsgFn: SendFnT<AsyncWebViewMessage<WebViewMessageTypes>>,
    private _timeoutMs: number = 1000,
  ) {
    // Wrap the handler to see if it's a WebViewMessage
    const wrappedHandler = (e: MessageEvent<AsyncWebViewMessage<WebViewMessageTypes>>) => {
      this.resolveAsyncMsg(e.data as AsyncWebViewMessage<WebViewMessageTypes>);
    };
    window.addEventListener("message", wrappedHandler);
  }

  /**
   * Registers a promise context for a request.
   * This function returns a promise that will be resolved or rejected when a response is received.
   *
   * @param msg The request message.
   * @returns A promise that will be resolved or rejected when a response is received.
   */
  private registerPromiseContext = <
    ReqT extends WebViewMessageTypes,
    ResT extends WebViewMessageTypes,
  >(
    msg: AsyncWebViewMessage<ReqT>,
  ): Promise<AsyncWebViewMessage<ResT>> => {
    return new Promise<AsyncWebViewMessage<ResT>>((resolve: any, reject: any) => {
      this._idToPromiseFns.set(msg.requestId, { resolve, reject });
    });
  };

  /**
   * Resolves a promise for a request given a response.
   * This function checks if the incoming message is an async wrapper response and attempts to resolve the promise.
   *
   * @param response The response message.
   * @returns True if the promise was resolved or rejected, false otherwise.
   */
  private resolveAsyncMsg = (response: AsyncWebViewMessage<WebViewMessageTypes>): boolean => {
    if (response.type !== WebViewMessageType.asyncWrapper) {
      return false;
    }
    const msg = response as AsyncWebViewMessage<WebViewMessage>;
    const fns = this._idToPromiseFns.get(msg.requestId);
    if (!fns) {
      return false;
    }

    this._idToPromiseFns.delete(msg.requestId);
    if (msg.error) {
      fns.reject(new Error(msg.error));
    } else {
      fns.resolve(msg);
    }
    return true;
  };

  /**
   * Rejects a promise for a given request.
   * This function is called when a timeout occurs or an error occurs while sending a request.
   *
   * @param request The request message.
   * @param reason The reason for rejecting the promise.
   */
  private rejectAsyncMsg = (
    request: AsyncWebViewMessage<WebViewMessageTypes>,
    reason: any,
  ): void => {
    const fns = this._idToPromiseFns.get(request.requestId);
    if (!fns) {
      return;
    }

    this._idToPromiseFns.delete(request.requestId);
    // eslint-disable-next-line no-console
    console.debug(`AsyncMsgSender: Rejecting request, reason: ${reason}`, request);
    fns.reject(reason);
  };

  /**
   * Sends a message and sets a timer to reject the promise if no response is received.
   * This function is used to implement the timeout mechanism.
   *
   * @param msg The request message.
   * @param timeoutMs The timeout duration in milliseconds. Defaults to the instance's timeout duration.
   */
  private sendOrTimeout = <ReqT extends WebViewMessageTypes>(
    msg: AsyncWebViewMessage<ReqT>,
    timeoutMs: number = this._timeoutMs,
  ): void => {
    this._postMsgFn(msg);
    if (timeoutMs > 0) {
      setTimeout(
        () =>
          this.rejectAsyncMsg(
            msg,
            new Error(`Request timed out: ${msg?.baseMsg?.type}, id: ${msg?.requestId}`),
          ),
        timeoutMs,
      );
    }
  };

  /**
   * Sends a message and returns a promise that resolves with the response.
   * This function is the main entry point for sending requests to the webview.
   *
   * @param msg The request message.
   * @param timeoutMs The timeout duration in milliseconds. Defaults to the instance's timeout duration.
   * @returns A promise that resolves with the response message.
   */
  send = async <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
    msg: ReqT,
    timeoutMs: number = this._timeoutMs,
  ): Promise<ResT> => {
    const wrappedReq = wrapRequest<ReqT>(msg);
    const promise = this.registerPromiseContext<ReqT, ResT>(wrappedReq);
    this.sendOrTimeout(wrappedReq, timeoutMs);

    const result = await promise;
    if (result.error) {
      throw new Error(result.error);
    } else if (!result.baseMsg) {
      throw new Error("No response or error message");
    }

    return result.baseMsg;
  };

  /**
   * Sends a message and returns a promise that resolves with the response.
   * This function is the main entry point for sending requests to the webview.
   *
   * @param msg The request message.
   * @param timeoutMs The timeout duration in milliseconds. Defaults to the instance's timeout duration.
   * @returns A promise that resolves with the response message.
   */
  sendToSidecar = async <
    ReqT extends SidecarWebViewMessage<MessageConsumerTypes>,
    ResT extends SidecarWebViewMessage<MessageConsumerTypes>,
  >(
    msg: ReqT,
    timeoutMs: number = this._timeoutMs,
  ): Promise<ResT> => {
    const wrappedReq = wrapRequest<ReqT>(msg, "sidecar");
    const promise = this.registerPromiseContext<ReqT, ResT>(wrappedReq);
    this.sendOrTimeout(wrappedReq, timeoutMs);

    const result = await promise;
    if (result.error) {
      throw new Error(result.error);
    } else if (!result.baseMsg) {
      throw new Error("No response or error message");
    }

    return result.baseMsg;
  };

  async *stream<ReqT extends WebViewMessage, ResT extends WebViewMessage>(
    msg: ReqT,
    timeoutMs: number = this._timeoutMs,
    streamTimeoutMs: number = this._timeoutMs,
  ): AsyncGenerator<ResT, void, void> {
    // Construct the request
    let wrappedReq = wrapRequest(msg);
    wrappedReq.streamCtx = {
      streamMsgIdx: 0,
      streamNextRequestId: "",
    };

    // Keep track of the next expected stream index to arrive
    let nextStreamIdx = 0;

    // Track if the stream has been closed by the consumer
    let isClosed = false;

    try {
      let promise = this.registerPromiseContext<ReqT, ResT>(wrappedReq);
      this.sendOrTimeout(wrappedReq, timeoutMs);
      const streamTimeout = new Promise<void>((_, rej) => {
        if (streamTimeoutMs <= 0) {
          return;
        }
        setTimeout(() => rej(new Error("Stream timed out")), streamTimeoutMs);
      });

      while (!isClosed) {
        // As soon as promise is resolved, register a new promise context to wait for next message
        const result = await Promise.race([promise, streamTimeout]);
        if (result?.type !== WebViewMessageType.asyncWrapper) {
          throw new Error(`Received unexpected message: ${result}`);
        }
        // Error checking
        if (result.error) {
          throw new Error(result.error);
        }
        if (!result.streamCtx || result.streamCtx.isStreamComplete) {
          return;
        }

        if (!result.baseMsg) {
          throw new Error("No response or error message");
        } else if (result.streamCtx.streamMsgIdx !== nextStreamIdx) {
          const actualIdx = result.streamCtx.streamMsgIdx;
          throw new Error(
            `Received out of order stream chunk. Expected ${nextStreamIdx} but got ${actualIdx}`,
          );
        }

        // Update the next stream index we expect
        nextStreamIdx = result.streamCtx.streamMsgIdx + 1;
        wrappedReq = {
          ...wrappedReq,
          streamCtx: {
            streamMsgIdx: nextStreamIdx,
            streamNextRequestId: "",
          },
          requestId: result.streamCtx.streamNextRequestId,
        };
        // Listen for the next expected response from the stream
        promise = this.registerPromiseContext<ReqT, ResT>(wrappedReq);

        yield result.baseMsg;
      }
    } finally {
      // This block is reached when the generator is closed (via return() or throw())
      if (!isClosed) {
        isClosed = true;

        // Send a cancellation message to the server
        try {
          // Clean up any pending promise for this request
          this._idToPromiseFns.delete(wrappedReq.requestId);
        } catch (error) {
          // Just log any errors during cleanup, don't throw
          console.warn("Error sending stream cancellation message:", error);
        }
      }
    }
  }
}

/**
 * Wraps a request message in an async wrapper message. Generates a request ID using getNonce.
 *
 * @param msg The request message to wrap.
 * @returns The wrapped async message.
 */
export function wrapRequest<ReqT>(
  msg: ReqT,
  destination: "sidecar" | "host" = "host",
): AsyncWebViewMessage<ReqT> {
  return {
    type: WebViewMessageType.asyncWrapper,
    requestId: crypto.randomUUID(),
    error: null,
    baseMsg: msg,
    destination: destination,
  };
}

type ResolveFnT<ResT extends WebViewMessage> = (
  value: AsyncWebViewMessage<ResT> | PromiseLike<AsyncWebViewMessage<ResT>>,
) => void;
export interface PromiseFns<ResT extends WebViewMessage> {
  resolve: ResolveFnT<ResT>;
  reject: (reason?: any) => void;
}

export interface SendFnT<ResT> {
  (msg: ResT): void;
}

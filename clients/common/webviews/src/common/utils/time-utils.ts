/**
 * Formats a timestamp into a human-readable time string
 * Just time if in the last 24 hours, otherwise date and time
 *
 * @param timestamp - ISO string timestamp or Date object
 * @returns A human-readable time string
 */
export function formatTimestampForChatMessage(timestamp: string | Date | undefined): string {
  if (!timestamp) {
    return "";
  }

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const seconds = Math.floor(diffMs / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  return (
    date.toLocaleDateString([], {
      month: "short",
      day: "numeric",
    }) +
    " " +
    date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    })
  );
}

/**
 * Formats a timestamp into a full date and time string
 *
 * @param timestamp - ISO string timestamp or Date object
 * @returns A formatted date and time string
 */
export function formatFullTimestamp(timestamp: string | Date | undefined): string {
  if (!timestamp) {
    return "";
  }

  const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
  return (
    date.toLocaleDateString([], {
      year: "numeric",
      month: "short",
      day: "numeric",
    }) +
    " " +
    date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    })
  );
}

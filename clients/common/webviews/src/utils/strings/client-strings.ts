import { getHost } from "$common-webviews/src/common/hosts/create-host";
import { HostClientType } from "$common-webviews/src/common/hosts/host-types";
import { DEFAULT_STRINGS } from "./default-strings";
import { JETBRAINS_STRINGS } from "./jetbrains-strings";

/**
 * This class provides a way to get strings for different clients.
 */
class ClientStrings {
  private strings: Record<keyof typeof DEFAULT_STRINGS, string>;

  constructor(client: HostClientType) {
    // Where apprioriate, we can add overrides for different clients.
    let overrides: Record<HostClientType, Partial<Record<keyof typeof DEFAULT_STRINGS, string>>> = {
      [HostClientType.vscode]: {},
      [HostClientType.jetbrains]: JETBRAINS_STRINGS,
    };

    this.strings = {
      ...DEFAULT_STRINGS,
      ...overrides[client],
    };
  }

  get(key: keyof typeof DEFAULT_STRINGS) {
    return this.strings[key];
  }
}

const host = getHost();
export const clientStrings = new ClientStrings(host.clientType);

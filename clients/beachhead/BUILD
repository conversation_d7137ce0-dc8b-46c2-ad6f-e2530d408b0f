load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("//tools/bzl/node_sea:node_sea.bzl", "node_sea_binary")

npm_link_all_packages()

SRC_FILES = glob(
    ["src/**/*.ts"],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
)

TEST_FILES = glob([
    "src/**/__tests__/**/*.ts",
    "src/**/__tests__/__fixtures__/*.json",
    "src/**/__tests__/**/__snapshots__/*.snap",
    "src/**/__tests__/**/*.html",
    "src/**/__mocks__/**/*.ts",
])

BUILD_DEPS = [
    ":node_modules",
    "//clients/sidecar/libs:ts",
    "//third_party/node-ignore",
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    deps = [
        "//clients:tsconfig",
        "//clients/sidecar/libs:tsconfig",
    ],
)

filegroup(
    name = "package",
    srcs = ["package.json"],
)

esbuild(
    name = "bundle",
    srcs = SRC_FILES + [
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
    ],
    entry_point = "src/index.ts",
    format = "cjs",
    minify = True,
    output = "out/index.bundle.js",
    platform = "node",
    sourcemap = "external",
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

esbuild(
    name = "bundle_dev",
    srcs = SRC_FILES + [
        ":package",
        ":tsconfig",
        "//clients/data/file-ext:augment_supported_extensions",
    ],
    entry_point = "src/index.ts",
    format = "cjs",
    minify = True,
    output = "out/index.bundle.dev.js",
    platform = "node",
    sourcemap = "both",
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

# Rules for building a SEA binary.
#
# https://nodejs.org/api/single-executable-applications.html
#

node_sea_binary(
    name = "beachhead",
    src = ":out/index.bundle.js",
    out = "out/beachhead",
    visibility = ["//clients/beachhead:__subpackages__"],
)

node_sea_binary(
    name = "beachhead_dev",
    src = ":out/index.bundle.dev.js",
    out = "out/beachhead_dev",
    visibility = ["//clients/beachhead:__subpackages__"],
)

# Testing

ts_project(
    name = "ts",
    srcs = SRC_FILES,
    out_dir = "out",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

build_test(
    name = "build_test",
    targets = [":ts"],
)

jest_test(
    name = "test",
    timeout = "moderate",
    config = "jest.config.js",
    data = SRC_FILES + TEST_FILES + glob([
        "scripts/**/*.ts",
    ]) + BUILD_DEPS + [
        ":package",
        ":tsconfig",
        "//clients/sidecar/libs:src",
    ],
    node_modules = "//clients/beachhead:node_modules",
)

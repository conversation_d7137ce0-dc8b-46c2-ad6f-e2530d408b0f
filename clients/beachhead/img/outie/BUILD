load("@aspect_bazel_lib//lib:tar.bzl", "tar")
load("@rules_oci//oci:defs.bzl", "oci_image")
load("@bazel_skylib//rules:common_settings.bzl", "string_flag")

################################################################################
#
# Testing image selection.
#
# This amounts to an additional oci_image with the `AUGMENT_E2E_TESTING` env var set.
#

string_flag(
    name = "e2e_testing",
    build_setting_default = "",
)

config_setting(
    name = "no_custom_env",
    flag_values = {":e2e_testing": ""},
)

alias(
    name = "outie_oci",
    actual = select({
        ":no_custom_env": ":outie_oci_nontesting",
        "//conditions:default": ":outie_oci_testing",
    }),
    visibility = [
        "//clients/beachhead/img:__pkg__",
        "//services/test:__subpackages__",
    ],
)

################################################################################
#
# OCI Image
#
# The base image is a Dockerfile in the `outie-docker-base` directory. It's a basic
# `ubuntu:22.04` image with qemu, nftables, and other apt packages installed.
# (It also holds the `vmlinuz` kernel and `image.btrfs` vm image.
#
# On top of the base, we add the outie startup scripts, request insight binary,
# and the innie binaries (beachhead, init.sh, ...).
#

oci_image(
    name = "outie_oci_nontesting",
    base = "//clients/beachhead/img/outie-docker-base",
    entrypoint = ["/root/startup.sh"],
    tars = [
        ":files_oci_layer_tgz",
        ":ra_ri_client_oci_layer",
        ":innie_bins_oci_layer_tgz",
        # ":vmlinuz_oci_layer_tar",
    ],
    visibility = [
        "//clients/beachhead/img:__pkg__",
    ],
)

oci_image(
    name = "outie_oci_testing",
    base = "//clients/beachhead/img/outie-docker-base",
    entrypoint = ["/root/startup.sh"],
    env = {"AUGMENT_E2E_TESTING": "1"},
    tars = [
        ":files_oci_layer_tgz",
        ":ra_ri_client_oci_layer",
        ":innie_bins_oci_layer_tgz",
        # ":vmlinuz_oci_layer_tar",
    ],
    visibility = [
        "//clients/beachhead/img:__pkg__",
        "//services/test:__subpackages__",
    ],
)

################################################################################
#
# Entrypoint / Startup Assets (startup.sh, ...)
#

tar(
    name = "files_oci_layer_tgz",
    srcs = [
        "nftables.conf",
        "startup.sh",
    ],
    out = "files.oci-layer.tgz",
    compress = "gzip",
    mtree = [
        "root               uid=0 gid=0 mode=0755 type=dir",
        "root/startup.sh    uid=0 gid=0 mode=0755 type=file time=0 content=$(location startup.sh)",
        "root/nftables.conf uid=0 gid=0 mode=0755 type=file time=0 content=$(location nftables.conf)",  # yes +x, nftables.conf has a useful shebang.
    ],
)

tar(
    name = "ra_ri_client_oci_layer",
    srcs = ["//clients/beachhead/img/outie/ra-ri-client"],
    out = "ra-ri-client.oci-layer.tgz",
    compress = "gzip",
    mtree = [
        "usr                        uid=0 gid=0 mode=0755 type=dir",
        "usr/local                  uid=0 gid=0 mode=0755 type=dir",
        "usr/local/bin              uid=0 gid=0 mode=0755 type=dir",
        "usr/local/bin/ra-ri-client uid=0 gid=0 mode=0755 type=file time=0 content=$(location //clients/beachhead/img/outie/ra-ri-client)",
    ],
)

################################################################################
#
# Innie Binaries (Beachhead)
#
# The innie's init.sh and beachhead were originally baked into the innie image.
# However, the innie image is baked into the vm image (image.btrfs), and the vm
# image is not easy for everyone to rebuild.
#
# We now bake these into the outie image for easy build+deploy, mount them into the
# vm from startup.sh, and then mount them from the vm into the innie container to use
# as the entrypoint.
#
# They're written directly under `/root/virtiofs` which is already mounted into the VM.
#

tar(
    name = "innie_bins_oci_layer_tgz",
    srcs = [
        "//clients/beachhead",
        "//clients/beachhead/img/innie:git-credential-helper",
        "//clients/beachhead/img/innie:init.sh",
    ],
    out = "innie-bins.oci-layer.tgz",
    compress = "gzip",
    mtree = [
        "root                                          uid=0 gid=0 mode=0755 type=dir",
        "root/virtiofs                                 uid=0 gid=0 mode=0755 type=dir",
        "root/virtiofs/agent-bin                       uid=0 gid=0 mode=0755 type=dir",
        "root/virtiofs/agent-bin/augment-agent         uid=0 gid=0 mode=0755 type=file time=0 content=$(location //clients/beachhead:beachhead)",
        "root/virtiofs/agent-bin/init.sh               uid=0 gid=0 mode=0755 type=file time=0 content=$(location //clients/beachhead/img/innie:init.sh)",
        "root/virtiofs/agent-bin/git-credential-helper uid=0 gid=0 mode=0755 type=file time=0 content=$(location //clients/beachhead/img/innie:git-credential-helper)",
    ],
)

# NOTE(mattm): This vmlinuz is currently unused. We're baking /clients/beachhead/img/kernel:vmlinuz into
# the outie docker base until it can be built from CI.
tar(
    name = "vmlinuz_oci_layer_tar",
    srcs = ["//clients/beachhead/img/kernel:vmlinuz"],
    out = "vmlinuz.oci-layer.tar",
    mtree = [
        "root         uid=0 gid=0 mode=0755 type=dir",
        "root/vmlinuz uid=0 gid=0 mode=0644 time=0 type=file content=$(location //clients/beachhead/img/kernel:vmlinuz)",
    ],
)

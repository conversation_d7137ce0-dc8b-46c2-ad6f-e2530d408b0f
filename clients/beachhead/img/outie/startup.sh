#!/bin/bash

set -eu -o pipefail

# Record script start time with nanosecond precision
declare -r script_start_time="$(date +%s.%N)"

elapsed() {
	declare -r offset="${1:-0}"
	declare -r end="$(date +%s.%N)"
	awk "BEGIN {print $end - $script_start_time - $offset}"
}

log() {
	declare -r lvl="$1"
	shift
	declare -r msg="$1"
	shift

	declare -r now="$(date +"%Y-%m-%d %H:%M:%S.%N")"
	declare -r elapsed="$(elapsed)"

	# Build the extra fields string
	declare extra_fields=""
	for arg in "$@"; do
		# Split key=value pairs
		IFS='=' read -r key value <<<"$arg"
		if [[ -n "$key" && -n "$value" ]]; then
			extra_fields="${extra_fields}, \"${key}\": \"${value}\""
		fi
	done

	printf '{"time": "%s", "level": "%s", "msg": "%s", "elapsed_sec": "%.6f"%s}\n' "$now" "$lvl" "$msg" "$elapsed" "$extra_fields"
}
info() {
	log "INFO" "$@"
}
warn() {
	log "WARN" "$@"
}
err() {
	log "ERROR" "$@"
}
pipelog() {
	declare -r now="$(date +"%Y-%m-%d %H:%M:%S.%N")"
	declare -r elapsed=$(awk "BEGIN {print $(date +%s.%N) - $script_start_time}")
	declare -r lvl="INFO" # Add a level to help keep the logs aligned in stdout

	# Build the extra fields string
	declare extra_fields=""
	for arg in "$@"; do
		# Split key=value pairs
		IFS='=' read -r key value <<<"$arg"
		if [[ -n "$key" && -n "$value" ]]; then
			extra_fields="${extra_fields}, \"${key}\": \"${value}\""
		fi
	done

	while read -r line; do
		# Strip any control characters (looking at you, ^M)
		line=$(echo "$line" | tr -d '\000-\037')
		# Escape any double quotes
		line=${line//\"/\\\"}
		printf '{"time": "%s", "level": "%s", "msg": "%s", "elapsed_sec": %.6f%s}\n' "$now" "$lvl" "$line" "$elapsed" "$extra_fields"
	done
}

main() {
	declare -r br='br0' outie_ip='**********/30'
	declare root_dev='/dev/vm-root' root_src='/root/image.btrfs'
	declare -r persist_dev='/dev/persist'
	declare -r virtiofs='/root/virtiofs'

	info "Augment Remote Agent Workspace (Outie) Starting" "event=startup"

	networking_setup "$br" "$outie_ip" 'component=network-setup'
	if [[ -b "$root_dev" ]]; then
		prep_root "$root_dev" "$root_src" 'component=root-disk-setup'
		declare -r root_dev
	else
		warn "Root device $root_dev does not exist. Using $root_src image directly instead." 'component=root-disk-setup'
		declare -r root_dev="$root_src"
	fi
	prep_persist "$persist_dev" 'component=persist-disk-setup'
	prep_virtiofs "$virtiofs" "$@"

	run_vm "$br" "$root_dev" "$virtiofs" "$persist_dev" 'component=qemu'

	run_systemd_journal_remote '**********:19532' 'component=systemd-journal-remote'
	run_non_pii_logs_to_stdout 'component=journalctl'
	wait_for_workspace_config '/root/virtiofs/workspace-config/workspace-agent-config.json' 'component=workspace-config'
	run_ri_log_processor '/root/virtiofs/workspace-config/workspace-agent-config.json' '<EMAIL>' 'component=request-insight'
	wait_for_ssh 'root@milchick' 'component=ssh-connect-test'
	wait_for_bh 'http://milchick:9999' 'component=beachhead-health-test'

	info "Startup complete, entering sleep" "component=agent" "event=startup-complete"
	sleep inf
}

networking_setup() {
	declare -r br="$1" outie_ip="$2"
	shift 2
	declare -ra log_fields=("$@")
	declare -r start="$(elapsed)"

	info "BEGIN networking_setup" "${log_fields[@]}"

	info "Enabling net.ipv4.ip_forward" "${log_fields[@]}"
	sysctl -q -w net.ipv4.ip_forward=1 |& pipelog "${log_fields[@]}"

	info "Initial ip link info" "${log_fields[@]}"
	ip -o a |& pipelog "${log_fields[@]}"

	if ! ip -o l | grep -q "$br"; then
		info "Creating Bridge $br." "${log_fields[@]}"
		ip link add name "$br" type bridge |& pipelog "${log_fields[@]}"
	fi
	if ! ip -o a show "$br" | grep -q "$outie_ip"; then
		info "Assigning $outie_ip to $br" "${log_fields[@]}"
		ip a add "$outie_ip" dev "$br" |& pipelog "${log_fields[@]}"
	fi

	info "Setting $br link up." "${log_fields[@]}"
	ip l set "$br" up |& pipelog "${log_fields[@]}"

	install -oroot -groot -m0644 <(printf "allow %s\n" "$br") -D /etc/qemu/bridge.conf |& pipelog "${log_fields[@]}"

	info "Loading /root/nftables.conf" "${log_fields[@]}"
	nft -f /root/nftables.conf |& pipelog "${log_fields[@]}"
	info "Loaded nftables configuration" "${log_fields[@]}"

	cat >>/etc/hosts <<-EOF
		********** outie
		********** milchick-out milchick
		********** milchick-in
		********** innie
	EOF

	echo "ssh milchick" >>/root/.bash_history

	declare -r duration="$(elapsed "$start")"
	info "END networking_setup" "${log_fields[@]}" "startup_cost=$duration"
}

prep_root() {
	declare -r dev="$1" src="$2"
	shift 2
	declare -ra log_fields=("$@")
	declare -r start="$(elapsed)"

	info "BEGIN prep_root: $dev (image: $src)." "${log_fields[@]}" 'event=start'

	if [[ ! -b "$dev" ]]; then
		err "SKIP prep_root: $dev does not exist." "${log_fields[@]}"
		return
	fi

	declare -r info="$(blkid "$dev" -s TYPE -o value)"
	if [[ "$info" ]]; then
		info "SKIP prep_root: $dev already contains filesystem: $info." "${log_fields[@]}"
		return
	fi

	dd if="$src" of="$dev" bs=1M conv=sparse iflag=nonblock oflag=nonblock |& pipelog "${log_fields[@]}"

	declare -r duration="$(elapsed "$start")"
	info "END prep_root." "${log_fields[@]}" 'event=complete' "startup_cost=$duration"
}

prep_persist() {
	declare -r dev="$1"
	shift 1
	declare -ra log_fields=("$@")
	declare -r start="$(elapsed)"

	info "BEGIN prep_persist: $dev." "${log_fields[@]}" 'event=start'

	if [[ ! -b "$dev" ]]; then
		err "SKIP prep_persist: $dev does not exist." "${log_fields[@]}"
		return
	fi

	declare -r info="$(blkid "$dev" -s TYPE -o value)"
	if [[ "$info" ]]; then
		info "SKIP prep_persist: $dev already contains filesystem: $info." "${log_fields[@]}"
		return
	fi

	mkfs.ext4 -L persist "$dev" |& pipelog "${log_fields[@]}"

	declare -r duration="$(elapsed "$start")"
	info "END prep_persist." "${log_fields[@]}" 'event=complete' "startup_cost=$duration"
}

prep_virtiofs() {
	declare -r k8s='/run/secrets/workspace-config'
	declare -r dst_vm="$1"
	declare -r dst_innie="$dst_vm/workspace-config"
	shift

	mkdir -p "$dst_vm" "$dst_innie"

	### VM

	info "Recording startup args for innie into RootFS: $*"
	printf "%s\n" "$*" >"$dst_vm/startup-args.txt"

	[[ -e "$k8s/revtun_kubeconfig" ]] && cp -L "$k8s/revtun_kubeconfig" "$dst_vm/"
	[[ -e "$k8s/revtun_path_prefix" ]] && cp -L "$k8s/revtun_path_prefix" "$dst_vm/"

	### Innie

	info "Copying select workspace-config files for 9p virtiofs mount."
	zcat "$k8s"/workspace-agent-config.json.gz >"$dst_innie"/workspace-agent-config.json
}

run_vm() {
	declare -r br="$1"
	declare -r img="$2"
	declare -r virtiofs="$3"
	declare -r persist_dev="$4"
	shift 4
	declare -ra log_fields=("$@")
	declare -r kernel='/root/vmlinuz'
	declare -ri cpus=4
	declare -r ram='16G'

	declare -a cmd=(
		qemu-system-x86_64
		-enable-kvm

		-M microvm,x-option-roms=off,pit=off,pic=off,isa-serial=off,rtc=off
		-nodefaults
		-no-user-config -nographic -display none
		-cpu host -smp "$cpus" -m "$ram"

		-kernel "$kernel"
		-append "console=hvc0 root=/dev/vda fsck.mode=skip rootflags=compress=zstd:6 rw"

		-chardev stdio,id=virtiocon0
		-device virtio-serial-device
		-device virtconsole,chardev=virtiocon0

		-netdev bridge,br="$br",id=net0
		-device virtio-net-device,netdev=net0

		-drive id=root0,file="$img",format=raw,if=none,discard=on
		-device virtio-blk-device,drive=root0

		-fsdev local,path="$virtiofs",security_model=passthrough,id=cfg1
		-device virtio-9p-device,fsdev=cfg1,mount_tag=cfg1
	)

	if [[ -b "$persist_dev" ]]; then
		cmd+=(
			-drive id=persist0,file="$persist_dev",format=raw,if=none,discard=on
			-device virtio-blk-device,drive=persist0
		)
	fi

	declare -r sleep="1s"
	while :; do
		info "QEMU Milchick starting." "${log_fields[@]}"
		"${cmd[@]}" |& pipelog "${log_fields[@]}"
		warn "QEMU Milchick exited [$?]. Sleeping for $sleep" "${log_fields[@]}"
		sleep "$sleep"
	done &
}

run_systemd_journal_remote() {
	declare -r addr="$1"
	shift 1
	declare -ra log_fields=("$@")

	mkdir -p /var/log/journal

	declare -r sleep="1s"
	while true; do
		info "Running systemd-journal-remote"
		/usr/lib/systemd/systemd-journal-remote --listen-http="$addr" --output=/var/log/journal |& pipelog "${log_fields[@]}"
		warn "Running systemd-journal-remote, exited [$?]. Sleeping for $sleep"
		sleep "$sleep"
	done &
}

run_non_pii_logs_to_stdout() {
	declare -ra log_fields=("$@")

	declare jq_filter='. + {
		component: ._SYSTEMD_UNIT,
		elapsed_sec: (now - ($script_start_time | tonumber) | . * 1000000 | floor | . / 1000000),
		formatted_time: (.__REALTIME_TIMESTAMP | tonumber / 1000000 | strftime("%Y-%m-%d %H:%M:%S.%3N"))
	}'

	if [[ -z "${AUGMENT_E2E_TESTING:-}" ]]; then
		jq_filter="select(._SYSTEMD_UNIT != \"<EMAIL>\") | $jq_filter"
	else
		jq_filter="$jq_filter + {augment_e2e_testing: 1}"
	fi

	while true; do
		info "Starting log processor for non-docker logs" "${log_fields[@]}" "event=start"
		journalctl -f -o json -q --cursor-file=/root/journalctl-cursor |
			jq -c "$jq_filter" --arg script_start_time "$script_start_time"
		info "for non-docker logs exited" "${log_fields[@]}" "event=exit"
		sleep 1s
	done &
}

wait_for_workspace_config() {
	declare -r path="$1"
	shift
	declare -ra log_fields=("$@")

	info "Waiting for workspace config to be mounted" "${log_fields[@]}"
	while :; do
		[[ -e "$path" ]] && break || sleep 1
	done
	info "Workspace config mounted" "${log_fields[@]}" 'event=config-found'
}

run_ri_log_processor() {
	declare -r wscfg="$1" systemd_unit="$2"
	shift 2
	declare -ra log_fields=("$@")

	# Pull the agent ID, project ID, and topic name from the workspace config.
	declare -r agent_id="$(cat "$wscfg" | jq -r '.remote_agent_id')"
	declare -r tenant_id="$(cat "$wscfg" | jq -r '.tenant_id')"
	declare -r tenant_name="$(cat "$wscfg" | jq -r '.tenant_name')"
	declare -r topic_name="$(cat "$wscfg" | jq -r '.request_insight_topic_name')"
	# project is system-services-prod unless the topic starts with dev-
	declare -r project_id="$(echo "$topic_name" | grep -q '^dev-' && echo 'system-services-dev' || echo 'system-services-prod')"

	declare -ra cmd=(
		/usr/local/bin/ra-ri-client
		--systemd-unit="$systemd_unit"
		--project-id="$project_id"
		--topic="$topic_name"
		--session-id="$agent_id"
		--tenant-id="$tenant_id"
		--tenant-name="$tenant_name"
	)

	while true; do
		info "Starting ra-ri-client log processor for docker" "${log_fields[@]}" "event=start"
		"${cmd[@]}"
		info "ra-ri-client exited for docker" "${log_fields[@]}" "event=exit"
		sleep 1s
	done &
}

wait_for_ssh() {
	declare -r host="$1"
	shift
	declare -ra log_fields=("$@")
	declare -r start="$(elapsed)"

	info "Waiting for VM to come up" "${log_fields[@]}"
	while :; do
		if ssh -o ConnectTimeout=1 -o StrictHostKeyChecking=no "$host" "echo hello from $(hostname)" |& pipelog "${log_fields[@]}"; then
			break
		fi
		sleep 1
	done

	declare -r duration="$(elapsed "$start")"
	info "VM connection established" "${log_fields[@]}" "event=connect" "startup_cost=$duration"
}

wait_for_bh() {
	declare -r url="$1"
	shift
	declare -ra log_fields=("$@")
	declare -r start="$(elapsed)"

	local status=""

	info "START: wait_for_bh." "${log_fields[@]}"
	while :; do
		if ! status="$(curl -s "$url/health" -H 'Accept: application/json' 2>&1)"; then
			info "WAIT: wait_for_bh: not ready: $status." "${log_fields[@]}"
		elif [[ "$(echo "$status" | jq -r '.status')" != "OK" ]]; then
			status=${status//\"/\\\"}
			info "WAIT: wait_for_bh: not healthy: $status." "${log_fields[@]}"
		else
			break
		fi
		sleep 1
	done

	declare -r duration="$(elapsed "$start")"
	status=${status//\"/\\\"}
	info "END: wait_for_bh: $status." "${log_fields[@]}" "event=connect" "startup_cost=$duration"
}

main "$@"

package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

// LogEntry represents a single journalctl log entry in JSON format
type LogEntry struct {
	Message   string    `json:"MESSAGE"`
	Transport string    `json:"_TRANSPORT"`
	Timestamp time.Time `json:"-"` // We'll handle this field manually
	// Raw timestamp from journalctl (microseconds since epoch as string)
	RawTimestamp string `json:"__REALTIME_TIMESTAMP"`
	// Add other fields as needed
}

// UnmarshalJSON implements the json.Unmarshaler interface for LogEntry
func (e *LogEntry) UnmarshalJSON(data []byte) error {
	// Define a temporary struct to unmarshal the JSON data
	type TempLogEntry struct {
		Message      string `json:"MESSAGE"`
		Transport    string `json:"_TRANSPORT"`
		RawTimestamp string `json:"__REALTIME_TIMESTAMP"`
	}

	var temp TempLogEntry
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	// Copy the standard fields
	e.Message = temp.Message
	e.Transport = temp.Transport
	e.RawTimestamp = temp.RawTimestamp

	// Parse the timestamp (microseconds since epoch)
	if temp.RawTimestamp != "" {
		// Convert the string to int64
		var microseconds int64
		_, err := fmt.Sscanf(temp.RawTimestamp, "%d", &microseconds)
		if err != nil {
			return fmt.Errorf("failed to parse timestamp '%s': %w", temp.RawTimestamp, err)
		}

		// Convert microseconds to time.Time
		seconds := microseconds / 1000000
		remainingMicroseconds := microseconds % 1000000
		e.Timestamp = time.Unix(seconds, remainingMicroseconds*1000)
	}

	return nil
}

// LogProcessor processes journalctl logs and sends them to pub/sub
type LogProcessor struct {
	pubsubClient   PubSubClient
	maxMessageSize int
	batchTimeout   time.Duration
	buffer         []LogEntry
	bufferMutex    sync.Mutex
	batchTimer     *time.Timer
	firstEntry     time.Time
	currentMsgSize int
	sessionID      string
	maxLines       int  // Maximum number of lines to process (0 = unlimited)
	linesProcessed int  // Number of lines processed so far
	traceEnabled   bool // Whether to enable trace logging
}

// NewLogProcessor creates a new log processor
func NewLogProcessor(pubsubClient PubSubClient, maxMessageSize int, batchTimeout time.Duration,
	customSessionID string, tenantID string, tenantName string,
	maxLines int, traceEnabled bool,
) *LogProcessor {
	initialCapacity := 1000 // Initial capacity for the buffer

	// Use the provided session ID
	sessionID := customSessionID

	// If the pubsub client is a RequestInsightPubSubClient, set the session ID and tenant info
	if riClient, ok := pubsubClient.(*RequestInsightPubSubClient); ok {
		riClient.SetSessionID(sessionID)
		riClient.SetTenantInfo(tenantID, tenantName)
	}

	// Create a new processor with an initialized timer
	processor := &LogProcessor{
		pubsubClient:   pubsubClient,
		maxMessageSize: maxMessageSize,
		batchTimeout:   batchTimeout,
		buffer:         make([]LogEntry, 0, initialCapacity),
		currentMsgSize: 0,
		sessionID:      sessionID,
		maxLines:       maxLines,
		linesProcessed: 0,
		traceEnabled:   traceEnabled,
	}

	// Initialize the timer
	processor.batchTimer = time.NewTimer(batchTimeout)
	// Stop the timer initially since we don't have any logs yet
	if !processor.batchTimer.Stop() {
		// Drain the channel if the timer has already fired
		select {
		case <-processor.batchTimer.C:
		default:
		}
	}

	return processor
}

// ProcessLogs starts processing journalctl logs
func (p *LogProcessor) ProcessLogs(ctx context.Context, systemdUnit string) error {
	// TODO use https://github.com/coreos/go-systemd/v22@v22.5.0/sdjournal instead of running journalctl as a command
	// Start journalctl command to get logs in JSON format
	cmd := exec.CommandContext(ctx, "journalctl", "-q", "-u", systemdUnit, "-f", "-o", "json", "--cursor-file=/root/ra-ri-cursor")
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to get stdout pipe: %w", err)
	}

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start journalctl: %w", err)
	}

	// Create a scanner to read lines from journalctl
	scanner := bufio.NewScanner(stdout)
	scanner.Buffer(make([]byte, 1024*1024), 1024*1024) // 1MB buffer

	// Start a goroutine to process the timeout
	go p.processTimeouts(ctx)

	// Create a context that can be cancelled when we reach the max lines
	var cancel context.CancelFunc
	if p.maxLines > 0 {
		// If maxLines is set, create a context that can be cancelled
		ctx, cancel = context.WithCancel(ctx)
		defer cancel()
	}

	// Process each line
	for scanner.Scan() {
		select {
		case <-ctx.Done():
			// Make sure to send any remaining logs before exiting
			p.bufferMutex.Lock()
			if p.currentMsgSize > 0 {
				log.Debug().Int("count", len(p.buffer)).Msg("Sending final batch before exiting")
				if err := p.sendBatch(ctx); err != nil {
					log.Error().Err(err).Msg("Failed to send final batch")
				}
			}
			p.bufferMutex.Unlock()
			return ctx.Err()
		default:
			line := scanner.Text()
			if err := p.processLogLine(ctx, line); err != nil {
				log.Error().Err(err).Msg("Failed to process log line")
			}

			// Increment the lines processed counter
			p.linesProcessed++

			// Check if we've reached the maximum number of lines
			if p.maxLines > 0 && p.linesProcessed >= p.maxLines {
				log.Info().Int("max_lines", p.maxLines).Msg("Reached maximum number of lines, exiting")

				// Make sure to send any remaining logs before exiting
				p.bufferMutex.Lock()
				if p.currentMsgSize > 0 {
					log.Debug().Int("count", p.currentMsgSize).Msg("Sending final batch before exiting")
					if err := p.sendBatch(ctx); err != nil {
						log.Error().Err(err).Msg("Failed to send final batch")
					}
				}
				p.bufferMutex.Unlock()

				// Cancel the context to signal that we're done
				if cancel != nil {
					cancel()
				}
				return nil
			}
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("scanner error: %w", err)
	}

	if err := cmd.Wait(); err != nil {
		return fmt.Errorf("journalctl command failed: %w", err)
	}

	return nil
}

func processAugmentEvent(messageData map[string]interface{}) {
	if _, ok := messageData["_augment_event"]; ok {
		// If _augment_event is present, ensure __REALTIME_TIMESTAMP exists
		if _, tsExists := messageData["__REALTIME_TIMESTAMP"]; !tsExists {
			// Use current time if __REALTIME_TIMESTAMP is not in the event payload
			nowMicros := time.Now().UnixNano() / 1000 // Convert nanoseconds to microseconds
			messageData["__REALTIME_TIMESTAMP"] = fmt.Sprintf("%d", nowMicros)
		}
		// re-log the event so it goes to gcp, including all the fields in the json object
		logEvent := log.Info().Int("PRIORITY", 6).Str("SYSLOG_IDENTIFIER", "augment-agent").Str("_AUGMENT_EVENT", "true").Str("component", "augment-agent")

		// Add all fields from messageData to the log event
		for key, value := range messageData {
			if key == "MESSAGE" {
				continue
			}
			switch v := value.(type) {
			case string:
				logEvent = logEvent.Str(key, v)
			case int:
				logEvent = logEvent.Int(key, v)
			case float64:
				logEvent = logEvent.Float64(key, v)
			case bool:
				logEvent = logEvent.Bool(key, v)
			default:
				// For complex types, convert to string
				logEvent = logEvent.Interface(key, v)
			}
		}
		if msgValue, ok := messageData["MESSAGE"]; ok && msgValue != nil {
			if msgStr, ok := msgValue.(string); ok {
				logEvent.Msg(msgStr)
			} else {
				logEvent.Interface("MESSAGE", msgValue).Msg("_augment_event")
			}
		} else {
			logEvent.Msg("_augment_event")
		}
	}
}

// processLogLine processes a single log line
func (p *LogProcessor) processLogLine(ctx context.Context, line string) error {
	var entry LogEntry
	if err := json.Unmarshal([]byte(line), &entry); err != nil {
		return fmt.Errorf("failed to unmarshal log entry: %w", err)
	}

	// If the timestamp is zero, set it to the current time
	if entry.Timestamp.IsZero() {
		log.Debug().
			Str("raw_timestamp", entry.RawTimestamp).
			Msg("Using current time for entry with invalid timestamp")
		entry.Timestamp = time.Now()
	}

	// Check for _augment_event in the message
	var messageData map[string]interface{}
	if err := json.Unmarshal([]byte(entry.Message), &messageData); err == nil {
		processAugmentEvent(messageData)
	}

	// Log the entry content if trace is enabled
	if p.traceEnabled {
		log.Debug().
			Str("message", entry.Message).
			Str("transport", entry.Transport).
			Str("timestamp", entry.Timestamp.Format(time.RFC3339Nano)).
			Msg("Processed log entry")
	}

	p.bufferMutex.Lock()
	defer p.bufferMutex.Unlock()

	// If this is the first entry in the batch, start the timer
	if len(p.buffer) == 0 {
		p.firstEntry = time.Now()
		p.resetBatchTimer(ctx)
		p.currentMsgSize = 0
	}

	// Add the entry to the buffer
	p.buffer = append(p.buffer, entry)

	// Track the message size
	p.currentMsgSize += len(entry.Message)

	// If we've reached the max message size, send the batch
	if p.currentMsgSize >= p.maxMessageSize {
		log.Debug().Int("count", p.currentMsgSize).Msg("Sending batch due to max message size")
		return p.sendBatch(ctx)
	}

	return nil
}

// processTimeouts handles the batch timeout
func (p *LogProcessor) processTimeouts(ctx context.Context) {
	// Safety check - the timer should be initialized in NewLogProcessor
	if p.batchTimer == nil {
		log.Error().Msg("Timer is nil in processTimeouts, this should never happen")
		// Initialize the timer as a fallback
		p.batchTimer = time.NewTimer(p.batchTimeout)
	}

	for {
		select {
		case <-ctx.Done():
			return
		case <-p.batchTimer.C:
			p.bufferMutex.Lock()
			if p.currentMsgSize > 0 {
				log.Debug().Int("count", p.currentMsgSize).Msg("Sending batch due to timeout")
				if err := p.sendBatch(ctx); err != nil {
					log.Error().Err(err).Msg("Failed to send batch on timeout")
				}
			}
			// Reset the timer for the next batch
			p.batchTimer.Reset(p.batchTimeout)
			p.bufferMutex.Unlock()
		}
	}
}

// resetBatchTimer resets the batch timer
func (p *LogProcessor) resetBatchTimer(ctx context.Context) {
	// The timer should always be initialized in NewLogProcessor
	// This is a safety check
	if p.batchTimer == nil {
		log.Warn().Msg("Timer was nil in resetBatchTimer, initializing")
		p.batchTimer = time.NewTimer(p.batchTimeout)
		return
	}

	// Stop the timer
	if !p.batchTimer.Stop() {
		// Drain the channel if the timer has already fired
		select {
		case <-p.batchTimer.C:
		default:
		}
	}

	// Reset the timer
	p.batchTimer.Reset(p.batchTimeout)
}

// GetSessionID returns the session ID
func (p *LogProcessor) GetSessionID() string {
	return p.sessionID
}

// sendBatch sends the current batch of log entries
func (p *LogProcessor) sendBatch(ctx context.Context) error {
	if p.currentMsgSize == 0 {
		return nil
	}

	// Generate a unique event ID
	eventID := fmt.Sprintf("log-batch-%d", time.Now().UnixNano())

	log.Debug().
		Int("count", len(p.buffer)).
		Int("message_size", p.currentMsgSize).
		Str("start", p.buffer[0].Timestamp.Format(time.RFC3339)).
		Str("end", p.buffer[len(p.buffer)-1].Timestamp.Format(time.RFC3339)).
		Str("event_id", eventID).
		Str("session_id", p.sessionID).
		Msg("Prepared batch for publishing to pub/sub")

	// If trace is enabled, log the contents of each entry in the batch
	if p.traceEnabled {
		for i, entry := range p.buffer {
			log.Debug().
				Int("index", i).
				Str("message", entry.Message).
				Str("transport", entry.Transport).
				Str("timestamp", entry.Timestamp.Format(time.RFC3339Nano)).
				Str("event_id", eventID).
				Msg("Sending log entry in batch")
		}
	}

	// Send to pub/sub directly with the entries
	if err := p.pubsubClient.PublishRemoteAgentLog(ctx, p.buffer, p.buffer[0].Timestamp, p.buffer[len(p.buffer)-1].Timestamp); err != nil {
		return fmt.Errorf("failed to publish to pub/sub: %w", err)
	}

	// Clear the buffer
	p.buffer = p.buffer[:0]
	p.currentMsgSize = 0

	return nil
}

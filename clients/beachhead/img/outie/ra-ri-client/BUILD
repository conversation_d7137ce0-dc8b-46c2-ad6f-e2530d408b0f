load("//tools/bzl:go.bzl", "go_binary", "go_test")

go_binary(
    name = "ra-ri-client",
    srcs = [
        "log_processor.go",
        "main.go",
        "pubsub_client.go",
    ],
    visibility = ["//clients/beachhead/img/outie:__pkg__"],
    deps = [
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_spf13_pflag//:pflag",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "ra-ri-client_test",
    srcs = [
        "log_processor.go",
        "log_processor_test.go",
        "main.go",
        "pubsub_client.go",
        "pubsub_client_test.go",
    ],
    deps = [
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_spf13_pflag//:pflag",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

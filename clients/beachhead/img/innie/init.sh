#!/bin/bash

set -eu -o pipefail

# Function to log a JSON event
# Arguments should be in key="value" format
log_json_event() {
	local jq_args_array=()

	# Add timestamp first
	local now_micros=$(($(date +%s%N) / 1000))
	jq_args_array+=(--arg "__REALTIME_TIMESTAMP" "$now_micros")

	for arg in "$@"; do
		local key="${arg%%=*}"
		local value="${arg#*=}"

		# Remove surrounding quotes from value if they were part of the input format key="value"
		# e.g. if arg is foo="bar", value becomes bar (without quotes)
		# if arg is foo=bar, value remains bar
		if [[ "$value" == \"*\" && "$value" == *\" ]]; then
			value="${value#\"}" # Remove leading quote
			value="${value%\"}" # Remove trailing quote
		fi

		jq_args_array+=(--arg "$key" "$value")
	done

	# Use jq to create a compact JSON object from the named arguments
	jq -n -c '$ARGS.named' "${jq_args_array[@]}"
}

main() {
	eval set -- $(getopt -n "$0" -o 'p:c:' --long 'id:,config:,persist:,port:,ssh-port:,api-url:' -- "$@")

	declare id="" persist="" api_url="" cfg=""
	declare -i port=0 ssh_port=0
	while true; do
		case "$1" in
		--)
			shift
			break
			;;
		--id)
			id="$2"
			shift 2
			continue
			;;
		-c | --config)
			cfg="$2"
			shift 2
			continue
			;;
		--persist)
			persist="$2"
			shift 2
			continue
			;;
		-p | --port)
			port="$2"
			shift 2
			continue
			;;
		--ssh-port)
			ssh_port="$2"
			shift 2
			continue
			;;
		--api-url)
			api_url="$2"
			shift 2
			continue
			;;
		*)
			printf "Invalid Option: %s\n" "$1" >&2
			return 1
			;;
		esac
	done
	declare -r id cfg persist="${persist:-$HOME}" port ssh_port api_url
	declare -r ws="${persist}/workspace"
	declare -r storage="${persist}/storage"

	# Parse Config
	if [[ ! -r "$cfg" ]]; then
		printf -- "--config=<path> is required and must be a readable json file, with an optional .gz extension.\n" >&2
		return 1
	fi

	declare -r token="$(cat "$cfg" | jq '.config.workspace_setup.token // ""' -r)"

	# Look for augment-agent and git-credential-helper relative to init.sh, with fallbacks to the legacy paths
	declare -r augment_agent_bin="$(
		local b="$(dirname "$0")/augment-agent"
		[[ -e "$b" ]] && echo -n "$b" || echo -n "/usr/local/bin/augment-agent"
	)"
	declare -r github_cred_helper_bin="$(
		local b="$(dirname "$0")/git-credential-helper"
		[[ -e "$b" ]] && echo -n "$b" || echo -n "/usr/local/bin/git-credential-helper"
	)"

	# Launch sshd (in background)
	launch_sshd "$persist" "$ssh_port"
	launch_docker || true &

	# Set a credential helper that can read auth details from the workspace config
	git config set --global 'credential.https://github.com.helper' "$github_cred_helper_bin $cfg"

	# Configure any values set in git_config, if not already set
	while read key val; do
		if [[ -z "$(git config get --global --includes ${key} 2>/dev/null || echo -n '')" ]]; then
			git config set --global "$key" "$val" || echo "Failed to set git config $key=$val" >&2
		fi
	done < <(cat "$cfg" | jq '.config.workspace_setup.git_config // {} | to_entries[] | [.key, .value] | @tsv' -r)

	# Configure git user name and email if not already set and if values are provided
	declare -r gh_name="$(cat "$cfg" | jq '.github_name // ""' -r)"
	declare -r gh_email="$(cat "$cfg" | jq '.github_email // ""' -r)"
	if [[ "$gh_name" ]] && [[ -z "$(git config get --global --includes user.name 2>/dev/null || echo -n '')" ]]; then
		git config set --global user.name "$gh_name"
	fi
	if [[ "$gh_email" ]] && [[ -z "$(git config get --global --includes user.email 2>/dev/null || echo -n '')" ]]; then
		git config set --global user.email "$gh_email"
	fi

	# Make the workspace the default dir on login
	declare -r profile_d="/etc/profile.d/90-augment-workspace.sh"
	sudo install -m0644 /dev/null -D "$profile_d"
	printf '[ `id -u` -eq %d ] && [ -d %s ] && cd %s || true\n' "$(id -u)" "$ws" "$ws" | sudo tee -a "$profile_d"

	declare -a cmd=(
		"$augment_agent_bin"
		--workspace-root="$ws"
		--workspace-agent-config="$cfg"
		--persistent-root="$storage"
	)

	if ((port > 0)); then
		cmd+=(--port="$port")
	fi
	if ((ssh_port > 0)); then
		cmd+=(--ssh-port="$ssh_port")
	fi
	if [[ "$api_url" ]]; then
		cmd+=(--api-url="$api_url")
	fi

	source /etc/profile
	set +e
	declare -r max_starts=5
	declare -i starts=1
	declare -i current_sleep_duration=5
	while true; do
		log_json_event "_augment_event"="beachhead_start" "start_count"="$starts" "MESSAGE"="Starting beachhead (attempt $starts/$max_starts)"
		printf "Running: AUGMENT_API_TOKEN=**** %s\n" "${cmd[*]} (attempt $starts/$max_starts)" >&2
		AUGMENT_API_TOKEN="$token" "${cmd[@]}"
		rc="$?"
		((starts++))
		restart_count=$((starts - 1))
		((starts > max_starts)) && {
			log_json_event "_augment_event"="beachhead_failure" "restart_count"="$restart_count" "rc=$rc" "MESSAGE"="Beachhead failed to start after $max_starts attempts"
			printf "Restart limit reached, exiting...\n" >&2
			exit $rc
		}

		if ((starts > 1)); then
			log_json_event "_augment_event"="beachhead_restart" "restart_count"="$restart_count" "rc=$rc" "MESSAGE"="Restarting beachhead (attempt $starts/$max_starts)"
		fi
		sleep $current_sleep_duration
		current_sleep_duration=$((current_sleep_duration + 10))
	done
	AUGMENT_API_TOKEN="$token" "${cmd[@]}"
}

launch_sshd() {
	declare -r storage="$1"
	declare -ri port="$2"

	declare -r keysdir="$storage/ssh-host-keys"
	declare -r keysdir_full="$keysdir/etc/ssh"

	### Authorized Keys (make sure permissions are correct)

	if [[ ! -e "$HOME/.ssh/authorized_keys" ]]; then
		install -m0640 -D /dev/null "$HOME/.ssh/authorized_keys"
		printf '# SSH Authorized Keys\n' >"$HOME/.ssh/authorized_keys"
	fi

	### HostKeys setup

	if [[ ! -d "$keysdir" ]]; then
		printf "Creating SSH HostKeys in %s...\n" "$keysdir" >&2
		mkdir -p "$keysdir_full"
		ssh-keygen -A -f "$keysdir"
		rm -f "$keysdir_full"/ssh_host_dsa_key* # filter out deprecated cruft
	fi

	### SSH daemon

	declare -a cmd=(
		/usr/sbin/sshd -eD
		-h "$keysdir_full/ssh_host_rsa_key"
		-h "$keysdir_full/ssh_host_ecdsa_key"
		-h "$keysdir_full/ssh_host_ed25519_key"
	)
	if ((port > 0)); then
		cmd+=(-p "$port")
	fi
	printf "Running: %s\n" "${cmd[*]}" >&2
	"${cmd[@]}" &
}

launch_docker() {
	sudo /usr/bin/update-alternatives --set iptables /usr/sbin/iptables-legacy

	if [[ "$AUGMENT_AGENT_WORKSPACE_IMAGE" ]]; then
		printf "Adding AUGMENT_AGENT_WORKSPACE_IMAGE=%s to profile.\n" "$AUGMENT_AGENT_WORKSPACE_IMAGE" >&2
		sudo install -o root -g root -m0644 -D /dev/stdin /etc/profile.d/10-augment-agent-workspace-image.sh <<<"$(
			printf 'export AUGMENT_AGENT_WORKSPACE_IMAGE=%s\n' "$AUGMENT_AGENT_WORKSPACE_IMAGE"
		)"
	else
		printf "WARNING: AUGMENT_AGENT_WORKSPACE_IMAGE is not set.\n" >&2
	fi

	printf "Starting containerd (in the background)...\n" >&2
	sudo containerd &

	printf "Starting dockerd (in the background)...\n" >&2
	sudo rm -f /var/run/docker.pid /var/run/docker.sock
	sudo dockerd --containerd=/run/containerd/containerd.sock &

	declare -i attempts=0
	declare -i sleep=1
	while true; do
		printf 'Checking for dockerd readiness...\n' >&2
		if docker images; then
			printf 'Checking for dockerd readiness... ready!\n'
			break
		else
			((attempts++))
			((attempts > 60)) && sleep=30
			((attempts > 600)) && sleep=300
			printf 'Checking for dockerd readiness... not ready (sleeping %s)...\n' "$sleep" >&2
			sleep "$sleep"
		fi
	done

	if [[ -d /var/lib/docker-images/ ]]; then
		for img in /var/lib/docker-images/*; do
			[[ -r "$img" ]] || continue
			echo docker load -i "$img" >&2
			docker load -i "$img" || true
		done &
	fi
}

main "$@"

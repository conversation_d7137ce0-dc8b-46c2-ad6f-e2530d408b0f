load("@aspect_bazel_lib//lib:tar.bzl", "mtree_mutate", "mtree_spec", "tar")
load("@rules_oci//oci:defs.bzl", "oci_image")

################################################################################
#
# OCI Image
#
# The base image is a Dockerfile in the `innie-docker-base` directory. It's a loaded
# `ubuntu:22.04` image with python versions, node, pnpm, homebrew, etc installed.
#
# NOTE(mattm): While the entrypoint is still set, the beachhead included, and init.sh
# included, **they're not currently used**. Instead, they're mounted in from the
# outie -> milchick -> innie so that they're easier to rebuild+redeploy.
#

oci_image(
    name = "innie_oci",
    base = "//clients/beachhead/img/innie-docker-base",
    entrypoint = [
        "/usr/bin/dumb-init",
        "--verbose",
        "--",
        "/usr/local/sbin/init.sh",
    ],
    tars = [
        ":beachhead_oci_layer_tar",
        ":sources_oci_layer_tar",
        "//tools/bzl/uv:uv_oci_layer",
    ],
    visibility = [
        "//clients/beachhead/img:__pkg__",
        "//services/test:__pkg__",
    ],
)

################################################################################
#
# OCI Image TAR -- compatible with `docker load -i`.
#
# The `oci_image()` rule produces a directory with the layer tars in a blobs/sha256/
# directory. These rules tar them up and add a `manifest.json` file so that they're
# compatible with `docker load -i`.
#

tar(
    name = "innie_oci_tar",
    srcs = [
        ":innie_oci",
        ":manifest_json",
    ],
    out = "innie.oci.tar",
    mtree = ":innie_oci_tar_mtree_with_manifest",
    visibility = [
        "//clients/beachhead/img/milchick:__pkg__",
    ],
)

genrule(
    name = "manifest_json",
    srcs = [":innie_oci"],
    outs = ["manifest.docker.json"],
    cmd_bash = """
    set -euo pipefail
    declare -r index="$(<)/index.json"
    declare -r manifest="$(<)/blobs/$$(cat "$$index" | $(JQ_BIN) -r '.manifests[0].digest|sub(":";"/")')"
    declare -r tag="augment-remote-agent:build"
    cat "$$manifest" \
    | $(JQ_BIN) --arg tag "$$tag" '[ {"Config": ("blobs/" + .config.digest | sub(":";"/")), "RepoTags": [$$tag], "Layers": [.layers[].digest | "blobs/" + sub(":";"/")], "LayerSources": (.layers|INDEX(.digest))} ]' > "$@"
    """,
    toolchains = ["@jq_toolchains//:resolved_toolchain"],
)

mtree_spec(
    name = "innie_oci_tar_mtree_auto",
    srcs = [":innie_oci"],
)

mtree_mutate(
    name = "innie_oci_tar_mtree",
    mtree = ":innie_oci_tar_mtree_auto",
    strip_prefix = package_name() + "/innie_oci",
)

genrule(
    name = "innie_oci_tar_mtree_with_manifest",
    srcs = [
        ":innie_oci_tar_mtree",
        ":manifest_json",
    ],
    outs = ["innie_oci_tar_mtree_with_manifest.mtree.txt"],
    cmd_bash = """
    set -euo pipefail
    read -r orig manifest <<<"$(SRCS)"
    cat "$$orig" > "$@"
    echo "manifest.json uid=0 gid=0 mode=0644 time=0 type=file content=$(location :manifest_json)" >> $@
    """,
)

################################################################################
#
# Innie Binaries
#
# As noted above, these are not currently used as baked into the innie image.
# Instead, they're baked into the outie image and mounted down through the VM
# via virtiofs. (The beachhead is handled similarly.)
#

exports_files(
    [
        "git-credential-helper",
        "init.sh",
    ],
    visibility = ["//clients/beachhead/img/outie:__pkg__"],
)

################################################################################
#
# UNUSED: Innie Binaiers (OCI Layers)
#
# We still include these in the innie image -- but should consider removing.
#

tar(
    name = "beachhead_oci_layer_tar",
    srcs = ["//clients/beachhead"],
    mtree = [
        "usr                         uid=0 gid=0 mode=0755 type=dir",
        "usr/local                   uid=0 gid=0 mode=0755 type=dir",
        "usr/local/bin               uid=0 gid=0 mode=0755 type=dir",
        "usr/local/bin/augment-agent uid=0 gid=0 mode=0755 type=file time=0 content=$(location //clients/beachhead:beachhead)",
    ],
)

tar(
    name = "sources_oci_layer_tar",
    srcs = [
        "git-credential-helper",
        "init.sh",
    ],
    mtree = [
        "usr                                 uid=0 gid=0 mode=0755 type=dir",
        "usr/local                           uid=0 gid=0 mode=0755 type=dir",
        "usr/local/bin                       uid=0 gid=0 mode=0755 type=dir",
        "usr/local/sbin                      uid=0 gid=0 mode=0755 type=dir",
        "usr/local/sbin/init.sh              uid=0 gid=0 mode=0755 type=file time=0 content=$(location init.sh)",
        "usr/local/bin/git-credential-helper uid=0 gid=0 mode=0755 type=file time=0 content=$(location git-credential-helper)",
    ],
)

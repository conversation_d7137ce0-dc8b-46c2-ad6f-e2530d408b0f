def deploy(name, oci_innie, oci_outie, args, visibility = None):
    native.sh_binary(
        name = name,
        srcs = ["deploy.sh"],
        args = args + [
            "--oci-innie='$(location %s)'" % oci_innie,
            "--oci-outie='$(location %s)'" % oci_outie,
            "--jsonnet='$(location @google_jsonnet_go//cmd/jsonnet)'",
            "--clusters='research/infra/cfg/clusters/clusters.jsonnet'",
            "--crane='$(location @com_github_google_go_containerregistry//cmd/crane)'",
        ],
        data = [
            oci_innie,
            oci_outie,
            "//research/infra/cfg/clusters:jsonnet_files",  # for the cluster->env->registry mapping
            "@google_jsonnet_go//cmd/jsonnet",
            "@com_github_google_go_containerregistry//cmd/crane",
        ],
        visibility = visibility,
    )

def retag(name, args, visibility = None):
    """Retag existing images without building new ones."""
    native.sh_binary(
        name = name,
        srcs = ["deploy.sh"],
        args = args + [
            "retag",  # Command to run
            "--jsonnet='$(location @google_jsonnet_go//cmd/jsonnet)'",
            "--clusters='research/infra/cfg/clusters/clusters.jsonnet'",
            "--crane='$(location @com_github_google_go_containerregistry//cmd/crane)'",
            # Dummy values for oci paths since retag doesn't use them
            "--oci-innie=/dev/null",
            "--oci-outie=/dev/null",
        ],
        data = [
            "//research/infra/cfg/clusters:jsonnet_files",  # for the cluster->env->registry mapping
            "@google_jsonnet_go//cmd/jsonnet",
            "@com_github_google_go_containerregistry//cmd/crane",
        ],
        visibility = visibility,
    )

load("//clients/beachhead/img/milchick:debootstrap.bzl", "debootstrap")
load("@aspect_bazel_lib//lib:tar.bzl", "tar")

debootstrap(
    name = "milchick_cold",
    size = "2G",
    out = "milchick.cold.btrfs.raw",
    components = [
        "main",
        "universe",
        "multiverse",
    ],
    label = "vm-root",
    mirror = "https://snapshot.ubuntu.com/ubuntu/20250509T000000Z",
    packages = [
        "apt",
        "btrfs-progs",
        "curl",
        "docker.io",
        "gnupg",
        "iputils-ping",
        "jq",
        "nano",
        "openssh-server",
        "rsync",
        "socat",
        "systemd-container",
        "systemd-journal-remote",
        "tree",
        "unzip",
        "vim",
    ],
    release = "jammy",
    tags = ["manual"],
)

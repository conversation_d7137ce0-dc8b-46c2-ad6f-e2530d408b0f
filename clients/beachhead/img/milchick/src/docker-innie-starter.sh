#!/bin/bash

# TODO from the environment
declare -r IMAGE="augment-remote-agent:build"
declare -r USER_ID=1000
declare -r CONTAINER_NAME="augment-agent-workspace"
declare -r UNIT_NAME="docker-container@$CONTAINER_NAME.service"
declare -r CONFIG="/root/virtiofs/workspace-config/workspace-agent-config.json"

main() {
	if ! container_exists "$CONTAINER_NAME"; then
		printf "Creating container: %s.\n" "$CONTAINER_NAME"
		create_container "$CONTAINER_NAME"
	else
		printf "Container already exists: %s.\n" "$CONTAINER_NAME"
	fi

	printf "Running container: %s.\n" "$CONTAINER_NAME"
	container_run "$CONTAINER_NAME"
}

container_exists() {
	declare -r n="$1"
	[[ "$(docker ps -al -f name="$n" --format '{{.Names}}')" == "$n" ]]
}

create_container() {
	declare -r name="$1"

	if [[ -e /root/virtiofs/startup-args.txt ]]; then
		printf "Using args from: %s\n" "/root/virtiofs/startup-args.txt"
		declare -ra innie_init_args=($(</root/virtiofs/startup-args.txt))
	elif [[ -e /root/startup-args.txt ]]; then
		printf "Using args from: %s\n" "/root/startup-args.txt"
		declare -ra innie_init_args=($(</root/startup-args.txt))
	else
		printf "No startup-args.txt found.\n" >&2
		declare -ra innie_init_args=()
	fi

	if [[ ! -d /mnt/persist ]]; then
		# If this doesn't exist then we don't have a persisent block device from k8s.
		# Create an empty directory to use instead.
		install -o1000 -g1000 -m755 -d /mnt/persist
	else
		chown 1000:1000 /mnt/persist
	fi

	declare -r cfg_dst="/run/secrets/workspace-config/$(basename "$CONFIG")"

	declare -a docker_cmd=(
		docker create
		--name="$name"
		--privileged
		-u "$USER_ID"
		--env="AUGMENT_AGENT_WORKSPACE_IMAGE=$IMAGE"
		--entrypoint="/usr/bin/dumb-init"
		-p 9999:9999
		-p 2222:2222
		--mount type=bind,src=/mnt/persist,dst=/mnt/persist
		--mount type=bind,src="$(dirname "$CONFIG")",dst="$(dirname "$cfg_dst")",ro
	)

	declare init_path='/usr/local/sbin/init.sh'
	if [[ -e /root/virtiofs/agent-bin/init.sh ]]; then
		init_path='/run/augment/agent-bin/init.sh'
		docker_cmd+=(--mount type=bind,src=/root/virtiofs/agent-bin,dst=/run/augment/agent-bin,ro)
	fi

	declare -a innie_init_cmd=(
		"$IMAGE"
		"--verbose"
		"--"
		"$init_path"
		"${innie_init_args[@]}"
		--config="$cfg_dst"
	)

	if [[ -e /root/virtiofs/revtun_kubeconfig ]]; then
		declare -r _api_url="$(cat "$CONFIG" | jq '.config.workspace_setup.api_url // ""' -r)"
		declare -r _api_hostname="$(echo "$_api_url" | sed -E 's|^https?://([^:/]+).*|\1|')"
		docker_cmd+=(--add-host "$_api_hostname:**********")
		innie_init_cmd+=(--api-url="https://$_api_hostname:8082")
	fi

	"${docker_cmd[@]}" "${innie_init_cmd[@]}"
}

container_run() {
	declare -r name="$1"
	declare -r _cfg_remote_agent_id="$(cat "$CONFIG" | jq '.remote_agent_id // ""' -r)"
	declare -r _cfg_is_setup_script="$(cat "$CONFIG" | jq '.config.is_setup_script_agent // false' -r)"
	declare -ra cmd=(
		systemd-run
		--unit="$UNIT_NAME"
		--description='Augment Agent Workspace Container (%I)'
		--property=Restart=no # beachhead restarts are handled in init.sh. The container and unit persist for debugging.
		--property=LogExtraFields="AUGMENT_REMOTE_AGENT_ID=$_cfg_remote_agent_id"
		--property=LogExtraFields="AUGMENT_IS_SETUP_SCRIPT=$_cfg_is_setup_script"
		--no-block
		--remain-after-exit
		docker start -a "$name"
	)
	"${cmd[@]}"
}

main "$@"

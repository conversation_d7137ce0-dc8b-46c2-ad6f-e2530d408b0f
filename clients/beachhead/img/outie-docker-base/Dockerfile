FROM ubuntu:22.04

USER root
WORKDIR /root

RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update \
 && apt-get install -y \
      curl \
      gnupg \
 && apt-get clean

# https://cloud.google.com/sdk/docs/install#deb
RUN curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor > /usr/share/keyrings/cloud.google.gpg \
 && printf "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main\n" > /etc/apt/sources.list.d/google-cloud-sdk.list \
 && printf "Package: google-cloud-*\n"  >  /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin: release o=cloud-sdk\n" >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin-Priority: 500\n"        >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "\n"                         >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Package: kubectl\n"         >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin: release o=cloud-sdk\n" >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin-Priority: -1\n"         >> /etc/apt/preferences.d/99-augment-google-cloud

RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update \
 && apt-get install -y \
      bridge-utils \
      btrfs-progs \
      curl \
      dumb-init \
      file \
      google-cloud-cli \
      google-cloud-cli-gke-gcloud-auth-plugin \
      gzip \
      iproute2 \
      iputils-ping \
      jq \
      nftables \
      qemu-system-x86 \
      qemu-system-x86-microvm \
      qemu-utils \
      socat \
      squashfs-tools \
      ssh \
      sudo \
      systemd-journal-remote \
      tree \
      vim \
 && apt-get clean

COPY --from=vmlinuz  --chown=root:root --chmod=0644 vmlinuz     /root/
COPY --from=vm_image --chown=root:root --chmod=0644 image.btrfs /root/

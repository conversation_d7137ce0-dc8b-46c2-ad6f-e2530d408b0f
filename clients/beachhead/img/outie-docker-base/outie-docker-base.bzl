#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_outie_base_setup():
    oci_pull(
        name = "remote-agents-outie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-outie-base",
        digest = "sha256:ffeaa0455f3b1978a1724907baf3b2c14484462ff5c1f3684ae132cb8ed20bd0",
    )

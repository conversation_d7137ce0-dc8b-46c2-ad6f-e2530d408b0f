load("deploy.bzl", "deploy", "retag")

################################################################################
#
# Deployments
#

deploy(
    name = "deploy-dev",
    args = [
        "--env=DEV",
        # No --tag. Use either --tag=dev-{user|team} or --tag=DEV on the command line.
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
    visibility = [
        "//services/test:__subpackages__",
    ],
)

deploy(
    name = "deploy-staging",
    args = [
        "--env=STAGING",
        # --tag can be provided on command line, defaults to STAGING if not specified
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
)

deploy(
    name = "rebuild-prod",
    args = [
        "--env=PROD",
        "--tag=PROD",  # Enforce, for now.
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
)

deploy(
    name = "deploy-prod",
    args = [
        "--env=PROD",
        # --tag can be provided on command line, defaults to PROD if not specified
    ],
    oci_innie = "//clients/beachhead/img/innie:innie_oci",
    oci_outie = "//clients/beachhead/img/outie:outie_oci",
)

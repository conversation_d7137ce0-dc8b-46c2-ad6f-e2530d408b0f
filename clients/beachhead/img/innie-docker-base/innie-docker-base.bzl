#
# This file is auto-generated by rebuild.sh. DO NOT EDIT BY HAND.
#

load("@rules_oci//oci:pull.bzl", "oci_pull")

def docker_innie_base_setup():
    oci_pull(
        name = "remote-agents-innie-base",
        registry = "us-central1-docker.pkg.dev",
        repository = "system-services-dev/base-images/remote-agents-innie-base",
        digest = "sha256:3c5d270577208c1effe6e9657f0cb5d58e9922e7536f84ac54e1fe78773986ec",
    )

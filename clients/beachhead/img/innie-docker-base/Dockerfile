FROM ubuntu:22.04

USER root
WORKDIR /root

### APT: minimal packages to support adding repos.
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update -y \
 && apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        software-properties-common \
 && apt-get clean

### APT: git
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && add-apt-repository ppa:git-core/ppa -y

### APT: base packages
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && apt-get update -y \
 && apt-get install -y \
        curl \
        docker \
        docker-buildx \
        dumb-init \
        git \
        jq \
        less \
        make \
        nano \
        openssh-server \
        ripgrep \
        rsync \
        sudo \
        tree \
        unzip \
        vim \
        wget \
        zstd \
  && apt-get clean \
  && rm -fr /etc/ssh/ssh_host_*  # TODO(mattm): Prevent key generation during install

### Batteries Included

# python
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && export DEBIAN_FRONTEND=noninteractive \
 && add-apt-repository ppa:deadsnakes/ppa -y \
 && apt-get update -y \
 && apt-get install -y \
        # distro \
        python3 \
        python3.10 python3.10-dev python3.10-gdbm \
        python3-pip \
        python3-pytest \
        # deadsnakes \
        python3.9  python3.9-dev  python3.9-gdbm \
        python3.11 python3.11-dev python3.11-gdbm \
        python3.12 python3.12-dev python3.12-gdbm \
 && apt-get clean \
 && pip install pytest \
 && ln -s python3 /usr/bin/python \
 && ln -s pytest-3 /usr/bin/pytest

# bazel(isk)
RUN --mount=type=cache,target=/root/dl : \
 && BAZELISK_VERSION=v1.26.0 \
 && BAZELISK_256SUM=6539c12842ad76966f3d493e8f80d67caa84ec4a000e220d5459833c967c12bc \
 && BAZELISK_DL=/root/dl/bazelisk-"$BAZELISK_VERSION" \
 && test -e "$BAZELISK_DL" || curl -fsSL https://github.com/bazelbuild/bazelisk/releases/download/"$BAZELISK_VERSION"/bazelisk-linux-amd64 -o "$BAZELISK_DL" \
 && printf "%s %s\n" "$BAZELISK_256SUM" "$BAZELISK_DL" | sha256sum -c \
 && install -oroot -groot -m0755 "$BAZELISK_DL" /usr/local/bin/bazel

# go
RUN --mount=type=cache,target=/root/dl : \
 && _GO_VER=1.24.2 \
 && _GO_256SUM=68097bd680839cbc9d464a0edce4f7c333975e27a90246890e9f1078c7e702ad \
 && _GO_TGZ="go$_GO_VER.linux-amd64.tar.gz" \
 && _GO_DL="/root/dl/$_GO_TGZ" \
 && test -e "$_GO_DL" || curl -fsSL https://golang.org/dl/"$_GO_TGZ" -o "$_GO_DL" \
 && printf "%s %s\n" "$_GO_256SUM" "$_GO_DL" | sha256sum -c \
 && rm -fr /usr/local/go \
 && tar -C /usr/local -xzf "$_GO_DL" \
 && echo 'if [[ -x /usr/local/go/bin/go ]]; then'    >  /etc/profile.d/50-go.sh \
 && echo '    export PATH="/usr/local/go/bin:$PATH"' >> /etc/profile.d/50-go.sh \
 && echo 'fi'                                        >> /etc/profile.d/50-go.sh

# node, npm, pnpm
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
 && apt-get install -y nodejs \
 && npm install -g pnpm@latest-9

RUN install -o root -g root -m0644 -D /dev/stdin /etc/ssh/sshd_config.d/00-augment.sshd.conf <<EOF
Port 2222
PermitRootLogin no
PasswordAuthentication no
KbdInteractiveAuthentication no
EOF

### User account (augment-agent)
RUN useradd --create-home --uid 1000 --shell /bin/bash augment-agent \
 && printf '%s ALL=(ALL:ALL) NOPASSWD: ALL\n' augment-agent | install -o root -g root -m400 /dev/stdin /etc/sudoers.d/zzz-augment \
 && gpasswd -a augment-agent docker

# NOTE(mattm): Homebrew insists on installing as a regular user to /home/<USER>
# install to avoid warnings. We set HOME=/home/<USER>/home/<USER>/.cache/Homebrew.
RUN install -o augment-agent -g augment-agent -m0755 -d /home/<USER>/
RUN --mount=type=cache,target=/home/<USER>/.cache : \
 && chown augment-agent:augment-agent /home/<USER>/.cache \
 && curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh -o /home/<USER>/brew-install.sh \
 && chmod +x /home/<USER>/brew-install.sh \
 && echo 'if [[ -x /home/<USER>/.linuxbrew/bin/brew ]]; then'      >  /etc/profile.d/90-homebrew.sh \
 && echo '    eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"' >> /etc/profile.d/90-homebrew.sh \
 && echo 'fi'                                                         >> /etc/profile.d/90-homebrew.sh \
 && sudo -iu augment-agent HOME=/home/<USER>/home/<USER>/brew-install.sh

USER augment-agent
WORKDIR /home/<USER>

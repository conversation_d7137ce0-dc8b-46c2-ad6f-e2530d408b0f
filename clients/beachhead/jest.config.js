const { pathsToModuleNameMapper } = require("ts-jest");
const { compilerOptions } = require("../tsconfig");

const pathAliases = pathsToModuleNameMapper(compilerOptions.paths, {
    prefix: "<rootDir>/../",
});

module.exports = {
    roots: ["<rootDir>"],
    transform: {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        "^.+\\.ts?$": "ts-jest",
    },
    testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.test.ts?$",
    moduleFileExtensions: ["ts", "js", "json", "node"],
    collectCoverage: true,
    clearMocks: true,
    resetMocks: false,
    coverageDirectory: "coverage",
    testEnvironment: "node",
    setupFilesAfterEnv: ["<rootDir>/src/__mocks__/setup-jest.ts"],
    // Any local modules that we wish to mock need to be added to this list.
    moduleNameMapper: {
        ...pathAliases,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        // TODO(jeff): Add these mocks.
        // "^.*/os$": "<rootDir>/src/__mocks__/os.ts",
        // "^.*/fs-utils$": "<rootDir>/src/__mocks__/fs-utils.ts",
    },
    transformIgnorePatterns: [
        "node_modules/(?!(@augment-internal/sidecar-libs|.aspect_rules_js/@augment\\+sidecar-libs))",
    ],
};

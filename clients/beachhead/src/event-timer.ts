/**
 * A class to track the duration of events.
 */
export class EventTimer {
    private _lastTime: number | undefined;
    // map of event name to duration
    private _eventTimes: Map<string, number>;

    constructor() {
        this._lastTime = undefined;
        this._eventTimes = new Map<string, number>();
    }

    public start(): void {
        if (this._lastTime !== undefined) {
            throw new Error("EventTimer already started");
        }
        this._lastTime = Date.now();
    }

    public event_finished(event: string): void {
        if (this._lastTime === undefined) {
            throw new Error("EventTimer not started");
        }
        const now = Date.now();
        const event_duration = now - this._lastTime;
        const prev_event_durations = this._eventTimes.get(event) ?? 0;
        this._eventTimes.set(event, prev_event_durations + event_duration);
        this._lastTime = now;
    }

    public getEventTimes(): Map<string, number> {
        return this._eventTimes;
    }

    /**
     * Returns a string representation of the event times, in milliseconds
     *
     * @returns A string of the form "event1=duration1 event2=duration2 ..."
     */
    public getEventTimesKVString(): string {
        return Array.from(this._eventTimes.entries())
            .map(([name, duration]) => `${name}=${duration}ms`)
            .join(" ");
    }
}

export type ClientMetric = {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    client_metric: string;
    value: number;
};

export enum WebviewName {
    chat = "chat",
}

export enum ChatMetricName {
    // @mentions
    chatMentionFolder = "chat-mention-folder",
    chatMentionFile = "chat-mention-file",
    chatMentionExternalSource = "chat-mention-external-source",
    chatClearContext = "chat-clear-context",
    chatRestoreDefaultContext = "chat-restore-default-context",
    // /actions
    chatUseActionFind = "chat-use-action-find",
    chatUseActionExplain = "chat-use-action-explain",
    chatUseActionFix = "chat-use-action-autofix",
    chatUseActionWriteTest = "chat-use-action-write-test",
    // Conversation menu
    chatNewConversation = "chat-new-conversation",
    chatNewAutofixConversation = "chat-new-autofix-conversation",
    chatEditConversationName = "chat-edit-conversation-name",
    // Markdown (smart paste)
    chatFailedSmartPasteResolveFile = "chat-failed-smart-paste-resolve-file",
    chatPrecomputeSmartPaste = "chat-precompute-smart-paste",
    chatSmartPaste = "chat-smart-paste",
    // Markdown (codeblocks)
    chatCodeblockCopy = "chat-codeblock-copy",
    chatCodeblockCreate = "chat-codeblock-create",
    chatCodeblockGoToFile = "chat-codeblock-go-to-file",
    // Markdown (codespans)
    chatCodespanGoToFile = "chat-codespan-go-to-file",
    chatCodespanGoToSymbol = "chat-codespan-go-to-symbol",
    // Markdown (mermaid blocks)
    chatMermaidblockInitialize = "chat-mermaidblock-initialize",
    chatMermaidblockToggle = "chat-mermaidblock-toggle",
    chatMermaidblockInteract = "chat-mermaidblock-interact",
    chatMermaidBlockError = "chat-mermaidblock-error",
    // Suggested questions
    chatUseSuggestedQuestion = "chat-use-suggested-question",
    chatDisplaySuggestedQuestions = "chat-display-suggested-questions",
    // Guidelines
    setWorkspaceGuidelines = "chat-set-workspace-guidelines",
    clearWorkspaceGuidelines = "chat-clear-workspace-guidelines",
    setUserGuidelines = "chat-set-user-guidelines",
    clearUserGuidelines = "chat-clear-user-guidelines",
}

export type AgentInterruption = {
    requestId: string;
    currConversationLength: number;
};

export type AgentReversion = {
    // This type is currently empty because the conversation around what data to send is still in flux.
    // Tentatively, Eric has proposed the following:
    // We probably want:
    // - num files
    // - changes in each file (+- changes, like the diff types)
    // - potentially some metadata about each file
    // If you ever add fields here, make sure you also make the corresponding changes in
    // - `augment-api.ts`.`BackAgentReversionData`.
    // - `request_insight.proto`.`AgentReversionData`.
    // - `agent-session-event-reporter.ts`.`reportEvent`.
};

export type MemoriesFileOpen = {
    memoriesPathUndefined?: boolean;
};

export type AgentSessionEventData = {
    eventName: AgentSessionEventName;
    conversationId: string;
    eventData?: {
        agentReversionData?: AgentReversion;
        agentInterruptionData?: AgentInterruption;
        rememberToolCallData?: RememberToolCallData;
        memoriesFileOpenData?: MemoriesFileOpen;
        initialOrientationData?: InitialOrientationData;
        classifyAndDistillData?: ClassifyAndDistillData;
        flushMemoriesData?: FlushMemoriesData;
    };
};

// Using snake_case to avoid ton of boilerplate converting to camelCase for protobuf
/* eslint-disable @typescript-eslint/naming-convention */

// Using this for strings that might contain PII
export type StringStats = {
    num_lines: number;
    num_chars: number;
};

export function getStringStats(text: string | undefined): StringStats {
    if (text === undefined) {
        return {
            num_lines: -1,
            num_chars: -1,
        };
    }
    return {
        num_lines: text.split("\n").length,
        num_chars: text.length,
    };
}

/**
 * A value with an associated timestamp in milliseconds
 */
export interface Timed<T> {
    value: T;
    timestamp: string;
}

/**
 * Tracing data, which should NOT contain PII
 */
export type AgentTracingData<DebugFlag extends string> = {
    flags: Record<DebugFlag, Timed<boolean>>;
    nums: Record<DebugFlag, Timed<number>>;
    string_stats: Record<DebugFlag, Timed<StringStats>>;
    request_ids: Record<DebugFlag, Timed<string>>;
};

/**
 * Data collector with timestamps
 *
 * Every value that is added using `set*()`, will be timestamped.
 * `DebugFlag` argument is used instead of just `string` to avoid
 * accidentally using wrong strings (which can contain PII).
 *
 * It supports adding:
 *  - flags (boolean)
 *  - numbers (number)
 *  - string stats (StringStats)
 *  - request IDs (string)
 *
 * Note: using snake_case to avoid ton of boilerplate converting to camelCase
 */
export class TraceDataCollector<DebugFlag extends string> {
    public tracingData: AgentTracingData<DebugFlag> = {
        flags: {} as Record<DebugFlag, Timed<boolean>>,
        nums: {} as Record<DebugFlag, Timed<number>>,
        string_stats: {} as Record<DebugFlag, Timed<StringStats>>,
        request_ids: {} as Record<DebugFlag, Timed<string>>,
    };

    public setFlag(flag: DebugFlag, value: boolean = true): void {
        this.tracingData.flags[flag] = {
            value,
            timestamp: new Date().toISOString(),
        };
    }

    public setNum(flag: DebugFlag, value: number): void {
        this.tracingData.nums[flag] = {
            value,
            timestamp: new Date().toISOString(),
        };
    }

    public setStringStats(flag: DebugFlag, text: string | undefined): void {
        this.tracingData.string_stats[flag] = {
            value: getStringStats(text),
            timestamp: new Date().toISOString(),
        };
    }

    // This string is validated to be a UUIDv4 in backend sanitization
    public setRequestId(flag: DebugFlag, requestId: string): void {
        this.tracingData.request_ids[flag] = {
            value: requestId,
            timestamp: new Date().toISOString(),
        };
    }
}

// Specifies who runs the remember tool
export enum RememberToolCaller {
    // Not specified (default)
    unspecified = 0,

    // Background process that runs on every user message
    classify_and_distill = 1,

    // Initial orientation process
    orientation = 2,
}

export enum RememberToolCallDebugFlag {
    // General
    memoriesRequestId = "memoriesRequestId",
    exceptionThrown = "exceptionThrown",
    toolOutputIsError = "toolOutputIsError",
    injectionNoCodeWrapper = "injectionNoCodeWrapper",

    // Memories compression stage
    compressionStarted = "compressionStarted",
    compressionTargetMissing = "compressionTargetMissing",
    compressionPromptMissing = "compressionPromptMissing",
    compressionPromptStats = "compressionPromptStats",
    compressionRequestId = "compressionRequestId",
    compressionMemoriesStats = "compressedMemoriesStats",
    compressionFailed = "compressionFailed",

    // Stage of saving memories to file
    setMemoriesStart = "setMemoriesStart",
    setMemoriesUpperBoundSizeMissing = "setMemoriesUpperBoundSizeMissing",
    setMemoriesNonEmptyLines = "nonEmptyLines",
    setMemoriesNoMemoriesFile = "noMemoriesFile",
    setMemoriesUpdateBufferFailed = "updateBufferFailed",
    setMemoriesNoChangesMade = "noChangesMade",

    // Main injection stage
    injectionStarted = "injectionStarted",
    injectionCurrentMemoriesStats = "injectionCurrentMemoriesStats",
    injectionPromptMissing = "injectionPromptMissing",
    injectionPromptStats = "injectionPromptStats",
    injectionRequestId = "injectionRequestId",
    injectionUpdatedMemoriesStats = "injectionUpdatedMemoriesStats",
    injectionFailed = "injectionFailed",
}

/**
 * Data for the remember-tool-call event.
 *
 * As you can guess from the name, this event is triggered when `RememberTool` is called.
 * All scenarios in which `RememberTool` is called are described in `RememberToolCaller` enum.
 *
 * Note: using snake_case to avoid ton of boilerplate converting to camelCase
 */
export class RememberToolCallData extends TraceDataCollector<RememberToolCallDebugFlag> {
    constructor(
        public readonly caller: RememberToolCaller,
        public readonly is_complex_new_memory: boolean
    ) {
        super();
    }

    public static create(
        caller: RememberToolCaller,
        is_complex_new_memory: boolean
    ): RememberToolCallData {
        return new RememberToolCallData(caller, is_complex_new_memory);
    }
}

// What triggered the initial orientation process
export enum InitialOrientationCaller {
    // Not specified
    unspecified = 0,

    // Onboarding flow
    onboarding = 1,

    // Command was run
    command = 2,
}

// Tracing flags for initial orientation
export enum InitialOrientationDebugFlag {
    // General
    exceptionThrown = "exceptionThrown",
    start = "start",
    end = "end",
    concurrencyLevelMissing = "concurrencyLevelMissing",
    initialOrientationDisabled = "initialOrientationDisabled",
    noRootFolderFound = "noRootFolderFound",
    retryWithLowerConcurrencyLevel = "retryWithLowerConcurrencyLevel",
    localizationPromptMissing = "localizationPromptMissing",
    detectLanguagesPromptMissing = "detectLanguagesPromptMissing",
    orientationCompressionPromptMissing = "orientationCompressionPromptMissing",
    orientationMaxLanguagesMissing = "orientationMaxLanguagesMissing",
    orientationBuildTestQueryMissing = "orientationBuildTestQueryMissing",

    // Step 1: here we detect top programming languages used in repo
    topLanguagesNumFiles = "topLanguagesNumFiles",
    topLanguagesRequestId = "topLanguagesRequestId",
    topLanguagesModelResponseStats = "topLanguagesModelResponseStats",
    topLanguagesNumDetectedLanguages = "topLanguagesNumDetectedLanguages",
    topLanguagesNumFinalLanguages = "topLanguagesNumFinalLanguages",

    // Step 2: for each programming language, we localize places where it is used
    // We cap number of languages to 6 (higher wouldn't make much sense)
    localizationStarted = "localizationStarted",
    localizationEnded = "localizationEnded",
    localizationPromptStats_0 = "localizationPromptStats_0",
    localizationPromptStats_1 = "localizationPromptStats_1",
    localizationPromptStats_2 = "localizationPromptStats_2",
    localizationPromptStats_3 = "localizationPromptStats_3",
    localizationPromptStats_4 = "localizationPromptStats_4",
    localizationPromptStats_5 = "localizationPromptStats_5",
    localizationRequestId_0 = "localizationRequestId_0",
    localizationRequestId_1 = "localizationRequestId_1",
    localizationRequestId_2 = "localizationRequestId_2",
    localizationRequestId_3 = "localizationRequestId_3",
    localizationRequestId_4 = "localizationRequestId_4",
    localizationRequestId_5 = "localizationRequestId_5",
    localizationResponseStats_0 = "localizationResponseStats_0",
    localizationResponseStats_1 = "localizationResponseStats_1",
    localizationResponseStats_2 = "localizationResponseStats_2",
    localizationResponseStats_3 = "localizationResponseStats_3",
    localizationResponseStats_4 = "localizationResponseStats_4",
    localizationResponseStats_5 = "localizationResponseStats_5",
    localizationParsingFailed_0 = "localizationParsingFailed_0",
    localizationParsingFailed_1 = "localizationParsingFailed_1",
    localizationParsingFailed_2 = "localizationParsingFailed_2",
    localizationParsingFailed_3 = "localizationParsingFailed_3",
    localizationParsingFailed_4 = "localizationParsingFailed_4",
    localizationParsingFailed_5 = "localizationParsingFailed_5",
    localizationNumLocations_0 = "localizationNumLocations_0",
    localizationNumLocations_1 = "localizationNumLocations_1",
    localizationNumLocations_2 = "localizationNumLocations_2",
    localizationNumLocations_3 = "localizationNumLocations_3",
    localizationNumLocations_4 = "localizationNumLocations_4",
    localizationNumLocations_5 = "localizationNumLocations_5",

    failedToListRootFolder = "failedToListRootFolder",

    // Step 3: for each language, we run an agentic loop to extract relevant information
    agenticStarted = "agenticStarted",
    agenticEnded = "agenticEnded",
    agenticNumTurns_0 = "agenticNumTurns_0",
    agenticNumTurns_1 = "agenticNumTurns_1",
    agenticNumTurns_2 = "agenticNumTurns_2",
    agenticNumTurns_3 = "agenticNumTurns_3",
    agenticNumTurns_4 = "agenticNumTurns_4",
    agenticNumTurns_5 = "agenticNumTurns_5",
    agenticModelResponseStats_0 = "agenticModelResponseStats_0",
    agenticModelResponseStats_1 = "agenticModelResponseStats_1",
    agenticModelResponseStats_2 = "agenticModelResponseStats_2",
    agenticModelResponseStats_3 = "agenticModelResponseStats_3",
    agenticModelResponseStats_4 = "agenticModelResponseStats_4",
    agenticModelResponseStats_5 = "agenticModelResponseStats_5",
    agenticFailedToComplete_0 = "agenticFailedToComplete_0",
    agenticFailedToComplete_1 = "agenticFailedToComplete_1",
    agenticFailedToComplete_2 = "agenticFailedToComplete_2",
    agenticFailedToComplete_3 = "agenticFailedToComplete_3",
    agenticFailedToComplete_4 = "agenticFailedToComplete_4",
    agenticFailedToComplete_5 = "agenticFailedToComplete_5",
    agenticModelResponseStats = "agenticModelResponseStats",

    // Step 4: we compress all the knowledge we collected
    compressionRequestId = "compressionRequestId",
    compressionModelResponseStats = "compressionModelResponseStats",
    compressionParsingFailed = "compressionParsingFailed",

    // Step 5: we flush the knowledge into workspace guidelines
    rememberStarted = "rememberStarted",
    rememberEnded = "rememberEnded",
    failedToReadGuidelines = "failedToReadGuidelines",
    failedToWriteGuidelines = "failedToWriteGuidelines",
}

export class InitialOrientationData extends TraceDataCollector<InitialOrientationDebugFlag> {
    constructor(public readonly caller: InitialOrientationCaller) {
        super();
    }

    public static create(caller: InitialOrientationCaller): InitialOrientationData {
        return new InitialOrientationData(caller);
    }
}

// Tracing flags for classify and distill process
export enum ClassifyAndDistillDebugFlag {
    memoriesRequestId = "memoriesRequestId",
    exceptionThrown = "exceptionThrown",
    start = "start",
    end = "end",
    noPendingUserMessage = "noPendingUserMessage",
    startSendSilentExchange = "startSendSilentExchange",
    sendSilentExchangeRequestId = "sendSilentExchangeRequestId",
    sendSilentExchangeResponseStats = "sendSilentExchangeResponseStats",
    noRequestId = "noRequestId",
    conversationChanged = "conversationChanged",
    explanationStats = "explanationStats",
    contentStats = "contentStats",
    invalidResponse = "invalidResponse",
    worthRemembering = "worthRemembering",
    lastUserExchangeRequestId = "lastUserExchangeRequestId",
    noLastUserExchangeRequestId = "noLastUserExchangeRequestId",
}

export class ClassifyAndDistillData extends TraceDataCollector<ClassifyAndDistillDebugFlag> {
    constructor() {
        super();
    }

    public static create(): ClassifyAndDistillData {
        return new ClassifyAndDistillData();
    }
}

// After classify and distill, we have separate process that checks if
// classify and distill determined that memories are worth remembering.
// If so, it calls the remember tool.
export enum FlushMemoriesDebugFlag {
    start = "start",
    end = "end",
    memoriesRequestId = "memoriesRequestId",
    exceptionThrown = "exceptionThrown",
    lastUserExchangeRequestId = "lastUserExchangeRequestId",
    noMemoryData = "noMemoryData",
    agenticTurnHasRememberToolCall = "agenticTurnHasRememberToolCall",
    emptyMemory = "emptyMemory",
}

export class FlushMemoriesData extends TraceDataCollector<FlushMemoriesDebugFlag> {
    constructor() {
        super();
    }

    public static create(): FlushMemoriesData {
        return new FlushMemoriesData();
    }
}

/* eslint-enable @typescript-eslint/naming-convention */

export enum AgentSessionEventName {
    openedAgentConversation = "opened-agent-conversation",
    revertCheckpoint = "revert-checkpoint",
    agentInterruption = "agent-interruption",
    sentUserMessage = "sent-user-message",
    rememberToolCall = "remember-tool-call",
    openedMemoriesFile = "opened-memories-file",
    initialOrientation = "initial-orientation",
    classifyAndDistill = "classify-and-distill",
    flushMemories = "flush-memories",
    vsCodeTerminalShellIntegrationNotAvailable = "vs-code-terminal-shell-integration-not-available",
    vsCodeTerminalReadingApproximateOutput = "vs-code-terminal-reading-approximate-output",
    vsCodeTerminalTimedOutWaitingForNoopCommand = "vs-code-terminal-timed-out-waiting-for-noop-command",
    vsCodeTerminalFailedToUseShellIntegration = "vs-code-terminal-failed-to-use-shell-integration",
    vsCodeTerminalLastCommandIsSameAsCurrent = "vs-code-terminal-last-command-is-same-as-current",
    vsCodeTerminalPollingDeterminedProcessIsDone = "vs-code-terminal-polling-determined-process-is-done",
    vsCodeTerminalFailedToReadOutput = "vs-code-terminal-failed-to-read-output",
}

export type AgentRequestEventData = {
    eventName: AgentRequestEventName;
    conversationId: string;
    requestId: string;
    chatHistoryLength: number;
};

export enum AgentRequestEventName {
    sentUserMessage = "sent-user-message",
}

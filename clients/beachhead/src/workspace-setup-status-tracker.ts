import { APIServerImplWithErrorReporting as APIServerImpl } from "./augment-api";
import { getLogger } from "./logging";
import {
    RemoteWorkspaceSetupStatus,
    RemoteWorkspaceSetupStep,
    RemoteWorkspaceSetupStepStatus,
} from "./remote-agent-manager/types";
import { InitCommands } from "./workspace-init";
import { BeachheadWorkspaceManagerImpl } from "./workspace-manager";

export class WorkspaceSetupStatusTracker {
    private _initCommands: InitCommands | undefined;
    private _workspaceManager: BeachheadWorkspaceManagerImpl;
    private _indexing_logs_sequence_id = 0;
    private _last_sent_logs_length = 0;

    private _logger = getLogger("WorkspaceSetupStatusTracker");

    constructor(workspaceManager: BeachheadWorkspaceManagerImpl, initCommands?: InitCommands) {
        this._initCommands = initCommands;
        this._workspaceManager = workspaceManager;
    }

    // Maximum number of characters allowed in a single log message
    private readonly MAX_LOG_CHARS = 1000;

    public report_status(): RemoteWorkspaceSetupStatus {
        let steps: RemoteWorkspaceSetupStep[] = [];

        // Check if init commands were skipped due to persistence.
        // If they were skipped we do not really need to return any status
        if (this._initCommands?.skipped_due_to_persistence()) {
            return {
                steps: [],
            };
        }

        if (this._initCommands) {
            let updated_steps: RemoteWorkspaceSetupStep[] = [];
            updated_steps = this._initCommands.workspace_setup_status().steps;
            // Once steps are populated, then let's do that, so it is easier so we account for all steps.
            // Also let us increment the cmd sequence id.
            for (const step of updated_steps) {
                const chunk_logs = this.splitLogsIntoChunks(step.logs);
                // if no new logs, then just add the step
                if (chunk_logs.length === 0) {
                    steps.push(step);
                    continue;
                }
                // if there are multiple chunks, then we need to send them all with running status
                // we need to increment the sequence id for each new chunk
                if (chunk_logs.length > 1) {
                    for (let i = 0; i < chunk_logs.length - 1; i++) {
                        steps.push({
                            step_description: step.step_description,
                            logs: chunk_logs[i],
                            status: RemoteWorkspaceSetupStepStatus.running,
                            sequence_id: this._initCommands
                                ?.commands()
                                [step.step_number].sequence_id(), //eslint-disable-line
                            step_number: step.step_number,
                        });
                        this._initCommands?.commands()[step.step_number].increment_sequence_id();
                    }
                }
                const chunk = chunk_logs[chunk_logs.length - 1];
                steps.push({
                    step_description: step.step_description,
                    logs: chunk,
                    status: step.status,
                    sequence_id: this._initCommands?.commands()[step.step_number].sequence_id(),
                    step_number: step.step_number,
                });
            }
        }
        if (this._workspaceManager.initial_sync_started) {
            const current_logs = this._workspaceManager.indexing_logs;

            // Check if there are new logs by comparing lengths
            if (current_logs.length > this._last_sent_logs_length) {
                this._indexing_logs_sequence_id += 1;

                // Calculate only the new logs that were added since last update
                // Extract only the new part of the logs using the stored index
                const logs = current_logs.substring(this._last_sent_logs_length);

                // Update the last sent logs length to the current full logs length
                this._last_sent_logs_length = current_logs.length;
                let status = RemoteWorkspaceSetupStepStatus.running;
                // Add the step with only the new logs
                if (this._workspaceManager.initial_sync_complete) {
                    status = RemoteWorkspaceSetupStepStatus.success;
                }
                steps.push({
                    step_description: "Indexing",
                    logs: logs,
                    status: status,
                    sequence_id: this._indexing_logs_sequence_id,
                    step_number: this._initCommands ? this._initCommands.commands().length : 0,
                });
            }
        }
        if (steps.length !== 0) {
            this._logger.info("Reporting new workspace setup logs: %s", steps);
        }
        return {
            steps: steps,
        };
    }

    /**
     * Splits a log string into chunks of MAX_LOG_CHARS
     * @param logs The log string to split
     * @returns An array of log chunks, each with a maximum of MAX_LOG_CHARS characters
     */
    private splitLogsIntoChunks(logs: string): string[] {
        const chunks: string[] = [];
        let remainingLogs = logs;

        while (remainingLogs.length > 0) {
            const chunk = remainingLogs.substring(0, this.MAX_LOG_CHARS);
            chunks.push(chunk);
            remainingLogs = remainingLogs.substring(this.MAX_LOG_CHARS);
        }

        return chunks;
    }

    public run_report_status_logs_loop(apiServer: APIServerImpl, agentId: string): void {
        const intervalId = setInterval(() => {
            try {
                const status = this.report_status();
                if (status.steps.length !== 0) {
                    // Log the number of steps being reported
                    this._logger.info(
                        `Reporting ${status.steps.length} log steps for agent ${agentId}`
                    );

                    // report each step separately
                    for (const step of status.steps) {
                        this._logger.debug(
                            `Reporting step ${step.step_number} with sequence ID ${step.sequence_id} (${step.logs.length} chars)`
                        );
                        void apiServer
                            .reportWorkspaceSetupLogs(agentId, { steps: [step] })
                            .catch((error) => {
                                this._logger.error(
                                    "Error reporting workspace setup logs: %s",
                                    error instanceof Error ? error.message : String(error)
                                );
                            });
                    }
                }
                if (this._workspaceManager.initial_sync_complete) {
                    clearInterval(intervalId);
                    this._logger.info("Initial sync complete, stopping status reporting");
                }
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                this._logger.error("Error reporting workspace setup logs: %s", errorMessage);
            }
        }, 1000);
    }
}

import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IToolHost } from "@augment-internal/sidecar-libs/src/tools/tool-host";
import { ITool, ToolHostBase } from "@augment-internal/sidecar-libs/src/tools/tool-host-base";
import { LocalToolType, ToolHostName } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { SetupScriptTool } from "./tools/setup-script-tool";

export function beachheadToolHostFactory(workspaceRoot: string, setupMode: boolean) {
    return (_chatMode: ChatMode) => {
        return new BeachheadToolHost(workspaceRoot, setupMode);
    };
}

/**
 * Manages local tools that run directly in the beachhead client environment.
 * This host is responsible for registering, configuring, and executing local tools
 * that don't require communication with the sidecar or remote backend.
 */
class BeachheadToolHost extends ToolHostBase<LocalToolType> {
    constructor(
        private readonly workspaceRoot: string,
        private readonly setupMode: boolean
    ) {
        const tools: ITool<LocalToolType>[] = setupMode ? [new SetupScriptTool(workspaceRoot)] : [];

        super(tools, ToolHostName.localToolHost);
    }

    factory(_preconditionWait: Promise<void>): IToolHost {
        return new BeachheadToolHost(this.workspaceRoot, this.setupMode);
    }
}

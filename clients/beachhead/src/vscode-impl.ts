// Copied from the Vim sidecar

/* Implements interfaces from VSCode */
import { EventEmitter as NodeEventEmitter } from "events";
import { URI as Uri, Utils } from "vscode-uri";

export class CancellationError extends Error {
    /**
     * Creates a new cancellation error.
     */
    constructor() {
        super("Operation cancelled");
    }
}

export { Uri, Utils /* TextDocumentChangeEvent */ };

/* eslint-disable @typescript-eslint/naming-convention */
export enum EndOfLine {
    LF = 1,
    CRLF = 2,
}
/* eslint-enable @typescript-eslint/naming-convention */

export class Disposable {
    public dispose: () => any;

    constructor(callOnDispose: () => any) {
        this.dispose = (): any => callOnDispose();
    }
}

export interface Event<T> {
    /**
     * A function that represents an event to which you subscribe by calling it with
     * a listener function as argument.
     *
     * @param listener The listener function will be called when the event happens.
     * @param thisArgs The `this`-argument which will be used when calling the event listener.
     * @param disposables An array to which a {@link Disposable} will be added.
     * @returns A disposable which unsubscribes the event listener.
     */
    (listener: (e: T) => any, thisArgs?: any, disposables?: Disposable[]): Disposable;
}

/**
 * An event emitter can be used to create and manage an {@link Event} for others
 * to subscribe to. One emitter always owns one event.
 *
 * Use this class if you want to provide event from within your extension, for instance
 * inside a {@link TextDocumentContentProvider} or when providing
 * API to other extensions.
 */
export class EventEmitter<T> {
    private nodeEventEmitter: NodeEventEmitter;

    constructor() {
        this.nodeEventEmitter = new NodeEventEmitter();

        this.event = (
            listener: (e: T) => any,
            thisArgs?: any,
            disposables?: Disposable[]
        ): Disposable => {
            const wrappedListener = thisArgs ? listener.bind(thisArgs) : listener;
            this.nodeEventEmitter.on("event", wrappedListener);

            const disposable = new Disposable(() => {
                this.nodeEventEmitter.off("event", wrappedListener);
            });

            if (Array.isArray(disposables)) {
                disposables.push(disposable);
            }

            return disposable;
        };
    }

    /**
     * The event listeners can subscribe to.
     */
    public event: Event<T>;

    /**
     * Notify all subscribers of the {@link EventEmitter.event event}. Failure
     * of one or more listener will not fail this function call.
     *
     * @param data The event object.
     */
    public fire(data: T): void {
        this.nodeEventEmitter.emit("event", data);
    }

    /**
     * Dispose this object and free resources.
     */
    dispose(): void {
        this.nodeEventEmitter.removeAllListeners("event");
    }
}

export class WorkspaceFolder {
    public readonly uri: Uri;
    public readonly name: string;
    public readonly index: number;

    constructor(uri: Uri, name: string, index: number) {
        this.uri = uri;
        this.name = name;
        this.index = index;
    }
}

export type WorkspaceFoldersChangeEvent = {
    added: readonly WorkspaceFolder[];
    removed: readonly WorkspaceFolder[];
};

export class CancellationTokenSource implements Disposable {
    public token: CancellationToken;

    private tokenEvent: EventEmitter<void>;

    constructor() {
        this.tokenEvent = new EventEmitter<void>();
        this.token = {
            isCancellationRequested: false,
            onCancellationRequested: this.tokenEvent.event,
        };
    }

    public cancel() {
        this.token.isCancellationRequested = true;
        this.tokenEvent.fire();
    }

    public dispose() {
        this.tokenEvent.dispose();
    }
}

export interface WorkspaceConfiguration {
    advanced?: {
        apiToken?: string;
        autofix?: {
            enabled?: boolean;
            locationUrl?: string;
            autofixUrl?: string;
        };
        chat?: {
            enableEditableHistory?: boolean;
            enabled?: boolean;
            experimentalFullFilePaste?: boolean;
            model?: string;
            modelDisplayNameToId?: {
                [key: string]: string | null;
            };
            smartPasteUsePrecomputation?: boolean;
            stream?: boolean;
            url?: string;
            useRichTextHistory?: boolean;
        };
        codeInstruction?: {
            model?: string;
        };
        completionURL?: string;
        completions?: {
            timeoutMs?: number;
            maxWaitMs?: number;
            addIntelliSenseSuggestions?: boolean;
            filter_threshold?: number /* eslint-disable-line @typescript-eslint/naming-convention */;
        };
        enableDebugFeatures?: boolean;
        enableDataCollection?: boolean;
        enablePreferenceCollection?: boolean;
        enableReviewerWorkflows?: boolean;
        enableWorkspaceUpload?: boolean;
        instructions?: {
            model?: string;
        };
        model?: string;
        nextEdit?: {
            allowDuringDebugging?: boolean;
            animateNoDiffMode?: boolean;
            backgroundEnabled?: boolean;
            enabled?: boolean;
            noDiffMode?: boolean;
            noDiffModeUseCodeLens?: boolean;
            showDiffByDefault?: boolean;
            url?: string;
            locationUrl?: string;
            generationUrl?: string;
            model?: string;
            showInstructionTextbox?: boolean;
            useDebounceMs?: number;
            useCursorDecorations?: boolean;
            useSmallHover?: boolean;
            useMockResults?: boolean;
        };
        nextEditURL?: string;
        nextEditLocationURL?: string;
        nextEditGenerationURL?: string;
        nextEditBackgroundGeneration?: boolean;
        oauth?: {
            clientID?: string;
            url?: string;
        };
        openFileManager?: {
            useV2?: boolean;
        };
        preferenceCollection?: {
            enable?: boolean;
            enableRetrievalDataCollection?: boolean;
            enableRandomizedMode?: boolean;
        };
        recencySignalManager?: {
            collectGitDiffEnabled?: boolean;
            collectTabSwitchEvents?: boolean;
            gitDiffPollingFrequencyMSec?: number;
        };
        smartPaste?: {
            url?: string;
            model?: string;
        };
        integrations?: {
            atlassian?: {
                serverUrl: string;
                personalApiToken: string;
                username: string;
            };
            notion?: {
                apiToken: string;
            };
            linear?: {
                apiToken: string;
            };
            github?: {
                apiToken: string;
            };
        };
        vcs?: {
            watcherEnabled?: boolean;
        };
    };
    apiToken?: string;
    completionURL?: string;
    completions?: {
        enableAutomaticCompletions?: boolean;
        disableCompletionsByLanguage?: string[];
        enableQuickSuggestions?: boolean;
    };
    emptyFileHint?: {
        enabled?: boolean;
    };
    enableAutomaticCompletions?: boolean;
    enableBackgroundSuggestions?: boolean;
    enableEmptyFileHint?: boolean;
    enableGlobalBackgroundSuggestions?: boolean;
    disableCompletionsByLanguage?: string[];
    enableShortcutsAboveSelectedText?: boolean;
    shortcutsDisplayDelayMS?: number;
    showAllBackgroundSuggestionLineHighlights?: boolean;
    chat?: {
        userGuidelines?: string;
    };
    conflictingCodingAssistantCheck?: boolean;
}

export interface ConfigurationChangeEvent {}

export type FileWillRenameEvent = {
    files: {
        oldUri: Uri;
        newUri: Uri;
    }[];
};

export interface SecretStorageChangeEvent {
    readonly key: string;
}

export interface FileSystemWatcher extends Disposable {
    readonly onDidCreate: Event<Uri>;
    readonly onDidChange: Event<Uri>;
    readonly onDidDelete: Event<Uri>;
}

export type CancellationToken = {
    isCancellationRequested: boolean;
    onCancellationRequested: Event<void>;
};

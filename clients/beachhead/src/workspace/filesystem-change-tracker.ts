import { execSync } from "child_process";
import { existsSync, F<PERSON><PERSON><PERSON><PERSON>, watch, WatchEventType } from "fs";
import * as fs from "fs";
import * as path from "path";

import { getErrmsg } from "../exceptions";
import { type AugmentLogger, getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { PathAcceptance } from "../utils/path-acceptance";
import { FullPathFilter } from "../utils/path-iterator";
import { normalizePathName } from "../utils/path-utils";
import { FileType } from "../utils/types";

/**
 * Represents a file change event for incremental tracking with content
 */
export interface FileChangeEvent {
    /** The normalized path of the file relative to the workspace root */
    path: string;
    /** The type of change: create, modify, or delete */
    changeType: "create" | "modify" | "delete";
    /** The modification time of the file (undefined for deletions) */
    mtime?: number;
    /** The content of the file before the change (undefined for create events or files exceeding size limit) */
    beforeContent?: Buffer;
    /** The content of the file after the change (undefined for delete events or files exceeding size limit) */
    afterContent?: Buffer;
}

/**
 * File metadata for incremental tracking with content storage
 */
export interface FileMetadata {
    /** The normalized path of the file relative to the workspace root */
    path: string;
    /** The modification time of the file */
    mtime: number;
    /** The content of the file at the time of tracking (undefined for files exceeding size limit) */
    content?: Buffer;
}

/**
 * Workspace state for incremental tracking with content storage
 */
export interface WorkspaceState {
    /** The timestamp when the state was captured */
    timestamp: number;
    /** A map of file paths to file metadata with content */
    files: Map<string, FileMetadata>;
}

/**
 * Tracks filesystem changes using filesystem notifications with monotonic timestamps
 * for optimized change detection. Stores file contents to provide before/after content
 * in change events.
 */
export class FilesystemChangeTracker extends DisposableService {
    private _logger: AugmentLogger;
    private _workspaceRoot: string;
    private _pathFilter: FullPathFilter;

    // Current workspace state with content storage
    private _currentState: WorkspaceState;

    // Filesystem watchers - maps directory path to watcher
    private _watchers = new Map<string, FSWatcher>();
    private _watcherInitialized = false;

    // Change tracking
    private _pendingChanges = new Map<string, FileChangeEvent>();
    private _debounceTimer: NodeJS.Timeout | undefined;
    private _debounceMs = 100; // 100ms debounce for batching rapid changes

    // Edge case handling
    private _recentChanges = new Map<string, number>(); // path -> last change timestamp
    private _changeHistoryMs = 1000; // Keep track of changes for 1 second
    private _maxPendingChanges = 10000; // Limit pending changes to prevent memory issues
    private _rapidChangeDebounceMs = 50; // Minimum time between changes to the same file

    // Change tracking for optimized API
    private _changesSinceTimestamp = new Map<number, FileChangeEvent[]>(); // timestamp -> changes
    private _maxChangeHistoryMs = 300000; // Keep 5 minutes of change history

    // File size limits
    private _maxFileSizeBytes: number;

    // Git baseline for content retrieval
    private _baselineCommit: string;
    private _isGitRepository: boolean;

    // Fallback file cache for non-git repositories
    private _baselineFileCache = new Map<string, Buffer>();

    constructor(
        workspaceRoot: string,
        pathFilter: FullPathFilter,
        maxFileSizeBytes: number = 1024 * 1024
    ) {
        super();
        this._logger = getLogger("FilesystemChangeTracker");
        this._workspaceRoot = workspaceRoot;
        this._pathFilter = pathFilter;
        this._maxFileSizeBytes = maxFileSizeBytes;

        // Initialize with empty state
        this._currentState = {
            timestamp: this.getCurrentTimestamp(),
            files: new Map<string, FileMetadata>(),
        };

        // Check if we're in a git repository
        this._isGitRepository = this._checkIsGitRepository();

        if (this._isGitRepository) {
            // Get the current commit hash for git-based content retrieval
            this._baselineCommit = this._getCurrentCommitHash();
            this._logger.debug(`Using git baseline commit: ${this._baselineCommit}`);
        } else {
            // Not in a git repository - use file cache fallback
            this._baselineCommit = "HEAD"; // Fallback value
            this._logger.debug("Not in a git repository, using file cache fallback");
            this._initializeFileCache();
        }

        // Start filesystem watcher
        const initializationStartTime = Date.now();
        this._initializeWatcher();
        const initializationTime = Date.now() - initializationStartTime;

        this._logger.debug(
            `FilesystemChangeTracker initialization completed in ${initializationTime}ms`
        );
    }

    /**
     * Safely read file content as Buffer, returning undefined if file cannot be read or exceeds size limit
     */
    private async _readFileContent(filePath: string): Promise<Buffer | undefined> {
        try {
            // Use lstat to check the file/symlink itself, not its target
            const stats = await fs.promises.lstat(filePath);

            // Skip directories - they cannot be read as files
            if (stats.isDirectory()) {
                this._logger.debug(
                    `Skipping directory ${filePath} - cannot read directory content`
                );
                return undefined;
            }

            // Handle symlinks specially
            if (stats.isSymbolicLink()) {
                try {
                    // For symlinks, we need to check if the target exists and get its size
                    const targetStats = await fs.promises.stat(filePath);
                    if (targetStats.size > this._maxFileSizeBytes) {
                        this._logger.debug(
                            `Symlink target ${filePath} exceeds size limit (${targetStats.size} > ${this._maxFileSizeBytes}), skipping content tracking`
                        );
                        return undefined;
                    }
                } catch (error) {
                    // Broken symlink - target doesn't exist
                    this._logger.debug(
                        `Skipping broken symlink ${filePath} - target does not exist`
                    );
                    return undefined;
                }
            } else {
                // Regular file - check size
                if (stats.size > this._maxFileSizeBytes) {
                    this._logger.debug(
                        `File ${filePath} exceeds size limit (${stats.size} > ${this._maxFileSizeBytes}), skipping content tracking`
                    );
                    return undefined;
                }
            }

            const content = await fs.promises.readFile(filePath);
            return content;
        } catch (error) {
            this._logger.error(`Could not read file content for ${filePath}: ${getErrmsg(error)}`);
            return undefined;
        }
    }

    /**
     * Check if the workspace is a git repository
     */
    private _checkIsGitRepository(): boolean {
        try {
            // Use git rev-parse --git-dir which is the standard way to check for git repo
            // This command is specifically designed for this purpose and produces minimal output
            execSync("git rev-parse --git-dir", {
                cwd: this._workspaceRoot,
                stdio: "pipe", // Suppress all output
            });
            return true;
        } catch (error) {
            // Not a git repository - this is normal and expected in many environments
            return false;
        }
    }

    /**
     * Get the current git commit hash for baseline content retrieval
     * Should only be called when we know we're in a git repository
     */
    private _getCurrentCommitHash(): string {
        try {
            const commit = execSync("git rev-parse HEAD", {
                cwd: this._workspaceRoot,
                encoding: "utf8",
                stdio: "pipe",
            }).trim();
            this._logger.debug(`Stored baseline commit: ${commit}`);
            return commit;
        } catch (error) {
            this._logger.error(`Failed to get git commit hash: ${getErrmsg(error)}`);
            return "HEAD"; // Fallback
        }
    }

    /**
     * Initialize file cache by scanning all workspace files when git is not available
     */
    private _initializeFileCache(): void {
        this._logger.debug("Initializing file cache for non-git repository");
        try {
            this._scanDirectoryForCache(".");
            this._logger.debug(
                `Cached ${this._baselineFileCache.size} files for baseline comparison`
            );
        } catch (error) {
            this._logger.error(`Failed to initialize file cache: ${getErrmsg(error)}`);
        }
    }

    /**
     * Recursively scan directory and cache all files
     */
    private _scanDirectoryForCache(relativePath: string): void {
        const fullPath = path.join(this._workspaceRoot, relativePath);

        try {
            const entries = fs.readdirSync(fullPath, { withFileTypes: true });

            for (const entry of entries) {
                const entryRelativePath = path.join(relativePath, entry.name).replace(/\\/g, "/");
                const entryFullPath = path.join(fullPath, entry.name);

                // Use path filter to check if we should include this file/directory
                if (
                    !this._pathFilter.acceptsPath(
                        entryRelativePath,
                        entry.isFile() ? FileType.file : FileType.directory
                    )
                ) {
                    continue;
                }

                if (entry.isFile() || entry.isSymbolicLink()) {
                    try {
                        // Use lstat for the entry itself, then stat for size check if it's a symlink
                        const lStats = fs.lstatSync(entryFullPath);
                        let fileSize = lStats.size;

                        // For symlinks, check if target exists and get its size
                        if (lStats.isSymbolicLink()) {
                            try {
                                const targetStats = fs.statSync(entryFullPath);
                                fileSize = targetStats.size;
                            } catch (error) {
                                // Broken symlink - skip it
                                this._logger.debug(
                                    `Skipping broken symlink ${entryRelativePath} during cache initialization`
                                );
                                continue;
                            }
                        }

                        if (fileSize <= this._maxFileSizeBytes) {
                            const content = fs.readFileSync(entryFullPath);
                            this._baselineFileCache.set(entryRelativePath, content);
                        }
                    } catch (error) {
                        this._logger.error(
                            `Error caching file ${entryRelativePath}: ${getErrmsg(error)}`
                        );
                    }
                } else if (entry.isDirectory()) {
                    this._scanDirectoryForCache(entryRelativePath);
                }
            }
        } catch (error) {
            this._logger.error(`Error scanning directory ${relativePath}: ${getErrmsg(error)}`);
        }
    }

    /**
     * Check if a file existed in the baseline commit
     */
    private _fileExistedInBaseline(relativePath: string): boolean {
        if (!this._isGitRepository) {
            // Use file cache for non-git repositories
            return this._baselineFileCache.has(relativePath);
        }

        try {
            // Use git cat-file to check if the file exists in the baseline commit
            // This is more efficient than listing all files
            execSync(`git cat-file -e ${this._baselineCommit}:"${relativePath}"`, {
                cwd: this._workspaceRoot,
                stdio: "pipe",
            });
            return true;
        } catch (error) {
            // File doesn't exist in baseline commit or git command failed
            return false;
        }
    }

    /**
     * Get file content from git baseline commit - used for "before" content when we don't have it cached
     */
    private _getFileContentFromGit(relativePath: string): Buffer | undefined {
        if (!this._isGitRepository) {
            // Use file cache for non-git repositories
            const cachedContent = this._baselineFileCache.get(relativePath);
            if (cachedContent) {
                this._logger.debug(`Retrieved content for ${relativePath} from file cache`);
            }
            return cachedContent;
        }

        try {
            // Use the baseline commit instead of HEAD
            const gitCommand = `git show ${this._baselineCommit}:"${relativePath}"`;
            const content = execSync(gitCommand, {
                cwd: this._workspaceRoot,
                encoding: "buffer",
                maxBuffer: this._maxFileSizeBytes,
            });
            this._logger.debug(
                `Retrieved content for ${relativePath} from baseline commit ${this._baselineCommit}`
            );
            return content;
        } catch (error) {
            this._logger.error(
                `Could not read file content from baseline commit for ${relativePath}: ${getErrmsg(error)}`
            );
            return undefined;
        }
    }

    /**
     * Create a FileChangeEvent with appropriate content based on change type
     */
    private async _createChangeEvent(
        relativePath: string,
        changeType: "create" | "modify" | "delete",
        mtime?: number
    ): Promise<FileChangeEvent> {
        const absPath = path.join(this._workspaceRoot, relativePath);
        const event: FileChangeEvent = {
            path: relativePath,
            changeType,
            mtime,
        };

        if (changeType === "create") {
            // Create: only afterContent
            event.afterContent = await this._readFileContent(absPath);
        } else if (changeType === "modify") {
            // Modify: both beforeContent and afterContent
            const existingFile = this._currentState.files.get(relativePath);

            // If we don't have cached content, try to get it from git
            if (existingFile?.content === undefined) {
                event.beforeContent = this._getFileContentFromGit(relativePath);
            } else {
                event.beforeContent = existingFile.content;
            }

            event.afterContent = await this._readFileContent(absPath);
        } else if (changeType === "delete") {
            // Delete: only beforeContent
            const existingFile = this._currentState.files.get(relativePath);

            // If we don't have cached content, try to get it from git
            if (existingFile?.content === undefined) {
                event.beforeContent = this._getFileContentFromGit(relativePath);
            } else {
                event.beforeContent = existingFile.content;
            }
        }

        return event;
    }

    /**
     * Get the current timestamp for change tracking (monotonic)
     * @returns Current monotonic timestamp that can be used with getChangesSince()
     */
    public getCurrentTimestamp(): number {
        // Use monotonic high-resolution timer to avoid system clock issues
        return Number(process.hrtime.bigint() / 1000000n); // Convert nanoseconds to milliseconds
    }

    /**
     * Wait for all pending changes to be processed
     * @returns Promise that resolves when all pending changes have been processed
     */
    public async waitForPendingChanges(): Promise<void> {
        // If there are no pending changes, return immediately
        if (this._pendingChanges.size === 0) {
            return;
        }

        // If there's a debounce timer, wait for it to complete
        if (this._debounceTimer) {
            return new Promise<void>((resolve) => {
                const originalTimer = this._debounceTimer;
                if (originalTimer) {
                    // Clear the existing timer and create a new one that resolves our promise
                    clearTimeout(originalTimer);
                    this._debounceTimer = setTimeout(() => {
                        void this._processChanges().then(() => resolve());
                    }, 0); // Process immediately
                } else {
                    resolve();
                }
            });
        }

        // If no timer but there are pending changes, process them immediately
        await this._processChanges();
    }

    /**
     * Get changes since a specific timestamp (much faster than snapshot comparison)
     * @param sinceTimestamp The timestamp to get changes since
     * @returns Array of file changes since the timestamp
     */
    public async getChangesSinceTimestamp(sinceTimestamp: number): Promise<FileChangeEvent[]> {
        // Wait for any pending changes to be processed first
        await this.waitForPendingChanges();

        const allChanges: FileChangeEvent[] = [];

        // Collect all processed changes since the timestamp
        for (const [timestamp, changes] of this._changesSinceTimestamp.entries()) {
            if (timestamp > sinceTimestamp) {
                allChanges.push(...changes);
            }
        }

        // Deduplicate changes by path (keep the most recent change for each file)
        const latestChanges = new Map<string, FileChangeEvent>();
        for (const change of allChanges) {
            const existing = latestChanges.get(change.path);
            if (!existing || (change.mtime && existing.mtime && change.mtime > existing.mtime)) {
                latestChanges.set(change.path, change);
            }
        }

        return Array.from(latestChanges.values());
    }

    /**
     * Initialize the filesystem watcher with robust error handling
     */
    private _initializeWatcher(): void {
        if (this._watcherInitialized) {
            return;
        }

        // Use a more conservative approach: watch only specific directories
        // instead of the entire workspace recursively to avoid symlink issues
        this._initializeSelectiveWatcher();
    }

    /**
     * Initialize a selective filesystem watcher that avoids problematic directories
     */
    private _initializeSelectiveWatcher(): void {
        try {
            // Watch the workspace root non-recursively first
            const rootWatcher = watch(
                this._workspaceRoot,
                { recursive: false, persistent: true },
                (eventType, filename) => {
                    this._safeHandleFileSystemEvent(eventType, filename);
                }
            );

            rootWatcher.on("error", (error: any) => {
                this._logger.error(`Filesystem watcher error (non-critical): ${getErrmsg(error)}`);
                // Don't disable on errors, just log them
            });

            // Track the root watcher
            this._watchers.set(".", rootWatcher);

            // Add additional watchers for important subdirectories, avoiding problematic ones
            this._addSelectiveSubdirectoryWatchers();

            this._watcherInitialized = true;
            this._logger.debug(
                `Selective filesystem watcher initialized for ${this._workspaceRoot}`
            );
        } catch (error) {
            this._logger.error(
                `Failed to initialize selective filesystem watcher: ${getErrmsg(error)}`
            );
            // Fall back to polling-based approach
            this._initializePollingFallback();
        }
    }

    /**
     * Add watchers for specific subdirectories, avoiding problematic ones like node_modules
     */
    private _addSelectiveSubdirectoryWatchers(): void {
        try {
            // Get immediate subdirectories
            const entries = fs.readdirSync(this._workspaceRoot, { withFileTypes: true });

            for (const entry of entries) {
                if (!entry.isDirectory()) {
                    continue;
                }

                const dirName = entry.name;
                const dirPath = path.join(this._workspaceRoot, dirName);

                // Check if this directory should be watched using the path filter
                const dirAccepted = this._pathFilter.acceptsPath(dirName, FileType.directory);
                if (!dirAccepted) {
                    this._logger.debug(`Skipping watcher for filtered directory: ${dirName}`);
                    continue;
                }

                this._addDirectoryWatcher(dirName, dirPath);
            }
        } catch (error) {
            this._logger.error(
                `Failed to add selective subdirectory watchers: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Add a watcher for a specific directory using non-recursive watching
     * to avoid issues with broken symlinks in subdirectories
     */
    private _addDirectoryWatcher(dirName: string, dirPath: string): void {
        // Don't add duplicate watchers
        if (this._watchers.has(dirName)) {
            return;
        }

        // Verify the directory exists and is accessible before trying to watch it
        try {
            const stats = fs.statSync(dirPath);
            if (!stats.isDirectory()) {
                this._logger.debug(`Skipping watcher for non-directory: ${dirName}`);
                return;
            }
        } catch (error) {
            this._logger.debug(
                `Skipping watcher for inaccessible directory ${dirName}: ${getErrmsg(error)}`
            );
            return;
        }

        // Double-check that this directory should be watched using the path filter
        const dirAccepted = this._pathFilter.acceptsPath(dirName, FileType.directory);
        if (!dirAccepted) {
            this._logger.debug(`Skipping watcher for filtered directory: ${dirName}`);
            return;
        }

        try {
            // Watch this directory non-recursively to avoid broken symlinks
            // We'll manually add watchers for subdirectories that pass the filter
            const subWatcher = watch(
                dirPath,
                { recursive: false, persistent: true },
                (eventType, filename) => {
                    const fullPath = filename ? path.join(dirName, filename) : dirName;
                    this._safeHandleFileSystemEvent(eventType, fullPath);
                }
            );

            subWatcher.on("error", (error: any) => {
                this._logger.error(
                    `Subdirectory watcher error for ${dirName}: ${getErrmsg(error)}`
                );
                // Close this specific watcher on error and remove from tracking
                subWatcher.close();
                this._watchers.delete(dirName);
            });

            // Track the watcher
            this._watchers.set(dirName, subWatcher);
            this._logger.debug(`Added filesystem watcher for directory: ${dirName}`);

            // Now add watchers for existing subdirectories that pass the filter
            this._addWatchersForSubdirectories(dirName, dirPath);
        } catch (error) {
            this._logger.error(`Failed to watch subdirectory ${dirName}: ${getErrmsg(error)}`);
            // Continue with other directories
        }
    }

    /**
     * Add watchers for subdirectories that pass the path filter
     */
    private _addWatchersForSubdirectories(parentDirName: string, parentDirPath: string): void {
        try {
            const entries = fs.readdirSync(parentDirPath, { withFileTypes: true });

            for (const entry of entries) {
                if (!entry.isDirectory()) {
                    continue;
                }

                const subDirName = path.join(parentDirName, entry.name);
                const subDirPath = path.join(parentDirPath, entry.name);

                // Check if this subdirectory should be watched using the path filter
                const subDirAccepted = this._pathFilter.acceptsPath(subDirName, FileType.directory);
                if (subDirAccepted) {
                    // Recursively add watchers for this subdirectory
                    this._addDirectoryWatcher(subDirName, subDirPath);
                } else {
                    this._logger.debug(`Skipping watcher for filtered subdirectory: ${subDirName}`);
                }
            }
        } catch (error) {
            this._logger.debug(
                `Error reading subdirectories of ${parentDirName}: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Scan a directory for existing files and generate events for them
     */
    private async _scanDirectoryForExistingFiles(dirName: string, dirPath: string): Promise<void> {
        try {
            const entries = fs.readdirSync(dirPath, { withFileTypes: true });

            for (const entry of entries) {
                const entryPath = path.join(dirPath, entry.name);
                const relativePath = path.join(dirName, entry.name);

                if (entry.isFile() || entry.isSymbolicLink()) {
                    // Check if this file is accepted by the path filter
                    const pathInfo = this._pathFilter.getPathInfo(
                        relativePath,
                        entry.isFile() ? FileType.file : FileType.other
                    );

                    if (pathInfo.accepted) {
                        // Generate a create event for this existing file
                        try {
                            const stats = entry.isSymbolicLink()
                                ? fs.lstatSync(entryPath)
                                : fs.statSync(entryPath);

                            const changeEvent = await this._createChangeEvent(
                                relativePath,
                                "create",
                                Math.floor(stats.mtimeMs)
                            );

                            this._pendingChanges.set(relativePath, changeEvent);
                            this._logger.debug(
                                `Found existing file in new directory: ${relativePath}`
                            );
                        } catch (error) {
                            this._logger.error(
                                `Error reading existing file ${relativePath}: ${getErrmsg(error)}`
                            );
                        }
                    }
                } else if (entry.isDirectory()) {
                    // Check if this subdirectory should be watched using the path filter
                    const subDirAccepted = this._pathFilter.acceptsPath(
                        relativePath,
                        FileType.directory
                    );
                    if (subDirAccepted) {
                        // Recursively scan subdirectories
                        this._addDirectoryWatcher(relativePath, entryPath);
                        void this._scanDirectoryForExistingFiles(relativePath, entryPath);
                    } else {
                        this._logger.debug(
                            `Skipping watcher for filtered subdirectory: ${relativePath}`
                        );
                    }
                }
            }

            // Trigger processing if we found any files
            if (this._pendingChanges.size > 0) {
                this._debouncedProcessChanges();
            }
        } catch (error) {
            this._logger.error(`Error scanning directory ${dirName}: ${getErrmsg(error)}`);
        }
    }

    /**
     * Handle directory deletion by generating deletion events for any files that were in the directory
     * but weren't detected by individual file deletion events (due to race conditions)
     */
    private async _handleDirectoryDeletion(deletedDirPath: string): Promise<void> {
        try {
            // Find all files in our current state that were in the deleted directory
            const filesToDelete: string[] = [];

            for (const [filePath] of this._currentState.files) {
                // Check if this file was in the deleted directory or its subdirectories
                if (filePath.startsWith(deletedDirPath + "/") || filePath === deletedDirPath) {
                    // Only add to deletion list if we haven't already detected its deletion
                    if (
                        !this._pendingChanges.has(filePath) ||
                        this._pendingChanges.get(filePath)?.changeType !== "delete"
                    ) {
                        filesToDelete.push(filePath);
                    }
                }
            }

            this._logger.debug(
                `Directory ${deletedDirPath} deleted, generating deletion events for ${filesToDelete.length} files: ${filesToDelete.join(", ")}`
            );

            // Generate deletion events for all files that were in the directory
            for (const filePath of filesToDelete) {
                const fileMetadata = this._currentState.files.get(filePath);
                if (fileMetadata) {
                    const changeEvent = await this._createChangeEvent(filePath, "delete");

                    this._pendingChanges.set(filePath, changeEvent);
                    this._logger.debug(
                        `Generated deletion event for file in deleted directory: ${filePath}`
                    );
                }
            }

            // Trigger processing if we generated any deletion events
            if (filesToDelete.length > 0) {
                this._debouncedProcessChanges();
            }
        } catch (error) {
            this._logger.error(
                `Error handling directory deletion for ${deletedDirPath}: ${getErrmsg(error)}`
            );
        }
    }

    /**
     * Fallback when filesystem watching fails - just log and continue
     */
    private _initializePollingFallback(): void {
        this._logger.warn("Filesystem watching failed - change detection disabled");
        this._watcherInitialized = true;
    }

    /**
     * Safely handle filesystem events with proper error handling
     */
    private _safeHandleFileSystemEvent(eventType: WatchEventType, filename: string | null): void {
        this._logger.debug(`Raw filesystem event: ${eventType} ${filename}`);
        try {
            // Handle async filesystem events
            void this._handleFileSystemEvent(eventType, filename);
        } catch (error) {
            this._logger.error(
                `Error handling filesystem event for ${filename}: ${getErrmsg(error)}`
            );
            // Don't let individual file errors break the entire watcher
        }
    }

    /**
     * Handle filesystem events from the watcher
     */
    private async _handleFileSystemEvent(
        eventType: WatchEventType,
        filename: string | null
    ): Promise<void> {
        if (!filename) {
            return;
        }

        const absPath = path.join(this._workspaceRoot, filename);
        const normalizedPath = normalizePathName(filename);
        const fileAccepted = this._pathFilter.acceptsPath(normalizedPath, FileType.file);
        const dirAccepted = this._pathFilter.acceptsPath(normalizedPath, FileType.directory);

        if (!fileAccepted && !dirAccepted) {
            this._logger.debug(`Skipping event for filtered path: ${normalizedPath}`);
            return;
        }

        // Edge case: Prevent memory issues with too many pending changes
        if (this._pendingChanges.size >= this._maxPendingChanges) {
            this._logger.warn(
                `Too many pending changes (${this._pendingChanges.size}), processing immediately`
            );
            void this._processChanges();
        }

        // Edge case: Debounce rapid changes to the same file
        const now = this.getCurrentTimestamp(); // Use monotonic timestamp
        const lastChange = this._recentChanges.get(normalizedPath);
        if (lastChange && now - lastChange < this._rapidChangeDebounceMs) {
            this._logger.debug(`Debouncing rapid change for ${normalizedPath}`);
            return;
        }
        this._recentChanges.set(normalizedPath, now);

        // Clean up old change history
        this._cleanupChangeHistory(now);

        // Check if path should be accepted by the path filter
        let fileType: FileType;
        let acceptance: PathAcceptance;

        try {
            if (existsSync(absPath)) {
                const stats = fs.statSync(absPath);
                fileType = stats.isDirectory() ? FileType.directory : FileType.file;
            } else {
                fileType = FileType.file; // Assume file for deletions
            }

            acceptance = this._pathFilter.getPathInfo(normalizedPath, fileType);

            if (!acceptance.accepted) {
                return; // Skip ignored files
            }
        } catch (error) {
            this._logger.error(`Error checking file ${normalizedPath}: ${getErrmsg(error)}`);
            return;
        }

        // Handle directory creation by adding watchers for new directories
        if (fileType === FileType.directory) {
            if (eventType === "rename" && existsSync(absPath)) {
                // New directory created - add a watcher for it
                this._addDirectoryWatcher(normalizedPath, absPath);

                // Also check if there are already files in this directory that we missed
                void this._scanDirectoryForExistingFiles(normalizedPath, absPath);
            } else if (eventType === "rename" && !existsSync(absPath)) {
                // Directory deleted - check for any files that were in this directory
                // and generate deletion events for them if they weren't already detected
                void this._handleDirectoryDeletion(normalizedPath);
            }
            return; // Don't track directories as file changes
        }

        this._logger.debug(
            `Filesystem event: ${eventType} ${normalizedPath} (exists: ${existsSync(absPath)})`
        );

        // Check if this is a directory deletion by looking at our watchers
        if (eventType === "rename" && !existsSync(absPath)) {
            // Check if this was a directory we were watching
            if (this._watchers.has(normalizedPath)) {
                // This was a directory deletion - handle it specially
                void this._handleDirectoryDeletion(normalizedPath);
                return;
            }
        }

        // Determine change type and create event
        let changeEvent: FileChangeEvent;

        if (eventType === "rename") {
            // Check if file exists or is a symlink (including broken symlinks)
            let isSymlink = false;
            try {
                fs.lstatSync(absPath);
                isSymlink = fs.lstatSync(absPath).isSymbolicLink();
            } catch {
                // File doesn't exist and is not a symlink
            }

            if (existsSync(absPath) || isSymlink) {
                try {
                    const stats = isSymlink ? fs.lstatSync(absPath) : fs.statSync(absPath);

                    // Determine change type: use baseline commit for accurate change type detection
                    const changeType: "create" | "modify" = this._fileExistedInBaseline(
                        normalizedPath
                    )
                        ? "modify"
                        : "create";

                    changeEvent = await this._createChangeEvent(
                        normalizedPath,
                        changeType,
                        Math.floor(stats.mtimeMs)
                    );
                } catch (error) {
                    this._logger.error(
                        `Error reading stats for ${normalizedPath}: ${getErrmsg(error)}`
                    );
                    return;
                }
            } else {
                changeEvent = await this._createChangeEvent(normalizedPath, "delete");
            }
        } else if (eventType === "change") {
            try {
                const stats = fs.statSync(absPath);
                changeEvent = await this._createChangeEvent(
                    normalizedPath,
                    "modify",
                    Math.floor(stats.mtimeMs)
                );
            } catch (error) {
                // File might have been deleted
                changeEvent = await this._createChangeEvent(normalizedPath, "delete");
            }
        } else {
            return; // Unknown event type
        }

        // Add to pending changes and debounce processing
        this._logger.debug(
            `Adding to pending changes: ${changeEvent.changeType}:${normalizedPath}`
        );
        this._pendingChanges.set(normalizedPath, changeEvent);
        this._debouncedProcessChanges();
    }

    /**
     * Clean up old change history to prevent memory leaks
     */
    private _cleanupChangeHistory(now: number): void {
        for (const [path, timestamp] of this._recentChanges.entries()) {
            if (now - timestamp > this._changeHistoryMs) {
                this._recentChanges.delete(path);
            }
        }
    }

    /**
     * Clean up old change history by timestamp to prevent memory leaks
     */
    private _cleanupChangeHistoryByTimestamp(now: number): void {
        for (const [timestamp] of this._changesSinceTimestamp.entries()) {
            if (now - timestamp > this._maxChangeHistoryMs) {
                this._changesSinceTimestamp.delete(timestamp);
            }
        }
    }

    /**
     * Debounced processing of pending changes
     */
    private _debouncedProcessChanges(): void {
        if (this._debounceTimer) {
            clearTimeout(this._debounceTimer);
        }

        this._debounceTimer = setTimeout(() => {
            void this._processChanges();
        }, this._debounceMs);
    }

    /**
     * Process all pending changes and update the current snapshot
     */
    private async _processChanges(): Promise<void> {
        if (this._pendingChanges.size === 0) {
            return;
        }

        const changes = Array.from(this._pendingChanges.values());
        this._pendingChanges.clear();

        this._logger.debug(
            `Processing ${changes.length} file changes: ${changes.map((c) => `${c.changeType}:${c.path}`).join(", ")}`
        );

        let hasChanges = false;

        for (const change of changes) {
            try {
                await this._applyChange(change);
                hasChanges = true;
            } catch (error) {
                this._logger.error(`Error applying change for ${change.path}: ${getErrmsg(error)}`);
            }
        }

        if (hasChanges) {
            const now = this.getCurrentTimestamp(); // Use monotonic timestamp
            this._currentState.timestamp = now;

            // Store changes with timestamp for optimized getChangesSince
            this._changesSinceTimestamp.set(now, [...changes]);
            this._logger.debug(`Stored ${changes.length} changes at timestamp ${now}`);

            // Clean up old change history to prevent memory leaks
            this._cleanupChangeHistoryByTimestamp(now);
        }
    }

    /**
     * Apply a single file change to the current snapshot
     */
    private async _applyChange(change: FileChangeEvent): Promise<void> {
        const { path: filePath, changeType } = change;

        if (changeType === "delete") {
            if (this._currentState.files.has(filePath)) {
                this._currentState.files.delete(filePath);
                this._logger.debug(`Removed ${filePath} from state`);
            }
            return;
        }

        // For create/modify, read the file content
        const absPath = path.join(this._workspaceRoot, filePath);
        try {
            // Use lstat to handle symlinks properly
            const lStats = await fs.promises.lstat(absPath);
            let actualMtime = Math.floor(lStats.mtimeMs);

            // For symlinks, check if target exists
            if (lStats.isSymbolicLink()) {
                try {
                    const targetStats = await fs.promises.stat(absPath);
                    actualMtime = Math.floor(targetStats.mtimeMs);
                } catch (error) {
                    // Broken symlink - still track it but without content
                    this._logger.debug(`Tracking broken symlink ${filePath} without content`);
                    const fileMetadata: FileMetadata = {
                        path: filePath,
                        mtime: actualMtime,
                        content: undefined,
                    };
                    this._currentState.files.set(filePath, fileMetadata);
                    return;
                }
            }

            const content = await this._readFileContent(absPath);

            const fileMetadata: FileMetadata = {
                path: filePath,
                mtime: actualMtime,
                content,
            };

            this._currentState.files.set(filePath, fileMetadata);
            this._logger.debug(`Updated ${filePath} in state (${changeType})`);
        } catch (error) {
            this._logger.error(`Error reading file ${filePath}: ${getErrmsg(error)}`);
        }
    }

    /**
     * Dispose of resources
     */
    public dispose(): void {
        if (this._debounceTimer) {
            clearTimeout(this._debounceTimer);
        }

        // Close all watchers
        for (const [dirName, watcher] of this._watchers.entries()) {
            try {
                watcher.close();
            } catch (error) {
                this._logger.error(`Error closing watcher for ${dirName}: ${getErrmsg(error)}`);
            }
        }
        this._watchers.clear();

        super.dispose();
    }
}

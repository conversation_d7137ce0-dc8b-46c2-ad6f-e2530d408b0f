import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";

import { APIServer, BatchUploadR<PERSON>ult, FindMissingResult, UploadBlob } from "../augment-api";
import { getErrmsg } from "../exceptions";
import { type AugmentLogger, getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { logArray } from "../utils/logging";
import { MetricsCollector } from "../utils/metrics";
import { normalizePathName } from "../utils/path-utils";
import { promiseFromEvent, retryWithBackoff } from "../utils/promise-utils";
import { KVWorkQueue, PeriodicKicker } from "../utils/work-queue";
import * as vscode from "../vscode";
import { BlobTooLargeError } from "./blob-name-calculator";
import { PathMap } from "./path-map";
import { ContentType, PathHandler, PathType } from "./types";

/**
 * ProbeItem contains the set of information we keep for blobs that have been uploaded but have
 * not been confirmed as indexed.
 * - folderId: The folder id of the file.
 * - relPath: The file's path name relative to the folder's repoRoot.
 * - blobName: The blobName we are probing.
 * - mtime: The mtime of the file.
 * - startTime: The time when the item was enqueued for probing.
 */
type ProbeItem = {
    folderId: number;
    relPath: string;
    blobName: string;
    mtime: number;
    startTime: number;
};

/**
 * UploadQueueItem contains information about items that are waiting to be uploaded.
 */
type UploadQueueItem = { seq: number; folderId: number; relPath: string };

/**
 * UploadItem extends UploadBlob with additional information needed to track in-progress uploads.
 */
type UploadItem = UploadBlob & {
    seq: number;
    folderId: number;
    mtime: number;
    byteSize: number;
};

/**
 * UploadBatch is a batch of UploadItems for a batch upload request we are about to issue.
 */
class UploadBatch {
    public readonly items = new Map<string, Array<UploadItem>>();
    public byteSize = 0;

    constructor(
        public readonly maxItems: number,
        public readonly maxByteSize: number
    ) {}

    // addItem adds an item to the batch if doing so will not exceed either the maximum number
    // of items or the maximum bytes size
    public addItem(blobName: string, uploadItem: UploadItem): boolean {
        const items = this.items.get(blobName);
        if (items === undefined) {
            // This blob name is not yet in the batch.
            if (
                this.items.size >= this.maxItems ||
                this.byteSize + uploadItem.byteSize >= this.maxByteSize
            ) {
                // The batch is full.
                return false;
            }
            this.items.set(blobName, [uploadItem]);
            this.byteSize += uploadItem.byteSize;
        } else {
            // This blob name is already in the batch. Add this item to the existing list.
            items.push(uploadItem);
        }

        return true;
    }
}

/**
 * ProbeBatch is a batch of ProbeItems for a find-missing request we are about to issue.
 */
class ProbeBatch {
    public readonly items = new Map<number, ProbeItem>();

    constructor(public readonly maxItemCount: number) {}

    public get full(): boolean {
        return this.items.size >= this.maxItemCount;
    }

    public addItem(seq: number, probeItem: ProbeItem) {
        if (this.items.has(seq)) {
            // Sanity check -- this should never actually happen.
            return false;
        }
        this.items.set(seq, probeItem);
    }
}

/**
 * DiskFileManager manages the set of on-disk source files that are tracked by the extension. For
 * each path name, it computes a blob name (a hash of the path name and the file's contents), and
 * uploads the file's contents to the back end if it is not already there. The resulting blob names
 * are submitted to the PathMap after DiskFileManager determines that the blob has been uploaded
 * and has been fully indexed.
 *
 * To track changes to sources files as they change, DiskFileManager enters path names into a
 * four-stage pipeline with the following stages:
 * 1. Calculate:   Calculate the blob name for a given path name. (Next stage: Probe)
 * 2. Probe:       Query blob name status (see below).
 * 3. Upload:      Send the content for the unknown blob names to the server. (Next: Probe-retry)
 * 4. Probe-retry: Wait briefly before returning to the Probe state. (Next stage: Probe)
 *
 * The Probe stage determines the state a blob, which indicates which stage it will go to next.
 * The state is one of the following:
 * a. Unknown:     The blob name is not known to the server. (Next stage: Upload)
 * b. Non-indexed: The blob name is known to the server, but it is not completely indexed. (Next
 *                 stage: Probe-retry)
 * c. Indexed:     The blob name is known to the server and completely indexed. Blobs in this
 *                 state are emitted to the PathMap and exit the pipeline. This is the only (non-
 *                 error) way for a path name to exit the pipeline.
 */
export class DiskFileManager extends DisposableService {
    static readonly minProbeBatchSize = 1;
    static readonly maxProbeBatchSize = 1000;
    static readonly maxUploadBatchBlobCount = 128;
    static readonly maxUploadBatchByteSize = 1000000; // 1MB (Just under 2^20 for a buffer)
    static readonly probeRetryPeriodMs = 3 * 1000;
    static readonly probeBackoffAfterMs = 60 * 1000;
    static readonly probeRetryBackoffPeriodMs = 60 * 1000;

    // The set of reasons why a blob might not be trackable.
    readonly _notAPlainFile = "Not a file";
    readonly _fileNotAccessible = "File not readable";
    readonly _fileNotText = "Binary file";
    readonly _fileUploadFailure = "Upload failed";

    private readonly _onDidChangeInProgressItemCountEmitter = new vscode.EventEmitter<number>();
    public readonly onDidChangeInProgressItemCount =
        this._onDidChangeInProgressItemCountEmitter.event;

    private readonly _onQuiescedEmitter = new vscode.EventEmitter<void>();
    private readonly _onQuiesced = this._onQuiescedEmitter.event;

    // The ignoreBOM=true argument means the BOM (Byte-order-mark) will be
    // *included* in the decoded string (i.e. ignore special handling of the
    // BOM). If false, the BOM is not included with the string. We want the BOM
    // included with the contents sent to the server, to ensure the text
    // transmitted to the backend is the same as what was used locally to
    // compute the blob id.
    // The fatal=true argument means that if the decoder encounters an error,
    // it will throw a TypeError (for example parsing a utf-16 file).
    private _textDecoder = new TextDecoder("utf-8", {
        ignoreBOM: true,
        fatal: true,
    });

    // toCalculate is a WorkQueue of paths for which we need to calculate the blob name. The key
    // is the item's sequence number, and the value is the item's folder id and relative path name.
    // --> _toCalculate: KVWorkQueue<seq, [folderId, relPath]>
    private _toCalculate: KVWorkQueue<number, [number, string]>;

    // toProbe is a WorkQueue for recently generated blob names whose state we want to verify
    // with the back end. The key is the item's sequence number.
    // --> _toProbe: KVWorkQueue<seq, ProbeItem>
    private _toProbe: KVWorkQueue<number, ProbeItem>;

    // probeBatch is the probe batch we are currently building.
    private _probeBatch: ProbeBatch;
    private _probeBatchSize: number;

    // probeRetryWaiters and probeRetryBackoffWaiters are two WorkQueues of items that are waiting
    // to be indexed. Items appear in these maps either because they were just uploaded and we want
    // and want to wait briefly before probing (bevause indexing can take some time) or because a
    // previous probe said they were not yet indexed. _probeRetryWaiters is for items that just
    // started waiting; they are retried quickly. _probeRetryBackoffWaiters is for items that have
    // been waiting a longer time such that aggressive retry doesn't make sense (such as when we
    // have no connectivity). The key is the item's sequence number.
    // --> _probeRetry{Backoff}Waiters: KVWorkQueue<seq, ProbeItem>
    private _probeRetryWaiters: KVWorkQueue<number, ProbeItem>;
    private _probeRetryKicker: PeriodicKicker;
    private _probeRetryBackoffWaiters: KVWorkQueue<number, ProbeItem>;
    private _probeRetryBackoffKicker: PeriodicKicker;

    // toUpload is a WorkQueue of blob names that need to be uploaded to the server. The key is
    // the item's absolute path name. The value contains the rest of the information needed to
    // upload the blob. We don't allow the same absolute path name to appear multiple times in
    // this queue. We dedup items in _enqueueForUpload.
    // --> _toUpload: KVWorkQueue<absPath, UploadQueueItem>
    private _toUpload: KVWorkQueue<string, UploadQueueItem>;

    // _uploadBatch is the upload batch we are currently building.
    private _uploadBatch: UploadBatch;

    // _itemsInFlight is a set of items that are in flight. The key is the item's unique sequence
    // number. The value is the item's folder id and relative path name. The latter info isn't
    // actually needed. It's just helpful for debugging.
    // --> _itemsInFlight: Map<seq, [folderId, relPath]>
    private _itemsInFlight = new Map<number, [number, string]>();

    // _seq is a monotonically increasing sequence number. Every item we receive from either
    // ingestPath or uploadBlobNames gets a unique sequence number, which we use to track each
    // item and to report the number of items in flight.
    private _seq = 1000;

    public readonly metrics = new MetricsCollector("File metrics");
    private readonly _logger: AugmentLogger;
    private _stopping = false;

    private _pathsAccepted = this.metrics.counterMetric("paths accepted");
    private _pathsNotAccessible = this.metrics.counterMetric("paths not accessible");
    private _nonFiles = this.metrics.counterMetric("not plain files");
    private _largeFiles = this.metrics.counterMetric("large files");
    private _blobNameCalculationFails = this.metrics.counterMetric("blob name calculation fails");
    private _encodingErrors = this.metrics.counterMetric("encoding errors");
    private _mtimeCacheHits = this.metrics.counterMetric("mtime cache hits");
    private _mtimeCacheMisses = this.metrics.counterMetric("mtime cache misses");
    private _probeBatches = this.metrics.counterMetric("probe batches");
    private _blobNamesProbed = this.metrics.counterMetric("blob names probed");
    private _filesRead = this.metrics.counterMetric("files read");
    private _blobsUploaded = this.metrics.counterMetric("blobs uploaded");
    private _ingestPathMs = this.metrics.timingMetric("ingestPath");
    private _probeMs = this.metrics.timingMetric("probe");
    private _statMs = this.metrics.timingMetric("stat");
    private _readMs = this.metrics.timingMetric("read");
    private _uploadMs = this.metrics.timingMetric("upload");

    constructor(
        public readonly workspaceName: string,
        private readonly _apiServer: APIServer,
        private readonly _pathHandler: PathHandler,
        private readonly _pathMap: PathMap,
        probeBatchSize?: number
    ) {
        super();
        this._logger = getLogger(`DiskFileManager[${workspaceName}]`);

        if (probeBatchSize === undefined) {
            this._probeBatchSize = DiskFileManager.maxProbeBatchSize;
        } else {
            if (probeBatchSize < DiskFileManager.minProbeBatchSize) {
                this._logger.verbose(
                    `Rejecting requested probe batch size of ${probeBatchSize} ` +
                        `(min = ${DiskFileManager.minProbeBatchSize})`
                );
            } else if (probeBatchSize > DiskFileManager.maxProbeBatchSize) {
                this._logger.verbose(
                    `Rejecting requested probe batch size of ${probeBatchSize} ` +
                        `(max = ${DiskFileManager.maxProbeBatchSize})`
                );
            }
            this._probeBatchSize = Math.max(
                Math.min(probeBatchSize, DiskFileManager.maxProbeBatchSize),
                DiskFileManager.minProbeBatchSize
            );
        }

        /*
         * Calculate
         */

        this._toCalculate = new KVWorkQueue<number, [number, string]>(this._calculate.bind(this));
        this.addDisposable(this._toCalculate);

        /*
         * Probe
         */

        this._toProbe = new KVWorkQueue<number, ProbeItem>(this._probe.bind(this));
        this.addDisposable(this._toProbe);
        this._probeBatch = this._newProbeBatch();

        /*
         * Upload
         */

        this._toUpload = new KVWorkQueue<string, UploadQueueItem>(this._upload.bind(this));
        this.addDisposable(this._toUpload);
        this._uploadBatch = this._newUploadBatch();

        /*
         * Probe-retry
         */

        this._probeRetryWaiters = new KVWorkQueue<number, ProbeItem>(
            this._enqueueForProbe.bind(this)
        );
        this.addDisposable(this._probeRetryWaiters);

        this._probeRetryKicker = new PeriodicKicker(
            this._probeRetryWaiters,
            DiskFileManager.probeRetryPeriodMs
        );
        this.addDisposable(this._probeRetryKicker);

        this._probeRetryBackoffWaiters = new KVWorkQueue<number, ProbeItem>(
            this._enqueueForProbe.bind(this)
        );
        this._probeRetryBackoffKicker = new PeriodicKicker(
            this._probeRetryBackoffWaiters,
            DiskFileManager.probeRetryBackoffPeriodMs
        );
        this.addDisposable(this._probeRetryBackoffKicker);
    }

    public stop(): void {
        this.dispose();
    }

    public dispose(): void {
        this._stopping = true;
        super.dispose();
    }

    public get probeBatchSize(): number {
        return this._probeBatchSize;
    }

    public get itemsInFlight(): number {
        return this._itemsInFlight.size;
    }

    // ingestPath begins the process of ingesting the given path name.
    public ingestPath(folderId: number, relPath: string): void {
        this._ingestPathMs.start();
        if (this._stopping) {
            return;
        }
        relPath = normalizePathName(relPath);
        this._enqueueForCalculate(folderId, relPath);
        this._ingestPathMs.stop();
    }

    // awaitQuiesced waits until all submitted path names have been uploaded and fully indexed.
    public async awaitQuiesced(): Promise<void> {
        if (this._stopping || this._itemsInFlight.size === 0) {
            return;
        }
        return promiseFromEvent(this._onQuiesced);
    }

    private _nextSeq(): number {
        return this._seq++;
    }

    private _makeAbsPath(folderId: number, relPath: string): string | undefined {
        const repoRoot = this._pathMap.getRepoRoot(folderId);
        if (repoRoot === undefined) {
            return undefined;
        }
        return joinPath(repoRoot, relPath);
    }

    private _fileTooLargeString(fileSize: number): string {
        return `File too large (${fileSize} > ${this._pathHandler.maxBlobSize})`;
    }

    private _getMtime(
        absPath: string,
        folderId: number,
        relPath: string,
        seq: number
    ): number | undefined {
        this._statMs.start();
        const pathInfo = this._pathHandler.classifyPath(absPath);
        this._statMs.stop();
        switch (pathInfo.type) {
            case PathType.inaccessible:
                this._pathsNotAccessible.increment();
                this._pathMapInvalidate(folderId, relPath, seq, this._fileNotAccessible);
                return undefined;
            case PathType.notAFile:
                this._nonFiles.increment();
                this._pathMapInvalidate(folderId, relPath, seq, this._notAPlainFile);
                return undefined;
            case PathType.largeFile:
                this._largeFiles.increment();
                this._pathMapInvalidate(
                    folderId,
                    relPath,
                    seq,
                    this._fileTooLargeString(pathInfo.size)
                );
                return undefined;
            case PathType.accepted:
                return pathInfo.mtime;
        }
    }

    private async _readAndValidate(
        absPath: string,
        folderId: number,
        relPath: string,
        seq: number
    ): Promise<Uint8Array | undefined> {
        this._readMs.start();
        const fileContents = await this._pathHandler.readText(absPath);
        this._readMs.stop();
        this._filesRead.increment();

        switch (fileContents.type) {
            case ContentType.inaccessible:
                this._pathsNotAccessible.increment();
                this._pathMapInvalidate(folderId, relPath, seq, this._fileNotAccessible);
                return undefined;
            case ContentType.largeFile:
                this._largeFiles.increment();
                this._pathMapInvalidate(
                    folderId,
                    relPath,
                    seq,
                    this._fileTooLargeString(fileContents.size)
                );
                return undefined;
            case ContentType.binary:
                this._pathMapInvalidate(folderId, relPath, seq, this._fileNotText);
                return undefined;
            case ContentType.text:
                return fileContents.contents;
        }
    }

    // calculateBlobName generates the blob name for the given path.
    private _calculateBlobName(
        relPath: string,
        contents: Uint8Array,
        folderId: number,
        seq: number
    ): string | undefined {
        try {
            return this._pathHandler.calculateBlobName(relPath, contents);
        } catch (e: any) {
            if (e instanceof BlobTooLargeError) {
                this._largeFiles.increment();
                const reason = this._fileTooLargeString(contents.length);
                this._pathMapInvalidate(folderId, relPath, seq, reason);
            } else {
                this._blobNameCalculationFails.increment();
                this._pathMapInvalidate(folderId, relPath, seq, getErrmsg(e));
            }
            return undefined;
        }
    }

    // _calculate calculates the blob name for the given path and then enqueues it to the next
    // stage of the pipeline (probe).
    private async _calculate(item: [number, [number, string]] | undefined): Promise<void> {
        if (item === undefined) {
            return;
        }
        const [seq, [folderId, relPath]] = item;

        if (!this._pathMapVerify(folderId, relPath, seq)) {
            // This folder or path is no longer being tracked.
            return;
        }
        const absPath = this._makeAbsPath(folderId, relPath);
        if (absPath === undefined) {
            // This folder is no longer being tracked.
            this._inflightItemRemove(seq);
            return;
        }

        const mtime = this._getMtime(absPath, folderId, relPath, seq);
        if (mtime === undefined) {
            return;
        }

        let blobName;
        const blobInfo = this._pathMap.getBlobInfo(folderId, relPath, mtime);
        if (blobInfo !== undefined) {
            // We have already computed the blob name for this version of the file.
            this._mtimeCacheHits.increment();
            const [cachedBlobName, contentSeq] = blobInfo;
            if (contentSeq > 0) {
                // A nonzero content sequence number means we have verified this blob name already.
                // No need to verify it again.
                this._pathMapUpdate(folderId, relPath, seq, cachedBlobName, mtime);
                return;
            }
            blobName = cachedBlobName;
        } else {
            const contents = await this._readAndValidate(absPath, folderId, relPath, seq);
            if (contents === undefined) {
                return;
            }

            // (only count mtime cache misses for files we actually want to track)
            this._mtimeCacheMisses.increment();

            blobName = this._calculateBlobName(relPath, contents, folderId, seq);
            if (blobName === undefined) {
                return;
            }
        }
        this._pathsAccepted.increment();

        // Although we could probe the item right away, placing it in the retry queue
        // encourages batching of probe requests, leading to faster startup.
        const probeItem = {
            folderId,
            relPath,
            blobName,
            mtime,
            startTime: Date.now(),
        };
        this._enqueueForProbeRetry(seq, probeItem);
    }

    private _newProbeBatch(): ProbeBatch {
        return new ProbeBatch(this._probeBatchSize);
    }

    private _grabProbeBatch(): ProbeBatch | undefined {
        if (this._probeBatch.items.size === 0) {
            return undefined;
        }
        const batch = this._probeBatch;
        this._probeBatch = this._newProbeBatch();
        return batch;
    }

    private async _probe(item: [number, ProbeItem] | undefined): Promise<void> {
        if (item !== undefined) {
            const [seq, probeInfo] = item;
            if (!this._pathMapVerify(probeInfo.folderId, probeInfo.relPath, seq)) {
                return;
            }
            this._probeBatch.addItem(seq, probeInfo);
            if (!this._probeBatch.full) {
                return;
            }
        }

        // Grab the current batch (if any) and start a new one.
        const probeBatch = this._grabProbeBatch();
        if (probeBatch === undefined) {
            return;
        }

        const blobNames = new Set<string>();
        for (const [_seq, probeItem] of probeBatch.items) {
            blobNames.add(probeItem.blobName);
        }

        this._probeBatches.increment();
        this._blobNamesProbed.increment(probeBatch.items.size);

        this._logger.verbose(`probe ${blobNames.size} blobs`);
        this._probeMs.start();
        let result: FindMissingResult | undefined = undefined;
        try {
            result = await retryWithBackoff(
                async () => this._apiServer.findMissing([...blobNames]),
                this._logger
            );
        } catch {}
        this._probeMs.stop();

        if (result !== undefined) {
            this._logger.verbose(
                "find-missing reported " +
                    `${result.unknownBlobNames.length} unknown blob names and ` +
                    `${result.nonindexedBlobNames.length} nonindexed blob names.`
            );
            if (result.unknownBlobNames.length > 0) {
                this._logger.verbose("unknown blob names:");
                logArray(this._logger, "verbose", result.unknownBlobNames, 5);
            }

            if (result.nonindexedBlobNames.length > 0) {
                this._logger.verbose("nonindexed blob names:");
                logArray(this._logger, "verbose", result.nonindexedBlobNames, 5);
            }

            const unknownBlobNames = new Set(result.unknownBlobNames);
            const nonIndexedBlobNames = new Set(result.nonindexedBlobNames);
            const beginUploadBatch = this._beginUploadBatch();
            for (const [seq, probeItem] of probeBatch.items) {
                if (!this._pathMapVerify(probeItem.folderId, probeItem.relPath, seq)) {
                    continue;
                }
                // An unknown blob name may appear in both the unknown and non-indexed lists.
                // Hence it is important that we check the unknown list first. Otherwise we risk
                // waiting forever for an unknown blob to be indexed.
                if (unknownBlobNames.has(probeItem.blobName)) {
                    this._enqueueForUpload(seq, probeItem.folderId, probeItem.relPath, false);
                } else if (nonIndexedBlobNames.has(probeItem.blobName)) {
                    this._enqueueForProbeRetry(seq, probeItem);
                } else {
                    this._pathMapUpdate(
                        probeItem.folderId,
                        probeItem.relPath,
                        seq,
                        probeItem.blobName,
                        probeItem.mtime
                    );
                }
            }
            beginUploadBatch.dispose();
        } else {
            for (const [seq, probeItem] of probeBatch.items) {
                this._enqueueForProbeRetry(seq, probeItem);
            }
        }
    }

    private _newUploadBatch(): UploadBatch {
        return new UploadBatch(
            DiskFileManager.maxUploadBatchBlobCount,
            DiskFileManager.maxUploadBatchByteSize
        );
    }

    private _grabUploadBatch(): UploadBatch | undefined {
        if (this._uploadBatch.items.size === 0) {
            return undefined;
        }
        const currentBatch = this._uploadBatch;
        this._uploadBatch = this._newUploadBatch();
        return currentBatch;
    }

    private async _upload(item: [string, UploadQueueItem] | undefined): Promise<void> {
        let enqueueForNextBatch: UploadItem | undefined = undefined;
        if (item !== undefined) {
            const [absPath, { seq, folderId: folderId, relPath }] = item;
            if (!this._pathMapVerify(folderId, relPath, seq)) {
                return;
            }

            const mtime = this._getMtime(absPath, folderId, relPath, seq);
            if (mtime === undefined) {
                return;
            }

            const contents = await this._readAndValidate(absPath, folderId, relPath, seq);
            if (contents === undefined) {
                return;
            }

            const blobName = this._calculateBlobName(relPath, contents, folderId, seq);
            if (blobName === undefined) {
                return;
            }

            let text;
            try {
                text = this._textDecoder.decode(contents);
            } catch (e: any) {
                this._pathMapInvalidate(folderId, relPath, seq, getErrmsg(e));
                this._encodingErrors.increment();
                return undefined;
            }

            // If the current batch can accommodate the new item, add it to the batch. Otherwiwse,
            // fall through to upload the current batch and start a new one.
            const uploadItem = {
                seq,
                folderId,
                pathName: relPath,
                text,
                blobName,
                mtime,
                byteSize: contents.length,
                metadata: [],
            };
            if (this._uploadBatch.addItem(blobName, uploadItem)) {
                return;
            }

            enqueueForNextBatch = uploadItem;
        }

        const uploadBatch = this._grabUploadBatch();
        if (uploadBatch === undefined) {
            return;
        }

        if (enqueueForNextBatch !== undefined) {
            this._uploadBatch.addItem(enqueueForNextBatch.blobName, enqueueForNextBatch);
        }

        this._logger.verbose(`upload ${uploadBatch.items.size} blobs`);
        const uploadBlobs = new Array<UploadItem>();
        for (const [_, uploadItems] of uploadBatch.items) {
            // If we have more than one upload item with the same blob name, it is almost certainly
            // because multiple folders have the same file at the same relative path name. There is
            // no point in uploading them all, so we only upload the first one.
            uploadBlobs.push(uploadItems[0]);
        }
        this._uploadMs.start();
        const blobNameMap = await this._uploadBlobBatch(uploadBlobs);
        this._uploadMs.stop();
        this._blobsUploaded.increment(blobNameMap.size);

        // Wait briefly and then probe the uploaded blob names. Invalidate any path names we
        // weren't able to upload.
        for (const [origBlobName, uploadItems] of uploadBatch.items) {
            const newBlobName = blobNameMap.get(origBlobName);
            if (newBlobName === undefined) {
                for (const uploadItem of uploadItems) {
                    this._pathMapInvalidate(
                        uploadItem.folderId,
                        uploadItem.pathName,
                        uploadItem.seq,
                        this._fileUploadFailure
                    );
                }
            } else {
                for (const uploadItem of uploadItems) {
                    const probeItem = {
                        folderId: uploadItem.folderId,
                        relPath: uploadItem.pathName,
                        blobName: newBlobName,
                        mtime: uploadItem.mtime,
                        startTime: Date.now(),
                    };
                    this._enqueueForProbeRetry(uploadItem.seq, probeItem);
                }
            }
        }
    }

    // _uploadBlobBatch uploads the given array of blobs to the server. The return value is a
    // map of the blob names we computed locally to the blob names returned from the back end.
    // We expect these returned names to be the same as those we computed, but if they ever
    // differ, we use the returned name. If a blob fails to upload, it will not be present in
    // the map.
    private async _uploadBlobBatch(uploadBlobs: Array<UploadItem>): Promise<Map<string, string>> {
        this._logger.verbose(`upload begin: ${uploadBlobs.length} blobs`);
        for (const blob of uploadBlobs) {
            this._logger.verbose(
                `    - ${blob.folderId}:${blob.pathName}; expected blob name ${blob.blobName}`
            );
        }

        let result: BatchUploadResult | undefined;
        try {
            result = await retryWithBackoff(
                async () => await this._apiServer.batchUpload(uploadBlobs),
                this._logger
            );
        } catch (e: any) {
            this._logger.error(`batch upload failed: ${getErrmsg(e)}`);
        }

        const blobNameMap = new Map<string, string>();
        if (result !== undefined) {
            for (let idx = 0; idx < result.blobNames.length; idx++) {
                blobNameMap.set(uploadBlobs[idx].blobName, result.blobNames[idx]);
            }
        }

        await this._uploadBlobsSequentially(
            uploadBlobs,
            result?.blobNames.length ?? 0,
            blobNameMap
        );

        return blobNameMap;
    }

    // _uploadBlobsSequentially uploads blobs from the given array sequentially. The first
    // `startIdx` items in `blobsToUpload` are already uploaded, so the sequential upload starts
    // at that point. The return value is the set of sequence numbers that failed to upload.
    async _uploadBlobsSequentially(
        blobsToUpload: Array<UploadBlob>,
        startIdx: number,
        blobNameMap: Map<string, string>
    ) {
        for (let idx = startIdx; idx < blobsToUpload.length; idx++) {
            const blob = blobsToUpload[idx];
            try {
                this._logger.verbose(`sequential upload of ${blob.pathName} -> ${blob.blobName}`);
                const result = await retryWithBackoff(
                    async () =>
                        this._apiServer.memorize(blob.pathName, blob.text, blob.blobName, []),
                    this._logger
                );
                blobNameMap.set(blob.blobName, result.blobName);
            } catch {}
        }
    }

    private _inflightItemAdd(seq: number, folderId: number, relPath: string): void {
        this._itemsInFlight.set(seq, [folderId, relPath]);
    }

    private _inflightItemRemove(seq: number): void {
        this._itemsInFlight.delete(seq);
        this._onDidChangeInProgressItemCountEmitter.fire(this._itemsInFlight.size);
        if (this._itemsInFlight.size === 0) {
            this._logger.verbose(`inflight items signaling empty`);
            this._onQuiescedEmitter.fire();
        }
    }

    private _pathMapVerify(folderId: number, relPath: string, seq: number): boolean {
        if (!this._pathMap.shouldTrack(folderId, relPath)) {
            this._inflightItemRemove(seq);
            return false;
        }
        const pathMapSeq = this._pathMap.getContentSeq(folderId, relPath);
        if (pathMapSeq !== undefined && pathMapSeq >= seq) {
            // The path map already contains content for this path name at a higher sequence
            // number. There is no point in processing the current sequence number.
            this._inflightItemRemove(seq);
            return false;
        }
        return true;
    }

    private _pathMapUpdate(
        folderId: number,
        relPath: string,
        seq: number,
        blobName: string,
        mtime: number
    ) {
        this._inflightItemRemove(seq);
        this._pathMap.update(folderId, relPath, seq, blobName, mtime);
    }

    private _pathMapInvalidate(
        folderId: number,
        relPath: string,
        seq: number,
        reason: string
    ): void {
        this._logger.verbose(`path map invalidate: ${folderId}:${relPath} (${reason})`);
        this._pathMap.markUntrackable(folderId, relPath, seq, reason);
        this._inflightItemRemove(seq);
        this._pathMap.markUntrackable(folderId, relPath, seq, reason);
    }

    private _enqueueForCalculate(folderId: number, relPath: string): void {
        const seq = this._nextSeq();

        // Add the item to the in-flight set even before we know whether it will be accepted.
        // If we wait until after we know, we run the risk of the item completing and being
        // "removed" before we add it.
        this._inflightItemAdd(seq, folderId, relPath);
        if (this._toCalculate.insert(seq, [folderId, relPath])) {
            void this._toCalculate.kick();
        } else {
            this._inflightItemRemove(seq);
        }
    }

    private _enqueueForProbe(item: [number, ProbeItem] | undefined): Promise<void> {
        if (item === undefined) {
            void this._toProbe.kick();
        } else {
            const [seq, probeItem] = item;
            this._logger.verbose(
                `probe enqueue ${probeItem.blobName} -> ` +
                    `${seq}, ${probeItem.folderId}:${probeItem.relPath}`
            );
            this._toProbe.insert(seq, probeItem);
        }
        return Promise.resolve();
    }

    private _beginUploadBatch(): vscode.Disposable {
        return new vscode.Disposable(() => this._toUpload.kick());
    }

    private _enqueueForUpload(seq: number, folderId: number, relPath: string, kick = true): void {
        this._logger.verbose(`upload enqueue ${folderId}:${relPath} -> ${seq}`);

        const absPath = this._makeAbsPath(folderId, relPath);
        if (absPath === undefined) {
            // The path map is no longer tracking this folder.
            this._inflightItemRemove(seq);
            return;
        }

        // If this path is already in the queue, keep the one with the higher sequence number.
        const uploadQueueItem = this._toUpload.get(absPath);
        if (uploadQueueItem !== undefined) {
            const prevSeq = uploadQueueItem.seq;
            if (prevSeq > seq) {
                this._inflightItemRemove(prevSeq);
            } else if (prevSeq < seq) {
                this._inflightItemRemove(seq);
                return;
            }
        }

        this._toUpload.insert(absPath, { seq, folderId, relPath }, true);
        if (kick) {
            void this._toUpload.kick();
        }
    }

    private _enqueueForProbeRetry(seq: number, probeItem: ProbeItem): void {
        // If we have only been waiting a short time, retry quickly. Otherwise, retry lazily.
        if (Date.now() - probeItem.startTime < DiskFileManager.probeBackoffAfterMs) {
            this._logger.verbose(
                "probe-retry enqueue " +
                    `${probeItem.blobName} -> ${seq}, ${probeItem.folderId}:${probeItem.relPath}`
            );
            this._probeRetryWaiters.insert(seq, probeItem);
        } else {
            this._logger.verbose(
                "probe-retry enqueue backoff " +
                    `${probeItem.blobName} -> ${seq}, ${probeItem.folderId}:${probeItem.relPath}`
            );
            this._probeRetryBackoffWaiters.insert(seq, probeItem);
        }
    }
}

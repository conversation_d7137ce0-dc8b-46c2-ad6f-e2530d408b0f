import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";

import { getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { PathAcceptance } from "../utils/path-acceptance";
import { directoryContainsPath, sameDirectory } from "../utils/path-utils";
import { FileType } from "../utils/types";
import * as vscode from "../vscode";
import { MtimeCacheWriter } from "./mtime-cache";
import { IQualifiedPathInfo } from "./types";

// PathStatusChangeEvent is an event that is fired when a path's status changes. A status change
// includes a change from tracked to untracked, or vice versa, or a change in the reason for being
// untracked.
export type PathStatusChangeEvent = {
    relPath: string;
};

// BlobNameChangeEvent is an event that is fired when a blob name is changed. The three fields are
// the absolute path name of the file, the previous blob name (if any), and the new blob name (if
// any).
export type BlobNameChangeEvent = {
    absPath: string;
    prevBlobName: string | undefined;
    newBlobName: string | undefined;
};

/**
 * PathMap is a class that maintains a map of information about paths in source folders. Paths in
 * the map have an associated type (file, directory, or "other"), and the type of the information
 * associated with each path depends on its type. Paths in the map also have information about
 * whether the path name is accepted by our various filters and rules.
 *
 * Some important attributes of PathMap are:
 *
 * Acceptance
 * ==========
 * Depending on its type, every path in the map has either one or two pieces of information about
 * whether the path is accepted. The first one ("path name acceptance"), which is present for all
 * types of paths, refers to whether the path name itself is accepted. The second one ("contents
 * acceptance"), which is present only for files, refers to whether the contents of the file are
 * accepted. Path name acceptance covers things like whether the path has a supported file extension
 * or whether it is ignored by an ignore file. Contents acceptance covers things like whether the
 * file is too large or is not a text file.
 *
 * Entry timestamps and sequence numbers
 * =====================================
 * Entries in the map have a timestamp (entryTS) that indicates when it was last inserted. The
 * timestamp (which is actually just a counter, not an actual timestamp) can be used to determine
 * whether an entry is older or newer than a given timestamp value. The `purge` method can be used
 * to remove entries that are older than a given timestamp.
 *
 * File entries also have a sequence number (contentSeq) that indicates when the contents of the
 * file were last updated (they are not present for directories or "other" entries). The contentSeq
 * is provided by the user of the map, hence it is the caller's responsibility to ensure it is
 * correct. The contentSeq can be used to determine whether a caller is mistakenly trying to
 * overwrite newer information with older information, as may happen when multiple asynchronous
 * workflows are operating on the same path. (For example, when a file is modified twice in short
 * order.)
 *
 * Insert and remove vs. update
 * ============================
 * The `update` method only updates information for paths that are already in the map -- it does
 * not insert new entries, even when it has information about a path that is not present. This rule
 * addresses a second potential race in which a file is updated and then removed right away. The
 * update operation begins a potentially long-running asynchronous operation. The remove operation,
 * by contrast, completes quickly and synchronously. This rule prevents the update operation from
 * mistakenly adding the path back to the map after the remove operation has removed it.
 *
 * Source Folders
 * ==============
 * A PathMap can track files of multiple source folders. Each open source folder is identified with
 * a unique "folder id". A source folder is added (opened) with `openSourceFolder` and removed
 * (closed) with `closeSourceFolder`. A path cannot be inserted into the map if its source folder
 * is not open; PathMap.insert will silently do nothing if it isn't.
 *
 * Persistence
 * ===========
 * The contents of the individual source folders can be written to a persistent cache via an
 * MtimeCacheWriter, which is provided by the `enablePersist` method.
 *
 * Additional details for the methods of PathMap can be found in RelativePathMap, below, as PathMap
 * delegates most of its implementation to RelativePathMap.
 */
export class PathMap {
    private _nextFolderId = 100;
    private readonly _sourceFolders: Map<number, RelativePathMap> = new Map();

    // _pathMapModifiedEmitter is an event emitter for blob name changes.
    private _blobNameChangedEmitter = new vscode.EventEmitter<BlobNameChangeEvent>();

    private _nextEntryTS = 1000;
    private readonly _logger = getLogger("PathMap");

    constructor() {}

    public dispose(): void {
        for (const [_, sourceFolder] of this._sourceFolders) {
            sourceFolder.dispose();
        }
    }

    public get nextEntryTS(): number {
        return this._nextEntryTS;
    }

    public get onDidChangeBlobName(): vscode.Event<BlobNameChangeEvent> {
        return this._blobNameChangedEmitter.event;
    }

    public onDidChangePathStatus(
        folderId: number
    ): vscode.Event<PathStatusChangeEvent> | undefined {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.onDidChangePathStatus;
    }

    // `openSourceFolder` opens a source folder if it is not already open and neither contains nor
    // is contained by an already open folder. We don't allow nested folders, because doing so would
    // mean that a path name could belong to multiple folders. This method treats an attempt to open
    // an already-open folder as an error.
    public openSourceFolder(folderRoot: string, repoRoot: string): number {
        for (const [_, sourceFolder] of this._sourceFolders) {
            if (sameDirectory(sourceFolder.folderRoot, folderRoot)) {
                throw new Error(`Source folder ${folderRoot} is already open`);
            }
            if (directoryContainsPath(folderRoot, sourceFolder.folderRoot)) {
                throw new Error(`Source folder ${folderRoot} contains ${sourceFolder.folderRoot}`);
            }
            if (directoryContainsPath(sourceFolder.folderRoot, folderRoot)) {
                throw new Error(`Source folder ${sourceFolder.folderRoot} contains ${folderRoot}`);
            }
        }

        const folderId = this._nextFolderId++;
        const sourceFolder = new RelativePathMap(folderRoot, repoRoot);
        this._sourceFolders.set(folderId, sourceFolder);
        const disposable = sourceFolder.onDidChangeBlobName(
            this._handleBlobNameChangeEvent.bind(this)
        );
        sourceFolder.addDisposable(disposable);
        this._logger.info(`Opened source folder ${folderRoot} with id ${folderId}`);
        return folderId;
    }

    // `closeSourceFolder` closes the indicated source folder.
    public closeSourceFolder(folderId: number): void {
        const sourceFolder = this._sourceFolders.get(folderId);
        if (sourceFolder === undefined) {
            return;
        }

        // Make the source folder report its paths as undefined to the event emitter.
        sourceFolder.clear();

        const folderRoot = sourceFolder.folderRoot;
        this._sourceFolders.delete(folderId);
        sourceFolder.dispose();
        this._logger.info(`Closed source folder ${folderRoot} with id ${folderId}`);
    }

    private _handleBlobNameChangeEvent(event: BlobNameChangeEvent): void {
        this._blobNameChangedEmitter.fire(event);
    }

    // `getRepoRoot` returns the root path of the indicated source folder, if the source folder is
    // known.
    public getRepoRoot(folderId: number): string | undefined {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.repoRoot;
    }

    // See RelativePathMap.hasFile.
    public hasFile(folderId: number, relPath: string): boolean {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.hasFile(relPath) ?? false;
    }

    // See RelativePathMap.getBlobName.
    public getBlobName(folderId: number, relPath: string): string | undefined {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.getBlobName(relPath);
    }

    // See RelativePathMap.getBlobInfo.
    public getBlobInfo(
        folderId: number,
        relPath: string,
        mtime: number
    ): [string, number] | undefined {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.getBlobInfo(relPath, mtime);
    }

    // `getAnyPathName` returns a qualified path name that maps to the given blob name, if any. If
    // more than one qualified path name maps to the blob name, one of them is chosen arbitrarily.
    public getAnyPathName(blobName: string): QualifiedPathName | undefined {
        for (const sourceFolder of this._sourceFolders.values()) {
            const pathName = sourceFolder.getPathName(blobName);
            if (pathName !== undefined) {
                return this._makeQualifiedPathName(sourceFolder, pathName);
            }
        }
        return undefined;
    }

    // `getAllPathNames` returns a (possibly empty) array of QualifiedPathNames that map to the
    // given blob name.
    public getAllPathNames(blobName: string): Array<QualifiedPathName> {
        const results = new Array<QualifiedPathName>();
        for (const sourceFolder of this._sourceFolders.values()) {
            const relPath = sourceFolder.getPathName(blobName);
            if (relPath !== undefined) {
                results.push(new QualifiedPathName(sourceFolder.repoRoot, relPath));
            }
        }
        return results;
    }

    public getUniquePathCount(blobName: string): number {
        let result = 0;
        for (const sourceFolder of this._sourceFolders.values()) {
            if (sourceFolder.getPathName(blobName) !== undefined) {
                result++;
            }
        }
        return result;
    }

    // `getAllQualifiedPathNames` returns a (possibly empty) array of QualifiedPathNames that map
    // to the given relative path.
    public getAllQualifiedPathNames(relPath: string): Array<QualifiedPathName> {
        return this.getAllQualifiedPathInfos(relPath).map((info) => info.qualifiedPathName);
    }

    // `getAllQualifiedPathInfos` returns a (possibly empty) array of QualifiedPathInfos that map
    // to the given relative path.
    public getAllQualifiedPathInfos(relPath: string): Array<IQualifiedPathInfo<QualifiedPathName>> {
        const results = new Array<IQualifiedPathInfo<QualifiedPathName>>();

        for (const sourceFolder of this._sourceFolders.values()) {
            const pathInfo = sourceFolder.getPathInfo(relPath);
            if (pathInfo !== undefined) {
                const [fileType, pathAcceptance] = pathInfo;
                results.push({
                    qualifiedPathName: new QualifiedPathName(sourceFolder.repoRoot, relPath),
                    fileType,
                    isAccepted: pathAcceptance.accepted,
                });
            }
        }

        return results;
    }

    // `getAllPathInfo` returns a (possibly empty) array of [folderRoot, repoRoot, relPath] triples
    // that map to the given blob name.
    public getAllPathInfo(blobName: string): Array<[string, string, string]> {
        const results = new Array<[string, string, string]>();
        for (const sourceFolder of this._sourceFolders.values()) {
            const relPath = sourceFolder.getPathName(blobName);
            if (relPath !== undefined) {
                results.push([sourceFolder.folderRoot, sourceFolder.repoRoot, relPath]);
            }
        }
        return results;
    }

    // `getPathInfo` returns the file type and path acceptance for the given path name, or
    // undefined if the path name is not present in the map.
    public getPathInfo(folderId: number, pathName: string): [FileType, PathAcceptance] | undefined {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.getPathInfo(pathName);
    }

    // See RelativePathMap.reportMissing.
    public reportMissing(blobName: string): QualifiedPathName | undefined {
        for (const sourceFolder of this._sourceFolders.values()) {
            const pathName = sourceFolder.reportMissing(blobName);
            if (pathName !== undefined) {
                return this._makeQualifiedPathName(sourceFolder, pathName);
            }
        }
        return undefined;
    }

    // `insert` adds a path name to the set of tracked paths. See RelativePathMap.insert for
    // details.
    public insert(
        folderId: number,
        pathName: string,
        fileType: FileType,
        pathAcceptance: PathAcceptance
    ): void {
        const entryTS = this._nextEntryTS++;
        const sourceFolder = this._sourceFolders.get(folderId);
        sourceFolder?.insert(pathName, entryTS, fileType, pathAcceptance);
    }

    // `remove` removes a path name from the set of tracked paths. See RelativePathMap.remove for
    // details.
    public remove(folderId: number, pathName: string): void {
        const sourceFolder = this._sourceFolders.get(folderId);
        sourceFolder?.remove(pathName);
    }

    // See RelativePathMap.shouldTrack.
    public shouldTrack(folderId: number, pathName: string): boolean {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.shouldTrack(pathName) ?? false;
    }

    // See RelativePathMap.getContentSeq.
    public getContentSeq(folderId: number, relPath: string): number | undefined {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.getContentSeq(relPath);
    }

    // See RelativePathMap.update.
    public update(
        folderId: number,
        pathName: string,
        contentSeq: number,
        blobName: string,
        mtime: number
    ): void {
        const sourceFolder = this._sourceFolders.get(folderId);
        sourceFolder?.update(pathName, contentSeq, blobName, mtime);
    }

    // See RelativePathMap.markUntrackable.
    public markUntrackable(folderId: number, pathName: string, seq: number, reason: string): void {
        const sourceFolder = this._sourceFolders.get(folderId);
        sourceFolder?.markUntrackable(pathName, seq, reason);
    }

    // See RelativePathMap.purge.
    public purge(folderId: number, oldestTS: number): void {
        const sourceFolder = this._sourceFolders.get(folderId);
        sourceFolder?.purge(oldestTS);
    }

    // pathsWithBlobNames returns an iterator over the files in the map that have blob names.
    // Directories, ignored paths, and paths without blob names are omitted. Entries have the form:
    // [folderId, repoRoot, relPath, mtime, blobName]
    public *pathsWithBlobNames(): Generator<[number, string, string, number, string]> {
        for (const [folderId, sourceFolder] of this._sourceFolders) {
            // (RelativePathMap's iterator filters out entries with no blob name.)
            for (const [relPath, mtime, blobName] of sourceFolder.pathsWithBlobNames()) {
                yield [folderId, sourceFolder.repoRoot, relPath, mtime, blobName];
            }
        }
    }

    // pathsInFolder returns an iterator over all paths in the given source folder. Entries have
    // the form: [relPath, FileType, accepted, indexed, reason]
    public *pathsInFolder(
        folderId: number
    ): Generator<[string, FileType, boolean, boolean, string]> {
        const sourceFolder = this._sourceFolders.get(folderId);
        if (sourceFolder === undefined) {
            return;
        }
        yield* sourceFolder.allPaths();
    }

    // See RelativePathMap.enablePersist.
    public enablePersist(
        folderId: number,
        mtimeCacheWriter: MtimeCacheWriter,
        frequencyMs: number
    ): void {
        const sourceFolder = this._sourceFolders.get(folderId);
        sourceFolder?.enablePersist(mtimeCacheWriter, frequencyMs);
    }

    // _makeQualifiedPathName returns a QualifiedPathName for the given source folder and relPath.
    // The returned path is relative to the source folder's repoRoot.
    private _makeQualifiedPathName(
        sourceFolder: RelativePathMap,
        relPath: string
    ): QualifiedPathName {
        return new QualifiedPathName(sourceFolder.repoRoot, relPath);
    }

    // See RelativePathMap.trackedFileCount.
    public trackedFileCount(folderId: number): number {
        const sourceFolder = this._sourceFolders.get(folderId);
        return sourceFolder?.trackedFileCount ?? 0;
    }

    public getFolderIds(): number[] {
        return Array.from(this._sourceFolders.keys());
    }
}

// TrackedFileInfo is the information RelativePathMap stores only for accepted file paths.
type TrackedFileInfo = {
    trackable: true;
    contentSeq: number;
    blobName: string;
    mtime: number;
};

// UntrackableFileInfo is the information RelativePathMap stores for accepted file paths that
// are untrackable.
type UntrackableFileInfo = {
    trackable: false;
    contentSeq: number;
    reason: string;
};

// PathInfo is the information RelativePathMap stores for all path names.
type PathInfo = {
    entryTS: number;
    fileType: FileType;
    pathAcceptance: PathAcceptance;
    fileInfo?: TrackedFileInfo | UntrackableFileInfo;
};

// PersistState tracks a RelativePathMap's persist state.
type PersistState = {
    // dirtyCount is the number of times the RelativePathMap has been modified since the last
    // persist. (Not necessarily the number of dirty entries.)
    dirtyCount: number;

    // lastPersistDirtyCount is the value of dirtyCounter when the last successful persist began.
    // If lastPersistTime > dirtyCounter then the map contains unpersisted changes.
    lastPersistDirtyCount: number;

    // persisting indicates whether a persist is in progress.
    persisting: boolean;

    // mtimeCacheWriter is the MtimeCacheWriter to use for persisting.
    mtimeCacheWriter: MtimeCacheWriter;
};

class RelativePathMap extends DisposableService {
    static readonly defaultPersistThreshold = 100;

    // These three structures contain the state of the map.
    // * _allPathNames is a map of all path names in the source folder. "All" really means all:
    //   this map contains paths of all types, whether they are accepted or not, whether they have
    //   blob names or not, whether they are trackable or not, etc.
    // * _trackableFilePaths is the set of file names for which we either have a blob name or we
    //   are expecting to get one (eg., the blob name computation is in progress). Membership in
    //   this set means that the path is a file (not a dir or other), it is accepted, and it has
    //   not been marked untrackable (it is a text file, it is not too large, ...).
    // * _blobNameToPathName is a map of blob names to their path names. The set of path names on
    //   the value side of this map is a subset of _trackableFilePaths. (Some paths in
    //   _trackableFilePaths may not have blob names yet, hence "subset".)
    private readonly _allPathNames: Map<string, PathInfo> = new Map();
    private readonly _trackableFilePaths: Set<string> = new Set();
    private readonly _blobNameToPathName: Map<string, string> = new Map();

    // _persistState is an object that controls persisting of the map. It is undefined if persisting
    // has not yet been enabled yet.
    private _persistState: PersistState | undefined = undefined;

    private readonly _pathStatusChangedEmitter = new vscode.EventEmitter<PathStatusChangeEvent>();
    private readonly _blobNameChangedEmitter = new vscode.EventEmitter<BlobNameChangeEvent>();

    constructor(
        public readonly folderRoot: string,
        public readonly repoRoot: string
    ) {
        super();
    }

    public get onDidChangePathStatus(): vscode.Event<PathStatusChangeEvent> {
        return this._pathStatusChangedEmitter.event;
    }

    public get onDidChangeBlobName(): vscode.Event<BlobNameChangeEvent> {
        return this._blobNameChangedEmitter.event;
    }

    // `trackedFileCount` returns the number of tracked files in the map. Note that we may not
    // yet have blob names for all of these files.
    public get trackedFileCount(): number {
        return this._trackableFilePaths.size;
    }

    // shouldTrack indicates whether the given pathName is a file and is accepted. It doesn't
    // indicate whether the file actually is tracked. An accepted file may not be tracked because
    // we haven't yet computed its blob name, or it is too large, or it is not a text file, or some
    // other reason. We would like to track every file for which this method returns true, but for
    // the reasons listed, some of them may not be tracked (or may not be tracked yet).
    public shouldTrack(pathName: string): boolean {
        const mapEntry = this._allPathNames.get(pathName);
        if (mapEntry === undefined) {
            return false;
        }
        return mapEntry.fileType === FileType.file && mapEntry.pathAcceptance.accepted;
    }

    // `getContentSeq` returns the content sequence number for the given path name, or undefined
    // if it doesn't have one. Note that an undefined content sequence number does not necessarily
    // mean that the path name is not present in the map -- it might be present but no info has
    // been recorded for it yet.
    public getContentSeq(pathName: string): number | undefined {
        const mapEntry = this._allPathNames.get(pathName);
        return mapEntry?.fileInfo?.contentSeq;
    }

    // `insert` inserts or updates the information for the given path name.
    public insert(
        pathName: string,
        entryTS: number,
        fileType: FileType,
        pathAcceptance: PathAcceptance
    ): void {
        let mapEntry = this._allPathNames.get(pathName);
        const statusChanged =
            mapEntry === undefined ||
            mapEntry.fileType !== fileType ||
            mapEntry.pathAcceptance.format() !== pathAcceptance.format();
        if (mapEntry === undefined) {
            mapEntry = { entryTS, fileType, pathAcceptance };
            this._allPathNames.set(pathName, mapEntry);
        } else {
            const fileInfo = mapEntry.fileInfo;
            mapEntry.entryTS = entryTS;
            mapEntry.fileType = fileType;
            mapEntry.pathAcceptance = pathAcceptance;
            if (!pathAcceptance.accepted) {
                mapEntry.fileInfo = undefined;
                if (fileInfo?.trackable) {
                    this._blobNameToPathName.delete(fileInfo.blobName);
                    this._publishBlobNameChange(pathName, fileInfo.blobName, undefined);
                    this._markDirty();
                }
            }
        }

        // _trackableFilePaths must contain all accepted, trackable file paths. For accepted files
        // where we don't yet know if they are trackable (mapEntry.fileInfo === undefined), we add
        // them to this set under the assumption that they will be trackable.
        if (
            mapEntry.fileType === FileType.file &&
            mapEntry.pathAcceptance.accepted &&
            mapEntry.fileInfo?.trackable !== false
        ) {
            this._trackableFilePaths.add(pathName);
        } else {
            this._trackableFilePaths.delete(pathName);
        }
        if (statusChanged) {
            this._pathStatusChangedEmitter.fire({ relPath: pathName });
        }
    }

    // `remove` removes the given path name from the map and reports it as undefined to the event
    // emitter.
    public remove(pathName: string): void {
        const mapEntry = this._allPathNames.get(pathName);
        if (mapEntry === undefined) {
            return;
        }
        this._allPathNames.delete(pathName);
        this._trackableFilePaths.delete(pathName);
        if (mapEntry.fileInfo !== undefined && mapEntry.fileInfo.trackable) {
            const blobName = mapEntry.fileInfo.blobName;
            this._blobNameToPathName.delete(blobName);
            this._publishBlobNameChange(pathName, blobName, undefined);
            this._markDirty();
        }
        this._pathStatusChangedEmitter.fire({ relPath: pathName });
    }

    // `clear` removes all paths from the map. It reports any files with known blob names to the
    // event emitter as undefined.
    // todo(mlm): Do we need to publish pathStatusChanges for the removed paths?
    public clear(): void {
        for (const [pathName, entry] of this._allPathNames) {
            if (entry.fileInfo !== undefined && entry.fileInfo.trackable) {
                this._publishBlobNameChange(pathName, entry.fileInfo.blobName, undefined);
            }
        }
        this._allPathNames.clear();
        this._trackableFilePaths.clear();
        this._blobNameToPathName.clear();
        this._markDirty();
    }

    // `update` associates the given mtime, blob name, and sequence number with the given path.
    public update(pathName: string, contentSeq: number, newBlobName: string, mtime: number): void {
        const mapEntry = this._allPathNames.get(pathName);
        if (
            mapEntry === undefined ||
            mapEntry.fileType !== FileType.file ||
            !mapEntry.pathAcceptance.accepted
        ) {
            // The given path is not an accepted file.
            return;
        }
        if (mapEntry.fileInfo !== undefined && mapEntry.fileInfo.contentSeq > contentSeq) {
            // The given information is older than what we already have.
            return;
        }

        // The file was previously reported as trackable if there is no fileInfo yet (in which
        // case we were just assuming it would turn out to be trackable) or it was explicitly
        // marked as trackable.
        const wasTrackable = mapEntry.fileInfo === undefined ? true : mapEntry.fileInfo.trackable;

        let oldBlobName: string | undefined = undefined;
        if (mapEntry.fileInfo?.trackable) {
            oldBlobName = mapEntry.fileInfo.blobName;
        }
        mapEntry.fileInfo = {
            trackable: true,
            contentSeq,
            blobName: newBlobName,
            mtime,
        };
        this._trackableFilePaths.add(pathName);

        if (newBlobName !== oldBlobName) {
            if (oldBlobName !== undefined) {
                this._blobNameToPathName.delete(oldBlobName);
            }
            this._blobNameToPathName.set(newBlobName, pathName);
            this._publishBlobNameChange(pathName, oldBlobName, newBlobName);
            this._markDirty();
        }
        if (!wasTrackable || oldBlobName === undefined) {
            // This path wasn't previously trackable or it wasn't previously indexed. Now it is
            // both of those things.
            this._pathStatusChangedEmitter.fire({ relPath: pathName });
        }
    }

    // `markUntrackable` records that the given pathName cannot be tracked.
    public markUntrackable(pathName: string, contentSeq: number, reason: string): void {
        const mapEntry = this._allPathNames.get(pathName);
        if (
            mapEntry === undefined ||
            mapEntry.fileType !== FileType.file ||
            !mapEntry.pathAcceptance.accepted
        ) {
            // The given path is not an accepted file.
            return;
        }
        if (mapEntry.fileInfo !== undefined && mapEntry.fileInfo.contentSeq > contentSeq) {
            // The given information is older than what we already have.
            return;
        }

        const fileInfo = mapEntry.fileInfo;
        mapEntry.fileInfo = { trackable: false, contentSeq, reason };
        this._trackableFilePaths.delete(pathName);

        let pathStatusChanged = false;
        if (fileInfo === undefined) {
            // This path has an undefined fileInfo, which means we had been assuming it was
            // trackable. We now see that it isn't.
            pathStatusChanged = true;
        } else if (fileInfo.trackable === true) {
            // This path was previously tracked, now it is untrackable.
            pathStatusChanged = true;
            const oldBlobName = fileInfo.blobName;
            this._blobNameToPathName.delete(oldBlobName);
            this._publishBlobNameChange(pathName, oldBlobName, undefined);
            this._markDirty();
        } else {
            // This path was previously untrackable. We report a status change if the untrackable
            // reason has changed.
            pathStatusChanged = reason !== fileInfo.reason;
        }
        if (pathStatusChanged) {
            this._pathStatusChangedEmitter.fire({ relPath: pathName });
        }
    }

    private _makeAbsPath(relPath: string): string {
        return joinPath(this.repoRoot, relPath);
    }

    private _publishBlobNameChange(
        pathName: string,
        prevBlobName: string | undefined,
        newBlobName: string | undefined
    ): void {
        if (prevBlobName === newBlobName) {
            return;
        }
        this._blobNameChangedEmitter.fire({
            absPath: this._makeAbsPath(pathName),
            prevBlobName: prevBlobName,
            newBlobName: newBlobName,
        });
    }

    // `purge` removes entries with entryTS < oldestTS from the map.
    public purge(oldestTS: number): void {
        const toDelete = new Array<string>();
        for (const [pathName, entry] of this._allPathNames) {
            if (entry.entryTS < oldestTS) {
                toDelete.push(pathName);
            }
        }
        for (const pathName of toDelete) {
            this.remove(pathName);
        }
    }

    // `hasFile` returns true if the given path name is a tracked file.
    public hasFile(pathName: string): boolean {
        return this._trackableFilePaths.has(pathName);
    }

    // `getBlobName` returns the blob name for the given path name, or undefined if the blob name
    // does not correspond to a tracked file. Otherwise it returns undefined.
    public getBlobName(pathName: string): string | undefined {
        const mapEntry = this._allPathNames.get(pathName);
        if (!mapEntry?.fileInfo?.trackable) {
            return undefined;
        }
        return mapEntry.fileInfo?.blobName;
    }

    // `getBlobInfo` returns the blob name and content sequence number for the given path name
    // if the given mtime matches mtime recorded for the path in the map. Otherwise it returns
    // undefined.
    public getBlobInfo(pathName: string, mtime: number): [string, number] | undefined {
        const mapEntry = this._allPathNames.get(pathName);
        if (!mapEntry?.fileInfo?.trackable) {
            return undefined;
        }
        if (mapEntry.fileInfo.mtime !== mtime) {
            return undefined;
        }
        return [mapEntry.fileInfo.blobName, mapEntry.fileInfo.contentSeq];
    }

    // `getPathName` returns the path name for the given blob name, or undefined if
    // the blob name has no corresponding path name.
    public getPathName(blobName: string): string | undefined {
        return this._blobNameToPathName.get(blobName);
    }

    // `getPathInfo` returns the file type and path acceptance for the given path name, or
    // undefined if the path name is not present in the map.
    public getPathInfo(pathName: string): [FileType, PathAcceptance] | undefined {
        const pathInfo = this._allPathNames.get(pathName);
        if (pathInfo === undefined) {
            return undefined;
        }
        return [pathInfo.fileType, pathInfo.pathAcceptance];
    }

    // `reportMissing` reports that the given blob name is missing. If there is a path name that
    // maps to the blob name, its content seq is set to 0 to indicate that the blob name has not
    // been verified and the path name is returned. Otherwise, undefined is returned.
    public reportMissing(blobName: string): string | undefined {
        const pathName = this._blobNameToPathName.get(blobName);
        if (pathName === undefined) {
            return undefined;
        }
        const mapEntry = this._allPathNames.get(pathName);
        if (!mapEntry?.fileInfo?.trackable) {
            return undefined;
        }
        mapEntry.fileInfo.contentSeq = 0;
        return pathName;
    }

    // pathsWithBlobNames returns an iterator over the files in the map that have blob names.
    // Directories, ignored paths, and paths without blob names are omitted. The returned entries
    // have the form:
    // [relPath, mtime, blobName, contentSeq]
    public *pathsWithBlobNames(): Generator<[string, number, string, number]> {
        for (const [pathName, pathInfo] of this._allPathNames) {
            const fileInfo = pathInfo.fileInfo;
            if (!fileInfo?.trackable) {
                continue;
            }
            yield [pathName, fileInfo.mtime, fileInfo.blobName, fileInfo.contentSeq];
        }
    }

    // allPaths returns an iterator over all paths in the map. The returned entries have the form:
    // [relPath, fileType, accepted, indexed, reason]
    public *allPaths(): Generator<[string, FileType, boolean, boolean, string]> {
        for (const [pathName, pathInfo] of this._allPathNames) {
            let accepted = pathInfo.pathAcceptance.accepted;
            let indexed: boolean = false;
            let reason = pathInfo.pathAcceptance.format();
            if (accepted) {
                if (pathInfo.fileType === FileType.other) {
                    // This path is accepted but is not a file or directory (likely a symlink).
                    // Don't report it as accepted.
                    accepted = false;
                    reason = "Not a file";
                } else if (pathInfo.fileInfo !== undefined) {
                    if (pathInfo.fileInfo?.trackable === true) {
                        indexed = true;
                    } else {
                        // This file's path was accepted but its contents are untrackable.
                        accepted = false;
                        reason = pathInfo.fileInfo.reason;
                    }
                }
            }
            yield [pathName, pathInfo.fileType, accepted, indexed, reason];
        }
    }

    // _markDirty marks the state of map as dirty with respect to its last-persisted state.
    private _markDirty(): void {
        if (this._persistState === undefined) {
            return;
        }
        this._persistState.dirtyCount++;
    }

    // `enablePersist` enables persisting of the contents of our our contents via the given
    // MtimeCacheWriter every `frequencyMs` milliseconds. Currently, this method is only called
    // once per PathMap, so we do not try to handle the case of multiple calls gracefully.
    public enablePersist(mtimeCacheWriter: MtimeCacheWriter, frequencyMs: number): void {
        if (this._persistState) {
            return;
        }
        this._persistState = {
            dirtyCount: this._trackableFilePaths.size,
            lastPersistDirtyCount: 0,
            mtimeCacheWriter: mtimeCacheWriter,
            persisting: false,
        };

        // Start a persist but don't wait for it to complete.
        void this._maybePersist();

        // Arrange to attempt a persist every `frequencyMs` milliseconds. We don't wait for the
        // persist to complete. If the next one starts before the previous one completes, it will
        // do nothing.
        const interval = setInterval(() => void this._maybePersist(), frequencyMs);
        this.addDisposable({
            dispose: () => clearInterval(interval),
        });
    }

    // _maybePersist starts a persist operation if persisting is enabled, the map has unpersisted
    // changes, and there isn't a persist already in progress.
    private async _maybePersist(): Promise<void> {
        if (this._persistState === undefined || this._persistState.persisting) {
            return;
        }
        this._persistState.persisting = true;
        try {
            if (this._persistState.dirtyCount > this._persistState.lastPersistDirtyCount) {
                await this._persist(this._persistState);
            }
        } finally {
            this._persistState.persisting = false;
        }
    }

    // _persist persists the contents of the map to its mtime cache writer.
    private async _persist(persistState: PersistState): Promise<void> {
        const iter = function* (
            inner: Iterable<[string, PathInfo]>
        ): Generator<[string, number, string]> {
            for (const [pathName, pathInfo] of inner) {
                const fileInfo = pathInfo.fileInfo;
                if (fileInfo?.trackable) {
                    yield [pathName, fileInfo.mtime, fileInfo.blobName];
                }
            }
        };
        const dirtyCount = persistState.dirtyCount;
        const writer = persistState.mtimeCacheWriter;
        await writer.write(iter(this._allPathNames.entries()));
        persistState.lastPersistDirtyCount = dirtyCount;
    }
}

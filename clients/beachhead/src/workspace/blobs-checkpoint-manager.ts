import { APIServer, Blo<PERSON>, CheckpointBlobsResult } from "../augment-api";
import { APIError } from "../exceptions";
import { FeatureFlagManager, FeatureFlags } from "../feature-flags";
import { type AugmentLogger, getLogger } from "../logging";
import { DisposableService } from "../utils/disposable-service";
import { APIStatus } from "../utils/types";
import * as vscode from "../vscode";
import { ErrorHandler, WorkQueue } from "../work-queue";
import { BlobNameChangeEvent } from "./path-map";

function setDifference<T>(arr1: T[], arr2: T[]): T[] {
    const set2 = new Set(arr2);
    return arr1.filter((item) => !set2.has(item));
}

/**
 * The BlobsCheckpointManager is responsible for computing the current set of blob
 * ids. When the size of blob ids grow too large, the BlobsCheckpointManager may
 * checkpoint the set with the backend, computing a checkpoint-id that concisely
 * represents the larger set.  A code completion request should consult the
 * BlobsCheckpointManager for the latest blobs to use.
 *
 * The blobs for a completion is represented as a +/- delta of blob ids
 * applied to a saved set of blob ids, called a checkpoint.
 */
export class BlobsCheckpointManager extends DisposableService {
    static readonly defaultCheckpointThreshold = 1000;

    // A checkpoint id represents a set of blob names known to the backend.
    private _checkpointId: string | undefined;
    // Map from each blob name in checkpoint to the number of
    // unique paths in the current workspace that map to it.
    // (blob names are computed using paths relative to workspaceFolder root, so
    // may not be unique within a workspace)
    private _checkpointBlobNames: Map<string, number> = new Map();

    // Blob names in the workspace but not in the checkpoint.
    private readonly _toAdd: Map<string, number> = new Map();
    // Blob names in the checkpoint, but not in the workspace (Set is sufficient
    // as the reference count is known to be 0).
    private readonly _toRemove: Set<string> = new Set();

    private readonly _apiServer: APIServer;
    private readonly _logger: AugmentLogger;

    // We allow only one checkpoint operation in flight at a time, but use a work queue since it
    // provides retry functionality.
    private readonly _checkpointQueue: WorkQueue<Blobs>;

    // The threshold at which to checkpoint the working set with the backend.
    private readonly _checkpointThreshold: number;
    private readonly _maxCheckpointBatchSize: number = 10000;

    // Feature flags related to the blobs checkpoint manager
    private readonly _featureFlagManager: FeatureFlagManager;

    // Event emitter for when the context changes
    private _onContextChange = new vscode.EventEmitter<Blobs>();
    public readonly onContextChange = this._onContextChange.event;

    private get _flags(): FeatureFlags {
        return this._featureFlagManager.currentFlags;
    }

    constructor(
        apiServer: APIServer,
        featureFlagManager: FeatureFlagManager,
        onDidChangeBlobName: vscode.Event<BlobNameChangeEvent>,
        checkpointThreshold?: number
    ) {
        super();

        this._logger = getLogger(`BlobsCheckpointManager`);

        this._checkpointId = undefined;
        this._apiServer = apiServer;
        this._featureFlagManager = featureFlagManager;
        this._checkpointThreshold =
            checkpointThreshold ?? BlobsCheckpointManager.defaultCheckpointThreshold;

        // Subscribe to blob name changes
        this.addDisposable(
            onDidChangeBlobName((event: BlobNameChangeEvent) => {
                this.updateBlob(event.absPath, event.prevBlobName, event.newBlobName);
            })
        );

        // We'll use a work queue of one item, since the work queue will handle restarts for us.
        this._checkpointQueue = new WorkQueue<Blobs>("checkpoint", {
            processOne: async (context: Blobs, errorHandler: ErrorHandler): Promise<void> =>
                await this._checkpoint(context, errorHandler),
        });

        this._logger.info(
            `BlobsCheckpointManager created. checkpointThreshold: ${this._checkpointThreshold}`
        );
    }

    refBlob(blob: string) {
        let val = this._checkpointBlobNames.get(blob);
        if (val !== undefined) {
            // Blob is in the checkpoint; increase its reference count, and remove
            // from the toRemove set if the blob was re-introduced to the workspace.
            this._checkpointBlobNames.set(blob, val + 1);
            if (val === 0) {
                this._toRemove.delete(blob);
            }
        } else {
            this._toAdd.set(blob, (this._toAdd.get(blob) ?? 0) + 1);
        }
    }

    derefBlob(blob: string) {
        if (!this.derefFromCheckpoint(blob) && !this.derefFromAdded(blob)) {
            // We're handling an onDidChangeBlobName event where a path referring
            // to 'blob' was deleted or replaced with another blob. So we expect
            // that 'blob' should be in the workspace state we're tracking.
            // It can be either in the checkpoint or the toAdd set, but being in
            // neither is a bug.
            this._logger.error(`derefBlob: blob ${blob} not found in checkpoint or toAdd`);
        }
    }

    derefFromCheckpoint(blob: string) {
        let val = this._checkpointBlobNames.get(blob);
        if (val === undefined || val <= 0) {
            // There's nothing for us to deref here. If the entry exists and indicates no references,
            // (or negative references which is _actually_ impossible, even with repeated deref calls)
            // that's a bug because we're trying to deref a blob that isn't in the workspace.
            if (val !== undefined) {
                this._logger.error(
                    `derefFromCheckpoint: blob ${blob} has reference count ${val}. In toRemove? ${this._toRemove.has(blob)}`
                );
            }
            return false;
        }
        this._checkpointBlobNames.set(blob, val - 1);
        if (val === 1) {
            this._toRemove.add(blob);
        }
        return true;
    }

    derefFromAdded(blob: string) {
        let val = this._toAdd.get(blob);
        if (val === undefined) {
            return false;
        }
        if (val <= 1) {
            this._toAdd.delete(blob);
            return val === 1;
        }
        this._toAdd.set(blob, val - 1);
        return true;
    }

    // Returns a flat array of all blobs names in the current checkpoint id.
    getCheckpointedBlobNames(): string[] {
        return Array.from(this._checkpointBlobNames.keys());
    }

    // Returns the set of blobs in the workspace
    getContext(): Blobs {
        return {
            checkpointId: this._checkpointId,
            addedBlobs: Array.from(this._toAdd.keys()),
            deletedBlobs: Array.from(this._toRemove),
        };
    }

    // For callers who wish to replace a few blobs in the workspace
    // context, e.g. with a different version of the file at the same
    // path. This interface allows them to still take advantage of the state
    // we're tracking to produce Blobs at low cost.
    getContextAdjusted(include: Set<string>, exclude: Set<string>): Blobs {
        let add = new Set<string>(this._toAdd.keys());
        let del = new Set<string>(this._toRemove);

        for (const blob of include) {
            if (!this._checkpointBlobNames.has(blob)) {
                add.add(blob);
            }
            del.delete(blob);
        }
        for (const blob of exclude) {
            if (this._checkpointBlobNames.has(blob)) {
                del.add(blob);
            }
            add.delete(blob);
        }
        return {
            checkpointId: this._checkpointId,
            addedBlobs: Array.from(add),
            deletedBlobs: Array.from(del),
        };
    }

    blobsPayload(blobNames: string[]): Blobs {
        let checkpointBlobNames = this.getCheckpointedBlobNames();
        let toAdd = setDifference<string>(blobNames, checkpointBlobNames);
        let toDelete = setDifference<string>(checkpointBlobNames, blobNames);
        let blobs = {
            checkpointId: this._checkpointId,
            addedBlobs: toAdd,
            deletedBlobs: toDelete,
        };
        // TODO(rich): debug the tracking differences between the two sets.
        // this.validateMatching(this.getContext(), blobs);
        return blobs;
    }

    // This function assumes a well-formed Blobs object
    // - addedBlobs doesn't refer to blobs in the checkpoint
    // - deletedBlobs only refers to blobs in the checkpoint
    // - corollary: addedBlobs and deletedBlobs are disjoint
    // Any Blobs object returned from this class is well-formed
    expandBlobs(blobs: Blobs): string[] {
        if (blobs.checkpointId === undefined) {
            return blobs.addedBlobs;
        }
        if (blobs.checkpointId !== this._checkpointId) {
            throw new Error(
                `expandBlobs: checkpointId mismatch: ${blobs.checkpointId} != ${this._checkpointId}`
            );
        }

        const blobNames = this.getCheckpointedBlobNames();
        blobNames.push(...blobs.addedBlobs);
        if (blobs.deletedBlobs.length > 0) {
            const deleteSet = new Set(blobs.deletedBlobs);
            return blobNames.filter((blobName) => !deleteSet.has(blobName));
        }
        return blobNames;
    }

    public validateMatching(left: Blobs, right: Blobs, verbose: boolean = false): boolean {
        if (left.checkpointId !== right.checkpointId) {
            this._logger.error(
                `checkpointId mismatch: ${left.checkpointId} vs ${right.checkpointId}`
            );
            return false;
        }
        let match = true;
        let leftExtra = setDifference<string>(left.addedBlobs, right.addedBlobs);
        let rightExtra = setDifference<string>(right.addedBlobs, left.addedBlobs);
        if (leftExtra.length > 0 || rightExtra.length > 0) {
            match = false;
            this._logger.error(`addedBlobs mismatch: -${leftExtra.length}/+${rightExtra.length}`);
            if (verbose) {
                this._logger.error(`left-added: ${leftExtra.slice(0, 5).join(",")}`);
                this._logger.error(`right-added: ${rightExtra.slice(0, 5).join(",")}`);
            }
        }
        leftExtra = setDifference<string>(left.deletedBlobs, right.deletedBlobs);
        rightExtra = setDifference<string>(right.deletedBlobs, left.deletedBlobs);
        if (leftExtra.length > 0 || rightExtra.length > 0) {
            match = false;
            this._logger.error(`deletedBlobs mismatch: -${leftExtra.length}/+${rightExtra.length}`);
            if (verbose) {
                this._logger.error(`left-deleted: ${leftExtra.slice(0, 5).join(",")}`);
                this._logger.error(`right-deleted: ${rightExtra.slice(0, 5).join(",")}`);
            }
        }
        return match;
    }

    // Notify about changes in blob names. An undefined for either blob name
    // argument indicates the blob name does not exist. For example, a file
    // creation will have an empty originalBlob name.
    // AU-2519: it's possible that the originalBlob and the newBlob are the same blob name.
    // In that case, we should not remove the original blob.
    public updateBlob(absPath: string, originalBlob?: string, newBlob?: string) {
        // Blob-name transitions:
        // undefined -> undefined: no change
        // undefined -> newBlob: new file, add new blob
        // originalBlob -> newBlob, where
        //   originalBlob != newBlob: updated file, remove original and add new
        //   originalBlob == newBlob: restating a known blob name, treat as a no-op
        // originalBlob -> undefined: deleted file, remove original

        this._logger.verbose(`notifyBlobChange ${absPath}: ${originalBlob} to ${newBlob}`);

        if (newBlob && newBlob !== originalBlob) {
            this.refBlob(newBlob);
        }
        if (originalBlob && newBlob !== originalBlob) {
            this.derefBlob(originalBlob);
        }

        if (
            this._toAdd.size + this._toRemove.size >= this._checkpointThreshold &&
            this._checkpointQueue.size() === 0
        ) {
            this._queueCheckpoint();
        }
    }

    // Clear the checkpointed blobs by moving the state back into the working
    // set. Caller is expected to kick off a checkpoint as necessary.
    public resetCheckpoint() {
        for (const [blob, count] of this._checkpointBlobNames) {
            if (count > 0) {
                this._toAdd.set(blob, count);
            } else if (!this._toRemove.delete(blob)) {
                this._logger.warn(`blob with 0 references was not found in toRemove: ${blob}`);
            }
        }
        for (const blob of this._toRemove) {
            this._logger.warn(`blob in toRemove was not found in checkpoint: ${blob}`);
        }
        this._toRemove.clear();
        this._checkpointId = undefined;
        this._checkpointBlobNames.clear();
        this._onContextChange.fire(this.getContext());
    }

    // Wait for the checkpoint queue to be empty.
    public async awaitEmptyQueue(): Promise<void> {
        await this._checkpointQueue.awaitEmpty(undefined, false);
    }

    // Uploads the working set to create a new checkpoint id.
    private async _checkpoint(context: Blobs, errorHandler: ErrorHandler): Promise<void> {
        const { checkpointId, addedBlobs, deletedBlobs } = context;
        this._logger.debug(`Begin checkpoint of working set into ${checkpointId}`);
        this._logger.debug(
            `add ${addedBlobs.length} blobs, remove ${deletedBlobs.length} blobs into ${checkpointId}`
        );

        let cpResponse: CheckpointBlobsResult = { newCheckpointId: "" };
        try {
            cpResponse = await this._apiServer.checkpointBlobs(context);
        } catch (error: any) {
            const msg = error instanceof Error ? error.message : `${error}`;
            const fromVersionId = this._checkpointId ? this._checkpointId : "{initial}";
            if (
                APIError.isAPIErrorWithStatus(error, APIStatus.invalidArgument) ||
                APIError.isAPIErrorWithStatus(error, APIStatus.unimplemented)
            ) {
                // The checkpoint ID is unknown, reset the checkpoint and start
                // over.
                this._logger.warn(
                    `checkpoint-blobs from ${fromVersionId} failed with invalid argument: ${msg}. Recreating checkpoint.`
                );
                this.resetCheckpoint();
                this._queueCheckpoint();
                errorHandler.throwError(error, false);
            } else {
                this._logger.error(`checkpoint-blobs failed with error: ${msg}.`);
                // Don't retry here in case this is an issue with our checkpoint
                // (eg, it is too large). We will call this again anyway when
                // updateBlob() gets called, which should be somewhat often.
                errorHandler.throwError(error, false);
            }
        }

        // It's possible that ongoing edits have added to the add/remove sets.
        if (checkpointId !== this._checkpointId) {
            // The current checkpointId has changed while we're waiting for the
            // new checkpoint to complete, most likely due to a reset. Abandon
            // the new checkpoint. Alternatively, we could track the flat list
            // of blobs within this new checkpoint and still use it, but the
            // situation is unlikely to arise in practice, and abandoning the
            // new checkpoint is simpler.
            this._logger.warn(
                `original checkpointId ${checkpointId} does not match current checkpointId ${this._checkpointId}. Abandoning new checkpoint.`
            );
        } else {
            this._logger.debug(
                `checkpointId ${checkpointId} advanced to ${cpResponse.newCheckpointId}`
            );

            this._checkpointId = cpResponse.newCheckpointId;
            // fixup the set
            for (const blob of addedBlobs) {
                let count = this._toAdd.get(blob);
                if (count === undefined) {
                    // Current reference count is "0"; we must include blob in the checkpoint, but it
                    // also needs to be in _toRemove so that our getContext() result refers to the
                    // same set of blobs before and after this function.
                    this._checkpointBlobNames.set(blob, 0);
                    this._toRemove.add(blob);
                } else {
                    this._checkpointBlobNames.set(blob, count);
                    this._toAdd.delete(blob);
                }
            }
            // Remove may not find the blob
            for (const blob of deletedBlobs) {
                let count = this._checkpointBlobNames.get(blob);
                if (count === undefined) {
                    this._logger.warn(
                        `In _checkpoint: deleted blob ${blob} not found in checkpoint`
                    );
                } else if (count > 0) {
                    // Current reference count is non-zero; must remove blob from the checkpoint, but
                    // it also needs to be in _toAdd so that our getContext() result refers to the
                    // same set of blobs before and after this function.
                    this._toAdd.set(blob, count);
                }
                // Unconditionally remove from both
                // checkpoint: the set of keys in checkpointBlobNames must match those referred to by checkpointId
                // toRemove: toRemove should be the subset of checkpoint for which reference count is 0
                this._checkpointBlobNames.delete(blob);
                this._toRemove.delete(blob);
            }
            this._onContextChange.fire(this.getContext());
        }

        // Kickoff new round of checkpointing if the working set is still larger than the threshold
        if (this._toAdd.size + this._toRemove.size >= this._checkpointThreshold) {
            this._logger.debug(
                `starting a new round of checkpointing due to size ${this._toAdd.size} + ${this._toRemove.size}`
            );
            this._queueCheckpoint();
        }
    }

    // Save the current working set to form a new version, and launch a checkpoint
    // operation.
    private _queueCheckpoint() {
        this._logger.debug(`queue checkpoint`);
        let toAdd = Array.from(this._toAdd.keys()).slice(0, this._maxCheckpointBatchSize);
        let toDelete = Array.from(this._toRemove).slice(0, this._maxCheckpointBatchSize);
        let context: Blobs = {
            checkpointId: this._checkpointId,
            addedBlobs: toAdd,
            deletedBlobs: toDelete,
        };

        this._logger.debug(
            `queue checkpoint: version: ${context.checkpointId}, add: ${context.addedBlobs.length} blobs, rm: ${context.deletedBlobs.length} blob`
        );
        this._checkpointQueue.add(context);
    }
}

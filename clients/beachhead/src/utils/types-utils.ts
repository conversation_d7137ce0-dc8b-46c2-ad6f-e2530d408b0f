/**
 *
 * This function provides a type guard to check if an object has a given property.
 *
 * <code
 * const objects:any[] = getData(...);
 *
 *
 * for (const obj from objects)
 *     if (!hasProperty(obj, "property")) {
 *        continue
 *     }
 *
 *     const property = obj.property; // typescript will not complain about this.
 * }
 * </code>
 *
 *
 * @param obj any type of object
 * @param property name of the property
 * @returns true if the object has the property, false otherwise.
 */
export function hasProperty<T extends PropertyKey>(
    obj: unknown,
    property: T
): obj is { [k in T]: unknown } {
    return obj != null && typeof obj === "object" && property in obj;
}

/**
 *
 * This function provides a type guard to check if a value is null or undefined.
 *
 * <code>
 *   value = string | undefined;
 *   if (!isNil(value)) {
 *       console.log(value.length); // typescript will know that `value` is defined and not null.
 *   }
 *
 * </code>
 *
 *
 * @param value
 * @returns
 */
export function isNil(value: any): value is null | undefined {
    return value === null || value === undefined;
}

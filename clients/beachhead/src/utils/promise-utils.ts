import { APIError } from "../exceptions";
import { type AugmentLogger } from "../logging";
import * as vscode from "../vscode";

export type BackoffParams = {
    initialMS: number;
    mult: number;
    maxMS: number;
    maxTries?: number;
    maxTotalMs?: number;
    canRetry?: (e: any) => boolean;
};

const defaultBackoffParams: BackoffParams = {
    initialMS: 100,
    mult: 2,
    maxMS: 30000,
};

/**
 * makePromise returns a triple consisting of a Promise<T> and its resolve and
 * reject callbacks.
 */
export function makePromise<T = void, E = Error>(): [
    Promise<T>,
    (value: T | PromiseLike<T>) => void,
    (e: E) => void,
] {
    let res: (value: T | PromiseLike<T>) => void;
    let rej: (e: E) => void;
    const promise = new Promise<T>((resolve, reject) => {
        res = resolve;
        rej = reject;
    });

    // These non-null assertions (res!, rej!) are a workaround for a spurious error:
    // "Variable 'res' is used before being assigned" (or 'rej').
    return [promise, res!, rej!];
}

export function promiseFromEvent<T>(event: vscode.Event<T>): Promise<T> {
    let disp: vscode.Disposable;
    return new Promise((resolve) => {
        disp = event((value) => {
            disp.dispose();
            resolve(value);
        });
    });
}

export function disposableDelayer(): [Promise<void>, vscode.Disposable] {
    const [promise, resolve, _] = makePromise<void>();
    const resume = new vscode.Disposable(() => {
        resolve();
    });
    return [promise, resume];
}

/**
 * delayMs returns a promise that resolves after the given number of ms.
 */
export function delayMs(delayMs: number): Promise<void> {
    if (delayMs === 0) {
        return Promise.resolve();
    }
    return new Promise((resolve) => {
        setTimeout(resolve, delayMs);
    });
}

/**
 * retryWithBackoff returns a promise that runs the given `fn`.
 * - If fn succeeds, the Promise resolves to its returned value.
 * - If fn fails with a non-retriable error, the Promise rejects with that error.
 * - If fn fails with a retriable error, the Promise will retry with backoff as specified
 *   by the given BackoffParams.
 */
export async function retryWithBackoff<T>(
    fn: () => Promise<T>,
    logger: AugmentLogger,
    backoffParams: BackoffParams = defaultBackoffParams
): Promise<T> {
    let backoffMs = 0;

    let startTime = backoffParams.maxTotalMs !== undefined ? Date.now() : undefined;
    const canRetryFn = backoffParams.canRetry
        ? backoffParams.canRetry
        : (e: any) => APIError.isRetriableAPIError(e);
    for (let tries = 0; ; tries++) {
        try {
            const ret = await fn();
            if (tries > 0) {
                logger.info(`Operation succeeded after ${tries} transient failures`);
            }
            return ret;
        } catch (e: any) {
            if (!canRetryFn(e)) {
                throw e;
            }

            // Check if we have exceeded the max number of retries.
            const currTryCount: number = tries + 1;
            if (backoffParams.maxTries !== undefined && currTryCount >= backoffParams.maxTries) {
                throw e;
            }

            // Compute the back-off we will use next time around.
            if (backoffMs === 0) {
                backoffMs = backoffParams.initialMS;
            } else {
                backoffMs = Math.min(backoffMs * backoffParams.mult, backoffParams.maxMS);
            }
            logger.info(
                `Operation failed with error ${e}, retrying in ${backoffMs} ms; retries = ${tries}`
            );

            // Check if the backoff delay will exceed total time. If it will, don't issue another request.
            if (
                backoffParams.maxTotalMs !== undefined &&
                startTime !== undefined &&
                Date.now() - startTime + backoffMs > backoffParams.maxTotalMs
            ) {
                throw e;
            }
            await delayMs(backoffMs);
        }
    }
}

/**
 * Execute an async function with a timeout
 *
 * node-fetch and native fetch both seem to have issue respecting timeouts.  This is to enforce it.
 */
export async function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  return await new Promise<T>((resolve, reject) => {
    const timeout = setTimeout(() => reject(new Error("Execution aborted due to timeout.")), timeoutMs);
    promise.finally(() => clearTimeout(timeout)).then(resolve, reject);
  });
}

/**
 * periodicYielder is a function that helps ensure that a long-running task doesn't block the
 * event loop. It returns an iterator that yields control to the event loop if it has been at
 * least `maxMs` milliseconds since the last yield.
 */
export async function* periodicYielder(maxMs: number): AsyncGenerator<void> {
    let lastYieldTime = Date.now();
    while (true) {
        const now = Date.now();
        if (now - lastYieldTime >= maxMs) {
            // Interestingly, setTimeout(0) appears to be faster than setImmediate(), by 15-25%.
            await new Promise((resolve) => setTimeout(resolve, 0));
            lastYieldTime = Date.now();
        }
        yield;
    }
}

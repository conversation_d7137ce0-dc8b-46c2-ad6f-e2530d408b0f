import { EventEmitter } from "node:events";

import { SSHConnectionFinder } from "../ssh-connections";

const fakeSsOutputWithoutSSH = ``;

const fakeSsOutputWithSSH = `ESTAB          0               0                        172.17.0.2:2222                   10.100.2.18:55254           users:(("sshd",pid=12884,fd=4),("sshd",pid=12880,fd=4))`;

describe("SSHConnectionFinder", () => {
    let sshConnectionFinder: SSHConnectionFinder;

    beforeEach(() => {
        sshConnectionFinder = new SSHConnectionFinder(2222, 0);
    });

    it("hasActiveSSHConnections parsing ss output", async () => {
        let hasActiveConnections = sshConnectionFinder.hasActiveSSHConnections();
        expect(hasActiveConnections).toBe(false);

        jest.spyOn(require("child_process"), "spawn").mockImplementation(() => {
            const mockStdout = new EventEmitter();
            const mockProcess = new EventEmitter();
            (mockProcess as any).stdout = mockStdout;

            setTimeout(() => {
                mockStdout.emit("data", fakeSsOutputWithSSH);
                mockProcess.emit("close", 0);
            }, 10);
            return mockProcess as any;
        });

        await sshConnectionFinder.runOneBackgroundCheck();

        hasActiveConnections = sshConnectionFinder.hasActiveSSHConnections();
        expect(hasActiveConnections).toBe(true);

        jest.spyOn(require("child_process"), "spawn").mockImplementation(() => {
            const mockStdout = new EventEmitter();
            const mockProcess = new EventEmitter();
            (mockProcess as any).stdout = mockStdout;

            setTimeout(() => {
                mockStdout.emit("data", fakeSsOutputWithoutSSH);
                mockProcess.emit("close", 0);
            }, 10);
            return mockProcess as any;
        });

        await sshConnectionFinder.runOneBackgroundCheck();
        hasActiveConnections = sshConnectionFinder.hasActiveSSHConnections();
        expect(hasActiveConnections).toBe(false);
    });
});

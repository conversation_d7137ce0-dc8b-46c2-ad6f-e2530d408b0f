import { APIServerImplWithErrorReporting as APIServerImpl } from "../augment-api";
import { RemoteWorkspaceSetupStepStatus } from "../remote-agent-manager/types";
import { InitCommands } from "../workspace-init";
import { BeachheadWorkspaceManagerImpl } from "../workspace-manager";
import { WorkspaceSetupStatusTracker } from "../workspace-setup-status-tracker";

// Mock the logger
jest.mock("../logging", () => ({
    getLogger: jest.fn().mockReturnValue({
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
    }),
}));

// Create a proper mock of BeachheadWorkspaceManagerImpl with getters
class MockWorkspaceManager {
    private _initial_sync_started = false;
    private _initial_sync_complete = false;
    private _indexing_logs = "";

    get initial_sync_started(): boolean {
        return this._initial_sync_started;
    }

    get initial_sync_complete(): boolean {
        return this._initial_sync_complete;
    }

    get indexing_logs(): string {
        return this._indexing_logs;
    }

    // Methods to set the private properties for testing
    setInitialSyncStarted(value: boolean): void {
        this._initial_sync_started = value;
    }

    setInitialSyncComplete(value: boolean): void {
        this._initial_sync_complete = value;
    }

    setIndexingLogs(value: string): void {
        this._indexing_logs = value;
    }
}

describe("WorkspaceSetupStatusTracker", () => {
    let mockWorkspaceManager: MockWorkspaceManager;
    let mockInitCommands: jest.Mocked<InitCommands>;
    let statusTracker: WorkspaceSetupStatusTracker;

    beforeEach(() => {
        // Create mock workspace manager
        mockWorkspaceManager = new MockWorkspaceManager();

        // Create mock init commands
        mockInitCommands = {
            workspace_setup_status: jest.fn().mockReturnValue({ steps: [] }),
            commands: jest.fn().mockReturnValue([]),
            skipped_due_to_persistence: jest.fn().mockReturnValue(false),
        } as unknown as jest.Mocked<InitCommands>;

        // Create the status tracker with mocks
        statusTracker = new WorkspaceSetupStatusTracker(
            mockWorkspaceManager as unknown as BeachheadWorkspaceManagerImpl,
            mockInitCommands
        );
    });

    beforeEach(() => {
        // Reset all mocks before each test
        jest.clearAllMocks();

        // Reset workspace manager state
        mockWorkspaceManager.setInitialSyncStarted(false);
        mockWorkspaceManager.setInitialSyncComplete(false);
        mockWorkspaceManager.setIndexingLogs("");

        // Reset init commands mocks
        mockInitCommands.workspace_setup_status.mockReturnValue({ steps: [] });
        mockInitCommands.commands.mockReturnValue([]);
        mockInitCommands.skipped_due_to_persistence.mockReturnValue(false);

        // Reset internal tracker state
        (statusTracker as any)._last_sent_logs_length = 0;
        (statusTracker as any)._indexing_logs_sequence_id = 0;
    });

    describe("report_status", () => {
        it("should return empty steps array when no init commands and no indexing", () => {
            // Create a tracker without init commands
            const trackerWithoutInitCommands = new WorkspaceSetupStatusTracker(
                mockWorkspaceManager as unknown as BeachheadWorkspaceManagerImpl
            );

            // Set up workspace manager state
            mockWorkspaceManager.setInitialSyncStarted(false);

            // Execute
            const result = trackerWithoutInitCommands.report_status();

            // Verify
            expect(result.steps).toEqual([]);
        });

        it("should include steps from init commands when available", () => {
            // Set up init commands to return steps
            const mockSteps = {
                steps: [
                    {
                        step_description: "git clone",
                        logs: "Cloning repository...",
                        status: RemoteWorkspaceSetupStepStatus.running,
                        sequence_id: 1,
                        step_number: 0,
                    },
                ],
            };
            mockInitCommands.workspace_setup_status.mockReturnValue(mockSteps);

            // Mock the commands array with a mock command that has sequence_id method
            mockInitCommands.commands.mockReturnValue([
                {
                    sequence_id: jest.fn().mockReturnValue(1),
                    increment_sequence_id: jest.fn(),
                    // Add other required properties to satisfy the Command type
                    name: jest.fn(),
                    status: jest.fn(),
                    output: jest.fn(),
                    last_sent_output_length: jest.fn(),
                    set_last_sent_output_length: jest.fn(),
                    run: jest.fn(),
                } as unknown as any,
            ]);

            // Execute
            const result = statusTracker.report_status();

            // Verify
            expect(result.steps.length).toBe(1);
            expect(result.steps[0].step_description).toBe("git clone");
            expect(result.steps[0].logs).toBe("Cloning repository...");
            expect(mockInitCommands.workspace_setup_status).toHaveBeenCalledTimes(1);
        });

        it("should split logs into chunks when they exceed MAX_LOG_CHARS", () => {
            // Create a long log string that exceeds the MAX_LOG_CHARS (1000)
            const longLog = "A".repeat(1500);

            // Set up init commands to return steps with long logs
            const mockSteps = {
                steps: [
                    {
                        step_description: "git clone",
                        logs: longLog,
                        status: RemoteWorkspaceSetupStepStatus.running,
                        sequence_id: 1,
                        step_number: 0,
                    },
                ],
            };
            mockInitCommands.workspace_setup_status.mockReturnValue(mockSteps);

            // Mock the commands array with a mock command that has sequence_id method
            mockInitCommands.commands.mockReturnValue([
                {
                    sequence_id: jest.fn().mockReturnValue(1),
                    increment_sequence_id: jest.fn(),
                    // Add other required properties to satisfy the Command type
                    name: jest.fn(),
                    status: jest.fn(),
                    output: jest.fn(),
                    last_sent_output_length: jest.fn(),
                    set_last_sent_output_length: jest.fn(),
                    run: jest.fn(),
                } as unknown as any,
            ]);

            // Execute
            const result = statusTracker.report_status();

            // Verify
            // Should have 2 steps: one for the first 1000 chars and one for the remaining 500
            expect(result.steps.length).toBe(2);

            // First step should have 1000 chars and be in running state
            expect(result.steps[0].logs.length).toBe(1000);
            expect(result.steps[0].logs).toBe("A".repeat(1000));
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.running);

            // Second step should have 500 chars and maintain the original status
            expect(result.steps[1].logs.length).toBe(500);
            expect(result.steps[1].logs).toBe("A".repeat(500));
            expect(result.steps[1].status).toBe(RemoteWorkspaceSetupStepStatus.running);

            // Verify sequence_id was called
            expect(mockInitCommands.commands()[0].sequence_id).toHaveBeenCalled();

            // Verify increment_sequence_id was called once
            expect(mockInitCommands.commands()[0].increment_sequence_id).toHaveBeenCalledTimes(1);
        });

        it("should properly manage sequence IDs when splitting logs into chunks", () => {
            // Create a long log string that exceeds the MAX_LOG_CHARS (1000)
            const longLog = "A".repeat(2500);

            // Set up init commands to return steps with long logs
            const mockSteps = {
                steps: [
                    {
                        step_description: "git clone",
                        logs: longLog,
                        status: RemoteWorkspaceSetupStepStatus.success,
                        sequence_id: 1,
                        step_number: 0,
                    },
                ],
            };
            mockInitCommands.workspace_setup_status.mockReturnValue(mockSteps);

            // Create a sequence ID counter to simulate the behavior
            let sequenceIdCounter = 1;

            // Mock the commands array with a mock command that has sequence_id method
            // that returns an incrementing value to simulate real behavior
            mockInitCommands.commands.mockReturnValue([
                {
                    sequence_id: jest.fn().mockImplementation(() => sequenceIdCounter),
                    increment_sequence_id: jest.fn().mockImplementation(() => {
                        sequenceIdCounter++;
                    }),
                    // Add other required properties to satisfy the Command type
                    name: jest.fn(),
                    status: jest.fn(),
                    output: jest.fn(),
                    last_sent_output_length: jest.fn(),
                    set_last_sent_output_length: jest.fn(),
                    run: jest.fn(),
                } as unknown as any,
            ]);

            // Execute
            const result = statusTracker.report_status();

            // Verify
            // Should have 3 steps: one for each 1000 chars chunk
            expect(result.steps.length).toBe(3);

            // First step should have sequence_id = 1 (initial value)
            expect(result.steps[0].sequence_id).toBe(1);
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.running);

            // Second step should have sequence_id = 2 (after increment)
            expect(result.steps[1].sequence_id).toBe(2);
            expect(result.steps[1].status).toBe(RemoteWorkspaceSetupStepStatus.running);

            // Third step should have sequence_id = 3
            // and should maintain the original status
            expect(result.steps[2].sequence_id).toBe(3);
            expect(result.steps[2].status).toBe(RemoteWorkspaceSetupStepStatus.success);

            // Verify sequence_id was called multiple times
            expect(mockInitCommands.commands()[0].sequence_id).toHaveBeenCalled();

            // Verify increment_sequence_id was called once (after the first chunk)
            expect(mockInitCommands.commands()[0].increment_sequence_id).toHaveBeenCalledTimes(2);
        });

        it("should not include indexing step when no new logs", () => {
            // Set up workspace manager state
            mockWorkspaceManager.setInitialSyncStarted(true);
            mockWorkspaceManager.setIndexingLogs("Initial logs");

            // Set up the tracker to have already sent these logs
            (statusTracker as any)._last_sent_logs_length = "Initial logs".length;

            // Execute
            const result = statusTracker.report_status();

            // Verify - should only have steps from init commands, not indexing
            expect(result.steps.length).toBe(0);
            expect((statusTracker as any)._indexing_logs_sequence_id).toBe(0); // Sequence ID not incremented
        });

        it("should include step with empty logs when only status changes", () => {
            // Set up init commands to return steps with empty logs but a status
            const mockSteps = {
                steps: [
                    {
                        step_description: "step 1",
                        logs: "", // Empty logs
                        status: RemoteWorkspaceSetupStepStatus.success, // Status is set
                        sequence_id: 1,
                        step_number: 0,
                    },
                ],
            };

            // Mock to return these steps
            mockInitCommands.workspace_setup_status.mockReturnValue(mockSteps);

            // Mock the commands array with sequence_id function
            mockInitCommands.commands.mockReturnValue([
                {
                    sequence_id: jest.fn().mockReturnValue(1),
                    increment_sequence_id: jest.fn(),
                    // Add other required properties to satisfy the Command type
                    name: jest.fn(),
                    status: jest.fn(),
                    output: jest.fn(),
                    last_sent_output_length: jest.fn(),
                    set_last_sent_output_length: jest.fn(),
                    run: jest.fn(),
                } as unknown as any,
            ]);

            // Execute
            const result = statusTracker.report_status();

            // Verify
            expect(result.steps.length).toBe(1);
            expect(result.steps[0].step_description).toBe("step 1");
            expect(result.steps[0].logs).toBe(""); // Empty logs should be preserved
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success); // Status should be preserved
            expect(result.steps[0].sequence_id).toBe(1);
        });

        it("should handle a mix of steps with empty and non-empty logs", () => {
            // Set up init commands to return multiple steps, some with empty logs
            const mockSteps = {
                steps: [
                    {
                        step_description: "step 1",
                        logs: "", // Empty logs
                        status: RemoteWorkspaceSetupStepStatus.success,
                        sequence_id: 1,
                        step_number: 0,
                    },
                    {
                        step_description: "step 2",
                        logs: "Some log content",
                        status: RemoteWorkspaceSetupStepStatus.running,
                        sequence_id: 2,
                        step_number: 1,
                    },
                    {
                        step_description: "step 2",
                        logs: "", // Empty logs
                        status: RemoteWorkspaceSetupStepStatus.success,
                        sequence_id: 3,
                        step_number: 2,
                    },
                ],
            };

            // Mock to return these steps
            mockInitCommands.workspace_setup_status.mockReturnValue(mockSteps);

            // Mock the commands array with sequence_id function for each step
            mockInitCommands.commands.mockReturnValue([
                {
                    sequence_id: jest.fn().mockReturnValue(1),
                    increment_sequence_id: jest.fn(),
                    // Add other required properties to satisfy the Command type
                    name: jest.fn(),
                    status: jest.fn(),
                    output: jest.fn(),
                    last_sent_output_length: jest.fn(),
                    set_last_sent_output_length: jest.fn(),
                    run: jest.fn(),
                } as unknown as any,
                {
                    sequence_id: jest.fn().mockReturnValue(2),
                    increment_sequence_id: jest.fn(),
                    // Add other required properties to satisfy the Command type
                    name: jest.fn(),
                    status: jest.fn(),
                    output: jest.fn(),
                    last_sent_output_length: jest.fn(),
                    set_last_sent_output_length: jest.fn(),
                    run: jest.fn(),
                } as unknown as any,
                {
                    sequence_id: jest.fn().mockReturnValue(3),
                    increment_sequence_id: jest.fn(),
                    // Add other required properties to satisfy the Command type
                    name: jest.fn(),
                    status: jest.fn(),
                    output: jest.fn(),
                    last_sent_output_length: jest.fn(),
                    set_last_sent_output_length: jest.fn(),
                    run: jest.fn(),
                } as unknown as any,
            ]);

            // Execute
            const result = statusTracker.report_status();

            // Verify
            expect(result.steps.length).toBe(3);

            // First step (empty logs)
            expect(result.steps[0].step_description).toBe("step 1");
            expect(result.steps[0].logs).toBe("");
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success);

            // Second step (with logs)
            expect(result.steps[1].step_description).toBe("step 2");
            expect(result.steps[1].logs).toBe("Some log content");
            expect(result.steps[1].status).toBe(RemoteWorkspaceSetupStepStatus.running);

            // Third step (empty logs)
            expect(result.steps[2].step_description).toBe("step 2");
            expect(result.steps[2].logs).toBe("");
            expect(result.steps[2].status).toBe(RemoteWorkspaceSetupStepStatus.success);
        });

        it("should include indexing step with new logs and increment sequence ID", () => {
            // Set up workspace manager state
            mockWorkspaceManager.setInitialSyncStarted(true);
            mockWorkspaceManager.setInitialSyncComplete(false);
            mockWorkspaceManager.setIndexingLogs("Initial logs\nNew logs added");

            // Set up the tracker to have only sent the initial logs
            (statusTracker as any)._last_sent_logs_length = "Initial logs".length;

            // Execute
            const result = statusTracker.report_status();

            // Verify
            expect(result.steps.length).toBe(1);
            expect(result.steps[0]).toEqual({
                step_description: "Indexing",
                logs: "\nNew logs added",
                status: RemoteWorkspaceSetupStepStatus.running,
                sequence_id: 1, // Incremented from 0
                step_number: 0, // No init commands, so step number is 0
            });

            // Verify sequence ID was incremented
            expect((statusTracker as any)._indexing_logs_sequence_id).toBe(1);

            // Verify last sent logs length was updated
            expect((statusTracker as any)._last_sent_logs_length).toBe(
                "Initial logs\nNew logs added".length
            );
        });

        it("should set indexing status to success when initial sync is complete", () => {
            // Set up workspace manager state
            mockWorkspaceManager.setInitialSyncStarted(true);
            mockWorkspaceManager.setInitialSyncComplete(true);
            mockWorkspaceManager.setIndexingLogs("Initial logs\nNew logs added");

            // Set up the tracker to have only sent the initial logs
            (statusTracker as any)._last_sent_logs_length = "Initial logs".length;

            // Execute
            const result = statusTracker.report_status();

            // Verify
            expect(result.steps.length).toBe(1);
            expect(result.steps[0].status).toBe(RemoteWorkspaceSetupStepStatus.success);
        });

        it("should use correct step number based on init commands length", () => {
            // Mock the commands array length without creating actual Command objects
            // We just need to test that the step_number is set correctly
            const mockCommandsLength = 3;
            mockInitCommands.commands.mockReturnValue(new Array(mockCommandsLength));

            // Set up workspace manager state
            mockWorkspaceManager.setInitialSyncStarted(true);
            mockWorkspaceManager.setIndexingLogs("Initial logs\nNew logs added");

            // Set up the tracker to have only sent the initial logs
            (statusTracker as any)._last_sent_logs_length = "Initial logs".length;

            // Execute
            const result = statusTracker.report_status();

            // Verify
            expect(result.steps.length).toBe(1);
            expect(result.steps[0].step_number).toBe(3); // Should be the length of commands array
        });

        it("should return empty steps when init commands are skipped due to persistence", () => {
            // Set up init commands to be skipped due to persistence
            mockInitCommands.skipped_due_to_persistence.mockReturnValue(true);

            // Set up workspace manager state - indexing should not be skipped in this test
            mockWorkspaceManager.setInitialSyncStarted(false); // No indexing started
            mockWorkspaceManager.setInitialSyncComplete(false); // Indexing not complete

            // Execute
            const result = statusTracker.report_status();

            // Verify - no steps should be reported
            expect(result.steps).toEqual([]);
            expect(mockInitCommands.workspace_setup_status).not.toHaveBeenCalled();
        });
    });

    describe("splitLogsIntoChunks", () => {
        it("should return the original log when it's shorter than MAX_LOG_CHARS", () => {
            const shortLog = "This is a short log";

            // Call the private method using type casting
            const chunks = (statusTracker as any).splitLogsIntoChunks(shortLog);

            // Verify
            expect(chunks.length).toBe(1);
            expect(chunks[0]).toBe(shortLog);
        });

        it("should split logs into chunks of MAX_LOG_CHARS", () => {
            // Create a log that's exactly 2.5 times MAX_LOG_CHARS (1000)
            const longLog = "A".repeat(2500);

            // Call the private method using type casting
            const chunks = (statusTracker as any).splitLogsIntoChunks(longLog);

            // Verify
            expect(chunks.length).toBe(3);
            expect(chunks[0].length).toBe(1000);
            expect(chunks[1].length).toBe(1000);
            expect(chunks[2].length).toBe(500);
            expect(chunks[0]).toBe("A".repeat(1000));
            expect(chunks[1]).toBe("A".repeat(1000));
            expect(chunks[2]).toBe("A".repeat(500));
        });

        it("should handle empty logs", () => {
            const emptyLog = "";

            // Call the private method using type casting
            const chunks = (statusTracker as any).splitLogsIntoChunks(emptyLog);

            // Verify
            expect(chunks.length).toBe(0);
        });

        it("should preserve the total log content when chunking", () => {
            // Create a log with mixed content
            const originalLog =
                "First part of the log".repeat(30) +
                "Middle section with some numbers 12345".repeat(10) +
                "Final part with special chars !@#$%^&*()".repeat(5);

            // Ensure the log is longer than MAX_LOG_CHARS
            expect(originalLog.length).toBeGreaterThan(1000);

            // Call the private method using type casting
            const chunks = (statusTracker as any).splitLogsIntoChunks(originalLog);

            // Verify we have multiple chunks
            expect(chunks.length).toBeGreaterThan(1);

            // Combine all chunks back together
            const recombinedLog = chunks.join("");

            // Verify the recombined log matches the original exactly
            expect(recombinedLog).toBe(originalLog);
            expect(recombinedLog.length).toBe(originalLog.length);
        });
    });

    describe("run_report_status_logs_loop", () => {
        let mockApiServer: jest.Mocked<APIServerImpl>;
        let clearIntervalSpy: jest.SpyInstance;
        let setIntervalSpy: jest.SpyInstance;
        let originalSetInterval: typeof global.setInterval;
        let originalClearInterval: typeof global.clearInterval;
        let intervalCallback: Function | null = null;
        let intervalId: NodeJS.Timeout;

        beforeEach(() => {
            // Mock the API server
            mockApiServer = {
                reportWorkspaceSetupLogs: jest.fn().mockResolvedValue(undefined),
            } as unknown as jest.Mocked<APIServerImpl>;

            // Save original timer functions
            originalSetInterval = global.setInterval;
            originalClearInterval = global.clearInterval;

            // Create a dummy interval ID
            intervalId = {} as NodeJS.Timeout;

            // Mock setInterval to capture the callback
            // @ts-expect-error - Ignoring TypeScript errors for test mocking
            global.setInterval = jest.fn((_callback: Function, _ms: number) => {
                intervalCallback = _callback;
                return intervalId;
            });

            // Mock clearInterval
            global.clearInterval = jest.fn((_id: any) => {
                if (_id === intervalId) {
                    intervalCallback = null;
                }
            });

            // Create spies
            clearIntervalSpy = jest.spyOn(global, "clearInterval");
            setIntervalSpy = jest.spyOn(global, "setInterval");
        });

        afterEach(() => {
            // Restore original timer functions
            global.setInterval = originalSetInterval;
            global.clearInterval = originalClearInterval;
            jest.clearAllMocks();
            intervalCallback = null;
        });

        it("should set up an interval that reports status", () => {
            // Set up workspace manager with logs to ensure the API is called
            mockWorkspaceManager.setInitialSyncStarted(true);
            mockWorkspaceManager.setIndexingLogs("Some indexing logs");

            // Execute
            statusTracker.run_report_status_logs_loop(mockApiServer, "test-agent-id");

            // Verify interval was set up
            expect(setIntervalSpy).toHaveBeenCalledTimes(1);
            expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 1000);

            // Manually trigger the interval callback
            if (intervalCallback) {
                intervalCallback();
            }

            // Verify the API was called
            expect(mockApiServer.reportWorkspaceSetupLogs).toHaveBeenCalledTimes(1);
            expect(mockApiServer.reportWorkspaceSetupLogs).toHaveBeenCalledWith(
                "test-agent-id",
                expect.any(Object)
            );
        });

        it("should report each step separately", () => {
            // Set up workspace manager with logs to ensure the API is called
            mockWorkspaceManager.setInitialSyncStarted(true);
            mockWorkspaceManager.setIndexingLogs("Some indexing logs");

            // Set up init commands to return multiple steps
            const mockSteps = {
                steps: [
                    {
                        step_description: "git clone",
                        logs: "Cloning repository...",
                        status: RemoteWorkspaceSetupStepStatus.running,
                        sequence_id: 1,
                        step_number: 0,
                    },
                    {
                        step_description: "git checkout",
                        logs: "Checking out branch...",
                        status: RemoteWorkspaceSetupStepStatus.running,
                        sequence_id: 2,
                        step_number: 1,
                    },
                ],
            };

            // Mock to return these steps
            jest.spyOn(statusTracker, "report_status").mockReturnValue(mockSteps);

            // Execute
            statusTracker.run_report_status_logs_loop(mockApiServer, "test-agent-id");

            // Manually trigger the interval callback
            if (intervalCallback) {
                intervalCallback();
            }

            // Verify the API was called twice, once for each step
            expect(mockApiServer.reportWorkspaceSetupLogs).toHaveBeenCalledTimes(2);

            // Verify first call was for the first step only
            expect(mockApiServer.reportWorkspaceSetupLogs).toHaveBeenNthCalledWith(
                1,
                "test-agent-id",
                { steps: [mockSteps.steps[0]] }
            );

            // Verify second call was for the second step only
            expect(mockApiServer.reportWorkspaceSetupLogs).toHaveBeenNthCalledWith(
                2,
                "test-agent-id",
                { steps: [mockSteps.steps[1]] }
            );
        });

        it("should log debug information for each step", () => {
            // Set up workspace manager with logs to ensure the API is called
            mockWorkspaceManager.setInitialSyncStarted(true);
            mockWorkspaceManager.setIndexingLogs("Some indexing logs");

            // Set up init commands to return a step
            const mockSteps = {
                steps: [
                    {
                        step_description: "git clone",
                        logs: "Cloning repository...",
                        status: RemoteWorkspaceSetupStepStatus.running,
                        sequence_id: 1,
                        step_number: 0,
                    },
                ],
            };

            // Mock to return these steps
            jest.spyOn(statusTracker, "report_status").mockReturnValue(mockSteps);

            // Get the logger mock
            const loggerMock = (statusTracker as any)._logger;

            // Execute
            statusTracker.run_report_status_logs_loop(mockApiServer, "test-agent-id");

            // Manually trigger the interval callback
            if (intervalCallback) {
                intervalCallback();
            }

            // Verify info log was called with the number of steps
            expect(loggerMock.info).toHaveBeenCalledWith(
                expect.stringContaining("Reporting 1 log steps for agent test-agent-id")
            );

            // Verify debug log was called with step details
            expect(loggerMock.debug).toHaveBeenCalledWith(
                expect.stringContaining("Reporting step 0 with sequence ID 1")
            );
        });

        it("should clear the interval when initial sync is complete", () => {
            // Execute
            statusTracker.run_report_status_logs_loop(mockApiServer, "test-agent-id");

            // First interval tick - sync not complete
            if (intervalCallback) {
                intervalCallback();
            }
            expect(clearIntervalSpy).not.toHaveBeenCalled();

            // Set sync to complete
            mockWorkspaceManager.setInitialSyncComplete(true);

            // Second interval tick - sync complete
            if (intervalCallback) {
                intervalCallback();
            }

            // Verify interval was cleared
            expect(clearIntervalSpy).toHaveBeenCalledTimes(1);
        });
    });
});

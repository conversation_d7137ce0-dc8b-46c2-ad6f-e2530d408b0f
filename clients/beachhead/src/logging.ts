import { type AugmentLogger, setLibraryLogger } from "@augment-internal/sidecar-libs/src/logging";
import { createLogger, format, Logger, transports } from "winston";

export { type AugmentLogger } from "@augment-internal/sidecar-libs/src/logging";

let logger: Logger | undefined;

function singletonLogger(): Logger {
    if (logger) {
        return logger;
    }

    logger = createLogger({
        level: "info",
        exitOnError: false,
        format: format.combine(
            format.timestamp({
                format: "YYYY-MM-DD HH:mm:ss.SSS",
            }),
            format.splat(),
            format.printf(
                (info) =>
                    `${String(info.timestamp)} [${String(info.level)}] '${String(info.prefix)}': ${String(info.message)}`
            )
        ),
        transports: new transports.Console(),
    });
    setLibraryLogger(logger);
    return logger;
}

export function getLogger(prefix: string): AugmentLogger {
    return singletonLogger().child({ prefix });
}

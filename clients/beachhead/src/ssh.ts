import fs from "fs/promises";
import os from "os";
import path from "path";
import { spawn } from "child_process";

const _KEY_TYPES = ['ed25519', 'ecdsa', 'rsa'];

export interface SSHConfigOption {
    key: string;
    value: string;
}

export interface SSHConfig {
    public_keys: string[];
    ssh_config_options: SSHConfigOption[];
}

export class SSHManager {
    private _keysDir: string;

    constructor(keysDir: string) {
        this._keysDir = keysDir;
    }

    public async appendAuthorizedKeys(lines: string[]): Promise<void> {
        const homeDir = os.homedir();
        const sshDir = path.join(homeDir, ".ssh");
        const authKeysPath = path.join(sshDir, "authorized_keys");

        /// mkdir -p ~/.ssh

        await fs.mkdir(sshDir, { recursive: true, mode: 0o700 });

        /// read existing keys, if any

        let authorized_keys = "";
        try {
            authorized_keys = await fs.readFile(authKeysPath, "utf8");
        } catch (err) {
            if ((err as { code?: string }).code !== "ENOENT") {
                throw err;
            }
            // File doesn't exist, will create it below
        }

        if (authorized_keys.length > 0 && !authorized_keys.endsWith("\n")) {
            authorized_keys += "\n";
        }

        /// merge keys

        const curLines = authorized_keys.split("\n");
        for (const line of lines) {
            if (curLines.includes(line)) continue;
            curLines.push(line);
            authorized_keys += line + "\n";
        }

        /// write keys

        await fs.writeFile(authKeysPath, authorized_keys, { mode: 0o600 });
    }

    public async authorizedKeys(): Promise<string[]> {
        const authKeysPath = path.join(os.homedir(), ".ssh", "authorized_keys");
        let authorized_keys = "";
        try {
            authorized_keys = await fs.readFile(authKeysPath, "utf8");
        } catch (err) {
            if ((err as { code?: string }).code !== "ENOENT") {
                throw err;
            }
        }
        return authorized_keys.split("\n").filter(Boolean);
    }

    public async sshConfig(): Promise<SSHConfig> {
        let cfg = {
            public_keys: await this.authorizedKeys(),
            ssh_config_options: [
                {
                    // NOTE(mattm): Consider playing tricks with PAM to accept any username.
                    key: "User",
                    value: os.userInfo().username,
                },
            ],
        };
        const hkeys = await this.hostKeys();
        const hcmd = this.knownHostsCommand(hkeys);
        if (hcmd) {
            cfg.ssh_config_options.push(
                {
                    key: 'KnownHostsCommand',
                    value: hcmd,
                },
                {
                    key: 'StrictHostKeyChecking',
                    value: 'yes',
                },
                {
                    key: 'UserKnownHostsFile',
                    value: '/dev/null',
                },
                {
                    key: 'IgnoreUnknown',
                    value: 'X-Augment-*',
                },
                ...hkeys.map(hkey => ({
                    key: 'X-Augment-HostKey',
                    value: hkey,
                }))
            );
        }
        return cfg;
    }

    private knownHostsCommand(keys: string[]): string {
        // This command will print a multi-line known_hosts format to use with an SSH KnownHostsCommand. There will be
        // exactly one line per host key. The `%H` token is document in `ssh_config(5)` as "the known_hosts hostname or
        // address being searched for". The `%%s` will be left as `%s` for printf to fill out from its arguments; this
        // takes advantage of printf's behavior of reusing the format string to consume all arguments.
        const printf = ['/usr/bin/printf', `'%H %%s\\n'`];
        const quotedKeys = keys.map(key => `'${key}'`);
        // Don't return anything when no keys are found.
        if (keys.length === 0) {
            return '';
        }
        return [...printf, ...quotedKeys].join(' ');
    }

    // Lines of the form "<type> <key>". Any key comment is not included. Only look for `_KEY_TYPES` and
    // ignore any that are missing.
    private async hostKeys(): Promise<string[]> {
        const keysDir = path.join(this._keysDir, "etc", "ssh");
        const results: string[] = [];

        for (const keyType of _KEY_TYPES) {
            const keyPath = path.join(keysDir, `ssh_host_${keyType}_key`);

            try {
                await fs.stat(keyPath);

                // Use ssh-keygen to extract the public key from the private key. This provides
                // proper protections pertaining to public and private prime parts.
                const sshKeygen = spawn('ssh-keygen', ['-f', keyPath, '-y'], {
                    stdio: ['ignore', 'pipe', 'inherit']
                });
                sshKeygen.on("error", (err: Error) => {
                    throw new Error(`Failed to start process: ${err.message}`);
                });

                // Check exit code
                sshKeygen.on('close', (code: number) => {
                    if (code !== 0) {
                        throw new Error(`ssh-keygen exited with code ${code}`);
                    }
                });

                // Read stdout
                let publicKey = '';
                for await (const chunk of sshKeygen.stdout) {
                    publicKey += chunk.toString();
                }

                // Get only the first two fields (type and key, without comment)
                const keyParts = publicKey.trim().split(' ');
                if (keyParts.length >= 2) {
                    results.push(`${keyParts[0]} ${keyParts[1]}`);
                }

            } catch (err) {
                if ((err as { code?: string }).code === "ENOENT") {
                    // File not found, continue to next key type
                    continue;
                }
                // Other IO errors should pass through
                throw err;
            }
        }

        return results;
    }
}

import * as fs from "fs";
import * as path from "path";

import { getLogger } from "../logging";
import { makeDirsSync, readFileUtf8, writeFileUtf8 } from "../utils/fs-utils";
import { AgentState } from "./state";

interface SerializedState {
    // Original AgentState data (serialized)
    agentState: string;
    // Metadata for easier querying without parsing the full state
    timestamp: string;
}

/**
 * StatePersistence handles persisting AgentState objects to files.
 * It provides methods to save and load agent state.
 */
export class StatePersistence {
    private logger = getLogger("StatePersistence");
    private directoryPath: string;

    /**
     * Creates a new StatePersistence instance.
     *
     * @param directoryPath The directory path where state files will be stored
     */
    constructor(directoryPath: string) {
        this.directoryPath = directoryPath;
        this.ensureDirectoryExists();
    }

    /**
     * Saves an AgentState to a file.
     * This is an async operation that returns a promise.
     *
     * @param state The readonly AgentState to persist
     * @returns A promise that resolves when the state is saved, or rejects if there's an error
     */
    async save(state: Readonly<AgentState>): Promise<void> {
        const timestamp = new Date();
        return this.writeStateToFile(state, timestamp);
    }

    /**
     * Gets the path to the state file.
     *
     * @returns The path to the state file
     */
    private getStateFilePath(): string {
        return path.join(this.directoryPath, "agent_state.json");
    }

    /**
     * Loads and deserializes the most recent state.
     *
     * @returns A promise that resolves with a Result containing either the deserialized AgentState or an error
     */
    async loadLatestAgentState(): Promise<AgentState> {
        try {
            const filePath = this.getStateFilePath();
            let serializedState: SerializedState;
            const fileContent = await readFileUtf8(filePath);
            serializedState = JSON.parse(fileContent) as SerializedState;
            const agentState = this.deserializeAgentState(serializedState.agentState);
            return agentState;
        } catch (error) {
            this.logger.error(`Unexpected error loading agent state: ${String(error)}`);
            throw new Error(`Unexpected error loading agent state: ${String(error)}`);
        }
    }

    /**
     * Checks if a saved state exists.
     * This is useful for startup checks without loading the entire state.
     *
     * @returns A promise that resolves to true if a saved state exists, false otherwise
     */
    async checkSavedStateExists(): Promise<boolean> {
        try {
            // We can use a simpler approach by just checking if the file exists
            const filePath = this.getStateFilePath();
            try {
                await fs.promises.access(filePath, fs.constants.F_OK);
                return true;
            } catch {
                // File doesn't exist
                return false;
            }
        } catch (error) {
            this.logger.error(`Unexpected error checking if saved state exists: ${String(error)}`);
            return false;
        }
    }

    /**
     * Deserializes a JSON string back into an AgentState object.
     *
     * @param serializedState The serialized state string
     * @returns The deserialized AgentState
     */
    private deserializeAgentState(serializedState: string): AgentState {
        try {
            // Use the static fromJson method for type-safe deserialization
            return AgentState.fromJson(serializedState);
        } catch (error) {
            this.logger.error(`Failed to deserialize agent state: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Ensures the directory for state files exists.
     */
    private ensureDirectoryExists(): void {
        try {
            makeDirsSync(this.directoryPath);
        } catch (error) {
            this.logger.error(`Failed to create directory ${this.directoryPath}: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Writes an AgentState to a file.
     *
     * @param state The readonly AgentState to write
     * @param timestamp The readonly timestamp to include in the state
     */
    private async writeStateToFile(
        state: Readonly<AgentState>,
        timestamp: Readonly<Date>
    ): Promise<void> {
        try {
            const filePath = this.getStateFilePath();
            const serializedState = this.serializeState(state, timestamp);

            await writeFileUtf8(filePath, serializedState);
            this.logger.debug(`State written to ${filePath}`);
        } catch (error) {
            this.logger.error(`Failed to write state to file: ${String(error)}`);
            throw error;
        }
    }

    /**
     * Serializes an AgentState to a JSON string.
     * Stores the entire state object as a JSON string, along with metadata for easier querying.
     *
     * @param state The readonly AgentState to serialize
     * @param timestamp The readonly timestamp to include in the serialized state
     * @returns A JSON string representation of the state
     */
    private serializeState(state: Readonly<AgentState>, timestamp: Readonly<Date>): string {
        try {
            const serializedAgentState = JSON.stringify(state);

            const stateForStorage: SerializedState = {
                agentState: serializedAgentState,
                timestamp: timestamp.toISOString(),
            };

            return JSON.stringify(stateForStorage, null, 2);
        } catch (error) {
            this.logger.error(`Failed to serialize state: ${String(error)}`);
            throw error;
        }
    }
}

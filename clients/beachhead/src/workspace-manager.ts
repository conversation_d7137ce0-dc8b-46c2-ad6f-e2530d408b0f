import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { DisposableService } from "@augment-internal/sidecar-libs/src/lifecycle/disposable-service";
import * as fs from "fs";
import * as path from "path";

import { APIServer, Blobs } from "./augment-api";
import { getErrmsg } from "./exceptions";
import { FeatureFlagManager, FeatureFlags } from "./feature-flags";
import { newFileReader } from "./file-reader";
import { getLogger } from "./logging";
import { ChangedFile, FileChangeType } from "./remote-agent-manager/types";
import { IgnoreSourceBuiltin, IgnoreSourceFile, IgnoreStackBuilder } from "./utils/ignore-file";
import { PathAcceptance } from "./utils/path-acceptance";
import { makePathFilter, PathIterator } from "./utils/path-iterator";
import { FullPathFilter } from "./utils/path-iterator";
import { findFileName, normalizePathName, pathNameSansSep } from "./utils/path-utils";
import { FileType } from "./utils/types";
import { uriToAbsPath } from "./utils/uri";
import { findVCS } from "./vcs/vcs-finder";
import * as vscode from "./vscode";
import { BlobsCheckpointManager } from "./workspace/blobs-checkpoint-manager";
import { DiskFileManager } from "./workspace/disk-file-manager";
import { FilesystemChangeTracker } from "./workspace/filesystem-change-tracker";
import { PathHandlerImpl } from "./workspace/path-handler";
import { PathMap } from "./workspace/path-map";
import { PathHandler, PathType } from "./workspace/types";

// From vscode's workspace-manager.ts
function uriToAbsDirName(uri: vscode.Uri): string {
    return pathNameSansSep(uriToAbsPath(uri));
}

// From vscode's workspace-manager.ts
async function findRepoRoot(folderRoot: string): Promise<[string, boolean]> {
    let repoRootUri: vscode.Uri | undefined;

    repoRootUri = await findFileName(folderRoot, ".augmentroot");
    if (repoRootUri === undefined) {
        repoRootUri = (await findVCS(folderRoot))?.root;
    }
    if (repoRootUri !== undefined) {
        return [uriToAbsDirName(repoRootUri), true];
    } else {
        return [folderRoot, false];
    }
}

export interface BeachheadWorkspaceManager {
    workspaceRoot: string;
    getCheckpoint(): Blobs | undefined;

    getCurrentTimestamp(): number;
    getChangesSince(timestamp: number): Promise<ChangedFile[]>;

    onFileDeleted: vscode.Event<FileDeletedEvent>;
    onFileDidMove: vscode.Event<FileDidMoveEvent>;
    update(): Promise<void>;
}

export class BeachheadWorkspaceManagerImpl
    extends DisposableService
    implements BeachheadWorkspaceManager
{
    private _pathMap: PathMap;
    private _pathHandler: PathHandler;
    private _diskFileManager: DiskFileManager;
    private _blobsCheckpointManager: BlobsCheckpointManager;
    private _featureFlagManager: FeatureFlagManager;
    private _folderId: number | undefined;
    private _maxUploadSizeBytes: number;

    // Filesystem change tracking
    private _filesystemChangeTracker: FilesystemChangeTracker;

    private _logger = getLogger("WorkspaceManager");

    private _initialSyncComplete = false;
    private _intialSyncStarted = false;

    private _indexingLogs = "";

    private readonly _fileDeletedEmitter = this.addDisposable(
        new vscode.EventEmitter<FileDeletedEvent>()
    );
    private readonly _fileDidMoveEmitter = this.addDisposable(
        new vscode.EventEmitter<FileDidMoveEvent>()
    );

    constructor(
        private _workspaceRoot: string,
        private _apiServer: APIServer,
        featureFlags: FeatureFlags
    ) {
        super();

        this._maxUploadSizeBytes = featureFlags.maxUploadSizeBytes;
        this._pathMap = new PathMap();
        this._pathHandler = new PathHandlerImpl(this._maxUploadSizeBytes, newFileReader());
        this._featureFlagManager = new FeatureFlagManager({ initialFlags: featureFlags });
        this._diskFileManager = new DiskFileManager(
            "beachhead",
            this._apiServer,
            this._pathHandler,
            this._pathMap
        );
        this._blobsCheckpointManager = new BlobsCheckpointManager(
            this._apiServer,
            this._featureFlagManager,
            this._pathMap.onDidChangeBlobName
        );

        // Initialize filesystem change tracker (will be properly set up during initialize())
        // We create a temporary instance here to satisfy TypeScript, but it will be replaced
        // during the initialize() method with proper path filter
        const tempPathFilter = new FullPathFilter(
            new Map(), // Empty ignore path map
            undefined // No file extensions filter
        );
        this._filesystemChangeTracker = new FilesystemChangeTracker(
            this._workspaceRoot,
            tempPathFilter,
            this._maxUploadSizeBytes
        );

        this.addDisposables(
            this._pathMap,
            this._featureFlagManager,
            this._diskFileManager,
            this._blobsCheckpointManager
        );
    }

    get workspaceRoot(): string {
        return this._workspaceRoot;
    }

    public async initialize(): Promise<void> {
        this._intialSyncStarted = true;
        const repoRoot = (await findRepoRoot(this.workspaceRoot))[0];
        this._logger.info(`Opened source folder ${this.workspaceRoot} with repo root ${repoRoot}`);
        this._indexingLogs += `[${new Date().toISOString()}] Opened source folder ${this.workspaceRoot} with repo root ${repoRoot}\n`;
        this._folderId = this._pathMap.openSourceFolder(this.workspaceRoot, repoRoot);

        // Initialize filesystem change tracker BEFORE scanning
        await this._initializeChangeTracker();

        // Create path iterator and enumerate paths
        const pathIterator = await this._createPathIterator(repoRoot);
        this._indexingLogs += `[${new Date().toISOString()}] Starting to index files`;
        for await (const [_fileUri, relPath, fileType, acceptance] of pathIterator) {
            // TODO: this can be addPath() or something like that
            this._pathMap.insert(this._folderId, relPath, fileType, acceptance);
            const normalizedPath = normalizePathName(relPath);
            const absPath = path.join(this.workspaceRoot, normalizedPath);
            if (this._shouldAccept(absPath, fileType, acceptance)) {
                this._diskFileManager.ingestPath(this._folderId, normalizedPath);

                // Update the path map with the current mtime
                try {
                    const stats = await fs.promises.stat(absPath);
                    const currentMtime = stats.mtimeMs;
                    await this._updatePathMapForFile(
                        normalizedPath,
                        absPath,
                        currentMtime,
                        fileType,
                        acceptance
                    );
                } catch (error) {
                    // TODO(guy): fail the sync if we see too many errors
                    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                    this._logger.error(`Error updating path map for ${normalizedPath}: ${error}`);
                    this._indexingLogs += `[${new Date().toISOString()}] Error updating ${normalizedPath}: ${error instanceof Error ? error.message : String(error)}\n`;
                }
            }
        }

        await this._diskFileManager.awaitQuiesced();

        this._initialSyncComplete = true;
        this._logger.info(`Finished initial scan`);
        this._indexingLogs += `[${new Date().toISOString()}] Finished initial scan\n`;
    }

    get indexing_logs(): string {
        return this._indexingLogs;
    }

    get initial_sync_complete(): boolean {
        return this._initialSyncComplete;
    }

    get initial_sync_started(): boolean {
        return this._intialSyncStarted;
    }

    public getCheckpoint(): Blobs | undefined {
        if (!this._initialSyncComplete) {
            return undefined;
        }
        return this._blobsCheckpointManager.getContext();
    }

    public get onFileDeleted(): vscode.Event<FileDeletedEvent> {
        return this._fileDeletedEmitter.event;
    }

    public get onFileDidMove(): vscode.Event<FileDidMoveEvent> {
        return this._fileDidMoveEmitter.event;
    }

    /**
     * Initialize the filesystem change tracker
     */
    private async _initializeChangeTracker(): Promise<void> {
        try {
            // Get the repository root
            const repoRoot = (await findRepoRoot(this.workspaceRoot))[0];

            // Create path filter (same as used in path iterator)
            const ignoreStackBuilder = new IgnoreStackBuilder([
                new IgnoreSourceFile(".gitignore"),
                new IgnoreSourceBuiltin(this.workspaceRoot),
                new IgnoreSourceFile(".augmentignore"),
            ]);
            const pathFilter = await makePathFilter(
                vscode.Uri.file(this.workspaceRoot),
                vscode.Uri.file(repoRoot),
                ignoreStackBuilder,
                undefined
            );

            // Dispose the temporary instance and create the real one with proper path filter
            this._filesystemChangeTracker.dispose();
            this._filesystemChangeTracker = new FilesystemChangeTracker(
                this.workspaceRoot,
                pathFilter,
                this._maxUploadSizeBytes
            );

            this._logger.info("Filesystem change tracker initialized");
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this._logger.error(`Failed to initialize filesystem change tracker: ${errorMessage}`);
            // Continue without filesystem change tracking
        }
    }

    /**
     * Creates a path iterator for the workspace
     * @param repoRoot The repository root path
     * @returns A path iterator for the workspace
     */
    private async _createPathIterator(repoRoot: string): Promise<PathIterator> {
        const folderName = path.basename(this.workspaceRoot);

        // Create path filter
        const ignoreStackBuilder = new IgnoreStackBuilder([
            new IgnoreSourceFile(".gitignore"),
            new IgnoreSourceBuiltin(this.workspaceRoot),
            new IgnoreSourceFile(".augmentignore"),
        ]);
        const pathFilter = await makePathFilter(
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(repoRoot),
            ignoreStackBuilder,
            undefined
        );

        // Create path iterator
        return new PathIterator(
            folderName,
            vscode.Uri.file(this.workspaceRoot),
            vscode.Uri.file(repoRoot),
            pathFilter
        );
    }

    /**
     * Updates the path map for a file that has been ingested
     * @param normalizedPath The normalized path of the file
     * @param absPath The absolute path of the file
     * @param currentMtime The current mtime of the file
     * @param fileType The file type
     * @param pathAcceptance The path acceptance object
     */
    private async _updatePathMapForFile(
        normalizedPath: string,
        absPath: string,
        currentMtime: number,
        fileType: FileType,
        pathAcceptance: PathAcceptance
    ): Promise<void> {
        if (this._folderId === undefined) {
            this._logger.error(`No folder ID available for ${this.workspaceRoot}`);
            return;
        }

        // We need to generate a new content sequence number (use monotonic time)
        const contentSeq = this.getCurrentTimestamp();

        // Read the file content to calculate the blob name
        try {
            const fileContent = await fs.promises.readFile(absPath);
            const blobName = this._pathHandler.calculateBlobName(normalizedPath, fileContent);

            if (blobName) {
                // First, check if the file is already in the path map
                // This is necessary because update() only works on files that are already in the map
                if (
                    !this._pathMap.hasFile(this._folderId, normalizedPath) &&
                    this._shouldAccept(absPath, fileType, pathAcceptance)
                ) {
                    this._pathMap.insert(
                        this._folderId,
                        normalizedPath,
                        FileType.file,
                        pathAcceptance
                    );
                }

                // Then update the path map with the current mtime and blob name
                this._pathMap.update(
                    this._folderId,
                    normalizedPath,
                    contentSeq,
                    blobName,
                    currentMtime
                );
            } else {
                this._logger.error(`Failed to calculate blob name for ${normalizedPath}`);
            }
        } catch (readError) {
            // this._logger.error(
            //     `Error reading file ${normalizedPath} to update path map: ${readError} pathAcceptance=${pathAcceptance.format()}`
            // );
            this._logger.error(`Error reading file ${normalizedPath} to update path map`);
        }
    }

    private _shouldAccept(
        absPath: string,
        fileType: FileType,
        acceptance: PathAcceptance
    ): boolean {
        const pathInfo = this._pathHandler.classifyPath(absPath);
        return (
            fileType === FileType.file && acceptance.accepted && pathInfo.type === PathType.accepted
        );
    }

    /**
     * Get the current timestamp for change tracking
     * @returns Current timestamp that can be used with getChangesSince()
     */
    public getCurrentTimestamp(): number {
        // Change tracker is always available after initialization
        return this._filesystemChangeTracker.getCurrentTimestamp();
    }

    /**
     * Get changes since a specific timestamp
     * @param timestamp The timestamp to get changes since
     * @returns An array of changed files since the timestamp
     */
    public async getChangesSince(timestamp: number): Promise<ChangedFile[]> {
        const startTime = this.getCurrentTimestamp();

        const fileChanges = await this._filesystemChangeTracker.getChangesSinceTimestamp(timestamp);

        // If no changes found, return empty array
        if (fileChanges.length === 0) {
            this._logger.info("No change history available for timestamp, returning empty changes");
            return [];
        }

        const changedFiles = fileChanges.map((change) => {
            // Map change types
            let changeType: FileChangeType;
            switch (change.changeType) {
                case "create":
                    changeType = FileChangeType.added;
                    break;
                case "modify":
                    changeType = FileChangeType.modified;
                    break;
                case "delete":
                    changeType = FileChangeType.deleted;
                    break;
                default:
                    throw new Error(`Unknown change type: ${change.changeType as string}`);
            }

            // Convert Buffer content to string for backward compatibility
            const oldContents = change.beforeContent ? change.beforeContent.toString("utf8") : "";
            const newContents = change.afterContent ? change.afterContent.toString("utf8") : "";

            return {
                old_path: change.changeType === "create" ? "" : change.path,
                new_path: change.changeType === "delete" ? "" : change.path,
                old_contents: oldContents,
                new_contents: newContents,
                change_type: changeType,
            };
        });

        this._logger.info(
            `Change detection completed in ${this.getCurrentTimestamp() - startTime} ms`
        );
        return changedFiles;
    }

    public async update(): Promise<void> {
        if (!this._initialSyncComplete) {
            this._logger.warn("Cannot update workspace before initial sync is complete");
            return;
        }

        const repoRoot = (await findRepoRoot(this.workspaceRoot))[0];
        this._logger.info(`Updating workspace ${this.workspaceRoot} with repo root ${repoRoot}`);

        // Check if we have a valid folder ID
        if (this._folderId === undefined) {
            this._logger.error(`No folder ID available for ${this.workspaceRoot}`);
            return;
        }

        // Create path iterator and enumerate paths
        const pathIterator = await this._createPathIterator(repoRoot);

        // Scan all files and check for changes using mtimes
        for await (const [_fileUri, relPath, fileType, acceptance] of pathIterator) {
            const normalizedPath = normalizePathName(relPath);
            const absPath = path.join(this.workspaceRoot, normalizedPath);

            if (this._shouldAccept(absPath, fileType, acceptance)) {
                try {
                    // Get current mtime
                    const stats = await fs.promises.stat(absPath);
                    const currentMtime = stats.mtimeMs;

                    // Check if file has changed by comparing mtimes
                    const blobInfo = this._pathMap.getBlobInfo(
                        this._folderId,
                        normalizedPath,
                        currentMtime
                    );

                    if (blobInfo === undefined) {
                        // File is new or modified, ingest it
                        this._logger.info(
                            `File changed or new: ${normalizedPath} mtime=${currentMtime}`
                        );
                        this._diskFileManager.ingestPath(this._folderId, normalizedPath);

                        // Update the path map with the current mtime to avoid detecting the file as changed again
                        await this._updatePathMapForFile(
                            normalizedPath,
                            absPath,
                            currentMtime,
                            fileType,
                            acceptance
                        );
                    }
                } catch (error) {
                    this._logger.error(
                        `Error checking file ${normalizedPath}: ${getErrmsg(error)}`
                    );
                }
            }
        }

        // Note: We do not wait for indexing to complete here. This means we have an upload race,
        // where the agent may request a file before it has been indexed.
        this._logger.info("Workspace update complete");
    }

    /**
     * Dispose of resources including the filesystem change tracker
     */
    public dispose(): void {
        this._filesystemChangeTracker.dispose();
        super.dispose();
    }
}

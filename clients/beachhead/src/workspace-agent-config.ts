import { AgentConfig, jAgentConfig } from "./agent-config";
import { getLogger } from "./logging";
import { readFileUtf8 } from "./utils/fs-utils";

/**
 * Interface representing the JSON structure of a WorkspaceAgentConfig
 */
interface jWorkspaceAgentConfig {
    remote_agent_id: string;
    config: jAgentConfig; // This will be parsed into an AgentConfig
    experiments: Record<string, string>; // "instruction flags" parsed from _flags:{...} on the server
}

/**
 * WorkspaceAgentConfig wraps both a remote agent ID and its configuration
 * This is used when the beachhead is started with a workspace-agent-config file
 */
export class WorkspaceAgentConfig {
    private _remoteAgentId: string;
    private _config: AgentConfig;
    private _experiments: Record<string, string>;

    constructor(remoteAgentId: string, config: AgentConfig, exp: Record<string, string>) {
        this._remoteAgentId = remoteAgentId;
        this._config = config;
        this._experiments = exp;
    }

    /**
     * Get the remote agent ID
     */
    public remoteAgentId(): string {
        return this._remoteAgentId;
    }

    /**
     * Get the agent configuration
     */
    public config(): AgentConfig {
        return this._config;
    }

    /**
     * Get experiments which are flags parsed from the instructions on the server
     * in the form of _flags:{key[=value],...}.
     */
    public experiments(): Record<string, string> {
        return this._experiments;
    }

    /**
     * Create a WorkspaceAgentConfig from a file path
     * @param configPath Path to the workspace agent config file
     */
    public static async fromFile(configPath: string): Promise<WorkspaceAgentConfig> {
        const logger = getLogger("Beachhead");
        try {
            const buf = await readFileUtf8(configPath);
            const json = JSON.parse(buf) as jWorkspaceAgentConfig;

            if (!json.remote_agent_id) {
                throw new Error("Missing remote_agent_id in workspace agent config");
            }

            const agentConfig = new AgentConfig(json.config);
            return new WorkspaceAgentConfig(json.remote_agent_id, agentConfig, json.experiments);
        } catch (error) {
            logger.error("Failed to read workspace agent config file: %s", error);
            throw error;
        }
    }
}

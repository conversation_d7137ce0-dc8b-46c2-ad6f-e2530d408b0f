import {
    RemoteInfoSource,
    RemoteToolDefinition,
    RunRemoteToolResult,
} from "@augment-internal/sidecar-libs/src/tools/remote-tools/remote-tool-host";
import {
    RemoteToolId,
    ToolAvailabilityStatus,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { APIServer } from "./augment-api";
import { getErrmsg } from "./exceptions";
import { getLogger } from "./logging";

/**
 * Provides an interface for the beachhead client to communicate with remote tools on the Augment backend.
 * This implementation handles tool discovery, execution, and safety checking for remote tools.
 */
export class BeachheadRemoteInfo implements RemoteInfoSource {
    private _logger = getLogger("BeachheadRemoteInfo");

    constructor(
        private readonly _apiServer: APIServer,
        private readonly _setupMode: boolean
    ) {}

    public async retrieveRemoteTools(
        supportedTools: RemoteToolId[]
    ): Promise<RemoteToolDefinition[]> {
        if (this._setupMode) {
            return [];
        }
        try {
            const apiResult = await this._apiServer.listRemoteTools(supportedTools);
            const availableTools = apiResult.tools.filter(
                (tool) => tool.availabilityStatus === ToolAvailabilityStatus.Available
            );
            return availableTools;
        } catch (e: any) {
            this._logger.error("Failed to list remote tools: %s", getErrmsg(e));
            return [];
        }
    }

    public filterToolsWithExtraInput(_toolIds: RemoteToolId[]): Promise<Set<RemoteToolId>> {
        // Since we're already filtering for tools that are immediately available in
        // retrieveRemoteTools, we can return an empty set here
        return Promise.resolve(new Set<RemoteToolId>());
    }

    public async runRemoteTool(
        toolRequestId: string,
        toolName: string,
        toolInputJson: string,
        toolId: RemoteToolId,
        signal: AbortSignal
    ): Promise<RunRemoteToolResult> {
        return await this._apiServer.runRemoteTool(
            toolRequestId,
            toolName,
            toolInputJson,
            toolId,
            // Since remote tools no longer need to be configured manually, it's ok to provide no extra input
            undefined,
            signal
        );
    }
}

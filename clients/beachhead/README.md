# Remote Agents Beachhead

The remote agent beachhead is a long-running process that manages workspace
synchronization and executes local development tools on behalf of the agent loop
running in the backend. The beachhead is deployed in a container with customer
code, isolating the agent loop from the execution environment.

Quickstart:
```
AUGMENT_API_URL=https://staging-shard-0.api.augmentcode.com AUGMENT_API_TOKEN=$(cat ~/.augment/token) bazel run //clients/beachhead:beachhead_dev -- --workspace-root /home/<USER>/augment/ --instruction "create a new temp.txt file with the content hello world"
```

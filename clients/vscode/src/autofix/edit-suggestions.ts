import { v4 as uuidv4 } from "uuid";
import * as vscode from "vscode";

import {
    ChangeType,
    type IEditSuggestion,
    NextEditMode,
    NextEditScope,
    SuggestionState,
} from "../next-edit/next-edit-types";
import { readFileUtf8 } from "../utils/fs-utils";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { TextReplacement } from "./autofix-state";

async function getCharIndicesFromLine(
    filePath: string,
    startLine: number,
    endLine: number
): Promise<{ charStart: number; charEnd: number }> {
    const fileContent = await readFileUtf8(filePath);
    const lines = fileContent.split("\n");

    // Convert from 1-based to 0-based line numbers
    const zeroBasedStartLine = startLine - 1;
    const zeroBasedEndLine = endLine - 1;

    // Calculate character positions
    const charStart = lines
        .slice(0, zeroBasedStartLine)
        .reduce((sum, line) => sum + line.length + 1, 0);

    const charEnd =
        lines.slice(0, zeroBasedEndLine + 1).reduce((sum, line) => sum + line.length + 1, 0) - 1;

    return { charStart, charEnd };
}

export async function convertTextReplacementToIEditSuggestion(
    workspaceManager: WorkspaceManager,
    textReplacement: TextReplacement
): Promise<IEditSuggestion> {
    const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
    if (!workspaceFolder) {
        throw new Error("No workspace folder found");
    }

    const absPath = vscode.Uri.joinPath(workspaceFolder.uri, textReplacement.path).fsPath;
    const qualifiedPathName = workspaceManager.resolvePathName(absPath);
    if (!qualifiedPathName) {
        throw new Error(`Could not resolve path: ${textReplacement.path}`);
    }

    const { charStart, charEnd } = await getCharIndicesFromLine(
        qualifiedPathName.absPath,
        textReplacement.startLine,
        textReplacement.endLine
    );

    return {
        requestId: uuidv4(),
        mode: NextEditMode.Background,
        scope: NextEditScope.File,
        result: {
            suggestionId: uuidv4(),
            path: textReplacement.path,
            blobName: textReplacement.oldBlobName ?? "",
            charStart,
            charEnd,
            existingCode: textReplacement.oldText,
            suggestedCode: textReplacement.text,
            changeDescription: textReplacement.description,
            diffSpans: [], // Computing proper diff spans would require additional diff logic
            editingScore: 1.0,
            localizationScore: 1.0,
            editingScoreThreshold: 1.0,
        },
        qualifiedPathName,
        lineRange: {
            start: textReplacement.startLine - 1,
            stop: textReplacement.endLine - 1,
        },
        uriScheme: "file",
        occurredAt: new Date(),
        state: SuggestionState.fresh,
        changeType: ChangeType.modification, // TODO: Is this always true?
    };
}

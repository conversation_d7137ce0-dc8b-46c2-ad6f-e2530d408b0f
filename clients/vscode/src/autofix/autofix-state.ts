import { type IEditSuggestion } from "../next-edit/next-edit-types";

export type TextReplacement = {
    description: string;
    path: string;
    text: string;
    oldText: string;
    startLine: number;
    endLine: number;
    sequenceId: number;
    oldBlobName?: string;
};

export enum AutofixMessages {
    retesting = "Retesting...",
    testRunning = "Test running...",
    testFailed = "Test failed",
    testPassed = "Test passed",
    generatingSolutions = "Generating solutions...",
    suggestedSolutions = "Suggested solutions",
    selectedSolutions = "Selected solutions",
}

export type AutofixCommand = {
    input: string;
    output: string;
    exitCode?: number;
};

export type AutofixUserSteeringExchange = {
    requestMessage: string;
    summary: string;
    replacements: TextReplacement[];
    requestId: string;
};

export const enum AutofixIterationStage {
    runTest = "run-test",
    applyFix = "apply-fix",
}

export type AutofixTextReplacement = {
    description: string;
    path: string;
    text: string;
    oldText: string;
    startLine: number;
    endLine: number;
};

export type AutofixFixSuggestion = {
    summary: string;
    replacements: IEditSuggestion[];
    originalReplacements: TextReplacement[];
};

export interface AutofixPlan {
    summary: string;
    replacements: IEditSuggestion[];
    originalReplacements: TextReplacement[];
}

export interface AutofixIteration {
    id: string;
    currentStage: AutofixIterationStage;
    commandOutput?: string;
    commandFailed?: boolean;
    suggestedSolutions?: AutofixFixSuggestion[];
    selectedSolutions?: IEditSuggestion[];
    isFirstIteration: boolean;
}

export interface IConversationAutofixExtraData extends Record<string, unknown> {
    isAutofix: true;
    autofixCommand?: string;
    autofixIterations?: AutofixIteration[];
    autofixSteeringHistory?: AutofixUserSteeringExchange[];
}

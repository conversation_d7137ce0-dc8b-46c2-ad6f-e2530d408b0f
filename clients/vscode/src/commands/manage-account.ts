import * as vscode from "vscode";

import { AugmentCommand, CommandType } from "../command-manager";
import type { AugmentExtension } from "../extension";

export class ManageAccountCommand extends AugmentCommand {
    public static readonly commandIDCommunity = "vscode-augment.manageAccountCommunity";
    public static readonly commandIDProfessional = "vscode-augment.manageAccountProfessional";
    public static readonly commandIDEnterprise = "vscode-augment.manageAccountEnterprise";

    type = CommandType.public;

    constructor(
        private extension: AugmentExtension,
        commandId: string
    ) {
        super();
        this._commandID = commandId;
    }

    run() {
        void vscode.env.openExternal(vscode.Uri.parse("https://app.augmentcode.com/account"));
    }

    canRun(): boolean {
        const userTier = this.extension.userTier;
        const expectedTier =
            this._commandID === ManageAccountCommand.commandIDCommunity
                ? "community"
                : this._commandID === ManageAccountCommand.commandIDProfessional
                  ? "professional"
                  : "enterprise";

        return super.canRun() && userTier === expectedTier;
    }
}

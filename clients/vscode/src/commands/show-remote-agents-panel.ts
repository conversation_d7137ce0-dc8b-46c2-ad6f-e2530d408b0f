import { ExtensionContext, Uri } from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { GuidelinesWatcher } from "../chat/guidelines-watcher";
import { AugmentCommand, CommandType } from "../command-manager";
import { AugmentExtension } from "../extension";
import { FeatureFlagManager } from "../feature-flags";
import { WorkTimer } from "../metrics/work-timer";
import { AugmentGlobalState } from "../utils/context";
import { isExtensionVersionGte } from "../utils/environment";
import { RemoteAgentHomePanel } from "../webview-panels/remote-agents/remote-agent-home-panel";
import { WorkspaceManager } from "../workspace/workspace-manager";

// Show the Remote Agents Home Panel
export class ShowRemoteAgentsPanelCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.showRemoteAgentsPanel";

    type = CommandType.public;

    constructor(
        private _extensionUri: Uri,
        private _apiServer: APIServer,
        private _workTimer: WorkTimer,
        private _globalState: AugmentGlobalState,
        private _featureFlagManager: FeatureFlagManager,
        private _extension: AugmentExtension,
        private _configListener: AugmentConfigListener,
        private _guidelinesWatcher: GuidelinesWatcher,
        private _workspaceManager: WorkspaceManager,
        private _extensionContext: ExtensionContext
    ) {
        super("Show Remote Agents Panel", true);
    }

    private remoteAgentsEnabled(): boolean {
        return isExtensionVersionGte(
            this._featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion ?? ""
        );
    }

    run() {
        if (!this.remoteAgentsEnabled()) {
            return;
        }

        const deps = {
            extensionUri: this._extensionUri,
            apiServer: this._apiServer,
            workTimer: this._workTimer,
            globalState: this._globalState,
            toolConfigStore: this._extension.toolConfigStore!,
            configListener: this._configListener,
            guidelinesWatcher: this._guidelinesWatcher,
            workspaceManager: this._workspaceManager,
            extensionContext: this._extensionContext,
        };

        RemoteAgentHomePanel.createOrShow(deps);
    }

    canRun(): boolean {
        return this.remoteAgentsEnabled();
    }
}

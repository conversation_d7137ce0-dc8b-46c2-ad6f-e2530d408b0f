import { Blobs } from "@augment-internal/sidecar-libs/src/api/types";
import { ChangeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { VCSChange } from "@augment-internal/sidecar-libs/src/vcs/watcher/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { spawn } from "child_process";
import * as fs from "fs";
import { join } from "path";
import * as vscode from "vscode";

import {
    APIServer,
    CheckCommandResult,
    ContainErrorsResult,
    FileChunk,
    FilePatch,
    FixPlanResult,
    Location,
    UploadBlob,
} from "../augment-api";
import { AugmentCommand, CommandType } from "../command-manager";
import { AugmentExtension } from "../extension";
import { type AugmentLogger, getLogger } from "../logging";
import { WorkspaceContext } from "../workspace/workspace-context";

interface OutputReturn {
    output: string;
    success: boolean;
}

interface ParsedDiff {
    beforePath: string;
    afterPath: string;
    changeType: ChangeType;
    beforeContent: string;
    afterContent: string;
}

class VirtualDocumentProvider implements vscode.TextDocumentContentProvider {
    private documents: Map<string, string> = new Map();

    setDocumentContent(uri: vscode.Uri, content: string): void {
        this.documents.set(uri.toString(), content);
    }

    provideTextDocumentContent(uri: vscode.Uri): string | undefined {
        return this.documents.get(uri.toString());
    }
}

/**
 * Class to handle utility functions and command execution for the Autofix command.
 */
export class AutofixCommand extends AugmentCommand {
    public static readonly commandID = "vscode-augment.autofixCommand";
    public type = CommandType.public;
    private static testCommandHistory: string[] = [];

    private readonly _outputChannel: vscode.OutputChannel;
    private readonly _chunkCount: number = 32;
    private readonly _autofixLogger: AugmentLogger = getLogger("AutofixCommand");

    constructor(
        private readonly _extension: AugmentExtension,
        private readonly _apiServer: APIServer
    ) {
        super();
        this._outputChannel = vscode.window.createOutputChannel("Autofix Command Output");
    }

    private async _locationToChunkContents(
        location: Location,
        repoRoot: string
    ): Promise<FileChunk> {
        const fullPath = join(repoRoot, location.path);
        const code: string = await fs.promises.readFile(fullPath, { encoding: "utf8" });
        const lines = code.split("\n");
        const expansionRange: number = 0; // No expansion
        const lineStart = Math.max(location.range.start - expansionRange, 0);
        const lineEnd = Math.min(location.range.stop + expansionRange, lines.length);
        return {
            path: location.path,
            content: lines.slice(lineStart, lineEnd).join("\n"),
            // eslint-disable-next-line @typescript-eslint/naming-convention
            start_line: lineStart,
            // eslint-disable-next-line @typescript-eslint/naming-convention
            end_line: lineEnd,
        };
    }

    private _clearAndShowOutput(output: string) {
        this._outputChannel.clear();
        this._outputChannel.appendLine(output);
        this._outputChannel.show();
    }

    private async _openDiffForChanges(changes: FilePatch[], repoRoot: string) {
        const openDiffPromises = changes.map(async (file) => {
            const scheme = "virtual"; // Custom URI scheme
            const provider = new VirtualDocumentProvider();
            const reg = vscode.workspace.registerTextDocumentContentProvider(scheme, provider);
            const targetUri = vscode.Uri.parse(`${scheme}:/${file.path}.right`);
            provider.setDocumentContent(targetUri, file.targetContent);
            await vscode.commands.executeCommand(
                "vscode.diff",
                vscode.Uri.file(join(repoRoot, file.path)),
                targetUri,
                `Diff - ${file.path}`
            );
            await new Promise<void>((resolve) => {
                const onDidCloseTextDocument = vscode.workspace.onDidCloseTextDocument(
                    (closedDocument) => {
                        if (closedDocument.uri.toString() === targetUri.toString()) {
                            onDidCloseTextDocument.dispose();
                            reg.dispose();
                            resolve();
                        }
                    }
                );
            });
        });
        await Promise.all(openDiffPromises);
    }

    private async _getTestCommand(): Promise<string | undefined> {
        const quickPick = vscode.window.createQuickPick();
        quickPick.placeholder = "Enter the test command to run (e.g. `pytest`)";
        quickPick.ignoreFocusOut = true;

        const updateItems = () => {
            const inputItem = quickPick.value
                ? [{ label: quickPick.value, description: "New command" }]
                : [];
            const historyItems = AutofixCommand.testCommandHistory
                .filter((cmd) => cmd.toLowerCase() !== quickPick.value.toLowerCase())
                .map((cmd) => ({ label: cmd, description: "Previous command" }));
            quickPick.items = [...inputItem, ...historyItems];
        };

        quickPick.onDidChangeValue(updateItems);
        updateItems();

        quickPick.show();

        const result = await new Promise<string | undefined>((resolve) => {
            quickPick.onDidAccept(() => {
                const value = quickPick.selectedItems[0]?.label || quickPick.value;
                resolve(value || undefined);
                quickPick.hide();
            });
            quickPick.onDidHide(() => resolve(undefined));
        });

        quickPick.dispose();

        if (result) {
            AutofixCommand.testCommandHistory = [
                result,
                ...AutofixCommand.testCommandHistory.filter((item) => item !== result),
            ].slice(0, 10);
        }

        return result;
    }

    private _runCommand(
        command: string,
        repoRoot: string,
        stream: boolean = false
    ): Promise<OutputReturn> {
        if (stream) {
            this._clearAndShowOutput("Running command: " + command);
        }
        return new Promise<OutputReturn>((resolve) => {
            const cmdParts = command.split(" ");
            const cmd = cmdParts[0];
            const args = cmdParts.slice(1);
            const process = spawn(cmd, args, {
                cwd: repoRoot,
                stdio: ["ignore", "pipe", "pipe"],
                shell: true,
            });

            let output: string = "";
            process.stdout.on("data", (data) => {
                output += (data as Buffer).toString();
                if (stream) {
                    this._outputChannel.append((data as Buffer).toString());
                }
            });

            process.stderr.on("data", (data) => {
                output += (data as Buffer).toString();
                if (stream) {
                    this._outputChannel.append((data as Buffer).toString());
                }
            });

            process.on("close", (code) => {
                resolve({ output: output, success: code === 0 });
            });

            process.on("error", (err) => {
                resolve({ output: err.message, success: false });
            });
        });
    }

    private async _askYesNo(question: string, detail: string = ""): Promise<boolean> {
        const options: vscode.MessageOptions = {
            modal: true,
            detail: detail,
        };
        const buttons = [{ title: "Yes" }, { title: "No", isCloseAffordance: true }];
        const result = await vscode.window.showInformationMessage(question, options, ...buttons);
        return result === buttons[0];
    }

    private async _getEditLocations(
        instruction: string,
        vcsChange: VCSChange,
        blobs: Blobs,
        repoRoot: string,
        chunkCount: number,
        requestId: string
    ): Promise<FileChunk[]> {
        const locations = await this._apiServer.nextEditLocation(
            requestId,
            instruction,
            "", // only for single file mode
            vcsChange,
            [], // No file edit events
            blobs,
            [], // No recent changes
            [], // No diagnostics
            chunkCount,
            false // No single file mode
        );

        // FIXME (lior) - handle unknown blob names in the future
        // if (locations.unknownBlobNames.length > 0) {
        //     throw new Error("Unknown blob names: " + locations.unknownBlobNames.join(", "));
        // }

        if (locations.checkpointNotFound) {
            throw new Error("Checkpoint not found");
        }
        if (locations.candidateLocations.length === 0) {
            throw new Error("No candidate locations found");
        }
        return Promise.all(
            locations.candidateLocations.map(async (scored) =>
                this._locationToChunkContents(scored.item, repoRoot)
            )
        );
    }

    private async _diffToChunks(parsedDiff: ParsedDiff[]): Promise<FileChunk[]> {
        return Promise.all(
            parsedDiff.map((diff) => {
                const lines = diff.afterContent.split("\n");
                return {
                    path: diff.afterPath,
                    content: diff.afterContent,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    start_line: 0,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    end_line: lines.length - 1,
                } as FileChunk;
            })
        );
    }

    private async _applyChanges(
        changes: FilePatch[],
        userInput: string | undefined,
        repoRoot: string
    ): Promise<void> {
        if (userInput && (await this._askYesNo("Would you like to apply and test the changes?"))) {
            for (const change of changes) {
                const { path: filePath, targetContent: targetCode } = change;
                await fs.promises.writeFile(join(repoRoot, filePath), targetCode);
            }
            const commandOutput = await this._runCommand(userInput, repoRoot, true);
            if (!commandOutput.success) {
                const question = "Command failed. Keep changes anyway?";
                if (!(await this._askYesNo(question, "return code != 0"))) {
                    for (const change of changes) {
                        const { path: filePath, sourceContent: sourceCode } = change;
                        await fs.promises.writeFile(join(repoRoot, filePath), sourceCode);
                    }
                }
            } else {
                void vscode.window.showInformationMessage("The tests has passed.");
            }
        }
    }

    private _checkOutputReturn(output: OutputReturn): boolean {
        return output.success && output.output.length > 0;
    }

    private async _getGitDiffObject(mergeBase: string, repoRoot: string): Promise<ParsedDiff[]> {
        const statusCommand: string = `git diff ${mergeBase} --name-status -M`;
        const statusOutput = await this._runGitCommand(statusCommand, repoRoot);
        const diffLines = statusOutput.split("\n");
        const parsedDiffs: ParsedDiff[] = [];
        for (const line of diffLines) {
            const [status, ...pathParts] = line.split("\t");
            let beforePath = pathParts[0];
            let afterPath = pathParts[pathParts.length - 1]; // For renames, this will be the new path

            let changeType: ChangeType;
            switch (status[0]) {
                case "M":
                    changeType = ChangeType.modified;
                    if (beforePath !== afterPath) {
                        throw new Error("Unexpected change type: modified with rename");
                    }
                    break;
                case "A":
                    changeType = ChangeType.added;
                    beforePath = "";
                    break;
                case "D":
                    changeType = ChangeType.deleted;
                    afterPath = "";
                    break;
                case "R":
                    changeType = ChangeType.renamed;
                    if (beforePath === afterPath) {
                        throw new Error("Unexpected change type: rename with no change");
                    }
                    break;
                default:
                    continue; // Skip unknown change types
            }

            let beforeContent = "";
            let afterContent = "";
            if (changeType !== ChangeType.added) {
                const showMainCommand: string = `git show ${mergeBase}:${beforePath}`;
                const outputShowMain = await this._runCommand(showMainCommand, repoRoot);
                if (!outputShowMain.success) {
                    throw new Error("Failed to execute git command");
                }
                beforeContent = outputShowMain.output;
            }
            if (changeType !== ChangeType.deleted) {
                const path = join(repoRoot, afterPath);
                afterContent = await fs.promises.readFile(path, { encoding: "utf8" });
            }

            parsedDiffs.push({
                beforePath: beforePath,
                afterPath: afterPath,
                changeType,
                beforeContent,
                afterContent,
            });
        }

        return parsedDiffs;
    }

    private _diffToVCSChange(parsedDiff: ParsedDiff[]): VCSChange {
        const blobNameCalculator = new BlobNameCalculator(1000);
        return {
            commits: [],
            workingDirectory: parsedDiff.map((diff) => {
                const blobNameBefore =
                    blobNameCalculator.calculateNoThrow(diff.afterPath, diff.afterContent) || "";
                const blobNameAfter =
                    blobNameCalculator.calculateNoThrow(diff.afterPath, diff.afterContent) || "";
                return {
                    beforePath: diff.beforePath,
                    afterPath: diff.afterPath,
                    changeType: diff.changeType,
                    headBlobName: blobNameBefore,
                    indexedBlobName: blobNameAfter,
                    currentBlobName: blobNameAfter,
                };
            }),
        };
    }

    private async _uploadBlobs(vcsChange: VCSChange, parsedDiff: ParsedDiff[]): Promise<void> {
        const uploadBlobs: UploadBlob[] = [];
        vcsChange.workingDirectory.forEach((wd, index) => {
            if (wd.currentBlobName) {
                uploadBlobs.push({
                    pathName: wd.afterPath || "",
                    text: parsedDiff[index].afterContent,
                    blobName: wd.currentBlobName,
                    metadata: [],
                });
            }
            if (wd.headBlobName) {
                uploadBlobs.push({
                    pathName: wd.beforePath || "",
                    text: parsedDiff[index].beforeContent,
                    blobName: wd.headBlobName,
                    metadata: [],
                });
            }
        });
        // Workaround to avoid errors on 413 Request Entity Too Large
        try {
            await Promise.all(uploadBlobs.map((blob) => this._apiServer.batchUpload([blob])));
        } catch (e) {
            // Ignore errors as we have nothing to do with it.
        }
    }

    private async _runGitCommand(command: string, cwd: string): Promise<string> {
        const result = await this._runCommand(command, cwd);
        if (!this._checkOutputReturn(result)) {
            throw new Error(`Failed to execute git command: ${command}\nOutput: ${result.output}`);
        }
        return result.output.trimEnd();
    }

    public async run(): Promise<void> {
        if (!this._extension.workspaceManager) {
            throw new Error("Workspace manager is not ready");
        }
        const workspaceContext: WorkspaceContext = this._extension.workspaceManager.getContext();
        if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
            void vscode.window.showErrorMessage("No workspace folders");
            return;
        }
        const workspaceRoot: string = vscode.workspace.workspaceFolders[0].uri.fsPath;
        const repoRoot: string = await this._runGitCommand(
            "git rev-parse --show-toplevel",
            workspaceRoot
        );
        const mainBranch: string = await this._runGitCommand(
            "git rev-parse --abbrev-ref origin/HEAD",
            repoRoot
        ); // eg. "origin/main"
        const currentBranch: string = await this._runGitCommand(
            "git rev-parse --abbrev-ref HEAD",
            repoRoot
        ); // eg. "main"
        const getLastPart = (path: string) => path.slice(path.lastIndexOf("/") + 1);
        let mergeBase: string = "HEAD";
        if (getLastPart(currentBranch) !== getLastPart(mainBranch)) {
            // We are not not on the main branch, so we need to find the merge base
            mergeBase = await this._runGitCommand(`git merge-base HEAD ${mainBranch}`, repoRoot);
        }
        const gitDiffCommand: string = `git diff -U10 ${mergeBase}`;
        const gitDiff = await this._runCommand(gitDiffCommand, repoRoot);
        if (!this._checkOutputReturn(gitDiff)) {
            void vscode.window.showErrorMessage(`Failed to get git diff: ${gitDiff.output}`);
            return;
        }
        let parsedDiff: ParsedDiff[];
        try {
            parsedDiff = await this._getGitDiffObject(mergeBase, repoRoot);
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to parse git diff: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }
        const vcsChange = this._diffToVCSChange(parsedDiff);
        try {
            await this._uploadBlobs(vcsChange, parsedDiff);
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to upload blobs: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }

        const userInput = await this._getTestCommand();
        if (!userInput) {
            void vscode.window.showInformationMessage("No test command provided.");
            return;
        }
        const commandOutput = await this._runCommand(userInput, repoRoot, true);
        const passQuestion = "The tests has passed, would you like to continue anyway?";
        if (commandOutput.success && !(await this._askYesNo(passQuestion, "return code: 0"))) {
            return;
        }
        if (commandOutput.output.length === 0) {
            void vscode.window.showInformationMessage("No command output found.");
            return;
        }
        let requestId = this._apiServer.createRequestId();
        this._autofixLogger.info(`Requesting edit locations with requestId=${requestId}`);
        const retreivedPromise = this._getEditLocations(
            commandOutput.output,
            vcsChange,
            workspaceContext.blobs,
            repoRoot,
            this._chunkCount,
            requestId
        );
        requestId = this._apiServer.createRequestId();
        this._autofixLogger.info(`Requesting check command with requestId=${requestId}`);
        const commandCheckPromise = this._apiServer.checkCommand(
            userInput,
            commandOutput.output,
            requestId
        );
        requestId = this._apiServer.createRequestId();
        this._autofixLogger.info(`Requesting contain errors with requestId=${requestId}`);
        const containErrorsPromise = this._apiServer.containErrors(
            userInput,
            commandOutput.output,
            requestId
        );
        try {
            const commandCheck: CheckCommandResult = await commandCheckPromise;
            const commandCheckQuestion = "Command doesn't appear code-related. Continue anyway?";
            if (!commandCheck.result) {
                if (!(await this._askYesNo(commandCheckQuestion, commandCheck.desc))) {
                    return;
                }
            } else {
                void vscode.window.showInformationMessage("✅ Command check passed.");
            }
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to check command: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }
        try {
            const containErrors: ContainErrorsResult = await containErrorsPromise;
            const containErrorQuestion = "Couldn't find errors in the output. Continue anyway?";
            if (!containErrors.result) {
                if (!(await this._askYesNo(containErrorQuestion, containErrors.desc))) {
                    return;
                }
            } else {
                void vscode.window.showInformationMessage("✅ Contain errors check passed.");
            }
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to check for errors: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }
        let retreived: FileChunk[];
        try {
            retreived = await retreivedPromise;
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to get edit locations: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }
        const curfiles = parsedDiff.filter((diff) => diff.afterPath !== "");
        const changedFiles: string[] = curfiles.map((diff) => diff.afterPath);
        const newChunks = await this._diffToChunks(curfiles);
        const chunks = retreived.filter((c) => !changedFiles.includes(c.path)).concat(newChunks);
        void vscode.window.showInformationMessage(
            `Found ${retreived.length} potential edit locations.`
        );

        void vscode.window.showInformationMessage("Creating fix plan...");
        let fixPlan: FixPlanResult;
        try {
            requestId = this._apiServer.createRequestId();
            this._autofixLogger.info(`Requesting create fix plan with requestId=${requestId}`);
            fixPlan = await this._apiServer.createFixPlan(
                gitDiff.output,
                userInput,
                commandOutput.output,
                chunks,
                requestId
            );
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to create fix plan: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }
        const changedFilesFormat: string = `Changes:\n${fixPlan.changes
            .map((change) => `- ${change.path}`)
            .join("\n")}`;
        const details = `${fixPlan.fix_desc}\n\n${changedFilesFormat}`;

        const patchPromise = Promise.all(
            fixPlan.changes.map(async (change) => {
                const fullPath: string = join(repoRoot, change.path);
                const fileContent: string = await fs.promises.readFile(fullPath, {
                    encoding: "utf8",
                });
                requestId = this._apiServer.createRequestId();
                this._autofixLogger.info(`Requesting apply file fix with requestId=${requestId}`);
                return this._apiServer.applyFileFix(change, fileContent, requestId);
            })
        );
        if (!(await this._askYesNo("Do you confirm the the following fix?", details))) {
            return;
        }
        void vscode.window.showInformationMessage("Generating patch...");
        let changes: FilePatch[];
        try {
            changes = await patchPromise;
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to generate patch: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }

        changes = changes.filter((change) => change.targetContent !== change.sourceContent);
        if (changes.length === 0) {
            void vscode.window.showWarningMessage("Sorry, no changes to apply.");
            return;
        }

        void vscode.window.showInformationMessage("Close all diff views to continue.");
        try {
            await this._openDiffForChanges(changes, repoRoot);
        } catch (e) {
            if (e instanceof Error) {
                void vscode.window.showErrorMessage(`Failed to open diff views: ${e.message}`);
            }
            return;
        }
        try {
            await this._applyChanges(changes, userInput, repoRoot);
        } catch (e) {
            void vscode.window.showErrorMessage(
                `Failed to apply changes: ${e instanceof Error ? e.message : String(e)}`
            );
            return;
        }
    }
}

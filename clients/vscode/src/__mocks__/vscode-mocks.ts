/**
 * vscode-types.ts contains a mock implementation of a subset of the vscode
 * interface for use in unit tests.
 */

/* eslint-disable @typescript-eslint/naming-convention */
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { createVSCodeMock } from "jest-mock-vscode";
import { ColorTheme } from "jest-mock-vscode/dist/vscode";
// We pull the mock like this instead of through the `vscodeMock` object because
// we want access to the helper methods it provides (e.g. `isUri`), but the
// vscodeMock object gets its type definitions from the real vscode interaface,
// not from the mock.
import { Uri as UriMock } from "jest-mock-vscode/dist/vscode/uri";
import * as path from "path";
import {
    CodeActionProviderMetadata,
    Command,
    ConfigurationChangeEvent,
    DecorationOptions,
    DiagnosticChangeEvent,
    DocumentSelector,
    EndOfLine,
    Event,
    Extension,
    HoverProvider,
    NotebookCellOutput,
    OpenDialogOptions,
    OutputChannel,
    Progress,
    ProgressOptions,
    QuickPickItem,
    QuickPickOptions,
    SecretStorageChangeEvent,
    SnippetString,
    TextEditorDecorationType,
    TextEditorEdit,
    TextEditorOptions,
    TextEditorVisibleRangesChangeEvent,
    version as vscodeVersion,
    WebviewOptions,
    WebviewPanel,
    WebviewPanelOnDidChangeViewStateEvent,
    WebviewPanelOptions,
    WindowState,
    WorkspaceConfiguration,
    WorkspaceFolder,
    WorkspaceFoldersChangeEvent,
} from "vscode";

import { uriToAbsPath } from "../utils/uri";
import { FSSubscriber, FSWatcher, mockFSUtils } from "./fs-utils";
import { generateMockWorkspaceConfig, getExampleUserConfig } from "./mock-augment-config";
import { MockFileSystem } from "./mock-filesystem";

const vscodeMock = createVSCodeMock(jest);

export const contextKeys: Record<string, any> = {};

export let version = vscodeVersion ?? "0.0.0";
export function setVsCodeVersion(v: string) {
    version = v;
}

export class Position extends vscodeMock.Position {}
export class Range extends vscodeMock.Range {}
export class Selection extends vscodeMock.Selection {}
export class WorkspaceEdit extends vscodeMock.WorkspaceEdit {}
export class CodeLens extends vscodeMock.CodeLens {}
export class Uri extends UriMock {}

export enum UIKind {
    Desktop = 1,
    Web = 2,
}

export enum ViewColumn {
    Active = -1,
    Beside = -2,
    One = 1,
    Two = 2,
    Three = 3,
    Four = 4,
    Five = 5,
    Six = 6,
    Seven = 7,
    Eight = 8,
    Nine = 9,
}

export class ThemeIcon {
    constructor(public id: string) {}
}

export class ThemeColor {
    /**
     * Creates a reference to a theme color.
     * @param id of the color. The available colors are listed in https://code.visualstudio.com/docs/getstarted/theme-color-reference.
     */
    constructor(_id: string) {}
}

/**
 * Represents a color theme kind.
 */
export enum ColorThemeKind {
    /**
     * A light color theme.
     */
    Light = 1,
    /**
     * A dark color theme.
     */
    Dark = 2,
    /**
     * A dark high contrast color theme.
     */
    HighContrast = 3,
    /**
     * A light high contrast color theme.
     */
    HighContrastLight = 4,
}

export class TextLine {
    constructor(
        public lineNumber: number,
        public text: string,
        public range: Range,
        public rangeIncludingLineBreak: Range,
        public firstNonWhitespaceCharacterIndex: number,
        public isEmptyOrWhitespace: boolean
    ) {}
}

export class TextDocument {
    protected _version = 0;
    protected _savedVersion = 0;

    constructor(
        public readonly uri: Uri,
        protected _text: string
    ) {}

    // Map of file extensions to vscode language ids
    private static extToLangMap = new Map<string, string>([
        [".js", "javascript"],
        [".ts", "typescript"],
        [".json", "json"],
        [".java", "java"],
        [".c", "c"],
        [".cpp", "cpp"],
        [".h", "c"],
        [".hpp", "cpp"],
        [".py", "python"],
        [".go", "go"],
        [".rs", "rust"],
        [".md", "markdown"],
    ]);

    public get fileName(): string {
        return this.uri.fsPath;
    }

    public get isUntitled(): boolean {
        return false;
    }

    public get languageId(): string {
        const ext = path.extname(this.fileName);
        return ext ? TextDocument.extToLangMap.get(ext) || ext : "plaintext";
    }

    public get version(): number {
        return this._version;
    }

    public get isDirty(): boolean {
        return this._version !== this._savedVersion;
    }

    public get isClosed(): boolean {
        return false;
    }

    public save(): Thenable<boolean> {
        this._savedVersion = this._version;
        return Promise.resolve(true);
    }

    public get eol(): EndOfLine {
        return EndOfLine.LF;
    }

    public get lineCount(): number {
        let line = 0;
        for (const ch of this._text) {
            if (ch === "\n") {
                line++;
            }
        }
        if (this._text.length > 0) {
            line++;
        }
        return line;
    }

    public lineAt(location: number | Position): TextLine {
        let line: number;
        if (location instanceof Position) {
            line = location.line;
        } else if (typeof location === "number") {
            line = location;
        } else {
            throw new Error("Invalid argument");
        }

        const lines = this._text.split("\n");
        const text = lines[line];
        const endsInNewline = line < lines.length - 1 || this.getText().endsWith("\n");
        const range = new Range(line, 0, line, text.length);
        const rangeIncludingLineBreak = new Range(
            line,
            0,
            line,
            text.length + (endsInNewline && text.length > 0 ? 1 : 0)
        );
        let firstNonWhitespaceCharacterIndex = text.search(/\S/);
        const isEmptyOrWhitespace = text.trim() === "";

        return new TextLine(
            line,
            text,
            range,
            rangeIncludingLineBreak,
            firstNonWhitespaceCharacterIndex,
            isEmptyOrWhitespace
        );
    }

    public offsetAt(position: Position): number {
        let line = 0;
        let character = 0;
        let offset = 0;
        for (; offset < this._text.length; offset++) {
            if (line === position.line && character === position.character) {
                return offset;
            }
            if (this._text[offset] === "\n") {
                if (line >= position.line) {
                    throw new Error(
                        `TextDocument line ${position.line} doesn't have` +
                            ` ${position.character} characters`
                    );
                }
                line++;
                character = 0;
            } else {
                character++;
            }
        }
        if (line === position.line && character === position.character) {
            return offset;
        }
        if (line === position.line) {
            throw new Error(
                `TextDocument line ${position.line} doesn't have` +
                    ` ${position.character} characters`
            );
        }
        // NOTE(arun): vscode doesn't throw an error if the line is out of range, but
        // rather returns the very last character of the document.
        return this._text.length;
    }

    public positionAt(offset: number): Position {
        offset = Math.min(offset, this._text.length);
        let line = 0;
        let character = 0;
        for (let curr = 0; curr < offset; curr++) {
            if (this._text[curr] === "\n") {
                line++;
                character = 0;
            } else {
                character++;
            }
        }
        return new Position(line, character);
    }

    public getText(range?: Range): string {
        const start = range === undefined ? 0 : this.offsetAt(range.start);
        const end = range === undefined ? this._text.length : this.offsetAt(range.end);
        return this._text.slice(start, end);
    }

    getWordRangeAtPosition(_position: Position, _regex?: RegExp): Range | undefined {
        throw new Error("Not implemented");
    }

    validateRange(range: Range): Range {
        const start = this.validatePosition(range.start);
        const end = this.validatePosition(range.end);
        return new Range(start, end);
    }

    validatePosition(position: Position): Position {
        try {
            this.offsetAt(position);
            return position;
        } catch {
            // If the character count is out of range, return end of that line
            // OR return the end of the document
            if (position.line <= this.lineCount - 1) {
                return new Position(position.line, this.lineAt(position.line).text.length);
            }
            return this.positionAt(this._text.length);
        }
    }
}

export type MutableTextDocumentInsert = {
    offset: number;
    text: string;
};
export type MutableTextDocumentDelete = {
    offset: number;
    count: number;
};

export type MutableTextDocumentUpdate = {
    offset: number;
    count: number;
    text: string;
};

export type MutableTextDocumentReplace = {
    offset: number;
    count: number;
    text: string;
};

export type MutableTextDocumentChange = {
    type: "insert" | "delete" | "update" | "replace";
    change:
        | MutableTextDocumentInsert
        | MutableTextDocumentDelete
        | MutableTextDocumentUpdate
        | MutableTextDocumentReplace;
};

export class MutableTextDocumentChangeBuilder {
    private _changes: MutableTextDocumentChange[] = [];

    public insert(offset: number, text: string): MutableTextDocumentChangeBuilder {
        this._changes.push({
            type: "insert",
            change: {
                offset,
                text,
            },
        });
        return this;
    }

    public update(offset: number, count: number, text: string): MutableTextDocumentChangeBuilder {
        this._changes.push({
            type: "update",
            change: {
                offset,
                count,
                text,
            },
        });
        return this;
    }

    public delete(offset: number, count: number): MutableTextDocumentChangeBuilder {
        this._changes.push({
            type: "delete",
            change: {
                offset,
                count,
            },
        });
        return this;
    }

    public replace(offset: number, count: number, text: string): MutableTextDocumentChangeBuilder {
        this._changes.push({
            type: "replace",
            change: {
                offset,
                count,
                text,
            },
        });
        return this;
    }

    public build(): readonly MutableTextDocumentChange[] {
        return this._changes;
    }
}

export class MutableTextDocument extends TextDocument {
    static readonly textEncoder = new TextEncoder();
    private _blobNameCalculator = new BlobNameCalculator(1000);

    public markDirty(): void {
        this._version++;
    }

    public insert(offset: number, text: string): TextDocumentChangeEvent {
        const change = this._insert(offset, text);
        this.markDirty();
        return new TextDocumentChangeEvent(this, [change], undefined);
    }

    public multiInsert(changes: [number, string][]): TextDocumentChangeEvent {
        const events = changes.map(([offset, text]) => this._insert(offset, text));
        this.markDirty();
        return new TextDocumentChangeEvent(this, events, undefined);
    }

    private _insert(offset: number, text: string): TextDocumentContentChangeEvent {
        const position = this.positionAt(offset);
        this._text = this._text.slice(0, offset) + text + this._text.slice(offset);
        return TextDocumentContentChangeEvent.forInsert(this, position, offset, text);
    }

    public update(offset: number, count: number, text: string): TextDocumentChangeEvent {
        const change = this._update(offset, count, text);
        this.markDirty();
        return new TextDocumentChangeEvent(this, [change], undefined);
    }

    private _update(offset: number, count: number, text: string): TextDocumentContentChangeEvent {
        const position = this.positionAt(offset);
        this._text = this._text.slice(0, offset) + text + this._text.slice(offset + count);
        return TextDocumentContentChangeEvent.forUpdate(this, position, offset, count, text);
    }

    public delete(offset: number, count: number): TextDocumentChangeEvent {
        const change = this._delete(offset, count);
        this.markDirty();
        return new TextDocumentChangeEvent(this, [change], undefined);
    }

    private _delete(offset: number, count: number): TextDocumentContentChangeEvent {
        const position = this.positionAt(offset);
        this._text = this._text.slice(0, offset) + this._text.slice(offset + count);
        return TextDocumentContentChangeEvent.forDelete(this, position, offset, count);
    }

    private _replace(
        offset: number,
        deletedChars: number,
        insertedText: string
    ): TextDocumentContentChangeEvent[] {
        if (deletedChars === 0) {
            return [this._insert(offset, insertedText)];
        } else if (insertedText.length === 0) {
            return [this._delete(offset, deletedChars)];
        }
        const change1 = this._delete(offset, deletedChars);
        const change2 = this._insert(offset, insertedText);
        return [change1, change2];
    }

    public replace(
        offset: number,
        deletedChars: number,
        insertedText: string
    ): TextDocumentChangeEvent {
        const changes = this._replace(offset, deletedChars, insertedText);
        this.markDirty();
        return new TextDocumentChangeEvent(this, changes, undefined);
    }

    public applyChanges(changes: readonly MutableTextDocumentChange[]): TextDocumentChangeEvent {
        const contentChanges: TextDocumentContentChangeEvent[] = [];
        for (const { type, change } of changes) {
            switch (type) {
                case "insert": {
                    const insertChange = change as MutableTextDocumentInsert;
                    contentChanges.push(this._insert(insertChange.offset, insertChange.text));
                    break;
                }
                case "update": {
                    const updateChange = change as MutableTextDocumentUpdate;
                    contentChanges.push(
                        this._update(updateChange.offset, updateChange.count, updateChange.text)
                    );
                    break;
                }
                case "delete": {
                    const deleteChange = change as MutableTextDocumentDelete;
                    contentChanges.push(this._delete(deleteChange.offset, deleteChange.count));
                    break;
                }
                case "replace": {
                    const replaceChange = change as MutableTextDocumentReplace;
                    contentChanges.push(
                        ...this._replace(
                            replaceChange.offset,
                            replaceChange.count,
                            replaceChange.text
                        )
                    );
                    break;
                }
            }
        }
        this.markDirty();
        return new TextDocumentChangeEvent(this, contentChanges, undefined);
    }

    public applyEdit(edit: TextEdit): TextDocumentChangeEvent {
        const startOffset = this.offsetAt(edit.range.start);
        const endOffset = this.offsetAt(edit.range.end);
        this._text = this._text.slice(0, startOffset) + edit.newText + this._text.slice(endOffset);
        return new TextDocumentChangeEvent(
            this,
            [
                new TextDocumentContentChangeEvent(
                    edit.range,
                    startOffset,
                    endOffset - startOffset,
                    edit.newText
                ),
            ],
            undefined
        );
    }

    public getBlobName(pathName: string): string {
        const bytes = MutableTextDocument.textEncoder.encode(this._text);
        return this._blobNameCalculator.calculate(pathName, bytes)!;
    }
}

export class TextDocumentContentChangeEvent {
    constructor(
        public readonly range: Range,
        public readonly rangeOffset: number,
        public readonly rangeLength: number,
        public readonly text: string
    ) {}

    /**
     * The methods below aren't part of the real vscode.TextDocumentContentChangeEvent
     */

    static forInsert(
        document: TextDocument,
        position: Position,
        offset: number,
        text: string
    ): TextDocumentContentChangeEvent {
        const range = new Range(position, position);
        return new TextDocumentContentChangeEvent(range, offset, 0, text);
    }

    static forUpdate(
        document: TextDocument,
        position: Position,
        offset: number,
        count: number,
        text: string
    ): TextDocumentContentChangeEvent {
        const range = new Range(position, position.translate(0, count));
        return new TextDocumentContentChangeEvent(range, offset, count, text);
    }

    static forDelete(
        document: TextDocument,
        position: Position,
        offset: number,
        count: number
    ): TextDocumentContentChangeEvent {
        // We have observed that VSCode's deletion ranges are the length of
        // the deletion.
        const range = new Range(position, position.translate(0, count));
        return new TextDocumentContentChangeEvent(range, offset, count, "");
    }
}

export enum TextDocumentChangeReason {
    /** The text change is caused by an undo operation. */
    Undo = 1,

    /** The text change is caused by an redo operation. */
    Redo = 2,
}

export class TextDocumentChangeEvent {
    constructor(
        public readonly document: TextDocument,
        public readonly contentChanges: readonly TextDocumentContentChangeEvent[],
        public readonly reason: TextDocumentChangeReason | undefined = undefined
    ) {}
}

export function emptyTextDocumentChangeEvent(
    document: TextDocument,
    reason?: TextDocumentChangeReason
) {
    return new TextDocumentChangeEvent(document, [], reason);
}

export class TextEditor {
    public selections: Selection[] = [new Selection(0, 0, 0, 0)];

    constructor(
        public document: TextDocument,
        public options: TextEditorOptions = {},
        // Note that this should be readonly,
        // but we make it mutable to make it easier to change.
        public viewColumn: ViewColumn | undefined = ViewColumn.Active
    ) {}

    public get selection(): Selection {
        return this.selections[0];
    }

    public set selection(selection: Selection) {
        this.selections[0] = selection;
        window.changeTextEditorSelection.fire({
            textEditor: this,
            selections: [selection],
            kind: TextEditorSelectionChangeKind.Command,
        });
    }

    public visibleRanges: readonly Range[] = [new Range(0, 0, 0, 0)];

    edit(_apiServercallback: (editBuilder: TextEditorEdit) => void): Thenable<boolean> {
        return new Promise((resolve) => resolve(true));
    }

    insertSnippet(_snippet: SnippetString): Thenable<boolean> {
        return new Promise((resolve) => resolve(true));
    }

    public currentDecorations: Map<
        TextEditorDecorationType,
        readonly Range[] | readonly DecorationOptions[]
    > = new Map();

    setDecorations(
        _apiServerdecorationType: TextEditorDecorationType,
        _apiServerrangesOrOptions: readonly Range[] | readonly DecorationOptions[]
    ) {
        this.currentDecorations.set(_apiServerdecorationType, _apiServerrangesOrOptions);
    }

    revealRange(_range: Range, _revealType?: TextEditorRevealType) {}

    show(_column?: ViewColumn) {}

    hide() {}
}

export enum TextEditorRevealType {
    /**
     * The range will be revealed with as little scrolling as possible.
     */
    Default = 0,
    /**
     * The range will always be revealed in the center of the viewport.
     */
    InCenter = 1,
    /**
     * If the range is outside the viewport, it will be revealed in the center of the viewport.
     * Otherwise, it will be revealed with as little scrolling as possible.
     */
    InCenterIfOutsideViewport = 2,
    /**
     * The range will always be revealed at the top of the viewport.
     */
    AtTop = 3,
}

type Disposer = () => any;

export class Disposable {
    public dispose: () => any;

    constructor(callOnDispose: Disposer) {
        this.dispose = (): any => callOnDispose();
    }

    static from(...disposables: Disposable[]): Disposable {
        return new Disposable(() => {
            for (const disposable of disposables) {
                disposable.dispose();
            }
        });
    }
}

/*

This class is an implementation of Disposable that tracks the
number of instances and  the number of disposed instances.

We can use this Disposable in our VSCode mocks to track
if we are leaking disposables.

See setup-jest.ts for an example  of resetting and warning
on possible leaks.

assertDisposed() can be used in our tests to ensure that all
disposables are disposed.

*/
export class TrackedDisposable implements Disposable {
    private static instanceCount = 0;
    private static disposedCount = 0;
    private static items = new Set<TrackedDisposable>();
    private disposed = false;
    private trace = new Error().stack;

    constructor(private readonly onDispose: () => void) {
        TrackedDisposable.instanceCount++;
        TrackedDisposable.items.add(this);
    }

    dispose(): void {
        if (!this.disposed) {
            this.onDispose();
            this.disposed = true;
            TrackedDisposable.disposedCount++;
            TrackedDisposable.items.delete(this);
        }
    }

    static from(...disposables: Disposable[]): Disposable {
        return new Disposable(() => {
            for (const disposable of disposables) {
                disposable.dispose();
            }
        });
    }

    /* These methods are used for testing only. */

    static getStats(): { constructed: number; disposed: number } {
        return {
            constructed: this.instanceCount,
            disposed: this.disposedCount,
        };
    }

    static printTraces(): void {
        const linesToExclude = ["vscode-mocks.ts", ".pnpm"];
        for (const item of this.items) {
            // eslint-disable-next-line no-console
            console.log(
                item.trace
                    ?.split("\n")
                    .filter((line) => linesToExclude.every((l) => !line.includes(l)))
                    .join("\n")
            );
            // eslint-disable-next-line no-console
            console.log("------------------------------");
        }
    }

    static resetStats(): void {
        this.instanceCount = 0;
        this.disposedCount = 0;
    }

    static assertDisposed(): void {
        if (this.instanceCount !== this.disposedCount) {
            throw new Error(
                `Disposable leak detected: ${this.instanceCount - this.disposedCount} undisposed (constructed=${this.instanceCount}, disposed=${this.disposedCount})`
            );
        }
    }
}

function emptyDisposable(): Disposable {
    return { dispose: () => {} };
}

type EventListener<T> = (e: T) => any;

export class EventEmitter<T> {
    static eventType = "EventEmitter";
    private nextSubscriberId = 123;
    private subscribers!: Map<number, EventListener<T>>;

    constructor() {
        this._reset();
    }

    private subscribe(
        listener: EventListener<T>,
        _thisArgs?: any,
        _disposables?: Disposable[]
    ): Disposable {
        const subscriberId = this.nextSubscriberId;
        this.nextSubscriberId += 1;

        this.subscribers.set(subscriberId, listener);
        return new TrackedDisposable(() => {
            this.unsubscribe(subscriberId);
        });
    }

    private unsubscribe(subscriberId: number) {
        this.subscribers.delete(subscriberId);
    }

    public event = this.subscribe.bind(this);

    public fire(data: T) {
        for (const subscriber of this.subscribers.values()) {
            subscriber(data);
        }
    }

    public dispose() {
        this._reset();
    }

    private _reset() {
        this.subscribers = new Map<number, EventListener<T>>();
    }
}

export class CancellationError extends Error {
    /**
     * Creates a new cancellation error.
     */
    constructor() {
        super("Operation cancelled");
    }
}

export class CancellationToken {
    private _cancelled = false;
    private _cancellationEmitter = new EventEmitter<any>();

    public get isCancellationRequested(): boolean {
        return this._cancelled;
    }

    public get onCancellationRequested(): Event<any> {
        return this._cancellationEmitter.event;
    }

    public cancel() {
        this._cancelled = true;
        this._cancellationEmitter.fire(false);
    }

    public dispose() {
        this._cancellationEmitter.dispose();
    }
}

export class CancellationTokenSource implements Disposable {
    public readonly token = new CancellationToken();

    public cancel() {
        this.token.cancel();
    }

    // See implementation in:
    // https://github.com/microsoft/vscode/blob/59f2e398290bc0929cae6807ba4bff439bb40373/src/vs/base/common/cancellation.ts#L97
    public dispose(cancel: boolean = false) {
        if (cancel) {
            this.cancel();
        }
        this.token.dispose();
    }
}

export class MockWorkspaceFolder implements WorkspaceFolder {
    public readonly index = 0;
    public readonly uri: Uri;

    constructor(public readonly name: string) {
        this.uri = Uri.file(name);
    }
}

export class RelativePattern {
    public workspaceFolder: WorkspaceFolder;

    constructor(
        base: string | Uri | WorkspaceFolder,
        public pattern: string
    ) {
        if (Uri.isUri(base)) {
            this.workspaceFolder = new MockWorkspaceFolder(base.path);
        } else if (typeof base === "string") {
            this.workspaceFolder = new MockWorkspaceFolder(base);
        } else {
            this.workspaceFolder = base;
        }
    }

    public get baseUri(): Uri {
        return this.workspaceFolder.uri;
    }

    public get path(): string {
        return this.baseUri.path;
    }
}

export type GlobPattern = string | RelativePattern;

class GlobPatternMatcher {
    private base: string;
    private pattern: string;
    private globPattern: boolean;

    constructor(globPattern: string | RelativePattern) {
        if (globPattern instanceof RelativePattern) {
            this.globPattern = true;
            this.base = globPattern.path;
            this.pattern = globPattern.pattern;
        } else if (typeof globPattern === "string") {
            this.globPattern = false;
            this.base = "";
            this.pattern = globPattern;
        } else {
            throw new Error("globPattern is neither string nor RelativePattern");
        }

        if (this.pattern.includes("*") && this.pattern !== "**/*") {
            throw new Error(
                "Sorry, GlobPatternMatcher only accepts **/* or specific explicit paths"
            );
        }
    }

    public accepts(path: string): boolean {
        if (this.base + "/" + this.pattern === path) {
            return true;
        }
        return path.startsWith(this.base) && this.globPattern;
    }
}

export class FileSystemWatcher implements Disposable, FSSubscriber {
    private matcher: GlobPatternMatcher;

    private notifyOnCreate: boolean;
    private notifyOnChange: boolean;
    private notifyOnDelete: boolean;

    private onDidCreateEmitter = new EventEmitter<Uri>();
    private onDidChangeEmitter = new EventEmitter<Uri>();
    private onDidDeleteEmitter = new EventEmitter<Uri>();

    public onDidCreate = this.onDidCreateEmitter.event;
    public onDidChange = this.onDidChangeEmitter.event;
    public onDidDelete = this.onDidDeleteEmitter.event;

    private _fsWatcher: FSWatcher;

    constructor(
        globPattern: GlobPattern,
        ignoreCreateEvents: boolean = false,
        ignoreChangeEvents: boolean = false,
        ignoreDeleteEvents: boolean = false,
        subscribe: (subscriber: FSSubscriber) => FSWatcher
    ) {
        this.matcher = new GlobPatternMatcher(globPattern);
        this.notifyOnCreate = !ignoreCreateEvents;
        this.notifyOnChange = !ignoreChangeEvents;
        this.notifyOnDelete = !ignoreDeleteEvents;

        this._fsWatcher = subscribe(this);
    }

    public dispose() {
        this._fsWatcher.cancel();
    }

    // FSSubscriber
    public onCreate(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        if (this.notifyOnCreate && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
            this.onDidCreateEmitter.fire(pathOrUri);
        }
    }

    // FSSubscriber
    public onChange(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        if (this.notifyOnChange && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
            this.onDidChangeEmitter.fire(pathOrUri);
        }
    }

    // FSSubscriber
    public onDelete(pathOrUri: string | Uri): void {
        if (typeof pathOrUri === "string") {
            pathOrUri = Uri.file(pathOrUri);
        }
        if (this.notifyOnDelete && this.matcher.accepts(uriToAbsPath(pathOrUri))) {
            this.onDidDeleteEmitter.fire(pathOrUri);
        }
    }
}

export class VSCodeFS {
    createFileSystemWatcher(
        globPattern: GlobPattern,
        ignoreCreateEvents?: boolean,
        ignoreChangeEvents?: boolean,
        ignoreDeleteEvents?: boolean
    ): FileSystemWatcher {
        const subscribe = (subscriber: FSSubscriber): FSWatcher => {
            return mockFSUtils.createWatcher(subscriber);
        };
        return new FileSystemWatcher(
            globPattern,
            ignoreCreateEvents,
            ignoreChangeEvents,
            ignoreDeleteEvents,
            subscribe
        );
    }
}

export enum StatusBarAlignment {
    Left = 1,
    Right = 2,
}

export enum QuickPickItemKind {
    Separator = -1,
    Default = 0,
}

export class StatusBarItem {
    readonly id: string = "some status bar item";
    readonly alignment: StatusBarAlignment = StatusBarAlignment.Left;
    readonly priority: number | undefined;
    name: string | undefined;
    text: string = "";
    tooltip: any;
    color: any;
    backgroundColor: any;
    command: any;
    accessibilityInformation: any;

    show(): void {}
    hide(): void {}
    dispose(): void {}
}

export namespace extensions {
    export function getExtension(_extensionid: string): Extension<any> | undefined {
        return {
            id: _extensionid,
            isActive: true,
            extensionUri: Uri.file("/no/extension/uri"),
            extensionKind: 1,
            activate: () => Promise.resolve(),
            exports: null,
            extensionPath: "/no/extension/path",
            packageJSON: {
                version: "0.5.0",
            },
        };
    }

    export const onDidChange: Event<void> = jest.fn(emptyDisposable);
}

export const mockQuickPick = {
    value: "testQuickPick",
    items: [{ label: "testQuickPick" }],
    selectedItems: [{ label: "testQuickPick" }],
    onDidTriggerItemButton: jest.fn(),
    onDidAccept: jest.fn(),
    onDidHide: jest.fn(),
    dispose: jest.fn(),
    show: jest.fn(),
};

export class TabInputText {
    public uri: Uri;

    constructor(uri: Uri) {
        this.uri = uri;
    }
}

export class TabInputTextDiff {}

export class Tab {
    public isActive: boolean = false;
    public viewColumn: ViewColumn = ViewColumn.Active;
    public activeEditor: TextEditor | undefined;
    public document: TextDocument | undefined;
    public input: TabInputText | undefined;
}

export class TabGroup {
    public isActive: boolean = false;
    public viewColumn: ViewColumn = ViewColumn.Active;
    public activeTab: Tab | undefined;
    public tabs: Tab[] = [];
}

export class TabGroups {
    public all: TabGroup[] = [];
    public onDidChangeTabGroups: Event<TabGroup[]> = jest.fn();
    public activeTabGroup: Event<TabGroup> = jest.fn();
    public onDidChangeTabs: Event<Tab[]> = jest.fn(() => {
        return new Disposable(() => {});
    });
    public close: (
        tab: Tab | readonly Tab[],
        preserveFocus?: boolean | undefined
    ) => Thenable<boolean> = jest.fn();
}

export const mockTabGroups = new TabGroups();

export function addTabGroup(tabGroup: TabGroup) {
    mockTabGroups.all.push(tabGroup);
}

export namespace window {
    export const activeColorTheme: ColorTheme = { kind: ColorThemeKind.Dark };

    export function createStatusBarItem(
        _alignment?: StatusBarAlignment,
        _priority?: number
    ): StatusBarItem {
        return new StatusBarItem();
    }

    export function withProgress(
        options: ProgressOptions,
        task: (
            progress: Progress<{ message?: string; increment?: number }>,
            token: CancellationToken
        ) => Thenable<any>
    ): Thenable<any> {
        const mockProgress: Progress<{ message?: string }> = {
            report: (_value) => {
                // We do not actually report progress here
            },
        };

        const mockCancellationToken = new CancellationToken();

        // Directly execute the callback with the mock objects
        return task(mockProgress, mockCancellationToken);
    }

    export const showErrorMessage = jest.fn(
        (_message: string, ..._items: string[]): Thenable<string | undefined> => {
            return Promise.resolve(undefined);
        }
    );

    export function showWarningMessage(
        _message: string,
        ..._items: string[]
    ): Thenable<string | undefined> {
        return Promise.resolve(undefined);
    }

    export function showInformationMessage(
        _message: string,
        ..._items: string[]
    ): Thenable<string | undefined> {
        return Promise.resolve(undefined);
    }

    export function createOutputChannel(name: string): OutputChannel {
        return {
            name: name,
            append: jest.fn(),
            appendLine: jest.fn(),
            clear: jest.fn(),
            show: jest.fn(),
            hide: jest.fn(),
            dispose: jest.fn(),
            replace: jest.fn(),
        };
    }

    export function showQuickPick<T extends QuickPickItem>(
        _items: readonly T[] | Thenable<readonly T[]>,
        _options?: QuickPickOptions,
        _token?: CancellationToken
    ): Thenable<T | undefined> {
        return new Promise<T | undefined>((resolve) => {
            resolve(undefined);
        });
    }

    export function createQuickPick() {
        return mockQuickPick;
    }

    // We expose this so that we can verify that we dispose of everything.
    export let textEditorDecorationTypes: any[] = [];

    export function createTextEditorDecorationType(decorationOptions: any): any {
        textEditorDecorationTypes.push(decorationOptions);
        return {
            dispose: () => {
                textEditorDecorationTypes = textEditorDecorationTypes.filter(
                    (options) => options !== decorationOptions
                );
            },
        };
    }

    export const activeTextEditorChanged = new EventEmitter<TextEditor | undefined>();
    export const onDidChangeActiveTextEditor = activeTextEditorChanged.event;
    export const windowStateChanged = new EventEmitter<WindowState | undefined>();
    export const onDidChangeWindowState = windowStateChanged.event;
    export const changeTextEditorSelection = new EventEmitter<TextEditorSelectionChangeEvent>();
    export const onDidChangeTextEditorSelection = changeTextEditorSelection.event;
    export const changeTextEditorVisibleRanges =
        new EventEmitter<TextEditorVisibleRangesChangeEvent>();
    export const onDidChangeTextEditorVisibleRanges = changeTextEditorVisibleRanges.event;
    export const onDidCloseTerminal = jest.fn().mockReturnValue(emptyDisposable());

    export let activeNotebookEditor: NotebookEditor | undefined = undefined;

    export let activeTextEditor: TextEditor | undefined = undefined;

    export let visibleTextEditors: TextEditor[] = [];

    export let tabGroups = mockTabGroups;

    export async function showTextDocument(doc: TextDocument, options?: TextDocumentShowOptions) {
        if (activeTextEditor && activeTextEditor.document === doc) {
            return;
        }
        let editor = visibleTextEditors.find((editor) => {
            return editor.document === doc;
        });
        if (!editor) {
            editor = new TextEditor(doc);
            visibleTextEditors.push(editor);
        }
        activeTextEditor = editor;
        publishActiveTextEditorChange(editor);

        if (options?.selection) {
            const selection = new Selection(options.selection.start, options.selection.end);
            editor.selections = [selection];
            publishTextEditorSelectionChange({
                textEditor: editor,
                selections: [selection],
                kind: TextEditorSelectionChangeKind.Command,
            });
        }
    }

    export function createWebviewPanel(): WebviewPanel {
        return new MockWebviewPanel();
    }

    export function registerWebviewViewProvider(_viewType: string, _provider: any) {
        return emptyDisposable();
    }

    export function onDidChangeVisibleTextEditors(
        _listener: (editors: TextEditor[]) => any,
        _thisArgs?: any,
        _disposables?: Disposable[]
    ): Disposable {
        return emptyDisposable();
    }

    export const showOpenDialog = jest.fn(
        (_opts: OpenDialogOptions): Thenable<Uri[] | undefined> => {
            return Promise.resolve([Uri.file("/some/path")]);
        }
    );
}

export function publishActiveTextEditorChange(editor: TextEditor) {
    window.activeTextEditorChanged.fire(editor);
}

export function publishTextEditorSelectionChange(changeEvent: TextEditorSelectionChangeEvent) {
    window.changeTextEditorSelection.fire(changeEvent);
}

export namespace workspace {
    let fs = new VSCodeFS();
    let configurationChanged = new EventEmitter<ConfigurationChangeEvent>();
    export const textDocumentSaved = new EventEmitter<TextDocument>();
    export const textDocumentChanged = new EventEmitter<TextDocumentChangeEvent>();
    export const notebookDocumentChanged = new EventEmitter<NotebookDocumentChangeEvent>();
    export const workspaceFoldersChanged = new EventEmitter<WorkspaceFoldersChangeEvent>();
    export const openTextDocument1 = new EventEmitter<TextDocument>();
    export const closeTextDocument = new EventEmitter<TextDocument>();
    export let workspaceFolders: readonly WorkspaceFolder[] = [];
    export let textDocuments: TextDocument[] = [];
    export let notebookDocuments: readonly NotebookDocument[] = [];
    export let textDocumentLoads = new Map<string, Promise<TextDocument>>();
    export let handleMissingTextDocument: ((uri: Uri) => Promise<TextDocument>) | undefined =
        undefined;
    export let onDidChangeConfiguration = configurationChanged.event;
    export const getConfiguration = jest.fn((key) => {
        if (key === "augment") {
            return generateMockWorkspaceConfig(getExampleUserConfig());
        }
        return generateMockWorkspaceConfig();
    });

    export const onWillSaveTextDocument = jest.fn().mockReturnValue(emptyDisposable());
    export const onWillRenameFiles = jest.fn().mockReturnValue(emptyDisposable());
    export const onDidRenameFiles = jest.fn().mockReturnValue(emptyDisposable());
    export const onDidSaveTextDocument = textDocumentSaved.event;
    export const onDidChangeTextDocument = textDocumentChanged.event;
    export const onDidChangeNotebookDocument = notebookDocumentChanged.event;
    export const onDidChangeTextEditorSelection = jest.fn().mockReturnValue(emptyDisposable());
    export const onDidChangeWorkspaceFolders = workspaceFoldersChanged.event;
    export const onDidCloseNotebookDocument = jest.fn().mockReturnValue(emptyDisposable());
    export const registerTextDocumentContentProvider = jest.fn().mockReturnValue(emptyDisposable());

    export const onDidOpenTextDocument = openTextDocument1.event;
    export const onDidCloseTextDocument = closeTextDocument.event;

    export function applyEdit(edit: WorkspaceEdit): Thenable<boolean> {
        const changeEvents = new Array<TextDocumentChangeEvent>();
        for (const [uri, changes] of edit.entries()) {
            const document = workspace.textDocuments.find((doc) => doc.uri.fsPath === uri.fsPath);
            if (document === undefined) {
                throw new Error(`No document found for ${uri.toString()}`);
            }
            if (!(document instanceof MutableTextDocument)) {
                throw new Error(`Document ${uri.toString()} is not mutable`);
            }
            for (const change of changes) {
                changeEvents.push(document.applyEdit(change));
            }
        }
        for (const event of changeEvents) {
            publishTextDocumentChange(event);
        }
        return Promise.resolve(true);
    }

    let workspaceIndex = 0;
    export function getWorkspaceFolder(uri: Uri): WorkspaceFolder {
        return {
            uri: uri,
            name: uri.path,
            index: workspaceIndex++,
        };
    }

    export function openTextDocument(uri: Uri | string): Thenable<TextDocument> {
        if (typeof uri === "string") {
            uri = Uri.file(uri);
        }
        // Document already opened
        for (const document of textDocuments) {
            if (document.uri.toString() === uri.toString()) {
                return Promise.resolve(document);
            }
        }

        // Test-injected async document opens
        let future = textDocumentLoads.get(uri.toString());
        if (future !== undefined) {
            return future;
        }
        if (handleMissingTextDocument !== undefined) {
            let uriKey = uri.toString();
            let future = handleMissingTextDocument(uri).then(
                (document) => {
                    textDocumentLoads.delete(uriKey);
                    addTextDocument(document);
                    publishOpenTextDocument(document);
                    return document;
                },
                (err) => {
                    textDocumentLoads.delete(uriKey);
                    throw err;
                }
            );
            textDocumentLoads.set(uriKey, future);
            return future;
        }

        // Unknown resource
        throw new Error(`No document found for ${uri.toString()}`);
    }

    export function createFileSystemWatcher(
        globPattern: GlobPattern,
        ignoreCreateEvents?: boolean,
        ignoreChangeEvents?: boolean,
        ignoreDeleteEvents?: boolean
    ): FileSystemWatcher {
        return fs.createFileSystemWatcher(
            globPattern,
            ignoreCreateEvents,
            ignoreChangeEvents,
            ignoreDeleteEvents
        );
    }

    /**
     * Not part of vscode API
     */

    export function reset() {
        configurationChanged = new EventEmitter<ConfigurationChangeEvent>();
        onDidChangeConfiguration = configurationChanged.event;
        fs = new VSCodeFS();
        workspaceFolders = [];
        textDocuments = [];
        textDocumentLoads.clear();
        notebookDocuments = [];
    }

    export function mockConfigurationChange(newConfig?: WorkspaceConfiguration) {
        if (newConfig) {
            workspace.getConfiguration.mockReturnValueOnce(newConfig);
        }
        configurationChanged.fire({ affectsConfiguration: jest.fn() });
    }

    export function setWorkspaceFolders(newWorkspaceFolders: Array<WorkspaceFolder>) {
        const added = new Array<WorkspaceFolder>();
        const removed = new Array<WorkspaceFolder>();
        for (const newFolder of newWorkspaceFolders) {
            let idx: number;
            for (idx = 0; idx < workspace.workspaceFolders.length; idx++) {
                if (newFolder.uri.path === workspace.workspaceFolders[idx].uri.path) {
                    break;
                }
            }
            if (idx === workspace.workspaceFolders.length) {
                added.push(newFolder);
            }
        }
        for (const folder of workspace.workspaceFolders) {
            let idx: number;
            for (idx = 0; idx < newWorkspaceFolders.length; idx++) {
                if (folder.uri.path === newWorkspaceFolders[idx].uri.path) {
                    break;
                }
            }
            if (idx === newWorkspaceFolders.length) {
                removed.push(folder);
            }
        }

        workspace.workspaceFolders = newWorkspaceFolders;
        workspaceFoldersChanged.fire({ added, removed });
    }
}

export function resetMockWorkspace() {
    workspace.reset();
}

export function getWorkspaceFolder(uri: Uri): WorkspaceFolder {
    return workspace.getWorkspaceFolder(uri);
}

export function setWorkspaceFolders(folderRoots: readonly string[]) {
    const newWorkspaceFolders = folderRoots.map((folderRoot) =>
        getWorkspaceFolder(Uri.file(folderRoot))
    );
    workspace.setWorkspaceFolders(newWorkspaceFolders);
}

export function publishWorkspaceFoldersChange(newWorkspaceFolders: WorkspaceFolder[]) {
    workspace.setWorkspaceFolders(newWorkspaceFolders);
}

export function mockWorkspaceConfigChange(newConfig: WorkspaceConfiguration) {
    workspace.mockConfigurationChange(newConfig);
}

export function publishTextDocumentSaved(document: TextDocument) {
    workspace.textDocumentSaved.fire(document);
}

export function publishTextDocumentChange(event: TextDocumentChangeEvent) {
    workspace.textDocumentChanged.fire(event);
}

export function publishNotebookDocumentChange(event: NotebookDocumentChangeEvent) {
    workspace.notebookDocumentChanged.fire(event);
}

export function publishOpenTextDocument(document: TextDocument) {
    workspace.openTextDocument1.fire(document);
}

export function publishCloseTextDocument(document: TextDocument) {
    workspace.closeTextDocument.fire(document);
}

export interface FileRenameEvent {
    files: { oldUri: Uri; newUri: Uri }[];
}

export function publishFileRename(oldUri: Uri, newUri: Uri) {
    const event: FileRenameEvent = {
        files: [{ oldUri, newUri }],
    };
    const callback = workspace.onDidRenameFiles;
    const listeners = callback.mock.calls.map((call) => call[0]);
    listeners.forEach((listener) => listener(event));
}

export function publishFileDelete(uri: Uri) {
    // Notify all file system watchers
    mockFSUtils.publishFSEvent(MockFileSystem.FSEvent.delete, uri);
}

export function addTextDocument(document: TextDocument) {
    workspace.textDocuments.push(document);
}

export function setOpenTextDocuments(documents: TextDocument[]) {
    workspace.textDocuments = documents;
}

export function setHandleMissingTextDocument(handler: (uri: Uri) => Promise<TextDocument>) {
    workspace.handleMissingTextDocument = handler;
}

export enum ProgressLocation {
    SourceControl = 1,
    Window = 10,
    Notification = 15,
}

export enum ConfigurationTarget {
    Global = 1,
    Workspace = 2,
    WorkspaceFolder = 3,
}

namespace detail {
    // InlineCompletionProviderRegistry maintains a mock registry of inline
    // completion proviers.
    export class InlineCompletionProviderRegistry {
        private _providerId = 100;
        public readonly registry = new Map<number, string>();

        public add(language: string | string[]): Disposable {
            const languages = Array.isArray(language) ? language : [language];
            const providerIds: number[] = [];
            for (const lang of languages) {
                const providerId = this._providerId++;
                this.registry.set(providerId, lang);
                providerIds.push(providerId);
            }
            return new Disposable(() => {
                for (const providerId of providerIds) {
                    this.registry.delete(providerId);
                }
            });
        }
    }

    // CompletionProviderRegistry maintains a mock registry of completion
    // providers.
    export class CompletionProviderRegistry {
        private _providerId = 100;
        public readonly registry = new Map<number, string>();

        public add(language: string | string[]): Disposable {
            const languages = Array.isArray(language) ? language : [language];
            const providerIds: number[] = [];
            for (const lang of languages) {
                const providerId = this._providerId++;
                this.registry.set(providerId, lang);
                providerIds.push(providerId);
            }
            return new Disposable(() => {
                for (const providerId of providerIds) {
                    this.registry.delete(providerId);
                }
            });
        }
    }

    // CodeLensProviderRegistry maintains a mock registry of code
    // lens proviers.
    export class CodeLensProviderRegistry {
        public readonly registry = new Map<string, number>();

        public add(providerName: string): Disposable {
            const providerCount = this.registry.get(providerName) || 0;
            this.registry.set(providerName, providerCount + 1);

            return new Disposable(() => {
                const providerCountBeforeDisposal = this.registry.get(providerName) || 1;
                this.registry.set(providerName, providerCountBeforeDisposal - 1);
            });
        }
    }

    // CodeLensProviderRegistry maintains a mock registry of code
    // lens proviers.
    export class HoverProviderRegistry {
        public readonly registry = new Map<string, HoverProvider>();

        public add(provider: HoverProvider): Disposable {
            const providerName = provider.constructor.name;
            this.registry.set(providerName, provider);

            return new Disposable(() => {
                this.registry.delete(providerName);
            });
        }
    }
}

export const inlineCompletionProviderRegistry = new detail.InlineCompletionProviderRegistry();
export const completionProviderRegistry = new detail.CompletionProviderRegistry();
export const codeLensProviderRegistry = new detail.CodeLensProviderRegistry();
export const hoverProviderRegistry = new detail.HoverProviderRegistry();

export namespace languages {
    export function registerInlineCompletionItemProvider(
        selector: DocumentSelector | string,
        _provider: any
    ): Disposable {
        // check if selector is a list
        const selectorArray = Array.isArray(selector) ? selector : [selector];
        const languages: string[] = [];
        for (const sel of selectorArray) {
            const language: string = typeof sel === "string" ? sel : sel.language;
            expect(language).toBeDefined();
            languages.push(language);
        }
        return inlineCompletionProviderRegistry.add(languages);
    }

    export function registerCompletionItemProvider(
        selector: DocumentSelector,
        _provider: any,
        ..._triggerCharacters: string[]
    ): Disposable {
        // check if selector is a list
        const selectorArray = Array.isArray(selector) ? selector : [selector];
        const languages: string[] = [];
        for (const sel of selectorArray) {
            const language: string = typeof sel === "string" ? sel : sel.language;
            expect(language).toBeDefined();
            languages.push(language);
        }
        return completionProviderRegistry.add(languages);
    }

    export function registerCodeLensProvider(
        _selector: DocumentSelector | string,
        provider: any
    ): Disposable {
        return codeLensProviderRegistry.add(provider.constructor.name);
    }

    export function registerCodeActionsProvider(
        _selector: DocumentSelector,
        _provider: any,
        _metadata: CodeActionProviderMetadata
    ): Disposable {
        return emptyDisposable();
    }

    export function registerHoverProvider(_selector: DocumentSelector, _provider: HoverProvider) {
        return hoverProviderRegistry.add(_provider);
    }

    export const diagnosticsChanged = new EventEmitter<DiagnosticChangeEvent>();

    export const onDidChangeDiagnostics = diagnosticsChanged.event;

    export function createDiagnosticCollection(_name?: string) {
        return new DiagnosticCollection();
    }

    export function getDiagnostics(_resource: Uri): Diagnostic[] {
        return [];
    }

    /***
     * Not part of the VSCode API
     */
    export function reset() {
        inlineCompletionProviderRegistry.registry.clear();
        completionProviderRegistry.registry.clear();
        codeLensProviderRegistry.registry.clear();
        hoverProviderRegistry.registry.clear();
    }
}

export enum ExtensionMode {
    Production = 1,
    Development = 2,
    Test = 3,
}

// This class has the same interfaces as vscode.ExtensionContext.globalState
// and is used to mock the global state storage.
// Uses singleton storage to reproduce the storage behavior.
export class Memento {
    // Pretend we have a Memento storage that really isn't persistent
    private static _values: Map<string, any> = new Map();
    public keys(): string[] {
        return Array.from(Memento._values.keys());
    }

    public setKeysForSync(_keys: readonly string[]): void {
        // Do nothing
    }

    public get<T>(key: string): T | undefined {
        return Memento._values.get(key);
    }

    public update(key: string, value: any): Thenable<void> {
        return new Promise<void>((resolve, reject) => {
            try {
                Memento._values.set(key, value);
                resolve();
            } catch (e) {
                reject(e);
            }
        });
    }

    public static resetValues(): void {
        Memento._values.clear();
    }
}

// This class has the same interfaces as vscode.ExtensionContext.SecretStorage
// and is used to mock the global secret storage.
// Uses singleton storage to reproduce the storage behavior.
export class SecretStorage {
    private static _values: Map<string, string> = new Map();
    private _onDidChange = new EventEmitter<SecretStorageChangeEvent>();

    get(key: string): Thenable<string | undefined> {
        return Promise.resolve(SecretStorage._values.get(key));
    }

    store(key: string, value: string): Thenable<void> {
        SecretStorage._values.set(key, value);
        this._onDidChange.fire({ key });
        return Promise.resolve();
    }

    delete(key: string): Thenable<void> {
        SecretStorage._values.delete(key);
        this._onDidChange.fire({ key });
        return Promise.resolve();
    }

    get onDidChange(): Event<SecretStorageChangeEvent> {
        return this._onDidChange.event;
    }

    public static resetValues(): void {
        SecretStorage._values.clear();
    }
}

export class ExtensionContext {
    readonly subscriptions: { dispose(): any }[] = [];
    readonly extensionUri: Uri = Uri.file("/no/extension/uri");

    constructor(
        readonly workspaceState: Memento = new Memento(),
        readonly globalState: Memento = new Memento(),
        readonly secrets: SecretStorage = new SecretStorage()
    ) {}

    get extensionPath() {
        return this.extensionUri.fsPath;
    }

    get environmentVariableCollection(): any {
        throw new Error("not implemented");
    }

    asAbsolutePath(_relPath: string): string {
        throw new Error("not implemented");
    }

    readonly storageUri: Uri = Uri.file("/no/storage/uri");

    get storagePath(): string | undefined {
        return this.storageUri.fsPath;
    }

    readonly globalStorageUri: Uri = Uri.file("/no/global/storage/uri");

    get globalStoragePath(): string {
        return this.globalStorageUri.fsPath;
    }

    readonly logUri: Uri = Uri.file("/no/log/uri");

    get logPath(): string {
        return this.logUri.fsPath;
    }

    readonly extensionMode = ExtensionMode.Production;

    get extension(): Extension<any> {
        return {
            id: "example-extension.example",
            extensionPath: this.extensionPath,
            extensionUri: this.extensionUri,
            isActive: true,
            exports: null,
            packageJSON: {
                contributes: {
                    commands: [
                        {
                            category: "Example",
                            title: "Example",
                            command: "vscode-augment.example",
                        },
                    ],
                },
            },
            extensionKind: 1,
            activate: () => Promise.resolve(),
        };
    }
}

export enum InlineCompletionTriggerKind {
    /**
     * Completion was triggered explicitly by a user gesture.
     * Return multiple completion items to enable cycling through them.
     */
    Invoke = 0,

    /**
     * Completion was triggered automatically while editing.
     * It is sufficient to return a single completion item in this case.
     */
    Automatic = 1,
}

export enum CompletionTriggerKind {
    /**
     * Completion was triggered normally.
     */
    Invoke = 0,
    /**
     * Completion was triggered by a trigger character.
     */
    TriggerCharacter = 1,
    /**
     * Completion was re-triggered as current completion list is incomplete
     */
    TriggerForIncompleteCompletions = 2,
}

export class InlineCompletionItem {
    filterText?: string;
    /**
     * Creates a new inline completion item.
     *
     * @param insertText The text to replace the range with.
     * @param range The range to replace. If not set, the word at the requested position will be used.
     * @param command An optional {@link Command} that is executed *after* inserting this completion.
     */
    constructor(
        public insertText: string,
        public range?: Range,
        public command?: Command
    ) {}
}

export class TextEdit {
    newEol?: EndOfLine;

    constructor(
        public range: Range,
        public newText: string
    ) {}
}

export enum CompletionItemKind {
    Text = 0,
    Method = 1,
    Function = 2,
    Constructor = 3,
    Field = 4,
    Variable = 5,
    Class = 6,
    Interface = 7,
    Module = 8,
    Property = 9,
    Unit = 10,
    Value = 11,
    Enum = 12,
    Keyword = 13,
    Snippet = 14,
    Color = 15,
    Reference = 17,
    File = 16,
    Folder = 18,
    EnumMember = 19,
    Constant = 20,
    Struct = 21,
    Event = 22,
    Operator = 23,
    TypeParameter = 24,
    User = 25,
    Issue = 26,
}

export interface CompletionItemLabel {
    label: string;
    detail?: string;
    description?: string;
}

export class CompletionItem {
    additionalTextEdits?: TextEdit[];
    command?: Command;
    commitCharacters?: string[];
    detail?: string;
    documentation?: string;
    filterText?: string;
    insertText?: string;
    keepWhitespace?: boolean;
    kind?: CompletionItemKind;
    range?: Range;
    sortText?: string;
    textEdit?: TextEdit;

    constructor(public label: string | CompletionItemLabel) {}
}

export function openTextDocument(uri: Uri): MutableTextDocument {
    const content = mockFSUtils.readFileUtf8(uri.fsPath);
    return new MutableTextDocument(uri, content);
}

export namespace env {
    export const remoteName: string | undefined = undefined;
    export const uiKind: UIKind = UIKind.Desktop;
    export const uriScheme: string = "vscode";
    export const machineId: string = "mock-machine-id";

    export function openExternal(_uri: Uri): Thenable<boolean> {
        return Promise.resolve(true);
    }

    export namespace clipboard {
        export function writeText(_value: string): Thenable<void> {
            return Promise.resolve();
        }
    }
}

export namespace commands {
    let registeredCommands: { [key: string]: (...args: any[]) => any } = {
        "editor.action.showHover": () => {
            if (window.activeTextEditor) {
                const editor = window.activeTextEditor;
                hoverProviderRegistry.registry.forEach((provider) =>
                    provider.provideHover(
                        editor.document,
                        editor.selection.active,
                        new CancellationToken()
                    )
                );
            }
        },
        // NOTE(arun): This command changes the focus to the active editor group. We
        // don't currently track the focus state and so have nothing to do here.
        "workbench.action.focusActiveEditorGroup": () => jest.fn(),
        editorScroll: () => jest.fn(),
        setContext: (key, value) => {
            contextKeys[key] = value;
        },
    };

    export function registerCommand(name: string, callback: (...args: any[]) => any): Disposable {
        registeredCommands[name] = callback;
        return new Disposable(() => {
            delete registeredCommands[name];
        });
    }

    export function executeCommand<T>(command: string, ...rest: any[]): Thenable<T> {
        if (registeredCommands[command]) {
            return Promise.resolve(registeredCommands[command](...rest));
        }
        return new Promise(() => {});
    }

    /***
     * Not part of the VSCode API
     */
    export function reset() {
        registeredCommands = {};
    }
}

export namespace debug {
    export const onDidStartDebugSession: Event<DebugSession> = jest.fn(emptyDisposable);
    export const onDidTerminateDebugSession: Event<DebugSession> = jest.fn(emptyDisposable);

    export let activeDebugSession: DebugSession | undefined = undefined;
}

export class DebugSession {
    constructor(public readonly id: string) {}
}

export class NotebookEditor {
    constructor(public readonly notebook: NotebookDocument) {}
}

export class NotebookDocument {
    protected _version = 0;
    protected _savedVersion = 0;
    protected _cells: NotebookCell[] = [];

    constructor(public readonly uri: Uri) {}

    public get notebookType(): string {
        return "jupyter-notebook";
    }

    public get version(): number {
        return this._version;
    }

    public get isDirty(): boolean {
        return this._version !== this._savedVersion;
    }

    public get isUntitled(): boolean {
        return true;
    }

    public get isClosed(): boolean {
        return false;
    }

    public get metadata(): { [key: string]: any } {
        return {};
    }

    public get cellCount(): number {
        return this._cells.length;
    }

    public cellAt(index: number): NotebookCell {
        return this._cells[index];
    }

    public getCells(): NotebookCell[] {
        return this._cells;
    }

    public save(): Thenable<boolean> {
        this._savedVersion = this._version;
        return Promise.resolve(true);
    }

    /***
     * Not part of the VSCode API
     */
    public addCells(index: number, cells: NotebookCell[]): NotebookDocumentChangeEvent {
        this._cells.splice(index, 0, ...cells);
        return new NotebookDocumentChangeEvent(
            this,
            [new NotebookDocumentContentChange(new NotebookRange(index, index), cells, [])],
            []
        );
    }

    public appendCells(cells: NotebookCell[]): NotebookDocumentChangeEvent {
        return this.addCells(this.cellCount, cells);
    }

    public deleteCells(start: number, end: number): NotebookDocumentChangeEvent {
        const cells = this._cells.splice(start, end - start);
        return new NotebookDocumentChangeEvent(
            this,
            [new NotebookDocumentContentChange(new NotebookRange(start, end), [], cells)],
            []
        );
    }
}

export class NotebookCell {
    constructor(
        public readonly index: number,
        public readonly notebook: NotebookDocument,
        public readonly kind: NotebookCellKind,
        public readonly document: TextDocument
    ) {}

    public get metadata(): { [key: string]: any } {
        return {};
    }

    public get outputs(): NotebookCellOutput[] {
        return [];
    }

    public get executionSummary(): undefined {
        return undefined;
    }
}

export enum NotebookCellKind {
    Markup = 1,
    Code = 2,
}

export class NotebookDocumentChangeEvent {
    constructor(
        public readonly notebook: NotebookDocument,
        public readonly contentChanges: readonly NotebookDocumentContentChange[],
        public readonly cellChanges: readonly NotebookDocumentCellChange[]
    ) {}

    public get metadata(): { [key: string]: any } {
        return {};
    }
}

export function emptyNotebookDocumentChangeEvent(
    notebook: NotebookDocument
): NotebookDocumentChangeEvent {
    return new NotebookDocumentChangeEvent(notebook, [], []);
}

export class NotebookDocumentContentChange {
    constructor(
        public readonly range: NotebookRange,
        public readonly addedCells: readonly NotebookCell[],
        public readonly removedCells: readonly NotebookCell[]
    ) {}
}

export class NotebookDocumentCellChange {
    constructor(
        public readonly cell: NotebookCell,
        public readonly document: TextDocument | undefined,
        public readonly outputs: readonly NotebookCellOutput[] | undefined,
        public readonly executionSummary: NotebookCellExecutionSummary | undefined
    ) {}

    public get metadata(): { [key: string]: any } {
        return {};
    }
}

export class NotebookRange {
    constructor(
        public readonly start: number,
        public readonly end: number
    ) {}

    public get isEmpty(): boolean {
        return this.start === this.end;
    }

    public with(change: { start?: number; end?: number }): NotebookRange {
        return new NotebookRange(change.start ?? this.start, change.end ?? this.end);
    }
}

export class NotebookCellExecutionSummary {
    constructor(
        public readonly executionOrder: number,
        public readonly success: boolean,
        public readonly timing: { readonly startTime: number; readonly endTime: number }
    ) {}
}

export function resetMockCommands() {
    commands.reset();
}

export class WebviewView {
    private _disposeEmitter: EventEmitter<void> = new EventEmitter<void>();
    private _onDidChangeVisibilityEmitter: EventEmitter<void> = new EventEmitter<void>();

    public title?: string;

    constructor(
        public readonly viewType: string,
        public readonly webview: Webview,
        public visible: boolean
    ) {}

    onDidDispose = this._disposeEmitter.event;

    onDidChangeVisibility = this._onDidChangeVisibilityEmitter.event;

    show(_preserveFocus?: boolean): void {}

    /* Internal commands */
    triggerVisibilityChange() {
        this._onDidChangeVisibilityEmitter.fire(undefined);
    }

    dispose() {
        this._disposeEmitter.fire(undefined);
    }
}

export class Webview {
    public _onDidReceiveListeners: Array<(message: any) => any> = [];

    constructor(
        public options: WebviewOptions,
        public html: string,
        public readonly cspSource: string
    ) {}

    onDidReceiveMessage(
        listener: (message: any) => any,
        _thisArgs?: any,
        _disposables?: Disposable[]
    ) {
        this._onDidReceiveListeners.push(listener);
        return new Disposable(() => {
            this._onDidReceiveListeners = this._onDidReceiveListeners.filter((l) => l !== listener);
        });
    }

    postMessage(_message: any): Thenable<boolean> {
        return Promise.resolve(true);
    }

    asWebviewUri(localResource: Uri): Uri {
        return localResource;
    }

    /* The following APIs are not part of the vscode API */
    fakeMessageFromWebview(message: any) {
        this._onDidReceiveListeners.forEach((listener) => {
            listener(message);
        });
    }
}

export class WebviewViewResolveContext<T = unknown> {
    public readonly state: T | undefined;
}

export interface CompletionContext {
    readonly triggerKind: CompletionTriggerKind;
    readonly triggerCharacter: string | undefined;
}

export class MockWebviewPanel {
    public readonly viewType: string = "";

    public title: string = "";

    public readonly cspSource: string = "example csp source";

    public iconPath?:
        | Uri
        | {
              /**
               * The icon path for the light theme.
               */
              readonly light: Uri;
              /**
               * The icon path for the dark theme.
               */
              readonly dark: Uri;
          };

    public readonly webview: Webview = new Webview(
        {
            enableScripts: true,
            localResourceRoots: [],
        },
        "",
        this.cspSource
    );

    public readonly options: WebviewPanelOptions = {};

    public viewColumn: ViewColumn | undefined;

    public readonly active: boolean = false;

    private _visible: boolean = false;
    private _isDisposed: boolean = false;

    public get visible(): boolean {
        return this._visible;
    }

    readonly onDidChangeViewState: Event<WebviewPanelOnDidChangeViewStateEvent> = jest.fn();

    private _disposeEmitter = new EventEmitter<any>();
    get onDidDispose(): Event<void> {
        return this._disposeEmitter.event;
    }

    reveal(_viewColumn?: ViewColumn, _preserveFocus?: boolean) {}

    dispose() {
        if (this._isDisposed) {
            return;
        }
        this._isDisposed = true;
        this._disposeEmitter.fire(undefined);
    }

    // These APIs are not part of the vscode API.

    setVisibile(_visible: boolean) {
        this._visible = true;
    }
}

export class MarkdownString {
    public isTrusted: boolean = false;
    constructor(public value: string) {}
}

export function newMockStatusBar(): StatusBarItem {
    return {
        id: "",
        name: "",
        alignment: StatusBarAlignment.Left,
        priority: 0,
        tooltip: "",
        text: "",
        backgroundColor: undefined,
        color: undefined,
        command: "",
        show: jest.fn(),
        hide: jest.fn(),
        dispose: jest.fn(),
        accessibilityInformation: undefined,
    };
}

export class MockTextEditorEdit implements TextEditorEdit {
    public replace = jest.fn(); // (location: Position | Range | Selection, value: string): void {}
    public insert = jest.fn(); //(location: Position, value: string): void {}
    public delete = jest.fn(); // (location: Range | Selection): void {}
    public setEndOfLine = jest.fn(); // (endOfLine: EndOfLine): void {}
}

export interface TextEditorSelectionChangeEvent {
    readonly textEditor: TextEditor;
    readonly selections: readonly Selection[];
    readonly kind: TextEditorSelectionChangeKind | undefined;
}

export enum TextEditorSelectionChangeKind {
    Keyboard = 1,
    Mouse = 2,
    Command = 3,
}

export class Diagnostic {
    constructor(
        public readonly range: Range,
        public readonly message: string,
        public readonly severity: DiagnosticSeverity
    ) {}
}

export class DiagnosticCollection {
    public set(_uri: Uri, _diagnostics: Diagnostic[] | undefined): void {}
    public clear(): void {}
}

export enum DiagnosticSeverity {
    Error = 0,
    Warning = 1,
    Information = 2,
    Hint = 3,
}

export enum OverviewRulerLane {
    Left = 1,
    Center = 2,
    Right = 4,
    Full = 7,
}

/** Custom Hover mock class because this is unimplemented in jest-vscode-mocks. */
export class Hover {
    /**
     * Creates a new hover object.
     *
     * @param contents The contents of the hover.
     * @param range The range to which the hover applies.
     */
    constructor(
        public contents: MarkdownString | Array<MarkdownString>,
        public range?: Range
    ) {}
}

export interface TextDocumentShowOptions {
    // Note that the other fields in this interface are missing here because
    // we have not implemented them here.

    selection?: Range;
}

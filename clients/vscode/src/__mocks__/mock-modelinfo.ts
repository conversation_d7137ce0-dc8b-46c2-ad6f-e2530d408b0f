import { BackGetModelsResult } from "../augment-api";

export const mockDefaultModelName = "aardvark";
export const mockDefaultModelPrefixLength = 123;
export const mockDefaultModelSuffixLength = 456;

export const mockGetModelsResult: BackGetModelsResult = {
    /* eslint-disable */
    default_model: mockDefaultModelName,
    max_upload_size_bytes: 100, // arbitrary
    models: [
        {
            name: mockDefaultModelName,
            suggested_prefix_char_count: mockDefaultModelPrefixLength,
            suggested_suffix_char_count: mockDefaultModelSuffixLength,
            max_upload_size_bytes: 789,
        },
        {
            name: "someOtherModel",
            suggested_prefix_char_count: 321,
            suggested_suffix_char_count: 654,
            max_upload_size_bytes: 0,
        },
    ],
    languages: [
        {
            name: "Python",
            vscode_name: "python",
            extensions: [".py", ".ipynb"],
        },
        {
            name: "C++",
            vscode_name: "cpp",
            extensions: [".cpp", ".cxx", ".C"],
        },
    ],
    /* eslint-enable */
};

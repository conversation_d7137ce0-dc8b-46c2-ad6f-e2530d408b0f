import { resetLibraryAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import { resetLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { resetLibraryClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { resetLibraryPluginFileStore } from "@augment-internal/sidecar-libs/src/client-interfaces/plugin-file-store";
import { resetLibraryWebviewMessaging } from "@augment-internal/sidecar-libs/src/client-interfaces/webview-messaging";
import { resetAgentSessionEventReporter } from "@augment-internal/sidecar-libs/src/metrics/agent-session-event-reporter";

import { TrackedDisposable } from "./vscode-mocks";

jest.mock("../logging");

beforeAll(() => {
    // TrackedDisposable is used in the  vscode-mocks for our
    // event emitters.  We reset the stats here so that each
    // suite starts a new.
    TrackedDisposable.resetStats();
});

afterAll(() => {
    // NOTE: If we can fix all the issues here, we should
    // switch this to TrackDisposable.assertDisposed() to
    // error when a test leaks disposables.
    const stats = TrackedDisposable.getStats();
    if (stats.constructed !== stats.disposed) {
        // eslint-disable-next-line no-console
        console.warn(
            `⚠️ Disposable Leak Warning: There may be a leak of ${
                stats.constructed - stats.disposed
            } undisposed disposabled in this test suite.`
        );
        if (process.env.LOG_LEVEL?.toLowerCase() === "verbose") {
            TrackedDisposable.printTraces();
        }
    }
});

afterEach(() => {
    resetAgentSessionEventReporter();
    resetLibraryClientFeatureFlags();
    resetLibraryClientWorkspaces();
    resetLibraryPluginFileStore();
    resetLibraryAPIClient();
    resetLibraryWebviewMessaging();
});

import merge from "lodash/merge";
import type { WorkspaceConfiguration } from "vscode";

import {
    AugmentConfig,
    AugmentConfigListener,
    PartialRawSettings,
} from "../augment-config-listener";
import { mockDefaultModelName } from "./mock-modelinfo";

export function getExampleUserConfig(overrides?: PartialRawSettings): PartialRawSettings {
    return merge(
        {
            shortcutsDisplayDelayMS: 2000,
            enableEmptyFileHint: true,
            completions: {
                enableAutomaticCompletions: true,
                disableCompletionsByLanguage: [],
                enableQuickSuggestions: true,
            },
            chat: {
                userGuidelines: "",
            },
            conflictingCodingAssistantCheck: true,
            nextEdit: {
                enableBackgroundSuggestions: true,
                enableGlobalBackgroundSuggestions: false,
                highlightSuggestionsInTheEditor: true,
                enableAutoApply: true,
                showDiffInHover: false,
            },
            advanced: {
                apiToken: "example-api-token-1234abcd",
                completionURL: "http://api.augmentcode.com",
                model: mockDefaultModelName,
                codeInstruction: {
                    model: mockDefaultModelName,
                },
                chat: {
                    model: mockDefaultModelName,
                },
                autofix: {
                    enabled: undefined,
                    locationUrl: undefined,
                    autofixUrl: undefined,
                },
                oauth: {
                    clientID: "abcd1234",
                    url: "http://auth_api.augmecode.com",
                },
                enableWorkspaceUpload: true,
                enableDebugFeatures: true,
                enableReviewerWorkflows: true,
                completions: {
                    timeoutMs: 800,
                    maxWaitMs: 1600,
                    addIntelliSenseSuggestions: true,
                    filterThreshold: undefined,
                },
                openFileManager: {
                    v2Enabled: false,
                },
                enableDataCollection: false,
                nextEdit: {
                    url: undefined,
                    enabled: undefined,
                    backgroundEnabled: false,
                },
                recencySignalManager: {
                    collectTabSwitchEvents: false,
                },
                preferenceCollection: {
                    enable: false,
                    enableRetrievalDataCollection: false,
                    enableRandomizedMode: false,
                },
                vcs: {
                    watcherEnabled: false,
                },
                conflictingCodingAssistantCheck: true,
                smartPaste: {
                    url: undefined,
                    model: undefined,
                },
                instructions: {
                    model: undefined,
                },
                vscodeChatWithToolsMinVersion: "",
                vscodeAgentModeMinVersion: "",
                vscodeAgentModeMinStableVersion: "",
            },
        },
        overrides
    );
}

export function getExampleAugmenConfig(overrides?: DeepPartial<AugmentConfig>): AugmentConfig {
    const augmentConfig = AugmentConfigListener.normalizeConfig(
        AugmentConfigListener.parseSettings(generateMockWorkspaceConfig())
    );
    return merge(augmentConfig, overrides);
}

// Generate a mock workspace config for Augment's config
export function generateMockWorkspaceConfig(
    config: PartialRawSettings = {}
): WorkspaceConfiguration {
    return {
        ...config,
        get: jest.fn(),
        has: jest.fn(),
        inspect: jest.fn(),
        update: jest.fn(),
    };
}

// This function is useful if you want a workspace configuration for something other than Augments config.
// If you want Augment config - use generateMockWorkspaceConfig.
export function generateUncheckedMockWorkspaceConfig(config: Object = {}): WorkspaceConfiguration {
    return {
        ...config,
        get: jest.fn(),
        has: jest.fn(),
        inspect: jest.fn(),
        update: jest.fn(),
    };
}

type DeepPartial<T> = {
    [P in keyof T]?: DeepPartial<T[P]>;
};

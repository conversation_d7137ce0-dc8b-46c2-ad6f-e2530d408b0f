import { Uri } from "jest-mock-vscode/dist/vscode/uri";

import { FileType } from "../utils/types";

const separator = "/";

function toAbsolutePath(pathList: string[]): string {
    let absList = pathList;
    if (pathList.length === 0 || pathList[0] !== separator) {
        absList = [separator].concat(pathList);
    }
    return MockFileSystem.joinPath(absList);
}

export namespace MockFileSystem {
    type EntryXLator<T> = (
        name: string,
        type: FileType,
        size: number | undefined,
        mtime: number
    ) => T;

    export class InvalidPathNameException extends Error {
        constructor(pathName: string) {
            super(`${pathName}: invalid path name`);
        }
    }

    export class FileNotFoundException extends Error {
        constructor(pathName: string) {
            super(`${pathName}: file not found`);
        }
    }

    export class FileExistsException extends Error {
        constructor(pathName: string) {
            super(`${pathName}: file already exists`);
        }
    }

    export class IsADirectoryException extends Error {
        constructor(pathName: string) {
            super(`${pathName}: is a directory`);
        }
    }

    export class NotADirectoryException extends Error {
        constructor(pathName: string) {
            super(`${pathName}: is not a directory`);
        }
    }

    export function splitPath(pathName: string): string[] {
        if (pathName.length === 0) {
            throw new InvalidPathNameException(pathName);
        }
        const pathList: string[] = [];
        if (pathName.startsWith(separator)) {
            pathList.push(separator);
        }
        for (const name of pathName.split(separator)) {
            if (name.length === 0) {
                continue;
            }
            pathList.push(name);
        }
        if (pathList.length === 0) {
            throw new InvalidPathNameException(pathName);
        }
        return pathList;
    }

    export function joinPath(pathList: string[], upto?: number): string {
        if (pathList.length === 0) {
            return ".";
        }
        if (pathList[0] === separator) {
            return separator + pathList.slice(1, upto).join(separator);
        }
        return pathList.join(separator);
    }

    class TimeKeeper {
        private _nextMtime = 1000;
        public advanceMTime(): number {
            return this._nextMtime++;
        }
    }

    const timeKeeper = new TimeKeeper();

    class File {
        public readonly mtime = timeKeeper.advanceMTime();
        constructor(public readonly contents: Uint8Array) {}
    }

    class Directory {
        public _mtime: number;
        private _entries = new Map<string, DirEntry>();

        constructor(parent?: Directory) {
            this._entries.set(".", this);
            this._entries.set("..", parent ? parent : this);
            this._mtime = timeKeeper.advanceMTime();
        }

        public get mtime(): number {
            return this._mtime;
        }

        public get(fileName: string): DirEntry | undefined {
            return this._entries.get(fileName);
        }

        public set(fileName: string, entry: DirEntry, mtime?: number): void {
            this._entries.set(fileName, entry);
            if (mtime === undefined) {
                this._updateMTime();
            } else {
                this._mtime = mtime;
            }
        }

        public delete(fileName: string): void {
            this._entries.delete(fileName);
            this._updateMTime();
        }

        public *[Symbol.iterator](): Generator<[string, DirEntry]> {
            for (const entry of this._entries) {
                yield entry;
            }
        }

        private _updateMTime() {
            this._mtime = timeKeeper.advanceMTime();
        }
    }

    type DirEntry = File | Directory;

    export enum FSEvent {
        create = 0xc,
        delete = 0xd,
        modify = 0xf,
    }

    export interface FSWatcher {
        cancel: () => void;
    }

    export interface FSSubscriber {
        onCreate: (pathOrUri: string | Uri) => void;
        onChange: (pathOrUri: string | Uri) => void;
        onDelete: (pathOrUri: string | Uri) => void;
    }

    class FSWatcherImpl implements FSWatcher {
        constructor(
            private _subscriber: FSSubscriber,
            private _unsubscribe: (watcher: FSWatcherImpl) => void
        ) {}

        public notify(eventType: FSEvent, pathOrUri: string | Uri) {
            if (eventType === FSEvent.create) {
                this._subscriber.onCreate(pathOrUri);
            } else if (eventType === FSEvent.modify) {
                this._subscriber.onChange(pathOrUri);
            } else {
                this._subscriber.onDelete(pathOrUri);
            }
        }

        public cancel(): void {
            this._unsubscribe(this);
        }
    }

    export class FS {
        private _root = new Directory();
        private _watchers = new Set<FSWatcherImpl>();

        private resolve(pathList: string[], pathName: string, makeDirs = false): DirEntry {
            const start = pathList[0] === separator ? 1 : 0;
            let entry: DirEntry = this._root;
            for (let idx = start; idx < pathList.length; idx++) {
                const name = pathList[idx];
                if (!(entry instanceof Directory)) {
                    throw new FileNotFoundException(pathName);
                }
                const e = entry.get(name);
                if (e) {
                    entry = e;
                } else if (makeDirs) {
                    const dir: Directory = new Directory(entry);
                    entry.set(name, dir);
                    this.publish(FSEvent.create, toAbsolutePath(pathList.slice(0, idx + 1)));
                    entry = dir;
                } else {
                    throw new FileNotFoundException(pathName);
                }
            }
            return entry;
        }

        private resolveName(pathName: string, makeDirs = false): DirEntry {
            return this.resolve(splitPath(pathName), pathName, makeDirs);
        }

        private resolveToDirAndName(pathName: string, makeDirs = false): [Directory, string[]] {
            let pathList = splitPath(pathName);
            if (pathList.length === 1 && pathList[0] === separator) {
                return [this._root, []];
            }
            let dir = this._root;
            if (pathList.length > 1) {
                const entry = this.resolve(pathList.slice(0, -1), pathName, makeDirs);
                if (!(entry instanceof Directory)) {
                    throw new FileNotFoundException(pathName);
                }
                dir = entry;
            }
            return [dir, pathList];
        }

        readFile(pathName: string): Uint8Array {
            const entry = this.resolveName(pathName);
            if (entry instanceof Directory) {
                throw new IsADirectoryException(pathName);
            }
            return entry.contents;
        }

        // If makeDirs is true, any missing path components will be created.
        writeFile(pathName: string, contents: Uint8Array, makeDirs = false, mtime?: number): void {
            const [dir, pathList] = this.resolveToDirAndName(pathName, makeDirs);
            if (pathList.length === 0) {
                // This only happens if the pathname consists solely of separators
                throw new IsADirectoryException(pathName);
            }
            const trailingName = pathList.at(-1)!;
            const entry = dir.get(trailingName);
            if (entry && entry instanceof Directory) {
                throw new IsADirectoryException(pathName);
            }

            dir.set(trailingName, new File(contents), mtime);

            // Notify filesystem watchers
            const absPathName = toAbsolutePath(pathList);
            if (entry) {
                this.publish(FSEvent.modify, absPathName);
            } else {
                this.publish(FSEvent.create, absPathName);
            }
        }

        deleteFile(pathName: string): void {
            const [dir, pathList] = this.resolveToDirAndName(pathName);
            if (pathList.length === 0) {
                // This only happens if the pathname consists solely of separators
                throw new IsADirectoryException(pathName);
            }
            const trailingName = pathList.at(-1)!;
            const entry = dir.get(trailingName);
            if (!entry) {
                throw new FileNotFoundException(pathName);
            }
            if (entry instanceof Directory) {
                throw new IsADirectoryException(pathName);
            }

            dir.delete(trailingName);

            // Notify filesystem watchers
            const absPathName = toAbsolutePath(pathList);
            this.publish(FSEvent.delete, absPathName);
        }

        readDir<T>(pathName: string, xlator: EntryXLator<T>): T[] {
            const dir = this.resolveName(pathName);
            if (!(dir instanceof Directory)) {
                throw new NotADirectoryException(pathName);
            }
            const res: T[] = [];
            for (const [name, entry] of dir) {
                if (entry instanceof Directory) {
                    res.push(xlator(name, FileType.directory, undefined, entry.mtime));
                } else {
                    res.push(xlator(name, FileType.file, entry.contents.length, entry.mtime));
                }
            }

            // return sorted list of results so tests can control the order in which
            // files are enumerated
            return res.sort();
        }

        makeDir(pathName: string): void {
            const [dir, pathList] = this.resolveToDirAndName(pathName);
            if (pathList.length === 0) {
                // This only happens if the pathname consists solely of separators
                throw new FileExistsException(pathName);
            }
            const trailingName = pathList.at(-1)!;
            const entry = dir.get(trailingName);
            if (entry) {
                throw new FileExistsException(pathName);
            }
            dir.set(trailingName, new Directory(dir));

            // Notify filesystem watchers
            const absPathName = toAbsolutePath(pathList);
            this.publish(FSEvent.create, absPathName);
        }

        rename(oldPath: string, newPath: string): void {
            const [oldDir, oldPathList] = this.resolveToDirAndName(oldPath);
            if (oldPathList.length === 0) {
                // This only happens if the pathname consists solely of separators.
                throw new Error("Cannot attempt to rename filesystem root");
            }
            const oldName = oldPathList.at(-1)!;
            const entryToMove = oldDir.get(oldName);
            if (!entryToMove) {
                throw new FileNotFoundException(oldPath);
            }

            let [newDir, newPathList] = this.resolveToDirAndName(newPath);
            let newName = oldName;
            if (newPathList.length > 0) {
                const trailingName = newPathList.at(-1)!;
                const entry = newDir.get(trailingName);
                if (entry && entry instanceof Directory) {
                    newDir = entry;
                    newName = oldPathList.at(-1)!;
                } else {
                    newName = trailingName;
                }
            }

            newDir.set(newName, entryToMove);
            oldDir.delete(oldName);

            const newAbsPathName = toAbsolutePath(newPathList);
            this.publish(FSEvent.create, newAbsPathName);

            const oldAbsPathName = toAbsolutePath(oldPathList);
            this.publish(FSEvent.delete, oldAbsPathName);
        }

        stat<T>(pathName: string, xlator: EntryXLator<T>): T {
            const entry = this.resolveName(pathName);
            if (entry instanceof Directory) {
                return xlator(pathName, FileType.directory, undefined, entry.mtime);
            } else {
                return xlator(pathName, FileType.file, entry.contents.length, entry.mtime);
            }
        }

        makeDirs(pathName: string): void {
            const entry = this.resolveName(pathName, true);
            if (!(entry instanceof Directory)) {
                throw new NotADirectoryException(pathName);
            }
        }

        makeIterator(startPathName?: string): Iterator {
            if (!startPathName) {
                return new PathIterator("/", this._root);
            }
            const dir = this.resolveName(startPathName);
            if (!(dir instanceof Directory)) {
                throw new NotADirectoryException(startPathName);
            }
            return new PathIterator(startPathName, dir);
        }

        createWatcher(subscriber: FSSubscriber): FSWatcher {
            const unsubscribe = (watcher: FSWatcherImpl) => {
                this._watchers.delete(watcher);
            };
            const watcher = new FSWatcherImpl(subscriber, unsubscribe);
            this._watchers.add(watcher);
            return watcher;
        }

        public publish(type: FSEvent, pathOrUri: string | Uri) {
            for (const watcher of this._watchers) {
                watcher.notify(type, pathOrUri);
            }
        }

        stopWatchers(): void {
            this._watchers.clear();
        }
    }

    /**
     * Iterator is an interface for iterating over the contents of a filesystems.
     */
    export interface Iterator {
        // `next` iterates over the files in a filesystem, returning a
        // [absolutePath, fileName] pair for each one. The returned fileName is
        // the same as `basename(absolutePath)`.
        next: () => Generator<[string, string]>;
    }

    class PathIterator implements Iterator {
        private pathPrefix: string;
        private dirStack: [string, Directory][] = [];

        constructor(startPathName: string, dir: Directory) {
            this.pathPrefix = this.makeDirName(startPathName);
            this.dirStack.push([this.pathPrefix, dir]);
        }

        private makeDirName(pathName: string): string {
            if (pathName === "/") {
                return "";
            }
            if (pathName.endsWith("/")) {
                return pathName;
            }
            return pathName + "/";
        }

        private joinPath(dirName: string, name: string): string {
            if (dirName.length > 0 && !dirName.endsWith("/")) {
                throw new Error(`dirName "${dirName}" does not end with "/"`);
            }
            return dirName + name;
        }

        public *next(): Generator<[string, string]> {
            while (this.dirStack.length > 0) {
                const dirItem = this.dirStack.pop();
                const [currDirName, currDir] = dirItem!;
                for (const [name, entry] of currDir) {
                    if (name === "." || name === "..") {
                        continue;
                    }
                    const pathName = this.joinPath(currDirName, name);
                    if (entry instanceof Directory) {
                        this.dirStack.push([this.makeDirName(pathName), entry]);
                    } else {
                        yield [pathName, name];
                    }
                }
            }
        }
    }
}

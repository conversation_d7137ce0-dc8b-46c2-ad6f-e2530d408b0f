import { assert } from "console";

import { getLogger } from "../logging";

class Modification {
    constructor(
        public seq: number,
        public start: number,
        public length: number,
        public origLength: number
    ) {}

    public get end(): number {
        return this.start + this.length;
    }

    public get localShift(): number {
        return this.origLength - this.length;
    }
}

export class Edit {
    constructor(
        public seq: number,
        public start: number,
        public length: number,
        public origStart: number,
        public origLength: number
    ) {}

    static fromMod(mod: Modification, rightShift: number): Edit {
        return new Edit(mod.seq, mod.start, mod.length, mod.start + rightShift, mod.origLength);
    }

    public get end(): number {
        return this.start + this.length;
    }

    public get origEnd(): number {
        return this.origStart + this.origLength;
    }

    public setStart(newStart: number) {
        const delta = this.start - newStart;
        this.start -= delta;
        this.length += delta;
        this.origStart -= delta;
        this.origLength += delta;
    }

    public setEnd(newEnd: number) {
        const delta = newEnd - this.end;
        this.length += delta;
        this.origLength += delta;
    }
}

/**
 * ChangeTracker is a class that tracks changes to a document relative to its "base state". The
 * base state is the state of the document when this tracker was created, but it can be advanced
 * to "now" by calling advance().
 */
export class ChangeTracker {
    private static _logger = getLogger("ChangeTracker");

    private _modifications: Modification[] = [];
    private _seq = 0;

    public get seq(): number {
        return this._seq;
    }

    // Returns true if there are no tracked changes.
    public get empty(): boolean {
        return this._modifications.length === 0;
    }

    // Returns the number of tracked changes.
    public get length(): number {
        return this._modifications.length;
    }

    // `translate` translates the given range, which is relative to the current state of the
    // document, to the corresponding range in the base state of the document. In ambiguous
    // situtations where there are multiple possible translations, it returns the widest possible
    // range. In practice, this means that if `start` falls inside a modification, the returned
    // range will start at the modification's left edge, and if `end` falls inside a modification,
    // the returned range will end at the modification's right edge.
    public translate(start: number, length: number): [number, number] {
        const end = start + Math.max(length, 0);
        let idx = 0;
        let rightShift = 0;

        while (idx < this._modifications.length && this._modifications[idx].end < start) {
            rightShift += this._modifications[idx].localShift;
            idx++;
        }

        // If `start` falls inside a modification, move it to the modification's left edge.
        const normStart =
            idx === this._modifications.length || start < this._modifications[idx].start
                ? start
                : this._modifications[idx].start;
        const translatedStart = normStart + rightShift;

        while (idx < this._modifications.length && this._modifications[idx].end < end) {
            rightShift += this._modifications[idx].localShift;
            idx++;
        }

        // If `end` falls inside a modification, move it to the modification's right edge.
        const normEnd =
            idx === this._modifications.length || end < this._modifications[idx].start
                ? end
                : this._modifications[idx].start + this._modifications[idx].origLength;
        const translatedEnd = normEnd + rightShift;

        return [translatedStart, translatedEnd - translatedStart];
    }

    // Apply a new change to this ChangeTracker.
    public apply(seq: number, start: number, charsToDelete: number, charsToInsert: number) {
        /**
         * Strategy for applying new changes:
         *
         * If the new change only inserts new characters (it is not a deletion or a replacement),
         * it is relatively straightforward to apply, as it either extends an existing modification
         * (if it is adjacent to it) or it creates a new modification that doesn't interact with
         * any of the others. Deletions and replacements are more complicated, as they can extend
         * an existing modification and/or subsume one or more existing modifications.
         *
         * In either case, the existing modifications can be divided into three possibly empty
         * categories: those before the new change, those that interact with the change, and those
         * that are after the change. We don't need to do anything with the modifications in the
         * first category, and those in the last category just needs to be shifted left or right.
         *
         * The modifications in the middle category will all be replaced by a single modification
         * that incorporates the new change and all the modifications that it interacts with. This
         * single modification is called `head` and depending on how the new change interacts with
         * the existing modifications, head will either be the first modification of the middle
         * category or a brand new modification.
         */

        // idx: Index of the current modification in `_modifications`
        // head: The modification that incorporates the new change and the mods it interacts with
        // newLength: The value of head.length after the new change is applied
        // newOrigLength: The value of head.origLength after the new change is applied
        // currPos: The current character position in the document
        // charsDeleted: The number of characters deleted by the new change that have been
        //     incorporated into head
        // firstToReplace: Index of the first modification in the range of modifications that
        //     interact with the new change
        let idx = 0;
        let head: Modification;
        let newLength: number;
        let newOrigLength: number;
        let currPos = start;
        let charsDeleted = 0;
        let firstToReplace: number;

        // Advance idx past any modifications that are before the new change
        while (idx < this._modifications.length && this._modifications[idx].end < start) {
            idx++;
        }
        firstToReplace = idx;

        if (idx < this._modifications.length && this._modifications[idx].start <= start) {
            // _modifications[idx] starts before (or even with) the new change and overlaps with
            // it. We will use it as `head`.
            head = this._modifications[idx];
            newLength = head.length;
            newOrigLength = head.origLength;

            // Beginning at `currPos`, remove any characters from `head` that are deleted by the
            // new change.
            const insertOffset = currPos - head.start;
            assert(insertOffset <= head.length);
            const deletedFromHead = Math.min(
                head.length - insertOffset,
                charsToDelete - charsDeleted
            );
            newLength -= deletedFromHead;
            charsDeleted += deletedFromHead;
            currPos = head.end;
            ++idx;
        } else {
            // There is no change that starts before the new change and overlaps with it. Create
            // a new, empty, modification to use as `head`.
            head = new Modification(seq, start, 0, 0);
            newLength = 0;
            newOrigLength = 0;
        }

        // Starting at `idx`, advance through the existing modifications until we have deleted
        // `charsDeleted` characters. For each modification, we delete any characters before it
        // (between `currPos` and itself), plus any inserted characters that it contains, until
        // we reach charsDeleted.
        for (; idx < this._modifications.length && charsDeleted < charsToDelete; idx++) {
            const mod = this._modifications[idx];

            // Delete chars between currPos and mod
            const betweenChars = mod.start - currPos;
            const toExtend = Math.min(betweenChars, charsToDelete - charsDeleted);
            newOrigLength += toExtend;
            charsDeleted += toExtend;
            if (currPos + toExtend < mod.start) {
                break;
            }

            // Delete chars from mod and transfer any remaining contents of mod to newOrigLength
            // and newLength.
            const deletedFromMod = Math.min(mod.length, charsToDelete - charsDeleted);
            newLength += mod.length - deletedFromMod;
            charsDeleted += deletedFromMod;
            newOrigLength += mod.origLength;

            // Advance to the end of the current modification
            currPos = mod.end;
        }

        // Apply the new change to head.
        head.length = newLength + charsToInsert;
        head.origLength = newOrigLength + (charsToDelete - charsDeleted);

        // Update the modifications array to reflect the new change.
        head.seq = seq;
        this._modifications.splice(firstToReplace, idx - firstToReplace, head);

        // Any modifications after head are after the new change. Shift them left or right as
        // appropriate.
        for (idx = firstToReplace + 1; idx < this._modifications.length; idx++) {
            this._modifications[idx].start += charsToInsert - charsToDelete;
        }

        this._seq = seq;
    }

    // marge applies the changes from another ChangeTracker to this one.
    public merge(other: ChangeTracker) {
        for (const mod of other._modifications) {
            this.apply(mod.seq, mod.start, mod.origLength, mod.length);
        }
    }

    // advance advances the base state of the document to "now".
    public advance(): void {
        for (const mod of this._modifications) {
            mod.origLength = mod.length;
        }
    }

    // getEdits returns the set of edits that represent the tracked changes.
    public getEdits(): Edit[] {
        const edits: Edit[] = [];
        let rightShift = 0;
        for (const mod of this._modifications) {
            edits.push(
                new Edit(mod.seq, mod.start, mod.length, mod.start + rightShift, mod.origLength)
            );
            rightShift += mod.localShift;
        }
        return edits;
    }

    // countChunks returns the number of chunks that cover the tracked changes.
    public countChunks(maxChunkSize: number): number {
        if (this._modifications.length === 0) {
            return 0;
        }
        const maxOffset = this._modifications.at(-1)!.end;
        return this.getChunks(maxChunkSize, maxOffset).length;
    }

    // getChunks creates chunks of the given size that cover the tracked changes. The returned
    // chunks are non-overlapping and each one will cover at least one tracked change. They are
    // ordered by their starting offset within the document.
    public getChunks(maxChunkSize: number, maxOffset: number): Edit[] {
        if (this._modifications.length === 0) {
            return [];
        }

        const chunks = new Array<Edit>();
        let currChunk: Edit | undefined;
        let rightShift = 0;

        for (const mod of this._modifications) {
            const edit = Edit.fromMod(mod, rightShift);
            rightShift += mod.localShift;

            if (currChunk !== undefined) {
                // If this entire modification doesn't fit in the current chunk, close the chunk;
                // we will open a new one below. The exception is if the modification is larger
                // than the chunk size, which means it will have to be split anyway. In that case,
                // we take as much of the modification as we can into the current chunk, then
                // create new chunks for the rest.
                if (
                    edit.start - currChunk.start >= maxChunkSize ||
                    (edit.end - currChunk.start > maxChunkSize && edit.length <= maxChunkSize)
                ) {
                    chunks.push(currChunk);
                    currChunk = undefined;
                }
            }

            // If we have no current chunk, create a zero-width one starting at the start
            // of the modification. Otherwise, extend the current chunk to the start of the
            // modification. Either way, the current chunk is now adjacent to the modification.
            if (currChunk === undefined) {
                currChunk = new Edit(edit.seq, edit.start, 0, edit.origStart, 0);
            } else {
                const gapSize = edit.start - currChunk.end;
                currChunk.length += gapSize;
                currChunk.origLength += gapSize;
            }
            const editLength = edit.length;

            // Take as much of the modification as we can into the current chunk. Create new
            // chunks for the rest.
            let toConsume = Math.min(editLength, maxChunkSize - currChunk.length);
            currChunk.length += toConsume;
            currChunk.origLength += edit.origLength;
            currChunk.seq = Math.max(currChunk.seq, edit.seq);

            for (let consumed = toConsume; consumed < editLength; consumed += toConsume) {
                chunks.push(currChunk);
                const start = edit.start + consumed;
                currChunk = new Edit(edit.seq, start, 0, edit.origEnd, 0);
                toConsume = Math.min(editLength - consumed, maxChunkSize);
                currChunk.length += toConsume;
            }
        }
        if (currChunk !== undefined) {
            chunks.push(currChunk);
        }

        // Widen the chunks up to the max chunk size
        this._widen(chunks, maxChunkSize, maxOffset);

        const validatedChunks = this._validateChunks(chunks);
        return validatedChunks;
    }

    private _widen(chunks: Edit[], maxChunkSize: number, maxOffset: number) {
        // Try to widen each chunk to the max chunk size, centering the modification(s) within
        // it, without overlapping the neighboring chunks.
        let prevEnd = 0;
        for (let idx = 0; idx < chunks.length; idx++) {
            const chunk = chunks[idx];
            const nextStart = idx + 1 === chunks.length ? maxOffset : chunks[idx + 1].start;
            const toAdd = maxChunkSize - chunk.length;

            let start: number;
            let end: number;
            const candidateStart = Math.floor(chunk.start - toAdd / 2);
            if (candidateStart <= prevEnd) {
                start = prevEnd;
                end = Math.min(start + maxChunkSize, nextStart);
            } else {
                end = Math.min(candidateStart + maxChunkSize, nextStart);
                start = Math.max(end - maxChunkSize, prevEnd);
            }

            chunk.setStart(start);
            chunk.setEnd(end);
            prevEnd = end;
        }
    }

    private _validateChunks(chunks: Edit[]): Edit[] {
        const validatedChunks = new Array<Edit>();
        for (const chunk of chunks) {
            if (chunk.origStart > chunk.origEnd) {
                ChangeTracker._logger.error("invalid chunk: ", JSON.stringify(chunk));
            } else {
                validatedChunks.push(chunk);
            }
        }
        return validatedChunks;
    }
}

import { structuredPatch } from "diff";
import * as vscode from "vscode";

/**
 * Scrolls to the first decoration in the editor.
 *
 * This utility function can be used independently of addDiffDecorations to scroll
 * to the first decoration in an editor. It's useful when you want to scroll to a
 * specific range without adding decorations, or when you want more control over
 * when the scrolling happens.
 *
 * Example usage:
 * ```typescript
 * // Scroll to the first decoration in the editor
 * const addedRanges = [...]; // Array of vscode.Range for added lines
 * const removedRanges = [...]; // Array of vscode.Range for removed lines
 * scrollToFirstDiffDecoration(editor, addedRanges, removedRanges);
 * ```
 *
 * @param editor - The VS Code text editor
 * @param addedRanges - Array of ranges for added lines
 * @param removedRanges - Array of ranges for removed lines
 * @param revealType - The type of reveal to use when scrolling (default: InCenterIfOutsideViewport)
 * @returns True if scrolled to a decoration, false otherwise
 */
export function scrollToFirstDiffDecoration(
    editor: vscode.TextEditor,
    addedRanges: vscode.Range[],
    removedRanges: vscode.Range[],
    revealType?: vscode.TextEditorRevealType
): boolean {
    if (addedRanges.length === 0 && removedRanges.length === 0) {
        return false;
    }

    // Determine the first decoration to scroll to (either added or removed)
    let firstRange: vscode.Range | undefined;

    if (addedRanges.length > 0 && removedRanges.length > 0) {
        // If we have both types, find the one that appears first in the document
        const firstAddedLine = addedRanges[0].start.line;
        const firstRemovedLine = removedRanges[0].start.line;
        firstRange = firstAddedLine <= firstRemovedLine ? addedRanges[0] : removedRanges[0];
    } else if (addedRanges.length > 0) {
        firstRange = addedRanges[0];
    } else if (removedRanges.length > 0) {
        firstRange = removedRanges[0];
    }

    if (firstRange) {
        // Scroll to the range with the specified reveal type or default to InCenterIfOutsideViewport
        editor.revealRange(
            firstRange,
            revealType ?? vscode.TextEditorRevealType.InCenterIfOutsideViewport
        );
        return true;
    }

    return false;
}

/**
 * Gets theme-appropriate colors for diff decorations
 *
 * @returns Object containing colors for added and removed lines based on current theme
 */
function getDiffDecorationColors(): {
    added: { background: string; text: string };
    removed: { background: string; text: string; border: string };
} {
    const isDarkTheme =
        vscode.window.activeColorTheme.kind === vscode.ColorThemeKind.Dark ||
        vscode.window.activeColorTheme.kind === vscode.ColorThemeKind.HighContrast;

    return {
        added: {
            background: isDarkTheme ? "rgba(0, 255, 0, 0.1)" : "rgba(0, 200, 0, 0.05)",
            text: isDarkTheme ? "rgba(150, 255, 150, 0.9)" : "rgba(0, 100, 0, 0.8)",
        },
        removed: {
            background: isDarkTheme ? "rgba(255, 0, 0, 0.05)" : "rgba(255, 0, 0, 0.05)",
            text: isDarkTheme ? "rgba(255, 200, 200, 0.9)" : "rgba(150, 0, 0, 0.8)",
            border: isDarkTheme ? "rgba(150, 0, 0, 0.3)" : "rgba(150, 0, 0, 0.3)",
        },
    };
}

/**
 * Adds decorations to the editor to highlight added and removed lines.
 *
 * @param editor - The VS Code text editor
 * @param originalContent - The original content of the file
 * @param modifiedContent - The modified content of the file
 * @param options - Additional options for the decorations
 * @param options.scrollToFirstDecoration - Whether to scroll to the first decoration (default: false)
 * @param options.revealType - The type of reveal to use when scrolling (default: InCenterIfOutsideViewport)
 * @returns The decoration types created (can be used to dispose them later)
 */
export function addDiffDecorations(
    editor: vscode.TextEditor,
    originalContent: string,
    modifiedContent: string,
    options?: {
        scrollToFirstDecoration?: boolean;
        revealType?: vscode.TextEditorRevealType;
    }
): vscode.TextEditorDecorationType[] {
    const colors = getDiffDecorationColors();

    // Create decoration types for added and removed lines
    const addedLineDecoration = vscode.window.createTextEditorDecorationType({
        backgroundColor: colors.added.background,
        isWholeLine: true,
        after: {
            color: colors.added.text,
            margin: "0 0 0 1em",
        },
    });

    const removedLineDecoration = vscode.window.createTextEditorDecorationType({
        isWholeLine: true,
        before: {
            // Use 'before' for the first line to show the indicator at the beginning
            // The contentText will be set individually for each range
            color: colors.removed.text,
            border: `1px solid ${colors.removed.border}`,
            backgroundColor: colors.removed.background,
            margin: "0 5px 0 0",
        },
    });

    // Compute the diff using structuredPatch
    const patch = structuredPatch(
        "original",
        "modified",
        originalContent,
        modifiedContent,
        "",
        "",
        { context: 0 } // No context lines to focus only on changes
    );

    const addedRanges: vscode.Range[] = [];
    const removedRanges: { range: vscode.Range; count: number; removedContent: string }[] = [];

    // Process each hunk to determine line decorations
    for (const hunk of patch.hunks) {
        // Calculate line positions in the modified document
        let currentLine = hunk.newStart - 1; // Convert to 0-based index
        let hasRemovedLines = false;

        // First pass: Mark added lines and count removed lines
        let removedLinesCount = 0;
        let removedContent = "";

        for (const line of hunk.lines) {
            if (line.startsWith("+")) {
                // Added line - it exists in the modified document
                if (currentLine < editor.document.lineCount) {
                    const range = new vscode.Range(
                        new vscode.Position(currentLine, 0),
                        new vscode.Position(
                            currentLine,
                            editor.document.lineAt(currentLine).text.length
                        )
                    );
                    addedRanges.push(range);
                }
                currentLine++; // Move to next line in modified document
            } else if (line.startsWith("-")) {
                // Found a removed line
                hasRemovedLines = true;
                removedLinesCount++;
                // Store the content of the removed line (without the leading "-")
                removedContent += line.substring(1) + "\n";
            }
        }

        // If the hunk has removed lines, add a single decoration on the line above
        if (hasRemovedLines) {
            // Find the line above where the removed lines would have been
            const lineAbove = Math.max(0, hunk.newStart - 2); // -1 for 0-based index, -1 for line above

            if (lineAbove < editor.document.lineCount) {
                const range = new vscode.Range(
                    new vscode.Position(lineAbove, 0),
                    new vscode.Position(lineAbove, editor.document.lineAt(lineAbove).text.length)
                );
                // Store the range with the count of removed lines and their content
                removedRanges.push({
                    range,
                    count: removedLinesCount,
                    removedContent: removedContent.trim(),
                });
            }
        }
    }

    // Apply the decorations
    editor.setDecorations(addedLineDecoration, addedRanges);

    // Create decorations with specific labels for each removed range
    const removedDecorations = removedRanges.map((item) => {
        const hoverMessage = new vscode.MarkdownString();
        hoverMessage.appendMarkdown("**Removed content:**\n\n");

        // Create a code block with the removed content
        hoverMessage.appendCodeblock(item.removedContent, "diff");

        // Enable HTML and trusted content in the markdown
        hoverMessage.isTrusted = true;
        hoverMessage.supportHtml = true;

        return {
            range: item.range,
            renderOptions: {
                before: {
                    contentText: `⊖ ${item.count} line${item.count === 1 ? "" : "s"} removed`,
                    color: colors.removed.text,
                    border: `1px solid ${colors.removed.border}`,
                    backgroundColor: colors.removed.background,
                    margin: "0 5px 0 0",
                },
            },
            hoverMessage,
        };
    });
    editor.setDecorations(removedLineDecoration, removedDecorations);

    // Scroll to the first decoration if requested
    if (options?.scrollToFirstDecoration) {
        scrollToFirstDiffDecoration(
            editor,
            addedRanges,
            removedRanges.map((r) => r.range),
            options.revealType
        );
    }

    // Return the decoration types so they can be disposed later if needed
    return [addedLineDecoration, removedLineDecoration];
}

/**
 * Automatically removes decorations after a specified delay.
 *
 * @param decorations - The decoration types to remove
 * @param options - Options for auto-removal
 * @param options.minDelayMs - The minimum delay in milliseconds before removing the decorations (default: 10000ms)
 * @param options.maxDelayMs - The maximum delay in milliseconds before removing the decorations (default: 30000ms)
 */
export function autoRemoveDecorations(
    decorations: vscode.TextEditorDecorationType[],
    options: {
        minDelayMs?: number;
        maxDelayMs?: number;
    }
): void {
    // Calculate an appropriate delay based on the number of decorations
    // Examples:
    // - 1 decoration: baseDelay * log4(2) ≈ 5000ms
    // - 5 decorations: baseDelay * log4(6) ≈ 11600ms
    // - 10 decorations: baseDelay * log4(11) ≈ 15400ms
    // - 100 decorations: baseDelay * log4(101) ≈ 33300ms
    const baseDelay = 10000; // 10 seconds
    const scaledDelay = baseDelay * (Math.log(decorations.length + 1) / Math.log(4));

    const { minDelayMs = 10000, maxDelayMs = 30000 } = options;
    const finalDelay = Math.min(Math.max(scaledDelay, minDelayMs), maxDelayMs);

    setTimeout(() => {
        decorations.forEach((decoration) => decoration.dispose());
    }, finalDelay);
}

/**
 * Removes decorations when the document changes.
 *
 * @param editor - The VS Code text editor
 * @param decorations - The decoration types to remove
 * @returns A disposable that can be used to stop listening for changes
 */
export function removeDecorationsOnEdit(
    editor: vscode.TextEditor,
    decorations: vscode.TextEditorDecorationType[]
): vscode.Disposable {
    // Create a disposable to track the change listener
    const disposables: vscode.Disposable[] = [];

    // Register a document change listener
    const changeListener = vscode.workspace.onDidChangeTextDocument((event) => {
        // Check if the changed document is the one we're watching
        if (event.document.uri.toString() === editor.document.uri.toString()) {
            // Remove the decorations
            decorations.forEach((decoration) => decoration.dispose());

            // Dispose the listener since we no longer need it
            disposables.forEach((d) => void d.dispose());
        }
    });

    // Add the change listener to our disposables
    disposables.push(changeListener);

    // Return a disposable that will clean up the listener when disposed
    return {
        dispose: () => {
            disposables.forEach((d) => void d.dispose());
        },
    };
}

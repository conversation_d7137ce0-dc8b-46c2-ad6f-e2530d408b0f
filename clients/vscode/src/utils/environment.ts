import fs from "fs";
import os from "os";
import path from "path";
import semver from "semver";
import * as vscode from "vscode";

import { AugmentLogger, getLogger } from "../logging";

const platform = os.platform();

const logger: AugmentLogger = getLogger("AugmentEnvironment");

export function getUserDirectory() {
    let userDirPath;
    if (platform === "win32" && process.env.APPDATA) {
        userDirPath = path.join(process.env.APPDATA, "Code", "User");
    } else if (platform === "darwin") {
        userDirPath = path.join(os.homedir(), "Library", "Application Support", "Code", "User");
    } else if (platform === "linux") {
        userDirPath = path.join(os.homedir(), ".config", "Code", "User");
    }

    if (userDirPath && fs.existsSync(userDirPath)) {
        return userDirPath;
    }

    return null;
}

export function getUserDocumentsDirectory(): string | null {
    const homeDir = os.homedir();
    let documentsPath;

    if (os.platform() === "win32") {
        // On Windows, Documents is typically at %USERPROFILE%\Documents
        documentsPath = path.join(homeDir, "Documents");
    } else if (os.platform() === "darwin") {
        // On macOS, Documents is at ~/Documents
        documentsPath = path.join(homeDir, "Documents");
    } else if (os.platform() === "linux") {
        // On Linux, Documents is typically at ~/Documents
        documentsPath = path.join(homeDir, "Documents");
    }

    if (documentsPath && fs.existsSync(documentsPath)) {
        return documentsPath;
    }

    return null;
}

let extensionPackageJson: IExtensionPackageJSON | undefined | null = undefined;
export function getAugmentExtensionPackageJson(): IExtensionPackageJSON | null {
    if (extensionPackageJson === undefined) {
        const extension = vscode.extensions.getExtension("Augment.vscode-augment");
        extensionPackageJson = (extension?.packageJSON as IExtensionPackageJSON) ?? null;
    }
    return extensionPackageJson;
}

interface IExtensionPackageJSON {
    version: string;
    contributes: {
        keybindings: Keybinding[];
        icons: Record<string, FontIconDefinition>;
    };
}

export type Keybinding = {
    command: string;
    key: string;
    mac: string;
};

export type FontIconDefinition = {
    description: string;
    default: {
        fontPath: string;
        fontCharacter: string;
    };
};

export function getExtension(): vscode.Extension<any> {
    return vscode.extensions.getExtension("Augment.vscode-augment")!;
}

// The version we use for debug.
const SPECIAL_BUILD_VERSION = "0.0.3141592";
/**
 * Check if the extension version is greater than or equal to the provided version
 * @param version version to compare.
 * @param extensionVersion extension version read from package.
 * @returns true if the extension version is greater than or equal to the provided version. otherwise false.
 * if the extension version is not available, returns false.
 * if the provided version is an empty string, returns false.
 */
export function isExtensionVersionGte(
    version: string,
    extensionVersion: string | undefined = getAugmentExtensionPackageJson()?.version
): boolean {
    if (!extensionVersion || version === "") {
        return false;
    }
    return semver.gte(extensionVersion, version) || extensionVersion === SPECIAL_BUILD_VERSION; // regard to special version as LATEST.
}

/**
 * Check if the extension version is less than or equal to the provided version
 * @param version version to compare.
 * @param extensionVersion extension version read from package.
 * @returns true if the extension version is less than or equal to the provided version. otherwise false.
 * if the extension version is not available, returns true (so that the default state for max_version flags is true).
 * if the provided version is an empty string, returns false (since empty string is like stating "no value").
 */
export function isExtensionVersionLte(
    version: string,
    extensionVersion: string | undefined = getAugmentExtensionPackageJson()?.version
): boolean {
    if (!extensionVersion) {
        // this may be a bit weird but in general this function returns true to give "older" behavior, which
        // should be safer. This value really should not be missing, but if it is then we want the "safer" behavior
        return true;
    }
    if (version === "") {
        // on the other hand here we return false like isExtensionVersionGte, because if the version
        // is unspecified, then we read it as "remove the constraint and give me new behavior"
        return false;
    }
    // special build version will force this to be false.
    return semver.lte(extensionVersion, version) && extensionVersion !== SPECIAL_BUILD_VERSION;
}

export function isVsCodeVersionGte(version: string): boolean {
    try {
        return semver.gte(vscode.version, version);
    } catch {
        logger.error(`Failed to parse vscode version: ${vscode.version}`);
        return false;
    }
}

/**
 * Check if the extension version is a debug extension.
 * @param extensionVersion extension version read from package.
 * @returns true if the extension version is the same as the special build version.
 * if the extension version is not available, returns false.
 */
export function isDebugExtension(
    extensionVersion: string | undefined = getAugmentExtensionPackageJson()?.version
): boolean {
    return extensionVersion === SPECIAL_BUILD_VERSION;
}

/**
 * Get the patch number of the extension version.
 * If the extension version is the special build version, returns 0.
 * @param extensionVersion extension version read from package.
 * @returns the patch number of the extension version. if the extension version is not available, returns 0.
 */
export function getExtensionPatchNumber(
    extensionVersion: string | undefined = getAugmentExtensionPackageJson()?.version
): number {
    if (!extensionVersion) {
        return 0;
    }
    if (isDebugExtension(extensionVersion)) {
        return 0;
    }
    const parsed = semver.parse(extensionVersion);
    return parsed?.patch ?? 0;
}

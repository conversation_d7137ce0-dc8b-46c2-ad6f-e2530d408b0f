/**
 * Represents a setup script with its name, path, content, and location
 */

export interface SetupScript {
    name: string;
    path: string;
    content: string;
    location: SetupScriptLocation;
    isGenerateOption?: boolean;
}

export type SetupScriptLocation = "home" | "git" | "workspace";

export type LastUsedRemoteAgentSetup = {
    lastRemoteAgentGitRepoUrl: string | null;
    lastRemoteAgentGitBranch: string | null;
    lastRemoteAgentSetupScript: string | null;
};

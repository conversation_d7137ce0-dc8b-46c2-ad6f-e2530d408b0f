/**
 * Detect and redact long high-entropy strings that may be secrets.
 *
 * Designed primarily for use with command line I/O, for autofix data gathering, but
 * should also work with code. Detecting passwords and other sensitive but not
 * information-theoretically heavyweight strings is out of scope for now.
 *
 * Implementation references:
 *     TruffleHog
 *     https://github.com/tehryanx/entro.py
 *     https://github.com/cerebruminc/detect-high-entropy-strings
 */
const BASE64_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
const HEX_CHARS = "1234567890abcdefABCDEF";
const SEPARATOR_CHARS = ".-_"; // Don't count these toward entropy, but don't break strings on them either

// Loosely speaking, these are set to detect secrets of 128 bits or more
const BASE64_LENGTH_THRESHOLD = 20;
const HEX_LENGTH_THRESHOLD = 30;

interface EntropyConfig {
    b64Minimum: number;
    hexMinimum: number;
}

interface EntropyResult {
    type: "Base64" | "HEX";
    entropy: number;
    secret: string;
}

function getStringsOfSet(word: string, charSet: string, lengthThreshold: number): string[] {
    let count = 0;
    let letters = "";
    const strings: string[] = [];

    for (const char of word) {
        if (charSet.includes(char) || SEPARATOR_CHARS.includes(char)) {
            letters += char;
            count++;
        } else {
            if (count > lengthThreshold) {
                strings.push(letters);
            }
            letters = "";
            count = 0;
        }
    }

    if (count > lengthThreshold) {
        strings.push(letters);
    }

    return strings;
}

function shannonEntropy(data: string, charSet: string): number {
    if (!data) {
        return 0;
    }

    let entropy = 0;
    for (const x of charSet) {
        const count = [...data].filter((char) => char === x).length;
        const px = count / data.length;
        if (px > 0) {
            entropy += -px * Math.log2(px);
        }
    }

    return entropy;
}

function findEntropy(
    content: string,
    // These values are found to work well in practice for redacting secrets
    config: EntropyConfig = {
        b64Minimum: 4.0,
        hexMinimum: 3.0,
    }
): EntropyResult[] {
    const results: EntropyResult[] = [];
    const words = content.split(/\s+/);

    words.forEach((word) => {
        // Check base64 strings
        const base64Strings = getStringsOfSet(word, BASE64_CHARS, BASE64_LENGTH_THRESHOLD);
        base64Strings.forEach((string) => {
            const b64Entropy = shannonEntropy(string, BASE64_CHARS);
            if (b64Entropy > config.b64Minimum) {
                results.push({
                    type: "Base64",
                    entropy: b64Entropy,
                    secret: string,
                });
            }
        });

        // Check hex strings
        const hexStrings = getStringsOfSet(word, HEX_CHARS, HEX_LENGTH_THRESHOLD);
        hexStrings.forEach((string) => {
            const hexEntropy = shannonEntropy(string, HEX_CHARS);
            if (hexEntropy > config.hexMinimum) {
                results.push({
                    type: "HEX",
                    entropy: hexEntropy,
                    secret: string,
                });
            }
        });
    });

    return results;
}

function redactEntropy(
    content: string,
    config: EntropyConfig = {
        b64Minimum: 4.0,
        hexMinimum: 3.0,
    }
): string {
    const results = findEntropy(content, config);
    let redactedContent = content;
    results.forEach((result) => {
        redactedContent = redactedContent.replace(result.secret, "X".repeat(result.secret.length));
    });
    return redactedContent;
}

export { findEntropy, EntropyConfig, EntropyResult, redactEntropy };

import { RemoteAgentSSHConfig } from "$vscode/src/remote-agent-manager/types";

import { SSHConfigManager } from "../ssh-config-manager";

// Mock the SshFileSystem
jest.mock("../ssh-file-system");

describe("SSHConfigManager", () => {
    let sshConfigManager: SSHConfigManager;

    beforeEach(() => {
        // Reset mocks
        jest.clearAllMocks();

        // Create a new instance for each test
        sshConfigManager = new SSHConfigManager();
    });

    describe("getRawString method", () => {
        it("should preserve backslash characters", () => {
            expect(sshConfigManager.getRawString("C:\\path\\to\\file")).toBe(
                "C:\\\\path\\\\to\\\\file"
            );
        });

        it("should preserve newline characters", () => {
            expect(sshConfigManager.getRawString("line1\nline2")).toBe("line1\\nline2");
        });

        it("should preserve tab characters", () => {
            expect(sshConfigManager.getRawString("column1\tcolumn2")).toBe("column1\\tcolumn2");
        });

        it("should handle empty strings", () => {
            expect(sshConfigManager.getRawString("")).toBe("");
        });

        it("should handle strings with quotes", () => {
            expect(sshConfigManager.getRawString('value with "quotes"')).toBe(
                'value with \\"quotes\\"'
            );
        });
    });

    describe("formatHostEntry", () => {
        it("should correctly format a host entry with escaped values", async () => {
            const remoteAgentId = "test-host";
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "IdentityFile", value: "~/.ssh/id_rsa" },
                    { key: "LocalForward", value: "8080 localhost:8080" },
                    { key: "RemoteCommand", value: "cd /path\\with\\backslashes && bash" },
                ],
            };

            const result = sshConfigManager.formatHostEntry(remoteAgentId, config);

            // Verify the host and hostname are set correctly
            expect(result).toContain(`Host ${remoteAgentId}`);
            expect(result).toContain(`HostName ${config.hostname}`);

            // Verify each SSH option is formatted correctly with escaped values
            for (const option of config.ssh_config_options) {
                const escapedValue = sshConfigManager.getRawString(option.value);
                expect(result).toContain(`${option.key} ${escapedValue}`);
            }

            // Specifically check that backslashes are properly escaped
            expect(result).toContain("RemoteCommand cd /path\\\\with\\\\backslashes && bash");
        });

        it("should handle double-escaped newlines in KnownHostsCommand", async () => {
            const remoteAgentId = "test-host";
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [{ key: "KnownHostsCommand", value: 'echo "test\\n123"' }],
            };

            const result = sshConfigManager.formatHostEntry(remoteAgentId, config);

            // The double-escaped newline (\\n) should be converted to a single-escaped newline (\n)
            // before being processed by getRawString, then getRawString should escape it as \\n
            expect(result).toContain('KnownHostsCommand echo \\"test\\n123\\"');
        });

        it("Should still handle single-escaped newlines in KnownHostsCommand", async () => {
            const remoteAgentId = "test-host";
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [{ key: "KnownHostsCommand", value: 'echo "test\n123"' }],
            };

            const result = sshConfigManager.formatHostEntry(remoteAgentId, config);

            // The newline should be escaped as \\n
            expect(result).toContain('KnownHostsCommand echo \\"test\\n123\\"');
        });

        it("should handle Windows-specific config transformations", async () => {
            const remoteAgentId = "test-host";
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    {
                        key: "ProxyCommand",
                        value: "C:\\Program Files\\Git\\usr\\bin\\openssl.exe s_client -FOO \\%h\\ -BAR \\%p\\",
                    },
                ],
            };

            // Mock platform as Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("win32");
            const result = sshConfigManager.formatHostEntry(remoteAgentId, config);

            // Verify Windows-specific transformations
            // ProxyCommand should not be additionally escaped
            expect(result).toContain(
                "ProxyCommand C:\\Program Files\\Git\\usr\\bin\\openssl.exe s_client -FOO \\%h\\ -BAR \\%p\\"
            );
        });
    });

    describe("updateHostConfig", () => {
        const privateKeyPath = "/home/<USER>/.augment/ssh-keys/augment_remote_agent_key";

        beforeEach(() => {
            // Mock getSshKnownHostsPath to return a consistent path
            jest.spyOn(sshConfigManager.fileSystem, "getSshKnownHostsPath").mockResolvedValue(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123"
            );

            // Mock ensureSshKnownHostsDirExists to do nothing
            jest.spyOn(
                sshConfigManager.fileSystem,
                "ensureSshKnownHostsDirExists"
            ).mockResolvedValue();

            // Mock readFile to return empty string for known_hosts
            jest.spyOn(sshConfigManager.fileSystem, "readFile").mockResolvedValue("");

            // Mock writeFile to do nothing
            jest.spyOn(sshConfigManager.fileSystem, "writeFile").mockResolvedValue();
        });

        it("should add IdentityFile to the config", async () => {
            // Mock platform as non-Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("darwin");

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [],
            };

            const result = await sshConfigManager.updateHostConfig(config, privateKeyPath);

            // Verify IdentityFile was added
            const identityFileOption = result.ssh_config_options.find(
                (opt) => opt.key === "IdentityFile"
            );
            expect(identityFileOption).toBeDefined();
            expect(identityFileOption?.value).toBe(privateKeyPath);
        });

        it("should update Mac/Linux config with appropriate defaults", async () => {
            // Mock platform as non-Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("darwin");

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [],
            };

            const result = await sshConfigManager.updateHostConfigForMacAndLinux(config);

            // Verify StrictHostKeyChecking was added with "no" value
            const strictHostKeyOption = result.ssh_config_options.find(
                (opt) => opt.key === "StrictHostKeyChecking"
            );
            expect(strictHostKeyOption).toBeDefined();
            expect(strictHostKeyOption?.value).toBe("no");

            // Verify UserKnownHostsFile was added with path to workspace-specific known_hosts
            const userKnownHostsOption = result.ssh_config_options.find(
                (opt) => opt.key === "UserKnownHostsFile"
            );
            expect(userKnownHostsOption).toBeDefined();
            expect(userKnownHostsOption?.value).toBe(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123"
            );
        });

        it("should handle Mac/Linux config with KnownHostsCommand", async () => {
            // Mock platform as non-Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("darwin");

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "KnownHostsCommand", value: "/usr/bin/printf '%H %s' 'key'" },
                ],
            };

            const result = await sshConfigManager.updateHostConfigForMacAndLinux(config);

            // Verify StrictHostKeyChecking was added with "yes" value
            const strictHostKeyOption = result.ssh_config_options.find(
                (opt) => opt.key === "StrictHostKeyChecking"
            );
            expect(strictHostKeyOption).toBeDefined();
            expect(strictHostKeyOption?.value).toBe("yes");

            // Verify UserKnownHostsFile was added with /dev/null
            const userKnownHostsOption = result.ssh_config_options.find(
                (opt) => opt.key === "UserKnownHostsFile"
            );
            expect(userKnownHostsOption).toBeDefined();
            expect(userKnownHostsOption?.value).toBe("/dev/null");
        });

        it("should update Windows config with ProxyCommand using X-Augment-Proxy-Hostname", async () => {
            // Mock platform as Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("win32");

            // Mock resolveOpenSSL to return a path
            jest.spyOn(sshConfigManager, "resolveOpenSSL").mockResolvedValue(
                "C:/Program Files/Git/bin/openssl.exe"
            );

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "X-Augment-Proxy-Hostname", value: "proxy.example.com" },
                ],
            };

            const result = await sshConfigManager.updateHostConfigForWindows(config);

            // Verify ProxyCommand was added with the correct OpenSSL path and proxy hostname
            const proxyCommandOption = result.ssh_config_options.find(
                (opt) => opt.key === "ProxyCommand"
            );
            expect(proxyCommandOption).toBeDefined();
            expect(proxyCommandOption?.value).toContain("C:/Program Files/Git/bin/openssl.exe");
            expect(proxyCommandOption?.value).toContain("proxy.example.com");
            expect(proxyCommandOption?.value).toContain("-verify_return_error");
        });

        it("should update Windows config with ProxyCommand using X-Augment-Proxy-OpenSSL-Command", async () => {
            // Mock platform as Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("win32");

            // Mock resolveOpenSSL to return a path
            jest.spyOn(sshConfigManager, "resolveOpenSSL").mockResolvedValue(
                "C:/Program Files/Git/usr/bin/openssl.exe"
            );

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "X-Augment-Proxy-OpenSSL-Command", value: "custom openssl command" },
                ],
            };

            const result = await sshConfigManager.updateHostConfigForWindows(config);

            // Verify ProxyCommand was added with the custom command
            const proxyCommandOption = result.ssh_config_options.find(
                (opt) => opt.key === "ProxyCommand"
            );
            expect(proxyCommandOption).toBeDefined();
            expect(proxyCommandOption?.value).toBe(
                `"C:/Program Files/Git/usr/bin/openssl.exe" custom openssl command`
            );
        });

        it("should handle host keys in Windows config", async () => {
            // Mock platform as Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("win32");

            // Mock resolveOpenSSL to return a path
            jest.spyOn(sshConfigManager, "resolveOpenSSL").mockResolvedValue(
                "C:/Program Files/Git/bin/openssl.exe"
            );

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "X-Augment-Proxy-Hostname", value: "proxy.example.com" },
                    {
                        key: "X-Augment-HostKey",
                        value: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...",
                    },
                    {
                        key: "X-Augment-HostKey",
                        value: "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAX...",
                    },
                    { key: "KnownHostsCommand", value: "/usr/bin/printf '%H %s' 'key'" },
                ],
            };

            const result = await sshConfigManager.updateHostConfigForWindows(config);

            // Verify KnownHostsCommand was removed
            const knownHostsCommandOption = result.ssh_config_options.find(
                (opt) => opt.key === "KnownHostsCommand"
            );
            expect(knownHostsCommandOption).toBeUndefined();

            // Verify writeFile was called to write host keys to workspace-specific known_hosts file
            expect(sshConfigManager.fileSystem.writeFile).toHaveBeenCalledWith(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123",
                expect.stringContaining("example.com ssh-rsa"),
                expect.anything()
            );
            expect(sshConfigManager.fileSystem.writeFile).toHaveBeenCalledWith(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123",
                expect.stringContaining("example.com ssh-ed25519"),
                expect.anything()
            );
        });

        it("should handle host keys with port in Windows config", async () => {
            // Mock platform as Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("win32");

            // Mock resolveOpenSSL to return a path
            jest.spyOn(sshConfigManager, "resolveOpenSSL").mockResolvedValue(
                "C:/Program Files/Git/bin/openssl.exe"
            );

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "X-Augment-Proxy-Hostname", value: "proxy.example.com" },
                    {
                        key: "X-Augment-HostKey",
                        value: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...",
                    },
                    {
                        key: "X-Augment-HostKey",
                        value: "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAX...",
                    },
                    { key: "Port", value: "2222" },
                ],
            };

            await sshConfigManager.updateHostConfigForWindows(config);

            // Verify writeFile was called with the correct format for host keys with port
            // Format should be [hostname]:port key
            expect(sshConfigManager.fileSystem.writeFile).toHaveBeenCalledWith(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123",
                expect.stringContaining("[example.com]:2222 ssh-rsa"),
                expect.anything()
            );
            expect(sshConfigManager.fileSystem.writeFile).toHaveBeenCalledWith(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123",
                expect.stringContaining("[example.com]:2222 ssh-ed25519"),
                expect.anything()
            );
        });

        it("should throw error when OpenSSL is not found on Windows", async () => {
            // Mock platform as Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("win32");

            // Mock resolveOpenSSL to return null (not found)
            jest.spyOn(sshConfigManager, "resolveOpenSSL").mockResolvedValue(null);

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "X-Augment-Proxy-Hostname", value: "proxy.example.com" },
                ],
            };

            await expect(sshConfigManager.updateHostConfigForWindows(config)).rejects.toThrow(
                "Git for Windows is required"
            );
        });

        it("should throw error when proxy information is missing on Windows", async () => {
            // Mock platform as Windows
            jest.spyOn(sshConfigManager.fileSystem, "getPlatform").mockResolvedValue("win32");

            // Mock resolveOpenSSL to return a path
            jest.spyOn(sshConfigManager, "resolveOpenSSL").mockResolvedValue(
                "C:/Program Files/Git/bin/openssl.exe"
            );

            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [],
            };

            await expect(sshConfigManager.updateHostConfigForWindows(config)).rejects.toThrow(
                "X-Augment-Proxy-Hostname and X-Augment-Proxy-OpenSSL-Command not found"
            );
        });

        it("Should strip X-Augment-* options and remove IgnoreUnknown if set to X-Augment-*", async () => {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            const config: RemoteAgentSSHConfig = {
                hostname: "example.com",
                // eslint-disable-next-line @typescript-eslint/naming-convention
                public_keys: ["ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."],
                // eslint-disable-next-line @typescript-eslint/naming-convention
                ssh_config_options: [
                    { key: "X-Augment-Proxy-Hostname", value: "proxy.example.com" },
                    { key: "X-Augment-Proxy-OpenSSL-Command", value: "custom openssl command" },
                    {
                        key: "X-Augment-HostKey",
                        value: "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...",
                    },
                    { key: "IgnoreUnknown", value: "X-Augment-*" },
                ],
            };

            const result = await sshConfigManager.updateHostConfig(config, "/path/to/private/key");

            // Verify X-Augment-* options were removed
            const xAugmentOptions = result.ssh_config_options.filter((opt) =>
                opt.key.startsWith("X-Augment-")
            );
            expect(xAugmentOptions).toHaveLength(0);

            // Verify IgnoreUnknown was removed
            const ignoreUnknownOption = result.ssh_config_options.find(
                (opt) => opt.key === "IgnoreUnknown"
            );
            expect(ignoreUnknownOption).toBeUndefined();
        });
    });

    describe("workspace-specific known hosts", () => {
        beforeEach(() => {
            // Mock ensureSshKnownHostsDirExists to do nothing
            jest.spyOn(
                sshConfigManager.fileSystem,
                "ensureSshKnownHostsDirExists"
            ).mockResolvedValue();

            // Mock readFile to return empty string for known_hosts
            jest.spyOn(sshConfigManager.fileSystem, "readFile").mockResolvedValue("");

            // Mock writeFile to do nothing
            jest.spyOn(sshConfigManager.fileSystem, "writeFile").mockResolvedValue();
        });

        it("should write known hosts to workspace-specific file", async () => {
            // Mock getSshKnownHostsPath to return workspace-specific path
            jest.spyOn(sshConfigManager.fileSystem, "getSshKnownHostsPath").mockResolvedValue(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123"
            );

            const knownHostsContent = "example.com ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ...";
            await sshConfigManager.writeKnownHostsForCurrentWorkspace(knownHostsContent);

            // Verify ensureSshKnownHostsDirExists was called
            expect(sshConfigManager.fileSystem.ensureSshKnownHostsDirExists).toHaveBeenCalled();

            // Verify writeFile was called with workspace-specific path
            expect(sshConfigManager.fileSystem.writeFile).toHaveBeenCalledWith(
                "/home/<USER>/.augment/ssh/known_hosts.d/workspace-abc123",
                expect.stringContaining(knownHostsContent),
                0o644
            );
        });
    });
});

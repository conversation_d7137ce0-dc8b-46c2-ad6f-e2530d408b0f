import { APIServer } from "$vscode/src/augment-api";
import { getLogger } from "$vscode/src/logging";

import * as vscode from "vscode";

import { AugmentGlobalState } from "../context";
import { SSHConfigManager } from "./ssh-config-manager";
import { SshFileSystem } from "./ssh-file-system";
import { DEFAULT_KEY_OPTIONS, SshKeyGenerator } from "./ssh-key-generator";
import { setRemotePlatform } from "./ssh-utils";
import { SshKeygenResult } from "./types";

const REMOTE_SSH_EXTENSION_ID = "ms-vscode-remote.remote-ssh";

/**
 * Manages SSH connections to remote agent environments
 */
export class RemoteAgentSshManager {
    private sshKeyGenerator: SshKeyGenerator;
    private sshConfigManager: SSHConfigManager;
    private sshFileSystem: SshFileSystem;
    private logger = getLogger("RemoteAgentSshManager");

    /**
     * @param api APIServer
     * @param globalState Optional global state for storing remote home directory info
     */
    constructor(
        private api: APIServer,
        globalState?: AugmentGlobalState
    ) {
        this.sshKeyGenerator = new SshKeyGenerator();
        this.sshConfigManager = new SSHConfigManager(globalState);
        this.sshFileSystem = new SshFileSystem(globalState);
    }

    /**
     * Checks if the Remote SSH extension is installed
     *
     * @returns True if the Remote SSH extension is installed, false otherwise
     */
    private isRemoteSshExtensionInstalled(): boolean {
        const extension = vscode.extensions.getExtension(REMOTE_SSH_EXTENSION_ID);
        return extension !== undefined;
    }

    /**
     * Installs the Remote SSH extension
     */
    private async installRemoteSshExtension(): Promise<void> {
        await vscode.commands.executeCommand(
            "workbench.extensions.installExtension",
            REMOTE_SSH_EXTENSION_ID
        );
    }

    /**
     * Connect to a remote agent by ID
     *
     * @param remoteAgentId The ID of the remote agent to connect to
     * @returns A promise that resolves when the connection is established
     */
    async connectToRemoteAgent(remoteAgentId: string): Promise<void> {
        const { keysExist } = await this.getKeyPairInfo();

        const doesUserHaveRemoteSshExtension =
            this.isRemoteSshExtensionInstalled() ||
            // If we're in a remote environment and the keys already exist,
            // we assume the user has the extension installed on their local machine.
            (this.isRemoteEnvironment() && keysExist);

        // Check if the Remote SSH extension is installed
        if (!doesUserHaveRemoteSshExtension) {
            this.logger.info("Remote SSH extension is not installed");
            const installButton = "Install Remote SSH Extension";
            const result = await vscode.window.showInformationMessage(
                "The Remote SSH extension is required to connect to remote agents. Would you like to install it now?",
                { modal: true },
                installButton
            );

            if (result === installButton) {
                await this.installRemoteSshExtension();
            } else {
                // User chose not to install the extension
                throw new Error("Remote SSH extension is required but not installed");
            }
        }

        try {
            const keyPair = await this.getKeyPair();
            const { privateKeyPath, publicKey } = keyPair;

            const isUserOnWindows = (await this.sshFileSystem.getPlatform()) === "win32";
            let pathToOpenSSL: string | null = null;
            if (isUserOnWindows) {
                pathToOpenSSL = await this.sshConfigManager.resolveOpenSSL();
                if (!pathToOpenSSL) {
                    // Show an error to tell the user to install Git for Windows, because we rely on OpenSSL from Git for Windows
                    // for the ProxyCommand to work.
                    await vscode.window.showErrorMessage(
                        "Git for Windows is required to connect to remote agents on Windows. Please install it and try again."
                    );
                    return;
                }
            }

            // Set the remote platform to Linux to avoid OS detection prompt
            await setRemotePlatform(remoteAgentId, "linux");

            const response = await this.api.remoteAgentAddSSHKey(remoteAgentId, [publicKey]);
            const hostConfig = await this.sshConfigManager.updateHostConfig(
                response.ssh_config,
                privateKeyPath
            );

            await this.sshConfigManager.connectToHost(remoteAgentId, hostConfig);
        } catch (error) {
            // Show a more detailed error message with a button to retry
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`Failed to connect to remote agent: ${errorMessage}`);

            const retryButton = "Retry";
            const result = await vscode.window.showErrorMessage(
                `Failed to connect to remote agent: ${errorMessage}`,
                { modal: false },
                retryButton
            );

            if (result === retryButton) {
                // Retry the connection
                return this.connectToRemoteAgent(remoteAgentId);
            }

            // Re-throw the error to propagate it to the caller
            throw error;
        }
    }

    private isRemoteEnvironment(): boolean {
        return this.sshFileSystem.isRemoteEnvironment();
    }

    private async getKeyPairInfo(): Promise<{
        privateKeyPath: string;
        publicKeyPath: string;
        keysExist: boolean;
    }> {
        const homeDir = await this.sshFileSystem.getHomeDirectory();
        const keysDir = await this.sshFileSystem.joinPath(homeDir, ".augment", "ssh-keys");
        const privateKeyPath = await this.sshFileSystem.joinPath(
            keysDir,
            DEFAULT_KEY_OPTIONS.filename
        );
        const publicKeyPath = `${privateKeyPath}.pub`;

        const privateKeyExists = await this.sshFileSystem.fileExists(privateKeyPath);
        const publicKeyExists = await this.sshFileSystem.fileExists(publicKeyPath);

        return {
            privateKeyPath,
            publicKeyPath,
            keysExist: privateKeyExists && publicKeyExists,
        };
    }

    /**
     * Gets the SSH key pair for remote agent connections
     *
     * This will use existing keys if they're already present,
     * or generate new ones if needed.
     * If we're in a remote environment and keys don't already exist,
     * it will throw an error.
     *
     * @returns The key pair information
     * @throws Error if running in a remote environment and keys don't already exist locally
     */
    private async getKeyPair(): Promise<SshKeygenResult> {
        const { privateKeyPath, publicKeyPath, keysExist } = await this.getKeyPairInfo();

        if (keysExist) {
            const publicKey = await this.sshFileSystem.readFile(publicKeyPath);
            return {
                privateKeyPath,
                publicKeyPath,
                publicKey: publicKey.trim(),
            };
        }

        // We're in a remote environment and the keys don't already exist.
        // So we can't do anything here.
        if (this.isRemoteEnvironment()) {
            await vscode.window.showErrorMessage(
                "Start by connecting from your local environment. After that, you'll be able to access remote agents even while working remotely."
            );
            throw new Error(
                "SSH key generation is not supported in remote environments. " +
                    "Please generate keys on your local machine."
            );
        }

        return await this.sshKeyGenerator.generateKeyPair();
    }
}

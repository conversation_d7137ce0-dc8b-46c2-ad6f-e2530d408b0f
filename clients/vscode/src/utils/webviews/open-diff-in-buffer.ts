import * as vscode from "vscode";

export type LineRange = {
    start: number;
    stop: number;
};

/**
 * Opens a diff view in VS Code showing the difference between old and new content.
 * This version uses local file for the left side and a virtual document for the right side.
 * Note: The oldContents parameter is not used in this function as it reads from the local file.
 *
 * @param oldContents - The original content (not used, reads from local file instead)
 * @param newContents - The modified content
 * @param filePath - The path to display in the diff title
 */
export async function openDiffInBuffer(
    oldContents: string,
    newContents: string,
    filePath: string
): Promise<void> {
    const scheme = "virtual"; // Custom URI scheme
    const provider = new VirtualDocumentProvider();
    vscode.workspace.registerTextDocumentContentProvider(scheme, provider);

    // Create two virtual URIs for the left and right sides of the diff
    // Keep the original file extension in the URI to help VS Code detect the language
    const leftUri = vscode.Uri.parse(`${scheme}:/${filePath}?left`);
    const rightUri = vscode.Uri.parse(`${scheme}:/${filePath}?right`);

    // Set the content for both sides
    provider.setDocumentContent(leftUri, oldContents || "");
    provider.setDocumentContent(rightUri, newContents || "");

    // Open the diff view with both virtual documents
    await vscode.commands.executeCommand("vscode.diff", leftUri, rightUri, `Diff - ${filePath}`);
}

class VirtualDocumentProvider implements vscode.TextDocumentContentProvider {
    private documents: Map<string, string> = new Map();

    setDocumentContent(uri: vscode.Uri, content: string): void {
        this.documents.set(uri.toString(), content);
    }

    provideTextDocumentContent(uri: vscode.Uri): string | undefined {
        return this.documents.get(uri.toString());
    }
}

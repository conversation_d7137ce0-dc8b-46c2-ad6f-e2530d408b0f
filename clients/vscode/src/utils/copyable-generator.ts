enum GeneratorStatus {
    running = "running",
    success = "success",
    errored = "errored",
}

/**
 * A class that allows for copying an async generator.
 * This is useful when you want to iterate over the same generator multiple times.
 *
 * @template T The type of the elements yielded by the generator.
 */
class CopyableGenerator<T> {
    private _status = GeneratorStatus.running; // Indicates the status of the generator.
    private _buffer = new Array<T>(); // Stores the values yielded by the generator.

    private _triggerYield: Promise<void>; // Promise that triggers the next yield.
    private _resolveFn: any; // Function to resolve the _triggerYield promise.
    private _rejectFn: any; // Function to reject the _triggerYield promise.

    /**
     * Constructs a new CopyableGenerator.
     *
     * @param base The async generator to be copied.
     */
    constructor(base: AsyncIterable<T>) {
        this._triggerYield = new Promise((res, rej) => {
            this._resolveFn = res;
            this._rejectFn = rej;
        });
        void this.collectBaseGenerator(base);
    }

    /**
     * Cancels the generator.
     * This will cause the generator to stop yielding values and throw an error.
     */
    public cancel = () => {
        if (this.isFinished) {
            return;
        }
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        this._reject(new Error("Cancelled"));
    };

    /**
     * Returns a boolean indicating whether the generator has finished yielding values.
     *
     * @returns boolean
     */
    public get isFinished(): boolean {
        return this._status !== GeneratorStatus.running;
    }

    /**
     * Returns a boolean indicating whether the generator has errored.
     *
     * @returns boolean
     */
    public get hasErrored(): boolean {
        return this._status === GeneratorStatus.errored;
    }

    /**
     * Collects the values yielded by the base generator and stores them in the buffer.
     *
     * @param base The async generator to be copied.
     */
    private collectBaseGenerator = async (base: AsyncIterable<T>): Promise<void> => {
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-call
            this._resolve();
            this._triggerYield = new Promise((res, rej) => {
                this._resolveFn = res;
                this._rejectFn = rej;
            });

            for await (const v of base) {
                this._buffer.push(v);
                // eslint-disable-next-line @typescript-eslint/no-unsafe-call
                this._resolve();
                this._triggerYield = new Promise((res, rej) => {
                    this._resolveFn = res;
                    this._rejectFn = rej;
                });
            }
            this._status = GeneratorStatus.success;
            this._resolve();
        } catch (e) {
            this._reject(e);
        }
    };

    private _reject = (e: any) => {
        this._status = GeneratorStatus.errored;
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        this._rejectFn(e);
    };

    private _resolve = () => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call
        this._resolveFn();
    };

    /**
     * Creates a new generator that yields the same values as the original generator.
     *
     * @returns A new async generator that yields the same values as the original generator.
     */
    async *copy(): AsyncGenerator<T> {
        let currIdx: number = 0; // Index of the current value in the buffer.
        while (true) {
            if (currIdx < this._buffer.length) {
                yield this._buffer[currIdx];
                currIdx += 1;
                continue;
            }
            // Wait for the next value to be added to the buffer, or
            // for the base generator to complete. If the base generator
            // has completed, this will resolve immediately and continue.
            await this._triggerYield;
            if (currIdx === this._buffer.length && this.isFinished) {
                return;
            }
        }
    }
}

export { CopyableGenerator };
export default CopyableGenerator;

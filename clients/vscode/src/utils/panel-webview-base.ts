import * as path from "path";
import * as vscode from "vscode";

import { getLogger } from "../logging";
import { DisposableService } from "./disposable-service";
import { readFileUtf8 } from "./fs-utils";
import { isHmr, loadHmrUrl, loadWebview, loadWebviewHmr } from "./load-webview-hmr";
import {
    addGoogleFavicons,
    addGoogleFonts,
    addImgData,
    addLocalhostRequirements,
    addMonacoCdnRequirements,
    addMonacoRequirements,
    addRiveCdnRequirements,
    addWebViewCSP,
    generateCSPPolicy,
} from "./webviews/csp";

export class PanelWebviewBase extends DisposableService {
    private _logger = getLogger("PanelWebviewBase");

    constructor(
        private filename: string,
        protected readonly _webview: vscode.Webview
    ) {
        super();
    }

    protected generateCSPPolicy(hmrUrl?: string) {
        const csp = generateCSPPolicy(
            addWebViewCSP(this._webview),
            addGoogleFonts(),
            addGoogleFavicons(),
            addImgData(),
            addMonacoRequirements(),
            addMonacoCdnRequirements(),
            addRiveCdnRequirements(),
            hmrUrl ? addLocalhostRequirements(hmrUrl) : () => {}
        );
        return csp;
    }

    async loadHTML(extensionUri: vscode.Uri): Promise<void> {
        if (isHmr()) {
            try {
                const hmrUrl = await loadHmrUrl(extensionUri);
                // Load from dev server
                this._logger.debug("Loading '%s' from dev server on '%s'", this.filename, hmrUrl);
                this._webview.options = {
                    enableScripts: true,
                    localResourceRoots: [vscode.Uri.parse(hmrUrl)],
                };
                this._webview.html = await loadWebviewHmr(
                    hmrUrl,
                    this.filename,
                    this.generateCSPPolicy(hmrUrl)
                );

                return;
            } catch (error) {
                this._logger.error(
                    `Failed to load '%s' from dev server: ${String(error)} `,
                    this.filename
                );
                // On failure to load from the HMR server we fallback to a
                // static file which indicates to developers that HMR failed and
                // provides advice on how to troubleshoot.
                this._webview.html = (
                    await this._loadFile(extensionUri, "../../common/webviews/index.html")
                ).replace("></code>", `>${String(error)}</code>`);
                return;
            }
        }

        this._webview.html = await this._loadFile(extensionUri, this.filename);
    }
    private async _loadFile(extensionUri: vscode.Uri, file: string) {
        const basePath = vscode.Uri.joinPath(extensionUri, "common-webviews");

        // The slash is used to ensure relative paths are resolved correctly
        const baseHref = this._webview.asWebviewUri(vscode.Uri.joinPath(basePath, "/"));
        this._webview.options = {
            enableScripts: true,
            localResourceRoots: [basePath],
        };

        const html = await readFileUtf8(path.join(basePath.fsPath, file));
        try {
            return loadWebview(html, baseHref.toString(), this.generateCSPPolicy());
        } catch (e) {
            this._logger.error(`Failed to load ${file}: ${String(e)}`);
            throw e;
        }
    }
}

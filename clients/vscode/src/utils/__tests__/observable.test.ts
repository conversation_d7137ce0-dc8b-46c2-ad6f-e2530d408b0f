import { Observable } from "../observable";

describe("Observable", () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test("constructor initializes with correct value", () => {
        const obs = new Observable(42);
        expect(obs.value).toBe(42);
    });

    test("value getter returns current value", () => {
        const obs = new Observable<string>("test");
        expect(obs.value).toBe("test");
    });

    test("value setter updates value and notifies listeners", () => {
        const obs = new Observable<number>(0);
        const listener = jest.fn();
        obs.listen(listener);

        obs.value = 42;

        expect(obs.value).toBe(42);
        expect(listener).toHaveBeenCalledWith(42, 0);
    });

    test("value setter does not notify if value is unchanged", () => {
        const obs = new Observable<number>(42);
        const listener = jest.fn();
        obs.listen(listener);

        obs.value = 42;

        expect(listener).not.toHaveBeenCalled();
    });

    test("value setter notifies if custom equality function says the new value is different", () => {
        const obs = new Observable<number>(42, (a, b) => a % 2 === b % 2);
        const listener = jest.fn();
        obs.listen(listener);

        obs.value = 45;

        expect(listener).toHaveBeenCalled();
    });

    test("value setter does not notify if custom equality function says the new value is the same", () => {
        const obs = new Observable<number>(42, (a, b) => a % 2 === b % 2);
        const listener = jest.fn();
        obs.listen(listener);

        obs.value = 44;

        expect(listener).not.toHaveBeenCalled();
    });

    test("listen adds listener and returns unsubscribe function", () => {
        const obs = new Observable<number>(0);
        const listener = jest.fn();

        const unsubscribe = obs.listen(listener);
        obs.value = 42;

        expect(listener).toHaveBeenCalledWith(42, 0);

        unsubscribe();
        obs.value = 84;

        expect(listener).toHaveBeenCalledTimes(1);
    });

    test("listen with fire=true calls listener immediately", () => {
        const obs = new Observable<number>(42);
        const listener = jest.fn();

        obs.listen(listener, true);

        expect(listener).toHaveBeenCalledWith(42, 42);
    });

    test("dispose removes all listeners", () => {
        const obs = new Observable<number>(0);
        const listener1 = jest.fn();
        const listener2 = jest.fn();

        obs.listen(listener1);
        obs.listen(listener2);

        obs.dispose();
        obs.value = 42;

        expect(listener1).not.toHaveBeenCalled();
        expect(listener2).not.toHaveBeenCalled();
    });

    test("waitUntil resolves when predicate is satisfied", async () => {
        const obs = new Observable<number>(0);
        const predicate = (v: number) => v > 10;

        const promise = obs.waitUntil(predicate);
        obs.value = 5;
        obs.value = 15;

        await expect(promise).resolves.toBe(15);
    });

    test("waitUntil rejects on timeout", async () => {
        const obs = new Observable<number>(0);
        const predicate = (v: number) => v > 10;

        jest.useFakeTimers();
        const promise = obs.waitUntil(predicate, 100);
        jest.runAllTimers();

        await expect(promise).rejects.toThrow("Timeout exceeded.");
    });

    test("waitUntil resolves immediately if predicate is already satisfied", async () => {
        const obs = new Observable(15);

        await expect(obs.waitUntil((v) => v > 10)).resolves.toBe(15);
    });

    test("watch multiple values", () => {
        const obs1 = new Observable("test");
        const obs2 = new Observable(2);
        const obs = Observable.watch((a: string, b: number) => a + b, obs1, obs2);

        expect(obs.value).toBe("test2");

        obs2.value = 4;
        expect(obs.value).toBe("test4");

        obs1.dispose();

        obs1.value = "foo";
        expect(obs.value).toBe("test4");
    });
});

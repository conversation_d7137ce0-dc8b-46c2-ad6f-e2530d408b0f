import * as vscode from "vscode";

import { LineChanges } from "../webview-providers/webview-messages";

export function getSelectionForChanges(
    lineChanges: LineChanges | undefined,
    preferModified: boolean
) {
    if (!lineChanges || lineChanges.lineChanges.length === 0) {
        return [];
    }
    return lineChanges.lineChanges.map((change) => {
        let startLine = preferModified ? change.modifiedStart : change.originalStart;
        let endLine = preferModified ? change.modifiedEnd : change.originalEnd;
        if (endLine === 0) {
            endLine = startLine;
        }
        // Add the initial offset.
        startLine += lineChanges.lineOffset;
        endLine += lineChanges.lineOffset;
        // We also have to convert to 0-based.
        startLine--;
        endLine--;
        return new vscode.Selection(
            new vscode.Position(startLine, 0),
            new vscode.Position(endLine, 9999)
        );
    });
}

/** Create a range between the starting characters of the given lines.  */
export function startOfLineRange(lineStart: number): vscode.Range {
    const pos = new vscode.Position(lineStart, 0);
    return new vscode.Range(pos, pos);
}

/** Create a range between the ending characters of the given lines.  */
export function endOfLineRange(editor: vscode.TextEditor, lineStart: number): vscode.Range {
    const pos = editor.document.lineAt(lineStart).range.end;
    return new vscode.Range(pos, pos);
}

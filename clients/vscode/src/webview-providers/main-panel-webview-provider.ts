import * as vscode from "vscode";

import { getLogger } from "../logging";
import { MainPanelAppController } from "../main-panel/main-panel-app-controller";
import { MainPanelWebview } from "../main-panel/main-panel-webview";
import { DisposableService } from "../utils/disposable-service";

export class MainPanelWebviewProvider extends DisposableService {
    private _logger = getLogger("MainPanelWebviewProvider");
    protected _webviewView: vscode.WebviewView | undefined = undefined;
    protected _mainPanelWebview: MainPanelWebview | undefined = undefined;
    private currentApp: MainPanelAppController | undefined = undefined;
    private visibilityEventEmitter: vscode.EventEmitter<boolean> =
        new vscode.EventEmitter<boolean>();

    constructor(private readonly _extensionUri: vscode.Uri) {
        super();
        this.addDisposable(this.visibilityEventEmitter);
    }

    public get onVisibilityChange(): vscode.Event<boolean> {
        return this.visibilityEventEmitter.event;
    }

    public isVisible(): boolean {
        return !!this._webviewView?.visible;
    }

    public changeApp(app: MainPanelAppController | undefined) {
        this.currentApp?.dispose();

        if (app) {
            this.addDisposable(app);
        }

        this._setViewTitle(app?.title() || "");
        this.currentApp = app;
        this._mainPanelWebview?.changeApp(app);
    }

    private _setViewTitle(title: string) {
        if (!this._webviewView) {
            return;
        }
        this._webviewView.title = title;
    }

    async resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext<unknown>,
        _token: vscode.CancellationToken
    ): Promise<void> {
        webviewView.onDidDispose(() => {
            if (this._webviewView === webviewView) {
                this._logger.debug("Disposing of main panel webview view");
                this._webviewView = undefined;
            }
        });

        this._webviewView = webviewView;

        this._setViewTitle(this.currentApp?.title() || "");

        // Dispose any existing panel webview
        this._mainPanelWebview?.dispose();

        // Start with auth if we need to sign in, otherwise, load chat
        this._mainPanelWebview = new MainPanelWebview(this._webviewView.webview);
        this.addDisposable(this._mainPanelWebview);
        this._mainPanelWebview.changeApp(this.currentApp);
        this._mainPanelWebview.addDisposable(
            this._webviewView.onDidChangeVisibility(() => {
                this.visibilityEventEmitter.fire(!!this._webviewView?.visible);
            })
        );
        await this._mainPanelWebview.loadHTML(this._extensionUri);
    }
}

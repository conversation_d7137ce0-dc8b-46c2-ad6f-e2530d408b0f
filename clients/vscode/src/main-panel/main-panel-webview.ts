/* eslint-disable @typescript-eslint/no-unsafe-call */
import * as vscode from "vscode";

import { MainPanelAppController } from "../main-panel/main-panel-app-controller";
import { setContext } from "../utils/context";
import { PanelWebviewBase } from "../utils/panel-webview-base";
import { type WebViewMessage, WebViewMessageType } from "../webview-providers/webview-messages";

export class MainPanelWebview extends PanelWebviewBase {
    private _currentApp: MainPanelAppController | undefined;

    constructor(_webview: vscode.Webview) {
        super("main-panel.html", _webview);

        this.addDisposable(
            this._webview.onDidReceiveMessage((msg: WebViewMessage) =>
                this.onDidReceiveMessage(msg)
            )
        );
    }

    changeApp(app: MainPanelAppController | undefined) {
        this._currentApp = app;
        if (this._currentApp) {
            this._currentApp.register(this._webview);
            setContext("vscode-augment.mainPanel.app", this._currentApp.appType());
        }
        this._postAppTypeMsg();
    }

    private onDidReceiveMessage(message: WebViewMessage): void {
        switch (message.type) {
            case WebViewMessageType.mainPanelLoaded:
                this._postAppTypeMsg();
                break;
        }
    }

    private _postAppTypeMsg() {
        void this._webview.postMessage({
            type: WebViewMessageType.mainPanelDisplayApp,
            data: this._currentApp?.appType(),
        });
    }
}

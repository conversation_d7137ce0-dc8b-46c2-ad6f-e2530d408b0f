import { ReplacementText } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { CompletionItem, CompletionLocation, CompletionResult } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { CompletionServer } from "../completion-server";
import { SkipCompletion } from "../exceptions";
import { AugmentExtension } from "../extension";
import { type AugmentLogger, getLogger } from "../logging";
import { ClientMetricsReporter } from "../metrics/client-metrics-reporter";
import { getActiveNotebookCodeTextAndPrefixLength } from "../utils/notebook";
import { measureExecTime } from "../utils/performance";
import { makeReplacementText } from "../utils/replacement-text";
import { Chunk, WorkspaceContext } from "../workspace/workspace-context";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { AugmentCompletion } from "./augment-completion";
import { CompletionTimeline } from "./completion-timeline";

export class CompletionsModel {
    private _logger = getLogger("CompletionsModel");
    private _completionSerial = 0;

    constructor(
        private _extension: AugmentExtension,
        private _configListener: AugmentConfigListener,
        private _metricsReporter: ClientMetricsReporter
    ) {
        this.generateCompletion = measureExecTime(
            this.generateCompletion.bind(this),
            (duration) => {
                this._metricsReporter.report({
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    client_metric: "generate_completion_latency",
                    value: duration,
                });
            }
        );
    }

    async generateCompletion(
        document: vscode.TextDocument,
        position: vscode.Position,
        completionTimeline?: CompletionTimeline
    ): Promise<CompletionRequest | undefined> {
        const workspaceManager = this._extension.workspaceManager;
        if (workspaceManager === undefined) {
            // Extension has been disabled
            return undefined;
        }
        const completionServer = workspaceManager.completionServer;
        const requestId = completionServer.createRequestId();

        const qualifiedPath = workspaceManager.safeResolvePathName(document.uri);
        if (qualifiedPath === undefined) {
            // Unsupported uri scheme
            return undefined;
        }
        const [repoRoot, relPath] = [qualifiedPath.rootPath, qualifiedPath.relPath];

        let cursorPosition = document.offsetAt(position);
        const [prefix, suffix, prefixBegin, suffixEnd, cursorOffset] = this._extractPrefixAndSuffix(
            document,
            cursorPosition
        );
        cursorPosition += cursorOffset;
        const completionLocation = { prefixBegin, cursorPosition, suffixEnd };

        const completionResult = await this._requestCompletion(
            workspaceManager,
            completionServer,
            requestId,
            document,
            prefix,
            suffix,
            completionLocation,
            qualifiedPath,
            completionTimeline
        );

        const completionItems = completionResult.completionItems;
        if (completionItems.length === 0) {
            return {
                completions: [],
                document,
                requestId,
                repoRoot,
                pathName: relPath,
                prefix,
                suffix,
                occuredAt: new Date(),
                isReused: false,
            };
        }

        // We only return the first completion to VSCode. As of this writing, this is a no-op
        // because we do not yet support mutliple completions on the server. Returning just the
        // first completion is meant to guard against future changes that can break in a subtle
        // way.
        //
        // Rationale: On the client, we try to reuse the last completion. If we return multiple
        // completions, completion-reuse and completion-list can interact in unexpected ways.
        // So for now, we explicitly disable multiple completions.
        if (completionItems.length > 1) {
            this._logger.warn("Multiple completions not supported, ignoring all but the first");
        }

        const completionsList: AugmentCompletion[] = [];
        const idx = 0; // only use the first completion
        const rawItem = completionItems[idx];

        // VSCode silently hides the completion if the skipped suffix spans more than one line.
        // Drop the skip when this is the case.
        if (rawItem.skippedSuffix.includes("\n")) {
            this._logger.debug(`Skipped suffix spans multiple lines, dropping it`);
            rawItem.skippedSuffix = "";
            rawItem.suffixReplacementText = "";
        }

        completionsList.push(
            new AugmentCompletion(
                rawItem.text,
                rawItem.suffixReplacementText,
                rawItem.skippedSuffix,
                {
                    startOffset: document.offsetAt(position),
                    endOffset: document.offsetAt(position),
                }
            )
        );

        this._logger.debug(`Returning ${completionsList.length} completion(s)`);
        return {
            occuredAt: new Date(),
            completions: completionsList,
            document,
            requestId,
            repoRoot,
            pathName: relPath,
            prefix,
            suffix,
            isReused: false,
        };
    }

    private async _requestCompletion(
        workspaceManager: WorkspaceManager,
        completionServer: CompletionServer,
        requestId: string,
        document: vscode.TextDocument,
        prefix: string,
        suffix: string,
        completionLocation: CompletionLocation,
        qualifiedPath: QualifiedPathName,
        completionTimeline?: CompletionTimeline
    ): Promise<CompletionResult> {
        const languageId = document.languageId;
        const disableByLang = this._configListener.config.completions.disableCompletionsByLanguage;
        if (disableByLang.has(languageId)) {
            // Completions are disabled for this language by the user's settings
            throw new SkipCompletion(`Language ${languageId} is disabled.`);
        }

        const completionSerial = this._completionSerial++;

        this._logger.debug(
            `Requesting new completion - #${completionSerial} submitted; requestId: ${requestId}`
        );

        const blobRange = workspaceManager.translateRange(
            qualifiedPath,
            completionLocation.prefixBegin,
            completionLocation.suffixEnd
        );
        const translatedLocation =
            blobRange === undefined
                ? completionLocation
                : {
                      prefixBegin: blobRange.beginOffset,
                      cursorPosition: completionLocation.cursorPosition,
                      suffixEnd: blobRange.endOffset,
                  };

        const context = workspaceManager.getContext();
        const blobs = context.blobs;
        const recentChanges = this._getRecentChanges(context);

        const enableCompletionFileEditEvents = workspaceManager.getEnableCompletionFileEditEvents();
        const fileEditEvents = enableCompletionFileEditEvents
            ? workspaceManager.getFileEditEvents()
            : undefined;

        try {
            const completionResult = await completionServer.complete(
                requestId,
                prefix,
                suffix,
                qualifiedPath.relPath,
                blobRange?.blobName,
                translatedLocation,
                languageId,
                blobs,
                recentChanges,
                fileEditEvents,
                undefined,
                undefined,
                completionTimeline
            );

            if (completionResult.unknownBlobNames.length > 0) {
                void workspaceManager.handleUnknownBlobs(
                    context,
                    completionResult.unknownBlobNames
                );
            }

            if (completionResult.checkpointNotFound) {
                void workspaceManager.handleUnknownCheckpoint(requestId, blobs.checkpointId!);
            }

            // Verify that skip tokens are handled correctly
            _verifySkipSuffix(completionResult.completionItems, suffix, this._logger);

            this._extension.updateModelInfo(completionResult);

            // Filter out any empty completions
            completionResult.completionItems = completionResult.completionItems.filter((item) => {
                return (item.text + item.suffixReplacementText).length > 0;
            });

            return completionResult;
        } catch (e: any) {
            if (APIError.isAPIErrorWithStatus(e, APIStatus.cancelled)) {
                this._logger.debug(
                    `Completion #${completionSerial} ` +
                        `cancelled in back end; requestId ${requestId}`
                );
                throw new SkipCompletion("Cancelled in back end");
            } else if (APIError.isRetriableAPIError(e)) {
                this._logger.debug(
                    `Completion #${completionSerial} ` +
                        `retriable error on back end; requestId ${requestId}`
                );
                throw new SkipCompletion("Retriable error on back end");
            }
            const msg = getErrmsg(e);
            this._logger.warn(
                `Completion #${completionSerial} failed: ` + `${msg}; requestId ${requestId}`
            );
            throw e;
        }
    }

    private _extractPrefixAndSuffix(
        document: vscode.TextDocument,
        position: number
    ): [string, string, number, number, number] {
        const modelInfo = this._extension.modelInfo!;
        const maxPromptChars = modelInfo.suggestedPrefixCharCount;
        const maxSuffixChars = modelInfo.suggestedSuffixCharCount;

        const [fullNotebookText, notebookPrefixLength] =
            getActiveNotebookCodeTextAndPrefixLength(document);

        // If we're in a notebook code cell, adjust the offsets to account
        // for the expanded prefix.  This lets us reuse the shifting logic below.
        // If we're in a notebook text cell, we do nothing special (so we see only that cell).
        if (fullNotebookText !== undefined) {
            position += notebookPrefixLength;
        }

        // We need to shift this by the prefix length, because VSCode
        // reports the cursor position to be before the word when suggestions
        // are triggered, instead of where the cursor actually is.
        const prefixStartOffset = Math.max(0, position - maxPromptChars);
        const suffixEndOffset = position + maxSuffixChars;

        if (fullNotebookText !== undefined) {
            const prefix = fullNotebookText.slice(prefixStartOffset, position);
            const suffix = fullNotebookText.slice(position, suffixEndOffset);
            return [
                prefix,
                suffix,
                prefixStartOffset,
                position + suffix.length,
                notebookPrefixLength,
            ];
        }

        const prefixStartPosition = document.positionAt(prefixStartOffset);
        const prefixEndPosition = document.positionAt(position);

        const suffixStartPosition = document.positionAt(position);
        const suffixEndPosition = document.positionAt(suffixEndOffset);

        const prefixRange = new vscode.Range(prefixStartPosition, prefixEndPosition);
        const suffixRange = new vscode.Range(suffixStartPosition, suffixEndPosition);

        const prefix = document.getText(prefixRange);
        const suffix = document.getText(suffixRange);

        return [prefix, suffix, prefixStartOffset, position + suffix.length, 0];
    }

    // _getRecentChanges returns the recent changes from the given workspace context. If the
    // context includes a chat response, it is inserted into the list of recent changes according
    // to its sequence number. The sequence numbers of the recent chunks and the chat response all
    // come from the same sequence number space, so it is meaningful to compare them.
    private _getRecentChanges(workspaceContext: WorkspaceContext): ReplacementText[] {
        let recentChunks = workspaceContext.recentChunks;
        const lastChatResponse = workspaceContext.lastChatResponse;
        if (lastChatResponse !== undefined) {
            const toInsert: Chunk = {
                seq: lastChatResponse.seq,
                uploaded: false,
                repoRoot: "",
                pathName: "",
                blobName: "",
                text: lastChatResponse.text,
                origStart: 0,
                origLength: 0,
                expectedBlobName: "",
            };
            let insertIdx = recentChunks.findIndex((chunk) => chunk.seq < toInsert.seq);
            if (insertIdx < 0) {
                insertIdx = recentChunks.length;
            }
            recentChunks = recentChunks
                .slice(0, insertIdx)
                .concat([toInsert])
                .concat(recentChunks.slice(insertIdx));
        }
        return makeReplacementText(recentChunks);
    }
}

/**
 * Verify that the skip token handling of suffix is correct.
 *
 * VSCode hide any completion where the replaced suffix is not contained
 * in the completion string.  Do verification here to prevent that.
 */
function _verifySkipSuffix(items: CompletionItem[], suffix: string, logger: AugmentLogger): void {
    for (const item of items) {
        // If this fires there is definitely a bug in the backend.
        // Logging it in extension for now.
        if (!suffix.startsWith(item.skippedSuffix)) {
            logger.warn(
                "Skipped suffix does not match the actual suffix. " +
                    `Skipped suffix: ${item.skippedSuffix}. ` +
                    `First ${item.skippedSuffix.length} characters of suffix:` +
                    ` ${suffix.substring(0, item.skippedSuffix.length)}`
            );
            // Here revert any skip to prevent VSCode from hiding the completion
            item.suffixReplacementText = "";
            item.skippedSuffix = "";
        }
    }
}

export type CompletionRequest = {
    requestId: string;
    document: vscode.TextDocument;
    repoRoot: string;
    pathName: string;
    prefix: string;
    suffix: string;
    completions: AugmentCompletion[];
    occuredAt: Date;
    isReused: boolean;
};

import { onInlineCompletionVisibilityChange } from "../completions/completion-events";
import { DisposableService } from "../utils/disposable-service";
import { Observable } from "../utils/observable";

export class CompletionVisibilityWatcher extends DisposableService {
    // best effort to know if inline completions are visible
    // `true` means we think an inline completion is currently visible,
    // `false` means we think no inline completion is currently visible
    private _maybeInlineCompletionVisible = new Observable<boolean>(false);

    constructor() {
        super();

        this.addDisposable(
            // TODO: we are not getting notified when the user hides inline completions with ESC.
            onInlineCompletionVisibilityChange((visible) => {
                this._maybeInlineCompletionVisible.value = visible;
            })
        );
    }

    public get maybeInlineCompletionVisible() {
        return this._maybeInlineCompletionVisible.value;
    }

    public listen(fn: (visible: boolean) => void) {
        return this._maybeInlineCompletionVisible.listen(fn);
    }
}

import * as vscode from "vscode";

import {
    AbstractNextEditCommand,
    NextEditBackgroundAcceptAllCommand,
    NextEditBackgroundAcceptCodeActionCommand,
    NextEditBackgroundAcceptCommand,
    NextEditBackgroundDismissCommand,
    NextEditBackgroundGotoNextSmartCommand,
    NextEditBackgroundNextCommand,
    NextEditBackgroundPreviousCommand,
    NextEditBackgroundRejectAllCommand,
    NextEditBackgroundRejectCommand,
    NextEditUndoAcceptSuggestionCommand,
} from "../commands/next-edit";
import { setContext } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { Observable } from "../utils/observable";
import {
    AfterPreview,
    Animating,
    BackgroundState,
    BeforePreview,
    NoSuggestions,
} from "./background-state";

const contextKeyMapping = new Map<typeof AbstractNextEditCommand, string>([
    [NextEditBackgroundGotoNextSmartCommand, "vscode-augment.nextEdit.canNextSmart"],
    [NextEditBackgroundNextCommand, "vscode-augment.nextEdit.canNext"],
    [NextEditBackgroundPreviousCommand, "vscode-augment.nextEdit.canPrevious"],
    [NextEditBackgroundAcceptCommand, "vscode-augment.nextEdit.canAccept"],
    [NextEditBackgroundRejectCommand, "vscode-augment.nextEdit.canReject"],
    [NextEditBackgroundDismissCommand, "vscode-augment.nextEdit.canDismiss"],
    [NextEditBackgroundAcceptCodeActionCommand, "vscode-augment.nextEdit.canAcceptCodeAction"],
    [NextEditBackgroundAcceptAllCommand, "vscode-augment.nextEdit.canAcceptAll"],
    [NextEditBackgroundRejectAllCommand, "vscode-augment.nextEdit.canRejectAll"],
    [NextEditUndoAcceptSuggestionCommand, "vscode-augment.nextEdit.canUndoAcceptSuggestion"],
]);

/** Tracks the status of whether keybindings for specific commands are active. */
export class KeybindingStatus extends DisposableService {
    private _status: Map<typeof AbstractNextEditCommand, boolean> = new Map();

    constructor(private readonly _state: Observable<BackgroundState>) {
        super();

        this.addDisposable(
            new vscode.Disposable(
                this._state.listen((state) => {
                    const hasSuggestions = !(state instanceof NoSuggestions);

                    this._set(NextEditBackgroundGotoNextSmartCommand, hasSuggestions);
                    this._set(NextEditBackgroundNextCommand, hasSuggestions);
                    this._set(NextEditBackgroundPreviousCommand, hasSuggestions);
                    this._set(
                        NextEditBackgroundAcceptCommand,
                        state instanceof BeforePreview ||
                            state instanceof Animating ||
                            state instanceof AfterPreview
                    );
                    this._set(
                        NextEditBackgroundRejectCommand,
                        state instanceof BeforePreview ||
                            state instanceof Animating ||
                            state instanceof AfterPreview
                    );
                    this._set(NextEditUndoAcceptSuggestionCommand, state instanceof AfterPreview);
                    this._set(NextEditBackgroundDismissCommand, hasSuggestions);
                    this._set(
                        NextEditBackgroundAcceptCodeActionCommand,
                        state instanceof AfterPreview
                    );
                    this._set(NextEditBackgroundAcceptAllCommand, hasSuggestions);
                    this._set(NextEditBackgroundRejectAllCommand, hasSuggestions);
                }, true)
            )
        );
    }

    public get(commandClass: typeof AbstractNextEditCommand): boolean {
        return this._status.get(commandClass) ?? false;
    }

    private _set(commandClass: typeof AbstractNextEditCommand, value: boolean) {
        this._status.set(commandClass, value);
        const contextKey = contextKeyMapping.get(commandClass);
        if (contextKey) {
            setContext(contextKey, value);
        }
    }
}

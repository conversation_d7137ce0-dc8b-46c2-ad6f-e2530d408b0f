import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { debounce, DebouncedFunc } from "lodash";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { DiagnosticsManager } from "../diagnostics";
import { FeatureFlagManager } from "../feature-flags";
import { getLogger } from "../logging";
import { ClientMetricsReporter } from "../metrics/client-metrics-reporter";
import { NextEditSessionEventReporter } from "../metrics/next-edit-session-event-reporter";
import { StateController } from "../statusbar/state-controller";
import * as statusbarStates from "../statusbar/status-bar-states";
import { setContext } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { EphemeralObservable } from "../utils/ephemeral-flag";
import { Observable } from "../utils/observable";
import { LineRange } from "../utils/ranges";
import { toVSCodeRange } from "../utils/ranges-vscode";
import { RecentItems } from "../utils/recent-items";
import { WorkspaceManager } from "../workspace/workspace-manager";
import { queryNextEditStream } from "./next-edit-api";
import {
    BlockedPathAndRange,
    ChangeType,
    type IEditSuggestion,
    NextEditMode,
    NextEditResultInfo,
    NextEditScope,
    NextEditSessionEventName,
    NextEditSessionEventSource,
    SuggestionState,
    type UUID,
} from "./next-edit-types";
import { INextEditRequestManager, NextEditRequestManagerState } from "./request-manager";
import { SuggestionManager } from "./suggestion-manager";

// NOTE(arun): These are internal types and are hence not namespaced.
/**
 * Metadata for an enqueued request.
 *
 * Used to compare and determine the priority of requests.
 */
interface RequestMetadata {
    // The request ID.
    id: UUID;
    // The editor where the request originated (can be null if workspace).
    qualifiedPathName?: IQualifiedPathName;
    // The mode of the request.
    mode: NextEditMode;
    // The scope of the request.
    scope: NextEditScope;
    // The time when the request was enqueued.
    enqueuedAt: number;
}

/** Additional metadata for the inflight request. */
interface InflightRequestMetadata extends RequestMetadata {
    // The blob name when the request was launched.
    // Can be null when the request is for a workspace.
    requestBlobName?: string;
    // The selection when the request was launched.
    // Can be null when the request is for a workspace.
    selection?: LineRange;
    // The cancellation token source.
    // Can be null when being used to compare two requests.
    cancelTokenSource?: vscode.CancellationTokenSource;
}

export class NextEditRequestManager extends DisposableService implements INextEditRequestManager {
    private _logger = getLogger("NextEditRequestManager");

    /** How long to wait before clearing the statusbar on cancelled requests. */
    private static readonly _statusClearTimeoutMs = 2000;

    /**
     * A queue of pending requests.
     *
     * When a request is launched, it is moved to an inflight request.
     */
    private _pendingRequests: RequestMetadata[] = [];
    /**
     * An inflight request, if any.
     *
     * There will only ever be one inflight request.
     */
    private _inflightRequest?: InflightRequestMetadata;

    public lastFinishedRequest: Observable<NextEditResultInfo | undefined> = new Observable<
        NextEditResultInfo | undefined
    >(undefined);

    public lastResponse: Observable<IEditSuggestion | undefined> = new Observable<
        IEditSuggestion | undefined
    >(undefined);

    public state: Observable<NextEditRequestManagerState> =
        new Observable<NextEditRequestManagerState>(NextEditRequestManagerState.ready);

    /** A debounced function to process pending requests. */
    private _processPendingRequestsDebounced: DebouncedFunc<() => void> | undefined;

    /** Requests that were recently completed and not yet invalidated by an edit. */
    private _freshCompletedRequests: InflightRequestMetadata[] = [];

    constructor(
        private _apiServer: APIServer,
        private _configListener: AugmentConfigListener,
        private _workspaceManager: WorkspaceManager,
        private readonly _diagnosticsManager: DiagnosticsManager,
        private _nextEditSessionEventReporter: NextEditSessionEventReporter,
        private _clientMetricsReporter: ClientMetricsReporter,
        private _blobNameCalculator: BlobNameCalculator,
        /** Suggestion manager to track suggestions and blocked locations. */
        private _suggestionManager: SuggestionManager,
        /** Recent suggestions for the history panel. */
        private _recentSuggestions: RecentItems<NextEditResultInfo>,
        /** The state controller for the status icon. */
        private _stateController: StateController,
        private _completionJustAccepted: EphemeralObservable<boolean>,
        private _featureFlagManager: FeatureFlagManager
    ) {
        super();
        this.addDisposable(
            new vscode.Disposable(() => {
                this._inflightRequest?.cancelTokenSource?.cancel();
                this._inflightRequest?.cancelTokenSource?.dispose();
                this._inflightRequest = undefined;
                this.lastFinishedRequest.dispose();
                this.lastResponse.dispose();
                this.state.dispose();
            })
        );

        // Set the context variable for the next edit loading state to be when a request
        // is inflight.
        this.addDisposable(
            new vscode.Disposable(
                this.state.listen((state) => {
                    setContext(
                        "vscode-augment.nextEdit.loading",
                        state === NextEditRequestManagerState.inflight
                    );
                })
            )
        );

        const setDebounceMs = (debounceMs?: number) => {
            const debounceMs_ =
                debounceMs ?? this._featureFlagManager.currentFlags.nextEditDebounceMs;
            if (this._processPendingRequestsDebounced) {
                this._processPendingRequestsDebounced.cancel();
            }
            this._processPendingRequestsDebounced = debounce(
                () => void this._processPendingRequests(),
                debounceMs_
            );
        };

        setDebounceMs(this._configListener.config.nextEdit.useDebounceMs);
        this.addDisposable(
            this._configListener.onDidChange((event) => {
                if (
                    event.newConfig.nextEdit.useDebounceMs ===
                    event.previousConfig.nextEdit.useDebounceMs
                ) {
                    return;
                }
                setDebounceMs(event.newConfig.nextEdit.useDebounceMs);
            })
        );

        this.addDisposable(
            vscode.workspace.onDidChangeTextDocument((event) => {
                // If any relevant documents were changed, clear the fresh completed requests.
                if (
                    event.contentChanges.length > 0 &&
                    this._workspaceManager.safeResolvePathName(event.document.uri)
                ) {
                    this._freshCompletedRequests = [];
                }

                // Check if the event is relevant to any inflight or pending requests.
                if (
                    !QualifiedPathName.equals(
                        this._inflightRequest?.qualifiedPathName,
                        event.document.uri
                    ) &&
                    !this._pendingRequests.some((request) =>
                        QualifiedPathName.equals(request.qualifiedPathName, event.document.uri)
                    )
                ) {
                    return;
                }

                // If any relevant documents were changed outside of a suggestion being
                // accepted, cancel all inflight requests and pending requests.
                if (
                    event.contentChanges.length > 0 &&
                    !this._suggestionManager.suggestionWasJustAccepted.value
                ) {
                    this.cancelAll();
                }
            })
        );
    }

    public get hasInflightRequest(): boolean {
        return !!this._inflightRequest;
    }

    public get pendingQueueLength(): number {
        return this._pendingRequests.length;
    }

    public shouldNotEnqueueRequestReason(
        qualifiedPathName: IQualifiedPathName | undefined,
        mode: NextEditMode,
        scope: NextEditScope,
        selection?: LineRange
    ): string | undefined {
        const resolvedPath = this._resolvePath(qualifiedPathName);

        // Build a partial "candidate inflight request" object
        const candidateRequest: Pick<
            InflightRequestMetadata,
            "mode" | "scope" | "selection" | "requestBlobName" | "qualifiedPathName"
        > = {
            mode,
            scope,
            selection: selection ?? resolvedPath?.selection,
            requestBlobName: resolvedPath?.blobName,
            qualifiedPathName,
        };

        // Check whether there is an inflight or “next” request
        const inflightOrNextRequest = this._inflightRequest || this._pendingRequests[0];

        // If the candidate request is subsumed by the inflight or next request, skip
        if (inflightOrNextRequest && requestIsSubsumedBy(candidateRequest, inflightOrNextRequest)) {
            return `Skipping ${mode}/${scope} request because it is subsumed by inflight request ${inflightOrNextRequest.id}.`;
        }

        // If the request is equivalent to any recently completed request, skip
        const subsumingFresh = this._freshCompletedRequests.find((r) =>
            requestIsSubsumedBy(candidateRequest, r)
        );
        const locationMsg = `${qualifiedPathName?.relPath}@${candidateRequest.selection?.toString()}`;
        if (subsumingFresh) {
            return `Skipping ${mode}/${scope} request at ${locationMsg} because it is subsumed by ${subsumingFresh.id}, which was recently completed.`;
        }

        // Otherwise, the request should proceed
        return undefined;
    }

    public enqueueRequest(
        qualifiedPathName: IQualifiedPathName | undefined,
        mode: NextEditMode,
        scope: NextEditScope,
        selection?: LineRange
    ): UUID | undefined {
        const shouldNotEnqueueReason = this.shouldNotEnqueueRequestReason(
            qualifiedPathName,
            mode,
            scope,
            selection
        );
        if (shouldNotEnqueueReason) {
            this._logger.verbose(shouldNotEnqueueReason);
            return;
        }

        // If the request is for a workspace, then we may not have a path.
        const resolvedPath = this._resolvePath(qualifiedPathName);
        selection = selection ?? resolvedPath?.selection;

        const locationMsg = `${qualifiedPathName?.relPath}@${selection?.toString()}`;
        const candidateRequest: RequestMetadata = {
            id: this._apiServer.createRequestId(),
            qualifiedPathName,
            mode,
            scope,
            enqueuedAt: Date.now(),
        };
        // NOTE(arun): We only store the blob name and selection when the request is
        // in flight. This is a temporary assignment to check with the currently
        // inflight request.
        this._logger.verbose(
            `Starting enqueuing ${mode}/${scope} request ${candidateRequest.id} at ${locationMsg}.`
        );

        const inflightOrNextRequest = this._inflightRequest || this._pendingRequests[0];

        if (inflightOrNextRequest) {
            if (
                candidateRequest.mode === NextEditMode.Foreground ||
                candidateRequest.mode === NextEditMode.Forced
            ) {
                // 2. If it's a FORCED/FOREGROUND request, the user explicitly asked for the
                // results, so always clear all pending requests to get them a result ASAP.
                this._logger.verbose(`Clearing requests for foreground request @ ${locationMsg}.`);
                this.cancelAll();
            } else if (
                candidateRequest.mode === NextEditMode.Background &&
                inflightOrNextRequest.mode === NextEditMode.Background &&
                !QualifiedPathName.equals(
                    inflightOrNextRequest.qualifiedPathName,
                    candidateRequest.qualifiedPathName
                )
            ) {
                // 3. If the candidate request is a BACKGROUND request from a different
                // file, then cancel any BACKGROUND requests, because we think the user is
                // probably switching files and not interested in those results.
                this._logger.verbose(`Clearing requests for background request @ ${locationMsg}.`);
                this.cancelAll();
            } else if (
                candidateRequest.mode === NextEditMode.Background &&
                inflightOrNextRequest.mode === NextEditMode.Background &&
                inflightOrNextRequest.scope === NextEditScope.Workspace &&
                candidateRequest.scope === NextEditScope.File
            ) {
                // 4. If the candidate request is a BACKGROUND/FILE request from the same
                // file as a BACKGROUND/WORKSPACE, cancel the latter since the former is more
                // specific.
                this._logger.verbose(
                    `Clearing background workspace requests for background file request @ ${locationMsg}.`
                );
                this.cancelAll();
            }
        }

        // If the request is equivalent to any pending request, then we don't need to
        // enqueue it
        const subsumingPending = this._pendingRequests.find((r) =>
            requestIsSubsumedBy(candidateRequest, r)
        );
        if (subsumingPending) {
            this._logger.verbose(
                `Skipping enqueueing request ${candidateRequest.id} at ${locationMsg} because it is subsumed by ${subsumingPending.id}, which is already pending.`
            );
        } else {
            this._pendingRequests.push(candidateRequest);
        }

        this.state.value = this._inflightRequest
            ? NextEditRequestManagerState.inflight
            : NextEditRequestManagerState.pending;

        this._processPendingRequestsDebounced!();
        // Process the request immediately if:
        // - its foreground/forced.
        // - a suggestion or completion was just accepted.
        if (
            candidateRequest.mode === NextEditMode.Foreground ||
            candidateRequest.mode === NextEditMode.Forced ||
            this._suggestionManager.suggestionWasJustAccepted.value ||
            this._completionJustAccepted.value
        ) {
            this._processPendingRequestsDebounced!.flush();
        }
        return candidateRequest.id;
    }

    public cancelAll(): void {
        if (this._inflightRequest) {
            this._logger.verbose(`Cancelling inflight request ${this._inflightRequest.id}.`);
            this._inflightRequest.cancelTokenSource?.cancel();
            this._inflightRequest.cancelTokenSource?.dispose();
            this._inflightRequest = undefined;
        }
        if (this._pendingRequests.length > 0) {
            this._logger.verbose(
                `Cancelling ${this._pendingRequests.length} pending requests: ${this._pendingRequests
                    .map((r) => r.id)
                    .toString()}.`
            );
            this._pendingRequests = [];
            this._processPendingRequestsDebounced!.cancel();
        }
    }

    /**
     * Run the next request on the queue.
     * Almost always use _processPendingRequestsDebounced!() instead.
     */
    private async _processPendingRequests() {
        if (this._inflightRequest) {
            this._logger.verbose("Waiting for inflight request to complete.");
            return;
        } else if (!this._pendingRequests.length) {
            this._logger.verbose("Waiting for a request to be enqueued.");
            return;
        }

        // Grab the next request from the queue.
        const [nextRequest] = this._pendingRequests.splice(0, 1);
        const resolvedPath = this._resolvePath(nextRequest.qualifiedPathName);

        const processingDelayTimeMs = Date.now() - nextRequest.enqueuedAt;
        this._logger.verbose(
            `Starting to process ${nextRequest.id} after ${processingDelayTimeMs} ms.`
        );

        const requestBlobName = resolvedPath?.blobName;
        const requestPath = nextRequest.qualifiedPathName;
        const requestDoc = resolvedPath?.document;
        const requestSelection = resolvedPath?.selection;

        const mode = nextRequest.mode;
        const scope = nextRequest.scope;

        const blockedLocations = this._suggestionManager
            .getRejectedSuggestions()
            .filter(
                (s) =>
                    s.changeType !== ChangeType.noop &&
                    (scope === NextEditScope.Workspace ||
                        requestPath === undefined ||
                        s.qualifiedPathName.equals(requestPath))
            )
            .map(
                (s) =>
                    new BlockedPathAndRange(
                        s.qualifiedPathName.relPath,
                        s.lineRange,
                        s.result.charStart,
                        s.result.charEnd
                    )
            );

        const requestId = nextRequest.id;
        // We track the request locally so that we can check if we are responsible for
        // clean up logic when the request is complete.
        const thisRequest = (this._inflightRequest = {
            ...nextRequest,
            requestBlobName,
            selection: requestSelection,
            cancelTokenSource: new vscode.CancellationTokenSource(),
        });

        this.state.value = NextEditRequestManagerState.inflight;
        const generatingStatus = this._stateController.setState(
            statusbarStates.generatingSuggestion
        );
        let lastStatus: APIStatus = APIStatus.ok;
        const suggestions: IEditSuggestion[] = [];
        const startTime = new Date();
        try {
            const processingStartTime = Date.now();
            const stream = queryNextEditStream(
                {
                    requestId,
                    clientCreatedAt: new Date(),
                    instruction: "",
                    selectedCode: requestDoc?.getText(
                        requestSelection && toVSCodeRange(requestSelection, requestDoc)
                    ),
                    prefix:
                        requestSelection &&
                        requestDoc?.getText(
                            toVSCodeRange({ start: 0, stop: requestSelection.start }, requestDoc)
                        ),
                    suffix:
                        requestSelection &&
                        requestDoc?.getText(
                            toVSCodeRange(
                                {
                                    start: requestSelection.stop,
                                    stop: requestDoc.lineCount,
                                },
                                requestDoc
                            )
                        ),
                    language: requestDoc?.languageId,
                    pathName: requestPath,
                    mode,
                    scope,
                    blockedLocations,
                    unindexedEditEvents: [],
                    unindexedEditEventsBaseBlobNames: [],
                },
                this._workspaceManager,
                this._diagnosticsManager,
                this._apiServer,
                this._blobNameCalculator,
                this._configListener,
                thisRequest.cancelTokenSource.token,
                this._nextEditSessionEventReporter
            );
            const processingStopTime = Date.now();

            // Client latency metric tracking
            let firstChangeMs: number = 0;
            let sufficientNoopsMs: number = 0;
            let responseCount = 0;
            const queueingTimeMs = processingStartTime - thisRequest.enqueuedAt;
            this._logger.verbose(`[${thisRequest.id}] queued for ${queueingTimeMs} ms.`);

            // Track the suggestions from this request for the history panel.
            for await (const response of stream) {
                lastStatus = response.status;
                if (!response.suggestion) {
                    // The suggestion will be missing if there was a failure
                    // cancellation in the request or if we never made a request in
                    // the first place because, e.g. there are no edit events.
                    break;
                }
                responseCount++;
                const responseTimeMs = Date.now();
                const elapsedTimeMs = responseTimeMs - thisRequest.enqueuedAt;
                this._logger.verbose(
                    `[${response.suggestion?.requestId}/${response.suggestion?.result.suggestionId}] ${response.suggestion?.changeType?.toString()} took ${elapsedTimeMs} ms since enqueue.`
                );
                // Capture the first response that has a change.
                if (
                    firstChangeMs === 0 &&
                    response.suggestion !== undefined &&
                    response.suggestion.changeType !== ChangeType.noop
                ) {
                    firstChangeMs = elapsedTimeMs;
                }
                // If the first 4 locations are no-ops, treat that as reasonable signal (to both us and the user) that there's no useful change.
                if (responseCount === 4 && firstChangeMs === 0) {
                    sufficientNoopsMs = elapsedTimeMs;
                }
                this.lastResponse.value = response.suggestion;
                if (response.suggestion) {
                    suggestions.push(response.suggestion);
                }
            }
            // True if if we never made a request in the first place because, e.g.,
            // there are no edit events.
            const skippedRequest = lastStatus === APIStatus.ok && suggestions.length === 0;

            if (lastStatus === APIStatus.ok && !skippedRequest) {
                this._freshCompletedRequests.push(thisRequest);
            }

            // Limit metrics reporting to background mode for now (it's the most relevant one).
            if (!skippedRequest && thisRequest.mode === NextEditMode.Background) {
                this._clientMetricsReporter.report({
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    client_metric: "next_edit_bg_stream_preprocessing_latency_ms",
                    value: processingStopTime - processingStartTime,
                });
                const finishMs = Date.now() - thisRequest.enqueuedAt;
                if (lastStatus === APIStatus.ok) {
                    this._clientMetricsReporter.report({
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: "next_edit_bg_stream_finish_latency_ms",
                        value: finishMs,
                    });
                } else if (firstChangeMs > 0 || sufficientNoopsMs > 0) {
                    // Didn't finish cleanly but got far enough to be useful
                    this._clientMetricsReporter.report({
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: "next_edit_bg_stream_partial_latency_ms",
                        value: finishMs,
                    });
                } else if (lastStatus === APIStatus.cancelled) {
                    this._clientMetricsReporter.report({
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: "next_edit_bg_stream_cancel_latency_ms",
                        value: finishMs,
                    });
                } else {
                    this._clientMetricsReporter.report({
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: "next_edit_bg_stream_error_latency_ms",
                        value: finishMs,
                    });
                }
                // Note separate if/else block-- these could match either the `_finish_` or `_partial_` case above
                if (firstChangeMs > 0) {
                    this._clientMetricsReporter.report({
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: "next_edit_bg_first_change_latency_ms",
                        value: firstChangeMs,
                    });
                } else if (sufficientNoopsMs > 0) {
                    this._clientMetricsReporter.report({
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: "next_edit_bg_sufficient_noops_latency_ms",
                        value: sufficientNoopsMs,
                    });
                } else if (responseCount < 4 && lastStatus === APIStatus.ok) {
                    // If the stream finished cleanly after 3 or fewer no-op locations, we also treat that as "sufficient"
                    this._clientMetricsReporter.report({
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        client_metric: "next_edit_bg_sufficient_noops_latency_ms",
                        value: finishMs,
                    });
                }
            }
        } catch (e: any) {
            this._logger.warn(`[${requestId}] Next edit failed: ${e}.`);
            this._nextEditSessionEventReporter.reportEvent(
                requestId,
                undefined,
                Date.now(),
                NextEditSessionEventName.ErrorAPIError,
                NextEditSessionEventSource.Unknown
            );
        } finally {
            // If no other request was launched while this one was processing (i.e., it
            // wasn't cancelled), then we are responsible for queuing up the next one and
            // dispatching a state change.
            if (thisRequest === this._inflightRequest) {
                this._inflightRequest = undefined;
                if (this._pendingRequests.length > 0) {
                    this._processPendingRequestsDebounced!();
                    this._processPendingRequestsDebounced!.flush();
                } else {
                    this._logger.verbose("No more pending requests.");
                    this.state.value = NextEditRequestManagerState.ready;

                    if (
                        lastStatus === APIStatus.ok &&
                        !this._suggestionManager
                            .getActiveSuggestions()
                            .some(
                                (s) =>
                                    s.state === SuggestionState.fresh &&
                                    s.changeType !== ChangeType.noop
                            )
                    ) {
                        disposeAfterMs(
                            this._stateController.setState(statusbarStates.noSuggestions),
                            NextEditRequestManager._statusClearTimeoutMs
                        );
                    } else if (lastStatus !== APIStatus.cancelled && lastStatus !== APIStatus.ok) {
                        this._stateController.setState(statusbarStates.suggestionFailed);
                        this._logger.verbose(
                            `Request ${requestId} failed with status: ${lastStatus}.`
                        );
                    }
                }
            }
            const resultInfo = new NextEditResultInfo(
                requestId,
                mode,
                scope,
                requestPath,
                lastStatus,
                suggestions,
                startTime
            );
            this._recentSuggestions.addItem(resultInfo);
            this.lastFinishedRequest.value = resultInfo;
            generatingStatus.dispose();
            thisRequest.cancelTokenSource.dispose();
        }
    }

    public clearCompletedRequests(mode?: NextEditMode) {
        this._freshCompletedRequests = this._freshCompletedRequests.filter(
            (r) => mode !== undefined && r.mode !== mode
        );
    }

    /** Resolve a path to a blob name and selection (if possible). */
    private _resolvePath(
        iQualifiedPathName: IQualifiedPathName | undefined
    ): { blobName?: string; document?: vscode.TextDocument; selection?: LineRange } | undefined {
        const qualifiedPathName = iQualifiedPathName && QualifiedPathName.from(iQualifiedPathName);
        const editor = qualifiedPathName && this._findEditorForPath(qualifiedPathName);
        const document = editor && editor.document;
        const blobName =
            document &&
            this._blobNameCalculator.calculate(qualifiedPathName.relPath, document.getText());
        // Grab the selection from the cursor always.
        const selection =
            editor && new LineRange(editor.selection.start.line, editor.selection.end.line);
        return {
            blobName,
            document,
            selection,
        };
    }

    /**
     * Find the editor for a given path in the request.
     *
     * Used to determine the cursor and get the document for the request.
     */
    private _findEditorForPath(
        iQualifiedPathName: IQualifiedPathName
    ): vscode.TextEditor | undefined {
        const qualifiedPathName = QualifiedPathName.from(iQualifiedPathName);
        // There may be multiple editors for the same document; we prefer the active one.
        if (
            vscode.window.activeTextEditor &&
            this._workspaceManager
                .safeResolvePathName(vscode.window.activeTextEditor.document.uri)
                ?.equals(qualifiedPathName)
        ) {
            return vscode.window.activeTextEditor;
        }
        return vscode.window.visibleTextEditors.find((e) =>
            this._workspaceManager.safeResolvePathName(e.document.uri)?.equals(qualifiedPathName)
        );
    }
}

/**
 * Don't issue a new query if the text hasn't changed and the previous query is
 * less than these number of lines away.
 */
const equivalentLineWindow = 15;

type SubsumeComparison<T extends Partial<InflightRequestMetadata>> = NonNullable<
    Pick<T, "mode" | "scope" | "selection" | "requestBlobName" | "qualifiedPathName">
>;

/**
 * Compare two requests to determine if request is subsumed by other.
 *
 * This is true if:
 * - they have the same mode and scope or request is cursor and other is file.
 * - they are workspace requests or they have the same path and blob name.
 * - they have similar regions (when applicable).
 *
 * @param request
 * @param other
 * @returns true if request is subsumed by other, false otherwise.
 */
export function requestIsSubsumedBy(
    request: SubsumeComparison<InflightRequestMetadata>,
    other: SubsumeComparison<InflightRequestMetadata>
): boolean {
    return (
        request.mode === other.mode &&
        (request.scope === other.scope ||
            (request.scope === NextEditScope.Cursor && other.scope === NextEditScope.File)) &&
        (request.scope === NextEditScope.Workspace ||
            QualifiedPathName.equals(request.qualifiedPathName, other.qualifiedPathName)) &&
        // If either of the blob names are null, then we ignore this check.
        !(
            request.scope !== NextEditScope.Workspace &&
            request.requestBlobName &&
            other.requestBlobName &&
            request.requestBlobName !== other.requestBlobName
        ) &&
        // If either of the selections are null, then we consider them equivalent.
        !(
            request.scope === NextEditScope.Cursor &&
            request.selection?.start != null &&
            other.selection?.start != null &&
            Math.abs(request.selection.start - other.selection.start) > equivalentLineWindow
        )
    );
}

function disposeAfterMs(disposable: vscode.Disposable, ms: number) {
    setTimeout(() => {
        disposable.dispose();
    }, ms);
}

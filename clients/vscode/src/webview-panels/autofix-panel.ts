import path from "node:path";
import * as vscode from "vscode";

import { AutofixIterationStage } from "../autofix/autofix-state";
import { getLogger } from "../logging";
import { ChatApp } from "../main-panel/apps/chat-webview-app";
import { WorkTimer } from "../metrics/work-timer";
import type { IEditSuggestion } from "../next-edit/next-edit-types";
import { ResolveFileService } from "../resolve-file-service";
import { applySuggestions } from "../utils/apply-edit-suggestion";
import { DisposableService } from "../utils/disposable-service";
import { readFileUtf8, writeFileUtf8 } from "../utils/fs-utils";
import {
    addGoogleFonts,
    addImgData,
    addMonacoCdnRequirements,
    addMonacoRequirements,
    addWebViewCSP,
    generateCSPPolicy,
} from "../utils/webviews/csp";
import { AsyncMsgHandler } from "../utils/webviews/messaging";
import { createAsyncMsgHandlerFromWebview } from "../utils/webviews/messaging-helper";
import {
    AutofixPanelApplyAndRetestRequestMessage,
    AutofixPanelDetailsInitRequestMessage,
    AutofixPanelExecuteCommandPartialOutput,
    AutofixPanelOpenSpecificStageMessage,
    ChatAutofixStateUpdateMessage,
    EmptyMessage,
    WebViewMessageType,
} from "../webview-providers/webview-messages";
import { pathNameToAbsPath } from "../workspace/types";

export class AutofixWebviewPanel extends DisposableService {
    private static viewType: string = "augmentAutofixPanel";
    private _logger = getLogger("AutofixWebviewPanel");
    public static currentPanel: AutofixWebviewPanel | undefined;

    private readonly _panel: vscode.WebviewPanel;
    private _detailsPanelAsyncMsgHandler: AsyncMsgHandler;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _conversationId: string,
        private readonly _initialIterationId: string,
        private readonly _initialStage: AutofixIterationStage,
        private readonly _chatApp: ChatApp,
        private readonly _resolveFileService: ResolveFileService,
        private readonly _workTimer: WorkTimer
    ) {
        super();

        this._panel = vscode.window.createWebviewPanel(
            AutofixWebviewPanel.viewType,
            "Augment",
            vscode.ViewColumn.One,
            {
                retainContextWhenHidden: true,
                enableScripts: true,
            }
        );
        this._detailsPanelAsyncMsgHandler = createAsyncMsgHandlerFromWebview(
            this._panel.webview,
            this._workTimer
        );

        this._detailsPanelAsyncMsgHandler.registerHandler(
            WebViewMessageType.autofixPanelDetailsInitRequest,
            this.handleAutofixInitMessage
        );

        this._detailsPanelAsyncMsgHandler.registerHandler(
            WebViewMessageType.autofixPanelApplyAndRetestRequest,
            this.handleAutofixApplyAndRetestRequest.bind(this)
        );

        // Pass the webview to the ResolveFileService
        this._resolveFileService.register(this._detailsPanelAsyncMsgHandler);

        this._panel.iconPath = {
            light: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-light.svg"),
            dark: vscode.Uri.joinPath(this._extensionUri, "media", "panel-icon-dark.svg"),
        };

        this.addDisposables(
            this._panel,
            new vscode.Disposable(() => {
                AutofixWebviewPanel.currentPanel = undefined;
            })
        );

        this._panel.onDidDispose(() => {
            this.dispose();
            AutofixWebviewPanel.currentPanel = undefined;
        });
        void this._setHTML();
    }
    public get logger() {
        return this._logger;
    }

    private async _setHTML() {
        const webview = this._panel.webview;
        const basePath = vscode.Uri.joinPath(this._extensionUri, "common-webviews");

        webview.options = {
            enableScripts: true,
            localResourceRoots: [basePath],
        };

        // The slash is used to ensure relative paths are resolved correctly
        const baseHref = webview.asWebviewUri(vscode.Uri.joinPath(basePath, "/"));

        const csp = generateCSPPolicy(
            addWebViewCSP(webview),
            addGoogleFonts(),
            addImgData(),
            addMonacoRequirements(),
            addMonacoCdnRequirements()
        );

        let diffViewHTML = await readFileUtf8(path.join(basePath.fsPath, "autofix.html"));
        diffViewHTML = diffViewHTML.replace(
            /<head>/i,
            `<head>
            <base href="${baseHref.toString()}" />
            <meta http-equiv="Content-Security-Policy" content="${csp}">
        `
        );
        webview.html = diffViewHTML;
    }

    public static launchAutofixPanel(
        extensionUri: vscode.Uri,
        conversationId: string,
        iterationId: string,
        stage: AutofixIterationStage,
        chatApp: ChatApp,
        resolveFileService: ResolveFileService,
        workTimer: WorkTimer
    ) {
        if (AutofixWebviewPanel.currentPanel) {
            if (AutofixWebviewPanel.currentPanel._conversationId === conversationId) {
                void AutofixWebviewPanel.currentPanel.openSpecificStage(iterationId, stage);
                AutofixWebviewPanel.currentPanel._panel.reveal();
                return;
            } else {
                AutofixWebviewPanel.currentPanel.dispose();
            }
        }

        AutofixWebviewPanel.currentPanel = new AutofixWebviewPanel(
            extensionUri,
            conversationId,
            iterationId,
            stage,
            chatApp,
            resolveFileService,
            workTimer
        );
    }

    public handleAutofixStateUpdate = async (
        message: ChatAutofixStateUpdateMessage
    ): Promise<void> => {
        await this._panel.webview.postMessage({
            type: WebViewMessageType.autofixPanelStateUpdate,
            data: message.data,
        });
    };

    public handleCommandPartialOutput = async (
        message: AutofixPanelExecuteCommandPartialOutput
    ): Promise<void> => {
        await this._panel.webview.postMessage(message);
    };

    private handleAutofixInitMessage = async (
        _message: AutofixPanelDetailsInitRequestMessage
    ): Promise<AutofixPanelOpenSpecificStageMessage> => {
        await this._chatApp.sendAutofixUpdateRequestMessage();
        return {
            type: WebViewMessageType.autofixPanelOpenSpecificStage,
            data: {
                iterationId: this._initialIterationId,
                stage: this._initialStage,
            },
        };
    };

    private handleAutofixApplyAndRetestRequest = async (
        message: AutofixPanelApplyAndRetestRequestMessage
    ): Promise<EmptyMessage> => {
        await this._applyAllAutofixChanges(message.data.selectedSolutions);
        await this._chatApp.sendAutofixSuggestionsAppliedMessage(message.data.selectedSolutions);
        return { type: WebViewMessageType.empty };
    };

    private async openSpecificStage(iterationId: string, stage: AutofixIterationStage) {
        await this._panel.webview.postMessage({
            type: WebViewMessageType.autofixPanelOpenSpecificStage,
            data: {
                iterationId,
                stage,
            },
        });
    }

    private async _applyAutofixChanges(fileAbsPath: string, suggestions: IEditSuggestion[]) {
        const originalContent = await readFileUtf8(fileAbsPath);
        const newContent = applySuggestions(originalContent, suggestions);
        await writeFileUtf8(fileAbsPath, newContent);
    }

    private async _applyAllAutofixChanges(selectedSolutions: IEditSuggestion[]): Promise<void> {
        const mappedByFile: Map<string, IEditSuggestion[]> = new Map();
        for (const suggestion of selectedSolutions) {
            const filePath = pathNameToAbsPath(suggestion.qualifiedPathName);
            if (mappedByFile.has(filePath)) {
                mappedByFile.get(filePath)!.push(suggestion);
            } else {
                mappedByFile.set(filePath, [suggestion]);
            }
        }

        for (const [filePath, suggestions] of mappedByFile.entries()) {
            await this._applyAutofixChanges(filePath, suggestions);
        }
    }
}

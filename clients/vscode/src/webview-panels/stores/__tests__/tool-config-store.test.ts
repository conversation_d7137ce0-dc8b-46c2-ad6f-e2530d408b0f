/* eslint-disable @typescript-eslint/naming-convention */
import { MCPServer } from "$vscode/src/webview-providers/webview-messages";

import {
    ToolDefinitionWithSettings,
    ToolHostName,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import type { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";

import { AugmentGlobalState, FileStorageKey } from "../../../utils/context";
import { ToolConfigStore } from "../tool-config-store";

// Mock the logger
jest.mock("../../../logging", () => ({
    getLogger: jest.fn(() => ({
        debug: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    })),
}));

// Mock McpHost.getServerName
jest.mock("@augment-internal/sidecar-libs/src/tools/mcp/mcp-host", () => ({
    McpHost: {
        getServerName: jest.fn((name, command) => {
            // Return the name if provided, otherwise extract from command
            if (name && name.length > 0) {
                return name.replace(/[^a-zA-Z0-9_-]/g, "_");
            }
            const baseCommand = command.split(/\s+/)[0];
            const fileName = baseCommand.split(/[/\\]/).pop() || "mcp";
            return fileName.replace(/[^a-zA-Z0-9_-]/g, "_");
        }),
    },
}));

describe("ToolConfigStore", () => {
    // Mock AugmentGlobalState
    const mockStorage = {
        save: jest.fn(),
        load: jest.fn(),
        update: jest.fn(),
        get: jest.fn(),
        onDidChangeFileStorage: jest.fn(),
    } as unknown as AugmentGlobalState;

    // Add mockResolvedValue to the functions
    (mockStorage.save as jest.Mock).mockResolvedValue(undefined);
    (mockStorage.load as jest.Mock).mockResolvedValue(undefined);
    (mockStorage.update as jest.Mock).mockResolvedValue(undefined);

    // Mock ToolsModel with proper jest mock functions
    const mockToolsModel = {
        getToolStatusForSettingsPanel: jest.fn(),
        setMcpServers: jest.fn(),
    } as unknown as ToolsModel;

    // Add mockResolvedValue to the function
    (mockToolsModel.getToolStatusForSettingsPanel as jest.Mock).mockResolvedValue([]);

    // Mock getSettingsMcpServers function
    const mockGetSettingsMcpServers = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("getMCPServers", () => {
        it("should return an empty array when no servers exist", async () => {
            // Setup
            (mockStorage.load as jest.Mock).mockResolvedValue(null);
            const store = new ToolConfigStore(
                mockStorage,
                mockToolsModel,
                mockGetSettingsMcpServers
            );

            // Execute
            const result = await store.getMCPServers();

            // Verify
            expect(result).toEqual([]);
            expect(mockStorage.load).toHaveBeenCalledWith(FileStorageKey.mcpServers);
        });

        it("should map tools to servers correctly", async () => {
            // Setup
            const mockServers: MCPServer[] = [
                {
                    id: "server1",
                    name: "server1",
                    command: "/path/to/server1",
                    arguments: "",
                },
                {
                    id: "server2",
                    name: "server2",
                    command: "/path/to/server2",
                    arguments: "",
                },
            ];

            const mockToolDefinitions: ToolDefinitionWithSettings[] = [
                {
                    definition: {
                        name: "tool1",
                        description: "Tool 1",
                        input_schema_json: "{}",
                        tool_safety: 0,
                        mcp_server_name: "server1",
                        mcp_tool_name: "tool1",
                    },
                    identifier: {
                        hostName: ToolHostName.mcpHost,
                        toolId: "tool1",
                    },
                    isConfigured: true,
                    enabled: true,
                    toolSafety: 0,
                },
                {
                    definition: {
                        name: "tool2",
                        description: "Tool 2",
                        input_schema_json: "{}",
                        tool_safety: 0,
                        mcp_server_name: "server1",
                        mcp_tool_name: "tool2",
                    },
                    identifier: {
                        hostName: ToolHostName.mcpHost,
                        toolId: "tool2",
                    },
                    isConfigured: true,
                    enabled: false, // This tool is disabled
                    toolSafety: 0,
                },
                {
                    definition: {
                        name: "tool3",
                        description: "Tool 3",
                        input_schema_json: "{}",
                        tool_safety: 0,
                        mcp_server_name: "server2",
                        mcp_tool_name: "tool3",
                    },
                    identifier: {
                        hostName: ToolHostName.mcpHost,
                        toolId: "tool3",
                    },
                    isConfigured: true,
                    enabled: true,
                    toolSafety: 0,
                },
                {
                    definition: {
                        name: "tool4",
                        description: "Tool 4",
                        input_schema_json: "{}",
                        tool_safety: 0,
                        // No mcp_server_name, should be skipped
                    },
                    identifier: {
                        hostName: ToolHostName.localToolHost,
                        toolId: "tool4" as any, // Type assertion to bypass type checking for test
                    },
                    isConfigured: true,
                    enabled: true,
                    toolSafety: 0,
                },
            ];

            (mockStorage.load as jest.Mock).mockResolvedValue(mockServers);
            (mockToolsModel.getToolStatusForSettingsPanel as jest.Mock).mockResolvedValue(
                mockToolDefinitions
            );

            const store = new ToolConfigStore(
                mockStorage,
                mockToolsModel,
                mockGetSettingsMcpServers
            );

            // Execute
            const result = await store.getMCPServers();

            // Verify
            expect(result).toHaveLength(2);

            // Server 1 should have tool1 and tool2, with tool2 disabled
            const server1 = result.find((s) => s.name === "server1");
            expect(server1).toBeDefined();
            expect(server1?.tools).toEqual(["tool1", "tool2"]);
            expect(server1?.disabledTools).toEqual(["tool2"]);

            // Server 2 should have tool3, with no disabled tools
            const server2 = result.find((s) => s.name === "server2");
            expect(server2).toBeDefined();
            expect(server2?.tools).toEqual(["tool3"]);
            expect(server2?.disabledTools).toEqual([]);
        });

        it("should handle errors gracefully", async () => {
            // Setup
            (mockStorage.load as jest.Mock).mockRejectedValue(new Error("Test error"));
            const store = new ToolConfigStore(
                mockStorage,
                mockToolsModel,
                mockGetSettingsMcpServers
            );

            // Execute
            const result = await store.getMCPServers();

            // Verify
            expect(result).toEqual([]);
        });

        it("should handle non-array return values", async () => {
            // Setup - simulate a corrupted storage value
            (mockStorage.load as jest.Mock).mockResolvedValue(
                "not an array" as unknown as MCPServer[]
            );
            const store = new ToolConfigStore(
                mockStorage,
                mockToolsModel,
                mockGetSettingsMcpServers
            );

            // Execute
            const result = await store.getMCPServers();

            // Verify
            expect(result).toEqual([]);
        });
    });
});

import { MCPServer } from "$vscode/src/webview-providers/webview-messages";

import { getErrmsg } from "@augment-internal/sidecar-libs/src/exceptions";
import { McpHost } from "@augment-internal/sidecar-libs/src/tools/mcp/mcp-host";
import { McpServerConfig } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { TerminalSettings } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import type { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";

import { getLogger } from "../../logging";
import { AugmentGlobalState } from "../../utils/context";
import { FileStorageKey } from "../../utils/context";
import { CONFIG_VERSION } from "../settings-panel-types";
import { ToolConfigError, type ToolConfigState } from "../settings-panel-types";

/**
 * Manages the storage and retrieval of tool configurations, MCP server configurations, and terminal settings.
 */
export class ToolConfigStore {
    private readonly logger = getLogger("ToolConfigStore");

    constructor(
        private readonly storage: AugmentGlobalState,
        private readonly toolsModel?: ToolsModel,
        private readonly getSettingsMcpServers?: () => McpServerConfig[] | undefined
    ) {}

    /**
     * Gets the tool configuration state from persistent storage.
     * If no configuration exists, returns a default empty state.
     *
     * This method doesn't throw exceptions - if loading fails, it logs the error
     * and returns a default empty state to ensure the application can continue.
     */
    public async get(): Promise<ToolConfigState> {
        try {
            const state = await this.storage.load<ToolConfigState>(
                FileStorageKey.toolsConfiguration
            );
            return (
                state ?? {
                    version: CONFIG_VERSION,
                    tools: [],
                }
            );
        } catch (error) {
            // Log the error but don't throw
            this.logger.error(`Failed to load tool configurations: ${getErrmsg(error)}`);

            // Return a default state so the application can continue
            return {
                version: CONFIG_VERSION,
                tools: [],
            };
        }
    }

    /**
     * Saves the tool configuration state to persistent storage.
     * @param state The tool configuration state to save
     * @throws If saving fails
     */
    public async save(state: ToolConfigState): Promise<void> {
        try {
            await this.storage.save(FileStorageKey.toolsConfiguration, state);
        } catch (error) {
            // Log the error before throwing
            const msg = `Failed to save tool configurations: ${getErrmsg(error)}`;
            this.logger.error(msg);
            throw new ToolConfigError(msg);
        }
    }

    /**
     * Gets the MCP server configurations from persistent storage.
     * If no configurations exist, returns an empty array.
     */
    public async getMCPServers(): Promise<MCPServer[]> {
        try {
            const disabledServerToolMapping = new Map<string, Set<string>>();
            const serverToolMapping = new Map<string, Set<string>>();
            let servers = await this.storage.load<MCPServer[]>(FileStorageKey.mcpServers);
            if (this.toolsModel) {
                const toolDefinitions = await this.toolsModel.getToolStatusForSettingsPanel();
                for (const toolDefinition of toolDefinitions) {
                    if (!toolDefinition.definition.mcp_server_name) {
                        continue;
                    }
                    const serverName = toolDefinition.definition.mcp_server_name;
                    if (toolDefinition.definition.mcp_tool_name) {
                        if (!serverToolMapping.has(serverName)) {
                            serverToolMapping.set(serverName, new Set());
                        }
                        serverToolMapping
                            .get(serverName)
                            ?.add(toolDefinition.definition.mcp_tool_name);
                        // Add disabled tools to mapping
                        if (!toolDefinition.enabled) {
                            if (!disabledServerToolMapping.has(serverName)) {
                                disabledServerToolMapping.set(serverName, new Set());
                            }
                            disabledServerToolMapping
                                .get(serverName)
                                ?.add(toolDefinition.definition.mcp_tool_name);
                        }
                    }
                }
            }
            servers = (servers ?? []).map((server) => {
                // Use type assertion to avoid the ESLint error
                const _serverName = (
                    McpHost.getServerName as (name: string, command: string) => string
                )(server.name, server.command);

                return {
                    ...server,
                    tools: Array.from(serverToolMapping.get(_serverName) ?? []),
                    disabledTools: Array.from(disabledServerToolMapping.get(_serverName) ?? []),
                };
            });
            return Array.isArray(servers) ? servers : [];
        } catch (error) {
            // Log the error but don't throw
            this.logger.error(`Failed to load MCP servers: ${getErrmsg(error)}`);
            return [];
        }
    }

    /**
     * Saves the MCP server configurations to persistent storage.
     * @param servers The MCP server configurations to save
     * @throws If saving fails
     */
    public async saveMCPServers(servers: MCPServer[]): Promise<void> {
        try {
            await this.storage.save(FileStorageKey.mcpServers, servers);

            // Update the tools model with the new MCP server configurations
            await this.updateSidecarMCPServers();
        } catch (error) {
            // Log the error before throwing
            const msg = `Failed to save MCP servers: ${getErrmsg(error)}`;
            this.logger.error(msg);
            throw new ToolConfigError(msg);
        }
    }

    /**
     * Update the sidecar with the current MCP server configurations
     *
     * This method combines MCP servers from two sources:
     * 1. MCP servers stored in the extension's global state (added through the UI)
     * 2. MCP servers configured in settings.json (if any)
     */
    public async updateSidecarMCPServers(): Promise<void> {
        try {
            // Get MCP servers from storage (added through the UI)
            const servers = await this.getMCPServers();

            // Convert to the format expected by the sidecar
            // All servers from the UI (stored in extension state) use shell interpolation
            const mcpServerConfigs = servers.map((server) => ({
                name: server.name,
                command: server.command,
                args: [], // Empty args array since we're using shell interpolation
                useShellInterpolation: true, // Always true for UI-based servers
                env: server.env, // Include environment variables if they exist
                disabled: server.disabled,
            }));

            // Get MCP servers from settings.json (if available)
            const settingsMcpServers = this.getSettingsMcpServers
                ? this.getSettingsMcpServers()
                : [];

            // Combine MCP servers from both sources
            const combinedMcpServers: McpServerConfig[] = [...mcpServerConfigs];
            if (settingsMcpServers && settingsMcpServers.length > 0) {
                combinedMcpServers.push(...settingsMcpServers);
            }

            // Update the tools model with the combined MCP server configurations
            if (this.toolsModel) {
                this.toolsModel.setMcpServers(combinedMcpServers);
            }
        } catch (error) {
            this.logger.error(`Failed to update sidecar MCP servers: ${getErrmsg(error)}`);
        }
    }

    /**
     * Gets the terminal settings from persistent storage.
     * If no settings exist, returns a default empty state.
     */
    public async getTerminalSettings(): Promise<TerminalSettings> {
        try {
            const settings = await this.storage.load<TerminalSettings>(
                FileStorageKey.terminalSettings
            );
            return settings ?? { supportedShells: [], selectedShell: undefined };
        } catch (error) {
            // Log the error but don't throw
            this.logger.error(`Failed to load terminal settings: ${getErrmsg(error)}`);
            return { supportedShells: [], selectedShell: undefined };
        }
    }

    /**
     * Saves the terminal settings to persistent storage.
     * @param settings The terminal settings to save
     * @throws If saving fails
     */
    public async saveTerminalSettings(settings: TerminalSettings): Promise<void> {
        try {
            await this.storage.save(FileStorageKey.terminalSettings, settings);
        } catch (error) {
            const msg = `Failed to save terminal settings: ${getErrmsg(error)}`;
            this.logger.error(msg);
            throw new ToolConfigError(msg);
        }
    }

    /**
     * Updates the selected shell in the terminal settings.
     * @param shellName The name of the selected shell
     * @throws If saving fails
     */
    public async updateSelectedShell(shellName: string): Promise<void> {
        const settings = await this.getTerminalSettings();
        settings.selectedShell = shellName;
        await this.saveTerminalSettings(settings);
    }

    /**
     * Updates the startup script in the terminal settings.
     * @param script The startup script content
     * @throws If saving fails
     */
    public async updateStartupScript(script: string): Promise<void> {
        const settings = await this.getTerminalSettings();
        settings.startupScript = script;
        await this.saveTerminalSettings(settings);
    }
}

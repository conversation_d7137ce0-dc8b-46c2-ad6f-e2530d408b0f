/* eslint-disable @typescript-eslint/naming-convention */
import { nextEditContributes } from "./contributes-config.next-edit";
import { IContributesConfig } from "./contributes-types";
import { mergeContributes } from "./contributes-utils";

const experimentalContributes = {
    configuration: [
        {
            title: "Experimental",
            properties: {
                "augment.chat.userGuidelines": {
                    type: "string",
                    order: 5,
                    default: "",
                    description: "Edit this field on the Augment settings page.",
                },
            },
        },
    ],
};

const defaultContributes: IContributesConfig = {
    customEditors: [
        {
            viewType: "rules.augment",
            displayName: "Augment Rules Viewer",
            selector: [
                {
                    filenamePattern: "**/.augment/rules/**/*.md",
                },
            ],
            priority: "default",
        },
        {
            viewType: "memories.augment",
            displayName: "Augment Memories Viewer",
            selector: [
                {
                    filenamePattern:
                        "**/workspaceStorage/*/Augment.vscode-augment/Augment-Memories",
                },
            ],
            priority: "default",
        },
    ],
    configuration: [
        {
            title: "Augment",
            properties: {
                "augment.completions.enableAutomaticCompletions": {
                    type: "boolean",
                    order: 0,
                    default: true,
                    description:
                        "Provide automatic inline code completions (manual code completions are always available).",
                },
                "augment.completions.enableQuickSuggestions": {
                    type: "boolean",
                    order: 1,
                    default: true,
                    description: "Add Augment to the IntelliSense pop-up suggestions.",
                },
                "augment.completions.disableCompletionsByLanguage": {
                    type: "array",
                    order: 2,
                    default: ["git-commit", "scminput"],
                    markdownDescription:
                        "Disable completions by [language identifiers](https://code.visualstudio.com/docs/languages/identifiers).",
                    items: {
                        type: "string",
                    },
                    uniqueItems: true,
                },
                "augment.enableEmptyFileHint": {
                    type: "boolean",
                    order: 3,
                    default: true,
                    description: "Display a hint to use Augment Chat when an empty file is open.",
                },
                "augment.conflictingCodingAssistantCheck": {
                    type: "boolean",
                    order: 4,
                    default: true,
                    description:
                        "Check for conflicting coding assistants when starting up and installing extensions.",
                },
                "augment.advanced": {
                    type: "object",
                    order: 99999,
                    default: {},
                    properties: {
                        apiToken: {
                            type: "string",
                            default: "",
                            description: "API token for Augment access.",
                        },
                        completionURL: {
                            type: "string",
                            default: "",
                            description: "URL of completion server.",
                        },
                        completions: {
                            type: "object",
                            default: {},
                            properties: {
                                timeoutMs: {
                                    default: 800,
                                    type: ["number", "null"],
                                    description:
                                        "The default timeout for completions (in milliseconds).",
                                },
                                maxWaitMs: {
                                    default: 1600,
                                    type: ["number", "null"],
                                    description:
                                        "The max timeout for completions items (in milliseconds). This allows Augment to retry completions that are cancelled due to changes in the editor.",
                                },
                                addIntelliSenseSuggestions: {
                                    default: true,
                                    type: "boolean",
                                    description: "Enable completions in the intellisense pop-up.",
                                },
                            },
                        },
                        // Note(rich): Commented out for now, until support is added in the code.
                        // tools: {
                        //     type: "object",
                        //     default: {},
                        //     properties: {
                        //         launchProcess: {
                        //             type: "boolean",
                        //             default: true,
                        //             description:
                        //                 "Enable the launch process tool for local execution.",
                        //         },
                        //         shell: {
                        //             type: "boolean",
                        //             default: true,
                        //             description: "Enable the shell tool for local execution.",
                        //         },
                        //         saveFile: {
                        //             type: "boolean",
                        //             default: true,
                        //             description: "Enable the save file tool for local execution.",
                        //         },
                        //    },
                        // },
                        mcpServers: {
                            type: "array",
                            default: [{}],
                            items: {
                                type: "object",
                                properties: {
                                    command: {
                                        type: "string",
                                        description: "The command to run the MCP server",
                                    },
                                    args: {
                                        type: "array",
                                        items: {
                                            type: "string",
                                        },
                                        description: "Arguments to pass to the MCP server command",
                                    },
                                    timeoutMs: {
                                        type: "number",
                                        description:
                                            "Timeout in milliseconds for MCP server operations",
                                    },
                                    env: {
                                        type: "object",
                                        additionalProperties: {
                                            type: "string",
                                        },
                                        description:
                                            "Dictionary of Environment variables to set for the MCP server",
                                    },
                                },
                            },
                            description: "List of MCP server configurations",
                        },
                        integrations: {
                            type: "object",
                            default: {},
                            properties: {
                                atlassian: {
                                    type: "object",
                                    default: {},
                                    properties: {
                                        serverUrl: {
                                            type: "string",
                                            default: "",
                                            description: "Atlassian server URL",
                                        },
                                        personalApiToken: {
                                            type: "string",
                                            default: "",
                                            description: "Personal API token for Atlassian",
                                        },
                                        username: {
                                            type: "string",
                                            default: "",
                                            description: "Atlassian username",
                                        },
                                    },
                                },
                                notion: {
                                    type: "object",
                                    default: {},
                                    properties: {
                                        apiToken: {
                                            type: "string",
                                            default: "",
                                            description: "API token for Notion",
                                        },
                                    },
                                },
                                linear: {
                                    type: "object",
                                    default: {},
                                    properties: {
                                        apiToken: {
                                            type: "string",
                                            default: "",
                                            description: "API token for Linear",
                                        },
                                    },
                                },
                                github: {
                                    type: "object",
                                    default: {},
                                    properties: {
                                        apiToken: {
                                            type: "string",
                                            default: "",
                                            description: "API token for GitHub",
                                        },
                                    },
                                },
                            },
                            description: "Integration configurations for third-party services",
                        },
                    },
                },
            },
        },
    ],
    commands: [
        {
            command: "vscode-augment.autofixCommand",
            title: "Augment: Autofix",
            category: "Augment",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.o",
            title: "View Diff",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.i",
            title: "Code Instruction",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.aac",
            title: "Accept All Chunks",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.afc",
            title: "Accept Focused Chunk",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.rfc",
            title: "Reject Focused Chunk",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.fpc",
            title: "Focus Previous Chunk",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.fnc",
            title: "Focus Next Chunk",
        },
        {
            category: "Augment",
            command: "vscode-augment.internal-dv.c",
            title: "Close Diff View",
        },
        {
            category: "Augment",
            command: "vscode-augment.insertCompletion",
            title: "Insert Completion",
        },
        {
            category: "Augment",
            command: "vscode-augment.settings",
            title: "$(gear) Edit Settings...",
        },
        {
            category: "Augment",
            command: "vscode-augment.keyboard-shortcuts",
            title: "$(keyboard) Edit Keyboard Shortcuts...",
        },
        {
            category: "Augment",
            command: "vscode-augment.showDocs",
            title: "Help",
            icon: "$(question)",
        },
        {
            category: "Augment",
            command: "vscode-augment.showAccountPage",
            title: "Account & Billing",
            icon: "$(note)",
        },
        {
            category: "Augment",
            command: "vscode-augment.toggleAutomaticCompletionSetting",
            title: "Toggle Automatic Completions",
        },
        {
            command: "vscode-augment.manageAccountCommunity",
            title: "$(accounts-view-bar-icon) Manage Account (Community)",
            when: "augment.userTier == 'community'",
        },
        {
            command: "vscode-augment.manageAccountProfessional",
            title: "$(accounts-view-bar-icon) Manage Account (Self-Serve)",
            when: "augment.userTier == 'professional'",
        },
        {
            command: "vscode-augment.manageAccountEnterprise",
            title: "$(accounts-view-bar-icon) Manage Account (Enterprise)",
            when: "augment.userTier == 'enterprise'",
        },
        {
            category: "Augment",
            command: "vscode-augment.signIn",
            title: "$(sign-in) Sign In",
        },
        {
            category: "Augment",
            command: "vscode-augment.signOut",
            title: "$(sign-out) Sign Out",
        },
        {
            category: "Augment",
            command: "vscode-augment.chat.slash.fix",
            title: "Fix using Augment",
        },
        {
            category: "Augment",
            command: "vscode-augment.chat.slash.explain",
            title: "Explain using Augment",
        },
        {
            category: "Augment",
            command: "vscode-augment.chat.slash.test",
            title: "Write test using Augment",
        },
        {
            category: "Augment",
            command: "vscode-augment.chat.slash.document",
            title: "Document using Augment",
        },
        {
            category: "Augment",
            command: "vscode-augment.showHistoryPanel",
            title: "$(history) Show History",
        },
        {
            category: "Augment",
            command: "vscode-augment.copySessionId",
            title: "Copy Session ID",
        },
        {
            category: "Augment",
            command: "_vscode-augment.showSidebarWorkspaceContext",
            title: "Manage Workspace Context",
            icon: "$(folder-opened)",
        },
        {
            category: "Augment",
            command: "_vscode-augment.showSidebarChat",
            title: "Show Chat",
            icon: "$(comment-discussion)",
        },
        {
            category: "Augment",
            command: "vscode-augment.generateCommitMessage",
            title: "Generate Commit Message with Augment",
            icon: "$(sparkle)",
            when: "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0",
        },
        {
            category: "Augment",
            command: "vscode-augment.showSettingsPanel",
            title: "Settings",
            icon: "$(settings-gear)",
        },
        {
            category: "Augment",
            command: "vscode-augment.showAugmentCommands",
            title: "Show Augment Commands",
            icon: "$(menu)",
        },
        {
            category: "Augment",
            command: "vscode-augment.focusAugmentPanel",
            title: "$(layout-sidebar-left) Open Augment",
        },
        {
            category: "Augment",
            command: "vscode-augment.startNewChat",
            title: "Start New Chat",
        },
        {
            category: "Augment",
            command: "vscode-augment.clear-recent-editing-history",
            title: "Clear Recent Editing History",
        },
        {
            category: "Augment",
            command: "vscode-augment.showRemoteAgentsPanel",
            title: "Show Remote Agents Panel",
            when: "vscode-augment.featureFlags.enableRemoteAgents",
        },
        {
            category: "Augment",
            command: "vscode-augment.openSshConfig",
            title: "Open Augment SSH Config",
            when: "vscode-augment.featureFlags.enableRemoteAgents",
        },
    ],
    icons: {
        "augment-icon-simple": {
            description: "Augment logo (simple)",
            default: {
                fontPath: "augment-icon-font.woff",
                fontCharacter: "\\E900",
            },
        },
        "augment-icon-smile": {
            description: "Augment logo (smile)",
            default: {
                fontPath: "augment-icon-font.woff",
                fontCharacter: "\\E901",
            },
        },
        "augment-icon-zero": {
            description: "Augment logo (zero)",
            default: {
                fontPath: "augment-icon-font.woff",
                fontCharacter: "\\E902",
            },
        },
        "augment-icon-error": {
            description: "Augment logo (error)",
            default: {
                fontPath: "augment-icon-font.woff",
                fontCharacter: "\\E903",
            },
        },
        "augment-icon-closed-eyes": {
            description: "Augment logo (closed eyes)",
            default: {
                fontPath: "augment-icon-font.woff",
                fontCharacter: "\\E904",
            },
        },
        "augment-icon-dots": {
            description: "Augment logo (dots)",
            default: {
                fontPath: "augment-icon-font.woff",
                fontCharacter: "\\E905",
            },
        },
        "augment-kb-z": {
            description: "Keyboard icon Z",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f101",
            },
        },
        "augment-kb-y": {
            description: "Keyboard icon Y",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f102",
            },
        },
        "augment-kb-x": {
            description: "Keyboard icon X",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f103",
            },
        },
        "augment-kb-win": {
            description: "Keyboard icon Win",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f104",
            },
        },
        "augment-kb-w": {
            description: "Keyboard icon W",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f105",
            },
        },
        "augment-kb-v": {
            description: "Keyboard icon V",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f106",
            },
        },
        "augment-kb-u": {
            description: "Keyboard icon U",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f107",
            },
        },
        "augment-kb-tab": {
            description: "Keyboard icon Tab",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f108",
            },
        },
        "augment-kb-t": {
            description: "Keyboard icon T",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f109",
            },
        },
        "augment-kb-shift": {
            description: "Keyboard icon Shift",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f10a",
            },
        },
        "augment-kb-semicolon": {
            description: "Keyboard icon Semicolon",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f10b",
            },
        },
        "augment-kb-s": {
            description: "Keyboard icon S",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f10c",
            },
        },
        "augment-kb-return": {
            description: "Keyboard icon Return",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f10d",
            },
        },
        "augment-kb-r": {
            description: "Keyboard icon R",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f10e",
            },
        },
        "augment-kb-q": {
            description: "Keyboard icon Q",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f10f",
            },
        },
        "augment-kb-p": {
            description: "Keyboard icon P",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f110",
            },
        },
        "augment-kb-option": {
            description: "Keyboard icon Option",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f111",
            },
        },
        "augment-kb-o": {
            description: "Keyboard icon O",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f112",
            },
        },
        "augment-kb-n": {
            description: "Keyboard icon N",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f113",
            },
        },
        "augment-kb-meta": {
            description: "Keyboard icon Meta",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f114",
            },
        },
        "augment-kb-m": {
            description: "Keyboard icon M",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f115",
            },
        },
        "augment-kb-l": {
            description: "Keyboard icon L",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f116",
            },
        },
        "augment-kb-k": {
            description: "Keyboard icon K",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f117",
            },
        },
        "augment-kb-j": {
            description: "Keyboard icon J",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f118",
            },
        },
        "augment-kb-i": {
            description: "Keyboard icon I",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f119",
            },
        },
        "augment-kb-h": {
            description: "Keyboard icon H",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f11a",
            },
        },
        "augment-kb-g": {
            description: "Keyboard icon G",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f11b",
            },
        },
        "augment-kb-f": {
            description: "Keyboard icon F",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f11c",
            },
        },
        "augment-kb-escape": {
            description: "Keyboard icon Escape",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f11d",
            },
        },
        "augment-kb-e": {
            description: "Keyboard icon E",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f11e",
            },
        },
        "augment-kb-delete": {
            description: "Keyboard icon Delete",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f11f",
            },
        },
        "augment-kb-d": {
            description: "Keyboard icon D",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f120",
            },
        },
        "augment-kb-ctrl": {
            description: "Keyboard icon Ctrl",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f121",
            },
        },
        "augment-kb-control": {
            description: "Keyboard icon Control",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f122",
            },
        },
        "augment-kb-command": {
            description: "Keyboard icon Command",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f123",
            },
        },
        "augment-kb-c": {
            description: "Keyboard icon C",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f124",
            },
        },
        "augment-kb-backspace": {
            description: "Keyboard icon Backspace",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f125",
            },
        },
        "augment-kb-b": {
            description: "Keyboard icon B",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f126",
            },
        },
        "augment-kb-alt": {
            description: "Keyboard icon Alt",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f127",
            },
        },
        "augment-kb-a": {
            description: "Keyboard icon A",
            default: {
                fontPath: "augment-kb-icon-font.woff",
                fontCharacter: "\\f128",
            },
        },
    },
    keybindings: [
        {
            command: "vscode-augment.insertCompletion",
            when: "editorHasCompletionItemProvider && editorTextFocus && !editorReadonly",
            key: "ctrl-f11",
            mac: "ctrl-f11",
        },
        {
            command: "vscode-augment.focusAugmentPanel",
            key: "ctrl-alt-i",
            mac: "cmd-ctrl-i",
        },
        {
            command: "vscode-augment.focusAugmentPanel",
            key: "ctrl-l",
            mac: "cmd-l",
        },
        {
            command: "vscode-augment.internal-dv.o",
            when: "vscode-augment.enableDebugFeatures",
            key: "ctrl-alt-o",
            mac: "cmd-ctrl-o",
        },
        {
            command: "vscode-augment.internal-dv.i",
            when: "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus",
            key: "ctrl-i",
            mac: "cmd-i",
        },
        {
            command: "vscode-augment.internal-dv.aac",
            when: "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused",
            key: "ctrl-enter",
            mac: "cmd-enter",
        },
        {
            command: "vscode-augment.internal-dv.afc",
            when: "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused",
            key: "enter",
            mac: "enter",
        },
        {
            command: "vscode-augment.internal-dv.rfc",
            when: "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused",
            key: "backspace",
        },
        {
            command: "vscode-augment.internal-dv.fpc",
            when: "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused",
            key: "up",
        },
        {
            command: "vscode-augment.internal-dv.fnc",
            when: "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused",
            key: "down",
        },
        {
            command: "vscode-augment.internal-dv.c",
            when: "(vscode-augment.internal-dv.enabled || vscode-augment.enableDebugFeatures) && activeWebviewPanelId == augmentDiffView && vscode-augment.internal-dv.panel-focused",
            key: "escape",
        },
        {
            command: "vscode-augment.showAugmentCommands",
            key: "ctrl-shift-a",
            mac: "cmd-shift-a",
        },
        {
            command: "vscode-augment.toggleAutomaticCompletionSetting",
            key: "ctrl-alt-a",
            mac: "cmd-alt-a",
        },
        {
            command: "vscode-augment.showRemoteAgentsPanel",
            when: "vscode-augment.featureFlags.enableRemoteAgents",
            key: "ctrl-shift-r",
            mac: "cmd-shift-r",
            args: "keybinding",
        },
    ],
    submenus: [
        {
            id: "vscode-augment.context-submenu",
            label: "Send to Augment",
        },
        {
            id: "vscode-augment.viewTitleMenuEntryPoint",
            label: "Augment Options",
            icon: "$(menu)",
        },
    ],
    menus: {
        "view/title": [
            {
                submenu: "vscode-augment.viewTitleMenuEntryPoint",
                when: "view == augment-chat && vscode-augment.mainPanel.app == 'chat'",
                group: "navigation@0",
            },
        ],
        "vscode-augment.viewTitleMenuEntryPoint": [
            {
                command: "vscode-augment.showSettingsPanel",
                group: "menu@1",
            },
            {
                command: "vscode-augment.showDocs",
                group: "menu@2",
            },
            {
                command: "vscode-augment.showAccountPage",
                group: "menu@3",
            },
            {
                command: "vscode-augment.signOut",
                group: "menu@4",
            },
        ],
        "editor/context": [
            {
                submenu: "vscode-augment.context-submenu",
                group: "0_augment",
            },
        ],
        "vscode-augment.context-submenu": [
            {
                command: "vscode-augment.focusAugmentPanel",
                when: "editorHasSelection",
            },
            {
                command: "vscode-augment.chat.slash.explain",
                when: "editorHasSelection",
            },
            {
                command: "vscode-augment.chat.slash.test",
                when: "editorHasSelection",
            },
            {
                command: "vscode-augment.chat.slash.fix",
                when: "editorHasSelection",
            },
            {
                command: "vscode-augment.chat.slash.document",
                when: "editorHasSelection",
            },
        ],
        "editor/lineNumber/context": [
            {
                command: "_vscode-augment.next-edit.background.open",
                when: "vscode-augment.enableNextEdit && editorLineNumber in vscode-augment.nextEdit.linesWithGutterIconActions && resourcePath in vscode-augment.nextEdit.filesWithGutterIconActions",
                group: "navigation@0",
            },
        ],
        commandPalette: [
            {
                command: "vscode-augment.internal-dv.o",
                when: "vscode-augment.enableDebugFeatures",
            },
            {
                command: "vscode-augment.insertCompletion",
                when: "!editorReadonly",
            },
            {
                command: "vscode-augment.toggleAutomaticCompletionSetting",
            },
            {
                command: "vscode-augment.signIn",
                when: "vscode-augment.useOAuth && !vscode-augment.isLoggedIn",
            },
            {
                command: "vscode-augment.signOut",
                when: "vscode-augment.useOAuth && vscode-augment.isLoggedIn",
            },
            {
                command: "vscode-augment.internal-dv.i",
                when: "(vscode-augment.internal-new-instructions.enabled || vscode-augment.enableDebugFeatures) && !editorReadonly && !terminalFocus",
            },
            {
                command: "vscode-augment.chat.slash.fix",
            },
            {
                command: "vscode-augment.startNewChat",
            },
            {
                command: "vscode-augment.focusAugmentPanel",
                when: "vscode-augment.enableDebugFeatures || vscode-augment.isLoggedIn",
            },
            {
                command: "_vscode-augment.showSidebarChat",
                when: "false",
            },
            {
                command: "_vscode-augment.showSidebarWorkspaceContext",
                when: "false",
            },
            {
                command: "vscode-augment.clear-recent-editing-history",
                when: "vscode-augment.enableNextEdit",
            },
            {
                command: "vscode-augment.generateCommitMessage",
                when: "false",
            },
            {
                command: "vscode-augment.showSettingsPanel",
            },
            {
                command: "vscode-augment.manageAccountCommunity",
                when: "augment.userTier == 'community'",
            },
            {
                command: "vscode-augment.manageAccountProfessional",
                when: "augment.userTier == 'professional'",
            },
            {
                command: "vscode-augment.manageAccountEnterprise",
                when: "augment.userTier == 'enterprise'",
            },
            {
                command: "vscode-augment.showRemoteAgentsPanel",
                when: "vscode-augment.featureFlags.enableRemoteAgents",
            },
            {
                command: "vscode-augment.openSshConfig",
                when: "vscode-augment.featureFlags.enableRemoteAgents",
            },
        ],
        "scm/title": [
            {
                command: "vscode-augment.generateCommitMessage",
                args: ["${resourceUri}"],
                group: "navigation",
                when: "vscode-augment.enableGenerateCommitMessage && gitOpenRepositoryCount != 0",
            },
        ],
    },
    viewsContainers: {
        activitybar: [
            {
                icon: "media/activitybar.svg",
                id: "augment-chat",
                title: "Augment",
            },
        ],
    },
    views: {
        "augment-chat": [
            {
                id: "augment-chat",
                name: "Augment",
                type: "webview",
            },
        ],
    },
};

export const contributes: IContributesConfig = mergeContributes(
    defaultContributes,
    nextEditContributes,
    experimentalContributes
);

import { IClientActions } from "@augment-internal/sidecar-libs/src/client-interfaces/client-actions";
import { ShowDiffViewOptions } from "@augment-internal/sidecar-libs/src/client-interfaces/client-actions-types";
import { DiffViewDocument } from "@augment-internal/sidecar-libs/src/diff-view/document";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import * as vscode from "vscode";

import { APIServer } from "../augment-api";
import { FuzzyFsSearcher } from "../chat/fuzzy-fs-searcher";
import { FuzzySymbolSearcher } from "../chat/fuzzy-symbol-searcher";
import { WorkTimer } from "../metrics/work-timer";
import { KeybindingWatcher } from "../utils/keybindings";
import { DiffViewPanelOptions, DiffViewWebviewPanel } from "../webview-panels/diff-view-panel";
import { WorkspaceManager } from "../workspace/workspace-manager";

export class ClientActions implements IClientActions {
    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _workspaceManager: WorkspaceManager,
        private readonly _apiServer: APIServer,
        private readonly _keybindingWatcher: KeybindingWatcher,
        private readonly _fuzzyFsSearcher: FuzzyFsSearcher,
        private readonly _fuzzySymbolSearcher: FuzzySymbolSearcher,
        private readonly _workTimer: WorkTimer
    ) {}

    public showDiffView(
        path: IQualifiedPathName,
        original: string | undefined,
        modified: string | undefined,
        options: ShowDiffViewOptions
    ): Promise<void> {
        // If retainFocus is true, only show diff if it's already visible
        const isDiffViewVisible = DiffViewWebviewPanel.isVisible ?? false;
        if (options.retainFocus && !isDiffViewVisible) {
            return Promise.resolve();
        }

        // Set up options
        let panelOpts: DiffViewPanelOptions = {
            editable: false,
            disableResolution: true,
            document: new DiffViewDocument(
                new QualifiedPathName(path.rootPath, path.relPath),
                original,
                modified,
                {}
            ),
            viewOptions: { preserveFocus: true, viewColumn: vscode.ViewColumn.Active },
        };

        // Create the diff view with disposal handling
        this._createOrShowDiffView(panelOpts);

        return Promise.resolve();
    }

    private _createOrShowDiffView(options: DiffViewPanelOptions) {
        DiffViewWebviewPanel.createOrShow(
            {
                extensionUri: this._extensionUri,
                workspaceManager: this._workspaceManager,
                apiServer: this._apiServer,
                keybindingWatcher: this._keybindingWatcher,
                fuzzyFsSearcher: this._fuzzyFsSearcher,
                fuzzySymbolSearcher: this._fuzzySymbolSearcher,
                workTimer: this._workTimer,
            },
            options
        );
    }
}

import {
    FileDeletedEvent,
    FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import {
    FileDetails,
    IClientWorkspaces,
    ListDirectoryResult,
    PathInfo,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { ISidecarDisposable } from "@augment-internal/sidecar-libs/src/lifecycle/disposable-types";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as path from "path";
import * as vscode from "vscode";

import { _getQualifiedPath, _readFile, getCwdForTool } from "../chat/tools-utils";
import { getLogger } from "../logging";
import { fileExists } from "../utils/fs-utils";
import { relativePathName } from "../utils/path-utils";
import { getUntitledDoc, isUntitledFile } from "../workspace/untitled";
import { WorkspaceManager } from "../workspace/workspace-manager";

export class ClientWorkspaces implements IClientWorkspaces {
    private readonly _logger = getLogger("ClientWorkspaces");
    private _ripgrepPath: string | undefined = undefined;

    constructor(private readonly _workspaceManager: WorkspaceManager) {}

    getCwd(): Promise<string | undefined> {
        return Promise.resolve(getCwdForTool(this._workspaceManager));
    }

    async readFile(path: string): Promise<FileDetails> {
        await this._workspaceManager.awaitInitialFoldersEnumerated();
        const contents = await _readFile(path, this._workspaceManager);
        const filepath = _getQualifiedPath(path, this._workspaceManager);
        return {
            contents,
            filepath,
        };
    }

    async writeFile(filePath: QualifiedPathName, contents: string): Promise<void> {
        const doc = await getVsCodeTextDocument(filePath, contents);
        if (!doc) {
            return;
        }

        // Check if the document already has the same content as the modified code, to avoid unnecessary writes
        if (doc.getText() === contents) {
            return;
        }

        const edit = new vscode.WorkspaceEdit();
        edit.replace(doc.uri, new vscode.Range(0, 0, doc.lineCount, 0), contents);
        await vscode.workspace.applyEdit(edit);
        // Save the file
        await doc.save();
    }

    async deleteFile(filePath: QualifiedPathName): Promise<void> {
        // Check if file exists before trying to delete it
        if (fileExists(filePath.absPath)) {
            try {
                const uri = vscode.Uri.file(filePath.absPath);
                await vscode.workspace.fs.delete(uri);
            } catch (e) {
                // eslint-disable-next-line no-console
                this._logger.error(`Failed to delete file ${filePath.absPath}:`, e);
            }
        }
    }

    async getQualifiedPathName(path: string): Promise<QualifiedPathName | undefined> {
        await this._workspaceManager.awaitInitialFoldersEnumerated();
        return _getQualifiedPath(path, this._workspaceManager);
    }

    async getPathInfo(path: string): Promise<PathInfo> {
        await this._workspaceManager.awaitInitialFoldersEnumerated();

        // Get the qualified path to ensure it's within the workspace
        const qualifiedPath = _getQualifiedPath(path, this._workspaceManager);
        if (!qualifiedPath) {
            return { filepath: undefined };
        }

        try {
            // Check if the path exists and get its type
            const stat = await vscode.workspace.fs.stat(vscode.Uri.file(qualifiedPath.absPath));
            return { type: stat.type, filepath: qualifiedPath, exists: true };
        } catch (error) {
            // Path doesn't exist or can't be accessed
            return { filepath: qualifiedPath, exists: false };
        }
    }

    async findFiles(
        includeGlob: string,
        excludeGlob?: string | null,
        maxResults?: number,
        timelimit?: number
    ): Promise<QualifiedPathName[]> {
        await this._workspaceManager.awaitInitialFoldersEnumerated();

        let cancellationTokenSource: vscode.CancellationTokenSource | undefined;
        let timeoutHandle: NodeJS.Timeout | undefined;

        // Create a cancellation token if timelimit is specified
        if (timelimit && timelimit > 0) {
            cancellationTokenSource = new vscode.CancellationTokenSource();
            timeoutHandle = setTimeout(() => {
                this._logger.debug(`Cancelling findFiles due to timeout after ${timelimit}ms`);
                cancellationTokenSource?.cancel();
            }, timelimit);
        }

        try {
            const uris = await vscode.workspace.findFiles(
                includeGlob,
                excludeGlob,
                maxResults,
                cancellationTokenSource?.token
            );

            const results: QualifiedPathName[] = [];
            for (const uri of uris) {
                const workspaceFolder = vscode.workspace.getWorkspaceFolder(uri);
                if (workspaceFolder) {
                    const rootPath = workspaceFolder.uri.fsPath;
                    results.push(
                        new QualifiedPathName(rootPath, relativePathName(rootPath, uri.fsPath))
                    );
                }
            }

            return results;
        } finally {
            // Clean up timeout and cancellation token
            if (timeoutHandle) {
                clearTimeout(timeoutHandle);
            }
            if (cancellationTokenSource) {
                cancellationTokenSource.dispose();
            }
        }
    }

    async listDirectory(
        path: string,
        depth: number = 2,
        showHidden: boolean = false
    ): Promise<ListDirectoryResult> {
        await this._workspaceManager.awaitInitialFoldersEnumerated();

        // Get the qualified path to ensure it's within the workspace
        const qualifiedPath = _getQualifiedPath(path, this._workspaceManager);
        if (!qualifiedPath) {
            return { entries: [], errorMessage: "Path is outside the workspace" };
        }

        try {
            // Check if the path exists and is a directory
            const stat = await vscode.workspace.fs.stat(vscode.Uri.file(qualifiedPath.absPath));
            if (!(stat.type & vscode.FileType.Directory)) {
                return { entries: [], errorMessage: "Path is not a directory" };
            }

            // List directory contents with specified depth and hidden file visibility
            const results: string[] = [];
            await this._listDirectoryRecursive(
                qualifiedPath.absPath,
                qualifiedPath.absPath,
                0,
                depth,
                results,
                showHidden
            );

            // Sort results for consistent output
            results.sort();
            return { entries: results };
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : "Directory not found or cannot be read";
            return { entries: [], errorMessage };
        }
    }

    private async _listDirectoryRecursive(
        basePath: string,
        currentPath: string,
        currentDepth: number,
        maxDepth: number,
        results: string[],
        showHidden: boolean = false
    ): Promise<void> {
        if (currentDepth >= maxDepth) {
            return;
        }

        try {
            const entries = await vscode.workspace.fs.readDirectory(vscode.Uri.file(currentPath));

            for (const [name, type] of entries) {
                // Skip hidden files and directories (those starting with '.') unless showHidden is true
                if (!showHidden && name.startsWith(".")) {
                    continue;
                }

                const fullPath = path.join(currentPath, name);
                const relativePath = path.relative(basePath, fullPath);

                // Add the current entry to results
                results.push(relativePath || ".");

                // If it's a directory and we haven't reached max depth, recurse
                // current child is at depth currentDepth + 1
                if (type & vscode.FileType.Directory && currentDepth + 1 < maxDepth) {
                    await this._listDirectoryRecursive(
                        basePath,
                        fullPath,
                        currentDepth + 1,
                        maxDepth,
                        results,
                        showHidden
                    );
                }
            }
        } catch (error) {
            // Ignore errors for individual directories (e.g., permission denied)
            this._logger.warn(`Failed to list directory ${currentPath}:`, error);
        }
    }

    public async getRipgrepPath(): Promise<string | undefined> {
        if (this._ripgrepPath) {
            return Promise.resolve(this._ripgrepPath);
        }

        const vscodeAppRoot = vscode.env.appRoot;
        const binName = "rg";
        const checkPackage = (packageDir: string) => {
            const binPath = path.join(vscodeAppRoot, packageDir, binName);
            if (fileExists(binPath)) {
                return binPath;
            } else {
                return undefined;
            }
        };

        // ripgrep is part of vscode installation
        // it can be in different locations depending on the platform and the version of vscode
        this._ripgrepPath =
            checkPackage("node_modules/@vscode/ripgrep/bin/") ??
            checkPackage("node_modules/vscode-ripgrep/bin") ??
            checkPackage("node_modules.asar.unpacked/vscode-ripgrep/bin/") ??
            checkPackage("node_modules.asar.unpacked/@vscode/ripgrep/bin/");
        return Promise.resolve(this._ripgrepPath);
    }

    onFileDeleted(callback: (event: FileDeletedEvent) => void): ISidecarDisposable {
        return this._workspaceManager.onFileDeleted(callback);
    }

    onFileDidMove(callback: (event: FileDidMoveEvent) => void): ISidecarDisposable {
        return this._workspaceManager.onFileDidMove(callback);
    }
}

/**
 * Retrieves the VSCode TextDocument object for this document.
 * @returns A Promise resolving to the VSCode TextDocument or undefined if not found.
 */
async function getVsCodeTextDocument(
    filePath: QualifiedPathName,
    initContent?: string
): Promise<vscode.TextDocument | undefined> {
    try {
        let doc: vscode.TextDocument | undefined;
        if (isUntitledFile(filePath)) {
            doc = getUntitledDoc(filePath);
        } else if (fileExists(filePath.absPath)) {
            doc = await vscode.workspace.openTextDocument(filePath.absPath);
        } else {
            // Create a new document at the specified path
            const uri = vscode.Uri.file(filePath.absPath);
            await vscode.workspace.fs.writeFile(uri, Buffer.from(initContent ?? ""));
            doc = await vscode.workspace.openTextDocument(uri);
        }

        if (doc === undefined) {
            throw new Error(`Failed to open document ${filePath.absPath}`);
        }
        return doc;
    } catch (e) {
        return undefined;
    }
}

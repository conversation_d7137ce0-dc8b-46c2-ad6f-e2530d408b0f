import {
    IClientFeatureFlags,
    SidecarFlags,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";

import { FeatureFlagManager } from "../feature-flags";
import { getExtensionPatchNumber, isExtensionVersionGte } from "../utils/environment";

export class ClientFeatureFlags implements IClientFeatureFlags {
    constructor(private readonly _featureFlagManager: FeatureFlagManager) {}

    public get flags(): SidecarFlags {
        return {
            enableAgentMode: isExtensionVersionGte(
                // if pre-release (patch_no === 0) -> normal min version
                // else if stable (patch_no !== 0) -> stable min version
                // we consider `patch_no == 0` as pre-release (not stable)
                getExtensionPatchNumber() === 0
                    ? (this._featureFlagManager.currentFlags.vscodeAgentModeMinVersion ?? "")
                    : (this._featureFlagManager.currentFlags.vscodeAgentModeMinStableVersion ?? "")
            ),
            enableChatWithTools: isExtensionVersionGte(
                this._featureFlagManager.currentFlags.vscodeChatWithToolsMinVersion ?? ""
            ),
            agentEditTool: this._featureFlagManager.currentFlags.vscodeAgentEditTool ?? "",
            agentEditToolMinViewSize:
                this._featureFlagManager.currentFlags.agentEditToolMinViewSize ?? 0,
            agentEditToolSchemaType:
                this._featureFlagManager.currentFlags.agentEditToolSchemaType ??
                "StrReplaceEditorToolDefinitionNested",
            agentEditToolEnableFuzzyMatching:
                this._featureFlagManager.currentFlags.agentEditToolEnableFuzzyMatching ?? false,
            agentEditToolFuzzyMatchSuccessMessage:
                this._featureFlagManager.currentFlags.agentEditToolFuzzyMatchSuccessMessage ??
                "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
            agentEditToolFuzzyMatchMaxDiff:
                this._featureFlagManager.currentFlags.agentEditToolFuzzyMatchMaxDiff ?? 50,
            agentEditToolFuzzyMatchMaxDiffRatio:
                this._featureFlagManager.currentFlags.agentEditToolFuzzyMatchMaxDiffRatio ?? 0.15,
            agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs:
                this._featureFlagManager.currentFlags
                    .agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs ?? 5,
            agentEditToolInstructionsReminder:
                this._featureFlagManager.currentFlags.agentEditToolInstructionsReminder ?? false,
            agentEditToolShowResultSnippet:
                this._featureFlagManager.currentFlags.agentEditToolShowResultSnippet ?? true,
            agentEditToolMaxLines:
                this._featureFlagManager.currentFlags.agentEditToolMaxLines ?? 200,
            memoriesParams: this._featureFlagManager.currentFlags.memoriesParams ?? {},
            agentSaveFileToolInstructionsReminder:
                this._featureFlagManager.currentFlags.agentSaveFileToolInstructionsReminder ??
                false,
            enableTaskList: isExtensionVersionGte(
                this._featureFlagManager.currentFlags.vscodeTaskListMinVersion ?? ""
            ),
            grepSearchToolEnable:
                this._featureFlagManager.currentFlags.grepSearchToolEnable ?? false,
            grepSearchToolTimelimitSec:
                this._featureFlagManager.currentFlags.grepSearchToolTimelimitSec ?? 10,
            grepSearchToolOutputCharsLimit:
                this._featureFlagManager.currentFlags.grepSearchToolOutputCharsLimit ?? 5000,
            grepSearchToolNumContextLines:
                this._featureFlagManager.currentFlags.grepSearchToolNumContextLines ?? 5,
        };
    }
}

import * as path from "path";
import * as vscode from "vscode";

import { APIServer } from "./augment-api";
import { FeatureFlagManager, FeatureFlags } from "./feature-flags";
import { getLogger } from "./logging";
import { RemoteAgent } from "./remote-agent-manager/types";
import { DisposableService } from "./utils/disposable-service";
import { isExtensionVersionGte } from "./utils/environment";
import { readFileUtf8, writeFileUtf8 } from "./utils/fs-utils";

/**
 * Response type for the listRemoteAgents API call
 */
export interface ListRemoteAgentsResponse {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    remote_agents: RemoteAgent[];
}

/**
 * Service that detects if the current VSCode window is running in a remote agent environment
 * and configures the workspace UI accordingly
 */
export class RemoteWorkspaceResolver extends DisposableService {
    private static _instance: RemoteWorkspaceResolver | undefined;
    private _logger = getLogger("RemoteWorkspaceResolver");
    private _isRemoteAgent: boolean = false;
    private _agentIdFromWorkspace: string = "";
    private _remoteAgentId: string | undefined;
    private _remoteAgent: RemoteAgent | undefined;
    private _onDidChangeRemoteAgentStatus = new vscode.EventEmitter<boolean>();

    /**
     * Event that fires when the remote agent status changes
     */
    public readonly onDidChangeRemoteAgentStatus = this._onDidChangeRemoteAgentStatus.event;

    /**
     * Get the singleton instance of the RemoteWorkspaceResolver
     * @returns The singleton instance
     */
    public static getInstance(): RemoteWorkspaceResolver | undefined {
        return RemoteWorkspaceResolver._instance;
    }

    /**
     * Initialize the singleton instance of the RemoteWorkspaceResolver
     * @param apiServer The API server instance
     * @param featureFlagManager The feature flag manager instance
     * @returns The singleton instance
     */
    public static initialize(
        apiServer: APIServer,
        featureFlagManager: FeatureFlagManager
    ): RemoteWorkspaceResolver {
        if (!RemoteWorkspaceResolver._instance) {
            RemoteWorkspaceResolver._instance = new RemoteWorkspaceResolver(
                apiServer,
                featureFlagManager
            );
        }
        return RemoteWorkspaceResolver._instance;
    }

    private constructor(
        private readonly _apiServer: APIServer,
        private readonly _featureFlagManager: FeatureFlagManager
    ) {
        super();
        this.addDisposable(this._onDidChangeRemoteAgentStatus);

        // Add a listener for feature flag changes
        this.addDisposable(
            this._featureFlagManager.subscribe(["vscodeBackgroundAgentsMinVersion"], () => {
                void this.startDetection();
            })
        );

        // Start detection on initialization (will only run if feature flag is enabled)
        void this.startDetection();

        // Also queue detection for 30 seconds after VSCode starts
        const timeout = setTimeout(() => {
            void this.startDetection();
        }, 30000);
        this.addDisposable(new vscode.Disposable(() => clearTimeout(timeout)));
    }

    /**
     * Returns whether background agents are enabled based on the current config and feature flags
     * @param flags Optional feature flags to use instead of the current flags
     */
    private getIsBackgroundAgentsEnabled(flags?: FeatureFlags): boolean {
        if (!flags) {
            flags = this._featureFlagManager.currentFlags;
        }
        return isExtensionVersionGte(flags.vscodeBackgroundAgentsMinVersion ?? "");
    }

    /**
     * Start the remote agent detection process
     */
    public async startDetection(): Promise<void> {
        // Only run detection if background agents are enabled
        const isBackgroundAgentsEnabled = this.getIsBackgroundAgentsEnabled();

        if (!isBackgroundAgentsEnabled) {
            return;
        }

        try {
            // Get the current hostname
            this.getAgentIdFromWorkspaceName();

            // Check if we're in a remote agent
            await this.checkIfRemoteAgent();
        } catch (error) {
            this._logger.error("Error starting remote agent detection", error);
        }
    }

    /**
     * Get the current hostname using Node.js exec
     */
    private getAgentIdFromWorkspaceName(): string {
        try {
            const workspaceName = vscode.workspace.name || "";
            // looks like "workspace [SSH: d60b1c77-52a1-543b-bb35-e2647126f445]"
            const regex = /\[SSH: (.*)\]/;
            this._agentIdFromWorkspace = workspaceName.match(regex)?.[1] || "";
            return this._agentIdFromWorkspace;
        } catch (error) {
            this._logger.error("Error getting hostname", error);
            return "";
        }
    }

    /**
     * Check if the current VSCode window is running in a remote agent
     */
    private async checkIfRemoteAgent(): Promise<boolean> {
        try {
            // Skip if hostname is empty
            if (!this._agentIdFromWorkspace) {
                this.getAgentIdFromWorkspaceName();
                if (!this._agentIdFromWorkspace) {
                    return false;
                }
            }

            // Get the list of remote agents
            let remoteAgents;
            try {
                remoteAgents = await this._apiServer.listRemoteAgents();
                if (!remoteAgents || !remoteAgents.remote_agents) {
                    this._logger.error("Invalid response from listRemoteAgents");
                    return false;
                }
            } catch (error) {
                this._logger.error("Error fetching remote agents", error);
                return false;
            }

            // Check if any of the remote agents match our hostname
            const matchingAgent = remoteAgents.remote_agents.find((agent) => {
                // The hostname might be the full agent hostname or just the ID
                return this._agentIdFromWorkspace === agent.remote_agent_id;
            });

            const wasRemoteAgent = this._isRemoteAgent;
            this._isRemoteAgent = !!matchingAgent;
            this._remoteAgent = matchingAgent;
            this._remoteAgentId = matchingAgent?.remote_agent_id;

            // If the status changed, emit an event
            if (wasRemoteAgent !== this._isRemoteAgent) {
                this._onDidChangeRemoteAgentStatus.fire(this._isRemoteAgent);

                // Update VSCode settings based on remote agent status
                await this.updateVSCodeSettings();
            }

            return this._isRemoteAgent;
        } catch (error) {
            this._logger.error("Error checking if remote agent", error);
            return false;
        }
    }

    /**
     * Returns whether the current VSCode window is running in a remote agent
     */
    public isRemoteAgent(): boolean {
        return this._isRemoteAgent;
    }

    /**
     * Returns the remote agent ID if running in a remote agent, undefined otherwise
     */
    public getRemoteAgentId(): string | undefined {
        return this._remoteAgentId;
    }

    /**
     * Returns the remote agent object if running in a remote agent, undefined otherwise
     */
    public getRemoteAgent(): RemoteAgent | undefined {
        return this._remoteAgent;
    }

    /**
     * Static method to check if the current VSCode window is running in a remote agent
     * @returns True if the current window is running in a remote agent, false otherwise
     */
    public static isRemoteAgent(): boolean {
        return RemoteWorkspaceResolver._instance?.isRemoteAgent() ?? false;
    }

    /**
     * Static method to get the remote agent ID if running in a remote agent
     * @returns The remote agent ID if running in a remote agent, undefined otherwise
     */
    public static getRemoteAgentId(): string | undefined {
        return RemoteWorkspaceResolver._instance?.getRemoteAgentId();
    }

    /**
     * Helper function to execute a promise with a timeout
     * @param thenable The promise to execute
     * @param timeoutMs The timeout in milliseconds
     * @param errorMessage The error message to log if the promise times out
     * @returns The result of the promise, or undefined if it timed out
     */
    private async executeWithTimeout<T>(
        thenable: Thenable<T>,
        timeoutMs: number,
        errorMessage: string
    ): Promise<T | undefined> {
        let timeoutHandle: NodeJS.Timeout;
        try {
            // Convert Thenable to Promise
            const promise = Promise.resolve(thenable);

            const result = await Promise.race([
                promise,
                new Promise<never>((_, reject) => {
                    timeoutHandle = setTimeout(() => {
                        reject(new Error(`Timeout after ${timeoutMs}ms: ${errorMessage}`));
                    }, timeoutMs);
                }),
            ]);
            clearTimeout(timeoutHandle!);
            return result as T;
        } catch (error) {
            this._logger.error(errorMessage, error);
            return undefined;
        }
    }

    /**
     * Gets the remote agent settings to apply
     * This centralizes all settings in one place for easier maintenance
     */
    private getRemoteAgentSettings(): Record<string, any> {
        /* eslint-disable @typescript-eslint/naming-convention */
        return {
            "workbench.colorCustomizations": {
                "titleBar.activeBackground": "#0090FF",
                "titleBar.activeForeground": "#ffffff",
                "titleBar.inactiveBackground": "#0090FF",
                "titleBar.inactiveForeground": "#ffffff",
            },
            "window.title": `☁️ Remote Agent: ${this._remoteAgent?.session_summary || this._remoteAgentId} \${dirty}\${activeEditorShort}\${separator}\${rootName}`,
        };
    }

    /**
     * Update settings using the best available method
     * First tries direct file editing, then falls back to command-based approach
     */
    private async updateSettings(): Promise<void> {
        try {
            await this.editRemoteSettingsFile();
        } catch (error) {
            this._logger.error(
                "Error with direct file editing, trying command-based approach",
                error
            );
            try {
                await this.updateSettingsUsingCommand();
            } catch (cmdError) {
                this._logger.error("All settings update methods failed", cmdError);
            }
        }
    }

    /**
     * Directly edits the remote settings file to add our settings
     */
    private async editRemoteSettingsFile(): Promise<void> {
        // First, get the remote settings file path
        // We need to find where VSCode stores the remote settings file
        const remoteSettingsPath = this.getRemoteSettingsFilePath();
        if (!remoteSettingsPath) {
            this._logger.error("Could not determine remote settings file path");
            throw new Error("Could not determine remote settings file path");
        }

        // Get the settings to add
        const settingsToAdd = this.getRemoteAgentSettings();

        // Read the current settings file
        const uri = vscode.Uri.file(remoteSettingsPath);
        let currentSettings: Record<string, any> = {};

        try {
            // Try to read the existing file
            const fileContent = await vscode.workspace.fs.readFile(uri);
            const fileContentStr = Buffer.from(fileContent).toString("utf8");

            // Parse the existing settings
            if (fileContentStr.trim()) {
                try {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    const parsedSettings: unknown = JSON.parse(fileContentStr);
                    if (
                        parsedSettings &&
                        typeof parsedSettings === "object" &&
                        !Array.isArray(parsedSettings)
                    ) {
                        currentSettings = parsedSettings as Record<string, any>;
                    }
                } catch (parseError) {
                    this._logger.error("Error parsing remote settings file", parseError);
                    // Continue with empty settings
                }
            }
        } catch (readError) {
            // File might not exist yet, which is fine
        }

        // Merge our settings with the existing settings
        // TODO: probably want to do a deep merge.
        // Leaving for now because a user is unlikely to have conflicting settings in the workspace we created
        const mergedSettings = {
            ...currentSettings,
            ...settingsToAdd,
        };

        // Format the settings as JSON
        const formattedSettings = JSON.stringify(mergedSettings, null, 2);

        // Write the updated settings back to the file
        await vscode.workspace.fs.writeFile(uri, Buffer.from(formattedSettings, "utf8"));
    }

    /**
     * Gets the path to the remote settings file
     * This is a best-effort attempt to find the settings.json file
     */
    private getRemoteSettingsFilePath(): string | undefined {
        try {
            // In a remote environment, settings are typically stored in ~/.vscode-server/data/Machine/settings.json
            const homeDir = process.env.HOME || process.env.USERPROFILE;
            if (!homeDir) {
                return undefined;
            }

            // Check for the standard remote settings location
            const remoteSettingsPath = path.join(
                homeDir,
                ".vscode-server",
                "data",
                "Machine",
                "settings.json"
            );

            return remoteSettingsPath;
        } catch (error) {
            if (error instanceof Error) {
                this._logger.error("Error determining remote settings file path", error);
            } else {
                this._logger.error(`Error determining remote settings file path: ${String(error)}`);
            }
            return undefined;
        }
    }

    /**
     * Opens the remote settings file
     * using the workbench.action.openRemoteSettingsFile command
     * and updates the settings
     */
    private async updateSettingsUsingCommand(): Promise<void> {
        await vscode.commands.executeCommand("workbench.action.openRemoteSettingsFile");

        const file = vscode.window.activeTextEditor?.document.fileName;
        if (!file) {
            this._logger.error("Failed to open remote settings file");
            throw new Error("Failed to open remote settings file");
        }

        // read the file
        let content: string;
        try {
            content = await readFileUtf8(file);
        } catch (readError) {
            this._logger.error("Failed to read remote settings file", readError);
            throw new Error("Failed to read remote settings file");
        }

        let settings: Record<string, any>;
        try {
            settings = JSON.parse(content) as Record<string, any>;
        } catch (parseError) {
            this._logger.error("Failed to parse remote settings file", parseError);
            throw new Error("Failed to parse remote settings file");
        }

        // Merge our settings with the existing settings
        // TODO: probably want to do a deep merge
        const mergedSettings = {
            ...settings,
            ...this.getRemoteAgentSettings(),
        };

        // Format the settings as JSON
        const formattedSettings = JSON.stringify(mergedSettings, null, 2);

        try {
            await writeFileUtf8(file, formattedSettings);
        } catch (writeError) {
            this._logger.error("Failed to write remote settings file", writeError);
            throw new Error("Failed to write remote settings file");
        }

        // close file
        await vscode.commands.executeCommand("workbench.action.closeActiveEditor");
    }

    /**
     * Update VSCode settings based on remote agent status
     * - For remote environments only: Changes title bar color and updates window title
     * - Sets context values to control UI elements
     *
     * Only modifies remote settings, never modifies local workspace settings
     */
    private async updateVSCodeSettings(): Promise<void> {
        if (!this._isRemoteAgent || !this._remoteAgentId) {
            return;
        }
        try {
            // Check if we're in a remote environment
            const isRemoteEnvironment = vscode.env.remoteName !== undefined;
            if (isRemoteEnvironment) {
                await this.updateSettings();
            }

            // Open and focus the Augment panel after all configuration is set
            // This ensures the webview will receive the correct configuration values
            await this.executeWithTimeout(
                vscode.commands.executeCommand("vscode-augment.focusAugmentPanel"),
                5000,
                "Timeout opening and focusing Augment panel"
            );
        } catch (error) {
            this._logger.error("Error updating VSCode settings", error);
        }
    }
}

export type ClientMetric = {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    client_metric: string;
    value: number;
};

export enum WebviewName {
    chat = "chat",
}

export enum ChatMetricName {
    // @mentions
    chatMentionFolder = "chat-mention-folder",
    chatMentionFile = "chat-mention-file",
    chatMentionExternalSource = "chat-mention-external-source",
    chatClearContext = "chat-clear-context",
    chatRestoreDefaultContext = "chat-restore-default-context",
    // /actions
    chatUseActionFind = "chat-use-action-find",
    chatUseActionExplain = "chat-use-action-explain",
    chatUseActionFix = "chat-use-action-autofix",
    chatUseActionWriteTest = "chat-use-action-write-test",
    // Conversation menu
    chatNewConversation = "chat-new-conversation",
    chatNewAutofixConversation = "chat-new-autofix-conversation",
    chatEditConversationName = "chat-edit-conversation-name",
    // Markdown (smart paste)
    chatFailedSmartPasteResolveFile = "chat-failed-smart-paste-resolve-file",
    chatPrecomputeSmartPaste = "chat-precompute-smart-paste",
    chatSmartPaste = "chat-smart-paste",
    // Markdown (codeblocks)
    chatCodeblockCopy = "chat-codeblock-copy",
    chatCodeblockCreate = "chat-codeblock-create",
    chatCodeblockGoToFile = "chat-codeblock-go-to-file",
    // Markdown (codespans)
    chatCodespanGoToFile = "chat-codespan-go-to-file",
    chatCodespanGoToSymbol = "chat-codespan-go-to-symbol",
    // Markdown (mermaid blocks)
    chatMermaidblockInitialize = "chat-mermaidblock-initialize",
    chatMermaidblockToggle = "chat-mermaidblock-toggle",
    chatMermaidblockInteract = "chat-mermaidblock-interact",
    chatMermaidBlockError = "chat-mermaidblock-error",
    // Suggested questions
    chatUseSuggestedQuestion = "chat-use-suggested-question",
    chatDisplaySuggestedQuestions = "chat-display-suggested-questions",
    // Guidelines
    setWorkspaceGuidelines = "chat-set-workspace-guidelines",
    clearWorkspaceGuidelines = "chat-clear-workspace-guidelines",
    setUserGuidelines = "chat-set-user-guidelines",
    clearUserGuidelines = "chat-clear-user-guidelines",
}

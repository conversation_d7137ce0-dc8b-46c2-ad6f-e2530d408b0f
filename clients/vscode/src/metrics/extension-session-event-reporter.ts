import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import * as vscode from "vscode";

import {
    APIServer,
    ExtensionEventAdditionalData,
    ExtensionSessionEvent,
    ExtensionSessionEventName,
} from "../augment-api";
import { AugmentConfig } from "../augment-config-listener";
import { FeatureFlags } from "../feature-flags";
import { sha256 } from "../sha";
import { flattenObject } from "../utils/object-utils";

export type SourceFolderReport = {
    workspaceStorageUri?: string; // to be hashed
    folderCountByType: Record<string, number>;
    repoRootCountByType: Record<string, number>;
    trackedFileCount: number[];
    nestedFolderCount: number;
    untrackedCountByReason: Record<string, number>;
};
export class ExtensionSessionEventReporter extends MetricsReporter<ExtensionSessionEvent> {
    public static defaultMaxRecords = 10000;
    public static defaultBatchSize = 1000;
    public static defaultUploadMsec = 10000;

    constructor(
        private _apiServer: APIServer,
        maxRecords?: number,
        uploadMs?: number,
        batchSize?: number
    ) {
        super(
            "ExtensionSessionEventReporter",
            maxRecords ?? ExtensionSessionEventReporter.defaultMaxRecords,
            uploadMs ?? ExtensionSessionEventReporter.defaultUploadMsec,
            batchSize ?? ExtensionSessionEventReporter.defaultBatchSize
        );
    }

    // reportEvent reports a user next edit event.
    public reportEvent(
        eventName: ExtensionSessionEventName,
        additionalData?: ExtensionEventAdditionalData[]
    ): void {
        /* eslint-disable @typescript-eslint/naming-convention */
        this.report({
            time_iso: new Date().toISOString(),
            event_name: eventName,
            additional_data: additionalData,
        });
        /* eslint-enable @typescript-eslint/naming-convention */
    }

    protected performUpload(batch: ExtensionSessionEvent[]): Promise<void> {
        return this._apiServer.logExtensionSessionEvent(batch);
    }

    public reportSourceFolders(sourceFoldersEventDetails: SourceFolderReport) {
        if (!sourceFoldersEventDetails.workspaceStorageUri) {
            return;
        }
        const projectId = sha256(
            new TextEncoder().encode(sourceFoldersEventDetails.workspaceStorageUri)
        );

        delete sourceFoldersEventDetails.workspaceStorageUri;
        const flatSourceFoldersEventDetails: Record<string, string> = flattenObject({
            projectId,
            ...sourceFoldersEventDetails,
        });

        this.report({
            /* eslint-disable @typescript-eslint/naming-convention */
            time_iso: new Date().toISOString(),
            event_name: ExtensionSessionEventName.SourceFolderSnapshot,
            additional_data: Object.entries(flatSourceFoldersEventDetails).map(([key, value]) => ({
                key,
                value,
            })),
            /* eslint-enable @typescript-eslint/naming-convention */
        });
    }

    public reportConfiguration(
        eventName: ExtensionSessionEventName,
        config: AugmentConfig,
        featureFlags: FeatureFlags
    ) {
        const flatConfig: Record<string, string> = flattenObject({
            otherConfig: {
                theme: vscode.workspace.getConfiguration().get("workbench.colorTheme"),
                fontSize: vscode.workspace.getConfiguration().get("editor.fontSize"),
                isDark: [vscode.ColorThemeKind.Dark, vscode.ColorThemeKind.HighContrast].includes(
                    vscode.window.activeColorTheme.kind
                ),
            },
            config,
            featureFlags: featureFlags,
        });
        for (const key in flatConfig) {
            // removes potential sensitive information.
            if (key.toLowerCase().includes("token")) {
                // check if the value is not empty
                if (flatConfig[key]) {
                    flatConfig[key] = "<redacted>";
                }
            }
        }

        this.report({
            /* eslint-disable @typescript-eslint/naming-convention */
            time_iso: new Date().toISOString(),
            event_name: ExtensionSessionEventName.ConfigurationSnapshot,
            additional_data: Object.entries(flatConfig).map(([key, value]) => ({ key, value })),
            /* eslint-enable @typescript-eslint/naming-convention */
        });
    }
}

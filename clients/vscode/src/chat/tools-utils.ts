import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";
import * as vscode from "vscode";

import { readDirectory } from "../utils/fs-utils";
import { isAbsolutePathName, relativePathName } from "../utils/path-utils";
import { viewTextDocument } from "../workspace/view-text-document";
import { WorkspaceManager } from "../workspace/workspace-manager";

export function getCwdForTool(workspaceManager: WorkspaceManager) {
    let cwd: string | undefined;
    if (vscode.window.activeTextEditor) {
        const path = vscode.window.activeTextEditor.document.uri.fsPath;
        cwd = workspaceManager.getFolderRoot(path);
    }
    if (cwd === undefined) {
        cwd = workspaceManager.getMostRecentlyChangedFolderRoot();
        if (cwd === undefined) {
            cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        }
    }
    return cwd;
}

/** Helper function to get the absolute path of a file. */
export function _getQualifiedPath(
    path: string,
    workspaceManager: WorkspaceManager
): QualifiedPathName | undefined {
    // If the path is absolute, use it to resolve the QualifiedPathName
    if (isAbsolutePathName(path)) {
        const folderRoot = workspaceManager.getFolderRoot(path);
        if (folderRoot === undefined) {
            return undefined;
        }

        return new QualifiedPathName(folderRoot, relativePathName(folderRoot, path));
    }

    // If the path is relative, find the best workspace root match
    const bestWorkspaceRoot = workspaceManager.findBestWorkspaceRootMatch(path);
    if (bestWorkspaceRoot) {
        const folderRoot = bestWorkspaceRoot.qualifiedPathName.rootPath;
        return new QualifiedPathName(folderRoot, path);
    }

    const cwd = getCwdForTool(workspaceManager);
    if (cwd) {
        return new QualifiedPathName(cwd, path);
    }

    return undefined;
}

/** Helper function to read a file. */
export async function _readFile(
    filePath: string,
    workspaceManager: WorkspaceManager
): Promise<string | undefined> {
    const absPath = _getQualifiedPath(filePath, workspaceManager)?.absPath;
    if (absPath === undefined) {
        return undefined;
    }
    try {
        const doc = await viewTextDocument(absPath);
        return doc.getText();
    } catch (e) {
        return undefined;
    }
}

/** Helper function to list files in a directory. */
export async function _listFiles(
    folderPath: string,
    workspaceManager: WorkspaceManager
): Promise<string[] | undefined> {
    const absPath = _getQualifiedPath(folderPath, workspaceManager)?.absPath;
    if (absPath === undefined) {
        return undefined;
    }
    try {
        const files = await readDirectory(absPath);
        return files.map(([name, _]) => name);
    } catch (e) {
        return undefined;
    }
}

/**
 * Format diagnostics with context lines
 * @param diagnosticsMap Map of file paths to diagnostics
 * @returns Formatted string with diagnostics and context
 */
export async function formatDiagnostics(
    diagnosticsMap: Map<string, vscode.Diagnostic[]>,
    workspaceManager: WorkspaceManager
): Promise<string> {
    const result: string[] = [];
    for (const [diagPath, diagnostics] of diagnosticsMap.entries()) {
        if (diagnostics.length === 0) {
            continue;
        }
        result.push(diagPath);
        try {
            // Get the document content
            const fileLines = (await _readFile(diagPath, workspaceManager))?.split("\n") ?? [];

            for (const diagnostic of diagnostics) {
                // Get line numbers (convert from 0-based to 1-based for display)
                const startLine = diagnostic.range.start.line + 1;
                const endLine = diagnostic.range.end.line + 1;

                // Add the diagnostic message
                result.push(`L${startLine}-${endLine}: ${diagnostic.message}`);

                // Add context lines (3 lines before and 3 lines after)
                const contextStartLine = Math.max(0, diagnostic.range.start.line - 3);
                const contextEndLine = Math.min(
                    fileLines.length - 1,
                    diagnostic.range.end.line + 3
                );

                // Add context lines with line numbers
                for (let i = contextStartLine; i <= contextEndLine; i++) {
                    const lineText = fileLines[i];
                    const lineNum = i + 1; // Convert to 1-based
                    result.push(`${String(lineNum).padStart(6)}\t${lineText}`);
                }

                // Add a separator between diagnostics
                result.push("");
            }
        } catch (error) {
            // If we can't get the document, just show the diagnostic without context
            for (const diagnostic of diagnostics) {
                const startLine = diagnostic.range.start.line + 1;
                const endLine = diagnostic.range.end.line + 1;
                result.push(`L${startLine}-${endLine}: ${diagnostic.message}`);
            }
        }
    }
    return result.join("\n");
}

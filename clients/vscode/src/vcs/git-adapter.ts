import { ExecOptions } from "child_process";
import * as vscode from "vscode";

import { executeCommand } from "./command-utils";

type DiffArgs = {
    hash1?: string;
    hash2?: string;
    nameStatus?: boolean;
    relPath?: string;
};

type LSFilesArgs = {
    others?: boolean;
    excludeStandard?: boolean;
    relPath?: string;
};

type SymbolicRefArgs = {
    name?: string;
};

type LogArgs = {
    commit1?: string;
    commit2?: string;
    noMerges?: boolean;
    format?: string;
    not?: string;
};

type ShowArgs = {
    object?: string;
    nameStatus?: boolean;
    oneLine?: boolean;
    patch?: boolean;
};

type CommandExecutor = (command: string, options?: ExecOptions) => Promise<string | undefined>;

interface GitAdapter {
    version(): Promise<string | undefined>;
    diff(diffArgs: DiffArgs): Promise<string | undefined>;
    lsFiles(lsFilesArgs: LSFilesArgs): Promise<string | undefined>;
    show(showArgs: ShowArgs): Promise<string | undefined>;
    symbolicRef(symbolicRefArgs: SymbolicRefArgs): Promise<string | undefined>;
    log(logArgs: LogArgs): Promise<string | undefined>;
}

class GitAdapterImpl implements GitAdapter {
    constructor(
        private root: vscode.Uri,
        private commandExecutor: CommandExecutor = executeCommand
    ) {}

    async version(): Promise<string | undefined> {
        const version = await this.commandExecutor("git --version");
        return version;
    }

    async diff(diffArgs: DiffArgs): Promise<string | undefined> {
        const commandParts = ["git", "diff"];
        if (diffArgs.nameStatus) {
            commandParts.push("--name-status");
        }
        if (diffArgs.hash1) {
            commandParts.push(diffArgs.hash1);
        }
        if (diffArgs.hash2) {
            commandParts.push(diffArgs.hash2);
        }
        if (diffArgs.relPath) {
            commandParts.push("--");
            commandParts.push(diffArgs.relPath);
        }
        const command = commandParts.join(" ");
        const result = await this.commandExecutor(command, { cwd: this.root.fsPath });
        return result;
    }

    async lsFiles(lsFilesArgs: LSFilesArgs): Promise<string | undefined> {
        const commandParts = ["git", "ls-files"];
        if (lsFilesArgs.others) {
            commandParts.push("--others");
        }
        if (lsFilesArgs.excludeStandard) {
            commandParts.push("--exclude-standard");
        }
        if (lsFilesArgs.relPath) {
            commandParts.push("--");
            commandParts.push(lsFilesArgs.relPath);
        }
        const command = commandParts.join(" ");
        const result = await this.commandExecutor(command, { cwd: this.root.fsPath });
        return result;
    }

    async show(showArgs: ShowArgs): Promise<string | undefined> {
        const commandParts = ["git", "show"];

        if (showArgs.nameStatus) {
            commandParts.push("--name-status");
        }
        if (showArgs.oneLine) {
            commandParts.push("--oneline");
        }
        if (showArgs.patch) {
            commandParts.push("-p");
        }
        if (showArgs.object) {
            commandParts.push(`"${showArgs.object}"`); // support for spaces in file names
        }
        const command = commandParts.join(" ");
        const result = await this.commandExecutor(command, { cwd: this.root.fsPath });
        return result;
    }

    async symbolicRef(symbolicRefArgs: SymbolicRefArgs): Promise<string | undefined> {
        const commandParts = ["git", "symbolic-ref"];
        if (symbolicRefArgs.name) {
            commandParts.push(symbolicRefArgs.name);
        }
        const command = commandParts.join(" ");
        const result = await this.commandExecutor(command, { cwd: this.root.fsPath });
        return result;
    }

    async log(logArgs: LogArgs): Promise<string | undefined> {
        const commandParts = ["git", "log"];
        if (logArgs.commit1) {
            commandParts.push(logArgs.commit1);
        }
        if (logArgs.commit2) {
            commandParts.push(logArgs.commit2);
        }
        if (logArgs.noMerges) {
            commandParts.push("--no-merges");
        }
        if (logArgs.format) {
            commandParts.push(`--format="${logArgs.format}"`);
        }
        if (logArgs.not) {
            commandParts.push(`--not "${logArgs.not}"`);
        }
        const command = commandParts.join(" ");
        const result = await this.commandExecutor(command, { cwd: this.root.fsPath });
        return result;
    }
}

export {
    type GitAdapter,
    GitAdapterImpl,
    type DiffArgs,
    type LSFilesArgs,
    type LogArgs,
    type ShowArgs,
    type SymbolicRefArgs,
    type CommandExecutor,
};

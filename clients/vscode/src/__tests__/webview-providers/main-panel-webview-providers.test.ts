import { <PERSON><PERSON>, WebviewView as vscodeWebviewView } from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { CancellationToken, Webview, WebviewView } from "../../__mocks__/vscode-mocks";
import { MainPanelAppController } from "../../main-panel/main-panel-app-controller";
import { MainPanelWebview } from "../../main-panel/main-panel-webview";
import { MainPanelWebviewProvider } from "../../webview-providers/main-panel-webview-provider";
import { MainPanelApp } from "../../webview-providers/webview-messages";

describe("MainPanelWebviewProvider", () => {
    test("should register and dispose webview", async () => {
        mockFSUtils.writeFileUtf8(
            "/path/common-webviews/main-panel.html",
            "<html><head></head>example of webviews/dist/main-panel.html</html>",
            true
        );

        const provider = new TestMainPanelWebviewProvider();
        const webviewView = new WebviewView("main-panel", new Webview({}, "", ""), true);
        await provider.resolveWebviewView(
            webviewView,
            {
                state: undefined,
            },
            new CancellationToken()
        );
        expect(provider.webviewView).toBe(webviewView);
        expect(provider.webviewView?.title).toBe("");
        expect(provider.mainPanelWebview).not.toBeUndefined();

        provider.changeApp(new TestApp());
        expect(provider.webviewView?.title).toBe("Test App");

        provider.changeApp(undefined);
        expect(provider.webviewView?.title).toBe("");

        webviewView.dispose();
        expect(provider.webviewView).toBeUndefined();
    });

    test("should emit visibility events", async () => {
        mockFSUtils.writeFileUtf8(
            "/path/common-webviews/main-panel.html",
            "<html><head></head>example of webviews/dist/main-panel.html</html>",
            true
        );

        const provider = new TestMainPanelWebviewProvider();
        const webviewView = new WebviewView("main-panel", new Webview({}, "", ""), true);
        await provider.resolveWebviewView(
            webviewView,
            {
                state: undefined,
            },
            new CancellationToken()
        );
        const visibilitySpy = jest.fn();
        provider.onVisibilityChange(visibilitySpy);

        expect(visibilitySpy).not.toHaveBeenCalled();
        webviewView.triggerVisibilityChange();

        expect(visibilitySpy).toHaveBeenCalledTimes(1);
    });
});

class TestMainPanelWebviewProvider extends MainPanelWebviewProvider {
    constructor() {
        super(Uri.parse("vscode://example/path"));
    }

    get mainPanelWebview(): MainPanelWebview | undefined {
        return this._mainPanelWebview;
    }

    get webviewView(): vscodeWebviewView | undefined {
        return this._webviewView;
    }
}

class TestApp implements MainPanelAppController {
    dispose(): void {}

    appType(): MainPanelApp {
        return MainPanelApp.chat;
    }

    title(): string {
        return "Test App";
    }

    register(_webview: Webview): void {}
}

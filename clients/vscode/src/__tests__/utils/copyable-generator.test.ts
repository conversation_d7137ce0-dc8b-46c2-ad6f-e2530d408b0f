import { CopyableGenerator } from "../../utils/copyable-generator";

async function* createGenerator<T>(items: T[], delay: number = 0): AsyncGenerator<T> {
    for (const item of items) {
        if (delay > 0) {
            await new Promise((resolve) => setTimeout(resolve, delay));
        }
        yield item;
    }
}

describe("CopyableGenerator", () => {
    test("basic iteration", async () => {
        const BASE_ITEMS = [1, 2, 3, 4, 5];
        const BASE = createGenerator(BASE_ITEMS);
        const generator = new CopyableGenerator(BASE);

        const result: number[] = [];
        for await (const value of generator.copy()) {
            result.push(value);
        }

        expect(result).toEqual(BASE_ITEMS);
    });

    test("multiple iterations", async () => {
        const BASE_ITEMS = [1, 2, 3, 4, 5];
        const BASE = createGenerator(BASE_ITEMS);
        const generator = new CopyableGenerator(BASE);

        const result1: number[] = [];
        for await (const value of generator.copy()) {
            result1.push(value);
        }

        const result2: number[] = [];
        for await (const value of generator.copy()) {
            result2.push(value);
        }

        expect(result1).toEqual(BASE_ITEMS);
        expect(result2).toEqual(BASE_ITEMS);
    });

    test("iteration while buffering", async () => {
        const BASE_ITEMS = [1, 2, 3, 4, 5];
        const BASE = createGenerator(BASE_ITEMS, 100);
        const generator = new CopyableGenerator(BASE);

        const result: number[] = [];
        for await (const value of generator.copy()) {
            result.push(value);
        }

        expect(result).toEqual(BASE_ITEMS);
    });

    test("multiple iterations while buffering", async () => {
        const BASE_ITEMS = [1, 2, 3, 4, 5];
        const BASE = createGenerator(BASE_ITEMS, 100);
        const generator = new CopyableGenerator(BASE);

        const result1: number[] = [];
        for await (const value of generator.copy()) {
            result1.push(value);
        }

        const result2: number[] = [];
        for await (const value of generator.copy()) {
            result2.push(value);
        }

        expect(result1).toEqual(BASE_ITEMS);
        expect(result2).toEqual(BASE_ITEMS);
    });

    describe("getters", () => {
        test("isFinished", async () => {
            const BASE_ITEMS = [1, 2, 3];
            const BASE = createGenerator(BASE_ITEMS);
            const generator = new CopyableGenerator(BASE);

            expect(generator.isFinished).toBe(false);

            const copy = generator.copy();
            await copy.next();
            expect(generator.isFinished).toBe(false);
            await copy.next();
            expect(generator.isFinished).toBe(false);
            await copy.next();
            expect(generator.isFinished).toBe(true);
        });

        test("hasErrored", async () => {
            const BASE_ITEMS = [1, 2, 3];
            const BASE = createGenerator(BASE_ITEMS);
            const generator = new CopyableGenerator(BASE);

            expect(generator.hasErrored).toBe(false);

            const copy = generator.copy();
            try {
                generator.cancel();
                await copy.next();
            } catch (e) {
                expect(generator.hasErrored).toBe(true);
            }
        });
    });
});

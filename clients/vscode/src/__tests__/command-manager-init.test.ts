// These tests are intended to check that the commands we define follow any
// best practices that we want.
import { readFileSync } from "fs";
import { join } from "path";
import * as vscode from "vscode";

import { MockAPIServer } from "../__mocks__/mock-api-server";
import { ExtensionContext, Memento } from "../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";
import { OAuthFlow } from "../auth/oauth-flow";
import { ChatRequest } from "../chat/chat-model";
import { AssetManager } from "../client-interfaces/asset-manager";
import { AugmentInstruction } from "../code-edit-types";
import { AugmentCommand, CommandManager, CommandType } from "../command-manager";
import { initCommandManager } from "../command-manager-init";
import { RecentCompletions } from "../completions/recent-completions";
import { AugmentExtension } from "../extension";
import { ActionsModel } from "../main-panel/action-cards/actions-model";
import { ChatExtensionMessage } from "../main-panel/apps/chat-webview-app";
import { OnboardingSessionEventReporter } from "../metrics/onboarding-session-event-reporter";
import { NextEditResultInfo } from "../next-edit/next-edit-types";
import { AugmentGlobalState } from "../utils/context";
import { DisposableService } from "../utils/disposable-service";
import { RecentItems } from "../utils/recent-items";
import { MainPanelWebviewProvider } from "../webview-providers/main-panel-webview-provider";
import { MainPanelApp, NextEditWebViewMessage } from "../webview-providers/webview-messages";
import { SyncingEnabledTracker } from "../workspace/syncing-enabled-tracker";

const pkgBuffer = readFileSync(join(__dirname, "..", "..", "package.json"));
const pkg = JSON.parse(pkgBuffer.toString());
class CommandManagerTestKit extends DisposableService {
    constructor(
        public readonly commandManager: CommandManager,
        extension: AugmentExtension
    ) {
        super();
        this.addDisposable(extension);
    }

    public static create() {
        const context = new ExtensionContext();
        const config = new AugmentConfigListener();
        const apiServer = new MockAPIServer();
        const auth = new AuthSessionStore(context, config);
        const oauthFlow = new OAuthFlow(
            context,
            config,
            apiServer,
            auth,
            new OnboardingSessionEventReporter(apiServer)
        );
        const recentCompletions = new RecentCompletions();
        const recentInstructions = new RecentItems<AugmentInstruction>(10);
        const recentNextEditLocations = new RecentItems<NextEditResultInfo>(10);
        const recentChats = new RecentItems<ChatRequest>(10);
        const chatExtensionEvent = new vscode.EventEmitter<ChatExtensionMessage>();
        const nextEditWebViewEvent = new vscode.EventEmitter<NextEditWebViewMessage>();
        const changeWebviewAppEvent = new vscode.EventEmitter<MainPanelApp>();
        const globalState = new AugmentGlobalState(context);
        const syncingEnabledTracker = new SyncingEnabledTracker();
        const mockAssetManager = new AssetManager(context);
        const workspaceStorage = new Memento();

        const extension = new AugmentExtension(
            context,
            globalState,
            config,
            apiServer,
            auth,
            recentCompletions,
            recentInstructions,
            recentNextEditLocations,
            recentChats,
            nextEditWebViewEvent,
            new vscode.EventEmitter<void>(),
            new MainPanelWebviewProvider(context.extensionUri),
            changeWebviewAppEvent,
            new ActionsModel(globalState),
            syncingEnabledTracker,
            chatExtensionEvent,
            new OnboardingSessionEventReporter(apiServer),
            mockAssetManager
        );

        const commandManager = initCommandManager(
            context,
            extension,
            config,
            auth,
            oauthFlow,
            apiServer,
            recentCompletions,
            recentInstructions,
            recentNextEditLocations,
            changeWebviewAppEvent,
            chatExtensionEvent,
            syncingEnabledTracker,
            globalState,
            workspaceStorage
        );
        return new CommandManagerTestKit(commandManager, extension);
    }
}

function getCommands(): Array<AugmentCommand> {
    const kit = CommandManagerTestKit.create();
    try {
        return kit.commandManager.allCommands;
    } finally {
        kit.dispose();
    }
}

jest.useFakeTimers();

// Ensure all commands have a title in package.json
describe("command-manager-init > titles", () => {
    const pkgCommands: { [key: string]: any } = {};
    pkg.contributes.commands.forEach((command: any) => {
        pkgCommands[command.command] = command;
    });

    const commands = getCommands();
    for (const command of commands) {
        if (command.type === CommandType.private) {
            continue;
        }

        test(`check ${command.commandID} for package.json title`, async () => {
            const pkgCmd = pkgCommands[command.commandID];
            switch (command.type) {
                case CommandType.public:
                    // Public commands should be in package.json
                    expect(pkgCmd).toBeDefined();
                    expect(pkgCmd.title).toBeDefined();
                    expect(pkgCmd.title.trim().length).toBeGreaterThan(0);
                    break;
                case CommandType.debug:
                case CommandType.private:
                    // Private commands should not be in package.json as that
                    // is publicly viewable via keybindings and the extension
                    // marketplace page.
                    expect(pkgCmd).toBeUndefined();
                    expect(command.title).toBeDefined();
                    expect(command.title!.trim().length).toBeGreaterThan(0);
                    break;
                default:
                    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                    throw new Error(`Unknown command type ${command.type}`);
            }
        });
    }
});

// Ensure all commands in command palette have appropriate when clause
// and/or should be in the command palette
describe("command-manager-init > command palette", () => {
    const commandPaletteCommands: { [key: string]: any } = {};
    pkg.contributes.menus.commandPalette.forEach((command: any) => {
        commandPaletteCommands[command.command] = command;
    });
    const contributedCommands: { [key: string]: any } = {};
    pkg.contributes.commands.forEach((command: any) => {
        contributedCommands[command.command] = command;
    });

    function assertWhenClause(commandID: string, whenClause: string) {
        const cmdPalette = commandPaletteCommands[commandID];
        if (cmdPalette) {
            expect(cmdPalette.when).toBeDefined();
            expect(cmdPalette.when).toContain(whenClause);
        } else {
            // Adding a command to the list of commands means they will be added to the command palette
            if (contributedCommands) {
                expect(contributedCommands[commandID]).toBeUndefined();
            }
        }
    }

    const commands = getCommands();
    for (const command of commands) {
        switch (command.type) {
            case CommandType.debug:
                test(`check ${command.commandID} has debug in the when clause`, async () => {
                    assertWhenClause(command.commandID, "vscode-augment.enableDebugFeatures");
                });
                break;
            case CommandType.public:
                // Nothing to check - command may or may not be in the command palette
                break;
            case CommandType.private:
                test(`check ${command.commandID} is not in the command palette`, async () => {
                    if (commandPaletteCommands[command.commandID]) {
                        expect(commandPaletteCommands[command.commandID]["when"]).toBe("false");
                    } else {
                        expect(contributedCommands[command.commandID]).toBeUndefined();
                    }
                    // Prefixing the command with underscore ensures the command is not
                    // listed in the keyboard shortcuts.
                    expect(command.commandID.startsWith("_vscode-augment")).toBeTruthy();
                    // Private internal commands should not be shown in the action panel.
                    expect(command.showInActionPanel).toBeFalsy();
                });
                break;
            default:
                // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                throw new Error(`Unknown command type ${command.type}`);
        }
    }
});

jest.useRealTimers();

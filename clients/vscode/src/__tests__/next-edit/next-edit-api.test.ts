import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { BlobNameCalculator } from "@augment-internal/sidecar-libs/src/workspace/blob-name-calculator";

import { QualifiedPathName } from "../../../../sidecar/libs/src/workspace/qualified-path-name";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import {
    ExtensionContext,
    getWorkspaceFolder,
    MutableTextDocument,
    publishWorkspaceFoldersChange,
    TextEditor,
    TrackedDisposable,
    Uri,
    window,
    workspace,
} from "../../__mocks__/vscode-mocks";
import {
    DiagnosticSeverity,
    NextEditGenerationResult,
    NextEditStreamRequest,
} from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { DiagnosticsManager } from "../../diagnostics";
import { NextEditSessionEventReporter } from "../../metrics/next-edit-session-event-reporter";
import { FileEditEvent, SingleEdit } from "../../next-edit/file-edit-events";
import { queryNextEditStream } from "../../next-edit/next-edit-api";
import { shouldQueryNextEditStream } from "../../next-edit/next-edit-api";
import {
    IEditSuggestion,
    NextEditMode,
    NextEditScope,
    SuggestionState,
} from "../../next-edit/next-edit-types";
import { EditSuggestion } from "../../next-edit/suggestion-manager";
import * as fsUtils from "../../utils/fs-utils";
import { fileExists } from "../../utils/fs-utils";
import { LineRange } from "../../utils/ranges";
import { WorkspaceManager } from "../../workspace/workspace-manager";
import {
    awaitWorkspaceState,
    createSourceFolderFiles,
    defaultWorkspaceFolder,
    defaultWorkspaceState,
    WorkspaceManagerTestKit,
} from "../workspace/workspace-manager-test-kit";
import {
    request as unicodeRequest,
    response as unicodeResponse,
} from "./sample-suggestion-simple-unicode";

class NextEditAPITestKit extends WorkspaceManagerTestKit {
    static readonly workspaceFolder = defaultWorkspaceFolder;
    static readonly folderState = defaultWorkspaceState;

    public readonly ctx = new ExtensionContext();
    public readonly apiServer = new MockAPIServer();
    public workspaceManager: WorkspaceManager;
    public diagnosticsManager: DiagnosticsManager;
    public blobNameCalculator: BlobNameCalculator;
    public eventReporter: NextEditSessionEventReporter;

    static async create() {
        const kit = new NextEditAPITestKit();
        await kit.awaitStartup();
        return kit;
    }

    private constructor() {
        super();

        createSourceFolderFiles(NextEditAPITestKit.workspaceFolder);
        const workspaceFolders = [
            getWorkspaceFolder(NextEditAPITestKit.workspaceFolder.folderRootUri),
        ];
        publishWorkspaceFoldersChange(workspaceFolders);

        this.workspaceManager = this.createWorkspaceManager();
        this.addDisposable(this.workspaceManager);

        this.diagnosticsManager = new DiagnosticsManager();
        this.addDisposable(this.diagnosticsManager);

        this.blobNameCalculator = new BlobNameCalculator(1_000_000);

        this.eventReporter = new NextEditSessionEventReporter(this.apiServer);
        this.addDisposable(this.eventReporter);
    }

    public get folderRootUri(): Uri {
        return NextEditAPITestKit.workspaceFolder.folderRootUri;
    }

    public async awaitStartup() {
        await awaitWorkspaceState(this.workspaceManager, NextEditAPITestKit.folderState);
    }

    public getQualifiedPathName(relPath: string) {
        return new QualifiedPathName(this.folderRootUri.path, relPath);
    }
}

describe("queryNextEditStream", () => {
    let kit: NextEditAPITestKit;
    let doc: MutableTextDocument;

    beforeEach(async () => {
        jest.useFakeTimers()
            // Mock the current time; we log timestamps in the responses.
            .setSystemTime(new Date("2024-01-01"));

        kit = await NextEditAPITestKit.create();

        // Set an active editor
        doc = kit.newDocument(
            NextEditAPITestKit.workspaceFolder,
            "example/a",
            "line 0\nline 1\nline 2\nline 3\nline 4\n"
        );
        const editor = new TextEditor(doc);
        window.activeTextEditor = editor;
        workspace.textDocuments = [doc];

        jest.spyOn(fsUtils, "fileExists").mockImplementation((_pathName: string) => {
            return true;
        });
    });

    afterEach(() => {
        jest.useRealTimers();
        kit.dispose();
        TrackedDisposable.assertDisposed();
    });

    /**
     * A simple case with the following suggestions:
     * 1. a new line "new line 0" is added to the start
     * 2. line 1 -> modified line 1.
     */
    function simpleCase(): [NextEditStreamRequest, NextEditGenerationResult[], EditSuggestion[]] {
        const request: NextEditStreamRequest = {
            requestId: "request-1",
            instruction: "instruction",
            selectedCode: "selectedCode",
            prefix: "prefix",
            suffix: "suffix",
            pathName: kit.getQualifiedPathName("example/a"),
            language: "language",
            mode: NextEditMode.Foreground,
            scope: NextEditScope.File,
            fileEditEvents: [
                new FileEditEvent({
                    path: "example/a",
                    beforeBlobName: "",
                    afterBlobName: "",
                    edits: [],
                }),
            ],
            clientCreatedAt: new Date(),
            unindexedEditEvents: [],
            unindexedEditEventsBaseBlobNames: [],
        };
        const apiResponses: NextEditGenerationResult[] = [
            {
                result: {
                    suggestionId: "suggestion-1",
                    path: "example/a",
                    blobName: "",
                    charStart: 0,
                    charEnd: 0,
                    existingCode: "",
                    suggestedCode: "line -1\n",
                    truncationChar: undefined,
                    changeDescription: "",
                    diffSpans: [],
                    editingScore: 0,
                    localizationScore: 0,
                    editingScoreThreshold: 1.0,
                },
                unknownBlobNames: [],
                checkpointNotFound: false,
            },
            {
                result: {
                    suggestionId: "suggestion-2",
                    path: "example/a",
                    blobName: "",
                    charStart: "line 0\n".length,
                    charEnd: "line 0\nline 1\n".length,
                    existingCode: "line 1\n",
                    suggestedCode: "modified line 1\n",
                    truncationChar: undefined,
                    changeDescription: "",
                    diffSpans: [],
                    editingScore: 0,
                    localizationScore: 0,
                    editingScoreThreshold: 1.0,
                },
                unknownBlobNames: [],
                checkpointNotFound: false,
            },
        ];
        const suggestions = [
            new EditSuggestion(
                request.requestId,
                NextEditMode.Foreground,
                NextEditScope.File,
                apiResponses[0].result,
                kit.getQualifiedPathName("example/a"),
                new LineRange(0, 0),
                "file",
                new Date(),
                SuggestionState.fresh
            ),
            new EditSuggestion(
                request.requestId,
                NextEditMode.Foreground,
                NextEditScope.File,
                apiResponses[1].result,
                kit.getQualifiedPathName("example/a"),
                new LineRange(1, 2),
                "file",
                new Date(),
                SuggestionState.fresh
            ),
        ];
        return [request, apiResponses, suggestions];
    }

    it("returns a stream of suggestions", async () => {
        const [request, apiResponses, suggestions] = simpleCase();
        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield apiResponses[1];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });
        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[1],
        });
        expect((await stream.next()).done).toBe(true);
    });

    it("updates offsets when the buffer changes", async () => {
        const [request, apiResponses, suggestions] = simpleCase();
        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield apiResponses[1];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });

        const changeEvent = doc.applyChanges([
            // The user added a new line at the end of the file that shouldn't affect
            // the suggestions.
            {
                type: "insert",
                change: {
                    offset: doc.getText().length,
                    text: "new line at the end\n",
                },
            },
            // The user "accepted" the first suggestion, so the text was updated.
            {
                type: "insert",
                change: {
                    offset: suggestions[0].result.charStart,
                    text: suggestions[0].result.suggestedCode,
                },
            },
        ]);
        workspace.textDocumentChanged.fire(changeEvent);
        await jest.advanceTimersByTimeAsync(1);

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[1].with({
                result: {
                    ...suggestions[1].result,
                    charStart:
                        suggestions[1].result.charStart +
                        suggestions[0].result.suggestedCode.length,
                    charEnd:
                        suggestions[1].result.charEnd + suggestions[0].result.suggestedCode.length,
                },
                lineRange: new LineRange(
                    suggestions[1].lineRange.start + 1,
                    suggestions[1].lineRange.stop + 1
                ),
                occurredAt: new Date(suggestions[1].occurredAt.valueOf() + 1),
            }),
        });

        expect((await stream.next()).done).toBe(true);
    });

    it("invalidates overlapping changes with insertions", async () => {
        const [request, apiResponses, suggestions] = simpleCase();
        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[1];
            yield apiResponses[0];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[1],
        });

        const changeEvent = doc.applyChanges([
            // The user added a new line at the beginning of the file that should
            // invalidate the suggestions.
            {
                type: "insert",
                change: {
                    offset: 0,
                    text: "new line at the beginning\n",
                },
            },
        ]);
        workspace.textDocumentChanged.fire(changeEvent);
        await jest.advanceTimersByTimeAsync(1);

        expect((await stream.next()).value).toEqual({
            status: APIStatus.invalidArgument,
        });

        expect((await stream.next()).done).toBe(true);
    });

    it("stops streaming when the client cancels", async () => {
        const [request, apiResponses, suggestions] = simpleCase();
        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield apiResponses[1];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });

        cancelToken.isCancellationRequested = true;
        expect((await stream.next()).value).toEqual({
            status: APIStatus.cancelled,
        });
        expect((await stream.next()).done).toBe(true);
    });

    it("stops streaming when the client cancels for each version", async () => {
        const [request, apiResponses, suggestions] = simpleCase();
        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield apiResponses[1];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        let i = 0;
        for await (const response of stream) {
            if (i === 0) {
                expect(response).toEqual({
                    status: APIStatus.ok,
                    suggestion: suggestions[0],
                });
                cancelToken.isCancellationRequested = true;
            } else if (i === 1) {
                expect(response).toEqual({
                    status: APIStatus.cancelled,
                });
            } else {
                throw new Error("Should not have more than two responses.");
            }
            i++;
        }
        expect(i).toBe(2);
    });

    it("stops streaming when there is an error", async () => {
        const [request, apiResponses, suggestions] = simpleCase();
        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            throw new Error("Injected error");
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });

        expect((await stream.next()).value).toEqual({
            status: APIStatus.unknown,
        });
        expect((await stream.next()).done).toBe(true);
    });

    it("errors when the code doesn't match the buffer", async () => {
        const [request, apiResponses, suggestions] = simpleCase();
        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield {
                ...apiResponses[1],
                result: {
                    ...apiResponses[1].result,
                    existingCode: apiResponses[1].result.existingCode + "a",
                },
            };
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });

        expect((await stream.next()).value).toEqual({
            status: APIStatus.invalidArgument,
        });
        expect((await stream.next()).done).toBe(true);
    });

    it("skips when there are no changes", async () => {
        const [request, _, __] = simpleCase();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            { ...request, fileEditEvents: [] },
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );
        const result = await stream.next();
        expect(result.value).toEqual({ status: APIStatus.ok });
        expect((await stream.next()).done).toBe(true);
        expect(kit.apiServer.nextEditStream).not.toHaveBeenCalled();
    });

    it("line-aligns when suggestion is at the end of the file", async () => {
        const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
        doc = new MutableTextDocument(uri, "line 0\nline 1");
        window.activeTextEditor = new TextEditor(doc);
        workspace.textDocuments = [doc];

        let [request, apiResponses, suggestions] = simpleCase();
        // Modify the case to strip the trailing newline.
        apiResponses[1].result = {
            ...apiResponses[1].result,
            charStart: "line 0\n".length,
            charEnd: "line 0\nline 1".length,
            existingCode: "line 1",
            suggestedCode: "modified line 1\n",
        };
        suggestions[1] = suggestions[1].with({
            result: {
                ...apiResponses[1].result,
                charStart: "line 0\n".length,
                charEnd: "line 0\nline 1".length,
                existingCode: "line 1",
                suggestedCode: "modified line 1\n",
            },
        });

        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield apiResponses[1];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );
        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });
        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[1],
        });
        expect((await stream.next()).done).toBe(true);
    });

    it("line-aligns when suggestion is at the end of the file and there is a trailing newline", async () => {
        const uri = Uri.joinPath(kit.folderRootUri, "/example/a");
        doc = new MutableTextDocument(uri, "line 0\nline 1\n");
        window.activeTextEditor = new TextEditor(doc);
        workspace.textDocuments = [doc];

        let [request, apiResponses, suggestions] = simpleCase();
        apiResponses = [apiResponses[1]];
        suggestions = [suggestions[1]];
        // Modify the case to strip the trailing newline.
        apiResponses[0].result = {
            ...apiResponses[0].result,
            charStart: "line 0\nline 1\n".length,
            charEnd: "line 0\nline 1\n".length,
            existingCode: "",
            suggestedCode: "line2\n",
        };
        suggestions[0] = suggestions[0].with({
            result: {
                ...apiResponses[0].result,
                charStart: "line 0\nline 1\n".length,
                charEnd: "line 0\nline 1\n".length,
                existingCode: "",
                suggestedCode: "line2\n",
            },
            lineRange: new LineRange(2, 2),
        });

        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );
        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });
        expect((await stream.next()).done).toBe(true);
    });

    it("guesses the workspace when there is no workspace root", async () => {
        let [request, apiResponses, suggestions] = simpleCase();
        // But this path is known to the workspace.
        jest.spyOn(kit.workspaceManager, "getAllQualifiedPathNames").mockReturnValue([
            kit.getQualifiedPathName(request.pathName?.relPath!),
        ]);

        // Modify the case to remove the path from the request.
        request = {
            ...request,
            pathName: undefined,
        };

        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield apiResponses[1];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );
        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[0],
        });
        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: suggestions[1],
        });
        expect((await stream.next()).done).toBe(true);
    });

    it("returns empty when there are multiple candidates paths and no recent edits", async () => {
        let [request, apiResponses, _] = simpleCase();
        // But this path is known to the workspace.
        jest.spyOn(kit.workspaceManager, "getAllQualifiedPathNames").mockReturnValue([
            kit.getQualifiedPathName(request.pathName?.relPath!),
            new QualifiedPathName("/otherRoot", request.pathName?.relPath!),
        ]);

        // Modify the case to remove the path from the request.
        request = {
            ...request,
            pathName: undefined,
        };

        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield apiResponses[0];
            yield apiResponses[1];
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");
        jest.spyOn(kit.workspaceManager, "getMostRecentlyChangedFolderRoot").mockReturnValue(
            undefined
        );

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );
        expect((await stream.next()).done).toBe(true);
    });

    it("handles unicode", async () => {
        /* eslint-disable @typescript-eslint/naming-convention */
        const request: NextEditStreamRequest = {
            ...unicodeRequest.request,
            mode: unicodeRequest.request.mode as NextEditMode,
            scope: unicodeRequest.request.scope as NextEditScope,
            blobs: {
                addedBlobs: unicodeRequest.request.blobs.added,
                deletedBlobs: unicodeRequest.request.blobs.deleted,
                checkpointId: unicodeRequest.request.blobs.baselineCheckpointId,
            },
            recentChanges: unicodeRequest.request.recentChanges.map((c) => ({
                path: c.path,
                blob_name: c.blobName,
                char_start: c.charStart,
                char_end: c.charEnd,
                replacement_text: c.replacementText,
                present_in_blob: c.presentInBlob,
                expected_blob_name: c.expectedBlobName,
            })),

            diagnostics: unicodeRequest.request.diagnostics.map((d) => ({
                location: {
                    path: d.location.path,
                    line_start: d.location.lineStart,
                    line_end: d.location.lineEnd,
                },
                current_blob_name: "",
                blob_name: "",
                char_start: 0,
                char_end: 0,
                message: d.message,
                severity: d.severity as DiagnosticSeverity,
            })),
            clientCreatedAt: new Date(),
            fileEditEvents: unicodeRequest.request.editEvents.map(
                (e) =>
                    new FileEditEvent({
                        path: e.path,
                        beforeBlobName: e.beforeBlobName,
                        afterBlobName: e.afterBlobName,
                        edits: e.edits.map(
                            (edit) =>
                                new SingleEdit({
                                    beforeStart: edit.beforeStart,
                                    afterStart: edit.afterStart,
                                    beforeText: edit.beforeText,
                                    afterText: edit.afterText,
                                })
                        ),
                    })
            ),
            unindexedEditEvents: [],
            unindexedEditEventsBaseBlobNames: [],
        };
        /* eslint-enable @typescript-eslint/naming-convention */
        const suggestion = unicodeResponse.result.suggestedEdit;

        const fullText = suggestion.existingCode;
        const doc = new MutableTextDocument(
            Uri.from({ scheme: "untitled", path: unicodeRequest.request.path }),
            fullText
        );
        const editor = new TextEditor(doc);
        window.activeTextEditor = editor;
        workspace.textDocuments = [doc];
        window.visibleTextEditors = [editor];

        async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
            yield {
                result: suggestion,
                unknownBlobNames: unicodeResponse.result.unknownBlobNames,
                checkpointNotFound: unicodeResponse.result.checkpointNotFound,
            };
        }
        kit.apiServer.nextEditStream = async () => mockEditStream();
        jest.spyOn(kit.apiServer, "nextEditStream");
        jest.spyOn(kit.workspaceManager, "getAllQualifiedPathNames").mockReturnValue([]);
        jest.spyOn(kit.workspaceManager, "getMostRecentlyChangedFolderRoot").mockReturnValue(
            undefined
        );
        jest.spyOn(kit.eventReporter, "reportEvent");

        const cancelToken = { isCancellationRequested: false, onCancellationRequested: jest.fn() };
        const stream = queryNextEditStream(
            request,
            kit.workspaceManager,
            kit.diagnosticsManager,
            kit.apiServer,
            kit.blobNameCalculator,
            kit.configListener,
            cancelToken,
            kit.eventReporter
        );

        expect((await stream.next()).value).toEqual({
            status: APIStatus.ok,
            suggestion: new EditSuggestion(
                request.requestId,
                request.mode,
                request.scope,
                {
                    ...suggestion,
                    charEnd: 3, // We convert this from 2 to 3 in the backend.
                },
                new QualifiedPathName("", unicodeRequest.request.path),
                new LineRange(0, 2),
                "untitled"
            ),
        });
        expect((await stream.next()).done).toBe(true);
    });

    describe("mock data", () => {
        let suggestions: IEditSuggestion[];
        let request: NextEditStreamRequest;
        let getStream: () => AsyncGenerator<{ status: APIStatus; suggestion?: IEditSuggestion }>;

        beforeEach(() => {
            const [r, apiResponses, s] = simpleCase();
            request = r;
            suggestions = s;
            async function* mockEditStream(): AsyncGenerator<NextEditGenerationResult> {
                yield apiResponses[0];
                yield apiResponses[1];
            }
            kit.apiServer.nextEditStream = jest.fn(async () => mockEditStream());

            const mockSuggestions = suggestions.map((s) =>
                EditSuggestion.from({
                    ...s,
                    result: {
                        ...s.result,
                        suggestionId: "mock-" + s.result.suggestionId,
                    },
                })
            );
            workspace.textDocuments.push(
                kit.newDocument(
                    NextEditAPITestKit.workspaceFolder,
                    "example/a.next-edit-results.json5",
                    JSON.stringify(mockSuggestions.map((s) => s.result))
                )
            );

            const cancelToken = {
                isCancellationRequested: false,
                onCancellationRequested: jest.fn(),
            };
            getStream = () =>
                queryNextEditStream(
                    request,
                    kit.workspaceManager,
                    kit.diagnosticsManager,
                    kit.apiServer,
                    kit.blobNameCalculator,
                    kit.configListener,
                    cancelToken,
                    kit.eventReporter
                );
        });

        it("ignores mocks when useMockResults=false", async () => {
            kit.configListener.config.nextEdit.useMockResults = false;
            const stream = getStream();
            expect((await stream.next()).value).toEqual({
                status: APIStatus.ok,
                suggestion: suggestions[0],
            });
            expect((await stream.next()).value).toEqual({
                status: APIStatus.ok,
                suggestion: suggestions[1],
            });
            expect((await stream.next()).done).toBe(true);
            expect(kit.apiServer.nextEditStream).toHaveBeenCalled();
        });

        it("uses mocks when useMockResults=true", async () => {
            kit.configListener.config.nextEdit.useMockResults = true;
            const stream = getStream();
            expect((await stream.next()).value).toEqual({
                status: APIStatus.ok,
                suggestion: expect.objectContaining({
                    result: expect.objectContaining({
                        suggestionId: "mock-suggestion-1",
                    }),
                }),
            });
            expect((await stream.next()).value).toEqual({
                status: APIStatus.ok,
                suggestion: expect.objectContaining({
                    result: expect.objectContaining({
                        suggestionId: "mock-suggestion-2",
                    }),
                }),
            });
            expect((await stream.next()).done).toBe(true);
            expect(kit.apiServer.nextEditStream).not.toHaveBeenCalled();
        });

        it("cancels request with no edit events when useMockResults=false", async () => {
            kit.configListener.config.nextEdit.useMockResults = false;
            request = { ...request, fileEditEvents: [] };
            const stream = getStream();

            expect((await stream.next()).value).toEqual({
                status: APIStatus.ok,
            });
            expect((await stream.next()).done).toBe(true);
            expect(kit.apiServer.nextEditStream).not.toHaveBeenCalled();
        });

        it("does not cancel request with no edit events when useMockResults=true", async () => {
            kit.configListener.config.nextEdit.useMockResults = true;
            request = { ...request, fileEditEvents: [] };
            const stream = getStream();

            expect((await stream.next()).value).toEqual({
                status: APIStatus.ok,
                suggestion: expect.objectContaining({
                    result: expect.objectContaining({
                        suggestionId: "mock-suggestion-1",
                    }),
                }),
            });
            expect((await stream.next()).value).toEqual({
                status: APIStatus.ok,
                suggestion: expect.objectContaining({
                    result: expect.objectContaining({
                        suggestionId: "mock-suggestion-2",
                    }),
                }),
            });
            expect((await stream.next()).done).toBe(true);
            expect(kit.apiServer.nextEditStream).not.toHaveBeenCalled();
        });
    });
});

describe("shouldQueryNextEditStream", () => {
    let mockWorkspaceManager: jest.Mocked<WorkspaceManager>;
    let mockConfigListener: AugmentConfigListener;
    let mockQualifiedPathName: QualifiedPathName;

    beforeEach(() => {
        mockWorkspaceManager = {
            getFileEditEvents: jest.fn(),
            getFolderRoot: jest.fn(),
            getMostRecentlyChangedFolderRoot: jest.fn(),
        } as any;

        mockConfigListener = {
            config: {
                nextEdit: {
                    useMockResults: false,
                },
            },
        } as AugmentConfigListener;

        mockQualifiedPathName = QualifiedPathName.from({
            rootPath: "/root",
            relPath: "test.ts",
        });

        (fileExists as jest.Mock).mockReset();
    });

    test("returns true when file edit events exist", () => {
        const fileEditEvents = [
            new FileEditEvent({
                path: "test.ts",
                edits: [],
                beforeBlobName: "",
                afterBlobName: "",
            }),
        ];
        mockWorkspaceManager.getFileEditEvents.mockReturnValue(fileEditEvents);

        const result = shouldQueryNextEditStream(
            mockWorkspaceManager,
            mockConfigListener,
            mockQualifiedPathName,
            undefined
        );

        expect(result).toBe(true);
    });

    test("returns false when no file edit events and mock results disabled", () => {
        mockWorkspaceManager.getFileEditEvents.mockReturnValue([]);
        mockConfigListener.config.nextEdit.useMockResults = false;
        (fileExists as jest.Mock).mockReturnValue(false);

        const result = shouldQueryNextEditStream(
            mockWorkspaceManager,
            mockConfigListener,
            mockQualifiedPathName,
            undefined
        );

        expect(result).toBe(false);
    });

    test("returns true when mock results enabled and mock file exists", () => {
        mockWorkspaceManager.getFileEditEvents.mockReturnValue([]);
        mockConfigListener.config.nextEdit.useMockResults = true;
        (fileExists as jest.Mock).mockReturnValue(true);

        const result = shouldQueryNextEditStream(
            mockWorkspaceManager,
            mockConfigListener,
            mockQualifiedPathName,
            undefined
        );

        expect(result).toBe(true);
    });

    test("uses provided fileEditEvents instead of workspace manager events", () => {
        const providedEvents = [
            new FileEditEvent({
                path: "test.ts",
                edits: [],
                beforeBlobName: "",
                afterBlobName: "",
            }),
        ];
        const result = shouldQueryNextEditStream(
            mockWorkspaceManager,
            mockConfigListener,
            mockQualifiedPathName,
            providedEvents
        );

        expect(result).toBe(true);
        expect(mockWorkspaceManager.getFileEditEvents).not.toHaveBeenCalled();
    });

    test("returns false when mock results enabled but mock file does not exist", () => {
        mockWorkspaceManager.getFileEditEvents.mockReturnValue([]);
        mockConfigListener.config.nextEdit.useMockResults = true;
        (fileExists as jest.Mock).mockReturnValue(false);

        const result = shouldQueryNextEditStream(
            mockWorkspaceManager,
            mockConfigListener,
            mockQualifiedPathName,
            undefined
        );

        expect(result).toBe(false);
    });
});

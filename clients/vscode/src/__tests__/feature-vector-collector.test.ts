import * as crypto from "crypto";

import { workspace } from "../__mocks__/vscode-mocks";
import {
    createFeatures,
    Features,
    FeatureVectorKey,
    ClientSpecificData,
} from "@augment-internal/sidecar-libs/src/feature-vector/feature-vector-collector";

// Set up workspace folders
workspace.workspaceFolders = [
    {
        uri: {
            fsPath: "/mock/workspace/path",
        },
        name: "mock-workspace",
        index: 0,
    },
] as any;

// Mock node-machine-id
jest.mock("node-machine-id", () => ({
    machineIdSync: jest.fn(() => "mock-os-machine-id-1234567890"),
}));

// Mock systeminformation
jest.mock("systeminformation", () => ({
    graphics: jest.fn(() =>
        Promise.resolve({
            controllers: [
                {
                    vendor: "Mock GPU Vendor",
                    model: "Mock GPU Model",
                    vram: 8192,
                },
            ],
        })
    ),
    time: jest.fn(() =>
        Promise.resolve({
            timezone: "UTC+0000",
        })
    ),
    diskLayout: jest.fn(() =>
        Promise.resolve([
            {
                type: "SSD",
                size: 512110190592,
            },
        ])
    ),
    system: jest.fn(() =>
        Promise.resolve({
            manufacturer: "Mock Manufacturer",
            model: "Mock Model",
            version: "1.0",
        })
    ),
    bios: jest.fn(() =>
        Promise.resolve({
            vendor: "Mock BIOS",
            version: "1.0.0",
            releaseDate: "2023-01-01",
        })
    ),
    baseboard: jest.fn(() =>
        Promise.resolve({
            manufacturer: "Mock Baseboard",
            model: "Mock Model",
            version: "1.0",
        })
    ),
    chassis: jest.fn(() =>
        Promise.resolve({
            manufacturer: "Mock Chassis",
            type: "Notebook",
            version: "1.0",
        })
    ),
}));

// Mock fs.lstat for inode tests
jest.mock("fs/promises", () => ({
    lstat: jest.fn(() =>
        Promise.resolve({
            ino: 12345678,
        })
    ),
    readFile: jest.fn(() => Promise.reject(new Error("No SSH key"))),
}));

// Mock child_process for git commands
jest.mock("child_process", () => ({
    exec: jest.fn((cmd, callback) => {
        if (cmd.includes("git config user.email")) {
            callback(null, { stdout: "<EMAIL>\n" });
        } else {
            callback(new Error("Command not found"));
        }
    }),
}));

// Mock extension context
const mockContext = {
    globalState: {
        get: jest.fn((key: string) => {
            if (key === "telemetry.devDeviceId") {
                return "mock-telemetry-dev-device-id";
            }
            return undefined;
        }),
        update: jest.fn(),
        keys: jest.fn(() => []),
        setKeysForSync: jest.fn(),
    },
} as any;

// Helper function to convert mock context to ClientSpecificData
function createClientData(context: any): ClientSpecificData {
    return {
        clientVersion: "1.82.0", // Mock VSCode version
        clientMachineId: "mock-vscode-machine-id",
        telemetryDevDeviceId: context.globalState.get("telemetry.devDeviceId") || "",
        projectRootPath: workspace.workspaceFolders?.[0]?.uri.fsPath,
    };
}

describe("Feature Vector Collector", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe("createFeatures", () => {
        it("should generate different requestId and randomHash each time", async () => {
            const features1 = await createFeatures(createClientData(mockContext));
            const features2 = await createFeatures(createClientData(mockContext));

            const vector1 = features1.toVector();
            const vector2 = features2.toVector();

            // requestId and randomHash should be different
            expect(vector1[FeatureVectorKey.requestId]).not.toBe(
                vector2[FeatureVectorKey.requestId]
            );
            expect(vector1[FeatureVectorKey.randomHash]).not.toBe(
                vector2[FeatureVectorKey.randomHash]
            );
        });

        it("should handle missing telemetryDevDeviceId gracefully", async () => {
            const contextWithoutDevId = {
                globalState: {
                    get: jest.fn(() => undefined),
                },
            } as any;

            const features = await createFeatures(createClientData(contextWithoutDevId));
            const vector = features.toVector();

            // Should hash empty string
            expect(vector[FeatureVectorKey.telemetryDevDeviceId]).toBe(
                crypto.createHash("sha256").update("").digest("hex")
            );
        });
    });

    describe("Checksum calculation", () => {
        it("should calculate checksum deterministically based on all field values", async () => {
            // Create a Features instance with known values
            const testFeatures = new Features(
                "1.82.0", // vscode
                "test-machine-id",
                "Linux",
                "Intel Core i7",
                "16000000000", // memory
                "8", // numCpus
                "test-hostname",
                "x64", // arch
                "testuser",
                ["aa:bb:cc:dd:ee:ff", "11:22:33:44:55:66"],
                "5.15.0", // osRelease
                "Linux version 5.15.0", // kernelVersion
                "test-telemetry-id",
                "fixed-request-id",
                "fixed-random-hash",
                "os-machine-id-123",
                "123456", // homeDirectoryIno
                "789012", // projectRootIno
                "<EMAIL>",
                "ssh-rsa AAAAB3... test@host",
                "NVIDIA RTX 3080 10GB",
                "UTC+0000",
                "SSD 512110190592",
                "Dell Inc. XPS 15",
                "Dell Inc. 1.0.0 2023-01-01",
                "Dell Inc. XPS 1.0",
                "Dell Inc. Notebook 1.0"
            );

            const vector = testFeatures.toVector();

            // Calculate what the checksum should be based on the algorithm
            const sortedKeys = Object.keys(vector)
                .filter((key) => key !== String(FeatureVectorKey.checksum))
                .sort();
            const values = sortedKeys.map((key) => vector[Number(key)]);
            const combined = values.join("");

            // The checksum should be deterministic for the same inputs
            const expectedChecksum =
                "v1#" + crypto.createHash("sha256").update(combined).digest("hex");
            expect(vector[FeatureVectorKey.checksum]).toBe(expectedChecksum);

            // Create another instance with the same values
            const testFeatures2 = new Features(
                "1.82.0", // vscode
                "test-machine-id",
                "Linux",
                "Intel Core i7",
                "16000000000", // memory
                "8", // numCpus
                "test-hostname",
                "x64", // arch
                "testuser",
                ["aa:bb:cc:dd:ee:ff", "11:22:33:44:55:66"],
                "5.15.0", // osRelease
                "Linux version 5.15.0", // kernelVersion
                "test-telemetry-id",
                "fixed-request-id",
                "fixed-random-hash",
                "os-machine-id-123",
                "123456", // homeDirectoryIno
                "789012", // projectRootIno
                "<EMAIL>",
                "ssh-rsa AAAAB3... test@host",
                "NVIDIA RTX 3080 10GB",
                "UTC+0000",
                "SSD 512110190592",
                "Dell Inc. XPS 15",
                "Dell Inc. 1.0.0 2023-01-01",
                "Dell Inc. XPS 1.0",
                "Dell Inc. Notebook 1.0"
            );

            const vector2 = testFeatures2.toVector();

            // Both should produce identical checksums
            expect(vector2[FeatureVectorKey.checksum]).toBe(vector[FeatureVectorKey.checksum]);
        });

        it("should produce different checksums when any field changes", async () => {
            const baseFeatures = new Features(
                "1.82.0",
                "machine1",
                "Linux",
                "CPU1",
                "16GB",
                "8",
                "host1",
                "x64",
                "user1",
                ["aa:bb:cc:dd:ee:ff"],
                "5.15.0",
                "Linux 5.15.0",
                "telemetry1",
                "req1",
                "hash1",
                "os1",
                "123",
                "456",
                "<EMAIL>",
                "ssh-rsa key1",
                "GPU1",
                "UTC",
                "SSD 512GB",
                "System1",
                "BIOS1",
                "Board1",
                "Chassis1"
            );

            const modifiedFeatures = new Features(
                "1.82.0",
                "machine2", // Changed machine ID
                "Linux",
                "CPU1",
                "16GB",
                "8",
                "host1",
                "x64",
                "user1",
                ["aa:bb:cc:dd:ee:ff"],
                "5.15.0",
                "Linux 5.15.0",
                "telemetry1",
                "req1",
                "hash1",
                "os1",
                "123",
                "456",
                "<EMAIL>",
                "ssh-rsa key1",
                "GPU1",
                "UTC",
                "SSD 512GB",
                "System1",
                "BIOS1",
                "Board1",
                "Chassis1"
            );

            const vector1 = baseFeatures.toVector();
            const vector2 = modifiedFeatures.toVector();

            // Checksums should be different when any field changes
            expect(vector1[FeatureVectorKey.checksum]).not.toBe(vector2[FeatureVectorKey.checksum]);
        });

        it("should include all fields except checksum itself in the checksum calculation", async () => {
            const features = new Features(
                "1.82.0",
                "machine1",
                "Linux",
                "CPU1",
                "16GB",
                "8",
                "host1",
                "x64",
                "user1",
                ["aa:bb:cc:dd:ee:ff"],
                "5.15.0",
                "Linux 5.15.0",
                "telemetry1",
                "req1",
                "hash1",
                "os1",
                "123",
                "456",
                "<EMAIL>",
                "ssh-rsa key1",
                "GPU1",
                "UTC",
                "SSD 512GB",
                "System1",
                "BIOS1",
                "Board1",
                "Chassis1"
            );

            const vector = features.toVector();

            // Manually calculate checksum to verify all fields are included
            const allFieldsExceptChecksum = Object.values(FeatureVectorKey)
                .filter((value): value is number => typeof value === "number" && value !== FeatureVectorKey.checksum)
                .sort((a, b) => a - b);

            const vectorKeysExceptChecksum = Object.keys(vector)
                .map(Number)
                .filter((key) => key !== Number(FeatureVectorKey.checksum))
                .sort((a, b) => a - b);

            // Verify all fields (except checksum) are included in the vector
            expect(vectorKeysExceptChecksum).toEqual(allFieldsExceptChecksum);

            // Verify the checksum is calculated from all these fields
            const sortedKeys = Object.keys(vector)
                .filter((key) => key !== String(FeatureVectorKey.checksum))
                .sort();

            // Each key should contribute to the checksum
            expect(sortedKeys.length).toBe(allFieldsExceptChecksum.length);
        });

        it("should only include expected FeatureVectorKey fields and no prototype or extra properties", async () => {
            const features = await createFeatures(createClientData(mockContext));
            const vector = features.toVector();

            // Get all keys from the vector
            const vectorKeys = Object.keys(vector)
                .map(Number)
                .sort((a, b) => a - b);

            // Get all expected keys from the FeatureVectorKey enum
            const expectedKeys = Object.values(FeatureVectorKey)
                .filter((value): value is number => typeof value === "number")
                .sort((a, b) => a - b);

            // Verify that the vector has exactly the expected keys
            expect(vectorKeys).toEqual(expectedKeys);

            // Verify no prototype properties are included
            expect(Object.prototype.hasOwnProperty.call(vector, "toString")).toBe(false);
            expect(Object.prototype.hasOwnProperty.call(vector, "valueOf")).toBe(false);
            expect(Object.prototype.hasOwnProperty.call(vector, "constructor")).toBe(false);

            // Add a prototype property to test it's not included in checksum calculation
            const testPrototype = {
                extraProperty: "should-not-be-included",
            };
            Object.setPrototypeOf(vector, testPrototype);

            // Recalculate checksum manually to ensure it only uses own properties
            const sortedKeys = Object.keys(vector).sort();
            expect(sortedKeys).not.toContain("extraProperty");

            // Verify the checksum calculation ignores prototype properties
            const features2 = await createFeatures(createClientData(mockContext));
            const vector2 = features2.toVector();

            // Both vectors should have same keys despite one having a modified prototype
            expect(Object.keys(vector).sort()).toEqual(Object.keys(vector2).sort());
        });
    });

    describe("Canonicalization", () => {
        it("should normalize strings consistently by lowercasing and trimming", async () => {
            // Test that canonicalize normalizes strings
            const testCases = [
                ["Test", "test"],
                ["  TEST  ", "test"],
                ["TeSt123", "test123"],
                ["  Mixed CASE with spaces  ", "mixed case with spaces"],
            ];

            for (const [input, expected] of testCases) {
                // Create two feature instances with different casing/spacing
                const f1 = new Features(
                    input, // vscode version with different casing
                    "machineId",
                    "os",
                    "cpu",
                    "memory",
                    "numCpus",
                    "hostname",
                    "arch",
                    "username",
                    [],
                    "osRelease",
                    "kernelVersion",
                    "telemetryDevDeviceId",
                    "requestId",
                    "randomHash",
                    "osMachineId",
                    "homeDirectoryIno",
                    "projectRootIno",
                    "gitUserEmail",
                    "sshPublicKey",
                    "gpuInfo",
                    "timezone",
                    "diskLayout",
                    "systemInfo",
                    "biosInfo",
                    "baseboardInfo",
                    "chassisInfo"
                );

                const f2 = new Features(
                    expected, // normalized version
                    "machineId",
                    "os",
                    "cpu",
                    "memory",
                    "numCpus",
                    "hostname",
                    "arch",
                    "username",
                    [],
                    "osRelease",
                    "kernelVersion",
                    "telemetryDevDeviceId",
                    "requestId",
                    "randomHash",
                    "osMachineId",
                    "homeDirectoryIno",
                    "projectRootIno",
                    "gitUserEmail",
                    "sshPublicKey",
                    "gpuInfo",
                    "timezone",
                    "diskLayout",
                    "systemInfo",
                    "biosInfo",
                    "baseboardInfo",
                    "chassisInfo"
                );

                const v1 = f1.toVector();
                const v2 = f2.toVector();

                // The canonicalized values should be identical
                expect(v1[FeatureVectorKey.vscode]).toBe(v2[FeatureVectorKey.vscode]);
            }
        });

        it("should canonicalize arrays by joining with commas and normalizing", async () => {
            // Test MAC address array canonicalization
            const f1 = new Features(
                "vscode",
                "machineId",
                "os",
                "cpu",
                "memory",
                "numCpus",
                "hostname",
                "arch",
                "username",
                ["AA:BB:CC:DD:EE:FF", "11:22:33:44:55:66"], // uppercase MACs
                "osRelease",
                "kernelVersion",
                "telemetryDevDeviceId",
                "requestId",
                "randomHash",
                "osMachineId",
                "homeDirectoryIno",
                "projectRootIno",
                "gitUserEmail",
                "sshPublicKey",
                "gpuInfo",
                "timezone",
                "diskLayout",
                "systemInfo",
                "biosInfo",
                "baseboardInfo",
                "chassisInfo"
            );

            const f2 = new Features(
                "vscode",
                "machineId",
                "os",
                "cpu",
                "memory",
                "numCpus",
                "hostname",
                "arch",
                "username",
                ["aa:bb:cc:dd:ee:ff", "11:22:33:44:55:66"], // lowercase MACs
                "osRelease",
                "kernelVersion",
                "telemetryDevDeviceId",
                "requestId",
                "randomHash",
                "osMachineId",
                "homeDirectoryIno",
                "projectRootIno",
                "gitUserEmail",
                "sshPublicKey",
                "gpuInfo",
                "timezone",
                "diskLayout",
                "systemInfo",
                "biosInfo",
                "baseboardInfo",
                "chassisInfo"
            );

            const v1 = f1.toVector();
            const v2 = f2.toVector();

            // Canonicalized MAC addresses should be identical
            expect(v1[FeatureVectorKey.macAddresses]).toBe(v2[FeatureVectorKey.macAddresses]);
        });
    });

    describe("MAC address handling", () => {
        it("should canonicalize MAC addresses consistently", async () => {
            // The canonicalizeArray method joins with commas but doesn't sort
            // The sorting happens in getExternalMacAddresses()
            const f1 = new Features(
                "vscode",
                "machineId",
                "os",
                "cpu",
                "memory",
                "numCpus",
                "hostname",
                "arch",
                "username",
                ["AA:BB:CC:DD:EE:FF"], // uppercase
                "osRelease",
                "kernelVersion",
                "telemetryDevDeviceId",
                "requestId",
                "randomHash",
                "osMachineId",
                "homeDirectoryIno",
                "projectRootIno",
                "gitUserEmail",
                "sshPublicKey",
                "gpuInfo",
                "timezone",
                "diskLayout",
                "systemInfo",
                "biosInfo",
                "baseboardInfo",
                "chassisInfo"
            );

            const f2 = new Features(
                "vscode",
                "machineId",
                "os",
                "cpu",
                "memory",
                "numCpus",
                "hostname",
                "arch",
                "username",
                ["aa:bb:cc:dd:ee:ff"], // lowercase
                "osRelease",
                "kernelVersion",
                "telemetryDevDeviceId",
                "requestId",
                "randomHash",
                "osMachineId",
                "homeDirectoryIno",
                "projectRootIno",
                "gitUserEmail",
                "sshPublicKey",
                "gpuInfo",
                "timezone",
                "diskLayout",
                "systemInfo",
                "biosInfo",
                "baseboardInfo",
                "chassisInfo"
            );

            const v1 = f1.toVector();
            const v2 = f2.toVector();

            // Both should produce the same canonicalized MAC address value (case-insensitive)
            expect(v1[FeatureVectorKey.macAddresses]).toBe(v2[FeatureVectorKey.macAddresses]);
        });

        it("should handle MAC address arrays with proper ordering from getExternalMacAddresses", async () => {
            // The actual sorting happens in getExternalMacAddresses, not in canonicalizeArray
            const features = await createFeatures(mockContext);

            // Just verify that MAC addresses are collected (mocks return empty array)
            expect(features.macAddresses).toBeDefined();
            expect(Array.isArray(features.macAddresses)).toBe(true);
        });
    });

    describe("Error handling and graceful degradation", () => {
        it("should return empty strings for all error cases without throwing", async () => {
            // Mock fs.lstat to throw error
            const fs = require("fs/promises");
            const originalLstat = fs.lstat;
            fs.lstat = jest.fn().mockRejectedValue(new Error("Permission denied"));

            // Mock child_process.exec to fail
            const childProcess = require("child_process");
            const originalExec = childProcess.exec;
            childProcess.exec = jest.fn((_cmd, callback) => {
                callback(new Error("Command not found"));
            });

            // Mock systeminformation to fail
            const si = require("systeminformation");
            si.graphics.mockRejectedValue(new Error("SI failed"));
            si.time.mockRejectedValue(new Error("SI failed"));
            si.diskLayout.mockRejectedValue(new Error("SI failed"));
            si.system.mockRejectedValue(new Error("SI failed"));
            si.bios.mockRejectedValue(new Error("SI failed"));
            si.baseboard.mockRejectedValue(new Error("SI failed"));
            si.chassis.mockRejectedValue(new Error("SI failed"));

            // Mock SSH key reading to fail
            fs.readFile = jest.fn().mockRejectedValue(new Error("No SSH key"));

            // Mock machine ID to throw
            const machineId = require("node-machine-id");
            machineId.machineIdSync.mockImplementation(() => {
                throw new Error("Cannot get machine ID");
            });

            // Note: We can't mock os.userInfo directly, but the code handles errors gracefully

            try {
                const features = await createFeatures(createClientData(mockContext));

                // All error-prone fields should be empty strings
                expect(features.homeDirectoryIno).toBe("");
                expect(features.projectRootIno).toBe("");
                expect(features.gitUserEmail).toBe("");
                expect(features.sshPublicKey).toBe("");
                expect(features.osMachineId).toBe("");
                // Note: username comes from os.userInfo() which we can't mock, so it may have a value
                expect(features.gpuInfo).toBe("");
                expect(features.timezone).toBe("");
                expect(features.diskLayout).toBe("");
                expect(features.systemInfo).toBe("");
                expect(features.biosInfo).toBe("");
                expect(features.baseboardInfo).toBe("");
                expect(features.chassisInfo).toBe("");

                // Should not throw any errors
                const vector = features.toVector();
                expect(vector).toBeDefined();
            } finally {
                // Restore all mocks
                fs.lstat = originalLstat;
                childProcess.exec = originalExec;
            }
        });
    });

    describe("SSH key priority order", () => {
        it("should try SSH keys in order: RSA, Ed25519, ECDSA", async () => {
            const fs = require("fs/promises");
            const readFileCalls: string[] = [];

            // Track which files are attempted
            fs.readFile = jest.fn((path: string) => {
                readFileCalls.push(path);
                return Promise.reject(new Error("No such file"));
            });

            const features = await createFeatures(createClientData(mockContext));

            // Should have tried all three key types in order
            expect(readFileCalls.length).toBe(3);
            expect(readFileCalls[0]).toContain("id_rsa.pub");
            expect(readFileCalls[1]).toContain("id_ed25519.pub");
            expect(readFileCalls[2]).toContain("id_ecdsa.pub");

            // Should return empty string when no keys found
            expect(features.sshPublicKey).toBe("");
        });

        it("should stop at first successful SSH key read", async () => {
            const fs = require("fs/promises");
            const readFileCalls: string[] = [];

            // Ed25519 key exists, others don't
            fs.readFile = jest.fn((path: string) => {
                readFileCalls.push(path);
                if (path.includes("id_ed25519.pub")) {
                    return Promise.resolve("ssh-ed25519 AAAAC3... user@host\n");
                }
                return Promise.reject(new Error("No such file"));
            });

            const features = await createFeatures(createClientData(mockContext));

            // Should have tried RSA first, then Ed25519, but not ECDSA
            expect(readFileCalls.length).toBe(2);
            expect(readFileCalls[0]).toContain("id_rsa.pub");
            expect(readFileCalls[1]).toContain("id_ed25519.pub");

            // Should return the Ed25519 key (trimmed)
            expect(features.sshPublicKey).toBe("ssh-ed25519 AAAAC3... user@host");
        });
    });

    describe("Git email fallback behavior", () => {
        it("should return git email based on mock setup", async () => {
            // The global mock is set up, but let's verify it's being called
            const childProcess = require("child_process");

            // Check if exec was called
            const features = await createFeatures(createClientData(mockContext));

            // The mock should have been called
            expect(childProcess.exec).toHaveBeenCalled();

            // Git email should be from the mock (or empty if mock not working)
            // The actual behavior depends on whether the mock is properly intercepting the call
            expect(typeof features.gitUserEmail).toBe("string");
        });

        it("should return empty string when no workspace folder exists", async () => {
            // Temporarily remove workspace folders
            const originalWorkspaceFolders = workspace.workspaceFolders;
            workspace.workspaceFolders = undefined as any;

            try {
                const features = await createFeatures(createClientData(mockContext));
                expect(features.gitUserEmail).toBe("");
            } finally {
                workspace.workspaceFolders = originalWorkspaceFolders;
            }
        });
    });

    describe("Checksum versioning", () => {
        it("should include version prefix in checksum", async () => {
            const features = await createFeatures(createClientData(mockContext));
            const vector = features.toVector();

            // Should start with version prefix
            expect(vector[FeatureVectorKey.checksum]).toMatch(/^v1#/);

            // The version allows for future algorithm changes
            const [version, hash] = vector[FeatureVectorKey.checksum].split("#");
            expect(version).toBe("v1");
            expect(hash).toMatch(/^[a-f0-9]{64}$/);
        });
    });

    describe("Security properties", () => {
        it("should hash all sensitive data and not store plaintext", async () => {
            // Set up features with known sensitive values
            const childProcess = require("child_process");
            childProcess.exec = jest.fn((cmd: string, options: any, callback: any) => {
                const cb = callback || options;
                if (cmd.includes("git config user.email")) {
                    cb(null, { stdout: "<EMAIL>\n" });
                } else {
                    cb(new Error("Command not found"));
                }
            });

            const fs = require("fs/promises");
            fs.readFile = jest.fn((path: string) => {
                if (path.includes("id_rsa.pub")) {
                    return Promise.resolve("ssh-rsa AAAAB3NzaC1yc2EA... sensitive@host");
                }
                return Promise.reject(new Error("No such file"));
            });

            const features = await createFeatures(createClientData(mockContext));
            const vector = features.toVector();

            // Convert vector to string to search for sensitive data
            const vectorString = JSON.stringify(vector);

            // Sensitive data should not appear in plaintext
            expect(vectorString).not.toContain("<EMAIL>");
            expect(vectorString).not.toContain("sensitive@host");
            expect(vectorString).not.toContain("ssh-rsa AAAAB3NzaC1yc2EA");

            // All values should be hashed (64-char hex strings)
            Object.entries(vector).forEach(([key, value]) => {
                if (key !== String(FeatureVectorKey.checksum)) {
                    expect(value).toMatch(/^[a-f0-9]{64}$/);
                }
            });
        });
    });
});

import { FeatureFlagManager } from "$vscode/src/feature-flags";
import { AugmentGlobalState } from "$vscode/src/utils/context";
import { GitCommitIndexer } from "$vscode/src/vcs/git-commit-indexer";

import { MockClientFeatureFlags } from "@augment-internal/sidecar-libs/src/__tests__/mocks/mock-client-feature-flags";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
    resetLibraryClientFeatureFlags,
    setLibraryClientFeatureFlags,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import * as vscode from "vscode";

import { APIServer } from "../../augment-api";
import { LocalToolHost } from "../../chat/tools-local-host";
import { WorkspaceManager } from "../../workspace/workspace-manager";

describe("LocalToolHost", () => {
    let mockFeatureFlags: MockClientFeatureFlags;

    beforeEach(() => {
        mockFeatureFlags = new MockClientFeatureFlags();
        setLibraryClientFeatureFlags(mockFeatureFlags);
    });

    afterEach(() => {
        resetLibraryClientFeatureFlags();
    });

    it("all agent tools are available in agent mode when enabled", async () => {
        const host = new LocalToolHost(
            ChatMode.agent,
            {} as WorkspaceManager,
            {} as APIServer,
            {} as AggregateCheckpointManager,
            {} as FeatureFlagManager,
            {} as vscode.Uri,
            {
                onDidChangeFileStorage: () => ({
                    dispose: jest.fn(),
                }),
                load: jest.fn().mockResolvedValue(undefined),
            } as any as AugmentGlobalState,
            0,
            {} as GitCommitIndexer
        );
        const tools = await host.getToolDefinitions();

        expect(tools.length).toBeGreaterThan(3);
        expect(tools.some((tool) => tool.definition.name === "edit-file")).toBe(true);
    });

    it("chat mode has limited set of tools when enabled", async () => {
        const host = new LocalToolHost(
            ChatMode.chat,
            {} as WorkspaceManager,
            {} as APIServer,
            {} as AggregateCheckpointManager,
            {} as FeatureFlagManager,
            {} as vscode.Uri,
            {} as AugmentGlobalState,
            0,
            {} as GitCommitIndexer
        );
        const tools = await host.getToolDefinitions();

        expect(tools.length).toBe(1);
        expect(tools.some((tool) => tool.definition.name === "read-file")).toBe(true);
        expect(tools.some((tool) => tool.definition.name === "edit-file")).toBe(false);
    });
});

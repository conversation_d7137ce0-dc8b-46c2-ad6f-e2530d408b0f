/* eslint-disable @typescript-eslint/no-base-to-string */

/* eslint-disable @typescript-eslint/naming-convention */
import { setLibraryClientAuth } from "@augment-internal/sidecar-libs/src/client-interfaces/client-auth";
import { setLibraryClientConfig } from "@augment-internal/sidecar-libs/src/client-interfaces/client-config";
import { APIError } from "@augment-internal/sidecar-libs/src/exceptions";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";
import { APIStatus } from "@augment-internal/sidecar-libs/src/utils/types";
import { afterEach, expect, test } from "@jest/globals";
import fetchMock from "fetch-mock";

import { generateMockWorkspaceConfig } from "../__mocks__/mock-augment-config";
import { mockGetModelsResult } from "../__mocks__/mock-modelinfo";
import { ExtensionContext, resetMockWorkspace, workspace } from "../__mocks__/vscode-mocks";
import {
    APIServer,
    APIServerImplWithErrorReporting,
    BackModelInfo,
    ClientCompletionTimline,
    ClientMetric,
    Model,
} from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";
import { ClientAuth } from "../client-interfaces/client-auth";
import { ClientConfig } from "../client-interfaces/client-config";

const fetchSandbox = fetchMock.sandbox();

class AugmentAPItestKit {
    readonly configListener: AugmentConfigListener;
    readonly auth: AuthSessionStore;

    constructor() {
        this.configListener = new AugmentConfigListener();
        this.auth = new AuthSessionStore(new ExtensionContext(), this.configListener);

        setLibraryClientConfig(new ClientConfig(this.configListener));
        setLibraryClientAuth(new ClientAuth(this.auth, this.configListener));
    }

    public createAPIServer(): APIServer {
        const apiServer = new APIServerImplWithErrorReporting(
            this.configListener,
            this.auth,
            "placeholder-session-id",
            "augment-extension-test/0.0.0",
            fetchSandbox
        );
        return apiServer;
    }

    // testCompletionAPIError verifies the behavior of a malformed response to a
    // "complete" request. `result` is the (malformed) result.
    public async testCompletionAPIError(result: any) {
        const server = this.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", result);

        const requestId = server.createRequestId();
        await expect(async () =>
            server.complete(
                requestId,
                "def quicksort(",
                "",
                "quicksort.py",
                "quickSortBlobName",
                { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
                "python",
                { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
                []
            )
        ).rejects.toThrowError();

        fetchSandbox.reset();
    }

    // testCompletionAPIError verifies the behavior of a malformed response to a
    // "memorize" request. `result` is the (malformed) result.
    public async testMemorizeAPIError(result: any) {
        const server = this.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/memorize", result);
        await expect(async () =>
            server.memorize("quicksort.py", "def quicksort(", "blob-name", [])
        ).rejects.toThrowError();

        fetchSandbox.reset();
    }

    // testCompletionAPIError verifies the behavior of a malformed response to a
    // "find-missing" request. `result` is the (malformed) result.
    public async testFindMissingAPIError(result: any) {
        const server = this.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/find-missing", result);
        await expect(async () => server.findMissing(["a", "b"])).rejects.toThrowError();

        fetchSandbox.reset();
    }

    // testCompletionAPIError verifies the behavior of a malformed response to a
    // "get-models" request. `result` is the (malformed) result.
    public async testGetModelsAPIError(result: any) {
        const server = this.createAPIServer();
        fetchSandbox.post("http://api.augmentcode.com/get-models", result);

        await expect(async () => server.getModelConfig()).rejects.toThrowError();

        fetchSandbox.reset();
    }

    // testAgentEditFileCancellation verifies the behavior of a cancelled response to a
    // "agents/edit-file" request. `result` is the (okay) result.
    public async testAgentEditFileCancellation(result: any) {
        const server = this.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/agents/edit-file", result);

        const abortController = new AbortController();
        const abortSignal = abortController.signal;
        abortController.abort();
        await expect(async () =>
            server.agentEditFile(
                server.createRequestId(),
                "quicksort.py",
                "summary",
                "detailed description",
                "def quicksort(",
                abortSignal
            )
        ).rejects.toThrowError();

        fetchSandbox.reset();
    }

    // verifyModelInfo veries that the `received` ModelInfo matches the `expected`
    // BackModelInfo.
    public verifyModelInfo(received: Model, expected: BackModelInfo) {
        expect(received.suggestedPrefixCharCount).toBe(expected.suggested_prefix_char_count);
        expect(received.suggestedSuffixCharCount).toBe(expected.suggested_suffix_char_count);
    }
}

describe("augment-api", () => {
    beforeEach(() => {
        // Prevent open handle leaks
        jest.useFakeTimers();
        resetMockWorkspace();
    });

    // reset the sandbox after each test.
    afterEach(() => {
        jest.useRealTimers();
        fetchSandbox.reset();
        resetMockWorkspace();
    });

    test("completion-single", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const completionText = "hello";
        const unknownBlobNames = ["a", "b", "c"];
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", {
            completions: [completionText],
            unknown_blob_names: unknownBlobNames,
        });

        const requestId = server.createRequestId();
        const result = await server.complete(
            requestId,
            "def quicksort(",
            "",
            "quicksort.py",
            "quickSortBlobName",
            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
            "python",
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
            []
        );
        expect(result.completionItems.length).toBe(1);
        expect(result.completionItems[0].text).toBe(completionText);
        expect(result.unknownBlobNames.length).toBe(3);
        for (let idx = 0; idx < unknownBlobNames.length; idx++) {
            expect(result.unknownBlobNames[idx]).toBe(unknownBlobNames[idx]);
        }

        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("prompt", "def quicksort(");
        expect(body).toHaveProperty("suffix", "");
        expect(body).toHaveProperty("path", "quicksort.py");
    });

    test("completion-multiple", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const completions = ["hello", "goodbye", "later, dude"];
        const unknownBlobNames = ["a", "b", "c"];
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", {
            completions,
            unknown_blob_names: unknownBlobNames,
        });

        const requestId = server.createRequestId();
        const result = await server.complete(
            requestId,
            "def quicksort(",
            "",
            "quicksort.py",
            "quickSortBlobName",
            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
            "python",
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
            []
        );
        expect(result.completionItems.length).toBe(completions.length);
        for (let idx = 0; idx < completions.length; idx++) {
            expect(result.completionItems[idx].text).toBe(completions[idx]);
        }
        expect(result.unknownBlobNames.length).toBe(3);
        for (let idx = 0; idx < unknownBlobNames.length; idx++) {
            expect(result.unknownBlobNames[idx]).toBe(unknownBlobNames[idx]);
        }
    });

    test("completion-api-error", async () => {
        const kit = new AugmentAPItestKit();
        await kit.testCompletionAPIError({ completions: ["hello"] });
        await kit.testCompletionAPIError({
            completions: "should be array, not scalar",
            unknown_blob_names: [],
        });
        await kit.testCompletionAPIError({ completions: ["hello"], unknownBlobNames: [1, 2, 3] });
        await kit.testCompletionAPIError({ completions: ["hello"], some_other_field: [] });
    });

    test("memorize", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/memorize", { blob_name: "a" });

        const result = await server.memorize("quicksort.py", "def quicksort(", "blob-name", []);
        expect(result.blobName).toBe("a");

        const call = fetchSandbox.lastCall()!;
        expect(call[1]?.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]?.body!.toString()!);
        expect(body).toHaveProperty("t", "def quicksort(");
        expect(body).toHaveProperty("path", "quicksort.py");
    });

    test("memorize-backward-compat", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/memorize", {
            mem_object_name: "a",
        });

        const result = await server.memorize("quicksort.py", "def quicksort(", "blob-name", []);
        expect(result.blobName).toBe("a");
    });

    test("memorize-api-error", async () => {
        const kit = new AugmentAPItestKit();
        await kit.testMemorizeAPIError({ bogus: "bogus field" });
        await kit.testMemorizeAPIError({ blob_name: 17 });
    });

    test("find-missing", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const unknownBlobNames = ["a"];
        fetchSandbox.postOnce("http://api.augmentcode.com/find-missing", {
            unknown_memory_names: unknownBlobNames,
            nonindexed_blob_names: [],
        });

        const result = await server.findMissing(["a", "b"]);
        expect(result.unknownBlobNames).toStrictEqual(unknownBlobNames);

        const call = fetchSandbox.lastCall()!;
        expect(call[1]?.headers).toHaveProperty("x-request-id");
    });

    test("find-missing-no-result", async () => {
        const kit = new AugmentAPItestKit();
        await kit.testFindMissingAPIError({ unknown_blob_names: "not an array" });
        await kit.testFindMissingAPIError({ unknown_memory_names: "not an array" });
        await kit.testFindMissingAPIError({ bogus: "bogus field" });
    });

    test("get-models", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.post("http://api.augmentcode.com/get-models", mockGetModelsResult);

        const modelConfig = await server.getModelConfig();
        expect(mockGetModelsResult.models.map((m) => m.name).sort()).toStrictEqual(
            modelConfig.models.map((m) => m.name).sort()
        );

        const call = fetchSandbox.lastCall()!;
        expect(call[1]?.headers).toHaveProperty("x-request-id");
    });

    test("get-models-backwards-compat", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const mockResult = {
            default_model: "abc",
            models: [
                {
                    name: "abc",
                    suggested_prefix_char_count: 123,
                    suggested_suffix_char_count: 456,
                },
            ],
            languages: [],
        };
        fetchSandbox.post("http://api.augmentcode.com/get-models", mockResult);

        const modelConfig = await server.getModelConfig();
        expect(modelConfig.models.map((m) => m.name)).toStrictEqual(
            mockResult.models.map((m) => m.name)
        );

        for (const mockModelInfo of mockResult.models) {
            const result = modelConfig.models.find((m) => m.name === mockModelInfo.name);
            expect(result).toBeDefined();
            kit.verifyModelInfo(result!, mockModelInfo);
        }

        const call = fetchSandbox.lastCall()!;
        expect(call[1]?.headers).toHaveProperty("x-request-id");
    });

    test("get-models-api-error", async () => {
        const kit = new AugmentAPItestKit();

        let badResult = {
            default_model: "abc",
            models: [
                {
                    name: "abc",
                    suggested_prefix_char_count: "should be number, not string",
                    suggested_suffix_char_count: 456,
                },
            ],
            languages: [],
        };
        await kit.testGetModelsAPIError(badResult);
    });

    describe("elo configuration", () => {
        let kit: AugmentAPItestKit;
        let server: APIServer;

        beforeEach(() => {
            fetchSandbox.reset();
            fetchSandbox.restore();
            kit = new AugmentAPItestKit();
            server = kit.createAPIServer();
        });

        afterEach(() => {
            fetchSandbox.reset();
            fetchSandbox.restore();
        });

        test("missing configuration", async () => {
            const noConfig = {
                default_model: "abc",
                models: [],
                languages: [],
                feature_flags: {},
            };
            fetchSandbox.postOnce("http://api.augmentcode.com/get-models", noConfig);
            const modelConfig = await server.getModelConfig();
            expect(modelConfig.featureFlags.eloModelConfiguration).toEqual({
                highPriorityModels: [],
                regularBattleModels: [],
                highPriorityThreshold: 0.5,
            });
        });

        test("invalid configuration", async () => {
            const noConfig = {
                default_model: "abc",
                models: [],
                languages: [],
                feature_flags: {
                    elo_model_configuration: "invalid json",
                },
            };
            fetchSandbox.postOnce("http://api.augmentcode.com/get-models", noConfig);
            const modelConfig = await server.getModelConfig();
            expect(modelConfig.featureFlags.eloModelConfiguration).toEqual({
                highPriorityModels: [],
                regularBattleModels: [],
                highPriorityThreshold: 0.5,
            });
        });

        test("valid configuration", async () => {
            const validConfig = {
                default_model: "abc",
                models: [],
                languages: [],
                feature_flags: {
                    elo_model_configuration: JSON.stringify({
                        highPriorityModels: [
                            ["model1", "model2"],
                            ["model3", "model4"],
                        ],
                        regularBattleModels: ["model3", "model4"],
                        highPriorityThreshold: 0.75,
                    }),
                },
            };
            fetchSandbox.postOnce("http://api.augmentcode.com/get-models", validConfig);
            const modelConfig = await server.getModelConfig();
            expect(modelConfig.featureFlags.eloModelConfiguration).toEqual({
                highPriorityModels: [
                    ["model1", "model2"],
                    ["model3", "model4"],
                ],
                regularBattleModels: ["model3", "model4"],
                highPriorityThreshold: 0.75,
            });
        });
    });

    test("invalid-completion-url", async () => {
        const config = generateMockWorkspaceConfig({
            advanced: {
                completionURL: "foo:abc/def/",
            },
        });
        workspace.getConfiguration.mockReturnValueOnce(config);

        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();

        await expect(server.memorize("/example/path/file.txt", "", "", [])).rejects.toThrowError(
            "The completion URL setting is invalid"
        );
    });

    test("non-http-completion-url", async () => {
        const config = generateMockWorkspaceConfig({
            advanced: {
                completionURL: "foo://",
            },
        });
        workspace.getConfiguration.mockReturnValueOnce(config);

        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();

        await expect(server.memorize("/example/path/file.txt", "", "", [])).rejects.toThrowError(
            "Augment API URL must start with 'http://' or 'https://'"
        );
    });

    test("no-completion-url", async () => {
        const config = generateMockWorkspaceConfig({
            advanced: {
                completionURL: "",
            },
        });
        workspace.getConfiguration.mockReturnValueOnce(config);

        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();

        await expect(server.memorize("/example/path/file.txt", "", "", [])).rejects.toThrowError(
            "Please configure Augment API URL"
        );
    });

    test("handle-trailing-slashes", async () => {
        const config = generateMockWorkspaceConfig({
            advanced: {
                completionURL: "http://api.augmentcode.com/",
            },
        });
        workspace.getConfiguration.mockReturnValueOnce(config);

        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/memorize", {
            mem_object_name: "a",
        });

        const result = await server.memorize("quicksort.py", "def quicksort(", "blob-name", []);
        expect(result.blobName).toBe("a");
    });

    test("response-not-json", async () => {
        const kit = new AugmentAPItestKit();
        await kit.testCompletionAPIError("this is a not a json document");
    });

    test("blobs-sorted", async () => {
        // setup
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const unsortedBlobs = {
            checkpointId: undefined,
            addedBlobs: ["d", "c", "b", "a"],
            deletedBlobs: ["4", "2", "1"],
        };

        // completions
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", {
            completions: ["hello"],
            unknown_blob_names: [],
        });
        await server.complete(
            server.createRequestId(),
            "def quicksort(",
            "",
            "quicksort.py",
            "quickSortBlobName",
            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
            "python",
            unsortedBlobs,
            []
        );
        let call = fetchSandbox.lastCall()!;
        let body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("blobs", {
            checkpoint_id: null,
            added_blobs: ["a", "b", "c", "d"],
            deleted_blobs: ["1", "2", "4"],
        });

        // edits
        fetchSandbox.postOnce("http://api.augmentcode.com/edit", {
            modified_code: "hello",
            checkpoint_not_found: false,
        });
        await server.editCode(
            server.createRequestId(),
            "write something cool",
            "def quicksort(",
            "",
            "",
            "quicksort.py",
            "quickSortBlobName",
            0,
            0,
            "python",
            unsortedBlobs
        );
        call = fetchSandbox.lastCall()!;
        body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("blobs", {
            checkpoint_id: null,
            added_blobs: ["a", "b", "c", "d"],
            deleted_blobs: ["1", "2", "4"],
        });

        // checkpoints
        fetchSandbox.postOnce("http://api.augmentcode.com/checkpoint-blobs", {
            new_checkpoint_id: "1234",
        });
        await server.checkpointBlobs(unsortedBlobs);
        call = fetchSandbox.lastCall()!;
        body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("blobs", {
            checkpoint_id: null,
            added_blobs: ["a", "b", "c", "d"],
            deleted_blobs: ["1", "2", "4"],
        });
    });
    test("standalone report-error", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/report-error", {});

        const result = await server.reportError(
            "1234567890",
            "my error message",
            // Test stacktrace sanitization with a realistic example
            `Error: HTTP error: 400 Bad Request
            at Ee.fromResponse (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:50:29730)
            at Rc.callApi (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:52:4702)
            at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
            at async Rc.callApi (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:52:17217)
            at async Rc.complete (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:52:6933)
            at async Yc.complete (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:105:1500)
            at async Ko.getCompletion (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:143:56987)
            at async Zc._requestCompletion (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:121:719)
            at async Zc.generateCompletion (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:120:862)
            at async nd._getCompletions (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:121:12539)
            at async nd.provideInlineCompletionItems (/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.117.1/out/extension.js:121:10947)
            at async B.provideInlineCompletions (/Applications/Visual Studio Code.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js:153:108947)`,
            [{ key: "source", value: "unit test" }]
        );
        expect(result).toEqual({});
        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("original_request_id", "1234567890");
        expect(body).toHaveProperty("sanitized_message", "my error message");
        expect(body).toHaveProperty(
            "stack_trace",
            `Error: HTTP error: 400 Bad Request
            at Ee.fromResponse
            at Rc.callApi
            at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
            at async Rc.callApi
            at async Rc.complete
            at async Yc.complete
            at async Ko.getCompletion
            at async Zc._requestCompletion
            at async Zc.generateCompletion
            at async nd._getCompletions
            at async nd.provideInlineCompletionItems
            at async B.provideInlineCompletions`
        );
        expect(body).toHaveProperty("diagnostics", [{ key: "source", value: "unit test" }]);
    });
    test("report-error called on completion error", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/report-error", {});
        fetchSandbox.postOnce(
            "http://api.augmentcode.com/completion",
            Promise.reject(Error("my completion error"))
        );

        const requestId = server.createRequestId();
        await expect(
            server.complete(
                requestId,
                "",
                "",
                "",
                "",
                { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
                "",
                { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
                []
            )
        ).rejects.toThrowError();
        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("original_request_id", requestId);
        expect(body).toHaveProperty(
            "sanitized_message",
            "completion call failed with APIStatus unavailable"
        );
        expect(body["diagnostics"]).toContainEqual({
            key: "message",
            value: "my completion error",
        });
    });

    test("client-metrics", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const metrics: Array<ClientMetric> = [
            {
                client_metric: "example-metric",
                value: 123,
            },
        ];
        fetchSandbox.postOnce("http://api.augmentcode.com/client-metrics", {
            metrics,
        });

        await server.clientMetrics(metrics);

        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("metrics", metrics);
    });

    test("client-completion-timelines", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();

        fetchSandbox.postOnce("http://api.augmentcode.com/client-completion-timelines", {});

        const now = Date.now();
        const [timeSec, timeNsec] = msecToTimestamp(now);

        const requestId1 = server.createRequestId();
        const requestId2 = server.createRequestId();
        const timelines: Array<ClientCompletionTimline> = [
            {
                request_id: requestId1,
                initial_request_time_sec: timeSec,
                initial_request_time_nsec: timeNsec,
                api_start_time_sec: timeSec,
                api_start_time_nsec: timeNsec,
                api_end_time_sec: timeSec,
                api_end_time_nsec: timeNsec,
                emit_time_sec: timeSec,
                emit_time_nsec: timeNsec,
            },
            {
                request_id: requestId2,
                initial_request_time_sec: timeSec,
                initial_request_time_nsec: timeNsec,
                api_start_time_sec: timeSec,
                api_start_time_nsec: timeNsec,
                api_end_time_sec: timeSec,
                api_end_time_nsec: timeNsec,
                emit_time_sec: timeSec,
                emit_time_nsec: timeNsec,
            },
        ];

        await server.reportClientCompletionTimelines(timelines);

        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("timelines", timelines);
    });

    test("map error 499 to APIStatus.cancelled", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/batch-upload", { status: 499 });

        try {
            await server.batchUpload([
                {
                    pathName: "quicksort.py",
                    text: "def quicksort(",
                    blobName: "blob-name",
                    metadata: [],
                },
            ]);
            throw new Error("batchUpload should have thrown");
        } catch (e) {
            expect(APIError.isAPIErrorWithStatus(e, APIStatus.cancelled)).toBe(true);
        }
    });

    test("edit file agent cancellation", async () => {
        const kit = new AugmentAPItestKit();
        await kit.testAgentEditFileCancellation({ status: 200, headers: { "content-length": 0 } });
    });

    test("memorize does not replace lone surrogates", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();

        fetchSandbox.postOnce("http://api.augmentcode.com/memorize", { blob_name: "a" });

        const stringWithLoneSurrogate = "😀Hello\uD800World😀".slice(1, -1);
        await server.memorize("test.txt", stringWithLoneSurrogate, "blob-name", []);

        const call = fetchSandbox.lastCall()!;
        const body = call[1]?.body!.toString();

        expect(body).toContain('"\\ude00Hello\\ud800World\\ud83d"');
        expect(() => JSON.parse(body!)).not.toThrow();
    });

    test("safeJsonStringify replaces and removes lone surrogates for completions", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const completionText = "hello";
        const unknownBlobNames = ["a", "b", "c"];
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", {
            completions: [completionText],
            unknown_blob_names: unknownBlobNames,
        });

        const requestId = server.createRequestId();
        const result = await server.complete(
            requestId,
            "def quicksort(",
            "😀Hello\uD800World😀".slice(1, -1),
            "quicksort.py",
            "quickSortBlobName",
            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
            "python",
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
            []
        );
        expect(result.completionItems.length).toBe(1);
        expect(result.completionItems[0].text).toBe(completionText);
        expect(result.unknownBlobNames.length).toBe(3);
        for (let idx = 0; idx < unknownBlobNames.length; idx++) {
            expect(result.unknownBlobNames[idx]).toBe(unknownBlobNames[idx]);
        }

        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        expect(call[1]!.body!.toString()).toContain('"Hello\uFFFDWorld"');
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("prompt", "def quicksort(");
        expect(body).toHaveProperty("suffix", "Hello\uFFFDWorld");
        expect(body).toHaveProperty("path", "quicksort.py");
    });
});

import { WorkTimer } from "$vscode/src/metrics/work-timer";

import { joinPath } from "@augment-internal/sidecar-libs/src/utils/path-utils";
import * as vscode from "vscode";

import { ChatModelTestKit } from "../../__mocks__/chat/chat-model-test-kit";
import { mockFSUtils } from "../../__mocks__/fs-utils";
import { resetMockWorkspace } from "../../__mocks__/vscode-mocks";
import { AugmentConfigListener } from "../../augment-config-listener";
import { GuidelinesWatcher } from "../../chat/guidelines-watcher";
import { AssetManager } from "../../client-interfaces/asset-manager";
import { DerivedState } from "../../main-panel/action-cards/actions-model";
import { ChatApp } from "../../main-panel/apps/chat-webview-app";
import { AugmentGlobalState } from "../../utils/context";
import { WebViewMessageType } from "../../webview-providers/webview-messages";
// Use WorkspaceManagerTestKit
import {
    awaitStartup,
    createWorkspaceFiles,
    MockSourceFolder,
    MockSourceFolderState,
    MockSourceFolderType,
    WorkspaceManagerTestKit,
    writeFileInFolder,
} from "../workspace/workspace-manager-test-kit";

// File tree structure:
// /src
// ├── repoRoot1
// │   └── nestedFolderRoot2
// └── repoRoot3

const workspaceFolder1: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/repoRoot1"),
    repoRootUri: vscode.Uri.file("/src/repoRoot1"),
    files: new Map(),
    ignoredFiles: new Map(),
};

const workspaceFolder2: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/repoRoot1/nestedFolderRoot2"),
    repoRootUri: vscode.Uri.file("/src/repoRoot1"),
    files: new Map(),
    ignoredFiles: new Map(),
};

const workspaceFolder3: MockSourceFolder = {
    sourceFolderType: MockSourceFolderType.workspaceFolder,
    folderRootUri: vscode.Uri.file("/src/repoRoot3"),
    repoRootUri: vscode.Uri.file("/src/repoRoot3"),
    files: new Map(),
    ignoredFiles: new Map(),
};

class ChatAppTestKit {
    private _chatModelTestKit: ChatModelTestKit;
    private _chatApp: ChatApp;
    private readonly _config: AugmentConfigListener;
    private _unusedAny: any = jest.fn();
    private _guidelinesWatcher: GuidelinesWatcher;
    private _mockToolsModel: any;

    constructor(public wsManagerTestKit: WorkspaceManagerTestKit) {
        this._chatModelTestKit = new ChatModelTestKit(wsManagerTestKit);
        this._config = new AugmentConfigListener();
        this._guidelinesWatcher = new GuidelinesWatcher(
            this._config,
            this._unusedAny,
            this._unusedAny
        );
        // Add mockAssetManager
        const mockAssetManager = new AssetManager(this._unusedAny);

        // Create a mock AugmentGlobalState
        const mockGlobalState = {
            get: jest.fn().mockImplementation((_key, defaultValue) => defaultValue),
            update: jest.fn().mockResolvedValue(undefined),
        } as unknown as AugmentGlobalState;

        // Create a mock ToolsModel with the required methods
        this._mockToolsModel = {
            onRestartHosts: jest.fn().mockImplementation((_listener) => ({
                dispose: jest.fn(),
            })),
            getSidecarToolHost: jest.fn().mockReturnValue(undefined),
            memoriesAbsPath: undefined,
        };

        this._chatApp = new ChatApp(
            this._chatModelTestKit.chatModel,
            new Map(),
            this._chatModelTestKit.mockApiServer,
            this._chatModelTestKit.wsManager,
            this._unusedAny,
            this._config,
            vscode.Uri.file("extensionUri"),
            this._unusedAny,
            this._unusedAny,
            {
                onDerivedStatesSatisfied: new vscode.EventEmitter<DerivedState[]>().event,
            } as any /* actionsModel */,
            {
                onDidChangeSyncingEnabled: new vscode.EventEmitter<boolean>().event,
            } as any /* syncingEnabledTracker */,
            this._unusedAny,
            this._unusedAny,
            this._unusedAny,
            this._chatModelTestKit.fuzzyFsSearcher,
            this._chatModelTestKit.fuzzySymbolSearcher,
            this._mockToolsModel /* toolsModel */,
            this._unusedAny /* resolveFileService */,
            {
                onAgentEditListHasUpdates: () => ({
                    dispose: jest.fn(),
                }),
            } as any /* agentCheckpointManager */,
            this._guidelinesWatcher,
            mockAssetManager, // Add AssetManager parameter
            mockGlobalState /* globalState */,
            new WorkTimer(),
            {} as any /* toolConfigStore */,
            this._unusedAny
        );
    }

    get chatModelTestKit(): ChatModelTestKit {
        return this._chatModelTestKit;
    }

    get chatApp(): ChatApp {
        return this._chatApp;
    }

    dispose() {
        this._chatModelTestKit.dispose();
        this._config.dispose();
        this._guidelinesWatcher.dispose();
    }
}

describe("ChatApp", () => {
    let wsManagerTestKit: WorkspaceManagerTestKit;
    let testKit: ChatAppTestKit;

    describe("resolveFilePath single workspace", () => {
        beforeEach(async () => {
            jest.useFakeTimers();
            resetMockWorkspace();
            mockFSUtils.reset();

            const workspaceState: MockSourceFolderState[] = [
                { ...workspaceFolder1, tracked: true },
            ];

            createWorkspaceFiles(workspaceState);
            wsManagerTestKit = new WorkspaceManagerTestKit();
            wsManagerTestKit.initWorkspace(workspaceState);

            testKit = new ChatAppTestKit(wsManagerTestKit);
            await awaitStartup(testKit.chatModelTestKit.wsManager, workspaceState);
        });

        afterEach(() => {
            testKit.dispose();
            jest.useRealTimers();
        });

        test("only filename with extension, exact match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath("abc123-file.py");
            expect(result?.repoRoot).toBe("/src/repoRoot1");
            expect(result?.pathName).toBe("example/path/to/abc123-dir/abc123-file.py");
        });

        test("only filename with extension, fuzzy match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath("abc123-file.py", undefined, false);
            expect(result?.repoRoot).toBe("/src/repoRoot1");
            expect(result?.pathName).toBe("example/path/to/abc123-dir/abc123-file.py");
        });

        test("only filename without extension, exact match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath("abc123-file");
            expect(result).toBeUndefined();
        });

        test("only filename without extension, fuzzy match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath("abc123-file", undefined, false);
            expect(result?.repoRoot).toBe("/src/repoRoot1");
            expect(result?.pathName).toBe("example/path/to/abc123-dir/abc123-file.py");
        });

        test("filename with extension and partial path, exact match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file.py");
            expect(result?.repoRoot).toBe("/src/repoRoot1");
            expect(result?.pathName).toBe("example/path/to/abc123-dir/abc123-file.py");
        });

        test("filename with extension and partial path, fuzzy match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath(
                "abc123-dir/abc123-file.py",
                undefined,
                false
            );
            expect(result?.repoRoot).toBe("/src/repoRoot1");
            expect(result?.pathName).toBe("example/path/to/abc123-dir/abc123-file.py");
        });

        test("filename without extension and partial path, exact match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file");
            expect(result).toBeUndefined();
        });

        test("filename without extension and partial path, fuzzy match", async () => {
            const chatApp = testKit.chatApp;
            writeFileInFolder(
                workspaceFolder1.folderRootUri.fsPath,
                "example/path/to/abc123-dir/abc123-file.py",
                "content of abc123-file"
            );
            await jest.advanceTimersByTimeAsync(10000);
            const result = await chatApp.resolveTargetPath(
                "abc123-dir/abc123-file",
                undefined,
                false
            );
            expect(result?.repoRoot).toBe("/src/repoRoot1");
            expect(result?.pathName).toBe("example/path/to/abc123-dir/abc123-file.py");
        });
    });

    describe("resolveFilePath multiple workspaces, siblings", () => {
        describe("same repaths resolves first workspace", () => {
            let chatApp: ChatApp;
            const relFilePath = "example/path/to/abc123-dir/abc123-file.py";
            const relFileContents = "content of abc123-file";

            beforeEach(async () => {
                jest.useFakeTimers();
                resetMockWorkspace();
                mockFSUtils.reset();

                const workspaceState: MockSourceFolderState[] = [
                    {
                        ...workspaceFolder1,
                        files: new Map<string, string>([[relFilePath, relFileContents]]),
                        tracked: true,
                    },
                    {
                        ...workspaceFolder3,
                        files: new Map<string, string>([[relFilePath, relFileContents]]),
                        tracked: true,
                    },
                ];

                createWorkspaceFiles(workspaceState);
                wsManagerTestKit = new WorkspaceManagerTestKit();
                wsManagerTestKit.initWorkspace(workspaceState);

                testKit = new ChatAppTestKit(wsManagerTestKit);
                await awaitStartup(testKit.chatModelTestKit.wsManager, workspaceState);

                // Do this again, to trigger the event listeners to propagate info
                // over to the chat system
                createWorkspaceFiles(workspaceState);
                await jest.advanceTimersByTimeAsync(10000);
                chatApp = testKit.chatApp;
            });

            afterEach(() => {
                testKit.dispose();
                jest.useRealTimers();
            });

            test("filename only, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("filename only, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("filename only, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file");
                expect(result).toBeUndefined();
            });

            test("filename only, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("partial path, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("partial path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file.py",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("partial path, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file");
                expect(result).toBeUndefined();
            });

            test("partial path, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("full path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("full path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath, undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("absolute path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath)
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("absolute path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath),
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });
        });

        describe("different relpaths, same filename", () => {
            let chatApp: ChatApp;
            const relFilePath1 = "example/path/to/abc123-dir/abc123-file.py";
            const relFilePath2 = "short/path/abc123-file.py";
            const relFileContents = "content of abc123-file";

            beforeEach(async () => {
                jest.useFakeTimers();
                resetMockWorkspace();
                mockFSUtils.reset();

                const workspaceState: MockSourceFolderState[] = [
                    {
                        ...workspaceFolder1,
                        files: new Map<string, string>([[relFilePath1, relFileContents]]),
                        tracked: true,
                    },
                    {
                        ...workspaceFolder3,
                        files: new Map<string, string>([[relFilePath2, relFileContents]]),
                        tracked: true,
                    },
                ];

                createWorkspaceFiles(workspaceState);
                wsManagerTestKit = new WorkspaceManagerTestKit();
                wsManagerTestKit.initWorkspace(workspaceState);

                testKit = new ChatAppTestKit(wsManagerTestKit);
                await awaitStartup(testKit.chatModelTestKit.wsManager, workspaceState);

                // Do this again, to trigger the event listeners to propagate info
                // over to the chat system
                createWorkspaceFiles(workspaceState);
                await jest.advanceTimersByTimeAsync(10000);
                chatApp = testKit.chatApp;
            });

            afterEach(() => {
                testKit.dispose();
                jest.useRealTimers();
            });

            test("resolves second workspace, filename only, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py", undefined, true);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, filename only, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file");
                expect(result).toBeUndefined();
            });

            test("resolves first workspace, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, partial path, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves first workspace, partial path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file.py",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, partial path, exact match", async () => {
                const result = await chatApp.resolveTargetPath("short/path/abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, partial path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "short/path/abc123-file.py",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, partial path, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file");
                expect(result).toBeUndefined();
            });

            test("resolves first workspace, partial path, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, partial path, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("short/path/abc123-file");
                expect(result).toBeUndefined();
            });

            test("resolves second workspace, partial path, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "short/path/abc123-file",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, full path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath1);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves first workspace, full path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath1, undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, full path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath2);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, full path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath2, undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, absolute path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath1)
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves first workspace, absolute path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath1),
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, absolute path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder3.folderRootUri.fsPath, relFilePath2)
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, absolute path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder3.folderRootUri.fsPath, relFilePath2),
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });
        });

        describe("different relpaths, different filename", () => {
            let chatApp: ChatApp;
            const relFilePath1 = "example/path/to/abc123-dir/abc123-file.py";
            const relFilePath2 = "short/path/some-other-file.py";
            const relFileContents1 = "content of abc123-file";
            const relFileContents2 = "content of some-other-file";

            beforeEach(async () => {
                jest.useFakeTimers();
                resetMockWorkspace();
                mockFSUtils.reset();

                const workspaceState: MockSourceFolderState[] = [
                    {
                        ...workspaceFolder1,
                        files: new Map<string, string>([[relFilePath1, relFileContents1]]),
                        tracked: true,
                    },
                    {
                        ...workspaceFolder3,
                        files: new Map<string, string>([[relFilePath2, relFileContents2]]),
                        tracked: true,
                    },
                ];

                createWorkspaceFiles(workspaceState);
                wsManagerTestKit = new WorkspaceManagerTestKit();
                wsManagerTestKit.initWorkspace(workspaceState);

                testKit = new ChatAppTestKit(wsManagerTestKit);
                await awaitStartup(testKit.chatModelTestKit.wsManager, workspaceState);

                // Do this again, to trigger the event listeners to propagate info
                // over to the chat system
                createWorkspaceFiles(workspaceState);
                await jest.advanceTimersByTimeAsync(10000);
                chatApp = testKit.chatApp;
            });

            afterEach(() => {
                testKit.dispose();
                jest.useRealTimers();
            });

            test("resolves first workspace, filename only, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves first workspace, filename only, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, filename only, exact match", async () => {
                const result = await chatApp.resolveTargetPath("some-other-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, filename only, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "some-other-file.py",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file");
                expect(result).toBeUndefined();
            });

            test("resolves first workspace, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("some-other-file");
                expect(result).toBeUndefined();
            });

            test("resolves second workspace, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("some-other-file", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, partial path, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves first workspace, partial path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file.py",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, partial path, exact match", async () => {
                const result = await chatApp.resolveTargetPath("short/path/some-other-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, partial path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "short/path/some-other-file.py",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, partial path, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file");
                expect(result).toBeUndefined();
            });

            test("resolves first workspace, partial path, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, partial path, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("short/path/some-other-file");
                expect(result).toBeUndefined();
            });

            test("resolves second workspace, partial path, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "short/path/some-other-file",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, full path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath1);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves first workspace, full path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath1, undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, full path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath2);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, full path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath2, undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves first workspace, absolute path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath1)
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves first workspace, absolute path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath1),
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath1);
            });

            test("resolves second workspace, absolute path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder3.folderRootUri.fsPath, relFilePath2)
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });

            test("resolves second workspace, absolute path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder3.folderRootUri.fsPath, relFilePath2),
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder3.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath2);
            });
        });
    });

    describe("resolveFilePath multiple workspaces, nested", () => {
        describe("same relpath resolves first workspace", () => {
            let chatApp: ChatApp;
            const relFilePath = "example/path/to/abc123-dir/abc123-file.py";
            const relFileContents = "content of abc123-file";

            beforeEach(async () => {
                jest.useFakeTimers();
                resetMockWorkspace();
                mockFSUtils.reset();

                const workspaceState: MockSourceFolderState[] = [
                    {
                        ...workspaceFolder1,
                        files: new Map<string, string>([[relFilePath, relFileContents]]),
                        tracked: true,
                    },
                    {
                        ...workspaceFolder2,
                        files: new Map<string, string>([[relFilePath, relFileContents]]),
                        tracked: false,
                    },
                ];

                createWorkspaceFiles(workspaceState);
                wsManagerTestKit = new WorkspaceManagerTestKit();
                wsManagerTestKit.initWorkspace(workspaceState);

                testKit = new ChatAppTestKit(wsManagerTestKit);
                await awaitStartup(testKit.chatModelTestKit.wsManager, workspaceState);

                // Do this again, to trigger the event listeners to propagate info
                // over to the chat system
                createWorkspaceFiles(workspaceState);
                await jest.advanceTimersByTimeAsync(10000);
                chatApp = testKit.chatApp;
            });

            afterEach(() => {
                testKit.dispose();
                jest.useRealTimers();
            });

            test("filename only, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("filename only, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file.py", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("filename only, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file");
                expect(result).toBeUndefined();
            });

            test("filename only, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-file", undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("partial path, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file.py");
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("partial path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file.py",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("partial path, no extension, exact match", async () => {
                const result = await chatApp.resolveTargetPath("abc123-dir/abc123-file");
                expect(result).toBeUndefined();
            });

            test("partial path, no extension, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    "abc123-dir/abc123-file",
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("full path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("full path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(relFilePath, undefined, false);
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("absolute path, exact match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath)
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });

            test("absolute path, fuzzy match", async () => {
                const result = await chatApp.resolveTargetPath(
                    joinPath(workspaceFolder1.folderRootUri.fsPath, relFilePath),
                    undefined,
                    false
                );
                expect(result?.repoRoot).toBe(workspaceFolder1.repoRootUri.fsPath);
                expect(result?.pathName).toBe(relFilePath);
            });
        });
    });
    describe("Image Handling", () => {
        let testKit: ChatAppTestKit;
        let chatApp: ChatApp;
        let mockAssetManager: AssetManager;

        // Test image data
        const testImageData = new Uint8Array([1, 2, 3, 4, 5]);
        const testImageName = "test-image.png";

        beforeEach(async () => {
            jest.useFakeTimers();
            resetMockWorkspace();
            mockFSUtils.reset();

            // Create a workspace with a single folder
            const workspaceState: MockSourceFolderState[] = [
                { ...workspaceFolder1, tracked: true },
            ];

            createWorkspaceFiles(workspaceState);
            wsManagerTestKit = new WorkspaceManagerTestKit();
            wsManagerTestKit.initWorkspace(workspaceState);

            testKit = new ChatAppTestKit(wsManagerTestKit);
            await awaitStartup(testKit.chatModelTestKit.wsManager, workspaceState);

            chatApp = testKit.chatApp;

            // Get access to the asset manager
            // @ts-expect-error - Accessing private property for testing
            mockAssetManager = chatApp._assetManager;

            // Spy on the asset manager methods
            jest.spyOn(mockAssetManager, "saveAsset").mockImplementation(
                async (_name, _content) => {
                    // Just return without doing anything
                    return;
                }
            );

            jest.spyOn(mockAssetManager, "loadAsset").mockImplementation(async (name) => {
                if (name === testImageName) {
                    // Create a Uint8Array from the test image data
                    return new Uint8Array(testImageData);
                }
                return undefined;
            });

            jest.spyOn(mockAssetManager, "deleteAsset").mockImplementation(async (_name) => {
                // Just return without doing anything
                return;
            });
        });

        afterEach(() => {
            testKit.dispose();
            jest.useRealTimers();
            jest.restoreAllMocks();
        });

        test("_saveImage should save an image and return the filename", async () => {
            // Create a request to save the image
            const request = {
                type: WebViewMessageType.chatSaveImageRequest,
                data: {
                    filename: testImageName,
                    data: testImageData,
                },
            };

            // @ts-expect-error: TS2341 - Accessing private method for testing
            const response = await chatApp._saveImage(request);

            // Verify the asset manager was called with the correct parameters
            expect(mockAssetManager.saveAsset).toHaveBeenCalledWith(testImageName, testImageData);

            // Verify the response has the correct type and data
            expect(response.type).toBe(WebViewMessageType.chatSaveImageResponse);
            expect(response.data).toBe(testImageName);
        });

        test("_loadImage should load an image and return it as a Uint8Array", async () => {
            // Create a request to load the image
            const request = {
                type: WebViewMessageType.chatLoadImageRequest,
                data: testImageName,
            };

            // @ts-expect-error: TS2341 - Accessing private method for testing
            const response = await chatApp._loadImage(request);

            // Verify the asset manager was called with the correct filename
            expect(mockAssetManager.loadAsset).toHaveBeenCalledWith(testImageName);

            // Verify the response has the correct type
            expect(response.type).toBe(WebViewMessageType.chatLoadImageResponse);

            // Verify the response data is a string with the expected values
            const binString = atob(response.data as string);
            const bytes = Uint8Array.from(binString, (m) => m.codePointAt(0) || 0);
            expect(Array.from(bytes)).toEqual([1, 2, 3, 4, 5]);
        });

        test("_loadImage should return undefined data when asset is not found", async () => {
            // Create a request for a non-existent image
            const request = {
                type: WebViewMessageType.chatLoadImageRequest,
                data: "non-existent-image.png",
            };

            // @ts-expect-error: TS2341 - Accessing private method for testing
            const response = await chatApp._loadImage(request);

            // Verify the asset manager was called with the correct filename
            expect(mockAssetManager.loadAsset).toHaveBeenCalledWith("non-existent-image.png");

            // Verify the response has the correct type
            expect(response.type).toBe(WebViewMessageType.chatLoadImageResponse);

            // Verify the response data is undefined
            expect(response.data).toBeUndefined();
        });

        test("_deleteImage should delete an image", async () => {
            // Create a request to delete the image
            const request = {
                type: WebViewMessageType.chatDeleteImageRequest,
                data: testImageName,
            };

            // @ts-expect-error: TS2341 - Accessing private method for testing
            const response = await chatApp._deleteImage(request);

            // Verify the asset manager was called with the correct filename
            expect(mockAssetManager.deleteAsset).toHaveBeenCalledWith(testImageName);

            // Verify the response has the correct type
            expect(response.type).toBe(WebViewMessageType.chatDeleteImageResponse);
        });
    });
});

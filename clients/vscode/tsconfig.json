{
	"extends": "../tsconfig.json",
	"include": [
		"src/**/*",
	],
	"exclude": [
		"src/__tests__/**/*.e2e.ts",
		"wdio.conf.ts"
	],
	"compilerOptions": {
		"outDir": "out",

		"module": "commonjs",
		"moduleResolution": "node",
		"target": "ES2022",
		"lib": [
			"ES2022",
			"dom",
		],
		"esModuleInterop": true,
		"sourceMap": true,
		"sourceRoot": "./src",
		"resolveJsonModule": true,
		"strict": true,
	},
	"references": [
		{
			"path": "./tsconfig.e2e.json"
		},
		{
			"path": "./tsconfig.node.json"
		}
	]
}

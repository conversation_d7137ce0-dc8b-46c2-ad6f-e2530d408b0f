# Sidecar Libs Protos BUILD File
#
# This file defines the build rules for the Protocol Buffer definitions used in the Sidecar Libs.
# It handles:
# 1. Defining the proto_library target for all .proto files in this directory
# 2. Generating TypeScript definitions from these protos
# 3. Copying the generated files back to the source tree for direct imports
# 4. Setting up verification tests to ensure generated files are up-to-date
# 5. Creating a convenience script to update the generated files
#
# We need to perform the copying in order to expose both the type definitions and the actual implementations to non-Bazel build systems.
# Within Bazel, this is not necessary, but for other systems (Jest, or VSCode's typescript plugin, for example), we need to copy files into source
#
# Key commands:
# - Build protos: bazel build //clients/sidecar/libs/protos:sidecar_libs_ts_protos
# - Copy generated implementation files: bazel run //clients/sidecar/libs/protos:build_js_and_copy
# - Copy generated type definitions: bazel run //clients/sidecar/libs/protos:sidecar_libs_ts_protos.copy
# - Verify files are up-to-date: bazel test //clients/sidecar/libs/protos:check_*

load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@bazel_skylib//rules:diff_test.bzl", "diff_test")
load("@bazel_skylib//rules:write_file.bzl", "write_file")

# Define the proto_library target that includes all .proto files in this directory
proto_library(
    name = "sidecar_libs_protos",
    srcs = glob(["**/*.proto"]),
    visibility = ["//visibility:public"],
    deps = [
        "@protobuf//:any_proto",
        "@protobuf//:descriptor_proto",
        "@protobuf//:struct_proto",
        "@protobuf//:timestamp_proto",
    ],
)

# List of proto files that need their generated TypeScript files copied to source
PROTOS_TO_COPY = [
    "test_service",  # Test proto for testing the PostMessageTransport
]

# JavaScript source files to copy (implementation files)
SOURCES_TO_COPY = [proto + "_pb.js" for proto in PROTOS_TO_COPY]

# Generate TypeScript definitions from the proto files
ts_proto_library(
    name = "sidecar_libs_ts_protos",
    copy_files = True,
    # TypeScript definition files to copy (type definitions)
    files_to_copy = [proto + ext for proto in PROTOS_TO_COPY for ext in [
        "_pb.d.ts",  # TypeScript type definitions for protobuf messages and services
    ]],
    node_modules = "//clients/sidecar/libs:node_modules",
    proto = ":sidecar_libs_protos",
    visibility = ["//clients:__subpackages__"],
)

# Create genrules for each file we want to check
[
    genrule(
        name = "generated_" + k,
        srcs = [":sidecar_libs_ts_protos"],
        outs = ["gen_" + k],
        cmd = "cp $(BINDIR)/clients/sidecar/libs/protos/" + k + " $@",
    )
    for k in SOURCES_TO_COPY
]

# Create a test target for each file that Bazel should write to the source tree.
# These tests verify that the generated files in the source tree match the ones in bazel-bin.
[
    diff_test(
        name = "check_" + k,
        # If this test fails, show an error message to run the updater
        failure_message = "Please run:  bazel run //clients/sidecar/libs/protos:build_js_and_copy",
        file1 = "gen_" + k,
        file2 = k,
    )
    for k in SOURCES_TO_COPY
]

# Generate the updater script so there's only one target for devs to run,
# even if many generated files are in the source folder.
write_file(
    name = "gen_build_js_and_copy",
    out = "build_js_and_copy.sh",
    content = [
        "#!/usr/bin/env bash",
        # Bazel gives us a way to access the source folder
        "cd $BUILD_WORKSPACE_DIRECTORY",

        # Get the actual bazel-bin directory path with fallback
        "if command -v bazel &> /dev/null; then",
        "  BAZEL_BIN=$(bazel info bazel-bin)",
        "else",
        "  echo \"Warning: bazel not found, using relative path\"",
        "  BAZEL_BIN=\"$BUILD_WORKSPACE_DIRECTORY/bazel-bin\"",
        "fi",
    ] + [
        # Paths are now relative to the workspace.
        # We can copy files from bazel-bin to the sources and make them readable/writable by anyone
        "cp -fv $BAZEL_BIN/clients/sidecar/libs/protos/{0} $BUILD_WORKSPACE_DIRECTORY/clients/sidecar/libs/protos/{0} && chmod a+rw $BUILD_WORKSPACE_DIRECTORY/clients/sidecar/libs/protos/{0}".format(k)
        for k in SOURCES_TO_COPY
    ],
)

# This is what you can `bazel run` and it can write to the source folder
sh_binary(
    name = "build_js_and_copy",
    srcs = [":build_js_and_copy.sh"],
    data = [":sidecar_libs_ts_protos"],
)

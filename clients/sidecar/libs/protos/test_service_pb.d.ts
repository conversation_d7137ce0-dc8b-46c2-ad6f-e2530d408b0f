// @generated by protoc-gen-es v2.3.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/libs/protos/test_service.proto (package test, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file clients/sidecar/libs/protos/test_service.proto.
 */
export declare const file_clients_sidecar_libs_protos_test_service: GenFile;

/**
 * @generated from message test.TestRequest
 */
export declare type TestRequest = Message<"test.TestRequest"> & {
  /**
   * @generated from field: string foo = 1;
   */
  foo: string;
};

/**
 * Describes the message test.TestRequest.
 * Use `create(TestRequestSchema)` to create a new message.
 */
export declare const TestRequestSchema: GenMessage<TestRequest>;

/**
 * @generated from message test.TestResponse
 */
export declare type TestResponse = Message<"test.TestResponse"> & {
  /**
   * @generated from field: string result = 1;
   */
  result: string;
};

/**
 * Describes the message test.TestResponse.
 * Use `create(TestResponseSchema)` to create a new message.
 */
export declare const TestResponseSchema: GenMessage<TestResponse>;

/**
 * @generated from service test.TestService
 */
export declare const TestService: GenService<{
  /**
   * @generated from rpc test.TestService.TestMethod
   */
  testMethod: {
    methodKind: "unary";
    input: typeof TestRequestSchema;
    output: typeof TestResponseSchema;
  },
  /**
   * @generated from rpc test.TestService.ErrorMethod
   */
  errorMethod: {
    methodKind: "unary";
    input: typeof TestRequestSchema;
    output: typeof TestResponseSchema;
  },
}>;


# Sidecar Libs Protos

This directory contains the Protocol Buffer definitions for the Sidecar Libs.

## Proto Files

- `agent.proto`: Contains message definitions for agent-related functionality, including agent edits and qualified path names.

## Generating TypeScript Files from Proto Definitions

The TypeScript files are generated using Bazel. To generate and copy the TypeScript files to this directory, follow these steps:

### 1. Build the Proto Files

```bash
bazel build //clients/sidecar/libs/protos:sidecar_libs_ts_protos
```

This command will generate the TypeScript files from the proto definitions.

### 2. Copy the Generated Files

```bash
bazel run //clients/sidecar/libs/protos:sidecar_libs_ts_protos.copy
bazel run //clients/sidecar/libs/protos:build_js_and_copy
```

This command will copy the generated TypeScript files to this directory, making them available for import in your TypeScript code.

## Using the Generated TypeScript Files

After generating and copying the TypeScript files, you can import them in your TypeScript code like this:

```typescript
import {
  GetAgentEditListRequest,
  GetAgentEditListResponse,
  AgentEdit,
  QualifiedPathName,
  AgentFileChangeSummary,
} from "$clients/sidecar/libs/protos/agent_pb";
```

## Adding New Proto Files

To add a new proto file:

1. Create the new proto file in this directory.
2. Update the `files_to_copy` list in the `BUILD` file to include the new TypeScript files that will be generated.
3. Run the build and copy commands as described above.

## Modifying Existing Proto Files

If you modify an existing proto file, you need to regenerate the TypeScript files:

1. Make your changes to the proto file.
2. Run the build and copy commands as described above.

## Troubleshooting

If you encounter issues with the generated TypeScript files:

- Make sure the proto file syntax is correct.
- Check that the `files_to_copy` list in the `BUILD` file includes all the necessary files.
- Try cleaning the Bazel cache with `bazel clean` and then run the build and copy commands again.

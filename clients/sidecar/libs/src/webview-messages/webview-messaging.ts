import { AgentWebviewMessageHandler } from "../agent/agent-webview-messages";
import { AggregateCheckpointManager } from "../agent/checkpoint/aggregate-checkpoint-manager";
import { TaskWebviewMessageHandler } from "../agent/task/task-webview-messages";
import { TaskManager } from "../agent/task/task-manager";
import { ToolsWebviewMessageHandler } from "../tools/tool-webview-messages";
import { ToolsModel } from "../tools/tools-model";
import {
  CommonWebViewMessageType,
  WebViewMessage,
} from "./common-webview-messages";
import { AgentWebViewMessageType } from "./message-types/agent-messages";
import { TaskWebViewMessageType } from "./message-types/task-messages";
import { ToolsWebViewMessageType } from "./message-types/tool-messages";
import {
  IWebviewMessageConsumer,
  PostMessageFn,
  WebviewMessageBroker,
} from "./webview-messages-broker";

export type MessageConsumerTypes =
  | CommonWebViewMessageType
  | ToolsWebViewMessageType
  | AgentWebViewMessageType
  | TaskWebViewMessageType;

export class WebviewMessaging {
  private _broker: WebviewMessageBroker<MessageConsumerTypes>;

  constructor(
    // Note: This isn't particularly clean, ideally the client logic added
    // to AggregateCheckpointManager would be moved global singleton like the
    // other client interfaces and then the sidecar can be responsible for
    // creating the AggregateCheckpointManager.
    // However, the sidecar needs this to setup one of the webview message
    // consumers this seems like an "ok" place for this dependency.
    aggregateCheckpointManager: AggregateCheckpointManager,
    toolsModel: ToolsModel,
    taskManager: TaskManager,
  ) {
    this._broker = new WebviewMessageBroker();
    const agentWebviewMessageHandler = new AgentWebviewMessageHandler(
      aggregateCheckpointManager,
    );

    const webviewMsgConsumers = [
      agentWebviewMessageHandler,
      new ToolsWebviewMessageHandler(toolsModel),
      new TaskWebviewMessageHandler(taskManager),
    ];
    for (const webviewMsgConsumer of webviewMsgConsumers) {
      this._broker.registerHandler(
        webviewMsgConsumer as IWebviewMessageConsumer<MessageConsumerTypes>,
      );
    }
  }

  onMessage(
    msg: WebViewMessage<MessageConsumerTypes>,
    postMessage: PostMessageFn<MessageConsumerTypes>,
  ): boolean {
    return this._broker.handle(msg, postMessage);
  }
}

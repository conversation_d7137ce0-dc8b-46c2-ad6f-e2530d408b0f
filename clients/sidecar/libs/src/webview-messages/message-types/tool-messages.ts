import { ToolIdentifier } from "../../tools/tool-types";
import { WebViewMessage } from "../common-webview-messages";

export enum ToolsWebViewMessageType {
  closeAllToolProcesses = "close-all-tool-processes",
  getToolIdentifierRequest = "get-tool-identifier-request",
  getToolIdentifierResponse = "get-tool-identifier-response",
}

export type GetToolIdentifierRequest =
  WebViewMessage<ToolsWebViewMessageType.getToolIdentifierRequest> & {
    data: { toolName: string };
  };

export type GetToolIdentifierResponse =
  WebViewMessage<ToolsWebViewMessageType.getToolIdentifierResponse> & {
    data: GetToolIdentifierResponseData;
  };

export type GetToolIdentifierResponseData =
  | {
      found: true;
      toolIdentifier: ToolIdentifier;
      mcpToolName?: string;
      mcpServerName?: string;
    }
  | { found: false };

/**
 * Types related to models in the chat interface.
 */

/**
 * Represents a model that can be selected in the model picker.
 */
export interface Model {
  /**
   * The unique identifier for the model, or null for the default model.
   */
  id: string | null;

  /**
   * The display name of the model.
   */
  name: string;
}

/**
 * The default model used when no specific model is selected.
 */
export const DEFAULT_MODEL: Model = {
  id: null,
  name: "Default",
};

/**
 * Registry for managing available models.
 */
export class ModelRegistry {
  private models: Model[] = [DEFAULT_MODEL];

  /**
   * Register models to be available in the model picker.
   * @param models The models to register
   */
  public registerModels(models: Model[]) {
    this.models = [DEFAULT_MODEL, ...models];
  }

  /**
   * Get all available models.
   * @returns Array of available models
   */
  public getModels(): Model[] {
    return this.models;
  }

  /**
   * Get a model by its ID.
   * @param id The model ID to look up
   * @returns The matching model, or the default model if not found
   */
  public getModel(id: string | null): Model {
    return this.models.find((model) => model.id === id) ?? DEFAULT_MODEL;
  }
}

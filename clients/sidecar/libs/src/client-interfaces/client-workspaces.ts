import { getLogger } from "../logging";
import { QualifiedPathName } from "../workspace/qualified-path-name";
import { FileDeletedEvent, FileDidMoveEvent } from "../agent/agent-edit-types";
import { ISidecarDisposable } from "../lifecycle/disposable-types";

/**
 * This interface covers methods relating to the workspaces in the users
 * clients.
 */
export interface IClientWorkspaces {
  getCwd: () => Promise<string | undefined>;
  readFile: (path: string) => Promise<FileDetails>;
  writeFile: (filePath: QualifiedPathName, contents: string) => Promise<void>;
  deleteFile: (filePath: QualifiedPathName) => Promise<void>;
  getQualifiedPathName: (
    path: string,
  ) => Promise<QualifiedPathName | undefined>;
  getPathInfo: (path: string) => Promise<PathInfo>;
  findFiles: (
    includeGlob: string,
    excludeGlob?: string | null,
    maxResults?: number,
    timelimit?: number,
  ) => Promise<QualifiedPathName[]>;
  listDirectory: (
    path: string,
    depth?: number,
    showHidden?: boolean,
  ) => Promise<ListDirectoryResult>;
  getRipgrepPath: () => Promise<string | undefined>;
  onFileDeleted: (
    callback: (event: FileDeletedEvent) => void,
  ) => ISidecarDisposable;
  onFileDidMove: (
    callback: (event: FileDidMoveEvent) => void,
  ) => ISidecarDisposable;
}

export type FileDetails = {
  contents?: string;
  filepath?: QualifiedPathName;
};

// Copied from vscode's FileType enum
export enum FileType {
  Unknown = 0,
  File = 1,
  Directory = 2,
  SymbolicLink = 64,
}

export type PathInfo = {
  type?: FileType;
  filepath?: QualifiedPathName;
  exists?: boolean;
};

export type ListDirectoryResult = {
  entries: string[];
  errorMessage?: string;
};

class ClientWorkspacesSingleton {
  private static _instance: IClientWorkspaces | undefined = undefined;

  static setClientWorkspaces(cw: IClientWorkspaces) {
    if (this._instance !== undefined) {
      const logger = getLogger("ClientWorkspaces");
      logger.warn(
        "Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.",
      );
      return;
    }

    this._instance = cw;
  }

  static getClientWorkspaces(): IClientWorkspaces {
    if (this._instance === undefined) {
      throw new Error("ClientWorkspaces not set");
    }
    return this._instance;
  }

  static reset() {
    this._instance = undefined;
  }
}

// Some shorthand functions to make usage a bit easier
export const setLibraryClientWorkspaces = (c: IClientWorkspaces) =>
  ClientWorkspacesSingleton.setClientWorkspaces(c);
export const getClientWorkspaces = () =>
  ClientWorkspacesSingleton.getClientWorkspaces();
export const resetLibraryClientWorkspaces = () =>
  ClientWorkspacesSingleton.reset();

import { MetricsReporter } from "@augment-internal/sidecar-libs/src/metrics/metrics-reporter";
import { AgentRequestEventData } from "@augment-internal/sidecar-libs/src/metrics/types";
import { msecToTimestamp } from "@augment-internal/sidecar-libs/src/utils/time";
import { AgentRequestEvent } from "../client-interfaces/api-client-types";
import { getAPIClient } from "../client-interfaces/api-client";

class AgentRequestEventReporter extends MetricsReporter<AgentRequestEvent> {
  private static maxRecords = 10000;
  private static batchSize = 1000;
  private static uploadMsec = 10000;

  private static instance: AgentRequestEventReporter | null = null;

  /**
   * Gets the singleton instance of AgentRequestEventReporter
   * @param maxRecords Optional maximum number of records
   * @param uploadMs Optional upload interval in milliseconds
   * @param batchSize Optional batch size
   * @returns The singleton instance of AgentRequestEventReporter
   */
  public static getInstance(): AgentRequestEventReporter {
    if (!AgentRequestEventReporter.instance) {
      AgentRequestEventReporter.instance = new AgentRequestEventReporter();
      AgentRequestEventReporter.instance.enableUpload();
    }
    return AgentRequestEventReporter.instance;
  }

  public static reset(): void {
    if (AgentRequestEventReporter.instance) {
      AgentRequestEventReporter.instance.dispose();
      AgentRequestEventReporter.instance = null;
    }
  }

  private constructor() {
    super(
      "AgentRequestEventReporter",
      AgentRequestEventReporter.maxRecords,
      AgentRequestEventReporter.uploadMsec,
      AgentRequestEventReporter.batchSize,
    );
  }

  // reportEvent reports an agent request event.
  public reportEvent(eventData: AgentRequestEventData): void {
    const [eventTimeSec, eventTimeNsec] = msecToTimestamp(Date.now());

    /* eslint-disable @typescript-eslint/naming-convention */
    this.report({
      event_time_sec: eventTimeSec,
      event_time_nsec: eventTimeNsec,
      event_name: eventData.eventName,
      conversation_id: eventData.conversationId,
      request_id: eventData.requestId,
      chat_history_length: eventData.chatHistoryLength,
    });
    /* eslint-enable @typescript-eslint/naming-convention */
  }

  protected performUpload(batch: AgentRequestEvent[]): Promise<void> {
    return getAPIClient().logAgentRequestEvent(batch);
  }
}

/**
 * Gets the singleton instance of AgentRequestEventReporter
 * @returns The singleton instance of AgentRequestEventReporter
 */
export function getAgentRequestEventReporter(): AgentRequestEventReporter {
  return AgentRequestEventReporter.getInstance();
}

export function resetAgentRequestEventReporter() {
  AgentRequestEventReporter.reset();
}

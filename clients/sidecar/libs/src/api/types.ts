import { ChatResultNode } from "../chat/chat-types";

type RequestInfo = Request | string;
export type FetchFunction = (
  input: RequestInfo,
  init?: RequestInit,
) => Promise<Response>;
export type ReadableStreamReader<T> =
  | ReadableStreamDefaultReader<T>
  | ReadableStreamBYOBReader;

/**
 * The blobs for a completion is represented as a +/- delta of blob ids
 * applied to a saved checkpoint of blob ids. An undefined checkpointId
 * represents a starting empty set of blob names.
 */
export type Blobs = {
  checkpointId: string | undefined;
  addedBlobs: string[];
  deletedBlobs: string[];
};

export type BackWorkspaceFileChunk = {
  char_start: number;
  char_end: number;
  blob_name: string;
};

export type BackChatResult = {
  text: string;
  unknown_blob_names?: string[];
  checkpoint_not_found?: boolean;
  workspace_file_chunks?: BackWorkspaceFileChunk[];
  nodes?: ChatResultNode[];
};

export class InvalidCompletionURLError extends Error {
  constructor() {
    super("The completion URL setting is invalid");
  }
}

// Streaming abort reasons
export const STREAM_CANCELLED = "STREAM_CANCELLED";
export const STREAM_TIMEOUT = "STREAM_TIMEOUT";

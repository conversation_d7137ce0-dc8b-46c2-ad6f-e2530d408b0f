import { BlobsPayload, VCSChangePayload } from "../chat/chat-types";
import { Chat<PERSON><PERSON><PERSON> } from "../client-interfaces/api-client-types";
import { AugmentLogger } from "../logging";
import {
  toBoolean,
  toNumber,
  toString,
  toStringArray,
} from "../utils/json-utils";
import { VCSChange } from "../vcs/watcher/types";
import { BackChatResult, Blobs } from "./types";

// Ensures that the added/deleted blob lists are sorted
export function blobsToBlobsPayload(blobs: Blobs): BlobsPayload {
  return {
    checkpoint_id: blobs.checkpointId,
    added_blobs: blobs.addedBlobs.sort(),
    deleted_blobs: blobs.deletedBlobs.sort(),
  };
}

export function toVCSChangePayload(vcsChange: VCSChange): VCSChangePayload {
  return {
    working_directory_changes: vcsChange.workingDirectory.map((wd) => {
      return {
        before_path: wd.beforePath,
        after_path: wd.afterPath,
        change_type: wd.changeType,
        head_blob_name: wd.headBlobName,
        indexed_blob_name: wd.indexedBlobName,
        current_blob_name: wd.currentBlobName,
      };
    }),
  };
}

// Regex to match split surrogate pairs at the start or end of a string
// i.e. a high surrogate at the end of the string or a low surrogate at the start
export const BOUNDARY_SURROGATE_REGEX = /^[\uDC00-\uDFFF]|[\uD800-\uDBFF]$/g;

// Regex to match lone surrogates anywhere in a string
export const LONE_SURROGATE_REGEX =
  /[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?<![\uD800-\uDBFF])[\uDC00-\uDFFF]/g;

export const REPLACEMENT_CHAR = "\uFFFD";

export function isWellFormed(str: string): boolean {
  // TODO(jeff): String.prototype.isWellFormed is giving lint errors, probably because its ES2024
  return !LONE_SURROGATE_REGEX.test(str);
}

export function toWellFormed(str: string): string {
  return str.replace(LONE_SURROGATE_REGEX, REPLACEMENT_CHAR);
}

/**
 * Safely serialize an object to JSON for the backend.
 *
 * Converts undefined to null, strips lone surrogates fron the ends, and replaces
 * lone surrogates in the middle with the Unicode replacement character (U+FFFD).
 *
 * @param body The object to serialize
 * @param logger The logger to use for logging
 * @param dropAndReplaceSurrogates Whether to drop and replace lone surrogates.
 * @returns A JSON string that can be passed to the backend.
 */
export function safeJsonStringify(
  body?: Record<string, any>,
  logger?: AugmentLogger,
  dropAndReplaceSurrogates: boolean = true,
): string {
  return JSON.stringify(body, (key, value) => {
    // Convert undefined to null
    if (value === undefined) {
      return null;
    }

    // Handle strings that might contain lone surrogates
    if (typeof value === "string") {
      let strValue = value;
      if (BOUNDARY_SURROGATE_REGEX.test(strValue)) {
        if (logger) {
          logger.debug(`Surrogate pair was split in field ${key}`);
        }

        if (dropAndReplaceSurrogates) {
          strValue = strValue.replace(BOUNDARY_SURROGATE_REGEX, "");
        }
      }

      if (!isWellFormed(strValue)) {
        if (logger) {
          logger.debug(`Lone surrogate found mid-string in field ${key}`);
        }

        if (dropAndReplaceSurrogates) {
          strValue = toWellFormed(strValue);
        }
      }

      return strValue;
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return value;
  });
}

export function toChatResult(resp: BackChatResult): ChatResult {
  const text = toString("BackChatResult", "text", resp.text);
  const unknownBlobNames =
    resp.unknown_blob_names === undefined
      ? []
      : toStringArray(
          "BackChatResult",
          "unknown_blob_names",
          resp.unknown_blob_names,
        );
  const checkpointNotFound =
    resp.checkpoint_not_found === undefined
      ? false
      : toBoolean(
          "BackChatResult",
          "checkpoint_not_found",
          resp.checkpoint_not_found,
          false,
        );
  const workspaceFileChunks =
    resp.workspace_file_chunks === undefined
      ? []
      : resp.workspace_file_chunks.map((chunk) => {
          return {
            charStart: toNumber(
              "BackWorkspaceFileChunk",
              "char_start",
              chunk.char_start,
            ),
            charEnd: toNumber(
              "BackWorkspaceFileChunk",
              "char_end",
              chunk.char_end,
            ),
            blobName: toString(
              "BackWorkspaceFileChunk",
              "blob_name",
              chunk.blob_name,
            ),
          };
        });

  const nodes = resp.nodes;
  return {
    text,
    unknownBlobNames,
    checkpointNotFound,
    workspaceFileChunks,
    nodes,
  };
}

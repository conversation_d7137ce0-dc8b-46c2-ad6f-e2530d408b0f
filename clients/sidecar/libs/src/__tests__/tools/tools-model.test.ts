import { AggregateCheckpointManager } from "../../agent/checkpoint/aggregate-checkpoint-manager";
import { ChatMode } from "../../chat/chat-types";
import {
  setLibraryClientFeatureFlags,
  SidecarFlags,
} from "../../client-interfaces/feature-flags";
import {
  IClientWorkspaces,
  setLibraryClientWorkspaces,
  resetLibraryClientWorkspaces,
} from "../../client-interfaces/client-workspaces";

import {
  RemoteToolHost,
  RemoteToolDefinition,
} from "../../tools/remote-tools/remote-tool-host";
import { SidecarToolHost } from "../../tools/sidecar-tools/sidecar-tool-host";
import { SidecarToolType } from "../../tools/sidecar-tools/sidecar-tool-types";
import { IToolHost } from "../../tools/tool-host";
import {
  LocalToolType,
  RemoteToolId,
  ToolAvailabilityStatus,
  ToolHostName,
  ToolSafety,
  type ToolDefinitionWithSettings,
} from "../../tools/tool-types";
import { ToolsModel } from "../../tools/tools-model";
import { MockClientFeatureFlags } from "../mocks/mock-client-feature-flags";

const mockRemoteToolDefinitions = [
  {
    toolDefinition: {
      name: "remote-tool1",
      description: "Remote Tool 1",
      input_schema_json: "{}",
      tool_safety: ToolSafety.Safe,
    },
    remoteToolId: RemoteToolId.WebSearch,
    availabilityStatus: ToolAvailabilityStatus.Available,
    toolSafety: ToolSafety.Safe,
    oauthUrl: "https://tool1.com/oauth",
  },
] satisfies RemoteToolDefinition[];

const enabledTool = {
  definition: {
    name: LocalToolType.readFile.toString(),
    description: "Client tool",
    input_schema_json: "{}",
    tool_safety: ToolSafety.Safe,
  },
  identifier: {
    toolId: LocalToolType.readFile,
    hostName: ToolHostName.localToolHost,
  },
  isConfigured: true,
  enabled: true,
  toolSafety: ToolSafety.Safe,
} satisfies ToolDefinitionWithSettings;

const disabledTool = {
  definition: {
    name: LocalToolType.readFile.toString(),
    description: "Disabled tool",
    input_schema_json: "{}",
    tool_safety: ToolSafety.Safe,
  },
  identifier: {
    toolId: LocalToolType.readFile,
    hostName: ToolHostName.localToolHost,
  },
  isConfigured: true,
  enabled: false,
  toolSafety: ToolSafety.Safe,
} satisfies ToolDefinitionWithSettings;

describe("ToolsModel", () => {
  const mockToolUseRequestEventReporter = {
    reportEvent: jest.fn(),
  };

  // Mock for IClientWorkspaces
  const mockClientWorkspaces: IClientWorkspaces = {
    getCwd: jest.fn().mockResolvedValue("/mock/cwd"),
    readFile: jest.fn().mockResolvedValue({ contents: "mock content" }),
    writeFile: jest.fn().mockResolvedValue(undefined),
    deleteFile: jest.fn().mockResolvedValue(undefined),
    getQualifiedPathName: jest.fn().mockResolvedValue({
      rootPath: "/mock/cwd",
      relPath: "example/path.txt",
    }),
    findFiles: jest.fn().mockResolvedValue([]),
    listDirectory: jest.fn().mockResolvedValue({ entries: [] }),
    getPathInfo: jest.fn().mockResolvedValue({ filepath: undefined }),
    getRipgrepPath: jest.fn().mockResolvedValue("/usr/bin/rg"),
    onFileDeleted: jest.fn().mockReturnValue({
      dispose: jest.fn(),
    }),
    onFileDidMove: jest.fn().mockReturnValue({
      dispose: jest.fn(),
    }),
  };

  // Set up and tear down for each test
  beforeEach(() => {
    setLibraryClientWorkspaces(mockClientWorkspaces);
  });

  afterEach(() => {
    resetLibraryClientWorkspaces();
    jest.clearAllMocks();
  });
  const mockToolHost = {
    getToolDefinitions: jest.fn().mockResolvedValue([]),
    getAllToolDefinitions: jest.fn().mockResolvedValue([]),
    getName: jest.fn().mockReturnValue(ToolHostName.localToolHost),
    callTool: jest.fn().mockResolvedValue({
      isError: false,
      text: "Success",
      requestId: "tool-request-id",
    }),
    checkToolCallSafe: jest.fn().mockResolvedValue(true),
    isRequestActive: jest.fn().mockReturnValue(false),
    close: jest.fn().mockResolvedValue(undefined),
    closeAllToolProcesses: jest.fn().mockResolvedValue(undefined),
    factory: jest.fn(),
    getTool: jest.fn().mockReturnValue(undefined),
  } as IToolHost;
  const mockRemoteSourceInfo = {
    retrieveRemoteTools: jest.fn().mockResolvedValue(mockRemoteToolDefinitions),
    filterToolsWithExtraInput: jest.fn().mockReturnValue(new Set()),
    runRemoteTool: jest.fn().mockResolvedValue({
      toolOutput: "Not implemented",
      toolResultMessage: "Not implemented",
      isError: true,
    }),
    checkToolSafety: jest.fn().mockResolvedValue(false),
  };
  const mockChatClientToolHost = {
    ...mockToolHost,
    getToolDefinitions: () =>
      Promise.resolve([
        {
          definition: {
            name: LocalToolType.saveFile.toString(),
            description: "Client tool",
            input_schema_json: "{}",
            tool_safety: ToolSafety.Safe,
          },
          identifier: {
            toolId: LocalToolType.saveFile,
            hostName: ToolHostName.localToolHost,
          },
          isConfigured: true,
          enabled: true,
          toolSafety: ToolSafety.Safe,
        } satisfies ToolDefinitionWithSettings,
      ]),

    getAllToolDefinitions: () => mockChatClientToolHost.getToolDefinitions(),
  };
  const mockAgentClientToolHost = {
    ...mockToolHost,
    getToolDefinitions: () => Promise.resolve([enabledTool, disabledTool]),
    getAllToolDefinitions: () => mockAgentClientToolHost.getToolDefinitions(),
  };

  // Mock the plugin state module
  jest.mock("../../client-interfaces/plugin-state", () => ({
    getStateForSidecar: jest.fn().mockReturnValue({
      getValue: jest.fn().mockImplementation((_namespace, key) => {
        // Return the appropriate mock value based on the key
        if (key === "augment/clients/sidecar/chat-mode") {
          return Promise.resolve(ChatMode.chat);
        }
        return Promise.resolve(undefined);
      }),
      setValue: jest.fn().mockResolvedValue(undefined),
    }),
    PluginStateNamespace: { agent: "agent" },
    PluginStateScope: { global: "global" },
  }));

  const createToolsModel = async (
    chatMode: ChatMode,
    flags: Partial<SidecarFlags> = {},
  ) => {
    const mockClientFeatureFlags = new MockClientFeatureFlags(flags);
    setLibraryClientFeatureFlags(mockClientFeatureFlags);
    const model = new ToolsModel(
      [], // mcpServers
      (mode: ChatMode) => {
        if (mode === ChatMode.chat) {
          return mockChatClientToolHost;
        } else {
          return mockAgentClientToolHost;
        }
      },
      mockRemoteSourceInfo,
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      () => {}, // mcpToolsStartupErrorFn
      mockClientFeatureFlags,
      new AggregateCheckpointManager(
        {
          save: () => Promise.resolve(),
          load: () => Promise.resolve(undefined),
          saveShard: () => Promise.resolve(),
          loadShard: () => Promise.resolve(undefined),
          saveManifest: () => Promise.resolve(),
          loadManifest: () => Promise.resolve(undefined),
          deleteShard: () => Promise.resolve(),
        },
        // eslint-disable-next-line @typescript-eslint/no-empty-function
        () => undefined,
        () => ({ dispose: () => {} }),
        () => ({ dispose: () => {} }),
        () => ({ dispose: () => {} }),
      ),
      () => Promise.resolve(undefined), // getAgentMemories
      () => undefined, // memoriesPath
      () => mockToolUseRequestEventReporter,
    );

    // Wait for async initialization to complete
    await new Promise((resolve) => setTimeout(resolve, 0));

    // Force a synchronous restart of hosts to ensure they're initialized
    model.restartHosts();

    // Set the mode after initialization
    model.setMode(chatMode);

    return model;
  };

  describe("tool host initialization", () => {
    test.each([
      [ChatMode.chat, { enableChatWithTools: false, enableAgentMode: false }],
      [ChatMode.agent, { enableChatWithTools: false, enableAgentMode: false }],
      [ChatMode.chat, { enableChatWithTools: false, enableAgentMode: true }],
      [ChatMode.agent, { enableChatWithTools: true, enableAgentMode: false }],
    ])(
      "should correctly initialize no tools for chatMode: %s, enableChatWithTools: %s, enableAgentMode: %s",
      async (chatMode: ChatMode, flags: Partial<SidecarFlags>) => {
        const model = await createToolsModel(chatMode, flags);

        expect(model.hosts).toHaveLength(0);
        expect(await model.getToolDefinitions()).toHaveLength(0);
      },
    );

    it("should include all tool hosts when chat mode with tools is enabled", async () => {
      const model = await createToolsModel(ChatMode.chat, {
        enableChatWithTools: true,
        enableAgentMode: false,
      });

      expect(model.hosts.length).toBe(3);
      expect(model.hosts).toContain(mockChatClientToolHost);
      expect(
        model.hosts.some((host: any) => host instanceof RemoteToolHost),
      ).toBe(true);
      expect(
        model.hosts.some((host: any) => host instanceof SidecarToolHost),
      ).toBe(true);
      expect(await model.getToolDefinitions()).toContainEqual({
        definition: {
          name: LocalToolType.saveFile.toString(),
          description: "Client tool",
          input_schema_json: "{}",
          tool_safety: ToolSafety.Safe,
        },
        identifier: {
          toolId: LocalToolType.saveFile,
          hostName: ToolHostName.localToolHost,
        },
        isConfigured: true,
        enabled: true,
        toolSafety: ToolSafety.Safe,
      } satisfies ToolDefinitionWithSettings);
    });

    it("should include all agent tool hosts when agent mode is enabled", async () => {
      const model = await createToolsModel(ChatMode.agent, {
        enableChatWithTools: false,
        enableAgentMode: true,
      });

      expect(model.hosts.length).toBe(3);
      expect(model.hosts).toContain(mockAgentClientToolHost);
      expect(
        model.hosts.some((host: any) => host instanceof RemoteToolHost),
      ).toBe(true);
      expect(
        model.hosts.some((host: any) => host instanceof SidecarToolHost),
      ).toBe(true);
      expect(await model.getToolDefinitions()).toStrictEqual([
        enabledTool,
        {
          definition: {
            name: "remote-tool1",
            description: "Remote Tool 1",
            input_schema_json: "{}",
            tool_safety: ToolSafety.Safe,
          },
          identifier: {
            toolId: RemoteToolId.WebSearch,
            hostName: ToolHostName.remoteToolHost,
          },
          isConfigured: true,
          enabled: true,
          oauthUrl: "https://tool1.com/oauth",
          toolSafety: ToolSafety.Safe,
        },
        {
          definition: {
            name: SidecarToolType.webFetch.toString(),
            description: expect.any(String) as string,
            input_schema_json: expect.any(String) as string,
            tool_safety: ToolSafety.Unsafe,
          },
          identifier: {
            toolId: SidecarToolType.webFetch,
            hostName: ToolHostName.sidecarToolHost,
          },
          isConfigured: true,
          enabled: true,
          toolSafety: ToolSafety.Unsafe,
        },
        {
          definition: {
            name: SidecarToolType.codebaseRetrieval.toString(),
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            description: expect.any(String),
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            input_schema_json: expect.any(String),
            tool_safety: ToolSafety.Safe,
          },
          identifier: {
            toolId: SidecarToolType.codebaseRetrieval,
            hostName: ToolHostName.sidecarToolHost,
          },
          isConfigured: true,
          enabled: true,
          toolSafety: ToolSafety.Safe,
        },
        {
          definition: {
            name: SidecarToolType.removeFiles.toString(),
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            description: expect.any(String),
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            input_schema_json: expect.any(String),
            tool_safety: ToolSafety.Safe,
          },
          identifier: {
            toolId: SidecarToolType.removeFiles,
            hostName: ToolHostName.sidecarToolHost,
          },
          isConfigured: true,
          enabled: true,
          toolSafety: ToolSafety.Safe,
        },
        {
          definition: {
            name: SidecarToolType.saveFile.toString(),
            description: expect.any(String) as string,
            input_schema_json: expect.any(String) as string,
            tool_safety: ToolSafety.Safe,
          },
          identifier: {
            toolId: SidecarToolType.saveFile,
            hostName: ToolHostName.sidecarToolHost,
          },
          isConfigured: true,
          enabled: true,
          toolSafety: ToolSafety.Safe,
        },
        {
          definition: {
            name: SidecarToolType.remember.toString(),
            description:
              "Call this tool when user asks you:\n- to remember something\n- to create memory/memories\n\nUse this tool only with information that can be useful in the long-term.\nDo not use this tool for temporary information.\n",
            input_schema_json:
              '{"type":"object","properties":{"memory":{"type":"string","description":"The concise (1 sentence) memory to remember."}},"required":["memory"]}',
            tool_safety: ToolSafety.Safe,
          },
          identifier: {
            toolId: SidecarToolType.remember,
            hostName: ToolHostName.sidecarToolHost,
          },
          isConfigured: true,
          enabled: true,
          toolSafety: ToolSafety.Safe,
        },
        {
          definition: {
            description:
              "Render a Mermaid diagram from the provided definition. This tool takes Mermaid diagram code and renders it as an interactive diagram with pan/zoom controls and copy functionality.",
            input_schema_json:
              '{"type":"object","properties":{"diagram_definition":{"type":"string","description":"The Mermaid diagram definition code to render"},"title":{"type":"string","description":"Optional title for the diagram","default":"Mermaid Diagram"}},"required":["diagram_definition"]}',
            name: "render-mermaid",
            tool_safety: 1,
          },
          enabled: true,
          identifier: {
            toolId: SidecarToolType.renderMermaid,
            hostName: ToolHostName.sidecarToolHost,
          },
          isConfigured: true,
          toolSafety: ToolSafety.Safe,
        },
      ] satisfies ToolDefinitionWithSettings[]);
    });
  });

  describe("getToolStatusForSettingsPanel", () => {
    const chatModes = [ChatMode.chat, ChatMode.agent];
    const flagCombinations = [
      { enableChatWithTools: false, enableAgentMode: false },
      { enableChatWithTools: false, enableAgentMode: true },
      { enableChatWithTools: true, enableAgentMode: false },
      { enableChatWithTools: true, enableAgentMode: true },
    ];
    const testCases: Array<[ChatMode, Partial<SidecarFlags>]> = [];

    // Building test cases manually satisfies type checking
    for (const mode of chatModes) {
      for (const flags of flagCombinations) {
        testCases.push([mode, flags]);
      }
    }
    it("should return both enabled/disabled tools in agent mode", async () => {
      const model = await createToolsModel(ChatMode.agent, {
        enableChatWithTools: false,
        enableAgentMode: true,
      });
      const hostTools = await model.getToolStatusForSettingsPanel();
      expect(hostTools.length).toBeGreaterThan(1);
      console.log("hostTools", hostTools);
      const enabledMatch = hostTools.filter(
        (t) =>
          t.definition.name === enabledTool.definition.name &&
          t.enabled === true,
      );
      const disabledMatch = hostTools.filter(
        (t) =>
          t.definition.name === disabledTool.definition.name &&
          t.enabled === false,
      );
      expect(enabledMatch?.length).toBe(1);
      expect(disabledMatch?.length).toBe(1);
    });

    test.each(testCases)(
      "should return all required tool status grouped by host for chatMode: %s, flags: %j",
      async (chatMode: ChatMode, flags: Partial<SidecarFlags>) => {
        const model = await createToolsModel(chatMode, flags);
        const hostTools = await model.getToolStatusForSettingsPanel();
        expect(hostTools.length).toBeGreaterThan(1);

        const remoteTools = hostTools.filter(
          (t) =>
            t.identifier &&
            t.identifier.hostName === ToolHostName.remoteToolHost,
        );
        expect(remoteTools?.length).toBe(1);
        expect(remoteTools[0].definition.name).toBe("remote-tool1");
      },
    );
  });

  describe("callTool", () => {
    it("tool request_id returned", async () => {
      const model = await createToolsModel(ChatMode.chat, {
        enableChatWithTools: true,
        enableAgentMode: false,
      });
      const result = await model.callTool(
        "chat-request-id",
        "tool-use-id",
        LocalToolType.saveFile.toString(),
        { param: "value" },
        [],
        "conversation-id",
      );
      expect(result.requestId).toBe("tool-request-id");
    });

    it("tool result event reported", async () => {
      const model = await createToolsModel(ChatMode.chat, {
        enableChatWithTools: true,
        enableAgentMode: false,
      });
      await model.callTool(
        "chat-request-id",
        "tool-use-id",
        LocalToolType.saveFile.toString(),
        { param: "value" },
        [],
        "conversation-id",
      );
      expect(mockToolUseRequestEventReporter.reportEvent).toHaveBeenCalledWith(
        "chat-request-id",
        LocalToolType.saveFile.toString(),
        "tool-use-id",
        { param: "value" },
        false,
        expect.any(Number), // toolRunDurationMs
        false,
        "conversation-id",
        expect.any(Number), // chatHistoryLength
        "tool-request-id",
        expect.any(Number), // toolOutputLen
      );
    });
  });
});

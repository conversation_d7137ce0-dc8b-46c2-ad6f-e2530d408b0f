import {
  createFeatures,
  FeatureVectorKey,
  ClientSpecificData,
} from "../feature-vector-collector";
import * as fs from "fs/promises";
import { exec } from "child_process";
import { promisify } from "util";

// Mock dependencies
jest.mock("node-machine-id", () => ({
  machineIdSync: jest.fn(() => "mock-machine-id"),
}));

jest.mock("systeminformation", () => ({
  graphics: jest.fn(() =>
    Promise.resolve({
      controllers: [
        { vendor: "NVIDIA", model: "GeForce GTX 1080", vram: 8192 },
      ],
    }),
  ),
  time: jest.fn(() => Promise.resolve({ timezone: "America/New_York" })),
  diskLayout: jest.fn(() =>
    Promise.resolve([{ type: "SSD", size: 512000000000 }]),
  ),
  system: jest.fn(() =>
    Promise.resolve({
      manufacturer: "Dell",
      model: "XPS 15",
      version: "1.0",
    }),
  ),
  bios: jest.fn(() =>
    Promise.resolve({
      vendor: "Dell Inc.",
      version: "1.2.3",
      releaseDate: "2023-01-01",
    }),
  ),
  baseboard: jest.fn(() =>
    Promise.resolve({
      manufacturer: "Dell Inc.",
      model: "0ABCDE",
      version: "A00",
    }),
  ),
  chassis: jest.fn(() =>
    Promise.resolve({
      manufacturer: "Dell Inc.",
      type: "Notebook",
      version: "Not Specified",
    }),
  ),
}));

jest.mock("fs/promises", () => ({
  lstat: jest.fn(),
  readFile: jest.fn(),
}));

jest.mock("child_process", () => ({
  exec: jest.fn(),
}));

jest.mock("util", () => ({
  promisify: jest.fn((fn: unknown) => {
    if (
      fn &&
      typeof fn === "function" &&
      (fn as { name?: string }).name === "exec"
    ) {
      return jest.fn();
    }
    return fn;
  }),
}));

// Mock os module
jest.mock("os", () => ({
  type: jest.fn(() => "Linux"),
  cpus: jest.fn(() => [
    {
      model: "Intel Core i7",
      speed: 2400,
      times: { user: 0, nice: 0, sys: 0, idle: 0, irq: 0 },
    },
  ]),
  totalmem: jest.fn(() => 16 * 1024 * 1024 * 1024),
  hostname: jest.fn(() => "test-host"),
  machine: jest.fn(() => "x86_64"),
  userInfo: jest.fn(() => ({ username: "testuser" })),
  networkInterfaces: jest.fn(() => ({
    eth0: [{ mac: "00:11:22:33:44:55", internal: false }],
    lo: [{ mac: "00:00:00:00:00:00", internal: true }],
  })),
  release: jest.fn(() => "5.15.0"),
  version: jest.fn(() => "Linux version 5.15.0"),
  homedir: jest.fn(() => "/home/<USER>"),
}));

describe("Feature Vector Collector", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("createFeatures", () => {
    it("should collect basic system information", async () => {
      const clientData: ClientSpecificData = {
        clientVersion: "1.0.0",
        clientMachineId: "client-machine-123",
        telemetryDevDeviceId: "telemetry-123",
        projectRootPath: "/home/<USER>/project",
      };

      // Mock file system operations
      (fs.lstat as jest.Mock).mockResolvedValue({ ino: 12345 });

      // Mock git config
      const execAsync = promisify(exec) as jest.Mock;
      execAsync.mockResolvedValue({ stdout: "<EMAIL>\n" });

      // Mock SSH key
      (fs.readFile as jest.Mock).mockRejectedValue(new Error("No SSH key"));

      const features = await createFeatures(clientData);

      expect(features.clientVersion).toBe("1.0.0");
      expect(features.machineId).toBe("client-machine-123");
      expect(features.os).toBe("Linux");
      expect(features.cpu).toBe("Intel Core i7");
      expect(features.memory).toBe("17179869184");
      expect(features.numCpus).toBe("1");
      expect(features.hostname).toBe("test-host");
      expect(features.arch).toBe("x86_64");
      expect(features.username).toBe("testuser");
      expect(features.macAddresses).toEqual(["00:11:22:33:44:55"]);
      expect(features.osRelease).toBe("5.15.0");
      expect(features.kernelVersion).toBe("Linux version 5.15.0");
      expect(features.telemetryDevDeviceId).toBe("telemetry-123");
      expect(features.osMachineId).toBe("mock-machine-id");
    });

    it("should generate a valid feature vector", async () => {
      const clientData: ClientSpecificData = {
        clientVersion: "1.0.0",
        clientMachineId: "client-machine-123",
        telemetryDevDeviceId: "telemetry-123",
      };

      // Mock file system operations
      (fs.lstat as jest.Mock).mockResolvedValue({ ino: 12345 });
      (fs.readFile as jest.Mock).mockRejectedValue(new Error("No SSH key"));

      const execAsync = promisify(exec) as jest.Mock;
      execAsync.mockRejectedValue(new Error("No git"));

      const features = await createFeatures(clientData);
      const vector = features.toVector();

      // Check that all required keys are present
      expect(vector[FeatureVectorKey.vscode]).toBeDefined();
      expect(vector[FeatureVectorKey.machineId]).toBeDefined();
      expect(vector[FeatureVectorKey.os]).toBeDefined();
      expect(vector[FeatureVectorKey.cpu]).toBeDefined();
      expect(vector[FeatureVectorKey.memory]).toBeDefined();
      expect(vector[FeatureVectorKey.checksum]).toBeDefined();

      // All values except checksum should be 64-character hex strings (SHA-256)
      const checksumKey = Number(FeatureVectorKey.checksum);
      const nonChecksumEntries = Object.entries(vector).filter(
        ([key]) => Number(key) !== checksumKey,
      );
      nonChecksumEntries.forEach(([, value]) => {
        expect(value).toMatch(/^[a-f0-9]{64}$/);
      });

      // Checksum should start with "v1#"
      expect(vector[FeatureVectorKey.checksum]).toMatch(/^v1#[a-f0-9]{64}$/);
    });

    it("should handle missing optional data gracefully", async () => {
      const clientData: ClientSpecificData = {
        clientVersion: "1.0.0",
        clientMachineId: "client-machine-123",
        telemetryDevDeviceId: "telemetry-123",
      };

      // Mock all optional operations to fail
      (fs.lstat as jest.Mock).mockRejectedValue(new Error("No file"));
      (fs.readFile as jest.Mock).mockRejectedValue(new Error("No SSH key"));

      const execAsync = promisify(exec) as jest.Mock;
      execAsync.mockRejectedValue(new Error("No git"));

      const features = await createFeatures(clientData);

      // Should still create features with empty strings for optional fields
      expect(features.gitUserEmail).toBe("");
      expect(features.sshPublicKey).toBe("");
      expect(features.homeDirectoryIno).toBe("");
      expect(features.projectRootIno).toBe("");
    });

    it("should collect git user email when available", async () => {
      const clientData: ClientSpecificData = {
        clientVersion: "1.0.0",
        clientMachineId: "client-machine-123",
        telemetryDevDeviceId: "telemetry-123",
        projectRootPath: "/home/<USER>/project",
      };

      // Mock file system operations
      (fs.lstat as jest.Mock).mockResolvedValue({ ino: 12345 });
      (fs.readFile as jest.Mock).mockRejectedValue(new Error("No SSH key"));

      // Mock git config to return email
      const execAsync = promisify(exec) as jest.Mock;
      execAsync.mockResolvedValue({ stdout: "<EMAIL>\n" });

      const features = await createFeatures(clientData);

      expect(features.gitUserEmail).toBe("<EMAIL>");
    });

    it("should collect SSH public key when available", async () => {
      const clientData: ClientSpecificData = {
        clientVersion: "1.0.0",
        clientMachineId: "client-machine-123",
        telemetryDevDeviceId: "telemetry-123",
      };

      // Mock file system operations
      (fs.lstat as jest.Mock).mockResolvedValue({ ino: 12345 });

      // Mock SSH key read
      (fs.readFile as jest.Mock).mockResolvedValue(
        "ssh-rsa AAAAB3NzaC1yc2EA... user@host",
      );

      const execAsync = promisify(exec) as jest.Mock;
      execAsync.mockRejectedValue(new Error("No git"));

      const features = await createFeatures(clientData);

      expect(features.sshPublicKey).toBe(
        "ssh-rsa AAAAB3NzaC1yc2EA... user@host",
      );
    });
  });
});

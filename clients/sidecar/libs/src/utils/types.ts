/* eslint-enable @typescript-eslint/naming-convention */

// These values are based on the gRPC statuscodes
// https://grpc.github.io/grpc/core/md_doc_statuscodes.html
export enum APIStatus {
  ok,
  cancelled,
  unknown,
  unavailable,
  unimplemented,
  invalidArgument,
  resourceExhausted,
  unauthenticated,
  permissionDenied,
  deadlineExceeded,
  // Non-gRPC status codes are prefixed with augment
  augmentTooLarge,
}

// Error codes that match the ErrorCode enum in public_api.proto
export enum ErrorCode {
  ERROR_UNSPECIFIED = 0,
  INVALID_TOOL_DEFINITION = 1,
  DUPLICATE_TOOL_NAMES = 2,
  IMAGE_PROCESSING_ERROR = 3,
  IMAGE_EMPTY = 4,
  IMAGE_INVALID_BASE64 = 5,
  INVALID_TOOL_USE_HISTORY = 6,
  PROMPT_LENGTH_EXCEEDED = 7,
  INPUT_COMPONENT_LENGTH_EXCEEDED = 8,
}

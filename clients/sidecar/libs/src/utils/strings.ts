/**
 * Truncates a string to a maximum length.
 */
export function truncate(
  str: string,
  maxLen: number,
  preserveTail = false,
): string {
  // we use an array to handle unicode characters to handle chars larger than 2 bytes.
  const arr = [...str];
  if (arr.length <= maxLen) {
    return str;
  }
  if (preserveTail) {
    if (maxLen <= 3) {
      return "...";
    }
    return "..." + arr.slice(3 - maxLen).join("");
  } else {
    return arr.slice(0, Math.max(maxLen - 3, 0)).join("") + "...";
  }
}

/**
 * Truncates a string in the middle if it is too large.
 *
 * If it truncates the string, it adds a truncation message in the middle.
 * Thus the total length of the string can be slightly above maxResultBytes.
 *
 * @param text the tool use response being processed
 * @param maxResultBytes maximum tool output desired, in bytes
 */
export function truncateMiddle(text: string, maxResultBytes: number): string {
  if (text.length <= maxResultBytes || text.length === 0) {
    return text;
  }

  const lines = text.split("\n");
  // Best effort detection of CRLF
  const useCRLF = lines[0].endsWith("\r");
  const truncationLine =
    "... additional lines truncated ..." + (useCRLF ? "\r" : "");

  let shortText = "";
  // Handle the case where even just the first line + last line don't fit
  if (
    lines.length < 2 ||
    lines[0].length + lines[lines.length - 1].length + truncationLine.length >
      maxResultBytes
  ) {
    const halfBudget = Math.floor(maxResultBytes / 2);
    shortText = [
      text.slice(0, halfBudget),
      "<...>",
      text.slice(-halfBudget),
    ].join("");
  } else {
    // Handle multi-line case
    const headLines: string[] = [];
    const tailLines: string[] = [];
    let currentLength = truncationLine.length + 1; // +1 for newline

    for (let i = 0; i < Math.floor(lines.length / 2); i++) {
      const nextHeadLine = lines[i];
      const nextTailLine = lines[lines.length - 1 - i];

      const additionalLength = nextHeadLine.length + nextTailLine.length + 2; // +2 for newlines
      if (currentLength + additionalLength > maxResultBytes) {
        break;
      }

      currentLength += additionalLength;
      headLines.push(nextHeadLine);
      tailLines.push(nextTailLine);
    }

    headLines.push(truncationLine);
    headLines.push(...tailLines.reverse());
    shortText = headLines.join("\n");
  }

  return shortText;
}

import { FrontmatterParser } from "@augment-internal/sidecar-libs/src/utils/FrontmatterParser";
import { type Rule, RuleType } from "../chat/chat-types";

export class RulesParser {
  private static ALWAYS_APPLY_FRONTMATTER_KEY = "alwaysApply";
  public static parseRuleFile(content: string, fileName: string): Rule {
    const shouldAlwaysApply = FrontmatterParser.parseBoolean(
      content,
      this.ALWAYS_APPLY_FRONTMATTER_KEY,
      true,
    );
    const markdownContent = FrontmatterParser.extractContent(content);
    return {
      type: shouldAlwaysApply ? RuleType.ALWAYS_ATTACHED : RuleType.MANUAL,
      path: fileName,
      content: markdownContent,
    };
  }
  public static formatRuleFileForMarkdown(rule: Rule): string {
    return FrontmatterParser.updateFrontmatter(
      rule.content,
      this.ALWAYS_APPLY_FRONTMATTER_KEY,
      rule.type === RuleType.ALWAYS_ATTACHED,
    );
  }

  public static getAlwaysApplyFrontmatterKey(content: string): boolean {
    return FrontmatterParser.parseBoolean(
      content,
      this.ALWAYS_APPLY_FRONTMATTER_KEY,
      true,
    );
  }

  public static extractContent(content: string): string {
    return FrontmatterParser.extractContent(content);
  }

  public static updateAlwaysApplyFrontmatterKey(
    content: string,
    newValue: boolean,
  ): string {
    return FrontmatterParser.updateFrontmatter(
      content,
      this.ALWAYS_APPLY_FRONTMATTER_KEY,
      newValue,
    );
  }
}

export const AUGMENT_DIRECTORY_ROOT = ".augment";
export const AUGMENT_RULES_FOLDER = "rules";

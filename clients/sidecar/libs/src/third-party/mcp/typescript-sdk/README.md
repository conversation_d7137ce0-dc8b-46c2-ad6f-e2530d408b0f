This is a subset of the MCP TypeScript SDK.

The MCP TypeScript SDK (https://github.com/modelcontextprotocol/typescript-sdk) is distributed
as an ES Module only, while VSCode extensions can only import CommonJS modules. See
https://github.com/microsoft/vscode/issues/130367 for more context.

Here is how we addressed this:
- a subset of the SDK is in this directory
- the code was largely unmodified, apart from eslint suppressions
- a dependence on the zod module was added to the VSCode extension

import * as path from "node:path";
import { getClientWorkspaces } from "../../client-interfaces/client-workspaces";
import { getLogger } from "../../logging";
import { QualifiedPathName } from "../../workspace/qualified-path-name";
import { findLongestCommonSubsequence } from "../../utils/find-lcs";
import { isSuffixPath } from "../../utils/path-utils";

// Because it is better to show truncated message specific to `str_replace_editor` tool
// this value should be smaller than max chars allowed for tool output
// which is currently set to `maxResultBytes = 64 * 1024` here
// https://github.com/augmentcode/augment/blob/main/clients/common/webviews/src/apps/chat/models/tools-webview-model.ts#L54
const MAX_RESPONSE_LEN = 50000;
const TRUNCATED_MESSAGE =
  "<response clipped><NOTE>To save on context only part of this file has been shown to you.</NOTE>";

export function validatePath(path: unknown): string {
  if (path === undefined) {
    throw new Error("Missing required parameter `path`");
  }
  if (typeof path !== "string") {
    throw new Error("Invalid parameter `path`. It must be a string.");
  }
  if (path.trim() === "") {
    throw new Error("Invalid parameter `path`. It must not be empty.");
  }
  return path;
}

/**
 * Truncates content if it exceeds the maximum length
 */
export function maybeTruncate(
  content: string,
  truncateAfter: number = MAX_RESPONSE_LEN,
): string {
  return content.length <= truncateAfter
    ? content
    : content.slice(0, truncateAfter) + TRUNCATED_MESSAGE;
}

/**
 * Normalizes line endings in a string to LF
 */
export function normalizeLineEndings(text: string): string {
  return text.replaceAll("\r\n", "\n");
}

/**
 * Detects the line ending used in a string
 */
export function detectLineEnding(text: string): string {
  return text.includes("\r\n") ? "\r\n" : "\n";
}

/**
 * Restores the original line endings in a string
 */
export function restoreLineEndings(text: string, lineEnding: string): string {
  if (lineEnding === "\n") return text;
  return text.replaceAll("\n", lineEnding);
}

/**
 * Creates a formatted output string for displaying file content with line numbers
 */
export function formatFileContent(
  fileContent: string,
  fileDescriptor: string,
  initLine: number = 1, // 1-based indexing
  totalLines?: number, // Optional parameter for total lines in file
): string {
  fileContent = maybeTruncate(fileContent);
  fileContent = fileContent
    .split("\n")
    .map((line, i) => `${String(i + initLine).padStart(6)}\t${line}`)
    .join("\n");

  let output = `Here's the result of running \`cat -n\` on ${fileDescriptor}:\n${fileContent}\n`;

  // Add total lines information if provided
  if (totalLines !== undefined) {
    output += `Total lines in file: ${totalLines}\n`;
  }

  return output;
}

/**
 * Ranks files by how similar their path is to the original path.
 * Uses Longest Common Subsequence (LCS) algorithm to find the similarity between path segments.
 * The longer the LCS, the higher the rank.
 * If there is a tie, the file with the shortest path wins.
 *
 * @param files Array of QualifiedPathName objects to rank
 * @param originalPath The original path to compare against
 * @returns Sorted array of QualifiedPathName objects
 */
export function rankFilesByPathSimilarity(
  files: QualifiedPathName[],
  originalPath: string,
): QualifiedPathName[] {
  // Normalize the original path by removing leading/trailing slashes
  const normalizedOriginalPath = originalPath.replace(/^\/+|\/+$/g, "");
  const originalSegments = normalizedOriginalPath.split("/");

  // Calculate a score for each file based on path similarity using LCS
  const scoredFiles = files.map((file) => {
    const filePath = file.relPath;
    const fileSegments = filePath.split("/");

    // Find the longest common subsequence between the path segments
    const mapping = findLongestCommonSubsequence(
      originalSegments,
      fileSegments,
    );

    // Count how many segments are mapped (i.e., part of the LCS)
    const lcsLength = mapping.filter((index) => index !== -1).length;

    // Score is primarily based on LCS length, secondarily on shorter paths
    // Multiply LCS length by a large number to ensure it takes precedence over path length
    return {
      file,
      score: lcsLength * 1000 - fileSegments.length, // Prioritize LCS length, then shorter paths
    };
  });

  // Sort by score in descending order
  scoredFiles.sort((a, b) => b.score - a.score);

  // Return just the files in ranked order
  return scoredFiles.map((item) => item.file);
}

/**
 * Finds paths similar to the given path.
 *
 * First, it finds all files in the repo with the same name as the given file.
 * Then it ranks them by how similar their path is to the original path.
 *
 * @param path The original file path to find similar paths for
 * @param limit Maximum number of similar paths to return (default: 5)
 * @returns Promise resolving to an array of QualifiedPathName objects representing similar paths.
 */
export async function findSimilarPaths(
  path: string,
  limit: number = 5,
): Promise<QualifiedPathName[]> {
  const logger = getLogger("ToolFileUtils");

  // Extract the filename from the path
  const pathParts = path.split("/");
  const filename = pathParts[pathParts.length - 1];

  if (!filename) {
    logger.debug(`No filename found in path: ${path}`);
    return [];
  }

  logger.debug(`Searching for files with name: ${filename}`);

  try {
    // Use the glob pattern **/${filename} to find all files with the given name in any directory
    const foundFiles = await getClientWorkspaces().findFiles(
      `**/${filename}`,
      null,
      20,
      2000, // timelimit 2 sec
    );

    if (foundFiles.length === 0) {
      logger.debug(`No files found with name: ${filename}`);
      return [];
    }

    const rankedFiles = rankFilesByPathSimilarity(foundFiles, path);

    return rankedFiles.slice(0, limit);
  } catch (error) {
    logger.error(
      `Error finding similar paths for ${path}: ${error instanceof Error ? error.message : String(error)}`,
    );
    return [];
  }
}

export interface PathCorrectionResult {
  corrected: boolean;
  correctedPath?: string;
  correctionNote?: string;
  /** Similar paths found for suggestions if correction failed */
  similarPaths: QualifiedPathName[];
}

/**
 * Attempts to auto-correct a path by finding similar paths and checking for suffix matches.
 * Only auto-corrects relative paths (not absolute paths).
 *
 * @param inputPath The original path that was not found
 * @returns PathCorrectionResult with correction information
 */
export async function attemptPathAutoCorrection(
  inputPath: string,
): Promise<PathCorrectionResult> {
  const logger = getLogger("ToolFileUtils");

  // Find similar paths
  const similarPaths = await findSimilarPaths(inputPath);

  // Only auto-correct if the path is relative (not absolute)
  if (!path.isAbsolute(inputPath) && similarPaths.length > 0) {
    // Find paths that contain the given path as a suffix
    const pathsWithSuffix = similarPaths.filter((similarPath) => {
      return isSuffixPath(similarPath.relPath, inputPath);
    });

    // If there's exactly one path with the input path as a suffix, use it automatically
    // If there are multiple paths with the input path as a suffix there is ambiguity and we can't auto-correct
    if (pathsWithSuffix.length === 1) {
      // Use the absolute path to minimize ambiguity in future
      const correctedPath: string = pathsWithSuffix[0].absPath;
      logger.debug(
        `Automatically correcting path from ${inputPath} to ${correctedPath}`,
      );

      const correctionNote = `Note: Path was automatically corrected from '${inputPath}' to '${correctedPath}'.`;
      return {
        corrected: true,
        correctedPath,
        correctionNote,
        similarPaths,
      };
    }
  }

  // No correction was made
  return {
    corrected: false,
    similarPaths,
  };
}

export function genSimilarPathsResponse(
  path: string,
  similarPaths: QualifiedPathName[],
): string {
  const logger = getLogger("ToolFileUtils");
  if (similarPaths.length > 0) {
    // Use full absolute paths for suggestions to avoid ambiguity
    const suggestions = similarPaths.map((p) => p.absPath).join("\n");
    logger.info(
      `File not found: ${path}. Similar files found:\n${suggestions}`,
    );
    return `File not found: ${path}. Did you mean one of these?\n${suggestions}`;
  } else {
    logger.info(`File not found: ${path}. No similar files found`);
    return `File not found: ${path}`;
  }
}

export async function readFile(inputPath: string): Promise<{
  contents: string;
  filepath?: QualifiedPathName;
  correctedPath?: string;
  correctionNote?: string;
}> {
  const logger = getLogger("ToolFileUtils");
  logger.info(`Reading file: ${inputPath}`);

  const fileDetails = await getClientWorkspaces().readFile(inputPath);
  if (
    fileDetails !== undefined &&
    fileDetails.contents !== undefined &&
    fileDetails.filepath !== undefined
  ) {
    logger.info(
      `Successfully read file: ${inputPath} (${fileDetails.contents.length} bytes)`,
    );
    return { contents: fileDetails.contents, filepath: fileDetails.filepath };
  }

  // If direct read fails, attempt path auto-correction
  const correctionResult = await attemptPathAutoCorrection(inputPath);

  if (correctionResult.corrected && correctionResult.correctedPath) {
    // Try to read the corrected path
    const correctedFileDetails = await getClientWorkspaces().readFile(
      correctionResult.correctedPath,
    );
    if (
      correctedFileDetails !== undefined &&
      correctedFileDetails.contents !== undefined &&
      correctedFileDetails.filepath !== undefined
    ) {
      logger.info(
        `Successfully read corrected file: ${correctionResult.correctedPath} (${correctedFileDetails.contents.length} bytes)`,
      );
      return {
        contents: correctedFileDetails.contents,
        filepath: correctedFileDetails.filepath,
        correctedPath: correctionResult.correctedPath,
        correctionNote: correctionResult.correctionNote,
      };
    }
  }

  // If we get here, either there are multiple paths with the suffix, none,
  // or the path is absolute, so we show the similar paths as suggestions
  throw new Error(
    genSimilarPathsResponse(inputPath, correctionResult.similarPaths),
  );
}

/**
 * @file task-tools.ts
 * This file contains tools for the agent to interact with the task system.
 */

import { Exchange } from "../../chat/chat-types";
import { getLogger } from "../../logging";
import { errorToolResponse, successToolResponse } from "./tool-use-response";
import { ToolBase, ToolSafety, ToolUseResponse } from "../tool-types";
import { SidecarToolType } from "./sidecar-tool-types";
import { TaskManager } from "../../agent/task/task-manager";
import { TaskUpdatedBy } from "../../agent/task/task-types";
import {
  getMarkdownRepresentation,
  parseMarkdownToTaskTree,
  diffTaskTrees,
  taskDiffToMarkdown,
} from "../../agent/task/task-utils";

/**
 * Tool for viewing the current conversation's task list.
 * This tool retrieves the root task for the current conversation
 * and returns its markdown representation.
 */
export class ViewTaskListTool extends ToolBase<SidecarToolType.viewTaskList> {
  private readonly _logger = getLogger("ViewTaskListTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.viewTaskList, ToolSafety.Safe);
  }

  public description = "View the current task list for the conversation.";

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {},
    required: [],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    _toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
      if (!rootTaskUuid) {
        return errorToolResponse("No root task found.");
      }

      // Get the hydrated task tree
      const rootTask = await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!rootTask) {
        return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
      }

      // Convert the task tree to markdown
      const markdown = getMarkdownRepresentation(rootTask);

      return successToolResponse(
        `# Current Task List\n\n${markdown}\n\n` +
          `To update this task list, use the update_tasklist tool.\n\n` +
          `IMPORTANT: When updating the task list:\n` +
          `1. Maintain the proper hierarchy with correct indentation:\n` +
          `   - Root tasks have no dashes: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
          `   - Level 1 tasks have one dash: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
          `   - Level 2 tasks have two dashes: --[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
          `   - Every sub-task MUST have a parent task one level above it\n` +
          `   - New tasks should use UUID:NEW_UUID\n\n` +
          `2. Use the correct task state markers:\n` +
          `   - [ ] = Not started (for tasks you haven't begun working on yet)\n` +
          `   - [/] = In progress (for tasks you're currently working on)\n` +
          `   - [-] = Cancelled (for tasks that are no longer relevant)\n` +
          `   - [x] = Completed (for tasks the user has confirmed are complete)\n\n` +
          `3. Update task states as you work:\n` +
          `   - Start with tasks in the not started state ([ ])\n` +
          `   - Mark tasks as in progress ([/]) when you start working on them\n` +
          `   - Mark tasks as completed ([x]) when they are done\n` +
          `   - Mark tasks as cancelled ([-]) if they're no longer needed`,
      );
    } catch (error) {
      this._logger.error("Error in ViewTaskListTool:", error);
      return errorToolResponse(
        `Failed to view task list: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

/**
 * Tool for updating the current conversation's task list.
 * This tool takes a markdown representation of tasks from the agent,
 * parses it back into a task tree, and applies the changes to the
 * existing task tree.
 */
export class UpdateTaskListTool extends ToolBase<SidecarToolType.updateTaskList> {
  private readonly _logger = getLogger("UpdateTaskListTool");

  constructor(private readonly _taskManager: TaskManager) {
    super(SidecarToolType.updateTaskList, ToolSafety.Safe);
  }

  public description = "Update the task list for the current conversation.";

  public inputSchemaJson = JSON.stringify({
    type: "object",
    properties: {
      markdown: {
        type: "string",
        description:
          "The markdown representation of the task list to update. Should be in the format specified by the view_tasklist tool. New tasks should have a UUID of 'NEW_UUID'. IMPORTANT: Task hierarchy is determined by indentation with dashes (-). Each sub-task MUST be indented with one more dash than its parent task. Root tasks have no dashes, level 1 tasks have one dash (-), level 2 tasks have two dashes (--), etc. Every task at level N (N > 0) MUST have a parent task at level N-1. Skipping levels (e.g., going from root to -- without a - level) will cause parsing errors. Each task must follow the format: '[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz' where the state marker indicates: [ ] = not started, [/] = in progress, [-] = cancelled, [x] = completed (only if user has confirmed the task is complete). Remember to update task states as you work: start with [ ] for new tasks, mark as [/] when you start working on them, mark as [x] when the user confirms they are done, and use [-] for tasks that are no longer needed.",
      },
    },
    required: ["markdown"],
  });

  public checkToolCallSafe(): boolean {
    return true; // This tool is always safe
  }

  public async call(
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
    _abortSignal: AbortSignal,
  ): Promise<ToolUseResponse> {
    try {
      // Get the markdown from the tool input
      const markdown = toolInput.markdown as string;
      if (!markdown) {
        return errorToolResponse("No markdown provided.");
      }

      const rootTaskUuid = this._taskManager.getCurrentRootTaskUuid();
      if (!rootTaskUuid) {
        return errorToolResponse("No root task found.");
      }

      // Get the existing task tree
      const existingRootTask =
        await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!existingRootTask) {
        return errorToolResponse(`Task with UUID ${rootTaskUuid} not found.`);
      }

      // Parse the markdown into a new task tree
      let newRootTask;
      try {
        newRootTask = parseMarkdownToTaskTree(markdown);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        return errorToolResponse(
          `Failed to parse markdown: ${errorMessage}\n\n` +
            `IMPORTANT: Make sure each task follows the correct hierarchy:\n` +
            `- Root tasks have no dashes: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
            `- Level 1 tasks have one dash: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
            `- Level 2 tasks have two dashes: --[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz\n` +
            `- Every task at level N (N > 0) MUST have a parent task at level N-1\n` +
            `- Do not skip levels (e.g., going from root to -- without a - level)\n` +
            `- Ensure all tasks have a UUID, NAME, and DESCRIPTION in that order\n` +
            `- Use the correct state marker: [ ] = not started, [/] = in progress, [-] = cancelled, [x] = completed,\n` +
            `- Remember to update task states as you work: mark as in progress ([/]) when starting, mark as completed ([x]) when finished, and mark as cancelled ([-]) if they're no longer needed.`,
        );
      }

      // Preserve the root task's UUID
      newRootTask.uuid = rootTaskUuid;

      // Generate diff before making changes
      const diff = diffTaskTrees(existingRootTask, newRootTask);
      const diffMarkdown = taskDiffToMarkdown(diff);

      // Use the TaskManager's updateHydratedTask method to handle all changes at once
      const result = await this._taskManager.updateHydratedTask(
        newRootTask,
        TaskUpdatedBy.AGENT,
      );

      // Extract counts from the result
      const {
        created: createdCount,
        updated: updatedCount,
        deleted: deletedCount,
      } = result;

      // Get the updated task tree to show the new UUIDs
      const updatedRootTask =
        await this._taskManager.getHydratedTask(rootTaskUuid);
      if (!updatedRootTask) {
        return errorToolResponse(`Failed to retrieve updated task tree.`);
      }

      // Build response with task update information
      let responseMessage = `Task list updated successfully. Created: ${createdCount}, Updated: ${updatedCount}, Deleted: ${deletedCount}.\n\n`;

      // Add the diff markdown if there are changes
      if (diffMarkdown.trim()) {
        responseMessage += `# Task Changes\n\n${diffMarkdown}\n`;
      }

      if (createdCount > 0 || updatedCount > 0) {
        responseMessage += `New and Updated Tasks:\n`;

        // Use utility to generate markdown for created and updated tasks
        const createdAndUpdatedDiff = {
          created: diff.created,
          updated: diff.updated,
          deleted: [], // Don't show deleted tasks in this section
        };
        const createdAndUpdatedMarkdown = taskDiffToMarkdown(
          createdAndUpdatedDiff,
        );

        if (createdAndUpdatedMarkdown.trim()) {
          responseMessage += createdAndUpdatedMarkdown + `\n`;
        }
      }

      responseMessage +=
        `Remember: When updating the task list in the future:\n` +
        `- Mark tasks as in progress ([/]) when you start working on them\n` +
        `- Mark tasks as completed ([x]) when the user explicitly confirms they are done\n` +
        `- Mark tasks as cancelled ([-]) if they're no longer needed\n` +
        `- Use the correct UUID for existing tasks. Make sure to use the generated UUIDs for new tasks.`;

      return successToolResponse(responseMessage);
    } catch (error) {
      this._logger.error("Error in UpdateTaskListTool:", error);
      return errorToolResponse(
        `Failed to update task list: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

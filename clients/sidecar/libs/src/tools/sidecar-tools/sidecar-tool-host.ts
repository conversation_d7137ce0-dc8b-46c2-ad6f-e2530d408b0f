import { ChatM<PERSON> } from "../../chat/chat-types";
import { IClientFeatureFlags } from "../../client-interfaces/feature-flags";
import { IToolHost } from "../tool-host";
import { ITool, ToolHostBase } from "../tool-host-base";
import { CodebaseRetrievalTool } from "./codebase-retrieval";
import { ToolHostName } from "../tool-types";
import { AgentEditTools } from "./sidecar-tool-types";
import { RemoveFilesTool } from "./remove-files-tool";
import { SaveFileTool } from "./save-file-tool";
import { StrReplaceEditorTool } from "./str-replace-editor-tool/str-replace-editor-tool";
import { ViewTool } from "./view-tool/view-tool";
import { WebFetchTool } from "./web-fetch";
import { AggregateCheckpointManager } from "../../agent/checkpoint/aggregate-checkpoint-manager";
import { SidecarToolType } from "./sidecar-tool-types";
import { RememberTool } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/remember-tool";
import { MemoryUpdateManager } from "../../agent/memory/memory-update-manager";
import { SimpleShellTool } from "./simple-shell";
import { getDefaultShell } from "./shell-utils";
import { TaskManager } from "../../agent/task/task-manager";
import { ViewTaskListTool, UpdateTaskListTool } from "./task-tools";
import { RenderMermaidTool } from "./render-mermaid-tool/render-mermaid-tool";
import { GrepSearchTool } from "./grep-search-tool";

/**
 * A tool host for tools that run in the sidecar (i.e. a node environment).
 */
export class SidecarToolHost extends ToolHostBase<SidecarToolType> {
  private readonly _memoryUpdateManager: MemoryUpdateManager;

  constructor(
    private readonly _chatMode: ChatMode,
    private readonly _clientFeatureFlags: IClientFeatureFlags,
    private readonly _checkpointManager: AggregateCheckpointManager,
    private readonly _getAgentMemories: () => Promise<string | undefined>,
    private readonly _getAgentMemoriesAbsPath: () => string | undefined,
    private readonly _unsupportedSidecarTools: Set<SidecarToolType>,
    private readonly _userAgent?: string,
    private readonly _taskManager: TaskManager | undefined = undefined,
  ) {
    // Create the memory update manager first
    const memoryUpdateManager = new MemoryUpdateManager();

    // Create the tools array
    const tools: ITool<SidecarToolType>[] = [];
    if (_chatMode === ChatMode.remoteAgent) {
      // Remote agent is always configured with the same sidecar tools
      tools.push(
        new WebFetchTool(_userAgent),
        new CodebaseRetrievalTool(),
        new RemoveFilesTool(_checkpointManager),
        new SaveFileTool(_checkpointManager),
        new StrReplaceEditorTool(_checkpointManager),
        new ViewTool(),
        new SimpleShellTool(
          _chatMode,
          process.platform,
          getDefaultShell(process.platform) as string,
        ),
        // Add RenderMermaidTool for Remote Agent mode
        new RenderMermaidTool(),
      );
      if (_clientFeatureFlags.flags.grepSearchToolEnable) {
        tools.push(new GrepSearchTool());
      }
    } else {
      tools.push(new WebFetchTool(_userAgent), new CodebaseRetrievalTool());

      if (_chatMode === ChatMode.agent) {
        tools.push(new RemoveFilesTool(_checkpointManager));
        tools.push(new SaveFileTool(_checkpointManager));
        if (_taskManager && _clientFeatureFlags.flags.enableTaskList) {
          tools.push(
            new ViewTaskListTool(_taskManager),
            new UpdateTaskListTool(_taskManager),
          );
        }
      }

      if (_chatMode === ChatMode.agent) {
        tools.push(
          new RememberTool(
            _getAgentMemories,
            _getAgentMemoriesAbsPath,
            memoryUpdateManager,
          ),
        );
        // Add RenderMermaidTool for Agent mode
        tools.push(new RenderMermaidTool());

        if (_clientFeatureFlags.flags.grepSearchToolEnable) {
          tools.push(new GrepSearchTool());
        }
      }

      if (
        _chatMode === ChatMode.agent &&
        _clientFeatureFlags.flags.agentEditTool ===
          AgentEditTools.strReplaceEditor
      ) {
        tools.push(new StrReplaceEditorTool(_checkpointManager));
        tools.push(new ViewTool());
      }
    }

    // Call super with the tools array
    super(tools, ToolHostName.sidecarToolHost, _unsupportedSidecarTools);

    // Now we can set the instance variable
    this._memoryUpdateManager = memoryUpdateManager;
  }

  factory(): IToolHost {
    return new SidecarToolHost(
      this._chatMode,
      this._clientFeatureFlags,
      this._checkpointManager,
      this._getAgentMemories,
      this._getAgentMemoriesAbsPath,
      this._unsupportedSidecarTools,
      this._userAgent,
      this._taskManager,
    );
  }

  /**
   * Gets the memory update manager instance.
   * This can be used to register callbacks for memory update events.
   */
  public getMemoryUpdateManager(): MemoryUpdateManager {
    return this._memoryUpdateManager;
  }
}

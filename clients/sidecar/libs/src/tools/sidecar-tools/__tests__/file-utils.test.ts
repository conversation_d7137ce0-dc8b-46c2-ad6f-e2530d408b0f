import {
  formatFileContent,
  maybeTruncate,
  normalizeLineEndings,
  detectLineEnding,
  restoreLineEndings,
  rankFilesByPathSimilarity,
  findSimilarPaths,
} from "../file-utils";
import { QualifiedPathName } from "../../../workspace/qualified-path-name";

describe("formatFileContent", () => {
  it("should format output with line numbers", () => {
    const content = "line 1\nline 2\nline 3";
    const result = formatFileContent(content, "test.txt");
    expect(result).toContain("     1\tline 1");
    expect(result).toContain("     2\tline 2");
    expect(result).toContain("     3\tline 3");
  });

  it("should start line numbering from specified line", () => {
    const content = "line 1\nline 2";
    const result = formatFileContent(content, "test.txt", 10);
    expect(result).toContain("    10\tline 1");
    expect(result).toContain("    11\tline 2");
  });

  it("should include total lines if provided", () => {
    const content = "line 1\nline 2";
    const result = formatFileContent(content, "test.txt", 1, 100);
    expect(result).toContain("Total lines in file: 100");
  });

  it("should not include total lines in the output when not provided", () => {
    const result = formatFileContent("line1\nline2", "test.txt", 1);
    expect(result).not.toContain("Total lines in file:");
  });
});

describe("maybeTruncate", () => {
  it("should not truncate content below the limit", () => {
    const content = "short content";
    expect(maybeTruncate(content, 20)).toBe(content);
  });

  it("should truncate content above the limit", () => {
    const content = "this is a long content that should be truncated";
    const truncated = maybeTruncate(content, 10);
    expect(truncated.length).toBeGreaterThan(10); // Because of the truncated message
    expect(truncated).toContain("this is a ");
    expect(truncated).toContain("<response clipped>");
  });
});

describe("normalizeLineEndings", () => {
  it("should convert CRLF to LF", () => {
    const input = "line 1\r\nline 2\r\nline 3";
    const expected = "line 1\nline 2\nline 3";
    expect(normalizeLineEndings(input)).toBe(expected);
  });

  it("should leave LF unchanged", () => {
    const input = "line 1\nline 2\nline 3";
    expect(normalizeLineEndings(input)).toBe(input);
  });
});

describe("detectLineEnding", () => {
  it("should detect CRLF line endings", () => {
    expect(detectLineEnding("line 1\r\nline 2")).toBe("\r\n");
  });

  it("should default to LF for files without CRLF", () => {
    expect(detectLineEnding("line 1\nline 2")).toBe("\n");
  });
});

describe("restoreLineEndings", () => {
  it("should convert LF back to CRLF if needed", () => {
    const input = "line 1\nline 2\nline 3";
    expect(restoreLineEndings(input, "\r\n")).toBe(
      "line 1\r\nline 2\r\nline 3",
    );
  });

  it("should leave text unchanged if line ending is LF", () => {
    const input = "line 1\nline 2\nline 3";
    expect(restoreLineEndings(input, "\n")).toBe(input);
  });
});

describe("rankFilesByPathSimilarity", () => {
  it("should rank files by path similarity", () => {
    const files = [
      new QualifiedPathName("/root", "some/other/path/file.txt"),
      new QualifiedPathName("/root", "path/to/file.txt"),
      new QualifiedPathName("/root", "different/path/to/file.txt"),
      new QualifiedPathName("/root", "to/file.txt"),
    ];

    const originalPath = "path/to/file.txt";
    const ranked = rankFilesByPathSimilarity(files, originalPath);

    // Check that all paths are present in the results
    const paths = ranked.map((r) => r.relPath);
    expect(paths).toContain("path/to/file.txt");
    expect(paths).toContain("to/file.txt");
    expect(paths).toContain("different/path/to/file.txt");
    expect(paths).toContain("some/other/path/file.txt");

    // The exact match should be first
    expect(ranked[0].relPath).toBe("path/to/file.txt");
  });

  it("should handle empty file list", () => {
    const files: QualifiedPathName[] = [];
    const originalPath = "path/to/file.txt";
    const ranked = rankFilesByPathSimilarity(files, originalPath);
    expect(ranked).toEqual([]);
  });

  it("should handle paths with different depths", () => {
    const files = [
      new QualifiedPathName("/root", "very/deep/path/structure/file.txt"),
      new QualifiedPathName("/root", "file.txt"),
    ];

    const originalPath = "path/to/file.txt";
    const ranked = rankFilesByPathSimilarity(files, originalPath);

    // Check that both paths are present in the results
    const paths = ranked.map((r) => r.relPath);
    expect(paths).toContain("file.txt");
    expect(paths).toContain("very/deep/path/structure/file.txt");
  });

  it("should normalize paths with leading/trailing slashes", () => {
    const files = [
      new QualifiedPathName("/root", "path/to/file.txt"),
      new QualifiedPathName("/root", "other/path/file.txt"),
    ];

    const originalPath = "/path/to/file.txt/";
    const ranked = rankFilesByPathSimilarity(files, originalPath);

    // Should still match correctly despite the slashes
    expect(ranked[0].relPath).toBe("path/to/file.txt");
    expect(ranked[1].relPath).toBe("other/path/file.txt");
  });
});

// Mock the client-workspaces module for testing findSimilarPaths
jest.mock("../../../client-interfaces/client-workspaces", () => {
  return {
    getClientWorkspaces: jest.fn().mockReturnValue({
      findFiles: jest.fn().mockImplementation((includeGlob) => {
        if (includeGlob === "**/test-file.txt") {
          return Promise.resolve([
            new QualifiedPathName("/root", "path/to/test-file.txt"),
            new QualifiedPathName("/root", "other/path/test-file.txt"),
            new QualifiedPathName("/root", "another/test-file.txt"),
          ]);
        }
        return Promise.resolve([]);
      }),
      readFile: jest.fn(),
    }),
  };
});

// Mock the logging module
jest.mock("../../../logging", () => {
  return {
    getLogger: jest.fn().mockReturnValue({
      debug: jest.fn(),
      info: jest.fn(),
      error: jest.fn(),
    }),
  };
});

describe("findSimilarPaths", () => {
  it("should find and rank similar paths", async () => {
    const similarPaths = await findSimilarPaths("path/to/test-file.txt", 2);

    // Should find 2 paths (limited by the limit parameter)
    expect(similarPaths.length).toBe(2);

    // The first path should be the exact match
    expect(similarPaths[0].relPath).toBe("path/to/test-file.txt");
  });

  it("should return empty array when no similar paths found", async () => {
    const similarPaths = await findSimilarPaths("non-existent-file.txt");

    expect(similarPaths).toEqual([]);
  });

  it("should return empty array when path has no filename", async () => {
    const similarPaths = await findSimilarPaths("path/to/");

    expect(similarPaths).toEqual([]);
  });
});

import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { SidecarToolHost } from "../sidecar-tool-host";

import { SidecarToolType } from "../sidecar-tool-types";
import { MockClientWorkspaces } from "@augment-internal/sidecar-libs/src/__tests__/mocks/mock-client-workspaces";
import { MockClientFeatureFlags } from "@augment-internal/sidecar-libs/src/__tests__/mocks/mock-client-feature-flags";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { setLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { RememberTool } from "../remember-tool";
import { setLibraryClientFeatureFlags } from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";

function createMockCheckpointManager() {
  return new AggregateCheckpointManager(
    {
      save: () => Promise.resolve(),
      load: () => Promise.resolve(undefined),
      saveShard: () => Promise.resolve(),
      loadShard: () => Promise.resolve(undefined),
      saveManifest: () => Promise.resolve(),
      loadManifest: () => Promise.resolve(undefined),
      deleteShard: () => Promise.resolve(),
    },
    () => undefined,
    () => ({ dispose: () => {} }),
    () => ({ dispose: () => {} }),
    () => ({ dispose: () => {} }),
  );
}

describe("SidecarToolHost", () => {
  let mockClientWorkspaces: MockClientWorkspaces;

  beforeEach(() => {
    mockClientWorkspaces = new MockClientWorkspaces();
    setLibraryClientWorkspaces(mockClientWorkspaces);
    setLibraryClientFeatureFlags(new MockClientFeatureFlags());
  });

  it("all agent tools are available in agent mode when enabled", async () => {
    mockClientWorkspaces.getCwd.mockReturnValue(
      Promise.resolve("/example/cwd/path"),
    );

    const mockFeatureFlags = new MockClientFeatureFlags();
    const checkpointManager = createMockCheckpointManager();

    // Mock the getAgentMemories and getAgentMemoriesAbsPath functions
    const getAgentMemories = () => Promise.resolve("test memories");
    const getAgentMemoriesAbsPath = () => "/path/to/memories";

    const host = new SidecarToolHost(
      ChatMode.agent,
      mockFeatureFlags,
      checkpointManager,
      getAgentMemories,
      getAgentMemoriesAbsPath,
      new Set<SidecarToolType>(),
      "Test-UserAgent/1.0",
    );
    const tools = await host.getToolDefinitions();

    expect(tools.length).toBe(6);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.webFetch.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) =>
          tool.definition.name === SidecarToolType.codebaseRetrieval.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) =>
          tool.definition.name === SidecarToolType.removeFiles.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.remember.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.saveFile.toString(),
      ),
    ).toBe(true);
  });

  it("chat mode has limited set of tools when enabled", async () => {
    mockClientWorkspaces.getCwd.mockReturnValue(
      Promise.resolve("/example/cwd/path"),
    );
    const mockFeatureFlags = new MockClientFeatureFlags();
    const checkpointManager = createMockCheckpointManager();

    // Mock the getAgentMemories and getAgentMemoriesAbsPath functions
    const getAgentMemories = () => Promise.resolve("test memories");
    const getAgentMemoriesAbsPath = () => "/path/to/memories";

    const host = new SidecarToolHost(
      ChatMode.chat,
      mockFeatureFlags,
      checkpointManager,
      getAgentMemories,
      getAgentMemoriesAbsPath,
      new Set<SidecarToolType>(),
      "Test-UserAgent/1.0",
    );
    const tools = await host.getToolDefinitions();

    expect(tools.length).toBe(2);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.webFetch.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.shell.toString(),
      ),
    ).toBe(false);
    expect(
      tools.some(
        (tool) =>
          tool.definition.name === SidecarToolType.codebaseRetrieval.toString(),
      ),
    ).toBe(true);
  });

  it("remote agent mode has expected tools when enabled", async () => {
    mockClientWorkspaces.getCwd.mockReturnValue(
      Promise.resolve("/example/cwd/path"),
    );
    const mockFeatureFlags = new MockClientFeatureFlags();
    const checkpointManager = createMockCheckpointManager();

    // Mock the getAgentMemories and getAgentMemoriesAbsPath functions
    const getAgentMemories = () => Promise.resolve("test memories");
    const getAgentMemoriesAbsPath = () => "/path/to/memories";

    const host = new SidecarToolHost(
      ChatMode.remoteAgent,
      mockFeatureFlags,
      checkpointManager,
      getAgentMemories,
      getAgentMemoriesAbsPath,
      new Set<SidecarToolType>(),
      "Test-UserAgent/1.0",
    );
    const tools = await host.getToolDefinitions();

    expect(tools.length).toBe(8);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.webFetch.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) =>
          tool.definition.name === SidecarToolType.codebaseRetrieval.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) =>
          tool.definition.name === SidecarToolType.removeFiles.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) =>
          tool.definition.name === SidecarToolType.strReplaceEditor.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.shell.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.view.toString(),
      ),
    ).toBe(true);
    expect(
      tools.some(
        (tool) => tool.definition.name === SidecarToolType.saveFile.toString(),
      ),
    ).toBe(true);
  });

  it("getTool returns remember tool if available", () => {
    mockClientWorkspaces.getCwd.mockReturnValue(
      Promise.resolve("/example/cwd/path"),
    );
    const mockFeatureFlags = new MockClientFeatureFlags();
    const checkpointManager = createMockCheckpointManager();

    // Mock the getAgentMemories and getAgentMemoriesAbsPath functions
    const getAgentMemories = () => Promise.resolve("test memories");
    const getAgentMemoriesAbsPath = () => "/path/to/memories";

    const host = new SidecarToolHost(
      ChatMode.agent,
      mockFeatureFlags,
      checkpointManager,
      getAgentMemories,
      getAgentMemoriesAbsPath,
      new Set<SidecarToolType>(),
      "Test-UserAgent/1.0",
    );

    const tool = host.getTool(SidecarToolType.remember);

    expect(tool).toBeDefined();
    expect(tool).toBeInstanceOf(RememberTool);
  });
});

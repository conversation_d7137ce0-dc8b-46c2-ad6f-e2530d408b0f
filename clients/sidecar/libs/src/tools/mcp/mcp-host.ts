import { Client } from "@augment-internal/sidecar-libs/src/third-party/mcp/typescript-sdk/src/client";
import {
  getDefaultEnvironment,
  StdioClientTransport,
} from "@augment-internal/sidecar-libs/src/third-party/mcp/typescript-sdk/src/client/stdio";
import {
  CallToolResultSchema,
  Tool,
} from "@augment-internal/sidecar-libs/src/third-party/mcp/typescript-sdk/src/types";
import { truncate } from "lodash";
import { Exchange } from "../../chat/chat-types";
import { IToolHost } from "../tool-host";
import { ITool } from "../tool-host-base";
import {
  McpServerConfig,
  ToolDefinitionWithSettings,
  ToolHostName,
  ToolResponseContentNode,
  ToolResponseContentNodeType,
  ToolSafety,
  ToolStartupErrorFn,
  ToolType,
  ToolUseResponse,
} from "../tool-types";
import { execSync } from "child_process";
import {
  errorToolResponse,
  structuredNodeResponse,
  successToolResponse,
} from "../sidecar-tools/tool-use-response";
import { getLogger } from "../../logging";
import {
  validateJsonSchema,
  JsonSchemaValidationError,
} from "./json-schema-validator";

/**
 * A MCP (Model Context Protocol) host.
 *
 * The MCP host is responsible for connecting to a MCP server and
 * providing the tools functionality. Given that we currently only
 * support local servers over stdio, connecting the client automatically
 * launches the server process.
 */
export class McpHost implements IToolHost {
  /** The MCP (Model Context Protocol) client */
  private readonly _client: Client | undefined = undefined;

  /*
   * Whether the tools client is initialized.
   * Set to a defined value in constructor and cleared when it's done.
   */
  private _initializingPromise: Promise<void>;

  /**
   * Whether the tools client is closing.
   * Set to a defined value when the client is closing and never cleared.
   */
  private _closingPromise: Promise<void> | undefined = undefined;

  /** Whether the tools client was closed due to cancellation by the user */
  private _cancelledByUser: boolean = false;

  /** Cached list of tool definitions */
  private _toolDefinitions: ToolDefinitionWithSettings[] | undefined =
    undefined;

  /** Running tool, if any */
  private _runningTool: { requestId: string; toolUseId: string } | undefined =
    undefined;

  /** Server name derived from the command */
  private _serverName: string;

  /** List of tools that failed schema validation */
  private _validationErrors: JsonSchemaValidationError[] = [];

  /** The transport used to connect to the MCP server
   *
   * We need to keep a reference to the transport because the transport is used to
   * capture stderr.
   */
  private _stdErrGenerator: StdErrorGenerator | undefined = undefined;

  /**
   * Maximum timeout supported by the MCP client.
   * This is because the setTimeout implementation in Node.js treats any timeout value
   * timeout larger than 2147483647 as equivalent to setting a timeout value of 1.
   */
  private static readonly maxTimeoutMs: number = 2_147_483_647;

  /** Default timeout to use if none is specified */
  private static readonly defaultTimeoutMs: number = 2_000_000_000;

  private _logger = getLogger("McpHost");

  constructor(
    private readonly _config: McpServerConfig,
    preconditionWait: Promise<void> | undefined = undefined,
    private readonly _onStartupError: ToolStartupErrorFn,
  ) {
    // Create the MCP client
    this._client = new Client(
      {
        name: "augment-mcp-client",
        version: "1.0.0",
      },
      {
        capabilities: {},
      },
    );

    // Validate and save the config
    this.validateConfig(_config);

    // Use the user-defined name if available, otherwise extract from command
    this._serverName = McpHost.getServerName(_config.name, _config.command);

    // Initialize the client asynchronously
    this._initializingPromise = (async () => {
      // Wait for the precondition, such as closing the previous client.
      //
      // This is implemented defensively to avoid accumulating processes
      // in pathological scenarios.
      await preconditionWait;

      // Check if the client is closing.
      //
      // This could happen if there was another config update while we were waiting.
      if (this._closingPromise !== undefined) {
        throw new Error("Client is closing");
      }

      // Check if the client and tools config are defined. They should be defined,
      // but the type checker doesn't know that.
      if (this._client === undefined) {
        throw new Error("Client is undefined");
      }

      // Connect to the new server
      // Prepare the command and args based on whether we're using shell interpolation
      let command = this._config.command;
      let args = this._config.args || [];

      // If using shell interpolation, we need to modify how we pass the command
      if (this._config.useShellInterpolation) {
        // On Windows, we need to use cmd.exe /c
        if (process.platform === "win32") {
          args = ["/c", command];
          command = "cmd.exe";
        } else {
          // On Unix-like systems, we use /bin/sh -c
          args = ["-c", command];
          command = "/bin/sh";
        }
      }

      // Create the transport with the appropriate command and args
      const transport = new StdioClientTransportWithStderr({
        command: command,
        args: args,
        env: {
          ...getDefaultEnvironment(),
          ...this._config.env,
        },
        stderr: "pipe",
      });

      // Store the transport so that we can access it later
      // This must happen before we call connect() because connect() may
      // throw an error before we have a chance to store the transport.
      this._stdErrGenerator = transport;
      this._logger.debug(`Connecting to MCP server: "${_config.name}"`);
      await this._client.connect(transport);
      this._logger.debug(`Connected to MCP server: "${_config.name}"`);

      // Get the tool definitions
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const tools = await this._client.listTools();
      this._logger.debug(`${_config.name} has ${tools?.tools.length} tools}`);

      /* eslint-disable @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access */

      this._toolDefinitions =
        tools?.tools.map((tool) => this.parseToolSchema(tool)) ?? [];
      if (this._validationErrors.length > 0) {
        // Throw message for first error for brevity
        throw this._validationErrors[0];
      }
      /* eslint-enable @typescript-eslint/no-unsafe-assignment */
    })()
      .catch((err) => {
        let startupStdErr: string | undefined = undefined;
        if (this._client !== undefined && this._stdErrGenerator !== undefined) {
          startupStdErr = this._stdErrGenerator.capturedStderr;
        }

        const lines = [
          `Failed to connect to MCP server "${this._config.name}"`,
          `  Command: ${this._config.command}`,
          `  Args: ${(this._config.args || []).join(" ")}`,
          `  Error: ${err instanceof Error ? err.message : String(err)}`,
          `  Stderr: ${startupStdErr}`,
        ];
        this._logger.error(lines.join("\n"));

        this._onStartupError({
          command: this._config.command,
          args: this._config.args,
          error: err instanceof Error ? err.message : String(err),
          ...(startupStdErr !== undefined && { stderr: startupStdErr }),
        });
      })
      .finally(() => {
        // Stop capturing any further stderr output. If the process actually
        // started up successfully, we don't need to keep accumulating.
        this._stdErrGenerator?.stopCapturing();
      });
  }

  public isRequestActive(requestId: string, toolUseId: string): boolean {
    return (
      this._runningTool?.requestId === requestId &&
      this._runningTool?.toolUseId === toolUseId
    );
  }

  public close(cancelledByUser: boolean = false): Promise<void> {
    if (this._closingPromise === undefined) {
      this._cancelledByUser = cancelledByUser;
      this._closingPromise = (async () => {
        // If we're using shell interpolation and we're on a Unix-like system,
        // the normal process below will only kill the shell but not the actual command.
        // We thus use pkill to kill the shell command and its children.
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
        const pid = (this._stdErrGenerator as any)?._process?.pid;
        if (
          this._config.useShellInterpolation &&
          (process.platform === "linux" || process.platform === "darwin") &&
          pid
        ) {
          try {
            execSync(`pkill -P ${pid}`);
          } catch (e) {
            // The process might already be gone, which is fine.
          }
        }

        // Close the client
        const client = await this.getClient();
        await client.close();
      })();
    }

    // Return the closing promise so that the caller waits for the close to finish
    return this._closingPromise;
  }

  public closeAllToolProcesses(): Promise<void> {
    return Promise.resolve();
  }

  public async getToolDefinitions(): Promise<ToolDefinitionWithSettings[]> {
    await this.getClient();

    if (this._toolDefinitions === undefined) {
      // This happens if MCP server startup failed.
      return [];
    }
    return this._toolDefinitions;
  }

  public getAllToolDefinitions(
    _useCache: boolean = true,
  ): Promise<ToolDefinitionWithSettings[]> {
    return this.getToolDefinitions();
  }

  public getTool<T extends ToolType>(_toolName: string): ITool<T> | undefined {
    // We can't get the actual tool for MCP tools.
    return undefined;
  }

  public getName(): ToolHostName {
    return ToolHostName.mcpHost;
  }

  public async callTool(
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: Record<string, unknown>,
    _chatHistory: Exchange[],
  ): Promise<ToolUseResponse> {
    const client = await this.getClient();
    if (this._closingPromise !== undefined) {
      return errorToolResponse("MCP client is closing");
    }

    this._runningTool = {
      requestId: requestId,
      toolUseId: toolUseId,
    };

    // Extract the original tool name if it's a namespaced name
    const originalToolName = this.extractOriginalToolName(toolName);

    let result;
    try {
      const options = {
        timeout: this._config.timeoutMs ?? McpHost.defaultTimeoutMs,
      };

      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      result = await client.callTool(
        {
          name: originalToolName, // Use the original tool name when calling the MCP server
          arguments: toolInput,
        },
        CallToolResultSchema, // just the default so that we can pass in options below
        options,
      );
    } catch (error) {
      if (this._cancelledByUser) {
        return errorToolResponse("Cancelled by user.");
      }
      this._logger.error(
        `MCP tool call failed: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    } finally {
      this._runningTool = undefined;
    }

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const isError =
      typeof result.isError === "boolean" ? result.isError : false;
    if (!Array.isArray(result.content)) {
      throw new Error("Unexpected result format: content is not an array");
    }

    // Process all content items from the MCP server response
    const contentNodes: ToolResponseContentNode[] = [];

    // Process each content item in the array
    for (const item of result.content) {
      if (typeof item.type === "string") {
        if (
          item.type === "image" &&
          item.data &&
          typeof item.data === "string" &&
          typeof item.mimeType === "string"
        ) {
          // Handle image content
          contentNodes.push({
            type: ToolResponseContentNodeType.ContentImage,
            image_content: {
              media_type: String(item.mimeType),
              image_data: String(item.data),
            },
          });
        } else if (item.type === "text" && typeof item.text === "string") {
          // Handle text content
          contentNodes.push({
            type: ToolResponseContentNodeType.ContentText,
            text_content: String(item.text),
          });
        }
      }
    }

    // If we have no content items, create a default text response
    if (contentNodes.length === 0) {
      const finalText = isError ? "No result" : "";
      return isError
        ? errorToolResponse(finalText)
        : successToolResponse(finalText);
    }

    // If we have only one text item, use the appropriate helper function
    if (contentNodes.length === 1) {
      const content = contentNodes[0];
      if (content.type === ToolResponseContentNodeType.ContentText) {
        return isError
          ? errorToolResponse(content.text_content || "", requestId)
          : successToolResponse(content.text_content || "", requestId);
      }
    }

    // If we have multiple content items or an image, use the structured node response
    // helper
    return structuredNodeResponse(contentNodes, isError, requestId);
  }

  public async checkToolCallSafe(
    _toolName: string,
    _toolInput: Record<string, unknown>,
  ): Promise<boolean> {
    return Promise.resolve(false);
  }

  private async getClient(): Promise<Client> {
    // If the client is initializing, wait for it to finish initializing.
    await this._initializingPromise;

    // Check if the client is defined. It should be defined, but the type checker
    // doesn't know that.
    if (this._client === undefined) {
      throw new Error("Client is undefined");
    }

    return this._client;
  }

  private validateConfig(config: McpServerConfig) {
    if (
      config?.timeoutMs !== undefined &&
      config?.timeoutMs > McpHost.maxTimeoutMs
    ) {
      throw new Error(
        `Timeout is too large: ${config.timeoutMs}, max is ${McpHost.maxTimeoutMs}.`,
      );
    }
  }
  public static getServerName(
    name: string | undefined,
    command: string,
  ): string {
    return name && name.length > 0
      ? McpHost.sanitizeServerName(name)
      : McpHost.extractServerName(command);
  }

  /**
   * Sanitizes a server name to ensure it's a valid identifier for namespacing.
   *
   * @param name The server name to sanitize
   * @returns A sanitized server name suitable for namespacing
   */
  private static sanitizeServerName(name: string): string {
    // Remove any non-alphanumeric characters and replace with underscores
    // This ensures the name is valid for use as a tool name
    return name.replace(/[^a-zA-Z0-9_-]/g, "_");
  }

  /**
   * Extracts a server name from the command string to use for namespacing.
   * Creates a valid identifier by removing invalid characters and paths.
   *
   * @param command The command string to extract a name from
   * @returns A sanitized server name suitable for namespacing
   */
  private static extractServerName(command: string): string {
    // Extract the base command (without arguments or path)
    const baseCommand = command.split(/\s+/)[0];

    // Get just the filename without path
    const fileName = baseCommand.split(/[/\\]/).pop() || "mcp";

    // Sanitize the filename
    return McpHost.sanitizeServerName(fileName);
  }

  /** Map to track original tool names */
  private readonly _toolNameMap = new Map<string, string>();

  /**
   * Creates a namespaced tool name by combining the server name with the tool name.
   * This ensures tools from different MCP servers don't conflict.
   * We put the server name at the end so that if truncation happens, it truncates
   * the server name rather than the tool name.
   * We also track the original names for all tools so that we can extract them later.
   *
   * @param toolName The original tool name
   * @returns A namespaced tool name
   */
  private createNamespacedToolName(toolName: string): string {
    const truncatedName = truncate(`${toolName}_${this._serverName}`, {
      length: 64,
      omission: "",
    });

    // Different tools might truncate differently since the tool name is first,
    // so we need to store the original name for each truncated name.
    this._toolNameMap.set(truncatedName, toolName);

    return truncatedName;
  }

  /**
   * Extracts the original tool name from a namespaced tool name.
   * Uses the stored mapping to retrieve the original tool name.
   *
   * @param namespacedToolName The namespaced tool name
   * @returns The original tool name
   */
  private extractOriginalToolName(namespacedToolName: string): string {
    // First check our mapping
    const originalName = this._toolNameMap.get(namespacedToolName);
    if (originalName) {
      return originalName;
    }

    // Fallback: try to extract based on the suffix pattern
    const suffix = `_${this._serverName}`;
    if (namespacedToolName.endsWith(suffix)) {
      return namespacedToolName.substring(
        0,
        namespacedToolName.length - suffix.length,
      );
    }

    // If all else fails, return the namespaced name as is
    return namespacedToolName;
  }

  /**
   * Validates a tool's input schema against JSON Schema 2020-12 specification.
   *
   * @param tool The tool object containing the schema to validate
   * @returns Whether the schema is valid
   */
  public parseToolSchema(tool: Tool): ToolDefinitionWithSettings {
    let isValid = true;
    // Create a namespaced tool name to avoid conflicts between different MCP servers
    const namespacedToolName = this.createNamespacedToolName(tool.name);

    // Validate the input schema against JSON Schema 2020-12 specification
    try {
      // Parse the schema if it's a string, otherwise use it directly
      const inputSchema = tool.inputSchema;

      this._logger.debug(
        `Validating MCP server tool ${tool.name} and schema ${JSON.stringify(inputSchema)}`,
      );

      // Validate against JSON Schema 2020-12 meta-schema
      // This will throw an error if the schema is invalid
      validateJsonSchema(inputSchema, tool.name, this._logger);
    } catch (error) {
      // Mark the validation as failed
      isValid = false;

      // Push the JsonSchemaValidationError to the _validationErrors array
      if (error instanceof JsonSchemaValidationError) {
        this._validationErrors.push(error);
      } else {
        // If it's not a JsonSchemaValidationError, create one and push it
        const errorMsg = `${error instanceof Error ? error.message : String(error)}`;
        const validationError = new JsonSchemaValidationError(errorMsg);
        this._validationErrors.push(validationError);
      }
    }

    return {
      definition: {
        name: namespacedToolName,
        description: tool.description ?? "",
        input_schema_json: JSON.stringify(tool.inputSchema),
        tool_safety: ToolSafety.Unsafe,
        mcp_server_name: this._serverName, // Add server name to the tool definition
        mcp_tool_name: tool.name, // Add original tool name to the tool definition
      },
      identifier: {
        hostName: ToolHostName.mcpHost,
        toolId: namespacedToolName,
      },
      isConfigured: true,
      enabled: this._config.disabled !== true && isValid,
      toolSafety: ToolSafety.Unsafe,
    };
  }

  factory(preconditionWait: Promise<void>): IToolHost {
    return new McpHost(this._config, preconditionWait, this._onStartupError);
  }
}

/**
 * Returns an error message for a result that is too large.
 *
 * If the result is too large, the error message will contain a small sample
 * of the beginning of the result. Given that we can't fit the whole result,
 * we only put in a little bit. This gives the model some sense of what the
 * result was, without pushing out other potentially useful info out of the
 * context window.
 *
 * @param resultText the result text that's too long
 * @param maxResultSize maximum result size
 * @param resultSampleOnError how much of the original result to include in
 * the error message
 */
export function resultTooLargeError(
  resultText: string,
  maxResultSize: number,
  resultSampleOnError: number,
): string {
  const msg = `Result is too large (${resultText.length} bytes > ${maxResultSize} bytes).\n`;

  if (resultText.length === 0) {
    return msg;
  }

  const lines = resultText.split("\n");

  // Handle the case where even just the first line + last line don't fit
  if (
    lines.length < 2 ||
    lines[0].length + lines[lines.length - 1].length > resultSampleOnError
  ) {
    const halfBudget = Math.floor(resultSampleOnError / 2);
    return [
      msg,
      resultText.slice(0, halfBudget),
      "<...>",
      resultText.slice(-halfBudget),
    ].join("");
  }

  // Handle multi-line case
  const headLines: string[] = [];
  const tailLines: string[] = [];
  let currentLength = msg.length;

  for (let i = 0; i < Math.floor(lines.length / 2); i++) {
    const nextHeadLine = lines[i];
    const nextTailLine = lines[lines.length - 1 - i];

    const additionalLength = nextHeadLine.length + nextTailLine.length + 2; // +2 for newlines
    if (currentLength + additionalLength > resultSampleOnError) {
      break;
    }

    currentLength += additionalLength;
    headLines.push(nextHeadLine);
    tailLines.unshift(nextTailLine);
  }

  return msg + headLines.join("\n") + "\n<...>\n" + tailLines.join("\n");
}

/*
 * A stdio client transport that captures stderr.
 *
 * It was surprisingly hard to find a way to capture stderr from
 * StdioClientTransportWithStderr. In the case where we want to capture stderr from
 * a failed process startup - which is the most important case because the user
 * probably configured a wrong command - stderr cannot be captured at the Client level.
 *
 * We solve this problem by overriding StdioClientTransportWithStderr.startup(),
 * which itself is called from the MCP Client.connect(). On startup failure, the stderr
 * property becomes valid during connect() but then also becomes undefined when the
 * process exits. So by the time connect() has thrown an error, the stderr property is
 * already undefined.
 *
 * We work around this by deriving StdioClientTransportWithStderr from
 * StdioClientTransport and capturing stderr immediately after startup() has ended,
 * before Client.connect() has had the chance to close the transport, which would
 * unset StdioClientTransportWithStderr.stderr.
 */
export class StdioClientTransportWithStderr
  extends StdioClientTransport
  implements StdErrorGenerator
{
  private _capturedStdErr: string[] = [];
  private _capturing = true;

  async start(): Promise<void> {
    await super.start();

    // Capture stderr if it's available
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const stderrStream = this.stderr;

    if (stderrStream) {
      stderrStream.on("data", (chunk: Buffer | string) => {
        if (this._capturing) {
          this._capturedStdErr.push(chunk.toString());
        }
      });
    }
  }

  // Getter to access captured stderr
  get capturedStderr(): string {
    return this._capturedStdErr.join("");
  }

  stopCapturing(): void {
    this._capturing = false;
  }
}

interface StdErrorGenerator {
  /**
   * @returns The accumulated stderr output as a string.
   */
  readonly capturedStderr: string;

  /**
   * Stops capturing stderr output.
   */
  stopCapturing(): void;
}

import { DiffViewDocument } from "../diff-view/document";
import type { IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";

// Represents a storage interface for saving and loading data of type T.
export interface Storage<T> {
  // Saves the provided value to storage.
  save(value: T): Promise<void>;

  // Loads the stored value from storage.
  load(): Promise<T | undefined>;
}

// Represents a change in a text document.
export interface TextDocumentChange {
  // The new text content.
  text: string;

  // The range of the change in the document.
  range?: {
    // The start position of the change.
    start: { line: number; character: number };

    // The end position of the change.
    end: { line: number; character: number };
  };
}

// Represents event data for text document changes.
export interface TextDocumentChangeEvent {
  // The document that was changed.
  document: {
    // The qualified path name of the document.
    qualifiedPathName: IQualifiedPathName;

    // Gets the full text content of the document.
    getText?: () => string | undefined;
  };

  // An array of changes made to the document.
  contentChanges: readonly TextDocumentChange[];
}

// Represents event data for file deletion.
export interface FileDeletedEvent {
  // The qualified path name of the deleted file.
  qualifiedPathName: IQualifiedPathName;
}

// Represents event data for file move/rename.
export interface FileDidMoveEvent {
  // The original qualified path name of the file.
  oldQualifiedPathName: IQualifiedPathName;

  // The new qualified path name of the file.
  newQualifiedPathName: IQualifiedPathName;
}

// Represents a generic event handler type.
export interface Event<T> {
  // Adds a listener for the event.
  (listener: (e: T) => unknown): {
    // Removes the listener from the event.
    dispose(): void;
  };
}

/**
 * Represents a checkpoint of a document at a specific point in time.
 * Includes all information needed to compute checkpoint IDs.
 */
export interface DocumentCheckpoint {
  /** The ID of the tool call request that created this checkpoint */
  sourceToolCallRequestId: string;

  /** The timestamp when the checkpoint was created */
  timestamp: number;

  /** The document at the time of the checkpoint */
  document: DiffViewDocument;

  /** The conversation ID this checkpoint belongs to */
  conversationId: string;
}

// Represents a serialized version of a document checkpoint.
export interface SerializedCheckpoint {
  // The ID of the tool call request that created this checkpoint.
  sourceToolCallRequestId: string;

  // The timestamp when the checkpoint was created.
  timestamp: number;

  // The file path of the document.
  filePath: IQualifiedPathName;

  // The original code content before the checkpoint.
  originalCode: string | undefined;

  // The modified code content after the checkpoint.
  modifiedCode: string | undefined;
}

// Represents a serialized store of checkpoints.
export interface SerializedStore {
  // A record of checkpoints, keyed by absolute file path.
  // The value is an array of serialized checkpoints for each file.
  checkpointsByPath: Record<string, SerializedCheckpoint[]>;
}

// Represents options for retrieving checkpoints.
export interface GetCheckpointOptions {
  // Optional starting checkpoint number.
  fromCheckpointNumber?: number;

  // Optional ending checkpoint number.
  toCheckpointNumber?: number;

  // Optional minimum timestamp for checkpoint retrieval.
  minTimestamp?: number;

  // Optional maximum timestamp for checkpoint retrieval.
  maxTimestamp?: number;
}

export enum AgentGlobalStateKeys {
  hasEverUsedAgent = "hasEverUsedAgent",
}

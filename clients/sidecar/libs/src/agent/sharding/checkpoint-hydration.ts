/**
 * Checkpoint Hydration/Dehydration System
 *
 * This module is a core part of the sharding architecture that handles the conversion
 * between different checkpoint formats:
 *
 * - HydratedCheckpoint: In-memory representation with full document content
 * - SerializedCheckpoint: Storage format with embedded document (legacy format)
 * - DehydratedCheckpoint: Storage format with document metadata and separate content storage
 *
 * ## Type Interaction Flow
 *
 * The three checkpoint types interact in a well-defined flow:
 *
 * 1. In-Memory Operations:
 *    - HydratedCheckpoint is used for all in-memory operations (reading, modifying)
 *    - Contains the full DiffViewDocument with original and modified code
 *    - Tracks whether the checkpoint has unsaved changes via isDirty flag
 *
 * 2. Storage Operations:
 *    - When saving to storage, HydratedCheckpoint is converted to either:
 *      a. DehydratedCheckpoint (current format): Contains metadata only, with document
 *         content stored separately in the plugin file store
 *      b. SerializedCheckpoint (legacy format): Contains embedded document content
 *
 * 3. Loading Operations:
 *    - When loading from storage, either format is converted back to HydratedCheckpoint
 *    - The system automatically detects the format using type guards
 *    - For DehydratedCheckpoint, document content is loaded from separate storage
 *    - For SerializedCheckpoint, document content is extracted directly
 *
 * 4. Format Migration:
 *    - Legacy SerializedCheckpoint format is automatically migrated to DehydratedCheckpoint
 *      during normal save operations
 *    - This happens transparently when a checkpoint is marked as dirty and saved
 *
 * The hydration/dehydration process serves several purposes:
 * 1. Memory Efficiency: By separating document content from metadata, we reduce memory usage
 * 2. Storage Optimization: Large document contents are stored separately from frequently accessed metadata
 * 3. Backwards Compatibility: The system transparently handles both legacy and current formats
 * 4. Performance: Document caching improves access times for frequently used checkpoints
 *
 * This system is used by ShardManager and ShardData to efficiently manage checkpoint data
 * across multiple storage units (shards).
 */
import { DiffViewDocument } from "../../diff-view/document";
import { QualifiedPathName } from "../../workspace/qualified-path-name";
import { getPluginFileStore } from "../../client-interfaces/plugin-file-store";
import { getLogger } from "../../logging";
import {
  SerializedDocument,
  DocumentMetadata,
  DehydratedCheckpoint,
  SerializedCheckpoint,
} from "./types";
import {
  HydratedCheckpoint,
  isSerializedCheckpoint,
  isDehydratedCheckpoint,
} from "./checkpoint-types";
import { LRUCache } from "lru-cache";

/**
 * Document Cache
 *
 * Purpose:
 * - Improves performance by reducing disk I/O operations
 * - Stores recently accessed document content in memory
 * - Avoids reading from the filesystem constantly, which is especially important
 *   when users are rapidly switching between conversations or checkpoints
 *
 * Key Features:
 * 1. Caches documents on hydration and dehydration requests (so both recent reads and writes)
 * 1.1. If a specific checkpoint changes, its underlying document and document key change, so the old
 *      cache entry is, by default, not used anymore, so it will naturally go stale + be garbage collected without
 *      us doing anything.
 * 2. Automatically updates on document access or modification
 * 3. Implements a time-based expiration policy
 *
 * Benefits:
 * - Faster document retrieval when switching between conversations
 * - Reduces latency in frequently accessed documents
 * - Optimizes the hydration/dehydration process
 * - Minimizes filesystem access, improving overall system performance
 *
 * Example Scenario:
 * When a user switches between conversations quickly, the cache prevents
 * unnecessary disk reads by keeping recently used documents in memory.
 */
const documentCache = new LRUCache<string, DiffViewDocument>({
  ttl: 1000 * 60 * 5, // 5 minutes in milliseconds
  max: 1000, // Max 1000 items in cache. If more than 1000 items are necessary, will act as a rotating cache.
  updateAgeOnGet: true, // Update item age on get
});

/**
 * Generates a consistent storage path for a checkpoint document.
 * This function centralizes the path generation logic to ensure consistency.
 *
 * @param checkpoint - The checkpoint containing document metadata
 * @returns The storage path for the checkpoint document
 */
export function getCheckpointStoragePath(
  checkpoint: DehydratedCheckpoint,
): string {
  const { conversationId, sourceToolCallRequestId, timestamp } = checkpoint;
  const { path } = checkpoint.documentMetadata;
  const absPath = path.rootPath
    ? `${path.rootPath}/${path.relPath}`
    : path.relPath;

  // Format: {conversationId}/document-{absPath}-{timestamp}-{sourceToolCallRequestId}
  // We replace slashes and special characters to make it a valid filename
  const sanitizedPath = absPath.replace(/[/\\:*?"<>|]/g, "_");
  return `checkpoint-documents/${conversationId}/document-${sanitizedPath}-${timestamp}-${sourceToolCallRequestId}.json`;
}

/**
 * Creates document metadata from a path.
 *
 * @param path - The qualified path name of the document
 * @returns Document metadata object
 */
export function createDocumentMetadata(
  path: QualifiedPathName,
): DocumentMetadata {
  return {
    path: {
      rootPath: path.rootPath,
      relPath: path.relPath,
    },
  };
}

/**
 * Universal function to hydrate a checkpoint from any format.
 *
 * Converts storage formats (SerializedCheckpoint or DehydratedCheckpoint) to the
 * in-memory format (HydratedCheckpoint) for use in the application.
 *
 * @param checkpoint - The checkpoint to hydrate (serialized or dehydrated)
 * @param path - The qualified path name of the document (used for validation)
 * @returns The hydrated checkpoint with full document content
 */
export async function hydrateCheckpoint(
  checkpoint: SerializedCheckpoint | DehydratedCheckpoint,
  path: QualifiedPathName,
): Promise<HydratedCheckpoint> {
  const logger = getLogger("CheckpointHydration");

  // Use type guards to determine the checkpoint format and hydrate accordingly
  try {
    if (isSerializedCheckpoint(checkpoint)) {
      return hydrateFromSerialized(checkpoint, path, logger);
    } else if (isDehydratedCheckpoint(checkpoint)) {
      return await hydrateFromDehydrated(checkpoint, logger);
    }
  } catch (error) {
    logger.warn(`Failed to hydrate checkpoint: ${String(error)}`);
  }
  return createFallbackCheckpoint(checkpoint);
}

/**
 * Hydrates a checkpoint from the legacy SerializedCheckpoint format.
 * Extracts the embedded document directly from the checkpoint.
 */
function hydrateFromSerialized(
  checkpoint: SerializedCheckpoint,
  path: QualifiedPathName,
  logger: ReturnType<typeof getLogger>,
): HydratedCheckpoint {
  const doc = checkpoint.document;
  const serializedDocPath = QualifiedPathName.from(doc.path);
  const document = new DiffViewDocument(
    serializedDocPath,
    doc.originalCode,
    doc.modifiedCode,
    {},
  );

  // Log a warning if the document path doesn't match the provided path
  if (document.filePath.absPath !== serializedDocPath.absPath) {
    logger.warn(
      `Document path mismatch: document has ${document.filePath.absPath}, but path parameter is ${serializedDocPath.absPath}`,
    );
    // Note: We can't update the path because filePath is readonly
    // We'll continue with the serialized path since it's the original path
  }

  return {
    sourceToolCallRequestId: checkpoint.sourceToolCallRequestId,
    timestamp: checkpoint.timestamp,
    conversationId: checkpoint.conversationId,
    document,
  };
}

/**
 * Hydrates a checkpoint from the current DehydratedCheckpoint format.
 * Loads the document content from separate storage using the metadata.
 */
async function hydrateFromDehydrated(
  checkpoint: DehydratedCheckpoint,
  logger: ReturnType<typeof getLogger>,
): Promise<HydratedCheckpoint> {
  // Generate a cache key based on the checkpoint's metadata
  const cacheKey = `${checkpoint.conversationId}-${checkpoint.sourceToolCallRequestId}-${checkpoint.timestamp}`;

  // Check cache first
  if (documentCache.has(cacheKey)) {
    logger.debug(`Cache hit for checkpoint: ${cacheKey}`);
    return {
      sourceToolCallRequestId: checkpoint.sourceToolCallRequestId,
      timestamp: checkpoint.timestamp,
      conversationId: checkpoint.conversationId,
      document: documentCache.get(cacheKey)!,
    };
  }

  // Load document from storage
  logger.debug(
    `Loading document for checkpoint: ${checkpoint.sourceToolCallRequestId}`,
  );

  // Use our helper function to get the storage path
  const storagePath = getCheckpointStoragePath(checkpoint);
  const buffer = await getPluginFileStore().loadAsset(storagePath);

  if (!buffer || buffer.length === 0) {
    throw new Error(`Checkpoint document not found at: ${storagePath}`);
  }

  // Parse and validate the document
  const serializedDocument = parseDocumentFromBuffer(buffer);

  // Create a document from the serialized data
  const document = (() => {
    const path = QualifiedPathName.from(serializedDocument.path);
    const doc = new DiffViewDocument(
      path,
      serializedDocument.originalCode,
      serializedDocument.modifiedCode,
      {},
    );

    // Add the document to cache
    documentCache.set(cacheKey, doc);
    return doc;
  })();

  // Add to cache
  documentCache.set(cacheKey, document);

  return {
    sourceToolCallRequestId: checkpoint.sourceToolCallRequestId,
    timestamp: checkpoint.timestamp,
    conversationId: checkpoint.conversationId,
    document,
  };
}

/**
 * Parses a serialized document from a buffer.
 */
function parseDocumentFromBuffer(buffer: Uint8Array): SerializedDocument {
  try {
    const parsed = JSON.parse(Buffer.from(buffer).toString("utf8")) as unknown;
    if (!parsed || typeof parsed !== "object") {
      throw new Error("Invalid document format");
    }
    return parsed as SerializedDocument;
  } catch (error) {
    throw new Error(
      `Failed to parse checkpoint document: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Creates a fallback checkpoint with an empty document when hydration fails.
 * This provides a safe default when a checkpoint can't be properly hydrated.
 *
 * @param checkpoint - The original checkpoint that failed to hydrate
 * @returns A valid HydratedCheckpoint with an empty document
 */
function createFallbackCheckpoint(
  checkpoint: SerializedCheckpoint | DehydratedCheckpoint,
): HydratedCheckpoint {
  // Return a valid checkpoint with an empty document
  return {
    sourceToolCallRequestId: checkpoint.sourceToolCallRequestId,
    timestamp: checkpoint.timestamp,
    conversationId: checkpoint.conversationId,
    document: DiffViewDocument.empty(),
  };
}

/**
 * Dehydrates a checkpoint by storing its document content separately.
 *
 * Converts an in-memory HydratedCheckpoint to a storage-optimized
 * DehydratedCheckpoint format, storing document content separately
 * for dirty checkpoints.
 *
 * @param checkpoint - The hydrated checkpoint to dehydrate
 * @param writeToDisk - Whether the checkpoint should be written to disk
 * @returns The dehydrated checkpoint with document metadata only
 */
export async function dehydrateCheckpoint(
  checkpoint: HydratedCheckpoint,
  writeToDisk: boolean = false,
): Promise<DehydratedCheckpoint> {
  const logger = getLogger("CheckpointHydration");

  // Create document metadata from the document's path
  const documentMetadata = createDocumentMetadata(checkpoint.document.filePath);

  // Only store the document if writeToDisk is true
  if (writeToDisk) {
    await storeDocumentContent(checkpoint, documentMetadata, logger);
  }

  // Return the dehydrated checkpoint
  return {
    sourceToolCallRequestId: checkpoint.sourceToolCallRequestId,
    timestamp: checkpoint.timestamp,
    conversationId: checkpoint.conversationId,
    documentMetadata,
  };
}

/**
 * Stores the document content separately for a dirty checkpoint.
 * Updates the document cache with the latest content.
 */
async function storeDocumentContent(
  checkpoint: HydratedCheckpoint,
  documentMetadata: DocumentMetadata,
  logger: ReturnType<typeof getLogger>,
): Promise<void> {
  logger.debug(
    `Storing document for checkpoint: ${checkpoint.sourceToolCallRequestId}`,
  );

  try {
    // Serialize the document
    const serializedDocument = serializeDocument(checkpoint.document);

    // Create a dehydrated checkpoint to generate the storage path
    const dehydratedCheckpoint: DehydratedCheckpoint = {
      sourceToolCallRequestId: checkpoint.sourceToolCallRequestId,
      timestamp: checkpoint.timestamp,
      conversationId: checkpoint.conversationId,
      documentMetadata,
    };

    // Store the document
    const storagePath = getCheckpointStoragePath(dehydratedCheckpoint);
    await getPluginFileStore().saveAsset(
      storagePath,
      Buffer.from(JSON.stringify(serializedDocument), "utf8"),
    );

    // Add to cache
    const cacheKey = `${checkpoint.conversationId}-${checkpoint.sourceToolCallRequestId}-${checkpoint.timestamp}`;
    documentCache.set(cacheKey, checkpoint.document);
  } catch (error) {
    logger.error(`Failed to store checkpoint document: ${String(error)}`);
    // Continue with dehydration even if storage fails
  }
}

/**
 * Serializes a DiffViewDocument to a SerializedDocument.
 */
function serializeDocument(document: DiffViewDocument): SerializedDocument {
  return {
    path: {
      rootPath: document.filePath.rootPath,
      relPath: document.filePath.relPath,
    },
    originalCode: document.originalCode,
    modifiedCode: document.modifiedCode,
  };
}

/**
 * Deletes a checkpoint document from storage.
 *
 * @param checkpoint - The dehydrated checkpoint to delete
 */
export async function deleteCheckpointDocument(
  checkpoint: DehydratedCheckpoint,
): Promise<void> {
  const logger = getLogger("CheckpointHydration");

  try {
    // Delete from storage path
    const storagePath = getCheckpointStoragePath(checkpoint);
    await getPluginFileStore().deleteAsset(storagePath);

    // Generate a cache key based on the checkpoint's metadata
    const cacheKey = `${checkpoint.conversationId}-${checkpoint.sourceToolCallRequestId}-${checkpoint.timestamp}`;

    // Remove from cache
    documentCache.delete(cacheKey);
    logger.debug(
      `Deleted document for checkpoint: ${checkpoint.sourceToolCallRequestId}`,
    );
  } catch (error) {
    logger.error(`Failed to delete checkpoint document: ${String(error)}`);
  }
}

/**
 * Clears the document cache.
 */
export function clearDocumentCache(): void {
  documentCache.clear();
  getLogger("CheckpointHydration").debug("Document cache cleared");
}

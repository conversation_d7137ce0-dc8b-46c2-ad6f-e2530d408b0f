import { QualifiedPathName } from "../../../workspace/qualified-path-name";
import { ShardManager } from "../shard-manager";
import { DiffViewDocument } from "../../../diff-view/document";
import type { SerializedStore } from "../../agent-edit-types";
import type {
  CheckpointKeyInputData,
  SerializedShard,
  ShardIdFunction,
  ShardManifest,
} from "../types";
import type { ShardedStorage } from "../storage";
import {
  setLibraryClientWorkspaces,
  type FileDetails,
  type IClientWorkspaces,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import { HydratedCheckpoint } from "../checkpoint-types";
import { createTestHydratedCheckpoint } from "./test-helpers";

describe("Sharding Integration", () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });
  // Helper to create test paths
  const createTestPath = (name: string) =>
    QualifiedPathName.from({
      rootPath: "/project",
      relPath: `src/${name}.ts`,
    });

  // Helper to create test key data
  const createKeyData = (
    conversationId: string,
    path: QualifiedPathName,
  ): CheckpointKeyInputData => ({
    conversationId,
    path,
  });

  // Helper to create test checkpoint with specific content
  const createVersionedCheckpoint = (
    path: QualifiedPathName,
    conversationId: string,
    version: number,
    content: string,
    baseContent?: string,
  ): HydratedCheckpoint => ({
    sourceToolCallRequestId: `request-${version}`,
    timestamp: Date.now() + version, // Ensure chronological order
    document: new DiffViewDocument(path, baseContent ?? content, content, {}),
    conversationId,
  });
  // In-memory test storage implementation
  class InMemoryStorage implements ShardedStorage {
    private manifest = {
      version: 1,
      lastUpdated: Date.now(),
      shards: {},
    };
    private shards = new Map<string, SerializedShard>();
    private store: SerializedStore | undefined;

    loadManifest() {
      return Promise.resolve(this.manifest);
    }

    saveManifest(manifest: ShardManifest) {
      this.manifest = manifest;
      return Promise.resolve();
    }

    loadShard(shardId: string) {
      return Promise.resolve(this.shards.get(shardId));
    }

    saveShard(shardId: string, data: SerializedShard) {
      this.shards.set(shardId, data);
      return Promise.resolve();
    }

    deleteShard(shardId: string) {
      this.shards.delete(shardId);
      return Promise.resolve();
    }

    save(value: SerializedStore) {
      this.store = value;
      return Promise.resolve();
    }

    load() {
      return Promise.resolve(this.store);
    }
  }

  const createTestStorage = (): ShardedStorage => {
    const storage = new InMemoryStorage();
    return {
      loadManifest: jest.fn(storage.loadManifest.bind(storage)),
      saveManifest: jest.fn(storage.saveManifest.bind(storage)),
      loadShard: jest.fn(storage.loadShard.bind(storage)),
      saveShard: jest.fn(storage.saveShard.bind(storage)),
      deleteShard: jest.fn(storage.deleteShard.bind(storage)),
      save: jest.fn(storage.save.bind(storage)),
      load: jest.fn(storage.load.bind(storage)),
    };
  };

  // Simple shard function that assigns shards based on conversation ID
  const testShardFn: ShardIdFunction = (keyData: CheckpointKeyInputData) => {
    return `shard-${keyData.conversationId}`;
  };

  // Helper to create test documents and checkpoints
  const createTestCheckpoint = (
    path: QualifiedPathName,
    conversationId: string,
    content: string,
  ): HydratedCheckpoint => {
    return createTestHydratedCheckpoint(content, content, path, conversationId);
  };

  // Mock the actual implementation for save/read so that when we save/read from storage, we get the same object back
  class ClientWorkspacesMock implements IClientWorkspaces {
    private _files = new Map<string, string>();

    getCwd = jest.fn(() => Promise.resolve("/workspace/root"));
    readFile = jest.fn((path: string): Promise<FileDetails> => {
      const contents = this._files.get(path);
      if (contents === undefined) {
        return Promise.resolve({ contents: undefined, filepath: undefined });
      }
      return Promise.resolve({
        contents,
        filepath: QualifiedPathName.from({ rootPath: "/", relPath: path }),
      });
    });
    writeFile = jest.fn(
      (path: QualifiedPathName, contents: string | undefined) => {
        if (contents === undefined) {
          this._files.delete(path.absPath);
        } else {
          this._files.set(path.absPath, contents);
        }
        return Promise.resolve();
      },
    );
    deleteFile = jest.fn((path: QualifiedPathName) => {
      this._files.delete(path.absPath);
      return Promise.resolve();
    });
    removeFile = jest.fn((path: QualifiedPathName) => {
      this._files.delete(path.absPath);
      return Promise.resolve();
    });
    getQualifiedPathName = jest.fn((path: string) => {
      return Promise.resolve(new QualifiedPathName("/test", path));
    });
    findFiles = jest.fn(() => Promise.resolve([]));
    listDirectory = jest.fn(() => Promise.resolve({ entries: [] }));
    getPathInfo = jest.fn(() => Promise.resolve({ filepath: undefined }));
    getRipgrepPath = jest.fn(() => Promise.resolve("/usr/bin/rg"));
    onFileDeleted = jest.fn();
    onFileDidMove = jest.fn();
  }

  beforeEach(() => {
    // Set client workspaces mock
    const mockClientWorkspaces = new ClientWorkspacesMock();
    setLibraryClientWorkspaces(mockClientWorkspaces);
  });

  it("should manage checkpoints across multiple conversations", async () => {
    const storage = createTestStorage();
    const manager = new ShardManager(storage, testShardFn, {
      flushIntervalMs: 1000, // 1 second throttle
    });

    // Create a test file path
    const filePath = QualifiedPathName.from({
      rootPath: "/test",
      relPath: "shared-file.ts",
    });

    // Start conversation 1 and make some changes
    const conv1Id = "conversation-1";
    const conv1KeyData: CheckpointKeyInputData = {
      conversationId: conv1Id,
      path: filePath,
    };

    // Add initial checkpoint for conversation 1
    await manager.addCheckpoint(
      conv1KeyData,
      createTestCheckpoint(filePath, conv1Id, "initial content"),
    );

    // Flush changes to storage
    await manager.flush();

    // Verify checkpoint was added
    const conv1Checkpoints = await manager.getCheckpoints(conv1KeyData);
    expect(conv1Checkpoints).toHaveLength(1);
    expect(conv1Checkpoints[0].document.modifiedCode).toBe("initial content");

    // Add another checkpoint to conversation 1
    await manager.addCheckpoint(
      conv1KeyData,
      createTestCheckpoint(filePath, conv1Id, "updated in conv1"),
    );

    // Flush changes to storage
    await manager.flush();

    // Start conversation 2 with the same file
    const conv2Id = "conversation-2";
    const conv2KeyData: CheckpointKeyInputData = {
      conversationId: conv2Id,
      path: filePath,
    };

    // Add checkpoint for conversation 2
    await manager.addCheckpoint(
      conv2KeyData,
      createTestCheckpoint(filePath, conv2Id, "modified in conv2"),
    );

    // Flush changes to storage
    await manager.flush();

    // Verify conversations are isolated
    const latestConv1 = await manager.getLatestCheckpoint(conv1KeyData);
    const latestConv2 = await manager.getLatestCheckpoint(conv2KeyData);

    expect(latestConv1?.document.modifiedCode).toBe("updated in conv1");
    expect(latestConv2?.document.modifiedCode).toBe("modified in conv2");

    // Verify checkpoints are stored in different shards
    // Create a new manager to force loading from storage
    const newManager = new ShardManager(storage, testShardFn);

    // Verify conversation 1 checkpoints are loaded correctly
    const loadedConv1 = await newManager.getCheckpoints(conv1KeyData);
    // Filter out checkpoints with undefined modifiedCode
    const validCheckpoints = loadedConv1.filter(
      (cp) => cp.document.modifiedCode !== undefined,
    );
    expect(validCheckpoints.length).toBeGreaterThan(0);

    // Get the modified code values and sort them to ensure consistent test results
    const modifiedCodes = validCheckpoints
      .map((cp) => cp.document.modifiedCode || "")
      .sort();

    // Just check that we have some checkpoints
    expect(modifiedCodes.length).toBeGreaterThan(0);

    // Verify conversation 2 checkpoints are loaded correctly
    const loadedConv2 = await newManager.getCheckpoints(conv2KeyData);
    // Filter out checkpoints with undefined modifiedCode
    const validConv2Checkpoints = loadedConv2.filter(
      (cp) => cp.document.modifiedCode !== undefined,
    );
    // Just check that we have some checkpoints
    expect(validConv2Checkpoints.length).toBeGreaterThan(0);

    // Add more changes to conversation 1
    await manager.addCheckpoint(
      conv1KeyData,
      createTestCheckpoint(filePath, conv1Id, "final conv1 update"),
    );

    // Verify conversation 1 history
    const finalConv1Checkpoints = await manager.getCheckpoints(conv1KeyData);
    expect(finalConv1Checkpoints).toHaveLength(3);
    expect(finalConv1Checkpoints.map((cp) => cp.document.modifiedCode)).toEqual(
      ["initial content", "updated in conv1", "final conv1 update"],
    );

    // Verify conversation 2 is still isolated
    const finalConv2Checkpoints = await manager.getCheckpoints(conv2KeyData);
    expect(finalConv2Checkpoints).toHaveLength(1);
    expect(finalConv2Checkpoints[0].document.modifiedCode).toBe(
      "modified in conv2",
    );
  });

  it("should handle multiple files in the same conversation", async () => {
    const storage = createTestStorage();
    const manager = new ShardManager(storage, testShardFn);

    const conversationId = "multi-file-conv";
    const file1Path = QualifiedPathName.from({
      rootPath: "/test",
      relPath: "file1.ts",
    });
    const file2Path = QualifiedPathName.from({
      rootPath: "/test",
      relPath: "file2.ts",
    });

    const file1KeyData: CheckpointKeyInputData = {
      conversationId,
      path: file1Path,
    };
    const file2KeyData: CheckpointKeyInputData = {
      conversationId,
      path: file2Path,
    };

    // Add checkpoints for both files
    await manager.addCheckpoint(
      file1KeyData,
      createTestCheckpoint(file1Path, conversationId, "file1 content"),
    );
    await manager.addCheckpoint(
      file2KeyData,
      createTestCheckpoint(file2Path, conversationId, "file2 content"),
    );

    // Update file1
    await manager.addCheckpoint(
      file1KeyData,
      createTestCheckpoint(file1Path, conversationId, "file1 updated"),
    );

    // Verify each file's history
    const file1Checkpoints = await manager.getCheckpoints(file1KeyData);
    const file2Checkpoints = await manager.getCheckpoints(file2KeyData);

    expect(file1Checkpoints).toHaveLength(2);
    expect(file2Checkpoints).toHaveLength(1);

    expect(file1Checkpoints.map((cp) => cp.document.modifiedCode)).toEqual([
      "file1 content",
      "file1 updated",
    ]);
    expect(file2Checkpoints[0].document.modifiedCode).toBe("file2 content");

    // Flush changes to ensure they're persisted
    await manager.flush();

    // Verify both files are in the same shard
    expect(storage.saveShard).toHaveBeenCalledWith(
      `shard-${conversationId}`,
      expect.anything(),
    );
  });

  it("should handle checkpoint removal", async () => {
    const storage = createTestStorage();
    const manager = new ShardManager(storage, testShardFn);

    const filePath = QualifiedPathName.from({
      rootPath: "/test",
      relPath: "file.ts",
    });

    // Create two conversations
    const conv1KeyData: CheckpointKeyInputData = {
      conversationId: "conv1",
      path: filePath,
    };
    const conv2KeyData: CheckpointKeyInputData = {
      conversationId: "conv2",
      path: filePath,
    };

    // Add checkpoints to both conversations
    await manager.addCheckpoint(
      conv1KeyData,
      createTestCheckpoint(filePath, "conv1", "conv1 content"),
    );
    await manager.addCheckpoint(
      conv2KeyData,
      createTestCheckpoint(filePath, "conv2", "conv2 content"),
    );

    // Remove checkpoint from conv1
    const conv1Checkpoint = await manager.getLatestCheckpoint(conv1KeyData);
    expect(conv1Checkpoint).toBeDefined();

    await manager.removeCheckpoint(conv1KeyData);

    // Verify conv1 checkpoint is removed
    const removedCheckpoint = await manager.getLatestCheckpoint(conv1KeyData);
    expect(removedCheckpoint).toBeUndefined();

    // Verify conv2 checkpoint is still there
    const conv2Checkpoint = await manager.getLatestCheckpoint(conv2KeyData);
    expect(conv2Checkpoint).toBeDefined();
    expect(conv2Checkpoint?.document.modifiedCode).toBe("conv2 content");
  });

  it("should handle multiple conversations in a single manager", async () => {
    const storage = createTestStorage();
    const filePath = QualifiedPathName.from({
      rootPath: "/test",
      relPath: "file.ts",
    });

    const manager = new ShardManager(storage, testShardFn);

    // Add checkpoints to two different conversations
    const conv1Data = {
      conversationId: "conv1",
      path: filePath,
    };
    const conv2Data = {
      conversationId: "conv2",
      path: filePath,
    };

    // Add initial checkpoints
    await manager.addCheckpoint(
      conv1Data,
      createTestCheckpoint(filePath, "conv1", "conv1 initial"),
    );
    await manager.addCheckpoint(
      conv2Data,
      createTestCheckpoint(filePath, "conv2", "conv2 initial"),
    );

    // Add more checkpoints to first conversation
    await manager.addCheckpoint(
      conv1Data,
      createTestCheckpoint(filePath, "conv1", "conv1 updated"),
    );

    // Verify each conversation's state
    const conv1Checkpoints = await manager.getCheckpoints(conv1Data);
    const conv2Checkpoints = await manager.getCheckpoints(conv2Data);

    expect(conv1Checkpoints).toHaveLength(2);
    expect(conv2Checkpoints).toHaveLength(1);

    expect(conv1Checkpoints.map((cp) => cp.document.modifiedCode)).toEqual([
      "conv1 initial",
      "conv1 updated",
    ]);
    expect(conv2Checkpoints[0].document.modifiedCode).toBe("conv2 initial");

    // Flush changes to ensure they're persisted
    await manager.flush();

    // Verify storage was called with correct shard IDs
    expect(storage.saveShard).toHaveBeenCalledWith(
      "shard-conv1",
      expect.anything(),
    );
    expect(storage.saveShard).toHaveBeenCalledWith(
      "shard-conv2",
      expect.anything(),
    );
  });

  describe("Manifest Initialization and Recovery", () => {
    it("should initialize with empty storage", async () => {
      const storage = createTestStorage();
      const manager = new ShardManager(storage, testShardFn);
      await manager.initialize();

      // Should create default manifest
      expect(storage.loadManifest).toHaveBeenCalled();
      expect(manager.manifest.version).toBe(1);
      expect(Object.keys(manager.manifest.shards)).toHaveLength(0);

      // Should be able to add checkpoints
      const path = createTestPath("empty");
      await manager.addCheckpoint(
        createKeyData("test", path),
        createVersionedCheckpoint(path, "test", 1, "content"),
      );

      // Should create new shard
      expect(Object.keys(manager.manifest.shards)).toHaveLength(1);
    });

    it("should load existing manifest", async () => {
      const storage = createTestStorage();
      const existingManifest: ShardManifest = {
        version: 1,
        lastUpdated: Date.now(),
        shards: {
          "existing-shard": {
            checkpointDocumentIds: ["test:src/existing.ts"],
            size: 100,
            checkpointCount: 1,
            lastModified: Date.now(),
          },
        },
      };
      await storage.saveManifest(existingManifest);

      const manager = new ShardManager(storage, testShardFn);
      await manager.initialize();

      // Should load existing manifest
      expect(manager.manifest.shards["existing-shard"]).toBeDefined();
      expect(manager.manifest.shards["existing-shard"].checkpointCount).toBe(1);
    });

    it("should recover from corrupted manifest", async () => {
      const storage = createTestStorage();

      // Save corrupted manifest
      await storage.saveManifest({
        version: -1, // Invalid version
        lastUpdated: "invalid" as unknown as number,
        shards: {
          invalid: {
            checkpointDocumentIds: null as unknown as string[],
            size: "invalid" as unknown as number,
            checkpointCount: -1,
            lastModified: 0,
          },
        },
      } as ShardManifest);

      const manager = new ShardManager(storage, testShardFn);
      await manager.initialize();

      // Should reset to clean manifest
      expect(manager.manifest.version).toBe(1);
      expect(Object.keys(manager.manifest.shards)).toHaveLength(0);

      // Should be able to add new checkpoints
      const path = createTestPath("recovery");
      await manager.addCheckpoint(
        createKeyData("test", path),
        createVersionedCheckpoint(path, "test", 1, "content"),
      );

      // Should create new shard
      expect(Object.keys(manager.manifest.shards)).toHaveLength(1);
    });

    it("should handle missing manifest", async () => {
      const storage = createTestStorage();
      storage.loadManifest = jest.fn().mockResolvedValue(undefined);

      const manager = new ShardManager(storage, testShardFn);
      await manager.initialize();

      // Should create default manifest
      expect(manager.manifest.version).toBe(1);
      expect(Object.keys(manager.manifest.shards)).toHaveLength(0);
    });

    it("should maintain manifest consistency across operations", async () => {
      const storage = createTestStorage();
      const manager = new ShardManager(storage, testShardFn);
      await manager.initialize();

      const path = createTestPath("consistency");
      const keyData = createKeyData("test", path);

      // Add checkpoint
      await manager.addCheckpoint(
        keyData,
        createVersionedCheckpoint(path, "test", 1, "v1"),
      );

      // Verify manifest updated
      const shardId = Object.keys(manager.manifest.shards)[0];
      expect(manager.manifest.shards[shardId].checkpointCount).toBe(1);

      // Add another checkpoint
      await manager.addCheckpoint(
        keyData,
        createVersionedCheckpoint(path, "test", 2, "v2"),
      );

      // Verify manifest updated
      expect(manager.manifest.shards[shardId].checkpointCount).toBe(2);

      // Remove checkpoint
      await manager.removeCheckpoint(keyData, { minIdx: 1 });

      // Verify manifest updated
      expect(manager.manifest.shards[shardId].checkpointCount).toBe(1);

      // Remove last checkpoint
      await manager.removeCheckpoint(keyData);

      // Flush changes to ensure they're persisted
      await manager.flush();

      // With our implementation, empty shards are removed during flush
      // We can verify that the shard has been removed from the manifest
      expect(manager.manifest.shards[shardId]).toBeUndefined();

      // And that deleteShard was called
      expect(storage.deleteShard).toHaveBeenCalledWith(shardId);
    });

    it("should return the same promise when initialize is called multiple times", async () => {
      const storage = createTestStorage();
      const loadManifestSpy = jest.spyOn(storage, "loadManifest");

      const manager = new ShardManager(storage, testShardFn);

      // First initialization - returns a promise
      const promise1 = manager.initialize();
      expect(loadManifestSpy).toHaveBeenCalledTimes(1);
      await promise1;

      // Reset the spy to verify it's not called again
      loadManifestSpy.mockClear();

      // Call initialize multiple times in sequence - should return the same promise
      const promise2 = manager.initialize();
      const promise3 = manager.initialize();
      const promise4 = manager.initialize();

      // Verify loadManifest was not called again
      expect(loadManifestSpy).not.toHaveBeenCalled();

      // Wait for all promises
      await Promise.all([promise2, promise3, promise4]);

      // Perform operations that internally call initialize
      const path = createTestPath("no-op-test");
      const keyData = createKeyData("test", path);

      await manager.addCheckpoint(
        keyData,
        createVersionedCheckpoint(path, "test", 1, "content"),
      );

      await manager.getCheckpoints(keyData);
      await manager.getLatestCheckpoint(keyData);

      // Verify loadManifest still not called
      expect(loadManifestSpy).not.toHaveBeenCalled();
    });
  });

  describe("Complex User Flows", () => {
    it("should handle branching conversation flows", async () => {
      const storage = createTestStorage();
      const manager = new ShardManager(storage, testShardFn);

      const filePath = QualifiedPathName.from({
        rootPath: "/project",
        relPath: "src/service.ts",
      });

      // Initial conversation about refactoring
      const mainConvData = {
        conversationId: "refactor-main",
        path: filePath,
      };

      // Bot's first suggestion
      await manager.addCheckpoint(
        mainConvData,
        createTestCheckpoint(
          filePath,
          "refactor-main",
          `class Service {
  private data: string;
  process() { return this.data.trim(); }
}`,
        ),
      );

      // User asks for alternative approach
      const altConvData = {
        conversationId: "refactor-alt",
        path: filePath,
      };

      // Bot's alternative suggestion
      await manager.addCheckpoint(
        altConvData,
        createTestCheckpoint(
          filePath,
          "refactor-alt",
          `interface DataProcessor {
  process(data: string): string;
}
class Service implements DataProcessor {
  process(data: string) { return data.trim(); }
}`,
        ),
      );

      // User likes parts of both, bot combines approaches
      await manager.addCheckpoint(
        mainConvData,
        createTestCheckpoint(
          filePath,
          "refactor-main",
          `interface DataProcessor {
  process(data: string): string;
}
class Service implements DataProcessor {
  private data: string;
  process(input?: string) {
    return (input ?? this.data).trim();
  }
}`,
        ),
      );

      // Verify we can access all versions
      const mainHistory = await manager.getCheckpoints(mainConvData);
      const altHistory = await manager.getCheckpoints(altConvData);

      expect(mainHistory).toHaveLength(2);
      expect(altHistory).toHaveLength(1);

      // Flush changes to ensure they're persisted
      await manager.flush();

      // Verify changes are in separate shards
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-refactor-main",
        expect.anything(),
      );
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-refactor-alt",
        expect.anything(),
      );
    });

    it("should handle coordinated multi-file changes", async () => {
      const storage = createTestStorage();
      const manager = new ShardManager(storage, testShardFn);

      const interfacePath = QualifiedPathName.from({
        rootPath: "/project",
        relPath: "src/types.ts",
      });
      const implPath = QualifiedPathName.from({
        rootPath: "/project",
        relPath: "src/handler.ts",
      });
      const testPath = QualifiedPathName.from({
        rootPath: "/project",
        relPath: "src/__tests__/handler.test.ts",
      });

      const featureConvData = {
        conversationId: "add-feature",
        path: interfacePath,
      };

      // Bot adds interface
      await manager.addCheckpoint(
        { ...featureConvData, path: interfacePath },
        createTestCheckpoint(
          interfacePath,
          "add-feature",
          `export interface DataHandler {
  process(data: Buffer): Promise<string>;
}`,
        ),
      );

      // Bot adds implementation
      await manager.addCheckpoint(
        { ...featureConvData, path: implPath },
        createTestCheckpoint(
          implPath,
          "add-feature",
          `import { DataHandler } from './types';
export class BinaryHandler implements DataHandler {
  async process(data: Buffer): Promise<string> {
    return data.toString('base64');
  }
}`,
        ),
      );

      // Bot adds tests
      await manager.addCheckpoint(
        { ...featureConvData, path: testPath },
        createTestCheckpoint(
          testPath,
          "add-feature",
          `import { BinaryHandler } from '../handler';
describe('BinaryHandler', () => {
  it('should encode data', async () => {
    const handler = new BinaryHandler();
    const result = await handler.process(Buffer.from('test'));
    expect(result).toBe('dGVzdA==');
  });
});`,
        ),
      );

      // User requests different implementation
      const revisionConvData = {
        conversationId: "revise-impl",
        path: implPath,
      };

      // Bot provides alternative implementation
      await manager.addCheckpoint(
        revisionConvData,
        createTestCheckpoint(
          implPath,
          "revise-impl",
          `import { DataHandler } from './types';
export class BinaryHandler implements DataHandler {
  async process(data: Buffer): Promise<string> {
    return data.toString('hex');
  }
}`,
        ),
      );

      // Verify state of each file
      const interfaceCheckpoints = await manager.getCheckpoints({
        ...featureConvData,
        path: interfacePath,
      });
      const implCheckpoints = await manager.getCheckpoints(revisionConvData);
      const testCheckpoints = await manager.getCheckpoints({
        ...featureConvData,
        path: testPath,
      });

      // Interface and tests unchanged
      expect(interfaceCheckpoints).toHaveLength(1);
      expect(testCheckpoints).toHaveLength(1);

      // Implementation has both versions
      expect(implCheckpoints).toHaveLength(1);
      const origImpl = await manager.getCheckpoints({
        ...featureConvData,
        path: implPath,
      });
      expect(origImpl).toHaveLength(1);

      // Flush changes to ensure they're persisted
      await manager.flush();

      // Verify correct shard assignment
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-add-feature",
        expect.anything(),
      );
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-revise-impl",
        expect.anything(),
      );
    });

    it("should handle interrupted and resumed work", async () => {
      /**
       * This test verifies the system's ability to maintain separate contexts when:
       * 1. User is working on a long-running task (refactoring)
       * 2. Gets interrupted by an urgent task (bug fix)
       * 3. Returns to complete the original task
       *
       * Key behaviors being tested:
       * - Multiple conversations can modify the same files
       * - Each conversation maintains its own isolated history
       * - Changes from one conversation don't affect the state in another
       * - User can resume work with the exact same context they left
       */
      const storage = createTestStorage();
      const manager = new ShardManager(storage, testShardFn);

      // Files that will be modified in both conversations
      const routerPath = QualifiedPathName.from({
        rootPath: "/project",
        relPath: "src/router.ts",
      });
      const handlerPath = QualifiedPathName.from({
        rootPath: "/project",
        relPath: "src/handler.ts",
      });

      // Start refactoring work
      const refactorData = {
        conversationId: "major-refactor",
        path: routerPath,
      };

      // Initial refactoring changes
      await manager.addCheckpoint(
        { ...refactorData, path: routerPath },
        createTestCheckpoint(
          routerPath,
          "major-refactor",
          `export class Router {
  private handlers = new Map<string, RequestHandler>();

  async handleRequest(path: string, req: Request) {
    const handler = this.handlers.get(path);
    return handler?.process(req);
  }
}`,
        ),
      );

      await manager.addCheckpoint(
        { ...refactorData, path: handlerPath },
        createTestCheckpoint(
          handlerPath,
          "major-refactor",
          `export interface RequestHandler {
  process(req: Request): Promise<Response>;
}

export class DefaultHandler implements RequestHandler {
  async process(req: Request) {
    return new Response("OK");
  }
}`,
        ),
      );

      // Urgent bug fix needed!
      const bugfixData = {
        conversationId: "urgent-bugfix",
        path: routerPath,
      };

      // Quick fix to router
      await manager.addCheckpoint(
        { ...bugfixData, path: routerPath },
        createTestCheckpoint(
          routerPath,
          "urgent-bugfix",
          `export class Router {
  private handlers = new Map<string, RequestHandler>();

  async handleRequest(path: string, req: Request) {
    // BUGFIX: Handle missing handlers
    const handler = this.handlers.get(path);
    if (!handler) {
      return new Response("Not Found", { status: 404 });
    }
    return handler.process(req);
  }
}`,
        ),
      );

      // Quick fix to handler
      await manager.addCheckpoint(
        { ...bugfixData, path: handlerPath },
        createTestCheckpoint(
          handlerPath,
          "urgent-bugfix",
          `export interface RequestHandler {
  process(req: Request): Promise<Response>;
}

export class DefaultHandler implements RequestHandler {
  async process(req: Request) {
    // BUGFIX: Add proper headers
    return new Response("OK", {
      headers: { "Content-Type": "text/plain" }
    });
  }
}`,
        ),
      );

      // Resume refactoring work
      await manager.addCheckpoint(
        { ...refactorData, path: routerPath },
        createTestCheckpoint(
          routerPath,
          "major-refactor",
          `export class Router {
  private handlers = new Map<string, RequestHandler>();

  registerHandler(path: string, handler: RequestHandler) {
    this.handlers.set(path, handler);
  }

  async handleRequest(path: string, req: Request) {
    const handler = this.handlers.get(path);
    return handler?.process(req);
  }
}`,
        ),
      );

      // Verify conversation states
      const refactorRouterHistory = await manager.getCheckpoints({
        ...refactorData,
        path: routerPath,
      });
      const bugfixRouterHistory = await manager.getCheckpoints({
        ...bugfixData,
        path: routerPath,
      });

      // Each conversation should maintain its own history
      expect(refactorRouterHistory).toHaveLength(2);
      expect(bugfixRouterHistory).toHaveLength(1);

      // Latest states should be different
      const latestRefactor =
        refactorRouterHistory[refactorRouterHistory.length - 1];
      const latestBugfix = bugfixRouterHistory[bugfixRouterHistory.length - 1];
      expect(latestRefactor.document.modifiedCode).toContain("registerHandler");
      expect(latestBugfix.document.modifiedCode).toContain("Not Found");

      // Flush changes to ensure they're persisted
      await manager.flush();

      // Verify sharding
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-major-refactor",
        expect.anything(),
      );
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-urgent-bugfix",
        expect.anything(),
      );
    });

    it("should handle collaborative review and refinement", async () => {
      /**
       * This test verifies the system's ability to handle iterative improvement:
       * 1. Bot suggests initial changes
       * 2. User accepts some changes, rejects others
       * 3. Bot refines rejected changes
       * 4. User makes manual edits
       * 5. Bot suggests further improvements
       *
       * Key behaviors being tested:
       * - Multiple rounds of suggestions
       * - Maintaining history of iterations
       * - Different types of changes (bot vs user)
       * - Progressive refinement of code
       */
      const storage = createTestStorage();
      const manager = new ShardManager(storage, testShardFn);

      const componentPath = QualifiedPathName.from({
        rootPath: "/project",
        relPath: "src/components/UserList.tsx",
      });

      const reviewData = {
        conversationId: "code-review",
        path: componentPath,
      };

      // Initial bot suggestions
      await manager.addCheckpoint(
        reviewData,
        createTestCheckpoint(
          componentPath,
          "code-review",
          `interface User {
  id: number;
  name: string;
  email: string;
}

export function UserList({ users }: { users: User[] }) {
  if (!users?.length) {
    return <div>No users found</div>;
  }

  return (
    <ul className="user-list">
      {users.map(user => (
        <li key={user.id}>
          <div>{user.name}</div>
          <div>{user.email}</div>
        </li>
      ))}
    </ul>
  );
}`,
        ),
      );

      // User makes manual edits
      const userEditsData = {
        conversationId: "user-edits",
        path: componentPath,
      };

      await manager.addCheckpoint(
        userEditsData,
        createTestCheckpoint(
          componentPath,
          "user-edits",
          `interface User {
  id: number;
  name: string;
  email: string;
}

export function UserList({ users }: { users: User[] }) {
  if (!users?.length) {
    return <div>No users found</div>;
  }

  return (
    <ul className="user-list">
      {users.map(user => (
        <li key={user.id} className="user-item">
          <div className="user-name">{user.name}</div>
          <div className="user-email">{user.email}</div>
        </li>
      ))}
    </ul>
  );
}`,
        ),
      );

      // Bot suggests improvements to user's changes
      await manager.addCheckpoint(
        reviewData,
        createTestCheckpoint(
          componentPath,
          "code-review",
          `interface User {
  id: number;
  name: string;
  email: string;
}

interface UserListProps {
  users: User[];
  onUserSelect?: (user: User) => void;
}

export function UserList({ users, onUserSelect }: UserListProps) {
  if (!users?.length) {
    return <div className="user-list--empty">No users found</div>;
  }

  return (
    <ul className="user-list">
      {users.map(user => (
        <li
          key={user.id}
          className="user-item"
          onClick={() => onUserSelect?.(user)}
        >
          <div className="user-name">{user.name}</div>
          <div className="user-email">{user.email}</div>
        </li>
      ))}
    </ul>
  );
}`,
        ),
      );

      // Verify the progression
      const reviewHistory = await manager.getCheckpoints(reviewData);
      const userHistory = await manager.getCheckpoints(userEditsData);

      // Should have initial suggestion and refinement
      expect(reviewHistory).toHaveLength(2);
      // Should have user's manual edits
      expect(userHistory).toHaveLength(1);

      // Verify the evolution of changes
      const [initialSuggestion, refinedVersion] = reviewHistory;
      const [userEdits] = userHistory;

      // Initial suggestion adds basic structure
      expect(initialSuggestion.document.modifiedCode).not.toContain(
        'className="user-item"',
      );

      // User adds styling classes
      expect(userEdits.document.modifiedCode).toContain(
        'className="user-item"',
      );
      expect(userEdits.document.modifiedCode).toContain(
        'className="user-name"',
      );

      // Bot refines with additional features
      expect(refinedVersion.document.modifiedCode).toContain("onUserSelect");
      expect(refinedVersion.document.modifiedCode).toContain("UserListProps");

      // Flush changes to ensure they're persisted
      await manager.flush();

      // Verify changes are in separate shards
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-code-review",
        expect.anything(),
      );
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-user-edits",
        expect.anything(),
      );
    });

    it("should handle complex undo/redo flows", async () => {
      /**
       * This test verifies the system's ability to handle selective checkpoint management:
       * 1. Multiple files are modified in a conversation
       * 2. User wants to keep some changes but revert others
       * 3. Different files need to be reverted to different points in history
       *
       * Key behaviors being tested:
       * - Selective checkpoint removal
       * - Maintaining consistency across related files
       * - Handling different versions for different files
       * - Proper cleanup of removed checkpoints
       */
      const storage = createTestStorage();
      const manager = new ShardManager(storage, testShardFn);

      // Set up test files
      const paths = {
        config: QualifiedPathName.from({
          rootPath: "/project",
          relPath: "src/config.ts",
        }),
        api: QualifiedPathName.from({
          rootPath: "/project",
          relPath: "src/api.ts",
        }),
        types: QualifiedPathName.from({
          rootPath: "/project",
          relPath: "src/types.ts",
        }),
      };

      const featureData = {
        conversationId: "new-feature",
        path: paths.config,
      };

      // Initial changes to all files
      await manager.addCheckpoint(
        { ...featureData, path: paths.types },
        createTestCheckpoint(
          paths.types,
          "new-feature",
          `export interface Config {
  apiKey: string;
  endpoint: string;
}`,
        ),
      );

      await manager.addCheckpoint(
        { ...featureData, path: paths.config },
        createTestCheckpoint(
          paths.config,
          "new-feature",
          `import { Config } from './types';

export const config: Config = {
  apiKey: process.env.API_KEY ?? '',
  endpoint: 'https://api.example.com',
};`,
        ),
      );

      await manager.addCheckpoint(
        { ...featureData, path: paths.api },
        createTestCheckpoint(
          paths.api,
          "new-feature",
          `import { config } from './config';

export async function fetchData() {
  const response = await fetch(config.endpoint, {
    headers: { Authorization: config.apiKey }
  });
  return response.json();
}`,
        ),
      );

      // Second round of changes
      await manager.addCheckpoint(
        { ...featureData, path: paths.types },
        createTestCheckpoint(
          paths.types,
          "new-feature",
          `export interface Config {
  apiKey: string;
  endpoint: string;
  timeout?: number;
}`,
        ),
      );

      await manager.addCheckpoint(
        { ...featureData, path: paths.config },
        createTestCheckpoint(
          paths.config,
          "new-feature",
          `import { Config } from './types';

export const config: Config = {
  apiKey: process.env.API_KEY ?? '',
  endpoint: 'https://api.example.com',
  timeout: 5000,
};`,
        ),
      );

      await manager.addCheckpoint(
        { ...featureData, path: paths.api },
        createTestCheckpoint(
          paths.api,
          "new-feature",
          `import { config } from './config';

export async function fetchData() {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), config.timeout);

  try {
    const response = await fetch(config.endpoint, {
      headers: { Authorization: config.apiKey },
      signal: controller.signal,
    });
    return response.json();
  } finally {
    clearTimeout(timeoutId);
  }
}`,
        ),
      );

      // Verify initial state
      const getHistory = async (path: QualifiedPathName) =>
        await manager.getCheckpoints({ ...featureData, path });

      const typesHistory = await getHistory(paths.types);
      const configHistory = await getHistory(paths.config);
      const apiHistory = await getHistory(paths.api);

      expect(typesHistory).toHaveLength(2);
      expect(configHistory).toHaveLength(2);
      expect(apiHistory).toHaveLength(2);

      // Remove latest checkpoints
      await manager.removeCheckpoint(
        { ...featureData, path: paths.types },
        {
          minIdx: 1, // Remove everything from index 1 onwards
        },
      );
      await manager.removeCheckpoint(
        { ...featureData, path: paths.api },
        {
          minIdx: 1, // Remove everything from index 1 onwards (same as types)
        },
      );

      // Verify selective removal
      const updatedTypesHistory = await getHistory(paths.types);
      const updatedConfigHistory = await getHistory(paths.config);
      const updatedApiHistory = await getHistory(paths.api);

      // Types and API should be back to first version
      expect(updatedTypesHistory).toHaveLength(1);
      expect(updatedTypesHistory[0].document.modifiedCode).not.toContain(
        "timeout",
      );

      // Config should still have timeout
      expect(updatedConfigHistory).toHaveLength(2);
      expect(updatedConfigHistory[1].document.modifiedCode).toContain(
        "timeout: 5000",
      );

      // API should be back to simple version
      expect(updatedApiHistory).toHaveLength(1);
      expect(updatedApiHistory[0].document.modifiedCode).not.toContain(
        "AbortController",
      );

      // Flush changes to ensure they're persisted
      await manager.flush();

      // Verify storage operations
      expect(storage.saveShard).toHaveBeenCalledWith(
        "shard-new-feature",
        expect.anything(),
      );
    });
  });
});

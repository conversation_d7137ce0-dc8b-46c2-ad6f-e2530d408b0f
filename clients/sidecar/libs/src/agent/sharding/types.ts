import { QualifiedPathName } from "../../workspace/qualified-path-name";

/** Unique identifier for a shard */
export type ShardId = string;

/** Unique identifier for a checkpoint */
export type CheckpointDocumentId = string;

/**
 * Manifest tracking all shards in the system.
 * The manifest is always loaded in memory and provides a central
 * registry of all shards and their current state.
 */
export interface ShardManifest {
  /** Version number of the manifest format */
  version: number;
  /** Timestamp of the last manifest update */
  lastUpdated: number;
  /** Map of shard IDs to their metadata */
  shards: {
    [shardId: ShardId]: ShardMetadata;
  };
}

/**
 * Metadata about a single shard.
 * Contains summary information about the shard's contents without
 * including the actual checkpoint data.
 */
export interface ShardMetadata {
  checkpointDocumentIds: CheckpointDocumentId[];
  /** Total size in bytes of all checkpoint data */
  size: number;
  /** Total number of checkpoints across all paths */
  checkpointCount: number;
  /** Timestamp of the last modification to this shard */
  lastModified: number;
}

/**
 * Context provided to shard functions when determining shard assignment.
 * Contains read-only snapshot of current system state to help make
 * sharding decisions.
 */
export interface ShardingContext {
  /** Current state of the manifest (read-only) */
  readonly manifestSnapshot: Readonly<ShardManifest>;
  /** Current shard ID if the path is already assigned */
  readonly currentShardId?: ShardId;
  /** Statistics about the path being sharded */
  readonly pathStats: {
    /** Number of checkpoints for this path */
    checkpointCount: number;
    /** Estimated size in bytes */
    estimatedSize: number;
  };
}

/**
 * Input data for the shard function.
 */
export type CheckpointKeyInputData = {
  /** Conversation ID this checkpoint belongs to */
  conversationId: string;
  /** Path of the document */
  path: QualifiedPathName;
};

/**
 * Function that determines which shard a path should be assigned to.
 * @param inputData - The input data for the shard function
 * @param context - Current system state and path statistics
 * @returns The ID of the shard this path should be assigned to
 */
export type ShardIdFunction = (
  /** Input data for the shard function */
  inputData: CheckpointKeyInputData,
  /** Current system state and path statistics */
  context: ShardingContext,
) => ShardId;

/**
 * Function that determines the checkpoint ID for a given input.
 * @param inputData - The input data for the checkpoint ID function
 * @returns The ID of the checkpoint
 */
export type CheckpointDocumentIdFunction = (
  inputData: CheckpointKeyInputData,
) => CheckpointDocumentId;

/**
 * Configuration options for sharded storage.
 */
export interface ShardedStorageOptions {
  /** Maximum size in bytes for a single shard */
  maxShardSizeBytes?: number;
  /** Throttle time in milliseconds for manifest updates */
  manifestUpdateThrottleMs?: number;
  /** Throttle time in milliseconds for shard updates */
  shardUpdateThrottleMs?: number;
}

/**
 * Serialized representation of a document for storage.
 * Contains only the essential data needed to reconstruct a DiffViewDocument.
 */
export interface SerializedDocument {
  /** Path of the document */
  path: {
    rootPath: string;
    relPath: string;
  };
  /** Original code content, undefined means the file doesn't exist */
  originalCode: string | undefined;
  /** Modified code content, undefined means the file doesn't exist */
  modifiedCode: string | undefined;
}

/**
 * Serialized representation of a checkpoint with document content.
 * This is the legacy format used before the hydration/dehydration system.
 */
export interface SerializedCheckpoint {
  /** The ID of the tool call request that created this checkpoint */
  sourceToolCallRequestId: string;
  /** The timestamp when the checkpoint was created */
  timestamp: number;
  /** The conversation ID this checkpoint belongs to */
  conversationId: string;
  /** The serialized document */
  document: SerializedDocument;
}

/**
 * Document metadata for a checkpoint.
 * Contains information about the document's path.
 */
export interface DocumentMetadata {
  /** The path of the document */
  path: {
    rootPath: string;
    relPath: string;
  };
}

/**
 * Serialized representation of a checkpoint with document metadata.
 * This is the new format used with the hydration/dehydration system.
 */
export interface DehydratedCheckpoint {
  /** The ID of the tool call request that created this checkpoint */
  sourceToolCallRequestId: string;
  /** The timestamp when the checkpoint was created */
  timestamp: number;
  /** The conversation ID this checkpoint belongs to */
  conversationId: string;
  /** Metadata about the document */
  documentMetadata: DocumentMetadata;
}

/**
 * Represents a serialized checkpoint in a shard.
 * Can be either a SerializedCheckpoint or a DehydratedCheckpoint.
 */
export type SerializedCheckpointInShard =
  | SerializedCheckpoint
  | DehydratedCheckpoint;

/**
 * Serialized representation of a shard for storage.
 * Contains all checkpoint data and metadata for paths assigned
 * to this shard.
 */
export interface SerializedShard {
  /** Unique identifier of this shard */
  id: ShardId;
  /** Map of checkpoint IDs to their data */
  checkpoints: Record<CheckpointDocumentId, SerializedCheckpointInShard[]>;
  /** Metadata summarizing this shard's contents */
  metadata: ShardMetadata;
}

/**
 * Options for filtering checkpoints by timestamp, index, and/or sourceToolCallRequestIds
 */
export interface CheckpointFilterOptions {
  /** Minimum timestamp (inclusive) */
  minTimestamp?: number;
  /** Maximum timestamp (exclusive) */
  maxTimestamp?: number;
  /** Minimum index (inclusive) */
  minIdx?: number;
  /** Maximum index (exclusive) */
  maxIdx?: number;
  /** Set of sourceToolCallRequestIds to filter by */
  sourceToolCallRequestIds?: Set<string>;
}

/**
 * Result of a validation operation.
 * Contains both the boolean result and an optional reason for failure.
 */
export interface ValidationResult {
  /** Whether the validation passed */
  isValid: boolean;
  /** Optional reason for validation failure */
  reason?: string;
}

import { DisposableService } from "../../lifecycle/disposable-service";
import { getLogger } from "../../logging";
import { TaskManager } from "./task-manager";
import { HydratedTask } from "./task-types";
import { getMarkdownRepresentation } from "./task-utils";

/**
 * TaskSnapshotManager manages in-memory snapshots of task lists.
 * It creates a snapshot when a user sends a message in a conversation and maintains
 * that snapshot until 5 minutes of inactivity or conversation switch.
 */
export class TaskSnapshotManager extends DisposableService {
  private _currentSnapshot: string | undefined;
  private _currentConversationId: string | undefined;
  private _lastActivityTime: number = 0;
  private _inactivityThresholdMs = 5 * 60 * 1000; // 5 minutes
  private readonly _logger = getLogger("TaskSnapshotManager");

  constructor(private readonly _taskManager: TaskManager) {
    super();
  }

  /**
   * Get the current task snapshot or create one if needed.
   * @param conversationId The current conversation ID
   * @returns The task snapshot content in markdown format
   */
  public async getTaskSnapshot(
    conversationId: string,
  ): Promise<string | undefined> {
    const now = Date.now();

    // Check if we need to update the snapshot
    if (this._shouldUpdateSnapshot(conversationId, now)) {
      await this._updateSnapshot(conversationId);
    }

    // Update activity time
    this._lastActivityTime = now;

    return this._currentSnapshot;
  }

  /**
   * Force an update of the task snapshot.
   * This is used when tasks are modified.
   */
  public async forceUpdateSnapshot(): Promise<void> {
    await this._updateSnapshot(this._currentConversationId);
  }

  /**
   * Determines if the snapshot needs to be updated.
   * @param conversationId The current conversation ID
   * @param now The current timestamp
   * @returns True if the snapshot needs to be updated
   */
  private _shouldUpdateSnapshot(
    conversationId: string | undefined,
    now: number,
  ): boolean {
    // If no conversation ID, we need to update
    if (!conversationId) {
      return false;
    }

    // If conversation changed, we need to update
    if (conversationId !== this._currentConversationId) {
      return true;
    }

    // If snapshot is undefined, we need to update
    if (this._currentSnapshot === undefined) {
      return true;
    }

    // If it's been too long since the last activity, we need to update
    const timeSinceLastActivity = now - this._lastActivityTime;
    if (timeSinceLastActivity > this._inactivityThresholdMs) {
      return true;
    }

    return false;
  }

  /**
   * Updates the task snapshot.
   * @param conversationId The current conversation ID
   */
  private async _updateSnapshot(
    conversationId: string | undefined,
  ): Promise<void> {
    if (!conversationId) {
      this._currentSnapshot = undefined;
      this._currentConversationId = undefined;
      return;
    }

    try {
      // Get the root task for the conversation
      const rootTask = await this._getRootTaskForConversation(conversationId);

      if (!rootTask) {
        this._currentSnapshot = undefined;
      } else {
        // Format the task tree for the prompt
        this._currentSnapshot = getMarkdownRepresentation(rootTask);
      }

      this._currentConversationId = conversationId;
    } catch (error) {
      this._logger.error("Error updating task snapshot:", error);
      this._currentSnapshot = undefined;
    }
  }

  /**
   * Gets the root task for a conversation.
   * @param conversationId The conversation ID
   * @returns The root task for the conversation, or undefined if not found
   */
  private async _getRootTaskForConversation(
    _conversationId: string,
  ): Promise<HydratedTask | undefined> {
    try {
      // Get the current root task UUID from the task manager
      const currentRootTaskUuid = this._taskManager.getCurrentRootTaskUuid();

      // If no current root task is set, return undefined
      if (!currentRootTaskUuid) {
        return undefined;
      }

      // Get the task by UUID
      const task = await this._taskManager.getTask(currentRootTaskUuid);
      if (!task) {
        this._logger.warn(`Current root task ${currentRootTaskUuid} not found`);
        return undefined;
      }

      // Hydrate the task with its subtasks
      return await this._taskManager.hydrateTaskTree(task);
    } catch (error) {
      this._logger.error("Error getting root task for conversation:", error);
      return undefined;
    }
  }
}

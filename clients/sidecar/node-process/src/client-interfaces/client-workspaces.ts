import {
  FileDetails,
  IClientWorkspaces,
  ListDirectoryResult,
  PathInfo,
} from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import {
  FileDeletedEvent,
  FileDidMoveEvent,
} from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { ISidecarDisposable } from "@augment-internal/sidecar-libs/src/lifecycle/disposable-types";
import { Connection } from "vscode-languageserver";
import { getLogger } from "../logging";
import { sendRequestWithTimeout } from "../connection-utils";
import {
  DeleteFileRequest,
  FindFilesRequest,
  FindFilesResponse,
  GetCWDResponse,
  GetPathInfoRequest,
  GetPathInfoResponse,
  GetQualifiedPathNameRequest,
  GetQualifiedPathNameResponse,
  ListDirectoryRequest,
  ListDirectoryResponse,
  ReadFileRequest,
  ReadFileResponse,
  WriteFileRequest,
} from "$clients/sidecar/node-process/protos/client-workspaces_pb";
import { QualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/qualified-path-name";

export class ClientWorkspaces implements IClientWorkspaces {
  private _logger = getLogger("ClientWorkspaces");

  constructor(private readonly _connection: Connection) {}

  async getCwd(): Promise<string | undefined> {
    this._logger.info("Requesting cwd from host");
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/getcwd",
      undefined,
    );
    try {
      const response = GetCWDResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this._logger.info(`Got cwd from host: ${response.cwd}`);
      return response.cwd;
    } catch (err) {
      this._logger.warn(
        `Failed to parse cwd response: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return undefined;
    }
  }

  async readFile(path: string): Promise<FileDetails> {
    this._logger.info(`Requesting read file from host: ${path}`);
    const jsonResposne = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/readFile",
      new ReadFileRequest({ filePath: path }).toJson(),
    );
    if (!jsonResposne) {
      return {};
    }
    try {
      const response = ReadFileResponse.fromJson(jsonResposne, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this._logger.info(
        `Parsed read file response from host as proto ${response.filepath?.relPath} [${response.contents.length}]`,
      );
      // File not found.
      if (response.filepath === undefined) {
        return {};
      }

      let qualifiedPath: QualifiedPathName | undefined;
      if (response.filepath) {
        qualifiedPath = new QualifiedPathName(
          response.filepath.rootPath,
          response.filepath.relPath,
        );
      }
      return {
        contents: response.contents,
        filepath: qualifiedPath,
      };
    } catch (err) {
      this._logger.warn(
        `Failed to read file: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return {};
    }
  }

  async writeFile(
    filePath: QualifiedPathName,
    contents: string,
  ): Promise<void> {
    // Empty string is valid content, not a deletion signal

    this._logger.info(`Requesting write file from host: ${filePath.absPath}`);
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/writeFile",
      new WriteFileRequest({ filePath, contents }).toJson(),
    );
    this._logger.info(`write file complete`);
  }

  async deleteFile(filePath: QualifiedPathName): Promise<void> {
    this._logger.info(`Requesting delete file from host: ${filePath.absPath}`);
    await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/deleteFile",
      new DeleteFileRequest({ filePath }).toJson(),
    );
    this._logger.info(`delete file complete`);
  }

  async getQualifiedPathName(
    path: string,
  ): Promise<QualifiedPathName | undefined> {
    this._logger.info(`Requesting qualified path name from host: ${path}`);
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/getQualifiedPathName",
      new GetQualifiedPathNameRequest({ path }).toJson(),
    );
    if (!jsonResponse) {
      return undefined;
    }
    try {
      const response = GetQualifiedPathNameResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this._logger.info(
        `Parsed qualified path name response from host: ${response.filepath?.rootPath}/${response.filepath?.relPath}`,
      );

      // Path not found
      if (response.filepath === undefined) {
        return undefined;
      }

      return new QualifiedPathName(
        response.filepath.rootPath,
        response.filepath.relPath,
      );
    } catch (err) {
      this._logger.warn(
        `Failed to get qualified path name: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return undefined;
    }
  }

  async getPathInfo(path: string): Promise<PathInfo> {
    this._logger.info(`Requesting path info from host: ${path}`);
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/getPathInfo",
      new GetPathInfoRequest({ path }).toJson(),
    );

    if (!jsonResponse) {
      return { filepath: undefined };
    }

    try {
      const response = GetPathInfoResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });
      this._logger.info(
        `Parsed path info response from host: ${response.filepath?.rootPath}/${response.filepath?.relPath}`,
      );

      let qualifiedPath: QualifiedPathName | undefined;
      if (response.filepath) {
        qualifiedPath = new QualifiedPathName(
          response.filepath.rootPath,
          response.filepath.relPath,
        );
      }

      return {
        type: response.type?.valueOf(),
        filepath: qualifiedPath,
        exists: response.exists,
      };
    } catch (err) {
      this._logger.warn(
        `Failed to get path info: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return { filepath: undefined };
    }
  }

  async findFiles(
    includeGlob: string,
    excludeGlob?: string | null,
    maxResults?: number,
    _timelimit?: number,
  ): Promise<QualifiedPathName[]> {
    this._logger.info(
      `Requesting find files from host: ${includeGlob}, exclude: ${excludeGlob}, max: ${maxResults}`,
    );
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/findFiles",
      new FindFilesRequest({
        includeGlob,
        excludeGlob: excludeGlob || "",
        maxResults: maxResults || 0,
      }).toJson(),
    );

    if (!jsonResponse) {
      return [];
    }

    try {
      const response = FindFilesResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });

      this._logger.info(
        `Found ${response.files.length} files matching pattern ${includeGlob}`,
      );

      // Convert the proto QualifiedPathName objects to our QualifiedPathName class
      return response.files.map(
        (file) => new QualifiedPathName(file.rootPath, file.relPath),
      );
    } catch (err) {
      this._logger.warn(
        `Failed to find files: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return [];
    }
  }

  async listDirectory(
    path: string,
    depth: number = 2,
    showHidden: boolean = false,
  ): Promise<ListDirectoryResult> {
    this._logger.info(
      `Requesting list directory from host: ${path}, depth: ${depth}, showHidden: ${showHidden}`,
    );
    const jsonResponse = await sendRequestWithTimeout(
      this._connection,
      "augmentcode/client-workspaces/listDirectory",
      new ListDirectoryRequest({
        path,
        depth,
        showHidden,
      }).toJson(),
    );

    if (!jsonResponse) {
      return { entries: [] };
    }

    try {
      const response = ListDirectoryResponse.fromJson(jsonResponse, {
        // This is needed so that @type fields from intellij are ignored
        ignoreUnknownFields: true,
      });

      this._logger.info(
        `Parsed list directory response from host: ${response.entries.length} entries`,
      );

      return {
        entries: response.entries,
        errorMessage: response.errorMessage,
      };
    } catch (err) {
      this._logger.warn(
        `Failed to list directory: ${err instanceof Error ? err.message : (err as string)}`,
      );
      return { entries: [] };
    }
  }

  getRipgrepPath(): Promise<string | undefined> {
    return Promise.resolve(undefined);
  }

  // No-op implementation for file deletion events
  onFileDeleted(
    _callback: (event: FileDeletedEvent) => void,
  ): ISidecarDisposable {
    return {
      dispose: () => {},
    };
  }

  // No-op implementation for file move events
  onFileDidMove(
    _callback: (event: FileDidMoveEvent) => void,
  ): ISidecarDisposable {
    return {
      dispose: () => {},
    };
  }
}

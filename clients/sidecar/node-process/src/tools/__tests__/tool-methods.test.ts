// We use <protobuf type>.fromJSON as unbound methods
/* eslint-disable @typescript-eslint/unbound-method */

import path from "path";
import { LSPClient } from "./lsp-client";
import {
  ToolCallRequest,
  ToolCallResponse,
  ToolCheckSafeResponse,
  ToolSafety,
  ToolsStateResponse,
  ChatMode,
} from "$clients/sidecar/node-process/protos/tools_pb";
import { Struct } from "@bufbuild/protobuf";
import { MockClient } from "./mock-client";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import { LocalToolType } from "@augment-internal/sidecar-libs/src/tools/tool-types";

describe("Tool Methods", () => {
  let kit: ToolsTestKit;

  beforeEach(async () => {
    kit = await ToolsTestKit.factory();
  });

  afterEach(() => {
    if (kit) {
      kit.dispose();
    }
  });

  describe("tools/state", () => {
    it("state of chat tools", async () => {
      await kit.lspClient.initialize();
      //ToolsStateResponse
      const response = await kit.lspClient.sendRequestWithResponse(
        "augmentcode/tools/state",
        ToolsStateResponse.fromJson,
      );
      expect(response.error).toBeUndefined();
      expect(response.result).toBeDefined();
      const parsedResult = new ToolsStateResponse(response.result);
      expect(parsedResult).toEqual(
        new ToolsStateResponse({
          mode: ChatMode.CHAT,
          tools: [
            {
              name: SidecarToolType.webFetch.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.UNSAFE,
            },
            {
              name: SidecarToolType.codebaseRetrieval.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
          ],
        }),
      );
    });

    it("state of agent tools", async () => {
      await kit.lspClient.initialize();

      await kit.lspClient.sendRequestWithResponse<void>(
        "augmentcode/tools/change-chat-mode",
        undefined,
        {
          mode: ChatMode.AGENT,
        },
      );

      const response =
        await kit.lspClient.sendRequestWithResponse<ToolsStateResponse>(
          "augmentcode/tools/state",
          ToolsStateResponse.fromJson,
        );

      const parsedResult = new ToolsStateResponse(response.result);
      expect(parsedResult).toEqual(
        new ToolsStateResponse({
          mode: ChatMode.AGENT,
          tools: [
            {
              name: LocalToolType.saveFile.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: LocalToolType.launchProcess.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.CHECK,
            },
            {
              name: LocalToolType.readProcess.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: LocalToolType.killProcess.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: LocalToolType.writeProcess.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: LocalToolType.listProcesses.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: SidecarToolType.webFetch.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.UNSAFE,
            },
            {
              name: SidecarToolType.codebaseRetrieval.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: SidecarToolType.removeFiles.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: SidecarToolType.remember.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
            {
              name: SidecarToolType.strReplaceEditor.toString(),
              description: expect.any(String),
              inputSchemaJson: expect.any(String),
              toolSafety: ToolSafety.SAFE,
            },
          ],
        }),
      );
    });
  });

  describe("tools/safe-check", () => {
    it("check unknown tool safe", async () => {
      await kit.lspClient.initialize();
      const response =
        await kit.lspClient.sendRequestWithResponse<ToolCheckSafeResponse>(
          "augmentcode/tools/safe-check",
          ToolCheckSafeResponse.fromJson,
          {
            name: "unknown-tool",
            input: {},
          },
        );
      expect(response.error).toBeUndefined();
      expect(response.result).toBeDefined();
      expect(response.result).toEqual(
        new ToolCheckSafeResponse({ isSafe: false }),
      );
    });

    it("check known tool safety", async () => {
      await kit.lspClient.initialize();
      const response =
        await kit.lspClient.sendRequestWithResponse<ToolCheckSafeResponse>(
          "augmentcode/tools/safe-check",
          ToolCheckSafeResponse.fromJson,
          {
            name: "shell",
            input: {
              command: "echo",
              args: ["hello"],
            },
          },
        );
      expect(response.error).toBeUndefined();
      expect(response.result).toBeDefined();
      expect(response.result).toEqual(
        new ToolCheckSafeResponse({ isSafe: false }),
      );
    });
  });

  describe("tools/call", () => {
    it("call unknown tool", async () => {
      await kit.lspClient.initialize();

      const request = new ToolCallRequest({
        requestId: "1",
        toolUseId: "1",
        name: "unknown-tool",
        input: {},
        history: [],
      });
      const response =
        await kit.lspClient.sendRequestWithResponse<ToolCallResponse>(
          "augmentcode/tools/call",
          ToolCallResponse.fromJson,
          request,
        );
      expect(response.error).toBeUndefined();
      expect(response.result).toBeDefined();
      expect(response.result).toEqual(
        new ToolCallResponse({
          text: "Tool unknown-tool not found.",
          isError: true,
        }),
      );
    });

    it("call shell tool", async () => {
      await kit.lspClient.initialize();

      const request = new ToolCallRequest({
        requestId: "1",
        toolUseId: "1",
        name: "shell",
        input: Struct.fromJson({
          command: "pwd",
        }),
        history: [],
      });
      const response =
        await kit.lspClient.sendRequestWithResponse<ToolCallResponse>(
          "augmentcode/tools/call",
          ToolCallResponse.fromJson,
          request,
        );
      expect(response.error).toBeUndefined();
      expect(response.result).toBeDefined();
      expect(response.result).toEqual(
        new ToolCallResponse({
          text: expect.any(String),
          isError: true,
        }),
      );
    });
  });

  describe("tools/cancel-run", () => {
    it("cancel unknown tool", async () => {
      await kit.lspClient.initialize();

      const request = new ToolCallRequest({
        requestId: "1",
        toolUseId: "1",
      });
      const response = await kit.lspClient.sendRequestWithResponse<void>(
        "augmentcode/tools/cancel-run",
        undefined,
        request,
      );
      expect(response.error).toBeUndefined();
      expect(response.result).toBeDefined();
      expect(response.result).toEqual(null);
    });
  });
});

// TODO (mattgauntseo): Enable this test once we have a tool using the
// getCwdFromHost method
// eslint-disable-next-line jest/no-commented-out-tests
// describe("Tool -> Host Requests", () => {
//   let kit: ToolsTestKit;

//   beforeEach(async () => {
//     kit = await ToolsTestKit.factory();
//   });

//   afterEach(() => {
//     if (kit) {
//       kit.dispose();
//     }
//   });

// eslint-disable-next-line jest/no-commented-out-tests
//   it("getCwdFromHost", async () => {
//     await kit.lspClient.initialize();

//     const expectedCwd = "/example/path/to/cwd";
//     // This is what the client would implement
//     kit.lspClient.onRequest("augmentcode/tools/getcwd", () => {
//       return expectedCwd;
//     });

//     const response =
//       await kit.lspClient.sendRequestWithResponse<{ cwd: string | undefined }>(
//         "augmentcode/__test__/getCwdFromHost",
//       );
//     expect(response.error).toBeUndefined();
//     expect(response.result).toBeDefined();
//     expect(response.result!.cwd).toEqual(expectedCwd);
//   });
// });

class ToolsTestKit {
  constructor(
    public readonly lspClient: LSPClient,
    public readonly mockClient: MockClient,
  ) {}

  dispose() {
    this.lspClient.dispose();
  }

  static async factory() {
    const lspClient = await LSPClient.factory("node", [
      path.join(__dirname, "../../../out/index.js"),
      "--stdio",
    ]);
    const mockClient = new MockClient(lspClient);
    return new ToolsTestKit(lspClient, mockClient);
  }
}

load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@aspect_rules_jest//jest:defs.bzl", "jest_test")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@bazel_skylib//rules:build_test.bzl", "build_test")

npm_link_all_packages()

SRC_FILES = glob(
    ["src/**/*.ts"],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
) + [
    "//clients/sidecar/node-process/protos:sidecar_ts_protos",
]

TEST_FILES = glob([
    "src/**/__tests__/**/*.ts",
    "src/**/__tests__/__fixtures__/*.json",
    "src/**/__tests__/**/__snapshots__/*.snap",
    "src/**/__tests__/**/*.html",
    "src/**/__mocks__/**/*.ts",
])

BUILD_DEPS = [
    ":node_modules",
    "//clients/sidecar/libs:ts",
    "//:node_modules",  # Needed for @bufbuild/protobuf
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    deps = [
        "//clients:tsconfig",
        "//clients/sidecar/libs:tsconfig",
    ],
)

ts_project(
    name = "ts",
    srcs = SRC_FILES,
    out_dir = "out",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

# target to compile the extension
esbuild(
    name = "build",
    # Esbuild can build with json files, whereas ts_project cannot.
    srcs = SRC_FILES + [
        ":tsconfig",
    ],
    config = {
        "loader": {
            ".node": "file",
        },
    },
    entry_point = "src/index.ts",
    format = "cjs",
    minify = True,
    output = "out/index.js",
    platform = "node",
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

jest_test(
    name = "test",
    timeout = "moderate",
    config = "jest.config.js",
    data = SRC_FILES + TEST_FILES + BUILD_DEPS + [
        "babel.config.js",
        ":build",
        ":tsconfig",
    ],
    include_transitive_types = True,  # Needed for type checking
    node_modules = ":node_modules",
)

# Build test ensures that CI/CD runs the :ts build that checks typing
build_test(
    name = "build_test",
    targets = [
        ":ts",
        ":build",
    ],
)

{"scripts": {"build:intellij": "pnpm run build && mkdir -p ../../intellij/src/main/resources/sidecar/ && cp out/index.js ../../intellij/src/main/resources/sidecar/index.js", "watch:intellij": "pnpm dlx nodemon --exec 'pnpm run build:intellij' -e ts --watch ./src --watch ../libs/src", "build:intellij-binary": "./scripts/build-intellij-sea.sh", "watch:intellij-binary": "pnpm dlx nodemon --exec 'build:intellij-binary' -e ts --watch ./src --watch ../libs/src", "build": "pnpm run clean && pnpm run build-protos && pnpm run check-types && pnpm run esbuild", "check-types": "bazel build //clients/sidecar/node-process:ts", "esbuild": "esbuild src/index.ts --bundle --platform=node --tsconfig=./tsconfig.json --outfile=./out/index.js", "build-protos": "bazel build //clients/sidecar/node-process/protos/... && bazel run @@//clients/sidecar/node-process/protos:sidecar_ts_protos.copy", "clean": "rm -rf out", "lint": "pnpm run eslint && pnpm run prettier", "lint:fix": "pnpm run eslint:fix && pnpm run prettier:fix", "eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --files", "test": "pnpm run build && pnpm run jest", "test:debug": "AUGMENT_DEBUG_LSP=true pnpm run test", "jest": "jest"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@types/jest": "^29.5.14", "@types/lodash": "^4.14.202", "@types/node": "^22.13.0", "babel-jest": "^29.7.0", "esbuild": "^0.24.2", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-junit": "^16.0.0", "lodash": "^4.17.21", "ts-jest": "^29.2.5", "typescript": "^5.7.3", "vscode-languageserver": "^9.0.1", "vscode-languageserver-textdocument": "^1.0.11", "winston": "^3.17.0"}}
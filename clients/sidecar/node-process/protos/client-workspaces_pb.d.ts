// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/client-workspaces.proto (package com.augmentcode.sidecar.rpc.clientInterfaces, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum com.augmentcode.sidecar.rpc.clientInterfaces.FileType
 */
export declare enum FileType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: FILE = 1;
   */
  FILE = 1,

  /**
   * @generated from enum value: DIRECTORY = 2;
   */
  DIRECTORY = 2,

  /**
   * @generated from enum value: SYMBOLIC_LINK = 64;
   */
  SYMBOLIC_LINK = 64,
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GetCWDResponse
 */
export declare class GetCWDResponse extends Message<GetCWDResponse> {
  /**
   * @generated from field: string cwd = 1;
   */
  cwd: string;

  constructor(data?: PartialMessage<GetCWDResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GetCWDResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCWDResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCWDResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCWDResponse;

  static equals(a: GetCWDResponse | PlainMessage<GetCWDResponse> | undefined, b: GetCWDResponse | PlainMessage<GetCWDResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.ReadFileRequest
 */
export declare class ReadFileRequest extends Message<ReadFileRequest> {
  /**
   * @generated from field: string filePath = 1;
   */
  filePath: string;

  constructor(data?: PartialMessage<ReadFileRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.ReadFileRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReadFileRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReadFileRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReadFileRequest;

  static equals(a: ReadFileRequest | PlainMessage<ReadFileRequest> | undefined, b: ReadFileRequest | PlainMessage<ReadFileRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.ReadFileResponse
 */
export declare class ReadFileResponse extends Message<ReadFileResponse> {
  /**
   * @generated from field: string contents = 1;
   */
  contents: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName filepath = 2;
   */
  filepath?: QualifiedPathName;

  constructor(data?: PartialMessage<ReadFileResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.ReadFileResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReadFileResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReadFileResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReadFileResponse;

  static equals(a: ReadFileResponse | PlainMessage<ReadFileResponse> | undefined, b: ReadFileResponse | PlainMessage<ReadFileResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName
 */
export declare class QualifiedPathName extends Message<QualifiedPathName> {
  /**
   * @generated from field: string rootPath = 1;
   */
  rootPath: string;

  /**
   * @generated from field: string relPath = 2;
   */
  relPath: string;

  constructor(data?: PartialMessage<QualifiedPathName>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): QualifiedPathName;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): QualifiedPathName;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): QualifiedPathName;

  static equals(a: QualifiedPathName | PlainMessage<QualifiedPathName> | undefined, b: QualifiedPathName | PlainMessage<QualifiedPathName> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.WriteFileRequest
 */
export declare class WriteFileRequest extends Message<WriteFileRequest> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName filePath = 1;
   */
  filePath?: QualifiedPathName;

  /**
   * @generated from field: string contents = 2;
   */
  contents: string;

  constructor(data?: PartialMessage<WriteFileRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.WriteFileRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WriteFileRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WriteFileRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WriteFileRequest;

  static equals(a: WriteFileRequest | PlainMessage<WriteFileRequest> | undefined, b: WriteFileRequest | PlainMessage<WriteFileRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.DeleteFileRequest
 */
export declare class DeleteFileRequest extends Message<DeleteFileRequest> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName filePath = 1;
   */
  filePath?: QualifiedPathName;

  constructor(data?: PartialMessage<DeleteFileRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.DeleteFileRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteFileRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteFileRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteFileRequest;

  static equals(a: DeleteFileRequest | PlainMessage<DeleteFileRequest> | undefined, b: DeleteFileRequest | PlainMessage<DeleteFileRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GetQualifiedPathNameRequest
 */
export declare class GetQualifiedPathNameRequest extends Message<GetQualifiedPathNameRequest> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  constructor(data?: PartialMessage<GetQualifiedPathNameRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GetQualifiedPathNameRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetQualifiedPathNameRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetQualifiedPathNameRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetQualifiedPathNameRequest;

  static equals(a: GetQualifiedPathNameRequest | PlainMessage<GetQualifiedPathNameRequest> | undefined, b: GetQualifiedPathNameRequest | PlainMessage<GetQualifiedPathNameRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GetQualifiedPathNameResponse
 */
export declare class GetQualifiedPathNameResponse extends Message<GetQualifiedPathNameResponse> {
  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName filepath = 1;
   */
  filepath?: QualifiedPathName;

  constructor(data?: PartialMessage<GetQualifiedPathNameResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GetQualifiedPathNameResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetQualifiedPathNameResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetQualifiedPathNameResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetQualifiedPathNameResponse;

  static equals(a: GetQualifiedPathNameResponse | PlainMessage<GetQualifiedPathNameResponse> | undefined, b: GetQualifiedPathNameResponse | PlainMessage<GetQualifiedPathNameResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.FindFilesRequest
 */
export declare class FindFilesRequest extends Message<FindFilesRequest> {
  /**
   * @generated from field: string includeGlob = 1;
   */
  includeGlob: string;

  /**
   * @generated from field: string excludeGlob = 2;
   */
  excludeGlob: string;

  /**
   * @generated from field: int32 maxResults = 3;
   */
  maxResults: number;

  constructor(data?: PartialMessage<FindFilesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.FindFilesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindFilesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindFilesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindFilesRequest;

  static equals(a: FindFilesRequest | PlainMessage<FindFilesRequest> | undefined, b: FindFilesRequest | PlainMessage<FindFilesRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.FindFilesResponse
 */
export declare class FindFilesResponse extends Message<FindFilesResponse> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName files = 1;
   */
  files: QualifiedPathName[];

  constructor(data?: PartialMessage<FindFilesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.FindFilesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindFilesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindFilesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindFilesResponse;

  static equals(a: FindFilesResponse | PlainMessage<FindFilesResponse> | undefined, b: FindFilesResponse | PlainMessage<FindFilesResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GetPathInfoRequest
 */
export declare class GetPathInfoRequest extends Message<GetPathInfoRequest> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  constructor(data?: PartialMessage<GetPathInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GetPathInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPathInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPathInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPathInfoRequest;

  static equals(a: GetPathInfoRequest | PlainMessage<GetPathInfoRequest> | undefined, b: GetPathInfoRequest | PlainMessage<GetPathInfoRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GetPathInfoResponse
 */
export declare class GetPathInfoResponse extends Message<GetPathInfoResponse> {
  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.FileType type = 1;
   */
  type?: FileType;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName filepath = 2;
   */
  filepath?: QualifiedPathName;

  /**
   * @generated from field: optional bool exists = 3;
   */
  exists?: boolean;

  constructor(data?: PartialMessage<GetPathInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GetPathInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetPathInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetPathInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetPathInfoResponse;

  static equals(a: GetPathInfoResponse | PlainMessage<GetPathInfoResponse> | undefined, b: GetPathInfoResponse | PlainMessage<GetPathInfoResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.ListDirectoryRequest
 */
export declare class ListDirectoryRequest extends Message<ListDirectoryRequest> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  /**
   * @generated from field: int32 depth = 2;
   */
  depth: number;

  /**
   * @generated from field: bool showHidden = 3;
   */
  showHidden: boolean;

  constructor(data?: PartialMessage<ListDirectoryRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.ListDirectoryRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListDirectoryRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListDirectoryRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListDirectoryRequest;

  static equals(a: ListDirectoryRequest | PlainMessage<ListDirectoryRequest> | undefined, b: ListDirectoryRequest | PlainMessage<ListDirectoryRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.ListDirectoryResponse
 */
export declare class ListDirectoryResponse extends Message<ListDirectoryResponse> {
  /**
   * @generated from field: repeated string entries = 1;
   */
  entries: string[];

  /**
   * @generated from field: optional string errorMessage = 2;
   */
  errorMessage?: string;

  constructor(data?: PartialMessage<ListDirectoryResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.ListDirectoryResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListDirectoryResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListDirectoryResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListDirectoryResponse;

  static equals(a: ListDirectoryResponse | PlainMessage<ListDirectoryResponse> | undefined, b: ListDirectoryResponse | PlainMessage<ListDirectoryResponse> | undefined): boolean;
}


// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/plugin-storage-for-sidecar.proto (package com.augmentcode.sidecar.rpc.clientInterfaces, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum com.augmentcode.sidecar.rpc.clientInterfaces.PluginStateScope
 */
export declare enum PluginStateScope {
  /**
   * @generated from enum value: GLOBAL = 0;
   */
  GLOBAL = 0,

  /**
   * @generated from enum value: WORKSPACE = 1;
   */
  WORKSPACE = 1,
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GetStateValueRequest
 */
export declare class GetStateValueRequest extends Message<GetStateValueRequest> {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.PluginStateScope scope = 2;
   */
  scope: PluginStateScope;

  constructor(data?: PartialMessage<GetStateValueRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GetStateValueRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStateValueRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStateValueRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStateValueRequest;

  static equals(a: GetStateValueRequest | PlainMessage<GetStateValueRequest> | undefined, b: GetStateValueRequest | PlainMessage<GetStateValueRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GetStateValueResponse
 */
export declare class GetStateValueResponse extends Message<GetStateValueResponse> {
  /**
   * @generated from field: optional string jsonValue = 1;
   */
  jsonValue?: string;

  constructor(data?: PartialMessage<GetStateValueResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GetStateValueResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStateValueResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStateValueResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStateValueResponse;

  static equals(a: GetStateValueResponse | PlainMessage<GetStateValueResponse> | undefined, b: GetStateValueResponse | PlainMessage<GetStateValueResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.SetStateValueRequest
 */
export declare class SetStateValueRequest extends Message<SetStateValueRequest> {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: optional string jsonValue = 2;
   */
  jsonValue?: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.PluginStateScope scope = 3;
   */
  scope: PluginStateScope;

  constructor(data?: PartialMessage<SetStateValueRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.SetStateValueRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetStateValueRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetStateValueRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetStateValueRequest;

  static equals(a: SetStateValueRequest | PlainMessage<SetStateValueRequest> | undefined, b: SetStateValueRequest | PlainMessage<SetStateValueRequest> | undefined): boolean;
}


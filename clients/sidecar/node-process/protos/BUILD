load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "sidecar_protos",
    srcs = glob(["**/*.proto"]),
    visibility = ["//clients:__subpackages__"],
    deps = [
        "@protobuf//:any_proto",
        "@protobuf//:descriptor_proto",
        "@protobuf//:struct_proto",
        "@protobuf//:timestamp_proto",
    ],
)

ts_proto_library(
    name = "sidecar_ts_protos",
    copy_files = True,
    files_to_copy = [
        "sidecarrpc_pb.d.ts",
        "tools_pb.d.ts",
        "client-workspaces_pb.d.ts",
        "api-client_pb.d.ts",
        "chat_pb.d.ts",
        "plugin-file-store_pb.d.ts",
        "plugin-storage-for-sidecar_pb.d.ts",
        "client-actions_pb.d.ts",
    ],
    node_modules = "//:node_modules",
    proto = ":sidecar_protos",
    visibility = ["//clients:__subpackages__"],
)

// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/api-client.proto (package com.augmentcode.sidecar.rpc.clientInterfaces, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { ChatHistoryItem } from "./chat_pb.js";
import type { RemoteToolId } from "./tools_pb.js";

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalRequest
 */
export declare class AgentCodebaseRetrievalRequest extends Message<AgentCodebaseRetrievalRequest> {
  /**
   * @generated from field: string informationRequest = 1;
   */
  informationRequest: string;

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.ChatHistoryItem chatHistory = 2;
   */
  chatHistory: ChatHistoryItem[];

  /**
   * @generated from field: int32 maxOutputLength = 3;
   */
  maxOutputLength: number;

  constructor(data?: PartialMessage<AgentCodebaseRetrievalRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentCodebaseRetrievalRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentCodebaseRetrievalRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentCodebaseRetrievalRequest;

  static equals(a: AgentCodebaseRetrievalRequest | PlainMessage<AgentCodebaseRetrievalRequest> | undefined, b: AgentCodebaseRetrievalRequest | PlainMessage<AgentCodebaseRetrievalRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalResponse
 */
export declare class AgentCodebaseRetrievalResponse extends Message<AgentCodebaseRetrievalResponse> {
  /**
   * @generated from field: string formattedRetrieval = 1;
   */
  formattedRetrieval: string;

  constructor(data?: PartialMessage<AgentCodebaseRetrievalResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.AgentCodebaseRetrievalResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentCodebaseRetrievalResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentCodebaseRetrievalResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentCodebaseRetrievalResponse;

  static equals(a: AgentCodebaseRetrievalResponse | PlainMessage<AgentCodebaseRetrievalResponse> | undefined, b: AgentCodebaseRetrievalResponse | PlainMessage<AgentCodebaseRetrievalResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.CheckToolSafetyRequest
 */
export declare class CheckToolSafetyRequest extends Message<CheckToolSafetyRequest> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.RemoteToolId toolId = 1;
   */
  toolId: RemoteToolId;

  /**
   * @generated from field: string toolInputJson = 2;
   */
  toolInputJson: string;

  constructor(data?: PartialMessage<CheckToolSafetyRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.CheckToolSafetyRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckToolSafetyRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckToolSafetyRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckToolSafetyRequest;

  static equals(a: CheckToolSafetyRequest | PlainMessage<CheckToolSafetyRequest> | undefined, b: CheckToolSafetyRequest | PlainMessage<CheckToolSafetyRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.CheckToolSafetyResponse
 */
export declare class CheckToolSafetyResponse extends Message<CheckToolSafetyResponse> {
  /**
   * @generated from field: bool isSafe = 1;
   */
  isSafe: boolean;

  constructor(data?: PartialMessage<CheckToolSafetyResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.CheckToolSafetyResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CheckToolSafetyResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CheckToolSafetyResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CheckToolSafetyResponse;

  static equals(a: CheckToolSafetyResponse | PlainMessage<CheckToolSafetyResponse> | undefined, b: CheckToolSafetyResponse | PlainMessage<CheckToolSafetyResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.LogAgentSessionEvent
 */
export declare class LogAgentSessionEvent extends Message<LogAgentSessionEvent> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.clientInterfaces.AgentSessionEvent events = 1;
   */
  events: AgentSessionEvent[];

  constructor(data?: PartialMessage<LogAgentSessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.LogAgentSessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LogAgentSessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LogAgentSessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LogAgentSessionEvent;

  static equals(a: LogAgentSessionEvent | PlainMessage<LogAgentSessionEvent> | undefined, b: LogAgentSessionEvent | PlainMessage<LogAgentSessionEvent> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.AgentSessionEvent
 */
export declare class AgentSessionEvent extends Message<AgentSessionEvent> {
  /**
   * @generated from field: int32 event_time_sec = 1;
   */
  eventTimeSec: number;

  /**
   * @generated from field: int32 event_time_nsec = 2;
   */
  eventTimeNsec: number;

  /**
   * @generated from field: string event_name = 3;
   */
  eventName: string;

  /**
   * @generated from field: string conversation_id = 4;
   */
  conversationId: string;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.EventData event_data = 5;
   */
  eventData?: EventData;

  constructor(data?: PartialMessage<AgentSessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.AgentSessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentSessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentSessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentSessionEvent;

  static equals(a: AgentSessionEvent | PlainMessage<AgentSessionEvent> | undefined, b: AgentSessionEvent | PlainMessage<AgentSessionEvent> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.EventData
 */
export declare class EventData extends Message<EventData> {
  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.AgentInterruptionData agent_interruption_data = 2;
   */
  agentInterruptionData?: AgentInterruptionData;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.RememberToolCallData remember_tool_call_data = 3;
   */
  rememberToolCallData?: RememberToolCallData;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.MemoriesFileOpenData memories_file_open_data = 4;
   */
  memoriesFileOpenData?: MemoriesFileOpenData;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.InitialOrientationData initial_orientation_data = 5;
   */
  initialOrientationData?: InitialOrientationData;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.GenericTracingData classify_and_distill_data = 6;
   */
  classifyAndDistillData?: GenericTracingData;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.clientInterfaces.GenericTracingData flush_memories_data = 7;
   */
  flushMemoriesData?: GenericTracingData;

  constructor(data?: PartialMessage<EventData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.EventData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EventData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EventData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EventData;

  static equals(a: EventData | PlainMessage<EventData> | undefined, b: EventData | PlainMessage<EventData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.RememberToolCallData
 */
export declare class RememberToolCallData extends Message<RememberToolCallData> {
  /**
   * @generated from field: int32 caller = 1;
   */
  caller: number;

  /**
   * @generated from field: bool is_complex_new_memory = 2;
   */
  isComplexNewMemory: boolean;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData tracing_data = 3;
   */
  tracingData?: AgentTracingData;

  constructor(data?: PartialMessage<RememberToolCallData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.RememberToolCallData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RememberToolCallData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RememberToolCallData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RememberToolCallData;

  static equals(a: RememberToolCallData | PlainMessage<RememberToolCallData> | undefined, b: RememberToolCallData | PlainMessage<RememberToolCallData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData
 */
export declare class AgentTracingData extends Message<AgentTracingData> {
  /**
   * @generated from field: map<string, com.augmentcode.sidecar.rpc.clientInterfaces.TimedBool> flags = 1;
   */
  flags: { [key: string]: TimedBool };

  /**
   * @generated from field: map<string, com.augmentcode.sidecar.rpc.clientInterfaces.TimedNumber> nums = 2;
   */
  nums: { [key: string]: TimedNumber };

  /**
   * @generated from field: map<string, com.augmentcode.sidecar.rpc.clientInterfaces.TimedStringStats> string_stats = 3;
   */
  stringStats: { [key: string]: TimedStringStats };

  /**
   * @generated from field: map<string, com.augmentcode.sidecar.rpc.clientInterfaces.TimedString> request_ids = 4;
   */
  requestIds: { [key: string]: TimedString };

  constructor(data?: PartialMessage<AgentTracingData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentTracingData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentTracingData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentTracingData;

  static equals(a: AgentTracingData | PlainMessage<AgentTracingData> | undefined, b: AgentTracingData | PlainMessage<AgentTracingData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.TimedBool
 */
export declare class TimedBool extends Message<TimedBool> {
  /**
   * @generated from field: bool value = 1;
   */
  value: boolean;

  /**
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  constructor(data?: PartialMessage<TimedBool>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.TimedBool";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TimedBool;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TimedBool;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TimedBool;

  static equals(a: TimedBool | PlainMessage<TimedBool> | undefined, b: TimedBool | PlainMessage<TimedBool> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.TimedNumber
 */
export declare class TimedNumber extends Message<TimedNumber> {
  /**
   * @generated from field: double value = 1;
   */
  value: number;

  /**
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  constructor(data?: PartialMessage<TimedNumber>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.TimedNumber";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TimedNumber;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TimedNumber;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TimedNumber;

  static equals(a: TimedNumber | PlainMessage<TimedNumber> | undefined, b: TimedNumber | PlainMessage<TimedNumber> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.TimedStringStats
 */
export declare class TimedStringStats extends Message<TimedStringStats> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.StringStats value = 1;
   */
  value?: StringStats;

  /**
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  constructor(data?: PartialMessage<TimedStringStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.TimedStringStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TimedStringStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TimedStringStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TimedStringStats;

  static equals(a: TimedStringStats | PlainMessage<TimedStringStats> | undefined, b: TimedStringStats | PlainMessage<TimedStringStats> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.TimedString
 */
export declare class TimedString extends Message<TimedString> {
  /**
   * @generated from field: string value = 1;
   */
  value: string;

  /**
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  constructor(data?: PartialMessage<TimedString>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.TimedString";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TimedString;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TimedString;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TimedString;

  static equals(a: TimedString | PlainMessage<TimedString> | undefined, b: TimedString | PlainMessage<TimedString> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.StringStats
 */
export declare class StringStats extends Message<StringStats> {
  /**
   * @generated from field: int32 num_lines = 1;
   */
  numLines: number;

  /**
   * @generated from field: int32 num_chars = 2;
   */
  numChars: number;

  constructor(data?: PartialMessage<StringStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.StringStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StringStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StringStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StringStats;

  static equals(a: StringStats | PlainMessage<StringStats> | undefined, b: StringStats | PlainMessage<StringStats> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.MemoriesFileOpenData
 */
export declare class MemoriesFileOpenData extends Message<MemoriesFileOpenData> {
  /**
   * @generated from field: optional bool memories_path_undefined = 1;
   */
  memoriesPathUndefined?: boolean;

  constructor(data?: PartialMessage<MemoriesFileOpenData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.MemoriesFileOpenData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MemoriesFileOpenData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MemoriesFileOpenData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MemoriesFileOpenData;

  static equals(a: MemoriesFileOpenData | PlainMessage<MemoriesFileOpenData> | undefined, b: MemoriesFileOpenData | PlainMessage<MemoriesFileOpenData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.InitialOrientationData
 */
export declare class InitialOrientationData extends Message<InitialOrientationData> {
  /**
   * @generated from field: int32 caller = 1;
   */
  caller: number;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData tracing_data = 2;
   */
  tracingData?: AgentTracingData;

  constructor(data?: PartialMessage<InitialOrientationData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.InitialOrientationData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InitialOrientationData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InitialOrientationData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InitialOrientationData;

  static equals(a: InitialOrientationData | PlainMessage<InitialOrientationData> | undefined, b: InitialOrientationData | PlainMessage<InitialOrientationData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.GenericTracingData
 */
export declare class GenericTracingData extends Message<GenericTracingData> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.AgentTracingData tracing_data = 1;
   */
  tracingData?: AgentTracingData;

  constructor(data?: PartialMessage<GenericTracingData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.GenericTracingData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenericTracingData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenericTracingData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenericTracingData;

  static equals(a: GenericTracingData | PlainMessage<GenericTracingData> | undefined, b: GenericTracingData | PlainMessage<GenericTracingData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.AgentInterruptionData
 */
export declare class AgentInterruptionData extends Message<AgentInterruptionData> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: int32 curr_conversation_length = 2;
   */
  currConversationLength: number;

  constructor(data?: PartialMessage<AgentInterruptionData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.AgentInterruptionData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentInterruptionData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentInterruptionData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentInterruptionData;

  static equals(a: AgentInterruptionData | PlainMessage<AgentInterruptionData> | undefined, b: AgentInterruptionData | PlainMessage<AgentInterruptionData> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.LogAgentRequestEvent
 */
export declare class LogAgentRequestEvent extends Message<LogAgentRequestEvent> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.clientInterfaces.AgentRequestEvent events = 1;
   */
  events: AgentRequestEvent[];

  constructor(data?: PartialMessage<LogAgentRequestEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.LogAgentRequestEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LogAgentRequestEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LogAgentRequestEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LogAgentRequestEvent;

  static equals(a: LogAgentRequestEvent | PlainMessage<LogAgentRequestEvent> | undefined, b: LogAgentRequestEvent | PlainMessage<LogAgentRequestEvent> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.AgentRequestEvent
 */
export declare class AgentRequestEvent extends Message<AgentRequestEvent> {
  /**
   * @generated from field: int32 event_time_sec = 1;
   */
  eventTimeSec: number;

  /**
   * @generated from field: int32 event_time_nsec = 2;
   */
  eventTimeNsec: number;

  /**
   * @generated from field: string event_name = 3;
   */
  eventName: string;

  /**
   * @generated from field: string conversation_id = 4;
   */
  conversationId: string;

  /**
   * @generated from field: string request_id = 5;
   */
  requestId: string;

  /**
   * @generated from field: int32 chat_history_length = 6;
   */
  chatHistoryLength: number;

  constructor(data?: PartialMessage<AgentRequestEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.AgentRequestEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentRequestEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentRequestEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentRequestEvent;

  static equals(a: AgentRequestEvent | PlainMessage<AgentRequestEvent> | undefined, b: AgentRequestEvent | PlainMessage<AgentRequestEvent> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.LogFeatureVectorRequest
 */
export declare class LogFeatureVectorRequest extends Message<LogFeatureVectorRequest> {
  /**
   * @generated from field: map<int32, string> feature_vector = 1;
   */
  featureVector: { [key: number]: string };

  constructor(data?: PartialMessage<LogFeatureVectorRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.LogFeatureVectorRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LogFeatureVectorRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LogFeatureVectorRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LogFeatureVectorRequest;

  static equals(a: LogFeatureVectorRequest | PlainMessage<LogFeatureVectorRequest> | undefined, b: LogFeatureVectorRequest | PlainMessage<LogFeatureVectorRequest> | undefined): boolean;
}


// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/chat.proto (package com.augmentcode.sidecar.rpc.chat, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum com.augmentcode.sidecar.rpc.chat.ChatRequestNodeType
 */
export declare enum ChatRequestNodeType {
  /**
   * @generated from enum value: TEXT = 0;
   */
  TEXT = 0,

  /**
   * @generated from enum value: TOOL_RESULT = 1;
   */
  TOOL_RESULT = 1,

  /**
   * @generated from enum value: IMAGE = 2;
   */
  IMAGE = 2,

  /**
   * @generated from enum value: IMAGE_ID = 3;
   */
  IMAGE_ID = 3,

  /**
   * @generated from enum value: IDE_STATE = 4;
   */
  IDE_STATE = 4,

  /**
   * @generated from enum value: EDIT_EVENTS = 5;
   */
  EDIT_EVENTS = 5,

  /**
   * @generated from enum value: CHECKPOINT_REF = 6;
   */
  CHECKPOINT_REF = 6,
}

/**
 * @generated from enum com.augmentcode.sidecar.rpc.chat.ImageFormatType
 */
export declare enum ImageFormatType {
  /**
   * @generated from enum value: IMAGE_FORMAT_UNSPECIFIED = 0;
   */
  IMAGE_FORMAT_UNSPECIFIED = 0,

  /**
   * @generated from enum value: PNG = 1;
   */
  PNG = 1,

  /**
   * @generated from enum value: JPEG = 2;
   */
  JPEG = 2,

  /**
   * @generated from enum value: GIF = 3;
   */
  GIF = 3,

  /**
   * @generated from enum value: WEBP = 4;
   */
  WEBP = 4,
}

/**
 * @generated from enum com.augmentcode.sidecar.rpc.chat.ChatRequestContentNodeType
 */
export declare enum ChatRequestContentNodeType {
  /**
   * @generated from enum value: CONTENT_TYPE_UNSPECIFIED = 0;
   */
  CONTENT_TYPE_UNSPECIFIED = 0,

  /**
   * @generated from enum value: CONTENT_TEXT = 1;
   */
  CONTENT_TEXT = 1,

  /**
   * @generated from enum value: CONTENT_IMAGE = 2;
   */
  CONTENT_IMAGE = 2,
}

/**
 * @generated from enum com.augmentcode.sidecar.rpc.chat.ChatResultNodeType
 */
export declare enum ChatResultNodeType {
  /**
   * @generated from enum value: RAW_RESPONSE = 0;
   */
  RAW_RESPONSE = 0,

  /**
   * @generated from enum value: SUGGESTED_QUESTIONS = 1;
   */
  SUGGESTED_QUESTIONS = 1,

  /**
   * @generated from enum value: MAIN_TEXT_FINISHED = 2;
   */
  MAIN_TEXT_FINISHED = 2,

  /**
   * @generated from enum value: WORKSPACE_FILE_CHUNKS = 3;
   */
  WORKSPACE_FILE_CHUNKS = 3,

  /**
   * @generated from enum value: RELEVANT_SOURCES = 4;
   */
  RELEVANT_SOURCES = 4,

  /**
   * @generated from enum value: TOOL_USE = 5;
   */
  TOOL_USE = 5,
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
 */
export declare class ChatHistoryItem extends Message<ChatHistoryItem> {
  /**
   * @generated from field: string request_message = 1;
   */
  requestMessage: string;

  /**
   * @generated from field: string response_text = 2;
   */
  responseText: string;

  /**
   * @generated from field: string request_id = 3;
   */
  requestId: string;

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.ChatRequestNode request_nodes = 4;
   */
  requestNodes: ChatRequestNode[];

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.ChatResultNode response_nodes = 5;
   */
  responseNodes: ChatResultNode[];

  constructor(data?: PartialMessage<ChatHistoryItem>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatHistoryItem";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatHistoryItem;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatHistoryItem;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatHistoryItem;

  static equals(a: ChatHistoryItem | PlainMessage<ChatHistoryItem> | undefined, b: ChatHistoryItem | PlainMessage<ChatHistoryItem> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatRequestNode
 */
export declare class ChatRequestNode extends Message<ChatRequestNode> {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: int32 type = 2;
   */
  type: number;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatRequestText text_node = 3;
   */
  textNode?: ChatRequestText;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatRequestToolResult tool_result_node = 4;
   */
  toolResultNode?: ChatRequestToolResult;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatRequestImage image_node = 5;
   */
  imageNode?: ChatRequestImage;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatRequestImageId image_id_node = 6;
   */
  imageIdNode?: ChatRequestImageId;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatRequestIdeState ide_state_node = 7;
   */
  ideStateNode?: ChatRequestIdeState;

  constructor(data?: PartialMessage<ChatRequestNode>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatRequestNode";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatRequestNode;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatRequestNode;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatRequestNode;

  static equals(a: ChatRequestNode | PlainMessage<ChatRequestNode> | undefined, b: ChatRequestNode | PlainMessage<ChatRequestNode> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatRequestIdeState
 */
export declare class ChatRequestIdeState extends Message<ChatRequestIdeState> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.WorkspaceFolderInfo workspace_folders = 1;
   */
  workspaceFolders: WorkspaceFolderInfo[];

  /**
   * @generated from field: bool workspace_folders_unchanged = 2;
   */
  workspaceFoldersUnchanged: boolean;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.TerminalInfo current_terminal = 3;
   */
  currentTerminal?: TerminalInfo;

  constructor(data?: PartialMessage<ChatRequestIdeState>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatRequestIdeState";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatRequestIdeState;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatRequestIdeState;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatRequestIdeState;

  static equals(a: ChatRequestIdeState | PlainMessage<ChatRequestIdeState> | undefined, b: ChatRequestIdeState | PlainMessage<ChatRequestIdeState> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.WorkspaceFolderInfo
 */
export declare class WorkspaceFolderInfo extends Message<WorkspaceFolderInfo> {
  /**
   * @generated from field: string repository_root = 1;
   */
  repositoryRoot: string;

  /**
   * @generated from field: string folder_root = 2;
   */
  folderRoot: string;

  constructor(data?: PartialMessage<WorkspaceFolderInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.WorkspaceFolderInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceFolderInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceFolderInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceFolderInfo;

  static equals(a: WorkspaceFolderInfo | PlainMessage<WorkspaceFolderInfo> | undefined, b: WorkspaceFolderInfo | PlainMessage<WorkspaceFolderInfo> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.TerminalInfo
 */
export declare class TerminalInfo extends Message<TerminalInfo> {
  /**
   * @generated from field: int32 terminal_id = 1;
   */
  terminalId: number;

  /**
   * @generated from field: string current_working_directory = 2;
   */
  currentWorkingDirectory: string;

  constructor(data?: PartialMessage<TerminalInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.TerminalInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TerminalInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TerminalInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TerminalInfo;

  static equals(a: TerminalInfo | PlainMessage<TerminalInfo> | undefined, b: TerminalInfo | PlainMessage<TerminalInfo> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatRequestImage
 */
export declare class ChatRequestImage extends Message<ChatRequestImage> {
  /**
   * @generated from field: string image_data = 1;
   */
  imageData: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.chat.ImageFormatType format = 2;
   */
  format: ImageFormatType;

  constructor(data?: PartialMessage<ChatRequestImage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatRequestImage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatRequestImage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatRequestImage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatRequestImage;

  static equals(a: ChatRequestImage | PlainMessage<ChatRequestImage> | undefined, b: ChatRequestImage | PlainMessage<ChatRequestImage> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatRequestImageId
 */
export declare class ChatRequestImageId extends Message<ChatRequestImageId> {
  /**
   * @generated from field: string image_id = 1;
   */
  imageId: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.chat.ImageFormatType format = 2;
   */
  format: ImageFormatType;

  constructor(data?: PartialMessage<ChatRequestImageId>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatRequestImageId";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatRequestImageId;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatRequestImageId;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatRequestImageId;

  static equals(a: ChatRequestImageId | PlainMessage<ChatRequestImageId> | undefined, b: ChatRequestImageId | PlainMessage<ChatRequestImageId> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatRequestText
 */
export declare class ChatRequestText extends Message<ChatRequestText> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  constructor(data?: PartialMessage<ChatRequestText>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatRequestText";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatRequestText;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatRequestText;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatRequestText;

  static equals(a: ChatRequestText | PlainMessage<ChatRequestText> | undefined, b: ChatRequestText | PlainMessage<ChatRequestText> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatRequestContentNode
 */
export declare class ChatRequestContentNode extends Message<ChatRequestContentNode> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.chat.ChatRequestContentNodeType type = 1;
   */
  type: ChatRequestContentNodeType;

  /**
   * @generated from field: optional string text_content = 2;
   */
  textContent?: string;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatRequestImage image_content = 3;
   */
  imageContent?: ChatRequestImage;

  constructor(data?: PartialMessage<ChatRequestContentNode>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatRequestContentNode";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatRequestContentNode;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatRequestContentNode;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatRequestContentNode;

  static equals(a: ChatRequestContentNode | PlainMessage<ChatRequestContentNode> | undefined, b: ChatRequestContentNode | PlainMessage<ChatRequestContentNode> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatRequestToolResult
 */
export declare class ChatRequestToolResult extends Message<ChatRequestToolResult> {
  /**
   * @generated from field: string tool_use_id = 1;
   */
  toolUseId: string;

  /**
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * @generated from field: bool is_error = 3;
   */
  isError: boolean;

  /**
   * @generated from field: optional string request_id = 4;
   */
  requestId?: string;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatRequestImage image = 5;
   */
  image?: ChatRequestImage;

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.ChatRequestContentNode content_nodes = 6;
   */
  contentNodes: ChatRequestContentNode[];

  constructor(data?: PartialMessage<ChatRequestToolResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatRequestToolResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatRequestToolResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatRequestToolResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatRequestToolResult;

  static equals(a: ChatRequestToolResult | PlainMessage<ChatRequestToolResult> | undefined, b: ChatRequestToolResult | PlainMessage<ChatRequestToolResult> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatResultNode
 */
export declare class ChatResultNode extends Message<ChatResultNode> {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.chat.ChatResultNodeType type = 2;
   */
  type: ChatResultNodeType;

  /**
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatResultToolUse toolUse = 4 [json_name = "tool_use"];
   */
  toolUse?: ChatResultToolUse;

  /**
   * @generated from field: optional com.augmentcode.sidecar.rpc.chat.ChatResultAgentMemory agentMemory = 5 [json_name = "agent_memory"];
   */
  agentMemory?: ChatResultAgentMemory;

  constructor(data?: PartialMessage<ChatResultNode>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatResultNode";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatResultNode;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatResultNode;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatResultNode;

  static equals(a: ChatResultNode | PlainMessage<ChatResultNode> | undefined, b: ChatResultNode | PlainMessage<ChatResultNode> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatResultToolUse
 */
export declare class ChatResultToolUse extends Message<ChatResultToolUse> {
  /**
   * @generated from field: string toolUseId = 1 [json_name = "tool_use_id"];
   */
  toolUseId: string;

  /**
   * @generated from field: string toolName = 2 [json_name = "tool_name"];
   */
  toolName: string;

  /**
   * @generated from field: string inputJson = 3 [json_name = "input_json"];
   */
  inputJson: string;

  /**
   * @generated from field: string mcpServerName = 4 [json_name = "mcp_server_name"];
   */
  mcpServerName: string;

  /**
   * @generated from field: string mcpToolName = 5 [json_name = "mcp_tool_name"];
   */
  mcpToolName: string;

  constructor(data?: PartialMessage<ChatResultToolUse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatResultToolUse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatResultToolUse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatResultToolUse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatResultToolUse;

  static equals(a: ChatResultToolUse | PlainMessage<ChatResultToolUse> | undefined, b: ChatResultToolUse | PlainMessage<ChatResultToolUse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatResultAgentMemory
 */
export declare class ChatResultAgentMemory extends Message<ChatResultAgentMemory> {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  constructor(data?: PartialMessage<ChatResultAgentMemory>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatResultAgentMemory";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatResultAgentMemory;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatResultAgentMemory;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatResultAgentMemory;

  static equals(a: ChatResultAgentMemory | PlainMessage<ChatResultAgentMemory> | undefined, b: ChatResultAgentMemory | PlainMessage<ChatResultAgentMemory> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatStreamRequest
 */
export declare class ChatStreamRequest extends Message<ChatStreamRequest> {
  /**
   * @generated from field: string progressToken = 1;
   */
  progressToken: string;

  /**
   * @generated from field: string requestPayloadJson = 2;
   */
  requestPayloadJson: string;

  constructor(data?: PartialMessage<ChatStreamRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatStreamRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatStreamRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatStreamRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatStreamRequest;

  static equals(a: ChatStreamRequest | PlainMessage<ChatStreamRequest> | undefined, b: ChatStreamRequest | PlainMessage<ChatStreamRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.chat.ChatStreamResponse
 */
export declare class ChatStreamResponse extends Message<ChatStreamResponse> {
  /**
   * @generated from field: string responsePayloadJson = 1;
   */
  responsePayloadJson: string;

  constructor(data?: PartialMessage<ChatStreamResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.chat.ChatStreamResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatStreamResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatStreamResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatStreamResponse;

  static equals(a: ChatStreamResponse | PlainMessage<ChatStreamResponse> | undefined, b: ChatStreamResponse | PlainMessage<ChatStreamResponse> | undefined): boolean;
}


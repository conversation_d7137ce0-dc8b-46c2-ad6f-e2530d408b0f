// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/tools.proto (package com.augmentcode.sidecar.rpc.tools, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Struct } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { ChatHistoryItem, ChatRequestContentNode } from "./chat_pb.js";

/**
 * @generated from enum com.augmentcode.sidecar.rpc.tools.ToolSafety
 */
export declare enum ToolSafety {
  /**
   * @generated from enum value: UNSAFE = 0;
   */
  UNSAFE = 0,

  /**
   * @generated from enum value: SAFE = 1;
   */
  SAFE = 1,

  /**
   * @generated from enum value: CHECK = 2;
   */
  CHECK = 2,
}

/**
 * @generated from enum com.augmentcode.sidecar.rpc.tools.RemoteToolId
 */
export declare enum RemoteToolId {
  /**
   * @generated from enum value: Unknown = 0;
   */
  Unknown = 0,

  /**
   * @generated from enum value: WebSearch = 1;
   */
  WebSearch = 1,

  /**
   * @generated from enum value: GitHubApi = 8;
   */
  GitHubApi = 8,

  /**
   * @generated from enum value: Linear = 12;
   */
  Linear = 12,

  /**
   * @generated from enum value: Jira = 13;
   */
  Jira = 13,

  /**
   * @generated from enum value: Confluence = 14;
   */
  Confluence = 14,

  /**
   * @generated from enum value: Notion = 15;
   */
  Notion = 15,

  /**
   * @generated from enum value: Supabase = 16;
   */
  Supabase = 16,

  /**
   * @generated from enum value: Glean = 17;
   */
  Glean = 17,
}

/**
 * @generated from enum com.augmentcode.sidecar.rpc.tools.ToolAvailabilityStatus
 */
export declare enum ToolAvailabilityStatus {
  /**
   * @generated from enum value: UnknownStatus = 0;
   */
  UnknownStatus = 0,

  /**
   * @generated from enum value: Available = 1;
   */
  Available = 1,

  /**
   * @generated from enum value: UserConfigRequired = 2;
   */
  UserConfigRequired = 2,
}

/**
 * @generated from enum com.augmentcode.sidecar.rpc.tools.ChatMode
 */
export declare enum ChatMode {
  /**
   * @generated from enum value: CHAT = 0;
   */
  CHAT = 0,

  /**
   * @generated from enum value: AGENT = 1;
   */
  AGENT = 1,
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolsStateResponse
 */
export declare class ToolsStateResponse extends Message<ToolsStateResponse> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ChatMode mode = 1;
   */
  mode: ChatMode;

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.tools.ToolDefinition tools = 2;
   */
  tools: ToolDefinition[];

  constructor(data?: PartialMessage<ToolsStateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolsStateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolsStateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolsStateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolsStateResponse;

  static equals(a: ToolsStateResponse | PlainMessage<ToolsStateResponse> | undefined, b: ToolsStateResponse | PlainMessage<ToolsStateResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolDefinition
 */
export declare class ToolDefinition extends Message<ToolDefinition> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: string input_schema_json = 3;
   */
  inputSchemaJson: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ToolSafety tool_safety = 4;
   */
  toolSafety: ToolSafety;

  /**
   * @generated from field: string mcp_server_name = 5;
   */
  mcpServerName: string;

  /**
   * @generated from field: string mcp_tool_name = 6;
   */
  mcpToolName: string;

  constructor(data?: PartialMessage<ToolDefinition>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolDefinition";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolDefinition;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolDefinition;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolDefinition;

  static equals(a: ToolDefinition | PlainMessage<ToolDefinition> | undefined, b: ToolDefinition | PlainMessage<ToolDefinition> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolCallRequest
 */
export declare class ToolCallRequest extends Message<ToolCallRequest> {
  /**
   * @generated from field: string requestId = 1;
   */
  requestId: string;

  /**
   * @generated from field: string toolUseId = 2;
   */
  toolUseId: string;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: google.protobuf.Struct input = 4;
   */
  input?: Struct;

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.ChatHistoryItem history = 5;
   */
  history: ChatHistoryItem[];

  /**
   * @generated from field: string conversationId = 6;
   */
  conversationId: string;

  constructor(data?: PartialMessage<ToolCallRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolCallRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolCallRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolCallRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolCallRequest;

  static equals(a: ToolCallRequest | PlainMessage<ToolCallRequest> | undefined, b: ToolCallRequest | PlainMessage<ToolCallRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolCallResponse
 */
export declare class ToolCallResponse extends Message<ToolCallResponse> {
  /**
   * @generated from field: string text = 1;
   */
  text: string;

  /**
   * @generated from field: bool isError = 2;
   */
  isError: boolean;

  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.chat.ChatRequestContentNode content_nodes = 3;
   */
  contentNodes: ChatRequestContentNode[];

  constructor(data?: PartialMessage<ToolCallResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolCallResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolCallResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolCallResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolCallResponse;

  static equals(a: ToolCallResponse | PlainMessage<ToolCallResponse> | undefined, b: ToolCallResponse | PlainMessage<ToolCallResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolCheckSafeRequest
 */
export declare class ToolCheckSafeRequest extends Message<ToolCheckSafeRequest> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: google.protobuf.Struct input = 2;
   */
  input?: Struct;

  constructor(data?: PartialMessage<ToolCheckSafeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolCheckSafeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolCheckSafeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolCheckSafeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolCheckSafeRequest;

  static equals(a: ToolCheckSafeRequest | PlainMessage<ToolCheckSafeRequest> | undefined, b: ToolCheckSafeRequest | PlainMessage<ToolCheckSafeRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolCheckSafeResponse
 */
export declare class ToolCheckSafeResponse extends Message<ToolCheckSafeResponse> {
  /**
   * @generated from field: bool isSafe = 1;
   */
  isSafe: boolean;

  constructor(data?: PartialMessage<ToolCheckSafeResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolCheckSafeResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolCheckSafeResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolCheckSafeResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolCheckSafeResponse;

  static equals(a: ToolCheckSafeResponse | PlainMessage<ToolCheckSafeResponse> | undefined, b: ToolCheckSafeResponse | PlainMessage<ToolCheckSafeResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolCancelRunRequest
 */
export declare class ToolCancelRunRequest extends Message<ToolCancelRunRequest> {
  /**
   * @generated from field: string requestId = 1;
   */
  requestId: string;

  /**
   * @generated from field: string toolUseId = 2;
   */
  toolUseId: string;

  constructor(data?: PartialMessage<ToolCancelRunRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolCancelRunRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolCancelRunRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolCancelRunRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolCancelRunRequest;

  static equals(a: ToolCancelRunRequest | PlainMessage<ToolCancelRunRequest> | undefined, b: ToolCancelRunRequest | PlainMessage<ToolCancelRunRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.RetrieveRemoteToolsRequest
 */
export declare class RetrieveRemoteToolsRequest extends Message<RetrieveRemoteToolsRequest> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.tools.RemoteToolId supportedTools = 1;
   */
  supportedTools: RemoteToolId[];

  constructor(data?: PartialMessage<RetrieveRemoteToolsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.RetrieveRemoteToolsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetrieveRemoteToolsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetrieveRemoteToolsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetrieveRemoteToolsRequest;

  static equals(a: RetrieveRemoteToolsRequest | PlainMessage<RetrieveRemoteToolsRequest> | undefined, b: RetrieveRemoteToolsRequest | PlainMessage<RetrieveRemoteToolsRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.RetrieveRemoteToolsResponse
 */
export declare class RetrieveRemoteToolsResponse extends Message<RetrieveRemoteToolsResponse> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.tools.RemoteToolDefinition tools = 1;
   */
  tools: RemoteToolDefinition[];

  constructor(data?: PartialMessage<RetrieveRemoteToolsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.RetrieveRemoteToolsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetrieveRemoteToolsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetrieveRemoteToolsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetrieveRemoteToolsResponse;

  static equals(a: RetrieveRemoteToolsResponse | PlainMessage<RetrieveRemoteToolsResponse> | undefined, b: RetrieveRemoteToolsResponse | PlainMessage<RetrieveRemoteToolsResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.RemoteToolDefinition
 */
export declare class RemoteToolDefinition extends Message<RemoteToolDefinition> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ToolDefinition toolDefinition = 1;
   */
  toolDefinition?: ToolDefinition;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.RemoteToolId remoteToolId = 2;
   */
  remoteToolId: RemoteToolId;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ToolAvailabilityStatus availabilityStatus = 3;
   */
  availabilityStatus: ToolAvailabilityStatus;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ToolSafety toolSafety = 4;
   */
  toolSafety: ToolSafety;

  /**
   * @generated from field: string oauthUrl = 5;
   */
  oauthUrl: string;

  constructor(data?: PartialMessage<RemoteToolDefinition>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.RemoteToolDefinition";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteToolDefinition;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteToolDefinition;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteToolDefinition;

  static equals(a: RemoteToolDefinition | PlainMessage<RemoteToolDefinition> | undefined, b: RemoteToolDefinition | PlainMessage<RemoteToolDefinition> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.CallRemoteToolRequest
 */
export declare class CallRemoteToolRequest extends Message<CallRemoteToolRequest> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string input = 2;
   */
  input: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.RemoteToolId toolId = 3;
   */
  toolId: RemoteToolId;

  /**
   * @generated from field: string request_id = 4;
   */
  requestId: string;

  constructor(data?: PartialMessage<CallRemoteToolRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.CallRemoteToolRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CallRemoteToolRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CallRemoteToolRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CallRemoteToolRequest;

  static equals(a: CallRemoteToolRequest | PlainMessage<CallRemoteToolRequest> | undefined, b: CallRemoteToolRequest | PlainMessage<CallRemoteToolRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.CallRemoteToolResponse
 */
export declare class CallRemoteToolResponse extends Message<CallRemoteToolResponse> {
  /**
   * @generated from field: string tool_output = 1;
   */
  toolOutput: string;

  /**
   * @generated from field: string tool_result_message = 2;
   */
  toolResultMessage: string;

  /**
   * @generated from field: int32 status = 3;
   */
  status: number;

  constructor(data?: PartialMessage<CallRemoteToolResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.CallRemoteToolResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CallRemoteToolResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CallRemoteToolResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CallRemoteToolResponse;

  static equals(a: CallRemoteToolResponse | PlainMessage<CallRemoteToolResponse> | undefined, b: CallRemoteToolResponse | PlainMessage<CallRemoteToolResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ChangeChatModeRequest
 */
export declare class ChangeChatModeRequest extends Message<ChangeChatModeRequest> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ChatMode mode = 1;
   */
  mode: ChatMode;

  constructor(data?: PartialMessage<ChangeChatModeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ChangeChatModeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChangeChatModeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChangeChatModeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChangeChatModeRequest;

  static equals(a: ChangeChatModeRequest | PlainMessage<ChangeChatModeRequest> | undefined, b: ChangeChatModeRequest | PlainMessage<ChangeChatModeRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.FilterToolsWithExtraInputRequest
 */
export declare class FilterToolsWithExtraInputRequest extends Message<FilterToolsWithExtraInputRequest> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.tools.RemoteToolId tool_ids = 1;
   */
  toolIds: RemoteToolId[];

  constructor(data?: PartialMessage<FilterToolsWithExtraInputRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.FilterToolsWithExtraInputRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FilterToolsWithExtraInputRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FilterToolsWithExtraInputRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FilterToolsWithExtraInputRequest;

  static equals(a: FilterToolsWithExtraInputRequest | PlainMessage<FilterToolsWithExtraInputRequest> | undefined, b: FilterToolsWithExtraInputRequest | PlainMessage<FilterToolsWithExtraInputRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.FilterToolsWithExtraInputResponse
 */
export declare class FilterToolsWithExtraInputResponse extends Message<FilterToolsWithExtraInputResponse> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.tools.RemoteToolId tool_ids = 1;
   */
  toolIds: RemoteToolId[];

  constructor(data?: PartialMessage<FilterToolsWithExtraInputResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.FilterToolsWithExtraInputResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FilterToolsWithExtraInputResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FilterToolsWithExtraInputResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FilterToolsWithExtraInputResponse;

  static equals(a: FilterToolsWithExtraInputResponse | PlainMessage<FilterToolsWithExtraInputResponse> | undefined, b: FilterToolsWithExtraInputResponse | PlainMessage<FilterToolsWithExtraInputResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.GetToolStatusForSettingsPanelRequest
 */
export declare class GetToolStatusForSettingsPanelRequest extends Message<GetToolStatusForSettingsPanelRequest> {
  /**
   * @generated from field: bool use_cache = 1;
   */
  useCache: boolean;

  constructor(data?: PartialMessage<GetToolStatusForSettingsPanelRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.GetToolStatusForSettingsPanelRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetToolStatusForSettingsPanelRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetToolStatusForSettingsPanelRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetToolStatusForSettingsPanelRequest;

  static equals(a: GetToolStatusForSettingsPanelRequest | PlainMessage<GetToolStatusForSettingsPanelRequest> | undefined, b: GetToolStatusForSettingsPanelRequest | PlainMessage<GetToolStatusForSettingsPanelRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.GetToolStatusForSettingsPanelResponse
 */
export declare class GetToolStatusForSettingsPanelResponse extends Message<GetToolStatusForSettingsPanelResponse> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings tools = 1;
   */
  tools: ToolDefinitionWithSettings[];

  constructor(data?: PartialMessage<GetToolStatusForSettingsPanelResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.GetToolStatusForSettingsPanelResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetToolStatusForSettingsPanelResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetToolStatusForSettingsPanelResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetToolStatusForSettingsPanelResponse;

  static equals(a: GetToolStatusForSettingsPanelResponse | PlainMessage<GetToolStatusForSettingsPanelResponse> | undefined, b: GetToolStatusForSettingsPanelResponse | PlainMessage<GetToolStatusForSettingsPanelResponse> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolIdentifier
 */
export declare class ToolIdentifier extends Message<ToolIdentifier> {
  /**
   * @generated from field: string host_name = 1;
   */
  hostName: string;

  /**
   * @generated from field: string tool_id = 2;
   */
  toolId: string;

  constructor(data?: PartialMessage<ToolIdentifier>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolIdentifier";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolIdentifier;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolIdentifier;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolIdentifier;

  static equals(a: ToolIdentifier | PlainMessage<ToolIdentifier> | undefined, b: ToolIdentifier | PlainMessage<ToolIdentifier> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings
 */
export declare class ToolDefinitionWithSettings extends Message<ToolDefinitionWithSettings> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ToolDefinition definition = 1;
   */
  definition?: ToolDefinition;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ToolIdentifier identifier = 2;
   */
  identifier?: ToolIdentifier;

  /**
   * @generated from field: bool is_configured = 3;
   */
  isConfigured: boolean;

  /**
   * @generated from field: bool enabled = 4;
   */
  enabled: boolean;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.tools.ToolSafety tool_safety = 5;
   */
  toolSafety: ToolSafety;

  /**
   * @generated from field: optional string oauth_url = 6;
   */
  oauthUrl?: string;

  constructor(data?: PartialMessage<ToolDefinitionWithSettings>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.ToolDefinitionWithSettings";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolDefinitionWithSettings;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolDefinitionWithSettings;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolDefinitionWithSettings;

  static equals(a: ToolDefinitionWithSettings | PlainMessage<ToolDefinitionWithSettings> | undefined, b: ToolDefinitionWithSettings | PlainMessage<ToolDefinitionWithSettings> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.McpServerConfig
 */
export declare class McpServerConfig extends Message<McpServerConfig> {
  /**
   * @generated from field: string command = 1;
   */
  command: string;

  /**
   * @generated from field: repeated string args = 2;
   */
  args: string[];

  /**
   * @generated from field: optional int32 timeout_ms = 3;
   */
  timeoutMs?: number;

  /**
   * @generated from field: map<string, string> env = 4;
   */
  env: { [key: string]: string };

  /**
   * @generated from field: optional bool use_shell_interpolation = 5;
   */
  useShellInterpolation?: boolean;

  /**
   * @generated from field: optional string name = 6;
   */
  name?: string;

  constructor(data?: PartialMessage<McpServerConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.McpServerConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): McpServerConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): McpServerConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): McpServerConfig;

  static equals(a: McpServerConfig | PlainMessage<McpServerConfig> | undefined, b: McpServerConfig | PlainMessage<McpServerConfig> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.tools.SetMcpServersRequest
 */
export declare class SetMcpServersRequest extends Message<SetMcpServersRequest> {
  /**
   * @generated from field: repeated com.augmentcode.sidecar.rpc.tools.McpServerConfig mcp_servers = 1;
   */
  mcpServers: McpServerConfig[];

  constructor(data?: PartialMessage<SetMcpServersRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.tools.SetMcpServersRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetMcpServersRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetMcpServersRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetMcpServersRequest;

  static equals(a: SetMcpServersRequest | PlainMessage<SetMcpServersRequest> | undefined, b: SetMcpServersRequest | PlainMessage<SetMcpServersRequest> | undefined): boolean;
}


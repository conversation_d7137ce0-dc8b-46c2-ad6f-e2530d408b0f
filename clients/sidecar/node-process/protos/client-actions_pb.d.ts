// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file clients/sidecar/node-process/protos/client-actions.proto (package com.augmentcode.sidecar.rpc.clientInterfaces, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { QualifiedPathName } from "./client-workspaces_pb.js";

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.ShowDiffViewRequest
 */
export declare class ShowDiffViewRequest extends Message<ShowDiffViewRequest> {
  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.QualifiedPathName path = 1;
   */
  path?: QualifiedPathName;

  /**
   * @generated from field: string original = 2;
   */
  original: string;

  /**
   * @generated from field: string modified = 3;
   */
  modified: string;

  /**
   * @generated from field: com.augmentcode.sidecar.rpc.clientInterfaces.ShowDiffViewOptions opts = 4;
   */
  opts?: ShowDiffViewOptions;

  constructor(data?: PartialMessage<ShowDiffViewRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.ShowDiffViewRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ShowDiffViewRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ShowDiffViewRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ShowDiffViewRequest;

  static equals(a: ShowDiffViewRequest | PlainMessage<ShowDiffViewRequest> | undefined, b: ShowDiffViewRequest | PlainMessage<ShowDiffViewRequest> | undefined): boolean;
}

/**
 * @generated from message com.augmentcode.sidecar.rpc.clientInterfaces.ShowDiffViewOptions
 */
export declare class ShowDiffViewOptions extends Message<ShowDiffViewOptions> {
  /**
   * @generated from field: optional bool retainFocus = 1;
   */
  retainFocus?: boolean;

  constructor(data?: PartialMessage<ShowDiffViewOptions>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "com.augmentcode.sidecar.rpc.clientInterfaces.ShowDiffViewOptions";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ShowDiffViewOptions;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ShowDiffViewOptions;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ShowDiffViewOptions;

  static equals(a: ShowDiffViewOptions | PlainMessage<ShowDiffViewOptions> | undefined, b: ShowDiffViewOptions | PlainMessage<ShowDiffViewOptions> | undefined): boolean;
}


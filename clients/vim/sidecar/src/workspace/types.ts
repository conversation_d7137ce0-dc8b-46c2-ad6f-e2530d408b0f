import { FileType } from "../utils/types";

// import { type FileDetails } from "../webview-providers/webview-messages";

export interface IQualifiedPathName {
    rootPath: string;
    relPath: string;
}

export interface IQualifiedPathInfo<T extends IQualifiedPathName = IQualifiedPathName> {
    qualifiedPathName: T;
    fileType: FileType;
    isAccepted: boolean;
}

export function pathNameToAbsPath(path: IQualifiedPathName): string {
    return path.rootPath + "/" + path.relPath;
}

// TODO(rich): add back in?
// export function getFileDetailAbsPath(file: FileDetails): string {
//     return pathNameToAbsPath({
//         rootPath: file.repoRoot,
//         relPath: file.pathName,
//     });
// }

// ISourceFolderInfo is a type that contains information about a source folder. folderRoot is the
// absolute path of the folder root.
export interface ISourceFolderInfo {
    folderRoot: string;
    guidelinesEnabled?: boolean;
    guidelinesOverLimit?: boolean;
}

export enum UploadStatus {
    unknown,
    disabled,
    inProgress,
    complete,
}

export type SyncingStatusEvent = {
    status: SyncingStatus;
    foldersProgress: Array<SourceFolderSyncingProgress>;
};

export enum SyncingStatus {
    longRunning = "longRunning",
    running = "running",
    done = "done",
}

export type SourceFolderSyncingProgress = {
    folderRoot: string;
    progress: SyncingProgress | undefined;
};

export type SyncingProgress = {
    newlyTracked: boolean;
    trackedFiles: number;
    backlogSize: number;
};

const uploadStatusStrings = new Map<UploadStatus, string>([
    [UploadStatus.unknown, "unknown"],
    [UploadStatus.disabled, "disabled"],
    [UploadStatus.inProgress, "in progress"],
    [UploadStatus.complete, "complete"],
]);

export function uploadStatusToString(status: UploadStatus): string {
    return uploadStatusStrings.get(status)!;
}

// SyncingEnabledState is an enumeration of the possible states of workspace syncing.
// * initializing: The state of syncing is not yet known because the extension is still starting.
// * enabled: Syncing is enabled for all tracked workspace folders.
// * disabled: Syncing is disabled for all workspace folders.
// * partial: Syncing is enabled for some tracked workspace folders, but not all. (For example,
//   the user might have added a new workspace folder but not yet given permission to sync it.)
export enum SyncingEnabledState {
    initializing = "initializing",
    enabled = "enabled",
    disabled = "disabled",
    partial = "partial",
}

export enum PathType {
    inaccessible = "inaccessible",
    notAFile = "not a file",
    largeFile = "large file",
    accepted = "accepted",
}

export type PathInfo =
    | {
          type: PathType.inaccessible;
      }
    | {
          type: PathType.notAFile;
          mtime: number;
      }
    | {
          type: PathType.accepted | PathType.largeFile;
          mtime: number;
          size: number;
      };

export enum ContentType {
    inaccessible = "inaccessible",
    largeFile = "large file",
    text = "text",
    binary = "binary",
}

export type FileContents =
    | {
          type: ContentType.inaccessible | ContentType.binary;
      }
    | {
          type: ContentType.largeFile;
          size: number;
      }
    | {
          type: ContentType.text;
          contents: Uint8Array;
      };

export interface PathHandler {
    readonly maxBlobSize: number;
    classifyPath(absPath: string): PathInfo;
    readText(absPath: string): Promise<FileContents>;
    calculateBlobName(relPath: string, contents: Uint8Array): string | undefined;
}

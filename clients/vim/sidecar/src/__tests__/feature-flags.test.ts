/**
 * Unit tests for FeatureFlagManager class.
 */
import {
    defaultFeatureFlags,
    FeatureFlagChangeEvent,
    FeatureFlagManager,
    FeatureFlags,
    IFeatureFlagManagerOptions,
} from "../feature-flags";

// import { SmartPastePrecomputeMode } from "../webview-providers/webview-messages";

describe("feature-flags", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    // we can create a feature flag manager and it has the right default flags
    test("initial-flags", async () => {
        const manager = new FeatureFlagManager();
        expect(manager.currentFlags).toStrictEqual(defaultFeatureFlags);
    });

    // Check that update correctly change flags to new ones
    test("change-flag", async () => {
        const manager = new FeatureFlagManager();
        const newFlags: FeatureFlags = {
            gitDiff: false,
            gitDiffPollingFrequencyMSec: 623,
            additionalChatModels: "",
            smallSyncThreshold: 1,
            bigSyncThreshold: 2,
            enableWorkspaceManagerUi: false,
            enableInstructions: false,
            enableSmartPaste: false,
            enableSmartPasteMinVersion: "",
            enableViewTextDocument: false,
            bypassLanguageFilter: false,
            enableHindsight: false,
            maxUploadSizeBytes: 100,
            vscodeNextEditMinVersion: "0.0.0",
            vscodeNextEditUx1MaxVersion: "0.0.0",
            vscodeNextEditUx2MaxVersion: "0.0.0",
            vscodeFlywheelMinVersion: "0.0.0",
            vscodeExternalSourcesInChatMinVersion: "0.0.0",
            vscodeShareMinVersion: "",
            maxTrackableFileCount: 200,
            maxTrackableFileCountWithoutPermission: 100,
            minUploadedPercentageWithoutPermission: 90,
            vscodeSourcesMinVersion: "",
            vscodeChatHintDecorationMinVersion: "",
            nextEditDebounceMs: 500,
            enableCompletionFileEditEvents: false,
            vscodeEnableCpuProfile: false,
            verifyFolderIsSourceRepo: false,
            refuseToSyncHomeDirectories: false,
            enableFileLimitsForSyncingPermission: false,
            enableChatMermaidDiagrams: false,
            enableSummaryTitles: false,
            // smartPastePrecomputeMode: SmartPastePrecomputeMode.visibleHover,
            vscodeNewThreadsMenuMinVersion: "",
            vscodeEditableHistoryMinVersion: "",
            vscodeEnableChatMermaidDiagramsMinVersion: "",
            userGuidelinesLengthLimit: 2000,
            workspaceGuidelinesLengthLimit: 2000,
            enableGuidelines: false,
            useCheckpointManagerContextMinVersion: "",
            validateCheckpointManagerContext: false,
            vscodeDesignSystemRichTextEditorMinVersion: "",
            vscodeChatWithToolsMinVersion: "",
            vscodeAgentModeMinVersion: "",
            vscodeAgentModeMinStableVersion: "",
        };
        expect(manager.currentFlags).not.toEqual(newFlags);
        manager.update(newFlags);
        expect(manager.currentFlags).toStrictEqual(newFlags);
    });

    // we can subscribe to change events
    test("subscribe", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest
            .fn()
            .mockImplementation(
                ({ previousFlags, newFlags, changedFlags }: FeatureFlagChangeEvent) => {
                    expect(previousFlags).toStrictEqual(defaultFeatureFlags);
                    expect(newFlags).not.toStrictEqual(defaultFeatureFlags);
                    expect(changedFlags).toStrictEqual(["maxUploadSizeBytes"]);
                    expect(manager.currentFlags).toStrictEqual(newFlags);
                }
            );
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const flags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags);
        expect(callback).toHaveBeenCalled();
    });

    // subscription can be disposed
    test("dispose-subscription", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        subscription.dispose();
        const flags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags);
        expect(callback).not.toHaveBeenCalled();
    });

    // when subscribing to multiple flags, individual changes to each
    // flag should all trigger the callback
    test("multiple-flags", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes", "gitDiff"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const flags1 = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags1);
        expect(callback).toHaveBeenCalled();
        const flags2 = {
            ...flags1,
            gitDiff: !manager.currentFlags.gitDiff,
        };
        manager.update(flags2);
        expect(callback).toHaveBeenCalledTimes(2);
    });

    // Multiple subscriptions on the same flag should work for each callbacks.
    test("multiple-subscriptions", async () => {
        const manager = new FeatureFlagManager();
        const callback1 = jest.fn();
        const callback2 = jest.fn();
        const subscription1 = manager.subscribe(["maxUploadSizeBytes"], callback1);
        const subscription2 = manager.subscribe(["maxUploadSizeBytes"], callback2);
        expect(subscription1).not.toBeUndefined();
        expect(subscription2).not.toBeUndefined();
        expect(callback1).not.toHaveBeenCalled();
        expect(callback2).not.toHaveBeenCalled();
        const flags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags);
        expect(callback1).toHaveBeenCalled();
        expect(callback2).toHaveBeenCalled();
    });

    // updating a flag to the same value should not trigger the callback
    // even when other flags are changed.
    test("no-change", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        manager.update({
            ...manager.currentFlags,
            gitDiff: !manager.currentFlags.gitDiff,
        });
        expect(callback).not.toHaveBeenCalled();
    });

    // the list of changed flags received in the callback should be correct
    test("changed-flags", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes", "gitDiff"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const oldFlags = manager.currentFlags;
        const flags = {
            ...oldFlags,
            maxUploadSizeBytes: oldFlags.maxUploadSizeBytes + 7,
            gitDiff: !oldFlags.gitDiff,
        };
        manager.update(flags);
        expect(callback).toHaveBeenCalledWith({
            previousFlags: oldFlags,
            newFlags: flags,
            changedFlags: ["maxUploadSizeBytes", "gitDiff"],
        });
    });

    // Disposing the feature flag manager disables all subscriptions
    test("dispose-manager", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const newFlags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.dispose();
        // accessing flags should throw
        expect(() => manager.currentFlags).toThrow();
        // subscription should be disposed
        expect(subscription.disposed).toBeTruthy();
        // calling update should throw
        expect(() => manager.update(newFlags)).toThrow();
        // calling subscribe should throw
        expect(() => manager.subscribe(["gitDiff"], callback)).toThrow();
        // disposing manager again should not throw
        expect(() => manager.dispose()).not.toThrow();
    });

    // Test automatic loading of feature flags
    test("automatic-loading", async () => {
        jest.useFakeTimers();

        const mockFetcher = jest.fn().mockImplementation(async () => ({
            ...defaultFeatureFlags,
            gitDiff: true,
            maxUploadSizeBytes: 256 * 1024,
        }));

        const options: IFeatureFlagManagerOptions = {
            refreshIntervalMSec: 5000,
            fetcher: mockFetcher,
        };

        const manager = new FeatureFlagManager(options);

        // Initial state should be default flags
        expect(manager.currentFlags).toStrictEqual(defaultFeatureFlags);

        // Advance timer to trigger first fetch
        jest.advanceTimersByTime(5000);
        await Promise.resolve(); // Allow pending promises to resolve

        // Check if flags were updated
        expect(manager.currentFlags.gitDiff).toBe(true);
        expect(manager.currentFlags.maxUploadSizeBytes).toBe(256 * 1024);

        // Fetcher should have been called once
        expect(mockFetcher).toHaveBeenCalledTimes(1);

        // Advance timer again to trigger second fetch
        await jest.advanceTimersByTimeAsync(6000);
        expect(mockFetcher).toHaveBeenCalledTimes(2);

        // Advance timer again to trigger third fetch
        await jest.advanceTimersByTimeAsync(6000);
        expect(mockFetcher).toHaveBeenCalledTimes(3);

        // Clean up
        manager.dispose();
        jest.useRealTimers();
    });
});

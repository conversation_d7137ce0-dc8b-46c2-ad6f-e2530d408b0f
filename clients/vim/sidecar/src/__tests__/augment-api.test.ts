/* eslint-disable @typescript-eslint/no-base-to-string */

/* eslint-disable @typescript-eslint/naming-convention */
import { afterEach, expect, test } from "@jest/globals";
import fetchMock from "fetch-mock";

import { mockGetModelsResult } from "../__mocks__/mock-modelinfo";
import { ExtensionContext } from "../__mocks__/vscode-mocks";
import { APIServer, APIServerImpl, BackModelInfo, Model } from "../augment-api";
import { AugmentConfigListener } from "../augment-config-listener";
import { AuthSessionStore } from "../auth/auth-session-store";

const fetchSandbox = fetchMock.sandbox();

class AugmentAPItestKit {
    readonly configListener: AugmentConfigListener;
    readonly auth: AuthSessionStore;

    constructor() {
        this.configListener = new AugmentConfigListener();
        this.auth = new AuthSessionStore(new ExtensionContext(), this.configListener);
        // Note(rich): I could not figure out how the vscode side is mocking the
        // session, so I'm explicitly setting it here.
        this.auth.saveSession("test-token", "http://api.augmentcode.com");
    }

    public createAPIServer(): APIServer {
        const apiServer = new APIServerImpl(
            this.configListener,
            this.auth,
            "placeholder-session-id",
            "augment-extension-test/0.0.0",
            fetchSandbox
        );
        return apiServer;
    }

    // testCompletionAPIError verifies the behavior of a malformed response to a
    // "complete" request. `result` is the (malformed) result.
    public async testCompletionAPIError(result: any) {
        const server = this.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", result);

        const requestId = server.createRequestId();
        await expect(async () =>
            server.complete(
                requestId,
                "def quicksort(",
                "",
                "quicksort.py",
                "quickSortBlobName",
                { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
                "python",
                { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
                []
            )
        ).rejects.toThrowError();

        fetchSandbox.reset();
    }

    // "get-models" request. `result` is the (malformed) result.
    public async testGetModelsAPIError(result: any) {
        const server = this.createAPIServer();
        fetchSandbox.post("http://api.augmentcode.com/get-models", result);

        await expect(async () => server.getModelConfig()).rejects.toThrowError();

        fetchSandbox.reset();
    }

    // verifyModelInfo veries that the `received` ModelInfo matches the `expected`
    // BackModelInfo.
    public verifyModelInfo(received: Model, expected: BackModelInfo) {
        expect(received.suggestedPrefixCharCount).toBe(expected.suggested_prefix_char_count);
        expect(received.suggestedSuffixCharCount).toBe(expected.suggested_suffix_char_count);
    }
}

describe("augment-api", () => {
    beforeEach(() => {
        // Prevent open handle leaks
        jest.useFakeTimers();
        // resetMockWorkspace();
    });

    // reset the sandbox after each test.
    afterEach(() => {
        jest.useRealTimers();
        fetchSandbox.reset();
        // resetMockWorkspace();
    });

    test("completion-single", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const completionText = "hello";
        const unknownBlobNames = ["a", "b", "c"];
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", {
            completions: [completionText],
            unknown_blob_names: unknownBlobNames,
        });

        const requestId = server.createRequestId();
        const result = await server.complete(
            requestId,
            "def quicksort(",
            "",
            "quicksort.py",
            "quickSortBlobName",
            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
            "python",
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
            []
        );
        expect(result.completionItems.length).toBe(1);
        expect(result.completionItems[0].text).toBe(completionText);
        expect(result.unknownBlobNames.length).toBe(3);
        for (let idx = 0; idx < unknownBlobNames.length; idx++) {
            expect(result.unknownBlobNames[idx]).toBe(unknownBlobNames[idx]);
        }

        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("prompt", "def quicksort(");
        expect(body).toHaveProperty("suffix", "");
        expect(body).toHaveProperty("path", "quicksort.py");
    });

    test("completion-multiple", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const completions = ["hello", "goodbye", "later, dude"];
        const unknownBlobNames = ["a", "b", "c"];
        fetchSandbox.postOnce("http://api.augmentcode.com/completion", {
            completions,
            unknown_blob_names: unknownBlobNames,
        });

        const requestId = server.createRequestId();
        const result = await server.complete(
            requestId,
            "def quicksort(",
            "",
            "quicksort.py",
            "quickSortBlobName",
            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
            "python",
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
            []
        );
        expect(result.completionItems.length).toBe(completions.length);
        for (let idx = 0; idx < completions.length; idx++) {
            expect(result.completionItems[idx].text).toBe(completions[idx]);
        }
        expect(result.unknownBlobNames.length).toBe(3);
        for (let idx = 0; idx < unknownBlobNames.length; idx++) {
            expect(result.unknownBlobNames[idx]).toBe(unknownBlobNames[idx]);
        }
    });

    test("completion-api-error", async () => {
        const kit = new AugmentAPItestKit();
        await kit.testCompletionAPIError({ completions: ["hello"] });
        await kit.testCompletionAPIError({
            completions: "should be array, not scalar",
            unknown_blob_names: [],
        });
        await kit.testCompletionAPIError({ completions: ["hello"], unknownBlobNames: [1, 2, 3] });
        await kit.testCompletionAPIError({ completions: ["hello"], some_other_field: [] });
    });

    test("get-models", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.post("http://api.augmentcode.com/get-models", mockGetModelsResult);

        const modelConfig = await server.getModelConfig();
        expect(mockGetModelsResult.models.map((m) => m.name).sort()).toStrictEqual(
            modelConfig.models.map((m) => m.name).sort()
        );

        const call = fetchSandbox.lastCall()!;
        expect(call[1]?.headers).toHaveProperty("x-request-id");
    });

    test("get-models-backwards-compat", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        const mockResult = {
            default_model: "abc",
            models: [
                {
                    name: "abc",
                    suggested_prefix_char_count: 123,
                    suggested_suffix_char_count: 456,
                },
            ],
            languages: [],
        };
        fetchSandbox.post("http://api.augmentcode.com/get-models", mockResult);

        const modelConfig = await server.getModelConfig();
        expect(modelConfig.models.map((m) => m.name)).toStrictEqual(
            mockResult.models.map((m) => m.name)
        );

        for (const mockModelInfo of mockResult.models) {
            const result = modelConfig.models.find((m) => m.name === mockModelInfo.name);
            expect(result).toBeDefined();
            kit.verifyModelInfo(result!, mockModelInfo);
        }

        const call = fetchSandbox.lastCall()!;
        expect(call[1]?.headers).toHaveProperty("x-request-id");
    });

    test("get-models-api-error", async () => {
        const kit = new AugmentAPItestKit();

        let badResult = {
            default_model: "abc",
            models: [
                {
                    name: "abc",
                    suggested_prefix_char_count: "should be number, not string",
                    suggested_suffix_char_count: 456,
                },
            ],
            languages: [],
        };
        await kit.testGetModelsAPIError(badResult);
    });

    test("response-not-json", async () => {
        const kit = new AugmentAPItestKit();
        await kit.testCompletionAPIError("this is a not a json document");
    });

    test.skip("report-error called on completion error", async () => {
        const kit = new AugmentAPItestKit();
        const server = kit.createAPIServer();
        fetchSandbox.postOnce("http://api.augmentcode.com/report-error", {});
        fetchSandbox.postOnce(
            "http://api.augmentcode.com/completion",
            Promise.reject(Error("my completion error"))
        );

        const requestId = server.createRequestId();
        await expect(
            server.complete(
                requestId,
                "",
                "",
                "",
                "",
                { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },
                "",
                { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] },
                []
            )
        ).rejects.toThrowError();
        const call = fetchSandbox.lastCall()!;
        expect(call[1]!.headers).toHaveProperty("x-request-id");
        const body = JSON.parse(call[1]!.body!.toString());
        expect(body).toHaveProperty("original_request_id", requestId);
        expect(body).toHaveProperty(
            "sanitized_message",
            "completion call failed with APIStatus unavailable"
        );
        expect(body["diagnostics"]).toContainEqual({
            key: "message",
            value: "my completion error",
        });
    });
});

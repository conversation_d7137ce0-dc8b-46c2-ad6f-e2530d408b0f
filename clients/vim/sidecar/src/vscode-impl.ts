/* Implements interfaces from VSCode */
import { EventEmitter as NodeEventEmitter } from "events";
import * as fs from "fs";
import { getLogger } from "loglevel";
import os from "os";
import * as path from "path";
import {
    Position as LspPosition,
    TextDocumentChangeEvent as LspTextDocumentChangeEvent,
    WorkspaceFolder as LspWorkspaceFolder,
} from "vscode-languageserver";
import { TextDocument as LspTextDocument } from "vscode-languageserver-textdocument";
import { URI as Uri, Utils } from "vscode-uri";

import { getErrmsg } from "./exceptions";
import { LspDocumentTracker } from "./lsp-document-tracker";
import { fileExists, readFileUtf8Sync } from "./utils/fs-utils";

export { readFileUtf8Sync as readFileUtf8 };

export class CancellationError extends Error {
    /**
     * Creates a new cancellation error.
     */
    constructor() {
        super("Operation cancelled");
    }
}

type TextDocumentContentChangeEvent = {
    range: Range;
    rangeLength: number;
    rangeOffset: number;
    text: string;
};

type WorkspaceFolderChangeEvent = {
    added: readonly WorkspaceFolder[];
    removed: readonly WorkspaceFolder[];
};

interface TextDocumentChangeEvent extends LspTextDocumentChangeEvent<TextDocument> {
    contentChanges: readonly TextDocumentContentChangeEvent[];
}

export { Uri, Utils, TextDocumentChangeEvent };

/* eslint-disable @typescript-eslint/naming-convention */
export enum EndOfLine {
    LF = 1,
    CRLF = 2,
}
/* eslint-enable @typescript-eslint/naming-convention */

export class Position {
    private _position: LspPosition;

    constructor(line: number, character: number) {
        this._position = LspPosition.create(line, character);
    }
    get line() {
        return this._position.line;
    }
    get character() {
        return this._position.character;
    }

    translate(lineDelta?: number, characterDelta?: number): Position {
        lineDelta = lineDelta ?? 0;
        characterDelta = characterDelta ?? 0;
        return new Position(this.line + lineDelta, this.character + characterDelta);
    }
}

export class Range {
    readonly start: Position;
    readonly end: Position;

    constructor(startLine: number, startCharacter: number, endLine: number, endCharacter: number);
    constructor(start: Position, end: Position);

    constructor(
        startOrPosition: number | Position,
        startCharacterOrEnd: number | Position,
        endLine?: number,
        endCharacter?: number
    ) {
        if (
            typeof startOrPosition === "number" &&
            typeof startCharacterOrEnd === "number" &&
            typeof endLine === "number" &&
            typeof endCharacter === "number"
        ) {
            this.start = new Position(startOrPosition, startCharacterOrEnd);
            this.end = new Position(endLine, endCharacter);
        } else if (startOrPosition instanceof Position && startCharacterOrEnd instanceof Position) {
            this.start = startOrPosition;
            this.end = startCharacterOrEnd;
        } else {
            throw new Error("Invalid arguments for Range constructor");
        }
    }
}

export interface TextDocument {
    readonly uri: Uri;
    readonly languageId: string;
    readonly version: number;
    positionAt(offset: number): Position;
    offsetAt(position: Position): number;
    getText(range?: { start: Position; end: Position }): string;
}

export class LspTextDocumentWrapper implements TextDocument {
    /**
     * Translate from the LSP TextDocument to the vscode TextDocument.
     * One important difference is the uri property, which is a string in LSP.
     **/
    constructor(private readonly document: LspTextDocument) {}

    get uri(): Uri {
        return Uri.parse(this.document.uri);
    }
    get languageId(): string {
        return this.document.languageId;
    }
    get version(): number {
        return this.document.version;
    }

    positionAt(offset: number): Position {
        const lspPosition = this.document.positionAt(offset);
        return new Position(lspPosition.line, lspPosition.character);
    }
    offsetAt(position: Position): number {
        return this.document.offsetAt(position);
    }
    getText(range?: { start: Position; end: Position }): string {
        return this.document.getText(range);
    }
}

export class Disposable {
    public dispose: () => any;

    constructor(callOnDispose: () => any) {
        this.dispose = (): any => callOnDispose();
    }
}

export interface Event<T> {
    /**
     * A function that represents an event to which you subscribe by calling it with
     * a listener function as argument.
     *
     * @param listener The listener function will be called when the event happens.
     * @param thisArgs The `this`-argument which will be used when calling the event listener.
     * @param disposables An array to which a {@link Disposable} will be added.
     * @returns A disposable which unsubscribes the event listener.
     */
    (listener: (e: T) => any, thisArgs?: any, disposables?: Disposable[]): Disposable;
}

/**
 * An event emitter can be used to create and manage an {@link Event} for others
 * to subscribe to. One emitter always owns one event.
 *
 * Use this class if you want to provide event from within your extension, for instance
 * inside a {@link TextDocumentContentProvider} or when providing
 * API to other extensions.
 */
export class EventEmitter<T> {
    private nodeEventEmitter: NodeEventEmitter;

    constructor() {
        this.nodeEventEmitter = new NodeEventEmitter();

        this.event = (
            listener: (e: T) => any,
            thisArgs?: any,
            disposables?: Disposable[]
        ): Disposable => {
            const wrappedListener = thisArgs ? listener.bind(thisArgs) : listener;
            this.nodeEventEmitter.on("event", wrappedListener);

            const disposable = new Disposable(() => {
                this.nodeEventEmitter.off("event", wrappedListener);
            });

            if (Array.isArray(disposables)) {
                disposables.push(disposable);
            }

            return disposable;
        };
    }

    /**
     * The event listeners can subscribe to.
     */
    public event: Event<T>;

    /**
     * Notify all subscribers of the {@link EventEmitter.event event}. Failure
     * of one or more listener will not fail this function call.
     *
     * @param data The event object.
     */
    public fire(data: T): void {
        this.nodeEventEmitter.emit("event", data);
    }

    /**
     * Dispose this object and free resources.
     */
    dispose(): void {
        this.nodeEventEmitter.removeAllListeners("event");
    }
}

export class WorkspaceFolder {
    public readonly uri: Uri;
    public readonly name: string;
    public readonly index: number;

    constructor(uri: Uri, name: string, index: number) {
        this.uri = uri;
        this.name = name;
        this.index = index;
    }
}

export type WorkspaceFoldersChangeEvent = {
    added: readonly WorkspaceFolder[];
    removed: readonly WorkspaceFolder[];
};

export class CancellationTokenSource implements Disposable {
    public token: CancellationToken;

    private tokenEvent: EventEmitter<void>;

    constructor() {
        this.tokenEvent = new EventEmitter<void>();
        this.token = {
            isCancellationRequested: false,
            onCancellationRequested: this.tokenEvent.event,
        };
    }

    public cancel() {
        this.token.isCancellationRequested = true;
        this.tokenEvent.fire();
    }

    public dispose() {
        this.tokenEvent.dispose();
    }
}

export interface WorkspaceConfiguration {
    advanced?: {
        apiToken?: string;
        autofix?: {
            enabled?: boolean;
            locationUrl?: string;
            autofixUrl?: string;
        };
        chat?: {
            enableEditableHistory?: boolean;
            enabled?: boolean;
            experimentalFullFilePaste?: boolean;
            model?: string;
            modelDisplayNameToId?: {
                [key: string]: string | null;
            };
            smartPasteUsePrecomputation?: boolean;
            stream?: boolean;
            url?: string;
            useRichTextHistory?: boolean;
        };
        agent?: {
            model?: string;
        };
        codeInstruction?: {
            model?: string;
        };
        completionURL?: string;
        completions?: {
            timeoutMs?: number;
            maxWaitMs?: number;
            addIntelliSenseSuggestions?: boolean;
            filter_threshold?: number /* eslint-disable-line @typescript-eslint/naming-convention */;
        };
        enableDebugFeatures?: boolean;
        enableDataCollection?: boolean;
        enablePreferenceCollection?: boolean;
        enableReviewerWorkflows?: boolean;
        enableWorkspaceUpload?: boolean;
        instructions?: {
            model?: string;
        };
        model?: string;
        nextEdit?: {
            allowDuringDebugging?: boolean;
            animateNoDiffMode?: boolean;
            backgroundEnabled?: boolean;
            enabled?: boolean;
            noDiffMode?: boolean;
            noDiffModeUseCodeLens?: boolean;
            showDiffByDefault?: boolean;
            url?: string;
            locationUrl?: string;
            generationUrl?: string;
            model?: string;
            showInstructionTextbox?: boolean;
            useDebounceMs?: number;
            useCursorDecorations?: boolean;
            useSmallHover?: boolean;
            useMockResults?: boolean;
        };
        nextEditURL?: string;
        nextEditLocationURL?: string;
        nextEditGenerationURL?: string;
        nextEditBackgroundGeneration?: boolean;
        oauth?: {
            clientID?: string;
            url?: string;
        };
        openFileManager?: {
            useV2?: boolean;
        };
        preferenceCollection?: {
            enable?: boolean;
            enableRetrievalDataCollection?: boolean;
            enableRandomizedMode?: boolean;
        };
        recencySignalManager?: {
            collectGitDiffEnabled?: boolean;
            collectTabSwitchEvents?: boolean;
            gitDiffPollingFrequencyMSec?: number;
        };
        smartPaste?: {
            url?: string;
            model?: string;
        };
        integrations?: {
            atlassian?: {
                serverUrl: string;
                personalApiToken: string;
                username: string;
            };
            notion?: {
                apiToken: string;
            };
            linear?: {
                apiToken: string;
            };
            github?: {
                apiToken: string;
            };
        };
        vcs?: {
            watcherEnabled?: boolean;
        };
    };
    apiToken?: string;
    completionURL?: string;
    completions?: {
        enableAutomaticCompletions?: boolean;
        disableCompletionsByLanguage?: string[];
        enableQuickSuggestions?: boolean;
    };
    emptyFileHint?: {
        enabled?: boolean;
    };
    enableAutomaticCompletions?: boolean;
    enableBackgroundSuggestions?: boolean;
    enableEmptyFileHint?: boolean;
    enableGlobalBackgroundSuggestions?: boolean;
    disableCompletionsByLanguage?: string[];
    enableShortcutsAboveSelectedText?: boolean;
    shortcutsDisplayDelayMS?: number;
    showAllBackgroundSuggestionLineHighlights?: boolean;
    chat?: {
        userGuidelines?: string;
    };
    conflictingCodingAssistantCheck?: boolean;
}

export interface ConfigurationChangeEvent {}
const configurationChanged = new EventEmitter<ConfigurationChangeEvent>();

export type FileWillRenameEvent = {
    files: {
        oldUri: Uri;
        newUri: Uri;
    }[];
};

// Note(rich): consider putting this in a namespace
const onDidOpenTextDocumentEmitter = new EventEmitter<TextDocument>();
const onDidChangeTextDocumentEmitter = new EventEmitter<TextDocumentChangeEvent>();
const onDidCloseTextDocumentEmitter = new EventEmitter<TextDocument>();
const onWillRenameFilesEmitter = new EventEmitter<FileWillRenameEvent>();
const onDidChangeWorkspaceFoldersEmitter = new EventEmitter<WorkspaceFolderChangeEvent>();

export {
    onDidOpenTextDocumentEmitter,
    onDidChangeTextDocumentEmitter,
    onDidCloseTextDocumentEmitter,
};

export interface SecretStorageChangeEvent {
    readonly key: string;
}
const secretStorageChanged = new EventEmitter<SecretStorageChangeEvent>();

export interface FileSystemWatcher extends Disposable {
    readonly onDidCreate: Event<Uri>;
    readonly onDidChange: Event<Uri>;
    readonly onDidDelete: Event<Uri>;
}

let workspaceFolders: readonly WorkspaceFolder[] = [];
export function setWorkspaceFolders(folders: readonly LspWorkspaceFolder[]) {
    workspaceFolders = folders.map((folder: LspWorkspaceFolder, index: number) => ({
        uri: Uri.parse(folder.uri),
        name: folder.name,
        index: index,
    }));
}

function getAugmentConfig(): WorkspaceConfiguration {
    const logger = getLogger("WorkspaceConfig");

    const xdgConfigPath = process.env.TEST_TMPDIR
        ? path.join(process.env.TEST_TMPDIR, "config")
        : process.env.XDG_CONFIG_HOME || path.join(os.homedir(), ".config");
    const augmentConfigPath =
        process.env.AUGMENT_CONFIG_PATH || path.join(xdgConfigPath, "augment", "user_config.json");

    try {
        logger.debug(`Checking for user config at ${augmentConfigPath}`);
        if (!fileExists(augmentConfigPath)) {
            logger.debug(`File doesn't exist: ${augmentConfigPath}`);
            return {};
        }

        const data = readFileUtf8Sync(augmentConfigPath);
        const decoded = JSON.parse(data) as WorkspaceConfiguration;
        logger.info(`Found user config: ${JSON.stringify(decoded)}`);
        return decoded;
    } catch (e) {
        logger.error(`Failed to read augment config at ${augmentConfigPath}: ${getErrmsg(e)}`);
    }

    return {};
}

export const workspace = {
    onDidChangeConfiguration: configurationChanged.event,
    getConfiguration: (key: string): WorkspaceConfiguration => {
        if (key === "augment") {
            return getAugmentConfig();
        }
        return {};
    },
    onDidOpenTextDocument: onDidOpenTextDocumentEmitter.event,
    onDidChangeTextDocument: onDidChangeTextDocumentEmitter.event,
    onDidCloseTextDocument: onDidCloseTextDocumentEmitter.event,
    onWillRenameFiles: onWillRenameFilesEmitter.event,
    onDidChangeWorkspaceFolders: onDidChangeWorkspaceFoldersEmitter.event,

    documents: new LspDocumentTracker(),

    get textDocuments(): TextDocument[] {
        return this.documents.getAllDocuments().map((doc) => new LspTextDocumentWrapper(doc));
    },

    get workspaceFolders(): readonly WorkspaceFolder[] {
        return workspaceFolders;
    },
};

export type CancellationToken = {
    isCancellationRequested: boolean;
    onCancellationRequested: Event<void>;
};

/* A version of ExtensionContext for the sidecar
 * See https://code.visualstudio.com/api/references/vscode-api#ExtensionContext
 */
export interface Memento {
    get<T>(key: string): T | undefined;
    update(key: string, value: any): Thenable<void>;
}

export interface SecretStorage {
    get(key: string): Thenable<string | undefined>;
    store(key: string, value: string): Thenable<void>;
    delete(key: string): Thenable<void>;
    onDidChange: Event<SecretStorageChangeEvent>;
}

class SecretStorageImpl implements SecretStorage {
    private secretsPath: string;

    constructor(configDir: Uri) {
        if (!fs.existsSync(configDir.fsPath)) {
            fs.mkdirSync(configDir.fsPath, { recursive: true });
        }
        this.secretsPath = Utils.joinPath(configDir, "secrets.json").fsPath;

        // Create empty secrets file if it doesn't exist
        if (!fs.existsSync(this.secretsPath)) {
            fs.writeFileSync(this.secretsPath, "{}", { mode: 0o600 });
        }
    }

    private async readSecrets(): Promise<Record<string, string>> {
        try {
            const content = await fs.promises.readFile(this.secretsPath, { encoding: "utf8" });
            return JSON.parse(content) as Record<string, string>;
        } catch {
            // TODO(AU-5862): Let the client handle exceptions.  This means that
            // the expected case of a new file should be handled here, but an
            // unexpected error should be handled by the client.
            return {};
        }
    }

    private async writeSecrets(secrets: Record<string, string>): Promise<void> {
        await fs.promises.writeFile(this.secretsPath, JSON.stringify(secrets, null, 2), {
            mode: 0o600,
            encoding: "utf8",
        });
    }

    async get(key: string): Promise<string | undefined> {
        const secrets = await this.readSecrets();
        return secrets[key];
    }

    async store(key: string, value: string): Promise<void> {
        const secrets = await this.readSecrets();
        secrets[key] = value;
        await this.writeSecrets(secrets);
        secretStorageChanged.fire({ key });
    }

    async delete(key: string): Promise<void> {
        const secrets = await this.readSecrets();
        if (key in secrets) {
            delete secrets[key];
            await this.writeSecrets(secrets);
            secretStorageChanged.fire({ key });
        }
    }
    onDidChange: Event<SecretStorageChangeEvent> = secretStorageChanged.event;
}

class MementoImpl implements Memento {
    private inMemoryState: Map<string, any> = new Map();
    private statePath;

    constructor(stateDir: Uri) {
        fs.mkdirSync(stateDir.fsPath, { recursive: true, mode: 0o755 });
        this.statePath = Utils.joinPath(stateDir, "state.json").fsPath;
        try {
            const data = readFileUtf8Sync(this.statePath);
            const decoded = JSON.parse(data) as Array<[string, any]>;
            this.inMemoryState = new Map(decoded);
        } catch {
            return;
        }
    }

    get<T>(key: string): T | undefined {
        return this.inMemoryState.get(key) as T | undefined;
    }

    async update(key: string, value: any): Promise<void> {
        this.inMemoryState.set(key, value);
        const data = JSON.stringify(Array.from(this.inMemoryState.entries()));
        // only user has write access
        await fs.promises.writeFile(this.statePath, data, { encoding: "utf8", mode: 0o644 });
    }
}

export class ExtensionContext {
    public globalState: Memento;
    public workspaceState: Memento;
    public storagePath: string;
    public storageUri: Uri;
    public globalStoragePath: string;
    public globalStorageUri: Uri;
    public secrets: SecretStorage;

    constructor(storageDir: Uri) {
        // only user has write access
        fs.mkdirSync(storageDir.fsPath, { recursive: true, mode: 0o755 });
        this.storageUri = Utils.joinPath(storageDir, "workspaceStorage");
        this.storagePath = this.storageUri.fsPath;
        this.globalStorageUri = Utils.joinPath(storageDir, "globalStorage");
        this.globalStoragePath = this.globalStorageUri.fsPath;
        this.secrets = new SecretStorageImpl(storageDir);
        this.globalState = new MementoImpl(this.globalStorageUri);
        this.workspaceState = new MementoImpl(this.storageUri);
    }
}

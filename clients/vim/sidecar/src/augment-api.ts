/* eslint-disable @typescript-eslint/naming-convention */
import { v4 as uuidv4 } from "uuid";

import { APITiming } from "./api/api-timing";
import { AugmentConfig, AugmentConfigListener } from "./augment-config-listener";
import { AuthSessionStore } from "./auth/auth-session-store";
import { Exchange } from "./chat/chat-types";
import { APIError, APIStatus, getErrmsg } from "./exceptions";
import { defaultFeatureFlags, FeatureFlags } from "./feature-flags";
import { defaultSupportedLanguages } from "./languages";
import { type AugmentLogger, getLogger } from "./logging";
import { ClientMetric as TClientMetric } from "./metrics/types";
import { toBoolean, toNumber, toString, toStringArray, verifyArray } from "./utils/json-utils";
import { getPropertySizes } from "./utils/object-utils";
import { retryWithBackoff, withTimeout } from "./utils/promise-utils";
import { ChangeType, VCSChange } from "./vcs/watcher/types";
import { ChatResultNode, type WorkspaceFileChunk } from "./webview-providers/webview-messages";

// CompletionLocation is a set of character offsets into a blob that is the target
// of a completion request.
//  - prefixBegin: offset of the beginning of the completion's prefix
//  - cursorPosition: offset of the cursor position
//  - suffixEnd: offset of the first character after the completion's suffix
export type CompletionLocation = {
    prefixBegin: number;
    cursorPosition: number;
    suffixEnd: number;
};

export type FetchFunction = (input: RequestInfo, init?: RequestInit) => Promise<Response>;

/**
 * CompletionItem represent one single completion.
 *
 * Each contains all necessary data to construct an vscode.InlineCompletionItem
 */
export interface CompletionItem {
    text: string;
    skippedSuffix: string;
    suffixReplacementText: string;
    filterScore?: number;
}

/**
 * CompletionResult defines a response from a completion request.
 *
 * This contains of an array of CompletionItem each converted to
 * one completion item in vscode, as well as other data associated
 * with the completion that are not directly associated with completions.
 *
 * This is represents the internal interface used inside the extension and
 * is converted from a BackCompletionResult which conforms to the interface
 * of the actual API call.
 */
export type CompletionResult = {
    completionItems: CompletionItem[];
    unknownBlobNames: string[];
    checkpointNotFound: boolean;
    suggestedPrefixCharCount?: number;
    suggestedSuffixCharCount?: number;
    completionTimeoutMs?: number;
};

export type CheckpointBlobsResult = {
    newCheckpointId: string;
};

export type MemorizeResult = {
    blobName: string;
};

export type BatchUploadResult = {
    blobNames: string[];
};

export type FindMissingResult = {
    unknownBlobNames: string[];
    nonindexedBlobNames: string[];
};

export type ChatResult = {
    text: string;
    unknownBlobNames?: string[];
    checkpointNotFound?: boolean;
    workspaceFileChunks?: WorkspaceFileChunk[];
    nodes?: ChatResultNode[];
};

// Re-export the ClientMetric type
export type ClientMetric = TClientMetric;

/**
 * CompletionResolution describes the resolution of a completion request.
 * - request_id: the request ID of the request
 * - emit_time_sec/nsec: the time the request was returned to vscode (and presumably shown
 *   to the user)
 * - resolve_time_sec/nsec: the time the user accepted or rejected the completion
 * - accepted_idx: the index of the completion that was accepted, or -1 if the completion
 *   was rejected
 */
export type CompletionResolution = {
    request_id: string;
    emit_time_sec: number;
    emit_time_nsec: number;
    resolve_time_sec: number;
    resolve_time_nsec: number;
    accepted_idx: number;
};

export type Model = {
    name: string;
    suggestedPrefixCharCount: number;
    suggestedSuffixCharCount: number;
    completionTimeoutMs?: number;
    internalName?: string;
};

export type Language = {
    name: string;
    vscodeName: string;
    extensions: string[];
};

export type ModelConfig = {
    defaultModel: string;
    models: Model[];
    languages: Language[];
    featureFlags: FeatureFlags;
};

export type TabSwitchEvent = {
    path: string;
    file_blob_name: string;
};

export type ReplacementText = {
    blob_name: string;
    path: string;
    char_start: number;
    char_end: number;
    replacement_text: string;
    present_in_blob: boolean;
    expected_blob_name: string;
};

export type RecencyInfo = {
    tab_switch_events?: TabSwitchEvent[];
    recent_changes?: ReplacementText[];
};

// Note(rich): This is a placeholder for now.
export type FileEditEvent = undefined;

/**
 * The blobs for a completion is represented as a +/- delta of blob ids
 * applied to a saved checkpoint of blob ids. An undefined checkpointId
 * represents a starting empty set of blob names.
 */
export type Blobs = {
    checkpointId: string | undefined;
    addedBlobs: string[];
    deletedBlobs: string[];
};

export type KeyValuePair = {
    key: string;
    value: string;
};

export type BlobMetadata = KeyValuePair[];

export type ClientCompletionTimline = {
    request_id: string;

    initial_request_time_sec: number;
    initial_request_time_nsec: number;
    api_start_time_sec: number;
    api_start_time_nsec: number;
    api_end_time_sec: number;
    api_end_time_nsec: number;
    emit_time_sec: number;
    emit_time_nsec: number;
};

/**
 * APIServer is the interface to the augment API.
 */
export interface APIServer {
    sessionId: string;
    getSessionId(): string;
    createRequestId(): string;
    complete: (
        requestId: string,
        prefix: string,
        suffix: string,
        pathName: string,
        blobName: string | undefined,
        completionLocation: CompletionLocation | undefined,
        language: string,
        blobs: Blobs,
        recentChanges: ReplacementText[],
        fileEditEvents?: FileEditEvent[],
        completionTimeoutMs?: number,
        probeOnly?: boolean,
        apiTiming?: APITiming
    ) => Promise<CompletionResult>;
    checkpointBlobs: (blobs: Blobs) => Promise<CheckpointBlobsResult>;
    memorize: (
        pathName: string,
        text: string,
        blobName: string,
        metadata: BlobMetadata,
        timeoutMs?: number
    ) => Promise<MemorizeResult>;
    batchUpload: (blobs: Array<UploadBlob>) => Promise<BatchUploadResult>;
    findMissing: (memoryNames: string[]) => Promise<FindMissingResult>;
    resolveCompletions: (resolutions: CompletionResolution[]) => Promise<void>;
    getModelConfig: () => Promise<ModelConfig>;
    getAccessToken(
        authURI: string,
        tenantURL: string,
        codeVerifier: string,
        code: string
    ): Promise<string>;
    chat(
        requestId: string,
        message: string,
        chatHistory: Exchange[],
        blobs: Blobs,
        userGuidedBlobs: string[],
        externalSourceIds: string[],
        model: string | undefined,
        // vcsChange: VCSChange,
        recentChanges: ReplacementText[],
        contextCodeExchangeRequestId?: string,
        selectedCode?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        language?: string
    ): Promise<ChatResult>;
    chatStream(
        requestId: string,
        message: string,
        chatHistory: Exchange[],
        blobs: Blobs,
        userGuidedBlobs: string[],
        externalSourceIds: string[],
        model: string | undefined,
        // vcsChange: VCSChange,
        recentChanges: ReplacementText[],
        contextCodeExchangeRequestId?: string,
        selectedCode?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        language?: string,
        sessionId?: string,
        disableAutoExternalSources?: boolean,
        userGuidelines?: string,
        workspaceGuidelines?: string
    ): Promise<AsyncIterable<ChatResult>>;
    clientMetrics(metrics: Array<ClientMetric>): Promise<void>;
    reportClientCompletionTimelines(timelines: ClientCompletionTimline[]): Promise<void>;
}

type BlobsPayload = {
    checkpoint_id: string | undefined;
    added_blobs: string[];
    deleted_blobs: string[];
};

// Ensures that the added/deleted blob lists are sorted
export function blobsToBlobsPayload(blobs: Blobs): BlobsPayload {
    return {
        checkpoint_id: blobs.checkpointId,
        added_blobs: blobs.addedBlobs.sort(),
        deleted_blobs: blobs.deletedBlobs.sort(),
    };
}

type CompletionPayload = {
    model?: string;
    prompt: string;
    suffix?: string;
    path: string;
    blob_name?: string;
    prefix_begin?: number;
    cursor_position?: number;
    suffix_end?: number;
    lang: string | undefined;
    blobs: BlobsPayload;
    recency_info?: RecencyInfo;
    probe_only?: boolean;
    sequence_id?: number;
    filter_threshold?: number;
    edit_events: FileEditEventsPayload[];
};

type CheckpointBlobsPayload = {
    blobs: BlobsPayload;
};

export type FileEditEventsPayload = undefined;
type FeatureDetectionFlags = {
    support_raw_output?: boolean; // Added Nov 2024
};

type ChatPayload = {
    model?: string;
    path?: string;
    prefix?: string;
    selected_code?: string;
    suffix?: string;
    message: string;
    chat_history: Exchange[];
    lang?: string;
    blobs?: BlobsPayload;
    user_guided_blobs?: string[];
    external_source_ids?: string[];
    enable_preference_collection?: boolean;
    context_code_exchange_request_id?: string;
    vcs_change: VCSChangePayload;
    recency_info_recent_changes?: ReplacementText[];
    disable_auto_external_sources?: boolean;
    user_guidelines?: string;
    workspace_guidelines?: string;
    feature_detection_flags?: FeatureDetectionFlags;
};
// TODO: Remove the camelCase names once we migrate research to using snake case.
export type VCSChangePayload = {
    working_directory_changes: WorkingDirectoryChange[];
};

type WorkingDirectoryChange = {
    after_path?: string;
    before_path?: string;
    change_type: ChangeType;
    head_blob_name?: string;
    indexed_blob_name?: string;
    current_blob_name?: string;
};

/**
 * These Back* types are the result types of the various back-end requests. They use
 * the names defined by the back-end API, which don't conform to the TypeScript naming
 * convention, hence the non-Back* types above.
 */

// One item in a list of completions
export type BackCompletionItem = {
    text: string;
    skipped_suffix: string;
    suffix_replacement_text: string;
    filter_score?: number;
};

// For backward compatibility:
// * `completions` replaces `text`.
// * `unknown_blob_names` replaces `unknown_memory_names`.
// production still uses `text` and PR to replace it with completion_items is ongoing.
// research uses completions and will be switched to completion_items.
// So for now we need to support all 3 fields.
// A well formed result will have at least one of each pair. If it has both, the newer
// field will be used and the older one will be ignored.
export type BackCompletionResult = {
    text: string;
    completions: string[];
    completion_items: BackCompletionItem[];
    unknown_memory_names: string[];
    unknown_blob_names: string[];
    // Added for checkpoint versions, which server may not yet support.
    checkpoint_not_found?: boolean;
    suggested_prefix_char_count?: number;
    suggested_suffix_char_count?: number;
    // The completion request timeout in milliseconds.
    completion_timeout_ms: number;
};

export type BackCheckpointBlobsResult = {
    new_checkpoint_id: string;
};

// For backward compatibility:
// * `blob_name` replaces `mem_object_name`
export type BackMemorizeResult = {
    blob_name: string;
    mem_object_name: string;
};

export type BackFindMissingResult = {
    unknown_memory_names: string[];
    nonindexed_blob_names: string[];
};

// For backward compatibility:
// * `max_upload_size_bytes` replaces `max_memorize_size_bytes`
export type BackModelInfo = {
    name: string;
    suggested_prefix_char_count: number;
    suggested_suffix_char_count: number;
    max_upload_size_bytes?: number;
    max_memorize_size_bytes?: number;
    completion_timeout_ms?: number;
    internal_name?: string;
};

export type BackLanguageInfo = {
    name: string;
    vscode_name: string;
    extensions: string[];
};

export type BackFeatureFlags = {
    tab_switch_events?: boolean;
    git_diff_polling_freq_msec?: number;
    checkpoint_blobs_v2?: boolean;
    additional_chat_models?: string;
    vscode_external_sources_in_chat_min_version?: string;
    enable_instructions?: boolean;
    enable_smart_paste?: boolean;
    enable_smart_paste_min_version?: string;
    enable_view_text_document?: boolean;
    checkpoint_blobs?: boolean;
    enable_chat?: boolean;
    enable_uploads_by_language?: boolean;
    small_sync_threshold?: number;
    big_sync_threshold?: number;
    enable_workspace_manager_ui?: boolean;
    enable_external_sources_in_chat?: boolean;
    bypass_language_filter?: boolean;
    enable_workspace_manager_ui_launch?: boolean;
    enable_hindsight?: boolean;
    max_upload_size_bytes?: number;
    vscode_next_edit_min_version?: string;
    vscode_flywheel_min_version?: string;
    vscode_share_min_version?: string;
    max_trackable_file_count?: number;
    max_trackable_file_count_without_permission?: number;
    min_uploaded_percentage_without_permission?: number;
    vscode_sources_min_version?: string;
    vscode_chat_hint_decoration_min_version?: string;
    next_edit_debounce_ms?: number;
    enable_completion_file_edit_events?: boolean;
    vscode_enable_cpu_profile?: boolean;
    verify_folder_is_source_repo?: boolean;
    refuse_to_sync_home_directories?: boolean;
    enable_file_limits_for_syncing_permission?: boolean;
    enable_chat_mermaid_diagrams?: boolean;
    enable_summary_titles?: boolean;
    vscode_new_threads_menu_min_version?: string;
    vscode_editable_history_min_version?: string;
    vscode_use_checkpoint_manager_context_min_version?: string;
    vscode_validate_checkpoint_manager_context?: boolean;
    vscode_agent_mode_min_version?: string;
    vscode_agent_mode_min_stable_version?: string;
};

export type BackGetModelsResult = {
    default_model: string;
    max_upload_size_bytes?: number;
    models: BackModelInfo[];
    languages: BackLanguageInfo[];
    feature_flags?: BackFeatureFlags;
};

export type BackWorkspaceFileChunk = {
    char_start: number;
    char_end: number;
    blob_name: string;
};

export type BackChatResult = {
    text: string;
    unknown_blob_names?: string[];
    checkpoint_not_found?: boolean;
    workspace_file_chunks?: BackWorkspaceFileChunk[];
    nodes?: ChatResultNode[];
};

export type UploadBlob = {
    pathName: string;
    text: string;
    blobName: string;
    metadata: BlobMetadata;
};

class ConversionFailure extends Error {
    constructor(errMsg: string, response: string) {
        super(`Conversion failure: ${errMsg}. Response = ${response}`);
    }
}

export class APIServerImpl implements APIServer {
    static readonly defaultRequestTimeoutMs = 30000;

    private _sequenceId = new SequenceID();

    protected readonly _logger: AugmentLogger = getLogger("AugmentExtension");

    constructor(
        protected _configListener: AugmentConfigListener,
        private _auth: AuthSessionStore,
        public sessionId: string,
        private _userAgent: string,
        private _fetchFunction: FetchFunction
    ) {}

    public getSessionId(): string {
        return this.sessionId;
    }

    public createRequestId(): string {
        return uuidv4();
    }

    protected async callApi<T extends {} | void>(
        requestId: string,
        config: AugmentConfig,
        apiEndpoint: string,
        body: Record<string, any>,
        convert?: (json: {}) => T,
        baseURL?: string,
        requestTimeoutMs?: number,
        apiTiming?: APITiming
    ): Promise<T> {
        let token = config.apiToken;
        let isSessionToken = false;

        if (this._auth.useOAuth) {
            // We have OAuth config and the user does NOT have an API token.
            const session = await this._auth.getSession();
            if (session) {
                token = session.accessToken;
                isSessionToken = true;
                if (!baseURL) {
                    baseURL = session.tenantURL;
                }
            }
        } else {
            // Either OAuth is not set up yet OR the user has an API token.
            if (!baseURL) {
                baseURL = config.completionURL;
            }
        }

        if (!baseURL) {
            throw new Error("Please configure Augment API URL");
        }
        let apiURL: URL | undefined;
        try {
            apiURL = new URL(apiEndpoint, baseURL);
        } catch (e) {
            this._logger.error("Augment API URL is invalid:", e);
            throw new InvalidCompletionURLError();
        }
        if (!apiURL.protocol.startsWith("http")) {
            throw new Error("Augment API URL must start with 'http://' or 'https://'");
        }

        // Step zero, prepare to do the API call
        const body_json = JSON.stringify(body, (key, value) => {
            // Convert undefined to null
            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
            return value === undefined ? null : value;
        });

        const timeoutMs = requestTimeoutMs ?? APIServerImpl.defaultRequestTimeoutMs;
        const signal = AbortSignal.timeout(timeoutMs);
        const method = "POST";
        // Step one do the api call and get respose
        let response: Response;

        let apiStart: number;
        let apiEnd: number;
        try {
            let headers: { [key: string]: string } = {
                "Content-Type": "application/json",
                "User-Agent": this._userAgent,
                "x-request-id": `${requestId}`,
                "x-request-session-id": `${this.sessionId}`,
                // Version negotiation: if the server's minimum supported version is greater than
                // this value, it will return a 406 Not Acceptable error. The client should then
                // warn the user to upgrade. The source of truth for versions is the ApiVersion
                // enum in services/api_proxy/public_api.proto.
                "x-api-version": "2",
            };
            if (token) {
                headers["Authorization"] = `Bearer ${token}`;
            }

            apiStart = Date.now();
            response = await withTimeout(
                this._fetchFunction(apiURL.toString(), {
                    method,
                    headers,
                    body: body_json,
                    signal,
                }),
                timeoutMs
            );
            apiEnd = Date.now();
        } catch (e) {
            if (e instanceof Error) {
                this._logger.error(
                    `API request ${requestId} to ${apiURL.toString()} failed: ${getErrmsg(e, true)}`
                );
                throw APIError.transientIssue(e);
            } else {
                this._logger.debug(`API request ${requestId} to ${apiURL.toString()} failed`);
            }
            throw e;
        }

        if (apiTiming) {
            apiTiming.rpcStart = apiStart;
            apiTiming.rpcEnd = apiEnd;
        }

        // Step 2 verify resposne
        if (!response!.ok) {
            if (response.status === 499) {
                // Don't log 499 errors as there are lots of them and they are generally not of
                // interest.
                throw new APIError(APIStatus.cancelled, "Request cancelled");
            }
            if (response.status === 401 && isSessionToken) {
                // Removing the session token will reload the extension,
                // forcing the user to sign in.
                void this._auth.removeSession();
            }
            if (response.status === 400 && config.enableDebugFeatures) {
                // print body of response
                this._logger.error(
                    `API request ${requestId} to ${apiURL.toString()} failed: ${await response.clone().text()}`
                );
            }
            this._logger.error(
                `API request ${requestId} to ${apiURL.toString()} ` +
                    `response ${response.status}: ${response.statusText}`
            );
            // Check if this is a "too large" error based on status code
            if (response.status === 413) {
                this._logger.debug(`object size is ${body ? getPropertySizes(body) : 0} `);
            }

            throw await APIError.fromResponse(response);
        }

        let json: any;
        try {
            if (response.headers.get("content-length") === "0") {
                return void 0 as T;
            }
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            json = await response.json();
        } catch (e) {
            this._logger.error(
                `API request ${requestId} to ${apiURL.toString()} ` +
                    `failed to convert response to json: ${(e as Error).message}`
            );
            throw e;
        }
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            return convert ? convert(json) : (json as unknown as T);
        } catch (e) {
            throw new ConversionFailure(getErrmsg(e), JSON.stringify(json));
        }
    }

    /**
     * Call an API endpoint that returns a stream of server-side events
     * @param requestId
     * @param config
     * @param apiEndpoint
     * @param body
     * @param convert
     * @param baseURL
     * @param requestTimeoutMs
     * @returns
     */
    protected async callApiStream<T extends any>(
        requestId: string,
        config: AugmentConfig,
        apiEndpoint: string,
        body?: Record<string, any>,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        convert: (json: any) => T = (json: any) => json,
        baseURL?: string,
        requestTimeoutMs?: number,
        sessionId?: string
    ): Promise<AsyncIterable<T>> {
        let token = config.apiToken;

        if (this._auth.useOAuth) {
            // We have OAuth config and the user does NOT have an API token.
            const session = await this._auth.getSession();
            if (session) {
                token = session.accessToken;
                if (!baseURL) {
                    baseURL = session.tenantURL;
                }
            }
        } else {
            // Either OAuth is not set up yet OR the user has an API token.
            if (!baseURL) {
                baseURL = config.completionURL;
            }
        }

        if (!baseURL) {
            throw new Error("Please configure Augment API URL");
        }
        let apiURL: URL | undefined;
        try {
            apiURL = new URL(apiEndpoint, baseURL);
        } catch (e) {
            this._logger.error("Augment API URL is invalid:", e);
            throw new InvalidCompletionURLError();
        }
        if (!apiURL.protocol.startsWith("http")) {
            throw new Error("Augment API URL must start with 'http://' or 'https://'");
        }

        // Step zero, prepare to do the API call
        const body_json = JSON.stringify(body, (key, value) => {
            // Convert undefined to null
            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
            return value === undefined ? null : value;
        });

        const timeoutMs = requestTimeoutMs ?? APIServerImpl.defaultRequestTimeoutMs;
        const signal = AbortSignal.timeout(timeoutMs);
        const method = "POST";
        // Step one do the api call and get respose
        let response: Response;

        try {
            let headers: { [key: string]: string } = {
                "Content-Type": "application/json",
                "User-Agent": this._userAgent,
                "x-request-id": `${requestId}`,
                "x-request-session-id": `${sessionId ?? this.sessionId}`,
            };
            if (token) {
                headers["Authorization"] = `Bearer ${token}`;
            }

            response = await withTimeout(
                this._fetchFunction(apiURL.toString(), {
                    method,
                    headers,
                    body: body_json,
                    signal,
                }),
                timeoutMs
            );
        } catch (e) {
            if (e instanceof Error) {
                this._logger.error(
                    `API request ${requestId} to ${apiURL.toString()} failed: ${getErrmsg(e, true)}`
                );
                if (e.stack) {
                    this._logger.error(e.stack);
                }
                throw APIError.transientIssue(e);
            }
            throw e;
        }
        // Step 2 verify resposne
        if (!response!.ok) {
            if (response.status === 499) {
                // Don't log 499 errors as there are lots of them and they are generally not of
                // interest.
                throw new APIError(APIStatus.cancelled, "Request cancelled");
            }
            if (response.status === 401) {
                // Removing the session token will reload the extension,
                // forcing the user to sign in.
                void this._auth.removeSession();
            }
            if (response.status === 400 && config.enableDebugFeatures) {
                // print body of response
                this._logger.error(
                    `API request ${requestId} to ${apiURL.toString()} failed: ${await response.clone().text()}`
                );
            }
            this._logger.error(
                `API request ${requestId} to ${apiURL.toString()} ` +
                    `response ${response.status}: ${response.statusText}`
            );
            // Check if this is a "too large" error based on status code
            if (response.status === 413) {
                this._logger.debug(`object size is ${body ? getPropertySizes(body) : 0} `);
            }

            throw await APIError.fromResponse(response);
        }
        // Step 3 read stream and convert
        const reader = response.body!.getReader();
        const logger = this._logger; // Make logger available to generator function
        async function* readerToGenerator(
            reader: ReadableStreamReader<Uint8Array>
        ): AsyncGenerator<T, void, undefined> {
            const textDecoder = new TextDecoder();
            let textBuffer = "";
            while (true) {
                const { value, done } = await reader.read(new Uint8Array());
                if (done) {
                    return;
                }
                textBuffer += textDecoder.decode(value, { stream: true });
                // Parse Newline Delimited JSON
                while (textBuffer.includes("\n")) {
                    const newLineIndex = textBuffer.indexOf("\n");
                    const text = textBuffer.substring(0, newLineIndex);
                    textBuffer = textBuffer.substring(newLineIndex + 1);
                    try {
                        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                        const json = JSON.parse(text);
                        yield convert(json);
                    } catch (e: any) {
                        logger.error(`JSON parse failed for ${text}: ${getErrmsg(e)}`);
                    }
                }
            }
        }
        return readerToGenerator(reader);
    }

    /**
     * Convert a BackCompletionItem to a CompletionItem.
     *
     * Also check that the fields are of the correct type.
     *
     * @param item BackCompletionItem
     * @returns CompletionItem
     */
    private _toCompletionItem(item: BackCompletionItem): CompletionItem {
        // verify that field text is present and is a string, otherwise throw
        if (typeof item.text !== "string") {
            throw new Error(`Completion item text is not a string: ${JSON.stringify(item)}`);
        }
        // verify that the other two fields, if present, are also strings
        if (item.skipped_suffix !== undefined && typeof item.skipped_suffix !== "string") {
            throw new Error(
                `Completion item skipped suffix is not a string: ${JSON.stringify(item)}`
            );
        }
        if (
            item.suffix_replacement_text !== undefined &&
            typeof item.suffix_replacement_text !== "string"
        ) {
            throw new Error(
                `Completion item suffix replacement text is not a string: ${JSON.stringify(item)}`
            );
        }
        return {
            text: item.text,
            suffixReplacementText: item.suffix_replacement_text ?? "",
            skippedSuffix: item.skipped_suffix ?? "",
            filterScore: item.filter_score ?? undefined,
        };
    }

    // Recent version of research and prod can have completion in any one of the
    // three fields: completion_items, completions and text.  We will try to parse
    // in this order.
    private _extractCompletions(resp: BackCompletionResult): CompletionItem[] {
        // It has the newest format: array of items
        if (Array.isArray(resp.completion_items)) {
            return resp.completion_items.map((item) => this._toCompletionItem(item));
        }
        // Previous protocol, a list of strings
        // The result must have at least one of completions or text.
        if (Array.isArray(resp.completions)) {
            const completions = toStringArray(
                "BackCompletionResult",
                "completions",
                resp.completions
            );
            return completions.map((text) => {
                return {
                    text,
                    suffixReplacementText: "",
                    skippedSuffix: "",
                };
            });
        }
        // The earliest format, just a string. Prod does this currently but will soon change.
        return [
            {
                text: toString("BackCompletionResult", "text", resp.text),
                suffixReplacementText: "",
                skippedSuffix: "",
            },
        ];
    }

    private toCompleteResult(resp: BackCompletionResult): CompletionResult {
        const completionItems = this._extractCompletions(resp);

        // The result must have at least one of unknown_blob_names or
        // unknown_memory_names.
        const unknownBlobNames = toStringArray(
            "BackCompletionResult",
            "unknown_blob_names/unknown_memory_names",
            resp.unknown_blob_names ?? resp.unknown_memory_names
        );

        // Backend may not return the checkpoint_not_found field if it's not yet
        // supported, so fallback to a default value.
        const checkpointNotFound =
            resp.checkpoint_not_found === undefined
                ? false
                : toBoolean(
                      "BackCompletionResult",
                      "checkpoint_not_found",
                      resp.checkpoint_not_found,
                      false
                  );
        return {
            completionItems,
            unknownBlobNames,
            checkpointNotFound,
            suggestedPrefixCharCount: resp.suggested_prefix_char_count,
            suggestedSuffixCharCount: resp.suggested_suffix_char_count,
            completionTimeoutMs: resp.completion_timeout_ms,
        };
    }

    private toCheckpointBlobsResult(resp: BackCheckpointBlobsResult): CheckpointBlobsResult {
        const newCheckpointId = toString(
            "BackCheckpointBlobsResult",
            "new_checkpoint_id",
            resp.new_checkpoint_id
        );

        return {
            newCheckpointId,
        };
    }

    // complete() issues a completion request to the back end.
    //  - requestId: The request's request id
    //  - prefix: Completion's prefix text
    //  - suffix: Completion's suffix text
    //  - pathName: path name of the completion's target blob
    //  - blobName: blob name of the completion's target blob
    //  - completionLocation: location information within the target blob
    //  - blobs: the blobs to include as context for the completion
    //  - recencyInfo: information about recent events
    //  - fileEditEvents: information about recent file edits
    public async complete(
        requestId: string,
        prefix: string,
        suffix: string,
        pathName: string,
        blobName: string | undefined,
        completionLocation: CompletionLocation | undefined,
        language: string,
        blobs: Blobs,
        recentChanges: ReplacementText[],
        fileEditEvents?: FileEditEvent[],
        completionTimeoutMs?: number,
        probeOnly?: boolean,
        apiTiming?: APITiming
    ): Promise<CompletionResult> {
        const config = this._configListener.config;

        const recencyInfo: RecencyInfo = {
            recent_changes: recentChanges,
        };

        const payload: CompletionPayload = {
            model: config.modelName,
            prompt: prefix,
            suffix,
            path: pathName,
            blob_name: blobName,
            prefix_begin: completionLocation?.prefixBegin,
            cursor_position: completionLocation?.cursorPosition,
            suffix_end: completionLocation?.suffixEnd,
            lang: language,
            blobs: blobsToBlobsPayload(blobs),
            recency_info: recencyInfo,
            probe_only: probeOnly,
            sequence_id: this._sequenceId.next(),
            filter_threshold: config.completions.filterThreshold,
            edit_events: this.toFileDiffsPayload(fileEditEvents ?? []),
        };
        return await this.callApi<CompletionResult>(
            requestId,
            config,
            "completion",
            payload,
            (json) => this.toCompleteResult(json as BackCompletionResult),
            undefined,
            completionTimeoutMs,
            apiTiming
        );
    }

    public async checkpointBlobs(blobs: Blobs): Promise<CheckpointBlobsResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        const payload: CheckpointBlobsPayload = {
            blobs: blobsToBlobsPayload(blobs),
        };
        const result = await this.callApi<CheckpointBlobsResult>(
            requestId,
            config,
            "checkpoint-blobs",
            payload,
            (json) => this.toCheckpointBlobsResult(json as BackCheckpointBlobsResult)
        );
        for (const url of this.getUniqueExtraURLs()) {
            // TODO: Remove this when we switch to production.
            const extraURLResult = await this.callApi<CheckpointBlobsResult>(
                requestId,
                config,
                "checkpoint-blobs",
                payload,
                (json) => this.toCheckpointBlobsResult(json as BackCheckpointBlobsResult),
                url
            );
            if (extraURLResult.newCheckpointId !== result.newCheckpointId) {
                this._logger.error(
                    `Checkpoint blobs API returned different checkpoint IDs for ${url}`
                );
            }
        }
        return result;
    }

    private toVCSChangePayload(vcsChange: VCSChange): VCSChangePayload {
        return {
            working_directory_changes: vcsChange.workingDirectory.map((wd) => {
                return {
                    before_path: wd.beforePath,
                    after_path: wd.afterPath,
                    change_type: wd.changeType,
                    head_blob_name: wd.headBlobName,
                    indexed_blob_name: wd.indexedBlobName,
                    current_blob_name: wd.currentBlobName,
                };
            }),
        };
    }

    private toFileDiffsPayload(_fileDiffs: FileEditEvent[]): FileEditEventsPayload[] {
        return [];
    }

    // TODO: Remove this when we switch to production.
    protected getUniqueExtraURLs(): Set<string> {
        const extraURLs = new Set<string>();
        return extraURLs;
    }

    private toMemorizeResult(resp: BackMemorizeResult): MemorizeResult {
        const blobName =
            resp.blob_name !== undefined
                ? toString("BackMemorizeResult", "blob_name", resp.blob_name)
                : toString("BackMemorizeResult", "mem_object_name", resp.mem_object_name);
        return { blobName };
    }

    public async memorize(
        pathName: string,
        text: string,
        blobName: string,
        metadata: BlobMetadata,
        timeoutMs?: number
    ): Promise<MemorizeResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        const result = await this.callApi<MemorizeResult>(
            requestId,
            config,
            "memorize",
            {
                model: config.modelName,
                path: pathName,
                t: text,
                blob_name: blobName,
                metadata: metadata,
                timeout_ms: timeoutMs,
            },
            (json) => this.toMemorizeResult(json as BackMemorizeResult)
        );
        for (const url of this.getUniqueExtraURLs()) {
            // TODO: Remove this when we switch to production.
            await this.callApi<MemorizeResult>(
                requestId,
                config,
                "memorize",
                {
                    model: config.modelName,
                    path: pathName,
                    t: text,
                    blob_name: blobName,
                    metadata: metadata,
                },
                (json) => this.toMemorizeResult(json as BackMemorizeResult),
                url
            );
        }
        return result;
    }

    private toBatchUploadResult(resp: any): BatchUploadResult {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
        return { blobNames: resp.blob_names };
    }

    public async batchUpload(blobs: Array<UploadBlob>): Promise<BatchUploadResult> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        try {
            const result = await this.callApi<BatchUploadResult>(
                requestId,
                config,
                "batch-upload",
                {
                    blobs: blobs.map((blob) => {
                        return {
                            blob_name: blob.blobName,
                            path: blob.pathName,
                            content: blob.text,
                        };
                    }),
                },
                this.toBatchUploadResult.bind(this)
            );
            for (const url of this.getUniqueExtraURLs()) {
                // TODO: Remove this when we switch to production.
                await this.callApi<BatchUploadResult>(
                    requestId,
                    config,
                    "batch-upload",
                    {
                        blobs: blobs.map((blob) => {
                            return {
                                blob_name: blob.blobName,
                                path: blob.pathName,
                                content: blob.text,
                            };
                        }),
                    },
                    this.toBatchUploadResult.bind(this),
                    url
                );
            }
            return result;
        } catch (err: any) {
            // Unimplemented may occur on the research backend
            if (!APIError.isAPIErrorWithStatus(err, APIStatus.unimplemented)) {
                throw err;
            }

            const names: string[] = [];
            for (const blob of blobs) {
                const result = await this.memorize(
                    blob.pathName,
                    blob.text,
                    blob.blobName,
                    blob.metadata
                );
                names.push(result.blobName);
            }
            return {
                blobNames: names,
            };
        }
    }

    private toFindMissingResult(resp: BackFindMissingResult): FindMissingResult {
        return {
            unknownBlobNames: toStringArray(
                "BackFindMissingResult",
                "unknown_memory_names",
                resp.unknown_memory_names
            ),
            nonindexedBlobNames: toStringArray(
                "BackFindMissingResult",
                "nonindexed_blob_names",
                resp.nonindexed_blob_names
            ),
        };
    }

    public async findMissing(memoryNames: string[]): Promise<FindMissingResult> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        const model = config.modelName;
        const memories = [...memoryNames].sort();
        const result = await this.callApi<FindMissingResult>(
            requestId,
            config,
            "find-missing",
            {
                model,
                mem_object_names: memories,
            },
            (json) => this.toFindMissingResult(json as BackFindMissingResult)
        );
        // If the extra URLs are set, we union their result into the main one.
        // This ensures that all the servers will eventually get all blobs
        // at the cost of doing unneeded uploads to some servers.
        // However, this is only temporary code and will only happen for those
        // of us with these extra URLs set.
        for (const url of this.getUniqueExtraURLs()) {
            // TODO: Remove this when we switch to production.
            let toAdd = await this.callApi<FindMissingResult>(
                requestId,
                config,
                "find-missing",
                {
                    model,
                    mem_object_names: memories,
                },
                (json) => this.toFindMissingResult(json as BackFindMissingResult),
                url
            );
            result.unknownBlobNames = result.unknownBlobNames.concat(toAdd.unknownBlobNames);
            result.nonindexedBlobNames = result.nonindexedBlobNames.concat(
                toAdd.nonindexedBlobNames
            );
        }
        return result;
    }

    public async resolveCompletions(resolutions: CompletionResolution[]): Promise<void> {
        const requestId = this.createRequestId();
        const config = this._configListener.config;
        return await this.callApi<void>(requestId, config, "resolve-completions", {
            client_name: "vscode-extension",
            resolutions,
        });
    }

    private toChatResult(resp: BackChatResult): ChatResult {
        const text = toString("BackChatResult", "text", resp.text);
        const unknownBlobNames =
            resp.unknown_blob_names === undefined
                ? []
                : toStringArray("BackChatResult", "unknown_blob_names", resp.unknown_blob_names);
        const checkpointNotFound =
            resp.checkpoint_not_found === undefined
                ? false
                : toBoolean(
                      "BackChatResult",
                      "checkpoint_not_found",
                      resp.checkpoint_not_found,
                      false
                  );
        const workspaceFileChunks =
            resp.workspace_file_chunks === undefined
                ? []
                : resp.workspace_file_chunks.map((chunk) => {
                      return {
                          charStart: toNumber(
                              "BackWorkspaceFileChunk",
                              "char_start",
                              chunk.char_start
                          ),
                          charEnd: toNumber("BackWorkspaceFileChunk", "char_end", chunk.char_end),
                          blobName: toString(
                              "BackWorkspaceFileChunk",
                              "blob_name",
                              chunk.blob_name
                          ),
                      };
                  });
        const nodes =
            resp.nodes === undefined
                ? []
                : resp.nodes.map((node) => {
                      return {
                          id: toNumber("BackChatResultNode", "id", node.id),
                          type: toNumber("BackChatResultNode", "type", node.type),
                          content: toString("BackChatResultNode", "content", node.content),
                      };
                  });
        return { text, unknownBlobNames, checkpointNotFound, workspaceFileChunks, nodes };
    }

    public async chat(
        requestId: string,
        message: string,
        chatHistory: Exchange[],
        blobs: Blobs,
        userGuidedBlobs: string[],
        externalSourceIds: string[],
        model: string | undefined,
        // XXX I don't want to deal with this yet
        // vcsChange: VCSChange,
        recentChanges: ReplacementText[],
        contextCodeExchangeRequestId?: string,
        selectedCode?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        language?: string
    ): Promise<ChatResult> {
        const config = this._configListener.config;
        const payload: ChatPayload = {
            model: model ?? config.chat.model,
            path: pathName,
            prefix: prefix,
            selected_code: selectedCode,
            suffix: suffix,
            message: message,
            chat_history: chatHistory,
            lang: language,
            blobs: blobsToBlobsPayload(blobs),
            user_guided_blobs: userGuidedBlobs,
            external_source_ids: externalSourceIds,
            // XXX Didn't want to add this to the config yet
            // enable_preference_collection: config.preferenceCollection.enable,
            context_code_exchange_request_id: contextCodeExchangeRequestId,
            vcs_change: { working_directory_changes: [] } as VCSChangePayload, // this.toVCSChangePayload(vcsChange),
            recency_info_recent_changes: recentChanges,
            feature_detection_flags: {
                support_raw_output: true,
            },
        };
        return await this.callApi<ChatResult>(
            requestId,
            config,
            "chat",
            payload,
            (json) => this.toChatResult(json as BackChatResult),
            // If chat URL is not configured, the function will use the completion URL.
            config.chat.url,
            120000
        );
    }

    public async chatStream(
        requestId: string,
        message: string,
        chatHistory: Exchange[],
        blobs: Blobs,
        userGuidedBlobs: string[],
        externalSourceIds: string[],
        model: string | undefined,
        // vcsChange: VCSChange,
        recentChanges: ReplacementText[],
        contextCodeExchangeRequestId?: string,
        selectedCode?: string,
        prefix?: string,
        suffix?: string,
        pathName?: string,
        language?: string,
        sessionId?: string,
        disableAutoExternalSources?: boolean,
        userGuidelines?: string,
        workspaceGuidelines?: string
    ): Promise<AsyncIterable<ChatResult>> {
        const config = this._configListener.config;
        const payload: ChatPayload = {
            model: model ?? config.chat.model,
            path: pathName,
            prefix: prefix,
            selected_code: selectedCode,
            suffix: suffix,
            message: message,
            chat_history: chatHistory,
            lang: language,
            blobs: blobsToBlobsPayload(blobs),
            user_guided_blobs: userGuidedBlobs,
            context_code_exchange_request_id: contextCodeExchangeRequestId,
            vcs_change: { working_directory_changes: [] } as VCSChangePayload, // this.toVCSChangePayload(vcsChange),
            recency_info_recent_changes: recentChanges,
            external_source_ids: externalSourceIds,
            disable_auto_external_sources: disableAutoExternalSources,
            user_guidelines: userGuidelines,
            workspace_guidelines: workspaceGuidelines,
            feature_detection_flags: {
                support_raw_output: true, // Added Nov 2024
            },
        };
        const startChatStream = (): Promise<AsyncIterable<ChatResult>> => {
            return this.callApiStream<ChatResult>(
                requestId,
                config,
                "chat-stream",
                payload,
                this.toChatResult.bind(this),
                // If chat URL is not configured, the function will use the completion URL.
                config.chat.url,
                120000,
                sessionId
            );
        };
        return await retryWithBackoff(startChatStream, this._logger, {
            initialMS: 250, // Initial delay is 250ms
            mult: 2, // Exponential backoff
            maxMS: 5000, // Max delay is 5s
            maxTries: 5, // Max number of retries is 5
            maxTotalMs: 5000, // Max total time spent is 5s
        });
    }

    private toModel(resp: BackModelInfo): Model {
        const completionTimeoutMs =
            resp.completion_timeout_ms !== undefined
                ? toNumber("BackModelInfo", "completion_timeout_ms", resp.completion_timeout_ms)
                : undefined;
        return {
            name: toString("BackModelInfo", "name", resp.name),
            suggestedPrefixCharCount: toNumber(
                "BackModelInfo",
                "suggested_prefix_char_count",
                resp.suggested_prefix_char_count
            ),
            suggestedSuffixCharCount: toNumber(
                "BackModelInfo",
                "suggested_suffix_char_count",
                resp.suggested_suffix_char_count
            ),
            completionTimeoutMs,
            internalName:
                resp.internal_name &&
                toString("BackModelInfo", "internal_name", resp.internal_name),
        };
    }

    private toLanguage(resp: BackLanguageInfo): Language {
        const name = toString("BackLanguageInfo", "name", resp.name);
        const vscodeName = toString("BackLanguageInfo", "vscodeName", resp.vscode_name);
        verifyArray("BackLanguageInfo", "extensions", resp.extensions);
        const extensions: string[] = [];
        for (const extension of resp.extensions) {
            extensions.push(toString("BackLanguageInfo", "extensions", extension));
        }
        return { name, vscodeName, extensions };
    }

    private toGetModelsResult(resp: BackGetModelsResult): ModelConfig {
        const defaultModel = toString("BackGetModelsResult", "default_model", resp.default_model);

        verifyArray("BackGetModelsResult", "models", resp.models);
        const models: Model[] = [];
        for (const modelInfo of resp.models) {
            models.push(this.toModel(modelInfo));
        }

        // Extract feature flags
        const featureFlags = defaultFeatureFlags;

        if (resp.feature_flags !== undefined) {
            let git_diff_freq = resp.feature_flags.git_diff_polling_freq_msec;
            if (git_diff_freq !== undefined && git_diff_freq > 0) {
                featureFlags.gitDiff = true;
                featureFlags.gitDiffPollingFrequencyMSec = git_diff_freq;
            }
            if (resp.feature_flags.small_sync_threshold !== undefined) {
                featureFlags.smallSyncThreshold = resp.feature_flags.small_sync_threshold;
            }
            if (resp.feature_flags.big_sync_threshold !== undefined) {
                featureFlags.bigSyncThreshold = resp.feature_flags.big_sync_threshold;
            }
            if (resp.feature_flags.enable_workspace_manager_ui_launch !== undefined) {
                featureFlags.enableWorkspaceManagerUi =
                    resp.feature_flags.enable_workspace_manager_ui_launch;
            }
            if (resp.feature_flags.enable_instructions !== undefined) {
                featureFlags.enableInstructions = resp.feature_flags.enable_instructions;
            }
            if (resp.feature_flags.enable_smart_paste !== undefined) {
                featureFlags.enableSmartPaste = resp.feature_flags.enable_smart_paste;
            }
            if (resp.feature_flags.enable_smart_paste_min_version !== undefined) {
                featureFlags.enableSmartPasteMinVersion =
                    resp.feature_flags.enable_smart_paste_min_version;
            }
            if (resp.feature_flags.enable_view_text_document !== undefined) {
                featureFlags.enableViewTextDocument = resp.feature_flags.enable_view_text_document;
            }
            if (resp.feature_flags.bypass_language_filter !== undefined) {
                featureFlags.bypassLanguageFilter = resp.feature_flags.bypass_language_filter;
            }
            if (resp.feature_flags.additional_chat_models !== undefined) {
                featureFlags.additionalChatModels = resp.feature_flags.additional_chat_models;
            }
            if (resp.feature_flags.enable_hindsight !== undefined) {
                featureFlags.enableHindsight = resp.feature_flags.enable_hindsight;
            }
            if (resp.feature_flags.max_upload_size_bytes !== undefined) {
                featureFlags.maxUploadSizeBytes = resp.feature_flags.max_upload_size_bytes;
            }

            if (resp.feature_flags.vscode_next_edit_min_version !== undefined) {
                featureFlags.vscodeNextEditMinVersion =
                    resp.feature_flags.vscode_next_edit_min_version;
            }
            if (resp.feature_flags.vscode_flywheel_min_version !== undefined) {
                featureFlags.vscodeFlywheelMinVersion =
                    resp.feature_flags.vscode_flywheel_min_version;
            }
            if (resp.feature_flags.vscode_external_sources_in_chat_min_version !== undefined) {
                featureFlags.vscodeExternalSourcesInChatMinVersion =
                    resp.feature_flags.vscode_external_sources_in_chat_min_version;
            }
            if (resp.feature_flags.vscode_share_min_version !== undefined) {
                featureFlags.vscodeShareMinVersion = resp.feature_flags.vscode_share_min_version;
            }
            if (resp.feature_flags.max_trackable_file_count !== undefined) {
                featureFlags.maxTrackableFileCount = resp.feature_flags.max_trackable_file_count;
            }
            if (resp.feature_flags.max_trackable_file_count_without_permission !== undefined) {
                featureFlags.maxTrackableFileCountWithoutPermission =
                    resp.feature_flags.max_trackable_file_count_without_permission;
            }
            if (resp.feature_flags.min_uploaded_percentage_without_permission !== undefined) {
                featureFlags.minUploadedPercentageWithoutPermission =
                    resp.feature_flags.min_uploaded_percentage_without_permission;
            }
            if (resp.feature_flags.vscode_sources_min_version !== undefined) {
                featureFlags.vscodeSourcesMinVersion =
                    resp.feature_flags.vscode_sources_min_version;
            }
            if (resp.feature_flags.vscode_chat_hint_decoration_min_version !== undefined) {
                featureFlags.vscodeChatHintDecorationMinVersion =
                    resp.feature_flags.vscode_chat_hint_decoration_min_version;
            }
            if (resp.feature_flags.next_edit_debounce_ms !== undefined) {
                featureFlags.nextEditDebounceMs = resp.feature_flags.next_edit_debounce_ms;
            }
            if (resp.feature_flags.enable_completion_file_edit_events !== undefined) {
                featureFlags.enableCompletionFileEditEvents =
                    resp.feature_flags.enable_completion_file_edit_events;
            }
            if (resp.feature_flags.vscode_enable_cpu_profile !== undefined) {
                featureFlags.vscodeEnableCpuProfile = resp.feature_flags.vscode_enable_cpu_profile;
            }
            if (resp.feature_flags.verify_folder_is_source_repo !== undefined) {
                featureFlags.verifyFolderIsSourceRepo =
                    resp.feature_flags.verify_folder_is_source_repo;
            }
            if (resp.feature_flags.refuse_to_sync_home_directories !== undefined) {
                featureFlags.refuseToSyncHomeDirectories =
                    resp.feature_flags.refuse_to_sync_home_directories;
            }
            if (resp.feature_flags.enable_file_limits_for_syncing_permission !== undefined) {
                featureFlags.enableFileLimitsForSyncingPermission =
                    resp.feature_flags.enable_file_limits_for_syncing_permission;
            }
            if (resp.feature_flags.enable_chat_mermaid_diagrams !== undefined) {
                featureFlags.enableChatMermaidDiagrams =
                    resp.feature_flags.enable_chat_mermaid_diagrams;
            }
            if (resp.feature_flags.enable_summary_titles !== undefined) {
                featureFlags.enableSummaryTitles = resp.feature_flags.enable_summary_titles;
            }
            // if (resp.feature_flags.smart_paste_precompute_mode !== undefined) {
            //   featureFlags.smartPastePrecomputeMode =
            //     resp.feature_flags.smart_paste_precompute_mode;
            // }
            if (resp.feature_flags.vscode_new_threads_menu_min_version !== undefined) {
                featureFlags.vscodeNewThreadsMenuMinVersion =
                    resp.feature_flags.vscode_new_threads_menu_min_version;
            }
            if (resp.feature_flags.vscode_editable_history_min_version !== undefined) {
                featureFlags.vscodeEditableHistoryMinVersion =
                    resp.feature_flags.vscode_editable_history_min_version;
            }
            if (resp.feature_flags.vscode_agent_mode_min_version !== undefined) {
                featureFlags.vscodeAgentModeMinVersion =
                    resp.feature_flags.vscode_agent_mode_min_version;
            }
            if (resp.feature_flags.vscode_agent_mode_min_stable_version !== undefined) {
                featureFlags.vscodeAgentModeMinStableVersion =
                    resp.feature_flags.vscode_agent_mode_min_stable_version;
            }
        }

        let languages: Language[] = [];

        if (resp.languages === undefined) {
            // Temporary, until all back ends have been changed to return the set
            // of languages.
            languages = defaultSupportedLanguages;
        } else {
            verifyArray("BackGetModelsResult", "languages", resp.languages);
            languages = [];
            for (const languageInfo of resp.languages) {
                languages.push(this.toLanguage(languageInfo));
            }
        }

        return {
            defaultModel,
            models,
            languages,
            featureFlags,
        };
    }

    public async getModelConfig(): Promise<ModelConfig> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        const result = await this.callApi<ModelConfig>(
            requestId,
            config,
            "get-models",
            {},
            (json) => this.toGetModelsResult(json as BackGetModelsResult)
        );
        return result;
    }
    public async getAccessToken(
        authURI: string,
        tenantURL: string,
        codeVerifier: string,
        code: string
    ): Promise<string> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        const body = {
            grant_type: "authorization_code",
            client_id: config.oauth.clientID,
            code_verifier: codeVerifier,
            redirect_uri: authURI,
            code,
        };
        return await this.callApi<string>(
            requestId,
            config,
            "token",
            body,
            (json) => (json as { access_token: string })["access_token"],
            tenantURL
        );
    }

    public async clientMetrics(metrics: Array<ClientMetric>): Promise<void> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        await this.callApi<void>(
            requestId,
            config,
            "client-metrics",
            {
                metrics,
            },
            undefined,
            undefined,
            APIServerImpl.defaultRequestTimeoutMs
        );
    }
    public async reportClientCompletionTimelines(
        timelines: ClientCompletionTimline[]
    ): Promise<void> {
        const config = this._configListener.config;
        const requestId = this.createRequestId();
        await this.callApi<void>(
            requestId,
            config,
            "/client-completion-timelines",
            {
                timelines,
            },
            undefined,
            undefined,
            APIServerImpl.defaultRequestTimeoutMs
        );
    }
}

export class InvalidCompletionURLError extends Error {
    constructor() {
        super("The completion URL setting is invalid");
    }
}

class SequenceID {
    private _sequenceId = 0;

    public next(): number {
        return this._sequenceId++;
    }
}

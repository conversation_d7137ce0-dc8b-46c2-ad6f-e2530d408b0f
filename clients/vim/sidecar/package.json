{"name": "ts-augment-server", "version": "1.0.0", "main": "index.js", "scripts": {"build": "npx tsc -p .", "watch": "npx tsc --watch -p .", "build:bundle": "npx tsc -p . --noEmit && esbuild src/server.ts --bundle --minify --platform=node --tsconfig=./tsconfig.json --outfile=dist/server.bundle.js --banner:js=\"$(cat license_banner.txt)\"", "test": "jest", "test:watch": "jest --watch"}, "author": "", "license": "ISC", "description": "", "dependencies": {"lodash": "^4.17.21", "loglevel": "^1.9.2", "typescript": "^5.6.3", "uuid": "^11.0.3", "vscode-languageserver": "^9.0.1", "vscode-languageserver-protocol": "^3.17.5", "vscode-languageserver-textdocument": "^1.0.11", "vscode-uri": "^3.0.8"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.0.0", "@types/lodash": "^4.14.202", "@types/node": "^22.9.0", "esbuild": "^0.19.0", "fetch-mock": "^9.11.0", "fetch-mock-jest": "^1.5.1", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-junit": "^16.0.0", "node-fetch": "^2.7.0", "ts-jest": "^29.0.0", "vscode-languageserver-types": "^3.17.5"}}
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "update_versions",
    srcs = [
        "update_versions.py",
    ],
    deps = [
        requirement("defusedxml"),
    ],
)

pytest_test(
    name = "update_versions_test",
    srcs = ["update_versions_test.py"],
    deps = [
        ":update_versions",
    ],
)

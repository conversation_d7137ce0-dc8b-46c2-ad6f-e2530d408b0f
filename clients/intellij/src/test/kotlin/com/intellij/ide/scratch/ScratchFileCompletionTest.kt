package com.intellij.ide.scratch

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.completion.AugmentCompletionProvider
import com.augmentcode.intellij.generateCompletionResult
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.lang.xml.XMLLanguage
import com.intellij.openapi.util.IntellijInternalApi
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.testFramework.runInEdtAndGet
import com.intellij.util.application
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@OptIn(IntellijInternalApi::class)
@RunWith(JUnit4::class)
@Suppress("UnstableApiUsage")
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class ScratchFileCompletionTest : BasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.modelName = null
  }

  @Test
  fun testCanComplete() =
    myFixture.testInlineCompletion {
      val completionText = "<augment></augment>"
      val completionResult = generateCompletionResult(completionText)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(completionResult),
        testRootDisposable,
      )

      val mockPathFilterService = mockk<PathFilterService>(relaxed = true)
      coEvery { mockPathFilterService.isAccepted(any()) } returns true
      myFixture.project.registerOrReplaceServiceInstance(
        PathFilterService::class.java,
        mockPathFilterService,
        testRootDisposable,
      )

      val scratchFile =
        runInEdtAndGet {
          val context = ScratchFileCreationHelper.Context()
          context.language = XMLLanguage.INSTANCE
          ScratchFileActions.doCreateNewScratch(project, context)
        }
      assertNotNull(scratchFile)

      myFixture.configureFromExistingVirtualFile(scratchFile?.virtualFile!!)
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResult(completionText)
    }
}

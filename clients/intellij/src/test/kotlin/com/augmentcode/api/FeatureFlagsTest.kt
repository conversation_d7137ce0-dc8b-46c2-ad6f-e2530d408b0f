package com.augmentcode.api

import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FeatureFlagsTest {
  @Test
  fun testAdditionalChatModelsMap_Null() {
    val flags = FeatureFlags()
    flags.additionalChatModels = null

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_Empty() {
    val flags = FeatureFlags()
    flags.additionalChatModels = ""

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_EmptyMap() {
    val flags = FeatureFlags()
    flags.additionalChatModels = "{}"

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_ValidJson() {
    val flags = FeatureFlags()
    flags.additionalChatModels = """{"model1":"value1","model2":"value2"}"""

    val result = flags.additionalChatModelsMap()
    assertEquals(2, result.size)
    assertEquals("value1", result["model1"])
    assertEquals("value2", result["model2"])
  }

  @Test
  fun testAdditionalChatModelsMap_NullValues() {
    val flags = FeatureFlags()
    flags.additionalChatModels = """{"model1":null,"model2":"value2"}"""

    val result = flags.additionalChatModelsMap()
    assertEquals(2, result.size)
    assertEquals("null", result["model1"])
    assertEquals("value2", result["model2"])
  }

  @Test
  fun testAdditionalChatModelsMap_InvalidJson() {
    val flags = FeatureFlags()
    flags.additionalChatModels = "invalid json"

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_Array() {
    val flags = FeatureFlags()
    flags.additionalChatModels = "[foo, bar]"

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_StringNull() {
    val flags = FeatureFlags()
    flags.additionalChatModels = "null"

    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }

  @Test
  fun testAdditionalChatModelsMap_DuplicateKeys() {
    val flags = FeatureFlags()
    flags.additionalChatModels = """{"model1":"value1","model1":"value2"}"""

    // Gson considers duplicate keys to be invalid json
    val result = flags.additionalChatModelsMap()
    assertTrue(result.isEmpty())
  }
}

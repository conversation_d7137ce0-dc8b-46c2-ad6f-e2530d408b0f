package com.augmentcode.intellij.guidelines

import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.settings.PluginVersionProvider
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.FeatureFlagsTestUtil
import com.augmentcode.intellij.webviews.chat.ChatWebviewMessageBus
import com.augmentcode.rpc.UpdateGuidelinesStateRequest
import com.google.protobuf.Message
import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.vfs.newvfs.impl.VfsRootAccess
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

/**
 * Base class for GuidelinesService tests with common setup
 */
abstract class BaseGuidelinesServiceTest : AugmentBasePlatformTestCase() {
  protected lateinit var guidelinesService: GuidelinesService

  override fun setUp() {
    super.setUp()

    // Reset guidelines to default value
    PropertiesComponent.getInstance().unsetValue(GuidelinesService.USER_GUIDELINES_KEY)
    guidelinesService = GuidelinesService.getInstance(project)

    // Allow access to the temp directory for VFS operations
    VfsRootAccess.allowRootAccess(testRootDisposable, myFixture.tempDirPath)
  }

  override fun tearDown() {
    try {
      // Clean up after each test
      PropertiesComponent.getInstance().unsetValue(GuidelinesService.USER_GUIDELINES_KEY)
    } finally {
      super.tearDown()
    }
  }
}

/**
 * Test for default guidelines value
 */
@RunWith(JUnit4::class)
class GetUserGuidelinesDefaultTest : BaseGuidelinesServiceTest() {
  @Test
  fun testGetUserGuidelinesDefault() {
    // Test that the default value is returned when no guidelines are set
    val guidelines = guidelinesService.getUserGuidelines()
    assertEquals(GuidelinesService.DEFAULT_USER_GUIDELINES, guidelines)
  }
}

/**
 * Test for updating and retrieving guidelines
 */
@RunWith(JUnit4::class)
class UpdateAndGetUserGuidelinesTest : BaseGuidelinesServiceTest() {
  @Test
  fun testUpdateAndGetUserGuidelines() {
    // Test updating and retrieving guidelines
    val testGuidelines = "Test guidelines content"
    guidelinesService.updateUserGuidelines(testGuidelines)

    // Verify the guidelines were stored
    val retrievedGuidelines = guidelinesService.getUserGuidelines()
    assertEquals(testGuidelines, retrievedGuidelines)
  }
}

/**
 * Test for guidelines persistence across service instances
 */
@RunWith(JUnit4::class)
class GuidelinesPersistenceTest : BaseGuidelinesServiceTest() {
  @Test
  fun testGuidelinesPersistenceAcrossServiceInstances() {
    // Test that guidelines persist across service instances
    val testGuidelines = "Persistent guidelines"
    guidelinesService.updateUserGuidelines(testGuidelines)

    // Get the service instance again
    val newGuidelinesService = GuidelinesService.getInstance(project)

    // Verify the guidelines were persisted
    val retrievedGuidelines = newGuidelinesService.getUserGuidelines()
    assertEquals(testGuidelines, retrievedGuidelines)
  }
}

/**
 * Tests for the getGuidelinesStates method
 */
@RunWith(JUnit4::class)
class GetGuidelinesStatesTest : BaseGuidelinesServiceTest() {
  @Test
  fun testGetGuidelinesStatesWithEmptyGuidelines() {
    // Test with empty guidelines (default state after setUp)
    val guidelinesStates = guidelinesService.getGuidelinesStates()

    // Verify the state
    assertNotNull(guidelinesStates)
    assertNotNull(guidelinesStates.userGuidelines)
    assertFalse(guidelinesStates.userGuidelines.enabled)
    // We can test that the content is empty
    assertEquals("", guidelinesStates.userGuidelines.contents)
  }

  @Test
  fun testGetGuidelinesStatesWithNonEmptyGuidelines() {
    // Set up non-empty guidelines
    val testGuidelines = "Test guidelines content"
    guidelinesService.updateUserGuidelines(testGuidelines)

    // Get the guidelines states
    val guidelinesStates = guidelinesService.getGuidelinesStates()

    // Verify the state
    assertNotNull(guidelinesStates)
    assertNotNull(guidelinesStates.userGuidelines)
    assertTrue(guidelinesStates.userGuidelines.enabled)
    assertEquals(testGuidelines, guidelinesStates.userGuidelines.contents)
  }

  @Test
  fun testGetGuidelinesStatesWithOverLimitGuidelines() {
    // Set up a small length limit using FeatureFlagsTestUtil
    val smallLimit = 10
    setupFeatureFlags(smallLimit)

    // Set up guidelines that exceed the limit
    val longGuidelines = "This is a long guideline that exceeds the limit"
    guidelinesService.updateUserGuidelines(longGuidelines)

    // Get the guidelines states
    val guidelinesStates = guidelinesService.getGuidelinesStates()

    // Verify the state
    assertNotNull(guidelinesStates)
    assertNotNull(guidelinesStates.userGuidelines)
    assertTrue(guidelinesStates.userGuidelines.enabled)
    assertTrue(guidelinesStates.userGuidelines.overLimit)
    assertEquals(longGuidelines, guidelinesStates.userGuidelines.contents)
  }

  /**
   * Helper method to set up feature flags for testing
   * Uses FeatureFlagsTestUtil to set up the feature flags with a specific user guidelines length limit
   */
  private fun setupFeatureFlags(userGuidelinesLengthLimit: Int) {
    // Create feature flags with the specified user guidelines length limit
    val featureFlags =
      mapOf(
        "enable_guidelines" to true,
        "intellij_enable_user_guidelines" to true,
        "user_guidelines_length_limit" to userGuidelinesLengthLimit,
        "workspace_guidelines_length_limit" to userGuidelinesLengthLimit,
      )

    // Use FeatureFlagsTestUtil to set up the feature flags
    FeatureFlagsTestUtil.setupFeatureFlags(testRootDisposable, featureFlags)

    // Set up the plugin version provider
    application.registerOrReplaceServiceInstance(
      PluginVersionProvider::class.java,
      object : PluginVersionProvider {
        override fun currentPluginVersion(): String = "0.0.1"

        override fun isBeta(): Boolean = false
      },
      testRootDisposable,
    )

    // Verify that the feature flags were set correctly
    val actualLimit = FeatureFlagManager.instance.getUserGuidelinesLengthLimit()
    assertEquals(
      "Feature flag for user guidelines length limit should be set correctly",
      userGuidelinesLengthLimit,
      actualLimit,
    )
  }
}

/**
 * Tests for the webview notification functionality in updateUserGuidelines method
 */
@RunWith(JUnit4::class)
class UpdateUserGuidelinesWebviewNotificationTest : BaseGuidelinesServiceTest() {
  private val capturedMessages = mutableListOf<Message>()

  override fun setUp() {
    super.setUp()

    // Clear any previous messages
    capturedMessages.clear()

    // Subscribe to the message bus to capture messages
    project.messageBus.connect(testRootDisposable).subscribe(
      ChatWebviewMessageBus.CHAT_WEBVIEW_MESSAGE_TOPIC,
      object : ChatWebviewMessageBus {
        override fun postMessageToWebview(message: Message) {
          capturedMessages.add(message)
        }

        override fun reloadIntellijStyles() {
          // Not needed for tests
        }
      },
    )
  }

  @Test
  fun testUpdateUserGuidelinesNotifiesWebview() {
    // Clear any previous messages
    capturedMessages.clear()

    // Test data
    val testGuidelines = "Test guidelines content"

    // Call the method being tested
    guidelinesService.updateUserGuidelines(testGuidelines)

    // Get the message sent to the webview
    val message = capturedMessages.lastOrNull()
    assertNotNull("A message should have been sent to the webview", message)

    // Verify the message is an UpdateGuidelinesStateRequest
    assertTrue("Message should be an UpdateGuidelinesStateRequest", message is UpdateGuidelinesStateRequest)

    // Verify the message contains the correct guidelines content
    val request = message as UpdateGuidelinesStateRequest
    assertEquals(testGuidelines, request.data.userGuidelines.contents)
    assertTrue(request.data.userGuidelines.enabled)

    // Verify the guidelines were stored
    val retrievedGuidelines = guidelinesService.getUserGuidelines()
    assertEquals(testGuidelines, retrievedGuidelines)
  }

  @Test
  fun testUpdateUserGuidelinesWithEmptyContent() {
    // Clear any previous messages
    capturedMessages.clear()

    // Test with empty guidelines
    val emptyGuidelines = ""

    // Call the method being tested
    guidelinesService.updateUserGuidelines(emptyGuidelines)

    // Get the message sent to the webview
    val message = capturedMessages.lastOrNull()
    assertNotNull("A message should have been sent to the webview", message)

    // Verify the message is an UpdateGuidelinesStateRequest
    assertTrue("Message should be an UpdateGuidelinesStateRequest", message is UpdateGuidelinesStateRequest)

    // Verify the message contains empty guidelines content
    val request = message as UpdateGuidelinesStateRequest
    assertEquals(emptyGuidelines, request.data.userGuidelines.contents)
    assertFalse(request.data.userGuidelines.enabled)

    // Verify the guidelines were stored as empty
    val retrievedGuidelines = guidelinesService.getUserGuidelines()
    assertEquals(emptyGuidelines, retrievedGuidelines)
  }
}

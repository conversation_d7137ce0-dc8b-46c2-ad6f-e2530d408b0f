package com.augmentcode.intellij.sidecar

import com.intellij.openapi.components.service
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import io.mockk.*
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Path

@RunWith(JUnit4::class)
class SidecarServiceTest : BasePlatformTestCase() {
  private var mockNodeInstallationService: NodeInstallationService? = null

  override fun setUp() {
    super.setUp()

    // Mock NodeInstallationService.instance
    mockkObject(NodeInstallationService)
    mockNodeInstallationService = mockk<NodeInstallationService>(relaxed = true)
    every { NodeInstallationService.instance } returns mockNodeInstallationService!!

    // Fake getNodeBinary with an immediate deferred with null
    every { mockNodeInstallationService!!.getNodeBinary(any()) } answers {
      val deferred = CompletableDeferred<Path?>(null)
      deferred.complete(null)
      deferred
    }
  }

  override fun tearDown() {
    super.tearDown()
    unmockkObject(NodeInstallationService)
  }

  @Test
  fun testReadyAfterStartServer(): Unit =
    runBlocking {
      val sidecarService = project.service<SidecarService>()

      // ready() called AFTER startServer()
      sidecarService.startServer()
      val readyDeferred = sidecarService.ready()

      try {
        readyDeferred.await()
      } catch (e: CancellationException) {
        // Expected
      }
      assertEquals(SidecarState.SIDECAR_ERROR, sidecarService.state())
      assertEquals(readyDeferred.isCancelled, true)
    }

  @Test
  fun testReadyBeforeStartServer(): Unit =
    runBlocking {
      val sidecarService = project.service<SidecarService>()

      // ready() called BEFORE startServer()
      val readyDeferred = sidecarService.ready()
      sidecarService.startServer()

      try {
        readyDeferred.await()
      } catch (e: CancellationException) {
        // Expected
      }
      assertEquals(SidecarState.SIDECAR_ERROR, sidecarService.state())
      assertEquals(readyDeferred.isCancelled, true)
    }

// TODO (mattgaunt): The test suite is slow to run and I can't determine why this test seems to stall CI.
//
//  @Test
//  fun testDisposeService(): Unit =
//    runBlocking {
//      val sidecarService = myFixture.project.service<SidecarService>()
//
//      try {
//        // Make it seem as though the project is disposed
//        mockkObject(myFixture.project)
//        every { myFixture.project.isDisposed } returns true
//
//        // THe message bus is not available when the project is disposed
//        mockkObject(SidecarMessageBus)
//        every { SidecarMessageBus.syncPublisher(any()) } throws IllegalStateException("Project is disposed")
//
//        sidecarService.dispose()
//        assertEquals(SidecarState.SIDECAR_STOPPED, sidecarService.state())
//      } finally {
//        unmockkObject(myFixture.project)
//        unmockkObject(SidecarMessageBus)
//      }
//    }
}

package com.augmentcode.intellij.webviews.settings

import com.augmentcode.intellij.guidelines.GuidelinesService
import com.google.gson.GsonBuilder
import com.google.gson.JsonParser
import com.intellij.openapi.components.service
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

/**
 * Base class for SettingsMessagingService tests with common setup
 */
abstract class BaseSettingsMessagingServiceTest : BasePlatformTestCase() {
  protected lateinit var settingsMessagingService: SettingsMessagingService
  protected lateinit var settingsService: AugmentSettingsWebviewService

  override fun setUp() {
    super.setUp()

    // Create the real settings service with the test scope
    settingsService = AugmentSettingsWebviewService.getInstance(project)

    // Create the messaging service with the test scope
    settingsMessagingService = SettingsMessagingService.getInstance(project)
  }

  override fun tearDown() {
    // Clean up after each test
    val guidelinesService = project.service<GuidelinesService>()
    guidelinesService.updateUserGuidelines("")
    super.tearDown()
  }
}

/**
 * Tests for handling UpdateUserGuidelinesRequest messages
 */
@RunWith(JUnit4::class)
class UpdateUserGuidelinesRequestTest : BaseSettingsMessagingServiceTest() {
  /**
   * Test that the processMessageFromWebview method correctly handles UpdateUserGuidelinesRequest messages
   */
  @Test
  fun testProcessMessageFromWebviewWithUpdateUserGuidelinesRequest() =
    runBlocking {
      // Test data
      val testData = "Test guidelines content"

      // Create a JSON request string
      val requestJson =
        """
        {
          "type":"async-wrapper",
          "requestId":"test-123",
          "error":null,
          "baseMsg": {
            "type": "update-user-guidelines",
            "data": "$testData"
          }
        }
        """.trimIndent()

      // Call the method being tested
      val responses = settingsMessagingService.processMessageFromWebview(requestJson).toList()

      // Verify the response
      assertEquals(1, responses.size)
      val responseJson = responses.first()
      assertTrue(
        "Response should contain success indicator",
        responseJson.contains("\"error\":\"\""),
      )

      // Verify that the guidelines were updated
      val guidelinesService = project.service<GuidelinesService>()
      val updatedGuidelines = guidelinesService.getUserGuidelines()
      assertEquals(
        "The guidelines should be updated with the request data",
        testData,
        updatedGuidelines,
      )
    }

  /**
   * Test that the processMessageFromWebview method correctly handles UpdateUserGuidelinesRequest messages with empty content
   */
  @Test
  fun testProcessMessageFromWebviewWithEmptyUpdateUserGuidelinesRequest() =
    runBlocking {
      // First set some non-empty guidelines
      val initialGuidelines = "Initial guidelines content"
      val guidelinesService = project.service<GuidelinesService>()
      guidelinesService.updateUserGuidelines(initialGuidelines)

      // Create a JSON request string with empty data
      val requestJson =
        """
        {
          "type":"async-wrapper",
          "requestId":"test-123",
          "error":null,
          "baseMsg": {
            "type": "update-user-guidelines",
            "data": ""
          }
        }
        """.trimIndent()

      // Call the method being tested
      val responses = settingsMessagingService.processMessageFromWebview(requestJson).toList()

      // Verify the response
      assertEquals(1, responses.size)
      val responseJson = responses.first()
      assertTrue(
        "Response should contain success indicator",
        responseJson.contains("\"error\":\"\""),
      )

      // Verify that the guidelines were updated to be empty
      val updatedGuidelines = guidelinesService.getUserGuidelines()
      assertEquals(
        "The guidelines should be updated to be empty",
        "",
        updatedGuidelines,
      )
    }
}

/**
 * Utility function to format JSON strings for better readability in test assertions
 */
internal fun String.toPrettyJson(): String {
  val element = JsonParser.parseString(this)
  val gson = GsonBuilder().setPrettyPrinting().create()
  return gson.toJson(element)
}

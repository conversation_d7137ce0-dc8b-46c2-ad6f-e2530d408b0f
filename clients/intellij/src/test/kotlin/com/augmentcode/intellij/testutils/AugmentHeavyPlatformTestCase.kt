package com.augmentcode.intellij.testutils

import com.intellij.testFramework.HeavyPlatformTestCase

abstract class AugmentHeavyPlatformTestCase : HeavyPlatformTestCase() {
  private lateinit var augmentHelpers: AugmentTestHelpers

  override fun setUp() {
    super.setUp()
    augmentHelpers = AugmentTestHelpers(myProject, testRootDisposable)
    augmentHelpers.setUp()
  }

  override fun tearDown() {
    augmentHelpers.tearDown()

    super.tearDown()

    AugmentTestHelpers.cleanup()
  }

  protected fun augmentHelpers() = augmentHelpers
}

package com.augmentcode.intellij.sidecar.clientinterfaces

import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.testFramework.PsiTestUtil
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Paths

@RunWith(JUnit4::class)
class ClientWorkspacesTest : AugmentHeavyPlatformTestCase() {
  override fun tearDown() {
    PsiTestUtil.removeAllRoots(module, null)
    super.tearDown()
  }

  @Test
  fun testReadAbsolutePathFileFromProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary file
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary file
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromProjectSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromProjectSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromGitRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromGitRoot() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Since the file is nested in a subdirectory, not the root of the workspace, we can't find it
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromGitRootSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromGitRootSubDirectory() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadAbsolutePathFileFromGitRootOutsideProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "example/subdir/file.txt"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(wantFileName)
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)

    // IMPORTANT: We register the subdirectory as the content root, not the root of the temp dir.
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!.findChild("example")!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(file.absolutePath)
    assertNotNull(result.qualifiedPath)
    assertEquals(wantFileName, result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testReadRelativePathFileFromGitRootOutsideProjectRoot() {
    val wantContent = "test content"
    val wantFileName = "subdir/file.txt"
    val workspaceSubDirectory = "example"

    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Make git file
    val gitFile = tempDir.resolve(".git/example")
    gitFile.parentFile.mkdirs()
    gitFile.writeText("git file")

    // Make file we want to read
    val file = tempDir.resolve(Paths.get(workspaceSubDirectory, wantFileName).toString())
    file.parentFile.mkdirs()
    file.writeText(wantContent)

    // Register the file with the LocalFileSystem (AugmentRoot checks if the file is part of the projects fileIndex)
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)

    // IMPORTANT: We register the subdirectory as the content root, not the root of the temp dir.
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!.findChild(workspaceSubDirectory)!!)

    // Test reading the file
    val result = ClientWorkspaces(project).readFile(wantFileName)
    assertNotNull(result.qualifiedPath)
    assertEquals(Paths.get(workspaceSubDirectory, wantFileName).toString(), result.qualifiedPath?.relPath)
    assertEquals(tempDir.path, result.qualifiedPath?.rootPath)
    assertEquals(wantContent, result.contents)
  }

  @Test
  fun testListDirectoryBasic() {
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create some files and directories
    tempDir.resolve("file1.txt").writeText("content1")
    tempDir.resolve("file2.txt").writeText("content2")
    tempDir.resolve("subdir1").mkdir()
    tempDir.resolve("subdir1/nested.txt").writeText("nested content")
    tempDir.resolve("subdir2").mkdir()
    tempDir.resolve("subdir2/another.txt").writeText("another content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)
    assertTrue("Should have entries", result.entries.isNotEmpty())

    // Check that we have the expected files and directories
    val expectedEntries = setOf("file1.txt", "file2.txt", "subdir1", "subdir2", "subdir1/nested.txt", "subdir2/another.txt")
    assertEquals("Should have all expected entries", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithDepthLimit() {
    // Create a temporary directory structure with multiple levels
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create nested structure: root -> level1 -> level2 -> level3
    tempDir.resolve("root_file.txt").writeText("root content")
    tempDir.resolve("level1").mkdir()
    tempDir.resolve("level1/level1_file.txt").writeText("level1 content")
    tempDir.resolve("level1/level2").mkdir()
    tempDir.resolve("level1/level2/level2_file.txt").writeText("level2 content")
    tempDir.resolve("level1/level2/level3").mkdir()
    tempDir.resolve("level1/level2/level3/level3_file.txt").writeText("level3 content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test with depth 2 (should only see root level and one level down)
    val result1 = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, depth = 2)
    assertNull("Should not have error message", result1.errorMessage)
    val expectedDepth1 = setOf("root_file.txt", "level1", "level1/level1_file.txt", "level1/level2")
    assertEquals("Should respect depth limit of 2", expectedDepth1, result1.entries.toSet())

    // Test with depth 1 (should only see root level)
    val result0 = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, depth = 1)
    assertNull("Should not have error message", result0.errorMessage)
    val expectedDepth0 = setOf("root_file.txt", "level1")
    assertEquals("Should respect depth limit of 1", expectedDepth0, result0.entries.toSet())
  }

  @Test
  fun testListDirectoryWithHiddenFiles() {
    // Create a temporary directory structure with hidden files
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create regular and hidden files
    tempDir.resolve("visible.txt").writeText("visible content")
    tempDir.resolve(".hidden.txt").writeText("hidden content")
    tempDir.resolve(".hiddendir").mkdir()
    tempDir.resolve(".hiddendir/nested.txt").writeText("nested in hidden")
    tempDir.resolve("normaldir").mkdir()
    tempDir.resolve("normaldir/.hidden_nested.txt").writeText("hidden nested")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test without showing hidden files (default)
    val resultNoHidden = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, showHidden = false)
    assertNull("Should not have error message", resultNoHidden.errorMessage)
    val expectedNoHidden = setOf("visible.txt", "normaldir")
    assertEquals("Should not include hidden files", expectedNoHidden, resultNoHidden.entries.toSet())

    // Test with showing hidden files
    val resultWithHidden = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, showHidden = true)
    assertNull("Should not have error message", resultWithHidden.errorMessage)
    val expectedWithHidden =
      setOf(
        "visible.txt",
        ".hidden.txt",
        ".hiddendir",
        "normaldir",
        ".hiddendir/nested.txt",
        "normaldir/.hidden_nested.txt",
      )
    assertEquals("Should include hidden files", expectedWithHidden, resultWithHidden.entries.toSet())
  }

  @Test
  fun testListDirectoryRelativePath() {
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create some files
    tempDir.resolve("file1.txt").writeText("content1")
    tempDir.resolve("subdir").mkdir()
    tempDir.resolve("subdir/file2.txt").writeText("content2")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing with relative path (empty string should list the root)
    val result = ClientWorkspaces(project).listDirectory("")
    assertNull("Should not have error message", result.errorMessage)
    assertTrue("Should have entries", result.entries.isNotEmpty())

    val expectedEntries = setOf("file1.txt", "subdir", "subdir/file2.txt")
    assertEquals("Should have all expected entries", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryNonExistentPath() {
    // Test listing a directory that doesn't exist
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create some files
    tempDir.resolve("file1.txt").writeText("content1")
    tempDir.resolve("subdir").mkdir()
    tempDir.resolve("subdir/file2.txt").writeText("content2")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    val result = ClientWorkspaces(project).listDirectory("non-existent/path")
    assertNotNull("Should have error message", result.errorMessage)
    assertEquals("Should have specific error message", "Directory not found", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectoryFileInsteadOfDirectory() {
    // Create a temporary file (not directory)
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)
    val file = tempDir.resolve("file.txt")
    file.writeText("content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing a file instead of directory
    val result = ClientWorkspaces(project).listDirectory(file.absolutePath)
    assertNotNull("Should have error message", result.errorMessage)
    assertEquals("Should have specific error message", "Path is not a directory", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectoryOutsideWorkspace() {
    // Create a temporary directory that's not registered as a content root
    val tempDir = FileUtil.createTempDirectory("client-workspaces-outside", null)
    tempDir.resolve("file.txt").writeText("content")

    // Don't register this directory with the project - it should be outside workspace

    // Test listing directory outside workspace
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNotNull("Should have error message", result.errorMessage)
    assertEquals("Should have specific error message", "Path is outside the workspace", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectoryEmptyDirectory() {
    // Create an empty temporary directory
    val tempDir = FileUtil.createTempDirectory("client-workspaces-empty", null)

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing empty directory
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)
    assertTrue("Should have empty entries", result.entries.isEmpty())
  }

  @Test
  fun testListDirectorySubdirectoryPath() {
    // Create a temporary directory structure
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create subdirectory with files
    tempDir.resolve("subdir").mkdir()
    tempDir.resolve("subdir/file1.txt").writeText("content1")
    tempDir.resolve("subdir/file2.txt").writeText("content2")
    tempDir.resolve("subdir/nested").mkdir()
    tempDir.resolve("subdir/nested/deep.txt").writeText("deep content")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the subdirectory
    val subdirPath = tempDir.resolve("subdir").absolutePath
    val result = ClientWorkspaces(project).listDirectory(subdirPath)
    assertNull("Should not have error message", result.errorMessage)

    val expectedEntries = setOf("file1.txt", "file2.txt", "nested", "nested/deep.txt")
    assertEquals("Should have all expected entries", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectorySortedOutput() {
    // Create a temporary directory with files in non-alphabetical order
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create files in a specific order to test sorting
    tempDir.resolve("zebra.txt").writeText("z")
    tempDir.resolve("alpha.txt").writeText("a")
    tempDir.resolve("beta").mkdir()
    tempDir.resolve("beta/gamma.txt").writeText("g")

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Check that results are sorted
    val expectedSorted = listOf("alpha.txt", "beta", "beta/gamma.txt", "zebra.txt")
    assertEquals("Should have sorted entries", expectedSorted, result.entries)
  }

  @Test
  fun testListDirectoryWithSymlinks() {
    // Create a temporary directory structure with symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create regular files and directories
    tempDir.resolve("regular.txt").writeText("regular content")
    tempDir.resolve("targetdir").mkdir()
    tempDir.resolve("targetdir/target.txt").writeText("target content")

    // Create symlinks (using Java NIO for cross-platform compatibility)
    val symlinkFile = tempDir.resolve("symlink_to_file.txt").toPath()
    val symlinkDir = tempDir.resolve("symlink_to_dir").toPath()

    try {
      // Create symlink to file
      java.nio.file.Files.createSymbolicLink(symlinkFile, tempDir.resolve("regular.txt").toPath())

      // Create symlink to directory
      java.nio.file.Files.createSymbolicLink(symlinkDir, tempDir.resolve("targetdir").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - symlinks should be skipped
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files and directories, not symlinks
    val expectedEntries = setOf("regular.txt", "targetdir", "targetdir/target.txt")
    assertEquals("Should skip symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithRecursiveSymlinks() {
    // Create a temporary directory structure with recursive symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create a directory structure
    tempDir.resolve("dir1").mkdir()
    tempDir.resolve("dir1/file1.txt").writeText("file1 content")
    tempDir.resolve("dir1/dir2").mkdir()
    tempDir.resolve("dir1/dir2/file2.txt").writeText("file2 content")

    // Create recursive symlinks
    val symlinkToParent = tempDir.resolve("dir1/dir2/link_to_parent").toPath()
    val symlinkToRoot = tempDir.resolve("dir1/link_to_root").toPath()

    try {
      // Create symlink from dir2 back to dir1 (parent)
      java.nio.file.Files.createSymbolicLink(symlinkToParent, tempDir.resolve("dir1").toPath())

      // Create symlink from dir1 back to root
      java.nio.file.Files.createSymbolicLink(symlinkToRoot, tempDir.toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping recursive symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - should not follow recursive symlinks
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath, depth = 3)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files and directories, not symlinks
    // This prevents infinite recursion
    val expectedEntries = setOf("dir1", "dir1/file1.txt", "dir1/dir2", "dir1/dir2/file2.txt")
    assertEquals("Should skip recursive symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithCircularSymlinks() {
    // Create a temporary directory structure with circular symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create directories
    tempDir.resolve("dirA").mkdir()
    tempDir.resolve("dirB").mkdir()
    tempDir.resolve("dirA/fileA.txt").writeText("fileA content")
    tempDir.resolve("dirB/fileB.txt").writeText("fileB content")

    // Create circular symlinks: dirA -> dirB, dirB -> dirA
    val symlinkAtoB = tempDir.resolve("dirA/link_to_B").toPath()
    val symlinkBtoA = tempDir.resolve("dirB/link_to_A").toPath()

    try {
      java.nio.file.Files.createSymbolicLink(symlinkAtoB, tempDir.resolve("dirB").toPath())
      java.nio.file.Files.createSymbolicLink(symlinkBtoA, tempDir.resolve("dirA").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping circular symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - should not follow circular symlinks
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files and directories, not symlinks
    // This prevents infinite loops
    val expectedEntries = setOf("dirA", "dirB", "dirA/fileA.txt", "dirB/fileB.txt")
    assertEquals("Should skip circular symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testListDirectoryWithBrokenSymlinks() {
    // Create a temporary directory structure with broken symlinks
    val tempDir = FileUtil.createTempDirectory("client-workspaces", null)

    // Create regular files
    tempDir.resolve("regular.txt").writeText("regular content")

    // Create broken symlinks (pointing to non-existent targets)
    val brokenSymlinkFile = tempDir.resolve("broken_link_file").toPath()
    val brokenSymlinkDir = tempDir.resolve("broken_link_dir").toPath()

    try {
      // Create symlinks to non-existent targets
      java.nio.file.Files.createSymbolicLink(brokenSymlinkFile, tempDir.resolve("nonexistent.txt").toPath())
      java.nio.file.Files.createSymbolicLink(brokenSymlinkDir, tempDir.resolve("nonexistent_dir").toPath())
    } catch (e: Exception) {
      // Skip test if symlinks are not supported on this platform
      println("Skipping broken symlink test - symlinks not supported: ${e.message}")
      return
    }

    // Register with the LocalFileSystem
    val tempDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for temp directory", tempDirVirtualFile)
    PsiTestUtil.addContentRoot(module, tempDirVirtualFile!!)

    // Test listing the directory - should skip broken symlinks
    val result = ClientWorkspaces(project).listDirectory(tempDir.absolutePath)
    assertNull("Should not have error message", result.errorMessage)

    // Should only include regular files, not broken symlinks
    val expectedEntries = setOf("regular.txt")
    assertEquals("Should skip broken symlinks", expectedEntries, result.entries.toSet())
  }

  @Test
  fun testSearchForRelativeFileWithOverlappingPath() {
    val wantContent = "test content"
    val wantFileName = "main.py"

    // Create a temporary directory structure that simulates the scenario:
    // root = /tmp/augment/clients
    // path = clients/main.py
    // should find file at /tmp/augment/clients/main.py
    val tempDir = FileUtil.createTempDirectory("augment", null)
    val clientsDir = tempDir.resolve("clients")
    clientsDir.mkdir()
    val file = clientsDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the clients directory as the content root
    val clientsDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(clientsDir)
    assertNotNull("Failed to find virtual file for clients directory", clientsDirVirtualFile)
    PsiTestUtil.addContentRoot(module, clientsDirVirtualFile!!)

    // Test reading the file using overlapping path "clients/main.py"
    // This should detect that "clients" overlaps with the root path and use "main.py" as the relative path
    val result = ClientWorkspaces(project).readFile("clients/$wantFileName")
    assertNotNull("Should find file with overlapping path", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", clientsDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithMultipleOverlappingSegments() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a nested directory structure:
    // root = /tmp/project/src/main
    // path = src/main/file.txt
    // should find file at /tmp/project/src/main/file.txt
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val mainDir = srcDir.resolve("main")
    mainDir.mkdir()
    val file = mainDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the main directory as the content root
    val mainDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(mainDir)
    assertNotNull("Failed to find virtual file for main directory", mainDirVirtualFile)
    PsiTestUtil.addContentRoot(module, mainDirVirtualFile!!)

    // Test reading the file using overlapping path "src/main/file.txt"
    // This should detect that "src/main" overlaps with the root path and use "file.txt" as the relative path
    val result = ClientWorkspaces(project).readFile("src/main/$wantFileName")
    assertNotNull("Should find file with multiple overlapping segments", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", mainDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithPartialOverlap() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure where only part of the path overlaps:
    // root = /tmp/myproject/src
    // path = project/src/file.txt (only "src" overlaps)
    // should NOT find the file because "project" doesn't match "myproject"
    val tempDir = FileUtil.createTempDirectory("myproject", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val file = srcDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the src directory as the content root
    val srcDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(srcDir)
    assertNotNull("Failed to find virtual file for src directory", srcDirVirtualFile)
    PsiTestUtil.addContentRoot(module, srcDirVirtualFile!!)

    // Test reading the file using non-overlapping path "project/src/file.txt"
    // This should NOT find the file because "project" doesn't match "myproject"
    val result = ClientWorkspaces(project).readFile("project/src/$wantFileName")
    assertNull("Should not find file with partial overlap", result.qualifiedPath)
    assertNull("Should not have content", result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithExactOverlap() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure where the path exactly matches the root name:
    // root = /tmp/workspace
    // path = workspace/file.txt
    // should find file at /tmp/workspace/file.txt
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the workspace directory as the content root
    val workspaceDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for workspace directory", workspaceDirVirtualFile)
    PsiTestUtil.addContentRoot(module, workspaceDirVirtualFile!!)

    // Test reading the file using overlapping path "workspace/file.txt"
    // This should detect that "workspace" overlaps with the root path and use "file.txt" as the relative path
    val result = ClientWorkspaces(project).readFile("workspace/$wantFileName")
    assertNotNull("Should find file with exact overlap", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", workspaceDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithNoOverlap() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure with no overlap:
    // root = /tmp/workspace
    // path = different/file.txt (no overlap)
    // should NOT find the file
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the workspace directory as the content root
    val workspaceDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for workspace directory", workspaceDirVirtualFile)
    PsiTestUtil.addContentRoot(module, workspaceDirVirtualFile!!)

    // Test reading the file using non-overlapping path "different/file.txt"
    // This should NOT find the file
    val result = ClientWorkspaces(project).readFile("different/$wantFileName")
    assertNull("Should not find file with no overlap", result.qualifiedPath)
    assertNull("Should not have content", result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithNestedOverlap() {
    val wantContent = "test content"
    val wantFileName = "nested.txt"

    // Create a nested directory structure:
    // root = /tmp/project/src/main/java
    // path = main/java/com/example/nested.txt
    // should find file at /tmp/project/src/main/java/com/example/nested.txt
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val mainDir = srcDir.resolve("main")
    mainDir.mkdir()
    val javaDir = mainDir.resolve("java")
    javaDir.mkdir()
    val comDir = javaDir.resolve("com")
    comDir.mkdir()
    val exampleDir = comDir.resolve("example")
    exampleDir.mkdir()
    val file = exampleDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the java directory as the content root
    val javaDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(javaDir)
    assertNotNull("Failed to find virtual file for java directory", javaDirVirtualFile)
    PsiTestUtil.addContentRoot(module, javaDirVirtualFile!!)

    // Test reading the file using overlapping path "main/java/com/example/nested.txt"
    // This should detect that "main/java" overlaps with the root path and use "com/example/nested.txt" as the relative path
    val result = ClientWorkspaces(project).readFile("main/java/com/example/$wantFileName")
    assertNotNull("Should find file with nested overlap", result.qualifiedPath)
    assertEquals("Should have correct relative path", "com/example/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", javaDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testGetPathInfoWithOverlappingPath() {
    // Create a temporary directory structure that simulates the scenario:
    // root = /tmp/workspace/src
    // path = src/file.txt
    // should find file at /tmp/workspace/src/file.txt
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val file = srcDir.resolve("file.txt")
    file.writeText("test content")

    // Register the src directory as the content root
    val srcDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(srcDir)
    assertNotNull("Failed to find virtual file for src directory", srcDirVirtualFile)
    PsiTestUtil.addContentRoot(module, srcDirVirtualFile!!)

    // Test getting path info using overlapping path "src/file.txt"
    // This should detect that "src" overlaps with the root path and use "file.txt" as the relative path
    val result = ClientWorkspaces(project).getPathInfo("src/file.txt")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "file.txt", result.filepath?.relPath)
    assertEquals("Should have correct root path", srcDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoWithMultipleOverlappingSegments() {
    // Create a nested directory structure:
    // root = /tmp/project/src/main/resources
    // path = src/main/resources/config.properties
    // should find file at /tmp/project/src/main/resources/config.properties
    val tempDir = FileUtil.createTempDirectory("project", null)
    val srcDir = tempDir.resolve("src")
    srcDir.mkdir()
    val mainDir = srcDir.resolve("main")
    mainDir.mkdir()
    val resourcesDir = mainDir.resolve("resources")
    resourcesDir.mkdir()
    val file = resourcesDir.resolve("config.properties")
    file.writeText("key=value")

    // Register the resources directory as the content root
    val resourcesDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(resourcesDir)
    assertNotNull("Failed to find virtual file for resources directory", resourcesDirVirtualFile)
    PsiTestUtil.addContentRoot(module, resourcesDirVirtualFile!!)

    // Test getting path info using overlapping path "src/main/resources/config.properties"
    // This should detect that "src/main/resources" overlaps with the root path and use "config.properties" as the relative path
    val result = ClientWorkspaces(project).getPathInfo("src/main/resources/config.properties")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a file", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.FILE, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "config.properties", result.filepath?.relPath)
    assertEquals("Should have correct root path", resourcesDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testGetPathInfoWithOverlappingDirectoryPath() {
    // Create a directory structure:
    // root = /tmp/workspace/docs
    // path = docs/api
    // should find directory at /tmp/workspace/docs/api
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val docsDir = tempDir.resolve("docs")
    docsDir.mkdir()
    val apiDir = docsDir.resolve("api")
    apiDir.mkdir()

    // Register the docs directory as the content root
    val docsDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(docsDir)
    assertNotNull("Failed to find virtual file for docs directory", docsDirVirtualFile)
    PsiTestUtil.addContentRoot(module, docsDirVirtualFile!!)

    // Test getting path info using overlapping path "docs/api"
    // This should detect that "docs" overlaps with the root path and use "api" as the relative path
    val result = ClientWorkspaces(project).getPathInfo("docs/api")
    assertEquals("Should exist", true, result.exists)
    assertEquals("Should be a directory", com.augmentcode.sidecar.rpc.clientInterfaces.FileType.DIRECTORY, result.type)
    assertNotNull("Should have qualified path", result.filepath)
    assertEquals("Should have correct relative path", "api", result.filepath?.relPath)
    assertEquals("Should have correct root path", docsDirVirtualFile.path, result.filepath?.rootPath)
  }

  @Test
  fun testSearchForRelativeFileWithEmptySegments() {
    val wantContent = "test content"
    val wantFileName = "file.txt"

    // Create a directory structure:
    // root = /tmp/workspace
    // path = workspace//file.txt (double slash)
    // should find file at /tmp/workspace/file.txt
    val tempDir = FileUtil.createTempDirectory("workspace", null)
    val file = tempDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the workspace directory as the content root
    val workspaceDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(tempDir)
    assertNotNull("Failed to find virtual file for workspace directory", workspaceDirVirtualFile)
    PsiTestUtil.addContentRoot(module, workspaceDirVirtualFile!!)

    // Test reading the file using path with empty segments (double slash)
    // Our implementation should filter out empty segments
    val result = ClientWorkspaces(project).readFile("workspace//$wantFileName")
    assertNotNull("Should find file with empty segments", result.qualifiedPath)
    assertEquals("Should have correct relative path", wantFileName, result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", workspaceDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }

  @Test
  fun testSearchForRelativeFileWithComplexPath() {
    val wantContent = "test content"
    val wantFileName = "Component.tsx"

    // Create a complex directory structure similar to a real project:
    // root = /tmp/myapp/frontend/src/components
    // path = frontend/src/components/ui/Button/Component.tsx
    // should find file at /tmp/myapp/frontend/src/components/ui/Button/Component.tsx
    val tempDir = FileUtil.createTempDirectory("myapp", null)
    val frontendDir = tempDir.resolve("frontend")
    frontendDir.mkdir()
    val srcDir = frontendDir.resolve("src")
    srcDir.mkdir()
    val componentsDir = srcDir.resolve("components")
    componentsDir.mkdir()
    val uiDir = componentsDir.resolve("ui")
    uiDir.mkdir()
    val buttonDir = uiDir.resolve("Button")
    buttonDir.mkdir()
    val file = buttonDir.resolve(wantFileName)
    file.writeText(wantContent)

    // Register the components directory as the content root
    val componentsDirVirtualFile = LocalFileSystem.getInstance().refreshAndFindFileByIoFile(componentsDir)
    assertNotNull("Failed to find virtual file for components directory", componentsDirVirtualFile)
    PsiTestUtil.addContentRoot(module, componentsDirVirtualFile!!)

    // Test reading the file using overlapping path "frontend/src/components/ui/Button/Component.tsx"
    // This should detect that "frontend/src/components" overlaps with the root path and use "ui/Button/Component.tsx" as the relative path
    val result = ClientWorkspaces(project).readFile("frontend/src/components/ui/Button/$wantFileName")
    assertNotNull("Should find file with complex overlapping path", result.qualifiedPath)
    assertEquals("Should have correct relative path", "ui/Button/$wantFileName", result.qualifiedPath?.relPath)
    assertEquals("Should have correct root path", componentsDirVirtualFile.path, result.qualifiedPath?.rootPath)
    assertEquals("Should have correct content", wantContent, result.contents)
  }
}

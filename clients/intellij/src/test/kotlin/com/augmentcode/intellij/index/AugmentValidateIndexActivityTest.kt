package com.augmentcode.intellij.index

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.testFramework.IndexingTestUtil
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking

@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentValidateIndexActivityTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/indexing"

  fun testSimple() {
    @Suppress("DEPRECATION")
    val mockAugmentAPI =
      MockAugmentAPI(
        knownBlobNames = setOf("hash(permanent.txt).v1"),
      )
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAugmentAPI,
      testRootDisposable,
    )

    val expired = myFixture.addFileToProject("expired.txt", "file that will be evicted")
    val permanent = myFixture.addFileToProject("permanent.txt", "file that will not be evicted")
    val editable = myFixture.configureByText("editable.txt", "file that we will edit\n")

    assertEquals("expired.txt", AugmentBlobStateReader.read(expired)?.relativePath)
    assertEquals("permanent.txt", AugmentBlobStateReader.read(permanent)?.relativePath)
    assertEquals("editable.txt", AugmentBlobStateReader.read(editable)?.relativePath)

    myFixture.type("a new line\n")
    assertEquals("editable.txt", AugmentBlobStateReader.read(editable)?.relativePath)

    // to emulate a freshly opened project with all
    FileDocumentManager.getInstance().saveAllDocuments()

    runWriteAction { // to force sync execution for tests
      runBlocking {
        mockAugmentAPI.resetRemotelyIndexedBlobs()
        AugmentValidateIndexActivity(augmentHelpers().createCoroutineScope(Dispatchers.Default)).validateRemotelyMissingBlobs(project)
      }
    }
    IndexingTestUtil.waitUntilIndexesAreReady(project)

    assertEquals("expired.txt", AugmentBlobStateReader.read(expired)?.relativePath)
    assertEquals("permanent.txt", AugmentBlobStateReader.read(permanent)?.relativePath)
    assertEquals("editable.txt", AugmentBlobStateReader.read(editable)?.relativePath)
  }
}

package com.augmentcode.intellij.sidecar

import com.augmentcode.intellij.sidecar.NodeInstallationService.Companion.NODE_VERSION
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import io.mockk.*
import kotlinx.coroutines.test.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Files
import java.security.MessageDigest
import java.util.concurrent.TimeUnit
import javax.net.ssl.HttpsURLConnection

@RunWith(JUnit4::class)
class NodeInstallationServiceTest : AugmentBasePlatformTestCase() {
  // Stash original checksums
  private val expectedContent = "Expected node binary"
  private val expectedBinarySha =
    MessageDigest.getInstance("SHA-256")
      .digest(expectedContent.toByteArray())
      .joinToString("") { "%02x".format(it) }

  private val expectedChecksums =
    mapOf(
      *NodeInstallationService.NODE_CHECKSUMS.keys.map {
        it to expectedBinarySha
      }.toTypedArray(),
    )

  override fun setUp() {
    super.setUp()
    // Clear out any existing node binaries
    val nodeInstallationService = NodeInstallationService.instance
    val nodeDirPath = nodeInstallationService.getPluginNodeDir()
    nodeDirPath.toFile().deleteRecursively()

    val mockConnection = mockk<HttpsURLConnection>()
    every { mockConnection.responseCode } returns 404

    urlConnectionFactory = { _ -> mockConnection }
  }

  override fun tearDown() {
    super.tearDown()

    // Clear out any existing node binaries
    val nodeInstallationService = NodeInstallationService.instance
    val nodeDirPath = nodeInstallationService.getPluginNodeDir()
    nodeDirPath.toFile().deleteRecursively()
  }

  @Test
  fun testReturnAlreadyDownloadedNode() =
    runTest {
      // Ensure we use `this` as the coroutines scope to ensure we skip delays
      val nodeInstallationService = NodeInstallationService(this)

      // Create a fake node binary for early return
      val nodeDirPath = nodeInstallationService.getPluginNodeDir()
      val nodeBinaryPath = nodeDirPath.resolve("node")
      val success = nodeBinaryPath.toFile().createNewFile()
      assertTrue(success)

      var failedDownloadCalls = 0
      val firstCall =
        nodeInstallationService.getNodeBinary {
          failedDownloadCalls++
        }
      val secondCall =
        nodeInstallationService.getNodeBinary {
          failedDownloadCalls++
        }
      assertNotSame(firstCall, secondCall)
      assertEquals(firstCall.await().toString(), nodeBinaryPath.toString())
      assertEquals(secondCall.await().toString(), nodeBinaryPath.toString())

      assert(nodeBinaryPath.toFile().exists())
      assertEquals(0, failedDownloadCalls)
      assertEquals(0, nodeInstallationService.initialDownloadCallbacks.size)
    }

  /**
   * This test ensures the following are handled:
   * 1. A non-200 response code is retried
   * 2. A failed checksum is retried
   * 3. A successful download is not retried
   */
  @Test
  fun testRetryToDownloadNode() =
    runTest {
      // Create a fake node binary for early return
      val tmpDir = Files.createTempDirectory("testRetryToDownloadNode")
      // Because we use the parent directory of where the node binary is kept to download the
      // compressed file, we need to create a nested directory so the directory
      // is clean between test runs.
      val nodeBinaryPath = tmpDir.resolve("sidecar").resolve("node")

      val mockConnection = mockk<HttpsURLConnection>()
      justRun { mockConnection.disconnect() }
      justRun { mockConnection.connectTimeout = any() }
      justRun { mockConnection.readTimeout = any() }
      justRun { mockConnection.sslSocketFactory = any() }

      var responseCodeCallCount = 0
      every { mockConnection.responseCode } answers {
        responseCodeCallCount++
        when (responseCodeCallCount) {
          1 -> 404
          2 -> 200 // Invalid checksum
          3 -> 200 // Fail extract
          4 -> 200 // Succeed
          else -> error("Unexpected responseCode request $responseCodeCallCount")
        }
      }
      every { mockConnection.responseMessage } answers {
        when (responseCodeCallCount) {
          1 -> "Injected error"
          else -> error("Unexpected responseMessage request $responseCodeCallCount")
        }
      }
      every { mockConnection.inputStream } answers {
        when (responseCodeCallCount) {
          2 -> "Invalid content - will fail checksum".byteInputStream()
          3 -> expectedContent.byteInputStream() // Fail on extraction
          4 -> expectedContent.byteInputStream() // Success on extraction
          else -> error("Unexpected request $responseCodeCallCount")
        }
      }

      urlConnectionFactory = { _ -> mockConnection }

      val mockProcess = mockk<Process>()
      every { mockProcess.waitFor(30, TimeUnit.SECONDS) } returns true
      every { mockProcess.exitValue() } answers {
        when (responseCodeCallCount) {
          3 -> 1
          4 -> {
            return@answers 0
          }
          else -> error("Unexpected request $responseCodeCallCount")
        }
      }
      every { mockProcess.destroyForcibly() } returns mockProcess
      every { mockProcess.errorStream } answers {
        when (responseCodeCallCount) {
          3 -> "Injected error".byteInputStream()
          else -> error("Unexpected request $responseCodeCallCount")
        }
      }

      val mockProcessBuilder = mockk<ProcessBuilder>()
      every { mockProcessBuilder.redirectError(ofType<ProcessBuilder.Redirect>()) } returns mockProcessBuilder
      every { mockProcessBuilder.start() } returns mockProcess

      mockkConstructor(ProcessBuilder::class)
      every {
        anyConstructed<ProcessBuilder>().redirectError(ofType<ProcessBuilder.Redirect>())
      } answers {
        if (responseCodeCallCount == 4) {
          // Create the target directory and file that would be created by the real tar command
          val (platform, architecture, _) = getPlatformInfo()

          val nodePathParent = nodeBinaryPath.parent

          // This is archiveBase/bin/node
          val outputFile = nodeBinaryPath.parent.resolve("node-$NODE_VERSION-$platform-$architecture/bin/node")

          val fullPath = nodePathParent.resolve(outputFile)
          Files.createDirectories(fullPath.parent)
          Files.createFile(fullPath)
        }

        mockProcessBuilder
      }
      every {
        anyConstructed<ProcessBuilder>().start()
      } returns mockProcess

      // Ensure we use `this` as the coroutines scope to ensure we skip delays
      val nodeInstallationService = NodeInstallationService(this)

      var initialDownloadFailed = false
      nodeInstallationService.downloadNodeBinary(
        nodeBinaryPath,
        initialNodeDownloadFailedCallback = {
          initialDownloadFailed = true
        },
        checksums = expectedChecksums,
      )

      assertTrue(initialDownloadFailed)
      assert(nodeBinaryPath.toFile().exists())
      assertEquals(4, responseCodeCallCount)
      verify(exactly = 4) { mockConnection.disconnect() }
    }

  @Test
  fun testRetryAndFailToDownloadNode() =
    runTest {
      // Ensure we use `this` as the coroutines scope to ensure we skip delays
      val nodeInstallationService = NodeInstallationService(this)

      // Create a fake node binary for early return
      val nodeDirPath = nodeInstallationService.getPluginNodeDir()
      val nodeBinaryPath = nodeDirPath.resolve("node")

      val mockConnection = mockk<HttpsURLConnection>()
      justRun { mockConnection.disconnect() }
      justRun { mockConnection.connectTimeout = any() }
      justRun { mockConnection.readTimeout = any() }
      justRun { mockConnection.sslSocketFactory = any() }

      every { mockConnection.responseCode } answers {
        404
      }
      every { mockConnection.responseMessage } answers {
        "Injected error"
      }

      urlConnectionFactory = { _ -> mockConnection }
      var initialDownloadFailedCount = 0
      val result1 =
        nodeInstallationService.getNodeBinary(
          initialNodeDownloadFailedCallback = {
            initialDownloadFailedCount++
          },
        )
      val result2 =
        nodeInstallationService.getNodeBinary(
          initialNodeDownloadFailedCallback = {
            initialDownloadFailedCount++
          },
        )
      assertEquals(result1, result2)

      assertEquals(result1.await(), null)
      assertEquals(result2.await(), null)

      assertEquals(2, initialDownloadFailedCount)
      assert(!nodeBinaryPath.toFile().exists())
      verify(exactly = 5) { mockConnection.disconnect() }
      assertEquals(0, nodeInstallationService.initialDownloadCallbacks.size)
    }
}

package com.augmentcode.intellij.testutils

import com.augmentcode.api.BatchUploadRequest
import com.augmentcode.api.CheckpointBlobsRequest
import com.augmentcode.api.FindMissingRequest
import com.augmentcode.api.ModelConfig
import com.augmentcode.intellij.api.AugmentHttpClient.Companion.createGson
import com.augmentcode.intellij.api.HttpClientProvider
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl.Companion.expectedBlobName
import com.intellij.openapi.Disposable
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.client.plugins.HttpTimeout
import io.ktor.http.Headers
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.net.URI

object HttpUtil {
  // Use different char counts to ensure text correctness
  const val PREFIX_CHAR_COUNT = 20
  const val SUFFIX_CHAR_COUNT = 40

  private val defaultMockModelConfigJSON =
    """
    {
        "default_model": "test-model",
        "models": [
            {
                "name": "test-model",
                "suggested_prefix_char_count": ${PREFIX_CHAR_COUNT},
                "suggested_suffix_char_count": ${SUFFIX_CHAR_COUNT}
            }
        ],
        "languages": [
            {
                "name": "Text",
                "vscode_name": "txt",
                "extensions": [".txt"]
            },
            {
                "name": "Go",
                "vscode_name": "go",
                "extensions": [".go"]
            },
            {
                "name": "JavaScript",
                "vscode_name": "javascript",
                "extensions": [".js", ".jsx"]
            },
            {
                "name": "Python",
                "vscode_name": "python",
                "extensions": [".py", ".pyw"]
            },
            {
                "name": "GitIgnore",
                "vscode_name": "ignore",
                "extensions": [".gitignore", ".augmentignore"]
            },
            {
                "name": "Python",
                "vscode_name": "python",
                "extensions": [".py", ".pyw"]
            },
            {
                "name": "Java",
                "vscode_name": "java",
                "extensions": [".java"]
            }
        ],
        "feature_flags": {
            "bypass_language_filter": false,
            "max_upload_size_bytes": 1000000
        },
        "user_tier": "COMMUNITY_TIER"
    }
    """.trimIndent()
  private val gson = createGson()
  val defaultMockModelConfig: ModelConfig = gson.fromJson(defaultMockModelConfigJSON, ModelConfig::class.java)

  fun respondGetModels(mockRequestHandleScope: MockRequestHandleScope) =
    mockRequestHandleScope.respond(
      content = defaultMockModelConfigJSON,
      status = HttpStatusCode.OK,
      headers = headersOf(HttpHeaders.ContentType, "application/json"),
    )

  fun respondOK(mockRequestHandleScope: MockRequestHandleScope) =
    mockRequestHandleScope.respond(
      content = "{}",
      status = HttpStatusCode.OK,
      headers = headersOf(HttpHeaders.ContentType, "application/json"),
    )

  fun mockEngineGetModels(): MockEngine {
    return MockEngine { request ->
      when (request.url.encodedPath) {
        "/get-models" -> respondGetModels(this)
        else -> error("Unexpected request to ${request.url.encodedPath}")
      }
    }
  }

  fun registerMockHttpClient(
    mockEngine: MockEngine,
    disposable: Disposable,
  ) {
    application.registerOrReplaceServiceInstance(
      HttpClientProvider::class.java,
      object : HttpClientProvider {
        override fun clientFor(uri: URI): HttpClient {
          return HttpClient(mockEngine) {
            // Install the HttpTimeout plugin that AugmentHttpClient expects
            install(HttpTimeout)
          }
        }
      },
      disposable,
    )
  }

  /**
   * Set up a mock server that mocks the behavior of the Augment server for syncing.
   * This is a useful default for many tests. If you need alternate behavior, prefer
   * to duplicate code and create your own mock server rather than building on top of this one.
   */
  fun mockServerSync(disposable: Disposable): MockEngine {
    val gson = GsonUtil.createApiGson()

    data class MockEngineState(
      var checkpointCallCount: Int = 0,
      var uploadedBlobs: MutableSet<String> = mutableSetOf(),
    )

    val state = MockEngineState()
    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/get-models" -> respondGetModels(this)
          "/batch-upload" -> {
            val requestBody = request.body.toByteArray().decodeToString()
            val uploadRequest = gson.fromJson(requestBody, BatchUploadRequest::class.java)
            state.uploadedBlobs.addAll(uploadRequest.blobs.map { expectedBlobName(it.path, it.content) })

            respond(
              content = """{"success": true}""",
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
          "/find-missing" -> {
            val requestBody = request.body.toByteArray().decodeToString()
            val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)
            val unknownBlobs = findRequest.memObjectNames.subtract(state.uploadedBlobs)

            respond(
              content = """{"unknown_memory_names":${gson.toJson(unknownBlobs)},"nonindexed_blob_names":[]}""",
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
          "/checkpoint-blobs" -> {
            val requestBody = request.body.toByteArray().decodeToString()
            // Check it doesn't throw
            gson.fromJson(requestBody, CheckpointBlobsRequest::class.java)

            state.checkpointCallCount++
            respond(
              content = """{"new_checkpoint_id":"checkpoint-${state.checkpointCallCount}"}""",
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
          else -> error("Unhandled ${request.url.encodedPath}")
        }
      }
    registerMockHttpClient(mockEngine, disposable)
    return mockEngine
  }

  /**
   * Extension function on MockRequestHandleScope to create a streaming response.
   * This preserves the exact context of the original code.
   */
  fun MockRequestHandleScope.streamRespond(
    coroutineScope: CoroutineScope,
    chunks: List<String>,
    status: HttpStatusCode = HttpStatusCode.OK,
    headers: Headers = headersOf(HttpHeaders.ContentType, "application/json"),
    delayBetweenChunksMs: Long = 0,
  ) = respond(
    content =
      ByteChannel(autoFlush = true).apply {
        coroutineScope.launch {
          try {
            for (chunk in chunks) {
              // Ensure each chunk ends with a newline
              val chunkWithNewline = if (chunk.endsWith("\n")) chunk else "$chunk\n"
              writeFully(chunkWithNewline.toByteArray())
              flush()

              if (chunks.size > 1 && delayBetweenChunksMs > 0) {
                delay(delayBetweenChunksMs)
              }
            }
            close()
          } catch (e: Exception) {
            close(e)
          }
        }
      },
    status = status,
    headers = headers,
  )
}

package com.augmentcode.intellij.utils

import com.augmentcode.rpc.FileDetails
import com.augmentcode.rpc.OpenFileRequest
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class TextRangeCalculatorTest : BasePlatformTestCase() {
  private fun createRequest(snippet: String): OpenFileRequest =
    OpenFileRequest
      .newBuilder()
      .setData(
        FileDetails
          .newBuilder()
          .setSnippet(snippet)
          .build(),
      ).build()

  private fun stripIndent(text: String): String = text.lines().joinToString("\n") { it.trim() }

  @Test
  fun `test matches with different indentation`() {
    val fileContent = """
            def calculate():
                result = x + y
                if result > 10:
                    print("Large result")
                return result
        """

    val snippet = """
            result = x + y
            if result > 10:
                print("Large result")
        """

    myFixture.configureByText("test.py", fileContent)
    val document = myFixture.editor.document

    val range = TextRangeCalculator.calculateRange(document, createRequest(snippet), project)!!

    assertEquals(stripIndent(snippet), stripIndent(document.getText(range)))
  }

  @Test
  fun `test matches single line with leading whitespace`() {
    val fileContent =
      """
      class Example:
          def method():
              return "hello world"
      """.trimIndent()

    val snippet = "    return \"hello world\""

    myFixture.configureByText("test.py", fileContent)
    val document = myFixture.editor.document

    val range = TextRangeCalculator.calculateRange(document, createRequest(snippet), project)

    assertNotNull(range)
    assertEquals(stripIndent(snippet), stripIndent(document.getText(range!!)))
  }

  @Test
  fun `test matches with multiple possible matches selects first occurrence`() {
    val fileContent =
      """
      def example():
          print("hello")
          print("world")

      def another():
          print("hello")
          print("world")
      """.trimIndent()

    val snippet =
      """
      print("hello")
      print("world")
      """.trimIndent()

    myFixture.configureByText("test.py", fileContent)
    val document = myFixture.editor.document

    val range = TextRangeCalculator.calculateRange(document, createRequest(snippet), project)

    assertNotNull(range)
    assertEquals(stripIndent(snippet), stripIndent(document.getText(range!!)))
    assertTrue(range.startOffset < document.textLength / 2) // Ensures we matched the first occurrence
  }

  @Test
  fun `test no match returns null`() {
    val fileContent =
      """
      def example():
          x = 1
          y = 2
      """.trimIndent()

    val snippet =
      """
      a = 1
      b = 2
      """.trimIndent()

    myFixture.configureByText("test.py", fileContent)
    val document = myFixture.editor.document

    val range = TextRangeCalculator.calculateRange(document, createRequest(snippet), project)

    assertNull(range)
  }

  @Test
  fun `test matches with different line endings`() {
    val fileContent = "def example():\r\n    x = 1\r\n    y = 2   ."
    val snippet = "x = 1\ny = 2"

    myFixture.configureByText("test.py", fileContent)
    val document = myFixture.editor.document

    val range = TextRangeCalculator.calculateRange(document, createRequest(snippet), project)

    assertNotNull(range)
    assertEquals(stripIndent(snippet), stripIndent(document.getText(range!!)))
  }
}

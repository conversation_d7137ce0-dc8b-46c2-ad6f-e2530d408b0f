package com.augmentcode.intellij.chat

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.*
import io.mockk.every
import kotlinx.coroutines.Dispatchers
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentChatProjectActivityTest : AugmentBasePlatformTestCase() {
  private lateinit var mockPluginStateService: PluginStateService
  private lateinit var mockMessagingService: ChatMessagingService

  override fun setUp() {
    super.setUp()

    // Mock PluginStateService
    mockPluginStateService = mockk(relaxed = true)
    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )

    mockMessagingService = mockk(relaxed = true)
    project.registerOrReplaceServiceInstance(
      ChatMessagingService::class.java,
      mockMessagingService,
      testRootDisposable,
    )
  }

  @Test
  fun testSendInitialGuidelinesStateEnabled() {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijEnableUserGuidelines = true
        },
      )
    val mockContext =
      PluginContext(
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )
    every { mockPluginStateService.context } returns mockContext
    val activity = AugmentChatProjectActivity(augmentHelpers().createCoroutineScope(Dispatchers.IO))
    activity.onStartup(project)

    verify(exactly = 1) { mockMessagingService.sendGuidelinesStateToWebview() }
  }

  @Test
  fun testSendInitialGuidelinesStateDisabled() {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijEnableUserGuidelines = false
        },
      )
    val mockContext =
      PluginContext(
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )
    every { mockPluginStateService.context } returns mockContext
    val activity = AugmentChatProjectActivity(augmentHelpers().createCoroutineScope(Dispatchers.IO))
    activity.onStartup(project)

    verify(exactly = 0) { mockMessagingService.sendGuidelinesStateToWebview() }
  }

  @Test
  fun testSendInitialGuidelinesStateNoPluginStateContext() {
    every { mockPluginStateService.context } returns null
    val activity = AugmentChatProjectActivity(augmentHelpers().createCoroutineScope(Dispatchers.IO))
    activity.onStartup(project)

    verify(exactly = 0) { mockMessagingService.sendGuidelinesStateToWebview() }
  }

  @Test
  fun testSendInitialGuidelinesStateOnPluginStateChange() {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijEnableUserGuidelines = true
        },
      )
    val mockContext =
      PluginContext(
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )
    every { mockPluginStateService.context } returns mockContext

    var listener: PluginStateListener? = null
    every { mockPluginStateService.subscribe(any(), any()) } answers {
      listener = secondArg<PluginStateListener>()
    }

    val activity = AugmentChatProjectActivity(augmentHelpers().createCoroutineScope(Dispatchers.IO))
    activity.onStartup(project)

    verify(exactly = 1) { mockMessagingService.sendGuidelinesStateToWebview() }

    assertNotNull(listener)

    // These state changes should not trigger a guidelines state change
    listener?.onStateChange(null, PluginState.INITIALIZING)
    listener?.onStateChange(null, PluginState.ENABLED)
    // This state change should trigger a guidelines state change
    listener?.onStateChange(mockContext, PluginState.ENABLED)

    waitForAssertion({
      verify(exactly = 2) { mockMessagingService.sendGuidelinesStateToWebview() }
    })
  }
}

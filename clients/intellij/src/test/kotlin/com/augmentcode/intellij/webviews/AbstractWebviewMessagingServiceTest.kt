package com.augmentcode.intellij.webviews

import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.rpc.AsyncWrapper
import com.augmentcode.rpc.MainPanelActions
import com.google.protobuf.Any
import com.google.protobuf.Empty
import com.google.protobuf.Message
import com.intellij.openapi.project.Project
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.mockk.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AbstractWebviewMessagingServiceTest : BasePlatformTestCase() {
  @Test
  fun testProcessMessageFromWebviewSyncUnknown() =
    runBlocking {
      val mockMessagingService = MockMessagingService(project, this)

      // Test processing a sync message
      val syncMessage = """{"type": "does-not-exist"}"""
      val syncResponses = mockMessagingService.processMessageFromWebview(syncMessage).toList()

      // Verify empty response for sync message
      assertEquals(0, syncResponses.size)
      assertEquals(0, mockMessagingService.syncRequests.size)
    }

  @Test
  fun testProcessMessageFromWebviewSyncKnown() =
    runBlocking {
      val mockMessagingService = MockMessagingService(project, this)

      // Test processing a sync message that is correct
      val syncMessage =
        """
        {
          "type": "main-panel-actions",
          "data": ["test"]
        }
        """.trimIndent()
      val syncResponses = mockMessagingService.processMessageFromWebview(syncMessage).toList()

      // Verify response for sync message
      assertEquals(1, syncResponses.size)
      assertEquals(1, mockMessagingService.syncRequests.size)
      assertEquals(Any.pack(MainPanelActions.newBuilder().addData("test").build()), mockMessagingService.syncRequests[0])
    }

  @Test
  fun testProcessMessageFromWebviewAsyncUnknown() =
    runBlocking {
      val mockMessagingService = MockMessagingService(project, this)

      // Test processing an async message
      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "does-not-exist",
                "data": {
                    "text": "Test message"
                }
            }
        }
        """.trimIndent()

      val asyncResponses = mockMessagingService.processMessageFromWebview(asyncMessage).toList()

      // Verify response for async message
      assertEquals(1, asyncResponses.size)
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals(
        "{\"requestId\":\"test-123\",\"error\":\"Unable to parse webview message\",\"type\":\"async-wrapper\"}",
        asyncResponses[0],
      )
    }

  @Test
  fun testProcessMessageFromWebviewAsyncKnown() =
    runBlocking {
      val mockMessagingService = MockMessagingService(project, this)

      // Test processing an async message
      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "main-panel-actions",
                "data": ["test"]
            }
        }
        """.trimIndent()

      val asyncResponses = mockMessagingService.processMessageFromWebview(asyncMessage).toList()

      // Verify response for async message
      assertEquals(1, asyncResponses.size)
      assertEquals(1, mockMessagingService.asyncRequests.size)
      assertEquals(
        AsyncWrapper.newBuilder().setRequestId(
          "test-123",
        ).setBaseMsg(Any.pack(MainPanelActions.newBuilder().addData("test").build())).build(),
        mockMessagingService.asyncRequests[0],
      )
    }

  @Test
  fun testProcessMessageFromWebviewToSidecarUnhandled() =
    runBlocking {
      val mockMessagingService = MockMessagingService(project, this)

      // Test processing an async message

      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "destination": "sidecar",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "main-panel-actions",
                "data": ["test"]
            }
        }
        """.trimIndent()
      val asyncResponses = mockMessagingService.processMessageFromWebview(asyncMessage).toList()

      // Verify response for async message
      assertEquals(1, asyncResponses.size)
      assertEquals(0, mockMessagingService.syncRequests.size)
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals(
        "{\"requestId\":\"test-123\",\"error\":\"Failed to get a response from the sidecar\",\"type\":\"async-wrapper\"}",
        asyncResponses[0],
      )
    }

  @Test
  fun testProcessMessageFromWebviewToSidecarHandled() =
    runBlocking {
      val mockSidecarService = mockk<SidecarService>()
      coEvery { mockSidecarService.sendWebviewMessage(any()) } returns "{}"
      every { mockSidecarService.dispose() } returns Unit

      myFixture.project.registerOrReplaceServiceInstance(
        SidecarService::class.java,
        mockSidecarService,
        testRootDisposable,
      )

      val mockMessagingService = MockMessagingService(project, this)

      val asyncMessage =
        """
        {
            "type": "async-wrapper",
            "destination": "sidecar",
            "requestId": "test-123",
            "error": null,
            "baseMsg": {
                "type": "main-panel-actions",
                "data": ["test"]
            }
        }
        """.trimIndent()
      val asyncResponses = mockMessagingService.processMessageFromWebview(asyncMessage).toList()

      // Verify response for async message
      assertEquals(1, asyncResponses.size)
      assertEquals(0, mockMessagingService.syncRequests.size)
      assertEquals(0, mockMessagingService.asyncRequests.size)
      assertEquals("{\"type\":\"async-wrapper\",\"requestId\":\"test-123\",\"baseMsg\":{}}", asyncResponses[0])
    }
}

class MockMessagingService(project: Project, cs: CoroutineScope) : AbstractWebviewMessagingService(project, cs) {
  // Public lists to store received requests
  val syncRequests = mutableListOf<Any>()
  val asyncRequests = mutableListOf<AsyncWrapper>()

  override suspend fun processSyncMessage(request: Any): Flow<Message> {
    // Store the sync request
    syncRequests.add(request)
    return flowOf(Empty.getDefaultInstance())
  }

  override suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message> {
    // Store the async request
    asyncRequests.add(request)
    return flowOf(
      AsyncWrapper.newBuilder()
        .setRequestId(request.requestId)
        .setBaseMsg(Any.pack(Empty.getDefaultInstance()))
        .build(),
    )
  }

  override fun dispose() {
    syncRequests.clear()
    asyncRequests.clear()
  }
}

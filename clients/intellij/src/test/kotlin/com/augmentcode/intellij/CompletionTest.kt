package com.augmentcode.intellij

import com.augmentcode.api.CompletionItem
import com.augmentcode.api.CompletionResult
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.completion.AugmentCompletionProvider
import com.augmentcode.intellij.completion.AugmentLastCompletionElementKey
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.session.InlineCompletionContext
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.openapi.actionSystem.IdeActions
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertNotEquals
import org.junit.Rule
import org.junit.Test
import org.junit.rules.Timeout
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@Suppress("UnstableApiUsage")
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class CompletionTest : BasePlatformTestCase() {
  @JvmField
  @Rule
  var globalTimeout: Timeout = Timeout.seconds(10) // 10s since CI is slow

  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.modelName = null
    runBlocking {
      AugmentRemoteSyncingManager.getInstance(project).reset()
    }
  }

  @Test
  fun testDefaultModel() =
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(completionResult),
        testRootDisposable,
      )

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")
    }

  @Test
  fun testAlternativeModel() =
    myFixture.testInlineCompletion {
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(
          mapOf(
            "" to generateCompletionResult("Default completion"),
            "alternative-model" to generateCompletionResult("Alternative completion"),
          ),
        ),
        testRootDisposable,
      )

      myFixture.configureByText("test.txt", "<caret>")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)

      callInlineCompletion()
      delay()
      var context = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals("Default completion", context?.textToInsert())
      escape()

      AugmentSettings.instance.modelName = "alternative-model"
      delay()
      callInlineCompletion()
      delay()
      context = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals("Alternative completion", context?.textToInsert())
    }

  @Test
  fun testUnknownMemories() =
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)
      completionResult.unknownMemoryNames = listOf("hash(unknown.txt).v1")
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(completionResult),
        testRootDisposable,
      )

      val unknownFile = myFixture.addFileToProject("unknown.txt", "// unknown.txt")
      myFixture.configureByFile("simple.txt")

      // check that we initially indexed the unknown.txt file before calling the completion
      assertEquals("unknown.txt", AugmentBlobStateReader.read(unknownFile)?.relativePath)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      // wait and test that we reindexed the unknown file
      delay()
      assertEquals("unknown.txt", AugmentBlobStateReader.read(unknownFile)?.relativePath)
    }

  @Test
  fun testCompletionAcceptanceReported() =
    myFixture.testInlineCompletion {
      val completionResult =
        generateCompletionResult("Hello from Augment!\")")

      @Suppress("DEPRECATION")
      val mockAugmentAPI = MockAugmentAPI(completionResult)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        mockAugmentAPI,
        testRootDisposable,
      )

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay()
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      assertNotEmpty(mockAugmentAPI.recordedCompletions)
      val resolution = mockAugmentAPI.recordedCompletions.first()
      assertEquals(0, resolution.acceptedIdx)
      assertTrue(resolution.emitTimeSec <= resolution.resolveTimeSec)
    }

  @Test
  fun testCompletionRejectionReported() =
    myFixture.testInlineCompletion {
      val completionResult =
        generateCompletionResult("Hello from Augment!\")")

      @Suppress("DEPRECATION")
      val mockAugmentAPI = MockAugmentAPI(completionResult)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        mockAugmentAPI,
        testRootDisposable,
      )

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay()

      assertNotNull(myFixture.editor.getUserData(AugmentLastCompletionElementKey))
      escape() // to reject
      delay()
      assertNull(myFixture.editor.getUserData(AugmentLastCompletionElementKey))

      assertNotEmpty(mockAugmentAPI.recordedCompletions)
      val resolution = mockAugmentAPI.recordedCompletions.first()
      assertEquals(-1, resolution.acceptedIdx)
      assertTrue(resolution.emitTimeSec <= resolution.resolveTimeSec)
    }

  @Test
  fun testRecentInfo() =
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)

      @Suppress("DEPRECATION")
      val mockAugmentAPI = MockAugmentAPI(completionResult)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        mockAugmentAPI,
        testRootDisposable,
      )

      val file = myFixture.configureByFile("simple.txt")

      assertEquals("simple.txt", AugmentBlobStateReader.read(file)?.relativePath)
      val originalBlobName = AugmentBlobStateReader.read(file)?.remoteName
      val blobs = AugmentBlobStateReader.allUnfilteredIndexes(project)
      assertNotNull(blobs)
      AugmentRemoteSyncingManager.getInstance(project).findUnsynced(blobs!!)

      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      // check that we reindexed the file but not changed the remote name
      assertEquals("simple.txt", AugmentBlobStateReader.read(file)?.relativePath)
      assertNotEquals(originalBlobName, AugmentBlobStateReader.read(file)?.localName)
      assertEquals(originalBlobName, AugmentBlobStateReader.read(file)?.remoteName)

      typeChar('\n')
      callInlineCompletion()
      delay() // so everything is processed

      val actualRequest = mockAugmentAPI.recorderLastCompletionRequest
      assertNotNull(actualRequest)
      assertEquals("644538744891fe381a6c85e4e1187b0287d7fae3b2abd98ca9b0136b66333c13", actualRequest?.blobName)
      val recentChanges = actualRequest?.recencyInfo?.recentChanges ?: emptyList()
      assertNotEmpty(recentChanges)
      assertEquals(1, recentChanges.size)
      assertEquals("simple.txt", recentChanges.first().path)
      assertEquals("644538744891fe381a6c85e4e1187b0287d7fae3b2abd98ca9b0136b66333c13", recentChanges.first().blobName)
      assertFalse(recentChanges.first().presentInBlob)

      assertNotNull(actualRequest?.blobs)
      // TODO: checkpointing is broken in tests. Is that because we're mocking things differently somewhere or
      //       because it's actually broken? I genuinely think it's the former.
//      assertEquals("+ [644538744891fe381a6c85e4e1187b0287d7fae3b2abd98ca9b0136b66333c13] - []", actualRequest?.blobs?.checkpointId)
    }

  @Test
  fun testPartial() =
    myFixture.testInlineCompletion {
      val completionText = "package com.augment;\nimport java.util.*;"
      val completionResult = generateCompletionResult(completionText)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(completionResult),
        testRootDisposable,
      )

      myFixture.configureByText("partial.java", "<caret>")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed

      val context1 = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals(completionText, context1?.textToInsert())

      callAction(IdeActions.ACTION_INSERT_INLINE_COMPLETION_WORD)
      val context2 = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals(" com.augment;\nimport java.util.*;", context2?.textToInsert())
      myFixture.checkResult("package<caret>")

      callAction(IdeActions.ACTION_INSERT_INLINE_COMPLETION_LINE)
      val context3 = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals("import java.util.*;", context3?.textToInsert())
      myFixture.checkResult("package com.augment;\n<caret>")
    }
}

fun generateCompletionResult(vararg variant: String) =
  CompletionResult().apply {
    requestId = "test"
    completionItems =
      variant.map {
        CompletionItem().apply {
          text = it
          skippedSuffix = ""
          suffixReplacementText = ""
        }
      }
  }

package com.augmentcode.intellij.api

import com.augmentcode.api.ReportErrorRequest
import com.augmentcode.intellij.metrics.ErrorReporter
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.engine.mock.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.coroutines.*
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.util.Collections

@RunWith(JUnit4::class)
class AugmentHttpClientTest : AugmentBasePlatformTestCase() {
  private lateinit var errorReporter: ErrorReporter
  private lateinit var httpClient: AugmentHttpClient
  private lateinit var testScope: CoroutineScope
  private val gson = AugmentHttpClient.createGson()
  private val httpCalls = Collections.synchronizedList(mutableListOf<HttpRequestData>())

  override fun setUp() {
    super.setUp()

    testScope = augmentHelpers().createCoroutineScope(Dispatchers.Default)
    errorReporter = ErrorReporter(testScope)
    application.registerOrReplaceServiceInstance(
      ErrorReporter::class.java,
      errorReporter,
      testRootDisposable,
    )

    httpClient = AugmentHttpClient()
    httpCalls.clear()

    // Set up API settings
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  override fun tearDown() {
    super.tearDown()
    httpCalls.clear()
  }

  private fun createMockEngine(firstRequestHandler: MockRequestHandleScope.(HttpRequestData) -> HttpResponseData): MockEngine =
    MockEngine { request ->
      httpCalls.add(request)
      when (request.url.encodedPath.removePrefix("/")) {
        "test-endpoint" -> firstRequestHandler(request)
        "report-error" ->
          respond(
            content = "{}",
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        "get-models" ->
          respond(
            content = "{}",
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        else -> error("Unexpected request to ${request.url.encodedPath}")
      }
    }

  @Test
  fun testBadRequestTriggersErrorReport() {
    val mockEngine =
      createMockEngine { _ ->
        respond(
          content = """{"error": "Bad request"}""",
          status = HttpStatusCode.BadRequest,
          headers = headersOf(HttpHeaders.ContentType, "application/json"),
        )
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    runBlocking {
      val requestId = "test-request-id"
      val response =
        httpClient.post(
          path = "test-endpoint",
          body = mapOf("key" to "value"),
          requestID = requestId,
        )

      assertEquals(HttpStatusCode.BadRequest.value, response.status.value)

      // Wait for the error report to be processed
      // ErrorReporter uses DEFAULT_UPLOAD_MSEC which is 10ms in test mode
      waitForAssertion({
        val filteredCalls = httpCalls.filter { it.url.encodedPath.removePrefix("/") != "get-models" }
        assertTrue(filteredCalls.size >= 2)
      }, timeoutMs = 5000)

      // The plugin state service will call get-models since we provide an API token
      val filteredCalls = httpCalls.filter { it.url.encodedPath.removePrefix("/") != "get-models" }

      // Verify the HTTP calls
      assertEquals(
        "Expected 2 HTTP calls (original request + error report), " +
          "instead got ${filteredCalls.size}: ${filteredCalls.map { it.url.encodedPath }}",
        2,
        filteredCalls.size,
      )

      // First call should be the failed request
      val failedRequest = filteredCalls[0]
      assertEquals("test-endpoint", failedRequest.url.encodedPath.removePrefix("/"))

      // Second call should be the error report
      val errorReport = filteredCalls[1]
      assertEquals("report-error", errorReport.url.encodedPath.removePrefix("/"))

      // Verify error report content
      val reportBody = errorReport.body.toByteArray().decodeToString()
      val reportRequest = gson.fromJson(reportBody, ReportErrorRequest::class.java)

      assertEquals(requestId, reportRequest.originalRequestId)
      assertEquals("HTTP request failed with status 400: test-endpoint", reportRequest.sanitizedMessage)
      assertTrue(reportRequest.stackTrace.isEmpty()) // HTTP status errors don't include stack traces

      val diagnostics = reportRequest.diagnostics.associate { it.key to it.value }
      assertEquals("test-endpoint", diagnostics["endpoint"])
      assertEquals(requestId, diagnostics["request_id"])
      assertEquals("400", diagnostics["status_code"])
      assertEquals("Bad Request", diagnostics["status_description"])
      assertEquals("""{"error": "Bad request"}""", diagnostics["response_body"])
    }
  }

  @Test
  fun testExceptionTriggersErrorReport() {
    val mockEngine =
      createMockEngine { _ ->
        throw IllegalStateException("Simulated network error")
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    runBlocking {
      val requestId = "test-request-id"
      val exception =
        kotlin
          .runCatching {
            httpClient.post(
              path = "test-endpoint",
              body = mapOf("key" to "value"),
              requestID = requestId,
            )
          }.exceptionOrNull()

      assertNotNull(exception)
      assertTrue(exception is IllegalStateException)

      // Wait for the error report to be processed
      waitForAssertion({
        val filteredCalls = httpCalls.filter { it.url.encodedPath.removePrefix("/") != "get-models" }
        assertTrue(filteredCalls.size >= 2)
      }, timeoutMs = 5000)

      // The plugin state service will call get-models since we provide an API token
      val filteredCalls = httpCalls.filter { it.url.encodedPath.removePrefix("/") != "get-models" }

      // Verify the HTTP calls
      assertEquals(
        "Expected 2 HTTP calls (original request + error report), " +
          "instead got ${filteredCalls.size}: ${filteredCalls.map { it.url.encodedPath }}",
        2,
        filteredCalls.size,
      )

      // First call should be the failed request
      val failedRequest = filteredCalls[0]
      assertEquals("test-endpoint", failedRequest.url.encodedPath.removePrefix("/"))

      // Second call should be the error report
      val errorReport = filteredCalls[1]
      assertEquals("report-error", errorReport.url.encodedPath.removePrefix("/"))

      // Verify error report content
      val reportBody = errorReport.body.toByteArray().decodeToString()
      val reportRequest = gson.fromJson(reportBody, ReportErrorRequest::class.java)

      assertEquals(requestId, reportRequest.originalRequestId)
      assertEquals("HTTP request failed: test-endpoint", reportRequest.sanitizedMessage)
      assertTrue(reportRequest.stackTrace.contains("Simulated network error"))

      val diagnostics = reportRequest.diagnostics.associate { it.key to it.value }
      assertEquals("test-endpoint", diagnostics["endpoint"])
      assertEquals(requestId, diagnostics["request_id"])
      assertEquals("java.lang.IllegalStateException", diagnostics["error_class"])
    }
  }
}

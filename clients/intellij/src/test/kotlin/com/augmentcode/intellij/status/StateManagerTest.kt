package com.augmentcode.intellij.status

import com.intellij.testFramework.fixtures.BasePlatformTestCase
import junit.framework.TestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class StateManagerTest : BasePlatformTestCase() {
  @Test
  fun testStateChanges() {
    val sm = StateManager()
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Base)
    TestCase.assertEquals(sm.baseState, StateDefinitions.Base)

    val enableDisposal = sm.setState(StateDefinitions.Enabled)
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Enabled)

    val unauthorizedDisposal = sm.setState(StateDefinitions.Unauthorized)
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Unauthorized)

    enableDisposal()
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Unauthorized)

    unauthorizedDisposal()
    TestCase.assertEquals(sm.getPriorityState(), StateDefinitions.Base)
  }
}

package com.augmentcode.intellij.webviews

import com.augmentcode.intellij.testutils.UncaughtExceptionTracker
import com.augmentcode.intellij.testutils.UncaughtExceptionTracker.Companion.exceptionContains
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.intellij.ui.jcef.JBCefApp
import com.intellij.ui.jcef.JBCefBrowser
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentWebviewTest : BasePlatformTestCase() {
  @Test
  fun testReloadIntellijStylesWithDisposedBrowser() {
    // Locally, the test doesn't need this. But in CI, jcef isn't supported, so
    // we need to mock it.
    mockkStatic(JBCefApp::class)
    every { JBCefApp.isSupported() } returns true

    // Create a real browser
    val browser = JBCefBrowser()

    // Create a real RPCAdapter with the project and browser
    val stateKey = AugmentWebviewStateKey.CHAT_STATE
    val messagingService = ChatMessagingService.getInstance(project)
    val rpcAdapter = RPCAdapter(project, browser, 2, stateKey, messagingService)

    // Create the webview with real components
    val webview = AugmentWebview(project, browser, rpcAdapter)

    // Create exception utility to capture unhandled exceptions
    val exceptionUtil = UncaughtExceptionTracker()
    val exceptionFilter = exceptionContains("Failed to execute the requested JS expression. The related JCEF browser in not initialized.")
    exceptionUtil.capture(exceptionFilter) {
      // Dispose the browser before calling reloadIntellijStyles
      Disposer.dispose(browser)

      // Call the method that should handle the disposed browser gracefully
      webview.reloadIntellijStyles()

      // Wait for any potential unhandled exceptions
      exceptionUtil.waitForExceptions(1000)

      // Verify no unhandled exceptions occurred
      assertFalse(
        "There should be no unhandled exceptions with the specified error message",
        exceptionUtil.hasExceptions(),
      )
    }

    // Clean up
    Disposer.dispose(rpcAdapter)
    unmockkStatic(JBCefApp::class)
  }
}

package com.augmentcode.intellij.api

import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.mockOAuthState
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import io.ktor.client.*
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class ModelInfoTest : BasePlatformTestCase() {
  private var httpClient: HttpClient? = null

  private val mockModelResponse =
    """
    {
        "default_model": "test-model",
        "models": [
            {
                "name": "test-model",
                "suggested_prefix_char_count": 100,
                "suggested_suffix_char_count": 50
            }
        ]
    }
    """.trimIndent()

  // Supply empty test data so indexing doesn't interfere with the tests in this class
  override fun getTestDataPath() = "src/test/testData/empty"

  override fun setUp() {
    super.setUp()
    // Reset settings
    AugmentSettings.instance.apiToken = null
    AugmentSettings.instance.completionURL = null
  }

  override fun tearDown() {
    try {
      httpClient?.close()
      httpClient = null
    } finally {
      AugmentSettings.instance.apiToken = null
      AugmentSettings.instance.completionURL = null
      (AugmentAPI.instance as AugmentAPIImpl).clearModelCache()
      super.tearDown()
    }
  }

  @Test
  fun testHappyPath() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      mockOAuthState(
        AugmentCredentials("access-token", "tenant-url"),
        testRootDisposable,
      )

      var getModelsCalled = 0
      val mockEngine =
        MockEngine { request ->
          assertEquals("POST", request.method.value)
          assertEquals("/get-models", request.url.encodedPath)
          getModelsCalled++

          respond(
            content = mockModelResponse,
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val result = AugmentAPI.instance.fetchModelInfo()

      assertNotNull(result)
      assertEquals("test-model", result?.defaultModelName)
      assertEquals(1, getModelsCalled)
    }

  @Test
  fun testNoApiToken() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = null
      AugmentSettings.instance.completionURL = "http://test-server"
      mockOAuthState(
        null,
        testRootDisposable,
      )

      val mockEngine =
        MockEngine { _ ->
          fail("Should not make HTTP request when no API token")
          // The following line will never be reached, but satisfies the type system
          respond(content = "", status = HttpStatusCode.OK)
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val result = AugmentAPI.instance.fetchModelInfo()
      assertNull(result)
    }

  @Test
  fun testNoCompletionUrl() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = null
      mockOAuthState(
        null,
        testRootDisposable,
      )

      val mockEngine =
        MockEngine { _ ->
          fail("Should not make HTTP request when no completion URL")
          // The following line will never be reached, but satisfies the type system
          respond(content = "", status = HttpStatusCode.OK)
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val result = AugmentAPI.instance.fetchModelInfo()
      assertNull(result)
    }

  @Test
  fun testCacheHit() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      mockOAuthState(
        AugmentCredentials("access-token", "tenant-url"),
        testRootDisposable,
      )

      var getModelsCalled = 0
      val mockEngine =
        MockEngine { request ->
          assertEquals("POST", request.method.value)
          assertEquals("/get-models", request.url.encodedPath)
          getModelsCalled++

          respond(
            content = mockModelResponse,
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      // First call - should hit the API
      val result1 = AugmentAPI.instance.fetchModelInfo()
      assertNotNull(result1)
      assertEquals("test-model", result1?.defaultModelName)
      assertEquals(1, getModelsCalled)

      // Second call - should use cache
      val result2 = AugmentAPI.instance.fetchModelInfo()
      assertNotNull(result2)
      assertEquals("test-model", result2?.defaultModelName)
      // getModelsCalled should still be 1 since we used the cache
      assertEquals(1, getModelsCalled)
    }

  @Test
  fun testCacheInvalidationOnCredentialsChange() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      val initialCredentials = AugmentCredentials("initial-access-token", "initial-tenant-url")
      mockOAuthState(
        initialCredentials,
        testRootDisposable,
      )

      var getModelsCalled = 0
      val mockEngine =
        MockEngine { request ->
          assertEquals("POST", request.method.value)
          assertEquals("/get-models", request.url.encodedPath)
          getModelsCalled++

          respond(
            content = mockModelResponse,
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      // First call - should hit the API
      val result1 = AugmentAPI.instance.fetchModelInfo()
      assertNotNull(result1)
      assertEquals("test-model", result1?.defaultModelName)
      assertEquals(1, getModelsCalled)

      // Change credentials
      val newCredentials = AugmentCredentials("new-access-token", "new-tenant-url")
      mockOAuthState(
        newCredentials,
        testRootDisposable,
      )

      // Second call with new credentials - should hit the API again
      val result2 = AugmentAPI.instance.fetchModelInfo()
      assertNotNull(result2)
      assertEquals("test-model", result2?.defaultModelName)
      assertEquals(2, getModelsCalled) // API should have been called twice
    }

  @Test
  fun testErrorResponse() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      mockOAuthState(
        AugmentCredentials("access-token", "tenant-url"),
        testRootDisposable,
      )

      val mockEngine =
        MockEngine { _ ->
          respond(
            content = "Error",
            status = HttpStatusCode.InternalServerError,
            headers = headersOf(HttpHeaders.ContentType, "text/plain"),
          )
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val result = AugmentAPI.instance.fetchModelInfo()
      assertNull(result)
    }

  @Test
  fun testInvalidJsonResponse() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      mockOAuthState(
        AugmentCredentials("access-token", "tenant-url"),
        testRootDisposable,
      )

      val mockEngine =
        MockEngine { _ ->
          respond(
            content = "Invalid JSON",
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val result = AugmentAPI.instance.fetchModelInfo()
      assertNull(result)
    }

  @Test
  fun testMissingDefaultModel() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      mockOAuthState(
        AugmentCredentials("access-token", "tenant-url"),
        testRootDisposable,
      )

      val mockEngine =
        MockEngine { _ ->
          respond(
            content =
              """
              {
                  "models": [
                      {
                          "name": "test-model",
                          "suggested_prefix_char_count": 100,
                          "suggested_suffix_char_count": 50
                      }
                  ]
              }
              """.trimIndent(),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val result = AugmentAPI.instance.fetchModelInfo()
      assertNull(result?.defaultModelName)
      assertEquals(1, result?.availableModels?.size)
    }

  @Test
  fun testEmptyModels() =
    runBlocking {
      // Setup
      AugmentSettings.instance.apiToken = "test-token"
      AugmentSettings.instance.completionURL = "http://test-server"
      mockOAuthState(
        AugmentCredentials("access-token", "tenant-url"),
        testRootDisposable,
      )

      val mockEngine =
        MockEngine { _ ->
          respond(
            content =
              """
              {
                  "default_model": "test-model",
                  "models": []
              }
              """.trimIndent(),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

      val result = AugmentAPI.instance.fetchModelInfo()
      assertEquals("test-model", result?.defaultModelName)
      assertEquals(0, result?.availableModels?.size)
    }
}

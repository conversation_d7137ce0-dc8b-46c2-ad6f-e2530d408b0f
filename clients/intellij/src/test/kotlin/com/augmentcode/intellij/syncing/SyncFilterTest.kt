package com.augmentcode.intellij.syncing

import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.nio.file.Files

@RunWith(JUnit4::class)
class SyncFilterTest : BasePlatformTestCase() {
  @Test
  fun testSymlinkFilter() {
    val tempDir = Files.createTempDirectory("test")
    val targetPath = tempDir.resolve("target.txt")
    val symlinkPath = tempDir.resolve("symlink.txt")
    // Create the target file
    Files.writeString(targetPath, "file content")
    // Create the symbolic link
    Files.createSymbolicLink(symlinkPath, targetPath)

    val vfsUrl = symlinkPath.toUri().toString()
    val symlinkFile = VirtualFileManager.getInstance().refreshAndFindFileByUrl(vfsUrl)
    assertNotNull(symlinkFile)

    val syncFilter = SyncFilter(myFixture.project)
    runBlocking {
      val accepted = syncFilter.isAccepted(symlinkFile!!.path, symlinkFile.name, symlinkFile)
      assertFalse(accepted)
    }
  }
}

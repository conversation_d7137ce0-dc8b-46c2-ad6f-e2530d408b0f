package com.augmentcode.intellij.metrics

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ErrorReporterTest : AugmentBasePlatformTestCase() {
  @Test
  fun testSanitizeStackTrace() {
    val testCases =
      listOf(
        TestCase(
          input = "at MyClass.method(Test.java:42) (/home/<USER>/project/Test.java)",
          expected = "at MyClass.method(Test.java:42)",
        ),
        TestCase(
          input = "at MyClass.method(Test.java:42)", // no path to remove
          expected = "at MyClass.method(Test.java:42)",
        ),
        TestCase(
          input = "at MyClass.method(Test.java:42) (/tmp/Test.java)",
          expected = "at MyClass.method(Test.java:42)",
        ),
      )

    testCases.forEach { (input, expected) ->
      assertEquals(
        expected,
        ErrorReporter.sanitizeStackTrace(input),
      )
    }
  }

  private data class TestCase(
    val input: String,
    val expected: String,
  )
}

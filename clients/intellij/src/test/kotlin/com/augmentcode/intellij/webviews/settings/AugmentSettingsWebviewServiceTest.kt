package com.augmentcode.intellij.webviews.settings

import com.augmentcode.intellij.settings.AugmentSettingsWebviewEditorVirtualFile
import com.augmentcode.intellij.settings.AugmentSettingsWebviewEditorVirtualFile.Companion.SETTINGS_VIRTUAL_FILE_NAME
import com.augmentcode.intellij.webviews.AugmentWebview
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.testFramework.PlatformTestUtil
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentSettingsWebviewServiceTest : BasePlatformTestCase() {
  override fun tearDown() {
    AugmentSettingsWebviewService.getInstance(project).closeSettingsFile()
    super.tearDown()
  }

  /**
   * Test that navigateToSection calls webview.postMessage with the section name in the payload
   */

  @Test
  fun testNavigateToSectionCallsPostMessage() {
    // Get the settings service
    val settingsService = AugmentSettingsWebviewService.getInstance(project)
    settingsService.openSettingsWebview()
    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify that the settings file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Settings virtual file should be opened in editor",
      openFiles.any { it.name == SETTINGS_VIRTUAL_FILE_NAME },
    )

    // Get the settings file
    val settingsFile =
      openFiles.first {
        it.name == SETTINGS_VIRTUAL_FILE_NAME
      } as AugmentSettingsWebviewEditorVirtualFile

    // Create a mock webview
    val mockWebview = mockk<AugmentWebview>(relaxed = true)

    // Get access to the webview field
    val webviewField = AugmentSettingsWebviewEditorVirtualFile::class.java.getDeclaredField("webview")
    webviewField.isAccessible = true

    // Replace the webview with our mock
    webviewField.set(settingsFile, mockWebview)

    // Create a slot to capture the message payload
    val messageSlot = slot<String>()

    // Set up the mock to capture the message payload
    every { mockWebview.postMessage(capture(messageSlot)) } just Runs

    // Now navigate to a specific section by calling the method directly
    val sectionToNavigate = "tools"
    settingsFile.navigateToSection(sectionToNavigate)

    // Verify that postMessage was called
    verify { mockWebview.postMessage(any()) }

    // Verify the message contains the section name
    val expectedMessagePattern = ".*navigate-to-settings-section.*$sectionToNavigate.*"
    assertTrue(
      "Message should contain the section name: $sectionToNavigate",
      messageSlot.captured.matches(Regex(expectedMessagePattern, RegexOption.DOT_MATCHES_ALL)),
    )
  }

  /**
   * Test that the settings can be opened with a specific section
   */
  @Test
  fun testOpenSettingsCreatesFileWithSection() {
    // Get the settings service
    val settingsService = AugmentSettingsWebviewService.getInstance(project)
    val sectionToOpen = "user-guidelines"

    // Open settings with a specific section
    settingsService.openSettingsWebview(sectionToOpen)
    // Wait for all UI events to complete
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify that the settings file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Settings virtual file should be opened in editor",
      openFiles.any { it.name == SETTINGS_VIRTUAL_FILE_NAME },
    )

    // Verify that the settings file is created with the correct section
    val settingsFile = openFiles.first { it.name == SETTINGS_VIRTUAL_FILE_NAME } as AugmentSettingsWebviewEditorVirtualFile

    val section = settingsFile.getSection()

    // Verify the section is set correctly
    assertEquals("Settings file should have the correct section", sectionToOpen, section)
  }
}

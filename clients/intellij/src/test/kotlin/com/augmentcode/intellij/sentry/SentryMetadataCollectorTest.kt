package com.augmentcode.intellij.sentry

import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.FeatureFlagsTestUtil
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.util.BuildNumber
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import io.mockk.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.io.File

/**
 * Test for SentryMetadataCollector with proper Sentry integration testing.
 * Tests metadata collection functionality and integration with feature flags.
 * Uses model-level functional tests that test actual behavior rather than relying heavily on mocks.
 */
@RunWith(JUnit4::class)
class SentryMetadataCollectorTest : AugmentBasePlatformTestCase() {
  private lateinit var originalSystemProperties: Map<String, String?>
  private lateinit var originalEnvironmentVariables: Map<String, String?>

  override fun setUp() {
    super.setUp()
    // Store original system properties and environment variables for restoration
    originalSystemProperties =
      mapOf(
        "idea.is.remote.connection" to System.getProperty("idea.is.remote.connection"),
        "jetbrains.gateway.mode" to System.getProperty("jetbrains.gateway.mode"),
        "jetbrains.client.mode" to System.getProperty("jetbrains.client.mode"),
      )

    originalEnvironmentVariables =
      mapOf(
        "JETBRAINS_REMOTE_RUN" to System.getenv("JETBRAINS_REMOTE_RUN"),
        "JETBRAINS_GATEWAY" to System.getenv("JETBRAINS_GATEWAY"),
      )
  }

  override fun tearDown() {
    try {
      // Restore original system properties
      originalSystemProperties.forEach { (key, value) ->
        if (value != null) {
          System.setProperty(key, value)
        } else {
          System.clearProperty(key)
        }
      }
    } finally {
      super.tearDown()
    }
  }

  /**
   * Helper method to set up feature flags for Sentry metadata collection testing.
   * Uses FeatureFlagsTestUtil to properly mock feature flag API calls.
   */
  private fun setupSentryFeatureFlags(
    sentryEnabled: Boolean = true,
    pluginErrorSamplingRate: Double = 1.0,
    pluginTraceSamplingRate: Double = 1.0,
  ) {
    FeatureFlagsTestUtil.setupFeatureFlags(
      testRootDisposable,
      mapOf(
        "intellij_enable_sentry" to sentryEnabled,
        "intellij_plugin_error_sampling_rate" to pluginErrorSamplingRate,
        "intellij_plugin_trace_sampling_rate" to pluginTraceSamplingRate,
      ),
    )
  }

  @Test
  fun testExtractTenantNameFromUrl() {
    // Given - Set up feature flags for Sentry integration
    setupSentryFeatureFlags(sentryEnabled = true)

    // Test the actual method directly (no reflection needed)
    val collector = createTestCollector()

    val testCases =
      mapOf(
        "https://tenant-name.api.augmentcode.com" to "tenant-name",
        "https://my-company.us-central.api.augmentcode.com" to "my-company",
        "https://test.api.augmentcode.com" to "test",
        "https://invalid-url.com" to "invalid-url.com",
        "not-a-url" to null,
      )

    testCases.forEach { (url, expected) ->
      val result = collector.extractTenantNameFromUrl(url)
      assert(result == expected) { "Failed for URL: $url, expected: $expected, got: $result" }
    }

    // Verify that the feature flag was properly read
    assertTrue("Sentry should be enabled via feature flag", FeatureFlagManager.instance.sentryEnabled())
  }

  @Test
  fun testDetectRemoteGateway_normalEnvironment() {
    // Given - Set up feature flags for Sentry integration
    setupSentryFeatureFlags(sentryEnabled = true)

    // Test in normal environment (no Gateway indicators)
    val collector = createTestCollector()
    val result = collector.detectRemoteGateway()

    // In normal test environment, should return false
    assert(!result) { "Expected false in normal test environment, got: $result" }

    // Verify that the feature flag was properly read
    assertTrue("Sentry should be enabled via feature flag", FeatureFlagManager.instance.sentryEnabled())
  }

  @Test
  fun testDetectRemoteGateway_withGatewayProductCode() {
    // Mock ApplicationInfo to return Gateway product code
    mockkStatic(ApplicationInfo::class)
    val mockApplicationInfo = mockk<ApplicationInfo>()
    val mockBuildNumber = mockk<BuildNumber>()

    every { ApplicationInfo.getInstance() } returns mockApplicationInfo
    every { mockApplicationInfo.build } returns mockBuildNumber
    every { mockBuildNumber.productCode } returns "GW"
    every { mockApplicationInfo.versionName } returns "IntelliJ IDEA"

    val collector = createTestCollector()
    val result = collector.detectRemoteGateway()

    // Should return true for Gateway product code
    assert(result) { "Expected true for Gateway product code 'GW', got: $result" }
  }

  @Test
  fun testDetectRemoteGateway_withJetBrainsClientProductCode() {
    // Mock ApplicationInfo to return JetBrains Client product code
    mockkStatic(ApplicationInfo::class)
    val mockApplicationInfo = mockk<ApplicationInfo>()
    val mockBuildNumber = mockk<BuildNumber>()

    every { ApplicationInfo.getInstance() } returns mockApplicationInfo
    every { mockApplicationInfo.build } returns mockBuildNumber
    every { mockBuildNumber.productCode } returns "JBC"
    every { mockApplicationInfo.versionName } returns "JetBrains Client"

    val collector = createTestCollector()
    val result = collector.detectRemoteGateway()

    // Should return true for JetBrains Client product code
    assert(result) { "Expected true for JetBrains Client product code 'JBC', got: $result" }
  }

  @Test
  fun testDetectRemoteGateway_withSystemProperties() {
    // Test with various system properties
    val testCases =
      mapOf(
        "idea.is.remote.connection" to "true",
        "jetbrains.gateway.mode" to "true",
        "jetbrains.client.mode" to "true",
      )

    testCases.forEach { (property, value) ->
      // Clear all properties first
      clearSystemProperties()

      // Set the specific property
      System.setProperty(property, value)

      // Mock ApplicationInfo for normal product code
      mockNormalApplicationInfo()

      val collector = createTestCollector()
      val result = collector.detectRemoteGateway()

      // Should return true for any Gateway system property
      assert(result) { "Expected true for system property '$property=$value', got: $result" }
    }
  }

  @Test
  fun testDetectRemoteGateway_withApplicationNamePatterns() {
    // Test with application names that contain Gateway indicators
    val testCases =
      listOf(
        "JetBrains Gateway",
        "IntelliJ Gateway",
        "JetBrains Client",
        "Remote Client",
      )

    testCases.forEach { appName ->
      // Mock ApplicationInfo with Gateway-indicating name
      mockkStatic(ApplicationInfo::class)
      val mockApplicationInfo = mockk<ApplicationInfo>()
      val mockBuildNumber = mockk<BuildNumber>()

      every { ApplicationInfo.getInstance() } returns mockApplicationInfo
      every { mockApplicationInfo.build } returns mockBuildNumber
      every { mockBuildNumber.productCode } returns "IC" // Normal product code
      every { mockApplicationInfo.versionName } returns appName

      val collector = createTestCollector()
      val result = collector.detectRemoteGateway()

      // Should return true for Gateway-indicating application names
      assert(result) { "Expected true for application name '$appName', got: $result" }
    }
  }

  @Test
  fun testDetectRemoteGateway_withInvalidSystemProperties() {
    // Test with invalid/malformed system property values
    val testCases =
      mapOf(
        "idea.is.remote.connection" to "invalid",
        "jetbrains.gateway.mode" to "not-a-boolean",
        "jetbrains.client.mode" to "",
      )

    testCases.forEach { (property, value) ->
      // Clear all properties first
      clearSystemProperties()

      // Set the invalid property
      System.setProperty(property, value)

      // Mock ApplicationInfo for normal product code
      mockNormalApplicationInfo()

      val collector = createTestCollector()
      val result = collector.detectRemoteGateway()

      // Should return false for invalid property values (they default to false)
      assert(!result) { "Expected false for invalid system property '$property=$value', got: $result" }
    }
  }

  @Test
  fun testDetectRemoteGateway_errorHandling() {
    // Test error handling when ApplicationInfo throws exception
    mockkStatic(ApplicationInfo::class)
    every { ApplicationInfo.getInstance() } throws RuntimeException("Test exception")

    val collector = createTestCollector()
    val result = collector.detectRemoteGateway()

    // Should return false when exception occurs
    assert(!result) { "Expected false when ApplicationInfo throws exception, got: $result" }
  }

  @Test
  fun testDetectRemoteGateway_multipleIndicators() {
    // Test with multiple Gateway indicators present
    System.setProperty("jetbrains.gateway.mode", "true")
    System.setProperty("idea.is.remote.connection", "true")

    // Mock ApplicationInfo with Gateway product code
    mockkStatic(ApplicationInfo::class)
    val mockApplicationInfo = mockk<ApplicationInfo>()
    val mockBuildNumber = mockk<BuildNumber>()

    every { ApplicationInfo.getInstance() } returns mockApplicationInfo
    every { mockApplicationInfo.build } returns mockBuildNumber
    every { mockBuildNumber.productCode } returns "GW"
    every { mockApplicationInfo.versionName } returns "JetBrains Gateway"

    val collector = createTestCollector()
    val result = collector.detectRemoteGateway()

    // Should return true when multiple indicators are present
    assert(result) { "Expected true when multiple Gateway indicators are present, got: $result" }
  }

  @Test
  fun testCountGitTrackedFiles_withValidGitRepo() {
    val collector = createTestCollector()

    // Set up the test project with a real git repository
    val projectPath = setupProjectWithGitRepo(createGitRepo = true)

    // Create some test files using myFixture in the sentry-metadata-test subdirectory
    myFixture.tempDirFixture.createFile("sentry-metadata-test/test1.txt", "test content 1")
    myFixture.tempDirFixture.createFile("sentry-metadata-test/test2.txt", "test content 2")

    try {
      val gitAddProcess =
        ProcessBuilder("git", "add", ".")
          .directory(File(projectPath))
          .start()
      gitAddProcess.waitFor()

      // Test the method using the real project
      runBlocking {
        val result = collector.countGitTrackedFiles(myFixture.project, timeoutSeconds = 10)
        assert(result == 2) { "Expected 2 tracked files, got: $result" }
      }
    } catch (e: Exception) {
      // If git commands fail (e.g., git not available), we can still test that the method
      // handles the git directory detection correctly
      runBlocking {
        val result = collector.countGitTrackedFiles(myFixture.project, timeoutSeconds = 10)
        // Should return -1 if git command fails, or >= 0 if it succeeds
        assert(result >= -1) { "Expected valid result (>= -1), got: $result" }
      }
    }
  }

  @Test
  fun testCountGitTrackedFiles_withNonGitDirectory() {
    val collector = createTestCollector()

    // Set up the test project WITHOUT a git repository
    setupProjectWithGitRepo(createGitRepo = false)

    // Test the method on non-git directory using the real project
    runBlocking {
      val result = collector.countGitTrackedFiles(myFixture.project, timeoutSeconds = 5)
      assert(result == -1) { "Expected -1 for non-git directory, got: $result" }
    }
  }

  @Test
  fun testCountGitTrackedFiles_withMaxFilesLimit() {
    val collector = createTestCollector()

    // Set up the test project with a real git repository
    val projectPath = setupProjectWithGitRepo(createGitRepo = true)

    // Create multiple test files using myFixture (more than our limit)
    for (i in 1..5) {
      myFixture.tempDirFixture.createFile("sentry-metadata-test/test$i.txt", "test content $i")
    }

    try {
      val gitAddProcess =
        ProcessBuilder("git", "add", ".")
          .directory(File(projectPath))
          .start()
      gitAddProcess.waitFor()

      // Test with a low max files limit using the real project
      runBlocking {
        val result =
          collector.countGitTrackedFiles(
            myFixture.project,
            timeoutSeconds = 10,
            maxFiles = 3,
          )
        assert(result == 3) { "Expected 3 (max limit), got: $result" }
      }
    } catch (e: Exception) {
      // If git commands fail, we can still test the maxFiles parameter logic
      runBlocking {
        val result = collector.countGitTrackedFiles(myFixture.project, timeoutSeconds = 10, maxFiles = 3)
        assert(result >= -1) { "Expected valid result (>= -1), got: $result" }
      }
    }
  }

  @Test
  fun testCountGitTrackedFiles_withTimeout() {
    val collector = createTestCollector()

    // Set up the test project without git to trigger error/timeout behavior
    setupProjectWithGitRepo(createGitRepo = false)

    // Test with a very short timeout - this should return -1 quickly due to no git repo
    runBlocking {
      val result =
        collector.countGitTrackedFiles(
          myFixture.project,
          timeoutSeconds = 1,
        )
      assert(result == -1) { "Expected -1 for timeout/error, got: $result" }
    }
  }

  // Helper methods for test setup
  private fun createTestCollector(): SentryMetadataCollector {
    return SentryMetadataCollector(
      project = myFixture.project,
      // Since it launches a background thread, we need to provide a coroutine scope to
      // run the job in the test context
      coroutineScope = augmentHelpers().createCoroutineScope(kotlinx.coroutines.Dispatchers.Default),
      parentDisposable = testRootDisposable,
      onMetricsCollected = { /* no-op for tests */ },
    )
  }

  /**
   * Sets up a git repository using VFS APIs and registers it with the IntelliJ project.
   * This creates VirtualFile directories and files that can be used with git commands.
   *
   * @param createGitRepo Whether to create a .git directory and initialize the repository
   * @return The VirtualFile path to the project directory
   */
  private fun setupProjectWithGitRepo(createGitRepo: Boolean = true): String {
    // Create a temporary directory using VFS APIs
    val tempProjectDir = myFixture.tempDirFixture.findOrCreateDir("sentry-metadata-test")
    val projectPath = tempProjectDir.path

    if (createGitRepo) {
      runWriteAction {
        // Create .git directory structure using VFS APIs
        val gitDir = tempProjectDir.createChildDirectory(this, ".git")

        // Create basic git structure files that git commands expect
        val headFile = gitDir.createChildData(this, "HEAD")
        VfsUtil.saveText(headFile, "ref: refs/heads/main")

        gitDir.createChildDirectory(this, "objects")
        val refsDir = gitDir.createChildDirectory(this, "refs")
        refsDir.createChildDirectory(this, "heads")
      }

      try {
        // Initialize git repo using actual git command for realistic testing
        val gitInitProcess =
          ProcessBuilder("git", "init")
            .directory(File(projectPath))
            .redirectErrorStream(true)
            .start()
        val exitCode = gitInitProcess.waitFor()

        // If git init fails, we still have a basic .git structure for testing
        if (exitCode != 0) {
          println("Git init failed with exit code $exitCode, but continuing with basic .git structure")
        }
      } catch (e: Exception) {
        // If git command is not available, we still have the .git directory structure
        println("Git command not available: ${e.message}, but continuing with basic .git structure")
      }
    }

    // Register the temporary directory with IntelliJ's project
    PsiTestUtil.addContentRoot(module, tempProjectDir)

    return projectPath
  }

  private fun clearSystemProperties() {
    listOf(
      "idea.is.remote.connection",
      "jetbrains.gateway.mode",
      "jetbrains.client.mode",
    ).forEach { System.clearProperty(it) }
  }

  private fun mockNormalApplicationInfo() {
    mockkStatic(ApplicationInfo::class)
    val mockApplicationInfo = mockk<ApplicationInfo>()
    val mockBuildNumber = mockk<BuildNumber>()

    every { ApplicationInfo.getInstance() } returns mockApplicationInfo
    every { mockApplicationInfo.build } returns mockBuildNumber
    every { mockBuildNumber.productCode } returns "IC" // Normal IntelliJ product code
    every { mockApplicationInfo.versionName } returns "IntelliJ IDEA"
  }
}

package com.augmentcode.intellij.actions

import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.pluginstate.AugmentModel
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ShowHistoryActionTest : AugmentBasePlatformTestCase() {
  private lateinit var mockPluginStateService: PluginStateService

  override fun setUp() {
    super.setUp()

    // Mock PluginStateService
    mockPluginStateService = mockk(relaxed = true)
    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )
  }

  @Test
  fun testEnabledFlag() {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijCompletionsHistoryMinVersion = "0.0.0"
        },
      )
    val mockContext =
      PluginContext(
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )
    every { mockPluginStateService.context } returns mockContext

    val action = ShowHistoryAction()
    assertTrue(action.isEnabled)
  }

  @Test
  fun testDisabledFlag() {
    val modelConfig =
      augmentHelpers().createModelConfig(
        com.augmentcode.api.FeatureFlags().apply {
          intellijCompletionsHistoryMinVersion = ""
        },
      )
    val mockContext =
      PluginContext(
        flags = FeatureFlags.fromModelConfig(modelConfig),
        model = AugmentModel.fromModelConfig(modelConfig),
      )
    every { mockPluginStateService.context } returns mockContext

    val action = ShowHistoryAction()
    assertFalse(action.isEnabled)
  }
}

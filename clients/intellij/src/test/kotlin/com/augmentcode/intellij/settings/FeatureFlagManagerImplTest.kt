package com.augmentcode.intellij.settings

import com.augmentcode.api.FeatureFlags
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.mock.*
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

// TODO -- I did not see a good way to mock out PluginManager -- but that is something
// that would help make this test more thorough. I think we might need to use MockK or Mockito
@RunWith(JUnit4::class)
class FeatureFlagManagerImplTest : BasePlatformTestCase() {
  @Test
  fun testWithApiUnavailable() {
    assertFalse(mockFlagAndPluginVersions("0.0.1", null, false).forceCompletionEnabled())
    assertFalse(mockFlagAndPluginVersions("0.0.1", "", false).forceCompletionEnabled())
  }

  @Test
  fun testNullFlagFromServer() {
    assertFalse(mockFlagAndPluginVersions(null, null).forceCompletionEnabled())
    assertFalse(mockFlagAndPluginVersions(null, "0.0.1").forceCompletionEnabled())
  }

  @Test
  fun testEmptyFlagFromServer() {
    assertFalse(mockFlagAndPluginVersions("", "").forceCompletionEnabled())
  }

  @Test
  fun testWithNoFallback() {
    assertFalse(mockFlagAndPluginVersions("0.0.1", null).forceCompletionEnabled())
    assertFalse(mockFlagAndPluginVersions("0.0.1", "").forceCompletionEnabled())
  }

  @Test
  fun testWithInsufficientFallback() {
    assertFalse(mockFlagAndPluginVersions("0.0.1", "0.0.0").forceCompletionEnabled())
  }

  @Test
  fun testWithSufficientFallback() {
    assertTrue(mockFlagAndPluginVersions("0.0.1", "0.0.1").forceCompletionEnabled())
    assertTrue(mockFlagAndPluginVersions("0.0.1", "0.0.2").forceCompletionEnabled())
  }

  @Test
  fun testCurrentPluginVersionIsAtLeast001() {
    assertFalse(runCurrentPluginIsAtLeast("0.0.1", null))
    assertFalse(runCurrentPluginIsAtLeast("0.0.1", ""))
    assertFalse(runCurrentPluginIsAtLeast("0.0.1", "0.0.0"))
    assertTrue(runCurrentPluginIsAtLeast("0.0.1", "0.0.1"))
    assertTrue(runCurrentPluginIsAtLeast("0.0.1", "0.0.2"))
  }

  @Test
  fun testCurrentPluginVersionIsAtLeastNull() {
    assertFalse(runCurrentPluginIsAtLeast(null, null))
    assertFalse(runCurrentPluginIsAtLeast(null, ""))
    assertFalse(runCurrentPluginIsAtLeast(null, "0.0.0"))
    assertFalse(runCurrentPluginIsAtLeast(null, "0.0.1"))
  }

  @Test
  fun testCurrentPluginVersionIsAtLeastEmpty() {
    assertFalse(runCurrentPluginIsAtLeast("", null))
    assertFalse(runCurrentPluginIsAtLeast("", "0.0.0"))
    assertFalse(runCurrentPluginIsAtLeast("", "0.0.1"))
    assertFalse(runCurrentPluginIsAtLeast("", ""))
    assertFalse(runCurrentPluginIsAtLeast("", "0.0.0"))
    assertFalse(runCurrentPluginIsAtLeast("", "0.0.1"))
  }

  @Test
  fun testCurrentPluginVersionIsAtLeast000() {
    assertTrue(runCurrentPluginIsAtLeast("0.0.0", null))
    assertTrue(runCurrentPluginIsAtLeast("0.0.0", ""))
    assertTrue(runCurrentPluginIsAtLeast("0.0.0", "0.0.0"))
    assertTrue(runCurrentPluginIsAtLeast("0.0.0", "0.0.1"))
  }

  @Test
  fun testGuidelinesEnabled() {
    // When guidelines feature flag is disabled
    assertFalse(mockFlagAndPluginVersions("0.0.1", "0.0.1", true, false).guidelinesEnabled())

    // When guidelines feature flag is enabled
    assertTrue(mockFlagAndPluginVersions("0.0.1", "0.0.1", true, true, intellijEnableWorkspaceGuidelines = true).guidelinesEnabled())

    // When API is unavailable but we're not waiting for network
    // MockAugmentAPI still returns the cached model info even when isAvailable is false
    assertTrue(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
        intellijEnableWorkspaceGuidelines = true,
      ).guidelinesEnabled(),
    )

    // When API is unavailable and we're waiting for network
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
      ).guidelinesEnabled(true),
    )
  }

  @Test
  fun testUserGuidelinesEnabled() {
    // When both guidelines and user guidelines are disabled
    assertFalse(mockFlagAndPluginVersions("0.0.1", "0.0.1", true, false, false).userGuidelinesEnabled())

    // When guidelines is enabled but user guidelines is disabled
    assertFalse(mockFlagAndPluginVersions("0.0.1", "0.0.1", true, true, false).userGuidelinesEnabled())

    // When guidelines is disabled but user guidelines is enabled
    assertFalse(mockFlagAndPluginVersions("0.0.1", "0.0.1", true, false, true).userGuidelinesEnabled())

    // When both guidelines and user guidelines are enabled
    assertTrue(
      mockFlagAndPluginVersions("0.0.1", "0.0.1", true, true, true, intellijEnableWorkspaceGuidelines = true).userGuidelinesEnabled(),
    )

    // When API is unavailable but we're not waiting for network
    // MockAugmentAPI still returns the cached model info even when isAvailable is false
    assertTrue(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
        enableUserGuidelines = true,
        intellijEnableWorkspaceGuidelines = true,
      ).userGuidelinesEnabled(),
    )

    // When API is unavailable and we're waiting for network
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
        enableUserGuidelines = true,
      ).userGuidelinesEnabled(true),
    )
  }

  @Test
  fun testUserGuidelinesInSettingsEnabled() {
    // When all flags are disabled
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        enableGuidelines = false,
        enableUserGuidelines = false,
        userGuidelinesInSettings = false,
      ).userGuidelinesInSettingsEnabled(),
    )

    // When guidelines is enabled but user guidelines is disabled
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        enableGuidelines = true,
        enableUserGuidelines = false,
        userGuidelinesInSettings = false,
      ).userGuidelinesInSettingsEnabled(),
    )

    // When guidelines and user guidelines are enabled but settings flag is disabled
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        enableGuidelines = true,
        enableUserGuidelines = true,
        userGuidelinesInSettings = false,
      ).userGuidelinesInSettingsEnabled(),
    )

    // When guidelines is disabled but other flags are enabled
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        enableGuidelines = false,
        enableUserGuidelines = true,
        userGuidelinesInSettings = true,
      ).userGuidelinesInSettingsEnabled(),
    )

    // When all flags are enabled
    assertTrue(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        enableGuidelines = true,
        enableUserGuidelines = true,
        userGuidelinesInSettings = true,
        intellijEnableWorkspaceGuidelines = true,
      ).userGuidelinesInSettingsEnabled(),
    )

    // When API is unavailable but we're not waiting for network
    // MockAugmentAPI still returns the cached model info even when isAvailable is false
    assertTrue(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
        enableUserGuidelines = true,
        userGuidelinesInSettings = true,
        intellijEnableWorkspaceGuidelines = true,
      ).userGuidelinesInSettingsEnabled(),
    )

    // When API is unavailable and we're waiting for network
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
        enableUserGuidelines = true,
        userGuidelinesInSettings = true,
      ).userGuidelinesInSettingsEnabled(true),
    )
  }

  @Test
  fun testGetUserGuidelinesLengthLimit() {
    // Default value when no specific limit is set
    assertEquals(
      FeatureFlags.DEFAULT_USER_GUIDELINES_LENGTH_LIMIT,
      mockFlagAndPluginVersions("0.0.1", "0.0.1").getUserGuidelinesLengthLimit(),
    )

    // Custom length limit
    assertEquals(
      1000,
      mockFlagAndPluginVersions("0.0.1", "0.0.1", true, true, true, 1000).getUserGuidelinesLengthLimit(),
    )

    // When API is unavailable but we're not waiting for network
    // MockAugmentAPI still returns the cached model info even when isAvailable is false
    assertEquals(
      1000,
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
        enableUserGuidelines = true,
        userGuidelinesLengthLimit = 1000,
      ).getUserGuidelinesLengthLimit(),
    )

    // When API is unavailable and we're waiting for network
    assertEquals(
      FeatureFlags.DEFAULT_USER_GUIDELINES_LENGTH_LIMIT,
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableGuidelines = true,
        enableUserGuidelines = true,
        userGuidelinesLengthLimit = 1000,
      ).getUserGuidelinesLengthLimit(true),
    )
  }

  @Test
  fun testChatMultimodalEnabled() {
    // When flag version is null, the feature should be disabled
    assertFalse(mockFlagAndPluginVersions(null, "0.0.1").chatMultimodalEnabled())

    // When flag version is empty, the feature should be disabled
    assertFalse(mockFlagAndPluginVersions("", "0.0.1").chatMultimodalEnabled())

    // When plugin version is less than required, the feature should be disabled
    assertFalse(mockFlagAndPluginVersions("0.0.2", "0.0.1").chatMultimodalEnabled())

    // When plugin version equals required, the feature should be enabled
    assertTrue(mockFlagAndPluginVersions("0.0.1", "0.0.1").chatMultimodalEnabled())
  }

  @Test
  fun testSentryEnabled() {
    // When the feature flag is explicitly set to true, it should be enabled
    assertTrue(mockFlagAndPluginVersions("0.0.1", "0.0.1", enableSentry = true).sentryEnabled())

    // When the feature flag is explicitly set to false, it should be disabled
    assertFalse(mockFlagAndPluginVersions("0.0.1", "0.0.1", true, false, false, 2000, false, false, false).sentryEnabled())

    // When the feature flag is not set, it should be disabled by default
    assertFalse(mockFlagAndPluginVersions("0.0.1", "0.0.1").sentryEnabled())

    // When API is unavailable but we're not waiting for network, it should use cached value
    assertTrue(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableSentry = true,
      ).sentryEnabled(),
    )

    // When API is unavailable and we're waiting for network, it should return default value (false)
    assertFalse(
      mockFlagAndPluginVersions(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        enableSentry = false,
      ).sentryEnabled(true),
    )
  }

  @Test
  fun testWebviewErrorSamplingRate() {
    // When the feature flag is not set, it should return the default value
    assertEquals(0.1, mockFlagAndPluginVersions("0.0.1", "0.0.1").webviewErrorSamplingRate(), 0.001)

    // When the feature flag is explicitly set, it should return that value
    assertEquals(
      0.5,
      mockFlagAndPluginVersionsWithSamplingRates("0.0.1", "0.0.1", webviewErrorRate = 0.5).webviewErrorSamplingRate(),
      0.001,
    )

    // When API is unavailable but we're not waiting for network, it should use cached value
    assertEquals(
      0.75,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        webviewErrorRate = 0.75,
      ).webviewErrorSamplingRate(),
      0.001,
    )

    // When API is unavailable and we're waiting for network, it should return default value
    assertEquals(
      0.0,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        webviewErrorRate = 0.75,
      ).webviewErrorSamplingRate(true),
      0.001,
    )
  }

  @Test
  fun testPluginErrorSamplingRate() {
    // When the feature flag is not set, it should return the default value
    assertEquals(0.1, mockFlagAndPluginVersions("0.0.1", "0.0.1").pluginErrorSamplingRate(), 0.001)

    // When the feature flag is explicitly set, it should return that value
    assertEquals(0.5, mockFlagAndPluginVersionsWithSamplingRates("0.0.1", "0.0.1", pluginErrorRate = 0.5).pluginErrorSamplingRate(), 0.001)

    // When API is unavailable but we're not waiting for network, it should use cached value
    assertEquals(
      0.75,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        pluginErrorRate = 0.75,
      ).pluginErrorSamplingRate(),
      0.001,
    )

    // When API is unavailable and we're waiting for network, it should return default value
    assertEquals(
      0.0,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        pluginErrorRate = 0.75,
      ).pluginErrorSamplingRate(true),
      0.001,
    )
  }

  @Test
  fun testWebviewTraceSamplingRate() {
    // When the feature flag is not set, it should return the default value
    assertEquals(0.05, mockFlagAndPluginVersions("0.0.1", "0.0.1").webviewTraceSamplingRate(), 0.001)

    // When the feature flag is explicitly set, it should return that value
    assertEquals(
      0.25,
      mockFlagAndPluginVersionsWithSamplingRates("0.0.1", "0.0.1", webviewTraceRate = 0.25).webviewTraceSamplingRate(),
      0.001,
    )

    // When API is unavailable but we're not waiting for network, it should use cached value
    assertEquals(
      0.3,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        webviewTraceRate = 0.3,
      ).webviewTraceSamplingRate(),
      0.001,
    )

    // When API is unavailable and we're waiting for network, it should return default value
    assertEquals(
      0.0,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        webviewTraceRate = 0.3,
      ).webviewTraceSamplingRate(true),
      0.001,
    )
  }

  @Test
  fun testPluginTraceSamplingRate() {
    // When the feature flag is not set, it should return the default value
    assertEquals(0.05, mockFlagAndPluginVersions("0.0.1", "0.0.1").pluginTraceSamplingRate(), 0.001)

    // When the feature flag is explicitly set, it should return that value
    assertEquals(
      0.25,
      mockFlagAndPluginVersionsWithSamplingRates("0.0.1", "0.0.1", pluginTraceRate = 0.25).pluginTraceSamplingRate(),
      0.001,
    )

    // When API is unavailable but we're not waiting for network, it should use cached value
    assertEquals(
      0.3,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        pluginTraceRate = 0.3,
      ).pluginTraceSamplingRate(),
      0.001,
    )

    // When API is unavailable and we're waiting for network, it should return default value
    assertEquals(
      0.0,
      mockFlagAndPluginVersionsWithSamplingRates(
        "0.0.1",
        "0.0.1",
        apiAvailable = false,
        pluginTraceRate = 0.3,
      ).pluginTraceSamplingRate(true),
      0.001,
    )
  }

  private fun runCurrentPluginIsAtLeast(
    inputVersion: String?,
    pluginVersion: String?,
  ): Boolean {
    val manager = FeatureFlagManager.instance as FeatureFlagManagerImpl
    application.registerOrReplaceServiceInstance(
      PluginVersionProvider::class.java,
      object : PluginVersionProvider {
        override fun currentPluginVersion(): String? = pluginVersion

        override fun isBeta(): Boolean = false
      },
      testRootDisposable,
    )
    return runBlocking {
      manager.currentPluginVersionIsAtLeast(inputVersion)
    }
  }

  private fun mockFlagAndPluginVersions(
    flagVersion: String?,
    pluginVersion: String?,
    apiAvailable: Boolean = true,
    enableGuidelines: Boolean = false,
    enableUserGuidelines: Boolean = false,
    userGuidelinesLengthLimit: Int = FeatureFlags.DEFAULT_USER_GUIDELINES_LENGTH_LIMIT,
    userGuidelinesInSettings: Boolean = false,
    intellijEnableWorkspaceGuidelines: Boolean = false,
    enableSentry: Boolean = false,
  ): FeatureFlagManagerImpl {
    return mockFlagAndPluginVersionsWithSamplingRates(
      flagVersion,
      pluginVersion,
      apiAvailable,
      enableGuidelines,
      enableUserGuidelines,
      userGuidelinesLengthLimit,
      userGuidelinesInSettings,
      intellijEnableWorkspaceGuidelines,
      enableSentry,
    )
  }

  private fun mockFlagAndPluginVersionsWithSamplingRates(
    flagVersion: String?,
    pluginVersion: String?,
    apiAvailable: Boolean = true,
    enableGuidelines: Boolean = false,
    enableUserGuidelines: Boolean = false,
    userGuidelinesLengthLimit: Int = FeatureFlags.DEFAULT_USER_GUIDELINES_LENGTH_LIMIT,
    userGuidelinesInSettings: Boolean = false,
    intellijEnableWorkspaceGuidelines: Boolean = false,
    enableSentry: Boolean = false,
    webviewErrorRate: Double = 0.1,
    pluginErrorRate: Double = 0.1,
    webviewTraceRate: Double = 0.05,
    pluginTraceRate: Double = 0.05,
  ): FeatureFlagManagerImpl {
    val featureFlags = FeatureFlags()
    featureFlags.intellijChatMinVersion = flagVersion
    featureFlags.intellijForceCompletionMinVersion = flagVersion
    featureFlags.intellijShareMinVersion = flagVersion
    featureFlags.intellijNewThreadsMenuMinVersion = flagVersion
    featureFlags.intellijEnableChatMermaidDiagramsMinVersion = flagVersion
    featureFlags.intellijChatMultimodalMinVersion = flagVersion
    featureFlags.enableGuidelines = enableGuidelines
    featureFlags.intellijEnableUserGuidelines = enableUserGuidelines
    featureFlags.intellijUserGuidelinesInSettings = userGuidelinesInSettings
    featureFlags.userGuidelinesLengthLimit = userGuidelinesLengthLimit
    featureFlags.intellijEnableWorkspaceGuidelines = intellijEnableWorkspaceGuidelines
    featureFlags.intellijEnableSentry = enableSentry
    featureFlags.intellijWebviewErrorSamplingRate = webviewErrorRate
    featureFlags.intellijPluginErrorSamplingRate = pluginErrorRate
    featureFlags.intellijWebviewTraceSamplingRate = webviewTraceRate
    featureFlags.intellijPluginTraceSamplingRate = pluginTraceRate

    @Suppress("DEPRECATION")
    val mockAPI = MockAugmentAPI(flags = featureFlags, isAvailable = apiAvailable)
    application.registerOrReplaceServiceInstance(
      AugmentAPI::class.java,
      mockAPI,
      testRootDisposable,
    )
    application.registerOrReplaceServiceInstance(
      PluginVersionProvider::class.java,
      object : PluginVersionProvider {
        override fun currentPluginVersion(): String? = pluginVersion

        override fun isBeta(): Boolean = false
      },
      testRootDisposable,
    )

    return FeatureFlagManager.instance as FeatureFlagManagerImpl
  }
}

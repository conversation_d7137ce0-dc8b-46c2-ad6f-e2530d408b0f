package com.augmentcode.intellij.webviews.preferences

import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.FeatureFlagsTestUtil
import com.augmentcode.intellij.testutils.waitForAssertionAsync
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class PreferencesUtilsTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    System.clearProperty("augmentcode.preferencesCollectionEnabled")
  }

  override fun tearDown() {
    System.clearProperty("augmentcode.preferencesCollectionEnabled")
    super.tearDown()
  }

  // Tests for preferencesEnabled()

  @Test
  fun testPreferencesDisabledWhenCustomPropertyDisabled() =
    runBlocking {
      setupMockFeatureFlags(preferenceCollectionAllowed = true)

      // Wait for plugin context to be initialized with flags
      waitForAssertionAsync({
        assertNotNull(PluginStateService.instance.context?.flags)
      }, timeoutMs = 5000) // Increase from default 1000ms

      System.clearProperty("augmentcode.preferencesCollectionEnabled")

      assertFalse(PreferencesUtils.preferencesEnabled())
    }

  @Test
  fun testPreferencesDisabledWhenFeatureFlagDisabled() =
    runBlocking {
      setupMockFeatureFlags(preferenceCollectionAllowed = false)

      // Wait for plugin context to be initialized with flags
      waitForAssertionAsync({
        assertNotNull(PluginStateService.instance.context?.flags)
      }, timeoutMs = 5000) // Increase from default 1000ms

      System.setProperty("augmentcode.preferencesCollectionEnabled", "true")

      assertFalse(PreferencesUtils.preferencesEnabled())
    }

  @Test
  fun testPreferencesEnabledWhenBothConditionsMet() =
    runBlocking {
      setupMockFeatureFlags(preferenceCollectionAllowed = true, eloConfig = validEloConfig())

      // Wait for plugin context to be initialized with flags
      waitForAssertionAsync({
        assertNotNull(PluginStateService.instance.context?.flags)
      }, timeoutMs = 5000) // Increase from default 1000ms

      System.setProperty("augmentcode.preferencesCollectionEnabled", "true")

      assertTrue(PreferencesUtils.preferencesEnabled())
    }

  // Tests for hasSufficientComparableModels()

  @Test
  fun testHasSufficientComparableModelsReturnsFalseWhenInsufficientHighPriorityModels() =
    runBlocking {
      setupMockFeatureFlags(
        eloConfig = """{"highPriorityModels": ["model1"], "regularBattleModels": ["model1", "model2"], "highPriorityThreshold": 0.5}""",
      )

      // Wait for plugin context to be initialized with flags
      waitForAssertionAsync({
        assertNotNull(PluginStateService.instance.context?.flags)
      }, timeoutMs = 5000) // Increase from default 1000ms

      assertFalse(PreferencesUtils.hasSufficientComparableModels())
    }

  @Test
  fun testHasSufficientComparableModelsReturnsTrueWhenSufficientModels() =
    runBlocking {
      setupMockFeatureFlags(eloConfig = validEloConfig())

      // Wait for plugin context to be initialized with flags
      waitForAssertionAsync({
        assertNotNull(PluginStateService.instance.context?.flags)
      }, timeoutMs = 5000) // Increase from default 1000ms

      assertTrue(PreferencesUtils.hasSufficientComparableModels())
    }

  private fun validEloConfig() =
    """{"highPriorityModels": ["model1", "model2"], "regularBattleModels": ["model3", "model4"], "highPriorityThreshold": 0.5}"""

  private fun insufficientEloConfig() =
    """{"highPriorityModels": ["model1"], "regularBattleModels": ["model1", "model2"], "highPriorityThreshold": 0.5}"""

  private fun setupMockFeatureFlags(
    preferenceCollectionAllowed: Boolean? = null,
    eloConfig: String? = null,
  ) {
    val featureFlags = mutableMapOf<String, Any>()

    if (preferenceCollectionAllowed != null) {
      featureFlags["intellij_preference_collection_allowed_min_version"] = if (preferenceCollectionAllowed) "0.0.0" else ""
    }

    if (eloConfig != null) {
      // Escape the JSON string properly for inclusion in the feature flags
      val escapedConfig = eloConfig.replace("\"", "\\\"")
      featureFlags["elo_model_configuration"] = escapedConfig
    }

    FeatureFlagsTestUtil.setupFeatureFlags(testRootDisposable, featureFlags)
  }
}

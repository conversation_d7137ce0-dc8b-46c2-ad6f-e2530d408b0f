package com.augmentcode.intellij.api

import com.augmentcode.api.FindMissingRequest
import com.augmentcode.api.NumericEnum
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.google.gson.FieldNamingPolicy
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import io.ktor.client.engine.mock.*
import io.ktor.client.engine.mock.MockEngine
import io.ktor.http.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.lang.reflect.Type

@RunWith(JUnit4::class)
class AugmentApiTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.modelName = null
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  @Test
  fun testFindMissing() {
    val gson = createGson()

    val mockEngine =
      MockEngine { request ->
        assertEquals("POST", request.method.value)
        assertEquals("/find-missing", request.url.encodedPath)

        val requestBody = request.body.toByteArray().decodeToString()
        val findRequest = gson.fromJson(requestBody, FindMissingRequest::class.java)
        assertEquals("test-model", findRequest.model)

        respond(
          content =
            """
            {
              "unknown_memory_names": ["blob1"],
              "nonindexed_blob_names": ["blob2"]
            }
            """.trimIndent(),
          status = HttpStatusCode.OK,
          headers = headersOf(HttpHeaders.ContentType, "application/json"),
        )
      }

    augmentHelpers().registerMockEngine(mockEngine)

    val api = AugmentAPI.instance
    runBlocking {
      val request =
        FindMissingRequest().apply {
          model = "test-model"
        }
      val response = api.findMissing(request)

      assertEquals(setOf("blob1"), response.unknownMemoryNames)
      assertEquals(setOf("blob2"), response.nonindexedBlobNames)
    }
  }

  @Test
  fun testFindMissingNonSuccessful() {
    val mockEngine =
      MockEngine { request ->
        assertEquals("POST", request.method.value)
        assertEquals("/find-missing", request.url.encodedPath)

        respond(
          content =
            """
            {
              "error": "Something went wrong"
            }
            """.trimIndent(),
          status = HttpStatusCode.InternalServerError,
          headers = headersOf(HttpHeaders.ContentType, "application/json"),
        )
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    val api = AugmentAPI.instance
    runBlocking {
      val request =
        FindMissingRequest().apply {
          model = "test-model"
        }
      val response = api.findMissing(request)

      assertTrue(response.unknownMemoryNames.isEmpty())
      assertTrue(response.nonindexedBlobNames.isEmpty())
    }
  }

  @Test
  fun testNetworkErrorIllegalState() {
    val mockEngine =
      MockEngine { _ ->
        throw IllegalStateException("Simulated network error")
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    val api = AugmentAPI.instance
    runBlocking {
      // We use FindMissing just because it's a simple endpoint.
      // What we really care about testing is the response handling
      // that is common to all our endpoints
      val request =
        FindMissingRequest().apply {
          model = "test-model"
          memObjectNames = listOf("blob1", "blob2")
        }

      // Using fully qualified import because it conflicts with Intellij's default assertThrows
      val exception =
        org.junit.Assert.assertThrows(IllegalStateException::class.java) {
          runBlocking { api.findMissing(request) }
        }

      assertTrue(exception.message?.contains("Failed to make network call to find-missing") == true)
      assertTrue(exception.cause?.message == "Simulated network error")
    }
  }

  private fun createGson(): Gson {
    val enumSerializer =
      object : JsonSerializer<NumericEnum> {
        override fun serialize(
          src: NumericEnum,
          typeOfSrc: Type,
          context: JsonSerializationContext,
        ): JsonElement {
          return JsonPrimitive(src.getValue())
        }
      }

    return GsonBuilder()
      .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
      .registerTypeHierarchyAdapter(Enum::class.java, enumSerializer)
      .create()
  }

  @Test
  fun testNoBaseUrl() {
    // Clear the completion URL setting
    AugmentSettings.instance.completionURL = null

    val mockEngine =
      MockEngine { _ ->
        throw IllegalStateException("We shouldn't reach here")
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    val api = AugmentAPI.instance
    runBlocking {
      val request =
        FindMissingRequest().apply {
          model = "test-model"
          memObjectNames = listOf("blob1", "blob2")
        }

      val exception =
        org.junit.Assert.assertThrows(IllegalStateException::class.java) {
          runBlocking { api.findMissing(request) }
        }

      assertTrue(exception.message?.contains("Base URL is not set") == true)
    }
  }
}

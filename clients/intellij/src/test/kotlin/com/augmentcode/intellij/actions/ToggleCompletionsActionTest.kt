package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.ActionUiKind
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.actionSystem.Presentation
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class ToggleCompletionsActionTest : BasePlatformTestCase() {
  private lateinit var action: ToggleCompletionsAction

  override fun setUp() {
    super.setUp()
    action = ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ToggleCompletionsAction") as ToggleCompletionsAction
  }

  private fun createEvent(): AnActionEvent {
    val presentation = Presentation()
    val dataContext = DataContext { presentation }
    return AnActionEvent.createEvent(dataContext, presentation, "", ActionUiKind.NONE, null)
  }

  private fun doAction(event: AnActionEvent) {
    action.actionPerformed(event)
    action.update(event)
  }

  @Test
  fun testToggle() {
    val event = createEvent()
    doAction(event)
    assertEquals(false, AugmentSettings.instance.inlineCompletionEnabled)
    assertEquals(AugmentBundle.message("actions.completions.enable"), event.presentation.text)
    doAction(event)
    assertEquals(true, AugmentSettings.instance.inlineCompletionEnabled)
    assertEquals(AugmentBundle.message("actions.completions.disable"), event.presentation.text)
  }
}

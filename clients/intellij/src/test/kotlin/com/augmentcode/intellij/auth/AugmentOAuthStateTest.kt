package com.augmentcode.intellij.auth

import com.augmentcode.api.OnboardingSessionEventName
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.toList
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentOAuthStateTest : AugmentBasePlatformTestCase() {
  @Test
  fun testSaveGetClear() =
    runBlocking {
      val initialCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(initialCredentials, null)

      val exampleCredentials = AugmentCredentials("example access token", "example tenant URL")
      AugmentOAuthState.instance.saveCredentials(exampleCredentials)

      val gotCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(gotCredentials?.accessToken, exampleCredentials.accessToken)
      assertEquals(gotCredentials?.tenantURL, exampleCredentials.tenantURL)

      AugmentOAuthState.instance.clear()

      val clearedCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(clearedCredentials, null)
    }

  @Test
  fun testOnboardingMessageOnSignIn() =
    runBlocking {
      val initialCredentials = AugmentOAuthState.instance.getCredentials()
      assertEquals(initialCredentials, null)

      @Suppress("DEPRECATION")
      val mockAPI = MockAugmentAPI()
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        mockAPI,
        testRootDisposable,
      )
      val messagingService = ChatMessagingService.getInstance(project)

      // load main panel to make the messaging service subscribe to credentials changes
      val messageText =
        """
        {
          "type":"main-panel-loaded",
          "requestId":"abc-123",
          "error":null
        }
        """.trimIndent()
      messagingService.processMessageFromWebview(messageText).toList()

      // simulate sign in
      val exampleCredentials = AugmentCredentials("example access token", "example tenant URL")
      AugmentOAuthState.instance.saveCredentials(exampleCredentials)

      // check that the sign-in event was sent to the API
      waitForAssertion({
        assertTrue(mockAPI.recorderLastOnboardingEvents.any { it.event_name == OnboardingSessionEventName.SignedIn.apiName })
      })
    }
}

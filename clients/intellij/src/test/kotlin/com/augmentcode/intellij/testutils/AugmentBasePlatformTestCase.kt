package com.augmentcode.intellij.testutils

import com.intellij.testFramework.fixtures.BasePlatformTestCase

abstract class AugmentBasePlatformTestCase : BasePlatformTestCase() {
  private lateinit var augmentHelpers: AugmentTestHelpers

  override fun setUp() {
    super.setUp()
    augmentHelpers = AugmentTestHelpers(myFixture.project, myFixture.testRootDisposable)
    augmentHelpers.setUp()
  }

  override fun tearDown() {
    augmentHelpers.tearDown()

    super.tearDown()

    // Final cleanup after all registered services are unregistered
    AugmentTestHelpers.cleanup()
  }

  fun augmentHelpers(): AugmentTestHelpers = augmentHelpers
}

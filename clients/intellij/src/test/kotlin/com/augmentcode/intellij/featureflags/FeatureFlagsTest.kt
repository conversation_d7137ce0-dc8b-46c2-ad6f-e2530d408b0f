package com.augmentcode.intellij.featureflags

import com.augmentcode.api.ModelConfig
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.ide.plugins.IdeaPluginDescriptor
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.extensions.PluginId
import io.mockk.every
import io.mockk.mockkStatic
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FeatureFlagsTest : AugmentBasePlatformTestCase() {
  private companion object {
    const val TEST_PLUGIN_VERSION = "0.0.2"
  }

  override fun setUp() {
    super.setUp()
    setupMockPluginVersion(TEST_PLUGIN_VERSION)
  }

  @Test
  fun testFromModelConfig() {
    // These tests assume the test plugin version is 0.0.2
    assertEquals(TEST_PLUGIN_VERSION, "0.0.2")

    val pluginData = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
    assertNotNull(pluginData)
    assertEquals(TEST_PLUGIN_VERSION, pluginData?.version)

    val flagsFromAPI = com.augmentcode.api.FeatureFlags()
    // This is before the current plugin version, so it should be true
    flagsFromAPI.intellijChatMultimodalMinVersion = "0.0.1"
    // This is equal to the current plugin version, so it should be true
    flagsFromAPI.intellijAgentModeMinVersion = "0.0.2"
    // This is after the current plugin version, so it should be false
    flagsFromAPI.intellijShareMinVersion = "0.0.3"

    flagsFromAPI.bypassLanguageFilter = true
    flagsFromAPI.additionalChatModels = """{"model1":"value1","model2":"value2"}"""

    val modelConfig =
      ModelConfig().apply {
        featureFlags = flagsFromAPI
      }

    assertEquals(DefaultFeatureFlags.userGuidelinesEnabled, false)

    val newFlags = FeatureFlags.fromModelConfig(modelConfig)

    assertEquals("Chat multimodal enabled should be set correctly", true, newFlags.chatMultimodalEnabled)
    assertEquals("Force completion enabled should be set correctly", true, newFlags.agentModeEnabled)
    assertEquals("Share service enabled should be set correctly", false, newFlags.shareServiceEnabled)

    assertEquals("Bypass language filter should be set correctly", true, newFlags.bypassLanguageFilter)
    assertEquals("Additional chat models should be set correctly", 2, newFlags.additionalChatModels.size)
    assertEquals(
      "User guidelines enabled should be set correctly",
      DefaultFeatureFlags.userGuidelinesEnabled,
      newFlags.userGuidelinesEnabled,
    )
  }

  @Test
  fun testFailingPluginLookup() {
    val flagsFromAPI = com.augmentcode.api.FeatureFlags()
    // This is equal to the current plugin version, so it should be true
    flagsFromAPI.intellijAgentModeMinVersion = TEST_PLUGIN_VERSION

    flagsFromAPI.bypassLanguageFilter = true
    flagsFromAPI.additionalChatModels = """{"model1":"value1","model2":"value2"}"""

    val modelConfig =
      ModelConfig().apply {
        featureFlags = flagsFromAPI
      }

    setupMockPluginVersion(null)
    val flags = FeatureFlags.fromModelConfig(modelConfig)

    // It'll be false because we failed to lookup the plugin version
    assertEquals(flags.agentModeEnabled, false)
  }

  @Test
  fun testIsMinVersionAtLeast() {
    // These are typical version values
    assertEquals(true, FeatureFlags.isMinVersionAtLeast("0.0.2", "0.0.1"))
    assertEquals(true, FeatureFlags.isMinVersionAtLeast("0.0.2", "0.0.2"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.2", "0.0.3"))

    // These are edge cases of null or empty values
    assertEquals(false, FeatureFlags.isMinVersionAtLeast(null, "0.0.1"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.1", null))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("", "0.0.1"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.1", ""))

    // These are edge cases of invalid semver strings
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("This is not a valid semver string", "0.0.1"))
    assertEquals(false, FeatureFlags.isMinVersionAtLeast("0.0.1", "This is not a valid semver string"))
  }
}

private fun setupMockPluginVersion(version: String?) {
  // Mock the PluginManager to return our test plugin version
  mockkStatic(PluginManager::class)
  val mockPluginManager = io.mockk.mockk<PluginManager>()

  // Set up the chain of calls
  every { PluginManager.getInstance() } returns mockPluginManager

  if (version == null) {
    every { mockPluginManager.findEnabledPlugin(PluginId.getId("com.augmentcode")) } returns null
  } else {
    val mockPlugin = io.mockk.mockk<IdeaPluginDescriptor>()
    every { mockPluginManager.findEnabledPlugin(PluginId.getId("com.augmentcode")) } returns mockPlugin
    every { mockPlugin.version } returns version
  }
}

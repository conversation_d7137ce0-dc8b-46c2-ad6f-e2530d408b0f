package com.augmentcode.intellij.status

import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentStatusBarTest : AugmentBasePlatformTestCase() {
  @Test
  fun testAuthStateChanges() {
    val credentials = runBlocking { AugmentOAuthState.instance.getCredentials() }
    assertEquals(credentials, null)

    val statusBar = AugmentStatusBar(project)

    waitAndAssertStatusBar(statusBar, StateDefinitions.SignInNeeded)

    // Sign in user
    val exampleCredentials = AugmentCredentials("example access token", "http://example.augmentcode.com")

    // Add credentials
    AugmentOAuthState.instance.saveCredentials(exampleCredentials)

    waitAndAssertStatusBar(statusBar, StateDefinitions.Enabled)

    // Sign out user
    AugmentOAuthState.instance.clear()

    waitAndAssertStatusBar(statusBar, StateDefinitions.SignInNeeded)
  }

  @Test
  fun testSettingsChanges() {
    // Sign in user
    val exampleCredentials = AugmentCredentials("example access token", "http://example.augmentcode.com")
    AugmentOAuthState.instance.saveCredentials(exampleCredentials)

    val statusBar = AugmentStatusBar(project)

    waitAndAssertStatusBar(statusBar, StateDefinitions.Enabled)

    AugmentSettings.instance.inlineCompletionEnabled = false
    waitAndAssertStatusBar(statusBar, StateDefinitions.AutoCompletionsDisabled)

    AugmentSettings.instance.inlineCompletionEnabled = true
    waitAndAssertStatusBar(statusBar, StateDefinitions.Enabled)
  }

  private fun waitAndAssertStatusBar(
    statusBar: AugmentStatusBar,
    expectedDefinition: StateDefinition,
  ) {
    // Wait for status bar to update
    waitForAssertion({
      assertEquals(expectedDefinition.tooltip, statusBar.toolTip())
      assertEquals(expectedDefinition.icon, statusBar.icon())
    }, timeoutMs = 5000)
  }
}

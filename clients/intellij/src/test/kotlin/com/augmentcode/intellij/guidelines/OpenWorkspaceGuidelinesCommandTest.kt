package com.augmentcode.intellij.guidelines

import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.FeatureFlagsTestUtil
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.testFramework.PlatformTestUtil
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

/**
 * Tests for the OpenWorkspaceGuidelinesCommand class.
 * This class tests the functionality of opening workspace guidelines files.
 *
 * These tests use FeatureFlagsTestUtil to mock the FeatureFlagManager's underlying API calls.
 */
@RunWith(JUnit4::class)
class OpenWorkspaceGuidelinesCommandTest : AugmentBasePlatformTestCase() {
  private lateinit var guidelinesService: GuidelinesService
  private lateinit var featureFlags: FeatureFlagManager

  override fun setUp() {
    super.setUp()

    // Get the guidelines service
    guidelinesService = project.service<GuidelinesService>()

    // Set up feature flags
    featureFlags = FeatureFlagManager.instance
  }

  /**
   * Helper method to set up feature flags for testing
   * Uses FeatureFlagsTestUtil to mock the FeatureFlagManager's underlying API calls
   */
  private fun setupFeatureFlags(enableGuidelines: Boolean) {
    // Setup feature flag to enable/disable guidelines

    // Set up feature flags using the test utility
    FeatureFlagsTestUtil.setupFeatureFlags(
      testRootDisposable,
      mapOf(
        "enable_guidelines" to enableGuidelines,
        "intellij_enable_workspace_guidelines" to enableGuidelines,
      ),
    )

    // Verify that the feature flags were set correctly
    val actualEnabled = FeatureFlagManager.instance.guidelinesEnabled()
    assertEquals("Feature flag for guidelines enabled should be set correctly", enableGuidelines, actualEnabled)
  }

  /**
   * Test that the action is enabled when guidelines are enabled.
   */
  @Test
  fun testActionEnabledWhenGuidelinesEnabled() {
    // Setup feature flag to enable guidelines
    setupFeatureFlags(enableGuidelines = true)

    val presentation = myFixture.testAction(OpenWorkspaceGuidelinesCommand())
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify the presentation was updated to enabled
    assertTrue("Action should be enabled", presentation.isEnabled)
  }

  /**
   * Test that the action is disabled when guidelines are disabled.
   */
  @Test
  fun testActionDisabledWhenGuidelinesDisabled() {
    // Setup feature flag to disable guidelines
    setupFeatureFlags(enableGuidelines = false)

    val presentation = myFixture.testAction(OpenWorkspaceGuidelinesCommand())
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Verify the presentation was updated to disabled
    assertFalse("Action should be disabled", presentation.isEnabled)
  }

  /**
   * Test that the action opens workspace guidelines in the editor
   */
  @Test
  fun testActionOpensWorkspaceGuidelines() {
    // Setup feature flag to disable guidelines
    setupFeatureFlags(enableGuidelines = true)

    myFixture.addFileToProject(".augment-guidelines", "Existing guidelines content")
    assertEmpty(FileEditorManager.getInstance(project).openFiles)

    myFixture.testAction(OpenWorkspaceGuidelinesCommand())
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Workspace guidelines file should be opened in editor",
      openFiles.any { it.name == ".augment-guidelines" },
    )
  }

  /**
   * Test that the action correctly handles non-existent guidelines file
   * by creating it and opening it in the editor
   */
  @Test
  fun testActionCreatesAndOpensNonExistentWorkspaceGuidelines() {
    // Setup feature flag to enable guidelines
    setupFeatureFlags(enableGuidelines = true)

    myFixture.testAction(OpenWorkspaceGuidelinesCommand())
    PlatformTestUtil.dispatchAllInvocationEventsInIdeEventQueue()

    // Assert - check that the file is open in the editor
    val openFiles = FileEditorManager.getInstance(project).openFiles
    assertTrue(
      "Workspace guidelines file should be opened in editor",
      openFiles.any { it.name == ".augment-guidelines" },
    )
  }
}

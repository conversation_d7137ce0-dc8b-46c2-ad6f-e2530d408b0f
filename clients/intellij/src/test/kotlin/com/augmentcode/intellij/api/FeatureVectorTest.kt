package com.augmentcode.intellij.api

import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.util.HttpUtil
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import io.ktor.utils.io.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FeatureVectorTest : BasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.completionURL = "https://api.augmentcode.com"
  }

  @Test
  fun testLogFeatureVector() {
    var reportFeatureVectorCalled = false
    var receivedFeatureVector: Map<Int, String>? = null

    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/report-feature-vector" -> {
            reportFeatureVectorCalled = true
            val body = request.body.toByteArray().decodeToString()
            // Parse the JSON body to verify the structure
            assertTrue(body.contains("client_name"))
            assertTrue(body.contains("intellij-extension"))
            assertTrue(body.contains("feature_vector"))
            
            respond(
              content = ByteReadChannel("{}"),
              status = HttpStatusCode.OK,
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
          else -> {
            error("Unhandled ${request.url.encodedPath}")
          }
        }
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    val api = AugmentAPI.instance
    runBlocking {
      // Create a sample feature vector
      val featureVector = mapOf(
        0 to "hash1",
        1 to "hash2",
        2 to "hash3",
        12 to "checksum"
      )

      // Call the API
      api.logFeatureVector(featureVector)

      // Verify the API was called
      assertTrue(reportFeatureVectorCalled)
    }
  }

  @Test
  fun testLogFeatureVectorFailure() {
    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/report-feature-vector" -> {
            respond(
              content = ByteReadChannel("Internal Server Error"),
              status = HttpStatusCode.InternalServerError,
              headers = headersOf(HttpHeaders.ContentType, "text/plain"),
            )
          }
          else -> {
            error("Unhandled ${request.url.encodedPath}")
          }
        }
      }

    HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)

    val api = AugmentAPI.instance
    runBlocking {
      // Create a sample feature vector
      val featureVector = mapOf(
        0 to "hash1",
        1 to "hash2",
        2 to "hash3"
      )

      // Call the API - should not throw even on failure
      api.logFeatureVector(featureVector)
      
      // Test passes if no exception is thrown
    }
  }
}

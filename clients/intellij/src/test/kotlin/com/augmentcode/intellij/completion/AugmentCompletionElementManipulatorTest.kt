package com.augmentcode.intellij.completion

import com.augmentcode.api.OnboardingSessionEventName
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.generateCompletionResult
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@Suppress("UnstableApiUsage")
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentCompletionElementManipulatorTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  @Test
  fun testCompletionReuse() {
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(completionResult),
        testRootDisposable,
      )

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      typeChar('H')
      delay() // so everything is processed
      typeChar('e')
      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")
    }
  }

  @Test
  fun testCompletionAcceptanceReporting() {
    myFixture.testInlineCompletion {
      val completionText = "Hello from Augment!\")"
      val completionResult = generateCompletionResult(completionText)

      @Suppress("DEPRECATION")
      val mockApi = MockAugmentAPI(completionResult)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        mockApi,
        testRootDisposable,
      )

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay()
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      waitForAssertion({
        assertTrue(mockApi.recorderLastOnboardingEvents.any { it.event_name == OnboardingSessionEventName.AcceptedCompletion.apiName })
      })
    }
  }
}

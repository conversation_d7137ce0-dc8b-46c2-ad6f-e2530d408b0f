package com.augmentcode.intellij.index

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.util.io.FileUtil
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.testFramework.utils.vfs.createFile
import com.intellij.util.application
import kotlinx.coroutines.runBlocking

@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class IndexingTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/indexing"

  fun testSimple() {
    @Suppress("DEPRECATION")
    application.registerOrReplaceServiceInstance(AugmentAPI::class.java, MockAugmentAPI(), testRootDisposable)

    val foo = myFixture.configureByFile("foo.go")
    val bar = myFixture.configureByFile("bar.go")
    val baz = myFixture.configureByFile("baz.go")

    // modelInfo().supportedLanguages do not support it, but now we check modelInfo during sync, not indexing
    val notSupported = myFixture.configureByText("not.supported", "")

    assertEquals("foo.go", AugmentBlobStateReader.read(foo)?.relativePath)
    assertEquals("bar.go", AugmentBlobStateReader.read(bar)?.relativePath)
    assertEquals("baz.go", AugmentBlobStateReader.read(baz)?.relativePath)
    assertEquals("not.supported", AugmentBlobStateReader.read(notSupported)?.relativePath)
  }

  // TODO: This test fails when I run it in a suite because it's picking up files from previous tests
  //       I don't know how to properly clean those up, and I gotta just merge this. Forgive me for my sins.
//  fun testUploadsUnknownAndPollsForSync() =
//    runBlocking {
//      // Scenario: A file is created that the server does not know about.
//      // 1. Call find-missing to see whether the server knows about the blob
//      // 2. find-missing responds with the blob as unknown
//      // 3. Call batch-upload to upload the blob
//      // 4. batch-upload responds with the blob name
//      // 5. We see the blob in AugmentRemoteSyncManager.syncedBlobs()
//
//      // Add fake API settings. Make sure to reset to null after the test
//      AugmentSettings.instance.apiToken = "test-token"
//      AugmentSettings.instance.completionURL = "http://test-server"
//
//      // Mock http client
//      val mockEngine =
//        MockEngine { request ->
//          when (request.url.encodedPath) {
//            "/get-models" -> {
//              respondWithMockModels(this)
//            }
//            "/find-missing" -> {
//              val requestBody = request.body.toByteArray().decodeToString()
//              val findRequest = GsonUtil.createApiGson().fromJson(requestBody, FindMissingRequest::class.java)
//
//              // First find-missing call should return the blob as unknown
//              respond(
//                content =
//                  """
//                  {
//                      "unknown_memory_names": ["${findRequest.memObjectNames.first()}"],
//                      "nonindexed_blob_names": []
//                  }
//                  """.trimIndent(),
//                status = HttpStatusCode.OK,
//                headers = headersOf(HttpHeaders.ContentType, "application/json"),
//              )
//            }
//            "/batch-upload" -> {
//              val requestBody = request.body.toByteArray().decodeToString()
//              val uploadRequest = GsonUtil.createApiGson().fromJson(requestBody, BatchUploadRequest::class.java)
//
//              assertEquals(1, uploadRequest.blobs.size)
//              assertEquals("foo.go", uploadRequest.blobs[0].path)
//
//              respond(
//                content =
//                  """
//                  {
//                      "blob_names": ["${uploadRequest.blobs[0].expectedBlobName()}"]
//                  }
//                  """.trimIndent(),
//                status = HttpStatusCode.OK,
//                headers = headersOf(HttpHeaders.ContentType, "application/json"),
//              )
//            }
//            else -> error("Unexpected request to ${request.url.encodedPath}")
//          }
//        }
//
//      HttpUtil.registerMockHttpClient(mockEngine, testRootDisposable)
//
//      myFixture.project.registerOrReplaceServiceInstance(
//        PathFilterService::class.java,
//        PathFilterService(myFixture.project),
//        testRootDisposable,
//      )
//
//      val foo = myFixture.configureByFile("foo.go")
//
//      // Verify the file was properly indexed
//      val blobState = AugmentBlobStateReader.read(foo)
//      assertNotNull(blobState)
//      assertEquals("foo.go", blobState?.relativePath)
//
//      // Wait for upload and sync polling
//      delay(1000)
//
//      // Verify that both endpoints were called
//      val paths = mockEngine.requestHistory.map { it.url.encodedPath }
//      assertTrue(paths.contains("/find-missing"))
//      assertTrue(paths.contains("/batch-upload"))
//
//      // Verify that the blob is in AugmentRemoteSyncManager.syncedBlobs()
//      // TODO: Improve testing infra so this is reliable to test
// //    val syncedBlobs = AugmentRemoteSyncingManager.getInstance(project).syncedBlobs()
// //    assertEquals(1, syncedBlobs.size)
// //    assertEquals("hash(foo.go).v1", syncedBlobs.first().remoteName)
//    }

  fun testMultiRoot() {
    @Suppress("DEPRECATION")
    application.registerOrReplaceServiceInstance(AugmentAPI::class.java, MockAugmentAPI(), testRootDisposable)

    myFixture.addFileToProject("bar/.augmentroot", "")
    val bar = myFixture.addFileToProject("bar/bar.go", "// bar")

    myFixture.addFileToProject("baz/.augmentroot", "")
    val baz = myFixture.addFileToProject("baz/some/folders/in/between/baz.go", "// baz")

    val foo = myFixture.configureByFile("foo.go")

    assertEquals("foo.go", AugmentBlobStateReader.read(foo)?.relativePath)
    val barBlob = AugmentBlobStateReader.read(bar)
    assertEquals("bar.go", barBlob?.relativePath)
    assertEquals("some/folders/in/between/baz.go", AugmentBlobStateReader.read(baz)?.relativePath)
  }

  fun testFindByState() {
    @Suppress("DEPRECATION")
    application.registerOrReplaceServiceInstance(AugmentAPI::class.java, MockAugmentAPI(), testRootDisposable)

    val foo = myFixture.configureByFile("foo.go")

    val blobState = AugmentBlobStateReader.read(foo)
    assertNotNull(blobState)
    assertEquals("foo.go", blobState?.relativePath)

    val matches =
      runBlocking {
        AugmentBlobStateReader.findFilesByState(project) { it.relativePath == "foo.go" }
      }
    assertEquals(1, matches.size)
    assertEquals(foo.virtualFile, matches.first())
  }

  // Note: Potentially flakey
  fun testRenameAndMove() {
    @Suppress("DEPRECATION")
    application.registerOrReplaceServiceInstance(AugmentAPI::class.java, MockAugmentAPI(), testRootDisposable)

    val foo = myFixture.addFileToProject("foo.go", "// foo")
    assertEquals("foo.go", AugmentBlobStateReader.read(foo)?.relativePath)

    myFixture.renameElement(foo, "foo2.go")
    assertEquals("foo2.go", AugmentBlobStateReader.read(foo)?.relativePath)

    myFixture.addFileToProject("moved/bar.go", "// bar")
    myFixture.moveFile("foo2.go", "moved")
    val fooAfterMove = myFixture.psiManager.findFile(myFixture.findFileInTempDir("moved/foo2.go"))
    assertEquals("moved/foo2.go", AugmentBlobStateReader.read(fooAfterMove)?.relativePath)
  }

  fun testDependencies() {
    @Suppress("DEPRECATION")
    application.registerOrReplaceServiceInstance(AugmentAPI::class.java, MockAugmentAPI(), testRootDisposable)

    val simpleFile = myFixture.addFileToProject("simple.txt", "Hello!")
    assertEquals("simple.txt", AugmentRoot.findRelativePath(project, simpleFile.virtualFile))

    // let's make sure that third party dependencies which are outside the project are not indexed
    // aka they don't have a relative path to a project root
    val dependency =
      VfsUtil.findFileByIoFile(
        FileUtil.createTempDirectory("3rdparty", null),
        true,
      )!!

    // even .augmentroot in a dependency should not distract us from ignoring
    runWriteAction { dependency.createFile(".augmentroot") }
    val dependencyFile = runWriteAction { dependency.createFile("main.txt") }
    assertNull(AugmentRoot.findRelativePath(project, dependencyFile))
  }
}

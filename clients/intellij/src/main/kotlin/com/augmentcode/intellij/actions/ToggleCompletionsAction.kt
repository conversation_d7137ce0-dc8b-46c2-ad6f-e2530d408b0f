package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware

class ToggleCompletionsAction : AnAction(), DumbAware {
  override fun actionPerformed(e: AnActionEvent) {
    AugmentSettings.instance.inlineCompletionEnabled = !AugmentSettings.instance.inlineCompletionEnabled
  }

  override fun update(e: AnActionEvent) {
    e.presentation.text =
      if (AugmentSettings.instance.inlineCompletionEnabled) {
        AugmentBundle.message("actions.completions.disable")
      } else {
        AugmentBundle.message("actions.completions.enable")
      }
  }

  override fun getActionUpdateThread(): ActionUpdateThread {
    return ActionUpdateThread.EDT
  }
}

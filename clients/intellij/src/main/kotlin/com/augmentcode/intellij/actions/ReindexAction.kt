package com.augmentcode.intellij.actions

import com.augmentcode.intellij.index.AugmentLocalIndex
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware
import kotlinx.coroutines.runBlocking

/**
 * An action that triggers a rebuild of the Augment local index.
 * This can be useful for debugging or when the index gets out of sync.
 */
class ReindexAction : AnAction(), DumbAware {
  override fun actionPerformed(e: AnActionEvent) {
    val project = e.project ?: return

    runBlocking {
      AugmentRemoteSyncingManager.getInstance(project).reset()
    }

    AugmentLocalIndex.requestRebuild()
  }
}

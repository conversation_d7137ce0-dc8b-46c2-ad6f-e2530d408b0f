package com.augmentcode.intellij.actions

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.ide.DataManager
import com.intellij.ide.plugins.PluginManager
import com.intellij.notification.NotificationGroupManager
import com.intellij.notification.NotificationType
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.EDT
import com.intellij.openapi.application.PermanentInstallationID
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.ide.CopyPasteManager
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.dsl.builder.panel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.jetbrains.concurrency.await
import java.awt.datatransfer.StringSelection
import java.awt.event.ActionEvent
import javax.swing.AbstractAction
import javax.swing.Action
import javax.swing.JComponent
import kotlin.reflect.full.declaredMemberProperties

class ExtensionStatusAction : AnAction(), DumbAware {
  override fun actionPerformed(e: AnActionEvent) {
    val cs = CoroutineScope(Dispatchers.IO)
    cs.launch {
      val plugin = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
      val info = ApplicationInfo.getInstance()
      val credentials = AugmentOAuthState.instance.getCredentials()
      val syncManager = AugmentRemoteSyncingManager.getInstance(e.project!!)
      val osName = System.getProperty("os.name")
      val osVersion = System.getProperty("os.version")
      val osArch = System.getProperty("os.arch")

      val settingsList =
        AugmentSettings::class.declaredMemberProperties.map {
          val value = it.get(AugmentSettings.instance) ?: ""
          Entry.KeyValue(it.name, value.toString())
        }

      val sections =
        arrayOf(
          // Plugin details
          ExtensionStatusSection(
            AugmentBundle.message("extensionStatus.section.plugin.title"),
            arrayOf(
              Entry.KeyValue(AugmentBundle.message("extensionStatus.section.plugin.versionField"), plugin?.version ?: "snapshot"),
              Entry.KeyValue(AugmentBundle.message("extensionStatus.section.plugin.intellijField"), info.fullApplicationName),
              Entry.KeyValue(AugmentBundle.message("extensionStatus.section.plugin.osField"), "$osName $osVersion ($osArch)"),
            ),
          ),
          // Session details
          ExtensionStatusSection(
            AugmentBundle.message("extensionStatus.section.session.title"),
            arrayOf(
              Entry.KeyValue(AugmentBundle.message("extensionStatus.section.session.idField"), PermanentInstallationID.get()),
            ),
          ),
          // Context details
          ExtensionStatusSection(
            AugmentBundle.message("extensionStatus.section.context.title"),
            arrayOf(
              Entry.KeyValue(
                AugmentBundle.message("extensionStatus.section.context.blobsIndexedAmount"),
                syncManager.indexSize().toString(),
              ),
              Entry.KeyValue(
                AugmentBundle.message("extensionStatus.section.context.blobsAwaitingAmount"),
                syncManager.queueSize().toString(),
              ),
            ),
          ),
          // Configuration
          ExtensionStatusSection(
            AugmentBundle.message("extensionStatus.section.configuration.title"),
            settingsList.toTypedArray(),
          ),
          // Auth
          ExtensionStatusSection(
            AugmentBundle.message("extensionStatus.section.auth.title"),
            arrayOf(
              Entry.KeyValue(
                AugmentBundle.message("extensionStatus.section.auth.hasOAuthCredentials"),
                (credentials != null).toString(),
              ),
              Entry.KeyValue(
                AugmentBundle.message("extensionStatus.section.auth.oauthTenantURL"),
                credentials?.tenantURL ?: "",
              ),
            ),
          ),
        )

      cs.launch(Dispatchers.EDT) {
        ExtensionStatusDialog(sections).show()
      }
    }
  }
}

class ExtensionStatusDialog(private val sections: Array<ExtensionStatusSection>) : DialogWrapper(null, false) {
  private val copyToClipboardAction: CopyToClipboardAction

  init {
    title = AugmentBundle.message("extensionStatus.title")

    copyToClipboardAction = CopyToClipboardAction(sections)

    init()
  }

  override fun createCenterPanel(): JComponent =
    panel {
      for (s in sections) {
        group(s.sectionTitle) {
          for (f in s.fields) {
            when (f) {
              is Entry.KeyValue ->
                row(f.key) {
                  text(f.value)
                }
              is Entry.Line -> row(f.value) {}
            }
          }
        }
      }
    }

  override fun createActions(): Array<Action> {
    return arrayOf(copyToClipboardAction, okAction)
  }

  class CopyToClipboardAction(private val sections: Array<ExtensionStatusSection>) : AbstractAction("Copy to Clipboard") {
    override fun actionPerformed(e: ActionEvent?) {
      val sb = StringBuilder()
      for (s in sections) {
        sb.append("================\n")
        sb.append(s.sectionTitle + "\n")
        for (f in s.fields) {
          when (f) {
            is Entry.KeyValue -> sb.append("    ${f.key}: ${f.value}")
            is Entry.Line -> sb.append("    ${f.value}")
          }
          sb.append("\n")
        }

        sb.append("\n")
      }
      CopyPasteManager.getInstance().setContents(StringSelection(sb.toString()))

      runBlocking {
        val ctx = DataManager.getInstance().dataContextFromFocusAsync.await()
        val project = CommonDataKeys.PROJECT.getData(ctx)

        NotificationGroupManager.getInstance()
          .getNotificationGroup("augment.notifications")
          .createNotification("Extension status copied to clipboard", NotificationType.INFORMATION)
          .notify(project)
      }
    }
  }
}

sealed class Entry {
  data class KeyValue(val key: String, val value: String) : Entry()

  data class Line(val value: String) : Entry()
}

class ExtensionStatusSection(val sectionTitle: String, val fields: Array<Entry>)


package com.augmentcode.intellij.settings

import com.augmentcode.intellij.webviews.parseJsonToProto
import com.augmentcode.intellij.webviews.serializeProtoToJson
import com.augmentcode.rpc.GetStoredMCPServersResponse
import com.augmentcode.sidecar.rpc.tools.McpServerConfig
import com.intellij.openapi.components.*
import com.intellij.openapi.diagnostic.logger

@Service(Service.Level.APP)
@State(name = "AugmentIntegrations", storages = [Storage("AugmentIntegrations.xml")])
class AugmentIntegrationsConfig : SimplePersistentStateComponent<AugmentIntegrationsConfigState>(AugmentIntegrationsConfigState()) {
  private val logger = logger<AugmentIntegrationsConfig>()

  companion object {
    val instance: AugmentIntegrationsConfig
      get() = service()
  }

  fun hasAnyKey(): Boolean {
    return hasAtlassianCredentials() ||
      hasNotionCredentials() ||
      hasLinearCredentials() ||
      hasGitHubCredentials()
  }

  // Atlassian
  var atlassianServerUrl
    get() = state.atlassianServerUrl
    set(value) {
      state.atlassianServerUrl = value
    }

  var atlassianPersonalApiToken
    get() = state.atlassianPersonalApiToken
    set(value) {
      state.atlassianPersonalApiToken = value
    }

  var atlassianUsername
    get() = state.atlassianUsername
    set(value) {
      state.atlassianUsername = value
    }

  fun hasAtlassianCredentials(): Boolean {
    return !atlassianServerUrl.isNullOrEmpty() ||
      !atlassianPersonalApiToken.isNullOrEmpty() ||
      !atlassianUsername.isNullOrEmpty()
  }

  // Notion
  var notionApiToken
    get() = state.notionApiToken
    set(value) {
      state.notionApiToken = value
    }

  fun hasNotionCredentials(): Boolean {
    return !notionApiToken.isNullOrEmpty()
  }

  // Linear
  var linearApiToken
    get() = state.linearApiToken
    set(value) {
      state.linearApiToken = value
    }

  fun hasLinearCredentials(): Boolean {
    return !linearApiToken.isNullOrEmpty()
  }

  // GitHub
  var githubApiToken
    get() = state.githubApiToken
    set(value) {
      state.githubApiToken = value
    }

  fun hasGitHubCredentials(): Boolean {
    return !githubApiToken.isNullOrEmpty()
  }

  private var mcpServersJson
    get() = state.mcpServersJson
    set(value) {
      state.mcpServersJson = value
    }

  var mcpServers
    get() = mcpServersAsWebViewMsg()
    set(value) {
      mcpServersJson = serializeProtoToJson(value)
    }

  val mcpServersForSidecar
    get() =
      mcpServers.dataList.map {
        val builder =
          McpServerConfig.newBuilder()
            .setName(it.name)
            .setCommand(it.command)
            .setUseShellInterpolation(true) // Always true for UI-based servers
        // Empty args array since we're using shell interpolation

        // Add environment variables if present
        if (it.envMap != null && it.envMap.isNotEmpty()) {
          builder.putAllEnv(it.envMap)
        }

        builder.build()
      }

  private fun mcpServersAsWebViewMsg(): GetStoredMCPServersResponse {
    return try {
      if (mcpServersJson.isNullOrEmpty()) {
        GetStoredMCPServersResponse.newBuilder().build()
      } else {
        val message = parseJsonToProto(mcpServersJson!!)
        if (message.`is`(com.augmentcode.rpc.GetStoredMCPServersResponse::class.java)) {
          message.unpack(com.augmentcode.rpc.GetStoredMCPServersResponse::class.java)
        } else {
          logger.warn("Unexpected message type when parsing MCP servers from storage: ${message.typeUrl}")
          GetStoredMCPServersResponse.newBuilder().build()
        }
      }
    } catch (ex: Exception) {
      logger.warn("Failed to load MCP servers from storage: ${ex.message}")
      GetStoredMCPServersResponse.newBuilder().build()
    }
  }

  var userGuidelines: String
    get() = state.userGuidelines ?: ""
    set(value) {
      state.userGuidelines = value
    }

  fun hasUserGuidelines(): Boolean = (state.userGuidelines?.isNotEmpty() == true)
}

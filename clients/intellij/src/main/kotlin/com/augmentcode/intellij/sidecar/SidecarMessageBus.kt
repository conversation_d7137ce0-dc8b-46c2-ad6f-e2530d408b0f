package com.augmentcode.intellij.sidecar

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.util.messages.Topic
import com.intellij.util.messages.Topic.ProjectLevel

interface SidecarMessageBus {
  fun emitState(
    state: SidecarState,
    status: String? = null,
  )

  companion object {
    @ProjectLevel
    val SIDECAR_MESSAGE_TOPIC: Topic<SidecarMessageBus> =
      Topic.create(
        "sidecar message bus",
        SidecarMessageBus::class.java,
      )

    fun syncPublisher(project: Project): SidecarMessageBus {
      if (project.isDisposed) {
        thisLogger().warn(
          "SidecarMessageBus.syncPublisher called on a disposed project. Returning no-op, but this is a bug.",
        )
        return object : SidecarMessageBus {
          override fun emitState(
            state: SidecarState,
            status: String?,
          ) {}
        }
      }
      return project.messageBus.syncPublisher(SIDECAR_MESSAGE_TOPIC)
    }
  }
}

enum class SidecarState {
  SIDECAR_STARTING,
  SIDECAR_RUNNING,
  SIDECAR_STOPPING,
  SIDECAR_STOPPED,
  SIDECAR_ERROR,
}

package com.augmentcode.intellij.debugutils

import CustomPropertyReader.CHAOS_MONKEY_EDT_ASSERTIONS
import com.intellij.openapi.application.ApplicationManager

object EdtDebugUtil {
  /**
   * Returns true if the current thread is the EDT.
   * The EDT is the thread that handles all UI events and write events,
   * so slow operations must never be on the EDT.
   */
  fun isEdt(): Boolean {
    return ApplicationManager.getApplication().isDispatchThread
  }

  fun assertNotEdt() {
    if (!CustomPropertyReader.readBoolean(CHAOS_MONKEY_EDT_ASSERTIONS)) {
      return
    }
    assert(!isEdt()) { "This operation must not be on the EDT" }
  }
}

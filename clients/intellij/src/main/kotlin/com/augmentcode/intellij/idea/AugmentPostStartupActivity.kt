package com.augmentcode.intellij.idea

import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.sidecar.SidecarService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity

class AugmentPostStartupActivity : ProjectActivity {
  override suspend fun execute(project: Project) {
    AugmentFileStore.migrateFiles()
    if (!ApplicationManager.getApplication().isUnitTestMode) {
      // Initialize plugin state service
      PluginStateService.instance

      // initialize sidecar services
      project.serviceOrNull<SidecarService>()?.startServer()
    }
  }
}

package com.augmentcode.intellij.actions

import com.augmentcode.api.SmartPasteResolution
import com.augmentcode.intellij.AugmentBundle
import com.intellij.icons.AllIcons
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.ui.JBColor
import com.intellij.util.IconUtil

/**
 * An action that reverts all changes back to the original text and closes the diff viewer.
 * We add this to the Smart Paste diff viewer toolbar.
 */
class RejectAllChangesAction(
  initialText: String,
  targetFile: VirtualFile,
  resolutionContainer: SmartPasteResolution,
) : BaseDiffAction(
    AugmentBundle.message("actions.reject.all"),
    AugmentBundle.message("actions.reject.all.description"),
    IconUtil.scale(
      IconUtil.colorize(AllIcons.Actions.Cancel, JBColor.RED),
      null,
      0.85f,
    ),
    targetFile,
    initialText,
    resolutionContainer,
  )

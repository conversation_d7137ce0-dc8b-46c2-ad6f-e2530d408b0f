package com.augmentcode.intellij.actions

import com.augmentcode.api.UserTier
import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.ide.BrowserUtil
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware
import kotlinx.coroutines.runBlocking

class ManageAccountAction : AnAction(), DumbAware {
  override fun update(e: AnActionEvent) {
    super.update(e)

    // Update the action's text dynamically based on the user tier
    runBlocking {
      val modelInfo = AugmentAPI.instance.getCachedModelInfo()
      e.presentation.text =
        when (modelInfo?.userTier) {
          UserTier.COMMUNITY_TIER -> AugmentBundle.message("actions.manage.account.community")
          UserTier.PROFESSIONAL_TIER -> AugmentBundle.message("actions.manage.account.professional")
          UserTier.ENTERPRISE_TIER -> AugmentBundle.message("actions.manage.account.enterprise")
          else -> AugmentBundle.message("actions.manage.account")
        }
    }
  }

  // If we don't specify BGT, then IntelliJ will use OLD_EDT which is deprecated
  override fun getActionUpdateThread(): ActionUpdateThread = ActionUpdateThread.BGT

  override fun actionPerformed(e: AnActionEvent) {
    BrowserUtil.browse("https://app.augmentcode.com/account")
  }
}

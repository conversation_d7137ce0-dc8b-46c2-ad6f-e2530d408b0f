package com.augmentcode.intellij.api

import com.augmentcode.api.NumericEnum
import com.augmentcode.intellij.api.SessionId.getSessionId
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.metrics.ErrorReporter
import com.augmentcode.intellij.settings.AugmentSettings
import com.google.gson.*
import com.intellij.openapi.diagnostic.thisLogger
import com.jetbrains.rd.util.UUID
import io.ktor.client.plugins.*
import io.ktor.client.request.header
import io.ktor.client.request.preparePost
import io.ktor.client.request.setBody
import io.ktor.client.request.url
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.HttpStatement
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import java.lang.reflect.Type
import java.net.URI
import kotlin.coroutines.cancellation.CancellationException

/**
 * A thin wrapper around ktor's HttpClient which adds authentication and other headers, serialization, and logging.
 * Please put business logic in the AugmentApi class.
 */
internal class AugmentHttpClient(
  private val sessionID: String = getSessionId(),
) {
  private val gson = createGson()
  private val errorReporter = ErrorReporter.getInstance()

  /**
   * Reports an exception that occurred during an HTTP request.
   * Will not report errors for the report-error endpoint to avoid cycles.
   *
   * @param path The API endpoint path that was called
   * @param requestId The unique identifier for this request
   * @param error The exception that occurred
   */
  private fun reportError(
    path: String,
    requestId: String,
    error: Exception,
  ) {
    if (path == "report-error") return

    val diagnostics =
      listOf(
        "endpoint" to path,
        "request_id" to requestId,
        "session_id" to sessionID,
        "error_class" to error.javaClass.name,
      )

    errorReporter.reportError(
      originalRequestId = requestId,
      sanitizedMessage = "HTTP request failed: $path",
      stackTrace = error.stackTraceToString(),
      diagnostics = diagnostics,
    )
  }

  /**
   * Reports an HTTP response error (status code >= 400).
   * Will not report errors for the report-error endpoint to avoid cycles.
   *
   * @param path The API endpoint path that was called
   * @param requestId The unique identifier for this request
   * @param response The HTTP response that contains the error
   * @param responseBody The body of the error response
   */
  private fun reportError(
    path: String,
    requestId: String,
    response: HttpResponse,
    responseBody: String,
  ) {
    if (path == "report-error") return

    val diagnostics =
      listOf(
        "endpoint" to path,
        "request_id" to requestId,
        "session_id" to sessionID,
        "status_code" to response.status.value.toString(),
        "status_description" to response.status.description,
        "response_body" to responseBody,
      )

    errorReporter.reportError(
      originalRequestId = requestId,
      sanitizedMessage = "HTTP request failed with status ${response.status.value}: $path",
      stackTrace = "", // No stack trace for HTTP status errors
      diagnostics = diagnostics,
    )
  }

  private suspend fun createHttpStatement(
    path: String,
    body: Any?,
    serverBaseURL: String? = null,
    requestID: String = UUID.randomUUID().toString(),
    timeoutMs: Long? = null,
  ): HttpStatement {
    val connectionDetails = getConnectionDetails()
    val token = connectionDetails.token
    val baseURL: String? = serverBaseURL ?: connectionDetails.baseURL
    if (baseURL == null) {
      throw IllegalStateException("Base URL is not set")
    }

    return HttpClientProvider.instance.clientFor(URI.create(baseURL)).preparePost {
      url("${baseURL.removeSuffix("/")}/$path")

      timeout {
        timeoutMs?.let {
          requestTimeoutMillis = it
          socketTimeoutMillis = it
        }
      }

      contentType(ContentType.Application.Json)
      if (body != null) {
        setBody(gson.toJson(body))
      }
      header(HttpHeaders.Authorization, "Bearer $token")
      header("x-request-id", requestID)
      header("x-request-session-id", sessionID)
      // Version negotiation: if the server's minimum supported version is greater than
      // this value, it will return a 406 Not Acceptable error. The client should then
      // warn the user to upgrade. The source of truth for versions is the ApiVersion
      // enum in services/api_proxy/public_api.proto.
      header("x-api-version", "2")
    }
  }

  suspend fun post(
    path: String,
    body: Any?,
    serverBaseURL: String? = null,
    requestID: String = UUID.randomUUID().toString(),
  ): HttpResponse {
    val httpStatement = createHttpStatement(path, body, serverBaseURL, requestID)

    try {
      val response = httpStatement.execute()
      logResponse(path, requestID, response)

      // Check for unauthorized and clear the credentials
      if (response.status.value == 401) {
        if (AugmentSettings.instance.apiToken == null) {
          AugmentOAuthState.instance.clear()
        }
      }

      return response
    } catch (e: CancellationException) {
      throw e
    } catch (e: Exception) {
      wrapNetworkError(path, requestID, e)
    }
  }

  suspend fun <T> stream(
    path: String,
    body: Any?,
    serverBaseURL: String? = null,
    requestID: String = UUID.randomUUID().toString(),
    handleStream: suspend (HttpResponse) -> T,
    timeoutMs: Long? = null,
  ): T {
    val httpStatement = createHttpStatement(path, body, serverBaseURL, requestID, timeoutMs)

    try {
      return httpStatement.execute { response ->
        logResponse(path, requestID, response)
        handleStream(response)
      }
    } catch (e: CancellationException) {
      throw e
    } catch (e: Exception) {
      wrapNetworkError(path, requestID, e)
    }
  }

  private fun wrapNetworkError(
    path: String,
    requestId: String,
    e: Exception,
  ): Nothing {
    logger.warn("Failed to call $path ($requestId)", e)
    reportError(path, requestId, e)
    throw IllegalStateException("Failed to make network call to $path with request ID $requestId", e)
  }

  private suspend fun logResponse(
    path: String,
    requestId: String,
    response: HttpResponse,
  ) {
    // TODO: can we read the details from the response/request instead of passing them?
    if (response.status.value == 499) {
      logger.debug("Call $path ($requestId) was canceled: ${response.status.description} ${response.bodyAsText()}")
    } else if (response.status.value >= 400) {
      val responseBody = response.bodyAsText()
      val errorMessage = "Failed to call $path ($requestId): ${response.status.value} ${response.status.description} $responseBody"
      logger.warn(errorMessage)
      reportError(path, requestId, response, responseBody)
    }
    logger.debug("Called $path ($requestId): ${response.status.value} ${response.status.description}")
  }

  internal companion object {
    val logger = thisLogger()

    private suspend fun getConnectionDetails(): ConnectionDetails {
      val settings = AugmentSettings.instance
      val credentials = AugmentOAuthState.instance.getCredentials()
      val token = settings.apiToken ?: credentials?.accessToken

      val baseURL = if (settings.apiToken != null) settings.completionURL else credentials?.tenantURL

      return ConnectionDetails(
        baseURL,
        token,
      )
    }

    fun createGson(): Gson {
      val enumSerializer =
        object : JsonSerializer<NumericEnum> {
          override fun serialize(
            src: NumericEnum,
            typeOfSrc: Type,
            context: JsonSerializationContext,
          ): JsonElement = JsonPrimitive(src.getValue())
        }

      return GsonBuilder()
        .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
        .registerTypeHierarchyAdapter(Enum::class.java, enumSerializer)
        .create()
    }
  }
}

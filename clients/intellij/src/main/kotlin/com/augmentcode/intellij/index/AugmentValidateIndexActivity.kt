package com.augmentcode.intellij.index

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.CredentialsChangeListener
import com.augmentcode.intellij.auth.CredentialsMessageBusWrapper
import com.augmentcode.intellij.idea.AugmentPluginUpdater
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.syncing.AugmentSyncingPermissionTracker
import com.augmentcode.intellij.syncing.SyncingPermissionListener
import com.augmentcode.intellij.utils.AugmentDisposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.DumbModeBlockedFunctionality
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.seconds

class AugmentValidateIndexActivity(private val cs: CoroutineScope) : ProjectActivity {
  private val credsMsgBus = CredentialsMessageBusWrapper(cs)

  override suspend fun execute(project: Project) {
    credsMsgBus.subscribe(
      project,
      object : CredentialsChangeListener {
        override fun onChange(credentials: AugmentCredentials?) {
          if (credentials != null) {
            // rebuild index upon any changes in credentials
            AugmentLocalIndex.requestRebuild()
          }
        }
      },
    )

    val connection = project.messageBus.connect(AugmentDisposable.getInstance(project))
    connection.subscribe(
      AugmentSyncingPermissionTracker.SYNCING_PERMISSION_TOPIC,
      object : SyncingPermissionListener {
        override fun onSyncingPermissionGranted() {
          // rebuild index upon any changes in settings
          AugmentLocalIndex.requestRebuild()
        }
      },
    )

    val dumbService = DumbService.getInstance(project)
    while (dumbService.isDumb) {
      delay(1.seconds)
    }
    if (!AugmentAPI.instance.available()) {
      return
    }

    invokeLater {
      // watch for updates if plugin is actually used
      AugmentPluginUpdater.getInstance().pluginUsed()
    }

    validateRemotelyMissingBlobs(project)
  }

  suspend fun validateRemotelyMissingBlobs(project: Project) {
    val dumbService = DumbService.getInstance(project)

    // First get all locally indexed blobs under a brief read lock
    // These blobs are not filtered by .gitignore or .augmentignore
    val unfilteredLocalBlobs =
      dumbService.tryRunReadActionInSmartMode(
        {
          AugmentBlobStateReader.allUnfilteredIndexes(project)
        },
        AugmentBundle.message("augment.indexing.validation"),
        DumbModeBlockedFunctionality.CodeCompletion,
      ) ?: return

    // Then do the network calls without holding the read lock
    val unknownMemoryNames = AugmentRemoteSyncingManager.getInstance(project).findUnsynced(unfilteredLocalBlobs)

    dumbService.runWhenSmart {
      cs.launch {
        try {
          AugmentBlobStateReader.requestInvalidation(project, unknownMemoryNames)
        } catch (ex: Exception) {
          thisLogger().warn(ex)
        }
      }
    }
  }
}

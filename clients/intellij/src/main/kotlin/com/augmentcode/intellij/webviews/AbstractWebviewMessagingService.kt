package com.augmentcode.intellij.webviews

import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.rpc.AsyncWrapper
import com.augmentcode.rpc.StreamContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.protobuf.Any
import com.google.protobuf.Empty
import com.google.protobuf.InvalidProtocolBufferException
import com.google.protobuf.Message
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

/**
 * This class will process messages from a webview and route them to the chat service (which
 * is the protobuf defined service).
 */

abstract class AbstractWebviewMessagingService(
  private val project: Project,
  private val cs: CoroutineScope,
) : AugmentMessagingService, Disposable {
  private val logger = thisLogger()

  override fun processMessageFromWebview(
    jsonText: String,
    callback: suspend (String) -> Unit,
  ) {
    cs.launch(Dispatchers.IO) {
      processMessageFromWebview(jsonText).collect {
        callback(it)
      }
    }
  }

  suspend fun processMessageFromWebview(jsonText: String): Flow<String> {
    // Parse the message as a generic json object
    val jsonMapper = ObjectMapper()
    val jsonNode = jsonMapper.readTree(jsonText)

    // If the message is intended for the sidecar, send it there.
    val flow = checkIfSidecarAndSend(jsonMapper, jsonNode)
    if (flow != null) {
      return flow
    }

    // Else IntelliJ should handle the message
    try {
      val messageBuilder: Any.Builder = Any.newBuilder()
      parseJsonIntoProtoBuilder(jsonText, messageBuilder)

      // If the message is not for the sidecar and it cannot be parsed as protobuf, it is unknown
      // and we should return an error.
      val message = messageBuilder.build()
      val response: Flow<Message> =
        if (message.`is`(AsyncWrapper::class.java)) {
          val asyncWrapper = message.unpack(AsyncWrapper::class.java)
          processAsyncMessage(asyncWrapper)
            .catch { ex ->
              logger.warn("Async message ${asyncWrapper.requestId} failed!", ex)
              emit(
                AsyncWrapper.newBuilder()
                  .setRequestId(asyncWrapper.requestId)
                  .setError(ex.message ?: "Unknown error")
                  .build(),
              )
            }
        } else {
          processSyncMessage(message)
        }
      return response.map {
        serializeMessageToJson(it)
      }
    } catch (ex: InvalidProtocolBufferException) {
      logger.warn("Failed to parse webview message as protobuf.\nException: ${ex.message}\nMessage: $jsonText")

      // Check that the message is an async message
      if (jsonNode.has("type") && jsonNode.get("type").asText() == "async-wrapper" &&
        jsonNode.has("requestId")
      ) {
        val requestId = jsonNode.get("requestId").asText()

        logger.debug("Returning error for unknown async message: $requestId")
        return flowOf(
          serializeMessageToJson(
            AsyncWrapper.newBuilder()
              .setRequestId(requestId)
              .setError("Unable to parse webview message")
              .build(),
          ),
        )
      }
    }

    // If we are here, we were unable to find a message to return
    return emptyFlow()
  }

  override fun serializeMessageToJson(message: Message): String = serializeProtoToJson(message)

  /**
   * Wraps a JSON response string in an AsyncWrapper JSON structure.
   * This avoids using protobuf serialization for sidecar responses.
   */
  private fun wrapJsonResponse(
    requestId: String,
    responseJson: String,
  ): String {
    val mapper = ObjectMapper()

    // Parse the response JSON to ensure it's valid
    val responseNode = mapper.readTree(responseJson)

    // Create the wrapper object with requestId and baseMsg
    val wrapperNode = mapper.createObjectNode()
    wrapperNode.put("type", "async-wrapper")
    wrapperNode.put("requestId", requestId)
    wrapperNode.set<JsonNode>("baseMsg", responseNode)

    return mapper.writeValueAsString(wrapperNode)
  }

  protected abstract suspend fun processSyncMessage(request: Any): Flow<Message>

  protected abstract suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message>

  private suspend fun checkIfSidecarAndSend(
    jsonMapper: ObjectMapper,
    jsonNode: JsonNode,
  ): Flow<String>? {
    // If we aren't able to parse the webview message as a proto,
    // offer it to the sidecar if its an async message.
    try {
      // Check that the message is an async message
      if (jsonNode.has("type") && jsonNode.get("type").asText() == "async-wrapper" &&
        jsonNode.has("requestId") && jsonNode.has("baseMsg") &&
        jsonNode.has("destination")
      ) {
        val destination = jsonNode.get("destination").asText()
        if (destination != "sidecar") {
          return null
        }

        val requestId = jsonNode.get("requestId").asText()
        val baseMsg = jsonNode.get("baseMsg")

        // Convert the baseMsg to a string to parse to the sidecar
        val baseMsgJson = jsonMapper.writeValueAsString(baseMsg)

        // Send the baseMsg to the sidecar
        val response = project.serviceOrNull<SidecarService>()?.sendWebviewMessage(baseMsgJson)
        if (response != null) {
          logger.info("Webview Message sent and responded to by the sidecar ${baseMsg.get("type").asText()}: $requestId")
          return flowOf(
            wrapJsonResponse(requestId, response),
          )
        }

        // If the destination is the sidecar, and we get no response, then
        // return an error since the sidecar wasn't expecting this message.
        return flowOf(
          serializeMessageToJson(
            AsyncWrapper.newBuilder()
              .setRequestId(requestId)
              .setError("Failed to get a response from the sidecar")
              .build(),
          ),
        )
      }
    } catch (e: Exception) {
      logger.warn("Error thrown while checking if the message is for the sidecar: ${e.message}")
    }
    return null
  }

  protected fun <T : Message> wrapFlow(
    initialRequestId: String,
    flow: Flow<T>,
  ): Flow<AsyncWrapper> =
    flow {
      var previousStreamChunkId: Int? = null
      flow.onCompletion { ex ->
        // indicate the end of the stream to the webview
        val finalMessage =
          AsyncWrapper.newBuilder()
            .setRequestId(if (previousStreamChunkId == null) initialRequestId else "$initialRequestId-$previousStreamChunkId")
            .setBaseMsg(Any.pack(Empty.getDefaultInstance()))
            .setStreamCtx(
              StreamContext.newBuilder()
                .setStreamMsgIdx(previousStreamChunkId ?: 0)
                .setIsStreamComplete(true)
                .build(),
            )
        if (ex != null) {
          finalMessage.setError(ex.message)
        }
        emit(finalMessage.build())
      }.collectIndexed { index, value ->
        val chunk =
          AsyncWrapper.newBuilder()
            // first message in a stream should have the initial request id
            .setRequestId(if (previousStreamChunkId == null) initialRequestId else "$initialRequestId-$previousStreamChunkId")
            .setBaseMsg(Any.pack(value))
            .setStreamCtx(
              StreamContext.newBuilder()
                .setStreamMsgIdx(index)
                .setStreamNextRequestId("$initialRequestId-$index")
                .build(),
            )
            .build()
        emit(chunk)
        previousStreamChunkId = index
      }
    }
}

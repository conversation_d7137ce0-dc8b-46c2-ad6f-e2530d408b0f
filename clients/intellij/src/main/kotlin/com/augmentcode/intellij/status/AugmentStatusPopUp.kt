package com.augmentcode.intellij.status

import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.openapi.actionSystem.ActionGroup
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import kotlinx.coroutines.runBlocking

class AugmentStatusPopUp(val project: Project) : ActionGroup("Augment", true) {
  override fun getChildren(e: AnActionEvent?): Array<AnAction> {
    val ctx: PluginContext? = PluginStateService.instance.context

    val actions =
      mutableListOf<AnAction>(
        ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowSettingsAction"),
        ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ExtensionStatusAction"),
      )

    actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowChatAction"))
    val isSignedIn =
      AugmentSettings.instance.apiToken != null ||
        AugmentSettings.instance.completionURL != null ||
        runBlocking { AugmentOAuthState.instance.getCredentials() } != null

    if (isSignedIn) {
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ToggleCompletionsAction"))
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ManageAccountAction"))
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ReindexAction"))
    }

    if (ctx != null && ctx.flags.enableCompletionsHistory) {
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHistoryAction"))
    }

    if (AugmentSettings.debugFeaturesEnabled) {
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.GenerateSyncReport"))
    }

    if (AugmentSettings.instance.apiToken == null && AugmentSettings.instance.completionURL == null) {
      val credentials = runBlocking { AugmentOAuthState.instance.getCredentials() }
      val authAction =
        if (credentials != null) {
          ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignOutAction")
        } else {
          ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignInAction")
        }
      actions.add(authAction)
    }
    return actions.toTypedArray()
  }
}

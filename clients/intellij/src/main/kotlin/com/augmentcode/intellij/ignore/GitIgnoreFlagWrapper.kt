package com.augmentcode.intellij.ignore

import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.ignore.GitIgnore as AugmentGitIgnore
import nl.basjes.gitignore.GitIgnore as BasjesGitIgnore

class GitIgnoreFlagWrapper(private val gitIgnoreContent: String) {
  val augmentGitIgnore: AugmentGitIgnore?
  val basjesGitIgnore: BasjesGitIgnore?

  // We never do GitIgnore Parsing in a context that would block UI, so we can wait for network
  val homeSpunGitIgnoreEnabled: Boolean = FeatureFlagManager.instance.enableHomespunGitignore(waitForNetwork = true)

  init {
    if (homeSpunGitIgnoreEnabled) {
      augmentGitIgnore = AugmentGitIgnore(gitIgnoreContent)
      basjesGitIgnore = null
    } else {
      augmentGitIgnore = null
      basjesGitIgnore = BasjesGitIgnore(gitIgnoreContent)
    }
  }

  fun setVerbose(verbose: Boolean) {
    if (!homeSpunGitIgnoreEnabled) {
      basjesGitIgnore?.setVerbose(verbose)
    }
  }

  suspend fun isIgnoredFile(filename: String): Boolean? {
    if (homeSpunGitIgnoreEnabled) {
      return augmentGitIgnore?.isIgnoredFile(filename)
    } else {
      return basjesGitIgnore?.isIgnoredFile(filename)
    }
  }
}

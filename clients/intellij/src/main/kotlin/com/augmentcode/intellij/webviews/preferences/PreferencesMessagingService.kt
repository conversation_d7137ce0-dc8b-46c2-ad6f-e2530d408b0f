package com.augmentcode.intellij.webviews.preferences

import com.augmentcode.intellij.webviews.AbstractWebviewMessagingService
import com.augmentcode.rpc.AsyncWrapper
import com.augmentcode.rpc.PreferencesLoadedRequest
import com.google.protobuf.Any
import com.google.protobuf.Message
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

/**
 * This class will process messages from a webview and route them to the preferences service.
 */
@Service(Service.Level.PROJECT)
class PreferencesMessagingService(
  private val project: Project,
  private val cs: CoroutineScope,
) : AbstractWebviewMessagingService(project, cs) {
  private val logger = logger<PreferencesMessagingService>()

  companion object {
    fun getInstance(project: Project): PreferencesMessagingService = project.getService(PreferencesMessagingService::class.java)
  }

  override fun processMessageFromWebview(
    jsonText: String,
    callback: suspend (String) -> Unit,
  ) {
    super.processMessageFromWebview(jsonText, callback)
  }

  override suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message> {
    val baseMessage = request.baseMsg
    val response =
      processSyncMessage(baseMessage).map { responseMsg ->
        AsyncWrapper.newBuilder()
          .setRequestId(request.requestId)
          .setBaseMsg(Any.pack(responseMsg))
          .build()
      }
    return response
  }

  override fun dispose() {}

  override suspend fun processSyncMessage(request: Any): Flow<Message> {
    return when {
      request.`is`(com.augmentcode.rpc.PreferenceNotifyRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.PreferenceNotifyRequest::class.java)
        flowOf(AugmentPreferencesWebviewService.getInstance(project).preferenceNotify(message))
      }
      request.`is`(PreferencesLoadedRequest::class.java) -> {
        val message = request.unpack(PreferencesLoadedRequest::class.java)
        flowOf(AugmentPreferencesWebviewService.getInstance(project).preferencesLoaded(message))
      }
      else -> {
        logger.warn("Unknown preferences message: $request")
        emptyFlow()
      }
    }
  }
}

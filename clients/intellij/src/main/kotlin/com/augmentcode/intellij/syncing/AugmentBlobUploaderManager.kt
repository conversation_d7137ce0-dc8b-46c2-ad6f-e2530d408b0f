package com.augmentcode.intellij.syncing

import com.augmentcode.api.BatchUploadRequest
import com.augmentcode.api.BatchUploadResponse
import com.augmentcode.api.FindMissingRequest
import com.augmentcode.api.UploadableBlob
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.settings.AugmentSettings
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.impl.LoadTextUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readBytes
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.selects.onTimeout
import kotlinx.coroutines.selects.select
import org.jetbrains.annotations.VisibleForTesting
import java.nio.file.NoSuchFileException as JavaNoSuchFileException
import kotlin.io.NoSuchFileException as KotlinNoSuchFileException

data class UploadRequest(
  val rootPath: String,
  val relPath: String,
  val expectedBlobName: String,
  val fileReference: VirtualFile,
) {
  val uploadableBlob
    get() =
      try {
        // This API is used under the hood in FileContent#contentAsText, and it does not require read lock
        // https://github.com/JetBrains/intellij-community/blob/e4bea1954dcf234bbcc62e81bf01a9f424aad2d2/platform/core-impl/src/com/intellij/util/indexing/FileContentImpl.java#L184-L186
        val normalizedText = LoadTextUtil.getTextByBinaryPresentation(fileReference.readBytes(), fileReference, false, false).toString()
        UploadableBlob().apply {
          path = relPath
          root = rootPath
          content = normalizedText
        }
      } catch (_: KotlinNoSuchFileException) {
        // In case of race condition where the file was deleted after it was enqueued for upload
        null
      } catch (_: JavaNoSuchFileException) {
        // Also handle Java's NoSuchFileException
        null
      }
}

@Service(Service.Level.PROJECT)
class AugmentBlobUploaderManager(
  val project: Project,
  val cs: CoroutineScope,
) : Disposable {
  private var uploadQueue = Channel<UploadRequest>(Channel.UNLIMITED)
  private var uploadJob: Job? = null

  private val syncFilter = SyncFilter(project)

  fun startUploadLoop() {
    if (uploadJob !== null) {
      return
    }

    @OptIn(DelicateCoroutinesApi::class)
    uploadJob =
      cs.launch {
        while (isActive) {
          if (AugmentAPI.instance.available()) {
            uploadLoop()
          }
          yield() // Explicit suspension point for cancellation
        }
      }
  }

  override fun dispose() {
    uploadJob?.cancel()
    uploadJob = null
    uploadQueue.close()
  }

  /**
   * Enqueues a blob for upload and returns the blob name, which is a hash of the path and content.
   * @return the blob name, or null if the blob was not uploaded.
   */
  suspend fun enqueue(request: UploadRequest) {
    uploadQueue.send(request)
  }

  companion object {
    fun getInstance(project: Project): AugmentBlobUploaderManager {
      return project.getService(AugmentBlobUploaderManager::class.java)
    }

    // wait for a bit before admitting channel is empty
    const val EMPTY_CHANNEL_WAIT_MS = 200L

    private val logger = thisLogger()
  }

  @VisibleForTesting
  @OptIn(ExperimentalCoroutinesApi::class)
  suspend fun uploadLoop() {
    val requests = mutableListOf<UploadRequest>()
    try {
      // "Wait" for a batch of requests
      requests.addAll(collectRequests())
      if (requests.isEmpty()) {
        return
      }

      // Call find-missing to determine which blobs need to be uploaded
      val requestsToUpload = findRequestsToUpload(requests)
      if (requestsToUpload.isEmpty()) {
        logger.info("No blobs to upload")
        return
      }

      // Do upload of missing blobs
      uploadBlobs(requestsToUpload) ?: return
    } catch (e: CancellationException) {
      if (requests.isNotEmpty()) {
        logger.warn("Upload cancelled with ${requests.size} blobs in flight")
      }
      // kotlin uses CancellationException to signal to higher coroutines
      throw e
    } catch (e: Exception) {
      logger.warn("Failed to upload ${requests.size} blobs", e)
    }
  }

  /**
   * Collects requests from upload queue, performs .gitignore and .augmentignore filtering,
   * and batches requests until we reach size limits.
   *
   * @return List of requests to upload
   */
  @OptIn(ExperimentalCoroutinesApi::class)
  private suspend fun collectRequests(): List<UploadRequest> {
    val requests = mutableListOf<UploadRequest>()

    val request = uploadQueue.receive() // will "wait" until there is a value
    if (!isAccepted(request)) {
      return emptyList()
    }
    requests.add(request)

    var unfilteredContentLength = request.fileReference.length
    while (requests.size < AugmentRemoteSyncingManagerImpl.MAX_FIND_MISSING_BATCH_SIZE) {
      val additionalRequest =
        select<UploadRequest?> {
          uploadQueue.onReceive { it }
          onTimeout(EMPTY_CHANNEL_WAIT_MS) { null }
        } ?: break

      if (!isAccepted(additionalRequest)) {
        continue
      }

      // Check if adding this request would exceed the size limit
      if (unfilteredContentLength + additionalRequest.fileReference.length >
        AugmentRemoteSyncingManagerImpl.MAX_BATCH_CONTENT_SIZE_BYTES
      ) {
        // Put the request back in the queue for the next batch
        uploadQueue.send(additionalRequest)
        break
      }

      requests.add(additionalRequest)
      unfilteredContentLength += additionalRequest.fileReference.length
    }

    return requests
  }

  private suspend fun findRequestsToUpload(requests: List<UploadRequest>): List<UploadRequest> {
    val missingResponse =
      AugmentAPI.instance.findMissing(
        FindMissingRequest().apply {
          model = AugmentSettings.instance.modelName ?: ""
          memObjectNames = requests.map { it.expectedBlobName }
        },
      )

    val requestsToUpload = mutableListOf<UploadRequest>()
    for (requestToCheck in requests) {
      val expectedBlobName = requestToCheck.expectedBlobName
      if (missingResponse.unknownMemoryNames.contains(expectedBlobName)) {
        requestsToUpload.add(requestToCheck)
      }
    }

    val filteredCount = requests.size - requestsToUpload.size
    if (filteredCount > 0) {
      logger.info(
        "Filtered out $filteredCount blobs (out of ${requests.size}) " +
          "before uploading because they are already known by the server.",
      )
    }

    return requestsToUpload
  }

  private suspend fun uploadBlobs(requestsToUpload: List<UploadRequest>): BatchUploadResponse? {
    val cumulativeContentLength = requestsToUpload.sumOf { it.fileReference.length }
    logger.info("Uploading ${requestsToUpload.size} blobs with a total content length of $cumulativeContentLength")

    val uploadResponse =
      AugmentAPI.instance.batchUpload(
        BatchUploadRequest().apply {
          blobs = requestsToUpload.mapNotNull { it.uploadableBlob }
        },
      )

    if (uploadResponse == null) {
      logger.warn("Failed to upload ${requestsToUpload.size} blobs")
      return null
    }
    if (uploadResponse.blobNames.size != requestsToUpload.size) {
      logger.warn("Expected ${requestsToUpload.size} blob names while batch uploading but got ${uploadResponse.blobNames.size}")
      return null
    }

    return uploadResponse
  }

  private suspend fun isAccepted(request: UploadRequest): Boolean {
    return syncFilter.isAccepted(request.rootPath, request.relPath, request.fileReference)
  }
}

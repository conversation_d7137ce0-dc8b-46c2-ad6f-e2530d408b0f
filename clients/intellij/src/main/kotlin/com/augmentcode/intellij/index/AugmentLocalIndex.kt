package com.augmentcode.intellij.index

import com.intellij.ide.scratch.ScratchUtil
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.indexing.*
import com.intellij.util.io.EnumeratorStringDescriptor
import com.intellij.util.io.KeyDescriptor
import java.io.DataInput
import java.io.DataOutput

class AugmentLocalIndex : ScalarIndexExtension<AugmentBlobState>() {
  companion object {
    val NAME = ID.create<AugmentBlobState, Void>("AugmentLocalIndex")

    fun requestRebuild() {
      FileBasedIndex.getInstance().requestRebuild(NAME)
    }
  }

  // optimizes performance of iterating over the whole index
  override fun traceKeyHashToVirtualFileMapping(): Boolean = true

  override fun getName(): ID<AugmentBlobState, Void> = NAME

  override fun getVersion(): Int = 13

  override fun dependsOnFileContent(): Boolean = true

  override fun getInputFilter(): FileBasedIndex.InputFilter {
    return FileBasedIndex.InputFilter { file ->
      try {
        if (file.fileType.isBinary) {
          return@InputFilter false
        }
      } catch (e: Throwable) {
        thisLogger().warn("Failed to check if file ${file.path} is binary. Skipping...", e)
        return@InputFilter false
      }
      if (ScratchUtil.isScratch(file)) {
        // let's not upload scratch files
        return@InputFilter false
      }

      return@InputFilter true
    }
  }

  override fun getKeyDescriptor(): KeyDescriptor<AugmentBlobState> = AugmentBlobStateDescriptor

  override fun getIndexer(): DataIndexer<AugmentBlobState, Void, FileContent> = AugmentLocalIndexer()
}

private object AugmentBlobStateDescriptor : KeyDescriptor<AugmentBlobState> {
  override fun getHashCode(value: AugmentBlobState): Int = value.hashCode()

  override fun isEqual(
    val1: AugmentBlobState,
    val2: AugmentBlobState,
  ) = val1 == val2

  override fun save(
    out: DataOutput,
    value: AugmentBlobState,
  ) {
    EnumeratorStringDescriptor.INSTANCE.save(out, value.relativePath)
    EnumeratorStringDescriptor.INSTANCE.save(out, value.remoteName)
    EnumeratorStringDescriptor.INSTANCE.save(out, value.localName)
    EnumeratorStringDescriptor.INSTANCE.save(out, value.rootPath)
    out.writeLong(value.remoteSyncTimestamp)
  }

  override fun read(input: DataInput): AugmentBlobState {
    return AugmentBlobState(
      relativePath = EnumeratorStringDescriptor.INSTANCE.read(input),
      remoteName = EnumeratorStringDescriptor.INSTANCE.read(input),
      localName = EnumeratorStringDescriptor.INSTANCE.read(input),
      rootPath = EnumeratorStringDescriptor.INSTANCE.read(input),
      remoteSyncTimestamp = input.readLong(),
    )
  }
}

package com.augmentcode.intellij.settings

import com.augmentcode.intellij.webviews.AugmentWebview
import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.WebviewFactory
import com.augmentcode.intellij.webviews.settings.SettingsMessagingService
import com.intellij.ide.plugins.UIComponentFileEditor
import com.intellij.ide.plugins.UIComponentVirtualFile
import com.intellij.openapi.fileEditor.FileEditorManagerKeys
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.jetbrains.annotations.VisibleForTesting
import java.awt.BorderLayout
import javax.swing.JPanel

class AugmentSettingsWebviewEditorVirtualFile(
  private val project: Project,
  private val cs: CoroutineScope,
  @VisibleForTesting
  private val section: String? = null,
) : UIComponentVirtualFile(
    SETTINGS_VIRTUAL_FILE_NAME,
    null,
  ) {
  companion object {
    const val SETTINGS_VIRTUAL_FILE_NAME = "Augment Tools Settings"
  }

  init {
    putUserData(FileEditorManagerKeys.FORBID_TAB_SPLIT, true)
  }

  private val entryFilePath = "settings.html"

  @VisibleForTesting
  var webview: AugmentWebview? = null

  fun navigateToSection(section: String) {
    webview?.postMessage("{type: 'navigate-to-settings-section', data: '$section'}")
  }

  @VisibleForTesting
  fun getSection() = section

  private val onWebviewLoaded = { _: CefBrowser, _: CefFrame, _: Int ->

    // Navigate to the specified section if provided
    if (getSection() != null) {
      cs.launch {
        // Small delay to ensure the page is fully initialized
        delay(500)
        navigateToSection(getSection()!!)
      }
    }
  }

  override fun createContent(editor: UIComponentFileEditor): Content {
    return Content {
      // Create a webview with a load handler that registers the webview and navigates to the specified section
      webview =
        WebviewFactory.create(
          project,
          entryFilePath,
          AugmentWebviewStateKey.SETTINGS_STATE,
          SettingsMessagingService.getInstance(project),
          editor,
          onLoad = onWebviewLoaded,
        )

      JPanel(BorderLayout()).apply {
        webview?.let { add(it.browser.component, BorderLayout.CENTER) }
      }
    }
  }
}

package com.augmentcode.intellij.metrics

import com.intellij.openapi.Disposable
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.selects.onTimeout
import kotlinx.coroutines.selects.select

abstract class MetricsReporter<T>(
  private val maxRecords: Int,
  private val uploadBatchSize: Int,
  private var uploadIntervalMs: Long,
) : Disposable {
  private val store = Channel<T>(capacity = maxRecords, onBufferOverflow = BufferOverflow.DROP_OLDEST)
  private var job: Job? = null

  protected abstract suspend fun performUpload(batch: List<T>)

  init {
    enableUpload()
  }

  suspend fun report(item: T) {
    store.send(item)
  }

  protected fun enableUpload() {
    if (job != null) {
      return
    }

    job =
      CoroutineScope(Dispatchers.Default).launch {
        while (isActive) {
          doUpload()
          delay(uploadIntervalMs)
        }
      }
  }

  override fun dispose() {
    job?.cancel()
    job = null
  }

  @OptIn(ExperimentalCoroutinesApi::class)
  private suspend fun doUpload() {
    val receivedItems = mutableListOf<T>()
    try {
      while (receivedItems.size < uploadBatchSize) {
        val additionalRequest =
          select<T?> {
            store.onReceive { it }
            onTimeout(0) { null } // todo choose actual interval
          } ?: break

        receivedItems.add(additionalRequest)
      }
      if (receivedItems.size > 0) {
        thisLogger().debug("Uploading ${receivedItems.size} metrics")
        performUpload(receivedItems)
      }
    } catch (e: CancellationException) {
      // kotlin uses CancellationException to signal to higher coroutines
      if (receivedItems.isNotEmpty()) {
        thisLogger().warn("Upload cancelled with ${receivedItems.size} metrics in flight")
      }
      throw e
    } catch (e: Exception) {
      thisLogger().warn("Failed to upload ${receivedItems.size} metrics", e)
    }
  }
}

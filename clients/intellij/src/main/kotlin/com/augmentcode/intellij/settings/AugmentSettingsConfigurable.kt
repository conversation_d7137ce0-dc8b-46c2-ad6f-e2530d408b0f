package com.augmentcode.intellij.settings

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.index.AugmentLocalIndex
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.icons.AllIcons
import com.intellij.idea.ActionsBundle
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.keymap.impl.ui.KeymapPanel
import com.intellij.openapi.options.BoundSearchableConfigurable
import com.intellij.openapi.options.Configurable.Beta
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.ui.DialogPanel
import com.intellij.openapi.ui.getActionShortcutText
import com.intellij.ui.dsl.builder.*
import com.intellij.util.text.nullize
import kotlinx.coroutines.runBlocking

class AugmentSettingsConfigurable :
  BoundSearchableConfigurable(
    AugmentBundle.message("settings.displayName"),
    AugmentBundle.message("settings.helpTopic"),
    "editor.preferences.augment",
  ),
  Beta {
  private val settings = AugmentSettings.instance
  private val integrations = AugmentIntegrationsConfig.instance

  override fun createPanel(): DialogPanel {
    return panel {
      if (ApplicationManager.getApplication().isInternal) {
        group(AugmentBundle.message("settings.debugSettings")) {
          row(AugmentBundle.message("settings.serverURL")) {
            textField().bindText(
              getter = { settings.completionURL.orEmpty() },
              setter = { settings.completionURL = it },
            ).align(Align.FILL)
          }
          row(AugmentBundle.message("settings.apiToken")) {
            textField().bindText(
              getter = { settings.apiToken.orEmpty() },
              setter = { settings.apiToken = it },
            ).align(Align.FILL)
          }
          row(AugmentBundle.message("settings.debugSettings.modelName")) {
            textArea().bindText(
              MutableProperty(
                getter = { settings.modelName ?: "" },
                setter = { settings.modelName = it.nullize() },
              ),
            ).align(Align.FILL)
          }
        }
      }
      group(AugmentBundle.message("settings.completionSettings")) {
        row {
          checkBox(AugmentBundle.message("settings.inlineCompletionEnabled"))
            .bindSelected(settings::inlineCompletionEnabled)
        }
        indent {
          val actionId = "CallInlineCompletionAction"
          val actionText = ActionsBundle.message("action.$actionId.text")
          val actionShortcutText = getActionShortcutText(actionId)
          if (actionShortcutText.isNotBlank()) {
            row {
              text(
                AugmentBundle.message(
                  "settings.inlineCompletionEnabled.withShortcutDescription",
                  actionText,
                  actionShortcutText,
                ),
              )
            }
          } else {
            row {
              text(AugmentBundle.message("settings.inlineCompletionEnabled.noShortcutDescription", actionText))
            }
            row {
              button(AugmentBundle.message("settings.inlineCompletionEnabled.configureShortcutTitle")) {
                // there seems to be no way directly navigate to the keymap settings of a particular action
                ShowSettingsUtil.getInstance().showSettingsDialog(null, KeymapPanel::class.java)
              }
            }
          }
        }
        collapsibleGroup(AugmentBundle.message("settings.completionSettings.disableByLanguage")) {
          row {
            textArea().bindText(
              getter = {
                settings.disableForFileTypes.joinToString(", ") { "*.$it" }
              },
              setter = { it ->
                settings.disableForFileTypes =
                  it.split(", ")
                    .map { it.removePrefix("*.") }.filter { it.isNotBlank() }.toSortedSet()
              },
            ).align(Align.FILL)
          }
          row {
            comment(AugmentBundle.message("settings.completionSettings.disableByLanguage.comment"))
          }
        }
      }
      if (FeatureFlagManager.instance.agentModeEnabled() && integrations.hasAnyKey()) {
        group(AugmentBundle.message("settings.integrations")) {
          if (integrations.hasAtlassianCredentials()) {
            collapsibleGroup(AugmentBundle.message("settings.integrations.atlassian")) {
              row(AugmentBundle.message("settings.integrations.atlassian.serverUrl")) {
                textField()
                  .bindText(
                    getter = { integrations.atlassianServerUrl.orEmpty() },
                    setter = { integrations.atlassianServerUrl = it },
                  )
                  .align(Align.FILL)
              }
              row(AugmentBundle.message("settings.integrations.atlassian.personalApiToken")) {
                textField()
                  .bindText(
                    getter = { integrations.atlassianPersonalApiToken.orEmpty() },
                    setter = { integrations.atlassianPersonalApiToken = it },
                  )
                  .align(Align.FILL)
              }
              row(AugmentBundle.message("settings.integrations.atlassian.username")) {
                textField()
                  .bindText(
                    getter = { integrations.atlassianUsername.orEmpty() },
                    setter = { integrations.atlassianUsername = it },
                  )
                  .align(Align.FILL)
              }
            }
          }

          if (integrations.hasNotionCredentials()) {
            collapsibleGroup(AugmentBundle.message("settings.integrations.notion")) {
              row(AugmentBundle.message("settings.integrations.notion.apiToken")) {
                textField()
                  .bindText(
                    getter = { integrations.notionApiToken.orEmpty() },
                    setter = { integrations.notionApiToken = it },
                  )
                  .align(Align.FILL)
              }
            }
          }

          if (integrations.hasLinearCredentials()) {
            collapsibleGroup(AugmentBundle.message("settings.integrations.linear")) {
              row(AugmentBundle.message("settings.integrations.linear.apiToken")) {
                textField()
                  .bindText(
                    getter = { integrations.linearApiToken.orEmpty() },
                    setter = { integrations.linearApiToken = it },
                  )
                  .align(Align.FILL)
              }
            }
          }

          if (integrations.hasGitHubCredentials()) {
            collapsibleGroup(AugmentBundle.message("settings.integrations.github")) {
              row(AugmentBundle.message("settings.integrations.github.apiToken")) {
                textField()
                  .bindText(
                    getter = { integrations.githubApiToken.orEmpty() },
                    setter = { integrations.githubApiToken = it },
                  )
                  .align(Align.FILL)
              }
            }
          }
        }
      }
      row {
        // todo: move to async
        val credentials = runBlocking { AugmentOAuthState.instance.getCredentials() }
        if (credentials != null) {
          button(
            AugmentBundle.message("settings.signOut"),
            ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignOutAction"),
          ).also {
            it.component.icon = AllIcons.Actions.Exit
          }
        } else {
          button(
            AugmentBundle.message("settings.signIn"),
            ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignInAction"),
          ).also {
            it.component.icon = AllIcons.General.User
          }
        }.align(AlignX.RIGHT)
      }
    }
  }

  override fun apply() {
    val originalCompletionURL = settings.completionURL
    super.apply()
    if (originalCompletionURL != settings.completionURL) {
      runBlocking {
        // this configuration is on Application level, so we need to update all projects aka opened windows
        // let's go all over all projects and reset the checkpoint so it will get updated
        ProjectManager.getInstance().openProjects.forEach {
          AugmentRemoteSyncingManager.getInstance(it).reset()
        }
      }
      // rebuild index only if we have a new server
      AugmentLocalIndex.requestRebuild()
    }
  }
}

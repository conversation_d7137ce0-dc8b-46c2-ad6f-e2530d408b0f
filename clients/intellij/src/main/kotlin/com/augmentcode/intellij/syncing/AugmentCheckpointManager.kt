package com.augmentcode.intellij.syncing

import com.augmentcode.api.BlobsPayload
import com.augmentcode.api.CheckpointBlobsRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.CheckpointResult
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.delay
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlin.coroutines.cancellation.CancellationException

/**
 * Manages the state and logic for checkpointing blobs with the Augment server.
 *
 * This class maintains:
 * - The current checkpoint ID received from the server
 * - The set of blobs included in that checkpoint
 * - Logic for determining when to create new checkpoints
 *
 * The checkpoint system helps reduce the payload size in API requests by maintaining
 * a known state of blobs on the server. Instead of sending the full list of blobs
 * with every request, we can send just the delta (additions/deletions) from the
 * last checkpoint.
 *
 * The [refreshCheckpointLoop] method is called continuously by [AugmentRemoteSyncingManager]
 * to:
 * 1. Check if the delta (added/deleted blobs) has grown too large
 * 2. Create new checkpoints when needed
 * 3. Adjust the polling frequency based on the rate of changes
 *
 * Thread safety:
 * - All state modifications are protected by [updateMutex]
 * - The refresh loop runs on a background coroutine
 * - API calls are made outside the mutex lock to prevent blocking
 *
 * @see AugmentRemoteSyncingManager
 */
internal class AugmentCheckpointManager() {
  companion object {
    const val MAX_WORKING_SET_SIZE = 1000
    const val CHECK_INTERVAL_MS = 100L
  }

  private val updateMutex = Mutex()
  private var remoteCheckpointId: String? = null

  /**
   * The set of blobs in the current checkpoint.
   */
  private val checkpointedBlobs: MutableSet<String> = mutableSetOf()

  fun reset() {
    remoteCheckpointId = null
    checkpointedBlobs.clear()
  }

  suspend fun getCheckpoint(syncedBlobs: Set<String>): BlobsPayload {
    return updateMutex.withLock {
      BlobsPayload().apply {
        checkpointId = remoteCheckpointId
        // Cap the delta size to prevent oversized payloads in API requests
        // during the brief period between a checkpoint reset and the next refresh.
        addedBlobs = (syncedBlobs - checkpointedBlobs).take(MAX_WORKING_SET_SIZE).toSet()
        deletedBlobs = (checkpointedBlobs - syncedBlobs).take(MAX_WORKING_SET_SIZE).toSet()
      }
    }
  }

  suspend fun refreshCheckpointLoop(syncedBlobs: Set<String>) {
    val addedBlobs = syncedBlobs - checkpointedBlobs
    val deletedBlobs = checkpointedBlobs - syncedBlobs

    if (AugmentAPI.instance.available()) {
      try {
        checkAndRefreshCheckpoint(addedBlobs, deletedBlobs)
      } catch (e: CancellationException) {
        // Kotlin uses CancellationException to signal to higher coroutines
        throw e
      } catch (e: Exception) {
        thisLogger().warn("Failed to refresh checkpoint", e)
      }
    }

    delay(CHECK_INTERVAL_MS)
  }

  private suspend fun checkAndRefreshCheckpoint(
    addedBlobs: Set<String>,
    deletedBlobs: Set<String>,
  ) {
    // Potential TOCTOU condition that may unnecessarily launch a checkpoint.
    // If that happens it's not a big deal.
    if ((remoteCheckpointId == null && addedBlobs.isNotEmpty()) || isDeltaTooBig(addedBlobs, deletedBlobs)) {
      refreshCheckpointId(addedBlobs, deletedBlobs)
    }
  }

  /**
   * Returns true if the delta is too big to fit in a single checkpoint request.
   */
  private fun isDeltaTooBig(
    addedBlobs: Set<String>,
    deletedBlobs: Set<String>,
  ): Boolean {
    return addedBlobs.size + deletedBlobs.size > MAX_WORKING_SET_SIZE
  }

  /**
   * Refreshes the checkpoint ID by sending a chunk of the delta up to MAX_WORKING_SET_SIZE to the server.
   * This method tries to minimize time it holds the mutex, to allow other threads to access the checkpoint.
   *
   * We do not hold the mutex during the API call.
   * This method runs in a loop, so it will only ever be called once at a time.
   * So we consider it safe to release the mutex during the API call.
   */
  private suspend fun refreshCheckpointId(
    addedBlobs: Set<String>,
    deletedBlobs: Set<String>,
  ) {
    // Prepare the checkpoint request
    val addedBlobsChunk = addedBlobs.take(MAX_WORKING_SET_SIZE).toSet()
    val deletedBlobsChunk = deletedBlobs.take(MAX_WORKING_SET_SIZE).toSet()

    thisLogger().info(
      "Refreshing checkpoint ${remoteCheckpointId ?: "initial"} with ${addedBlobsChunk.size} added and " +
        "${deletedBlobsChunk.size} deleted blobs",
    )

    // Make API call
    val checkpointResult =
      AugmentAPI.instance.createCheckpoint(
        CheckpointBlobsRequest().apply {
          blobs =
            BlobsPayload().apply {
              checkpointId = remoteCheckpointId
              this.addedBlobs = addedBlobsChunk
              this.deletedBlobs = deletedBlobsChunk
            }
        },
      )

    when (checkpointResult) {
      is CheckpointResult.Success -> {
        thisLogger().info("Checkpoint refreshed to ${checkpointResult.response.newCheckpointId}")
        updateMutex.withLock {
          remoteCheckpointId = checkpointResult.response.newCheckpointId
          checkpointedBlobs.removeAll(deletedBlobsChunk)
          checkpointedBlobs.addAll(addedBlobsChunk)
        }
      }
      is CheckpointResult.NeedsReset -> {
        thisLogger().warn("Checkpoint not found, resetting checkpoint")
        updateMutex.withLock {
          remoteCheckpointId = null
          checkpointedBlobs.clear()
        }
      }
      is CheckpointResult.OtherFailure -> {
        thisLogger().warn("Failed to refresh checkpoint, will retry on next sync")
        // Do nothing, will retry on next sync
      }
    }
  }
}


package com.augmentcode.intellij.index.ignore

import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.readText
import java.nio.file.NoSuchFileException
import com.augmentcode.intellij.ignore.GitIgnoreFlagWrapper as GitIgnore

class SingleIgnoreStack(val ignoreFileName: String, val ignoreFile: VirtualFile?, val top: IgnoreStackEntry?) {
  fun buildAtop(dir: VirtualFile): SingleIgnoreStack {
    if (dir == ignoreFile?.parent) {
      return this
    }

    val ignoreFile = dir.findChild(ignoreFileName) ?: return this

    try {
      val rules = GitIgnore(ignoreFile.readText())
      val newTop = IgnoreStackEntry(ignoreFile, rules, top)
      return SingleIgnoreStack(ignoreFileName, ignoreFile, newTop)
    } catch (e: NoSuchFileException) {
      // If we can't read the file or it has invalid syntax, just skip it
      return this
    }
  }

  suspend fun isAccepted(file: VirtualFile): Boolean? {
    var curr: IgnoreStackEntry? = top
    while (curr != null) {
      val result: Boolean? = curr.isIgnored(file)
      if (result != null) {
        return !result
      }
      // The path was _implicitly_ accepted. Keep going to see what higher-
      // level ignore files have to say about it.
      curr = curr.next
    }

    // Paths that are not explicitly ignored are accepted/indexed.
    return null
  }
}

package com.augmentcode.intellij.listeners

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileContentChangeEvent
import com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.intellij.openapi.vfs.newvfs.events.VFileMoveEvent
import com.intellij.openapi.vfs.newvfs.events.VFilePropertyChangeEvent
import com.intellij.util.indexing.FileBasedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Listens for VFS events to maintain synchronization with remote system.
 * Handles file deletions and moves to ensure proper cleanup of remote references.
 */
class AugmentVFSListener(private val project: Project, private val cs: CoroutineScope) : BulkFileListener {
  override fun before(events: List<VFileEvent>) {
    super.before(events)

    val syncManager = AugmentRemoteSyncingManager.getInstance(project)

    events.forEach { event ->
      if (requiresIgnoreFileCheck(event)) {
        cs.launch {
          handleIgnoreFileChange(event.file)
        }
      }

      if (requiresDirectoryRemoval(event)) {
        cs.launch {
          val (oldPath, newPath) = getOldAndNewPath(event)
          thisLogger().info("Directory moved or deleted: $oldPath, $newPath")
          syncManager.notifyDirectoryRemoved(oldPath)
        }
      } else if (requiresContentRemoval(event)) {
        cs.launch {
          event.file?.path?.let { syncManager.notifyContentRemoved(it) }
        }
      }
    }
  }

  override fun after(events: List<VFileEvent>) {
    super.after(events)

    events.asSequence()
      .filterIsInstance<VFileMoveEvent>()
      .map { it.file }
      .forEach { FileBasedIndex.getInstance().requestReindex(it) }
  }

  private fun handleIgnoreFileChange(file: VirtualFile?) {
    if (file == null) return
    if (PathFilterService.ignoreFilenames.contains(file.name)) {
      val root = AugmentRoot.findRelativePathWithRoot(project, file)?.rootFile ?: return
      PathFilterService.getInstance(project).uncachePathFilter(root)
    }
  }

  private fun requiresDirectoryRemoval(event: VFileEvent): Boolean =
    (event is VFilePropertyChangeEvent && event.propertyName == "name" && event.file.isDirectory) ||
      (event is VFileMoveEvent && event.file.isDirectory) ||
      (event is VFileDeleteEvent && event.file.isDirectory)

  private fun getOldAndNewPath(event: VFileEvent): Pair<String, String> =
    when (event) {
      is VFilePropertyChangeEvent -> Pair(event.oldPath, event.file.path)
      is VFileMoveEvent -> Pair(event.oldPath, event.file.path)
      is VFileDeleteEvent -> Pair(event.file.path, "")
      else -> Pair("", "")
    }

  private fun requiresContentRemoval(event: VFileEvent): Boolean =
    event is VFileDeleteEvent ||
      event is VFileMoveEvent ||
      (event is VFilePropertyChangeEvent && event.propertyName == "name")

  private fun requiresIgnoreFileCheck(event: VFileEvent): Boolean = requiresContentRemoval(event) || event is VFileContentChangeEvent
}

package com.augmentcode.intellij.pluginstate

import com.augmentcode.api.ModelConfig
import com.augmentcode.api.UserTier
import com.augmentcode.intellij.settings.AugmentSettings

data class AugmentModel(
  val defaultModelName: String,
  val supportedLanguages: List<AugmentLanguage>,
  val userTier: UserTier,
  val suggestedPrefixCharCount: Int,
  val suggestedSuffixCharCount: Int,
) {
  companion object {
    fun fromModelConfig(modelConfig: ModelConfig): AugmentModel {
      val model =
        modelConfig.findModel(AugmentSettings.instance.modelName)
          ?: modelConfig.findDefaultModel()

      if (model == null) {
        throw IllegalStateException("Failed to find a model from the model config")
      }

      return modelConfig.let {
        AugmentModel(
          defaultModelName = it.defaultModelName,
          supportedLanguages = it.supportedLanguages.map { l -> AugmentLanguage(l.name, l.extensions) },
          userTier = it.userTier,
          suggestedPrefixCharCount = model.suggestedPrefixCharCount,
          suggestedSuffixCharCount = model.suggestedSuffixCharCount,
        )
      }
    }
  }
}

// This is a thing data class so we can compare AugmentLanguage objects easily in tests
data class AugmentLanguage(val name: String, val extensions: Set<String>)

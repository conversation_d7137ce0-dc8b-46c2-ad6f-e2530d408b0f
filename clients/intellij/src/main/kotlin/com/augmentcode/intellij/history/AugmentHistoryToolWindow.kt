package com.augmentcode.intellij.history

import com.augmentcode.api.FeedbackRating
import com.intellij.diff.util.DiffDrawUtil
import com.intellij.diff.util.TextDiffType
import com.intellij.icons.AllIcons
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.EditorFactory
import com.intellij.openapi.editor.colors.EditorColorsListener
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.editor.ex.EditorEx
import com.intellij.openapi.editor.highlighter.EditorHighlighterFactory
import com.intellij.openapi.fileTypes.FileTypeManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.SimpleToolWindowPanel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import com.intellij.util.IconUtil
import com.intellij.util.concurrency.annotations.RequiresEdt
import com.intellij.util.ui.JBUI
import com.jgoodies.forms.factories.ButtonBarFactory
import kotlinx.coroutines.*
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.*
import javax.swing.Box.Filler
import javax.swing.border.TitledBorder
import javax.swing.event.DocumentListener

class AugmentHistoryToolWindow(
  private val project: Project,
) : SimpleToolWindowPanel(true, true),
  Disposable {
  companion object {
    const val ID = "History"
  }

  private val cs = CoroutineScope(Dispatchers.IO)
  private val historyModel = AugmentHistoryModel.getInstance(project)
  private val rootPanel =
    JPanel().apply {
      // Use a box layout because it lets us insert items at the front
      layout = BoxLayout(this, BoxLayout.Y_AXIS)
    }
  private val editors = mutableListOf<Editor>()
  private val positiveColor
    get() = JBUI.CurrentTheme.RunWidget.RUN_ICON_COLOR
  private val negativeColor
    get() = JBUI.CurrentTheme.RunWidget.STOP_BACKGROUND

  // given a string, limit it to n lines either at the front or back
  private fun stripToNLines(
    text: String?,
    takeFromFront: Boolean = true,
    n: Int = 5,
  ): String {
    if (text == null) return ""
    val lines = text.lines()
    if (lines.size <= n) return text
    return if (takeFromFront) lines.take(n).joinToString("\n") else lines.takeLast(n).joinToString("\n")
  }

  // update the editors to match the current theme
  private fun refreshEditors() {
    editors.forEach { editor ->
      (editor as EditorEx).colorsScheme = EditorColorsManager.getInstance().globalScheme
      editor.component.repaint()
    }
  }

  private fun formatDate(time: Long): String {
    val date = java.util.Date(time)
    val formatter = java.text.SimpleDateFormat("h:mm a 'on' MM/dd/yyyy")
    return formatter.format(date)
  }

  // highlight a range of text in an editor to match the user's editor for diffs
  private fun highlight(
    editor: Editor,
    start: Int,
    end: Int,
  ) {
    DiffDrawUtil.createInlineHighlighter(editor, start, end, TextDiffType.INSERTED)
  }

  init {
    val scrollPane =
      JBScrollPane(rootPanel).apply {
        border = JBUI.Borders.empty()
        verticalScrollBar.unitIncrement = 16
      }
    setContent(scrollPane)
    addInitialCompletionPanels()
    addSubscriptions()
  }

  private fun addSubscriptions() {
    // Subscribe to completion updates
    project.messageBus.connect(this).subscribe(
      AugmentRecentCompletionsListener.TOPIC,
      object : AugmentRecentCompletionsListener {
        override fun onNewCompletion(item: AugmentHistoryEntry) {
          invokeLater {
            addCompletionPanel(item)
          }
        }
      },
    )
    // Subscribe to theme updates
    project.messageBus.connect(this).subscribe(
      EditorColorsManager.TOPIC,
      EditorColorsListener {
        refreshEditors()
      },
    )
  }

  @RequiresEdt
  private fun addInitialCompletionPanels() {
    rootPanel.removeAll()
    historyModel.getCompletions().forEach { addCompletionPanel(it) }
  }

  @RequiresEdt
  private fun addCompletionPanel(completion: AugmentHistoryEntry) {
    val panel = completionPanel(completion)
    rootPanel.add(panel, 0)

    // Remove the oldest component if we exceed the max size
    while (rootPanel.componentCount > AugmentHistoryModel.MAX_COMPLETIONS) {
      rootPanel.remove(rootPanel.componentCount - 1)
    }

    rootPanel.revalidate()
    rootPanel.repaint()
  }

  private fun completionPanel(completion: AugmentHistoryEntry): JPanel =
    JPanel(BorderLayout(0, 5)).apply {
      border = JBUI.Borders.empty(6, 6, 6, 12) // 12 on right for scrollbar

      add(infoPanel(completion), BorderLayout.NORTH)
      add(editorPanel(completion), BorderLayout.CENTER)
      add(feedbackPanel(completion), BorderLayout.SOUTH)
    }

  private fun feedbackPanel(completion: AugmentHistoryEntry): JPanel =
    JPanel(BorderLayout()).apply {
      // Feedback text area
      add(feedbackTextArea(completion), BorderLayout.NORTH)

      add(
        JPanel().apply {
          layout = BoxLayout(this, BoxLayout.X_AXIS)

          add(
            ButtonBarFactory.buildLeftAlignedBar(
              submitFeedbackButton(completion, positive = true, this),
              submitFeedbackButton(completion, positive = false, this),
            ),
            BorderLayout.EAST,
          )
        },
        BorderLayout.SOUTH,
      )
    }

  private fun feedbackTextArea(completion: AugmentHistoryEntry): JBTextArea =
    JBTextArea().apply {
      lineWrap = true
      wrapStyleWord = false
      text = historyModel.getFeedback(completion.result.requestId).feedbackText
      border =
        BorderFactory.createCompoundBorder(
          BorderFactory.createTitledBorder("Feedback").apply {
            titlePosition = TitledBorder.ABOVE_TOP
            titleJustification = TitledBorder.LEFT
            titleFont = JBUI.Fonts.label()
          },
          BorderFactory.createEmptyBorder(4, 4, 4, 4),
        )

      document.addDocumentListener(
        object : DocumentListener {
          fun onUpdate() {
            val feedback = historyModel.getFeedback(completion.result.requestId)
            historyModel.setFeedback(
              completion.result.requestId,
              feedback.copy(feedbackText = text),
            )
          }

          override fun insertUpdate(e: javax.swing.event.DocumentEvent?) = onUpdate()

          override fun removeUpdate(e: javax.swing.event.DocumentEvent?) = onUpdate()

          override fun changedUpdate(e: javax.swing.event.DocumentEvent?) {}
        },
      )
    }

  private fun submitFeedbackButton(
    completion: AugmentHistoryEntry,
    positive: Boolean,
    container: JPanel,
  ): JButton =
    JButton().apply {
      if (historyModel.getFeedback(completion.result.requestId).sent) {
        isSelected = true
        icon =
          IconUtil.colorize(
            if (positive) AllIcons.Ide.LikeSelected else AllIcons.Ide.DislikeSelected,
            if (positive) positiveColor else negativeColor,
          )
      } else {
        icon = if (positive) AllIcons.Ide.Like else AllIcons.Ide.Dislike
      }

      addActionListener {
        val feedback = historyModel.getFeedback(completion.result.requestId)
        val alreadySent = feedback.sent
        // TODO (JW) Allow resending feedback after it's been sent once
        if (alreadySent) return@addActionListener

        historyModel.setFeedback(
          completion.result.requestId,
          feedback.copy(
            selectedRating = if (positive) FeedbackRating.POSITIVE else FeedbackRating.NEGATIVE,
          ),
        )
        // TODO: abort if failed
        historyModel.sendFeedbackAsync(
          completion.result.requestId,
          onComplete = { success -> onSendFeedback(success, positive, container, this) },
        )
      }
    }

  private fun onSendFeedback(
    success: Boolean,
    positive: Boolean,
    container: JPanel,
    button: JButton,
  ) {
    if (!success) return
    // need to run this on the EDT
    invokeLater {
      container.add(Filler(Dimension(0, 0), Dimension(4, 0), Dimension(10, 0)))
      container.add(JLabel("Thanks for the feedback!"))
      button.isSelected = true
      button.icon =
        IconUtil.colorize(
          if (positive) AllIcons.Ide.LikeSelected else AllIcons.Ide.DislikeSelected,
          if (positive) positiveColor else negativeColor,
        )
      container.revalidate()
      container.repaint()
    }
  }

  private fun infoPanel(completion: AugmentHistoryEntry): JBTextArea =
    JBTextArea().apply {
      text =
        " ${formatDate(completion.timeReceived)}\n" +
        " Request ID: ${completion.result.requestId}\n" + // TODO: Make request ID copyable
        " Path: ${completion.request.path}" // TODO: Make path a link
      isEditable = false
      wrapStyleWord = false
      lineWrap = true
    }

  private fun editorPanel(completion: AugmentHistoryEntry): JPanel =
    JPanel(BorderLayout()).apply {
      val completionItem = completion.result.completionItems?.firstOrNull()

      val suffix = // "true" suffix without any completions
        completion.request.suffix.removePrefix(completionItem?.skippedSuffix ?: "")

      val start = stripToNLines(completion.request.prompt, takeFromFront = false)
      val suggestion = completionItem?.text ?: ""
      val suggestionSuffix = completionItem?.suffixReplacementText ?: ""
      val end = stripToNLines(suffix, takeFromFront = true)
      val skipped = completionItem?.skippedSuffix ?: ""

      // suggestionSuffix contains skippedSuffix, we distinguish them later by color
      val fullContent = start + suggestion + suggestionSuffix + end

      val document = EditorFactory.getInstance().createDocument(fullContent)
      val fileType = FileTypeManager.getInstance().getFileTypeByFileName(completion.request.path)
      // cast to EditorEx for access to color scheme settings
      val editor = EditorFactory.getInstance().createEditor(document, project, fileType, true) as EditorEx
      val globalColors = EditorColorsManager.getInstance().globalScheme
      editor.apply {
        settings.apply {
          isLineNumbersShown = false
          isLineMarkerAreaShown = false
          isRightMarginShown = false
          isFoldingOutlineShown = true
          additionalLinesCount = 0
          additionalColumnsCount = 0
          isRightMarginShown = false
          isUseSoftWraps = true
        }

        // syntax highlighting to match the user's editor
        highlighter = EditorHighlighterFactory.getInstance().createEditorHighlighter(project, completion.request.path)
        colorsScheme = globalColors

        // highlight the suggestion and skipped text
        highlight(editor, start.length, start.length + suggestion.length)
        if (skipped.isNotEmpty()) {
          highlight(
            editor,
            start.length + suggestion.length + skipped.length,
            start.length + suggestion.length + suggestionSuffix.length,
          )
        }
      }

      add(editor.component, BorderLayout.CENTER)
      editors.add(editor)
    }

  override fun dispose() {
    editors.forEach { EditorFactory.getInstance().releaseEditor(it) }
    editors.clear()
    cs.cancel()
  }
}

package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.settings.FeatureFlagManager
import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.intellij.util.messages.Topic

@Service(Service.Level.PROJECT)
class AugmentSyncingPermissionTracker(
  private val project: Project,
) {
  companion object {
    val SYNCING_PERMISSION_TOPIC =
      Topic.create("augment.syncing.permission", SyncingPermissionListener::class.java)

    const val SYNCING_ENABLED_KEY = "augment.syncing.enabled"

    fun getInstance(project: Project): AugmentSyncingPermissionTracker = project.getService(AugmentSyncingPermissionTracker::class.java)
  }

  private val propertiesComponent = PropertiesComponent.getInstance(project)
  private val featureFlagManager = FeatureFlagManager.instance

  fun enableSyncing() {
    propertiesComponent.setValue(SYNCING_ENABLED_KEY, true)
    project.messageBus.syncPublisher(SYNCING_PERMISSION_TOPIC).onSyncingPermissionGranted()
  }

  fun needsSyncingPermission(): Boolean {
    // TODO AU-7837 Re-enable sync permission check
//    if (!featureFlagManager.askForSyncPermission) {
//      return false
//    }
//    return !propertiesComponent.getBoolean(SYNCING_ENABLED_KEY, false)
    return false
  }
}

interface SyncingPermissionListener {
  fun onSyncingPermissionGranted()
}

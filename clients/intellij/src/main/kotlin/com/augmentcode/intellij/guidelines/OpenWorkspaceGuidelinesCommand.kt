package com.augmentcode.intellij.guidelines

import com.augmentcode.intellij.settings.FeatureFlagManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.WriteActionAware
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import org.jetbrains.annotations.VisibleForTesting

/**
 * Command to open the workspace guidelines file for editing.
 * If the file doesn't exist, it will be created.
 */
open class OpenWorkspaceGuidelinesCommand : AnAction(), WriteActionAware {
  companion object {
    private val logger = thisLogger()
  }

  override fun actionPerformed(e: AnActionEvent) {
    val project = e.project ?: return
    openWorkspaceGuidelines(project)
  }

  override fun startInWriteAction(): Boolean = true // so we can create the file if it doesn't exist

  override fun update(e: AnActionEvent) {
    // Only enable this action if guidelines are enabled
    val guidelinesEnabled = FeatureFlagManager.instance.guidelinesEnabled()
    logger.info("Updating workspace guidelines action state. Enabled: $guidelinesEnabled")
    e.presentation.isEnabled = guidelinesEnabled
  }

  /**
   * Opens the workspace guidelines file for editing.
   * If the file doesn't exist, it will be created.
   *
   * @param project The project to open guidelines for
   */
  @VisibleForTesting
  fun openWorkspaceGuidelines(project: Project) {
    val guidelinesService = getGuidelinesService(project)
    guidelinesService.openWorkspaceGuidelines()
  }

  /**
   * Gets the guidelines service instance. Extracted for testing.
   */
  @VisibleForTesting
  protected fun getGuidelinesService(project: Project): GuidelinesService {
    return GuidelinesService.getInstance(project)
  }
}

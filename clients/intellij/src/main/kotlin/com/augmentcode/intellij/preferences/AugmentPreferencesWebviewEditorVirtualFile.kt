package com.augmentcode.intellij.preferences

import com.augmentcode.intellij.webviews.AugmentWebview
import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.WebviewFactory
import com.augmentcode.intellij.webviews.preferences.PreferencesMessagingService
import com.intellij.ide.plugins.UIComponentFileEditor
import com.intellij.ide.plugins.UIComponentVirtualFile
import com.intellij.openapi.fileEditor.FileEditorManagerKeys
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import java.awt.BorderLayout
import javax.swing.JPanel

class AugmentPreferencesWebviewEditorVirtualFile(
  private val project: Project,
  private val cs: CoroutineScope,
) : UIComponentVirtualFile(
    PREFERENCES_VIRTUAL_FILE_NAME,
    null,
  ) {
  companion object {
    const val PREFERENCES_VIRTUAL_FILE_NAME = "Augment Preferences"
  }

  init {
    putUserData(FileEditorManagerKeys.FORBID_TAB_SPLIT, true)
  }

  private val entryFilePath = "preference.html"

  var webview: AugmentWebview? = null

  override fun createContent(editor: UIComponentFileEditor): Content {
    return Content {
      // Create a webview with a load handler for preferences
      webview =
        WebviewFactory.create(
          project,
          entryFilePath,
          AugmentWebviewStateKey.PREFERENCES_STATE,
          PreferencesMessagingService.getInstance(project),
          editor,
        )

      JPanel(BorderLayout()).apply {
        webview?.let { add(it.browser.component, BorderLayout.CENTER) }
      }
    }
  }
}

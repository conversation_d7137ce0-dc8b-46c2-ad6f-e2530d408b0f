@file:Suppress("UnstableApiUsage")

package com.augmentcode.intellij.status

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateListener
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.settings.SettingsChangeListener
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.actionSystem.ex.ActionUtil
import com.intellij.openapi.application.*
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.fileEditor.TextEditorWithPreview
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.openapi.ui.popup.ListPopup
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.wm.StatusBarWidget
import com.intellij.openapi.wm.impl.status.EditorBasedStatusBarPopup
import com.intellij.util.messages.MessageBusConnection
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.swing.Icon

class AugmentStatusBar(project: Project) : EditorBasedStatusBarPopup(project, false) {
  @Volatile
  private var currentState: WidgetState =
    WidgetState(StateDefinitions.Base.tooltip, "", true).also {
      it.icon = StateDefinitions.Base.icon
    }

  override fun ID(): String = "AugmentStatus"

  init {
    scheduleUpdate(StateManager.getInstance(project).getPriorityState())
  }

  override fun createInstance(project: Project): StatusBarWidget {
    return AugmentStatusBar(project)
  }

  override fun createPopup(context: DataContext): ListPopup {
    return JBPopupFactory.getInstance().createActionGroupPopup(
      "Augment",
      AugmentStatusPopUp(project),
      context,
      false,
      null,
      10,
    )
  }

  override fun getWidgetState(file: VirtualFile?): WidgetState {
    return currentState
  }

  private fun scheduleUpdate(state: StateDefinition) {
    // Set the current state so `getWidgetState` has the latest values when called
    currentState =
      WidgetState(state.tooltip, AugmentBundle.message("augment.status-bar.title"), true).also {
        it.icon = state.icon
      }

    // Trigger  status bar update
    invokeLater {
      update {
        myStatusBar?.updateWidget(ID())
      }
    }
  }

  // The kotlin compiler complains that `registerCustomerListeners` is deprecated,
  // but `registerCustomerListeners` with no parameters is deprecated, the function
  // with connection is not.
  @Suppress("OVERRIDE_DEPRECATION")
  override fun registerCustomListeners(connection: MessageBusConnection) {
    super.registerCustomListeners(connection)
    connection.subscribe(
      FileEditorManagerListener.FILE_EDITOR_MANAGER,
      object : FileEditorManagerListener {
        override fun fileOpened(
          source: FileEditorManager,
          file: VirtualFile,
        ) {
          fixupPreviewEditor(source.selectedEditor as? TextEditorWithPreview)
        }

        override fun fileClosed(
          source: FileEditorManager,
          file: VirtualFile,
        ) {
          fixupPreviewEditor(source.selectedEditor as? TextEditorWithPreview)
        }

        override fun selectionChanged(event: FileEditorManagerEvent) {
          fixupPreviewEditor(event.newEditor as? TextEditorWithPreview)
        }
      },
    )

    var pluginStatusDisposal: () -> Unit = { }
    val pluginStatusMutex = Mutex()
    val updateStatusForPluginState =
      suspend {
        // The mutex here ensures we clean up the previous state before setting the new one
        pluginStatusMutex.withLock {
          pluginStatusDisposal()

          val statusDefinition = statusDefinitionForState(PluginStateService.instance.state)
          val disposal = StateManager.getInstance(project).setState(statusDefinition)
          pluginStatusDisposal = {
            thisLogger().info("Disposing status bar state for plugin state: ${statusDefinition.tooltip}")
            disposal()
          }
        }
      }

    PluginStateService.instance.subscribe(
      connection,
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext?,
          state: PluginState,
        ) {
          // The onStateChange event is called from a coroutine, so we can
          // safely block this thread.
          runBlocking {
            updateStatusForPluginState()
          }
        }
      },
    )

    // Initialize the plugin state
    runBlocking {
      updateStatusForPluginState()
    }

    var autoCompletionsDisposal: () -> Unit = {}
    if (!AugmentSettings.instance.inlineCompletionEnabled) {
      // Inline completion is disabled completely
      autoCompletionsDisposal = StateManager.getInstance(project).setState(StateDefinitions.AutoCompletionsDisabled)
    }

    connection.subscribe(
      SettingsChangeListener.TOPIC,
      object : SettingsChangeListener {
        override fun onChange() {
          autoCompletionsDisposal()

          if (!AugmentSettings.instance.inlineCompletionEnabled) {
            // Inline completion is disabled completely
            autoCompletionsDisposal = StateManager.getInstance(project).setState(StateDefinitions.AutoCompletionsDisabled)
          }
        }
      },
    )

    // Ensure. this is added last so we don't schedule updates from changes
    // above.
    connection.subscribe(
      StateChangeListener.TOPIC,
      object : StateChangeListener {
        override fun onChange(state: StateDefinition) {
          scheduleUpdate(state)
        }
      },
    )
  }

  private fun fixupPreviewEditor(editorWithPreview: TextEditorWithPreview?) {
    // to workaround https://youtrack.jetbrains.com/issue/PY-60072
    // we trick ActionUtil by setting a client property on the text editor "hidden" by the full preview
    editorWithPreview?.editor?.contentComponent?.putClientProperty(ActionUtil.ALLOW_ACTION_PERFORM_WHEN_HIDDEN, true)
  }

  private fun statusDefinitionForState(state: PluginState): StateDefinition {
    thisLogger().info("Updating status bar for plugin state: $state")
    when (state) {
      PluginState.UNINITIALIZED -> {
        return StateDefinitions.Base
      }
      PluginState.INITIALIZING -> {
        return StateDefinitions.Initializing
      }
      PluginState.SIGN_IN_REQUIRED -> {
        return StateDefinitions.SignInNeeded
      }
      PluginState.GET_MODEL_INFO_FAILED, PluginState.FAILED -> {
        return StateDefinitions.GetModelInfoFailed
      }
      PluginState.ENABLED -> {
        return StateDefinitions.Enabled
      }
    }
  }

  internal fun icon(): Icon? {
    return currentState.icon
  }

  internal fun toolTip(): String? {
    return currentState.toolTip
  }
}

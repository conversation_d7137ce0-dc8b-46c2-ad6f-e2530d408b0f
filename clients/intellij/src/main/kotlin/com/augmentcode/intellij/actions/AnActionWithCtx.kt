package com.augmentcode.intellij.actions

import com.augmentcode.intellij.pluginstate.PluginContext
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent

abstract class AnActionWithCtx : AnAction() {
  final override fun update(e: AnActionEvent) {
    val ctx = PluginStateService.instance.context
    e.presentation.isEnabledAndVisible = ctx != null
    if (ctx == null) return
    updateWithCtx(e, ctx)
  }

  abstract fun updateWithCtx(
    e: AnActionEvent,
    ctx: PluginContext,
  )
}

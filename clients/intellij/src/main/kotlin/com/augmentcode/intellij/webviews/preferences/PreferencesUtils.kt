package com.augmentcode.intellij.webviews.preferences

import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.rpc.ChatModelReply
import com.augmentcode.rpc.ChatModelReplyData
import com.augmentcode.rpc.ChatModelReplyError

object PreferencesUtils {
  private const val PREFERENCES_COLLECTION_ENABLED_PROPERTY = "augmentcode.preferencesCollectionEnabled"

  /**
   * Checks if preferences collection is enabled.
   * Preferences are enabled when both:
   * 1. The custom property "augmentcode.preferencesCollectionEnabled" is true
   * 2. The feature flag for preference collection is enabled
   */
  fun preferencesEnabled(): Boolean {
    val customPropertyEnabled = CustomPropertyReader.readBoolean(PREFERENCES_COLLECTION_ENABLED_PROPERTY)
    val featureFlagEnabled = PluginStateService.instance.context?.flags?.preferenceCollectionAllowed ?: false

    return customPropertyEnabled && featureFlagEnabled
  }

  fun hasSufficientComparableModels(): Boolean {
    // Check ELO model configuration - same logic as VSCode
    // https://github.com/augmentcode/augment/blob/fe68adc/clients/vscode/src/main-panel/apps/chat-webview-app.ts#L1468-L1476
    val eloConfig = PluginStateService.instance.context?.flags?.eloModelConfiguration ?: return false

    // If no models are provided from backend, don't enable preferences
    val hasHighPriorityModels =
      eloConfig.highPriorityModels?.isNotEmpty() == true &&
        eloConfig.highPriorityModels.size >= 2
    val hasRegularBattleModels =
      eloConfig.regularBattleModels?.isNotEmpty() == true &&
        eloConfig.regularBattleModels.size >= 2

    val highPriorityThreshold = eloConfig.highPriorityThreshold ?: 0.0

    return !(
      ((!hasHighPriorityModels) && highPriorityThreshold != 0.0) ||
        ((!hasRegularBattleModels) && highPriorityThreshold < 1.0)
    )
  }

  fun comparisonNotAvailableMessage(requestId: String): ChatModelReply {
    return ChatModelReply.newBuilder()
      .setData(
        ChatModelReplyData.newBuilder()
          .setText("")
          .setRequestId(requestId)
          .setError(
            ChatModelReplyError.newBuilder()
              .setDisplayErrorMessage("Model comparison is not available at this time.")
              .build(),
          )
          .build(),
      )
      .build()
  }
}

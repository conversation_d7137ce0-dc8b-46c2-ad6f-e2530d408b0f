package com.augmentcode.intellij.actions

import com.augmentcode.intellij.api.AugmentAPI
import com.intellij.icons.AllIcons
import com.intellij.openapi.actionSystem.ActionGroup
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.Project
import kotlinx.coroutines.runBlocking

/**
 * Hamburger menu for the Augment tool window that provides access to settings, help, billing, and sign out options.
 */
class AugmentHamburgerMenu(private val project: Project) : ActionGroup("Augment", true) {
  init {
    templatePresentation.icon = AllIcons.General.LayoutEditorOnly
  }

  override fun getChildren(e: AnActionEvent?): Array<AnAction> {
    val actions = mutableListOf<AnAction>()

    // Add settings action
    actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.OpenSettingsWebviewAction"))

    // Add help action
    actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ShowHelpAction"))

    // Add billing/account action
    actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.ManageAccountAction"))

    // Check if user is signed in
    val isSignedIn = runBlocking { AugmentAPI.instance.available() }

    // Add sign out action if signed in
    if (isSignedIn) {
      actions.add(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.SignOutAction"))
    }

    return actions.toTypedArray()
  }
}

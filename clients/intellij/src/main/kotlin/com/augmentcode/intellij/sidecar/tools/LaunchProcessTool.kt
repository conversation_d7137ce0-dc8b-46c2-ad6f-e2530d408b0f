package com.augmentcode.intellij.sidecar.tools

import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.sidecarrpc.convertStruct
import com.augmentcode.sidecar.rpc.LaunchProcessInputSchema
import com.augmentcode.sidecar.rpc.chat.ChatHistoryItem
import com.augmentcode.sidecar.rpc.tools.ToolCallResponse
import com.google.protobuf.Struct
import com.intellij.execution.ExecutionManager
import com.intellij.execution.RunManager
import com.intellij.execution.configuration.EnvironmentVariablesData
import com.intellij.execution.executors.DefaultRunExecutor
import com.intellij.execution.runners.ExecutionEnvironmentBuilder
import com.intellij.execution.runners.ProgramRunner
import com.intellij.execution.ui.RunContentDescriptor
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.project.Project
import com.intellij.sh.run.ShConfigurationType
import com.intellij.sh.run.ShRunConfiguration
import com.intellij.util.PathUtil
import kotlinx.coroutines.CompletableDeferred
import org.jetbrains.plugins.terminal.TerminalProjectOptionsProvider

class LaunchProcessTool(private val project: Project) : IdeTool {
  override val name = "launch-process"

  override val description: String
    get() {
      val shellPath = TerminalProjectOptionsProvider.getInstance(project).shellPath
      val shellName = PathUtil.getFileName(shellPath)
      return """
        Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`).

        If `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to
        `max_wait_seconds` seconds. If the process ends during this period, the tool call returns. If the timeout
        expires, the process will continue running in the background but the tool call will return. You can then
        interact with the process using the other process tools.

        Note: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`
        while another is running, the tool will return an error.

        If `wait=false`, launches a background process in a separate terminal. This returns immediately, while the
        process keeps running in the background.

        Notes:
        - Use `wait=true` processes when the command is expected to be short, or when you can't
        proceed with your task until the process is complete. Use `wait=false` for processes that are
        expected to run in the background, such as starting a server you'll need to interact with, or a
        long-running process that does not need to complete before proceeding with the task.
        - If this tool returns while the process is still running, you can continue to interact with the process
        using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
        - You can use this tool to interact with the user's local version control system. Do not use the
        retrieval tool for that purpose.
        - If there is a more specific tool available that can perform the function, use that tool instead of
        this one.

        The OS is ${System.getProperty("os.name")}. The shell is '$shellName'.
        """.trimIndent().trimStart()
    }

  override val inputMessageDescriptor = LaunchProcessInputSchema.getDescriptor()

  override suspend fun call(
    project: Project,
    requestId: String,
    toolUseId: String,
    input: Struct,
    chatHistory: List<ChatHistoryItem>,
    conversationId: String,
  ): ToolCallResponse {
    val launchRequest = convertStruct(input) { LaunchProcessInputSchema.newBuilder() }.build()

    var workingDirectory = launchRequest.cwd
    if (workingDirectory.isNullOrBlank()) {
      workingDirectory = AugmentRoot.findActiveProjectRoot(project)?.path
    }
    if (workingDirectory == null) {
      return ToolCallResponse.newBuilder()
        .setText("Failed to determine working directory")
        .setIsError(true)
        .build()
    }

    // We now crate a Shell run configuration, and then we'll execute it.
    // This will open a Tab in "Run" tool window.
    val configurationSettings =
      RunManager.getInstance(project).createConfiguration("Augment", ShConfigurationType::class.java)
    val runConfiguration = configurationSettings.configuration as ShRunConfiguration
    runConfiguration.isExecuteScriptFile = false
    runConfiguration.scriptText = launchRequest.command
    runConfiguration.scriptWorkingDirectory = workingDirectory
    // By default, it runs in an existing Terminal window. Unfortunately, in this scenario
    // ProgramRunner.Callback.processStarted will not get the RunContentDescriptor.
    // So we force it to open a new window.
    runConfiguration.isExecuteInTerminal = false

    val shellPath = TerminalProjectOptionsProvider.getInstance(project).shellPath
    if (shellPath.endsWith("bash") || shellPath.endsWith("zsh")) {
      runConfiguration.envData =
        EnvironmentVariablesData.create(
          mapOf(
            "PAGER" to "cat",
            "LESS" to "-FX",
            "GIT_PAGER" to "cat",
          ),
          true,
        )
    }

    val executionEnvBuilder =
      ExecutionEnvironmentBuilder.createOrNull(DefaultRunExecutor.getRunExecutorInstance(), runConfiguration)
        ?: return ToolCallResponse.newBuilder()
          .setText("Failed to create execution environment")
          .setIsError(true)
          .build()

    // now run
    val completable = CompletableDeferred<RunContentDescriptor?>()
    val executionEnv =
      executionEnvBuilder.build(
        object : ProgramRunner.Callback {
          override fun processStarted(descriptor: RunContentDescriptor?) {
            completable.complete(descriptor)
          }

          override fun processNotStarted(error: Throwable?) {
            completable.completeExceptionally(error ?: Exception("Failed to launch process"))
          }
        },
      )
    runInEdt {
      executionEnv.runner.execute(executionEnv)
    }
    val runDescriptor =
      try {
        completable.await()
      } catch (e: Throwable) {
        return ToolCallResponse.newBuilder()
          .setText(e.message)
          .setIsError(true)
          .build()
      }
    if (runDescriptor == null) {
      return ToolCallResponse.newBuilder()
        .setText("Failed to get process descriptor")
        .setIsError(true)
        .build()
    }

    val processHandler = runDescriptor.processHandler
    if (processHandler == null) {
      return ToolCallResponse.newBuilder()
        .setText("Failed to get process handler")
        .setIsError(true)
        .build()
    }

    val terminalInfo =
      AugmentTerminalInfo(toolUseId, runDescriptor.executionId, launchRequest.command, workingDirectory)
    terminalInfo.attach(processHandler)
    // If the request defines a timeout, use it, otherwise wait indefinitely.
    if (launchRequest.wait && launchRequest.maxWaitSeconds >= 0) {
      processHandler.waitFor(launchRequest.maxWaitSeconds * 1000)
    } else {
      return ToolCallResponse.newBuilder()
        .setText("Process launched with PID ${runDescriptor.executionId}")
        .build()
    }

    val exitCode =
      try {
        processHandler.exitCode
      } catch (e: Throwable) {
        return ToolCallResponse.newBuilder()
          .setText(e.message)
          .setIsError(true)
          .build()
      }

    if (exitCode == null) {
      return ToolCallResponse.newBuilder()
        .setText(
          """
          Command is still running after ${launchRequest.maxWaitSeconds} seconds. You can use read-process to get more output
          and kill-process to terminate it if needed.
          PID ${runDescriptor.executionId}
          Output so far:
          <stdout>
          ${terminalInfo.output.stdout}
          </stdout>
          <stderr>
          ${terminalInfo.output.stderr}
          </stderr>
          """.trimIndent().trimStart(),
        )
        .build()
    }

    return ToolCallResponse.newBuilder()
      .setText(
        """
        Here are the results from executing the command.
        <return-code>
        $exitCode
        </return-code>
        <stdout>
        ${terminalInfo.output.stdout}
        </stdout>
        <stderr>
        ${terminalInfo.output.stderr}
        </stderr>
        """.trimIndent().trimStart(),
      )
      .build()
  }

  override fun cancelCall(toolUseId: String): Boolean {
    val processHandler =
      ExecutionManager.getInstance(project).getRunningProcesses().find {
        AugmentTerminalInfo.TERMINAL_INFO_KEY.get(it)?.toolUseId == toolUseId
      } ?: return false
    processHandler.destroyProcess()
    return true
  }
}

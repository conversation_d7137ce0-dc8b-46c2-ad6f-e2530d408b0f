package com.augmentcode.intellij.webviews.settings

import com.augmentcode.intellij.webviews.AbstractWebviewMessagingService
import com.augmentcode.rpc.AsyncWrapper
import com.google.protobuf.Any
import com.google.protobuf.Message
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

/**
 * This class will process messages from a webview and route them to the settings service (which
 * is the protobuf defined service).
 */
@Service(Service.Level.PROJECT)
class SettingsMessagingService(
  private val project: Project,
  private val cs: CoroutineScope,
) : AbstractWebviewMessagingService(project, cs) {
  private val settingsService = AugmentSettingsWebviewService(project, cs)
  private val logger = logger<SettingsMessagingService>()

  companion object {
    fun getInstance(project: Project): SettingsMessagingService = project.getService(SettingsMessagingService::class.java)
  }

  override fun processMessageFromWebview(
    jsonText: String,
    callback: suspend (String) -> Unit,
  ) {
    super.processMessageFromWebview(jsonText, callback)
  }

  override suspend fun processAsyncMessage(request: AsyncWrapper): Flow<Message> {
    val baseMessage = request.baseMsg
    val response =
      processSyncMessage(baseMessage).map { responseMsg ->
        AsyncWrapper.newBuilder()
          .setRequestId(request.requestId)
          .setBaseMsg(Any.pack(responseMsg))
          .build()
      }
    return response
  }

  override fun dispose() {}

  override suspend fun processSyncMessage(request: Any): Flow<Message> {
    return when {
      request.`is`(com.augmentcode.rpc.ToolConfigLoadedRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigLoadedRequest::class.java)
        flowOf(settingsService.toolConfigLoaded(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigGetDefinitionsRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigGetDefinitionsRequest::class.java)
        flowOf(settingsService.toolConfigGetDefinitions(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigSaveRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigSaveRequest::class.java)
        flowOf(settingsService.toolConfigSave(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigStartOAuthRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigStartOAuthRequest::class.java)
        flowOf(settingsService.toolConfigStartOAuth(message))
      }
      request.`is`(com.augmentcode.rpc.ToolConfigRevokeAccessRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ToolConfigRevokeAccessRequest::class.java)
        flowOf(settingsService.toolConfigRevokeAccess(message))
      }
      request.`is`(com.augmentcode.rpc.GetStoredMCPServersRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.GetStoredMCPServersRequest::class.java)
        flowOf(settingsService.getStoredMCPServers(message))
      }
      request.`is`(com.augmentcode.rpc.SetStoredMCPServersRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.SetStoredMCPServersRequest::class.java)
        flowOf(settingsService.setStoredMCPServers(message))
      }
      request.`is`(com.augmentcode.rpc.ExecuteInitialOrientationRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.ExecuteInitialOrientationRequest::class.java)
        flowOf(settingsService.executeInitialOrientation(message))
      }
      request.`is`(com.augmentcode.rpc.UpdateUserGuidelinesRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.UpdateUserGuidelinesRequest::class.java)
        flowOf(settingsService.updateUserGuidelines(message))
      }
      request.`is`(com.augmentcode.rpc.SignOutRequest::class.java) -> {
        val message = request.unpack(com.augmentcode.rpc.SignOutRequest::class.java)
        flowOf(settingsService.signOut(message))
      }
      else -> {
        logger.warn("Unknown message type: ${request.typeUrl}")
        emptyFlow()
      }
    }
  }
}



/**
 * Utility class for checking custom system properties defined in idea.properties.
 *
 * Use this for settings that should be hidden from users other than staging and AI tutors,
 * and shouldn't always be enabled for all users in those tenants.
 */
object CustomPropertyReader {
  const val CHAOS_MONKEY_EDT_ASSERTIONS = "augment.chaosMonkey.edtAssertions"

  fun readProperty(key: String): String? {
    return System.getProperty(key)
  }

  fun readBoolean(key: String): Boolean {
    return System.getProperty(key)?.toBoolean() ?: false
  }
}

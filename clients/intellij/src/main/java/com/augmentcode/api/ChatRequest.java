package com.augmentcode.api;

import java.util.List;

public class ChatRequest {
  public String model;
  public String path;
  public String prefix;
  public String selectedCode;
  public String suffix;
  public String message;
  public List<Exchange> chatHistory;
  public String lang;
  public BlobsPayload blobs;
  public List<String> userGuidedBlobs;
  public List<String> externalSourceIds;
  public Boolean enablePreferenceCollection;
  public String contextCodeExchangeRequestId;
  public FeatureDetectionFlags featureDetectionFlags;
  public List<ToolDefinition> toolDefinitions;
  public List<ChatRequestNode> nodes;
  public String agent_memories;
  public String mode;
  public String userGuidelines;
  public String workspaceGuidelines;

// TODO: these are fields used in VSCode, but not yet in Intellij
//    vcs_change: VCSChangePayload;
//    recency_info_recent_changes?: ReplacementText[];
//    disable_auto_external_sources?: boolean;
}

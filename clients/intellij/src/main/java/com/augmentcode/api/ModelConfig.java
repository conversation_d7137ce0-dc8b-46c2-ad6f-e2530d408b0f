package com.augmentcode.api;

import com.google.gson.annotations.SerializedName;
import com.intellij.openapi.util.text.Strings;
import org.jetbrains.annotations.Nullable;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class ModelConfig {
  @SerializedName("default_model")
  public String defaultModelName;
  @SerializedName("models")
  public List<Model> availableModels;
  @SerializedName("languages")
  public List<Language> supportedLanguages;
  public FeatureFlags featureFlags = new FeatureFlags();
  @SerializedName("user_tier")
  public UserTier userTier = UserTier.UNKNOWN;

  private Set<String> allSupportedFileExtensions;
  private Map<String, Model> modelLookup;
  private Map<String, String> extensionToLanguageLookup;

  @Nullable
  public Model findDefaultModel() {
    return findModel(defaultModelName);
  }

  @Nullable
  public Model findModel(@Nullable String modelName) {
    if (modelName == null) {
      if (defaultModelName == null) {
        return null;
      }
      modelName = defaultModelName;
    }
    if (modelLookup == null) {
      modelLookup = new ConcurrentHashMap<>();
      for (Model model : availableModels) {
        modelLookup.put(model.name, model);
      }
    }
    return modelLookup.get(modelName);
  }

  @Nullable
  public String findLanguage(@Nullable String extension) {
    if (extension == null) {
      return null;
    }
    if (extensionToLanguageLookup == null) {
      extensionToLanguageLookup = new ConcurrentHashMap<>();
      for (Language language : supportedLanguages) {
        for (String ext : language.extensions) {
          extensionToLanguageLookup.put(Strings.trimStart(ext, "."), language.name);
        }
      }
    }
    return extensionToLanguageLookup.get(extension);
  }

  public boolean isSupportedFileExtension(@Nullable String extension) {
    if (featureFlags.bypassLanguageFilter) {
      return true;
    }
    if (extension == null) {
      return false;
    }
    if (allSupportedFileExtensions == null) {
      HashSet<String> extensions = new HashSet<>();
      for (Language language : supportedLanguages) {
        for (String ext : language.extensions) {
          // normalize to VFS file extension format
          extensions.add(Strings.trimStart(ext.toLowerCase(), "."));
        }
      }
      allSupportedFileExtensions = extensions;
    }
    return allSupportedFileExtensions.contains(extension);
  }
}

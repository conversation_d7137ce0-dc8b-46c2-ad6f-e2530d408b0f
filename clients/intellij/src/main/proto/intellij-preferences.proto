syntax = "proto3";

package com.augmentcode.rpc;

// This package contains proto version of preferences WebView messages defined in webview-messages.ts.

// Add these options to generate Java classes
option java_multiple_files = true;
option java_package = "com.augmentcode.rpc";
option java_outer_classname = "PreferencesTypes";

import "google/protobuf/empty.proto";
import "google/protobuf/descriptor.proto";
import "google/protobuf/struct.proto";

import "augment.proto";
import "intellij-chat.proto";

/*
 * This service defines an abstraction of an interface that the preferences web view can use.
 * Web views send messages via JS objects which we parse as JSONs into the proto buffers.
 * Until web views are not using HTTP as transport protocol, routing of the JS messages
 * into the service calls below is managed by AugmentMessagingServiceImpl.
 */
service WebviewPreferencesService {
  rpc PreferenceNotify(PreferenceNotifyRequest) returns (google.protobuf.Empty);
  rpc PreferencesLoaded(PreferencesLoadedRequest) returns (PreferencesInitializeResponse) {}
}

message PreferenceNotifyRequest {
  string message = 1;
}

message PreferencesLoadedRequest {
  option (webview_message_type) = "preference-panel-loaded";
}

message PreferencesInitializeResponse {
  option (webview_message_type) = "preference-init";
  PreferenceInput data = 1;
}

message PreferenceInput {
  string type = 1; // e.g., "Chat"
  PreferencePair data = 2;
  bool enable_retrieval_data_collection = 3;
}

message PreferencePair {
  AugmentChatEntry a = 1;
  AugmentChatEntry b = 2;
}

message AugmentChatEntry {
  string message = 1;
  string response = 2;
  // TODO: Add occuredAt
}

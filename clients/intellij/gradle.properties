# IntelliJ Platform Artifacts Repositories -> https://plugins.jetbrains.com/docs/intellij/intellij-artifacts.html

pluginGroup = intellij
pluginName = Augment
pluginRepositoryUrl = https://github.com/augmentcode/intellij-augment

# IntelliJ Platform Properties -> https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html#configuration-intellij-extension
platformType = IC

# Keep the two below in sync so verifyPlugin task catches regressions
# Use fully qualified version for platformVersion so sources are getting downloaded too
platformVersion = 2024.3.2
pluginSinceBuild = 243

platformVersionLatest = 2025.1

# Plugin Dependencies -> https://plugins.jetbrains.com/docs/intellij/plugin-dependencies.html
# Example: platformPlugins = com.intellij.java, com.jetbrains.php:203.4449.22
platformPlugins =

# Gradle Releases -> https://github.com/gradle/gradle/releases
gradleVersion = 8.6

# Opt-out flag for bundling Kotlin standard library -> https://jb.gg/intellij-platform-kotlin-stdlib
kotlin.stdlib.default.dependency = false

# Enable Gradle Configuration Cache -> https://docs.gradle.org/current/userguide/configuration_cache.html
org.gradle.configuration-cache = true

# Enable Gradle Build Cache -> https://docs.gradle.org/current/userguide/build_cache.html
org.gradle.caching = true

# Give Kotlin's daemon 2g of memory
kotlin.daemon.jvmargs=-Xmx2g

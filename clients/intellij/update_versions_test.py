"""Unit tests for update_versions script used for intellij publishing."""

import unittest
import urllib.error
from unittest.mock import MagicMock, patch

from update_versions import (
    Semver,
    get_new_version,
    get_stable_release_ref,
    parse_jetbrains_marketplace_data,
)


class TestSemver(unittest.TestCase):
    """Semver parsing."""

    def test_is_semver_string(self):
        self.assertEqual(Semver.is_semver_string("0.0.0"), True)
        self.assertEqual(Semver.is_semver_string("1.12.123"), True)
        self.assertEqual(Semver.is_semver_string("Nope"), False)
        self.assertEqual(Semver.is_semver_string(""), False)

    def test_from_string(self):
        self.assertEqual(Semver.from_string("0.0.0"), Semver(0, 0, 0))
        self.assertEqual(Semver.from_string("1.12.123"), Semver(1, 12, 123))

    def test_from_string_error(self):
        with self.assertRaises(SystemError) as context:
            Semver.from_string("nope")

        self.assertTrue("Failed to parse version" in str(context.exception))

    def test_lt(self):
        self.assertTrue(Semver(0, 0, 0) < Semver(0, 0, 1))
        self.assertTrue(Semver(0, 0, 0) < Semver(0, 1, 0))
        self.assertTrue(Semver(0, 0, 0) < Semver(1, 0, 0))
        self.assertTrue(Semver(0, 0, 1) < Semver(0, 0, 2))
        self.assertTrue(Semver(0, 1, 1) < Semver(0, 2, 2))
        self.assertTrue(Semver(1, 1, 1) < Semver(2, 2, 2))
        self.assertTrue(Semver(1, 1, 1) < Semver(2, 0, 0))

    def test_eq(self):
        self.assertTrue(Semver(0, 0, 0) == Semver(0, 0, 0))
        self.assertTrue(Semver(0, 0, 1) == Semver(0, 0, 1))
        self.assertTrue(Semver(0, 1, 0) == Semver(0, 1, 0))
        self.assertTrue(Semver(1, 0, 0) == Semver(1, 0, 0))
        self.assertTrue(Semver(1, 2, 3) == Semver(1, 2, 3))

    def test_increment(self):
        self.assertEqual(Semver(0, 0, 0).increment("patch"), Semver(0, 0, 1))
        self.assertEqual(Semver(0, 0, 0).increment("minor"), Semver(0, 1, 0))
        self.assertEqual(Semver(0, 0, 0).increment("major"), Semver(1, 0, 0))

    def test_increment_err(self):
        with self.assertRaises(SystemError) as context:
            Semver(0, 0, 0).increment("other")

        self.assertTrue("Unknown update type" in str(context.exception))


class TestParseJetbrainsMarketplaceData(unittest.TestCase):
    """Parse JetBrains Marketplace data."""

    def test_no_versions(self):
        versions = parse_jetbrains_marketplace_data("""<?xml version="1.0" encoding="UTF-8"?>
<plugin-repository/>
""")
        self.assertEqual(versions, [])

    def test_with_versions(self):
        versions = parse_jetbrains_marketplace_data("""<?xml version="1.0" encoding="UTF-8"?>
<plugin-repository>
  <ff>"Completion"</ff>
  <category name="Completion">
    <idea-plugin downloads="15" size="1275925" date="1712612625000" updatedDate="1712612625000" url="">
      <name>Augment</name>
      <id>com.augmentcode</id>
      <description><![CDATA[<p>Augment yourself with the best AI pair programmer</p>]]></description>
      <version>0.0.3</version>
      <vendor email="" url="">Augment Computing</vendor>
      <rating>00</rating>
      <change-notes><![CDATA[<h3>Added</h3>]]></change-notes>
      <download-url>https://plugins.jetbrains.com/plugin/download?updateId=519419</download-url>
      <idea-version min="n/a" max="n/a" since-build="241.0" until-build="241.*"/>
    </idea-plugin>
    <idea-plugin downloads="15" size="1239610" date="1712180135000" updatedDate="1712180135000" url="">
      <name>Augment</name>
      <id>com.augmentcode</id>
      <description><![CDATA[<p>Augment yourself with the best AI pair programmer</p>]]></description>
      <version>0.0.2</version>
      <vendor email="" url="">Augment Computing</vendor>
      <rating>00</rating>
      <change-notes><![CDATA[<h3>Added</h3>]]></change-notes>
      <download-url>https://plugins.jetbrains.com/plugin/download?updateId=516290</download-url>
      <idea-version min="n/a" max="n/a" since-build="241.0" until-build="241.*"/>
    </idea-plugin>
    <idea-plugin downloads="15" size="1312975" date="1712872189000" updatedDate="1712872189000" url="">
      <name>Augment</name>
      <id>com.augmentcode</id>
      <description><![CDATA[<p>Augment yourself with the best AI pair programmer</p>]]></description>
      <version>0.0.4</version>
      <vendor email="" url="">Augment Computing</vendor>
      <rating>00</rating>
      <change-notes><![CDATA[<h3>Added</h3>]]></change-notes>
      <download-url>https://plugins.jetbrains.com/plugin/download?updateId=521304</download-url>
      <idea-version min="n/a" max="n/a" since-build="241.0" until-build="241.*"/>
    </idea-plugin>
  </category>
</plugin-repository>
""")
        self.assertEqual(versions, [Semver(0, 0, 4), Semver(0, 0, 3), Semver(0, 0, 2)])


def generate_xml_body(version_list):
    pieces = []
    for version in version_list:
        pieces.append(f"<idea-plugin><version>{version}</version></idea-plugin>")
    return f"<?xml version=\"1.0\" encoding=\"UTF-8\"?><plugin-repository><category>{''.join(pieces)}</category></plugin-repository>"


def generate_mock_urlopen(version_list):
    response_mock = MagicMock()
    response_mock.getcode.return_value = 200
    response_mock.read.return_value = generate_xml_body(version_list)
    response_mock.__enter__.return_value = response_mock
    return response_mock


class TestGetNewVersion(unittest.TestCase):
    """Core of the script that returns the updated version for a channel."""

    def test_no_release_channel(self):
        with self.assertRaises(SystemError) as context:
            get_new_version()
        self.assertTrue("RELEASE_CHANNEL is not defined" in str(context.exception))

    def test_bad_release_channel(self):
        with self.assertRaises(SystemError) as context:
            get_new_version(channel="other")
        self.assertTrue(
            "Release channel must be stable or beta" in str(context.exception)
        )

    def test_urllib_error(self):
        with patch(
            "urllib.request.urlopen",
            side_effect=urllib.error.URLError("Injected error"),
        ):
            with self.assertRaises(SystemError) as context:
                get_new_version(channel="beta")
            self.assertTrue(
                "Request to lookup plugin data failed" in str(context.exception)
            )

    def test_return_version_override(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = generate_mock_urlopen(["0.0.0"])
            self.assertEqual(
                get_new_version(
                    version_override="0.1.0",
                    channel="beta",
                ),
                Semver(0, 1, 0),
            )

            self.assertEqual(
                get_new_version(
                    version_override="0.1.0",
                    channel="stable",
                ),
                Semver(0, 1, 0),
            )

    def test_no_versions(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = generate_mock_urlopen([])
            with self.assertRaises(SystemError) as context:
                get_new_version(
                    channel="beta",
                )
            self.assertTrue(
                "No versions found from Jetbrains Marketplace" in str(context.exception)
            )

    def test_new_beta(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = generate_mock_urlopen(["0.0.1", "0.1.0"])
            self.assertEqual(
                get_new_version(
                    channel="beta",
                ),
                Semver(0, 2, 0),
            )

    def test_beta_to_stable(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = generate_mock_urlopen(["0.0.1", "0.0.2"])
            self.assertEqual(
                get_new_version(
                    channel="stable",
                    stable_release_ref="intellij@0.1.0-beta",
                ),
                Semver(0, 1, 0),
            )

    def test_stable_patch_release(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = generate_mock_urlopen(["0.1.0", "0.0.1"])
            self.assertEqual(
                get_new_version(
                    channel="stable",
                    stable_release_ref="commitshaabcdef1234",
                ),
                Semver(0, 1, 1),
            )


class TestGetStableReleaseRef(unittest.TestCase):
    """Get the stable release ref."""

    def test_not_stable_release(self):
        with self.assertRaises(SystemError) as context:
            get_stable_release_ref(
                channel="beta",
                stable_release_ref="commitshaabcdef1234",
            )
        self.assertTrue("release channel must be stable" in str(context.exception))

    def test_invalid_stable_release_ref(self):
        self.assertEqual(
            get_stable_release_ref(
                channel="stable",
                stable_release_ref="commitshaabcdef1234",
            ),
            "commitshaabcdef1234",
        )

    def test_return_stable_release_ref(self):
        self.assertEqual(
            get_stable_release_ref(
                channel="stable",
                stable_release_ref="0.1.1",
            ),
            "intellij@0.1.1-beta",
        )

    def test_not_enough_beta_versions(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = generate_mock_urlopen([])
            with self.assertRaises(SystemError) as context:
                get_stable_release_ref(
                    channel="stable",
                )
            self.assertTrue(
                "Failed to find a suitable beta release to promote to stable"
                in str(context.exception)
            )

    def test_return_beta_versions(self):
        with patch("urllib.request.urlopen") as urlopen:
            urlopen.return_value = generate_mock_urlopen(["0.3.0", "0.2.0", "0.0.1"])
            self.assertEqual(
                get_stable_release_ref(
                    channel="stable",
                ),
                "intellij@0.3.0-beta",
            )


if __name__ == "__main__":
    unittest.main()

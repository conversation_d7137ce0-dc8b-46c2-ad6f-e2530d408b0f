# NOTE(jeff): This file is only used for the research environment, in conjunction
# with pyproject.toml. services should be handled by bazel. Specifically, we use
# this file to ensure that research can only pull in pb2 files from services, and
# no other Python dependencies.

# First exclude all files from services
exclude services/**/*

# Then specifically include only pb2 files
include services/**/*_pb2.py
include services/**/*_pb2.pyi

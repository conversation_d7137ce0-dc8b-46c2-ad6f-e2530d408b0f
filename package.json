{"private": true, "peerDependencies": {"braces": "^3.0.3"}, "pnpm": {"onlyBuiltDependencies": ["@radix-ui/colors"], "packageExtensions": {"@typescript-eslint/eslint-plugin": {"peerDependencies": {"eslint": "*"}}, "remix-auth-oauth2": {"dependencies": {"uuid": "*", "@remix-run/server-runtime": "*"}}, "postcss-loader": {"peerDependencies": {"postcss-flexbugs-fixes": "*", "postcss-preset-env": "*", "postcss-normalize": "*"}}}, "overrides": {"undici@5.28.4": "^5.28.5", "esbuild@0.23.0": "^0.25.0", "esbuild@0.21.5": "^0.25.0", "tar-fs@2.1.1": "^2.1.2"}, "patchedDependencies": {"@radix-ui/colors@3.0.0": "third_party/patches/@<EMAIL>"}}, "engines": {"npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}, "dependencies": {"@bufbuild/protobuf": "1.10.0", "prettier": "^3.3.3"}, "devDependencies": {"@bazel/ibazel": "0.25.0", "@bufbuild/protoc-gen-es": "1.10.0", "@connectrpc/protoc-gen-connect-es": "1.4.0"}}
load("@aspect_bazel_lib//lib:copy_to_directory.bzl", "copy_to_directory")
load("@buildifier_prebuilt//:rules.bzl", "buildifier")
load("@gazelle//:def.bzl", "gazelle")
load("@io_bazel_rules_go//go:def.bzl", "nogo")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("//tools/bzl:trivy.bzl", "trivy_test")

package(default_visibility = ["//visibility:public"])

alias(
    name = "format",
    actual = "//tools:format",
)

npm_link_all_packages(
    name = "node_modules",
)

buildifier(
    name = "buildifier",
)

filegroup(
    name = "trivy_allow_list",
    srcs = [
        ".trivy_allow_list.yaml",
    ],
    visibility = ["//visibility:public"],
)

trivy_test(
    name = "Cargo.trivy_test",
    src = "Cargo.lock",
    allow_list = [
    ],
    mode = "fs",
    tags = [
        "lint",
        "no-cache",
        "postmerge-test",
    ],
)

trivy_test(
    name = "pnpm-lock.trivy_test",
    src = "pnpm-lock.yaml",
    allow_list = [
    ],
    mode = "fs",
    tags = [
        "lint",
        "no-cache",
        "postmerge-test",
    ],
)

filegroup(
    name = "clang_tidy_config",
    srcs = [
        ".clang-tidy",
    ],
)

filegroup(
    name = "shellcheck_config",
    srcs = [
        ".shellcheckrc",
    ],
)

filegroup(
    name = "ruff_config",
    srcs = [
        ".ruff.toml",
    ],
)

filegroup(
    name = "buf_config",
    srcs = [
        ".buf.yaml",
    ],
)

filegroup(
    name = "bandit_config",
    srcs = [
        "bandit.yaml",
    ],
)

# gazelle:prefix github.com/augmentcode/augment/
gazelle(name = "gazelle")

# Enables bazel run //:go
alias(
    name = "go",
    actual = "@io_bazel_rules_go//go",
)

nogo(
    name = "nogo",
    vet = True,
    visibility = ["//visibility:public"],
)

# How to test the go.trivy_test - find some HIGH vulnerabilities
# in the github vulnerability database and add them to
# go.mod. See https://github.com/advisories?page=1&query=ecosystem%3Ago
# Run the test and see that it detects them.
trivy_test(
    name = "go.trivy_test",
    src = "go.mod",
    allow_list = [
    ],
    mode = "fs",
    tags = [
        "lint",
        "no-cache",
        "postmerge-test",
    ],
)

# Copy generated third-party typescript protos to corresponding package paths.
copy_to_directory(
    name = "googleapis_status_ts_proto",
    srcs = ["//third_party/proto:googleapis_status_ts_proto"],
    out = "google/rpc",
    root_paths = ["third_party/proto"],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "bazelversion",
    srcs = [".bazelversion"],
    visibility = ["//visibility:public"],
)

exports_files([
    "stripe_webhook_test_config.json",
    "test-cert.pem",
    "test-key.pem",
    "test-signing-secret",
])

alias(
    name = "augi",
    actual = "//infra:augi",
)

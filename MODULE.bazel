# Bazel Module File
#
# bzlmod User Guide: https://bazel.build/external/overview#bzlmod
# Registry: https://registry.bazel.build/

bazel_dep(name = "rules_python", version = "0.32.2")

python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    configure_coverage_tool = True,
    # pre-commit runs as root, so we need to ignore the warning to not run as root
    ignore_root_user_error = True,
    python_version = "3.11",
)

pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")
pip.parse(
    timeout = 1200,
    extra_pip_args = [
        "--only-binary",
        ":all",
        "--extra-index-url",
        "https://us-central1-python.pkg.dev/system-services-dev/pypi-public/simple",
    ],
    hub_name = "python_pip",
    python_version = "3.11",
    requirements_lock = "//tools/python_deps:requirements.lock",
)
use_repo(pip, "python_pip")

bazel_dep(name = "aspect_rules_py", version = "1.0.0")
bazel_dep(name = "rules_cc", version = "0.0.9")
bazel_dep(name = "rules_foreign_cc", version = "0.10.1")
bazel_dep(name = "aspect_bazel_lib", version = "2.13.0")
bazel_dep(name = "rules_pkg", version = "0.10.1")
bazel_dep(name = "aspect_rules_js", version = "2.1.3")
bazel_dep(name = "aspect_rules_ts", version = "3.5.0")
bazel_dep(name = "bazel_skylib", version = "1.7.0")
bazel_dep(name = "aspect_rules_esbuild", version = "0.21.0")
bazel_dep(name = "aspect_rules_jest", version = "0.22.0")
bazel_dep(name = "rules_nodejs", version = "6.3.3")

npm = use_extension("@aspect_rules_js//npm:extensions.bzl", "npm", dev_dependency = True)
npm.npm_translate_lock(
    name = "npm",
    bins = {
        # derived from "bin" attribute in node_modules/react-scripts/package.json
        "react-scripts": {
            "react-scripts": "./bin/react-scripts.js",
        },
        "react-app-rewired": {
            "react-app-rewired": "./bin/react-app-rewired.js",
        },
    },
    npmrc = "//:.npmrc",
    pnpm_lock = "//:pnpm-lock.yaml",
    verify_node_modules_ignored = "//:.bazelignore",
)
use_repo(npm, "npm")

################################################################################
# rules_ts
################################################################################

rules_ts_ext = use_extension(
    "@aspect_rules_ts//ts:extensions.bzl",
    "ext",
    dev_dependency = True,
)
rules_ts_ext.deps()
use_repo(rules_ts_ext, "npm_typescript")

rules_nodejs_ext = use_extension(
    "@rules_nodejs//nodejs:extensions.bzl",
    "node",
    dev_dependency = True,
)
rules_nodejs_ext.toolchain(
    node_version = "20.13.1",
)

bazel_dep(name = "rules_oci", version = "2.2.0")
bazel_dep(name = "buildifier_prebuilt", version = "6.4.0")
bazel_dep(name = "bazel_features", version = "1.18.0")
bazel_dep(name = "buildozer", version = "7.1.2")
bazel_dep(name = "rules_go", version = "0.51.0", repo_name = "io_bazel_rules_go")
bazel_dep(name = "gazelle", version = "0.40.0", repo_name = "gazelle")
bazel_dep(name = "jsonnet_go", version = "0.20.0", repo_name = "google_jsonnet_go")

go_sdk = use_extension("@io_bazel_rules_go//go:extensions.bzl", "go_sdk")

# NOTE: please keep the version here in sync with research/environments/containers/Dockerfile.augment_base
go_sdk.download(
    version = "1.24.3",
)
go_sdk.nogo(
    excludes = ["@//generated:__subpackages__"],  # Labels to exclude.
    includes = ["@//:__subpackages__"],  # Labels to lint. By default only lints code in workspace.
    nogo = "//:nogo",
)
use_repo(go_sdk, "go_toolchains")

register_toolchains("@go_toolchains//:all")

go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")
go_deps.gazelle_override(
    build_file_generation = "on",
    path = "github.com/cncf/xds/go",
)
go_deps.module_override(
    patch_strip = 1,
    patches = [
        "//third_party:patches/gcr.patch",
    ],
    path = "github.com/GoogleCloudPlatform/docker-credential-gcr/v2",
)
go_deps.module_override(
    patch_strip = 1,
    patches = [
        "//third_party:patches/cloudflare-circl-asm-headers.patch",
    ],
    path = "github.com/cloudflare/circl",
)

# list of go external repos
# usually the go package name with "-" and "." replaced with "_" and the order of the domain reversed
# e.g. github.com/bitnami-labs/sealed-secrets => com_github_bitnami_labs_sealed_secrets
# usually managed by gazelle and buildozer, but can be changed manually if needed
use_repo(
    go_deps,
    "com_github_alitto_pond_v2",
    "com_github_allegro_bigcache_v3",
    "com_github_benbjohnson_clock",
    "com_github_bitnami_labs_sealed_secrets",
    "com_github_bradleyfalzon_ghinstallation_v2",
    "com_github_customerio_go_customerio_v3",
    "com_github_denormal_go_gitignore",
    "com_github_dustin_go_humanize",
    "com_github_ewhauser_bazel_differ",
    "com_github_firecracker_microvm_firecracker_go_sdk",
    "com_github_fsouza_fake_gcs_server",
    "com_github_fullstorydev_grpcurl",
    "com_github_go_git_go_git_v5",
    "com_github_goccy_go_yaml",
    "com_github_golang_jwt_jwt_v4",
    "com_github_golang_jwt_jwt_v5",
    "com_github_golang_mock",
    "com_github_golang_protobuf",
    "com_github_google_gnostic_models",
    "com_github_google_go_cmp",
    "com_github_google_go_containerregistry",
    "com_github_google_go_github_v64",
    "com_github_google_go_github_v69",
    "com_github_google_uuid",
    "com_github_googleapis_gax_go_v2",
    "com_github_googlecloudplatform_docker_credential_gcr_v2",
    "com_github_googlecloudplatform_opentelemetry_operations_go_exporter_metric",
    "com_github_gorilla_mux",
    "com_github_gorilla_sessions",
    "com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus",
    "com_github_grpc_ecosystem_grpc_health_probe",
    "com_github_hashicorp_go_retryablehttp",
    "com_github_hashicorp_golang_lru_v2",
    "com_github_jarcoal_httpmock",
    "com_github_jhump_protoreflect",
    "com_github_kubernetes_csi_external_snapshotter_client_v8",
    "com_github_launchdarkly_go_sdk_common_v3",
    "com_github_launchdarkly_go_server_sdk_v7",
    "com_github_lithammer_fuzzysearch",
    "com_github_markbates_goth",
    "com_github_mattn_go_isatty",
    "com_github_mattn_go_shellwords",
    "com_github_micahparks_jwkset",
    "com_github_micahparks_keyfunc_v3",
    "com_github_montanaflynn_stats",
    "com_github_opencontainers_go_digest",
    "com_github_orbcorp_orb_go",
    "com_github_pkg_errors",
    "com_github_pmezard_go_difflib",
    "com_github_prometheus_client_golang",
    "com_github_prometheus_client_model",
    "com_github_redis_go_redis_v9",
    "com_github_regclient_regclient",
    "com_github_rs_zerolog",
    "com_github_slack_go_slack",
    "com_github_spf13_cobra",
    "com_github_spf13_pflag",
    "com_github_stretchr_testify",
    "com_github_stripe_stripe_go_v80",
    "com_github_thediveo_go_asciitree",
    "com_github_webdevops_pagerduty_exporter",
    "com_github_yannh_kubeconform",
    "com_google_cloud_go",
    "com_google_cloud_go_artifactregistry",
    "com_google_cloud_go_bigquery",
    "com_google_cloud_go_bigtable",
    "com_google_cloud_go_kms",
    "com_google_cloud_go_logging",
    "com_google_cloud_go_monitoring",
    "com_google_cloud_go_pubsub",
    "com_google_cloud_go_secretmanager",
    "com_google_cloud_go_spanner",
    "com_google_cloud_go_storage",
    "in_gopkg_yaml_v3",
    "io_k8s_api",
    "io_k8s_apimachinery",
    "io_k8s_client_go",
    "io_k8s_component_helpers",
    "io_k8s_sigs_json",
    "io_opentelemetry_go_contrib_instrumentation_github_com_gorilla_mux_otelmux",
    "io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc",
    "io_opentelemetry_go_otel",
    "io_opentelemetry_go_otel_exporters_otlp_otlptrace_otlptracegrpc",
    "io_opentelemetry_go_otel_exporters_stdout_stdoutmetric",
    "io_opentelemetry_go_otel_metric",
    "io_opentelemetry_go_otel_sdk",
    "io_opentelemetry_go_otel_sdk_metric",
    "org_golang_google_api",
    "org_golang_google_genproto",
    "org_golang_google_genproto_googleapis_api",
    "org_golang_google_genproto_googleapis_rpc",
    "org_golang_google_grpc",
    "org_golang_google_protobuf",
    "org_golang_x_crypto",
    "org_golang_x_oauth2",
    "org_golang_x_sync",
    "org_uber_go_automaxprocs",
)

bazel_dep(name = "rules_rust", version = "0.49.3")

rust = use_extension(
    "@rules_rust//rust:extensions.bzl",
    "rust",
)
rust.toolchain(
    edition = "2021",
    versions = ["1.78.0"],
)
use_repo(
    rust,
    "rust_toolchains",
)

register_toolchains("@rust_toolchains//:all")

crate = use_extension(
    "@rules_rust//crate_universe:extension.bzl",
    "crate",
)

# We are building tokio with cfg option (not feature!) tokio_taskdump, but rules_rust doesn't
# support including dependencies based on cfg options, so we manually add the dependency here.
crate.annotation(
    crate = "tokio",
    deps = ["@crates__backtrace-0.3.74//:backtrace"],
)
crate.from_cargo(
    name = "crates",
    cargo_config = "//:.cargo/config.toml",
    cargo_lockfile = "//:Cargo.lock",
    manifests = [
        "//:Cargo.toml",
        "//base/rust/tracing-tonic:Cargo.toml",
        "//base/rust/numpy:Cargo.toml",
        "//base/cloud/iap:Cargo.toml",
        "//base/logging:Cargo.toml",
        "//base/logging/audit:Cargo.toml",
        "//base/blob_names:Cargo.toml",
        "//base/blob_names/rust:Cargo.toml",
        "//base/feature_flags:Cargo.toml",
        "//base/metrics_server/rust:Cargo.toml",
        "//services/api_proxy/server:Cargo.toml",
        "//services/auth/central/server:Cargo.toml",
        "//services/auth/query/client:Cargo.toml",
        "//services/auth/query/server:Cargo.toml",
        "//services/bigtable_proxy/client:Cargo.toml",
        "//services/content_manager/server:Cargo.toml",
        "//services/content_manager/client:Cargo.toml",
        "//services/content_manager:Cargo.toml",
        "//services/checkpoint_indexer:Cargo.toml",
        "//services/checkpoint_indexer/server:Cargo.toml",
        "//services/examples:Cargo.toml",
        "//services/examples/server/rust:Cargo.toml",
        "//services/embeddings_search_host/cpu_server:Cargo.toml",
        "//services/inference_host/server/continuous_batching:Cargo.toml",
        "//services/integrations/docset/client:Cargo.toml",
        "//services/integrations/github/processor/client:Cargo.toml",
        "//services/lib/grpc/auth:Cargo.toml",
        "//services/lib/grpc/client:Cargo.toml",
        "//services/lib/grpc/metrics:Cargo.toml",
        "//services/lib/grpc/stream_mux:Cargo.toml",
        "//services/lib/grpc/testing:Cargo.toml",
        "//services/lib/grpc/tls_config:Cargo.toml",
        "//services/lib/grpc/service:Cargo.toml",
        "//services/lib/request_context:Cargo.toml",
        "//services/memstore/client:Cargo.toml",
        "//services/ping_pong:Cargo.toml",
        "//services/ping_pong/client:Cargo.toml",
        "//services/ping_pong/server:Cargo.toml",
        "//services/remote_agents/client:Cargo.toml",
        "//services/request_insight/publisher:Cargo.toml",
        "//services/share/client:Cargo.toml",
        "//services/tenant_watcher/client:Cargo.toml",
        "//services/token_exchange/client:Cargo.toml",
        "//services/working_set/client:Cargo.toml",
        "//tools/genie/backend:Cargo.toml",
        "//third_party/bigtable_rs:Cargo.toml",
        "//third_party/tiktoken:Cargo.toml",
        "//third_party/tracing-actix-web:Cargo.toml",
        "//third_party/tracing-stackdriver:Cargo.toml",
        "//third_party/scann_rs:Cargo.toml",
    ],
)
use_repo(crate, "crates")

bazel_dep(name = "rules_jsonnet", version = "0.6.0")
bazel_dep(name = "rules_proto_grpc", version = "5.0.0-alpha2")
bazel_dep(name = "rules_proto_grpc_go", version = "5.0.0-alpha2")
bazel_dep(name = "rules_proto_grpc_python", version = "5.0.0-alpha2")
bazel_dep(name = "rules_proto_grpc_cpp", version = "5.0.0-alpha2")

# protobuf version need to be handled with extreme care as the python pip version
# and the generation versions need to match.
# the single_version_override forces the version.
# Updated from 23.1 (4.23.x) to 25.6 (4.25.6) to support mypy-protobuf 3.6.0
bazel_dep(name = "protobuf", version = "25.6")
bazel_dep(name = "toolchains_llvm", version = "1.2.0")
bazel_dep(name = "abseil-cpp", version = "20230802.0.bcr.1")
single_version_override(
    module_name = "abseil-cpp",
    version = "20230802.0.bcr.1",
)

bazel_dep(name = "fmt", version = "11.0.2")
bazel_dep(name = "eigen", version = "4.0.0-20241125")
archive_override(
    module_name = "eigen",
    integrity = "sha256-F9GSdDfmzQoZ899jR1Fto11iR8AKq3UOz5Gyci1amFY=",
    # this is a mirror of https://github.com/bazelbuild/bazel-central-registry/blob/main/modules/eigen/4.0.0-20241125/source.json
    # with the two patches applied. The reason is a hash instability when downloading from gitlab
    # see https://github.com/bazelbuild/bazel-central-registry/issues/4633
    urls = ["https://storage.googleapis.com/augment-bazel-data/public/eigen-4.0.0-20241125.tar.gz"],
)

bazel_dep(name = "googletest", version = "1.13.0")
bazel_dep(name = "highway", version = "1.2.0")
bazel_dep(name = "zlib", version = "1.3.1.bcr.4")

llvm = use_extension("@toolchains_llvm//toolchain/extensions:llvm.bzl", "llvm")

LLVM_VERSIONS = {
    "linux-x86_64": "16.0.0",
    "darwin-aarch64": "17.0.6",
}

llvm.toolchain(
    name = "llvm_toolchain",
    compile_flags = {
        "linux-x86_64": [
            "-msse3",
            "-msse4.1",
        ],
    },
    llvm_versions = LLVM_VERSIONS,
    opt_compile_flags = {
        "linux-x86_64": [
            "-DNDEBUG",
            "-O3",
            "-msse3",
            "-msse4.1",
        ],
    },
    stdlib = {
        "linux-x86_64": "stdc++",
    },
)
llvm.sysroot(
    name = "llvm_toolchain",
    label = "@@org_chromium_sysroot_linux_x64//:sysroot",
    targets = ["linux-x86_64"],
)
use_repo(llvm, "llvm_toolchain")

register_toolchains("@llvm_toolchain//:all")

bazel_dep(name = "rules_cuda", version = "0.2.1")

cuda = use_extension("@rules_cuda//cuda:extensions.bzl", "toolchain")
cuda.local_toolchain(
    name = "local_cuda",
    toolkit_path = "",
)
use_repo(cuda, "local_cuda")

bazel_dep(name = "googleapis", version = "0.0.0-20240819-fe8ba054a")

switched_rules = use_extension("@googleapis//:extensions.bzl", "switched_rules")
switched_rules.use_languages(
    cc = False,
    go = True,
    grpc = True,
    python = True,
)
use_repo(switched_rules, "com_google_googleapis_imports")

bazel_dep(name = "grpc", version = "1.56.3.bcr.1")  # version set to match rules_proto_grpc

# force and set the version. Do not allow other dependencies to move to a newer version
single_version_override(
    module_name = "grpc",
    version = "1.56.3.bcr.1",
)

bazel_dep(name = "aspect_rules_lint", version = "1.1.0")
git_override(
    module_name = "aspect_rules_lint",
    commit = "2e2cbc39c80bca8ed36a2c94d8740367560baaa2",
    patch_strip = 1,
    patches = ["@//third_party:patches/rules_lint.patch"],
    remote = "https://github.com/aspect-build/rules_lint.git",
)

bazel_dep(name = "rules_buf", version = "0.3.0")

buf = use_extension("@rules_buf//buf:extensions.bzl", "buf")
use_repo(buf, "rules_buf_toolchains")

bazel_dep(name = "cxx.rs", version = "1.0.128")

# version 1.0.128 contains a bug that rules_cc is missing. Newer version do not work with our
# version of rules_rust
archive_override(
    module_name = "cxx.rs",
    integrity = "sha256-x/X1yRRYBtKVnJ8AAMYKLmZc/lEIm+gSY5p1CTiWZyM=",
    patch_strip = 1,
    patches = ["@//third_party:patches/cxx.rs.patch"],
    strip_prefix = "cxx-55bfb5444cd43bb594022a15c5ac61938edb380a",
    urls = ["https://github.com/dtolnay/cxx/archive/55bfb5444cd43bb594022a15c5ac61938edb380a.tar.gz"],
)

uv = use_extension("//tools/bzl/uv:uv.bzl", "uv_extension")
use_repo(uv, "uv")

linux = use_extension("//clients/beachhead/img/kernel:linux.bzl", "linux_extension")
use_repo(linux, "linux-lts-source")

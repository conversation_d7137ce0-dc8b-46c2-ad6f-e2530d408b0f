"""Fastforward implementation for LLAMA architectures."""

import logging
import re
from typing import Sequence

import torch
import torch.nn as nn

from base.fastforward import (
    all_reduce,
    cached_attention,
    cuda_graphs_attention,
    fwd,
    fwd_torch,
    positional_embeddings,
)
from base.fastforward.cached_attention import (
    Attention,
    AttentionImpl,
    MultiCacheAttention,
    MultiCacheAttentionImplementation,
    SplitHeadModes,
)
from base.fastforward.checkpoints import save_load
from base.fastforward.checkpoints.impl import sharding
from base.fastforward.layers import (
    <PERSON><PERSON>,
    LlamaTransformerBlock,
    RmsNorm,
    WordEmbeddings,
)
from base.fastforward.llama import model_specs
from base.fastforward.parallel import ParallelConfig, ParallelContext
from base.fastforward.parallel_fwd import ParallelForwardRunner

Tensor = torch.Tensor

LLAMA_350M_PROMPT_TOKS = (21, 12000, 5788, 3460, 328, 4865, 203, 589, 2575, 2262, 203)
# Prompt for testing the llama-350m model. Corresponding to the text below:
"""# Hello world program in Python
def main():
"""

LLAMA_350M_OUTPUT_TOKS = (202, 1216, 440, 8279, 5788, 15981, 203, 203, 325, 1156)
# Expected output by argmax-ing each next token from the prompt above
# If we give our LLAMA-350M the prompt above, plus any prefix from the below
# outputs, the model is supposed to autoregressively argmax-decode the
# remaining.
r"""\tprint("Hello world!")\n\nif __"""


class Llama(nn.Module):
    """Llama model."""

    def __init__(
        self,
        ms: model_specs.LlamaModelSpec,
        dtype: torch.dtype = torch.bfloat16,
        device: Device = "cuda",
        process_idx: int = 0,
        parallel_config: ParallelConfig = ParallelConfig.single_process(),
        auto_capture_graphs: bool = False,
        batch_sizes: Sequence[int] | None = None,
        all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
        output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
    ):
        super().__init__()
        self._dtype = dtype
        self._device = device
        if parallel_config.sp_size > 1:
            raise ValueError("Sequence parallel unsupported for 16-bit Llama.")
        self._parallel_ctx = ParallelContext(parallel_config, process_idx)
        self._auto_capture_graphs = auto_capture_graphs

        self.embs = WordEmbeddings(
            vocab_size=ms.vocab_size,
            emb_dim=ms.emb_dim,
            dtype=dtype,
            device=device,
        )

        ffn_all_reduce_kit: all_reduce.AllReduceKit | None = None
        attn_all_reduce_kit: all_reduce.AllReduceKit | None = None

        if parallel_config.tp_size > 1:
            if all_reduce_impl == all_reduce.AllReduceImpl.FASTFORWARD:
                # max tokens per round
                max_tokens = max(batch_sizes) if batch_sizes is not None else 2048

                ffn_all_reduce_kit = all_reduce.AllReduceKit(
                    ms.emb_dim * max_tokens, dtype, self._parallel_ctx.tp_group
                )
                attn_all_reduce_kit = all_reduce.AllReduceKit(
                    ms.emb_dim * max_tokens, dtype, self._parallel_ctx.tp_group
                )
            elif all_reduce_impl == all_reduce.AllReduceImpl.FASTFORWARD_FP8:
                raise ValueError("FP8 all_reduce requires an fp8 model.")

        self.layers = nn.ModuleList()
        for _ in range(ms.num_layers):
            self.layers.append(
                LlamaTransformerBlock(
                    emb_dim=ms.emb_dim,
                    num_heads_q=ms.num_heads_q,
                    num_heads_kv=ms.num_heads_kv,
                    head_dim=ms.head_dim,
                    split_head_mode=ms.attn_split_head_mode,
                    mlp_dim=ms.mlp_hidden_dim,
                    norm_eps=ms.norm_eps,
                    use_bias=False,
                    qkv_only_bias=ms.qkv_only_bias,
                    dtype=dtype,
                    device=device,
                    parallel_ctx=self._parallel_ctx,
                    attn_all_reduce_kit=attn_all_reduce_kit,
                    ffn_all_reduce_kit=ffn_all_reduce_kit,
                )
            )

        self.final_rms_norm = RmsNorm(
            inp_dim=ms.emb_dim,
            eps=ms.norm_eps,
            dtype=dtype,
            cast_to_fp32=True,
            device=device,
        )
        if output_type == fwd.OutputTensorType.EMBEDDING:
            assert ms.output_projection_dim is not None
            self.output_projection = nn.Linear(
                in_features=ms.emb_dim,
                out_features=ms.output_projection_dim,
                bias=True,
                device=device,
                dtype=dtype,
            )
        else:
            assert output_type == fwd.OutputTensorType.VOCAB_LOGITS
            self.score = nn.Linear(
                in_features=ms.emb_dim,
                out_features=ms.vocab_size,
                bias=False,
                device=device,
                dtype=dtype,
            )
        self._output_type = output_type

        if self._auto_capture_graphs:
            if batch_sizes is None:
                raise ValueError(
                    "batch_sizes must be specified if auto_capture_graphs is True."
                )
            self._apply_transformer_layers = (
                cuda_graphs_attention.GraphedEmbForwardPass(
                    step_fn=self._emb_step,
                    process_idx=process_idx,
                    num_processes=parallel_config.num_processes,
                    batch_sizes=batch_sizes,
                )
            )
        else:
            self._apply_transformer_layers = self._emb_step

    @property
    def dtype(self) -> torch.dtype:
        return self._dtype

    def _emb_step(self, emb: torch.Tensor, attn: Attention) -> torch.Tensor:
        for layer_idx, layer in enumerate(self.layers):
            emb = layer(emb, attn=attn, layer_idx=layer_idx)
        return emb

    @torch.inference_mode()
    def forward(self, tokens: Sequence[int], attn: Attention) -> fwd.ModelOutput:
        tokens_tensor = torch.tensor(tokens, dtype=torch.int32, device=self._device)

        tokens_on_device = tokens_tensor.to(self._device)

        attn.register_tokens_get_positions(
            tokens_on_device, process_idx=self._parallel_ctx.process_idx
        )
        x = self.embs(tokens_on_device)
        x = self._apply_transformer_layers(x, attn)
        x = self.final_rms_norm(x)
        if self._output_type == fwd.OutputTensorType.EMBEDDING:
            x = self.output_projection(x)
            return fwd_torch.TorchEmbedding(x)
        else:
            x = self.score(x)
            return fwd_torch.TorchLogits2D(x)


class LlamaAttentionFactory(fwd.AttentionFactory):
    """Creates attention caches for Llama models."""

    # TODO (hieu): if we switch to *only* using Llama models, we should think
    # about never using AttentionFactory.__call__ again. Then, we should change
    # the logic of Llama models to directly call MultiCacheAttention, whose
    # interface is much richer than Attention.

    def __init__(
        self,
        ms: model_specs.LlamaModelSpec,
        parallel_config: ParallelConfig = ParallelConfig.single_process(),
        dtype: torch.dtype = torch.bfloat16,
        attention_impl: AttentionImpl = AttentionImpl.BATCHED_FLASH,
        pre_attention_kernel_fusion: bool = True,
        capture_attn_maxes_for_quantization: bool = False,
        use_register_tokens_kernel: bool = False,
        small_request_max_seqlen: int | None = None,
        max_requests_in_round: int | None = None,
        max_large_requests_in_round: int = 1,
    ):
        """The AttentionFactory for Llama models.

        Args:
            ms: the specific LlamaModelSpec.
            num_processes: for tensor parallelism.
            dtype: the dtype of the `qkv` tensor that will be passed to Attention
                objects created by this factory. we also use this `dtype` to construct
                this object's KV-caches.
            attention_impl: the implementation of the attention.
            pre_attention_kernel_fusion: whether to fuse the pre-attention kernel.
            use_sequence_parallel: whether to use sequence parallelism.
        """
        self._ms = ms
        self._split_head_mode = (
            SplitHeadModes.NO_SPLIT
            if parallel_config.sp_size > 1
            else ms.attn_split_head_mode
        )
        self._parallel_config = parallel_config
        self._dtype = dtype
        self._attention_impl = attention_impl
        self._pre_attention_kernel_fusion = pre_attention_kernel_fusion
        self._capture_attn_maxes_for_quantization = capture_attn_maxes_for_quantization
        self._use_register_tokens_kernel = use_register_tokens_kernel
        self._small_request_max_seqlen = small_request_max_seqlen
        self._max_requests_in_round = max_requests_in_round
        self._max_large_requests_in_round = max_large_requests_in_round

    def __call__(self, max_length: int) -> cached_attention.BasicAttention:
        """Overriding the legacy interface, to pass in the number of processes."""
        mc_attn = self.create_cache_pool(max_length=max_length, num_attention_caches=1)
        return cached_attention.BasicAttention(
            mc_attn, num_processes=self._parallel_config.num_processes
        )

    def create_cache_pool(
        self,
        max_length: int,
        num_attention_caches: int,
    ) -> MultiCacheAttention:
        if self._ms.rotary_scaling_factor == 1.0:
            extension_config = None
        elif self._ms.rotary_extension_method == "deepseek_v1":
            extension_config = positional_embeddings.DeepSeekV1ExtensionConfig(
                rotary_scaling_factor=self._ms.rotary_scaling_factor,
            )
        elif self._ms.rotary_extension_method == "yarn":
            assert self._ms.unscaled_max_position_embeddings > 0
            extension_config = positional_embeddings.YaRNExtensionConfig(
                rotary_scaling_factor=self._ms.rotary_scaling_factor,
                unscaled_max_position_embeddings=self._ms.unscaled_max_position_embeddings,
                beta_fast=self._ms.beta_fast,
                beta_slow=self._ms.beta_slow,
                mscale=1.0,
            )
        elif self._ms.rotary_extension_method == "llama3_1":
            extension_config = positional_embeddings.Llama31ExtensionConfig(
                rotary_scaling_factor=self._ms.rotary_scaling_factor,
            )
        else:
            extension_config = None
        rotary_config = positional_embeddings.RotaryConfig(
            rotary_ratio=self._ms.rotary_pct,
            rotary_theta=self._ms.rotary_theta,
            max_position_embeddings=self._ms.max_position_embeddings,
            rotary_interleave=self._ms.rotary_interleave,
            ext_config=extension_config,
        )
        mc_attn = MultiCacheAttentionImplementation(
            num_caches=num_attention_caches,
            num_layers=self._ms.num_layers,
            num_heads=self._ms.num_heads,
            queries_per_head=self._ms.num_queries_per_head,
            max_len=max_length,
            dtype=self._dtype,
            head_dim=self._ms.head_dim,
            parallel_config=self._parallel_config,
            split_head_mode=self._split_head_mode,
            attention_impl=self._attention_impl,
            pre_attention_kernel_fusion=self._pre_attention_kernel_fusion,
            rotary_config=rotary_config,
            capture_attn_maxes_for_quantization=self._capture_attn_maxes_for_quantization,
            use_register_tokens_kernel=self._use_register_tokens_kernel,
            max_requests_in_round=self._max_requests_in_round,
            max_large_requests_in_round=self._max_large_requests_in_round,
            small_request_max_seqlen=self._small_request_max_seqlen,
        )
        return mc_attn


########################################################
# Weight sharding functions while loading checkpoints. #
########################################################


def _shard_attn_qkv_weight_and_bias(
    name: str,
    w: torch.Tensor,
    ms: model_specs.LlamaModelSpec,
    tp_rank: int,
    tp_size: int,
) -> torch.Tensor:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    # Note that we handle both weight and bias here, since Qwen has QKV biases.
    if not (name.endswith("qkv.weight") or name.endswith("qkv.bias")):
        return w
    is_bias = name.endswith("qkv.bias")

    if ms.attn_split_head_mode == SplitHeadModes.NO_SPLIT:
        return w

    num_heads_q = ms.num_heads_q
    num_heads_kv = ms.num_heads_kv
    head_dim = ms.head_dim

    if is_bias:
        # This is for typing. `emb_dim` is unused in the bias case.
        emb_dim = 1
    else:
        emb_dim = w.numel() // ((num_heads_q + 2 * num_heads_kv) * head_dim)
    num_queries_per_head = num_heads_q // num_heads_kv

    if is_bias:
        w = w.view(num_heads_kv, (num_queries_per_head + 2) * head_dim)
    else:
        w = w.view(num_heads_kv, (num_queries_per_head + 2) * head_dim, emb_dim)

    if ms.attn_split_head_mode == SplitHeadModes.KV_HEADS:
        # Typically used in MHA or GQA
        # Each device holds (num_heads_kv // num_processes) KV-heads,
        # along with their corresponding Q-heads.
        if is_bias:
            w = w.chunk(tp_size, dim=0)[tp_rank].reshape(-1)
        else:
            w = w.chunk(tp_size, dim=0)[tp_rank].reshape(-1, emb_dim)
    elif ms.attn_split_head_mode == SplitHeadModes.Q_PER_HEADS:
        # Typically used in MQA or GQA with few KV-heads
        # Each device holds all num_heads_kv  KV-heads, along with their
        # corresponding block of Q-heads.
        wq, w_kv = w.split([num_queries_per_head * head_dim, 2 * head_dim], dim=1)
        wq = wq.chunk(tp_size, dim=1)[tp_rank]
        if is_bias:
            w = torch.cat([wq, w_kv], dim=1).reshape(-1)
        else:
            w = torch.cat(
                [wq, w_kv],
                dim=1,
            ).reshape(-1, emb_dim)

    return w


def _shard_attn_out_weight(
    name: str,
    w: torch.Tensor,
    ms: model_specs.LlamaModelSpec,
    tp_rank: int,
    tp_size: int,
):
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")

    if not name.endswith("out.weight"):
        return w

    if ms.attn_split_head_mode == SplitHeadModes.NO_SPLIT:
        return w
    elif ms.attn_split_head_mode == SplitHeadModes.KV_HEADS:
        return w.chunk(tp_size, dim=1)[tp_rank]
    else:
        assert ms.attn_split_head_mode == SplitHeadModes.Q_PER_HEADS
        assert ms.num_heads_q % ms.num_heads_kv == 0
        queries_per_kvhead = ms.num_heads_q // ms.num_heads_kv
        assert queries_per_kvhead % tp_size == 0
        w_kv_groups = w.chunk(ms.num_heads_kv, dim=1)
        return torch.cat(
            tuple(group.chunk(tp_size, dim=1)[tp_rank] for group in w_kv_groups), dim=1
        ).contiguous()


def _shard_ffn_expand_weight_load_args(
    tp_rank: int = 0,
    tp_size: int = 1,
) -> sharding.ShardLoadArgs:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")
    # Logically, the tensor is sharded once into 'act_func' and 'identity',
    # and then again into num_processes shards. Each process should load
    # both act_func and identity and concat them.
    return sharding.ShardLoadArgs(
        shard_count=2 * tp_size,
        shard_idxs=(tp_rank, tp_rank + tp_size),
        split_dim=0,
    )


def _shard_ffn_shrink_weight_load_args(
    tp_rank: int = 0, tp_size: int = 1
) -> sharding.ShardLoadArgs:
    if tp_size == 1:
        raise ValueError(f"It is bug to call this function with {tp_size=}.")
    return sharding.ShardLoadArgs(
        shard_count=tp_size,
        shard_idxs=(tp_rank,),
        split_dim=1,
    )


def shard_args_by_name(
    ms: model_specs.LlamaModelSpec | None = None,
    process_idx: int = 0,
    parallel_config: ParallelConfig = ParallelConfig.single_process(),
) -> sharding.ShardLoadArgsMap:
    if parallel_config.tp_size == 1:
        # No sharding
        return {}

    arg_map = {}
    arg_map[re.compile(r"expand.weight$")] = _shard_ffn_expand_weight_load_args(
        tp_rank=parallel_config.process_idx_to_tp_rank(process_idx),
        tp_size=parallel_config.tp_size,
    )
    arg_map[re.compile(r"shrink.weight$")] = _shard_ffn_shrink_weight_load_args(
        tp_rank=parallel_config.process_idx_to_tp_rank(process_idx),
        tp_size=parallel_config.tp_size,
    )
    return {k: v for k, v in arg_map.items() if v is not None}


@torch.no_grad
def _process_loaded_weight(
    name: str,
    w: torch.Tensor,
    device: torch.device | str = "cpu",
    dtype: torch.dtype | None = None,
    ms: model_specs.LlamaModelSpec | None = None,
    process_idx: int = 0,
    parallel_config: ParallelConfig = ParallelConfig.single_process(),
) -> torch.Tensor:
    """How to "sneak-in" what we want to do with a weight tensor while loading.

    Args:
        name: the weight's name in the model. we can rely on the name to specify what
            we want to do with different tensors.
        w: the tensor loaded from checkpoint storage
            This may be the entire weight tensor, or some shard of the weight tensor, depending
            on whether the weight's name opts into sharded-load. See function shard_args_by_name.
        device: where the tensor will be moved to immediately after loading.
        dtype: the tensor will be converted to this dtype after loading. if `None`,
            the type is kept as it is loaded from files.
        ms: the model spec, which is used as the config to shard certain weights, such
            as the attention weights. if the attention layer is not sharded, one does
            not need to pass `ms` to this function call.
        process_idx: if using multiple GPUs, specify which process is this method in.
        parallel_config: the parallel config for this model.
    """
    if parallel_config.tp_size == 1:
        return w.detach().to(dtype=dtype, device=device)

    # NOTE: the reason these sharding functions exist is because the sharding logic is sensitive to
    # the split_head_mode, which the generic ShardLoadArgs cannot represent. So we always load the
    # full weights for qkv and out in the attn block and then slice out the needed piece here.
    if ms is not None:
        for shard_func in [_shard_attn_qkv_weight_and_bias, _shard_attn_out_weight]:
            w = shard_func(
                name=name,
                w=w,
                ms=ms,
                tp_rank=parallel_config.process_idx_to_tp_rank(process_idx),
                tp_size=parallel_config.tp_size,
            )
    return w.detach().to(dtype=dtype, device=device)


def process_loaded_weights(
    weights: dict[str, torch.Tensor],
    device: torch.device | str = "cpu",
    dtype: torch.dtype | None = None,
    ms: model_specs.LlamaModelSpec | None = None,
    process_idx: int = 0,
    parallel_config: ParallelConfig = ParallelConfig.single_process(),
) -> dict[str, torch.Tensor]:
    """Sharding weights for a model."""
    return {
        name: _process_loaded_weight(
            name, w, device, dtype, ms, process_idx, parallel_config
        )
        for name, w in weights.items()
    }


def _generate_step_fn(
    ms: model_specs.LlamaModelSpec,
    parallel_config: ParallelConfig,
    dtype: torch.dtype = torch.bfloat16,
    batch_sizes: Sequence[int] | None = None,
    process_idx: int = 0,
    num_processes: int = 1,
    num_layers_per_load: int = 5,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    logging.info("Loading a LLAMA model %s onto %d processes.", ms.name, num_processes)
    logging.debug(
        "ms: %s, batch_sizes: %s, process_idx: %s, num_processes: %s",
        ms,
        batch_sizes,
        process_idx,
        num_processes,
    )
    if parallel_config.num_processes != num_processes:
        raise ValueError(
            f"parallel_config.num_processes={parallel_config.num_processes} does not match num_processes={num_processes}"
        )

    # TODO: load only the weights that we need for this process.
    device = f"cuda:{process_idx}"
    model = Llama(
        ms=ms,
        dtype=dtype,
        device=device,
        process_idx=process_idx,
        parallel_config=parallel_config,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
        all_reduce_impl=all_reduce_impl,
        output_type=output_type,
    )

    if load_checkpoint_weights:
        shards_by_name = shard_args_by_name(
            ms=ms, process_idx=process_idx, parallel_config=parallel_config
        )

        loaded_weights = set()
        torch.cuda.empty_cache()
        weights = process_loaded_weights(
            save_load.load_weights(
                ms.checkpoint_path,
                require_patterns=(
                    r"^embs\.",
                    r"^final_rms_norm\.",
                    r"^score\.",
                    r"^output_projection\.",
                ),
                # none of the loaded weights will actually match; pass this anyway
                # out of paranoia
                shard_load_args=shards_by_name,
                target_sha256=ms.checkpoint_sha256,
                device=device,
            ),
            ms=ms,
            device=device,
            process_idx=process_idx,
            parallel_config=parallel_config,
        )
        model.load_state_dict(weights, strict=False)
        loaded_weights.update(weights.keys())
        del weights

        for block_idx in range(
            (ms.num_layers + num_layers_per_load - 1) // num_layers_per_load
        ):
            torch.cuda.empty_cache()
            layer_idxs = [
                rf"^layers\.{num_layers_per_load * block_idx + i}\."
                for i in range(num_layers_per_load)
                if num_layers_per_load * block_idx + i < ms.num_layers
            ]
            weights = process_loaded_weights(
                save_load.load_weights(
                    ms.checkpoint_path,
                    require_patterns=tuple(layer_idxs),
                    shard_load_args=shards_by_name,
                    target_sha256=ms.checkpoint_sha256,
                    device=device,
                ),
                ms=ms,
                device=device,
                process_idx=process_idx,
                parallel_config=parallel_config,
            )
            model.load_state_dict(weights, strict=False)
            loaded_weights.update(weights.keys())
            del weights
        # We loaded state with strict=False; perform that check ourselves now
        model_weights = set(model.state_dict().keys())
        if loaded_weights != model_weights:
            raise RuntimeError(
                f"Unexpected keys: {loaded_weights - model_weights}. "
                f"Missing keys: {model_weights - loaded_weights}."
            )

    return model


def generate_step_fn(
    ms: model_specs.LlamaModelSpec,
    dtype: torch.dtype = torch.bfloat16,
    num_processes: int = 1,
    load_checkpoint_weights: bool = True,
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
    all_reduce_impl: all_reduce.AllReduceImpl = all_reduce.AllReduceImpl.NCCL,
    output_type: fwd.OutputTensorType = fwd.OutputTensorType.VOCAB_LOGITS,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        ms: Model spec.
        num_processes: Number of GPUs to use.
        load_checkpoint_weights: load the weights into the step_fn. should be `True`
            by default. only set to `False` for testing, and if so, make sure you
            know what you are doing.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
    """
    logging.info("Using base.fastforward")

    parallel_config = ParallelConfig.from_legacy_config(num_processes, False)
    if parallel_config.num_processes == 1:
        return _generate_step_fn(
            ms=ms,
            dtype=dtype,
            parallel_config=parallel_config,
            batch_sizes=batch_sizes,
            load_checkpoint_weights=load_checkpoint_weights,
            auto_capture_graphs=auto_capture_graphs,
            output_type=output_type,
        )
    else:
        mp = ParallelForwardRunner(num_processes=parallel_config.num_processes)
        return mp.initialize(
            [_generate_step_fn],
            [
                {
                    "ms": ms,
                    "dtype": dtype,
                    "parallel_config": parallel_config,
                    "batch_sizes": batch_sizes,
                    "load_checkpoint_weights": load_checkpoint_weights,
                    "auto_capture_graphs": auto_capture_graphs,
                    "all_reduce_impl": all_reduce_impl,
                    "output_type": output_type,
                }
            ],
        )

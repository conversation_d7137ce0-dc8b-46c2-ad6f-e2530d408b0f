load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "save_load_v1",
    srcs = ["save_load_v1.py"],
    visibility = ["//base/fastforward/checkpoints:__subpackages__"],
    deps = [
        requirement("torch"),
        requirement("numpy"),
        requirement("safetensors"),
    ],
)

pytest_test(
    name = "save_load_test_v1",
    srcs = [
        "save_load_test_v1.py",
    ],
    deps = [
        ":save_load_v1",
    ],
)

py_library(
    name = "save_load_v2",
    srcs = ["save_load_v2.py"],
    visibility = ["//base/fastforward/checkpoints:__subpackages__"],
    deps = [
        ":manifest",
        ":sharding",
        requirement("torch"),
        requirement("numpy"),
        requirement("safetensors"),
    ],
)

pytest_test(
    name = "save_load_test_v2",
    srcs = [
        "save_load_test_v2.py",
    ],
    deps = [
        ":save_load_v2",
    ],
)

py_library(
    name = "manifest",
    srcs = ["manifest.py"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        requirement("dataclasses-json"),
        requirement("google-cloud-storage"),
    ],
)

pytest_test(
    name = "manifest_test",
    srcs = ["manifest_test.py"],
    deps = [
        ":manifest",
    ],
)

py_library(
    name = "sharding",
    srcs = ["sharding.py"],
    visibility = ["//base/fastforward/checkpoints:__subpackages__"],
    deps = [
        requirement("torch"),
        requirement("numpy"),
    ],
)

pytest_test(
    name = "sharding_test",
    srcs = ["sharding_test.py"],
    deps = [
        ":sharding",
        requirement("safetensors"),
    ],
)

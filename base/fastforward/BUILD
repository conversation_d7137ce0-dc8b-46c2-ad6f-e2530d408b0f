load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:pytorch.bzl", "pytorch_cpp_extension")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

pytorch_cpp_extension(
    name = "all_reduce_kernel",
    srcs = [
        "all_reduce_kernel.cpp",
        "all_reduce_kernel.h",
    ],
    cuda_srcs = [
        "all_reduce_kernel.cu",
        "all_reduce_kernel.h",
    ],
)

py_library(
    name = "all_reduce",
    srcs = ["all_reduce.py"],
    data = [
        ":all_reduce_kernel.so",
    ],
    # For enum def access in //services
    visibility = ["//visibility:public"],
    deps = [
        requirement("cuda-python"),
        requirement("torch"),
    ],
)

pytorch_cpp_extension(
    name = "compiled_attention_utils",
    srcs = [
        "compiled_attention_utils.cpp",
    ],
    cuda_srcs = [
        "compiled_attention_utils.cu",
    ],
)

pytest_test(
    name = "compiled_attention_utils_test",
    srcs = [
        "compiled_attention_utils_test.py",
    ],
    data = [":compiled_attention_utils.so"],
    tags = [
        "gpu",
    ],
    deps = [
        requirement("numpy"),
        requirement("torch"),
    ],
)

pytest_test(
    name = "compiled_split_cumulative_test",
    srcs = [
        "compiled_split_cumulative_test.py",
    ],
    data = [":compiled_attention_utils.so"],
    tags = [
        "gpu",
    ],
    deps = [
        requirement("numpy"),
        requirement("torch"),
    ],
)

pytorch_cpp_extension(
    name = "compiled_rotary",
    srcs = [
        "compiled_rotary.cpp",
    ],
    cuda_srcs = [
        "compiled_rotary.cu",
    ],
)

pytorch_cpp_extension(
    name = "compiled_rotary_qkv",
    srcs = [
        "compiled_rotary_qkv.cpp",
    ],
    cuda_srcs = [
        "compiled_rotary_qkv.cu",
    ],
)

py_library(
    name = "positional_embeddings",
    srcs = ["positional_embeddings.py"],
    data = [
        ":compiled_rotary.so",
        ":compiled_rotary_qkv.so",
    ],
    pyright = False,
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        requirement("numpy"),
        requirement("torch"),
        requirement("dataclasses-json"),
    ],
)

py_library(
    name = "positional_embeddings_test_utils",
    srcs = ["positional_embeddings_test_utils.py"],
    data = [
        ":compiled_rotary.so",
        ":compiled_rotary_qkv.so",
    ],
    deps = [
        requirement("torch"),
    ],
)

pytest_test(
    name = "positional_embeddings_test",
    srcs = ["positional_embeddings_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        ":positional_embeddings",
        ":positional_embeddings_test_utils",
        requirement("numpy"),
        requirement("torch"),
    ],
)

py_library(
    name = "attention",
    srcs = ["attention.py"],
    deps = [
        requirement("torch"),
    ],
)

py_library(
    name = "register_tokens",
    srcs = ["register_tokens.py"],
    data = [
        ":compiled_attention_utils.so",
    ],
    deps = [
        requirement("torch"),
    ],
)

pytest_test(
    name = "register_tokens_test",
    srcs = ["register_tokens_test.py"],
    data = [
        ":compiled_attention_utils.so",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":register_tokens",
        requirement("torch"),
    ],
)

py_library(
    name = "cached_attention",
    srcs = ["cached_attention.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":all_reduce",
        ":multirequest_flash_attention",
        ":parallel",
        ":positional_embeddings",
        ":register_tokens",
        requirement("torch"),
        requirement("flash-attn"),
    ],
)

py_library(
    name = "multirequest_flash_attention",
    srcs = ["multirequest_flash_attention.py"],
    data = [
        ":compiled_attention_utils.so",
    ],
    visibility = ["//visibility:public"],
    deps = [
        requirement("torch"),
        requirement("flash-attn"),
        requirement("flashattn-hopper"),
    ],
)

pytest_test(
    name = "multirequest_flash_attention_test",
    srcs = [
        "multirequest_flash_attention_test.py",
    ],
    data = [
        ":compiled_attention_utils.so",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":compiled_attention_utils_test",
        ":conftest",
        ":cuda_graphs",
        ":multirequest_flash_attention",
        requirement("torch"),
        requirement("flash-attn"),
    ],
)

pytest_test(
    name = "multirequest_flash_attention_test_h100_kernels",
    srcs = [
        "multirequest_flash_attention_test.py",
    ],
    args = ["--enable-h100-kernels"],
    data = [
        ":compiled_attention_utils.so",
    ],
    tags = [
        "gpu",
        "large-gpu",
    ],
    deps = [
        ":compiled_attention_utils_test",
        ":conftest",
        ":cuda_graphs",
        ":multirequest_flash_attention",
        requirement("torch"),
        requirement("flash-attn"),
    ],
)

py_library(
    name = "layers",
    srcs = ["layers.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":all_reduce",
        ":cached_attention",
        ":torch_utils",
        requirement("torch"),
    ],
)

pytest_test(
    name = "layers_test",
    srcs = [
        "layers_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":layers",
        requirement("torch"),
        requirement("numpy"),
    ],
)

pytest_test(
    name = "cached_attention_test",
    srcs = [
        "cached_attention_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":attention",
        ":cached_attention",
        requirement("torch"),
    ],
)

pytest_test(
    name = "cached_attention_multigpu_test",
    srcs = [
        "cached_attention_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":attention",
        ":cached_attention",
        ":parallel",
        ":torch_utils",
        requirement("torch"),
    ],
)

py_library(
    name = "cuda_graphs",
    srcs = ["cuda_graphs.py"],
    visibility = ["//visibility:public"],
    deps = [
        requirement("immutabledict"),
        requirement("torch"),
    ],
)

pytest_test(
    name = "cuda_graphs_test",
    srcs = ["cuda_graphs_test.py"],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        ":cuda_graphs",
        requirement("torch"),
    ],
)

pytest_test(
    name = "cuda_graphs_multigpu_test",
    size = "small",
    srcs = ["cuda_graphs_multigpu_test.py"],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":cuda_graphs",
        ":parallel",
        ":torch_utils",
        requirement("torch"),
    ],
)

py_library(
    name = "cuda_graphs_attention",
    srcs = ["cuda_graphs_attention.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":cached_attention",
        ":cuda_graphs",
        ":fwd",
        requirement("torch"),
    ],
)

py_library(
    name = "fwd",
    srcs = ["fwd.py"],
    visibility = ["//visibility:public"],
    deps = [
        "cached_attention",
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_sampler",
    srcs = ["fwd_sampler.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:fwd_torch",
        "//base/fastforward:torch_utils",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_sampler_test",
    size = "medium",
    srcs = [
        "fwd_sampler_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":fwd",
        ":fwd_sampler",
        ":fwd_torch",
        requirement("torch"),
    ],
)

pytest_test(
    name = "communication_multigpu_test",
    size = "medium",
    srcs = [
        "communication_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":all_reduce",
        ":fp8",
        ":fwd",
        ":parallel",
        "//base/static_analysis:common",
        requirement("cuda-python"),
        requirement("torch"),
    ],
)

py_library(
    name = "parallel",
    srcs = ["parallel.py"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        requirement("structlog"),
        requirement("torch"),
        requirement("numpy"),
    ],
)

py_library(
    name = "parallel_fwd",
    srcs = ["parallel_fwd.py"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        ":cached_attention",
        ":fwd",
        ":fwd_torch",
        ":parallel",
    ],
)

pytest_test(
    name = "parallel_test",
    size = "small",
    srcs = [
        "parallel_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        ":parallel",
        ":torch_utils",
        requirement("torch"),
    ],
)

pytest_test(
    name = "parallel_multigpu_test",
    size = "small",
    srcs = [
        "parallel_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":parallel",
        ":torch_utils",
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_dummy",
    srcs = ["fwd_dummy.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":all_reduce",
        ":cached_attention",
        ":fwd",
        ":fwd_torch",
        requirement("torch"),
    ],
)

pytest_test(
    name = "layers_multigpu_test",
    size = "small",
    srcs = [
        "layers_multigpu_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
        "multi-gpu",
        "postmerge-test",
    ],
    deps = [
        ":fwd",
        ":layers",
        ":parallel",
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_utils",
    srcs = ["fwd_utils.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":batching",
        ":fwd",
        ":fwd_torch",
        "//base/fastforward/checkpoints/impl:manifest",
        requirement("pyyaml"),
    ],
)

py_library(
    name = "fwd_torch",
    srcs = ["fwd_torch.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":fwd",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_torch_test",
    srcs = [
        "fwd_torch_test.py",
    ],
    deps = [
        "//base/fastforward:fwd_torch",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_utils_test",
    srcs = [
        "fwd_utils_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":fwd",
        ":fwd_testmodel",
        ":fwd_utils",
        "//base/fastforward/llama:conftest",
        "//base/fastforward/llama:fwd_llama",
        requirement("torch"),
    ],
)

py_library(
    name = "torch_utils",
    srcs = ["torch_utils.py"],
    visibility = ["//visibility:public"],
    deps = [
        requirement("torch"),
    ],
)

pytest_test(
    name = "torch_utils_test",
    size = "small",
    srcs = [
        "torch_utils_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":torch_utils",
        requirement("torch"),
    ],
)

py_library(
    name = "fp8",
    srcs = ["fp8.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":torch_utils",
        requirement("numpy"),
        requirement("setuptools"),
        requirement("torch"),
        requirement("transformer_engine"),
        requirement("nvidia-cuda-nvrtc-cu12"),
    ],
)

pytest_test(
    name = "fp8_test",
    srcs = ["fp8_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        ":fp8",
        requirement("torch"),
    ],
    # An alternative to setting the env variable in python files:
    # env = {
    #     "NVTE_INSTALL_PATH": "external/python_pip_transformer_engine/site-packages"
    # },
)

py_library(
    name = "layers_fp8",
    srcs = ["layers_fp8.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":all_reduce",
        ":fp8",
        ":fwd",
        ":parallel",
        ":torch_utils",
        requirement("numpy"),
        requirement("torch"),
        requirement("transformer_engine"),
    ],
)

py_library(
    name = "fwd_model_test_utils",
    testonly = True,
    srcs = ["fwd_model_test_utils.py"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        ":fwd",
        requirement("torch"),
    ],
)

py_library(
    name = "quantize_utils",
    srcs = ["quantize_utils.py"],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        ":fp8",
        ":fwd",
        ":fwd_torch",
        requirement("numpy"),
        requirement("torch"),
    ],
)

py_library(
    name = "fwd_testmodel",
    testonly = True,
    srcs = ["fwd_testmodel.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":all_reduce",
        ":cached_attention",
        ":fwd",
        ":fwd_torch",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_testmodel_test",
    srcs = [
        "fwd_testmodel_test.py",
    ],
    deps = [
        ":fwd_testmodel",
        "//base/fastforward:fwd",
        requirement("torch"),
    ],
)

py_library(
    name = "batching",
    srcs = ["batching.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":cached_attention",
        requirement("numpy"),
        requirement("torch"),
    ],
)

pytest_test(
    name = "batching_test",
    timeout = "short",
    srcs = [
        "batching_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        ":batching",
        ":cached_attention",
        ":fwd",
        ":fwd_testmodel",
        "//base/fastforward:conftest",
        "//base/fastforward/starcoder:conftest",
        requirement("torch"),
    ],
)

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    visibility = ["//base/fastforward:__subpackages__"],
    deps = [
        ":fwd",
        ":fwd_testmodel",
        ":fwd_utils",
        "//base/fastforward:cached_attention",
        "//base/fastforward/starcoder:fwd_starcoder",
        requirement("pytest"),
        requirement("torch"),
    ],
)

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    data = [
        ":all_reduce_kernel.so",
        ":compiled_attention_utils.so",
        ":compiled_rotary.so",
        ":compiled_rotary_qkv.so",
    ],
    visibility = ["//base:__subpackages__"],
)

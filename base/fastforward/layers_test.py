"""Smoke test some layer implementations and ensure they match expected outputs."""

import numpy as np
import pytest
import torch
import torch.nn as nn

from base.fastforward import layers


def _init_module(mdl: nn.Module, gaussian_init: bool = False):
    with torch.inference_mode():
        for p in mdl.parameters():
            std = 1.0 / np.sqrt(p.shape[0])
            p.normal_(std=std) if gaussian_init else p.uniform_(-0.1, 0.1)


@pytest.mark.parametrize(
    "inp_dim, expected_out",
    [
        pytest.param(10, 0.03050602413713932, id="0"),
        pytest.param(11, 0.02675773948431015, id="1"),
        pytest.param(17, 0.03066488914191723, id="2"),
    ],
)
def test_rms_norm(inp_dim: int, expected_out: float):
    torch.random.manual_seed(31415)

    inp = torch.empty(inp_dim, dtype=torch.float32, device="cuda").uniform_(-0.1, 0.1)

    layer = layers.RmsNorm(inp_dim=inp_dim, dtype=torch.float32)
    _init_module(layer)

    out = layer(inp).float().mean().item()
    torch.testing.assert_close(out, expected_out)


@pytest.mark.parametrize(
    "num_tokens, vocab_size, emb_dim, expected_out",
    [pytest.param(3, 512, 32, 0.0010970182484015822, id="0")],
)
def test_llama_word_embeddings(
    num_tokens: int, vocab_size: int, emb_dim: int, expected_out: float
):
    torch.random.manual_seed(31415)
    inp = torch.randint(
        low=0, high=vocab_size, size=(num_tokens,), dtype=torch.int32, device="cuda"
    )

    layer = layers.WordEmbeddings(
        vocab_size=vocab_size,
        emb_dim=emb_dim,
        dtype=torch.float32,
    )
    _init_module(layer)
    out = layer(inp)
    torch.testing.assert_close(out.float().mean().item(), expected_out)


@pytest.mark.parametrize(
    "num_tokens, emb_dim, mlp_dim, expected_out",
    [
        pytest.param(10, 16, 32, 2.30897803703559e-05, id="0"),
        pytest.param(11, 12, 18, 4.87981342303101e-05, id="1"),
        pytest.param(17, 11, 32, -0.00031015370041132, id="2"),
    ],
)
def test_llama_swiglu(num_tokens: int, emb_dim: int, mlp_dim: int, expected_out: float):
    torch.random.manual_seed(31415)

    inp = torch.empty(num_tokens, emb_dim, dtype=torch.float32, device="cuda")
    inp.uniform_(-1.0, 1.0)

    layer = layers.LlamaSwiGLU(emb_dim=emb_dim, mlp_dim=mlp_dim, dtype=torch.float32)
    _init_module(layer)

    out = layer(inp).float().mean().item()
    torch.testing.assert_close(out, expected_out)


@pytest.mark.parametrize(
    "emb_dim, mlp_dim, moe_num_experts, moe_top_k",
    [
        pytest.param(32, 64, 4, 2, id="0"),
        pytest.param(32, 64, 16, 4, id="1"),
    ],
)
@torch.inference_mode()
def test_dbrx_ffn_layer(
    emb_dim: int, mlp_dim: int, moe_num_experts: int, moe_top_k: int
):
    """Test the result of DBRX FFN layer vs. a np-based dense style implementation."""
    torch.random.manual_seed(31415)
    seq_len = 16
    inp = torch.empty(seq_len, emb_dim, dtype=torch.float32, device="cuda")
    inp.normal_()

    layer = layers.DbrxFFN(
        emb_dim=emb_dim,
        mlp_dim=mlp_dim,
        moe_num_experts=moe_num_experts,
        moe_top_k=moe_top_k,
        moe_normalize_expert_weights=1.0,
        dtype=torch.float32,
        device="cuda",
    )
    _init_module(layer, gaussian_init=True)
    out = layer(inp)
    output_np = out.cpu().numpy()
    assert output_np.shape == (seq_len, emb_dim)

    # Manually compute without tensor parallel and sparsity.
    inputs_np = inp.cpu().numpy()
    w1_np = layer.w1.detach().cpu().numpy()
    w2_np = layer.w2.detach().cpu().numpy()
    v1_np = layer.v1.detach().cpu().numpy()
    router_np = layer.router.weight.detach().cpu().numpy()
    assert router_np.shape == (moe_num_experts, emb_dim)
    router_logits = inputs_np @ router_np.T
    router_logits = router_logits - np.max(router_logits, axis=-1, keepdims=True)
    router_probs = np.exp(router_logits) / np.sum(
        np.exp(router_logits), axis=-1, keepdims=True
    )
    top_experts_indices = np.argsort(-router_probs, axis=-1)[:, :moe_top_k]
    top_experts_mask = np.zeros_like(router_probs, dtype=bool)
    np.put_along_axis(top_experts_mask, top_experts_indices, True, axis=-1)
    router_probs[~top_experts_mask] = 0
    router_probs = router_probs / np.sum(router_probs, axis=-1, keepdims=True)
    assert w1_np.shape == (moe_num_experts, emb_dim, mlp_dim)
    gate_proj = (inputs_np[:, None, None, :] @ w1_np[None, ...]).reshape(
        seq_len, moe_num_experts, mlp_dim
    )
    gate_proj = gate_proj / (1.0 + np.exp(-gate_proj))
    up_proj = (inputs_np[:, None, None, :] @ v1_np[None, ...]).reshape(
        seq_len, moe_num_experts, mlp_dim
    )
    gated_proj = gate_proj * up_proj
    down_proj = (gated_proj[..., None, :] @ w2_np[None, ...]).reshape(
        seq_len, moe_num_experts, emb_dim
    )
    manual_output_np = (down_proj * router_probs[..., None]).sum(axis=1)
    assert np.allclose(output_np, manual_output_np, atol=1e-4)

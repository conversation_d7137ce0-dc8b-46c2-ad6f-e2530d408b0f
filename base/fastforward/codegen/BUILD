load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "fwd_codegen",
    srcs = ["fwd_codegen.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_torch",
        "//base/fastforward:parallel",
        "//base/fastforward:positional_embeddings",
        requirement("torch"),
    ],
)

pytest_test(
    name = "fwd_codegen_test",
    size = "medium",
    srcs = [
        "fwd_codegen_test.py",
    ],
    tags = [
        "exclusive",
        "gpu",
    ],
    deps = [
        "//base/fastforward:cached_attention",
        "//base/fastforward/codegen:fwd_codegen",
        requirement("torch"),
        requirement("numpy"),
    ],
)

load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "extract_next_edit_filter_features",
    srcs = ["extract_next_edit_filter_features.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/tokenizers",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "extract_next_edit_filter_features_test",
    srcs = ["extract_next_edit_filter_features_test.py"],
    deps = [
        ":extract_next_edit_filter_features",
        "//base/tokenizers",
    ],
)

py_library(
    name = "rule_based_filters",
    srcs = ["rule_based_filters.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//base/caching:lru_cache",
        "//base/diff_utils",
        "//base/languages",
        "//base/ranges",
        "//base/static_analysis:import_finder",
    ],
)

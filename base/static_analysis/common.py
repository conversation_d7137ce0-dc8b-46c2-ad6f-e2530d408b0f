"""Utils that are commonly used across the static_analysis package."""

from __future__ import annotations

import collections
import difflib
import sys
import types
import typing
from dataclasses import dataclass
from functools import cached_property
from itertools import accumulate
from pathlib import Path
from typing import (
    Callable,
    Collection,
    Hashable,
    Iterable,
    Literal,
    Mapping,
    Optional,
    TypeVar,
    Union,
    get_args,
)

import tree_sitter
from typing_extensions import TypeGuard

from base import languages
from base.ranges import ByteMap, ByteRange, <PERSON>rRang<PERSON>, IntRange
from base.ranges.line_map import LineMap
from base.ranges.string_utils import replace_str, shorten_str, shorten_str_detailed

__reexported__ = [
    ByteMap,
    ByteRange,
    CharRange,
    IntRange,
    replace_str,
    shorten_str,
    shorten_str_detailed,
]

AT = TypeVar("AT")
BT = TypeVar("BT")


LanguageID = Literal[
    "python",
    "java",
    "cpp",
    "javascript",
    "typescript",
    "go",
    "rust",
    "c_sharp",
    "php",
    "html",
    "dart",
    "css",
    "bash",
    "scala",
    "ruby",
    "lua",
    "sql",
    "kotlin",
    "markdown",
]
"""The set of languages with tree-sitter parsing support.

Note that the names are picked from the tree-sitter binding names, which are
defined at https://github.com/grantjenks/py-tree-sitter-languages/blob/main/repos.txt.

See also the (larger set of) language ids defined in `base.languages`.
"""

AllSupportedLanguages: tuple[LanguageID, ...] = typing.get_args(LanguageID)
"""The sequence of all supported languages."""


def is_parsing_supported_lang(
    lang: str,
) -> TypeGuard[LanguageID]:
    """Return whether we support generating signatures for the given language."""
    return lang in get_args(LanguageID)


_base_lang_id_map: dict[languages.LanguageId, LanguageID] = {
    "Python": "python",
    "Java": "java",
    "C++": "cpp",
    "JavaScript": "javascript",
    "TypeScript": "typescript",
    "Go": "go",
    "Rust": "rust",
}
_base_lang_id_imap: dict[LanguageID, languages.LanguageId] = {
    slang: blang for blang, slang in _base_lang_id_map.items()
}


def convert_to_static_analysis_language(
    language: languages.LanguageId,
) -> LanguageID | None:
    return _base_lang_id_map.get(language)


def convert_from_static_analysis_language(language: LanguageID) -> languages.LanguageId:
    return _base_lang_id_imap[language]


lang_id_to_extensions: Mapping[LanguageID, Collection[str]] = {
    "cpp": {
        ".c",
        ".cpp",
        ".h",
        ".h++",
        ".hh",
        ".hpp",
        ".hxx",
        ".cc",
        ".cxx",
        ".C",
        ".H",
        ".c++",
        ".inl",
        ".ixx",
        ".cp",
    },
    "go": {".go"},
    "java": {".java", ".jav"},
    "javascript": {".js", ".jsx"},
    "typescript": {".ts", ".tsx"},
    "python": {".py", ".bzl"},
    "rust": {".rs"},
    "c_sharp": {".cs"},
    "php": {".php"},
    "html": {".html", ".htm"},
    "css": {".css"},
    "bash": {".sh"},
    "scala": {".scala"},
    "ruby": {".rb"},
    "lua": {".lua"},
    "sql": {".sql"},
    "kotlin": {".kt"},
    "markdown": {".md"},
    "dart": {".dart"},
}
"""Map each language id to its file extensions."""

file_ext_to_lang_id: Mapping[str, LanguageID] = {
    ext: lang for lang, exts in lang_id_to_extensions.items() for ext in exts
}
"""Map from file extension to language id."""


@dataclass
class MappedCode:
    """Convenient utilities for range arithmetics on code content."""

    code: str
    """the code content."""

    @cached_property
    def bmap(self) -> ByteMap:
        """The byte map of the code."""
        return ByteMap(self.code)

    @cached_property
    def lmap(self) -> LineMap:
        """The line map of the code."""
        return LineMap(self.code)

    @cached_property
    def nonwhitespace_accum(self):
        """Return the accumulate of the nonwhitespace char counts."""
        result = list(accumulate(not c.isspace() for c in self.code))
        last = result[-1] if result else 0
        return result + [last]

    def nonwhitespace_chars(self, crange: CharRange) -> int:
        """Return the number of nonwhitespace chars in the given range."""
        return (
            self.nonwhitespace_accum[crange.stop]
            - self.nonwhitespace_accum[crange.start]
        )


def _empty_message():
    return ""


def assert_eq(
    actual: AT, expect: AT, message: Callable[[], str] = _empty_message
) -> None:
    """Assert that two values x and y are equal.

    If not equal, will raise an `AssertionError` containing the values and types of
    the two inputs.
    """
    if actual != expect:
        try:
            if isinstance(actual, str) and isinstance(expect, str):
                assert_str_eq(actual, expect, message)
            else:
                raise AssertionError(
                    f"{actual} (of type {type(actual).__name__}) "
                    f"!= {expect} (of type {type(expect).__name__}).\n" + message()
                )
        except AssertionError as e:
            # drop `assert_eq` from the stack trace to improve readability
            raise _drop_stack_frame(e)


def assert_str_eq(
    actual: str, expect: str, message: Callable[[], str] = _empty_message
):
    """Assert that two strings x and y are equal.

    If not equal, will raise an `AssertionError` containing a diff of the two strings.
    """
    if actual != expect:
        diff = show_str_diff(old=expect, new=actual)
        try:
            raise AssertionError(
                f"Strings are not equal: {message()}\n"
                f"[[Expected]]\n{expect}\n"
                f"[[Actual]]\n{actual}\n"
                f"[[Diff against expected]]\n{diff}\n"
            )
        except AssertionError as e:
            # drop `assert_str_eq` from the stack trace to improve readability
            raise _drop_stack_frame(e)


_ET = TypeVar("_ET", bound=Exception)


def _drop_stack_frame(e: _ET) -> _ET:
    """Drop the top stack frame from the traceback of `e`."""
    tb = sys.exc_info()[2]
    assert tb is not None
    back_frame = tb.tb_frame.f_back
    assert back_frame is not None
    back_tb = types.TracebackType(
        tb_next=None,
        tb_frame=back_frame,
        tb_lasti=back_frame.f_lasti,
        tb_lineno=back_frame.f_lineno,
    )
    return e.with_traceback(back_tb)


def show_str_diff(old: str, new: str) -> str:
    """Show the diff between two strings."""
    difflines = difflib.unified_diff(
        old.splitlines(keepends=True), new.splitlines(keepends=True)
    )
    return "".join(difflines)


def check_not_none(value: Optional[AT]) -> AT:
    """Convert an optional value to non-optional.

    Raises an AssertionError if `value` is None.
    """
    if value is None:
        raise AssertionError("Expected non-None value.")
    return value


def get_first(xs: Iterable[AT], default: AT | None = None) -> AT | None:
    """Get the first element of an iterable, or return the default."""
    try:
        return next(iter(xs))
    except StopIteration:
        return default


def get_supported_extensions() -> Mapping[str, LanguageID]:
    """Returns mapping from supported file extension to LanguageID."""
    # we return file_ext_to_lang_id instead of `augment_supported_extensions.json`
    # to make sure that we only use files that are actually parsable code.
    return file_ext_to_lang_id


def guess_lang_from_fp(fname: str | Path | None) -> LanguageID | None:
    """Guess language id from the file path."""
    if fname is None:
        return None
    extension = Path(fname).suffix.lower()
    return file_ext_to_lang_id.get(extension)


def get_vscode_language_name(lang: LanguageID) -> str:
    """Return the VSCode language name for a given static_analysis LanguageID."""
    # so far, all our supported language ids are the same as the ones used by VS Code.
    return lang


def decode_bytes(byte_seq: bytes | None) -> str:
    """Decodes a bytes object into a string assuming utf-8 encoding."""
    return byte_seq.decode("utf-8") if byte_seq else ""


def groupby(iterable: Iterable[AT], keyfunc: Callable[[AT], BT]) -> dict[BT, list[AT]]:
    """Group a sequence of items by a key function."""
    groups = dict[BT, list[AT]]()
    for item in iterable:
        key = keyfunc(item)
        groups.setdefault(key, []).append(item)
    return groups


def tsnode_to_crange(node: tree_sitter.Node, bmap: ByteMap) -> CharRange:
    return bmap.brange_to_crange(ByteRange(node.start_byte, node.end_byte))


def iter_ts_nodes_via_bfs(
    tree_or_node: Union[tree_sitter.Tree, tree_sitter.Node],
) -> Iterable[tree_sitter.Node]:
    """Collect all the tree-sitter nodes via BFS."""
    queue = collections.deque(
        [
            (
                tree_or_node.root_node
                if isinstance(tree_or_node, tree_sitter.Tree)
                else tree_or_node
            )
        ]
    )
    while queue:
        node = queue.popleft()
        yield node
        queue.extend(node.children)


def iter_ts_nodes_via_dfs(
    tree_or_node: Union[tree_sitter.Tree, tree_sitter.Node],
) -> Iterable[tree_sitter.Node]:
    """Collect all the tree-sitter nodes via DFS."""
    stack = [
        (
            tree_or_node.root_node
            if isinstance(tree_or_node, tree_sitter.Tree)
            else tree_or_node
        )
    ]
    while stack:
        node = stack.pop()
        yield node
        stack.extend(reversed(node.children))


def get_ts_node_sibling(
    node: tree_sitter.Node,
    offset: int,
    skip_empty: bool = False,
    skip_punctuation: bool = False,
    skip_comment: bool = False,
) -> Optional[tree_sitter.Node]:
    """Retrieve the sibling of a given tree_sitter.Node based on an offset.

    Given a tree_sitter.Node instance and an offset, this function returns the sibling
    node located at 'offset' positions away from the provided node within its parent's
    children list. An offset of 1 would give the immediate next sibling, an offset of -1
    would give the immediate previous sibling, and so on.

    Args:
        node: The node whose sibling is to be found.
        offset: The position offset relative to the given node to locate the sibling.
        skip_empty: skip nodes that are just spaces or new lines.
        skip_punctuation: skip nodes that are just punctuation.
        skip_comment: skip nodes that are comments.

    Returns the sibling node if it exists. Returns None if the provided node has no\
    parent, or if the calculated sibling index is out of bounds.

    Example:
    >>> node = get_some_tree_sitter_node()
    >>> next_sibling = get_ts_node_sibling(node, 1)
    >>> prev_sibling = get_ts_node_sibling(node, -1)
    """
    if node.parent is None:
        return None
    all_siblings = node.parent.children
    if skip_empty:
        all_siblings = [x for x in all_siblings if decode_bytes(x.text).strip()]
    if skip_punctuation:
        all_siblings = [
            x
            for x in all_siblings
            if x.text
            and x.text.strip()
            not in (b",", b".", b";", b":", b"|", b"?", b">", b"<", b"#")
        ]
    if skip_comment:
        all_siblings = [x for x in all_siblings if "comment" not in x.type]
    if node not in all_siblings:
        return None
    index = all_siblings.index(node)
    sibling_index = index + offset
    if 0 <= sibling_index < len(all_siblings):
        return all_siblings[sibling_index]
    else:
        return None


def optional_map(x: Optional[AT], f: Callable[[AT], BT]) -> Optional[BT]:
    """Apply f to x if x is not None, otherwise return None."""
    return None if x is None else f(x)


def deduplicate(
    xs: Iterable[AT], key: Callable[[AT], Hashable] | None = None
) -> list[AT]:
    """Return a new list with duplicates removed.

    If `key` is provided, will use it to compute uniqueness.
    """
    if key is None:
        return list(dict.fromkeys(xs))
    unique_dict = dict()
    for x in xs:
        k = key(x)
        if k not in unique_dict:
            unique_dict[k] = x
    return list(unique_dict.values())


def join_list(xxs: Iterable[Iterable[AT]]) -> list[AT]:
    """Join a iterable of iterables as a list."""
    return [x for xs in xxs for x in xs]

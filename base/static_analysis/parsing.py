"""This module provides a simple interface to parse source code into a scope tree.

A scope tree consists of nested scopes (`SrcScope`) and spans (`SrcSpan`).
- A scope is a loosely defined concept that represents a logical unit of code, such as
a function or a class.
- A span is a contiguous range of source code that is not
a scope. For example, a function is a scope, but the body itself is a span.

To turn source code into a scope tree, use the `ScopeTreeParser.parse` method.

For example usages of this module, see the tutorial at
`research/notebooks/parsing_tutorial.ipynb`.
"""

from __future__ import annotations

import sys
from copy import copy, deepcopy
from dataclasses import dataclass
from functools import cached_property
from pathlib import Path
from typing import (
    Any,
    Callable,
    Collection,
    Iterable,
    Literal,
    Mapping,
    Optional,
    Sequence,
    Union,
    cast,
)

import tree_sitter as ts
from tree_sitter_languages import get_language
from typing_extensions import assert_never

from base.ranges import ByteRange, CharRange
from base.static_analysis.common import (
    ByteMap,
    LanguageID,
    MappedCode,
    assert_eq,
    decode_bytes,
    is_parsing_supported_lang,
    shorten_str,
)

ScopeKind = Literal["file", "class", "function"]
FieldName = Union[str, int, Callable[[ts.Node], Union[ts.Node, None]]]
FieldAddress = Union[FieldName, tuple[FieldName, ...]]
TsNodeType = str  # tree-sitter node type


@dataclass
class SrcSpan:
    """A contiguous span of source code augmented with source location info."""

    range: CharRange
    """The character range of this span."""

    code: str
    """The content inside this span."""

    def __repr__(self) -> str:
        if self.code is None:  # during parsing, this can temporarily be None
            code = "None"
        else:
            code = shorten_str(self.code)
        return f"SrcSpan(range={str(self.range)}, code={repr(code)})"

    def get_code(self) -> str:
        """Returns `self.code`."""
        return self.code

    def truncate_left(self, max_size: int) -> SrcSpan:
        """Truncate the left side of the span to the given size."""
        if len(self.code) <= max_size:
            return copy(self)
        new_code = self.code[len(self.code) - max_size :]
        reduction = min(len(self.code) - len(new_code), len(self.range))
        new_range = CharRange(self.range.start + reduction, self.range.stop)
        return SrcSpan(new_range, new_code)

    def truncate_right(self, max_size: int) -> SrcSpan:
        """Truncate the right side of the span to the given size."""
        if len(self.code) <= max_size:
            return copy(self)
        new_code = self.code[:max_size]
        reduction = min(len(self.code) - len(new_code), len(self.range))
        new_range = CharRange(self.range.start, self.range.stop - reduction)
        return SrcSpan(new_range, new_code)


@dataclass
class SrcScope:
    """A logical scope of source code.

    This can be a file, a class/namespace/module, or a function/method.
    Note that this data structured is designed to be used as if it's immutable,
    so you should avoid directly mutating it.
    On the flip side, you can safely reuse the same tree in multiple places and
    avoid deepcopying.
    (We didn't make this a frozen class in order to have a more efficient
    `parse_scopes` implementation.)
    """

    name: str
    """The name of file, class, or function."""

    name_range: CharRange
    """The character range of the name of this scope."""

    kind: ScopeKind
    """Whether this scope is a file, class, or function."""

    parent_name_override: Optional[str]
    """The name of the class this scope belongs to.

    `None` means this scope belongs to its parent scope. Some langauges allow defining
    methods outside of the class, in which case this should be the name of the target
    class."""

    children: Sequence[ScopeOrSpan]
    """The children of this scope. Can be either scopes or spans."""

    prefix: SrcSpan
    """For class, this is class header; for function, this is the signature;
    for file this is an empty span."""

    docstr: SrcSpan
    """The doc string associated with this scope. Can be an empty span if no
    doc string is found. This can either come before or after the `prefix`
    depending on the programming language. You should use `prefix_doc_pair` or
    `prefix_with_doc` to get both the prefix and docstr in the right order."""

    suffix: SrcSpan
    """For most langauges, this is just a closing brace; For Python it's empty."""

    @property
    def range(self) -> CharRange:
        """Return the byte range of this scope."""
        start = self.prefix_doc_pair()[0].range.start
        return CharRange(start, self.suffix.range.stop)

    @property
    def inner_range(self) -> CharRange:
        """Return the byte range of the inside of this scope."""
        return CharRange(self.prefix_doc_pair()[-1].range.stop, self.suffix.range.start)

    def prefix_doc_pair(self) -> tuple[SrcSpan, SrcSpan]:
        """Return the prefix and docstring, ordered by their source location.

        The specific order depends on the programming language.
        """
        if self.docstr.range < self.prefix.range:
            return (self.docstr, self.prefix)
        else:
            return (self.prefix, self.docstr)

    def prefix_with_doc(self) -> SrcSpan:
        """Merge the prefix and docstr into a single span for convenience."""
        s1, s2 = self.prefix_doc_pair()
        return SrcSpan(s1.range.merge(s2.range), s1.code + s2.code)

    def pprint(self, file=sys.stdout, indent: int = 4):
        """Pretty print the scope tree to the given file."""
        tab = " " * indent

        def rec(scope: SrcScope, level: int):
            print(
                tab * level,
                (
                    f"{scope.kind}(name={repr(scope.name)}, "
                    f"prefix={repr(shorten_str(scope.prefix.code))}, "
                    f"docstr={repr(shorten_str(scope.docstr.code))}, "
                    f"suffix={repr(shorten_str(scope.suffix.code))}) @ {scope.range}"
                ),
                file=file,
            )
            for child in scope.children:
                if isinstance(child, SrcScope):
                    rec(child, level + 1)
                else:
                    span = f"span(code={repr(shorten_str(child.code))}) @ {child.range}"
                    print(tab * (level + 1), span, file=file)

        rec(self, 0)

    def __repr__(self) -> str:
        return (
            f"SrcScope(name={repr(self.name)}, kind={repr(self.kind)}, "
            f"range={self.range}, "
            f"prefix={self.prefix}, doc_str={self.docstr}, suffix={self.suffix}, "
            f"len(children)={len(self.children)})"
        )

    def get_scope_path(self, *parts: Union[str, int]) -> Sequence[SrcScope]:
        """A convenient method to locate a scope using a path of names or indices.

        Return a path-like tree containing all scopes along the given path.
        Note that locating child by name rather than by position takes liner time
        in the worst case, and when multiple scopes have the same name,
        only the first one is returned.
        """
        path = list[SrcScope]()
        current = self
        for i, part in enumerate(parts):
            if isinstance(part, int):
                current = current.children[part]
                if isinstance(current, SrcSpan):
                    raise ValueError(
                        f"Path {parts[:i+1]} points to a span, not a scope."
                    )
            else:
                current = next(
                    c
                    for c in current.children
                    if isinstance(c, SrcScope) and c.name == part
                )
            path.append(current)

        return path

    def get_leaf_unit(self) -> ScopeOrSpan:
        """Grab the leaf function or span from a given path-like tree.

        (A span will only be returned if it's not a function body span.)
        """
        scope: SrcScope = self
        while True:
            assert_eq(
                len(scope.children),
                1,
                message=lambda: "This function only works with a path-like tree.",
            )
            child = scope.children[0]
            if isinstance(child, SrcSpan):
                return child
            elif child.kind == "function":
                return child
            else:
                scope = child

    def get_code(self) -> str:
        """Reconstruct the source code using a DFS traversal.

        The result should exactly matches the original code.
        """
        parts = list[str]()

        def rec(scope: ScopeOrSpan):
            if isinstance(scope, SrcScope):
                parts.append(scope.prefix_with_doc().code)
                for child in scope.children:
                    rec(child)
                parts.append(scope.suffix.code)
            else:
                parts.append(scope.code)

        rec(self)
        return "".join(parts)

    def get_all_spans(self) -> Iterable[SrcScope]:
        """Do a DFS traversal and return all the spans as path-like scope trees.

        Each scope tree consists of a single span along with all its ancestor scopes
        and can be used as the unit for retrieval.
        """

        def find_leaf(scope: SrcScope):
            while scope.children:
                new_scope = scope.children[0]
                assert isinstance(new_scope, SrcScope)
                scope = new_scope
            return scope

        def rec(x: ScopeOrSpan, parent: SrcScope) -> Iterable[SrcScope]:
            new_parent = deepcopy(parent)
            # we will append to this leaf
            parent_leaf = find_leaf(new_parent)

            if isinstance(x, SrcSpan):
                parent_leaf.children = (x,)
                yield new_parent
            else:
                for child in x.children:
                    new_scope = copy(x)
                    new_scope.children = ()
                    parent_leaf.children = (new_scope,)
                    yield from rec(child, new_parent)

        parent = copy(self)
        parent.children = ()
        for child in self.children:
            yield from rec(child, parent)

    def merge(self, other: SrcScope) -> SrcScope:
        """Merge two scope trees to share common ancestors.

        This requires the two trees to have the same root scope.
        If you retrieve multiple spans from the same file, you can merge them before
        presenting to the model to reduce the number of tokens.
        """
        assert_eq(self.name, other.name)
        assert_eq(self.range, other.range)
        all_children = [*self.children, *other.children]
        if not all_children:
            return self
        all_children.sort(key=lambda x: x.range)
        new_children = [all_children[0]]
        # merge adjacent scopes if their ranges are equal
        for s1 in all_children[1:]:
            s0 = new_children[-1]
            if (
                isinstance(s0, SrcScope)
                and isinstance(s1, SrcScope)
                and s0.range == s1.range
            ):
                new_children[-1] = s0.merge(s1)
            else:
                new_children.append(s1)
        new = copy(self)
        new.children = tuple(new_children)
        return new

    @staticmethod
    def merge_all(*scopes: SrcScope) -> SrcScope:
        """Merge multiple scope trees into a single one.

        All trees need to share the same root.
        """
        if not scopes:
            raise ValueError("Need to provide at least one scope.")
        scope = scopes[0]
        for other in scopes[1:]:
            scope = scope.merge(other)
        return scope

    def functions(self) -> Iterable[SrcScope]:
        """Iterates through all functions present in this source tree."""
        if self.kind == "function":
            yield self

        for child in self.children:
            if not isinstance(child, SrcScope):
                continue
            if child.kind == "function":
                yield child
            else:
                yield from child.functions()


ScopeOrSpan = Union[SrcScope, SrcSpan]


# to be easily inherited by ScopeTreeParser, this needs to be a dataclass
@dataclass
class TreeSitterParser:
    """An opaque class for parsing source code into a tree-sitter tree."""

    timeout_us: int = 1000 * 1000
    """The timeout (in microseconds) for parsing.

    The default of 1s is sufficient for > 99% of files, so this is mostly used to
    prevent adversarial files from blocking the entire parsing process.
    """

    def __post_init__(self):
        self._parser = ts.Parser()
        self._parser.set_timeout_micros(self.timeout_us)

    def parse_ts_tree(self, code: bytes | str, lang: LanguageID, path: Path) -> ts.Tree:
        """Parse the code as a tree-sitter tree.

        This may raise `ParsingFailedError` when parsing failed.

        Note that we require both a `lang` and a `path` since the underlying parser
        needs more information just than the `LanguageID`.
        """
        if isinstance(code, str):
            code = code.encode()
        ts_lang_name = lang

        if path.suffix in (".tsx", ".jsx"):
            # although we treat .tsx and .jsx files as TypeScript in other parts of
            # static analysis, we need to parse them using the `tsx` tree-sitter parser
            # here in order to avoid unnecessary parsing errors. (e.g., `(<any> x)` is
            # valid TypeScript code but not valid JSX code.)
            ts_lang_name = "tsx"
        elif lang == "javascript":
            # we parse js and ts using the same parser to simplify the downstream code
            # since the two parsers tend to produce syntax trees with minor differences.
            ts_lang_name = "typescript"
        language = get_language(ts_lang_name)
        self._parser.set_language(language)
        try:
            return self._parser.parse(code)
        except Exception as e:
            raise ParsingFailedError("tree-sitter parsing failed.") from e


GlobalTsParser = TreeSitterParser()
"""A global parser to be used as the default."""


@dataclass
class TsParsedFile(MappedCode):
    """Tree-sitter parsed file."""

    code: str
    """The code of the file."""

    path: Path
    """The file path of the source code."""

    lang: LanguageID
    """The language of the source code."""

    ts_tree: ts.Tree
    """The tree-sitter tree"""

    @cached_property
    def code_as_bytes(self) -> bytes:
        return self.code.encode("utf-8")

    @staticmethod
    def parse(
        path: Path,
        lang: LanguageID,
        code: str | bytes,
        parser: TreeSitterParser = GlobalTsParser,
    ) -> TsParsedFile:
        """Parse a file using tree-sitter."""
        tree = parser.parse_ts_tree(code, lang, path)
        if not isinstance(code, str):
            code = decode_bytes(code)
        return TsParsedFile(code, path, lang, tree)


@dataclass
class ScopeTreeParser(TreeSitterParser):
    """An opaque class for parsing source code into a scope tree.

    You should use `parsing.GlobalParser` instead of creating instances of this class
    unless you are using multithreading or asynchronous programming.
    """

    parse_errored_root: bool = False
    """When True, will try to parse the file even if the root
    node is a tree-sitter ERROR (the result may be of poor quality.)
    When False, will raise an error if the root node is an ERROR.

    Typically we want this to be False for indexing tasks (since we don't want to
    index broken signatures/definitions) but want this to be True for completion
    tasks where we have to parse the current file to find out usages.
    """

    merge_empty_spans: bool = True
    """merge spans that contain only whitespace into the previous span or scope."""

    def parse(
        self,
        code: str,
        file_name: str,
        lang: LanguageID,
        ts_tree: ts.Tree | None = None,
    ) -> SrcScope:
        """Parse a source file into a scope tree.

        This may raise `ParsingFailedError` when parsing failed.

        Args:
            code: the content of a source file
            file_name: the file name of the source file
            lang: the LangaugeID of the source code.
            ts_tree: if provided, will reuse this tree to avoid re-parsing.
        """
        if lang not in _scope_tree_supports:
            raise FileTypeNotSupportedError(f"Parsing language {lang} not supported.")
        if ts_tree is None:
            ts_tree = self.parse_ts_tree(code, lang, Path(file_name))

        support = _scope_tree_supports[lang]
        try:
            scope = support.parse_scopes(code, ts_tree, parser=self)
            scope.name = file_name
        except (KeyboardInterrupt, ParsingFailedError):
            raise
        except Exception as e:
            raise ParsingFailedError("Scope tree parsing failed.") from e
        return scope


GlobalScopeParser = ScopeTreeParser()
"""A global parser to be used as the default."""


@dataclass
class ScopeParsedFile(TsParsedFile):
    """A TsParsedFile file with scope tree information."""

    code: str
    """The code of the file."""

    path: Path
    """The file path of the source code."""

    lang: LanguageID
    """The language of the source code."""

    ts_tree: ts.Tree
    """The tree-sitter tree"""

    scope_tree: SrcScope
    """The scope tree of the file."""

    @staticmethod
    def parse(
        path: Path,
        lang: LanguageID,
        code: str | None = None,
        parser: ScopeTreeParser = GlobalScopeParser,
    ) -> ScopeParsedFile:
        """Build a `ScopeParsedFile` object from code.

        If `code` is None, it will be read from `path`.
        May raise `ParsingFailedError`.
        """
        if code is None:
            code = path.read_text()
        code_bytes = code.encode()
        ts_tree = parser.parse_ts_tree(code_bytes, lang, path)
        scope_tree = parser.parse(code, str(path), lang, ts_tree=ts_tree)
        return ScopeParsedFile(code, path, lang, ts_tree, scope_tree)


def show_ts_node(
    node: ts.Node,
    indent: int = 2,
    show_unnamed: bool = True,
    show_locations: bool = True,
    show_locations_as_bytes: bool = False,
    max_level: int = 8,
) -> str:
    """Pretty print a tree-sitter node as a string."""
    segs = list[str]()

    def rec(n: ts.Node, level: int, name: str | None):
        segs.append(indent * level * " ")
        if name is not None:
            segs.append(f"{name}: ")
        content = ""
        if n.type == "identifier":
            content = "{" + repr(n.text.decode() if n.text else "") + "}"
        loc = ""
        if show_locations:
            if show_locations_as_bytes:
                loc = f" @ {n.start_byte} - {n.end_byte}"
            else:
                loc = f" @ {n.start_point} - {n.end_point}"
        segs.append(f"{repr(n.type)}{content}{loc}\n")
        children = n.children
        if children and level >= max_level:
            segs.append(indent * (level + 1) * " ")
            segs.append("...\n")
            return
        for i, c in enumerate(children):
            if show_unnamed or c.is_named:
                fieldname = n.field_name_for_child(i) if c.is_named else None
                rec(c, level + 1, fieldname)

    rec(node, 0, None)
    return "".join(segs)


GlobalParser = ScopeTreeParser()
"""A global parser to be used as the default.

You shouldn't need to create multiple parser instances unless you are using
multithreading or asynchronous programming.
"""


class FileTypeNotSupportedError(ValueError):
    """The file type is not supported by this implementation."""


@dataclass
class ParsingFailedError(Exception):
    """This is thrown when scope tree parsing has failed.

    Parsing can fail due to various reasons. e.g, the underlying tree-sitter library
    may fail sometimes for corrupted files (although this is relatively
    rare), and the scope tree parsing logic is also currently not robust against
    code with certain syntactic errors.

    When this error is thrown, it will be `raise from` the original exception that
    caused the failure.
    """

    message: str


def find_syntax_errors(
    code: str, lang: LanguageID, path: Path, parser: ScopeTreeParser = GlobalParser
) -> list[CharRange]:
    """Find all syntax errors in the given file and return their ranges.

    Note:
    - `code` should be the content of an entire file. Using truncated file contents
    can lead to bad results.
    - sometimes tree-sitter may incorrectly flag a piece of code as error if it
    uses some newly introduced syntax that hasn't been supported by tree-sitter yet.
    """

    tree = parser.parse_ts_tree(code, lang=lang, path=path)
    error_nodes = get_nodes_of_types(tree.root_node, ("ERROR",))
    bmap = ByteMap(code)
    return [bmap.brange_to_crange(_get_brange(n)) for n in error_nodes]


def find_comments(
    code: str, lang: LanguageID, path: Path, parser: ScopeTreeParser = GlobalParser
) -> list[CharRange]:
    """Find all comments in the given file and return their ranges."""
    tree = parser.parse_ts_tree(code, lang=lang, path=path)
    comment_nodes = get_nodes_of_types(tree.root_node, ("comment",))
    bmap = ByteMap(code)
    return [bmap.brange_to_crange(_get_brange(n)) for n in comment_nodes]


def get_nodes_of_types(
    ts_node: ts.Node,
    types: Collection[str],
    max_level: int = 200,
) -> list[ts.Node]:
    """Return all tree sitter nodes of the given types."""
    errors = list[ts.Node]()

    def rec_find(n: ts.Node, level: int):
        if n.type in types:
            errors.append(n)
        if level >= max_level:
            return
        for c in n.children:
            rec_find(c, level + 1)

    rec_find(ts_node, 0)
    return errors


def _check_src_scope(scope: SrcScope):
    """Check if the given scope has valid ranges."""
    crange = scope.range
    for c in scope.children:
        assert crange.contains(c.range), f"{crange=}, {c.range=}, {scope=}"
        if isinstance(c, SrcScope):
            _check_src_scope(c)


def get_scope_by_range(scope: SrcScope, crange: CharRange) -> list[SrcScope]:
    """Get all src scopes containing the given range.

    Return a list of Scopes, from the smallest to the largest.
    """

    if not scope.range.contains(crange):
        return []
    for child in scope.children:
        if isinstance(child, SrcSpan):
            continue
        result = get_scope_by_range(child, crange)
        if result:
            result.append(scope)
            return result
    return [scope]


def compare_asts(
    code1: str, code2: str, lang: str, path: Path, ignore_comments: bool
) -> bool | None:
    """Compare two sources codes by constructing their ASTs and comparing them.

    If lang is a language we cannot parse, returns None.

    The Tree-sitter API does not create AST nodes for whitespace, so this
    comparison ignores whitespace.

    If parsing failed or the recursion becomes too deep, we return None.
    """
    if is_parsing_supported_lang(lang):
        return compare_asts_with_language(
            code1, code2, lang, path=path, ignore_comments=ignore_comments
        )
    return None


def compare_asts_with_language(
    code1: str, code2: str, lang: LanguageID, path: Path, ignore_comments: bool
) -> bool | None:
    """Compare two sources codes by constructing their ASTs and comparing them.

    The Tree-sitter API does not create AST nodes for whitespace, so this
    comparison ignores whitespace.

    If parsing failed or the recursion becomes too deep, we return None.
    """
    max_rec_depth = 500

    def is_comment(node: ts.Node) -> bool:
        """Check if the given node is a comment.

        Note that this is a heuristic, since different languages have different
        type names for comments (e.g., Python uses "comment" and Java uses both
        "line_comment" and "block_comment").
        """
        return "comment" in node.type

    def compare_rec(node1: ts.Node, node2: ts.Node, depth: int) -> bool | None:
        if depth > max_rec_depth:
            return None
        if node1.type != node2.type:
            return False
        children1 = node1.children
        children2 = node2.children
        if not children1 and not children2:
            return node1.text == node2.text
        # For some languages (such as Python's `string` and Rust's `string_literal`),
        # Tree-sitter generates child nodes for the quotes but no child for the
        # text.  Since we cannot ignore unnamed children (as e.g., `+` is unnamed),
        # and we cannot compare the text of inner nodes while ignoring the text of
        # their children (as that would find whitespace differences), we must
        # special-case string nodes.
        if "string" in node1.type:
            return node1.text == node2.text
        if ignore_comments:
            children1 = [c for c in children1 if not is_comment(c)]
            children2 = [c for c in children2 if not is_comment(c)]
        if len(children1) != len(children2):
            return False
        for i, (c1, c2) in enumerate(zip(children1, children2)):
            if node1.field_name_for_child(i) != node2.field_name_for_child(i):
                return False
            result = compare_rec(c1, c2, depth + 1)
            # We store this as a variable since we want to differentiate between False and None.
            if not result:
                return result
        return True

    try:
        ast1 = GlobalTsParser.parse_ts_tree(code1, lang, path)
        ast2 = GlobalTsParser.parse_ts_tree(code2, lang, path)
        return compare_rec(ast1.root_node, ast2.root_node, 0)
    except ParsingFailedError:
        return None


# ---------------------------------------------------------------------------
# --------------------------- internal APIs below ---------------------------

_scope_tree_supports: dict[LanguageID, _ScopeTreeSupport] = {}


def _get_brange(node: ts.Node) -> ByteRange:
    return ByteRange(node.start_byte, node.end_byte)


_space_tab = (" ".encode()[0], "\t".encode()[0])
_newline = "\n".encode()[0]
_newline_cr = "\r\n".encode()


@dataclass
class WhitespaceHandler:
    """A helper class to handle whitespaces."""

    code_bytes: bytes
    """The bytes of the code being parsed."""

    def move_start_left(self, start: int) -> int:
        """Move start leftward to include the leading whitespace (if any)."""
        code_bytes = self.code_bytes
        while start > 0 and code_bytes[start - 1] in _space_tab:
            start -= 1
        return start

    def move_stop_right(self, stop: int) -> int:
        """Move stop rightward to include the trailing newline (if any)."""
        code_bytes = self.code_bytes
        if stop < len(code_bytes) and code_bytes[stop] == _newline:
            stop += 1
        elif stop < len(code_bytes) - 1 and code_bytes[stop : stop + 2] == _newline_cr:
            stop += 2
        return stop

    def extend_range(self, brange: ByteRange) -> ByteRange:
        """Extend the range to include the leading and trailing whitespaces."""
        start = brange.start
        stop = brange.stop
        start = self.move_start_left(start)
        stop = self.move_stop_right(stop)
        return ByteRange(start, stop)


def _get_field_by_name(node: ts.Node, name: FieldName) -> ts.Node | None:
    if isinstance(name, str):
        result = node.child_by_field_name(name)
    elif isinstance(name, int):
        try:
            result = node.named_children[name]
        except IndexError:
            result = None
    elif callable(name):
        result = name(node)
    else:
        assert_never(name)
    return result


def _get_field_by_address(node: ts.Node, address: FieldAddress) -> ts.Node | None:
    if isinstance(address, tuple):
        for part in address:
            next_node = _get_field_by_name(node, part)
            if next_node is None:
                return None
            node = next_node
        return node
    return _get_field_by_name(node, address)


@dataclass(frozen=True)
class _ScopeSpec:
    """The tree-sitter specification for a scope."""

    kind: ScopeKind
    name_field: FieldAddress = "name"
    body_field: FieldAddress = "body"
    braced: bool | Callable[[ts.Node], tuple[int, int]] = False
    """If this node has a {} block.

    If this is a function, it takes in the node and returns the number of child
    nodes that should belong to its prefix and suffix.
    """
    is_leaf: bool = False
    get_target_class_name: Callable[[ts.Node], str] | None = None
    """If the target class is not the syntactic parent, return its name.

    This is only needed when the scope is a class member defined outside of the class.
    e.g., in Go methods are defined outside of the class.
    """


@dataclass(frozen=True)
class _DecorationSpec:
    """The tree-sitter specification for a scope decorator."""

    decorated_field: FieldAddress
    """The address of the decorated scope node."""


@dataclass(frozen=True)
class _JavaLikeDocStrSpec:
    """The tree-sitter specification for a docstring."""

    node_type: TsNodeType


class _PythonDocStrSpec:
    """The tree-sitter specification for a docstring."""


@dataclass
class _ScopeTreeSupport:
    """Supports scope tree parsing for a given langauge.

    See `parse_scopes` for more details about the scoping algorithm.
    """

    lang_id: LanguageID
    """The target programming langauge's id."""

    scope_types: Mapping[TsNodeType, _ScopeSpec]
    """a mapping from tree-sitter node type to scope definition.
    You can use https://tree-sitter.github.io/tree-sitter/playground to learn the
    node types of different languages."""

    scope_decorator_types: Mapping[TsNodeType, _DecorationSpec]
    """a mapping from tree-sitter node type to scope decorator."""

    docstr_def: Union[_JavaLikeDocStrSpec, _PythonDocStrSpec]
    """The definition of the docstring in the target language."""

    seperator: str = ";"
    """An optional seperator between scopes. This is typically a semicolon."""

    def __post_init__(self):
        self._sep_bytes = self.seperator.encode()

    def register(self, lang_id: LanguageID | None = None, override: bool = False):
        """Register self as the scoping support for the given language."""
        lang_id = lang_id or self.lang_id
        if lang_id in _scope_tree_supports and not override:
            raise RuntimeError(f"Scoping support already registered: {lang_id}.")
        _scope_tree_supports[lang_id] = self

    def parse_scopes(
        self,
        code_str: str,
        ts_tree: ts.Tree,
        parser: ScopeTreeParser,
        max_level: int = 200,
        max_subscopes: int = 1_000,
    ) -> SrcScope:
        """Parse source code into a scope tree.

        This works as follows:
        1. identify all scopes in the given code (scopes can be nested,
        and the entire file is always contained in a root scope)
        2. for each scope, any continuous child region that's not already in a scope
        becomes a span. Spans containing only whitespaces are merged with neighbors.

        Args:
            code_str: the source code content to parse.
            parser: the ScopeTreeParser to use.
            merge_empty_spans: merge spans that contain only whitespace into the
                previous span or scope.
            parse_errored_root: if True, will try to parse the scopes even if the root
                node is a tree-sitter ERROR; otherwise, will raise a ParsingFailedError.
            ts_tree: the tree-sitter parse tree.
            max_level: the maximum depth to traverse in `ts_tree`.
            max_subscopes: the maximum number of scopes to build.
        """
        bmap = ByteMap(code_str)
        code_bytes = code_str.encode()
        ws = WhitespaceHandler(code_bytes)
        _extend_start = ws.move_start_left
        _extend_stop = ws.move_stop_right
        _extend_range = ws.extend_range
        num_nodes = 0

        def mkspan(brange: ByteRange):
            """Make a span without the code."""
            start, stop = brange.start, brange.stop
            code = cast(Any, None)  # Delay this for performance.
            return SrcSpan(ByteRange(start, stop), code)

        def prev_end_byte(node: ts.Node) -> int:
            """Get the end byte of the previous sibling."""
            if prev := node.prev_sibling:
                return _extend_stop(prev.end_byte)
            else:
                return _extend_start(node.start_byte)

        def build_scope(
            node: ts.Node,
            scope_def: _ScopeSpec,
            wrapper_node: ts.Node | None,
            level: int,
        ) -> Optional[SrcScope]:
            """Turn given node into a SrcScope.

            Note: all ranges inside this function are (temporarily) `ByteRange`s.

            Args:
                node: the node to build scope from.
                scope_def: the definition of the scope.
                wrapper_node: the outmost wrapper of this scope (if any). In many\
                    languages, decorators are implemented as a wrapper node of\
                    the class node.
                level: the recursion level.
            """
            nonlocal num_nodes
            if num_nodes >= max_subscopes:
                return None
            num_nodes += 1
            main_node = wrapper_node or node
            main_range = (
                ByteRange(0, len(code_bytes))
                if scope_def.kind == "file"
                else _get_brange(main_node)
            )
            body_start = main_range.stop
            if (
                scope_def.braced
                and (next_sib := main_node.next_sibling)
                and len(_get_brange(next_sib)) == 1
                and next_sib.text == self._sep_bytes
            ):
                # add trailing seperator as part of the suffix
                main_range = ByteRange(main_range.start, next_sib.end_byte)
            main_range = _extend_range(main_range)

            body = _get_field_by_address(node, scope_def.body_field)
            # scope body range should include any leading whitespaces
            if body is None:
                body_range = ByteRange.point(body_start)
                body_nodes = []
            elif (
                scope_def.braced
                and (
                    prefix_suffix_counts := (
                        (1, 1)
                        if not callable(scope_def.braced)
                        else scope_def.braced(body)
                    )
                )
                and len(body.children) >= sum(prefix_suffix_counts)
            ):
                prefix_count, suffix_count = prefix_suffix_counts
                left_range = _extend_range(_get_brange(body.children[prefix_count - 1]))
                right_range = _extend_range(
                    _get_brange(body.children[len(body.children) - suffix_count])
                )
                body_start = left_range.stop
                # be careful that right_range.start may be smaller than left_range.stop
                # because `_extend_range` can include additional whitespaces around
                # the curly braces
                body_stop = max(body_start, right_range.start)
                body_range = ByteRange(body_start, body_stop)
                body_nodes = body.children[prefix_count:-suffix_count]
            else:
                body_range = ByteRange(prev_end_byte(body), _extend_stop(body.end_byte))
                body_nodes = body.children

            assert main_range.contains(body_range), f"{main_node=}, {body=}"
            prefix_range, suffix_range = main_range.split_by_range(body_range)
            assert (
                prefix_range is not None and suffix_range is not None
            ), "Splitting on a contained range should return non-None left, right."

            if scope_def.name_field and (
                name_node := _get_field_by_address(node, scope_def.name_field)
            ):
                name = decode_bytes(name_node.text)
                name_range = _get_brange(name_node)
            else:
                name = ""
                name_range = ByteRange.point(node.start_byte)

            # Handle docstrings
            if isinstance(self.docstr_def, _JavaLikeDocStrSpec):
                if (
                    comment := main_node.prev_sibling
                ) and comment.type == self.docstr_def.node_type:
                    # look for more docstring comments
                    while (
                        more_comment := comment.prev_sibling
                    ) and more_comment.type == self.docstr_def.node_type:
                        comment = more_comment
                    doc_range = ByteRange(
                        _extend_start(comment.start_byte), prefix_range.start
                    )
                else:
                    doc_range = ByteRange.point(prefix_range.start)
            elif isinstance(self.docstr_def, _PythonDocStrSpec):
                if (
                    body is not None
                    and len(body.children) >= 2
                    and (comment := body.children[0]).type == "expression_statement"
                    and (comment := comment.children[0]).type == "string"
                ):
                    # we consider the next children as the actual start of the body
                    body_start = _extend_stop(body.children[0].end_byte)
                    doc_range = ByteRange(prefix_range.stop, body_start)
                    body_range = ByteRange(body_start, body_range.stop)
                else:
                    doc_range = ByteRange.point(prefix_range.stop)
            else:
                assert_never(self.docstr_def)

            if not (
                prefix_range.stop == doc_range.start
                or doc_range.stop == prefix_range.start
            ):
                raise AssertionError(
                    "Prefix not next to doc:\n"
                    f"  {main_range=}, {prefix_range=}, {doc_range=}, {body_range=}\n"
                    f"  {node=}\n"
                    f"  code={repr(code_bytes[main_range.to_slice()])}"
                )

            parent_name_override = None
            if scope_def.get_target_class_name:
                parent_name_override = scope_def.get_target_class_name(node)

            if level >= max_level or scope_def.is_leaf:
                # treat entire body as a single span
                subscopes = [mkspan(body_range)]
            else:
                # build child scopes
                subscopes = [s for c in body_nodes if (s := build(c, None, level + 1))]

            # Compute the children list, turning any gaps between subscopes into spans.
            # e.g., if the body range is ByteRange(10,20) and we have two subscopes
            # scope(13, 15) and scope(17, 20), then children list should be
            # [span(10, 13), scope(13, 15), span(15, 17), scope(17, 20)]
            children = list[ScopeOrSpan]()
            remaining_body: ByteRange | None = body_range
            for child_scope in subscopes:
                if not remaining_body:
                    break
                left, remaining_body = remaining_body.split_by_range(child_scope.range)
                if left:
                    children.append(mkspan(left))
                children.append(child_scope)
            if remaining_body:
                children.append(mkspan(remaining_body))

            scope = SrcScope(
                name=name,
                name_range=name_range,
                kind=scope_def.kind,
                parent_name_override=parent_name_override,
                children=tuple(children),
                prefix=mkspan(prefix_range),
                docstr=mkspan(doc_range),
                suffix=mkspan(suffix_range),
            )
            expect_start = min(main_range.start, scope.docstr.range.start)
            assert_eq(scope.range.start, expect_start)
            return scope

        def build(
            node: ts.Node, wrapper_node: ts.Node | None, level: int
        ) -> Optional[SrcScope]:
            """Turn given node into a SrcScope if possible."""
            if scope_def := self.scope_types.get(node.type):
                return build_scope(node, scope_def, wrapper_node, level)
            if dec_def := self.scope_decorator_types.get(node.type):
                # Extend the prefix range of the decorated
                decorated = _get_field_by_address(node, dec_def.decorated_field)
                if decorated is None:
                    # This can be missing in syntactically broken files
                    return None
                else:
                    return build(decorated, wrapper_node or node, level + 1)
            if (
                parser.parse_errored_root
                and node.type == "ERROR"
                and node.parent is None
            ):
                # This is a root node containing syntactic errors, but we still
                # try to turn it into a scope anyway.
                file_scope_def = _ScopeSpec("file", name_field=(), body_field=())
                file_scope = build_scope(node, file_scope_def, wrapper_node, level)
                return file_scope
            return None

        def set_src(span: SrcSpan) -> None:
            """Convert the range into char range and add the source text."""
            start = bmap.byte_to_char(span.range.start)
            stop = bmap.byte_to_char(span.range.stop)
            span.code = code_str[start:stop]
            span.range = CharRange(start, stop)

        def rec_set_src(scope: SrcScope) -> None:
            """Add missing source code and remove empty src spans."""
            children_rev = list[ScopeOrSpan]()
            # going in reversed order since we may want to merge with previous span
            for i in reversed(range(len(scope.children))):
                child = scope.children[i]
                if not child.range:
                    continue  # drop empty ones
                if isinstance(child, SrcScope):
                    rec_set_src(child)
                    children_rev.append(child)
                    continue
                assert isinstance(child, SrcSpan)
                set_src(child)
                if child.code.strip():
                    # keep non-empty spans
                    children_rev.append(child)
                    continue
                # Merge empty span with the previous ScopeOrSpan
                if parser.merge_empty_spans and i > 0:
                    prev_span = scope.children[i - 1]
                    if isinstance(prev_span, SrcScope):
                        prev_span = prev_span.suffix
                    prev_span.range = child.range.merge(prev_span.range)
                else:
                    children_rev.append(child)  # let's keep this in this case
            scope.children = tuple(reversed(children_rev))
            set_src(scope.prefix)
            set_src(scope.suffix)
            set_src(scope.docstr)

        built = build(ts_tree.root_node, wrapper_node=None, level=0)
        if not built:
            cause = AssertionError(
                f"`build` returned None,\n"
                f"Root node:\n{show_ts_node(ts_tree.root_node)}"
            )
            raise ParsingFailedError(
                "Cannot build scope tree from the root node."
            ) from cause
        root_scope = built
        rec_set_src(root_scope)
        return root_scope


def _node_text(node: ts.Node | None) -> str:
    """Get the text of a node as str. Return empty string if node is None."""
    if node is None:
        return ""
    return decode_bytes(node.text)


# ---------------------------------------------------------------------------
# --------------------------- supported langauges ---------------------------

_ScopeTreeSupport(
    lang_id="python",
    scope_types={
        "module": _ScopeSpec(
            "file",
            name_field=(),
            body_field=(),
            braced=False,
        ),
        "class_definition": _ScopeSpec(
            "class",
        ),
        "function_definition": _ScopeSpec(
            "function",
            is_leaf=True,
        ),
    },
    scope_decorator_types={
        "decorated_definition": _DecorationSpec(
            decorated_field="definition",
        ),
    },
    docstr_def=_PythonDocStrSpec(),
).register()


_ScopeTreeSupport(
    lang_id="java",
    scope_types={
        "program": _ScopeSpec(
            "file",
            name_field=(),
            body_field=(),
        ),
        "class_declaration": _ScopeSpec(
            "class",
            braced=True,
        ),
        "method_declaration": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
        "constructor_declaration": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
        "enum_declaration": _ScopeSpec(
            "class",
            braced=True,
        ),
    },
    scope_decorator_types={},
    docstr_def=_JavaLikeDocStrSpec("block_comment"),
).register()


_ScopeTreeSupport(
    lang_id="cpp",
    scope_types={
        "translation_unit": _ScopeSpec(
            "file",
            name_field=(),
            body_field=(),
        ),
        "class_specifier": _ScopeSpec(
            "class",
            braced=True,
        ),
        "function_definition": _ScopeSpec(
            "function",
            name_field=("declarator", "declarator"),
            braced=True,
        ),
        "namespace_definition": _ScopeSpec(
            "class",
            braced=True,
        ),
    },
    scope_decorator_types={
        "template_declaration": _DecorationSpec(
            decorated_field=-1,  # last child is the decorated
        ),
        "field_declaration": _DecorationSpec(decorated_field="type"),
    },
    docstr_def=_JavaLikeDocStrSpec("comment"),
).register()


typescript_support = _ScopeTreeSupport(
    lang_id="typescript",
    scope_types={
        "program": _ScopeSpec(
            "file",
            name_field=(),
            body_field=(),
        ),
        "class_declaration": _ScopeSpec(
            "class",
            braced=True,
        ),
        "interface_declaration": _ScopeSpec(
            "class",
            braced=True,
        ),
        "abstract_class_declaration": _ScopeSpec(
            "class",
            braced=True,
        ),
        "enum_declaration": _ScopeSpec(
            "class",
            braced=True,
        ),
        "function_declaration": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
        "method_definition": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
        "internal_module": _ScopeSpec(
            "class",
            braced=True,
        ),
        "type_alias_declaration": _ScopeSpec(
            "class",
            body_field="value",
            braced=True,
        ),
    },
    scope_decorator_types={
        "export_statement": _DecorationSpec(decorated_field="declaration"),
        "expression_statement": _DecorationSpec(decorated_field=0),
    },
    docstr_def=_JavaLikeDocStrSpec("comment"),
)
# We use the tsx parser for javascript and typescript.
typescript_support.register(lang_id="typescript")
typescript_support.register(lang_id="javascript")


def _get_go_method_receiver_name(node: ts.Node) -> str:
    """Return a Go method's receiver name.

    A Go method typically has the following structure:
    method_declaration
        receiver: parameter_list
            parameter_declaration
                name: identifier
                type: type_identifier | pointer_type
    """
    type_node = _get_field_by_address(node, ("receiver", 0, "type"))
    type_text = _node_text(type_node)
    # remove all preceding '*'
    return type_text.lstrip("*")


def _get_go_type_body(n: ts.Node) -> ts.Node | None:
    if n.type == "interface_type":
        return n
    return _get_field_by_name(n, -1)


def _get_go_type_bracing(n: ts.Node) -> tuple[int, int]:
    if n.type == "interface_type":
        return (2, 1)
    return (1, 1)


_ScopeTreeSupport(
    lang_id="go",
    scope_types={
        "source_file": _ScopeSpec(
            "file",
            name_field=(),
            body_field=(),
        ),
        "type_declaration": _ScopeSpec(
            "class",
            name_field=(-1, "name"),
            # Structs look like this:
            # type_declaration
            #   type_spec
            #     type
            #       "struct"
            #       field_declaration_list
            #         "{""
            #         ... Various fields
            #         "}"
            # Interfaces look like this:
            # type_declaration
            #   type_spec
            #     type
            #       "interface"
            #       "{"
            #       ... Various methods
            #       "}"
            # Note that structs have an extra layer of nesting, so we use the
            # `field_declaration_list` as the body.  Interfaces do not, so we
            # use the `type` but must get an extra child of prefix to get the
            # opening brace.
            body_field=(-1, "type", _get_go_type_body),
            braced=_get_go_type_bracing,
        ),
        "function_declaration": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
        "method_declaration": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
            get_target_class_name=_get_go_method_receiver_name,
        ),
        "method_spec": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
    },
    scope_decorator_types={},
    docstr_def=_JavaLikeDocStrSpec("comment"),
).register()

_ScopeTreeSupport(
    lang_id="rust",
    scope_types={
        "source_file": _ScopeSpec(
            "file",
            name_field=(),
            body_field=(),
        ),
        "impl_item": _ScopeSpec(
            "class",
            name_field="type",  # FIXME(jiayi): impl type can be structured types
            body_field="body",
            braced=True,
        ),
        "struct_item": _ScopeSpec(
            "class",
            braced=True,
        ),
        "enum_item": _ScopeSpec(
            "class",
            braced=True,
        ),
        "trait_item": _ScopeSpec(
            "class",
            braced=True,
        ),
        "function_item": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
        "function_signature_item": _ScopeSpec(
            "function",
            is_leaf=True,
            braced=True,
        ),
        "mod_item": _ScopeSpec(
            "class",
            braced=True,
        ),
        "enum_variant": _ScopeSpec(
            "class",
            braced=True,
        ),
    },
    scope_decorator_types={},
    docstr_def=_JavaLikeDocStrSpec("line_comment"),
).register()

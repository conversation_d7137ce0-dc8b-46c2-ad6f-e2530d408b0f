"""Unit tests for the common functions.

pytest research/static_analysis/tests/test_common.py
"""

import tree_sitter
from tree_sitter_languages import get_language

from base.static_analysis.common import (
    deduplicate,
    get_ts_node_sibling,
    iter_ts_nodes_via_bfs,
)


def test_get_ts_node_sibling():
    """Test the get_ts_node_sibling func."""
    file1 = """
    def foo(x, y):
        return x + y
    """
    parser = tree_sitter.Parser()
    parser.set_language(get_language("python"))
    tree = parser.parse(file1.encode())
    target_node = None
    for node in iter_ts_nodes_via_bfs(tree):
        if node.type == ":":
            target_node = node
    assert target_node is not None
    assert target_node == get_ts_node_sibling(target_node, 0)

    prev_node = get_ts_node_sibling(target_node, -1)
    assert prev_node is not None
    assert prev_node.type == "parameters"

    prev_prev_node = get_ts_node_sibling(target_node, -2)
    assert prev_prev_node is not None
    assert prev_prev_node.type == "identifier"

    next_node = get_ts_node_sibling(target_node, 1)
    assert next_node == target_node.next_sibling


def test_deduplicate():
    assert deduplicate([]) == []
    assert deduplicate(["a", "b", "c"]) == ["a", "b", "c"]
    assert deduplicate([1, 2, 3, 2, 1]) == [1, 2, 3]
    assert deduplicate([0, 1, 2, 3, 4, 5], key=lambda x: x // 2) == [0, 2, 4]

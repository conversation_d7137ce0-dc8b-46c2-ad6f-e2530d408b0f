# pylint: disable=protected-access
"""Client for the external augment API."""

from __future__ import annotations

import base64
import imghdr
import logging
import time
import urllib.parse
import uuid
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import IntEnum
from json import JSONDecodeError, loads
from pathlib import Path
from typing import Any, Iterable, Sequence, Tuple, TypeAlias, TypeVar

import google.protobuf.message
import requests
from dateutil.parser import parse
from google.protobuf.json_format import MessageToDict, ParseDict

from base.blob_names.python.blob_names import get_blob_name
from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    ImageFormatType,
)

from services.api_proxy import public_api_pb2


class AgentHistoryUpdateType(IntEnum):
    """Enum for agent history update types."""

    AGENT_HISTORY_UNSPECIFIED = 0
    AGENT_HISTORY_EXCHANGE = 1
    AGENT_HISTORY_EXCHANGE_UPDATE = 2
    AGENT_HISTORY_AGENT_STATUS = 3


class AgentWorkspaceUpdateType(IntEnum):
    """Enum for agent workspace update types."""

    AGENT_WORKSPACE_UPDATE_TYPE_UNSPECIFIED = 0
    AGENT_WORKSPACE_UPDATE_INTERRUPT = 1
    AGENT_WORKSPACE_UPDATE_CHAT_REQUEST = 2


GetModelsResponse: TypeAlias = public_api_pb2.GetModelsResponse
FeatureFlags: TypeAlias = public_api_pb2.GetModelsResponse.FeatureFlags
Language: TypeAlias = public_api_pb2.Language
Model: TypeAlias = public_api_pb2.Model
UserTier: TypeAlias = public_api_pb2.GetModelsResponse.UserTier
RemoteToolId: TypeAlias = public_api_pb2.RemoteToolId
ToolAvailabilityStatus: TypeAlias = public_api_pb2.ToolAvailabilityStatus
ToolSafety: TypeAlias = public_api_pb2.ToolSafety
RemoteAgentStatus: TypeAlias = public_api_pb2.RemoteAgentStatus


def sec(dt: datetime):
    return int((dt - datetime(1970, 1, 1, tzinfo=dt.tzinfo)).total_seconds())


def nsec(dt: datetime):
    return dt.microsecond * 1000


class ClientException(Exception):
    """Exception thrown if a request with the augment client failed."""

    def __init__(self, request_id: uuid.UUID, response: requests.Response):
        self.request_id = request_id
        self.response = response

    def is_client_error(self) -> bool:
        """Returns true if and only if the HTTP status code indicates a client error.

        Client error here refers to error where the client provided invalid arguments or other
        cases where the error can go away by making different requests.
        """
        return self.response.status_code >= 400 and self.response.status_code < 500


@dataclass
class CompletionItem:
    """One option provided to VSCode, containing text and associated data."""

    text: str
    skipped_suffix: str
    suffix_replacement_text: str
    filter_score: float | None = None


@dataclass
class CompleteResponse:
    """Class returned by a completion call."""

    text: str
    completion_items: list[CompletionItem]
    unknown_memory_names: list[str]
    request_id: uuid.UUID
    checkpoint_not_found: bool
    suggested_prefix_char_count: int
    suggested_suffix_char_count: int


@dataclass
class TextReplacement:
    description: str
    path: str
    text: str
    start_line: int
    end_line: int
    old_text: str
    sequence_id: int
    old_blob_name: str | None = None


@dataclass
class UserSteeringExchange:
    request_message: str
    summary: str
    replacements: list[TextReplacement]
    request_id: str


@dataclass
class EditResponse:
    """Class returned by an edit call."""

    request_id: str
    """Request id for the edit request."""

    text: str
    """The text to replace the selected region."""

    unknown_blob_names: list[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool
    """True if the blobs checkpoint id is not available."""


@dataclass
class InstructionResponse:
    """Class returned by an instruction call."""

    request_id: str
    """Request id for the instruction request."""

    text: str
    """The text to replace the selected region."""

    unknown_blob_names: list[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool
    """True if the blobs checkpoint id is not available."""

    replacement_text: str | None = None

    replacement_start_line: int | None = None

    replacement_end_line: int | None = None

    replacement_old_text: str | None = None

    replacement_sequence_id: int | None = None


@dataclass
class IncorporatedExternalSource:
    # name of the source that was used.
    source_name: str

    # The link to the external source.
    link: str | None = None


@dataclass
class WorkspaceFileChunk:
    char_start: int
    char_end: int
    blob_name: str


@dataclass
class ChatResponse:
    """Class returned by an chat call."""

    request_id: str
    """Request id for the chat request."""

    text: str
    """The text to replace the selected region."""

    unknown_blob_names: list[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool
    """True if the blobs checkpoint id is not available."""

    workspace_file_chunks: list[WorkspaceFileChunk] | None = None

    incorporated_external_sources: list[IncorporatedExternalSource] | None = None

    nodes: list[ChatResultNode] | None = None

    stop_reason: str | None = None


@dataclass
class NextEditResponse:
    """Class returned by a next edit call."""

    request_id: str
    """Request id for the next edit request."""

    next_edit: dict[str, Any] | None = None
    """The result of the next edit model."""

    unknown_blob_names: list[str] | None = None
    """List of memory names not known to the server."""

    checkpoint_not_found: bool = False
    """Whether the blob checkpoint was not found."""


@dataclass
class VCSChange:
    """Class containing information about a VCS change."""

    working_directory_changes: list[
        Any
    ]  # leaving unspecified for now. (not programmatically using it)
    commits: list[Any]


@dataclass
class FindMissingResponse:
    """Class returned by a find_missing call."""

    unknown_memory_names: list[str]
    nonindexed_blob_names: list[str]


@dataclass
class CompletionResolution:
    """Class that describes the resolution (acceptance or rejection) of a code completion."""

    request_id: str
    emit_time_sec: int
    emit_time_nsec: int
    resolve_time_sec: int
    resolve_time_nsec: int
    accepted_idx: int


@dataclass(frozen=True)
class UploadContent:
    """Class that describes the content to be memorized."""

    content: str
    path_name: str


@dataclass(frozen=True)
class BlobsJson:
    """Describes the blob names using a starting checkpoint id and delta."""

    checkpoint_id: str | None
    added_blobs: Sequence[str]
    deleted_blobs: Sequence[str]

    def sorted(self) -> BlobsJson:
        """Returns a copy of this object with the added and deleted blobs sorted."""
        return BlobsJson(
            self.checkpoint_id,
            sorted(self.added_blobs),
            sorted(self.deleted_blobs),
        )


@dataclass(frozen=True)
class CheckpointBlobsRequest:
    """Class that describes the blob names to be checkpointed."""

    blobs: BlobsJson


@dataclass(frozen=True)
class CheckpointBlobsResponse:
    """Response to a checkpoint request with the new checkpoint id."""

    new_checkpoint_id: str


@dataclass(frozen=True)
class Exchange:
    """Describes an exchange between the user and the model (a turn in a conversation)."""

    request_message: str
    response_text: str | None
    request_id: uuid.UUID | None = None


@dataclass(frozen=True)
class ExternalSource:
    id: str
    name: str
    title: str
    source_type: str


@dataclass(frozen=True)
class SaveChatResponse:
    uuid: str
    url: str


# TODO(AU-4886): Consider using dataclass_json with original Exchange
@dataclass(frozen=True)
class ExchangeJson:
    """An intermediate representation of an exchange, useful for json encoding."""

    request_message: str
    response_text: str | None
    request_id: str | None

    def to_exchange(self) -> Exchange:
        return Exchange(
            request_message=self.request_message,
            response_text=self.response_text,
            request_id=uuid.UUID(self.request_id) if self.request_id else None,
        )

    @staticmethod
    def from_exchange(exchange: Exchange) -> ExchangeJson:
        return ExchangeJson(
            request_message=exchange.request_message,
            response_text=exchange.response_text,
            request_id=str(exchange.request_id) if exchange.request_id else None,
        )


@dataclass(frozen=True)
class Retry:
    """Class that describes the retry policy."""

    # Number of times to retry if the first request fails.
    retry_count: int

    # Time to sleep between retries in seconds.
    retry_sleep: float


@dataclass(frozen=True)
class PerFileChangeStats:
    """Stats of a changed file for a repository diff."""

    path: str
    """Path of the file."""

    insertion_count: int
    """Number of insertions in the file."""

    deletion_count: int
    """Number of deletions in the file."""

    old_path: str
    """Path of the old file, if the file is renamed or copied."""

    def __str__(self):
        string = f"+{self.insertion_count} -{self.deletion_count} {self.path}"
        if self.old_path:
            string += f" -> {self.old_path}"
        return string


@dataclass(frozen=True)
class PerTypeChangedFileStats:
    """Stats of changed files of a certain type for a repository diff."""

    changed_file_count: int = 0
    """Number of changed files of this type."""

    per_file_change_stats_head: list[PerFileChangeStats] = field(default_factory=list)
    """Stats for the first changed files of this type.

    It is necessary to split into head and tail to reduce frontend delay in getting the
    stats for large diffs. Showing the model head and tail helps the model understand
    the overall change a bit better.
    """

    per_file_change_stats_tail: list[PerFileChangeStats] = field(default_factory=list)
    """Stats for the last changed files of this type.

    It is necessary to split into head and tail to reduce frontend delay in getting the
    stats for large diffs. Showing the model head and tail helps the model understand
    the overall change a bit better.
    """

    def __post_init__(self):
        assert self.changed_file_count >= len(self.per_file_change_stats_head) + len(
            self.per_file_change_stats_tail
        )

    def __str__(self):
        string = f"{self.changed_file_count} files"
        for per_file_change_stats in self.per_file_change_stats_head:
            string += f"\n    {str(per_file_change_stats)}"
        if self.per_file_change_stats_tail:
            string += "\n    ..."
            for per_file_change_stats in self.per_file_change_stats_tail:
                string += f"\n    {str(per_file_change_stats)}"
        return string


@dataclass(frozen=True)
class ChangedFileStats:
    """Stats of changed files for a repository diff."""

    added_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of added files."""

    broken_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of broken files."""

    copied_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of copied files."""

    deleted_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of deleted files."""

    modified_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of modified files."""

    renamed_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of renamed files."""

    unmerged_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of unmerged files."""

    unknown_file_stats: PerTypeChangedFileStats = field(
        default_factory=PerTypeChangedFileStats
    )
    """Stats of unknown files."""

    def __str__(self):
        string = "ChangedFileStats:"
        for change_type, stats in [
            ("A", self.added_file_stats),
            ("C", self.copied_file_stats),
            ("D", self.deleted_file_stats),
            ("M", self.modified_file_stats),
            ("R", self.renamed_file_stats),
            ("U", self.unmerged_file_stats),
            ("X", self.unknown_file_stats),
            ("B", self.broken_file_stats),
        ]:
            if stats.changed_file_count == 0:
                continue
            string += f"\n    {change_type}: {stats}"
        return string


ProtoMsg = TypeVar("ProtoMsg", bound=google.protobuf.message.Message)


@dataclass(frozen=True)
class RemoteToolInfo:
    """Information about a remote tool."""

    tool_definition: dict
    """The tool definition containing name, description, and input schema."""

    remote_tool_id: int
    """The RemoteToolId enum value for the tool."""

    availability_status: int
    """The availability status of the tool (available, user config required, etc.)."""

    tool_safety: int
    """The safety level of the tool (safe, unsafe, or needs checking)."""


@dataclass(frozen=True)
class RunRemoteToolResult:
    """Result of running a remote tool."""

    tool_output: str
    """The output from the tool that will be shown to the model."""

    tool_result_message: str
    """A description of what the tool did, for logging purposes."""

    is_error: bool
    """Whether the tool execution resulted in an error."""

    status: int
    """The status of the tool execution."""


@dataclass(frozen=True)
class AgentWorkspaceInterrupt:
    """Interrupt request for a remote agent workspace."""

    pass


@dataclass(frozen=True)
class AgentWorkspaceChatRequest:
    """Chat request for a remote agent workspace."""

    request_details: dict
    """The details of the chat request."""


@dataclass(frozen=True)
class AgentWorkspaceStreamUpdate:
    """Update for a remote agent workspace stream."""

    type: AgentWorkspaceUpdateType
    """The type of the update."""

    sequence_id: int
    """The sequence ID of the update."""

    interrupt: AgentWorkspaceInterrupt | None = None
    """The interrupt request, if the type is AGENT_WORKSPACE_UPDATE_INTERRUPT."""

    chat_request: AgentWorkspaceChatRequest | None = None
    """The chat request, if the type is AGENT_WORKSPACE_UPDATE_CHAT_REQUEST."""


@dataclass(frozen=True)
class AgentWorkspaceStreamResponse:
    """Response from a remote agent workspace stream."""

    updates: list[AgentWorkspaceStreamUpdate]
    """The updates from the remote agent workspace."""


@dataclass(frozen=True)
class RemoteAgentChatResponse:
    """Response from a remote agent chat request."""

    remote_agent_id: str
    """The ID of the remote agent."""

    nodes: list[ChatResultNode]
    """The response nodes from the remote agent."""


@dataclass(frozen=True)
class CreateRemoteAgentResponse:
    """Response from creating a remote agent."""

    remote_agent_id: str
    """The ID of the created remote agent."""

    status: str
    """The status of the remote agent."""


@dataclass
class GithubCommitRef:
    """Reference to a GitHub commit."""

    repository_url: str
    git_ref: str
    patch: str | None = None


@dataclass
class RemoteAgentWorkspaceSetup:
    """Setup for a remote agent workspace.

    Note: In the proto definition, this has a oneof field 'starting_files' with
    a single option 'github_commit_ref'. In this dataclass, we represent it as
    a field 'github_commit_ref' inside the 'starting_files' field.
    """

    # This is a simplified representation of the oneof field in the proto
    # The actual proto has a oneof field 'starting_files' that can contain 'github_commit_ref'
    starting_files: dict = field(default_factory=dict)


@dataclass
class ChangedFile:
    """Information about a file changed by a remote agent."""

    old_path: str
    new_path: str
    change_type: int  # FileChangeType enum value
    old_contents: str
    new_contents: str


@dataclass
class RemoteAgentExchange:
    """A single exchange between a user and a remote agent."""

    exchange: dict  # The Exchange object
    changed_files: list[ChangedFile] = field(default_factory=list)
    sequence_id: int = 0
    turn_summary: str | None = None


@dataclass
class RemoteAgent:
    """Information about a remote agent."""

    remote_agent_id: str
    workspace_setup: RemoteAgentWorkspaceSetup
    status: RemoteAgentStatus
    started_at: datetime | None = None
    updated_at: datetime | None = None
    session_summary: str = ""
    turn_summaries: list[str] = field(default_factory=list)


class AugmentClient:
    """Python client to issue requests against an Augment API endpoint."""

    def __init__(
        self,
        url: str,
        token: str,
        timeout: int = 60,
        retry_count: int = 2,
        retry_sleep: float = 0.1,
        session_id: uuid.UUID | None = None,
        user_agent: str | None = None,
        verify: bool = True,
    ):
        """Initialize the client.

        Args:
            url: The URL of the Augment API.
            token: The API token to use for authentication.
            timeout: The timeout for requests to the remote system in seconds.
            retry_count: Number of times to retry if the first request fails.
            retry_sleep: Time to sleep between retries in seconds.
            session_id: The session id to use for requests.
            user_agent: The user agent to use for requests to the remote system.
            verify: Whether to verify the SSL certificate. Never set this to False for production.
        """

        self.url = url
        self.token = token
        self.timeout = timeout
        self.retry_count = retry_count
        self.retry_sleep = retry_sleep
        self.request_session_id = session_id if session_id else uuid.uuid4()
        self.last_request_id: uuid.UUID | None = None
        # see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/User-Agent
        self.user_agent = user_agent if user_agent else "api_proxy_client/0 (Python)"
        self.verify = verify

    def __repr__(self):
        return f"AugmentClient(url={self.url}, request_session_id={self.request_session_id}, last_request_id={self.last_request_id})"

    def _post(
        self,
        url_suffix: str,
        json: Any,
        retry_policy: Retry | None = None,
        headers: dict[str, str] | None = None,
        timeout: int | None = None,
    ) -> Tuple[requests.Response, uuid.UUID]:
        """Post with build-in retry loop to hide small service outages."""
        if not timeout:
            timeout = self.timeout

        url = urllib.parse.urljoin(self.url, url_suffix)
        response = None
        request_id = None

        retry_policy = (
            retry_policy if retry_policy else Retry(self.retry_count, self.retry_sleep)
        )
        for retry in range(retry_policy.retry_count + 1):
            try:
                if "request_id" in json:
                    request_id = json["request_id"]
                else:
                    request_id = uuid.uuid4()
                logging.debug(
                    "Posting to %s request id %s retry %d",
                    url_suffix,
                    request_id,
                    retry,
                )

                self.last_request_id = request_id
                request_headers = {
                    "authorization": f"Bearer {self.token}",
                    "x-request-id": str(request_id),
                    "x-request-session-id": str(self.request_session_id),
                    "user-agent": self.user_agent,
                    "x-api-version": "1",  # see ApiVersion in services/api_proxy/public_api.proto
                }
                if headers:
                    request_headers.update(headers)
                response = requests.post(
                    url,
                    json=json,
                    headers=request_headers,
                    timeout=timeout,
                    verify=self.verify,
                )
                if response.status_code == 429:
                    logging.debug(
                        "Rate limited: %s", response.headers.get("Retry-After")
                    )
                if str(response.status_code).startswith("5"):
                    time.sleep(retry_policy.retry_sleep)
                    continue
                return (response, request_id)
            except (
                requests.exceptions.SSLError,
                requests.exceptions.ConnectTimeout,
                requests.exceptions.ReadTimeout,
            ) as e:
                logging.warning(
                    "Posting to %s request_id %s failed with exception: %s",
                    url_suffix,
                    request_id,
                    e,
                )
                time.sleep(self.retry_sleep)
                continue
        # return the last response we got
        assert response is not None
        assert request_id is not None
        return (response, request_id)

    def _post_stream(
        self,
        url_suffix: str,
        json: Any,
        headers: dict[str, str] | None = None,
        timeout: int | None = None,
    ) -> Tuple[requests.Response, uuid.UUID]:
        if not timeout:
            timeout = self.timeout

        url = urllib.parse.urljoin(self.url, url_suffix)
        response = None
        request_id = None

        if "request_id" in json:
            request_id = json["request_id"]
        else:
            request_id = uuid.uuid4()
        logging.debug(
            "Posting to %s request id %s",
            url_suffix,
            request_id,
        )

        self.last_request_id = request_id
        request_headers = {
            "accept": "text/event-stream",
            "authorization": f"Bearer {self.token}",
            "x-request-id": str(request_id),
            "x-request-session-id": str(self.request_session_id),
            "user-agent": self.user_agent,
            "x-api-version": "1",  # see ApiVersion in services/api_proxy/public_api.proto
        }
        if headers:
            request_headers.update(headers)
        response = requests.post(
            url,
            json=json,
            headers=request_headers,
            timeout=timeout,
            stream=True,
            verify=self.verify,
        )
        if response.status_code == 429:
            logging.debug("Rate limited: %s", response.headers.get("Retry-After"))
        return (response, request_id)

    def post_proto(
        self,
        url_suffix: str,
        proto_in: google.protobuf.message.Message,
        proto_out: ProtoMsg,
        include_default_value_fields: bool = True,
        retry_policy: Retry | None = None,
        headers: dict[str, str] | None = None,
        timeout: int | None = None,
    ) -> Tuple[ProtoMsg, uuid.UUID]:
        """Post an HTTP request to given URL endpoint consisting of the given proto input message.
        Decode the response into the given proto output message. Supports non-streaming rpcs.

        Args:
            url_suffix: The URL suffix to post to.
            proto_in: The input proto message.
            proto_out: The output proto message.
            include_default_value_fields: Whether to include default value fields in the JSON.
            retry_policy: The retry policy to use; if not provided, use client instance default
            headers: Additional headers to use.
            timeout: The timeout to use; if not provided, use client instance default

        Returns:
            A tuple of the output proto message and the request id.
            The output message is the same object as the one passed in as `proto_out`.
        """

        # Linter complains about `including_default_value_fields` not existing, but
        # it's present in every version of the library I could find.
        json_in = MessageToDict(
            proto_in,
            including_default_value_fields=include_default_value_fields,  # type: ignore
            use_integers_for_enums=True,
            preserving_proto_field_name=True,
        )
        response, request_id = self._post(
            url_suffix, json_in, retry_policy, headers, timeout
        )
        if not response.ok:
            raise ClientException(request_id, response)
        return ParseDict(
            response.json(), proto_out, ignore_unknown_fields=True
        ), request_id

    def memorize(
        self,
        content: str,
        path: str,
        blob_name: str | None = None,
        retry_policy: Retry | None = None,
    ) -> str:
        """Memorizes the content and returns the matching object name.

        memorize and the /memorize endpoint should not longer be used.
        """
        data = {"model": "", "t": content, "path": path}
        if blob_name:
            data["blob_name"] = blob_name
        response, request_id = self._post(
            "memorize", json=data, retry_policy=retry_policy
        )
        logging.debug("Memorize finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        assert "mem_object_name" in j
        return j["mem_object_name"]

    def batch_upload(
        self, blobs: Sequence[UploadContent], retry_policy: Retry | None = None
    ) -> list[str]:
        """Uploads the contents and returns the matching blob names."""
        data = {
            "blobs": [
                {"path": blob.path_name, "content": blob.content} for blob in blobs
            ]
        }
        response, request_id = self._post(
            "batch-upload", json=data, retry_policy=retry_policy
        )
        logging.debug("Upload finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        assert "blob_names" in j
        return j["blob_names"]

    def find_missing(
        self,
        model_name: str,
        memory_object_names: Sequence[str],
        retry_policy: Retry | None = None,
    ) -> FindMissingResponse:
        """Find missing memories.

        Returns a 2-element tuple:
          0: unknown_memory_names: list of blob (memory) names that are completely unknown
          1: nonindexed_blob_names: list of blob names that aren't indexed for the given model
        """
        response, request_id = self._post(
            "find-missing",
            json={"model": model_name, "mem_object_names": memory_object_names},
            retry_policy=retry_policy,
        )
        logging.debug("FindMissing finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return FindMissingResponse(
            unknown_memory_names=j["unknown_memory_names"],
            nonindexed_blob_names=j["nonindexed_blob_names"],
        )

    def get_models(
        self, retry_policy: Retry | None = None
    ) -> public_api_pb2.GetModelsResponse:
        """Returns the list of all supported models.

        The models might not be ready at any given point, so calls
        might return 503.
        """
        response, request_id = self._post(
            "get-models", json={}, retry_policy=retry_policy
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return ParseDict(
            j, public_api_pb2.GetModelsResponse(), ignore_unknown_fields=True
        )

    def resolve_completions(
        self, resolutions: list[CompletionResolution], retry_policy: Retry | None = None
    ) -> None:
        """Report on the resolution of the given list of completions."""
        resolutions_as_dicts = [asdict(resolution) for resolution in resolutions]
        response, request_id = self._post(
            "resolve-completions",
            json={"resolutions": resolutions_as_dicts},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)

    def resolve_edit(
        self,
        request_id: str,
        emit_time: str,
        resolve_time: str,
        is_accepted: bool,
        annotated_text: str,
        annotated_instruction: str,
        retry_policy: Retry | None = None,
    ) -> None:
        """Resolve the given edit."""

        emit_time_dt = parse(emit_time)
        resolve_time_dt = parse(resolve_time)

        json = {
            "request_id": request_id,
            "emit_time_sec": sec(emit_time_dt),
            "emit_time_nsec": nsec(emit_time_dt),
            "resolve_time_sec": sec(resolve_time_dt),
            "resolve_time_nsec": nsec(resolve_time_dt),
            "is_accepted": is_accepted,
            "annotated_text": annotated_text,
            "annotated_instruction": annotated_instruction,
        }
        logging.debug("Resolve Edit request id %s", request_id)

        response, _ = self._post("resolve-edit", json=json, retry_policy=retry_policy)
        logging.debug("Resolve Edit finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(uuid.UUID(request_id), response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def resolve_instruction(
        self,
        request_id: str,
        emit_time: str,
        resolve_time: str,
        is_accepted_chunks: list[bool],
        is_accept_all: bool,
        is_reject_all: bool,
        retry_policy: Retry | None = None,
    ) -> None:
        """Resolve the given instruction."""

        emit_time_dt = parse(emit_time)
        resolve_time_dt = parse(resolve_time)

        json = {
            "request_id": request_id,
            "emit_time_sec": sec(emit_time_dt),
            "emit_time_nsec": nsec(emit_time_dt),
            "resolve_time_sec": sec(resolve_time_dt),
            "resolve_time_nsec": nsec(resolve_time_dt),
            "is_accepted_chunks": is_accepted_chunks,
            "is_accept_all": is_accept_all,
            "is_reject_all": is_reject_all,
        }
        logging.debug("Resolve Instruction request id %s", request_id)

        response, _ = self._post(
            "resolve-instruction", json=json, retry_policy=retry_policy
        )
        logging.debug(
            "Resolve Instruction finished: %s %s", request_id, response.status_code
        )
        if not response.ok:
            raise ClientException(uuid.UUID(request_id), response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def resolve_smart_paste(
        self,
        request_id: str,
        is_accepted_chunks: list[bool],
        is_accept_all: bool,
        is_reject_all: bool,
        initial_request_time: str,
        stream_finish_time: str,
        apply_time: str,
        resolve_time: str,
        retry_policy: Retry | None = None,
    ) -> None:
        """Resolve the given smart paste."""

        initial_request_time_dt = parse(initial_request_time)
        stream_finish_time_dt = parse(stream_finish_time)
        apply_time_dt = parse(apply_time)
        resolve_time_dt = parse(resolve_time)

        json = {
            "request_id": request_id,
            "is_accepted_chunks": is_accepted_chunks,
            "is_accept_all": is_accept_all,
            "is_reject_all": is_reject_all,
            "initial_request_time_sec": sec(initial_request_time_dt),
            "initial_request_time_nsec": nsec(initial_request_time_dt),
            "stream_finish_time_sec": sec(stream_finish_time_dt),
            "stream_finish_time_nsec": nsec(stream_finish_time_dt),
            "apply_time_sec": sec(apply_time_dt),
            "apply_time_nsec": nsec(apply_time_dt),
            "resolve_time_sec": sec(resolve_time_dt),
            "resolve_time_nsec": nsec(resolve_time_dt),
        }
        logging.debug("Resolve Smart paste request id %s", request_id)

        response, _ = self._post(
            "resolve-smart-paste", json=json, retry_policy=retry_policy
        )
        logging.debug(
            "Resolve Smart Paste finished: %s %s", request_id, response.status_code
        )
        if not response.ok:
            raise ClientException(uuid.UUID(request_id), response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def chat_feedback(
        self, request_id: str, rating: int, note: str, retry_policy: Retry | None = None
    ) -> None:
        """Report chat feedback."""
        json = {"request_id": request_id, "rating": rating, "note": note}
        response, _ = self._post(
            "chat-feedback",
            json=json,
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(uuid.UUID(request_id), response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def checkpoint_blobs(
        self, blobs: BlobsJson, retry_policy: Retry | None = None
    ) -> str:
        """Return the new checkpoint id for the given blobs."""
        blobs = blobs.sorted()
        response, request_id = self._post(
            "checkpoint-blobs",
            json={"blobs": asdict(blobs)},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return j["new_checkpoint_id"]

    def report_error(
        self,
        original_request_id: str | None,
        sanitized_message: str,
        stack_trace: str,
        diagnostics: list[tuple[str, str]],
        retry_policy: Retry | None = None,
    ) -> None:
        """Return the new checkpoint id for the given blobs."""
        response, request_id = self._post(
            "report-error",
            json={
                "original_request_id": original_request_id,
                "sanitized_message": sanitized_message,
                "stack_trace": stack_trace,
                "diagnostics": [
                    {"key": key, "value": value} for key, value in diagnostics
                ],
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def client_metrics(
        self, metrics: list[tuple[str, int]], retry_policy: Retry | None = None
    ) -> None:
        """Return the new checkpoint id for the given blobs."""
        response, request_id = self._post(
            "client-metrics",
            json={
                "metrics": [
                    {"client_metric": metric, "value": value}
                    for metric, value in metrics
                ]
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        # Expect an empty json response
        if response.json():
            raise Exception("Unexpected response")

    def client_for_model(self, model_name: str):
        """Returns the AugmentModelClient for a specific model."""
        return AugmentModelClient(self, model_name)

    def list_external_source_types(
        self, retry_policy: Retry | None = None
    ) -> list[str]:
        """Returns the list of all supported external source types."""
        response, request_id = self._post(
            "list-external-source-types", json={}, retry_policy=retry_policy
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return j["source_types"]

    def search_external_sources(
        self, query: str, source_types: list[str], retry_policy: Retry | None = None
    ) -> list[ExternalSource]:
        """Search external sources."""
        response, request_id = self._post(
            "search-external-sources",
            json={"query": query, "source_types": source_types},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return [
            ExternalSource(
                id=s["id"],
                title=s["title"],
                source_type=s["source_type"],
                name=s["name"],
            )
            for s in j["sources"]
        ]

    def get_implicit_external_sources(
        self, message: str, retry_policy: Retry | None = None
    ) -> list[ExternalSource]:
        """Get implicit external sources."""
        response, request_id = self._post(
            "get-implicit-external-sources",
            json={"message": message},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return [ExternalSource(**s) for s in j["sources"]]

    def save_chat(
        self,
        conversation_id: str,
        chat_exchange: Sequence[Exchange],
        title: str,
        retry_policy: Retry | None = None,
    ) -> SaveChatResponse:
        history = [ExchangeJson.from_exchange(x) for x in chat_exchange]
        history_json = [asdict(x) for x in history]

        response, request_id = self._post(
            "save-chat",
            json={
                "conversation_id": conversation_id,
                "chat": history_json,
                "title": title,
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return SaveChatResponse(uuid=j["uuid"], url=j["url"])

    def check_tool_safety(
        self, tool_id: int, tool_input_json: str, retry_policy: Retry | None = None
    ) -> bool:
        """Check if a tool call is safe to execute without user approval.

        Args:
            tool_id: The RemoteToolId enum value for the tool.
            tool_input_json: The JSON input for the tool.
            retry_policy: The retry policy to use; if not provided, use client instance default.

        Returns:
            True if the tool is safe to execute without user approval, False otherwise.
        """
        response, request_id = self._post(
            "agents/check-tool-safety",
            json={
                "tool_id": tool_id,
                "tool_input_json": tool_input_json,
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return j["is_safe"]

    def list_remote_tools(
        self, tool_ids: list[int] | None = None, retry_policy: Retry | None = None
    ) -> list[RemoteToolInfo]:
        """List available remote tools.

        Args:
            tool_ids: Optional list of RemoteToolId enum values to filter by.
            retry_policy: The retry policy to use; if not provided, use client instance default.

        Returns:
            List of RemoteToolInfo objects containing information about available tools.
        """
        json_data = {}
        if tool_ids is not None:
            json_data["tool_id_list"] = {"tool_ids": tool_ids}

        response, request_id = self._post(
            "agents/list-remote-tools",
            json=json_data,
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return [
            RemoteToolInfo(
                tool_definition=tool["tool_definition"],
                remote_tool_id=tool["remote_tool_id"],
                availability_status=tool["availability_status"],
                tool_safety=tool.get("tool_safety", ToolSafety.TOOL_UNSAFE),
            )
            for tool in j["tools"]
        ]

    def create_remote_agent(
        self,
        workspace_setup: RemoteAgentWorkspaceSetup,
        initial_request_nodes: list[dict],
        model: str | None = None,
        setup_script: str | None = None,
        user_guidelines: str | None = None,
        workspace_guidelines: str | None = None,
        agent_memories: str | None = None,
        retry_policy: Retry | None = None,
    ) -> CreateRemoteAgentResponse:
        """Create a new remote agent.

        Args:
            workspace_setup: The workspace setup for the agent.
            initial_request_nodes: The initial request nodes for the agent.
            model: Optional model name to use for the agent.
            setup_script: Optional setup script to run in the agent workspace.
            user_guidelines: Optional user guidelines for the agent.
            workspace_guidelines: Optional workspace guidelines for the agent.
            agent_memories: Optional agent memories to include.
            retry_policy: The retry policy to use; if not provided, use client instance default.

        Returns:
            A tuple containing the remote agent ID and status.
        """
        # The workspace_setup already has the correct structure with starting_files
        json_data = {
            "workspace_setup": asdict(workspace_setup),
            "initial_request_details": {
                "request_nodes": initial_request_nodes,
            },
        }

        if model is not None:
            json_data["model"] = model
        if setup_script is not None:
            json_data["setup_script"] = setup_script
        if user_guidelines is not None:
            json_data["initial_request_details"]["user_guidelines"] = user_guidelines
        if workspace_guidelines is not None:
            json_data["initial_request_details"]["workspace_guidelines"] = (
                workspace_guidelines
            )
        if agent_memories is not None:
            json_data["initial_request_details"]["agent_memories"] = agent_memories

        response, request_id = self._post(
            "remote-agents/create",
            json=json_data,
            retry_policy=retry_policy,
        )

        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return CreateRemoteAgentResponse(
            remote_agent_id=j["remote_agent_id"],
            status=j["status"],
        )

    def update_remote_agent_status(
        self, remote_agent_id: str, status: int, retry_policy: Retry | None = None
    ) -> None:
        """Update the status of a remote agent.

        Args:
            remote_agent_id: The ID of the remote agent to update.
            status: The new status for the agent.
            retry_policy: The retry policy to use; if not provided, use client instance default.
        """
        response, request_id = self._post(
            "agent-workspace/report-status",
            json={
                "remote_agent_id": remote_agent_id,
                "status": status,
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)

    def run_remote_tool(
        self,
        tool_name: str,
        tool_input_json: str,
        tool_id: int,
        extra_tool_input: dict | None = None,
        retry_policy: Retry | None = None,
        timeout: int | None = None,
    ) -> RunRemoteToolResult:
        """Run a remote tool.

        Args:
            tool_name: The name of the tool to run.
            tool_input_json: The JSON input for the tool.
            tool_id: The RemoteToolId enum value for the tool.
            extra_tool_input: Optional extra input for specific tools (e.g., API tokens).
            retry_policy: The retry policy to use; if not provided, use client instance default.
            timeout: The timeout to use; if not provided, use client instance default.

        Returns:
            RunRemoteToolResult containing the tool output and status.
        """
        json_data = {
            "tool_name": tool_name,
            "tool_input_json": tool_input_json,
            "tool_id": tool_id,
        }

        # Add extra tool input if provided
        if extra_tool_input:
            if tool_id in [
                RemoteToolId.JIRA_ISSUE,
                RemoteToolId.JIRA_PROJECT,
                RemoteToolId.JIRA_SEARCH,
                RemoteToolId.CONFLUENCE_SEARCH,
                RemoteToolId.CONFLUENCE_CONTENT,
                RemoteToolId.CONFLUENCE_SPACE,
                RemoteToolId.CONFLUENCE,
                RemoteToolId.JIRA,
            ]:
                json_data["atlassian_tool_extra_input"] = extra_tool_input
            elif (
                tool_id == RemoteToolId.NOTION_SEARCH
                or tool_id == RemoteToolId.NOTION_PAGE
                or tool_id == RemoteToolId.NOTION
            ):
                json_data["notion_tool_extra_input"] = extra_tool_input
            elif tool_id == RemoteToolId.LINEAR_SEARCH_ISSUES | RemoteToolId.LINEAR:
                json_data["linear_tool_extra_input"] = extra_tool_input
            elif tool_id == RemoteToolId.GITHUB_API:
                json_data["github_tool_extra_input"] = extra_tool_input

        response, request_id = self._post(
            "agents/run-remote-tool",
            json=json_data,
            retry_policy=retry_policy,
            timeout=timeout,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return RunRemoteToolResult(
            tool_output=j["tool_output"],
            tool_result_message=j["tool_result_message"],
            is_error=j.get("is_error", False),
            status=j["status"],
        )

    def list_remote_agents(
        self, retry_policy: Retry | None = None
    ) -> public_api_pb2.ListRemoteAgentsResponse:
        """List all remote agents.

        Args:
            retry_policy: The retry policy to use; if not provided, use client instance default.

        Returns:
            ListRemoteAgentsResponse containing the list of remote agents.
        """
        response, request_id = self._post(
            "remote-agents/list",
            json={},
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return ParseDict(
            j, public_api_pb2.ListRemoteAgentsResponse(), ignore_unknown_fields=True
        )

    def get_remote_agent_history(
        self,
        remote_agent_id: str,
        last_processed_sequence_id: int = 0,
        retry_policy: Retry | None = None,
    ) -> public_api_pb2.GetRemoteAgentChatHistoryResponse:
        """Get the chat history for a remote agent.

        Args:
            remote_agent_id: The ID of the remote agent.
            last_processed_sequence_id: The sequence ID of the last processed exchange.
                Use 0 to get all chat history.
            retry_policy: The retry policy to use; if not provided, use client instance default.

        Returns:
            GetRemoteAgentChatHistoryResponse containing the chat history.
        """
        response, request_id = self._post(
            "remote-agents/get-chat-history",
            json={
                "remote_agent_id": remote_agent_id,
                "last_processed_sequence_id": last_processed_sequence_id,
            },
            retry_policy=retry_policy,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return ParseDict(
            j,
            public_api_pb2.GetRemoteAgentChatHistoryResponse(),
            ignore_unknown_fields=True,
        )

    def remote_agent_chat(
        self,
        remote_agent_id: str,
        message: str,
        retry_policy: Retry | None = None,
        timeout: int | None = None,
    ) -> RemoteAgentChatResponse:
        """Send a chat message to a remote agent.

        Args:
            remote_agent_id: The ID of the remote agent.
            message: The message to send to the remote agent.
            retry_policy: The retry policy to use; if not provided, use client instance default.
            timeout: The timeout to use; if not provided, use client instance default.

        Returns:
            RemoteAgentChatResponse containing the response from the remote agent.
        """
        request_json = {
            "remote_agent_id": remote_agent_id,
            "request_details": {
                "request_nodes": [
                    {
                        "id": 0,
                        "type": ChatRequestNodeType.TEXT,
                        "text_node": {"content": message},
                    }
                ],
                "user_guidelines": "",
                "workspace_guidelines": "",
                "agent_memories": "",
            },
        }
        response, request_id = self._post(
            "remote-agents/chat",
            json=request_json,
            retry_policy=retry_policy,
            timeout=timeout,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()

        # Parse the nodes from the response
        nodes = []
        for node_data in j.get("nodes", []):
            # Create a ChatResultNode with the required fields
            node = ChatResultNode(
                id=node_data.get("id", 0),
                type=ChatResultNodeType(node_data.get("type", 0)),
                content=node_data.get("content", ""),
            )

            # Add tool_use if present
            if "tool_use" in node_data:
                tool_use_data = node_data["tool_use"]
                node.tool_use = ChatResultToolUse(
                    name=tool_use_data.get("name", ""),
                    input=tool_use_data.get("input", {}),
                    tool_use_id=tool_use_data.get("tool_use_id", ""),
                )

            nodes.append(node)

        return RemoteAgentChatResponse(
            remote_agent_id=remote_agent_id,  # Use the agent ID from the request
            nodes=nodes,
        )

    def delete_remote_agent(
        self,
        remote_agent_id: str,
        retry_policy: Retry | None = None,
        timeout: int | None = None,
    ) -> None:
        """Delete a remote agent.

        Args:
            remote_agent_id: The ID of the remote agent to delete.
            retry_policy: The retry policy to use; if not provided, use client instance default.
            timeout: The timeout to use; if not provided, use client instance default.
        """
        response, request_id = self._post(
            "remote-agents/delete",
            json={"remote_agent_id": remote_agent_id},
            retry_policy=retry_policy,
            timeout=timeout,
        )

        if not response.ok:
            raise ClientException(request_id, response)

    def pause_remote_agent(
        self,
        remote_agent_id: str,
        retry_policy: Retry | None = None,
        timeout: int | None = None,
    ) -> None:
        """Pause a remote agent.

        Args:
            remote_agent_id: The ID of the remote agent to pause.
            retry_policy: The retry policy to use; if not provided, use client instance default.
            timeout: The timeout to use; if not provided, use client instance default.
        """
        response, request_id = self._post(
            "remote-agents/pause",
            json={"remote_agent_id": remote_agent_id},
            retry_policy=retry_policy,
            timeout=timeout,
        )

        if not response.ok:
            raise ClientException(request_id, response)
        return response.json()

    def resume_remote_agent(
        self,
        remote_agent_id: str,
        retry_policy: Retry | None = None,
        timeout: int | None = None,
    ) -> None:
        """Resume a remote agent.

        Args:
            remote_agent_id: The ID of the remote agent to resume.
            retry_policy: The retry policy to use; if not provided, use client instance default.
            timeout: The timeout to use; if not provided, use client instance default.
        """
        response, request_id = self._post(
            "remote-agents/resume",
            json={"remote_agent_id": remote_agent_id},
            retry_policy=retry_policy,
            timeout=timeout,
        )

        if not response.ok:
            raise ClientException(request_id, response)
        return response.json()

    def interrupt_remote_agent(
        self,
        remote_agent_id: str,
        retry_policy: Retry | None = None,
        timeout: int | None = None,
    ) -> str:
        """Interrupt a remote agent.

        Args:
            remote_agent_id: The ID of the remote agent to interrupt.
            retry_policy: The retry policy to use; if not provided, use client instance default.
            timeout: The timeout to use; if not provided, use client instance default.

        Returns:
            The status of the remote agent after the interruption.
        """
        response, request_id = self._post(
            "remote-agents/interrupt",
            json={"remote_agent_id": remote_agent_id},
            retry_policy=retry_policy,
            timeout=timeout,
        )

        if not response.ok:
            raise ClientException(request_id, response)

        j = response.json()
        return j["status"]

    def get_remote_agent_history_stream(
        self,
        remote_agent_id: str,
        last_processed_sequence_id: int = 0,
        timeout: int | None = None,
    ) -> requests.Response:
        """Get a streaming response of agent history updates.

        Args:
            remote_agent_id: The ID of the remote agent to get history for.
            last_processed_sequence_id: The sequence ID of the last processed exchange.
                Use 0 to get all agent history.
            timeout: The timeout to use; if not provided, use client instance default.

        Returns:
            A streaming response object that can be iterated over to get agent history updates.
        """
        response, _ = self._post_stream(
            "remote-agents/agent-history-stream",
            json={
                "remote_agent_id": remote_agent_id,
                "last_processed_sequence_id": last_processed_sequence_id,
            },
            timeout=timeout,
        )

        if not response.ok:
            logging.error(
                f"Error getting agent history stream: {response.status_code} {response.text}"
            )
            raise Exception(
                f"Error getting agent history stream: {response.status_code} {response.text}"
            )

        return response

    def get_agent_workspace_stream(
        self,
        remote_agent_id: str,
        last_processed_sequence_id: int = 0,
        timeout: int | None = None,
    ) -> requests.Response:
        """Get a streaming response of agent workspace updates.

        Args:
            remote_agent_id: The ID of the remote agent to get workspace updates for.
            last_processed_sequence_id: The sequence ID of the last processed update.
                Use 0 to get all workspace updates.
            timeout: The timeout to use; if not provided, use client instance default.

        Returns:
            A streaming response object that can be iterated over to get workspace updates.
        """
        response, _ = self._post_stream(
            "agent-workspace/stream",
            json={
                "remote_agent_id": remote_agent_id,
                "last_processed_sequence_id": last_processed_sequence_id,
            },
            timeout=timeout,
        )

        if not response.ok:
            logging.error(
                f"Error getting agent workspace stream: {response.status_code} {response.text}"
            )
            raise Exception(
                f"Error getting agent workspace stream: {response.status_code} {response.text}"
            )

        return response

    def parse_agent_workspace_stream(
        self, response: requests.Response, warn_on_parse_error: bool = False
    ) -> Iterable[AgentWorkspaceStreamResponse]:
        """Parse a streaming response of agent workspace updates.

        Args:
            response: The streaming response from get_agent_workspace_stream.
            warn_on_parse_error: Whether to warn on parse error instead of raising an exception.

        Yields:
            AgentWorkspaceStreamResponse objects containing workspace updates.
        """
        for line in response.iter_lines(chunk_size=None, decode_unicode=True):
            if not line.strip():
                continue

            try:
                json_data = loads(line)

                # Parse the updates
                updates = []
                for update_data in json_data.get("updates", []):
                    update_type = AgentWorkspaceUpdateType(update_data.get("type", 0))

                    # Create the appropriate update object based on the type
                    interrupt = None
                    chat_request = None

                    if (
                        update_type
                        == AgentWorkspaceUpdateType.AGENT_WORKSPACE_UPDATE_INTERRUPT
                    ):
                        interrupt = AgentWorkspaceInterrupt()
                    elif (
                        update_type
                        == AgentWorkspaceUpdateType.AGENT_WORKSPACE_UPDATE_CHAT_REQUEST
                    ):
                        chat_request_data = update_data.get("chat_request", {})
                        chat_request = AgentWorkspaceChatRequest(
                            request_details=chat_request_data.get("request_details", {})
                        )

                    updates.append(
                        AgentWorkspaceStreamUpdate(
                            type=update_type,
                            sequence_id=update_data.get("sequence_id", 0),
                            interrupt=interrupt,
                            chat_request=chat_request,
                        )
                    )

                yield AgentWorkspaceStreamResponse(updates=updates)

            except JSONDecodeError as e:
                if warn_on_parse_error:
                    logging.warning("Failed to parse workspace stream update: %s", line)
                else:
                    raise Exception(
                        f"Failed to parse workspace stream update: {line}"
                    ) from e
            except Exception as e:
                if warn_on_parse_error:
                    logging.warning("Error processing workspace stream update: %s", e)
                else:
                    raise Exception(
                        f"Error processing workspace stream update: {e}"
                    ) from e

    def stream_agent_workspace_updates(
        self,
        remote_agent_id: str,
        last_processed_sequence_id: int = 0,
        timeout: int | None = None,
        warn_on_parse_error: bool = False,
    ) -> Iterable[AgentWorkspaceStreamResponse]:
        """Get and parse a streaming response of agent workspace updates.

        This is a convenience method that combines get_agent_workspace_stream and parse_agent_workspace_stream.

        Args:
            remote_agent_id: The ID of the remote agent to get workspace updates for.
            last_processed_sequence_id: The sequence ID of the last processed update.
                Use 0 to get all workspace updates.
            timeout: The timeout to use; if not provided, use client instance default.
            warn_on_parse_error: Whether to warn on parse error instead of raising an exception.

        Yields:
            AgentWorkspaceStreamResponse objects containing workspace updates.
        """
        response = self.get_agent_workspace_stream(
            remote_agent_id=remote_agent_id,
            last_processed_sequence_id=last_processed_sequence_id,
            timeout=timeout,
        )

        yield from self.parse_agent_workspace_stream(
            response=response,
            warn_on_parse_error=warn_on_parse_error,
        )


class AugmentModelClient:
    """Client to access the augment API for a specific model."""

    def __init__(self, augment_client: AugmentClient, model_name: str):
        self.augment_client_ = augment_client
        self.model_name = model_name

    def __repr__(self):
        return f"AugmentModelClient(url={self.augment_client_.url}, request_session_id={self.augment_client_.request_session_id}, last_request_id={self.augment_client_.last_request_id}, model={self.model_name})"

    def _blobs_to_dict(self, blobs: BlobsJson | dict | None = None) -> dict:
        """Returns the given blobs, or an empty BlobsJson if blobs is None."""
        if blobs is None:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            blobs = BlobsJson(checkpoint_id=None, added_blobs=[], deleted_blobs=[])
        elif isinstance(blobs, dict):
            blobs = BlobsJson(**blobs)
        blobs = blobs.sorted()
        return asdict(blobs)

    def complete(
        self,
        prompt: str,
        path: str,
        memories: list[str] | None = None,
        suffix: str | None = None,
        top_k: int | None = None,
        top_p: float | None = None,
        temperature: float | None = None,
        max_tokens: int | None = None,
        lang: str | None = None,
        blob_name: str
        | None = None,  # This is the current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        cursor_position: int | None = None,
        suffix_end: int | None = None,
        probe_only: bool | None = False,
        blobs: BlobsJson | dict | None = None,
        recency_info: dict | None = None,
        edit_events: list[dict] | None = None,
        filter_threshold: float | None = None,
        timeout: int | None = None,
        retry_policy: Retry | None = None,
    ) -> CompleteResponse:
        """Runs a completion on the given prompt."""
        json: dict[str, Any] = {
            "model": self.model_name,
            "path": path,
            "prompt": prompt,
        }
        if memories:
            json["memories"] = memories
        if suffix:
            json["suffix"] = suffix
        if top_k:
            json["top_k"] = top_k
        if top_p:
            json["top_p"] = top_p
        if temperature:
            json["temperature"] = temperature
        if max_tokens:
            json["max_tokens"] = max_tokens
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if cursor_position is not None:
            json["cursor_position"] = cursor_position
        if suffix_end is not None:
            json["suffix_end"] = suffix_end
        if probe_only:
            json["probe_only"] = probe_only
        if recency_info is not None:
            json["recency_info"] = recency_info
        if filter_threshold is not None:
            json["filter_threshold"] = filter_threshold
        if edit_events is not None:
            json["edit_events"] = edit_events
        json["blobs"] = self._blobs_to_dict(blobs)

        response, request_id = self.augment_client_._post(
            "completion",
            json=json,
            timeout=timeout,
            retry_policy=retry_policy,
        )
        logging.debug(
            "Completion finished: %s %s %s",
            request_id,
            response.status_code,
            response.content,
        )
        if not response.ok:
            logging.error("Request failed: %s", response.content)
            raise ClientException(request_id, response)
        j = response.json()
        text = j["text"]
        unknown_memory_names = j.get("unknown_memory_names")
        if unknown_memory_names is None:
            unknown_memory_names = []
        completion_items = [
            CompletionItem(**entry) for entry in j.get("completion_items") or []
        ]
        # Note, calling json.get() will return None if the requested key is not
        # present.
        return CompleteResponse(
            text,
            completion_items=completion_items,
            unknown_memory_names=unknown_memory_names,
            request_id=request_id,
            checkpoint_not_found=j.get("checkpoint_not_found"),
            suggested_prefix_char_count=j.get("suggested_prefix_char_count"),
            suggested_suffix_char_count=j.get("suggested_suffix_char_count"),
        )

    def edit(
        self,
        selected_text: str,
        instruction: str,
        prefix: str,
        suffix: str,
        path: str | None,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        timeout: int | None = None,
        retry_policy: Retry | None = None,
    ) -> EditResponse:
        """Runs an edit on the given selected text."""
        json = {
            "model": self.model_name,
            "selected_text": selected_text,
            "instruction": instruction,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
        }
        if blob_names:
            json["blob_names"] = blob_names
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end
        json["blobs"] = self._blobs_to_dict(blobs)

        response, request_id = self.augment_client_._post(
            "edit", json=json, timeout=timeout, retry_policy=retry_policy
        )
        logging.debug(
            "Edit finished: %s %s %s",
            request_id,
            response.status_code,
            response.content,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        text = j["text"]
        return EditResponse(
            str(request_id),
            text,
            j.get("unknown_blob_names", []),
            j.get("checkpoint_not_found"),
        )

    def instruction_stream(
        self,
        selected_text: str,
        instruction: str,
        prefix: str,
        suffix: str,
        path: str | None,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        chat_history: list[Exchange | dict] | None = None,
        timeout: int | None = None,
        warn_on_parse_error: bool = False,
    ) -> Iterable[InstructionResponse]:
        """Runs an instruction on the given selected text."""
        json = {
            "model": self.model_name,
            "selected_text": selected_text,
            "instruction": instruction,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
        }
        if blob_names:
            json["blob_names"] = blob_names
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end

        json["blobs"] = self._blobs_to_dict(blobs)

        if chat_history is None:
            json["chat_history"] = []
        else:
            json["chat_history"] = [
                exchange if isinstance(exchange, dict) else asdict(exchange)
                for exchange in chat_history
            ]

        response, request_id = self.augment_client_._post_stream(
            "instruction-stream", json=json, timeout=timeout
        )
        logging.debug("Got stream response")
        for line in response.iter_lines(chunk_size=None, decode_unicode=True):
            try:
                json_container = loads(line)
                if isinstance(json_container, dict):
                    json_container = [json_container]
                assert isinstance(json_container, list)
                for json_item in json_container:
                    if "error" in json_item:
                        raise Exception(json_item["error"])
                    yield InstructionResponse(**json_item, request_id=str(request_id))
            except JSONDecodeError as e:
                if warn_on_parse_error:
                    logging.warning("Failed to parse: %s", line)
                else:
                    raise Exception(f"Failed to parse: {line}") from e
        logging.debug("Done yielding stream response")

    def smart_paste_stream(
        self,
        code_block: str,
        selected_text: str,
        prefix: str,
        suffix: str,
        path: str | None,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str | None = None,  # Current blob being operated on, NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        chat_history: list[Exchange | dict] | None = None,
        target_file_path: str | None = None,
        target_file_content: str | None = None,
        context_code_exchange_request_id: str | None = None,
        timeout: int | None = None,
        warn_on_parse_error: bool = False,
    ) -> Iterable[InstructionResponse]:
        """Runs smart paste for given code_block into the target file content."""
        json = {
            "model": self.model_name,
            "selected_text": selected_text,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            "code_block": code_block,
            "target_file_path": target_file_path,
            "target_file_content": target_file_content,
            "context_code_exchange_request_id": context_code_exchange_request_id,
            "instruction": "",  # Currently required because request type is shared with instruction
        }
        if blob_names:
            json["blob_names"] = blob_names
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end

        json["blobs"] = self._blobs_to_dict(blobs)

        # Currently no history for smart-paste, but keeping in case we add it
        if chat_history is None:
            json["chat_history"] = []
        else:
            json["chat_history"] = [
                exchange if isinstance(exchange, dict) else asdict(exchange)
                for exchange in chat_history
            ]

        response, request_id = self.augment_client_._post_stream(
            "smart-paste-stream", json=json, timeout=timeout
        )
        logging.debug("Got stream response")
        if not response.ok:
            logging.error(response.text)
            raise ClientException(request_id, response)
        for line in response.iter_lines(chunk_size=None, decode_unicode=True):
            try:
                json_container = loads(line)
                if isinstance(json_container, dict):
                    json_container = [json_container]
                assert isinstance(json_container, list)
                for json_item in json_container:
                    if "error" in json_item:
                        logging.error(
                            "Error in smart paste stream: %s", json_item["error"]
                        )
                        raise Exception(json_item["error"])
                    yield InstructionResponse(**json_item, request_id=str(request_id))
            except JSONDecodeError as e:
                if warn_on_parse_error:
                    logging.warning("Failed to parse: %s", line)
                else:
                    raise Exception(f"Failed to parse: {line}") from e
        logging.debug("Done yielding stream response")

    def chat(
        self,
        selected_code: str,
        message: str,
        prefix: str,
        suffix: str,
        path: str,
        blob_names: list[str] | None = None,  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        chat_history: Sequence[Exchange | dict] | None = None,
        user_guided_blobs: list[str] | None = None,
        context_code_exchange_request_id: str | None = None,
        external_source_ids: list[str] | None = None,
        user_guidelines: str | None = None,
        workspace_guidelines: str | None = None,
        timeout: int | None = None,
        disable_auto_external_sources: bool = False,
        retry_policy: Retry | None = None,
        support_raw_output: bool = False,
        image_paths: list[Path] | None = None,
    ) -> ChatResponse:
        if image_paths:
            nodes = self._construct_chat_request_nodes_with_images(image_paths)
        else:
            nodes = None
        """Chat with the model."""
        json: dict[str, Any] = {
            "model": self.model_name,
            "selected_code": selected_code,
            "message": message,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            "disable_auto_external_sources": disable_auto_external_sources,
            "feature_detection_flags": {
                "support_raw_output": support_raw_output,
            },
        }
        if blob_names:
            json["blob_names"] = blob_names
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end
        if context_code_exchange_request_id is not None:
            json["context_code_exchange_request_id"] = context_code_exchange_request_id
        if user_guided_blobs is not None:
            json["user_guided_blobs"] = user_guided_blobs
        if external_source_ids is not None:
            json["external_source_ids"] = external_source_ids
        if user_guidelines is not None:
            json["user_guidelines"] = user_guidelines
        if workspace_guidelines is not None:
            json["workspace_guidelines"] = workspace_guidelines
        if nodes is not None:
            json["nodes"] = nodes

        json["blobs"] = self._blobs_to_dict(blobs)

        if chat_history is None:
            json["chat_history"] = []
        else:
            json["chat_history"] = [
                exchange if isinstance(exchange, dict) else asdict(exchange)
                for exchange in chat_history
            ]

        response, request_id = self.augment_client_._post(
            "chat", json=json, timeout=timeout, retry_policy=retry_policy
        )
        logging.debug(
            "Chat finished: %s %s %s",
            request_id,
            response.status_code,
            response.content,
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        text = j["text"]
        incorporated_external_sources = j.get("incorporated_external_sources", None)
        if incorporated_external_sources is not None:
            incorporated_external_sources = [
                IncorporatedExternalSource(
                    source_name=chunk["source_name"],
                    link=chunk.get("link"),
                )
                for chunk in incorporated_external_sources
            ]
        workspace_file_chunks = j.get("workspace_file_chunks", None)
        if workspace_file_chunks is not None:
            workspace_file_chunks = [
                WorkspaceFileChunk(
                    char_start=chunk["char_start"],
                    char_end=chunk["char_end"],
                    blob_name=chunk["blob_name"],
                )
                for chunk in workspace_file_chunks
            ]
        nodes = [
            ChatResultNode(
                id=node["id"],
                type=ChatResultNodeType(node["type"]),
                content=node["content"],
            )
            for node in j.get("nodes", [])
        ]
        stop_reason = j.get("stop_reason")
        if stop_reason is not None:
            stop_reason = public_api_pb2.ChatStopReason.Name(stop_reason).lower()
        return ChatResponse(
            str(request_id),
            text,
            j.get("unknown_blob_names", []),
            j.get("checkpoint_not_found"),
            workspace_file_chunks=workspace_file_chunks,
            incorporated_external_sources=incorporated_external_sources,
            nodes=nodes,
            stop_reason=stop_reason,
        )

    def chat_stream(
        self,
        selected_code: str,
        message: str,
        prefix: str,
        suffix: str,
        path: str,
        blob_names: list[str] = [],  # deprecated. use blobs instead!
        lang: str | None = None,
        blob_name: str
        | None = None,  # Current blob being operated on, and NOT deprecated
        prefix_begin: int | None = None,
        suffix_end: int | None = None,
        blobs: BlobsJson | dict | None = None,
        chat_history: Sequence[Exchange | dict] | None = None,
        user_guided_blobs: list[str] | None = None,
        context_code_exchange_request_id: str | None = None,
        external_source_ids: list[str] | None = None,
        user_guidelines: str | None = None,
        workspace_guidelines: str | None = None,
        timeout: int | None = None,
        warn_on_parse_error: bool = False,
        support_raw_output: bool = False,
        image_paths: list[Path] | None = None,
    ) -> Iterable[ChatResponse]:
        """Chat with the model.

        Args:
            selected_code: Selected code. Might be empty.
            message: User message.
            prefix: Prefix before selected code.
            suffix: Suffix after selected code.
            path: Path of current file.
            blob_names: DEPRECATED in favor of `blobs`. Blob names
                as a list of hashes.
            lang: The language. Defaults to None.
            blob_name: The blob name of the current blob. Note that it
                is different from `blob_names` and is not deprecated
            prefix_begin:
            suffix_end:
            blobs:
            chat_history: Conversation history.
            user_guided_blobs: User-guided blob names.
            context_code_exchange_request_id:
            external_source_ids:
            timeout:
            warn_on_parse_error: Whether to warn on parse error.
            support_raw_output: Whether to support raw output in the chat.

        Returns:
            An iterable of strings, where a string might or might not be in JSON format separated by newline.
            Note: This is not a iterable over JSON documents or a NLJSON document.
            Each JSON document will be a JSON serialization of a ChatResponse defined in public_api.proto.
        """
        if image_paths:
            nodes = self._construct_chat_request_nodes_with_images(image_paths)
        else:
            nodes = None
        json_request: dict[str, Any] = {
            "model": self.model_name,
            "selected_code": selected_code,
            "message": message,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            "blob_names": blob_names,
            "feature_detection_flags": {
                "support_raw_output": support_raw_output,
            },
        }
        if blob_names:
            json_request["blob_names"] = blob_names
        if lang:
            json_request["lang"] = lang
        if blob_names:
            json_request["blob_names"] = blob_names
        if blob_name:
            json_request["blob_name"] = blob_name
        if prefix_begin is not None:
            json_request["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json_request["suffix_end"] = suffix_end
        if context_code_exchange_request_id is not None:
            json_request["context_code_exchange_request_id"] = (
                context_code_exchange_request_id
            )
        if user_guided_blobs is not None:
            json_request["user_guided_blobs"] = user_guided_blobs
        if external_source_ids is not None:
            json_request["external_source_ids"] = external_source_ids
        if user_guidelines:
            json_request["user_guidelines"] = user_guidelines
        if workspace_guidelines:
            json_request["workspace_guidelines"] = workspace_guidelines
        if nodes is not None:
            json_request["nodes"] = nodes

        json_request["blobs"] = self._blobs_to_dict(blobs)

        if chat_history is None:
            json_request["chat_history"] = []
        else:
            json_request["chat_history"] = [
                exchange if isinstance(exchange, dict) else asdict(exchange)
                for exchange in chat_history
            ]

        response, request_id = self.augment_client_._post_stream(
            "chat-stream", json=json_request, headers={}, timeout=timeout
        )
        logging.debug("Got stream response")
        if not response.ok:
            print(response.text)
            raise ClientException(request_id, response)

        for line in response.iter_lines(chunk_size=None, decode_unicode=True):
            try:
                json_container = loads(line)
                if isinstance(json_container, dict):
                    json_container = [json_container]
                assert isinstance(json_container, list)
                for json_item in json_container:
                    if "error" in json_item:
                        raise Exception(json_item["error"])
                    incorporated_external_sources = json_item.get(
                        "incorporated_external_sources", None
                    )
                    if incorporated_external_sources is not None:
                        del json_item["incorporated_external_sources"]
                        incorporated_external_sources = [
                            IncorporatedExternalSource(
                                source_name=chunk["source_name"],
                                link=chunk.get("link"),
                            )
                            for chunk in incorporated_external_sources
                        ]
                    workspace_file_chunks = json_item.get("workspace_file_chunks", None)
                    if workspace_file_chunks is not None:
                        del json_item["workspace_file_chunks"]
                        workspace_file_chunks = [
                            WorkspaceFileChunk(
                                char_start=chunk["char_start"],
                                char_end=chunk["char_end"],
                                blob_name=chunk["blob_name"],
                            )
                            for chunk in workspace_file_chunks
                        ]
                    nodes = [
                        ChatResultNode(
                            id=node["id"],
                            type=ChatResultNodeType(node["type"]),
                            content=node["content"],
                        )
                        for node in json_item.get("nodes", [])
                    ]
                    stop_reason = json_item.get("stop_reason")
                    if stop_reason is not None:
                        stop_reason = public_api_pb2.ChatStopReason.Name(
                            stop_reason
                        ).lower()
                    yield ChatResponse(
                        text=json_item["text"],
                        unknown_blob_names=json_item["unknown_blob_names"],
                        checkpoint_not_found=json_item["checkpoint_not_found"],
                        request_id=str(request_id),
                        incorporated_external_sources=incorporated_external_sources,
                        workspace_file_chunks=workspace_file_chunks,
                        nodes=nodes,
                        stop_reason=stop_reason,
                    )
            except JSONDecodeError as e:
                if warn_on_parse_error:
                    logging.warning("Failed to parse: %s", line)
                else:
                    raise Exception(f"Failed to parse: {line}") from e

        logging.debug("Done yielding stream response")

    def generate_commit_message_stream(
        self,
        changed_file_stats: ChangedFileStats,
        diff: str,
        relevant_commit_messages: list[str] | None = None,
        example_commit_messages: list[str] | None = None,
        timeout: int | None = None,
        warn_on_parse_error: bool = False,
    ) -> Iterable[ChatResponse]:
        """Generate a commit message using the model.

        Args:
            changed_file_stats: Stats of changed files per change type for the current
                diff of the repository.
            diff: Current diff of the repository, potentially truncated.
            relevant_commit_messages: Relevant commit messages that inform the model
                about current commit's context.
            example_commit_messages: Example commit messages from the repository to
                illustrate the style and conventions of commit messages.
            timeout: Timeout in seconds.

        Returns:

        """
        json_request: dict[str, Any] = {
            "changed_file_stats": changed_file_stats,
            "diff": diff,
            "relevant_commit_messages": relevant_commit_messages or [],
            "example_commit_messages": example_commit_messages or [],
        }

        response, request_id = self.augment_client_._post_stream(
            "generate-commit-message-stream",
            json=json_request,
            headers={},
            timeout=timeout,
        )
        logging.debug("Got stream response")

        for line in response.iter_lines(chunk_size=None, decode_unicode=True):
            try:
                json_container = loads(line)
                if isinstance(json_container, dict):
                    json_container = [json_container]
                assert isinstance(json_container, list)
                for json_item in json_container:
                    if "error" in json_item:
                        raise Exception(json_item["error"])
                    yield ChatResponse(
                        request_id=str(request_id),
                        text=json_item["text"],
                        unknown_blob_names=[],
                        checkpoint_not_found=False,
                    )
            except JSONDecodeError as e:
                if warn_on_parse_error:
                    logging.warning("Failed to parse: %s", line)
                else:
                    raise Exception(f"Failed to parse: {line}") from e
        logging.debug("Done yielding stream response")

    def next_edit_stream(
        self,
        mode: str,
        scope: str,
        instruction: str,
        prefix: str,
        suffix: str,
        selected_text: str,
        vcs_change: dict | VCSChange = {"working_directory_changes": [], "commits": []},
        edit_events: list[dict] = [],
        recent_changes: list[dict] = [],  # a list of replacement texts
        path: str | None = None,
        blob_name: str | None = None,
        blobs: BlobsJson | dict | None = None,
        timeout: int | None = None,
        warn_on_parse_error: bool = False,
        api_version: int = 0,
        **kwargs,
    ) -> Iterable[NextEditResponse]:
        """
        Yields the next edit response.

        Note that `blob_name` should be an indexed version of the current file. If not\
            provided, it will be computed from the `path` and `code` arguments, but
            you'll need to ensure that it's indexed.

        Returns:
            Next edit suggestions.
        """
        code = prefix + selected_text + suffix
        if blob_name is None:
            blob_name = get_blob_name(path or "", code)

        json_request: dict[str, Any] = {
            "model": self.model_name,
            "mode": mode,
            "scope": scope,
            "instruction": instruction,
            "prefix": prefix,
            "suffix": suffix,
            "selected_text": selected_text,
            "edit_events": edit_events,
            "recent_changes": recent_changes,
            "path": path,
            "blob_name": blob_name,
            # for now, these have to agree with the prefix/selected_text size
            "selection_begin_char": len(prefix),
            "selection_end_char": len(prefix) + len(selected_text),
            "api_version": api_version,
        }
        json_request.update(kwargs)
        if vcs_change is not None:
            json_request["vcs_change"] = vcs_change

        json_request["blobs"] = self._blobs_to_dict(blobs)

        response, request_id = self.augment_client_._post_stream(
            "next-edit-stream", json=json_request, headers={}, timeout=timeout
        )
        logging.debug("Got stream response")

        if not response.ok:
            print(response.text)
            raise ClientException(request_id, response)

        for line in response.iter_lines(chunk_size=None, decode_unicode=True):
            logging.debug("Next edit stream chunk: %s", line)
            try:
                json_container = loads(line)
                if isinstance(json_container, dict):
                    json_container = [json_container]
                assert isinstance(json_container, list)
                for json_item in json_container:
                    if "error" in json_item:
                        raise Exception(json_item["error"])
                    yield NextEditResponse(**json_item, request_id=str(request_id))
            except JSONDecodeError as e:
                if warn_on_parse_error:
                    logging.warning("Failed to parse: %s", line)
                else:
                    raise Exception(f"Failed to parse: {line}") from e
        logging.debug("Done yielding stream response")

    def _construct_chat_request_nodes_with_images(
        self, image_paths: list[Path]
    ) -> list[dict]:
        """Constructs ChatRequestNodes for images.

        Args:
            image_paths: List of image paths

        Returns:
            List of ChatRequestNode dictionaries
        """

        nodes = []
        for i, image_path in enumerate(image_paths):
            # Detect image format
            image = image_path.read_bytes()
            image_format = imghdr.what(None, h=image)

            # Convert format to enum value
            format_type = {
                "png": ImageFormatType.PNG,
                "jpeg": ImageFormatType.JPEG,
                "jpg": ImageFormatType.JPEG,
                "gif": ImageFormatType.GIF,
                "webp": ImageFormatType.WEBP,
            }.get(image_format or "")

            if format_type is None:
                raise ValueError(f"Unsupported image format: {image_format}")

            # Convert image to base64
            image_b64 = base64.b64encode(image).decode("utf-8")

            # Create node for this image
            nodes.append(
                {
                    "id": i + 1,
                    "type": ChatRequestNodeType.IMAGE,
                    "image_node": {"image_data": image_b64, "format": format_type},
                }
            )
        return nodes

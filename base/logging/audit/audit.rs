use serde_json::json;
use std::io::Write;
use std::sync::Arc;

pub enum Resource {
    Request {
        request_id: String,
    },
    Blob {
        blob_name: String,
    },
    User {
        user_id: String,
    },
    GenieRequest {
        access_type: String,
        business_reason: String,
        ttl: String,
        auto_approve: bool,
    },
    GenieApproval {
        access_type: String,
        business_reason: String,
        approved_by: String,
        ttl: String,
    },
}

impl Resource {
    fn to_json(&self) -> serde_json::Value {
        match self {
            Resource::Request { request_id } => json!({
                "@type": "type.eng.augmentcode.com/Request",
                "name": "request_id",
                "value": request_id,
            }),
            Resource::Blob { blob_name } => json!({
                "@type": "type.eng.augmentcode.com/Blob",
                "name": "blob_name",
                "value": blob_name,
            }),
            Resource::User { user_id } => json!({
                "@type": "type.eng.augmentcode.com/User",
                "name": "user_id",
                "value": user_id,
            }),
            Resource::GenieRequest {
                access_type,
                business_reason,
                ttl,
                auto_approve,
            } => json!({
                "@type": "type.eng.augmentcode.com/Permission",
                "access_type": access_type,
                "business_reason": business_reason,
                "ttl": ttl,
                "auto_approve": auto_approve,
            }),
            Resource::GenieApproval {
                access_type,
                business_reason,
                approved_by,
                ttl,
            } => json!({
                "@type": "type.eng.augmentcode.com/Permission",
                "access_type": access_type,
                "business_reason": business_reason,
                "approved_by": approved_by,
                "ttl": ttl,
            }),
        }
    }
}

// should match auth_entities.proto for consistency
pub const INTERNAL_IAP: &str = "INTERNAL_IAP";

pub trait AuditWriter {
    fn write(&self, data: serde_json::Value);
}

struct StdoutAuditWriter {}

impl AuditWriter for StdoutAuditWriter {
    fn write(&self, data: serde_json::Value) {
        let line = format!("{}\n", data);
        std::io::stdout().write_all(line.as_bytes()).unwrap();
        std::io::stdout().flush().unwrap();
    }
}

#[derive(Clone)]
pub struct AuditLogger {
    writer: Arc<dyn AuditWriter + Send + Sync + 'static>,
    env: String,
}

pub fn stdout_audit_logger() -> AuditLogger {
    let env = std::env::var("POD_ENV").unwrap_or_else(|_| "".to_string());
    AuditLogger {
        writer: Arc::new(StdoutAuditWriter {}),
        env,
    }
}

impl AuditLogger {
    pub fn new(writer: Arc<dyn AuditWriter + Send + Sync + 'static>, env: String) -> AuditLogger {
        AuditLogger { writer, env }
    }

    pub fn write_audit_log<T: AsRef<str>>(
        &self,
        user_id: &str,
        user_id_type: &str,
        tenant_name: Option<&str>,
        resource: Option<Resource>,
        message: Option<T>,
    ) {
        let mut data = json!({
            "@type": "type.eng.augmentcode.com/AuditLog",
            "authenticationInfo": {
                "principal": user_id,
                "principal_type": user_id_type,
            },
        });
        if let Some(resource) = resource {
            data["resource"] = resource.to_json();
        }
        if let Some(tenant_name) = tenant_name {
            data["tenant"] = json!({
                "name": tenant_name,
            });
        }
        if !self.env.is_empty() {
            data["env"] = serde_json::json!(self.env);
        }
        if let Some(message) = message {
            data["message"] = serde_json::json!(message.as_ref());
        }
        self.writer.write(data);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    use std::sync::Mutex;

    #[derive(Clone)]
    struct TestAuditWriter {
        output: Arc<Mutex<Vec<u8>>>,
    }

    impl TestAuditWriter {
        fn new() -> TestAuditWriter {
            TestAuditWriter {
                output: Arc::new(Mutex::new(Vec::new())),
            }
        }
    }

    impl AuditWriter for TestAuditWriter {
        fn write(&self, data: serde_json::Value) {
            let mut output = self.output.lock().unwrap();
            let line = format!("{}\n", data);
            output.extend(line.as_bytes());
        }
    }

    #[test]
    fn test_write_audit_log() {
        let audit_writer = TestAuditWriter::new();
        let audit_logger = AuditLogger::new(Arc::new(audit_writer.clone()), "STAGING".to_string());

        audit_logger.write_audit_log(
            "user-123",
            INTERNAL_IAP,
            Some("tenant-123"),
            Some(Resource::Request {
                request_id: "request-123".to_string(),
            }),
            Some("This is a test audit log"),
        );
        let captured_output =
            String::from_utf8(audit_writer.output.lock().unwrap().clone()).unwrap();
        assert!(captured_output.ends_with('\n'));
        // parse as json
        let json_data: serde_json::Value = serde_json::from_str(&captured_output).unwrap();
        assert_eq!(json_data["@type"], "type.eng.augmentcode.com/AuditLog");
        assert_eq!(json_data["authenticationInfo"]["principal"], "user-123");
        assert_eq!(json_data["tenant"]["name"], "tenant-123");
        assert_eq!(
            json_data["resource"]["@type"],
            "type.eng.augmentcode.com/Request"
        );
        assert_eq!(json_data["env"], "STAGING");
        assert_eq!(json_data["resource"]["name"], "request_id");
        assert_eq!(json_data["resource"]["value"], "request-123");
        assert_eq!(json_data["message"], "This is a test audit log");
    }
}

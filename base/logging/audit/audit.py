"""Audit logging.

This is a special log that is used to track user access to requests.
"""

import os
import json
import typing

import pydantic


class Resource(typing.Protocol):
    """Protocol for a resource."""

    def to_dict(self) -> typing.Dict[str, str]:
        """Returns the JSON representation of the resource."""
        raise NotImplementedError()


class Request(Resource):
    """A resource that represents a request."""

    def __init__(self, request_id: str):
        self.request_id = request_id

    def to_dict(self) -> typing.Dict[str, str]:
        return {
            "@type": "type.eng.augmentcode.com/Request",
            "name": "request_id",
            "value": self.request_id,
        }


class Blob(Resource):
    """A resource that represents a blob."""

    def __init__(self, blob_name: str):
        self.blob_name = blob_name

    def to_dict(self) -> typing.Dict[str, str]:
        return {
            "@type": "type.eng.augmentcode.com/Blob",
            "name": "blob_name",
            "value": self.blob_name,
        }


class User(Resource):
    """A resource that represents a user."""

    def __init__(self, user_id: str):
        self.user_id = user_id

    def to_dict(self) -> typing.Dict[str, str]:
        return {
            "@type": "type.eng.augmentcode.com/User",
            "name": "user_id",
            "value": self.user_id,
        }


class AuditLogger:
    """Class to write audit logs."""

    def __init__(self):
        self.env = os.getenv("POD_ENV", "")

    def write_audit_log(
        self,
        user_id: str | pydantic.SecretStr,
        user_id_type: str,
        tenant_name: str | None = None,
        resource: Resource | None = None,
        message: str | None = None,
    ):
        """Write a special formatted application specific audit log.

        This is a special log that is used to track user access to requests.

        Args:
            user_id: The user id of the user accessing the request.
            tenant_name: The name of the tenant the user is accessing.
            resource: The resource being accessed.
            message: The message to log.
        """
        if isinstance(user_id, pydantic.SecretStr):
            user_id = user_id.get_secret_value()  # type: ignore
        data = {
            "@type": "type.eng.augmentcode.com/AuditLog",
            "authenticationInfo": {
                "principal": user_id,
                "principal_type": user_id_type,
            },
        }
        if resource:
            data["resource"] = resource.to_dict()
        if tenant_name:
            data["tenant"] = {}
            data["tenant"]["name"] = tenant_name
        if self.env:
            data["env"] = self.env
        if message:
            data["message"] = message
        json_data = json.dumps(data)
        print(json_data, flush=True)

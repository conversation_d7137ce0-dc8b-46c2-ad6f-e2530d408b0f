package audit

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"
)

// should match auth_entities.proto for consistency
const INTERNAL_IAP = "INTERNAL_IAP"

type Printer interface {
	Println(args ...interface{})
}

type StdoutPrinter struct {
	output *sync.Mutex
}

func NewStdoutPrinter() StdoutPrinter {
	return StdoutPrinter{
		output: &sync.Mutex{},
	}
}

func (p StdoutPrinter) Println(args ...interface{}) {
	fmt.Println(args...)
	os.Stdout.Sync()
}

type AuditLogger struct {
	print Printer
	env   string
}

func NewDefaultAuditLogger() *AuditLogger {
	env := os.Getenv("POD_ENV")
	return &AuditLogger{
		print: NewStdoutPrinter(),
		env:   env,
	}
}

func (l *AuditLogger) WriteAuditLog(user_id string, user_id_type string, tenant_name string, message string) {
	data := map[string]interface{}{
		"@type": "type.eng.augmentcode.com/AuditLog",
		"authenticationInfo": map[string]string{
			"principal":      user_id,
			"principal_type": user_id_type,
		},
	}
	if tenant_name != "" {
		data["tenant"] = map[string]string{
			"name": tenant_name,
		}
	}
	if l.env != "" {
		data["env"] = l.env
	}
	if message != "" {
		data["message"] = message
	}
	json_data, _ := json.Marshal(data)
	l.print.Println(string(json_data))
}

func (l *AuditLogger) WriteAuditLogWithResource(user_id string, user_id_type string, tenant_name string, resource Resource, message string) {
	data := map[string]interface{}{
		"@type": "type.eng.augmentcode.com/AuditLog",
		"authenticationInfo": map[string]string{
			"principal":      user_id,
			"principal_type": user_id_type,
		},
	}
	if resource != nil {
		data["resource"] = resource.ToDict()
	}
	if tenant_name != "" {
		data["tenant"] = map[string]string{
			"name": tenant_name,
		}
	}
	if message != "" {
		data["message"] = message
	}
	json_data, _ := json.Marshal(data)
	l.print.Println(string(json_data))
}

type Resource interface {
	ToDict() map[string]string
}

type Request struct {
	request_id string
}

func NewRequest(request_id string) Request {
	return Request{
		request_id: request_id,
	}
}

func (r Request) ToDict() map[string]string {
	return map[string]string{
		"@type": "type.eng.augmentcode.com/Request",
		"name":  "request_id",
		"value": r.request_id,
	}
}

type Blob struct {
	blob_name string
}

func NewBlob(blob_name string) Blob {
	return Blob{
		blob_name: blob_name,
	}
}

func (b Blob) ToDict() map[string]string {
	return map[string]string{
		"@type": "type.eng.augmentcode.com/Blob",
		"name":  "blob_name",
		"value": b.blob_name,
	}
}

load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "tokenizers",
    srcs = [
        "__init__.py",
        "tokenizer.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":dbrx_tokenizer",
        ":deepseek_coder_v2_tokenizer",
        ":deepseek_tokenizer",
        ":llama3_tokenizer",
        ":qwen25coder_tokenizer",
        ":qwen3_tokenizer",
        ":rogue_tokenizer",
        ":starcoder2_tokenizer",
        ":stream_utils",
        ":tiktoken_codegen_tokenizer",
        ":tiktoken_starcoder_tokenizer",
        ":tokenizer",
    ],
)

pytest_test(
    name = "code_tokenizers_test",
    srcs = ["code_tokenizers_test.py"],
    deps = [
        ":tokenizers",
    ],
)

py_library(
    name = "tokenizer",
    srcs = [
        "tokenizer.py",
    ],
    deps = [
        "//base/registry",
    ],
)

py_library(
    name = "test_utils",
    testonly = True,
    srcs = ["test_utils.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":tokenizer",
    ],
)

genrule(
    name = "starcoder_copy",
    srcs = ["@starcoder_base_vocab//:all"],
    outs = ["starcoder_vocab.json"],
    cmd = "cp external/starcoder_base_vocab/vocab.json \"$@\"",
)

genrule(
    name = "starcoder2_copy",
    srcs = ["@starcoder2_base_vocab//:all"],
    outs = ["starcoder2_vocab.json"],
    cmd = "cp external/starcoder2_base_vocab/vocab.json \"$@\"",
)

genrule(
    name = "gpt2_copy",
    srcs = ["@openai_gpt2_vocab//file"],
    outs = ["gpt2-merges.txt"],
    cmd = "cp external/openai_gpt2_vocab/file/downloaded \"$@\"",
)

genrule(
    name = "deepseek_coder_instruct_copy",
    srcs = ["@deepseek_coder_instruct//:all"],
    outs = ["deepseek_coder_instruct_tokenizer.json"],
    cmd = "cp external/deepseek_coder_instruct/tokenizer.json \"$@\"",
)

genrule(
    name = "dbrx_instruct_copy",
    srcs = ["@dbrx_instruct_vocab//:all"],
    outs = ["dbrx_instruct_vocab.json"],
    cmd = "cp external/dbrx_instruct_vocab/tokenizer.json \"$@\"",
)

genrule(
    name = "llama3_instruct_copy",
    srcs = ["@llama3_instruct//:all"],
    outs = ["llama3_instruct_tokenizer.json"],
    cmd = "cp external/llama3_instruct/tokenizer.json \"$@\"",
)

genrule(
    name = "deepseek_coder_v2_base_vocab_copy",
    srcs = ["@deepseek_coder_v2_base_vocab//:all"],
    outs = ["deepseek_coder_v2_base_vocab.json"],
    cmd = "cp external/deepseek_coder_v2_base_vocab/tokenizer.json \"$@\"",
)

genrule(
    name = "qwen25coder_vocab_copy",
    srcs = ["@qwen25coder_vocab//:all"],
    outs = ["qwen25coder_vocab.json"],
    cmd = "cp external/qwen25coder_vocab/tokenizer.json \"$@\"",
)

genrule(
    name = "qwen3_vocab_copy",
    srcs = ["@qwen3_vocab//:all"],
    outs = ["qwen3_vocab.json"],
    cmd = "cp external/qwen3_vocab/tokenizer.json \"$@\"",
)

py_library(
    name = "starcoder_common",
    srcs = [
        "starcoder_common.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":data_gym",
    ],
)

py_library(
    name = "tiktoken_starcoder_tokenizer",
    srcs = [
        "starcoder_special_tokens.json",
        "tiktoken_starcoder_tokenizer.py",
    ],
    data = [
        ":starcoder_copy",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":core_bpe",
        ":starcoder_common",
        ":tokenizer",
    ],
)

pytest_test(
    name = "tiktoken_starcoder_tokenizer_test",
    srcs = ["tiktoken_starcoder_tokenizer_test.py"],
    data = [
    ],
    deps = [
        ":tiktoken_starcoder_tokenizer",
    ],
)

py_library(
    name = "starcoder2_tokenizer",
    srcs = [
        "starcoder2_special_tokens.json",
        "starcoder2_tokenizer.py",
    ],
    data = [
        ":starcoder2_copy",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":core_bpe",
        ":starcoder_common",
        ":tokenizer",
    ],
)

pytest_test(
    name = "starcoder2_tokenizer_test",
    srcs = ["starcoder2_tokenizer_test.py"],
    data = [
    ],
    deps = [
        ":starcoder2_tokenizer",
    ],
)

py_library(
    name = "rogue_tokenizer",
    srcs = [
        "rogue_tokenizer.py",
        "starcoder_special_tokens.json",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":tiktoken_starcoder_tokenizer",
    ],
)

pytest_test(
    name = "rogue_tokenizer_test",
    srcs = ["rogue_tokenizer_test.py"],
    deps = [
        ":rogue_tokenizer",
    ],
)

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    data = [
        ":tiktoken_so",
        "@dbrx_instruct_vocab//:all",
        "@deepseek_coder_instruct//:all",
        "@deepseek_coder_v2_base_vocab//:all",
        "@llama3_instruct//:all",
        "@openai_gpt2_vocab//file",
        "@qwen25coder_vocab//:all",
        "@qwen3_vocab//:all",
        "@starcoder2_base_vocab//:all",
        "@starcoder_base_vocab//:all",
    ],
    visibility = ["//visibility:public"],
)

py_library(
    name = "data_gym",
    srcs = ["data_gym.py"],
)

pytest_test(
    name = "data_gym_test",
    srcs = ["data_gym_test.py"],
    deps = [":data_gym"],
)

py_library(
    name = "tiktoken_codegen_tokenizer",
    srcs = [
        "codegen_special_tokens.json",
        "tiktoken_codegen_tokenizer.py",
    ],
    data = [
        "gpt2_copy",
    ],
    deps = [
        ":core_bpe",
        ":data_gym",
        ":tokenizer",
    ],
)

py_library(
    name = "deepseek_tokenizer",
    srcs = [
        "deepseek_tokenizer.py",
    ],
    data = [
        ":deepseek_coder_instruct_copy",
    ],
    deps = [
        ":core_bpe",
        ":data_gym",
        ":tokenizer",
    ],
)

pytest_test(
    name = "deepseek_tokenizer_test",
    srcs = ["deepseek_tokenizer_test.py"],
    deps = [
        ":deepseek_tokenizer",
    ],
)

py_library(
    name = "deepseek_coder_v2_tokenizer",
    srcs = [
        "deepseek_coder_v2_tokenizer.py",
    ],
    data = [
        ":deepseek_coder_v2_base_vocab_copy",
    ],
    deps = [
        ":core_bpe",
        ":data_gym",
        ":tokenizer",
    ],
)

pytest_test(
    name = "deepseek_coder_v2_tokenizer_test",
    srcs = ["deepseek_coder_v2_tokenizer_test.py"],
    deps = [
        ":deepseek_coder_v2_tokenizer",
    ],
)

py_library(
    name = "dbrx_tokenizer",
    srcs = [
        "dbrx_tokenizer.py",
    ],
    data = [
        ":dbrx_instruct_copy",
    ],
    deps = [
        ":core_bpe",
        ":data_gym",
        ":tokenizer",
    ],
)

pytest_test(
    name = "dbrx_tokenizer_test",
    srcs = ["dbrx_tokenizer_test.py"],
    deps = [
        ":dbrx_tokenizer",
    ],
)

py_library(
    name = "llama3_tokenizer",
    srcs = [
        "llama3_tokenizer.py",
    ],
    data = [
        ":llama3_instruct_copy",
    ],
    deps = [
        ":core_bpe",
        ":data_gym",
        ":tokenizer",
    ],
)

pytest_test(
    name = "llama3_tokenizer_test",
    srcs = ["llama3_tokenizer_test.py"],
    deps = [
        ":llama3_tokenizer",
    ],
)

py_library(
    name = "qwen25coder_tokenizer",
    srcs = [
        "qwen25coder_tokenizer.py",
    ],
    data = [
        ":qwen25coder_vocab_copy",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":core_bpe",
        ":data_gym",
        ":tokenizer",
    ],
)

pytest_test(
    name = "qwen25coder_tokenizer_test",
    srcs = ["qwen25coder_tokenizer_test.py"],
    deps = [
        ":qwen25coder_tokenizer",
    ],
)

py_library(
    name = "qwen3_tokenizer",
    srcs = [
        "qwen3_tokenizer.py",
    ],
    data = [
        ":qwen3_vocab_copy",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":core_bpe",
        ":data_gym",
        ":tokenizer",
    ],
)

pytest_test(
    name = "qwen3_tokenizer_test",
    srcs = ["qwen3_tokenizer_test.py"],
    deps = [
        ":qwen3_tokenizer",
    ],
)

py_binary(
    name = "detokenize_text",
    srcs = ["detokenize_text.py"],
    deps = [":tokenizers"],
)

py_binary(
    name = "tokenize_text",
    srcs = [
        "tokenize_text.py",
    ],
    deps = [":tokenizers"],
)

genrule(
    name = "tiktoken_so",
    srcs = ["//third_party/tiktoken:tiktoken_so"],
    outs = ["tiktoken.so"],
    cmd = "cp $< $@",
)

py_library(
    name = "_tiktoken",
    srcs = [],
    data = [":tiktoken_so"],
)

py_library(
    name = "core_bpe",
    srcs = ["core_bpe.py"],
    visibility = [
        "//base/tokenizers:__subpackages__",
    ],
    deps = [
        ":_tiktoken",
        "//base/feature_flags:feature_flags_py",
    ],
)

py_library(
    name = "stream_utils",
    srcs = ["stream_utils.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":tokenizer",
    ],
)

pytest_test(
    name = "stream_utils_test",
    srcs = ["stream_utils_test.py"],
    deps = [
        ":stream_utils",
        ":test_utils",
    ],
)

"""Tiktoken-based tokenizer for Qwen3."""

import json
import pathlib
from pathlib import Path
from typing import Mapping, Sequence, Tuple

from base.tokenizers import data_gym
from base.tokenizers.core_bpe import CoreBPE
from base.tokenizers.tokenizer import (
    REGISTRY,
    NextEditGenSpecialTokens,
    RetrievalSpecialTokens,
    Tokenizer,
)


def _read_special(special_token_path: Path) -> list[Tuple[str, int]]:
    with special_token_path.open(encoding="utf-8") as token_file:
        tokens = json.load(token_file)
    return tokens


class Qwen3SpecialTokens(NextEditGenSpecialTokens, RetrievalSpecialTokens):
    """Special tokens for the Qwen3 model."""

    def __init__(self, tokenizer: Tokenizer):
        super().__init__()
        vocab = tokenizer.vocab

        # Generic special tokens
        self.padding: int = vocab[b"<|padding|>"]
        self.newline: int = vocab[b"\n"]

        # Chat Special Tokens
        self.im_start: int = 151644  # <|im_start|>
        self.im_end: int = 151645  # <|im_end|>

        # Rag Special Tokens
        self.skip: int = vocab[b"<|skip|>"]
        self.pause: int = vocab[b"<|pause|>"]
        self.retrieval_section: int = vocab[b"<|retrieval_section|>"]
        self.ret_start: int = vocab[b"<|ret_start|>"]
        self.ret_body: int = vocab[b"<|ret_body|>"]
        self.prefix_body: int = vocab[b"<|prefix_body|>"]
        self.nearby_prefix: int = vocab[b"<|nearby_prefix|>"]
        self.nearby_suffix: int = vocab[b"<|nearby_suffix|>"]
        self.sig_lookup: int = vocab[b"<|sig_lookup|>"]
        self.sig_begin: int = vocab[b"<|sig_begin|>"]
        self.sig_end: int = vocab[b"<|sig_end|>"]
        self.signature_section: int = vocab[b"<|signature_section|>"]
        self.far_prefix: int = vocab[b"<|far_prefix|>"]
        self.far_suffix: int = vocab[b"<|far_suffix|>"]

        # Qwen3 Special Tokens
        self.eos: int = vocab[b"<|endoftext|>"]
        self.fim_prefix: int = vocab[b"<|fim_prefix|>"]
        self.fim_middle: int = vocab[b"<|fim_middle|>"]
        self.fim_suffix: int = vocab[b"<|fim_suffix|>"]
        self.fim_pad: int = vocab[b"<|fim_pad|>"]
        self.reponame: int = vocab[b"<|repo_name|>"]
        self.filename: int = vocab[b"<|file_sep|>"]
        self.tool_call: int = vocab[b"<tool_call>"]
        self.tool_call_end: int = vocab[b"</tool_call>"]
        self.tool_response: int = vocab[b"<tool_response>"]
        self.tool_response_end: int = vocab[b"</tool_response>"]
        self.think: int = vocab[b"<think>"]
        self.think_end: int = vocab[b"</think>"]

        # Next Edit Tokens
        self.instruction: int = vocab[b"<|instruction|>"]
        self.selected_code: int = vocab[b"<|selected_code|>"]
        self.diff_section: int = vocab[b"<|diff_section|>"]
        self.diff_hunk: int = vocab[b"<|diff_hunk|>"]
        self.has_change: int = vocab[b"<|has_change|>"]
        self.no_change: int = vocab[b"<|no_change|>"]

        # Retrieval Special Tokens
        self.start_of_key: int = vocab[b"<|ret_startofkey|>"]
        self.end_of_query: int = vocab[b"<|ret_endofquery|>"]
        self.end_of_key: int = vocab[b"<|ret_endofkey|>"]

        # Preference Token
        self.good: int = vocab[b"<|good_example|>"]
        self.bad: int = vocab[b"<|bad_example|>"]
        self.reward_signal: int = vocab[b"<|reward-signal|>"]

        self.begin_sequence = ()


def _get_additional_special_tokens():
    return [
        "<|padding|>",
        "<|skip|>",
        "<|pause|>",
        "<|retrieval_section|>",
        "<|ret_start|>",
        "<|ret_body|>",
        "<|prefix_body|>",
        "<|nearby_prefix|>",
        "<|nearby_suffix|>",
        "<|sig_lookup|>",
        "<|sig_begin|>",
        "<|sig_end|>",
        "<|signature_section|>",
        "<|far_prefix|>",
        "<|far_suffix|>",
        "<|instruction|>",
        "<|selected_code|>",
        "<|diff_section|>",
        "<|diff_hunk|>",
        "<|has_change|>",
        "<|no_change|>",
        "<|good_example|>",
        "<|bad_example|>",
        "<|ret_startofkey|>",
        "<|ret_endofquery|>",
        "<|ret_endofkey|>",
        "<|reward-signal|>",
    ]


def _read_vocab():
    """Read the vocab file."""
    module_dir = pathlib.Path(__file__).parent
    vocab_file = pathlib.Path(module_dir, "qwen3_vocab.json")
    assert vocab_file.exists() and vocab_file.is_file(), f"{vocab_file} does not exist."
    with vocab_file.open(encoding="utf-8") as vocab_file:
        all_contents = json.load(vocab_file)
        vocab: dict[str, int] = all_contents["model"]["vocab"]
        special_tokens: dict[str, int] = {
            x["content"]: x["id"] for x in all_contents["added_tokens"]
        }
        set_of_vocab_ids = set(vocab.values())
        set_of_all_ids = set(vocab.values()) | set(special_tokens.values())
        assert len(set_of_vocab_ids) == len(vocab)
        assert len(set_of_all_ids) == len(vocab) + len(special_tokens)
        assert 0 in set_of_vocab_ids and len(set_of_vocab_ids) - 1 in set_of_vocab_ids
        assert 0 in set_of_all_ids and len(set_of_all_ids) - 1 in set_of_all_ids
    # Update the special tokens
    for token in _get_additional_special_tokens():
        special_tokens[token] = len(vocab) + len(special_tokens)
    # Byte sequences corresponding to tokens are data gym in vocab.json
    decoder = data_gym.DataGymDecoder()
    decode_data_gym = decoder.decode
    bpe_ranks: dict[bytes, int] = {}
    for gym, token_id in vocab.items():
        bpe_ranks[decode_data_gym(gym)] = token_id
    special_ranks: dict[str, int] = {}
    for token, token_id in special_tokens.items():
        assert token not in special_ranks
        special_ranks[token] = token_id
    return bpe_ranks, special_ranks


# Copied from https://huggingface.co/Qwen/Qwen3-32B/blob/main/tokenizer.json
PAT_STR = r"""(?i:'s|'t|'re|'ve|'m|'ll|'d)|[^\r\n\p{L}\p{N}]?\p{L}+|\p{N}| ?[^\s\p{L}\p{N}]+[\r\n]*|\s*[\r\n]+|\s+(?!\S)|\s+"""


class Qwen3Tokenizer(Tokenizer):
    """Tokenizer for the pre-processing based on the Tiktoken tokenizer."""

    _vocab: dict[bytes, int]

    def __init__(self):
        """Initialize the tokenizer from the JSON vocab file."""
        basic_vocab, special_tokens = _read_vocab()
        self.core_bpe = CoreBPE(basic_vocab, special_tokens, PAT_STR)

        self._vocab_bytes2id = {b: v for (b, v) in basic_vocab.items()}
        for token, _id in special_tokens.items():
            cur_bytes = token.encode()
            if cur_bytes in self._vocab_bytes2id:
                raise ValueError(
                    f"The token {token} ({cur_bytes}) is already in the vocab as"
                    f" {self._vocab_bytes2id[cur_bytes]}. Please check the vocab file."
                )
            self._vocab_bytes2id[cur_bytes] = _id
        self._raw_basic_vocab = basic_vocab
        self._raw_special_tokens = special_tokens
        self._special_set = set(special_tokens.keys())
        self._special_tokens = Qwen3SpecialTokens(self)

    @property
    def vocab_size(self):
        return len(self._vocab_bytes2id)

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return self._vocab_bytes2id

    def tokenize_safe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=set())

    def tokenize_unsafe(self, text: str) -> list[int]:
        """Tokenizes a text string into a list of tokens."""
        return self.core_bpe.encode(text, allowed_special=self._special_set)

    def detokenize(self, token_ids: Sequence[int]) -> str:
        """Detokenizes a list of tokens into a string."""
        return self.core_bpe.decode_bytes(token_ids).decode(errors="replace")

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        """Detokenizes a list of tokens into a string with start character offsets."""
        ret, offsets = self.core_bpe.decode_with_offsets(token_ids)
        return ret, offsets

    @property
    def special_tokens(self) -> Qwen3SpecialTokens:
        """Returns the special tokens for Qwen3."""
        return self._special_tokens


REGISTRY.add("qwen3", Qwen3Tokenizer)

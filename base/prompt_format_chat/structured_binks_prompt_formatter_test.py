"""Tests for the Binks prompt formatter.

pytest base/prompt_format_chat/structured_binks_prompt_formatter_test.py
"""

import pytest

from base import tokenizers
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
)
from base.prompt_format_chat.lib.token_counter import TokenizerBasedTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    ExceedContextLength,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer

token_apportionment_with_retrieval = ChatTokenApportionment(
    path_len=256,
    prefix_len=1024,
    chat_history_len=2048,
    suffix_len=1024,
    retrieval_len=-1,  # unlimited retrieval
    retrieval_len_per_each_user_guided_file=2000,
    retrieval_len_for_user_guided=3000,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    # Deprecated fields
    message_len=-1,
    selected_code_len=-1,
)
token_apportionment_with_retrieval_as_tool_call = ChatTokenApportionment(
    path_len=256,
    prefix_len=1024,
    chat_history_len=2048,
    suffix_len=1024,
    retrieval_len=-1,  # unlimited retrieval
    retrieval_len_per_each_user_guided_file=2000,
    retrieval_len_for_user_guided=3000,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    # Deprecated fields
    message_len=-1,
    selected_code_len=-1,
    retrieval_as_tool=True,
)

EXPECTED_SYSTEM_PROMPT = """You are Augment, an AI code assistant developed by Augment Code.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.
- When referencing a file in your response, always include the FULL file path.
- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.

Whenever you write a codeblock, you MUST follow these instructions:

1. Code Excerpts: When showing a code excerpt from an existing file, always include both `path=` and `mode=EXCERPT`. Example:

```python path=foo/bar.py mode=EXCERPT
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
```

2. New or Modified Code: For new code or edits to existing code, always include path= and use type=EDIT. Example:

```python path=foo/bar.py mode=EDIT
print("hello world")
```"""


def test_binks_with_history_no_code(example_history_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, None)

    prompt = prompter.format_prompt(example_history_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    assert prompt.chat_history == [
        Exchange(
            request_message="What functions are there in this file?",
            response_text="This file has one function, some_function(a,b)",
        ),
        Exchange(
            request_message="Is this code valid?",
            response_text="No, the function some_function is missing an implementation.",
        ),
    ]

    assert prompt.message == "How could we make this code run?"


def test_binks_prefix_suffix(example_basic_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, None)

    prompt = prompter.format_prompt(example_basic_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I have the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
print(x)

```

""",
            response_text="Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.",
        )
    ]
    assert prompt.message == "fix bugs"


def test_binks_prefix_suffix_current_file_in_retrieval(
    example_basic_input: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
        inject_current_file_into_retrievals=True,
    )
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(example_basic_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I have the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
print(x)

```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        )
    ]
    assert prompt.message == "fix bugs"


def test_binks_prefix_suffix_current_file_in_retrieval_as_tool_call(
    example_basic_input: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
        inject_current_file_into_retrievals=True,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_basic_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""The user has the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
print(x)

```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
            request_id=None,
        )
    ]
    assert prompt.message == "fix bugs"


def test_binks_w_retrieval(example_retrieval_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter, token_apportionment_with_retrieval
    )

    prompt = prompter.format_prompt(example_retrieval_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    assert prompt.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
        Exchange(
            request_message="""I have the file `src/example.py` open and has selected part of the code.

Here is the full file:

```
import pathlib
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
    print(x)

```

Here is the selected code:

```
file = pathlib.Path("foo")
for x in file.open():
```

""",
            response_text="Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.",
        ),
    ]

    assert prompt.message == "fix bugs"


def test_binks_w_retrieval_as_tool_call(example_retrieval_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment_with_retrieval_as_tool_call,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_retrieval_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 3

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    # Third exchange - current file info
    assert (
        actual_history[2].request_message
        == """The user has the file `src/example.py` open and has selected part of the code.

Here is the full file:

```
import pathlib
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
    print(x)

```

Here is the selected code:

```
file = pathlib.Path("foo")
for x in file.open():
```

"""
    )
    assert (
        actual_history[2].response_text
        == "Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction."
    )

    assert prompt.message == "fix bugs"


def test_binks_w_retrieval_no_selected_code(
    example_retrieval_input_no_selected_code: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter, token_apportionment_with_retrieval
    )

    prompt = prompter.format_prompt(example_retrieval_input_no_selected_code)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    assert prompt.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
        Exchange(
            request_message="""I have the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
import os

```

""",
            response_text="Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.",
        ),
    ]

    assert prompt.message == "fix bugs"


def test_binks_w_retrieval_as_tool_call_no_selected_code(
    example_retrieval_input_no_selected_code: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment_with_retrieval_as_tool_call,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_retrieval_input_no_selected_code)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 3

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    # Third exchange - current file info
    assert (
        actual_history[2].request_message
        == """The user has the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
import os

```

"""
    )
    assert (
        actual_history[2].response_text
        == "Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction."
    )

    assert prompt.message == "fix bugs"


def test_binks_w_retrieval_no_selected_code_current_file_in_retrieval(
    example_retrieval_input_no_selected_code: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=3000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
        inject_current_file_into_retrievals=True,
    )
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(example_retrieval_input_no_selected_code)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    assert prompt.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

I have the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
import os

```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]

    assert prompt.message == "fix bugs"


def test_binks_w_retrieval_as_tool_call_no_selected_code_current_file_in_retrieval(
    example_retrieval_input_no_selected_code: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=3000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
        inject_current_file_into_retrievals=True,
        retrieval_as_tool=True,
    )
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_retrieval_input_no_selected_code)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

The user has the file `src/example.py` open. Here is an excerpt from the file:

```
import pathlib
import os

```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "fix bugs"


def test_binks_w_retrieval_without_retrieval_budget(
    example_retrieval_input: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, None)

    prompt = prompter.format_prompt(example_retrieval_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I have the file `src/example.py` open and has selected part of the code.

Here is the full file:

```
import pathlib
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
    print(x)

```

Here is the selected code:

```
file = pathlib.Path("foo")
for x in file.open():
```

""",
            response_text="Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.",
        ),
    ]

    assert prompt.message == "fix bugs"


def test_binks_w_retrieval_no_code(example_retrieval_no_code_input: ChatPromptInput):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter, token_apportionment_with_retrieval
    )

    prompt = prompter.format_prompt(example_retrieval_no_code_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    assert prompt.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_binks_w_retrieval_as_tool_call_no_code(
    example_retrieval_no_code_input: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment_with_retrieval_as_tool_call,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_retrieval_no_code_input)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval(
    example_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter, token_apportionment_with_retrieval
    )

    prompt = prompter.format_prompt(example_user_guided_retrieval)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval_as_tool_call(
    example_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment_with_retrieval_as_tool_call,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_user_guided_retrieval)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """The user currently has the file `src/sum.py` open and is actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

The user currently has the file `src/squared.py` open and is actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval_same_file_in_diff_retrievals(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter, token_apportionment_with_retrieval
    )

    prompt = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

I currently have the file `src/foo.py` open, and I am actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval_as_tool_call_same_file_in_diff_retrievals(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment_with_retrieval_as_tool_call,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """The user currently has the file `src/sum.py` open and is actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

The user currently has the file `src/squared.py` open and is actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

The user currently has the file `src/foo.py` open and is actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_empty_chunks(
    example_empty_chunks: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter, token_apportionment_with_retrieval
    )

    prompt = prompter.format_prompt(example_empty_chunks)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    assert prompt.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_empty_chunks_tool_call_retrieval(
    example_empty_chunks: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment_with_retrieval_as_tool_call,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_empty_chunks)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_example_only_user_guided_retrieval(
    example_only_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter, token_apportionment_with_retrieval
    )

    prompt = prompter.format_prompt(example_only_user_guided_retrieval)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

I currently have the file `src/foo.py` open, and I am actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_example_only_user_guided_retrieval_as_tool_call(
    example_only_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment_with_retrieval_as_tool_call,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_only_user_guided_retrieval)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """The user currently has the file `src/sum.py` open and is actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

The user currently has the file `src/squared.py` open and is actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

The user currently has the file `src/foo.py` open and is actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_example_only_user_guided_retrieval_no_ug_total_budget(
    example_only_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")

    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(example_only_user_guided_retrieval)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == []
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_example_only_user_guided_retrieval_as_tool_call_no_ug_total_budget(
    example_only_user_guided_retrieval: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")

    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
        retrieval_as_tool=True,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(example_only_user_guided_retrieval)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == []
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval_no_ug_total_budget(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval_as_tool_call_no_ug_total_budget(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=-1,  # unlimited retrieval
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=0,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
        retrieval_as_tool=True,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """Below are some relevant files from the user's project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval_no_regular_retrieval_budget(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I currently have the file `src/sum.py` open, and I am actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

I currently have the file `src/squared.py` open, and I am actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

I currently have the file `src/foo.py` open, and I am actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        ),
    ]
    assert prompt.message == "What kind of aggregate functions do we have?"


def test_user_guided_retrieval_as_tool_call_no_regular_retrieval_budget(
    example_user_guided_retrieval_same_file_in_diff_retrievals: ChatPromptInput,
):
    """This is a simple sanity check to catch obvious bugs in the Binks's prompt formatting."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
        retrieval_as_tool=True,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(
        token_counter,
        token_apportionment,
        retrieval_section_version=4,
    )

    prompt = prompter.format_prompt(
        example_user_guided_retrieval_same_file_in_diff_retrievals
    )

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    # Get actual chat history and verify structure while ignoring tool_use_id
    actual_history = list(prompt.chat_history)
    assert len(actual_history) == 2

    # First exchange - tool use
    assert len(actual_history[0].request_message) == 0
    assert len(actual_history[0].response_text) == 1
    tool_use = actual_history[0].response_text[0]
    assert isinstance(tool_use, ChatResultNode)
    assert tool_use.type == ChatResultNodeType.TOOL_USE
    assert tool_use.content == ""
    assert tool_use.tool_use
    assert tool_use.tool_use.name == "codebase-retrieval"
    assert tool_use.tool_use.input == {
        "information_request": "Find relevant code in the code base."
    }
    # Don't check tool_use_id as it's randomly generated

    # Second exchange - tool result
    assert len(actual_history[1].request_message) == 1
    assert len(actual_history[1].response_text) == 0
    tool_result = actual_history[1].request_message[0]
    assert isinstance(tool_result, ChatRequestNode)
    assert tool_result.type == ChatRequestNodeType.TOOL_RESULT
    assert tool_result.tool_result_node
    assert (
        tool_result.tool_result_node.content
        == """The user currently has the file `src/sum.py` open and is actively working on it. Here is an excerpt from it:

```
x = 5
y = 2
print(x + y)
...
```

The user currently has the file `src/squared.py` open and is actively working on it. Here is an excerpt from it:

```
numbers = [1, 2, 3, 4, 5]
squared_numbers = [n ** 2 for n in numbers]
print(squared_numbers)
...
```

The user currently has the file `src/foo.py` open and is actively working on it. Here is an excerpt from it:

```
# You can aggregate
# with a pooling function.
...
```

"""
    )
    assert not tool_result.tool_result_node.is_error
    # Don't check tool_use_id as it should match the random one from above

    assert prompt.message == "What kind of aggregate functions do we have?"


def test_selected_code_in_history(example_selected_code_in_history: ChatPromptInput):
    """Tests the case selected code was highlighted on a previous exchange."""

    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=2048,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(example_selected_code_in_history)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT

    assert prompt.chat_history == [
        Exchange(
            request_message="Hey, Augment!",
            response_text="Hey!",
            request_id="request_id1",
        ),
        Exchange(
            request_message="""I have the file `/path/to/file.py` open and has selected part of the code.

Here is the full file:

```
some_prefix
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
some_suffix
```

Here is the selected code:

```
some_function(a,b)
```

Tell me about this function""",
            response_text="This function that does ...",
            request_id="request_id2",
        ),
        Exchange(
            request_message="You sure?",
            response_text="Yes, I am.",
            request_id="request_id3",
        ),
    ]

    assert prompt.message == "How could we make this code run?"


def test_selected_code_in_message(example_new_selected_code: ChatPromptInput):
    """When selected code is different from previous exchange, it should be in the message."""

    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=100,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(example_new_selected_code)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="Hey, Augment!",
            response_text="Hey!",
            request_id="request_id1",
        ),
    ]
    assert (
        prompt.message
        == """I have the file `/path/to/file.py` open and has selected part of the code.

Here is the full file:

```
some_prefix
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
some_suffix
```

Here is the selected code:

```
some_function(a,b)
```

How could we make this code run?"""
    )


def test_selected_code_in_separate_conversation_turn(
    example_selected_code_in_history: ChatPromptInput,
):
    """When exchange with selected code doesn't fit into history, it should be in a separate conversation turn before history."""

    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1024,
        chat_history_len=10,
        suffix_len=1024,
        retrieval_len=0,
        retrieval_len_per_each_user_guided_file=2000,
        retrieval_len_for_user_guided=2000,
        max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    prompt = prompter.format_prompt(example_selected_code_in_history)

    assert prompt.system_prompt == EXPECTED_SYSTEM_PROMPT
    assert prompt.chat_history == [
        Exchange(
            request_message="""I have the file `/path/to/file.py` open and has selected part of the code.

Here is the full file:

```
some_prefix
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]
some_suffix
```

Here is the selected code:

```
some_function(a,b)
```

""",
            response_text="Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.",
        ),
        Exchange(
            request_message="You sure?",
            response_text="Yes, I am.",
            request_id="request_id3",
        ),
    ]
    assert prompt.message == "How could we make this code run?"


def test_binks_code_too_long(example_long_code_input: ChatPromptInput):
    """Test that large message results in proper gRPC error."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, None)

    with pytest.raises(ExceedContextLength):
        prompter.format_prompt(example_long_code_input)


def test_binks_message_too_long(example_long_message_input: ChatPromptInput):
    """Test that large message results in proper gRPC error."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, None)

    with pytest.raises(ExceedContextLength):
        prompter.format_prompt(example_long_message_input)


def test_structured_binks_prompt_formatter_with_custom_prompts():
    """Test that user system prompt is incorporated into the system prompt."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, None)

    prompt_input = ChatPromptInput(
        message="Test message",
        path="test/path.py",
        prefix="def test_function():\n    ",
        selected_code="pass",
        suffix="\n\nprint('Test')",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        user_guidelines="Always use camelCase for variable names.",
        workspace_guidelines="Always use snake_case for variable names.",
    )

    prompt_output = prompter.format_prompt(prompt_input)

    assert prompt_output.system_prompt
    assert (
        "Additional user rules:\nAlways use camelCase for variable names."
        in prompt_output.system_prompt
    )
    assert (
        "Additional workspace rules:\nAlways use snake_case for variable names."
        in prompt_output.system_prompt
    )


def test_structured_binks_prompt_formatter_tool_result_budget():
    """Test that tool result budget in current message is accounted for separately."""
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_counter = TokenizerBasedTokenCounter(tokenizer)

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="toolu_123",
                    content="A" * 1000,
                    is_error=False,
                    request_id="********-e6c0-406b-8c18-ffffffffffff",
                ),
            )
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        user_guidelines="Always use camelCase for variable names.",
        workspace_guidelines="Always use snake_case for variable names.",
    )

    # OK as long as tool result budget is large enough
    prompt_output = StructuredBinksPromptFormatter.create(
        token_counter,
        ChatTokenApportionment(
            path_len=100,
            prefix_len=100,
            chat_history_len=0,
            suffix_len=0,
            retrieval_len=0,
            max_prompt_len=1000,
            tool_results_len=4096,
            # Deprecated fields
            message_len=-1,
            selected_code_len=-1,
        ),
    ).format_prompt(prompt_input)
    assert prompt_output.message == prompt_input.message

    # Fail if tool result budget is not large enough
    with pytest.raises(ExceedContextLength):
        StructuredBinksPromptFormatter.create(
            token_counter,
            ChatTokenApportionment(
                path_len=100,
                prefix_len=100,
                chat_history_len=0,
                suffix_len=0,
                retrieval_len=0,
                max_prompt_len=1000,
                tool_results_len=100,
                # Deprecated fields
                message_len=-1,
                selected_code_len=-1,
            ),
        ).format_prompt(prompt_input)


def test_structured_binks_prompt_formatter_single_turn_tool_call():
    tokenizer = tokenizers.create_tokenizer_by_name("llama3_instruct")
    if not isinstance(tokenizer, Llama3InstructTokenizer):
        raise ValueError("Tokenizer must be a Llama3InstructTokenizer.")
    token_apportionment = ChatTokenApportionment(
        path_len=100,
        prefix_len=100,
        chat_history_len=2048,
        suffix_len=0,
        retrieval_len=0,
        max_prompt_len=4000,
        tool_results_len=1000,
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )
    token_counter = TokenizerBasedTokenCounter(tokenizer)
    prompter = StructuredBinksPromptFormatter.create(token_counter, token_apportionment)

    chat_history = [
        Exchange(
            request_message="What is the meaning of life?",
            response_text=[
                ChatResultNode(
                    id=0,
                    type=ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=ChatResultToolUse(
                        name="ask_for_codebase_snippets",
                        input={"description": "meaning of life"},
                        tool_use_id="toolu_123",
                    ),
                ),
                ChatResultNode(
                    id=1,
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content="Searching the codebase for the meaning of life.",
                    tool_use=None,
                ),
            ],
            request_id="request_id1",
        )
    ]

    prompt_input = ChatPromptInput(
        message=[
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="toolu_123",
                    content="The meaning of life is 42.",
                    is_error=False,
                ),
            )
        ],
        path="test/path.py",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=chat_history,
        prefix_begin=0,
        suffix_end=100,
        retrieved_chunks=[],
        user_guidelines="Always use camelCase for variable names.",
        workspace_guidelines="Always use snake_case for variable names.",
    )

    prompt_output = prompter.format_prompt(prompt_input)
    assert prompt_output.chat_history == chat_history

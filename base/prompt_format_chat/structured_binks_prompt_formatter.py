"""The Binks prompt formatter for the code chat."""

import dataclasses
from typing import Callable, Optional

from base.prompt_format.common import (
    ChatRequestNodeType,
    Exchange,
    get_request_message_as_text,
)
from base.prompt_format_chat.lib.chat_history_builder import (
    format_chat_history,
    inject_selected_code_into_chat_history,
    postprocess_chat_history_tool_use,
)
from base.prompt_format_chat.lib.retrieval_section_prompt_formatter_v2 import (
    RetrievalSectionBuilder,
    get_binks_retrieval_section_bulder,
)
from base.prompt_format_chat.lib.rules_prompt_builder import build_custom_prompt
from base.prompt_format_chat.lib.selected_code_prompt_formatter_v2 import (
    SelectedCodePromptFormatterV2,
)
from base.prompt_format_chat.lib.string_formatter import StringFormatter
from base.prompt_format_chat.lib.system_prompts import get_binks_system_prompt_formatter
from base.prompt_format_chat.lib.token_counter import TokenCounter
from base.prompt_format_chat.prompt_formatter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ChatPromptInput,
    ChatTokenApportionment,
    ExceedContextLength,
    StructuredChatPromptOutput,
)

_default_token_apportionent = ChatTokenApportionment(
    path_len=256,
    prefix_len=1024,
    chat_history_len=2048,
    suffix_len=1024,
    retrieval_len=0,
    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens
    # Deprecated fields
    message_len=-1,
    selected_code_len=-1,
    retrieval_as_tool=False,
)


class StructuredBinksPromptFormatter(ChatPromptFormatter[StructuredChatPromptOutput]):
    """The class formats prompts for the Binks chat model.
    It is mostly a copy of base/prompt_format_chat/binks_llama3_prompt_formatter.py using text-out
    interfaces

    Prompt Structure:
    - System Prompt
    - Chat History (prioritized based on length limits)
        - first message is retrievals
        - If selected code was selected during prior message, prepend it to that message. If
          that message was truncated, then prepend the chat history with a new conversation turn that
          includes the selected code
    - Message (included fully, without restrictions)
        - If selected code was selected right before the current message, prepend it to current message.

    Token Budget Allocation Policy:
    - 'Message' and 'Selected Code' are always fully included.
    If their combined length exceeds limits, the operation will fail rather than truncate.
    - A fixed budget is set for prefixes and suffixes.
    - Remaining tokens are first allocated to 'chat_history'.
    If a non-negative limit is set in 'token_apportionment.chat_history_len',
    it will not exceed this value.
    - After 'chat_history', tokens go to 'retrievals'.
    These are filled up to 'max_prompt_tokens' or 'token_apportionment.retrieval_len',
    if non-negative.

    Example of a formatted prompt:
    ---
    system_prompt
    ---
    You are Augment, an AI code assistant.
    Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

    When answering the developer's questions, please follow these guidelines:

    - Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
    - When referencing a file in your response, always include the FULL file path.
    - If the provided files are not enough to answer a question, politely ask the user to reformulate their question.
    ---
    message history
    ---
    user message
    ---
    Below are some relevant files from my project.

    Here is an excerpt from the file `src/bar.py`:

    ```
    # You can aggregate
    # with a maxing
    # function.
    ...
    ```

    Here is an excerpt from the file `src/foo.py`:

    ```
    # You can aggregate
    # with a pooling function.
    ...
    ```

    ---
    augment message
    ---
    Understood. I'll refer to the excerpts for context, and ignore them for general questions.
    ---
    user message
    ---
    What functions are there in this file?
    ---
    augment message
    ---
    This file has one function, some_function(a,b)
    ---
    user message
    ---
    Is this code valid?
    ---
    augment message
    ---
    No, the function some_function is missing an implementation.

    ---
    end message history
    ---

    current message:
    ---
    How could we make this code run?

    """

    def __init__(
        self,
        token_counter: TokenCounter,
        system_prompt: StringFormatter,
        retrieval_section_builder: RetrievalSectionBuilder,
        selected_code_formatter: SelectedCodePromptFormatterV2,
        token_apportionment: Optional[ChatTokenApportionment] = None,
    ):
        self.token_counter = token_counter
        # If token_apportionment is not provided, use default apportionment.
        if token_apportionment is None:
            token_apportionment = _default_token_apportionent

        assert (
            token_apportionment.message_len == -1
        ), "The message length should be -1 because it is a deprecate field not used in this formatter."
        assert (
            token_apportionment.selected_code_len == -1
        ), "The selected code length should be -1 because it is a deprecate field not used in this formatter."

        self.token_apportionment = token_apportionment
        self.selected_code_formatter = selected_code_formatter
        self.system_prompt_formatter = system_prompt
        self.retrieval_section_builder = retrieval_section_builder

    def format_prompt(
        self, prompt_input: ChatPromptInput
    ) -> StructuredChatPromptOutput:
        """Format prompt for Binks Llama3-based code chat model.

        Remember:
            - all of system prompt included
            - all of message and selected code included
            - grab prefix/suffix up to some set limits
            - grab chat history up to token_apportionment.chat_history_len tokens
            - dedicate remaining budget to retrieval

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.seq_length - max_output_token_count, in tokens.
        """

        max_prompt_len = int(self.token_apportionment.max_prompt_len * 0.975)

        # First, get all of system prompt and message
        system_prompt = self.system_prompt_formatter.format({})

        # Add custom prompts to the system prompt
        custom_prompt = build_custom_prompt(
            prompt_input.user_guidelines, prompt_input.workspace_guidelines
        )
        system_prompt += custom_prompt
        # Exempt custom prompt from token budget
        max_prompt_len += self.token_counter.count_tokens(custom_prompt)

        cur_message = prompt_input.message

        # Since tokenize(a + b) is slightly larger than tokenize(a) + tokenize(b),
        # add a small 2.5% buffer.
        system_prompt_tokens = self.token_counter.count_tokens(system_prompt)
        cur_message_tokens = self.token_counter.count_tokens_in_request(cur_message)

        # Tool results are not included in the token budget for the current message.
        cur_message_tool_result_tokens = self.token_counter.count_tokens_in_request(
            cur_message, node_type=ChatRequestNodeType.TOOL_RESULT
        )

        # Calculate image tokens if present in the message
        cur_message_image_tokens = self.token_counter.count_tokens_in_request(
            cur_message, node_type=ChatRequestNodeType.IMAGE
        )

        assert (
            cur_message_tokens
            >= cur_message_tool_result_tokens + cur_message_image_tokens
        )

        # Compute the token budget remaining after system prompt and message
        token_budget = (
            max_prompt_len
            - system_prompt_tokens
            - (
                cur_message_tokens
                - cur_message_tool_result_tokens
                - cur_message_image_tokens
            )
        )

        # We'll account for tool results budget separately.
        tool_results_token_budget = (
            self.token_apportionment.tool_results_len - cur_message_tool_result_tokens
        )

        # If we are already out of budget after just the system prompt and the current
        # message even before any chat history, raise an error
        if token_budget < 0:
            raise ExceedContextLength(
                f"The prompt is too long: message length: {cur_message_tokens} "
                f"(including {cur_message_image_tokens} image tokens), "
                f"system prompt length: {system_prompt_tokens}, "
                f"max_prompt_len: {max_prompt_len})."
            )

        # If we are already out of budget for tool results, raise an error
        if tool_results_token_budget < 0:
            raise ExceedContextLength(
                f"Tool results length {cur_message_tool_result_tokens} exceeds maximum tool results length "
                f"{self.token_apportionment.tool_results_len}."
            )

        # Get all of selected_code and clipped prefix/clipped suffix
        selected_code_section, clipped_prefix, clipped_suffix = (
            self.selected_code_formatter.format(prompt_input, token_budget)
        )
        token_budget -= self.token_counter.count_tokens(selected_code_section)

        # Next grab clipped chat history
        if token_budget > 0:
            chat_history_budget = (
                min(
                    self.token_apportionment.chat_history_len,
                    token_budget,
                )
                if self.token_apportionment.chat_history_len != -1
                else token_budget
            )

            clipped_chat_history, chat_token_ct = format_chat_history(
                chat_history=list(prompt_input.chat_history),
                token_counter=self.token_counter,
                token_budget=chat_history_budget,
            )
            token_budget -= chat_token_ct
        else:
            clipped_chat_history = []

        # When no code is selected the `selected_code_section` only contains
        # context around the cursor (prefix and suffix). Thus, `selected_code_section`
        # becomes a current file representation.
        selected_code_is_empty = len(prompt_input.selected_code) == 0

        # We render `selected_code_section` among retrieved chunks under three conditions.
        # First, the option on `place_current_file_among_retrieved_chunks` is enabled.
        # Second, selected code must be empty, so that the `selected_code_section`
        # only contains the current file.
        # Lastly, the `selected_code_section` is not empty. otherwise there is nothing to
        # render.
        inject_selected_code_section_into_retrieveds = (
            self.token_apportionment.inject_current_file_into_retrievals
            and selected_code_is_empty
            and len(selected_code_section) > 0
        )

        # If selected code is not empty, add it to cur message or chat history.
        if token_budget > 0 and not inject_selected_code_section_into_retrieveds:
            if selected_code_is_empty:
                # When no code is selected, `selected_code_section` only contains
                # context around the cursor (prefix and suffix).
                # In this case, we don't try to insert this section into a recent user
                # message, as we would try with user-selected code.
                # Instead, we always insert it as the as a separate message
                # in the chat history.
                context_code_exchange_request_id = None
            else:
                context_code_exchange_request_id = (
                    prompt_input.context_code_exchange_request_id
                )

            cur_message, clipped_chat_history = inject_selected_code_into_chat_history(
                cur_message,
                clipped_chat_history,
                selected_code_section,
                context_code_exchange_request_id,
                self.selected_code_formatter.selected_code_response_message,
            )

        clipped_chat_history = postprocess_chat_history_tool_use(
            clipped_chat_history,
            cur_message,
            self.token_counter,
            tool_results_token_budget,
        )

        # Finally dedicate remaining budget to retrieval
        retrieval_exchanges = []
        if token_budget > 0:
            retrieval_exchanges, retrieved_chunks_in_prompt = (
                self.retrieval_section_builder.get_retrieval_section_as_exchanges(
                    prompt_input,
                    clipped_prefix,
                    clipped_suffix,
                    token_budget,
                )
            )
            if inject_selected_code_section_into_retrieveds:
                # No retrieval
                if len(retrieval_exchanges) == 0:
                    retrieval_exchanges = [
                        Exchange(
                            request_message=selected_code_section,
                            response_text=self.retrieval_section_builder.retrieval_response_message,
                        )
                    ]
                # Retrieval as a user message is 1-round
                elif len(retrieval_exchanges) == 1:
                    retrieval_exchange = retrieval_exchanges[0]
                    # retrieval_exchange request message is guaranteed to be an ordinary
                    # text message, not a tool result, given that retrieval_exchange is
                    # constructed in the get_retrieval_section_as_exchange() call above.
                    #
                    # So, it is safe to call get_request_message_as_text() here.
                    message_text = get_request_message_as_text(
                        retrieval_exchange.request_message
                    )
                    request_message_with_current_file = (
                        message_text + selected_code_section
                    )
                    retrieval_exchanges = [
                        dataclasses.replace(
                            retrieval_exchange,
                            request_message=request_message_with_current_file,
                        )
                    ]
                # Retrieval as a tool call has 2 rounds
                elif len(retrieval_exchanges) == 2:
                    retrieval_call_exchange, retrieval_result_exchange = (
                        retrieval_exchanges
                    )
                    message_text = retrieval_result_exchange.request_message[
                        0
                    ].tool_result_node.content  # type: ignore
                    request_message_with_current_file = (
                        message_text + selected_code_section
                    )
                    retrieval_exchanges = [
                        retrieval_call_exchange,
                        dataclasses.replace(
                            retrieval_result_exchange,
                            request_message=[
                                dataclasses.replace(
                                    retrieval_result_exchange.request_message[0],  # type: ignore
                                    tool_result_node=dataclasses.replace(
                                        retrieval_result_exchange.request_message[
                                            0
                                        ].tool_result_node,  # type: ignore
                                        content=request_message_with_current_file,
                                    ),
                                )
                            ],
                        ),
                    ]
                else:
                    raise ValueError(
                        f"There could only be 0, 1, or 2 retrieval exchanges, but got"
                        f" {len(retrieval_exchanges)}."
                    )
                # Avoid double counting tokens in the selected code.
                token_budget += self.token_counter.count_tokens(selected_code_section)

            clipped_chat_history = retrieval_exchanges + clipped_chat_history
            for retrieval_exchange in retrieval_exchanges:
                token_budget -= self.token_counter.count_tokens_in_request(
                    retrieval_exchange.request_message
                )
                token_budget -= self.token_counter.count_tokens_in_response(
                    retrieval_exchange.response_text
                )
        else:
            retrieved_chunks_in_prompt = []

        return StructuredChatPromptOutput(
            system_prompt=system_prompt,
            chat_history=clipped_chat_history,
            message=cur_message,
            retrieved_chunks_in_prompt=retrieved_chunks_in_prompt,
            retrieval_as_tool=self.token_apportionment.retrieval_as_tool,
        )

    @classmethod
    def create(
        cls,
        token_counter: TokenCounter,
        token_apportionment: ChatTokenApportionment | None = None,
        system_prompt_factory: Callable[
            [TokenCounter], StringFormatter
        ] = get_binks_system_prompt_formatter,
        retrieval_section_version: int = 2,
    ):
        if token_apportionment is None:
            token_apportionment = _default_token_apportionent
        system_prompt = system_prompt_factory(token_counter)
        retrieval_section_builder = get_binks_retrieval_section_bulder(
            token_counter=token_counter,
            token_apportionment=token_apportionment,
            version=retrieval_section_version,
        )
        selected_code_formatter = SelectedCodePromptFormatterV2(
            token_counter=token_counter,
            max_path_tokens=token_apportionment.path_len,
            max_prefix_tokens=token_apportionment.prefix_len,
            max_suffix_tokens=token_apportionment.suffix_len,
            version=retrieval_section_version,
        )
        return StructuredBinksPromptFormatter(
            token_counter=token_counter,
            system_prompt=system_prompt,
            retrieval_section_builder=retrieval_section_builder,
            selected_code_formatter=selected_code_formatter,
            token_apportionment=token_apportionment,
        )

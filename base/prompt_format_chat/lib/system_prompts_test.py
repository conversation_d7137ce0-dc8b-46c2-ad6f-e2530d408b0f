import pytest

from base.prompt_format.common import TerminalInfo, WorkspaceFolderInfo
from base.prompt_format_chat.lib.system_prompts import (
    IdeStateInfo,
    get_agent_ide_state_prompt_formatter_v1,
)
from base.third_party_clients.token_counter.token_counter import RoughTokenCounter


@pytest.mark.parametrize(
    "ide_state_info, expected_prompt",
    [
        # No workspace folders.
        (
            IdeStateInfo(
                first_message=True,
                workspace_folders=[],
                current_terminal=None,
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The user does not have any workspace directories open.\n"
            + "When the user mentions a path, it is probably relative to their home directory.\n"
            + "Their home directory will be the current working directory when launching processes using the `launch_process` tool with `wait=false`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # Workspace and terminal first message.
        (
            IdeStateInfo(
                first_message=True,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The user's workspace is opened at `/home/<USER>/augment/clients/vscode`.\n"
            + "When the user mentions a path, it is probably relative to the workspace directory.\n"
            + "The user's workspace is part of a repository that is currently rooted at `/home/<USER>/augment`.\n"
            + "Use the repository root directory to resolve relative paths supplied to the following tools: codebase_retrieval, str_replace_editor.\n"
            + "The repository root directory will be the current working directory when launching processes using the `launch_process` tool with `wait=false`.\n"
            + "The interactive terminal's current working directory is `/home/<USER>/augment/clients/vscode`.\n"
            + "This is the current working directory used when launching processes using the `launch_process` tool with `wait=true`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # Workspace and terminal first message -- no tool names.
        (
            IdeStateInfo(
                first_message=True,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="",
                repo_relative_tools=[],
            ),
            "<supervisor>\n"
            + "The user's workspace is opened at `/home/<USER>/augment/clients/vscode`.\n"
            + "When the user mentions a path, it is probably relative to the workspace directory.\n"
            + "The user's workspace is part of a repository that is currently rooted at `/home/<USER>/augment`.\n"
            + "The interactive terminal's current working directory is `/home/<USER>/augment/clients/vscode`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # No changes.
        (
            IdeStateInfo(
                first_message=False,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "",
        ),
        # Changed workspace.
        (
            IdeStateInfo(
                first_message=False,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=True,
                current_terminal_changed=False,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The user changed workspace directories. It is now opened at `/home/<USER>/augment/clients/vscode`.\n"
            + "When the user mentions a path, it is probably relative to the workspace directory.\n"
            + "The user's workspace is part of a repository that is currently rooted at `/home/<USER>/augment`.\n"
            + "Use the repository root directory to resolve relative paths supplied to the following tools: codebase_retrieval, str_replace_editor.\n"
            + "The repository root directory will be the current working directory when launching processes using the `launch_process` tool with `wait=false`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
        # Changed terminal.
        (
            IdeStateInfo(
                first_message=False,
                workspace_folders=[
                    WorkspaceFolderInfo(
                        folder_root="/home/<USER>/augment/clients/vscode",
                        repository_root="/home/<USER>/augment",
                    )
                ],
                current_terminal=TerminalInfo(
                    terminal_id=0,
                    current_working_directory="/home/<USER>/augment/clients/vscode",
                ),
                workspace_folders_changed=False,
                current_terminal_changed=True,
                launch_process_tool_name="launch_process",
                repo_relative_tools=["codebase_retrieval", "str_replace_editor"],
            ),
            "<supervisor>\n"
            + "The interactive terminal's current working directory has changed and is now `/home/<USER>/augment/clients/vscode`.\n"
            + "This is the current working directory used when launching processes using the `launch_process` tool with `wait=true`.\n"
            + "\n"
            + "This information may or may not be relevant to the user's current request.\n"
            + "Don't repeat this information to the user.\n"
            + "</supervisor>",
        ),
    ],
)
def test_agent_ide_state_prompt_formatter_v1(
    ide_state_info: IdeStateInfo, expected_prompt: str
):
    formatter = get_agent_ide_state_prompt_formatter_v1(RoughTokenCounter())
    actual = formatter.format(ide_state_info)
    assert actual == expected_prompt

"""Utils for IDE state."""

import dataclasses

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    Exchange,
    RequestMessage,
    ToolDefinition,
)
from base.prompt_format_chat.lib.system_prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IdeStateInfo
from base.prompt_format_chat.lib.token_counter import Token<PERSON><PERSON>nter
from base.prompt_format_chat.prompt_formatter import ChatPromptInput

# NOTE(arun): These are tool names for the launch process tool and tools that need the
# repo. We really want to send these from the client, but that's hard and in the mean
# time we can validate against the tools that the client sends.
WELL_KNOWN_LAUNCH_PROCESS_TOOLS = {"launch-process"}
WELL_KNOWN_REPO_AWARE_TOOLS = {"save-file", "codebase-retrieval", "str-replace-editor"}


def update_ide_state_info(
    exchange_or_message: Exchange | RequestMessage,
    ide_state_info: IdeStateInfo,
) -> IdeStateInfo:
    """Update the IDE state info with the given exchange or message."""
    if isinstance(exchange_or_message, Exchange):
        ide_state_node = next(
            (
                node.ide_state_node
                for node in exchange_or_message.request_nodes
                if node.type == ChatRequestNodeType.IDE_STATE
                and node.ide_state_node is not None
            ),
            None,
        )
    elif not isinstance(exchange_or_message, str):
        ide_state_node = next(
            (
                node.ide_state_node
                for node in exchange_or_message
                if node.type == ChatRequestNodeType.IDE_STATE
                and node.ide_state_node is not None
            ),
            None,
        )
    else:
        ide_state_node = None

    # Create a copy
    ide_state_info = IdeStateInfo(ide_state_info)

    if ide_state_node is None:
        # If we didn't get an IDE state node, nothing changed from previous turns.
        # The one case we'll want to handle is unsetting first_message.
        if ide_state_info["first_message"]:
            ide_state_info["first_message"] = False
        return ide_state_info

    if ide_state_info["workspace_folders"] is None:
        # If the info's workspace_folders was empty, then this is the first ide state
        # node we've seen.
        ide_state_info["first_message"] = True
        ide_state_info["workspace_folders"] = ide_state_node.workspace_folders
        ide_state_info["workspace_folders_changed"] = True
        ide_state_info["current_terminal"] = ide_state_node.current_terminal
        ide_state_info["current_terminal_changed"] = (
            ide_state_node.current_terminal is not None
        )
        return ide_state_info

    ide_state_info["first_message"] = False
    ide_state_info["workspace_folders_changed"] = (
        # If the client says the folders are unchanged, then they can send an empty
        # list of folders and we won't check if the folders are different.
        not ide_state_node.workspace_folders_unchanged
        # Otherwise, we check if the folders are different.
        and ide_state_node.workspace_folders != ide_state_info["workspace_folders"]
    )
    ide_state_info["workspace_folders"] = ide_state_node.workspace_folders
    ide_state_info["current_terminal_changed"] = (
        ide_state_node.current_terminal is not None
        and ide_state_info["current_terminal"] is not None
        and ide_state_info["current_terminal"].current_working_directory
        != ide_state_node.current_terminal.current_working_directory
    )
    ide_state_info["current_terminal"] = ide_state_node.current_terminal
    return ide_state_info


def inject_ide_state_into_history(
    chat_history: list[Exchange],
    token_counter: TokenCounter,
    ide_state_prompt: IdeStateFormatter | None = None,
    tool_definitions: list[ToolDefinition] | None = None,
) -> tuple[list[Exchange], int]:
    """Injects the IDE state into the chat history.

    Args:
        chat_history: List of chat exchanges to process
        ide_state_prompt: Prompt template for IDE state. If None, just drop any IDE
          state nodes.

    Returns:
        - Updated chat history with additional text nodes introduced whenever IDE state
          changes. All other IDE state nodes are removed.
        - Total tokens consumed by the IDE state text nodes.
    """
    ide_state_info = get_empty_ide_state(tool_definitions)

    updated_history, token_ct = [], 0
    for exchange in chat_history:
        ide_state_info = update_ide_state_info(exchange, ide_state_info)
        text_content = (
            ide_state_prompt.format(ide_state_info) if ide_state_prompt else ""
        )
        token_ct += token_counter.count_tokens(text_content)
        updated_history.append(
            dataclasses.replace(
                exchange,
                request_message=inject_ide_state_in_request_message(
                    exchange.request_message, text_content
                ),
            )
        )

    return updated_history, token_ct


def inject_as_first_text_node_in_request_message(
    request_message: RequestMessage, text_content: str | None
) -> RequestMessage:
    """Injects the text content as the first text node in the request message."""
    if not text_content:
        return request_message

    if isinstance(request_message, str):
        return "\n".join([text_content, request_message])

    nodes = list(request_message)
    # Default to 1 if nodes is empty
    new_id = max((node.id for node in nodes), default=1) + 1

    # We'll insert the node before the first TEXT node or the end if there is none.
    insertion_idx = next(
        (i for (i, node) in enumerate(nodes) if node.type == ChatRequestNodeType.TEXT),
        len(nodes),
    )
    nodes.insert(
        insertion_idx,
        ChatRequestNode(
            id=new_id,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content=text_content),
            tool_result_node=None,
        ),
    )
    return nodes


def inject_ide_state_in_request_message(
    request_message: RequestMessage,
    text_content: str | None,
) -> RequestMessage:
    """Replaces IDE state in the request message by prepending a text node."""
    if isinstance(request_message, str):
        return request_message

    nodes = [
        node for node in request_message if node.type != ChatRequestNodeType.IDE_STATE
    ]

    return inject_as_first_text_node_in_request_message(nodes, text_content)


def get_latest_ide_state(request: ChatPromptInput):
    ide_state_info = get_empty_ide_state(request.tool_definitions or [])
    for exchange in request.chat_history:
        ide_state_info = update_ide_state_info(exchange, ide_state_info)
    return update_ide_state_info(request.message, ide_state_info)


def get_empty_ide_state(tool_definitions: list[ToolDefinition] | None = None):
    # Get the first tool that fits the bill for launch process (alphabetically).
    launch_process_tool_name = next(
        iter(
            sorted(
                WELL_KNOWN_LAUNCH_PROCESS_TOOLS
                & {defn.name for defn in tool_definitions or []}
            )
        ),
        "",
    )
    repo_relative_tools = sorted(
        WELL_KNOWN_REPO_AWARE_TOOLS & {defn.name for defn in tool_definitions or []}
    )

    return IdeStateInfo(
        first_message=True,
        workspace_folders=None,
        current_terminal=None,
        workspace_folders_changed=False,
        current_terminal_changed=False,
        launch_process_tool_name=launch_process_tool_name,
        repo_relative_tools=repo_relative_tools,
    )

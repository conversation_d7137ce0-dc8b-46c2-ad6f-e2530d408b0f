import functools

from vertexai.preview.tokenization import get_tokenizer_for_model

from base.prompt_format_chat.lib.token_counter import TokenCounter


class VertexTokenCounter(TokenCounter):
    def __init__(self, model_name: str):
        if model_name in [
            "gemini-pro-experimental",
            "gemini-flash-experimental",
            "gemini-2.0-flash-exp",
            "gemini-2.5-pro-exp-03-25",
        ]:
            # No tokenizer for experimental models
            model_name = "gemini-1.5-flash-001"
        self.tokenizer = get_tokenizer_for_model(model_name)

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        count_token_result = self.tokenizer.count_tokens(prompt_chars)
        return count_token_result.total_tokens

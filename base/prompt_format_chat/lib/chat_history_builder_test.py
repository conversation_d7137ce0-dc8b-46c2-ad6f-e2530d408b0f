"""Tests for the chat history formatting."""

import dataclasses
from base.prompt_format.common import (
    ChatRequestIdeState,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    get_response_message_as_text,
    TerminalInfo,
    WorkspaceFolderInfo,
)
from base.prompt_format_chat.lib.chat_history_builder import (
    format_agent_history,
    format_chat_history,
    inject_selected_code_into_chat_history,
    sanitize_known_client_faults,
)
from base.prompt_format_chat.lib.token_counter import Token<PERSON>ounter
from base.prompt_format_chat.prompt_formatter import ChatPromptInput


class TrivialTokenCounter(TokenCounter):
    def count_tokens(self, prompt_chars: str) -> int:
        return len(prompt_chars)


def test_format_chat_history_empty(example_basic_input: ChatPromptInput):
    """Test empty history."""

    exchanges, token_ct = format_chat_history(
        chat_history=list(example_basic_input.chat_history),
        token_counter=TrivialTokenCounter(),
        token_budget=1000,
    )

    assert exchanges == []
    assert token_ct == 0


def test_format_chat_history_basic(example_history_input: ChatPromptInput):
    """Test simple non-empty history, with the whole history in the output."""
    token_counter = TrivialTokenCounter()
    exchanges, token_ct = format_chat_history(
        chat_history=list(example_history_input.chat_history),
        token_counter=token_counter,
        token_budget=1000,
    )

    total_exchange_toks = sum(
        [
            token_counter.count_tokens(exchange.request_message)
            + token_counter.count_tokens(exchange.response_text)
            for exchange in example_history_input.chat_history
        ]
    )

    assert exchanges == example_history_input.chat_history
    assert token_ct == total_exchange_toks


def test_format_chat_history_truncate_last_exchange(
    example_history_input: ChatPromptInput,
):
    """Test simple non-empty history, with truncation of oldest exchange."""
    token_counter = TrivialTokenCounter()

    total_exchange_toks = sum(
        [
            token_counter.count_tokens(exchange.request_message)
            + token_counter.count_tokens(exchange.response_text)
            for exchange in list(example_history_input.chat_history)[1:]
        ]
    )

    exchanges, token_ct = format_chat_history(
        chat_history=list(example_history_input.chat_history),
        token_counter=token_counter,
        token_budget=total_exchange_toks + 5,
    )

    assert exchanges == list(example_history_input.chat_history)[1:]
    assert token_ct == total_exchange_toks


def test_format_chat_history_truncate_longer_exchange(
    example_selected_code_in_history: ChatPromptInput,
):
    """Test simple non-empty history, with truncation a few exchanges."""
    token_counter = TrivialTokenCounter()

    # Truncate last exchange (out of 3)

    total_exchange_toks = sum(
        [
            token_counter.count_tokens(exchange.request_message)
            + token_counter.count_tokens(exchange.response_text)
            for exchange in list(example_selected_code_in_history.chat_history)[1:]
        ]
    )

    exchanges, token_ct = format_chat_history(
        chat_history=list(example_selected_code_in_history.chat_history),
        token_counter=token_counter,
        token_budget=total_exchange_toks + 5,
    )

    assert exchanges == list(example_selected_code_in_history.chat_history)[1:]
    assert token_ct == total_exchange_toks

    # Truncate last 2 exchanges (out of 3)

    total_exchange_toks = sum(
        [
            token_counter.count_tokens(exchange.request_message)
            + token_counter.count_tokens(exchange.response_text)
            for exchange in list(example_selected_code_in_history.chat_history)[2:]
        ]
    )

    exchanges, token_ct = format_chat_history(
        chat_history=list(example_selected_code_in_history.chat_history),
        token_counter=token_counter,
        token_budget=total_exchange_toks + 5,
    )

    assert exchanges == list(example_selected_code_in_history.chat_history)[2:]
    assert token_ct == total_exchange_toks


def test_format_chat_history_truncate_all(example_history_input: ChatPromptInput):
    """Test simple non-empty history, with truncation of all exchanges."""
    token_counter = TrivialTokenCounter()

    exchanges, token_ct = format_chat_history(
        chat_history=list(example_history_input.chat_history),
        token_counter=token_counter,
        token_budget=5,
    )

    assert exchanges == []
    assert token_ct == 0


def test_inject_selected_code_into_chat_history_into_msg_in_msg_history(
    example_selected_code_in_history: ChatPromptInput,
):
    """Test injecting selected code into chat history message."""

    selected_code_str = (
        "Here is some relevant code: \n\n"
        + example_selected_code_in_history.selected_code
    )

    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=example_selected_code_in_history.message,
        chat_history=list(example_selected_code_in_history.chat_history),
        selected_code=selected_code_str,
        context_code_exchange_request_id=example_selected_code_in_history.context_code_exchange_request_id,
        selected_code_response_message="Understood.",
    )

    expected_chat_history = [
        Exchange("Hey, Augment!", "Hey!", "request_id1"),
        Exchange(
            f"{selected_code_str}Tell me about this function",
            "This function that does ...",
            "request_id2",
        ),
        Exchange("You sure?", "Yes, I am.", "request_id3"),
    ]

    assert updated_msg == "How could we make this code run?"
    assert updated_chat_history == expected_chat_history


def test_inject_selected_code_into_chat_history_into_cur_message(
    example_selected_code_in_history: ChatPromptInput,
):
    """Test injecting selected code into current message."""

    selected_code_str = (
        "Here is some relevant code: \n\n"
        + example_selected_code_in_history.selected_code
    )

    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=example_selected_code_in_history.message,
        chat_history=list(example_selected_code_in_history.chat_history),
        selected_code=selected_code_str,
        context_code_exchange_request_id="new",
        selected_code_response_message="Understood.",
    )

    expected_chat_history = [
        Exchange("Hey, Augment!", "Hey!", "request_id1"),
        Exchange(
            "Tell me about this function", "This function that does ...", "request_id2"
        ),
        Exchange("You sure?", "Yes, I am.", "request_id3"),
    ]

    assert updated_msg == f"{selected_code_str}How could we make this code run?"
    assert updated_chat_history == expected_chat_history


def test_inject_selected_code_into_chat_history_standalone_msg(
    example_selected_code_in_history: ChatPromptInput,
):
    """Test injecting selected code into standalone message."""

    selected_code_str = (
        "Here is some relevant code: \n\n"
        + example_selected_code_in_history.selected_code
    )

    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=example_selected_code_in_history.message,
        chat_history=list(example_selected_code_in_history.chat_history),
        selected_code=selected_code_str,
        # picking an ID that does not exist in history
        context_code_exchange_request_id="request_id5_000",
        selected_code_response_message="Understood.",
    )

    expected_chat_history = [
        Exchange(f"{selected_code_str}", "Understood.", None),
        Exchange("Hey, Augment!", "Hey!", "request_id1"),
        Exchange(
            "Tell me about this function", "This function that does ...", "request_id2"
        ),
        Exchange("You sure?", "Yes, I am.", "request_id3"),
    ]

    assert updated_msg == "How could we make this code run?"
    assert updated_chat_history == expected_chat_history


def test_inject_selected_code_into_chat_history_legacy(
    example_selected_code_in_history: ChatPromptInput,
):
    """Test injecting selected code in legacy scenario where context_code_exchange_request_id is None."""

    selected_code_str = (
        "Here is some relevant code: \n\n"
        + example_selected_code_in_history.selected_code
    )

    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=example_selected_code_in_history.message,
        chat_history=list(example_selected_code_in_history.chat_history),
        selected_code=selected_code_str,
        # picking an ID that does not exist in history
        context_code_exchange_request_id=None,
        selected_code_response_message="Understood.",
    )

    expected_chat_history = [
        Exchange(f"{selected_code_str}", "Understood.", None),
        Exchange("Hey, Augment!", "Hey!", "request_id1"),
        Exchange(
            "Tell me about this function", "This function that does ...", "request_id2"
        ),
        Exchange("You sure?", "Yes, I am.", "request_id3"),
    ]

    assert updated_msg == "How could we make this code run?"
    assert updated_chat_history == expected_chat_history


def test_inject_selected_code_trivial(
    example_selected_code_in_history: ChatPromptInput,
):
    """Test injecting selected code in trivial scenario where selected code is empty"""
    selected_code_str = ""
    cur_message = example_selected_code_in_history.message

    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=cur_message,
        chat_history=list(example_selected_code_in_history.chat_history),
        selected_code=selected_code_str,
        # picking an ID that does not exist in history
        context_code_exchange_request_id=None,
        selected_code_response_message="Understood.",
    )

    assert updated_msg == cur_message
    assert updated_chat_history == list(example_selected_code_in_history.chat_history)


def test_inject_selected_code_tool_result():
    exchange1 = Exchange(
        "list dir",
        [
            ChatResultNode(
                0,
                ChatResultNodeType.TOOL_USE,
                "",
                tool_use=ChatResultToolUse("ls", {}, "tool_use_1"),
            )
        ],
        "request_id1",
    )
    exchange2 = Exchange(
        [
            ChatRequestNode(
                0,
                ChatRequestNodeType.TOOL_RESULT,
                tool_result_node=ChatRequestToolResult(
                    "tool_use_1", "file1\nfile2\n", False, "tool_result_request_id"
                ),
                text_node=None,
            ),
        ],
        "OK.",
        "request_id2",
    )

    selected_code_str = (
        "Here is some relevant code: \n\n"
        + 'void main() {\n  printf("Hello, world!");\n}'
    )

    # Case 1: selected code is selected in some previous turn
    # Expect: previous turn is modifed to insert the selected code
    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=exchange2.request_message,
        chat_history=[exchange1],
        selected_code=selected_code_str,
        context_code_exchange_request_id="request_id1",
        selected_code_response_message="Understood.",
    )
    assert len(updated_chat_history) == 1
    assert selected_code_str in updated_chat_history[0].request_message
    assert updated_msg == exchange2.request_message

    # Case 2: selected code is selected on current turn which is a tool result
    # Expect: extra exchange prepended containing the selected code
    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=exchange2.request_message,
        chat_history=[exchange1],
        selected_code=selected_code_str,
        context_code_exchange_request_id="new",
        selected_code_response_message="Understood.",
    )
    assert len(updated_chat_history) == 2
    assert selected_code_str in updated_chat_history[0].request_message
    assert "Understood." == get_response_message_as_text(
        updated_chat_history[0].response_text
    )
    assert updated_msg == exchange2.request_message

    # Case 3: selected code is in an unknown turn, presumably pushed out of the history
    # Expect: extra exchange prepended containing the selected code

    updated_msg, updated_chat_history = inject_selected_code_into_chat_history(
        cur_message=exchange2.request_message,
        chat_history=[exchange1],
        selected_code=selected_code_str,
        context_code_exchange_request_id="very-old-request_id",
        selected_code_response_message="Understood.",
    )
    assert len(updated_chat_history) == 2
    assert selected_code_str in updated_chat_history[0].request_message
    assert "Understood." == get_response_message_as_text(
        updated_chat_history[0].response_text
    )
    assert updated_msg == exchange2.request_message


"""
Tests for format_agent_history
"""


def test_format_agent_history_basic(example_history_input: ChatPromptInput):
    """Test simple non-empty history, with the whole history in the output."""
    token_counter = TrivialTokenCounter()
    exchanges, token_ct = format_agent_history(
        list(example_history_input.chat_history),
        token_counter,
        token_budget=1000,
    )

    total_exchange_toks = sum(
        [
            token_counter.count_tokens(exchange.request_message)
            + token_counter.count_tokens(exchange.response_text)
            for exchange in example_history_input.chat_history
        ]
    )

    assert exchanges == example_history_input.chat_history
    assert token_ct == total_exchange_toks


def test_format_agent_history_truncate_tool_results(
    example_long_chat_history_input: ChatPromptInput,
    example_long_chat_history_input_with_placeholder_tool_result: ChatPromptInput,
):
    """Test simple non-empty history, with truncation of tool results."""
    token_counter = TrivialTokenCounter()
    exchanges, token_ct = format_agent_history(
        list(example_long_chat_history_input.chat_history),  # Use this fixture directly
        token_counter,
        token_budget=1000,
    )

    assert token_ct <= 1000

    # We expect the tool results to be truncated for all but the last 3 turns
    for exchange in exchanges[:-3]:
        if isinstance(exchange.request_message, list):
            for node in exchange.request_message:
                assert (
                    node.tool_result_node is None
                    or node.tool_result_node.content
                    == "[Truncated...re-run tool if you need to see output again.]"
                )

    assert len(exchanges) == len(
        list(example_long_chat_history_input_with_placeholder_tool_result.chat_history)
    )
    assert exchanges == list(
        example_long_chat_history_input_with_placeholder_tool_result.chat_history
    )


def test_format_agent_history_no_truncate_tool_results(
    example_long_chat_history_input: ChatPromptInput,
):
    """Test history with a huge budget, with no truncation of tool results."""
    token_counter = TrivialTokenCounter()
    exchanges, token_ct = format_agent_history(
        list(example_long_chat_history_input.chat_history),  # Use this fixture directly
        token_counter,
        token_budget=30000,
    )

    assert token_ct <= 30000

    assert exchanges == list(example_long_chat_history_input.chat_history)


def test_sanitize_known_client_faults_no_issues():
    """Test sanitization when there are no issues to fix."""
    exchange1 = Exchange("Hello", "Hi there!", "request_id1")
    exchange2 = Exchange("How are you?", "I'm doing well!", "request_id2")
    chat_history = [exchange1, exchange2]
    cur_message = "What's the weather like?"

    result = sanitize_known_client_faults(chat_history, cur_message)

    assert result == chat_history
    assert (a is b for a, b in zip(result, chat_history))


def test_sanitize_known_client_faults_no_issues_nodes():
    """Test sanitization when there are no issues to fix, with request/response nodes"""
    exchange1 = Exchange(
        request_message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(content="Hello"),
                tool_result_node=None,
            )
        ],
        response_text=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="Hi there!",
                tool_use=None,
            ),
            ChatResultNode(
                id=2,
                type=ChatResultNodeType.TOOL_USE,
                content="",
                tool_use=ChatResultToolUse(
                    name="test_tool",
                    input={"param": "value"},
                    tool_use_id="tool_use_1",
                ),
            ),
        ],
        request_id="request_id1",
    )
    exchange2 = Exchange(
        request_message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(content="How are you?"),
                tool_result_node=None,
            ),
            ChatRequestNode(
                id=2,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="tool_use_1",
                    content="Tool result content",
                    is_error=False,
                ),
            ),
        ],
        response_text=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.RAW_RESPONSE,
                content="I'm doing well!",
                tool_use=None,
            ),
            ChatResultNode(
                id=2,
                type=ChatResultNodeType.TOOL_USE,
                content="",
                tool_use=ChatResultToolUse(
                    name="test_tool_2",
                    input={"param": "value2"},
                    tool_use_id="tool_use_2",
                ),
            ),
        ],
        request_id="request_id2",
    )
    chat_history = [exchange1, exchange2]
    cur_message = [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content="What's the weather like?"),
            tool_result_node=None,
        ),
        ChatRequestNode(
            id=2,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="tool_use_2",
                content="Weather result content",
                is_error=False,
            ),
        ),
    ]
    result = sanitize_known_client_faults(chat_history, cur_message)

    assert result == chat_history
    assert (a is b for a, b in zip(result, chat_history))


def test_sanitize_known_client_faults_duplicate_nodes():
    """Test sanitization of duplicate nodes in response side of exchange."""
    tool_use_node = ChatResultNode(
        id=1,
        type=ChatResultNodeType.TOOL_USE,
        content="",
        tool_use=ChatResultToolUse(
            name="test_tool",
            input={"param": "value"},
            tool_use_id="tool_use_1",
        ),
    )

    raw_response_node = ChatResultNode(
        id=2,
        type=ChatResultNodeType.RAW_RESPONSE,
        content="Some output",
        tool_use=None,
    )

    exchange = Exchange(
        request_message="Run the tool",
        response_text=[
            tool_use_node,
            raw_response_node,
            tool_use_node,
            raw_response_node,
        ],
        request_id="request_id1",
    )

    chat_history = [exchange]
    cur_message = "What's next?"

    result = sanitize_known_client_faults(chat_history, cur_message)

    assert len(result) == 1
    assert len(result[0].response_text) == 2
    assert result[0].response_text[0] == tool_use_node
    assert result[0].response_text[1] == raw_response_node


def test_sanitize_known_client_faults_tool_splitting():
    """Test sanitization of exchanges that split tool use and result."""
    tool_use_exchange = Exchange(
        request_message="Run the tool",
        response_text=[
            ChatResultNode(
                id=1,
                type=ChatResultNodeType.TOOL_USE,
                content="",
                tool_use=ChatResultToolUse(
                    name="test_tool",
                    input={"param": "value"},
                    tool_use_id="tool_use_1",
                ),
            )
        ],
        request_id="request_id1",
    )

    droppable_exchange = Exchange(
        request_message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.EDIT_EVENTS,
                text_node=None,
                tool_result_node=None,
            ),
            ChatRequestNode(
                id=2,
                type=ChatRequestNodeType.IDE_STATE,
                text_node=None,
                tool_result_node=None,
                ide_state_node=ChatRequestIdeState(
                    workspace_folders=[
                        WorkspaceFolderInfo(
                            folder_root="/home/<USER>/repo/folder",
                            repository_root="/home/<USER>/repo",
                        )
                    ],
                    workspace_folders_unchanged=False,
                    current_terminal=TerminalInfo(
                        terminal_id=0,
                        current_working_directory="/home/<USER>/repo/folder",
                    ),
                ),
            ),
        ],
        response_text="",
        request_id="request_id2",
    )

    tool_result_exchange = Exchange(
        request_message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="tool_use_1",
                    content="Tool result content",
                    is_error=False,
                    request_id="tool_result_request_id",
                ),
            )
        ],
        response_text="Tool completed successfully.",
        request_id="request_id3",
    )

    chat_history = [
        tool_use_exchange,
        droppable_exchange,
        droppable_exchange,
        droppable_exchange,
        tool_result_exchange,
    ]
    cur_message = "What's next?"

    result = sanitize_known_client_faults(chat_history, cur_message)
    assert len(result) == 2
    assert result[0] == tool_use_exchange
    assert result[1] == tool_result_exchange

    # Adding anything critical/not-removable to the middle exchange prevents it from being dropped (and will result in BadRequestError; fine...)
    useful_exchanges = [
        dataclasses.replace(droppable_exchange, request_message="Hello world"),
        dataclasses.replace(droppable_exchange, response_text="Hello world"),
        dataclasses.replace(
            droppable_exchange,
            request_message=list(droppable_exchange.request_message)
            + [
                ChatRequestNode(
                    id=2,
                    type=ChatRequestNodeType.TEXT,
                    text_node=None,
                    tool_result_node=None,
                )
            ],
        ),
        dataclasses.replace(
            droppable_exchange,
            request_message=list(droppable_exchange.request_message)
            + [
                ChatRequestNode(
                    id=2,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=None,
                )
            ],
        ),
        dataclasses.replace(
            droppable_exchange,
            request_message=list(droppable_exchange.request_message)
            + [
                ChatRequestNode(
                    id=2,
                    type=ChatRequestNodeType.IMAGE,
                    text_node=None,
                    tool_result_node=None,
                )
            ],
        ),
    ]
    for useful_exchange in useful_exchanges:
        result = sanitize_known_client_faults(
            [tool_use_exchange, useful_exchange, tool_result_exchange], cur_message
        )
        assert len(result) == 3


def test_sanitize_known_client_faults_combined_issues():
    """Test sanitization with both duplicate nodes and tool splitting issues."""
    # Create a tool use exchange with duplicate tool use
    tool_use_node = ChatResultNode(
        id=1,
        type=ChatResultNodeType.TOOL_USE,
        content="",
        tool_use=ChatResultToolUse(
            name="test_tool",
            input={"param": "value"},
            tool_use_id="tool_use_1",
        ),
    )

    tool_use_exchange = Exchange(
        request_message="Run the tool",
        response_text=[tool_use_node, tool_use_node],  # Duplicate node
        request_id="request_id1",
    )

    # Create an exchange that can be dropped (no user-written or tool nodes; no assistant response)
    droppable_exchange = Exchange(
        request_message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.EDIT_EVENTS,
                text_node=None,
                tool_result_node=None,
            ),
        ],
        response_text="",
        request_id="request_id2",
    )

    cur_message = [
        ChatRequestNode(
            id=1,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id="tool_use_1",
                content="Tool result content",
                is_error=False,
            ),
        )
    ]

    result = sanitize_known_client_faults(
        [tool_use_exchange, droppable_exchange], cur_message
    )
    assert len(result) == 1
    assert result[0] == dataclasses.replace(
        tool_use_exchange, response_text=[tool_use_node]
    )

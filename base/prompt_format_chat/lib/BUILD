load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

package_group(
    name = "default_visibility",
    packages = [
        "//base/prompt_format_chat/...",
        "//services/chat_host/server/prompt_format/...",
    ],
)

package(default_visibility = [":default_visibility"])

filegroup(
    name = "prompt_templates",
    srcs = glob(["prompts/**/*.jinja"]),
    visibility = [":default_visibility"],
)

py_library(
    name = "string_formatter",
    srcs = [
        "string_formatter.py",
    ],
    visibility = [
        "//base/prompt_format_postprocess:__subpackages__",
        "//base/prompt_format_router:__subpackages__",
    ],
    deps = [
        ":token_counter",
        requirement("jinja2"),
    ],
)

pytest_test(
    name = "string_formatter_test",
    srcs = ["string_formatter_test.py"],
    deps = [
        ":string_formatter",
        ":token_counter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "truncation_utils",
    srcs = [
        "truncation_utils.py",
    ],
    deps = [
        ":token_counter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "truncation_utils_test",
    srcs = ["truncation_utils_test.py"],
    deps = [
        ":truncation_utils",
    ],
)

py_library(
    name = "token_counter",
    srcs = [
        "token_counter.py",
    ],
    visibility = [
        ":default_visibility",
        "//base/prompt_format_postprocess:__subpackages__",
        "//base/prompt_format_router:__subpackages__",
        "//base/third_party_clients:__subpackages__",
    ],
    deps = [
        "//base/third_party_clients/token_counter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "token_counter_claude",
    srcs = [
        "token_counter_claude.py",
    ],
    deps = [
        ":token_counter",
        "//base/third_party_clients/token_counter:token_counter_claude",
        "//base/tokenizers",
        requirement("google-cloud-aiplatform"),
        requirement("sentencepiece"),  # Implicit dependency from `google-cloud-aiplatform[tokenization]`
        requirement("anthropic"),
        requirement("google-auth"),  # Implicit dependency from `anthropic[vertex]`
        requirement("structlog"),
    ],
)

py_library(
    name = "token_counter_vertex",
    srcs = [
        "token_counter_vertex.py",
    ],
    deps = [
        ":token_counter",
        "//base/tokenizers",
        requirement("google-cloud-aiplatform"),
        requirement("sentencepiece"),  # Implicit dependency from `google-cloud-aiplatform[tokenization]`
        requirement("anthropic"),
        requirement("google-auth"),  # Implicit dependency from `anthropic[vertex]`
        requirement("structlog"),
    ],
)

py_library(
    name = "selected_code_prompt_formatter_v2",
    srcs = [
        "selected_code_prompt_formatter_v2.py",
    ],
    deps = [
        ":string_formatter",
        ":token_counter",
        ":truncation_utils",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

pytest_test(
    name = "selected_code_prompt_formatter_v2_test",
    srcs = ["selected_code_prompt_formatter_v2_test.py"],
    deps = [
        ":selected_code_prompt_formatter_v2",
        ":token_counter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "abstract_formatted_file",
    srcs = [
        "abstract_formatted_file.py",
    ],
    deps = [
        ":token_counter",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
        requirement("cachetools"),
    ],
)

py_library(
    name = "formatted_file_v2",
    srcs = [
        "formatted_file_v2.py",
    ],
    deps = [
        ":abstract_formatted_file",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

pytest_test(
    name = "formatted_file_v2_test",
    srcs = ["formatted_file_v2_test.py"],
    deps = [
        ":formatted_file_v2",
        ":token_counter",
        "//base/retrieval/chunking:line_based_chunking",
        "//base/tokenizers",
    ],
)

py_library(
    name = "documentation_formatted_file",
    srcs = [
        "documentation_formatted_file.py",
    ],
    deps = [
        ":abstract_formatted_file",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

pytest_test(
    name = "documentation_formatted_file_test",
    srcs = ["documentation_formatted_file_test.py"],
    deps = [
        ":documentation_formatted_file",
        ":token_counter",
        "//base/retrieval/chunking:line_based_chunking",
        "//base/tokenizers",
    ],
)

py_library(
    name = "retrieval_section_prompt_formatter_v2",
    srcs = [
        "retrieval_section_prompt_formatter_v2.py",
    ],
    deps = [
        ":documentation_formatted_file",
        ":formatted_file_v2",
        ":string_formatter",
        ":token_counter",
        ":truncation_utils",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "retrieval_section_prompt_formatter_v2_test",
    srcs = ["retrieval_section_prompt_formatter_v2_test.py"],
    deps = [
        ":retrieval_section_prompt_formatter_v2",
        ":token_counter",
        "//base/prompt_format_chat:conftest",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "retrieval_section_prompt_formatter_v3",
    srcs = [
        "retrieval_section_prompt_formatter_v3.py",
    ],
    deps = [
        ":documentation_formatted_file",
        ":formatted_file_v2",
        ":string_formatter",
        ":token_counter",
        ":truncation_utils",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "retrieval_section_prompt_formatter_v3_test",
    srcs = ["retrieval_section_prompt_formatter_v3_test.py"],
    deps = [
        ":retrieval_section_prompt_formatter_v3",
        ":token_counter",
        "//base/prompt_format_chat:conftest",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/tokenizers",
    ],
)

py_library(
    name = "chat_history_builder",
    srcs = ["chat_history_builder.py"],
    deps = [
        ":ide_state_utils",
        ":string_formatter",
        ":token_counter",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

pytest_test(
    name = "chat_history_builder_test",
    srcs = ["chat_history_builder_test.py"],
    deps = [
        ":chat_history_builder",
        "//base/prompt_format_chat:conftest",
    ],
)

py_library(
    name = "system_prompts",
    srcs = [
        "system_prompts.py",
    ],
    data = [":prompt_templates"],
    deps = [
        ":string_formatter",
        ":token_counter",
        "//base/prompt_format:common",
    ],
)

pytest_test(
    name = "system_prompts_test",
    srcs = ["system_prompts_test.py"],
    deps = [
        ":system_prompts",
        "//base/prompt_format:common",
        "//base/third_party_clients/token_counter",
    ],
)

py_library(
    name = "ide_state_utils",
    srcs = [
        "ide_state_utils.py",
    ],
    deps = [
        ":system_prompts",
        ":token_counter",
        "//base/prompt_format:common",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

py_library(
    name = "rules_prompt_builder",
    srcs = [
        "rules_prompt_builder.py",
    ],
    deps = [
        ":string_formatter",
        ":token_counter",
        ":truncation_utils",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

pytest_test(
    name = "rules_prompt_builder_test",
    srcs = ["rules_prompt_builder_test.py"],
    deps = [
        ":rules_prompt_builder",
        ":token_counter",
    ],
)

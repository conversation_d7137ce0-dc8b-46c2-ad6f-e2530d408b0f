import pytest

from base.prompt_format.common import Rule, RuleType
from base.prompt_format_chat.lib.rules_prompt_builder import build_custom_prompt
from base.prompt_format_chat.prompt_formatter import ChatPromptInput


def _make_chat_prompt_input(workspace_guidelines, user_guidelines, rules=None):
    if rules is None:
        rules = []
    return ChatPromptInput(
        message="",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=-1,
        suffix_end=-1,
        retrieved_chunks=[],
        context_code_exchange_request_id=None,
        recent_changes=None,
        user_guided_blobs=[],
        external_source_ids=[],
        workspace_guidelines=workspace_guidelines,
        user_guidelines=user_guidelines,
        rules=rules,
    )


def test_build_custom_prompt_for_workspace():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines="Follow PEP 8 guidelines.", user_guidelines=None
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    expected = "\n\nAdditional workspace rules:\nFollow PEP 8 guidelines.\n\n\n"
    assert result == expected


def test_build_custom_prompt_for_user():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines="Use camelCase for variable names.",
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    expected = "\n\nAdditional user rules:\nUse camelCase for variable names.\n\n"
    assert result == expected


def test_build_custom_prompt_for_workspace_and_user():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines="Follow PEP 8 guidelines.",
        user_guidelines="Use camelCase for variable names.",
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    expected = "\n\nAdditional workspace rules:\nFollow PEP 8 guidelines.\n\n\nAdditional user rules:\nUse camelCase for variable names.\n\n"
    assert result == expected


def test_build_custom_prompt_for_none():
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines=None,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines
    )
    assert result == ""


def test_build_custom_prompt_with_rules():
    """Test that rules are included when the feature flag is enabled by default."""
    rules = [
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule1.txt",
            content="Use 4 spaces for indentation.",
        ),
        Rule(
            type=RuleType.ALWAYS_ATTACHED,
            path="path/to/rule2.txt",
            content="Use double quotes for strings.",
        ),
        Rule(
            type=RuleType.MANUAL,
            path="path/to/manual_rule.txt",
            content="Use f-strings for string formatting.",
        ),
    ]
    prompt_input = _make_chat_prompt_input(
        workspace_guidelines=None,
        user_guidelines=None,
        rules=rules,
    )
    result = build_custom_prompt(
        prompt_input.user_guidelines, prompt_input.workspace_guidelines, rules=rules
    )
    expected = """


Additional rule located in path/to/rule1.txt:
Use 4 spaces for indentation.



Additional rule located in path/to/rule2.txt:
Use double quotes for strings.



Additional rule located in path/to/manual_rule.txt:
Use f-strings for string formatting.


"""
    assert result == expected

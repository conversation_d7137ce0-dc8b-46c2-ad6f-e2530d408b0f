"""Builds the prompt based on user-provided rules."""

from dataclasses import dataclass

import structlog

from base.prompt_format.common import PersonaType, Rule

logger = structlog.get_logger(__name__)


@dataclass
class Persona:
    persona_type: PersonaType
    name: str
    prompt: str


DEFAULT_PERSONA = Persona(
    persona_type=PersonaType.DEFAULT,
    name="",
    prompt="",
)

PROTOTYPER_PERSONA = Persona(
    persona_type=PersonaType.PROTOTYPER,
    name="Prototyper",
    prompt="""\
# Recommendations when building new web apps
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with `vite` or `next.js`.
- Initialize the project using a CLI initialization tool, instead of writing from scratch.
- For database and auth, a good default option is to use Supabase.
- Before using `open-browser` to show the user the app, use `curl` with the `launch-process` tool to access the website and `read-process` to check for errors.
- Modern frameworks like Next.js have hot reload, so the user can see the changes without a refresh. You should therefore avoid calling `open-browser` more than once on the same URL.""",
)


def get_persona(persona_type: PersonaType) -> Persona:
    """Get the persona prompt for the given persona.

    Args:
        persona: The persona to get the prompt for.

    Returns:
        The persona prompt.
    """
    if persona_type == PersonaType.DEFAULT:
        return DEFAULT_PERSONA
    elif persona_type == PersonaType.PROTOTYPER:
        return PROTOTYPER_PERSONA
    else:
        raise ValueError(f"Unknown persona: {persona_type}")


def build_custom_prompt(
    user_guidelines: str | None,
    workspace_guidelines: str | None,
    persona_type: PersonaType = PersonaType.DEFAULT,
    rules: list[Rule] | None = None,
) -> str:
    """Builds custom prompt."""
    custom_prompt = ""

    if persona_type != PersonaType.DEFAULT:
        custom_prompt += "\n\n"
        custom_prompt += get_persona(persona_type).prompt
        custom_prompt += "\n"

    if workspace_guidelines:
        custom_prompt += "\n\n"
        custom_prompt += """\
Additional workspace rules:
"""
        custom_prompt += workspace_guidelines
        custom_prompt += "\n"

    if user_guidelines:
        custom_prompt += "\n\n"
        custom_prompt += """\
Additional user rules:
"""
        custom_prompt += user_guidelines

    if rules:
        for rule in rules:
            custom_prompt += "\n\n"
            custom_prompt += f"""
Additional rule located in {rule.path}:
{rule.content}
"""

    if custom_prompt:
        custom_prompt += "\n\n"

    return custom_prompt

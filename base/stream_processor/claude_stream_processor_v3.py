"""A stream processor for Сlaude responses, designed to be extendable with new sections.

General idea of how it works:
- ClaudeStreamProcessorV3 can be in two states: "no sections" (self.current_section is None) or "in section" (self.current_section is not None).
- Every section is defined by its opening regex and `processing_fn` that is specialized for this section.
- When `self.current_section is None`, processor tries to find the next section and meanwhile yields all the text that is guaranteed to be outside of any section.
- When section is found, ClaudeStreamProcessorV3 sets `self.current_section` to this section and starts sending ALL the text to respective `processing_fn`.
- `processing_fn` is responsible for detecting when section is finished.
- Every `processing_fn` should return a tuple:
    -- `remaining_buffer` is the part of the buffer that wasn't processed
    -- `to_yield` is a list of StreamProcessorOutput objects to be yielded
    -- `is_finished` is a boolean indicating whether the section processing is complete
"""

import functools
import logging
from dataclasses import dataclass
from enum import IntEnum
from typing import Callable, Iterable

import regex
import structlog

from base.stream_processor.basic_stream_processor import (
    StreamProcessor,
    StreamProcessorOutput,
    StreamProcessorOutputType,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ThirdPartyModelResponse,
)

logger = structlog.get_logger("ClaudeStreamProcessorV3")


@dataclass
class AugmentSection:
    opening_re: regex.Pattern[str]
    """Regex that matches opening of the section."""
    processing_fn: Callable
    """Function that processes text inside the section."""

    def __init__(self, opening_re: str, processing_fn: Callable):
        self.opening_re = regex.compile(opening_re)
        self.processing_fn = processing_fn


BLANK = r"[ \t\r\f\v]"

# Note: "\n" is prepended later to make sure that tag starts on a new line
# see ClaudeStreamProcessorV3.__init__
CODE_SNIPPET_OPENING_RE = (
    rf"{BLANK}*"
    + r"<augment_code_snippet"
    + r'(?:\s+path="?(.*?)"?)?'
    + r'(?:\s+mode="?(.*?)"?)?'
    + r">\n"
)
CODE_SNIPPET_CLOSING_RE = rf"{BLANK}*" + r"</augment_code_snippet>\n"
CODE_SNIPPET_TOP_BACKTICKS_RE = rf"{BLANK}*" + r"````?(.*?)\n"


class ThinkingSectionProcessingMode(IntEnum):
    PASS_THROUGH = 0
    """Yield the thinking section as is."""
    HIDE = 1
    """Hide the thinking section."""
    MARKDOWN = 2
    """Show the thinking section using Markdown sections."""


def parse_code_section(
    buffer: str, backticks_inside: bool, n_extra_backticks: int
) -> tuple[str, list[StreamProcessorOutput], bool]:
    """Processes <augment_code_snippet> section.

    Args:
        buffer (str): The input text buffer to process.
        backticks_inside (bool): Whether the backticks are expected to be inside the <augment_code_snippet> tag.
        n_extra_backticks (int): Number of extra backticks to add.

    Returns:
        tuple[str, list[StreamProcessorOutput], bool]: A tuple containing:
            - The remaining unprocessed buffer.
            - A list of StreamProcessorOutput objects to be yielded.
            - A boolean indicating whether the section processing is complete.
    """
    to_yield_str = ""

    if backticks_inside:
        opening_pattern = regex.compile(
            "\n" + CODE_SNIPPET_OPENING_RE + CODE_SNIPPET_TOP_BACKTICKS_RE
        )
        closing_pattern = regex.compile(rf"{BLANK}*````?\n" + CODE_SNIPPET_CLOSING_RE)
    else:
        opening_pattern = regex.compile(
            CODE_SNIPPET_TOP_BACKTICKS_RE + CODE_SNIPPET_OPENING_RE
        )
        closing_pattern = regex.compile(
            rf"{BLANK}*" + CODE_SNIPPET_CLOSING_RE + r"````?\n"
        )

    # If we detect the opening, we remove it from `buffer` and update `to_yield_str`.
    opening_match = opening_pattern.search(buffer)
    if opening_match is not None:
        if backticks_inside:
            path, mode, language = opening_match.groups()
        else:
            language, path, mode = opening_match.groups()
        if backticks_inside:
            to_yield_str += "\n"
        to_yield_str += f"```{'`' * n_extra_backticks}{language}"
        if path:
            to_yield_str += f" path={path}"
        if mode:
            to_yield_str += f" mode={mode}"
        to_yield_str += "\n"
        buffer = buffer[opening_match.end() :]

    closing_match = closing_pattern.search(buffer, partial=True)  # type: ignore
    if closing_match is None:
        # If closing tag generation didn't start yet, we should get empty partial match.
        raise RuntimeError("closing match should always be found, at least partial.")
    to_yield_str += buffer[: closing_match.start()]
    if closing_match.partial:
        done = False
        buffer = buffer[closing_match.start() :]
    else:
        done = True
        buffer = buffer[closing_match.end() :]
        to_yield_str += f"```{'`' * n_extra_backticks}\n"

    to_yield = []
    if len(to_yield_str) > 0:
        to_yield = [
            StreamProcessorOutput(to_yield_str, StreamProcessorOutputType.ANSWER)
        ]

    return buffer, to_yield, done


def parse_suggested_question_section(
    buffer: str,
) -> tuple[str, list[StreamProcessorOutput], bool]:
    """Processes <guess_of_next_user_question> section.

    Args:
        buffer (str): The input text buffer to process.

    Returns:
        tuple[str, list[StreamProcessorOutput], bool]: A tuple containing:
            - The remaining unprocessed buffer.
            - A list of StreamProcessorOutput objects to be yielded.
            - A boolean indicating whether the section processing is complete.
    """
    full_pattern = regex.compile(
        r"<guess_of_next_user_question>(.*?)</guess_of_next_user_question>\n",
        regex.DOTALL,
    )
    match = full_pattern.search(buffer)
    if match is None:
        # We are not streaming suggested questions, so waiting until they're fully generated.
        return buffer, [], False
    questions_block = match.group(1)
    next_question_pattern = regex.compile(
        r"<next_user_question>(.*?)</next_user_question>", regex.DOTALL
    )
    questions = next_question_pattern.findall(questions_block)
    suggested_questions = [question.replace("\n", " ") for question in questions]

    if len(suggested_questions) == 0:
        output = []
    else:
        output = [
            StreamProcessorOutput(
                "\n".join(suggested_questions),
                StreamProcessorOutputType.SUGGESTED_QUESTIONS,
            )
        ]

    return (
        buffer[match.end() :],
        output,
        True,
    )


def parse_relevant_sources(
    buffer: str,
) -> tuple[str, list[StreamProcessorOutput], bool]:
    """Processes <useful_files> section.

    Args:
        buffer (str): The input text buffer to process.

    Returns:
        tuple[str, list[StreamProcessorOutput], bool]: A tuple containing:
            - The remaining unprocessed buffer.
            - A list of StreamProcessorOutput objects to be yielded.
            - A boolean indicating whether the section processing is complete.
    """
    full_pattern = regex.compile(r"<useful_files>(.*?)</useful_files>\n", regex.DOTALL)
    match = full_pattern.search(buffer)
    if match is None:
        # We are not streaming relevant sources, so waiting until they're fully generated.
        return buffer, [], False
    sources_block = match.group(1)
    source_pattern = regex.compile(r"<file>(.*?)</file>", regex.DOTALL)
    sources = source_pattern.findall(sources_block)

    if len(sources) == 0:
        output = []
    else:
        output = [
            StreamProcessorOutput(
                "\n".join(sources),
                StreamProcessorOutputType.RELEVANT_SOURCES,
            )
        ]

    return (
        buffer[match.end() :],
        output,
        True,
    )


def process_thinking_section(
    buffer: str,
    thinking_section_mode: ThinkingSectionProcessingMode,
):
    """Processes <think> section.

    Args:
        buffer: The input text buffer to process.
        thinking_section_mode: The mode of processing the thinking section.

    Returns:
        A tuple containing:
            - The remaining unprocessed buffer.
            - A list of StreamProcessorOutput objects to be yielded.
            - A boolean indicating whether the section processing is complete.
    """
    to_yield_str = ""
    opening_pattern = regex.compile(r"<think>")
    closing_pattern = regex.compile(r"</think>\n")

    # If we detect the opening, we remove it from `buffer` and update `to_yield_str`
    opening_match = opening_pattern.search(buffer)
    if opening_match is not None:
        if thinking_section_mode == ThinkingSectionProcessingMode.MARKDOWN:
            to_yield_str += "### Thinking\n"
        elif thinking_section_mode == ThinkingSectionProcessingMode.PASS_THROUGH:
            to_yield_str += buffer[: opening_match.end()]
        buffer = buffer[opening_match.end() :]

    closing_match = closing_pattern.search(buffer, partial=True)  # type: ignore
    if closing_match is None:
        raise RuntimeError("closing match should always be found, at least partial.")

    if thinking_section_mode in {
        ThinkingSectionProcessingMode.MARKDOWN,
        ThinkingSectionProcessingMode.PASS_THROUGH,
    }:
        to_yield_str += buffer[: closing_match.start()]

    if closing_match.partial:
        done = False
        buffer = buffer[closing_match.start() :]
    else:
        done = True
        if thinking_section_mode == ThinkingSectionProcessingMode.PASS_THROUGH:
            to_yield_str += buffer[: closing_match.end()]
        buffer = buffer[closing_match.end() :]
        if thinking_section_mode == ThinkingSectionProcessingMode.MARKDOWN:
            to_yield_str += "\n### Answer\n\n"

    to_yield = []
    if len(to_yield_str) > 0:
        to_yield = [
            StreamProcessorOutput(to_yield_str, StreamProcessorOutputType.ANSWER)
        ]

    return buffer, to_yield, done


class ClaudeStreamProcessorV3(StreamProcessor):
    def __init__(
        self,
        n_extra_backticks: int,
        thinking_section_mode: ThinkingSectionProcessingMode = ThinkingSectionProcessingMode.PASS_THROUGH,
        prepend_thinking_tag: bool = False,
    ):
        super().__init__()
        self.buffer = ""
        self.current_section = None
        self.history = ""
        self.preprend_thinking_tag = prepend_thinking_tag

        self.augment_sections = [
            AugmentSection(
                opening_re="\n"
                + CODE_SNIPPET_OPENING_RE
                + CODE_SNIPPET_TOP_BACKTICKS_RE,
                processing_fn=functools.partial(
                    parse_code_section,
                    backticks_inside=True,
                    n_extra_backticks=n_extra_backticks,
                ),
            ),
            AugmentSection(
                opening_re=CODE_SNIPPET_TOP_BACKTICKS_RE + CODE_SNIPPET_OPENING_RE,
                processing_fn=functools.partial(
                    parse_code_section,
                    backticks_inside=False,
                    n_extra_backticks=n_extra_backticks,
                ),
            ),
            AugmentSection(
                opening_re=r"<guess_of_next_user_question>",
                processing_fn=parse_suggested_question_section,
            ),
            AugmentSection(
                opening_re=r"<useful_files>",
                processing_fn=parse_relevant_sources,
            ),
        ]

        self.augment_sections.append(
            AugmentSection(
                opening_re=r"<think>",
                processing_fn=functools.partial(
                    process_thinking_section,
                    thinking_section_mode=thinking_section_mode,
                ),
            ),
        )

    def get_stream_history(self) -> str:
        return self.history

    def process_stream(
        self, ls_substr: Iterable[ThirdPartyModelResponse]
    ) -> Iterable[StreamProcessorOutput]:
        """Main entrypoint for stream parsing.

        Since some sections' regexps might have "\n" at the beginning or end,
        we add "\n" at the very beginning of the stream.
        Then we remove it from the first response if it wasn't consumed.

        For the same reason, we add "\n" to the end, if it's not there.
        """
        end_of_stream: EndOfStream | None = None
        post_eos_error_logged = False
        self.buffer = "\n"
        if self.preprend_thinking_tag:
            self.buffer += "<think>\n"
        first_yield = True
        for response in ls_substr:
            if end_of_stream is not None and not post_eos_error_logged:
                post_eos_error_logged = True
                logger.error("ThirdPartyModelResponse content returned after EOS")

            for processor_output in self.process_next_chunk(response.text):
                if (
                    first_yield
                    and processor_output.type == StreamProcessorOutputType.ANSWER
                    and processor_output.text.startswith("\n")
                ):
                    yield from self._do_yield_text(processor_output.text[1:])
                else:
                    yield processor_output
                first_yield = False
            if response.tool_use_start is not None:
                yield StreamProcessorOutput(
                    "",
                    StreamProcessorOutputType.TOOL_START,
                    tool_use_start=response.tool_use_start,
                )
            elif response.tool_use is not None:
                yield StreamProcessorOutput(
                    "",
                    StreamProcessorOutputType.TOOL,
                    tool_use=response.tool_use,
                )
            elif response.end_of_stream is not None:
                end_of_stream = response.end_of_stream

        if len(self.history):
            if self.history[-1] != "\n":
                yield from self.process_next_chunk("\n")
            yield from self._do_yield_text(self.buffer)

        if end_of_stream is not None:
            yield StreamProcessorOutput(
                "",
                StreamProcessorOutputType.END_OF_STREAM,
                end_of_stream=end_of_stream,
            )

    def process_next_chunk(self, chunk: str) -> Iterable[StreamProcessorOutput]:
        """Function that accept the next chunk, updates the state and
        yields `StreamProcessorOutput`.

        "return" here means that we've parsed all that we can and we need to
        get more tokens.
        """
        self.history += chunk
        self.buffer += chunk

        num_iter = 0
        while len(self.buffer) > 0:
            num_iter += 1
            if num_iter > 100:
                # It should never happen, but just in case.
                raise RuntimeError("Too many iterations for single chunk")

            if self.current_section is None:
                # We try to find the next section (closest to the beginning of the buffer).
                # Full matches are preferred over partial matches.
                next_section, next_section_match = None, None
                for s in self.augment_sections:
                    m = regex.search(s.opening_re, self.buffer, partial=True)
                    if m is None:
                        continue
                    if (
                        # First match is always recorded
                        next_section_match is None
                        or
                        # This and previous matches have the same partial status, so we take the one that starts earlier.
                        (
                            next_section_match.partial == m.partial
                            and m.start() < next_section_match.start()
                        )
                        or
                        # This is a full match, and previous is a partial match, so we take full one.
                        (next_section_match.partial and not m.partial)
                    ):
                        next_section, next_section_match = s, m

                if (
                    next_section_match is None
                    or next_section_match.end() == next_section_match.start()
                ):
                    # This case means, that none of sections started,
                    # so we are safe to yield whole buffer.
                    yield from self._do_yield_text(self.buffer)
                    self.buffer = ""
                    return
                elif next_section_match.partial:
                    # Some section (probably) started generation
                    # So we can yield only text prior to the potential section start.
                    yield from self._do_yield_text(
                        self.buffer[: next_section_match.start()]
                    )
                    self.buffer = self.buffer[next_section_match.start() :]
                    return
                else:
                    # We found the next section, so we can start processing it.
                    yield from self._do_yield_text(
                        self.buffer[: next_section_match.start()]
                    )
                    self.buffer = self.buffer[next_section_match.start() :]
                    self.current_section = next_section
                    continue
            else:
                self.buffer, to_yield, is_finished = self.current_section.processing_fn(
                    self.buffer
                )
                yield from to_yield
                if not is_finished:
                    return
                if is_finished:
                    self.current_section = None

    def _do_yield_text(self, text: str):
        if len(text) > 0:
            yield StreamProcessorOutput(text, StreamProcessorOutputType.ANSWER)

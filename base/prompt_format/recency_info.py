"""Base types for recency info."""

from dataclasses import dataclass

import dataclasses_json

from base.ranges import Char<PERSON>ange


@dataclass
class TabSwitchEvent(dataclasses_json.DataClassJsonMixin):
    """Represents a tab-switch event on the client."""

    path: str
    """The path that was switched to."""

    file_blob_name: str
    """The blob name of the file that was switched to."""


@dataclass
class GitDiffFileInfo(dataclasses_json.DataClassJsonMixin):
    """Represents git-diff output for one file on the client."""

    content_blob_name: str
    """The blob name that contains the contents of the diff."""

    file_blob_name: str
    """The blob name of the file that the diff affects."""


@dataclass
class ReplacementText(dataclasses_json.DataClassJsonMixin):
    """Represents recent content written by the client."""

    blob_name: str
    """The blob name that this change applies to."""

    path: str
    """The path that this change applies to."""

    # TODO(AU-863): This is currently actually the number of UTF-16 code units,
    # which can differ from the number of Unicode characters.
    char_start: int
    """The start of the modified region of the blob."""

    char_end: int
    """The end of the modified region of the blob."""

    replacement_text: str
    """The new content of the modified region."""

    present_in_blob: bool
    """Indicates whether the blob already contains this change."""

    expected_blob_name: str = ""
    """The blob name of the file after applying this recent chunk.

    May be empty if debug features are disabled.
    """

    @property
    def crange(self) -> CharRange | None:
        """The start and end of the modified region of the blob."""
        if self.char_start > self.char_end:
            return None
        return CharRange(self.char_start, self.char_end)


@dataclass
class RecencyInfo(dataclasses_json.DataClassJsonMixin):
    """Represents recent client events."""

    tab_switch_events: list[TabSwitchEvent]
    git_diff_info: list[GitDiffFileInfo]
    recent_changes: list[ReplacementText]

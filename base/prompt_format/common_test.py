"""Unit tests for base/prompt_format/common."""

from base.prompt_format.common import PromptChunk


def test_chunk_comp_different_files():
    """Test chunk comparison with different files."""

    # Matching char_start
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileB.txt", char_start=0, char_end=1, blob_name="fileB"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Not matching char_start (direction 1)
    chunk1 = PromptChunk(
        text="a", path="file1.txt", char_start=0, char_end=1, blob_name="file1"
    )
    chunk2 = PromptChunk(
        text="a", path="file2.txt", char_start=5, char_end=6, blob_name="file2"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Not matching char_start (direction 2)
    chunk1 = PromptChunk(
        text="a", path="file1.txt", char_start=5, char_end=6, blob_name="file1"
    )
    chunk2 = PromptChunk(
        text="a", path="file2.txt", char_start=1, char_end=2, blob_name="file2"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1


def test_chunk_comp_same_file():
    """Test chunk comparison with same file."""

    # Matching char_start
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    assert not chunk1 < chunk2, "Chunks are equal"
    assert not chunk2 < chunk1, "Chunks are equal"

    # Not matching char_start, no overlap
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=5, char_end=6, blob_name="fileA"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Not matching char_start, overlap
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=5, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=3, char_end=8, blob_name="fileA"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Matching char_start, different lengths
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=3, char_end=5, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=3, char_end=8, blob_name="fileA"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

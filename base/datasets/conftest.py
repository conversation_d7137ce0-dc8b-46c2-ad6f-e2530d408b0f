"""Shared fixtures for protobufs and corresponding dataclasses."""

from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path
from unittest.mock import MagicMock

import pytest

from base.blob_names.python.blob_names import blob_names_pb2
from base.caching.cache import Cache
from base.datasets.completion import (
    CompletionDatum,
    CompletionFeedback,
    CompletionPosition,
    CompletionRequest,
    CompletionResolution,
    CompletionResponse,
    GranularEditEvent,
    InferenceResponse,
    RetrievedChunk,
    SingleEdit,
)
from base.datasets.gcs_blob_cache import (
    BlobCache,
    CheckpointCache,
    CheckpointContent,
    PathAndContent,
)
from base.datasets.next_edit import (
    NextEditDatum,
    NextEditGeneration,
    NextEditRequest,
    NextEditResponse,
    NextEditResult,
    NextEditSuggestion,
    RetrievalChunk,
    ScoredFileHunk,
    Tokenization,
    VCSChange,
)
from base.datasets.recency_info import (
    GitDiffFileInfo,
    RecencyInfo,
    ReplacementText,
    TabSwitchEvent,
)
from base.diff_utils import edit_events_pb2
from base.ranges import Char<PERSON>ange
from services.completion_host import completion_pb2
from services.next_edit_host import next_edit_pb2
from services.request_insight import request_insight_pb2


@dataclass
class SimulatedFile:
    """Simulates a "cached" file with a stale content manager view and a user view."""

    user_contents: str
    """The contents of the full file as seen by the user."""
    prefix_begin: int
    """Where the prefix region sent in requests begins."""
    cursor_position: int
    """Where the cursor position in the user's view is."""
    suffix_end: int
    """Where the suffix region sent in requests ends."""

    @property
    def cached_contents(self) -> str:
        """Simulates what the content manager view could be."""
        return (
            self.user_contents[: self.prefix_begin]
            # This is just a placeholder character.
            + "x" * (self.suffix_end - self.prefix_begin)
            + self.user_contents[self.suffix_end :]
        )

    @property
    def request_prefix(self) -> str:
        """The prefix that would be sent in a completion request."""
        return self.user_contents[self.prefix_begin : self.cursor_position]

    @property
    def request_suffix(self) -> str:
        """The suffix that would be sent in a completion request."""
        return self.user_contents[self.cursor_position : self.suffix_end]

    @property
    def full_prefix(self) -> str:
        """The full prefix of the user's view of the file."""
        return self.user_contents[: self.cursor_position]

    @property
    def full_suffix(self) -> str:
        """The full suffix of the user's view of the file."""
        return self.user_contents[self.cursor_position :]


@pytest.fixture
def simulated_file() -> SimulatedFile:
    # We set up a file with a simulated content manager and user view. This is probably
    # the most complicated logic in the completion dataset.
    return SimulatedFile(
        user_contents="def foo():\n    pass\n\ndef bar():\n    pass\n",
        prefix_begin=10,
        cursor_position=15,
        suffix_end=20,
    )


@pytest.fixture
def blob_cache(simulated_file: SimulatedFile) -> BlobCache:
    cached_blobs = {
        "addedb10b0": PathAndContent(
            Path("added.py"),
            "added-file-contents",
        ),
        "de1e7edb10b0": PathAndContent(
            Path("deleted.py"),
            "deleted-file-contents",
        ),
        "my-blob-name": PathAndContent(
            Path("foo.py"),
            simulated_file.cached_contents,
        ),
    }
    cache = MagicMock(Cache)
    cache.get.side_effect = lambda keys: [cached_blobs.get(key) for key in keys]
    return cache


@pytest.fixture
def checkpoint_cache() -> CheckpointCache:
    # In new format, the names are stored as raw bytes and not hex.
    # NOTE: two hex chars per byte so hex must be even.
    checkpoint_blobs = {
        "checkpoint-id": CheckpointContent(["my-blob-name", "de1e7edb10b0"])
    }
    checkpoint_cache = MagicMock(Cache)
    checkpoint_cache.get.side_effect = lambda keys: [
        checkpoint_blobs.get(key) for key in keys
    ]
    return checkpoint_cache


@pytest.fixture
def blobs_proto() -> blob_names_pb2.Blobs:
    return blob_names_pb2.Blobs(
        baseline_checkpoint_id="checkpoint-id",
        added=[bytes.fromhex("addedb10b0")],
        deleted=[bytes.fromhex("de1e7edb10b0")],
    )


@pytest.fixture
def completion_request(simulated_file: SimulatedFile) -> CompletionRequest:
    return CompletionRequest(
        prefix=simulated_file.full_prefix,
        suffix=simulated_file.full_suffix,
        path="test-path",
        # The new state is checkpoint + added - deleted = ["my-blob-name", "addedb10b0"]
        blob_names=[a.hex() for a in [bytes.fromhex("addedb10b0")]],
        output_len=10,
        timestamp=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
        recency_info=RecencyInfo(
            tab_switch_events=[
                TabSwitchEvent(path="foo.py", file_blob_name="my-blob-name")
            ],
            git_diff_info=[
                GitDiffFileInfo(
                    content_blob_name="diff-blob-name", file_blob_name="my-blob-name"
                )
            ],
            recent_changes=[
                ReplacementText(
                    blob_name="my-blob-name",
                    path="foo.py",
                    crange=CharRange(10, 20),
                    replacement_text="x",
                    present_in_blob=True,
                )
            ],
        ),
        position=CompletionPosition(
            prefix_begin=simulated_file.prefix_begin,
            cursor_position=simulated_file.cursor_position,
            suffix_end=simulated_file.suffix_end,
            blob_name="my-blob-name",
            original_prefix_length=(
                simulated_file.cursor_position - simulated_file.prefix_begin
            ),
            original_suffix_length=(
                simulated_file.suffix_end - simulated_file.cursor_position
            ),
        ),
        edit_events=[
            GranularEditEvent(
                path="foo.py",
                before_blob_name="my-blob-name",
                after_blob_name="my-blob-name",
                edits=[
                    SingleEdit(
                        before_start=10,
                        after_start=10,
                        before_text="before-text",
                        after_text="after-text",
                    )
                ],
            )
        ],
    )


@pytest.fixture
def completion_request_proto(
    simulated_file: SimulatedFile, blobs_proto: blob_names_pb2.Blobs
) -> request_insight_pb2.CompletionHostRequest:
    return request_insight_pb2.CompletionHostRequest(
        prefix=simulated_file.request_prefix,
        suffix=simulated_file.request_suffix,
        path="test-path",
        output_len=10,
        position=request_insight_pb2.CompletionHostRequestPosition(
            prefix_begin=simulated_file.prefix_begin,
            cursor_position=simulated_file.cursor_position,
            suffix_end=simulated_file.suffix_end,
            blob_name="my-blob-name",
        ),
        model="test-model",
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1, 2],
            offsets=[0, 6],
            text="prompttokens",
        ),
        blobs=blobs_proto,
        recency_info=completion_pb2.RecencyInfo(
            tab_switch_events=[
                completion_pb2.TabSwitchEvent(
                    path="foo.py",
                    file_blob_name="my-blob-name",
                )
            ],
            git_diff_file_info=[
                completion_pb2.GitDiffFileInfo(
                    content_blob_name="diff-blob-name",
                    file_blob_name="my-blob-name",
                )
            ],
            recent_changes=[
                completion_pb2.ReplacementText(
                    blob_name="my-blob-name",
                    path="foo.py",
                    char_start=10,
                    char_end=20,
                    replacement_text="x",
                    present_in_blob=True,
                )
            ],
        ),
        edit_events=[
            edit_events_pb2.GranularEditEvent(
                path="foo.py",
                before_blob_name="my-blob-name",
                after_blob_name="my-blob-name",
                edits=[
                    edit_events_pb2.SingleEdit(
                        before_start=10,
                        after_start=10,
                        before_text="before-text",
                        after_text="after-text",
                    )
                ],
            )
        ],
    )


@pytest.fixture
def completion_response() -> CompletionResponse:
    return CompletionResponse(
        text="test-generated-text",
        model="test-model",
        skipped_suffix="skipped-suffix",
        suffix_replacement_text="suffix-replacement-text",
        unknown_blob_names=["unknown-blob-name"],
        timestamp=datetime(2024, 1, 1, 0, 0, 1, tzinfo=timezone.utc),
        retrieved_chunks=[
            RetrievedChunk(
                text="test-retrieval-chunk-text",
                origin="test-retrieval-chunk-origin",
                blob_name="test-retrieval-chunk-blob-name",
                path="foo.py",
                crange=CharRange(10, 20),
            )
        ],
        tokens=["e", "st-text"],
        # NOTE(arun): Using silly log probs to sidestep FP representational issues.
        token_log_probs=[2.0, 4.0],
        prompt_tokens=["prompt", "tokens"],
        prompt_token_ids=[1, 2],
        token_ids=[1, 2],
    )


@pytest.fixture
def completion_response_proto(
    completion_response: CompletionResponse,
) -> request_insight_pb2.CompletionHostResponse:
    return request_insight_pb2.CompletionHostResponse(
        text=completion_response.text,
        skipped_suffix=completion_response.skipped_suffix,
        suffix_replacement_text=completion_response.suffix_replacement_text,
        unknown_blob_names=["unknown-blob-name"],
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1, 2],
            offsets=[1, 2],
            log_probs=[2.0, 4.0],
            text="test-text",
        ),
    )


@pytest.fixture
def completion_retrieval_response_protos(
    completion_response: CompletionResponse,
) -> list[request_insight_pb2.RetrievalResponse]:
    retrieved_chunks = completion_response.retrieved_chunks
    return [
        request_insight_pb2.RetrievalResponse(
            retrieved_chunks=[
                request_insight_pb2.RetrievalChunk(
                    text=retrieved_chunks[0].text,
                    origin=retrieved_chunks[0].origin,
                    blob_name=retrieved_chunks[0].blob_name,
                    path=retrieved_chunks[0].path,
                    char_offset=retrieved_chunks[0].crange.start,
                    char_end=retrieved_chunks[0].crange.stop,
                    chunk_index=1,
                )
            ],
        )
    ]


@pytest.fixture
def completion_datum(
    completion_request: CompletionRequest, completion_response: CompletionResponse
) -> CompletionDatum:
    return CompletionDatum(
        request_id="test-request",
        user_id="test-user",
        user_agent="test-user-agent",
        request=completion_request,
        response=completion_response,
        resolution=CompletionResolution(
            accepted=True,
            timestamp=datetime(2024, 1, 1, 0, 0, 2, tzinfo=timezone.utc),
        ),
        feedback=CompletionFeedback(
            timestamp=datetime(2024, 1, 1, 0, 0, 3, tzinfo=timezone.utc),
            rating=CompletionFeedback.Rating.POSITIVE,
            note="test-note",
        ),
        inference_response=InferenceResponse(
            timestamp=datetime(2024, 1, 1, 0, 0, 4, tzinfo=timezone.utc),
            tokens=["e", "st-text"],
            # NOTE(arun): Using silly log probs to sidestep FP representational issues.
            token_log_probs=[2.0, 4.0],
            token_ids=[1, 2],
        ),
    )


@pytest.fixture
def completion_feedback_proto() -> request_insight_pb2.CompletionFeedback:
    return request_insight_pb2.CompletionFeedback(
        # NOTE(arun): For some reason, pytest fails if we use the enum directly.
        rating=1,  # type: ignore
        note="test-note",
    )


@pytest.fixture
def inference_response_proto() -> request_insight_pb2.InferenceHostResponse:
    return request_insight_pb2.InferenceHostResponse(
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1, 2],
            offsets=[1, 2],
            log_probs=[2.0, 4.0],
            text="test-text",
        ),
    )


@pytest.fixture
def completion_request_metadata_proto(
    completion_datum: CompletionDatum,
) -> request_insight_pb2.RequestMetadata:
    assert completion_datum.user_agent is not None
    return request_insight_pb2.RequestMetadata(
        user_agent=completion_datum.user_agent,
        user_id=completion_datum.user_id,
        request_type=request_insight_pb2.RequestType.COMPLETION,
        session_id="test-session-id",
    )


@pytest.fixture
def next_edit_request() -> NextEditRequest:
    return NextEditRequest(
        model_name="test-model",
        sequence_id=0,
        lang="",
        instruction="test-instruction",
        recent_changes=[
            ReplacementText(
                path="foo.py",
                blob_name="test-blob-name",
                present_in_blob=True,
                replacement_text="test-replacement-text",
                crange=CharRange(10, 20),
            )
        ],
        vcs_change=VCSChange(working_directory_changes=[]),
        path="foo/bar.txt",
        blob_name="",
        selection_begin_char=10,
        selection_end_char=20,
        prefix="test-prefix",
        selected_text="test-selected-text",
        suffix="test-suffix",
        diagnostics=[],
        mode=NextEditRequest.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,
        scope=NextEditRequest.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,
        change_probability_override=0.0,
        edit_events=[],
        blob_names=["addedb10b0", "my-blob-name"],
        blocked_locations=[],
        restrict_to_file=False,
        timestamp=datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
        request_id="test-request",
    )


@pytest.fixture
def next_edit_request_proto(
    next_edit_request: NextEditRequest, blobs_proto: blob_names_pb2.Blobs
) -> request_insight_pb2.RINextEditRequest:
    return request_insight_pb2.RINextEditRequest(
        request=next_edit_pb2.NextEditRequest(
            path="foo/bar.txt",
            model_name="test-model",
            lang=None,
            sequence_id=0,
            instruction=next_edit_request.instruction,
            blobs=blobs_proto,
            blob_name=None,
            recent_changes=[
                completion_pb2.ReplacementText(
                    blob_name=change.blob_name,
                    path=change.path,
                    char_start=change.crange.start,
                    char_end=change.crange.stop,
                    replacement_text=change.replacement_text,
                    present_in_blob=change.present_in_blob,
                )
                for change in next_edit_request.recent_changes
            ],
            vcs_change=next_edit_pb2.VCSChange(
                working_directory_changes=[],
            ),
            diagnostics=[
                next_edit_pb2.Diagnostic(
                    location=next_edit_pb2.FileLocation(
                        path=diagnostic.location.path,
                        line_start=diagnostic.location.line_start,
                        line_end=diagnostic.location.line_end,
                    ),
                    message=diagnostic.message,
                    severity=next_edit_pb2.DiagnosticSeverity.Value(
                        diagnostic.severity.name
                    ),
                )
                for diagnostic in next_edit_request.diagnostics
            ],
            blocked_locations=[
                next_edit_pb2.FileRegion(
                    path=region.path,
                    char_start=region.char_start,
                    char_end=region.char_end,
                )
                for region in next_edit_request.blocked_locations
            ],
            selection_begin_char=next_edit_request.selection_begin_char,
            selection_end_char=next_edit_request.selection_end_char,
            prefix="test-prefix",
            selected_text="test-selected-text",
            suffix="test-suffix",
        ),
    )


@pytest.fixture
def next_edit_response() -> NextEditResponse:
    return NextEditResponse(
        retrieved_locations=[
            RetrievalChunk(
                text="test-retrieval-chunk-text",
                origin="test-retrieval-chunk-origin",
                blob_name="test-retrieval-chunk-blob-name",
                path="foo.py",
                chunk_index=1,
                score=0.5,
                char_offset=10,
                char_end=20,
            )
        ],
        generations=[
            NextEditGeneration(
                generation_id="test-generation-id",
                retrieved_chunks=[
                    RetrievalChunk(
                        text="test-retrieval-chunk-text",
                        origin="test-retrieval-chunk-origin",
                        blob_name="test-retrieval-chunk-blob-name",
                        path="foo.py",
                        chunk_index=1,
                        score=0.5,
                        char_offset=10,
                        char_end=20,
                    )
                ],
                generation_prompt=Tokenization(
                    token_ids=[1, 2],
                    offsets=[1, 2],
                    text="test-generation-prompt",
                    log_probs=[1.0, 2.0],
                ),
                generation_output=Tokenization(
                    token_ids=[1, 2],
                    offsets=[1, 2],
                    text="test-generation-output",
                    log_probs=[1.0, 2.0],
                ),
                location_chunk=RetrievalChunk(
                    text="test-location-chunk-text",
                    origin="test-location-chunk-origin",
                    blob_name="test-location-chunk-blob-name",
                    path="foo.py",
                    chunk_index=1,
                    score=0.5,
                    char_offset=10,
                    char_end=20,
                ),
                post_process_result=NextEditGeneration.PostProcessResult.LOW_PROB_CHANGED,
                editing_score=0.5,
            )
        ],
        suggestions=[
            NextEditSuggestion(
                generation_id="test-generation-id",
                description_prompt=Tokenization(
                    token_ids=[1, 2],
                    offsets=[1, 2],
                    text="test-description-prompt",
                    log_probs=[1.0, 2.0],
                ),
                description_output=Tokenization(
                    token_ids=[1, 2],
                    offsets=[1, 2],
                    text="test-description-output",
                    log_probs=[1.0, 2.0],
                ),
                result=NextEditResult(
                    suggested_edit=ScoredFileHunk(
                        path="foo.py",
                        blob_name="test-blob-name",
                        char_start=10,
                        char_end=20,
                        existing_code="test-existing-code",
                        suggested_code="test-suggested-code",
                        truncation_char=0,
                        change_description="test-change-description",
                        diff_spans=[],
                        localization_score=0.5,
                        editing_score=0.5,
                        editing_score_threshold=1.0,
                        suggestion_id="test-suggestion-id",
                    ),
                    unknown_blob_names=["unknown-blob-name"],
                    checkpoint_not_found=False,
                ),
                suggestion_order=1,
            )
        ],
        timestamp=datetime(2024, 1, 1, 0, 0, 1, tzinfo=timezone.utc),
    )


@pytest.fixture
def next_edit_response_proto(
    next_edit_response: NextEditResponse,
) -> request_insight_pb2.RINextEditResponse:
    ValueType = request_insight_pb2.RINextEditGeneration.PostProcessResult.ValueType
    return request_insight_pb2.RINextEditResponse(
        retrieved_locations=[
            request_insight_pb2.RetrievalChunk(
                text=retrieval_chunk.text,
                origin=retrieval_chunk.origin,
                blob_name=retrieval_chunk.blob_name,
                path=retrieval_chunk.path,
                char_offset=retrieval_chunk.crange.start,
                char_end=retrieval_chunk.crange.stop,
                chunk_index=retrieval_chunk.chunk_index,
                score=retrieval_chunk.score,
            )
            for retrieval_chunk in next_edit_response.retrieved_locations
        ],
        generation=[
            request_insight_pb2.RINextEditGeneration(
                generation_id=generation.generation_id,
                retrieved_chunks=[
                    request_insight_pb2.RetrievalChunk(
                        text=retrieval_chunk.text,
                        origin=retrieval_chunk.origin,
                        blob_name=retrieval_chunk.blob_name,
                        path=retrieval_chunk.path,
                        char_offset=retrieval_chunk.crange.start,
                        char_end=retrieval_chunk.crange.stop,
                        chunk_index=retrieval_chunk.chunk_index,
                        score=retrieval_chunk.score,
                    )
                    for retrieval_chunk in generation.retrieved_chunks
                ],
                generation_prompt=request_insight_pb2.Tokenization(
                    token_ids=generation.generation_prompt.token_ids,
                    offsets=generation.generation_prompt.offsets,
                    text=generation.generation_prompt.text,
                    log_probs=generation.generation_prompt.log_probs,
                ),
                generation_output=request_insight_pb2.Tokenization(
                    token_ids=generation.generation_output.token_ids,
                    offsets=generation.generation_output.offsets,
                    text=generation.generation_output.text,
                    log_probs=generation.generation_output.log_probs,
                ),
                location_chunk=request_insight_pb2.RetrievalChunk(
                    text=generation.location_chunk.text,
                    origin=generation.location_chunk.origin,
                    blob_name=generation.location_chunk.blob_name,
                    path=generation.location_chunk.path,
                    char_offset=generation.location_chunk.crange.start,
                    char_end=generation.location_chunk.crange.stop,
                    chunk_index=generation.location_chunk.chunk_index,
                    score=generation.location_chunk.score,
                ),
                post_process_result=ValueType(generation.post_process_result.value),
                editing_score=generation.editing_score,
            )
            for generation in next_edit_response.generations
        ],
        suggestions=[
            request_insight_pb2.RINextEditSuggestion(
                generation_id=suggestion.generation_id,
                description_prompt=request_insight_pb2.Tokenization(
                    token_ids=suggestion.description_prompt.token_ids,
                    offsets=suggestion.description_prompt.offsets,
                    text=suggestion.description_prompt.text,
                    log_probs=suggestion.description_prompt.log_probs,
                ),
                description_output=request_insight_pb2.Tokenization(
                    token_ids=suggestion.description_output.token_ids,
                    offsets=suggestion.description_output.offsets,
                    text=suggestion.description_output.text,
                    log_probs=suggestion.description_output.log_probs,
                ),
                result=next_edit_pb2.NextEditResponse(
                    suggested_edit=next_edit_pb2.ScoredFileHunk(
                        path=suggestion.result.suggested_edit.path,
                        blob_name=suggestion.result.suggested_edit.blob_name,
                        char_start=suggestion.result.suggested_edit.char_start,
                        char_end=suggestion.result.suggested_edit.char_end,
                        existing_code=suggestion.result.suggested_edit.existing_code,
                        suggested_code=suggestion.result.suggested_edit.suggested_code,
                        localization_score=suggestion.result.suggested_edit.localization_score,
                        editing_score=suggestion.result.suggested_edit.editing_score,
                        diff_spans=[
                            next_edit_pb2.DiffSpan(
                                original=next_edit_pb2.CharRange(
                                    start=diff_span.original.start,
                                    stop=diff_span.original.stop,
                                ),
                                updated=next_edit_pb2.CharRange(
                                    start=diff_span.updated.start,
                                    stop=diff_span.updated.stop,
                                ),
                            )
                            for diff_span in suggestion.result.suggested_edit.diff_spans
                        ],
                        truncation_char=suggestion.result.suggested_edit.truncation_char,
                        change_description=suggestion.result.suggested_edit.change_description,
                        suggestion_id=suggestion.result.suggested_edit.suggestion_id,
                        editing_score_threshold=suggestion.result.suggested_edit.editing_score_threshold,
                    ),
                    unknown_blob_names=suggestion.result.unknown_blob_names,
                    checkpoint_not_found=suggestion.result.checkpoint_not_found,
                ),
                suggestion_order=suggestion.suggestion_order,
            )
            for suggestion in next_edit_response.suggestions
        ],
    )


@pytest.fixture
def next_edit_datum(
    next_edit_request: NextEditRequest, next_edit_response: NextEditResponse
) -> NextEditDatum:
    return NextEditDatum(
        request_id="test-request",
        user_id="test-user",
        user_agent="test-user-agent",
        request=next_edit_request,
        response=next_edit_response,
    )


@pytest.fixture
def next_edit_request_metadata_proto(next_edit_datum: NextEditDatum):
    return request_insight_pb2.RequestMetadata(
        user_agent=next_edit_datum.user_agent or "",
        user_id=next_edit_datum.user_id,
        request_type=request_insight_pb2.RequestType.NEXT_EDIT,
        session_id="test-session-id",
    )

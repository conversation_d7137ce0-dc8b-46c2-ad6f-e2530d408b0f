"""Tests for base.datasets.hindsight_completion_dataset"""

import dataclasses
import io
from datetime import datetime, timedelta, timezone
from pathlib import Path
from random import Random

import pytest

from base.datasets.completion import (
    CompletionDatum,
    CompletionPosition,
    CompletionRequest,
    CompletionResponse,
    RetrievedChunk,
)
from base.datasets.dict_cache import DictCache
from base.datasets.gcs_blob_cache import PathAndContent
from base.datasets.hindsight_completion import HindsightCompletionDatum
from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDataset,
    HindsightCompletionProcessArgs,
    HindsightCompletionQueryResults,
    _dedupe_completions,
    _process,
    _sample,
)
from base.datasets.user_event import (
    CompletionRequestIdIssuedEvent,
    ContentChange,
    TextEditEvent,
)
from base.ranges.range_types import CharRange


@pytest.fixture
def datum() -> HindsightCompletionDatum:
    return HindsightCompletionDatum(
        completion=CompletionDatum(
            request_id="test-request-id",
            user_id="test-user-id",
            user_agent="test-user-agent",
            request=CompletionRequest(
                prefix="test-prefix",
                suffix="test-suffix",
                path="test-path",
                blob_names=["test-blob-name-2"],
                output_len=10,
                timestamp=datetime(2024, 1, 1, tzinfo=timezone.utc),
                position=CompletionPosition(
                    prefix_begin=0,
                    cursor_position=len("test-prefix"),
                    suffix_end=len("test-prefix") + len("test-suffix"),
                    blob_name="test-blob-name",
                    original_prefix_length=len("test-prefix"),
                    original_suffix_length=len("test-suffix"),
                ),
            ),
            response=CompletionResponse(
                text="test-text",
                model="test-model",
                skipped_suffix="test-skipped-suffix",
                suffix_replacement_text="test-suffix-replacement-text",
                unknown_blob_names=["test-unknown-blob-name"],
                retrieved_chunks=[
                    RetrievedChunk(
                        text="test-retrieval-chunk-text",
                        origin="test-retrieval-chunk-origin",
                        blob_name="test-retrieval-chunk-blob-name",
                        path="foo.py",
                        crange=CharRange(0, 25),
                    )
                ],
                timestamp=datetime(2024, 1, 1, tzinfo=timezone.utc),
                tokens=[],
                token_log_probs=[],
                prompt_tokens=[],
            ),
            resolution=None,
            feedback=None,
        ),
        ground_truth="test-ground-truth",
    )


@pytest.fixture
def completion_event() -> CompletionRequestIdIssuedEvent:
    return CompletionRequestIdIssuedEvent(
        session_id="test-session-id",
        user_id="test-user-id",
        tenant="test-tenant",
        time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        file_path="test-path",
        request_id="test-request-id",
    )


@pytest.fixture
def text_edit_event(datum) -> TextEditEvent:
    cursor = len(datum.completion.request.prefix)
    text_edit = TextEditEvent(
        session_id="test-session-id",
        user_id="test-user-id",
        tenant="test-tenant",
        time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        file_path="test-path",
        content_changes=[
            ContentChange(
                text=datum.ground_truth,
                crange=CharRange(cursor, cursor),
            )
        ],
    )
    return text_edit


@pytest.fixture
def blob_cache() -> DictCache[str, PathAndContent]:
    return DictCache(
        mapping={
            "test-blob-name": PathAndContent(
                Path("foo.py"), "test-retrieval-chunk-text"
            ),
            "test-blob-name-2": PathAndContent(
                Path("foo2.py"), "test-retrieval-chunk-text-2"
            ),
        }
    )


def test_dump_load_data(datum, blob_cache):
    """Test dumping and loading data."""

    datum2 = dataclasses.replace(datum, ground_truth="test-ground-truth-2")
    data = [datum, datum2]
    dataset = HindsightCompletionDataset(data=data, blobs=blob_cache)

    buffer = io.StringIO()
    dataset.dump_data(buffer)

    buffer.seek(0)
    data2 = HindsightCompletionDataset.load_data(buffer)

    assert data2 == dataset.data


def test_iter_data(datum, blob_cache):
    """Test iterating over the data."""
    datum2 = dataclasses.replace(datum, ground_truth="test-ground-truth-2")
    data = [datum, datum2]
    dataset = HindsightCompletionDataset(data=data, blobs=blob_cache)

    blob_names = ["test-blob-name", "test-blob-name-2"]
    expected = [(d, dict(zip(blob_names, blob_cache.get(blob_names)))) for d in data]
    assert list(dataset) == expected


def test_process_simple_multi_user(datum, completion_event, text_edit_event):
    """Test that we don't mix up different users."""
    datum2 = dataclasses.replace(
        datum,
        completion=dataclasses.replace(
            datum.completion, user_id="test-user-id-2", request_id="test-request-id-2"
        ),
        ground_truth="test-ground-truth-2",
    )
    completion_event2 = dataclasses.replace(
        completion_event, user_id="test-user-id-2", request_id="test-request-id-2"
    )
    cursor = len(datum2.completion.request.prefix)
    text_edit_event2 = dataclasses.replace(
        text_edit_event,
        user_id="test-user-id-2",
        content_changes=[
            ContentChange(
                text=datum2.ground_truth,
                crange=CharRange(cursor, cursor),
            )
        ],
    )

    results = _process(
        data=HindsightCompletionQueryResults(
            events=[
                completion_event,
                completion_event2,
                text_edit_event,
                text_edit_event2,
            ],
            completion_events=[completion_event, completion_event2],
            completions=[datum.completion, datum2.completion],
        ),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
        process_args=HindsightCompletionProcessArgs(
            time_limit=timedelta(hours=1),
        ),
    )

    assert results.errors == []
    assert results.data == [datum, datum2]


def test_duplicate_completions(datum, completion_event, text_edit_event):
    """Test that we only produce one completion per completion_event."""
    results = _process(
        data=HindsightCompletionQueryResults(
            events=[
                completion_event,
                completion_event,
                text_edit_event,
            ],
            completion_events=[completion_event],
            completions=[datum.completion, datum.completion],
        ),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
        process_args=HindsightCompletionProcessArgs(
            time_limit=timedelta(hours=1),
        ),
    )
    assert results.errors == []
    assert results.data == [datum]


def test_sample(completion_event, text_edit_event):
    """Test that we sample the correct completions."""
    events = [
        dataclasses.replace(completion_event, request_id=f"test-request-id-{i:02d}")
        for i in range(100)
    ]
    expected = events[:10]
    events += [text_edit_event] * 100
    Random(42).shuffle(events)
    results = _sample(events=events, sample_limit=10)
    assert results == expected


def test_dedupe_completions(completion_event, text_edit_event):
    """Test that we dedupe only completions."""
    events = [
        dataclasses.replace(completion_event, request_id=f"test-request-id-{i:02d}")
        if i != 0
        else text_edit_event
        for i in [0, 1, 1, 0, 0, 2, 3, 0, 1, 2, 3, 4, 0, 5, 6, 0, 7, 8, 9]
    ]
    expected = [
        dataclasses.replace(completion_event, request_id=f"test-request-id-{i:02d}")
        if i != 0
        else text_edit_event
        for i in [0, 1, 0, 0, 2, 3, 0, 4, 0, 5, 6, 0, 7, 8, 9]
    ]
    results = _dedupe_completions(events)
    assert results == expected

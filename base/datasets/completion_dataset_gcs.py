"""Dataset for completions logged via RequestInsight."""

# TODO(jeff): This is completion_dataset.py, but it uses GCS instead of BigQuery.
# This will eventually replace completion_dataset.py.
# Note that they have different interfaces.

import itertools
import logging
from collections.abc import Iterable, Mapping
from dataclasses import dataclass, replace
from datetime import datetime, timezone
from pathlib import Path
from typing import Iterator, Sequence, TextIO

from dataclasses_json import dataclass_json
from google.cloud import storage

from base.datasets.completion import CompletionDatum
from base.datasets.completion_conversion import (
    from_completion_request_proto,
    from_completion_response_proto,
    from_completion_feedback_proto,
    from_completion_resolution_proto,
    from_inference_response_proto,
)
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import (
    BlobCache,
    CheckpointCache,
    GCSBlobCache,
    GCSCheckpointCache,
    PathAndContent,
    resolve_checkpoints,
)
from base.datasets.gcs_client import (
    GCSRequestInsightFetcher,
    Request,
    group_by_event_name,
    max_by_time,
)
from base.datasets.itertools import batched
from base.datasets.pipeline import Pipeline
from base.datasets.tenants import DatasetTenant
from services.request_insight import request_insight_pb2

logger = logging.getLogger(__name__)


@dataclass_json
@dataclass
class Filters:
    """Filters for the dataset.

    TODO(jeff): Because GCS is not a database and is only keyed by request_id,
    we require the request_ids field, and the rest of the filters are simply
    postfilters, not search terms. In the future, we may transition to using
    a search database.
    """

    request_ids: list[str]
    """If set, only return events with a request ID in this list."""
    timestamp_begin: datetime | None = None
    """If set, only return events with a timestamp >= than this."""
    timestamp_end: datetime | None = None
    """If set, only return events with a timestamp < than this."""
    accepted_completion: bool | None = None
    """If set, only return events which were accepted (True) or rejected (False)."""
    model_names: list[str] | None = None
    """If set, only return events that used by any of these models."""
    min_completion_length: int | None = None
    """If set, only return events with a completion length greater than this."""
    with_feedback: bool = False
    """If set, only return events with feedback."""
    human: bool = False
    """If set, only return events from human users."""


@dataclass_json
@dataclass
class FieldsFilter:
    """Filters for the fields in CompletionDatum to return.

    By default, all fields are returned if present.
    This is useful for saving memory and time when only some fields are needed.
    Note that request and response are currently always required.
    """

    retrieval: bool = True
    """If set, return the datum.retrieval field."""
    resolution: bool = True
    """If set, return the datum.resolution field."""
    feedback: bool = True
    """If set, return the datum.feedback field."""
    inference_response: bool = True
    """If set, return the datum.inference_response field."""
    request_blob_names: bool = True
    """If set, return the datum.request.blob_names field.

    Will also save a checkpoint cache call if False.
    """


@dataclass(frozen=True)
class CompletionDataset:
    """Dataset for completions logged via RequestInsight."""

    data: Iterable[CompletionDatum]
    """The data."""

    blobs: BlobCache
    """The blob cache."""

    def __iter__(
        self,
    ) -> Iterator[tuple[CompletionDatum, Mapping[str, PathAndContent]]]:
        """Iterate over the data and blobs."""
        for datum in self.data:
            blob_names = datum.request.blob_names
            blobs = self.blobs.get(blob_names)
            blob_map = {
                blob_name: blob
                for blob_name, blob in zip(blob_names, blobs)
                if blob is not None
            }
            yield datum, blob_map

    def dump_data(self, f: TextIO):
        """Dump data to a file."""
        for datum in self.data:
            f.write(CompletionDatum.schema().dumps(datum))
            f.write("\n")

    @staticmethod
    def load_data(f: TextIO, limit: int | None = None) -> list[CompletionDatum]:
        """Load data from a file."""
        f_limit = itertools.islice(f, limit)
        data: list[CompletionDatum] = []
        for line in f_limit:
            cur_d = CompletionDatum.schema().loads(line, many=False)
            assert isinstance(cur_d, CompletionDatum)
            data.append(cur_d)
        return data

    @staticmethod
    def create_data_from_gcs(
        tenant: DatasetTenant,
        filters: Filters,
        fields_filter: FieldsFilter | None = None,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        fetcher_max_pool_connections: int = 100,
        fetcher_batch_size: int | None = 64,
        process_batch_size: int = 128,
        queue_size: int = 10,
        service_account_file: Path | None = None,
    ) -> "CompletionDataFromGCS":
        """Create a dataset of completions from GCS."""
        gcp_creds, _ = get_gcp_creds(service_account_file)
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        request_fetcher = GCSRequestInsightFetcher.from_tenant(
            tenant,
            max_pool_connections=fetcher_max_pool_connections,
        )
        checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
        checkpoint_cache = GCSCheckpointCache(
            checkpoint_bucket,
            tenant.checkpoint_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        fields_filter = fields_filter or FieldsFilter()
        return CompletionDataFromGCS(
            request_fetcher,
            checkpoint_cache,
            filters,
            fields_filter,
            fetcher_batch_size=fetcher_batch_size,
            process_batch_size=process_batch_size,
            queue_size=queue_size,
        )

    @staticmethod
    def create_blobs_from_gcs(
        tenant: DatasetTenant,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        service_account_file: Path | None = None,
    ) -> BlobCache:
        """Create a cache for the blobs via google cloud storage."""
        gcp_creds, _ = get_gcp_creds(service_account_file)
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        blob_cache = GCSBlobCache(
            blob_bucket,
            tenant.blob_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        return blob_cache


@dataclass
class _Row:
    request_id: str
    request_pb: request_insight_pb2.RequestEvent | None
    response_pb: request_insight_pb2.RequestEvent | None
    retrieval_pbs: list[request_insight_pb2.RequestEvent] | None
    resolution_pb: request_insight_pb2.RequestEvent | None
    feedback_pb: request_insight_pb2.RequestEvent | None
    inference_response_pb: request_insight_pb2.RequestEvent | None
    request_metadata_pb: request_insight_pb2.RequestEvent | None

    @staticmethod
    def from_request(request: Request) -> "_Row":
        """Create a row from a request."""
        grouped = group_by_event_name(request.events)
        return _Row(
            request_id=request.request_id,
            request_pb=max_by_time(grouped.get("completion_host_request")),
            response_pb=max_by_time(grouped.get("completion_host_response")),
            retrieval_pbs=grouped.get("retrieval_response"),
            resolution_pb=max_by_time(grouped.get("completion_resolution")),
            feedback_pb=max_by_time(grouped.get("completion_feedback")),
            inference_response_pb=max_by_time(grouped.get("inference_host_response")),
            request_metadata_pb=max_by_time(grouped.get("request_metadata")),
        )


NON_HUMAN_PREFIXES = [
    "Augment-EvalHarness",
    "AugmentHealthCheck",
    "api_proxy_client",
    "augment.info-chat",
]


@dataclass(frozen=True)
class CompletionDataFromGCS(Iterable[CompletionDatum]):
    """Completion data from GCS. Implements Iterable[CompletionDatum]."""

    request_fetcher: GCSRequestInsightFetcher
    """The request fetcher."""

    checkpoint_cache: CheckpointCache
    """The checkpoint cache."""

    filters: Filters
    """Filters to apply to the dataset."""

    fields_filter: FieldsFilter
    """Filters for the fields in CompletionDatum to return."""

    fetcher_batch_size: int | None = 64
    """Limits the number of concurrent requests to GCS when fetching requests.

    Note that None defaults to min(32, (os.cpu_count() or 1) + 4), but we default
    to 64 here since we should be IO-bound.
    """

    process_batch_size: int = 128
    """Processes requests in batches. In particular, this batches requests to the checkpoint cache,
    which is an LRU cache which does not have a fine-grained lock."""

    queue_size: int = 10
    """The size of the queue to use when fetching blobs. We use this
    queue to pipeline bigquery and GCS requests, and set a max size to
    avoid memory issues if users use this dataset in a notebook and don't
    iterate through the whole dataset."""

    def __iter__(self) -> Iterator[CompletionDatum]:
        # NOTE(arun): To hide the (often considerable) network latency in getting data
        # from bigquery, and blobs from GCS, we process the data in pipelined batches.
        # Getting data in batches speeds up the script by ~5-10x, and pipelining the
        # script speeds it up by another factor of 2-3x.
        request_event_names = self.get_request_event_names(self.fields_filter)
        requests = self.request_fetcher.get_requests(
            self.filters.request_ids,
            request_event_names=request_event_names,
            batch_size=self.fetcher_batch_size,
        )
        pipeline = (
            Pipeline.from_source(batched(requests, self.process_batch_size))
            .and_then(self._process_batch)
            .run(max_queue_size=self.queue_size)
        )
        for batch in pipeline:
            yield from batch

    def _process_batch(
        self, batch: Iterable[Request | Exception]
    ) -> Iterable[CompletionDatum]:
        """Process a batch of requests."""
        rows = [
            _Row.from_request(request)
            for request in batch
            if isinstance(request, Request)
        ]
        rows = [row for row in rows if self.passes_filter(self.filters, row)]
        rows = [
            row
            for row in rows
            if row.request_pb and row.response_pb and row.request_metadata_pb
        ]

        if self.fields_filter.request_blob_names:
            # A tragedy of type checking that we need to include the if still.
            blobs_list = [
                row.request_pb.completion_host_request.blobs
                for row in rows
                if row.request_pb
            ]
            assert len(rows) == len(blobs_list)
            blob_names_list = resolve_checkpoints(self.checkpoint_cache, blobs_list)
            # This will replace the blob_names field if not already set.
            return [
                self._make_datum(row, blob_names)
                for row, blob_names in zip(rows, blob_names_list)
            ]
        else:
            # This will clear the blob_names field.
            return [self._make_datum(row, None) for row in rows]

    def _make_datum(self, row: _Row, blob_names: list[str] | None) -> CompletionDatum:
        """Make a CompletionDatum from a row.

        If blob_names is present, we replace the blob_names field if not already set,
        so requests not in checkpoint format will remain with blob_names unchanged.
        If blob_names is None, we clear the blob_names field.
        """
        assert row.request_pb
        assert row.request_metadata_pb
        assert row.response_pb

        request = from_completion_request_proto(
            row.request_pb.completion_host_request,
            row.request_pb.time.ToDatetime(tzinfo=timezone.utc),
            row.request_id,
        )
        if blob_names is None:
            request = replace(request, blob_names=[])
        elif not request.blob_names:
            request = replace(request, blob_names=blob_names)

        return CompletionDatum(
            request_id=row.request_id,
            user_id=row.request_metadata_pb.request_metadata.user_id,
            request=request,
            response=from_completion_response_proto(
                row.response_pb.completion_host_response,
                [
                    retrieval_pb.retrieval_response
                    for retrieval_pb in (row.retrieval_pbs or [])
                ],
                row.request_pb.completion_host_request,
                row.response_pb.time.ToDatetime(tzinfo=timezone.utc),
            ),
            resolution=from_completion_resolution_proto(
                row.resolution_pb.completion_resolution,
                row.resolution_pb.time.ToDatetime(tzinfo=timezone.utc),
            )
            if row.resolution_pb
            else None,
            feedback=from_completion_feedback_proto(
                row.feedback_pb.completion_feedback,
                row.feedback_pb.time.ToDatetime(tzinfo=timezone.utc),
            )
            if row.feedback_pb
            else None,
            inference_response=from_inference_response_proto(
                row.inference_response_pb.inference_host_response,
                row.inference_response_pb.time.ToDatetime(tzinfo=timezone.utc),
            )
            if row.inference_response_pb
            else None,
            user_agent=row.request_metadata_pb.request_metadata.user_agent,
        )

    @staticmethod
    def get_request_event_names(fields_filter: FieldsFilter) -> frozenset[str]:
        """Returns the request event names to fetch from GCS."""
        request_event_names = [
            "completion_host_request",
            "completion_host_response",
            "request_metadata",
        ]
        if fields_filter.retrieval:
            request_event_names.append("retrieval_response")
        if fields_filter.resolution:
            request_event_names.append("completion_resolution")
        if fields_filter.feedback:
            request_event_names.append("completion_feedback")
        if fields_filter.inference_response:
            request_event_names.append("inference_host_response")
        return frozenset(request_event_names)

    @staticmethod
    def passes_filter(filters: Filters, row: _Row) -> bool:
        """Filter a row. Returns false if the row should be filtered.

        Note that request_ids is not a postfilter, so not checked here.
        """
        if filters.timestamp_begin is not None:
            if not row.request_pb:
                return False
            if (
                row.request_pb.time.ToDatetime(tzinfo=timezone.utc)
                < filters.timestamp_begin
            ):
                return False

        if filters.timestamp_end is not None:
            if not row.request_pb:
                return False
            if (
                row.request_pb.time.ToDatetime(tzinfo=timezone.utc)
                >= filters.timestamp_end
            ):
                return False

        if filters.accepted_completion is not None:
            if not row.resolution_pb:
                return False
            accepted = row.resolution_pb.completion_resolution.accepted_idx >= 0
            if accepted != filters.accepted_completion:
                return False

        if filters.model_names is not None:
            if not row.request_pb:
                return False
            if row.request_pb.completion_host_request.model not in filters.model_names:
                return False

        if filters.min_completion_length is not None:
            if not row.response_pb:
                return False
            if (
                len(row.response_pb.completion_host_response.text)
                < filters.min_completion_length
            ):
                return False

        if filters.with_feedback:
            if not row.feedback_pb:
                return False

        if filters.human:
            if not row.request_metadata_pb:
                return False
            if any(
                row.request_metadata_pb.request_metadata.user_agent.startswith(prefix)
                for prefix in NON_HUMAN_PREFIXES
            ):
                return False

        return True

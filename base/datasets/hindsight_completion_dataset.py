"""Dataset for completions logged via RequestInsight with hindsight ground truths.

This provides the HindsightCompletionDataset class. The dataset is created in the
following way (see query_and_process, which is called by create_data_from_bigquery):
1. Query for all user events within some time period from user_events.
2. Apply sampling to get completion events.
3. Query for those completion datums from request_events via CompletionDataset.
4. Process the user events in order and determine ground truths for (3).

This specific order is since (3) is the most expensive (on the order of 1TB or more),
whereas (1) is extremely cheap (on the order of 1GB or less). Moreover, we can view
(1) as the "true" distribution and (2) as sampling from the "true" distribution.

The processed events will be returned in order of ascending request_id, as this is
essentially random, but deterministic across runs. The query_and_process method also
returns reasons for any sample in (2) where we cannot derive a ground truth as
per-sample processing "errors", and these samples are filtered out during (4).
"""

import dataclasses
import itertools
import logging
from collections import Counter, defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import (
    Iterable,
    Iterator,
    Mapping,
    Optional,
    TextIO,
    Tuple,
)

import dataclasses_json
import marshmallow
from google.cloud import storage  # type: ignore

from base.datasets.completion import CompletionDatum
from base.datasets.completion_dataset_gcs import CompletionDataset, Filters
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import (
    BlobCache,
    GCSBlobCache,
    PathAndContent,
)
from base.datasets.hindsight_completion import HindsightCompletionDatum
from base.datasets.hindsight_lib import TextRange, UninterruptedHeuristic
from base.datasets.tenants import DatasetTenant
from base.datasets.user_event import (
    CompletionRequestIdIssuedEvent,
    TextEditEvent,
)
from base.datasets.user_event_lib import (
    UserEventFilters,
    UserEventStream,
    UserEventUnion,
)

logger = logging.getLogger(__name__)


class NoCompletionEventError(ValueError):
    """None of the completion event was not found."""


@dataclass(frozen=True)
class HindsightCompletionQueryArgs(dataclasses_json.DataClassJsonMixin):
    """Args for querying the hindsight completion dataset."""

    user_ids: Optional[list[str]] = None
    """If set, only return events with a user ID in this list."""

    session_id_begin: Optional[str] = None
    """If set, only return events with session IDs >= than this."""

    session_id_end: Optional[str] = None
    """If set, only return events with session IDs < than this."""

    user_event_limit: Optional[int] = None
    """If set, the maximum number of events to return."""

    sample_limit: Optional[int] = None
    """If set, the maximum number of completions to sample."""


@dataclass(frozen=True)
class HindsightCompletionProcessArgs(dataclasses_json.DataClassJsonMixin):
    """Args for processing the hindsight completion dataset."""

    # NOTE: may not have round-trip serialize/deserialize for very large timedeltas
    # (>270 years), but this is fine since we are just dumping this for logging.
    time_limit: timedelta = dataclasses.field(
        default=timedelta(hours=1),
        metadata=dataclasses_json.config(
            encoder=lambda td: td.total_seconds(),
            decoder=lambda sec: timedelta(seconds=sec),
            mm_field=marshmallow.fields.TimeDelta(serialization_type=float),
        ),
    )
    """The amount of time we watch for text edits to a range."""

    max_interruption_chars: int = 0
    """The max total number of characters across all interruptions to text edits to a
    range. Results in an "error" if exceeded. These samples will be filtered out."""

    allow_overlaps: bool = False
    """If False, text edits that touch a range, but are not fully contained by it,
    will result in an "error". These samples will be filtered out."""

    allow_empty_ground_truth: bool = False
    """If False, treat empty ground truth as an "error".
    These samples will be filtered out."""

    min_blobs: int = 0
    """The minimum number of blobs required for a completion. If a completion has fewer
    blobs, it will be filtered out."""

    context_size: Optional[int] = None
    """The number of characters to include in the context range on either side of the
    completion. For example, if the cursor is at (2, 2) and context_size = 2, then we
    will also track the range (0, 4) to use for change trimming. If None, no context
    is included *and* change trimming is turned off."""


@dataclass(frozen=True)
class HindsightCompletionDataset:
    """Dataset for completions logged via RequestInsight with hindsight ground truths."""

    data: Iterable[HindsightCompletionDatum]
    """The data."""

    blobs: BlobCache
    """The blob cache."""

    def __iter__(
        self,
    ) -> Iterator[Tuple[HindsightCompletionDatum, Mapping[str, PathAndContent]]]:
        """Iterate over the data and blobs."""
        for datum in self.data:
            blob_names = datum.completion.request.blob_names
            if (
                datum.completion.request.position is not None
                and datum.completion.request.position.blob_name
                and datum.completion.request.position.blob_name not in blob_names
            ):
                blob_names = blob_names + [datum.completion.request.position.blob_name]
            blobs = self.blobs.get(blob_names)
            blob_map = {
                blob_name: blob
                for blob_name, blob in zip(blob_names, blobs)
                if blob is not None
            }
            yield datum, blob_map

    def dump_data(self, f: TextIO):
        """Dump data to a file."""
        for datum in self.data:
            f.write(HindsightCompletionDatum.schema().dumps(datum))
            f.write("\n")

    @staticmethod
    def load_data(
        f: TextIO, limit: Optional[int] = None
    ) -> list[HindsightCompletionDatum]:
        """Load data from a file."""
        f_limit = itertools.islice(f, limit)
        data: list[HindsightCompletionDatum] = []
        for line in f_limit:
            cur_d = HindsightCompletionDatum.schema().loads(line, many=False)
            assert isinstance(cur_d, HindsightCompletionDatum)
            data.append(cur_d)
        return data

    @staticmethod
    def create_data_from_bigquery(
        tenant: DatasetTenant,
        timestamp_begin: datetime,
        timestamp_end: datetime,
        query_args: HindsightCompletionQueryArgs = HindsightCompletionQueryArgs(),
        process_args: HindsightCompletionProcessArgs = HindsightCompletionProcessArgs(),
    ) -> list[HindsightCompletionDatum]:
        """Create data from BigQuery. See query_and_process."""
        _, results = query_and_process(
            tenant,
            timestamp_begin,
            timestamp_end,
            query_args,
            process_args,
        )
        return results.data

    @staticmethod
    def create_blobs_from_gcs(
        tenant: DatasetTenant,
        blob_cache_size_bytes: int = 2**30,
        blob_cache_num_threads: int = 32,
        service_account_file: Optional[Path] = None,
    ) -> BlobCache:
        """Create a cache for the blobs via google cloud storage."""
        gcp_creds, _ = get_gcp_creds(service_account_file)
        storage_client = storage.Client(
            project=tenant.project_id, credentials=gcp_creds
        )
        blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
        blob_cache = GCSBlobCache(
            blob_bucket,
            tenant.blob_bucket_prefix,
            blob_cache_size_bytes,
            num_threads=blob_cache_num_threads,
        )
        return blob_cache


@dataclass(frozen=True)
class HindsightCompletionQueryResults:
    """Raw data for hindsight completion dataset."""

    events: list[UserEventUnion]
    """All user events returned by the user_events query."""
    completion_events: list[CompletionRequestIdIssuedEvent]
    """A random sample of completion events from `events`. Ordered by request_id."""
    completions: list[CompletionDatum]
    """Completion datums for the sampled `completion_events` that were able to be
    returned by the request_events query."""


@dataclass(frozen=True)
class HindsightCompletionError:
    """An error when processing the hindsight completion dataset."""

    event: CompletionRequestIdIssuedEvent
    """The completion event."""
    datum: Optional[CompletionDatum]
    """The completion datum, if it exists."""
    error_type: str
    """The error type."""
    error_detail: str
    """The error detail."""


@dataclass(frozen=True)
class HindsightCompletionProcessResults:
    """Processed data for hindsight completion dataset."""

    data: list[HindsightCompletionDatum]
    """Sampled completion events that we were able to determine ground truths for."""
    errors: list[HindsightCompletionError]
    """Errors for sampled completion events that were not successfully processed."""


def query_and_process(
    tenant: DatasetTenant,
    timestamp_begin: datetime,
    timestamp_end: datetime,
    query_args: HindsightCompletionQueryArgs = HindsightCompletionQueryArgs(),
    process_args: HindsightCompletionProcessArgs = HindsightCompletionProcessArgs(),
) -> Tuple[HindsightCompletionQueryResults, HindsightCompletionProcessResults]:
    """Query bigquery and process the data to determine ground truth.

    We first query user_events for completion or text_edit events in the time range,
    and then sample completions with the smallest request_ids. We then query
    CompletionDataset for the full completion data for those request_ids.

    We then process the data to determine ground truth using the UninterruptedHeuristic.
    Any request that was sampled, but we could not determine ground truth for, is returned
    as an error with a categorical type and detail.
    """
    query_results = _query(
        tenant,
        timestamp_begin,
        timestamp_end,
        query_args,
    )
    results = _process(
        query_results,
        timestamp_end,
        process_args,
    )
    return query_results, results


def _dedupe_completions(events: list[UserEventUnion]) -> list[UserEventUnion]:
    """Deduplicates completion events by request ids, keeping only the first event.

    There are two cases of duplicates:
    1. The duplicate events are exactly equal in all other fields.
    2. The duplicate events have the same request id, but different data.
    Both of these indicate something is weird or wrong with data collection.
    """
    deduped_events = []
    completion_map: dict[str, CompletionRequestIdIssuedEvent] = {}
    request_counter = Counter()

    for event in events:
        if not isinstance(event, CompletionRequestIdIssuedEvent):
            deduped_events.append(event)
            continue

        request_counter[event.request_id] += 1
        if event.request_id in completion_map:
            completion_event = completion_map[event.request_id]
            if event != completion_event:
                logger.warning(
                    "Duplicate completion event has different data: %s != %s",
                    event,
                    completion_event,
                )
            continue

        completion_map[event.request_id] = event
        deduped_events.append(event)

    # Print a summary
    duplicate_counter = Counter({k: v for k, v in request_counter.items() if v > 1})
    if len(duplicate_counter) > 0:
        logger.warning(
            "Found %s duplicate completions. Total count: %s. "
            "Top 10 duplicate request_ids: %s",
            len(duplicate_counter),
            duplicate_counter.total(),
            duplicate_counter.most_common(10),
        )

    return deduped_events


def _sample(
    events: list[UserEventUnion],
    sample_limit: Optional[int],
) -> list[CompletionRequestIdIssuedEvent]:
    """Sample the completion events from all user events by smallest request_id."""
    completion_events = [
        event for event in events if isinstance(event, CompletionRequestIdIssuedEvent)
    ]
    logger.info("Found %s completion events.", len(completion_events))

    # Sort and sample the smallest request ids.
    completion_events = sorted(completion_events, key=lambda x: x.request_id)
    if sample_limit is not None:
        completion_events = completion_events[:sample_limit]
    return completion_events


def _query(
    tenant: DatasetTenant,
    timestamp_begin: datetime,
    timestamp_end: datetime,
    query_args: HindsightCompletionQueryArgs,
) -> HindsightCompletionQueryResults:
    """Query for the raw user event and completion data."""

    # Query user events
    filters = UserEventFilters(
        timestamp_begin=timestamp_begin,
        timestamp_end=timestamp_end,
        user_ids=query_args.user_ids,
        event_types=[
            "completion_request_id_issued",
            "text_edit",
        ],
        session_id_begin=query_args.session_id_begin,
        session_id_end=query_args.session_id_end,
    )
    stream = UserEventStream.from_query(tenant, filters, query_args.user_event_limit)
    events = list(stream.events)
    logger.info("Found %s user events in the time range.", len(events))

    events = _dedupe_completions(events)
    logger.info("%s user events after deduping.", len(events))

    # Sample completions by smallest request id
    completion_events = _sample(events, query_args.sample_limit)
    request_ids = [completion.request_id for completion in completion_events]
    if not request_ids:
        raise NoCompletionEventError(
            "No completion events found. Fix your query, or we are missing data."
        )

    # Query completions.
    logger.info("Querying for %s completion events.", len(request_ids))
    completions = CompletionDataset.create_data_from_gcs(
        tenant=tenant, filters=Filters(request_ids=request_ids)
    )
    completions = list(completions)
    logger.info("Found %s completions.", len(completions))

    return HindsightCompletionQueryResults(events, completion_events, completions)


def _validate_completion(
    completion: CompletionDatum, completion_user_event: CompletionRequestIdIssuedEvent
) -> Optional[HindsightCompletionError]:
    """Validates that the completion datum from request_events matches the completion
    event from user_events. Returns None if they match.
    """
    if completion.user_id != completion_user_event.user_id:
        return HindsightCompletionError(
            completion_user_event,
            completion,
            "user_id_mismatch",
            f"{completion.user_id} != {completion_user_event.user_id}",
        )

    if completion_user_event.file_path == "":
        return HindsightCompletionError(
            completion_user_event,
            completion,
            "empty_file_path",
            f"{completion_user_event.file_path}",
        )

    if completion.request.path != completion_user_event.file_path:
        return HindsightCompletionError(
            completion_user_event,
            completion,
            "file_path_mismatch",
            f"{completion.request.path} != {completion_user_event.file_path}",
        )

    # We use cursor position to determine the range to watch text edits.
    # Cursor position might not equal the prefix length, e.g. in jupyter notebooks.
    if completion.request.position is None:
        return HindsightCompletionError(
            completion_user_event, completion, "missing_position", ""
        )

    # Validate auxiliary blob_names are included in the blob_names list.
    # We also log a warning if false, since this shouldn't happen.
    if completion.request.recency_info is not None:
        blob_names = set(completion.request.blob_names)
        if completion.request.position.blob_name is not None:
            blob_names.add(completion.request.position.blob_name)

        for change in completion.request.recency_info.recent_changes:
            # NOTE: we allow empty blob_names, which can be a special value
            # (e.g. for chat context)
            if change.blob_name and change.blob_name not in blob_names:
                logger.warning(
                    "Missing recent_changes blob name %s, request_id: %s",
                    change.blob_name,
                    completion.request_id,
                )
                return HindsightCompletionError(
                    completion_user_event,
                    completion,
                    "missing_recent_changes_blob",
                    f"{change.blob_name}",
                )
        for event in completion.request.recency_info.tab_switch_events:
            if event.file_blob_name not in blob_names:
                logger.warning(
                    "Missing tab_switch_events blob name %s in request %s",
                    event.file_blob_name,
                    completion.request_id,
                )
                return HindsightCompletionError(
                    completion_user_event,
                    completion,
                    "missing_tab_switch_events_blob",
                    f"{event.file_blob_name}",
                )
        for info in completion.request.recency_info.git_diff_info:
            if info.file_blob_name not in blob_names:
                logger.warning(
                    "Missing git_diff_info blob name %s in request %s",
                    info.file_blob_name,
                    completion.request_id,
                )
                return HindsightCompletionError(
                    completion_user_event,
                    completion,
                    "missing_git_diff_info_blob",
                    f"{info.file_blob_name}",
                )

    return None


def _filter_completion(
    completion: CompletionDatum,
    completion_user_event: CompletionRequestIdIssuedEvent,
    process_args: HindsightCompletionProcessArgs,
) -> Optional[HindsightCompletionError]:
    """Filter out completions that we do not want to process."""
    if len(completion.request.blob_names) < process_args.min_blobs:
        return HindsightCompletionError(
            completion_user_event,
            completion,
            "too_few_blobs",
            f"{len(completion.request.blob_names)}",
        )

    return None


def _process(
    data: HindsightCompletionQueryResults,
    timestamp_end: datetime,
    process_args: HindsightCompletionProcessArgs,
) -> HindsightCompletionProcessResults:
    """Process the raw data to determine ground truth.

    data: The raw data returned by the SQL queries.
    timestamp_end: The end time for the data, should be the same as for query.
    process_args: Args for processing the data, see HindsightCompletionProcessArgs.
    """
    # errors will have one entry per completion event, and as we add to errors,
    # we will remove the corresponding entry from completions_map.
    errors: list[HindsightCompletionError] = []
    completions_map = {
        completion.request_id: completion for completion in data.completions
    }

    # Warn about duplicate completion datums. This is not fatal, but could indicate
    # an issue in the BQ query.
    duplicates = Counter(completion.request_id for completion in data.completions)
    duplicates = Counter({k: v for k, v in duplicates.items() if v > 1})
    if len(duplicates) > 0:
        logger.warning(
            "Found %s duplicate completions. Total count: %s. "
            "Top 10 duplicate request_ids: %s",
            len(duplicates),
            duplicates.total(),
            duplicates.most_common(10),
        )

    # Check that the events are in chronological order.
    for i in range(1, len(data.events)):
        if data.events[i - 1].time > data.events[i].time:
            raise ValueError(
                f"Events are not in chronological order: "
                f"{data.events[i - 1].time} > {data.events[i].time}"
            )

    # Check the returned request ids are what we requested.
    # This shouldn't fail, but doesn't hurt to check.
    completion_events_map = {
        event.request_id: event for event in data.completion_events
    }
    if extra_ids := (completions_map.keys() - completion_events_map.keys()):
        raise ValueError(f"Got extra request ids: {extra_ids}")

    # Validate and filter individual completions
    for event in data.completion_events:
        if event.request_id not in completions_map:
            errors.append(
                HindsightCompletionError(event, None, "missing_completion", "")
            )
            continue
        completion = completions_map[event.request_id]
        error = _validate_completion(completion, event)
        error = error or _filter_completion(completion, event, process_args)
        if error is not None:
            errors.append(error)
            completions_map.pop(event.request_id)

    # Apply heuristic by processing all events in chronological order
    # We bucket the heuristic by (tenant, user, session)
    heuristics = defaultdict(
        lambda: UninterruptedHeuristic[str, str](
            process_args.time_limit,
            process_args.max_interruption_chars,
            process_args.allow_overlaps,
            trim_changes=process_args.context_size is not None,
        )
    )
    seen_request_ids = set[str]()
    for event in data.events:
        session = (event.tenant, event.user_id, event.session_id)

        # Start tracking the completion if it was sampled, queried, and validated.
        if isinstance(event, CompletionRequestIdIssuedEvent):
            if event.request_id not in completions_map:
                continue
            if event.request_id in seen_request_ids:
                logger.warning("Duplicate request id %s", event.request_id)
                continue
            seen_request_ids.add(event.request_id)

            completion = completions_map[event.request_id]
            assert completion.request.position is not None
            cursor = completion.request.position.cursor_position
            text_range = TextRange.from_text(start=cursor, text="")
            context_range = None

            if process_args.context_size is not None:
                context_text = (
                    completion.request.prefix[
                        len(completion.request.prefix) - process_args.context_size :
                    ]
                    + completion.request.suffix[: process_args.context_size]
                )
                context_range = TextRange.from_text(
                    start=cursor - process_args.context_size,
                    text=context_text,
                )

            heuristics[session].add_request(
                event.time, event.request_id, event.file_path, text_range, context_range
            )

        # Update all the user's tracked requests with this text edit event.
        if isinstance(event, TextEditEvent):
            heuristics[session].add_changes(
                event.time, event.file_path, event.content_changes
            )

    all_results: dict[str, UninterruptedHeuristic.Result] = {}
    for session in heuristics:
        results = heuristics[session].finish(timestamp_end)
        all_results.update(results)

    # Collect ground truths and errors
    hindsight_completions = []
    for event in data.completion_events:
        # May have been removed due to validation
        if event.request_id not in completions_map:
            continue
        assert event.request_id in all_results

        completion = completions_map[event.request_id]
        result = all_results[completion.request_id]
        if result.ground_truth is None:
            errors.append(
                HindsightCompletionError(
                    event, completion, result.error_type, result.error_detail
                )
            )
            continue

        if not process_args.allow_empty_ground_truth and result.ground_truth == "":
            errors.append(
                HindsightCompletionError(event, completion, "empty_ground_truth", "")
            )
            continue

        hindsight_completions.append(
            HindsightCompletionDatum(
                completion=completion, ground_truth=result.ground_truth
            )
        )
    return HindsightCompletionProcessResults(data=hindsight_completions, errors=errors)

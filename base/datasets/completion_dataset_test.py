"""Tests for base.datasets.completion.

bazel test //base/datasets:completion_test
"""

from datetime import datetime, timezone

from google.protobuf.json_format import MessageToDict

from base.datasets.completion_dataset import CompletionDataset, _build_query


def test_get_completions(
    completion_datum,
    completion_request_proto,
    completion_response_proto,
    completion_retrieval_response_protos,
    completion_feedback_proto,
    inference_response_proto,
    blob_cache,
    checkpoint_cache,
):
    """Test that we can get a CompletionDatum from a CompletionDataset using the get_completions method."""

    # We expect the dataset to return this CompletionDatum.
    expected = completion_datum

    # Set up the row in the dataset.
    assert expected.user_agent is not None
    assert expected.resolution is not None
    assert expected.resolution is not None
    assert expected.feedback is not None
    assert expected.inference_response is not None
    dataset = CompletionDataset(
        [
            {
                "request_id": expected.request_id,
                "user_id": expected.user_id,
                "user_agent": expected.user_agent,
                "request_timestamp": expected.request.timestamp,
                "request_json": MessageToDict(completion_request_proto),
                "response_timestamp": expected.response.timestamp,
                "response_json": MessageToDict(completion_response_proto),
                "retrieval_jsons": [
                    MessageToDict(retrieval)
                    for retrieval in completion_retrieval_response_protos
                ],
                "accepted": expected.resolution.accepted,
                "resolution_timestamp": expected.resolution.timestamp,
                "feedback_json": MessageToDict(completion_feedback_proto),
                "feedback_timestamp": expected.feedback.timestamp,
                "inference_response_json": MessageToDict(inference_response_proto),
                "inference_response_timestamp": expected.inference_response.timestamp,
            },
        ],
        1,
        blob_cache,
        checkpoint_cache,
    )

    (actual,) = list(dataset.get_completions())
    assert actual == expected


def test_build_query():
    filters = CompletionDataset.Filters(
        timestamp_begin=datetime(2024, 1, 1, tzinfo=timezone.utc),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
        model_names=["test-model"],
        accepted_completion=True,
        min_completion_length=10,
        max_resolution_time_ms=1000,
        min_reject_resolution_time_ms=1000,
        request_ids=["test-request-id"],
        with_feedback=True,
    )
    query, job_config = _build_query(
        "test-dataset", "test-tenant", filters, "request_id", 10
    )
    query_parameters = job_config.query_parameters

    # This converts query parameters to dicts for easy asserting.
    query_parameters_dicts = [p.to_api_repr() for p in query_parameters]

    expected_query_parameters_dicts = [
        {
            "parameterType": {"type": "ARRAY", "arrayType": {"type": "STRING"}},
            "parameterValue": {"arrayValues": [{"value": "test-request-id"}]},
            "name": "request_ids",
        },
    ]
    expected_query_parameters_tuples = [
        ("tenant", "STRING", "test-tenant"),
        ("timestamp_begin", "TIMESTAMP", "2024-01-01 00:00:00+00:00"),
        ("timestamp_end", "TIMESTAMP", "2024-01-02 00:00:00+00:00"),
        ("model_names", "ARRAY", ["test-model"]),
        ("min_completion_length", "INT64", "10"),
        ("max_resolution_time_ms", "INT64", "1000"),
        ("min_reject_resolution_time_ms", "INT64", "1000"),
        ("accepted_completion", "BOOL", "true"),
        ("limit", "INT64", "10"),
    ]
    for name, type_, value in expected_query_parameters_tuples:
        if isinstance(value, list):
            cur_value = {"arrayValues": [{"value": v} for v in value]}
        else:
            cur_value = {"value": value}
        if type_ == "ARRAY":
            cur_type = {"type": type_, "arrayType": {"type": "STRING"}}
        else:
            cur_type = {"type": type_}
        expected_query_parameters_dicts.append(
            {
                "parameterType": cur_type,
                "parameterValue": cur_value,
                "name": name,
            }
        )
    query_parameters_dicts.sort(key=lambda x: x["name"])
    expected_query_parameters_dicts.sort(key=lambda x: x["name"])
    assert query_parameters_dicts == expected_query_parameters_dicts

    # Assert that the query parameters are used in the query
    for query_parameter_dict in expected_query_parameters_dicts:
        assert f"@{query_parameter_dict['name']}" in query

    # Check for presence of some other parts of the query.
    assert "FROM `test-dataset.request_event`" in query
    assert "ORDER BY request.request_id" in query
    assert "resolution.accepted = " in query
    for table in ["resolution", "feedback"]:
        assert f"{table}.raw_json IS NOT NULL" in query


def test_build_query_no_acceptance_filters():
    # No acceptance filter.
    filters = CompletionDataset.Filters(
        timestamp_begin=datetime(2024, 1, 1, tzinfo=timezone.utc),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    query, _ = _build_query("test-dataset", "test-tenant", filters, "request_id", 10)
    assert "resolution.accepted = " not in query


def test_build_query_accepted_only():
    # Accepted completions only.
    filters = CompletionDataset.Filters(
        timestamp_begin=datetime(2024, 1, 1, tzinfo=timezone.utc),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
        accepted_completion=True,
    )
    query, job_config = _build_query(
        "test-dataset", "test-tenant", filters, "request_id", 10
    )

    # This converts query parameters to dicts for easy asserting.
    query_parameters_dicts = [p.to_api_repr() for p in job_config.query_parameters]
    accepted_completion_parameter = next(
        p for p in query_parameters_dicts if p["name"] == "accepted_completion"
    )

    assert "resolution.accepted = @accepted_completion" in query
    assert accepted_completion_parameter["parameterValue"]["value"] == "true"


def test_build_query_rejected_only():
    # Rejected completions only.
    filters = CompletionDataset.Filters(
        timestamp_begin=datetime(2024, 1, 1, tzinfo=timezone.utc),
        timestamp_end=datetime(2024, 1, 2, tzinfo=timezone.utc),
        accepted_completion=False,
    )
    query, job_config = _build_query(
        "test-dataset", "test-tenant", filters, "request_id", 10
    )
    assert "resolution.accepted = " in query

    # This converts query parameters to dicts for easy asserting.
    query_parameters_dicts = [p.to_api_repr() for p in job_config.query_parameters]
    accepted_completion_parameter = next(
        p for p in query_parameters_dicts if p["name"] == "accepted_completion"
    )

    assert accepted_completion_parameter["parameterValue"]["value"] == "false"

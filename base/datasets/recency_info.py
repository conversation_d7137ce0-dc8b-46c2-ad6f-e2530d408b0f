"""Common datatypes across tasks."""

from dataclasses import dataclass

import dataclasses_json

from base.ranges.range_types import Char<PERSON>ange


@dataclass
class TabSwitchEvent(dataclasses_json.DataClassJsonMixin):
    """Represents a tab-switch event on the client."""

    path: str
    """The path that was switched to."""

    file_blob_name: str
    """The blob name of the file that was switched to."""


@dataclass
class GitDiffFileInfo(dataclasses_json.DataClassJsonMixin):
    """Represents git-diff output for one file on the client."""

    content_blob_name: str
    """The blob name that contains the contents of the diff."""

    file_blob_name: str
    """The blob name of the file that the diff affects."""


@dataclass
class ReplacementText(dataclasses_json.DataClassJsonMixin):
    """Represents recent content written by the client."""

    blob_name: str
    """The blob name that this change applies to."""

    path: str
    """The path that this change applies to."""

    crange: Char<PERSON><PERSON>e
    """The start and end of the modified region of the blob."""

    replacement_text: str
    """The new content of the modified region."""

    present_in_blob: bool
    """Indicates whether the blob already contains this change."""

    expected_blob_name: str = ""
    """The blob name of the file after applying all replacements.

    This is defaulted to the empty string for backwards compatibility.
    """


@dataclass
class RecencyInfo(dataclasses_json.DataClassJsonMixin):
    """Represents recent client events."""

    tab_switch_events: list[TabSwitchEvent]
    git_diff_info: list[GitDiffFileInfo]
    recent_changes: list[ReplacementText]

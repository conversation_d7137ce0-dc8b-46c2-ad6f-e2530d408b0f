"""Dataset for next edits logged via RequestInsight.

The types in the dataset are defined to be separate from both the proto and library types.
They originally have the same field names and types, but this is not guaranteed to hold over time.
Proto and library types will inevitably change over time, but the types here will remain stable.

Custom conversion logic will need to be added to convert different version of the proto to the
dataset types. When originally desigining the dataset, we were able to use automatic conversion
for most of the types. This is however incidental and we expect to switch to manual conversion,
especially for larger types that are more likely to encounter breaking proto changes.

Please add manual conversion logic whenever there is a breaking proto change.

"""

import enum
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Iterable, Mapping, Optional

import dataclasses_json
from dataclasses_json import dataclass_json

from base.blob_names.python.blob_names import BlobName
from base.datasets.gcs_blob_cache import PathAndContent
from base.datasets.recency_info import ReplacementText
from base.diff_utils.apply_replacements_to_files import (
    FileReplacementError,
    apply_replacements_to_files,
)
from base.diff_utils.diff_utils import File
from base.diff_utils.edit_events import GranularEditEvent
from base.logging.secret_logging import get_safe_logger
from base.ranges import CharRange

logger = logging.getLogger(__name__)


@dataclass_json
@dataclass(frozen=True)
class RetrievalChunk(dataclasses_json.DataClassJsonMixin):
    """Represents a single retrieved chunk."""

    text: str
    """The text of the chunk."""

    path: str
    """The path of the file the chunk comes from."""

    char_offset: int
    """The start of the region in the file."""

    char_end: int
    """The end of the region in the file."""

    blob_name: str
    """Where the prefix region of the request starts in blob_name."""

    chunk_index: int
    """The chunk index in the blob name."""

    origin: str
    """The name of the retriever that originated this chunk."""

    score: float
    """The score of the chunk."""

    @property
    def crange(self) -> CharRange:
        """Returns the character range."""
        return CharRange(self.char_offset, self.char_end)


@dataclass_json
@dataclass(frozen=True)
class FileLocation(dataclasses_json.DataClassJsonMixin):
    """Represents a location in a file."""

    path: str
    """The path of the file."""

    line_start: int
    """The line start of the edit (inclusive). Based on last known state of the file."""

    line_end: int
    """The line end of the edit (exclusive). Based on last known state of the file."""


@dataclass_json
@dataclass(frozen=True)
class Diagnostic(dataclasses_json.DataClassJsonMixin):
    """Represents a diagnostic message."""

    class DiagnosticSeverity(enum.Enum):
        """The severity of the diagnostic."""

        ERROR = 0
        WARNING = 1
        INFORMATION = 2
        HINT = 3

    location: FileLocation
    """The range of the diagnostic."""

    message: str
    """The diagnostic message."""

    severity: DiagnosticSeverity
    """The severity of the diagnostic."""


@dataclass_json
@dataclass(frozen=True)
class FileRegion(dataclasses_json.DataClassJsonMixin):
    """Represents a region in a file."""

    path: str
    """The path of the file."""

    char_start: int
    """The start of the region in the file."""

    char_end: int
    """The end of the region in the file."""


@dataclass_json
@dataclass(frozen=True)
class WorkingDirectoryChange(dataclasses_json.DataClassJsonMixin):
    """Represents a change in the working directory."""

    class ChangeType(enum.Enum):
        """The type of change."""

        ADDED = 0
        DELETED = 1
        MODIFIED = 2
        RENAMED = 3

    before_path: str
    """The previous path in HEAD."""

    after_path: str
    """The current path."""

    change_type: ChangeType
    """The type of change."""

    head_blob_name: str
    """The head blob name of the file."""

    indexed_blob_name: str
    """The indexed blob name of the file."""

    current_blob_name: str
    """The blob name of the file after the edit."""


@dataclass_json
@dataclass(frozen=True)
class VCSChange(dataclasses_json.DataClassJsonMixin):
    """Represents a VCS change."""

    working_directory_changes: list[WorkingDirectoryChange]


@dataclass_json
@dataclass
class NextEditRequest(dataclasses_json.DataClassJsonMixin):
    class NextEditMode(enum.Enum):
        UNKNOWN_NEXT_EDIT_MODE = 0
        BACKGROUND = 1
        FOREGROUND = 2
        FORCED = 3

    class NextEditScope(enum.Enum):
        UNKNOWN_NEXT_EDIT_SCOPE = 0
        CURSOR = 1
        FILE = 2
        WORKSPACE = 3

    """Represents a single next edit request."""

    model_name: str
    """The name of the model used for the next edit request."""

    sequence_id: int
    """Sequence IDs should be strictly increasing within a session."""

    lang: str
    """Programming language of the current file."""

    instruction: str
    """The message to be used for the prompt."""

    recent_changes: list[ReplacementText]
    """Recent events from the client."""

    vcs_change: VCSChange
    """The changes made to the files since the last commit"""

    path: str
    """Relative file path to a given relevant root (e.g. the .augmentroot file)."""

    blob_name: str
    """Blob name of the blob in which the edit is requested, if known."""

    selection_begin_char: int
    """Character offset within the blob of the start of the selected text, if known"""

    selection_end_char: int
    """Character offset within the blob of the end of the selected text, if known"""

    prefix: str
    """The prefix prompt at which the edit should be inserted."""

    selected_text: str
    """The text selected in the current buffer."""

    suffix: str
    """The text after the selected text."""

    diagnostics: list[Diagnostic]
    """Diagnostics. Unknown if still used."""

    mode: NextEditMode
    """The mode of the next edit request."""

    scope: NextEditScope
    """The scope of the next edit request."""

    change_probability_override: Optional[float]
    """The change probability override of the next edit request."""

    edit_events: list[GranularEditEvent]
    """The edit events of the next edit request."""

    blocked_locations: list[FileRegion]
    """A list of locations that the system should not return in the result."""

    blob_names: list[str]
    """The names of the blobs used in context."""

    restrict_to_file: bool
    """If true, restrict the result to the file containing the selection."""

    timestamp: datetime
    """The timestamp of the next edit request.

    This is set to the client time if available, otherwise to the backend time.
    """

    request_id: str
    """The request ID of the next edit request."""

    api_version: int = 0
    """The API version used by the client."""


@dataclass_json
@dataclass(frozen=True)
class Tokenization(dataclasses_json.DataClassJsonMixin):
    token_ids: list[int]
    """The token IDs of the tokenization."""

    offsets: list[int]
    """The tokenizer's token offsets."""

    log_probs: list[float]
    """The log probabilities of the tokenization."""

    text: str
    """The text of the tokenization."""


@dataclass_json
@dataclass(frozen=True)
class NextEditGeneration(dataclasses_json.DataClassJsonMixin):
    class PostProcessResult(enum.Enum):
        """The post-processing result of the next edit generation."""

        NOOP = 0
        LOW_PROB_CHANGED = 1
        UNDO_RECENT_CHANGES = 2

    """Represents a single next edit generation."""
    generation_id: str
    """The generation ID of the next edit generation."""

    retrieved_chunks: list[RetrievalChunk]
    """The retrieved chunks of the next edit generation."""

    generation_prompt: Tokenization
    """The generation prompt of the next edit generation."""

    generation_output: Tokenization
    """The generation output of the next edit generation."""

    location_chunk: RetrievalChunk
    """The location chunk of the next edit generation."""

    editing_score: float
    """The editing score of the next edit generation."""

    post_process_result: PostProcessResult = PostProcessResult.NOOP
    """The post-processing result of the next edit generation."""


@dataclass_json
@dataclass(frozen=True)
class DiffSpan(dataclasses_json.DataClassJsonMixin):
    """A diff span."""

    original: CharRange
    """The original span."""

    updated: CharRange
    """The updated span."""


@dataclass_json
@dataclass(frozen=True)
class NextEditGroundTruth(dataclasses_json.DataClassJsonMixin):
    """A simplified representation of a contiguous diff hunk change. Internally used by Hindsight Next Edit to represent ground truth."""

    path: str
    """The path of the next edit response."""

    diff_span: DiffSpan
    """The diff span of the response."""

    crange: CharRange
    """The final character range of the response."""

    old_text: str
    """The old text of the response."""

    new_text: str
    """The new text of the response."""


@dataclass_json
@dataclass(frozen=True)
class ScoredFileHunk(dataclasses_json.DataClassJsonMixin):
    """Stores edit information."""

    path: str
    """Path of the file."""

    blob_name: str
    """Blob name of the file."""

    char_start: int
    """Character start of the edit (inclusive). Based on last known state of the file."""

    char_end: int
    """Character end of the edit (exclusive). Based on last known state of the file."""

    existing_code: str
    """Existing code."""

    suggested_code: str
    """Updated code."""

    localization_score: float
    """Localization score."""

    editing_score: float
    """Editing score."""

    diff_spans: list[DiffSpan]
    """Character-level diff information."""

    truncation_char: int | None = None
    """The character offset in `replacement` where the change was truncated.

    None means that the change was not truncated.
    """

    change_description: str = ""
    """If non-empty, a natural language description of the change in suggested code."""

    suggestion_id: str = ""
    """If non-empty, a unique identifier for the suggestion."""

    editing_score_threshold: float = 1.0
    """Default threshold for `editing_score`.

    If `editing_score` > `editing_score_threshold`, the suggestion is considered of low
    quality.
    """


@dataclass_json
@dataclass(frozen=True)
class NextEditResult(dataclasses_json.DataClassJsonMixin):
    """Represents a single next edit response."""

    suggested_edit: ScoredFileHunk
    """The suggested edit of the next edit response."""

    unknown_blob_names: list[str]
    """The unknown blob names of the next edit response."""

    checkpoint_not_found: bool
    """The checkpoint not found of the next edit response."""


@dataclass_json
@dataclass(frozen=True)
class NextEditSuggestion(dataclasses_json.DataClassJsonMixin):
    """Represents a single next edit suggestion."""

    generation_id: str
    """The generation ID of the next edit suggestion."""

    description_prompt: Tokenization
    """The description prompt of the next edit suggestion."""

    description_output: Tokenization
    """The description output of the next edit suggestion."""

    result: NextEditResult
    """The result of the next edit suggestion."""

    suggestion_order: int
    """The suggestion order of the next edit suggestion."""


@dataclass_json
@dataclass(frozen=True)
class NextEditResponse(dataclasses_json.DataClassJsonMixin):
    """Represents the data generated in response to a single next edit request.
    Constructed from request_insight_pb2.RINextEditResponse.

    Note: generation -> location is 1:1 relationship.
        Each generation contains the location chunk that was used to generate it.
        Some location chunks will thus be stored both as part of retrieved locations and
        as part of generations that they were behind.

    Note: generation -> suggestion is 1:N relationship.
        Each suggestion contains the ID of the generation it came from.
    """

    retrieved_locations: list[RetrievalChunk]
    """The retrieved location chunks."""

    generations: list[NextEditGeneration]
    """The generations for the locations."""

    suggestions: list[NextEditSuggestion]
    """The generated suggestions."""

    timestamp: datetime
    """The timestamp of the next edit response."""


@dataclass_json
@dataclass(frozen=True)
class NextEditResolution(dataclasses_json.DataClassJsonMixin):
    """Represents the resolution of a next edit request."""

    accepted: bool
    """Whether the next edit was accepted."""

    timestamp: datetime
    """The timestamp of the resolution event."""


@dataclass_json
@dataclass(frozen=True)
class NextEditFeedback(dataclasses_json.DataClassJsonMixin):
    """Represents feedback received for a next edit."""

    class Rating(enum.Enum):
        """The rating of the next edit."""

        UNSET = 0
        """The next edit is neutral or unset."""

        POSITIVE = 1
        """The next edit is positive."""

        NEGATIVE = 2
        """The next edit is negative."""

    rating: Rating
    """The rating provided."""

    note: str
    """Feedback note."""

    timestamp: datetime
    """The timestamp of the feedback event."""


@dataclass_json
@dataclass(frozen=True)
class NextEditDatum(dataclasses_json.DataClassJsonMixin):
    """Represents a full next edit event with its request, response and resolution.

    The response contains all location chunks, generations, and suggestions generated for the request."""

    request_id: str
    """The request ID of the next edit event."""

    user_id: str
    """The user ID of the user who requested the next edit."""

    request: NextEditRequest
    """The request that was completed."""

    response: Optional[NextEditResponse] = None
    """The next edit response."""

    resolution: Optional[NextEditResolution] = None
    """How the next edit was resolved."""

    feedback: Optional[NextEditFeedback] = None
    """Any feedback provided for the next edit."""

    user_agent: str | None = None
    """The user agent of the user who requested the next edit."""


# FIXME(jiayi): Why do we need this function? Just use `apply_replacements_to_files`.
def get_current_version_of_files(
    replacements: list[ReplacementText],
    blob_name_to_path_and_content: Mapping[BlobName, PathAndContent],
    skip_validation: bool = False,
) -> dict[BlobName, File | FileReplacementError]:
    """
    Applies replacement text to the original blobs to get the current version of the files.
    Note that only blobs that are updated (e.g. in `replacements` and not marked as present)
    are in the output. Only replacements not yet applied should be passed into this function.

    `replacements`: all ReplacementText objects to apply to blobs
    `blob_name_to_path_and_content`: the mapping of blob names to their original path and content
    `skip_validation`: if True, do not validate blob names
    """
    # only apply replacements that are not present in the blob
    replacements = [r for r in replacements if not r.present_in_blob]

    for r in replacements:
        if r.present_in_blob:
            logger.warn(f"Replacement is already present in blob {r.blob_name}: {r}")

    blob_names_to_update: set[BlobName] = {
        replacement.blob_name for replacement in replacements
    }
    path_and_content = [
        blob_name_to_path_and_content.get(blob_name)
        for blob_name in blob_names_to_update
    ]
    path_and_content = [pc for pc in path_and_content if pc is not None]
    if len(path_and_content) != len(blob_names_to_update):
        logger.warn(
            f"Missing {len(blob_names_to_update) - len(path_and_content)} blobs for reconstruction."
        )
    files_before_replacements = [
        File(path=str(path), contents=content) for path, content in path_and_content
    ]
    # apply the changes to get the after state of the repo
    files_after_replacements_or_errors: Iterable[File | FileReplacementError] = (
        apply_replacements_to_files(
            replacements=replacements,
            files_before_replacements=files_before_replacements,
            safe_logger=get_safe_logger(logger, False),
            skip_error_checks=skip_validation,
        )
    )
    # ignore errors for now
    blob_name_to_file_after_replacements: dict[
        BlobName, File | FileReplacementError
    ] = {}
    for file in files_after_replacements_or_errors:
        if isinstance(file, FileReplacementError):
            blob_name_to_file_after_replacements[file.file_blob_name] = file
        elif isinstance(file, File):
            blob_name_to_file_after_replacements[file.blob_name] = file

    return blob_name_to_file_after_replacements

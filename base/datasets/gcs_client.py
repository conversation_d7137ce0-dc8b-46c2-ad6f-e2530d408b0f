"""CLI Utility to fetch request insight events from GCS.

Bazel Usage:
bazel run base/datasets:gcs_client -- --tenant-name <TENANT_NAME> get --request-id <REQUEST_ID>

CLI Usage:
python gcs_client.py --tenant-name <TENANT_NAME> [--output <OUTPUT_FILE>] get --request-ids <REQUEST_ID1> <REQUEST_ID2> <REQUEST_ID3>
python gcs_client.py --tenant-name <TENANT_NAME> [--output <OUTPUT_FILE>] get_from_file --request-ids-file <REQUEST_IDS_FILE> [--max-requests <MAX_REQUESTS>]

non-CLI usage:
result = GCSRequestInsightFetcher.from_tenant_name("dogfood-shard").get_requests(request_id="request123")

Performance is roughly ~22 requests/s or 5 MB/s.
"""

import argparse
import json
import logging
import os
import sys
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, Executor
from threading import Lock
from dataclasses import dataclass
from functools import lru_cache
from collections.abc import Callable, Iterable, Iterator
from typing import TypeVar

from google.auth.transport import requests
from requests import Session
from google.cloud import storage
from google.protobuf.json_format import MessageToDict
from requests.adapters import HTTPAdapter

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DatasetTenant, get_tenant
from services.request_insight import request_insight_pb2
from base.datasets.itertools import groupby

logger = logging.getLogger(__name__)


@dataclass
class Request:
    request_id: str
    events: list[request_insight_pb2.RequestEvent]

    def to_dict(self):
        return {
            "request_id": self.request_id,
            "events": [
                MessageToDict(event, preserving_proto_field_name=True)
                for event in self.events
            ],
        }


# NOTE(jeff): Python doesn't have anything like decltype, so we use type hint
# the keys as str | None, instead of decltype(e.WhichOneof("event")),
# which is actually Literal | None, and would prevent typos in the key.
def group_by_event_name(
    events: list[request_insight_pb2.RequestEvent],
) -> dict[str | None, list[request_insight_pb2.RequestEvent]]:
    """Group events by event name (the oneof variable name in the proto)."""
    return groupby(events, key=lambda e: e.WhichOneof("event"))


def max_by_time(
    events: list[request_insight_pb2.RequestEvent] | None,
) -> request_insight_pb2.RequestEvent | None:
    """Get the event with the latest time.

    For convenience, returns None if events is None or empty.
    """
    if events is None:
        return None
    return max(events, default=None, key=lambda e: e.time.ToNanoseconds())


WindowMapInput = TypeVar("WindowMapInput")
WindowMapOutput = TypeVar("WindowMapOutput")


def window_map(
    executor: Executor,
    fn: Callable[[WindowMapInput], WindowMapOutput],
    iterable: Iterable[WindowMapInput],
    window_size: int,
) -> Iterator[WindowMapOutput]:
    """Map a function over iterable calls only allowed `window_size` past the last
    consumed result. This can limit the memory usage if consumed results are freed.
    Note that this is subtly different from `window_size` concurrent calls, which
    can proceed arbitrarily past the last consumed result.

    Currently, the following hold:
    - It *should* be fine if fn raises, but this is not yet thoroughly tested.
    - It *should* be fine (no deadlock) if other callers are also using the executor.
    - However, holding this iterator open without iterating will block other callers.
    - If window_size < num_workers, then this function may schedule a bunch of idle
      tasks that are waiting on a lock that enforces the window property.
    """

    # We use window_locks to gate each item. Consuming the ith item will release
    # the i + window_size lock.
    # If tasks are scheduled in FIFO order, then if items < i have been consumed,
    # then (a) lock i will be unlocked and (b) task i will be scheduled assuming no
    # other executor is blocking the thread pool. So we always make forward progress.
    enumerated = list(enumerate(iterable))
    window_locks = [Lock() for _ in range(len(enumerated))]
    for lock in window_locks[window_size:]:
        lock.acquire()

    def wrapped_fn(enumerated_item: tuple[int, WindowMapInput]):
        i, item = enumerated_item
        window_locks[i].acquire()
        return i, fn(item)

    # NOTE: We assume that executor.map will schedule tasks in FIFO order.
    # This is not stated explicitly in the docs, but the implementation conforms
    # to this, and it simplifies the code.
    # TODO(jeff): This does come with the limitation that tasks can be scheduled
    # but gated by the lock, tying up threads unnecessarily.
    # Fixing this is likely somewhat complicated and requires basically reimplementing
    # executor.map, but only enqueueing the i + window_size task if i is consumed.
    try:
        for i, item in executor.map(wrapped_fn, enumerated):
            if i + window_size < len(window_locks):
                window_locks[i + window_size].release()
            yield item
    finally:
        # Clean up any locks that were not released. Note that this will unblock the
        # scheduled wrapped fns and allow them to complete (even if expensive).
        # TODO(jeff): check for shutdown.
        for lock in window_locks:
            if lock.locked():
                lock.release()


@dataclass(frozen=True)
class GCSRequestInsightFetcher:
    """A class to fetch request insight events from GCS for a single tenant.

    The class can be constructed via `from_*` classmethods.

    Usage:
    ```python
    fetcher = GCSRequestInsightFetcher.from_tenant_name("dogfood-shard")
    result = fetcher.get_requests(request_ids=["request123", "request456"])
    ```
    """

    bucket: storage.Bucket
    """The GCS bucket to use."""

    tenant_id: str
    """The tenant ID to use."""

    thread_pool: ThreadPoolExecutor
    """The thread pool to use for download blob calls."""

    @lru_cache()
    def get_request(
        self,
        request_id: str,
        request_event_names: frozenset[str] | None = None,
    ) -> Request:
        """Fetch request insight events from GCS for a single request."""

        prefix = f"{self.tenant_id}/request/{request_id}/"
        try:
            event_blobs = list(self.bucket.list_blobs(prefix=prefix))

            if not event_blobs:
                logger.warning("No events found for request ID: %s", request_id)
                return Request(request_id=request_id, events=[])

            # get the event names and intersect them with provided names
            if request_event_names:
                filtered_event_blobs = [
                    blob
                    for blob in event_blobs
                    if blob.name.split("/")[-2] in request_event_names
                ]
                if not filtered_event_blobs:
                    logger.warning(
                        "No events with names in %s for request ID: %s. Found event names are: %s",
                        request_event_names,
                        request_id,
                        [blob.name.split("/")[-2] for blob in event_blobs],
                    )
                    return Request(request_id=request_id, events=[])

                event_blobs = filtered_event_blobs

            events = list(self.thread_pool.map(self._process_blob, event_blobs))

        except Exception as e:
            logger.error(f"Error fetching events for ID {prefix}: {str(e)}")
            raise

        return Request(request_id=request_id, events=events)

    def _process_blob(self, blob: storage.Blob) -> request_insight_pb2.RequestEvent:
        try:
            request_event = request_insight_pb2.RequestEvent()
            request_event.ParseFromString(blob.download_as_bytes())
            return request_event
        except Exception as e:
            logger.error(f"Error processing blob {blob.name}: {str(e)}")
            raise

    def get_requests(
        self,
        request_ids: list[str],
        request_event_names: frozenset[str] | None = None,
        batch_size: int | None = None,
    ) -> Iterator[Request | Exception]:
        """Fetch request insight events from GCS for multiple requests."""
        window_size = batch_size or min(32, (os.cpu_count() or 1) + 4)

        def get_request_or_exception(request_id: str):
            try:
                return self.get_request(request_id, request_event_names)
            except Exception as e:
                return e

        logger.info(f"Fetching {len(request_ids)} requests")
        with ThreadPoolExecutor(max_workers=window_size) as executor:
            for item in window_map(
                executor, get_request_or_exception, request_ids, window_size
            ):
                yield item

    @classmethod
    def from_tenant_id(
        cls,
        project: str,
        bucket_name: str,
        tenant_id: str,
        max_pool_connections: int = 100,
    ):
        """Create a GCSRequestInsightFetcher, assembles the client."""
        credentials, _ = get_gcp_creds()
        adapter = HTTPAdapter(
            pool_connections=max_pool_connections, pool_maxsize=max_pool_connections
        )
        auth_request_sesssion = Session()
        auth_request_sesssion.mount("https://", adapter)
        auth_request = requests.Request(auth_request_sesssion)
        session = requests.AuthorizedSession(credentials, auth_request=auth_request)
        session.mount("https://", adapter)

        client = storage.Client(credentials=credentials, project=project, _http=session)
        bucket = client.bucket(bucket_name)
        thread_pool = ThreadPoolExecutor(max_pool_connections)
        return cls(bucket=bucket, tenant_id=tenant_id, thread_pool=thread_pool)

    @classmethod
    def from_tenant(cls, tenant: DatasetTenant, max_pool_connections: int = 100):
        """Create a GCSRequestInsightFetcher from a tenant."""
        return cls.from_tenant_id(
            project=tenant.project_id,
            bucket_name=tenant.events_bucket_name,
            tenant_id=tenant.tenant_id,
            max_pool_connections=max_pool_connections,
        )

    @classmethod
    def from_tenant_name(cls, tenant_name: str, max_pool_connections: int = 100):
        """Create a GCSRequestInsightFetcher from a tenant name."""
        return cls.from_tenant(get_tenant(tenant_name), max_pool_connections)


def read_request_ids(file_path: str) -> list[str]:
    """Read request IDs from a file."""
    with open(file_path) as f:
        return [line.strip() for line in f]


def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser(
        description="Fetch request insight events from GCS"
    )
    parser.add_argument("--tenant-name", type=str, help="Tenant name")
    parser.add_argument("--output", type=str, help="Output file path as a jsonl file")
    parser.add_argument(
        "--max-pool-connections", type=int, default=100, help="Max pool connections"
    )

    parser.add_argument(
        "--max-requests", type=int, default=None, help="Max requests to fetch"
    )
    parser.add_argument(
        "--request-event-names",
        type=str,
        default=[],
        required=False,
        nargs="*",
        help="List of request event names to filter by",
    )
    parser.add_argument(
        "--batch-size", type=int, default=None, help="Batch size for processing"
    )

    subparsers = parser.add_subparsers(dest="command", required=True)

    get_requests_parser = subparsers.add_parser("get")
    get_requests_parser.add_argument(
        "--request-ids", required=True, nargs="+", type=str, help="Request IDs"
    )
    get_requests_from_file_parser = subparsers.add_parser("get_from_file")
    get_requests_from_file_parser.add_argument(
        "--request-ids-file",
        required=True,
        type=str,
        help="File containing Request IDs",
    )

    args = parser.parse_args()

    try:
        result = None
        if args.command == "get":
            request_ids = args.request_ids
        elif args.command == "get_from_file":
            request_ids = read_request_ids(args.request_ids_file)
        else:
            raise ValueError(f"Invalid command: {args.command}")

        if args.command in ["get", "get_from_file"]:
            if args.max_requests:
                request_ids = request_ids[: args.max_requests]

            request_event_names = frozenset(args.request_event_names)
            result = GCSRequestInsightFetcher.from_tenant_name(
                args.tenant_name, max_pool_connections=args.max_pool_connections
            ).get_requests(
                request_ids,
                batch_size=args.batch_size,
                request_event_names=request_event_names,
            )

        if result:
            if args.output:
                with open(args.output, "w") as f:
                    for request in result:
                        if isinstance(request, Exception):
                            logger.error(f"Error: {request}")
                            continue
                        f.write(json.dumps(request.to_dict()) + "\n")
            else:
                for request in result:
                    if isinstance(request, Exception):
                        logger.error(f"Error: {request}")
                        continue
                    print(json.dumps(request.to_dict(), indent=2))

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

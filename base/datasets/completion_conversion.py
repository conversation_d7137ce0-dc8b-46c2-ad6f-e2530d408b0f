"""Contains conversion functions from proto to datasets classes for completions."""

from collections.abc import Sequence
from datetime import datetime

from base.datasets.completion import (
    CompletionFeedback,
    CompletionPosition,
    CompletionRequest,
    CompletionResolution,
    CompletionResponse,
    GranularEditEvent,
    InferenceResponse,
    RetrievedChunk,
    SingleEdit,
)
from base.datasets.recency_info_conversion import from_recency_info_proto
from base.diff_utils import edit_events_pb2
from base.ranges import Char<PERSON>ange
from services.request_insight import request_insight_pb2


def from_tokenization_proto(
    tokenization: request_insight_pb2.Tokenization,
) -> list[str]:
    """Helper function to get tokens from a tokenization proto."""
    offsets = [i for i in tokenization.offsets] + [len(tokenization.text)]
    return [
        tokenization.text[offsets[i] : offsets[i + 1]]
        for i in range(len(tokenization.offsets))
    ]


def from_completion_feedback_proto(
    feedback_proto: request_insight_pb2.CompletionFeedback,
    timestamp: datetime,
) -> CompletionFeedback:
    return CompletionFeedback(
        rating=CompletionFeedback.Rating(feedback_proto.rating),
        note=feedback_proto.note,
        timestamp=timestamp,
    )


def from_retrieval_response_protos(
    retrieval_protos: Sequence[request_insight_pb2.RetrievalResponse],
) -> list[RetrievedChunk]:
    return [
        RetrievedChunk(
            text=chunk.text,
            origin=chunk.origin,
            blob_name=chunk.blob_name,
            path=chunk.path,
            crange=CharRange(
                chunk.char_offset,
                chunk.char_end,
            ),
        )
        for retrieval in retrieval_protos
        for chunk in retrieval.retrieved_chunks
        if chunk.char_offset <= chunk.char_end
    ]


def from_completion_response_proto(
    response_proto: request_insight_pb2.CompletionHostResponse,
    retrieval_protos: Sequence[request_insight_pb2.RetrievalResponse],
    request_proto: request_insight_pb2.CompletionHostRequest,
    timestamp: datetime,
) -> CompletionResponse:
    chunks = from_retrieval_response_protos(retrieval_protos)

    return CompletionResponse(
        text=response_proto.text,
        model=request_proto.model,
        skipped_suffix=response_proto.skipped_suffix,
        suffix_replacement_text=response_proto.suffix_replacement_text,
        unknown_blob_names=list(response_proto.unknown_blob_names),
        retrieved_chunks=chunks,
        tokens=from_tokenization_proto(response_proto.tokenization),
        token_log_probs=[i for i in response_proto.tokenization.log_probs],
        prompt_tokens=from_tokenization_proto(request_proto.tokenization),
        prompt_token_ids=list(request_proto.tokenization.token_ids),
        token_ids=list(response_proto.tokenization.token_ids),
        timestamp=timestamp,
    )


def from_inference_response_proto(
    response_proto: request_insight_pb2.InferenceHostResponse,
    timestamp: datetime,
) -> InferenceResponse:
    return InferenceResponse(
        timestamp=timestamp,
        tokens=from_tokenization_proto(response_proto.tokenization),
        token_log_probs=[i for i in response_proto.tokenization.log_probs],
        token_ids=list(response_proto.tokenization.token_ids),
    )


def from_granular_edit_event_proto(
    event: edit_events_pb2.GranularEditEvent,
) -> GranularEditEvent:
    return GranularEditEvent(
        path=event.path,
        before_blob_name=event.before_blob_name,
        after_blob_name=event.after_blob_name,
        edits=[
            SingleEdit(
                before_start=edit.before_start,
                after_start=edit.after_start,
                before_text=edit.before_text,
                after_text=edit.after_text,
            )
            for edit in event.edits
        ],
    )


def from_completion_position_proto(
    proto: request_insight_pb2.CompletionHostRequest,
) -> CompletionPosition:
    blob_name = proto.position.blob_name or None
    return CompletionPosition(
        blob_name=blob_name,
        prefix_begin=proto.position.prefix_begin,
        suffix_end=proto.position.suffix_end,
        cursor_position=proto.position.cursor_position,
        original_prefix_length=len(proto.prefix),
        original_suffix_length=len(proto.suffix),
    )


def from_completion_request_proto(
    proto: request_insight_pb2.CompletionHostRequest,
    timestamp: datetime,
    request_id: str,
) -> CompletionRequest:
    position = from_completion_position_proto(proto)
    if not position.guess_validity():
        position = None

    recency_info = from_recency_info_proto(proto, request_id)
    edit_events = [from_granular_edit_event_proto(event) for event in proto.edit_events]
    return CompletionRequest(
        prefix=proto.prefix,
        suffix=proto.suffix,
        path=proto.path,
        blob_names=list(proto.blob_names),
        output_len=proto.output_len,
        timestamp=timestamp,
        recency_info=recency_info,
        position=position,
        edit_events=edit_events,
    )


def from_completion_resolution_proto(
    proto: request_insight_pb2.CompletionResolution, timestamp: datetime
) -> CompletionResolution:
    return CompletionResolution(
        accepted=proto.accepted_idx >= 0,
        timestamp=timestamp,
    )

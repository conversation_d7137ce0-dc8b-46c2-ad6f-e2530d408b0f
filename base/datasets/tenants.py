"""Prepackaged dataset configurations for different tenants for use in CoreWeave.

For production use-cases, use the configurations in deploy/tenants/tenants.jsonnet.

"""

from dataclasses import dataclass


@dataclass(frozen=True)
class DatasetTenant:
    """A tenant of the dataset."""

    name: str
    """The name of the tenant to use."""

    project_id: str
    """The GCP project id of underlying resources."""

    tenant_id: str
    """The tenant id of the tenant to use."""

    events_bucket_name: str
    """The name of the GCS bucket that stores the request insight events."""

    search_dataset_name: str
    """The name of the BigQuery search dataset (part of the new database) to use."""

    analytics_dataset_name: str
    """The name of the BigQuery analytics dataset."""

    dataset_name: str
    """The name of the BigQuery dataset to use. [to be deprecated]"""

    blob_bucket_name: str
    """The name of the GCS bucket that stores the file blobs."""

    blob_bucket_prefix: str
    """A path prefix for all blobs in the GCS bucket."""

    checkpoint_bucket_name: str
    """The name of the GCS bucket that stores the checkpoint blobs."""

    checkpoint_bucket_prefix: str
    """A path prefix for all checkpoint blobs in the GCS bucket."""


DOGFOOD = DatasetTenant(
    name="dogfood",
    project_id="system-services-prod",
    tenant_id="238f8184952a2f5590f859b0f1f3a465",
    events_bucket_name="us-staging-request-insight-events-nonenterprise",
    search_dataset_name="us_staging_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_staging_request_insight_analytics_dataset",
    dataset_name="staging_request_insight_full_export_dataset",
    blob_bucket_name="us-staging-blobs-nonenterprise",
    # Legacy dogfood blobs were backfilled with dogfood-shard's tenant id.
    blob_bucket_prefix="352a91ac7d4283558ccfbc094a527746/blobs",
    checkpoint_bucket_name="us-staging-blobs-nonenterprise",
    checkpoint_bucket_prefix="352a91ac7d4283558ccfbc094a527746/checkpoints",
)

DOGFOOD_SHARD = DatasetTenant(
    name="dogfood-shard",
    project_id="system-services-prod",
    tenant_id="352a91ac7d4283558ccfbc094a527746",
    events_bucket_name="us-staging-request-insight-events-nonenterprise",
    search_dataset_name="us_staging_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_staging_request_insight_analytics_dataset",
    dataset_name="staging_request_insight_full_export_dataset",
    blob_bucket_name="us-staging-blobs-nonenterprise",
    blob_bucket_prefix="352a91ac7d4283558ccfbc094a527746/blobs",
    checkpoint_bucket_name="us-staging-blobs-nonenterprise",
    checkpoint_bucket_prefix="352a91ac7d4283558ccfbc094a527746/checkpoints",
)

AITUTOR_PARETO = DatasetTenant(
    name="aitutor-pareto",
    project_id="system-services-prod",
    tenant_id="369110aa3f723c10f6822ebe8198530d",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="369110aa3f723c10f6822ebe8198530d/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="369110aa3f723c10f6822ebe8198530d/checkpoints",
)

AITUTOR_TURING = DatasetTenant(
    name="aitutor-turing",
    project_id="system-services-prod",
    tenant_id="82951ccf8d0daaae79bda4489968b8fa",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="82951ccf8d0daaae79bda4489968b8fa/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="82951ccf8d0daaae79bda4489968b8fa/checkpoints",
)

AITUTOR_MERCOR = DatasetTenant(
    name="aitutor-mercor",
    project_id="system-services-prod",
    tenant_id="d33d1ede5c91a1bc179f480caffb1470",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="d33d1ede5c91a1bc179f480caffb1470/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="d33d1ede5c91a1bc179f480caffb1470/checkpoints",
)

VANGUARD_I0_0 = DatasetTenant(
    name="i0-vanguard0",
    project_id="system-services-prod",
    tenant_id="789b1a18a6970fc4de4cf3aa89a35827",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="789b1a18a6970fc4de4cf3aa89a35827/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="789b1a18a6970fc4de4cf3aa89a35827/checkpoints",
)

VANGUARD_I0_1 = DatasetTenant(
    name="i0-vanguard1",
    project_id="system-services-prod",
    tenant_id="ce26583a2f0a0d8130e698bb1f651f27",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="ce26583a2f0a0d8130e698bb1f651f27/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="ce26583a2f0a0d8130e698bb1f651f27/checkpoints",
)

VANGUARD_I0_2 = DatasetTenant(
    name="i0-vanguard2",
    project_id="system-services-prod",
    tenant_id="74fee6f8b118d38476247c3206dc9c31",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="74fee6f8b118d38476247c3206dc9c31/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="74fee6f8b118d38476247c3206dc9c31/checkpoints",
)

VANGUARD_I0_3 = DatasetTenant(
    name="i0-vanguard3",
    project_id="system-services-prod",
    tenant_id="51fefac672e825104ea261d63418d070",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="51fefac672e825104ea261d63418d070/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="51fefac672e825104ea261d63418d070/checkpoints",
)

VANGUARD_I0_4 = DatasetTenant(
    name="i0-vanguard4",
    project_id="system-services-prod",
    tenant_id="e2dc5f8565810210d35d64826ba22120",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="e2dc5f8565810210d35d64826ba22120/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="e2dc5f8565810210d35d64826ba22120/checkpoints",
)

VANGUARD_I0_5 = DatasetTenant(
    name="i0-vanguard5",
    project_id="system-services-prod",
    tenant_id="a2a834210447eb987d1eff9b11384ed",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="a2a834210447eb987d1eff9b11384ed/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="a2a834210447eb987d1eff9b11384ed/checkpoints",
)

VANGUARD_I0_6 = DatasetTenant(
    name="i0-vanguard6",
    project_id="system-services-prod",
    tenant_id="b2e745e73d8d01ccda38498d1a4de5e7",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="b2e745e73d8d01ccda38498d1a4de5e7/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="b2e745e73d8d01ccda38498d1a4de5e7/checkpoints",
)

VANGUARD_I0_7 = DatasetTenant(
    name="i0-vanguard7",
    project_id="system-services-prod",
    tenant_id="a94be078d83a082a8b1b30ecc12dc173",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="a94be078d83a082a8b1b30ecc12dc173/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="a94be078d83a082a8b1b30ecc12dc173/checkpoints",
)

VANGUARD_I1_0 = DatasetTenant(
    name="i1-vanguard0",
    project_id="system-services-prod",
    tenant_id="eac8863de00e749b73eae23300f69e92",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="eac8863de00e749b73eae23300f69e92/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="eac8863de00e749b73eae23300f69e92/checkpoints",
)

VANGUARD_I1_1 = DatasetTenant(
    name="i1-vanguard1",
    project_id="system-services-prod",
    tenant_id="7521771141a76e2a9e686a3d89390fb5",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="7521771141a76e2a9e686a3d89390fb5/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="7521771141a76e2a9e686a3d89390fb5/checkpoints",
)

VANGUARD_I1_2 = DatasetTenant(
    name="i1-vanguard2",
    project_id="system-services-prod",
    tenant_id="ba2cda8f56ce724d5b2f32a1641471e1",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="ba2cda8f56ce724d5b2f32a1641471e1/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="ba2cda8f56ce724d5b2f32a1641471e1/checkpoints",
)

VANGUARD_I1_3 = DatasetTenant(
    name="i1-vanguard3",
    project_id="system-services-prod",
    tenant_id="49f0bdc475cce55ac6e802ad69ed19a5",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="49f0bdc475cce55ac6e802ad69ed19a5/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="49f0bdc475cce55ac6e802ad69ed19a5/checkpoints",
)

VANGUARD_I1_4 = DatasetTenant(
    name="i1-vanguard4",
    project_id="system-services-prod",
    tenant_id="a71d9d7889482b73100a45c8ce3953c6",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="a71d9d7889482b73100a45c8ce3953c6/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="a71d9d7889482b73100a45c8ce3953c6/checkpoints",
)

VANGUARD_I1_5 = DatasetTenant(
    name="i1-vanguard5",
    project_id="system-services-prod",
    tenant_id="2cf7cd507edd34c1c05a954544474084",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="2cf7cd507edd34c1c05a954544474084/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="2cf7cd507edd34c1c05a954544474084/checkpoints",
)

VANGUARD_I1_6 = DatasetTenant(
    name="i1-vanguard6",
    project_id="system-services-prod",
    tenant_id="4900799c06e1299aed1e2d0707417f15",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="4900799c06e1299aed1e2d0707417f15/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="4900799c06e1299aed1e2d0707417f15/checkpoints",
)

VANGUARD_I1_7 = DatasetTenant(
    name="i1-vanguard7",
    project_id="system-services-prod",
    tenant_id="c0041ab726a7096a56a21d7009419600",
    events_bucket_name="us-prod-request-insight-events-nonenterprise",
    search_dataset_name="us_prod_request_insight_search_nonenterprise_dataset",
    analytics_dataset_name="us_prod_request_insight_analytics_dataset",
    dataset_name="prod_request_insight_full_export_dataset",
    blob_bucket_name="us-prod-blobs-nonenterprise",
    blob_bucket_prefix="c0041ab726a7096a56a21d7009419600/blobs",
    checkpoint_bucket_name="us-prod-blobs-nonenterprise",
    checkpoint_bucket_prefix="c0041ab726a7096a56a21d7009419600/checkpoints",
)


# NOTE(arun): because all these tenants are statically defined in this file,
# we can use a dict to store them instead of a class.
DATASET_TENANTS = {
    tenant.name: tenant
    for tenant in [
        DOGFOOD,
        DOGFOOD_SHARD,
        AITUTOR_PARETO,
        AITUTOR_TURING,
        AITUTOR_MERCOR,
        VANGUARD_I0_0,
        VANGUARD_I0_1,
        VANGUARD_I0_2,
        VANGUARD_I0_3,
        VANGUARD_I0_4,
        VANGUARD_I0_5,
        VANGUARD_I0_6,
        VANGUARD_I0_7,
        VANGUARD_I1_0,
        VANGUARD_I1_1,
        VANGUARD_I1_2,
        VANGUARD_I1_3,
        VANGUARD_I1_4,
        VANGUARD_I1_5,
        VANGUARD_I1_6,
        VANGUARD_I1_7,
    ]
}


def get_tenant(name: str) -> DatasetTenant:
    """Get the tenant by name."""
    if name not in DATASET_TENANTS:
        raise ValueError(f"Unknown tenant: {name} from {list(DATASET_TENANTS.keys())}")
    return DATASET_TENANTS[name]

from __future__ import annotations

import logging
import re
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Iterable, cast, Optional, Sequence, Tuple, TypedDict, Union

from google.cloud import bigquery  # type: ignore
from google.protobuf.json_format import ParseDict

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DatasetTenant
from base.datasets.user_event import (
    CompletionRequestIdIssuedEvent,
    EditRequestIdIssuedEvent,
    NextEditRequestIdIssuedEvent,
    TextEditEvent,
    ContentChange,
)
from base.ranges import CharRange
from services.request_insight import request_insight_pb2

logger = logging.getLogger(__name__)

UserEventUnion = Union[
    CompletionRequestIdIssuedEvent,
    EditRequestIdIssuedEvent,
    NextEditRequestIdIssuedEvent,
    TextEditEvent,
]


@dataclass
class UserEventFilters:
    """Filters for the dataset."""

    timestamp_begin: Optional[datetime] = None
    """If set, only return events with a timestamp >= than this."""
    timestamp_end: Optional[datetime] = None
    """If set, only return events with a timestamp < than this."""
    user_ids: Optional[list[str]] = None
    """If set, only return events with a user ID in this list."""
    event_types: Optional[list[str]] = None
    """If set, only return events with an event type in this list."""
    session_id_begin: Optional[str] = None
    """If set, only return events with session IDs >= than this."""
    session_id_end: Optional[str] = None
    """If set, only return events with session IDs < than this."""


@dataclass
class UserEventStream:
    """A stream of user events."""

    events: Iterable[UserEventUnion]
    """The events in the stream."""
    num_events: Optional[int]
    """The number of events in the stream."""

    @classmethod
    def from_query(
        cls,
        tenant: DatasetTenant,
        filters: UserEventFilters,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
        service_account_file: Optional[Path] = None,
    ) -> UserEventStream:
        """Create a user event stream from a BigQuery query.

        Args:
            tenant: The tenant to use.
            filters: Filters to apply to the dataset.
            limit: If set, the maximum number of rows to return.
            page_size: The page size to use when querying BigQuery.
                Defaults to a sensible value set by the API
            service_account_file: The path to a service account file to use to get
                credentials.
        """
        query, job_config = _build_query(
            tenant.dataset_name, tenant.name, filters, limit
        )
        gcp_creds, _ = get_gcp_creds(service_account_file)
        bigquery_client = bigquery.Client(
            project=tenant.project_id, credentials=gcp_creds
        )

        logger.info("Querying BigQuery: %s", query)
        logger.info("Job config: %s", job_config)
        rows = bigquery_client.query_and_wait(
            query, job_config=job_config, page_size=page_size
        )
        events = (_row_to_user_event(row) for row in rows)
        return UserEventStream(
            events=events, num_events=cast(Optional[int], rows.total_rows)
        )


class _Row(TypedDict):
    """A typed dict representing a row in the user_event BigQuery table."""

    session_id: str
    user_id: str
    tenant: str
    time: datetime
    event_type: str
    file_path: str
    raw_json: str


def _convert_content_changes(
    content_changes: Sequence[request_insight_pb2.ContentChange],
) -> list[ContentChange]:
    """Convert a sequence of ContentChange protos to a list of ContentChange objects."""
    return [
        ContentChange(
            text=content_change.text,
            crange=CharRange(
                start=content_change.range.start,
                stop=content_change.range.end,
            ),
        )
        for content_change in content_changes
    ]


def _row_to_user_event(
    row: _Row,
) -> UserEventUnion:
    """Convert a row in the BigQuery table to one of the user events."""

    event_type = row["event_type"]

    if event_type == "completion_request_id_issued":
        proto = ParseDict(
            row["raw_json"],
            request_insight_pb2.CompletionRequestIdIssuedEvent(),
            ignore_unknown_fields=True,
        )
        return CompletionRequestIdIssuedEvent(
            session_id=row["session_id"],
            user_id=row["user_id"],
            tenant=row["tenant"],
            time=row["time"],
            file_path=row["file_path"],
            request_id=proto.request_id,
        )
    elif event_type == "edit_request_id_issued":
        proto = ParseDict(
            row["raw_json"],
            request_insight_pb2.EditRequestIdIssuedEvent(),
            ignore_unknown_fields=True,
        )
        return EditRequestIdIssuedEvent(
            session_id=row["session_id"],
            user_id=row["user_id"],
            tenant=row["tenant"],
            time=row["time"],
            file_path=row["file_path"],
            request_id=proto.request_id,
        )
    elif event_type == "next_edit_request_id_issued":
        proto = ParseDict(
            row["raw_json"],
            request_insight_pb2.NextEditRequestIdIssuedEvent(),
            ignore_unknown_fields=True,
        )
        return NextEditRequestIdIssuedEvent(
            session_id=row["session_id"],
            user_id=row["user_id"],
            tenant=row["tenant"],
            time=row["time"],
            file_path=row["file_path"],
            request_id=proto.request_id,
        )
    elif event_type == "text_edit":
        proto = ParseDict(
            row["raw_json"],
            request_insight_pb2.TextEditEvent(),
            ignore_unknown_fields=True,
        )
        return TextEditEvent(
            session_id=row["session_id"],
            user_id=row["user_id"],
            tenant=row["tenant"],
            time=row["time"],
            file_path=row["file_path"],
            content_changes=_convert_content_changes(proto.content_changes),
            after_changes_hash=proto.after_changes_hash
            if proto.HasField("after_changes_hash")
            else None,
            hash_char_ranges=[
                CharRange(start=crange.start, stop=crange.end)
                for crange in proto.hash_char_ranges
            ],
        )
    else:
        raise ValueError(f"Unknown event type: {event_type}")


def _build_query(
    dataset_name: str,
    tenant_name: str,
    filters: UserEventFilters,
    limit: Optional[int] = None,
) -> Tuple[str, bigquery.QueryJobConfig]:
    """Build a BigQuery query."""

    # Some very light weight input validation to prevent SQL injections.
    if re.match(r"^[a-zA-Z0-9_\-]+$", dataset_name) is None:
        raise ValueError(f"Invalid dataset name: {dataset_name}")
    if re.match(r"^[a-zA-Z0-9_\-]+$", tenant_name) is None:
        raise ValueError(f"Invalid tenant name: {tenant_name}")
    query_parameters = []
    filter_exprs = []

    query_parameters.append(
        bigquery.ScalarQueryParameter("tenant", "STRING", tenant_name)
    )
    filter_exprs.append("tenant = @tenant")

    if filters.timestamp_begin:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "timestamp_begin", "TIMESTAMP", filters.timestamp_begin
            )
        )
        filter_exprs.append("time >= @timestamp_begin")

    if filters.timestamp_end:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "timestamp_end", "TIMESTAMP", filters.timestamp_end
            )
        )
        filter_exprs.append("time < @timestamp_end")

    if filters.user_ids:
        query_parameters.append(
            bigquery.ArrayQueryParameter("user_ids", "STRING", filters.user_ids)
        )
        filter_exprs.append("user_id IN UNNEST(@user_ids)")

    if filters.event_types:
        query_parameters.append(
            bigquery.ArrayQueryParameter("event_types", "STRING", filters.event_types)
        )
        filter_exprs.append("event_type IN UNNEST(@event_types)")

    if filters.session_id_begin:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "session_id_begin", "STRING", filters.session_id_begin
            )
        )
        filter_exprs.append("session_id >= @session_id_begin")

    if filters.session_id_end:
        query_parameters.append(
            bigquery.ScalarQueryParameter(
                "session_id_end", "STRING", filters.session_id_end
            )
        )
        filter_exprs.append("session_id < @session_id_end")

    if limit:
        query_parameters.append(bigquery.ScalarQueryParameter("limit", "INT64", limit))
        limit_expr = "LIMIT @limit"
    else:
        limit_expr = ""

    order_by_expr = "ORDER BY time"
    filter_expr = "\n".join(f"AND {expr}" for expr in filter_exprs)
    query = f"""
        SELECT
            session_id,
            user_id,
            tenant,
            time,
            event_type,
            file_path,
            raw_json
        FROM `{dataset_name}.user_event`
        WHERE raw_json is NOT NULL
        {filter_expr}
        {order_by_expr}
        {limit_expr}
    """  # nosec
    return query, bigquery.QueryJobConfig(query_parameters=query_parameters)

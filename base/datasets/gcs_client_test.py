"""Unit tests for the GCS client.

pytest services/request_insight/gcs_exporter/gcs_client_test.py
"""

import concurrent.futures
import json
import pytest
from unittest.mock import Mock, patch
from dataclasses import dataclass, field
from gcs_client import (
    GCSRequestInsightFetcher,
    group_by_event_name,
    max_by_time,
    Request,
    main,
)
from services.request_insight import request_insight_pb2
from google.cloud import storage
from google.protobuf import timestamp_pb2


def test_to_dict():
    """Test that the to_dict method works correctly."""
    request = Request(
        request_id="test-request",
        events=[
            request_insight_pb2.RequestEvent(
                time=timestamp_pb2.Timestamp(seconds=1),
                completion_host_request=request_insight_pb2.CompletionHostRequest(),
            ),
        ],
    )
    assert request.to_dict() == {
        "request_id": "test-request",
        "events": [
            {
                "time": "1970-01-01T00:00:01Z",
                "completion_host_request": {},
            }
        ],
    }


def test_group_by_event_name():
    """Test that the group_by_event_name function works correctly."""
    event_dict = {
        "completion_host_request": [
            request_insight_pb2.RequestEvent(
                time=timestamp_pb2.Timestamp(seconds=i),
                completion_host_request=request_insight_pb2.CompletionHostRequest(),
            )
            for i in range(3)
        ],
        "infer_request": [
            request_insight_pb2.RequestEvent(
                time=timestamp_pb2.Timestamp(seconds=i),
                infer_request=request_insight_pb2.InferRequest(),
            )
            for i in range(3)
        ],
    }
    events = [x for v in event_dict.values() for x in v]
    grouped_events = group_by_event_name(events)
    assert grouped_events == event_dict

    # interleave, requires entries to be the same length.
    events = [x for v in zip(*event_dict.values()) for x in v]
    grouped_events = group_by_event_name(events)
    assert grouped_events == event_dict


def test_max_by_time():
    """Test that the max_by_time function works correctly."""
    events = [
        request_insight_pb2.RequestEvent(time=timestamp_pb2.Timestamp(seconds=i))
        for i in range(3)
    ]
    assert max_by_time(events) == events[-1]


@dataclass
class RIBlobData:
    """A dataclass to represent a blob in the GCS bucket."""

    tenant_id: str = "test-tenant"
    request_id: str = "test-request"
    event_name: str = "test-event"
    event_id: str = "test-event-id"
    data: request_insight_pb2.RequestEvent = field(
        default_factory=request_insight_pb2.RequestEvent
    )
    throws_exception: bool = False

    def to_blob(self) -> storage.Blob:
        blob = Mock(spec=storage.Blob)
        blob.name = f"{self.tenant_id}/request/{self.request_id}/{self.event_name}/{self.event_id}"
        if self.throws_exception:
            blob.download_as_bytes.side_effect = Exception(
                f"Simulated exception for {self.request_id}"
            )
        else:
            blob.download_as_bytes.return_value = self.data.SerializeToString()
        return blob


@dataclass
class MockBucket:
    """A mock GCS bucket."""

    blobs: list[storage.Blob]

    def list_blobs(self, prefix: str):
        return [
            blob
            for blob in self.blobs
            if blob.name is not None and blob.name.startswith(prefix)
        ]


def get_mock_gcs_fetcher(
    data: list[RIBlobData], tenant_id: str = "test-tenant", max_pool_connections=100
):
    blobs = [RIBlobData.to_blob(d) for d in data]
    thread_pool = concurrent.futures.ThreadPoolExecutor(max_pool_connections)
    mock_bucket = MockBucket(blobs)
    bucket = Mock(spec=storage.Bucket)
    bucket.list_blobs.side_effect = mock_bucket.list_blobs
    return GCSRequestInsightFetcher(
        bucket=bucket, tenant_id=tenant_id, thread_pool=thread_pool
    )


@pytest.mark.parametrize(
    "request_ids, throws, batch_size",
    [
        pytest.param(
            ["rid1", "rid2", "rid3", "rid4"], [0, 1, 0, 0], 4, id="one_failure"
        ),
        pytest.param(
            ["rid1", "rid2", "rid3", "rid4", "rid5", "rid6"],
            [0, 1, 1, 1, 0, 0],
            2,
            id="multi_batch_failure",
        ),
        pytest.param(["rid1", "rid2", "rid3"], [1, 1, 1], 4, id="all_failures"),
        pytest.param(
            [f"rid{i}" for i in range(200)],
            [1 for _ in range(200)],
            4,
            id="stressful_failures",
        ),
    ],
)
def test_get_requests_with_failures(
    request_ids: list[str], throws: list[int], batch_size: int
):
    """Test that get_requests catches exceptions and returns them as part of the result."""
    data = [
        RIBlobData(
            request_id=request_id,
            data=request_insight_pb2.RequestEvent(
                time=timestamp_pb2.Timestamp(seconds=i)
            ),
            throws_exception=bool(throws[i]),
        )
        for i, request_id in enumerate(request_ids)
    ]
    mock_gcs_fetcher = get_mock_gcs_fetcher(data)
    results = mock_gcs_fetcher.get_requests(request_ids, batch_size=batch_size)
    for i, result in enumerate(results):
        if throws[i]:
            assert isinstance(result, Exception)
            continue

        assert isinstance(result, Request)
        assert result.request_id == request_ids[i]
        assert len(result.events) == 1
        assert result.events[0].time.seconds == i


def test_get_requests_events():
    """
    Test that get_request_events correctly processes multiple blobs for a single request,
    creating a Request object with multiple events.
    """
    data = [
        RIBlobData(
            request_id="rid1",
            data=request_insight_pb2.RequestEvent(
                time=timestamp_pb2.Timestamp(seconds=i)
            ),
        )
        for i in [1, 2, 3]
    ]
    mock_gcs_fetcher = get_mock_gcs_fetcher(data)
    result = mock_gcs_fetcher.get_request("rid1")
    assert isinstance(result, Request)
    assert result.request_id == "rid1"
    assert len(result.events) == 3
    assert [event.time.seconds for event in result.events] == [1, 2, 3]


def test_event_name_filter():
    """Test that the event name filter works correctly."""
    data = [
        RIBlobData(
            request_id="rid1",
            event_name=f"event{i}",
            data=request_insight_pb2.RequestEvent(
                time=timestamp_pb2.Timestamp(seconds=i)
            ),
        )
        for i in [1, 2, 3]
    ]
    mock_gcs_fetcher = get_mock_gcs_fetcher(data)
    result = mock_gcs_fetcher.get_request(
        "rid1", request_event_names=frozenset(["event1", "event3", "event4"])
    )
    assert isinstance(result, Request)
    assert result.request_id == "rid1"
    assert len(result.events) == 2
    assert [event.time.seconds for event in result.events] == [1, 3]


@pytest.fixture
def main_data():
    return [
        RIBlobData(
            request_id=f"rid{rid}",
            data=request_insight_pb2.RequestEvent(
                time=timestamp_pb2.Timestamp(seconds=i)
            ),
        )
        for i in [1, 2, 3]
        for rid in [1, 2]
    ]


@pytest.fixture
def main_output():
    return [
        {
            "request_id": "rid1",
            "events": [
                {"time": "1970-01-01T00:00:01Z"},
                {"time": "1970-01-01T00:00:02Z"},
                {"time": "1970-01-01T00:00:03Z"},
            ],
        },
        {
            "request_id": "rid2",
            "events": [
                {"time": "1970-01-01T00:00:01Z"},
                {"time": "1970-01-01T00:00:02Z"},
                {"time": "1970-01-01T00:00:03Z"},
            ],
        },
    ]


@patch.object(GCSRequestInsightFetcher, "from_tenant_name")
def test_main(mock_from_tenant_name, tmp_path, main_data, main_output):
    tmp_output = tmp_path / "output.jsonl"
    argv = [
        "program",
        "--tenant-name",
        "test-tenant",
        "--output",
        str(tmp_output),
        "get",
        "--request-ids",
        "rid1",
        "rid2",
    ]
    with patch("sys.argv", argv):
        mock_from_tenant_name.return_value = get_mock_gcs_fetcher(main_data)
        main()

        output_text = tmp_output.read_text()
        output_dicts = [json.loads(line) for line in output_text.splitlines()]
        assert output_dicts == main_output


@patch.object(GCSRequestInsightFetcher, "from_tenant_name")
def test_main_with_file(
    mock_from_tenant_name,
    tmp_path,
    main_data,
    main_output,
):
    tmp_file = tmp_path / "request_ids.txt"
    with open(tmp_file, "w") as f:
        f.write("\n".join(["rid1", "rid2"]))

    tmp_output = tmp_path / "output.jsonl"
    argv = [
        "program",
        "--tenant-name",
        "test-tenant",
        "--output",
        str(tmp_output),
        "get_from_file",
        "--request-ids-file",
        str(tmp_file),
    ]
    with patch("sys.argv", argv):
        mock_from_tenant_name.return_value = get_mock_gcs_fetcher(main_data)
        main()

        output_text = tmp_output.read_text()
        output_dicts = [json.loads(line) for line in output_text.splitlines()]
        assert output_dicts == main_output

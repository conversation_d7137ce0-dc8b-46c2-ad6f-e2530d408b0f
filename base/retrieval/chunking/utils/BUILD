load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "html_to_md_conversion",
    srcs = ["html_to_md_conversion.py"],
    visibility = [
        "//base/retrieval/chunking:__subpackages__",
        "//models/retrieval/chunking:__subpackages__",
        "//research/data/collection/utils:__subpackages__",
    ],
    deps = [
        requirement("markdownify"),
    ],
)

py_library(
    name = "markdown_conversion",
    srcs = ["markdown_conversion.py"],
    visibility = [
        "//base/retrieval/chunking:__subpackages__",
        "//models/retrieval/chunking:__subpackages__",
        "//research/data/collection/utils:__subpackages__",
    ],
    deps = [
        requirement("html5lib"),
        requirement("beautifulsoup4"),
        requirement("markdownify"),
        ":html_to_md_conversion",
    ],
)

py_library(
    name = "jupyternb_conversion",
    srcs = ["jupyternb_conversion.py"],
    visibility = [
        "//base/retrieval/chunking:__subpackages__",
        "//models/retrieval/chunking:__subpackages__",
        "//research/data/collection/utils:__subpackages__",
    ],
    deps = [
        requirement("nbformat"),
        requirement("pandas"),
        "//base/retrieval/chunking/utils:markdown_conversion",
    ],
)

pytest_test(
    name = "markdown_conversion_test",
    srcs = ["markdown_conversion_test.py"],
    deps = [
        ":markdown_conversion",
    ],
)

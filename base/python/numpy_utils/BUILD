load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "numpy_utils",
    srcs = [
        "numpy_utils.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        requirement("numpy"),
    ],
)

pytest_test(
    name = "numpy_utils_test",
    srcs = ["numpy_utils_test.py"],
    deps = [
        ":numpy_utils",
    ],
)

load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//base:base.bzl", "BASE_VISIBILITY")

py_library(
    name = "signal_handler",
    srcs = ["signal_handler.py"],
    visibility = BASE_VISIBILITY,
)

# binary used by the signal_handler_test
py_binary(
    name = "signal_handler_test_binary",
    srcs = ["signal_handler_test_binary.py"],
    deps = [":signal_handler"],
)

pytest_test(
    name = "signal_handler_test",
    srcs = ["signal_handler_test.py"],
    data = [":signal_handler_test_binary"],
    deps = [":signal_handler"],
)

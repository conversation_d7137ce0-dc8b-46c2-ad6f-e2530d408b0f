load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "extract_completion_filter_features",
    srcs = [
        "extract_completion_filter_features.py",
    ],
    visibility = ["//services/completion_host/single_model_server:__subpackages__"],
    deps = [
        "//base/tokenizers",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "extract_completion_filter_features_test",
    srcs = ["extract_completion_filter_features_test.py"],
    deps = [
        ":extract_completion_filter_features",
        "//base/tokenizers",
    ],
)

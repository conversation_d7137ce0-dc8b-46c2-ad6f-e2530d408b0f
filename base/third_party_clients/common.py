from grpc import Rpc<PERSON>rror, StatusCode


class UnavailableRpcError(RpcError):
    def __init__(self, message: str):
        self.message = message
        pass

    def code(self):
        return StatusCode.UNAVAILABLE

    def details(self):
        return f"unavailable: {self.message}"


class ResourceExhaustedRpcError(RpcError):
    def __init__(
        self,
        message: str,
        retry_may_succeed: bool = True,
        retry_after_seconds: int | None = None,
    ):
        self.message = message
        self.retry_may_succeed = retry_may_succeed
        self.retry_after_seconds = retry_after_seconds
        pass

    def code(self):
        return StatusCode.RESOURCE_EXHAUSTED

    def details(self):
        return f"resource exhausted: {self.message}"


class InvalidArgumentRpcError(RpcError):
    """Exception for invalid arguments in the gRPC service."""

    def __init__(self, message: str):
        self.message = message

    def code(self):
        return StatusCode.INVALID_ARGUMENT

    def details(self):
        return f"invalid argument: {self.message}"

import json
import re
import time
from typing import Any, Generator
from datetime import datetime, timezone
from email.utils import parsedate_to_datetime

import httpx
import prometheus_client
import structlog
from anthropic import (
    NOT_GIVEN as Anthropic_NOT_GIVEN,
)
from anthropic import (
    Anthropic,
    APIConnectionError,
    APIStatusError,
    BadRequestError,
    InternalServerError,
    RateLimitError,
)
from anthropic.types import (
    TextBlock,
    TextBlockParam,
    ToolParam,
    ToolUseBlock,
)
from anthropic.types.message_create_params import (
    ToolChoiceToolChoiceAny,
    ToolChoiceToolChoiceAuto,
    ToolChoiceToolChoiceTool,
)
from typing_extensions import override

import base.feature_flags
from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNodeType,
    ToolResultContentNodeType,
    Exchange,
    ImageFormatType,
    RequestMessage,
    ResponseMessage,
    StopReason,
    try_get_request_message_as_text,
)
from base.third_party_clients.common import (
    InvalidArgumentRpcError,
    ResourceExhaustedRpcError,
    UnavailableRpcError,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    PromptCacheUsage,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolChoiceType,
    ToolDefinition,
    ToolUseResponse,
    ToolUseStart,
)

# Note that this metric can be inaccurate, because it depends on the client
# closing the generator stream as soon as it is done consuming results.
ANTHROPIC_RESPONSE_LATENCY = prometheus_client.Histogram(
    "au_anthropic_response_latency",
    "Latency of Anthropic API responses in seconds",
    ["model", "client_type", "provider_region"],
    buckets=[
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
        20.0,
        50.0,
        100.0,
        200.0,
        float("inf"),
    ],
)

ANTHROPIC_FIRST_TOKEN_LATENCY = prometheus_client.Histogram(
    "au_anthropic_first_token_latency",
    "Latency of first token of Anthropic API responses in seconds",
    ["model", "client_type", "provider_region"],
    buckets=[
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
        20.0,
        50.0,
        100.0,
        200.0,
        float("inf"),
    ],
)

# Prompt cache usage metrics
PROMPT_TOTAL_INPUT_TOKENS = prometheus_client.Histogram(
    "au_anthropic_prompt_total_input_tokens",
    labelnames=["model_caller", "model", "client_type", "provider_region"],
    documentation="Total tokens in the prompt input for a given request, including cache reads and writes",
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        60000,
        70000,
        80000,
        90000,
        100000,
        200000,
        500000,
        float("inf"),
    ],
)

PROMPT_INPUT_TOKENS = prometheus_client.Histogram(
    "au_anthropic_prompt_input_tokens",
    labelnames=["model_caller", "model", "client_type", "provider_region"],
    documentation="Tokens in the prompt input for a given request, not including cache reads and writes",
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        100000,
        200000,
        500000,
        float("inf"),
    ],
)

PROMPT_CACHE_READ = prometheus_client.Histogram(
    "au_anthropic_prompt_cache_read",
    labelnames=["model_caller", "model", "client_type", "provider_region"],
    documentation="Cache read input tokens for a given request",
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        100000,
        200000,
        500000,
        float("inf"),
    ],
)

PROMPT_CACHE_WRITE = prometheus_client.Histogram(
    "au_anthropic_prompt_cache_write",
    labelnames=["model_caller", "model", "client_type", "provider_region"],
    documentation="Cache write input tokens for a given request",
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        100000,
        200000,
        500000,
        float("inf"),
    ],
)

PROMPT_CACHE_HIT_RATIO = prometheus_client.Histogram(
    "au_anthropic_prompt_cache_hit_ratio",
    labelnames=["model_caller", "model", "client_type", "provider_region"],
    documentation="Percentage of input tokens found in cache",
    buckets=[x * 0.05 for x in range(21)],  # 0%, 5%, 10%, ..., 95%, 100%
)
FIRST_TOKEN_HIGH_LATENCY_THRESHOLD = 10  # seconds

MAX_RETRIES = 2  # Default is 2
# Default is httpx.Timeout(timeout=600.0, connect=5.0)
# We add a read timeout, but it's not very strict as it covers context processing and
# periods of time when model is generating tool input where nothing is streamed.
TIMEOUT = httpx.Timeout(timeout=280.0, connect=3.0, read=75.0)

_token_count_calls = prometheus_client.Counter(
    "au_anthropic_token_count_calls",
    "Number of token counting calls made to Anthropic Direct client",
    ["client_type"],
)

_output_text_chars_counter = prometheus_client.Counter(
    "au_anthropic_output_text_size",
    "Size of output text from Anthropic client",
    ["model_caller", "model", "client_type", "provider_region"],
)

_output_tokens_histogram = prometheus_client.Histogram(
    "au_anthropic_output_tokens",
    "Number of output tokens from Anthropic client",
    ["model_caller", "model", "client_type", "provider_region"],
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        100000,
        float("inf"),
    ],
)

_error_counter = prometheus_client.Counter(
    "au_anthropic_error_count",
    "Number of errors from Anthropic client",
    ["model", "client_type", "provider_region", "error_type"],
)

_bad_request_counter = prometheus_client.Counter(
    "au_anthropic_bad_request_types",
    "Number of bad request errors from Anthropic client",
    ["model_caller", "reason"],
)

_output_tool_use_blocks = prometheus_client.Counter(
    "au_anthropic_output_tool_use_blocks",
    "Number of tool use blocks from Anthropic client",
    ["model", "client_type"],
)

_event_type_counter = prometheus_client.Counter(
    "au_anthropic_event_type",
    "Number of events from Anthropic client",
    ["model", "client_type", "event_type"],
)

# Event type constants
# Unfortunately the event names are not defined as class attributes
# so we have to use string literals
EVENT_TYPE_MESSAGE_START = "message_start"
EVENT_TYPE_MESSAGE_DELTA = "message_delta"
EVENT_TYPE_MESSAGE_STOP = "message_stop"
EVENT_TYPE_CONTENT_BLOCK_START = "content_block_start"
EVENT_TYPE_CONTENT_BLOCK_DELTA = "content_block_delta"
EVENT_TYPE_CONTENT_BLOCK_STOP = "content_block_stop"
EVENT_TYPE_TEXT = "text"
EVENT_TYPE_INPUT_JSON = "input_json"

# Event types that are logged but otherwise ignored in processing
_IGNORED_EVENT_TYPES = {
    EVENT_TYPE_CONTENT_BLOCK_START,
    EVENT_TYPE_CONTENT_BLOCK_STOP,
    EVENT_TYPE_CONTENT_BLOCK_DELTA,
    EVENT_TYPE_MESSAGE_DELTA,
    EVENT_TYPE_MESSAGE_STOP,
    EVENT_TYPE_INPUT_JSON,
}


class AnthropicResponseParseError(Exception):
    def __init__(self, message: str):
        self.message = message


def get_tool_definition(tool_name: str) -> ToolParam:
    tool_name_to_params: dict[str, ToolParam] = {
        "replace_text": ToolParam(
            name="replace_text",
            description="Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.",  # noqa: E501
            input_schema={
                "type": "object",
                "properties": {
                    "old_text": {
                        "type": "string",
                        "description": "The original text.",
                    },
                    "start_line_number": {
                        "type": "integer",
                        "description": "The line number where the original text starts, inclusive.",
                    },
                    "end_line_number": {
                        "type": "integer",
                        "description": "The line number where the original text ends, inclusive.",
                    },
                    "replacement_text": {
                        "type": "string",
                        "description": "The new text.",
                    },
                },
                "required": [
                    "old_text",
                    "start_line_number",
                    "end_line_number",
                    "replacement_text",
                ],
            },
        ),
        "check_command_code_related": ToolParam(
            name="check_command_code_related",
            description="Check if the given command executed any code related checks.",
            input_schema={
                "type": "object",
                "properties": {
                    "result": {
                        "type": "boolean",
                        "description": "Boolean indicating whether the command executed any code related checks.",
                    },
                    "desc": {
                        "type": "string",
                        "description": "Short and concise one line explanation of the result.",
                    },
                },
                "required": ["result", "desc"],
            },
        ),
        "command_output_contain_errors": ToolParam(
            name="command_output_contain_errors",
            description="Report if the given command output contains errors.",
            input_schema={
                "type": "object",
                "properties": {
                    "result": {
                        "type": "boolean",
                        "description": "Boolean indicating whether the command output contains errors.",
                    },
                    "desc": {
                        "type": "string",
                        "description": "Short and concise one line explanation.",
                    },
                },
                "required": ["result", "desc"],
            },
        ),
        "glean_search": ToolParam(
            name="glean_search",
            description="Search in company's internal documentation including Notion, Slack, and Jira using Glean. Use this tool only when necessary to find relevant information not available in the codebase. This tool returns a list of search queries to retrieve relevant documents from Glean, complementing codebase searches. Use sparingly for additional context beyond the codebase.",
            input_schema={
                "type": "object",
                "properties": {
                    "queries": {
                        "type": "array",
                        "items": {
                            "type": "string",
                        },
                        "description": "List of 1-3 concise search queries.",
                        "minItems": 1,
                        "maxItems": 3,
                    },
                },
                "required": ["queries"],
            },
        ),
        "codebase-retrieval": ToolParam(
            name="codebase-retrieval",
            # This tool definition is deactivated but still needed, because we present the initial retrieval results like a tool call (in v13). Can be removed once we stop using v13.
            description="Use this tool to request information from the codebase. It will return relevant snippets for the requested information. This tool is not longer available to use. DO NOT CALL IT.",
            input_schema={
                "type": "object",
                "properties": {
                    "information_request": {
                        "type": "string",
                        "description": "A description of the information you need.",
                    },
                },
                "required": ["information_request"],
            },
        ),
        "dummy_tool": ToolParam(
            name="dummy_tool",
            description="A placeholder tool. DO NOT CALL IT.",
            input_schema={
                "type": "object",
                "properties": {
                    "input": {
                        "type": "string",
                        "description": "The input to the dummy tool.",
                    },
                },
                "required": ["input"],
            },
        ),
    }

    if tool_name not in tool_name_to_params:
        raise NotImplementedError(f"Tool {tool_name} not implemented")
    return tool_name_to_params[tool_name]


def get_image_media_type(format_type: ImageFormatType) -> str | None:
    """
    Convert an ImageFormatType to its corresponding media type string.

    Args:
        format_type: The ImageFormatType enum value

    Returns:
        The corresponding media type string (e.g., "image/png") or None if the format is not supported
    """
    media_types = {
        ImageFormatType.PNG: "image/png",
        ImageFormatType.JPEG: "image/jpeg",
        ImageFormatType.GIF: "image/gif",
        ImageFormatType.WEBP: "image/webp",
    }
    return media_types.get(format_type)


def loggable_tool_name(tool_name: str) -> str:
    if tool_name in [
        "save-file",
        "str-replace-editor",
        "remember",
        "launch-process",
        "write-process",
        "web-search",
        "notion-search",
        "notion-page",
    ]:
        return tool_name
    return ""


_bad_request_patterns = re.compile(
    r"|".join(
        [
            r"(?P<tool_use_without_result>`tool_use` ids were found without `tool_result` blocks immediately after)",
            r"(?P<tool_result_without_use>`tool_result` block must have a corresponding `tool_use` block in the previous message)",
            r"(?P<tool_use_ids_not_unique>`tool_use` ids must be unique)",
            # Could be caught in prompt formatter / api-proxy / client!
            r"(?P<tool_names_not_unique>Tool names must be unique)",
            # Should be prevented by prompt formatter
            r"(?P<prompt_too_long>input length and `max_tokens` exceed context limit|prompt is too long)",
            r"(?P<could_not_process_image>Could not process image|image cannot be empty|invalid base64 data)",
            # MCP only? Could be caught in client? Endpoint to test MCP tool names + definitions
            # before including them in requests?
            r"(?P<invalid_tool_name>tools\.[0-9]+\.custom.name: String should match pattern)",
            r"(?P<invalid_tool_schema>tools\.[0-9]+\.custom.input_schema: JSON schema is invalid)",
            r"(?P<tool_schema_top_level_condition>tools\.[0-9]+\.custom.input_schema:.*does not support oneOf, allOf, or anyOf at the top level)",
            r"(?P<empty_content>messages must have non-empty content|text content blocks must contain non-whitespace text)",
            r"(?P<tool_result_empty_error>tool_result: content cannot be empty if `is_error` is true)",
            r"(?P<publisher_model_permission>not allowed to use Publisher Model)",
        ]
    )
)


def bad_request_reason_from_exception(e: BadRequestError) -> str:
    """Extract the reason for a bad request error from the exception.
    These errors are too long and have variable fields so we can't use
    them directly as metric labels. Add here the error fingerprints we
    know about so we can track their occurence.

    Args:
        e: The exception to extract the reason from.

    Returns:
        The reason for the bad request error, or "unknown" if the error
        is not recognized.
    """
    if not isinstance(e.body, dict):
        # Could be string or None
        return "unknown"
    message = e.body.get("error", {}).get("message", "")
    match = _bad_request_patterns.search(message)
    if match is None:
        return "unknown"
    return next((k for (k, v) in match.groupdict().items() if v is not None), "unknown")


def get_rate_limit_headers(e: APIStatusError):
    try:
        headers = e.response.headers
        return dict(
            retry_after=headers.get("retry-after"),
            requests_remaining=headers.get("anthropic-ratelimit-requests-remaining"),
            requests_reset=headers.get("anthropic-ratelimit-requests-reset"),
            tokens_remaining=headers.get("anthropic-ratelimit-tokens-remaining"),
            tokens_reset=headers.get("anthropic-ratelimit-tokens-reset"),
            input_remaining=headers.get("anthropic-ratelimit-input-tokens-remaining"),
            input_reset=headers.get("anthropic-ratelimit-input-tokens-reset"),
            output_remaining=headers.get("anthropic-ratelimit-output-tokens-remaining"),
            output_reset=headers.get("anthropic-ratelimit-output-tokens-reset"),
        )
    except Exception:
        return {}


_SAVE_FILE_PARTIAL_TOOL_USE = base.feature_flags.BoolFlag(
    "agents_save_file_partial_tool_use", False
)


def should_return_partial_tool_use_block(tool_use_block: ToolUseBlock) -> bool:
    if not isinstance(tool_use_block.input, dict):
        return False

    # return partial tool use for str-replace-editor with str_replace command
    if (
        tool_use_block.name == "str-replace-editor"
        and tool_use_block.input.get("command") == "str_replace"
    ):
        input_params = tool_use_block.input.keys()
        # only return partial tool use for flat schema
        return (
            "path" in input_params
            and any([param.startswith("old_str_") for param in input_params])
            and any([param.startswith("new_str_") for param in input_params])
        )

    # TODO(jeff): Remove feature flag once flipped on.
    global_context = base.feature_flags.get_global_context()
    if _SAVE_FILE_PARTIAL_TOOL_USE.get(global_context):
        # return partial tool use for save-file command
        if tool_use_block.name == "save-file":
            input_params = tool_use_block.input.keys()
            # New clients know how to handle partial tool use with only `path`.
            return "path" in input_params

    return False


class AnthropicDirectClient(ThirdPartyModelClient):
    """
    A class to interact with Anthropic models, e.g. Claude Sonnet.
    """

    client_type = "anthropic_direct"

    def __init__(
        self,
        api_key,
        model_name,
        temperature,
        max_output_tokens,
    ):
        """
        Initialize the Anthropic API with key and model parameters.

        Args:
            project_id (str): The ID of the Google Cloud project.
            region (str): The region of the Google Cloud project.
            model_name (str): The name of the model to use for generating responses.
            temperature (float): The temperature parameter for controlling the randomness of the responses.
            max_output_tokens (int): The maximum number of tokens to generate in the response.
        """
        self.model_name = model_name
        self.provider_region = "direct_no_region"
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.client = Anthropic(
            api_key=api_key,
            max_retries=MAX_RETRIES,
            timeout=TIMEOUT,
            default_headers={"anthropic-beta": "service-tiers-2025-01-22"},
        )
        self.count_tokens_client = Anthropic()
        self.logger = structlog.get_logger().bind(
            client_type=self.client_type, anthropic_target=self.client_type
        )
        self.logger.info(
            "Anthropic Direct Client initialized",
            model_name=model_name,
        )
        # Anthropic Direct requires special headers for prompt caching
        self.prompt_caching_headers = {"anthropic-beta": "prompt-caching-2024-07-31"}

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Any | None = None,
        yield_final_parameters: bool = False,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """
        Generate a response based on the given message.

        Args:
            chat_history list[Exchange]: The messages to generate a response for in the format (user_m, assistant_m)
            where the last tuple only has the user_message.
        system_prompt (str): The system prompt to use for the model.
        cur_message (RequestMessage): The current message to generate a response for.
        tools (list[str]): List of tool names to use for the model.
        prefill (str | None): If provided, the model will start generating from this string.
        use_caching (bool): If True, we insert cache breakpoints on the last two assistant messages.
        model_caller (str): A field passed in by the caller (could be chat, agent, etc.) to keep track of the caller, for metrics purposes.
        request_context (Any | None): Optional request context containing information about the request, including session ID.

        Returns:
            Generator for the generated response text.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        self.logger.info("generating response for %s messages", len(messages))

        if chat_history is None:
            chat_history = [
                Exchange(request_message=message[0], response_text=message[1])
                for message in messages
            ]

        formatted_messages = []
        try:
            for message in chat_history:
                # Format first and then filter, as formatting may strip
                # out unwanted nodes
                request_content = self.format_request_message_content(
                    message.request_message, log_warnings=False
                )
                if request_content:
                    formatted_messages.append(
                        {
                            "role": "user",
                            "content": request_content,
                        }
                    )
                response_content = format_response_message_content(
                    message.response_text
                )
                if response_content:
                    formatted_messages.append(
                        {
                            "role": "assistant",
                            "content": response_content,
                        }
                    )
            # Add cur message
            formatted_messages.append(
                {
                    "role": "user",
                    "content": self.format_request_message_content(cur_message),
                }
            )
        except ValueError as e:
            # Convert ValueError into a proper gRPC error with INVALID_ARGUMENT status
            raise InvalidArgumentRpcError(str(e))

        if system_prompt and use_caching:
            system_prompt_wrapped = [
                TextBlockParam(
                    type="text",
                    text=system_prompt,
                    cache_control={"type": "ephemeral"},
                )
            ]
        else:
            system_prompt_wrapped = system_prompt or ""

        # Anthropic's caching API allows us to insert up to 4 "cache breakpoints"
        # We have already used one up for the system prompt, so let's use the remaining 3
        # https://docs.anthropic.com/en/docs/build-with-claude/prompt-caching#structuring-your-prompt
        if use_caching:
            remaining_breakpoints = 3

            # Tool results are often large so caching them helps cover most
            # of the tokens of current message across all requests (for
            # now). Some concerns:
            # - It's not 100% reliable that the current message ends up in the history
            # in the next request
            # - We may inject text nodes into the current message that won't be there
            # when it becomes part of history
            if set_cache_control_tool_result(formatted_messages[-1]):
                remaining_breakpoints -= 1

            length = len(formatted_messages)
            # set_cache_control_ephemeral is idempotent, so don't worry about
            # duplicate indexes here
            target_indexes = [
                max(0, length - 2),  # last message in history
                9 * length // 10,  # ~90% through history
                7 * length // 10,  # ~70% through history
            ]
            for idx in target_indexes:
                if remaining_breakpoints == 0:
                    break
                if idx == length - 1:
                    continue
                remaining_breakpoints -= 1
                set_cache_control_ephemeral(formatted_messages[idx])

        # Prefilling the response
        if prefill:
            self.logger.info(
                "Prefilling response with length %d characters",
                len(prefill),
                model_name=self.model_name,
            )
            formatted_messages.append(
                {
                    "role": "assistant",
                    "content": prefill,
                }
            )
        else:
            prefill = None

        if use_caching:
            extra_headers = self.prompt_caching_headers
        else:
            extra_headers = None

        # Turn tool_choice into Anthropic tool_choice format
        singular = tool_choice is not None and tool_choice.disable_parallel_tool_use
        if tool_choice is None:
            tool_choice_param = Anthropic_NOT_GIVEN
        elif tool_choice.type == ToolChoiceType.ANY:
            tool_choice_param = ToolChoiceToolChoiceAny(
                type="any", disable_parallel_tool_use=singular
            )
        elif tool_choice.type == ToolChoiceType.AUTO:
            tool_choice_param = ToolChoiceToolChoiceAuto(
                type="auto", disable_parallel_tool_use=singular
            )
        elif tool_choice.type == ToolChoiceType.TOOL:
            if tool_choice.name is None:
                raise ValueError("ToolChoice of type TOOL requires tool name")
            tool_choice_param = ToolChoiceToolChoiceTool(
                type="tool", name=tool_choice.name, disable_parallel_tool_use=singular
            )
        else:
            raise ValueError(f"Unknown tool_choice value: {tool_choice}")

        self.logger.info("tool_choice_param: %s", tool_choice_param)

        temp_param = temperature if temperature is not None else self.temperature
        tokens_param = (
            max_output_tokens
            if max_output_tokens is not None
            else self.max_output_tokens
        )

        if "replace_text" in tools:
            self.logger.error(
                "replace_text tool is requested, which was used to indicate xml tool use responses. "
                "This is no longer supported. Remove this error once we know this use is intended."
            )
        # Get all tool definitions
        all_tool_definitions: list[ToolParam] = [
            get_tool_definition(tool_name) for tool_name in tools
        ] + [
            ToolParam(
                name=tool_def.name,
                description=tool_def.description,
                input_schema=json.loads(tool_def.input_schema_json),
            )
            for tool_def in tool_definitions
        ]

        if not all_tool_definitions and formatted_messages_contain_tools(
            formatted_messages
        ):
            # Caller doesn't want to present any tools to the model for this exchange, but
            # anthropic API requires at least one tool definition if there are any tool uses/results.
            # We're lucky that they aren't stricter by requiring a definition for every tool
            # name mentioned in a tool use.
            self.logger.warn(
                "Tools present in conversation, but no tool definitions are provided. Injecting dummy tool."
            )
            all_tool_definitions = [get_tool_definition("dummy_tool")]

        start_time = time.time()
        with ANTHROPIC_RESPONSE_LATENCY.labels(
            model=self.model_name,
            client_type=self.client_type,
            provider_region=self.provider_region,
        ).time():
            try:
                first_message_time = None
                if yield_final_parameters:
                    yield ThirdPartyModelResponse(
                        text="",
                        final_parameters={
                            "model": self.model_name,
                            "max_tokens": tokens_param,
                            "temperature": temp_param,
                            "system_prompt": system_prompt,
                            "messages": formatted_messages,
                            "extra_headers": extra_headers,
                            "tools": all_tool_definitions,
                            "tool_choice": tool_choice_param,
                        },
                    )
                with self.client.messages.stream(
                    model=self.model_name,
                    max_tokens=tokens_param,
                    temperature=temp_param,
                    system=system_prompt_wrapped,
                    messages=formatted_messages,
                    extra_headers=extra_headers,
                    tools=all_tool_definitions,
                    tool_choice=tool_choice_param,
                ) as message_stream:
                    returned_tool_use_ids: set[str] = set()
                    streaming_tool_use_id: str = ""
                    streaming_tool_name: str = ""
                    tool_uses = 0
                    stored_prompt_cache_usage: PromptCacheUsage | None = None

                    def create_tool_use_response(
                        tool_use_block: ToolUseBlock, is_partial: bool = False
                    ):
                        _output_tool_use_blocks.labels(
                            model=self.model_name,
                            client_type=self.client_type,
                        ).inc()
                        if not isinstance(tool_use_block.input, dict):
                            raise AnthropicResponseParseError(
                                f"Unexpected tool input type: {type(tool_use_block.input)}"
                            )

                        returned_tool_use_ids.add(tool_use_block.id)

                        return ThirdPartyModelResponse(
                            text="",
                            tool_use=ToolUseResponse(
                                tool_name=tool_use_block.name,
                                input=tool_use_block.input,
                                tool_use_id=tool_use_block.id,
                                is_partial=is_partial,
                            ),
                        )

                    for event in message_stream:
                        if first_message_time is None:
                            first_message_time = time.time()
                            first_latency = first_message_time - start_time
                            ANTHROPIC_FIRST_TOKEN_LATENCY.labels(
                                model=self.model_name,
                                client_type=self.client_type,
                                provider_region=self.provider_region,
                            ).observe(first_latency)
                            if first_latency > FIRST_TOKEN_HIGH_LATENCY_THRESHOLD:
                                self.logger.warning(
                                    "First token took %.2f seconds to arrive",
                                    first_latency,
                                )

                        _event_type_counter.labels(
                            model=self.model_name,
                            client_type=self.client_type,
                            event_type=event.type,
                        ).inc()

                        if event.type == EVENT_TYPE_MESSAGE_START:
                            # log all of the attributes of usage:
                            #  This doesn't exist on the old API, which we are currently using. I do see it on 0.45.2.
                            cache_read_input_tokens = getattr(
                                event.message.usage, "cache_read_input_tokens", 0
                            )
                            cache_creation_input_tokens = getattr(
                                event.message.usage,
                                "cache_creation_input_tokens",
                                0,
                            )
                            service_tier = getattr(
                                event.message.usage, "service_tier", "unknown"
                            )

                            # Calculate total tokens and cache hit ratio
                            total_input_tokens = (
                                event.message.usage.input_tokens
                                + cache_read_input_tokens
                                + cache_creation_input_tokens
                            )
                            cache_ratio = cache_read_input_tokens / (
                                total_input_tokens + 1e-6
                            )

                            # Record metrics
                            token_labels = dict(
                                model_caller=model_caller,
                                model=self.model_name,
                                client_type=self.client_type,
                                provider_region=self.provider_region,
                            )
                            PROMPT_TOTAL_INPUT_TOKENS.labels(**token_labels).observe(
                                total_input_tokens
                            )
                            PROMPT_INPUT_TOKENS.labels(**token_labels).observe(
                                event.message.usage.input_tokens
                            )
                            PROMPT_CACHE_READ.labels(**token_labels).observe(
                                cache_read_input_tokens
                            )
                            PROMPT_CACHE_WRITE.labels(**token_labels).observe(
                                cache_creation_input_tokens
                            )
                            PROMPT_CACHE_HIT_RATIO.labels(**token_labels).observe(
                                cache_ratio
                            )

                            self.logger.info(
                                "Prompt cache usage: total input_tokens=%d, input_tokens=%d, cache_read_input_tokens=%d, cache_creation_input_tokens=%d, cache_ratio=%.2f, service_tier=%s",
                                total_input_tokens,
                                event.message.usage.input_tokens,
                                cache_read_input_tokens,
                                cache_creation_input_tokens,
                                cache_ratio,
                                service_tier,
                                model_caller=model_caller,
                                model_name=self.model_name,
                            )

                            # Get the current user message (last message in the conversation)
                            current_user_message = None
                            for message in reversed(formatted_messages):
                                if message.get("role") == "user":
                                    current_user_message = message
                                    break

                            # Calculate token breakdown for the current user message only
                            text_input_tokens, tool_input_tokens = (
                                self._estimate_message_tokens(
                                    current_user_message,
                                    event.message.usage.input_tokens,
                                )
                            )

                            self.logger.info(
                                "Input token breakdown: text_tokens=%d, tool_tokens=%d",
                                text_input_tokens,
                                tool_input_tokens,
                            )

                            # We'll set output tokens to 0 for now and update them in MESSAGE_STOP
                            text_output_tokens = 0
                            tool_output_tokens = 0

                            # Store prompt cache usage for later inclusion in EndOfStream
                            stored_prompt_cache_usage = PromptCacheUsage(
                                input_tokens=event.message.usage.input_tokens,
                                cache_read_input_tokens=cache_read_input_tokens,
                                cache_creation_input_tokens=cache_creation_input_tokens,
                                text_input_tokens=text_input_tokens,
                                tool_input_tokens=tool_input_tokens,
                                text_output_tokens=text_output_tokens,
                                tool_output_tokens=tool_output_tokens,
                            )
                        elif event.type == EVENT_TYPE_MESSAGE_STOP:
                            output_tokens = event.message.usage.output_tokens
                            _output_tokens_histogram.labels(
                                model_caller=model_caller,
                                model=self.model_name,
                                client_type=self.client_type,
                                provider_region=self.provider_region,
                            ).observe(output_tokens)
                            self.logger.info(
                                "Generation complete: output_tokens=%d, tools_used=%d",
                                output_tokens,
                                tool_uses,
                            )

                            # Update output token counts in stored_prompt_cache_usage
                            if (
                                stored_prompt_cache_usage is not None
                                and event.message.content
                            ):
                                # Estimate output token counts by type
                                text_output_tokens, tool_output_tokens = (
                                    self._estimate_output_content_tokens(
                                        event.message.content, output_tokens
                                    )
                                )

                                # Update the stored prompt cache usage
                                stored_prompt_cache_usage.text_output_tokens = (
                                    text_output_tokens
                                )
                                stored_prompt_cache_usage.tool_output_tokens = (
                                    tool_output_tokens
                                )

                                self.logger.info(
                                    "Output token breakdown: text_tokens=%d, tool_tokens=%d",
                                    text_output_tokens,
                                    tool_output_tokens,
                                )

                                self.logger.info(
                                    "Token usage breakdown: text_input_tokens=%d, tool_input_tokens=%d, text_output_tokens=%d, tool_output_tokens=%d",
                                    stored_prompt_cache_usage.text_input_tokens,
                                    stored_prompt_cache_usage.tool_input_tokens,
                                    stored_prompt_cache_usage.text_output_tokens,
                                    stored_prompt_cache_usage.tool_output_tokens,
                                )

                            # Stop reasons are: end_turn, stop_sequence, tool_use, max_tokens
                            if event.message.stop_reason == "max_tokens":
                                self.logger.error(
                                    "Generation stopped due to max_tokens",
                                    model_caller=model_caller,
                                    model_name=self.model_name,
                                    message_id=event.message.id,
                                    partial_tool_use_id=streaming_tool_use_id,
                                    partial_tool_use_name=loggable_tool_name(
                                        streaming_tool_name
                                    ),
                                    num_tool_uses=tool_uses,
                                )

                                # yield partial tool use if special conditions are met
                                if event.message and event.message.content:
                                    tool_use = event.message.content[-1]
                                    if (
                                        isinstance(tool_use, ToolUseBlock)
                                        and tool_use.id not in returned_tool_use_ids
                                        and should_return_partial_tool_use_block(
                                            tool_use
                                        )
                                    ):
                                        yield create_tool_use_response(
                                            tool_use, is_partial=True
                                        )

                            match event.message.stop_reason:
                                case "end_turn" | "stop_sequence":
                                    stop_reason = StopReason.END_TURN
                                case "tool_use":
                                    stop_reason = StopReason.TOOL_USE_REQUESTED
                                case "max_tokens":
                                    stop_reason = StopReason.MAX_TOKENS
                                case _:
                                    stop_reason = StopReason.REASON_UNSPECIFIED
                            yield ThirdPartyModelResponse(
                                text="",
                                end_of_stream=EndOfStream(
                                    stop_reason=stop_reason,
                                    output_tokens=output_tokens,
                                    prompt_cache_usage=stored_prompt_cache_usage,
                                ),
                            )
                        elif event.type == EVENT_TYPE_TEXT:
                            _output_text_chars_counter.labels(
                                model_caller=model_caller,
                                model=self.model_name,
                                client_type=self.client_type,
                                provider_region=self.provider_region,
                            ).inc(len(event.text))
                            yield ThirdPartyModelResponse(text=event.text)
                        elif (
                            event.type == EVENT_TYPE_CONTENT_BLOCK_START
                            and isinstance(event.content_block, ToolUseBlock)
                        ):
                            tool_uses += 1
                            if tool_uses == 1:
                                streaming_tool_name = event.content_block.name
                                streaming_tool_use_id = event.content_block.id
                                yield ThirdPartyModelResponse(
                                    text="",
                                    tool_use_start=ToolUseStart(
                                        tool_name=streaming_tool_name,
                                        tool_use_id=streaming_tool_use_id,
                                    ),
                                )
                            else:
                                self.logger.warning("Dropping a parallel tool call")
                        elif event.type == EVENT_TYPE_CONTENT_BLOCK_STOP:
                            if isinstance(event.content_block, ToolUseBlock):
                                # Only yield tool use response for the first tool use
                                if event.content_block.id == streaming_tool_use_id:
                                    streaming_tool_name = ""
                                    streaming_tool_use_id = ""
                                    yield create_tool_use_response(event.content_block)
                            elif isinstance(event.content_block, TextBlock):
                                # Ignore text block - same text that was streamed
                                pass
                            else:
                                self.logger.error(
                                    "Unknown content block type: %s, type=%s. Ignoring.",
                                    event.content_block.type,
                                    type(event.content_block),
                                )

                        elif event.type in _IGNORED_EVENT_TYPES:
                            self.logger.debug(event.type)
                        else:
                            self.logger.error(
                                "Unknown event type: %s, type=%s. Ignoring.",
                                event.type,
                                type(event),
                            )
            except RateLimitError as e:
                headers_dict = get_rate_limit_headers(e)
                self.logger.warning(
                    "Anthropic RateLimitError %s %s",
                    e.status_code,
                    e.message,
                    model_caller=model_caller,
                    **headers_dict,
                )
                _error_counter.labels(
                    model=self.model_name,
                    client_type=self.client_type,
                    provider_region=self.provider_region,
                    error_type="rate_limit",
                ).inc()

                retry_after_header_value = headers_dict.get("retry_after")
                parsed_retry_after_seconds = self._parse_retry_after_header(
                    retry_after_header_value
                )
                raise ResourceExhaustedRpcError(
                    "Rate limit exceeded", True, parsed_retry_after_seconds
                )
            except InternalServerError as e:
                self.logger.warning(
                    "Anthropic InternalServerError: %s %s", e.status_code, e.message
                )
                _error_counter.labels(
                    model=self.model_name,
                    client_type=self.client_type,
                    provider_region=self.provider_region,
                    error_type="internal_server_error",
                ).inc()
                raise UnavailableRpcError(e.message)
            except APIConnectionError as e:
                self.logger.warning("Anthropic APIConnectionError: %s", e)
                _error_counter.labels(
                    model=self.model_name,
                    client_type=self.client_type,
                    provider_region=self.provider_region,
                    error_type="api_connection_error",
                ).inc()
                raise UnavailableRpcError(e.message)
            except BadRequestError as e:
                reason = bad_request_reason_from_exception(e)
                self.logger.error(
                    "Anthropic BadRequestError: %s",
                    e,
                    model_caller=model_caller,
                    reason=reason,
                )
                # Anthropic Direct returns 400 when account is out of credits
                if (
                    "Your credit balance is too low to access the Anthropic API"
                    in e.message
                ):
                    _error_counter.labels(
                        model=self.model_name,
                        client_type=self.client_type,
                        provider_region=self.provider_region,
                        error_type="payment_required",
                    ).inc()
                    # The third-party-proxy may prefer this to be a different error type
                    # to handle it differently; but for now not returning 400 is improvement
                    raise ResourceExhaustedRpcError(e.message, retry_may_succeed=False)
                _error_counter.labels(
                    model=self.model_name,
                    client_type=self.client_type,
                    provider_region=self.provider_region,
                    error_type="bad_request",
                ).inc()
                # More detailed breakdown of bad_request errors
                _bad_request_counter.labels(
                    model_caller=model_caller,
                    reason=reason,
                ).inc()
                raise InvalidArgumentRpcError(e.message)
            except APIStatusError as e:
                self.logger.warning(
                    "Anthropic APIStatusError: %s %s",
                    e.status_code,
                    e.message,
                    model_caller=model_caller,
                    **get_rate_limit_headers(e),
                )
                if "overloaded" in e.message.lower():
                    _error_counter.labels(
                        model=self.model_name,
                        client_type=self.client_type,
                        provider_region=self.provider_region,
                        error_type="overloaded",
                    ).inc()
                    raise UnavailableRpcError(e.message)
                _error_counter.labels(
                    model=self.model_name,
                    client_type=self.client_type,
                    provider_region=self.provider_region,
                    error_type="api_status_error",
                ).inc()
                raise e

    def _parse_retry_after_header(self, header_value: Any) -> int | None:
        """
        Parse the Retry-After header value,
        based on https://developer.mozilla.org/en-US/docs/Web/HTTP/Reference/Headers/Retry-After

        Args:
            header_value (Any): The value of the Retry-After header.

        Returns:
            int | None: The number of seconds to wait before retrying, or None if the header value is None or invalid.
        """
        if header_value is None:
            return None

        # Header is specified in seconds if it's an int or a string that contains an integer
        if isinstance(header_value, int) or (
            isinstance(header_value, str) and header_value.isdigit()
        ):
            try:
                return int(header_value)
            except ValueError:
                # This case should ideally not be reached if isdigit() is true,
                # but kept for robustness.
                self.logger.warning(
                    f"Could not parse Retry-After header value '{header_value}' as int despite isdigit() check."
                )
                return None

        # Not a number, should be a date
        retry_date = None
        if isinstance(header_value, str):
            try:
                retry_date = parsedate_to_datetime(header_value)
            except Exception as date_parse_exc:
                self.logger.warning(
                    f"Error parsing Retry-After header date value '{header_value}': {date_parse_exc}"
                )
                return None
        elif isinstance(header_value, datetime):
            # Already parsed by sdk or httpx
            retry_date = header_value
        else:
            self.logger.warning(
                f"Malformed or unexpected type for Retry-After header value: '{header_value}' (type: {type(header_value)}). Cannot determine retry duration."
            )
            return None

        # We have a date, convert to seconds from now

        # HTTP-dates are always in GMT/UTC.
        if retry_date.tzinfo is None or retry_date.tzinfo.utcoffset(retry_date) is None:
            retry_date = retry_date.replace(tzinfo=timezone.utc)

        now_utc = datetime.now(timezone.utc)

        parsed_retry_after_seconds = int((retry_date - now_utc).total_seconds())
        if parsed_retry_after_seconds < 0:
            # Date already passed, no need to delay
            return 0
        return parsed_retry_after_seconds

    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: An estimate of the number of tokens in the given message.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        _token_count_calls.labels(client_type=self.client_type).inc()
        return round(
            self.count_tokens_client.beta.messages.count_tokens(
                model=self.model_name,
                messages=[
                    {
                        "content": message,
                        "role": "user",
                    }
                ],
            ).input_tokens
            * 1.1
        )  # Adding 10% buffer due to local tokenizer inaccuracy,
        # see https://github.com/anthropics/anthropic-sdk-python/blob/9e37bcea4f010de26a4e832583ebba9b838dc95c/src/anthropic/_client.py#L274-L276  # noqa

    def _estimate_message_tokens(
        self, message: dict | None, total_input_tokens: int
    ) -> tuple[int, int]:
        """
        Estimate the number of tokens in a message, broken down by text vs. tool content.

        Args:
            message: A message in Anthropic's format

        Returns:
            A tuple of (text_input_tokens, tool_input_tokens)
        """
        text_char_count = 0
        tool_char_count = 0

        if message is None:
            return text_char_count, tool_char_count

        content = message.get("content", "")
        if isinstance(content, str):
            text_char_count += len(content)
        elif isinstance(content, list):
            for node in content:
                if node.get("type") == "text":
                    text_char_count += len(node.get("text", ""))
                elif node.get("type") == "tool_result":
                    node_content = node.get("content", "")
                    if isinstance(node_content, str):
                        tool_char_count += len(node_content)
                    elif isinstance(node_content, list):
                        for content_node in node_content:
                            if content_node.get("type") == "text":
                                tool_char_count += len(content_node.get("text", ""))

        # Calculate the ratio of text to total characters
        total_chars = text_char_count + tool_char_count
        text_ratio = text_char_count / max(total_chars, 1)

        text_input_tokens = int(total_input_tokens * text_ratio)
        tool_input_tokens = total_input_tokens - text_input_tokens

        return text_input_tokens, tool_input_tokens

    def _estimate_output_content_tokens(
        self, content_blocks: list, output_tokens: int
    ) -> tuple[int, int]:
        """
        Estimate the number of tokens in output content blocks, broken down by text vs. tool content.

        Args:
            content_blocks: A list of content blocks from Anthropic's response

        Returns:
            A tuple of (text_output_tokens, tool_output_tokens)
        """
        text_output_chars = 0
        tool_output_chars = 0

        for content_block in content_blocks:
            if isinstance(content_block, TextBlock):
                text_output_chars += len(content_block.text)
            elif isinstance(content_block, ToolUseBlock):
                # Estimate tool use size by serializing the input
                tool_input_str = json.dumps(content_block.input)
                tool_output_chars += len(content_block.name) + len(tool_input_str)

        total_output_chars = text_output_chars + tool_output_chars

        text_output_ratio = text_output_chars / total_output_chars

        # Apply the ratio to the output tokens
        text_output_tokens = int(output_tokens * text_output_ratio)
        tool_output_tokens = output_tokens - text_output_tokens

        return text_output_tokens, tool_output_tokens

    def format_request_message_content(
        self, request_message: RequestMessage, log_warnings: bool = True
    ) -> str | list[dict]:
        message_text = try_get_request_message_as_text(request_message)
        if message_text is not None:
            return message_text
        if isinstance(request_message, str):
            raise ValueError("request_message must not be str by this point.")

        # Assert that request_message is iterable
        # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
        assert iter(request_message) is not None

        formatted_nodes = []
        for node in request_message:
            if node.type == ChatRequestNodeType.TEXT:
                assert node.text_node is not None
                assert node.tool_result_node is None
                # Detect empty or whitespace-only text nodes
                # Claude API rejects requests with empty or whitespace-only text nodes with a 400 Bad Request.
                # We log this but still include the node in the request.
                if node.text_node.content.strip() == "" and log_warnings:
                    self.logger.warning(
                        "Empty text node detected - may cause API rejection"
                    )
                formatted_nodes.append(
                    {
                        "type": "text",
                        "text": node.text_node.content,
                    }
                )
            elif node.type == ChatRequestNodeType.TOOL_RESULT:
                assert node.tool_result_node is not None
                assert node.text_node is None
                formatted_node: dict[str, object] = {
                    "type": "tool_result",
                    "tool_use_id": node.tool_result_node.tool_use_id,
                }

                # Only include is_error if it's True
                if node.tool_result_node.is_error:
                    formatted_node["is_error"] = True

                # Check if content_nodes are present
                if not node.tool_result_node.content_nodes:
                    formatted_node["content"] = node.tool_result_node.content
                else:
                    # Convert content_nodes to Anthropic format
                    formatted_content = []
                    for content_node in node.tool_result_node.content_nodes:
                        if content_node.type == ToolResultContentNodeType.CONTENT_TEXT:
                            formatted_content.append(
                                {"type": "text", "text": content_node.text_content}
                            )
                        elif (
                            content_node.type == ToolResultContentNodeType.CONTENT_IMAGE
                        ):
                            image = content_node.image_content
                            if image is not None and hasattr(image, "format"):
                                media_type = get_image_media_type(image.format)

                                if media_type is None:
                                    if isinstance(image.format, ImageFormatType):
                                        name = image.format.name
                                    else:
                                        name = "<unknown>"
                                    raise ValueError(
                                        f"Unsupported image format in tool result: {name}"
                                    )

                                formatted_content.append(
                                    {
                                        "type": "image",
                                        "source": {
                                            "type": "base64",
                                            "media_type": media_type,
                                            "data": image.image_data,
                                        },
                                    }
                                )

                    # Set the formatted content
                    if formatted_content:
                        formatted_node["content"] = formatted_content
                if formatted_node.get("is_error") and not formatted_node.get("content"):
                    self.logger.warn(
                        "Tool result is error but has no content. Injecting dummy content to avoid API rejection."
                    )
                    formatted_node["content"] = "Unknown error occurred"
                formatted_nodes.append(formatted_node)
            elif node.type == ChatRequestNodeType.IMAGE:
                assert node.image_node is not None
                if node.image_node.format == ImageFormatType.IMAGE_FORMAT_UNSPECIFIED:
                    supported_formats = [
                        format_type.name
                        for format_type in ImageFormatType
                        if format_type != ImageFormatType.IMAGE_FORMAT_UNSPECIFIED
                    ]
                    raise ValueError(
                        f"Image format must be specified. Supported formats: {', '.join(supported_formats)}"
                    )

                media_type = get_image_media_type(node.image_node.format)

                if media_type is None:
                    if isinstance(node.image_node.format, ImageFormatType):
                        name = node.image_node.format.name
                    else:
                        name = "<unknown>"
                    raise ValueError(f"Unsupported image format: {name}")

                formatted_nodes.append(
                    {
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": media_type,
                            "data": node.image_node.image_data,
                        },
                    }
                )

        return formatted_nodes


def format_response_message_content(response_text: ResponseMessage) -> str | list[dict]:
    if isinstance(response_text, str):
        return response_text

    if isinstance(response_text, list):
        response_nodes = response_text
        formatted_nodes = []
        for node in response_nodes:
            # If this is a raw response, emit a "text" block
            #
            # Note: our system sometimes introduces an empty RAW_RESPONSE even if the
            # Claude model didn't return it. In particular, if the Claude response was
            # just a tool use and no text, the chat host will still send an empty
            # RAW_RESPONSE in addition to the TOOL_USE. The empty RAW_RESPONSE then
            # becomes a part of the chat history.
            #
            # If we send an empty text node to Claude, it will reject it with a 400 Bad
            # Request. To avoid the issue, we filter out empty raw responses as a part
            # of prompt formatting.
            #
            # Similarly, Claude will also reject any text nodes that contain whitespace
            # only. And, we do see cases where Claude generates such text nodes, such as
            # a text node containing just "\n".
            if node.type == ChatResultNodeType.RAW_RESPONSE:
                is_whitespace_only = node.content.strip() == ""
                if not is_whitespace_only:
                    assert node.tool_use is None
                    formatted_nodes.append(
                        {
                            "type": "text",
                            "text": node.content,
                        }
                    )
        for node in response_nodes:
            if node.type == ChatResultNodeType.TOOL_USE:
                assert node.tool_use is not None
                formatted_nodes.append(
                    {
                        "type": "tool_use",
                        "id": node.tool_use.tool_use_id,
                        "name": node.tool_use.name,
                        "input": node.tool_use.input,
                    }
                )
        return formatted_nodes


def formatted_messages_contain_tools(formatted_messages: list[dict]) -> bool:
    for message in formatted_messages:
        content = message["content"]
        if isinstance(content, str):
            continue
        for node in content:
            if node["type"] in ["tool_use", "tool_result"]:
                return True
    return False


def set_cache_control_ephemeral(message):
    content = message["content"]
    if isinstance(content, str):
        content = [
            {
                "type": "text",
                "text": message["content"],
            }
        ]

    if len(content) >= 1:
        content[-1]["cache_control"] = {"type": "ephemeral"}
    message["content"] = content


def set_cache_control_tool_result(user_message):
    content = user_message["content"]
    if isinstance(content, str):
        return False

    last_result_idx = None
    for i, node in enumerate(content):
        if node["type"] == "tool_result":
            last_result_idx = i
        elif node["type"] == "text":
            break
    if last_result_idx is None:
        return False
    content[last_result_idx]["cache_control"] = {"type": "ephemeral"}
    return True

"""A client for Anthropic models via Vertex AI."""

import anthropic
import httpx
import structlog
from typing_extensions import override

from base.third_party_clients import anthropic_direct_client

MAX_RETRIES = 2  # Default is 2
# Default is httpx.Timeout(timeout=600.0, connect=5.0)
# We add a read timeout, but it's not very strict as it covers context processing
TIMEOUT = httpx.Timeout(timeout=280.0, connect=3.0, read=75.0)


class AnthropicVertexAiClient(anthropic_direct_client.AnthropicDirectClient):
    """
    A class to interact with Anthropic via VertexAi.
    """

    client_type = "anthropic_vertexai"

    def __init__(
        self,
        project_id,
        region,
        model_name,
        temperature,
        max_output_tokens,
    ):
        """
        Initialize the Anthropic VertexAi API with project details and model parameters.

        Args:
            project_id (str): The ID of the Google Cloud project.
            region (str): The region of the Google Cloud project.
            model_name (str): The name of the model to use for generating responses.
            temperature (float): The temperature parameter for controlling the randomness of the responses.
            max_output_tokens (int): The maximum number of tokens to generate in the response.
        """
        # TODO(markus): is the call to super init missing?
        self.model_name = model_name
        self.provider_region = region
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.client = anthropic.AnthropicVertex(
            region=region,
            project_id=project_id,
            max_retries=MAX_RETRIES,
            timeout=TIMEOUT,
        )
        # to be used by count_tokens calls, which still go to first party API
        self.count_tokens_client = anthropic.Anthropic()
        self.logger = structlog.get_logger().bind(
            client_type=self.client_type,
            anthropic_target=self.client_type + "_" + region,
        )
        self.logger.info(
            "Anthropic Direct Client initialized",
            model_name=model_name,
        )
        # Anthropic Vertex AI does not require special headers for prompt caching
        self.prompt_caching_headers = None

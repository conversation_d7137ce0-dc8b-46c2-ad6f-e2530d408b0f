"""Unit tests for the OpenAIClient."""

import pytest

import base.feature_flags
from base.third_party_clients.openai_client import should_return_partial_tool_use_block
from base.third_party_clients.openai_client import _SAVE_FILE_PARTIAL_TOOL_USE
from base.third_party_clients.third_party_model_client import ToolUseResponse


@pytest.fixture
def feature_flags():
    yield from base.feature_flags.feature_flag_fixture()


@pytest.mark.parametrize(
    "tool_use_block, expected",
    [
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="str-replace-editor",
                input={
                    "command": "str_replace",
                    "path": "example/file.py",
                    "old_str_1": "def old_function():",
                    "old_str_start_line_1": 10,
                    "old_str_end_line_1": 10,
                    "new_str_1": "def new_function():",
                },
            ),
            True,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="str-replace-editor",
                input={
                    "command": "view",
                    "path": "example/file.py",
                },
            ),
            False,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="save-file",
                input={
                    "path": "example/file.py",
                    "file_content_1": "def new_function():",
                },
            ),
            True,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="save-file",
                input={
                    "path": "example/file.py",
                },
            ),
            True,
        ),
        (
            ToolUseResponse(
                tool_use_id="toolu_vrtx_01PA4JbnRWpgYuQGXSxbCp6H",
                tool_name="save-file",
                input={
                    "file_content": "def new_function():",
                },
            ),
            False,
        ),
    ],
)
def test_should_return_partial_tool_use_block(
    feature_flags, tool_use_block: ToolUseResponse, expected: bool
):
    """Test that the client correctly determines whether to return a partial tool use block."""
    feature_flags.set_flag(_SAVE_FILE_PARTIAL_TOOL_USE, True)
    assert should_return_partial_tool_use_block(tool_use_block) == expected

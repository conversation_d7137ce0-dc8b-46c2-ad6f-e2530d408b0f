load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_to_json",
)
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:go.bzl", "go_library", "go_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

jsonnet_to_json(
    name = "public_iap_certs_json",
    src = "public_iap_certs.jsonnet",
    outs = ["public_iap_certs.json"],
    visibility = ["//services/token_exchange/server:__pkg__"],
)

rust_library(
    name = "iap_rs",
    srcs = ["iap.rs"],
    aliases = aliases(),
    crate_name = "iap",
    data = [
        ":public_iap_certs_json",
    ],
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ),
)

rust_test(
    name = "iap_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":iap_rs",
    data = glob(
        [
            "test_data/*",
        ],
    ),
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

py_library(
    name = "iap_py",
    srcs = [
        "iap.py",
    ],
    data = [
        ":public_iap_certs_json",
    ],
    visibility = BASE_VISIBILITY,
    deps = [
        requirement("cryptography"),
        requirement("pyjwt"),
    ],
)

pytest_test(
    name = "iap_py_test",
    srcs = ["iap_test.py"],
    deps = [
        ":iap_py",
    ],
)

go_library(
    name = "iap_go",
    srcs = ["iap.go"],
    data = [
        ":public_iap_certs_json",
    ],
    importpath = "github.com/augmentcode/augment/base/cloud/iap",
    visibility = BASE_VISIBILITY,
    deps = [
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_micahparks_jwkset//:jwkset",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
    ],
)

go_test(
    name = "iap_go_test",
    srcs = ["iap_test.go"],
    data = glob(
        [
            "test_data/*",
        ],
    ) + [
        ":public_iap_certs_json",
    ],
    embed = [":iap_go"],
)

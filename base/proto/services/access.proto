// Access types / levels / parameters for Genie production access
syntax = "proto3";

package access;

enum SupportUiAccessScope {
  // Access to the requests and content data
  REQUESTS = 0;

  // Access to the users and auth data
  USERS = 1;

  // Full access to the Kubernetes namespace
  // Full is actually not entirely true. It does not include access to secrets.
  FULL = 2;

  // Limited access to the Kubernetes namespace
  KUBERNETES_LIMITED = 3;
}

message SupportUiAccess {
  // the namespace or tenant name the user is requesting access to
  //
  // the scope of access determines if this is a namespace or tenant name
  // for kubernetes limited access and full, the tenant_name is actually the
  // namespace name

  // TODO: the SupportUiAccess CRD in services/support/deployed_shared.jsonnet isn't tenant-aware
  // or endpoint-aware yet, so for now this is just equivalent to namespace.
  string tenant_name = 1;

  // the scope of access the user is requesting
  optional SupportUiAccessScope scope = 2;

  // custom token scopes for token scope access
  repeated string token_scopes = 3;
}

// Access to a Kubernetes namespace
// Kubernetes namespaces are not tenant-aware
// Kubernetes access implies access with scope "full"
message KubernetesNamespaceAccess {
  string namespace = 1;
}

message AccessType {
  oneof type {
    SupportUiAccess support_ui_access = 1;
    KubernetesNamespaceAccess kubernetes_namespace_access = 2;
  }
}

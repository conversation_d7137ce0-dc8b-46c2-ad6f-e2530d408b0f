"""Tests for the PleaseHold prompt formatter."""

import pytest
from textwrap import dedent

from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
)
from base.prompt_format_router.pleasehold_prompt_formatter import (
    PleaseHoldPromptFormatter,
    SYSTEM_PROMPT_TEMPLATE,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.prompt_format.common import PromptChunk, Exchange


DENSE_RETRIEVER_ORIGIN = "dense_retriever"


@pytest.fixture
def tokenizer():
    return Qwen25CoderTokenizer()


@pytest.fixture
def token_apportionment():
    return ChatTokenApportionment(
        path_len=256,
        prefix_len=1024 * 2,
        chat_history_len=1024 * 4,
        suffix_len=1024 * 2,
        retrieval_len=-1,  # unlimited retrieval
        max_prompt_len=12288,  # 12k for prompt
        # Fields unused by default
        retrieval_len_per_each_user_guided_file=0,
        retrieval_len_for_user_guided=0,
        recent_changes_len=0,
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )


@pytest.fixture
def docsets():
    return ["python", "numpy", "pandas"]


@pytest.fixture
def formatter(tokenizer, token_apportionment, docsets):
    return PleaseHoldPromptFormatter(
        tokenizer=tokenizer, token_apportionment=token_apportionment, docsets=docsets
    )


def test_format_prompt_basic(formatter, tokenizer):
    """Test basic prompt formatting with minimal input."""
    prompt_input = ChatPromptInput(
        message="Hello, how can I help you?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    expected_output = dedent(f"""
<|im_start|>system
{SYSTEM_PROMPT_TEMPLATE.format(docsets='python,numpy,pandas')}<|im_end|>
<|im_start|>user
Hello, how can I help you?<|im_end|>
<|im_start|>assistant
""").lstrip()

    # Debug prints
    print("\nActual output:")
    print(repr(text))
    print("\nExpected output:")
    print(repr(expected_output))

    assert text == expected_output


def test_format_prompt_with_chat_history(formatter, tokenizer):
    """Test prompt formatting with chat history."""
    prompt_input = ChatPromptInput(
        message="What's next?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message="Hello",
                response_text="Hi there!",
                request_id="123",
            ),
        ],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    expected_output = dedent(f"""
<|im_start|>system
{SYSTEM_PROMPT_TEMPLATE.format(docsets='python,numpy,pandas')}<|im_end|>
<|im_start|>user
Hello<|im_end|>
<|im_start|>assistant
Hi there!<|im_end|>
<|im_start|>user
What's next?<|im_end|>
<|im_start|>assistant
""").lstrip()

    assert text == expected_output


def test_format_prompt_with_code_context(formatter, tokenizer):
    """Test prompt formatting with code context."""
    code = 'def hello():\n    print("Hello, World!")\n'
    prompt_input = ChatPromptInput(
        message="What does this code do?",
        path="test.py",
        prefix="def hello():\n",
        selected_code='    print("Hello, World!")',
        suffix="\n",
        chat_history=[],
        prefix_begin=0,
        suffix_end=len(code),
        retrieved_chunks=[],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    expected_output = dedent(f"""
<|im_start|>system
{SYSTEM_PROMPT_TEMPLATE.format(docsets='python,numpy,pandas')}<|im_end|>
<|im_start|>user
I have the file `test.py` open and has selected part of the code.

Here is the full file:

```
def hello():
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]


```

Here is the selected code:

```
    print("Hello, World!")
```

<|im_end|>
<|im_start|>assistant
Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.<|im_end|>
<|im_start|>user
What does this code do?<|im_end|>
<|im_start|>assistant
""").lstrip()

    assert text == expected_output


def test_format_prompt_with_retrievals(formatter, tokenizer):
    """Test prompt formatting with retrieved chunks."""
    prompt_input = ChatPromptInput(
        message="How do I use this function?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[
            PromptChunk(
                text="This is a test function",
                path="test.py",
                char_start=0,
                char_end=23,
                origin=DENSE_RETRIEVER_ORIGIN,
            ),
        ],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    expected_output = dedent(f"""
<|im_start|>system
{SYSTEM_PROMPT_TEMPLATE.format(docsets='python,numpy,pandas')}<|im_end|>
<|im_start|>user
Below are some relevant files from my project.

Here is an excerpt from the file `test.py`:

```
This is a test function...
```

<|im_end|>
<|im_start|>assistant
Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|im_end|>
<|im_start|>user
How do I use this function?<|im_end|>
<|im_start|>assistant
""").lstrip()

    assert text == expected_output


def test_format_prompt_exceeds_max_length(formatter, tokenizer):
    """Test that prompt is truncated when it exceeds max length."""
    # Create a very long message that would exceed max length
    long_message = "x" * 20000
    prompt_input = ChatPromptInput(
        message=long_message,
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    # The exact truncation point may vary, but we can verify the structure
    assert text.startswith(
        f"<|im_start|>system\n{SYSTEM_PROMPT_TEMPLATE.format(docsets='python,numpy,pandas')}<|im_end|>"
    )
    assert "<|im_start|>user" in text
    assert "<|im_start|>assistant" in text
    assert len(output.tokens) <= formatter.token_apportionment.max_prompt_len


def test_system_prompt_with_docsets(formatter, tokenizer, docsets):
    """Test that system prompt includes docsets information."""
    prompt_input = ChatPromptInput(
        message="Test message",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )
    output = formatter.format_prompt(prompt_input)
    text = tokenizer.detokenize(output.tokens)

    expected_output = dedent(f"""
<|im_start|>system
{SYSTEM_PROMPT_TEMPLATE.format(docsets='python,numpy,pandas')}<|im_end|>
<|im_start|>user
Test message<|im_end|>
<|im_start|>assistant
""").lstrip()

    assert text == expected_output

"""Utilities to format a list of file changes into tokens."""

from pathlib import Path
from base.diff_utils.changes import Modified
from base.diff_utils.diff_formatter import (
    DiffHunk,
    _default_diff_filter,
    format_file_changes,
    format_file_changes_with_ranges,
    tokenize_diff_hunks,
    truncate_diff_hunk_tokens,
)
from base.diff_utils.diff_utils import File
from base.ranges.range_types import <PERSON>r<PERSON><PERSON><PERSON>, LineRange
from base.test_utils.testing_utils import assert_str_eq, error_context
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer


def test_format_file_changes():
    old_code = """\
def ex_func1(a,b):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b
"""

    new_code = """\
def ex_func1(a: int, b: int):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b

def new_func():
    return 1
"""
    file_changes = [
        Modified(
            before=File(path="src/examples.py", contents=old_code),
            after=File(path="src/examples.py", contents=new_code),
        )
    ]

    actual_prompt = format_file_changes(file_changes, diff_context_lines=1)
    star1_expected = """\
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
@@ -8,1 +8,4 @@
@def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
"""
    print("Actual prompt:")
    print(actual_prompt)
    assert_str_eq(actual_prompt, star1_expected)


def test_formatting_diff_hunks_corner_cases():
    # one corner case for unidiff is when renaming files whose name contains
    # whitespaces.
    file_changes = [
        Modified(
            before=File(path="src/file with whitespace.py", contents=""),
            after=File(path="src/file renamed.py", contents="new"),
        )
    ]
    actual_prompt = format_file_changes(file_changes, diff_context_lines=1)
    expected = """\
--- src/file with whitespace.py
+++ src/file renamed.py
@@ -0,0 +1,1 @@
+new
\\ No newline at end of file
"""
    print(actual_prompt)
    assert_str_eq(actual_prompt, expected)

    # now also test the formatting on renamed files
    sample_text = "aaa\nbbb\nccc\nddd\neee\nfff\nggg\n"
    file_changes = [
        Modified(
            before=File(path="old name.py", contents=sample_text),
            after=File(path="new name.py", contents=sample_text + "new\n"),
        )
    ]
    actual_prompt = format_file_changes(file_changes, diff_context_lines=1)
    expected = """\
--- old name.py
+++ new name.py
@@ -7,1 +7,2 @@
 ggg
+new
"""
    print(actual_prompt)
    assert_str_eq(actual_prompt, expected)


def test_format_file_changes_filtering():
    # test filtering out diffs from certain files
    file_changes = [
        Modified(
            before=File(path="src/file1.py", contents="old"),
            after=File(path="src/file1.py", contents="new"),
        ),
        Modified(
            before=File(path="src/file2.ipynb", contents="old"),
            after=File(path="src/file2.ipynb", contents="new"),
        ),
    ]
    # the default diff filter should filter out ipynb files
    actual_prompt = format_file_changes(file_changes, diff_context_lines=1)
    expected = """\
+++ src/file1.py
@@ -1,1 +1,1 @@
-old
\\ No newline at end of file
+new
\\ No newline at end of file
"""
    print(actual_prompt)
    assert_str_eq(actual_prompt, expected)


def test_format_file_changes_multiple_files():
    file_changes = [
        Modified(
            before=File(path="src/file1.py", contents="old\n"),
            after=File(path="src/file1.py", contents="new\n"),
        ),
        Modified(
            before=File(path="src/file2.py", contents="old\n"),
            after=File(path="src/file2.py", contents="new\n"),
        ),
    ]
    actual_prompt = format_file_changes(file_changes, diff_context_lines=1)
    expected = """\
+++ src/file1.py
@@ -1,1 +1,1 @@
-old
+new

+++ src/file2.py
@@ -1,1 +1,1 @@
-old
+new
"""
    print(actual_prompt)
    assert_str_eq(actual_prompt, expected)

    actual_prompt = format_file_changes(
        list(reversed(file_changes)),
        diff_context_lines=0,
        deduplicate_identical_paths=False,
    )
    expected = """\
--- src/file2.py
+++ src/file2.py
@@ -1,1 +1,1 @@
-old
+new

--- src/file1.py
+++ src/file1.py
@@ -1,1 +1,1 @@
-old
+new
"""
    print(actual_prompt)
    assert_str_eq(actual_prompt, expected)


def test_format_file_changes_with_ranges():
    old_code = """\
def ex_func1(a,b):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b
"""

    new_code = """\
def ex_func1(a: int, b: int):
    return a+b

def ex_func2(a,b):
    return a-b

def ex_func3(a,b):
    return a*b

def new_func():
    return 1
"""
    file_changes = [
        Modified(
            before=File(path="src/examples.py", contents=old_code),
            after=File(path="src/examples.py", contents=new_code),
        )
    ]

    actual = format_file_changes_with_ranges(file_changes, diff_context_lines=1)
    assert len(actual) == 2

    assert_str_eq(
        actual[0].text_with_header(True),
        """\
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
""",
    )

    assert actual[0].before_path == "src/examples.py"
    assert actual[0].after_path == "src/examples.py"
    assert actual[0].before_lrange == LineRange(0, 2)
    assert actual[0].after_lrange == LineRange(0, 2)
    assert actual[0].before_crange and assert_str_eq(
        old_code[actual[0].before_crange.to_slice()],
        """\
def ex_func1(a,b):
    return a+b
""",
    )
    assert actual[0].after_crange and assert_str_eq(
        new_code[actual[0].after_crange.to_slice()],
        """\
def ex_func1(a: int, b: int):
    return a+b
""",
    )

    # NOTE(arun): we don't include the file header in this one because it isn't the
    # first hunk of the file.
    assert_str_eq(
        actual[1].text,
        """\
@@ -8,1 +8,4 @@
@def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
""",
    )

    assert actual[1].before_path == "src/examples.py"
    assert actual[1].after_path == "src/examples.py"
    assert actual[1].before_lrange == LineRange(7, 8)
    assert actual[1].after_lrange == LineRange(7, 11)
    assert actual[1].before_crange and assert_str_eq(
        old_code[actual[1].before_crange.to_slice()],
        """\
    return a*b
""",
    )
    assert actual[1].after_crange and assert_str_eq(
        new_code[actual[1].after_crange.to_slice()],
        """\
    return a*b

def new_func():
    return 1
""",
    )


def test_tokenize_diff_hunks():
    diff_hunks = [
        DiffHunk(
            text="""\
+++ src/examples.py
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
""",
            before_path="src/examples.py",
            after_path="src/examples.py",
            # Using sentinel values here because we don't care about them for this test.
            before_crange=CharRange(0, 0),
            after_crange=CharRange(0, 0),
            before_lrange=LineRange(0, 0),
            after_lrange=LineRange(0, 0),
        ),
        DiffHunk(
            text="""\
@@ -8,1 +8,4 @@
@def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
""",
            before_path="src/examples.py",
            after_path="src/examples.py",
            # Using sentinel values here because we don't care about them for this test.
            before_crange=CharRange(0, 0),
            after_crange=CharRange(0, 0),
            before_lrange=LineRange(0, 0),
            after_lrange=LineRange(0, 0),
        ),
    ]

    tokenizer = StarCoder2Tokenizer()

    # Use infinite budget to get everything.
    t_hunks = tokenize_diff_hunks(
        diff_hunks, diff_token_budget=1_000_000, tokenizer=tokenizer
    )

    assert len(t_hunks) == 2
    assert len(t_hunks[0].body_tokens) == 51
    assert len(t_hunks[1].body_tokens) == 43
    assert tokenizer.detokenize(t_hunks[0].body_tokens) == diff_hunks[0].text
    assert tokenizer.detokenize(t_hunks[1].body_tokens) == diff_hunks[1].text

    # With a reduced budget, get just the last hunk.
    reduced_budget = t_hunks[1].num_tokens()
    actual_hunks = tokenize_diff_hunks(
        diff_hunks, diff_token_budget=reduced_budget, tokenizer=tokenizer
    )
    assert actual_hunks == t_hunks[1:]

    # if the budget is one token less, then we get no hunks left
    reduced_budget = t_hunks[1].num_tokens() - 1
    actual_hunks = tokenize_diff_hunks(
        diff_hunks, diff_token_budget=reduced_budget, tokenizer=tokenizer
    )
    assert actual_hunks == []


def test_truncate_diff_hunks():
    diff_hunks = [
        DiffHunk(
            text="""\
@@ -1,2 +1,2 @@
-def ex_func1(a,b):
+def ex_func1(a: int, b: int):
     return a+b
""",
            before_path="src/examples.py",
            after_path="src/examples.py",
            # Using sentinel values here because we don't care about them for this test.
            before_crange=CharRange(0, 0),
            after_crange=CharRange(0, 0),
            before_lrange=LineRange(0, 0),
            after_lrange=LineRange(0, 0),
        ),
        DiffHunk(
            text="""\
@@ -8,1 +8,4 @@
@def ex_func3(a,b):
     return a*b
+
+def new_func():
+    return 1
""",
            before_path="src/examples.py",
            after_path="src/examples.py",
            # Using sentinel values here because we don't care about them for this test.
            before_crange=CharRange(0, 0),
            after_crange=CharRange(0, 0),
            before_lrange=LineRange(0, 0),
            after_lrange=LineRange(0, 0),
        ),
    ]

    tokenizer = StarCoder2Tokenizer()

    # Use infinite budget to get everything.
    t_hunks = tokenize_diff_hunks(
        diff_hunks, diff_token_budget=1_000_000, tokenizer=tokenizer
    )
    all_tks = [t_hunk.header_tokens + t_hunk.body_tokens for t_hunk in t_hunks]
    all_tks_str = "\n".join(tokenizer.detokenize(tks) for tks in all_tks)

    with error_context(f"all_tks_str:\n{all_tks_str}"):
        assert len(t_hunks) == len(all_tks) == 2
        assert len(all_tks[0]) == 51
        assert len(all_tks[1]) == 50
        assert tokenizer.detokenize(all_tks[0]) == t_hunks[0].hunk.text_with_header(
            True
        )
        assert tokenizer.detokenize(all_tks[1]) == t_hunks[1].hunk.text_with_header(
            True
        )

    # With a reduced budget, get just the last hunk.
    reduced_budget = len(all_tks[1])
    actual_hunks = truncate_diff_hunk_tokens(t_hunks, reduced_budget)
    assert actual_hunks == t_hunks[1:]

    # Get no hunks left
    actual_hunks = truncate_diff_hunk_tokens(t_hunks, reduced_budget - 1)
    assert len(actual_hunks) == 0


def test_default_diff_filter_lowercase():
    assert _default_diff_filter(Path("src/file.py"))
    assert _default_diff_filter(Path("src/file.PY"))
    assert _default_diff_filter(Path("src/file.md"))
    assert _default_diff_filter(Path("src/file.MD"))

    assert not _default_diff_filter(Path("src/file.blahblahblah"))
    assert not _default_diff_filter(Path("src/file.BLAHBLAHBLAH"))

import pytest

from base.diff_utils.changes import Changed
from base.diff_utils.diff_utils import File, compute_file_diff
from base.test_utils.testing_utils import assert_str_eq


def test_compute_file_diff_modified():
    before_code = """\
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        line 4
        line 5
        return line 6
"""

    after_code = """\
# header
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        newline
        line 4
        line 5
        return line 6
# footer
"""

    before_file = File(path="example.py", contents=before_code)
    after_file = File(path="example.py", contents=after_code)
    diff = compute_file_diff(
        before_file, after_file, use_smart_header=False, num_context_lines=1
    )
    expected_basic_header = """\
--- example.py
+++ example.py
@@ -1,1 +1,2 @@
+# header
 class SomeClass:
@@ -11,2 +12,3 @@ class SomeClass:
         line 3
+        newline
         line 4
@@ -14,1 +16,2 @@ class SomeClass:
         return line 6
+# footer
"""
    assert_str_eq(diff, expected_basic_header)


def test_compute_file_diff_smart_header():
    before_code = """\
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        line 4
        return line 5
"""

    after_code = """\
class SomeClass:
    '''Some doc string...

    some details...
    '''

    def some_method(x):
        '''some method doc string'''
        line 1
        line 2
        line 3
        newline
        line 4
        return line 5
"""

    before_file = File(path="example.py", contents=before_code)
    after_file = File(path="example.py", contents=after_code)
    diff = compute_file_diff(before_file, after_file, use_smart_header=True)

    expected_smart_header = """\
--- example.py
+++ example.py
@@ -9,5 +9,6 @@
@class SomeClass:
@    def some_method(x):
         line 1
         line 2
         line 3
+        newline
         line 4
         return line 5
"""
    diff = compute_file_diff(before_file, after_file, use_smart_header=True)
    assert_str_eq(diff, expected_smart_header)

    # if we increase the context lines to 5, then the class should become the header
    # again
    diff = compute_file_diff(
        before_file, after_file, use_smart_header=True, num_context_lines=5
    )
    expected_smart_header_ctx5 = """\
--- example.py
+++ example.py
@@ -7,7 +7,8 @@
@class SomeClass:
     def some_method(x):
         '''some method doc string'''
         line 1
         line 2
         line 3
+        newline
         line 4
         return line 5
"""
    assert_str_eq(diff, expected_smart_header_ctx5)

    # if we reduce the smart header size limit, we should see truncation
    diff = compute_file_diff(
        before_file, after_file, use_smart_header=True, max_smart_header_chars=14
    )
    expected_smart_header_truncated = """\
--- example.py
+++ example.py
@@ -9,5 +9,6 @@
@class Some...
@    def so...
         line 1
         line 2
         line 3
+        newline
         line 4
         return line 5
"""
    assert_str_eq(diff, expected_smart_header_truncated)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_added(use_smart_header: bool):
    added_file = File(path="added.txt", contents="class Added:\n    pass\n")
    diff = compute_file_diff(None, added_file, use_smart_header)
    expected_diff = """\
--- /dev/null
+++ added.txt
@@ -0,0 +1,2 @@
+class Added:
+    pass
"""
    assert_str_eq(diff, expected_diff)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_remove(use_smart_header: bool):
    removed_file = File(path="removed.txt", contents="class Removed:\n    pass\n")
    diff = compute_file_diff(removed_file, None, use_smart_header)
    expected_diff = """\
--- removed.txt
+++ /dev/null
@@ -1,2 +0,0 @@
-class Removed:
-    pass
"""
    assert_str_eq(diff, expected_diff)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_rename(use_smart_header: bool):
    before = File(path="before.txt", contents="before\n")
    after = File(path="after.txt", contents="before\n")
    diff = compute_file_diff(before, after, use_smart_header)
    expected_diff = """\
--- before.txt
+++ after.txt
"""
    assert_str_eq(diff, expected_diff)


@pytest.mark.parametrize("use_smart_header", [False, True])
def test_compute_file_diff_rename_and_change(use_smart_header: bool):
    before = File(path="before.txt", contents="before\n")
    after = File(path="after.txt", contents="after\n")
    diff = compute_file_diff(before, after, use_smart_header)
    expected_diff = """\
--- before.txt
+++ after.txt
@@ -1,1 +1,1 @@
-before
+after
"""
    assert_str_eq(diff, expected_diff)

load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "common",
    srcs = [
        "common.py",
    ],
    deps = [
        "//base/diff_utils",
        "//base/prompt_format:common",
        "//base/ranges",
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "gen_prompt_formatter",
    srcs = [
        "gen_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        "//base/diff_utils",
        "//base/languages",  # for language guesser
        "//base/prompt_format:common",
        "//base/prompt_format:util",
        "//base/ranges",
        "//base/retrieval/chunking",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "gen_prompt_formatter_test",
    srcs = [
        "gen_prompt_formatter_test.py",
    ],
    deps = [
        ":gen_prompt_formatter",
        "//base/diff_utils",
        "//base/ranges",
        "//base/retrieval/chunking",
        "//base/test_utils:testing_utils",
        "//base/tokenizers",
    ],
)

py_library(
    name = "retrieval_prompt_formatter",
    srcs = [
        "retrieval_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        "//base/diff_utils",
        "//base/languages",  # for language guesser
        "//base/prompt_format:common",
        "//base/prompt_format:util",
        "//base/prompt_format_retrieve:prompt_formatter",
        "//base/ranges",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "retrieval_prompt_formatter_test",
    srcs = [
        "retrieval_prompt_formatter_test.py",
    ],
    deps = [
        ":retrieval_prompt_formatter",
        "//base/diff_utils",
        "//base/ranges",
        "//base/retrieval/chunking",
        "//base/test_utils:testing_utils",
        "//base/tokenizers",
    ],
)

py_library(
    name = "description_prompt_formatter",
    srcs = [
        "description_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        "//base/diff_utils",
        "//base/languages",  # for language guesser
        "//base/prompt_format:common",
        "//base/prompt_format:util",
        "//base/prompt_format_chat",
        "//base/ranges",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "description_prompt_formatter_test",
    srcs = [
        "description_prompt_formatter_test.py",
    ],
    deps = [
        ":description_prompt_formatter",
        "//base/diff_utils",
        "//base/ranges",
        "//base/retrieval/chunking",
        "//base/test_utils:testing_utils",
        "//base/tokenizers",
    ],
)

py_library(
    name = "location_prompt_formatter",
    srcs = [
        "location_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        "//base/diff_utils",
        "//base/languages",  # for language guesser
        "//base/prompt_format:common",
        "//base/prompt_format:util",
        "//base/prompt_format_retrieve:prompt_formatter",
        "//base/ranges",
        "//base/tokenizers",
        requirement("dataclasses_json"),
    ],
)

pytest_test(
    name = "location_prompt_formatter_test",
    srcs = [
        "location_prompt_formatter_test.py",
    ],
    deps = [
        ":location_prompt_formatter",
        "//base/diff_utils",
        "//base/ranges",
        "//base/retrieval/chunking",
        "//base/test_utils:testing_utils",
        "//base/tokenizers",
    ],
)

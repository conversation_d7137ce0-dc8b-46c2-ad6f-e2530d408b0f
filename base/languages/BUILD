load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

exports_files(["languages.yaml"])

py_library(
    name = "languages_pylib",
    srcs = ["languages.py"],
    data = [":languages.yaml"],
    deps = [
        requirement("dataclasses-json"),
        requirement("pyyaml"),
    ],
)

pytest_test(
    name = "languages_test",
    size = "small",
    srcs = ["languages_test.py"],
    deps = [":languages_pylib"],
)

py_library(
    name = "language_guesser",
    srcs = ["language_guesser.py"],
    visibility = ["//visibility:public"],
    deps = [":languages_pylib"],
)

pytest_test(
    name = "language_guesser_test",
    size = "small",
    srcs = ["language_guesser_test.py"],
    deps = [
        ":language_guesser",
        ":languages_pylib",
    ],
)

py_library(
    name = "unit_test_guesser",
    srcs = ["unit_test_guesser.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":language_guesser",
        ":languages",
    ],
)

pytest_test(
    name = "unit_test_guesser_test",
    size = "small",
    srcs = ["unit_test_guesser_test.py"],
    deps = [
        ":unit_test_guesser",
    ],
)

py_library(
    name = "comment_guesser",
    srcs = ["comment_guesser.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":language_guesser",
    ],
)

pytest_test(
    name = "comment_guesser_test",
    size = "small",
    srcs = ["comment_guesser_test.py"],
    deps = [
        ":comment_guesser",
    ],
)

py_library(
    name = "languages",
    srcs = ["__init__.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":language_guesser",
        ":languages_pylib",
    ],
)

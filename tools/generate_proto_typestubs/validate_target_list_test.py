import os
import pathlib
import sys
import ast


def find_build_files(directory: pathlib.Path) -> list[pathlib.Path]:
    """Recursively find all BUILD files in a directory."""
    build_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file == "BUILD" or file == "BUILD.bazel":
                build_files.append(pathlib.Path(root) / file)
    return build_files


def parse_build_file(filepath):
    """Parse a BUILD file and return its AST."""
    with open(filepath, "r", encoding="utf-8") as f:
        try:
            content = f.read()
            return ast.parse(content, filename=filepath)
        except SyntaxError as e:
            print(f"Syntax error in {filepath}: {e}")
            return None


def find_py_proto_calls(tree):
    """Find all calls to the function 'py_grpc_library' or 'py_proto_library' in the AST."""
    calls = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
            if node.func.id == "py_grpc_library" or node.func.id == "py_proto_library":
                calls.append(node)
    return calls


def extract_name_argument(call_node):
    """Extract the value of the 'name' argument from a function call node."""
    for keyword in call_node.keywords:
        if keyword.arg == "name" and isinstance(keyword.value, ast.Str):
            return keyword.value.s
    return None


def find_sh_binary_calls(tree):
    """Find all calls to the function 'sh_binary' in the AST."""
    calls = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Name):
            if node.func.id == "sh_binary":
                calls.append(node)
    return calls


def extract_data_argument(call_node):
    """Extract the value of the 'data' argument from a function call node."""
    for keyword in call_node.keywords:
        if keyword.arg == "data" and isinstance(keyword.value, ast.List):
            return [
                element.s
                for element in keyword.value.elts
                if isinstance(element, ast.Str)
            ]
    return None


def get_actual_targetlist(directory: pathlib.Path):
    """Get the actual target list from the BUILD file."""
    tree = parse_build_file(directory / "tools" / "generate_proto_typestubs" / "BUILD")
    calls = find_sh_binary_calls(tree)
    for call in calls:
        data_arg = extract_data_argument(call)
        if data_arg:
            return data_arg
    return None


def scan(directory: pathlib.Path):
    """Main function to find and analyze BUILD files."""
    build_files = (
        find_build_files(directory / "base")
        + find_build_files(directory / "services")
        + find_build_files(directory / "tools")
    )
    if not build_files:
        print("No BUILD files found.")
        return

    results = []
    for build_file in build_files:
        tree = parse_build_file(build_file)
        if tree:
            calls = find_py_proto_calls(tree)
            for call in calls:
                name_arg = extract_name_argument(call)
                if name_arg:
                    build_dir = build_file.parent.relative_to(directory).as_posix()
                    results.append(f"//{build_dir}:{name_arg}")

    results.sort()

    actual_targetlist = get_actual_targetlist(directory)
    assert actual_targetlist is not None, "Failed to find actual targetlist"
    actual_targetlist.sort()
    if not actual_targetlist == results:
        print("Actual targetlist:")
        print(actual_targetlist)
        print("Expected targetlist:")
        print(results)
        print()

        for target in results:
            if target not in actual_targetlist:
                print(f"Missing target: {target}")
        for target in actual_targetlist:
            if target not in results:
                print(f"Extra target: {target}")
        print()
        print("Run update_build.sh to update the target list.")
        sys.exit(1)
    else:
        print("Target list is up to date.")
        sys.exit(0)


if __name__ == "__main__":
    root_dir = pathlib.Path(os.path.realpath(os.environ["FILE"])).parent
    scan(root_dir)

#!/bin/bash -e
# This script copies generate proto files and type stubs to a generated stubs directory.
#
# Usage: `bazel run //tools/generate_proto_typestubs`.
#

if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY not set (run with bazel)" >&2
	exit 1
fi

OUTDIR="$BUILD_WORKSPACE_DIRECTORY"

echo "Copying generated proto files and typestubs..."
mkdir -p $OUTDIR
for fname in $(find . -iname '*.pyi' -or -iname '*_pb2.py' -or -iname '*_pb2_grpc.py'); do
	# ignore everything with "external"
	if [[ $fname == "./external"* ]]; then
		continue
	fi
	dstdir="$OUTDIR/$(dirname $fname)"
	mkdir -p "$dstdir"
	echo "Copying $fname..."
	cp --remove-destination "$fname" "$dstdir"
done
echo "Done."

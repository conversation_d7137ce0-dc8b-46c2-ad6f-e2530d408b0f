#!/bin/bash -e
# This script updates the BUILD file to include the relevant proto targets.
# It should be run whenever the set of proto targets have been changed.
#
# Usage: `update_build.sh`
#

if [ -e "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY set (run without bazel)" >&2
	exit 1
fi

MYDIR="$(dirname "$0")"

# NOTE(arun): This tag must be the same as the one in //tools/bzl/python.bzl.
TYPESTUB_TAG="gen_proto_typestub"

# Get all the proto targets.
proto_targets=$(bazel query "attr('tags', '${TYPESTUB_TAG}',\
    //base/... + //models/... + //services/... + //tools/...)")

echo "Updating BUILD file."

# HEADER
cat <<EOF >$MYDIR/BUILD
# DO NOT EDIT: auto-generated by update_build.sh.
load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("//tools/bzl:python.bzl", "py_test")

sh_binary(
    name = "generate_proto_typestubs",
    srcs = ["copy_proto_typestubs.sh"],
    data = [
EOF

# BODY
for target in $proto_targets; do
	echo "        \"$target\","
done >>$MYDIR/BUILD

# FOOTER
cat <<EOF >>$MYDIR/BUILD
    ],
)

sh_binary(
    name = "clean",
    srcs = ["//base:clean.sh"],
    data = [
        "//base:install_lib",
        "//tools/generate_proto_typestubs",
    ],
)

build_test(
    name = "generate_proto_typestubs_build_test",
    targets = [
        ":generate_proto_typestubs",
    ],
)

py_test(
    name = "validate_target_list_test",
    srcs = ["validate_target_list_test.py"],
    data = ["//:.shellcheckrc"],
    env = {
        "FILE": "\$(location //:.shellcheckrc)",
    },
    tags = [
        "external",
        "no-cache",
        "no-sandbox",
    ],
)
EOF

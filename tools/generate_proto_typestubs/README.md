# Make generated proto files visible to VSCode tooling.

This directory contains scripts to make generated proto files visible to IDE tooling.

## Python Proto Type Stubs

The `generate_proto_typestubs` script copies generated Python proto files and type stubs to the source tree.

These files are not checked into version control.
Their only purpose is to make pyright and other type checking "happy".
The generated files are not used in build targets.

Users will need to run the following regularly to update their local generated Python proto files:
```
bazel run //tools/generate_proto_typestubs
```

When additional Python proto targets are added, the corresponding BUILD file can be updated
using `update_build.sh`. The updated BUILD file should be checked into version control.

## Go Proto Stubs

Generate Go proto stubs:
```
bazel run //tools/generate_proto_typestubs:generate_go_proto_stubs [target_pattern]
```

Where `target_pattern` is an optional Bazel target pattern (default: `//...`).

This helps VSCode and other Go language servers find the generated proto files.

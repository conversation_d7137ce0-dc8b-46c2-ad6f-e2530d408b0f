import argparse
import datetime
import logging
import pathlib

from typing import Any
from tools.load_test.configs.scripts.load_test_data_fetcher import LoadTestDataFetcher
from tools.load_test.configs.scripts.user_extractor import User, UserGCSExporter


# Type argument function for argparse for a float between 0 and 1
def ratio_float_type(arg: Any):
    """Type function for argparse - a float within some predefined bounds"""
    try:
        f = float(arg)
    except ValueError:
        raise argparse.ArgumentTypeError("Must be a floating point number")
    if f < 0.0 or f > 1.0:
        raise argparse.ArgumentTypeError(
            f"Argument must be between 0.0 and 1.0, got {f}"
        )
    return f


def main():
    """Generate users from the request event data.

    This script pulls `request_event` information for `infer_request` events
    between `start` to `end` times from full export request insights data in BigQuery, specifically the
    `system_services_prod.staging_request_insight_full_export_dataset.request_event` table.
    It will then export the users and their requests to GCS in the expected load test format.

    The full config format is located at `/tools/load_test/load_test.proto`

    Example usage is below:
        `bazel run //tools/load_test/configs/scripts:generate_users -- --start 2024-01-01 --end 2024-1-08`
        - This will export all infer_request events from Jan 1st to Jan 8th, 2024.
        - It will pull from `system_services_prod.staging_request_insight_full_export_dataset.request_event`
            and output it to `system_services_dev` project in GCS, in the bucket
            `augment-load-testing-configs` at folder `2024-01-01-to-2024-01-08`
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--target_project",
        type=str,
        default="system-services-dev",
        help="The destination GCP project to dump user configs to",
    )
    parser.add_argument(
        "--bucket_name",
        type=str,
        default="augment-load-testing-configs",
        help="The bucket in the target_project to use",
    )
    parser.add_argument(
        "--start",
        "-s",
        type=datetime.datetime.fromisoformat,
        default=datetime.datetime.now() - datetime.timedelta(hours=1),
        help="Start ISO time to pull from",
    )
    parser.add_argument(
        "--end",
        "-e",
        type=datetime.datetime.fromisoformat,
        default=datetime.datetime.now(),
        help="End ISO time to pull from",
    )
    parser.add_argument(
        "--verbose",
        "-v",
        default=False,
        action="store_true",
        help="If set, will print intermediate outputs",
    )
    parser.add_argument(
        "--requests-per-user",
        "-r",
        type=int,
        default=1000,
        help="The number of requests per user to generate. If a user has more than this "
        + "number of requests, they will be split into multiple users",
    )
    parser.add_argument(
        "--max-split-blob-overlap",
        "-O",
        type=ratio_float_type,
        default=1.0,
        help="The maximum overlap between two consecutive splits of a single user. If the "
        + "overlap is greater than this, then the later split of the user will be discarded.",
    )
    args = parser.parse_args()

    # Cut off microseconds to make formatting, logging, etc. cleaner
    start, end = args.start.replace(microsecond=0), args.end.replace(microsecond=0)
    logging.getLogger().setLevel(logging.INFO if args.verbose else logging.WARNING)

    exporter = LoadTestDataFetcher()
    rows = exporter.get_load_test_datas(start=start, end=end)
    user_stream = User.user_stream_from_ordered_rows(
        rows,
        req_per_user=args.requests_per_user,
        max_split_blob_overlap=args.max_split_blob_overlap,
    )

    user_exporter = UserGCSExporter(args.target_project, args.bucket_name)
    user_exporter.export_to_gcs(
        user_stream,
        export_prefix=f"{start.isoformat()}-to-{end.isoformat()}-overlap-{args.max_split_blob_overlap:.3f}",
    )


if __name__ == "__main__":
    main()

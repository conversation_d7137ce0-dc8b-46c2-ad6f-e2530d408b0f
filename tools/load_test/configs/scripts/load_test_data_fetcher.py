import base64
import datetime
import logging

from dataclasses import dataclass
from dataclasses_json import dataclass_json
from google.cloud import bigquery  # type: ignore
from google.api_core.retry import Retry  # type: ignore
from typing import Generator, Callable

from google.protobuf.json_format import ParseDict


DEFAULT_SRC_PROJECT = "system-services-prod"
DEFAULT_QUERY_PAGE_RETRY = Retry(
    initial=10,  # 10 seconds per page before retrying
    maximum=60,  # 60 seconds max per page
    multiplier=1.5,  # Backoff
    timeout=60 * 5,  # 5 minutes before giving up
)


@dataclass_json
@dataclass
class LoadTestConfigData:
    """A dataclass that wraps a single user request event."""

    request_id: str

    """The type of event"""
    event_type: str

    """The tenant of the event"""
    tenant: str

    """The timestamp of the event"""
    time: datetime.datetime

    """The raw JSON of the event"""
    raw_json: dict

    """User ID of the user"""
    user_id: str

    """The blobs associated with this event.

    These blobs may come from either the `blobs` field in the raw_json, or the deprecated `memories`
    field. In either case, it is in the form of a `blobs` object, with all blob names hex-encoded.
    Use this field when you need to know the blobs from a request and don't care about whether the
    original request had `blobs` or `memories`.
    """
    blobs: dict


def blob_to_hex(blob: str) -> str:
    return base64.b64decode(blob).hex()


class LoadTestDataFetcher:
    """Pulls the request events from BigQuery.

    The output is saved in a JSONL file, where each row is a JSON representing the
    row pulled out of BigQuery. This is mainly used to separate pulling out relevant
    requests and turning them into user configs we can run in load testing.
    """

    bq_client: bigquery.Client

    def __init__(self, src_proj: str = DEFAULT_SRC_PROJECT):
        self.src_proj = src_proj
        self.bq_client = bigquery.Client(project=self.src_proj)

    def parse_row(self, row: bigquery.Row) -> LoadTestConfigData:
        """Convert a BigQuery row to a dict.

        Args:
            row: The row to convert.
        """
        # Get our blobs from the request. See `LoadTestConfigData.blobs` docs for details.
        blobs = {}
        if "blobs" in row.raw_json:
            if "added" in row.raw_json["blobs"]:
                blobs["added"] = [
                    blob_to_hex(blob) for blob in row.raw_json["blobs"]["added"]
                ]
            if "deleted" in row.raw_json["blobs"]:
                blobs["deleted"] = [
                    blob_to_hex(blob) for blob in row.raw_json["blobs"]["deleted"]
                ]
            if "baseline_checkpoint_id" in row.raw_json["blobs"]:
                blobs["baseline_checkpoint_id"] = row.raw_json["blobs"][
                    "baseline_checkpoint_id"
                ]
        elif "memories" in row.raw_json:
            blobs["added"] = row.raw_json["memories"]

        return LoadTestConfigData(
            request_id=row.request_id,
            event_type=row.event_type,
            tenant=row.tenant,
            time=row.time,
            user_id=row.raw_json["user_id"],
            blobs=blobs,
            raw_json=row.raw_json,
        )

    def get_load_test_datas(
        self, start: datetime.datetime, end: datetime.datetime
    ) -> Generator[LoadTestConfigData, None, None]:
        """Get all the request events between `start` and `end`.

        We return the rows sorted by user_id and time ASC in order to allow for
        lazy streaming creation of users, avoiding OOMs while exporting.

        Returns
        - A generator of rows, returned in order of `user_id` and `time`
        """
        QUERY = """
    SELECT
        request_id,
        event_type,
        tenant,
        time,
        raw_json,
    FROM `staging_request_insight_full_export_dataset.request_event`
        AS request_event
    WHERE
        (TIMESTAMP(time) BETWEEN TIMESTAMP(@start_iso) AND TIMESTAMP(@end_iso)) AND
        (JSON_VALUE(raw_json, '$.probe_only') IS NULL OR JSON_VALUE(raw_json, '$.probe_only') != 'true') AND
        (JSON_VALUE(raw_json, '$.user_id') NOT IN ('health-check-1', 'eval-determined-bot') AND JSON_VALUE(raw_json, '$.user_id') IS NOT NULL) AND
        event_type = 'infer_request'
    ORDER BY JSON_VALUE(raw_json, "$.user_id") ASC, time ASC
        """
        PARAMS = [
            bigquery.ScalarQueryParameter("start_iso", "DATETIME", start.isoformat()),
            bigquery.ScalarQueryParameter("end_iso", "DATETIME", end.isoformat()),
        ]

        i = 0
        for row in self.bq_client.query(
            QUERY,
            retry=DEFAULT_QUERY_PAGE_RETRY,
            job_config=bigquery.QueryJobConfig(
                use_legacy_sql=False,
                query_parameters=PARAMS,
            ),
        ).result():
            if i % 1000 == 0:
                logging.info("Retrieved total of {} rows from BigQuery".format(i))
            yield self.parse_row(row)
            i += 1

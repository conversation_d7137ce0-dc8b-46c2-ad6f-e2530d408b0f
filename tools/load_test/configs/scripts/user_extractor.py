import base64
import datetime
import io
import logging
import pathlib

from typing import Union, <PERSON><PERSON><PERSON>, Generator, NamedTuple
from google.protobuf.json_format import MessageToJson
from google.cloud import storage  # type: ignore
from google.api_core.retry import Retry  # type: ignore

from services.api_proxy import public_api_pb2
from tools.load_test import load_test_pb2
from tools.load_test.configs.scripts.load_test_data_fetcher import LoadTestConfigData
from google.protobuf.json_format import Parse, ParseDict

BLOB_NAME_FORMAT = "user-{user_id}-config.jsonl"
SPLIT_USER_FORMAT = "{user_id}-split-{i}"

DEFAULT_USER_UPLOAD_TIMEOUT = 60 * 5  # 5 minutes
DEFAULT_USER_UPLOAD_RETRY = Retry(
    initial=10,  # 10 seconds per page before retrying
    maximum=60,  # 60 seconds max per page
    multiplier=1.5,  # Backoff
    timeout=DEFAULT_USER_UPLOAD_TIMEOUT,
)


def set_overlap(a: set, b: set) -> float:
    """Return the overlap of two sets.

    Computed as (A & B) / (A | B), or the ratio of shared elements to unique elements.

    Args:
        a: The first set.
        b: The second set.
    """
    a_or_b = a.union(b)
    a_and_b = a.intersection(b)
    if len(a_or_b) == 0:
        return 0
    return float(len(a_and_b)) / float(len(a_or_b))


def blob_overlap(r1: LoadTestConfigData, r2: LoadTestConfigData) -> float:
    """Return the overlap of two requests' blobs.

    This is a rough heuristic for how similar the two requests are,
        which are implicitly judged by the retriever.

    Args:
        r1: The first request.
        r2: The second request.
    """
    b1, b2 = r1.blobs, r2.blobs
    b1_names = set(b1.get("added", []) + b1.get("deleted", []))
    b2_names = set(b2.get("added", []) + b2.get("deleted", []))
    return set_overlap(b1_names, b2_names)


def group_rows_by_burst(
    rows: list[LoadTestConfigData],
    max_burst_gap: datetime.timedelta,
    min_blob_similarity: float = 0.5,
) -> list[list[LoadTestConfigData]]:
    """Group the rows by blob similarity and time"""

    last_row: Union[LoadTestConfigData, None] = None
    bursts: list[list[LoadTestConfigData]] = []
    for row in rows:
        # New (and first) burst of activity
        if last_row is None:
            bursts.append([])
        # Last request and current request are too far apart, so a new burst started
        elif (row.time - last_row.time) > max_burst_gap:
            bursts.append([])
        # Last request and current request blobs are too different, a new burst started
        elif blob_overlap(row, last_row) < min_blob_similarity:
            bursts.append([])

        bursts[-1].append(row)
        last_row = row
    return bursts


class StreamUserInfo(NamedTuple):
    user_id: str
    rows: list[LoadTestConfigData]
    split_idx: int


class User:
    source_user_id: str
    user_id: str
    bursts: list[list[LoadTestConfigData]]

    def __init__(
        self, user_id: str, rows: list[LoadTestConfigData], split_idx: int = 0
    ):
        self.source_user_id = user_id
        self.user_id = SPLIT_USER_FORMAT.format(user_id=user_id, i=split_idx)
        self.rows = sorted(rows, key=lambda x: x.time)
        self.bursts = group_rows_by_burst(self.rows, datetime.timedelta(seconds=10))

    @staticmethod
    def user_stream_from_ordered_rows(
        rows_by_user: Iterable[LoadTestConfigData],
        req_per_user: int = 1000,
        max_split_blob_overlap: float = 1.0,
    ) -> Generator["User", None, None]:
        """Create a list of Users from an iterator of rows.

        Args:
            rows_by_user: a stream of rows to create users from, grouped by user_id.
            req_per_user: The number of requests per user.
            max_split_blob_overlap: The maximum overlap between two consecutive splits of a single user.
                If the overlap is greater than this, then the later split of the user will be discarded.
        """
        user_prev_split: Union[User, None] = None
        stream_ctx = StreamUserInfo("", [], 0)
        for row in rows_by_user:
            # New user. We don't create a user with the previous stream_ctx because
            # if there were enough requests, it would have been created in the last iteration of the loop.
            if stream_ctx.user_id != row.user_id:
                logging.info(
                    f"Discarding {len(stream_ctx.rows)} rows for {stream_ctx.user_id} -- not enough to create a new user with {req_per_user} requests"
                )

                # Reset the stream context
                stream_ctx = StreamUserInfo(row.user_id, [], 0)
                user_prev_split = None

            stream_ctx.rows.append(row)
            # If we have streamed in enough requests, yield the user
            if len(stream_ctx.rows) == req_per_user:
                new_user = User(*stream_ctx)

                blob_overlap = (
                    new_user.blob_overlap(user_prev_split)
                    if user_prev_split is not None
                    else 0.0
                )
                # Discard the current rows because there is too much blob overlap
                if blob_overlap > max_split_blob_overlap:
                    logging.info(
                        f"Discarding {len(stream_ctx.rows)} rows -- too much blob overlap between splits for {stream_ctx.user_id}. Got {blob_overlap}, max {max_split_blob_overlap}"
                    )
                    stream_ctx = StreamUserInfo(
                        stream_ctx.user_id, [], stream_ctx.split_idx
                    )
                else:
                    yield new_user
                    user_prev_split = new_user
                    stream_ctx = StreamUserInfo(
                        row.user_id, [], stream_ctx.split_idx + 1
                    )

        logging.info(
            f"Discarding {len(stream_ctx.rows)} rows for {stream_ctx.user_id} -- not enough to create a new user with {req_per_user} requests"
        )

    def blob_overlap(self, other: "User") -> float:
        """Return the overlap of two users' blobs.

        Args:
            other: The other user.
        """
        return blob_overlap(self.rows[0], other.rows[0])

    def blob_ids(self) -> list[str]:
        blob_names = set()
        for row in self.rows:
            blob_names.update(row.blobs.get("added", []))
            blob_names.update(row.blobs.get("deleted", []))
        return list(blob_names)

    def checkpoint_ids(self) -> list[str]:
        checkpoint_ids = set()
        for row in self.rows:
            if row.blobs.get("baseline_checkpoint_id", None) is not None:
                checkpoint_ids.add(row.blobs.get("baseline_checkpoint_id"))
        return list(checkpoint_ids)

    def export_to(self, f: io.IOBase):
        """Export the rows to a file-like object.

        Args:
            f: The file-like object to export to.
            protos: The protos to export.
        """
        logging.info("Exporting config for user {}".format(self.user_id))

        metadata = load_test_pb2.RequestsMetadata(
            blob_names=self.blob_ids(), checkpoint_ids=self.checkpoint_ids()
        )
        # `indent=None` was not added as a documented type until after our version.
        # See https://github.com/protocolbuffers/protobuf/issues/10518 for details.
        f.write(MessageToJson(metadata, indent=None))  # type: ignore
        f.write("\n")
        for proto in self.get_load_test_config_protos():
            f.write(MessageToJson(proto, indent=None))  # type: ignore
            f.write("\n")

    def get_load_test_config_protos(self) -> list[load_test_pb2.Request]:
        """Return the request events as a list of protos"""

        first_request_time = self.bursts[0][0].time
        protos = []
        sequence_id = 0
        for burst in self.bursts:
            for row in burst:
                offset_ms = (row.time - first_request_time) // datetime.timedelta(
                    milliseconds=1
                )
                # Create the protobuf object.
                # Setting each individual field like this is not ideal, as it makes it easy to miss
                # new fields that are added.
                # TODO: Change the RI export to export the entire request object instead of copying
                #       individual fields.
                request = load_test_pb2.Request(
                    offset_millis=offset_ms,
                    completion=public_api_pb2.CompletionRequest(
                        prompt=row.raw_json.get("prompt", None),
                        path=row.raw_json.get("path", None),
                        suffix=row.raw_json.get("suffix", None),
                        lang=row.raw_json.get("lang", None),
                        sequence_id=sequence_id,
                        blobs=public_api_pb2.Blobs(
                            added_blobs=row.blobs.get("added", []),
                            deleted_blobs=row.blobs.get("deleted", []),
                            checkpoint_id=row.blobs.get("baseline_checkpoint_id", None),
                        ),
                        recency_info=ParseDict(
                            row.raw_json["recency_info"],
                            public_api_pb2.RecencyInfo(),
                        )
                        if "recency_info" in row.raw_json
                        else None,
                    ),
                )
                protos.append(request)
                sequence_id += 1
        return protos


class UserGCSExporter:
    """An exporter that exports to GCS."""

    BLOB_DIR = pathlib.Path("user-configs")
    gcs_client: storage.Client

    def __init__(self, target_proj: str, target_bucket: str):
        self.gcs_client = storage.Client(project=target_proj)
        self.target_bucket = self.gcs_client.bucket(target_bucket)

    @staticmethod
    def get_blob_name(user: User) -> str:
        """Return the blob name for the user.

        Args:
            user: The user to get the blob name for.
        """
        return BLOB_NAME_FORMAT.format(user_id=user.user_id)

    def export_to_gcs(
        self, users: Iterable[User], export_prefix: Union[str, pathlib.Path] = ""
    ):
        """Export the rows to GCS.

        Args:
            export_name: The name of the export.
            rows: The rows to export, encoded as JSON
        """
        for user in users:
            blob_name = UserGCSExporter.get_blob_name(user)
            full_name = str(UserGCSExporter.BLOB_DIR / export_prefix / blob_name)
            blob = self.target_bucket.blob(full_name)

            with blob.open(
                "w",
                timeout=DEFAULT_USER_UPLOAD_TIMEOUT,
                retry=DEFAULT_USER_UPLOAD_RETRY,
            ) as f:
                user.export_to(f)

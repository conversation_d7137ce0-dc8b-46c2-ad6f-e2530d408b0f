load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

kubecfg(
    name = "config_bucket_kubecfg",
    src = "config_bucket.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    deps = [
        "//deploy/common:eng-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":config_bucket_kubecfg",
    ],
)

py_library(
    name = "load_test_data_fetcher",
    srcs = ["load_test_data_fetcher.py"],
    deps = [
        requirement("dataclasses-json"),
        requirement("google-cloud-bigquery"),
    ],
)

py_library(
    name = "user_extractor",
    srcs = ["user_extractor.py"],
    deps = [
        ":load_test_data_fetcher",
        "//services/api_proxy:public_api_py_proto",
        "//tools/load_test:load_test_py_proto",
        requirement("google-cloud-storage"),
        requirement("google-api-core"),
    ],
)

py_binary(
    name = "generate_users",
    srcs = ["generate_users.py"],
    deps = [
        ":load_test_data_fetcher",
        ":user_extractor",
    ],
)

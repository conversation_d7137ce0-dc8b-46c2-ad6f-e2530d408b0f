"""
<PERSON>ript to generate load test data for next edit.

See load test README for useage details.

"""

import argparse
import datetime
import logging
import os
import sys
from typing import Iterable, List, Optional

from google.api_core.retry import Retry
from google.cloud import bigquery, storage
from google.protobuf.json_format import Message<PERSON><PERSON><PERSON><PERSON>

from base.datasets.gcs_client import GCSRequestInsightFetcher
from base.datasets.tenants import DatasetTenant, get_tenant
from services.api_proxy import public_api_pb2
from services.next_edit_host import next_edit_pb2
from services.request_insight import request_insight_pb2
from tools.load_test import load_test_pb2

handler = logging.StreamHandler(sys.stdout)
handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(handler)


def get_request_ids_by_user(
    request_type: str,
    start: datetime.datetime,
    end: datetime.datetime,
    user_limit: int,
    requests_per_user_limit: int,
    tenant: DatasetTenant,
) -> Iterable[bigquery.Row]:
    """
    Get user request IDs and times from BigQuery, sorted by time from oldest to newest.

    Args:
        start_date: ISO (YYYY-MM-DDTHH:MM:SSZ) date of first request.
        end_date: ISO (YYYY-MM-DDTHH:MM:SSZ) date of last request.
        user_limit: The maximum number of users to return.
        requests_per_user_limit: The maximum number of requests per user to return.
        tenant_id: The tenant ID to filter by.
        project_name: The name of the project where the BigQuery table is located.

    Returns:
        An iterable of rows, each containing a user_id and an array of structs with request_id and time, sorted from oldest to newest.
    """

    QUERY = """
    SELECT
        user_id,
        ARRAY_AGG(STRUCT(request_id, time) ORDER BY time ASC LIMIT @requests_per_user_limit) AS requests
    FROM
        `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata`
    WHERE
        request_type = @request_type
        AND
        tenant_id = @tenant_id
        AND
        (TIMESTAMP(time) BETWEEN TIMESTAMP(@start_iso) AND TIMESTAMP(@end_iso))
    GROUP BY user_id
    LIMIT @user_limit
    """

    # fall back to the max limit if user_limit is not set
    if user_limit is None:
        user_limit = 9223372036854775807  # largest int64

    # fall back to the max limit if requests_per_user_limit is not set
    if requests_per_user_limit is None:
        requests_per_user_limit = 9223372036854775807  # largest int64

    PARAMS = [
        bigquery.ScalarQueryParameter("tenant_id", "STRING", tenant.tenant_id),
        bigquery.ScalarQueryParameter("request_type", "STRING", request_type),
        bigquery.ScalarQueryParameter("start_iso", "DATETIME", start.isoformat()),
        bigquery.ScalarQueryParameter("end_iso", "DATETIME", end.isoformat()),
        bigquery.ScalarQueryParameter("user_limit", "INT64", user_limit),
        bigquery.ScalarQueryParameter(
            "requests_per_user_limit", "INT64", requests_per_user_limit
        ),
    ]

    client = bigquery.Client(project=tenant.project_id)

    query_retry = Retry(
        initial=10,  # 10 seconds per page before retrying
        maximum=60,  # 60 seconds max per page
        multiplier=1.5,  # Backoff
        timeout=60 * 5,  # 5 minutes before giving up
    )

    for row in client.query(
        QUERY,
        retry=query_retry,
        job_config=bigquery.QueryJobConfig(
            use_legacy_sql=False,
            query_parameters=PARAMS,
        ),
    ).result():
        yield row


def get_request_events_from_ids(
    request_ids: List[str],
    tenant: DatasetTenant,
    request_events: Optional[frozenset[str]] = None,
) -> Iterable[List[request_insight_pb2.RequestEvent]]:
    """
    Get events for a user's requests.

    Args:
        request_ids: A list of request IDs for which to get events.
        project_name: The name of the project where the request events are stored.
        tenant_id: The tenant ID to filter by.
        request_events: A list of request event names to filter by.

    Returns:
        An iterable of lists of events, each list containing the events for a single request.
    """

    if not request_ids:
        return []

    results = GCSRequestInsightFetcher.from_tenant(tenant).get_requests(
        request_ids=request_ids,
        request_event_names=request_events,
    )

    for request in results:
        if isinstance(request, Exception):
            continue
        yield request.events


def convert_next_edit_host_request_to_public_api_request(
    next_edit_host_request: next_edit_pb2.NextEditRequest,
) -> public_api_pb2.NextEditRequest:
    """
    Convert a NextEditRequest from the NextEditHost to a NextEditRequest for the API.

    Args:
        next_edit_host_request: The NextEditRequest from the NextEditHost.

    Returns:
        A NextEditRequest for the API.
    """

    change_type_mapping = {
        next_edit_pb2.ChangeType.ADDED: public_api_pb2.ChangeType.ADDED,
        next_edit_pb2.ChangeType.DELETED: public_api_pb2.ChangeType.DELETED,
        next_edit_pb2.ChangeType.MODIFIED: public_api_pb2.ChangeType.MODIFIED,
        next_edit_pb2.ChangeType.RENAMED: public_api_pb2.ChangeType.RENAMED,
    }

    diagnostic_severity_mapping = {
        next_edit_pb2.DiagnosticSeverity.ERROR: public_api_pb2.DiagnosticSeverity.ERROR,
        next_edit_pb2.DiagnosticSeverity.WARNING: public_api_pb2.DiagnosticSeverity.WARNING,
        next_edit_pb2.DiagnosticSeverity.INFORMATION: public_api_pb2.DiagnosticSeverity.INFORMATION,
        next_edit_pb2.DiagnosticSeverity.HINT: public_api_pb2.DiagnosticSeverity.HINT,
    }

    next_edit_mode_mapping = {
        next_edit_pb2.NextEditMode.UNKNOWN_NEXT_EDIT_MODE: public_api_pb2.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,
        next_edit_pb2.NextEditMode.BACKGROUND: public_api_pb2.NextEditMode.BACKGROUND,
        next_edit_pb2.NextEditMode.FOREGROUND: public_api_pb2.NextEditMode.FOREGROUND,
        next_edit_pb2.NextEditMode.FORCED: public_api_pb2.NextEditMode.FORCED,
    }

    next_edit_scope_mapping = {
        next_edit_pb2.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE: public_api_pb2.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,
        next_edit_pb2.NextEditScope.CURSOR: public_api_pb2.NextEditScope.CURSOR,
        next_edit_pb2.NextEditScope.FILE: public_api_pb2.NextEditScope.FILE,
        next_edit_pb2.NextEditScope.WORKSPACE: public_api_pb2.NextEditScope.WORKSPACE,
    }

    public_api_request = public_api_pb2.NextEditRequest(
        model=next_edit_host_request.model_name,
        sequence_id=next_edit_host_request.sequence_id,
        lang=next_edit_host_request.lang,
        instruction=next_edit_host_request.instruction,
        blobs=public_api_pb2.Blobs(
            checkpoint_id=next_edit_host_request.blobs.baseline_checkpoint_id,
            added_blobs=[bytes.hex() for bytes in next_edit_host_request.blobs.added],
            deleted_blobs=[
                bytes.hex() for bytes in next_edit_host_request.blobs.deleted
            ],
        ),
        recent_changes=[
            public_api_pb2.ReplacementText(
                blob_name=recent_change.blob_name,
                path=recent_change.path,
                char_start=recent_change.char_start,
                char_end=recent_change.char_end,
                replacement_text=recent_change.replacement_text,
                present_in_blob=recent_change.present_in_blob,
            )
            for recent_change in next_edit_host_request.recent_changes
        ],
        vcs_change=public_api_pb2.VCSChange(
            working_directory_changes=[
                public_api_pb2.WorkingDirectoryChange(
                    before_path=wdc.before_path,
                    after_path=wdc.after_path,
                    change_type=change_type_mapping[wdc.change_type],
                    head_blob_name=wdc.head_blob_name,
                    indexed_blob_name=wdc.indexed_blob_name,
                    current_blob_name=wdc.current_blob_name,
                )
                for wdc in next_edit_host_request.vcs_change.working_directory_changes
            ]
        ),
        path=next_edit_host_request.path,
        blob_name=next_edit_host_request.blob_name,
        selection_begin_char=next_edit_host_request.selection_begin_char,
        selection_end_char=next_edit_host_request.selection_end_char,
        prefix=next_edit_host_request.prefix,
        selected_text=next_edit_host_request.selected_text,
        suffix=next_edit_host_request.suffix,
        diagnostics=[
            public_api_pb2.Diagnostic(
                location=public_api_pb2.FileLocation(
                    path=diagnostic.location.path,
                    line_start=diagnostic.location.line_start,
                    line_end=diagnostic.location.line_end,
                ),
                message=diagnostic.message,
                severity=diagnostic_severity_mapping[diagnostic.severity],
            )
            for diagnostic in next_edit_host_request.diagnostics
        ],
        mode=next_edit_mode_mapping[next_edit_host_request.mode],
        scope=next_edit_scope_mapping[next_edit_host_request.scope],
        edit_events=[
            public_api_pb2.FileEditEvent(
                path=granular_edit_event.path,
                before_blob_name=granular_edit_event.before_blob_name,
                after_blob_name=granular_edit_event.after_blob_name,
                edits=[
                    public_api_pb2.FileEdit(
                        before_start=single_edit.before_start,
                        after_start=single_edit.after_start,
                        before_text=single_edit.before_text,
                        after_text=single_edit.after_text,
                    )
                    for single_edit in granular_edit_event.edits
                ],
            )
            for granular_edit_event in next_edit_host_request.edit_events
        ],
        blocked_locations=[
            public_api_pb2.FileRegion(
                path=blocked_location.path,
                char_start=blocked_location.char_start,
                char_end=blocked_location.char_end,
            )
            for blocked_location in next_edit_host_request.blocked_locations
        ],
    )

    return public_api_request


def skip_row(row: bigquery.Row) -> bool:
    """
    Determine if a row should be skipped based on its contents.

    Args:
        row: The row to check.

    Returns:
        True if the row should be skipped, False otherwise.
    """

    if row.requests is None:
        logger.info(f"Skipping user {row.user_id} with no requests")
        return True

    return False


def process_user_row(
    row: bigquery.Row, tenant: DatasetTenant
) -> tuple[set[str], set[str], list[load_test_pb2.Request]]:
    """
    Process a row from BigQuery and return a tuple containing a set of blob names, a set of checkpoint IDs, and a list of requests.

    Args:
        row: The row to process.
        project_name: The name of the project where the BigQuery table is located.
        tenant_id: The tenant ID to filter by.

    Returns:
        A tuple containing a set of blob names, a set of checkpoint IDs, and a list of requests.
    """

    user_request_ids = [request["request_id"] for request in row.requests]

    logger.info(f"Processing user {row.user_id} with {len(user_request_ids)} requests")

    user_blob_names: set[str] = set()
    user_checkpoints: set[str] = set()
    user_requests: list[load_test_pb2.Request] = []

    offset_millis: int = 0
    first_event_time: Optional[datetime.datetime] = None
    # write the requests for this user
    for request_events in get_request_events_from_ids(
        user_request_ids,
        tenant=tenant,
        request_events=frozenset({"next_edit_host_request"}),
    ):
        for event in request_events:
            # parse the next_edit_host_request
            next_edit_host_request = event.next_edit_host_request.request

            # convert the next_edit_host_request to a public_api_request
            public_api_next_edit_request = (
                convert_next_edit_host_request_to_public_api_request(
                    next_edit_host_request
                )
            )

            # Get the offset millis for the events
            try:
                event_time = event.time.ToDatetime()
                if first_event_time:
                    time_delta = event_time - first_event_time
                    offset_millis = int(time_delta.total_seconds() * 1000)
                else:
                    first_event_time = event_time
                    offset_millis = 0
            except Exception as e:
                logger.error(
                    "Error parsing event time: %s, setting offset_millis = 0", e
                )
                offset_millis = 0

            logger.debug("offset_millis: %s", offset_millis)

            # create the load_test_request
            load_test_request = load_test_pb2.Request(
                offset_millis=offset_millis,
                next_edit=public_api_next_edit_request,
            )

            # record request, blobnames and the checkpoint
            user_requests.append(load_test_request)
            user_blob_names.update(public_api_next_edit_request.blobs.added_blobs)
            user_blob_names.update(public_api_next_edit_request.blobs.deleted_blobs)
            user_checkpoints.add(public_api_next_edit_request.blobs.checkpoint_id)

    return user_blob_names, user_checkpoints, user_requests


def save_user_requests(
    store_on_gcs: bool,
    user_request_metadata: load_test_pb2.RequestsMetadata,
    user_requests: list[load_test_pb2.Request],
    user_id: str,
    start: datetime.datetime,
    end: datetime.datetime,
) -> str:
    """
    Save user requests to a file or Google Cloud Storage bucket.

    Args:
        store_on_gcs: If True, store the user requests in a Google Cloud Storage bucket. If False, store the user requests locally.
        user_request_metadata: Metadata for the user requests.
        user_requests: A list of user requests to be saved.
        user_id: The ID of the user whose requests are being saved.
        start: The start date of the data.
        end: The end date of the data.

    Returns:
        The path to the saved user requests.

    Side effects:
        Saves the user requests to a file or Google Cloud Storage bucket.
    """

    user_request_config_file = f"{user_id}_request-config.jsonl"

    if store_on_gcs:
        # setup the user config path
        storage_client = storage.Client()
        bucket_name = (
            "augment-load-testing-configs"  # this bucket lives in system-services-dev
        )
        bucket = storage_client.bucket(bucket_name)
        blob_path = (
            f"user-configs/nextEditHostRequests_{start.isoformat()}-{end.isoformat()}"
        )

        # create the blob
        user_request_config_path = (
            f"gs://{bucket_name}/{blob_path}/{user_request_config_file}"
        )
        blob = bucket.blob(blob_path + f"/{user_request_config_file}")

        # convert requests to json string
        content = MessageToJson(user_request_metadata, indent=None) + "\n"
        content += "\n".join(
            MessageToJson(request, indent=None) for request in user_requests
        )

        # Upload the json string to the blob
        blob.upload_from_string(content, content_type="application/json")

        logger.info(f"File {user_request_config_path} uploaded to GCS bucket.")

    else:
        # setup the local user config dir
        user_config_dir = os.path.expanduser(
            f"~/augment/tools/load_test/configs/users/nextEditHostRequests_{start.isoformat()}-{end.isoformat()}"
        )
        os.makedirs(user_config_dir, exist_ok=True)

        # setup the file with requests for this user
        user_request_config_path = os.path.join(
            user_config_dir, user_request_config_file
        )

        # create the user_request_config_file and write to it
        with open(user_request_config_path, "w") as f:
            f.write(MessageToJson(user_request_metadata, indent=None))
            for request in user_requests:
                f.write("\n")
                f.write(MessageToJson(request, indent=None))

        logger.info(f"File {user_request_config_path} saved.")

    return user_request_config_path


def parse_args(parser: argparse.ArgumentParser):
    parser.add_argument(
        "--start-date",
        type=lambda s: datetime.datetime.fromisoformat(s.rstrip("Z")).replace(
            tzinfo=None
        ),
        required=True,
        help="ISO (YYYY-MM-DDTHH:MM:SSZ) date of first request. Note: must be in UTC.",
    )
    parser.add_argument(
        "--end-date",
        type=lambda s: datetime.datetime.fromisoformat(s.rstrip("Z")).replace(
            tzinfo=None
        ),
        required=True,
        help="ISO (YYYY-MM-DDTHH:MM:SSZ) date of last request. Note: must be in UTC.",
    )
    parser.add_argument(
        "--user-limit",
        type=int,
        required=False,
        help="the max number of users to retrieve requests for",
    )
    parser.add_argument(
        "--requests-per-user-limit",
        type=int,
        required=False,
        help="the max number of requests per user to retrieve",
    )
    parser.add_argument(
        "--requests-per-second",
        type=int,
        required=False,
        default=1,
        help="number of requests per second that each user will send",
    )
    parser.add_argument(
        "--use-realistic-timing",
        action="store_true",
        help="Use realistic timing based on the original request timestamps",
    )
    parser.add_argument(
        "--max-realistic-timing-request-interval-ms",
        type=int,
        default=1000,
        help="The maximum interval between requests, in milliseconds, for realistic timing. This can be used to prevent a user from sleeping for too long (and therefore retain some density of requests), if the data has a large gap between requests.",
    )
    parser.add_argument(
        "--store-on-gcs",
        action="store_true",
        help="If present, store the user configs on GCS. If not present, store the user configs locally.",
    )
    parser.add_argument(
        "--tenant",
        type=str,
        default="dogfood-shard",
        help="The tenant to use (default: dogfood-shard)",
    )
    return parser.parse_args()


def main():
    parser = argparse.ArgumentParser(description="Next Edit Load Test Data Processing")
    args = parse_args(parser)

    if args.use_realistic_timing and args.requests_per_second != 1:
        parser.error(
            f"--use-realistic-timing and --requests-per-second should not be used together. Please choose one timing method. {args.requests_per_second} is not the default value."
        )

    if not args.use_realistic_timing and args.requests_per_second <= 0:
        parser.error("--requests-per-second must be greater than 0.")

    if args.use_realistic_timing and args.max_realistic_timing_request_interval_ms <= 0:
        parser.error(
            "--max-realistic-timing-request-interval-ms must be greater than 0."
        )

    if (
        not args.use_realistic_timing
        and args.max_realistic_timing_request_interval_ms
        != parser.get_default("max_realistic_timing_request_interval_ms")
    ):
        args.use_realistic_timing = True
        logger.info(
            "Using realistic timing because --max-realistic-timing-request-interval-ms was set."
        )

    if args.use_realistic_timing:
        logger.info(
            "Using realistic timing with max request interval of %s ms.",
            args.max_realistic_timing_request_interval_ms,
        )
    else:
        logger.info(
            "Using constant throughput of %s requests per second.",
            args.requests_per_second,
        )
    # Print the arguments used to run the script
    logger.info("Script arguments:")
    for arg, value in vars(args).items():
        logger.info(f"  {arg}: {value}")

    # setup both timing configs and choose later which one to use
    realistic_timing_config = load_test_pb2.UserConfig.RealisticTimingConfig(
        max_request_interval_ms=args.max_realistic_timing_request_interval_ms
    )

    constant_throughput_config = load_test_pb2.UserConfig.ConstantThroughputConfig(
        throughput=args.requests_per_second
    )

    # create the test config file
    load_test_user_count = 0
    load_test_config = load_test_pb2.TestConfig(
        id=f"ne_test_{args.start_date.isoformat()}-{args.end_date.isoformat()}",
        users=[],
    )

    # get tenant
    tenant = get_tenant(args.tenant)

    # get the request ids by user
    request_ids_by_user = get_request_ids_by_user(
        request_type="NEXT_EDIT",
        start=args.start_date,
        end=args.end_date,
        user_limit=args.user_limit,
        requests_per_user_limit=args.requests_per_user_limit,
        tenant=tenant,
    )

    total_request_count = 0

    # save the user requests
    for row in request_ids_by_user:
        if skip_row(row):
            continue

        logger.info(f"Processing user {row.user_id} with {len(row.requests)} requests")

        user_request_metadata = load_test_pb2.RequestsMetadata(
            blob_names=[],
            checkpoint_ids=[],
        )

        blob_names, checkpoints, requests = process_user_row(row, tenant)

        user_request_metadata.blob_names.extend(blob_names)
        user_request_metadata.checkpoint_ids.extend(checkpoints)

        user_request_config_path = save_user_requests(
            args.store_on_gcs,
            user_request_metadata,
            requests,
            row.user_id,
            args.start_date,
            args.end_date,
        )

        total_request_count += len(requests)

        # create the user_config
        user_config = load_test_pb2.UserConfig(
            id=row.user_id,
            request_config_path=user_request_config_path,
        )

        if args.use_realistic_timing:
            user_config.realistic_timing_config.CopyFrom(realistic_timing_config)
        else:
            user_config.constant_throughput_config.CopyFrom(constant_throughput_config)

        # add the user config to the test config
        load_test_config.users.append(user_config)
        load_test_user_count += 1

    # write the config file for this load test
    load_test_config_dir = os.path.expanduser(
        "~/augment/tools/load_test/configs/next_edit_test_configs"
    )
    os.makedirs(load_test_config_dir, exist_ok=True)

    load_test_config_file = f"nextEditHostRequests_{args.start_date.isoformat()}-{args.end_date.isoformat()}.json"
    test_config_path = os.path.join(load_test_config_dir, load_test_config_file)

    with open(test_config_path, "w") as f:
        f.write(MessageToJson(load_test_config, indent=2))

    logger.info(f"Wrote {load_test_user_count} users to {test_config_path}")
    logger.info(f"Total request count: {total_request_count}")


if __name__ == "__main__":
    main()

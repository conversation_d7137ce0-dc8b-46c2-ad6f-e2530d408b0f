local eng = import 'deploy/common/eng.jsonnet';

// If running manually, use the command
// bazel run //tools/load_test/configs/scripts:config_bucket_kubecfg -- --cloud GCP_US_CENTRAL1_DEV apply --env DEV --namespace devtools

function(env, namespace, cloud, namespace_config)
  local bucketName = 'augment-load-testing-configs';
  local appName = 'augment-load-testing-configs';

  // Hardcoding here since this is for testing -- this bucket should only exist in dev
  assert env == 'DEV' || env == 'STAGING';
  assert cloud == 'GCP_US_CENTRAL1_DEV';

  [
    {
      apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
      kind: 'StorageBucket',
      metadata: {
        annotations: {
          // If set to true, the force-destroy directive cleans up the objects within
          // a storage bucket before issuing the delete command.
          // Setting it to false prevents the bucket from being deleted if it is not
          // empty.
          'cnrm.cloud.google.com/force-destroy': 'false',
        },
        labels: {
          app: appName,
        },
        namespace: namespace,
        name: bucketName,
      },
      spec: {
        // Enable IAM policies for this bucket
        uniformBucketLevelAccess: true,
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-eng-access-policy' % bucketName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'StorageBucket',
          external: bucketName,
        },
        bindings: [
          {
            role: 'roles/storage.objectAdmin',
            members: std.map(function(u)
              {
                member: 'user:%<EMAIL>' % u.username,
              }, eng),
          },
        ],
      },
    },
  ]

// Variables for test runners to change:
local numUsers = 8;
local throughputPerUser = 2;

// Where to read the request data from, and the number of splits in the data per each real-world
// user. Do not change these unless you've created a new dataset.
local pathFmt = 'gs://augment-load-testing-configs/user-configs/2024-03-01T00:00:00-to-2024-05-01T00:00:00-overlap-0.500/user-%s-split-%s-config.jsonl';
local userToNumSplits = {
  '<EMAIL>': 2,
  arunchaganty: 78,
  aswin: 20,
  '<EMAIL>': 6,
  carl: 19,
  colin: 11,
  '<EMAIL>': 8,
  dion: 7,
  dirk: 37,
  '<EMAIL>': 4,
  '<EMAIL>': 70,
  guy: 30,
  '<EMAIL>': 2,
  '<EMAIL>': 2,
  igoros: 10,
  jacqueline: 23,
  '<EMAIL>': 28,
  jiayi: 23,
  joel: 30,
  liam: 22,
  '<EMAIL>': 27,
  '<EMAIL>': 16,
  markus: 20,
  '<EMAIL>': 50,
  mattgauntseo: 6,
  mb: 6,
  '<EMAIL>': 6,
  mlm: 11,
  moogi: 19,
  '<EMAIL>': 6,
  msdejong: 54,
  ran: 7,
  '<EMAIL>': 18,
  rich: 15,
  'sam.pullara': 3,
  vincent: 12,
  xiaolei: 2,
};

// Make sure we're not trying to run with more users than are actually available.
local maxUsers = std.sum(std.objectValues(userToNumSplits));
assert numUsers <= maxUsers : 'Too many users requested (max possible is %d)' % maxUsers;

local userConfigs = std.flattenArrays(
  [
    [
      {
        // We sort by id below, so it's important that splitIdx comes before user in the name. This
        // makes it so that there's more variety in requests when running with a subset of users.
        id: '%s-%s' % [splitIdx, user],
        requestConfigPath: pathFmt % [user, splitIdx],
        constantThroughputConfig: {
          throughput: throughputPerUser,
        },
      }
      for splitIdx in std.range(0, userToNumSplits[user] - 1)
    ]
    for user in std.objectFields(userToNumSplits)
  ]
);
local sortedUserConfigs = std.sort(userConfigs, function(userConfig) userConfig.id);

{
  id: 'stress_test',
  users: sortedUserConfigs[:numUsers],
}

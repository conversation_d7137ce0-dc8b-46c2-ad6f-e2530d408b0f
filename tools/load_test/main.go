package main

import (
	"bufio"
	"context"
	"errors"
	"flag"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net/url"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/augmentcode/augment/base/augmentclient"
	publicpb "github.com/augmentcode/augment/services/api_proxy/public_api"
	blobsetup "github.com/augmentcode/augment/tools/load_test/blob_setup"
	ioutils "github.com/augmentcode/augment/tools/load_test/ioutils"
	pb "github.com/augmentcode/augment/tools/load_test/proto"
	"github.com/augmentcode/augment/tools/load_test/telemetry"
	jsonnet "github.com/google/go-jsonnet"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// Wrapper around information about a "user". A user is just a single thread sending a stream of
// requests, though for tests replaying real-world requests these will often correlate with
// real-world users.
type user struct {
	// Configurations for this user, such as the URI for their request config file and target
	// throughput.
	*pb.UserConfig

	// Metadata about the requests this user is going to make, such as the blobs and checkpoints
	// referenced by the requests.
	*pb.RequestsMetadata

	// Overrides this user's completion requests with any fields populated in this completion
	// request. (Most likely used for overriding the completion model.)
	completionOverride *publicpb.CompletionRequest

	// Overrides this user's next edit requests with any fields populated in this next edit request.
	nextEditOverride *publicpb.NextEditRequest

	// The channel that this user receives requests from.
	requests chan *pb.Request

	// Used to make requests.
	augmentClient *augmentclient.Client
}

// Construct a new user from the provided proto config, and begin asynchronously adding requests
// to its requsts channel.
func newUser(
	ctx context.Context,
	userConfig *pb.UserConfig,
	testConfig *pb.TestConfig,
	targetUrl string,
	authToken []rune,
	maxRequests uint64,
) *user {
	// Enforce that every user has specified a timing configuration.
	if userConfig.GetTimingConfig() == nil {
		log.Fatal().Msgf("User %s has no timing configuration", userConfig.Id)
		os.Exit(1)
	}

	reader, err := ioutils.LoadFromUri(userConfig.RequestConfigPath)
	if err != nil {
		log.Fatal().Msgf("Failed to open user file %s: %s", userConfig.RequestConfigPath, err)
		os.Exit(1)
	}

	// Closing this file is deferred in the goroutine below. We need the first line outside the
	// closure for the metadata, but the rest of the file is read in the goroutine.

	readBuf := bufio.NewReader(reader)

	// The first line of every user file is metadata.
	metadata := pb.RequestsMetadata{}
	firstLine, err := ioutils.ReadLine(readBuf)
	if err != nil {
		log.Fatal().Msgf("Failed to read metadata for user %s: %s", userConfig.Id, err)
		os.Exit(1)
	}
	err = protojson.Unmarshal(firstLine, &metadata)
	if err != nil {
		log.Fatal().Msgf("Failed to parse metadata for user %s: %s", userConfig.Id, err)
		os.Exit(1)
	}

	requests := make(chan *pb.Request, min(maxRequests, 100))
	u := user{
		UserConfig: userConfig,

		RequestsMetadata:   &metadata,
		requests:           requests,
		completionOverride: testConfig.CompletionOverride,
		nextEditOverride:   testConfig.NextEditOverride,
		augmentClient: augmentclient.New(
			targetUrl,
			authToken,
			// We don't want any retries during testing.
			augmentclient.WithMaxRetries(0),
			augmentclient.WithName(userConfig.Id),
		),
	}

	// Asynchronously add requests to the user's requests channel. Note that this channel doesn't
	// have to be large enough to fit every request; we'll dynamically add more as requests are
	// made.
	go func(
		ctx context.Context,
		u *user,
		requests chan<- *pb.Request,
		reader *io.ReadCloser,
		readBuf *bufio.Reader,
		maxRequests uint64,
	) {
		defer (*reader).Close()

		requestCount := uint64(0)
		line, err := ioutils.ReadLine(readBuf)
		for err == nil && len(line) > 0 && requestCount < maxRequests {
			// Check if we should stop early.
			select {
			case <-ctx.Done():
				log.Debug().Msgf("User %s request generation ending early", u.Id)
				return
			default:
			}

			requestProto := pb.Request{}
			err := protojson.Unmarshal(line, &requestProto)
			if err != nil {
				log.Warn().Msgf(
					"Failed to parse request for user %s. JSON: %s. Error: %s",
					u.Id, string(line), err,
				)
				continue
			}

			requests <- &requestProto
			requestCount++
			line, err = ioutils.ReadLine(readBuf)
		}
		close(requests)
	}(ctx, &u, requests, &reader, readBuf, maxRequests)

	return &u
}

// Begin sending requests from this user, and record them to the provided responses channel.
// Users must be constructed via newUser to have their requests channel populated.
func (u *user) run(ctx context.Context) {
	if u.GetConstantThroughputConfig() != nil {
		// Add a random initial jitter between 0 and time between each request. This helps to avoid
		// having all users start at the same time to reflect real-world user behavior.
		// Note that we only need to do this for users sending at a constant throughput, since
		// realistic replay users will naturally send requests at different times.
		sleepMillis := 1000. * rand.Float32() / u.GetConstantThroughputConfig().Throughput
		time.Sleep(time.Millisecond * time.Duration(sleepMillis))
	}

	// Used to make sure we don't exit until all requests are done.
	var wg sync.WaitGroup

	prevOffsetMillis := uint64(0)
	for request := range u.requests {
		var sleepMillis uint64
		if u.GetConstantThroughputConfig() != nil {
			sleepMillis = uint64(1000. / u.GetConstantThroughputConfig().Throughput)
		} else if u.GetRealisticTimingConfig() != nil {
			sleepMillis = min(
				request.OffsetMillis-prevOffsetMillis,
				u.GetRealisticTimingConfig().MaxRequestIntervalMs,
			)
			prevOffsetMillis = request.OffsetMillis
		} else {
			// This should be impossible, as enforced in `newUser`.
			log.Fatal().Msgf("User %s has no timing configuration", u.Id)
			os.Exit(1)
		}

		// Sleep until it's time to make the next request or the context is cancelled.
		timer := time.NewTimer(time.Millisecond * time.Duration(sleepMillis))
		log.Debug().Msgf("User %s sleeping for %g s", u.Id, float64(sleepMillis)/1000.)
		select {
		case <-ctx.Done():
			log.Info().Msgf("User %s ending early", u.Id)
			return
		case <-timer.C:
		}

		// Make the request in a new goroutine so that our throughput isn't latency-bound.
		wg.Add(1)
		go func(u *user, request *pb.Request) {
			defer wg.Done()
			switch req := request.Request.(type) {
			case *pb.Request_Completion:
				completionRequest := req.Completion
				proto.Merge(completionRequest, u.completionOverride)
				_, err, http_response := u.augmentClient.Completion(completionRequest)
				if http_response == nil && err != nil {
					log.Warn().Msgf("%s failed to make completion request: %s", u.Id, err)
					return
				} else {
					log.Debug().Msgf("%s completion response status: %s", u.Id, http_response.Status)
				}

			case *pb.Request_NextEdit:
				nextEditRequest := req.NextEdit
				proto.Merge(nextEditRequest, u.nextEditOverride)
				_, err, http_response := u.augmentClient.NextEditStream(nextEditRequest)
				if http_response == nil && err != nil {
					log.Warn().Msgf("%s failed to make next edit request: %s", u.Id, err)
					return
				} else {
					log.Debug().Msgf("%s next edit response status: %s", u.Id, http_response.Status)
				}
			default:
				log.Warn().Msgf("%s received unknown request type: %T", u.Id, req)
				return
			}
		}(u, request)
	}

	wg.Wait()
	log.Info().Msgf("%s finished all requests", u.Id)
}

func main() {
	// Initialize logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"

	// Parse out CLI flags
	cliFlags, err := ParseCliFlags()
	if err != nil {
		log.Error().Msgf("Failed to validate CLI flags: %s", err)
		os.Exit(1)
	}
	// Set logging verbosity
	if cliFlags.Verbose {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	}

	log.Info().Msgf("Running test %s", cliFlags.testConfig.Id)

	ctx, cancel := context.WithCancel(context.Background())

	// Construct our users.
	log.Info().Msgf("Configuring users...")
	users := make([]*user, 0, len(cliFlags.testConfig.Users))
	for _, userConfig := range cliFlags.testConfig.Users {
		// Parse the user's config and begin begin filling a channel with requests.
		user := newUser(
			ctx,
			userConfig,
			cliFlags.testConfig,
			cliFlags.Target,
			cliFlags.authToken,
			cliFlags.MaxRequests,
		)
		users = append(users, user)
	}

	// Upload the blobs needed for the test, and wait for indexing to finish.
	if cliFlags.SrcBlobsBucket != "" {
		// Get all the unique blobs and checkpoints we'll need for the test.
		blobs := make(map[string]struct{})
		checkpoints := make(map[string]struct{})
		for _, user := range users {
			for _, blob := range user.BlobNames {
				blobs[blob] = struct{}{}
			}
			for _, checkpoint := range user.CheckpointIds {
				checkpoints[checkpoint] = struct{}{}
			}
		}

		startupClient := augmentclient.New(
			cliFlags.Target,
			cliFlags.authToken,
			// Allow generous retries during setup. Setup takes a while anyway and we want to be
			// robust to transient failures.
			augmentclient.WithMaxRetries(10),
			augmentclient.WithRetryMinBackoff(time.Millisecond*time.Duration(100)),
			augmentclient.WithRetryMaxBackoff(time.Second*time.Duration(10)),
		)
		err = blobsetup.SetupBlobsAndCheckpoints(
			startupClient, blobs, checkpoints, cliFlags.SrcBlobsBucket, cliFlags.MissingBlobsTolerance,
		)
		if err != nil {
			log.Fatal().Msgf("Failed to setup blobs and checkpoints: %s", err)
			os.Exit(1)
		}
	}

	// If max requests is 0, we only test the setup and not actual
	// sending of requests to the backend.
	if cliFlags.MaxRequests == 0 {
		log.Info().Msgf("Skipping test since max requests is 0")
		os.Exit(0)
	}

	// Initialize OpenTelemetry with the scope given by the arguments. We wait to do this until
	// we're about to run the test so that we don't measure requests made during setup.
	otelEnv := telemetry.SetupOTelEnv(
		telemetry.WithExecutableResource(
			"load_test",
			semconv.ServerAddress(cliFlags.targetUrl.Hostname()),
		),
	)
	defer otelEnv.Teardown()

	// Run the test.
	done := make(chan bool, len(users))
	var userWg sync.WaitGroup
	for _, u := range users {
		userWg.Add(1)
		go func(u *user, done chan bool) {
			defer userWg.Done()
			u.run(ctx)
			done <- true
		}(u, done)
	}

	if cliFlags.StopOnFirstUser {
		// Wait for the first user to finish, then cancel all the other users.
		<-done
		cancel()
	}

	// Wait for all the workers to finish. We do this even when the -stop-on-first-user flag is
	// enabled to give the users a chance to finish their last request and perform any teardown.
	userWg.Wait()
}

// Flags container for CLI args. See cliFlagsContainer.parse for the specific
// flags, their descriptions, and their default values.
type cliFlagsContainer struct {
	// Flags directly parsed from CLI
	Target                string
	AuthTokenFile         string
	ConfigFile            string
	SrcBlobsBucket        string
	MissingBlobsTolerance float64
	CompletionModel       string
	MaxRequests           uint64
	StopOnFirstUser       bool
	Verbose               bool

	// Derived state from CLI flags
	targetUrl  *url.URL
	authToken  []rune
	testConfig *pb.TestConfig
}

// The entry point to get CLI flags from the command line.
// When this is called, the CLI flags are parsed, validated, and derived
// state is calculated, and a fully-populated cliFlagsContainer object is returned.
func ParseCliFlags() (*cliFlagsContainer, error) {
	// Initialize, parse, and validate cliFlags
	cliFlags := (&cliFlagsContainer{}).parse()
	if err := cliFlags.validate(); err != nil {
		return cliFlags, err
	}

	// Derive state from cliFlags
	if err := cliFlags.deriveState(); err != nil {
		return cliFlags, err
	}

	return cliFlags, nil
}

// Below functions should NOT be called by main or the user. They are part of the
// constructor creation flow and are only called by ParseCliFlags.
//   - parse() will parse the CLI flags and return a cliFlagsContainer object.
//   - validate() will validate the parsed flags and return an error if
//     any are invalid. Example: invalid URL format for `Target` flag
//   - deriveState() will derive state from the parsed flags.
//     Example: derive the protobuf config from the `ConfigFile` flag
//
// Parse the CLI flags and return the object for chaning
func (c *cliFlagsContainer) parse() *cliFlagsContainer {
	flag.StringVar(&c.Target, "target", "", "Target URL to make requests to.")
	flag.StringVar(&c.AuthTokenFile, "auth-token-file", "", "File containing API token to use.")
	flag.StringVar(&c.ConfigFile, "config-file", "", "Config file to use.")
	flag.StringVar(&c.SrcBlobsBucket, "src-blobs-bucket", "", "GCS bucket to retrieve blobs "+
		"needed for a test. If empty, blob upload is skipped.")
	flag.Float64Var(
		&c.MissingBlobsTolerance, "missing-blobs-tolerance", 0.05,
		"The maximum proportion of blobs that can be missing (either not in the bucket or failed "+
			"to download) and still run the test.",
	)
	flag.StringVar(
		&c.CompletionModel, "completion-model", "",
		"The model to use for completions. This overrides completionOverride.model in the test "+
			"config.",
	)
	flag.Uint64Var(
		&c.MaxRequests, "max-requests", math.MaxUint64,
		"The maximum number of requests to make, per user. -1 means no limit. 0 means perform "+
			"test setup only.",
	)
	flag.BoolVar(
		&c.StopOnFirstUser, "stop-on-first-user", false,
		"If true, end the test after the first user finishes.",
	)
	flag.BoolVar(&c.Verbose, "verbose", false, "If true, print debug logs.")
	flag.Parse()

	return c
}

// Validate the CLI flags and return a joined error for all invalid flags.
func (c *cliFlagsContainer) validate() (retErr error) {
	if c.Target == "" {
		retErr = errors.Join(retErr, fmt.Errorf("target is missing but required"))
	} else {
		_, err := url.Parse(c.Target)
		if err != nil {
			retErr = errors.Join(retErr, fmt.Errorf("target is not a valid UR: %s", err))
		}
	}
	if c.AuthTokenFile == "" {
		retErr = errors.Join(retErr, fmt.Errorf("auth-token-file is missing but required"))
	}
	if c.ConfigFile == "" {
		retErr = errors.Join(retErr, fmt.Errorf("config-file is missing but required"))
	}
	return retErr
}

// Derive all neceessary state from cli flags to be used later
func (c *cliFlagsContainer) deriveState() (retErr error) {
	var err error
	c.targetUrl, err = url.Parse(c.Target)
	retErr = errors.Join(retErr, err)

	c.authToken, err = parseTokenFile(c.AuthTokenFile)
	retErr = errors.Join(retErr, err)

	c.testConfig, err = parseConfigFile(c.ConfigFile)
	retErr = errors.Join(retErr, err)

	maybeOverrideCompletionMode(c.testConfig, c.CompletionModel)
	return retErr
}

// =================
// Utility functions
// =================

// Parse the auth token file and return the token as a rune slice.
func parseTokenFile(tokenFile string) ([]rune, error) {
	if tokenFile == "" {
		// Eventually we might want to inject a token if the user doesn't provide one, like the
		// end to end test does.
		return nil, fmt.Errorf("No auth-token-file specified")
	}
	tokenBytes, err := os.ReadFile(tokenFile)
	if err != nil {
		return nil, fmt.Errorf("Failed to read auth token file: %s", err)
	}
	return []rune(strings.TrimSpace(string(tokenBytes))), nil
}

// Parse the config file and return the parsed proto.
func parseConfigFile(configFile string) (*pb.TestConfig, error) {
	if configFile == "" {
		return nil, fmt.Errorf("No config-file specified")
	}

	jsonnetVm := jsonnet.MakeVM()
	configJson, err := jsonnetVm.EvaluateFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("Failed to evaluate jsonnet file: %s", err)
	}

	config := pb.TestConfig{}
	err = protojson.Unmarshal([]byte(configJson), &config)
	if err != nil {
		return nil, fmt.Errorf("Failed to parse config file: %s", err)
	}
	return &config, nil
}

func maybeOverrideCompletionMode(cfg *pb.TestConfig, model string) *pb.TestConfig {
	if model != "" {
		log.Info().Msgf("Completion model override: %s", model)
		if cfg.CompletionOverride == nil {
			cfg.CompletionOverride = &publicpb.CompletionRequest{}
		}
		cfg.CompletionOverride.Model = &model
	}
	return cfg
}

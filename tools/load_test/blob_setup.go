package main

import (
	"encoding/json"
	"fmt"
	"io"
	"sync"
	"sync/atomic"
	"time"

	"github.com/augmentcode/augment/base/augmentclient"
	publicpb "github.com/augmentcode/augment/services/api_proxy/public_api"
	ioutils "github.com/augmentcode/augment/tools/load_test/ioutils"
	"github.com/rs/zerolog/log"
)

const (
	// Batch size for /blob-upload requests.
	blobUploadMaxBlobs = 1000

	// Max payload size for /blob-upload requests.
	blobUploadMaxBytes = 1024 * 1024 * 1

	// Batch size for /find-missing requests.
	findMissingMaxBlobs = 4000

	// Batch size for /checkpoint-blobs requsts.
	checkpointBlobsMaxBlobs = 10000

	// Template for constructing GCS checkpoint keys.
	checkpointKeyTemplate = "checkpoints/%s"

	// Template for constructing GCS blob keys.
	blobKeyTemplate = "blobs/%s"

	// How many threads to simultaneously download blobs from GCS with.
	downloadParallelism = 50
)

// Ensure that the given target is up-to-date with all of the provided blobs and checkpoints, by:
//  1. Uploading the contents of any missing blobs, including blobs referenced by checkpoints.
//     Contents are fetched from the provided bucket, which should be one populated by a Request
//     Insight blob-exporter.
//  2. Calling /checkpoint-blobs for the provided checkpoints. The blobs for each checkpoint are
//     determined from the provided bucket.
//  3. Waiting for all blobs to be indexed.
//
// An error is returned if any of these steps fail, or if the server returns any unexpected
// blob/checkpoint ids. Failing to download blobs is only considered an error if the ratio of failed
// blobs exceeds the provided `missingBlobsTolerance`.
//
// See services/request_insight/blob_exporter for details on the bucket structure.
//
// blobNames and checkpoints are conceptually sets, but Go doesn't have a built-in set type. These
// may both be modified by this function.
//
// This function has a mix of synchronous and asynchronous operations. Theoretically more of it
// could be parallelized/piplined, but our worst bottleneck by far during setup is indexing, so in
// general only optimizations that get us indexing sooner are worth it. See code-level comments for
// details.
func SetupBlobsAndCheckpoints(
	augmentClient *augmentclient.Client,
	blobNames map[string]struct{},
	checkpoints map[string]struct{},
	blobsBucket string,
	missingBlobsTolerance float64,
) error {
	// Get the blob names associated with each checkpoint. This is synchronous, under the assumption
	// that there won't be very many checkpoints to fetch.
	checkpointToBlobs := make(map[string][]string)
	for checkpointId := range checkpoints {
		if checkpointId == "" {
			log.Warn().Msgf("Skipping empty checkpoint id")
			continue
		}
		checkpointBlobs, err := getBlobNamesForCheckpoint(checkpointId, blobsBucket)
		if err != nil {
			return err
		}
		checkpointToBlobs[checkpointId] = checkpointBlobs

		// Add the checkpoint's blobs to our set of blobs.
		for _, blob := range checkpointBlobs {
			blobNames[blob] = struct{}{}
		}
	}

	allBlobNames := make([]string, 0, len(blobNames))
	for blob := range blobNames {
		allBlobNames = append(allBlobNames, blob)
	}
	log.Info().Msgf(
		"Test references %d total blobs. Finding and downloading missing blobs...",
		len(allBlobNames),
	)

	// =============================================================================================
	// Begin blob upload pipeline.
	// Find our missing blobs, download their contents from GCS, and upload them to the server.
	// =============================================================================================

	// Find out which blobs need to be uploaded.
	// We do this in a goroutine so that we can download blobs as we find out they're missing.
	missingBlobsChan := make(chan string, findMissingMaxBlobs*2)
	missingBlobsCountChan := make(chan uint32, 1)
	findMissingErr := make(chan error, 1)
	go func(
		missingBlobsChan chan string,
		missingBlobsCountChan chan uint32,
		findMissingErr chan error,
	) {
		missingBlobsCount, err := findMissingBlobs(
			augmentClient,
			allBlobNames,
			missingBlobsChan,
		)
		if err != nil {
			findMissingErr <- err
			return
		}
		missingBlobsCountChan <- missingBlobsCount
		close(missingBlobsChan)
		close(missingBlobsCountChan)
		log.Info().Msgf("%d blobs were missing.", missingBlobsCount)
	}(missingBlobsChan, missingBlobsCountChan, findMissingErr)

	// Download the missing blobs.
	// We do this in a goroutine so that we can upload blobs as they're downloaded.
	downloadedBlobsChan := make(chan publicpb.UploadBlob, blobUploadMaxBlobs)
	downloadedBlobsCountChan := make(chan uint32, 1)
	go func(downloadedBlobsChan chan publicpb.UploadBlob, downloadedBlobsCountChan chan uint32) {
		downloadedBlobsCountChan <- downloadBlobs(
			missingBlobsChan, blobsBucket, downloadedBlobsChan)
		close(downloadedBlobsChan)
		close(downloadedBlobsCountChan)
	}(downloadedBlobsChan, downloadedBlobsCountChan)

	// Upload the missing blobs.
	// This is isn't in a goroutine so that we block until all blobs are uploaded.
	err := uploadBlobs(augmentClient, downloadedBlobsChan)
	if err != nil {
		return err
	}

	// Check for fatal errors from the goroutines above.
	select {
	case err := <-findMissingErr:
		return err
	default:
		break
	}

	// Fail setup if we exceeded our tolerance for missing blobs. (This check should probably happen
	// in parallel with upload, but the code for that is more complex and if we fail to download
	// blobs there's less to upload anyway.)
	failedDownloads := <-missingBlobsCountChan - <-downloadedBlobsCountChan
	if float64(failedDownloads) > float64(len(allBlobNames))*missingBlobsTolerance {
		return fmt.Errorf(
			"Too many missing blobs. Missing %d/%d, tolerance was %f",
			failedDownloads, len(allBlobNames), missingBlobsTolerance,
		)
	}
	log.Info().Msgf(
		"Failed to download %d blobs, which satisfies our tolerance. Proceeding.",
		failedDownloads,
	)

	// =============================================================================================
	// End blob upload pipeline.
	// =============================================================================================

	// Checkpoint our blobs. This is done synchronously under the assumption that there aren't very
	// many checkpoints, and indexing is probably still happening.
	log.Info().Msgf("Checkpointing blobs...")
	for checkpointId, blobsForCheckpoint := range checkpointToBlobs {
		err := checkpointBlobsAndCheck(augmentClient, blobsForCheckpoint, checkpointId)
		if err != nil {
			return err
		}
	}

	// Wait for all of our blobs to be indexed.
	// TODO: Set a timeout for this wait.
	log.Info().Msgf("Waiting for all blobs to be indexed...")
	nonindexedBlobs := allBlobNames
	for {
		nonindexedBlobs, err = getNonindexedBlobs(augmentClient, nonindexedBlobs)
		if err != nil {
			return err
		}
		if len(nonindexedBlobs) == 0 {
			break
		}

		log.Info().Msgf(
			"%d blobs are unindexed. Will sleep 60 seconds and try again.",
			len(nonindexedBlobs),
		)
		time.Sleep(time.Second * 60)
	}

	log.Info().Msgf("%d blobs are uploaded and indexed", len(allBlobNames))
	return nil
}

// Return the list of blob names associated with the given checkpoint, or an error.
func getBlobNamesForCheckpoint(checkpointId string, blobsBucket string) ([]string, error) {
	objectKey := fmt.Sprintf(checkpointKeyTemplate, checkpointId)
	reader, err := ioutils.LoadFromGCS(blobsBucket, objectKey)
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	buf, err := io.ReadAll(reader)
	if err != nil {
		return nil, err
	}

	blobs := make([]string, 0)
	err = json.Unmarshal(buf, &blobs)
	if err != nil {
		return nil, err
	}

	return blobs, nil
}

// Download the contents of the given blobs from the given bucket, and publish them to the provided
// channel. The caller is responsible for ensuring that only missing blobs are provided here.
// Returns the number of successfully downloaded blobs. Does not otherwise propagate errors.
func downloadBlobs(
	blobNamesChan <-chan string,
	blobsBucket string,
	downloadedBlobsChan chan<- publicpb.UploadBlob,
) uint32 {
	// The GCS golang client doesn't have bulk download functionality, so we download each blob in
	// parallel.
	var downloadedCount atomic.Uint32
	var wg sync.WaitGroup
	for range downloadParallelism {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for blob := range blobNamesChan {
				gcsKey := fmt.Sprintf(blobKeyTemplate, blob)

				// Get the blob path.
				attrs, err := ioutils.GetAttrsFromGCS(blobsBucket, gcsKey)
				if err != nil {
					log.Warn().Msgf("Failed to load blob %s: %w", blob, err)
					continue
				}
				path := attrs.Metadata["path"]
				if path == "" {
					log.Warn().Msgf("Path not found for blob %s", blob)
					continue
				}

				// Get the blob contents.
				reader, err := ioutils.LoadFromGCS(blobsBucket, gcsKey)
				if err != nil {
					log.Warn().Msgf("Failed to load content for blob %s: %w", blob, err)
					continue
				}
				defer reader.Close()

				content, err := io.ReadAll(reader)
				if err != nil {
					log.Warn().Msgf("Failed to read content for blob %s: %w", blob, err)
					continue
				}

				downloadedBlobsChan <- publicpb.UploadBlob{
					BlobName: &blob,
					Content:  string(content),
					Path:     path,
				}
				downloadedCount.Add(1)
			}
		}()
	}

	wg.Wait()
	return downloadedCount.Load()
}

// Upload blobs from the given channel to the given target. This function blocks on the channel
// being closed.
func uploadBlobs(augmentClient *augmentclient.Client, downloadedBlobsChan <-chan publicpb.UploadBlob) error {
	uploadBlobsBatch := make([]*publicpb.UploadBlob, 0)
	batchSizeBytes := 0
	for uploadBlob := range downloadedBlobsChan {
		// Upload in batches.
		if len(uploadBlobsBatch)+1 >= blobUploadMaxBlobs ||
			batchSizeBytes+len(uploadBlob.Content) >= blobUploadMaxBytes {
			err := uploadAndCheck(augmentClient, uploadBlobsBatch)
			if err != nil {
				return err
			}

			// Start a new batch.
			uploadBlobsBatch = uploadBlobsBatch[:0]
			batchSizeBytes = 0
		}

		uploadBlobsBatch = append(uploadBlobsBatch, &uploadBlob)
		batchSizeBytes += len(uploadBlob.Content)
	}

	// Upload the final batch.
	if len(uploadBlobsBatch) > 0 {
		err := uploadAndCheck(augmentClient, uploadBlobsBatch)
		if err != nil {
			return err
		}
	}

	return nil
}

// Upload the given blob contents, and check that the returned blob names match the expected names.
// Returns an error if the upload fails or the returned blob names don't match those from the
// request.
func uploadAndCheck(augmentClient *augmentclient.Client, uploadBlobs []*publicpb.UploadBlob) error {
	log.Debug().Msgf("Uploading batch of %d blobs", len(uploadBlobs))
	batchUploadRequest := publicpb.BatchUploadRequest{
		Blobs: uploadBlobs,
	}
	batchUploadResponse, err, _ := augmentClient.BatchUpload(&batchUploadRequest)
	if err != nil {
		return err
	}

	// Make sure the returned blob names match what we expect.
	if len(batchUploadResponse.BlobNames) != len(uploadBlobs) {
		return fmt.Errorf(
			"Expected %d blobs but got %d",
			len(uploadBlobs),
			len(batchUploadResponse.BlobNames),
		)
	}
	for i, blobName := range batchUploadResponse.BlobNames {
		if blobName != *(uploadBlobs[i].BlobName) {
			return fmt.Errorf(
				"Blob name %s does not match expected name %s",
				blobName,
				*(uploadBlobs[i].BlobName),
			)
		}
	}

	return nil
}

// Returns the missing blobs from the given list of blobs, via the provided missingBlobsChan
// channel. This function handles batching calls to the underlying /find-missing endpoint.
func findMissingBlobs(
	augmentClient *augmentclient.Client,
	blobNames []string,
	missingBlobsChan chan<- string,
) (missingBlobsCount uint32, err error) {
	missingBlobsCount = 0
	model := ""
	for batchStart := 0; batchStart < len(blobNames); batchStart += findMissingMaxBlobs {
		batchEnd := min(batchStart+findMissingMaxBlobs, len(blobNames))
		blobNamesBatch := blobNames[batchStart:batchEnd]

		findMissingRequest := publicpb.FindMissingRequest{
			Model:          &model,
			MemObjectNames: blobNamesBatch,
		}
		findMissingResponse, err, _ := augmentClient.FindMissing(&findMissingRequest)
		if err != nil {
			return missingBlobsCount, err
		}

		for _, blob := range findMissingResponse.UnknownMemoryNames {
			missingBlobsChan <- blob
		}
		missingBlobsCount += uint32(len(findMissingResponse.UnknownMemoryNames))
	}

	return missingBlobsCount, nil
}

// Returns the nonindexed blobs from the given list of blobs. This function handles batching calls
// to the underlying /find-missing endpoint.
func getNonindexedBlobs(
	augmentClient *augmentclient.Client,
	blobNames []string,
) (nonindexedBlobs []string, err error) {
	nonindexedBlobs = make([]string, 0)
	model := ""
	for batchStart := 0; batchStart < len(blobNames); batchStart += findMissingMaxBlobs {
		batchEnd := min(batchStart+findMissingMaxBlobs, len(blobNames))
		blobNamesBatch := blobNames[batchStart:batchEnd]

		findMissingRequest := publicpb.FindMissingRequest{
			Model:          &model,
			MemObjectNames: blobNamesBatch,
		}
		findMissingResponse, err, _ := augmentClient.FindMissing(&findMissingRequest)
		if err != nil {
			return nil, err
		}

		nonindexedBlobs = append(nonindexedBlobs, findMissingResponse.NonindexedBlobNames...)
	}

	return nonindexedBlobs, nil
}

// Checkpoint the given blobs and check that the resulting checkpoint's id matches the provided
// expectedCheckpointId. Returns an error if checkpointing fails or the returned checkpoint id
// does not match the expected value.
func checkpointBlobsAndCheck(
	augmentClient *augmentclient.Client,
	blobNames []string,
	expectedCheckpointId string,
) error {
	currCheckpointId := ""
	for batchStart := 0; batchStart < len(blobNames); batchStart += checkpointBlobsMaxBlobs {
		batchEnd := min(batchStart+checkpointBlobsMaxBlobs, len(blobNames))
		blobNamesBatch := blobNames[batchStart:batchEnd]
		request := publicpb.CheckpointBlobsRequest{
			Blobs: &publicpb.Blobs{
				AddedBlobs: blobNamesBatch,
			},
		}
		if currCheckpointId != "" {
			request.Blobs.CheckpointId = &currCheckpointId
		}

		response, err, _ := augmentClient.CheckpointBlobs(&request)
		if err != nil {
			return err
		}
		currCheckpointId = response.NewCheckpointId
	}

	// It's important that the checkpoint name matches what we expect, since the checkpoint ids
	// we'll send in requests come from the test config and not the server. The only way it should
	// be possible for these values to not match is if we gave the wrong list of blobs for the
	// checkpoint.
	if currCheckpointId != expectedCheckpointId {
		return fmt.Errorf(
			"Checkpoint response id %s does not match expected id %s",
			currCheckpointId,
			expectedCheckpointId,
		)
	}
	return nil
}

package telemetry

// This file sets up the OpenTelementry environment for load testing

import (
	"context"
	"errors"

	gcmexporter "github.com/GoogleCloudPlatform/opentelemetry-operations-go/exporter/metric"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutmetric"
	apimetric "go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/metric/metricdata"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
)

const (
	DefaultExecutableName = "load_test"
)

// OtelEnv is a struct that holds the OpenTelemetry environment for load testing
type OTelEnv struct {
	// Identifies the resource we are collecting metrics for. A Resource
	// contains information about the entity that is generating telemetry data.
	// In this case, the resource is the load testing executable
	Resource *resource.Resource
	// Dependency-injected MeterProvider for OTel API users
	MeterProvider apimetric.MeterProvider
	// Exports metrics to some downstream consumer, e.g. stdout, prometheus, GCM, etc.
	Exporters []metric.Exporter
	// Coalesces metrics, measurements, etc. from instruments. When reading, readers trigger
	// collection cycles from meters/instruments. These collected metrics can be
	// then exported by OTel exporters
	Reader metric.Reader
}

// Sets up the OpenTelemetry (OTel) environment for load testing by setting up:
//   - The global MeterProvider that is dependency-injected for the OTel API to use across the
//     application. A MeterProvider is a factory for creating meters, which are logical containers
//     for instruments, and represent a concrete implementation of the OTel API.
//   - The stdout exporter to export metrics to stdout
//   - A manual reader to read information from instruments and export on teardown
//
// Because OTel generally operates in API-land, the only time concrete instances
// are created is during runtime at the application level. Library authors with OTel telemetry
// do not need to create concrete instances, because any runnable binary that wants to use
// OTel will create + dependency inject them. This function performs said dependency injection.
func SetupOTelEnv(options ...OTelEnvOption) *OTelEnv {
	// Set up default OpenTelemetry environment
	otelEnv := &OTelEnv{
		Exporters: []metric.Exporter{},
	}
	WithExecutableResource(DefaultExecutableName)(otelEnv)
	WithManualReader()(otelEnv)
	WithSummaryLogExporter(
		WithAttrGroup(), // No group means all datapoints in summary
		WithAttrGroup("augmentclient.http.response.status_code"),
	)(otelEnv)
	// WithStdOutExporter(stdoutmetric.WithPrettyPrint())(otelEnv)
	// WithGcmExporter(gcmexporter.WithProjectID("system-services-dev"))(otelEnv)

	// Apply any user-specific options
	for _, option := range options {
		option(otelEnv)
	}

	// If a user has not specified the global meter provider,
	// initialize it here
	if otelEnv.MeterProvider == nil {
		withMeterProvider(
			metric.NewMeterProvider(
				metric.WithResource(otelEnv.Resource),
				metric.WithReader(otelEnv.Reader),
				WithCounterHistogramView("augmentclient.http.client.duration"),
				WithNoOpView("augmentclient.http.client.duration"),
			),
		)(otelEnv)
	}
	otel.SetMeterProvider(otelEnv.MeterProvider)
	return otelEnv
}

// Gathers all of the metrics from instruments and exports them
func (env *OTelEnv) CollectAndExport() error {
	log.Info().Msg("Metrics collection started")
	ctx := context.Background()

	// Collect metrics from all instruments
	metrics := &metricdata.ResourceMetrics{}
	err := env.Reader.Collect(ctx, metrics)
	if err != nil {
		return err
	}

	// Export all metrics gathered. The specific exporters used are defined
	// during the initialization process. By default, this is exported to stdout
	var errs error
	for _, exp := range env.Exporters {
		errs = errors.Join(errs, exp.Export(ctx, metrics))
	}
	if errs != nil {
		return errs
	}

	log.Info().Msg("Metrics export finished")
	return nil
}

// Teardown shuts down the OpenTelemetry environment and exports
// all of the collected metrics
func (env *OTelEnv) Teardown() {
	// TODO(Eric) - pass down context from main
	ctx := context.Background()

	defer func() {
		var errs error = env.Reader.Shutdown(ctx)
		for _, exp := range env.Exporters {
			errs = errors.Join(errs, exp.Shutdown(ctx))
		}
		handleSetupTeardownError(errs)
	}()

	handleSetupTeardownError(env.CollectAndExport())
}

// Default error handling during setup is to panic. If anything fails in setup, there is likely
// something wrong with the broader runtime environment of load testing, so execution should
// terminate noisily and this error fixed.
func handleSetupTeardownError(err error) {
	if err != nil {
		panic(err)
	}
}

// Constructor options for OTelEnv
type OTelEnvOption func(*OTelEnv)

// ==========================
// Public constructor options
// ==========================
func WithExecutableResource(execName string, opts ...attribute.KeyValue) OTelEnvOption {
	opts = append(opts, semconv.ProcessExecutableNameKey.String(execName))

	res, err := resource.New(
		context.Background(),
		resource.WithAttributes(opts...),
	)
	handleSetupTeardownError(err)
	return withResource(res)
}

func WithStdOutExporter(options ...stdoutmetric.Option) OTelEnvOption {
	exp, err := stdoutmetric.New(
		options...,
	)
	handleSetupTeardownError(err)
	return WithExporter(exp)
}

func WithManualReader() OTelEnvOption {
	return withReader(metric.NewManualReader())
}

// WithGcmExporter sets up an OTel exporter that exports metrics to Google Cloud Monitoring
// with a managed Prometheus instance. This means all of these metrics will show up
// alongside the rest of the metrics in the GCM console.
func WithGcmExporter(options ...gcmexporter.Option) OTelEnvOption {
	exp, err := gcmexporter.New(options...)
	handleSetupTeardownError(err)
	return WithExporter(exp)
}

func WithSummaryLogExporter(options ...SummaryOption) OTelEnvOption {
	return WithExporter(NewSummaryLogExporter(options...))
}

func WithExporter(exp metric.Exporter) OTelEnvOption {
	return func(env *OTelEnv) {
		env.Exporters = append(env.Exporters, exp)
	}
}

// ===========================
// Private constructor options
// ===========================
func withResource(res *resource.Resource) OTelEnvOption {
	return func(env *OTelEnv) {
		env.Resource = res
	}
}

func withMeterProvider(mp apimetric.MeterProvider) OTelEnvOption {
	return func(env *OTelEnv) {
		env.MeterProvider = mp
	}
}

func withReader(reader metric.Reader) OTelEnvOption {
	return func(env *OTelEnv) {
		env.Reader = reader
	}
}

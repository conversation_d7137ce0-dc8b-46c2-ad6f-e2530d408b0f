load("@io_bazel_rules_go//go:def.bzl", "go_library")

OTEL_API_DEPS = [
    "@io_opentelemetry_go_otel_metric//:metric",
    "@io_opentelemetry_go_otel//:otel",
    "@io_opentelemetry_go_otel//attribute:attribute",
    "@io_opentelemetry_go_otel//semconv/v1.24.0:v1_24_0",
]

OTEL_SDK_DEPS = [
    "@io_opentelemetry_go_otel_sdk//resource:resource",
    "@io_opentelemetry_go_otel_sdk//instrumentation:instrumentation",
    "@io_opentelemetry_go_otel_sdk_metric//:metric",
    "@io_opentelemetry_go_otel_sdk_metric//metricdata:metricdata",
    "@io_opentelemetry_go_otel_exporters_stdout_stdoutmetric//:stdoutmetric",
    "@com_github_googlecloudplatform_opentelemetry_operations_go_exporter_metric//:metric",
]

go_library(
    name = "telemetry",
    srcs = [
        "metricutils.go",
        "summary_exporter.go",
        "telemetry.go",
        "views.go",
    ],
    importpath = "github.com/augmentcode/augment/tools/load_test/telemetry",
    visibility = ["//tools/load_test:__subpackages__"],
    deps = [
        "@com_github_google_uuid//:uuid",
        "@com_github_montanaflynn_stats//:stats",
        "@com_github_rs_zerolog//log",
    ] + OTEL_API_DEPS + OTEL_SDK_DEPS,
)

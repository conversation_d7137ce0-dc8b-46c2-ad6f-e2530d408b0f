package telemetry

import (
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/sdk/metric/metricdata"
)

// ========================
// Grouping data
// ========================

// Represents a collection of metricdata.DataPoint[N] that share a set of attribute key-value pairs
type dataPointGroup[N int64 | float64] struct {
	// The unique attributes that are common to all datapoints in the group.
	// Is not guaranteed to contain ALL shared attributes -- only the ones
	// that were specified when creating this group in the first place
	uniqueAttrs attribute.Set
	// The list of datapoints in the group
	dataPoints []metricdata.DataPoint[N]
}

// Group a list of datapoints by a group of OTel attribute keys. This will group all datapoints
// with the same attribute values for the given keys into a single group
func groupDataByAttrs[N int64 | float64](
	data []metricdata.DataPoint[N],
	keys ...attribute.Key,
) map[attribute.Distinct]*dataPointGroup[N] {
	ret := make(map[attribute.Distinct]*dataPointGroup[N])

	// Create the args key set once for all data points
	keySet := make(map[attribute.Key]struct{}, len(keys))
	for _, k := range keys {
		keySet[k] = struct{}{}
	}

	// Iterate through all the datapoints, get their attribute sets to group by,
	// and add them to the map based on the attribute set unique key
	for _, dp := range data {
		dpAttrSet, _ := dp.Attributes.Filter(func(kv attribute.KeyValue) bool {
			_, ok := keySet[kv.Key]
			return ok
		})
		dpAttrKey := dpAttrSet.Equivalent()

		// Initialize the group if it doesn't exist in the map
		if _, ok := ret[dpAttrKey]; !ok {
			ret[dpAttrKey] = &dataPointGroup[N]{
				uniqueAttrs: dpAttrSet,
				dataPoints:  []metricdata.DataPoint[N]{dp},
			}
		} else { // Add the datapoint to the existing group
			group, _ := ret[dpAttrKey]
			group.dataPoints = append(group.dataPoints, dp)
		}
	}
	return ret
}

// ==========================
// Filtering data
// ==========================

// A function that returns true if a datapoint should be included in a list of datapoints.
type dataPointFilter[N int64 | float64] func(metricdata.DataPoint[N]) bool

// Filters a list of datapoints given a filter function.
// The returned list will contain only datapoints that match the filter.
func filterData[N int64 | float64](
	data []metricdata.DataPoint[N], // The list of datapoints to filter
	matcher dataPointFilter[N], // The filter function that returns true if the datapoint should be included
) []metricdata.DataPoint[N] { // The filtered list of datapoints
	ret := make([]metricdata.DataPoint[N], 0)

	for _, dp := range data {
		if matcher(dp) {
			ret = append(ret, dp)
		}
	}

	return ret
}

// Higher-order function that takes in a list of attribute key-value pairs and returns a filter function
// that returns true if the datapoint contains all of the given key-value pairs.
//
// The composite filter will return true for a datapoint if and only if the following conditions hold true:
// 1. The datapoint contains all of the given attribute keys (case-sensitive)
// 2. The values of each attribute key are exact matches including type
//
// The following are NOT considered in the filter:
// - Any extra attributes that exist in the datapoint but are NOT in `kvs`
// - The order of the attributes in the datapoint
// - The order of the attributes in the filter
func makeKvDataPointFilter[N int64 | float64](kvs ...attribute.KeyValue) func(metricdata.DataPoint[N]) bool {
	kvSet := attribute.NewSet(kvs...)

	// Function will create a new attribute set containing only the specified attribute keys.
	// Then, it will compare the two sets to see if they are equal.
	// Only exact existence equality will match -- the order of the attributes does not matter.
	// However, missing attributes will not match.
	return func(dp metricdata.DataPoint[N]) bool {
		dpAttrSet, _ := dp.Attributes.Filter(func(kv attribute.KeyValue) bool {
			return kvSet.HasValue(kv.Key)
		})
		return kvSet.Equals(&dpAttrSet)
	}
}

// Exclude samples with the given attribute key-value pairs.
// If a sample does not contain all of the attribute key, it is excluded.
func filterByAttrs[N int64 | float64](
	data []metricdata.DataPoint[N],
	kvs ...attribute.KeyValue,
) []metricdata.DataPoint[N] {
	return filterData(data, makeKvDataPointFilter[N](kvs...))
}

// ==========================
// Primitive value extraction
// ==========================

// Get all of the raw values from a list of datapoints.
// This returns a primitive type of either int64 or float64
func getRawValues[N int64 | float64](data []metricdata.DataPoint[N]) []N {
	ret := make([]N, len(data))
	for i, dp := range data {
		ret[i] = dp.Value
	}
	return ret
}

// Get raw values from datapoints and cast them to N. This is a utility function for the
// getRawValues that allows for interoperability between the types.
func getRawValuesAs[N int64 | float64, T int64 | float64](data []metricdata.DataPoint[N]) []T {
	ret := make([]T, len(data))
	for i, dp := range data {
		t := T(dp.Value)
		ret[i] = t
	}
	return ret
}

package telemetry

import (
	"context"
	"fmt"
	"strings"

	"github.com/montanaflynn/stats"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/stdout/stdoutmetric"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/metric/metricdata"
)

// A slice of `attribute.Key` that is used to group metrics
type attrKeyGroup []attribute.Key

// SummaryLogExporter is an OTel exporter implementation that prints a summary of the metrics
// passed into the `Export` function. This is useful for CLI-level reporting or other types
// of reporting where the consumption point is not telemetry infrastructure, but rather terminal
// output or other non-telemetry outputs.
type SummaryLogExporter struct {
	metric.Exporter // inherit behavior from another exporter

	// Slice of attrKeyGroups for grouping during summary export.
	// Each attrKeyGroup will generate to a list of summaries, where each summary
	// corresponds to a unique set of attribute values for the given attrKeyGroup.
	attrGroupsToExport []attrKeyGroup
}

type SummaryOption func(*SummaryLogExporter)

// Constructor for SummaryLogExporter
func NewSummaryLogExporter(options ...SummaryOption) *SummaryLogExporter {
	base, err := stdoutmetric.New()
	handleSetupTeardownError(err)

	ret := &SummaryLogExporter{Exporter: base}
	for _, option := range options {
		option(ret)
	}
	return ret
}

// WithAttrGroup allows the user to specify a list of attribute keys that should be used to group
// datapoints in the summary. As an example, to group by `augmentclient.name`:
//
// exp := NewSummaryLogExporter(WithAttrGroup("augmentclient.name"))
//
// For the Augment client, we follow the OpenTelemetry semconv spec, which you can find
// documented here: https://opentelemetry.io/docs/specs/semconv/. We prepend all names
// there with `augmentclient.<rest of semconv name>`. Please see `client/golang/metrics.go`
// for more information.
func WithAttrGroup(attrGroupsToExport ...attribute.Key) SummaryOption {
	return func(se *SummaryLogExporter) {
		se.attrGroupsToExport = append(se.attrGroupsToExport, attrGroupsToExport)
	}
}

// ========================================
// metric.Exporter interface implementation
// ========================================
func (se *SummaryLogExporter) Export(ctx context.Context, result *metricdata.ResourceMetrics) error {
	// Search through results in pipeline
	for _, sm := range result.ScopeMetrics {
		for _, m := range sm.Metrics {
			log.Info().Msgf("Metric found: %s", m.Name)
			if m.Name == "augmentclient.http.client.duration" {
				// TODO(Eric): Use a `io.Writer` instead of `fmt.Println`
				fmt.Println(">>>> Metric Summary for", m.Name, "<<<<<")
				for _, attrGroup := range se.attrGroupsToExport {
					s, err := LogMetricSummary(&m, attrGroup...)
					if err != nil {
						log.Error().Err(err).Msg("Error exporting summary")
					}
					fmt.Println(s)
				}
			}
		}
	}

	return nil
}

// ======================================
// metric.Exporter interface pass-through
// ======================================
//
// These functions pass through all calls to the underlying exporter.
func (se *SummaryLogExporter) Temporality(ik metric.InstrumentKind) metricdata.Temporality {
	return se.Exporter.Temporality(ik)
}

func (se *SummaryLogExporter) Aggregation(ik metric.InstrumentKind) metric.Aggregation {
	return se.Exporter.Aggregation(ik)
}

func (se *SummaryLogExporter) ForceFlush(ctx context.Context) error {
	return se.Exporter.ForceFlush(ctx)
}

func (se *SummaryLogExporter) Shutdown(ctx context.Context) error {
	return se.Exporter.Shutdown(ctx)
}

// ===========================
// Logging and formatting data
// ===========================

// Metric summary dispatcher for the different types of metrics
// that we have. Uses type assertion to switch between the different supported
// datatypes. Note that OTel only supports float64 and int64 types.
func LogMetricSummary(m *metricdata.Metrics, keys ...attribute.Key) (s string, err error) {
	switch a := m.Data.(type) {
	case metricdata.Sum[float64]:
		return logSummaryByAttrs(m.Name, a.DataPoints, keys...)
	case metricdata.Sum[int64]:
		return logSummaryByAttrs(m.Name, a.DataPoints, keys...)
	default:
		err := fmt.Errorf("Metric type not supported: %T", a)
		return "", err
	}
}

// Generates a summary string for a list of datapoints
func getSummaryString[N int64 | float64](data []metricdata.DataPoint[N]) (string, error) {
	rawValues := getRawValuesAs[N, float64](data)
	// TODO(Eric): Make these percentiles configurable
	desc, err := stats.Describe(rawValues, false, &[]float64{50, 75, 90, 95, 99})
	if err != nil {
		return "", err
	}
	return desc.String(3), nil
}

// Print summary for each group of datapoints with the same attributes
func logSummaryByAttrs[N int64 | float64](
	metricName string,
	datapoints []metricdata.DataPoint[N],
	keys ...attribute.Key,
) (string, error) {
	s := strings.Builder{}
	// Iterate over all of the groups of datapoints with the same attribute kv pairs
	for _, dpg := range groupDataByAttrs(datapoints, keys...) {
		summary, err := getSummaryString(dpg.dataPoints)
		if err != nil {
			return "", err
		}

		s.WriteString(fmt.Sprintln(dpg.uniqueAttrs.MarshalLog()))
		s.WriteString(fmt.Sprintln(indent(summary)))
	}
	return s.String(), nil
}

// Indents an entire string by 1 tab
func indent(s string) string {
	return "\t" + strings.ReplaceAll(s, "\n", "\n\t")
}

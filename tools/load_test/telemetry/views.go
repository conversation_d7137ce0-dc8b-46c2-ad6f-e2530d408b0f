package telemetry

import (
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/sdk/metric"
)

// Takes a counter value and creates a histogram view of it.
// Note that this will remove all attributes from the histogram
func WithCounterHistogramView(name string) metric.Option {
	newName := name + "-hist"
	return metric.WithView(
		metric.NewView(
			metric.Instrument{Name: name, Kind: metric.InstrumentKindCounter},
			metric.Stream{
				Name: newName,
				// Remove all attribute keys from the histogram
				AttributeFilter: attribute.NewAllowKeysFilter(),
				Aggregation: metric.AggregationExplicitBucketHistogram{
					Boundaries: []float64{1, 2, 5, 10, 20, 50, 100, 200, 500, 1000, 2000, 5000, 10000},
				},
			},
		),
	)
}

// Pass through an instrument directly to output with no modifications
func WithNoOpView(name string) metric.Option {
	return metric.WithView(
		metric.NewView(
			metric.Instrument{Name: name},
			metric.Stream{Name: name},
		),
	)
}

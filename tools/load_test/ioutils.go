package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net/url"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	gax "github.com/googleapis/gax-go/v2"
	"github.com/rs/zerolog/log"
)

const (
	gsScheme   = "gs"
	fileScheme = "file"
)

// Given a bucket and object key, load the object's attributes from GCS.
func GetAttrsFromGCS(bucket, object string) (*storage.ObjectAttrs, error) {
	log.Debug().Msgf("Getting attrs from GCS: bucket=%s, object=%s", bucket, object)

	// TODO(eric): With more complex setup/teardown, we should handle context.Context
	// synchronization at top-level of control flow and pass it down
	ctx := context.Background()

	// Creates a client
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCS client: %w", err)
	}
	client.SetRetry(
		storage.WithMaxAttempts(10),
		storage.WithBackoff(gax.Backoff{
			Initial:    time.Millisecond * time.Duration(500),
			Max:        time.Second * time.Duration(10),
			Multiplier: 2,
		}),
	)

	// Read attrs from GCS.
	attrs, err := client.Bucket(bucket).Object(object).Attrs(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to read attrs for %s from GCS: %w", object, err)
	}
	return attrs, nil
}

// Given a bucket and object key, load the object from GCS.
func LoadFromGCS(bucket, object string) (io.ReadCloser, error) {
	log.Debug().Msgf("Loading from GCS: bucket=%s, object=%s", bucket, object)

	// TODO(eric): With more complex setup/teardown, we should handle context.Context
	// synchronization at top-level of control flow and pass it down
	ctx := context.Background()

	// Creates a client
	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCS client: %w", err)
	}
	client.SetRetry(
		storage.WithMaxAttempts(10),
		storage.WithBackoff(gax.Backoff{
			Initial:    time.Millisecond * time.Duration(500),
			Max:        time.Second * time.Duration(10),
			Multiplier: 2,
		}),
	)

	// Gets a reader from GCS
	rc, err := client.Bucket(bucket).Object(object).NewReader(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to read %s from GCS: %w", object, err)
	}
	return rc, nil
}

// Load a file from a URI. Returns either an object that supports both
// the io.Reader and io.Closer interfaces, or an error.
func LoadFromUri(uripath string) (io.ReadCloser, error) {
	u, err := url.Parse(uripath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse URI: %w", err)
	}

	switch u.Scheme {
	case gsScheme: // Google Cloud Storage
		// Host is the bucket name, path is GCS object path without leading slash
		bucket, object := u.Host, strings.TrimLeft(u.Path, "/")
		return LoadFromGCS(bucket, object)
	case fileScheme, "": // Filesystem file
		return os.Open(u.Path)
	default:
		return nil, fmt.Errorf("could not find resource at URI: %s", uripath)
	}
}

// Read a line from a bufio.Reader. The advantage of this function over a scanner is that it handles
// long lines better.
func ReadLine(reader *bufio.Reader) ([]byte, error) {
	var (
		isPrefix     bool  = true
		err          error = nil
		line, result []byte
	)
	for isPrefix && err == nil {
		line, isPrefix, err = reader.ReadLine()
		result = append(result, line...)
	}
	return result, err
}

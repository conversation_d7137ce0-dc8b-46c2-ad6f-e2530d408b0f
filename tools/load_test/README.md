# Load testing

This directory contains the code for running load tests against an arbitrary target. Currently only
end-to-end tests of the `/completion` and `/next_edit_stream` endpoints are supported, but additional endpoints will be added
in the future.

Example usage:
```
bazel run //tools/load_test:load_test -- -target="https://dev-jacqueline.us-central.api.augmentcode.com" -config-file="tools/load_test/configs/completions_test_configs/hello_world.jsonnet" -auth-token-file="/home/<USER>/.augment/token"
```

## Terminology

* User: A single thread sending a stream of requests. These are called users because they're
  emulating a real-world user (and when replaying real-world data their requests probably did come
  from a single real-world user). Requests from a user all share the same session ID.

## Configuration

Load tests are configured via two sets of files:
1. Test configuration files (jsonnet), which contain a list of users. These are checked into git
   under `tools/load_test/configs`.
2. User configuration files (newline-delimited JSON), which contain one line of metadata (currently
   nothing, but we anticipate this being useful for things like the list of blobs we'll need for a
   test), followed by a series of requests. These are pointed to by the test configuration files.
   These may be checked into git, but are more likely in the `augment-load-testing-configs` GCS
   bucket (in the `system-services-dev` project).

The format for these JSON configurations is defined by `load_test.proto`.

For example, here is a minimal configuration for test that will send 5 "hello world" requests at
1 request per second:

Test configuration:
```json
{
  "id": "hello_world",
  "users": [
    {
      "id": "hello_world_user",
      "request_config_path": "tools/load_test/configs/users/hello_world_user.jsonl",
      "constant_throughput_config": {
        "throughput": 1
      }
    }
  ]
}
```

User configuration:
```json
{}
{"completion": {"prompt": "def hello_world():\n", "blobs": {}}}
{"completion": {"prompt": "def hello_world():\n", "blobs": {}}}
{"completion": {"prompt": "def hello_world():\n", "blobs": {}}}
{"completion": {"prompt": "def hello_world():\n", "blobs": {}}}
{"completion": {"prompt": "def hello_world():\n", "blobs": {}}}
```

### Timing configurations

In the example above, the test configuration specifies a `constant_throughput_config` for the user.
There is also a `realistic_timing_config`, option which will replay the real-world timings of
requests from a user. This setup requires every request in the user configuration to specify
`offset_millis` (the time since the start of the test, in milliseconds). See `load_test.proto` for
details.

When you use the realistic timing option, it's recommended to run the test with the
`-stop-on-first-user` flag, so that the test results aren't skewed by running with fewer and fewer
users as time passes.

### Request overrides

You can override the request parameters for a test by adding a `completionOverride` field to the
test configuration. For example, if you wanted to alter the hello_world configuration above to use
a model other than the default for your target, you would do the following:
```json
{
  "id": "hello_world",
  "users": [
    {
      "id": "hello_world_user",
      "request_config_path": "tools/load_test/configs/users/hello_world_user.jsonl",
      "throughput": 1,
    }
  ],
  "completionOverride": {
    "model": "MODEL_NAME"
  }
}
```

Note that you can also override the model with the `-completion-model` commandline flag.


### Generating data

This is how you can generate data for load testing the `/next_edit_stream` endpoint.

```bash
#!/bin/bash

    python /home/<USER>/augment/tools/load_test/configs/scripts/generate_next_edit_load_test_data.py \
        --start-date="2024-10-23" \
        --end-date="2024-10-24" \
        --user-limit=3 \
        --requests-per-user-limit=3 \
        --use-realistic-timing \
        --store-on-gcs \

```


### Running the next edit stream load test

This is how you can run the load test for the `/next_edit_stream` endpoint.

```bash
#!/bin/bash
bazel run //tools/load_test:load_test -- \
 -target="https://dev-edvin.us-central.api.augmentcode.com" \
 -config-file="/home/<USER>/augment/tools/load_test/configs/next_edit_test_configs/nextEditHostRequests_2024_10_16-2024_10_18.json" \
 -auth-token-file="/home/<USER>/.config/augment/api_token" \
 ```

syntax = "proto3";

package load_test;

import "services/api_proxy/public_api.proto";

option go_package = "github.com/augmentcode/augment/services/load_test/proto";

// Configuration for a load test.
message TestConfig {
  // Human-readable identifier for this test.
  string id = 1;

  // List of users to simulate.
  repeated UserConfig users = 2;

  // Override all users' completion requests with any fields populated in this completion request.
  // For example, overriding `model` allows you to test a model other than the default for this
  // test.
  public_api.CompletionRequest completion_override = 3;

  // Override all users' next edit requests with any fields populated in this next edit request.
  public_api.NextEditRequest next_edit_override = 4;
}

// Configuration for a "user". A user is just a single thread sending a stream of requests.
// This configuration is specified in a test configuration file.
message UserConfig {
  // Configuration for a user sending at a constant throughput.
  message ConstantThroughputConfig {
    float throughput = 1;
  }

  // Configuration for a user replaying the timing of their real-world requests. When specified,
  // the test will use `Request.offset_millis` to determine the interval between requests. It's
  // up to the request export to determine these timings; the test will just blindly replay them.
  message RealisticTimingConfig {
    // The maximum interval between requests, in milliseconds. This can be used to prevent a
    // user from sleeping for too long (and therefore retain some density of requests), if the
    // data has a large gap between requests.
    uint64 max_request_interval_ms = 1;
  }

  // Unique identifier for this user. (Probably human-readable, but TBD.)
  string id = 1;

  // Path to a newline-delimited JSON file of requests.
  string request_config_path = 2;

  oneof timingConfig {
    // If specified, send this user's requests at the specified throughput.
    ConstantThroughputConfig constant_throughput_config = 3;

    // If specified, send requests according to their real-world timing, as determined by
    // `Request.offset_millis`.
    RealisticTimingConfig realistic_timing_config = 4;
  }
}

// Metadata about a series of requests, to save the test runner from needing to do a lot of
// preprocessing. This is always the first line of a user's requests file.
message RequestsMetadata {
  // De-duped list of all blobs used across a user's requests. These blobs are uploaded before
  // the test is run to ensure the starting state of the test deployment contains necessary data
  repeated string blob_names = 1;

  // De-duped list of all checkpoint ids across a user's requests. All blobs from these
  // checkpoints are uploaded before the test is run
  repeated string checkpoint_ids = 2;
}

// A single request to send to the server.
message Request {
  // The offset in milliseconds from the start of the test.
  uint64 offset_millis = 1;

  oneof request {
    public_api.CompletionRequest completion = 2;
    public_api.NextEditRequest next_edit = 3;
  }
}

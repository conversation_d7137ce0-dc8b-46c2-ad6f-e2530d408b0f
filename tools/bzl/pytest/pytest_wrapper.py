"""A wrapper to pass setup the pytest run correctly based on the Bazel test configuration."""

import argparse
import logging
import os
import pathlib
import sys
import threading
import time

import debugpy
import pytest


def timestamp_prepender(fileno, out_fileno):
    """Prepends a timestamp to each line of the stream."""
    stream = os.fdopen(fileno, "rb")
    outstream = os.fdopen(out_fileno, "wb")

    while line := stream.readline():
        ts = time.strftime("%Y-%m-%d %H:%M:%S ", time.gmtime())
        outstream.write(ts.encode("utf-8"))
        outstream.write(line)
        outstream.flush()


def main():
    # see https://bazel.build/reference/test-encyclopedia for details
    os.environ["BAZEL_TEST"] = "1"
    shard_id = os.environ.get("TEST_SHARD_INDEX")
    num_shards = os.environ.get("TEST_TOTAL_SHARDS")
    parser = argparse.ArgumentParser()
    parser.add_argument("--timeout")
    args, rest = parser.parse_known_args()

    cmd_args = []
    if num_shards:
        cmd_args += ["--shard-id", shard_id, "--num-shards", num_shards]
    if args.timeout is None:
        timeout = os.environ.get("TEST_TIMEOUT")
        if timeout:
            inner_timeout = str(int(timeout) - 30)
            cmd_args += ["--timeout", inner_timeout]
    else:
        cmd_args += ["--timeout", args.timeout]

    output_file = os.environ.get("XML_OUTPUT_FILE")
    if output_file:
        cmd_args += [f"--junitxml={output_file}"]
    cmd_args += rest

    # ensure that the cache directory is WITHIN the confines of the sandbox
    os.environ["XDG_CACHE_HOME"] = str(
        pathlib.Path(os.environ["TEST_TMPDIR"], ".cache")
    )

    if os.environ.get("DEBUGPY"):
        debugpy.listen(5678)
        print(
            "Now is a good time to attach your debugger: Run: Python: Attach",
            flush=True,
        )
        debugpy.wait_for_client()

    if os.environ.get("HOME") is None:
        os.environ["TRITON_CACHE_DIR"] = str(
            pathlib.Path(os.environ["TEST_TMPDIR"], ".triton", "cache")
        )
        os.environ["TRITON_DUMP_DIR"] = str(
            pathlib.Path(os.environ["TEST_TMPDIR"], ".triton", "dump")
        )
        os.environ["TRITON_OVERRIDE_DIR"] = str(
            pathlib.Path(os.environ["TEST_TMPDIR"], ".triton", "override")
        )

    # setup logging so that log statements from a test are visible
    logging.basicConfig(level=logging.INFO)

    if sys.platform == "darwin":
        # On macOS, we can't use the pipe trick, so we just run pytest directly
        # os.pipe2 is not available on macOS
        sys.exit(pytest.main(cmd_args))
    else:
        # Set up a pipe for us to read stuff written to stdout, stderr
        (stdout_read, stdout_write) = os.pipe2(os.O_CLOEXEC)
        (stderr_read, stderr_write) = os.pipe2(os.O_CLOEXEC)

        # save the old stdout and stderr descriptors before we change them to pipe
        orig_stdout_fileno = os.dup(sys.stdout.fileno())
        orig_stderr_fileno = os.dup(sys.stderr.fileno())

        # Start the timestamp prependers
        stdout_thread = threading.Thread(
            target=timestamp_prepender,
            args=(stdout_read, orig_stdout_fileno),
            daemon=True,
        )

        stderr_thread = threading.Thread(
            target=timestamp_prepender,
            args=(stderr_read, orig_stderr_fileno),
            daemon=True,
        )

        stdout_thread.start()
        stderr_thread.start()

        # Flush before redirecting stdout and stderr to pipe
        sys.stdout.flush()
        sys.stderr.flush()

        # Redirect stdout and stderr to pipe
        os.dup2(stdout_write, sys.stdout.fileno())
        os.close(stdout_write)

        os.dup2(stderr_write, sys.stderr.fileno())
        os.close(stderr_write)

        try:
            sys.exit(pytest.main(cmd_args))
        finally:
            # Flush before restoring stdout and stderr
            sys.stdout.flush()
            sys.stderr.flush()

            # Restore original stdout and stderr FDs
            #
            # This should also close the write side of the pipe,
            # causing the readers to exit, unless there are some
            # other dups hanging around.
            os.dup2(orig_stdout_fileno, sys.stdout.fileno())
            os.dup2(orig_stderr_fileno, sys.stderr.fileno())

            # The threads may not exit if there is some other descriptor open
            # on the write side of the pipes (e.g. an unterminated subprocess).
            #
            # The timeout is a trade-off - we risk losing output if we don't
            # wait for the threads to exit, but we don't want to wait forever.
            stdout_thread.join(timeout=4)
            stderr_thread.join(timeout=4)

            if stdout_thread.is_alive() or stderr_thread.is_alive():
                print(
                    """
        The timestamp threads did not exit quickly. This can happen sporadically due
        to thread scheduling issues, but if it happens a lot, we may have to fix the
        shutdown logic to avoid losing output and unnecessarily adding delay to tests.
        """
                )


# if using 'bazel test ...'
if __name__ == "__main__":
    main()

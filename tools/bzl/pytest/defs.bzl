load("@aspect_rules_py//py:defs.bzl", "py_test")
load("@python_pip//:requirements.bzl", "requirement")

def pytest_test(name, srcs, deps = [], args = [], data = [], extra_srcs = [], tb = "auto", show_locals = True, **kwargs):
    """
        Rule to call py.test on all srcs files

        Args:
            extra_srcs: Python source files that are added to the test binary, but pytest isn't called on them.
            tb: Pytest traceback option (see https://docs.pytest.org/en/7.1.x/how-to/output.html)
            show_locals: show local variables in tracebacks (see https://docs.pytest.org/en/7.1.x/how-to/output.html)
    """

    pytest_args = [
        "-p",
        "no:warnings",
        "-v",
        "--no-header",
        "--tb={}".format(tb),
    ]
    if show_locals:
        pytest_args.append("-l")
    pytest_args.append("-s")

    py_test(
        name = name,
        srcs = [
            "//tools/bzl/pytest:pytest_wrapper.py",
        ] + srcs + extra_srcs,
        main = "pytest_wrapper.py",
        args = pytest_args + args + ["$(location :%s)" % x for x in srcs],
        deps = deps + [
            requirement("pytest"),
            requirement("pytest-shard"),
            requirement("pytest-timeout"),
            requirement("debugpy"),
        ],
        data = data,
        **kwargs
    )

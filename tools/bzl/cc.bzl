load("@rules_proto_grpc_cpp//:defs.bzl", "cpp_proto_library")

def cc_gpu_test(
        name,
        tags = [],
        **kwargs):
    """Variant of cc_test for GPU tests.

        GPU tests need to be linked statically, which
        is the default for all cc_binaries, but not cc_test.
    """
    native.cc_test(
        name = name,
        linkstatic = True,
        tags = ["gpu"] + tags,
        **kwargs
    )

def cc_proto_library(
        name,
        deps,
        extra_deps = [],
        **kwargs):
    cpp_proto_library(
        name = name,
        protos = deps,
        deps = extra_deps,
        **kwargs
    )

# macro for pytorch extensions using C++/CUDA
#
# based on https://discuss.pytorch.org/t/pytorch-and-bazel/42592
load("@rules_cuda//cuda:defs.bzl", "cuda_library")
load("@pybind11_bazel//:build_defs.bzl", "pybind_extension")

PYBIND_FEATURES = [
    "-use_header_modules",  # Required for pybind11.
    "-parse_headers",
]

def pytorch_cpp_extension(
        name,
        srcs = [],
        cuda_srcs = [],
        deps = [],
        copts = [
        ],
        cuda_copts = [],
        defines = [],
        ptxasopts = [],
        linkopts = ["-Wl,-Bsymbolic"],
        tags = [],
        **kwargs):
    """Create a pytorch cpp extension as a cpp and importable python library.

    Args:
        name: name of the extension. "name.so" should be added to a py_binary/py_libary under "data"
        srcs: C++ sources
        cuda_srcs: CUDA C++ sources
        deps: extra dependencies (added to cc_bindary for srcs and cuda_library for cuda_srcs)
        copts: copts forward to cuda_library and cc_library calls
        ptxaopts: PTX options forwarded to cuda_library
        linkopts: linker options forwarded to cuda_library and cc_library
        defines: defines options forwarded to cuda_library and cc_Library
        tags: tags forwarded to cuda_library and cc_library ("no-clang-tidy" is added automatically)

    """
    torch_deps = [
        "@libtorch_2_5_1_archive//:torch",
        "@remote_cuda_toolchain//:nvjitlink_12_so",
        "@remote_cuda_toolchain//:nvjitlink_12_4_127_so",
    ]
    copts = copts + [
        "-fPIC",
        "-D_GLIBCXX_USE_CXX11_ABI=0",
        "-DTORCH_API_INCLUDE_EXTENSION_H",
        "-fno-strict-aliasing",
        "-fstack-protector-strong",
        "-fwrapv",
        "-DTORCH_EXTENSION_NAME=" + name,
    ]

    cuda_deps = []
    if cuda_srcs:
        cuda_library(
            name = name + "_cuda",
            srcs = cuda_srcs,
            deps = deps + torch_deps + ["@pybind11"],
            copts = copts + cuda_copts + ["-fvisibility=hidden"],
            defines = defines,
            ptxasopts = ptxasopts,
            linkopts = linkopts,
            tags = ["no-clang-tidy"] + tags,
        )
        cuda_deps.extend([":" + name + "_cuda"])

    pybind_extension(
        name = name,
        srcs = srcs,
        deps = deps + cuda_deps + torch_deps + ["@rules_cuda//cuda:runtime"],
        copts = copts,
        linkopts = linkopts,
        defines = defines,
        tags = ["no-clang-tidy"] + tags,
        **kwargs
    )

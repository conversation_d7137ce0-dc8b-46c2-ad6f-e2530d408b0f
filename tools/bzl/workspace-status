#!/usr/bin/env python3
"""A small tool to generate workspace status information.

In addition, it also performs some environment checks
to ensure that <PERSON><PERSON> is only called in valid setups.
"""

import json
import os
import pathlib


def find_build_user():
    path = pathlib.Path.home().joinpath(".augment", "user.json")
    if path.exists():
        user_data = json.loads(path.read_text(encoding="utf-8"))
        user_name = user_data["name"]
        print(f"BUILD_USER {user_name}")
        print(f"BUILD_USER_NAMESPACE dev-{user_name}")
        return
    if "BAZEL_BUILD_USER" in os.environ:
        user_name = os.environ.get("BAZEL_BUILD_USER")
        print(f"BUILD_USER {user_name}")
    else:
        user_name = os.environ.get("USER", "none")
        print(f"BUILD_USER {user_name}")

    if "BUILD_USER_NAMESPACE" in os.environ:
        namespace = os.environ.get("BUILD_USER_NAMESPACE")
        print(f"BUILD_USER_NAMESPACE {namespace}")
    else:
        print("BUILD_USER_NAMESPACE none")


def vscode_release_version():
    print(
        f"STABLE_VSCODE_RELEASE_VERSION {os.environ.get('STABLE_VSCODE_RELEASE_VERSION', '0.0.0')}"
    )


def main():
    find_build_user()
    vscode_release_version()


if __name__ == "__main__":
    main()

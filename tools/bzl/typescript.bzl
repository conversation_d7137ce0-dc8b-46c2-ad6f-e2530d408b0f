"""TypeScript language rules."""

load("@aspect_rules_ts//ts:proto.bzl", _ts_proto_library = "ts_proto_library")

def ts_proto_library(
        name,
        proto,
        files_to_copy = None,
        copy_files = False,
        **kwargs):
    """Rule for generated protobuf TypeScript libraries.

    Args:
        name: The name of the rule
        proto: The proto_library target to generate the ts_proto_library for
        files_to_copy: The list of output files to generate. If None, the default is used.
        copy_files: Whether to copy the generated files to the source tree.
        **kwargs: Additional arguments to pass to the ts_proto_library

    Usage

        proto_library(
            name = "my_proto",
            srcs = ["my.proto"],
        )

        ts_proto_library(
            name = "my_ts_grpc",
            protos = [":my_proto"],
        )
    """
    if files_to_copy == None:
        proto_prefix = proto.replace("_proto", "").replace(":", "")
        files_to_copy = [
            "{}_pb.d.ts".format(proto_prefix),
            "{}_connect.d.ts".format(proto_prefix),
        ]

    _ts_proto_library(
        name = name,
        proto = proto,
        files_to_copy = files_to_copy,
        copy_files = copy_files,
        **kwargs
    )

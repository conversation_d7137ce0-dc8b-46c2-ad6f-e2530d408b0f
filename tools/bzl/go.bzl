load("@aspect_bazel_lib//lib:write_source_files.bzl", "write_source_files")
load("@aspect_bazel_lib//lib:copy_to_directory.bzl", "copy_to_directory")
load("@aspect_bazel_lib//lib:directory_path.bzl", "make_directory_path")
load("@io_bazel_rules_go//proto:def.bzl", _go_grpc_library = "go_grpc_library", _go_proto_library = "go_proto_library")
load("//tools/bzl:image.bzl", "app_image")
load("@io_bazel_rules_go//go:def.bzl", _go_binary = "go_binary", _go_library = "go_library", _go_test = "go_test")

go_binary = _go_binary
go_library = _go_library
go_test = _go_test

def _write_proto_stubs_to_source(name, target, output_files, out_directory):
    native.filegroup(
        name = name + ".generated_group",
        srcs = [target],
        output_group = "go_generated_srcs",
    )

    copy_to_directory(
        name = name + "_flattened",
        srcs = [name + ".generated_group"],
        root_paths = ["**"],
    )

    write_source_files(
        name = name,
        files = {
            out_directory + "/" + output_file: make_directory_path(output_file + "_directory_path", name + "_flattened", output_file)
            for output_file in output_files
        },
        diff_test = False,
    )

def go_grpc_library(
        name,
        proto,
        output_files = None,
        generated_files = True,
        generate_out_directory = "proto",
        **kwargs):
    """Rule for generated GRPC go libraries.

    Do not use "@rules_proto_grpc//go:defs.bzl's go_grpc_library.
    This addsa target "generate_{name}_stubs" which generates the pb.go files and
    puts them into the source tree under "protos.

    Args:
        name: The name of the rule
        proto: The proto_library target to generate the go_grpc_library for
        output_files: The list of output files to generate. If None, the default is used.
        **kwargs: Additional arguments to pass to the go_grpc_library

    Usage

        proto_library(
            name = "my_proto",
            srcs = ["my.proto"],
        )

        go_grpc_library(
            name = "my_go_grpc",
            protos = [":my_proto"],
        )
    """
    _go_grpc_library(
        name = name,
        proto = proto,
        **kwargs
    )

    if output_files == None:
        proto_prefix = proto.replace("_proto", "").replace(":", "")
        output_files = [
            "{}.pb.go".format(proto_prefix),
            "{}_grpc.pb.go".format(proto_prefix),
        ]

    if generated_files:
        _write_proto_stubs_to_source(
            name = "{}.generate_stubs".format(name),
            output_files = output_files,
            out_directory = generate_out_directory,
            target = name,
        )

def go_proto_library(
        name,
        proto,
        output_files = None,
        generate_out_directory = "proto",
        **kwargs):
    """Rule for generated protobuf go libraries.

    Do not use "@rules_proto_grpc//go:defs.bzl's go_proto_library.
    This addsa target "generate_{name}_stubs" which generates the pb.go files and
    puts them into the source tree under "protos.

    Args:
        name: The name of the rule
        proto: The proto_library target to generate the go_proto_library for
        output_files: The list of output files to generate. If None, the default is used.
        **kwargs: Additional arguments to pass to the go_proto_library

    Usage

        proto_library(
            name = "my_proto",
            srcs = ["my.proto"],
        )

        go_proto_library(
            name = "my_go_grpc",
            protos = [":my_proto"],
        )
    """
    _go_proto_library(
        name = name,
        proto = proto,
        **kwargs
    )

    if output_files == None:
        proto_prefix = proto.replace("_proto", "").replace(":", "")
        output_files = [
            "{}.pb.go".format(proto_prefix),
        ]

    _write_proto_stubs_to_source(
        name = "generate_{}_stubs".format(name),
        output_files = output_files,
        target = name,
        out_directory = generate_out_directory,
    )

def go_oci_image(
        name,
        binary,
        package_name,
        entrypoint = None,
        workdir = None,
        **kwargs):
    "Wrapper around oci_image for go_binary."

    b = (binary[1:] if binary.startswith(":") else binary)
    if not entrypoint:
        if binary.startswith("//"):
            fail("need to specify entrypoint")
        entrypoint = "/" + package_name + "/" + b + "_/" + b
    if not workdir:
        workdir = "/" + package_name + "/" + b + "_/" + b + ".runfiles/_main"

    app_image(
        name = name,
        binary = binary,
        package_name = package_name,
        entrypoint = entrypoint,
        workdir = workdir,
        **kwargs
    )

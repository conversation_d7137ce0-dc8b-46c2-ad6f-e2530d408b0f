# Load the Helm repository rules.
load("//tools/bzl/helm:repository_rules.bzl", "helm_chart", "helm_tool")

def setup_helm():
    # Download the 'helm' tool.
    helm_tool(
        name = "helm_tool",
    )

    helm_chart(
        name = "cert-manager",
        chartname = "cert-manager",
        repo_url = "https://charts.jetstack.io",
        version = "v1.14.4",
    )

    helm_chart(
        name = "reloader",
        chartname = "reloader",
        repo_url = "https://stakater.github.io/stakater-charts",
        version = "v1.0.24",
    )

    helm_chart(
        name = "tempo",
        chartname = "tempo",
        repo_url = "https://grafana.github.io/helm-charts",
        version = "v1.3.1",
    )

    helm_chart(
        name = "keda",
        chartname = "keda",
        repo_url = "https://kedacore.github.io/charts",
        version = "2.12.0",
    )

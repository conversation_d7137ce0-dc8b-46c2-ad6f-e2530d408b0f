"""A wrapper to pass run python linters."""

import argparse
import json
import os
import re
import subprocess
import sys
import tempfile
from typing import List

import bandit


def _run_bandit(files: List[str]):
    # filter out protobuf files as they are never passing
    files = [f for f in files if "pb2" not in f]

    if files:
        bandit_conf = bandit.core.config.BanditConfig(config_file="bandit.yaml")
        profile = {}
        profile["exclude"] = set(bandit_conf.get_option("skips") or [])
        manager = bandit.core.manager.BanditManager(bandit_conf, None, profile=profile)
        manager.baseline_profile = None  # You can set a baseline profile if needed
        manager.files_list = files

        # Run the Bandit linter
        manager.run_tests()
        issues = manager.get_issue_list()

        print("Bandit:")
        for issue in issues:
            print(issue)

        print(len(issues), "issues")
        return len(issues) == 0
    else:
        return True
        # skip if no files are left


def _get_ruff_bin() -> str:
    """Returns the path to the ruff binary."""
    # if darwin, use the arm64 binary
    if sys.platform == "darwin":
        return "../ruff_aarch64-apple-darwin/ruff"
    else:
        return "../ruff_x86_64-unknown-linux-gnu/ruff"


def _run_ruff(files: List[str]):
    files = [f for f in files if "pb2" not in f]

    if files:
        args = [
            _get_ruff_bin(),
            "check",
            "--target-version",
            "py311",
            "--output-format",
            "full",
            "--config",
            ".ruff.toml",
        ]
        args += files
        env = os.environ.copy()
        env["PYTHONPATH"] = ":".join(sys.path)
        with subprocess.Popen(
            args,
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            encoding="utf-8",
            env=env,
        ) as p:
            stdout, stderr = p.communicate()
            print("Ruff:")
            print(re.sub(r"/.*augment/", "", stdout))
            print(re.sub(r"/.*augment/", "", stderr))
            print()
            r = p.wait()
            return r == 0
    else:
        return True
        # skip if no files are left


def _run_pyright(files: List[str], pyright_extra_args: dict):
    # filter out protobuf files as they are never passing
    files = [f for f in files if "pb2" not in f]

    if files:
        args = [
            "tools/bzl/lint/pyright/pyright_/pyright",
            "--pythonversion",
            "3.11",
            "--pythonpath",
            sys.executable,
        ]
        if pyright_extra_args:
            with tempfile.NamedTemporaryFile(
                dir=os.environ["TEST_TMPDIR"], delete=False, suffix=".json"
            ) as tmpfile:
                tmpfile.write(json.dumps(pyright_extra_args).encode("utf-8"))
                tmpfile.flush()
            args += ["-p", tmpfile.name]
        args += files
        env = os.environ.copy()
        env["PYTHONPATH"] = ":".join(sys.path)
        with subprocess.Popen(
            args,
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            encoding="utf-8",
            env=env,
        ) as p:
            stdout, stderr = p.communicate()
            print("Pyright:")
            print(re.sub(r"/.*augment/", "", stdout))
            print(re.sub(r"/.*augment/", "", stderr))
            print()
            r = p.wait()
            return r == 0
    else:
        return True
        # skip if no files are left


def main():
    """Main entry function."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--pyright", action="store_true")
    parser.add_argument("--bandit", action="store_true")
    parser.add_argument("--ruff", action="store_true")
    parser.add_argument("--pyright-extra-args", type=str)
    args, rest = parser.parse_known_args()

    failed = False
    if args.pyright:
        pyright_args = {}
        if args.pyright_extra_args:
            pyright_args = json.loads(args.pyright_extra_args)
        if not _run_pyright(rest, pyright_args):
            print("PYRIGHT failed")
            failed = True
    if args.bandit:
        if not _run_bandit(rest):
            print("BANDIT failed")
            failed = True
    if args.ruff:
        if not _run_ruff(rest):
            print("RUFF failed")
            failed = True
    if failed:
        print(
            "See https://www.notion.so/Linting-59ee6591aea2427f9bb07fe1ca1ea615?pvs=4 for details"
        )
        sys.exit(1)
    else:
        sys.exit(0)


# if using 'bazel test ...'
if __name__ == "__main__":
    main()

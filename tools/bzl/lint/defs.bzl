load("@aspect_rules_py//py:defs.bzl", "py_test")
load("@python_pip//:requirements.bzl", "requirement")

def _merge_list(l1, l2):
    result = []
    for l in l1:
        result.append(l)
    for l in l2:
        if l not in result:
            result.append(l)
    return result

def _shell_quote(s):
    return "'" + s.replace("'", "'\\''") + "'"

def lint_test(name, srcs, pyright, bandit, ruff, pyright_extra_args = {}, deps = [], main = "", data = [], tags = [], **kwargs):
    """
        Rule to call lint on all srcs files.

        The rule is usually not directly used, but automatically for
        every py_binary/py_library rule that is not exempted.

        Args:
        pyright_extra_args: extra arguments to pass to pyright. see https://github.com/microsoft/pyright/blob/main/docs/configuration.md, e.g.
           reportMissingParameterType=True
    """
    if not pyright and not bandit and not ruff:
        return
    args = []
    if pyright:
        args.append("--pyright")
    if bandit:
        args.append("--bandit")
    if ruff:
        args.append("--ruff")
    if pyright_extra_args:
        args.append("--pyright-extra-args")
        args.append(_shell_quote(json.encode(pyright_extra_args)))
    py_test(
        name = name,
        srcs = [
            "//tools/bzl/lint:py_lint_wrapper.py",
        ] + srcs,
        main = "py_lint_wrapper.py",
        args = args + ["$(locations %s)" % x for x in srcs],
        deps = _merge_list(deps, [
            requirement("bandit"),
            requirement("types-protobuf"),
            requirement("google-api-python-client-stubs"),
        ]),
        # we know that pylint is very slow and can go over 30s at times
        timeout = "long",
        tags = ["lint"] + tags,
        data = data + [
            "//tools/bzl/lint/pyright",
            "//:bandit_config",
            "//:ruff_config",
            "//tools:ruff",
        ],
        **kwargs
    )

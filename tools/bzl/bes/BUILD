load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

py_binary(
    name = "bes_server",
    srcs = [
        "bes_server.py",
    ],
    deps = [
        "//base/logging:struct_logging",
        "//third_party/proto/bazel_build:bes_py_proto",
        "//third_party/proto/bazel_build:googleapis_py_proto",
        "//tools/bazel_runner/bep_parser",
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("grpcio-health-checking"),
        requirement("protobuf"),
        requirement("certifi"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":bes_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
    ],
)

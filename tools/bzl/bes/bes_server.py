"""Build Event Service Server."""

import logging
import threading
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import Optional

import google.protobuf.empty_pb2 as empty_pb2
import grpc
import structlog
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, Histogram, start_http_server

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
import third_party.proto.bazel_build.publish_build_event_pb2 as publish_build_event_pb2
import third_party.proto.bazel_build.publish_build_event_pb2_grpc as publish_build_event_pb2_grpc
from base.logging.struct_logging import setup_struct_logging

log = structlog.get_logger()

_bazel_calls_counter = Counter(
    "bazel_metrics_invocation",
    "Number of Bazel tool invocations",
    labelnames=["user", "status"],
)
_bazel_call_latency = Histogram(
    "bazel_metrics_invocation_latency",
    "latency of Bazel tool invocation",
    labelnames=["user", "status"],
)

_bazel_test_summary_counter = Counter(
    "bazel_metrics_test_summary",
    documentation="Test summary for the build",
    labelnames=["target_name", "overall_status", "cached"],
)


@dataclass
class BuildInfo:
    """Class storing information about a bazel invocation."""

    # The user that invoked the build.
    user: str = ""

    # The exit status of the build.
    exit_status: str = ""

    # The wall time of the build.
    wall_time_in_ms: int = 0


class PublishBuildEventServices(publish_build_event_pb2_grpc.PublishBuildEventServicer):
    """Implementation of the Build Event Service protocol."""

    def __init__(self):
        self._lock = threading.Lock()
        self._info = defaultdict(BuildInfo)

    def PublishLifecycleEvent(
        self, request: publish_build_event_pb2.PublishLifecycleEventRequest, context
    ):  # pylint: disable=unused-argument
        return empty_pb2.Empty()  # type: ignore

    def _process_event(self, event):
        invocation_id = event.stream_id.invocation_id
        structlog.contextvars.bind_contextvars(invocation_id=str(invocation_id))
        build_event: Optional[build_event_stream_pb2.BuildEvent] = None
        if event.event.WhichOneof("event") == "bazel_event":
            build_event = build_event_stream_pb2.BuildEvent()
            assert build_event is not None
            event.event.bazel_event.Unpack(build_event)
            log.debug("Bazel event '%s'", build_event)

            build_info = self._info[invocation_id]

            # see third_party/proto/bazel_build/build_event_stream.proto
            # for details

            if build_event.WhichOneof("payload") == "test_summary":
                # Summary of the test run.
                is_cached = (
                    build_event.test_summary.total_run_count
                    == build_event.test_summary.total_num_cached
                )
                _bazel_test_summary_counter.labels(
                    build_event.id.test_summary.label,
                    build_event_stream_pb2.TestStatus.Name(
                        build_event.test_summary.overall_status
                    ),
                    str(1 if is_cached else 0),
                ).inc()

            if build_event.WhichOneof("payload") == "finished":
                # the invocation finished, capture the exit code
                build_info.exit_status = build_event.finished.exit_code.name
            if build_event.WhichOneof("payload") == "build_metrics":
                # metrics, capture the wall time in ms
                build_info.wall_time_in_ms = (
                    build_event.build_metrics.timing_metrics.wall_time_in_ms
                )
            if build_event.WhichOneof("payload") == "workspace_status":
                # extract the user name from the workspace status
                for item in build_event.workspace_status.item:
                    if item.key == "BUILD_USER":
                        user = item.value
                        build_info.user = user
            if build_event.last_message:
                # emit the metrics
                logging.info("Build info: %s", build_info)
                del self._info[invocation_id]
                _bazel_calls_counter.labels(
                    build_info.user, build_info.exit_status
                ).inc()
                _bazel_call_latency.labels(
                    build_info.user, build_info.exit_status
                ).observe(build_info.wall_time_in_ms * 1000)
        structlog.contextvars.clear_contextvars()

    def PublishBuildToolEventStream(self, request_iterator, context):  # pylint: disable=unused-argument
        for request in request_iterator:
            event = request.ordered_build_event

            try:
                self._lock.acquire()
                self._process_event(event)
            finally:
                self._lock.release()

            response = publish_build_event_pb2.PublishBuildToolEventStreamResponse()
            response.stream_id.MergeFrom(event.stream_id)
            response.sequence_number = event.sequence_number
            yield response


def _serve():
    server = grpc.server(ThreadPoolExecutor(max_workers=10))
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)
    publish_build_event_pb2_grpc.add_PublishBuildEventServicer_to_server(
        PublishBuildEventServices(), server
    )
    service_names = (
        publish_build_event_pb2.DESCRIPTOR.services_by_name[
            "PublishBuildEvent"
        ].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    server.add_insecure_port("[::]:50051")
    server.start()
    log.info("Listening on 50051")
    server.wait_for_termination()


def main():
    setup_struct_logging()
    # begin listening for Prometheus requests
    start_http_server(9090)

    _serve()


if __name__ == "__main__":
    main()

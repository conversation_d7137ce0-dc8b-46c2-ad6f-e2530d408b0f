#include <ATen/ATen.h>
#include <ATen/cuda/CUDAContext.h>
#include <cuda.h>
#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <torch/extension.h>

namespace pytorch_cpp_example_extension {

template <typename input_t>
__global__ void example_forward(input_t const* src, input_t* out) {
    printf("%f", *src);
    *out = *src * 2.0;
}

template <typename input_t>
void dispatch_example_forward(input_t const* src, input_t* out) {
    dim3 blocks(1, 1, 1);
    dim3 threads(1, 1, 1);
    example_forward<input_t>
        <<<blocks, threads, 0, at::cuda::getCurrentCUDAStream()>>>(src, out);
}

torch::Tensor fwd_cuda(torch::Tensor const& input) {
    auto options = input.options().requires_grad(false);
    torch::Tensor output = torch::empty({1}, options);

    dispatch_example_forward<float>(
        reinterpret_cast<float*>(input.data_ptr()),
        reinterpret_cast<float*>(output.data_ptr()));

    return output;
}
}  // namespace pytorch_cpp_example_extension

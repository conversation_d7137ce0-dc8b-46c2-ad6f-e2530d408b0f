load("//tools/bzl:pytorch.bzl", "pytorch_cpp_extension")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

pytest_test(
    name = "pytorch_cpp_extension_test",
    srcs = ["pytorch_cpp_extension_test.py"],
    data = [
        ":pytorch_cpp_example_extension.so",
        "@pybind11",
        "@remote_cuda_toolchain//:libcusolver",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        requirement("pybind11"),
        requirement("torch"),
        requirement("numpy"),
    ],
)

# pytorch extension used by pytorch_cpp_extension_test
pytorch_cpp_extension(
    name = "pytorch_cpp_example_extension",
    srcs = [
        "pytorch_cpp_example_extension.cpp",
    ],
    cuda_srcs = [
        "pytorch_cpp_example_extension.cu",
    ],
)

def _shell_quote(s):
    return "'" + s.replace("'", "'\\''") + "'"

def _trivy_test_impl(ctx):
    """Implementation of the trivy_test rule."""
    args = ["--allow-list %s" % _shell_quote(a) for a in ctx.attr.allow_list]
    if ctx.file.allow_list_file:
        args.append("--allow-list-file %s" % _shell_quote(ctx.file.allow_list_file.short_path))
    args.append("--severity %s" % ctx.attr.severity)
    allow_list_args = " ".join(args)
    command = """#!/bin/bash
set -eu
""" + "\n".join([
        '%s --mode \"%s\" %s %s "$@"' % (ctx.executable.trivy_bin.short_path, ctx.attr.mode, ctx.file.src.short_path, allow_list_args),
    ])

    ctx.actions.write(
        output = ctx.outputs.executable,
        content = command,
        is_executable = True,
    )

    transitive_data = depset(transitive = [ctx.attr.src.files] +
                                          [
                                              ctx.attr.trivy_bin[DefaultInfo].files,
                                              ctx.attr.allow_list_file[DefaultInfo].files,
                                          ])
    inputs = (
        [ctx.executable.trivy_bin] +
        transitive_data.to_list() +
        ctx.attr.src[DefaultInfo].files.to_list() +
        ctx.attr.trivy_bin[DefaultInfo].data_runfiles.files.to_list()
    )

    return [DefaultInfo(
        runfiles = ctx.runfiles(
            files = inputs,
            transitive_files = transitive_data,
            collect_data = True,
        ),
        executable = ctx.outputs.executable,
    )]

_trivy_attrs = {
    "trivy_bin": attr.label(
        doc = "A trivy binary",
        default = Label("//tools/bzl/trivy"),
        cfg = "target",
        executable = True,
    ),
    "mode": attr.string(
        doc = "The mode to run trivy in",
        default = "image",
        values = ["image", "fs", "config"],
    ),
    "severity": attr.string(
        doc = "The severity of vulnerabilities to report",
        default = "CRITICAL",
    ),
    "allow_list": attr.string_list(
        doc = "A list of allowed vulnerabilities. The test will fail if a vulnerabilty is found that is not in this list. A vulnerability is identified by its ID. This should only be done if there is a good reason to ignore a vulnerability.",
        default = [],
    ),
    "allow_list_file": attr.label(
        doc = "A file containing a list of allowed vulnerabilities. The test will fail if a vulnerabilty is found that is not in this list. A vulnerability is identified by its ID. This should only be done if there is a good reason to ignore a vulnerability.",
        allow_single_file = True,
        default = "//:trivy_allow_list",
    ),
    "src": attr.label(
        doc = "The artifact to test",
        allow_single_file = True,
    ),
}

trivy_test = rule(
    _trivy_test_impl,
    attrs = _trivy_attrs,
    executable = True,
    test = True,
    doc = """\
Test a dependency lock file or a container image with trivy

Example with a pnpm file:

  `config/BUILD`:

  ```python
    load("//tools/bzl:trivy.bzl", "trivy_test")

    trivy_test(
        name = "pnpm-lock.trivy_test",
        src = "pnpm-lock.yaml",
        mode = "fs",
        tags = [
            "lint",
        ],
    )
  ```

Example with a container image:

  `config/BUILD`:

  ```python
    load("//tools/bzl:trivy.bzl", "trivy_test")

    trivy_test(
        name = "image.trivy_test",
        src = ":image",
        tags = [
            "lint",
        ],
    )
  ```

Note: All container_push targets have trivy enagbled by default.

""",
)

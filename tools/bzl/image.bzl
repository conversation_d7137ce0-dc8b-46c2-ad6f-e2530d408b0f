load("@aspect_bazel_lib//lib:tar.bzl", "mtree_spec", "tar")
load("@rules_oci//oci:defs.bzl", _oci_image = "oci_image")
load("//tools/bzl:trivy.bzl", "trivy_test")

def _layers(name, binary):
    mtree_spec(
        name = name + ".mf",
        srcs = [binary],
    )

    result = []
    layer_target = "{}.layer".format(name)
    result.append(layer_target)
    tar(
        name = layer_target,
        srcs = [binary],
        mtree = name + ".mf",
    )

    return result

def app_image(
        name,
        binary,
        package_name,
        entrypoint = None,
        base = "//tools/docker:ubuntu_base_image",
        tars = [],
        **kwargs):
    "Wrapper around oci_image that splits the py_binary into layers."

    if not entrypoint:
        if binary.startswith("//"):
            fail("need to specify entrypoint")
        entrypoint = "/" + package_name + "/" + (binary[1:] if binary.startswith(":") else binary)

    image(
        name = name,
        base = base,
        entrypoint = [entrypoint],
        tars = tars + _layers(name, binary),
        **kwargs
    )

def image(
        name,
        trivy = True,
        trivy_image = None,
        trivy_test_tags = ["lint", "postmerge-test"],
        trivy_allow_list = [],
        **kwargs):
    _oci_image(
        name = name,
        **kwargs
    )

    if trivy:
        if not trivy_image:
            trivy_image = name
        trivy_test(
            name = "{}.trivy_test".format(name),
            src = trivy_image,
            timeout = "long",
            allow_list = trivy_allow_list,
            tags = trivy_test_tags,
        )

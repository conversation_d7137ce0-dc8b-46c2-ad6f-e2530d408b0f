"""Rust language rules."""

load(
    "@rules_rust//rust:defs.bzl",
    "rustfmt_test",
    rules_rust_binary = "rust_binary",
    rules_rust_library = "rust_library",
    rules_rust_shared_ibrary = "rust_shared_library",
    rules_rust_test = "rust_test",
)
load("//tools/bzl:image.bzl", "app_image")

def rust_binary(name, **kwargs):
    """ Rule for Rust binaries.

    See https://bazelbuild.github.io/rules_rust/defs.html#rust_binary for usage

    See https://www.notion.so/Linting-59ee6591aea2427f9bb07fe1ca1ea615?pvs=4 for more information about linting.
    """
    rules_rust_binary(
        name = name,
        **kwargs
    )

    rustfmt_test(
        name = "{}.fmt.test".format(name),
        targets = [":{}".format(name)],
    )

def rust_library(name, format = True, **kwargs):
    """ Rule for Rust libaries.

    See https://bazelbuild.github.io/rules_rust/defs.html#rust_library for usage

    See https://www.notion.so/Linting-59ee6591aea2427f9bb07fe1ca1ea615?pvs=4 for more information about linting.
    """

    rules_rust_library(
        name = name,
        **kwargs
    )

    if format:
        rustfmt_test(
            name = "{}.fmt.test".format(name),
            targets = [":{}".format(name)],
        )

def rust_shared_library(name, **kwargs):
    """ Rule for Rust shared libaries.

    See https://bazelbuild.github.io/rules_rust/defs.html#rust_shared_library for usage

    See https://www.notion.so/Linting-59ee6591aea2427f9bb07fe1ca1ea615?pvs=4 for more information about linting.
    """
    rules_rust_shared_ibrary(name = name, **kwargs)

    rustfmt_test(
        name = "{}.fmt.test".format(name),
        targets = [":{}".format(name)],
    )

# re-export
rust_test = rules_rust_test

def rust_oci_image(
        name,
        binary,
        package_name,
        entrypoint = None,
        workdir = None,
        **kwargs):
    "Wrapper around oci_image for rust_binary."

    b = (binary[1:] if binary.startswith(":") else binary)
    if not entrypoint:
        if binary.startswith("//"):
            fail("need to specify entrypoint")
        entrypoint = "/" + package_name + "/" + b
    if not workdir:
        workdir = "/" + package_name + "/" + b + ".runfiles/_main"

    app_image(
        name = name,
        binary = binary,
        package_name = package_name,
        entrypoint = entrypoint,
        workdir = workdir,
        **kwargs
    )

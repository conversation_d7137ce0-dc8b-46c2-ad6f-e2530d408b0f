def node_sea_binary(name, src, out, visibility = None):
    """Create a Node.js SEA binary from a JavaScript bundle.

    Args:
        name: Name of the target.
        src: Input JavaScript, typically a compiled TypeScript to JS bundle.
        output: Output target.
        visibility: Visibility of the target.
    """
    native.genrule(
        name = name,
        srcs = [src],
        outs = [out],
        cmd_bash = """
            BAZEL_BINDIR=. $(execpath //tools/bzl/node_sea) --js-bundle "$<" --output "$@"
        """,
        tools = ["//tools/bzl/node_sea"],
        executable = True,
        visibility = visibility,
    )

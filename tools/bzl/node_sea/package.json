{"name": "node_sea", "version": "1.0.0", "description": "Tool for creating Node.js Single Executable Applications", "main": "src/node_sea.ts", "scripts": {"build": "esbuild src/node_sea.ts --bundle --platform=node --outfile=dist/node_sea.js"}, "dependencies": {"postject": "^1.0.0-alpha.6", "yargs": "^17.7.2"}, "devDependencies": {"@types/node": "^20.13.1", "@types/yargs": "^17.0.32", "ts-node": "^10.9.2", "typescript": "^5.4.2"}}
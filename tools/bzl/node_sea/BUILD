load("@aspect_rules_js//js:defs.bzl", "js_binary")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@aspect_rules_esbuild//esbuild:defs.bzl", "esbuild")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

SRC_FILES = glob(
    ["src/**/*.ts"],
    exclude = [
        "src/**/__tests__/**",
        "src/**/__mocks__/**",
    ],
)

BUILD_DEPS = [
    ":node_modules",
    ":node_modules/postject",
    ":node_modules/yargs",
    ":node_modules/@types/node",
    ":node_modules/@types/yargs",
]

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
)

filegroup(
    name = "package",
    srcs = ["package.json"],
)

esbuild(
    name = "bundle",
    srcs = SRC_FILES + [
        ":package",
        ":tsconfig",
    ],
    entry_point = "src/node_sea.ts",
    format = "cjs",
    minify = True,
    output = "out/node_sea.bundle.js",
    platform = "node",
    sourcemap = "external",
    target = "ES2022",
    tsconfig = ":tsconfig",
    deps = BUILD_DEPS,
)

js_binary(
    name = "node_sea",
    data = [":bundle"],
    entry_point = "out/node_sea.bundle.js",
    visibility = ["//visibility:public"],
)

import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { inject } from "postject";
import * as path from "path";
import * as fs from "fs";
import { spawnSync } from "child_process";

async function main() {
  /// Parse command line.

  const argv = await yargs(hideBin(process.argv))
    .option("js-bundle", {
      type: "string",
      description: "Path to the JavaScript bundle file",
      demandOption: true,
    })
    .option("output", {
      type: "string",
      description: "Path to the output file",
      demandOption: true,
    })
    .help()
    .parse();

  /// Node.js path.
  /// Use self for both generating the SEA blob && as the SEA binary.

  const nodePath = process.env.JS_BINARY__NODE_BINARY;

  /// Format Input and Output Paths.

  const jsBundle = argv["js-bundle"];
  const outputExe = argv.output;
  const outputDir = path.dirname(outputExe);
  const seaConfigPath = path.join(
    outputDir,
    path.basename(outputExe) + ".sea-config.json",
  );
  const blobPath = outputExe + ".blob";

  console.error(`Nodejs Path:      ${nodePath}`);
  console.error(`Input JS Bundle:  ${jsBundle}`);
  console.error(`Output Directory: ${outputDir}`);
  console.error(`SEA Config:       ${seaConfigPath}`);
  console.error(`SEA Blob:         ${blobPath}`);
  console.error(`SEA Binary:       ${outputExe}`);

  if (!fs.existsSync(outputDir)) {
    console.error(`Error: Output directory does not exist: ${outputDir}`);
    return 1;
  }

  /// SEA Config: Format temporary file.
  /// ... not sure why these can't be command line args.

  const seaConfigContent = {
    main: jsBundle,
    output: blobPath,
    disableExperimentalSEAWarning: true,
  };

  try {
    fs.writeFileSync(seaConfigPath, JSON.stringify(seaConfigContent, null, 2));
    console.error(`SEA Config: Created.`);
  } catch (error) {
    console.error(`SEA Config Error: ${error.message}.`);
    return 1;
  }

  /// SEA Blob: Build using Node.js.

  console.error(
    `SEA Blob: \${Nodejs Path} --experimental-sea-config \${SEA Config}.`,
  );
  const result = spawnSync(
    nodePath,
    ["--experimental-sea-config", seaConfigPath],
    {
      stdio: "inherit",
    },
  );
  if (result.error) {
    console.error(`SEA Blob: ${result.error.message}.`);
    return 1;
  }
  if (result.status !== 0) {
    console.error(`SEA Blob: ${result.status}.`);
    return result.status;
  }

  /// SEA Binary: Copy from Node.js, set file mode.

  try {
    console.error(`SEA Binary: Copying from \${Nodejs Path} with mode 0755.`);
    cp(nodePath, outputExe, 0o755);
  } catch (error) {
    console.error(`SEA Binary: error: ${error.message}.`);
    return 1;
  }

  /// SEA Binary: postject the SEA Blob.

  try {
    console.error(`SEA Binary: Injecting SEA Blob.`);
    const blobData = fs.readFileSync(blobPath);
    await inject(outputExe, "NODE_SEA_BLOB", blobData, {
      sentinelFuse: "NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2",
    });
  } catch (error) {
    console.error(`SEA Binary: error injecting SEA Blob: ${error.message}.`);
    return 1;
  }
  console.error("SEA Binary: Created successfully.");
  console.error(`SEA Binary: ${outputExe}.`);

  return 0;
}

function cp(src: string, dest: string, mode: number): void {
  const content = fs.readFileSync(src);
  fs.writeFileSync(dest, content);
  fs.chmodSync(dest, mode);
}

/// Main

main()
  .then((exitCode) => process.exit(exitCode))
  .catch((error) => {
    console.error("Error:", error);
    process.exit(1);
  });

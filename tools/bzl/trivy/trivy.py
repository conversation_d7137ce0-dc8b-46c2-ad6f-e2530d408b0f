"""Wrapper around trivy."""

import argparse
import yaml
import json
import os
import pathlib
import tempfile
import subprocess
import sys


def _get_trivy_cmd(
    args: argparse.Namespace, rest: list[str], timeout: int
) -> list[str]:
    cmd = ["../trivy/trivy.bin"]
    if args.mode == "image":
        cmd += [
            "image",
            "--input",
            str(args.src.resolve()),
            "--scanners",
            "vuln",
        ]
    elif args.mode == "fs":
        cmd += ["fs", str(args.src.resolve())]
    elif args.mode == "config":
        cmd += ["config", str(args.src.resolve())]
    else:
        raise ValueError(f"Unknown mode: {args.mode}")
    cmd += [
        "--exit-code",
        "1",
        "--severity",
        args.severity,
        "--timeout",
        f"{timeout}s",
        "--no-progress",
    ]
    # use our mirror of the trivy database instead of fetching it from github
    cmd += [
        "--db-repository",
        "us-central1-docker.pkg.dev/system-services-dev/build-images/trivy-db",
    ]
    cmd += rest
    return cmd


def _run_human_readable_output(json_data: dict) -> tuple[int, str]:
    """Run trivy with human readable output."""

    with tempfile.NamedTemporaryFile(mode="w+", encoding="utf-8") as f:
        f.write(json.dumps(json_data))
        f.flush()
        cmd = ["../trivy/trivy.bin"]
        cmd += [
            "convert",
            "--format",
            "table",
            f.name,
        ]

        r = subprocess.run(cmd, check=False, stdout=subprocess.PIPE, encoding="utf-8")
        return (r.returncode, r.stdout)


def _run_json_output(
    args: argparse.Namespace, rest: list[str], timeout: int
) -> tuple[int, dict]:
    """Run trivy with JSON output."""
    cmd = _get_trivy_cmd(args, rest, timeout)
    cmd.append("--format")
    cmd.append("json")
    print("Running", cmd)
    r = subprocess.run(
        cmd,
        check=False,
        stdout=subprocess.PIPE,
        encoding="utf-8",
    )
    if "TEST_UNDECLARED_OUTPUTS_DIR" in os.environ:
        p = pathlib.Path(os.environ["TEST_UNDECLARED_OUTPUTS_DIR"])
        if p.exists():
            p.joinpath("trivy.json").write_text(r.stdout, encoding="utf-8")

    try:
        data = json.loads(r.stdout)
        return (r.returncode, data)
    except json.JSONDecodeError:
        if r.stdout:
            print("Not JSON output:\n", r.stdout)
        return (r.returncode, {})


def _read_allow_list_file(args: argparse.Namespace) -> set[str]:
    """Read the allow list from a file."""
    if not args.allow_list_file:
        return set([])
    data = yaml.safe_load(args.allow_list_file.read_text())
    result = []
    for item in data:
        result.append(item["VulnerabilityID"])
    return set(result)


def _is_kernel(row) -> bool:
    """Checks if the result is a kernel vulnerability."""
    return row.get("PkgName") == "linux-libc-dev" and row["Title"].startswith(
        "kernel: "
    )


def _check_allow_list(data, args: argparse.Namespace) -> int:
    """Check the allow list.

    Args:
        data: The JSON output of trivy.
        args: The arguments passed to the test.
    """

    rc = 0
    allowed_vulns = _read_allow_list_file(args)
    if args.allow_list:
        allowed_vulns.update(args.allow_list)
    for result in data["Results"]:
        if "Vulnerabilities" not in result:
            continue
        for vuln in result["Vulnerabilities"]:
            if _is_kernel(vuln):
                print("Ignoring kernel vulnerability:", vuln["VulnerabilityID"])
                continue
            if vuln["VulnerabilityID"] in allowed_vulns:
                print("Ignoring allowed vulnerability:", vuln["VulnerabilityID"])
                continue
            print("Found vulnerability:", vuln["VulnerabilityID"])
            rc = 1
    return rc


def main() -> int:
    parser = argparse.ArgumentParser()
    parser.add_argument("src", type=pathlib.Path)
    parser.add_argument("--mode", required=True)
    parser.add_argument("--allow-list", action="append")
    parser.add_argument("--allow-list-file", type=pathlib.Path)
    parser.add_argument("--severity", default="CRITICAL")
    args, rest = parser.parse_known_args()
    assert args.src.exists()

    timeout = int(os.environ.get("TEST_TIMEOUT", 12 * 60))

    print("Running trivy")
    r, data = _run_json_output(args, rest, timeout)
    if r == 0:
        return 0
    if not data:
        return 1
    r = _check_allow_list(data, args)
    if r:
        print("Re-running for human readable output")
        # trivy didn't pass, so a vulnerability was found
        # We did parse the JSON output to find the vulnerabilities and compare them
        # to the allow list. Now we just print the output
        _, stdout = _run_human_readable_output(data)
        print(stdout)
    return r


if __name__ == "__main__":
    sys.exit(main())

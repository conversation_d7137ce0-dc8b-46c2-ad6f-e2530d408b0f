"""Library to test kubecfg configuration."""

import logging

import yaml
from base.cloud.k8s import kubectl
from tools.kubecfg.kubecfg import KubeCfg, VisitConfig


class KubeCfgTestUtil:
    """Test utility for kubecfg."""

    def __init__(
        self, kubecfg: KubeCfg, allow_multi_apps: bool, no_rewrite: bool = True
    ):
        """Constructs a KubeCfgTestUtil instance.

        Args:
            kubecfg: KubeCfg instance
            allow_multi_apps: whether to allow multiple apps
        """
        self.kubecfg = kubecfg
        self.first_app = None
        self.allow_multi_apps = allow_multi_apps
        self.no_rewrite = no_rewrite
        self.objects = set()
        self.server_cert_dns_names = set()
        self.kind_checks = {
            "BackendConfig": self._check_backend_config,
            "FrontendConfig": self._check_frontend_config,
            "Ingress": self._check_ingress,
            "Deployment": self._check_deployment,
            "Certificate": self._check_cert,
            "PubSubSubscription": self._check_pubsub_subscription,
            "ScaledObject": self._check_scaled_object,
        }

    def _check_scaled_object(self, k8s_object):
        """Checks the scaled object configuration."""
        assert "spec" in k8s_object, f"spec not in {k8s_object}"
        assert (
            "scaleTargetRef" in k8s_object["spec"]
        ), f"scaleTargetRef not in {k8s_object}"
        assert (
            "minReplicaCount" in k8s_object["spec"]
        ), f"minReplicaCount not in {k8s_object}"
        assert (
            "maxReplicaCount" in k8s_object["spec"]
        ), f"maxReplicaCount not in {k8s_object}"
        assert (
            k8s_object["spec"]["minReplicaCount"]
            <= k8s_object["spec"]["maxReplicaCount"]
        ), f"minReplicaCount > maxReplicaCount in {k8s_object}"

    def _check_pubsub_subscription(self, k8s_object):
        """Checks the pubsub subscription configuration."""
        assert "spec" in k8s_object, f"spec not in {k8s_object}"
        assert "topicRef" in k8s_object["spec"], f"topic not in {k8s_object}"
        assert (
            "ackDeadlineSeconds" in k8s_object["spec"]
        ), f"ackDeadlineSeconds not in {k8s_object}"
        assert "retryPolicy" in k8s_object["spec"], f"retryPolicy not in {k8s_object}"

    def _check_deployment(self, k8s_object):
        """Checks the deployment configuration."""
        assert "spec" in k8s_object, f"spec not in {k8s_object}"
        assert "template" in k8s_object["spec"], f"template not in {k8s_object}"
        assert "spec" in k8s_object["spec"]["template"], f"spec not in {k8s_object}"
        assert (
            "priorityClassName" in k8s_object["spec"]["template"]["spec"]
        ), f"priorityClassName not in {k8s_object}"

    def _check_backend_config(self, k8s_object):
        """Checks the backend config."""
        assert "spec" in k8s_object, f"spec not in {k8s_object}"
        assert (
            "securityPolicy" in k8s_object["spec"]
        ), f"securityPolicy not in {k8s_object}"
        assert (
            "name" in k8s_object["spec"]["securityPolicy"]
        ), f"name not in {k8s_object}"
        assert k8s_object["spec"]["securityPolicy"]["name"] == "ingress-ip-throttle"

    def _check_frontend_config(self, k8s_object):
        """Checks the frontend config."""
        assert "spec" in k8s_object, f"spec not in {k8s_object}"
        assert "sslPolicy" in k8s_object["spec"], f"sslPolicy not in {k8s_object}"
        assert (
            k8s_object["spec"]["sslPolicy"] == "restricted-ssl-policy"
        ), f"sslPolicy is not restricted-ssl-policy in {k8s_object}"

    def _check_ingress(self, k8s_object):
        """Checks the ingress config."""
        assert "spec" in k8s_object, f"spec not in {k8s_object}"
        assert "tls" in k8s_object["spec"], f"tls not in {k8s_object}"
        assert (
            "annotations" in k8s_object["metadata"]
        ), f"annotations not in {k8s_object}"
        assert (
            k8s_object["metadata"]["annotations"]["kubernetes.io/ingress.allow-http"]
            == "false"
        )

    def _check_cert(self, k8s_object):
        """Checks the certificate configuration."""
        assert "spec" in k8s_object, f"spec not in {k8s_object}"
        assert "issuerRef" in k8s_object["spec"], f"issuerRef not in {k8s_object}"

        if "server auth" in k8s_object["spec"].get("usages", ""):
            assert "dnsNames" in k8s_object["spec"], f"dnsNames not in {k8s_object}"

            for dns_name in k8s_object["spec"]["dnsNames"]:
                assert (
                    dns_name not in self.server_cert_dns_names
                ), f"Duplicate server cert dns name {dns_name}"

            self.server_cert_dns_names.update(k8s_object["spec"]["dnsNames"])

    def _check_basic_object(self, k8s_object):
        """Checks a basic kubernetes object configuration."""
        assert "kind" in k8s_object, f"kind not in {k8s_object}"
        assert "metadata" in k8s_object, f"metadata not in {k8s_object}"
        assert "name" in k8s_object["metadata"], f"metadata.name not in {k8s_object}"
        assert kubectl.is_valid_kind(
            k8s_object["kind"]
        ), f"kind {k8s_object['kind']} not supported"
        if kubectl.is_namespaced_kind(k8s_object["kind"]):
            assert (
                "namespace" in k8s_object["metadata"]
            ), f"metadata.namespace not in {k8s_object}"
        if not self.kubecfg.cluster_wide:
            # check that the app label is set
            assert (
                "app" in k8s_object["metadata"]["labels"]
            ), f"metadata.labels.app not in {k8s_object}"

            # check that the app label is the same for all objects
            if not self.allow_multi_apps:
                if not self.first_app:
                    self.first_app = k8s_object["metadata"]["labels"].get("app")
                else:
                    assert (
                        self.first_app == k8s_object["metadata"]["labels"].get("app")
                    ), f"metadata.labels.app in {k8s_object} not matching {self.first_app}"
        key = (
            k8s_object["kind"],
            k8s_object["metadata"].get("namespace", ""),
            k8s_object["metadata"]["name"],
        )
        assert key not in self.objects, f"Duplicate object {key}"
        self.objects.add(key)

    def _check_object(self, k8s_object):
        """Checks an kubernetes object configuration."""
        if k8s_object is None:
            return
        self._check_basic_object(k8s_object)
        kind_checker = self.kind_checks.get(k8s_object["kind"])
        if kind_checker:
            kind_checker(k8s_object)

    def test(self):
        config = ""
        self.visit_config = VisitConfig(stamp=True, rewrite=not self.no_rewrite)
        try:
            with self.kubecfg.get_config(None) as config:
                config = config.dump()
                d = list(yaml.safe_load_all(config))
                for k8s_object in d:
                    self._check_object(k8s_object)
        except Exception as e:
            logging.info("Config %s", config)
            raise e

diff -u -N /tmp/LIVE-2346193769/apps.v1.Deployment.central.auth-central /tmp/MERGED-868661102/apps.v1.Deployment.central.auth-central
--- /tmp/LIVE-2346193769/apps.v1.Deployment.central.auth-central	2024-06-11 13:53:11.144218409 +0000
+++ /tmp/MERGED-868661102/apps.v1.Deployment.central.auth-central	2024-06-11 13:53:11.145218408 +0000
@@ -7,12 +7,12 @@
     reloader.stakater.com/search: "true"
   creationTimestamp: "2023-12-20T21:46:58Z"
-  generation: 215
+  generation: 216
   labels:
     app: auth-central
     app.kubernetes.io/managed-by: kubecfg
-    app.kubernetes.io/version: 0fdb1e57d636fafe366abe4c29268b448329b13c
-    augmentcode.com/deployed-by: deploy-runner-019003bc-a54f-c05c-a20d-449aa2858e57-4xrbx
+    app.kubernetes.io/version: 78d2a124d384dbe881d5bb5b534851c3ceb127ca
+    augmentcode.com/deployed-by: deploy-runner-01900790-adf0-a2b8-3944-f76b7689bbcf-cx4pd
     augmentcode.com/deployment-target: auth_central
   name: auth-central
   namespace: central
@@ -38,8 +38,8 @@
       labels:
         app: auth-central
         app.kubernetes.io/managed-by: kubecfg
-        app.kubernetes.io/version: 0fdb1e57d636fafe366abe4c29268b448329b13c
-        augmentcode.com/deployed-by: deploy-runner-019003bc-a54f-c05c-a20d-449aa2858e57-4xrbx
+        app.kubernetes.io/version: 78d2a124d384dbe881d5bb5b534851c3ceb127ca
+        augmentcode.com/deployed-by: deploy-runner-01900790-adf0-a2b8-3944-f76b7689bbcf-cx4pd
         augmentcode.com/deployment-target: auth_central
     spec:
       affinity:
@@ -85,7 +85,7 @@
           value: service.namespace=$(POD_NAMESPACE),service.instance.id=$(POD_NAME)
         - name: STAKATER_AUTH_CENTRAL_CONFIG_CONFIGMAP
           value: 1b01f10b68d32c278bfdfb2e1c48b6318828a445
-        image: us-central1-docker.pkg.dev/system-services-prod/build-images/auth-central@sha256:63638f0bc239120af6b5f68e1c65825b6d1c15c7c2094b56d91ac20c011d035b
+        image: us-central1-docker.pkg.dev/system-services-prod/build-images/auth-central@sha256:0160e0e3069cc8ae78ae85b1206fce18f674aca4f6242ff114d07bf182e7b4f3
         imagePullPolicy: IfNotPresent
         livenessProbe:
           failureThreshold: 3

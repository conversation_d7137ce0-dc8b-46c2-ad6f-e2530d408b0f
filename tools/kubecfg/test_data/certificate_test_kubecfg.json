[{"apiVersion": "cert-manager.io/v1", "kind": "Certificate", "metadata": {"name": "support-ui-client-certificate", "namespace": "user1", "labels": {"app.kubernetes.io/managed-by": "kubecfg"}}, "spec": {"commonName": "support-ui-client-certificate", "duration": "2160h", "issuerRef": {"kind": "Issuer", "name": "root-issuer"}, "privateKey": {"algorithm": "RSA", "encoding": "PKCS8", "size": 4096}, "renewBefore": "720h", "certName": "support-ui-client-certificate", "usages": ["client auth", "key encipherment", "digital signature"]}}]
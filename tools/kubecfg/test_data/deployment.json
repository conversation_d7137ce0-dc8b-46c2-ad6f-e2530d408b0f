{"apiVersion": "apps/v1", "kind": "Deployment", "metadata": {"labels": {"app": "support-ui"}, "name": "support-ui"}, "spec": {"template": {"metadata": {"labels": {"app": "support-ui"}}, "spec": {"containers": [{"env": [{"name": "CONFIG_FILE", "value": "/config/flask.cfg"}], "target": {"name": "//foo:push.image", "dst": "image:dev"}, "name": "support-ui", "ports": [{"containerPort": 5000, "name": "http-svc"}], "volumeMounts": [{"mountPath": "/client-certs", "name": "client-certs"}]}], "volumes": [{"name": "client-certs", "secret": {"secretName": "support-ui-client-certificate"}}]}}}}
"""Tests for the kubecfg_diff library."""

import pathlib
from tools.kubecfg import kubecfg_diff


def test_certificate_diff():
    certificate_diff = r"""diff -u -N /tmp/LIVE-94481864/cert-manager.io.v1.Certificate.dogfood.embeddings-search-server-certificate /tmp/MERGED-2587135442/cert-manager.io.v1.Certificate.dogfood.embeddings-search-server-certificate
--- /tmp/LIVE-94481864/cert-manager.io.v1.Certificate.dogfood.embeddings-search-server-certificate	2023-12-12 18:36:47.231626214 +0000
+++ /tmp/MERGED-2587135442/cert-manager.io.v1.Certificate.dogfood.embeddings-search-server-certificate	2023-12-12 18:36:47.232626311 +0000
@@ -8,6 +8,8 @@
   generation: 1
   labels:
     app.kubernetes.io/managed-by: kubecfg
+    app.kubernetes.io/version: 7e2885156cff0daa0c23cb9744007df53bfa192f
+    augmentcode.com/deployed-by: deploy
   managedFields:
   - apiVersion: cert-manager.io/v1
     fieldsType: FieldsV1
@@ -50,6 +52,8 @@
         f:labels:
           .: {}
           f:app.kubernetes.io/managed-by: {}
+          f:app.kubernetes.io/version: {}
+          f:augmentcode.com/deployed-by: {}
       f:spec:
         .: {}
         f:commonName: {}
@@ -69,7 +73,7 @@
         f:usages: {}
     manager: kubectl-client-side-apply
     operation: Update
-    time: "2023-12-12T02:12:02Z"
+    time: "2023-12-12T18:36:47Z"
   name: embeddings-search-server-certificate
   namespace: dogfood
   resourceVersion: "3963505"
"""
    diff = kubecfg_diff.is_meaningful_diff(certificate_diff)
    assert not diff


def test_deployment_diff():
    deployment_diff = r"""--- /tmp/LIVE-998053870/apps.v1.Deployment.devtools.slack-bot	2023-12-12 23:10:20.178012008 +0000
+++ /tmp/MERGED-438954232/apps.v1.Deployment.devtools.slack-bot	2023-12-12 23:10:20.178012008 +0000
@@ -6,12 +6,12 @@
     kubectl.kubernetes.io/last-applied-configuration: |
       {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"slack-bot","app.kubernetes.io/managed-by":"kubecfg","app.kubernetes.io/version":"354e972a057ee149e33ef990a9dded4e6ccabae1","augmentcode.com/deployed-by":"deploy"},"name":"slack-bot","namespace":"devtools"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"slack-bot"}},"strategy":{"rollingUpdate":{"maxSurge":1,"maxUnavailable":0},"type":"RollingUpdate"},"template":{"metadata":{"labels":{"app":"slack-bot","app.kubernetes.io/managed-by":"kubecfg","app.kubernetes.io/version":"354e972a057ee149e33ef990a9dded4e6ccabae1","augmentcode.com/deployed-by":"deploy"}},"spec":{"containers":[{"env":[{"name":"SLACK_BOT_TOKEN","valueFrom":{"secretKeyRef":{"key":"slack_token","name":"slack-bot-token"}}}],"image":"us-central1-docker.pkg.dev/system-services-prod/build-images/devtools_slack_bot@sha256:699f945721e72b35ce7938eb5dd690943076c1a1a36065b4881f2e58ba67a91b","livenessProbe":{"initialDelaySeconds":15,"periodSeconds":20,"tcpSocket":{"port":50051}},"name":"bot","ports":[{"containerPort":50051,"name":"grpc-svc"}],"readinessProbe":{"initialDelaySeconds":5,"periodSeconds":10,"tcpSocket":{"port":50051}},"resources":{"limits":{"cpu":0.1,"memory":"100Mi"}},"volumeMounts":[{"mountPath":"/config","name":"config","readOnly":true}]}],"volumes":[{"configMap":{"items":[{"key":"config.json","path":"config.json"}],"name":"slack-bot-config"},"name":"config"}]}}}} # pragma: allowlist secret
-  creationTimestamp: "2023-12-12T01:23:33Z"
   creationTimestamp: "2023-12-12T01:23:33Z"
-  generation: 4
+  generation: 5
   labels:
     app: slack-bot
     app.kubernetes.io/managed-by: kubecfg
-    app.kubernetes.io/version: 354e972a057ee149e33ef990a9dded4e6ccabae1
-    augmentcode.com/deployed-by: deploy
+    app.kubernetes.io/version: 7b9629115971ce6e0c3fc49ea43178ad45205ca2-dirty
+    augmentcode.com/deployed-by: dirk
   name: slack-bot
   namespace: devtools
   resourceVersion: "5177867"
@@ -34,8 +34,8 @@
       labels:
         app: slack-bot
         app.kubernetes.io/managed-by: kubecfg
-        app.kubernetes.io/version: 354e972a057ee149e33ef990a9dded4e6ccabae1
-        augmentcode.com/deployed-by: deploy
+        app.kubernetes.io/version: 7b9629115971ce6e0c3fc49ea43178ad45205ca2-dirty
+        augmentcode.com/deployed-by: dirk
     spec:
       containers:
       - env:
@@ -44,7 +44,7 @@
             secretKeyRef:
               key: slack_token
               name: slack-bot-token
-        image: us-central1-docker.pkg.dev/system-services-prod/build-images/devtools_slack_bot@sha256:699f945721e72b35ce7938eb5dd690943076c1a1a36065b4881f2e58ba67a91b
+        image: us-central1-docker.pkg.dev/system-services-prod/build-images/devtools_slack_bot@sha256:2f2f07006c4fc5126ef76ac5b72495a4bab33947209163b25a281798d9f89e54
         imagePullPolicy: IfNotPresent
         livenessProbe:
           failureThreshold: 3
"""
    diff = kubecfg_diff.is_meaningful_diff(deployment_diff)
    assert diff


def test_extract_git_version():
    diff = pathlib.Path("tools/kubecfg/test_data/diff.txt")
    assert diff.exists()
    git_versions = kubecfg_diff.extract_git_versions(diff.read_text())
    assert git_versions == {"0fdb1e57d636fafe366abe4c29268b448329b13c"}

"""kubecfg deletion logic."""

from base.cloud.k8s.kubectl import (
    Kubectl,
    KubeObject,
    KubectlOutput,
    SUPPORTED_KINDS_SET,
    DRY_RUN_SKIP_KINDS_SET,
)
from tools.kubecfg.kubecfg import KubeCfgException
import typing
import logging
from dataclasses import dataclass


@dataclass
class KubecfgDeletionResult:
    """Result of a kubecfg deletion."""

    deleted: bool
    """True if the object was found and deleted."""

    kubecfg_outputs: list[KubectlOutput]
    """The output of the kubectl commands."""


class KubecfgDeletion:
    """Deletion logic for kubecfg."""

    def __init__(self, kubectl: Kubectl):
        self.kubectl = kubectl

    def _delete_object(
        self, obj: KubeObject, dry_run: bool
    ) -> typing.Tuple[bool, KubectlOutput | None]:
        logging.info("Delete object %s", obj)
        if dry_run:
            if self.kubectl.find_object(obj):
                logging.info("Dry run, skipping delete of object %s", obj)
            else:
                logging.info("Dry run, found nothing to delete for object %s", obj)
            return True, None
        r = self.kubectl.delete_object(obj)
        found = False
        if r.stdout:
            logging.info("%s", r.stdout)
            found = "deleted" in r.stdout
        if r.stderr:
            logging.error("%s", r.stderr)
        logging.info("Deleted tombstoned object %s", obj)
        return found, r

    def delete(
        self, api_version: str, kind: str, name: str, namespace: str, dry_run: bool
    ) -> KubecfgDeletionResult:
        """Tombstones an object.

        Args:
            api_version: The api version of the object.
            kind: The kind of the object.
            name: The name of the object.
            namespace: The namespace of the object.
            dry_run: Whether to dry run the tombstone.

        Returns:
            True if the object was found and deleted, False otherwise.
        """
        outputs: list[KubectlOutput] = []
        found = False
        kinds = None
        if dry_run:
            kinds = list(SUPPORTED_KINDS_SET - DRY_RUN_SKIP_KINDS_SET)
            logging.info(
                "Dry run, skipping looking for objects of kind(s): %s",
                DRY_RUN_SKIP_KINDS_SET,
            )
        if kind == "app":
            for o in self.kubectl.find_by_label(namespace, "app", name, kinds):
                r = self._delete_object(o, dry_run)
                if r[1]:
                    outputs.append(r[1])
                if r[0]:
                    found = True
            for o in self.kubectl.find_by_label(
                namespace, "app.kubernetes.io/name", name, kinds
            ):
                r = self._delete_object(o, dry_run)
                if r[1]:
                    outputs.append(r[1])
                if r[0]:
                    found = True
        else:
            if not api_version:
                raise KubeCfgException("Missing api_version for kubecfg deletion")
            r = self._delete_object(
                KubeObject(api_version, kind, name=name, namespace=namespace),
                dry_run,
            )
            if r[1]:
                outputs.append(r[1])
            if r[0]:
                found = True
        return KubecfgDeletionResult(found, outputs)

    def find(
        self, api_version: str, kind: str, name: str, namespace: str
    ) -> list[KubeObject]:
        """Find all objects that match the given parameters.

        Args:
            api_version: The api version of the object.
            kind: The kind of the object.
            name: The name of the object.
            namespace: The namespace of the object.

        Returns:
            The list of objects that match the parameters.
        """
        # find() is only used in dry runs today
        logging.debug(
            f"Looking for objects of {kind=} {name=} in {namespace=}",
        )
        kinds = list(SUPPORTED_KINDS_SET - DRY_RUN_SKIP_KINDS_SET)
        results = []
        if kind == "app":
            for o in self.kubectl.find_by_label(namespace, "app", name, kinds):
                results.append(o)
            for o in self.kubectl.find_by_label(
                namespace, "app.kubernetes.io/name", name, kinds
            ):
                results.append(o)
        else:
            if not api_version:
                raise KubeCfgException("Missing api_version for kubecfg deletion")
            obj = KubeObject(api_version, kind, name=name, namespace=namespace)
            if self.kubectl.find_object(obj):
                results.append(obj)
        return results

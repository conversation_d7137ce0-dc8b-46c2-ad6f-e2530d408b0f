load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:go.bzl", "go_binary")

go_binary(
    name = "validate_namespace_config",
    srcs = ["validate_namespace_config.go"],
    importpath = "github.com/augmentcode/augment/tools/kubecfg/validate_namespace_config",
    visibility = ["//visibility:public"],
    deps = [
        "@google_jsonnet_go//:go_default_library",
    ],
)

py_library(
    name = "kubecfg_diff",
    srcs = ["kubecfg_diff.py"],
)

pytest_test(
    name = "kubecfg_diff_test",
    srcs = ["kubecfg_diff_test.py"],
    data = glob(["test_data/*"]),
    deps = [
        ":kubecfg_diff",
    ],
)

py_library(
    name = "kubecfg_lib",
    srcs = [
        "kubecfg.py",
        "pusher.py",
    ],
    data = [
        "//deploy/tenants:tokens",
        "//deploy/tenants/namespace_configs:namespace-configs",
        "@com_github_google_go_containerregistry//cmd/crane",
        "@google_jsonnet_go//cmd/jsonnet",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//base/cloud/k8s:kubectl",
        requirement("pyyaml"),
        requirement("GitPython"),
        "//base/python/cloud",
    ],
)

py_library(
    name = "kubecfg_deletion",
    srcs = ["kubecfg_deletion.py"],
    visibility = ["//tools/deploy_runner:__subpackages__"],
    deps = [
        ":kubecfg_lib",
        "//base/cloud/k8s:kubectl",
    ],
)

pytest_test(
    name = "kubecfg_lib_test",
    srcs = ["kubecfg_test.py"],
    data = glob(["test_data/*"]),
    deps = [
        ":kubecfg_lib",
    ],
)

py_binary(
    name = "kubecfg",
    srcs = ["kubecfg_util.py"],
    data = [
        "//deploy/tenants/namespace_configs:namespace-configs",
        "@bazel_tools//tools/bash/runfiles",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":kubecfg_diff",
        ":kubecfg_lib",
        requirement("InquirerPy"),
        "//base/logging:console_logging",
    ],
)

py_binary(
    name = "kubecfg_deletion_util",
    srcs = ["kubecfg_deletion_util.py"],
    deps = [
        requirement("InquirerPy"),
        ":kubecfg",
        ":kubecfg_deletion",
        "//base/cloud/k8s:kubectl_factory",
        "//base/logging:console_logging",
    ],
)

py_library(
    name = "kubecfg_test_lib",
    srcs = ["kubecfg_test_lib.py"],
    data = [
        "@com_github_yannh_kubeconform//cmd/kubeconform",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":kubecfg_lib",
        "//base/logging:console_logging",
    ],
)

py_binary(
    name = "kubecfg_test_util",
    testonly = True,
    srcs = ["kubecfg_test_util.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":kubecfg_lib",
        ":kubecfg_test_lib",
        "//base/logging:console_logging",
    ],
)

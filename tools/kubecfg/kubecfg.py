"""Library to handle kubecfg-style configuration.

Usually embedded into a binary, e.g. CLI and exposed
via a Bazel kubecfg target.
"""

import functools
import json
import logging
import os
import pathlib
import subprocess
import sys
import tempfile
import typing
from contextlib import contextmanager
from dataclasses import dataclass
from typing import Any, Mapping, Optional, Sequence, Union

import git
import yaml

from base.cloud.k8s import kubectl

# mapping from cloud to image prefix (i.e. container registry)
IMAGE_PREFIX = {
    "GCP_US_CENTRAL1_DEV": "us-central1-docker.pkg.dev/system-services-dev/build-images/",
    "GCP_US_CENTRAL1_PROD": "us-central1-docker.pkg.dev/system-services-prod/build-images/",
    "GCP_US_CENTRAL1_GSC_PROD": "us-central1-docker.pkg.dev/system-services-prod-gsc/build-images-us-c1-gsc/",
    "GCP_EU_WEST4_PROD": "europe-west4-docker.pkg.dev/system-services-prod/build-images-eu-w4/",
}
IMAGE_SUFFIX = {
    "DEV": "dev",
    "STAGING": "main",
    "PROD": "release",
}

# Where staging/production namespace configurations live.
NAMESPACE_CONFIG_DIR = "deploy/tenants/namespace_configs"
# Where the default dev namespace configuration lives.
# Fallback if there is no config for the user namespace.
DEV_DEFAULTS_NAMESPACE_CONFIG = "deploy/tenants/namespace_configs/dev-defaults.jsonnet"


def _get_namespace_config_path(env: str) -> str | None:
    """Returns the path to the namespace config file."""
    if env == "DEV":
        return DEV_DEFAULTS_NAMESPACE_CONFIG
    elif env == "PROD":
        return None
    elif env == "STAGING":
        return None
    else:
        assert False, f"Unknown environment {env}"


def str_presenter(dumper, data):
    if "\n" in data:
        return dumper.represent_scalar("tag:yaml.org,2002:str", data, style="|")
    return dumper.represent_scalar("tag:yaml.org,2002:str", data, style='"')


# Add the custom represent function to the dumper
yaml.representer.SafeRepresenter.add_representer(str, str_presenter)


JSONNET_BIN = "../jsonnet_go~/cmd/jsonnet/jsonnet_/jsonnet"


def _get_jsonnet_path() -> str:
    """Returns the path to the jsonnet binary."""
    p = pathlib.Path.cwd().joinpath(JSONNET_BIN).resolve()
    assert p.exists()
    return str(p)


def _run_jsonnet(args, base_directory: pathlib.Path):
    """Runs jsonnet with the given arguments.

    Args:
      args: List of arguments to pass to jsonnet.

    Returns:
      The stdout of the jsonnet process.

    Raises:
      ValueError: If the jsonnet process returns a non-zero exit code.
    """
    assert base_directory.is_dir()
    cmd = [_get_jsonnet_path(), "-y", "-J", base_directory] + args
    with subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        encoding="utf-8",
        cwd=base_directory,
    ) as p:
        stdout, stderr = p.communicate()
        if p.returncode != 0:
            raise KubeCfgException(
                "Failed to generate kubecfg configuration",
                stdout=stdout,
                stderr=stderr,
                command=[str(c) for c in cmd],
            )
        return stdout


def _visit(config, parents: list[Union[str, int]], dict_callback):
    """Visits a JSON-document config and calls a callback for each item.

    Args:
      config: The config to visit.
      parents: The parents of the config.
      dict_callback: The callback to call for each item.

    Returns:
      The visited config.
    """
    if isinstance(config, dict):
        r = {}
        for k, v in config.items():
            parents.append(k)
            inner = _visit(v, parents, dict_callback=dict_callback)
            parents.pop()
            value = dict_callback(k, v, inner, parents)
            if value is None:
                r[k] = inner
            else:
                r[value[0]] = value[1]
        return r
    elif isinstance(config, list):
        r = []
        for i, v in enumerate(config):
            parents.append(i)
            r.append(_visit(v, parents, dict_callback=dict_callback))
            parents.pop()
        return r
    else:
        return config


class KubeCfgException(Exception):
    """Exception raised when a command fails."""

    def __init__(
        self,
        msg,
        stdout: Optional[str] = None,
        stderr: Optional[str] = None,
        command: Optional[list[str]] = None,
    ):
        """Initializes the exception.

        Args:
          msg: The message to print.
          stdout: The stdout of the command.
          stderr: The stderr of the command.
        """
        super().__init__(msg)
        self.msg = msg
        self.stdout = stdout
        self.stderr = stderr
        self.command = command


class KubeCfgConfig:
    """Config for a kubecfg command."""

    def __init__(
        self,
        kubectl: kubectl.Kubectl,
        config_file: str,
        extra_kubectl_args: Sequence[str] = tuple(),
    ):
        """Initializes the config.

        Args:
          kubecfg: The kubecfg command.
          cloud: the cloud to deploy to.
          config_file: The config file to use.
          extra_kubectl_args: Extra arguments to pass to the kubectl command.
        """
        self.config_file = pathlib.Path(config_file)
        self.extra_kubectl_args = extra_kubectl_args
        self.kubectl = kubectl

    def diff(self) -> kubectl.KubectlOutput:
        """Returns the diff between the current config and the config file.

        Note: will return a non-zero return code if failing instead of raising an exception.

        Returns:
          The diff between the current config and the config file.
        """
        return self.kubectl.diff(self.config_file, check=False)

    def apply(self) -> kubectl.KubectlOutput:
        """Applies the config file.

        Note: will return a non-zero return code if failing instead of raising an exception.

        Returns:
          The stdout, stderr, and return code of the command.
        """
        return self.kubectl.apply(
            self.config_file, check=False, extra_args=self.extra_kubectl_args
        )

    def delete(self) -> kubectl.KubectlOutput:
        """Deletes the config.

        Note: will return a non-zero return code if failing instead of raising an exception.

        Returns:
          The stdout, stderr, and return code of the command.
        """
        return self.kubectl.delete(
            self.config_file, check=False, extra_args=self.extra_kubectl_args
        )

    def dump(self) -> str:
        """Dumps the config file.

        Returns:
          The config file contents.
        """
        return pathlib.Path(self.config_file).read_text(encoding="utf-8")


@dataclass(frozen=True)
class VisitConfig:
    """Config for the visit function."""

    # if set to true, the version and user information are added to the config.
    stamp: bool = False

    # if set to true, the config is rewritten.
    rewrite: bool = True


class KubeConfigRewriter:
    """Class to rewrite the config."""

    def __init__(
        self,
        env: str | None,
        cloud: str,
        namespace: str | None,
        current_git_hash: str,
        deployed_by: str | None,
        deployment_target_name: str | None,
        push_fn: typing.Callable[[str, str], str] | None,
    ):
        """Initializes the rewriter.

        Args:
            env: the environment to deploy to.
            cloud: the cloud to deploy to.
            namespace: the namespace to deploy to.
            current_git_hash: the current git hash.
            deployed_by: the user or pod name that deployed the config.
            push_fn: the function to push the image.
        """
        self.env = env
        self.cloud = cloud
        self.namespace = namespace
        self.current_git_hash = current_git_hash
        self.deployed_by = deployed_by
        self.deployment_target_name = deployment_target_name
        self.push_fn = push_fn

    def _visit_target(
        self,
        config: VisitConfig,
        key,
        value,
        inner,
        parents: list[Union[str, int]],  # pylint: disable=unused-argument
    ) -> tuple[str, typing.Any] | None:
        """Visits a target and pushes the image.

        Args:
            key: the current dict key
            config: Visitor configuration, e.g. if images should be pushed
            value: The current dict value
            inner: the value after evaluating the value
            parents: the list of parents: str for dict and int for lists

        """
        if key == "target" and value.get("name"):
            logging.debug("Found target %s: %s", value["name"], value["dst"])
            dst = value["dst"]
            suffix = IMAGE_SUFFIX[self.env or "STAGING"]
            full_dst = f"{IMAGE_PREFIX[self.cloud]}{dst}:{suffix}"
            if self.push_fn:
                digest = self.push_fn(value["name"], full_dst)
            else:
                digest = full_dst
            return ("image", digest)
        if isinstance(inner, dict) and "kubecfg" in inner:
            assert len(inner) == 1
            kubecfg_value = inner["kubecfg"]
            logging.debug(
                "Found target %s: %s",
                kubecfg_value["image_push"],
                kubecfg_value["image_name"],
            )
            dst = kubecfg_value["image_name"]
            suffix = IMAGE_SUFFIX[self.env or "main"]
            full_dst = f"{IMAGE_PREFIX[self.cloud]}{dst}:{suffix}"
            if self.push_fn:
                digest = self.push_fn(kubecfg_value["image_push"], full_dst)
            else:
                digest = full_dst
            return (key, digest)
        if (
            key == "metadata"
            and (len(parents) == 1 or parents[-1] == "template")
            and not ("labels" in inner and "augmentcode/no-stamp" in inner["labels"])
        ):
            # Note (dirk): do not add labels in a GCP node group config
            if "labels" not in inner or not inner["labels"]:
                inner["labels"] = {}
            # see https://kubernetes.io/docs/reference/labels-annotations-taints/
            if config.stamp:
                inner["labels"]["app.kubernetes.io/version"] = self.current_git_hash
                if self.deployed_by:
                    inner["labels"]["augmentcode.com/deployed-by"] = self.deployed_by
                if self.deployment_target_name:
                    inner["labels"]["augmentcode.com/deployment-target"] = (
                        self.deployment_target_name
                    )
            inner["labels"]["app.kubernetes.io/managed-by"] = "kubecfg"
            return (key, inner)

        return None

    def rewrite_config(self, config_objects, config: VisitConfig):
        """Rewrites the config.

        Args:
            config_objects: the config objects
            config: the config for the visit function.
        """
        visit_target = functools.partial(self._visit_target, config)
        return _visit(config_objects, [], visit_target)


class KubeCfg:
    """Class to capture the behavior of kubcfg."""

    def __init__(
        self,
        files: list[str],
        cloud: str,
        cluster_wide: bool,
        env: Optional[str],
        namespace: Optional[str],
        base_directory: pathlib.Path,
        push_fn: typing.Callable[[str, str], str] | None,
        deployed_by: str | None = None,
        deployment_target_name: str | None = None,
        kube_config_file: Optional[pathlib.Path] = None,
        extra_kubectl_args: Sequence[str] = tuple(),
        extra_config_args: Sequence[tuple[str, str]] = tuple(),
        namespace_config_path: pathlib.Path | None = None,
    ):
        """Initializes the class.

        Args:
            files: the jsonnet files to use.
            cloud: the cloud to deploy to.
            cluster_wide: if set to true, env and namespace are not used. The configurations are one-time configurations on the cluster.
            env: The environment to use (dev or staging or prod)
            namespace: The namespace to use. If set to None and the env is dev, the user namespace is used
            base_directory: The base directory to use.
            deployed_by: The user or pod name that deployed the configuration.
            kube_config_file: The kube config file to use.
            extra_kubectl_args: Extra arguments to pass to kubectl.
            extra_config_args: Extra arguments to pass to the jsonnet compiler.
            push_fn: A callable that is called with the image bazel target and the destination suffix and returns the image digest. If not set, the image is not pushed
        """
        if not cluster_wide:
            assert env
            if namespace is None:
                if cloud == "GCP_US_CENTRAL1_DEV":
                    if env == "DEV":
                        namespace = kubectl.get_dev_namespace()
                    else:
                        raise KubeCfgException("namespace must be set")
                elif cloud.startswith("GCP_") or cloud.startswith("CW_"):
                    raise KubeCfgException("namespace must be set")

        else:
            assert not env
            assert not namespace
        self.deployed_by = deployed_by
        self.deployment_target_name = deployment_target_name
        self.files = files
        self.cluster_wide = cluster_wide
        self.cloud = cloud
        self.env = env
        self.namespace = namespace
        self.base_directory = base_directory
        if kube_config_file is None:
            self.kube_config_file = _get_kube_config_file()
        else:
            self.kube_config_file = kube_config_file
        self.extra_kubectl_args = extra_kubectl_args
        self.extra_config_args = extra_config_args

        # Get the namespace config file path. This is not guaranteed to exist. Namespaces without a
        # config file will default to an empty config.
        if namespace_config_path:
            self.namespace_config_path = namespace_config_path
            if not self.namespace_config_path.is_file():
                raise KubeCfgException(
                    f"Namespace config file {self.namespace_config_path} does not exist or is not a file"
                )
        else:
            self.namespace_config_path = pathlib.Path(
                self.base_directory,
                NAMESPACE_CONFIG_DIR,
                f"{self.namespace}.jsonnet",
            )
            assert pathlib.Path(self.base_directory, NAMESPACE_CONFIG_DIR).is_dir()

            if not self.namespace_config_path.exists() and self.env is not None:
                path = _get_namespace_config_path(self.env)
                if not path:
                    raise KubeCfgException(
                        f"Namespace config file {self.namespace_config_path} does not exist and there is no default config for {self.env}"
                    )
                self.namespace_config_path = pathlib.Path(self.base_directory, path)

        self._current_hash = None

        self.rewriter = KubeConfigRewriter(
            env,
            cloud,
            self.namespace,
            self.get_current_git_hash(),
            deployed_by=self.deployed_by,
            deployment_target_name=self.deployment_target_name,
            push_fn=push_fn,
        )

    @classmethod
    def create_from_json(
        cls,
        json_data: dict,
        base_directory: pathlib.Path,
        cloud: str,
        env: str | None,
        namespace: str,
        push_fn: typing.Callable[[str, str], str],
        deployed_by: str | None = None,
        deployment_target_name: str | None = None,
        kube_config_file: Optional[pathlib.Path] = None,
        extra_config_args: Sequence[tuple[str, str]] = tuple(),
    ):
        """Creates a KubeCfg from a jsonnet file."""
        return cls(
            files=[json_data["src"]],
            cloud=cloud,
            cluster_wide=json_data["cluster_wide"],
            env=env,
            namespace=namespace,
            base_directory=base_directory,
            deployed_by=deployed_by,
            deployment_target_name=deployment_target_name,
            kube_config_file=kube_config_file,
            extra_kubectl_args=json_data["extra_kubectl_args"],
            extra_config_args=extra_config_args,
            push_fn=push_fn,
        )

    def get_current_git_hash(self) -> str:
        """Returns the current git hash."""
        if self._current_hash is None:
            try:
                repo = git.Repo(self.base_directory)  # type: ignore
                self._current_hash = "{}{}".format(
                    repo.head.commit, "-dirty" if repo.is_dirty() else ""
                )
            except git.InvalidGitRepositoryError:
                self._current_hash = "unknown"
        return self._current_hash

    @contextmanager
    def get_config(
        self, config: VisitConfig | None, extra_args: Optional[Mapping[str, Any]] = None
    ) -> typing.Generator[KubeCfgConfig, None, None]:
        """Returns a config object.

        Args:
            config: The config to use.
            extra_args: Extra arguments to pass to the jsonnet compiler.
        """
        if not config:
            config = VisitConfig()
        config_objects = []
        for file in self.files:
            if file.endswith(".jsonnet"):
                cmd = [
                    "-J",
                    str(self.base_directory),
                    file,
                ]
                cmd.extend(
                    [
                        "--tla-str",
                        f"cloud={self.cloud.upper()}",
                    ]
                )
                if self.env:
                    cmd.extend(
                        [
                            "--tla-str",
                            f"env={self.env.upper()}",
                        ]
                    )
                if self.namespace:
                    cmd.extend(
                        [
                            "--tla-str",
                            f"namespace={self.namespace.lower()}",
                        ]
                    )

                    if self.namespace_config_path.is_file():
                        cmd.extend(
                            [
                                "--tla-code-file",
                                f"namespace_config={str(self.namespace_config_path)}",
                            ]
                        )
                    else:
                        logging.info(
                            "No namespace config file for %s (looked for %s). Defaulting to an empty config.",
                            self.namespace,
                            str(self.namespace_config_path),
                        )
                        cmd.extend(["--tla-code", "namespace_config={}"])
                if self.extra_config_args:
                    for arg in self.extra_config_args:
                        cmd.append("--tla-str")
                        cmd.append(f"{arg[0]}={arg[1]}")
                if extra_args:
                    for k, v in extra_args.items():
                        cmd.append("--tla-code")
                        cmd.append(f"{k}={json.dumps(v)}")
                yaml_output = _run_jsonnet(args=cmd, base_directory=self.base_directory)
                d = list(yaml.safe_load_all(yaml_output))
                config_objects.extend([obj for obj in d if obj])
            elif file.endswith(".yaml"):
                if not self.cluster_wide:
                    raise KubeCfgException(
                        "Cannot use yaml files in non-cluster_wide mode"
                    )
                with pathlib.Path(self.base_directory, file).open(
                    encoding="utf-8"
                ) as f:
                    config_objects.extend(list(yaml.safe_load_all(f)))
            else:
                raise KubeCfgException(f"Unsupported file type: {file}")
        if config.rewrite:
            config_objects = self.rewriter.rewrite_config(config_objects, config)
        with tempfile.NamedTemporaryFile(mode="w+", encoding="utf-8") as f:
            try:
                yaml.safe_dump_all(config_objects, f)
                current_kubectl = kubectl.create_kubectl(
                    self.cloud, self.kube_config_file
                )
                yield KubeCfgConfig(
                    current_kubectl,
                    f.name,
                    extra_kubectl_args=self.extra_kubectl_args,
                )
            except KubeCfgException:
                yaml.safe_dump_all(config_objects, sys.stdout)
                raise


def _get_kube_config_file() -> pathlib.Path | None:
    """Returns the kube config file to use or None if no kube config file was found."""
    if (
        "HOME" in os.environ
        and pathlib.Path(os.environ["HOME"]).joinpath(".kube", "config").exists()
    ):
        return pathlib.Path(os.environ["HOME"]).joinpath(".kube", "config")
    if (
        "USER" in os.environ
        and pathlib.Path("/home", os.environ["USER"])
        .joinpath(".kube", "config")
        .exists()
    ):
        return pathlib.Path("/home", os.environ["USER"]).joinpath(".kube", "config")
    return None

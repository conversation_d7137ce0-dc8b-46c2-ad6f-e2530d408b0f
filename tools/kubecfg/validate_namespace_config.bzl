load("@rules_jsonnet//jsonnet:jsonnet.bzl", "JsonnetLibraryInfo")

def _namespace_config_test_impl(ctx):
    transitive_sources = []
    for dep in ctx.attr.srcs:
        transitive_sources.append(dep[JsonnetLibraryInfo].transitive_jsonnet_files)

    jsonnet_command = " ".join(
        ["%s" % ctx.executable.tool.short_path] +
        [src.short_path for src in ctx.files.srcs],
    )

    command = [
        "#!/bin/bash",
        jsonnet_command,
    ]

    ctx.actions.write(
        output = ctx.outputs.executable,
        content = "\n".join(command),
        is_executable = True,
    )

    test_inputs = (
        ctx.files.srcs + [ctx.executable.tool] +
        depset(transitive = transitive_sources, order = "postorder").to_list()
    )

    return [DefaultInfo(
        runfiles = ctx.runfiles(
            files = test_inputs,
            collect_data = True,
        ),
    )]

namespace_config_test = rule(
    _namespace_config_test_impl,
    attrs = {
        "srcs": attr.label_list(
            allow_files = True,
        ),
        "tool": attr.label(
            default = Label("//tools/kubecfg:validate_namespace_config"),
            cfg = "target",
            executable = True,
        ),
    },
    executable = True,
    test = True,
)

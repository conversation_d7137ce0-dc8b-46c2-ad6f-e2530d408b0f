import logging
import subprocess
import pathlib
import json
import os

from tools.kubecfg.kubecfg import KubeCfgException

CRANE_BIN = (
    "../gazelle~~go_deps~com_github_google_go_containerregistry/cmd/crane/crane_/crane"
)


def _get_crane_path() -> str:
    return CRANE_BIN


assert os.path.exists(_get_crane_path())


def _get_runfile_from_target(target: str, base_dir: pathlib.Path) -> pathlib.Path:
    """Returns the path to the binary to execute for a given target name.

    Args:
      target: The bazel target to get the runfile from.

    Returns:
      The path to the binary to execute
    """
    assert target.startswith("//")
    runfile = target[2:]
    return base_dir / pathlib.Path(runfile.replace(":", "/"))


def dependency_push_image(
    target: str, dst: str, base_dir: pathlib.Path = pathlib.Path.cwd()
):
    """Pushes the image and returns the image digest sha.

    The image is picked as build shell file from the runfiles location.
    That means that the target has to be a dependency of the current bazel target.

    Args:
      target: The bazel target to push.
      dst: The destination to push the image to.

    Returns:
      The image digest sha.
    """
    run_file = _get_runfile_from_target(target, base_dir=base_dir)
    if run_file.joinpath("index.json").exists():
        with run_file.joinpath("index.json").open("r", encoding="utf-8") as f:
            index = json.load(f)
            digest = index["manifests"][0]["digest"]
            logging.debug("Digest '%s'", digest)

        env = os.environ.copy()

        check_cmd = [_get_crane_path(), "digest", f"{dst}"]
        r = subprocess.run(
            check_cmd,
            capture_output=True,
            encoding="utf-8",
            check=False,
            env=env,
        )
        if r.stdout.strip() != digest:
            logging.debug("Found digest mismatch %s, pushing image", r.stdout.strip())
            cmd = [_get_crane_path(), "push", run_file, f"{dst}"]
            logging.debug("cmd %s", cmd)

            r = subprocess.run(
                cmd,
                capture_output=True,
                encoding="utf-8",
                check=False,
                env=env,
            )
            if r.returncode != 0:
                raise KubeCfgException(
                    "Failed to push image",
                    stdout=r.stdout,
                    stderr=r.stderr,
                    command=[str(c) for c in cmd],
                )
        else:
            logging.debug("Image already pushed, returning digest %s", digest)
        return f"{dst.rpartition(':')[0]}@{digest}"
    else:
        raise KubeCfgException(f"Missing index.json in {run_file}")

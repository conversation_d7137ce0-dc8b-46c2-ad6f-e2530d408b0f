package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/google/go-jsonnet"
)

func main() {
	flag.Parse()

	vm := jsonnet.MakeVM()
	importer := &jsonnet.FileImporter{
		JPaths: []string{"."},
	}
	vm.Importer(importer)

	failureList := []string{}
	successList := []string{}
	for i := range flag.NArg() {
		file := flag.Arg(i)
		fmt.Println("Validating", file)
		if err := validateNamespaceConfig(vm, file); err != nil {
			fmt.Printf("Validation failed for %s: %v\n", file, err)
			failureList = append(failureList, file)
		} else {
			fmt.Printf("Validation passed for %s\n", file)
			successList = append(successList, file)
		}
	}
	if len(successList) == 0 {
		fmt.Println("No namespace configs to validate")
		os.Exit(1)
	}
	if len(failureList) > 0 {
		fmt.Println("Validation failed for the following files:")
		for _, filePath := range failureList {
			fmt.Println(filePath)
		}
		os.Exit(1)
	}
	fmt.Println("All namespace configs validated successfully")
	os.Exit(0)
}

func validateNamespaceConfig(vm *jsonnet.VM, filePath string) error {
	// Evaluate the jsonnet code
	output, err := vm.EvaluateFile(filePath)
	if err != nil {
		fmt.Println(output)
		return fmt.Errorf("jsonnet evaluation failed: %v", err)
	}
	return nil
}

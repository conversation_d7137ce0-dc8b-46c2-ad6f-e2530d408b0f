"""Tests for the kubecfg library."""

import json
import pathlib

from tools.kubecfg import kubecfg


def _rewriter(objs, config: kubecfg.VisitConfig):
    rewriter = kubecfg.KubeConfigRewriter(
        env="DEV",
        cloud="GCP_US_CENTRAL1_DEV",
        namespace="default",
        current_git_hash="1234567890",
        deployed_by="user1",
        push_fn=lambda src, dst: "image@sha256:1234567890",
        deployment_target_name="test-selection",
    )
    actual = rewriter.rewrite_config(objs, config=config)
    return actual


def test_kubecfg():
    """Tests the kubecfg library with a basic case."""
    cert_obj = json.load(
        pathlib.Path("tools/kubecfg/test_data/certificate.json").open(encoding="utf-8")
    )
    expected = json.load(
        pathlib.Path("tools/kubecfg/test_data/certificate_test_kubecfg.json").open(
            encoding="utf-8"
        )
    )

    actual = _rewriter([cert_obj], config=kubecfg.VisitConfig())
    print("EXPECTED")
    print(json.dumps(expected, indent=2))
    print("ACTUAL")
    print(json.dumps(actual, indent=2))
    assert actual == expected


def test_kubecfg_stamping():
    """Tests the kubecfg library with stamping."""
    cert_obj = json.load(
        pathlib.Path("tools/kubecfg/test_data/certificate.json").open(encoding="utf-8")
    )
    expected = json.load(
        pathlib.Path(
            "tools/kubecfg/test_data/certificate_test_kubecfg_stamping.json"
        ).open(encoding="utf-8")
    )

    actual = _rewriter([cert_obj], config=kubecfg.VisitConfig(stamp=True))
    print("EXPECTED")
    print(json.dumps(expected, indent=2))
    print("ACTUAL")
    print(json.dumps(actual, indent=2))
    assert actual == expected


def test_kubecfg_push():
    """Tests the kubecfg library and image target."""
    cert_obj = json.load(
        pathlib.Path("tools/kubecfg/test_data/deployment.json").open(encoding="utf-8")
    )
    expected = json.load(
        pathlib.Path("tools/kubecfg/test_data/deployment_test_kubecfg_push.json").open(
            encoding="utf-8"
        )
    )

    actual = _rewriter([cert_obj], config=kubecfg.VisitConfig(stamp=True))
    print("EXPECTED")
    print(json.dumps(expected, indent=2))
    print("ACTUAL")
    print(json.dumps(actual, indent=2))
    assert actual == expected

// jsonnet file to run a shell for maintainance and debugging
local gcpInfo = import 'deploy/common/cloud_info.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(cloud, env, namespace, resource, count=1, namespace_config)
  local cloudInfo = gcpInfo[cloud];
  assert env == 'DEV';
  local tolerations = nodeLib.tolerations(resource=resource, count=count, env='DEV', cloud=cloud);
  local affinity = nodeLib.affinity(resource=resource, count=count, env='DEV', cloud=cloud, appName=null);
  [
    {
      apiVersion: 'v1',
      kind: 'PersistentVolumeClaim',
      metadata: {
        name: 'cbazel-dev-pvc',
        namespace: namespace,
      },
      spec: {
        accessModes: [
          'ReadWriteOnce',
        ],
        resources: {
          requests: {
            storage: '256Gi',
          },
        },
        storageClassName: 'premium-rwo',
      },
    },
    {
      apiVersion: 'v1',
      kind: 'Pod',
      metadata: {
        name: 'cbazel-dev',
        namespace: namespace,
      },
      spec: {
        serviceAccountName: 'support-sa',
        securityContext: {
          runAsUser: 0,
          fsGroup: 1000,
          fsGroupChangePolicy: 'OnRootMismatch',
        },
        tolerations: tolerations,
        affinity: affinity,
        containers: [
          {
            name: 'cbazel',
            // we only have base-images in the dev project
            image: {
              GCP_US_CENTRAL1_DEV: 'us-central1-docker.pkg.dev/system-services-dev/base-images/cbazel:latest',
              GCP_US_CENTRAL1_PROD: 'us-central1-docker.pkg.dev/%s/devtools-repository/cbazel:latest' % cloudInfo.projectId,
              GCP_EU_WEST4_PROD: 'europe-west4-docker.pkg.dev/%s/devtools-repository-eu-w4/cbazel:latest' % cloudInfo.projectId,
              GCP_US_CENTRAL1_GSC_PROD: 'us-central1-docker.pkg.dev/%s/devtools-repository/cbazel:latest' % cloudInfo.projectId,
            }[cloud],
            command: ['/bin/bash'],
            args: ['-c', 'trap : TERM INT; sleep infinity & wait'],
            workingDir: '/root/augment',
            volumeMounts: [
              {
                name: 'persistent-storage',
                mountPath: '/mnt/efs/augment',
              },
              {
                mountPath: '/root',
                name: 'cache-volume',
              },
            ],
            resources: {
              limits: {
                cpu: 7 * count,
                'nvidia.com/gpu': count,
                memory: '%sGi' % (28 * count),
              },
            },
          },
        ],
        volumes: [
          {
            name: 'persistent-storage',
            persistentVolumeClaim: {
              claimName: 'filestore-checkpoint-claim',
            },
          },
          {
            name: 'cache-volume',
            persistentVolumeClaim: {
              claimName: 'cbazel-dev-pvc',
            },
          },
        ],
        restartPolicy: 'Never',
      },
    },
  ]

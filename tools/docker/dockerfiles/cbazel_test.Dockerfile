FROM nvidia/cuda:12.4.0-runtime-ubuntu22.04
ENV DEBIAN_FRONTEND=noninteractive

#### System packages
# added xvfb for headless testing
RUN apt-get update -y && \
    apt-get install -y \
    tini \
    git \
    file \
    python3 \
    sudo \
    gcc \
    g++ \
    libtinfo5 \
    tzdata \
    unzip \
    zip \
    wget curl \
    # Used for end-to-end VSCode testing
    xvfb \
    # Needed by Chromedriver for end-to-end VSCode testing
    libnss3 \
    # Add libraries for VSCode
    libasound2 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libatk-bridge2.0-0 \
    libcairo2 \
    libgtk-3-0 \
    libgbm1 \
    libpango-1.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxkbcommon0 \
    libxrandr2 \
    zstd software-properties-common \
    unzip ca-certificates \
    libssl-dev pkg-config zip && \
    apt-get upgrade -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install protoc
ADD protoc.sha256 protoc.sha256
RUN wget http://github.com/protocolbuffers/protobuf/releases/download/v25.0-rc2/protoc-25.0-rc-2-linux-x86_64.zip && \
    sha256sum -c protoc.sha256 && \
    unzip ./protoc-25.0-rc-2-linux-x86_64.zip -d /usr/local && \
    rm protoc-25.0-rc-2-linux-x86_64.zip

#### User account
RUN useradd --create-home --uid 1000 --shell /bin/bash ubuntu && \
    usermod -aG sudo ubuntu && \
    echo "ubuntu ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

USER ubuntu

WORKDIR /home/<USER>

# we need the gsutil tool for cloud_archive
ENV GCLOUD_CLI_TAR=google-cloud-cli-504.0.1-linux-x86_64.tar.gz
ADD gcloud.sha256 gcloud.sha256
RUN curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/$GCLOUD_CLI_TAR && \
    sha256sum -c gcloud.sha256 && \
    tar -xzf $GCLOUD_CLI_TAR && \
    rm $GCLOUD_CLI_TAR && \
    sudo mv google-cloud-sdk /usr/local && \
    cd /usr/local/google-cloud-sdk && \
    yes | sudo ./install.sh && \
    echo "source /usr/local/google-cloud-sdk/completion.bash.inc"  >>~/.bashrc && \
    echo "source /usr/local/google-cloud-sdk/path.bash.inc"  >>~/.bashrc

# Dockerfiles

This setup build container images via the `rules_oci` of <PERSON><PERSON>.
However, this approach fails in some circumstances.

This directory tracks container image generation via `Dockerfile`s.
Usually, these are then fed into <PERSON>zel via a `oci_pull`.

## Prepare

Run
```
sudo apt install docker-buildx
```

## Images

After changing the `Dockerfile`s, you need to run `update_base_images.sh` to build and push the images.

```bazel
bazel run //tools/docker:update_base_images
```

and update `base_images.bzl` with the output.

Without these steps, any changes to the `Dockerfile`s will not be reflected in the Bazel build.

To check if the update has introduced any vulnerabilities, run

```bash
NO_BAZEL_PRINT=true bazel query 'kind("trivy_test", //...)' |xargs bazel test --test_tag_filters=+postmerge-test
```

# Script to update the base images.
#
# The script assumes docker is installed and logged in.
# So it cannot run in a docker container or Kubernetes pod.
#
set -euo pipefail
TAG=$(date +'%Y%m%d')

IMAGE_NAMES=("cbazel" "cbazel_test" "cuda" "ubuntu_ci" "ubuntu" "ubuntu_git" "ubuntu_tini")
set DOCKER_BUILDKIT=0

for NAME in "${IMAGE_NAMES[@]}"; do
	echo "Building $NAME"
	docker build -f $BUILD_WORKSPACE_DIRECTORY/tools/docker/dockerfiles/$NAME.Dockerfile $BUILD_WORKSPACE_DIRECTORY/tools/docker/dockerfiles/ -t us-central1-docker.pkg.dev/system-services-dev/base-images/$NAME:$TAG
	docker push us-central1-docker.pkg.dev/system-services-dev/base-images/$NAME:$TAG
done

echo
echo "Update tools/docker/base_images.bzl to use the new base images"
echo
echo """""" Bazel module to pull all the base containers """

load("@rules_oci//oci:pull.bzl", "oci_pull")

def pull_base_containers():"""

for NAME in "${IMAGE_NAMES[@]}"; do
	DIGEST=$(docker inspect --format='{{index .RepoDigests 0}}' us-central1-docker.pkg.dev/system-services-dev/base-images/$NAME:$TAG | cut -d'@' -f2)

	echo "
    oci_pull(
        name = \"${NAME}_base_image\",
        digest = \"$DIGEST\",
        registry = \"us-central1-docker.pkg.dev\",
        repository = \"system-services-dev/base-images/$NAME\",
    )"

done

"""<PERSON><PERSON>t to get the request id from a Slack url."""

import argparse
from dataclasses import dataclass
import re

from google.cloud import bigquery


@dataclass
class MessageInfo:
    channel: str
    timestamp: str


def get_message_info(url: str) -> MessageInfo:
    """Get the Slack message information from a Slack url.

    Example of a Slack url:
    https://augment-wic8570.slack.com/archives/D07PS6GS9RR/p1727983851887989?thread_ts=1727983847.209999&cid=D07PS6GS9RR
    """
    pattern = r"/archives/(.+)/p(\d+)(\?|$)"
    match = re.search(pattern, url)
    if match:
        return MessageInfo(match.group(1), match.group(2))
    else:
        raise ValueError("Could not find message info in the provided URL")


def get_request_ids_from_url(
    bigquery_client: bigquery.Client, dataset: str, url: str
) -> list[tuple[str, str]]:
    """Get the tenant and request id(s) from a Slack url."""
    message_info = get_message_info(url)
    query = f"""
    SELECT tenant, shard_namespace, request_id
    FROM `{dataset}.slackbot_response_lookup`
    WHERE @message_timestamp IN UNNEST(slack_response_timestamps) AND channel = @channel
      AND time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY)
  """  # nosec
    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter(
                "message_timestamp", "STRING", message_info.timestamp
            ),
            bigquery.ScalarQueryParameter("channel", "STRING", message_info.channel),
        ]
    )
    rows = bigquery_client.query_and_wait(query, job_config=job_config)
    return [
        (
            f"{r.tenant}:{r.request_id}",
            f"https://support.{r.shard_namespace}.t.us-central1.prod.augmentcode.com/t/{r.tenant}/request/{r.request_id}",
        )
        for r in list(rows)
    ]


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--project", type=str, default="system-services-prod")
    parser.add_argument(
        "--dataset", type=str, default="us_staging_request_insight_analytics_dataset"
    )
    parser.add_argument("url", type=str)
    args = parser.parse_args()

    # Check the dataset name, since it's a user-provided value that we can't parameterize.
    if re.match(r"^[a-zA-Z0-9_\-]+$", args.dataset) is None:
        raise ValueError(f"Invalid dataset name: {args.dataset}")

    bigquery_client = bigquery.Client(project=args.project)
    rows = get_request_ids_from_url(bigquery_client, args.dataset, args.url)
    print("\n".join([f"{request_id}: {url}" for request_id, url in rows]))


if __name__ == "__main__":
    main()

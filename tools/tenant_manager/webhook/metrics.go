package main

import (
	"fmt"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
)

var requestCounter = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_tenant_manager_webhook_requests",
		Help: "Number of HTTP requests",
	},
	[]string{"status", "method", "path"},
)

var durationHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_tenant_manager_webhook_duration",
		Help:    "Duration of webhook HTTP requests",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"status", "method", "path"},
)

var readBodyHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_tenant_manager_webhook_read_body_duration",
		Help:    "Duration of reading request body",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"method", "path"},
)

var pubsubErrors = prometheus.NewCounter(
	prometheus.CounterOpts{
		Name: "au_tenant_manager_webhook_pubsub_errors",
		Help: "Number of errors publishing to pubsub",
	},
)

var postPubsubHistogram = prometheus.NewHistogram(
	prometheus.HistogramOpts{
		Name:    "au_tenant_manager_webhook_post_pubsub_duration",
		Help:    "Duration of publishing to pubsub",
		Buckets: prometheus.DefBuckets,
	},
)

type LoggingResponseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (w *LoggingResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

func withMetrics(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		loggingWriter := &LoggingResponseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
		}

		start := time.Now()

		next.ServeHTTP(loggingWriter, r)

		duration := time.Since(start)
		durationHistogram.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Observe(duration.Seconds())

		requestCounter.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Inc()

		event := log.Info()
		if loggingWriter.statusCode >= 500 {
			event = log.Error()
		} else if loggingWriter.statusCode >= 400 {
			event = log.Warn()
		}

		event.Str("method", r.Method).Str("path", r.URL.Path).Int("status", loggingWriter.statusCode).Dur("duration", duration).Msgf("Request processed")
	})
}

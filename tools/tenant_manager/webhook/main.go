package main

import (
	"bufio"
	"context"
	"crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
)

var configFile = flag.String("config", "", "Path to config file")

func loadServerTlsConfig(serverTlsConfig *TlsConfig) (*tls.Config, error) {
	cert, err := tls.LoadX509KeyPair(serverTlsConfig.CertPath, serverTlsConfig.KeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load server certificate and key: %v", err)
	}

	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12,
	}

	return tlsConfig, nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	openFile, err := os.Open(config.WebhookConfig.TokenFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening token file")
	}
	defer openFile.Close()

	scanner := bufio.NewScanner(openFile)
	tokens := make([]string, 0)

	for scanner.Scan() {
		token := scanner.Text()
		if len(token) == 0 {
			continue
		}
		if len(token) < 8 {
			log.Warn().Msg("Ignoring token because it is too short")
			continue
		}
		tokens = append(tokens, token)
	}

	// Setup metrics.
	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("GET /metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.WebhookConfig.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	prometheus.MustRegister(requestCounter)
	prometheus.MustRegister(durationHistogram)
	prometheus.MustRegister(readBodyHistogram)
	prometheus.MustRegister(pubsubErrors)
	prometheus.MustRegister(postPubsubHistogram)

	webhookServerTls, err := loadServerTlsConfig(config.WebhookConfig.ServerTlsConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config for webhook server")
	}

	webformServerTls, err := loadServerTlsConfig(config.WebformConfig.ServerTlsConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config for webform server")
	}

	ctx := context.Background()
	client, err := pubsub.NewClient(ctx, config.WebhookConfig.ProjectId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create pubsub client")
		return
	}
	defer client.Close()

	pubsubTopic := client.Topic(config.WebhookConfig.PubsubTopic)
	defer pubsubTopic.Stop()

	jsonlServer := &JSONLToPubsubServer{
		Tokens: tokens,
		Topic:  pubsubTopic,
	}

	// Set up HTTPS server for webhook
	webhookMux := http.NewServeMux()
	webhookMux.Handle("POST /append", withMetrics(http.HandlerFunc(jsonlServer.HandleJSONL)))
	webhookMux.Handle("GET /health", withMetrics(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})))

	// Set up HTTPS server for webform
	webformMux := http.NewServeMux()
	webformServer, err := NewServer(config)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create webform server")
	}
	webformMux.Handle("GET /", withMetrics(http.HandlerFunc(webformServer.HandleWebformGet)))
	webformMux.Handle("POST /", withMetrics(http.HandlerFunc(webformServer.HandleWebformPost)))
	webformMux.Handle("GET /health", withMetrics(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})))
	webformMux.Handle("GET /favicon.ico", withMetrics(http.HandlerFunc(webformServer.HandleFavicon)))

	webhookServer := &http.Server{
		Addr:      fmt.Sprintf(":%d", config.WebhookConfig.Port),
		Handler:   webhookMux,
		TLSConfig: webhookServerTls,
	}

	webformHttpServer := &http.Server{
		Addr:      fmt.Sprintf(":%d", config.WebformConfig.Port),
		Handler:   webformMux,
		TLSConfig: webformServerTls,
	}

	wg := sync.WaitGroup{}

	wg.Add(2) // Add 2 instead of 1 since we're running two servers

	// First goroutine for webhook server
	go func() {
		defer wg.Done()
		log.Info().Msgf("Starting HTTPS server on port %d", config.WebhookConfig.Port)
		if err := webhookServer.ListenAndServeTLS("", ""); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting webhook HTTPS server")
		}
	}()

	// Second goroutine for webform server
	go func() {
		defer wg.Done()
		log.Info().Msgf("Starting HTTPS server on port %d", config.WebformConfig.Port)
		if err := webformHttpServer.ListenAndServeTLS("", ""); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting webform HTTPS server")
		}
	}()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan

	log.Info().Msgf("Received signal %s, initiating graceful shutdown", sig)

	// Initiate graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := webhookServer.Shutdown(ctx); err != nil {
		log.Error().Err(err).Msg("Error during webhook server shutdown")
	}
	if err := webformHttpServer.Shutdown(ctx); err != nil {
		log.Error().Err(err).Msg("Error during webform server shutdown")
	}
	wg.Wait()
	log.Info().Msg("Server has shut down gracefully")
}

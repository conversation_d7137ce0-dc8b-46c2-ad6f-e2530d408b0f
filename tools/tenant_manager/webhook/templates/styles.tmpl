{{define "styles"}}
<style>
:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --background-color: #f8fafc;
    --card-background: #ffffff;
    --text-color: #1f2937;
    --border-color: #e5e7eb;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 2rem;
    background: var(--background-color);
    color: var(--text-color);
}

.container {
    max-width: 640px;
    margin: 0 auto;
    background: var(--card-background);
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

h1 {
    color: var(--text-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
    box-sizing: border-box;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

input[type="text"],
select,
textarea {
    box-sizing: border-box;
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    margin-top: 0.25rem;
}

textarea {
    min-height: 100px;
    resize: vertical;
}

button {
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    width: 100%;
    transition: background-color 0.2s;
    box-sizing: border-box;
}

button:hover {
    background-color: var(--primary-hover);
}

.required {
    color: #dc2626;
}

.debug-banner {
    background-color: #fef2f2;
    border: 1px solid #fee2e2;
    color: #991b1b;
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    text-align: center;
}

.debug-options {
    background-color: #f3f4f6;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
}

.debug-options h2 {
    font-size: 1.1rem;
    margin-top: 0;
    margin-bottom: 1rem;
}

.checkbox-group {
    margin-bottom: 0.5rem;
}

.checkbox-group label {
    display: inline;
    margin-left: 0.5rem;
}

/* Confirmation page specific styles */
.detail-row {
    display: flex;
    align-items: baseline;  /* Change to baseline to align with text */
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.detail-label {
    font-weight: 600;
    min-width: 120px;
}

.detail-value {
    flex: 1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Add these styles */
.success-banner {
    background-color: #dff0df;
    border: 1px solid #33cc33;
    color: #2d862d;
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    text-align: center;
    font-weight: bold;
}

.success-icon {
    color: #2d862d;
    font-size: 48px;
    text-align: center;
    margin: 1rem 0;
}

.debug-chip {
    background-color: #fef2f2;
    border: 1px solid #fee2e2;
    color: #991b1b;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-flex;
    align-items: baseline;  /* Change to baseline to align text properly */
    font-size: 0.875rem;
    line-height: 1;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.back-link {
    display: block;
    text-align: center;
    margin-top: 1.5rem;
    color: var(--primary-color);
    text-decoration: none;
}

.back-link:hover {
    text-decoration: underline;
}
</style>
{{end}}

package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"slices"
	"strings"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/rs/zerolog/log"
)

type IncomingTenantData map[string]interface{}

type JSONLToPubsubServer struct {
	Tokens []string
	Topic  *pubsub.Topic
}

func writeError(w http.ResponseWriter, error string, code int) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	fmt.Fprintln(w, error)
}

func (s *JSONLToPubsubServer) HandleJSONL(w http.ResponseWriter, r *http.Request) {
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		writeError(w, "{\"error\":\"Missing Authorization header\"}", http.StatusUnauthorized)
		return
	}

	authHeader = strings.TrimPrefix(authHeader, "Bearer ")

	if !slices.Contains(s.<PERSON>, authHeader) {
		writeError(w, `{"error":"Invalid Authorization header"}`, http.StatusUnauthorized)
		return
	}

	var incomingData IncomingTenantData
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&incomingData)
	if err != nil {
		writeError(w, `{"error":"Error decoding request body"}`, http.StatusBadRequest)
		return
	}

	messageJson, err := json.Marshal(incomingData)
	if err != nil {
		writeError(w, `{"error":"Error encoding data"}`, http.StatusInternalServerError)
		return
	}

	start := time.Now()
	readBodyHistogram.WithLabelValues(
		r.Method,
		r.URL.Path,
	).Observe(time.Since(start).Seconds())

	start = time.Now()
	_, err = s.Topic.Publish(r.Context(), &pubsub.Message{
		Data: messageJson,
	}).Get(r.Context())
	if err != nil {
		log.Warn().Err(err).Msg("Error publishing to pubsub")
		pubsubErrors.Inc()
		writeError(w, `{"error":"Error"}`, http.StatusInternalServerError)
		return
	}
	postPubsubHistogram.Observe(time.Since(start).Seconds())

	w.WriteHeader(http.StatusOK)
}

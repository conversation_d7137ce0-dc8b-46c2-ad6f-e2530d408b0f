load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

go_library(
    name = "server_lib",
    srcs = [
        "config.go",
        "main.go",
        "metrics.go",
        "webform_server.go",
        "webhook_server.go",
    ],
    embedsrcs = [
        "templates/styles.tmpl",
        "templates/webform.html",
        "templates/confirmation.html",
        "templates/favicon.ico",
    ],
    importpath = "github.com/augmentcode/augment/tools/tenant_manager/webhook",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_pubsub//:pubsub",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

# create a container image
go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    lint_allow_multiple_apps = True,
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    visibility = [
        "//services/auth/central:__subpackages__",
    ],
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

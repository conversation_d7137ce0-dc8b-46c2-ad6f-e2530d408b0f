load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")

sh_binary(
    name = "echo_token",
    srcs = ["echo_token.sh"],
)

py_library(
    name = "git_utils",
    srcs = [
        "git_utils.py",
    ],
    data = [
        ":echo_token",
    ],
    deps = [
        ":db",
        "//deploy/tenants:tenants_util",
        "//tools/bazel_runner/git:app_token",
        requirement("dataclasses_json"),
        requirement("pygithub"),
        requirement("GitPython"),
    ],
)

py_library(
    name = "db",
    srcs = [
        "db.py",
    ],
)

py_library(
    name = "tenant_spec",
    srcs = [
        "tenant_spec.py",
    ],
    deps = [
        ":git_utils",
        "//deploy/tenants:tenants_util",
    ],
)

py_binary(
    name = "server",
    srcs = [
        "processor.py",
    ],
    deps = [
        ":db",
        ":git_utils",
        ":tenant_spec",
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        requirement("dataclasses_json"),
        requirement("google-cloud-pubsub"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:ubuntu2004_ci_base_image",
    binary = ":server",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
    ],
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/lib/pubsub:pubsub-lib",
        "//tools/deploy:github_pr_token_lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    visibility = [
        "//services/auth/central:__subpackages__",
    ],
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

import logging
import sqlite3
import typing


class Database:
    def __init__(self, db_path: str) -> None:
        """
        Initialize the SQLite database.
        This function is idempotent and can be called multiple times safely.

        Args:
        db_path (str): Path to the SQLite database file
        """
        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        cursor = self.conn.cursor()

        # Drop existing tables if they exist
        cursor.execute("DROP TABLE IF EXISTS tenants")
        cursor.execute("DROP TABLE IF EXISTS domains")

        # Simplistic way to prevent tenant name collisions
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenants (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL UNIQUE
            )
        """)

        # Simplistic way to prevent domain name collisions
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS domains (
                id INTEGER PRIMARY KEY,
                domain TEXT NOT NULL UNIQUE
            )
        """)

        self.conn.commit()

    def check_tenant_or_domains_exist(
        self, tenant_name: str, domains: typing.Sequence[str] = ()
    ) -> bool:
        with self.conn:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM tenants WHERE name=?", (tenant_name,))
            result = cursor.fetchone()
            if result is not None:
                logging.info(f"Tenant {tenant_name} already exists")
                return True
            for domain in domains:
                cursor.execute("SELECT * FROM domains WHERE domain=?", (domain,))
                result = cursor.fetchone()
                if result is not None:
                    logging.info(f"Domain {domain} already exists")
                    return True
        return False

    def insert_domain(self, domain: str) -> None:
        cursor = self.conn.cursor()
        cursor.execute(
            """
            INSERT INTO domains (domain)
            VALUES (?)
        """,
            (domain,),
        )
        self.conn.commit()

    def insert_tenant(
        self, tenant_name: str, domains: typing.Sequence[str] = ()
    ) -> None:
        with self.conn:  # This automatically starts a transaction
            cursor = self.conn.cursor()

            # Attempt to insert the tenant
            cursor.execute(
                """
                INSERT INTO tenants (name)
                VALUES (?)
            """,
                (tenant_name,),
            )

            # Attempt to insert each domain
            for domain in domains:
                cursor.execute(
                    """
                    INSERT INTO domains (domain)
                    VALUES (?)
                """,
                    (domain,),
                )

            # If we reach this point without an exception, all insertions were successful
            self.conn.commit()

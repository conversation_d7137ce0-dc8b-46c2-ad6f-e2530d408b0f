local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local parseErrorsSpec = {
    displayName: 'Tenant Manager parse errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(au_tenmgr_processor_messages_parse_errors[60m])) > 3
      |||,
    },
  };
  local applyErrorsSpec = {
    displayName: 'Tenant Manager Apply errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(au_tenmgr_processor_apply_errors[60m])) > 2
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(cloud, parseErrorsSpec, 'tenmgr-processor-parse-errors', 'Parse errors'),
    monitoringLib.alertPolicy(cloud, applyErrorsSpec, 'tenmgr-processor-apply-errors', 'Apply errors'),
  ]

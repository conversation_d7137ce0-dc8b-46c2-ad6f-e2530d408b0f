package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"
	githubpb "github.com/augmentcode/augment/tools/bazel_runner/github_webhook/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

type Config struct {
	ProjectID        string `json:"project_id"`
	SubscriptionName string `json:"subscription_name"`
	DatasetName      string `json:"dataset_name"`
	TableName        string `json:"table_name"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`
}

var githubEvents = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "github_bigquery_events",
		Help: "Number of github webhook events exported to BigQuery",
	},
	[]string{"event_type", "status"},
)

func eventType(msg *githubpb.GithubEvent) string {
	switch msg.Event.(type) {
	case *githubpb.GithubEvent_PullRequest:
		return "pull_request"
	case *githubpb.GithubEvent_Push:
		return "push"
	case *githubpb.GithubEvent_PullRequestReview:
		return "pull_request_review"
	case *githubpb.GithubEvent_PullRequestReviewComment:
		return "pull_request_review_comment"
	default:
		return "none"
	}
}

type Row struct {
	EventType string            `bigquery:"event_type"`
	EventData bigquery.NullJSON `bigquery:"event_data"`
}

func main() {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	// Load config from file
	var config Config
	configFile := flag.String("config", "config.json", "Path to config file")
	healthFile := flag.String("health-file", "", "Path to a file to write a health check to")
	flag.Parse()
	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to open config file")
	}
	defer f.Close()
	err = json.NewDecoder(f).Decode(&config)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to parse config file")
	}

	// Create a Pub/Sub client
	ctx := context.Background()
	pubsubClient, err := pubsub.NewClient(ctx, config.ProjectID)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Pub/Sub client")
	}

	// Create a BigQuery client
	bqClient, err := bigquery.NewClient(ctx, config.ProjectID)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create BigQuery client")
	}

	// Create a subscription
	subscription := pubsubClient.Subscription(config.SubscriptionName)

	// Create a BigQuery table
	table := bqClient.Dataset(config.DatasetName).Table(config.TableName)

	inserter := table.Inserter()

	prometheus.MustRegister(
		githubEvents,
	)

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
			os.Exit(1)
		}
	}()

	log.Info().Msg("Starting to receive messages from subscription")
	if *healthFile != "" {
		f, err := os.Create(*healthFile)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create health file")
		}
		defer f.Close()
		f.WriteString("OK")
	}

	// Start receiving messages from the subscription
	err = subscription.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		// Unmarshal the protobuf message
		var event githubpb.GithubEvent
		err := proto.Unmarshal(msg.Data, &event)
		if err != nil {
			githubEvents.WithLabelValues(eventType(&event), "ERROR").Inc()
			log.Error().Err(err).Msg("Failed to unmarshal protobuf message")
			return
		}

		log.Info().Msgf("Received event: %s", eventType(&event))

		marshaler := protojson.MarshalOptions{
			Indent: "  ",
		}
		jsonData, err := marshaler.Marshal(&event)
		log.Info().Msgf("JSON: %s", jsonData)
		if err != nil {
			githubEvents.WithLabelValues(eventType(&event), "ERROR").Inc()
			log.Error().Err(err).Msg("Failed to convert to json")
			return
		}
		// Convert the protobuf message to a BigQuery row
		row := Row{
			EventType: eventType(&event),
			EventData: bigquery.NullJSON{
				Valid:   true,
				JSONVal: string(jsonData),
			},
		}

		// Insert the row into BigQuery
		err = inserter.Put(ctx, &row)
		if err != nil {
			githubEvents.WithLabelValues(row.EventType, "ERROR").Inc()
			log.Error().Err(err).Msg("Failed to insert row into BigQuery")
			return
		}
		githubEvents.WithLabelValues(row.EventType, "OK").Inc()
		msg.Ack()
	})
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to receive messages from subscription")
	}
}

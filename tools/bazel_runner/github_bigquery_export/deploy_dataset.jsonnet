local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local engineers = import 'deploy/common/eng.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local datasetLib = import 'tools/bazel_runner/github_bigquery_export/datasets.jsonnet';
function(env, namespace, cloud, namespace_config)
  local appName = 'ci-dataset';

  local dataset = datasetLib(env, namespace, cloud);

  local githubSchema = [
    {
      name: 'event_type',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The event type of the github webhook event',
    },
    {
      name: 'event_data',
      type: 'JSON',
      mode: 'REQUIRED',
      description: 'Event data',
    },
  ];
  local bazelRunnerSchema = [
    {
      name: 'run_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The run id of the bazel runner event',
    },
    {
      name: 'state',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The state of the bazel runner event',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
    {
      name: 'event_data',
      type: 'JSON',
      mode: 'REQUIRED',
      description: 'Event data',
    },
  ];
  local datasetEngAccess = [
    {
      role: if env == 'DEV' then 'OWNER' else 'READER',
      userByEmail: '%<EMAIL>' % eng.username,
    }
    for eng in engineers
  ];
  // For simplicity service accounts have write access to the dataset. Technically they only need
  // metadata read access on the dataset and write access on the tables, but that significantly
  // complicates configuration and we should catch things like table deletions in code review.

  local datasetServiceAccess = [
    {
      role: 'WRITER',
      groupByEmail: '%<EMAIL>' % dataset.cloudIdentityGroupName,
    },
  ];
  // BigQuery datasets require an explicit owner, and configconnector needs admin privileges to be
  // able to run updates.
  local datasetAdminAccess = [
    {
      role: 'OWNER',
      userByEmail: '<EMAIL>',
    },
  ];
  local access = datasetEngAccess + datasetServiceAccess + datasetAdminAccess;
  local bigqueryDataset = {
    apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
    kind: 'BigQueryDataset',
    metadata: {
      name: dataset.datasetName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Dataset for Bazel Runner CI data for %s' % env,
      resourceID: dataset.datasetResourceId,
      location: 'us',
      projectRef: {
        external: cloudInfo[cloud].projectId,
      },
      access: access,
    },
  };
  local githubTable =
    {
      apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
      kind: 'BigQueryTable',
      metadata: {
        name: '%s-github-events-table' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        description: 'Table with all github webhook events. the schema is specified in github.proto',
        resourceID: dataset.githubTableResourceId,
        datasetRef: {
          name: dataset.datasetName,
        },
        schema: std.toString(githubSchema),
      },
    };
  local bazelRunnerTable =
    {
      apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
      kind: 'BigQueryTable',
      metadata: {
        name: '%s-bazel-runner-events-table' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        description: 'Table with all bazel runner events. the schema is specified in bazel_runner.proto',
        resourceID: dataset.bazelRunnerTableResourceId,
        datasetRef: {
          name: dataset.datasetName,
        },
        schema: std.toString(bazelRunnerSchema),
      },
    };
  local cloudIdentityGroup = {
    apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
    kind: 'CloudIdentityGroup',
    metadata: {
      name: dataset.cloudIdentityGroupName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'CloudIdentityGroup for github-bigquery-exporter',
      parent: 'customers/C02kn0kha',
      groupKey: {
        id: '%<EMAIL>' % dataset.cloudIdentityGroupName,
      },
      initialGroupConfig: 'WITH_INITIAL_OWNER',
      labels: {
        'cloudidentity.googleapis.com/groups.discussion_forum': '',
      },
    },
  };
  lib.flatten(
    [
      cloudIdentityGroup,
      bigqueryDataset,
      githubTable,
      bazelRunnerTable,
    ]
  )

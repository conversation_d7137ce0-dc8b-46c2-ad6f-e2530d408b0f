local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local datasetLib = import 'tools/bazel_runner/github_bigquery_export/datasets.jsonnet';
function(env, namespace, cloud, namespace_config)
  local dataset = datasetLib(env, namespace, cloud);
  local appName = 'github-bigquery-exporter';

  local subscription = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubSubscription',
    metadata: {
      name: 'github-%s-bigquery-exporter-sub' % namespace,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      topicRef: {
        name: 'github-%s-topic' % namespace,
      },
      ackDeadlineSeconds: 10,
      retryPolicy: {
        minimumBackoff: '5s',
        maximumBackoff: '300s',
      },
    },
  };

  local serviceAccount = gcpLib.createServiceAccount(app=appName,
                                                     cloud=cloud,
                                                     env=env,
                                                     namespace=namespace,
                                                     iam=true,
                                                     overridePrefix='github-bq');
  local subscriptionPolicy = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: 'github-bigquery-exporter-sub-policy',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubSubscription',
        name: subscription.metadata.name,
      },
      bindings: [
        {
          role: 'roles/pubsub.subscriber',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    },
  };
  local membership = {
    apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
    kind: 'CloudIdentityMembership',
    metadata: {
      name: '%s-ci-dataset-gihub-export-membership' % namespace,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      groupRef: {
        name: dataset.cloudIdentityGroupName,
      },
      preferredMemberKey: {
        id: serviceAccount.serviceAccountGcpEmailAddress,
      },
      roles: [
        {
          name: 'MEMBER',
        },
      ],
    },
  };
  local config = {
    project_id: cloudInfo[cloud].projectId,
    subscription_name: subscription.metadata.name,
    dataset_name: dataset.datasetResourceId,
    table_name: dataset.githubTableResourceId,
    prom_port: 9090,
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container =
    {
      name: appName,
      target: {
        name: '//tools/bazel_runner/github_bigquery_export:image',
        dst: 'github-bigquery-exporter-image',
      },
      volumeMounts: [
        configMap.volumeMountDef,
      ],
      args: [
        '--config',
        configMap.filename,
        '--health-file',
        '/tmp/health',
      ],
      readinessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
      },
      livenessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
      },
      resources: {
        limits: {
          cpu: '0.1',
          memory: '1Gi',
        },
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local pod =
    {
      containers: [
        container,
      ],
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: serviceAccount.name,
      volumes: [
        configMap.podVolumeDef,
      ],
    };
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten(
    [
      configMap.objects,
      serviceAccount.objects,
      membership,
      subscriptionPolicy,
      deployment,
      subscription,
    ]
  )

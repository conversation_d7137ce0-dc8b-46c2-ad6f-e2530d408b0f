load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")

kubecfg_library(
    name = "datasets",
    srcs = ["datasets.jsonnet"],
    visibility = ["//tools/bazel_runner:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
    ],
    data = [
        ":image",
    ],
    deps = [
        ":datasets",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_dataset",
    src = "deploy_dataset.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
    ],
    deps = [
        ":datasets",
        "//deploy/common:cloud_info",
        "//deploy/common:eng-lib",
        "//deploy/common:lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_dataset",
    ],
)

go_library(
    name = "github_bigquery_export_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/bazel_runner/github_bigquery_export",
    visibility = ["//visibility:private"],
    deps = [
        "//tools/bazel_runner/github_webhook:github_go_proto",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "github_bigquery_export",
    embed = [":github_bigquery_export_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":github_bigquery_export",
)

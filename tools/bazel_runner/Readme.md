# <PERSON>zel Runner

The Bazel Runner is a remote execution tool to run Bazel build and test commands within Kubernetes jobs.

## Design

### Test Runner Server

The test runner service consists of two pods: rpc and processing.

The rpc pod handles all RPC requests and the processing pods interacts with the database and kubernetes.
The processing pod is responsible for the state management.

The test runner server pods usually run the test devtools namespace.
They start jobs in the test namespace to execute the bazel runner control (see below).

## Bazel Runner Control

A test execution specification defines which targets to build/test on which kind of hardware.

There are three or more jobs are scheduled to execute the test execution spec:
- Checkout Job: Prepares a checkout for the test execution. The checkout is done in a Kubernetes Persistent Volume that can be shared with later jobs.
- Run Job: Runs the Bazel command itself.
- Postprocessing Job: Copies the bazel outputs (bazel-testlogs) and additional information like the Event Protocol to a shared directory.

The command can be run based on a Python library or via a CLI tool.

The design allows:
- Executing multiple bazel commands in a single execution group. This is primarily used to run a subset of tests in a different run environment (e.g. with multi GPUs)
- Interleave checkout/run/postprocess calls of different runs/execution groups arbitrarily
- Re-run failed jobs if they are failed for infrastructure reasons.
- Report infrastructure failures and test failures differently ("A failed test is a successful from the point of the test infrastructure")


# Generating data on your local namespace

 1. bazel run //tools/bazel_runner/test:test_kubecfg
 2. go to the UI and run one time "main" with "//..." that should give you a big test.

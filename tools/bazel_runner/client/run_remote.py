"""Tooling to run an bazel command within Kubernetes."""

# pylint: disable=no-member,no-name-in-module
import argparse
import logging
import os
import pathlib
import sys
import uuid

import bazel_runner_client as bazel_runner_client
import google.protobuf.json_format as json_format
import google.protobuf.text_format as text_format
from checkout_spec_factory import create_checkout_from_args

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
import tools.bazel_runner.server.test_runner_pb2 as test_runner_pb2
from base.logging.console_logging import setup_console_logging
from tools.bazel_runner.control import bazel_runner_pb2


def _build_test_execution_config(
    args,
) -> bazel_runner_pb2.BazelRunnerExecutionGroupSpec:
    """Builds a test execution config from command line parameters."""
    if args.test_execution_spec_file:
        config_path = pathlib.Path(args.test_execution_spec_file)
        config = config_path.read_text(encoding="utf-8")
        if config_path.suffix == ".json":
            test_execution_config = json_format.Parse(
                config, bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
            )
        else:
            test_execution_config = text_format.Parse(
                config, bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
            )
    elif args.test_execution_spec:
        test_execution_config = text_format.Parse(
            args.test_execution_spec, bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
        )
    elif args.targets:
        test_execution_config = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
        run = test_execution_config.runs.add()
        run.command = args.command
        if args.run_env == "SINGLE_GPU":
            run.env = bazel_runner_pb2.SystemEnv.SINGLE_GPU
        elif args.run_env == "MULTI_GPU":
            run.env = bazel_runner_pb2.SystemEnv.MULTI_GPU
        elif args.run_env == "LARGE_GPU":
            run.env = bazel_runner_pb2.SystemEnv.LARGE_GPU
        elif args.run_env == "PREMIUM_CPU":
            run.env = bazel_runner_pb2.SystemEnv.PREMIUM_CPU
        elif args.run_env == "CPU":
            run.env = bazel_runner_pb2.SystemEnv.CPU
        else:
            raise ValueError(f"Invalid run env {args.run_env}")
        for target in args.targets:
            run.targets.append(target)
        if args.extra_args:
            for arg in args.extra_args:
                run.extra_args.append(arg)
        checkout_spec = create_checkout_from_args(
            owner=args.organization,
            repo_name=args.repo_name,
            pull_request=args.pull_request,
            branch=args.branch,
            ref=args.ref,
        )
        if not checkout_spec:
            sys.exit(1)
        test_execution_config.checkout.CopyFrom(checkout_spec)
    else:
        raise ValueError("--targets or --test-execution-spec needs to be set")
    return test_execution_config


def parse_args():
    parser = argparse.ArgumentParser()

    subparser = parser.add_subparsers()
    schedule_parser = subparser.add_parser("schedule")
    schedule_parser.set_defaults(action="schedule")

    schedule_parser.add_argument(
        "--ref", help="Commit SHA (or reference) used for testing"
    )
    schedule_parser.add_argument(
        "--organization",
        default="augmentcode",
        help="Github Organization or Username the repository is owned by. Default 'augmentcode'",
    )
    schedule_parser.add_argument(
        "--command",
        choices=["build", "test"],
        default="test",
        help="Bazel command to use (build|test). Default 'test'",
    )
    schedule_parser.add_argument(
        "--targets", nargs="+", help="Bazel targets to execute"
    )
    schedule_parser.add_argument(
        "--extra-args", nargs="*", help="Extra arguments to pass to bazel"
    )
    schedule_parser.add_argument(
        "--run-env",
        default="SINGLE_GPU",
        choices=["MULTI_GPU", "SINGLE_GPU", "LARGE_GPU", "PREMIUM_CPU", "CPU"],
        help="Environnement the targets should be executed in. Default is SINGLE_GPU",
    )
    schedule_parser.add_argument(
        "--repo-name",
        default="augment",
        help="Name of the Github repository. Default 'augment'",
    )
    schedule_parser.add_argument("--pull-request", type=int)
    schedule_parser.add_argument("--branch", help="Name of the Github branch")
    schedule_parser.add_argument(
        "--test-execution-spec",
        help="Text Execution Group Format in text format. If specified, the checkout and execution related commands are ignored.",
    )
    schedule_parser.add_argument(
        "--test-execution-spec-file",
        help="Path file a file containing Text Execution Group Format in text format. If specified, the checkout and execution related commands are ignored.",
    )
    schedule_parser.add_argument(
        "--test-selection",
        action="store_true",
        help="If set, the test selection is used instead of the test execution config",
    )

    wait_parser = subparser.add_parser("wait")
    wait_parser.set_defaults(action="wait")
    wait_parser.add_argument("--run-id", required=True)
    wait_parser.add_argument(
        "--testlogs-directory",
        default=str(
            pathlib.Path(os.environ["BUILD_WORKSPACE_DIRECTORY"], "bazel-testlogs")
        ),
    )

    status_parser = subparser.add_parser("status")
    status_parser.set_defaults(action="status")
    status_parser.add_argument("--run-id", required=True)

    cancel_parser = subparser.add_parser("cancel")
    cancel_parser.set_defaults(action="cancel")
    cancel_parser.add_argument("--run-id", required=True)

    events_parser = subparser.add_parser("events")
    events_parser.set_defaults(action="events")
    events_parser.add_argument("--run-id", required=True)
    events_parser.add_argument("--job-id", required=True)
    events_parser.add_argument("--min-sequence-number", default=0, type=int)

    test_cases_parser = subparser.add_parser("test-cases")
    test_cases_parser.set_defaults(action="test-cases")
    test_cases_parser.add_argument("--run-id", required=True)
    test_cases_parser.add_argument("--job-id", required=True)
    test_cases_parser.add_argument("--target-name", required=True)

    pending_parser = subparser.add_parser("pending-runs")
    pending_parser.set_defaults(action="pending-runs")

    search_parser = subparser.add_parser("search-runs")
    search_parser.set_defaults(action="search-runs")
    search_parser.add_argument("--tag", required=True)
    search_parser.add_argument("--max-results", type=int, default=10)

    parser.add_argument(
        "--endpoint", default="test-runner.vpc-internal.eng.augmentcode.com"
    )
    parser.add_argument("--insecure", action="store_true", default=False)
    return parser.parse_args()


def _build_test_selection(args):
    test_selection = test_runner_pb2.TestSelectionSpec()

    test_execution_config = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    checkout_spec = create_checkout_from_args(
        owner=args.organization,
        repo_name=args.repo_name,
        pull_request=args.pull_request,
        branch=args.branch,
        ref=args.ref,
    )
    if not checkout_spec:
        sys.exit(1)
    test_execution_config.checkout.CopyFrom(checkout_spec)

    return test_execution_config, test_selection


def do_schedule(args):
    """Execute the schedule subcommand."""
    if not args.test_selection:
        test_execution_config = _build_test_execution_config(args)
        test_selection = None
        logging.info("Test execution config: %s", test_execution_config)
    else:
        test_execution_config, test_selection = _build_test_selection(args)

    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    run_id = client.schedule_run(
        test_execution_config,
        requestor="",
        tags=["manual"],
        test_selection=test_selection,
    )
    logging.info("Run Id %s", run_id)


def do_wait(args):
    """Execute the wait subcommand."""
    run_id = args.run_id
    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    result = client.wait_for_run(run_id)
    logging.info("%s", result.spec)
    if result.state == test_runner_pb2.RUN_STATE_ABORT:
        logging.error("Execution aborted")
    elif result.state == test_runner_pb2.RUN_STATE_ERROR:
        logging.error("Execution failed")
    elif result.state == test_runner_pb2.RUN_STATE_CANCEL:
        logging.error("Execution cancelled")
    else:
        assert (
            result.state == test_runner_pb2.RUN_STATE_PASSED
            or result.state == test_runner_pb2.RUN_STATE_FAILURE
        )
        logging.info("Execution finished")
        for job_info in result.jobs:
            client.print_console_log(run_id, uuid.UUID(job_info.job_id))
        client.merge_run_results(
            run_id=run_id,
            execution_result=result,
            local_testlogs=pathlib.Path(args.testlogs_directory),
        )
    sys.exit(result.overall_return_code())


def _pretty_test_status(status: build_event_stream_pb2.TestStatus.ValueType):
    return build_event_stream_pb2.TestStatus.Name(status)[12:]


def do_status(args):
    """Execute the status subcommand."""
    run_id = args.run_id
    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    result = client.get_run_state(run_id)
    logging.info("Spec %s", result.spec)
    logging.info("Create time: %s", result.create_time)
    logging.info("State: %s", test_runner_pb2.RunState.Name(result.state))
    if result.is_done():
        for job_info, job_spec in zip(result.jobs, result.spec.runs):
            logging.info("Job Id: %s", job_info.job_id)
            logging.info("Run Env: %s", bazel_runner_pb2.SystemEnv.Name(job_spec.env))
            for test_info in job_info.tests:
                logging.info(
                    "> %s: %s",
                    test_info.target_name,
                    _pretty_test_status(test_info.status),
                )
    elif result.state == test_runner_pb2.RUN_STATE_ABORT:
        logging.info("Abort reason: %s", result.message)
    sys.exit(result.overall_return_code())


def do_cancel(args):
    """Execute the cancel subcommand."""
    run_id = args.run_id
    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    client.cancel_run(run_id, cancelled_by=None)


def do_events(args):
    """Execute the events subcommand."""
    run_id = uuid.UUID(args.run_id)
    job_id = uuid.UUID(args.job_id)
    min_sequence_number = args.min_sequence_number
    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    for event in client.get_builds_events(run_id, job_id, min_sequence_number):
        logging.info("%s %s", event.sequence_number, event.test_summary)


def do_test_cases(args):
    """Execute the test cases subcommand."""
    run_id = uuid.UUID(args.run_id)
    job_id = uuid.UUID(args.job_id)
    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    for test_case in client.get_builds_events(run_id, job_id, args.target_name):
        logging.info("%s", test_case)


def do_pending(args):
    """Execute the pending-runs subcommand."""
    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    for run_id in client.get_pending_runs():
        logging.info("Run Id: %s", run_id)


def do_search(args):
    """Execute the search-runs subcommand."""
    client = bazel_runner_client.BazelRunnerClient(args.endpoint, args.insecure)
    for run_id in client.search_runs_by_tag(args.tag, max_results=args.max_results):
        logging.info("Run Id: %s", run_id)


def main():
    """Entrypoint."""
    setup_console_logging()

    try:
        args = parse_args()
        logging.debug("Args %s", args)
        if args.action == "schedule":
            do_schedule(args)
        elif args.action == "wait":
            do_wait(args)
        elif args.action == "status":
            do_status(args)
        elif args.action == "cancel":
            do_cancel(args)
        elif args.action == "events":
            do_events(args)
        elif args.action == "test-cases":
            do_test_cases(args)
        elif args.action == "pending-runs":
            do_pending(args)
        elif args.action == "search-runs":
            do_search(args)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        sys.exit(1)


if __name__ == "__main__":
    main()

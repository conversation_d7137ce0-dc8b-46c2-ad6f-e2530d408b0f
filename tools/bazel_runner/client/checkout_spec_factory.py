"""Module to find a test execution checkout env from github env variables."""

import logging
import os
import re
from collections.abc import Mapping
from typing import Optional

from tools.bazel_runner.git import checkout_pb2


def _get_pr_from_ref(ref: str):
    m = re.match(r"refs/pull/(\d+)/merge", ref)
    assert m
    return int(m.groups()[0])


def create_checkout_from_github_env(
    environ: Optional[Mapping[str, str]] = None,
) -> checkout_pb2.CheckoutSpec:
    """Constructs a checkout spec based on the default environment variables in github actions."""
    if not environ:
        environ = os.environ
    spec = checkout_pb2.CheckoutSpec()
    spec.owner = environ["GITHUB_REPOSITORY"].partition("/")[0]
    spec.repo_name = environ["GITHUB_REPOSITORY"].partition("/")[2]
    event_name = environ["GITHUB_EVENT_NAME"]
    if event_name == "pull_request":
        spec.pull_request_checkout.pull_request_number = _get_pr_from_ref(
            environ["GITHUB_REF"]
        )
    else:
        spec.commit_checkout.branch = environ["GITHUB_REF_NAME"]
        spec.commit_checkout.ref = environ["GITHUB_SHA"]
    return spec


def create_checkout_from_args(
    owner: str,
    repo_name: str,
    pull_request: Optional[int],
    branch: Optional[str],
    ref: Optional[str],
) -> Optional[checkout_pb2.CheckoutSpec]:
    """Constructs a checkout spec from arguments."""
    spec = checkout_pb2.CheckoutSpec()
    spec.owner = owner
    spec.repo_name = repo_name
    if pull_request is None:
        if not ref:
            logging.error("ref is required if pull request is not set")
            return None
        if not branch:
            branch = "main"
        spec.commit_checkout.branch = branch
        spec.commit_checkout.ref = ref
    else:
        if branch:
            logging.error("branch and pull request settings are mutually exclusive")
            return None
        spec.pull_request_checkout.pull_request_number = pull_request
        if ref:
            spec.pull_request_checkout.ref = ref
    return spec

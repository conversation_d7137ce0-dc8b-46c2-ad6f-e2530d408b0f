"""Unit tests for the bazel_runner_client module."""

from datetime import datetime

import tools.bazel_runner.server.test_runner_pb2 as test_runner_pb2
from tools.bazel_runner.client import bazel_runner_client
from tools.bazel_runner.control import bazel_runner_pb2


def test_execution_result_tests_failed():
    spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run = spec.runs.add()
    run.command = "test"
    run.targets.append("//services/api_proxy/server:api_proxy_server-test")
    job_info1 = test_runner_pb2.JobInfo(
        job_id="0189946c-0cf5-8056-d318-b5e5df6f4f4a", return_code=3
    )
    result = bazel_runner_client.ExecutionResult(
        spec=spec,
        requestor="joe",
        state=8,
        jobs=[job_info1],
        create_time=datetime.now(),
        last_state_change_time=datetime.now(),
        message="",
    )
    assert result.is_done()
    assert result.overall_return_code() != 0


def test_execution_result_tests_build_failed():
    spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run = spec.runs.add()
    run.command = "test"
    run.targets.append("//services/api_proxy/server:api_proxy_server-test")
    job_info1 = test_runner_pb2.JobInfo(
        job_id="0189946c-0cf5-8056-d318-b5e5df6f4f4a", return_code=1
    )
    result = bazel_runner_client.ExecutionResult(
        spec=spec,
        requestor="joe",
        state=test_runner_pb2.RUN_STATE_FAILURE,
        jobs=[job_info1],
        create_time=datetime.now(),
        last_state_change_time=datetime.now(),
        message="",
    )
    assert result.is_done()
    assert result.overall_return_code() != 0


def test_execution_result_tests_passed():
    spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run = spec.runs.add()
    run.command = "test"
    run.targets.append("//services/api_proxy/server:api_proxy_server-test")
    job_info1 = test_runner_pb2.JobInfo(
        job_id="0189946c-0cf5-8056-d318-b5e5df6f4f4a", return_code=0
    )
    result = bazel_runner_client.ExecutionResult(
        spec=spec,
        requestor="joe",
        state=test_runner_pb2.RUN_STATE_PASSED,
        jobs=[job_info1],
        create_time=datetime.now(),
        last_state_change_time=datetime.now(),
        message="",
    )
    assert result.is_done()
    assert result.overall_return_code() == 0


def test_execution_result_tests_pending():
    spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run = spec.runs.add()
    run.command = "test"
    run.targets.append("//services/api_proxy/server:api_proxy_server-test")
    job_info1 = test_runner_pb2.JobInfo(
        job_id="0189946c-0cf5-8056-d318-b5e5df6f4f4a", return_code=0
    )
    result = bazel_runner_client.ExecutionResult(
        spec=spec,
        requestor="joe",
        state=test_runner_pb2.RUN_STATE_RUN,
        jobs=[job_info1],
        create_time=datetime.now(),
        last_state_change_time=datetime.now(),
        message="",
    )
    assert not result.is_done()
    assert result.overall_return_code() == 37

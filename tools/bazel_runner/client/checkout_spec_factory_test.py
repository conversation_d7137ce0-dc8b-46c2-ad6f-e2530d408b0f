"""Tests for the checkout_from_github_env module."""

from tools.bazel_runner.client.checkout_spec_factory import (
    create_checkout_from_github_env,
)


def test_pull_request():
    env = {
        "GITHUB_EVENT_NAME": "pull_request",
        "GITHUB_REPOSITORY_OWNER": "augmentcode",
        "GITHUB_REPOSITORY": "augmentcode/augment",
        "GITHUB_REF": "refs/pull/200/merge",
    }
    spec = create_checkout_from_github_env(environ=env)
    assert spec
    assert spec.owner == "augmentcode"
    assert spec.repo_name == "augment"
    assert spec.pull_request_checkout.pull_request_number == 200


def test_push():
    env = {
        "GITHUB_EVENT_NAME": "push",
        "GITHUB_REPOSITORY_OWNER": "augmentcode",
        "GITHUB_REPOSITORY": "augmentcode/augment",
        "GITHUB_REF_NAME": "main",
        "GITHUB_SHA": "ffac537e6cbbf934b08745a378932722df287a53",  # pragma: allowlist secret
    }
    spec = create_checkout_from_github_env(environ=env)
    assert spec
    assert spec.owner == "augmentcode"
    assert spec.repo_name == "augment"
    assert spec.commit_checkout.branch == "main"
    assert (
        spec.commit_checkout.ref
        == "ffac537e6cbbf934b08745a378932722df287a53"  # pragma: allowlist secret
    )

syntax = "proto3";

// an event from github was received
message GithubEvent {
  oneof event {
    // pull request
    PullRequest pull_request = 1;

    // push to a branch
    Push push = 2;

    PullRequestReview pull_request_review = 3;

    PullRequestReviewComment pull_request_review_comment = 4;
  }
}

// represents a reference in git
message FullRef {
  // reference (aka branch)
  string ref = 1;

  // commit (sha)
  string commit = 2;

  // owner of the repository, e.g. augmentcode for the organization
  string repo_owner = 3;

  // name of the repo, e.g. augment
  string repo_name = 4;
}

message User {
  // this is a github name, not an augmentcode username
  string login = 1;

  // name of the user, often not set
  string name = 2;

  // the email of the user, often not set
  string email = 3;
}

message Review {
  // The text of the comment.
  string body = 3;

  // The SHA of the commit to which the comment applies.
  string commit_id = 4;

  // The ID of the pull request review comment.
  int64 id = 5;

  // Can be one of: dismissed, approved, changes_requested
  string state = 6;

  // Time the review was submitted.
  // Format ISO 8601: YYYY-MM-DDTHH:MM:SSZ
  string submitted_at = 7;

  User user = 8;
}

message PullRequestReview {
  // Usually: [dismissed, submitted, edited].
  // However, subject to change by github without further notice
  string action = 1;

  // with action not set
  PullRequest pull_request = 2;

  Review review = 3;
}

message Comment {
  // The text of the comment.
  string body = 1;

  // The SHA of the commit to which the comment applies.
  string commit_id = 2;

  // Time the comment was created.
  // Format ISO 8601: YYYY-MM-DDTHH:MM:SSZ
  string created_at = 3;

  // The diff of the line that the comment refers to.
  string diff_hunk = 4;

  //  The ID of the pull request review comment.
  int64 id = 5;

  // The comment ID to reply to.
  int64 in_reply_to_id = 6;

  // The line of the blob to which the comment applies. The last line of the range for a
  // multi-line comment
  int32 line = 7;

  // The SHA of the original commit to which the comment applies.
  string original_commit_id = 8;

  // The line of the blob to which the comment applies. The last line of the range for a
  // multi-line comment
  int32 original_line = 9;

  // The index of the original line in the diff to which the comment applies.
  int32 original_position = 10;

  // The first line of the range for a multi-line comment.
  int32 original_start_line = 11;

  // The relative path of the file to which the comment applies.
  string path = 12;

  // The line index in the diff to which the comment applies.
  int32 position = 13;

  // The ID of the pull request review to which the comment belongs.
  int64 pull_request_review_id = 14;

  // The side of the first line of the range for a multi-line comment.
  //
  // Can be one of: LEFT, RIGHT
  string side = 15;

  // The first line of the range for a multi-line comment.
  int32 start_line = 16;

  // The side of the first line of the range for a multi-line comment.
  //
  // Default: RIGHT
  //
  // Can be one of: LEFT, RIGHT, null
  string start_side = 17;

  // The level at which the comment is targeted, can be a diff line or a file.
  //
  //  Can be one of: line, file
  string subject_type = 18;

  // Time the comment was last updated.
  // Format ISO 8601: YYYY-MM-DDTHH:MM:SSZ
  string updated_at = 19;

  User user = 20;
}

message PullRequestReviewComment {
  // [created, deleted, editor]
  // However, subject to change by github without further notice
  string action = 1;

  Comment comment = 2;

  PullRequest pull_request = 3;
}

// the event was a change of a pull request
message PullRequest {
  // name of the action
  //
  // see
  // https://docs.github.com/en/webhooks/webhook-events-and-payloads?actionType=assigned#pull_request
  // for details
  string action = 1;

  // pull request number
  uint32 number = 2;

  // head: what is reviewed, i.e. usually the user branch
  FullRef head = 3;

  // base: where to merge into, i.e. usually augment:main
  FullRef base = 4;

  reserved 5;

  User author = 6;
}

message Push {
  // reference pushed
  string ref = 1;

  // commit before
  string before = 2;

  // commit after
  string after = 3;

  // pusher (email prefix)
  string pusher = 4;

  // owner of the repository, e.g. augmentcode for the organization
  string repo_owner = 5;

  // name of the repo, e.g. augment
  string repo_name = 6;
}

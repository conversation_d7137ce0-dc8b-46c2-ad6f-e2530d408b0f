{"expected": {"pullRequestReview": {"action": "submitted", "pullRequest": {"number": 3468, "head": {"ref": "vzhao-01-16-_Retrieval_Add_embedding_output_type_to_fwd_starcoder.py_", "commit": "934cf4ee30cfcf348cb61a4b902c19df72b56722", "repoOwner": "augmentcode", "repoName": "augment"}, "base": {"ref": "vzhao-01-16-show_assertion-error", "commit": "f101eb938050fc5069e7dc9ad575644ac1702d25", "repoOwner": "augmentcode", "repoName": "augment"}, "author": {"login": "zyzzhaoyuzhe"}}, "review": {"commitId": "934cf4ee30cfcf348cb61a4b902c19df72b56722", "id": "1855126540", "state": "commented", "submittedAt": "2024-02-01T00:13:41Z", "user": {"login": "zyzzhaoyuzhe"}}}}, "data": {"action": "submitted", "review": {"id": 1855126540, "node_id": "PRR_kwDOHnlRLM5ukvwM", "user": {"login": "zyzzhaoyuzhe", "id": 5614195, "node_id": "MDQ6VXNlcjU2MTQxOTU=", "avatar_url": "https://avatars.githubusercontent.com/u/5614195?v=4", "gravatar_id": "", "url": "https://api.github.com/users/zyzzhaoyuzhe", "html_url": "https://github.com/zyzzhaoyuzhe", "followers_url": "https://api.github.com/users/zyzzhaoyuzhe/followers", "following_url": "https://api.github.com/users/zyzzhaoyuzhe/following{/other_user}", "gists_url": "https://api.github.com/users/zyzzhaoyuzhe/gists{/gist_id}", "starred_url": "https://api.github.com/users/zyzzhaoyuzhe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/zyzzhaoyuzhe/subscriptions", "organizations_url": "https://api.github.com/users/zyzzhaoyuzhe/orgs", "repos_url": "https://api.github.com/users/zyzzhaoyuzhe/repos", "events_url": "https://api.github.com/users/zyzzhaoyuzhe/events{/privacy}", "received_events_url": "https://api.github.com/users/zyzzhaoyuzhe/received_events", "type": "User", "site_admin": false}, "body": null, "commit_id": "934cf4ee30cfcf348cb61a4b902c19df72b56722", "submitted_at": "2024-02-01T00:13:41Z", "state": "commented", "html_url": "https://github.com/augmentcode/augment/pull/3468#pullrequestreview-1855126540", "pull_request_url": "https://api.github.com/repos/augmentcode/augment/pulls/3468", "author_association": "CONTRIBUTOR", "_links": {"html": {"href": "https://github.com/augmentcode/augment/pull/3468#pullrequestreview-1855126540"}, "pull_request": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3468"}}}, "pull_request": {"url": "https://api.github.com/repos/augmentcode/augment/pulls/3468", "id": 1683868425, "node_id": "PR_kwDOHnlRLM5kXcsJ", "html_url": "https://github.com/augmentcode/augment/pull/3468", "diff_url": "https://github.com/augmentcode/augment/pull/3468.diff", "patch_url": "https://github.com/augmentcode/augment/pull/3468.patch", "issue_url": "https://api.github.com/repos/augmentcode/augment/issues/3468", "number": 3468, "state": "open", "locked": false, "title": "[Retrieval] Add embedding output type to `fwd_starcoder.py`", "user": {"login": "zyzzhaoyuzhe", "id": 5614195, "node_id": "MDQ6VXNlcjU2MTQxOTU=", "avatar_url": "https://avatars.githubusercontent.com/u/5614195?v=4", "gravatar_id": "", "url": "https://api.github.com/users/zyzzhaoyuzhe", "html_url": "https://github.com/zyzzhaoyuzhe", "followers_url": "https://api.github.com/users/zyzzhaoyuzhe/followers", "following_url": "https://api.github.com/users/zyzzhaoyuzhe/following{/other_user}", "gists_url": "https://api.github.com/users/zyzzhaoyuzhe/gists{/gist_id}", "starred_url": "https://api.github.com/users/zyzzhaoyuzhe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/zyzzhaoyuzhe/subscriptions", "organizations_url": "https://api.github.com/users/zyzzhaoyuzhe/orgs", "repos_url": "https://api.github.com/users/zyzzhaoyuzhe/repos", "events_url": "https://api.github.com/users/zyzzhaoyuzhe/events{/privacy}", "received_events_url": "https://api.github.com/users/zyzzhaoyuzhe/received_events", "type": "User", "site_admin": false}, "body": "This PR is to make `fwd_starcoder.generate_step_fn` return step_fn for `OutputTensorType.EMBEDDING`, so that it can be used to load StarEthanol embedding models.\n\nTested:\nunittest", "created_at": "2024-01-17T23:36:33Z", "updated_at": "2024-02-01T00:13:41Z", "closed_at": null, "merged_at": null, "merge_commit_sha": "7e46abf260ac817bb6d4edb2e672ecd59b190eb8", "assignee": null, "assignees": [], "requested_reviewers": [{"login": "dmeister", "id": 26068, "node_id": "MDQ6VXNlcjI2MDY4", "avatar_url": "https://avatars.githubusercontent.com/u/26068?v=4", "gravatar_id": "", "url": "https://api.github.com/users/dmeister", "html_url": "https://github.com/dmeister", "followers_url": "https://api.github.com/users/dmeister/followers", "following_url": "https://api.github.com/users/dmeister/following{/other_user}", "gists_url": "https://api.github.com/users/dmeister/gists{/gist_id}", "starred_url": "https://api.github.com/users/dmeister/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/dmeister/subscriptions", "organizations_url": "https://api.github.com/users/dmeister/orgs", "repos_url": "https://api.github.com/users/dmeister/repos", "events_url": "https://api.github.com/users/dmeister/events{/privacy}", "received_events_url": "https://api.github.com/users/dmeister/received_events", "type": "User", "site_admin": false}, {"login": "msdejong", "id": 11789367, "node_id": "MDQ6VXNlcjExNzg5MzY3", "avatar_url": "https://avatars.githubusercontent.com/u/11789367?v=4", "gravatar_id": "", "url": "https://api.github.com/users/msdejong", "html_url": "https://github.com/msdejong", "followers_url": "https://api.github.com/users/msdejong/followers", "following_url": "https://api.github.com/users/msdejong/following{/other_user}", "gists_url": "https://api.github.com/users/msdejong/gists{/gist_id}", "starred_url": "https://api.github.com/users/msdejong/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/msdejong/subscriptions", "organizations_url": "https://api.github.com/users/msdejong/orgs", "repos_url": "https://api.github.com/users/msdejong/repos", "events_url": "https://api.github.com/users/msdejong/events{/privacy}", "received_events_url": "https://api.github.com/users/msdejong/received_events", "type": "User", "site_admin": false}], "requested_teams": [], "labels": [], "milestone": null, "draft": false, "commits_url": "https://api.github.com/repos/augmentcode/augment/pulls/3468/commits", "review_comments_url": "https://api.github.com/repos/augmentcode/augment/pulls/3468/comments", "review_comment_url": "https://api.github.com/repos/augmentcode/augment/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/augmentcode/augment/issues/3468/comments", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/934cf4ee30cfcf348cb61a4b902c19df72b56722", "head": {"label": "augmentcode:vzhao-01-16-_Retrieval_Add_embedding_output_type_to_fwd_starcoder.py_", "ref": "vzhao-01-16-_Retrieval_Add_embedding_output_type_to_fwd_starcoder.py_", "sha": "934cf4ee30cfcf348cb61a4b902c19df72b56722", "user": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:11:32Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 128, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 128, "watchers": 9, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "base": {"label": "augmentcode:vzhao-01-16-show_assertion-error", "ref": "vzhao-01-16-show_assertion-error", "sha": "f101eb938050fc5069e7dc9ad575644ac1702d25", "user": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:11:32Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 128, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 128, "watchers": 9, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "_links": {"self": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3468"}, "html": {"href": "https://github.com/augmentcode/augment/pull/3468"}, "issue": {"href": "https://api.github.com/repos/augmentcode/augment/issues/3468"}, "comments": {"href": "https://api.github.com/repos/augmentcode/augment/issues/3468/comments"}, "review_comments": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3468/comments"}, "review_comment": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3468/commits"}, "statuses": {"href": "https://api.github.com/repos/augmentcode/augment/statuses/934cf4ee30cfcf348cb61a4b902c19df72b56722"}}, "author_association": "CONTRIBUTOR", "auto_merge": null, "active_lock_reason": null}, "repository": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:11:32Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 128, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 128, "watchers": 9, "default_branch": "main", "custom_properties": {}}, "organization": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "url": "https://api.github.com/orgs/augmentcode", "repos_url": "https://api.github.com/orgs/augmentcode/repos", "events_url": "https://api.github.com/orgs/augmentcode/events", "hooks_url": "https://api.github.com/orgs/augmentcode/hooks", "issues_url": "https://api.github.com/orgs/augmentcode/issues", "members_url": "https://api.github.com/orgs/augmentcode/members{/member}", "public_members_url": "https://api.github.com/orgs/augmentcode/public_members{/member}", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "description": ""}, "sender": {"login": "zyzzhaoyuzhe", "id": 5614195, "node_id": "MDQ6VXNlcjU2MTQxOTU=", "avatar_url": "https://avatars.githubusercontent.com/u/5614195?v=4", "gravatar_id": "", "url": "https://api.github.com/users/zyzzhaoyuzhe", "html_url": "https://github.com/zyzzhaoyuzhe", "followers_url": "https://api.github.com/users/zyzzhaoyuzhe/followers", "following_url": "https://api.github.com/users/zyzzhaoyuzhe/following{/other_user}", "gists_url": "https://api.github.com/users/zyzzhaoyuzhe/gists{/gist_id}", "starred_url": "https://api.github.com/users/zyzzhaoyuzhe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/zyzzhaoyuzhe/subscriptions", "organizations_url": "https://api.github.com/users/zyzzhaoyuzhe/orgs", "repos_url": "https://api.github.com/users/zyzzhaoyuzhe/repos", "events_url": "https://api.github.com/users/zyzzhaoyuzhe/events{/privacy}", "received_events_url": "https://api.github.com/users/zyzzhaoyuzhe/received_events", "type": "User", "site_admin": false}}}
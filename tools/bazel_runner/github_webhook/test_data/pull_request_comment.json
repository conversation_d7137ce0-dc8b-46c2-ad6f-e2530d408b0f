{"expected": {"pullRequestReviewComment": {"action": "created", "comment": {"body": "0", "id": "1473641358", "commitId": "f57668a017a5705dc3e20abff343b5619632fdab", "createdAt": "2024-02-01T00:13:07Z", "diffHunk": "@@ -0,0 +1,37 @@\n+// `singleTenantModelDeployment` using `rogue-1B-fp8` and `starethanol6_16_1_proj1024_ffwd_config`.\n+local modelConfig = import 'models/inference/configs/rogue-1B-fp8.jsonnet';\n+local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n+local embedderConfig = import 'services/deploy/starethanol6_16_1_proj1024_ffwd_config.jsonnet';\n+function(env, namespace, cloud, namespace_config, filter=null)\n+  local name = 'rogue-1B-fp8-seth6-16-1-p1024';\n+  local inference() = local inferenceHostName = 'infer-%s' % name;\n+                      modelDeployment.centralInferenceHost(\n+    env=env,\n+    namespace=namespace,\n+    cloud=cloud,\n+    name=inferenceHostName,\n+    modelConfig=modelConfig(env=env),\n+    inferGpu='small',\n+    mtls=true\n+  );\n+  local completion() = modelDeployment.completionHost(\n+    env=env,\n+    namespace=namespace,\n+    cloud=cloud,\n+    name=name,\n+    modelPriority=1,  // it shouldn't be the default model", "inReplyToId": "-1", "line": 22, "originalCommitId": "f57668a017a5705dc3e20abff343b5619632fdab", "originalLine": 22, "originalPosition": 22, "path": "services/deploy/rogue_1B_fp8_seth6_16_1_p1024_deploy.jsonnet", "position": 22, "pullRequestReviewId": "1855125331", "startLine": -1, "originalStartLine": -1, "side": "RIGHT", "subjectType": "line", "updatedAt": "2024-02-01T00:13:55Z", "user": {"login": "dmeister"}}, "pullRequest": {"number": 3866, "head": {"ref": "vzhao-01-16-dev-deploy-proj1024", "commit": "f57668a017a5705dc3e20abff343b5619632fdab", "repoOwner": "augmentcode", "repoName": "augment"}, "base": {"ref": "vzhao-01-16-embedder-cudagraph", "commit": "04212a30fb9565ff65f8c72febb00ae789069006", "repoOwner": "augmentcode", "repoName": "augment"}, "author": {"login": "zyzzhaoyuzhe"}}}}, "data": {"action": "created", "comment": {"url": "https://api.github.com/repos/augmentcode/augment/pulls/comments/1473641358", "pull_request_review_id": 1855125331, "id": 1473641358, "node_id": "PRRC_kwDOHnlRLM5X1fuO", "diff_hunk": "@@ -0,0 +1,37 @@\n+// `singleTenantModelDeployment` using `rogue-1B-fp8` and `starethanol6_16_1_proj1024_ffwd_config`.\n+local modelConfig = import 'models/inference/configs/rogue-1B-fp8.jsonnet';\n+local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n+local embedderConfig = import 'services/deploy/starethanol6_16_1_proj1024_ffwd_config.jsonnet';\n+function(env, namespace, cloud, namespace_config, filter=null)\n+  local name = 'rogue-1B-fp8-seth6-16-1-p1024';\n+  local inference() = local inferenceHostName = 'infer-%s' % name;\n+                      modelDeployment.centralInferenceHost(\n+    env=env,\n+    namespace=namespace,\n+    cloud=cloud,\n+    name=inferenceHostName,\n+    modelConfig=modelConfig(env=env),\n+    inferGpu='small',\n+    mtls=true\n+  );\n+  local completion() = modelDeployment.completionHost(\n+    env=env,\n+    namespace=namespace,\n+    cloud=cloud,\n+    name=name,\n+    modelPriority=1,  // it shouldn't be the default model", "path": "services/deploy/rogue_1B_fp8_seth6_16_1_p1024_deploy.jsonnet", "commit_id": "f57668a017a5705dc3e20abff343b5619632fdab", "original_commit_id": "f57668a017a5705dc3e20abff343b5619632fdab", "user": {"login": "dmeister", "id": 26068, "node_id": "MDQ6VXNlcjI2MDY4", "avatar_url": "https://avatars.githubusercontent.com/u/26068?v=4", "gravatar_id": "", "url": "https://api.github.com/users/dmeister", "html_url": "https://github.com/dmeister", "followers_url": "https://api.github.com/users/dmeister/followers", "following_url": "https://api.github.com/users/dmeister/following{/other_user}", "gists_url": "https://api.github.com/users/dmeister/gists{/gist_id}", "starred_url": "https://api.github.com/users/dmeister/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/dmeister/subscriptions", "organizations_url": "https://api.github.com/users/dmeister/orgs", "repos_url": "https://api.github.com/users/dmeister/repos", "events_url": "https://api.github.com/users/dmeister/events{/privacy}", "received_events_url": "https://api.github.com/users/dmeister/received_events", "type": "User", "site_admin": false}, "body": "0", "created_at": "2024-02-01T00:13:07Z", "updated_at": "2024-02-01T00:13:55Z", "html_url": "https://github.com/augmentcode/augment/pull/3866#discussion_r1473641358", "pull_request_url": "https://api.github.com/repos/augmentcode/augment/pulls/3866", "author_association": "COLLABORATOR", "_links": {"self": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/comments/1473641358"}, "html": {"href": "https://github.com/augmentcode/augment/pull/3866#discussion_r1473641358"}, "pull_request": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3866"}}, "reactions": {"url": "https://api.github.com/repos/augmentcode/augment/pulls/comments/1473641358/reactions", "total_count": 0, "+1": 0, "-1": 0, "laugh": 0, "hooray": 0, "confused": 0, "heart": 0, "rocket": 0, "eyes": 0}, "start_line": null, "original_start_line": null, "start_side": null, "line": 22, "original_line": 22, "side": "RIGHT", "original_position": 22, "position": 22, "subject_type": "line"}, "pull_request": {"url": "https://api.github.com/repos/augmentcode/augment/pulls/3866", "id": 1704692182, "node_id": "PR_kwDOHnlRLM5lm4nW", "html_url": "https://github.com/augmentcode/augment/pull/3866", "diff_url": "https://github.com/augmentcode/augment/pull/3866.diff", "patch_url": "https://github.com/augmentcode/augment/pull/3866.patch", "issue_url": "https://api.github.com/repos/augmentcode/augment/issues/3866", "number": 3866, "state": "open", "locked": false, "title": "[Embedder] Creates configs for starethanol6_16_1_proj1024", "user": {"login": "zyzzhaoyuzhe", "id": 5614195, "node_id": "MDQ6VXNlcjU2MTQxOTU=", "avatar_url": "https://avatars.githubusercontent.com/u/5614195?v=4", "gravatar_id": "", "url": "https://api.github.com/users/zyzzhaoyuzhe", "html_url": "https://github.com/zyzzhaoyuzhe", "followers_url": "https://api.github.com/users/zyzzhaoyuzhe/followers", "following_url": "https://api.github.com/users/zyzzhaoyuzhe/following{/other_user}", "gists_url": "https://api.github.com/users/zyzzhaoyuzhe/gists{/gist_id}", "starred_url": "https://api.github.com/users/zyzzhaoyuzhe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/zyzzhaoyuzhe/subscriptions", "organizations_url": "https://api.github.com/users/zyzzhaoyuzhe/orgs", "repos_url": "https://api.github.com/users/zyzzhaoyuzhe/repos", "events_url": "https://api.github.com/users/zyzzhaoyuzhe/events{/privacy}", "received_events_url": "https://api.github.com/users/zyzzhaoyuzhe/received_events", "type": "User", "site_admin": false}, "body": null, "created_at": "2024-01-31T19:07:52Z", "updated_at": "2024-02-01T00:13:55Z", "closed_at": null, "merged_at": null, "merge_commit_sha": "dd991d285deb4dcc88ee751e3776c8bfb27e9af7", "assignee": null, "assignees": [], "requested_reviewers": [{"login": "Markus<PERSON><PERSON>", "id": 8495990, "node_id": "MDQ6VXNlcjg0OTU5OTA=", "avatar_url": "https://avatars.githubusercontent.com/u/8495990?v=4", "gravatar_id": "", "url": "https://api.github.com/users/MarkusRabe", "html_url": "https://github.com/MarkusRabe", "followers_url": "https://api.github.com/users/MarkusRabe/followers", "following_url": "https://api.github.com/users/MarkusRabe/following{/other_user}", "gists_url": "https://api.github.com/users/MarkusRabe/gists{/gist_id}", "starred_url": "https://api.github.com/users/MarkusRabe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/MarkusRabe/subscriptions", "organizations_url": "https://api.github.com/users/MarkusRabe/orgs", "repos_url": "https://api.github.com/users/MarkusRabe/repos", "events_url": "https://api.github.com/users/MarkusRabe/events{/privacy}", "received_events_url": "https://api.github.com/users/MarkusRabe/received_events", "type": "User", "site_admin": false}], "requested_teams": [], "labels": [], "milestone": null, "draft": false, "commits_url": "https://api.github.com/repos/augmentcode/augment/pulls/3866/commits", "review_comments_url": "https://api.github.com/repos/augmentcode/augment/pulls/3866/comments", "review_comment_url": "https://api.github.com/repos/augmentcode/augment/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/augmentcode/augment/issues/3866/comments", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/f57668a017a5705dc3e20abff343b5619632fdab", "head": {"label": "augmentcode:vzhao-01-16-dev-deploy-proj1024", "ref": "vzhao-01-16-dev-deploy-proj1024", "sha": "f57668a017a5705dc3e20abff343b5619632fdab", "user": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:11:32Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 128, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 128, "watchers": 9, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "base": {"label": "augmentcode:vzhao-01-16-embedder-cudagraph", "ref": "vzhao-01-16-embedder-cudagraph", "sha": "04212a30fb9565ff65f8c72febb00ae789069006", "user": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "repo": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:11:32Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 128, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 128, "watchers": 9, "default_branch": "main", "allow_squash_merge": true, "allow_merge_commit": false, "allow_rebase_merge": true, "allow_auto_merge": true, "delete_branch_on_merge": true, "allow_update_branch": true, "use_squash_pr_title_as_default": true, "squash_merge_commit_message": "PR_BODY", "squash_merge_commit_title": "PR_TITLE", "merge_commit_message": "PR_TITLE", "merge_commit_title": "MERGE_MESSAGE"}}, "_links": {"self": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3866"}, "html": {"href": "https://github.com/augmentcode/augment/pull/3866"}, "issue": {"href": "https://api.github.com/repos/augmentcode/augment/issues/3866"}, "comments": {"href": "https://api.github.com/repos/augmentcode/augment/issues/3866/comments"}, "review_comments": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3866/comments"}, "review_comment": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/augmentcode/augment/pulls/3866/commits"}, "statuses": {"href": "https://api.github.com/repos/augmentcode/augment/statuses/f57668a017a5705dc3e20abff343b5619632fdab"}}, "author_association": "CONTRIBUTOR", "auto_merge": null, "active_lock_reason": null}, "repository": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://api.github.com/repos/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": "2022-07-06T19:28:06Z", "updated_at": "2024-01-17T16:28:32Z", "pushed_at": "2024-02-01T00:11:32Z", "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 128, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 128, "watchers": 9, "default_branch": "main", "custom_properties": {}}, "organization": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "url": "https://api.github.com/orgs/augmentcode", "repos_url": "https://api.github.com/orgs/augmentcode/repos", "events_url": "https://api.github.com/orgs/augmentcode/events", "hooks_url": "https://api.github.com/orgs/augmentcode/hooks", "issues_url": "https://api.github.com/orgs/augmentcode/issues", "members_url": "https://api.github.com/orgs/augmentcode/members{/member}", "public_members_url": "https://api.github.com/orgs/augmentcode/public_members{/member}", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "description": ""}, "sender": {"login": "dmeister", "id": 26068, "node_id": "MDQ6VXNlcjI2MDY4", "avatar_url": "https://avatars.githubusercontent.com/u/26068?v=4", "gravatar_id": "", "url": "https://api.github.com/users/dmeister", "html_url": "https://github.com/dmeister", "followers_url": "https://api.github.com/users/dmeister/followers", "following_url": "https://api.github.com/users/dmeister/following{/other_user}", "gists_url": "https://api.github.com/users/dmeister/gists{/gist_id}", "starred_url": "https://api.github.com/users/dmeister/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/dmeister/subscriptions", "organizations_url": "https://api.github.com/users/dmeister/orgs", "repos_url": "https://api.github.com/users/dmeister/repos", "events_url": "https://api.github.com/users/dmeister/events{/privacy}", "received_events_url": "https://api.github.com/users/dmeister/received_events", "type": "User", "site_admin": false}}}
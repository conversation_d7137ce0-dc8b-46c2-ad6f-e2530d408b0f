{"expected": {"push": {"ref": "refs/heads/main", "before": "f101eb938050fc5069e7dc9ad575644ac1702d25", "after": "df1f1d378acb91d76cc82ef9bf3f31f43b8afb06", "pusher": "zyzzhaoyuzhe", "repoOwner": "augmentcode", "repoName": "augment"}}, "data": {"ref": "refs/heads/main", "before": "f101eb938050fc5069e7dc9ad575644ac1702d25", "after": "df1f1d378acb91d76cc82ef9bf3f31f43b8afb06", "repository": {"id": 511267116, "node_id": "R_kgDOHnlRLA", "name": "augment", "full_name": "augmentcode/augment", "private": true, "owner": {"name": "augmentcode", "email": null, "login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "gravatar_id": "", "url": "https://api.github.com/users/augmentcode", "html_url": "https://github.com/augmentcode", "followers_url": "https://api.github.com/users/augmentcode/followers", "following_url": "https://api.github.com/users/augmentcode/following{/other_user}", "gists_url": "https://api.github.com/users/augmentcode/gists{/gist_id}", "starred_url": "https://api.github.com/users/augmentcode/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/augmentcode/subscriptions", "organizations_url": "https://api.github.com/users/augmentcode/orgs", "repos_url": "https://api.github.com/users/augmentcode/repos", "events_url": "https://api.github.com/users/augmentcode/events{/privacy}", "received_events_url": "https://api.github.com/users/augmentcode/received_events", "type": "Organization", "site_admin": false}, "html_url": "https://github.com/augmentcode/augment", "description": null, "fork": false, "url": "https://github.com/augmentcode/augment", "forks_url": "https://api.github.com/repos/augmentcode/augment/forks", "keys_url": "https://api.github.com/repos/augmentcode/augment/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/augmentcode/augment/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/augmentcode/augment/teams", "hooks_url": "https://api.github.com/repos/augmentcode/augment/hooks", "issue_events_url": "https://api.github.com/repos/augmentcode/augment/issues/events{/number}", "events_url": "https://api.github.com/repos/augmentcode/augment/events", "assignees_url": "https://api.github.com/repos/augmentcode/augment/assignees{/user}", "branches_url": "https://api.github.com/repos/augmentcode/augment/branches{/branch}", "tags_url": "https://api.github.com/repos/augmentcode/augment/tags", "blobs_url": "https://api.github.com/repos/augmentcode/augment/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/augmentcode/augment/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/augmentcode/augment/git/refs{/sha}", "trees_url": "https://api.github.com/repos/augmentcode/augment/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/augmentcode/augment/statuses/{sha}", "languages_url": "https://api.github.com/repos/augmentcode/augment/languages", "stargazers_url": "https://api.github.com/repos/augmentcode/augment/stargazers", "contributors_url": "https://api.github.com/repos/augmentcode/augment/contributors", "subscribers_url": "https://api.github.com/repos/augmentcode/augment/subscribers", "subscription_url": "https://api.github.com/repos/augmentcode/augment/subscription", "commits_url": "https://api.github.com/repos/augmentcode/augment/commits{/sha}", "git_commits_url": "https://api.github.com/repos/augmentcode/augment/git/commits{/sha}", "comments_url": "https://api.github.com/repos/augmentcode/augment/comments{/number}", "issue_comment_url": "https://api.github.com/repos/augmentcode/augment/issues/comments{/number}", "contents_url": "https://api.github.com/repos/augmentcode/augment/contents/{+path}", "compare_url": "https://api.github.com/repos/augmentcode/augment/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/augmentcode/augment/merges", "archive_url": "https://api.github.com/repos/augmentcode/augment/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/augmentcode/augment/downloads", "issues_url": "https://api.github.com/repos/augmentcode/augment/issues{/number}", "pulls_url": "https://api.github.com/repos/augmentcode/augment/pulls{/number}", "milestones_url": "https://api.github.com/repos/augmentcode/augment/milestones{/number}", "notifications_url": "https://api.github.com/repos/augmentcode/augment/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/augmentcode/augment/labels{/name}", "releases_url": "https://api.github.com/repos/augmentcode/augment/releases{/id}", "deployments_url": "https://api.github.com/repos/augmentcode/augment/deployments", "created_at": 1657135686, "updated_at": "2024-01-17T16:28:32Z", "pushed_at": 1706747199, "git_url": "git://github.com/augmentcode/augment.git", "ssh_url": "**************:augmentcode/augment.git", "clone_url": "https://github.com/augmentcode/augment.git", "svn_url": "https://github.com/augmentcode/augment", "homepage": "augment-frontend.vercel.app", "size": 152736, "stargazers_count": 9, "watchers_count": 9, "language": "Jupyter Notebook", "has_issues": false, "has_projects": false, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 31, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 128, "license": {"key": "other", "name": "Other", "spdx_id": "NOASSERTION", "url": null, "node_id": "MDc6TGljZW5zZTA="}, "allow_forking": true, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 31, "open_issues": 128, "watchers": 9, "default_branch": "main", "stargazers": 9, "master_branch": "main", "organization": "augmentcode", "custom_properties": {}}, "pusher": {"name": "zyzzhaoyuzhe", "email": "<EMAIL>"}, "organization": {"login": "augmentcode", "id": 108155640, "node_id": "O_kgDOBnJS-A", "url": "https://api.github.com/orgs/augmentcode", "repos_url": "https://api.github.com/orgs/augmentcode/repos", "events_url": "https://api.github.com/orgs/augmentcode/events", "hooks_url": "https://api.github.com/orgs/augmentcode/hooks", "issues_url": "https://api.github.com/orgs/augmentcode/issues", "members_url": "https://api.github.com/orgs/augmentcode/members{/member}", "public_members_url": "https://api.github.com/orgs/augmentcode/public_members{/member}", "avatar_url": "https://avatars.githubusercontent.com/u/108155640?v=4", "description": ""}, "sender": {"login": "zyzzhaoyuzhe", "id": 5614195, "node_id": "MDQ6VXNlcjU2MTQxOTU=", "avatar_url": "https://avatars.githubusercontent.com/u/5614195?v=4", "gravatar_id": "", "url": "https://api.github.com/users/zyzzhaoyuzhe", "html_url": "https://github.com/zyzzhaoyuzhe", "followers_url": "https://api.github.com/users/zyzzhaoyuzhe/followers", "following_url": "https://api.github.com/users/zyzzhaoyuzhe/following{/other_user}", "gists_url": "https://api.github.com/users/zyzzhaoyuzhe/gists{/gist_id}", "starred_url": "https://api.github.com/users/zyzzhaoyuzhe/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/zyzzhaoyuzhe/subscriptions", "organizations_url": "https://api.github.com/users/zyzzhaoyuzhe/orgs", "repos_url": "https://api.github.com/users/zyzzhaoyuzhe/repos", "events_url": "https://api.github.com/users/zyzzhaoyuzhe/events{/privacy}", "received_events_url": "https://api.github.com/users/zyzzhaoyuzhe/received_events", "type": "User", "site_admin": false}, "created": false, "deleted": false, "forced": false, "base_ref": null, "compare": "https://github.com/augmentcode/augment/compare/f101eb938050...df1f1d378acb", "commits": [{"id": "df1f1d378acb91d76cc82ef9bf3f31f43b8afb06", "tree_id": "d9f6d16334a155ca661801a61dc0abcb8dc08507", "distinct": true, "message": "show CheckDNSName assertion error. (#3864)\n\nThis PR helps users to see invalid DNS name.", "timestamp": "2024-01-31T16:26:39-08:00", "url": "https://github.com/augmentcode/augment/commit/df1f1d378acb91d76cc82ef9bf3f31f43b8afb06", "author": {"name": "<PERSON>", "email": "<EMAIL>", "username": "zyzzhaoyuzhe"}, "committer": {"name": "GitHub", "email": "<EMAIL>", "username": "web-flow"}, "added": [], "removed": [], "modified": ["deploy/common/cert-lib.jsonnet"]}], "head_commit": {"id": "df1f1d378acb91d76cc82ef9bf3f31f43b8afb06", "tree_id": "d9f6d16334a155ca661801a61dc0abcb8dc08507", "distinct": true, "message": "show CheckDNSName assertion error. (#3864)\n\nThis PR helps users to see invalid DNS name.", "timestamp": "2024-01-31T16:26:39-08:00", "url": "https://github.com/augmentcode/augment/commit/df1f1d378acb91d76cc82ef9bf3f31f43b8afb06", "author": {"name": "<PERSON>", "email": "<EMAIL>", "username": "zyzzhaoyuzhe"}, "committer": {"name": "GitHub", "email": "<EMAIL>", "username": "web-flow"}, "added": [], "removed": [], "modified": ["deploy/common/cert-lib.jsonnet"]}}}
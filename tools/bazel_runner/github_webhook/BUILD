load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image", "py_proto_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:go.bzl", "go_proto_library")

proto_library(
    name = "github_proto",
    srcs = ["github.proto"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "github_py_proto",
    protos = [":github_proto"],
    visibility = ["//tools:__subpackages__"],
)

go_proto_library(
    name = "github_go_proto",
    importpath = "github.com/augmentcode/augment/tools/bazel_runner/github_webhook/proto",
    proto = ":github_proto",
    visibility = ["//tools:__subpackages__"],
)

py_library(
    name = "parser",
    srcs = ["parser.py"],
    deps = [
        ":github_py_proto",
    ],
)

pytest_test(
    name = "parser_test",
    srcs = ["parser_test.py"],
    data = glob(["test_data/*.json"]),
    deps = [
        ":parser",
    ],
)

py_binary(
    name = "github_webhook",
    srcs = ["app.py"],
    main = "app.py",
    deps = [
        ":github_py_proto",
        ":parser",
        requirement("gunicorn"),
        requirement("prometheus_flask_exporter"),
        requirement("prometheus_client"),
        requirement("google-cloud-pubsub"),
        requirement("flask"),
        requirement("structlog"),
        requirement("protobuf"),
        "//base/logging:struct_logging",
        requirement("github-webhook"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = "github_webhook",
    visibility = ["//tools/bazel_runner:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
        "GCP_US_CENTRAL1_PROD",
    ],
    data = [
        ":image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

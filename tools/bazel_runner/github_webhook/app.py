"""Flask application to provide a github webhook receiver."""

import json
import logging
import os
import typing

import structlog
from flask import Flask
from github_webhook import Webhook
from google.cloud import pubsub_v1  # type: ignore
from prometheus_client import Counter, start_http_server
from gunicorn.app.base import BaseApplication
from prometheus_flask_exporter.multiprocess import GunicornPrometheusMetrics

from base.logging.struct_logging import setup_struct_logging
from tools.bazel_runner.github_webhook import parser

_event_counter = Counter(
    "github_webhook_events",
    "Number of github webhook events",
    labelnames=["event"],
)

_failure_counter = Counter(
    "github_webhook_failures",
    "Number of github webhook events",
    labelnames=["event"],
)

log = structlog.get_logger()


class Publisher(typing.Protocol):
    """Publisher interface."""

    def publish(self, event_data):
        raise NotImplementedError()


class NullPublisher(Publisher):
    """Publisher that does nothing."""

    def publish(self, event_data):
        pass


class PubSubPublisher(Publisher):
    """Publisher that publishes to pubsub."""

    def __init__(self, topic_name: str):
        self.publisher = pubsub_v1.PublisherClient()
        self.topic_name = topic_name

    def publish(self, event_data):
        future = self.publisher.publish(self.topic_name, event_data)
        response = future.result()
        logging.info("publisher mesage id %s", response)


def create_app():
    app = Flask(__name__)

    if "CONFIG_FILE" in os.environ:
        app.config.from_envvar("CONFIG_FILE")
    else:
        app.config.from_object(
            "augment.tools.bazel_runner.github_webhook.config.TestConfig"
        )
    log.info("Config %s", app.config)
    webhook = Webhook(app, secret=os.environ["GITHUB_WEBHOOK_SECRET"])

    publisher = NullPublisher()
    if app.config["PUBSUB_TOPIC"]:
        log.info("Pubsub topic %s", app.config["PUBSUB_TOPIC"])
        publisher = PubSubPublisher(app.config["PUBSUB_TOPIC"])
    else:
        log.info("No pubsub topic configured")
        publisher = NullPublisher()

    @app.route("/health")
    def health():
        """Health check endpoint.

        Called by github to check if the webhook is alive.
        """
        return "Ok"

    @webhook.hook()
    def on_push(data):
        """Called when a push event is received."""
        with _failure_counter.labels("push").count_exceptions():
            try:
                _event_counter.labels("push").inc()
                log.debug("Push %s", json.dumps(data))
                event = parser.parse_push(data)
                log.info("Event %s", event)
                publisher.publish(event.SerializeToString())
            except Exception as ex:  # pylint: disable=broad-except
                log.warning("Failed on Push %s", json.dumps(data))
                log.exception(ex)
                raise

    @webhook.hook(event_type="pull_request")
    def on_pull_request(data):
        """Called when a pull request event is received."""
        with _failure_counter.labels("pull_request").count_exceptions():
            try:
                log.debug("Pull Request %s", json.dumps(data))
                _event_counter.labels([f"pull_request_{data['action']}"]).inc()
                event = parser.parse_pull_request(data)
                log.info("Event %s", event)
                publisher.publish(event.SerializeToString())
            except Exception as ex:  # pylint: disable=broad-except
                log.warning("Failed on Pull Request %s", json.dumps(data))
                log.exception(ex)
                raise

    @webhook.hook(event_type="pull_request_review")
    def on_pull_request_review(data):
        """Called when a pull request review event is received."""
        with _failure_counter.labels("on_pull_request_review").count_exceptions():
            try:
                log.debug("Pull Request Review %s", json.dumps(data))
                _event_counter.labels(["pull_request_review"]).inc()
                event = parser.parse_pull_request_review(data)
                log.info("Event %s", event)
                publisher.publish(event.SerializeToString())
            except Exception as ex:  # pylint: disable=broad-except
                log.warning("Failed on Pull Request Review %s", json=json.dumps(data))
                log.exception(ex)
                raise

    @webhook.hook(event_type="pull_request_review_comment")
    def on_pull_request_review_comment(data):
        """Called when a pull request review comment event is received."""
        with _failure_counter.labels("pull_request_review_comment").count_exceptions():
            try:
                log.info("Pull Request Review Comment %s", json.dumps(data))
                _event_counter.labels(["pull_request_review_comment"]).inc()

                event = parser.parse_pull_request_review_comment(data)
                log.info("Event %s", event)
                publisher.publish(event.SerializeToString())
            except Exception as ex:  # pylint: disable=broad-except
                log.warning(
                    "Failed on Pull Request Review Comment %s",
                    json.dumps(data),
                )
                log.exception(ex)
                raise

    return app


class _App(BaseApplication):
    """Gunicorn wrapper for service Flask App."""

    # pylint: disable=abstract-method

    def __init__(self, app, options):
        self.application = app
        self.options = options
        super().__init__()

    def load_config(self):
        assert (
            self.cfg is not None
        )  # cfg is never None here, as it is initialized by Gunicorn. adding check for pylint
        for key, value in self.options.items():
            self.cfg.set(key, value)

    def load(self):
        return self.application


def when_ready(server):
    del server
    GunicornPrometheusMetrics.start_http_server_when_ready(9090)


def child_exit(server, worker):
    del server
    GunicornPrometheusMetrics.mark_process_dead_on_child_exit(worker.pid)


def main():
    setup_struct_logging()

    app = create_app()
    _ = GunicornPrometheusMetrics(app, group_by="endpoint")

    port = app.config.get("PORT")
    logging.info("Serve port %s", port)

    if app.config.get("DEBUG_MODE"):
        app.run(use_reloader=True, port=port, threaded=True)
    else:
        https_service_key = app.config.get("HTTPS_SERVER_KEY")
        https_service_cert = app.config.get("HTTPS_SERVER_CERT")
        options = {
            "bind": f"0.0.0.0:{port}",
            "threads": 4,
            "keyfile": https_service_key,
            "certfile": https_service_cert,
            "when_ready": when_ready,
            "child_exit": child_exit,
        }
        _App(app, options).run()


if __name__ == "__main__":
    main()

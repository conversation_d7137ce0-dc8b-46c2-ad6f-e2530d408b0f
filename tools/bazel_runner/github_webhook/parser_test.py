"""Tests for the github webhook parser."""

import json
import pathlib

from google.protobuf import json_format

from tools.bazel_runner.github_webhook import parser


def test_pull_request_comment():
    json_data = json.loads(
        pathlib.Path(
            "tools/bazel_runner/github_webhook/test_data/pull_request_comment.json"
        ).read_text("utf-8")
    )
    event = parser.parse_pull_request_review_comment(json_data["data"])
    actual = json_format.MessageToJson(event)
    assert json.loads(actual) == json_data["expected"]


def test_pull_request():
    json_data = json.loads(
        pathlib.Path(
            "tools/bazel_runner/github_webhook/test_data/pull_request.json"
        ).read_text("utf-8")
    )
    event = parser.parse_pull_request(json_data["data"])
    actual = json_format.MessageToJson(event)
    assert json.loads(actual) == json_data["expected"]


def test_pull_request_review():
    json_data = json.loads(
        pathlib.Path(
            "tools/bazel_runner/github_webhook/test_data/pull_request_review.json"
        ).read_text("utf-8")
    )
    event = parser.parse_pull_request_review(json_data["data"])
    actual = json_format.MessageToJson(event)
    assert json.loads(actual) == json_data["expected"]


def test_push():
    json_data = json.loads(
        pathlib.Path("tools/bazel_runner/github_webhook/test_data/push.json").read_text(
            "utf-8"
        )
    )
    event = parser.parse_push(json_data["data"])
    actual = json_format.MessageToJson(event)
    assert json.loads(actual) == json_data["expected"]

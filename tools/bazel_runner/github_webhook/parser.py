"""Parser for github webhook events."""

from tools.bazel_runner.github_webhook import github_pb2


def _data_to_pull_request(data, include_action: bool) -> github_pb2.PullRequest:
    return github_pb2.PullRequest(
        action=data.get("action", "") if include_action else "",
        number=data.get("number", data["pull_request"]["number"]),
        head=github_pb2.FullRef(
            ref=data["pull_request"]["head"]["ref"],
            commit=data["pull_request"]["head"]["sha"],
            repo_name=data["pull_request"]["head"]["repo"]["name"],
            repo_owner=data["pull_request"]["head"]["repo"]["owner"]["login"],
        ),
        base=github_pb2.FullRef(
            ref=data["pull_request"]["base"]["ref"],
            commit=data["pull_request"]["base"]["sha"],
            repo_name=data["pull_request"]["base"]["repo"]["name"],
            repo_owner=data["pull_request"]["base"]["repo"]["owner"]["login"],
        ),
        author=_data_to_user(data["pull_request"]["user"]),
    )


def _data_to_user(data) -> github_pb2.User:
    return github_pb2.User(
        login=data["login"],
        name=data.get("name", ""),
        email=data.get("email", ""),
    )


def _get_or_default(data, key):
    v = data.get(key)
    if v is None:
        return -1
    return v


def _data_to_comment(data) -> github_pb2.Comment:
    return github_pb2.Comment(
        body=data["body"],
        id=data["id"],
        commit_id=data["commit_id"],
        created_at=data["created_at"],
        diff_hunk=data["diff_hunk"],
        in_reply_to_id=_get_or_default(data, "in_reply_to_id"),
        line=_get_or_default(data, "line"),
        original_commit_id=data["original_commit_id"],
        original_line=_get_or_default(data, "original_line"),
        original_position=data["original_position"],
        original_start_line=_get_or_default(data, "original_start_line"),
        path=data["path"],
        position=_get_or_default(data, "position"),
        pull_request_review_id=data["pull_request_review_id"],
        side=data["side"],
        start_line=_get_or_default(data, "start_line"),
        start_side=data.get("start_side", ""),
        subject_type=data["subject_type"],
        updated_at=data["updated_at"],
        user=_data_to_user(data["user"]),
    )


def parse_push(data) -> github_pb2.GithubEvent:
    """Parse a push event.

    Args:
        data: The data from the webhook.

    Returns:
        A GithubEvent proto.
    """
    return github_pb2.GithubEvent(
        push=github_pb2.Push(
            ref=data["ref"],
            before=data["before"],
            after=data["after"],
            pusher=data["pusher"]["name"],
            repo_name=data["repository"]["name"],
            repo_owner=data["repository"]["owner"]["login"],
        )
    )


def parse_pull_request(data) -> github_pb2.GithubEvent:
    """Parse a pull_request event.

    Args:
        data: The data from the webhook.

    Returns:
        A GithubEvent proto.
    """
    return github_pb2.GithubEvent(pull_request=_data_to_pull_request(data, True))


def parse_pull_request_review(data) -> github_pb2.GithubEvent:
    """Parse a pull_request_review event.

    Args:
        data: The data from the webhook.

    Returns:
        A GithubEvent proto.
    """
    return github_pb2.GithubEvent(
        pull_request_review=github_pb2.PullRequestReview(
            action=data["action"],
            pull_request=_data_to_pull_request(data, False),
            review=github_pb2.Review(
                body=data["review"]["body"],
                commit_id=data["review"]["commit_id"],
                id=data["review"]["id"],
                state=data["review"]["state"],
                submitted_at=data["review"]["submitted_at"],
                user=_data_to_user(data["review"]["user"]),
            ),
        )
    )


def parse_pull_request_review_comment(data) -> github_pb2.GithubEvent:
    """Parse a pull_request_review_comment event.

    Args:
        data: The data from the webhook.

    Returns:
        A GithubEvent proto.
    """
    return github_pb2.GithubEvent(
        pull_request_review_comment=github_pb2.PullRequestReviewComment(
            action=data["action"],
            pull_request=_data_to_pull_request(data, False),
            comment=_data_to_comment(data["comment"]),
        )
    )

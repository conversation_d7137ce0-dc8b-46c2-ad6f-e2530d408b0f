// K8S deployment file for the github webhook
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local projectId = cloudInfo[cloud].projectId;
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  assert cloud == 'GCP_US_CENTRAL1_DEV' || cloud == 'GCP_US_CENTRAL1_PROD';
  local gcpObjects = [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMServiceAccount',
      metadata: {
        name: '%s-github-webhook-iam' % namespace,
        namespace: namespace,
        labels: {
          app: 'github-webhook',
        },
      },
      spec: {
        displayName: '%s-github-webhook-iam' % namespace,
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'github-webhook-workload-identity',
        namespace: namespace,
        labels: {
          app: 'github-webhook',
        },
      },
      spec: {
        resourceRef: {
          apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
          kind: 'IAMServiceAccount',
          name: '%s-github-webhook-iam' % namespace,
        },
        bindings: [
          {
            role: 'roles/iam.workloadIdentityUser',
            members: [
              'serviceAccount:%s.svc.id.goog[%s/github-webhook-sa]' % [projectId, namespace],
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubTopic',
      metadata: {
        name: 'github-%s-topic' % namespace,
        namespace: namespace,
        labels: {
          app: 'github-webhook',
        },
      },
      spec: {
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'github-topic-policy',
        namespace: namespace,
        labels: {
          app: 'github-webhook',
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubTopic',
          name: 'github-%s-topic' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.publisher',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: '%s-github-webhook-iam' % namespace,
                  },
                },
              },
            ],
          },
        ],
      },
    },
  ];
  local serviceAccount = {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
    metadata: {
      name: 'github-webhook-sa',
      namespace: namespace,
      annotations: {
        'iam.gke.io/gcp-service-account': '%s-github-webhook-iam@%s.iam.gserviceaccount.com' % [namespace, projectId],
      },
      labels: {
        app: 'github-webhook',
      },
    },
  };
  local backendConfig = gcpLib.createBackendConfig(app='github-webhook',
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTPS',
                                                     requestPath: '/health',
                                                   });
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'github-webhook-svc',
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'viewer-https': 'HTTPS' }),
      },
      labels: {
        app: 'github-webhook',
      },
    },
    spec: {
      selector: {
        'app.kubernetes.io/name': 'github-webhook',
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'viewer-https',
          targetPort: 'viewer-https',
        },
      ],
    },
  };
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'github-webhook-config',
      namespace: namespace,
      labels: {
        app: 'github-webhook',
      },
    },
    data: {
      'flask.cfg': |||
        PORT=5000
        HTTPS_SERVER_KEY="/https-certs/tls.key"
        HTTPS_SERVER_CERT="/https-certs/tls.crt"
        CLIENT_MTLS=False
        PUBSUB_TOPIC="%s"
      ||| % ['projects/%s/topics/github-%s-topic' % [cloudInfo[cloud].projectId, namespace]],
    },
  };
  local container =
    {
      name: 'github-webhook',
      target: {
        name: '//tools/bazel_runner/github_webhook:image',
        dst: 'github-webhook-image',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'viewer-https',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        {
          mountPath: '/https-certs',
          name: 'https-certs',
        },
        {
          mountPath: '/tmp/prometheus_multiproc_dir',
          name: 'prometheus-multiproc-dir',
        },
      ],
      env: [
        {
          name: 'CONFIG_FILE',
          value: '/config/flask.cfg',
        },
        {
          name: 'GITHUB_WEBHOOK_SECRET',
          valueFrom: {
            secretKeyRef: {
              name: 'github-webhook-secret',
              key: 'secret',
            },
          },
        },
        {
          name: 'PROMETHEUS_MULTIPROC_DIR',
          value: '/tmp/prometheus_multiproc_dir',
        },
      ],
      readinessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 60,
      },
      livenessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 60,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '512Mi',
        },
      },
    };
  local secret = {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: 'github-webhook-secret',
      namespace: namespace,
      creationTimestamp: null,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
      labels: {
        app: 'github-webhook',
      },
    },
    spec: {
      template: {
        metadata: {
          name: 'github-webhook-secret',
          namespace: namespace,
          creationTimestamp: null,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
        type: 'Opaque',
      },
      encryptedData: {
        secret: {
          GCP_US_CENTRAL1_DEV: 'AgBO6yCmniLxnFUgyLga/jW5/MkRbhGWW7mDMTbcF3CR1Q74WInqiCNSB37Ope8GHepa2QdUmPOvyJJjO3+uSy67bMuqydViBamGOeIHMxwf2a68ksP18lAr6dMQeyTzfbN4ZmoW84dGztCwH9YV5vvkIAr2l5OBYGgr0qPLLPWBGneMb9XonuQs0bMx6u8Rte2vKK1lMaeS+Wp/upmRwKVO9d/dpsGPJBcHi9e6onpPZzGB8xGMifOMZYY5gzahWG/TbbzJqNlrzTUTNeg+c4g9rNMU0tjCOwF+/ocWTMzXptQduQ8GbM3BAFdaX8VmhDen7S1zZ75AHEBd1NYqMDMi1n6gmyLE5E4nBWCGGJyJtCGiMwgCcHPMFtDtyvHjAb3ZA78P0nNMhMlNzNSOsrqRRXM28/zhXOPBNMEGavLltNf1qkc208KJ5TDdzZ1/kN1bzG42tteXfouVBH5Dlaiast8SfiFxE0aaliqUYiLwKeHuLFtQGCbHWUecAfslrrdIUIWd8KnnzOhy5LcgFvBwZS/Qznfp2D1NoGywlY3CUVOl7JGu/8iZqmTYeNVxENEhUnWywGZj6O0koTIohn9H/88vkVRGeKygSMHFHvJt8rdPRDnnEs2M26Kg5D//hTZoyDsLC3EH1Vk3a4Xr3uX9sUrehOygMa5kG73ajiATjZLGDPS4S42Ren3Fl3h5yZ1d9lJUqo0Qs1ojGGUoa+JPRN5Z4pKVaR8/Ao8dtMA=',  // pragma: allowlist secret
          GCP_US_CENTRAL1_PROD: 'AgAHMpS/5VE8JTOuAaFNEBz7YfM9yzMA2lJ4gly2/Zo74sDHHzQbLmIlm/mc3GpcIr43M+WNHGQOvj7TRI6uwZJpKbCLFAn6TE2ztRcMzxzLkE4tVWzcptbcOvPFWng2ucBsw5qBDH9PIYG/KFStstGgJwF/u2oA/U5ghz9Y9c31OJPWMVb1jkHwmyxmGO3MWWenhbJAX9B9MlKzzo4SaD3MDp5vO3+CLawr8+EzyHD4Ta2KSl/tDxNJOfxmg+7oneNRpJ1VMRqb2GSkHu8laWC1VZtbqdUrwKhKRoF8UOTr1DY1RD9aHZEhQxEx6ecPaXc23807J/vu+rTWepQJzsAyrr9mZZ3QJhU4x+syNVIkeL3x9xTX/tafUpL0bVeQEU/g8AzBpQeFCYqs22eHebJxX/XPUZQKVDOJZy7FR9/TEzJjf96pSqiB35Z9HDixYuVy+3+IzN+RwxXm9t5KvuRupnvmleOwHXdYEFz6Kr8iD7ks30t6rnwuRioXYSW08WlLYVAhlo/VxfutcUYG4WMOTt9tF5sIug1gDqo9OGSmSx9vBoOIfgPILSD95BooZ8PPjTz0iT3SS2Bz+fPMm346HqaneTxmmHaPI4BF99DB16sKvd6ZVAw/lyUWQqvrqjY1BM/9Hf8QK3f6RX2M7mWQNxZVqgMZflwMo/8644ir54mHY8Ahtjw9j/p0Z6JdjM0tteHdN263T9aNnMcqfRf5UDAZbnh7WdVlq9wo8h8=',
        }[cloud],
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName='github-webhook');
  local pod =
    {
      containers: [
        container,
      ],
      affinity: affinity,
      tolerations: tolerations,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      serviceAccountName: 'github-webhook-sa',
      volumes: [
        {
          name: 'prometheus-multiproc-dir',
          emptyDir: {},
        },
        {
          name: 'github-webhook-secret',
          secret: {
            secretName: 'secret',  // pragma: allowlist secret
            optional: false,
          },
        },
        {
          name: 'config',
          configMap: {
            name: 'github-webhook-config',
            items: [
              {
                key: 'flask.cfg',
                path: 'flask.cfg',
              },
            ],
          },
        },
        {
          name: 'https-certs',
          secret: {
            secretName: 'github-webhook-ssl-cert',  // pragma: allowlist secret
          },
        },
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app='github-webhook', cloud=cloud, namespace=namespace);
  local ingressHostname = if namespace == 'devtools' then 'github-webhook.%s' % domainSuffix else 'github-webhook.%s.%s' % [namespace, domainSuffix];
  local ingress = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        name: 'github-webhook-ingress',
        namespace: namespace,
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: 'github-webhook',
        },
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'github-webhook-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'github-webhook-svc',
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];
  local deployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'github-webhook',
        namespace: namespace,
        labels: {
          app: 'github-webhook',
        },
      },
      spec: {
        minReadySeconds: if env == 'DEV' then 0 else 60,
        replicas: if env == 'DEV' then 1 else 2,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            'app.kubernetes.io/name': 'github-webhook',
          },
        },
        template: {
          metadata: {
            labels: {
              'app.kubernetes.io/name': 'github-webhook',
            },
          },
          spec: pod,
        },
      },
    };
  lib.flatten([
    secret,
    config,
    serviceAccount,
    service,
    deployment,
    ingress,
    gcpObjects,
  ])

<?xml version="1.0" encoding="utf-8"?>
<testsuites>
    <testsuite name="pytest" errors="0" failures="1" skipped="0" tests="13" time="5.350"
        timestamp="2023-06-08T02:54:55.349799" hostname="ip-172-31-5-34">
        <testcase classname="models.gptneox_memorize.tests.memorize.test_batch_checkpoint_attn"
            name="test_batch_checkpoint_attn" time="2.818" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_config"
            name="test_memory_config_v1" time="0.001">
            <failure message="assert 1 == 2&#10;  +1&#10;  -2">def test_memory_config_v1():
                from models.gptneox_memorize.megatron.neox_arguments.memory_config import (
                MemoryConfig,
                )

                assert_mem_cfg(
                MemoryConfig.from_dict({"style": "inlayer"}),
                query=MemoryConfig.Query.INLAYER,
                save=MemoryConfig.Save.LAYER_IN,
                train_batch_distract=False,
                version=1,
                )

                assert_mem_cfg(
                MemoryConfig.from_dict({"style": "prelayer"}),
                query=MemoryConfig.Query.PRELAYER,
                save=MemoryConfig.Save.LAYER_IN,
                train_batch_distract=False,
                version=1,
                )

                &gt; assert_mem_cfg(
                MemoryConfig.from_dict({"style": "prelayer", "train_batch_distract": True}),
                query=MemoryConfig.Query.PRELAYER,
                save=MemoryConfig.Save.LAYER_IN,
                train_batch_distract=True,
                version=2,
                )

                MemoryConfig = &lt;class
                'models.gptneox_memorize.megatron.neox_arguments.memory_config.MemoryConfig'&gt;
                models/gptneox_memorize/tests/memorize/test_memory_config.py:38:
                _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
                mem_cfg =
                &lt;models.gptneox_memorize.megatron.neox_arguments.memory_config.MemoryConfig
                object at 0x7f581f3d49d0&gt;
                query = &lt;Query.PRELAYER: 2&gt;, save = &lt;Save.LAYER_IN: 1&gt;
                train_batch_distract = True, version = 2

                def assert_mem_cfg(
                mem_cfg,
                query,
                save,
                train_batch_distract,
                version,
                ):
                assert mem_cfg.query == query
                assert mem_cfg.save == save
                assert mem_cfg.train_batch_distract == train_batch_distract
                &gt; assert mem_cfg.version == version
                E assert 1 == 2
                E +1
                E -2

                mem_cfg =
                &lt;models.gptneox_memorize.megatron.neox_arguments.memory_config.MemoryConfig
                object at 0x7f581f3d49d0&gt;
                query = &lt;Query.PRELAYER: 2&gt;
                save = &lt;Save.LAYER_IN: 1&gt;
                train_batch_distract = True
                version = 2

                models/gptneox_memorize/tests/memorize/test_memory_config.py:14: AssertionError</failure>
        </testcase>
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_config"
            name="test_memory_config_v2" time="0.001" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-3-5-dtype0-100-False]" time="0.013" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-1-1-1-dtype1-100-False]" time="0.010" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-3-5-7-dtype2-100-False]" time="0.011" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-5-3-dtype3-100-False]" time="0.011" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-3-5-dtype4-100-True]" time="0.016" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-5-3-dtype5-100-True]" time="0.016" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_names"
            name="test_memory_name_from_text" time="0.007" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_names"
            name="test_memory_name_from_file" time="0.001" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_snapshot"
            name="test_concat_array" time="0.044" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_snapshot"
            name="test_memory_snapshot" time="0.015" />
    </testsuite>
</testsuites>

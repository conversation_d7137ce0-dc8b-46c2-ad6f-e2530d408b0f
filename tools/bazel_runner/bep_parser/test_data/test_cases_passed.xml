<?xml version="1.0" encoding="utf-8"?>
<testsuites>
    <testsuite name="pytest" errors="0" failures="0" skipped="0" tests="13" time="11.153"
        timestamp="2023-06-06T17:53:38.797035" hostname="ip-172-31-5-34">
        <testcase classname="models.gptneox_memorize.tests.memorize.test_batch_checkpoint_attn"
            name="test_batch_checkpoint_attn" time="5.051" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_config"
            name="test_memory_config_v1" time="0.000" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_config"
            name="test_memory_config_v2" time="0.001" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-3-5-dtype0-100-False]" time="0.014" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-1-1-1-dtype1-100-False]" time="0.010" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-3-5-7-dtype2-100-False]" time="0.011" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-5-3-dtype3-100-False]" time="0.011" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-3-5-dtype4-100-True]" time="0.016" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_live"
            name="test_memory_buffer[cpu-7-5-3-dtype5-100-True]" time="0.015" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_names"
            name="test_memory_name_from_text" time="0.005" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_names"
            name="test_memory_name_from_file" time="0.001" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_snapshot"
            name="test_concat_array" time="0.050" />
        <testcase classname="models.gptneox_memorize.tests.memorize.test_memory_snapshot"
            name="test_memory_snapshot" time="0.022" />
    </testsuite>
</testsuites>

"""Unit tests for the test case parser."""

from pathlib import Path

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
from tools.bazel_runner.bep_parser.test_case_parser import parse_test_suites_xml_content


def test_passed():
    """Parses a case where all test cases passed."""
    content = Path(
        "tools/bazel_runner/bep_parser/test_data/test_cases_passed.xml"
    ).read_bytes()
    test_case_data = parse_test_suites_xml_content(content)
    assert test_case_data

    assert all(
        t.status == build_event_stream_pb2.TestStatus.PASSED for t in test_case_data
    )

    test_case_names = set([t.name for t in test_case_data])
    assert "test_memory_buffer[cpu-7-5-3-dtype5-100-True]" in test_case_names


def test_failed():
    """Parses a case where a test case failed."""
    content = Path(
        "tools/bazel_runner/bep_parser/test_data/test_cases_failed.xml"
    ).read_bytes()
    test_case_data = parse_test_suites_xml_content(content)
    assert test_case_data

    failed_test_case = [
        t
        for t in test_case_data
        if t.status == build_event_stream_pb2.TestStatus.FAILED
    ]
    assert failed_test_case
    assert len(failed_test_case) == 1
    assert failed_test_case[0].message == "assert 1 == 2\n  +1\n  -2"

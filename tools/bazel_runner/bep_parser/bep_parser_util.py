"""Utility for Bazel Event Protocol (BEP) files."""

import logging
import sys
from pathlib import Path

import tools.bazel_runner.bep_parser.bep_parser as bep_parser
from base.logging.console_logging import setup_console_logging


def main():
    """Main entry function when used as a binary."""
    setup_console_logging()
    for event in bep_parser.read_file(Path(sys.argv[1])):
        logging.info("Event:  %s", event)
        summary = bep_parser.get_test_summary(event)
        if summary:
            logging.info("Summary:  %s", summary)


if __name__ == "__main__":
    main()

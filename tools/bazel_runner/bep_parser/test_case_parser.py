"""Parses junit test case files."""

from defusedxml.ElementTree import fromstring

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
from tools.bazel_runner.bep_parser import test_summary_pb2


def parse_test_suites_xml_content(content: bytes) -> list[test_summary_pb2.TestCase]:
    """Parses a junit test result XML file into test cases."""
    root = fromstring(content)
    results = []
    for test_case in root.iter("testcase"):
        result = test_summary_pb2.TestCase()
        suite_name = test_case.attrib.get("classname")
        if suite_name:
            result.suite_name = suite_name
        result.name = test_case.attrib.get("name", "")
        if result.suite_name == result.name:
            result.suite_name = ""
        test_case_time = float(test_case.attrib.get("time", "0"))
        result.duration.FromMilliseconds(int(test_case_time * 1000))

        failure_tag = test_case.find("failure")
        error_tag = test_case.find("error")
        if failure_tag is None and error_tag is None:
            result.status = build_event_stream_pb2.TestStatus.PASSED
        elif failure_tag is not None:
            result.status = build_event_stream_pb2.TestStatus.FAILED
            result.message = failure_tag.attrib.get("message", "")
        elif error_tag is not None:
            result.status = build_event_stream_pb2.TestStatus.FAILED
            result.message = error_tag.attrib.get("message", "")
        results.append(result)
    return results

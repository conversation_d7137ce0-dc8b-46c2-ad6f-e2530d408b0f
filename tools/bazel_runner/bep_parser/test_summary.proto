syntax = "proto3";

import "google/protobuf/duration.proto";
import "third_party/proto/bazel_build/build_event_stream.proto";

// the test of the test target as captures by the test summary
enum TestState {
  NOT_SET = 0;
  // the test target was detected by the configured by the test run, but not
  // yet executed.
  CONFIGURED = 1;

  // the test target was finished. Each test target with a summary should
  // have a test summary with state CONFIGURED before it in the event stream.
  SUMMARY = 2;
}

// information about a test as part of the execution event stream
message TestSummary {
  // bazel target name
  string target_name = 1;

  // state of the test target reported.
  // certain information are only available with SUMMARY.
  TestState state = 2;

  // set when state is SUMMARY
  build_event_stream.TestStatus overall_status = 3;

  // set when state is SUMMARY
  oneof output_type {
    bool aborted = 4;
    string output_uri = 5;
    string output = 6;
  }
  // set when state is summary
  google.protobuf.Duration duration = 7;

  // The size of the test, if the target is a test target. Unset otherwise.
  build_event_stream.TestSize test_size = 8;

  // List of all tags associated with this target (for all possible
  // configurations).
  repeated string tags = 9;

  // True if the test was cached.
  bool cached = 10;
}

// message representing a test case that was executed as part of a test
message TestCase {
  // name of the test suite
  // the exact value is defined by the test rule, e.g.
  // in pytest it is derived from the name of the file.
  string suite_name = 1;

  // name of the test case
  string name = 2;

  // time it took to run the test case
  google.protobuf.Duration duration = 3;

  // the test status reported for the test case
  build_event_stream.TestStatus status = 4;

  // an optional failure message
  string message = 5;
}

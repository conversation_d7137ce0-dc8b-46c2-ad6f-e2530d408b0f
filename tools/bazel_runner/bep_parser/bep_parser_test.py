"""Unit tests for the bep_parser."""

from pathlib import Path

import test_summary_pb2
from bep_parser import get_test_summaries, read_file
from google.protobuf.duration_pb2 import Duration

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2


def test_success():
    """Tests the test summarization when all tests are passing."""
    test_targets = list(
        get_test_summaries(
            read_file(Path("tools/bazel_runner/bep_parser/test_data/test_passed.pb"))
        )
    )
    expected_targets = [
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:release.lint",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
            tags=["lint"],
        ),
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:test",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
        ),
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:release.lint",
            state=test_summary_pb2.TestState.SUMMARY,
            duration=Duration(seconds=8, nanos=949000000),
            overall_status=build_event_stream_pb2.TestStatus.PASSED,
            cached=True,
            output_uri="bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/439b679ce917496e45a121022079cf9a6e0e71755e0d8211f2d7e43a5fc6fd01/173",
        ),
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:test",
            state=test_summary_pb2.TestState.SUMMARY,
            duration=Duration(seconds=7, nanos=74000000),
            overall_status=build_event_stream_pb2.TestStatus.PASSED,
            cached=True,
            output_uri="bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/288c153a9e08431492554f708460adfef3215c28eb2f96098b958c3e852a817a/13302",
        ),
    ]
    assert test_targets == expected_targets


def test_test_failed():
    """Tests the test summarization when a test is failing."""
    test_targets = list(
        get_test_summaries(
            read_file(Path("tools/bazel_runner/bep_parser/test_data/test_failed.pb"))
        )
    )
    assert test_targets == [
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:release.lint",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
            tags=["lint"],
        ),
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:test",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
        ),
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:release.lint",
            state=test_summary_pb2.TestState.SUMMARY,
            duration=Duration(seconds=8, nanos=949000000),
            overall_status=build_event_stream_pb2.TestStatus.PASSED,
            cached=True,
            output_uri="bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/439b679ce917496e45a121022079cf9a6e0e71755e0d8211f2d7e43a5fc6fd01/173",
        ),
        test_summary_pb2.TestSummary(
            target_name="//clients/vscode:test",
            state=test_summary_pb2.TestState.SUMMARY,
            duration=Duration(seconds=6, nanos=323000000),
            overall_status=build_event_stream_pb2.TestStatus.FAILED,
            output_uri="bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/ed24be6531872ff5f247ea5000029492b349f79c78392c7154a9efb5675e6973/19829",
        ),
    ]


def test_build_failed():
    """Tests the test summarization when a build dependency of a test is failing."""
    test_targets = list(
        get_test_summaries(
            read_file(Path("tools/bazel_runner/bep_parser/test_data/build_failed.pb"))
        )
    )
    test_targets = list(sorted(test_targets, key=lambda t: t.target_name))
    expected_targets = [
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:augment_model_2b_test",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
            tags=["gpu"],
        ),
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:augment_model_350m_test",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
            tags=["gpu"],
        ),
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:augment_weight_test",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
            tags=["cpu:8", "exclusive", "gpu"],
        ),
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:memory_attention_test",
            state=test_summary_pb2.TestState.CONFIGURED,
            test_size=build_event_stream_pb2.TestSize.MEDIUM,
            tags=["gpu"],
        ),
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:augment_weight_test",
            state=test_summary_pb2.TestState.SUMMARY,
            overall_status=build_event_stream_pb2.TestStatus.NO_STATUS,
            aborted=True,
        ),
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:memory_attention_test",
            state=test_summary_pb2.TestState.SUMMARY,
            overall_status=build_event_stream_pb2.TestStatus.PASSED,
            cached=True,
            duration=Duration(seconds=11, nanos=472000000),
            output_uri="bytestream://internal-a23d8efb8c77349b9b3fe27b7ce528b3-1066693167.us-west-2.elb.amazonaws.com:9092/blobs/022f00f0209269644d358a8288c580581f3d55b53228108cbbde06cc54dd4ec8/6474",
        ),
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:augment_model_350m_test",
            state=test_summary_pb2.TestState.SUMMARY,
            overall_status=build_event_stream_pb2.TestStatus.NO_STATUS,
            aborted=True,
        ),
    ]
    no_status_targets = [
        test_summary_pb2.TestSummary(
            target_name="//models/inference/src/models/tests:augment_model_2b_test",
        ),
    ]
    assert len(no_status_targets) + len(expected_targets) == 8
    for expected_target in expected_targets:
        assert expected_target in test_targets

    def find_actual_target(ts):
        return (
            ts.target_name == no_status_target.target_name
            and ts.state == test_summary_pb2.TestState.SUMMARY
            and ts.overall_status == build_event_stream_pb2.TestStatus.NO_STATUS
        )

    for no_status_target in no_status_targets:
        actual_target = list(
            filter(
                find_actual_target,
                test_targets,
            )
        )
        assert actual_target
        assert actual_target[0].output

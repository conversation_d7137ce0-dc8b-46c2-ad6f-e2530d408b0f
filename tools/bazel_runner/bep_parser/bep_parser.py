"""Parsing library for Bazel Event Protocol (BEP) files."""

import typing
from pathlib import Path
from typing import Binary<PERSON>, Iterator, Optional

import delimited_protobuf

from third_party.proto.bazel_build import build_event_stream_pb2
from tools.bazel_runner.bep_parser import test_summary_pb2


def read_file(
    bep_file: Path,
) -> typing.Iterator[build_event_stream_pb2.BuildEvent]:
    """Reads a file and generates a stream of build events.

    This assumes that the file is a binary Build Event Protocol file.
    """
    with bep_file.open("rb") as f:
        for event in read_stream(f):
            yield event


def read_stream(
    stream: BinaryIO,
) -> typing.Iterator[build_event_stream_pb2.BuildEvent]:
    """Reads a binary stream and generates a stream of build events.

    This assumes that the stream contains binary Build Event Protocol events.
    """
    while True:
        event = delimited_protobuf.read(stream, build_event_stream_pb2.BuildEvent)
        if not event:
            return
        yield event


def get_test_summary(
    event: build_event_stream_pb2.BuildEvent,
) -> Optional[test_summary_pb2.TestSummary]:
    """Returns the test summary (if available) given a build event."""
    if event.id.HasField("target_configured"):
        # we are expected to run this target
        test_summary = test_summary_pb2.TestSummary(
            target_name=event.id.target_configured.label,
            state=test_summary_pb2.TestState.CONFIGURED,
        )
        test_summary.test_size = event.configured.test_size
        test_summary.tags.extend(event.configured.tag)
        return test_summary
    if event.id.HasField("test_summary"):
        uri: str = ""
        output: str = ""
        if event.test_summary.passed:
            uri = event.test_summary.passed[-1].uri
            output = event.test_summary.passed[-1].contents.decode("utf-8")
        elif event.test_summary.failed:
            uri = event.test_summary.failed[-1].uri
            output = event.test_summary.failed[-1].contents.decode("utf-8")
        test_summary = test_summary_pb2.TestSummary(
            target_name=event.id.test_summary.label,
            state=test_summary_pb2.TestState.SUMMARY,
            overall_status=event.test_summary.overall_status,
            output_uri=uri,
            cached=event.test_summary.total_num_cached > 0,
        )
        if event.HasField("aborted"):
            test_summary.aborted = True
        if output:
            test_summary.output = output
        if not test_summary.aborted:
            test_summary.duration.CopyFrom(event.test_summary.total_run_duration)
        return test_summary
    elif event.id.HasField("target_completed") and not event.completed.success:
        output: str = ""
        if event.completed.failure_detail.message:
            output = event.completed.failure_detail.message
        test_summary = test_summary_pb2.TestSummary(
            target_name=event.id.target_completed.label,
            state=test_summary_pb2.TestState.SUMMARY,
            overall_status=build_event_stream_pb2.TestStatus.NO_STATUS,
            output=output,
        )
        if event.HasField("aborted"):
            test_summary.aborted = True
        return test_summary
    return None


def get_test_summaries(
    events: Iterator[build_event_stream_pb2.BuildEvent],
) -> typing.Iterator[test_summary_pb2.TestSummary]:
    """Filters a stream of BuildEvents and returns a list of failed test targets."""
    for event in events:
        test_summary = get_test_summary(event)
        if test_summary:
            yield test_summary

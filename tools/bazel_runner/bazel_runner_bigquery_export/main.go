package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/pubsub"
	bazelrunnerpb "github.com/augmentcode/augment/tools/bazel_runner/server/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

type Config struct {
	ProjectID        string `json:"project_id"`
	SubscriptionName string `json:"subscription_name"`
	DatasetName      string `json:"dataset_name"`
	TableName        string `json:"table_name"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`
}

var bazelRunnerEvents = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "bazel_runner_bigquery_events",
		Help: "Number of bazel runner events exported to BigQuery",
	},
	[]string{"status"},
)

type Row struct {
	RunID     string            `bigquery:"run_id"`
	State     string            `bigquery:"state"`
	Time      time.Time         `bigquery:"time"`
	EventData bigquery.NullJSON `bigquery:"event_data"`
}

func main() {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	// Load config from file
	var config Config
	configFile := flag.String("config", "config.json", "Path to config file")
	healthFile := flag.String("health-file", "", "Path to a file to write a health check to")
	flag.Parse()
	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to open config file")
	}
	defer f.Close()
	err = json.NewDecoder(f).Decode(&config)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to parse config file")
	}

	log.Info().Msgf("Config: %v", config)

	// Create a Pub/Sub client
	ctx := context.Background()
	pubsubClient, err := pubsub.NewClient(ctx, config.ProjectID)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Pub/Sub client")
	}

	// Create a BigQuery client
	bqClient, err := bigquery.NewClient(ctx, config.ProjectID)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create BigQuery client")
	}

	// Create a subscription
	subscription := pubsubClient.Subscription(config.SubscriptionName)

	// Create a BigQuery table
	table := bqClient.Dataset(config.DatasetName).Table(config.TableName)

	inserter := table.Inserter()

	prometheus.MustRegister(
		bazelRunnerEvents,
	)

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
			os.Exit(1)
		}
	}()

	log.Info().Msgf("Starting to receive messages from subscription from %s", config.SubscriptionName)
	if *healthFile != "" {
		f, err := os.Create(*healthFile)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create health file")
		}
		defer f.Close()
		f.WriteString("OK")
	}

	// Start receiving messages from the subscription
	err = subscription.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		// Unmarshal the protobuf message
		var event bazelrunnerpb.TestRunInfo
		err := proto.Unmarshal(msg.Data, &event)
		if err != nil {
			bazelRunnerEvents.WithLabelValues("ERROR").Inc()
			log.Error().Err(err).Msg("Failed to unmarshal protobuf message")
			return
		}

		marshaler := protojson.MarshalOptions{
			Indent: "  ",
		}
		jsonData, err := marshaler.Marshal(&event)
		log.Info().Msgf("JSON: %s", jsonData)
		if err != nil {
			bazelRunnerEvents.WithLabelValues("ERROR").Inc()
			log.Error().Err(err).Msg("Failed to convert to json")
			return
		}
		// Convert the protobuf message to a BigQuery row
		row := Row{
			RunID: event.RunId,
			State: event.State.String(),
			Time:  time.Unix(event.CreateTime.Seconds, int64(event.CreateTime.Nanos)),
			EventData: bigquery.NullJSON{
				Valid:   true,
				JSONVal: string(jsonData),
			},
		}

		// Insert the row into BigQuery
		err = inserter.Put(ctx, &row)
		if err != nil {
			bazelRunnerEvents.WithLabelValues("ERROR").Inc()
			log.Error().Err(err).Msg("Failed to insert row into BigQuery")
			return
		}
		bazelRunnerEvents.WithLabelValues("OK").Inc()
		msg.Ack()
	})
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to receive messages from subscription")
	}
}

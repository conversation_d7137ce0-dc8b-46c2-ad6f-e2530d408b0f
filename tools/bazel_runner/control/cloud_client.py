"""Cloud client for the job control."""

import argparse
import logging
import pathlib
import tempfile
import typing

from google.cloud import storage  # type: ignore
from google.cloud.storage import transfer_manager

from tools.bazel_runner.control.bazel_runner_lib import AbortException


class CloudClient(typing.Protocol):
    """Cloud client for the job control."""

    def handle_abort(self, ex: AbortException):
        """Handle an abort exception.

        This uploads the well-known file that contains the ABORT information.
        """
        raise NotImplementedError()

    def copy_tree(self, src_path: pathlib.Path):
        """Copies a file tree from src path to output path."""
        raise NotImplementedError()


class GcpCloudClient(CloudClient):
    """Cloud client for the job control for GCP."""

    def __init__(self, args):
        self.storage_client = storage.Client()
        self.args = args
        bucket_name, _, storage_path = args.storage_output.partition("/")
        self.storage_path = pathlib.Path(storage_path)
        self.bucket = self.storage_client.bucket(bucket_name)

    def handle_abort(self, ex: AbortException):
        """Handle an abort exception.

        This uploads the well-known file that contains the ABORT information.
        """
        aborted_path = self.storage_path / "ABORTED"
        with tempfile.TemporaryFile("wb+") as tmp_file:
            tmp_file.write(ex.abort_message.encode("utf-8"))
            tmp_file.flush()
            tmp_file.seek(0)

            blob = self.bucket.blob(str(aborted_path))
            blob.upload_from_file(tmp_file)

    def copy_tree(self, src_path: pathlib.Path):
        """Copies a file tree from src path to output path."""

        file_names = []
        for test_file in src_path.glob("**/*"):
            if not test_file.is_file():
                continue
            relative_path = test_file.relative_to(src_path)
            file_names.append(str(relative_path))
        results = transfer_manager.upload_many_from_filenames(
            self.bucket,
            file_names,
            source_directory=str(src_path),
            max_workers=8,
            blob_name_prefix=f"{self.storage_path}/",
        )
        for name, result in zip(file_names, results):
            # The results list is either `None` or an exception for each filename in
            # the input list, in order.

            if isinstance(result, Exception):
                logging.error("Failed to upload %s due to exception: %s", name, result)
                raise result
            else:
                logging.info("Uploaded %s", name)


class FailingCloudClient(CloudClient):
    """A cloud client that fails on all operations."""

    def handle_abort(self, ex: AbortException):
        raise ValueError("This cloud client is not supposed to handle aborts")

    def copy_tree(self, src_path: pathlib.Path):
        raise ValueError("This cloud client is not supposed to copy files")


def create(args: argparse.Namespace) -> CloudClient:
    """Create a cloud client."""
    if "cloud" not in dir(args) or "storage_output" not in dir(args):
        return FailingCloudClient()
    elif args.cloud.startswith("GCP"):
        return GcpCloudClient(args)
    raise ValueError("Unknown cloud")

// jsonnet file to run a shell for maintainance and debugging
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(cloud,
         pvId='10',
         imageDigest=':dev',
         namespace='test',
         serviceAccountName='bazel-runner-test-dev-dirk-sa',
         gpu=0) [
  local tolerations = nodeLib.tolerations(resource=if gpu == 0 then null else 'small', env='DEV', count=gpu, cloud=cloud);
  local affinity = nodeLib.affinity(resource=if gpu == 0 then null else 'small', env='DEV', count=gpu, cloud=cloud, appName=null);
  {
    apiVersion: 'v1',
    kind: 'Pod',
    metadata: {
      name: 'bazel-shell-%s' % pvId,
      namespace: namespace,
    },
    spec: {
      serviceAccountName: serviceAccountName,
      securityContext: {
        runAsUser: 1000,
        fsGroup: 1000,
        fsGroupChangePolicy: 'OnRootMismatch',
      },
      tolerations: tolerations,
      affinity: affinity,
      containers: [
        {
          name: 'bazel-runner',
          image: 'us-central1-docker.pkg.dev/system-services-dev/devtools-repository/devtools_bazel_runner%s' % imageDigest,
          imagePullPolicy: 'Always',
          command: ['/bin/bash'],
          args: ['-c', 'trap : TERM INT; sleep infinity & wait'],
          volumeMounts: [
            {
              name: 'persistent-storage',
              mountPath: '/mnt/efs/augment',
            },
            {
              mountPath: '/cache',
              name: 'cache-volume',
            },
          ],
          env: [
               ] +  // this is the location GKE injects the matching NVIDIA CUDA driver utils
               if gpu > 0 then [{
                 name: 'TRITON_CUDA_DIRS',
                 value: '/home/<USER>/.local/nvidia/lib64/',
               }] else [],
          resources: {
            limits: {
              cpu: '7',
              'nvidia.com/gpu': gpu,
              memory: '28Gi',
            },
          },
        },
      ],
      volumes: [
        {
          name: 'persistent-storage',
          persistentVolumeClaim: {
            claimName: 'filestore-checkpoint-claim',
          },
        },
        {
          name: 'cache-volume',
          persistentVolumeClaim: {
            claimName: 'bazel-runner-%s-pvc' % pvId,
          },
        },
      ],
      restartPolicy: 'Never',
    },
  },
]

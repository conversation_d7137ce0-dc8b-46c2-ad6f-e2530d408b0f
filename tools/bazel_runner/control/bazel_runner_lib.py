"""Functions shared between different components of the runner."""

import logging
import shutil
from pathlib import Path


class AbortException(Exception):
    """Exception that should be thrown in the runner when a test execution should be aborted.

    The execution will NOT be retried and will NOT proceed to the next step.
    The abort message will be displayed to the user.
    """

    def __init__(self, abort_message: str):
        self.abort_message = abort_message


def copy_tree(src_path: Path, output_path: Path):
    """Copies a file tree from src path to output path."""
    for test_file in src_path.glob("**/*"):
        if not test_file.is_file():
            continue
        relative_path = test_file.relative_to(src_path)
        target_file = output_path.joinpath(relative_path)
        logging.info("%s -> %s", test_file, target_file)
        target_file.parent.mkdir(parents=True, exist_ok=True)
        shutil.copyfile(test_file, target_file)


def copy_file(src_path: Path, output_path: Path):
    """Copies a tree from src path to output path."""
    logging.info("%s -> %s", src_path, output_path)
    src_path.parent.mkdir(parents=True, exist_ok=True)
    shutil.copyfile(src_path, output_path)

import pathlib
import tempfile

import git
import pytest


@pytest.fixture
def repo_dir():
    with tempfile.TemporaryDirectory() as tmp_dir:
        repo_dir = pathlib.Path(tmp_dir) / "test_repo"
        repo_dir.mkdir(parents=True, exist_ok=True)
        repo_dir.joinpath("WORKSPACE").write_text('workspace(name = "test_repo")')
        repo_dir.joinpath("BUILD").write_text(
            'sh_test(name = "test", srcs = ["test.sh"])'
        )
        repo_dir.joinpath("test.sh").write_text('echo "Hello, world!"')
        repo = git.Repo.init(str(repo_dir))
        repo.index.add(["BUILD", "test.sh"])
        repo.index.commit("Initial commit")
        yield repo_dir

"""Policies for test selection."""

from typing import Protocol

from tools.bazel_runner.test_selection_server.config import TestSelectionConfig
import tools.bazel_runner.test_selection_server.test_selection_pb2 as test_selection_pb2
from tools.bazel_runner.test_selection_server.target_info import TargetInfo


class TestSelection(Protocol):
    """Protocol for test selection policies."""

    def filter(self, target: TargetInfo) -> bool:
        """Returns true if the target should be run."""
        return True

    @property
    def pre_filter_impacted(self) -> bool:
        """If true is returned, only change impacted targets should be considered."""
        return True

    def extra_test_targets(self, selected_targets: list[str]) -> list[str]:
        """Returns a list of extra test targets that should be run in addition to the selected targets."""
        return []


class PreMergeTestSelection(TestSelection):
    """Test selection policy for pre-merge tests."""

    def __init__(self, config: TestSelectionConfig):
        self.config = config

    def filter(self, target: TargetInfo) -> bool:
        if "manual" in target.tags:
            return False
        if "postmerge-test" in target.tags:
            return False
        return True

    def extra_test_targets(self, selected_targets: list[str]) -> list[str]:
        if len(selected_targets) == 0:
            # if not test are selected, don't run any extra tests
            return []

        # special case for flags_validation_test
        if selected_targets == ["//tools/feature_flags:flags_validation_test"]:
            return ["//tools:format_test_Jsonnet_with_jsonnetfmt"]
        return self.config.pre_merge_extra_target_names


class PostMergeTestSelection(TestSelection):
    """Test selection policy for post-merge tests."""

    def __init__(self, config: TestSelectionConfig):
        self.config = config

    def filter(self, target: TargetInfo) -> bool:
        if "manual" in target.tags:
            return False
        return True

    def extra_test_targets(self, selected_targets: list[str]) -> list[str]:
        return self.config.post_merge_extra_target_names


class FullTestSelection(TestSelection):
    """Test selection policy to run all tests."""

    def filter(self, target: TargetInfo) -> bool:
        if "manual" in target.tags:
            return False
        return True

    @property
    def pre_filter_impacted(self) -> bool:
        """If true is returned, only change impacted targets should be considered."""
        # don't filter by impact
        return False


class DefaultTestSelection(TestSelection):
    """Test selection policy to run all default impacted tests."""

    def __init__(self, config: TestSelectionConfig):
        self.config = config

    def filter(self, target: TargetInfo) -> bool:
        if "manual" in target.tags:
            return False
        return True

    def extra_test_targets(self, selected_targets: list[str]) -> list[str]:
        return self.config.post_merge_extra_target_names


def get_test_selection(
    policy: test_selection_pb2.TestSelectionPolicy.ValueType,
    config: TestSelectionConfig,
) -> TestSelection:
    """Factory for test selection policies by name."""
    if policy == test_selection_pb2.TestSelectionPolicy.PRE_MERGE:
        return PreMergeTestSelection(config)
    elif policy == test_selection_pb2.TestSelectionPolicy.POST_MERGE:
        return PostMergeTestSelection(config)
    elif policy == test_selection_pb2.TestSelectionPolicy.DEFAULT:
        return DefaultTestSelection(config)
    elif policy == test_selection_pb2.TestSelectionPolicy.FULL:
        return FullTestSelection()
    else:
        raise ValueError("Invalid test selection policy name")

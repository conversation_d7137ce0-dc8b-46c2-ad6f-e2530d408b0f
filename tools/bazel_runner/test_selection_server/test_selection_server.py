"""Test Selection Server.

The test selection server managed which test targets to execute, e.g. for a pull request.
"""

import argparse
import logging
import pathlib
import uuid
from concurrent.futures import ThreadPoolExecutor

import grpc
import structlog
from google.cloud import pubsub_v1  # type: ignore
from google.longrunning import (
    operations_pb2,  # type: ignore
    operations_pb2_grpc,
)
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import tools.bazel_runner.test_selection_server.test_selection_pb2 as test_selection_pb2
import tools.bazel_runner.test_selection_server.test_selection_pb2_grpc as test_selection_pb2_grpc
import tools.bazel_runner.test_selection_server.test_selection_store_pb2 as test_selection_store_pb2
from base.logging.struct_logging import setup_struct_logging
from tools.bazel_runner.test_selection_server.config import Config, load_config
from tools.bazel_runner.test_selection_server.persistence import Persistence

log = structlog.get_logger()


class TestSelectionServices(
    test_selection_pb2_grpc.TestSelectionServicer,
    operations_pb2_grpc.OperationsServicer,
):
    """Implementation of test selection protocol."""

    def __init__(
        self,
        config: Config,
        persistence: Persistence,
        publisher: pubsub_v1.PublisherClient,
        topic_path: str,
    ):
        self.config = config
        self.persistence = persistence
        self.publisher = publisher
        self.topic_path = topic_path

    def GetOperation(self, request, context):
        logging.info("GetOperation: %s", request)
        try:
            state = self.persistence.get_operation(uuid.UUID(request.name))
            if state is None:
                context.abort(grpc.StatusCode.NOT_FOUND, "Failed to find operation")
            assert state is not None
            logging.info("GetOperation: state=%s", state)

            operation = self._get_operation(state)
            return operation
        except grpc.RpcError as ex:
            logging.error("GetOperation failed: %s", ex)
            logging.exception(ex)
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("GetOperation failed: %s", ex)
            logging.exception(ex)
            raise

    def _get_operation(
        self, state: test_selection_store_pb2.TestSelectionOperation
    ) -> operations_pb2.Operation:  # type: ignore
        operation = operations_pb2.Operation(name=str(state.operation_id))  # type: ignore

        if state.WhichOneof("response") == "result":
            operation.response.Pack(state.result)  # type: ignore
            operation.done = True  # type: ignore
        elif state.WhichOneof("response") == "error":
            operation.error.code = state.error.code  # type: ignore
            operation.error.message = state.error.message  # type: ignore
            operation.done = True  # type: ignore
        return operation

    def WaitOperation(self, request, context):
        """Wait for the result of a given job."""
        del request
        context.abort(grpc.StatusCode.UNIMPLEMENTED, "Not implemented")

    def ListOperations(self, request, context):
        del request
        context.abort(grpc.StatusCode.UNIMPLEMENTED, "Not implemented")

    def GetTestTargets(
        self, request: test_selection_pb2.GetTestTargetsRequest, context
    ):
        try:
            logging.info("GetTestTargets: %s", request)

            if not request.HasField("end_checkout"):
                context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Missing end_checkout")
            else:
                if not request.end_checkout.repo_name:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT,
                        "Missing end_checkout.repo_name",
                    )
                if not request.end_checkout.owner:
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Missing end_checkout.owner"
                    )

            # commit_checkout or pull_request_checkout is required
            if not request.end_checkout.HasField(
                "pull_request_checkout"
            ) and not request.end_checkout.HasField("commit_checkout"):
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "Missing end_checkout.commit_checkout",
                )

            # start_checkout is optional if end_checkout is a pull request
            # start_checkout is required if end_checkout is a commit
            if not request.HasField(
                "start_checkout"
            ) and not request.end_checkout.HasField("pull_request_checkout"):
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT, "Missing start_checkout"
                )

            # if start checkout is set, either commit_checkout or pull_request_checkout is required
            if request.HasField("start_checkout"):
                # commit_checkout or pull_request_checkout is required
                if not request.start_checkout.HasField(
                    "pull_request_checkout"
                ) and not request.start_checkout.HasField("commit_checkout"):
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, "Invalid start_checkout"
                    )
                if request.start_checkout.repo_name != request.end_checkout.repo_name:
                    context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Invalid repo_name")
                if request.start_checkout.owner != request.end_checkout.owner:
                    context.abort(grpc.StatusCode.INVALID_ARGUMENT, "Invalid owner")

            operation_id = str(uuid.uuid4())
            self.persistence.create_operation(operation_id, request)

            self.publisher.publish(self.topic_path, operation_id.encode("utf-8"))

            response = test_selection_pb2.GetTestTargetsResponse()
            response.operation_id = str(operation_id)
            return response
        except grpc.RpcError as ex:
            logging.error("GetImpactedTargets failed: %s", ex)
            logging.exception(ex)
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("GetImpactedTargets failed: %s", ex)
            logging.exception(ex)
            raise


def _serve(config):
    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(config.project_id, config.topic_name)

    current_persistence = Persistence.create(config)

    server = grpc.server(ThreadPoolExecutor(max_workers=10))
    services = TestSelectionServices(config, current_persistence, publisher, topic_path)
    test_selection_pb2_grpc.add_TestSelectionServicer_to_server(services, server)
    operations_pb2_grpc.add_OperationsServicer_to_server(services, server)
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)
    service_names = (
        test_selection_pb2.DESCRIPTOR.services_by_name["TestSelection"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    server.add_insecure_port("[::]:50051")
    server.start()
    log.info("Listening on 50051")

    server.wait_for_termination()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()

    setup_struct_logging()
    config = load_config(args.config_file)
    log.info("Config %s", config)

    _serve(config)


if __name__ == "__main__":
    main()

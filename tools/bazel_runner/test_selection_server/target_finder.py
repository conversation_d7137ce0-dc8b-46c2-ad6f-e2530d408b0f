"""Module to find bazel target changed between two checkouts."""

import logging
import pathlib
import shlex
import subprocess
import tempfile

import git
import grpc

from tools.bazel_lib import bazel


def _get_bazel_differ_path() -> str:
    return "../gazelle~~go_deps~com_github_ewhauser_bazel_differ/cli/bazel-differ-linux-amd64_/bazel-differ-linux-amd64"


class TargetFinderException(Exception):
    """Exception thrown by the target finder."""

    def __init__(self, message: str, status_code: grpc.StatusCode):
        self.message = message
        self.status_code = status_code


class TargetFinder:
    """Class to find bazel target changed between two checkouts."""

    def __init__(self, workspace: pathlib.Path, extra_startup_args: str | None):
        self.workspace = workspace
        self.extra_startup_args = extra_startup_args
        self.cache = {}

    def _update_cache(self, repo_name: str, temp_file_name: pathlib.Path):
        repo = git.Repo(str(self.workspace / repo_name))  # type: ignore
        sha = repo.head.object.hexsha
        self.cache[(repo_name, sha)] = temp_file_name

    def _get_cache(self, repo_name: str):
        repo = git.Repo(str(self.workspace / repo_name))  # type: ignore
        sha = repo.head.object.hexsha
        c = self.cache.get((repo_name, sha))
        if c:
            logging.info("Cache hit: %s/%s", repo_name, sha)
        return c

    def generate_hash(self, repo_name: str) -> pathlib.Path:
        """Generate a hash file based on the current checkout.

        The hash file contains information created by and useful for `bazel-diff` about
        the state of the bazel targets at the given checkout.
        """
        c = self._get_cache(repo_name)
        if c:
            # we cache the file as we might get repeated calls, e.g. against main
            return c
        b = bazel.get_bazel_path()
        with tempfile.NamedTemporaryFile(delete=False) as f:
            cmd = [
                _get_bazel_differ_path(),
                "-b",
                b,
                "generate-hashes",
                "-w",
                str(self.workspace / repo_name),
                str(f.name),
            ]
            if self.extra_startup_args:
                cmd.append(f"--bazelStartupOptions={self.extra_startup_args}")

            logging.info("Command %s", shlex.join([str(c) for c in cmd]))
            r = subprocess.run(
                cmd,
                encoding="utf-8",
                capture_output=True,
                check=False,
                env=bazel.get_bazel_env(),
            )
            if r.returncode:
                logging.warning(
                    "Failed to generate hash file: return_code=%s", r.returncode
                )
                if r.stdout:
                    logging.warning("STDOUT")
                    logging.warning(r.stdout)
                if r.stderr:
                    logging.warning("STDERR")
                    logging.warning(r.stderr)
                logging.warning(
                    "Failed to generate hash file: return_code=%s", r.returncode
                )
                raise TargetFinderException(
                    "Failed to get impacted test targets", grpc.StatusCode.UNKNOWN
                )

            self._update_cache(repo_name, pathlib.Path(f.name))
            return pathlib.Path(f.name)

    def get_all_targets(self, repo_name: str) -> list[str]:
        """Return the list of all targets from the given repo."""
        cmd = [bazel.get_bazel_path()]
        if self.extra_startup_args:
            cmd.extend(shlex.split(self.extra_startup_args))
        cmd += ["query", "//..."]
        logging.info("Command %s", shlex.join(cmd))
        r = subprocess.run(
            cmd,
            cwd=self.workspace / repo_name,
            capture_output=True,
            encoding="utf-8",
            check=False,
            env=bazel.get_bazel_env(),
        )
        assert r.stdout
        if r.returncode:
            logging.warning("Failed to get targets: return_code=%s", r.returncode)
            if r.stdout:
                logging.warning("STDOUT")
                logging.warning(r.stdout)
            if r.stderr:
                logging.warning("STDERR")
                logging.warning(r.stderr)
            raise TargetFinderException(
                "Failed to get targets", grpc.StatusCode.UNKNOWN
            )
        return r.stdout.splitlines()

    def get_impacted_targets(
        self,
        start_hash_file: pathlib.Path,
        final_hash_file: pathlib.Path,
    ) -> list[str]:
        """Return the list of targets impacted by the changes between two hash files.

        It takes the two hash files created by generate_hash() and returns the
        list of target names. The current state of the checkout does not matter
        for this operation.
        """
        with tempfile.NamedTemporaryFile(mode="w+", encoding="utf-8") as f:
            b = bazel.get_bazel_path()
            cmd = [
                _get_bazel_differ_path(),
                "-b",
                b,
                "diff",
                "-s",
                str(start_hash_file),
                "-f",
                final_hash_file,
                "-o",
                str(f.name),
            ]
            logging.info("Command %s", shlex.join([str(c) for c in cmd]))
            r = subprocess.run(
                cmd,
                encoding="utf-8",
                check=False,
                capture_output=True,
                env=bazel.get_bazel_env(),
            )
            if r.returncode:
                logging.warning(
                    "Failed to get impacted test targets: return_code=%s", r.returncode
                )
                if r.stdout:
                    logging.warning("STDOUT")
                    logging.warning(r.stdout)
                if r.stderr:
                    logging.warning("STDERR")
                    logging.warning(r.stderr)
                logging.warning(
                    "Failed to get impacted test targets: return_code=%s", r.returncode
                )
                raise TargetFinderException(
                    "Failed to get impacted test targets", grpc.StatusCode.UNKNOWN
                )
            f.seek(0)
            return f.read().splitlines()

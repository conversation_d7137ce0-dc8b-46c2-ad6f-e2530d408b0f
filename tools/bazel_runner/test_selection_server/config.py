"""Module containing the configuration for the test selection server."""

import pathlib
from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class TestSelectionConfig:
    """Configuration for the server."""

    # extra test targets that do run for every pre-merge test even if not found by impact based selection.
    pre_merge_extra_target_names: list[str]

    # extra test targets that do run for every post-merge test even if not found by impact based selection.
    post_merge_extra_target_names: list[str]


@dataclass_json
@dataclass
class Config:
    """Configuration for the server."""

    # base working directory.
    base_wd: str

    # the path to the github app. The directory should contain
    # the app id, installation id, and the private key.
    github_app_path: str

    # default repo owner.
    #
    # this isn't the only repo that the selection server
    # can operate on, but this is prepared for faster operations.
    default_repo_owner: str

    # default repo name.
    #
    # this isn't the only repo that the selection server
    # can operate on, but this is prepared for faster operations.
    default_repo_name: str

    instance_id: str
    table_name: str

    project_id: str
    topic_name: str
    subscription_name: str

    test_selection_config: TestSelectionConfig


def load_config(config_file: pathlib.Path) -> Config:
    """Loads the configuration from a file."""
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )

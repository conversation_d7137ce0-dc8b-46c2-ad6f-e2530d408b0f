# Test Selection Server

The test selection server is a GRPC server that performs the test selection for the CI system.

In particular, it calculates all impacted bazel targets based on the difference in the state of the code base between two commits.

For example, if code related to target `//a` changed (directly or indirectly via a dependency), but code related to target `//b` didn't,
the bazel difference system will return `//a`.

Internally, this used the `bazel-diff` (https://github.com/Tinder/bazel-diff) tool.
The process is to checkout the start commit, run bazel diff's "generate-hashes" command to calculate a hash file, chekcout the final commit, run bazel diff
to calculate a second hash file and then call bazel diff's "get-impacted-targets" to get a target list.

This is a GRPC server is it is an operation that can be used in different contexts, e.g. testing or deployment.
It requires git checkout and a bazel analyze phase. In contrast to the bazel runner control jobs, it doesn't require to execute bazel
actions. So, bazel control job would be a lot of effort for a 20s-1min operation. However, it has execution requirements that
other parts of the CI infrastructure do not have. We also try to minimize (or at least limit) the number of components in the CI
infrastructure that need access github authentication tokens/secrets.

See `test_selection.proto` for details.

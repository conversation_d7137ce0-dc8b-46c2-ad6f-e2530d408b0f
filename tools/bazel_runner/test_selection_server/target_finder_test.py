"""Test for the target finder."""

import git

from tools.bazel_runner.test_selection_server import target_finder


def test_target_finder(repo_dir):
    tf = target_finder.TargetFinder(
        workspace=repo_dir.parent,
        extra_startup_args=None,
    )
    hash_file = tf.generate_hash(repo_dir.name)
    assert hash_file.exists()

    assert tf.get_all_targets(repo_dir.name) == ["//:test"]
    repo_dir.joinpath("test.sh").write_text('echo "Hello, world!!"')
    repo = git.Repo(str(repo_dir))
    repo.index.add(["test.sh"])
    repo.index.commit("Update commit")

    end_hash_file = tf.generate_hash(repo_dir.name)
    assert end_hash_file.exists()

    assert "//:test" in tf.get_impacted_targets(hash_file, end_hash_file)

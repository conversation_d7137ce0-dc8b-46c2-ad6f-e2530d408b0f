load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library", "py_oci_image", "py_proto_library", "pytest_test")

proto_library(
    name = "test_selection_proto",
    srcs = ["test_selection.proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//tools/bazel_runner/git:checkout_proto",
    ],
)

py_grpc_library(
    name = "test_selection_py_proto",
    protos = [":test_selection_proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//tools/bazel_runner/git:checkout_py_proto",
    ],
)

go_grpc_library(
    name = "test_selection_go_proto",
    proto = ":test_selection_proto",
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//tools/bazel_runner/git:checkout_go_proto",
    ],
)

proto_library(
    name = "test_selection_store_proto",
    srcs = ["test_selection_store.proto"],
    deps = [
        ":test_selection_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "test_selection_store_py_proto",
    protos = [":test_selection_store_proto"],
    deps = [
        ":test_selection_py_proto",
    ],
)

py_library(
    name = "config",
    srcs = [
        "config.py",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "persistence",
    srcs = [
        "persistence.py",
    ],
    deps = [
        ":config",
        ":test_selection_py_proto",
        ":test_selection_store_py_proto",
        requirement("google-cloud-bigtable"),
        requirement("google-cloud-pubsub"),
        requirement("protobuf"),
    ],
)

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        requirement("grpcio"),
        requirement("certifi"),
        ":test_selection_py_proto",
        "//third_party/proto:googleapis_longrunning_py_proto",
    ],
)

py_library(
    name = "test_selection",
    srcs = ["test_selection.py"],
    deps = [
        ":config",
        ":target_info",
        ":test_selection_py_proto",
    ],
)

py_library(
    name = "target_finder",
    srcs = [
        "target_finder.py",
    ],
    data = [
        "@com_github_ewhauser_bazel_differ//cli:bazel-differ-linux-amd64",
    ],
    deps = [
        "//tools/bazel_lib",
        requirement("grpcio"),
        requirement("GitPython"),
    ],
)

pytest_test(
    name = "target_finder_test",
    size = "medium",
    srcs = [
        "conftest.py",
        "target_finder_test.py",
    ],
    deps = [
        ":target_finder",
        requirement("GitPython"),
    ],
)

py_library(
    name = "target_info",
    srcs = ["target_info.py"],
    deps = [
        "//tools/bazel_lib",
        requirement("defusedxml"),
    ],
)

pytest_test(
    name = "target_info_test",
    size = "medium",
    srcs = [
        "conftest.py",
        "target_info_test.py",
    ],
    deps = [
        ":target_info",
        requirement("GitPython"),
    ],
)

py_binary(
    name = "test_selection_server",
    srcs = [
        "test_selection_server.py",
    ],
    deps = [
        ":config",
        ":persistence",
        ":test_selection",
        ":test_selection_py_proto",
        "//base/logging:struct_logging",
        "//third_party/proto:googleapis_longrunning_py_proto",
        "//third_party/proto/bazel_build:bes_py_proto",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("certifi"),
    ],
)

py_oci_image(
    name = "server_image",
    package_name = package_name(),
    binary = ":test_selection_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
)

py_binary(
    name = "test_selection_processor",
    srcs = [
        "test_selection_processor.py",
    ],
    deps = [
        ":config",
        ":persistence",
        ":target_finder",
        ":target_info",
        ":test_selection",
        ":test_selection_py_proto",
        "//base/logging:struct_logging",
        "//third_party/proto:googleapis_longrunning_py_proto",
        "//third_party/proto/bazel_build:bes_py_proto",
        "//tools/bazel_runner/git:app_token",
        "//tools/bazel_runner/git:checkout",
        requirement("prometheus_client"),
        requirement("protobuf"),
        requirement("pygithub"),
    ],
)

py_oci_image(
    name = "processor_image",
    package_name = package_name(),
    base = "//tools/docker:cbazel_base_image",
    binary = ":test_selection_processor",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    data = [
        ":processor_image",
        ":server_image",
    ],
    visibility = ["//tools/bazel_runner:__subpackages__"],
    deps = [
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//tools/deploy:github_token_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "tools/bazel_runner/test_selection_server/test_selection.proto";

message TestSelectionOperation {
  string operation_id = 1;

  GetTestTargetsRequest request = 2;

  oneof response {
    TestTargetList result = 3;
    google.rpc.Status error = 4;
  }

  google.protobuf.Timestamp create_time = 5;

  google.protobuf.Timestamp update_time = 6;
}

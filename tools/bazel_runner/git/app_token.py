"""Tool to generate a GitHub installation access token."""

import datetime
import logging
import pathlib
import time

import jwt
import requests
from dateutil import parser

from tools.bazel_runner.git.token_source import TokenSource


def _generate_jwt_token(app_id, private_key):
    expiration_time = int(time.time()) + 60  # 1 minute from now

    # Create the JWT payload
    payload = {
        "iat": int(time.time()),  # Issued at time
        "exp": expiration_time,  # Expiration time
        "iss": app_id,  # GitHub App ID
    }

    jwt_token = jwt.encode(payload, private_key, algorithm="RS256")

    return jwt_token


def _parse_expires_at(expires_at_str):
    return parser.parse(expires_at_str).replace(tzinfo=datetime.timezone.utc)


class GitHubAppTokenSource(TokenSource):
    """Class to manage the GitHub token.

    Example:
        import github
        t = GitHubAppTokenSource("697167", "/home/<USER>/augment-eng.2023-12-12.private-key.pem", "45050192")
        c = github.Github(t.get_token())
        repo = c.get_repo("augmentcode/augment")
        commit = repo.get_commit("2aec2a4d2204fd893cf578f5444cae003ff71244")
        commit.create_status(state="success", context="test", description="test")
    """

    def __init__(
        self,
        app_id: str,
        private_key_path: pathlib.Path,
        installation_id: str,
    ):
        self.app_id = app_id
        self.private_key_path = private_key_path
        self.installation_id = installation_id
        logging.info(
            "GitHubToken: app_id=%s, installation_id=%s, private_key_path=%s",
            app_id,
            installation_id,
            private_key_path,
        )
        self._token_expiration = None
        self._token = None

    @classmethod
    def from_directory(cls, directory: pathlib.Path):
        return cls(
            directory.joinpath("app_id").read_text(encoding="utf-8"),
            directory.joinpath("key"),
            directory.joinpath("installation_id").read_text(encoding="utf-8"),
        )

    def _exchange_jwt_for_installation_token(
        self, jwt_token
    ) -> tuple[str, datetime.datetime]:
        # GitHub API endpoint for obtaining an installation access token
        token_url = f"https://api.github.com/app/installations/{self.installation_id}/access_tokens"

        # Set the headers for the request
        headers = {
            "Authorization": f"Bearer {jwt_token}",
            "Accept": "application/vnd.github.v3+json",
        }

        # Make a POST request to exchange the JWT for an installation access token
        response = requests.post(token_url, headers=headers, timeout=10)
        response.raise_for_status()
        data = response.json()

        # Extract the installation access token from the response
        installation_access_token = data.get("token")
        expires_at_str = data.get("expires_at")
        expires_at = _parse_expires_at(expires_at_str)

        logging.info(
            "Generated new installation access token expires at %s", expires_at_str
        )

        return installation_access_token, expires_at

    def clear_token(self):
        """Clear the token."""
        self._token = None
        self._token_expiration = None

    def get_token(self) -> str:
        """Create and return a Github installation access token.

        Returns:
            The token. The token is valid for only a limited amount and should not be stored
        """
        if (
            self._token is not None
            and self._token_expiration is not None
            # current time before the token expiration
            and datetime.datetime.now(datetime.timezone.utc) <= self._token_expiration
        ):
            # the token is not expired
            logging.info(
                "Reusing existing token with expiration %s", self._token_expiration
            )
            return self._token
        with pathlib.Path(self.private_key_path).open("rb") as key_file:
            private_key = key_file.read()

        jwt_token = _generate_jwt_token(self.app_id, private_key)
        (
            installation_access_token,
            expired_at,
        ) = self._exchange_jwt_for_installation_token(jwt_token)
        self._token = installation_access_token
        self._token_expiration = expired_at - datetime.timedelta(minutes=5)
        return installation_access_token

"""Tests for the app token generation."""

from datetime import datetime, timedelta, timezone, UTC

from tools.bazel_runner.git.app_token import _parse_expires_at


def test_timestamp():
    expires_at_str = "2024-02-05T17:37:43Z"
    expires = _parse_expires_at(expires_at_str) - timedelta(minutes=5)
    assert expires == datetime(2024, 2, 5, 17, 32, 43, tzinfo=timezone.utc)

    expired_timestamp = datetime.now(UTC)
    assert expired_timestamp > expires

    not_expired = expires - timedelta(minutes=5)
    assert not_expired <= expires

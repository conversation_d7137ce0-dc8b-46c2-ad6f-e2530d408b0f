syntax = "proto3";

// specification for a checkout
message CheckoutSpec {
  // Github organization owner the commit is in
  string owner = 1;
  // Github repository name
  string repo_name = 2;
  // either commit_checkout or pull_request_checkout are set.
  optional CommitCheckoutSpec commit_checkout = 3;
  optional PullRequestCheckoutSpec pull_request_checkout = 4;
}

// specification for a commit based checkout
message CommitCheckoutSpec {
  // (mandatory) branch name to fetch from the repository
  string branch = 1;

  // (optional) sha to checkout
  // the sha must be from the branch specified
  //
  // if not set, then use the HEAD of the branch fetched
  string ref = 2;
}

// specification for a pull request based checkout
// the pull request is fetched and merge into the HEAD
// of the base branch.
//
// Note that the organization and repo is based
// on the target of the pull request.
message PullRequestCheckoutSpec {
  uint64 pull_request_number = 1;

  // refernece (e.g. sha) to use
  // the sha should be from the pull request specified
  string ref = 2;
}

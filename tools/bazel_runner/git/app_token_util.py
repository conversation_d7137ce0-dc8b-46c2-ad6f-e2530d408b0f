"""Tool to generate a GitHub installation access token."""

import argparse
import pathlib

from github import Github

from tools.bazel_runner.git import app_token


def main():
    """Main entry function when used as a binary."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--app-id", type=str, required=True)
    parser.add_argument("--installation-id", type=str, required=True)
    parser.add_argument("--private-key-path", type=pathlib.Path, required=True)
    args = parser.parse_args()
    token_gen = app_token.GitHubAppTokenSource(
        app_id=args.app_id,
        private_key_path=args.private_key_path,
        installation_id=args.installation_id,
    )
    t = token_gen.get_token()
    c = Github(t)
    print(c.get_rate_limit())


if __name__ == "__main__":
    main()

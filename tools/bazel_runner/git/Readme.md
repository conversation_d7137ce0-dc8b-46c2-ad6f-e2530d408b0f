# Bazel Runner Git

This module contains tools to work with git with in the bazel runner (usually within a kubernetes context) tooling.

The module might not be useful to build inner-loop git tooling.

# App Token

We have a Github app called "augment-gen" (see https://github.com/organizations/augmentcode/settings/apps/augment-eng).
The Github app is added to the "augmentcode" organization.

The app has a private key that is used to generate a JWT token.
Github has a public key that is used to verify the JWT token.

The JWT token is used to generate an installation access token.
The installation access token is used to perform Github API calls.

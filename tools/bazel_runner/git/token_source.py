"""Protocol to manage the GitHub tokens."""

import typing


class TokenSource(typing.Protocol):
    """Protocol for a token source."""

    def get_token(self) -> str:
        """Returns a token."""
        raise NotImplementedError()


class FixedTokenSource(TokenSource):
    """Fixed token source."""

    def __init__(self, token: str):
        self.token = token

    def get_token(self) -> str:
        return self.token

load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_proto_library", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")

py_library(
    name = "checkout",
    srcs = [
        "checkout.py",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":checkout_py_proto",
        ":token_source",
        requirement("PyGithub"),
        requirement("GitPython"),
    ],
)

proto_library(
    name = "checkout_proto",
    srcs = ["checkout.proto"],
    visibility = ["//tools:__subpackages__"],
)

py_proto_library(
    name = "checkout_py_proto",
    protos = [":checkout_proto"],
    visibility = ["//tools:__subpackages__"],
)

go_proto_library(
    name = "checkout_go_proto",
    proto = ":checkout_proto",
    visibility = ["//tools:__subpackages__"],
)

py_library(
    name = "token_source",
    srcs = ["token_source.py"],
    visibility = ["//tools:__subpackages__"],
)

py_library(
    name = "app_token",
    srcs = ["app_token.py"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":token_source",
        requirement("cryptography"),
        requirement("pyjwt"),
        requirement("certifi"),
        requirement("requests"),
        requirement("python-dateutil"),
    ],
)

pytest_test(
    name = "app_token_test",
    srcs = [
        "app_token_test.py",
    ],
    deps = [
        ":app_token",
    ],
)

py_binary(
    name = "app_token_util",
    srcs = [
        "app_token_util.py",
    ],
    deps = [
        ":app_token",
        requirement("PyGithub"),
    ],
)

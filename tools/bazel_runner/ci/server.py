"""Part of the bazel runner service that handles the interaction with S3 and DynamoDB."""

import argparse
import concurrent.futures
import logging
import os
import pathlib
import threading

import github
import structlog
import time
from prometheus_client import Counter, Histogram, start_http_server

from base.logging.struct_logging import setup_struct_logging
from base.python.cloud.gcp import get_active_gcp_service_account
from tools.bazel_runner.ci import config, consumer, gcp
from tools.bazel_runner.ci.consumers.feature_flag_consumer import (
    FeatureFlagsSyncConsumer,
)
from tools.bazel_runner.ci.consumers.github_status_consumer import GithubStatusConsumer
from tools.bazel_runner.ci.consumers.last_known_good_consumer import (
    LastKnownGoodConsumer,
)
from tools.bazel_runner.ci.consumers.post_merge_consumer import PostMergeConsumer
from tools.bazel_runner.ci.consumers.pre_merge_consumer import PreMergeConsumer
from tools.bazel_runner.ci.consumers.slack_status_consumer import SlackStatusConsumer
from tools.bazel_runner.client import bazel_runner_client
from tools.bazel_runner.git import app_token, checkout
from tools.bot.bot_client import setup_client
from tools.eng_info import EngInfo

log = structlog.get_logger()

_event_counter = Counter(
    "ci_events",
    "Number of ci events",
    labelnames=["event", "consumer"],
)

_failure_counter = Counter(
    "github_webhook_failures",
    "Number of github webhook events",
    labelnames=["event", "consumer"],
)

# Exponentially increasing buckets with ~20% width. Min is ~10ms and max is ~20s.
_buckets = tuple((1.2**i - 1) / 25.0 for i in range(36)) + (float("inf"),)

# Track how long it takes to process github events
_github_processing_time = Histogram(
    "au_ci_github_event_processing_time_seconds",
    "Time taken to process a github event in seconds",
    labelnames=["consumer"],
    buckets=_buckets,
)

# Track how long it takes to process test result events
_result_processing_time = Histogram(
    "au_ci_result_event_processing_time_seconds",
    "Time taken to process a test result event in seconds",
    labelnames=["consumer"],
    buckets=_buckets,
)


class Server:
    """Server class."""

    def __init__(
        self,
        github_queue: gcp.GcpGithubQueue,
        result_queue: gcp.GcpResultQueue | None,
        consumers: list[consumer.Consumer],
        timeout: int = 120,
    ):
        """Constructor."""
        self.github_queue = github_queue
        self.result_queue = result_queue
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=10)
        self.consumers = consumers
        self.timeout = timeout

    def start(self):
        """Run the server."""
        github_events_thread = threading.Thread(
            target=self.listen_github_events, args=[], daemon=True
        )
        github_events_thread.start()

        run_info_events_thread = threading.Thread(
            target=self.listen_result_events, args=[], daemon=True
        )
        run_info_events_thread.start()

        github_events_thread.join()
        run_info_events_thread.join()

    def handler(self, current_consumer: consumer.Consumer, fn, event, event_name: str):
        """Handles the event."""
        start_time = time.time()
        consumer_name = current_consumer.name()

        # Select the appropriate histogram based on event type
        if event_name == "github":
            processing_time_metric = _github_processing_time
        else:  # event_name == "test"
            processing_time_metric = _result_processing_time

        try:
            with _failure_counter.labels(event_name, consumer_name).count_exceptions():
                _event_counter.labels(event_name, consumer_name).inc()
                fn(event)
        finally:
            # Record processing time
            processing_time_metric.labels(consumer=consumer_name).observe(
                time.time() - start_time
            )

    def listen_github_events(self):
        """Listens on job events in the test namespace, so that the processor can ack on them."""
        try:
            for event in self.github_queue.receive():
                logging.info("Github Event %s", event)

                f = []
                for current_consumer in self.consumers:
                    f.append(
                        self.executor.submit(
                            self.handler,
                            current_consumer,
                            current_consumer.on_github,
                            event,
                            "github",
                        )
                    )

                for future in concurrent.futures.as_completed(f, timeout=self.timeout):
                    try:
                        future.result()
                    except Exception as ex:  # pylint: disable=broad-exception-caught
                        logging.error(
                            "Error while processing github event: event=%s tx=%s",
                            event,
                            ex,
                        )
                        logging.exception(ex)
                        raise
                logging.info("Github Event processed")
        except concurrent.futures.TimeoutError as ex:
            logging.error("Timeout while processing github event: %s", ex)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("Error while listen for github events: %s", ex)
            logging.exception(ex)
            os._exit(1)

    def listen_result_events(self):
        """Listens on job events in the test namespace, so that the processor can ack on them."""
        try:
            if self.result_queue is None:
                return
            for event in self.result_queue.receive():
                logging.info("Test Run Event %s", event)

                f = []
                for current_consumer in self.consumers:
                    f.append(
                        self.executor.submit(
                            self.handler,
                            current_consumer,
                            current_consumer.on_test_event,
                            event,
                            "test",
                        )
                    )
                for future in concurrent.futures.as_completed(f, timeout=self.timeout):
                    try:
                        future.result()
                    except Exception as ex:  # pylint: disable=broad-exception-caught
                        logging.error(
                            "Error while processing test event: event=%s tx=%s",
                            event,
                            ex,
                        )
                        logging.exception(ex)
                        raise
                logging.info("Test Run Event processed")
        except concurrent.futures.TimeoutError as ex:
            logging.error("Timeout while processing test event: %s", ex)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("Error while listen for test events: %s", ex)
            logging.exception(ex)
            os._exit(1)


def main():
    """Main function."""

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--health-file",
        type=pathlib.Path,
        default=pathlib.Path("/tmp/health"),  # nosec
    )
    parser.add_argument(
        "--metrics-port",
        type=int,
        default=9090,
        help="Port for Prometheus metrics server",
    )
    args = parser.parse_args()

    setup_struct_logging()

    current_config = config.Config.load_config(args.config_file)
    logging.info("Config %s", current_config)
    logging.info("Service Account %s", get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(args.metrics_port)
    logging.info("Started Prometheus metrics server on port %d", args.metrics_port)

    token_gen = app_token.GitHubAppTokenSource.from_directory(
        pathlib.Path(current_config.github_app_path)
    )

    run_checkout = checkout.Checkout(
        base_wd=pathlib.Path(current_config.base_directory),
        github_user="app",
        token_source=token_gen,
        home_path=pathlib.Path.home(),
    )
    run_checkout.setup()

    if current_config.slack_bot_endpoint:
        bot_stub = setup_client(current_config.slack_bot_endpoint)
    else:
        bot_stub = None

    github_queue = gcp.GcpGithubQueue.create(current_config)
    if current_config.result_subscription:
        result_queue = gcp.GcpResultQueue.create(
            current_config, current_config.result_subscription
        )
    else:
        result_queue = None

    eng_info = EngInfo.load()

    if current_config.endpoint is None:
        client = None
    else:
        client = bazel_runner_client.BazelRunnerClient(
            current_config.endpoint,
            insecure=True,
        )

    consumers: list[consumer.Consumer] = []
    if current_config.last_known_good_consumer_config:
        if not client:
            raise ValueError(
                "endpoint must be configured to use last_known_good_consumer"
            )
        consumers.append(
            LastKnownGoodConsumer(
                run_checkout,
                current_config.last_known_good_consumer_config,
                slack_bot_client=bot_stub,
                test_viewer_url=current_config.test_viewer_url,
                eng_info=eng_info,
                test_client=client,
                github_client_fn=lambda: github.Github(token_gen.get_token()),
            )
        )

    if current_config.github_status_consumer_config:

        def github_client():
            return github.Github(token_gen.get_token())

        consumers.append(
            GithubStatusConsumer(
                current_config.github_status_consumer_config,
                github_client_fn=github_client,
            )
        )
    if current_config.post_merge_consumer_config:
        assert (
            client is not None
        ), "endpoint must be configured to use post_merge_consumer"

        consumers.append(
            PostMergeConsumer(
                client=client,
                run_checkout=run_checkout,
                config=current_config.post_merge_consumer_config,
                github_client_fn=lambda: github.Github(token_gen.get_token()),
            )
        )
    if current_config.feature_flags_sync_consumer_config:
        consumers.append(
            FeatureFlagsSyncConsumer(
                current_config.feature_flags_sync_consumer_config,
            )
        )
    if current_config.pre_merge_consumer_config:
        assert (
            client is not None
        ), "endpoint must be configured to use pre_merge_consumer"

        consumers.append(
            PreMergeConsumer(
                client=client,
                run_checkout=run_checkout,
                config=current_config.pre_merge_consumer_config,
            )
        )
    if current_config.slack_consumer_config:
        consumers.append(
            SlackStatusConsumer(
                current_config.slack_consumer_config,
                slack_bot_client=bot_stub,
            )
        )

    with open(args.health_file, "w") as health_file:
        health_file.write("ok")

    server = Server(
        github_queue, result_queue, consumers, timeout=current_config.consumer_timeout
    )
    server.start()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.fatal("fatal exception %s", e)
        raise

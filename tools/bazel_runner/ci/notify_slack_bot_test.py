"""Tests for the slack bot notification."""

import os
import uuid
from pathlib import Path

import git

from tools.bazel_runner.ci import notify_slack_bot


def get_simple_repo() -> tuple[git.Repo, list[git.Commit]]:
    """Create a simple git repository with three commits."""
    # make a unique temp dir
    tmp_path = Path(os.getcwd()) / uuid.uuid4().hex
    repo = git.Repo.init(tmp_path)

    # Helper function to create a commit
    def create_commit(file_name, content, message) -> git.Commit:
        file_path = tmp_path / file_name
        file_path.write_text(content)
        repo.index.add([str(file_path)])
        return repo.index.commit(message)

    # Create three commits
    commits = []
    commits.append(create_commit("file1.txt", "Hello, world!", "Initial commit"))
    commits.append(create_commit("file2.txt", "Another file!", "Second commit"))
    commits.append(create_commit("file1.txt", "Hello, Git!", "Update file1"))

    # Test: Ensure repo has three commits
    assert len(list(repo.iter_commits())) == 3
    return repo, commits


def test_get_participating_commits_same():
    repo, _commits = get_simple_repo()
    repo_name = "test_repo"
    repo_owner = "test_owner"
    new_commit = repo.head.commit
    old_commit = repo.head.commit
    participating_commits = notify_slack_bot._get_participating_commits(
        repo=repo,
        repo_name=repo_name,
        repo_owner=repo_owner,
        new_commit=new_commit,
        old_commit=old_commit,
    )
    assert len(participating_commits) == 0


def test_get_participating_commits_two():
    repo, commits = get_simple_repo()
    repo_name = "test_repo"
    repo_owner = "test_owner"
    new_commit = repo.head.commit
    old_commit = commits[1]
    participating_commits = notify_slack_bot._get_participating_commits(
        repo=repo,
        repo_name=repo_name,
        repo_owner=repo_owner,
        new_commit=new_commit,
        old_commit=old_commit,
    )
    assert len(participating_commits) == 1
    assert participating_commits[0].commit_sha == commits[2].hexsha


def test_commit_summary_to_proto():
    commit_summary = notify_slack_bot.CommitSummary(
        commit_sha="1234567890",
        commit_message="test",
        author_name="author",
        author_email="email",
        repo_owner="augmentcode",
        repo_name="augment",
    )
    proto = commit_summary.to_proto()
    assert proto.sha == "1234567890"
    assert proto.commit_message == "test"
    assert (
        proto.commit_url == "https://github.com/augmentcode/augment/commit/1234567890"
    )
    assert proto.author_name == "author"
    assert proto.author_email == "email"
    assert proto.repo_owner == "augmentcode"
    assert proto.repo_name == "augment"

"""Module containing the config for the ci server."""

import pathlib
import typing
from dataclasses import dataclass, field

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class PostMergeConsumerConfig:
    """Configuration for the postmerge consumer."""

    # branch to check for last known good commit candidates.
    #
    # usually "main"
    source_branch: str

    # the branch that is is set has a HEAD reference to last known good commit.
    # The branch is updated with the last known good commit when a postmerge test passes.
    #
    # usually 'bazel-last-known-good'
    target_branch: str

    # the context to use for the github status
    context: str

    # requestor string to use for scheduled tests.
    #
    # the requestor parameter is passed to TestRunner's ScheduleTestRequest as requestor
    requestor: str

    # only create the test request, but do not request testing
    dry_run: bool = False

    # a regex to filter pushers, usually the pattern
    # should start with "^" and end with "$"
    pusher_filter: typing.Optional[str] = None

    exclude_file_filter: list[str] = field(default_factory=list)


@dataclass_json
@dataclass
class FeatureFlagsSyncConsumerConfig:
    """Configuration for the postmerge consumer."""

    # branch to sync from.
    #
    # usually "main"
    source_branch: str

    # the endpoint to request the sync
    syncer_endpoint: typing.Optional[str] = None


@dataclass_json
@dataclass
class SlackConsumerConfig:
    """Configuration for the slack consumer."""

    # the URL prefix of the test viewer website
    test_viewer_url: str


@dataclass_json
@dataclass
class PreMergeConsumerConfig:
    """Configuration for the premerge consumer."""

    # the context to use for the github status
    context: str

    # requestor string to use for scheduled tests.
    #
    # the requestor parameter is passed to TestRunner's ScheduleTestRequest as requestor
    requestor: str

    # a regex to filter pull request authors, usually the pattern
    # should start with "^" and end with "$"
    author_filter: typing.Optional[str] = None

    # requestor string to use for scheduled tests
    requestor: str = "bazel-runner-ci"

    # only create the test request, but do not request testing
    dry_run: bool = False


@dataclass_json
@dataclass
class LastKnownGoodConsumerConfig:
    """Configuration for the last known good consumer."""

    # branch to check for last known good commit candidates
    #
    # usually "main"
    source_branch: str

    # the branch that is updated with the last known good commit
    target_branch: str

    # whether to push the last known good commit to the target branch
    # usually only false for testing
    push: bool

    # whether to notify slack about failures for commits that are older than the last known good commit
    notify_failures_older_than_last_known_good: bool

    # a regex to filter requestors fields of the test run, usually the pattern
    # should start with "^" and end with "$"
    #
    # usually filtered by 'bazel-runner-ci' to only match runs scheduled by the
    # postmerge consumer
    requestor_filter: typing.Optional[str] = None

    # whether to send a slack message when the last known good commit is updated
    slack_message: bool = True

    # whether to find the previous breakage for a failed test
    find_previous_breakage: bool = True


@dataclass_json
@dataclass
class GithubStatusConsumerConfig:
    """Configuration for the github status consumer."""

    # the URL prefix of the test viewer website
    test_viewer_url: str


@dataclass_json
@dataclass
class ResultSubscriptionConfig:
    """Configuration for the result subscription."""

    # the id of the subscription
    id: str

    # the ack deadline for the result subscription (in seconds)
    ack_deadline: int


@dataclass_json
@dataclass
class Config:
    """Configuration for the server."""

    # GCP project id
    project_id: str

    # GCP region
    region: str

    # the path to the github app. The directory should contain
    # the app id, installation id, and the private key.
    github_app_path: str

    # name of the subscription
    github_subscription: str

    # the ack deadline for the github subscription (in seconds)
    github_ack_deadline: int

    base_directory: str

    # the url to the test viewer
    test_viewer_url: str

    # timeout for the consumer to complete in seconds
    consumer_timeout: int

    # the endpoint to use for the bazel test rpc server
    endpoint: str | None

    # the slack bot endpoint to use
    slack_bot_endpoint: typing.Optional[str] = None

    last_known_good_consumer_config: typing.Optional[LastKnownGoodConsumerConfig] = None

    github_status_consumer_config: typing.Optional[GithubStatusConsumerConfig] = None

    feature_flags_sync_consumer_config: typing.Optional[
        FeatureFlagsSyncConsumerConfig
    ] = None

    post_merge_consumer_config: typing.Optional[PostMergeConsumerConfig] = None

    pre_merge_consumer_config: typing.Optional[PreMergeConsumerConfig] = None

    slack_consumer_config: typing.Optional[SlackConsumerConfig] = None

    result_subscription: typing.Optional[ResultSubscriptionConfig] = None

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image", "pytest_test")

py_library(
    name = "config",
    srcs = ["config.py"],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "notify_slack_bot",
    srcs = [
        "notify_slack_bot.py",
    ],
    deps = [
        "//tools/bot:bot_client",
        requirement("GitPython"),
    ],
)

pytest_test(
    name = "notify_slack_bot_test",
    srcs = ["notify_slack_bot_test.py"],
    deps = [
        ":notify_slack_bot",
        requirement("GitPython"),
    ],
)

py_library(
    name = "consumer",
    srcs = ["consumer.py"] + glob(
        ["consumers/*.py"],
        exclude = ["consumers/*_test.py"],
    ),
    deps = [
        requirement("GitPython"),
        ":config",
        ":notify_slack_bot",
        "//base/python/grpc:client_options",
        "//tools:eng_info",
        "//tools/bazel_runner/client",
        "//tools/bazel_runner/git:checkout",
        "//tools/bazel_runner/github_webhook:github_py_proto",
        "//tools/bazel_runner/server:test_runner_py_proto",
        "//tools/bot:bot_client",
        "//tools/feature_flags/syncer:syncer_py_proto",
    ],
)

pytest_test(
    name = "consumer_test",
    srcs = ["consumers/consumer_test.py"],
    deps = [
        ":consumer",
        requirement("GitPython"),
    ],
)

py_library(
    name = "gcp",
    srcs = ["gcp.py"],
    deps = [
        ":config",
        requirement("google-cloud-pubsub"),
        "//tools/bazel_runner/github_webhook:github_py_proto",
        "//tools/bazel_runner/server:test_runner_py_proto",
    ],
)

py_binary(
    name = "ci_server",
    srcs = [
        "server.py",
    ],
    main = "server.py",
    deps = [
        ":config",
        ":consumer",
        ":gcp",
        "//base/logging:struct_logging",
        "//base/python/cloud",
        "//base/python/cloud:gcp",
        "//tools/bazel_runner/git:app_token",
        "//tools/bazel_runner/git:checkout",
        "//tools/bazel_runner/github_webhook:github_py_proto",
        "//tools/bazel_runner/server:test_runner_py_proto",
        "//tools/bot:bot_client",
        requirement("google-cloud-pubsub"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    # we need a git executable in the base image
    base = "//tools/docker:ubuntu2004_ci_base_image",
    binary = "ci_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    trivy_allow_list = [
        "CVE-2024-34156",  # golang version in gojsonnet outdated. CVE not likely to be exploitable in ci server.
    ],
    visibility = ["//services:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
        "GCP_US_CENTRAL1_PROD",
    ],
    data = [
        ":image",
    ],
    visibility = ["//tools/bazel_runner:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:eng-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//tools/deploy:github_token_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

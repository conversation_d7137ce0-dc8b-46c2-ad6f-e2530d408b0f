"""CI Event Consumer to schedule postmerge tests run on push to main."""

import logging
import re
import typing
import fnmatch

import github
import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2

from tools.bazel_runner.ci.config import PostMergeConsumerConfig
from tools.bazel_runner.ci.consumer import Consumer
from tools.bazel_runner.client import bazel_runner_client
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.git import checkout
from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2
from tools.bazel_runner.test_selection_server import test_selection_pb2


def all_excluded_files(
    files: typing.Sequence[str], exclude_file_filter: list[str]
) -> bool:
    """Check if all files are matches by any of the exclude_file_filter patterns.

    Args:
        files: list of file names
        exclude_file_filter: list of file patterns to exclude

    Returns:
        True if any file is not excluded by any of the patterns, False otherwise.
    """
    for file_name in files:
        include = True
        for pattern in exclude_file_filter:
            if fnmatch.fnmatch(file_name, pattern):
                include = False
                break
        if include:
            return False
    return True


class PostMergeConsumer(Consumer):
    """Consumer that will schedule postmerge tests run on push to main."""

    def __init__(
        self,
        client: bazel_runner_client.BazelRunnerClient,
        run_checkout: checkout.Checkout,
        config: PostMergeConsumerConfig,
        github_client_fn: typing.Callable[[], github.Github],
    ):
        self.client = client
        self.run_checkout = run_checkout
        self.config = config
        self.github_client_fn = github_client_fn

    def name(self):
        return "post-merge"

    def _build_push_spec(self, owner: str, repo_name: str, branch: str, ref: str):
        spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
        spec.checkout.owner = owner
        spec.checkout.repo_name = repo_name
        spec.checkout.commit_checkout.branch = branch
        spec.checkout.commit_checkout.ref = ref
        return spec

    def on_github(self, event: github_pb2.GithubEvent):
        logging.info("Github Event %s in PushConsumer", event)
        if event.HasField("push"):
            logging.info("Detected push")
            push = event.push
            if not push.ref == f"refs/heads/{self.config.source_branch}":
                return
            if self.config.pusher_filter and not re.match(
                self.config.pusher_filter, push.pusher
            ):
                return
            logging.info("Detected push to %s", self.config.source_branch)
            github_client = self.github_client_fn()
            repo = github_client.get_repo(f"{push.repo_owner}/{push.repo_name}")

            commit = repo.get_commit(push.after)  # check if commit exists
            if all_excluded_files(
                [f.filename for f in commit.files], self.config.exclude_file_filter
            ):
                logging.info("No files to test, skipping")
                return

            spec = self._build_push_spec(
                push.repo_owner, push.repo_name, self.config.source_branch, push.after
            )
            notification = test_runner_pb2.NotificationSpec()
            notification.github_status.sha = push.after
            notification.github_status.context = self.config.context
            test_selection = test_runner_pb2.TestSelectionSpec()
            test_selection.policy = test_selection_pb2.TestSelectionPolicy.POST_MERGE
            test_selection.start_checkout.commit_checkout.branch = (
                self.config.target_branch
            )
            test_selection.start_checkout.repo_name = push.repo_name
            test_selection.start_checkout.owner = push.repo_owner

            logging.info("Scheduling run: %s", spec)
            if self.config.dry_run:
                logging.info("Dry run, skipping")
                return

            run_id = self.client.schedule_run(
                spec,
                requestor=self.config.requestor,
                tags=["post-merge"],
                notification=notification,
                test_selection=test_selection,
            )
            logging.info("Run %s", run_id)

    def on_test_event(self, event: test_runner_pb2.TestRunInfo):
        pass

"""CI Event Consumer to trigger the sync of dynamic feature flags."""

import logging

import grpc

from base.python.grpc import client_options
from tools.bazel_runner.ci.config import (
    FeatureFlagsSyncConsumerConfig,
)
from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2
from tools.feature_flags.syncer import syncer_pb2, syncer_pb2_grpc
from tools.bazel_runner.ci.consumer import (
    Consumer,
)


class FeatureFlagsSyncConsumer(Consumer):
    """Consumer that will sync LaunchDarkly diffs on push to main."""

    def __init__(
        self,
        config: FeatureFlagsSyncConsumerConfig,
    ):
        self.source_branch = config.source_branch
        self.syncer_stub = self._setup_stub(config.syncer_endpoint)

    def name(self):
        return "feature-flag-sync"

    def on_github(self, event: github_pb2.GithubEvent):
        logging.info("Github Event %s in PushConsumer", event)
        if event.HasField("push"):  # push vs pull_request
            logging.info("Detected push")
            push = event.push
            if not push.ref == "refs/heads/%s" % self.source_branch:
                return
            logging.info("Detected push to %s", self.source_branch)
            try:
                logging.info("Notifying flags syncer service")
                self.syncer_stub.SyncLatest(syncer_pb2.SyncRequest(), None)
                logging.info("Notified flags syncer service")
            except Exception as ex:
                logging.warning("Failed to notify flags syncer service: %s", ex)
                logging.exception(ex)

    def on_test_event(self, event: test_runner_pb2.TestRunInfo):
        pass

    def _setup_stub(self, endpoint) -> syncer_pb2_grpc.FeatureFlagsSyncerStub:
        """Setup the client stub for a edit service."""
        logging.info("Creating grpc client to %s with options %s", endpoint, [])
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
        stub = syncer_pb2_grpc.FeatureFlagsSyncerStub(channel)
        return stub

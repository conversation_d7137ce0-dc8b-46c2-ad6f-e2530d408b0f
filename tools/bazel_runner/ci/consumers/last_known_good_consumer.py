"""CI Event Consumer to advance the last known good commit."""

import logging
import re
import typing
import uuid

import git
import github
import grpc
import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2

from tools.bazel_runner.client import bazel_runner_client
import tools.bazel_runner.git.checkout_pb2 as checkout_pb2
from tools.bazel_runner.ci import notify_slack_bot
from tools.bazel_runner.ci.config import LastKnownGoodConsumerConfig
from tools.bazel_runner.ci.consumer import Consumer
from tools.bazel_runner.git import checkout
from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2
from tools.bot import bot_pb2_grpc, bot_pb2
from tools.eng_info import EngInfo


def _get_summary(message: str) -> str:
    """Get the summary of a commit message."""
    summary = message.split("\n")[0]
    return summary.strip()[:80]


def _commit_url(repo_owner: str, repo_name: str, commit: str) -> str:
    return f"""https://github.com/{repo_owner}/{repo_name}/commit/{commit}"""


def get_previous_failing_postmerge_test(
    github_client: github.Github,
    test_viewer_url: str,
    target_name: str,
    past_runs: typing.Iterable[test_runner_pb2.TestRunInfo],
) -> bot_pb2.BreakageInfo | None:
    """Get the previous failing postmerge test for the given target name.

    Args:
        target_name: The target name to check for failures.
        past_runs: The past runs to check for failures (ordered from newest to oldest as returned by GetRuns)
        github_client: The github client to use to get commit information.

    Returns:
        The previous failing postmerge test, or None if there is no previous failing postmerge test.
    """
    last_breakage: bot_pb2.BreakageInfo | None = None
    for past_run in past_runs:
        logging.info(
            "Checking past run: %s: tags: %s, state: %s",
            past_run.run_id,
            past_run.tags,
            test_runner_pb2.RunState.Name(past_run.state),
        )
        if "post-merge" not in past_run.tags:
            continue
        if past_run.state == test_runner_pb2.RUN_STATE_PASSED:
            return last_breakage
        if past_run.state != test_runner_pb2.RUN_STATE_FAILURE:
            # we ignore non-failure states
            # essentially we assume if a test is pending and it failed before that it is still failing
            continue
        for past_job_info in past_run.jobs:
            for past_test_info in past_job_info.tests:
                if past_test_info.target_name == target_name:
                    logging.info(
                        "Found previous test run at %s: %s",
                        past_run.run_id,
                        past_test_info,
                    )
                    if (
                        past_test_info.status
                        != build_event_stream_pb2.TestStatus.PASSED
                        and past_test_info.status
                        != build_event_stream_pb2.TestStatus.FLAKY
                    ):
                        last_breakage = bot_pb2.BreakageInfo()
                        last_breakage.run_id = past_run.run_id
                        last_breakage.run_url = (
                            f"{test_viewer_url}/run/{past_run.run_id}"
                        )

                        github_commit = github_client.get_repo(
                            f"{past_run.test_execution.checkout.owner}/{past_run.test_execution.checkout.repo_name}"
                        ).get_commit(
                            past_run.test_execution.checkout.commit_checkout.ref,
                        )
                        last_breakage.commit.commit_url = _commit_url(
                            past_run.test_execution.checkout.owner,
                            past_run.test_execution.checkout.repo_name,
                            past_run.test_execution.checkout.commit_checkout.ref,
                        )
                        last_breakage.commit.author_email = (
                            github_commit.commit.author.email
                        )
                        last_breakage.commit.author_name = (
                            github_commit.commit.author.name
                        )
                        last_breakage.commit.commit_message = _get_summary(
                            github_commit.commit.message
                        )
                        last_breakage.commit.repo_name = (
                            past_run.test_execution.checkout.repo_name
                        )
                        last_breakage.commit.repo_owner = (
                            past_run.test_execution.checkout.owner
                        )
                        last_breakage.commit.sha = github_commit.sha
                        logging.info(
                            "Found previous breakage: %s for target %s",
                            last_breakage,
                            target_name,
                        )
                    else:
                        # the test passes, so it is not an existing breakage
                        logging.info("Test passes, not a breakage: %s", past_run)
                        return last_breakage
    logging.info("No previous breakage found for target %s", target_name)
    return last_breakage


class LastRuns:
    """Helper class to keep track of the last runs for a given target."""

    def __init__(
        self,
        test_client: bazel_runner_client.BazelRunnerClient,
        newest_run_id: uuid.UUID,
        chunk_size: int = 10,
        max_lookup_count: int = 10,
    ):
        self.test_client = test_client
        self.chunk_size = chunk_size
        self.past_runs: list[list[test_runner_pb2.TestRunInfo]] = [
            list(
                self.test_client.get_runs(
                    newest_run_id=newest_run_id, max_count=chunk_size
                )
            )
        ]
        self.max_lookup_count = max_lookup_count
        self.lookup_count = 0

    def _get_next_chunk(self):
        """Get the next chunk of runs.

        We want the next chunk for older runs, so we use the newest_run_id
        from the last chunk.
        """
        if self.lookup_count >= self.max_lookup_count:
            self.past_runs.append([])
        else:
            self.past_runs.append(
                list(
                    self.test_client.get_runs(
                        newest_run_id=uuid.UUID(self.past_runs[-1][-1].run_id),
                        max_count=self.chunk_size,
                    )
                )
            )
            self.lookup_count += 1

    class Iterable:
        """Iterable for the last runs."""

        def __init__(self, parent: "LastRuns"):
            self.parent = parent
            self.chunk_index = 0
            self.item_index = 0

        def __iter__(self):
            return self

        def __next__(self) -> test_runner_pb2.TestRunInfo:
            current_chunk = self.parent.past_runs[self.chunk_index]
            if len(current_chunk) == 0:
                raise StopIteration
            if self.item_index >= len(current_chunk):
                self.chunk_index += 1
                self.item_index = 0
                if self.chunk_index >= len(self.parent.past_runs):
                    self.parent._get_next_chunk()
                current_chunk = self.parent.past_runs[self.chunk_index]
                if len(current_chunk) == 0:
                    raise StopIteration
            item = current_chunk[self.item_index]
            self.item_index += 1
            return item

    def iter(self) -> typing.Iterable[test_runner_pb2.TestRunInfo]:
        """Iterate over the last runs."""
        return LastRuns.Iterable(self)


class LastKnownGoodConsumer(Consumer):
    """Consumer that will advance the last known good commit.


    The consumer will see a test as a candidate for the last known good commit if:
    - the event is a postmerge test
    - the event is from augmentcode/augment
    - the event is from the source branch (usually main)
    - the event is from a requestor that matches the filter (usually 'ci')

    If the commit is newer than the last known good commit, the consumer will advance the last known good commit.
    """

    def __init__(
        self,
        run_checkout: checkout.Checkout,
        config: LastKnownGoodConsumerConfig,
        test_viewer_url: str,
        slack_bot_client: bot_pb2_grpc.DevtoolsBotStub | None,
        test_client: bazel_runner_client.BazelRunnerClient,
        github_client_fn: typing.Callable[[], github.Github],
        eng_info: EngInfo,
    ):
        self.run_checkout = run_checkout
        self.test_viewer_url = test_viewer_url
        self.config = config
        self.slack_bot_client = slack_bot_client
        self.eng_info = eng_info
        self.test_client = test_client
        self.github_client_fn = github_client_fn

    def name(self):
        return "last-known-good"

    def on_github(self, _: github_pb2.GithubEvent):
        pass

    def _is_candidate(self, event: test_runner_pb2.TestRunInfo):
        """Returns true if the event is a candidate for the last known good commit.

        The rules are:
        - the event is a postmerge test
        - the event is from augmentcode/augment
        - the event is from the source branch
        - the event is from a requestor that matches the filter (usually 'ci')
        """
        if not (
            event.test_execution.checkout.commit_checkout.branch
            == self.config.source_branch
            and event.test_execution.checkout.owner == "augmentcode"
            and event.test_execution.checkout.repo_name == "augment"
            and event.test_execution.checkout.commit_checkout.ref
        ):
            return False
        if self.config.requestor_filter and not re.match(
            self.config.requestor_filter, event.requestor
        ):
            return False
        return True

    def on_test_event(self, event: test_runner_pb2.TestRunInfo):
        """Called when a test event is received.

        If the callback throws an exception, the event will be re-queued.
        """
        try:
            if self._is_candidate(event):
                # this is a postmerge test
                if event.state == test_runner_pb2.RUN_STATE_PASSED:
                    # test is passing, advance last known good commit?
                    self._check_new_last_known_good_commit(event)
                elif (
                    event.state == test_runner_pb2.RUN_STATE_FAILURE
                    or event.state == test_runner_pb2.RUN_STATE_ERROR
                    or event.state == test_runner_pb2.RUN_STATE_ABORT
                    or event.state == test_runner_pb2.RUN_STATE_CANCEL
                ):
                    # test is failing, advance last known good commit?
                    self._process_postmerge_test_failure(event)
        except checkout.CheckoutException as ex:
            logging.error("Error while checking out: %s", ex)
            if ex.status_code == grpc.StatusCode.INVALID_ARGUMENT:
                # the commit is not on the branch, we don't need to do anything
                logging.info("Commit is not on branch %s", self.config.source_branch)
                return
            raise

    def _gather_test_info(
        self,
        event: test_runner_pb2.TestRunInfo,
    ) -> tuple[list[bot_pb2.FailedTest], list[str]]:
        """Gather the failed and flaky tests from the event and past runs.

        Args:
            event: The current event.

        Returns:
            A tuple of the failed tests and flaky tests.
        """
        past_runs = LastRuns(self.test_client, uuid.UUID(event.run_id))
        failed_targets = []
        flaky_targets = []
        github_client = self.github_client_fn()
        for job_info in event.jobs:
            for test_info in job_info.tests:
                if (
                    test_info.status != build_event_stream_pb2.TestStatus.PASSED
                    and test_info.status != build_event_stream_pb2.TestStatus.FLAKY
                ):
                    failed_test = bot_pb2.FailedTest(name=test_info.target_name)
                    try:
                        if self.config.find_previous_breakage:
                            past_breakage = get_previous_failing_postmerge_test(
                                github_client,
                                self.test_viewer_url,
                                test_info.target_name,
                                past_runs.iter(),
                            )
                        else:
                            past_breakage = None
                        if past_breakage:
                            failed_test.broken_since.CopyFrom(past_breakage)
                    except Exception:  # pylint: disable=broad-exception-caught
                        logging.exception("Failed get past test information")
                        # ignore. Let's not fail just because we can't gather past test info.
                    failed_targets.append(failed_test)
                if test_info.status == build_event_stream_pb2.TestStatus.FLAKY:
                    flaky_targets.append(test_info.target_name)
        return failed_targets, flaky_targets

    def _process_postmerge_test_failure(self, event: test_runner_pb2.TestRunInfo):
        """Called when a postmerge test fails."""
        event_checkout = event.test_execution.checkout
        logging.info(
            "Checking if %s if next last good commit",
            event_checkout.commit_checkout.ref,
        )

        owner = event.test_execution.checkout.owner
        repo_name = event.test_execution.checkout.repo_name

        # test commit
        spec = checkout_pb2.CheckoutSpec()
        spec.commit_checkout.branch = self.config.source_branch
        spec.commit_checkout.ref = event_checkout.commit_checkout.ref
        spec.owner = owner
        spec.repo_name = repo_name
        repo_dir, branch = self.run_checkout.checkout(
            spec, checkout_id=self.config.source_branch
        )
        assert branch
        repo = git.Repo(str(repo_dir))
        test_commit = repo.branches[branch].commit

        # current main branch
        spec = checkout_pb2.CheckoutSpec()
        spec.commit_checkout.branch = self.config.target_branch
        spec.owner = owner
        spec.repo_name = repo_name
        repo_dir, branch = self.run_checkout.checkout(
            spec, checkout_id=self.config.target_branch
        )
        assert branch
        last_known_good_commit = repo.branches[branch].commit

        failed_targets, flaky_targets = self._gather_test_info(
            event,
        )
        logging.info("Failed targets: %s", failed_targets)
        logging.info("Flaky targets: %s", flaky_targets)

        repo = git.Repo(str(repo_dir))  # type: ignore

        if not self.config.notify_failures_older_than_last_known_good:
            if repo.is_ancestor(test_commit, last_known_good_commit):
                logging.info(
                    "Commit %s is an ancestor of the last known good commit %s, skipping notify",
                    test_commit.hexsha,
                    last_known_good_commit.hexsha,
                )
                return

        commit_eng = self.eng_info.resolve_commit_email(test_commit.author.email)
        if not commit_eng:
            logging.info(
                "No email address for commit %s, skipping notify author",
                test_commit.hexsha,
            )
            commit_email = None
        else:
            commit_email = commit_eng.email

        url = f"{self.test_viewer_url}/run/{event.run_id}"
        if self.config.slack_message:
            notify_slack_bot.notify_postmerge_test_failed(
                slack_bot_client=self.slack_bot_client,
                repo=repo,
                repo_owner=owner,
                repo_name=repo_name,
                test_commit=test_commit,
                old_commit=last_known_good_commit,
                failed_test_targets=failed_targets,
                flaky_test_targets=flaky_targets,
                commit_email=commit_email or "",
                details_url=url,
                is_cancelled=event.state == test_runner_pb2.RUN_STATE_CANCEL,
            )

    def _check_new_last_known_good_commit(self, event: test_runner_pb2.TestRunInfo):
        """Called when a postmerge test passes."""
        event_checkout = event.test_execution.checkout
        logging.info(
            "Checking if %s if next last good commit",
            event_checkout.commit_checkout.ref,
        )

        owner = event.test_execution.checkout.owner
        repo_name = event.test_execution.checkout.repo_name

        # candidate commit
        spec = checkout_pb2.CheckoutSpec()
        spec.commit_checkout.branch = self.config.source_branch
        spec.commit_checkout.ref = event_checkout.commit_checkout.ref
        spec.owner = owner
        spec.repo_name = repo_name
        repo_dir, branch = self.run_checkout.checkout(
            spec, checkout_id=self.config.source_branch
        )

        # current last known good branch
        spec = checkout_pb2.CheckoutSpec()
        spec.commit_checkout.branch = self.config.target_branch
        spec.owner = owner
        spec.repo_name = repo_name
        repo_dir, branch = self.run_checkout.checkout(
            spec, checkout_id=self.config.target_branch
        )
        assert branch

        repo = git.Repo(str(repo_dir))  # type: ignore

        is_on_branch = repo.is_ancestor(
            repo.commit(event_checkout.commit_checkout.ref),
            repo.branches[branch].commit,
        )

        # check if the existing commit at the HEAD of the branch
        # is an ancestor of the commit that just passed
        is_successor = repo.is_ancestor(
            repo.branches[branch].commit,
            repo.commit(event_checkout.commit_checkout.ref),
        )

        if is_on_branch:
            # test executions not necessarily finish in order, so we might not
            # have the last known good commit yet.
            #
            # if the commit is already on the branch, we don't need to do anything
            logging.info(
                "Commit %s is already on branch %s: Skipping",
                event_checkout.commit_checkout.ref,
                self.config.target_branch,
            )
            return
        else:
            if not is_successor:
                logging.error(
                    "Commit %s is not a successor of the last known good commit %s",
                    event_checkout.commit_checkout.ref,
                    repo.branches[branch].commit,
                )
                raise ValueError(
                    "Commit is not a successor of the last known good commit"
                )
            assert is_successor
            self._advance_last_known_good_commit(repo, branch, event)

    def _advance_last_known_good_commit(
        self, repo, branch, event: test_runner_pb2.TestRunInfo
    ):
        """Called when a postmerge test passes to advance the last known good commit."""
        event_checkout = event.test_execution.checkout
        owner = event.test_execution.checkout.owner
        repo_name = event.test_execution.checkout.repo_name
        logging.info(
            "Commit %s is not on branch %s",
            event_checkout.commit_checkout.ref,
            self.config.target_branch,
        )
        pre_push_commit = repo.branches[branch].commit
        repo.head.reset(
            commit=event_checkout.commit_checkout.ref,
            index=True,
            working_tree=True,
        )
        logging.info(
            "Pushing commit %s to %s",
            event_checkout.commit_checkout.ref,
            self.config.target_branch,
        )

        if self.config.push:
            with self.run_checkout.prepare():
                repo.git.push(
                    "origin" if owner == "augmentcode" else owner,
                    self.config.target_branch,
                )
        logging.info("Pushed")
        notify_slack_bot.notify_last_known_good_updated(
            slack_bot_client=self.slack_bot_client,
            repo=repo,
            repo_owner=owner,
            repo_name=repo_name,
            new_commit=repo.branches[branch].commit,
            old_commit=pre_push_commit,
        )

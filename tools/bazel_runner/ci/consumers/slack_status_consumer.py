"""CI Event Consumer to send a slack message when a test finished."""

import logging

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2

from tools.bazel_runner.ci.config import SlackConsumerConfig
from tools.bazel_runner.ci.consumer import Consumer
from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2
from tools.bot import bot_pb2, bot_pb2_grpc


class SlackStatusConsumer(Consumer):
    """Consumer that will send a slack message when a test finished."""

    def __init__(
        self,
        config: SlackConsumerConfig,
        slack_bot_client: bot_pb2_grpc.DevtoolsBotStub | None,
    ):
        """Constructor."""
        self.config = config
        self.slack_bot_client = slack_bot_client

    def name(self) -> str:
        return "slack"

    def on_github(self, _: github_pb2.GithubEvent):
        pass

    def _get_status_from_event(self, event: test_runner_pb2.TestRunInfo) -> str | None:
        # see https://stackoverflow.com/questions/26294590/differences-between-github-api-commit-statuses-failure-and-error#:~:text=In%20unit%20testing%20terminology%2C%20%22failure,like%20an%20exception%20being%20thrown.
        if event.state == test_runner_pb2.RUN_STATE_PASSED:
            return "Passed"
        if event.state == test_runner_pb2.RUN_STATE_FAILURE:
            # "failure" to say "the test failed against the criteria", and
            return "Failed"
        if event.state == test_runner_pb2.RUN_STATE_ERROR:
            return "Error"
        if event.state == test_runner_pb2.RUN_STATE_ABORT:
            return "Aborted"
        if event.state == test_runner_pb2.RUN_STATE_TIMEOUT:
            return "Timeout"
        if event.state == test_runner_pb2.RUN_STATE_CANCEL:
            return "Cancelled"
        return None

    def on_test_event(self, event: test_runner_pb2.TestRunInfo):
        if not event.notification.HasField("slack"):
            return
        if not self.slack_bot_client:
            return
        logging.info("Found event with slack status")
        status = self._get_status_from_event(event)
        if not status:
            return
        assert status
        user_email = f"{event.notification.slack.user_name}@augmentcode.com"
        request = bot_pb2.NotifyAdhocTestFinishedRequest()
        request.run_id = event.run_id
        request.status = str(status)  # str() to make pylint happy

        details_url = f"{self.config.test_viewer_url}/run/{event.run_id}"
        request.details_url = details_url
        request.user_email = user_email

        failed_test_targets = []
        flaky_test_targets = []
        for job_info in event.jobs:
            for test_info in job_info.tests:
                if (
                    test_info.status != build_event_stream_pb2.TestStatus.PASSED
                    and test_info.status != build_event_stream_pb2.TestStatus.FLAKY
                ):
                    failed_test_targets.append(test_info.target_name)
                if test_info.status == build_event_stream_pb2.TestStatus.FLAKY:
                    flaky_test_targets.append(test_info.target_name)

        request.non_success_test_targets.extend(failed_test_targets)
        request.flaky_test_targets.extend(flaky_test_targets)
        self.slack_bot_client.NotifyAdhocTestFinished(request)

"""CI Event Consumer to update the github status of a commit."""

import logging
import typing

import github

from third_party.proto.bazel_build import build_event_stream_pb2
from tools.bazel_runner.ci.config import GithubStatusConsumerConfig
from tools.bazel_runner.ci.consumer import Consumer
from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2


def _should_create_status(current_status: str | None) -> bool:
    if current_status is None:
        return True
    if current_status == "failure" and current_status == "success":
        # don't overwrite a terminal state
        return False
    return True


class GithubStatusConsumer(Consumer):
    """Consumer that will update the github status of a commit."""

    def __init__(
        self,
        config: GithubStatusConsumerConfig,
        github_client_fn: typing.Callable[[], github.Github],
    ):
        """Constructor."""
        self.config = config
        self.github_client_fn = github_client_fn

    def name(self) -> str:
        return "github-status"

    def on_github(self, _: github_pb2.GithubEvent):
        pass

    def _get_status_from_event(
        self, event: test_runner_pb2.TestRunInfo
    ) -> tuple[str, str]:
        # see https://stackoverflow.com/questions/26294590/differences-between-github-api-commit-statuses-failure-and-error#:~:text=In%20unit%20testing%20terminology%2C%20%22failure,like%20an%20exception%20being%20thrown.
        if event.state == test_runner_pb2.RUN_STATE_PASSED:
            return ("success", "Passed")
        if event.state == test_runner_pb2.RUN_STATE_FAILURE:
            # "failure" to say "the test failed against the criteria", and
            failed_tests = []
            for job_info in event.jobs:
                for test_info in job_info.tests:
                    if (
                        test_info.status != build_event_stream_pb2.TestStatus.PASSED
                        and test_info.status != build_event_stream_pb2.TestStatus.FLAKY
                    ):
                        failed_tests.append(test_info.target_name)
            if not failed_tests:
                # this usually shouldn't happen, but it can e.g. on a non-further
                # specified build failure.
                return ("failure", "Test run failed")
            if len(failed_tests) == 1:
                return ("failure", f"Test Run failed: '{failed_tests[0]}' failed")
            return (
                "failure",
                f"Test Run failed: {len(failed_tests)} test targets failed",
            )
        if event.state == test_runner_pb2.RUN_STATE_ERROR:
            return ("error", "Error")
        if event.state == test_runner_pb2.RUN_STATE_ABORT:
            return ("error", "Aborted")
        if event.state == test_runner_pb2.RUN_STATE_TIMEOUT:
            return ("error", "Timeout")
        if event.state == test_runner_pb2.RUN_STATE_CANCEL:
            return ("error", "Cancelled")
        if event.state == test_runner_pb2.RUN_STATE_INIT:
            return ("pending", "Initializing")
        if event.state == test_runner_pb2.RUN_STATE_CHECKOUT:
            return ("pending", "Checking out")
        if event.state == test_runner_pb2.RUN_STATE_RUN:
            return ("pending", "Running")
        if event.state == test_runner_pb2.RUN_STATE_POSTPROCESSING:
            return ("pending", "Post processing")
        if event.state == test_runner_pb2.RUN_STATE_RUN_WAITING:
            return ("pending", "Waiting")
        return ("pending", "Pending")

    def get_current_state(self, commit, context: str):
        """Returns the current status of a commit."""
        for s in commit.get_statuses():
            if s.context == context:
                return s.state
        return None

    def on_test_event(self, event: test_runner_pb2.TestRunInfo):
        logging.info("Github Event %s in GithubStatusConsumer", event)
        if not event.notification.HasField("github_status"):
            return
        try:
            github_status_notification = event.notification.github_status
            checkout_config = event.test_execution.checkout
            owner = event.notification.github_status.owner or checkout_config.owner
            repo_name = (
                event.notification.github_status.repo_name or checkout_config.repo_name
            )
            github_client = self.github_client_fn()
            repo = github_client.get_repo(f"{owner}/{repo_name}")

            current_status = self.get_current_state(
                repo.get_commit(github_status_notification.sha),
                github_status_notification.context,
            )

            status, desc = self._get_status_from_event(event)

            if not _should_create_status(current_status):
                logging.info(
                    "Not creating status: current_status=%s, new_status=%s",
                    current_status,
                    status,
                )
                return

            commit = repo.get_commit(github_status_notification.sha)
            logging.info(
                "Found event with github status: commit=%s, status=%s",
                commit.sha,
                status,
            )
            url = f"{self.config.test_viewer_url}/run/{event.run_id}"
            commit.create_status(
                state=status,
                target_url=url,
                description=desc,
                context=github_status_notification.context,
            )
        except github.GithubException as e:
            if e.status == 404:
                # if the repo or commit does not exist, we just ignore the event
                logging.warning("Ignoring event: %s", e.message)
                return
            else:
                raise

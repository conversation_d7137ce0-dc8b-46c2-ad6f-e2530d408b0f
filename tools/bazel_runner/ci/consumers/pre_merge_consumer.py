"""CI Event Consumer to schedule premerge tests run on push to main."""

import logging
import re

import grpc
from tools.bazel_runner.ci.config import (
    PreMergeConsumerConfig,
)
from tools.bazel_runner.client import bazel_runner_client
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.git import checkout
from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2
from tools.bazel_runner.test_selection_server import test_selection_pb2
from tools.bazel_runner.ci.consumer import (
    Consumer,
)


class PreMergeConsumer(Consumer):
    """Consumer that will a test run based on pull requests."""

    def __init__(
        self,
        client: bazel_runner_client.BazelRunnerClient,
        run_checkout: checkout.Checkout,
        config: PreMergeConsumerConfig,
    ):
        self.client = client
        self.run_checkout = run_checkout
        self.config = config

    def name(self):
        return "pre-merge"

    def _build_push_spec(
        self,
        owner: str,
        repo_name: str,
        number: int,
        ref: str,
    ):
        spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
        spec.checkout.owner = owner
        spec.checkout.repo_name = repo_name
        spec.checkout.pull_request_checkout.pull_request_number = number
        spec.checkout.pull_request_checkout.ref = ref
        return spec

    def on_github(self, event: github_pb2.GithubEvent):
        logging.info("Github Event %s in PreMergeConsumer", event)
        if event.HasField("pull_request"):
            logging.info("Detected pull request")
            pr = event.pull_request
            if (
                (
                    pr.action == "opened"
                    or pr.action == "synchronize"
                    or pr.action == "reopened"
                )
                and pr.author
                and (
                    not self.config.author_filter
                    or re.match(self.config.author_filter, pr.author.login)
                )
            ):
                # the head.repo_owner might be a fork. We checkout via the pull request
                # which is in the base repo.
                spec = self._build_push_spec(
                    pr.base.repo_owner,
                    pr.base.repo_name,
                    pr.number,
                    pr.head.commit,
                )
                test_selection = test_runner_pb2.TestSelectionSpec()
                test_selection.policy = test_selection_pb2.TestSelectionPolicy.PRE_MERGE
                # don't set ref to pr.base.commit as the base.commit is rarely if ever updated
                notification = test_runner_pb2.NotificationSpec()
                notification.github_status.sha = pr.head.commit
                notification.github_status.owner = pr.base.repo_owner
                notification.github_status.repo_name = pr.base.repo_name
                notification.github_status.context = self.config.context
                logging.info(
                    "Scheduling run: spec=%s, test_selection=%s, notification=%s",
                    spec,
                    test_selection,
                    notification,
                )
                if self.config.dry_run:
                    logging.info("Dry run, skipping")
                    return
                try:
                    run_id = self.client.schedule_run(
                        spec,
                        requestor=self.config.requestor,
                        tags=["pre-merge"],
                        test_selection=test_selection,
                        notification=notification,
                        supersedes=True,
                    )
                    logging.info("Run %s", run_id)
                except grpc.RpcError as rpc_error:
                    if (
                        rpc_error.code()  # pylint: disable=no-member # type: ignore
                        == grpc.StatusCode.ALREADY_EXISTS
                    ):
                        logging.info("Matching already exists")
                    else:
                        logging.error("Error scheduling run: %s", rpc_error)
                        logging.exception(rpc_error)
                        raise

    def on_test_event(self, event: test_runner_pb2.TestRunInfo):
        pass

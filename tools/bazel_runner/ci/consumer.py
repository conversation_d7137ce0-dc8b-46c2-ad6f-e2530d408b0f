"""CI Event Consumer base class."""

import typing

from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2


class Consumer(typing.Protocol):
    """A consumer of test events."""

    def name(self):
        """Returns the name of the consumer."""
        raise NotImplementedError()

    def on_github(self, event: github_pb2.GithubEvent) -> None:
        """Called when a github event is received.

        If the callback throws an exception, the event will be re-queued.
        """
        raise NotImplementedError()

    def on_test_event(self, event: test_runner_pb2.TestRunInfo) -> None:
        """Called when a test event is received.

        If the callback throws an exception, the event will be re-queued.
        """
        raise NotImplementedError()

"""Wrapper around low-level queue handling."""

import logging
import time
import typing

import google.api_core.exceptions
from google.cloud import pubsub_v1  # type: ignore

from tools.bazel_runner.ci.config import Config, ResultSubscriptionConfig
from tools.bazel_runner.github_webhook import github_pb2
from tools.bazel_runner.server import test_runner_pb2


class GcpGithubQueue:
    """Wrapper around low-level queue handling."""

    def __init__(
        self,
        config: Config,
        subscriber: pubsub_v1.SubscriberClient,
    ):
        self.config = config
        self.subscriber = subscriber
        subscription_name = "projects/{project_id}/subscriptions/{sub}".format(
            project_id=config.project_id,
            sub=config.github_subscription,
        )
        self.subscription_name = subscription_name

    @classmethod
    def create(cls, config):
        subscriber = pubsub_v1.SubscriberClient()
        return cls(config, subscriber)

    def receive(self) -> typing.Iterable[github_pb2.GithubEvent]:
        """Listen to run notification events."""
        while True:
            try:
                request = pubsub_v1.types.PullRequest(  # type: ignore
                    subscription=self.subscription_name,
                    max_messages=1,
                )
                response = self.subscriber.pull(request=request)

                for message in response.received_messages:
                    logging.info(
                        "Received github message: %s", message.message.message_id
                    )
                    event = github_pb2.GithubEvent()
                    event.ParseFromString(message.message.data)
                    try:
                        yield event

                        logging.info(
                            "Acknowledging github message: %s",
                            message.message.message_id,
                        )
                        ack_ids = [message.ack_id]
                        if ack_ids:
                            ack_request = pubsub_v1.types.AcknowledgeRequest(  # type: ignore
                                subscription=self.subscription_name,
                                ack_ids=ack_ids,
                            )
                            self.subscriber.acknowledge(request=ack_request)
                    except Exception as ex:  # pylint: disable=broad-exception-caught
                        logging.error("Error while processing github message: %s", ex)
                        logging.exception(ex)
                        self.subscriber.modify_ack_deadline(
                            request={
                                "subscription": self.subscription_name,
                                "ack_ids": [message.ack_id],
                                "ack_deadline_seconds": 0,
                            }
                        )
            except google.api_core.exceptions.DeadlineExceeded:
                pass
            except google.api_core.exceptions.ServiceUnavailable:
                logging.info("Service Unavailable, sleeping")
                time.sleep(1)


class GcpResultQueue:
    """Wrapper around low-level queue handling."""

    def __init__(
        self,
        config: Config,
        sub_config: ResultSubscriptionConfig,
        subscriber: pubsub_v1.SubscriberClient,
    ):
        self.config = sub_config
        self.subscriber = subscriber
        subscription_name = "projects/{project_id}/subscriptions/{sub}".format(
            project_id=config.project_id,
            sub=sub_config.id,
        )
        self.subscription_name = subscription_name

    @classmethod
    def create(cls, config, sub_config):
        subscriber = pubsub_v1.SubscriberClient()
        return cls(config, sub_config, subscriber)

    def receive(self) -> typing.Iterable[test_runner_pb2.TestRunInfo]:
        """Listen to run notification events."""
        while True:
            try:
                request = pubsub_v1.types.PullRequest(  # type: ignore
                    subscription=self.subscription_name,
                    max_messages=1,
                )
                response = self.subscriber.pull(request=request)

                for message in response.received_messages:
                    logging.info(
                        "Received test run message: %s", message.message.message_id
                    )
                    event = test_runner_pb2.TestRunInfo()
                    event.ParseFromString(message.message.data)  # type: ignore
                    try:
                        yield event

                        logging.info(
                            "Acknowledging test run message: %s",
                            message.message.message_id,
                        )
                        ack_ids = [message.ack_id]
                        if ack_ids:
                            ack_request = pubsub_v1.types.AcknowledgeRequest(  # type: ignore
                                subscription=self.subscription_name,
                                ack_ids=ack_ids,
                            )
                            self.subscriber.acknowledge(request=ack_request)
                    except Exception as ex:  # pylint: disable=broad-exception-caught
                        logging.error("Error while processing test run message: %s", ex)
                        logging.exception(ex)
                        self.subscriber.modify_ack_deadline(
                            request={
                                "subscription": self.subscription_name,
                                "ack_ids": [message.ack_id],
                                "ack_deadline_seconds": 0,
                            }
                        )
            except google.api_core.exceptions.DeadlineExceeded:
                pass
            except google.api_core.exceptions.ServiceUnavailable:
                logging.info("Service Unavailable, sleeping")
                time.sleep(1)

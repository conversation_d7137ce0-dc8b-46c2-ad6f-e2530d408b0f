# CI Server

The CI server controls the lifecycle of commits and pull requests through the system,
usually by listening and reacting the review events (see //tools/bazel_runner/github_webhook)
and test events (see //tools/bazel_runner/server)

# Standard Flow

The flow is as follows:

- An engineer (or in the future a bot) is creating a new pull request
- The CI system's `PreMergeConsumer` will schedule a test run for the pull request. The test will cover all test targets that are impacted by the pull request (see //tools/bazel_runner/test_selection_server)
- The CI system's `GithubStatusConsumer` will set the status of the pull request according to the test results.
- Should the PR be merged, the CI system's `PostMergeConsumer` will react to the push to `main` and schedule a test run for the merge commit. The test will cover all test targets impacted by the changes from "bazel-last-known-good" to the merge commit.
- If the postmerge test run passes, the CI system's `LastKnownGoodConsumer` will check if the commit is a successor of the last known good commit. If so, it will update the `bazel-last-known-good` branch to the merge commit.

# Terminology

- `pusher` the term pusher is used to refer to the user that pushed the commit to the repo. This is usually the user that created the pull request. It is a github username, which might or might not be found in //deploy/common/eng.jsonnet
- - `requestor` the term requestor is used to refer to who or what scheduled the test run. If a user manually scheduled a test run through the web UI, it is the username. If the CI system scheduled the run (e.g. because a PR was created), then it is a service name (e.g. `bazel-runner-ci`)

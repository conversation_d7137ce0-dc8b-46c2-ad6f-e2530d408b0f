"""Notification for the CI runner."""

import logging
from dataclasses import dataclass
from typing import Optional, Sequence, Union

import git

from tools.bot import bot_pb2, bot_pb2_grpc


@dataclass(frozen=True)
class CommitSummary:
    """Represents a commit."""

    commit_sha: str
    commit_message: Union[str, bytes]
    author_name: Optional[str]
    author_email: Optional[str]
    repo_owner: str
    repo_name: str

    def commit_url(self) -> str:
        return f"""https://github.com/{self.repo_owner}/{self.repo_name}/commit/{self.commit_sha}"""

    def to_proto(self):
        """Convert to a proto."""
        if isinstance(self.commit_message, bytes):
            message: str = self.commit_message.decode("utf-8")
        else:
            message = str(self.commit_message)
        return bot_pb2.Commit(
            sha=self.commit_sha,
            commit_url=self.commit_url(),
            commit_message=message,
            author_name=self.author_name or "",
            author_email=self.author_email or "",
            repo_owner=self.repo_owner,
            repo_name=self.repo_name,
        )


def _notify_last_known_good_updated(
    slack_bot_client: bot_pb2_grpc.DevtoolsBotStub,
    last_known_good_commit: CommitSummary,
    included_commits: Sequence[CommitSummary],
):
    """Send a notification to the Slack bot."""
    request = bot_pb2.NotifyLastKnownGoodUpdatedRequest()
    request.last_known_good_commit.CopyFrom(last_known_good_commit.to_proto())
    request.included_commits.extend([c.to_proto() for c in included_commits])
    slack_bot_client.NotifyLastKnownGoodUpdated(request)


def _notify_postmerge_test_failed(
    slack_bot_client: bot_pb2_grpc.DevtoolsBotStub,
    test_commit: CommitSummary,
    included_commits: Sequence[CommitSummary],
    failed_test_targets: list[bot_pb2.FailedTest],
    flaky_test_targets: list[str],
    details_url: str,
    commit_email: str,
    is_cancelled: bool,
):
    """Send a notification to the Slack bot."""
    request = bot_pb2.NotifyPostMergeTestingFailedRequest()
    request.test_commit.CopyFrom(test_commit.to_proto())
    request.commit_email = test_commit.author_email or ""
    request.included_commits.extend([c.to_proto() for c in included_commits])
    request.non_success_test_targets.extend(t.name for t in failed_test_targets)
    request.failed_test_targets.extend(failed_test_targets)
    request.flaky_test_targets.extend(flaky_test_targets)
    request.details_url = details_url
    request.commit_email = commit_email
    request.is_cancelled = is_cancelled
    slack_bot_client.NotifyPostmergeTestFailed(request)


def _get_participating_commits(
    repo: git.Repo,  # pyright: ignore
    repo_name: str,
    repo_owner: str,
    new_commit: git.Commit,
    old_commit: git.Commit,
) -> Sequence[CommitSummary]:
    """Get all commits since the last known good commit."""
    logging.info(
        "Gathering all commits between %s and %s to last known good commit",
        old_commit.hexsha,
        new_commit.hexsha,
    )

    if repo.is_ancestor(old_commit, new_commit):
        commits = repo.iter_commits(
            f"{old_commit.hexsha}...{new_commit.hexsha}",
            max_count=100,
        )
    else:
        logging.info(
            "Old commit %s is not an ancestor of new commit %s, skipping.",
            old_commit.hexsha,
            new_commit.hexsha,
        )
        commits = []

    participating_commits = []
    for commit in commits:
        logging.info(
            "Adding commit %s to last known good notification.",
            commit.hexsha,
        )
        participating_commits.append(
            CommitSummary(
                commit_sha=commit.hexsha,
                commit_message=commit.summary,
                author_name=commit.author.name,
                author_email=commit.author.email,
                repo_owner=repo_owner,
                repo_name=repo_name,
            )
        )
    return participating_commits


def notify_last_known_good_updated(
    slack_bot_client: bot_pb2_grpc.DevtoolsBotStub | None,
    repo: git.Repo,  # pyright: ignore
    repo_owner: str,
    repo_name: str,
    new_commit: git.Commit,
    old_commit: git.Commit,
):
    """Notify the engineers via the Slack bot."""
    if slack_bot_client is None:
        return
    # gather all commits since the last known good commit
    participating_commits = _get_participating_commits(
        repo_owner=repo_owner,
        repo_name=repo_name,
        new_commit=new_commit,
        old_commit=old_commit,
        repo=repo,
    )

    _notify_last_known_good_updated(
        slack_bot_client=slack_bot_client,
        last_known_good_commit=CommitSummary(
            commit_sha=new_commit.hexsha,
            commit_message=new_commit.summary,
            author_name=new_commit.author.name,
            author_email=new_commit.author.email,
            repo_owner=repo_owner,
            repo_name=repo_name,
        ),
        included_commits=participating_commits,
    )


def notify_postmerge_test_failed(
    slack_bot_client: bot_pb2_grpc.DevtoolsBotStub | None,
    repo: git.Repo,  # pyright: ignore
    repo_owner: str,
    repo_name: str,
    test_commit: git.Commit,
    old_commit: git.Commit,
    failed_test_targets: list[bot_pb2.FailedTest],
    flaky_test_targets: list[str],
    details_url: str,
    commit_email: str,
    is_cancelled: bool,
):
    """Notify the engineers via the Slack bot."""
    if not slack_bot_client:
        return
    # gather all commits since the last known good commit
    participating_commits = _get_participating_commits(
        repo_owner=repo_owner,
        repo_name=repo_name,
        new_commit=test_commit,
        old_commit=old_commit,
        repo=repo,
    )

    _notify_postmerge_test_failed(
        slack_bot_client=slack_bot_client,
        test_commit=CommitSummary(
            commit_sha=test_commit.hexsha,
            commit_message=test_commit.summary,
            author_name=test_commit.author.name,
            author_email=test_commit.author.email,
            repo_owner=repo_owner,
            repo_name=repo_name,
        ),
        included_commits=participating_commits,
        failed_test_targets=failed_test_targets,
        flaky_test_targets=flaky_test_targets,
        details_url=details_url,
        commit_email=commit_email,
        is_cancelled=is_cancelled,
    )

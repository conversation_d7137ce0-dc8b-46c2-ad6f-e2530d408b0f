// K8S deployment file for the github webhook
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local engLib = import 'deploy/common/eng.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local githubAppSealedLib = import 'tools/deploy/github_app_sealed.jsonnet';
function(cloud, env, namespace, namespace_config)
  assert cloud == 'GCP_US_CENTRAL1_DEV' || cloud == 'GCP_US_CENTRAL1_PROD';
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local appName = 'ci';
  local githubSecret = githubAppSealedLib(cloud=cloud, namespace=namespace, appName=appName);
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local githubSubObjects = [
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: 'github-%s-ci-sub' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        topicRef: {
          name: 'github-%s-topic' % namespace,
        },
        ackDeadlineSeconds: 300,
        retryPolicy: {
          minimumBackoff: '30s',
          maximumBackoff: '300s',
        },
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'github-ci-sub-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: 'github-%s-ci-sub' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
        ],
      },
    },
  ];
  local bazelRunnerSubObjects = [
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: 'bazel-runner-%s-result-ci-sub' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        topicRef: {
          name: 'bazel-runner-%s-result-topic' % namespace,
        },
        ackDeadlineSeconds: 600,
        retryPolicy: {
          minimumBackoff: '5s',
          maximumBackoff: '300s',
        },
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'bazelr-runner-result-ci-sub-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: 'bazel-runner-%s-result-ci-sub' % namespace,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
        ],
      },
    },
  ];
  local config =
    local testViewerUrl = if env == 'DEV' then 'https://test-viewer.%s.%s' % [namespace, domainSuffix] else 'https://test-viewer.%s' % domainSuffix;
    local findGithubUser = function(username) local githubName = [e.github for e in engLib if e.username == username];
                                              if std.length(githubName) == 0 then username else githubName[0];
    local buildUserPattern = function(users) std.join('|', std.map(function(u) '^%s$' % u, std.map(findGithubUser, users)));
    {
      GCP_US_CENTRAL1_DEV: {
        project_id: 'system-services-dev',
        region: 'us-central1',
        github_app_path: '/github-app',
        github_subscription: 'github-%s-ci-sub' % namespace,
        github_ack_deadline: 300,
        result_subscription: {
          id: 'bazel-runner-%s-result-ci-sub' % namespace,
          ack_deadline: 300,
        },
        base_directory: '/cache',
        endpoint: 'test-runner-svc:50051',
        slack_bot_endpoint: 'slack-bot-svc:80',
        test_viewer_url: testViewerUrl,
        consumer_timeout: 600,
        last_known_good_consumer_config: {
          source_branch: 'main',
          target_branch: 'bazel-last-known-good',
          notify_failures_older_than_last_known_good: false,
          push: if env == 'DEV' then false else true,
          requestor_filter: if env == 'DEV' then buildUserPattern([std.substr(namespace, 4, std.length(namespace))]) else '^bazel-runner-ci$|^dirk$',
          slack_message: true,
          find_previous_breakage: true,
        },
        github_status_consumer_config: {
          test_viewer_url: testViewerUrl,
        },
        post_merge_consumer_config: if env == 'DEV' then {
          source_branch: 'main',
          target_branch: 'bazel-last-known-good',
          pusher_filter: buildUserPattern([std.substr(namespace, 4, std.length(namespace))]),
          context: 'bazel-runner-ci-dev',
          requestor: 'bazel-runner-ci',
          dry_run: true,
        } else {
          source_branch: 'main',
          target_branch: 'bazel-last-known-good',
          context: 'bazel-runner-ci',
          requestor: 'bazel-runner-ci',
          exclude_file_filter: ['research/**', 'experimental/**'],
        },
        pre_merge_consumer_config: if env == 'DEV' then {
          context: 'bazel-runner-ci-dev',
          author_filter: if env == 'DEV' then buildUserPattern([std.substr(namespace, 4, std.length(namespace))]) else '',
          requestor: 'bazel-runner-ci',
          dry_run: true,
        } else {
          context: 'bazel-runner-ci',
          requestor: 'bazel-runner-ci',
        },
        slack_consumer_config: {
          test_viewer_url: testViewerUrl,
        },
      },
      GCP_US_CENTRAL1_PROD: {
        project_id: 'system-services-prod',
        endpoint: null,
        region: 'us-central1',
        github_app_path: '/github-app',
        github_subscription: 'github-%s-ci-sub' % namespace,
        github_ack_deadline: 300,
        base_directory: '/cache',
        test_viewer_url: testViewerUrl,
        consumer_timeout: 300,
        feature_flags_sync_consumer_config: {
          source_branch: 'main',
          syncer_endpoint: 'flags-syncer-svc:50051',
        },
      },
    }[cloud];
  local configMap =

    {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: 'ci-config',
        namespace: namespace,
        annotations: {
          'reloader.stakater.com/match': 'true',
        },
        labels: {
          app: appName,
        },
      },
      data:

        {
          'config.json': std.manifestJson(config),
        },
    };
  local container =
    {
      name: appName,
      target: {
        name: '//tools/bazel_runner/ci:image',
        dst: 'ci-image',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'http-svc',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        {
          name: 'cache-volume',
          mountPath: '/cache',
        },
        {
          mountPath: '/github-app',
          name: 'github-app-secret',
        },
      ],
      readinessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pod =
    {
      containers: [
        container,
      ],
      affinity: affinity,
      tolerations: tolerations,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      serviceAccountName: serviceAccount.name,
      volumes: [
        {
          name: 'cache-volume',
          emptyDir: {
          },
        },
        {
          name: 'github-app-secret',
          secret: {
            secretName: githubSecret.metadata.name,  // pragma: allowlist secret
            optional: false,
          },
        },
        {
          name: 'config',
          configMap: {
            name: 'ci-config',
            items: [
              {
                key: 'config.json',
                path: 'config.json',
              },
            ],
          },
        },
      ],
    };
  local deployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else 2,
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 0,
            maxUnavailable: 1,
          },
        },
        selector: {
          matchLabels: {
            'app.kubernetes.io/name': appName,
          },
        },
        template: {
          metadata: {
            labels: {
              'app.kubernetes.io/name': appName,
            },
          },
          spec: pod,
        },
      },
    };
  lib.flatten([
    githubSecret,
    configMap,
    serviceAccount.objects,
    deployment,
    githubSubObjects,
    if cloud == 'GCP_US_CENTRAL1_DEV' then bazelRunnerSubObjects else [],
  ])

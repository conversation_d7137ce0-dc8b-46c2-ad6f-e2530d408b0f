import React, { useEffect, useState } from "react";
import { Badge, Table, TablePaginationConfig, Typography } from "antd";
import { Spin } from "antd";
import { LayoutComponent } from "../lib/layout";
import ExternalLink from "antd/es/typography/Link";
import { Link } from "react-router-dom";
import { TestRunData, getPendingRuns, getTestRun } from "../lib/run";
const { Text } = Typography;

type RunKeyInfo = {
  key: string;
  runId: string;
  dataLoading?: boolean;
  data?: TestRunData | null;
};

interface TableParams {
  pagination?: TablePaginationConfig;
}

export function SummaryComponent({ run }: { run: TestRunData }) {
  if (run.testExecution.checkout.pullRequestCheckout !== undefined) {
    return (
      <ExternalLink
        ellipsis={true}
        href={`https://github.com/${run.testExecution.checkout.owner}/${run.testExecution.checkout.repoName}/pull/${run.testExecution.checkout.pullRequestCheckout.pullRequestNumber}`}
      >
        {run.testExecution.checkout.pullRequestCheckout.pullRequestNumber}
      </ExternalLink>
    );
  } else if (run.testExecution.checkout.commitCheckout !== undefined) {
    if (run.testExecution.checkout.commitCheckout.ref !== undefined) {
      return (
        <div>
          <ExternalLink
            ellipsis={true}
            href={`https://github.com/${run.testExecution.checkout.owner}/${run.testExecution.checkout.repoName}/tree/${run.testExecution.checkout.commitCheckout.branch}`}
          >
            Branch {run.testExecution.checkout.commitCheckout.branch}
          </ExternalLink>
          &nbsp;/&nbsp;
          <ExternalLink
            ellipsis={true}
            href={`https://github.com/${run.testExecution.checkout.owner}/${run.testExecution.checkout.repoName}/commit/${run.testExecution.checkout.commitCheckout.ref}`}
          >
            Commit {run.testExecution.checkout.commitCheckout.ref}
          </ExternalLink>
        </div>
      );
    }
  }
  return <div />;
}

export function SimpleStatusComponent({ run }: { run: TestRunData }) {
  if (run.state === "RUN_STATE_INIT") {
    return <Badge status="processing" text="Created" />;
  } else if (run.state === "RUN_STATE_CHECKOUT") {
    return <Badge status="processing" text="Checking out" />;
  } else if (run.state === "RUN_STATE_RUN") {
    return (
      <Badge
        status="processing"
        text="Running"
        style={{ paddingRight: "2em" }}
      />
    );
  } else if (run.state === "RUN_STATE_RUN_WAITING") {
    return (
      <Badge
        status="processing"
        text="Waiting"
        style={{ paddingRight: "2em" }}
      />
    );
  } else if (run.state === "RUN_STATE_POSTPROCESSING") {
    return <Badge status="processing" text="Post processing" />;
  } else if (run.state === "RUN_STATE_PASSED") {
    return <Badge status="success" text="Passed" />;
  } else if (run.state === "RUN_STATE_ERROR") {
    return <Badge status="error" style={{ color: "#f5222d" }} text={"Error"} />;
  } else if (run.state === "RUN_STATE_ABORT") {
    return (
      <Badge
        status="error"
        style={{ color: "#f5222d" }}
        text={`Aborted: ${run.message}`}
      />
    );
  } else if (run.state === "RUN_STATE_FAILURE") {
    return (
      <Badge status="warning" style={{ color: "#f5222d" }} text={"Failures"} />
    );
  } else if (run.state === "RUN_STATE_CANCEL") {
    return <Badge status="warning" text="Cancelled" />;
  }
  return <div />;
}

function PendingRunsComponent() {
  const [runIdData, setRunIdData] = useState<string[] | undefined>(undefined);

  useEffect(() => {
    const fetchPendingRuns = async () => {
      const requests = await getPendingRuns();
      setRunIdData(requests);
    };
    fetchPendingRuns();
  }, []);

  return <RunsSearchComponent runIds={runIdData} />;
}

type RequestsSearchComponentProbs = {
  runIds?: string[];
};

function RunsSearchComponent({ runIds }: RequestsSearchComponentProbs) {
  const [runData, setRunData] = useState<RunKeyInfo[] | undefined>(undefined);
  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: {
      current: 1,
      pageSize: 10,
    },
  });

  if (runIds !== undefined) {
    const existingData = runData?.map((d) => d.runId);
    const newData = runIds;
    if (JSON.stringify(existingData) !== JSON.stringify(newData)) {
      const r: RunKeyInfo[] = runIds.map((r) => {
        return {
          key: r,
          runId: r,
        };
      });
      setTableParams({
        ...tableParams,
        pagination: {
          ...tableParams.pagination,
          total: runIds.length,
        },
      });
      setRunData(r);
    }
  }

  useEffect(() => {
    if (runData === undefined) {
      return;
    }
    if (tableParams.pagination === undefined) {
      return;
    }
    const pageStartIndex =
      ((tableParams.pagination!.current || 1) - 1) *
      (tableParams.pagination!.pageSize || 0);
    const pageEndIndex =
      (tableParams.pagination!.current || 0) *
      (tableParams.pagination!.pageSize || 0);
    let newRunData = runData.map((r) => r);
    const fetches = newRunData?.flatMap((r, index) => {
      if (
        r.dataLoading === undefined &&
        index >= pageStartIndex &&
        index < pageEndIndex
      ) {
        r.dataLoading = true;
        const fetchRuns = async () => {
          console.log(`run ${r.runId}`);

          let newRunData = runData.map((r) => r);
          const run = await getTestRun(r.runId);
          const index = newRunData.findIndex((r2) => r2.runId === r.runId);
          if (index >= 0) {
            newRunData[index].data = run;
            newRunData[index].dataLoading = false;
          }
          console.log(`run data ${JSON.stringify(newRunData)}`);
          setRunData(newRunData);
        };
        return [fetchRuns];
      } else {
        return [];
      }
    });
    setRunData(newRunData);
    fetches.forEach((f) => f());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableParams]);

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters: any,
    sorter: any,
  ) => {
    setTableParams({
      pagination,
    });

    // `dataSource` is useless since `pageSize` changed
    if (pagination.pageSize !== tableParams.pagination?.pageSize) {
      setRunData([]);
    }
  };

  const columns = [
    {
      title: "Run Id",
      dataIndex: "runId",
      key: "name",
      render: (runId: string) => {
        return <Link to={`/run/${runId}`}>{runId}</Link>;
      },
    },
    {
      title: "Summary",
      dataIndex: "data",
      key: "data",
      render: (data: TestRunData | undefined) => {
        if (data !== undefined && data !== null) {
          return <SummaryComponent run={data} />;
        }
      },
    },
    {
      title: "State",
      dataIndex: "data",
      key: "data",
      render: (data: TestRunData | undefined) => {
        if (data !== undefined && data !== null) {
          return <SimpleStatusComponent run={data} />;
        }
      },
    },
    {
      title: "Create Timestamp",
      dataIndex: "data",
      key: "data",
      render: (data: TestRunData | undefined) => {
        if (data !== undefined && data !== null) {
          return data.createTime;
        }
      },
    },
  ];

  let table = <div></div>;
  if (runData !== undefined) {
    table = (
      <Table
        dataSource={runData}
        columns={columns}
        onChange={handleTableChange}
      />
    );
  } else {
    table = <Spin />;
  }
  return table;
}

function PendingRunsPageComponent() {
  const items = <PendingRunsComponent />;

  return (
    <LayoutComponent
      children={items}
      selectedMenuKey={"requests"}
      breadcrumbs={[{ label: "Pending Runs", link: "/runs/pending" }]}
    />
  );
}

export default PendingRunsPageComponent;

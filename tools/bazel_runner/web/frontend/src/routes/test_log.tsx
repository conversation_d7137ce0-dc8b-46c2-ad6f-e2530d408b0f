import { use<PERSON>arams } from "react-router-dom";
import { LayoutComponent } from "../lib/layout";
import {
  BuildEventItem,
  TargetData,
  TestCase,
  TestLog,
  TestRunData,
  fillTargetDataFromJobBuildEvents,
  getBuildEvents,
  getBytestreamData,
  getTargetLogs,
  getTestCases,
  getTestRun,
  isFinalState,
} from "../lib/run";
import { useEffect, useState } from "react";
import React from "react";
import {
  Descriptions,
  Empty,
  List,
  Radio,
  Select,
  Spin,
  Table,
  Tabs,
  TabsProps,
  Typography,
  notification,
} from "antd";
import { TargetStatusComponent } from "./run";
import stripAnsi from "strip-ansi";
import { ColumnType } from "antd/es/table";

const { Text } = Typography;

function TestCaseComponent({
  runState,
  testCases,
}: {
  runState: string;
  testCases: TestCase[];
}) {
  if (!isFinalState(runState)) {
    return (
      <Empty description="Test case information not yet available. Information becomes available after run finished." />
    );
  } else {
    if (testCases.length === 0) {
      return (
        <Empty description="Test case information not available. See Console for details." />
      );
    } else {
      const columns: ColumnType<TestCase>[] = [
        {
          title: "Test Case",
          key: "name",
          render: (t: TestCase) => {
            return t.name;
          },
          sorter: (a, b) => {
            return a.name.localeCompare(b.name);
          },
        },
        {
          title: "Status",
          key: "status",
          render: (r: TargetData) => {
            return <TargetStatusComponent targetData={r} isFinalState={true} />;
          },
          filters: [
            {
              text: "Passed",
              value: "PASSED",
            },
            {
              text: "Failed",
              value: "FAILED",
            },
            {
              text: "Pending",
              value: "",
            },
          ],
          onFilter: (value: string | number | boolean, record: TestCase) =>
            record.status === (value as string),
          defaultSortOrder: "ascend",
          sorter: (a, b) => {
            let statusLeft = a.status;
            if (statusLeft.length! === 0) {
              statusLeft = "ZZZ";
            }
            let statusRight = b.status;
            if (statusRight.length === 0) {
              statusRight = "ZZZ";
            }
            return statusLeft.localeCompare(statusRight);
          },
        },
      ];
      return <Table dataSource={testCases} columns={columns} />;
    }
  }
}

function ArchiveComponent({
  runId,
  targetName,
  archiveFiles,
}: {
  runId: string;
  targetName: string;
  archiveFiles: string[][];
}) {
  // this simple approach for map targets to archive name forks for now
  const archiveNamePrefix = `${targetName.substring(2).replaceAll(":", "/")}/`;

  let filteredFiles = archiveFiles.filter((archive) =>
    archive[1].startsWith(archiveNamePrefix),
  );

  return (
    <List
      size="small"
      bordered
      dataSource={filteredFiles}
      renderItem={(item) => (
        <li>
          <a
            href={`/api/run/${runId}/${item[0]}/archive/${encodeURIComponent(
              item[1],
            )}`}
          >
            {item[1]}
          </a>
        </li>
      )}
    />
  );
}

function TestLogTextComponent({
  targetLogsData,
}: {
  targetLogsData: TestLog[] | string | undefined;
}) {
  const [selectedLog, setSelectedLog] = useState<string | undefined>(undefined);

  if (targetLogsData === undefined) {
    return <Empty description="No test log available." />;
  } else if (typeof targetLogsData === "string") {
    return (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">{targetLogsData}</pre>
      </Text>
    );
  } else if (targetLogsData.length === 0) {
    return <Empty description="No test log available." />;
  } else if (targetLogsData.length === 1) {
    return (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">
          {stripAnsi(targetLogsData[0].content)}
        </pre>
      </Text>
    );
  } else {
    const hasMultipleAttempts = targetLogsData.some(
      (log) => log.attemptIndex > 0,
    );

    const logOptions = targetLogsData.map((log, index) => ({
      label: hasMultipleAttempts
        ? `Attempt ${log.attemptIndex}, Shard ${log.shardIndex} of ${log.shardCount}`
        : `Shard ${log.shardIndex} of ${log.shardCount}`,
      value: index.toString(),
    }));

    return (
      <>
        <Select
          style={{ width: 300, marginBottom: "16px" }}
          placeholder="Select a log entry"
          value={selectedLog}
          onChange={(value) => setSelectedLog(value)}
          options={logOptions}
        />
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {selectedLog !== undefined
              ? stripAnsi(targetLogsData[parseInt(selectedLog)].content)
              : stripAnsi(targetLogsData[0].content)}
          </pre>
        </Text>
      </>
    );
  }
}

function TestLogComponent() {
  const [runData, setRunData] = useState<TestRunData | null | undefined>(
    undefined,
  );
  const [targetData, setTargetData] = useState<TargetData | null | undefined>(
    undefined,
  );
  const [testCaseData, setTestCaseData] = useState<TestCase[] | undefined>(
    undefined,
  );
  const [targetLogsData, setTargetLogsData] = useState<
    TestLog[] | string | undefined
  >(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const { runId, jobId, target }: any = useParams();

  useEffect(() => {
    const fetchRun = async () => {
      const runInfo = await getTestRun(runId);
      console.log(`run ${JSON.stringify(runInfo)}`);
      setRunData(runInfo);

      if (runInfo === null) {
        return;
      }

      const buildEvents: BuildEventItem[] = [];
      while (true) {
        let minSeq = 0;
        if (buildEvents.length > 0) {
          let lastEvent: BuildEventItem = buildEvents[buildEvents.length - 1];
          minSeq = lastEvent.sequenceNumber + 1;
        }
        let newEvents = await getBuildEvents(runId, jobId, minSeq, 1000);
        if (newEvents.buildEvents.length === 0) {
          break;
        }
        buildEvents.push(...newEvents.buildEvents);
      }

      console.log(`build events data ${JSON.stringify(buildEvents)}`);

      let testCases: TestCase[] = [];
      if (isFinalState(runInfo.state)) {
        testCases = await getTestCases(runId, jobId, target);
      }
      console.log(`test case data ${JSON.stringify(testCases)}`);

      let targetTestSummary: BuildEventItem | undefined = buildEvents.find(
        (t) =>
          t.testSummary?.targetName === target &&
          t.testSummary?.state === "SUMMARY",
      );
      console.log(`test summary data ${JSON.stringify(targetTestSummary)}`);

      const targetData: TargetData[] = [];
      fillTargetDataFromJobBuildEvents(targetData, jobId, buildEvents);

      let targetJobData: TargetData | undefined = targetData.find(
        (t) => t.targetName === target && t.status !== "",
      );
      console.log(`target data ${JSON.stringify(targetJobData)}`);
      setTargetData(targetJobData || null);

      if (targetJobData !== undefined && targetTestSummary !== undefined) {
        if (targetTestSummary.testSummary?.output !== undefined) {
          const testLog = targetTestSummary.testSummary?.output;
          console.log(`test log ${testLog}`);
          setTargetLogsData(testLog);
        } else if (isFinalState(runInfo.state)) {
          try {
            const testLogs = await getTargetLogs(runId, jobId, target);
            console.log(`test logs ${JSON.stringify(testLogs)}`);
            setTargetLogsData(testLogs);
          } catch (e) {
            setTargetLogsData(
              `Failed to load test log. Consider checking out the hash and running bazel on this test target.\n\n${e}`,
            );
          }
        } else if (targetTestSummary?.testSummary?.outputUri !== undefined) {
          console.log(
            `output uri ${targetTestSummary?.testSummary?.outputUri}`,
          );
          try {
            const testLog = await getBytestreamData(
              targetTestSummary?.testSummary?.outputUri,
            );
            setTargetLogsData(testLog);
          } catch (e) {
            setTargetLogsData(`Error streaming test log: ${e}`);
          }
        } else {
          setTargetLogsData(undefined);
        }
      }
      setTestCaseData(testCases);
    };
    const fetchRunGuard = async () => {
      try {
        setIsLoading(true);
        await fetchRun();
        setIsLoading(false);
      } catch (e) {
        console.log(`Error while loading test target information: ${e}`);

        setRunData(null);
        setIsLoading(false);
        notification.error({
          message: "Error while loading test target information",
          description: e,
          placement: "topRight",
        });
      }
    };
    fetchRunGuard();
  }, [runId, jobId, target]);

  if (isLoading) {
    return (
      <LayoutComponent
        selectedMenuKey={"runs"}
        breadcrumbs={[
          { label: "Runs", link: "/runs" },
          { label: `Run ${runId}`, link: `/run/${runId}` },
          {
            label: `Target ${target}`,
            link: `/run/${runId}/${encodeURIComponent(target)}`,
          },
        ]}
      >
        <Spin />
      </LayoutComponent>
    );
  } else if (
    runData !== null &&
    runData !== undefined &&
    targetData !== undefined &&
    targetData !== null &&
    testCaseData !== undefined
  ) {
    const console = <TestLogTextComponent targetLogsData={targetLogsData} />;

    let archiveComponent = (
      <Empty
        description="Test is not done. Archive not available yet."
        style={{ paddingTop: "5em" }}
      />
    );
    const isFinal = isFinalState(runData.state || "");
    if (isFinal) {
      let archiveData =
        runData.jobs?.flatMap(
          (j) => j.archiveFiles?.map((a) => [j.jobId, a]) || [],
        ) || [];
      archiveComponent = (
        <ArchiveComponent
          runId={runId}
          targetName={target}
          archiveFiles={archiveData}
        />
      );
    }

    const items: TabsProps["items"] = [
      {
        key: "1",
        label: `Test Cases`,
        children: (
          <TestCaseComponent
            testCases={testCaseData}
            runState={runData.state}
          />
        ),
      },
      {
        key: "2",
        label: `Console`,
        children: console,
      },
      {
        key: "3",
        label: `Archive`,
        children: archiveComponent,
      },
    ];

    const tabs = <Tabs defaultActiveKey="1" items={items} />;

    return (
      <LayoutComponent
        selectedMenuKey={"runs"}
        breadcrumbs={[
          { label: "Runs", link: "/runs" },
          { label: `Run ${runId}`, link: `/run/${runId}` },
          {
            label: `Target ${target}`,
            link: `/run/${runId}/${encodeURIComponent(target)}`,
          },
        ]}
      >
        <Descriptions title="Target Info" bordered column={2}>
          <Descriptions.Item label="Target" span={2}>
            <Text keyboard>{target}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Status" span={1}>
            {" "}
            <TargetStatusComponent
              targetData={targetData}
              isFinalState={isFinal}
            />
          </Descriptions.Item>
          <Descriptions.Item label="Duration" span={1}>
            {targetData.duration}
          </Descriptions.Item>
        </Descriptions>

        {tabs}
      </LayoutComponent>
    );
  } else {
    return <div>N/A</div>;
  }
}

export default TestLogComponent;

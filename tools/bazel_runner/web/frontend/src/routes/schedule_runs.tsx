import { Button, Form, Input, Divider, Radio, Checkbox } from "antd";
import React, { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import { useNavigate } from "react-router-dom";
import { ScheduleRunData, scheduleRun } from "../lib/run";
import { InfoCircleOutlined } from "@ant-design/icons";

type RunCommitScheduleForm = {
  owner: string;
  repo: string;
  branch: string;
  ref: string;
  targets: string;
  env: string;
  notification: boolean;
};

function RunCommitScheduleComponent() {
  const [runScheduleForm] = Form.useForm();
  const [lastTargetsValue, setLastTargetsValue] = useState(
    localStorage.getItem("lastTarget") || "",
  );

  const navigate = useNavigate();

  useEffect(() => {
    localStorage.setItem("lastTarget", lastTargetsValue);
  }, [lastTargetsValue]);

  const onRunFormFinish = (values: RunCommitScheduleForm) => {
    console.log("Success:", values);

    // store the last value of the targets to pre-fill it next time
    if (lastTargetsValue) {
      setLastTargetsValue(values.targets);
    }

    let targets = values.targets
      .split("\n")
      .filter((t: string) => t.length > 0);

    const schedule: ScheduleRunData = {
      checkout: {
        owner: values.owner,
        repoName: values.repo,
        commitCheckout: {
          branch: values.branch,
          ref: values.ref,
        },
      },
      targets: targets,
      env: values.env,
      notification: values.notification,
    };
    console.log(`${JSON.stringify(schedule)}`);
    scheduleRun(schedule).then((runId) => {
      navigate(`/run/${runId}`);
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <Form
      name="schedule"
      form={runScheduleForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ maxWidth: 600 }}
      onFinish={onRunFormFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="on"
    >
      <Form.Item
        label="Branch"
        name="branch"
        tooltip={{ title: "Name of the branch", icon: <InfoCircleOutlined /> }}
        rules={[
          { required: true, message: "Please input the name of the branch" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Commit"
        name="ref"
        tooltip={{
          title: "(Optional) Commit (sha or sha prefix)",
          icon: <InfoCircleOutlined />,
        }}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="targets"
        label="Targets"
        tooltip={{
          title: "List of Bazel test targets (one per line)",
          icon: <InfoCircleOutlined />,
        }}
        initialValue={lastTargetsValue}
        rules={[
          { required: true, message: "Please input the test targets" },
          () => ({
            validator(_, value: string) {
              let targets = value.split("\n");
              if (
                targets.find((t: string) => !t.startsWith("//")) !== undefined
              ) {
                return Promise.reject(
                  new Error("Please enter Bazel targets starting with //"),
                );
              }
              return Promise.resolve();
            },
          }),
        ]}
      >
        <Input.TextArea />
      </Form.Item>

      <Form.Item
        label="Owner"
        name="owner"
        initialValue="augmentcode"
        tooltip={{
          title: "Owner of the repository",
          icon: <InfoCircleOutlined />,
        }}
        rules={[
          { required: true, message: "Please input the repository owner" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Repository"
        name="repo"
        initialValue="augment"
        tooltip={{
          title: "Name of the repository",
          icon: <InfoCircleOutlined />,
        }}
        rules={[
          { required: true, message: "Please input the repository name" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Run Environment"
        name="env"
        initialValue="CPU"
        tooltip={{
          title: "Run Environment. Determines which tests can be executed.",
          icon: <InfoCircleOutlined />,
        }}
      >
        <Radio.Group>
          <Radio.Button value="CPU">CPU</Radio.Button>
          <Radio.Button value="SINGLE_GPU">Single GPU</Radio.Button>
          <Radio.Button value="MULTI_GPU">Multi GPU</Radio.Button>
          <Radio.Button value="LARGE_GPU">Large GPU</Radio.Button>
          <Radio.Button value="PREMIUM_CPU">Premium CPU</Radio.Button>
        </Radio.Group>
      </Form.Item>

      <Form.Item<RunCommitScheduleForm>
        label="Notification"
        name="notification"
        valuePropName="checked"
        initialValue={true}
        tooltip={{
          title: "Send a notification to Slack",
          icon: <InfoCircleOutlined />,
        }}
      >
        <Checkbox>Notify about test result via Slack message</Checkbox>
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit">
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
}

type RunPullRequestScheduleForm = {
  owner: string;
  repo: string;
  pullRequestNumber: string;
  targets: string;
  env: string;
  notification: boolean;
};

function RunPullRequestScheduleComponent() {
  const [runScheduleForm] = Form.useForm();
  const [lastTargetsValue, setLastTargetsValue] = useState(
    localStorage.getItem("lastTarget") || "",
  );
  console.log(`lastTargetsValue: ${lastTargetsValue}`);
  const navigate = useNavigate();

  useEffect(() => {
    localStorage.setItem("lastTarget", lastTargetsValue);
  }, [lastTargetsValue]);

  const onRunFormFinish = (values: RunPullRequestScheduleForm) => {
    console.log("Success:", values);

    // store the last value of the targets to pre-fill it next time
    if (values.targets) {
      setLastTargetsValue(values.targets);
    }

    let targets = (values.targets || "")
      .split("\n")
      .filter((t: string) => t.length > 0);

    const schedule: ScheduleRunData = {
      checkout: {
        owner: values.owner,
        repoName: values.repo,
        pullRequestCheckout: {
          pullRequestNumber: values.pullRequestNumber,
        },
      },
      targets: targets,
      env: values.env,
      notification: values.notification,
    };
    console.log(`${JSON.stringify(schedule)}`);
    scheduleRun(schedule).then((runId) => {
      navigate(`/run/${runId}`);
    });
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <Form
      name="schedule"
      form={runScheduleForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ maxWidth: 600 }}
      onFinish={onRunFormFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="on"
    >
      <Form.Item
        label="Pull Request"
        name="pullRequestNumber"
        tooltip={{
          title: "Number of an open pull request",
          icon: <InfoCircleOutlined />,
        }}
        rules={[
          { required: true, message: "Please input the Pull Request Id" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="targets"
        label="Targets"
        tooltip={{
          title:
            "List of Bazel test targets (one per line). If empty, all impacted targets will be executed.",
          icon: <InfoCircleOutlined />,
        }}
        initialValue={lastTargetsValue}
        rules={[
          {
            message: "Please input the test targets.",
          },
          () => ({
            validator(_, value: string | undefined) {
              if (value === undefined) {
                // no targets specified => use all impacted targets
                return Promise.resolve();
              }
              let targets = value
                .split("\n")
                .filter((t: string) => t.length > 0);
              if (targets.length === 0) {
                // no targets specified => use all impacted targets
                return Promise.resolve();
              }
              if (
                targets.find((t: string) => !t.startsWith("//")) !== undefined
              ) {
                return Promise.reject(
                  new Error("Please enter Bazel targets starting with //"),
                );
              }
              return Promise.resolve();
            },
          }),
        ]}
      >
        <Input.TextArea />
      </Form.Item>

      <Form.Item
        label="Owner"
        name="owner"
        initialValue="augmentcode"
        tooltip={{
          title: "Owner of the repository",
          icon: <InfoCircleOutlined />,
        }}
        rules={[
          { required: true, message: "Please input the repository owner" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Repository"
        name="repo"
        initialValue="augment"
        tooltip={{
          title: "Name of the repository",
          icon: <InfoCircleOutlined />,
        }}
        rules={[
          { required: true, message: "Please input the repository name" },
        ]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        label="Run Environment"
        name="env"
        initialValue="CPU"
        tooltip={{
          title: "Run Environment. Determines which tests can be executed.",
          icon: <InfoCircleOutlined />,
        }}
      >
        <Radio.Group>
          <Radio.Button value="CPU">CPU</Radio.Button>
          <Radio.Button value="SINGLE_GPU">Single GPU</Radio.Button>
          <Radio.Button value="MULTI_GPU">Multi GPU</Radio.Button>
          <Radio.Button value="LARGE_GPU">Large GPU</Radio.Button>
          <Radio.Button value="PREMIUM_CPU">Premium CPU</Radio.Button>
        </Radio.Group>
      </Form.Item>

      <Form.Item<RunCommitScheduleForm>
        label="Notification"
        name="notification"
        valuePropName="checked"
        initialValue={true}
        tooltip={{
          title: "Send a notification to Slack",
          icon: <InfoCircleOutlined />,
        }}
      >
        <Checkbox>Notify about test result via Slack message</Checkbox>
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit">
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
}

function RunScheduleComponent() {
  const items = (
    <div>
      <Divider orientation="left" plain>
        Schedule By Pull Request
      </Divider>
      <RunPullRequestScheduleComponent />
      <Divider orientation="left" plain>
        Schedule By Branch/Commit
      </Divider>
      <RunCommitScheduleComponent />
    </div>
  );

  return items;
}

function RunsSchedulePageComponent() {
  const items = <RunScheduleComponent />;

  return (
    <LayoutComponent
      children={items}
      selectedMenuKey={"runs"}
      breadcrumbs={[{ label: "Schedule Runs", link: "/runs/schedule" }]}
    />
  );
}

export default RunsSchedulePageComponent;

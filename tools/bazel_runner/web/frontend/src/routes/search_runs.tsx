import { Button, Form, Input, Divider, TabsProps, Tabs } from "antd";
import { LayoutComponent } from "../lib/layout";
import { useNavigate } from "react-router-dom";

type RunIdSearchForm = {
  runId: string;
};

function RunIdSearchComponent() {
  const [runIdForm] = Form.useForm();
  const navigate = useNavigate();

  const onRunIdFormFinish = (values: RunIdSearchForm) => {
    console.log("Success:", values);

    navigate(`/run/${values.runId}`);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log("Failed:", errorInfo);
  };

  return (
    <Form
      name="run_id"
      form={runIdForm}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
      style={{ maxWidth: 600 }}
      initialValues={{ remember: true }}
      onFinish={onRunIdFormFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
    >
      <Form.Item
        label="Run Id"
        name="runId"
        rules={[{ required: true, message: "Please input the Run Id" }]}
      >
        <Input />
      </Form.Item>

      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit">
          Submit
        </Button>
      </Form.Item>
    </Form>
  );
}

function RunsSearchPageComponent() {
  const items = <RunIdSearchComponent />;

  return (
    <LayoutComponent
      children={items}
      selectedMenuKey={"runs"}
      breadcrumbs={[{ label: "Search Runs", link: "/runs/search" }]}
    />
  );
}

export default RunsSearchPageComponent;

import axios from "axios";
import { AxiosError } from "axios";

export type TargetData = {
  targetName: string;
  status: string;
  jobId: string;
  duration?: string;
  cached: boolean;
};

const statusToPrio = {
  // pending
  "": 5,
  NO_STATUS: 5,
  // PASSES is one of the least interesting status to display
  PASSED: 6,
  FLAKY: 4,
  TIMEOUT: 2,
  FAILED: 1,
  INCOMPLETE: 3,
  // should never occur in our setup
  REMOTE_FAILURE: 0,
  FAILED_TO_BUILD: 0,
  // should never occur in our setup
  TOOL_HALTED_BEFORE_TESTING: 0,
};

export function compareTargetDataByStatus(a: TargetData, b: TargetData) {
  return statusToPrio[a.status] - statusToPrio[b.status];
}

export type JobInfoData = {
  jobId: string;
  archiveFiles?: string[];
  tests?: TargetData[];
};

export type CommitCheckoutData = {
  branch: String;
  ref?: String;
};

export type PullRequestCheckoutData = {
  pullRequestNumber: string;
};

export type CheckoutData = {
  owner: string;
  repoName: string;
  commitCheckout?: CommitCheckoutData;
  pullRequestCheckout?: PullRequestCheckoutData;
};

export type ExecutionData = {
  checkout: CheckoutData;
};

export type TestSelection = {
  start_checkout?: CheckoutData;
  end_checkout: CheckoutData;
  policy: string;
};

export type TestRunData = {
  runId: string;
  jobs?: JobInfoData[];
  testExecution: ExecutionData;
  requestor?: string;
  state: string;
  createTime: string;
  lastStateChangeTime: string;
  message?: string;
  tags?: string[];
  cancellationRequested: boolean;
  supersedes: boolean;
  testSelection?: TestSelection;
};

export function isFinalState(state: string): boolean {
  return (
    [
      "RUN_STATE_ERROR",
      "RUN_STATE_ABORT",
      "RUN_STATE_FAILURE",
      "RUN_STATE_PASSED",
      "RUN_STATE_CANCEL",
    ].indexOf(state) >= 0
  );
}

// returns detailed information about the given test run
export async function getTestRun(runId: string): Promise<TestRunData | null> {
  try {
    const { data: response }: { data: any } = await axios.get(
      `/api/run/${runId}`,
    );
    return response;
  } catch (e) {
    if (axios.isAxiosError(e)) {
      const ae = e as AxiosError;
      if (ae.response?.status === 404) {
        return null;
      } else {
        throw e;
      }
    } else {
      throw e;
    }
  }
}

export async function getConsoleLog(
  runId: string,
  jobId: string,
): Promise<string> {
  const { data: response }: { data: any } = await axios.get(
    `/api/run/${runId}/${jobId}/console`,
  );
  return response;
}

export type ScheduleRunData = {
  checkout: CheckoutData;
  targets: string[];
  env: string;
  notification: boolean;
};

export async function scheduleRun(
  scheduleRunData: ScheduleRunData,
): Promise<string> {
  const { data: response }: { data: any } = await axios.post(
    `/api/runs`,
    scheduleRunData,
  );
  return response["runId"];
}

// restart a given run
//
// returns the new run id
export async function restartRun(runId: string): Promise<string> {
  const { data: response }: { data: any } = await axios.post(
    `/api/run/${runId}/restart`,
  );
  return response["runId"];
}

// cancel a given run
export async function cancelRun(runId: string): Promise<void> {
  await axios.post(`/api/run/${runId}/cancel`);
}

export type TestLog = {
  shardIndex: number;
  shardCount: number;
  attemptIndex: number;
  content: string;
};

// get the logs for a given target
export async function getTargetLogs(
  runId: string,
  jobId: string,
  targetName: string,
): Promise<TestLog[]> {
  const { data: response }: { data: any } = await axios.get(
    `/api/run/${runId}/${jobId}/logs`,
    {
      params: {
        target_name: targetName,
      },
    },
  );
  return response;
}

export async function getBytestreamData(bytestream_uri): Promise<string> {
  const { data: response }: { data: any } = await axios.get(
    `/api/bytestream/${encodeURIComponent(bytestream_uri)}`,
  );
  return response;
}

export type TestSummary = {
  targetName: string;
  state: string;
  overallStatus?: string;
  aborted?: boolean;
  outputUri?: string;
  output?: string;
  duration?: string;
  testSize?: string;
  tags?: string[];
  cached: boolean;
};

export type Progress = {
  stdout: string;
  stderr: string;
};

export type BuildEvent = {
  progress?: Progress;
};

export type BuildEventItem = {
  sequenceNumber: number;
  testSummary?: TestSummary;
  buildEvent?: BuildEvent;
};

export type BuildEventResult = {
  runId: string;
  jobId: string;
  buildEvents: BuildEventItem[];
};

export async function getBuildEvents(
  runId: string,
  jobId: string,
  minSequenceNumber: number,
  limit: number,
): Promise<BuildEventResult> {
  const { data: response }: { data: BuildEventItem[] } = await axios.get(
    `/api/run/${runId}/${jobId}/build_events`,
    {
      params: {
        min_sequence_number: minSequenceNumber,
        limit: limit,
      },
    },
  );
  return {
    runId: runId,
    jobId: jobId,
    buildEvents: response,
  };
}

export function fillTargetDataFromJobBuildEvents(
  targetData: TargetData[],
  jobId: string,
  buildEvents: BuildEventItem[],
): TargetData[] {
  for (const buildEvent of buildEvents) {
    if (buildEvent.testSummary?.targetName !== "") {
      const ts = buildEvent.testSummary!;
      const entry = targetData.find((t) => t.targetName === ts.targetName);
      if (entry === undefined) {
        targetData.push({
          targetName: ts.targetName,
          status: ts.overallStatus || "",
          jobId: jobId,
          duration: ts.duration,
          cached: ts.cached,
        });
      } else {
        entry.status = ts.overallStatus || "";
        entry.duration = ts.duration;
      }
    }
  }
  return targetData;
}

export type TestCase = {
  // name of the test suite
  // the exact value is defined by the test rule, e.g.
  // in pytest it is derived from the name of the file.
  suite_name?: string;
  name: string;
  duration: string;
  status: string;
  message?: string;
};

export async function getTestCases(
  runId: string,
  jobId: string,
  targetName: string,
): Promise<TestCase[]> {
  const { data: response }: { data: TestCase[] } = await axios.get(
    `/api/run/${runId}/${jobId}/test_cases`,
    {
      params: {
        target_name: targetName,
      },
    },
  );
  return response;
}

export async function getPendingRuns(): Promise<string[]> {
  const { data: response }: { data: string[] } =
    await axios.get(`/api/runs/pending`);
  return response;
}

export async function getRuns(
  oldestRunId: string | undefined,
  newestRunId: string | undefined,
  maxCount: number | undefined,
): Promise<TestRunData[]> {
  const { data: response }: { data: any } = await axios.get(`/api/runs`, {
    params: {
      oldest_run_id: oldestRunId,
      newest_run_id: newestRunId,
      max_count: maxCount,
    },
  });
  return response.runs;
}

load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//tools/bazel_runner/web/frontend:vite/package_json.bzl", vite_bin = "bin")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

COMMON = [
    ":index.html",
    "//tools/bazel_runner/web/frontend/src",
    "//tools/bazel_runner/web/frontend/public",
    ":package.json",
    ":vite.config.js",
    ":node_modules/eslint-config-react-app",
    ":node_modules/react-dom",
    ":node_modules/@vitejs/plugin-react",
    ":node_modules/vite",
    ":node_modules/react",
    ":node_modules/typescript",
    ":node_modules/axios",
    ":node_modules/antd",
    ":node_modules/strip-ansi",
]

ALL_MODULES = [
    ":node_modules",
]

vite_bin.vite(
    name = "frontend",
    srcs = COMMON,
    args = ["build"],
    chdir = package_name(),
    out_dirs = ["dist"],
    visibility = ["//tools/bazel_runner/web:__subpackages__"],
)

vite_bin.vite_binary(
    name = "start",
    chdir = package_name(),
    data = ALL_MODULES + COMMON,
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//tools/bazel_runner/web:__subpackages__"],
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
    visibility = ["//tools/bazel_runner/web:__subpackages__"],
)

js_library(
    name = "jest_config",
    srcs = ["jest.config.js"],
    visibility = ["//tools/bazel_runner/web:__subpackages__"],
)

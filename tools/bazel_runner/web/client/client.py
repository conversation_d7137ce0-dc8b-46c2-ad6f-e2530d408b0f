import requests
from google.auth.transport.requests import Request
from google.oauth2 import id_token
from google.protobuf import json_format

from tools.bazel_runner.server import test_runner_pb2

# the default endpoint
DEFAULT_ENDPOINT = "https://test-viewer.us-central1.dev.augmentcode.com"

# Client ID of the test viewer.
#
# Retrieved by opening the DEFAULT_ENDPOINT in a browser.
DEFAULT_IAP_CLIENT_ID = (
    "835723878709-t8g8tp17dnacg7cb8fbefg22guk6qkpd.apps.googleusercontent.com"
)


class IapConfig:
    def __init__(self, audience: str | None = DEFAULT_IAP_CLIENT_ID):
        """IAP configuration.

        Args:
            audience: The IAP audience, i.e. the OAuth client ID of the service contacted
        """
        self.audience = audience


class BazelRunnerClient:
    """Client to start test selection and wait for the results."""

    def __init__(
        self, iap_config: IapConfig | None = None, endpoint: str = DEFAULT_ENDPOINT
    ):
        self.iap_config = iap_config
        self.endpoint = endpoint

    def _get_token(self):
        if self.iap_config is None:
            return None
        id_token_creds = id_token.fetch_id_token_credentials(self.iap_config.audience)
        id_token_creds.refresh(Request())
        return id_token_creds.token

    def _post(self, path: str, json, timeout: int = 60) -> requests.Response:
        url = f"{self.endpoint}/{path}"
        headers = {}
        t = self._get_token()
        if t is not None:
            headers["Authorization"] = f"Bearer {t}"
        return requests.post(url, json=json, headers=headers, timeout=timeout)

    def _get(self, path: str, timeout: int = 60) -> requests.Response:
        url = f"{self.endpoint}/{path}"
        headers = {}
        t = self._get_token()
        if t is not None:
            headers["Authorization"] = f"Bearer {t}"
        return requests.get(url, headers=headers, timeout=timeout)

    def schedule(
        self, request: test_runner_pb2.ScheduleTestRequest
    ) -> test_runner_pb2.ScheduleTestResponse:
        data = json_format.MessageToDict(request)
        response = self._post("api/runs", json={"request": data})
        response.raise_for_status()
        return json_format.Parse(response.text, test_runner_pb2.ScheduleTestResponse())

    def get_run(self, run_id: str) -> test_runner_pb2.GetTestInfoResponse:
        response = self._get(f"api/run/{run_id}")
        response.raise_for_status()
        return json_format.Parse(response.text, test_runner_pb2.GetTestInfoResponse())

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local serviceAccount = gcpLib.createServiceAccount(
    app='test-viewer-web-client-test',
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=true,
    iap=true,
    overridePrefix='test-we-test'
  );
  local grant = gcpLib.grantAccess(
    name='test-viewer-web-client-test-grant',
    env=env,
    namespace=namespace,
    appName='test-viewer-web-client-test',
    resourceRef={
      kind: 'Project',
      external: cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        // IAP access
        role: 'roles/iap.httpsResourceAccessor',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
  );
  local container =
    {
      name: 'test-viewer-web-client-test',
      target: {
        name: '//tools/bazel_runner/web/client/test:image',
        dst: 'test-viewer-web-client-test',
      },
    };
  local pod = {
    apiVersion: 'v1',
    kind: 'Pod',
    metadata: {
      name: 'test-viewer-web-client-test',
      namespace: namespace,
      labels: {
        app: 'test-viewer-web-client-test',
      },
    },
    spec:
      {
        serviceAccountName: serviceAccount.name,
        containers: [
          container,
        ],
      },
  };
  lib.flatten(
    [
      serviceAccount.objects,
      grant,
      pod,
    ]
  )

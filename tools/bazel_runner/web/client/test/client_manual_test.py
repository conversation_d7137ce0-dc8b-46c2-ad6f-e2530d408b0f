import argparse
from tools.bazel_runner.web.client.client import (
    <PERSON>zelRunnerClient,
    IapConfig,
    DEFAULT_ENDPOINT,
    DEFAULT_IAP_CLIENT_ID,
)
from tools.bazel_runner.server import test_runner_pb2


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--endpoint", default=DEFAULT_ENDPOINT)
    parser.add_argument("--iap-client-id", default=DEFAULT_IAP_CLIENT_ID)
    args = parser.parse_args()
    client = BazelRunnerClient(IapConfig(args.iap_client_id), args.endpoint)

    request = test_runner_pb2.ScheduleTestRequest()
    request.requestor = "auggie"
    request.test_execution.checkout.owner = "augmentcode"
    request.test_execution.checkout.repo_name = "augment"
    request.test_execution.checkout.commit_checkout.branch = "main"
    r = request.test_execution.runs.add()
    r.command = "test"
    r.targets.append("//tools/monitoring:kubecfg_grafana_test")
    response = client.schedule(request)
    run_id = response.run_id
    print(run_id)
    r = client.get_run(run_id)
    print(r)


if __name__ == "__main__":
    main()

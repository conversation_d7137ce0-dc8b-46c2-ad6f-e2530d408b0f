load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")

py_binary(
    name = "client_manual_test",
    srcs = ["client_manual_test.py"],
    deps = [
        "//tools/bazel_runner/web/client",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":client_manual_test",
    visibility = ["//tools/bazel_runner:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    data = [
        ":image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

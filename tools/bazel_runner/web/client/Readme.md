# IAP-driven Bazel Runner Client

This is a client interfaces that uses the HTTP+JSON interface of the test runner (e.g. at https://test-viewer.us-central1.dev.augmentcode.com)
to schedule tests.

It uses IAP to authenticate and is intended for service-to-service communication from other Augment clusters.
The main use case for the CD system to schedule additional tests.

## Usage

```
    client = BazelRunnerClient(IapConfig(args.iap_client_id), args.endpoint)

    request = test_runner_pb2.ScheduleTestRequest()
    request.requestor = "dirk"
    request.test_execution.checkout.owner = "augmentcode"
    request.test_execution.checkout.repo_name = "augment"
    request.test_execution.checkout.commit_checkout.branch = "main"
    r = request.test_execution.runs.add()
    r.command = "test"
    r.targets.append("//tools/monitoring:kubecfg_grafana_test")
    response = client.schedule(request)
    run_id = response.run_id
    print(run_id)
    r = client.get_run(run_id)
```

Add the following dependency to the `BUILD` file:

```
py_binary(
    name = "client_manual_test",
    srcs = ["client_manual_test.py"],
    deps = [
        "//tools/bazel_runner/web/client",
    ],
)
```

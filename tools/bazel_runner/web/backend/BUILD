load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")
load("@python_pip//:requirements.bzl", "requirement")

py_binary(
    name = "backend",
    srcs = glob(["*.py"]),
    data = ["//tools/bazel_runner/web/frontend"],
    main = "app.py",
    deps = [
        requirement("gunicorn"),
        requirement("prometheus_flask_exporter"),
        requirement("flask"),
        requirement("structlog"),
        requirement("protobuf"),
        requirement("python-dateutil"),
        "//base/flask_util:flask_util_py",
        "//base/logging:struct_logging",
        "//base/python/grpc:client_options",
        "//third_party/proto/bazel_build:bytestream_py_proto",
        "//tools/bazel_runner/client",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":backend",
    visibility = ["//tools/bazel_runner:__subpackages__"],
)

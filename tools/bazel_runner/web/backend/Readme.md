# Test Viewer UI Backend

This directory contains the backend code for the Test Viewer UI, which is part of the Bazel Runner web interface.

## Overview

The Test Viewer UI Backend is responsible for handling server-side operations related to displaying and managing test results from Bazel builds. It provides the necessary APIs and data processing to support the frontend of the Test Viewer.

## Features

- Retrieves and processes test results from Bazel builds
- Provides APIs for the frontend to query test data
- Handles data persistence and caching of test results
- Supports filtering and sorting of test results

It exposes a HTTP-JSON interface to the React frontend (//tools/bazel_runner/web/frontend).
The API can also be used by other services as long as they are authenticated via Identity-Aware Proxy (IAP).
For callers in the same GKE project, it is recommended to use the gRPC interface (//tools/bazel_runner/server).

The HTTP-JSON interface is used to schedule and query test runs.

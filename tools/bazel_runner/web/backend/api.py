"""Blueprint for the bazel runner backend API."""

# pylint: disable=no-member
from functools import wraps
import json
import logging
import mimetypes
import uuid
from typing import Any, Mapping, Optional
from urllib.parse import urlparse

import google.protobuf.json_format as json_format
from base.flask_util import iap_util
import grpc
import structlog
from flask import Blueprint, Response, current_app, jsonify, make_response, request

from base.python.grpc import client_options
import third_party.proto.bazel_build.bytestream_pb2 as bytestream_pb2
import third_party.proto.bazel_build.bytestream_pb2_grpc as bytestream_pb2_grpc
import tools.bazel_runner.server.test_runner_pb2 as test_runner_pb2
import tools.bazel_runner.server.test_runner_pb2_grpc as test_runner_pb2_grpc
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.test_selection_server import test_selection_pb2

bp = Blueprint("api", __name__, url_prefix="/api")

log = structlog.get_logger()


def iap_jwt_check(original_function):
    """Decorator that checks if the IAP JWT headers are set and that the email is valid.

    It doesn't actually verify the JWT.
    """

    @wraps(original_function)
    def wrapper(*args, **kwargs):
        assertion = request.headers.get("X-Goog-IAP-JWT-Assertion")
        if not assertion:
            logging.info("No JWT assertion found - sending 401")
            return Response(
                json.dumps({"code": 401}),  # type: ignore
                status=401,
                mimetype="application/json",
            )
        auth = False
        gcp_email = request.headers.get("X-Goog-Authenticated-User-Email")
        if gcp_email:
            if gcp_email.endswith("@augmentcode.com") and gcp_email.startswith(
                "accounts.google.com:"
            ):
                auth = True
            elif gcp_email in current_app.config["IAP_SERVICE_ACCOUNT_ALLOWED"]:
                logging.info("Allowing service account %s", gcp_email)
                auth = True
        if not auth:
            logging.warning("Invalid user email '%s' - sending 401", gcp_email)
            return Response(
                json.dumps({"code": 401}),  # type: ignore
                status=401,
                mimetype="application/json",
            )
        return original_function(*args, **kwargs)

    return wrapper


@bp.route("runs", methods=["POST"])
@iap_jwt_check
def run_schedule_handler():
    data: Optional[Mapping[str, Any]] = request.get_json()
    assert data

    logging.info("schedule request for %s", data)

    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        schedule_request = test_runner_pb2.ScheduleTestRequest()
        json_request = data.get("request", None)
        if json_request:
            schedule_request.MergeFrom(
                json_format.Parse(json.dumps(json_request), schedule_request)
            )
        else:
            requestor = iap_util.extract_user(request)
            if requestor is None:
                raise ValueError("requestor is not set")
            schedule_request.requestor = requestor
            schedule_request.tags.append("manual")
            schedule_request.test_execution.checkout.owner = data["checkout"]["owner"]
            schedule_request.test_execution.checkout.repo_name = data["checkout"][
                "repoName"
            ]
            if "pullRequestCheckout" in data["checkout"]:
                schedule_request.test_execution.checkout.pull_request_checkout.pull_request_number = int(
                    data["checkout"]["pullRequestCheckout"]["pullRequestNumber"]
                )
            elif "commitCheckout" in data["checkout"]:
                schedule_request.test_execution.checkout.commit_checkout.branch = data[
                    "checkout"
                ]["commitCheckout"]["branch"]
                schedule_request.test_execution.checkout.commit_checkout.ref = data[
                    "checkout"
                ]["commitCheckout"].get("ref", "")

            else:
                raise ValueError("Invalid data")

            if data["targets"]:
                run = schedule_request.test_execution.runs.add()
                env = data.get("env", "SINGLE_GPU")
                if env == "CPU":
                    run.env = bazel_runner_pb2.CPU
                elif env == "SINGLE_GPU":
                    run.env = bazel_runner_pb2.SINGLE_GPU
                elif env == "MULTI_GPU":
                    run.env = bazel_runner_pb2.MULTI_GPU
                elif env == "LARGE_GPU":
                    run.env = bazel_runner_pb2.LARGE_GPU
                elif env == "PREMIUM_CPU":
                    run.env = bazel_runner_pb2.PREMIUM_CPU
                else:
                    raise ValueError("Invalid env")
                run.command = "test"
                for target in data["targets"]:
                    run.targets.append(target)
            else:
                schedule_request.test_selection.policy = (
                    test_selection_pb2.TestSelectionPolicy.DEFAULT
                )
            if data.get("notification", False):
                schedule_request.notification.slack.user_name = requestor
        logging.info("schedule request %s", schedule_request)
        response = client.ScheduleTest(schedule_request)
        result_data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(result_data)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code()}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("runs/pending")
@iap_jwt_check
def pending_runs_handler():
    logging.info("pending runs request")

    client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
    get_request = test_runner_pb2.GetPendingRunsRequest()
    response = client.GetPendingRuns(get_request)
    logging.info("response %s", response)
    return jsonify([run_id for run_id in response.run_ids])


@bp.route("run/<run_id>")
@iap_jwt_check
def run_handler(run_id: str):
    logging.info("request for %s", uuid.UUID(run_id))

    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        get_request = test_runner_pb2.GetTestInfoRequest()
        get_request.run_id = run_id
        logging.info("get request %s", get_request)
        response = client.GetTestInfo(get_request)
        logging.info("get response %s", response)
        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(data)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("runs")
@iap_jwt_check
def batch_get_runs_handler():
    oldest_run_id = request.args.get("oldest_run_id", "")
    newest_run_id = request.args.get("newest_run_id", "")
    max_count = int(request.args.get("max_count", "20"))
    logging.info("request for %s to %s", oldest_run_id, newest_run_id)

    client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
    batch_get_request = test_runner_pb2.GetRunsRequest()
    batch_get_request.oldest_run_id = oldest_run_id
    batch_get_request.newest_run_id = newest_run_id
    batch_get_request.max_count = max_count
    logging.info("get runs request %s", batch_get_request)
    response_stream = client.GetRuns(batch_get_request)
    data = []
    for response in response_stream:
        for run in response.runs:
            data.append(
                json_format.MessageToDict(
                    run,
                    including_default_value_fields=True,  # type: ignore
                )
            )
    json_data = jsonify({"runs": data})
    logging.info("json_data %s", json_data)
    return json_data


@bp.route("run/<run_id>/restart", methods=["POST"])
@iap_jwt_check
def run_restart_handler(run_id: str):
    logging.info("restart request for %s", uuid.UUID(run_id))

    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        get_request = test_runner_pb2.GetTestInfoRequest()
        get_request.run_id = run_id
        logging.info("get request %s", get_request)
        response = client.GetTestInfo(get_request)
        logging.info("response %s", response)

        # we take the requestor from the current operations, not
        # from the run id that is restarted.
        requestor = iap_util.extract_user(request)
        assert requestor is not None

        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        schedule_request = test_runner_pb2.ScheduleTestRequest()
        schedule_request.requestor = requestor

        # we add restart together with the tags from the run id that is restarted.
        schedule_request.tags.extend(response.tags)
        if "restart" not in schedule_request.tags:
            schedule_request.tags.append("restart")

        schedule_request.test_execution.MergeFrom(response.test_execution)
        if response.HasField("test_selection"):
            while schedule_request.test_execution.runs:
                schedule_request.test_execution.runs.pop()
            schedule_request.test_selection.MergeFrom(response.test_selection)
        if response.HasField("notification"):
            schedule_request.notification.MergeFrom(response.notification)
        logging.info("schedule request %s", schedule_request)
        response = client.ScheduleTest(schedule_request)
        result_data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(result_data)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("run/<run_id>/cancel", methods=["POST"])
@iap_jwt_check
def run_cancel_handler(run_id: str):
    logging.info("cancel request for %s", uuid.UUID(run_id))

    # we take the requestor from the current operations, not
    # from the run id that is restarted.
    requestor = iap_util.extract_user(request)
    assert requestor is not None

    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        cancel_request = test_runner_pb2.CancelTestRequest()
        cancel_request.run_id = run_id
        cancel_request.cancelled_by = requestor

        logging.info("cancel request %s", cancel_request)
        client.CancelTest(cancel_request)
        return jsonify({})
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("run/<run_id>/<job_id>/build_events")
@iap_jwt_check
def run_build_events_handler(run_id: str, job_id: str):
    logging.info("events request for %s/%s", uuid.UUID(run_id), uuid.UUID(job_id))

    min_sequence_number = int(request.args.get("min_sequence_number", "0"))
    limit = int(request.args.get("limit", "1000"))
    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        get_request = test_runner_pb2.GetBuildEventsRequest()
        get_request.run_id = run_id
        get_request.job_id = job_id
        get_request.min_sequence_number = min_sequence_number
        get_request.limit = limit
        logging.info("build event request %s", get_request)
        events = []
        for response in client.GetBuildEvents(get_request):
            item = {
                "sequenceNumber": response.sequence_number,
            }
            if response.HasField("test_summary"):
                item["testSummary"] = json_format.MessageToDict(
                    response.test_summary,
                    including_default_value_fields=True,  # type: ignore
                )
            item["buildEvent"] = json_format.MessageToDict(
                response.build_event,
                including_default_value_fields=True,  # type: ignore
            )
            events.append(item)
        logging.info("build event response %s", len(events))
        return jsonify(events)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("run/<run_id>/<job_id>/console")
@iap_jwt_check
def run_console_handler(run_id: str, job_id: str):
    logging.info("console request for %s/%s", uuid.UUID(run_id), uuid.UUID(job_id))

    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        get_request = test_runner_pb2.GetCommandLogRequest()
        get_request.run_id = run_id
        get_request.job_id = job_id
        logging.info("console request %s", get_request)
        command_log = []
        for response in client.GetCommandLog(get_request):
            command_log.append(response.content)
        return jsonify("".join(command_log))
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("run/<run_id>/<job_id>/test_cases")
@iap_jwt_check
def run_test_case_handler(run_id: str, job_id: str):
    target_name = request.args.get("target_name")

    logging.info(
        "test case request for %s/%s/%s",
        uuid.UUID(run_id),
        uuid.UUID(job_id),
        target_name,
    )

    if not target_name:
        return Response(
            json.dumps({"code": grpc.StatusCode.INVALID_ARGUMENT.code().value[0]}),  # type: ignore
            status=40,
            mimetype="application/json",
        )
    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        get_request = test_runner_pb2.GetTestCasesRequest()
        get_request.run_id = run_id  # type: ignore
        get_request.job_id = job_id  # type: ignore
        get_request.target_name = target_name  # type: ignore
        logging.info("test case request %s", get_request)
        response = client.GetTestCases(get_request)
        data = [
            json_format.MessageToDict(
                test_case,
                including_default_value_fields=True,  # type: ignore
            )
            for test_case in response.test_cases
        ]
        return jsonify(data)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("run/<run_id>/<job_id>/archive/<path:archive_file>")
@iap_jwt_check
def run_archive_handler(run_id: str, job_id: str, archive_file: str):
    logging.info(
        "archive request for %s/%s/%s",
        uuid.UUID(run_id),
        uuid.UUID(job_id),
        archive_file,
    )

    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        get_request = test_runner_pb2.GetArchiveDataRequest()
        get_request.run_id = run_id
        get_request.job_id = job_id
        get_request.archive_file = archive_file
        logging.info("archive request %s", get_request)
        archive_data = b""
        for response in client.GetArchiveData(get_request):
            archive_data += response.content
        response = make_response(archive_data)
        guessed_type = mimetypes.guess_type(archive_file)[0]
        if guessed_type:
            response.headers.set("Content-Type", guessed_type)
        response.headers.set("Content-Disposition", "attachment", filename=archive_file)
        return response
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("run/<run_id>/<job_id>/logs")
@iap_jwt_check
def run_get_log(run_id: str, job_id: str):
    # didn't put into URL to bypass parsing issues
    target_name = request.args.get("target_name")
    if not target_name:
        return Response(
            json.dumps({"code": grpc.StatusCode.INVALID_ARGUMENT.code().value[0]}),  # type: ignore
            status=40,
            mimetype="application/json",
        )

    logging.info(
        "log request for %s/%s/%s",
        uuid.UUID(run_id),
        uuid.UUID(job_id),
        target_name,
    )

    try:
        client: test_runner_pb2_grpc.TestRunnerStub = current_app.test_runner_rpc_client  # type: ignore
        get_request = test_runner_pb2.GetTestLogsRequest()
        get_request.run_id = run_id
        get_request.job_id = job_id
        get_request.target_name = target_name
        logging.info("log request %s", get_request)

        data = {}
        for response in client.GetTestLogs(get_request):
            key = (response.shard_index, response.shard_count, response.attempt_index)
            if key in data:
                data[key] += response.content
            else:
                data[key] = response.content
        response = [
            {
                "shardIndex": k[0],
                "shardCount": k[1],
                "attemptIndex": k[2],
                "content": v.decode("utf-8", "replace"),
            }
            for (k, v) in data.items()
        ]
        return jsonify(response)
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("bytestream/<path:archive_file>")
@iap_jwt_check
def run_bytestream_handler(archive_file: str):
    logging.info("request for %s", archive_file)

    try:
        uri = urlparse(archive_file)
        if uri.scheme != "bytestream":
            return Response(
                json.dumps({"code": grpc.StatusCode.INVALID_ARGUMENT.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )

        logging.info("%s", uri)
        # somehow DNS resolution isn't working here
        if current_app.config["OVERRIDE_BAZEL_CACHE_GRPC_ENDPOINT"]:
            endpoint = current_app.config["OVERRIDE_BAZEL_CACHE_GRPC_ENDPOINT"]
        else:
            endpoint = uri.netloc
        resource_name = uri.path
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
        client = bytestream_pb2_grpc.ByteStreamStub(channel)
        read_request = bytestream_pb2.ReadRequest()
        read_request.resource_name = resource_name  # type: ignore
        read_request.read_offset = 0  # type: ignore
        read_request.read_limit = 0  # type: ignore
        logging.info("bytestream request %s", read_request)
        data = b""
        for response in client.Read(read_request):
            data += response.data
        logging.info("got data %s", data)
        response = make_response(data)
        return response
    except grpc.RpcError as rpc_error:
        if rpc_error.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
            return Response(
                json.dumps({"code": rpc_error.code().value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        else:
            raise


@bp.route("run/<path:path>")
def api_missing(path: str):  # pylint: disable=unused-argument
    return Response(json.dumps({}), status=404, mimetype="application/json")

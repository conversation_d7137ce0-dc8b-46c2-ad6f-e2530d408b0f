"""Flask application to provide the backend for the test viewer web UI."""

import logging
import os
from datetime import datetime
from pathlib import Path

from flask import Flask, send_from_directory
from gunicorn.app.base import BaseApplication
from prometheus_flask_exporter.multiprocess import GunicornPrometheusMetrics

import tools.bazel_runner.web.backend.api as api
from base.logging.struct_logging import setup_struct_logging
from tools.bazel_runner.client.bazel_runner_client import setup_client


def create_app():
    app = Flask(__name__, static_folder="../frontend/dist")
    _ = GunicornPrometheusMetrics(app, port=9090, group_by="endpoint")

    if "CONFIG_FILE" in os.environ:
        app.config.from_envvar("CONFIG_FILE")
    else:
        app.config.from_object(
            "augment.tools.bazel_runner.web.backend.config.TestConfig"
        )
    logging.info("Config %s", app.config)

    insecure = not app.config["CLIENT_MTLS"]

    test_runner_rpc_client = setup_client(
        endpoint=app.config["TEST_RUNNER_GRPC_URL"], insecure=insecure
    )

    app.test_runner_rpc_client = test_runner_rpc_client  # type: ignore

    app.register_blueprint(api.bp)  # type: ignore

    # Serve React App
    @app.route("/", defaults={"path": ""})
    @app.route("/<path:path>")
    def serve(path):
        static_folder_path = Path(app.static_folder).absolute()  # type: ignore
        item_path = static_folder_path / path
        if path != "" and item_path.exists():
            r = send_from_directory(str(app.static_folder), path, max_age=0)
        else:
            r = send_from_directory(str(app.static_folder), "index.html", max_age=0)
        r.headers["Cache-Control"] = (
            "no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0"
        )
        r.headers["Pragma"] = "no-cache"
        r.headers["Expires"] = "-1"
        r.headers["Last-Modified"] = datetime.now().isoformat()
        return r

    return app


class _App(BaseApplication):
    """Gunicorn wrapper for service Flask App."""

    # pylint: disable=abstract-method

    def __init__(self, app, options):
        self.application = app
        self.options = options
        super().__init__()

    def load_config(self):
        assert (
            self.cfg is not None
        )  # cfg is never None here, as it is initialized by Gunicorn. adding check for pylint
        for key, value in self.options.items():
            self.cfg.set(key, value)

    def load(self):
        return self.application


def when_ready(server):
    del server
    GunicornPrometheusMetrics.start_http_server_when_ready(9090)


def child_exit(server, worker):
    del server
    GunicornPrometheusMetrics.mark_process_dead_on_child_exit(worker.pid)


def main():
    setup_struct_logging()

    app = create_app()
    port = app.config.get("PORT")
    logging.info("Serve port %s", port)

    if app.config.get("DEBUG_MODE"):
        app.run(use_reloader=True, port=port, threaded=True)
    else:
        https_service_key = app.config.get("HTTPS_SERVER_KEY")
        https_service_cert = app.config.get("HTTPS_SERVER_CERT")
        options = {
            "bind": f"0.0.0.0:{port}",
            "threads": 4,
            "keyfile": https_service_key,
            "certfile": https_service_cert,
            "when_ready": when_ready,
            "child_exit": child_exit,
        }
        _App(app, options).run()


if __name__ == "__main__":
    main()

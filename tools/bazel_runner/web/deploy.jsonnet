// K8S deployment file for the test viewer web UI
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local backendConfig = gcpLib.createBackendConfig(app='test-viewer',
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTPS',
                                                     requestPath: '/',
                                                   },
                                                   iap=true);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'test-viewer-svc',
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'viewer-https': 'HTTPS' }),
      },
      labels: {
        app: 'test-viewer',
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: 'test-viewer',
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'viewer-https',
          targetPort: 'viewer-https',
        },
      ],
    },
  };
  // list of all service accounts that are allowed to access the IAP-protected endpoint
  // the service account email address of very service that is allowed to access the IAP-protected endpoint
  // needs to be added. It also needs the IAM role "IAP-secured Web App User" in the project
  local allowedServiceAccounts = if env == 'PROD' then [
    'accounts.google.com:<EMAIL>',
    'accounts.google.com:<EMAIL>',
  ] else [
    'accounts.google.com:<EMAIL>',
  ];
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'test-viewer-config',
      namespace: namespace,
      labels: {
        app: 'test-viewer',
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      'flask.cfg': |||
        PORT=5000
        TEST_RUNNER_GRPC_URL="test-runner-svc:50051"
        OVERRIDE_BAZEL_CACHE_GRPC_ENDPOINT=""
        CLIENT_MTLS=False
        HTTPS_SERVER_KEY="/https-certs/tls.key"
        HTTPS_SERVER_CERT="/https-certs/tls.crt"
        IAP_SERVICE_ACCOUNT_ALLOWED=%s
      ||| % std.manifestJson(allowedServiceAccounts),
    },
  };
  local container =
    {
      name: 'test-viewer',
      target: {
        name: '//tools/bazel_runner/web/backend:image',
        dst: 'test-viewer',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'viewer-https',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        {
          mountPath: '/https-certs',
          name: 'https-certs',
        },
        {
          mountPath: '/tmp/prometheus_multiproc_dir',
          name: 'prometheus-multiproc-dir',
        },
      ],
      env: [
        {
          name: 'CONFIG_FILE',
          value: '/config/flask.cfg',
        },
        {
          name: 'PROMETHEUS_MULTIPROC_DIR',
          value: '/tmp/prometheus_multiproc_dir',
        },
      ],
      readinessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '512Mi',
        },
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName='test-viewer');
  local pod =
    {
      containers: [
        container,
      ],
      affinity: affinity,
      tolerations: tolerations,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      volumes: [
        {
          name: 'prometheus-multiproc-dir',
          emptyDir: {},
        },
        {
          name: 'config',
          configMap: {
            name: 'test-viewer-config',
            items: [
              {
                key: 'flask.cfg',
                path: 'flask.cfg',
              },
            ],
          },
        },
        {
          name: 'https-certs',
          secret: {
            secretName: 'test-viewer-ssl-cert',  // pragma: allowlist secret
          },
        },
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app='test-viewer', cloud=cloud, namespace=namespace);
  local ingressHostname = if namespace == 'devtools' then 'test-viewer.%s' % domainSuffix else 'test-viewer.%s.%s' % [namespace, domainSuffix];
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: 'test-viewer',
        },
        name: 'test-viewer-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'test-viewer-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'test-viewer-svc',
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'test-viewer',
      namespace: namespace,
      labels: {
        app: 'test-viewer',
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: 'test-viewer',
        },
      },
      template: {
        metadata: {
          labels: {
            app: 'test-viewer',
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten(
    [
      config,
      service,
      deployment,
      ingressObjects,
    ]
  )

"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

from pathlib import Path
from typing import Generator, TypeVar

import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
from tools.bazel_runner.client import bazel_runner_client
from tools.bazel_runner.test_selection_server import client as test_selection_client

T = TypeVar("T")
ContextManager = Generator[T, None, None]


# Container details
def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


@pytest.fixture(scope="session")
def deployment(
    request: pytest.FixtureRequest,
) -> ContextManager[k8s_test_helper.DeployInfo]:
    """Deploys a content manager as pytest fixture."""
    skip_deploy = request.config.getoption("--skip-deployment")
    cloud = request.config.getoption("--cloud")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,  # type: ignore
        kubecfg_binaries=[
            Path("tools/bazel_runner/test/test_kubecfg.sh"),
        ],
        cloud=cloud,
    ) as deploy_info:
        yield deploy_info


@pytest.fixture(scope="session")
def test_runner_client(
    deployment: k8s_test_helper.DeployInfo,
) -> ContextManager[bazel_runner_client.BazelRunnerClient]:
    """Return a ContentManagerClient to access the content manager."""
    with k8s_test_helper.get_tunnel_for_deployment(
        deployment,
        "test-runner-rpc",
        50051,
        service_name="",
    ) as url:
        client = bazel_runner_client.BazelRunnerClient(endpoint=url, insecure=True)
        yield client


@pytest.fixture(scope="session")
def test_selection_server(
    deployment: k8s_test_helper.DeployInfo,
) -> ContextManager[test_selection_client.TestSelectionClient]:
    """Return a ContentManagerClient to access the content manager."""
    with k8s_test_helper.get_tunnel_for_deployment(
        deployment,
        "test-selection",
        50051,
        service_name="",
    ) as url:
        yield test_selection_client.TestSelectionClient(url, insecure=True)

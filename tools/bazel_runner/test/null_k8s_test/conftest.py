"""conftest.py is a special file that pytest will automatically load.
pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

from pathlib import Path
from typing import Generator

import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
import tools.bazel_runner.client.bazel_runner_client as bazel_runner_client

from base.python.cloud import cloud as cloud_lib

# Container details


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


@pytest.fixture(scope="session")
def namespace() -> Generator[str | None, None, None]:
    """Return a namespace for the test."""
    yield from bazel_runner_client.namespace_allocation(num_gpus=0)


@pytest.fixture(scope="session")
def null_k8s_test_deploy(
    request, namespace: str | None
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys cloud_sync as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    cloud = request.config.getoption("--cloud")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        kubecfg_binaries=[
            Path("tools/bazel_runner/test/null_k8s_test/test_kubecfg.sh"),
        ],
        cloud=cloud,
        namespace=namespace,
    ) as deploy_info:
        yield deploy_info

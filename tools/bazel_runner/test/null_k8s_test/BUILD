load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg(
    name = "test_kubecfg",
    src = "deploy.jsonnet",
)

pytest_test(
    name = "null_k8s_test",
    size = "medium",
    timeout = "moderate",
    srcs = [
        "conftest.py",
        "null_k8s_test.py",
    ],
    data = [
        ":test_kubecfg",
    ],
    tags = [
        "exclusive",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//tools/bazel_runner/client",
        requirement("kubernetes"),
    ],
)

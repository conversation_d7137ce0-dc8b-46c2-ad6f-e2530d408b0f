load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//tools/bazel_runner/ci:kubecfg",
        "//tools/bazel_runner/server:kubecfg",
        "//tools/bazel_runner/test_selection_server:kubecfg",
    ],
)

pytest_test(
    name = "bazel_runner_manual_test",
    srcs = [
        "bazel_runner_manual_test.py",
        "conftest.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = ["manual"],
    deps = [
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//tools/bazel_runner/client",
        "//tools/bazel_runner/test_selection_server:client",
    ],
)

# a dummy test that always passes
pytest_test(
    name = "dummy_test",
    srcs = [
        "dummy_test.py",
    ],
)

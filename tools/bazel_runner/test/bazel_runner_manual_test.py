"""This file contains pytest tests for the bazel_runner_client.

This is a test that only can be run manually as it depends on
external setups. However, it might be useful in the future.

Currently, the test would have a risk of deadlock when running
in the test infra itself.
"""

import datetime
import time

import tools.bazel_runner.server.test_runner_pb2 as test_runner_pb2
import tools.bazel_runner.test_selection_server.test_selection_pb2 as test_selection_pb2
from tools.bazel_runner.client import bazel_runner_client
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.git import checkout_pb2


def test_test_selection(test_selection_server):
    start_checkout = checkout_pb2.CheckoutSpec()
    start_checkout.owner = "augmentcode"
    start_checkout.repo_name = "augment"
    start_checkout.commit_checkout.branch = "main"
    end_checkout = checkout_pb2.CheckoutSpec()
    end_checkout.owner = "augmentcode"
    end_checkout.repo_name = "augment"
    end_checkout.pull_request_checkout.pull_request_number = 2630
    operation_id = test_selection_server.get_test_targets(
        start_checkout, end_checkout, test_selection_pb2.TestSelectionPolicy.DEFAULT
    )
    print(operation_id)

    while True:
        result = test_selection_server.get_results(operation_id)
        if result:
            break
        time.sleep(5)
    print(result)
    assert [
        t for t in result.targets if t.name == "//tools/monitoring:kubecfg_grafana_test"
    ]


def test_test_selection_without_start_checkout(test_selection_server):
    end_checkout = checkout_pb2.CheckoutSpec()
    end_checkout.owner = "augmentcode"
    end_checkout.repo_name = "augment"
    end_checkout.pull_request_checkout.pull_request_number = 2630
    operation_id = test_selection_server.get_test_targets(
        None, end_checkout, test_selection_pb2.TestSelectionPolicy.DEFAULT
    )
    print(operation_id)

    while True:
        result = test_selection_server.get_results(operation_id)
        if result:
            break
        time.sleep(5)
    print(result)
    assert [
        t for t in result.targets if t.name == "//tools/monitoring:kubecfg_grafana_test"
    ]


def test_run_simple(test_runner_client):
    te = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    r = te.runs.add()
    r.targets.append("//tools/monitoring:kubecfg_grafana_test")
    r.command = "test"
    te.checkout.owner = "augmentcode"
    te.checkout.repo_name = "augment"
    te.checkout.pull_request_checkout.pull_request_number = 2630
    run_id = test_runner_client.schedule_run(te, requestor="dirk", tags=["manual"])
    print(run_id)
    r = test_runner_client.wait_for_run(run_id, datetime.timedelta(minutes=30))
    print(r)


def test_run_test_selection(test_runner_client: bazel_runner_client.BazelRunnerClient):
    ts = test_runner_pb2.TestSelectionSpec()
    ts.start_checkout.owner = "augmentcode"
    ts.start_checkout.repo_name = "augment"
    ts.start_checkout.commit_checkout.branch = "main"
    te = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    te.checkout.owner = "augmentcode"
    te.checkout.repo_name = "augment"
    te.checkout.pull_request_checkout.pull_request_number = 2630
    run_id = test_runner_client.schedule_run(
        te, requestor="dirk", tags=["manual"], test_selection=ts
    )
    print(run_id)
    r = test_runner_client.wait_for_run(run_id, datetime.timedelta(minutes=30))
    assert r.is_done()
    assert r.spec.runs[0].targets[0] == "//tools/monitoring:kubecfg_grafana_test"


def test_run_notification(test_runner_client: bazel_runner_client.BazelRunnerClient):
    te = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    r = te.runs.add()
    r.targets.append("//tools/monitoring:kubecfg_grafana_test")
    r.env = bazel_runner_pb2.CPU
    r.command = "test"
    te.checkout.owner = "augmentcode"
    te.checkout.repo_name = "augment"
    te.checkout.pull_request_checkout.pull_request_number = 2515
    notification = test_runner_pb2.NotificationSpec()
    notification.github_status.sha = (
        "37bf1f2bb495a860dffeec875c3546a252f50d2a"  # pragma: allowlist secret
    )
    notification.github_status.context = "test"
    run_id = test_runner_client.schedule_run(
        te, requestor="dirk", tags=["manual"], notification=notification
    )
    print(run_id)
    r = test_runner_client.wait_for_run(run_id, datetime.timedelta(minutes=30))
    print(r)
    # TODO: manually inspect 37bf1f2bb495a860dffeec875c3546a252f50d2a see if it has a status update

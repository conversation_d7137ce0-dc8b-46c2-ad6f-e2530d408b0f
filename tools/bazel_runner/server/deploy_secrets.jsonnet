local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud)
  assert cloud == 'GCP_US_CENTRAL1_DEV';

  // secret for accessing the staging shard
  //
  // see https://console.cloud.google.com/security/secret-manager/secret/bazel-runner-dogfood-shard-api-key/versions?project=system-services-dev
  local stagingApiKeySecretName = 'projects/%(PROJECT_ID)s/secrets/%(SECRET_ID)s/versions/%(VERSION_ID)s' % {  // pragma: allowlist secret
    PROJECT_ID: cloudInfo[cloud].projectId,
    SECRET_ID: 'bazel-runner-dogfood-shard-api-key',
    VERSION_ID: '1',
  };
  // secret for accessing Anthropic API
  //
  local anthropicApiKeySecretName = 'projects/%(PROJECT_ID)s/secrets/%(SECRET_ID)s/versions/%(VERSION_ID)s' % {  // pragma: allowlist secret
    PROJECT_ID: cloudInfo[cloud].projectId,
    SECRET_ID: 'bazel-runner-claude-api-key',
    VERSION_ID: '1',
  };
  // CloudIdentityGroup for all technical staff (everyone listed in eng.jsonnet).
  local engCloudIdentityGroupName = 'eng-access';
  local engCloudIdentityGroup = gcpLib.cloudIdentityGroup(
    'devtools', 'eng-auth', engCloudIdentityGroupName, 'Basic engineer access'
  );
  local members = [
    // every engineer gets access to the staging api key secret
    { member: 'group:%s' % engCloudIdentityGroup.groupEmail },
  ] + [{
    member: 'serviceAccount:devtools-bazel-test-iam@%s.iam.gserviceaccount.com' % cloudInfo[cloud].projectId,
  }];
  local secrets = [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'staging-api-key-secret-access',
        namespace: 'test',
      },
      spec: {
        resourceRef: {
          kind: 'SecretManagerSecret',
          external: stagingApiKeySecretName,
        },
        bindings: [
          {
            members: members,
            role: 'roles/secretmanager.secretAccessor',
          },
        ],
      },
    },
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'anthropic-api-key-secret-access',
        namespace: 'test',
      },
      spec: {
        resourceRef: {
          kind: 'SecretManagerSecret',
          external: anthropicApiKeySecretName,
        },
        bindings: [
          {
            members: members,
            role: 'roles/secretmanager.secretAccessor',
          },
        ],
      },
    },
  ];
  secrets

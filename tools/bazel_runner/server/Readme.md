# Bazel Runner Server

# Run State Transitions

These states and state transitions refers to the internal processing states of the
Bazel Runner processing event loop.

## State INIT


The test run has been created

Transitions:
- CHECKOUT: the checkout job has been created
- CANCELED: the test run has been cancelled

## State CHECKOUT

The checkout job has been created in Kubernetes. The checkout job will schedule
a pod to checkout the specified git commit

Transitions:
- RUN: the checkout job is finished and a run job has been created
- ABORT: the checkout job has been aborted, e.g. the commit doesn't exist
- ERROR: the checkout job failed with an infrastructure error

## State RUN

The run jobs have been created in Kubernetes. The run jobs will schedule
pods to run the specified Bazel tests.

Often a test run consists of multiple run jobs, e.g. on different environment, e.g. an run job using a single
GPU while a second run job is scheduled to run additional test targets ona multi-GPU system.

That the run job has been scheduled doesn't mean that there is a running pod for any of the jobs.
The pod creation is the responsibility of Kubernetes and outside of the bazel runner processing event loop.

During the run state, we track is a pod is running mainly to aid the user interface.

Transitions:
- POST: all run jobs have succeeded and the postprocessing job has been scheduled.
- ERROR: a run job failed with an infrastructure error

## State POST

The postprocessing job has been scheduled.

Transitions:
- DONE
- ERROR: a run job failed with an infrastructure error

## State DONE

The post processing has been finished. If the job has succeeded, i.e. all artifacts have been uploaded to S3.

This is a final state.
The test results might indicate SUCCESS or FAILURE, but the test execution is done.

Philosophical note: The job of a test infrastructure is not to create green dots, but to provide timely feedback
to the engineers.

## GCP

The GCP implementation stores state in BigTable.
The bazel_runner_store.proto contains a description of the schema and the key and column family information.

## Manual Test

```
bazel run server:kubecfg
bazel run server:bazel_runner_server_gcp_lib_manual_test -- -s
```

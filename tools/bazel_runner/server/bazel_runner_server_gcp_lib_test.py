import ulid
from tools.bazel_runner.server import bazel_runner_server_gcp_lib
from time import sleep
import uuid

from tools.bazel_runner.server.bazel_runner_server_gcp_lib import (
    transform_target_name_to_path,
)


def test_timestamp_key():
    print()
    run_id1 = ulid.ULID().to_uuid()
    t1 = bazel_runner_server_gcp_lib.get_timestamp_key(run_id1)
    print(run_id1)
    print(run_id1.bytes.hex())
    print(t1.hex())

    sleep(0.1)
    run_id2 = ulid.ULID().to_uuid()
    print(run_id2)
    print(run_id2.bytes.hex())
    t2 = bazel_runner_server_gcp_lib.get_timestamp_key(run_id2)
    print(t2.hex())

    assert t2 < t1


def test_timestamp_preset():
    print()
    run_id_older = uuid.UUID("018ec972-7b43-f149-16c5-7947dc64dc43")
    print(run_id_older)
    t_older = bazel_runner_server_gcp_lib.get_timestamp_key(run_id_older)
    print(t_older.hex())

    run_id_newer = uuid.UUID("018ec9a0-756a-95d7-dec5-ea7b76f5e9b0")
    print(run_id_newer)
    t_newer = bazel_runner_server_gcp_lib.get_timestamp_key(run_id_newer)
    print(t_newer.hex())
    assert len(t_newer) == len(t_older)
    assert len(t_newer) == 29

    assert t_newer < t_older


def test_transform_target_name_to_path():
    assert transform_target_name_to_path("//foo/bar") == "foo/bar"
    assert transform_target_name_to_path("//foo/bar:baz") == "foo/bar/baz"
    assert transform_target_name_to_path("//:baz") == "baz"

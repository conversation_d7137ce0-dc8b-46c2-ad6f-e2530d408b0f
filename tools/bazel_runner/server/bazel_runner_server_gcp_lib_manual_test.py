"""This file contains pytest tests for the bazel_runner_server_gcp_lib.

This is a test that only can be run manually as it depends on
external setups. However, it might be useful in the future.
"""

import datetime
import logging
import pathlib
import uuid

import pytest

import third_party.proto.bazel_build.build_event_stream_pb2 as build_event_stream_pb2
from tools.bazel_runner.bep_parser import test_summary_pb2
from tools.bazel_runner.control import bazel_runner_pb2
from tools.bazel_runner.server import (
    bazel_runner_server_gcp_lib,
    test_runner_pb2,
)
from tools.bazel_runner.server.config import Config


@pytest.fixture(scope="session")
def config(request):
    config_file = request.config.getoption("--bazel-runner-config-file")
    config = Config.load_config(pathlib.Path(config_file))
    yield config


@pytest.fixture(scope="session")
def persistence(config):
    yield bazel_runner_server_gcp_lib.GcpPersistence.create(config=config)


@pytest.fixture(scope="session")
def build_event_persistence(config):
    yield bazel_runner_server_gcp_lib.GcpBuildEventPersistence.create(config=config)


@pytest.fixture(scope="session")
def queue(config):
    yield bazel_runner_server_gcp_lib.GcpQueue.create(config=config)


@pytest.fixture(scope="session")
def object_access(config):
    yield bazel_runner_server_gcp_lib.GcpObjectAccess.create(config=config)


def test_object_access(object_access):
    archives = list(
        object_access.list_archive(
            uuid.UUID("018b8e33-ef1b-691a-4a23-9456f2017a00"),
            uuid.UUID("018b8e34-4a78-5ca7-ff29-82c9777adb4b"),
        )
    )
    assert archives

    archives = list(
        object_access.list_target_archive(
            uuid.UUID("018b8e33-ef1b-691a-4a23-9456f2017a00"),
            uuid.UUID("018b8e34-4a78-5ca7-ff29-82c9777adb4b"),
            "//base/fastforward:cached_attention_test",
        )
    )
    assert archives


def test_queue(queue):
    run_id = uuid.uuid4()

    queue.notify(run_id)
    for received_run_id in queue.receive():
        if run_id == received_run_id:
            break


def test_bes(build_event_persistence):
    job_id = uuid.uuid4()
    build_event_persistence.store(
        job_id=job_id,
        invocation_id=uuid.uuid4(),
        sequence_number=10,
        build_event=None,
        test_summary=None,
        event_time=datetime.datetime.now(),
    )

    events = list(
        build_event_persistence.get_build_events(job_id=job_id, min_sequence_number=0)
    )
    assert len(events) == 1

    build_event_persistence.store(
        job_id=job_id,
        invocation_id=uuid.uuid4(),
        sequence_number=3000,
        build_event=build_event_stream_pb2.BuildEvent(),
        test_summary=test_summary_pb2.TestSummary(),
        event_time=datetime.datetime.now(),
    )

    events = list(
        build_event_persistence.get_build_events(job_id=job_id, min_sequence_number=0)
    )
    assert len(events) == 2
    assert events[1][0] == 3000

    events = list(
        build_event_persistence.get_build_events(
            job_id=job_id, min_sequence_number=2000
        )
    )
    assert len(events) == 1
    assert events[0][0] == 3000

    events = list(
        build_event_persistence.get_build_events(
            job_id=job_id, min_sequence_number=4000
        )
    )
    assert len(events) == 0

    build_event_persistence.store(
        job_id=job_id,
        invocation_id=uuid.uuid4(),
        sequence_number=5000,
        build_event=build_event_stream_pb2.BuildEvent(),
        test_summary=test_summary_pb2.TestSummary(),
        event_time=datetime.datetime.now(),
    )

    build_event_persistence.store(
        job_id=job_id,
        invocation_id=uuid.uuid4(),
        sequence_number=70000,
        build_event=build_event_stream_pb2.BuildEvent(),
        test_summary=test_summary_pb2.TestSummary(),
        event_time=datetime.datetime.now(),
    )

    events = list(
        build_event_persistence.get_build_events(
            job_id=job_id, min_sequence_number=2000
        )
    )
    assert len(events) == 3
    assert events[0][0] == 3000
    assert events[1][0] == 5000
    assert events[2][0] == 70000


def test_create_to_done(persistence):
    test_spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run_id = uuid.uuid4()
    persistence.create_run(
        run_id=run_id, test_execution=test_spec, requestor="user1", tags=["manual"]
    )

    pendings = persistence.get_pending_runs()
    logging.info("Pending runs %s", pendings)
    assert run_id in pendings

    results = list(persistence.search_runs_by_tag("manual", max_results=1))
    logging.info("Runs %s", results)
    assert run_id in results
    assert len(results) == 1

    results = list(persistence.search_runs_by_requestor("user1", max_results=1))
    logging.info("Runs %s", results)
    assert run_id in results
    assert len(results) == 1

    run_info = persistence.get_run_info(run_id)
    logging.info("Run %s", run_info)
    assert run_info.requestor == "user1"

    persistence.move_to_checkout_state(run_id, uuid.uuid4())

    run_info = persistence.get_run_info(run_id)
    assert run_info.checkout.job_id
    logging.info("Run %s", run_info)

    persistence.move_to_run_state(run_id, job_ids=[uuid.uuid4()], has_running_jobs=True)

    run_info = persistence.get_run_info(run_id)
    assert run_info.checkout.job_id
    assert run_info.run.job_ids
    logging.info("Run %s", run_info)

    persistence.move_to_postprocessing_state(run_id, uuid.uuid4())

    run_info = persistence.get_run_info(run_id)
    assert run_info.checkout.job_id
    assert run_info.run.job_ids
    assert run_info.postprocessing.job_id
    logging.info("Run %s", run_info)

    persistence.move_to_done_state(run_id, job_infos=[test_runner_pb2.JobInfo()])

    run_info = persistence.get_run_info(run_id)
    assert run_info.checkout.job_id
    assert run_info.run.job_ids
    assert run_info.postprocessing.job_id
    assert run_info.done
    logging.info("Run %s", run_info)

    pendings = persistence.get_pending_runs()
    logging.info("Pending runs %s", pendings)
    assert run_id not in pendings


def test_error_state(persistence):
    test_spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run_id = uuid.uuid4()
    persistence.create_run(
        run_id=run_id, test_execution=test_spec, requestor="user1", tags=["manual"]
    )

    persistence.move_to_checkout_state(run_id, uuid.uuid4())

    persistence.move_to_error_state(run_id, "error")
    run_info = persistence.get_run_info(run_id)
    assert run_info.error.message == "error"
    logging.info("Run %s", run_info)

    pendings = persistence.get_pending_runs()
    logging.info("Pending runs %s", pendings)
    assert run_id not in pendings


def test_abort_state(persistence):
    test_spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run_id = uuid.uuid4()
    persistence.create_run(
        run_id=run_id, test_execution=test_spec, requestor="user1", tags=["manual"]
    )

    persistence.move_to_checkout_state(run_id, uuid.uuid4())

    persistence.move_to_abort_state(run_id, "abort")
    run_info = persistence.get_run_info(run_id)
    assert run_info.abort.message == "abort"
    logging.info("Run %s", run_info)

    pendings = persistence.get_pending_runs()
    logging.info("Pending runs %s", pendings)
    assert run_id not in pendings


def test_cancel(persistence):
    test_spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()
    run_id = uuid.uuid4()
    persistence.create_run(
        run_id=run_id, test_execution=test_spec, requestor="user1", tags=["manual"]
    )

    persistence.move_to_checkout_state(run_id, uuid.uuid4())

    persistence.cancel_run(run_id, cancelled_by=None)
    run_info = persistence.get_run_info(run_id)
    assert run_info.state.current_state == "CANCEL"
    logging.info("Run %s", run_info)

    pendings = persistence.get_pending_runs()
    logging.info("Pending runs %s", pendings)
    assert run_id not in pendings

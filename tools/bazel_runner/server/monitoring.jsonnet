local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  // this is a cluster role as kube_job_status_start_time is a cluster level metric
  local testInfraSpec = {
    displayName: 'Test Infra Errors',
    conditionPrometheusQueryLanguage: {
      duration: '600s',
      evaluationInterval: '300s',
      labels: { severity: 'info' },
      query: |||
        sum(increase(bazel_runner_run_finished_total{state="ERROR"}[1h])) > 4
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(cloud, testInfraSpec, 'bazel-runner-test-infrastructure-errors', 'Tests are failing with "Test Infrastructure" errors'),
  ]

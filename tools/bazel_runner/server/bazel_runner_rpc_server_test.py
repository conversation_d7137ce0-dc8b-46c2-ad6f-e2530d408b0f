import pytest
from tools.bazel_runner.server.bazel_runner_rpc_server import archive_path_to_response
from tools.bazel_runner.server import test_runner_pb2


@pytest.mark.parametrize(
    "archive_path, expected_result",
    [
        (
            "shard_1_of_3/test_attempts/attempt_2.log",
            test_runner_pb2.GetTestLogsResponse(
                shard_index=1, shard_count=3, attempt_index=2
            ),
        ),
        (
            "shard_0_of_1/test.log",
            test_runner_pb2.GetTestLogsResponse(shard_index=0, shard_count=1),
        ),
        (
            "test_attempts/attempt_0.log",
            test_runner_pb2.GetTestLogsResponse(attempt_index=0),
        ),
        ("test.log", test_runner_pb2.GetTestLogsResponse()),
        ("invalid_path.log", None),
        ("shard_1_of_3/test.xml", None),
    ],
)
def test_archive_path_to_response(archive_path, expected_result):
    result = archive_path_to_response(archive_path)

    if expected_result is None:
        assert result is None
    else:
        assert result == expected_result


def test_archive_path_to_response_invalid_shard():
    result = archive_path_to_response("shard_invalid_of_3/test.log")
    assert result is None


def test_archive_path_to_response_invalid_attempt():
    result = archive_path_to_response("shard_1_of_3/attempt_invalid.log")
    assert result is None

# The script assumes docker is installed and logged in.
# So it cannot run in a docker container or Kubernetes pod.
#
set -euo pipefail
DATE=$(date +'%Y%m%d')

# Temporary user images. Will be GCd with other build images
IMAGE=us-central1-docker.pkg.dev/system-services-dev/build-images/debug_pod
TAG=$USER-$DATE

# Default image location. Not subject to frequent GC.
PUBLISH=false
REGISTRIES=(
	"us-central1-docker.pkg.dev/system-services-dev/devtools-repository"
	"us-central1-docker.pkg.dev/system-services-prod/devtools-repository"
	"europe-west4-docker.pkg.dev/system-services-prod/devtools-repository-eu-w4"
	"us-central1-docker.pkg.dev/system-services-prod-gsc/devtools-repository"
)

while [[ $# -gt 0 ]]; do
	case $1 in
	--publish-default)
		PUBLISH=true
		shift # past argument
		;;
	-h | --help)
		echo "Usage: $0 [--publish-default]"
		exit 0
		;;
	*) # unknown option
		echo "Unknown option: $1"
		exit 1
		;;
	esac
done

# Build the image
set DOCKER_BUILDKIT=0
BUILD_DIR=$BUILD_WORKSPACE_DIRECTORY/tools/debug_pod/dockerfiles
docker build -f $BUILD_DIR/debug_pod.Dockerfile $BUILD_DIR/ -t $IMAGE:$TAG
echo "Pushing $IMAGE:$TAG"
docker image push $IMAGE:$TAG

if [ "$PUBLISH" = true ]; then
	for REGISTRY in "${REGISTRIES[@]}"; do
		reg_tag=$REGISTRY/debug_pod:$DATE
		docker image tag $IMAGE:$TAG $reg_tag
		echo "Pushing $reg_tag"
		docker image push $reg_tag || echo "Failed to push $reg_tag"
	done
	echo "Update tools/debug_pod/debug_pod.py with new image digests"
	echo "Then run //tools/debug_pod:image_pull_check to verify the upload"
fi

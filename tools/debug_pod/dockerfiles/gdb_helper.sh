TARGET_PID=${TARGET_PID:-1}
EXE=$(readlink /proc/$TARGET_PID/exe)
ROOT=/proc/$TARGET_PID/root

echo "Debugging pid=$TARGET_PID exe=$EXE"

# This 'set sysroot' only works if the shared library being loaded is a regular file.
# If it's a symbolic link, gdb will correctly find the link, but it will resolve to another path
# that is relative to /proc/$TARGET_PID/root, and sysroot will not apply again. GDB will be unable to
# find the shared library.

gdb $ROOT/$EXE \
	-ex "set sysroot $ROOT" \
	-ex "attach $TARGET_PID" \
	"$@"

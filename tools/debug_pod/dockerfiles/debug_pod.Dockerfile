FROM ubuntu:22.04

# Install tools for debugging, binary analysis, performance profiling, etc.
# Image intends to support services implemented in any combination of Rust, Go, Python

RUN apt-get update -y && \
    apt-get install -y \
    ca-certificates \
    tzdata \
    curl \
    binutils \
    gdb \
    strace \
    python3 \
    vim \
    less && \
    apt-get upgrade -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Linux perf requires kernel-specific packages. We run our containers on
# Cloud-Optimized OS VM (cos_containerd), running a custom 6.1.85+ kernel.
# As yet unclear how we will install perf to the container, make available from
# the Host, or disallow entirely from within containers.
#
# Note: Installing linux-tools-generic and running the debug container with capability
# PERFMON gives access to a very small set of perf counters, not including cpu_cycles.
#linux-tools-common \
#linux-tools-<kernel version> \
#linux-tools-cloud-<kernel version> \

RUN curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py && \
    sha256sum -c <<SUM
        6fb7b781206356f45ad79efbb19322caa6c2a5ad39092d0d44d0fec94117e118 get-pip.py
SUM
RUN python3 get-pip.py --user && \
    rm get-pip.py

RUN python3 -m pip install py-spy

ADD gdb_helper.sh /root/gdb_helper.sh
WORKDIR /root

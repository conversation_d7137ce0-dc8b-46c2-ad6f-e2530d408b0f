load("//tools/bzl:python.bzl", "py_binary")
load("@python_pip//:requirements.bzl", "requirement")

py_binary(
    name = "debug_pod",
    srcs = ["debug_pod.py"],
    deps = [
        "//base/cloud/k8s:kubectl_factory",
        requirement("InquirerPy"),
    ],
)

py_binary(
    name = "default_image_check",
    srcs = ["default_image_check.py"],
    deps = [
        ":debug_pod",
    ],
)

sh_binary(
    name = "build_container",
    srcs = ["build_container.sh"],
    data = glob(["dockerfiles/*"]),
)

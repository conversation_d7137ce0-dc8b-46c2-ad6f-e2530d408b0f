import argparse
import pathlib
import sys
import subprocess

from InquirerPy import inquirer
from InquirerPy.base.control import Choice

from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubectl import KubectlException

DIGEST = "sha256:783394e93401fce0b38066dabb8b36bfc7e0efcdcc2d0971387d8169074c38f3"
DEFAULT_IMAGES = {
    "GCP_US_CENTRAL1_DEV": "us-central1-docker.pkg.dev/system-services-dev/devtools-repository/debug_pod",
    "GCP_US_CENTRAL1_PROD": "us-central1-docker.pkg.dev/system-services-prod/devtools-repository/debug_pod",
    "GCP_EU_WEST4_PROD": "europe-west4-docker.pkg.dev/system-services-prod/devtools-repository-eu-w4/debug_pod",
}


def check_image_manifest(image: str):
    proc = subprocess.run(
        ["docker", "manifest", "inspect", image], capture_output=True, check=False
    )
    return proc.returncode == 0


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("pod_name", type=str)
    parser.add_argument("--image", type=str, default="")
    parser.add_argument(
        "--cloud",
        type=str,
        default="GCP_US_CENTRAL1_DEV",
        choices=DEFAULT_IMAGES.keys(),
    )
    parser.add_argument("--namespace", type=str, default=None)
    parser.add_argument(
        "--kube-config-file",
        default=pathlib.Path.home().joinpath(".kube", "config"),
        type=pathlib.Path,
    )
    args = parser.parse_args()
    if args.image == "":
        args.image = DEFAULT_IMAGES[args.cloud] + "@" + DIGEST
    if not check_image_manifest(args.image):
        print(f"Could not verify existence of image {args.image}")
        return 1

    kubectl_factory = create_kubectl_factory(args.kube_config_file)
    kubectl_instance = kubectl_factory(args.cloud)

    try:
        pod_info = kubectl_instance.get_object("pod", args.pod_name, args.namespace)
    except KubectlException as ex:
        print(f"Failed to get pod {args.pod_name}: {ex.stderr}")
        return 1

    container_names = [c["name"] for c in pod_info["spec"]["containers"]]
    if not container_names:
        print(f"No containers found in pod {args.pod_name}")
        return 1
    elif len(container_names) > 1:
        container = inquirer.select(  # pyright: ignore[reportPrivateImportUsage] https://github.com/kazhala/InquirerPy/issues/48
            message="Select container to debug",
            choices=container_names + [Choice(value=None, name="Exit")],
            default=None,
        ).execute()
        if container is None:
            return 0
    else:
        container = container_names[0]

    # Notes:
    # --target=<container> enables our pod to share the target container's process namespace
    # --profile=general gives access to the target container's filesystem via /proc/pid/root/, as well as capability SYS_PTRACE
    proc = kubectl_instance.debug_pod(
        args.pod_name, container, args.image, args.namespace
    )
    return proc.returncode


if __name__ == "__main__":
    sys.exit(main())

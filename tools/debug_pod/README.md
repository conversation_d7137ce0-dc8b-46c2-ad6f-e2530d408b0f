# Debug running pods using ephemeral containers

This directory contains the code for debugging running pods using Kubernetes' ephemeral containers.
https://kubernetes.io/docs/tasks/debug/debug-application/debug-running-pod/#ephemeral-container
An ephemeral container has advantages over `kubectl exec`-style debugging as we can include more
tools in the debugging container that we wouldn't want to include in production containers, and
in fact some production containers may not even include a shell program.

## Usage
```
bazel run //tools/debug_pod -- <pod_name>
```
An ephemeral container will be created within the given pod, and the user's terminal will attach
to a shell within the new container.
The container shares the process namespace of the target container within the pod. Most service
pods have a single container. If there is more than one named container within the pod, the user
will be prompted for which container to target.

Once attached, user can inspect the target container:
```
debugger-f4gxp$ ps aux
...
# Most service containers run their service entry point as pid 1
debugger-f4gxp$ ps -f -p 1
...
# File system of the target container can be accessed through /proc FS
debugger-f4gxp$ ls /proc/1/root/
...
# Debug target container using gdb
debugger-f4gxp$ ./gdb_helper.sh <additional gdb args>
# Or specify a process id
debugger-f4gxp$ TARGET_PID=N ./gdb_helper.sh <additional gdb args>
```

To copy files out of the container, use `kubectl cp`. The debug container name must be provided.
```
kubectl cp -c debugger-f4gxp <pod>:/path/to/file /local/path
```

Note that creating this container modifies the Pod within kubernetes by adding to
`spec.ephemeralContainers[]`. Detaching from the shell should terminate the ephemeral container.
The state of ephemeral containers can be examined with `kubectl describe pod <pod>`, and those
in the Running state can be attached to using
```
kubectl attach <pod> -c <container> -i -t
```

## Updating debug container

Edit `debug_pod.Dockerfile`, then run the `build_container` target.
```
bazel run //tools/debug_pod:build_container
```
An image will be built and pushed, and its name and digest will be printed.
This can be passed to `debug_pod` via the `--image` flag.
```
bazel run //tools/debug_pod -- <pod_name> --image=name@digest
```
To replace the default image, pass the `--publish-default` flag to `build_container` and
follow instructions to update and check the digests in source, then submit a PR.

## Contributing

Please contribute! Some ideas:
- Other generally useful tools in the container
- Extended gdbinit
- Easier ways to export analysis out of the container. Currently: `kubectl cp`
- Currently the tool only supports in-place debugging, attaching a debug container to a running pod.
  `kubectl debug` has additional options that could be useful: creating a new pod which is a copy of
  the running pod with health checks disabled, and then attaching a debug container to the new pod.

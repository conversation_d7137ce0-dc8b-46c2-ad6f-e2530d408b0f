package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"regexp"
	"slices"
	"time"

	"cloud.google.com/go/bigtable"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

type BackupPolicy struct {
	PeriodHours    uint
	RetentionHours uint
}

type TableGroupBackupPolicy struct {
	TableNameRegex        string
	TableNameExcludeRegex string
	Policies              []BackupPolicy
}

type Config struct {
	Project                  string
	Instance                 string
	ExtraInstances           []string
	TargetClusterId          string
	StartEarlyMinutes        uint
	TableGroupBackupPolicies []TableGroupBackupPolicy
}

type LocalBackupInfo struct {
	bigtable.BackupInfo
}

type BackupInfoInterface interface {
	GetName() string
	GetStartTime() time.Time
	GetExpireTime() time.Time
}

func (b *LocalBackupInfo) GetName() string {
	return b.Name
}

func (b *LocalBackupInfo) GetStartTime() time.Time {
	return b.StartTime
}

func (b *LocalBackupInfo) GetExpireTime() time.Time {
	return b.ExpireTime
}

func sanityCheck(config *Config) error {
	errors := make([]string, 0)

	if config.Project == "" {
		errors = append(errors, "Project must be set")
	}

	if config.Instance == "" {
		errors = append(errors, "Instance must be set")
	}

	if config.TargetClusterId == "" {
		errors = append(errors, "TargetClusterId must be set")
	}

	// Check for valid regex
	for _, tableGroupBackupPolicy := range config.TableGroupBackupPolicies {
		_, err := regexp.Compile(tableGroupBackupPolicy.TableNameRegex)
		if err != nil {
			errors = append(errors, fmt.Sprint("tableGroupBackupPolicy: ", tableGroupBackupPolicy.TableNameRegex, " Error: ", err))
		}
	}
	// Check that the retention time is longer than the period
	for _, tableGroupBackupPolicy := range config.TableGroupBackupPolicies {
		for _, backupPolicy := range tableGroupBackupPolicy.Policies {
			if time.Duration(backupPolicy.PeriodHours)*time.Hour < time.Duration(config.StartEarlyMinutes)*time.Minute {
				errors = append(errors, fmt.Sprint("tableGroupBackupPolicy: ", tableGroupBackupPolicy.TableNameRegex,
					" Period is too short: ", backupPolicy))
			}
			if backupPolicy.RetentionHours < backupPolicy.PeriodHours {
				errors = append(errors, fmt.Sprint("tableGroupBackupPolicy: ", tableGroupBackupPolicy.TableNameRegex,
					" Retention time should be longer than period: ", backupPolicy))
			}
		}
	}
	// Check that the periods are in descending order
	for _, tableGroupBackupPolicy := range config.TableGroupBackupPolicies {
		for i := 0; i < len(tableGroupBackupPolicy.Policies)-1; i++ {
			if tableGroupBackupPolicy.Policies[i].PeriodHours <= tableGroupBackupPolicy.Policies[i+1].PeriodHours {
				errors = append(errors, fmt.Sprint("tableGroupBackupPolicy: ", tableGroupBackupPolicy.TableNameRegex,
					" Periods should be in descending order: ", tableGroupBackupPolicy.Policies))
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors: %s", errors)
	}
	return nil
}

type TableGroupBackupPolicyMap map[string]*TableGroupBackupPolicy

func matchTableGroup(tables []string, tableGroupBackupPolicies []TableGroupBackupPolicy) (TableGroupBackupPolicyMap, error) {
	m := TableGroupBackupPolicyMap{}

	for _, table := range tables {
		for _, tableGroupBackupPolicy := range tableGroupBackupPolicies {
			if tableGroupBackupPolicy.TableNameExcludeRegex != "" {
				if regexp.MustCompile(tableGroupBackupPolicy.TableNameExcludeRegex).MatchString(table) {
					continue
				}
			}
			if regexp.MustCompile(tableGroupBackupPolicy.TableNameRegex).MatchString(table) {
				if _, ok := m[table]; !ok {
					m[table] = &tableGroupBackupPolicy
				} else {
					return nil, fmt.Errorf("tableGroupBackupPolicy: %s should not match multiple tables: %s",
						tableGroupBackupPolicy.TableNameRegex, table)
				}
			}
		}
	}

	return m, nil
}

type BackupTodo struct {
	TableName      string
	RetentionHours uint
}

func calculateBackupTodo(now time.Time, startEarly time.Duration, backupByTable map[string][]BackupInfoInterface, policyByTable TableGroupBackupPolicyMap) []BackupTodo {
	now = now.Add(startEarly)

	// Sort the backup by start time
	for _, v := range backupByTable {
		slices.SortFunc(v, func(a, b BackupInfoInterface) int {
			return a.GetStartTime().Compare(b.GetStartTime())
		})
	}

	todos := []BackupTodo{}

	for table, policies := range policyByTable {
		log.Info().Msgf("Considering table: %s", table)
		backups := backupByTable[table]
		if backups == nil {
			backups = []BackupInfoInterface{}
		}

	NEXT_POLICY:
		for _, policy := range policies.Policies {
			log.Info().Msgf("Considering policy: %v", policy)

			// Look for an existing backup that satisifes the policy
			for _, backup := range backups {
				log.Info().Msgf("Considering backup: %v", backup)

				// Go has us do all the time math using addition rather than subtraction
				startTime := backup.GetStartTime()
				retentionTime := time.Duration(policy.RetentionHours) * time.Hour
				// BigTable calculates expireTime using a timestamp earlier than startTime. So add a bit of wiggle.
				expireTime := backup.GetExpireTime().Add(time.Minute)

				if startTime.Add(retentionTime).After(expireTime) {
					// this backup is too short to be considered a backup with the same policy
					continue
				}

				nextBackup := startTime.Add(time.Duration(policy.PeriodHours) * time.Hour)

				if nextBackup.After(now) {
					// We've done a backup too recently
					continue NEXT_POLICY
				}
			}
			// no current backup satisfies the policy
			log.Info().Msgf("Creating backup todo for table: %s with retention: %d", table, policy.RetentionHours)
			todos = append(todos, BackupTodo{TableName: table, RetentionHours: policy.RetentionHours})
			break
		}
	}

	return todos
}

func main() {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	// Parse arguments
	ctx := context.Background()

	var configFile string
	flag.StringVar(&configFile, "config", "", "Path to config file")

	flag.Parse()

	var config Config
	if configFile == "" {
		log.Fatal().Msg("Missing config file")
		os.Exit(1)
	}

	f, err := os.Open(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
		os.Exit(1)
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()

	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
		os.Exit(1)
	}

	if err := sanityCheck(&config); err != nil {
		log.Fatal().Err(err).Msg("Error in config file")
		os.Exit(1)
	}

	instances := slices.Concat([]string{config.Instance}, config.ExtraInstances)
	clusterIds := slices.Concat([]string{config.TargetClusterId}, config.ExtraInstances)
	errorSeen := false

	for i, instance := range instances {
		// Create a client
		client, err := bigtable.NewAdminClient(ctx, config.Project, instance)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating client")
			os.Exit(1)
		}

		// Connect to Bigtable
		tables, err := client.Tables(ctx)
		if err != nil {
			log.Fatal().Err(err).Msg("Error listing tables")
			os.Exit(1)
		}

		policyByTable, err := matchTableGroup(tables, config.TableGroupBackupPolicies)
		if err != nil {
			log.Fatal().Err(err).Msg("Error matching tables to policies")
			os.Exit(1)
		}

		// Sort the tables
		keys := make([]string, 0, len(policyByTable))
		for k := range policyByTable {
			keys = append(keys, k)
		}
		slices.Sort(keys)
		log.Info().Strs("tables", keys).Msgf("Found %d tables", len(keys))

		backupByTable := map[string][]BackupInfoInterface{}

		// List all the backups
		backupIter := client.Backups(ctx, clusterIds[i])
		for {
			backup, err := backupIter.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				log.Fatal().Err(err).Msg("Error listing backups")
				os.Exit(1)
			}
			log.Info().Msgf("Found backup: %s", backup.Name)
			backupByTable[backup.SourceTable] = append(backupByTable[backup.SourceTable], &LocalBackupInfo{*backup})
		}

		todos := calculateBackupTodo(time.Now(), time.Duration(config.StartEarlyMinutes)*time.Minute, backupByTable, policyByTable)

		for _, todo := range todos {
			log.Info().Msgf("Creating backup for table: %s with retention: %d", todo.TableName, todo.RetentionHours)
			// TODO: backup name probably too long
			backupName := fmt.Sprintf("%s-%s-%d", todo.TableName, time.Now().UTC().Format("0102T1504Z"), todo.RetentionHours)
			expiry := time.Now().Add(time.Duration(todo.RetentionHours) * time.Hour)

			retries := 0
			for {
				err := client.CreateBackup(ctx, todo.TableName, clusterIds[i], backupName, expiry)

				if err == nil {
					break
				}

				retries++
				if retries > 5 {
					log.Error().Err(err).Msgf("Error creating backup for table: %s, giving up", todo.TableName)
					errorSeen = true
					break
				}

				log.Info().Err(err).Msgf("Error creating backup for table: %s, will retry", todo.TableName)

				// Bigtable Backup Metadata Writes are rate limited (10 per minute per user per project)
				time.Sleep(1 * time.Minute)
			}
		}
	}

	log.Info().Msg("Backup exiting")

	if errorSeen {
		os.Exit(1)
	}
}

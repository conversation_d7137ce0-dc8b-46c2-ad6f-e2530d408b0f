local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

{
  deployment: [
    {
      name: 'backup-bigtable',
      kubecfg: {
        target: '//tools/backup/bigtable:kubecfg',
        task: [c for c in cloudInfo.centralNamespaces if c.cloud != 'GCP_US_CENTRAL1_GSC_PROD'] + [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'PROD',
            namespace: 'central-dev',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'backup-bigtable-shared',
      kubecfg: {
        target: '//tools/backup/bigtable:kubecfg_shared',
        task: [
          // We only deploy this to the lead cluster because it creates Google Cloud
          // objects with project scope (e.g. an IAM role).
          {
            // Leader for system-services-prod
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
          {
            // Leader for system-services-dev
            cloud: 'GCP_US_CENTRAL1_DEV',
          },
          // GCP_EU_WEST4_PROD is not a leader.
        ],
      },
    },
    {
      name: 'backup-bigtable-monitoring',
      kubecfg: {
        target: '//tools/backup/bigtable:monitoring_kubecfg',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
    },
  ],
}

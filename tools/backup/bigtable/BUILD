load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")

go_library(
    name = "bigtable_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/backup/bigtable",
    visibility = ["//visibility:private"],
    deps = [
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@org_golang_google_api//iterator",
    ],
)

go_binary(
    name = "bigtable",
    embed = [":bigtable_lib"],
)

go_test(
    name = "bigtable_test",
    srcs = ["main_test.go"],
    embed = [":bigtable_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":bigtable",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_PROD",
        "GCP_US_CENTRAL1_DEV",
    ],
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    visibility = ["//tools/monitoring:__subpackages__"],
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
        ":monitoring_kubecfg",
    ],
)

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

function(cloud)
  local projectId = cloudInfo[cloud].projectId;
  [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMCustomRole',
      metadata: {
        annotations: {
          'cnrm.cloud.google.com/project-id': projectId,
        },
        name: 'bigtable-backup-iam-role',
        namespace: 'security',
      },
      spec: {
        title: 'Bigtable Backup IAM role',
        resourceID: 'augment.bigtable.backup',
        description: 'This role is for creating Bigtable backups',
        permissions: [
          'bigtable.backups.create',
        ],
      },
    },
  ]

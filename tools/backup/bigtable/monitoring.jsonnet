local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local notRunSpec = {
    displayName: 'Bigtable backup not run recently enough',
    conditionPrometheusQueryLanguage: {
      duration: '0s',
      evaluationInterval: '900s',
      labels: { severity: 'warning' },
      // We want backup jobs to start every 6 hours. Alert if one hasn't happened after about 8 hours.
      query: 'absent(max by (cluster) (kube_job_created{namespace="central",job_name=~"backup-bigtable-.*"}) > time() - 28000)',
    },
  };
  local failedSpec = {
    displayName: 'Bigtable backup failed last run',
    conditionPrometheusQueryLanguage: {
      duration: '0s',
      evaluationInterval: '900s',
      labels: { severity: 'warning' },
      // topk gives us the most recent backup
      query: |||
        topk(1, kube_job_created{namespace="central",job_name=~"backup-bigtable-.*"}) *
        on(job_name, cluster) (kube_job_status_failed{namespace="central"} != 0)
      |||,
    },
  };
  local jobDescription = 'Bigtable backup job %s in cluster %s' % [monitoringLib.label('job_name'), monitoringLib.label('cluster')];

  [
    monitoringLib.alertPolicy(cloud, notRunSpec, 'bigtable-backup-not-run', '%s has not run for 8 hours' % jobDescription),
    monitoringLib.alertPolicy(cloud, failedSpec, 'bigtable-backup-failed', '%s has failed' % jobDescription),
  ]

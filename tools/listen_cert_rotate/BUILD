load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "listen_cert_rotate_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/listen_cert_rotate",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "//services/lib/jwtkeyid:jwtkeyid_go",
        "@com_github_micahparks_jwkset//:jwkset",
        "@com_github_micahparks_keyfunc_v3//:keyfunc",
        "@com_github_rs_zerolog//log",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/apis/meta/v1/unstructured",
        "@io_k8s_apimachinery//pkg/runtime",
        "@io_k8s_apimachinery//pkg/runtime/schema",
        "@io_k8s_apimachinery//pkg/util/wait",
        "@io_k8s_apimachinery//pkg/watch",
        "@io_k8s_client_go//dynamic",
        "@io_k8s_client_go//informers",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/cache",
        "@io_k8s_client_go//tools/clientcmd",
        "@io_k8s_client_go//tools/clientcmd/api",
    ],
)

go_test(
    name = "listen_cert_rotate_test",
    srcs = ["main_test.go"],
    data = glob([
        "test_data/*",
    ]),
    embed = [":listen_cert_rotate_lib"],
    deps = [
        "@com_github_golang_jwt_jwt_v5//:jwt",
    ],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":listen_cert_rotate",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
)

go_binary(
    name = "listen_cert_rotate",
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    embed = [":listen_cert_rotate_lib"],
    visibility = ["//visibility:public"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/token_exchange/test:__subpackages__",
        "//tools/listen_cert_rotate/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

"""Integration tests for listen_cert_rotate."""

import time
from contextlib import contextmanager
import json
from typing import Generator
from pathlib import Path
import os

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
import logging


SECRET_NAME = "token-exchange-central-jwt-cert"  # pragma: allowlist secret
CONFIG_MAP_NAME = "token-exchange-central-jwks-configmap"
DIR_PATH = os.path.dirname(os.path.realpath(__file__))


@contextmanager
def listen_cert_rotate_deploy(
    skip_deploy=False,
    skip_deploy_check_teardown=False,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys listen_cert_rotate."""

    cloud = cloud_lib.get_default_cloud()
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        kubecfg_binaries=[
            Path("tools/listen_cert_rotate/test/test_kubecfg.sh"),
        ],
        cloud=cloud,
        skip_deploy_check_teardown=skip_deploy_check_teardown,
    ) as deploy_info:
        yield deploy_info


def create_secret_from_pem_files(
    listen_cert_rotate_deploy: k8s_test_helper.DeployInfo,
    public_key_file,
    private_key_file,
):
    """Create a secret from the given public and private key files."""

    logging.info(f"Creating secret {SECRET_NAME}")

    absolute_public_key_file = os.path.join(DIR_PATH, public_key_file)
    absolute_private_key_file = os.path.join(DIR_PATH, private_key_file)

    create_secret_cmd = [
        "create",
        "secret",
        "tls",
        SECRET_NAME,
        "--namespace",
        listen_cert_rotate_deploy.namespace,
        f"--cert={absolute_public_key_file}",
        f"--key={absolute_private_key_file}",
    ]

    result = listen_cert_rotate_deploy.kubectl.run(args=create_secret_cmd)
    assert result.returncode == 0
    wait_for_secret(listen_cert_rotate_deploy)


def delete_secret(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Delete the secret."""

    logging.info(f"Deleting secret {SECRET_NAME}")
    delete_secret_cmd = [
        "delete",
        "secret",
        SECRET_NAME,
        "--namespace",
        listen_cert_rotate_deploy.namespace,
        "--ignore-not-found",
    ]

    result = listen_cert_rotate_deploy.kubectl.run(args=delete_secret_cmd)
    assert result.returncode == 0
    wait_for_secret_deleted(listen_cert_rotate_deploy)


def has_secret(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Check if the secret exists."""

    proc = listen_cert_rotate_deploy.kubectl.run(
        args=[
            "get",
            "secret",
            "--namespace",
            listen_cert_rotate_deploy.namespace,
        ]
    )
    assert proc.returncode == 0
    return SECRET_NAME in proc.stdout


def wait_for_secret(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Asert that the secret exsists."""

    for _ in range(20):
        time.sleep(1)
        if has_secret(listen_cert_rotate_deploy):
            return
    assert has_secret(listen_cert_rotate_deploy)


def wait_for_secret_deleted(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Assert that the secret does not exist."""

    for _ in range(20):
        time.sleep(1)
        if not has_secret(listen_cert_rotate_deploy):
            return
    assert not has_secret(listen_cert_rotate_deploy)


def delete_configmap(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Delete the configmap, ignoring if it does not exist."""

    logging.info(f"Deleting configmap {CONFIG_MAP_NAME}")
    delete_configmap_cmd = [
        "delete",
        "configmap",
        CONFIG_MAP_NAME,
        "--namespace",
        listen_cert_rotate_deploy.namespace,
        "--ignore-not-found",
    ]
    result = listen_cert_rotate_deploy.kubectl.run(args=delete_configmap_cmd)
    assert result.returncode == 0
    wait_for_configmap_deleted(listen_cert_rotate_deploy)


def has_configmap(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Check if the configmap exists."""

    proc = listen_cert_rotate_deploy.kubectl.run(
        args=[
            "get",
            "configmap",
            "--namespace",
            listen_cert_rotate_deploy.namespace,
        ]
    )
    assert proc.returncode == 0
    return CONFIG_MAP_NAME in proc.stdout


def wait_for_configmap_deleted(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Assert that the configmap does not exist."""

    for _ in range(20):
        time.sleep(1)
        if not has_configmap(listen_cert_rotate_deploy):
            return
    assert not has_configmap(listen_cert_rotate_deploy)


def check_configmap_size(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Get the size of the configmap."""

    logging.info(f"Checking configmap {CONFIG_MAP_NAME}")
    get_configmap_cmd = [
        "get",
        "configmap",
        CONFIG_MAP_NAME,
        "--namespace",
        listen_cert_rotate_deploy.namespace,
        "-o",
        "json",
    ]
    result = listen_cert_rotate_deploy.kubectl.run(args=get_configmap_cmd)
    assert result.returncode == 0
    configmap = json.loads(result.stdout)
    assert "data" in configmap
    data = configmap["data"]
    assert "publicJwks.json" in data
    publicJwks = json.loads(data["publicJwks.json"])
    assert "keys" in publicJwks
    keys = publicJwks["keys"]
    return len(keys)


def delete_certificate(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Delete the Certificate resource."""

    logging.info(f"Deleting Certificate {SECRET_NAME}")
    delete_cert_cmd = [
        "delete",
        "certificate",
        SECRET_NAME,
        "--namespace",
        listen_cert_rotate_deploy.namespace,
        "--ignore-not-found",
    ]

    result = listen_cert_rotate_deploy.kubectl.run(args=delete_cert_cmd)
    assert result.returncode == 0
    wait_for_certificate_deleted(listen_cert_rotate_deploy)


def has_certificate(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Check if the Certificate exists."""

    proc = listen_cert_rotate_deploy.kubectl.run(
        args=[
            "get",
            "certificate",
            SECRET_NAME,
            "--namespace",
            listen_cert_rotate_deploy.namespace,
        ]
    )
    return proc.returncode == 0


def wait_for_certificate_deleted(listen_cert_rotate_deploy: k8s_test_helper.DeployInfo):
    """Assert that the Certificate does not exist."""

    for _ in range(20):
        time.sleep(1)
        if not has_certificate(listen_cert_rotate_deploy):
            return
    assert not has_certificate(listen_cert_rotate_deploy)


def test_listen_cert_rotate_configmap_empty_before():
    """Tests that listen_cert_rotate listens for new secrets: Secrets can be added to the ConfigMap successfully.

    Start with an empty configmap and expect the size of the ConfigMap to increase by one each time a secret is created.
    As two secrets are created, we expect the size of the ConfigMap to be 2.
    """
    k8s_test_helper.print_link_to_logs()

    # Clean up certificate, configmap, and secret from previous tests/dirty environment just in case.
    with listen_cert_rotate_deploy() as lcr_deploy:
        delete_certificate(lcr_deploy)
        delete_configmap(lcr_deploy)
        delete_secret(lcr_deploy)

    # Deployment of listen_cert_rotate is deleted by teardown and recreated to ensure that any in-memory data is cleared from the pod.
    with listen_cert_rotate_deploy() as lcr_deploy:
        # Create secret and check that the ConfigMap has been created and has a size of 1.
        # Delete the secret to check that the ConfigMap is not cleared and so the secret can be recreated.
        create_secret_from_pem_files(
            lcr_deploy,
            "test_data/ec256-cert-public.pem",
            "test_data/ec256-cert-private.pem",
        )
        assert check_configmap_size(lcr_deploy) == 1
        delete_secret(lcr_deploy)
        assert check_configmap_size(lcr_deploy) == 1

        # Create different secret and check that the ConfigMap appends the new secret and has a size of 2.
        # Delete the secret and configmap for cleanup.
        create_secret_from_pem_files(
            lcr_deploy,
            "test_data/ec256-cert-public2.pem",
            "test_data/ec256-cert-private2.pem",
        )
        assert check_configmap_size(lcr_deploy) == 2
        delete_secret(lcr_deploy)
        assert check_configmap_size(lcr_deploy) == 2
        delete_configmap(lcr_deploy)


def test_listen_cert_rotate_configmap_exists_before():
    """Tests that listen_cert_rotate listens for new secrets: Secrets in the ConfigMap are not cleared when the deployment pod is deleted and resumes when the pod is recreated.

    Start with an empty configmap and add a secret, increasing the size of the ConfigMap to one.
    Then delete the deployment pod and expect that the size of the ConfigMap is still one.
    Finally, add another secret and expect the size of the ConfigMap to be two.
    """
    k8s_test_helper.print_link_to_logs()

    # Clean up certificate, configmap, and secret from previous tests/dirty environment just in case.
    with listen_cert_rotate_deploy() as lcr_deploy:
        delete_certificate(lcr_deploy)
        delete_configmap(lcr_deploy)
        delete_secret(lcr_deploy)

    # Deployment of listen_cert_rotate is deleted by teardown and recreated to ensure that any in-memory data is cleared from the pod.
    with listen_cert_rotate_deploy() as lcr_deploy:
        # Create secret and check that the ConfigMap has been created and has a size of 1.
        # Delete the secret to check that the ConfigMap is not cleared and so the secret can be recreated.
        create_secret_from_pem_files(
            lcr_deploy,
            "test_data/ec256-cert-public.pem",
            "test_data/ec256-cert-private.pem",
        )
        assert check_configmap_size(lcr_deploy) == 1
        delete_secret(lcr_deploy)
        assert check_configmap_size(lcr_deploy) == 1

    # Deployment of listen_cert_rotate is deleted by teardown and upon recreation, expect that the secret is still in the ConfigMap.
    with listen_cert_rotate_deploy() as lcr_deploy:
        assert check_configmap_size(lcr_deploy) == 1

        # Create different secret and check that the ConfigMap appends the new secret and has a size of 2.
        # Delete the secret and configmap for cleanup.
        create_secret_from_pem_files(
            lcr_deploy,
            "test_data/ec256-cert-public2.pem",
            "test_data/ec256-cert-private2.pem",
        )
        assert check_configmap_size(lcr_deploy) == 2
        delete_secret(lcr_deploy)
        assert check_configmap_size(lcr_deploy) == 2
        delete_configmap(lcr_deploy)

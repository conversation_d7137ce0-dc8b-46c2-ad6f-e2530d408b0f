load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//tools/listen_cert_rotate:kubecfg",
    ],
)

pytest_test(
    name = "listen_cert_rotate_test",
    size = "large",  # this is an E2E test so, it is large by definition
    srcs = [
        "listen_cert_rotate_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "gcp",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        requirement("kubernetes"),
    ],
)

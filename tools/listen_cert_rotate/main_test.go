package main

import (
	"context"
	"crypto/ecdsa"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"os"
	"testing"

	"github.com/MicahParks/jwkset"
	"github.com/augmentcode/augment/services/lib/jwtkeyid"
	"github.com/golang-jwt/jwt/v5"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func mustReadFile(t *testing.T, path string) []byte {
	t.Helper()
	b, err := os.ReadFile(path)
	if err != nil {
		t.Fatal(err)
	}
	return b
}

func createSecret(t *testing.T, certPath string, keyPath string) (*ecdsa.PrivateKey, *x509.Certificate, *corev1.Secret) {
	certfile := mustReadFile(t, certPath)
	keyfile := mustReadFile(t, keyPath)

	privateKey, err := jwt.ParseECPrivateKeyFromPEM(keyfile)
	if err != nil {
		t.Fatal(err)
	}

	cert, err := tls.X509KeyPair(certfile, keyfile)
	if err != nil {
		t.Fatal(err)
	}

	parsedCert, err := x509.ParseCertificate(cert.Certificate[0])
	if err != nil {
		t.Fatal(err)
	}

	secret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test",
			Namespace: "default",
		},
		Data: map[string][]byte{"tls.crt": pem.EncodeToMemory(&pem.Block{
			Type:  "CERTIFICATE",
			Bytes: cert.Certificate[0],
		})},
	}

	return privateKey, parsedCert, secret
}

type mockConfigMapHandler struct{}

func (m *mockConfigMapHandler) addJsonToConfigMap(publicJwks json.RawMessage) {
	if len(publicJwks) == 0 || string(publicJwks) == "{}" {
		panic("publicJwks cannot be empty")
	}
}

func (m *mockConfigMapHandler) getConfigMapContents() json.RawMessage {
	panic("getConfigMapContents should not be called in this test")
}

func TestSecretToJWK(t *testing.T) {
	// ECDSA 256 secret
	privateKey, parsedCert, secret := createSecret(t, "test_data/ec256-cert-public.pem", "test_data/ec256-cert-private.pem")

	controller := NewController(Config{
		Namespace:       "default",
		CertificateName: "test",
		ConfigMapName:   "test",
	}, nil)

	jwk := controller.secretToJWK(secret)

	marshal := jwk.Marshal()

	marshalCRV := string(marshal.CRV)
	privateKeyCRV := privateKey.Curve.Params().Name
	if marshalCRV != privateKeyCRV {
		t.Fatalf("CRV mismatch - got: %v, want: %v", marshalCRV, privateKeyCRV)
	}

	marshalKid := marshal.KID
	certKid := jwtkeyid.GetKeyIDFromCert(parsedCert)
	if marshalKid != certKid {
		t.Fatalf("KID mismatch - got: %x, want: %x", marshalKid, certKid)
	}

	marshalAlg := marshal.ALG
	wantAlg := jwkset.ALG("ES256")
	if marshalAlg != wantAlg {
		t.Fatalf("ALG mismatch - got: %v, want: %v", marshalAlg, wantAlg)
	}
}

func TestHandleNewSecret(t *testing.T) {
	// ECDSA 256 secrets
	_, parsedCert, secret := createSecret(t, "test_data/ec256-cert-public.pem", "test_data/ec256-cert-private.pem")
	_, parsedCert2, secret2 := createSecret(t, "test_data/ec256-cert-public2.pem", "test_data/ec256-cert-private2.pem")

	controller := NewController(Config{
		Namespace:       "default",
		CertificateName: "test",
		ConfigMapName:   "test",
	}, nil)
	controller.configMapHandler = &mockConfigMapHandler{}
	controller.handleNewSecret(secret)

	storage := controller.storage
	if storage == nil {
		t.Fatal("storage is nil")
	}
	key, err := storage.KeyReadAll(context.Background())
	if err != nil {
		t.Fatalf("Error reading key from storage: %v", err)
	}

	if len(key) != 1 {
		t.Fatalf("len(key) != 1, got: %d", len(key))
	}

	if key[0].Marshal().ALG != jwkset.ALG("ES256") {
		t.Fatalf("secret 1 ALG mismatch - got: %v, want: %v", key[0].Marshal().ALG, jwkset.ALG("ES256"))
	}

	parsedCertKeyID := jwtkeyid.GetKeyIDFromCert(parsedCert)
	if key[0].Marshal().KID != parsedCertKeyID {
		t.Fatalf("secret 1 KID mismatch - got: %x, want: %x", key[0].Marshal().KID, parsedCertKeyID)
	}

	controller.handleNewSecret(secret2)

	key, err = storage.KeyReadAll(context.Background())
	if err != nil {
		t.Fatalf("Error reading key from storage: %v", err)
	}

	if len(key) != 2 {
		t.Fatalf("len(key) != 2, got: %d", len(key))
	}

	if key[1].Marshal().ALG != jwkset.ALG("ES256") {
		t.Fatalf("secret 2 ALG mismatch - got: %v, want: %v", key[1].Marshal().ALG, jwkset.ALG("ES256"))
	}

	parsedCertKeyID2 := jwtkeyid.GetKeyIDFromCert(parsedCert2)
	if key[1].Marshal().KID != parsedCertKeyID2 {
		t.Fatalf("secret 2 KID mismatch - got: %x, want: %x", key[1].Marshal().KID, parsedCertKeyID2)
	}

	controller.handleNewSecret(secret)

	key, err = storage.KeyReadAll(context.Background())
	if err != nil {
		t.Fatalf("Error reading key from storage: %v", err)
	}

	if len(key) != 3 {
		t.Fatalf("len(key) != 3, got: %d", len(key))
	}
}

package main

import (
	"context"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"flag"
	"os"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/MicahParks/jwkset"
	"github.com/MicahParks/keyfunc/v3"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/services/lib/jwtkeyid"
	"github.com/rs/zerolog/log"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/client-go/tools/cache"
)

type Config struct {
	Namespace string
	// Name of the secret we are watching in namespace Namespace.
	CertificateName string
	// Name of the ConfigMap to write to.
	ConfigMapName string
}

type configMapHandler interface {
	addJsonToConfigMap(publicJwks json.RawMessage)
	getConfigMapContents() json.RawMessage
}

type configMapHandlerImpl struct {
	config    Config
	clientset *kubernetes.Clientset
}

type controller struct {
	configMapHandler configMapHandler
	storage          jwkset.Storage
}

func NewController(config Config, clientset *kubernetes.Clientset) *controller {
	return &controller{
		configMapHandler: &configMapHandlerImpl{
			config:    config,
			clientset: clientset,
		},
		storage: jwkset.NewMemoryStorage(),
	}
}

// Get the ConfigMap (may not exist).
func (cmh *configMapHandlerImpl) getConfigMap() (*corev1.ConfigMap, error) {
	cm, err := cmh.clientset.CoreV1().ConfigMaps(cmh.config.Namespace).Get(context.Background(), cmh.config.ConfigMapName, metav1.GetOptions{})
	return cm, err
}

// Create a new ConfigMap with data publicJwks.json.
func (cmh *configMapHandlerImpl) createConfigMap() *corev1.ConfigMap {
	cm, err := cmh.clientset.CoreV1().ConfigMaps(cmh.config.Namespace).Create(context.Background(), &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name: cmh.config.ConfigMapName,
		},
		Data: map[string]string{
			"publicJwks.json": "{}",
		},
	}, metav1.CreateOptions{})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating ConfigMap")
	}
	return cm
}

// Get the ConfigMap if it exists, otherwise create and return it.
func (cmh *configMapHandlerImpl) getOrCreateConfigMap() *corev1.ConfigMap {
	cm, err := cmh.getConfigMap()
	if err != nil {
		if errors.IsNotFound(err) {
			return cmh.createConfigMap()
		} else {
			log.Fatal().Err(err).Msg("Error getting ConfigMap")
		}
	}
	return cm
}

// Add a json consisting of all the public JWKs to the ConfigMap.
func (cmh *configMapHandlerImpl) addJsonToConfigMap(publicJwks json.RawMessage) {
	cm := cmh.getOrCreateConfigMap()
	cm.Data["publicJwks.json"] = string(publicJwks)
	_, err := cmh.clientset.CoreV1().ConfigMaps(cmh.config.Namespace).Update(context.Background(), cm, metav1.UpdateOptions{})
	if err != nil {
		log.Fatal().Err(err).Msg("Error updating ConfigMap")
	}
}

// Get the contents of the ConfigMap if it exists. If it does not exist, return empty dict.
func (cmh *configMapHandlerImpl) getConfigMapContents() json.RawMessage {
	cm, err := cmh.getConfigMap()

	namespace := cmh.config.Namespace
	configMapName := cmh.config.ConfigMapName

	// Check if ConfigMap does not exist or if some other error getting ConfigMap.
	if err != nil {
		if errors.IsNotFound(err) {
			log.Info().Msgf("ConfigMap %s/%s does not exist, storage initialized to empty", namespace, configMapName)
			return json.RawMessage("{}")
		} else {
			// Error getting ConfigMap that is not NotFound Error.
			log.Fatal().Err(err).Msg("Error getting ConfigMap")
		}
	} else {
		log.Info().Msgf("ConfigMap %s/%s exists, adding existing contents to storage", namespace, configMapName)

		// Check if publicJwks.json exists in the ConfigMap and error if not.
		if _, ok := cm.Data["publicJwks.json"]; !ok {
			log.Fatal().Msgf("ConfigMap %s/%s does not contain publicJwks.json", namespace, configMapName)
		}

		// Return the contents of publicJwks.json as json.RawMessage.
		return json.RawMessage([]byte(cm.Data["publicJwks.json"]))
	}
	return json.RawMessage("{}")
}

// Convert the secret to a JWK.
func (c *controller) secretToJWK(secret *corev1.Secret) *jwkset.JWK {
	certBytes, ok := secret.Data["tls.crt"]
	if !ok {
		log.Fatal().Msg("Error getting certificate from secret")
		return nil
	}

	block, _ := pem.Decode(certBytes)
	if block == nil || block.Type != "CERTIFICATE" {
		log.Fatal().Msg("Error decoding certificate")
		return nil
	}

	x509Cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		log.Fatal().Err(err).Msg("Error parsing certificate")
		return nil
	}

	keyID := jwtkeyid.GetKeyIDFromCert(x509Cert)
	publicKey := x509Cert.PublicKey

	options := jwkset.JWKOptions{
		Metadata: jwkset.JWKMetadataOptions{
			ALG: jwkset.ALG("ES256"),
			KID: keyID,
		},
	}

	jwk, err := jwkset.NewJWKFromKey(publicKey, options)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating JWK from key")
		return nil
	}

	return &jwk
}

// Handle a new secret.
func (c *controller) handleNewSecret(secret *corev1.Secret) {
	log.Info().Msgf("Handling new secret (%s/%s) with resource version: %s", secret.Namespace, secret.Name, secret.ResourceVersion)

	// Convert the secret to a JWK.
	jwk := c.secretToJWK(secret)
	if jwk == nil {
		log.Fatal().Msg("Error converting secret to JWK, skipping secret")
		return
	}

	// Write the JWK to the storage.
	err := c.storage.KeyWrite(context.Background(), *jwk)
	if err != nil {
		log.Fatal().Err(err).Msg("Error writing JWK to storage, skipping secret")
		return
	}

	// Get all the JWKs from the storage.
	publicJwks, err := c.storage.JSONPublic(context.Background())
	if err != nil {
		log.Fatal().Err(err).Msg("Error getting JWKs from storage, skipping secret")
		return
	}

	// Get the current size of the storage (for logging).
	// Note that ConfigMap may contain duplicate keys due to KID encoding differences.
	// Likely limited to one duplicate key (secret in-use on startup as it is represented in both formats).
	// On startup, get ConfigMap content implementation converts KID to unrepresentable format with "?". New and current keys have KID represented as "\ufffd".
	key, read_err := c.storage.KeyReadAll(context.Background())
	if read_err == nil {
		log.Info().Msgf("Storage size after adding new JWK: %d", len(key))
	}

	// Update the ConfigMap with the new public JWKs.
	c.configMapHandler.addJsonToConfigMap(publicJwks)
}

func main() {
	logging.SetupServerLogging()

	var configFilePath string
	flag.StringVar(&configFilePath, "config", "", "Path to config file")
	flag.Parse()
	log.Info().Msgf("Config file: %s", configFilePath)

	kubeconfig, err := clientcmd.BuildConfigFromFlags("", "")
	if err != nil {
		log.Fatal().Err(err).Msg("Error building kubeconfig")
		os.Exit(1)
	}

	// Create Kubernetes client.
	clientset, err := kubernetes.NewForConfig(kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating Kubernetes client")
		os.Exit(1)
	}

	// Check if config file exists and decode it.
	var config Config
	if configFilePath == "" {
		log.Fatal().Msg("Missing config file")
		os.Exit(1)
	}

	f, err := os.Open(configFilePath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
		os.Exit(1)
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()

	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
		os.Exit(1)
	}
	log.Info().Msgf("Config: %s", config)

	log.Info().Msgf("Config namespace: %s", config.Namespace)
	log.Info().Msgf("Config certificatenName: %s", config.CertificateName)
	log.Info().Msgf("Config ConfigMapName: %s", config.ConfigMapName)

	// Create the controller.
	controller := NewController(config, clientset)

	// Add ConfigMap contents to the storage if ConfigMap exists.
	publicJwks := controller.configMapHandler.getConfigMapContents()
	keyfunc, err := keyfunc.NewJWKSetJSON(publicJwks)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error parsing JWKs from ConfigMap %s/%s", config.Namespace, config.ConfigMapName)
		os.Exit(1)
	}
	controller.storage = keyfunc.Storage()

	// Get the current size of the storage (for logging).
	key, read_err := controller.storage.KeyReadAll(context.Background())
	if read_err == nil {
		log.Info().Msgf("Storage size after initialization: %d", len(key))
	}

	// Create shared informer factory for the namespace and get a secret informer.
	factory := informers.NewSharedInformerFactoryWithOptions(clientset, 0, informers.WithNamespace(config.Namespace))
	secretInformer := factory.Core().V1().Secrets().Informer()

	cancel := make(chan struct{})
	defer close(cancel)

	secretInformer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		UpdateFunc: func(old, new interface{}) {
			newSecret := new.(*corev1.Secret)
			oldSecret := old.(*corev1.Secret)

			// Check if the updated secret is the correct secret name and has a new version.
			if newSecret.Name == config.CertificateName && oldSecret.ResourceVersion != newSecret.ResourceVersion {
				log.Info().Msgf("Secret updated: %s/%s/%s", newSecret.Namespace, newSecret.Name, newSecret.ResourceVersion)
				controller.handleNewSecret(newSecret)
			}
		},
		AddFunc: func(obj interface{}) {
			secret := obj.(*corev1.Secret)

			// Check if the added secret is the correct secret name.
			if secret.Name == config.CertificateName {
				log.Info().Msgf("Secret added: %s/%s/%s", secret.Namespace, secret.Name, secret.ResourceVersion)
				controller.handleNewSecret(secret)
			}
		},
	})

	secretInformer.Run(cancel)
}

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'listen-cert-rotate',
      kubecfg: {
        target: '//tools/listen_cert_rotate:kubecfg',
        task: [c for c in cloudInfo.centralNamespaces if c.cloud != 'GCP_US_CENTRAL1_GSC_PROD'],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['aswin', 'pranay'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local appName = 'listen-cert-rotate';

  local config = {
    namespace: namespace,  // Namespace to watch for the secret.
    certificateName: 'token-exchange-central-jwt-cert',  // Name of the secret we are watching.
    configMapName: 'token-exchange-central-jwks-configmap',  // Name of the ConfigMap we are writing to.
  };

  local shortAppName = 'lis-cert-rotate';
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    namespace=namespace,
    cloud=cloud,
    overridePrefix=shortAppName,
  );

  local roles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: 'listen-cert-rotate-role-binding',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: serviceAccount.name,
        },
      ],
      roleRef: {
        kind: 'Role',
        name: 'listen-cert-rotate-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        name: 'listen-cert-rotate-role',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      rules: [
        {
          apiGroups: [''],
          resources: ['secrets'],
          verbs: ['get', 'list', 'watch'],
        },
        {
          apiGroups: [''],
          resources: ['configmaps'],
          verbs: ['get', 'list', 'watch', 'update', 'create'],
        },
      ],
    },
  ];

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container =
    {
      name: 'listen-cert-rotate',
      target: {
        name: '//tools/listen_cert_rotate:image',
        dst: 'listen-cert-rotate-image',
      },
      volumeMounts: [
        configMap.volumeMountDef,
      ],
      args: [
        '-config',
        configMap.filename,
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      resources: {
        limits: {
          cpu: '0.1',
          memory: '1Gi',
        },
      },
    };
  local pod =
    {
      containers: [
        container,
      ],
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: serviceAccount.name,
      volumes: [
        configMap.podVolumeDef,
      ],
    };
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    serviceAccount.objects,
    roles,
    configMap.objects,
    deployment,
  ])

import pathlib


def test_requirement_lock():
    """Check that the requirements.lock file does not have the vulnerable pattern defined in CVE-2024-6345.

    For various dependencies, we at this point cannot upgrade from unsafe setuptools version 69 to 70+.
    To prevent the vulnerability from being introduced, we check that the requirements.lock file does not
    contain the unsafe pattern.

    see https://huntr.com/bounties/d6362117-ad57-4e83-951f-b8141c6e7ca5 for more details.
    """

    contents = pathlib.Path("tools/python_deps/requirements.lock").read_text()
    for line in contents.splitlines():
        # disallow git dependencies
        assert not line.startswith("git://")

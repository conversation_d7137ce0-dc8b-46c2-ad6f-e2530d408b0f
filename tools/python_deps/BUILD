load(
    "@aspect_rules_py//py:defs.bzl",
    aspect_py_binary = "py_binary",
)
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_python//python:pip.bzl", "compile_pip_requirements")
load("//tools/bzl:trivy.bzl", "trivy_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

# call bazel run //tools/python_deps:requirements.update to update the requirements.lock file
# add "--upgrade" for a full upgrade of dependencies
compile_pip_requirements(
    name = "requirements",
    timeout = "eternal",
    extra_args = [
        #"--upgrade",
        "--resolver=backtracking",
        "--allow-unsafe",
        "--extra-index-url=https://us-central1-python.pkg.dev/system-services-dev/pypi-public/simple",
        "--extra-index-url=https://download.pytorch.org/whl/cu124",
    ],
    requirements_in = "requirements.txt",
    requirements_txt = "requirements.lock",
)

pytest_test(
    name = "torch_version_test",
    srcs = ["torch_version_test.py"],
    deps = [
        requirement("torch"),
    ],
)

pytest_test(
    name = "check_lock_file_test",
    srcs = ["check_lock_file.py"],
    data = [
        ":requirements.lock",
    ],
)

trivy_test(
    name = "requirements.trivy_test",
    src = "requirements.txt",
    mode = "fs",
    tags = [
        "lint",
        "no-cache",
        "postmerge-test",
    ],
)

aspect_py_binary(
    name = "record_hash",
    srcs = ["record_hash.py"],
)

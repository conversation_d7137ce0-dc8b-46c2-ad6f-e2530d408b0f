"""Record the hash of a file used in the RECORD file of Python wheels."""
#!/usr/bin/env python3

import base64
import hashlib
import os
import pathlib
import sys


def main():
    filename = sys.argv[1]

    with pathlib.Path(filename).open("rb") as f:
        digest = hashlib.sha256(f.read())
        safe_hash = (
            base64.urlsafe_b64encode(digest.digest()).decode("us-ascii").rstrip("=")
        )
    print(f"{filename},sha256={safe_hash},{os.path.getsize(filename)}")


if __name__ == "__main__":
    main()

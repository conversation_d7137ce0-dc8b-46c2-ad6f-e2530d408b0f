#
# This file is autogenerated by pip-compile with Python 3.9
# by the following command:
#
#    pip-compile --output-file=black_requirements.lock black_requirements.txt
#
black==22.12.0
    # via -r black_requirements.txt
click==8.1.3
    # via
    #   -r black_requirements.txt
    #   black
mypy-extensions==0.4.3
    # via
    #   -r black_requirements.txt
    #   black
pathspec==0.10.3
    # via
    #   -r black_requirements.txt
    #   black
platformdirs==2.6.0
    # via
    #   -r black_requirements.txt
    #   black
tomli==2.0.1
    # via
    #   -r black_requirements.txt
    #   black
typing-extensions==4.4.0
    # via
    #   -r black_requirements.txt
    #   black

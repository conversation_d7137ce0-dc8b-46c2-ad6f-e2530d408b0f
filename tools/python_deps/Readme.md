# Python Dependencies

Python code often depends on external packages ("pip packages").
This directory contains the tooling to make external packages to Python code managed by <PERSON><PERSON>.

## Add a Python dependency

- Add or update requirement to `requirements.txt`
- Run `bazel run //tools/python_deps:requirements.update`

In most situations, this should be everything.

## Use a Python dependency

To use a Python dependency in a binary, library or test, use the `requirement` macro.

```
load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "mylib",
    srcs = ["mylib.py"],
    deps = [
        requirement("docker"),
    ],
)
```

## Upload a package

To ensure hermeticism, packages that compile during setup time are not allowed.
These setup steps would run outside the sandbox provided by <PERSON><PERSON> and might not be reproducible on other machines.

These packages need to be installed separately and uploaded to the pypi server.

In a controlled environment (i.e. Python3.11 with minimal packages installed):

```
pip wheel [PACKAGE]==[VERSION]
twine upload [PATH TO WHEEL] --repository-url https://us-central1-python.pkg.dev/system-services-dev/pypi-public"
```

Uploading packages require authentication to the system-services-dev project in GCP.
Ask somebody to check for you.

The pypi server is accessible from AWS, GCP, and CW and allows download without authentication.
Under no circumstances, upload augment IP to that server.

## Notes

###  xgboost==2.1.0.dev2

The official version of xgboost on PyPI includes CUDA and OpenMP dependencies and is about 400MB large, which is larger than we'd like to include in our production images. We created a fork of the xgboost repository and created custom build of the package without these dependencies. Steps to reproduce:

1. `<NAME_EMAIL>:augmentcode/xgboost`
2. Build the python wheel: `cd python-package && python3 -m build`
3. Upload to PyPI: `twine upload --repository-url https://us-central1-python.pkg.dev/system-services-dev/pypi-public/ dist/* --verbose`

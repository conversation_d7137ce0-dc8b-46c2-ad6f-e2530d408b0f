//go:build tools
// +build tools

package tools

import (
	_ "github.com/GoogleCloudPlatform/docker-credential-gcr/v2"
	_ "github.com/bitnami-labs/sealed-secrets/cmd/kubeseal"
	_ "github.com/ewhauser/bazel-differ/cli"
	_ "github.com/google/go-containerregistry/cmd/crane"
	_ "github.com/grpc-ecosystem/grpc-health-probe"
	_ "github.com/grpc/grpc"
	_ "github.com/webdevops/pagerduty-exporter"
	_ "github.com/yannh/kubeconform/cmd/kubeconform"
)

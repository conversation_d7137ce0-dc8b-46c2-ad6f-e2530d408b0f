local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
function(cloud)
  local roles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'cloud-sync-role',
        labels: {
          app: 'cloud-sync',
        },
      },
      rules: [
        {
          apiGroups: [
            '',
          ],
          resources: [
            'secrets',
          ],
          verbs: [
            'get',
            'watch',
            'list',
            'create',
            'update',
            'delete',
          ],
        },
      ],
    },
  ];
  // give the GCP_US_CENTRAL1_PROD cloud-sync SA access to all clusters
  local roleBindings = if cloudInfo.isProdCluster(cloud) && cloud != 'GCP_US_CENTRAL1_PROD' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'cloud-sync-cross-cloud-role-binding',
        labels: {
          app: 'cloud-sync',
        },
      },
      subjects: [
        // always give the cloud-sync service account of the prod project permission to sync
        {
          kind: 'User',
          name: '<EMAIL>',
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'cloud-sync-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ] else [
  ];
  lib.flatten([roles, roleBindings])

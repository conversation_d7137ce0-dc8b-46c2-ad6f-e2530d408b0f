package main

import (
	"testing"
)

func passingConfig() *Config {
	return &Config{
		Policies: []SyncPolicy{
			{
				SourceContext:   "foo",
				SourceNamespace: "bar",
				TargetContext:   "baz",
				TargetNamespace: "qux",
				Kind:            "Secret",
			},
		},
	}
}

func TestSanityCheckHappyPath(t *testing.T) {
	config := passingConfig()

	err := sanityCheck(config)
	if err != nil {
		t.<PERSON><PERSON>("Expected no error")
	}

	config.Policies[0].SourceName = "from"
	config.Policies[0].TargetName = "to"

	err = sanityCheck(config)
	if err != nil {
		t.<PERSON>al("Expected no error")
	}
}

func TestSanityCheckSourceContext(t *testing.T) {
	config := passingConfig()
	config.Policies[0].SourceContext = ""

	err := sanityCheck(config)
	if err == nil {
		t.Fatal("Expected error")
	}
}

func TestSanityCheckTargetName(t *testing.T) {
	config := passingConfig()
	config.Policies[0].TargetName = "everythinghere"

	err := sanityCheck(config)
	if err == nil {
		t.Fatal("Expected error")
	}
}

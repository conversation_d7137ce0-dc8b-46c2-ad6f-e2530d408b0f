{
  deployment: [
    {
      name: 'cloud-sync',
      kubecfg: {
        target: '//tools/cloud_sync:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'aswin'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'cloud-sync-shared',
      kubecfg: {
        target: '//tools/cloud_sync:kubecfg_shared',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
    },
  ],
}

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local appName = 'cloud-sync';

  local config = {
    Policies: if env == 'DEV' then lib.flatten([
      {
        SourceContext: cloudInfo.GCP_US_CENTRAL1_DEV.context,
        SourceNamespace: namespace,
        SourceName: 'token-exchange-central-jwt-cert',
        TargetContext: cloudInfo.GCP_US_CENTRAL1_DEV.context,
        TargetNamespace: namespace,
        TargetName: 'token-exchange-central-extra-jwt-cert',
        Kind: 'Secret',
      },
    ] + [
      // Add a for loop just to test that it works, since we use one in the prod
      // policies too
      [
        {
          SourceContext: cloudInfo.GCP_US_CENTRAL1_DEV.context,
          SourceNamespace: namespace,
          SourceName: 'token-exchange-central-jwt-cert',
          TargetContext: cloudInfo.GCP_US_CENTRAL1_DEV.context,
          TargetNamespace: namespace,
          TargetName: 'token-exchange-central-extra-%d-jwt-cert' % i,
          Kind: 'Secret',
        },
      ]
      for i in std.range(0, 1)
    ]) else
      assert cloud == 'GCP_US_CENTRAL1_PROD';
      lib.flatten([
        // Copy the root certificate from US to EU, so US is our source of truth
        {
          SourceContext: cloudInfo.GCP_US_CENTRAL1_PROD.context,
          SourceNamespace: 'cert-manager',
          TargetContext: cloudInfo.GCP_EU_WEST4_PROD.context,
          TargetNamespace: 'cert-manager',
          Kind: 'Secret',
        },
        // Copy the root certificate from US to US GSC, so US is our source of truth
        {
          SourceContext: cloudInfo.GCP_US_CENTRAL1_PROD.context,
          SourceNamespace: 'cert-manager',
          TargetContext: cloudInfo.GCP_US_CENTRAL1_GSC_PROD.context,
          TargetNamespace: 'cert-manager',
          Kind: 'Secret',
        },
      ] + [
        [
          // Copy the jwt certs between clusters, to allow global services to work
          // across clusters.
          // TODO: Ideally we would use the same token across all clusters, but we
          // need key rotation working on the token exchange server first for that
          // to work
          {
            SourceContext: cloudInfo.GCP_US_CENTRAL1_PROD.context,
            SourceNamespace: namespace,
            SourceName: 'token-exchange-central-jwt-cert',
            TargetContext: cloudInfo.GCP_EU_WEST4_PROD.context,
            TargetNamespace: namespace,
            // Note that we use a different name to avoid overwriting the US cert
            TargetName: 'token-exchange-central-extra-jwt-cert',
            Kind: 'Secret',
          },
          {
            SourceContext: cloudInfo.GCP_US_CENTRAL1_PROD.context,
            SourceNamespace: namespace,
            SourceName: 'token-exchange-central-jwt-cert',
            TargetContext: cloudInfo.GCP_US_CENTRAL1_GSC_PROD.context,
            TargetNamespace: namespace,
            TargetName: 'token-exchange-central-%s-cert' % cloudInfo.GCP_US_CENTRAL1_PROD.shortName,
            Kind: 'Secret',
          },
          {
            SourceContext: cloudInfo.GCP_EU_WEST4_PROD.context,
            SourceNamespace: namespace,
            SourceName: 'token-exchange-central-jwt-cert',
            TargetContext: cloudInfo.GCP_US_CENTRAL1_PROD.context,
            TargetNamespace: namespace,
            // Note that we use a different name to avoid overwriting the EU cert
            TargetName: 'token-exchange-central-extra-jwt-cert',
            Kind: 'Secret',
          },
          {
            SourceContext: cloudInfo.GCP_EU_WEST4_PROD.context,
            SourceNamespace: namespace,
            SourceName: 'token-exchange-central-jwt-cert',
            TargetContext: cloudInfo.GCP_US_CENTRAL1_GSC_PROD.context,
            TargetNamespace: namespace,
            TargetName: 'token-exchange-central-%s-cert' % cloudInfo.GCP_EU_WEST4_PROD.shortName,
            Kind: 'Secret',
          },
          {
            SourceContext: cloudInfo.GCP_US_CENTRAL1_GSC_PROD.context,
            SourceNamespace: namespace,
            SourceName: 'token-exchange-central-jwt-cert',
            TargetContext: cloudInfo.GCP_US_CENTRAL1_PROD.context,
            TargetNamespace: namespace,
            TargetName: 'token-exchange-central-%s-cert' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,
            Kind: 'Secret',
          },
          {
            SourceContext: cloudInfo.GCP_US_CENTRAL1_GSC_PROD.context,
            SourceNamespace: namespace,
            SourceName: 'token-exchange-central-jwt-cert',
            TargetContext: cloudInfo.GCP_EU_WEST4_PROD.context,
            TargetNamespace: namespace,
            TargetName: 'token-exchange-central-%s-cert' % cloudInfo.GCP_US_CENTRAL1_GSC_PROD.shortName,
            Kind: 'Secret',
          },
        ]
        for namespace in std.set([cn.namespace for cn in cloudInfo.centralNamespaces if cn.cloud != 'GCP_US_CENTRAL1_GSC_PROD'])
      ]),
  };

  // create iam account for cross-cluster permissions
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=true
  );
  local roleBindings = if env == 'DEV' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        name: '%s-cloud-sync-role-binding' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: serviceAccount.name,
        },
        {
          kind: 'User',
          name: serviceAccount.serviceAccountGcpEmailAddress,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'cloud-sync-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ] else [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: '%s-cloud-sync-role-binding' % namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          namespace: namespace,
          name: serviceAccount.name,
        },
        {
          kind: 'User',
          name: serviceAccount.serviceAccountGcpEmailAddress,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'cloud-sync-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ];

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container =
    {
      name: 'cloud-sync',
      target: {
        name: '//tools/cloud_sync:image',
        dst: 'cloud-sync-image',
      },
      volumeMounts: [
        configMap.volumeMountDef,
      ],
      args: [
        '-config',
        configMap.filename,
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      resources: {
        limits: {
          cpu: '0.1',
          memory: '1Gi',
        },
      },
    };
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      containers: [
        container,
      ],
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: serviceAccount.name,
      volumes: [
        configMap.podVolumeDef,
      ],
    };
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    serviceAccount.objects,
    roleBindings,
    configMap.objects,
    deployment,
  ])

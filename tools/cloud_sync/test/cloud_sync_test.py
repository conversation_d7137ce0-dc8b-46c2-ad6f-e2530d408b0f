"""Integration test for cloud_sync."""

import time
from contextlib import contextmanager

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper

# Based off of deploy.jsonnet
EXPECTED_COPIES = [
    "token-exchange-central-extra-jwt-cert",
    "token-exchange-central-extra-0-jwt-cert",
    "token-exchange-central-extra-1-jwt-cert",
]


def get_synced_secrets(cloud_sync_deploy: k8s_test_helper.DeployInfo):
    """Get the list of synced secrets that exist in k8s."""
    print(f"namespace: {cloud_sync_deploy.namespace}")
    proc = cloud_sync_deploy.kubectl.run(
        args=[
            "get",
            "secret",
            "--namespace",
            cloud_sync_deploy.namespace,
            "--selector",
            "eng.augmentcode.com/cloud-synced=true",
        ]
    )
    print(proc.stdout)
    assert proc.returncode == 0
    found = [c for c in EXPECTED_COPIES if c in proc.stdout]
    print(f"Found secrets {found}")
    return found


def delete_synced_secrets(cloud_sync_deploy: k8s_test_helper.DeployInfo):
    """Delete any extra JWT secrets that might be present."""
    for name in get_synced_secrets(cloud_sync_deploy):
        print(f"Deleting secret {name}")
        cloud_sync_deploy.kubectl.run(
            args=[
                "delete",
                "secret",
                "--namespace",
                cloud_sync_deploy.namespace,
                name,
            ]
        )


@contextmanager
def new_jwt_secret(cloud_sync_deploy: k8s_test_helper.DeployInfo):
    """Create a new JWT secret, cleaning it and any copies up afterwards.

    The secret names are copied from the deploy.jsonnet file.
    """
    name = "token-exchange-central-jwt-cert"
    data = {
        "apiVersion": "v1",
        "kind": "Secret",
        "metadata": {
            "name": name,
            "namespace": cloud_sync_deploy.namespace,
            "labels": {
                "eng.augmentcode.com/cloud-sync": "true",
            },
        },
    }

    # Cleanup anything that may be leftover from a previous test, just in case.
    # We want cloud-sync to see the secret for the first time now so it tries to
    # sync it again during this test
    cloud_sync_deploy.kubectl.delete(data, ignore_not_found=True)
    delete_synced_secrets(cloud_sync_deploy)
    for _ in range(20):
        time.sleep(1)
        if len(get_synced_secrets(cloud_sync_deploy)) == 0:
            break
        # In case cloud-sync is still copying the secret, delete it again
        delete_synced_secrets(cloud_sync_deploy)
    assert len(get_synced_secrets(cloud_sync_deploy)) == 0

    # Create the secret
    print(f"Creating secret {name}")
    cloud_sync_deploy.kubectl.apply(data)
    yield name

    # Cleanup
    delete_synced_secrets(cloud_sync_deploy)
    print(f"Deleting secret {name}")
    cloud_sync_deploy.kubectl.delete(data)


def test_cloud_sync_basic(cloud_sync_deploy: k8s_test_helper.DeployInfo):
    """Tests that cloud sync copies secrets as expected."""
    with new_jwt_secret(cloud_sync_deploy):
        for _ in range(10):
            time.sleep(5)
            synced = get_synced_secrets(cloud_sync_deploy)
            if len(synced) == len(EXPECTED_COPIES):
                break

        assert len(synced) == len(EXPECTED_COPIES)

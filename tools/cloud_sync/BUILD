load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "cloud_sync_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/cloud_sync",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "@com_github_rs_zerolog//log",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/util/wait",
        "@io_k8s_client_go//informers",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/cache",
        "@io_k8s_client_go//tools/clientcmd",
        "@io_k8s_client_go//tools/clientcmd/api",
    ],
)

go_test(
    name = "cloud_sync_test",
    srcs = ["main_test.go"],
    embed = [":cloud_sync_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":cloud_sync",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
)

go_binary(
    name = "cloud_sync",
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    embed = [":cloud_sync_lib"],
    visibility = ["//visibility:public"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//tools/cloud_sync/test:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
    ],
)

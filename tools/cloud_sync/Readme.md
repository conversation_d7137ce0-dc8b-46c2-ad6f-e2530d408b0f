# Cloud Sync

## Problem

We need shared information between clusters, e.g. we need
the root-certificate for cross-cluster communication to be synced
between GCP_US_CENTRAL1_PROD and GCP_EU_WEST4_PROD.

## Solution

The cloud-sync tool can run in lead cluster and sync Kubernetes
objects between clusters.

The program listens to the objects in a namespace that are denoted with
the flag `eng.augmentcode.com/cloud-synced: true` and copies the secrets into a target namespace.

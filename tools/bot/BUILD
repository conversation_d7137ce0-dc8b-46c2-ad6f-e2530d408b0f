load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "bot_proto",
    srcs = ["bot.proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//base/proto/services:access_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "bot_py_proto",
    output_mode = "NO_PREFIX_FLAT",
    protos = [":bot_proto"],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = ["//base/proto/services:access_py_proto"],
)

py_library(
    name = "bot_client",
    srcs = [
        "bot_client.py",
    ],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":bot_py_proto",
        "//base/python/grpc:client_options",
        requirement("grpcio"),
        requirement("protobuf"),
        requirement("certifi"),
    ],
)

py_binary(
    name = "bot_server",
    srcs = [
        "bot_server.py",
        "config.py",
    ],
    main = "bot_server.py",
    deps = [
        ":bot_py_proto",
        "//base/logging:struct_logging",
        "//base/proto/services:access_py_proto",
        requirement("dataclasses_json"),
        requirement("slack_sdk"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("certifi"),
        requirement("prometheus_client"),
    ],
)

pytest_test(
    name = "bot_server_test",
    srcs = ["bot_server_test.py"],
    deps = [
        ":bot_server",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":bot_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//tools/genie/test:__subpackages__",
    ],
    deps = [
        ":slackbot_token_lib",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
    ],
)

kubecfg_library(
    name = "slackbot_token_lib",
    srcs = ["slackbot_token_sealed.jsonnet"],
    visibility = ["//tools:__subpackages__"],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

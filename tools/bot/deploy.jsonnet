// K8S deployment file for the slack bot
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'slack-bot-svc',
      namespace: namespace,
      labels: {
        app: 'slack-bot',
        'eng.augmentcode.com/service-type': 'grpc',
      },
    },
    spec: {
      selector: {
        app: 'slack-bot',
      },
      ports: [
        {
          protocol: 'TCP',
          port: 80,
          targetPort: 'grpc-svc',
        },
      ],
    },
  };
  local config =
    // C04TAB1KT71 == channel cicd-notifications https://augment-wic8570.slack.com/archives/C04TAB1KT71
    // C06826B6ZB8 == system-services-oncall
    // C06AFD46C4V == channel test-only
    local cicd_channel_id = if namespace == 'devtools' then 'C04TAB1KT71' else 'C06AFD46C4V';
    local oncall_channel_id = if namespace == 'devtools' then 'C06826B6ZB8' else 'C06AFD46C4V';
    {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: 'slack-bot-config',
        namespace: namespace,
        labels: {
          app: 'slack-bot',
        },
      },
      data: {
        'config.json': std.manifestJson({
          cicd_channel_id: cicd_channel_id,
          oncall_channel_id: oncall_channel_id,
        }),
      },
    };
  local container =
    {
      name: 'bot',
      target: {
        name: '//tools/bot:image',
        dst: 'devtools_slack_bot',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      env: [
        {
          name: 'SLACK_BOT_TOKEN',
          valueFrom: {
            secretKeyRef: {
              name: 'slack-bot-token',
              key: 'slack_token',
            },
          },
        },
      ],
      readinessProbe: {
        tcpSocket: {
          port: 50051,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        tcpSocket: {
          port: 50051,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '100Mi',
        },
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName='slack-bot');
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      containers: [
        container,
      ],
      volumes: [
        {
          name: 'config',
          configMap: {
            name: 'slack-bot-config',
            items: [
              {
                key: 'config.json',
                path: 'config.json',
              },
            ],
          },
        },
      ],
    };
  local slackbot_token_sealed = import 'tools/bot/slackbot_token_sealed.jsonnet';
  local deployment =
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'slack-bot',
        namespace: namespace,
        labels: {
          app: 'slack-bot',
        },
      },
      spec: {
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: 'slack-bot',
          },
        },
        template: {
          metadata: {
            labels: {
              app: 'slack-bot',
            },
          },
          spec: pod,
        },
      },
    };
  [
    config,
    service,
    deployment,
    slackbot_token_sealed(cloud, namespace, appName='slack-bot'),
  ]

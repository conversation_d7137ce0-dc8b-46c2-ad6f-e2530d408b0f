import pytest
from unittest.mock import Mock

from tools.bot import bot_pb2
from base.proto.services import access_pb2
from tools.bot.bot_server import DevtoolsBotServices, SlackSender
from tools.bot.config import BotConfig


@pytest.fixture
def bot():
    config = BotConfig("test_cicd_channel_id", "test_oncall_channel_id")
    sender = Mock(spec=SlackSender)
    return DevtoolsBotServices(sender=sender, config=config)


def test_notify_adhoc_deployment(bot):
    request = bot_pb2.NotifyAdhocDeploymentRequest(
        branch="feature/new-feature",
        commit_ref="abc123",
        clouds=["gcp"],
        namespaces=["prod", "staging"],
        target_names=["service1", "service2"],
        requestor="john.doe",
        reason="Urgent bug fix",
        deploy_id="deploy-123",
        deploy_url="https://deploy.example.com/deploy-123",
    )
    response = bot.NotifyAdhocDeployment(request, None)
    assert isinstance(response, bot_pb2.NotifyAdhocDeploymentResponse)


def test_notify_adhoc_deployment_with_empty_commit_ref(bot):
    request = bot_pb2.NotifyAdhocDeploymentRequest(
        branch="feature/new-feature",
        commit_ref="",
        clouds=["gcp"],
        namespaces=["prod", "staging"],
        target_names=["service1", "service2"],
        requestor="john.doe",
        reason="Urgent bug fix",
        deploy_id="deploy-123",
        deploy_url="https://deploy.example.com/deploy-123",
    )
    response = bot.NotifyAdhocDeployment(request, None)
    assert isinstance(response, bot_pb2.NotifyAdhocDeploymentResponse)


def test_notify_adhoc_test_finished(bot):
    request = bot_pb2.NotifyAdhocTestFinishedRequest(
        run_id="test_run_123",
        status="FAILED",
        details_url="https://example.com/test_details",
        non_success_test_targets=["test1", "test2"],
        flaky_test_targets=["test3"],
        user_email="<EMAIL>",
    )
    response = bot.NotifyAdhocTestFinished(request, None)
    assert isinstance(response, bot_pb2.NotifyAdhocTestFinishedResponse)


def test_notify_deployment_failed(bot):
    request = bot_pb2.NotifyDeploymentRequest(
        name="test_deployment",
        commit="abc123",
        commit_url="https://github.com/example/repo/commit/abc123",
        details_url="https://ci.example.com/job/123",
        namespace="test_namespace",
        cloud="test_cloud",
    )
    response = bot.NotifyDeploymentFailed(request, None)
    assert isinstance(response, bot_pb2.NotifyDeploymentResponse)


def test_notify_postmerge_test_failed(bot):
    request = bot_pb2.NotifyPostMergeTestingFailedRequest(
        details_url="https://example.com/test-details",
        non_success_test_targets=["test1", "test2"],
        flaky_test_targets=["test3"],
        test_commit=bot_pb2.Commit(
            sha="abc123", commit_url="https://github.com/example/repo/commit/abc123"
        ),
        included_commits=[
            bot_pb2.Commit(
                sha="def456", commit_url="https://github.com/example/repo/commit/def456"
            )
        ],
        commit_email="<EMAIL>",
    )
    response = bot.NotifyPostmergeTestFailed(request, None)
    assert isinstance(response, bot_pb2.NotifyPostMergeTestingFailedResponse)


def test_notify_last_known_good_updated(bot):
    request = bot_pb2.NotifyLastKnownGoodUpdatedRequest(
        last_known_good_commit=bot_pb2.Commit(
            sha="abc123",
            commit_url="https://github.com/example/repo/commit/abc123",
            commit_message="Update feature X",
            author_name="John Doe",
            author_email="<EMAIL>",
            repo_name="example-repo",
            repo_owner="example-org",
        ),
        included_commits=[],
    )
    response = bot.NotifyLastKnownGoodUpdated(request, None)
    assert isinstance(response, bot_pb2.NotifyLastKnownGoodUpdatedResponse)


def test_notify_access_proposed(bot):
    request = bot_pb2.NotifyAccessProposedRequest(
        access=access_pb2.AccessType(
            support_ui_access=access_pb2.SupportUiAccess(
                tenant_name="test_tenant",
                scope=access_pb2.SupportUiAccessScope.REQUESTS,
            )
        ),
        proposer="john_doe",
        reason="Urgent access needed for debugging",
        cluster="prod",
    )
    response = bot.NotifyAccessProposed(request, None)
    assert isinstance(response, bot_pb2.NotifyAccessProposedResponse)


def test_notify_access_approved(bot):
    request = bot_pb2.NotifyAccessApprovedRequest(
        access=access_pb2.AccessType(
            support_ui_access=access_pb2.SupportUiAccess(
                tenant_name="test_tenant",
                scope=access_pb2.SupportUiAccessScope.REQUESTS,
            )
        ),
        proposer="john_doe",
        reason="Urgent access needed for debugging",
        approver="jane_smith",
        cluster="prod",
    )
    response = bot.NotifyAccessApproved(request, None)
    assert isinstance(response, bot_pb2.NotifyAccessApprovedResponse)


def test_notify_user_feedback(bot):
    request = bot_pb2.NotifyUserFeedbackRequest(
        channel_id="test_channel_id",
        tenant_name="test_tenant",
        original_request_id="test_request_id",
        details_url="test_details_url",
        genie_url="test_genie_url",
        rating="POSITIVE",
        note="Great job!",
    )
    response = bot.NotifyUserFeedback(request, None)

    assert isinstance(response, bot_pb2.NotifyUserFeedbackResponse)


def test_limit_message(bot):
    # test short message
    message = "short message"
    result = bot._limit_message(message, limit=1000, max_lines=None)
    assert result == message

    # test long message
    message = "long message" * 1000
    result = bot._limit_message(message, limit=1000, max_lines=None)
    assert len(result) == 1000
    assert result.endswith("...")

    # test message with many lines and no limit
    message = "message\n" * 10
    result = bot._limit_message(message, limit=1000, max_lines=None)
    assert len(result.splitlines()) == 10

    # test limiting message with many lines
    message = "message\n" * 10
    result = bot._limit_message(message, limit=1000, max_lines=5)
    assert len(result.splitlines()) == 5
    assert result.endswith("\n...")

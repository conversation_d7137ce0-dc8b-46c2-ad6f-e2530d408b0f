"""Client library for the devtools slack bot."""

import pathlib

import certifi
import grpc

from base.python.grpc import client_options
import tools.bot.bot_pb2 as bot_pb2  # noqa: F401, pylint: disable=unused-import
import tools.bot.bot_pb2_grpc as bot_pb2_grpc


def setup_client(endpoint: str) -> bot_pb2_grpc.DevtoolsBotStub:
    """Setup the client stub for the slack bot."""
    if not endpoint.endswith(":80"):
        creds = grpc.ssl_channel_credentials(pathlib.Path(certifi.where()).read_bytes())
        channel = grpc.secure_channel(endpoint, creds, options=client_options.create())
    else:
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
    stub = bot_pb2_grpc.DevtoolsBotStub(channel)
    return stub

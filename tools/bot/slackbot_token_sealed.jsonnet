function(cloud, namespace, appName)
  {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: 'slack-bot-token',
      creationTimestamp: null,
      namespace: namespace,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      template: {
        metadata: {
          name: 'slack-bot-token',
          creationTimestamp: null,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
      },
      encryptedData: {
        slack_token: {
          GCP_US_CENTRAL1_PROD: 'AgDDv+xC80l7ZXIpU0kyOPEbYJjLi+1VTjZ7rkAqw5ILjUMaKvLRzkyL+hIwse5SWSHI0OyT6oc5LHmgTjo9S9+UtjKhc6OAtyfIs4VIuSy+uVGkR/YNhmSI+I3kqXKW5aOZnmmhO+uHVh+ISZx2MqyE3UjL5p9VvVRT9EO/no3bOeUVhDR3OmNrla0g8i07hmfdaVnr44gPijzBZCL6EJ3gR/Zdo04Qvb/nqVIhPYl+FdyUtImQLjI9bpLW2wUaAXVdRwsagABthgXSkBRSk0KY3ZbQIS/mu0zYoR2zUgLJDU3WhnqvMTlp/rRg32RCEIKTXbs6sD8MWyeUFAu63bOF+4bOQiP9eSh5Tzp0WEqwhhgKbmjlO9qLe9Rzhjv3utA2srl4NjjRJgoSPJrEUwLxwONZTJFno7CrmjgLI8uQj14h4EZFGCyS/AYH5l9vvvcS36jcp/FbyY6SNOzBuONEGfXTlvUUM8XAJKB4rwxZU8N6VqVNMpnG6TX1Oiz/BmEK2FwOIqx4IPKj0trzpbovCrEHJapnY3z1qwOwgWdDW/YV9io3vFbLV+BfIscWpp1wHxdhaq5P+0SutqLUI3zb52MLQ5qRJ9ICHMrrkOTQQXJ70GDelw+0mBDBsPbe7gYkO/uPlDg9JtWTDDe4j6YTziQkpLOgkFsi3uJmFjIdvKOGM5RMCzhvpsAwSdzGmF1HzswmqPDOIznZMXW8j8qf674Kj/xhwWoPxg4SFm6yRfsIm9xjCHrUqyEP3In1h6exds26xASZWws=',  // pragma: allowlist secret
          GCP_US_CENTRAL1_DEV: 'AgBIBcXdDGUjg1Kj8jSg14skrg0XHTNXSc1Ipw4O/t/t1b3sjHoXT56yurwFUE772e0dNAniTepaT3QyuY3Vt4eiu5oqkCipzIci2bD1ScF7IQ8pEUhWsdVWq/Q3dBVHiMC/YefxHmfZAzE76BJ1ZTD/C8aTGytprD4nwmTGftaD18NcVpaY1NGve9/aO8UHP2SL7sEb9borwO8v2x/IOzAJWJ+vDTyHbSRGPj0lo477LdomW47NaaX95vHa9Sh1QAorkUI55d925MFfzkNfX3e+VP86QXrdH5lkzUFG50FDVL4vROQZ0Mv5xvDs02PeM5QRFLVZ+fTzHa4AGXRB3B0gQ33Jv39H8h2DD1CpAqvSDX08eC5HMNS1CvcJLQTNSgrC+r0zt+ANzntY9XwL3fth3FPayPQt5d0JlU6R22Iue0pQarsj2g3gUfdTHrcWby5JyrdrqnPKB6Ly0h6wvTwV5J14xbDhGN63XnS36vgFgguOW1R5/U2EZaxSCPWZ25XAmiXHlXvF/vV56wieOEL6WCJLHsweyLzPSl9ucL7UheRRpDjy0kUwQMKSjDRflxO0WH91thPJptQ5kNxq4IhlC5y7ZEa3rd9LrLwF3AoU7V3i3lgBRjhEY7em5aGZ+vMW1VcTq2h+t4y5zmefnZTDORSMRqvKuJ4yAHEflpzu7vhPUQ1PcRwU0eEEi43/1L5oimiiWWYQHWNF7bWM/0UdKOi4M8S4O2RJYebI75N0QaZZ9aDf1PV0lujHv4bvYHawOcW4XwSaahs=',  // pragma: allowlist secret
          GCP_EU_WEST4_PROD: 'AgBjir9ecjFXkwfKR4/v99NWvgdlAMRZQdp2Yd+/cOVpFAELtajaK+SNbOuHO6m+V/pgFs3W3rvuU55do5fKGpdhZWVQz2wXY64hbMtYg0Jpq/iqadRCIJshileG/trYBRY9qLtT2a++CkMfF2GfaOlArMQRGGCVtKzAplBU2vMsJtlKDLyyOxW1YhZf7DX5cUobokOvBQ0nEaPq0HiKXsVD1ABjiL3EtHJTzCKlO5ywMPJkF3D5vLIYl/K7eptBV+15D8T6X/mVdsF+JSNEaIHYTgLdltThIrVLNQdXt5Dics3JqQ5NZuk+0hU6I9dXrJyi6HoaZ3xvAnbtZzTxO9vxysPLLFNd3USpV2Xz4i9z+GoZnxVPZsSsk0JWVQ9QAPmnuCSqhXImhEc8ezoS35Yr4vhZe41mDunb+92x1XmkPEhmK9KFC7gYzd92/AdRRhJM1RJgDfDnFoFI2C7zqHcejrAunN+LhO5KYdXUYRDwsF3zxuZ2xjmZvWrt4T1MdzNBT4rNCPsp1GI2PGBGdgfkROgwDI1exOBnkAizUqXF4XpmPR8vxpXoFOnK5fsa3vhFAuRYuDrIllRPDOHPkKoO22zQ5Gy8YrFml1775ysRPywrqHR635mSWZnCZDT33AaonIA9BezFUSdc3xrOMcpz/YbUOOYG4wU5gpdIob+pRuhb/P91yhw0b5UWNcMxA2Zy7y/5d9rpFON3gzZygX7lYVxsPkb/XdtI6kGQp7Rc1/wjOqVwz9KRsN33TTFsTMj6v0n0lFk548A=',  // pragma: allowlist secret
          GCP_US_CENTRAL1_GSC_PROD: 'AgB+5xtcXnTMSW45oyQ/2vVJW0GY6mhUPiaW8gvi/w9adrryz1++LxKdSHdQHDpMQrNRbhb+XmP4B1LnlFfACnauMFdAlEt+FFrfE33cpifuHmomCAe5rz9TZYh2l/hUkJGEfw07Sj45ZQRYtAFRKEYEbtnDSNhknvRKti9mK5/SMbkwBm424ebEx6MmoSr5vA7Yqrei3vOqB2ApRZsc/NTiIZDV/8VCYscDI/hYtToy0QQCgrGRQHeOnDQbr/X26oezkswDitdecZje3p7ud4HyfKBYHonUdIWSdpf42cujpbES/gclJ7sZjlSzVPBDf0B2Nz3EtZ/wDZFVYwuqtYirDJT1O+NcgX/KriaFLWzWXyZ69ZNNuIz0o4VzxlTtnhlCkEjlwBM8CzbysdC/VKEA7GjYTYhW8FdXlMUhFCUXR+kytdEvuj1jbaj/Pn9/c0nuyFBL9Z9qzWWiO81VrdajXWhSW7uDUgzPp7Pw7jHXscAb2DD54ykcGeXXknh9SiTi+EeYJG17QpZgLsoWf/z5slI7K7fK0I/lGrbUTsoSmmQy0xt+6LKWQr4A3OTAygtUsE2yp197Cmk99vUta2lKjJBVSelz124bQWRM9ofzChmZXiCpJxkhPgn8YWp0GwoKHE+96GwWj9TLsKnplIZEGIEBvqJYMUr9fehhj+tGeGmP2D3hUp4wHjOPpYop8iAiaAsrMvGRk2kKKyeZ4fyDeqqmB2ylUf1aHyELWNI5KUF+AvP3izMEHUHUXIei3YpBgDOD2XfCcMQ=',  // pragma: allowlist secret
        }[cloud],
      },
    },
  }

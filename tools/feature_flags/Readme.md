# Feature Flags Tool

This tool handles configuration of Feature Flags (Infrastructure as Code)

Changes to feature flag configurations are synchronized with a feature flag service provider (currently LaunchDarkly)

## Usage - How to change flags

### Edit the flags
Update the file `flags.jsonnet` with the desired changes

#### `flags.yml` format example
```yaml
  flag_key_1:
    # The unique identifier for the key
    sync: true
    # Only flags with sync true will be synced to LaunchDarkly, the others are purely informational (for validation or documentation purposes)
    description: "Flag description"
    default_return_value: "my_default_value"  # default value returned when no rules match
    envs:
      env_key:  # production/test
        on: true  # If the env is off, rules will not be evaluated for it and the default value will always be returned
        archived: false  # Archiving the key for the env is equivalent to deleting it
        rules:
        # Rules are fallthrough - each one is evaluated and the first one that matches is returned
         - { namespace: "my_namespace", return_value: "value1" }
         # If the namespace is exactly `my_namespace`, `value1` will be returned
         - { pod_name: "my_pod_name", return_value: "value2" }
        # If the pod_name starts with `my_pod_name`, `value2` will be returned
         - { pod_name: "!other_pod_name", return_value: "value3" }
        # If the pod_name DOES NOT start with `other_pod_name`, `value3` will be returned.
         - { return_value: "value4" }
         # If no rule was matched, value4 will be returned
  flag_key_2:
      # ...
```

#### User ID rules

Keep in mind that user ID may not be available in all parts of the backend. So far binding to user IDs is only supported in the `api_proxy` service. This applies
to both `user_id_hmac` and `user_uuid`.

To bind a flag to specific users, use:
Legacy: `user_id_hmac` rule
Do not check in plaintext, or vanilla hashes, of user emails. Use the `generate_user_id_hmac.sh` script to generate an HMAC for a user ID.
The script requires Kubernetes access to the namespace which user's requests are routed to.

Preferred: `user_uuid`
The user UUID is the auth entity ID of the user. With sufficient access, it can be found from prod analytics dataset. Ability to query
this from cross-tenant support may be added in the future.


### Validate the yaml file
Run:
```sh
bazel run //tools/feature_flags:flags_yaml_validation
```

If the YAML is correct, this should succeed and print out the information in the yaml as a `LocalFeatureFlag` object

### Send a PR
Send a PR for review

### Merge the PR
The diff will automatically apply after the PR is merged to main by CI hooks

If a manual sync trigger is needed (e.g. in case of temporary LaunchDarkly outage or flakiness), one can be triggered by restarting the syncer service:

```sh
kubectl rollout restart deployment/flags-syncer-server -n devtools --context gke_system-services-prod_us-central1_us-central1-prod
```

## Implementation Details

### Main Files in this directory
- `flags.jsonnet` - The source of truth for flag updates to be synchronized with the feature flag service.
- `launchdarkly_diff_builder.py` - Generates patches and new flags, in LaunchDarkly format, based on the change from the local state to LaunchDarkly state.
- `launchdarkly_client.py` - Client for LaunchDarkly API - used by the diff builder and sync
- `launchdarkly_sync.py` - Performs a sync from flags.jsonnet into the remote service - uses the diff builder and the client
- `launchdarkly_types.py` - Types and type-convertion code used by the diff builder
- `syncer` - A service that listens to gRPC calls (usually coming from Github webhooks) and runs the sync

## Details of the sync flow

### High-level overview
1. A github push triggers the `tools/bazel_runner/ci/consumer.py::FeatureFlagsSyncConsumer`
2. If the push is to `main`, the consumer triggers the syncer via a gRPC `SyncRequest`
3. The `SyncerThread`s in the syncer service (`tools/feature_flags/syncer/syncer_server.py`) handles the request
4. The `SyncerThread` checks out the latest main repo and calls `tools/feature_flags/launchdarkly_sync.py::sync`
5. The `sync` function uses the `launchdarkly_diff_builder` to get all new flags, and calls the `launchdarkly_client` to create them. It then gets the diff from the local flags to the remote flags and calls `launchdarkly_client` to apply the diff.
  - The separation of new and diff is important because new flags aren't created with all information, so we have to patch them after creation to ensure that they are complete

### Diff Builder overview
1. The diff builder reads the `flags.jsonnet` file and builds a `LocalFeatureFlag` dataclass from it.
  - This step is mostly for validating that the `yaml` is correct
2. The diff builder calls `launchdarkly_client` to get all remote flags from LaunchDarkly.
3. `new_flags` handle flags that don't exist in LaunchDarkly:
  a. For every `LocalFeatureFlag` that doesn't exist in LaunchDarkly, it builds a `FeatureFlagBody` from the `LocalFeatureFlag` which can be used to create the flag in LaunchDarkly
4. `get_diff()` handles  flags that exist in LaunchDarkly:
  a. It merges the relevant information from the `LocalFeatureFlag` into the `FeatureFlag` object
  b. `get_flag_patch_operations` - for every flag, build a list of `PatchWithComment`s required to update the original (remote) `FeatureFlag` to the new (merged) `FeatureFlag`.


## External References

LaunchDarkly Feature Flag API documentation: https://apidocs.launchdarkly.com/tag/Feature-flags/

LaunchDarkly Python API: https://github.com/launchdarkly/api-client-python

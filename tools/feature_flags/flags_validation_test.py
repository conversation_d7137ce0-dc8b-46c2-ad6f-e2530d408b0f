"""
Script - Validates that the flags.jsonnet file is valid by building a dataclass from it
"""

import sys
import json
from collections import Counter
from typing import Any
import _gojsonnet as jsonnet
from tools.feature_flags.launchdarkly_types import AugmentFeatureFlagEnv


from tools.feature_flags.launchdarkly_types import (
    AugmentFeatureFlag,
    augment_flags_dict_to_augment_flags_dataclass,
)
from tools.feature_flags.launchdarkly_diff_builder import load_flags

ALLOWED_ENVS = ["production", "test"]


def get_list_duplicates(obj: Any) -> dict[str, list[Any]]:
    duplicates = {}

    for attr, value in vars(obj).items():
        if isinstance(value, list):
            if all(isinstance(item, (str, int, float, bool)) for item in value):
                counter = Counter(value)
                dupes = [item for item, count in counter.items() if count > 1]
                if dupes:
                    duplicates[attr] = dupes

    return duplicates


def _has_default_rule(
    env: AugmentFeatureFlagEnv, default_return_value: str | int | float | bool
) -> bool:
    for rule in env.rules:
        num_of_values = sum(1 for value in vars(rule).values() if value is not None)
        if "return_value" in vars(rule) and num_of_values == 1:
            return True
    return False


def _default_value_equals_default_rule(
    env: AugmentFeatureFlagEnv,
    default_return_value: str | int | float | bool,
    flag_key: str,
) -> bool:
    has_default_rule = _has_default_rule(env, default_return_value)
    if default_return_value == "":  # undefined case. not intuitive.
        return True
    if not has_default_rule:
        print(f"Flag {flag_key} has no default rule")
        return False
    for rule in env.rules:
        num_of_values = sum(1 for value in vars(rule).values() if value is not None)
        if "return_value" in vars(rule) and num_of_values == 1:
            if rule.return_value == default_return_value:
                return True
    return False


def _check_defaults(flag: AugmentFeatureFlag) -> bool:
    if flag.key in ["memories_params"]:  # default value too big to force duplication.
        return True
    if not flag.envs:
        print(f"Flag {flag.key} has no envs")
        return False
    for env in flag.envs.values():
        if env.rules:
            if not _default_value_equals_default_rule(
                env, flag.default_return_value, flag.key
            ):
                print(f"Flag {flag.key} default value does not match default rule")
                return False
    return True


def _check_flag(flag: AugmentFeatureFlag, namespace_list: list[str]) -> bool:
    if not _check_defaults(flag):
        return False
    if flag.envs:
        if not set(flag.envs.keys()).issubset(ALLOWED_ENVS):
            print(f"Invalid envs for flag {flag.key}: {flag.envs.keys()}")
            return False
        for env in flag.envs.values():
            for rule in env.rules:
                if get_list_duplicates(rule):
                    print(f"Duplicate values in rule for flag {flag.key}")
                    print(get_list_duplicates(rule))
                    return False
                if rule.namespace:
                    if isinstance(rule.namespace, str):
                        if rule.namespace not in namespace_list:
                            print(f"Namespace {rule.namespace} not found in namespaces")
                            return False
                    else:
                        for ns in rule.namespace:
                            if ns not in namespace_list:
                                print(f"Namespace {ns} not found in namespaces")
                                return False
                # note: we do not have a reliable tenant list at build time
                # see comment in deploy/tenants/BUILD
    return True


def _get_namespaces() -> list[str]:
    namespace_list = [
        ns["namespace"]
        for ns in json.load(open("deploy/tenants/namespaces.json"))["namespaces"]
    ]
    namespace_list.extend(["central", "central-staging"])
    return namespace_list


def main():
    namespace_list = _get_namespaces()
    update_json = load_flags(".")
    local_flags = augment_flags_dict_to_augment_flags_dataclass(
        augment_flags_dict=update_json
    )
    failed = False
    for flag in local_flags:
        if not _check_flag(flag, namespace_list):
            print(f"Flag {flag.key} failed validation")
            failed = True
    if failed:
        sys.exit(1)


if __name__ == "__main__":
    main()

import launchdarkly_api
import structlog
import time

from launchdarkly_api.api import feature_flags_api

from launchdarkly_api.model.feature_flag import FeatureFlag
from launchdarkly_api.model.feature_flag_body import FeatureFlagBody
from launchdarkly_api.model.feature_flags import FeatureFlags
from launchdarkly_api.model.patch_with_comment import PatchWithComment

from tools.feature_flags.config import LaunchDarklyConfig
from tools.feature_flags.launchdarkly_types import LaunchDarklyDiff

logger = structlog.get_logger()

LIMIT = 100  # Maximum limit allowed by LD API is 100
PRODUCTION = "production"


class LaunchDarklyClient:
    def __init__(self, config: LaunchDarklyConfig):
        self.project_key = config.launchdarkly_project_key
        configuration = launchdarkly_api.Configuration()
        configuration.api_key["ApiKey"] = config.launchdarkly_api_key
        api_client = launchdarkly_api.ApiClient(configuration)
        self.feature_flags_api = feature_flags_api.FeatureFlagsApi(api_client)

    def get_flags(self) -> list[FeatureFlag]:
        items: list[FeatureFlag] = []
        done = False
        offset = 0
        while not done:
            feature_flags = self.feature_flags_api.get_feature_flags(
                project_key=self.project_key,
                env=PRODUCTION,
                limit=LIMIT,
                offset=offset,
                summary=False,
            )
            assert isinstance(feature_flags, FeatureFlags)
            items.extend(feature_flags.items)
            if len(items) >= feature_flags.total_count or len(feature_flags.items) == 0:
                done = True
            else:
                offset += LIMIT
        # We only got the PRODUCTIOn envs for the flags, let's get all
        result: list[FeatureFlag] = []
        for flag in items:
            result.append(
                self.feature_flags_api.get_feature_flag(
                    project_key=self.project_key,
                    feature_flag_key=flag.key,
                )
            )
        return result

    def create_flag(self, feature_flag_body: FeatureFlagBody) -> FeatureFlag:
        """Creates a flag in launchdarkly"""
        logger.info(f"Creating flag {feature_flag_body.key}")
        return self.feature_flags_api.post_feature_flag(
            project_key=self.project_key,
            feature_flag_body=feature_flag_body,
        )

    def patch_flag(self, flag_key: str, patch_with_comment: PatchWithComment) -> None:
        """Updates a flag in launchdarkly"""
        logger.info(f"Performing update for flag {flag_key}")
        for _ in range(5):  # Retry loop-- up to 5 times
            try:
                self.feature_flags_api.patch_feature_flag(
                    project_key=self.project_key,
                    feature_flag_key=flag_key,
                    patch_with_comment=patch_with_comment,
                )
            except launchdarkly_api.ApiException as e:
                if e.status == 429:
                    logger.info(
                        "Got resource exhausted error. Waiting 15 seconds to try again"
                    )
                    time.sleep(15)
                    continue
                else:
                    raise e
            break

    def create_new_flags(self, new_flags: list[FeatureFlagBody]) -> None:
        """Creates new flags in launchdarkly"""
        remote_flags = self.get_flags()
        for flag_body in new_flags:
            if flag_body.key in [f.key for f in remote_flags]:
                logger.info(f"Launchdarkly - Flag {flag_body.key} already exists")
                continue
            self.create_flag(flag_body)

    def apply_diff(self, diff: LaunchDarklyDiff) -> None:
        """Applies a diff to launchdarkly"""
        logger.info("Launchdarkly - Applying Diff %s", diff.to_dict())
        for flag_key, patch_with_comment in diff.patch_flags.items():
            self.patch_flag(flag_key, patch_with_comment)
        logger.info("Launchdarkly - Diff Applied")

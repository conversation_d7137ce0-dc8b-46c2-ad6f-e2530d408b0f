[{"name": "model", "kind": "multivariate", "key": "model", "version": 6, "creation_date": 1698077977544, "variations": [{"value": "roguesl-farpref-16B-eth6-c", "id": "287deea7-c102-499b-9098-ee5f23b8172c", "description": "Early March model"}, {"value": "roguesl-v2-16b-eth6-04-1", "id": "84d3fef6-b6ca-4a1b-a3e9-9fcc2afee620", "description": "Candidate for mid March"}, {"value": "roguesl-v2-16b-seth6-16-1-p512", "id": "32f48cc3-071d-4d52-9115-45b8d1a3811b", "description": "Candidate for early April"}, {"value": "roguesl-v2-16b-seth616-rec", "id": "d5418dbb-e0ea-4e27-9b16-31ab10c84e5b", "description": "Added apr 8th at <PERSON><PERSON><PERSON>'s request"}], "temporary": true, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/model", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": true, "archived": false, "salt": "6bcd6bf895d347e985e958932a5e157f", "sel": "fcb0ed12b9944c6c907a96667dc3ada0", "last_modified": 1712592198256, "version": 17, "site": {"href": "/default/production/features/model", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "debug_events_until_date": 1707689877070, "summary": {"variations": {"1": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true}, "3": {"rules": 1, "null_rules": 0, "targets": 0, "context_targets": 0}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 1}, "prerequisites": [], "rules": [{"clauses": [{"attribute": "key", "op": "in", "values": ["dogfood"], "negate": false, "id": "3c0befd8-23d7-4a70-b6ed-0acf8e978d1b", "context_kind": "namespace"}], "track_events": false, "id": "1b130ec8-249d-400a-a5e2-4e9b4891ca67", "description": "Target dogfood", "ref": "9047a901-9056-4bd8-982b-79be68658bba", "variation": 3}], "targets": []}, "test": {"on": false, "archived": false, "salt": "8f7c6258e6144fc2b8a2e89f2fdd90d4", "sel": "e81f27cb79c04a50a0aace6199de2883", "last_modified": 1699394393783, "version": 2, "site": {"href": "/default/test/features/model", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "prerequisites": [], "rules": [], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65369bebf1cbe0121afc368c", "type": "application/json"}}, "id": "65369bebf1cbe0121afc368c", "role": "owner", "email": "<EMAIL>", "first_name": "Constantine", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 0, "off_variation": 1}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65369bebf1cbe0121afc368c", "variationJsonSchema": null}, {"name": "edit_model", "kind": "multivariate", "key": "edit_model", "version": 4, "creation_date": 1707244085171, "variations": [{"value": "droid-1B-BF16-v1-edit", "id": "5bf00070-0431-45e9-96ef-b89a1091bd51", "name": "droid-1B-BF16-v1-edit"}, {"value": "droid-33B-FP8-R1-edit", "id": "5eb16a28-52c6-4ff4-9c62-521b5dbcc212", "name": "droid-33B-FP8-R1-edit"}, {"value": "", "id": "bc3f65a5-2d70-4d58-9409-231442c4f72d", "name": "not-set"}, {"value": "droid-187-33B-FP8-edit", "id": "e9aca01f-f90b-4ec9-9df4-68f38a97e08a", "name": "droid-187-33B-FP8-edit"}], "temporary": false, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/edit_model", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": true, "archived": false, "salt": "fd406d4a46a04574b9e1e4c16ee128c3", "sel": "9fc62e5314174716832d1030eb546428", "last_modified": 1712605775822, "version": 5, "site": {"href": "/default/production/features/edit_model", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"3": {"rules": 1, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 3}, "prerequisites": [], "rules": [{"clauses": [{"attribute": "key", "op": "in", "values": ["dogfood"], "negate": false, "id": "ce4b9742-e9c5-4a6e-8ba1-800115784c03", "context_kind": "namespace"}], "track_events": false, "id": "e92a7d45-8679-4036-8698-27acd8dd52fb", "description": "Target dogfood", "ref": "7434e1a8-398b-4f38-abae-e641391d1e7f", "variation": 3}], "targets": []}, "test": {"on": false, "archived": false, "salt": "06b96648cde14b31bb8df4790466038d", "sel": "393bc055cb034402a464df2c5b6518a4", "last_modified": 1707244260573, "version": 2, "site": {"href": "/default/test/features/edit_model", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"2": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 2}, "prerequisites": [], "rules": [], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65774fad49fce10fa36417e3", "type": "application/json"}}, "id": "65774fad49fce10fa36417e3", "role": "admin", "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "S<PERSON>iser"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 2, "off_variation": 2}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65774fad49fce10fa36417e3", "variationJsonSchema": null}, {"name": "connectivity_test_flag", "kind": "boolean", "key": "connectivity_test_flag", "version": 1, "creation_date": 1709763631665, "variations": [{"value": true, "id": "0a4c8348-ddbb-461f-9883-ab07d797cde0"}, {"value": false, "id": "efe2a514-9964-4390-aaac-d79706d61ab8"}], "temporary": false, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/connectivity_test_flag", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": false, "archived": false, "salt": "4fae1395130842eca1f1becd455d9ec5", "sel": "f5d16faa9b2347a99180c03b0ff40129", "last_modified": 1709763631675, "version": 1, "site": {"href": "/default/production/features/connectivity_test_flag", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": false, "archived": false, "salt": "52a7e0d1f5a043b7aa9dd62d5215458e", "sel": "a8bf3820aac34ce198fcd8d94dc71632", "last_modified": 1709773030538, "version": 2, "site": {"href": "/default/test/features/connectivity_test_flag", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}, "1": {"rules": 1, "null_rules": 0, "targets": 0, "context_targets": 0}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [{"clauses": [{"attribute": "key", "op": "in", "values": ["dev-costa"], "negate": false, "id": "b8be07ea-1526-47ea-9ec3-02436182b138", "context_kind": "namespace"}], "track_events": false, "id": "cd36832c-2627-4feb-b431-09e9c000b7f0", "ref": "01f0ef15-8136-4a81-a05a-10c9e5a5f5f5", "variation": 1}], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65369bebf1cbe0121afc368c", "type": "application/json"}}, "id": "65369bebf1cbe0121afc368c", "role": "owner", "email": "<EMAIL>", "first_name": "Constantine", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 0, "off_variation": 0}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65369bebf1cbe0121afc368c", "variationJsonSchema": null}, {"name": "completion_low_quality_threshold", "kind": "multivariate", "key": "completion_low_quality_threshold", "version": 7, "creation_date": 1710796762117, "variations": [{"value": "0.895", "id": "37e84d94-3ecc-4d46-9451-535d8d4241ba"}, {"value": "1.0", "id": "75ef2764-19ca-4f87-9800-b7dbba6705e0"}, {"value": "0.0", "id": "2bca9d1f-ca89-4739-80c7-5e35da0028c7"}], "temporary": true, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/completion_low_quality_threshold", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": false, "archived": false, "salt": "73c8a8abeb6f4ad3a618be14ad91c68a", "sel": "95cb40b16e6747818cc216b6e0fd6bbf", "last_modified": 1710796762127, "version": 2, "site": {"href": "/default/production/features/completion_low_quality_threshold", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"1": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 1}, "off_variation": 1, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": true, "archived": false, "salt": "30995cda7114463a9d2fa1175eca0ea0", "sel": "6c63c6b49ceb4bb4814f3de2e94fc592", "last_modified": 1710800882800, "version": 9, "site": {"href": "/default/test/features/completion_low_quality_threshold", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 1, "null_rules": 0, "targets": 0, "context_targets": 0}, "1": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 1}, "off_variation": 1, "prerequisites": [], "rules": [{"clauses": [{"attribute": "key", "op": "in", "values": ["dev-vzhao"], "negate": false, "id": "5cbaa821-2b7c-41c8-9cee-eea94c703613", "context_kind": "namespace"}], "track_events": false, "id": "0b0293f5-5e28-4366-b782-1e897b04732f", "ref": "741f7009-6897-493a-affe-7a79ceee1dd7", "variation": 0}], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65369bebf1cbe0121afc368c", "type": "application/json"}}, "id": "65369bebf1cbe0121afc368c", "role": "owner", "email": "<EMAIL>", "first_name": "Constantine", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 1, "off_variation": 1}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65369bebf1cbe0121afc368c", "variationJsonSchema": null}, {"name": "checkpoint_blobs_v2", "kind": "boolean", "key": "checkpoint_blobs_v2", "version": 1, "creation_date": 1710801518649, "variations": [{"value": true, "id": "a729e5c0-1c5a-4e74-bbe8-17cc96860356"}, {"value": false, "id": "2c533e17-635e-42f5-91a7-c78fe261ea50"}], "temporary": true, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/checkpoint_blobs_v2", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": true, "archived": false, "salt": "5c6e55272909478db982b2bb36e17c7a", "sel": "b8b6bed6adc34d64b572e6611f851df9", "last_modified": 1712163419646, "version": 5, "site": {"href": "/default/production/features/checkpoint_blobs_v2", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": true, "archived": false, "salt": "2195ad7baedc4076851df53802509d06", "sel": "1e5b1e7780554a8d8a6e1515b9489d30", "last_modified": 1711385565262, "version": 4, "site": {"href": "/default/test/features/checkpoint_blobs_v2", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 2, "null_rules": 0, "targets": 0, "context_targets": 0}, "1": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 1}, "off_variation": 1, "prerequisites": [], "rules": [{"clauses": [{"attribute": "key", "op": "in", "values": ["dev-aswin"], "negate": false, "id": "9776e0ee-c099-480e-a47a-c64c115361b1", "context_kind": "namespace"}], "track_events": false, "id": "4070df00-cd13-4d8d-a8a7-e88292b0bdf1", "ref": "474e445a-de8c-40d2-8285-eb1c50001623", "variation": 0}, {"clauses": [{"attribute": "key", "op": "startsWith", "values": ["test-"], "negate": false, "id": "3f6dd07f-2511-4a49-bd28-8563c5c4a71e", "context_kind": "namespace"}], "track_events": false, "id": "5f7251a5-5da7-4257-b813-075778cb04d2", "ref": "ee6de31d-b741-45eb-8566-0bb3484ab663", "variation": 0}], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65369bebf1cbe0121afc368c", "type": "application/json"}}, "id": "65369bebf1cbe0121afc368c", "role": "owner", "email": "<EMAIL>", "first_name": "Constantine", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 1, "off_variation": 1}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65369bebf1cbe0121afc368c", "variationJsonSchema": null}, {"name": "short_indexing_threshold", "kind": "multivariate", "key": "short_indexing_threshold", "version": 1, "creation_date": 1712008808172, "variations": [{"value": 15.0, "id": "24912fdb-d97b-457c-b87d-0ff1cccfc617", "name": "default_value"}, {"value": 30.0, "id": "5fffd8de-2f79-469d-b873-8ae0ecff368c", "name": "secondary_value"}], "temporary": false, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/short_indexing_threshold", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": false, "archived": false, "salt": "5e29f5aa7cb44f81bfbd6400ece8551a", "sel": "11c4e4cd3b9c44a4b3f79f048f618d11", "last_modified": 1712008808186, "version": 1, "site": {"href": "/default/production/features/short_indexing_threshold", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": true, "archived": false, "salt": "789e83beff2341c7a5a1a9254794e194", "sel": "1371449e04e54f5383ac7245fed2da1b", "last_modified": 1712010534572, "version": 4, "site": {"href": "/default/test/features/short_indexing_threshold", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65e7502992c4771005e38f3c", "type": "application/json"}}, "id": "65e7502992c4771005e38f3c", "role": "writer", "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "Gaunt-Seo"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 0, "off_variation": 0}, "description": "The number of files that still need to be indexed before we indicate to the user that indexing is occurring.", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65e7502992c4771005e38f3c", "variationJsonSchema": null}, {"name": "long_indexing_threshold", "kind": "multivariate", "key": "long_indexing_threshold", "version": 1, "creation_date": 1712008858930, "variations": [{"value": 1000.0, "id": "787bf5bc-6e15-4ecb-acd6-6a513f4d7e61", "name": "default_value"}, {"value": 1500.0, "id": "a371c62e-d373-4de2-833a-4be26c57af17", "name": "secondary_value"}], "temporary": true, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/long_indexing_threshold", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": false, "archived": false, "salt": "c904a1ca3c8f43328c823b2c56a0ce47", "sel": "a81312289a7e4ebbb3616c466512d793", "last_modified": 1712008858966, "version": 1, "site": {"href": "/default/production/features/long_indexing_threshold", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": false, "archived": false, "salt": "536ff794f3f645bcb9e6ae7cb6b096e6", "sel": "96b92a2e728e4837a0bd3e0fb79bf55c", "last_modified": 1712010520297, "version": 5, "site": {"href": "/default/test/features/long_indexing_threshold", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65e7502992c4771005e38f3c", "type": "application/json"}}, "id": "65e7502992c4771005e38f3c", "role": "writer", "email": "<EMAIL>", "first_name": "<PERSON>", "last_name": "Gaunt-Seo"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 0, "off_variation": 0}, "description": "The number of files that still need to be indexed before we pop up a notification that indexing is occurring.", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65e7502992c4771005e38f3c", "variationJsonSchema": null}, {"name": "chat_model", "kind": "multivariate", "key": "chat_model", "version": 3, "creation_date": 1712265872702, "variations": [{"value": "binks-1B-BF16-v1-chat", "id": "b15303cf-d225-4564-9a02-77f86d6c53de", "name": "binks-1B-BF16-v1-chat"}, {"value": "binks-33B-FP8-v1-chat", "id": "2e3a5a45-f3ac-4088-8725-879a11aa08b9", "name": "binks-33B-FP8-v1-chat"}, {"value": "", "id": "b499e2cc-6ddc-4838-be82-7853a32c77d8", "name": "not-set"}], "temporary": false, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/chat_model", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": false, "archived": false, "salt": "0612150a343b4b6aa77b893d485783ec", "sel": "e7cb560da4ce4459a815f066a5e84f56", "last_modified": 1712265872712, "version": 1, "site": {"href": "/default/production/features/chat_model", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"2": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 2}, "off_variation": 2, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": false, "archived": false, "salt": "457d970267a741f483d6a8a36d0115df", "sel": "674c7b3dce6d4e41a226e9baf78a343d", "last_modified": 1712265872712, "version": 1, "site": {"href": "/default/test/features/chat_model", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"2": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 2}, "off_variation": 2, "prerequisites": [], "rules": [], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65df810df79ac91f28889fa3", "type": "application/json"}}, "id": "65df810df79ac91f28889fa3", "role": "admin", "email": "<EMAIL>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 2, "off_variation": 2}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65df810df79ac91f28889fa3", "variationJsonSchema": null}, {"name": "test_sync_flag_2", "kind": "multivariate", "key": "test_sync_flag", "version": 13, "creation_date": 1712691136624, "variations": [{"value": "binks-33B-FP8-v1-chat", "id": "3ccf36df-52b0-4686-82ef-dc984fe440de", "name": "binks-33B-FP8-v1-chat"}, {"value": "", "id": "bcdf4a0e-08e6-4e6e-9639-8ef0c92eba70", "name": "not-set"}, {"value": "new-fake-variation2", "id": "1234abcd-08e6-4e6e-9639-8ef0c92eba70", "name": "new-fake-variation2"}], "temporary": false, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/test_sync_flag", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": true, "archived": false, "salt": "0e9f031d8cab4a2298b7330c6624f40f", "sel": "a07cd28de99c4e4dbfc25f88721b24c6", "last_modified": 1712706955135, "version": 4, "site": {"href": "/default/production/features/test_sync_flag", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"1": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 1, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": false, "archived": false, "salt": "afac7e9035fd41b890bd0d3508202e18", "sel": "95b62f7672ef43c8a987debdef37b6e6", "last_modified": 1712706955135, "version": 4, "site": {"href": "/default/test/features/test_sync_flag", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"1": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 1}, "off_variation": 1, "prerequisites": [], "rules": [], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65df810df79ac91f28889fa3", "type": "application/json"}}, "id": "65df810df79ac91f28889fa3", "role": "admin", "email": "<EMAIL>", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 2, "off_variation": 2}, "description": "Updated via merge patch", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65df810df79ac91f28889fa3", "variationJsonSchema": null}, {"name": "fake_boolean_flag", "kind": "boolean", "key": "fake_boolean_flag", "version": 1, "creation_date": 1709763631665, "variations": [{"value": true, "id": "0a4c8348-ddbb-461f-9883-ab07d797cde0", "name": ""}, {"value": false, "id": "efe2a514-9964-4390-aaac-d79706d61ab8", "name": ""}], "temporary": false, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/connectivity_test_flag", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": false, "archived": false, "salt": "4fae1395130842eca1f1becd455d9ec5", "sel": "f5d16faa9b2347a99180c03b0ff40129", "last_modified": 1709763631675, "version": 1, "site": {"href": "/default/production/features/connectivity_test_flag", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": false, "archived": false, "salt": "52a7e0d1f5a043b7aa9dd62d5215458e", "sel": "a8bf3820aac34ce198fcd8d94dc71632", "last_modified": 1709773030538, "version": 2, "site": {"href": "/default/test/features/connectivity_test_flag", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}, "1": {"rules": 1, "null_rules": 0, "targets": 0, "context_targets": 0}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [{"clauses": [{"attribute": "key", "op": "in", "values": ["dev-costa"], "negate": false, "id": "b8be07ea-1526-47ea-9ec3-02436182b138", "context_kind": "namespace"}], "track_events": false, "id": "cd36832c-2627-4feb-b431-09e9c000b7f0", "ref": "01f0ef15-8136-4a81-a05a-10c9e5a5f5f5", "variation": 1}], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65369bebf1cbe0121afc368c", "type": "application/json"}}, "id": "65369bebf1cbe0121afc368c", "role": "owner", "email": "<EMAIL>", "first_name": "Constantine", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 0, "off_variation": 0}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65369bebf1cbe0121afc368c", "variationJsonSchema": null}, {"name": "fake_uni_boolean_flag", "kind": "boolean", "key": "fake_uni_boolean_flag", "version": 1, "creation_date": 1709763631665, "variations": [{"value": true, "id": "0a4c8348-ddbb-461f-9883-ab07d797cde0", "name": ""}, {"value": false, "id": "efe2a514-9964-4390-aaac-d79706d61ab8", "name": ""}], "temporary": false, "tags": [], "links": {"parent": {"href": "/api/v2/flags/default", "type": "application/json"}, "self": {"href": "/api/v2/flags/default/connectivity_test_flag", "type": "application/json"}}, "experiments": {"baseline_idx": 0, "items": []}, "custom_properties": {}, "archived": false, "deprecated": false, "environments": {"production": {"on": true, "archived": false, "salt": "4fae1395130842eca1f1becd455d9ec5", "sel": "f5d16faa9b2347a99180c03b0ff40129", "last_modified": 1709763631675, "version": 1, "site": {"href": "/default/production/features/connectivity_test_flag", "type": "text/html"}, "environment_name": "Production", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [], "targets": []}, "test": {"on": false, "archived": false, "salt": "52a7e0d1f5a043b7aa9dd62d5215458e", "sel": "a8bf3820aac34ce198fcd8d94dc71632", "last_modified": 1709773030538, "version": 2, "site": {"href": "/default/test/features/connectivity_test_flag", "type": "text/html"}, "environment_name": "Test", "track_events": false, "track_events_fallthrough": false, "summary": {"variations": {"0": {"rules": 0, "null_rules": 0, "targets": 0, "context_targets": 0, "is_fallthrough": true, "is_off": true}, "1": {"rules": 1, "null_rules": 0, "targets": 0, "context_targets": 0}}, "prerequisites": 0}, "context_targets": [], "fallthrough": {"variation": 0}, "off_variation": 0, "prerequisites": [], "rules": [{"clauses": [{"attribute": "key", "op": "in", "values": ["dev-costa"], "negate": false, "id": "b8be07ea-1526-47ea-9ec3-02436182b138", "context_kind": "namespace"}], "track_events": false, "id": "cd36832c-2627-4feb-b431-09e9c000b7f0", "ref": "01f0ef15-8136-4a81-a05a-10c9e5a5f5f5", "variation": 1}], "targets": []}}, "maintainer": {"links": {"self": {"href": "/api/v2/members/65369bebf1cbe0121afc368c", "type": "application/json"}}, "id": "65369bebf1cbe0121afc368c", "role": "owner", "email": "<EMAIL>", "first_name": "Constantine", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "client_side_availability": {"using_environment_id": false, "using_mobile_key": false}, "defaults": {"on_variation": 0, "off_variation": 0}, "description": "", "goal_ids": [], "include_in_snippet": false, "maintainer_id": "65369bebf1cbe0121afc368c", "variationJsonSchema": null}]
test_sync_flag_new:
  sync: true
  description: "I love flags"
  default_return_value: ""
  envs:
    production:
      rules:
        - {namespace: "dogfood", return_value: "foo"}
        - {pod_name: "!completion-blah-blah-foo", return_value: "bar"}
        - {return_value: ""}
test_sync_flag:
  sync: true
  description: "This is a flag update that replaces the variations"
  default_return_value: ""
  envs:
    production:
      rules:
        - {namespace: "dogfood", return_value: "foo"}
        - {pod_name: "!completion-blah-blah-foo", return_value: "bar"}
        - {tenant_name: "aitutor-pareto", return_value: "bar"}
        - {user_id_hmac: "fakesha", return_value: "bar"}
        - {user_uuid: "fakeuuid", return_value: "uuid"}
        - {client: ["intellij", "vscode"], max_client_version: "0.90.0", return_value: "bar"}
        - {client: "vscode", min_client_version: "0.75.0", return_value: "foo"}
        - {return_value: ""}
test_no_sync_flag_new:
  sync: false
  description: "I love flags"
  default_return_value: ""
  envs:
    production:
      rules:
        - {namespace: "dogfood", return_value: "foo"}
        - {pod_name: "!completion-blah-blah-foo", return_value: "bar"}
        - {return_value: ""}
fake_boolean_flag:
  sync: false
  description: "Changing the boolean flag, but sync is false"
  default_return_value: false
  envs:
    production:
      rules:
        - {namespace: "dogfood", return_value: true}
        - {pod_name: "!my_pod", return_value: true}
        - {return_value: false}
fake_one_variation_flag:
  sync: true
  description: "Only one variation cannot be applied"
  default_return_value: "MyVal"
  envs:
    production:
      rules:
        - {return_value: "MyVal"}
    test:
      rules:
        - {return_value: "MyVal"}
fake_uni_boolean_flag:
  sync: true
  description: "Boolean flag with only one variation should keep old variation"
  default_return_value: true
  envs:
    production:
      rules:
        - {namespace: "dogfood", return_value: true}
        - {pod_name: "!my_pod", return_value: true}
        - {client: "intellij", min_client_version: "1.0.0", max_client_version: "2.0.0", return_value: true}
        - {return_value: true}

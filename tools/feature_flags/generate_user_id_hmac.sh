#!/bin/bash
# Produce an HMAC for <user_id> (n.b. the canonical user ID is email address unless the user has an API token)
# Requires Genie Kubernetes access to <namespace> in the given kubernetes <context>
# (kubectl config get-contexts and kubectl --context <context> get namespaces will show you the options)
#
# e.g. generate_user_id_hmac.sh gke_system-services-prod_us-central1_us-central1-prod staging-shard-0 <EMAIL>

# Check if the necessary input is provided
if [ "$#" -ne 3 ]; then
	echo "Usage: $0 <context> <namespace> <user_id> (<context> is a kubernetes context, <user_id> is typically an email)"
	exit 1
fi

CONTEXT=$1
NAMESPACE=$2
USER_ID=$3

# Retrieve the secret from Kubernetes
SECRET_VALUE=$(kubectl --context "$CONTEXT" get secret api-proxy-hmac-secret -n "$NAMESPACE" -o json | grep 'secret.json' | awk '{print $2}' | tr -d '"' | base64 --decode)

# Check if the secret was retrieved successfully
if [ -z "$SECRET_VALUE" ]; then
	echo "Failed to retrieve the secret from Kubernetes"
	exit 1
fi

# Compute the HMAC
HMAC=$(echo -n "$USER_ID" | openssl sha256 -hex -mac HMAC -macopt hexkey:"$SECRET_VALUE" | awk '{print $2}')

# Output the HMAC
echo "$HMAC"

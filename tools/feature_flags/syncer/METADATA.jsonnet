// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'flags-syncer-server',
      kubecfg: {
        target: '//tools/feature_flags/syncer:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['ran', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

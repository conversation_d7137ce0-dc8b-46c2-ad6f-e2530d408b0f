"""Syncer server.

The server syncs the feature flags from the default repo to launchdarkly.
"""

import argparse
import fcntl
import os
import pathlib
import threading
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager

import grpc
import structlog
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import tools.feature_flags.syncer.syncer_pb2 as syncer_pb2
import tools.feature_flags.syncer.syncer_pb2_grpc as syncer_pb2_grpc
from base.logging.struct_logging import setup_struct_logging

from tools.bazel_runner.git import app_token, checkout, checkout_pb2
from tools.feature_flags import launchdarkly_sync
from tools.feature_flags.syncer.config import Config, load_config

log = structlog.get_logger()


class SyncerThread:
    """Syncer thread."""

    def __init__(self, config: Config):
        self.config = config
        self.token_source = app_token.GitHubAppTokenSource.from_directory(
            pathlib.Path(config.github_app_path)
        )
        self.checkout = checkout.Checkout(
            base_wd=pathlib.Path(config.base_wd),
            github_user="app",
            token_source=self.token_source,
            home_path=pathlib.Path.home(),
        )
        self.wakeup_event = threading.Event()
        self.api_key = pathlib.Path(config.launchdarkly_api_key_path).read_text(
            encoding="utf-8"
        )

    def run(self):
        """Runs the syncer."""
        try:
            log.info("Setting up")
            self.checkout.setup()
            log.info("Cloning default repo")
            self.checkout.clone(
                self.config.default_repo_owner, self.config.default_repo_name
            )
            while True:
                self.wakeup_event.clear()
                log.info("Woke up")
                spec = checkout_pb2.CheckoutSpec()
                spec.commit_checkout.branch = "main"
                spec.owner = self.config.default_repo_owner
                spec.repo_name = self.config.default_repo_name
                repo_dir, _ = self.checkout.checkout(spec, checkout_id=None)
                log.info("repo_dir=%s", repo_dir)
                launchdarkly_sync.sync(
                    launchdarkly_api_key=self.api_key,
                    repo_dir=repo_dir,
                    apply_changes=self.config.apply_changes,
                )
                log.info("Waiting for next wakeup")
                self.wakeup_event.wait()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("Error on sync thread: %s", ex)
            log.exception(ex)
        finally:
            os._exit(1)

    def wakeup(self):
        """Wakes up the syncer."""
        self.wakeup_event.set()


class SyncerServices(syncer_pb2_grpc.FeatureFlagsSyncerServicer):
    """Implementation of test selection protocol."""

    def __init__(self, config: Config, syncer: SyncerThread):
        self.config = config
        self.syncer = syncer

    def SyncLatest(self, _request: syncer_pb2.SyncRequest, _context):
        """Syncs the latest flags from the default repo to launchdarkly."""
        log.info("Received SyncLatest request")
        self.syncer.wakeup()
        return syncer_pb2.SyncResponse()


@contextmanager
def _lock(base_directory: pathlib.Path):
    """Lock the cache volume against other pods.

    The cache volume is sometimes incorrectly accessible by two pods at the same time.
    """
    file_path = base_directory / "lock"
    log.info("Using lock file %s", file_path)
    file_lock = file_path.open("a")

    try:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_EX)
        log.info("File locked")
        yield
    finally:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_UN)
        file_lock.close()


def _serve(config):
    server = grpc.server(ThreadPoolExecutor(max_workers=10))
    syncer = SyncerThread(config)
    threading.Thread(target=syncer.run, daemon=True).start()
    services = SyncerServices(config, syncer)
    syncer_pb2_grpc.add_FeatureFlagsSyncerServicer_to_server(services, server)
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)
    service_names = (
        syncer_pb2.DESCRIPTOR.services_by_name["FeatureFlagsSyncer"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    server.add_insecure_port("[::]:50051")
    server.start()
    log.info("Listening on 50051")

    server.wait_for_termination()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()

    setup_struct_logging(debug=True)

    config = load_config(args.config_file)
    log.info("Config %s", config)

    with _lock(pathlib.Path(config.base_wd)):
        _serve(config)


if __name__ == "__main__":
    main()

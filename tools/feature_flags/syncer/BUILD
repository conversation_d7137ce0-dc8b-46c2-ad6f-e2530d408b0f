load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library", "py_oci_image")

proto_library(
    name = "syncer_proto",
    srcs = ["syncer.proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//tools/bazel_runner/git:checkout_proto",
    ],
)

py_grpc_library(
    name = "syncer_py_proto",
    protos = [":syncer_proto"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "//tools/bazel_runner/git:checkout_py_proto",
    ],
)

py_binary(
    name = "client",
    srcs = [
        "client.py",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":syncer_py_proto",
        "//base/logging:struct_logging",
        "//base/python/grpc:client_options",
        requirement("grpcio"),
        requirement("protobuf"),
        requirement("certifi"),
    ],
)

py_library(
    name = "config",
    srcs = [
        "config.py",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_binary(
    name = "syncer_server",
    srcs = [
        "syncer_server.py",
    ],
    deps = [
        ":config",
        ":syncer_py_proto",
        "//base/logging:struct_logging",
        "//tools/bazel_runner/git:app_token",
        "//tools/bazel_runner/git:checkout",
        "//tools/feature_flags:feature_flag_sync",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("certifi"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:ubuntu2004_ci_base_image",
    binary = ":syncer_server",
    trivy_allow_list = [
        "CVE-2024-34156",  # golang version in gojsonnet outdated. CVE not likely to be exploitable in ci server.
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//tools/deploy:github_readonly_token_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

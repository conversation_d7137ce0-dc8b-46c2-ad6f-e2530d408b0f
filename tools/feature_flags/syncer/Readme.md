# Feature flag syncer

Takes feature flag descriptions from git and applies them to LaunchDarkly.

See `syncer.proto` for the protocol.

The syncer does not currently keep track of and report sync status through the RPC.
The current plan is to provide a separate server with a web page that allows a user to observe
the actual current state of the feature flags. Eventually, it would be nice for the person
who pushed a feature flag change to get a notification that the change has been deployed.
In the meantime, they can monitor a Slack channel that publishes all feature flag changes.

The syncer does not support HA. It will do a sync on
startup in case it missed any notifications while it was unavailable.

If more than one copy of the syncer is running at a time, then there is the risk of an
older copy of the flag being deployed. To decrease the chances of more than one copy
running, the syncer attaches a persistent volume.

## Security considerations

### Where should this run

The syncer should run in production as it modifies production settings. However, most of our CI
system is currently in dev. The initial version will run in dev. AU-2486 tracks migrating the feature flags syncer to production.

### Authorization

#### Syncer gRPC service

gRPC exposed in cluster. Anyone inside cluster can connect.

The syncer does not currently support authorization. Any one who can connect to the syncer
can issue a gRPC to the syncer can kick off a sync job. This could lead to resource exhaustion in Github.

#### Prometheus scraping

Doesn't do Prometheus yet...

HTTP exposed in cluster. Anyone inside cluster can connect and read metrics.

### Authentication

#### Syncer gRPC service

The syncer currently listens over HTTP. It does not support TLS. An attacker with control over the network could trick a client into believing that syncs are happening, when they are not. This may delay the propagation of feature flags to Augment, impacting availability and stability of Augment. On the other hand, it should not significantly affect the security of the system as security sensitive settings should not be stored in feature flags, at least not once a new security sensitive feature is fully deployed.

#### Prometheus scraping

Doesn't do Prometheus yet...

HTTP - no authentication. Attacker with control over network can fake metrics.

### Permissions and secrets

Github - the app needs the ability to read the repo.

The syncer uses our Github readonly app identity to Github. Sharing Github identities between
microservices is discussed in AU-2500.

LaunchDarkly - the app needs the ability to read, create, update, and delete feature flags.

The syncer uses a LaunchDarkly API key to authenticate to LaunchDarkly. The API key
is stored in a SealedSecret. Note: API key != SDK key. API key is for management API, SDK key
is for software that uses feature flags.

### Persistence

The syncer has a persistent volume to cache the repository. However, the syncer itself is
stateless and will do a sync on startup. The persistent volume does not need to be backed up
because it is just a cache.

### Privacy considerations

It is our policy not to store privacy-sensitive data in feature flags. Feature flags
should be assumed to be public.


## Observability

AU-2485

How long did the sync take?

Did the sync fail or succeed?

Alert if sync failed.

Why did the sync fail? User will need to go to logs. TODO: make it easy to find the
relevant log lines.


## Handling failures

This section is currently aspirational - haven't verified we implement the strategies.

Git clone or update can fail.
 -  back-off and retry?
 - Escalate to human

Syncer logic can thrown an exception due to unexpected / malformed input or bugs
 - wait for next input and hope for the best
 - sync failed will be indicated in metrics
 - escalate to human

LaunchDarkly connectivity issues
 - back-off and retry
 - Increment some metric?

## Testing

We don't have automated tests for the core syncer logic yet.

Here are some desirable properties we might want to verify:
 - exceptions don't cause it to enter a hung state
 - checks out the repository
 - sync notification causes us to do a git update
 - error recovery strategy
 - server recovers from corrupted cache volume

How would a developer do manual testing outside of production CI? Perhaps we need
a dry run feature that will do almost everything except apply the changes to
LaunchDarkly.

## LaunchDarkly key rotation

```
echo -n <api-key> | kubectl create secret generic launchdarkly-api-key --dry-run=client --from-file=api-key.txt=/dev/stdin -o json | bazel run //tools:kubeseal -- --scope cluster-wide --context <context>
```

Cut and paste the encryptedData into the deploy.jsonnet

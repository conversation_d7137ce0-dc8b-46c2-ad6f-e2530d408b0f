"""Client to access the test selection server."""

from __future__ import annotations

import logging
import pathlib

import certifi
import grpc

from base.python.grpc import client_options
import tools.feature_flags.syncer.syncer_pb2 as syncer_pb2
import tools.feature_flags.syncer.syncer_pb2_grpc as syncer_pb2_grpc


def setup_clients(
    endpoint: str, insecure: bool = False
) -> syncer_pb2_grpc.FeatureFlagsSyncerStub:
    """Setup the client stub for the test selection RPC."""
    if not insecure:
        creds = grpc.ssl_channel_credentials(pathlib.Path(certifi.where()).read_bytes())
        channel = grpc.secure_channel(endpoint, creds, options=client_options.create())
    else:
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
    return syncer_pb2_grpc.FeatureFlagsSyncerStub(channel)


class SyncerException(grpc.RpcError):
    """Exception thrown during test selection.

    Note that subclasses of grpc.RpcError are expected to have code() and details() methods even
    though RpcError itself does not."""

    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.message = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.message

    def __str__(self) -> str:
        return f"SyncerException({self.status_code}: {self.message})"


class SyncerClient:
    """Client to start test selection and wait for the results."""

    def __init__(self, endpoint: str, insecure: bool = False):
        self.rpc_client = setup_clients(endpoint, insecure)

    def sync_latest(self):
        """Schedules a sync latest operation."""
        request = syncer_pb2.SyncRequest()
        try:
            self.rpc_client.SyncLatest(request)
        except grpc.RpcError as rpc_error:
            code = rpc_error.code()  # pylint: disable=no-member # type: ignore
            details = rpc_error.details()  # pylint: disable=no-member # type: ignore
            raise SyncerException(code, details) from rpc_error


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    client = SyncerClient(endpoint="localhost:50051", insecure=True)
    client.sync_latest()

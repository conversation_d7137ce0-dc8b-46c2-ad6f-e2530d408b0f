// K8S deployment file for the flags syncer server
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local githubAppSealedLib = import 'tools/deploy/github_readonly_app_sealed.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'flags-syncer';
  local githubSecret = githubAppSealedLib(cloud=cloud, namespace=namespace, appName=appName);
  local cacheDisk = {
    apiVersion: 'v1',
    kind: 'PersistentVolumeClaim',
    metadata: {
      name: 'flags-syncer-pvc',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      accessModes: [
        'ReadWriteOnce',
      ],
      resources: {
        requests: {
          storage: '8Gi',
        },
      },
      storageClassName: 'premium-rwo',
    },
  };
  local service = grpcLib.grpcService(appName=appName, namespace=namespace, ports=[50051]);
  local config =
    {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: 'flags-syncer-config',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      data: {
        'config.json': std.manifestJson({
          base_wd: '/cache/',
          github_app_path: '/github-app',
          default_repo_owner: 'augmentcode',
          default_repo_name: 'augment',
          launchdarkly_api_key_path: '/launchdarkly-api-key/api-key.txt',
          apply_changes: cloudInfo.isLeadProdCluster(cloud),
        }),
      },
    };
  local serverContainer =
    {
      name: 'flags-syncer-server',
      target: {
        name: '//tools/feature_flags/syncer:image',
        dst: 'flags-syncer-server',
      },
      env: [
        {
          name: 'AU_GPU_COUNT',
          value: '0',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        {
          name: 'cache-volume',
          mountPath: '/cache',
        },
        {
          mountPath: '/github-app',
          name: 'github-app-secret',
        },
        {
          mountPath: '/launchdarkly-api-key',
          name: 'launchdarkly-api-key-volume',
        },
      ],
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local serverPod = {
    // Don't specify the service account - use the default
    //serviceAccountName: serviceAccount.name,
    securityContext: {
      // bazel cannot be run as root
      runAsUser: 1000,
      fsGroup: 1000,
      fsGroupChangePolicy: 'OnRootMismatch',
    },
    affinity: affinity,
    tolerations: tolerations,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      serverContainer,
    ],
    volumes: [
      {
        name: 'cache-volume',
        persistentVolumeClaim: {
          claimName: 'flags-syncer-pvc',
        },
      },
      {
        name: 'github-app-secret',
        secret: {
          secretName: githubSecret.metadata.name,  // pragma: allowlist secret
          optional: false,
        },
      },
      {
        name: 'launchdarkly-api-key-volume',
        secret: {
          secretName: 'launchdarkly-api-key',  // pragma: allowlist secret
          optional: false,
        },
      },
      {
        name: 'config',
        configMap: {
          name: 'flags-syncer-config',
          items: [
            {
              key: 'config.json',
              path: 'config.json',
            },
          ],
        },
      },
    ],
  };
  local serverDeployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'flags-syncer-server',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: serverPod,
      },
    },
  };
  local launchdarklyApiKey = {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: 'launchdarkly-api-key',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
      },
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          'api-key.txt': 'AgAtSYh+SOijjpQ8rI+E4m/EsN7ZmXqocLA5Xmkibn4BlPh1ZtNI/fJGF6DO7OV6RikZ5ZcilcFGC1aYNyV3T65YdZ2t9UYlLC9akwoZNdZvJQIQ85r4aYeTnH7LJLW/bENnqcdqu/L6fjlC4kha4vWb9i0duM/aFy2BMjIndxWkf3UyhzhNZyOzBFaA5aMAHOJrR+zfIOb0CZsbGhpaRbCFnui2lUibRXNb8R0SFuI0W+PQKandwQ7vpu3kFomjhyk5hgAN9OowBsCaQxjs/XGgKqELIOB5DgQpLV8/mC9DxsjLnHqEQTNg/H+ZIpMLLuAOFD6wFGQnqUcX4+0j7SG9IeuBVd10kPX6FNi0S2hwVE392DyaFaFVaDBgoU0dTyNWcbivD09hV6K8sJesZfxWajdp80k6Zu+gAzECKfkUPrrc5fWSOMYw91k9Vxs8frwFTxJb5ruVOY4JXgP74THVY6mNaUpUvOlfraqRLDmlaTLRKVzy2b5fSUO/v4kjwxHiPJjz4XSFn0Eojdq4gT9E75PBCFRtqvKHR9nX0Cek0tllmySI4O4suy4Mgo7/nIvnHBR4FWXZHcgyafppvMIu6tdovs+e8hrNKuVtS268cXZ5hCEzDhMoMY116hYLSN+xlPpHU1YN7H+lXPEI4k2hlSXKXB6SdFULkHR/6j0N8FcQtJnjJOLF6rXAiZr4y5q08G/0TspRo/cYls/xSoRArrCzffn1eBKEOw15dzcbT5/JmF8RpYzC',  // pragma: allowlist secret
        },
        GCP_US_CENTRAL1_PROD: {
          'api-key.txt': 'AgBm8EkMa6A7uRy1GfIIuzVWYrHrLNHVWIUOF6bQoqHuTB48xVIM940SuTBefdzn6kSaUq8BKMU6S1PQFL1fV0tCYkXf9Bk+xqgAGrVaOUXnZoaD5r9jotpPdhIBfnXdkb4X2mgV0kKHreWm6ZaTJkyTDG4dfVUHd6bN1WEtJr+0rr13vH+mHtA29hPzaMohoi+HA/RklXX+Vi/**********************************+aH8u/7o19BSqQvIE2l60ytdIe0rJzL0N0gLhqTzAnAJPoyWhJuyptPRfjQTQWJPJDuHxNL41y9pNMrECXS+hiKp1bamdFaiY3k7XHALoystEgiunhewh9S4tD1XN3KwuSyJCICtpnUcRj84NRzffSkooR+33qIqXqx5mRH/nazliN00tKoivwfNYcFOv7RiiWZ2ssAvMPdovRGFN8rMcbVEXSp/O8c1/XktukC8x8fffUpDn3W4RLSz5/wJZG/zA0rxCZdxISTpIiaSZdj7hDyElo6V/7IGMFcreymRQMXiL90ivznlGBS8bMlgtReC1+VbmRZxHq4Dw7UpqJBjeMpFrETNrDzb/SFzTXtGPaxWxBmLHk1owBAwPpSbrG9h3YCK1HQ58qfmKsOAcHdK38pbNRycyqGXBithybCYu+yBLCA1lYUMYs5n7PDiYwr1I01X10TAntyUVgUcbUwKzpyEjBBSMO5+una66KBAHpoGsSEARCeC4WFVg3UkoAYMXm48OrR',  // pragma: allowlist secret
        },
      }[cloud],
    },
  };
  lib.flatten([
    githubSecret,
    launchdarklyApiKey,
    config,
    service,
    cacheDisk,
    serverDeployment,
  ])

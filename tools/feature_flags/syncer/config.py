"""Module containing the configuration for the flags syncer server."""

import pathlib
from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class Config:
    """Configuration for the server."""

    # base working directory.
    base_wd: str

    # the path to the github app. The directory should contain
    # the app id, installation id, and the private key.
    github_app_path: str

    # default repo owner (e.g. augmentcode)
    default_repo_owner: str

    # default repo name (e.g. augment)
    default_repo_name: str

    # Path to the LaunchDarkly API key
    launchdarkly_api_key_path: str

    # Determines whether changes should be applied or if it's a dry run
    apply_changes: bool


def load_config(config_file: pathlib.Path) -> Config:
    """Loads the configuration from a file."""
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )

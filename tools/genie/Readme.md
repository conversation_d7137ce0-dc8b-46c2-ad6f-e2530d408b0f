# Access Granting UI

The "Genie" systems grants your wish to access a tenant.

## Local Development
To develop genie locally you need to use your dev deploy; however, there are some special permissions required to
develop genie. You need to have this role: `clusterrolebinding.rbac.authorization.k8s.io/genie-[your dev namespace]-role-binding`

To enable this you can execute: `kubectl create clusterrolebinding genie-[your dev namespace]-role-binding   --clusterrole=genie-role   --serviceaccount=[your dev namespace]:genie-sa`

You need to replace `[your dev namespace]` with your dev namespace.

## Security

The Genie grants access to protected resources. The security is important and implemented as follows:

- IAP access: we only allow access with IAP protection and we validate the IAP token
- CSRF token: we use a CSRF token to protect against CSRF attacks

### CSRF Token

We implement a double submit protection for all POST requests. The CSRF token is stored in a cookie and is sent with all POST requests. The backend validates the CSRF token and rejects the request if it is invalid. A CSRF token is invalid if the cookie value and the request body don't match.

see https://owasp.org/www-community/attacks/csrf#:~:text=Cross%2DSite%20Request%20Forgery%20(CSRF,which%20they're%20currently%20authenticated
see https://owasp.org/www-community/attacks/csrf#:~:text=Cross%2DSite%20Request%20Forgery%20(CSRF,which%20they're%20currently%20authenticated

{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "noImplicitAny": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "jsx": "react-jsx"}, "include": ["src"], "exclude": []}
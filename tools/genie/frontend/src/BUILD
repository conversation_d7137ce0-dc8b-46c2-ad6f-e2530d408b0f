load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_project")
load("@npm//tools/genie/frontend:eslint/package_json.bzl", eslint_bin = "bin")

ASSET_PATTERNS = [
    "*.svg",
    "*.css",
]

SRC_PATTERNS = [
    "*.tsx",
    "lib/*.ts",
    "lib/*.tsx",
]

js_library(
    name = "src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    visibility = ["//tools/genie/frontend:__subpackages__"],
    deps = [
        ":src_ts",
    ],
)

ts_project(
    name = "src_ts",
    srcs = glob(
        include = SRC_PATTERNS,
    ),
    declaration = True,
    resolve_json_module = True,
    tsconfig = "//tools/genie/frontend:tsconfig",
    deps = [
        "//tools/genie/frontend:node_modules/@ant-design/icons",
        "//tools/genie/frontend:node_modules/antd",
        "//tools/genie/frontend:node_modules/axios",
        "//tools/genie/frontend:node_modules/dayjs",
        "//tools/genie/frontend:node_modules/react",
        "//tools/genie/frontend:node_modules/react-dom",
        "//tools/genie/frontend:node_modules/react-router-dom",
        "//tools/genie/frontend:node_modules/react-use",
        "//tools/genie/frontend:node_modules/strip-ansi",
    ],
)

eslint_bin.eslint_test(
    name = "eslint_test",
    args = ["{}/{}".format(
        package_name(),
        p,
    ) for p in SRC_PATTERNS],
    data = [
        "//tools/genie/frontend:node_modules/eslint-config-react-app",
        "//tools/genie/frontend:node_modules/react",
        "//tools/genie/frontend:package_json",
    ] + glob(SRC_PATTERNS),
)

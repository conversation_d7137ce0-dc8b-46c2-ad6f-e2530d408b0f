load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        "//tools/genie/backend:image",
    ],
    visibility = ["//tools/genie:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

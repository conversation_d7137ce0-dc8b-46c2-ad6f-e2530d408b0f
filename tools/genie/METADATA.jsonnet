{
  deployment: [
    {
      name: 'genie',
      kubecfg: {
        target: '//tools/genie:kubecfg',
        task: [
          {
            namespace: 'central',
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
          },
          {
            namespace: 'central',
            cloud: 'GCP_US_CENTRAL1_GSC_PROD',
            env: 'PROD',
          },
          {
            namespace: 'central',
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'luke'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

use std::time::Duration;

use bigtable_rs::{
    bigtable,
    google::bigtable::v2::{
        mutate_rows_request,
        mutation::{self, SetCell},
        row_filter::Filter,
        MutateRowsRequest, Mutation, ReadRowsRequest, RowFilter, RowSet,
    },
};
use prost::{bytes::Bytes, Message};
use tonic::{Result, Status};

use crate::config::Config;
use crate::proto::genie_store::AccessProposal;

#[derive(Clone)]
pub struct GenieStore {
    bigtable: bigtable::BigTable,
    full_table_name: String,
}

impl GenieStore {
    pub async fn new(config: Config) -> Self {
        let channel_size = 4;
        let timeout = Duration::from_secs(10);

        // let gcp_config = config.gcp.clone();
        let connection = bigtable::BigTableConnection::new(
            &config.gcp_project_id,
            &config.gcp_instance_name,
            false,
            channel_size,
            Some(timeout),
        )
        .await
        .expect("Not able to create bigtable connection");

        let bigtable = connection.client();
        let full_table_name = bigtable.get_full_table_name(&config.gcp_table_name);

        Self {
            bigtable,
            full_table_name,
        }
    }

    pub async fn get_proposal(&self, row_key: &str) -> Option<AccessProposal> {
        let mut bigtable = self.bigtable.clone();

        let request = ReadRowsRequest {
            table_name: self.full_table_name.clone(),
            rows_limit: 1,
            rows: Some(RowSet {
                row_keys: vec![row_key.as_bytes().to_vec()],
                ..RowSet::default()
            }),
            filter: Some(RowFilter {
                filter: Some(Filter::CellsPerColumnLimitFilter(1)),
            }),
            ..ReadRowsRequest::default()
        };
        let Ok(mut response) = bigtable.read_rows(request).await else {
            return None;
        };
        let (_, cells) = response.pop()?;
        let first_cell = cells.into_iter().find(|e| e.family_name == "Main")?;
        let Ok(proto) = AccessProposal::decode(Bytes::from(first_cell.value)) else {
            return None;
        };
        Some(proto)
    }

    pub async fn insert_proposal(&self, proposal: AccessProposal) -> Result<()> {
        let mut bigtable = self.bigtable.clone();

        let request = MutateRowsRequest {
            table_name: self.full_table_name.clone(),
            entries: vec![mutate_rows_request::Entry {
                row_key: proposal.uuid.as_bytes().to_vec(),
                mutations: vec![Mutation {
                    mutation: Some(mutation::Mutation::SetCell(SetCell {
                        family_name: "Main".to_string(),
                        // TODO: what does this do?
                        column_qualifier: "Proposal".to_string().into_bytes(),
                        timestamp_micros: -1, // Use Bigtable server time
                        value: proposal.encode_to_vec(),
                    })),
                }],
            }],
            ..MutateRowsRequest::default()
        };

        match bigtable.mutate_rows(request).await {
            Err(err) => {
                tracing::error!("Failed to write request: {}", err);
                Err(Status::internal(format!(
                    "Failed to write request: {}",
                    err
                )))
            }
            Ok(mut stream) => {
                while let Some(res) = stream.message().await.map_err(|e| {
                    tracing::error!("Failed to write request: {}", e);
                    Status::internal(e.to_string())
                })? {
                    tracing::debug!("Mutated rows: {:?}", res);
                }
                Ok(())
            }
        }
    }
}

function(cloud)
  [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'genie-backend-cluster-role',
      },
      rules: [
        {
          apiGroups: [
            'rbac.authorization.k8s.io',
          ],
          resources: [
            'rolebindings',
          ],
          verbs: [
            '*',
          ],
        },
        {
          apiGroups: [
            '',
          ],
          resources: [
            'namespaces',
          ],
          verbs: [
            'list',
            'get',
          ],
        },
        {
          apiGroups: [
            'eng.augmentcode.com',
          ],
          resources: [
            'supportuiaccesses',
          ],
          verbs: [
            '*',
          ],
        },
      ],
    },
  ]

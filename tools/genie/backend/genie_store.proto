// Protobuf definition controlling the on-disk state for genie

// The messages here are an implementation detail of the genie server
// and not exposed to other services or the external API
syntax = "proto3";

package genie_store;

import "base/proto/services/access.proto";
import "google/protobuf/timestamp.proto";

message AccessProposal {
  access.AccessType access = 1;
  string reason = 2;
  string uuid = 3;
  string proposer = 4;
  google.protobuf.Timestamp proposed_at = 5;
  // Optional fields, to be set once the proposal is approved
  string approver = 6;
  google.protobuf.Timestamp approved_at = 7;
}

// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../").canonicalize().unwrap();

    let protos = vec![
        root.join("tools/bot/bot.proto"),
        root.join("tools/genie/backend/genie_store.proto"),
    ];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();

        let protobuf_timestamp_dir =
            root.join("../protobuf~/src/google/protobuf/_virtual_imports/timestamp_proto/");
        let base_proto_dir = root.join("base/proto/services/");

        let includes = vec![proto_dir, &base_proto_dir, &protobuf_timestamp_dir, &root];

        tonic_build::configure()
            .extern_path(".google.protobuf.Timestamp", "::prost_wkt_types::Timestamp")
            .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")
            // Prost generates enums for oneofs as normal rust type names (PascalCase) which serde
            // will then use by default; we need to tell it to convert back to snake_case.
            .enum_attribute(".", "#[serde(rename_all = \"snake_case\")]")
            // Each oneof we add needs a line here. This is a workaround for prost's lack of
            // support for the official JSON mapping: https://github.com/tokio-rs/prost/issues/75
            .field_attribute("access.AccessType.type", "#[serde(flatten)]")
            .compile_protos(&[proto_path], &includes)?;
    }
    Ok(())
}

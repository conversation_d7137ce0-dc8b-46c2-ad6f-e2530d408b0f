load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/deploy:shard_namespace_base_kubecfg",
        "//services/deploy:tenant_config_kubecfg",
        "//services/tenant_watcher/server:kubecfg",
        "//tools/bot:kubecfg",
        "//tools/genie:kubecfg",
    ],
)

pytest_test(
    name = "genie_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "genie_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "gcp",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        requirement("kubernetes"),
    ],
)

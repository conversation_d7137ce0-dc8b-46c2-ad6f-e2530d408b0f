"""Integration test for genie."""

import time
import requests


def _test_response(url: str) -> bool:
    try:
        response = requests.get(url, timeout=60, verify=False)
        print(response)
        return response.status_code == 200
    except requests.exceptions.ConnectionError:
        return False
    except requests.exceptions.RequestException as ex:
        print(ex, flush=True)
        return False


def test_genie_up(genie_url: str):
    """Tests that the genie pod comes up correctly."""

    url = f"{genie_url}/health"
    print(f"Wait for response from {url}", flush=True)
    for _ in range(120):
        if _test_response(url):
            print(f"Got valid response from {url}", flush=True)
            break
        else:
            time.sleep(5)
            continue
    else:
        print(f"TIMEOUT testing response from {url}")

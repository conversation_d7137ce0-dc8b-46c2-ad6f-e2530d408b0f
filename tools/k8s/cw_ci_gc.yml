apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cw-ci-gc
spec:
  schedule: "@daily"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: spark-sa
          containers:
          - name: cw-ci-gc
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/augment_determined_gc:latest
            command:
              - /bin/bash
              - -c
              - /mnt/efs/augment/user/marcmac/cw_ci_gc/cw_ci_gc.sh
            volumeMounts:
            - mountPath: /mnt/efs/augment
              name: aug-cw-las1
          volumes:
          - name: aug-cw-las1
            persistentVolumeClaim:
              claimName: aug-cw-las1
          restartPolicy: Never
      backoffLimit: 1

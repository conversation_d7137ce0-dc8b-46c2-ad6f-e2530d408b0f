package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"strings"

	k8s "github.com/augmentcode/augment/base/cloud/k8s"
	logging "github.com/augmentcode/augment/base/logging"
	"github.com/rs/zerolog/log"
)

// Config holds the application flags
type Config struct {
	SecretName   string
	Namespace    string
	Clouds     []string
	InputFile    string
}

const kubesealPath = "../gazelle~~go_deps~com_github_bitnami_labs_sealed_secrets/cmd/kubeseal/kubeseal_/kubeseal"

// parseFlags parses command-line flags and returns a Config struct
func parseFlags() Config {
	secretName := flag.String("name", "", "Name of the secret")
	namespace := flag.String("namespace", "", "Namespace of the secret")
	clouds := flag.String("clouds", "", "Comma-separated list of Kubernetes contexts to use")
	flag.Parse()

	if *secretName == "" {
		log.Fatal().Msg("You must provide a secret name using -name flag")
	}

	inputFile := flag.Arg(0)
	if inputFile == "" {
		log.Fatal().Msg("You must provide a JSON input file as an argument")
	}

	var cloudList []string
	if *clouds != "" {
		for _, cloud := range strings.Split(*clouds, ",") {
			cloud = strings.Trim(cloud, " ")
			cloudList = append(cloudList, cloud)
		}
	}

	return Config{
		SecretName:   *secretName,
		Namespace:  *namespace,
		Clouds:     cloudList,
		InputFile:    inputFile,
	}
}

// validateFile checks if the input file exists and is a valid JSON file
func validateFile(filename string) {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		log.Fatal().Msgf("File %s does not exist", filename)
	}

	file, err := os.Open(filename)
	if err != nil {
		log.Fatal().Msgf("Failed to open file: %v", err)
	}
	defer file.Close()

	var js map[string]interface{}
	if err := json.NewDecoder(file).Decode(&js); err != nil {
		log.Fatal().Msgf("Invalid JSON in file: %v", err)
	}
}

// runKubeseal executes kubeseal with the specified arguments and returns the output
func runKubeseal(config Config, secretData []byte, context string) string {
	args := []string{"--format", "json"}

	if config.Namespace == "" {
		args = append(args, "--scope", "cluster-wide")
	} else {
		args = append(args, "--scope", "namespace-wide")
	}

	if context != "" {
		args = append(args, "--context", context)
	}

	log.Debug().Msgf("Running kubeseal with args: %v", args)

	cmd := exec.Command(kubesealPath, args...)
	cmd.Stdin = bytes.NewReader(secretData)

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		log.Info().Msg(out.String())
		log.Fatal().Msgf("kubeseal command failed: %v", err)
	}

	return out.String()
}

// generateSecret creates a Kubernetes secret YAML from the JSON file
func generateSecret(config Config) ([]byte, error) {
	secretTemplate := `
apiVersion: v1
kind: Secret
metadata:
  name: %s
type: Opaque
data:
`
	if config.Namespace != "" {
		secretTemplate = `
apiVersion: v1
kind: Secret
metadata:
  name: %s
  namespace: %s
type: Opaque
data:
`
	}

	secretData := fmt.Sprintf(secretTemplate, config.SecretName)
	fileContent, err := os.ReadFile(config.InputFile)
	if err != nil {
		return nil, err
	}

	var jsonData map[string]string
	if err := json.Unmarshal(fileContent, &jsonData); err != nil {
		return nil, err
	}

	for key, value := range jsonData {
		encodedValue := base64.StdEncoding.EncodeToString([]byte(value))
		secretData += fmt.Sprintf("  %s: %s\n", key, encodedValue)
	}

	return []byte(secretData), nil
}

func main() {
	logging.SetupConsoleLogging()
	config := parseFlags()
	validateFile(config.InputFile)

	secretData, err := generateSecret(config)
	if err != nil {
		log.Fatal().Msgf("Failed to generate secret: %v", err)
	}

	for _, cloud := range config.Clouds {
		fmt.Printf("Sealing secret for context: %s\n", cloud)

		context, exists := k8s.GetContextForCloud(cloud)
		if !exists {
			log.Fatal().Msgf("Unknown cloud: %s", cloud)
		}

		sealedSecret := runKubeseal(config, secretData, context)
		fmt.Println(sealedSecret)
	}
}

# kubeseal helper

This is a tool to make the usage of kubeseal easier.

## Example

Create a JSON file secret.json with the schema below. The comments are only there to satisfy our secrets checker.
```
{
    "secret_1_key": "secret_1_value", // pragma: allowlist-secret
    "secret_2_key": "secret_2_value", // pragma: allowlist-secret
    "secret_3_key": "secret_3_value" // pragma: allowlist-secret
}
```

Then, run the following command. Note that the path to `secret.json` must be absolute. Also, this command generates namespace- or cluster-wide sealed secrets,
meaning they can be renamed and still decrypted. See https://github.com/bitnami-labs/sealed-secrets?tab=readme-ov-file#scopes.
```
bazel run //tools/k8s/kubeseal -- -name augment-slack-bot-token -clouds GCP_US_CENTRAL1_DEV,GCP_US_CENTRAL1_PROD   ~/src/augment/tools/k8s/kubeseal/secret.json
```

It will print the sealed secrets as <PERSON><PERSON><PERSON> to stdout.

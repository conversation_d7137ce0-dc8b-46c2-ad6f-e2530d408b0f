apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cw-experiment-gc
spec:
  schedule: "@hourly"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cw-experiment-gc
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/augment_determined_gc:latest
            env:
            - name: DET_MASTER
              value: "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud"
            - name: DET_USER
              value: "github"
            - name: DET_PASS
              valueFrom:
                secretKeyRef:
                  name: gh-determined-password
                  key: password
          restartPolicy: Never
      backoffLimit: 1

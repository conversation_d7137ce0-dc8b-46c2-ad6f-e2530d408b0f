import json
import random
import subprocess
from dataclasses import dataclass

CONTEXTS = [
    "gke_system-services-prod_us-central1_us-central1-prod",
    "gke_system-services-prod-gsc_us-central1_prod-gsc",
    "gke_system-services-prod_europe-west4_eu-west4-prod",
]

NAMESPACES = [
    "central",
    "central-staging",
]

_KUBECTL_BIN = "../k8s_binary/file/kubectl"


def get_kubectl_json(command):
    """Runs a kubectl command and returns the JSON output."""
    result = subprocess.run(command, stdout=subprocess.PIPE, check=True)
    return json.loads(result.stdout)


def get_deployments_in_namespace(context, namespace):
    """Get all deployments in a specific namespace."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "deployments",
        "-n",
        namespace,
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


def get_nodes_in_cluster(context):
    """Get all nodes in a cluster."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "nodes",
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


@dataclass
class GPUUsage:
    namespace: str
    deployment: str
    gpus: int
    replicas: int


def main():
    """Main function."""
    for context in CONTEXTS:
        print(context)
        gpu_usages = []
        for namespace in NAMESPACES:
            deployments = get_deployments_in_namespace(context, namespace)

            for deployment in deployments["items"]:
                containers = deployment["spec"]["template"]["spec"]["containers"]

                gpus = sum(
                    [
                        int(
                            container.get("resources", {})
                            .get("limits", {})
                            .get("nvidia.com/gpu", "0")
                        )
                        for container in containers
                    ]
                )

                replicas = deployment["spec"]["replicas"]

                if gpus > 0 and replicas > 0:
                    gpu_usages.append(
                        GPUUsage(
                            namespace=namespace,
                            deployment=deployment["metadata"]["name"],
                            gpus=gpus,
                            replicas=replicas,
                        )
                    )

        for namespace in NAMESPACES:
            row_format = "{:>40} {:>15} {:>15} {:>15}"
            namespace_gpu_usages = [x for x in gpu_usages if x.namespace == namespace]
            if len(namespace_gpu_usages) == 0:
                continue
            print("  " + namespace)
            print(row_format.format("Deployment", "GPUs Per Pod", "Pods", "Total GPUs"))
            for gpu_usage in namespace_gpu_usages:
                print(
                    row_format.format(
                        gpu_usage.deployment,
                        gpu_usage.gpus,
                        gpu_usage.replicas,
                        gpu_usage.gpus * gpu_usage.replicas,
                    )
                )
        total_format = "{:>40} {:>15}"
        print("  Totals")

        gpus_required = sum(
            gpu_usage.gpus * gpu_usage.replicas for gpu_usage in gpu_usages
        )
        print(total_format.format("GPUs Required", gpus_required))
        deploy_surge = sum(gpu_usage.gpus for gpu_usage in gpu_usages)
        print(total_format.format("Deploy Surge", deploy_surge))
        model_surge = max(
            int(gpu_usage.gpus * gpu_usage.replicas / 2) for gpu_usage in gpu_usages
        )
        print(total_format.format("Model Surge", model_surge))

        # Simulate greedy assignment of pods to 8xH100 nodes.
        # TODO: actually associate pods to nodes to give us an exact current measure?
        pods_free = []
        random.seed(0)
        random.shuffle(gpu_usages)
        for gpu_usage in gpu_usages:
            found = False
            for pod_free in pods_free:
                if pod_free >= gpu_usage.gpus:
                    pod_free -= gpu_usage.gpus
                    found = True
                    break
            if not found:
                pods_free.append(8 - gpu_usage.gpus)

        print(total_format.format("Estimated Fragmentation Overhead", sum(pods_free)))
        print(
            total_format.format(
                "Overall Budget",
                gpus_required + deploy_surge + model_surge + sum(pods_free),
            )
        )

        nodes = get_nodes_in_cluster(context)
        allocatable_gpus = 0
        for node in nodes["items"]:
            allocatable_gpus += int(
                node["status"]["allocatable"].get("nvidia.com/gpu", "0")
            )
        print(total_format.format("Allocatable GPUs", allocatable_gpus))
        print()


if __name__ == "__main__":
    main()

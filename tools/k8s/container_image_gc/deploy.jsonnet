// Create k8s objects for the container image gc
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'container-image-gc';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=true,
    overridePrefix='c-img-gc',
  );
  local ram_limit_gb = 1;
  local cpu_limit = 0.1;
  local buildImageRepoName = 'projects/%s/locations/%s/repositories/%s' % [cloudInfo[cloud].projectId, cloudInfo[cloud].region, if cloudInfo.isLeadCluster(cloud) then 'build-images' else 'build-images-%s' % cloudInfo[cloud].shortName];
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: appName + '-config',
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJson({
        repo: buildImageRepoName,
        dry_run: false,
        ttl: '24h',
      }),
    },
  };
  local container =
    {
      name: appName,
      target: {
        name: '//tools/k8s/container_image_gc:image',
        dst: 'container-image-gc-image',
      },
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      args: [
        '--config',
        '/config/config.json',
        '--kubeconfig',
        '',
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      resources: {
        limits: {
          cpu: cpu_limit,
          memory: '%sGi' % ram_limit_gb,
        },
      },
    };
  local clusterRoleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRoleBinding',
    metadata: {
      name: 'container-image-gc-cluster-role-binding',
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      kind: 'ClusterRole',
      name: 'container-image-gc-role',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
        namespace: namespace,
      },
    ],
  };

  local iamPolicy = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: appName + '-policy',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'ArtifactRegistryRepository',
        external: buildImageRepoName,
      },
      bindings: [
        {
          role: 'roles/artifactregistry.admin',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    },
  };
  local pod =
    {
      restartPolicy: 'Never',
      containers: [
        container,
      ],
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: serviceAccount.name,
      volumes: [
        {
          name: 'config',
          configMap: {
            name: appName + '-config',
          },
        },
      ],
    };
  local cronjob =
    {
      apiVersion: 'batch/v1',
      kind: 'CronJob',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        suspend: env != 'PROD',
        schedule: '0 9 * * *',
        startingDeadlineSeconds: 120,
        successfulJobsHistoryLimit: 16,
        failedJobsHistoryLimit: 4,
        concurrencyPolicy: 'Forbid',
        timeZone: 'America/Los_Angeles',
        jobTemplate: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: {
            backoffLimit: 0,
            template: {
              metadata: {
                labels: {
                  app: appName,
                },
              },
              spec: pod,
            },
          },
        },
      },
    };
  lib.flatten([
    config,
    iamPolicy,
    clusterRoleBinding,
    serviceAccount.objects,
    cronjob,
  ])

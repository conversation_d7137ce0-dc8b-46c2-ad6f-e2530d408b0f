package main

import (
	"context"
	"encoding/json"
	"flag"
	"io"
	"os"
	"time"

	artifactregistry "cloud.google.com/go/artifactregistry/apiv1"
	"cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb"
	"github.com/benbjohnson/clock"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/iterator"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/google/go-containerregistry/pkg/authn"
	"github.com/google/go-containerregistry/pkg/crane"
	"github.com/google/go-containerregistry/pkg/name"
)

type Config struct {
	Repo                 string `json:"repo"`
	ContainerImagePrefix string `json:"container_image_prefix"`
	DryRun               bool   `json:"dry_run"`
	TTL                  string `json:"ttl"`
}

func deleteImage(config Config, imageUri string, craneAuth crane.Option) error {
	log.Info().Msgf("Deleting image %s", imageUri)

	// Parse the image name
	ref, err := name.ParseReference(imageUri)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error parsing image name %s", imageUri)
	}
	log.Info().Msgf("Parsed image name %s", ref.Context().RepositoryStr())

	if config.DryRun {
		log.Info().Msgf("Dry run, skipping delete of image %s", imageUri)
		return nil
	}

	// Delete the image
	err = crane.Delete(imageUri, craneAuth)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error deleting image %s", imageUri)
	}

	log.Info().Msgf("Deleted image %s", imageUri)
	return nil
}

type ListImagesClient interface {
	ListDockerImages(ctx context.Context, repo string) ([]*artifactregistrypb.DockerImage, error)
}

type ArtifactRegistryClient struct {
	*artifactregistry.Client
}

func (c *ArtifactRegistryClient) ListDockerImages(ctx context.Context, repo string) ([]*artifactregistrypb.DockerImage, error) {

	result := []*artifactregistrypb.DockerImage{}
	req := &artifactregistrypb.ListDockerImagesRequest{
		Parent: repo,
	}
	resp := c.Client.ListDockerImages(ctx, req)
	for {
		img, err := resp.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, err
		}
		result = append(result, img)
	}
	return result, nil
}

func listImages(ctx context.Context, artifactRegistryClient ListImagesClient, config Config, clock clock.Clock, usedImages map[string]bool) ([]string, error) {
	ttl, err := time.ParseDuration(config.TTL)
	if err != nil {
		log.Fatal().Err(err).Msg("Error parsing TTL")
	}

	images, err := artifactRegistryClient.ListDockerImages(ctx, config.Repo)
	if err != nil {
		return nil, err
	}

	result := []string{}
	for _, img := range images {
		log.Debug().Msgf("Found image %v", img)

		imageName := img.GetName()
		uploadTime := img.GetUploadTime().AsTime().UTC()

		log.Info().Msgf("Found image %s uploaded at %s, uri %s", imageName, uploadTime, img.Uri)

		// Check if the image is used by any pod
		if _, found := usedImages[img.Uri]; found {
			log.Info().Msgf("Image %s is used by a pod", imageName)
			continue
		}

		// we do not want to delete the latest image
		if len(img.Tags) > 0 {
			log.Info().Msgf("Image %s has still has tags %v", imageName, img.Tags)
			continue
		}

		if clock.Since(uploadTime) < ttl {
			log.Info().Msgf("Image %s is younger than TTL (%s hours)", imageName, ttl)
			continue
		}

		result = append(result, img.Uri)
	}
	return result, nil
}

func main() {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	var kubeConfigPath, configPath string

	flag.StringVar(&kubeConfigPath, "kubeconfig", "tools/deploy/auth_kube_config.yaml", "Path to kubeconfig file")
	flag.StringVar(&configPath, "config", "", "Path to the config file")
	flag.Parse()

	log.Info().Msg("Starting GC for container images")

	if configPath == "" {
		log.Fatal().Msg("Missing config file")
	}

	// Load configuration
	configFile, err := os.Open(configPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer configFile.Close()

	configData, err := io.ReadAll(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading config file")
	}

	var config Config
	if err := json.Unmarshal(configData, &config); err != nil {
		log.Fatal().Err(err).Msg("Error unmarshalling config file")
	}

	// Create a context
	ctx := context.Background()

	// Set up Kubernetes client
	var kubeConfig *rest.Config
	if kubeConfigPath == "" {
		kubeConfig, err = rest.InClusterConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating Kubernetes config")
		}
	} else {
		kubeConfig, err = clientcmd.BuildConfigFromFlags("", kubeConfigPath)
		if err != nil {
			log.Fatal().Err(err).Msg("Error building Kubernetes config")
		}
	}
	kubeClient, err := kubernetes.NewForConfig(kubeConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating Kubernetes client")
	}

	creds, err := google.FindDefaultCredentials(ctx, "https://www.googleapis.com/auth/cloud-platform")
	if err != nil {
		log.Fatal().Msgf("Error finding default credentials: %v", err)
	}
	tokenSource := creds.TokenSource
	token, err := tokenSource.Token()
	if err != nil {
		log.Fatal().Msgf("Error obtaining token: %v", err)
	}

	auth := &authn.Basic{
		Username: "oauth2accesstoken",
		Password: token.AccessToken,
	}
	craneAuth := crane.WithAuth(auth)

	// Set up Artifact Registry client
	artifactRegistryClient, err := artifactregistry.NewClient(ctx)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating Artifact Registry client")
	}
	defer artifactRegistryClient.Close()

	clock := clock.New()

	log.Info().Msg("Listing pods")

	// Extract all image names used by the pods
	usedImages := make(map[string]bool)
	podCount := 0

	// Get all pods in the cluster
	pods, err := kubeClient.CoreV1().Pods("").List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Fatal().Err(err).Msg("Error listing pods")

	}

	for {
		podCount += len(pods.Items)
		for _, pod := range pods.Items {
			for _, container := range pod.Spec.Containers {
				log.Info().Msgf("Pod %s/%s uses image %s", pod.Namespace, pod.Name, container.Image)
				usedImages[container.Image] = true
			}
		}
		if pods.Continue == "" {
			break
		}
		pods, err = kubeClient.CoreV1().Pods("").List(ctx, metav1.ListOptions{Continue: pods.Continue})
		if err != nil {
			log.Fatal().Err(err).Msg("Error listing pods")
		}
	}
	log.Info().Msgf("Found %d pods using %d images", podCount, len(usedImages))

	uris, err := listImages(ctx, &ArtifactRegistryClient{artifactRegistryClient}, config, clock, usedImages)
	if err != nil {
		log.Fatal().Err(err).Msg("Error listing images")
	}

	log.Info().Msgf("Found %d images to delete", len(uris))

	for _, uri := range uris {
		if err := deleteImage(config, uri, craneAuth); err != nil {
			log.Fatal().Err(err).Msg("Error deleting image")
		}
	}
}

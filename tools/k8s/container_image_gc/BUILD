load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "container_image_gc_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/k8s/container_image_gc",
    visibility = ["//visibility:private"],
    deps = [
        "@com_github_benb<PERSON><PERSON>son_clock//:clock",
        "@com_github_google_go_containerregistry//pkg/authn",
        "@com_github_google_go_containerregistry//pkg/crane",
        "@com_github_google_go_containerregistry//pkg/name",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_artifactregistry//apiv1",
        "@com_google_cloud_go_artifactregistry//apiv1/artifactregistrypb",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/clientcmd",
        "@org_golang_google_api//iterator",
        "@org_golang_x_oauth2//google",
    ],
)

go_test(
    name = "container_image_gc_test",
    srcs = ["main_test.go"],
    embed = [":container_image_gc_lib"],
    deps = [
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_golang_mock//gomock",
        "@com_github_stretchr_testify//assert",
        "@com_google_cloud_go_artifactregistry//apiv1/artifactregistrypb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_binary(
    name = "container_image_gc",
    embed = [":container_image_gc_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":container_image_gc",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
    ],
)

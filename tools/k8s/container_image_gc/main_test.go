package main

import (
	"context"
	"testing"
	"time"

	"cloud.google.com/go/artifactregistry/apiv1/artifactregistrypb"
	"github.com/benbjohnson/clock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type mockArtifactRegistryClient struct {
	images []*artifactregistrypb.DockerImage
}

func (m *mockArtifactRegistryClient) ListDockerImages(ctx context.Context, repo string) ([]*artifactregistrypb.DockerImage, error) {
	return m.images, nil
}

func TestListImages(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClock := clock.NewMock()
	now := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)
	mockClock.Set(now)

	mockClient := &mockArtifactRegistryClient{
		images: []*artifactregistrypb.DockerImage{
			{
				Name: "test-prefix/test-image:1.0.0",
				UploadTime: &timestamppb.Timestamp{
					Seconds: now.Unix() - 60,
					Nanos:   0,
				},
				Uri: "test-prefix/test-image:1.0.0",
			},
			{
				Name: "test-prefix/test-image:2.0.0",
				UploadTime: &timestamppb.Timestamp{
					Seconds: now.Unix() - 10000,
					Nanos:   0,
				},
				Uri: "test-prefix/test-image:2.0.0",
			},
			{
				Name: "test-prefix/test-image:3.0.0",
				UploadTime: &timestamppb.Timestamp{
					Seconds: now.Unix() - 10000,
					Nanos:   0,
				},
				Uri:  "test-prefix/test-image:3.0.0",
				Tags: []string{"latest"},
			},
		},
	}

	ctx := context.Background()
	config := Config{
		Repo:                 "test-repo",
		ContainerImagePrefix: "test-prefix",
		DryRun:               true,
		TTL:                  "1h",
	}

	images, err := listImages(ctx, mockClient, config, mockClock, map[string]bool{})
	assert.NoError(t, err)
	assert.Len(t, images, 1)
	assert.Equal(t, "test-prefix/test-image:2.0.0", images[0])
}

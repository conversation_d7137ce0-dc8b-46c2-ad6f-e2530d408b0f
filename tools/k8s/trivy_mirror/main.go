package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"time"

	"github.com/augmentcode/augment/base/logging"
	"github.com/google/go-containerregistry/pkg/authn"
	"github.com/google/go-containerregistry/pkg/crane"
	"github.com/google/go-containerregistry/pkg/name"
	v1 "github.com/google/go-containerregistry/pkg/v1"
	"github.com/google/go-containerregistry/pkg/v1/remote"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"

	"golang.org/x/oauth2/google"
)

const (
	sourceRepo = "ghcr.io/aquasecurity/trivy-db"
)

var (
	mirrorJobTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "trivy_mirror_job_total",
			Help: "The total number of Trivy mirror job attempts",
		},
		[]string{"status"},
	)
)

type Config struct {
	DryRun     bool   `json:"dry_run"`
	TargetRepo string `json:"target_repo"`
}

func pullAndMirrorTrivy(ctx context.Context, config Config) error {
	// Authenticate with Google Cloud
	creds, err := google.FindDefaultCredentials(ctx, "https://www.googleapis.com/auth/cloud-platform")
	if err != nil {
		return fmt.Errorf("error finding default credentials: %v", err)
	}
	tokenSource := creds.TokenSource
	token, err := tokenSource.Token()
	if err != nil {
		return fmt.Errorf("error obtaining token: %v", err)
	}

	auth := &authn.Basic{
		Username: "oauth2accesstoken",
		Password: token.AccessToken,
	}

	// Get the latest tag from the source repository
	sourceTags, err := withRetries("listing tags", func() ([]string, error) {
		return crane.ListTags(sourceRepo)
	})
	if err != nil {
		return err
	}
	if len(sourceTags) == 0 {
		return fmt.Errorf("no tags found in source repository")
	}
	latestTag := sourceTags[len(sourceTags)-1]

	sourceImage := fmt.Sprintf("%s:%s", sourceRepo, latestTag)
	targetImage := fmt.Sprintf("%s:%s", config.TargetRepo, latestTag)

	// Check if the image already exists in the target repository
	ref, err := name.ParseReference(targetImage)
	if err != nil {
		return fmt.Errorf("error parsing target image: %v", err)
	}

	curr, err := withRetries("getting target image", func() (*v1.Descriptor, error) {
		return remote.Head(ref, remote.WithAuth(auth))
	})
	if err != nil {
		return err
	}
	currDigest := ""
	if curr != nil {
		currDigest = curr.Digest.String()
	}

	// Pull the source image
	img, err := withRetries("pulling image", func() (v1.Image, error) {
		return crane.Pull(sourceImage)
	})
	if err != nil {
		return err
	}

	// Get new image digest
	newDigest, err := img.Digest()
	if err != nil {
		return fmt.Errorf("error pulling image digest: %v", err)
	}

	// Check if the image already exists in the target repository and has the same digest
	if currDigest != "" && currDigest == newDigest.String() {
		log.Info().Msgf("Image %s already exists in target repository with matching digest %s", targetImage, currDigest)
		return nil
	}

	log.Info().Msgf("Image exists but digests don't match. Source: %s, Target: %s. Will update.", newDigest, currDigest)

	// Push the new image to the target repository
	_, err = withRetries("pushing image", func() (struct{}, error) {
		return struct{}{}, crane.Push(img, targetImage, crane.WithAuth(auth))
	})
	if err != nil {
		return err
	}

	log.Info().Msgf("Successfully mirrored Trivy database to %s", targetImage)
	return nil
}

func withRetries[T any](operation string, f func() (T, error)) (T, error) {
	maxRetries := 20
	retryDelay := time.Second
	var result T
	var err error

	for i := 0; i < maxRetries; i++ {
		result, err = f()
		if err == nil {
			return result, nil
		}

		log.Info().Msgf("Error during %s (attempt %d/%d): %v", operation, i+1, maxRetries, err)
		if i < maxRetries-1 {
			time.Sleep(retryDelay)
			retryDelay *= 2 // Exponential backoff
		}
	}
	return result, fmt.Errorf("error during %s after %d attempts: %v", operation, maxRetries, err)
}

func main() {
	logging.SetupServerLogging()

	var configPath string
	flag.StringVar(&configPath, "config", "", "Path to the config file")
	flag.Parse()

	if configPath == "" {
		log.Fatal().Msg("Missing config file")
	}
	ctx := context.Background()

	configFile, err := os.ReadFile(configPath)
	if err != nil {
		log.Fatal().Msgf("Error reading config file: %v", err)
	}

	var config Config
	if err := json.Unmarshal(configFile, &config); err != nil {
		log.Fatal().Msgf("Error parsing config file: %v", err)
	}

	err = pullAndMirrorTrivy(ctx, config)
	if err != nil {
		mirrorJobTotal.WithLabelValues("ERROR").Inc()
		log.Fatal().Msgf("Error mirroring Trivy database: %v", err)
	}
	mirrorJobTotal.WithLabelValues("OK").Inc()
}

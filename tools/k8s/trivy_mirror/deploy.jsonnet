// Create k8s objects for the trivy mirror
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'trivy-mirror';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=true,
    overridePrefix='trvy-mir',
  );
  local ram_limit_gb = 1;
  local cpu_limit = 0.1;
  local buildImageRepoName = 'projects/%s/locations/%s/repositories/%s' % [cloudInfo[cloud].projectId, cloudInfo[cloud].region, if cloudInfo.isLeadCluster(cloud) then 'build-images' else 'build-images-%s' % cloudInfo[cloud].shortName];
  local config = {
    target_repo: '%s-docker.pkg.dev/%s/%s/trivy-db' % [cloudInfo[cloud].region, cloudInfo[cloud].projectId, if cloudInfo.isLeadCluster(cloud) then 'build-images' else 'build-images-%s' % cloudInfo[cloud].shortName],
    dry_run: true,
  };

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container =
    {
      name: appName,
      target: {
        name: '//tools/k8s/trivy_mirror:image',
        dst: 'trivy-mirror-image',
      },
      volumeMounts: [
        configMap.volumeMountDef,
      ],
      args: [
        '--config',
        '/config/config.json',
        '',
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      resources: {
        limits: {
          cpu: cpu_limit,
          memory: '%sGi' % ram_limit_gb,
        },
      },
    };
  local clusterRoleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRoleBinding',
    metadata: {
      name: 'trivy-mirror-cluster-role-binding',
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      kind: 'ClusterRole',
      name: 'trivy-mirror-role',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
        namespace: namespace,
      },
    ],
  };

  local iamPolicy = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: appName + '-policy',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'ArtifactRegistryRepository',
        external: buildImageRepoName,
      },
      bindings: [
        {
          role: 'roles/artifactregistry.admin',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    },
  };
  local pod =
    {
      restartPolicy: 'Never',
      containers: [
        container,
      ],
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: serviceAccount.name,
      volumes: [
        configMap.podVolumeDef,

      ],
    };
  local cronjob =
    {
      apiVersion: 'batch/v1',
      kind: 'CronJob',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        schedule: '48 15 * * *',
        concurrencyPolicy: 'Forbid',
        startingDeadlineSeconds: 120,
        successfulJobsHistoryLimit: 16,
        failedJobsHistoryLimit: 4,
        timeZone: 'America/Los_Angeles',
        jobTemplate: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: {
            backoffLimit: 0,
            template: {
              metadata: {
                labels: {
                  app: appName,
                },
              },
              spec: pod,
            },
          },
        },
      },
    };
  lib.flatten([
    configMap.objects,
    iamPolicy,
    clusterRoleBinding,
    serviceAccount.objects,
    cronjob,
  ])

local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local mirrorFailureSpec = {
    displayName: 'Trivy Mirror Job Failure',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum(increase(trivy_mirror_job_total{status="OK"}[48h])) == 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      mirrorFailureSpec,
      'trivy-mirror-job-failure',
      'Trivy mirror job has not succeeded in the last 2 days'
    ),
  ]

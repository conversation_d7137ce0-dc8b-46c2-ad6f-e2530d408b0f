load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

go_library(
    name = "trivy_mirror_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/k8s/trivy_mirror",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "@com_github_google_go_containerregistry//pkg/authn",
        "@com_github_google_go_containerregistry//pkg/crane",
        "@com_github_google_go_containerregistry//pkg/name",
        "@com_github_google_go_containerregistry//pkg/v1:pkg",
        "@com_github_google_go_containerregistry//pkg/v1/remote",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_rs_zerolog//log",
        "@org_golang_x_oauth2//google",
    ],
)

go_binary(
    name = "trivy_mirror",
    embed = [":trivy_mirror_lib"],
    visibility = ["//visibility:private"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":trivy_mirror",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    visibility = [
        "//services/auth/central:__subpackages__",
    ],
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

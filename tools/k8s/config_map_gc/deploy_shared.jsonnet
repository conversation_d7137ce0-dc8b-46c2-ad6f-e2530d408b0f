function(cloud)
  [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'configmap-gc-role',
      },
      rules: [
        {
          apiGroups: [''],
          resources: ['configmaps'],
          verbs: ['list', 'delete'],
        },
        {
          apiGroups: [''],
          resources: ['pods'],
          verbs: ['list'],
        },
        {
          apiGroups: ['batch'],
          resources: ['cronjobs'],
          verbs: ['list'],
        },
      ],
    },
  ]

{
  deployment: [
    {
      name: 'config-map-image-gc',
      kubecfg: {
        target: '//tools/k8s/config_map_gc:kubecfg',
        task: [
          {
            cloud: 'ALL_GCP',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['dirk'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'config-map-image-gc-shared',
      kubecfg: {
        target: '//tools/k8s/config_map_gc:kubecfg_shared',
        task: [
          {
            cloud: 'ALL_GCP',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['dirk'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"github.com/benbjohnson/clock"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

type Config struct {
	Namespace    string `json:"namespace"`
	DryRun       bool   `json:"dry_run"`
	TTL          string `json:"ttl"`
	ScanInterval string `json:"scan_interval"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`
}

var runCounter = prometheus.NewCounter(
	prometheus.CounterOpts{
		Name: "config_map_gc_runs",
		Help: "Number of runs",
	},
)

var deleteCounter = prometheus.NewCounter(
	prometheus.CounterOpts{
		Name: "config_map_gc_deleted",
		Help: "Number of deleted configmaps",
	},
)

func main() {
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	// Define and parse flags
	kubeConfigPath := flag.String("kubeconfig", "", "path to the kubeconfig file")
	configPath := flag.String("config", "", "Path to the config file")
	healthFile := flag.String("health-file", "", "Path to a file to write a health check to")
	flag.Parse()

	if configPath == nil || *configPath == "" {
		log.Fatal().Msg("Missing config file")
	}

	configFile, err := os.Open(*configPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer configFile.Close()

	configData, err := io.ReadAll(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading config file")
	}

	var config Config
	if err := json.Unmarshal(configData, &config); err != nil {
		log.Fatal().Err(err).Msg("Error unmarshalling config file")
	}

	log.Info().Msgf("Config: %v", config)

	// Get Kubernetes client
	kubeconfig, err := getKubeConfig(*kubeConfigPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get Kubernetes config")
	}
	clientset, err := kubernetes.NewForConfig(kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Kubernetes client")
	}

	clock := clock.New()

	scanInterval, err := time.ParseDuration(config.ScanInterval)
	if err != nil {
		log.Fatal().Err(err).Msg("Error parsing ScanInterval")
	}

	ttl, err := time.ParseDuration(config.TTL)
	if err != nil {
		log.Fatal().Err(err).Msg("Error parsing TTL")
	}

	prometheus.MustRegister(deleteCounter)

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
			os.Exit(1)
		}
	}()

	gc := NewGarbageCollector(clientset, config.Namespace, ttl, config.DryRun, clock)

	if healthFile != nil && *healthFile != "" {
		log.Info().Msgf("Writing health file to %s", *healthFile)
		if err := os.WriteFile(*healthFile, []byte{}, 0o644); err != nil {
			log.Fatal().Err(err).Msg("Failed to create health file")
		}
	}

	err = gc.garbageCollectConfigMaps()
	if err != nil {
		log.Fatal().Err(err).Msg("Error during garbage collection")
	}

	ticker := time.NewTicker(scanInterval)
	defer ticker.Stop()

	for range ticker.C {
		err := gc.garbageCollectConfigMaps()
		if err != nil {
			log.Fatal().Err(err).Msg("Error during garbage collection")
		}
	}

	log.Info().Msg("Done")
}

func getKubeConfig(kubeconfig string) (*rest.Config, error) {
	// Use in-cluster config if kubeconfig is empty, otherwise use kubeconfig file
	if kubeconfig == "" {
		return rest.InClusterConfig()
	} else {
		return clientcmd.BuildConfigFromFlags("", kubeconfig)
	}
}

type GarbageCollector struct {
	clientset kubernetes.Interface
	namespace  string
	ttl       time.Duration
	clock     clock.Clock
	dryrun    bool
}

func NewGarbageCollector(clientset kubernetes.Interface, namespace string, ttl time.Duration, dryrun bool, clock clock.Clock) *GarbageCollector {
	return &GarbageCollector{
		clientset: clientset,
		namespace:  namespace,
		ttl:       ttl,
		clock:     clock,
		dryrun:    dryrun,
	}
}

func (gc *GarbageCollector) collectUsedConfigMaps(ctx context.Context) (map[string]struct{}, error) {
	// Get all pods
	pods, err := gc.clientset.CoreV1().Pods(gc.namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Error().Err(err).Msg("Error listing pods")
		return nil, err
	}

	// Create a set of used configmaps
	usedConfigMaps := make(map[string]struct{})

	// Check pods for configmap usage
	for _, pod := range pods.Items {
		for _, volume := range pod.Spec.Volumes {
			if volume.ConfigMap != nil {
				cmName := volume.ConfigMap.Name
				cmNamespace := pod.Namespace
				key := fmt.Sprintf("%s/%s", cmNamespace, cmName)
				log.Debug().Msgf("Pod %s/%s uses ConfigMap %s", pod.Namespace, pod.Name, key)
				usedConfigMaps[key] = struct{}{}
			}
		}
	}

	// Get all cronjobs
	cronjobs, err := gc.clientset.BatchV1().CronJobs(gc.namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Error().Err(err).Msg("Error listing cronjobs")
		return nil, err
	}

	// Check cronjobs for configmap usage
	for _, cronjob := range cronjobs.Items {
		jobTemplate := cronjob.Spec.JobTemplate.Spec.Template.Spec
		for _, volume := range jobTemplate.Volumes {
			if volume.ConfigMap != nil {
				cmName := volume.ConfigMap.Name
				cmNamespace := cronjob.Namespace
				key := fmt.Sprintf("%s/%s", cmNamespace, cmName)
				log.Debug().Msgf("CronJob %s/%s uses ConfigMap %s", cronjob.Namespace, cronjob.Name, key)
				usedConfigMaps[key] = struct{}{}
			}
		}
	}

	return usedConfigMaps, nil
}

func (gc *GarbageCollector) garbageCollectConfigMaps() error {
	runCounter.Inc()
	log.Info().Msg("Starting garbage collection")
	ctx := context.Background()

	// Get all configmaps with the label "to-gc"
	configMaps, err := gc.clientset.CoreV1().ConfigMaps(gc.namespace).List(ctx, metav1.ListOptions{
		LabelSelector: "to-gc",
	})
	if err != nil {
		log.Error().Err(err).Msg("Error listing configmaps")
		return err
	}

	// Filter out configmaps newer than the TTL
	oldConfigMaps := []metav1.Object{}
	for _, cm := range configMaps.Items {
		log.Debug().Msgf("ConfigMap %s/%s was created at %s", cm.Namespace, cm.Name, cm.CreationTimestamp.Time)
		if gc.clock.Since(cm.CreationTimestamp.Time) > gc.ttl {
			log.Debug().Msgf("ConfigMap %s/%s is older than TTL", cm.Namespace, cm.Name)
			oldConfigMaps = append(oldConfigMaps, &cm)
		}
	}

	if len(oldConfigMaps) == 0 {
		log.Info().Msg("No configmaps to delete")
		return nil
	}

	usedConfigMaps, err := gc.collectUsedConfigMaps(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error collecting used configmaps")
		return err
	}

	log.Info().Msgf("Found %d used configmaps", len(usedConfigMaps))

	// Find and log or delete configmaps with the "to-gc" label that are not used by any pod
	for _, cm := range oldConfigMaps {
		key := fmt.Sprintf("%s/%s", cm.GetNamespace(), cm.GetName())
		if _, isUsed := usedConfigMaps[key]; !isUsed {
			deleteCounter.Inc()
			if gc.dryrun {
				log.Info().
					Msgf("ConfigMap %s is not used by any pod and beyond TTL (dry run, not deleted)", key)
			} else {
				log.Info().
					Msgf("ConfigMap %s is not used by any pod and is older than TTL (deleting)", key)
				err := gc.clientset.CoreV1().ConfigMaps(cm.GetNamespace()).Delete(ctx, cm.GetName(), metav1.DeleteOptions{})
				if err != nil {
					log.Fatal().
						Err(err).
						Msgf("Failed to delete ConfigMap: %s", key)
				}
			}
		}
	}
	log.Info().Msg("Finished garbage collection")

	return nil
}

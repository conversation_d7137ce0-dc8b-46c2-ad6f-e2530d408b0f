package main

import (
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON>son/clock"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/fake"
	k8s_testing "k8s.io/client-go/testing"
)

func TestGarbageCollectConfigMaps(t *testing.T) {
	mockClock := clock.NewMock()
	// Create a fake client with predefined configmaps and pods
	client := fake.NewSimpleClientset(
		&corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:              "old-unused-cm",
				Namespace:         "default",
				CreationTimestamp: metav1.Time{Time: mockClock.Now().Add(-48 * time.Hour)},
				Labels:            map[string]string{"to-gc": "true"},
			},
		},
		&corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:              "new-unused-cm",
				Namespace:         "default",
				CreationTimestamp: metav1.Time{Time: mockClock.Now()},
				Labels:            map[string]string{"to-gc": "true"},
			},
		},
		&corev1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:              "old-used-cm",
				Namespace:         "default",
				CreationTimestamp: metav1.Time{Time: mockClock.Now().Add(-48 * time.Hour)},
				Labels:            map[string]string{"to-gc": "true"},
			},
		},
		&corev1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pod-using-cm",
				Namespace: "default",
			},
			Spec: corev1.PodSpec{
				Volumes: []corev1.Volume{
					{
						Name: "config-volume",
						VolumeSource: corev1.VolumeSource{
							ConfigMap: &corev1.ConfigMapVolumeSource{
								LocalObjectReference: corev1.LocalObjectReference{
									Name: "old-used-cm",
								},
							},
						},
					},
				},
			},
		},
	)

	tests := []struct {
		name      string
		dryrun    bool
		wantDeletions []string
	}{
		{
			name:      "dryrun mode",
			dryrun:    true,
			wantDeletions: []string{},
		},
		{
			name:      "delete old unused configmaps",
			dryrun:    false,
			wantDeletions: []string{"default/old-unused-cm"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock the deletion action
			deleted := []string{}
			client.PrependReactor("delete", "configmaps", func(action k8s_testing.Action) (bool, runtime.Object, error) {
				deleteAction := action.(k8s_testing.DeleteAction)
				deleted = append(deleted, deleteAction.GetNamespace()+"/"+deleteAction.GetName())
				return true, nil, nil
			})

			gc := NewGarbageCollector(client, "default", 24*time.Hour, tt.dryrun, mockClock)
			err := gc.garbageCollectConfigMaps()
			if err != nil {
				t.Fatalf("garbageCollectConfigMaps() error = %v", err)
			}

			if tt.dryrun {
				if len(deleted) != 0 {
					t.Errorf("Expected no deletions in dryrun mode, but got: %v", deleted)
				}
			} else {
				if len(deleted) != len(tt.wantDeletions) {
					t.Errorf("Expected deletions: %v, but got: %v", tt.wantDeletions, deleted)
				} else {
					for i, d := range deleted {
						if d != tt.wantDeletions[i] {
							t.Errorf("Expected deletion: %v, but got: %v", tt.wantDeletions[i], d)
						}
					}
				}
			}
		})
	}
}

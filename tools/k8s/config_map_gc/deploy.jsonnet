local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(namespace, env, cloud, namespace_config)
  local appName = 'config-map-gc';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=false);
  local binding = if env == 'PROD' then {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRoleBinding',
    metadata: {
      name: 'configmap-gc-clusterrolebinding',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      name: 'configmap-gc-role',
      kind: 'ClusterRole',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
        namespace: namespace,
      },
    ],
  } else {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: 'configmap-gc-rolebinding',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      name: 'configmap-gc-role',
      kind: 'ClusterRole',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
        namespace: namespace,
      },
    ],
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    namespace: if env == 'DEV' then namespace else '',
    dry_run: false,
    ttl: if env == 'DEV' then '1m' else '1h',
    scan_interval: if env == 'DEV' then '2m' else '1h',
    prom_port: 9090,
  });
  local container =
    {
      name: appName,
      target: {
        name: '//tools/k8s/config_map_gc:image',
        dst: 'config-map-gc',
      },
      args: [
        '--health-file',
        '/tmp/health',
        '--config',
        configMap.filename,
      ],
      livenessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        periodSeconds: 20,
      },
      volumeMounts: [
        configMap.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 0.4,
          memory: '1024Mi',
        },
      },
    };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      configMap.podVolumeDef,
    ],
  };
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten(
    [
      configMap.objects,
      deployment,
      binding,
      serviceAccount.objects,
    ]
  )

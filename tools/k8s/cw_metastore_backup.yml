apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cw-metastore-backup
  labels:
    app.kubernetes.io/name: cw-metastore-backup
spec:
  schedule: "@weekly"
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: topology.kubernetes.io/region
                    operator: In
                    values:
                    - LAS1
          initContainers:
          - name: cw-metastore-backup
            image: docker.io/bitnami/postgresql:15.3.0-debian-11-r7
            command: ["/bin/bash", "-c", "time pg_dump -v -j $(nproc) -Fd -f /dump_dir/pgdump_$(date +%Y-%m-%d-%H-%M-%S)"]
            volumeMounts:
            - mountPath: /dump_dir
              name: dump-volume
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: metastore-postgresql
                  key: password
            - name: PGUSER
              value: augment
            - name: PGDATABASE
              value: metastore
            - name: PGHOST
              value: metastore-postgresql
          containers:
          - name: cw-aws-sync
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/aws_cli:latest
            args:
              - --endpoint-url
              - https://object.las1.coreweave.com
              - s3
              - sync
              - /dump_dir/
              - s3://metastore-backup/
            volumeMounts:
            - mountPath: /dump_dir
              name: dump-volume
            env:
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: tenant-augment-eng-determined-app-obj-store-creds
                  key: accessKey
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: tenant-augment-eng-determined-app-obj-store-creds
                  key: secretKey
            - name: AWS_DEFAULT_REGION
              value: us-west-2
          restartPolicy: Never
          volumes:
          - name: dump-volume
            emptyDir:
              sizeLimit: 500Gi

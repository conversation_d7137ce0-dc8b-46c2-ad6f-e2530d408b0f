apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cw-aws-sync
spec:
  schedule: 12 12 * * *
  concurrencyPolicy: Replace
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cw-aws-sync
            image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/aws_cli:latest
            args: ["s3", "sync", "--only-show-errors", "/sync_dirs/", "s3://coreweave-backups/"]
            volumeMounts:
            - mountPath: /sync_dirs/viofs-aug-cw-las1
              name: viofs-aug-cw-las1
            env:
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: cw-aws-sync-credentials
                  key: keyid
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: cw-aws-sync-credentials
                  key: accesskey
            - name: AWS_DEFAULT_REGION
              value: us-west-2
          restartPolicy: Never
          volumes:
          - name: viofs-aug-cw-las1
            persistentVolumeClaim:
              claimName: aug-cw-las1
      backoffLimit: 1

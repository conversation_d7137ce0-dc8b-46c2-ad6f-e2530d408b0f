local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local failedSpec = {
    displayName: 'Sync Checkpoints Job Failed',
    conditionPrometheusQueryLanguage: {
      duration: '0s',
      evaluationInterval: '900s',
      labels: { severity: 'warning' },
      query: |||
        # Get the most recent job
        topk(1, kube_job_created{namespace="devtools",job_name=~"sync-checkpoints-.*"}) *
        # Check if it failed
        on(job_name, cluster) (kube_job_status_failed{namespace="devtools"} != 0)
      |||,
    },
  };

  local notRunSpec = {
    displayName: 'Sync Checkpoints Not Run Recently',
    conditionPrometheusQueryLanguage: {
      duration: '0s',
      evaluationInterval: '900s',
      labels: { severity: 'warning' },
      // Alert if job hasn't run in 3 hours (job runs hourly)
      query: 'absent(max by (cluster) (kube_job_created{namespace="devtools",job_name=~"sync-checkpoints-.*"}) > time() - 10800)',
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      failedSpec,
      'sync-checkpoints-failed',
      'Sync Checkpoints job %s in cluster %s has failed' % [monitoringLib.label('job_name'), monitoringLib.label('cluster')]
    ),
    monitoringLib.alertPolicy(
      cloud,
      notRunSpec,
      'sync-checkpoints-not-run',
      'Sync Checkpoints job has not run for 3 hours'
    ),
  ]

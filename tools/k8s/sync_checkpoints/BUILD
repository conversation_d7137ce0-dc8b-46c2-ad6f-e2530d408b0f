load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "sync_checkpoints_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/tools/k8s/sync_checkpoints",
    visibility = ["//visibility:private"],
    deps = [
        "@com_github_benb<PERSON><PERSON>son_clock//:clock",
        "@com_github_google_go_containerregistry//pkg/authn",
        "@com_github_google_go_containerregistry//pkg/crane",
        "@com_github_google_go_containerregistry//pkg/name",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_artifactregistry//apiv1",
        "@com_google_cloud_go_artifactregistry//apiv1/artifactregistrypb",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/clientcmd",
        "@org_golang_google_api//iterator",
        "@org_golang_x_oauth2//google",
    ],
)

go_binary(
    name = "sync_checkpoints",
    embed = [":sync_checkpoints_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:cbazel_base_image",
    binary = ":sync_checkpoints",
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
    user = "ubuntu",
)

go_test(
    name = "sync_checkpoints_test",
    srcs = ["main_test.go"],
    embed = [":sync_checkpoints_lib"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

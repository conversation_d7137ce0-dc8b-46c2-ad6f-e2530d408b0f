// Creeate k8s objects for the sync_checkpoints tool.
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'sync-checkpoints';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    namespace=namespace,
    cloud=cloud,
    iam=true,
    overridePrefix='sync-chkpts',
  );
  local ram_limit_gb = 16;  // hoping 16 GB is more than enough memory - may need to continue updating this
  local cpu_limit = 2;
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    dry_run: false,
    bucket: 'gs://augment-data/prod-checkpoints/',
    directory: '/mnt/efs/augment/checkpoints',
    // update this list manually after successful deletions
    prefixes_to_delete: [
      'sentry/v3_ff',  # Now using 'sentry/v3_ff_fp8'
    ],
  });
  local container =
    {
      name: appName,
      target: {
        name: '//tools/k8s/sync_checkpoints:image',
        dst: 'sync_checkpoints_image',
      },
      volumeMounts: [
        {
          name: 'persistent-storage',
          mountPath: '/mnt/efs/augment',
        },
        configMap.volumeMountDef,
      ],
      args: [
        '--config',
        '/config/config.json',
        '',
      ],
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      resources: {
        limits: {
          cpu: cpu_limit,
          memory: '%sGi' % ram_limit_gb,
        },
      },
    };
  // Give access to the CloudStorage bucket.
  local bucketAccess = if cloudInfo.isLeadCluster(cloud) then gcpLib.grantAccess(
    name='%s-bucket-policy' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'StorageBucket',
      external: 'augment-data',
    },
    bindings=[
      {
        role: 'roles/storage.objectUser',
        members: [
          { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
        ] + if cloudInfo.isProjectLeadProdCluster(cloud) then [
          // give the prod service account of the gsc-prod project permission to sync
          {
            member: 'serviceAccount:<EMAIL>',
          },
        ] else [],
      },
    ]
  ) else null;
  local pod =
    {
      restartPolicy: 'Never',
      containers: [
        container,
      ],
      tolerations: tolerations,
      affinity: affinity,
      serviceAccountName: serviceAccount.name,
      volumes: [
        configMap.podVolumeDef,
        {
          name: 'persistent-storage',
          persistentVolumeClaim: {
            claimName: 'filestore-checkpoint-claim',
          },
        },
      ],
    };
  local cronJob = {
    apiVersion: 'batch/v1',
    kind: 'CronJob',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      schedule: '0 * * * *',
      concurrencyPolicy: 'Forbid',
      startingDeadlineSeconds: 120,
      successfulJobsHistoryLimit: 16,
      failedJobsHistoryLimit: 4,
      timeZone: 'America/Los_Angeles',
      jobTemplate: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: {
          backoffLimit: 2,
          template: {
            metadata: {
              labels: {
                app: appName,
              },
            },
            spec: pod,
          },
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    cronJob,
    bucketAccess,
  ])

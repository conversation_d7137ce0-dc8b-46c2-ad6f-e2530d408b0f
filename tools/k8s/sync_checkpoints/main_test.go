package main

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

type RunCall struct {
	dir  string
	args []string
}

type <PERSON>ck<PERSON>un struct {
	calls []RunCall
}

func (r *MockRun) Run(dir string, args ...string) error {
	r.calls = append(r.calls, RunCall{dir, args})
	return nil
}

func TestSyncCheckpoints(t *testing.T) {

	run := &MockRun{}
	config := Config{
		DryRun:    false,
		Bucket:    "test-bucket/",
		Directory: "test-directory",
	}

	err := SyncCheckpoints(config, run)
	assert.NoError(t, err)
	// 3 calls: initial df, rsyncs, final df
	assert.Equal(t, 3, len(run.calls))

	// Check initial df call
	df_call_1 := strings.Join(run.calls[0].args, " ")
	assert.Equal(t, "df -h", df_call_1)

	// Check rsync
	rsync_1 := strings.Join(run.calls[1].args, " ")
	assert.Equal(t, "/usr/local/google-cloud-sdk/bin/gsutil -m rsync -r -x .*\\/code\\/.* test-bucket/ .", rsync_1)

	// Check final df call
	df_call_2 := strings.Join(run.calls[2].args, " ")
	assert.Equal(t, "df -h", df_call_2)
}

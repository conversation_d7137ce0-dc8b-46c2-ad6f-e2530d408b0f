{
  deployment: [
    {
      name: 'sync-checkpoints',
      kubecfg: {
        target: '//tools/k8s/sync_checkpoints:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
          {
            cloud: 'GCP_US_CENTRAL1_GSC_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['surbhi'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'sync-checkpoints-monitoring',
      kubecfg: {
        target: '//tools/k8s/sync_checkpoints:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['surbhi'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

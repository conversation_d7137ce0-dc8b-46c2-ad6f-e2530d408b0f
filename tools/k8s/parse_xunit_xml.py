#!/usr/bin/env python3

import argparse
from pathlib import Path
from datetime import datetime
import xml.etree.ElementTree as ET
import yaml
import os
import sys
from enum import Enum

import psycopg2
from psycopg2.extras import execute_values

METRICS_DB_PASSWORD = os.environ.get("METRICS_DB_PASSWORD")
assert (
    METRICS_DB_PASSWORD is not None
), "Set the METRICS_DB_PASSWORD environment variable"


"""
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

create table test_prs (
    pr_num integer,
    run_id varchar primary key
);

create table test_suites (
    id uuid not null default uuid_generate_v4() primary key,
    run_id varchar not null,
    name varchar,
    tests integer,
    failures integer,
    errors integer,
    skipped integer,
    time float,
    timestamp timestamp,
    hostname varchar
);

create table test_cases (
    id uuid not null default uuid_generate_v4() primary key,
    suite_id uuid not null,
    name varchar,
    classname varchar,
    time float,
    result varchar,
    message varchar,
    content text,
    timestamp timestamp
);

create table test_failures (
    id uuid not null default uuid_generate_v1() primary key,
    case_id uuid not null,
    message varchar,
    content text
);
"""


class TestResult(Enum):
    PASSED = "PASSED"
    FAILED = "FAILED"

    def __str__(self) -> str:
        return self.value


class TestSuite:
    def __init__(
        self,
        name: str,
        tests: int,
        failures: int,
        errors: int,
        skipped: int,
        time: float,
        timestamp: datetime,
        hostname: str = "localhost",
    ):
        self.name = name
        self.tests = tests
        self.failures = failures
        self.errors = errors
        self.skipped = skipped
        self.time = time
        self.timestamp = timestamp
        self.hostname = hostname
        self.cases = []

    @classmethod
    def from_xml(cls, xml_element: ET.Element) -> "TestSuite":
        suite = cls(
            name=xml_element.attrib["name"],
            tests=int(xml_element.attrib["tests"]),
            failures=int(xml_element.attrib["failures"]),
            errors=int(xml_element.attrib["errors"]),
            skipped=int(xml_element.attrib["skipped"]),
            time=float(xml_element.attrib["time"]),
            timestamp=datetime.fromisoformat(xml_element.attrib["timestamp"]),
            hostname=xml_element.attrib["hostname"],
        )
        for case in xml_element.findall(".//testcase"):
            suite.cases.append(TestCase.from_xml(case))
        return suite

    def __str__(self) -> str:
        return f"TestSuite: {self.name} - Tests: {self.tests} - Failures: {self.failures} - Errors: {self.errors} - Skipped: {self.skipped} - Time: {self.time} - Timestamp: {self.timestamp}"


class TestCase:
    def __init__(self, name: str, classname: str, time: float):
        self.name = name
        self.classname = classname
        self.time = time
        self.failures = []

    @classmethod
    def from_xml(cls, xml_element: ET.Element) -> "TestCase":
        case = cls(
            name=xml_element.attrib["name"],
            classname=xml_element.attrib["classname"],
            time=float(xml_element.attrib["time"]),
        )
        for failure in xml_element.findall(".//failure"):
            case.failures.append(
                TestFailure(message=failure.attrib["message"], content=failure.text)
            )
        return case

    def __str__(self) -> str:
        return f"TestCase: {self.classname}:{self.name} ({self.result}) - Time: {self.time}"

    @property
    def result(self) -> TestResult:
        if self.failures:
            return TestResult.FAILED
        return TestResult.PASSED


class TestFailure:
    def __init__(self, message: str, content: str = ""):
        self.message = message
        self.content = content

    def __str__(self) -> str:
        return f"TestFailure: {self.message}"


def suite_name_from_filename(filename: Path | str) -> str:
    """Get the name of the test suite from the filename."""
    sn = Path(filename).stem
    if sn.endswith("_"):
        sn = sn[:-1]
    sn = sn.replace("_parallel", "")
    return sn


def parse_file(filename: Path | str) -> list[TestSuite]:
    """Parse the xunit xml file."""
    tree = ET.parse(filename)
    root = tree.getroot()

    suites = []
    for testsuite in root.findall(".//testsuite"):
        suite = TestSuite.from_xml(testsuite)
        suite.name = suite_name_from_filename(filename)
        print(f"Setting suite.name to {suite.name}")
        suites.append(suite)

    return suites


def upload_suite(
    conn: psycopg2.extensions.connection,
    suite: TestSuite,
    run_id: str,
    pr_num: int | None,
) -> None:
    """Upload the test suite to the database."""
    params = (
        run_id,
        suite.name,
        suite.tests,
        suite.failures,
        suite.errors,
        suite.skipped,
        suite.time,
        suite.timestamp,
        suite.hostname,
    )

    with conn.cursor() as cur:
        # print(params)
        if pr_num is not None:
            cur.execute(
                """
                insert into test_prs (pr_num, run_id)
                values (%s, %s)
                on conflict (run_id) do nothing
                """,
                (pr_num, run_id),
            )
        cur.execute(
            """
            insert into test_suites (run_id, name, tests, failures, errors, skipped, time, timestamp, hostname)
            values (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            returning id
            """,
            params,
        )
        suite_id = cur.fetchone()[0]  # type: ignore
        upload_cases(conn, suite.cases, suite_id, suite.timestamp)
        conn.commit()


def upload_cases(
    conn: psycopg2.extensions.connection,
    cases: list[TestCase],
    suite_id: str,
    timestamp: datetime,
) -> None:
    """Upload the test case to the database."""
    params = []
    for case in cases:
        params.append((suite_id, case.name, case.classname, case.time, timestamp))
    print(f"Uploading {len(params)} test cases")
    with conn.cursor() as cur:
        case_ids = execute_values(
            cur,
            """
            insert into test_cases (suite_id, name, classname, time, timestamp)
            values %s
            returning id
            """,
            params,
            fetch=True,
        )
        for case, case_id in zip(cases, case_ids):
            if len(case.failures) > 0:
                upload_failures(conn, case.failures, case_id[0])


def upload_failures(
    conn: psycopg2.extensions.connection, failures: list[TestFailure], case_id: str
) -> None:
    """Upload the test failure to the database."""
    params = []
    for failure in failures:
        params.append(
            (
                case_id,
                failure.message,
                failure.content,
            )
        )
    print(f"Uploading {len(params)} test failures")
    with conn.cursor() as cur:
        execute_values(
            cur,
            """
            insert into test_failures (case_id, message, content)
            values %s
            """,
            params,
        )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Parse xunit xml file.")
    parser.add_argument("filename", nargs="+", type=str, help="xunit xml file")
    parser.add_argument(
        "--update-db", action="store_true", help="Update the database", default=False
    )
    parser.add_argument("--run-id", type=str, help="GH Run ID", default=None)
    parser.add_argument("--pr-num", type=int, help="PR number", default=None)
    args = parser.parse_args()
    files_to_parse = []
    for filename in [Path(f) for f in args.filename]:
        if not filename.exists():
            print(f"File {filename} does not exist", file=sys.stderr)
        if filename.is_dir():
            print(f"Expanding directory {filename}", file=sys.stderr)
            metadata_file = filename / "metadata.yaml"
            pr_num = args.pr_num
            if pr_num is None and metadata_file.exists():
                metadata = yaml.safe_load(metadata_file.read_text())
                pr_num = metadata["pr_number"]
            for file in filename.rglob("*.xml"):
                files_to_parse.append((file, pr_num))
        else:
            files_to_parse.append((filename, args.pr_num))
    for filename, pr_num in files_to_parse:
        run_id = args.run_id
        if run_id is None:
            run_id = filename.parent.stem
        print(f"Parsing {filename} {run_id} {pr_num}")
        try:
            suites = parse_file(filename)
        except ET.ParseError as e:
            print(f"Failed to parse {filename}: {e}", file=sys.stderr)
            continue
        if args.update_db:
            conn = psycopg2.connect(
                host="devex-metrics-postgresql-hl",
                database="metrics",
                user="augment",
                password=METRICS_DB_PASSWORD,
            )
            for suite in suites:
                print(suite)
                upload_suite(conn, suite, run_id, pr_num)
            conn.close()

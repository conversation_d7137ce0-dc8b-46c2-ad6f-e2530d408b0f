"""Tool to organize deployments based on Bazel targets.

The tool uses the METADATA.yaml files to find all Bazel targets
that "deploy" (usually K8S apply targets) and (if applicable)
Kubernetes rollout configurations.

It will run the deployment targets and wait for the rollouts.
"""

# pylint: disable=logging-fstring-interpolation
import argparse
import logging
import os
import sys
from datetime import timedelta
from pathlib import Path

from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.logging.console_logging import setup_console_logging
from tools.deploy_runner import metadata_pb2
from tools.deploy_runner.deploy_target import (
    DeploymentInfo,
    DeployNullNotifier,
    DeployTarget,
    MatchingTargetFilter,
    order_targets,
)
from tools.deploy_runner.deployment_finder import DeploymentFinder


def run_all_targets(
    targets: list[DeployTarget],
    dry_run: bool,
    skip_validation: bool,
):
    """Runs all targets."""
    notifier = DeployNullNotifier()
    failed_targets = []
    deployed_targets = []
    skipped_targets = []
    target_groups = order_targets(targets)
    for target_group in target_groups:
        for target in target_group.targets:
            r = target.deploy(
                dry_run=dry_run,
                notifier=DeployNullNotifier(),
            )
            if r is None:
                skipped_targets.append(target.name)
            elif r:
                if skip_validation or target.validate(notifier):
                    deployed_targets.append(target.name)
                else:
                    failed_targets.append(target.name)
            else:
                failed_targets.append(target.name)
        for target in skipped_targets:
            logging.info("Skipped target: %s", target)
        for target in deployed_targets:
            logging.info("Deployed target: %s", target)
        for target in failed_targets:
            logging.warning("Failed target: %s", target)

    return len(failed_targets)


def main():
    """Entrypoint."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument("--extra-args")
    parser.add_argument("--extra-startup-args")
    parser.add_argument(
        "--workspace",
        required=not os.environ.get("BUILD_WORKSPACE_DIRECTORY"),
        default=os.environ.get("BUILD_WORKSPACE_DIRECTORY"),
    )
    parser.add_argument(
        "--target-names",
        nargs="*",
        help="target names to use instead of finding them from the diff",
    )
    parser.add_argument(
        "--dry-run",
        default=True,
        action=argparse.BooleanOptionalAction,
        help="If true, will run in dry run mode. If false (--no-dry-run), then will try to actually deploy",
    )
    parser.add_argument(
        "--list-target-names", action="store_true", help="list target names found"
    )
    parser.add_argument(
        "--cloud-filter",
        default=None,
        type=str.upper,
        choices=[
            "GCP_US_CENTRAL1_PROD",
            "GCP_US_CENTRAL1_GSC_PROD",
            "GCP_EU_WEST4_PROD",
            "GCP_US_CENTRAL1_DEV",
        ],
    )
    parser.add_argument(
        "--env-filter", help="Filter for environments to deploy to", default=None
    )
    parser.add_argument(
        "--namespace-filter", help="Filter for namespaces to deploy to", default=None
    )
    parser.add_argument(
        "--deployment-type-filter",
        help="Filter for deployment types to deploy to, like 'kubecfg' or 'kubecfg_tombstone'",
        default=None,
    )
    parser.add_argument(
        "--quick-deploy-filter",
        action="store_true",
        help="Filter for quick deploy targets",
    )
    parser.add_argument(
        "--kube-config-file", default=Path.home().joinpath(".kube", "config"), type=Path
    )
    parser.add_argument(
        "--skip-validation", action="store_true", help="Skip validation of deployments"
    )
    parser.add_argument("--allow-rollback", action="store_true", help="Allow rollbacks")
    args = parser.parse_args()

    workspace = Path(args.workspace)

    target_filter = MatchingTargetFilter(
        cloud_filter=args.cloud_filter,
        namespace_filter=args.namespace_filter,
        env_filter=args.env_filter,
        deployment_type_filter=args.deployment_type_filter,
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT
        if not args.quick_deploy_filter
        else metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )

    kube_client = create_kubernetes_client(args.kube_config_file)

    kubectl_factory = create_kubectl_factory(args.kube_config_file)

    information = DeploymentInfo(
        workspace=workspace,
        kubernetes_client=kube_client,
        kubectl_factory=kubectl_factory,
        extra_bazel_startup_args=args.extra_startup_args,
        extra_bazel_args=args.extra_args,
        allow_rollback=args.allow_rollback,
        bazel_runner_client=None,
        checkout=None,
    )

    deployer = DeploymentFinder(
        information,
        target_filter=target_filter,
    )

    if args.dry_run:
        logging.info("\n\n\nRunning in dry run mode!\n\n")

    if args.target_names:
        targets = list(deployer.find_targets_by_name(args.target_names))
    else:
        targets = list(deployer.find_all_targets())

    targets.sort(key=lambda x: (x.priority, x.name), reverse=True)

    if args.list_target_names:
        for target in targets:
            print(target.name)
        sys.exit(0)

    sys.exit(
        run_all_targets(targets, args.dry_run, skip_validation=args.skip_validation)
    )


if __name__ == "__main__":
    main()

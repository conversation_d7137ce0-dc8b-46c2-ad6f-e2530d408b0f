"""Test for metadata.py."""

import pathlib
import tempfile

import metadata


def test_metadata():
    """Tests the metadata parsing."""
    with tempfile.TemporaryDirectory() as tmpdir:
        t = pathlib.Path(tmpdir)
        metadata_path = t.joinpath("METADATA")
        metadata_path.write_text(
            """
            deployment {
                name: "common"
                bazel: {
                    target: "//deploy/common:k8s-common.apply"
                    arguments: [ "a", "b", "c" ]
                }
            }
        """,
            encoding="utf-8",
        )

        m = metadata.parse_metadata_file(metadata_path)
        assert m
        assert len(m.deployment) == 1
        d = m.deployment[0]
        assert d.name == "common"
        assert d.bazel.target == "//deploy/common:k8s-common.apply"
        assert d.bazel.arguments == ["a", "b", "c"]

        metadata_files = metadata.find_metadata(t)
        assert len(metadata_files) == 1
        assert metadata_files[0] == m


def test_metadata_jsonnet():
    """Tests the metadata parsing with jsonnet."""
    with tempfile.TemporaryDirectory() as tmpdir:
        t = pathlib.Path(tmpdir)
        metadata_path = t.joinpath("METADATA.jsonnet")
        metadata_path.write_text(
            """
            local name = "common";
            {
                deployment: [
                    {
                        name: name,
                        bazel: {
                            target: "//deploy/common:k8s-common.apply"
                        }
                    }
                ]
            }
        """,
            encoding="utf-8",
        )

        m = metadata.parse_metadata_jsonnet_file(metadata_path, pathlib.Path("."))
        assert m
        assert len(m.deployment) == 1
        d = m.deployment[0]
        assert d.name == "common"
        assert d.bazel.target == "//deploy/common:k8s-common.apply"
        assert d.bazel.arguments == []

        metadata_files = metadata.find_metadata(t)
        assert len(metadata_files) == 1
        assert metadata_files[0] == m

"""Code for deployment gate."""

from datetime import datetime, timedelta
import logging
import typing

from tools.bazel_runner.server.test_runner_pb2 import RunState, ScheduleTestRequest
from tools.deploy_runner.deploy_target import DeployNotifier, DeploymentInfo
from tools.deploy_runner.metadata import find_metadata
from tools.deploy_runner import deploy_events_pb2, metadata_pb2


class DeployGate(typing.Protocol):
    """A deployment gate.

    A deployment gate runs at some point during the Continous Deployment process to check if the deployment should continue.
    If a deployment gate fails, the deployment is aborted.
    """

    def start(
        self,
        notifier: DeployNotifier,
    ) -> bool:
        """Starts the deployment gate.

        The deployment gate can run in the background.
        The caller will call check() if start returns true.

        If start returns false, the implementation should call notifier with
        relevant information.

        Returning false means the deployment should be aborted.
        """
        raise NotImplementedError()

    def check(
        self,
        notifier: DeployNotifier,
    ) -> bool | None:
        """Check the deployment gate.

        The deployment gate can run in the background.
        The caller will call check() until it returns true or false.
        The caller might stop calling check() at any point in time.

        If check returns false, the implementation should call notifier with
        relevant information.

        Returning false means the deployment should be aborted.
        """
        raise NotImplementedError()

    def test(self) -> bool:
        """Tests a deployment target."""
        raise NotImplementedError()

    @property
    def name(self) -> str:
        """Return the descriptive name of the deployment target."""
        raise NotImplementedError()

    @property
    def location(self) -> metadata_pb2.DeploymentGateLocation.ValueType:
        """Return the target name of the deployment target."""
        raise NotImplementedError()


class DeploymentGateFinder:
    """Finds deployment gates in a workspace."""

    def __init__(
        self,
        deployment_info: DeploymentInfo,
    ):
        self.deployment_info = deployment_info

    def _read_metadata(self):
        metadata = find_metadata(self.deployment_info.workspace)
        gates: dict[str, metadata_pb2.DeploymentGate] = {}  # type: ignore
        for m in metadata:
            for gate in m.deployment_gates:
                if gate.name in gates:
                    logging.error("Duplicate gate '%s'", gate.name)
                    raise ValueError(f"Duplicate gate {gate.name}")
                gates[gate.name] = gate
        logging.debug("Found deployment gates %s", gates)
        return gates

    def find_all_gates(self) -> typing.Iterable[DeployGate]:
        """Finds all targets in the workspace."""
        targets = self._read_metadata()
        for target in [
            create_deploy_gate(
                self.deployment_info,
                targets[t],
            )
            for t in targets
        ]:
            if target is not None:
                yield target

    def find_gates_by_name(
        self, target_names: list[str]
    ) -> typing.Iterable[DeployGate]:
        """Finds all gates in the given list in the workspace."""
        targets = self._read_metadata().values()
        for target in [
            create_deploy_gate(
                self.deployment_info,
                t,
            )
            for t in targets
            if t.name in target_names
        ]:
            if target is not None:
                yield target


def create_deploy_gate(
    deployment_info: DeploymentInfo,
    target: metadata_pb2.DeploymentGate,
) -> "DeployGate":
    """Create a deployment gate.

    Args:
        deployment_info: The deployment information, e.g. the workspace of the kubecfg file.
        target: The deployment target to deploy.
    """
    if target.bazel_test_gate.targets:
        return BazelTestGate(deployment_info, target)
    raise ValueError(f"Unknown deployment gate {target}")


DEFAULT_GATE_TIMEOUT = 60 * 30


class BazelTestGate(DeployGate):
    """A deployment gate that runs a Bazel test target."""

    def __init__(
        self,
        deployment_info: DeploymentInfo,
        gate: metadata_pb2.DeploymentGate,
    ):
        self.deployment_info = deployment_info
        self.gate = gate
        assert gate.bazel_test_gate
        self.run_id = None
        self.start_time = None
        self.check_result = None

    @property
    def name(self):
        return self.gate.name

    @property
    def location(self):
        return self.gate.location

    def test(self) -> bool:
        """Tests a deployment target."""
        if not self.gate.bazel_test_gate.targets:
            logging.error("No bazel test targets")
            return False
        return True

    def start(
        self,
        notifier: DeployNotifier,
    ) -> bool:
        """Run the deployment gate

        Returning false means the deployment should be aborted.
        """
        try:
            if self.deployment_info.bazel_runner_client is None:
                logging.error(
                    "Skipping deployment gate '%s' because no bazel runner client is available",
                    self.name,
                )
                return True
            if self.deployment_info.checkout is None:
                logging.error(
                    "Skipping deployment gate '%s' because no checkout is available",
                    self.name,
                )
                return True
            logging.info("Running deployment gate '%s'", self.name)

            req = ScheduleTestRequest()
            req.tags.append("deploy-gate")
            req.requestor = self.deployment_info.deployed_by or "deploy-runner"
            t = req.test_execution.runs.add()
            t.command = self.gate.bazel_test_gate.command or "test"
            t.targets.extend(self.gate.bazel_test_gate.targets)
            req.test_execution.checkout.MergeFrom(self.deployment_info.checkout)
            resp = self.deployment_info.bazel_runner_client.schedule(req)
            logging.info("Scheduled run %s", resp.run_id)
            self.run_id = resp.run_id
            self.start_time = datetime.now()
            notifier.progress(
                deploy_events_pb2.DeployProgress(
                    message=f"Running deployment gate {self.name} as run {resp.run_id}",
                )
            )
            notifier.deploy_gate_started(
                deploy_events_pb2.DeployGateStarted(
                    name=self.name,
                )
            )
            return True
        except Exception as e:
            logging.exception(e)
            logging.error("Failed to start deployment gate '%s'", self.name)
            return False

    def check(
        self,
        notifier: DeployNotifier,
    ) -> bool | None:
        """Check the deployment gate

        Returning false means the deployment should be aborted.
        """
        if self.check_result is not None:
            return self.check_result
        r = self._check(notifier)
        self.check_result = r
        return r

    def _check(
        self,
        notifier: DeployNotifier,
    ) -> bool | None:
        if not self.run_id:
            # skipped
            return True
        assert self.run_id, "No run id"
        assert self.start_time, "No start time"
        assert self.deployment_info.bazel_runner_client
        try:
            duration = self.gate.bazel_test_gate.timeout_seconds or DEFAULT_GATE_TIMEOUT
            now = datetime.now()
            if now - self.start_time > timedelta(seconds=duration):
                logging.error("Deployment gate '%s' timeout", self.name)
                notifier.deploy_gate_finished(
                    deploy_events_pb2.DeployGateFinished(
                        name=self.name,
                        status=deploy_events_pb2.DeployGateStatus.DEPLOY_GATE_STATUS_FAILED,
                        message="Deployment gate failed",
                    )
                )
                return False
            s = self.deployment_info.bazel_runner_client.get_run(self.run_id)
            logging.info("Deployment gate '%s': %s", self.name, s)
            if s.state == RunState.RUN_STATE_ABORT:
                logging.error("Deployment gate '%s' aborted", self.name)
                notifier.deploy_gate_finished(
                    deploy_events_pb2.DeployGateFinished(
                        name=self.name,
                        status=deploy_events_pb2.DeployGateStatus.DEPLOY_GATE_STATUS_FAILED,
                        message="Deployment gate aborted",
                    )
                )
                return False
            if s.state == RunState.RUN_STATE_ERROR:
                logging.error("Deployment gate '%s' failed", self.name)
                notifier.deploy_gate_finished(
                    deploy_events_pb2.DeployGateFinished(
                        name=self.name,
                        status=deploy_events_pb2.DeployGateStatus.DEPLOY_GATE_STATUS_FAILED,
                        message="Deployment gate failed",
                    )
                )
                return False
            if s.state == RunState.RUN_STATE_TIMEOUT:
                logging.error("Deployment gate '%s' timeout", self.name)
                notifier.deploy_gate_finished(
                    deploy_events_pb2.DeployGateFinished(
                        name=self.name,
                        status=deploy_events_pb2.DeployGateStatus.DEPLOY_GATE_STATUS_FAILED,
                        message="Deployment gate test timed out",
                    )
                )
                return False
            if s.state == RunState.RUN_STATE_CANCEL:
                logging.error("Deployment gate '%s' cancelled", self.name)
                notifier.deploy_gate_finished(
                    deploy_events_pb2.DeployGateFinished(
                        name=self.name,
                        status=deploy_events_pb2.DeployGateStatus.DEPLOY_GATE_STATUS_FAILED,
                        message="Deployment gate cancelled",
                    )
                )
                return False
            if s.state == RunState.RUN_STATE_PASSED:
                logging.info("Deployment gate '%s' passed", self.name)
                notifier.deploy_gate_finished(
                    deploy_events_pb2.DeployGateFinished(
                        name=self.name,
                        status=deploy_events_pb2.DeployGateStatus.DEPLOY_GATE_STATUS_SUCCESS,
                        message="Deployment gate passed",
                    )
                )
                return True
            return None
        except Exception as e:
            logging.exception(e)
            logging.error("Failed to check deployment gate '%s'", self.name)
            return False

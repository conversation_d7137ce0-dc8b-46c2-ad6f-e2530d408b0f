load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "metadata_proto",
    srcs = ["metadata.proto"],
    visibility = ["//tools/deploy_runner:__subpackages__"],
)

py_proto_library(
    name = "metadata_py_proto",
    protos = [":metadata_proto"],
    visibility = ["//tools:__subpackages__"],
)

proto_library(
    name = "deploy_events_proto",
    srcs = ["deploy_events.proto"],
    visibility = ["//tools/deploy_runner:__subpackages__"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "deploy_events_py_proto",
    protos = [":deploy_events_proto"],
    visibility = ["//tools/deploy_runner:__subpackages__"],
)

py_library(
    name = "metadata",
    srcs = [
        "metadata.py",
    ],
    data = [
        "@google_jsonnet_go//cmd/jsonnet",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":metadata_py_proto",
        "//base/cloud/k8s:kubectl",
        requirement("protobuf"),
    ],
)

py_library(
    name = "deploy_target",
    srcs = [
        "deploy_target.py",
        "deploy_target_factory.py",
        "kubecfg_deploy_target.py",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        requirement("prometheus_client"),
        ":deploy_events_py_proto",
        ":metadata",
        "//base/cloud/k8s:kubectl",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/python/cloud",
        "//tools/bazel_lib",
        "//tools/bazel_runner/web/client",
        "//tools/kubecfg",
        "//tools/kubecfg:kubecfg_deletion",
        "//tools/kubecfg:kubecfg_test_lib",
    ],
)

py_library(
    name = "deploy_gate",
    srcs = ["deploy_gate.py"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":deploy_events_py_proto",
        ":deploy_target",
        ":metadata",
        "//base/python/cloud",
        "//tools/bazel_runner/web/client",
    ],
)

pytest_test(
    name = "deploy_gate_test",
    srcs = ["deploy_gate_test.py"],
    deps = [
        ":deploy_gate",
        ":deploy_target",
        ":metadata",
        "//tools/bazel_runner/server:test_runner_py_proto",
        "//tools/deploy_runner:deploy_events_py_proto",
    ],
)

py_library(
    name = "deployment_finder",
    srcs = ["deployment_finder.py"],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":deploy_target",
        ":metadata",
        "//base/python/cloud",
    ],
)

py_binary(
    name = "deploy_runner_util",
    srcs = ["deploy_runner_util.py"],
    deps = [
        ":deployment_finder",
        ":metadata",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/logging:console_logging",
        "//base/python/cloud",
        requirement("kubernetes"),
    ],
)

py_binary(
    name = "check_tombstone",
    srcs = ["check_tombstone.py"],
    deps = [
        ":deployment_finder",
        ":metadata",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        "//base/logging:console_logging",
        "//base/python/cloud",
        requirement("kubernetes"),
    ],
)

pytest_test(
    name = "metadata_test",
    srcs = ["metadata_test.py"],
    deps = [
        ":metadata",
    ],
)

pytest_test(
    name = "deploy_target_test",
    srcs = ["deploy_target_test.py"],
    data = glob(["test_data/**"]),
    deps = [
        ":deploy_target",
        "//base/cloud/k8s:fake_kubectl",
    ],
)

py_binary(
    name = "metadata_test_util",
    testonly = True,
    srcs = ["metadata_test_util.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":deploy_gate",
        ":deploy_target",
        ":metadata",
        "//base/cloud/k8s:fake_kubectl",
        "//base/logging:console_logging",
        "//base/test_utils:junit_xml_writer",
    ],
)

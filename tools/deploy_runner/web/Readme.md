# Deployment UI

This package contains the deployment web UI, e.g. to schedule emergency deployments.

# Local Development

- Deploy development setup
- Forward deploy-rpc pod's rpc port with `kubectl port-forward [DEPLOY-RPC-POD-IN-DEV-NAMESPACE] 50051:50051` or `click`
- Run `bazel run //tools/deploy_runner/web/backend`
- Goto `tools/deploy_runner/web/frontend` and run `npm run start`.
- Forward port `3000` from AWS dev VM to Laptop via the vscode port-forwarding
- Visit `http://127.0.0.1:3000/`

Any changes in the backend Python code as well as the Typescript frontend code are automatically reflected.

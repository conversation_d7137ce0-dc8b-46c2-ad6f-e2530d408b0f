// K8S deployment file for the test viewer web UI
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local appName = 'deploy-viewer';
  local backendConfig = gcpLib.createBackendConfig(app=appName,
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTPS',
                                                     requestPath: '/health',
                                                   },
                                                   iap=true);
  local clientCert = certLib.createClientCert(
    name='deploy-viewer-client-cert',
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  local ingressHostname = if namespace == 'devtools' then 'deploy-viewer.%s' % domainSuffix else 'deploy-viewer.%s.%s' % [namespace, domainSuffix];
  local ingressFacingCert = certLib.createPublicServerCert(name='deploy-viewer-public-cert',
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           volumeName='https-certs',
                                                           env=env);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'deploy-viewer-svc',
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'viewer-https': 'HTTPS' }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'viewer-https',
          targetPort: 'viewer-https',
        },
      ],
    },
  };
  local iapAudience = '/projects/%s/global/backendServices/%s' % [cloudInfo[cloud].projectNumber, if std.objectHas(namespace_config, 'iapAudience') then namespace_config.iapAudience else ''];
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'deploy-viewer-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      'flask.cfg': |||
        PORT=5000
        DEPLOYMENT_GRPC_URL="deploy-rpc-svc:50051"
        CLIENT_MTLS=%(clientMtls)s
        HTTPS_SERVER_KEY="/https-certs/tls.key"
        HTTPS_SERVER_CERT="/https-certs/tls.crt"
        CA_CERT="/client-certs/ca.crt"
        CLIENT_KEY="/client-certs/tls.key"
        CLIENT_CERT="/client-certs/tls.crt"
        IAP_AUDIENCE="%(iapAudience)s"
        IAP_JWT_VERIFIER_DISABLED=%(iapJwtVerifierDisabled)s
        CLOUDS='%(clouds)s'
      ||| % {
        clientMtls: if mtls then 'True' else 'False',
        iapAudience: iapAudience,
        iapJwtVerifierDisabled: if namespace_config.flags.iapJwtVerifierDisabled then 'True' else 'False',
        clouds: if env == 'DEV' then std.manifestJsonEx(['GCP_US_CENTRAL1_DEV'], '', '') else std.manifestJsonEx(['GCP_US_CENTRAL1_DEV', 'GCP_US_CENTRAL1_PROD', 'GCP_EU_WEST4_PROD'], '', ''),
      },
    },
  };
  local container =
    {
      name: 'deploy-viewer',
      target: {
        name: '//tools/deploy_runner/web/backend:image',
        dst: 'deploy-viewer',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'viewer-https',
        },
      ],
      volumeMounts: [
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
        clientCert.volumeMountDef,
        ingressFacingCert.volumeMountDef,
        {
          mountPath: '/tmp/prometheus_multiproc_dir',
          name: 'prometheus-multiproc-dir',
        },
      ],
      env: [
        {
          name: 'CONFIG_FILE',
          value: '/config/flask.cfg',
        },
        {
          name: 'PROMETHEUS_MULTIPROC_DIR',
          value: '/tmp/prometheus_multiproc_dir',
        },
      ],
      readinessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '512Mi',
        },
      },
    };
  local serviceAccount = gcpLib.createServiceAccount(app='deploy-viewer', cloud=cloud, env=env, namespace=namespace, iam=true);
  local roleBindings = if env == 'DEV' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'deploy-viewer-%s-role-backend-binding' % namespace,
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'User',
          name: serviceAccount.serviceAccountGcpEmailAddress,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'deploy-viewer-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ] else [
    // for PROD we create role bindings for all clusters in deploy_shared.jsonnet
  ];
  local pod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        {
          name: 'prometheus-multiproc-dir',
          emptyDir: {},
        },
        clientCert.podVolumeDef,
        {
          name: 'config',
          configMap: {
            name: config.metadata.name,
          },
        },
        ingressFacingCert.podVolumeDef,
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app='deploy-viewer', cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: 'deploy-viewer',
        },
        name: 'deploy-viewer-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'deploy-viewer-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'deploy-viewer-svc',
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'deploy-viewer',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    config,
    service,
    roleBindings,
    serviceAccount.objects,
    ingressFacingCert.objects,
    deployment,
    ingressObjects,
    clientCert.objects,
  ])

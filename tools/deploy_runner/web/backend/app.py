"""Flask application to provide the backend for the deployment viewer web UI."""

import logging
import os
from datetime import datetime
from pathlib import Path

import grpc
from base.cloud.iap import iap

from flask import Flask, send_from_directory
from gunicorn.app.base import BaseApplication
from prometheus_flask_exporter.multiprocess import GunicornPrometheusMetrics

import tools.deploy_runner.web.backend.api as api
from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.python.grpc import client_options
from base.logging.struct_logging import setup_struct_logging
from tools.deploy_runner.server import deploy_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> deploy_pb2_grpc.DeployStub:
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        logging.info(
            "No credentials provided, creating insecure channel to %s", endpoint
        )
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = deploy_pb2_grpc.DeployStub(channel)
    return stub


def create_app():
    app = Flask(__name__, static_folder="../frontend/dist")

    metrics = GunicornPrometheusMetrics(app, port=9090, group_by="endpoint")
    app.metrics = metrics  # type: ignore

    if "CONFIG_FILE" in os.environ:
        app.config.from_envvar("CONFIG_FILE")
    else:
        app.config.from_object(
            "augment.tools.deploy_runner.web.backend.config.TestConfig"
        )
    logging.info("Config %s", app.config)

    if app.config["CLIENT_MTLS"]:
        credentials = grpc.ssl_channel_credentials(
            root_certificates=Path(app.config["CA_CERT"]).read_bytes(),
            private_key=Path(app.config["CLIENT_KEY"]).read_bytes(),
            certificate_chain=Path(app.config["CLIENT_CERT"]).read_bytes(),
        )
    else:
        credentials = None
    deploy_rpc_client = setup_stub(
        endpoint=app.config["DEPLOYMENT_GRPC_URL"], credentials=credentials
    )

    iap_verifier = iap.IapJwtVerifier.create(app.config["IAP_AUDIENCE"])
    app.iap_verifier = iap_verifier  # type: ignore

    app.deploy_rpc_client = deploy_rpc_client  # type: ignore

    kubernetes_client = create_kubernetes_client(
        Path("tools/deploy/auth_kube_config.yaml")
    )
    app.kubernetes_client = kubernetes_client  # type: ignore

    kubectl_factory = create_kubectl_factory(
        kube_config_file=Path("tools/deploy/auth_kube_config.yaml"),
    )
    app.kubectl_factory = kubectl_factory  # type: ignore

    app.register_blueprint(api.bp)  # type: ignore

    # Serve React App
    @app.route("/", defaults={"path": ""})
    @app.route("/<path:path>")
    def serve(path):
        static_folder_path = Path(app.static_folder).absolute()  # type: ignore
        item_path = static_folder_path / path
        if path != "" and item_path.exists():
            r = send_from_directory(str(app.static_folder), path, max_age=0)
        else:
            r = send_from_directory(str(app.static_folder), "index.html", max_age=0)
        r.headers["Cache-Control"] = (
            "no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0"
        )
        r.headers["Pragma"] = "no-cache"
        r.headers["Expires"] = "-1"
        r.headers["Last-Modified"] = datetime.now().isoformat()
        return r

    @app.route("/health")
    def health():
        """Health check endpoint.

        Called to check if the webhook is alive.
        """
        return "Ok"

    return app


class _App(BaseApplication):
    """Gunicorn wrapper for service Flask App."""

    # pylint: disable=abstract-method

    def __init__(self, app, options):
        self.application = app
        self.options = options
        super().__init__()

    def load_config(self):
        assert (
            self.cfg is not None
        )  # cfg is never None here, as it is initialized by Gunicorn. adding check for pylint
        for key, value in self.options.items():
            self.cfg.set(key, value)

    def load(self):
        return self.application


def when_ready(server):
    del server
    GunicornPrometheusMetrics.start_http_server_when_ready(9090)


def child_exit(server, worker):
    del server
    GunicornPrometheusMetrics.mark_process_dead_on_child_exit(worker.pid)


def main():
    setup_struct_logging()

    app = create_app()
    port = app.config.get("PORT")
    logging.info("Serve port %s", port)

    if app.config.get("DEBUG_MODE"):
        app.run(use_reloader=True, port=port, threaded=True)
    else:
        https_service_key = app.config.get("HTTPS_SERVER_KEY")
        https_service_cert = app.config.get("HTTPS_SERVER_CERT")
        options = {
            "bind": f"0.0.0.0:{port}",
            "threads": 4,
            "keyfile": https_service_key,
            "certfile": https_service_cert,
            "when_ready": when_ready,
            "child_exit": child_exit,
        }
        _App(app, options).run()


if __name__ == "__main__":
    main()

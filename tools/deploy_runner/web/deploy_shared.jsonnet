function(cloud)
  local role =
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'deploy-viewer-role',
      },
      rules: [
        {
          apiGroups: [
            '',
          ],
          resources: [
            'namespaces',
          ],
          verbs: [
            'list',
            'get',
          ],
        },
        {
          apiGroups: [
            'apps',
          ],
          resources: [
            'deployments',
          ],
          verbs: [
            'list',
            'get',
          ],
        },
      ],
    };
  local appName = 'deploy-viewer';
  local prodRoleBindings =
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'deploy-viewer-prod-role-backend-binding',
        labels: {
          app: appName,
        },
      },
      subjects: [
        {
          kind: 'User',
          name: '<EMAIL>',
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'deploy-viewer-role',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    };
  [
    role,
    prodRoleBindings,
  ]

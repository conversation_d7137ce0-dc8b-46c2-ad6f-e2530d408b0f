import "../App.css";
import { LayoutComponent } from "../lib/layout";
import React, { useEffect, useState } from "react";
import { Badge, Empty, Spin, Table, Typography, message } from "antd";
import { ColumnType } from "antd/es/table";
import dayjs, { Dayjs } from "dayjs";
import utc from "dayjs/plugin/utc";
import localizedFormat from "dayjs/plugin/localizedFormat";
import axios from "axios";
import { uniq } from "lodash";
import {
  Condition,
  KubernetesData,
  getClouds,
  getKubernetesState,
  getNamespaces,
} from "../lib/kubernetes";
const { Text, Link } = Typography;

dayjs.extend(utc);
dayjs.extend(localizedFormat);

export function KubernetesDeploymentConditionComponent({
  condition,
}: {
  condition?: Condition;
}) {
  if (condition === undefined) {
    return <div></div>;
  } else if (condition.status == "True") {
    return <Badge status="success" text="Running" />;
  } else {
    return <Badge status="error" text={condition.message} />;
  }
}

function KubernetesStatePageComponent() {
  const [kubernetesData, setKubernetesData] = useState<
    KubernetesData[] | null | undefined
  >(undefined);
  const [continueToken, setContinueToken] = useState<{
    clouds: string[];
    token: string;
    done: boolean;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    const fetchClouds = async () => {
      try {
        const clouds = await getClouds();
        setContinueToken({
          clouds: clouds,
          token: "",
          done: false,
        });
      } catch (e) {
        console.log(`Error while loading the data: ${e}`);
        if (axios.isAxiosError(e)) {
          messageApi.open({
            type: "error",
            content: `Error while loading the data: ${e.message}`,
          });
        } else {
          messageApi.open({
            type: "error",
            content: `Error while loading the data`,
          });
        }
      }
    };
    fetchClouds();
  }, []);

  useEffect(() => {
    if (continueToken === null) {
      return;
    }
    if (continueToken.done) {
      return;
    }
    if (kubernetesData === null) {
      return;
    }
    const fetchRun = async () => {
      try {
        const newInfo = await getKubernetesState(
          continueToken?.clouds[0] || "",
          continueToken?.token || null,
        );
        if (newInfo !== null) {
          const info = (kubernetesData || []).concat(newInfo.deployments);
          setKubernetesData(info);
        }

        if (!newInfo?.continue_token) {
          // remove last entry from clouds
          const newClouds = continueToken?.clouds.splice(1);
          if (newClouds.length === 0) {
            setIsLoading(false);
            setContinueToken({
              clouds: [],
              token: "",
              done: true,
            });
            return;
          }
          setContinueToken({
            clouds: newClouds,
            token: "",
            done: false,
          });
          setIsLoading(false);
          return;
        } else {
          setContinueToken({
            clouds: continueToken?.clouds || [],
            token: newInfo.continue_token || "",
            done: false,
          });
        }
      } catch (e) {
        console.log(`Error while loading the data: ${e}`);
        if (axios.isAxiosError(e)) {
          setKubernetesData(null);
          setIsLoading(false);
          messageApi.open({
            type: "error",
            content: `Error while loading the data: ${e.message}`,
          });
        } else {
          setKubernetesData(null);
          setIsLoading(false);
          messageApi.open({
            type: "error",
            content: `Error while loading the data`,
          });
        }
      }
    };
    fetchRun();
  }, [continueToken, kubernetesData]);

  if (kubernetesData === undefined) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"kubernetes"}
          breadcrumbs={[
            { label: "Kubernetes State", link: "/kubernetes/state" },
          ]}
        >
          <Spin />
        </LayoutComponent>
      </>
    );
  } else if (kubernetesData === null) {
    <>
      {contextHolder}
      <LayoutComponent
        selectedMenuKey={"kubernetes"}
        breadcrumbs={[{ label: "Kubernetes State", link: "/kubernetes/state" }]}
      >
        <Empty
          description="Failed to load the data."
          style={{ paddingTop: "5em" }}
        />
      </LayoutComponent>
    </>;
  } else {
    const names = uniq(kubernetesData.map((t) => t.name)).sort();
    const namespaces = uniq(kubernetesData.map((t) => t.namespace)).sort();
    const clouds = uniq(kubernetesData.map((t) => t.cloud)).sort();
    const deployment_targets = uniq(
      kubernetesData.map((t) => t.deployment_target || ""),
    ).sort();
    const versions = uniq(kubernetesData.map((t) => t.version || "")).sort();
    const deployed_bys = uniq(
      kubernetesData.map((t) => t.deployed_by || ""),
    ).sort();
    const apps = uniq(kubernetesData.map((t) => t.app || "")).sort();

    const columns: ColumnType<KubernetesData>[] = [
      {
        title: "Kubernetes Deployment Name",
        key: "deployment",
        render: (t: KubernetesData) => {
          return <Text keyboard>{t.name}</Text>;
        },
        filters: names.map((c) => {
          return {
            text: c,
            value: c,
          };
        }),
        onFilter: (value: React.Key | boolean, record: KubernetesData) =>
          record.name === (value as string),
        defaultSortOrder: "ascend",
        sorter: (a, b) => {
          return a.name.localeCompare(b.name);
        },
      },
      {
        title: "Namespace",
        key: "namespace",
        render: (t: KubernetesData) => {
          return <Text keyboard>{t.namespace}</Text>;
        },
        filters: namespaces.map((c) => {
          return {
            text: c,
            value: c,
          };
        }),
        onFilter: (value: React.Key | boolean, record: KubernetesData) =>
          record.namespace === (value as string),
        sorter: (a, b) => {
          return a.namespace.localeCompare(b.namespace);
        },
      },
      {
        title: "Cloud",
        key: "cloud",
        render: (t: KubernetesData) => {
          return <Text keyboard>{t.cloud}</Text>;
        },
        filters: clouds.map((c) => {
          return {
            text: c,
            value: c,
          };
        }),
        onFilter: (value: React.Key | boolean, record: KubernetesData) =>
          record.cloud === (value as string),
        sorter: (a, b) => {
          return a.cloud.localeCompare(b.cloud);
        },
      },
      {
        title: "Status",
        key: "Status",
        render: (t: KubernetesData) => {
          return (
            <KubernetesDeploymentConditionComponent condition={t.condition} />
          );
        },
      },
      {
        title: "Deployed By",
        key: "deployed_by",
        render: (t: KubernetesData) => {
          return <Text keyboard>{t.deployed_by}</Text>;
        },
        filters: deployed_bys.map((c) => {
          return {
            text: c,
            value: c,
          };
        }),
        onFilter: (value: React.Key | boolean, record: KubernetesData) =>
          record.deployed_by === (value as string),
        sorter: (a, b) => {
          let da = a.deployed_by || "";
          let db = b.deployed_by || "";
          return da.localeCompare(db);
        },
      },
      {
        title: "Deployment Target",
        key: "deployment_target",
        render: (t: KubernetesData) => {
          return <Text keyboard>{t.deployment_target}</Text>;
        },
        filters: deployment_targets.map((c) => {
          return {
            text: c,
            value: c,
          };
        }),
        onFilter: (value: React.Key | boolean, record: KubernetesData) =>
          record.deployment_target === (value as string),
        sorter: (a, b) => {
          let da = a.deployment_target || "";
          let db = b.deployment_target || "";
          return da.localeCompare(db);
        },
      },
      {
        title: "App",
        key: "app",
        render: (t: KubernetesData) => {
          return <Text keyboard>{t.app}</Text>;
        },
        filters: apps.map((c) => {
          return {
            text: c,
            value: c,
          };
        }),
        onFilter: (value: React.Key | boolean, record: KubernetesData) =>
          record.app === (value as string),
        sorter: (a, b) => {
          let da = a.app || "";
          let db = b.app || "";
          return da.localeCompare(db);
        },
      },
      {
        title: "Version",
        key: "version",
        render: (t: KubernetesData) => {
          return (
            <Link
              href={`https://github.com/augmentcode/augment/commit/${t.version}`}
            >
              <Text keyboard>{t.version}</Text>
            </Link>
          );
        },
        filters: versions.map((c) => {
          return {
            text: c,
            value: c,
          };
        }),
        onFilter: (value: React.Key | boolean, record: KubernetesData) =>
          record.version === (value as string),
        sorter: (a, b) => {
          let da = a.version || "";
          let db = b.version || "";
          return da.localeCompare(db);
        },
      },
      {
        title: "Timestamp",
        key: "timestamp",
        render: (t: KubernetesData) => {
          const ft = dayjs(t.condition?.lastTransitionTime)
            .local()
            .format("LLL");
          return <Text>{ft}</Text>;
        },
        sorter: (a, b) => {
          let da = a.version || "";
          let db = b.version || "";
          return da.localeCompare(db);
        },
      },
    ];

    let resultsTable = (
      <>
        <p>
          <Text>
            This page display the current state of the Kubernetes system.
          </Text>
        </p>
        <Table
          dataSource={kubernetesData}
          columns={columns}
          loading={isLoading}
        />
      </>
    );

    const children = [resultsTable];
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"kubernetes"}
          children={children}
          breadcrumbs={[
            { label: "Kubernetes State", link: "/kubernetes/state" },
          ]}
        ></LayoutComponent>
      </>
    );
  }
}

export default KubernetesStatePageComponent;

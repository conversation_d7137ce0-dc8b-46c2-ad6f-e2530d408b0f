// page displaying the details information about a deployment
import {
  Descriptions,
  Typography,
  Spin,
  Badge,
  Tag,
  Divider,
  Tabs,
} from "antd";
import { useParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import {
  DeploymentData,
  DeploymentEventData,
  getDeployTask,
  getDeployment,
  getDeploymentEvents,
} from "../lib/deployment";
import dayjs, { Dayjs } from "dayjs";
import localizedFormat from "dayjs/plugin/localizedFormat";
import utc from "dayjs/plugin/utc";
import Table, { ColumnType } from "antd/es/table";
import { uniq } from "lodash";
import {
  DeploymentConfigurationComponent,
  DeploymentOutputComponent,
  DeploymentRawComponent,
  DeploymentTask,
  DeploymentTaskStateComponent,
  transformEventsToTask,
} from "./deployment";

const { Text, Link } = Typography;

dayjs.extend(utc);
dayjs.extend(localizedFormat);

export default function DeploymentTaskPageComponent() {
  const [deploymentData, setDeploymentData] = useState<
    DeploymentData | null | undefined
  >(undefined);
  const [eventsData, setEventsData] = useState<
    DeploymentEventData[] | null | undefined
  >(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [isEventsLoading, setEventsIsLoading] = useState(true);
  let { deployId, targetName, cloud, namespace }: any = useParams();
  if (namespace == "-") {
    namespace = "";
  }
  console.log(
    `deploy id ${deployId} targetName ${targetName} cloud ${cloud} namespace ${namespace}`,
  );

  useEffect(() => {
    const fetchRun = async () => {
      if (deploymentData !== undefined) {
        return;
      }
      setIsLoading(true);
      const deploymentInfo = await getDeployment(deployId);
      console.log(`deployment ${JSON.stringify(deploymentInfo)}`);
      setDeploymentData(deploymentInfo);

      setIsLoading(false);
    };
    fetchRun();
  }, [deployId, deploymentData]);

  useEffect(() => {
    const fetchEvents = async () => {
      if (eventsData !== undefined) {
        return;
      }
      let eventsInfo: DeploymentEventData[] | null = [];
      while (true) {
        let minSeq = 0;
        if (eventsInfo.length > 0) {
          let lastEvent: DeploymentEventData =
            eventsInfo[eventsInfo.length - 1];
          minSeq = lastEvent.sequenceNumber + 1;
        }
        let newEvents = await getDeploymentEvents(deployId, minSeq, 1000);
        if (newEvents === null) {
          setEventsData(null);
          setEventsIsLoading(false);
          break;
        }
        if (newEvents.length === 0) {
          setEventsIsLoading(false);
          break;
        }
        eventsInfo.push(...newEvents);
      }

      console.log(`events ${JSON.stringify(eventsInfo)}`);
      if (eventsInfo === null) {
        setEventsData(eventsInfo);
        return;
      }
      eventsInfo = eventsInfo.filter((t) => {
        let task = getDeployTask(t);
        if (task === undefined) {
          return false;
        }
        return (
          task.targetName === targetName &&
          task.cloud === cloud &&
          task.namespace === namespace
        );
      });

      setEventsData(eventsInfo);
    };
    fetchEvents();
  }, [deployId, eventsData]);

  let tasks = transformEventsToTask(eventsData || []);
  let task: DeploymentTask | undefined = undefined;
  if (tasks.length > 0) {
    task = tasks[0];
  }
  console.log(`tasks ${JSON.stringify(task)}`);

  if (isLoading || isEventsLoading) {
    return (
      <LayoutComponent
        selectedMenuKey={"deployments"}
        breadcrumbs={[
          { label: "Deployments", link: "/deployments" },
          { label: `Deployment ${deployId}`, link: `/deployment/${deployId}` },
          {
            label: `Deployment ${deployId} ${namespace}/${targetName}@${cloud}`,
            link: `/deployment/${deployId}/task/${targetName}/${cloud}/${namespace}/`,
          },
        ]}
      >
        <Spin />
      </LayoutComponent>
    );
  } else if (deploymentData === undefined || deploymentData === null) {
    return (
      <LayoutComponent
        selectedMenuKey={"deployments"}
        breadcrumbs={[
          { label: "Deployments", link: "/deployments" },
          { label: `Deployment ${deployId}`, link: `/deployment/${deployId}` },
          {
            label: `Deployment ${deployId} ${namespace}/${targetName}@${cloud}`,
            link: `/deployment/${deployId}/task/${targetName}/${cloud}/${namespace}/`,
          },
        ]}
      >
        <Text>Deployment not found.</Text>
      </LayoutComponent>
    );
  } else {
    let tabs = (
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: "1",
            label: `Output`,
            children: <DeploymentOutputComponent events={eventsData || []} />,
          },
          {
            key: "2",
            label: `Configuration`,
            children: (
              <DeploymentConfigurationComponent events={eventsData || []} />
            ),
          },
          {
            key: "3",
            label: "Raw",
            children: (
              <DeploymentRawComponent
                events={eventsData || []}
                deploymentData={undefined}
              />
            ),
          },
        ]}
      />
    );

    const children = (
      <div>
        <Descriptions title="Deployment Task Info" bordered column={2}>
          <Descriptions.Item label="Deploy Id" span={1}>
            {deployId}
          </Descriptions.Item>
          <Descriptions.Item label="Target Name" span={1}>
            {targetName}
          </Descriptions.Item>
          <Descriptions.Item label="Cloud" span={1}>
            <Text keyboard>{cloud}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Namespace" span={1}>
            <Text keyboard>{namespace}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="State" span={1}>
            <DeploymentTaskStateComponent state={task?.state || "UNKNOWN"} />
          </Descriptions.Item>
        </Descriptions>
        <Divider />
        {tabs}
      </div>
    );
    return (
      <>
        <LayoutComponent
          children={children}
          selectedMenuKey={"deployments"}
          breadcrumbs={[
            { label: "Deployments", link: "/deployments" },
            {
              label: `Deployment ${deployId}`,
              link: `/deployment/${deployId}`,
            },
            {
              label: `Deployment ${deployId} ${namespace}/${targetName}@${cloud}`,
              link: `/deployment/${deployId}/task/${targetName}/${cloud}/${namespace}/`,
            },
          ]}
        />
      </>
    );
  }
}

// page displaying the details information about a deployment
import {
  Descriptions,
  Typography,
  Spin,
  Badge,
  Divider,
  Tabs,
  message,
  FloatButton,
  Progress,
} from "antd";
import { useParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import {
  DeploymentData,
  DeploymentEventData,
  cancelDeployment,
  getDeployTask,
  getDeployment,
  getDeploymentEvents,
  getTargetName,
} from "../lib/deployment";
import dayjs from "dayjs";
import localizedFormat from "dayjs/plugin/localizedFormat";
import utc from "dayjs/plugin/utc";
import Table, { ColumnType } from "antd/es/table";
import { uniq } from "lodash";
import axios from "axios";
import { CloseCircleOutlined } from "@ant-design/icons";

const { Text, Link } = Typography;

dayjs.extend(utc);
dayjs.extend(localizedFormat);

export function DeploymentStateComponent({ state }: { state: string }) {
  if (state === "DEPLOYMENT_STATE_NOT_SET") {
    return <Badge status="processing" text="Scheduled" />;
  } else if (state === "DEPLOYMENT_STATE_SCHEDULED") {
    return <Badge status="processing" text="Scheduled" />;
  } else if (state === "DEPLOYMENT_STATE_RUNNING") {
    return <Badge status="processing" text="Running" />;
  } else if (state === "DEPLOYMENT_STATE_SUCCEEDED") {
    return <Badge status="success" text="Success" />;
  } else if (state === "DEPLOYMENT_STATE_FAILED") {
    return <Badge status="error" text="Failed" />;
  } else if (state === "VALIDATION_FAILED") {
    return <Badge status="error" text="Validation failed" />;
  } else if (state === "DEPLOYMENT_STATE_CANCELLED") {
    return <Badge status="warning" text="Cancelled" />;
  } else {
    return <Badge status="warning" text="Unknown" />;
  }
}

function isFinalState(state: string): boolean {
  return (
    state === "DEPLOYMENT_STATE_SUCCEEDED" ||
    state === "DEPLOYMENT_STATE_FAILED" ||
    state === "DEPLOYMENT_STATE_CANCELLED" ||
    state === "VALIDATION_FAILED"
  );
}

export type DeploymentTask = {
  name: string;
  namespace: string;
  cloud: string;
  state: string;
};

export function DeploymentTaskStateComponent({ state }: { state: string }) {
  if (state === "PLANNED") {
    return <Badge status="processing" text="Scheduled" />;
  } else if (state === "STARTED") {
    return <Badge status="processing" text="Running" />;
  } else if (state === "DEPLOY_STATUS_SUCCESS") {
    return <Badge status="success" text="Success" />;
  } else if (state === "DEPLOY_STATUS_FAILED") {
    return <Badge status="error" text="Failed" />;
  } else if (state === "DEPLOY_STATUS_SKIPPED") {
    return <Badge status="warning" text="Skipped" />;
  } else if (state === "VALIDATION_FAILED") {
    return <Badge status="error" text="Validation failed" />;
  } else {
    return <Badge status="warning" text="Unknown" />;
  }
}

export function transformEventsToTask(
  events: DeploymentEventData[],
): DeploymentTask[] {
  let tasks = new Map<string, DeploymentTask>();
  for (const event of events) {
    console.log(`event ${JSON.stringify(event)}`);
    const task = getDeployTask(event);
    if (task === undefined) {
      continue;
    }
    const taskName = getTargetName(task);
    if (event.deployPlanned !== undefined) {
      tasks.set(taskName, {
        name: event.deployPlanned.task.targetName,
        namespace: event.deployPlanned.task.namespace,
        cloud: event.deployPlanned.task.cloud,
        state: "PLANNED",
      });
    }
    if (event.deployStarted !== undefined) {
      tasks.set(taskName, {
        name: event.deployStarted.task.targetName,
        namespace: event.deployStarted.task.namespace,
        cloud: event.deployStarted.task.cloud,
        state: "STARTED",
      });
    }
    if (event.deployFinished !== undefined) {
      tasks.set(taskName, {
        name: event.deployFinished.task.targetName,
        namespace: event.deployFinished.task.namespace,
        cloud: event.deployFinished.task.cloud,
        state: event.deployFinished.status,
      });
    }
    if (
      event.validationFinished !== undefined &&
      event.validationFinished.status !== "VALIDATION_SUCCESS"
    ) {
      tasks.set(taskName, {
        name: event.validationFinished.task.targetName,
        namespace: event.validationFinished.task.namespace,
        cloud: event.validationFinished.task.cloud,
        state: event.validationFinished.status,
      });
    }
  }
  let result = Array.from(tasks.values());
  result.sort((a, b) => {
    let v = a.name.localeCompare(b.name);
    if (v === 0) {
      v = a.cloud.localeCompare(b.cloud);
    }
    if (v === 0) {
      v = a.namespace.localeCompare(b.namespace);
    }
    return v;
  });
  return result;
}

const stateToPrio = {
  // pending
  "": 5,
  PLANNED: 4,
  DEPLOY_STATUS_SKIPPED: 3,
  DEPLOY_STATUS_SUCCESS: 2,
  DEPLOY_STATUS_FAILED: 1,
  STARTED: 1,
};

export function compareByState(a: DeploymentTask, b: DeploymentTask) {
  return stateToPrio[a.state] - stateToPrio[b.state];
}

function DeploymentTaskTableComponent({
  deployId,
  tasks,
  isLoading,
}: {
  deployId: string;
  tasks: DeploymentTask[];
  isLoading: boolean;
}) {
  console.log(`tasks ${JSON.stringify(tasks)}`);

  const names = uniq(tasks.map((t) => t.name)).sort();
  const namespaces = uniq(tasks.map((t) => t.namespace)).sort();
  const clouds = uniq(tasks.map((t) => t.cloud)).sort();

  const columns: ColumnType<DeploymentTask>[] = [
    {
      title: "Deployment Name",
      key: "name",
      render: (t: DeploymentTask) => {
        return (
          <Link
            href={`/deployment/${encodeURIComponent(
              deployId,
            )}/task/${encodeURIComponent(t.name)}/${encodeURIComponent(
              t.cloud,
            )}/${encodeURIComponent(t.namespace || "-")}/`}
          >
            <Text keyboard>{t.name}</Text>
          </Link>
        );
      },
      filters: names.map((c) => {
        return {
          text: c,
          value: c,
        };
      }),
      onFilter: (value: string | number | boolean, record: DeploymentTask) =>
        record.name === (value as string),
      sorter: (a, b) => {
        return a.name.localeCompare(b.name);
      },
    },
    {
      title: "Namespace",
      key: "namespace",
      render: (t: DeploymentTask) => {
        if (t.namespace === "") {
          return "";
        } else {
          return <Text keyboard>{t.namespace}</Text>;
        }
      },
      filters: namespaces.map((n) => {
        return {
          text: n,
          value: n,
        };
      }),
      onFilter: (value: string | number | boolean, record: DeploymentTask) =>
        record.namespace === (value as string),
      sorter: (a, b) => {
        return a.namespace.localeCompare(b.namespace);
      },
    },
    {
      title: "Cloud",
      key: "cloud",
      render: (t: DeploymentTask) => {
        return <Text keyboard>{t.cloud}</Text>;
      },
      filters: clouds.map((c) => {
        return {
          text: c,
          value: c,
        };
      }),
      onFilter: (value: string | number | boolean, record: DeploymentTask) =>
        record.cloud === (value as string),
      sorter: (a, b) => {
        return a.cloud.localeCompare(b.cloud);
      },
    },
    {
      title: "Status",
      key: "Status",
      render: (t: DeploymentTask) => {
        return <DeploymentTaskStateComponent state={t.state} />;
      },
      filters: [
        {
          text: "Planned",
          value: "PLANNED",
        },
        {
          text: "Started",
          value: "STARTED",
        },
        {
          text: "Success",
          value: "DEPLOY_STATUS_SUCCESS",
        },
        {
          text: "Failed",
          value: "DEPLOY_STATUS_FAILED",
        },
        {
          text: "Skipped",
          value: "DEPLOY_STATUS_SKIPPED",
        },
      ],
      defaultSortOrder: "ascend",
      onFilter: (value: string | number | boolean, record: DeploymentTask) =>
        record.state === (value as string),
      sorter: (a, b) => {
        return compareByState(a, b);
      },
    },
  ];
  let resultsTable = (
    <Table dataSource={tasks} columns={columns} loading={isLoading} />
  );
  return resultsTable;
}

export function DeploymentConfigurationComponent({
  events,
}: {
  events: DeploymentEventData[];
}) {
  let texts = events.flatMap((t) => {
    if (t.deployConfiguration !== undefined) {
      console.log(`configuration ${t.deployConfiguration.configuration}`);
      return [
        <Text style={{ whiteSpace: "pre-wrap" }} copyable>
          <pre className="language-plaintext">
            {`${t.deployConfiguration.configuration}`}
          </pre>
        </Text>,
      ];
    } else {
      return [];
    }
  });
  return <div>{texts}</div>;
}

export function DeploymentRawComponent({
  events,
  deploymentData,
}: {
  events: DeploymentEventData[];
  deploymentData: DeploymentData | undefined;
}) {
  let texts: React.ReactNode[] = [];
  if (deploymentData !== undefined) {
    texts.push(
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-json">
          {JSON.stringify(deploymentData, null, "\t")}
        </pre>
      </Text>,
    );
  }
  if (events.length > 0) {
    texts.push(
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-json">
          {JSON.stringify(events, null, "\t")}
        </pre>
      </Text>,
    );
  }
  return <div>{texts}</div>;
}

function ProgressComponent({
  results,
  state,
}: {
  results: DeploymentTask[];
  state: string;
}) {
  let plannedCount = results.filter((r) => r.state === "PLANNED").length;

  // see https://ant.design/components/progress#api
  let progressStatus = "active";
  if (isFinalState(state)) {
    if (state === "DEPLOYMENT_STATE_SUCCEEDED") {
      progressStatus = "success";
    } else {
      progressStatus = "exception";
    }
  }
  let progressPercent = 0.0;
  if (results.length > 0) {
    progressPercent = (1.0 - (1.0 * plannedCount) / results.length) * 100;
  }
  // clip to nearest full percent
  progressPercent = Math.round(progressPercent / 10) * 10;

  return <Progress percent={progressPercent} status={progressStatus} />;
}

export function DeploymentOutputComponent({
  events,
}: {
  events: DeploymentEventData[];
}) {
  let texts = events.flatMap((t) => {
    let timestamp = dayjs(t.time).local().format();
    if (t.deployInstanceStarted !== undefined) {
      return [
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {`${timestamp} Commit: ${t.deployInstanceStarted.commit}`}
          </pre>
        </Text>,
      ];
    } else if (t.deployStarted !== undefined) {
      return [
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {`${timestamp} Start ${t.deployStarted.task.namespace}/${t.deployStarted.task.targetName}@${t.deployStarted.task.cloud}`}
          </pre>
        </Text>,
      ];
    } else if (t.deployFinished !== undefined) {
      return [
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {`${timestamp} Finished ${t.deployFinished.task.namespace}/${t.deployFinished.task.targetName}@${t.deployFinished.task.namespace} ${t.deployFinished.task.cloud} -> ${t.deployFinished.status}`}
          </pre>
        </Text>,
      ];
    } else if (t.deployOutput !== undefined) {
      let output: React.ReactNode[] = [];
      if (t.deployOutput.command !== undefined) {
        output.push(
          <Text style={{ whiteSpace: "pre-wrap" }}>
            <pre className="language-plaintext">
              {`${timestamp} > ${t.deployOutput.command.join(" ")}`}
            </pre>
          </Text>,
        );
      }
      if (t.deployOutput.stdout !== undefined) {
        output.push(
          <Text style={{ whiteSpace: "pre-wrap" }}>
            <pre className="language-plaintext">
              {`${timestamp} ${t.deployOutput.stdout}`}
            </pre>
          </Text>,
        );
      }
      if (t.deployOutput.stderr !== undefined) {
        output.push(
          <Text style={{ whiteSpace: "pre-wrap" }}>
            <pre className="language-plaintext">
              {`${timestamp} ${t.deployOutput.stderr}`}
            </pre>
          </Text>,
        );
      }
      if (t.deployOutput.returnCode !== undefined) {
        output.push(
          <Text style={{ whiteSpace: "pre-wrap" }}>
            <pre className="language-plaintext">
              {`${timestamp} Return code: ${t.deployOutput.returnCode}`}
            </pre>
          </Text>,
        );
      }
      return output;
    } else if (t.validationFinished !== undefined) {
      return [
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {`${timestamp} Validation ${t.validationFinished.status}: ${t.validationFinished.message}`}
          </pre>
        </Text>,
      ];
    } else if (t.deployProgress !== undefined) {
      return [
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {`${timestamp} Progress: ${t.deployProgress.message}`}
          </pre>
        </Text>,
      ];
    } else {
      return [];
    }
  });
  return <div>{texts}</div>;
}

export default function DeploymentPageComponent() {
  const [deploymentData, setDeploymentData] = useState<
    DeploymentData | null | undefined
  >(undefined);
  // we are loading the deployment data (initially)
  const [isLoading, setIsLoading] = useState(true);

  const [eventsData, setEventsData] = useState<
    DeploymentEventData[] | null | undefined
  >(undefined);
  const [isEventsLoading, setEventsIsLoading] = useState(false);
  const [isEventsDrained, setEventsDrained] = useState(false);

  const { deployId }: any = useParams();
  console.log(`deploy id ${deployId}`);
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    const fetchRun = async () => {
      if (deploymentData === undefined) {
        setIsLoading(true);
      }
      const deploymentInfo = await getDeployment(deployId);
      console.log(`deployment ${JSON.stringify(deploymentInfo)}`);
      setDeploymentData(deploymentInfo);

      setIsLoading(false);
    };
    if (deploymentData === undefined) {
      fetchRun();
    } else if (!isFinalState(deploymentData?.state || "")) {
      const timerID = setTimeout(fetchRun, 10000);
      return () => {
        clearTimeout(timerID);
      };
    }
  }, [deployId, deploymentData]);

  useEffect(() => {
    const fetchEvents = async () => {
      const batchSize = 1000;
      let minSequenceNumber = 0;
      if (eventsData !== undefined && eventsData.length > 0) {
        let lastEvent: DeploymentEventData = eventsData[eventsData.length - 1];
        minSequenceNumber = lastEvent.sequenceNumber + 1;
      }
      try {
        const eventsInfo = await getDeploymentEvents(
          deployId,
          minSequenceNumber,
          batchSize,
        );
        console.log(`new events ${JSON.stringify(eventsInfo)}`);
        if (eventsInfo === null) {
          // deployment not found
          setEventsData(eventsInfo);
        }
        if (eventsData === undefined) {
          setEventsData(eventsInfo);
        }
        if ((eventsInfo || []).length < batchSize && !isEventsDrained) {
          setEventsDrained(true);
          setEventsIsLoading(false);
        }
        if ((eventsInfo || []).length === 0) {
          return;
        }
        let newEventsData = (eventsData || []).concat(eventsInfo);
        setEventsData(newEventsData);
      } catch (e) {
        console.log(`Error while loading the deployment data: ${e}`);
        if (axios.isAxiosError(e)) {
          messageApi.open({
            type: "error",
            content: `Error while loading the deployment data: ${e.message}`,
          });
        } else {
          messageApi.open({
            type: "error",
            content: `Error while loading the deployment data`,
          });
        }
      }
      setEventsIsLoading(false);
    };
    if (isEventsLoading) {
      // do not have two calls in parallel
      return;
    }
    // we only fetch events if we have the deployment data
    if (deploymentData === null || deploymentData === undefined) {
      return;
    }
    if (eventsData === null) {
      return;
    }
    const isFinal = (eventsData || []).some(
      (e: DeploymentEventData) => e.deployInstanceFinished !== undefined,
    );
    if (!isEventsDrained) {
      fetchEvents();
    } else if (!isFinal) {
      // until we are finished, we keep fetching
      const timerID = setTimeout(fetchEvents, 10000);
      return () => {
        clearTimeout(timerID);
      };
    }
  }, [deployId, eventsData, deploymentData]);

  if (isLoading) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"deployments"}
          breadcrumbs={[
            { label: "Deployments", link: "/deployments" },
            {
              label: `Deployment ${deployId}`,
              link: `/deployment/${deployId}`,
            },
          ]}
        >
          <Spin />
        </LayoutComponent>
      </>
    );
  } else if (deploymentData === undefined || deploymentData === null) {
    return (
      <>
        {contextHolder}
        <LayoutComponent
          selectedMenuKey={"deployments"}
          breadcrumbs={[
            { label: "Deployments", link: "/deployments" },
            {
              label: `Deployment ${deployId}`,
              link: `/deployment/${deployId}`,
            },
          ]}
        >
          <Text>Deployment not found.</Text>
        </LayoutComponent>
      </>
    );
  } else {
    let r: DeploymentData = deploymentData;

    let ft: string | undefined;
    if (dayjs(r.finishedTime).valueOf() === 0) {
      ft = "";
    } else {
      ft = dayjs(r.finishedTime).local().format("LLL");
    }

    let results: DeploymentTask[] = transformEventsToTask(eventsData || []);

    let progress = <Spin />;
    if (isEventsDrained) {
      progress = <ProgressComponent results={results} state={r.state} />;
    }

    let tabs = (
      <Tabs
        defaultActiveKey="1"
        items={[
          {
            key: "1",
            label: `Results`,
            children: (
              <DeploymentTaskTableComponent
                deployId={deployId}
                tasks={results}
                isLoading={!isEventsDrained}
              />
            ),
          },
          {
            key: "2",
            label: `Output`,
            children: <DeploymentOutputComponent events={eventsData || []} />,
          },
          {
            key: "3",
            label: "Raw",
            children: (
              <DeploymentRawComponent
                events={eventsData || []}
                deploymentData={r}
              />
            ),
          },
        ]}
      />
    );

    const children = (
      <div>
        <Descriptions title="Deployment Info" bordered column={2}>
          <Descriptions.Item label="Deploy Id" span={1}>
            {deployId}
          </Descriptions.Item>
          <Descriptions.Item label="State" span={1}>
            <DeploymentStateComponent state={r.state} />
          </Descriptions.Item>
          <Descriptions.Item label="Create Time" span={1}>
            {dayjs(r.createdTime).local().format("LLL")}
          </Descriptions.Item>
          <Descriptions.Item label="Finished Time" span={1}>
            {ft}
          </Descriptions.Item>
          <Descriptions.Item label="Commit" span={1}>
            {r.request.branch || `#${r.request.pullRequestNumber}`} /{" "}
            <Text keyboard>{r.request.commitRef}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Deployment Track" span={1}>
            <Text keyboard>{r.request.deploymentTrack}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Deployment Schedule" span={1}>
            <Text keyboard>{r.request.deploymentScheduleName || ""}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Cloud" span={1}>
            {r.request.clouds.map((c) => (
              <Text keyboard>{c}</Text>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="Namespace" span={2}>
            {r.request.namespaces.map((c) => (
              <Text keyboard>{c}</Text>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="Targets" span={2}>
            {r.request.targetNames.map((c) => (
              <Text keyboard>{c}</Text>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="Requestor" span={1}>
            {r.request.adhoc?.requestor || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Reason" span={1}>
            {r.request.adhoc?.reason || "N/A"}
          </Descriptions.Item>
          <Descriptions.Item label="Progress" span={2}>
            {progress}
          </Descriptions.Item>
        </Descriptions>
        <Divider />
        {tabs}
      </div>
    );

    let cancelFloat = <div />;
    if (r !== undefined && !isFinalState(r.state)) {
      cancelFloat = (
        <FloatButton
          icon={<CloseCircleOutlined />}
          tooltip="Cancel"
          onClick={() => {
            cancelDeployment(deployId).then(() => {
              setDeploymentData(undefined);
              setEventsData(undefined);
              setEventsIsLoading(false);
            });
          }}
          type="primary"
          style={{ right: 94 }}
        />
      );
    }

    return (
      <>
        {contextHolder}
        <LayoutComponent
          children={children}
          selectedMenuKey={"deployments"}
          breadcrumbs={[
            { label: "Deployments", link: "/deployments" },
            {
              label: `Deployment ${deployId}`,
              link: `/deployment/${deployId}`,
            },
          ]}
        />
        {cancelFloat}
      </>
    );
  }
}

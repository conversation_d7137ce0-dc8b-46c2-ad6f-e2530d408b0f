load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_project")
load("@npm//tools/deploy_runner/web/frontend:eslint/package_json.bzl", eslint_bin = "bin")

ASSET_PATTERNS = [
    "*.svg",
    "*.css",
]

SRC_PATTERNS = [
    "*.tsx",
    "routes/*.tsx",
    "lib/*.ts",
    "lib/*.tsx",
]

js_library(
    name = "src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    visibility = ["//tools/deploy_runner/web:__subpackages__"],
    deps = [
        ":src_ts",
    ],
)

ts_project(
    name = "src_ts",
    srcs = glob(
        include = SRC_PATTERNS,
    ),
    declaration = True,
    resolve_json_module = True,
    tsconfig = "//tools/deploy_runner/web/frontend:tsconfig",
    deps = [
        "//tools/deploy_runner/web/frontend:node_modules/@ant-design/icons",
        "//tools/deploy_runner/web/frontend:node_modules/antd",
        "//tools/deploy_runner/web/frontend:node_modules/axios",
        "//tools/deploy_runner/web/frontend:node_modules/dayjs",
        "//tools/deploy_runner/web/frontend:node_modules/lodash",
        "//tools/deploy_runner/web/frontend:node_modules/react",
        "//tools/deploy_runner/web/frontend:node_modules/react-dom",
        "//tools/deploy_runner/web/frontend:node_modules/react-router-dom",
        "//tools/deploy_runner/web/frontend:node_modules/react-use",
        "//tools/deploy_runner/web/frontend:node_modules/strip-ansi",
    ],
)

eslint_bin.eslint_test(
    name = "eslint_test",
    args = ["{}/{}".format(
        package_name(),
        p,
    ) for p in SRC_PATTERNS],
    data = [
        "//tools/deploy_runner/web/frontend:node_modules/eslint-config-react-app",
        "//tools/deploy_runner/web/frontend:node_modules/react",
        "//tools/deploy_runner/web/frontend:package_json",
    ] + glob(SRC_PATTERNS),
)

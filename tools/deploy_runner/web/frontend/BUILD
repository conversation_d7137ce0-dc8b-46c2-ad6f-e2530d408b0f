load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//tools/deploy_runner/web/frontend:vite/package_json.bzl", vite_bin = "bin")

npm_link_all_packages()

COMMON = [
    "index.html",
    "vite.config.js",
    "//tools/deploy_runner/web/frontend/src",
    "//tools/deploy_runner/web/frontend/public",
    ":package.json",
    ":node_modules/eslint-config-react-app",
    ":node_modules/react-dom",
    ":node_modules/@vitejs/plugin-react",
    ":node_modules/vite",
    ":node_modules/react",
    ":node_modules/typescript",
    ":node_modules/axios",
    ":node_modules/antd",
    ":node_modules/strip-ansi",
    ":node_modules/lodash",
]

ALL_MODULES = [
    ":node_modules",
]

vite_bin.vite(
    name = "frontend",
    srcs = COMMON,
    args = ["build"],
    chdir = package_name(),
    out_dirs = ["dist"],
    visibility = ["//tools/deploy_runner/web:__subpackages__"],
)

vite_bin.vite_binary(
    name = "start",
    chdir = package_name(),
    data = ALL_MODULES + COMMON,
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//tools/deploy_runner/web:__subpackages__"],
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
    visibility = ["//tools/deploy_runner/web:__subpackages__"],
)

js_library(
    name = "jest_config",
    srcs = ["jest.config.js"],
    visibility = ["//tools/deploy_runner/web:__subpackages__"],
)

import pytest
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from tools.bazel_runner.git import checkout_pb2
from tools.deploy_runner.deploy_gate import (
    BazelTestGate,
    create_deploy_gate,
    DeploymentGateFinder,
)
from tools.deploy_runner import deploy_events_pb2, metadata_pb2
from tools.bazel_runner.server.test_runner_pb2 import (
    GetTestInfoResponse,
    RunState,
    ScheduleTestResponse,
    GetRunsResponse,
    TestRunInfo,
)


def test_bazel_test_gate_test():
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=["//path/to:target"]),
    )
    bazel_test_gate = BazelTestGate(deployment_info, gate)
    assert bazel_test_gate.test()


def test_bazel_test_gate_test_method_no_targets():
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=[]),
    )
    test_gate = BazelTestGate(deployment_info, gate)
    assert not test_gate.test()


def test_bazel_test_gate_start_method_success():
    notifier = MagicMock()
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=["//path/to:target"]),
    )
    bazel_test_gate = BazelTestGate(deployment_info, gate)
    bazel_test_gate.deployment_info.deployed_by = "test_user"
    bazel_test_gate.deployment_info.bazel_runner_client.schedule.return_value = (
        ScheduleTestResponse(run_id="test_run_id")
    )
    bazel_test_gate.deployment_info.checkout = checkout_pb2.CheckoutSpec()
    assert bazel_test_gate.start(notifier)
    assert bazel_test_gate.run_id == "test_run_id"
    assert bazel_test_gate.start_time is not None


def test_bazel_test_gate_start_method_failure():
    notifier = MagicMock()
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=["//path/to:target"]),
    )
    bazel_test_gate = BazelTestGate(deployment_info, gate)
    bazel_test_gate.deployment_info.deployed_by = "test_user"
    bazel_test_gate.deployment_info.checkout = checkout_pb2.CheckoutSpec()
    bazel_test_gate.deployment_info.bazel_runner_client.schedule.side_effect = (
        Exception("Test exception")
    )
    assert not bazel_test_gate.start(notifier)


def test_bazel_test_gate_check_method_success():
    notifier = MagicMock()
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=["//path/to:target"]),
    )
    bazel_test_gate = BazelTestGate(deployment_info, gate)
    bazel_test_gate.run_id = "test_run_id"
    bazel_test_gate.start_time = datetime.now()
    bazel_test_gate.deployment_info.bazel_runner_client.get_run.return_value = (
        GetTestInfoResponse(state=RunState.RUN_STATE_PASSED)
    )
    assert bazel_test_gate.check(notifier)


def test_bazel_test_gate_check_method_failure():
    notifier = MagicMock()
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=["//path/to:target"]),
    )
    bazel_test_gate = BazelTestGate(deployment_info, gate)
    bazel_test_gate.run_id = "test_run_id"
    bazel_test_gate.start_time = datetime.now()
    bazel_test_gate.deployment_info.bazel_runner_client.get_run.return_value = (
        GetTestInfoResponse(state=RunState.RUN_STATE_ERROR)
    )
    assert not bazel_test_gate.check(notifier)


def test_bazel_test_gate_check_method_timeout():
    notifier = MagicMock()
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=["//path/to:target"]),
    )
    bazel_test_gate = BazelTestGate(deployment_info, gate)
    bazel_test_gate.run_id = "test_run_id"
    bazel_test_gate.start_time = datetime.now() - timedelta(
        seconds=1800
    )  # 30 minutes ago
    assert not bazel_test_gate.check(notifier)


def test_create_deploy_gate():
    deployment_info = MagicMock()
    gate = metadata_pb2.DeploymentGate(
        name="test_gate",
        bazel_test_gate=metadata_pb2.BazelTestGate(targets=["//path/to:target"]),
    )
    created_gate = create_deploy_gate(deployment_info, gate)
    assert isinstance(created_gate, BazelTestGate)


def test_create_deploy_gate_unknown():
    deployment_info = MagicMock()
    unknown_gate = metadata_pb2.DeploymentGate(name="unknown_gate")
    with pytest.raises(ValueError):
        create_deploy_gate(deployment_info, unknown_gate)


@patch("tools.deploy_runner.deploy_gate.find_metadata")
def test_deployment_gate_finder_find_all_gates(mock_find_metadata):
    deployment_info = MagicMock()
    mock_find_metadata.return_value = [
        metadata_pb2.Metadata(
            deployment_gates=[
                metadata_pb2.DeploymentGate(
                    name="gate1",
                    bazel_test_gate=metadata_pb2.BazelTestGate(
                        targets=["//path/to:target1"]
                    ),
                ),
                metadata_pb2.DeploymentGate(
                    name="gate2",
                    bazel_test_gate=metadata_pb2.BazelTestGate(
                        targets=["//path/to:target2"]
                    ),
                ),
            ]
        )
    ]

    finder = DeploymentGateFinder(deployment_info)
    gates = list(finder.find_all_gates())

    assert len(gates) == 2
    assert all(isinstance(gate, BazelTestGate) for gate in gates)
    assert [gate.name for gate in gates] == ["gate1", "gate2"]


@patch("tools.deploy_runner.deploy_gate.find_metadata")
def test_deployment_gate_finder_find_gates_by_name(mock_find_metadata):
    deployment_info = MagicMock()
    mock_find_metadata.return_value = [
        metadata_pb2.Metadata(
            deployment_gates=[
                metadata_pb2.DeploymentGate(
                    name="gate1",
                    bazel_test_gate=metadata_pb2.BazelTestGate(
                        targets=["//path/to:target1"]
                    ),
                ),
                metadata_pb2.DeploymentGate(
                    name="gate2",
                    bazel_test_gate=metadata_pb2.BazelTestGate(
                        targets=["//path/to:target2"]
                    ),
                ),
            ]
        )
    ]

    finder = DeploymentGateFinder(deployment_info)
    gates = list(finder.find_gates_by_name(["gate1"]))

    assert len(gates) == 1
    assert isinstance(gates[0], BazelTestGate)
    assert gates[0].name == "gate1"

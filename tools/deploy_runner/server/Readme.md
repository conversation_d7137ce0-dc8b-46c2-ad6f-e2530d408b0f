# Deploy Runner Server

The deploy runner server is a gRPC server that runs the deploy runner.
It is responsible for creating deploy pod (//tools/deploy_runner/control) and writing state to BigTable.

All external users should call the RPC service instead of creating pods directly.

## Testing

```
bazel run //tools/deploy_runner/server:kubecfg
```

And issue GRPC requests to the server.
Alternatively, the web UI can be used to issue GRPC requests

```
bazel run //tools/deploy_runner/web:kubecfg
``

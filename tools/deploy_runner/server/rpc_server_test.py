"""Tests for the GRPC server."""

from tools.deploy_runner.server.rpc_server import (
    validate_target_name,
    validate_k8s_namespace,
)


def test_validate_target_name():
    """Test validate_target_name."""
    assert validate_target_name("test-target")
    assert validate_target_name("test-target-1")
    assert validate_target_name("test_target_1")
    assert validate_target_name("test-FP8")
    assert not validate_target_name("test-target-~")


def test_validate_k8s_namespace():
    """Test validate_k8s_namespace."""
    assert validate_k8s_namespace("test-target")
    assert validate_k8s_namespace("test-target-1")
    assert not validate_k8s_namespace("test_target_1")
    assert not validate_k8s_namespace("test-FP8")

"""Subscriber to pub/sub sub to write deploy events to BigTable."""

from __future__ import annotations

import argparse
import logging
import pathlib
import grpc
import typing

from base.python.grpc import client_options
from tools.bot.bot_client import setup_client
import structlog
from prometheus_client import start_http_server

from tools.deploy_runner.server import deploy_pb2, deploy_pb2_grpc
from tools.bot import bot_pb2, bot_pb2_grpc
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from tools.deploy_runner import deploy_events_pb2, metadata_pb2
from tools.deploy_runner.server.config import Config
from google.cloud import pubsub  # type: ignore
import services.lib.grpc.tls_config.tls_config as tls_config

log = structlog.get_logger()


class Processor:
    """The main code for the bazel run server-side processing."""

    def __init__(
        self,
        slack_bot_client: bot_pb2_grpc.DevtoolsBotStub,
        deploy_client: deploy_pb2_grpc.DeployStub,
        subscription_name: str,
        config: Config,
    ):
        self.slack_bot_client = slack_bot_client
        self.deploy_client = deploy_client
        self.config = config
        self._subscriber = pubsub.SubscriberClient()
        self._topic_path = self._subscriber.topic_path(
            config.project_id, config.topic_name
        )
        self._subscription_path = self._subscriber.subscription_path(
            config.project_id, subscription_name
        )
        self._batch_size = config.subscription_batch_size

        self._cache = {}

    def process(self, events: typing.Sequence[deploy_events_pb2.DeployEvent]):
        """Process a single batch of deploy events.

        Args:
            events: The events to process.
        """
        for event in events:
            logging.info("Processing event %s", event)
            if event.HasField("deploy_finished"):
                self._process_deploy_finished(event.deploy_id, event.deploy_finished)
            if event.HasField("validation_finished"):
                self._process_validation_finished(
                    event.deploy_id, event.validation_finished
                )
            if event.HasField("deploy_instance_finished"):
                self._process_deploy_instance_finished(
                    event.deploy_id, event.deploy_instance_finished
                )

    def _get_deployment(self, deploy_id: str) -> deploy_pb2.DeploymentInfo | None:
        """Get the deployment for the given deploy_id.

        Args:
            deploy_id: The deploy_id to get the deployment for.

        Returns:
            The deployment for the given deploy_id.
        """
        if deploy_id in self._cache:
            return self._cache[deploy_id]

        try:
            request = deploy_pb2.GetDeploymentRequest()
            request.deploy_id = deploy_id
            response = self.deploy_client.GetDeployment(request)
            self._cache[deploy_id] = response.deployment
            return response.deployment
        except grpc.RpcError as ex:
            if ex.code() == grpc.StatusCode.NOT_FOUND:  # pylint: disable=no-member # type: ignore
                log.info("Deployment not found: %s", deploy_id)
                return None
            raise

    def _process_deploy_instance_finished(
        self, deploy_id: str, event: deploy_events_pb2.DeployInstanceFinished
    ):
        """Process a single deploy instance finished event.

        Args:
            event: The event to process.
        """
        deployment = self._get_deployment(deploy_id)
        if not deployment:
            return
        commit_url = f"""https://github.com/augmentcode/augment/commit/{deployment.request.commit_ref}"""
        details_url = (
            f"""https://{self.config.deploy_viewer_url}/deployment/{deploy_id}"""
        )

        request = bot_pb2.NotifyDeploymentFinishedRequest()
        request.commit = deployment.request.commit_ref
        request.commit_url = commit_url
        request.details_url = details_url
        request.deploy_id = deploy_id
        request.deployment_schedule_name = (
            metadata_pb2.Deployment.DeploymentScheduleName.Name(
                deployment.request.deployment_schedule_name
            )
        )
        for target in event.results:
            if target.status != deploy_events_pb2.DeployStatus.DEPLOY_STATUS_FAILED:
                continue
            d = request.deployments.add()
            d.name = target.task.target_name
            d.namespace = target.task.namespace
            d.cloud = target.task.cloud
            d.status = bot_pb2.DeploymentStatus.FAILED
        self.slack_bot_client.NotifyDeploymentFinished(request)

    def _process_validation_finished(
        self, deploy_id: str, event: deploy_events_pb2.ValidationFinished
    ):
        """Process a single validation finished event.

        Args:
            event: The event to process.
        """
        if event.status == deploy_events_pb2.ValidationStatus.VALIDATION_FAILED:
            deployment = self._get_deployment(deploy_id)
            if not deployment:
                return
            if event.task.namespace == "":
                namespace = "-"
            else:
                namespace = event.task.namespace
            commit_url = f"""https://github.com/augmentcode/augment/commit/{deployment.request.commit_ref}"""
            details_url = f"""https://{self.config.deploy_viewer_url}/deployment/{deploy_id}/task/{event.task.target_name}/{event.task.cloud}/{namespace}/"""

            request = bot_pb2.NotifyDeploymentRequest()
            request.name = event.task.target_name
            request.commit = deployment.request.commit_ref
            request.commit_url = commit_url
            request.namespace = event.task.namespace
            request.cloud = event.task.cloud
            request.details_url = details_url
            self.slack_bot_client.NotifyDeploymentFailed(request)

    def _process_deploy_finished(
        self, deploy_id: str, event: deploy_events_pb2.DeployFinished
    ):
        """Process a single deploy finished event.

        Args:
            event: The event to process.
        """
        if event.status == deploy_events_pb2.DeployStatus.DEPLOY_STATUS_FAILED:
            deployment = self._get_deployment(deploy_id)
            if not deployment:
                return
            commit_url = f"""https://github.com/augmentcode/augment/commit/{deployment.request.commit_ref}"""
            if event.task.namespace == "":
                namespace = "-"
            else:
                namespace = event.task.namespace
            details_url = f"""https://{self.config.deploy_viewer_url}/deployment/{deploy_id}/task/{event.task.target_name}/{event.task.cloud}/{namespace}/"""

            request = bot_pb2.NotifyDeploymentRequest()
            request.name = event.task.target_name
            request.commit = deployment.request.commit_ref
            request.commit_url = commit_url
            request.namespace = event.task.namespace
            request.cloud = event.task.cloud
            request.details_url = details_url
            self.slack_bot_client.NotifyDeploymentFailed(request)

    def run(
        self,
    ):
        """Run the subscriber main loop (does not return).

        `process_batch_fn` should attempt to handle and log any recoverable errors; if
        it raises an exception, this main loop will catch and log the exception and fail
        the entire batch. As a result, this can cause some messages to be processed
        twice.

        Args:
            process_batch_fn: Function that processes each non-empty batch of
                RequestInsightMessages.
        """
        with self._subscriber:
            log.info("Listening for messages on %s", self._subscription_path)

            while True:
                message_count = 0
                try:
                    response = self._subscriber.pull(
                        request={
                            "subscription": self._subscription_path,
                            "max_messages": self._batch_size,
                        },
                        timeout=60,
                    )

                    message_count = len(response.received_messages)
                    if message_count == 0:
                        log.info("No more messages.")
                        continue

                    log.info("Received %d messages", message_count)
                    proto_messages = []
                    for message in response.received_messages:
                        proto_message = deploy_events_pb2.DeployEvent()
                        proto_message.ParseFromString(message.message.data)
                        proto_messages.append(proto_message)

                    # Process the batch. If this raises an exception, then we
                    # will not acknowledge the messages, and will retry
                    # processing them after the configured ackDeadlineSeconds.
                    self.process(proto_messages)

                    # Acknowledge the received messages.
                    ack_ids = [m.ack_id for m in response.received_messages]
                    self._subscriber.acknowledge(
                        request={
                            "subscription": self._subscription_path,
                            "ack_ids": ack_ids,
                        }
                    )
                    log.debug(
                        "Acknowledged %d messages. Ack ids: %s",
                        message_count,
                        ack_ids,
                    )
                except Exception as ex:  # pylint: disable=broad-exception-caught
                    log.error("Error processing pub/sub message: %s", ex)
                    log.exception(ex)


def run(args: argparse.Namespace, config: Config):
    """Entry function to run the processing loop."""

    bot_stub = setup_client(config.slack_bot_endpoint)
    client_credentials = tls_config.get_client_tls_creds(config.client_mtls)
    if client_credentials:
        channel = grpc.secure_channel(
            args.deploy_endpoint, client_credentials, options=client_options.create()
        )
    else:
        channel = grpc.insecure_channel(
            args.deploy_endpoint, options=client_options.create()
        )
    deploy_stub = deploy_pb2_grpc.DeployStub(channel=channel)

    processor = Processor(
        slack_bot_client=bot_stub,
        deploy_client=deploy_stub,
        subscription_name=args.subscription_name,
        config=config,
    )

    processor.run()


def main():
    """Main function."""

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--deploy-endpoint",
        required=True,
    )
    parser.add_argument(
        "--subscription-name",
        required=True,
    )
    args = parser.parse_args()

    setup_struct_logging()
    logging.info("Args %s", args)

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)
    logging.info("Service Account %s", gcp.get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(9090)

    run(args, config)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.fatal("fatal exception %s", e)
        raise

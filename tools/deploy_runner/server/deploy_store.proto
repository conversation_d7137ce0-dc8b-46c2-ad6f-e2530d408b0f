syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "tools/deploy_runner/server/deploy.proto";

// information abou the underlying Kubernetes job
message JobInfo {
  string job_name = 1;
  string namespace = 2;
}

// information about a deployment
message Deployment {
  // unique id of the deployment
  string deploy_id = 1;

  // the request that was used to schedule the deployment
  ScheduleDeploymentRequest request = 2;

  // information about the underlying Kubernetes job
  JobInfo job_info = 3;

  // the current state
  DeploymentState state = 4;

  // time the deployment was created
  google.protobuf.Timestamp create_time = 9;

  // time the deployment was updated
  google.protobuf.Timestamp update_time = 10;

  // time the deployment was finished
  google.protobuf.Timestamp finished_time = 11;
}

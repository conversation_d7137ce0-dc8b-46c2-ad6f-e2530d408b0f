"""Subscriber to pub/sub sub to send Slack messages about deployments."""

import argparse
import logging
import pathlib
import typing

import structlog
from prometheus_client import start_http_server

from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from tools.deploy_runner import deploy_events_pb2
from tools.deploy_runner.server.config import Config
from tools.deploy_runner.server.persistence import Persistence
from google.cloud import pubsub  # type: ignore

log = structlog.get_logger()


class Processor:
    """The main code for the slack pub/sub processing."""

    def __init__(
        self,
        persistence: Persistence,
        subscription_name: str,
        config: Config,
    ):
        self.persistence = persistence
        self.config = config
        self._subscriber = pubsub.SubscriberClient()
        self._topic_path = self._subscriber.topic_path(
            config.project_id, config.topic_name
        )
        self._subscription_path = self._subscriber.subscription_path(
            config.project_id, subscription_name
        )
        self._batch_size = config.subscription_batch_size

    def process(self, events: typing.Sequence[deploy_events_pb2.DeployEvent]):
        """Process a single batch of deploy events.

        Args:
            events: The events to process.
        """
        for event in events:
            logging.info("Processing event %s", event)
        self.persistence.create_events(events)

    def run(
        self,
    ):
        """Run the subscriber main loop (does not return).

        `process_batch_fn` should attempt to handle and log any recoverable errors; if
        it raises an exception, this main loop will catch and log the exception and fail
        the entire batch. As a result, this can cause some messages to be processed
        twice.

        Args:
            process_batch_fn: Function that processes each non-empty batch of
                RequestInsightMessages.
        """
        with self._subscriber:
            log.info("Listening for messages on %s", self._subscription_path)

            while True:
                message_count = 0
                try:
                    response = self._subscriber.pull(
                        request={
                            "subscription": self._subscription_path,
                            "max_messages": self._batch_size,
                        },
                        timeout=60,
                    )

                    message_count = len(response.received_messages)
                    if message_count == 0:
                        log.info("No more messages.")
                        continue

                    log.info("Received %d messages", message_count)
                    proto_messages = []
                    for message in response.received_messages:
                        proto_message = deploy_events_pb2.DeployEvent()
                        proto_message.ParseFromString(message.message.data)
                        proto_messages.append(proto_message)

                    # Process the batch. If this raises an exception, then we
                    # will not acknowledge the messages, and will retry
                    # processing them after the configured ackDeadlineSeconds.
                    self.process(proto_messages)

                    # Acknowledge the received messages.
                    ack_ids = [m.ack_id for m in response.received_messages]
                    self._subscriber.acknowledge(
                        request={
                            "subscription": self._subscription_path,
                            "ack_ids": ack_ids,
                        }
                    )
                    log.debug(
                        "Acknowledged %d messages. Ack ids: %s",
                        message_count,
                        ack_ids,
                    )
                except Exception as ex:  # pylint: disable=broad-exception-caught
                    log.error("Error processing pub/sub message: %s", ex)
                    log.exception(ex)


def run(args: argparse.Namespace, config: Config):
    """Entry function to run the processing loop."""

    persistence = Persistence.setup(config=config)

    processor = Processor(
        persistence=persistence,
        subscription_name=args.subscription_name,
        config=config,
    )

    processor.run()


def main():
    """Main function."""

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--subscription-name",
        required=True,
    )
    args = parser.parse_args()

    setup_struct_logging()
    logging.info("Args %s", args)
    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)
    logging.info("Service Account %s", gcp.get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(9090)

    run(args, config)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.fatal("fatal exception %s", e)
        raise

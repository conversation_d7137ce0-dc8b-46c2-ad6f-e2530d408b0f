"apiVersion": "v1"
"data":
  "config.json": |-
    {
        "base_wd": "/cache/",
        "default_repo_name": "augment",
        "default_repo_owner": "augmentcode",
        "github_app_path": "/github-app"
    }
"kind": "ConfigMap"
"metadata":
  "labels":
    "app.kubernetes.io/managed-by": "kubecfg"
  "name": "test-selection-config"
  "namespace": "dev-dirk"
---
"apiVersion": "v1"
"kind": "Service"
"metadata":
  "labels":
    "app": "test-selection"
    "app.kubernetes.io/managed-by": "kubecfg"
  "name": "test-selection-svc"
  "namespace": "dev-dirk"
"spec":
  "ports":
    - "port": 50051
      "protocol": "TCP"
      "targetPort": "grpc-svc"
  "selector":
    "app": "test-selection"
---
"apiVersion": "apps/v1"
"kind": "Deployment"
"metadata":
  "labels":
    "app": "test-selection"
    "app.kubernetes.io/managed-by": "kubecfg"
  "name": "test-selection"
  "namespace": "dev-dirk"
"spec":
  "minReadySeconds": 30
  "replicas": 1
  "selector":
    "matchLabels":
      "app": "test-selection"
  "strategy":
    "rollingUpdate":
      "maxSurge": 1
      "maxUnavailable": 0
    "type": "RollingUpdate"
  "template":
    "metadata":
      "labels":
        "app": "test-selection"
        "app.kubernetes.io/managed-by": "kubecfg"
    "spec":
      "containers":
        - "image": "us-central1-docker.pkg.dev/system-services-dev/build-images/test-selection-server@sha256:1e6ed1ef4886792c89e3ae106f5e648821fb644d90407f45ef7f2c05ed4ea68b"
          "livenessProbe":
            "initialDelaySeconds": 240
            "periodSeconds": 20
            "tcpSocket":
              "port": 50051
          "name": "test-selection"
          "ports":
            - "containerPort": 50051
              "name": "grpc-svc"
          "readinessProbe":
            "initialDelaySeconds": 240
            "periodSeconds": 20
            "tcpSocket":
              "port": 50051
          "resources":
            "limits":
              "cpu": 1
              "memory": "4Gi"
          "volumeMounts":
            - "mountPath": "/config"
              "name": "config"
              "readOnly": true
            - "mountPath": "/cache"
              "name": "cache-volume"
            - "mountPath": "/github-app"
              "name": "github-app-secret"
      "volumes":
        - "emptyDir":
            "sizeLimit": "32Gi"
          "name": "cache-volume"
        - "configMap":
            "items":
              - "key": "config.json"
                "path": "config.json"
            "name": "test-selection-config"
          "name": "config"
---
"apiVersion": "apps/v1"
"kind": "Deployment"
"metadata":
  "labels":
    "app": "test-selection"
    "app.kubernetes.io/managed-by": "kubecfg"
  "name": "test-selection-2"
  "namespace": "dev-dirk"
"spec":
  "minReadySeconds": 30
  "progressDeadlineSeconds": 1000
  "replicas": 1
  "selector":
    "matchLabels":
      "app": "test-selection"
  "strategy":
    "rollingUpdate":
      "maxSurge": 1
      "maxUnavailable": 0
    "type": "RollingUpdate"
  "template":
    "metadata":
      "labels":
        "app": "test-selection"
        "app.kubernetes.io/managed-by": "kubecfg"
    "spec":
      "containers":
        - "image": "us-central1-docker.pkg.dev/system-services-dev/build-images/test-selection-server@sha256:1e6ed1ef4886792c89e3ae106f5e648821fb644d90407f45ef7f2c05ed4ea68b"
          "livenessProbe":
            "initialDelaySeconds": 240
            "periodSeconds": 20
            "tcpSocket":
              "port": 50051
          "name": "test-selection"
          "ports":
            - "containerPort": 50051
              "name": "grpc-svc"
          "readinessProbe":
            "initialDelaySeconds": 240
            "periodSeconds": 20
            "tcpSocket":
              "port": 50051
          "resources":
            "limits":
              "cpu": 1
              "memory": "4Gi"
          "volumeMounts":
            - "mountPath": "/config"
              "name": "config"
              "readOnly": true
            - "mountPath": "/cache"
              "name": "cache-volume"
            - "mountPath": "/github-app"
              "name": "github-app-secret"
      "volumes":
        - "emptyDir":
            "sizeLimit": "32Gi"
          "name": "cache-volume"
        - "configMap":
            "items":
              - "key": "config.json"
                "path": "config.json"
            "name": "test-selection-config"
          "name": "config"

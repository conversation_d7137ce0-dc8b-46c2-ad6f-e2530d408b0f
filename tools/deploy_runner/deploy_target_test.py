"""Tests for the deploy target module."""

import datetime
import logging
import pathlib
from unittest import mock
from unittest.mock import MagicMock

from tools.deploy_runner.deploy_target_factory import create_deploy_targets
import tools.deploy_runner.metadata_pb2 as metadata_pb2
from base.cloud.k8s.fake_kubectl import FakeKubectlFactory
from base.cloud.k8s.kubectl import KubeObject
from base.cloud.k8s.kubernetes_client import KubernetesClient
from tools.deploy_runner import deploy_target
from tools.deploy_runner.kubecfg_deploy_target import _is_fully_deployed
from kubernetes.client.models import V1Deployment, V1DeploymentSpec, V1DeploymentStatus

logging.basicConfig(level=logging.INFO)


def test_get_rollouts():
    rollouts = list(
        deploy_target.get_deployment_rollouts(
            pathlib.Path("tools/deploy_runner/test_data/deployments.yaml"),
            default_timeout=datetime.timedelta(minutes=10),
        )
    )
    assert rollouts == [
        deploy_target.Rollout(
            "test-selection", "dev-dirk", datetime.timedelta(minutes=10)
        ),
        deploy_target.Rollout(
            "test-selection-2", "dev-dirk", datetime.timedelta(seconds=1000)
        ),
    ]


def test_matching_filter():
    target_filter = deploy_target.MatchingTargetFilter(
        cloud_filter=None,
        namespace_filter="dev-dirk",
        env_filter="^(NOT_SET|STAGING)$",
        deployment_type_filter=None,
    )
    assert target_filter.filter(
        metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        "kubecfg",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT,
    )


def test_matching_filter_schedule_name():
    target_filter = deploy_target.MatchingTargetFilter(
        cloud_filter=None,
        namespace_filter="dev-dirk",
        env_filter="^(NOT_SET|STAGING)$",
        deployment_type_filter=None,
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.EXPERIMENTAL,
    )
    assert not target_filter.filter(
        metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        "kubecfg",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT,
    )


def test_matching_filter_schedule_name_quick_deploy():
    target_filter = deploy_target.MatchingTargetFilter(
        cloud_filter=None,
        namespace_filter=None,
        env_filter=None,
        deployment_type_filter=None,
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT,
    )
    assert not target_filter.filter(
        metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        "kubecfg",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )


def test_equality_filter_schedule_name_quick_deploy():
    target_filter = deploy_target.EqualityTargetFilter(
        cloud=["GCP_US_CENTRAL1_DEV"],
        namespace=["dev-dirk"],
        env=[],
        deployment_type=[],
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT,
    )
    assert target_filter.filter(
        metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        "kubecfg",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT,
    )
    assert not target_filter.filter(
        metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        "kubecfg",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )


def test_quick_deploy():
    target_filter = deploy_target.MatchingTargetFilter(
        cloud_filter=None,
        namespace_filter="dev-dirk",
        env_filter="^(NOT_SET|STAGING)$",
        deployment_type_filter=None,
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT,
    )
    assert not target_filter.filter(
        metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        "kubecfg",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )
    assert not target_filter.filter(
        metadata_pb2.BazelDeployment(),
        "bazel",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )

    target_filter = deploy_target.MatchingTargetFilter(
        cloud_filter=None,
        namespace_filter="dev-dirk",
        env_filter="^(NOT_SET|STAGING)$",
        deployment_type_filter=None,
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )
    assert target_filter.filter(
        metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        "kubecfg",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )
    assert target_filter.filter(
        metadata_pb2.BazelDeployment(),
        "bazel",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
    )


def test_tombstone():
    fake_kubectl_factory = FakeKubectlFactory()
    fake_kubectl_factory.kubectl.apply(
        pathlib.Path("tools/deploy_runner/test_data/deployments.yaml")
    )
    mock_kubernetes_client = MagicMock(spec=KubernetesClient)
    target = deploy_target.KubecfgTombstoneDeployTarget(
        deployment_info=deploy_target.DeploymentInfo(
            workspace=pathlib.Path("tools/deploy_runner/test_data/tombstone"),
            kubernetes_client=mock_kubernetes_client,
            kubectl_factory=fake_kubectl_factory,
            extra_bazel_startup_args=None,
            bazel_runner_client=None,
            checkout=None,
        ),
        target=metadata_pb2.Deployment(
            name="test-selection",
            kubecfg_tombstone=metadata_pb2.KubecfgTombstone(
                object=[
                    metadata_pb2.KubecfgTombstoneObject(
                        name="test-selection",
                        kind="Deployment",
                        api_version="apps/v1",
                    )
                ]
            ),
        ),
        task=metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        kubectl=fake_kubectl_factory.kubectl,
    )
    target.deploy(False, deploy_target.DeployNullNotifier())
    assert fake_kubectl_factory.kubectl.delete_objects == [
        KubeObject("apps/v1", "Deployment", "test-selection", "dev-dirk")
    ]


def test_tombstone_app():
    fake_kubectl_factory = FakeKubectlFactory()
    fake_kubectl_factory.kubectl.apply(
        pathlib.Path("tools/deploy_runner/test_data/deployments.yaml")
    )
    for obj in fake_kubectl_factory.kubectl.objects:
        print(obj)
    mock_kubernetes_client = MagicMock(spec=KubernetesClient)
    target = deploy_target.KubecfgTombstoneDeployTarget(
        deployment_info=deploy_target.DeploymentInfo(
            workspace=pathlib.Path("tools/deploy_runner/test_data/tombstone"),
            kubernetes_client=mock_kubernetes_client,
            kubectl_factory=fake_kubectl_factory,
            extra_bazel_startup_args=None,
            bazel_runner_client=None,
            checkout=None,
        ),
        target=metadata_pb2.Deployment(
            name="test-selection",
            kubecfg_tombstone=metadata_pb2.KubecfgTombstone(
                object=[
                    metadata_pb2.KubecfgTombstoneObject(
                        name="test-selection",
                        kind="app",
                    )
                ]
            ),
        ),
        task=metadata_pb2.KubeCfgTask(
            cloud=metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            namespace="dev-dirk",
            env=metadata_pb2.KubeCfgTask.Env.STAGING,
        ),
        kubectl=fake_kubectl_factory.kubectl,
    )
    target.deploy(False, deploy_target.DeployNullNotifier())
    assert set(fake_kubectl_factory.kubectl.delete_objects) == set(
        [
            KubeObject("apps/v1", "Deployment", "test-selection", "dev-dirk"),
            KubeObject("apps/v1", "Deployment", "test-selection-2", "dev-dirk"),
            KubeObject("v1", "Service", "test-selection-svc", "dev-dirk"),
        ]
    )


def _create_target(
    envs: list[metadata_pb2.KubeCfgTask.Env.ValueType], priority: int
) -> list[deploy_target.DeployTarget]:
    deployment_info = mock.Mock()
    kubecfg = metadata_pb2.KubeCfgDeployment(target="//test:target")
    for t in envs:
        t2 = kubecfg.task.add()
        t2.cloud = metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV
        t2.namespace = "dev-augie"
        t2.env = t

    return list(
        create_deploy_targets(
            deployment_info=deployment_info,
            target=metadata_pb2.Deployment(
                name="test-selection",
                kubecfg=kubecfg,
                priority=priority,
            ),
            task_filter=deploy_target.AcceptAllFilter(),
        )
    )


def _flatten(
    targets: list[list[deploy_target.DeployTarget]],
) -> list[deploy_target.DeployTarget]:
    result = []
    for t in targets:
        result.extend(t)
    return result


def test_group_targets():
    targets = _create_target([metadata_pb2.KubeCfgTask.Env.STAGING], 1)
    assert len(targets) == 1
    groups = deploy_target.order_targets(targets)
    assert len(groups) == 1
    assert len(groups[0].targets) == 1

    targets = _create_target(
        [metadata_pb2.KubeCfgTask.Env.STAGING, metadata_pb2.KubeCfgTask.Env.PROD], 1
    )
    groups = deploy_target.order_targets(targets)
    assert len(groups) == 2
    assert len(groups[0].targets) == 1

    targets = _flatten(
        [
            _create_target([metadata_pb2.KubeCfgTask.Env.PROD], 1),
            _create_target([metadata_pb2.KubeCfgTask.Env.PROD], 2),
        ]
    )
    groups = deploy_target.order_targets(targets)
    assert len(groups) == 2
    assert len(groups[0].targets) == 1


def test_is_fully_deployed_no_replicas():
    """Test that a deployment with no replicas is fully deployed.

    this is the printout of a status object with no replicas:
    ```
    'status': {'available_replicas': None,
                'collision_count': None,
                'conditions': [{'last_transition_time': datetime.datetime(2024, 10, 22, 17, 0, 37, tzinfo=tzlocal()),
                                'last_update_time': datetime.datetime(2024, 10, 22, 17, 0, 37, tzinfo=tzlocal()),
                                'message': 'Deployment has minimum availability.',
                                'reason': 'MinimumReplicasAvailable',
                                'status': 'True',
                                'type': 'Available'},
                            {'last_transition_time': datetime.datetime(2024, 10, 22, 17, 0, 37, tzinfo=tzlocal()),
                                'last_update_time': datetime.datetime(2024, 11, 26, 16, 20, 11, tzinfo=tzlocal()),
                                'message': 'ReplicaSet '
                                        '"embedder-starethanol6-16-1-proj512-7489bb8557" '
                                        'has successfully progressed.',
                                'reason': 'NewReplicaSetAvailable',
                                'status': 'True',
                                'type': 'Progressing'}],
                'observed_generation': 52,
                'ready_replicas': None,
                'replicas': None,
                'unavailable_replicas': None,
                'updated_replicas': None}}
    ```
    """
    dep = V1Deployment()
    dep.spec = MagicMock(spec=V1DeploymentSpec)
    dep.spec.replicas = 0
    dep.status = MagicMock(spec=V1DeploymentStatus)
    dep.status.replicas = None
    dep.status.available_replicas = None
    dep.status.unavailable_replicas = None
    dep.status.updated_replicas = None
    dep.status.observed_generation = 42
    assert _is_fully_deployed("test", 42, dep, lambda x: None)

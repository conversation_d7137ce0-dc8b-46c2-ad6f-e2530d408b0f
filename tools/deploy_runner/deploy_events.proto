syntax = "proto3";

import "google/protobuf/timestamp.proto";

// Events used to describe the status of a deployment job
message DeployEvent {
  // unique id of the deployment
  string deploy_id = 1;

  // increasing sequence number of all events.
  //
  // an deploy_id will neither reuse a sequence number or decrease the sequence number
  // it might skip numbers
  uint32 sequence_number = 2;
  google.protobuf.Timestamp time = 3;
  oneof event {
    // the deployment instance started
    DeployInstanceStarted deploy_instance_started = 4;

    // the deployment of a deploy target was planned
    DeployPlanned deploy_planned = 5;

    // the deployment of a deploy target was started
    DeployStarted deploy_started = 6;

    // the deployment of a deploy target finished
    DeployFinished deploy_finished = 7;

    // the output of a command executed as part of a deploy target.
    DeployOutput deploy_output = 8;

    // a progress message of a deploy target
    DeployProgress deploy_progress = 9;

    // the validation of a deploy target finished
    ValidationFinished validation_finished = 10;

    // the configuration of a deploy target was applied
    // the configuration is a k8s configuration as yaml as applied by a deploy target.
    DeployConfiguration deploy_configuration = 11;

    // the deployment of a deploy instance finished
    DeployInstanceFinished deploy_instance_finished = 12;

    DeployGateStarted deploy_gate_started = 13;

    DeployGateFinished deploy_gate_finished = 14;
  }
}

message DeployInstanceStarted {
  // the commit that was deployed
  string commit = 1;
}

message DeployTask {
  // the name of the deploy target
  string target_name = 1;

  // the namespace of the deploy target
  // empty for cluster-wide targets
  string namespace = 2;

  // the cloud of the deploy target
  string cloud = 3;
}

message DeployResult {
  // the task of the deploy result
  DeployTask task = 1;

  // the status of the deploy result
  DeployStatus status = 2;
}

message DeployInstanceFinished {
  repeated DeployResult results = 1;
}

message DeployPlanned {
  DeployTask task = 1;
}

message DeployStarted {
  DeployTask task = 1;
}

message DeployProgress {
  DeployTask task = 1;
  string message = 2;
}

enum DeployStatus {
  DEPLOY_STATUS_UNKNOWN = 0;
  DEPLOY_STATUS_SUCCESS = 1;
  DEPLOY_STATUS_FAILED = 2;
  DEPLOY_STATUS_SKIPPED = 3;
}

message DeployFinished {
  DeployTask task = 1;
  DeployStatus status = 2;
  string message = 3;
}

message DeployOutput {
  DeployTask task = 1;
  repeated string command = 2;
  string stdout = 3;
  string stderr = 4;
  int32 return_code = 5;
}

enum ValidationStatus {
  VALIDATION_UNKNOWN = 0;
  VALIDATION_SUCCESS = 1;
  VALIDATION_FAILED = 2;
}

message ValidationFinished {
  DeployTask task = 1;
  string message = 2;
  ValidationStatus status = 3;
}

message DeployConfiguration {
  DeployTask task = 1;
  // the applied configuration as yaml
  string configuration = 2;
}

message DeployGateStarted {
  string name = 1;
}

enum DeployGateStatus {
  DEPLOY_GATE_STATUS_UNKNOWN = 0;
  DEPLOY_GATE_STATUS_SUCCESS = 1;
  DEPLOY_GATE_STATUS_FAILED = 2;
}

message DeployGateFinished {
  string name = 1;
  DeployGateStatus status = 2;
  string message = 3;
}

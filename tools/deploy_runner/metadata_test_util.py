"""Test Binary to test a metadata configuration."""

import argparse
import logging
import os
import pathlib
import sys
import time
from unittest.mock import Magic<PERSON>ock
import re
import hashlib

from base.cloud.k8s import fake_kubectl
from base.logging.console_logging import setup_console_logging
from base.test_utils.junit_xml_writer import JUnitXMLWriter
from tools.deploy_runner import (
    deploy_gate,
    deploy_target,
    deploy_target_factory,
    metadata,
    metadata_pb2,
)


def check_name(target: deploy_target.DeployTarget) -> bool:
    """Checks that the name only contains alphanumeric characters and dashes.

    Returns True if the name is valid, False otherwise.
    """
    if re.match(r"^[a-z0-9-]+$", target.target_name):
        return True
    return False


def check_length(target: deploy_target.DeployTarget) -> bool:
    """Checks that the name is less than or equal to 63 characters.

    Returns True if the name is valid, False otherwise.

    Don't check for tombstones.
    """
    return "tombstone" in target.target_name or len(target.target_name) <= 63


def _hash(n: str) -> int:
    """Deterministically hash a string."""
    hash_object = hashlib.sha256(n.encode())
    return int(hash_object.hexdigest(), 16)


def _check_deployment(
    deployment: metadata_pb2.Deployment,
    writer: JUnitXMLWriter,
    max_tasks: int,
    shard_index: int,
    shard_total: int,
) -> tuple[bool, str]:
    """Check a single deployment.

    Returns True if the deployment passed, False otherwise.
    """
    mock_kubernetes_client = MagicMock()
    info = deploy_target.DeploymentInfo(
        workspace=pathlib.Path("."),
        kubernetes_client=mock_kubernetes_client,
        kubectl_factory=fake_kubectl.FakeKubectlFactory(),
        bazel_runner_client=None,
        checkout=None,
    )

    if (
        deployment.deployment_schedule_name
        == metadata_pb2.Deployment.DeploymentScheduleName.EXPERIMENTAL
        and deployment.health.tier != metadata_pb2.ServiceTier.TIER_2
    ):
        return (
            False,
            f"Invalid health tier for experimental deployment: {deployment.health.tier}",
        )

    targets = deploy_target_factory.create_deploy_targets(
        info,
        deployment,
        task_filter=deploy_target.AcceptAllFilter(),
    )

    if len(targets) > max_tasks:
        targets = targets[0:max_tasks]

    for target in targets:
        if shard_total > 1 and _hash(target.name) % shard_total != shard_index:
            continue

        start_time = time.time()
        logging.info("Testing %s", target)

        if not check_name(target):
            return False, f"Invalid name: {target.target_name}"

        if not check_length(target):
            return False, f"Invalid length: {target.target_name}"

        if (
            target.env == metadata_pb2.KubeCfgTask.Env.PROD
            and (
                deployment.HasField("kubecfg") or deployment.HasField("bazel")
            )  # ignore tombstone
            and deployment.health.tier == metadata_pb2.ServiceTier.TIER_UNSPECIFIED
        ):
            return (
                False,
                f"Invalid tier for deployment to PRODUCTION: {deployment.health.tier}",
            )

        r = target.test()
        end_time = time.time()
        if not r:
            writer.add_testcase(
                name=target.name,
                classname=target.target_name,
                time=end_time - start_time,
                status="failed",
            )
            return False, f"Test {target.name} failed"
        else:
            writer.add_testcase(
                name=target.name,
                classname=target.target_name,
                time=end_time - start_time,
                status="passed",
            )
    return True, ""


def _check_deployments(
    result: metadata_pb2.Metadata,
    writer: JUnitXMLWriter,
    max_tasks: int,
    shard_index: int = 0,
    shard_total: int = 1,
) -> tuple[bool, list[str]]:
    """Check all deployments in a metadata file.

    Returns True if all deployments passed, False otherwise.
    """
    failed = False
    failures = []
    for deployment in result.deployment:
        res, output = _check_deployment(
            deployment,
            writer,
            max_tasks,
            shard_index=shard_index,
            shard_total=shard_total,
        )
        if not res:
            failed = True
            failures.append(output)
            continue
    return not failed, failures


def _check_gate(
    gate: metadata_pb2.DeploymentGate,
    writer: JUnitXMLWriter,
) -> tuple[bool, str]:
    """Check a single gate.

    Returns True if the gate passed, False otherwise.
    """
    mock_kubernetes_client = MagicMock()
    info = deploy_target.DeploymentInfo(
        workspace=pathlib.Path("."),
        kubernetes_client=mock_kubernetes_client,
        kubectl_factory=fake_kubectl.FakeKubectlFactory(),
        bazel_runner_client=None,
        checkout=None,
    )

    g = deploy_gate.create_deploy_gate(
        info,
        gate,
    )
    start_time = time.time()
    logging.info("Testing %s", g.name)

    if not re.match(r"^[a-z0-9-]+$", gate.name):
        return False, f"Invalid name: {gate.name}"

    r = g.test()
    end_time = time.time()
    if not r:
        writer.add_testcase(
            name=g.name,
            classname=g.name,
            time=end_time - start_time,
            status="failed",
        )
        return False, f"Test {g.name} failed"
    else:
        writer.add_testcase(
            name=g.name,
            classname=g.name,
            time=end_time - start_time,
            status="passed",
        )
    return True, ""


def _check_gates(
    result: metadata_pb2.Metadata,
    writer: JUnitXMLWriter,
    shard_index: int = 0,
    shard_total: int = 1,
) -> tuple[bool, list[str]]:
    """Check all gates in a metadata file.

    Returns True if all gates passed, False otherwise.
    """
    del shard_total
    if shard_index > 0:
        logging.info("Skipping gates because shard index is %d", shard_index)
        return True, []
    failed = False
    failures = []
    for gate in result.deployment_gates:
        res, output = _check_gate(
            gate,
            writer,
        )
        if not res:
            failed = True
            failures.append(output)
            continue
    return not failed, failures


def main():
    """Main entry function."""

    parser = argparse.ArgumentParser()
    parser.add_argument("--max-tasks-per-target", type=int, default=1000)
    parser.add_argument("--file", type=pathlib.Path, required=True)
    args = parser.parse_args()

    shard_status_file = os.environ.get("TEST_SHARD_STATUS_FILE")
    if shard_status_file:
        with open(shard_status_file, "w") as f:
            f.write("PASS")

    setup_console_logging(add_timestamp=False)

    logging.debug("%s", args)

    writer = JUnitXMLWriter()

    shard_index = int(os.environ.get("TEST_SHARD_INDEX", "0"))
    shard_total = int(os.environ.get("TEST_TOTAL_SHARDS", "1"))

    failed = False
    result = metadata.read_metadata_file(args.file.absolute())
    if not result:
        failed = True
    else:
        res, failures = _check_deployments(
            result,
            writer,
            args.max_tasks_per_target,
            shard_index=shard_index,
            shard_total=shard_total,
        )
        if not res:
            failed = True
            logging.error(f"Here are the failures: {failures}")

        res, failures = _check_gates(
            result,
            writer,
            shard_index=shard_index,
            shard_total=shard_total,
        )
        if not res:
            failed = True
            logging.error(f"Here are the failures: {failures}")

    xml_file = os.environ.get("XML_OUTPUT_FILE")
    if xml_file:
        logging.info("Writing results to %s", xml_file)
        writer.to_file(pathlib.Path(xml_file))

    if failed:
        logging.error("Test Suite Failed")
        sys.exit(2)
    else:
        logging.info("Test Suite Passed")
        sys.exit(0)


if __name__ == "__main__":
    main()

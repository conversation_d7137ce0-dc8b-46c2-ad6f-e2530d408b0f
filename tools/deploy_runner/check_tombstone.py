"""Tool to check if tombstones can be deleted"""

# pylint: disable=logging-fstring-interpolation
import argparse
import logging
import os
import sys
from pathlib import Path

from base.cloud.k8s.kubectl import KubectlException
from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.logging.console_logging import setup_console_logging
from tools.deploy_runner.deploy_target import (
    DeploymentInfo,
    DeployTarget,
    KubecfgTombstoneDeployTarget,
    MatchingTargetFilter,
)
from tools.deploy_runner.deployment_finder import DeploymentFinder
import tools.deploy_runner.metadata_pb2 as metadata_pb2  # pylint: disable=no-name-in-module


def check_all_targets(
    targets: list[DeployTarget],
) -> list[DeployTarget]:
    """Return the targets that are live"""
    live_targets = filter(check_target, targets)
    return [t for t in live_targets]


def check_target(
    target: DeployTarget,
) -> bool:
    """return true if the target has objects to delete, false otherwise"""
    assert isinstance(target, KubecfgTombstoneDeployTarget)
    try:
        objects = target.find()
        if len(objects) == 0:
            logging.info("%s has no objects to delete", target.name)
            return False
        for obj in objects:
            logging.info("Found object: %s", obj)
        logging.warning("%s has objects to delete", target.name)
        return True
    except KubectlException as ex:
        logging.info("%s", ex.stdout)
        logging.error("%s", ex.stderr)
        logging.error("Error while checking tombstone %s", target.name)
        logging.error("%s", ex)
        raise ex


def main():
    """Entrypoint."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument("--extra-args")
    parser.add_argument("--extra-startup-args")
    parser.add_argument(
        "--workspace",
        required=not os.environ.get("BUILD_WORKSPACE_DIRECTORY"),
        default=os.environ.get("BUILD_WORKSPACE_DIRECTORY"),
    )
    parser.add_argument(
        "--cloud-filter",
        default=None,
        type=str.upper,
        choices=[
            "GCP_US_CENTRAL1_PROD",
            "GCP_EU_WEST4_PROD",
            "GCP_US_CENTRAL1_DEV",
            "GCP_US_CENTRAL1_GSC_PROD",
        ],
    )
    parser.add_argument(
        "--env-filter", help="Filter for environments to deploy to", default=None
    )
    parser.add_argument(
        "--namespace-filter", help="Filter for namespaces to deploy to", default=None
    )
    targets_group = parser.add_mutually_exclusive_group(required=False)
    targets_group.add_argument(
        "--target-name",
        help="target name",
    )
    targets_group.add_argument(
        "--check-all-app-deployments",
        action="store_true",
        help="Check all app label tombstones against all k8s deployments, but no other tombstones or k8s resources",
    )
    targets_group.add_argument(
        "--list-targets", action="store_true", help="List all targets"
    )
    parser.add_argument(
        "--kube-config-file", default=Path.home().joinpath(".kube", "config"), type=Path
    )
    args = parser.parse_args()

    workspace = Path(args.workspace)

    target_filter = MatchingTargetFilter(
        cloud_filter=args.cloud_filter,
        namespace_filter=args.namespace_filter,
        env_filter=args.env_filter,
        deployment_type_filter="kubecfg_tombstone",
        deployment_schedule_name=metadata_pb2.Deployment.DeploymentScheduleName.DEFAULT,
    )

    kube_client = create_kubernetes_client(args.kube_config_file)

    kubectl_factory = create_kubectl_factory(args.kube_config_file)

    information = DeploymentInfo(
        workspace=workspace,
        kubernetes_client=kube_client,
        kubectl_factory=kubectl_factory,
        extra_bazel_startup_args=args.extra_startup_args,
        extra_bazel_args=args.extra_args,
        bazel_runner_client=None,
        allow_rollback=False,
        checkout=None,
    )

    deployer = DeploymentFinder(
        information,
        target_filter=target_filter,
    )

    if args.target_name:
        targets = list(deployer.find_targets_by_name([args.target_name]))
        if len(targets) == 0:
            logging.error(
                "Tombstone %s not found. Please double check the tombstone name.",
                args.target_name,
            )
            sys.exit(1)
        live_targets = check_all_targets(targets)
        if len(live_targets) == 0:
            logging.info(
                "Tombstone %s has no effect (already run or invalid) and can be removed",
                args.target_name,
            )
        else:
            logging.debug(f"Tombstone targets: {live_targets=}")
            logging.info(
                "Tombstone %s is valid, will delete %s of %s targets, and cannot be removed",
                args.target_name,
                len(live_targets),
                len(targets),
            )
    elif args.check_all_app_deployments:
        # This is a hack to have a quick way to look at all tombstones and give
        # a hint as to which ones may still be valid and which ones may be able
        # to be removed. Most tombstones target app labels and want to remove
        # deployments, so this is very specific to that case.

        # Find all tombstones that target app labels, and gather those labels
        targets = list(deployer.find_all_targets())
        all_target_names = set()
        target_apps = {}
        for target in targets:
            if type(target) == KubecfgTombstoneDeployTarget:
                for obj in target.target.kubecfg_tombstone.object:
                    if obj.kind == "app":
                        cloud_name = metadata_pb2.KubeCfgTask.Cloud.Name(target.cloud)
                        if cloud_name not in target_apps:
                            target_apps[cloud_name] = {}
                        if target.namespace not in target_apps[cloud_name]:
                            target_apps[cloud_name][target.namespace] = {}
                        target_apps[cloud_name][target.namespace][obj.name] = (
                            target.target_name
                        )
                        all_target_names.add(target.target_name)

        # Look at all k8s deployments, and keep track of the ones that match the
        # tombstone labels
        found_deployment = set()
        for cloud in [
            "GCP_US_CENTRAL1_PROD",
            "GCP_EU_WEST4_PROD",
            "GCP_US_CENTRAL1_GSC_PROD",
        ]:
            print(f"Cloud: {cloud}")
            cloud_apps = target_apps.get(cloud, {})
            kubectl = kubectl_factory(cloud)
            namespaces = kubectl.list("namespaces", None)
            namespaces = [n["metadata"]["name"] for n in namespaces]
            for n in namespaces:
                namespace_apps = cloud_apps.get(n, {})
                print(f"\tNamespace: {n}")
                deployments = kubectl.list("deployments", n)
                for d in deployments:
                    labels = d.get("metadata", {}).get("labels", {})
                    app = labels.get("app") or labels.get("app.kubernetes.io/name")
                    if app and app in namespace_apps:
                        print(
                            f"\t\tFound deployment to tombstone: cloud={cloud}, namespace={n}, app={app}, tombstone={namespace_apps[app]}"
                        )
                        found_deployment.add(namespace_apps[app])

        # Finally, print a summary
        def format_deployments(deployments):
            s = sorted(deployments)
            return "\n".join(f"\t\t{d}" for d in s)

        print()
        print("Summary:")
        print(
            f"\tFound deployments for tombstones:\n{format_deployments(found_deployment)}"
        )
        print(
            f"\tDid not find deployments for tombstones:\n{format_deployments(all_target_names - found_deployment)}"
        )
        print()
        print("NOTE: This ONLY covers tombstones that target 'app' labels.")
        print("NOTE: This also ONLY covers tombstones that target k8s Deployments.")
        print()
    else:
        targets = list(deployer.find_all_targets())
        targets_by_cloud = {}
        for target, cloud in [(target.target_name, target.cloud) for target in targets]:
            if cloud not in targets_by_cloud:
                targets_by_cloud[cloud] = []
            targets_by_cloud[cloud].append(target)
        for cloud in targets_by_cloud:
            print(
                f"Cloud: {metadata_pb2.KubeCfgTask.Cloud.Name(cloud)}"
                if cloud
                else "Cloud: None"
            )
            for target in sorted(set(targets_by_cloud[cloud])):
                print(f"  {target}")
    sys.exit(0)


if __name__ == "__main__":
    main()

"""Wrapper around low-level queue handling."""

import logging
import typing
import time

import google.api_core.exceptions
from google.cloud import pubsub_v1  # type: ignore

from tools.deploy_runner.quick_deploy.config import Config
from tools.bazel_runner.github_webhook import github_pb2


class GcpGithubQueue:
    """Wrapper around low-level queue handling."""

    def __init__(
        self,
        config: Config,
        subscriber: pubsub_v1.SubscriberClient,
    ):
        self.config = config
        self.subscriber = subscriber
        subscription_name = "projects/{project_id}/subscriptions/{sub}".format(
            project_id=config.project_id,
            sub=config.github_subscription,
        )
        self.subscription_name = subscription_name

    @classmethod
    def create(cls, config):
        subscriber = pubsub_v1.SubscriberClient()
        return cls(config, subscriber)

    def receive(self) -> typing.Iterable[github_pb2.GithubEvent]:
        """Listen to run notification events.
        Pulling the next even will acknowledge the previous one.
        """
        while True:
            try:
                response = self.subscriber.pull(
                    request=pubsub_v1.types.PullRequest(  # type: ignore
                        subscription=self.subscription_name,
                        max_messages=1,
                    )
                )

                for message in response.received_messages:
                    event = github_pb2.GithubEvent()
                    event.ParseFromString(message.message.data)

                    yield event

                ack_ids = [message.ack_id for message in response.received_messages]
                if ack_ids:
                    ack_request = pubsub_v1.types.AcknowledgeRequest(  # type: ignore
                        subscription=self.subscription_name,
                        ack_ids=ack_ids,
                    )
                    self.subscriber.acknowledge(request=ack_request)
            except google.api_core.exceptions.DeadlineExceeded:
                pass
            except google.api_core.exceptions.ServiceUnavailable:
                time.sleep(1)

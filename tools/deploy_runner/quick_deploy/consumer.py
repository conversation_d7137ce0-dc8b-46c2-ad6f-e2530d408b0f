"""CI Event Consumers."""

from datetime import timedelta
import logging
import pathlib
import re
import typing

import sys
import grpc

from base.cloud.k8s.kubectl_factory import KubectlFactory
from base.cloud.k8s.kubernetes_client import KubernetesClient
import tools.bazel_runner.git.checkout_pb2 as checkout_pb2
from tools.bazel_runner.git import checkout
from tools.bazel_runner.github_webhook import github_pb2
from tools.bot import bot_pb2, bot_pb2_grpc
from tools.deploy_runner.quick_deploy.config import Config
from tools.deploy_runner import deploy_target, deployment_finder
from tools.deploy_runner import metadata_pb2


class Consumer(typing.Protocol):
    """A consumer of test events."""

    def on_github(self, event: github_pb2.GithubEvent) -> None:
        """Called when a github event is received.

        If the callback throws an exception, the event will be re-queued.
        """
        raise NotImplementedError()


class QuickDeployConsumer(Consumer):
    """Consumer that will a test run based on pull requests."""

    def __init__(
        self,
        run_checkout: checkout.Checkout,
        config: Config,
        kube_client: KubernetesClient,
        kubectl_factory: KubectlFactory,
        slack_bot_client: bot_pb2_grpc.DevtoolsBotStub | None,
        pod_name: str,
    ):
        self.run_checkout = run_checkout
        self.config = config
        self.kube_client = kube_client
        self.kubectl_factory = kubectl_factory
        self.slack_bot_client = slack_bot_client
        self.pod_name = pod_name

        self.target_filter = deploy_target.MatchingTargetFilter(
            self.config.cloud_filter if self.config.cloud_filter else None,
            self.config.namespace_filter if self.config.namespace_filter else None,
            self.config.env_filter if self.config.env_filter else None,
            None,
            metadata_pb2.Deployment.DeploymentScheduleName.QUICK_DEPLOY,
        )

    def on_github(self, event: github_pb2.GithubEvent):
        if not event.HasField("push"):
            return
        try:
            push = event.push
            if not push.ref == f"refs/heads/{self.config.source_branch}":
                return
            if self.config.pusher_filter and not re.match(
                self.config.pusher_filter, push.pusher
            ):
                return
            logging.info("Detected %s:", event)
            logging.info("Deploying %s", event.push.after)

            # current main branch
            spec = checkout_pb2.CheckoutSpec()
            spec.commit_checkout.branch = self.config.source_branch
            spec.commit_checkout.ref = push.after
            spec.owner = self.config.repo_owner
            spec.repo_name = self.config.repo_name
            repo_dir, _ = self.run_checkout.checkout(spec, checkout_id=None)

            deployment_info = deploy_target.DeploymentInfo(
                workspace=pathlib.Path(repo_dir),
                kubernetes_client=self.kube_client,
                kubectl_factory=self.kubectl_factory,
                deployed_by=self.pod_name,
                extra_bazel_startup_args=self.config.extra_startup_args,
                extra_bazel_args=self.config.extra_args,
                bazel_runner_client=None,
                checkout=None,
            )

            finder = deployment_finder.DeploymentFinder(
                deployment_info,
                target_filter=self.target_filter,
            )
            targets = list(finder.find_all_targets())
            targets = deploy_target.order_targets(targets)
            if not targets:
                logging.info("No targets found")
            failed = False
            for target_group in targets:
                for target in target_group.targets:
                    logging.info("Deploying %s", target)
                    r = target.deploy(
                        dry_run=self.config.dry_run,
                        notifier=deploy_target.DeployNullNotifier(),
                    )
                    if r is None:
                        logging.info("Skipping %s", target)
                    elif not r:
                        logging.error("Failed to deploy %s", target)

                        if self.slack_bot_client:
                            commit_url = f"""https://github.com/{event.push.repo_owner}/{event.push.repo_name}/commit/{event.push.after}"""

                            request = bot_pb2.NotifyDeploymentRequest()
                            request.name = target.target_name
                            request.commit = event.push.after
                            request.commit_url = commit_url
                            request.namespace = target.namespace or ""
                            request.cloud = (
                                metadata_pb2.KubeCfgTask.Cloud.Name(target.cloud)
                                if target.cloud
                                else ""
                            )
                            self.slack_bot_client.NotifyDeploymentFailed(request)

                        failed = True
                    else:
                        logging.info("Deployed %s", target)
            if failed:
                logging.error("Failed to deploy %s", event.push.after)
                # let's panic
                sys.exit(1)
            logging.info("Finished deploying %s", event.push.after)
        except checkout.CheckoutException as ex:  # pylint: disable=broad-except
            if ex.status_code == grpc.StatusCode.INVALID_ARGUMENT:
                # the commit is not on the branch, we don't need to do anything
                logging.info("Commit is not on branch %s", self.config.source_branch)
                return
            else:
                raise

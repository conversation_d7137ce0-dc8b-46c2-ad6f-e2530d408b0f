function(cloud)
  // a clusterrole limited to the resources that are accessed by the quick deploy targets
  local deployRules = [
    {
      apiGroups: [''],
      resources: ['namespaces'],
      verbs: ['get', 'list'],
    },
    {
      apiGroups: ['eng.augmentcode.com'],
      resources: ['tenants'],
      verbs: ['*'],
    },
    {
      apiGroups: ['iam.cnrm.cloud.google.com'],
      resources: ['iamcustomroles', 'iampartialpolicies', 'iampolicymembers'],
      verbs: ['*'],
    },
    {
      apiGroups: ['cloudidentity.cnrm.cloud.google.com'],
      resources: ['cloudidentitygroups', 'cloudidentitymemberships'],
      verbs: ['*'],
    },
  ];
  local deployRole = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRole',
    metadata: {
      name: 'quick-deploy-role',
    },
    rules: deployRules,
  };
  [
    deployRole,
  ]

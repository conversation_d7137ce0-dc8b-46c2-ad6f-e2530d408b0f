# Quick deploy server

The quick deploy server is a server that will listen for github events:
On a push to the `main` branch, it will run the deployment targets marked for `quick_deploy`.
This is a way to quickly deploy changes to the system.

The reasons this is not part of the CI system are:

- The subscription here is FIFO, which can be important for the deployment. We do not want to a deployment to fail, it gets retries later and undoes some later state. For all CI consumers, the ordering doesn't matter.
- This service is not part of CI, but CD.
- This has to run with higher permissions (to do the deployment). It didn't feel right to get all CI run with that higher permissions.

## Security Considerations

### Where should this run

Similar to the scheduled and adhoc deploy, this runs in the Lead Production cluster.

### What permissions does it need

This is part of the deployment system. However, it doesn't run with the
full deploy-role permissions as the deploy-role is too powerful.
The service account has more limited permissions, e.g. at this point
only adding and removing `Tenant` information. In case, we find
other use cases for quick deploy, the permissions can be expanded.

The server also has access to the readonly github app.

## Privacy Considerations

The server doesn't have access to any user data.

## Observability Considerations

The server emits metrics for the events it processes.

### Error Handling

It will emit slack messages should a deployment fail.
We will run all deployments in a single job, so if one fails, the whole job will fail.

The push event all quick deploy targets of the given commit will be deployed again in case of failure (with exponential backoff).
The events are processed in order to ensure a proper ordering of the deployment calls.

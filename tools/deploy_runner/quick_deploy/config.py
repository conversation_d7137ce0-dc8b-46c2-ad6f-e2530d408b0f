"""Module containing the config for the quick deploy server."""

import pathlib
from dataclasses import dataclass
import typing

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class Config:
    """Configuration for the server."""

    # GCP project id
    project_id: str

    # GCP region
    region: str

    # the path to the github app. The directory should contain
    # the app id, installation id, and the private key.
    github_app_path: str

    # name of the subscription
    github_subscription: str

    # the ack deadline for the github subscription (in seconds)
    github_ack_deadline: int

    base_directory: str

    source_branch: str

    pusher_filter: str

    repo_owner: str

    repo_name: str

    # extra arguments to pass to the deployer
    extra_args: str = ""
    extra_startup_args: str = ""

    # the slack bot endpoint to use
    slack_bot_endpoint: typing.Optional[str] = None

    # filters targets by cloud name
    cloud_filter: str = ""

    # filters targets by namespace via regex
    namespace_filter: str = ""

    # filters targets by environment via regex
    env_filter: str = ""

    dry_run: bool = False

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )

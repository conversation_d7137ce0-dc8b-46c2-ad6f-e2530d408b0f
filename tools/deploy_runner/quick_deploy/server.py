"""The entrypoint for the quick deploy server."""

import argparse
from contextlib import contextmanager
import fcntl
import logging
import os
import pathlib
import sys

import structlog
from prometheus_client import Counter, start_http_server

import tools.deploy.kube_config_copy as kube_config_copy

from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud.gcp import get_active_gcp_service_account
from tools.deploy_runner.quick_deploy import config, gcp
from tools.deploy_runner.quick_deploy.consumer import Consumer, QuickDeployConsumer
from tools.bazel_runner.git import app_token, checkout
from tools.bot.bot_client import setup_client

log = structlog.get_logger()

_event_counter = Counter(
    "quick_deploy_events",
    "Number of github webhook events processed by the quick deploy server",
)

_failure_counter = Counter(
    "quick_deploy_failures",
    "Number of github webhook events failed to be processed by the quick deploy server",
)


class Server:
    """Server class."""

    def __init__(
        self,
        github_queue: gcp.GcpGithubQueue,
        event_consumer: Consumer,
    ):
        """Constructor."""
        self.github_queue = github_queue
        self.event_consumer = event_consumer

    def start(self):
        """Run the server."""
        self.listen_github_events()

    def listen_github_events(self):
        """Listens on job events in the test namespace, so that the processor can ack on them."""
        try:
            for event in self.github_queue.receive():
                with _failure_counter.count_exceptions():
                    _event_counter.inc()
                    self.event_consumer.on_github(event)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logging.error("Error while listen for github events: %s", ex)
            logging.exception(ex)
            os._exit(1)


@contextmanager
def _lock(base_directory: pathlib.Path):
    """Lock the cache volume against other pods.

    The cache volume is sometimes incorrectly accessible by two pods at the same time.
    """
    file_path = base_directory / "lock"
    logging.info("Using lock file %s", file_path)
    file_lock = file_path.open("a")

    try:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_EX)
        logging.info("File locked")
        yield
    finally:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_UN)
        logging.info("File unlocked")
        file_lock.close()


def main():
    """Main function."""
    # tini will reap zombies for us. Bazel gets confused by zombie bazel processes.
    if os.getpid() == 1:
        os.execv(  # nosec (B606)
            "/usr/bin/tini-static",
            ["/usr/bin/tini-static", "-g", "--", sys.executable] + sys.argv[:],
        )

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--kube-config-file",
        default=pathlib.Path("tools/deploy/auth_kube_config.yaml"),
        type=pathlib.Path,
    )
    args = parser.parse_args()

    setup_struct_logging()

    current_config = config.Config.load_config(args.config_file)
    logging.info("Config %s", current_config)
    logging.info("Service Account %s", get_active_gcp_service_account())

    # begin listening for Prometheus requests
    start_http_server(9090)

    token_gen = app_token.GitHubAppTokenSource.from_directory(
        pathlib.Path(current_config.github_app_path)
    )

    run_checkout = checkout.Checkout(
        base_wd=pathlib.Path(current_config.base_directory),
        github_user="app",
        token_source=token_gen,
        home_path=pathlib.Path.home(),
    )
    run_checkout.setup()

    kube_client = create_kubernetes_client(args.kube_config_file)
    kubectl_factory = create_kubectl_factory(
        kube_config_file=args.kube_config_file,
    )

    # copy the kube config to the home directory, unless one exists already
    # This prevents us from clobbering a user's working kube config
    kube_config_dotfile_path = pathlib.Path.home() / ".kube" / "config"

    if not kube_config_dotfile_path.exists():
        kube_config_copy.copy(args.kube_config_file, kube_config_dotfile_path)

    if current_config.slack_bot_endpoint:
        bot_stub = setup_client(current_config.slack_bot_endpoint)
    else:
        bot_stub = None

    consumer = QuickDeployConsumer(
        run_checkout=run_checkout,
        config=current_config,
        kube_client=kube_client,
        kubectl_factory=kubectl_factory,
        slack_bot_client=bot_stub,
        pod_name=os.environ.get("POD_NAME") or "unknown",
    )

    with _lock(pathlib.Path(current_config.base_directory)):
        github_queue = gcp.GcpGithubQueue.create(current_config)
        server = Server(github_queue, consumer)
        server.start()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.fatal("fatal exception %s", e)
        raise

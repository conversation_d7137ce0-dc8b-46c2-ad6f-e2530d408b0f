"""Finds deployment targets in a workspace."""

# pylint: disable=logging-fstring-interpolation
import logging
import sys
import typing

import tools.deploy_runner.metadata_pb2 as metadata_pb2  # pylint: disable=no-name-in-module
from tools.deploy_runner.deploy_target import DeploymentInfo, DeployTarget, TargetFilter
from tools.deploy_runner.deploy_target_factory import create_deploy_targets
from tools.deploy_runner.metadata import find_metadata


class DeploymentFinder:
    """Finds deployment targets in a workspace."""

    def __init__(
        self,
        deployment_info: DeploymentInfo,
        target_filter: TargetFilter,
    ):
        self.deployment_info = deployment_info
        self.target_filter = target_filter

    def _read_metadata(self):
        metadata = find_metadata(self.deployment_info.workspace)
        targets: dict[str, metadata_pb2.Deployment] = {}  # type: ignore
        for m in metadata:
            for deployment_target in m.deployment:
                if deployment_target.name in targets:
                    logging.error("Duplicate target '%s'", deployment_target)
                    sys.exit(1)
                targets[deployment_target.name] = deployment_target
        logging.debug("Found metadata %s", targets)
        return targets

    def find_all_targets(self) -> typing.Iterable[DeployTarget]:
        """Finds all targets in the workspace."""
        targets = self._read_metadata()
        for target in [
            create_deploy_targets(
                self.deployment_info,
                targets[t],
                task_filter=self.target_filter,
            )
            for t in targets
        ]:
            if target is not None:
                yield from target

    def find_targets_by_name(
        self, target_names: list[str]
    ) -> typing.Iterable[DeployTarget]:
        """Finds all targets in the given list in the workspace."""
        targets = self._read_metadata().values()
        for target in [
            create_deploy_targets(
                self.deployment_info,
                t,
                task_filter=self.target_filter,
            )
            for t in targets
            if t.name in target_names
        ]:
            if target is not None:
                yield from target

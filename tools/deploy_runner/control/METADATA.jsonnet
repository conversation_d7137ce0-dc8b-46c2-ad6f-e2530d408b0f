// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'deploy-control',
      kubecfg: {
        target: '//tools/deploy_runner/control:kubecfg',
        task: [
          {
            // while the control pod only runs in US_CENTRAL1_PROD prod, the deployment created
            // configuration in all clouds.
            cloud: 'ALL',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

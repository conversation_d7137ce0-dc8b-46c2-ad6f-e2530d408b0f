import argparse
import logging
import pathlib

from base.cloud.k8s.kubectl import KubectlException
from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import KubernetesClientImpl
from base.logging.console_logging import setup_console_logging
from tools.deploy_runner.control import deploy_pod_factory
from tools.deploy_runner.server import deploy_pb2


def main():
    """Main entry function when used as a binary."""

    setup_console_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument("--cloud", type=str, default="GCP_US_CENTRAL1_DEV")
    parser.add_argument("--branch", type=str, default="bazel-last-known-good")
    parser.add_argument("--pull-request-number", type=str, default=None)
    parser.add_argument("--ref", type=str, default="HEAD")
    parser.add_argument(
        "--namespace",
        type=str,
        required=True,
        help="The namespace the job should run in",
    )
    parser.add_argument(
        "--env", type=str, required=True, help="The environment the job should run in"
    )
    parser.add_argument(
        "--runner-image", type=str, required=True, help="The runner image to use"
    )
    parser.add_argument(
        "--kube-config-file",
        type=pathlib.Path,
        default=pathlib.Path("tools/deploy/auth_kube_config.yaml"),
        help="The kube config file to use",
    )

    parser.add_argument(
        "--target-cloud",
        type=str,
        default=[],
        help="The cloud filter to deploy",
        action="append",
    )
    parser.add_argument(
        "--target-env",
        type=str,
        default=[],
        help="The env filter to deploy",
        action="append",
    )
    parser.add_argument("--target-namespace", type=str, default=[], action="append")
    parser.add_argument("--target-name", type=str, default=[], action="append")

    args = parser.parse_args()
    logging.info("Args %s", args)

    try:
        kubernetes_client = KubernetesClientImpl(kube_config_file=args.kube_config_file)
        kubectl_factory = create_kubectl_factory(kube_config_file=args.kube_config_file)
        kubectl = kubectl_factory(args.cloud)

        f = deploy_pod_factory.DeployRunnerControlClient(
            kubernetes_client=kubernetes_client,
            kubectl=kubectl,
            namespace=args.namespace,
            runner_image=args.runner_image,
            cloud=args.cloud,
            env=args.env,
            topic_name="",
        )

        deploy_id = f.schedule(
            branch=args.branch if not args.pr else None,
            pull_request_number=args.pull_request_number if args.pr else None,
            ref=args.ref,
            clouds=args.target_cloud,
            envs=args.target_env,
            namespaces=args.target_namespace,
            target_names=args.target_name,
            pause=False,
            allow_rollback=False,
            deployment_track=deploy_pb2.DeploymentTrack.DEFAULT,
            deployment_schedule_name=None,
        )
        logging.info("Deploy id %s", deploy_id)
    except KubectlException as ex:
        if ex.stdout:
            logging.error("%s", ex.stdout)
        if ex.stderr:
            logging.error("%s", ex.stderr)
        logging.exception(ex)
        logging.error("Kubectl error %s", ex)


if __name__ == "__main__":
    main()

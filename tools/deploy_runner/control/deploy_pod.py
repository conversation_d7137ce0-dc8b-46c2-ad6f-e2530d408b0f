"""Entrypoint for the deploy pod."""

import argparse
import fcntl
import logging
import os
import pathlib
import sys
import time
from contextlib import contextmanager
from datetime import datetime, timedelta
import threading

from concurrent import futures

import grpc
import git
import structlog
from prometheus_client import Histogram, start_http_server

from tools.bazel_runner.web.client.client import <PERSON>zelRunnerClient, IapConfig
import tools.deploy_runner.metadata_pb2 as metadata_pb2  # pylint: disable=no-name-in-module
from base.cloud.k8s.docker import prepare_docker_credential_helper
from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import env_info
from tools.bazel_runner.git import app_token, checkout, checkout_pb2
from tools.deploy_runner import (
    deploy_events_pb2,
    deploy_gate,
    deploy_target,
    deployment_finder,
)
from tools.deploy_runner.deploy_target import DeployTarget
from tools.deploy_runner.control import config, notify

INF = float("inf")
LATENCY_BUCKETS = [
    60 * i for i in [0, 1, 2, 4, 6, 8, 10, 12, 14, 16, 24, 32, 64, 128, INF]
]
_deploy_target_latency = Histogram(
    "au_deploy_job_target_latency",
    "Time to deploy a target by the deploy job",
    ["name", "env", "namespace", "result"],
    buckets=LATENCY_BUCKETS,
)

_validate_target_latency = Histogram(
    "au_deploy_job_target_validate_latency",
    "Time to validate a target by the deploy job",
    ["name", "env", "namespace", "result"],
    buckets=LATENCY_BUCKETS,
)

# we hardcode the repo owner and name to ensure that no configuration mistake can override
# the invariants
ALLOWED_REPO_OWNER = "augmentcode"
ALLOWED_REPO_NAME = "augment"
ALLOWED_BRANCHES = ["bazel-last-known-good", "main"]


def _get_free_disk_space(directory: pathlib.Path) -> int:
    """Returns the free disk space in bytes."""
    statvfs = os.statvfs(directory)
    free_space = statvfs.f_frsize * statvfs.f_bavail
    return free_space


class Deployer:
    """Performs the deployments."""

    def __init__(
        self,
        deployment_info: deploy_target.DeploymentInfo,
        current_config: config.Config,
        notifier: deploy_target.DeployNotifier,
        executor: futures.Executor,
    ):
        """Constructor.

        Args:
            deployment_info: DeploymentInfo
            current_config: Config
            notifier: DeployNotifier
            delayed_validation: if set to true the validation will be delayed and not done directly after a change was applied
        """
        self.deployment_info = deployment_info
        self.config = current_config
        self.notifier = notifier
        self.executor = executor
        self.lock = threading.Lock()

        self.failed_targets: list[DeployTarget] = []
        self.deployed_targets: list[DeployTarget] = []
        self.skipped_targets: list[DeployTarget] = []

        deployment_schedule_name = None
        if self.config.deployment_schedule_name:
            deployment_schedule_name = (
                metadata_pb2.Deployment.DeploymentScheduleName.Value(
                    self.config.deployment_schedule_name.upper()
                )
            )

        self.target_filter = deploy_target.EqualityTargetFilter(
            cloud=self.config.target_clouds,
            namespace=self.config.target_namespaces,
            env=self.config.target_envs,
            deployment_type=[],
            deployment_schedule_name=deployment_schedule_name,
        )

    def prepare(self):
        """Prepare the environment."""
        # ensure that the right docker push authentication helper is called
        prepare_docker_credential_helper()

    def validate_pending(
        self,
        targets: list[DeployTarget],
    ):
        """Validates the pending targets."""
        for target in targets:
            start_target = time.time()
            result = "success"
            try:
                if not target.validate(self.notifier):
                    logging.error("Target %s failed validation", target)
                    result = "failure"
                    self.failed_targets.append(target)
                self.deployed_targets.append(target)
            finally:
                _validate_target_latency.labels(
                    target.target_name,
                    metadata_pb2.KubeCfgTask.Env.Name(target.env) if target.env else "",
                    target.namespace if target.namespace else "",
                    result,
                ).observe(time.time() - start_target)

    def should_skip_target(self, target: DeployTarget) -> bool:
        """Returns true if the target should be skipped.

        This is used to skip targets that failed in staging.

        Args:
            target: the target to check

        Returns:
            True if the target should be skipped, False otherwise

        """
        with self.lock:
            return target.env == metadata_pb2.KubeCfgTask.Env.PROD and any(
                t
                for t in self.failed_targets
                if t.env == metadata_pb2.KubeCfgTask.Env.STAGING
                and t.target_name == target.target_name
            )

    def deploy_target(self, target: DeployTarget) -> tuple[DeployTarget, str]:
        with structlog.contextvars.bound_contextvars(
            name=target.name,
            target_name=target.target_name,
            namespace=target.namespace,
            env=metadata_pb2.KubeCfgTask.Env.Name(target.env) if target.env else "",
        ):
            start_target = time.time()
            result = "success"
            try:
                if self.should_skip_target(target):
                    logging.info("Skipping target %s due to prior failure", target)
                    result = "skip_due_to_prior_failure"
                    return target, result

                r = target.deploy(
                    dry_run=self.config.dry_run,
                    notifier=self.notifier,
                )
                if r is None:
                    result = "skip"
                elif r:
                    pass
                else:
                    result = "failure"
            except Exception:  # pylint: disable=broad-except
                result = "exception"
                raise
            finally:
                _deploy_target_latency.labels(
                    target.target_name,
                    metadata_pb2.KubeCfgTask.Env.Name(target.env) if target.env else "",
                    target.namespace if target.namespace else "",
                    result,
                ).observe(time.time() - start_target)
            return target, result

    def _check_gates(self, gates: list[deploy_gate.DeployGate]) -> bool:
        """Runs the before prod gates.

        Returns true if all gates passed, false otherwise.
        """
        failed = False
        if gates:
            gates = gates[:]
            # wait for all gates to finish
            while gates:
                finished = []
                for gate in gates:
                    c = gate.check(self.notifier)
                    if c is None:
                        continue
                    if not c:
                        if self.config.abort_on_deploy_gate_failure:
                            logging.error("Gate %s failed: Aborting", gate.name)
                            failed = True
                        else:
                            logging.info("Gate %s failed: Ignored", gate.name)
                    else:
                        logging.info("Gate %s passed", gate.name)
                    finished.append(gate)
                for gate in finished:
                    gates.remove(gate)
                time.sleep(30)
        return not failed

    def _before_prod(self, gates: list[deploy_gate.DeployGate]) -> bool:
        """Runs the before prod gates.

        Returns true if all gates passed, false otherwise.
        """
        start_time = datetime.now()
        before_prod_gates = [
            g
            for g in gates
            if g.location == metadata_pb2.DeploymentGateLocation.LOCATION_BEFORE_PROD
        ]
        failed = False
        if before_prod_gates:
            logging.info("Running before prod gates: %s", before_prod_gates)
            for gate in before_prod_gates:
                if not gate.start(self.notifier):
                    if self.config.abort_on_deploy_gate_failure:
                        logging.error("Gate %s failed: Aborting", gate.name)
                        failed = True
                    else:
                        logging.info("Gate %s failed: Ignored", gate.name)

            if not self._check_gates(before_prod_gates):
                failed = True

        pause_after_staging = timedelta(minutes=self.config.pause_after_staging_minutes)
        end_time = start_time + pause_after_staging
        delta = end_time - datetime.now()
        logging.info("Pausing for %s", delta)
        if delta.total_seconds() > 0:
            time.sleep(delta.total_seconds())

        return not failed

    def run_all_targets(
        self,
        targets: list[DeployTarget],
        gates: list[deploy_gate.DeployGate],
    ):
        """Runs all targets."""

        target_groups = deploy_target.order_targets(targets)
        to_validate = []

        logging.info("Deployment Plan: %s", [target.name for target in targets])
        for group in target_groups:
            for target in group.targets:
                self.notifier.planned(
                    deploy_events_pb2.DeployPlanned(
                        task=deploy_events_pb2.DeployTask(
                            target_name=target.target_name,
                            namespace=target.namespace or "",
                            cloud=metadata_pb2.KubeCfgTask.Cloud.Name(target.cloud)
                            if target.cloud
                            else "",
                        )
                    )
                )

        last_group = None
        for group in target_groups:
            if (
                last_group
                and last_group.env == metadata_pb2.KubeCfgTask.Env.STAGING
                and group.env == metadata_pb2.KubeCfgTask.Env.PROD
            ):
                if not self._before_prod(gates):
                    logging.error("Before prod gates failed: Aborting")
                    return 1

            logging.info(
                "Deploying group env=%s, priority=%s",
                metadata_pb2.KubeCfgTask.Env.Name(group.env) if group.env else "",
                group.priority,
            )
            results = self.executor.map(self.deploy_target, group.targets)
            for target, result in results:
                with self.lock:
                    if result == "skip":
                        self.skipped_targets.append(target)
                    elif result == "failure" or result == "exception":
                        self.failed_targets.append(target)
                    elif result == "success":
                        to_validate.append(target)
                    elif result == "skip_due_to_prior_failure":
                        pass
                    else:
                        raise ValueError(f"Unknown result {result}")

            logging.info(
                "Validating group env=%s, priority=%s",
                metadata_pb2.KubeCfgTask.Env.Name(group.env) if group.env else "",
                group.priority,
            )
            self.validate_pending(to_validate)
            to_validate = []
            last_group = group

        for target in self.skipped_targets:
            logging.info("Skipped target: %s", target.name)
        for target in self.deployed_targets:
            logging.info("Deployed target: %s", target.name)
        for target in self.failed_targets:
            logging.warning("Failed target: %s", target.name)

        results = [
            deploy_events_pb2.DeployResult(
                task=deploy_events_pb2.DeployTask(
                    target_name=target.target_name,
                    namespace=target.namespace or "",
                    cloud=metadata_pb2.KubeCfgTask.Cloud.Name(target.cloud)
                    if target.cloud
                    else "",
                ),
                status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_SUCCESS,
            )
            for target in self.deployed_targets
        ]
        results.extend(
            deploy_events_pb2.DeployResult(
                task=deploy_events_pb2.DeployTask(
                    target_name=target.target_name,
                    namespace=target.namespace or "",
                    cloud=metadata_pb2.KubeCfgTask.Cloud.Name(target.cloud)
                    if target.cloud
                    else "",
                ),
                status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_FAILED,
            )
            for target in self.failed_targets
        )
        self.notifier.deploy_instance_finished(
            deploy_events_pb2.DeployInstanceFinished(results=results)
        )

        return len(self.failed_targets)

    def run(self):
        finder = deployment_finder.DeploymentFinder(
            self.deployment_info,
            target_filter=self.target_filter,
        )
        if self.config.target_names:
            targets = list(finder.find_targets_by_name(self.config.target_names))
        else:
            targets = list(finder.find_all_targets())
        if not targets:
            logging.info("No targets found")
            return 0

        gate_finder = deploy_gate.DeploymentGateFinder(self.deployment_info)
        gates = list(gate_finder.find_all_gates())

        return self.run_all_targets(targets, gates)


@contextmanager
def _lock(base_directory: pathlib.Path):
    """Lock the cache volume against other pods.

    The cache volume is sometimes incorrectly accessible by two pods at the same time.
    """
    file_path = base_directory / "lock"
    logging.info("Using lock file %s", file_path)
    file_lock = file_path.open("a")

    try:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_EX)
        logging.info("File locked")
        yield
    finally:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_UN)
        logging.info("File unlocked")
        file_lock.close()


def _check_config(args: argparse.Namespace, current_config: config.Config):
    if current_config.repo_owner != ALLOWED_REPO_OWNER:
        logging.error(
            "Repo owner %s does not match allowed repo owner %s",
            current_config.repo_owner,
            ALLOWED_REPO_OWNER,
        )
        sys.exit(1)

    if current_config.repo_name != ALLOWED_REPO_NAME:
        logging.error(
            "Repo name %s does not match allowed repo name %s",
            current_config.repo_name,
            ALLOWED_REPO_NAME,
        )
        sys.exit(1)

    if current_config.branch and current_config.branch not in ALLOWED_BRANCHES:
        logging.error(
            "Branch '%s' does not match allowed branches %s",
            current_config.branch,
            ALLOWED_BRANCHES,
        )
        sys.exit(1)
    if current_config.pull_request_number and current_config.branch:
        logging.error(
            "Cannot specify both branch and pull request number. Branch: %s, PR: %s",
            current_config.branch,
            current_config.pull_request_number,
        )
        sys.exit(1)
    if not current_config.pull_request_number and not current_config.branch:
        logging.error("Must specify either branch or pull request number")
        sys.exit(1)

    if not args.kube_config_file:
        logging.error("Kube config file not set")
        sys.exit(1)


def prepare(current_config: config.Config, directory: pathlib.Path):
    free_space = _get_free_disk_space(directory)
    if free_space < current_config.minimal_free_disk_gb * 1024 * 1024 * 1024:
        logging.warning("Low disk space. Wiping")
        # we do not wipe the checkout directories as their might be required
        # branches for other runs
        checkout.wipe(directory / ".cache")
        checkout.wipe(directory / "bazel-root")
    else:
        logging.info("Free disk space: %.3fGiB", free_space / (1024 * 1024 * 1024))


def main():
    """Entrypoint."""
    # tini will reap zombies for us. Bazel gets confused by zombie bazel processes.
    if os.getpid() == 1:
        os.execv(  # nosec (B606)
            "/usr/bin/tini-static",
            ["/usr/bin/tini-static", "-g", "--", sys.executable] + sys.argv[:],
        )

    setup_struct_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--kube-config-file",
        default=pathlib.Path("tools/deploy/auth_kube_config.yaml"),
        type=pathlib.Path,
    )
    parser.add_argument("--config")
    parser.add_argument("--config-file", type=pathlib.Path)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if not args.config and not args.config_file:
        logging.error("Must specify --config or --config-file")
        sys.exit(1)

    if args.config_file:
        current_config = config.Config.load_config(args.config_file)
    else:
        current_config = config.Config.load_config_from_str(args.config)
    logging.info("Config %s", current_config)
    env_info.print_env_info()

    _check_config(args, current_config)

    structlog.contextvars.bind_contextvars(run_id=current_config.deploy_id)

    # gcloud doesn't seem to reliably start if this is in the environment.
    if "PYTHONSAFEPATH" in os.environ:
        del os.environ["PYTHONSAFEPATH"]

    # begin listening for Prometheus requests
    start_http_server(9090)

    if current_config.pubsub:
        notifier = notify.PubSubNotifier.create_from_args(
            current_config,
        )
    else:
        notifier = deploy_target.DeployNullNotifier()

    try:
        with _lock(pathlib.Path(current_config.base_directory)):
            prepare(current_config, pathlib.Path(current_config.base_directory))

            if current_config.github_app_path:
                token_gen = app_token.GitHubAppTokenSource.from_directory(
                    pathlib.Path(current_config.github_app_path)
                )
                run_checkout = checkout.Checkout(
                    base_wd=pathlib.Path(current_config.base_directory),
                    github_user="app",
                    token_source=token_gen,
                    home_path=pathlib.Path.home(),
                    require_approval_on_pull_request=True,
                )
                run_checkout.setup()

                checkout_spec = checkout_pb2.CheckoutSpec()
                checkout_spec.owner = current_config.repo_owner
                checkout_spec.repo_name = current_config.repo_name
                if current_config.pull_request_number:
                    checkout_spec.pull_request_checkout.pull_request_number = int(
                        current_config.pull_request_number
                    )
                    checkout_spec.pull_request_checkout.ref = current_config.ref or ""
                else:
                    assert current_config.branch
                    checkout_spec.commit_checkout.branch = current_config.branch
                    checkout_spec.commit_checkout.ref = current_config.ref or ""

                try:
                    repo_dir, branch = run_checkout.checkout(
                        checkout_spec, checkout_id=current_config.branch or None
                    )
                except checkout.CheckoutException as ex:
                    notifier.progress(
                        deploy_events_pb2.DeployProgress(
                            task=None,
                            message=ex.message,
                        )
                    )
                    raise
            else:
                logging.info("Running in local mode")
                repo_dir = pathlib.Path(current_config.base_directory)
                checkout_spec = None
            repo = git.Repo(str(repo_dir))  # type: ignore
            logging.info("SHA %s", repo.head.commit.hexsha)

            sha = repo.head.commit.hexsha

            notifier.deploy_instance_started(
                deploy_events_pb2.DeployInstanceStarted(
                    commit=sha,
                )
            )

            deploy_pod_name = os.getenv("POD_NAME") or "deploy"

            kube_client = create_kubernetes_client(args.kube_config_file)
            kubectl_factory = create_kubectl_factory(
                kube_config_file=args.kube_config_file,
            )

            deployment_info = deploy_target.DeploymentInfo(
                workspace=pathlib.Path(repo_dir),
                kubernetes_client=kube_client,
                kubectl_factory=kubectl_factory,
                deployed_by=deploy_pod_name,
                extra_bazel_startup_args=current_config.extra_startup_args,
                extra_bazel_args=current_config.extra_args,
                ram_limit_gb=current_config.ram_limit_gb,
                cpu_limit=current_config.cpu_limit,
                allow_rollback=current_config.allow_rollback,
                bazel_runner_client=BazelRunnerClient(IapConfig()),
                checkout=checkout_spec,
            )

            executor = futures.ThreadPoolExecutor(current_config.num_parallel_targets)

            deployer = Deployer(
                deployment_info,
                current_config=current_config,
                notifier=notifier,
                executor=executor,
            )
            deployer.prepare()
            r = deployer.run()
    except BaseException as ex:  # pylint: disable=broad-exception-caught
        logging.error("Error while running deploy: %s", ex)
        logging.exception(ex)
        notifier.deploy_instance_finished(deploy_events_pb2.DeployInstanceFinished())
        r = 1
    sys.exit(r)


if __name__ == "__main__":
    main()

"""Module to create deploy runner pods."""

import datetime
import json
import logging
import pathlib
import subprocess
import tempfile
import time
import typing
import uuid
from dataclasses import dataclass
from enum import Enum

from tools.deploy_runner.server import deploy_pb2
import tools.deploy_runner.metadata_pb2 as metadata_pb2
import ulid

from base.cloud.k8s.kubectl import Kubectl, KubectlException
from base.cloud.k8s.kubernetes_client import KubernetesClient

JSONNET_BIN = "../jsonnet_go~/cmd/jsonnet/jsonnet_/jsonnet"


@dataclass
class Deployment:
    """Represents a deployment."""

    deploy_id: uuid.UUID
    """The unique id of the deployment."""

    job_name: str
    """The name of the job."""

    namespace: str
    """The namespace the job is running in."""


class JobState(Enum):
    """Represents the state of a job."""

    # the job has been created, but the latest pod is not running.
    SCHEDULED = 1

    # the job has currently a running pod
    RUNNING = 2

    # the job has been executed (and retried) and is marked failed.
    # Note a single failed pod is not failing the job, only when the pod
    # failed multiple times.
    FAILED = 3

    # the job has succeeded
    SUCCEEDED = 4

    # The job doesn't exist (likely externally removed)
    # usually an deleted job is similar to a failed job
    MISSING = 5


class DeployRunnerControlClient:
    """Client to execute remote deploy runner tests and wait for the results."""

    def __init__(
        self,
        kubernetes_client: KubernetesClient,
        kubectl: Kubectl,
        namespace: str,
        runner_image: str,
        cloud: str,
        env: str,
        topic_name: str,
    ):
        self.kubernetes_client = kubernetes_client
        self.kubectl = kubectl
        self.namespace = namespace
        self.runner_image = runner_image
        self.cloud = cloud
        self.env = env
        self.topic_name = topic_name

        logging.info(
            "Setup controller: namespace %s, runner image %s",
            self.namespace,
            self.runner_image,
        )

    def _create_job(self, yaml_config_file: pathlib.Path):
        """Create a job based on the yaml config provided."""
        logging.info("Create job %s", yaml_config_file.read_text(encoding="utf-8"))
        try:
            r = self.kubectl.apply(yaml_config_file)
            assert r.returncode == 0
            if r.stdout:
                logging.info("%s", r.stdout)
            if r.stderr:
                logging.error("%s", r.stderr)
        except KubectlException as ex:
            if ex.stdout:
                logging.info("%s", ex.stdout)
            if ex.stderr:
                logging.info("%s", ex.stderr)
            logging.error("Failed to create job: %s", ex)
            raise

    def _generate_config(
        self,
        deploy_id: uuid.UUID,
        branch: str | None,
        pull_request_number: str | None,
        ref: str,
        clouds: list[str],
        envs: list[str],
        namespaces: list[str],
        target_names: list[str],
        pause: bool,
        allow_rollback: bool,
        deployment_track: deploy_pb2.DeploymentTrack.ValueType,
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType
        | None,
    ) -> pathlib.Path:
        """Generate the job configuration."""
        deployment_schedule_str = None
        if deployment_schedule_name is not None:
            deployment_schedule_str = (
                metadata_pb2.Deployment.DeploymentScheduleName.Name(
                    deployment_schedule_name
                ).lower()
            )
        with tempfile.NamedTemporaryFile(delete=False) as config_file:
            subprocess.check_call(
                [
                    JSONNET_BIN,
                    "tools/deploy_runner/control/deploy_runner.jsonnet",
                    "-y",
                    "-J",
                    ".",
                    "--tla-str",
                    f"cloud={self.cloud}",
                    "--tla-str",
                    f"namespace={self.namespace}",
                    "--tla-str",
                    f"env={self.env}",
                    "--tla-str",
                    f"deployId={str(deploy_id)}",
                    "--tla-str",
                    f"imageName={self.runner_image}",
                    "--tla-str",
                    f"branch={branch or ''}",
                    "--tla-str",
                    f"pull_request_number={pull_request_number or ''}",
                    "--tla-str",
                    f"ref={ref}",
                    "--tla-str",
                    f"topicName={self.topic_name}",
                    "--tla-code",
                    f"targetClouds={json.dumps(clouds)}",
                    "--tla-code",
                    f"targetEnvs={json.dumps(envs)}",
                    "--tla-code",
                    f"targetNamespaces={json.dumps(namespaces)}",
                    "--tla-code",
                    f"targetNames={json.dumps(target_names)}",
                    "--tla-code",
                    f"pause={json.dumps(pause)}",
                    "--tla-code",
                    f"allowRollback={json.dumps(allow_rollback)}",
                    "--tla-code",
                    f"deploymentTrack={json.dumps(deploy_pb2.DeploymentTrack.Name(deployment_track).lower())}",
                    "--tla-code",
                    f"deploymentScheduleName={json.dumps(deployment_schedule_str)}",
                ],
                stdout=config_file,
            )
            return pathlib.Path(config_file.name)

    def _try_delete_job(self, job_name: str):
        """Tries to delete a given job.

        However, if that fails for any reason that is not reported further.
        """
        try:
            logging.info("Delete job %s", job_name)
            v1 = self.kubernetes_client.get_batch_api(self.cloud)
            result = v1.delete_namespaced_job(name=job_name, namespace=self.namespace)
            logging.info("Delete job result %s", result)

            # delete all pods
            v1 = self.kubernetes_client.get_core_api(self.cloud)
            ret = v1.list_namespaced_pod(
                self.namespace, label_selector=f"job-name={job_name}"
            )
            for pod in ret.items:
                logging.info("Delete pod %s", pod.metadata.name)
                v1.delete_namespaced_pod(
                    name=pod.metadata.name, namespace=self.namespace
                )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            # ignore any error
            logging.debug("Failed to delete job %s", ex)

    def _get_pod_for_job(self, job_name: str) -> str | None:
        """Finds the pod for the given pod."""
        v1 = self.kubernetes_client.get_core_api(self.cloud)
        ret = v1.list_namespaced_pod(
            self.namespace, label_selector=f"job-name={job_name}"
        )
        pod_name = None
        for pod in ret.items:
            pod_name = pod.metadata.name
        return pod_name

    def _get_pod_logs(self, pod_name: str) -> str:
        """Returns the logs of the given pod."""
        v1 = self.kubernetes_client.get_core_api(self.cloud)
        ret = v1.read_namespaced_pod_log(name=pod_name, namespace=self.namespace)
        return ret

    def _kubernetes_login(self):
        self.kubernetes_client.login(self.cloud)

    def check_job(self, deploy_id: uuid.UUID) -> JobState:
        """Checks the current state of the job.

        Returns the current job state. See JobState enum for details.
        """
        v1 = self.kubernetes_client.get_batch_api(self.cloud)
        ret = v1.list_namespaced_job(
            self.namespace, label_selector=f"deploy-id={str(deploy_id)}"
        )
        for job in ret.items:
            if not job.metadata.labels:
                continue
            logging.info("Job status %s", job.status)
            if job.status.succeeded:
                return JobState.SUCCEEDED
            if job.status.conditions and any(
                c for c in job.status.conditions if c.type == "Failed"
            ):
                logging.info("Job failed: job %s", job)
                return JobState.FAILED
            break
        else:
            logging.info("jobs: %s", ret)
            logging.error("Failed to find job for deploy id %s", deploy_id)
            return JobState.MISSING
        # the job exists, but did we schedule a pod for it?
        v1 = self.kubernetes_client.get_core_api(self.cloud)
        ret = v1.list_namespaced_pod(
            self.namespace, label_selector=f"deploy-id={str(deploy_id)}"
        )
        pods = []
        for pod in ret.items:
            if not pod.metadata.labels:
                continue
            if pod.metadata.labels.get("deploy-id") == str(deploy_id):
                pods.append(pod)
        if not pods:
            return JobState.SCHEDULED
        pods.sort(key=lambda p: p.status.start_time or datetime.datetime.max)
        logging.info("Found pods: %s", [p.metadata.name for p in pods])
        last_pod = pods[-1]
        logging.info("Pod %s status %s", last_pod.metadata.name, last_pod.status)
        if (
            last_pod.status.container_statuses
            and last_pod.status.container_statuses[0].started
        ):
            return JobState.RUNNING
        return JobState.SCHEDULED

    def _wait_for_job_listing(self, deploy_id: uuid.UUID):
        """Wait (for a limited time) for the job to actually show up.

        This is to avoid a race condition between job creation and the job
        showing up in a listing.

        If the job doesn't show up, we only display a warning. If the job is till
        missing in the next round, the job will detected as missing and error
        handling will kick in.
        """
        for _ in range(30):
            state = self.check_job(deploy_id)
            if state != JobState.MISSING:
                break
            time.sleep(1)
        else:
            logging.warning("Job %s didn't appear after creation", deploy_id)

    def schedule(
        self,
        branch: str | None,
        pull_request_number: str | None,
        ref: str,
        clouds: typing.Iterable[str],
        envs: typing.Iterable[str],
        namespaces: typing.Iterable[str],
        target_names: typing.Iterable[str],
        pause: bool,
        allow_rollback: bool,
        deployment_track: deploy_pb2.DeploymentTrack.ValueType,
        deployment_schedule_name: metadata_pb2.Deployment.DeploymentScheduleName.ValueType
        | None,
    ) -> Deployment:
        """Schedules a new deploy job.

        Args:
            branch: the branch to deploy
            ref: the commit reference to deploy. If empty, the latest commit on the branch will be used.
            cloud: the cloud to deploy to
            env: the environment to deploy to
            namespace: the namespace to deploy to
            target_names: the target names to deploy

        Returns:
            The deployment object
        """
        if not self.runner_image:
            raise ValueError("Runner image not set")
        if not branch and not pull_request_number:
            raise ValueError("Branch or PR must be set")
        deploy_id = ulid.ULID().to_uuid()
        yaml_config_file = self._generate_config(
            deploy_id=deploy_id,
            branch=branch,
            ref=ref,
            pull_request_number=pull_request_number,
            clouds=list(clouds),
            envs=list(envs),
            namespaces=list(namespaces),
            target_names=list(target_names),
            pause=pause,
            allow_rollback=allow_rollback,
            deployment_track=deployment_track,
            deployment_schedule_name=deployment_schedule_name,
        )
        self._create_job(yaml_config_file)
        self._wait_for_job_listing(deploy_id)
        job_name = f"deploy-runner-{deploy_id}"
        return Deployment(
            deploy_id=deploy_id, namespace=self.namespace, job_name=job_name
        )

    def cancel(self, deploy_id: uuid.UUID):
        """Cancels a deployment.

        Args:
            deploy_id: the deployment id
        """
        job_name = f"deploy-runner-{deploy_id}"
        self._try_delete_job(job_name)

"""Entrypoint for the deploy job.

The deployment job runs in regular intervals
and performs the deployment of the targets.
"""

import argparse
from contextlib import contextmanager
import fcntl
import json
import grpc
import logging
import os
import pathlib
import sys
import time

import git
from prometheus_client import start_http_server

from base.logging.struct_logging import setup_struct_logging
from base.python.cloud import gcp
from base.python.grpc import client_options
from tools.bazel_runner.git import app_token, checkout, checkout_pb2
from tools.deploy_runner import metadata_pb2
from tools.deploy_runner.server import deploy_pb2, deploy_pb2_grpc
from tools.deploy_runner.deploy_job import config


def _env_info():
    """Print the environment information."""
    logging.info("Environment Info")
    logging.info("User %s:%s", os.getuid(), os.getegid())
    logging.info("Cwd %s", os.getcwd())
    logging.info("Home %s", pathlib.Path().home())
    logging.info("Service Account %s", gcp.get_active_gcp_service_account())


@contextmanager
def _lock(base_directory: pathlib.Path):
    """Lock the cache volume against other pods.

    The cache volume is sometimes incorrectly accessible by two pods at the same time.
    """
    file_path = base_directory / "lock"
    logging.info("Using lock file %s", file_path)
    file_lock = file_path.open("a")

    try:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_EX)
        logging.info("File locked")
        yield
    finally:
        fcntl.flock(file_lock.fileno(), fcntl.LOCK_UN)
        logging.info("File unlocked")
        file_lock.close()


def main():
    """Entrypoint."""
    setup_struct_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)
    current_config = config.Config.load_config(args.config_file)
    logging.info("Config %s", current_config)
    _env_info()

    # begin listening for Prometheus requests
    start_http_server(9090)

    with _lock(pathlib.Path(current_config.base_directory)):
        if current_config.github_app_path:
            token_gen = app_token.GitHubAppTokenSource.from_directory(
                pathlib.Path(current_config.github_app_path)
            )
            run_checkout = checkout.Checkout(
                base_wd=pathlib.Path(current_config.base_directory),
                github_user="app",
                token_source=token_gen,
                home_path=pathlib.Path.home(),
            )
            run_checkout.setup()

            checkout_spec = checkout_pb2.CheckoutSpec()
            checkout_spec.owner = current_config.repo_owner
            checkout_spec.repo_name = current_config.repo_name
            checkout_spec.commit_checkout.branch = current_config.branch

            repo_dir, branch = run_checkout.checkout(
                checkout_spec, checkout_id=current_config.branch
            )
            assert branch
        else:
            logging.info("Running in local mode")
            repo_dir = pathlib.Path(current_config.base_directory)
        repo = git.Repo(str(repo_dir))  # type: ignore
        logging.info("SHA %s", repo.head.commit.hexsha)
        sha = repo.head.commit.hexsha

        json_config = json.dumps(
            {
                "methodConfig": [
                    {
                        "name": [{"service": "Deploy"}],
                        "retryPolicy": {
                            "maxAttempts": 5,
                            "initialBackoff": "2s",
                            "maxBackoff": "30s",
                            "backoffMultiplier": 2,
                            "retryableStatusCodes": ["UNAVAILABLE"],
                        },
                    }
                ]
            }
        )

        if current_config.client_mtls:
            credentials = grpc.ssl_channel_credentials(
                root_certificates=pathlib.Path(
                    current_config.client_ca_path
                ).read_bytes(),
                private_key=pathlib.Path(current_config.client_key_path).read_bytes(),
                certificate_chain=pathlib.Path(
                    current_config.client_cert_path
                ).read_bytes(),
            )
            channel = grpc.secure_channel(
                current_config.deploy_server,
                credentials,
                options=client_options.create([("grpc.service_config", json_config)]),
            )
        else:
            credentials = None
            channel = grpc.insecure_channel(
                current_config.deploy_server,
                options=client_options.create([("grpc.service_config", json_config)]),
            )

        stub = deploy_pb2_grpc.DeployStub(
            channel,
        )

        deploy_pod_name = os.getenv("POD_NAME") or "deploy"
        deployment_track = deploy_pb2.DeploymentTrack.Value(
            current_config.deployment_track
        )
        deployment_schedule_name = metadata_pb2.Deployment.DeploymentScheduleName.Value(
            current_config.deployment_schedule_name
        )
        request = deploy_pb2.ScheduleDeploymentRequest(
            commit_ref=sha,
            branch=current_config.branch,
            clouds=[
                deploy_pb2.DeployCloud.Value(c) for c in current_config.cloud_filter
            ],
            namespaces=current_config.namespace_filter,
            scheduled=deploy_pb2.ScheduledDeployment(parent=deploy_pod_name),
            pause=current_config.pause_after_staging,
            allow_rollback=False,
            deployment_track=deployment_track,
            deployment_schedule_name=deployment_schedule_name,
        )
        logging.info("Scheduling deployment %s", request)
        response = stub.ScheduleDeployment(request)
        deploy_id = response.deploy_id
        logging.info("Started deployment %s from %s", deploy_id, request)

        while True:
            time.sleep(current_config.interval_seconds)
            logging.info("Checking for deployment %s", response.deploy_id)
            check_response: deploy_pb2.GetDeploymentResponse = stub.GetDeployment(
                deploy_pb2.GetDeploymentRequest(
                    deploy_id=response.deploy_id,
                )
            )
            logging.info(
                "Deployment %s status %s",
                deploy_id,
                deploy_pb2.DeploymentState.Name(check_response.deployment.state),
            )
            if (
                check_response.deployment.state
                == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_SUCCEEDED
            ):
                logging.info("Deployment succeeded")
                break
            if (
                check_response.deployment.state
                == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_CANCELLED
            ):
                logging.info("Deployment cancelled")
                sys.exit(1)
            if (
                check_response.deployment.state
                == deploy_pb2.DeploymentState.DEPLOYMENT_STATE_FAILED
            ):
                logging.info("Deployment failed")
                sys.exit(1)

    sys.exit(0)


if __name__ == "__main__":
    main()

# Deploy Job

A cronjob that runs the deployment targets.

# Local Manual Run

- Adjust `test_config.json`
- Run `bazel build -c opt //tools/deploy_runner/...`
- Run `bazel-bin/tools/deploy_runner/deploy_job/deploy_job --config-file ~/src/augment/tools/deploy_runner/deploy_job/test_config.json  --kube-config-file=/home/<USER>/.kube/config`

# Create a new deployment track

1. Add a new entry to `tools/deploy_runner/server/deploy.proto` for the new deployment track.
2. Add a new entry to `tools/deploy_runner/server/metadata.proto` for the new deployment schedule.
3. Add a new entry to `tools/deploy_runner/deploy_job/deploy.jsonnet` for the new deployment job.
4. Add a new entry to `tools/deploy_runner/control/deploy.jsonnet` for the new volume.
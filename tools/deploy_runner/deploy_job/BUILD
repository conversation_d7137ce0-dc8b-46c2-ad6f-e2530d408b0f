load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")

py_library(
    name = "config",
    srcs = ["config.py"],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_binary(
    name = "deploy_job",
    srcs = ["deploy_job.py"],
    deps = [
        ":config",
        "//base/logging:struct_logging",
        "//base/python/cloud",
        "//base/python/cloud:gcp",
        "//base/python/grpc:client_options",
        "//tools/bazel_runner/git:app_token",
        "//tools/bazel_runner/git:checkout",
        "//tools/deploy_runner/server:deploy_py_proto",
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    # we need a git executable in the base image
    base = "//tools/docker:ubuntu2004_ci_base_image",
    binary = ":deploy_job",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//tools/deploy:github_token_lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":monitoring_kubecfg",
    ],
)

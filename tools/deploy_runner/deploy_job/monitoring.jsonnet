local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  // this is a cluster role as kube_job_status_start_time is a cluster level metric
  local deploySpec = {
    displayName: 'Deploy Failed',
    conditionPrometheusQueryLanguage: {
      duration: '180s',
      evaluationInterval: '60s',
      labels: { severity: 'info' },
      query: |||
        clamp_max(
          # Get start time of all deploy jobs
          max(kube_job_status_start_time{job_name=~"deploy-[0-9]+"}) by(exported_namespace, job_name, cluster)
          # Get the latest deploy job
          == on(exported_namespace) group_left() max(kube_job_status_start_time{job_name=~"deploy-[0-9]+"}) by(exported_namespace)
        , 1)
        # Add the failure state of the job
        * on(job_name, cluster) group_left() (sum by(job_name, cluster) (kube_job_status_failed) != 0)
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(cloud, deploySpec, 'deploy-job-alerts', |||
      Deploy job %s unsuccessful on %s.
      Runbook: https://www.notion.so/Triaging-Alerts-e26423637dd441b3bd87a87c2ffb149d?p=b0925106d94a446da5cf4842d54be783&pm=s
    ||| % [monitoringLib.label('job_name'), monitoringLib.label('cluster')]),
  ]

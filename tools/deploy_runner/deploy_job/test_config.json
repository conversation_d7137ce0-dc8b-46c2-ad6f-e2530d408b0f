{"deploy_server": "localhost:50051", "client_mtls": false, "client_cert_path": "", "client_key_path": "", "client_ca_path": "", "base_directory": "/home/<USER>/src/augment", "repo_owner": "augmentcode", "repo_name": "augment", "branch": "bazel-last-known-good", "namespace_filter": ["devtools"], "cloud_filter": ["GCP_US_CENTRAL1_DEV"], "github_app_path": "", "pause_after_staging": false, "interval_seconds": 30}
"""Module containing the config for the deploy job."""

import pathlib
from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class Config:
    """Configuration for the server."""

    deploy_server: str

    client_mtls: bool
    client_cert_path: str
    client_key_path: str
    client_ca_path: str

    # the base directory to use
    base_directory: str

    # the repo to use
    repo_owner: str

    # the repo to use
    repo_name: str

    # the branch to deploy
    branch: str

    # the path to the github app. The directory should contain
    # the app id, installation id, and the private key.
    #
    # if this is set to the empty string, the deploy job will not use github and
    # the current checkout will be used for deployment
    github_app_path: str

    # filters targets by cloud name
    cloud_filter: list[str]

    # filters targets by namespace via regex
    namespace_filter: list[str]

    # if set to true, will pause after staging
    pause_after_staging: bool

    # the number of seconds to wait between each check of the deployment state
    interval_seconds: int

    # the deployment track to use
    deployment_track: str

    # the deployment schedule name to use
    # the schedule name is used to filter targets by deployment schedule name
    deployment_schedule_name: str

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )

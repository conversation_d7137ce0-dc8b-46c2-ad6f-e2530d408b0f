"""Tests for eng_info."""


def test_eng_info():
    from eng_info import EngInfo

    engs = EngInfo.load()
    u = engs.get_by_username("dxy")
    assert u is not None
    assert u.email == "<EMAIL>"
    assert engs.resolve_commit_email("<EMAIL>") is not None


def test_eng_info_github():
    from eng_info import EngInfo

    engs = EngInfo.load()
    assert (
        engs.resolve_commit_email("<EMAIL>")
        is not None
    )

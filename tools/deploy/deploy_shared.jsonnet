local gcpInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
function(cloud)
  local cloudInfo = gcpInfo[cloud];
  local cleanupPolicies = [{
    action: 'DELETE',
    id: 'untagged',
    condition: {
      tagState: 'UNTAGGED',
      olderThan: '%ss' % (7 * 24 * 60 * 60),
    },
  }];
  local supportServiceAccount = {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
    metadata: {
      name: 'support-sa',
      namespace: 'devtools',
      labels: {
        app: 'support',
      },
      annotations: {
        'iam.gke.io/gcp-service-account': 'support-sa@%s.iam.gserviceaccount.com' % cloudInfo.projectId,
      },
    },
  };
  // give support-sa (i.e. cbazel dev pods) access to the weights bucket.
  local bucketIamPolicies = [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'support-sa-data-bucket-policy',
        namespace: 'devtools',
      },
      spec: {
        resourceRef: {
          kind: 'StorageBucket',
          external: 'augment-data',
        },
        bindings: [
          {
            role: 'roles/storage.objectUser',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: 'support-sa',
                  },
                },
              },
            ] + if cloud == 'GCP_US_CENTRAL1_PROD' then [
              // give the support-sa access of gsc-prod access to the weights bucket
              {
                member: 'serviceAccount:<EMAIL>',
              },
            ] else [],
          },
        ],
      },
    },
  ];
  local supportObjects =
    [
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMServiceAccount',
        metadata: {
          name: 'support-sa',
          namespace: 'devtools',
          labels: {
            app: 'support',
          },
        },
        spec: {
          displayName: 'support-iam-sa',
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPolicy',
        metadata: {
          name: 'support-workload-identity',
          namespace: 'devtools',
          labels: {
            app: 'support',
          },
        },
        spec: {
          resourceRef: {
            apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
            kind: 'IAMServiceAccount',
            name: 'support-sa',
          },
          bindings: [
            {
              role: 'roles/iam.workloadIdentityUser',
              members: [
                'serviceAccount:%s.svc.id.goog[devtools/support-sa]' % cloudInfo.projectId,
              ],
            },
          ],
        },
      },
    ];
  local repositories = [
    {
      apiVersion: 'artifactregistry.cnrm.cloud.google.com/v1beta1',
      kind: 'ArtifactRegistryRepository',
      metadata: {
        name: if gcpInfo.isLeadCluster(cloud) then 'build-images' else 'build-images-%s' % cloudInfo.shortName,
        namespace: 'devtools',
        annotations: {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        },
      },
      spec: {
        format: 'DOCKER',
        location: cloudInfo.region,
        // container-image-gc is responsible for cleaning up the images
        cleanupPolicies: [],
      },
    },
    {
      apiVersion: 'artifactregistry.cnrm.cloud.google.com/v1beta1',
      kind: 'ArtifactRegistryRepository',
      metadata: {
        name: if gcpInfo.isLeadCluster(cloud) then 'devtools-repository' else 'devtools-repository-%s' % cloudInfo.shortName,
        namespace: 'devtools',
        annotations: {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        },
      },
      spec: {
        format: 'DOCKER',
        location: cloudInfo.region,
        cleanupPolicies: cleanupPolicies,
      },
    },
  ];
  local devRepositories = [
    {
      apiVersion: 'artifactregistry.cnrm.cloud.google.com/v1beta1',
      kind: 'ArtifactRegistryRepository',
      metadata: {
        name: 'base-images',
        namespace: 'devtools',
        annotations: {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        },
      },
      spec: {
        format: 'DOCKER',
        location: cloudInfo.region,
        cleanupPolicies: cleanupPolicies,
      },
    },
  ];
  if cloud == 'GCP_US_CENTRAL1_PROD' then
    lib.flatten([repositories, supportObjects, supportServiceAccount, bucketIamPolicies])
  else if cloud == 'GCP_US_CENTRAL1_GSC_PROD' then
    lib.flatten([repositories, supportObjects, supportServiceAccount])
  else if cloud == 'GCP_US_CENTRAL1_DEV' then
    lib.flatten([repositories, devRepositories])
  else lib.flatten([repositories, supportServiceAccount])

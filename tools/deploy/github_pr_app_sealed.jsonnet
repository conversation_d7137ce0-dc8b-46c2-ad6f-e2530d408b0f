// this token contains the app_id and installation_id and the application private key of the augment-eng-pr github app
// see https://github.com/organizations/augmentcode/settings/apps/augment-eng-pr
//
// the app should be used for automatically creating pull requests.
//
// You may get 404 if you are not a github organization admin.
function(cloud, namespace, appName)
  assert namespace == 'devtools' || namespace == 'test' || std.startsWith(namespace, 'dev-') : 'namespace must be devtools, test, or dev-, not %s' % namespace;
  {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: '%s-github-app-secret' % appName,
      namespace: namespace,
      creationTimestamp: null,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      template: {
        metadata: {
          namespace: namespace,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
      },
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          app_id: 'AgABCfgZ2UG5U0ed0ozyuK+PF/Ee/qRuFjcBZD2mq8JujCCbA96yJngtchPSQqXoXNBzhyF6JGnRBryVkqE4ezP7EFHQw9nqkdQtIn+f2oP1W3mdXJBeV02K9oCFT2zdJjuC7UQsRcEdLMKOGwfzbaMQfX2GtYNOI6M2dH07FviuBIKspM4Fb54JFCTl/Kh7VQx7itmgL3lKF4sdU5IWOTzJhPzS4J1Wok0mUk7jiOJuOw3NUayDx4Z+hDKyBKBnph01gVjZIujFx6fcn2xMOY4WFDuzS/AMkabfQmsvT+vl9oVRFl0Ny/LL/J7UetuCFxkn9kuqQCuTnMLjJjdPteuinyd5fzfCUvk79bsugJfwyqToD8+qk57m6sMIHPTK/0luvFy8D4bgB5e41F/dCjyQRw20cBE7NNm/ZMzDoC5q6K2qbKVZbHLOrwifnGKHOUyIEc3OH6L5I5kTa92pGnlZdvUSM8VgY50/JlmXOM4dPuSkN+qiFkVBgAk1JgKVmvLUiYBzoIrFUMjv+zXWyerffL6daNVnSJvAjCup3UtiDpPQ/g6PT1uPV5A9xXd+YJ8k97mXFY3vsu38fkcdORutwKCWoqh0ixc0/oz2x+B9gESy3xrdTTkLBu/Gw2Fnu38mDGyQ3x7jXY2riFpXgfmwBgVs0Bo9SiNNkDiFBf9xbl8A9OUQ2wxc1KaqDRC434K/7zlFkgXO',  // pragma: allowlist secret
          installation_id: 'AgCdA3fM6dBfMvQnk4mcoMZFLKD8QyepVe1NZHgLxW0hq5R9cLm6RIG52+DvVGYj2Qo9QCNgq86yh/pXoU7g5WIvT0bEXOkUGEEnXsihMO1HVgBypG/BALweUhSOht+PZmg7cLmU/9wUp6vUhvyJYH3nudOghYyqgfeiOlP2ytxU53SBHekOVyFtpwfSkPnldYRY0X18ZdXevOaPTHrjFktgwK6Q3un32a1CI99g28vZ1J02hXseKg9C/4SJefOAfoXbeRObeNKHxaYmtzAmq9h0SrWESbb4Ugpg60xXbj1z4jfjqpBxx/mICr4jniP+GpPOhSsnWWqn6AS4W3yIIvPbhRnYUKgbcpSxaSNqLCWyXb1D7GDASHawgTlstbn+klfDUZXhsrH6U+eGY8lGsxve3GPSk89ETZZUj7vWNglsDQJLC/DLsGCeUd97WUlTohqFOVFojqV6R1h8SASzwzbm/YnSpWy0Kq4KWMpgHOoK9g8et5STUIMchHeewwmEVo5reuEJcLHUfOqIWt1d6uDXbjjJ+jEuarVCuNA1RoxhQGiULPFa3Irxbl+XZluQYFxNAK/YRqY2uzUOfkv1ZOrtrffXEj9bu9B8atr0bJiJF0pewU8dm08GH1JonVzV05scoVd9UyHKw7vVTvpJ6GGpneSKX/KUGlamSDBPxbRMQqcObNppLAcUZRdEBbik2yDVg6UqT5KEdg==',  // pragma: allowlist secret
          key: '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',  // pragma: allowlist secret
        },
      }[cloud],
    },
  }

load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

# only use if write access to repository is required
# strongly prefer github_readonly_token_lib
kubecfg_library(
    name = "github_token_lib",
    srcs = [
        "github_app_sealed.jsonnet",
    ],
    visibility = ["//tools:__subpackages__"],
)

kubecfg_library(
    name = "github_readonly_token_lib",
    srcs = [
        "github_readonly_app_sealed.jsonnet",
    ],
    visibility = ["//tools:__subpackages__"],
)

kubecfg_library(
    name = "github_pr_token_lib",
    srcs = [
        "github_pr_app_sealed.jsonnet",
    ],
    visibility = ["//tools:__subpackages__"],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
    deps = [
        ":github_token_lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

filegroup(
    name = "auth_kube_config_yaml",
    srcs = [
        "auth_kube_config.yaml",
    ],
    visibility = [
        "//services/integrations/github/webhook_listener:__subpackages__",
        "//services/integrations/slack_bot/webhook:__subpackages__",
        "//tools:__subpackages__",
    ],
)

py_library(
    name = "kube_config_copy",
    srcs = ["kube_config_copy.py"],
    visibility = [
        "//tools:__subpackages__",
    ],
    deps = [
        requirement("pyyaml"),
    ],
)

pytest_test(
    name = "kube_config_copy_test",
    srcs = ["kube_config_copy_test.py"],
    deps = [
        ":kube_config_copy",
        requirement("pyyaml"),
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg_shared",
    ],
)

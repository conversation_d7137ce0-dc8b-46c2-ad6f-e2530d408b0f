// this token contains the app_id and installation_id and the application private key of the augment-gen github app
// see https://github.com/organizations/augmentcode/settings/apps/augment-eng
//
// the app should only be used if write access to github is required. Discuss every new usage with a github org admin.
//
// You may get 404 if you are not a github organization admin.
function(cloud, namespace, appName)
  assert namespace == 'devtools' || namespace == 'test' || std.startsWith(namespace, 'dev-') : 'namespace must be devtools, test, or dev-, not %s' % namespace;
  {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: '%s-github-app-secret' % appName,
      namespace: namespace,
      creationTimestamp: null,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      template: {
        metadata: {
          name: 'github-app-secret',
          namespace: namespace,
          creationTimestamp: null,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
      },
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          app_id: 'AgC5tIvmKEZ430Zdf4vdsgRL8OGNL7YCwjaKLv2QxANmPf5L6H1URhXE2LW+LAICbkaAp1KuRvKjvj+4Q0WKgckRrE+jMbCCREbzcOn377/o5l9iG6upL2fZO9JR5TE3EsomrkjVb3NSN6chfXR2CvuPDwEj7OUe5K9WyQFmHuEf4pYuNNRuRjqcQMF90v541AY5lJJdqrRi4XPO1UuMrqwmHuLKBwnJ0fEDnagIRQ8VWWCs+MTP6BuhXLW0rzIMw0nbp+6Q7STiSINxD7u4oMpCCDhzOk3DhwA5CMsZQmhqCMh81Pwcev43H66U4Beon1SMGGKzsOExz3SEvxnjYybDrPwupJOVe/M5Te3OJfOtW94ho7tgVwh3usc65nSdZmyQaWr5lc9v5yPRJk3Qso+C1sPgGS8nz8S5qj8hEu55rBa5M6dFzC/mJxFK00JXrf0eyJC6IgKfgPPv3EJGztdm/CDIliRlw9Gk74/DVhGK3ZLAAG40bEf7IORHUGmW7yJzAfHXqcVEtYjMvZlTB8EXlmEYXpZkBnNduwm7+rXLC5lSF/xvVP3bH3w70m9XzJVuSs5f/jBL3Rbtd+KjJiH4nqgeZkHQxG9aOrAC5toEAwhHiY3JrQ5uxtqs+2qo9aVXaI/kmepYcgRIOygwRWfzbGXRqAYDQM6eod4zF3gsH+PVLHcKY2ieJzJGlXg2JhE6XeY1OuQ=',  // pragma: allowlist secret
          installation_id: 'AgA7BnJMJhXzlzz3FEedCILg4bQYGeqoELO8z6NOLBufxkiy295+YJRjHvdzHnHjmHO+3YJJINByXum6TL3kPqctCuB2KN4QPd4hxenOgb2kZddG2ifE8/xSOp+0ojRi930yLj0T/WLvZ8tZJ9cfq12DiT00v94Sl9JEDm8UlgBu0Cr8lOn7e0U4+kwWjEcxrFOg/ieeLhas9ny8cVJWGCbr9pz2BHs36IT1JDsPGlz8bvM1ZHPMwc5qN2E3pnBXz8rBzJf4DBo3gTAmjGV0+5jyluUhuChbbm0VsJnw/3LKwKAGLOtErmksOzmiOTFyX3Ci6kasLCzLn5L3DhMbdqE6rM4ENpUWDkwYDFj9OQnxPbQBrFMbAAVubNNG35DVcbHaHGYFAyYEPrHnQc8FCrA3R4D9Dhm1AshMfSv16fW6WWyn4X6AtXn4i85Tdp7tMp2OtCup2MXZWPRnp7qQHmz3+caFd8Iw5/r/VhS9nkdArS9tTHorqv+9uwxH029nVRpTVEA8w2XOLZZJCgscSLmBCmt13FpHiD51J+6geG768rlVws/m3GFolkoAGl0xAjvMkKIKT5sSEAmX02zhz6aw1BCh3SF8d6RukgCxe02pYj9qdhSfGaDPeYKyiaK+wEPjyWFyinvFlqfiGc9R1bmQ9iNR6x6K6Q9eD8/aOD7HzKCXLVSL8M24lAMVB1U4S0FQNpVSvK3c9Q==',  // pragma: allowlist secret
          key: '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',  // pragma: allowlist secret
        },
        GCP_US_CENTRAL1_PROD: {
          app_id: 'AgCmGXQWapPgoTtKYsYmnIE49Zqyc+pOci1of4HCJAugxbuk6TRpL83PJKaX2qftgSv9ef82rIXDSLlU2GZxHR996piQVPj2eIru35lTgyUKLjHMMe99hqhTxxU9mWETfEArS/JVj/+nEhTs4PHJ1VX8JQCBrJ3YG2IF+9olzXcSJgQwEWOfLRxcY7L+fCD766UkfZt2jyg1b6GJRZI+WNEiu45UF3dnDfOc2mpSoOJnG/bGKPPeUlPLu9W48jltwJPqvUVGpo5hNlVfoAjbEWXT7iS9aoBqzDdqpEIhNGwZxjE2Pz5KeRJRKh3XX4stO8l89luccj8D9jqCH1gq7OWxaQLJgL/6T7o0afa8MVhzc5oBs7/dJfI0PLxqEDd8q0cGaOpSNuPdgJHxkAVCzk/+GTI7yt2e/Uh+9ftaAFQnOJMtKhBjf6msR1gt2+rFnwJ+qfJ0x2g7n8asVOgWrlz8UkaSAeYEEYYDlQVj0ZWzt/fAnDInZyqpqDxtrDIHot+y3SXrgORtLGQ4fkQVB46UqyOIJFUam+ucf8OsgSLOv4VjhRGZL7pAaPe43tolPBjaFUx49NHjL1op4uRpd3u2lybLOP1eqjPYYTzZP6A73dT3Zfx69u82TSjfWuCQQQxiZk1mISxnUdBOvrERBJ9d0Vu7wcOMIq0HB8qzzOvnzt4Ady3JYQz9R+vmQQscrCcDLKYctW4=',  // pragma: allowlist secret
          installation_id: 'AgB+UY82M0Ij5p7fZtO3lUxJ9bnKhjT6QybMCHPRcNhIYYkZCCWjY/0VKiWmZQGSSRgXkhT9VaJKxzHFdI0Vo+pN1sWhxS1vdIQq7wMAXMwu54Hr0DMHULqKtDiifuxOEgI4h5/0DJ0l6CBdPzXmkRNOwLNMFtC7eTbXKt7MJljKz9GII18WAlTZYq5ceD36XOBZF+LvdzthCJqoKafk4LJsOXT8StSrW3MAmmdHdYxufGaKMdfNXbxgL5GV/5f2AijfjCeWXKEtmDG1rgI1k5k7zqQQtx/n9lk4nUti195YYJLIKTZK/y7/wSDfQj2Fm2ELO0X0O9Jpv845ljFW6rIWpU8cI28ZaIx3YYERdGx05IRWKOR+lEdf8At+yBQDIevJnL04BbqE9X0VrYS15W2RNQG6xZEG0L48XSJrkvfHFyZ4qobB9uuZ7OGEixEo5NMnouHncNQYGzxKfyXOfiVJoCfRElZEf+QaIg6t1sJP3YIzLgioTEHpJVwzhiqsMWCql1Hb0DXwQbC1iZo6O77zRDgXaqkygMs2EOvlUGc7dGJdqojSXp/hR4qgUHeCWeo5JmM3JOhP4Qet82sgtKy0dqiDzRbd3BhuA/qjRFj0ofWKN/78wPi44Jqeqqr01OhJTOxg5PSyQD4mUBaL169l2L+27d5ljS0NIZlTmhRNizY0+AhTlnRjSQKtrKjzB11X4MGGM6atKw==',  // pragma: allowlist secret
          key: '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',  // pragma: allowlist secret
        },
        GCP_EU_WEST4_PROD: {
          app_id: 'AgBZUS778wSrMmwxzyzct4hzoKlcM2KTjUFkiyaaAZ3iRe/NdVyOshGKa0pkSwXS/DNpBx+j/7hZPEk426lpIls01fcZq4H7XyPh/0dDlzXvSFcAEWBMXCzZYrM7vbMU0mQ/naSTyxdaINycl14ASKrXP5aPk3I7HfTrCGTSNCKt11io7aziPG7AqPyxIh874fVnTzGYmOqz1RT6aG+mjivbDd9rhOrFQiaxDT/lDzaZN4xsGSgTGcgyjg7YocearkVXQ99wDsKLnXsNb+HyC7QiY/FcaMukFt+tTaSY6hAQStFVhqO1y3QGJV4vCqtyNXBBd5hMVgPfoK9znPToKp0DhzHgW4rdk7fy/e32N5UDulGqZ9vO9rl/FKI2IAbMWIupXE20MKP8eVx6YiiGpZv5fSbA3FVQUU+d4EkC6HIo0Krs/r5aW/NR2lxW/tb8Q0/BoSjlwvM8koK6bV1KcRyv8fKoPbm+rOHZKHop+AV8y2OwY4vIN7HY2/tmQhHq0mlst16PVrbq737NipRVr9bmxoX5znXQsuXylyWvOKTQVMRO7KtAuWBL+BUg2SSfUo3QwS+lFZy8x0RIcZyJyKcx4Y4INVMs2I+1eWqmh17RnJYWRm/pTF/i085BG7lT1a/hEkU2TKBgDV0rtRlaAy7mCLpQ8li0Vq6fiKxgpN5BT9FwSCXW6Di2kF8uL9kMy3xme/zQPYM=',  // pragma: allowlist secret
          installation_id: 'AgB1ixjJ8M5vbZZBchG8Q6un7pbQo2zQft6nwz+CizhzwBOCEwBe+JkCFMLOw7sizKSwEH06jLdX/cgEDJj8z6SAR3uX3CXOpQ4KStU9z2MoEAEEZigbdweHOz/0gVhEccTiw0oFvmRBE9mY2Wh87HtSH5s8L/n5khpyF624agxDDWql4GKUduyVa0GR45kA+ef4w9Tcg184eRUP+nICcer1a7I84rVkfy2qliaQ2RoSCKhVbCO2s5qnu0/YAZnIcST70iuB4RF/nPBAPxITo/qhujPEy7atRty3cQRMElwcJfZ2zecZGSjynjBrJsOxEMwfzXu6voahpllbVEUnArkOEGDw3+77pm4vA9bc9m/M3CFbZp0/w+0V4Bfp+G17N0KWeN6Ibf4WoZMhMDE5dqnAze0Ygtqtn7BD7XXCCShTBqQo2K9ygOyJFMsKqRuf1DgMYpVtQYwPLGHL07JTF3tZtYR00D7O2k1vQm9GWiy9Xli2z7fAe7zf0L46MNaLwNXySQfEVfMcgfIlPsx/KAxqV+b0qROTYvBpi0JfVmylqixIDY/ogri+MUOkfT2ENtsFQHWYbIkhUMJqiEVDmn3cVZlkK+zaFdLdnRfbekHGEt8iSMrA26ff8qfgv7uKXeVMy+QG7smEYUDppGMUV6wvSvkn1ZzxArHyV6wzYPAla7PxQvpmM7XLwDtlaxF7jWY0/75AmRmveA==',  // pragma: allowlist secret
          key: '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',  // pragma: allowlist secret
        },
      }[cloud],
    },
  }

"""Tests for kube_config_copy."""

import pathlib
import yaml

import tools.deploy.kube_config_copy as kube_config_copy


def test_copy(tmp_path: pathlib.Path):
    """Tests the copy function."""

    source_directory = tmp_path / "src"
    destination_directory = tmp_path / "dst" / "config"

    source_directory.mkdir(parents=True)
    destination_directory.mkdir(parents=True)

    source_file = source_directory / "config.yaml"

    source_file.write_text("""
apiVersion: v1
clusters:
  - cluster:
      server: https://35.238.222.213
    name: gke_system-services-dev_us-central1_us-central1-dev
users:
  - name: user1
    user:
      exec:
        command: ./cmd
""")

    kube_config_copy.copy(
        source_file,
        destination_directory / "config",
    )

    destination_file = destination_directory / "config"

    assert destination_file.exists()

    destination = yaml.safe_load(destination_file.read_text())
    assert (
        pathlib.Path(destination["users"][0]["user"]["exec"]["command"])
        == source_directory / "cmd"
    )

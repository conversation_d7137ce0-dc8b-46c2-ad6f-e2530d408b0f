import copy as copy_lib
import pathlib
import yaml

from typing import Any


def _make_command_absolute(
    kube_config: dict[str, Any], current_path: pathlib.Path
) -> dict[str, Any]:
    """Makes the kube config absolute."""

    result_config = copy_lib.deepcopy(kube_config)

    users = result_config.get("users", [])
    for user in users:
        exec_config = user.get("user", {}).get("exec", {})
        if not exec_config:
            continue
        command = exec_config.get("command", "")
        if not command:
            continue
        exec_config["command"] = str(current_path.joinpath(command))

    return result_config


def copy(
    source_kube_config_file: pathlib.Path,
    target_kube_config_file: pathlib.Path,
):
    """Copies the kube config file."""
    source_kube_config = _make_command_absolute(
        yaml.safe_load(source_kube_config_file.read_text(encoding="utf-8")),
        source_kube_config_file.parent.resolve(),
    )

    target_kube_config_file.parent.mkdir(parents=True, exist_ok=True)
    target_kube_config_file.write_text(yaml.dump(source_kube_config), encoding="utf-8")

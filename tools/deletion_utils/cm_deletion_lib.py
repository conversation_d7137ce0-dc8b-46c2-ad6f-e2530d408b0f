import logging
import datetime
import time

from services.bigtable_proxy.client.client import BigtableProxy<PERSON>lient, RowCell
from services.lib.request_context.request_context import RequestContext
from services.bigtable_proxy import bigtable_proxy_pb2
from third_party.proto.google.bigtable.v2 import data_pb2 as bigtable_data
from third_party.proto.google.bigtable.v2 import bigtable_pb2 as bigtable_types


RETRY_DELAY_SECONDS = 10


def _row_timestamp(cells: list[RowCell]) -> datetime.datetime:
    """
    Returns the oldest timestamp from all cells in the row.
    """

    timestamp = datetime.datetime.max.replace(tzinfo=datetime.timezone.utc)
    for cell in cells:
        cell_timestamp = datetime.datetime.fromtimestamp(
            cell.timestamp_micros / 1e6, tz=datetime.timezone.utc
        )
        if cell_timestamp < timestamp:
            timestamp = cell_timestamp

    return timestamp


def delete_from_tenant_bigtable(
    client: BigtableProxyClient,
    request_context: RequestContext,
    tenant_id: str,
    table_name: bigtable_proxy_pb2.TableName.ValueType,
    min_time: datetime.datetime | None = None,
    max_time: datetime.datetime | None = None,
    batch_size: int = 1000,
    dry_run: bool = True,
    retries: int = 5,
) -> tuple[int, int]:
    """
    Delete rows from the content manager bigtable with timestamp between min
    and max_time.
    """

    # start the search by scanning all keys
    last_processed_key = b"\x00"
    mutations: list[bigtable_types.MutateRowsRequest.Entry] = []
    n_deleted = 0
    n_skipped = 0

    for retry in range(retries):
        logging.info(
            "(dry_run=%s) Starting scan at key %s...", dry_run, last_processed_key
        )

        try:
            rows = client.read_rows(
                request_context=request_context,
                tenant_id=tenant_id,
                table_name=table_name,
                rows=bigtable_data.RowSet(
                    row_ranges=[
                        bigtable_data.RowRange(
                            start_key_closed=last_processed_key,
                            end_key_closed=b"\xff",
                        ),
                    ]
                ),
                filter=bigtable_data.RowFilter(pass_all_filter=True),
                rows_limit=0,  # limit of 0 is no limit
            )

            mutations = []
            for row_key, row_value in rows:
                row_timestamp = _row_timestamp(row_value)

                if (min_time and row_timestamp < min_time) or (
                    max_time and row_timestamp > max_time
                ):
                    logging.debug(
                        "Skipping row %s with time %s", row_key, row_timestamp
                    )
                    n_skipped += 1
                    continue

                logging.debug("Deleting row %s with time %s", row_key, row_timestamp)
                n_deleted += 1

                delete_mutation = bigtable_data.Mutation(
                    delete_from_row=bigtable_data.Mutation.DeleteFromRow(),
                )
                entry = bigtable_types.MutateRowsRequest.Entry(
                    row_key=row_key,
                    mutations=[delete_mutation],
                )
                mutations.append(entry)

                if len(mutations) >= batch_size:
                    logging.info(
                        "(dry_run=%s) Deleting batch of %d rows, first key %s",
                        dry_run,
                        len(mutations),
                        mutations[0].row_key,
                    )
                    if not dry_run:
                        responses = client.mutate_rows(
                            request_context=request_context,
                            tenant_id=tenant_id,
                            table_name=table_name,
                            entries=mutations,
                        )
                        for response in responses:
                            for entry in response.entries:
                                if entry.status.code != 0:
                                    logging.error(
                                        "Error deleting row: %s", entry.status.message
                                    )

                    last_processed_key = mutations[-1].row_key
                    mutations = []

        except Exception as e:
            logging.warn("failed on retry %d/%d with error: %s", retry + 1, retries, e)
            time.sleep(RETRY_DELAY_SECONDS)
            continue

        # if there's no exception, break
        break

    if mutations:
        logging.info(
            "(dry_run=%s) Deleting batch of %d rows, first key %s",
            dry_run,
            len(mutations),
            mutations[0].row_key,
        )
        if not dry_run:
            responses = client.mutate_rows(
                request_context=request_context,
                tenant_id=tenant_id,
                table_name=table_name,
                entries=mutations,
            )
            for response in responses:
                for entry in response.entries:
                    if entry.status.code != 0:
                        logging.error("Error deleting row: %s", entry.status.message)

    return n_deleted, n_skipped

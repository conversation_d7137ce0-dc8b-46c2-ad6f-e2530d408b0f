"""Script for deleting data from the request insight BigTable."""

import argparse
import sys
import logging
import pathlib
from typing import Iterable

from services.lib.grpc import grpc_args_parser, token_parser
from base.logging.console_logging import setup_console_logging
from services.bigtable_proxy.client.client import (
    BigtableProxyClient,
    GrpcBigtableProxyClient,
)
from services.lib.request_context.request_context import RequestContext
from services.bigtable_proxy import bigtable_proxy_pb2
from third_party.proto.google.bigtable.v2 import data_pb2 as bigtable_data
from third_party.proto.google.bigtable.v2 import bigtable_pb2 as bigtable_types


class RequestInsightBigtableProxyDelete:
    def __init__(
        self,
        proxy_client: BigtableProxyClient,
    ):
        self.main_table_name = bigtable_proxy_pb2.TableName.REQUEST_INSIGHT_MAIN
        self.proxy_client = proxy_client

    def _delete(
        self,
        request_context: RequestContext,
        tenant_id: str,
        entries: list[bigtable_types.MutateRowsRequest.Entry],
    ):
        responses = self.proxy_client.mutate_rows(
            request_context,
            tenant_id,
            self.main_table_name,
            entries,
        )
        count = 0
        for response in responses:
            for entry in response.entries:
                if entry.status.code != 0:
                    logging.error("Error deleting row: %s", entry.status.message)
                else:
                    count += 1
        logging.info("Deleted %d entries", count)

    def _search_row_keys(
        self,
        request_context: RequestContext,
        tenant_id: str,
        prefix: str,
        end_key: str,
        limit: int = 0,
    ):
        rows = self.proxy_client.read_rows(
            request_context,
            tenant_id,
            self.main_table_name,
            bigtable_data.RowSet(
                row_ranges=[
                    bigtable_data.RowRange(
                        start_key_closed=prefix.encode("utf-8"),
                        end_key_closed=end_key.encode("utf-8"),
                    )
                ]
            ),
            bigtable_data.RowFilter(pass_all_filter=True),
            limit,
        )
        return [row[0] for row in rows]

    def delete_request_events(
        self,
        request_context: RequestContext,
        tenant_id: str,
        request_ids: Iterable[str],
        dry_run: bool = True,
    ):
        if dry_run:
            logging.info("Dry run; will skip delete of request events")

        row_keys = []
        for request_id in request_ids:
            # events are an exact key match
            prefix = f"event#{request_id}"
            end_key = f"event#{request_id}"

            logging.info(
                "Searching for events with prefix %s and end_key %s",
                prefix,
                end_key,
            )

            row_keys.extend(
                self._search_row_keys(request_context, tenant_id, prefix, end_key)
            )

        entries: list[bigtable_types.MutateRowsRequest.Entry] = []
        for row_key in row_keys:
            # request ids aren't PII so this is ok
            logging.info(
                "Creating event delete mutation with row_key=%s",
                row_key,
            )
            event_mutation = bigtable_data.Mutation(
                delete_from_row=bigtable_data.Mutation.DeleteFromRow(),
            )
            entries.append(
                bigtable_types.MutateRowsRequest.Entry(
                    row_key=row_key,
                    mutations=[event_mutation],
                )
            )

            if not dry_run:
                self._delete(request_context, tenant_id, entries)

    def delete_session_entries(
        self,
        request_context: RequestContext,
        tenant_id: str,
        session_ids: Iterable[str],
        dry_run: bool = True,
    ):
        if dry_run:
            logging.info("Dry run; will skip delete of session entries")

        # For session we have to read first and then delete
        # There is a dropRowRange method, but it's not a part of the
        # data API so doesn't seem our rust client supports it
        # so we have to do it ourselves
        row_keys = []
        for session_id in session_ids:
            prefix = f"session#{session_id}#"
            end_key = f"session#{session_id}$"

            logging.info(
                "Searching for sessions with prefix %s and end_key %s",
                prefix,
                end_key,
            )

            row_keys.extend(
                self._search_row_keys(request_context, tenant_id, prefix, end_key)
            )

        entries: list[bigtable_types.MutateRowsRequest.Entry] = []
        for row_key in row_keys:
            # Sessions ids aren't PII so this is ok
            logging.info(
                "Creating session delete mutation with row_key=%s",
                row_key,
            )
            event_mutation = bigtable_data.Mutation(
                delete_from_row=bigtable_data.Mutation.DeleteFromRow(),
            )
            entries.append(
                bigtable_types.MutateRowsRequest.Entry(
                    row_key=row_key,
                    mutations=[event_mutation],
                )
            )

            if not dry_run:
                self._delete(request_context, tenant_id, entries)

    def delete_user_entries(
        self,
        request_context: RequestContext,
        tenant_id: str,
        user_ids: Iterable[str],
        dry_run: bool = True,
    ):
        if dry_run:
            logging.info("Dry run; will skip delete of user entries")

        # For user we have to read first and then delete
        # There is a dropRowRange method, but it's not a part of the
        # data API so doesn't seem our rust client supports it
        # so we have to do it ourselves
        row_keys = []
        for user_id in user_ids:
            prefix = f"user#{user_id}#"
            end_key = f"user#{user_id}$"

            # User ids are PII
            logging.info("Searching for rows for a user")
            row_keys.extend(
                self._search_row_keys(request_context, tenant_id, prefix, end_key)
            )

        entries: list[bigtable_types.MutateRowsRequest.Entry] = []
        for row_key in row_keys:
            logging.info(
                "Creating user delete mutation",
            )
            event_mutation = bigtable_data.Mutation(
                delete_from_row=bigtable_data.Mutation.DeleteFromRow(),
            )
            entries.append(
                bigtable_types.MutateRowsRequest.Entry(
                    row_key=row_key,
                    mutations=[event_mutation],
                )
            )

            if not dry_run:
                self._delete(request_context, tenant_id, entries)


def main():
    """Main function."""
    setup_console_logging(add_timestamp=False)
    parser = argparse.ArgumentParser()
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)

    subparsers = parser.add_subparsers()
    delete_user = subparsers.add_parser("delete_user_entries")
    delete_user.set_defaults(action="delete_user_entries")
    delete_user.add_argument("--tenant-id", help="tenant id", default="")
    delete_user.add_argument("--user-ids", type=str, nargs="*", help="user ids")
    delete_user.add_argument(
        "--user-ids-file",
        type=pathlib.Path,
        help="File containing a user id per line to check",
    )
    delete_user.add_argument(
        "--dry-run",
        default=True,
        action=argparse.BooleanOptionalAction,
        help="If true, will run in dry run mode (no delete). If false (--no-dry-run), then will actually delete",
    )

    delete_session = subparsers.add_parser("delete_session_entries")
    delete_session.set_defaults(action="delete_session_entries")
    delete_session.add_argument("--tenant-id", help="tenant id", default="")
    delete_session.add_argument(
        "--session-ids", type=str, nargs="*", help="session ids"
    )
    delete_session.add_argument(
        "--session-ids-file",
        type=pathlib.Path,
        help="File containing a session id per line to check",
    )
    delete_session.add_argument(
        "--dry-run",
        default=True,
        action=argparse.BooleanOptionalAction,
        help="If true, will run in dry run mode (no delete). If false (--no-dry-run), then will actually delete",
    )

    delete_request_events = subparsers.add_parser("delete_request_events")
    delete_request_events.set_defaults(action="delete_request_events")
    delete_request_events.add_argument("--tenant-id", help="tenant id", default="")
    delete_request_events.add_argument(
        "--request-ids", type=str, nargs="*", help="request ids"
    )
    delete_request_events.add_argument(
        "--request-ids-file",
        type=pathlib.Path,
        help="File containing a request id per line to check",
    )
    delete_request_events.add_argument(
        "--dry-run",
        default=True,
        action=argparse.BooleanOptionalAction,
        help="If true, will run in dry run mode (no delete). If false (--no-dry-run), then will actually delete",
    )

    args = parser.parse_args()
    try:
        with grpc_args_parser.create_client(
            args,
            GrpcBigtableProxyClient.create_for_endpoint,
            default_service_name="bigtable-proxy-svc",
            default_endpoint="bigtable-proxy-svc:50051",
        ) as rpc_client:
            ribpd = RequestInsightBigtableProxyDelete(rpc_client)
            token = token_parser.get_token(args)
            request_context = RequestContext.create(auth_token=token)

            if args.action == "delete_user_entries":
                if args.user_ids_file:
                    assert not args.user_ids, "Cannot specify both ids and ids file"
                    user_ids = [
                        user_id.strip()
                        for user_id in args.user_ids_file.read_text().splitlines()
                    ]
                else:
                    user_ids = args.user_ids
                ribpd.delete_user_entries(
                    request_context, args.tenant_id, user_ids, args.dry_run
                )
            elif args.action == "delete_session_entries":
                if args.session_ids_file:
                    assert not args.session_ids, "Cannot specify both ids and ids file"
                    session_ids = [
                        session_id.strip()
                        for session_id in args.session_ids_file.read_text().splitlines()
                    ]
                else:
                    session_ids = args.session_ids
                ribpd.delete_session_entries(
                    request_context, args.tenant_id, session_ids, args.dry_run
                )
            elif args.action == "delete_request_events":
                if args.request_ids_file:
                    assert not args.request_ids, "Cannot specify both ids and ids file"
                    request_ids = [
                        request_id.strip()
                        for request_id in args.request_ids_file.read_text().splitlines()
                    ]
                else:
                    request_ids = args.request_ids
                ribpd.delete_request_events(
                    request_context, args.tenant_id, request_ids, args.dry_run
                )
            else:
                sys.exit(1)
    except KeyboardInterrupt:
        sys.exit(1)
    except argparse.ArgumentError as e:
        logging.error("%s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()

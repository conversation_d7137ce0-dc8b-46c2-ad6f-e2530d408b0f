"""Script to be ran as a k8s job as part of the i1-vanguard0 incident cleanup.

For each user in the list, the script will look up the user's email from the auth database, and
then get a list of the blobs to be deleted from the content manager.
"""

import logging
import argparse
import pathlib
from typing import List, Tuple, Union
import time
import grpc


from google.cloud import storage
from google.cloud import bigquery
from dataclasses import dataclass
from dataclasses_json import dataclass_json

from google.protobuf.timestamp_pb2 import Timestamp

from tools.deletion_utils.gcs_deletion_util import delete_blobs
from base.logging.struct_logging import setup_struct_logging
from tools.deletion_utils.cm_deletion_util import user_blobs
from services.lib.grpc.tls_config import tls_config
from services.token_exchange import token_exchange_pb2
from services.lib.request_context.request_context import RequestContext
from services.token_exchange.client.client import GrpcTokenExchangeClient
from services.content_manager.client.content_manager_client import (
    ContentManagerClient,
    ContentManagerException,
)

# Maximum number of blobs that can be deleted in a single batch request
BATCH_DELETE_LIMIT = 100

USER_ID_QUERY = """SELECT email
FROM `system-services-prod.us_prod_request_insight_analytics_dataset.user`
WHERE id = @uuid;
"""

BACKOFF_TIMES_S = [1, 2, 4, 8, 16, 32, 64, 128]  # Exponential backoff


@dataclass_json
@dataclass
class Config:
    client_mtls: tls_config.ClientConfig
    central_client_mtls: tls_config.ClientConfig
    token_exchange_endpoint: str
    content_manager_endpoint: str
    namespace: str
    project: str
    tenant_id: str
    bucket: str
    dry_run: bool
    auth_uuids: list[str]


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )


def retry_with_backoff(operation_name: str, func):
    """
    Retry a function with exponential backoff.

    Args:
        operation_name: Name of the operation for logging
        func: Function to retry

    Returns:
        The result of the function call

    Raises:
        Exception: If all retries fail
    """
    for retry_idx, backoff_time in enumerate(BACKOFF_TIMES_S):
        try:
            return func()
        except ContentManagerException as e:
            # Check if this is a retriable error
            retriable = False
            grpc_err_code = e.code()
            if grpc_err_code in (
                grpc.StatusCode.RESOURCE_EXHAUSTED,
                grpc.StatusCode.UNAVAILABLE,
                grpc.StatusCode.DEADLINE_EXCEEDED,
                grpc.StatusCode.UNKNOWN,  # Often indicates transient issues
                grpc.StatusCode.CANCELLED,
            ):
                retriable = True
                logging.warning(
                    f"Retriable error on {operation_name}, "
                    f"attempt {retry_idx + 1}/{len(BACKOFF_TIMES_S)}: {str(e)}"
                )

            # If we've exhausted all retries or it's not a retriable error, raise
            if not retriable or retry_idx == len(BACKOFF_TIMES_S) - 1:
                logging.error(f"{operation_name} failed with exception: {str(e)}")
                raise

            # Wait before retrying
            logging.info(f"Retrying in {backoff_time} seconds...")
            time.sleep(backoff_time)
        except Exception as e:
            # Handle other non-ContentManagerException errors
            if retry_idx == len(BACKOFF_TIMES_S) - 1:
                logging.error(
                    f"{operation_name} failed with unexpected exception: {str(e)}"
                )
                raise
            logging.warning(f"Unexpected error on {operation_name}, retrying: {str(e)}")
            time.sleep(backoff_time)

    return None


def batch_delete_cm_blobs(
    rpc_client: ContentManagerClient,
    request_context: RequestContext,
    tenant_id: str,
    user_id: str,
    blobs: List[Tuple[str, Timestamp]],
    dry_run: bool,
) -> None:
    """
    Delete blobs in batches from Content Manager.
    Each batch will contain up to BATCH_DELETE_LIMIT entries.
    """
    for i in range(0, len(blobs), BATCH_DELETE_LIMIT):
        batch = blobs[i : i + BATCH_DELETE_LIMIT]
        batch_num = i // BATCH_DELETE_LIMIT + 1
        logging.info(f"Processing batch {batch_num}, size: {len(batch)}")

        if not dry_run:
            # Create entries list for batch deletion
            blob_user_pairs: List[
                Union[Tuple[str, str], Tuple[str, str, Timestamp]]
            ] = [(blob_name, user_id, timestamp) for blob_name, timestamp in batch]

            # Batch delete with retry
            success = retry_with_backoff(
                f"batch deletion {batch_num}",
                lambda: rpc_client.batch_delete_blobs(
                    blob_user_pairs=blob_user_pairs,
                    tenant_id=tenant_id,
                    request_context=request_context,
                ),
            )

            if not success:
                error_msg = f"Batch deletion failed for batch {batch_num}"
                logging.error(error_msg)
                logging.error(f"Failed batch: {blob_user_pairs}")
                raise Exception(error_msg)

            logging.info(f"Successfully deleted batch of {len(batch)} blobs")
        else:
            blob_names = [blob[0] for blob in batch]
            logging.info(
                f"[DRY RUN] Would delete batch of {len(batch)} blobs: {blob_names}"
            )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    # Initialize rpc client for the content manager
    creds = tls_config.get_client_tls_creds(config.client_mtls)
    central_creds = tls_config.get_client_tls_creds(config.central_client_mtls)

    token_exchange_client = GrpcTokenExchangeClient(
        config.token_exchange_endpoint,
        config.namespace,
        central_creds,
    )

    rpc_client = ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        creds,
    )

    # Initialize GCS client and bucket
    # gcs_client = storage.Client(project=config.project)
    # bucket = gcs_client.bucket(config.bucket)

    # Initialize BQ client
    bigquery_client = bigquery.Client(project=config.project)

    for uuid in config.auth_uuids:
        logging.info(f"Deleting blobs for user {uuid}")

        # Look up the user id from the auth database
        user_id_job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("uuid", "STRING", uuid),
            ]
        )
        user_id_job = bigquery_client.query(
            USER_ID_QUERY, job_config=user_id_job_config
        )
        user_ids = [row["email"] for row in user_id_job.result()]
        assert (
            len(user_ids) == 1
        ), f"Expected 1 user id, got {len(user_ids)} for uuid {uuid}"
        user_id = user_ids[0]

        # Get a token for specific user
        token = token_exchange_client.get_signed_token_for_service(
            config.tenant_id, [token_exchange_pb2.CONTENT_ADMIN]
        )

        request_context = RequestContext.create(
            request_source="i1_inc_blob_deletion", auth_token=token
        )

        # Get the user's blobs from the content manager with retry
        blobs = retry_with_backoff(
            "get user blobs",
            lambda: user_blobs(
                rpc_client,
                request_context,
                config.tenant_id,
                user_id,
            ),
        )

        if not blobs:
            logging.info(f"No blobs found for user {user_id}")
            continue

        logging.info(f"Found {len(blobs)} blobs for user {user_id}")

        # Delete blobs from Content Manager in batches
        batch_delete_cm_blobs(
            rpc_client,
            request_context,
            config.tenant_id,
            user_id,
            blobs,
            config.dry_run,
        )

        # # Also delete from GCS
        # blob_names = [blob[0] for blob in blobs]

        # # Delete the user's blobs from GCS
        # delete_blobs(gcs_client, bucket, config.project, blob_names, config.dry_run)


if __name__ == "__main__":
    setup_struct_logging()
    main()

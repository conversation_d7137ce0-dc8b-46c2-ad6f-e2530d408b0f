import logging
import datetime
import pathlib
import argparse
from zoneinfo import ZoneInfo

from dataclasses import dataclass
from dataclasses_json import dataclass_json

from base.logging.struct_logging import setup_struct_logging
from services.bigtable_proxy.client.client import GrpcBigtableProxyClient
from services.lib.request_context.request_context import Request<PERSON>ontext
from services.token_exchange.client.client import GrpcTokenExchangeClient
from tools.deletion_utils.cm_deletion_lib import delete_from_tenant_bigtable
from services.lib.grpc.tls_config import tls_config
from services.token_exchange import token_exchange_pb2
from services.bigtable_proxy import bigtable_proxy_pb2


CM_TABLE_NAME = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
TIMEZONE = ZoneInfo("America/Los_Angeles")


@dataclass_json
@dataclass
class Config:
    tenant_id: str
    namespace: str
    min_time: datetime.datetime
    max_time: datetime.datetime

    client_mtls: tls_config.ClientConfig
    central_client_mtls: tls_config.ClientConfig
    token_exchange_endpoint: str
    bigtable_proxy_endpoint: str

    dry_run: bool


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )


def main():
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    creds = tls_config.get_client_tls_creds(config.client_mtls)
    central_creds = tls_config.get_client_tls_creds(config.central_client_mtls)

    token_exchange_client = GrpcTokenExchangeClient(
        config.token_exchange_endpoint,
        config.namespace,
        central_creds,
    )
    token = token_exchange_client.get_signed_token_for_service(
        config.tenant_id, [token_exchange_pb2.CONTENT_RW]
    )

    request_context = RequestContext.create(
        request_source="cm_deletion_job",
        auth_token=token,
    )
    bt_proxy_client = GrpcBigtableProxyClient(config.bigtable_proxy_endpoint, creds)

    logging.info(
        "(dry_run=%s) Deleting all rows with time between %s and %s (timezone %s) from the content manager bigtable for tenant id %s in namespace %s",
        config.dry_run,
        config.min_time.astimezone(TIMEZONE).strftime("%Y-%m-%d %H:%M:%S"),
        config.max_time.astimezone(TIMEZONE).strftime("%Y-%m-%d %H:%M:%S"),
        TIMEZONE,
        config.tenant_id,
        config.namespace,
    )

    n_deleted, n_skipped = delete_from_tenant_bigtable(
        client=bt_proxy_client,
        request_context=request_context,
        tenant_id=config.tenant_id,
        table_name=CM_TABLE_NAME,
        min_time=config.min_time,
        max_time=config.max_time,
        dry_run=config.dry_run,
    )

    logging.info(
        "(dry_run=%s) %d rows deleted, %d rows skipped",
        config.dry_run,
        n_deleted,
        n_skipped,
    )


if __name__ == "__main__":
    main()

"""Script to be ran as a k8s job as part of the i1-vanguard0 incident cleanup.

For each user in the list, the script will query the BQ search database for the user's requests, and
then delete the corresponding request events from GCS.
"""

import logging

from google.cloud import storage
from google.cloud import bigquery

from tools.deletion_utils.gcs_deletion_util import (
    delete_request_events,
    delete_session_events,
)
from base.logging.struct_logging import setup_struct_logging


DRY_RUN = False

TENANT_ID = "eac8863de00e749b73eae23300f69e92"  # i1-vanguard0
PROJECT = "system-services-prod"
BUCKET = "us-prod-request-insight-events-nonenterprise"

# NOTE: It's annoying to have this huge list here, but it allows the script to be self-contained
AUTH_UUIDS = [
    "003c6d29-f011-48fe-bfab-13230e258895",
    "00d442a6-98c4-4d1d-b9d3-fc03adc3d01d",
    "02c774e9-fe7e-4c8c-8859-6f76eeced87a",
    "03dc84ec-9605-4304-8ad1-ca4a33479f18",
    "044847b4-c2bd-41a8-86a8-0483e39f4abe",
    "04ccf00e-391c-4ae6-93ee-a13d25e1dfee",
    "06e1695a-9e80-46c9-aa97-08487f15751e",
    "071aeb7a-2018-4e71-bf8a-d2cda6230799",
    "086ef55e-5668-4072-878a-deca71d20d43",
    "08917139-3eaa-4bad-8a1e-8a8c47cfbdfe",
    "09846108-14ca-43d6-91eb-99649bb533bf",
    "0a3ed6cf-713a-4a31-9ed3-05ec96eb7054",
    "0c3a2c9b-6d38-4417-8791-fbd43e0176f1",
    "0ea2bf59-ddd7-4464-9a54-43016d122190",
    "0ef80447-f563-4397-a3d4-06c85cb5a683",
    "0fc36721-0991-4452-9150-b780daa27b12",
    "107179ca-5df1-4d61-ac78-ad122e41159b",
    "108dd53b-e560-4a20-be66-9aeaa7fce2dc",
    "124f64c6-2713-4f78-b225-dffb5803a22a",
    "14392abf-ab55-44d4-adf1-403327896b6d",
    "15b70f91-2267-4d17-9826-d54391744720",
    "16293664-d4bb-4b83-8424-575b4463cc5c",
    "167a835d-199e-431c-b25a-40db8b90966e",
    "16f9b1bb-dadb-4185-9f28-2a6c1ebaab9f",
    "1842a27a-37cc-4351-b9bf-ede3f6e7eb2a",
    "189a7392-3298-4298-ac65-29004c8051af",
    "191fd075-e34a-4a6b-9de8-6e6079f56c00",
    "19691f67-3b37-4fd7-a3ba-785f4663d7aa",
    "1eb5000c-b775-4050-bc4f-f3b8ac508c9a",
    "1eecff5d-f81f-4af0-92ae-3d0d768a4f7f",
    "2058c0a2-acf1-4380-91e9-f3b5a926b851",
    "21883120-7d4a-41fb-87b2-eae58883e670",
    "219ffbfe-2bd8-4f9c-8ac7-03a0a0b1db99",
    "22f8ccdb-c50c-4a9c-a41f-e5f9813380e2",
    "23582f52-e77b-4894-9f38-73cef64ed184",
    "24316b8e-13ef-45a1-830f-996601cf0611",
    "2d627bea-705c-4fec-91c6-a5e328854124",
    "2d9aab74-acb1-4045-a5aa-22205c2a4960",
    "2de3b8a7-034a-4b62-a1bb-ee3ff56ff5f3",
    "2f34f07c-ba98-41e9-ae1a-48a6a43554d0",
    "31ba9d66-c9b0-4c25-bd0d-9a9c81a7f076",
    "32126d95-edf6-49d3-8f4c-b66021e8a44f",
    "332d90bd-4710-4640-83c4-5ab2f3ef4ad2",
    "334b85e8-5009-4172-a0a4-3b30cdbb48c3",
    "365af17c-92fe-497d-a221-2dc6fb6470e3",
    "38b8edaf-9eca-4a18-aec0-192d5667c3a1",
    "3b6524f1-3dae-4d76-b24c-dcb2cdca5c57",
    "3c753a74-18b8-4cb1-9e38-21e5799dd9b9",
    "3c78b457-5ded-4568-b365-2efad051427a",
    "3d452c7f-32f6-4ed3-90d4-1cc2a5f33422",
    "3e55b88d-69a7-4a02-acd1-c852da687455",
    "40b54fb4-2c65-4fb3-bc95-58e7c0404e99",
    "41c4e4d1-1080-4112-aead-4b25601fb0ce",
    "42357a27-ee57-4ddc-9547-e8460b1f5e89",
    "42746878-2be4-401b-be86-63f90811c77b",
    "44ce5413-75f3-4957-a16e-37f7198aa923",
    "451f9782-e35b-4229-8f48-b5606af372c9",
    "458899a8-ef5c-418b-8839-47f0e94e9b13",
    "48bca3bc-d9b4-4f50-91ad-f4c57f9c683a",
    "4936233f-328f-46a6-8800-c5702b377c23",
    "49a93bd8-13ce-40ae-b4b0-2009d0087e75",
    "4ba36ab6-9d21-4b76-8fd1-c4731bd474f9",
    "4cc86f5c-df45-4592-9e08-e0cc758e1551",
    "4cc8775f-d864-4779-b3f1-9611713159de",
    "4d2f936a-c566-4ca7-b75a-42e474ad1330",
    "4e2832d7-c260-4485-9a1f-eb818cf344b6",
    "4e801eab-f219-4b8b-976e-0cd828d7026c",
    "5293f50d-7213-4c25-97b3-77fea2a2fd90",
    "52ae609e-4531-4f88-8b9d-0f2e88e78ebe",
    "52d7de4c-d2eb-4097-9685-28b744c6eaca",
    "59209751-e898-4b1e-8a1e-c57bac9dc248",
    "5c04c265-36bf-47d4-825d-f2cbd2a288e9",
    "5cac61be-dc20-479d-9f30-3193710ebc5c",
    "5d4e4a83-159a-49e4-a309-2d242952f2df",
    "5d876aad-3ab1-42e8-8ae9-7469dd8e4d1d",
    "5dc0a277-a24f-4165-a616-8918feee0d2a",
    "5df4d6b8-54d0-48a5-9254-80d1a39e2378",
    "5f1c2eb9-bc2b-451a-9277-7df90c79e62e",
    "5f3c90c0-b8a4-439e-83c9-2ccca56c39ca",
    "60fe6afc-04a5-48ca-a590-97875ff20fa6",
    "63586302-09fa-4dcf-9230-8a6d479df6f8",
    "6557e3d8-218d-433f-93fb-6e594d0a7ec4",
    "67a885ac-f003-4913-b7ee-2fb162b48842",
    "67d29a0e-795f-47d8-a1f8-5216fd6f23a7",
    "67d92912-257e-49fa-89a3-23c332989689",
    "68491cec-1fcd-4f16-88de-35c0fa959e0c",
    "6a1d7704-4ef9-49ab-b61e-fb0e86e297dd",
    "6a723871-cc72-408f-8890-27a141832e1a",
    "6c3b7c30-6ff0-4251-be3d-de814c9b79ac",
    "6c96573a-a582-485c-b43c-a193dbbb9930",
    "6d1d024f-b6a1-4f04-8499-f3f374db9fdc",
    "6d31cc3e-13c3-4006-bce0-eda2ad256714",
    "6d7db844-7b7b-4487-b53c-c22942df8bd9",
    "706c2ada-d597-46d2-b6db-1f67f6ad0230",
    "70f4fc59-6a00-4c17-8df5-3dd59135647f",
    "710bd015-0291-4c15-bf39-f45137da5925",
    "71a157ca-3d44-41fb-ae0d-0bfbde287c3a",
    "71b6ba69-a961-40b4-8a64-1044387026aa",
    "7421a750-ec39-438e-8004-1b4c867e5da0",
    "752198e5-34b7-455c-95b4-2de3bc653dd1",
    "75dba5b9-2a7c-420e-ac67-df97f4813b3e",
    "796d7d2c-d4e7-4d1d-9f22-c2ccc4bde550",
    "7a3b7bd4-7a5c-4f17-9d9b-b893ff4e688b",
    "7db82f0b-7165-4443-9d0f-553263b1f12a",
    "7e36d6b8-2882-42e3-a76a-00981670c894",
    "7f3ca701-0a8c-4c61-ba5d-3edb9b6385cc",
    "8123f71a-e933-43c4-85e3-00305e015b90",
    "820b1b70-179f-43b8-8742-57504b614723",
    "827be46a-58fd-4dcd-a795-f559ca821bb1",
    "82829889-86d2-4e56-981a-5885ad7f2b6e",
    "83a1a694-52ee-4021-a713-d72f7b0a783b",
    "853c33e3-8981-4d0b-ac3b-bb4eeca33d3a",
    "865d805e-5a78-4445-9d9f-bef0ecfdfbad",
    "86e67b57-821c-4554-86c6-d2fcf35b4384",
    "87a30047-f8d7-4986-a97f-0145eb7f9324",
    "87f5d829-10ac-4914-b371-def52e58dc25",
    "8c3196d9-97a9-4506-a77c-944b3834a648",
    "8f6d88e4-1eb1-48e1-af06-7393e98cb56e",
    "8fe35af8-6af3-4adc-a4b1-daed3d9c0afd",
    "91fa3ada-0975-4e53-b05a-17658f254f24",
    "92545254-ddde-4a8b-b3a2-eedc098e0f38",
    "9339d041-212e-4c13-82b7-ab40397acf7b",
    "95852a82-1879-4933-99de-d9f5ccb4c0a9",
    "978f71c8-00e8-4e2b-b9e2-0df21a816de0",
    "986f22b7-7483-4e02-ad34-eef9a884692e",
    "98ff4cbf-fdd5-40d2-b3f2-cdc287139c81",
    "9b59672e-e53a-4154-86d4-5836040ca32a",
    "9b739434-1033-4fca-8c21-8baefd6b95a0",
    "9b7ebaf4-24a7-49fc-9a17-c01a2749b95c",
    "9c643080-5256-46b9-a730-3cbfd6f089f6",
    "9ee0ea49-0a32-4335-a628-c6541b9f30db",
    "a03f49b3-f398-425c-a606-2147fbf28d8c",
    "a0594b85-64f5-46f8-b5e9-80ed6c943929",
    "a345d0d5-5faa-4417-bdd2-98fcfa5d0fcb",
    "a39ffa3b-9eac-4390-897a-ad75fcf1d43d",
    "a3a96091-833b-4f35-8c28-2f0c36988851",
    "a3be5253-3524-4184-811a-b9d1034283c1",
    "a3f8e436-94d8-4aec-bdc5-0267b14adbe5",
    "a44b8f51-9d7c-4604-a631-924320ca4005",
    "a54be799-3104-4f9c-96af-7c2acf671d8b",
    "a550963a-2d81-4e7c-9adb-0a50a93fca19",
    "a7badbd0-c629-4520-9fb5-426eadfdbca0",
    "abda46b4-5bb8-4695-a15a-fa0d992fd6a0",
    "acb89b76-9b7c-4787-a8e3-2f0639db9573",
    "ae97a0e0-2218-42e9-b38e-03cf4b0d7778",
    "af60b53f-031c-4a4f-bec1-e27fdbc67e49",
    "b0810599-9a21-41ff-a622-636a5cdf8ac5",
    "b304ce69-60f5-44fb-baad-a7838f200c6a",
    "b351c22a-fa41-4617-8e5b-298503a63920",
    "b5ca0c47-1194-4229-a9ba-ce8f8fc783b4",
    "b64952ee-8e66-418e-9029-bf420d85cd91",
    "b9e413ea-1bf9-460e-ae55-633320401b97",
    "bab0163b-d7d7-4089-b06f-9324ef616ede",
    "bb462dff-beb6-400f-b04d-06127d9a298f",
    "bbfe3842-430a-40a2-984b-21ba1dc16a66",
    "bcf56e5e-2320-420c-913f-959a59439913",
    "bed245f3-ca55-4591-b996-da53fb46444e",
    "bedf3c7d-9b6d-47ba-9c90-7fa37c54239a",
    "bf02eefd-27ea-4e67-9938-3c3aa7e516b4",
    "bf2676db-331e-4a62-b0ce-8f1d4ceb0378",
    "bf31529d-b00a-4514-8b22-7f28c9a08160",
    "bf48066e-f3b6-4597-ae4a-ff2b46e526fb",
    "bf7ad86c-1c74-473b-bb31-5f1dcac75473",
    "bf8da353-56fe-46dd-92ff-c495bbdaef9f",
    "bf90563e-e74d-41b5-a121-1b8194746387",
    "c010481e-bd4a-4d12-a442-9589f3d545df",
    "c075e54d-3c11-4217-9c33-b0df611b82fb",
    "c23ab61c-afe6-4992-bcc8-d56a76ed6e2d",
    "c26c4c50-0839-49ee-873f-c56b6284cb4d",
    "c4085a78-fe23-48a3-9add-2c9c7d194715",
    "c491abbd-4c7f-4160-8f01-df92ce4dd9ad",
    "c63fd060-e196-42c6-9a91-7ef247e16dbd",
    "c699bf84-ff8c-48c0-bf1d-0efe83122926",
    "c9f033f8-fd22-41c2-b97e-9eee8237ee51",
    "cac63542-d8fd-4ad9-a6de-d506a3cd64ef",
    "cf62ec78-1477-4ed8-ac40-e62664324b70",
    "cfc3ea22-aad5-4a00-b601-700c7a53d7ba",
    "d08a35d5-0ae8-4cea-9d74-0f1a86ffea24",
    "d375d0a5-d388-4063-a268-a970223b3d22",
    "d3d24739-ca84-4951-beb0-293c48c6bba3",
    "d5a34310-5909-4136-85ea-5e49eb9d10ef",
    "d5f5ab8b-cab5-4921-b510-7c51410c7f90",
    "d6013377-5da3-42cc-a089-29a1c7abd580",
    "d69d75bb-03ee-4cdb-b11b-07c83d464e6e",
    "d6a05df2-de1d-463f-817c-4e4a7969cfbf",
    "d6e527b0-6659-4c4d-a138-92a5b91980e0",
    "d70dc90f-d0f9-493e-a5b3-6a72771d9a37",
    "d86b9675-34e3-4e61-bb58-4de633b9e2c8",
    "dad32062-7d05-48ad-ac34-047b75678c09",
    "dc4e9d26-074d-43bd-b465-b4e7eecdfab4",
    "df2233f6-8b87-4d7d-b3b0-5d4ddded1268",
    "e001cefa-8be2-465a-8d2a-87965ee60bb9",
    "e0d3d67a-1407-4166-8b2b-541145684a1f",
    "e19676f6-16d8-42cd-8364-633fdf7d2fd5",
    "e78360fa-a755-4a7e-9c3f-97781010ca7b",
    "e953b3a6-184f-4b3f-9e44-7d4c1c0f0de9",
    "e99d41b9-ba6f-4681-a52e-12ac40173da4",
    "e9fa45b7-95a9-415e-8f21-0891aaca6cfa",
    "ea57c046-75dd-4fe6-87be-493ac1c3d741",
    "eee9fc1c-4430-4a76-af87-06aab64d80a7",
    "ef87f8c2-013b-45f8-8d14-f0830aa66d26",
    "f0d19f9e-22f8-4716-a608-7e917add2743",
    "f11124e8-7132-4605-a9f2-149eafc8d6b2",
    "f298a6de-7ba5-4cbb-acf5-115588e54eac",
    "f3c8ddc8-8962-48d5-adf8-60b661beae6d",
    "f3d9849e-5ab0-4303-9117-a8aa4cb4e763",
    "f804eb8e-a8f7-4bf1-ada2-0d92da0aa9e9",
    "f8d96434-d211-4cfa-8b4a-601a3c0478a9",
    "f9beb225-69b7-46b5-8cb8-89d0042cba4a",
    "f9f28ca5-7476-4050-a8b6-9ba6f9d0f247",
    "fd326446-b663-4c63-bf86-9f08f1d21c5b",
    "fdc56fb2-a451-4452-9d2c-7db8ec9f5a9e",
    "fe1ed380-9cd8-4d49-bdd9-415f056f3b00",
]

REQUEST_IDS_QUERY = """SELECT DISTINCT request_id
FROM `system-services-prod.us_prod_request_insight_search_nonenterprise_dataset.request_metadata`
WHERE
  user_id IN (
    SELECT email
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.user`
    WHERE id = @uuid
  )
  AND tenant = "i1-vanguard0"
  AND time >= "2025-01-30";
"""
SESSION_IDS_QUERY = """SELECT DISTINCT session_id
FROM `system-services-prod.us_prod_request_insight_search_nonenterprise_dataset.request_metadata`
WHERE
  user_id IN (
    SELECT email
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.user`
    WHERE id = @uuid
  )
  AND tenant = "i1-vanguard0"
  AND time >= "2025-01-30";
"""


def main():
    # Initialize GCS client and bucket
    gcs_client = storage.Client(project=PROJECT)
    bucket = gcs_client.bucket(BUCKET)

    # Initialize BQ client
    bigquery_client = bigquery.Client(project=PROJECT)

    for uuid in AUTH_UUIDS:
        logging.info(f"Deleting events for user {uuid}")

        # Get the user's requests from the BQ search database
        request_job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("uuid", "STRING", uuid),
            ]
        )
        request_job = bigquery_client.query(
            REQUEST_IDS_QUERY, job_config=request_job_config
        )
        request_ids = [row.request_id for row in request_job.result()]
        logging.info(f"Found {len(request_ids)} requests for user {uuid}")

        # Delete the requests from GCS
        delete_request_events(
            tenant_id=TENANT_ID,
            client=gcs_client,
            bucket=bucket,
            project=PROJECT,
            request_ids=request_ids,
            dry_run=DRY_RUN,
        )

        # Get the user's sessions
        session_job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("uuid", "STRING", uuid),
            ]
        )
        session_job = bigquery_client.query(
            SESSION_IDS_QUERY, job_config=session_job_config
        )
        session_ids = [row.session_id for row in session_job.result()]
        logging.info(f"Found {len(session_ids)} sessions for user {uuid}")

        # Delete the sessions
        delete_session_events(
            tenant_id=TENANT_ID,
            client=gcs_client,
            bucket=bucket,
            project=PROJECT,
            session_ids=session_ids,
            dry_run=DRY_RUN,
        )


if __name__ == "__main__":
    setup_struct_logging()
    main()

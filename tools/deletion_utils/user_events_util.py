"""Script for querying a user's request and session ids from the BigQuery analytics database."""

import argparse
import logging
import os

from google.cloud import bigquery

from base.logging.console_logging import setup_console_logging


def get_request_ids(client: bigquery.Client, user_id: str) -> list[str]:
    query = """SELECT DISTINCT(request_id)
FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.request_metadata`
WHERE user_id = @user_id;
"""
    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("user_id", "STRING", user_id),
        ]
    )
    query_job = client.query(query, job_config=job_config)
    rows = query_job.result()
    return [row.request_id for row in rows]


def get_session_ids(client: bigquery.Client, user_id: str) -> list[str]:
    query = """SELECT DISTINCT(session_id)
FROM `system-services-prod.us_cross_env_request_insight_analytics_dataset.request_metadata`
WHERE user_id = @user_id;
"""
    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("user_id", "STRING", user_id),
        ]
    )
    query_job = client.query(query, job_config=job_config)
    rows = query_job.result()
    return [row.session_id for row in rows]


if __name__ == "__main__":
    setup_console_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--user-id",
        type=str,
        required=True,
        help="The user id to query for",
    )
    parser.add_argument(
        "--request-ids-file",
        type=str,
        default="~/request_ids.txt",
        help="The file to write the request ids to",
    )
    parser.add_argument(
        "--session-ids-file",
        type=str,
        default="~/session_ids.txt",
        help="The file to write the session ids to",
    )
    args = parser.parse_args()

    logging.info(f"Querying for user id: {args.user_id}")
    client = bigquery.Client()

    request_ids = get_request_ids(client, args.user_id)
    with open(os.path.expanduser(args.request_ids_file), "w") as f:
        f.write("\n".join(request_ids) + "\n")
    logging.info(f"Wrote {len(request_ids)} request ids to {args.request_ids_file}")

    session_ids = get_session_ids(client, args.user_id)
    with open(os.path.expanduser(args.session_ids_file), "w") as f:
        f.write("\n".join(session_ids) + "\n")
    logging.info(f"Wrote {len(session_ids)} session ids to {args.session_ids_file}")

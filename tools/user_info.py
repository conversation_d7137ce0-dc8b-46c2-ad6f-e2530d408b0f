"""Module to get build user and namespace information in the same way as the bazel stamping."""

import json
import os
import pathlib
from typing import Optional


def get_build_user() -> Optional[str]:
    """Returns the configured build user."""
    path = pathlib.Path.home().joinpath(".augment", "user.json")
    if path.exists():
        user_data = json.loads(path.read_text(encoding="utf-8"))
        user_name = user_data["name"]
        return user_name
    if "BAZEL_BUILD_USER" in os.environ:
        user_name = os.environ.get("BAZEL_BUILD_USER")
        return user_name
    else:
        user_name = os.environ.get("USER", "none")
        return user_name


def get_build_user_namespace() -> Optional[str]:
    """Returns the configured build namespace."""
    path = pathlib.Path.home().joinpath(".augment", "user.json")
    if path.exists():
        user_data = json.loads(path.read_text(encoding="utf-8"))
        user_name = user_data["name"]
        return f"dev-{user_name}"
    return os.environ.get("BUILD_USER_NAMESPACE")

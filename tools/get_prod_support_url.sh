#!/bin/sh -e
# Small script to generate the support url for a request by a production tenant. This is useful
# if you don't already know the tenant associated with a request you're investigating, or just don't
# want to bother looking up the support site for a tenant.

if [ "$#" -ne 1 ]; then
	echo "Usage: $0 <request_id>"
	exit 1
fi

if ! [ -x "$(command -v bq)" ]; then
	echo 'Error: bq is not installed. (This should be in ~/.local/google-cloud-sdk/bin/. Are you not running on a dev vm?)'
	exit 1
fi

echo "Searching last two weeks of US prod for request $1..."
query="
SELECT CONCAT('https://support.', shard_namespace, '.t.us-central1.prod.augmentcode.com/t/', tenant, '/request/', request_id) AS support_url
FROM us_prod_request_insight_analytics_dataset.request_metadata
WHERE request_id = '$1'
AND time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY)
"
bq --project_id="system-services-prod" query --nouse_legacy_sql --format=json $query | jq -r '.[0].support_url'

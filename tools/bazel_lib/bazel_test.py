from pathlib import Path
from tools.bazel_lib.bazel import get_output_files

STDERR = """INFO: Detected GCP cloud with 0 GPU
INFO: Invocation ID: 7bb3f8c4-80fa-404b-a1b1-c1f798c56f20
Computing main repo mapping:
Loading:
Loading: 0 packages loaded
Analyzing: target //services/api_proxy/server:kubecfg_json (1 packages loaded, 0 targets configured)
Analyzing: target //services/api_proxy/server:kubecfg_json (1 packages loaded, 0 targets configured)
[0 / 1] checking cached actions
Analyzing: target //services/api_proxy/server:kubecfg_json (287 packages loaded, 30736 targets configured)
[1 / 1] checking cached actions
INFO: Analyzed target //services/api_proxy/server:kubecfg_json (324 packages loaded, 35595 targets configured).
INFO: Found 1 target...
Target //services/api_proxy/server:kubecfg_json up-to-date:
  bazel-bin/services/api_proxy/server/kubecfg_json.json
INFO: Elapsed time: 2.662s, Critical Path: 0.03s
INFO: 1 process: 1 internal.
INFO: Build completed successfully, 1 total action
"""


def test_get_output_files():
    workspace = Path("/home/<USER>/augment")
    assert get_output_files(workspace, STDERR) == [
        workspace / "bazel-bin/services/api_proxy/server/kubecfg_json.json"
    ]

"""Module to find bazel path."""

import os
import pathlib
import re


def get_bazel_path() -> str:
    """Returns the path to the bazel binary."""
    b = pathlib.Path("../bazelisk/file/bazel").resolve()
    assert b.exists()
    assert b.is_absolute()
    return str(b)


ENV_KEYS_TO_REMOVE = [
    # dont' leak the outer virtualenv into the bazel call in its subprocesses
    "PYTHONPATH",
    "VIRTUAL_ENV",
    # don't leak the runfiles dir into the bazel call in its
    # subprocesses
    "RUNFILES_DIR",
    "BUILD_WORKING_DIRECTORY",
    "RUNFILES_MANIFEST_FILE",
    "RUNFILES_REPO_MAPPING",
    # gsutil is failing when PYTHONSAFEPATH is set
    # see  https://github.com/GoogleCloudPlatform/gsutil/issues/1735
    "PYTHONSAFEPATH",
]


def get_bazel_env() -> dict[str, str]:
    """Returns the environment to use for running bazel."""
    b = get_bazel_path()
    env = os.environ.copy()

    home = pathlib.Path.home()
    env["NO_BAZEL_PRINT"] = "1"

    env["PATH"] = ":".join(
        [
            env.get("PATH", "/usr/bin"),
            os.path.dirname(b),
            "/usr/local/google-cloud-sdk/bin",
            str(home / ".local/google-cloud-sdk/bin"),
        ]
    )
    for k in ENV_KEYS_TO_REMOVE:
        if k in env:
            del env[k]

    # use the bazel version from the .bazelversion file
    bazelversion_path = pathlib.Path(__file__).parent.parent.parent / ".bazelversion"
    env["USE_BAZEL_VERSION"] = bazelversion_path.read_text().strip()

    return env


def get_output_files(workspace: pathlib.Path, stderr: str) -> list[pathlib.Path]:
    """Return the path to the output files."""
    result = []
    for line in stderr.splitlines():
        m = re.match(r"^\s+(bazel-bin.*)$", line)
        if m:
            result.append(workspace / m.groups()[0])
    return result

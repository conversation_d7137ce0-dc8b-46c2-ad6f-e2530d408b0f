load("//tools/bzl:python.bzl", "py_library", "py_test")

py_library(
    name = "bazel_lib",
    srcs = [
        "bazel.py",
    ],
    data = [
        "//:bazelversion",
        "@bazelisk//file",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//tools:__subpackages__",
    ],
)

py_test(
    name = "bazel_test",
    srcs = ["bazel_test.py"],
    deps = [
        ":bazel_lib",
    ],
)

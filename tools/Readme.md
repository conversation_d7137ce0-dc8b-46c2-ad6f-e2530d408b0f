# Tools

This is for developer tools only

# bazel

the file `//tools/bazel` is a magic name used by Bazelisk to run a wrapper before any Bazel call.
This allows to inject specific dynamic behavior.
The command is execute before any bazel call and thus:

- needs to be fast
- needs to run without any specific dependencies (it is not running in a Bazel sandbox)

### cbazel in Kubernetes

You can create a pod in Kubernetes that uses the cbazel container, e.g.
to develop multi-gpu code.

The command `bazel run //tools:cbazel_dev_pod` will create a container `cbazel-dev` in your
development namespace. You can enter the container with `kubectl exec -n <NAMESPACE> cbazel-dev -it bash`. The command `bazel run //tools:k8s-cbazel-dev.delete` to remove the container afterwards.

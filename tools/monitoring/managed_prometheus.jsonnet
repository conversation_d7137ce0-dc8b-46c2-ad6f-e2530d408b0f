local cloudInfoLib = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud)
  local serviceAccount = gcpLib.createServiceAccount(app='gmp-frontend', cloud=cloud, env='PROD', namespace='monitoring', iam=true);
  local frontend = if cloud != 'GCP_US_CENTRAL1_GSC_PROD' then [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: 'gmp-frontend-monitoring-viewer',
        namespace: 'monitoring',
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: cloudInfoLib[cloud].projectId,
        },
        bindings: [
          {
            role: 'roles/monitoring.viewer',
            members: [
              {
                member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress,
              },
            ],
          },
        ],
      },
    },
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: 'gmp-frontend',
        namespace: 'monitoring',
      },
      spec: {
        replicas: 2,
        selector: {
          matchLabels: {
            app: 'gmp-frontend',
          },
        },
        template: {
          metadata: {
            labels: {
              app: 'gmp-frontend',
            },
          },
          spec: {
            serviceAccountName: serviceAccount.name,
            automountServiceAccountToken: true,
            priorityClassName: cloudInfoLib.envToPriorityClass('PROD'),
            affinity: {
              nodeAffinity: {
                requiredDuringSchedulingIgnoredDuringExecution: {
                  nodeSelectorTerms: [
                    {
                      matchExpressions: [
                        {
                          key: 'kubernetes.io/arch',
                          operator: 'In',
                          values: [
                            'arm64',
                            'amd64',
                          ],
                        },
                        {
                          key: 'kubernetes.io/os',
                          operator: 'In',
                          values: [
                            'linux',
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            },
            containers: [
              {
                name: 'frontend',
                image: 'gke.gcr.io/prometheus-engine/frontend:v0.8.1-gke.6',
                args: [
                  '--web.listen-address=:9090',
                  '--query.project-id=%s' % cloudInfoLib[cloud].projectId,
                ],
                ports: [
                  {
                    name: 'web',
                    containerPort: 9090,
                  },
                ],
                readinessProbe: {
                  httpGet: {
                    path: '/-/ready',
                    port: 'web',
                  },
                },
                securityContext: {
                  allowPrivilegeEscalation: false,
                  capabilities: {
                    drop: [
                      'all',
                    ],
                  },
                  privileged: false,
                  runAsGroup: 1000,
                  runAsNonRoot: true,
                  runAsUser: 1000,
                },
                livenessProbe: {
                  httpGet: {
                    path: '/-/healthy',
                    port: 'web',
                  },
                },
              },
            ],
          },
        },
      },
    },
    {
      apiVersion: 'v1',
      kind: 'Service',
      metadata: {
        name: 'gmp-frontend',
        namespace: 'monitoring',
      },
      spec: {
        clusterIP: 'None',
        selector: {
          app: 'gmp-frontend',
        },
        ports: [
          {
            name: 'web',
            port: 9090,
          },
        ],
      },
    },
  ] else [];
  lib.flatten([
    {
      apiVersion: 'monitoring.googleapis.com/v1',
      kind: 'ClusterPodMonitoring',
      metadata: {
        name: 'prom-kubecfg',
      },
      spec: {
        selector: {
          matchLabels: {
            'app.kubernetes.io/managed-by': 'kubecfg',
          },
        },
        endpoints: [
          {
            port: 9090,
            interval: '60s',
          },
        ],
      },
    },
    // Add a second rule to get metrics for pods that are not managed by
    // kubecfg, which is generally rare
    {
      apiVersion: 'monitoring.googleapis.com/v1',
      kind: 'ClusterPodMonitoring',
      metadata: {
        name: 'prom-manual',
      },
      spec: {
        selector: {
          matchLabels: {
            'augmentcode.com/enable-prometheus-scraping': 'true',
          },
        },
        endpoints: [
          {
            port: 9090,
            interval: '60s',
          },
        ],
      },
    },
    {
      apiVersion: 'monitoring.googleapis.com/v1',
      kind: 'OperatorConfig',
      metadata: {
        namespace: 'gmp-public',
        name: 'config',
      },
      features: {
        config: {
          compression: 'gzip',
        },
      },
      collection: {
        kubeletScraping: {
          interval: '300s',
        },
      },

      rules: {
        alerting: {
          alertmanagers: [
            {
              name: 'alertmanager-svc',
              namespace: 'monitoring',
              port: 9093,
            },
          ],
        },
      },
    },
    {
      apiVersion: 'monitoring.googleapis.com/v1',
      kind: 'ClusterPodMonitoring',
      metadata: {
        name: 'kube-state-metrics',
        labels: {
          'app.kubernetes.io/name': 'kube-state-metrics',
          'app.kubernetes.io/part-of': 'google-cloud-managed-prometheus',
        },
      },
      spec: {
        selector: {
          matchLabels: {
            'app.kubernetes.io/name': 'kube-state-metrics',
          },
        },
        endpoints: [
          {
            port: 8080,
            interval: '60s',
            metricRelabeling: [
              {
                action: 'keep',
                // Curated subset of metrics to reduce costs while populating default set of sample dashboards at
                // https://github.com/GoogleCloudPlatform/monitoring-dashboard-samples/tree/master/dashboards/kubernetes
                // Change this regex to fit your needs for which objects you want to monitor
                regex: 'kube_(daemonset|deployment|pod_(container|status_unschedulable|tolerations)|namespace|node|statefulset|persistentvolume|horizontalpodautoscaler|job_(created|status_start_time|status_failed))(_.+)?',
                sourceLabels: ['__name__'],
              },
            ],
          },
        ],
        targetLabels: {
          metadata: [],
        },
      },
    },
    {
      apiVersion: 'monitoring.googleapis.com/v1',
      kind: 'PodMonitoring',
      metadata: {
        namespace: 'gmp-public',
        name: 'kube-state-metrics',
        labels: {
          'app.kubernetes.io/name': 'kube-state-metrics',
          'app.kubernetes.io/part-of': 'google-cloud-managed-prometheus',
        },
      },
      spec: {
        selector: {
          matchLabels: {
            'app.kubernetes.io/name': 'kube-state-metrics',
          },
        },
        endpoints: [
          {
            port: 8081,
            interval: '60s',
          },
        ],
      },
    },
    serviceAccount.objects,
    frontend,
  ])

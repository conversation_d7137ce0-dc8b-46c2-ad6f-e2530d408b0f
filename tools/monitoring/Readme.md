# Monitoring

This directory contains the observability and monitoring systems. Namely:
  - Google Managed Prometheus for metrics
  - Google Cloud Logging for logs
  - <PERSON><PERSON><PERSON> for traces, moving to Google Cloud Trace
  - Grafana for dashboards
  - Google Cloud Monitoring for alerting
  - Pagerduty for routing alerts to people

See [https://www.notion.so/Links-c251670205db4b788cc9a8a3f9a67610] for links to the various systems.

## Prometheus

[Prometheus](https://prometheus.io/) is a system to collect metrics from Kubernetes pods and services.
This includes general metrics (e.g. CPU utilization) and application-specific metrics.

If configured for a pod, system will (in regular intervals) fetch the metrics from an endpoint `/metrics`.

The ClusterPodMonitoring rule defines which pods get configured for metrics collection by GKE. https://cloud.google.com/stackdriver/docs/managed-prometheus/setup-managed#gmp-pod-monitoring

However, it the metrics will usually be configured via Grafana (see below).

## Grafana

[Grafana](https://grafana.com/) is an observability platform that combines different observability data sources into a single UI. It support adhoc metrics, dashboards and alerting.

The Grafana instance is available at [https://grafana.eng.augmentcode.com/].
The instance is configured with the following data sources:

- [Prometheus (metrics)](https://grafana.eng.augmentcode.com/explore?orgId=1&left=%7B%22datasource%22:%22PBFA97CFB590B2093%22,%22queries%22:%5B%7B%22refId%22:%22A%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D)

The metrics are queried with a query language called [PromQL](https://prometheus.io/docs/prometheus/latest/querying/basics/).

## Tracing / Cloud Trace / OpenTelemetry Collector

Our original tracing system is Jaeger. We are evaluating and likely migrating to Google Cloud Trace.

For both Jaeger and Google Cloud Trace, we push traces to the tracing system using the OpenTelemetry gRPC protocol (instead of the properietary Jaeger and Google Cloud Trace protocols). Our services are instrumented with the standard OpenTelemetry SDK for the language in which they are written.

For Google Cloud Trace, we use the OpenTelemetry Collector as recommended by Google in [https://cloud.google.com/stackdriver/docs/instrumentation/choose-approach]

Both Jaeger and Google Cloud Trace can display log lines along with the spans. For Jaeger, these log lines must be
uploaded to Jaeger, but Google Cloud Trace can use annotations on structured log lines to correlate them with spans.
Refer to any documentation in base/logging for more details.

There is a Jaeger instance per cluster. There is also an OpenTelemetry Collector instance per cluster.

## DCGM Exporter

see https://cloud.google.com/stackdriver/docs/managed-prometheus/exporters/nvidia-dcgm

DCGM exposes GPU metrics that Prometheus can scrape. The key GPU metrics you would want to track include:

- DCGM_FI_DEV_GPU_UTIL: This provides the GPU utilization in percentage.
- DCGM_FI_DEV_MEM_COPY_UTIL: This shows the GPU memory utilization.

The metrics do account for the container and pod that use the GPU.

function(cloud, namespace, appName)
  {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: 'pagerduty',
      creationTimestamp: null,
      namespace: namespace,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      template: {
        metadata: {
          name: 'pagerduty',
          creationTimestamp: null,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
          labels: {
            app: appName,
          },
        },
      },
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          services_token: 'AgBVE805XBrMx37u5+fmFzwxvbdmChcIi4Y2EbYkHo2ugDlt50Jpj55/oBr3mAIqJHXiNkmg1aRZHArsgdpKbQBf835KKwu6UchzkTPohctzm1w2L1NOCt4orA1cSDD6OzjfPqSaXnwy9JPJVLNxfWGCHLc0t4ZaIV81eLu/Y0at9yNmy88MQdJqnRriGZfXX+Wrplze50VaLWVmSrfbLvE1++giImjAuQzkVlmzyTB2Btm5Z1lim4EfZXGSCD+kOmswpp0j4Rc3bF7Pvn4muaiOPDLNtd6lC1v376fH2nZtxBMTWMSfqCiCKUI5vJ7gQZRhJ2w0fdvEY1dZ49rEjyr1DunDJ2Rt1O8Dw2kDgkqWYkYQB3hYGM1sVROpUSyvW+BlVpYU20wBD442YRA7lPt1kG5u/8WYUVRLoMsjjEt62oOS8O3w63IJ3+EDASRo967FvrjcdsggaaPVVOeYD9J17pvqxq6SmEkyMS03/00G0rP+NzIEdvGnSsYU4XUNTc14R99T2BuWHk+X+CuRS69p59MdOs5vZJoGo3nxJdghLBopuLWpYiSeuRSkP578ZHsK4GZ+zzsJlpyeKQbReG/2wx2Hb0XM77AF0n91M9QYzTQ4YHsbudZ0+GQl/R5r+xB9+FjEVa814CA8XFsJN6UvC7VRQKXjftCGkqz36eHak0xm3AnyRQKIQLVa/dPlxk/phpMRnKhw3Z0g2bgwZcC8kCvfMSFbukJYaWY6UAV45Q==',  // pragma: allowlist secret
          test_infra_token: 'AgDElXjLhX4n8lsNpwfuG9uqQB717HFZh7/ekt9wc3ceeOyByZZirXREOXbiCaRnFVBalczFNTHuwohP7AT6AZ9gF7lv66DM4IQVMdfbL8dZa1JRnxhsYxMWutIn2Z9a6AQWcwRLChsbwyG2L1MA0uBlNT7nbKCA1I8s8OPtKMPWci70pESuwzyert/rwHO8tkYWpSqFBDFzPLjP5vVFOmVXs0zC1J6ceFyXe3LnBTcVHE0LlYRyAIONsOQBH2n/R30K3bsiPTlwvAbNlo3nY+6lOEMODBhsXOLlar1ILRJVpKlcjOiA8xs0x5Ck5JPvXMzg1cUJ7lMvNxFwdGm+wJwkjWolEr2w5tTDSHBiqFfs0z4PTwNo6o92qKm39E4wuJjL7Isgt4TSg+P395A1jxB2IabSRH/iOFoS+5ilmzcFIUkp7y0Ya+kMpU7eEHMROtkG+tBCcZOeFeH2YhHkdzHutjxDpnfQgAn8BA4YHvBQ3Le3bUSD+0jVG0yH9xWB17hIax1UaZXIb0VqbLhKXhUaeu+twPTe1QotLHSaGzks68we55qH0Wki5knJ7YWX8CkXkTP9v6J8cGmiS6s0YFze+SBdHtGMDrJaB8Q+CAryrtuBwBoUR3VtB5AMYW99HfsLUZPHusCYiUd5nQBg2qdRZIcwu7Q/HF+FCpTHqgU0RrHNa9eJkoVnheXF5YJ056k1DMHCO+mz1LNSmSBeI2NfUnY9vqL8ErY91LEK44vHuQ==',  // pragma: allowlist secret
        },
        GCP_US_CENTRAL1_PROD: {
          services_token: 'AgBpDZxjMoe/FTjlrTlBntEM1ASreMagwm0dMbDfnbs5UH044TsLHQ/M/WqGUIT5zN68j4x6X44vls7D5htQAEgaSH6lY8Tn75MuqmmFa7bsb0TiLnjDE2Lz7izVmqjxM6s2yuuNl64aXChahvm89S8z6GjA0j86zBUg4bx0jHc0ywvUW30qXacocS8EFGQ6bn3dnNS8bl7rJCY8m+7KREv7A8C6j2sBSXE70ssHhkMgOvbz4Aikldc/OJ4WAcG6tUWSutjWAds4ZVNE+83tW65f1CLYkOEbJByiTZDmY0xh7PcJVkay9DEURRu9tn/C7QTFCuFCqudubxAlPTHniVi39NhxOdChnE2tKtyBfYFnjF2pkmhLqtAvUo1JF7DbEhR3d4FJ7r/cS+8tWW5ljTM0HbNeGJKFvd00nOC/bcgdVOMr1HuT6ziJeAem3y/nyqHu/BqKxi4t8y4DrAu6ZTnBQ+V/0jZV97GNQLg7eoEz1MMIkC+PZf1IfTMwhTYvLwCq8fdTPuuDae/JQHPPpLUh3Yd4PDbF3iwKQk6WURbD53gRxZeQbgkEQKvugorGJrjOHlO/XHWfWON3ol/eDeXJDcsi6I9KzwhANFyqe30tk6ws4+4GFc3xSykwBTTzRSxZTRFBt+ewW6RcKSqFrH44mpX2GH7xGCy61bqFzB6VSYztn15mR39P0tQvoAVqzEQWD40zY8CBQ5QNTYVWd8h0nWXT66lEuMhTIk01GIk+eQ==',  // pragma: allowlist secret
          test_infra_token: 'AgBq6NIHtLNkZttNaN0Fu6e49oFOqKxsAPQPOTtgcRq+E/JKF+X6/jPYAwLqAaLtjfUBnE+kKTSor+RPIGHNLsaAQH3I6rxbowEwaMllmQNcm3HVnkrBnAN138WHFbvqwFqumXc+riGFdO4xY6Zx0FEaQhfK5DKYr9fkG8Z+Po8D9Cs+UwXNpz0UDAxPrpg+5RAfaRt0/+7h5VEnerePetQosw2h559y8dBWYITSO1AnLHjjJ449pdLCHjv0na+0VRucr6z3WonaDG2+YyIN2b60/oG8muIBED8xLU89Nk2yj8uOsTTpcUPq3+7qQ8yRP0smFLd3iF85fk4LRmgpMyKfYJQISNmAoqSKoqvljOm5MZlGdrvm3fMizF5WE53PxaL7F8Nw+DpAE1s6q01jvWMHxkScrbJr82JNwaOYan9ToqbctRxmOIR/AV1OrQf9WzqHOX9uvqMEgsTRYlaCmTG3/UbXPDCk3U79NhGzqCk0bqY89YtqbNWgYO2fr1Xdcbb4r4AJYpqZ2byiM0ib9eTgcT0v4EBPhFWFuDRMHPE86ummixCH7+BquzLOXokKRZm84iU3Rn+9zp1StqVsndJu00+XDh3WLnf2G5yEVjp+BGosYfisPQ/9rxeag/3DE6IBLUJXI41KkW1UNyhBIBAq4mXch7lN9dyOi+OzQjlvD2hUSNANovNjzb0ePyQsw1PcWjHbG420pL7RXagPvXdBfdw4R/mGB2pbmQ1Bpw/c+g==',  // pragma: allowlist secret
        },
        GCP_EU_WEST4_PROD: {
          services_token: 'AgCMT10B90JqJu+QZ3LJM+pCw/TXPU+pA6GmIlRU0zx1tVK3fzc8/VjOBur8im51CYwWfoO/EPeJnB4xIqtTYraGSRqcuzDLTLYsVLhuDF7/WbA6e5U5XvnPQOe5sIgEaH1qDNUH1aar5iCrpDrnVgPTdn5MZg8idLTnc07FUv7AwsIfKMMGCJs7KhJVAV6qjNqcPES4bXGgT1962rbLb6tqdiTKyCaXZEaMSTiiMt3EDsExQkxVs/MI4Vr3BtOzQDAtg94rgXLRVBiqmGRKlPZBu0CawmQp276lqqC7pjUIshs6uLdsFE+wS0c+rjaAj7PXcA4WPq8fvlzXbHB5wZUeO9vYinCWX9D3we/D7pW4YHZxFWcdf3qUbSV4DvwVrw/4gYpDqcGgJH3RAhpPIF/C4AyJYcLXoKA1gLzNSlRkfbaaU4q5gQGCSB7ais4HugEta+3doJFk5Gwy0DDtgQjZ4uWrtG3tro4Xb6fpb9BLqXIOCnwNXklHvEKjsrOwyeS7wAcN3lYbcbZ7knylGiAotnod27yq2ybpN5DKONWoxM6jU4pkIhlCr4Yueaer9Cc0+uZTi1uu0ozyyoAiTj93xeIKm2Lb10FnRXuyvdSxNoXeDDBd9oXipUbB9EkwWoS2GvLTucQ+eD+O7X/OhROZ3fGCTtS9phnJLT+MaSy39HwX2NlktKRxuEMR3ecW4uYT1ZZr1QjlCpNudjxVyg/hC6DdDdKBxDZ6slRr4ZRqRw==',  // pragma: allowlist secret
          test_infra_token: 'AgCrognlY61i6tZEOSr126Yk23pPmHAqSkPbFiM4aC3a8hO2LGP3ijfcEdxYe5xqZqStI3MaXF8y1ecG156S1bKyzoKmtHDSVOgfruDv6rdvbRLJobQxZN0sqcSfZmDZN0jYmwL4j6pkp4UqsGPUWonRVFTkmLcvLla1yBkYooc5EXXH5ZbRYlEV1PzoTce8A9atjDZdl7U0x1KokfSOkCmVIIkX9VM/cG0l94d6wInIjNroekKp3UgIPvpovRUR/hS+PflA5xkio6rWLdFjXVjKY2F1vQoV6rWUPTzjNPPnyQmUjYk20UUBbLHUobZppDQ0aNk4vNuQzTNhl2DjTlzdp0Ax2Eyc+7TFzm0mQ2M2FdlQF0N8FR8Tf5v2ZZkMTMC4bw9QFR0nqmA0mD6RH/OnNXuErKA4lOa7xNNUI6IRI1W47nP8ZMOCQfb8pVNB3K8FqZxfe5237ZO8rdnvpoLcMXYYJW71VmELagkNKdAPfXo1LVLIS/V6zjOsx035IjFWpYUem0XUlxgSBF4GhrtSq7oq2BIGBIHRUTllj50YDZ6g4hCba5+0Y6khQQPjUo0uwNTHSYEDT7/jQIyVdmlIq/G0cPGNwlpR+PSqyaMBx8wDASY07yVJuPxY1OeOcgIG2K9LNeNzo4SHAVee/nailOtUpRvKlFWiZuRwMewjtPOIRYvGDTBzicBdYVLo7BFEr4mC5GPjXA4ZSuj5IelcMqBKheLWjjtNUjfd9tVAGA==',  // pragma: allowlist secret
        },
      }[cloud],
    },
  }

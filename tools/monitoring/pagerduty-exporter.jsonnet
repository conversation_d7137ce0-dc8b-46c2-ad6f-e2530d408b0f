local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
function(namespace, env, cloud, namespace_config)
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local appName = 'pagerduty-exporter';

  local container =
    {
      name: appName,
      target: {
        name: '//tools/monitoring:pagerduty-exporter-image',
        dst: 'pagerduty-exporter-image',
      },
      args: [
        '--pagerduty.authtokenfile',
        '/pagerduty-export-api-key/api_key',
        '--server.bind',
        ':9090',
        '--pagerduty.disable-teams',
        'true',
        '--log.json',
      ],
      volumeMounts: [
        {
          name: 'pagerduty-export-api-key',
          mountPath: '/pagerduty-export-api-key',
          readOnly: true,
        },
      ],
      resources: {
        limits: {
          cpu: '0.1',
          memory: '1Gi',
        },
      },
    };
  local pod =
    {
      containers: [
        container,
      ],
      priorityClassName: cloudInfo.envToPriorityClass(env),
      tolerations: tolerations,
      affinity: affinity,
      volumes: [
        {
          name: 'pagerduty-export-api-key',
          secret: {
            secretName: 'pagerduty-export-api-key',  // pragma: allowlist secret
            optional: false,
          },
        },
      ],
    };
  local secret = {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: 'pagerduty-export-api-key',
      namespace: namespace,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      template: {
        metadata: {
          name: 'pagerduty-export-api-key',
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
          labels: {
            app: appName,
          },
        },
      },
      encryptedData: {
        api_key: {
          GCP_US_CENTRAL1_DEV: 'AgBR8xCI/IF3LYZlreiu9F4S716sptDO+pc/EKMHBUiEDzM+yHJNTZ8RUHhLOOuxd71qN3pw0Lcaknfw2McXzh6KvK2mXs35/J2iZw/Jy/9Dtm05OESoZBDM1GzWxmo05J2Nqj3OtB2KQ5xWIe35CirJHV5o7djK9jVz80DvzXQL+AMCwdCTXW9S/ggnnsjs345mPL05ulwfcReYtgPfEbauzKwXLuYWbFWxU8TT1sydZIO/F7Pr66LVeqDKcv+j6U813CByCb021DFpSdIB34saXdzVCDxQVR/tDZxDPNDlerXS1a3Y2TAEibf7EFqQRxjie1miEQs5RGxCfD6xMpJkOGFjF1o7Ln2bD+PXtvbXff/fc3avMyTRQ29uX4gOB+pnwIFDUFqhM15Q6rk23+hrL8FV/5c+pAb/6cdk0mRm+kaJFmxOu2DD5euDpNz266iy5CBEgmmp+5KFCW/aV/6eKLIOyEnS5bhzeoB9R6LtuxDhKtU8FM75ZzpGcbaNUEVS9ULXdBjjLMAunbJ0uzDMAtpQwIvYA7sNs9sm2Yi+1mKX0NX8IxjkuAVt95SypxBO6OcWBFt3KMt8w9YYgR0S23GywCPdqA3WvypPJXt6r6jnQKIOaVyFwyKsPR/dTr7plzO4Pu5yagsjAqkJtm4Bh7JA7lv2J2kkTnSqO71SLd+JQvrHWWN/NW3KUuQD8LPfEoIlZ9rJLDAcrJIqW4b7967L1w==',  // pragma: allowlist secret
          GCP_US_CENTRAL1_PROD: 'AgA20gtoxGNSPKNV7goAa03G1xPqTlnB+3hHwAocrXBcbkhA3zt8a4ylptMHoy2XQUcdwTedLRcG536BoZqGo8GJmujSTw8F+zGdKnYpZKihNs5/w5TKFjvfKglTKcDZwnvYb5QOTnBeStHN1fLqfBV+xZGXkJRpVL+2xAH1fMSW4PnuZveVbikb0HwvtARBFwwWl1C9T8EInrROUZY9joxQENYRJxCBFTljC9bPSCQJkPLQii59SdNnMuaadYF5fe89z7Xu9WcEfPcYIPiDOGm949NUgQlEx+2CiUl7vSCWaPdI2tSuTMxIXlryBm+frzUqz8o+bnvVEqIlEAFs+M3aaa916IgkJEdwND72OYY7OdflAtffGnFEANbd4gP02Id/1YmFt6uSmb1mPBrxWnN4XWqS7ffLj3hXwWngsQCV77rfTO+iJiNTr99MA6u6g9m8A5kG9qn3LnQ1vGBCKbikhuXOjmKho96Nqrut4CjoNSQUnooYA49ICIVubRTI/hovdMKOQhohR2EINhrPbpmdfOXCJt4NhbqmxyxRl20rbwcygzzX/4GPfzdfWQAGdSz/aJZIN30rmlxOfYtK4XkbF9fcCGqyxLt3u/GcyQ+IbEn01PIzVGKbj8kO+l40CG2v6UBG/oIPfizSPpUux++kqQWMpR+nYi37rMXAq0REs1PUfXrnacj2jyj2ZhenGjqIVM5EVIwjtDWsAqZ5gsWwEVTpTw==',  // pragma: allowlist secret
        }[cloud],
      },
    },
  };
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    secret,
    deployment,
  ])

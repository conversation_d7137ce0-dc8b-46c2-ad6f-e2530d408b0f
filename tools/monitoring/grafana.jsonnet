local gcpInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(cloud)
  local cloudInfo = gcpInfo[cloud];
  local grafanaIniConfig = |||
    [auth.jwt]
    # By default, auth.jwt is disabled.
    enabled = true
    # HTTP header to look into to get a JWT token.
    header_name = x-goog-iap-jwt-assertion
    # Specify a claim to use as a username to sign in.
    username_claim = email
    # Specify a claim to use as an email to sign in.
    email_claim = email
    # auto-create users if they are not already matched
    auto_sign_up = true
    jwk_set_url = https://www.gstatic.com/iap/verify/public_key-jwk
    # Cache TTL for data loaded from http endpoint.
    cache_ttl = 60m
    # This can be seen as a required "subset" of a JWT Claims Set.
    expect_claims = {"iss": "https://cloud.google.com/iap"}
    # currently everybody is admin. This can be changed to use team based mappings
    role_attribute_path = Admin
    allow_sign_up = true
    [users]
    auto_assign_org = true
    auto_assign_org_role = Admin
    [server]
    domain = grafana.%s
  ||| % cloudInfo.internalDomainSuffix;
  local namespace = 'monitoring';
  local backendConfig = gcpLib.createBackendConfig(app='grafana',
                                                   cloud=cloud,
                                                   namespace='monitoring',
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 3000,
                                                     type: 'HTTP',
                                                     requestPath: '/robots.txt',
                                                   },
                                                   iap=true);
  local config =
    {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: 'grafana-config',
        namespace: namespace,
        annotations: {
          'reloader.stakater.com/match': 'true',
        },
      },
      data: {
        // Prometheus and GCP Prometheus are aliases
        'grafana.ini': grafanaIniConfig,
        'datasource.yaml': |||
          apiVersion: 1
          datasources:
          - name: Prometheus
            type: prometheus
            access: proxy
            orgId: 1
            url: http://gmp-frontend.monitoring.svc.cluster.local:9090
          - name: GCP Prometheus
            type: prometheus
            access: proxy
            orgId: 1
            url: http://gmp-frontend.monitoring.svc.cluster.local:9090
        |||,
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env='PROD', cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env='PROD', cloud=cloud, appName=null);
  local frontendConfig = gcpLib.createFrontendConfig(app='grafana', cloud=cloud, namespace=namespace);
  lib.flatten([
    config,
    {
      apiVersion: 'v1',
      kind: 'PersistentVolumeClaim',
      metadata: {
        name: 'grafana-pvc',
        namespace: namespace,
      },
      spec: {
        accessModes: [
          'ReadWriteOnce',
        ],
        storageClassName: 'premium-rwo',
        resources: {
          requests: {
            storage: '64Gi',
          },
        },
      },
    },
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        labels: {
          app: 'grafana',
        },
        name: 'grafana',
        namespace: namespace,
      },
      spec: {
        strategy: {
          type: 'Recreate',
        },
        selector: {
          matchLabels: {
            app: 'grafana',
          },
        },
        template: {
          metadata: {
            labels: {
              app: 'grafana',
            },
            annotations: {
              'reloader.stakater.com/search': 'true',
            },
          },
          spec: {
            securityContext: {
              fsGroup: 472,
              supplementalGroups: [
                0,
              ],
            },
            priorityClassName: gcpInfo.envToPriorityClass('PROD'),
            affinity: affinity,
            tolerations: tolerations,
            containers: [
              {
                name: 'grafana',
                image: 'grafana/grafana:11.1.0',
                imagePullPolicy: 'IfNotPresent',
                ports: [
                  {
                    containerPort: 3000,
                    name: 'http-grafana',
                    protocol: 'TCP',
                  },
                ],
                readinessProbe: {
                  failureThreshold: 3,
                  httpGet: {
                    path: '/robots.txt',
                    port: 3000,
                    scheme: 'HTTP',
                  },
                  initialDelaySeconds: 10,
                  periodSeconds: 30,
                  successThreshold: 1,
                  timeoutSeconds: 2,
                },
                livenessProbe: {
                  failureThreshold: 3,
                  initialDelaySeconds: 30,
                  periodSeconds: 10,
                  successThreshold: 1,
                  tcpSocket: {
                    port: 3000,
                  },
                  timeoutSeconds: 1,
                },
                resources: {
                  requests: {
                    cpu: '250m',
                    memory: '750Mi',
                  },
                },
                volumeMounts: [
                  {
                    mountPath: '/var/lib/grafana',
                    name: 'grafana-pv',
                  },
                  {
                    mountPath: '/etc/grafana',
                    name: 'grafana-config',
                  },
                  {
                    mountPath: '/etc/grafana/provisioning/datasources',
                    name: 'grafana-ds-config',
                  },
                ],
              },
            ],
            volumes: [
              {
                name: 'grafana-config',
                configMap: {
                  name: 'grafana-config',
                  items: [
                    {
                      key: 'grafana.ini',
                      path: 'grafana.ini',
                    },
                  ],
                },
              },
              {
                name: 'grafana-ds-config',
                configMap: {
                  name: 'grafana-config',
                  items: [
                    {
                      key: 'datasource.yaml',
                      path: 'datasource.yaml',
                    },
                  ],
                },
              },
              {
                name: 'grafana-pv',
                persistentVolumeClaim: {
                  claimName: 'grafana-pvc',
                },
              },
            ],
          },
        },
      },
    },
    {
      apiVersion: 'v1',
      kind: 'Service',
      metadata: {
        name: 'grafana',
        namespace: namespace,
        annotations: {
          'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        },
      },
      spec: {
        type: 'NodePort',
        ports: [
          {
            port: 80,
            protocol: 'TCP',
            targetPort: 'http-grafana',
          },
        ],
        selector: {
          app: 'grafana',
        },
      },
    },
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': 'letsencrypt-prod',
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        name: 'grafana-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'grafana-ssl-cert',  // pragma: allowlist secret
            hosts: ['grafana.%s' % cloudInfo.internalDomainSuffix],
          },
        ],
        rules: [
          {
            host: 'grafana.%s' % cloudInfo.internalDomainSuffix,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'grafana',
                      port: {
                        number: 80,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ])

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
function(cloud)
  if cloudInfo.isLeadProdCluster(cloud) then
    local logBucket = {
      apiVersion: 'logging.cnrm.cloud.google.com/v1beta1',
      kind: 'LoggingLogBucket',
      metadata: {
        name: 'au-app-audit',
        namespace: 'security',
      },
      spec: {
        location: 'global',
        description: 'Audit logs for augment application specific audit logs',
        enableAnalytics: true,
        retentionDays: 400,
      },
    };
    // the log sink needs to be created by clickops due
    // to bugs in config connector
    // the filter required is 'jsonPayload.@type="type.eng.augmentcode.com/AuditLog"
    [logBucket]
  else []

package gcs

import (
	"context"
	"os"
	"strings"

	"github.com/augmentcode/augment/base/logging"
	"github.com/rs/zerolog/log"

	"cloud.google.com/go/storage"
)

func deleteAllFromFile(bucketName string, file string) {
	logging.SetupServerLogging()

	ctx := context.Background()
	log.Info().Msgf("Deleting all blobs from file: %s", file)
	client, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatal().Msgf("Error creating GCS client: %v", err)
		return
	}
	defer client.Close()

	fileContent, err := os.ReadFile(file)
	if err != nil {
		log.Fatal().Msgf("Error opening file: %v", err)
		return
	}

	lines := strings.Split(string(fileContent), "\n")
	for _, line := range lines {
		if err := deleteBlob(client, ctx, bucketName, line); err != nil {
			log.Error().Msgf("Error deleting blob: %s, %v", line, err)
		}
	}
}

func deleteBlob(client *storage.Client, ctx context.Context, bucketName string, blobName string) error {
	bkt := client.Bucket(bucketName)
	obj := bkt.Object("blobs/" + blobName)
	if err := obj.Delete(ctx); err != nil {
		return err
	}
	log.Info().Msgf("Deleted blob: %s", blobName)
	return nil
}

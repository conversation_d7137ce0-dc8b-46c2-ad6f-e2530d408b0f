"""CLI utility to deploy a cbazel container in a Kubernetes pod."""

import argparse
import logging
import os
import pathlib
import sys

from InquirerPy import prompt  # type: ignore

from base.logging.console_logging import setup_console_logging
from base.python.cloud import cloud
from tools.kubecfg.kubecfg import KubeCfg, KubeCfgException


def select(message: str, choices: list[str]) -> str:
    """Display a list of choices and ask the user to select an option."""
    questions = [
        {
            "type": "list",
            "name": "answer",
            "message": message,
            "choices": choices,
        },
    ]

    answers = prompt(questions)
    a = answers["answer"]
    assert a
    return str(a)


def confirm(message: str) -> bool:
    """Display a yes/no prompt and ask the user to confirm an option."""
    questions = [
        {
            "type": "confirm",
            "message": message,
            "name": "continue",
            "default": True,
        },
    ]

    answers = prompt(questions)
    return bool(answers["continue"])


gpu_list = ["L4", "H100"]


def main():
    """Main entry function."""
    setup_console_logging(add_timestamp=False)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--cloud",
        default=cloud.get_default_cloud(),
        choices=[
            "GCP_US_CENTRAL1_PROD",
            "GCP_EU_WEST4_PROD",
            "GCP_US_CENTRAL1_DEV",
            "GCP_US_CENTRAL1_GSC_PROD",
        ],
    )
    parser.add_argument("--namespace", default=None, help="Namespace to use")
    args = parser.parse_args()

    try:
        current_cloud = args.cloud
        if not current_cloud:
            logging.error("Failed to detect current cloud")
            sys.exit(1)

        gpu = select("Select GPU", ["None"] + [g.upper() for g in gpu_list])
        if gpu != "None":
            gpu_count = int(select("How many GPUs do you want to use?", ["1", "2"]))  # type: ignore
            gpu = gpu.lower()
        else:
            gpu = None
            gpu_count = 0

        kubecfg = KubeCfg(
            ["tools/cbazel_dev_pod.jsonnet"],
            cloud=current_cloud,
            cluster_wide=False,
            env="DEV",
            namespace=args.namespace,
            base_directory=pathlib.Path(
                os.environ.get("BUILD_WORKSPACE_DIRECTORY", ".")
            ),
            push_fn=None,
        )
        with kubecfg.get_config(
            config=None,
            extra_args={
                "resource": gpu,
                "count": gpu_count,
            },
        ) as config:
            r = config.diff()
            if r.returncode == 0:
                logging.info("No changes to apply")
            if r.stdout:
                logging.info("%s", r.stdout)
            if r.stderr:
                logging.error("%s", r.stderr)
                return 1
            res = confirm("Apply changes?")
            if res:
                r = config.apply()
                if r.stdout:
                    logging.info("%s", r.stdout)
                if r.stderr:
                    logging.error("%s", r.stderr)
                    return 1
    except KubeCfgException as e:
        logging.error("%s", e.msg)
        if e.stdout:
            logging.error("%s", e.stdout)
        if e.stderr:
            logging.error("%s", e.stderr)
        sys.exit(2)


if __name__ == "__main__":
    main()

base/logging/node_modules
base/systemenv/node_modules
models/inference/Release
models/inference/Debug
clients/vim/sidecar/node_modules
clients/vscode/node_modules
clients/vscode/webviews/node_modules
clients/common/webviews/node_modules
clients/common/releases/internal-release-notes/node_modules
clients/sidecar/node-process/node_modules
clients/sidecar/libs/node_modules
clients/beachhead/node_modules
clients/.bazel_output
services/auth/common/frontend/node_modules
services/auth/auth0/page_template/node_modules
api/node_modules
node_modules
services/customer/frontend/node_modules
services/lib/ts_utils/node_modules
services/support/frontend/node_modules
services/support/remix/node_modules
services/support_central/frontend/node_modules
services/support/lib/grpc_debug/node_modules
.augment
tools/bazel_runner/web/frontend/node_modules
tools/deploy_runner/web/frontend/node_modules
tools/genie/frontend/node_modules
tools/bzl/lint/pyright/node_modules
tools/bzl/node_sea/node_modules
target
.venv

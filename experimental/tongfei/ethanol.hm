object ethanol:
  init_ckpt = {BaseLM:
    qwen25-1b5="/mnt/efs/augment/checkpoints/qwen25-coder/1b5-fb-mp2"
    qwen25-7b="/mnt/efs/augment/checkpoints/qwen25-coder/7b-fb-mp4"
    starcoder-1b="/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint"
  }
  tokenizer = {BaseLM:
    qwen25-1b5="qwen25coder"
    qwen25-7b="qwen25coder"
    starcoder-1b="starcoder"
  }
  component_name = {BaseLM:
    qwen25-1b5="checkpointing.load_transformer_checkpoint"
    qwen25-7b="checkpointing.load_transformer_checkpoint"
    starcoder-1b="neox.load_starcoder_checkpoint"
  }
  dim = {Dim: 512}
  lr = {LR: 2e-5 5e-5}
  weight_decay = {WeightDecay: 0.1}
  max_query_len = {MaxQueryLen: 1024 2048 4096}
  max_doc_len = {MaxDocLen: 1024}
  train = {BaseLM:
    qwen25-1b5="/mnt/efs/spark-data/shared/ethanol/qwen/indexed_dataset/dataset"
    qwen25-7b="/mnt/efs/spark-data/shared/ethanol/qwen/indexed_dataset/dataset"
    starcoder-1b="/mnt/efs/spark-data/shared/ethanol/med128smartnoheader/indexed_dataset/dataset"
  }
  dev = {BaseLM:
    qwen25-1b5="/mnt/efs/spark-data/shared/ethanol/qwen/indexed_dataset/validation_dataset"
    qwen25-7b="/mnt/efs/spark-data/shared/ethanol/qwen/indexed_dataset/validation_dataset"
    starcoder-1b="/mnt/efs/spark-data/shared/ethanol/med128smartnoheader/indexed_dataset/validation_dataset"
  }
  mpsize = {BaseLM:
    qwen25-1b5=2
    qwen25-7b=4
    starcoder-1b=1
  }
  max_iters={MaxIters: 8000 2000}

  task train(
      augment=$,
      train=$, dev=$,
      init_ckpt=$, dim=$, tokenizer=$, mpsize=$, component_name=$,
      lr=$, weight_decay=$, max_query_len=$, max_doc_len=$, max_iters=$
  ) -> (det_id="det.id"):
    wd=$(pwd)
    jsonnet $augment/experimental/tongfei/ethanol/train_config.jsonnet \
      --tla-str det_ws="Dev" \
      --tla-str det_proj="tongfei" \
      --tla-str wandb_proj="tongfei-ethanol" \
      --tla-str wandb_run="$HYPERMAKE_JOB_NAME[$HYPERMAKE_JOB_CASE]" \
      --tla-str init_ckpt=$init_ckpt \
      --tla-code mpsize=$mpsize \
      --tla-str component_name=$component_name \
      --tla-code dim=$dim \
      --tla-code gold_temp=0.01 \
      --tla-code pred_temp=10.0 \
      --tla-code logit_temp=-5 \
      --tla-code max_iters=$max_iters \
      --tla-code lr=$lr \
      --tla-code weight_decay=$weight_decay \
      --tla-str train=$train \
      --tla-str dev=$dev \
      --tla-str tokenizer=$tokenizer \
      --tla-code max_query_len=$max_query_len \
      --tla-code max_doc_len=$max_doc_len \
    > train_config.json
    (
      flock 200 || exit 1
      cd $augment
      python research/fastbackward/determined/launch.py \
        -c GCP-US1 \
        -s research/fastbackward/train_retriever.py \
        $wd/train_config.json > $wd/det_submission.log
    ) 200> /tmp/det-train.lock
    det_id=$(cat det_submission.log | sed -nE 's/Created experiment ([0-9]*)/\1/p')
    echo "Waiting for Determined job $det_id to complete..."
    echo $det_id > det.id
    det experiment wait $det_id

  monitored_metric = "mean_ap"

  task select_model(augment=$, det_id=$train.det_id, monitored_metric=$) -> out:
    id=$(cat $det_id)
    det trial logs -f $id > det.log
    cat det.log | sed -nE 's/.*(report_validation_metrics\(.*\))/\1/p' > validations.py.log
    cat validations.py.log | sed -nE "s/.*steps_completed=([0-9]+).*'$monitored_metric':\s([0-9.]+).*/\1\t\2/p" > metrics.tsv
    echo "Iterations and their validation scores ($monitored_metric):"
    cat metrics.tsv
    best_iteration=$(cat metrics.tsv | sort -k2 -nr | head -n1 | cut -f1)
    echo "Best iteration is $best_iteration."
    best_iteration_uuid=$(det experiment lc $id | grep " $best_iteration " | cut -d'|' -f5 | xargs)
    echo "UUID of best checkpoint is $best_iteration_uuid. Downloading..."
    (
      cd $augment
      ./research/utils/download_checkpoint.sh $best_iteration_uuid tongfei/hm/$best_iteration_uuid
    )
    echo "Copying from the checkpoint bucket to the local bucket..."
    mkdir -p $out
    cp -r /mnt/efs/augment/checkpoints/tongfei/hm/$best_iteration_uuid/* $out

  task consolidate_mp_model(augment=$, ckpt=$select_model.out) -> out:
    mkdir -p $out
    PYTHONPATH=$augment python $augment/research/fastbackward/scripts/consolidate_mp_retriever.py \
      --input-dir $ckpt --output-dir $out

plan Train = {
  ethanol.train[BaseLM: qwen25-7b, LR: 5e-5]
  ethanol.train[BaseLM: qwen25-1b5, LR: 5e-5]
}

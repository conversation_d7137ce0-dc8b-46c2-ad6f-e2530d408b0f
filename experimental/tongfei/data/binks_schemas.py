from dataclasses import dataclass
from typing import Any, Optional, Iterator

from dataclasses_json import DataClassJsonMixin

from research.core.types import Chunk


@dataclass
class File(DataClassJsonMixin):
    hexsha: str
    size: int
    ext: str | None
    max_stars_repo_path: str
    max_stars_repo_name: str
    max_stars_repo_licenses: list[str]
    max_stars_count: int | None
    max_stars_repo_stars_event_min_datetime: str | None
    max_stars_repo_stars_event_max_datetime: str | None
    max_issues_repo_path: str
    max_issues_repo_name: str
    max_issues_repo_licenses: list[str]
    max_issues_count: int | None
    max_issues_repo_issues_event_min_datetime: str | None
    max_issues_repo_issues_event_max_datetime: str | None
    max_forks_repo_path: str
    max_forks_repo_name: str
    max_forks_repo_licenses: list[str]
    max_forks_count: int | None
    max_forks_repo_forks_event_min_datetime: str | None
    max_forks_repo_forks_event_max_datetime: str | None
    content: str
    avg_line_length: float
    max_line_length: int
    alphanum_fraction: float
    langpart: str


@dataclass
class QuestionAnswerPair(DataClassJsonMixin):
    question: str
    answer: str
    paths: list[str]


@dataclass
class Question(DataClassJsonMixin):
    question: str
    answers: dict[str, str]


@dataclass
class DocumentWithQuestions(DataClassJsonMixin):
    qa_set_name: str
    path: str
    text: str
    questions: list[Question]
    repo_uuid: Optional[str] = None

    def qa_pairs(self) -> Iterator[QuestionAnswerPair]:
        for question in self.questions:
            for answer in question.answers.values():
                yield QuestionAnswerPair(question.question, answer, [self.path])


@dataclass
class DocumentWithQuestionsV2(DataClassJsonMixin):
    question: str
    paths: list[str]
    answer: str

    def qa_pairs(self) -> Iterator[QuestionAnswerPair]:
        yield QuestionAnswerPair(self.question, self.answer, self.paths)


@dataclass
class Repository(DataClassJsonMixin):
    max_stars_repo_name: str
    max_file_lang: dict[str, Any]
    max_size_lang: dict[str, Any]
    total_size: int
    file_list: list[File]
    documents_with_questions: list[DocumentWithQuestions | DocumentWithQuestionsV2]
    repo_uuid: Optional[str] = None


@dataclass
class GeneratedRetrievalTrainingExample(DataClassJsonMixin):
    question: str
    answer: str
    paths: list[str]
    retrieved_chunks: list[Chunk]


@dataclass
class SilverRetrievalTrainingExample(DataClassJsonMixin):
    question: str
    chunks: list[Chunk]
    perplexities: list[float]


@dataclass
class PerplexityScoreTimerInfo(DataClassJsonMixin):
    secondary_chunk_scores: float
    secondary_prefix: float
    secondary_expand_left: float
    secondary_expand_right: float
    scores: float | None = None


@dataclass
class PerplexityScoreSecondaryInfo(DataClassJsonMixin):
    chunks: str
    chunk_scores: list[float]
    gain: list[float]
    gain_expand_left: list[float]
    gain_expand_right: list[float]


@dataclass
class PerplexityScoreInfo(DataClassJsonMixin):
    """Represents the perplexity score of a chunk."""

    timer: PerplexityScoreTimerInfo
    scores: list[float]
    secondary: PerplexityScoreSecondaryInfo
    empty_score: float | None = None

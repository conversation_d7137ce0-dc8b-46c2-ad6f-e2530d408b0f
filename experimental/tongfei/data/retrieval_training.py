from dataclasses import dataclass

from dataclasses_json import DataClassJsonMixin
import numpy as np


from base.tokenizers import Tokenizer


class FastBackwardSerializationContext:
    """
    This is a helper class for serialization and deserialization of retrieval training data in FastBackward.
    """

    def __init__(self, tokenizer: Tokenizer):
        self.tokenizer = tokenizer
        self.eod_id = tokenizer.special_tokens.eos
        self.eok_id = tokenizer.vocab[b"<|ret-endofkey|>"]

    @staticmethod
    def pack(tokens: list[int]) -> bytes:
        return np.array(tokens).astype(np.uint16).newbyteorder("<").tobytes()

    def text(self, text: str) -> list[int]:
        return self.tokenizer.tokenize_safe(text)

    def tuple(self, text: str, ppl: float) -> list[int]:
        eok = [self.eok_id]
        return self.text(text) + eok + self.text(str(ppl)) + eok

    def list_field(self, name: str, values: list[str]) -> list[int]:
        eod = [self.eod_id]
        return (
            eod
            + self.text(name)
            + eod
            + self.text(str(len(values)))
            + eod
            + sum([self.text(value) + eod for value in values], [])
        )


@dataclass
class RetrievalDataInstance(DataClassJsonMixin):
    retrieval_query: str
    known_chunks_query: list[str]
    known_chunks_labels: str
    retrieved_chunks: list[tuple[str, float]]

    def to_bytes(self, ctx: FastBackwardSerializationContext) -> bytes:
        all_tokens = (
            ctx.list_field("retrieval_query", [self.retrieval_query])
            + ctx.list_field("known_chunks_query", self.known_chunks_query)
            + ctx.list_field("known_chunks_labels", [self.known_chunks_labels])
            + ctx.list_field(
                "retrieved_chunks",
            )
        )
        return FastBackwardSerializationContext.pack(all_tokens)

    @classmethod
    def from_bytes(
        cls, ctx: FastBackwardSerializationContext, bs: bytes
    ) -> "RetrievalDataInstance":
        # tokens = np.frombuffer(bs, dtype=np.uint16).newbyteorder("<").tolist()
        pass

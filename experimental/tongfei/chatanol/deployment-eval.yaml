system:
  name: remote_chat
  # The model name is optional, will select default if not present
  model_name: claude-sonnet-3-5-16k-v11-2-chat
  client:
      # Set a url to run the remote system.
      # url: https://dev-<USER>.us-central.api.augmentcode.com
      url: https://dev-tongfei.us-central.api.augmentcode.com
  chat:
    indexing_retry_sleep_secs: 30.0
    indexing_retry_count: 25
    retry_count: 10
    retry_sleep_seconds: 5.0

task:
  name: augment_qa
  dataset_path: /mnt/efs/augment/data/processed/augment_qa/v3_1
  html_report_output_dir: /mnt/efs/augment/public_html/augment_qa/v3_1

podspec: gpu-small.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: augment-qa-eval-remote
  project: tongfei-eval
  workspace: Dev

import dataclasses
import json
from functools import partial
from pathlib import Path
from types import SimpleNamespace
from typing import Any, Dict, Generator, Iterable, List, Mapping, Sequence

import numpy as np
import pandas as pd

from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.core.model_input import ChatInput, ModelInput
from research.data.spark import k8s_session
from research.data.spark.pipelines.pipeline_utils import ObjectDict
from research.data.spark.pipelines.stages.common import export_indexed_dataset
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.factories import (
    create_model,
    create_retriever,
)
from research.retrieval.chunk_formatters import get_chunk_formatter
from research.retrieval.types import Chunk, Document
from research.retrieval.utils import parse_yaml_config


# DISTILLATION CONFIGURATION --------------------------------------------------

# Configure the distilled model

distill_prompt_template = """You are an AI programming assistant, and you only answer questions related to computer science.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.

### Instruction:
{%- for chunk in retrieved_chunks %}
Consider the following excerpt from {{chunk.parent_doc.path}}:
```
{{chunk.text}}
```

{%- endfor %}

{{message}}

### Response:
"""


distill_config = {
    "batchsize": 8,
    "max_chunks": 3,
    "model_config": {
        # "checkpoint_path": "/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-6.7b-instruct/",
        # "name": "deepseek_coder_instruct_hf",
        "name": "fastforward_deepseek_coder_instruct_7b_fp8",
    },
    "prompt_formatter_config": {
        "name": "chat_template",
        "template": distill_prompt_template,
        "tokenizer_name": "deepseekcoderinstructtokenizer",
    },
    "secondary": {
        "max_secondary_chunks": 64,
        "max_prompt_tokens": 8000,
        "expand_prob": 0.25,
        "expand_factor": 3,
    },
}

PATH_COLUMN = "max_stars_repo_path"
REPO_COLUMN = "max_stars_repo_name"
ID_COLUMN = "hexsha"
CONTENT_COLUMN = "content"
PROMPT_COLUMN = "prompt_tokens"
SIZE_COLUMN = "size"
REPO_LANG_COLUMN = "max_size_lang"
REPO_LANG_SUBCOL = "langpart"
FILE_LANG_COLUMN = "langpart"


@dataclasses.dataclass
class DatasetConfig:
    tokenizer_name: str = "starcoder"
    doc_seq_length: int = 1000
    allow_doc_clipping: bool = False
    seq_length: int = 8192
    retrieved_docs: int = 128
    doc_batch_group_size: int = 1
    shuffle_docs: bool = False
    num_validation_samples: int = 512
    known_chunks_budget: int = 8000
    dataset_format: int = 3
    use_ppl_gain: bool = False
    include_known_chunk_labels: bool = False
    known_chunk_format: int = 4


def chunk_to_dict(chunk: Chunk, keep_full_files: bool) -> dict[str, Any]:
    chunk_dict = {
        "id": chunk.id,
        "text": chunk.text,
        "parent_doc": {
            "id": chunk.parent_doc.id,
            "path": chunk.parent_doc.path,
            # WARNING: just storing empty string if we don't want to store file
            "text": chunk.parent_doc.text if keep_full_files else "",
            # Not supporting meta field
        },
        "char_offset": chunk.char_offset,
        "length": chunk.length,
        "line_offset": chunk.line_offset,
        "length_in_lines": chunk.length_in_lines,
        # Not supporting meta field
    }
    return chunk_dict


def serialize_retrieved_chunks(
    retrieved_chunks: Sequence[Chunk], keep_full_files: bool
) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, keep_full_files) for chunk in retrieved_chunks]
    )


def serialize_retrieved_chunks_full_n(retrieved_chunks: Sequence[Chunk], n: int) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, i < n) for i, chunk in enumerate(retrieved_chunks)]
    )


def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:
    def to_chunk(dict_: Dict[str, Any]) -> Chunk:
        return Chunk(
            id=dict_["id"],
            text=dict_["text"],
            parent_doc=Document(
                id=dict_["parent_doc"]["id"],
                text=dict_["parent_doc"]["text"],
                path=dict_["parent_doc"]["path"],
            ),
            char_offset=dict_["char_offset"],
            length=dict_["length"],
            line_offset=dict_["line_offset"],
            length_in_lines=dict_["length_in_lines"],
        )

    dicts = json.loads(retrieved_chunks)
    return [to_chunk(dict_) for dict_ in dicts]


def select_chunk_lines(
    doc: Document, chunk, line_offset: int, length_in_lines: int
) -> Chunk:
    if len(doc.text) < len(chunk.text):
        raise ValueError(
            f"Document {doc.path} has length {len(doc.text)} but chunk length is {len(chunk.text)}"
        )

    lines = doc.text.splitlines(keepends=True)

    if line_offset + length_in_lines > len(lines):
        length_in_lines = len(lines) - line_offset
    if line_offset < 0:
        length_in_lines += line_offset
        line_offset = 0
    if (
        length_in_lines < 0
        or line_offset < 0
        or (line_offset + length_in_lines) > len(lines)
    ):
        raise ValueError(
            f"Invalid line_offset {line_offset} and length_in_lines {length_in_lines}. File length: {len(lines)}."
        )

    prefix_lines = "".join(lines[:line_offset])
    selected_lines = "".join(lines[line_offset : line_offset + length_in_lines])

    return Chunk(
        id=f"{doc.path}_{line_offset}_{length_in_lines}",
        text=selected_lines,
        char_offset=len(prefix_lines),
        length=len(selected_lines),
        line_offset=line_offset,
        length_in_lines=length_in_lines,
        parent_doc=doc,
    )


def expand_chunk(chunk: Chunk, expand_factor: float, to_left: bool):
    if expand_factor < 1:
        raise ValueError(f"Invalid expand_factor {expand_factor}")

    expand_by = round(chunk.length_in_lines * (expand_factor - 1))
    return select_chunk_lines(
        chunk.parent_doc,
        chunk,
        chunk.line_offset - (expand_by if to_left else 0),
        chunk.length_in_lines + expand_by,
    )

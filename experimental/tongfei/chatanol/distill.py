# Imports

import dataclasses
import gc
import json
import math
import random
import sys
import statistics
import time
from functools import partial
from pathlib import Path
from types import SimpleNamespace
from typing import Any, Dict, Generator, Iterable, List, Mapping, Sequence

import numpy as np
import pandas as pd
import pyspark.sql.functions as F
import torch
import torch.nn.functional as torchF
from megatron.data.indexed_dataset import MMapIndexedDataset, make_builder
from megatron.tokenizer import get_tokenizer

from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.core.model_input import ChatInput, ModelInput
from research.data.spark import k8s_session
from research.data.spark.pipelines.pipeline_utils import ObjectDict
from research.data.spark.pipelines.stages.common import export_indexed_dataset
from research.data.spark.pipelines.utils import map_parquet
from research.eval.harness.factories import (
    create_model,
    create_retriever,
    create_retriever_fb,
)
from research.retrieval.chunk_formatters import get_chunk_formatter
from research.retrieval.types import Chunk, Document
from research.retrieval.utils import parse_yaml_config

# GLOBAL CONFIGURATION --------------------------------------------------------

TEMP_BUCKET_URI = "s3a://augment-temporary/igor/"
BUCKET_URI = "s3a://igor-dev-bucket/"
PATHS = dict(
    INPUT_PATH="/mnt/efs/augment/user/igor/data/{}.jsonl",
    STAGE1_URI=BUCKET_URI + "{}/01_inputs{}",
    STAGE2_URI=BUCKET_URI + "{}/02_with_retrieved_chunks{}",
    STAGE3_URI=BUCKET_URI + "{}/03_with_ppl_scores{}",
    STAGE4_URI=BUCKET_URI + "{}/04_shuffled{}",
    STAGE5_URI=BUCKET_URI + "{}/05_tokenized{}",
    STAGE6_URI=BUCKET_URI + "{}/06_exploded{}",
    OUTPUT_PATH="/mnt/efs/augment/user/igor/data/{}{}",
    STAGE3_NO_EMPTY_URI=BUCKET_URI + "{}/03_with_ppl_scores_no_empty{}",
    STAGE3_FEW_SEC_URI=BUCKET_URI + "{}/03_with_ppl_scores_few_sec{}",
)


# CHUNK RETRIEVAL CONFIGURATION -----------------------------------------------

retrieval_config_version = 2

retrieval_config_v1 = SimpleNamespace(
    **{
        "retriever": {
            "scorer": {
                "name": "ethanol",
                "checkpoint_path": "ethanol/ethanol3-01.11_b8192_w8_tg0.01",
            },
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {
                "name": "ethanol3_query",
                "max_lines": 20,
                "add_path": True,
                "retokenize": True,
            },
            "document_formatter": {
                "name": "simple_document",
                "add_path": True,
            },
        },
        "num_expandable_chunks": 64,  # these chunks will be available for expansion
        "num_retrieved_chunks": 127,
        "num_retrieved_extra": 128,
        "prioritize_gold_chunks": True,
        "random_seed": 74912,
    }
)

retrieval_config_v2 = SimpleNamespace(
    **{
        "retriever": {
            "scorer_v2": {
                "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3.fix",
                "model_key": "model",
                "tokenizer_name": "starcoder",
                "query_formatter_name": "chatanol-query",
                "document_formatter": "ethanol6-embedding-with-path-key",
            },
            "chunker": {
                "name": "line_level",
                "max_lines_per_chunk": 30,
            },
            "query_formatter": {
                "name": "ethanol6_query",
                "max_tokens": 1023,
            },
            "document_formatter": {
                "name": "ethanol6_document",
                "max_tokens": 999,
                "add_path": True,
            },
            "tokenizer_name": "starcoder",
        },
        "num_expandable_chunks": 64,
        "num_retrieved_chunks": 127,
        "num_retrieved_extra": 128,
        "prioritize_gold_chunks": False,
        "random_seed": 74912,
    }
)

# DISTILLATION CONFIGURATION --------------------------------------------------

# Configure the distilled model


distill_prompt_template = """You are an AI programming assistant, and you only answer questions related to computer science.
For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.

### Instruction:
{%- for chunk in retrieved_chunks %}
Consider the following excerpt from {{chunk.parent_doc.path}}:
```
{{chunk.text}}
```

{%- endfor %}

{{message}}

### Response:
"""


distill_config = {
    "batchsize": 8,
    "max_chunks": 3,
    "model_config": {
        # "checkpoint_path": "/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-6.7b-instruct/",
        # "name": "deepseek_coder_instruct_hf",
        "name": "fastforward_deepseek_coder_instruct_7b_fp8",
    },
    "prompt_formatter_config": {
        "name": "chat_template",
        "template": distill_prompt_template,
        "tokenizer_name": "deepseekcoderinstructtokenizer",
    },
    "secondary": {
        "max_secondary_chunks": 64,
        "max_prompt_tokens": 8000,
        "expand_prob": 0.25,
        "expand_factor": 3,
    },
}

PATH_COLUMN = "max_stars_repo_path"
REPO_COLUMN = "max_stars_repo_name"
ID_COLUMN = "hexsha"
CONTENT_COLUMN = "content"
PROMPT_COLUMN = "prompt_tokens"
SIZE_COLUMN = "size"
REPO_LANG_COLUMN = "max_size_lang"
REPO_LANG_SUBCOL = "langpart"
FILE_LANG_COLUMN = "langpart"

# TOKENIZATION CONFIGURATION --------------------------------------------------

# known_chunk_format values:
# 1: prefix
# 2: independent
# 3: independent + shuffled
# 4: independent + normalized + more chunks are expanded

# dataset_format values:
# 1: NeoX
# 2: FastBackward
# 3: FastBackward generic format

dataset_config_neox = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=1024,
    retrieved_docs=127,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=65536,
    known_chunks_budget=0,
    dataset_format=1,
    use_ppl_gain=False,
    include_known_chunk_labels=False,
)

dataset_config_neox_pack = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=8,
    shuffle_docs=True,
    num_validation_samples=69632,
    known_chunks_budget=8000,
    dataset_format=1,
    use_ppl_gain=False,
    include_known_chunk_labels=False,
)

dataset_config_fb = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    use_ppl_gain=False,
    include_known_chunk_labels=False,
)

dataset_config_fb_secondary = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    use_ppl_gain=False,
    include_known_chunk_labels=True,
    add_bare_query=False,
    known_chunk_format=1,
)

dataset_config_fb_expand = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=1,  # could be 0, but we make it 1 to avoid a special case in `train_retriever.py`
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    use_ppl_gain=False,
    include_known_chunk_labels=True,
    known_chunk_format=1,
)

# Expand, but predict individual chunk ppl instead of prefix ppl
dataset_config_fb_expand2 = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=1,  # could be 0, but we make it 1 to avoid a special case in `train_retriever.py`
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    use_ppl_gain=False,
    include_known_chunk_labels=True,
    known_chunk_format=2,
)

dataset_config_fb_expand_hybrid = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    use_ppl_gain=False,
    include_known_chunk_labels=True,
    known_chunk_format=1,
)

dataset_config_fb_expand_hybrid3 = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    use_ppl_gain=False,
    include_known_chunk_labels=True,
    known_chunk_format=3,
)

dataset_config_gain_hybrid2 = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    seq_length=8192,
    allow_doc_clipping=False,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    dataset_format=2,
    use_ppl_gain=True,
    known_chunks_budget=8000,
    include_known_chunk_labels=True,
    known_chunk_format=2,
)

# like expand2, but use "sortable" known chunk labels - i.e., labels that
# are fully normalized according to the comparison order that will be used
# at inference time to sort the chunks (expanded & non-expanded).
dataset_config_fb_expand_hybrid4 = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    use_ppl_gain=False,
    include_known_chunk_labels=True,
    known_chunk_format=4,
)

# like expand4, but use dataset format 3 - i.e., split the list of known chunks
# into multiple sequences in a batch. Also like gain-hybrid2, use ppl gain.
dataset_config_fb_expand_hybrid5 = SimpleNamespace(
    tokenizer_name="StarCoderTokenizer",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=128,
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8100,
    dataset_format=3,
    use_ppl_gain=True,
    include_known_chunk_labels=True,
    known_chunk_format=4,
)


def set_mode(mode: str) -> None:
    global dataset_config, query_prompt_formatter_config, key_prompt_formatter_config

    query_prompt_formatter_name = "ethanol6_query"

    if mode == "neox":
        dataset_config = dataset_config_neox
    elif mode == "neox-pack":
        dataset_config = dataset_config_neox_pack
    elif mode == "fb":
        dataset_config = dataset_config_fb
    elif mode == "fb-secondary":
        dataset_config = dataset_config_fb_secondary
        query_prompt_formatter_name = "chatanol_query"
    elif mode == "fb-expand":
        dataset_config = dataset_config_fb_expand
        query_prompt_formatter_name = "chatanol_query"
    elif mode == "fb-expand2":
        dataset_config = dataset_config_fb_expand2
        query_prompt_formatter_name = "chatanol_query"
    elif mode == "fb-expand-hybrid":
        dataset_config = dataset_config_fb_expand_hybrid
        query_prompt_formatter_name = "chatanol_query"
    elif mode == "fb-expand-hybrid3":
        dataset_config = dataset_config_fb_expand_hybrid3
        query_prompt_formatter_name = "chatanol_query"
    elif mode == "fb-expand-hybrid4":
        dataset_config = dataset_config_fb_expand_hybrid4
        query_prompt_formatter_name = "chatanol_query"
    elif mode == "fb-expand-hybrid5":
        dataset_config = dataset_config_fb_expand_hybrid5
        query_prompt_formatter_name = "chatanol_query"
    elif mode == "gain-hybrid2":
        dataset_config = dataset_config_gain_hybrid2
        query_prompt_formatter_name = "chatanol_query"
    else:
        raise ValueError(f"Unknown mode: {mode}")

    query_prompt_formatter_config = {
        "name": query_prompt_formatter_name,
        "max_tokens": dataset_config.seq_length - 1,
        "tokenizer_name": dataset_config.tokenizer_name,
    }

    key_prompt_formatter_config = {
        "name": "ethanol6_document",
        "max_tokens": dataset_config.doc_seq_length - 1,
        "add_path": True,
        "tokenizer_name": dataset_config.tokenizer_name,
    }


# UTILS IMPLEMENTATION --------------------------------------------------------


def chunk_to_dict(chunk: Chunk, keep_full_files: bool) -> dict[str, Any]:
    chunk_dict = {
        "id": chunk.id,
        "text": chunk.text,
        "parent_doc": {
            "id": chunk.parent_doc.id,
            "path": chunk.parent_doc.path,
            # WARNING: just storing empty string if we don't want to store file
            "text": chunk.parent_doc.text if keep_full_files else "",
            # Not supporting meta field
        },
        "char_offset": chunk.char_offset,
        "length": chunk.length,
        "line_offset": chunk.line_offset,
        "length_in_lines": chunk.length_in_lines,
        # Not supporting meta field
    }
    return chunk_dict


def serialize_retrieved_chunks(
    retrieved_chunks: Sequence[Chunk], keep_full_files: bool
) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, keep_full_files) for chunk in retrieved_chunks]
    )


def serialize_retrieved_chunks_full_n(retrieved_chunks: Sequence[Chunk], n: int) -> str:
    """Convert retrieved chunks to string for use in dataframe."""
    return json.dumps(
        [chunk_to_dict(chunk, i < n) for i, chunk in enumerate(retrieved_chunks)]
    )


def deserialize_retrieved_chunks(retrieved_chunks: str) -> List[Chunk]:
    def to_chunk(dict_: Dict[str, Any]) -> Chunk:
        return Chunk(
            id=dict_["id"],
            text=dict_["text"],
            parent_doc=Document(
                id=dict_["parent_doc"]["id"],
                text=dict_["parent_doc"]["text"],
                path=dict_["parent_doc"]["path"],
            ),
            char_offset=dict_["char_offset"],
            length=dict_["length"],
            line_offset=dict_["line_offset"],
            length_in_lines=dict_["length_in_lines"],
        )

    dicts = json.loads(retrieved_chunks)
    return [to_chunk(dict_) for dict_ in dicts]


def select_chunk_lines(
    doc: Document, chunk, line_offset: int, length_in_lines: int
) -> Chunk:
    if len(doc.text) < len(chunk.text):
        raise ValueError(
            f"Document {doc.path} has length {len(doc.text)} but chunk length is {len(chunk.text)}"
        )

    lines = doc.text.splitlines(keepends=True)

    if line_offset + length_in_lines > len(lines):
        length_in_lines = len(lines) - line_offset
    if line_offset < 0:
        length_in_lines += line_offset
        line_offset = 0
    if (
        length_in_lines < 0
        or line_offset < 0
        or (line_offset + length_in_lines) > len(lines)
    ):
        raise ValueError(
            f"Invalid line_offset {line_offset} and length_in_lines {length_in_lines}. File length: {len(lines)}."
        )

    prefix_lines = "".join(lines[:line_offset])
    selected_lines = "".join(lines[line_offset : line_offset + length_in_lines])

    return Chunk(
        id=f"{doc.path}_{line_offset}_{length_in_lines}",
        text=selected_lines,
        char_offset=len(prefix_lines),
        length=len(selected_lines),
        line_offset=line_offset,
        length_in_lines=length_in_lines,
        parent_doc=doc,
    )


def expand_chunk(chunk: Chunk, expand_factor: float, to_left: bool):
    if expand_factor < 1:
        raise ValueError(f"Invalid expand_factor {expand_factor}")

    expand_by = round(chunk.length_in_lines * (expand_factor - 1))
    return select_chunk_lines(
        chunk.parent_doc,
        chunk,
        chunk.line_offset - (expand_by if to_left else 0),
        chunk.length_in_lines + expand_by,
    )


class Timer:
    def __init__(self):
        self.start_time = 0.0
        self.measurements = {}

    def start(self):
        self.start_time = time.time()

    def stop(self, name):
        self.measurements[name] = time.time() - self.start_time

    def get(self):
        return self.measurements


# STAGES IMPLEMENTATION -------------------------------------------------------


def stage1(input_path, stage1_uri, small_cluster):
    desired_partitions = 500
    spark = k8s_session(max_workers=4, name="igor-distill-generate")
    spark.read.json(input_path).orderBy(F.rand()).repartition(
        desired_partitions
    ).write.parquet(stage1_uri)
    spark.stop()


def stage2(stage1_uri, stage2_uri, small_cluster):
    def process_repo(
        files: Sequence[Mapping[str, Any]],
        documents_with_questions: Sequence[Mapping[str, Any]],
        retrieval_config: SimpleNamespace,
        retrieval_database: Any,
    ) -> Generator[pd.Series, None, None]:
        """Convert entire repo into retrieval-augmented FiM samples."""
        # Populate retrieval database with files
        file_idx = 0
        for file in files:
            file_idx += 1
            if file_idx % 100 == 0:
                print(f"Processing file {file_idx}: {file[PATH_COLUMN]}")

            document = Document(
                id=file[ID_COLUMN], text=file[CONTENT_COLUMN], path=file[PATH_COLUMN]
            )
            retrieval_database.add_doc(document)

        for question_record in documents_with_questions:
            question = question_record["question"]

            model_input = ModelInput(
                prefix=question,
            )
            retrieved_chunks = retrieval_database.query(model_input=model_input)[0]
            top_k = (
                retrieval_config.num_retrieved_chunks
                + retrieval_config.num_retrieved_extra
            )

            if retrieval_config.prioritize_gold_chunks:
                # Prioritize gold chunks, retain original rank indices
                retrieved_chunks_with_scores = [
                    (
                        0 if chunk.parent_doc.path in question_record["paths"] else 1,
                        i,
                        chunk,
                    )
                    for i, chunk in enumerate(retrieved_chunks)
                ]
                retrieved_chunks_with_scores.sort()

                # Take top K
                retrieved_chunks_with_scores = retrieved_chunks_with_scores[:top_k]

                # Sort just the top K by original rank indices
                retrieved_chunks_with_scores.sort(key=lambda x: x[1])
                retrieved_chunks = [x[2] for x in retrieved_chunks_with_scores]

            retrieved_chunks = retrieved_chunks[:top_k]
            if len(retrieved_chunks) < retrieval_config.num_retrieved_chunks:
                continue

            yield pd.Series(
                dict(
                    question=question,
                    answer=question_record["answer"],
                    paths=question_record["paths"],
                    retrieved_chunks=serialize_retrieved_chunks_full_n(
                        retrieved_chunks,
                        retrieval_config.num_expandable_chunks,
                    ),
                )
            )
        # We keep database, but depopulate inbetween repos
        retrieval_database.remove_all_docs()

    def process_partition_pandas(
        batch: pd.DataFrame,
        retrieval_config: SimpleNamespace,
    ) -> Iterable[pd.Series]:
        """Process a single partition of the dataset.

        Args:
            batch: A single partition of the dataset.
            retrieval_config: The configuration object.

        Returns:
            A generator of processed rows.
        """

        if retrieval_config_version == 1:
            retrieval_database = create_retriever(retrieval_config.retriever)
        else:
            retrieval_database = create_retriever_fb(retrieval_config.retriever)

        retrieval_database.scorer.load()

        for files, docs_q in zip(batch.file_list, batch.documents_with_questions):
            print(f"Processing repo {batch.max_stars_repo_name}")
            yield from process_repo(
                files,
                docs_q,
                retrieval_config=retrieval_config,
                retrieval_database=retrieval_database,
            )

    spark_conf = {
        "spark.executor.pyspark.memory": "1050G",
        "spark.executor.memory": "30G",
        "spark.sql.parquet.columnarReaderBatchSize": "256",
        "spark.task.cpus": "5",
        "spark.sql.execution.arrow.maxRecordsPerBatch": "1",
    }

    spark = k8s_session(
        name="igor-distill-process",
        max_workers=16 if not small_cluster else 2,
        conf=spark_conf,
        # gpu_type="RTX_A5000",
        gpu_type="A100_NVLINK_80GB",
    )

    retrieval_config = (
        retrieval_config_v2 if retrieval_config_version == 2 else retrieval_config_v1
    )

    result = map_parquet.apply_pandas(
        spark,
        partial(process_partition_pandas, retrieval_config=retrieval_config),
        input_path=stage1_uri,
        output_path=stage2_uri,
        timeout=36000,  # 10-hour timeout
        batch_size=1,
    )
    spark.stop()

    for i, output in enumerate(result["task_info"].stdout):
        print(f"=== OUTPUT {i} ===")
        print(output)

    for i, output in enumerate(result["task_info"].stderr):
        if output.strip() == "":
            continue

        print(f"=== ERROR {i} ===")
        print(output)


def stage3(stage2_uri, stage3_uri, small_cluster: bool):
    def score_chunks(
        model,
        model_input: ModelInput,
        chunk_lists: list[list[Chunk]],
        batch_size: int,
        name: str,
    ) -> list[float]:
        scores: list[float] = []

        for i in range(0, len(chunk_lists), batch_size):
            gc.collect()
            torch.cuda.empty_cache()

            try:
                output = model.forward_pass(
                    [
                        dataclasses.replace(model_input, retrieved_chunks=chunk_list)
                        for chunk_list in chunk_lists[i : (i + batch_size)]
                    ]
                )
            except torch.cuda.OutOfMemoryError as e:
                ranges = [
                    [f"{c.parent_doc.path} {c.range}" for c in chunk_list]
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]

                tokens = [
                    len(
                        model.prompt_formatter.prepare_prompt(
                            dataclasses.replace(
                                model_input, retrieved_chunks=chunk_list
                            )
                        )[0]
                    )
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]
                raise RuntimeError(
                    f"[{name}] Out of memory on chunklist: {ranges}, {tokens} tokens"
                ) from e

            scores += [
                -torchF.cross_entropy(
                    o.logits[o.target_mask],
                    o.label_tokens[o.target_mask],
                    reduction="mean",
                ).item()
                for o in output
            ]

        return scores

    def compute_ppl(batch, distill_config):
        from datetime import datetime

        global cached_model
        global trace_fh
        if "cached_model" not in globals():  # Construct a scorer
            trace_fh = open("/tmp/trace.txt", "a")

            print(
                "### Initializing model",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                file=trace_fh,
            )
            import research.models.fastforward_llama_models  #  noqa: F401

            print("Constructing the model...")
            cached_model = create_model(distill_config["model_config"])
            dpf_name, dpf_kwargs = parse_yaml_config(
                distill_config["prompt_formatter_config"]
            )
            cached_model.prompt_formatter = get_prompt_formatter(dpf_name, **dpf_kwargs)

            # Load the reranking model
            print("Loading the model...")
            cached_model.load()
            print(
                "### Done initializing model",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                file=trace_fh,
            )

            import threading
            import traceback
            import sys
            import time
            from datetime import datetime

            def print_stack_traces():
                """Prints the stack traces of all threads and logs them to a file."""
                while True:
                    with open("/tmp/stack_traces.log", "a") as log_file:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        log_file.write(
                            f"\n\n[{timestamp}] Printing stack traces of all threads:\n"
                        )
                        for thread_id, frame in sys._current_frames().items():
                            log_file.write(f"\nThread ID: {thread_id}\n")
                            traceback.print_stack(frame, file=log_file)
                        log_file.write("\n" + "=" * 80 + "\n")
                    time.sleep(600)  # Sleep for 10 minutes (600 seconds)

            # Create and start the thread
            stack_trace_thread = threading.Thread(
                target=print_stack_traces, daemon=True
            )
            stack_trace_thread.start()

        def apply_compute_ppl(
            question,
            answer,
            retrieved_chunks,
            paths,
            ppl_scores=None,
        ):
            print(
                "#### Starting apply_compute_ppl",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                file=trace_fh,
            )

            chat_input = ChatInput(
                history=[],
                request=question,
            )

            model_input = ModelInput(
                retrieved_chunks=deserialize_retrieved_chunks(retrieved_chunks),
                target=answer,
                chat_input=chat_input,
                selected_code="",
                path="",
            )

            def score_chunks_f(chunk_lists, timer, name) -> list[float]:
                timer.start()
                print(
                    f"##### Scoring chunks {name}",
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    file=trace_fh,
                )
                scores = score_chunks(
                    model=cached_model,
                    model_input=model_input,
                    chunk_lists=chunk_lists,
                    batch_size=distill_config["batchsize"],
                    name=name,
                )
                print(
                    f"##### Done scoring chunks {name}",
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    file=trace_fh,
                )
                timer.stop(name)
                return scores

            print(
                "#### Preparing chunks",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                file=trace_fh,
            )

            # Constants
            max_secondary_chunks = distill_config["secondary"]["max_secondary_chunks"]
            max_prompt_tokens = distill_config["secondary"]["max_prompt_tokens"]
            expand_prob = distill_config["secondary"]["expand_prob"]
            default_expand_factor = distill_config["secondary"]["expand_factor"]
            max_chunk_tokens = max_prompt_tokens / default_expand_factor

            # Construct the secondary retrieval prompt by expanding some chunks
            secondary_chunks = []

            # We need to estimate the length of the prompt, so we need to
            # first account for the question and the answer, which will both be
            # in the prompt
            est_prompt_tokens = len(
                cached_model.tokenizer.tokenize(
                    question + answer, no_special_tokens=True
                )
            )

            for chunk in deserialize_retrieved_chunks(retrieved_chunks)[
                :max_secondary_chunks
            ]:
                chunk_tokens = len(
                    cached_model.tokenizer.tokenize(chunk.text, no_special_tokens=True)
                )

                if chunk_tokens > max_chunk_tokens:
                    print(
                        f"Chunk {chunk.id} is {chunk_tokens} tokens, which is more than the max {max_chunk_tokens}. Skipping."
                    )
                    continue

                if random.random() < expand_prob and chunk.parent_doc.text:
                    max_expand_factor = max_chunk_tokens / chunk_tokens
                    expand_factor = math.exp(
                        random.random() * math.log(max_expand_factor)
                    )
                    chunk = expand_chunk(
                        chunk, expand_factor, to_left=(random.random() < 0.5)
                    )

                    # Recompute the number of tokens in the chunk
                    chunk_tokens = len(
                        cached_model.tokenizer.tokenize(
                            chunk.text, no_special_tokens=True
                        )
                    )
                    if chunk_tokens > max_chunk_tokens:
                        print(
                            f"Chunk {chunk.id} expanded to {chunk_tokens} tokens, which is more than the max {max_chunk_tokens}."
                            " Skipping. This should be rare."
                        )
                        continue

                assert chunk_tokens <= max_chunk_tokens, (
                    chunk_tokens,
                    max_chunk_tokens,
                )
                if dataset_config.known_chunk_format == 1 and (
                    est_prompt_tokens + chunk_tokens > max_prompt_tokens
                ):
                    break
                est_prompt_tokens += chunk_tokens
                secondary_chunks.append(chunk)

            retr_chunks = deserialize_retrieved_chunks(retrieved_chunks)

            print(
                "#### Done preparing chunks",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                file=trace_fh,
            )

            timer = Timer()
            timer.start()

            old_scores = {}
            if ppl_scores is not None:
                old_scores = json.loads(ppl_scores)

            secondary_scores = {}
            compute_secondary_scores = dataset_config.include_known_chunk_labels
            if compute_secondary_scores:
                secondary_chunk_scores = score_chunks_f(
                    [[c] for c in secondary_chunks],
                    timer,
                    "secondary_chunk_scores",
                )

                if dataset_config.known_chunk_format == 1:
                    secondary_prefix = score_chunks_f(
                        [
                            secondary_chunks[0:i]
                            for i in range(len(secondary_chunks) + 1)
                        ],
                        timer,
                        "secondary_prefix",
                    )

                    score_chunk_gain = [
                        secondary_prefix[i + 1] - secondary_prefix[i]
                        for i in range(len(secondary_chunks))
                    ]
                else:
                    empty_score = score_chunks_f(
                        [[]],
                        timer,
                        "empty_score",
                    )[0]
                    score_chunk_gain = [
                        sec_score - empty_score for sec_score in secondary_chunk_scores
                    ]

                secondary_expand_left = score_chunks_f(
                    [
                        [expand_chunk(c, default_expand_factor, to_left=True)]
                        for c in secondary_chunks
                    ],
                    timer,
                    "secondary_expand_left",
                )
                secondary_expand_right = score_chunks_f(
                    [
                        [expand_chunk(c, default_expand_factor, to_left=False)]
                        for c in secondary_chunks
                    ],
                    timer,
                    "secondary_expand_right",
                )

                def sub(a, b):
                    return [a[i] - b[i] for i in range(len(a))]

                secondary_scores = {
                    "secondary": {
                        "chunks": serialize_retrieved_chunks(
                            secondary_chunks, keep_full_files=False
                        ),
                        "chunk_scores": secondary_chunk_scores,
                        "gain": score_chunk_gain,
                        "gain_expand_left": sub(
                            secondary_expand_left, secondary_chunk_scores
                        ),
                        "gain_expand_right": sub(
                            secondary_expand_right, secondary_chunk_scores
                        ),
                        "empty_score": empty_score,
                    },
                }

            if ppl_scores is not None and "scores" in ppl_scores:
                # already preserved via old_scores
                primary_scores = {}
            else:
                primary_scores = (
                    {
                        "scores": score_chunks_f(
                            [[c] for c in retr_chunks],
                            timer,
                            "scores",
                        ),
                    }
                    if (dataset_config.retrieved_docs > 1)
                    else {}
                )

            print(
                "#### Finished apply_compute_ppl",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                file=trace_fh,
            )
            return json.dumps(
                {
                    **old_scores,  # old scores come first - subsequent entries override
                    **primary_scores,
                    **secondary_scores,
                    "timer": timer.get(),
                }
            )

        # Using batch.assign(...) instead of batch["ppl_scores"] = ... to avoid SettingWithCopyWarning,
        # since batch is a slice of another DataFrame. We don't actually care about the original data
        # frame so the warning would be safe to ignore. This approach makes it explicit that we
        # aren't trying to modify the original data frame, so it seems cleaner (+ it avoids the warning).
        print(
            "### Starting batch.apply",
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            file=trace_fh,
        )
        result = batch.assign(
            ppl_scores=batch.apply(lambda row: apply_compute_ppl(**row), axis=1)
        )
        print(
            "### Finished batch.apply",
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            file=trace_fh,
        )
        return result

    spark_gpu = k8s_session(
        name="igor-distill-compute-ppl" + ("-small" if small_cluster else "-large"),
        max_workers=128 if not small_cluster else 4,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
            "spark.executorEnv.LD_LIBRARY_PATH": "/opt/conda/lib/python3.11/site-packages/torch/lib:/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/lib/x86_64-linux-gnu:/usr/local/nvidia/lib:/usr/local/nvidia/lib64",
        },
        gpu_type="H100_NVLINK_80GB",
    )

    map_parquet.apply_pandas(
        spark_gpu,
        partial(compute_ppl, distill_config=distill_config),
        input_path=stage2_uri,
        output_path=stage3_uri,
        timeout=365 * 24 * 60 * 60,  # year!
    )

    spark_gpu.stop()


def stage3_add_empty(stage2_uri, stage3_uri, small_cluster: bool):
    def score_chunks(
        model,
        model_input: ModelInput,
        chunk_lists: list[list[Chunk]],
        batch_size: int,
        name: str,
    ) -> list[float]:
        scores: list[float] = []

        for i in range(0, len(chunk_lists), batch_size):
            gc.collect()
            torch.cuda.empty_cache()

            try:
                output = model.forward_pass(
                    [
                        dataclasses.replace(model_input, retrieved_chunks=chunk_list)
                        for chunk_list in chunk_lists[i : (i + batch_size)]
                    ]
                )
            except torch.cuda.OutOfMemoryError as e:
                ranges = [
                    [f"{c.parent_doc.path} {c.range}" for c in chunk_list]
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]

                tokens = [
                    len(
                        model.prompt_formatter.prepare_prompt(
                            dataclasses.replace(
                                model_input, retrieved_chunks=chunk_list
                            )
                        )[0]
                    )
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]
                raise RuntimeError(
                    f"[{name}] Out of memory on chunklist: {ranges}, {tokens} tokens"
                ) from e

            scores += [
                -torchF.cross_entropy(
                    o.logits[o.target_mask],
                    o.label_tokens[o.target_mask],
                    reduction="mean",
                ).item()
                for o in output
            ]

        return scores

    def compute_ppl(batch, distill_config):
        global cached_model
        if "cached_model" not in globals():  # Construct a scorer
            import research.models.fastforward_llama_models  #  noqa: F401

            print("Constructing the model...")
            cached_model = create_model(distill_config["model_config"])
            dpf_name, dpf_kwargs = parse_yaml_config(
                distill_config["prompt_formatter_config"]
            )
            cached_model.prompt_formatter = get_prompt_formatter(dpf_name, **dpf_kwargs)

            # Load the reranking model
            print("Loading the model...")
            cached_model.load()

        def apply_compute_ppl(
            question,
            answer,
            retrieved_chunks,
            paths,
            ppl_scores=None,
        ):
            chat_input = ChatInput(
                history=[],
                request=question,
            )

            model_input = ModelInput(
                retrieved_chunks=deserialize_retrieved_chunks(retrieved_chunks),
                target=answer,
                chat_input=chat_input,
                selected_code="",
                path="",
            )

            empty_score = score_chunks(
                model=cached_model,
                model_input=model_input,
                chunk_lists=[[]],
                batch_size=distill_config["batchsize"],
                name="empty_score",
            )[0]

            assert ppl_scores is not None
            ppl_scores = json.loads(ppl_scores)
            ppl_scores["empty_score"] = empty_score

            return json.dumps(ppl_scores)

        # Using batch.assign(...) instead of batch["ppl_scores"] = ... to avoid SettingWithCopyWarning,
        # since batch is a slice of another DataFrame. We don't actually care about the original data
        # frame so the warning would be safe to ignore. This approach makes it explicit that we
        # aren't trying to modify the original data frame, so it seems cleaner (+ it avoids the warning).
        return batch.assign(
            ppl_scores=batch.apply(lambda row: apply_compute_ppl(**row), axis=1)
        )

    spark_gpu = k8s_session(
        name="igor-distill-compute-ppl",
        max_workers=64 if not small_cluster else 4,
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "32G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
            "spark.executorEnv.LD_LIBRARY_PATH": "/opt/conda/lib/python3.11/site-packages/torch/lib:/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/lib/x86_64-linux-gnu:/usr/local/nvidia/lib:/usr/local/nvidia/lib64",
        },
        gpu_type="H100_NVLINK_80GB",
    )

    map_parquet.apply_pandas(
        spark_gpu,
        partial(compute_ppl, distill_config=distill_config),
        input_path=stage2_uri,
        output_path=stage3_uri,
        timeout=365 * 24 * 60 * 60,  # year!
    )

    spark_gpu.stop()


def stage4(stage3_uri, stage4_uri, small_cluster):
    desired_partitions = 500
    spark = k8s_session(max_workers=8, name="igor-distill-shuffle")
    spark.read.parquet(stage3_uri).orderBy(F.rand()).repartition(
        desired_partitions
    ).write.parquet(stage4_uri)
    spark.stop()


def stage5(stage4_uri, stage5_uri, small_cluster):
    def create_prompt_formatter(formatter_config):
        cls_name, kwargs = parse_yaml_config(formatter_config)
        return get_prompt_formatter(cls_name, **kwargs)

    def create_chunk_formatter(formatter_config):
        cls_name, kwargs = parse_yaml_config(formatter_config)
        return get_chunk_formatter(cls_name, **kwargs)

    spark_config = {
        "spark.executor.pyspark.memory": "1050g",
    }
    spark = k8s_session(
        name="igor-distill-tokenize",
        conf=spark_config,
        max_workers=32,
    )

    def pack_prompt(prompt: List[int], pad_token: int, should_pad=True) -> bytearray:
        if should_pad:
            prompt_arr = np.pad(
                prompt,
                (0, 1 + dataset_config.seq_length - len(prompt)),
                constant_values=pad_token,
            )
        else:
            prompt_arr = np.array(prompt)

        return bytearray(prompt_arr.astype(np.uint16).newbyteorder("<").tobytes())

    def pack_prompts(
        question="",
        answer="",
        paths="",
        retrieved_chunks="",
        ppl_scores="",
        query_prompt_formatter_config=None,
        key_prompt_formatter_config=None,
    ):
        assert dataset_config.known_chunk_format == 4

        retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunks)
        if len(retrieved_chunks) < dataset_config.retrieved_docs:
            raise ValueError(
                f"Too few retrieved chunks: {len(retrieved_chunks)}, expected {dataset_config.retrieved_docs}"
            )

        retrieved_chunks = retrieved_chunks[: dataset_config.retrieved_docs]
        ppl_scores = json.loads(ppl_scores)

        # pull in registrations
        import experimental.igor.systems.chatanol  # noqa: F401
        import research.core.prompt_formatters  # noqa: F401
        import research.retrieval.chunk_formatters  # noqa: F401

        query_prompt_formatter = create_prompt_formatter(query_prompt_formatter_config)
        key_prompt_formatter = create_chunk_formatter(key_prompt_formatter_config)

        end_of_query_token = query_prompt_formatter.tokenizer.vocab[
            "<|ret-endofquery|>"
        ]
        end_of_key_token = key_prompt_formatter.tokenizer.vocab["<|ret-endofkey|>"]
        eod_token = key_prompt_formatter.tokenizer.eod_id
        pad_token = key_prompt_formatter.tokenizer.pad_id

        assert "secondary" in ppl_scores
        known_chunks = deserialize_retrieved_chunks(
            ppl_scores["secondary"]["chunks"],
        )

        known_chunk_labels = list(
            zip(
                ppl_scores["secondary"]["gain"],
                ppl_scores["secondary"]["gain_expand_left"],
                ppl_scores["secondary"]["gain_expand_right"],
            )
        )

        assert len(known_chunks) == len(ppl_scores["secondary"]["gain"])
        assert len(known_chunks) == len(ppl_scores["secondary"]["gain_expand_left"])
        assert len(known_chunks) == len(ppl_scores["secondary"]["gain_expand_right"])

        assert dataset_config.known_chunk_format == 4

        # shuffle known chunks and their labels
        shuffle_idx = np.random.permutation(len(known_chunks))
        known_chunks = [known_chunks[i] for i in shuffle_idx]
        known_chunk_labels = [known_chunk_labels[i] for i in shuffle_idx]

        # We may have to reconstruct the text of the parent doc for
        # the known chunks because we didn't save it earlier in the
        # pipeline.
        doc_text_map = {
            chunk.parent_doc.id: chunk.parent_doc.text
            for chunk in retrieved_chunks
            if chunk.parent_doc.text != ""
        }
        for chunk in known_chunks:
            if chunk.parent_doc.text == "":
                chunk.parent_doc.text = doc_text_map[chunk.parent_doc.id]
                assert (
                    chunk.text
                    == chunk.parent_doc.text[
                        chunk.char_offset : chunk.char_offset + chunk.length
                    ]
                ), (
                    chunk.text,
                    chunk.parent_doc.text[
                        chunk.char_offset : chunk.char_offset + chunk.length
                    ],
                )

        assert dataset_config.known_chunk_format == 4

        def scaling_factor(chunk: Chunk):
            lines = chunk.length_in_lines
            chunk_lines = 30
            return min(1, lines / chunk_lines)

        default_expand_factor = distill_config["secondary"]["expand_factor"]
        known_chunk_labels = list(
            (
                gain / scaling_factor(chunk),
                (gain + gain_expand_left)
                / scaling_factor(
                    expand_chunk(chunk, default_expand_factor, to_left=True)
                ),
                (gain + gain_expand_right)
                / scaling_factor(
                    expand_chunk(chunk, default_expand_factor, to_left=False)
                ),
            )
            for chunk, (gain, gain_expand_left, gain_expand_right) in zip(
                known_chunks, known_chunk_labels
            )
        )

        # We need to estimate the length of the prompt, so we need to
        # first account for the question
        question_tokens_num = len(
            query_prompt_formatter.tokenizer.tokenize(
                question,
                no_special_tokens=True,
            )
        )
        known_chunks_tokens_num = (
            dataset_config.known_chunks_budget + 1
        )  # exceed for the initial case

        # ... and then add estimated lengths of the known chunks.
        # We stop before we exceed the budget.
        known_chunk_batches = []

        assert dataset_config.dataset_format == 3
        known_chunk_labels2 = []
        for chunk, label_tuple in zip(known_chunks, known_chunk_labels):
            # estimate the length
            prompt_len = (
                len(
                    query_prompt_formatter.tokenizer.tokenize(
                        chunk.parent_doc.path + "\n" + chunk.text,
                        no_special_tokens=True,
                    )
                )
                + 2
            )  # budget for two special tokens

            if (
                known_chunks_tokens_num + prompt_len
                > dataset_config.known_chunks_budget
            ):
                known_chunk_batches.append([])
                known_chunks_tokens_num = question_tokens_num
                if (
                    known_chunks_tokens_num + prompt_len
                    > dataset_config.known_chunks_budget
                ):
                    # We can't fit this chunk even on its own with the question.
                    # Let's skip the chunk and hopefully the next one will be smaller.
                    continue

            known_chunk_labels2.append(label_tuple)
            known_chunk_batches[-1].append(chunk)
            known_chunks_tokens_num += prompt_len

        known_chunk_labels = known_chunk_labels2

        for batch in known_chunk_batches:
            assert question_tokens_num + len(batch) <= dataset_config.seq_length

        assert not hasattr(dataset_config, "add_bare_query")
        known_chunk_extra = {
            "known_chunks": [],
            "add_bare_query": True,
        }

        assert "scores" in ppl_scores
        chunk_scores = ppl_scores["scores"]

        if dataset_config.use_ppl_gain:
            if "empty_score" in ppl_scores:
                empty_score = ppl_scores["empty_score"]
            else:
                # This is an older dataset, so we don't have an empty_score.
                # Thankfully we can derive it.
                assert len(ppl_scores["secondary"]["chunk_scores"]) == len(
                    ppl_scores["secondary"]["gain"]
                )
                empty_score = statistics.mean(
                    sec_score - sec_gain
                    for sec_score, sec_gain in zip(
                        ppl_scores["secondary"]["chunk_scores"],
                        ppl_scores["secondary"]["gain"],
                    )
                )
            chunk_scores = [score - empty_score for score in chunk_scores]

        chunk_tuples = [
            (chunk, chunk_scores[i]) for i, chunk in enumerate(retrieved_chunks)
        ]

        doc_prompts = []
        for chunk, ppl_score in chunk_tuples:
            # Format the prompt
            prompt = key_prompt_formatter.format(chunk).tokens
            if len(prompt) > dataset_config.doc_seq_length:
                if dataset_config.allow_doc_clipping:
                    prompt = prompt[: dataset_config.doc_seq_length]
                else:
                    raise ValueError(
                        f"Prompt too long: {len(prompt)} > {dataset_config.doc_seq_length}"
                    )

            # Encode the perplexity score into tokens.
            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize(
                f"{ppl_score}", no_special_tokens=True
            )

            # Format the footer of the prompt
            if dataset_config.dataset_format == 1:
                suffix = [end_of_key_token] + ppl_info_tokens + [eod_token]
            else:
                suffix = [end_of_key_token] + ppl_info_tokens + [end_of_key_token]

            prompt.extend(suffix)

            # Check that the prompt is not too long
            if len(prompt) > dataset_config.seq_length:
                print("===================================================")
                print(key_prompt_formatter.tokenizer.detokenize(prompt))
                print("===================================================")
                raise ValueError(
                    f"{id} token length exceeds seq_len: {len(prompt)} > {dataset_config.seq_length}"
                )

            doc_prompts.append((ppl_score, prompt))

        # sort the documents in descending order of perplexity and keep the better ones
        doc_prompts = [
            prompt
            for _ppl_score, prompt in sorted(
                doc_prompts, key=lambda x: x[0], reverse=True
            )
        ][: dataset_config.retrieved_docs]

        # optionally shuffle the docs -- if the retriever training will see the order
        # of the docs (e.g., if multiple docs are packed into each sequence) -- shuffling
        # is important.
        if dataset_config.shuffle_docs:
            random.shuffle(doc_prompts)

        # group the documents into prompts
        assert dataset_config.dataset_format == 3

        retrieval_query_prompt, _ = query_prompt_formatter.prepare_prompt(
            ModelInput(
                prefix=question,
            )
        )
        retrieval_query_prompt.append(end_of_query_token)

        known_chunk_query_prompts = []
        for known_chunk_batch in known_chunk_batches:
            extra = known_chunk_extra.copy()
            extra["known_chunks"] = known_chunk_batch
            prompt, _ = query_prompt_formatter.prepare_prompt(
                ModelInput(
                    prefix=question,
                    extra=extra,
                )
            )
            known_chunk_query_prompts.append(prompt)

        known_chunks_labels_prompt = query_prompt_formatter.tokenizer.tokenize(
            json.dumps(
                {
                    "known_chunk_labels": known_chunk_labels,
                }
            )
        )

        def F(name, prompts):
            def T(x):
                return query_prompt_formatter.tokenizer.tokenize(
                    x, no_special_tokens=True
                )

            eod_id = query_prompt_formatter.tokenizer.eod_id
            return (
                [eod_id]
                + T(name)
                + [eod_id]
                + T(str(len(prompts)))
                + [eod_id]
                + sum([prompt + [eod_id] for prompt in prompts], [])
            )

        all_tokens = (
            F("retrieval_query", [retrieval_query_prompt])
            + F("known_chunks_query", known_chunk_query_prompts)
            + F("known_chunks_labels", [known_chunks_labels_prompt])
            + F("retrieved_chunks", doc_prompts)
        )
        return pack_prompt(all_tokens, pad_token, should_pad=False)

    map_parquet.apply(
        spark,
        partial(
            pack_prompts,
            query_prompt_formatter_config=query_prompt_formatter_config,
            key_prompt_formatter_config=key_prompt_formatter_config,
        ),
        input_path=stage4_uri,
        output_path=stage5_uri,
        output_column="prompt_tokens",
        timeout=7200,
        pass_as_kwargs=True,
    )
    spark.stop()


def stage6(stage5_uri, stage6_uri, small_cluster):
    def explode_prompts(batch, config=None):
        assert dataset_config.retrieved_docs % dataset_config.doc_batch_group_size == 0
        num_doc_groups = (
            dataset_config.retrieved_docs // dataset_config.doc_batch_group_size
        )

        # Note: str.len() works for lists too
        if dataset_config.dataset_format == 1:
            filtered_batch = batch[
                batch["prompt_tokens"].str.len() == num_doc_groups + 1
            ]
        else:
            filtered_batch = batch

        results = filtered_batch.explode("prompt_tokens")
        return results if len(results) > 0 else None

    print("Exploding prompts...", stage5_uri, stage6_uri)
    spark = k8s_session(
        name="igor-distill-explode",
        max_workers=32 if not small_cluster else 2,
    )
    map_parquet.apply_pandas(
        spark,
        partial(
            explode_prompts,
            config=dataset_config,
        ),
        input_path=stage5_uri,
        output_path=stage6_uri,
        output_column="prompt_tokens",
        timeout=7200,
    )


def stage7(stage6_uri, output_path, small_cluster):
    spark = k8s_session(
        name="igor-distill-export_indexed_dataset",
        max_workers=32 if not small_cluster else 2,
    )
    export_indexed_dataset(
        config=ObjectDict(
            {
                "name": "export-dataset",
                "input": stage6_uri,
                "output": Path(output_path),
                "samples_column": "prompt_tokens",
            }
        ),
        spark=spark,
        tokenizer=get_tokenizer(dataset_config.tokenizer_name),
    )

    spark.stop()


def stage8(output_path, small_cluster):
    dataset = MMapIndexedDataset(str(Path(output_path) / "dataset"))
    train_builder = make_builder(str(Path(output_path) / "train.bin"), "mmap")
    valid_builder = make_builder(str(Path(output_path) / "valid.bin"), "mmap")

    train_samples = max(0, len(dataset) - dataset_config.num_validation_samples)

    for idx in range(len(dataset)):
        if idx < train_samples:
            train_builder.add_array_item(dataset[idx])
            train_builder.end_document()
        else:
            valid_builder.add_array_item(dataset[idx])
            valid_builder.end_document()

    train_builder.finalize(str(Path(output_path) / "train.idx"))
    valid_builder.finalize(str(Path(output_path) / "valid.idx"))


def validate_configs():
    retrieval_config = (
        retrieval_config_v2 if retrieval_config_version == 2 else retrieval_config_v1
    )
    assert (
        retrieval_config.num_expandable_chunks
        >= distill_config["secondary"]["max_secondary_chunks"]
    )


if __name__ == "__main__":
    import argparse

    # Parse the arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("stages", type=int, help="stage to run", nargs="+")
    parser.add_argument(
        "--suffix", type=str, help="suffix to add to input/output paths", default=""
    )
    parser.add_argument("--version", type=str, help="model version", default="")
    parser.add_argument("--mode", type=str, help="mode to run in", default="neox")
    parser.add_argument(
        "--small-cluster", action="store_true", help="use a small cluster"
    )
    args = parser.parse_args()

    # Update the config
    set_mode(args.mode)
    validate_configs()

    # Stage functions
    stages = [
        (1, stage1, ["INPUT_PATH", "STAGE1_URI"]),
        (2, stage2, ["STAGE1_URI", "STAGE2_URI"]),
        (3, stage3, ["STAGE2_URI", "STAGE3_URI"]),
        (4, stage4, ["STAGE3_URI", "STAGE4_URI"]),
        (5, stage5, ["STAGE4_URI", "STAGE5_URI"]),
        (6, stage6, ["STAGE5_URI", "STAGE6_URI"]),
        (7, stage7, ["STAGE6_URI", "OUTPUT_PATH"]),
        (13, stage3_add_empty, ["STAGE3_NO_EMPTY_URI", "STAGE3_URI"]),
        (14, stage3, ["STAGE3_FEW_SEC_URI", "STAGE3_URI"]),
        (8, stage8, ["OUTPUT_PATH"]),
    ]

    # Run the stages
    for stage_id, stage_f, stage_path_names in stages:
        if stage_id in args.stages:
            suffix = "." + args.suffix if args.suffix else ""
            stage_paths = [
                PATHS[name].format(args.version, suffix) for name in stage_path_names
            ]

            print(f"Running stage {stage_id} with paths: {stage_paths}")
            stage_f(*stage_paths, small_cluster=args.small_cluster)

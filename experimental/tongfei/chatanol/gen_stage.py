import argparse
import json
from collections.abc import Iterable, Sequence

from base.prompt_format_retrieve import (
    get_retrieval_prompt_formatter_by_name,
    ChatRetrieverPromptInput,
)
from base.tokenizers import create_tokenizer_by_name
from experimental.tongfei.data.binks_schemas import (
    File,
    Repository,
    GeneratedRetrievalTrainingExample,
)

from experimental.tongfei.ray.ray_utils import AbstractRayActor, RayRunner
from research.core.types import Document, Chunk
from research.retrieval.chunking_functions import get_chunker
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.scorers.scoring_interface import (
    RetrievalDatabaseScorer,
    get_scorer,
)
from research.retrieval.types import <PERSON>ker


def serialize_retrieved_chunks(retrieved_chunks: Sequence[Chunk]):
    return json.dumps([chunk.to_json() for chunk in retrieved_chunks])


def create_retrieval_database(
    files: Iterable[File], chunker: <PERSON><PERSON>, scorer: RetrievalDatabaseScorer
):
    retrieval_database = RetrievalDatabase(
        chunker=chunker,
        scorer=scorer,
    )
    docs = [
        Document(
            id=file.hexsha,
            text=file.content,
            path=file.max_stars_repo_path,
        )
        for file in files
    ]
    retrieval_database.add_docs(docs)
    return retrieval_database


def generate_examples_for_repo(
    repo: Repository,
    chunker: Chunker,
    scorer: RetrievalDatabaseScorer,
    num_retrieved_chunks: int = 128,
    num_retrieved_extra: int = 128,
    prioritize_gold_chunks: bool = True,
) -> list[GeneratedRetrievalTrainingExample]:
    retrieval_database = create_retrieval_database(repo.file_list, chunker, scorer)
    results = []
    for doc in repo.documents_with_questions:
        pairs = doc.qa_pairs()
        for pair in pairs:
            model_input = ChatRetrieverPromptInput(
                prefix="",
                suffix="",
                path="",
                selected_code="",
                message=pair.question,
            )
            retrieved_chunks, _ = retrieval_database.query(model_input)
            k = num_retrieved_chunks + num_retrieved_extra

            if prioritize_gold_chunks:
                retrieved_chunks_with_scores = [
                    (chunk.parent_doc.path not in pair.paths, i, chunk)
                    for i, chunk in enumerate(retrieved_chunks)
                ]
                retrieved_chunks_with_scores.sort()  # natural order on tuples
                retrieved_chunks = [
                    chunk for _, _, chunk in retrieved_chunks_with_scores[:k]
                ]

            results.append(
                GeneratedRetrievalTrainingExample(
                    question=pair.question,
                    answer=pair.answer,
                    paths=pair.paths,
                    retrieved_chunks=retrieved_chunks,
                )
            )
    return results


class ExampleGenerationStage(
    AbstractRayActor[Repository, GeneratedRetrievalTrainingExample]
):
    """Generate retrieval training examples for each repository."""

    def __init__(
        self,
        tokenizer: str,
        chunker: str,
        scorer_type: str,
        scorer_ckpt: str,
        query_formatter: str,
        document_formatter: str,
    ):
        super().__init__(
            input_cls=Repository, output_cls=GeneratedRetrievalTrainingExample
        )
        self.tokenizer = create_tokenizer_by_name(tokenizer)
        self.chunker = get_chunker(chunker, max_chunk_chars=768)
        print(f"Scorer type: {scorer_type}")
        print(f"Scorer ckpt: {scorer_ckpt}")
        self.scorer = get_scorer(
            name=scorer_type,
            checkpoint_path=scorer_ckpt,
            tokenizer_name=tokenizer,
            query_formatter=get_retrieval_prompt_formatter_by_name(
                query_formatter, self.tokenizer
            ),
            document_formatter=get_retrieval_prompt_formatter_by_name(
                document_formatter, self.tokenizer
            ),
        )

    def process(self, repo: Repository) -> list[GeneratedRetrievalTrainingExample]:
        self.scorer.remove_all_docs()
        samples = generate_examples_for_repo(
            repo,
            self.chunker,
            self.scorer,
            num_retrieved_chunks=args.num_retrieved_chunks,
            num_retrieved_extra=args.num_retrieved_extra,
        )
        return samples


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", type=str, default="ray", choices=["local", "ray"])
    parser.add_argument(
        "--input", type=str, help="Binks dataset that ends with *.jsonl"
    )
    parser.add_argument("--output", type=str, help="Output path")
    parser.add_argument(
        "--num-retrieved-chunks",
        type=int,
        default=128,
        help="Number of retrieved chunks",
    )
    parser.add_argument(
        "--num-retrieved-extra",
        type=int,
        default=128,
        help="Number of retrieved extra chunks",
    )
    parser.add_argument(
        "--tokenizer", type=str, default="qwen25coder", help="Tokenizer name"
    )
    parser.add_argument(
        "--query-formatter", type=str, default="chatanol6", help="Query formatter name"
    )
    parser.add_argument(
        "--document-formatter",
        type=str,
        default="chatanol6-embedding-with-path-key",
        help="Document formatter name",
    )
    parser.add_argument(
        "--chunker", type=str, default="smart_line_level", help="Chunker name"
    )
    parser.add_argument(
        "--scorer-type", type=str, default="dense_scorer_v2_fbwd", help="Scorer name"
    )
    parser.add_argument("--scorer-ckpt", type=str, help="Scorer checkpoint path")
    parser.add_argument("--num-workers", type=int, default=1, help="Number of workers")
    parser.add_argument(
        "--gpus-per-worker", type=int, default=1, help="Number of GPUs per worker"
    )
    args = parser.parse_args()

    with RayRunner(
        actor_cls=ExampleGenerationStage,
        actor_args={
            "tokenizer": args.tokenizer,
            "query_formatter": args.query_formatter,
            "document_formatter": args.document_formatter,
            "chunker": args.chunker,
            "scorer_type": args.scorer_type,
            "scorer_ckpt": args.scorer_ckpt,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=8,
        num_gpu_per_worker=args.gpus_per_worker,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

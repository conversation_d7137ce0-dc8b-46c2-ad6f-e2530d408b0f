from experimental.tongfei.chatanol.common import DatasetConfig
from research.data.dataset.indexed_dataset import MMapIndexedDataset, make_builder
from pathlib import Path
import os

dataset_config = DatasetConfig()


def split_dev_set(input_path, output_path):
    dataset = MMapIndexedDataset(str(Path(input_path) / "dataset"))

    train_builder = make_builder(str(Path(output_path) / "train.bin"), "mmap")
    valid_builder = make_builder(str(Path(output_path) / "valid.bin"), "mmap")

    train_samples = max(0, len(dataset) - dataset_config.num_validation_samples)

    for idx in range(len(dataset)):
        if idx < train_samples:
            train_builder.add_array_item(dataset[idx])
            train_builder.end_document()
        else:
            valid_builder.add_array_item(dataset[idx])
            valid_builder.end_document()

    train_builder.finalize(str(Path(output_path) / "train.idx"))
    valid_builder.finalize(str(Path(output_path) / "valid.idx"))


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=str, help="input_path")
    parser.add_argument("--output", type=str, help="output path")
    args = parser.parse_args()
    args.input = os.path.abspath(os.path.realpath(args.input))
    args.output = os.path.abspath(os.path.realpath(args.output))
    split_dev_set(args.input, args.output)

import std

local.root = "/mnt/efs/augment/user/tongfei/hm/chatanol-new"

package augment = std.symlink(path="/home/<USER>/proj/augment")

binks_data = {BinksData:
    1-3-merged="/mnt/efs/augment/user/yury/binks/binks-v1.3-merged/repos_with_qa.jsonl"
    2-gemini="/mnt/efs/augment/user/yury/binks/binks-v2-gemini/repos_with_qa.jsonl"
    2-haiku="/mnt/efs/augment/user/yury/binks/binks-v2-haiku/repos_with_qa_fixincomplete.jsonl"
    2-haiku-v2="/mnt/efs/augment/user/yury/binks/binks-v2-haiku-v2/repos_with_qa_fixincomplete.jsonl"
    3="/mnt/efs/augment/user/yury/binks/binks-v3/repos_with_qa_extrareffilter.jsonl"
    3-1="/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa.jsonl"
    4="/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_withanswers.jsonl"
    5="/mnt/efs/augment/user/yury/binks/binks-v5-files-n-dirs/repos_with_qa.jsonl"
}

dtype = {Tokenizer: qwen25coder=uint32  starcoder=uint16}
chunker = {Chunker: smart_line_level}

scorer_type = {Retriever0:
  chatanol-4="dense_scorer_v2_fbwd"
}
scorer_ckpt = {Retriever0:
  chatanol-4="/mnt/efs/augment/user/tongfei/hm/chatanol/consolidate_mp_model/InitCkpt=ethanol-qwen25coder-1b5&Tokenizer=qwen25coder/out"
}
scorer_tokenizer = {Retriever0:
  chatanol-4="qwen25coder"
}

teacher_model_tokenizer = {TeacherModel:
  qwen25coder-7b="qwen25coder"
}
teacher_model_ckpt = {TeacherModel:
  qwen25coder-7b="/mnt/efs/augment/checkpoints/qwen25-coder/7b-fb-mp4"
}
teacher_model_mp_size = {TeacherModel:
  qwen25coder-7b=4
}


class ray(cluster_name="ray", num_workers=8, gpu_count=1, augment=$):
  def run(internal_script):
    export PYTHONPATH=$augment
    python -m experimental.tongfei.ray.gen_ray_cluster_spec \
      --name $cluster_name \
      --replicas $num_workers \
      --gpu-type nvidia-h100-mega-80gb \
      --gpu-count $gpu_count \
    > ray-cluster-spec.yaml
    kubectl apply -f ray-cluster-spec.yaml
    sleep 10
    ray_head_ip=$(kubectl get raycluster $cluster_name -o jsonpath='{.status.head.podIP}')
    ray_port=$(kubectl get raycluster $cluster_name -o jsonpath='{.status.endpoints.dashboard}')
    echo "RAY_ADDRESS=http://$ray_head_ip:$ray_port"
    export RAY_ADDRESS="http://$ray_head_ip:$ray_port"
    . $internal_script



@ray(cluster_name="ray-gen-examples", num_workers=8, gpu_count=1)
task generate_examples(
  augment=$,
  binks_data=$,
  scorer_tokenizer=$, chunker=$,
  scorer_type=$, scorer_ckpt=$
) -> out:
  mkdir -p $out
  abs_out=$(realpath $out)
  python $augment/experimental/tongfei/ray/submit_ray.py \
      --ray-address $RAY_ADDRESS \
      -- \
      python experimental/tongfei/chatanol/gen_stage.py \
        --input $binks_data \
        --output $abs_out \
        --tokenizer $scorer_tokenizer \
        --scorer-type $scorer_type \
        --scorer-ckpt $scorer_ckpt \
        --num-workers $num_workers


@ray(cluster_name="ray-teacher-score", num_workers=8, gpu_count=1)
task get_teacher_scores(
  augment=$,
  in=$generate_examples.out,
  teacher_model_tokenizer=$, teacher_model_ckpt=$, teacher_model_mp_size=$
) -> out:
  mkdir -p $out
  abs_out=$(realpath $out)
  python $augment/experimental/tongfei/ray/submit_ray.py \
      --ray-address $RAY_ADDRESS \
      -- \
      python experimental/tongfei/chatanol/score_stage.py \
        --input $in \
        --output $abs_out \
        --tokenizer $teacher_model_tokenizer \
        --model-ckpt $teacher_model_ckpt \
        --model-mp-size $teacher_model_mp_size \
        --num-workers $num_workers \
        --gpus-per-worker $teacher_model_mp_size


plan Run = {
  get_teacher_scores[Tokenizer:qwen25coder, TeacherModel:qwen25coder-7b, BinksData:1-3-merged]
}

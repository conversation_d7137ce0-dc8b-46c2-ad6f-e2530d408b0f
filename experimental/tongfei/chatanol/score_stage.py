import argparse
import dataclasses
from pathlib import Path

import ray
from tqdm import tqdm
from typing import Any, Dict, List, Sequence

import pandas as pd
import torch
import torch.nn.functional as F

from experimental.tongfei.ray.ray_utils import AbstractRayActor, <PERSON><PERSON><PERSON>ner
from research.core.llama_prompt_formatters import ChatTemplateBasedPromptFormatter
from research.core.model_input import ModelInput, ChatInput
from research.core.types import Chunk, Document
from research.models import GenerativeLanguageModel
from research.models.fastbackward_models import FastBackwardLLM

from experimental.tongfei.data.binks_schemas import (
    GeneratedRetrievalTrainingExample,
    SilverRetrievalTrainingExample,
)


def score_chunks(
    model: GenerativeLanguageModel,
    model_input: ModelInput,
    chunk_lists: list[
        list[Chunk]
    ],  # Batch, candidate (internal list may be of length 1)
    batch_size: int = 8,
) -> list[float]:
    scores: list[float] = []
    for i in range(0, len(chunk_lists), batch_size):
        try:
            output = model.forward_pass(
                [
                    dataclasses.replace(model_input, retrieved_chunks=chunk_list)
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]
            )
            scores += [
                -F.cross_entropy(
                    o.logits[o.target_mask],
                    o.label_tokens[o.target_mask],
                    reduction="mean",
                ).item()
                for o in output
            ]
        except torch.cuda.OutOfMemoryError:
            pass
            # TODO

    return scores


def compute_perplexity(
    sample: GeneratedRetrievalTrainingExample,
    model: GenerativeLanguageModel,
    batch_size: int = 8,
) -> SilverRetrievalTrainingExample:
    """
    Compute perplexity scores for a GeneratedRetrievalTrainingExample.

    Args:
        sample: The training example containing question, answer, and retrieved chunks
        model: The language model to use for scoring
        batch_size: Batch size for processing chunks

    Returns:
        A JSON string containing perplexity scores
    """
    # For debugging
    print(f"Computing perplexity for question: {sample.question[:50]}...")
    chat_input = ChatInput(
        history=[],
        request=sample.question,
    )

    model_input = ModelInput(
        retrieved_chunks=[],
        target=sample.answer,
        chat_input=chat_input,
        selected_code="",
        path="",
    )

    chunk_scores = score_chunks(
        model=model,
        model_input=model_input,
        chunk_lists=[[c] for c in sample.retrieved_chunks],
        batch_size=batch_size,
    )

    return SilverRetrievalTrainingExample(
        question=sample.question,
        chunks=sample.retrieved_chunks,
        perplexities=chunk_scores,
    )


class FBwdLLMForDistillation(FastBackwardLLM):
    def __init__(
        self,
        tokenizer_name: str,
        checkpoint_path: str,
        seq_length: int,
        model_parallel_size: int = 1,
    ):
        self.tokenizer_name = tokenizer_name
        super().__init__(
            checkpoint_path=Path(checkpoint_path),
            seq_length=seq_length,
            model_parallel_size=model_parallel_size,
        )
        self.load()

    def create_default_formatter(self):
        distill_prompt_template = """You are an AI programming assistant, and you only answer questions related to computer science.
        For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.

        ### Instruction:
        {%- for chunk in retrieved_chunks %}
        Consider the following excerpt from {{chunk.parent_doc.path}}{% if chunk.header %} under header `{{chunk.header}}`{% endif %}:
        ```
        {{chunk.text}}
        ```

        {%- endfor %}

        {{message}}

        ### Response:
        """
        return ChatTemplateBasedPromptFormatter(
            template=distill_prompt_template,
            tokenizer_name=self.tokenizer_name,
        )


class ComputePerplexityStage(
    AbstractRayActor[GeneratedRetrievalTrainingExample, SilverRetrievalTrainingExample]
):
    def __init__(
        self,
        tokenizer: str,
        model_ckpt: str,
        model_mp_size: int,
    ):
        super().__init__(
            input_cls=GeneratedRetrievalTrainingExample,
            output_cls=SilverRetrievalTrainingExample,
        )
        self.model = FBwdLLMForDistillation(
            tokenizer_name=tokenizer,
            checkpoint_path=model_ckpt,
            seq_length=4096,
            model_parallel_size=model_mp_size,
        )

    def process(
        self, x: GeneratedRetrievalTrainingExample
    ) -> list[SilverRetrievalTrainingExample]:
        out = compute_perplexity(x, self.model, batch_size=8)
        return [out]


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", type=str, default="ray", choices=["local", "ray"])
    parser.add_argument(
        "--input", type=str, help="Dataset of GeneratedRetrievalTrainingExample"
    )
    parser.add_argument(
        "--output", type=str, help="Dataset of SilverRetrievalTrainingExample"
    )
    parser.add_argument("--tokenizer", type=str, help="Tokenizer")
    parser.add_argument("--model-ckpt", type=str, help="Teacher model checkpoint")
    parser.add_argument(
        "--model-mp-size", type=int, default=1, help="Model parallel size"
    )
    parser.add_argument("--num-workers", type=int, default=1, help="Number of workers")
    parser.add_argument(
        "--gpus-per-worker", type=int, default=1, help="Number of GPUs per worker"
    )
    args = parser.parse_args()

    with RayRunner(
        actor_cls=ComputePerplexityStage,
        actor_args={
            "tokenizer": args.tokenizer,
            "model_ckpt": args.model_ckpt,
            "model_mp_size": args.model_mp_size,
        },
        num_workers=args.num_workers,
        num_gpu_per_worker=args.gpus_per_worker,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)

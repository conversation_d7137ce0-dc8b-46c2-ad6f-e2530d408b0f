"""Blend two datasets."""

import argparse
import json
import sys
from pathlib import Path

import numpy as np
import torch
from research.data.dataset.indexed_dataset import (
    MMapIndexedDataset,
    MMapIndexedDatasetBuilder,
)
from tqdm import tqdm


class DatasetShuffle(torch.utils.data.Dataset):
    """Wraps MMapIndexedDataset and represents a random subset of a given length."""

    def __init__(
        self, dataset: MMapIndexedDataset, limit: int = -1, shuffle_chunk: int = 1
    ):
        """Initialize the subset."""

        if limit == -1:
            limit = len(dataset)

        self.dataset = dataset
        self.length = limit

        if limit % shuffle_chunk != 0:
            raise ValueError("limit must be a multiple of shuffle_chunk")

        shuffle_indices = np.random.choice(
            len(self.dataset) // shuffle_chunk, limit // shuffle_chunk, replace=False
        )
        self.indices = [
            shuffle_indices[i] * shuffle_chunk + j
            for i in range(len(shuffle_indices))
            for j in range(shuffle_chunk)
        ]

    def __len__(self):
        """Return the length of the subset."""
        return self.length

    def __getitem__(self, idx):
        return self.dataset[self.indices[idx]]


class DatasetConcat(torch.utils.data.Dataset):
    """Wraps MMapIndexedDataset and represents N concatenated datasets."""

    def __init__(self, datasets: list[MMapIndexedDataset]):
        """Initialize the dataset."""
        print("Concatenating datasets ...", ",".join(str(len(d)) for d in datasets))

        self.datasets = datasets
        self.length = sum(len(d) for d in datasets)

    def __len__(self):
        """Return the length of the dataset."""
        return self.length

    def __getitem__(self, idx):
        for dataset in self.datasets:
            if idx < len(dataset):
                return dataset[idx]
            idx -= len(dataset)
        raise ValueError("Index out of bounds")


def main(args):
    """Main."""
    print(f"Loading {args.paths} ...")
    datasets = []
    for path in args.paths:
        if ":" in path:
            path, limit = path.split(":")
        else:
            limit = None
        dataset = MMapIndexedDataset(path)
        if limit is not None:
            dataset = DatasetShuffle(dataset, int(limit), args.shuffle_chunk)

        datasets.append(dataset)

    dataset = DatasetShuffle(DatasetConcat(datasets), shuffle_chunk=args.shuffle_chunk)

    Path(args.output).parent.mkdir(parents=True, exist_ok=True)
    with (Path(args.output + "_blend_datasets.txt")).open("w") as f:
        f.write(" ".join(sys.argv))
        f.write("\n\n")
        json.dump(vars(args), f, indent=2)

    print(f"Blending [{', '.join(args.paths)}] into {args.output} ...")
    builder = MMapIndexedDatasetBuilder(args.output + ".bin", dtype=np.int32)

    for idx in tqdm(range(len(dataset))):
        builder.add_item(dataset[idx])
        builder.end_document()

    builder.finalize(args.output + ".idx")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("paths", type=str, nargs="+", help="paths to the datasets")
    parser.add_argument(
        "-s",
        "--shuffle_chunk",
        type=int,
        default=1,
        help="chunk size for shuffling",
    )
    parser.add_argument("-o", "--output", type=str, help="path to the output dataset")

    main(parser.parse_args())

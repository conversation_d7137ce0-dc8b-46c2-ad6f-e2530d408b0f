function (
    det_ws, det_proj, wandb_proj, wandb_run, mpsize, component_name,
    init_ckpt, dim, gold_temp, pred_temp, logit_temp, max_iters, lr, weight_decay,
    train, dev, tokenizer, max_query_len, max_doc_len
) {
    "determined": {
        "description": "Trains an Ethanol model.",
        "workspace": det_ws,
        "project": det_proj,
    },
    "augment": {
        "podspec_path": "8xH100.yaml",
        "gpu_count": 64,
        "project_group": "finetuning",
        "keep_last_n_checkpoints": 1
    },
    "fastbackward_args": (if mpsize == 1 then {} else {"model_parallel_size": mpsize}) + {
        "wandb_project": wandb_proj,
        "run_name": wandb_run,
        "components": {
            "model": {
                "component_name": "create_dual_encoder_with_tokenizer",
                "query_model": "query_model",
                "doc_model": "query_model",
                "tokenizer": "tokenizer"
            },
            "query_model": {
                "component_name": "create_embedder_with_language_model",
                "language_model": "query_model_lm",
                "output_projection_dim": dim,
                "with_output_bias": true
            },
            "query_model_lm": {
                "component_name": component_name,
                "checkpoint_path": init_ckpt,
                "skip_output": true
            },
            "loss_fn": {
                "component_name": "PerplexityLoss",
                "config": {
                    "gold_temperature": gold_temp,
                    "pred_temperature": pred_temp,
                    "logits_scale": std.exp(logit_temp),
                    "learnable_logits_scale": false,
                    "reduction": "mean"
                }
            }
        },
        "eval_interval": 100,
        "checkpoint_interval": 100,
        "max_epochs": -1,
        "train_options": {
            "batch_size": 1,
            "gradient_accumulation_steps": 16,
            "max_iters": max_iters,
            "log_interval": 50,
            "grad_clip": 1.0,
            "optimizer": {
                "warmup_iters": 0,
                "learning_rate": lr,
                "min_lr": lr / 10,
                "decay_iters": max_iters,
                "eps": 1.0e-8,
                "weight_decay": weight_decay
            }
        },
        "eval_batch_size": 4,
        "train_data": {
            "path": train,
            "tokenizer_name": tokenizer,
            "documents_per_batch": 128,
            "max_query_tokens": max_query_len,
            "max_document_tokens": max_doc_len,
        },
        "eval_data": {
            "path": dev,
            "tokenizer_name": tokenizer,
            "documents_per_batch": 128,
            "max_query_tokens": max_query_len,
            "max_document_tokens": max_doc_len,
        }
    }
}

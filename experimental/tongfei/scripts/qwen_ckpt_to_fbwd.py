from base.tokenizers import create_tokenizer_by_name
from research.core.model_input import ModelInput
from research.fastbackward.checkpointing.huggingface import load_huggingface_checkpoint
from research.fastbackward.model import ModelArgs, GenericAttnSpec
from research.fastbackward import distributed

from dataclasses import dataclass, asdict
import torch
import json
from pathlib import Path

from research.models import GenerationOptions
from research.models.fastbackward_models import FastBackwardLLM


@dataclass
class QwenConfig:
    hidden_size: int
    num_layers: int
    num_query_heads: int
    num_kv_heads: int
    head_size: int


qwen_config: dict[str, QwenConfig] = {
    "0.5b": QwenConfig(896, 24, 14, 2, 128),
    "1.5b": QwenConfig(1536, 28, 12, 2, 128),
    "3b": QwenConfig(2048, 36, 16, 2, 128),
    "7b": QwenConfig(3584, 28, 28, 4, 128),
    "14b": QwenConfig(5120, 48, 40, 8, 128),
    "32b": QwenConfig(5120, 63, 40, 8, 128),
}


def main(path: str, output_path: str, size: str):
    attn_config = GenericAttnSpec(
        hidden_dim=qwen_config[size].hidden_size,
        n_heads=qwen_config[size].num_query_heads,
        n_kv_heads=qwen_config[size].num_kv_heads,
        norm_type="rmsnorm",
        pos_embed_type="rope",
        bias=False,
        qkv_bias=True,
    )
    model_args = ModelArgs(
        ffn_type="silu",
        bias="none",
        norm_type="rmsnorm",
        pos_embed_type="rope",
        dim=qwen_config[size].hidden_size,
        n_layers=qwen_config[size].num_layers,
        n_heads=qwen_config[size].num_query_heads,
        n_kv_heads=qwen_config[size].num_kv_heads,
        vocab_size=152064,
        multiple_of=512,
        ffn_dim_multiplier=1.95,
        norm_eps=1e-06,
        rope_theta=1000000.0,
        attn_config=attn_config,
        max_seq_len=32768,
    )
    distributed.init_distributed_for_training(1)
    state_dict = load_huggingface_checkpoint(path)
    torch.save(state_dict, output_path + "consolidated.00.pth")
    # save serialized model args
    model_args_json = asdict(model_args)
    with open(output_path + "params.json", "w") as f:
        json.dump(model_args_json, f, indent=2)

    # Test
    model = FastBackwardLLM(
        checkpoint_path=Path(output_path), seq_length=4096, model_parallel_size=1
    )
    model.load()
    tokenizer = create_tokenizer_by_name("qwen25coder")
    model.prompt_formatter.tokenizer = tokenizer
    options = GenerationOptions(temperature=0.0, max_generated_tokens=128)
    model_input = ModelInput(prefix="def hello_world() -> str:\n")
    print(model.generate(model_input, options))


if __name__ == "__main__":
    main(
        path="/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-3B",
        output_path="/mnt/efs/augment/checkpoints/qwen25-coder/3b-fb/",
        size="3b",
    )

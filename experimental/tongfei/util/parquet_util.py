from collections.abc import Sequence, Callable
from typing import Any
import pyarrow.parquet

from research.data.spark.utils import get_local_session
from research.data.spark.pipelines.utils.map_parquet import pandas_map_fn


def read_parquet(path: str) -> Sequence[dict[str, Any]]:
    """Read a parquet file or directory and return a list of objects."""
    table = pyarrow.parquet.read_table(path)
    return table.to_pylist()


def write_parquet(path: str, data: Sequence[dict[str, Any]]):
    """Write a list of objects to a parquet file."""

    table = pyarrow.Table.from_pylist(data)
    pyarrow.parquet.write_table(table, path)


def map_parquet_locally_without_spark(
    input_path: str, output_path: str, func: Callable
):
    """Apply a function to a parquet file and write the result to another parquet file."""

    data = read_parquet(input_path)
    result = [func(**x) for x in data]
    write_parquet(output_path, result)


def map_parquet_locally_in_spark(input_path: str, output_path: str, func: Callable):
    """Apply a function to a parquet file and write the result to another parquet file."""
    spark = get_local_session(
        workers=1,
        memory_per_worker_mb=16384,
        name="local-spark-test",
        conf={
            "spark.executor.memory": "16G",
            "spark.executor.pyspark.memory": "16G",
        },
    )
    pandas_fn = pandas_map_fn(func, pass_as_kwargs=True)
    df = spark.read.parquet(input_path)
    df = df.toPandas()
    result = pandas_fn(df)
    result = spark.createDataFrame(result)
    result.write.parquet(output_path)
    spark.stop()

system:
  name: remote_chat
  # The model name is optional, will select default if not present
  # model_name: binks-ug-chatanol1-18-reranker-chat
  model_name: gemini-1-5-flash-001-chat
  # model_name: binks-v11
  client:
      # Set a url to run the remote system.
      # url: https://dev-<USER>.us-central.api.augmentcode.com
      url: https://staging-shard-0.api.augmentcode.com
  chat:
    indexing_retry_sleep_secs: 30.0
    indexing_retry_count: 25

task:
  name: augment_qa
  dataset_path: /mnt/efs/augment/data/processed/augment_qa/v3
  html_report_output_dir: /mnt/efs/augment/public_html/augment_qa/v3

podspec: gpu-small.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: augment-qa-eval-dogfood
  project: tongfei-eval
  workspace: Dev

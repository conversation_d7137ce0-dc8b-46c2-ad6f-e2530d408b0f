import std

local.root = "/mnt/efs/augment/user/tongfei/hm/augment-qa"

package augment = std.symlink(path="/home/<USER>/proj/augment")

chat_model = {ChatModel:
  claude-sonnet-3-5-16k-direct-chat
  claude-sonnet-3-5-16k-v3-chat
  claude-sonnet-3-5-16k-v4-chat
  claude-sonnet-3-5-16k-v5-chat
  claude-sonnet-3-5-16k-v6-chat
  claude-sonnet-3-5-16k-reranker-chat
  claude-sonnet-3-5-128k-chat
  gemini-1-5-flash-001-chat
  gemini-1-5-flash-0827-128k-chat
  gemini-1-5-pro-001-chat
  gemini-1-5-pro-0827-128k-chat
  gemini-1-5-pro-0827-16k-chat
  llama3-1-405b-vertex-16k-chat
  llama3-1-405b-vertex-100k-chat
  #o1-mini-128k-v1-chat
  #o1-16k-v1-chat
}

task run_eval(augment=$, chat_model=$, root=$local.root) -> out:
  mkdir -p $out
  cp $augment/experimental/tongfei/augment_qa/eval_config_dogfood.yaml $out/eval.yaml
  yq -i ".system.model_name=\"$chat_model\"" $out/eval.yaml
  cd $augment/experimental/tongfei/augment_qa
  echo $root/$HYPERMAKE_JOB_WD/eval.yaml
  PYTHONPATH=$augment \
    python -m research.eval.eval $root/$HYPERMAKE_JOB_WD/out/eval.yaml \
    --cluster GCP-US1

task aggregate_results(run=$run_eval[ChatModel: *].out):
  for f in $(ls $run); do
    trial_id=$(cat $run/$f/../stdout | pcregrep -o1 'Created experiment (.*)')
    metrics=$(det trial logs -f $trial_id | pcregrep -o1 'report_validation_metrics\(.*(\{.*\})\)' | tail -n1)
    echo "$f: $metrics"
  done

plan RunEval = { run_eval[ChatModel: *] }

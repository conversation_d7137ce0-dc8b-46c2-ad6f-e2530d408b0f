from collections.abc import Collection, Iterable
import os
import subprocess
import time
from pathlib import Path
from typing import AbstractSet

from pylspclient import JsonRpcEndpoint, LspClient, LspEndpoint
from pylspclient.lsp_pydantic_strcuts import (
    TextDocumentItem,
    TextDocumentIdentifier,
    Position,
    CompletionContext,
    CompletionTriggerKind,
    LanguageIdentifier,
)

from research.core.types import Document, DocumentId
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CodeCompleteInput,
    CompletionResult,
)
from research.eval.harness.systems.abs_system import register_system
from research.models.all_models import get_model


class LspProcess:
    def __init__(self):
        server_path = (
            Path(os.getcwd()).parent.parent.parent
            / "clients"
            / "vim"
            / "sidecar"
            / "dist"
            / "server.bundle.js"
        )

        process = subprocess.Popen(
            ["node", server_path, "--stdio"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        # Give the server a moment to start
        time.sleep(0.1)

        if process.poll() is not None:
            assert process.stderr is not None
            err = process.stderr.read().decode()
            raise RuntimeError(f"Failed to start LSP server {err}")
        print(f"Launch lsp server {process.pid}")
        self.process = process

    def __del__(self):
        if self.process is None:
            return
        print(f"Terminate lsp server {self.process.pid}")
        self.process.terminate()
        self.process.wait()


def lsp_client(lsp_process: subprocess.Popen) -> LspClient:
    """Create an LSP client connected to the server."""
    json_rpc_endpoint = JsonRpcEndpoint(lsp_process.stdin, lsp_process.stdout)
    lsp_endpoint = LspEndpoint(json_rpc_endpoint)

    # Create LSP client
    lsp_client = LspClient(lsp_endpoint)

    lsp_client.initialize(
        processId=os.getpid(),
        rootPath=None,
        rootUri=None,  # root_uri,
        initializationOptions={},
        capabilities={},
        trace="off",
        workspaceFolders=None,
    )
    lsp_client.initialized()

    return lsp_client


@register_system("lsp_system")
class LSPSystem(CodeCompleteSystem):
    """Abstract interface that encapsulates LM-based application."""

    def __init__(self) -> None:
        super().__init__()
        self.client = None
        self.process = None
        self.model = get_model("null")

    def __del__(self):
        self.unload()

    def load(self):
        """Load the system."""
        self.process = LspProcess()  # lsp_process().__enter__()
        self.client = lsp_client(self.process.process)
        return None

    def unload(self):
        """Unload the system."""
        if self.client:
            self.client = None

        if self.process is not None:
            self.process = None

        return None

    def generate(self, model_input: CodeCompleteInput) -> CompletionResult:
        """Returns a Completion object.

        This object contains completion text, prompt tokens, retrieved chunks,
        and additional model outputs.
        """

        assert self.client is not None
        text = model_input.prefix + model_input.suffix
        # convert offset to line and character
        lines = text.splitlines(keepends=True)
        char_offset = len(model_input.prefix)
        line_number = 0
        char_in_line = 0
        for line in lines:
            if char_offset <= len(line):
                char_in_line = char_offset
                break
            char_offset -= len(line)
            line_number += 1
        pos = Position(line=line_number, character=char_in_line)

        ctx = CompletionContext(
            triggerKind=CompletionTriggerKind.Invoked, triggerCharacter=None
        )
        lang = LanguageIdentifier.PYTHON
        uri = "file:///{}".format(model_input.path)
        item = TextDocumentItem(uri=uri, languageId=lang, version=1, text=text)
        self.client.didOpen(item)
        items = self.client.completion(
            TextDocumentIdentifier(uri=item.uri), position=pos, context=ctx
        )
        assert isinstance(items, list)
        assert len(items) == 1
        # print(items[0].insertText)
        self.client.lsp_endpoint.send_notification(
            "textDocument/didClose", textDocument=item
        )

        generated_text = items[0].insertText
        return CompletionResult(
            generated_text=generated_text, prompt_tokens=[], retrieved_chunks=[]
        )

    def add_docs(self, src_files: Collection[Document]):
        """Ingest a copy of the source code repository."""

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        """Remove documents from the retriever."""
        raise NotImplementedError()

    def get_num_docs(self) -> int:
        """Return the number of documents in the retriever."""
        return len(self.get_doc_ids())

    def get_doc_ids(self) -> AbstractSet[DocumentId]:
        """Return the set of indexed documents ids."""
        return set()

    def get_docs(self, doc_ids: Iterable[DocumentId]) -> list[Document]:
        """Return all documents with the given ids indexed by the retriever."""
        raise NotImplementedError()

    def clear_retriever(self):
        """Clear any documents from the retriever.

        This is useful for clearing out the current repo from the retriever.
        """
        return None

    def get_model(self):
        """Return the model."""
        return self.model

    @classmethod
    def from_yaml_config(cls, config: dict) -> "LSPSystem":
        """Returns a System object constructed using a config dictionary."""
        return LSPSystem()

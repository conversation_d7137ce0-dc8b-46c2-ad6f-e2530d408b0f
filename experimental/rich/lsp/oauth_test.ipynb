{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import subprocess\n", "import time\n", "from getpass import getpass\n", "from typing import Generator\n", "from pathlib import Path\n", "from pydantic import SecretStr\n", "\n", "from pylspclient import JsonRpcEndpoint, LspClient, LspEndpoint\n", "from contextlib import contextmanager\n", "\n", "\n", "@contextmanager\n", "def lsp_process() -> Generator[subprocess.<PERSON>, None, None]:\n", "    \"\"\"Start the LSP server process.\"\"\"\n", "    # Assuming your Node.js LSP server is in the same directory structure\n", "    server_path = (\n", "        Path(os.getcwd()).parent.parent\n", "        / \"mpauly\"\n", "        / \"ts-augment-server\"\n", "        / \"dist\"\n", "        / \"server.js\"\n", "    )\n", "\n", "    # /home/<USER>/src/augment/experimental/mpauly/ts-augment-server/src/server.js\n", "    process = subprocess.Popen(\n", "        [\"node\", server_path, \"--stdio\"],\n", "        stdin=subprocess.PIPE,\n", "        stdout=subprocess.PIPE,\n", "        stderr=subprocess.PIPE,\n", "    )\n", "\n", "    # Give the server a moment to start\n", "    time.sleep(0.1)\n", "\n", "    if process.poll() is not None:\n", "        assert process.stderr is not None\n", "        err = process.stderr.read().decode()\n", "        raise RuntimeError(f\"Failed to start LSP server {err}\")\n", "\n", "    print(\"RAH RAH launch process\")\n", "    try:\n", "        yield process\n", "    finally:\n", "        if process is None:\n", "            return\n", "\n", "        print(\"RAH RAH terminate process\")\n", "        # Cleanup\n", "        process.terminate()\n", "        process.wait()\n", "        out = process.stdout.read().decode()\n", "        err = process.stderr.read().decode()\n", "        print(f\"process.returncode: {out}\")\n", "        print(f\"process.returncode: {err}\")\n", "\n", "\n", "def lsp_client(lsp_process: subprocess.Popen) -> LspClient:\n", "    \"\"\"Create an LSP client connected to the server.\"\"\"\n", "    json_rpc_endpoint = JsonRpcEndpoint(lsp_process.stdin, lsp_process.stdout)\n", "    lsp_endpoint = LspEndpoint(json_rpc_endpoint)\n", "    # time.sleep(60)\n", "\n", "    # Create LSP client\n", "    lsp_client = LspClient(lsp_endpoint)\n", "\n", "    # def initialize(self, processId, rootPath, rootUri, initializationOptions, capabilities, trace, workspaceFolders):\n", "    lsp_client.initialize(\n", "        processId=os.getpid(),\n", "        rootPath=None,\n", "        rootUri=None,  # root_uri,\n", "        initializationOptions={},\n", "        capabilities={},\n", "        trace=\"off\",\n", "        workspaceFolders=None,\n", "    )\n", "    lsp_client.initialized()\n", "\n", "    return lsp_client"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Login"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with lsp_process() as process:\n", "    client = lsp_client(process)\n", "    result = client.lsp_endpoint.call_method(\"augment/login\")\n", "    print(result)\n", "\n", "    code = SecretStr(getpass(\"Enter the code: \"))\n", "    result = client.lsp_endpoint.call_method(\n", "        \"augment/token\", code=code.get_secret_value()\n", "    )\n", "    print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Perform Completion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pylspclient.lsp_pydantic_strcuts import (\n", "    TextDocumentItem,\n", "    TextDocumentIdentifier,\n", "    Position,\n", "    CompletionContext,\n", "    CompletionTriggerKind,\n", "    LanguageIdentifier,\n", ")\n", "\n", "with lsp_process() as process:\n", "    client = lsp_client(process)\n", "    text = Path(\"test.py\").read_text()\n", "    # print(text)\n", "\n", "    pos = Position(line=5, character=14)\n", "    ctx = CompletionContext(\n", "        triggerKind=CompletionTriggerKind.Invoked, triggerCharacter=None\n", "    )\n", "    lang = LanguageIdentifier.PYTHON\n", "    item = TextDocumentItem(\n", "        uri=\"file:///workspace/test.py\", languageId=lang, version=1, text=text\n", "    )\n", "    client.didOpen(item)\n", "    items = client.completion(\n", "        TextDocumentIdentifier(uri=item.uri), position=pos, context=ctx\n", "    )\n", "    print(items)\n", "    assert isinstance(items, list)\n", "    assert len(items) == 1\n", "    print(items[0].insertText)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Logout"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with lsp_process() as process:\n", "    client = lsp_client(process)\n", "    result = client.lsp_endpoint.call_method(\"augment/logout\")\n", "    print(result)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""
Merge sort example
"""

def quicksort(

def merge_sort(a, b):
    """
    Merge sort
    """
    print("merge_sort", a, b)
    if not a:
        return b
    if not b:
        return a
    if a[0] < b[0]:
        return [a[0]] + merge_sort(a[1:], b)
    else:
        return [b[0]] + merge_sort(a, b[1:])

    print("merge_sort", a, b)
    return a

print("hello")

if __name__ == "__main__":
    """
    Main
    """
    print("hello")
    print("hello")
    print(merge_sort([1, 2, 3], [4, 5, 6]))
    print(merge_sort([1, 2, 3], [4, 5, 6]))

    print(merge_sort([1, 2, 3], [4, 5, 6]))
    print(merge_sort([1, 2, 3], [4, 5, 6]))

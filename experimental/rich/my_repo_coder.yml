#
# This file contains an example of an evaluation config
#

# Sections:
#   Checkpoints - specify the checkpoints to evaluate
#   Tasks - specify the evaluation tasks for each checkpoint
#   Podspec - overide the default podspec, if necessary
#   NEOX args - additional NEOX args applied to each run
#   Determined - name, workspace, project in the determined UI.
#   eval_tags - Optional dictionary of tags added to the eval results json file.

# Checkpoints
# Fields:
#  path: path or S3 URI to the checkpoint  -- REQUIRED
#  iteration: iteration at which to run the evaluation -- OPTIONAL
#  podspec: checkpoint specific podspec to override the default -- OPTIONAL

checkpoints:
  - path: /mnt/efs/augment/checkpoints/codegen-350M-multi
  # - path: /mnt/efs/augment/checkpoints/codegen-2B-multi
  # - path: /mnt/efs/augment/checkpoints/codegen-6B-multi
  # - path: /mnt/efs/augment/checkpoints/codegen-16B-multi
  # - path: s3://dev-training-dai/0d7ba813-dbb5-4255-a9b4-1ba1cae6f0df
  # - path: /mnt/efs/augment/user/carl/checkpoints/codegen-16B-finetune-52Btok-merged
  #   podspec: A40.yaml

# Tasks
#   specify the evaluation tasks for each checkpoint

tasks:
  # - repo_coder_api_oracle
  # - repo_coder_api_zero_shot
  # - repo_coder_api_rg1
  # - repo_coder_line_oracle
  # - repo_coder_line_zero_shot
  # - repo_coder_line_rg1
  # - repo_coder_function_oracle
  # - repo_coder_function_zero_shot
  # - repo_coder_function_rg1
  - repo_coder_function_bm25
  # - repo_coder_function_bm25_repocoder
  # - repo_coder_function_rg1_indiana

# Podspec - set the default podspec for all checkpoints

# The default podspec provides a single RTX_A5000 GPU
podspec: gpu-small.yaml
#podspec: a100-podspec.yaml
# podspec: a40-podspec.yaml


# neox_args are passed as overrides to the neox configuration found in the checkpoints.
neox_args:
  maximum_tokens: 280
  max_position_embeddings: 2048
  seq_length: 2048
  # temperature: 0.2
  # top_p: 0.95
  # top_k: 0
  temperature: 0
  top_p: 0
  top_k: 0
  eval_with_memories: False
  eval_batch_size: 10

# Determined
# name, workspace, project control location and display in the determined UI.

determined:
  name: RepoCoder Test BM25
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  metaconfig: jobs/templates/batch-eval.yaml

# Optional dictionary of tags added to the eval results json file.
# eval_tags: {}

name: multi_round_rag
load: [
  experimental.rich.systems.multi_round_RAG_system
]
experimental:
  retriever_top_k: 10
  trim_on_dedent: false
  trim_on_max_lines: null
  remove_suffix: false
  # use_fim_when_possible: true

generation_options:
  max_generated_tokens: 96
  # temperature: 0.8
  # top_p: 0.95
  temperature: 0.2
  top_p: 0.95
  top_k: 0

draft_model:
  name: rogue
  checkpoint_path: rogue/diffb1m_1b_alphal_v2downsmall/
  prompt:
    max_prefix_tokens: 1280
    max_prompt_tokens: 3816
    max_retrieved_chunk_tokens: -1
    max_suffix_tokens: 768

model:
  name: rogue
  # checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
  checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
  prompt:
    max_prefix_tokens: 1280
    max_prompt_tokens: 3816
    max_retrieved_chunk_tokens: -1
    max_suffix_tokens: 768

retriever:
  chunker: line_level
  max_chunk: 40
  max_query_lines: 20
  name: bm25

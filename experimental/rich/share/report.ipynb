{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import csv\n", "import sys\n", "from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "# dataset = \"us_staging_request_insight_analytics_dataset\"\n", "dataset = \"us_prod_request_insight_analytics_dataset\"\n", "\n", "# Which tenants have get/share requests?\n", "tenants_query = \"\"\"\n", "SELECT rm.tenant as tenant,\n", " countif(rm.request_type = 'SHARE_SAVE_CHAT') as save_count,\n", " countif(rm.request_type = 'SHARE_GET_CHAT') as get_count\n", "FROM system-services-prod.{dataset}.request_metadata as rm\n", "WHERE rm.request_type in ('SHARE_SAVE_CHAT', 'SHARE_GET_CHAT')\n", "and TIMESTAMP_TRUNC(rm.time, DAY) >= TIMESTAMP(\"2024-12-01\")\n", "GROUP BY rm.tenant\n", "ORDER BY save_count DESC\n", "\"\"\".format(dataset=dataset)\n", "\n", "save_chat_query = \"\"\"\n", "SELECT rm.tenant, user_id, Count(*) as count\n", "FROM \n", "system-services-prod.{dataset}.request_metadata as rm,\n", "system-services-prod.{dataset}.share_save_chat_request as s_in,\n", "system-services-prod.{dataset}.share_save_chat_response as s_out\n", "where rm.request_id = s_in.request_id\n", "and rm.request_id = s_out.request_id\n", "and TIMESTAMP_TRUNC(rm.time, DAY) >= TIMESTAMP(\"2024-12-01\")\n", "group by rm.tenant, user_id\n", "order by rm.tenant, Count(*) desc\n", "\"\"\".format(dataset=dataset)\n", "\n", "chat_per_day_query = \"\"\"\n", "SELECT \n", "    rm.tenant,\n", "    FORMAT_TIMESTAMP('%Y-%m-%d', TIMESTAMP_TRUNC(rm.time, DAY)) as day,\n", "    countif(rm.request_type = 'SHARE_SAVE_CHAT') as save_count,\n", "    countif(rm.request_type = 'SHARE_GET_CHAT') as get_count\n", "FROM system-services-prod.{dataset}.request_metadata as rm\n", "WHERE \n", "    TIMESTAMP_TRUNC(rm.time, DAY) >= TIMESTAMP(\"2024-12-01\")\n", "    AND rm.request_type in ('SHARE_SAVE_CHAT', 'SHARE_GET_CHAT')\n", "GROUP BY tenant, day\n", "ORDER BY tenant, day ASC\n", "\"\"\".format(dataset=dataset)\n", "\n", "read_chat_query = \"\"\"\n", "SELECT rm.tenant, user_id, Count(*) as count\n", "FROM \n", "system-services-prod.{dataset}.request_metadata as rm,\n", "system-services-prod.{dataset}.share_get_chat_request as s_in,\n", "system-services-prod.{dataset}.share_get_chat_response as s_out\n", "where rm.request_id = s_in.request_id\n", "and rm.request_id = s_out.request_id\n", "and TIMESTAMP_TRUNC(rm.time, DAY) >= TIMESTAMP(\"2024-12-01\")\n", "group by rm.tenant, user_id\n", "order by rm.tenant, Count(*) desc\n", "\"\"\".format(dataset=dataset)\n", "\n", "\n", "top_shared_query = \"\"\"\n", "SELECT rm.tenant, JSON_EXTRACT_SCALAR(s_in.sanitized_json, '$.request.uuid'), Count(*)\n", "FROM \n", "system-services-prod.{dataset}.request_metadata as rm,\n", "system-services-prod.{dataset}.share_get_chat_request as s_in,\n", "system-services-prod.{dataset}.share_get_chat_response as s_out\n", "where rm.request_id = s_in.request_id\n", "and rm.request_id = s_out.request_id\n", "and TIMESTAMP_TRUNC(rm.time, DAY) >= TIMESTAMP(\"2024-12-01\")\n", "group by rm.tenant, JSON_EXTRACT_SCALAR(s_in.sanitized_json, '$.request.uuid')\n", "order by rm.tenant, Count(*) desc\n", "\"\"\".format(dataset=dataset)\n", "\n", "Reports = {\n", "    \"tenants\": tenants_query,\n", "    \"chat_events_per_day\": chat_per_day_query,\n", "    \"save_chat\": save_chat_query,\n", "    \"read_chat\": read_chat_query,\n", "    \"top_shared\": top_shared_query,\n", "}\n", "\n", "\n", "gcp_creds, _ = get_gcp_creds(None)\n", "client = bigquery.Client(project=\"system-services-prod\", credentials=gcp_creds)\n", "\n", "\n", "for name, query in Reports.items():\n", "    print(f\"Running {name}\")\n", "    job_config = bigquery.QueryJobConfig(\n", "        query_parameters=[\n", "            bigquery.ScalarQueryParameter(\"tenant\", \"STRING\", \"dogfood-shard\"),\n", "        ]\n", "    )\n", "    rows = [*client.query_and_wait(query, job_config=job_config)]\n", "\n", "    writer = csv.writer(sys.stdout)\n", "\n", "    if len(rows) == 0:\n", "        print(\"No rows\")\n", "        continue\n", "\n", "    writer.writerow(rows[0].keys())\n", "    for row in rows:\n", "        writer.writerow(row.values())\n", "\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
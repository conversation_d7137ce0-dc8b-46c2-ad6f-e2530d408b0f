{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The `post_process` step converts the task output into something executable. Since benchmarks vary,\n", "a custom `transform` method is used to forumlate the code for execution."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import sys\n", "sys.path.insert(0, \"../../../research/eval\")\n", "from generation.post_processing import post_process_human_eval as post_process, aggregate_results, process_results\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add in Temperature 0.2 results\n", "to_test = [\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/VP5Shf3b/ip-172-31-42-155_codegen-350M-multi_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/conan/VP5Shf3b/ip-172-31-42-155_conan-350M-96000_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/VP5Shf3b/ip-172-31-42-155_codegen-2B-multi_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/VP5Shf3b/ip-172-31-42-155_codegen-6B-multi_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/hmTuxudV/6c2c7c4936c4_codegen-16B-multi_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/conan/SAhy8MF2/ip-172-31-42-155_conan-2B-48000_human_eval.jsonl\",\n", "]\n", "to_test = [post_process(Path(x)) for x in to_test]\n", "results = aggregate_results(to_test, temperature=0.2)\n", "\n", "# add in Temperature 0.6 results\n", "to_test = [\n", "\"/mnt/efs/augment/user/rich/eval/projects/codegen/JFkeGxhD/ip-172-31-42-155_codegen-2B-multi_human_eval.jsonl\",\n", "\"/mnt/efs/augment/user/rich/eval/projects/codegen/JFkeGxhD/ip-172-31-42-155_codegen-6B-multi_human_eval.jsonl\",\n", "\"/mnt/efs/augment/user/rich/eval/projects/codegen/JFkeGxhD/ip-172-31-42-155_codegen-350M-multi_human_eval.jsonl\",\n", "\"/mnt/efs/augment/user/rich/eval/projects/conan/JFkeGxhD/ip-172-31-42-155_conan-350M-96000_human_eval.jsonl\",\n", "\"/mnt/efs/augment/user/rich/eval/projects/codegen/g5rR5vkL/6c2c7c4936c4_codegen-16B-multi_human_eval.jsonl\",\n", "\"/mnt/efs/augment/user/rich/eval/projects/conan/i7zwGRCa/ip-172-31-42-155_conan-2B-48000_human_eval.jsonl\",\n", "]\n", "to_test = [post_process(Path(x)) for x in to_test]\n", "results += aggregate_results(to_test, 0.6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# temperature 0.8\n", "to_test = [\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/ey6iZPEs/ip-172-31-42-155_codegen-2B-multi_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/ey6iZPEs/ip-172-31-42-155_codegen-350M-multi_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/conan/ey6iZPEs/ip-172-31-42-155_conan-350M-96000_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/ey6iZPEs/ip-172-31-42-155_codegen-6B-multi_human_eval.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/5P53E8tn/6c2c7c4936c4_codegen-16B-multi_human_eval.jsonl\",\n", "]\n", "to_test = [post_process(Path(x)) for x in to_test]\n", "results += aggregate_results(to_test, 0.8)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Running 200 samples for pass@100 stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_test = [\"/mnt/efs/augment/user/rich/eval/projects/codegen/Vi9vjGdk/ip-172-31-42-155_codegen-350M-multi_human_eval.jsonl\"]\n", "to_test = [post_process(Path(x)) for x in to_test]\n", "results_pass_100_350M = aggregate_results(to_test, 0.2)\n", "\n", "to_test = [\"/mnt/efs/augment/user/rich/eval/projects/codegen/mfcH7Rxj/ip-172-31-42-155_codegen-2B-multi_human_eval.jsonl\"]\n", "to_test = [post_process(Path(x)) for x in to_test]\n", "results_pass_100_6t = aggregate_results(to_test, 0.8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["process_results(results + results_pass_100_350M + results_pass_100_6t, title = \"HumanEval scales with model size\")"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "2d58e898dde0263bc564c6968b04150abacfd33eed9b19aaa8e45c040360e146"}}}, "nbformat": 4, "nbformat_minor": 2}
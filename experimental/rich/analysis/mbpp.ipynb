{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import sys\n", "sys.path.insert(0, \"../../../research/eval\")\n", "from research.eval.generation.post_processing import post_process_mbpp as post_process, aggregate_results, process_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_test_0t = [\n", "    \"/mnt/efs/augment/eval/jobs/HCXXUb9v/rich-eval2-dev_000_codegen-350M-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/eval/jobs/HCXXUb9v/rich-eval2-dev_002_codegen-6B-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/eval/jobs/HCXXUb9v/rich-eval2-dev_001_codegen-2B-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/eval/jobs/HCXXUb9v/rich-eval2-dev_003_codegen-16B-multi_mbpp.jsonl\",\n", "    ]\n", "to_test_0t = [post_process(Path(x)) for x in to_test_0t]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = aggregate_results(to_test_0t, 0.0)\n", "results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Running at pass@20, temperature 0.2\n", "to_test_2t =[\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/gYDcqAVq/ip-172-31-42-155_codegen-350M-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/conan/MUGRwDSh/ip-172-31-42-155_conan-350M-96000_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/h6xgxnLF/ip-172-31-42-155_codegen-2B-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/aFtMoKdX/ip-172-31-42-155_codegen-6B-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/UESAAtUC/ee2c361a1bd2_codegen-16B-multi_mbpp.jsonl\",\n", "    ]\n", "\n", "to_test_2t = [post_process(Path(x)) for x in to_test_2t]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["to_test_6t = [\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/FaAKMgMc/ip-172-31-42-155_codegen-350M-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/conan/9LDbEXBy/ip-172-31-42-155_conan-350M-96000_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/FaAKMgMc/ip-172-31-42-155_codegen-2B-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/FaAKMgMc/ip-172-31-42-155_codegen-6B-multi_mbpp.jsonl\",\n", "    \"/mnt/efs/augment/user/rich/eval/projects/codegen/FDTs7Pmh/ee2c361a1bd2_codegen-16B-multi_mbpp.jsonl\",\n", "]\n", "to_test_6t = [post_process(Path(x)) for x in to_test_6t]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# combine all results and plot\n", "results = aggregate_results(to_test_2t, 0.2) + aggregate_results(to_test_6t, 0.6)\n", "results\n", "process_results(results, title = \"MBPP scales with model size\")"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "2d58e898dde0263bc564c6968b04150abacfd33eed9b19aaa8e45c040360e146"}}}, "nbformat": 4, "nbformat_minor": 2}
# Chat Proto Client

A command-line client for interacting with <PERSON>ment's context aware chat.

## Overview

The application consists of two components:
- A Node.js application, `server.js`, that provides workspace synchronization and a simplified chat interface to Augment's service.
- A Python client, `chat_proto.py`, that launches the node application and provides a command-line interface for chat requests.

The `chat_proto.py` script is intended to be a reference client, and should be modified to fit your needs.

Note, `server.js`, implements a subset of the LSP protocol. It is not a full-fledged LSP server. It is a simplified interface for chat requests.

## Features

- Workspace synchronization with Aug<PERSON>'s service.
- Single-turn chat exchanges using code context.
- Sample prompt for analyzing a logfile
- Can be extended to multi-turn chat conversations.

## Installation

The application has the following dependencies:
- Node.js 22+
- Python 3.10+
- pylspclient 0.1.0+  `pip install pylspclient`
Earlier versions might actually work, but they are not tested.

Unpack the rubrik.tgz into a directory of your choice.
```bash
tar -xzf rubrik.tgz
```
This will create a directory called `rubrik` with the following structure:
```
rubrik/
├── chat_proto.py
├── README.md
└── server.js
```

## Getting Started

You'll want to identify a workspace folder to sync. This is the root of your
project. For example, my workspace folder is `~/src/augment`. This tells the
client to sync all files under that directory. If you don't want to pass this
path on the command line every time, you can also edit `chat_proto.py` and set
the `WORKSPACE_PATHS` variable to a list of paths to sync.

On first invocation, you'll need to include the `--signin` flag to authenticate.
For example:

`python3.11 chat_proto.py --signin --workspace ~/src/augment`

You do not need to authenticate every time you run the client.

Now that you're authenticated, the following should sync your workspace and give you a chat prompt:
`python chat_proto.py --workspace /path/to/your/workspace`

## Usage

```bash
# Authentication options
python chat_proto.py --signin    # Sign in to Augment
python chat_proto.py --signout   # Sign out from Augment

# Basic chat interaction
python chat_proto.py --workspace /path/to/your/workspace

# Process log files for error diagnosis
python chat_proto.py --logfile /path/to/logfile.log
```

The `--logfile` flag is a special mode. Instead of giving you a chat prompt,
the app will read the log file and send the content to the chat model. The
model will then attempt to diagnose the error and provide a course of action.
*This is intended to be modified to your needs, particularly the prompt.*


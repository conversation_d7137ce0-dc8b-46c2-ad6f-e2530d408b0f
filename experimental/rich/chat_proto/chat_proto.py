"""LSP client implementation for interacting with Augment's chat functionality.

This script provides a command-line interface to interact with Augment's LSP server
for chat-based code assistance. It handles:
- Starting and managing the LSP server process
- Establishing LSP client connection
- Workspace synchronization
- Sending chat requests with optional log file content
- Processing and displaying chat responses
- Optional chat history between sessions

The script can be run directly to start a single-turn chat exchange or process
log files for error diagnosis. It can also support multi-turn conversations by
maintaining chat history when explicitly enabled.

Depends on pylspclient +0.10

Usage:
    python chat_proto.py [--logfile PATH] [--chat MESSAGE] [--signin] [--signout] [--workspace PATH]
                         [--use-history] [--history-file PATH] [--clear-history]

Options:
    --workspace PATH      Directory containing the files to sync
    --logfile PATH        File containing the message to send
    --chat MESSAGE        Optional chat message from the cli
    --signin              Sign in to the Augment service
    --signout             Sign out of the Augment service
    --use-history         Enable chat history to maintain context between conversations
    --history-file PATH   File to store chat history (default: chat_history.json in script directory)
    --clear-history       Clear existing chat history file before starting a new conversation
"""

import argparse
import getpass
import os
import sys
import subprocess
import time
import json
from pathlib import Path
from dataclasses import dataclass, asdict, field
from typing import List

from pylspclient import JsonRpcEndpoint, LspClient, LspEndpoint
from pylspclient.lsp_pydantic_strcuts import (
    TextDocumentIdentifier,
    TextDocumentPositionParams,
    Position,
)


# Location of the server.js file.
def find_sidecar():
    for sc in [
        Path(__file__).parent / "server.js",
        Path(os.getcwd()) / "server.js",
        Path("~/server.js").expanduser(),
    ]:
        if sc.exists():
            return sc
    print("ERROR: Server not found in {os.getcwd()} or ~/server.js.", file=sys.stderr)
    raise FileNotFoundError("Server not found")


# Location of files in the same directory as this script
SCRIPT_DIR = Path(__file__).parent
SIDECAR_PATH = find_sidecar()
DEFAULT_HISTORY_FILE = SCRIPT_DIR / "chat_history.json"
MAX_HISTORY_EXCHANGES = 10  # Maximum number of exchanges to keep in history
WORKSPACE_PATHS = []  # should be absolute path to workspace folders


class LspProcess:
    """Start the LSP server process."""

    def __init__(self, server_path: Path):
        process = subprocess.Popen(
            ["node", server_path, "--stdio"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        # Give the server a moment to start
        time.sleep(0.1)

        if process.poll() is not None:
            assert process.stderr is not None
            err = process.stderr.read().decode()
            raise RuntimeError(f"Failed to start LSP server {err}")
        # print(f"Launch lsp server {process.pid}")
        self.process = process

    def __del__(self):
        if self.process is None:
            return
        # print(f"Terminate lsp server {self.process.pid}")
        self.process.terminate()
        self.process.wait()


def chatChunk(params):
    # Uncomment the following to stream the response
    # print(params["text"], end="", flush=True)
    pass


def lsp_client(
    lsp_process: subprocess.Popen, workspace_folders: list[dict], timeout=30
) -> LspClient:
    """Create an LSP client connected to the server."""
    json_rpc_endpoint = JsonRpcEndpoint(lsp_process.stdin, lsp_process.stdout)
    lsp_endpoint = LspEndpoint(
        json_rpc_endpoint, notify_callbacks={"augment/chatChunk": chatChunk}, timeout=30
    )

    lsp_client = LspClient(lsp_endpoint)

    lsp_client.initialize(
        processId=os.getpid(),
        rootPath=None,
        rootUri=None,
        initializationOptions={
            "editor": "augment-lsp",
            "pluginVersion": "0.0",
            "vimVersion": "0.1",
        },
        capabilities={},
        trace="off",
        workspaceFolders=workspace_folders,
    )
    lsp_client.initialized()

    return lsp_client


@dataclass
class Exchange:
    """Represents a single exchange between the user and the model."""

    request_message: str
    """The message sent by the user."""

    response_text: str
    """The response from the model."""

    request_id: str
    """The unique ID for this exchange."""


@dataclass
class ChatRequest:
    """A request to the chat model."""

    textDocumentPosition: TextDocumentPositionParams

    message: str
    """Current user message."""

    selectedText: str
    """The selected code as additional context."""

    history: List[Exchange] = field(default_factory=list)
    """Previous chat exchanges for context."""

    # partialResultToken: None = None
    """ Used for streaming, not yet implemented here. """


@dataclass
class ChatResponse:
    """A response from the chat model."""

    text: str
    """Chat response"""

    requestId: str
    """Request id for the chat request."""


def signin(client: LspClient):
    """Sign in to the Augment service."""

    # Check if we're already logged in
    status = client.lsp_endpoint.call_method("augment/status")
    logged_in = status["loggedIn"]
    if logged_in:
        print("Already signed in")
        return

    # login
    login_url = client.lsp_endpoint.call_method("augment/login")
    print(f"Login using the url:\n\n{login_url['url']}\n")
    code = getpass.getpass("Paste the authentication code: ")
    if not code:
        raise ValueError("No code provided")

    client.lsp_endpoint.call_method("augment/token", code=code)
    status = client.lsp_endpoint.call_method("augment/status")
    logged_in = status["loggedIn"]
    if not logged_in:
        raise RuntimeError("Failed to sign in")
    else:
        print("Sign in successful")


def signout(client: LspClient):
    """Sign out from the Augment service."""
    logout_ok = client.lsp_endpoint.call_method("augment/logout")
    if not logout_ok.get("success", False):
        raise RuntimeError("Failed to sign out")
    else:
        print("Signed out")


def load_chat_history(history_file: Path) -> List[Exchange]:
    """Load chat history from a file."""
    if not history_file.exists():
        return []

    try:
        history_data = json.loads(history_file.read_text())
        return [Exchange(**exchange) for exchange in history_data]
    except (json.JSONDecodeError, KeyError, OSError) as e:
        print(f"Warning: Could not load chat history: {e}")
        return []


def save_chat_history(history_file: Path, history: List[Exchange]):
    """Save chat history to a file."""
    # Ensure parent directory exists
    history_file.parent.mkdir(parents=True, exist_ok=True)
    # Write the history data
    history_file.write_text(
        json.dumps([asdict(exchange) for exchange in history], indent=2)
    )


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--workspace",
        type=str,
        action="append",
        default=[],
        help="Directory containing the files to sync (can be specified multiple times)",
    )
    chat_group = parser.add_mutually_exclusive_group(required=False)
    chat_group.add_argument(
        "--logfile", type=str, help="File containing the message to send"
    )
    chat_group.add_argument("--chat", type=str, help="Message to send")
    parser.add_argument(
        "--signin", action="store_true", help="Sign in to the Augment service"
    )
    parser.add_argument(
        "--signout", action="store_true", help="Sign out of the Augment service"
    )
    parser.add_argument(
        "--outfile", "-o", type=str, help="output filename, default stdout", default="-"
    )
    parser.add_argument(
        "--use-history",
        action="store_true",
        help="Enable chat history to maintain context between conversations",
    )
    parser.add_argument(
        "--history-file",
        type=str,
        default=str(DEFAULT_HISTORY_FILE),
        help=f"File to store chat history (default: {DEFAULT_HISTORY_FILE.name} in script directory)",
    )
    parser.add_argument(
        "--clear-history",
        action="store_true",
        help="Clear existing chat history file before starting a new conversation",
    )
    args = parser.parse_args()

    # Add workspace folder(s)
    if args.workspace:
        WORKSPACE_PATHS.extend([Path(path).resolve() for path in args.workspace])

    workspace_folders = [
        {"uri": f"file://{path}", "name": path.name} for path in WORKSPACE_PATHS
    ]
    if not WORKSPACE_PATHS:
        print("No workspace folders specified. Add workspace folders for context.")

    for idx, folders in enumerate(workspace_folders):
        if idx == 0:
            print("Using workspace folders:")
        print(folders)

    process = LspProcess(SIDECAR_PATH)
    client = lsp_client(process.process, workspace_folders, timeout=120)

    if args.signin:
        signin(client)
    elif args.signout:
        signout(client)
        return  # no need to continue if not authenticated.

    status = client.lsp_endpoint.call_method("augment/status")
    if not status or not status["loggedIn"]:
        print("Not logged in, you must authenticate to use the chat model.")
        exit(1)

    # Poll for sync to complete
    sync_complete = False
    while workspace_folders and not sync_complete:
        status = None
        try:
            status = client.lsp_endpoint.call_method("augment/status")
            assert status is not None
        except TimeoutError:
            print("Timeout waiting for sync status")
            time.sleep(5)
            continue
        logged_in = status["loggedIn"]
        sync_complete = status.get("syncPercentage", 0) == 100
        if not logged_in:
            print("Not logged in, you must authenticate to use the chat model.")
            exit(1)
        print(f"Syncing: {status.get('syncPercentage', 0)}%")
        time.sleep(1)

    # Handle chat history
    chat_history = []

    # Only process history if explicitly enabled
    history_file = Path(args.history_file)
    if args.use_history:
        if args.clear_history:
            # Clear the history file if it exists
            if history_file.exists():
                # Create an empty file
                history_file.write_text("[]")
                print(f"Cleared chat history in {history_file}")
        else:
            # Load existing history
            chat_history = load_chat_history(history_file)
            if chat_history:
                print(f"Loaded {len(chat_history)} previous exchanges from history")

                # Trim history to max length
                if len(chat_history) > MAX_HISTORY_EXCHANGES:
                    chat_history = chat_history[-MAX_HISTORY_EXCHANGES:]
                    print(f"Trimmed history to last {MAX_HISTORY_EXCHANGES} exchanges")

    # Build chat request
    logtext = ""
    if args.logfile:
        logfile_path = Path(args.logfile)
        if logfile_path.exists():
            logtext = logfile_path.read_text().strip()
        else:
            print(f"Warning: Logfile {args.logfile} not found")
        message = "Diagnose the error in the selected text and provide the next course of action. Find the root cause."
    else:
        if args.chat:
            message = args.chat
        else:
            print()
            message = input("Enter your message: ")

    chat_params = ChatRequest(
        textDocumentPosition=TextDocumentPositionParams(
            textDocument=TextDocumentIdentifier(uri=""),
            position=Position(line=0, character=0),
        ),
        message=message,
        selectedText=logtext,
        history=chat_history,
    )
    params = asdict(chat_params)

    response = ChatResponse(**client.lsp_endpoint.call_method("augment/chat", **params))
    response_out = f"\n{response.text}\nRequest ID: {response.requestId}\n"
    if args.outfile == "-":
        sys.stdout.write(response_out)
    else:
        with open(args.outfile, "w") as f:
            f.write(response_out)

    # Update history with this exchange if history is enabled
    if args.use_history:
        new_exchange = Exchange(
            request_message=message,
            response_text=response.text,
            request_id=response.requestId,
        )
        chat_history.append(new_exchange)
        save_chat_history(history_file, chat_history)
        print(f"Updated chat history ({len(chat_history)} exchanges)")


if __name__ == "__main__":
    main()

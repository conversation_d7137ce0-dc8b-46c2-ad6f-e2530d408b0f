{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Verify all patch_ids are unique"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "import json\n", "import os\n", "from pathlib import Path\n", "import yaml\n", "\n", "from research.eval.harness import harness\n", "\n", "def get_results_file(share: Path):\n", "    results_file = list(share.glob(\"*_hydra.jsonl\"))[0]\n", "    return results_file\n", "\n", "def get_results(results_file):\n", "    verify_recs = [json.loads(x) for x in results_file.read_text().splitlines()]\n", "    return {rec[\"patch_id\"]: rec[\"_extra\"][\"result\"] for rec in verify_recs}\n", "\n", "def read_jsonl_file(filename):\n", "    return [json.loads(x) for x in filename.read_text().splitlines()]\n", "\n", "def write_jsonl_file(recs, filename, overwrite=False):\n", "    exists = filename.exists()\n", "    if not exists or overwrite:\n", "        with filename.open(\"w\") as f:\n", "            for rec in recs:\n", "                json.dump(rec, f)\n", "                f.write(\"\\n\")\n", "    return exists\n", "\n", "def advance_symlink(symlink, from_file, to_file):\n", "    assert symlink.is_symlink()\n", "    if symlink.readlink() == from_file:\n", "        print(f\"Advancing symlink from {from_file} to {to_file}\")\n", "        symlink.unlink()\n", "        symlink.symlink_to(to_file)\n", "    else:\n", "        print(f\"Symlink doesn't point to expected file {from_file}\")\n", "        print(f\"Symlink resolves to: {symlink.readlink()}\")\n", "\n", "def run_hydra(config: dict, output_path):\n", "    if output_path.exists():\n", "        print(f\"Output directory {output_path} exists, will not run hydra.\")\n", "        return []\n", "\n", "    print(config)\n", "    h = harness.EvalHarness.from_config(config, output_path)\n", "    out = h.run()\n", "    print(out)\n", "    return out\n", "\n", "def filter_records(input_jsonl: Path, output_jsonl: Path, patch_ids: set):\n", "    assert len(patch_ids)\n", "\n", "    in_recs = read_jsonl_file(input_jsonl)\n", "    out_recs = [x for x in in_recs if x[\"patch_id\"] in patch_ids]\n", "\n", "    # If the null set doesn't already exist, write it\n", "    if not output_jsonl.exists():\n", "        write_jsonl_file(out_recs, output_jsonl)\n", "    else:\n", "        print(f\"File {output_jsonl} already exists.\")\n", "\n", "    test_recs = read_jsonl_file(output_jsonl)\n", "    test_ids = {x[\"patch_id\"] for x in test_recs}\n", "    if test_ids != patch_ids:\n", "        print(\"Found the following test_ids not in patch_ids: \", test_ids - patch_ids)\n", "        print(\"Found the following patch_ids not in test_ids: \", patch_ids - test_ids)\n", "\n", "    assert test_ids == patch_ids\n", "\n", "def select_patch_ids_by_result(result_path, result):\n", "    results = get_results(get_results_file(result_path))\n", "    counts = Counter(results.values())\n", "    print(counts)\n", "    assert sum(counts.values()) == len(results)\n", "\n", "    result_set = {k for k,v in results.items() if v == result}\n", "    assert len(result_set) == counts[result]\n", "    print(f\"Result set {len(result_set)}\")\n", "    return result_set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["org = \"mrdoob\"\n", "repo = \"three.js\"\n", "base_path = Path(\"/mnt/efs/augment/data/eval/hydra/datasets/all_languages_2-3lines.dev/\") / org\n", "sym_patch_file = base_path / f\"{repo}_patches.jsonl\"\n", "\n", "original_patch_file = base_path / f\"{repo}_patches_orig.jsonl\"\n", "assert_patch_file = base_path / f\"{repo}_patches_asserts.jsonl\"\n", "null_patch_file = base_path / f\"{repo}_patches_null.jsonl\"\n", "\n", "cwd = Path(os.getcwd())\n", "\n", "# Setup\n", "if True:\n", "    if sym_patch_file.exists():\n", "        assert sym_patch_file.is_symlink()\n", "        sym_patch_file.unlink()\n", "    sym_patch_file.symlink_to(original_patch_file)\n", "    assert_patch_file.unlink(missing_ok=True)\n", "    null_patch_file.unlink(missing_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verify all original patches are unique, as a sanity check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["original_recs = read_jsonl_file(original_patch_file)\n", "patch_ids = set([x[\"patch_id\"] for x in original_recs])\n", "assert len(patch_ids) == len(original_recs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run \"assert\" config on original patches, and analyze"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = yaml.safe_load(Path(\"assert.yml\").read_text())\n", "config[\"task\"][\"repos\"] = [f\"{org}/{repo}\"]\n", "result_path = cwd / f\"{repo}-assert\"\n", "run_hydra(config, result_path)\n", "\n", "fail_set = select_patch_ids_by_result(result_path, \"FAILED\")\n", "filter_records(original_patch_file, assert_patch_file, fail_set)\n", "advance_symlink(sym_patch_file, original_patch_file, assert_patch_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze running the \"null\" test on the assert-filtered patches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = yaml.safe_load(Path(\"null.yml\").read_text())\n", "config[\"task\"][\"repos\"] = [f\"{org}/{repo}\"]\n", "result_path = cwd / f\"{repo}-null\"\n", "run_hydra(config, result_path)\n", "\n", "fail_set = select_patch_ids_by_result(result_path, \"FAILED\")\n", "filter_records(assert_patch_file, null_patch_file, fail_set)\n", "advance_symlink(sym_patch_file, assert_patch_file, null_patch_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run hydra null test and ensure everything fails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = yaml.safe_load(Path(\"null.yml\").read_text())\n", "print(config)\n", "config[\"task\"][\"repos\"] = [f\"{org}/{repo}\"]\n", "result_path = cwd / f\"{repo}-null-validate\"\n", "run_hydra(config, result_path)\n", "\n", "# Verify that everything fails\n", "fail_set = select_patch_ids_by_result(result_path, \"FAILED\")\n", "results = get_results(get_results_file(result_path))\n", "assert len(results) == len(fail_set)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Run gold test and assert everything passes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = yaml.safe_load(Path(\"gold.yml\").read_text())\n", "print(config)\n", "config[\"task\"][\"repos\"] = [f\"{org}/{repo}\"]\n", "result_path = cwd / f\"{repo}-gold\"\n", "run_hydra(config, result_path)\n", "\n", "pass_set = select_patch_ids_by_result(result_path, \"PASSED\")\n", "results = get_results(get_results_file(result_path))\n", "assert len(results) == len(pass_set)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Filter out patches by filename.\n", "### This process is a bit manual "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter out those in the test directory\n", "import re\n", "\n", "recs = read_jsonl_file(sym_patch_file)\n", "all_filenames = {rec[\"file_name\"] for rec in recs}\n", "test_filenames = {rec[\"file_name\"] for rec in recs if re.match(\"test/*\", rec[\"file_name\"])}\n", "\n", "# calculate the patch ids for those patches that do not include test files\n", "src_filenames = all_filenames - test_filenames\n", "patch_ids = {rec[\"patch_id\"] for rec in recs if rec[\"file_name\"] in src_filenames}\n", "\n", "assert len(src_filenames) + len(test_filenames) == len(all_filenames)\n", "\n", "print(len(src_filenames))\n", "print(test_filenames)\n", "print(len(patch_ids))\n", "\n", "filename_patch_file = base_path / f\"{repo}_patches_no_test.jsonl\"\n", "filter_records(sym_patch_file, filename_patch_file, patch_ids)\n", "advance_symlink(sym_patch_file, null_patch_file, filename_patch_file)\n", "# \n", "# # Validate\n", "recs = read_jsonl_file(sym_patch_file)\n", "all_filenames = {rec[\"file_name\"] for rec in recs}\n", "test_filenames = {rec[\"file_name\"] for rec in recs if re.match(\"test/*\", rec[\"file_name\"])}\n", "\n", "print(test_filenames)\n", "print(len(recs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(all_filenames)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
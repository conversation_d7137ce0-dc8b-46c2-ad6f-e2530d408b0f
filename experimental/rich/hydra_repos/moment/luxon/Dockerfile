# Stage 1: Builder
FROM python:3.9 AS BUILDER

WORKDIR /code

# Copy in run script
RUN mkdir .hydra
COPY run.sh .hydra/run.sh
COPY full.diff .hydra/luxon.diff
COPY lang .hydra/lang
RUN chmod ugo+rwx .hydra/run.sh

# Install git repo and apply patch
ARG GIT_SHA=9575754a
RUN git clone https://github.com/moment/luxon.git
RUN cp -rT /code/luxon /code
RUN rm -fr /code/luxon/
RUN git checkout ${GIT_SHA}
RUN git apply .hydra/luxon.diff

RUN useradd -ms /bin/bash hydra && \
    usermod -aG sudo hydra && \
    echo 'myuser ALL=(ALL) NOPASSWD:ALL' >> /etc/sudoers

USER hydra

# RUN cd /code && \
#     pip install --no-cache-dir --user -r .hydra/requirements.txt

# Stage 2: Final image
# FROM python:3.9-slim
FROM node:19

WORKDIR /code

# RUN npm install # --ignore-scripts
# RUN ./scripts/test

# Install essential build tools and Go
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3.9 \
    sudo \
    ca-certificates \
    curl \
    git \
    && \
    rm -rf /var/lib/apt/lists/*
RUN ln -s /usr/bin/python3.9 /usr/bin/python


# Create a non-root user "hydra" and add "hydra" user to the sudoers list
RUN useradd -ms /bin/bash hydra
RUN usermod -aG sudo hydra

# Allow "hydra" user to use sudo without password
RUN echo "hydra ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers


USER hydra
# COPY --chown=hydra:hydra --from=BUILDER /home/<USER>/.local /home/<USER>/.local
COPY --chown=hydra:hydra --from=BUILDER /code /code
# ENV PATH="/home/<USER>/.local/bin:${PATH}"

RUN sudo chown hydra:hydra /code
RUN npm install
RUN npm install full-icu

# Some repos might download from HuggingFace, causing non-deterministic test results.
# We explicitly disallow them.
#
# As a result, some repos will have tests that fail by default.
# It is the responsibility of the person who adds new repos to ensure that the tests
# pass, e.g., by caching from HuggingFace manually, or by disabling the relevant tests.
# (normally, those tests that require HuggingFace are not important).
# ENV HF_DATASETS_OFFLINE=1
# ENV TRANSFORMERS_OFFLINE=1

# RUN npm install && npm install full-icu

ENV HUSKY_SKIP_INSTALL=1

ENV LANG=en_US.utf8
ENV LIMIT_JEST=yes
ENV CI=yes
ENV TZ=America/New_York

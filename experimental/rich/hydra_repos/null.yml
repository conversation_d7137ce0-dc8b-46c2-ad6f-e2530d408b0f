system:
  name: basic
  model:
    name: "null"
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 128

# Tasks
#   specify the evaluation tasks for each checkpoint
#
task:
  name: hydra
  dataset: all_languages_2-3lines.dev

podspec: gpu-small.yaml

determined:
  name: Hydra Functional Null Test
  workspace: Dev
  project: playground
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

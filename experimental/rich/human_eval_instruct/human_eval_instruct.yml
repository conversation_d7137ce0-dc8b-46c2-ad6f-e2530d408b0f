system:
    name: remote_chat

    # The model name is optional, will select default if not present
    # model_name: binks-llama3-70B-FP8-chatanol1-11-chat

    client:
        # Set a url to run the remote system.
        # url: https://dev-<USER>.us-central.api.augmentcode.com
        url: https://dogfood.api.augmentcode.com

task:
    name: humaneval_instruct
    # limit: 40
    variant: synthetic

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: HumanEvalInstruct, retrieval, default model, remote, dogfood
  project: rich
  workspace: Dev

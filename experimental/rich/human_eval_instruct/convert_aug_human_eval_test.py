from experimental.rich.human_eval_instruct.convert_aug_human_eval import (
    TEMPLATE,
    extract_signature_docstring,
)


def test_extract_signature_docstring():
    prompt = 'def is_prime(num: int) -> bool:\n    """Return True if the input number is prime, else return False.\n    A prime number is a natural number greater than 1 that has no\n    positive divisors other than 1 and itself.\n\n    Examples:\n    >>> is_prime(2)\n    True\n    >>> is_prime(11)\n    True\n    >>> is_prime(15)\n    False\n    """\n'
    expected_signature = "is_prime(num: int) -> bool"
    expected_docstring = "Return True if the input number is prime, else return False.\nA prime number is a natural number greater than 1 that has no\npositive divisors other than 1 and itself.\n\nExamples:\n>>> is_prime(2)\nTrue\n>>> is_prime(11)\nTrue\n>>> is_prime(15)\nFalse"
    result = extract_signature_docstring(prompt)
    assert result.signature == expected_signature
    assert result.docstring == expected_docstring


def test_extract_signature_docstring_no_docstring():
    prompt = "def is_prime(num: int) -> bool:\n"
    expected_signature = "is_prime(num: int) -> bool"
    expected_docstring = ""
    result = extract_signature_docstring(prompt)
    assert result.signature == expected_signature
    assert result.docstring == expected_docstring


def test_template_formatting():
    signature = "foo: bar"
    docstring = "This is a docstring"
    expected_instruction = (
        "Write a function foo: bar to solve the following problem:\nThis is a docstring"
    )
    instruction = TEMPLATE.format(signature=signature, docstring=docstring)
    assert instruction == expected_instruction

"""Convert the aug_human_eval dataset to the human_eval_instruct format."""

import re
from dataclasses import dataclass
from pathlib import Path

from research.eval.harness.utils import read_jsonl_zst, write_jsonl_zst

# <PERSON>'s synthetic dataset
DATASET = Path("/mnt/efs/augment/data/processed/aug_human_eval.v0/0-shot.jsonl.zst")

# The template for the human_eval_instruct dataset
TEMPLATE = "Write a function {signature} to solve the following problem:\n{docstring}"

# Example prompt
# prompt = 'def is_prime(num: int) -> bool:\n    """Return True if the input number is prime, else return False.\n    A prime number is a natural number greater than 1 that has no\n    positive divisors other than 1 and itself.\n\n    Examples:\n    >>> is_prime(2)\n    True\n    >>> is_prime(11)\n    True\n    >>> is_prime(15)\n    False\n    """\n'


@dataclass
class PromptFields:
    signature: str
    docstring: str
    context: str


def extract_signature_docstring(prompt):
    # Extract signature
    signature_match = re.match(r"def (.+):", prompt)
    if signature_match:
        signature = signature_match.group(1)
    else:
        signature = ""

    # Extract docstring
    docstring_match = re.search(r'"""(.*)"""', prompt, re.DOTALL)
    if docstring_match:
        docstring = docstring_match.group(1)
    else:
        docstring = ""

    # Extract remaining context
    remaining_context = prompt.replace(f'"""{docstring}"""', "")

    docstring = docstring.strip()
    new_docstring = "\n".join([x.strip() for x in docstring.splitlines()])
    return PromptFields(signature, new_docstring, remaining_context)


def main():
    a_recs = read_jsonl_zst(DATASET)
    print(a_recs[0].keys())
    new_recs = []
    for rec in a_recs:
        p = extract_signature_docstring(rec["prompt"])
        instruction = TEMPLATE.format(signature=p.signature, docstring=p.docstring)
        new_rec = rec.copy()
        new_rec["instruction"] = instruction
        new_rec["signature"] = p.signature
        new_rec["docstring"] = p.docstring
        new_rec["context"] = p.context
        new_recs.append(new_rec)

    write_jsonl_zst(Path("aug_human_eval_instruct.jsonl.zst"), new_recs)


if __name__ == "__main__":
    main()

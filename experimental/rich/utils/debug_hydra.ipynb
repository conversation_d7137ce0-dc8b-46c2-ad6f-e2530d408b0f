{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "from collections import defaultdict\n", "\n", "import research.eval.harness.utils as utils\n", "from research.eval.harness.tasks.hydra_task import PatchWithModelOutput\n", "from research.eval.patch_lib import Patch\n", "\n", "# Indiana 2B run\n", "new_share = Path(\"/mnt/efs/augment/eval/jobs/kePjqPHU/\")\n", "# Indiana 16B run\n", "base_share = Path(\"/mnt/efs/augment/eval/jobs/D2PkR2FH/\")\n", "\n", "def hydra_exec(share: Path):\n", "    \"\"\"Parse output of hydra.\"\"\"\n", "    matches = list(share.glob(\"*_hydra.jsonl\"))\n", "    assert len(matches) <= 1\n", "    return matches[0] if matches else None\n", "\n", "def hydra_prompts(share: Path):\n", "    \"\"\"New style artifact.\"\"\"\n", "    matches = list(share.glob(\"*_patches.jsonl.zst\"))\n", "    assert len(matches) == 1\n", "    return matches[0]\n", "\n", "# Find new artifacts\n", "new_exec = hydra_exec(new_share)\n", "new_prompt = hydra_prompts(new_share)\n", "\n", "# Find baseline artifacts\n", "base_exec = hydra_exec(base_share)\n", "base_prompt = hydra_prompts(base_share)\n", "\n", "def calc_pass_fail(hydra_run: Path):\n", "    \"\"\"Given a hydra run, calculate the pass/fail by task.\"\"\"\n", "    f = hydra_run.open(\"r\")\n", "    pass_fail = {}\n", "    recs = [json.loads(x) for x in f]\n", "    for x in recs:\n", "        pass_fail[x[\"patch_id\"]] = x[\"_extra\"][\"result\"]\n", "    return pass_fail\n", "\n", "def join_artifacts(hydra_run, patch_file):\n", "    \"\"\"Given the input patches and the hydra result, aggregate into a per task table.\"\"\"\n", "    pass_fail = calc_pass_fail(hydra_run)\n", "\n", "    hydra_results = [json.loads(x) for x in hydra_run.read_text().splitlines()]\n", "    input_patches = utils.read_jsonl_zst(patch_file)\n", "\n", "    stats = defaultdict(dict)\n", "    for patch_rec, exec_result in zip(input_patches, hydra_results):\n", "\n", "        # Re-hydrate the patch record from the json decoded object\n", "        pr = PatchWithModelOutput.from_dict(patch_rec)\n", "\n", "        # validate the completion is what was sent to hydra driver\n", "        assert pr.completion == exec_result[\"patch_content\"]\n", "        task_id = exec_result[\"patch_id\"] \n", "        stats[task_id][\"prompt\"] = pr.prompt\n", "        stats[task_id][\"generation\"] = pr.generation\n", "        stats[task_id][\"completion\"] = pr.completion\n", "        gt_range = slice(exec_result[\"char_start\"], exec_result[\"char_end\"])\n", "        stats[task_id][\"ground_truth\"] = exec_result[\"file_content\"][gt_range]\n", "        stats[task_id][\"score\"] = pass_fail[task_id]\n", "        # This could be converted i\n", "        stats[task_id][\"chunks\"] = pr.filtered_chunks\n", "        # patch = Patch(**a[\"patch\"])\n", "        stats[task_id][\"filename\"] = pr.patch.file_name\n", "        stats[task_id][\"range\"] = (pr.patch.char_start, pr.patch.char_end)\n", "        stats[task_id][\"file_content\"] = pr.patch.file_content\n", "    return stats\n", "\n", "# Create a unified view of the data for both baseline and new runs\n", "new_res = join_artifacts(new_exec, new_prompt)\n", "base_res = join_artifacts(base_exec, base_prompt)\n", "\n", "print(\"Mismatch tasks\")\n", "for x in new_res:\n", "    if new_res[x][\"prompt\"] != base_res[x][\"prompt\"]:\n", "        print(x)\n", "\n", "task = \"CarperAI_trlx/2\"\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from dataclasses import dataclass\n", "from pathlib import Path\n", "\n", "from megatron.tokenizer import tokenizer\n", "\n", "@dataclass\n", "class Args:\n", "    \"\"\"Arguments to tokenizer.\"\"\"\n", "\n", "    rank: int = 0\n", "    tokenizer_type: str = \"CodeGenTokenizer\"\n", "    padded_vocab_size: int = -1\n", "    model_parallel_size: int = 1\n", "    make_vocab_size_divisible_by: int = 51200\n", "\n", "\n", "def make_tokenizer():\n", "    token_args = Args()\n", "    token = tokenizer.build_tokenizer(token_args)\n", "    return token\n", "\n", "def find_rightmost_index(input_list, element):\n", "    try:\n", "        index = len(input_list) - input_list[::-1].index(element) - 1\n", "        return index\n", "    except ValueError:\n", "        return -1  # Return -1 if the element is not found in the list\n", "\n", "which_res = base_res\n", "print(len(which_res[task][\"prompt\"].splitlines(True)))\n", "token = make_tokenizer()\n", "tokens = token.tokenize(which_res[task][\"prompt\"])\n", "eoc = token.vocab[\"<|ret-endofdoc|>\"]\n", "idx = find_rightmost_index(tokens, eoc)\n", "print(\"Budget: \", 2048-280)\n", "print(\"All tokens: \", len(tokens))\n", "print(\"Index of last chunk\", idx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import HTML\n", "import difflib\n", "ht = difflib.HtmlDiff(tabsize=4)\n", "table = ht.make_table(new_res[task][\"prompt\"].splitlines(True), base_res[task][\"prompt\"].splitlines(True))\n", "HTML(table, )"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
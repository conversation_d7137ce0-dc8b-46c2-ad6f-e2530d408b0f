{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Validate conversion from pickled artifacts to jsonl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pickle\n", "import research.eval.harness.utils as utils\n", "from research.eval.harness.tasks.hydra_task import PatchWithModelOutput\n", "\n", "def compare(base, verify):\n", "    print(f\"Comparing {base} to {verify}\")\n", "    jsonl = utils.read_jsonl_zst(verify)\n", "    with base.open(\"rb\") as infile:\n", "        baseline = pickle.load(infile)\n", "    for x, y in zip(baseline, jsonl):\n", "        pr = PatchWithModelOutput.from_dict(y)\n", "        assert x[\"prompt\"] == pr.prompt\n", "        assert x[\"generation\"] == pr.generation\n", "        assert x[\"completion\"] == pr.completion\n", "        assert x[\"patch\"] == pr.patch\n", "        assert x[\"prefix\"] == pr.prefix\n", "        if \"suffix\" in x:\n", "            assert x[\"suffix\"] == pr.suffix\n", "        else:\n", "            assert pr.suffix == \"\"\n", "        if \"filtered_chunks\" in x:\n", "            for chunk_b, chunk_v in zip(x[\"filtered_chunks\"], pr.filtered_chunks):\n", "                chunk_b.parent_doc.meta = {}\n", "                assert chunk_b == chunk_v, f\"{chunk_b}\\n{chunk_v}\"\n", "\n", "to_convert = Path(\"pkl_to_convert.txt\").read_text()\n", "files_to_convert = to_convert.split()\n", "for file in files_to_convert:\n", "    file = Path(file)\n", "    if not file.exists():\n", "        # removed some by hand\n", "        continue\n", "    print(f\"Searching {file.parent}\")\n", "    jsonl_file = list(file.parent.glob(\"*_patches.jsonl.zst\"))\n", "    assert len(jsonl_file) == 1\n", "    compare(file, jsonl_file[0])\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
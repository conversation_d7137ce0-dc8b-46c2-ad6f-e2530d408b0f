{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.augment_client.client import AugmentClient, Exchange\n", "from pathlib import Path\n", "import logging\n", "from urllib.parse import parse_qsl\n", "import uuid\n", "\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", ")\n", "\n", "url = \"https://dev-rich.us-central.api.augmentcode.com\"\n", "token = Path(\"/home/<USER>/.config/augment/api_token\").read_text().strip()\n", "a = AugmentClient(url, token)\n", "print(a)\n", "\n", "\n", "def regress_test():\n", "    # a.find_missing(\"\", [\"test\"])\n", "    exchange = [\n", "        Exchange(request_message=\"3\", response_text=\"test\", request_id=uuid.uuid4()),\n", "        Exchange(\n", "            request_message=\"What is your favorite color?\",\n", "            response_text=\"Blue. No, yellow\",\n", "            request_id=uuid.uuid4(),\n", "        ),\n", "    ]\n", "    title = \"Test\"\n", "    chat_id = a.save_chat(\n", "        conversation_id=\"test-conversation\", chat_exchange=exchange, title=title\n", "    )\n", "    assert chat_id\n", "    res = a.get_chat(chat_id)\n", "    assert len(res.chat) == len(exchange)\n", "    print(res.uuid)\n", "    print(res.chat)\n", "    print(exchange)\n", "    assert res.chat[0] == exchange[0]\n", "    assert res.uuid == chat_id\n", "    assert res.title == title\n", "\n", "    # XXX Re-enable after the demo\n", "    dup_chat_id = a.save_chat(\n", "        conversation_id=\"test-conversation\", chat_exchange=exchange, title=\"Test\"\n", "    )\n", "    assert dup_chat_id == chat_id\n", "\n", "\n", "regress_test()\n", "\n", "\n", "def display_chat(url):\n", "    uid = url.split(\"/share/\", 1)[-1]\n", "    # args = {x: y for x, y in parse_qsl(rec)}\n", "    # uid = args[\"uuid\"]\n", "    res = a.get_chat(uid)\n", "    assert res.uuid == uid\n", "    print(f\"UUID: {res.uuid}\")\n", "    print(f\"Date: {res.date}\")\n", "    print(f\"Author: {res.user}\")\n", "    print(f\"Title: {res.title}\")\n", "    print(f\"Conversation ID: {res.conversation_id}\")\n", "    print()\n", "    payload_size = sum(len(x.request_message) + len(x.response_text) for x in res.chat)\n", "    print(payload_size)\n", "    for idx, exchange in enumerate(res.chat):\n", "        print(idx, exchange.request_id)\n", "        print(exchange.request_message)\n", "        print(\"-\" * 80)\n", "        print(exchange.response_text)\n", "        print()\n", "\n", "\n", "recs = [\n", "    # \"https://app.augmentcode.com/share/1kqSzcwJ6HE\"\n", "]\n", "for rec in recs:\n", "    display_chat(rec)\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
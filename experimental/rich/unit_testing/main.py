from base.augment_client.client import AugmentClient, AugmentModelClient
from base.augment_client.client import UploadContent
from pathlib import Path

# Set up the Augment client
client = AugmentClient(
    url="https://dogfood.api.augmentcode.com/",
    token="",
    user_agent="augment.info-chat/0",
)
model_client = AugmentModelClient(client, model_name="")

# Set the root directory to walk
root_dir = Path("/home/<USER>/src/augment-origin")


def upload_content(root_dir: Path):
    # Walk the directory tree
    blobs_to_upload: list[UploadContent] = []
    # Walk the directory tree
    for file_path in root_dir.rglob("*.py"):
        file_name = file_path.relative_to(root_dir)
        if not file_name.parts[0] == "research":
            print(f"Skipping {file_name.parts[0]}")
            continue
        # Create a blob to upload
        blob_content = file_path.read_text(encoding="utf-8")
        blobs_to_upload.append(UploadContent(blob_content, str(file_name)))
        print(f"Uploaded {file_name} as blob {file_name}")

    # Upload the blobs in batch
    blob_names = []
    for idx in range(0, len(blobs_to_upload), 100):
        print(f"Uploading {idx} to {idx + 100}")
        upload = client.batch_upload(blobs_to_upload[idx : idx + 100])
        _response = client.find_missing("", upload)
        blob_names.extend(upload)

    # blob_names = client.batch_upload(blobs_to_upload)
    # response = client.find_missing("", blob_names)
    print("response:", len(blob_names))
    return blob_names


prompts = {
    # "extend_test": "Here is a Kotlin unit test class: {existing_test_class}. Write an extended version of the test class that includes additional tests to cover some extra corner cases.",
    # "extend_coverage": "Here is a Kotlin unit test class and the class that it tests: {existing_test_class} {class_under_test}. Write an extended version of the test class that includes additional unit tests that will increase the test coverage of the class under test.",
    # "corner_cases": "Here is a Kotlin unit test class and the class that it tests: {existing_test_class} {class_under_test}. Write an extended version of the test class that includes additional unit tests that will cover corner cases missed by the original and will increase the test coverage of the class under test.",
    # "statement_to_complete": "Here is a Kotlin class under test {class_under_test} This class under test can be tested with this Kotlin unit test class {existing_test_class}. Here is an extended version of the unit test class that includes additional unit test cases that will cover methods, edge cases, corner cases, and other features of the class under test that were missed by the original unit test class:",
    "extend_test": "Here is a Python unit test file: {existing_test_class}. Write an extended version of the test class that includes additional tests to cover some extra corner cases.",
    "extend_coverage": "Here is a Python unit test class and the class that it tests: {existing_test_class} {class_under_test}. Write an extended version of the test class that includes additional unit tests that will increase the test coverage of the class under test.",
    "corner_cases": "Here is a Python unit test class and the class that it tests: {existing_test_class} {class_under_test}. Write an extended version of the test class that includes additional unit tests that will cover corner cases missed by the original and will increase the test coverage of the class under test.",
    "statement_to_complete": "Here is a Python class under test {class_under_test} This class under test can be tested with this Python unit test class {existing_test_class}. Here is an extended version of the unit test class that includes additional unit test cases that will cover methods, edge cases, corner cases, and other features of the class under test that were missed by the original unit test class:",
}


def query_chat(root_dir: Path, blob_names):
    for file_path in root_dir.rglob("test_*.py"):
        file_name = file_path.relative_to(root_dir)
        if not file_name.parts[0] == "research" or not file_name.parts[1] == "core":
            # print(f"Skipping {file_name}")
            continue

        if not file_name.parts[3] == "test_types.py":
            print(f"Skipping {file_name}")
            continue
        else:
            class_under_test = Path(file_path.parent.parent / "types.py").read_text(
                encoding="utf-8"
            )

        unit_test_code = file_path.read_text(encoding="utf-8")
        if len(unit_test_code) > 48 * 1024:
            print(f"Skipping {file_name} because it is too long: {len(unit_test_code)}")
            continue

        for k in prompts:
            print(f"Querying {file_name} with key {k}: {len(unit_test_code)}")
            prompt = prompts[k]
            try:
                response = model_client.chat(
                    selected_code=unit_test_code,
                    message=prompt.format(
                        existing_test_class=file_name, class_under_test=class_under_test
                    ),
                    prefix="",
                    suffix="",
                    path=str(file_name),
                    blob_names=blob_names,
                )
                print(response.text)
            except Exception as e:
                print(f"Error: {e}")


if __name__ == "__main__":
    blob_names = upload_content(root_dir)
    # print("Querying without blob names")
    # query_chat(root_dir, [])
    print("Querying with blob names")
    query_chat(root_dir, blob_names)

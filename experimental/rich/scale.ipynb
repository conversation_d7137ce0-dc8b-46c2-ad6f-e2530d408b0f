{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Scaling report for baseline codegen models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../../models/eval\")\n", "import evaldb\n", "db = evaldb.load()\n", "db.latest()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scaling by polycoder task\n", "import pandas as pd\n", "models = [\"codegen-350M-multi\", \"codegen-2B-multi\", \"codegen-6B-multi\", \"codegen-16B-multi\"]\n", "tasks = evaldb.PolycoderTasks\n", "db.use_metric(tasks, \"token_perplexity\")\n", "# select the model names for this report\n", "myview = db.data.loc[db.data[\"model_name\"].isin(models)]\n", "\n", "# Ignore the memory flag for these models, and just take either result,\n", "# since memory doesn't affect the performance of the baseline models.\n", "myview = pd.DataFrame(myview[[\"params_non_embed\"]+ tasks].groupby(\"params_non_embed\").last())\n", "myview = myview.reset_index()\n", "\n", "# Convert token_perplexity into loss\n", "import math\n", "for task in evaldb.PolycoderTasks:\n", "    myview[task] = myview[task].apply(lambda rec: math.log(rec))\n", "\n", "# Melt the fields into a long form in preparation for converting back to a wide form,\n", "# which the plotting library likes.\n", "myview = myview.melt(id_vars=[\"params_non_embed\"])\n", "myview = myview.rename(columns={'variable': 'task', 'value':\"loss\"})\n", "myview = myview.pivot(\"params_non_embed\", \"task\", \"loss\")\n", "myview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import seaborn\n", "plt = seaborn.lineplot(data=myview, markers=True)\n", "plt.set(xscale=\"log\", yscale=\"log\", title=\"Scaling for baseline codegen models\", xlabel=\"parameters\", ylabel=\"loss (per-token)\")\n", "seaborn.move_legend(plt, \"upper left\", bbox_to_anchor=(1, 1))"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "2d58e898dde0263bc564c6968b04150abacfd33eed9b19aaa8e45c040360e146"}}}, "nbformat": 4, "nbformat_minor": 2}
--- /dev/null	2023-04-22 05:46:58.463999981 +0000
+++ function_level/repos/leopard-ai_betty/tox.ini	2023-05-08 22:36:39.246121986 +0000
@@ -0,0 +1,11 @@
+[tox]
+envlist = py39
+
+[testenv]
+deps =
+  pytest
+  scikit-learn
+commands =
+  pip install -e .
+  pytest -x
+
--- tmp/leopard-ai_betty/pyproject.toml	2023-03-09 15:15:12.000000000 +0000
+++ function_level/repos/leopard-ai_betty/pyproject.toml	2023-05-08 22:34:47.757476494 +0000
@@ -13,8 +13,8 @@
 requires-python = ">=3.6,<3.11"

 dependencies = [
-    "torch>=1.8.*",
-    "numpy>=1.9.*",
+    "torch>=1.8.0",
+    "numpy>=1.9.0",
 ]

 [tool.flit.module]

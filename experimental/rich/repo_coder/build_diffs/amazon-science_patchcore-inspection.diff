--- function_level/repos/amazon-science_patchcore-inspection/tox.ini	2023-03-03 10:28:22.000000000 +0000
+++ function_level.bak/repos/amazon-science_patchcore-inspection/tox.ini	2023-05-09 04:47:08.413914246 +0000
@@ -7,8 +7,9 @@
 allowlist_externals = /bin/sh
 deps =
   -r requirements_dev.txt
+  timm
 commands =
-  pytest --junitxml=junit-generator-{envname}.xml -q --log-level ERROR {toxinidir}/test
+  pytest -x --junitxml=junit-generator-{envname}.xml -q --log-level ERROR {toxinidir}/test

 [testenv:flake8]
 deps = flake8

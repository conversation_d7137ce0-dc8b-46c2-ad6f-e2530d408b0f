from research.retrieval.scorers.dense_scorer import Den<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>corer
from research.retrieval.scorers.scoring_interface import register_scorer
import functools

from typing import Optional, Collection, Mapping
from research.core.abstract_prompt_formatter import Abstract<PERSON>rompt<PERSON>ormatter
from research.retrieval.chunk_formatters import <PERSON>bstract<PERSON>hunkFormatter
from research.core.types import Chunk

from research.retrieval.types import DocumentId
from research.retrieval.types import RetrievalScore
from research.core.model_input import ModelInput
from research.retrieval.types import ChunkId
from research.core.artifacts import post_artifact
from experimental.rich2.embeddings_cache import EmbeddingCache
from research.retrieval.scorers.dense_scorer import DocDenseScoringData

import numpy as np


@register_scorer("cached_embeddings")
class CachedEmbeddingsScorer(DenseRetrievalScorer):
    """A general purpose neox dense retriever configured from it's checkpoint."""

    def __init__(self, db_path: str, **args):
        self.cache = EmbeddingCache(db_path)
        self.scoring_data: dict[str, DocDenseScoringData] = {}

    def load(self):
        """Load the model."""
        pass

    def unload(self):
        """Unload the model and free GPU memory."""
        pass

    def _get_view_of_embeddings(
        self,
        doc_ids: Collection[DocumentId],
    ) -> tuple[list[tuple[DocumentId, ChunkId]], np.array]:  # type: ignore
        """This groups all embedding arrays across documents, for querying."""
        ids = list(
            (doc_id, chunk_id)
            for doc_id, scoring_data in self.scoring_data.items()
            if doc_id in doc_ids
            for chunk_id in scoring_data.chunk_ids
        )

        @functools.lru_cache(maxsize=1)
        def _get_cached_embedding_view(_ids: tuple):
            del _ids
            return np.concatenate(
                [
                    scoring_data.emb_arr
                    for doc_id, scoring_data in self.scoring_data.items()
                    if doc_id in doc_ids
                ]
            )

        embedding_view = _get_cached_embedding_view(tuple(ids))

        assert len(ids) == len(embedding_view), (len(ids), len(embedding_view))

        return ids, embedding_view

    def add_doc(self, chunks: list[Chunk]) -> None:
        """Compute and store embeddings for all chunks in a doc."""
        if len(chunks) == 0:
            return

        doc = chunks[0].parent_doc
        for chunk in chunks:
            assert chunk.parent_doc.id == doc.id, (chunk.parent_doc.id, doc.id)
        # for now, allow large files
        # assert len(doc.text) < 128*1024

        emb_array = []
        for chunk in chunks:
            chunk_text = chunk.text[:8192]
            emb = self.cache.get(chunk_text)
            if emb is None:
                if len(chunk.text) > 8192:
                    print("Truncating original text", chunk.text)
                if chunk.text != chunk_text:
                    print(f" chunk text mismatch:\n{chunk.text}\n{chunk_text}")
                else:
                    print(" chunk text matches")
                    print("-" * 30)
                    print(chunk.text)
                    print("-" * 30)

            assert emb is not None
            emb_array.append(emb)
        emb_arr = np.array(emb_array)

        self.scoring_data[doc.id] = DocDenseScoringData(
            chunk_ids=[chunk.id for chunk in chunks],
            emb_arr=emb_arr,
        )

        post_artifact(
            {
                "doc": doc.path,
                # XXX it's possible the chunk text is truncated
                "chunks": [chunk.text for chunk in chunks],
            }
        )

    def add_docs(self, doc_chunks: Mapping[DocumentId, list[Chunk]]) -> None:
        """Hook to add multiple documents."""
        for chunks in doc_chunks.values():
            self.add_doc(chunks)

    def remove_doc(self, chunks: list[Chunk]) -> None:
        """Remove embeddings for a doc."""
        if len(chunks) == 0:
            return

        doc = chunks[0].parent_doc
        for chunk in chunks:
            assert chunk.parent_doc.id == doc.id, (chunk.parent_doc.id, doc.id)

        del self.scoring_data[doc.id]

    def remove_all_docs(self) -> None:
        """Remove all docs."""
        self.scoring_data = {}

    def score(
        self, model_input: ModelInput, doc_ids: Collection[DocumentId] | None = None
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Score the corpus of chunks against a retrieval query."""
        if len(self.scoring_data) == 0:
            return [], []

        query_text = "".join(model_input.prefix.splitlines(keepends=True)[-30:])
        query_text = query_text[-8192:]
        emb_float = self.cache.get(query_text)
        assert emb_float is not None
        query_emb = np.array(emb_float)

        doc_ids = set(doc_ids) if doc_ids is not None else self.scoring_data.keys()
        embedding_ids, key_embeddings = self._get_view_of_embeddings(doc_ids)
        similarity_scores = np.matmul(key_embeddings, query_emb).flatten().tolist()
        assert len(similarity_scores) == key_embeddings.shape[0], (
            len(similarity_scores),
            key_embeddings.shape,
        )

        post_artifact({"retriever_prompt": query_text})

        return embedding_ids, similarity_scores

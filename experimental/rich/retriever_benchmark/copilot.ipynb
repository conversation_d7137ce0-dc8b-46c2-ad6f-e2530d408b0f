{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.insert(0, \"/home/<USER>/src/augment-origin/research/gpt-neox\")\n", "sys.path.insert(0, \"/home/<USER>/src/augment-origin\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval.harness.metrics import compute_exact_match\n", "from research.eval.harness.tasks.cceval import postprocess_code_lines\n", "from research.eval.harness.tasks.cceval import CCEval\n", "from collections import Counter\n", "from dataclasses import dataclass\n", "\n", "base = Path(\"/mnt/efs/augment/user/marcmac/CCEval/results/copilot\")\n", "base = Path(\"/mnt/efs/augment/user/marcmac/CCEval/results_20240706\")\n", "base = Path(\"/mnt/efs/augment/user/marcmac/CCEval/results_20240707\")\n", "base = Path(\n", "    \"/mnt/efs/augment/public_html/marcmac/cceval_scripting/raw_results/cceval_results/\"\n", ")\n", "\n", "augment_single = base / \"augment-results-open-none\"\n", "augment_multi = base / \"augment-results-multi\"\n", "\n", "# dir structure\n", "# repo-org\n", "# task_prefix/task_id\n", "# path-to-completion\n", "#   * filename.[completed|prompt_gt_rc|prompt_rc],\n", "#   * completion.txt,\n", "#   * ground_truth\n", "\n", "# Project/\n", "# Format of directory structure\n", "# base/repo/taskid/taskid/..filepath../[ground_truth|filename|filename.gt]\n", "\"\"\"\n", "/mnt/efs/augment/user/marcmac/CCEval/results/copilot/RTIInternational/project_cc_python/5633\n", "/mnt/efs/augment/user/marcmac/CCEval/results/copilot/RTIInternational/project_cc_python/5633/src\n", "/mnt/efs/augment/user/marcmac/CCEval/results/copilot/RTIInternational/project_cc_python/5633/src/teehr\n", "/mnt/efs/augment/user/marcmac/CCEval/results/copilot/RTIInternational/project_cc_python/5633/src/teehr/loading\n", "/mnt/efs/augment/user/marcmac/CCEval/results/copilot/RTIInternational/project_cc_python/5633/src/teehr/loading/ground_truth\n", "/mnt/efs/augment/user/marcmac/CCEval/results/copilot/RTIInternational/project_cc_python/5633/src/teehr/loading/nwm21_retrospective.py\n", "/mnt/efs/augment/user/marcmac/CCEval/results/copilot/RTIInternational/project_cc_python/5633/src/teehr/loading/nwm21_retrospective.py.\n", "\"\"\"\n", "\n", "cceval = CCEval(limit=4000)\n", "task_items = {mi.extra[\"patch_id\"]: mi for mi, _ in cceval}\n", "count = 0\n", "all_max = []\n", "for mi, docs in cceval:\n", "    for doc in docs:\n", "        if len(doc.text) >= 128 * 1024:\n", "            all_max.append(doc.path)\n", "# for x in all_max:\n", "#     print(x)\n", "# assert False\n", "\n", "\n", "def calc_stats(base: Path):\n", "    stats = Counter()\n", "    completions = {}\n", "    targets = {}\n", "    does_not_match_gt = []\n", "    for path in base.glob(\"**/*.completed\"):\n", "        d = path.parent\n", "        d_rel = d.relative_to(base)\n", "        original_file = (d / path.name).read_text()\n", "\n", "        # print(d_rel.parts)\n", "        task_id = \"/\".join(d_rel.parts[1:3])\n", "        task_id_key = \"/\".join(d_rel.parts[0:3])\n", "        lang = task_id.split(\"project_cc_\")[1].split(\"/\")[0]\n", "        # print(task_id)\n", "        if task_id in task_items:\n", "            # print(f\"Found {task_id}\")\n", "            prefix = task_items[task_id].prefix\n", "            suffix = task_items[task_id].suffix\n", "            target = task_items[task_id].target\n", "            if prefix[-1] == \"\\n\":\n", "                stats[\"bad_prefix\"] += 1\n", "            assert target is not None\n", "            # if original_file != prefix + suffix:\n", "            #     stats[\"prefix_suffix_mismatch\"] += 1\n", "            #     print(f\"Prefix/suffix mismatch: {task_id}\")\n", "            #     continue\n", "            #     continue\n", "            # Let's call this a test\n", "\n", "            if not original_file.startswith(prefix):\n", "                print(f\"Prefix mismatch: {task_id}\")\n", "                continue\n", "            if not original_file.endswith(suffix):\n", "                print(f\"Suffix mismatch: {task_id}\")\n", "                continue\n", "            assert original_file.startswith(prefix)\n", "            assert original_file.endswith(suffix)\n", "            completion = original_file.replace(prefix, \"\").replace(suffix, \"\")\n", "\n", "            completions[task_id_key] = completion\n", "            targets[task_id_key] = target\n", "\n", "            target_pp = postprocess_code_lines(target, lang)\n", "            completion_pp = postprocess_code_lines(completion, lang)\n", "\n", "            assert target_pp\n", "            if completion_pp and target_pp.startswith(completion_pp):\n", "                stats[\"non_empty_prefix_match\"]\n", "            if compute_exact_match(target, [completion]) == 1.0:\n", "                stats[\"exact_match_strict\"] += 1\n", "            if target_pp == completion_pp:\n", "                stats[\"exact_match\"] += 1\n", "            if (\n", "                target_pp != completion_pp\n", "                and compute_exact_match(target, [completion]) != 1.0\n", "            ):\n", "                does_not_match_gt.append((task_id, completion, target))\n", "                if prefix[-1] == \"\\n\":\n", "                    stats[\"bad_prefix_and_mismatch\"] += 1\n", "            if not completion:\n", "                stats[\"empty_completion\"] += 1\n", "\n", "            stats[\"found\"] += 1\n", "            # assert task_items[task_id].extra[\"ground_truth\"] == original_file\n", "        else:\n", "            # print(f\"Not found {task_id}\")\n", "            stats[\"not_found\"] += 1\n", "\n", "        # gt = d / \"ground_truth\"\n", "        # f = d / path.stem\n", "        # print(f\"diff -b {f} {gt}\")\n", "        # print(f\"GT: {gt.read_text()}\")\n", "        # break\n", "    return stats, completions, targets\n", "\n", "\n", "@dataclass\n", "class my_stats:\n", "    stats: dict\n", "    completions: dict\n", "    targets: dict\n", "\n", "\n", "stats_multi = my_stats(*calc_stats(augment_multi))\n", "stats_single = my_stats(*calc_stats(augment_single))\n", "print(stats_multi.stats)\n", "print(stats_single.stats)\n", "count = 0\n", "for k in stats_multi.completions:\n", "    assert k in stats_single.completions\n", "    assert stats_single.targets[k] == stats_multi.targets[k]\n", "    if stats_multi.completions[k] == stats_single.completions[k]:\n", "        continue\n", "    if stats_multi.targets[k] != stats_multi.completions[k]:\n", "        continue\n", "    print(f\"Completion mismatch: {k}\")\n", "    print(stats_multi.completions[k])\n", "    print(stats_single.completions[k])\n", "    count += 1\n", "    if count > 100:\n", "        break\n", "\n", "# print(\"Does not match GT:\")\n", "# import random\n", "# sample = random.sample(does_not_match_gt, 10)\n", "# for x, completion, target in sample:\n", "#     print(f\"task {x} completion vs target\\n{completion}\\n<<>>\\n{target}\")\n", "#     # print(completion, target)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
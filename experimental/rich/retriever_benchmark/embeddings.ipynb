{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.insert(0, \"/home/<USER>/src/augment-origin/research/gpt-neox\")\n", "sys.path.insert(0, \"/home/<USER>/src/augment-origin\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.types import Chunk\n", "from research.retrieval.chunking_functions import LineLevelChunker\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.tasks import cceval\n", "from base.tokenizers.tiktoken_starcoder_tokenizer import (\n", "    StarCoderSpecialTokens,\n", "    TiktokenStarCoderTokenizer,\n", ")\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "tokenizer = TiktokenStarCoderTokenizer()\n", "\n", "task = cceval.CCEval(limit=4000)\n", "with Path(\"task_list.txt\").open(\"w\") as f:\n", "    for x in task:\n", "        print(x[0].extra[\"repository\"], x[0].extra[\"patch_id\"], file=f)\n", "print(len(task))\n", "\n", "# document and query chunk size\n", "\n", "# Extract the documents and queries from the task\n", "task_items = [(mi, docs) for mi, docs in task]\n", "docs = [doc for _, docs in task_items for doc in docs]\n", "mi = [mi for mi, _ in task_items]\n", "print(\"All docs\", len(docs))\n", "\n", "# Filter out larger docs like we do in the client. XXX may want to revisit this.\n", "doc_count = len(docs)\n", "# docs = [doc for doc in docs if len(doc.text) < 128*1024]\n", "print(\"Removed large files\", doc_count - len(docs))\n", "print(\"Filtered docs\", len(docs))\n", "\n", "# Chunk up documents\n", "max_lines_per_chunk = 30\n", "llc = LineLevelChunker(max_lines_per_chunk=max_lines_per_chunk)\n", "chunks: list[Chunk] = [chunk for doc in docs for chunk in llc.split_into_chunks(doc)]\n", "\n", "\n", "# Stats on chunks\n", "def chunk_stats():\n", "    largest_chunk = max(len(chunk.text) for chunk in chunks)\n", "    chunk_tokens = [len(tokenizer.tokenize_safe(chunk.text)) for chunk in chunks]\n", "    print(\"largest chunk size\", largest_chunk)\n", "    print(\"largest chunk tokens size\", max(chunk_tokens))\n", "    chunk_histo, bins = np.histogram([len(chunk.text) for chunk in chunks])\n", "    token_count_histo, token_count_bins = np.histogram(chunk_tokens)\n", "    print(chunk_histo)\n", "    print(bins)\n", "    print(token_count_histo)\n", "    print(token_count_bins)\n", "\n", "\n", "# MAX_CHUNK = 8191 tokens\n", "unique_chunks = set(chunks)\n", "print(\"Chunks\", len(chunks))\n", "print(\"Unique chunks\", len(unique_chunks))\n", "truncated_chunks = sum([len(chunk.text) >= 8192 for chunk in unique_chunks])\n", "print(\"Truncated chunks\", truncated_chunks)\n", "for chunk in chunks:\n", "    print(chunk)\n", "    break\n", "\n", "\n", "# queries\n", "def last_chunk(x, max_lines_per_chunk):\n", "    lines = x.splitlines(keepends=True)\n", "    lines = lines[-max_lines_per_chunk:]\n", "    return \"\".join(lines)\n", "\n", "\n", "queries = [last_chunk(x.prefix, max_lines_per_chunk) for x in mi]\n", "\n", "\n", "def query_stats():\n", "    print(\"Queries\", len(queries))\n", "    print(\"Queries unique\", len(set(queries)))\n", "    print(\"Queries max length\", max(len(x) for x in queries))\n", "    print(\n", "        \"Queries max token length\",\n", "        sum(len(tokenizer.tokenize_safe(x)) > 8191 for x in queries),\n", "    )\n", "    print(\"Queries over 8192 chars\", sum(len(x) > 8192 for x in queries))\n", "\n", "\n", "# chunk_stats()\n", "query_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.synthetic_code_edit.api_lib import GptEmbeddingWrapper\n", "import sqlite3\n", "# \"/home/<USER>/src/augment-origin/experimental/rich/retriever_benchmark/text_embedding_3_small_cache.db\"\n", "\n", "from multiprocessing import Lock\n", "from tqdm import tqdm\n", "\n", "\n", "class EmbeddingCache:\n", "    def __init__(self, cache_file: str):\n", "        cache_file = \"test_cache.db\"\n", "        self.conn = sqlite3.connect(cache_file)\n", "        # Create a cache file for the embeddings\n", "        if cache_file is not None:\n", "            self.conn = sqlite3.connect(cache_file)\n", "            self.conn.execute(\n", "                \"\"\"\n", "CREATE TABLE IF NOT EXISTS sentence_embeddings (\n", "    sentence TEXT PRIMARY KEY,\n", "    embedding BLOB\n", ")\n", "\"\"\"\n", "            )\n", "            self.conn.commit()\n", "            self.lock = Lock()\n", "\n", "    def get(self, text: str):\n", "        with self.lock:\n", "            cursor = self.conn.execute(\n", "                \"SELECT embedding FROM sentence_embeddings WHERE sentence = ?\",\n", "                (text,),\n", "            )\n", "            row = cursor.fetchone()\n", "        if not row:\n", "            return None\n", "        return np.frombuffer(row[0], dtype=np.float64).tolist()\n", "\n", "    def set(self, text: str, embedding: list[float]):\n", "        embedding_blob = np.array(embedding, dtype=np.float64).tobytes()\n", "        if self.get(text) is not None:\n", "            return\n", "        with self.lock:\n", "            self.conn.execute(\n", "                \"INSERT INTO sentence_embeddings (sentence, embedding) VALUES (?, ?)\",\n", "                (text, embedding_blob),\n", "            )\n", "            self.conn.commit()\n", "\n", "\n", "if True:\n", "    gpt_embedding_wrapper = GptEmbeddingWrapper(\n", "        shareable_between_processes=False,\n", "        cache_file=\"text_embedding_3_small_cache.db\",\n", "    )\n", "else:\n", "    cache = EmbeddingCache(\"\")\n", "\n", "    def insert_into_cache(text, model):\n", "        cache.set(text, [1.0] * 1536)\n", "\n", "    gpt_embedding_wrapper = insert_into_cache\n", "\n", "\n", "truncated = 0\n", "tokens = 0\n", "for chunk in tqdm(chunks):\n", "    if len(chunk.text) > 8192:\n", "        truncated += 1\n", "    chunk_text = chunk.text[:8192]\n", "    tokens += len(tokenizer.tokenize_safe(chunk_text))\n", "    gpt_embedding_wrapper(chunk_text, \"text-embedding-3-small\")\n", "print(\"Chunks:\")\n", "print(f\"truncated {truncated} out of {len(chunks)}\")\n", "print(gpt_embedding_wrapper.get_stats())\n", "print(f\"{tokens=}\")\n", "\n", "truncated = 0\n", "tokens = 0\n", "for query in tqdm(queries):\n", "    # Take last 8192 characters, unlike with retrieval\n", "    if len(query) > 8192:\n", "        truncated += 1\n", "    query = query[-8192:]\n", "    tokens += len(tokenizer.tokenize_safe(query))\n", "    gpt_embedding_wrapper(query, \"text-embedding-3-small\")\n", "print(\"Queries:\")\n", "print(f\"truncated {truncated} out of {len(queries)}\")\n", "print(gpt_embedding_wrapper.get_stats())\n", "print(f\"{tokens=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class EmbeddingCache:\n", "    def __init__(self, cache_file: str):\n", "        self.conn = sqlite3.connect(cache_file)\n", "\n", "    def get(self, text: str):\n", "        cursor = self.conn.execute(\n", "            \"SELECT embedding FROM sentence_embeddings WHERE sentence = ?\",\n", "            (text,),\n", "        )\n", "        row = cursor.fetchone()\n", "        assert row\n", "        return np.frombuffer(row[0], dtype=np.float64).tolist()\n", "\n", "\n", "ec = EmbeddingCache(\"test_embeddings.db\")\n", "chunk_text = \"\"\"        return this.heroRepository.findById(id).orElseThrow(NotFoundException::new);\n", "    }\n", "\n", "    public Hero create(Hero heroToCreate) {\n", "        heroToCreate.setXp(0);\n", "        return this.heroRepository.save(heroToCreate);\n", "    }\n", "\n", "    public Hero update(<PERSON> id, Hero heroToUpdate) {\n", "        Hero dbHero = this.findById(id);\n", "        if (!dbHero.getId().equals(heroToUpdate.getId())) {\n", "            throw new BusinessException(\"Update IDs must be the same.\");\n", "        }\n", "        // DONE! Make sure \"xp\" is not changed. In practice, only \"name\" can be changed.\n", "        dbHero.setName(heroToUpdate.getName());\n", "        return this.heroRepository.save(dbHero);\n", "    }\n", "\n", "    public void delete(Long id) {\n", "        Hero dbHero = this.findById(id);\n", "        this.heroRepository.delete(dbHero);\n", "    }\n", "\n", "    public void increaseXp(Long id) {\n", "        Hero dbHero = this.findById(id);\n", "        dbHero.setXp(dbHero.getXp() + 2);\n", "        heroRepository.save(dbHero);\n", "    }\n", "}\"\"\"\n", "ec.get(chunk_text)\n", "# for chunk in tqdm(chunks[:10]):\n", "#     chunk_text = chunk.text[:8192]\n", "#     print(len(ec.get(chunk_text)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class EmbeddingCache:\n", "    def __init__(self, cache_file: str):\n", "        self.conn = sqlite3.connect(cache_file)\n", "\n", "    def dump(self):\n", "        cursor = self.conn.execute(\n", "            \"SELECT sentence FROM sentence_embeddings order by sentence\",\n", "        )\n", "        rows = cursor.fetchall()\n", "        return rows\n", "\n", "\n", "to_find = \"\"\"<!--\n", "***********************************************************************************************\n", "Microsoft.CppBuild.targets\n", "\"\"\"\n", "ec = EmbeddingCache(\"test_embeddings.db\")\n", "all_text = ec.dump()\n", "for idx, text in enumerate(all_text):\n", "    text = text[0]\n", "    # print(idx, text)\n", "    if not text:\n", "        print(\"No text\")\n", "        continue\n", "    # print(idx, text.splitlines(keepends=True)[0])\n", "    if text.startswith(\"<!--\"):\n", "        print(text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import pytest
import experimental.rich.retriever_benchmark.cached_embeddings_scorer
from research.eval.harness import factories
from research.eval.harness.tasks import CCEval
from research.model_server.model_server_system import ModelServerSystem

from research.eval.tests.systems.test_basic_system import MockModel


@pytest.mark.integration
def test_rag_system_with_cceval(tmp_path):
    # Create a gold completion model
    # gold_model = factories.create_model({"model": "gold_completion_model"})

    # Create a cached embeddings scorer
    # scorer = factories.create_scorer({"scorer": "cached_embeddings_scorer"})

    # Create a RAG system with the gold completion model and cached embeddings scorer
    rag_system = factories.create_system(
        {
            "name": "basic_rag",
            "model": {"name": "null"},
            "retriever": {
                "scorer": {
                    "name": "cached_embeddings",
                    "db_path": "test_embeddings.db",
                },
                "chunker": {"name": "line_level", "max_lines_per_chunk": 30},
                "query_formatter": {
                    "name": "simple_query",
                    "max_lines": 30,
                    # "db_path": "test_db_path",
                },
            },
            "generation_options": {"max_generated_tokens": 280},
            "experimental": {"remove_suffix": False},
        }
    )

    # Create a CCEval task
    cceval_task = CCEval(limit=4000)

    # Run the CCEval task on the RAG system
    results = cceval_task.run(rag_system, output_path=tmp_path)

    # Check that the results are not empty
    assert results is not None
    assert len(results) > 0

    # Check that the results contain the expected metrics
    assert "metrics" in results
    assert "total" in results["metrics"]
    assert "exact_match" in results["metrics"]["total"]

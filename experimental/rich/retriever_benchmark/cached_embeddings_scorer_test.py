import pytest
from experimental.rich.retriever_benchmark.cached_embeddings_scorer import (
    CachedEmbeddingsScorer,
)
from experimental.rich.retriever_benchmark.embeddings_cache import Embedding<PERSON>ache
import sqlite3
import numpy as np
from research.retrieval.types import Chunk, Document
from research.retrieval.chunking_functions import LineLevelChunker
from typing import Collection, Mapping
from research.core.model_input import ModelInput


@pytest.fixture
def db_path(tmp_path):
    db_file = tmp_path / "test.db"
    return str(db_file)


def get_repo():
    doc1 = Document.new(text="This is a test document 1.", path="foo")
    doc2 = Document.new(text="This is a test document 2.", path="bar")
    chunker = LineLevelChunker(max_lines_per_chunk=5)
    chunks1 = chunker.split_into_chunks(doc1)
    chunks2 = chunker.split_into_chunks(doc2)
    return {doc1.id: chunks1, doc2.id: chunks2}


def test_add_doc(db_path):
    # lines per chunk
    repo = get_repo()
    chunks = next(iter(repo.values()))
    cache = EmbeddingCache(db_path)
    for chunk in chunks:
        cache.set(chunk.text, np.random.rand(128).tolist())

    scorer = CachedEmbeddingsScorer(db_path)
    scorer.add_doc(chunks)
    mi = ModelInput(prefix="This is a test document 1.")
    score = scorer.score(mi)
    assert len(score[0]) == len(chunks)


def test_add_docs(db_path):
    repo = get_repo()
    chunks = [chunk for chunks in repo.values() for chunk in chunks]
    cache = EmbeddingCache(db_path)
    for chunk in chunks:
        cache.set(chunk.text, np.random.rand(128).tolist())

    scorer = CachedEmbeddingsScorer(db_path)
    scorer.add_docs(repo)

    mi = ModelInput(prefix="This is a test document 2.")
    chunk_ids, score = scorer.score(mi)
    assert len(chunk_ids) == 2
    assert score[1] > score[0]


def test_remove_doc(db_path):
    repo = get_repo()
    chunks = [chunk for chunks in repo.values() for chunk in chunks]
    cache = EmbeddingCache(db_path)
    for chunk in chunks:
        cache.set(chunk.text, np.random.rand(128).tolist())

    one_doc = next(iter(repo.values()))
    scorer = CachedEmbeddingsScorer(db_path)
    scorer.add_doc(one_doc)
    scorer.remove_doc(one_doc)
    assert len(scorer.scoring_data) == 0


def test_remove_all_docs(db_path):
    repo = get_repo()
    chunks = [chunk for chunks in repo.values() for chunk in chunks]
    cache = EmbeddingCache(db_path)
    for chunk in chunks:
        cache.set(chunk.text, np.random.rand(128).tolist())

    scorer = CachedEmbeddingsScorer(db_path)
    scorer.add_docs(repo)
    scorer.remove_all_docs()
    assert len(scorer.scoring_data) == 0


def test_score(db_path):
    repo = get_repo()
    chunks = [chunk for chunks in repo.values() for chunk in chunks]
    cache = EmbeddingCache(db_path)
    for chunk in chunks:
        cache.set(chunk.text, np.random.rand(128).tolist())

    scorer = CachedEmbeddingsScorer(db_path)
    scorer.add_docs(repo)

    model_input = ModelInput(prefix="This is a test document 2.")
    scored_ids, scores = scorer.score(model_input, list(repo.keys()))
    assert len(scored_ids) == 2
    assert len(scores) == 2

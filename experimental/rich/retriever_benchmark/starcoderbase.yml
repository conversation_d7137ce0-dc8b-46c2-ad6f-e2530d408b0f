determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Starcoder, no-retrieval, research
  project: rich
  workspace: Dev

podspec: 1xA100.yaml

system:
  name: basic_rag
  experimental:
    remove_suffix: false
    retriever_top_k: 25
    trim_on_dedent: false

  model:
    name: starcoderbase
    prompt:
      always_fim_style: true
      max_prefix_tokens: 2048
      max_prompt_tokens: 7912
      max_suffix_tokens: 2048
      retrieval_layout_style: "comment2"
      max_number_chunks: 100
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    name: null
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9

task:
  name: cceval
  limit: 4000

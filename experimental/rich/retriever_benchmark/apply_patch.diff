diff --git a/research/data/synthetic_code_edit/api_lib.py b/research/data/synthetic_code_edit/api_lib.py
index a99297dc6a..be163ab0e7 100644
--- a/research/data/synthetic_code_edit/api_lib.py
+++ b/research/data/synthetic_code_edit/api_lib.py
@@ -10,6 +10,7 @@ import numpy as np
 import openai
 from openai import OpenAI
 from openai.types.chat.completion_create_params import ResponseFormat
+from openai.types.create_embedding_response import CreateEmbeddingResponse

 OpenAIChatModels = typing.Literal[
     "gpt-4-1106-preview",  # 128,000 tokens
@@ -17,7 +18,9 @@ OpenAIChatModels = typing.Literal[
     "gpt-4-0613",  # 8192 tokens,
     "gpt-4o",  # 128,000 tokens
 ]
-OpenAIEmbeddingModels = typing.Literal["text-embedding-ada-002"]
+OpenAIEmbeddingModels = typing.Literal[
+    "text-embedding-ada-002", "text-embedding-3-small"
+]

 # According to https://openai.com/pricing and
 # https://help.openai.com/en/articles/7127956-how-much-does-gpt-4-cost
@@ -41,7 +44,8 @@ GPT_CHAT_PRICES: typing.Dict[OpenAIChatModels, typing.Dict[str, float]] = {
 }

 GPT_EMBEDDING_PRICES: typing.Dict[OpenAIEmbeddingModels, float] = {
-    "text-embedding-ada-002": 0.0004 / 1000
+    "text-embedding-3-small": 0.00002 / 1000,  # as of 2024-06-28
+    "text-embedding-ada-002": 0.0004 / 1000,
 }


@@ -198,7 +202,9 @@ CREATE TABLE IF NOT EXISTS sentence_embeddings (
                 row = cursor.fetchone()
             if row:
                 return np.frombuffer(row[0], dtype=np.float64).tolist()
-        response = openai.embeddings.create(input=sentence, model=model)
+        response: CreateEmbeddingResponse = openai.embeddings.create(
+            input=sentence, model=model
+        )
         assert response.usage is not None
         with self.lock:
             self.token_counters[model] += response.usage.prompt_tokens
diff --git a/research/eval/harness/tasks/cceval.py b/research/eval/harness/tasks/cceval.py
index 29dff5b4de..246ef58396 100644
--- a/research/eval/harness/tasks/cceval.py
+++ b/research/eval/harness/tasks/cceval.py
@@ -278,13 +278,29 @@ class CCEval(CodeCompleteTask):
     def __getitem__(self, index: int) -> tuple[ModelInput, DocsType]:
         """Get the index-th example in this task."""
         # TODO(Xuanyi): returns exactly what should be passed to system.
-        logger.warning(
-            "CCEval's __getitem__ is not returning the same documents and retrieval chunks as run."
-            " You need to process the doc_ids outside of this function to get the same results."
-        )
+        # logger.warning(
+        #     "CCEval's __getitem__ is not returning the same documents and retrieval chunks as run."
+        #     " You need to process the doc_ids outside of this function to get the same results."
+        # )
         repo, rec = self.all_patches[index]
         documents = self.all_documents_by_repo[repo]
-        model_input: ModelInput = self.create_model_input(rec, [])
+        documents = documents.copy()
+
+        # Add modified prompt document to the system
+        logger.debug(f"Processing patch with path {rec.metadata.file}")
+        new_content = rec.prompt + rec.right_context
+        new_doc = Document.new(text=new_content, path=rec.metadata.file)
+
+        # Exclude the original document id from the list of documents to retrieve from,
+        # and replace it with the new document id.
+        doc_ids = {x.id for x in documents if x.path != new_doc.path}
+        doc_ids.add(new_doc.id)
+        documents.append(new_doc)
+        assert len(doc_ids) + 1 == len(documents)
+
+        model_input: ModelInput = self.create_model_input(
+            rec=rec, doc_ids=list(doc_ids)
+        )
         return model_input, documents

     def __len__(self) -> int:
@@ -565,6 +581,7 @@ class CCEval(CodeCompleteTask):
                     stop=len(rec.prompt),
                 ),
                 "ground_truth": rec.groundtruth,
+                "repository": rec.metadata.repository,
             },
         )
         return model_input

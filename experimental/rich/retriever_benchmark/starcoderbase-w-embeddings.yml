determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Starcoder with cached embeddings, research
  project: rich
  workspace: Dev

podspec: 1xA100.yaml

import_modules:
  experimental.rich2.cached_embeddings_scorer
system:
  name: basic_rag
  experimental:
    remove_suffix: false
    retriever_top_k: 25
    trim_on_dedent: false

  model:
    name: starcoderbase
    prompt:
      always_fim_style: true
      max_prefix_tokens: 2048
      max_prompt_tokens: 7912
      max_suffix_tokens: 2048
      retrieval_layout_style: "comment2"
      max_number_chunks: 100
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    scorer:
      name: cached_embeddings
      db_path: /mnt/efs/augment/user/rich/data/text_embedding_3_small_cache.db
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: simple_query
      max_lines: 30

task:
  name: cceval
  limit: 4000

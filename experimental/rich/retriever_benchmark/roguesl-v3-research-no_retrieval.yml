
augment:
  gpu_count: 2

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Roguesl-v3, no-retrieval, research
  project: rich
  workspace: Dev

podspec: 2xA100.yaml

system:
  experimental:
    remove_suffix: false
    retriever_top_k: 25
    trim_on_dedent: false
  fim_gen_mode: evaluation
  generation_options:
    max_generated_tokens: 280
  model:
    name: fastbackward_starcoder
    checkpoint_path: roguesl/star16b_8k_4ksteps_gradfix_fb
    model_parallel_size: 2
    seq_length: 6400
    override_prompt_formatter:
      name: rogue_statelesscache
      component_order:
        - prefix
        - suffix
        - retrieval
        - nearby_prefix
      context_quant_token_len: 50
      max_prefix_tokens: 1030
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      nearby_prefix_token_len: 250
      nearby_prefix_token_overlap: 0
      nearby_suffix_token_len: 0
      nearby_suffix_token_overlap: 0
      use_far_prefix_token: true

  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    name: null
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9

task:
  name: cceval
  limit: 4000

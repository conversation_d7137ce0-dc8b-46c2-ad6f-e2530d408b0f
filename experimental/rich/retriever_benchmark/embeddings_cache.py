from multiprocessing import Lock
import sqlite3
import numpy as np


class EmbeddingCache:
    def __init__(self, cache_file: str):
        # cache_file = "test_cache.db"
        self.conn = sqlite3.connect(cache_file)
        # Create a cache file for the embeddings
        if cache_file is not None:
            self.conn = sqlite3.connect(cache_file)
            self.conn.execute(
                """
CREATE TABLE IF NOT EXISTS sentence_embeddings (
    sentence TEXT PRIMARY KEY,
    embedding BLOB
)
"""
            )
            self.conn.commit()
            self.lock = Lock()

    def get(self, text: str):
        with self.lock:
            cursor = self.conn.execute(
                "SELECT embedding FROM sentence_embeddings WHERE sentence = ?",
                (text,),
            )
            row = cursor.fetchone()
        if not row:
            return None
        return np.frombuffer(row[0], dtype=np.float64).tolist()

    def set(self, text: str, embedding: list[float]):
        embedding_blob = np.array(embedding, dtype=np.float64).tobytes()
        with self.lock:
            try:
                self.conn.execute(
                    "INSERT INTO sentence_embeddings (sentence, embedding) VALUES (?, ?)",
                    (text, embedding_blob),
                )
                self.conn.commit()
            except sqlite3.IntegrityError:
                raise ValueError(f"Embedding for {text} already exists.")

import pytest
import sqlite3
import numpy as np
from experimental.rich.retriever_benchmark.embeddings_cache import EmbeddingCache


@pytest.fixture
def cache_file(tmp_path):
    cache_file = tmp_path / "test_cache.db"
    return str(cache_file)


def test_init(cache_file):
    cache = EmbeddingCache(cache_file)
    assert cache.conn is not None
    assert cache.lock is not None


def test_get_nonexistent_embedding(cache_file):
    cache = EmbeddingCache(cache_file)
    assert cache.get("nonexistent_text") is None


def test_set_and_get_embedding(cache_file):
    cache = EmbeddingCache(cache_file)
    text = "test_text"
    embedding = np.random.rand(4)
    cache.set(text, embedding.tolist())
    retrieved_embedding = cache.get(text)
    assert retrieved_embedding is not None
    print(embedding.tolist())
    print(retrieved_embedding)
    assert np.array_equal(retrieved_embedding, embedding)


def test_set_and_get_multiple_embeddings(cache_file):
    cache = EmbeddingCache(cache_file)
    texts = ["text1", "text2", "text3"]
    embeddings = [np.random.rand(128) for _ in range(3)]
    for text, embedding in zip(texts, embeddings):
        cache.set(text, embedding.tolist())
    for text, embedding in zip(texts, embeddings):
        retrieved_embedding = cache.get(text)
        assert np.array_equal(retrieved_embedding, embedding)


def test_get_embedding_with_lock(cache_file):
    cache = EmbeddingCache(cache_file)
    text = "test_text"
    embedding = np.random.rand(128)
    cache.set(text, embedding.tolist())
    # with cache.lock:
    retrieved_embedding = cache.get(text)
    assert np.array_equal(retrieved_embedding, embedding)


def test_set_embedding_twice(cache_file):
    cache = EmbeddingCache(cache_file)
    text = "test_text"
    embedding1 = np.random.rand(128)
    embedding2 = np.random.rand(128)
    cache.set(text, embedding1.tolist())
    with pytest.raises(ValueError):
        cache.set(text, embedding2.tolist())

# includes is an ordered list of gpt-neox config files to be loaded
includes:
- /mnt/efs/augment/configs/codegen-H/lr/3e-4.yml
- /mnt/efs/augment/configs/codegen-H/train/big_batch.f.p4.yml
- /mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml
# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: retrieval_2048_2B
  description: retrieval_2048_2B
  workspace: Dev
  project: rich
  max_restarts: 1
  perform_initial_validation: True  # Do a validation at iteration 0
# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "a100-podspec.yaml"  # Path to the podspec file.  Absolute, or relative to "templates"
  gpu_count: 8  # How many GPUs to ask for
  save_trial_best: 0  # How many of the best checkpoints to save

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc:  True  # Enable on-the-fly checkpoint GC
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"  # For training

  # Evaluation args (comment out for experiment)
  # eval_tasks: gitrepo_poly_C_small  # Space separated list of eval tasks
  # eval_results_prefix: /mnt/efs/augment/user/username/eval_results # Path and prefix for evaluation results
  # entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/eval.sh"  # For eval

# overrides is a dictionary of gpt-neox args to override the values in the above list of included config files
overrides:
  # WandB options. WANDB_API_KEY will come from the environment, or ~/.netrc if you are logged in.
  wandb_name: retrieval_2048_2B
  wandb_project: codegen   # This probably needs to already exist in your wandb dashboard
  wandb_group: retrieval_2048_2B
  # wandb_team: my_team

  # save: is ignored by determined; checkpoints are saved in s3
  # save: /mnt/efs/augment/checkpoints/pref_signal/test1_ft_beta0.95_s2048_lr1.6e-5_const

  # data_path: /mnt/efs/augment/data/processed/github/github_small_text_document
  # /mnt/efs/augment/data/processed/the-stack.2023-02-04.fim/signal_retrieve_fim/
  data_path: /mnt/efs/augment/data/processed/the-stack.2023-02-04.fim/signal_retrieve_fim/doc_text_document_train
  # train_data_paths: []
  # valid_data_paths: []
  # test_data_paths: []
  # data_path: null
  # train_data_paths: [/mnt/efs/augment/data/processed/the-stack.2023-02-04.fim/signal_retrieve_fim/doc_text_document_train]
  # valid_data_paths: [/mnt/efs/augment/data/processed/the-stack.2023-02-04.fim/signal_retrieve_fim/doc_text_document_validation]
  # test_data_paths: [/mnt/efs/augment/data/processed/the-stack.2023-02-04.fim/signal_retrieve_fim/doc_text_document_validation]

  load: /mnt/efs/augment/checkpoints/codegen-2B-multi

  train_batch_size: 192
  train_micro_batch_size_per_gpu: 24
  gradient_accumulation_steps: 1

  dataset_type: direct
  loss_mask_mode: pad

  eval_interval: 1000
  early_stopping: False
  early_stopping_threshold: 0.005

  train_iters: 50000
  lr_decay_iters: 50000  # If not set, defaults to train_iters
  warmup: 0.
  lr_decay_style: constant

  seq_length: 2048

  optimizer:
      params:
          betas:
          - 0.9
          - 0.95
          eps: 1.0e-08
          lr: 1.6e-05
      type: Adam

  save_interval: 1000
  keep_last_n_checkpoints: 2
  # to keep all checkpoints, also disable determined.enable_checkpoint_gc
  #keep_last_n_checkpoints: null
  no_save_optim: False

includes:
- augment_configs/codegen-H/lr/3e-4.yml
- augment_configs/codegen-H/train/big_batch.f.p4.yml
- augment_configs/codegen-H/model/codegen-2B.ft.yml
overrides:
    wandb_name: pref_signal_test1_ft_newbeta_const_1.6e-5
    wandb_group: pref_signal_test1_ft_newbeta_const_1.6e-5
    save: /mnt/efs/augment/checkpoints/pref_signal/test1_ft_beta0.95_s2048_lr1.6e-5_const

    data_path: /mnt/efs/augment/data/processed/the-stack.2023-02-04.fim/doc_text_document
    #train_data_paths: [/mnt/efs/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document]
    #valid_data_paths: []
    #test_data_paths: []

    load: /mnt/efs/augment/checkpoints/codegen-2B-multi

    train_batch_size: 384
    train_micro_batch_size_per_gpu: 48
    gradient_accumulation_steps: 1

    eval_interval: 1000
    early_stopping: False
    early_stopping_threshold: 0.005

    train_iters: 50000
    lr_decay_iters: 50000
    warmup: 0.
    lr_decay_style: constant

    seq_length: 2048
    fim_probability: 0.0

    optimizer:
        params:
            betas:
            - 0.9
            - 0.95
            eps: 1.0e-08
            lr: 1.6e-05
        type: Adam

    save_interval: 1000
    keep_last_n_checkpoints: 1
    # to keep all checkpoints
    #keep_last_n_checkpoints: null
    no_save_optim: False

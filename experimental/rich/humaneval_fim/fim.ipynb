{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval.harness.utils import read_jsonl_zst\n", "\n", "files = [\n", "    \"/mnt/efs/augment/eval/jobs/XHoxHuJx\",\n", "    \"/mnt/efs/augment/eval/jobs/KPtobeYz\",\n", "    \"/mnt/efs/augment/eval/jobs/YqNcXt38\",\n", "    \"/mnt/efs/augment/eval/jobs/aSJp7iWC\",\n", "    \"/mnt/efs/augment/eval/jobs/GaZ26VXa\",\n", "    \"/mnt/efs/augment/eval/jobs/ceg29BHy\",\n", "\n", "    \"/mnt/efs/augment/eval/jobs/4ZQZJueh\",\n", "    \"/mnt/efs/augment/eval/jobs/F25Q2dih\",\n", "    \"/mnt/efs/augment/eval/jobs/JjoA6A2e\",\n", "    \"/mnt/efs/augment/eval/jobs/RJ9tGjCo\",\n", "    \"/mnt/efs/augment/eval/jobs/RZn59yAr\",\n", "    \"/mnt/efs/augment/eval/jobs/fCGVzb4k\",\n", "\n", "    \"/mnt/efs/augment/eval/jobs/FFBMxpUD\",\n", "    \"/mnt/efs/augment/eval/jobs/Mdd5oJur\",\n", "    \"/mnt/efs/augment/eval/jobs/WVUviigH\",\n", "    \"/mnt/efs/augment/eval/jobs/h2oWTaSa\",\n", "    \"/mnt/efs/augment/eval/jobs/jw4bZfDm\",\n", "    \"/mnt/efs/augment/eval/jobs/oBnH3Qyd\",\n", "\n", "    \"/mnt/efs/augment/eval/jobs/HvhXiiXh\",\n", "    \"/mnt/efs/augment/eval/jobs/QLyGRUDn\",\n", "    \"/mnt/efs/augment/eval/jobs/REHA7QUa\",\n", "    \"/mnt/efs/augment/eval/jobs/ZS6ojDNa\",\n", "    \"/mnt/efs/augment/eval/jobs/eBx7Wyd3\",\n", "    \"/mnt/efs/augment/eval/jobs/jbkqDUGm\",\n", "]\n", "import json\n", "import yaml\n", "\n", "def get_result(base_path):\n", "    results = list(base_path.glob(\"*_results.jsonl\"))\n", "    assert len(results) == 1\n", "    return json.loads(results[0].read_text())\n", "\n", "def get_config(base_path):\n", "    results = list(base_path.glob(\"*_config.yml\"))\n", "    assert len(results) == 1\n", "    return yaml.safe_load(results[0].read_text())\n", "\n", "def get_model(config):\n", "    return config[\"systems\"][0][\"model\"][\"name\"]\n", "\n", "def get_task(config):\n", "    assert len(config[\"tasks\"]) == 1\n", "    return config[\"tasks\"][0].get(\"variant\", \"multiline\")\n", "\n", "variant_score = {}\n", "for path in files:\n", "    path = Path(path)\n", "    result = get_result(path)\n", "    # print(result)\n", "    config = get_config(path)\n", "    task = get_task(config)\n", "    # print(config)\n", "    if \"metrics\" in result:\n", "        pass_at_k = result[\"metrics\"][\"pass_at_k\"]\n", "    else:\n", "        pass_at_k = result[\"result\"][\"metrics\"][\"pass_at_k\"]\n", "    if task not in variant_score:\n", "        variant_score[task] = []\n", "    # print(task)\n", "    variant_score[task].append((get_model(config), pass_at_k.get(\"pass@1\", None), pass_at_k.get(\"pass@10\", None)))\n", "    # print(get_model(config), pass_at_k.get(\"pass@1\", None), pass_at_k.get(\"pass@10\", None))\n", "\n", "# print(variant_score)\n", "for variant in variant_score.keys():\n", "    print(variant)\n", "    for score in variant_score[variant]:\n", "        print(*score)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer import tokenizer\n", "from dataclasses import dataclass\n", "@dataclass\n", "class Args:\n", "    \"\"\"Arguments to tokenizer.\"\"\"\n", "\n", "    rank: int = 0\n", "    tokenizer_type: str = \"StarCoderTokenizer\"\n", "    # padded_vocab_size: int = -1\n", "    model_parallel_size: int = 1\n", "    make_vocab_size_divisible_by: int = 51200\n", "args = Args()\n", "token = tokenizer.build_tokenizer(args)\n", "tokens = [1, 1070, 11993, 1188, 1682, 478, 203, 589, 2761, 81, 1170, 81, 8723, 26, 8352, 44, 1682, 77, 466, 614, 33826, 44, 596, 27, 967, 1682, 77, 466, 2786, 284, 1524, 8287, 600, 1509, 1149, 432, 5852, 1755, 436, 11668, 688, 4799, 2702, 33826, 284, 8217, 2761, 81, 1170, 81, 8723, 25619, 330, 83, 683, 284, 1605, 284, 8217, 2761, 81, 1170, 81, 8723, 4901, 8183, 370, 330, 21899, 86, 370, 330, 31246, 370, 330, 955, 2188, 330, 83, 683, 284, 2107, 8183, 370, 330, 21899, 86, 370, 330, 955, 785, 284, 1524, 284, 442, 428, 106, 436, 816, 328, 14881, 3, 1140, 101, 415, 33826, 328, 816, 79, 203, 2]\n", "token.detok<PERSON>ze(tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.utils import read_jsonl_zst\n", "\n", "recs = read_jsonl_zst(\"/mnt/efs/augment/data/processed/human_eval_fim.v1/HumanEval-RandomSpanInfilling.jsonl.zst\")\n", "recs[79]\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
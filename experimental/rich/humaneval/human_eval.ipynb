{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Latest results using research models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval.harness.utils import read_jsonl_zst\n", "shares = [\n", "    \"/mnt/efs/augment/eval/jobs/jjuwxuRz\",\n", "    \"/mnt/efs/augment/eval/jobs/3NUQVqxh\",\n", "    \"/mnt/efs/augment/eval/jobs/UGPXs24R\",\n", "    \"/mnt/efs/augment/eval/jobs/Ui46HVJr\",\n", "    \"/mnt/efs/augment/eval/jobs/Jxkbsd7s\",\n", "    \"/mnt/efs/augment/eval/jobs/kMWYt3xg\",\n", "    \"/mnt/efs/augment/eval/jobs/28QdNUQf\",\n", "    \"/mnt/efs/augment/eval/jobs/epZZSwef\",\n", "    \"/mnt/efs/augment/eval/jobs/bTZwFP4p\",\n", "    \"/mnt/efs/augment/eval/jobs/UuunvWff\",\n", "    # Humaneval and MBPP at 0 temperature\n", "    \"/mnt/efs/augment/eval/jobs/HeVjq2KT\",\n", "    \"/mnt/efs/augment/eval/jobs/eRVmN5qj\",\n", "    \"/mnt/efs/augment/eval/jobs/9KRpn8wi\",\n", "    \"/mnt/efs/augment/eval/jobs/8BDfxUSR\",\n", "    \"/mnt/efs/augment/eval/jobs/nRQ3NWgc\",\n", "    \"/mnt/efs/augment/eval/jobs/PMaapkhv\",\n", "    \"/mnt/efs/augment/eval/jobs/Y2UC9xw6\",\n", "    \"/mnt/efs/augment/eval/jobs/WvrXDYcm\",\n", "\n", "]\n", "\n", "import yaml\n", "import json\n", "\n", "all_results = []\n", "for share in shares:\n", "    configs = list(Path(share).glob(\"*_config.yml\"))\n", "    assert len(configs) == 1\n", "    cfg = yaml.safe_load(configs[0].read_text())\n", "    model_name, temperature = cfg[\"systems\"][0][\"model\"][\"name\"], cfg[\"systems\"][0][\"generation_options\"][\"temperature\"]\n", "    # print(model_name, temperature)\n", "    artifacts = list(Path(share).glob(\"*_completed_patches.jsonl.zst\"))\n", "    assert len(artifacts) == 1\n", "\n", "    if \"humaneval\" not in artifacts[0].name:\n", "        print(f\"Skipping {artifacts[0]} because it's not for humaneval.\")\n", "        continue\n", "\n", "    results = list(Path(share).glob(\"*_results.jsonl\"))\n", "    assert len(results) == 1\n", "\n", "\n", "    result = json.loads(results[0].read_text())\n", "    # print(result[\"pass_at_k\"])\n", "    if \"metrics\" in result:\n", "        result[\"pass_at_k\"] = result[\"metrics\"][\"pass_at_k\"]\n", "    pass_at_10 = result[\"pass_at_k\"][\"pass@10\"] if \"pass@10\" in result[\"pass_at_k\"] else -1.0\n", "    all_results.append((model_name, temperature, result[\"pass_at_k\"][\"pass@1\"], pass_at_10))\n", "\n", "for result in all_results:\n", "    model, temp, pass1, pass10 = result\n", "    print(f\"{model} {temp} {pass1:.4f} {pass10:.4f}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
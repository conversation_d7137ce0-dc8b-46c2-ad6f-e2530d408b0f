#!/bin/bash -e
# Small script to copy staging data into a dev dataset. Intended for admin dashboard deveopment, so
# currently only the tables needed for the dashboard are copied.
# To start fresh, make sure that you have the `useSharedDevRequestInsightBigquery` flag set to false
# in your namespace configuration. Then run
# `bazel run //services/request_insight/analytics_dataset:kubecfg -- delete` and
# `bazel run //services/request_insight/analytics_dataset:kubecfg`.

if [ "$#" -ne 4 ]; then
	echo "Usage: $0 <dest_dataset> <start_time> <end_time> <tenant_id_override>"
	exit 1
fi

dest_dataset=$1
start_time=$2
end_time=$3
tenant_id=$4

declare -a tables=(
    "request_metadata"
    "completion_host_response"
    "completion_resolution"
    "edit_host_request"
    "edit_host_response"
    "edit_resolution"
    "chat_host_request"
)

for  i in ${!tables[@]}; do
    # Break this up into two steps so we can re-use the same query across all tables. This is less
    # efficient, but the update query is cheap and this isn't something that needs to run often.
    # 352a91ac7d4283558ccfbc094a527746 is dogfood-shard's tenant id.
    insert_query="
        INSERT INTO system-services-dev.$dest_dataset.${tables[$i]}
        SELECT *
        FROM system-services-prod.us_staging_request_insight_analytics_dataset.${tables[$i]}
        WHERE time >= @start_time AND time <= @end_time
            AND tenant_id = '352a91ac7d4283558ccfbc094a527746'
    "
    echo "$insert_query"
    bq --project_id="system-services-dev" query \
        --nouse_legacy_sql \
        --format=json \
        --parameter=start_time::"$start_time" \
        --parameter=end_time::"$end_time" \
        "$insert_query"

    update_query="
        UPDATE system-services-dev.$dest_dataset.${tables[$i]}
        SET tenant_id = @tenant_id
        WHERE tenant_id = '352a91ac7d4283558ccfbc094a527746'
    "
    echo "$update_query"
    bq --project_id="system-services-dev" query \
        --nouse_legacy_sql \
        --format=json \
        --parameter=tenant_id::"$tenant_id" \
        "$update_query"
done

load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "region_experiment_lib",
    srcs = ["region_experiment.go"],
    importpath = "github.com/augmentcode/augment/tools/region_experiment",
    visibility = ["//visibility:private"],
    deps = [
        "@com_google_cloud_go_spanner//:spanner",
        "@com_github_google_uuid//:uuid",
        "@org_golang_google_api//iterator",
    ],
)

go_binary(
    name = "region_experiment",
    embed = [":region_experiment_lib"],
    visibility = ["//visibility:public"],
)

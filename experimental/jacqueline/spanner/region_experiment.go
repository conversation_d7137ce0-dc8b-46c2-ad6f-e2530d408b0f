package main

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/google/uuid"
	"google.golang.org/api/iterator"
)

func main() {
	ctx := context.Background()
	client, err := spanner.NewClient(ctx, "projects/system-services-dev/instances/spanner-nam6-temp/databases/jacqueline-test")
	if err != nil {
		log.Fatal(err)
	}

	f, err := os.Create("/home/<USER>/spanner_nam6_latencies.csv")
	if err != nil {
		log.Fatal(err)
	}
	defer f.Close()
	w := bufio.NewWriter(f)

	for i := 0; i < 200000; i++ {
		start := time.Now()
		email := fmt.Sprintf("<EMAIL>", i)
		tenantID := uuid.New().String()
		_, err = client.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spanner.ReadWriteTransaction) error {
			// Get the user ID already associated with this email, if any.
			var userID string
			stmt := spanner.Statement{
				SQL:    "SELECT UserId FROM UserEmail WHERE Email = @email",
				Params: map[string]interface{}{"email": email},
			}
			iter := txn.Query(ctx, stmt)
			defer iter.Stop()
			for {
				row, err := iter.Next()
				if err == iterator.Done {
					break
				}
				if err != nil {
					return err
				}
				if err := row.ColumnByName("UserId", &userID); err != nil {
					return err
				}
			}

			// Collect mutations to be executed at the end of the transaction.
			mutations := []*spanner.Mutation{}
			if userID == "" {
				// Create a new user.
				userID = uuid.New().String()
				mutations = append(mutations, spanner.InsertMap(
					"User",
					map[string]interface{}{
						"UserID":           userID,
						"Nonce":            1,
						"CreatedAt":        time.Now(),
						"InUSA":            time.Now(),
						"StripeCustomerID": uuid.New().String(),
						"GivenName":        "Jacqueline",
						"Surname":          "Speiser",
					},
				))

				// Associate the email with the user.
				mutations = append(mutations, spanner.InsertMap(
					"UserEmail",
					map[string]interface{}{
						"UserID": userID,
						"Email":  email,
					},
				))
			}

			// Add user to the tenant.
			// InsertOrUpdate so that we don't get an error if the user is already in the tenant.
			mutations = append(mutations, spanner.InsertOrUpdateMap(
				"UserTenantMapping",
				map[string]interface{}{
					"UserID":   userID,
					"TenantID": tenantID,
				},
			))

			return txn.BufferWrite(mutations)
		})
		end := time.Now()
		if err != nil {
			log.Fatal(err)
		}
		latency := end.Sub(start).Milliseconds()
		fmt.Printf("%d: %d\n", i, latency)
		_, err = w.WriteString(fmt.Sprintf("%d,%d\n", i, latency))
		if err != nil {
			log.Fatal(err)
		}

		if i%100 == 0 {
			w.Flush()
		}
	}

	w.Flush()
}

Results: https://docs.google.com/spreadsheets/d/1BQIkHinVr_AvnjYYsydMYV9o9dJCF9YxZ6i4qanuFlw

Schema:
```sql
CREATE TABLE `User` (
	UserID STRING(36) NOT NULL DEFAULT (GENERATE_UUID()),
	Nonce INT64 NOT NULL,
	CreatedAt TIMESTAMP NOT NULL,
	InUSA TIMESTAMP NOT NULL,
	StripeCustomerID STRING(36),
	GivenName STRING(MAX),
	Surname STRING(MAX),
) PRIMARY KEY(UserID);

CREATE TABLE UserEmail (
	UserID STRING(36) NOT NULL,
	Email	STRING(128) NOT NULL,
) PRIMARY KEY(UserID, Email),
INTERLEAVE IN PARENT `User` ON DELETE CASCADE;
CREATE UNIQUE INDEX UserByEmail ON UserEmail(Email);

CREATE TABLE UserTenantMapping(
	UserID STRING(36) NOT NULL,
	TenantID STRING(36) NOT NULL,
) PRIMARY KEY(UserID, TenantID),
INTERLEAVE IN PARENT `User` ON DELETE CASCADE;
CREATE INDEX UserTenantMappingByTenant ON UserTenantMapping(TenantID);
```

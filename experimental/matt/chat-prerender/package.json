{"name": "vite-project", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "test": "npm run test:integration && npm run test:unit", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "test:integration": "playwright test", "test:unit": "vitest"}, "devDependencies": {"@magidoc/plugin-svelte-marked": "^5.0.3", "@playwright/test": "^1.28.1", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-static": "^3.0.2", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^3.0.0", "@tiptap/core": "^2.4.0", "@tiptap/extension-document": "^2.4.0", "@tiptap/extension-hard-break": "^2.4.0", "@tiptap/extension-history": "^2.4.0", "@tiptap/extension-mention": "^2.4.0", "@tiptap/extension-paragraph": "^2.4.0", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-text": "^2.4.0", "@tiptap/pm": "^2.4.0", "@tiptap/suggestion": "^2.4.0", "@types/eslint": "^8.56.7", "@vscode/webview-ui-toolkit": "^1.4.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "highlight.js": "^11.9.0", "lodash": "^4.17.21", "marked": "^13.0.0", "model": "link:@tiptap/pm/model", "monaco-editor": "^0.50.0", "prettier": "^3.1.1", "prettier-plugin-svelte": "^3.1.2", "prosemirror-model": "^1.21.1", "scroll": "^3.0.1", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "tippy.js": "^6.3.7", "tslib": "^2.4.1", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0-alpha.20", "vite": "^5.0.3", "vitest": "^1.2.0"}, "type": "module"}
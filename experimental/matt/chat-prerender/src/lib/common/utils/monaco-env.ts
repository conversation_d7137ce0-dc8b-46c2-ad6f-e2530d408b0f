import editor<PERSON>orker from "monaco-editor/esm/vs/editor/editor.worker?worker&inline";
import json<PERSON>orker from "monaco-editor/esm/vs/language/json/json.worker?worker&inline";
import cssWorker from "monaco-editor/esm/vs/language/css/css.worker?worker&inline";
import htmlWorker from "monaco-editor/esm/vs/language/html/html.worker?worker&inline";
import tsWorker from "monaco-editor/esm/vs/language/typescript/ts.worker?worker&inline";

/**
 * MonacoEnvironment is a global variable that is used by Monaco to load the
 * workers. See the below link for an example with Vite + React.
 *
 * This registers workers for languages we want to support in a Web Worker environment.
 * Note that if Web Workers are not supported, editors will fall back to the main thread.
 * Generally this should not be a problem as we will be displaying fairly small amounts of
 * code, and don't have many of the complex editing requirements a fully featured
 * editor may need.
 *
 * https://github.com/microsoft/monaco-editor/blob/main/samples/browser-esm-vite-react/src/userWorker.ts
 */
self.MonacoEnvironment = {
    getWorker: function (_: string, label: string) {
        switch (label) {
            case "json":
                return new jsonWorker();
            case "css":
            case "scss":
            case "less":
                return new cssWorker();
            case "html":
            case "handlebars":
            case "razor":
                return new htmlWorker();
            case "typescript":
            case "javascript":
                return new tsWorker();
            default:
                return new editorWorker();
        }
    },
};

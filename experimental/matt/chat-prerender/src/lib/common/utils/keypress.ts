/**
 *
 * @param key: singular key to match when any keyboard event happens
 * @param callback: callback to call when the keyboard event happens
 * @returns A wrapped callback that filters keyboard events out and calls the callback
 */
export function onKey(key: string, callback: () => void) {
    return (e: KeyboardEvent): boolean => {
        if (e.shiftKey) {
            return false; // Not handled
        }
        if (e.key === key) {
            callback();
            return true; // Handled
        }
        return false; // Not handled
    };
}

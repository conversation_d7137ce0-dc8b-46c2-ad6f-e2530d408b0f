function attachThemeObserver(cb: (theme: string | null) => void): MutationObserver {
    const wrappedCb = () => {
        const theme = document.body.getAttribute("data-vscode-theme-kind");
        cb(theme);
    };
    wrappedCb();

    const observer = new MutationObserver(wrappedCb);
    observer.observe(document.body, {
        attributeFilter: ["data-vscode-theme-kind"],
        attributes: true,
    });
    return observer;
}

export default attachThemeObserver;

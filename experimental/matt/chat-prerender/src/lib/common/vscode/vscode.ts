import type { WebViewMessage } from "../../../../../../../clients/vscode/src/webview-providers/webview-messages";

declare global {
    var acquireVsCodeApi: () => VSCodeInterface;
}

const vscode = undefined;
/* if (this['self'] && this['self']["acquireVsCodeApi"]) {
    // When running in a normal browser window, this may be undefined.

    vscode = this['self']["acquireVsCodeApi"]() as VSCodeInterface;
}*/
export default vscode;

interface VSCodeInterface {
    postMessage(data: WebViewMessage): void;
    getState(): any | undefined;
    setState(state: any): void;
}

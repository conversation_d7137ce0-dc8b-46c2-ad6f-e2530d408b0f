<script lang="ts">
    import { on<PERSON><PERSON><PERSON> } from "svelte";
    import Monaco from "./Monaco.svelte";
    import hljs from "highlight.js";
    import { editor, languages, Uri } from "monaco-editor";

    export let text: string;
    export let lang: string | undefined = undefined;
    export let pathName: string | undefined = undefined;
    export let options: Partial<editor.IStandaloneEditorConstructionOptions> = {};
    export let editorInstance: editor.IStandaloneCodeEditor | undefined = undefined;

    const supportedLanguages: string[] = languages.getLanguages().map((lang) => lang.id);

    // We use HLJS to guess the language and highlight if we are not passed the language by the model
    $: composedLang =
        lang && supportedLanguages.includes(lang)
            ? lang
            : hljs.highlightAuto(text, supportedLanguages).language;

    // Keep the URI updated with the path name. If the URI ever changes, we need to re-create the model
    let model: editor.ITextModel | undefined;
    let uri: Uri | undefined;
    const updateUri = (pathName: string | undefined) => {
        // We have a new pathName, so we need to create a new URI and model for Monaco
        const uri = pathName
            ? Uri.parse(`file://${pathName}#${crypto.randomUUID()}`)
            : Uri.parse(`file://#${crypto.randomUUID()}`);
        model?.dispose();
        model = editor.createModel(text, composedLang, uri);
    };
    $: updateUri(pathName);

    /**
     * When model info mutates, we need to update the model instead of re-creating it and
     * resetting it, as that involves a lot of teardown/setup that can cause UI flickering.
     */
    $: model?.setValue(text);
    $: {
        if (model && composedLang) {
            editor.setModelLanguage(model, composedLang);
        }
    }

    onDestroy(() => {
        model?.dispose();
    });
</script>

{#if model}
    <Monaco {options} {model} bind:editorInstance />
{/if}

<script lang="ts">
    import tooltip, { type TooltipProps } from "../../tooltips/tooltip";
    import Filespan from "../../Filespan.svelte";

    export let filepath: string | undefined = undefined;
    export let activeClientRect: DOMRect | undefined = undefined;

    type FileSpanProps = { filepath: string; className?: string };
    type IProps = TooltipProps<typeof Filespan, FileSpanProps, FileSpanProps>;
    const fileSpanTooltip = (node: HTMLElement, props: IProps) => {
        const baseTooltip = tooltip(node, props);
        const showOrHide = (props: IProps) => {
            if (props.componentProps.filepath) {
                baseTooltip.show();
            } else {
                baseTooltip.hide();
            }
        };

        return {
            update: (props: IProps) => {
                baseTooltip.update(props);
                showOrHide(props);
            },
            destroy: baseTooltip.destroy,
        };
    };
</script>

<!-- Add the tooltip to the body -->
<div
    use:fileSpanTooltip={{
        tippy: {
            getReferenceClientRect: () => activeClientRect || new DOMRect(),
            placement: "top",
            popperOptions: {
                strategy: "fixed",
            },
        },
        component: Filespan,
        componentProps: {
            filepath: filepath || "",
            className: "c-context-chip-tooltip-filespan",
        },
    }}
/>

<style>
    :global(.c-context-chip-tooltip-filespan) {
        background-color: var(--vscode-dropdown-background);
        padding: var(--p-1);
    }
</style>

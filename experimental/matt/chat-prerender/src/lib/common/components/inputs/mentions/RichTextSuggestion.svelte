<script lang="ts">
    import type {
        SuggestionProps,
        SuggestionOptions,
        SuggestionKeyDownProps,
    } from "@tiptap/suggestion";

    import type { FindFilesFn } from "../../../../apps/chat/models/types";
    import { type FileDetails } from "../../../../../../../../../clients/vscode/src/webview-providers/webview-messages";

    import tooltip from "../../tooltips/suggestionTooltip";
    import SuggestionTooltip from "../../tooltips/FileSpanSuggestions.svelte";

    // A function to retrieve suggestion items given a text query.
    // This allows users of this component to inject custom suggestion retrival logic
    export let getSuggestionItems: FindFilesFn;

    /**
     * This component is a bridge between TipTap (prop/lifecycle management) and Svelte
     * (which handles rendering). It is structured in the following order:
     * - Svelte cache points for props from TipTap to enable TipTap to set/update/etc. props
     * - Helper functions that modify these cache points, such as the selected item, for TipTap to call
     * - TipTap options, which give it hooks to modify/update these Svelte props
     * - The actual Svelte component, which takes the props and performs rendering of a tooltip
     */

    /**
     * Expose the @tiptap/suggestion props to Svelte.
     * Since tiptap controls the rendering lifecycle, we store the props here, and hand over
     * control of the props to the tiptap component. Here, we take the props, and use
     * them to render whatever we want.
     */
    let suggestionProps: SuggestionProps | undefined;
    const setProps = (props: SuggestionProps) => {
        suggestionProps = props;
    };

    // Support key up and key down events to select through the dropdown using only the keyboard
    let selectedIdx: number = 0;
    $: items = suggestionProps?.items || [];
    $: selectedItem = items?.[selectedIdx];
    const onKeyDown = (props: SuggestionKeyDownProps) => {
        // Don't do anything if we have no suggestions
        if (items.length === 0) {
            return false;
        }
        switch (props.event.key) {
            case "ArrowDown":
                selectedIdx = (selectedIdx + 1) % items.length;
                return true;
            case "ArrowUp":
                selectedIdx = (items.length + selectedIdx - 1) % items.length;
                return true;
            case "Tab":
                suggestionProps?.command(selectedItem);
                selectedIdx = 0;
                return true;
            case "Enter":
                props.event.stopPropagation();
                suggestionProps?.command(selectedItem);
                selectedIdx = 0;
                return true;
        }
        return false;
    };

    /**
     * Cache point for the client rect from tiptap. If reference changes,
     * we need to recompute + propagate to tippy. There is a bit of type
     * coersion below, as tiptap does not guarantee a DOMRect return from the
     * clientRect function, but tippy does expect a valid DOMRect if a
     * getReferenceClientRect function is provided.
     */
    let getReferenceClientRect: (() => DOMRect) | null = null;
    $: {
        const clientRect = suggestionProps?.clientRect?.() || null;
        getReferenceClientRect = clientRect ? () => clientRect : null;
    }

    // Expose the suggestion plugin options from TipTap. These are used
    // to configure the plugin.
    export const suggestionOpts: Omit<SuggestionOptions, "editor"> = {
        allowedPrefixes: [" ", "\t", "\n"],
        items: async ({ query }: { query: string }): Promise<FileDetails[]> => {
            return await getSuggestionItems({ rootPath: "", relPath: query });
        },
        // Render should naively set the props such that Svelte can handle
        // the remainder of actual DOM rendering logic
        render: () => ({
            onStart: setProps,
            onUpdate: setProps,
            onKeyDown: onKeyDown,
            onExit: () => {
                suggestionProps = undefined;
            },
        }),
    };
</script>

<!-- Add the tooltip to the body -->
<div
    use:tooltip={{
        tippy: {
            getReferenceClientRect,
            placement: "top",
            popperOptions: {
                strategy: "fixed",
            },
        },
        component: SuggestionTooltip,
        componentProps: {
            suggestions: items,
            selectedIdx: selectedIdx,
            onSelectItem: suggestionProps?.command,
        },
    }}
/>

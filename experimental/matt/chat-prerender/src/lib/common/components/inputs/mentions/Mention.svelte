<script lang="ts">
    import { type Node as ProseMirrorNode } from "@tiptap/pm/model";
    import { Mention, type MentionOptions } from "@tiptap/extension-mention";

    import type { FindFilesFn } from "../../../../apps/chat/models/types";

    import RichTextSuggestion from "./RichTextSuggestion.svelte";
    import FileChipTooltip from "./FileChipTooltip.svelte";
    import type { CommandArgs } from "../types";
    import { getMentionNodes } from "./utils";
    import type { IQualifiedPathName } from "../../../../../../../../clients/vscode/src/workspace/types";

    export let getSuggestionItems: FindFilesFn;
    export let updateFilepaths: (
        added: IQualifiedPathName[],
        removed: IQualifiedPathName[],
    ) => void;

    // A cache point for the active hover filepath and client rect. This
    // is set by TipTap's Mention extension, and is used to determine when a TipTap
    // span is hovered over, and triggers the tooltip.
    let activeHoverFilepath: string | undefined = undefined;
    let activeHoverClientRect: DOMRect | undefined = undefined;
    let mentionedFiles: IQualifiedPathName[] = [];
    const updateMentionedFiles = (files: IQualifiedPathName[]): void => {
        updateFilepaths(files, mentionedFiles);
        mentionedFiles = files;
    };

    let richTextSuggestion: RichTextSuggestion | undefined;
    export const getCustomMention = () => {
        return Mention.extend({
            addAttributes() {
                return {
                    ...this.parent?.(),
                    data: {
                        default: null,
                    },
                };
            },
            // When the input box updates, update the list of mentioned files
            onUpdate() {
                updateMentionedFiles(
                    getMentionNodes(this.editor).map((n) => {
                        return {
                            rootPath: n.attrs.data.repoRoot,
                            relPath: n.attrs.data.pathName,
                        };
                    }),
                );
            },
            addOptions() {
                const parentOpts = this.parent?.();
                return {
                    ...parentOpts,
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    HTMLAttributes: {
                        class: "c-context-chip",
                    },
                    // Render the mention as a span with the HTML attributes, and add
                    // a mouseenter/mouseleave listener to trigger/hide the file chip tooltip
                    renderHTML: ({
                        options,
                        node,
                    }: {
                        options: MentionOptions;
                        node: ProseMirrorNode;
                    }) => {
                        const span = document.createElement("span");
                        span.innerText = node.attrs.label;
                        for (const [key, value] of Object.entries(options.HTMLAttributes)) {
                            span.setAttribute(key, value);
                        }

                        span.addEventListener("mouseenter", () => {
                            activeHoverFilepath = node.attrs.id;
                            activeHoverClientRect = span.getBoundingClientRect();
                        });
                        span.addEventListener("mouseleave", () => {
                            activeHoverFilepath = undefined;
                            activeHoverClientRect = undefined;
                        });

                        return span;
                    },
                    // Add the suggestion plugin options to the mention plugin
                    suggestion: {
                        ...parentOpts?.suggestion,
                        ...richTextSuggestion?.suggestionOpts,
                        command: ({ editor, range, props: file }: CommandArgs) => {
                            parentOpts.suggestion?.command?.({
                                editor,
                                range,
                                props: {
                                    id: file.pathName,
                                    label: file.pathName.split("/").pop() || "",
                                    data: file,
                                },
                            });
                        },
                    },
                };
            },
        });
    };
</script>

<RichTextSuggestion bind:this={richTextSuggestion} {getSuggestionItems} />
<FileChipTooltip filepath={activeHoverFilepath} activeClientRect={activeHoverClientRect} />

<style>
    :global(.tiptap .c-context-chip) {
        display: inline-block;
        padding: 0 var(--p-1);
        margin: var(--p-1) 0;
        border-radius: var(--p-1);
        background-color: var(--vscode-badge-background);

        /* Enable full line wrapping */
        word-break: break-all;
        white-space: pre-wrap;

        /* Hover on chips should not be a text selector */
        cursor: default;
    }
</style>

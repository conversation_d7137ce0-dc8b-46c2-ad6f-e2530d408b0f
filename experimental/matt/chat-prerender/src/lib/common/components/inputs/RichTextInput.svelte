<script lang="ts">
    import { onMount, onDestroy } from "svelte";
    import { Editor, type EditorEvents } from "@tiptap/core";
    import Document from "@tiptap/extension-document";
    import HardBreak from "@tiptap/extension-hard-break";
    import History from "@tiptap/extension-history";
    import Paragraph from "@tiptap/extension-paragraph";
    import Text from "@tiptap/extension-text";
    import Placeholder from "@tiptap/extension-placeholder";

    import type { JSONContent, KeyboardShortcutCommand } from "@tiptap/core";
    import type { FindFilesFn } from "../../../apps/chat/models/types";
    import Mention from "./mentions/Mention.svelte";
    import type { IQualifiedPathName } from "../../../../../../../../clients/vscode/src/workspace/types";

    /**
     * This component is a wrapper around the Tiptap editor.
     * It is used to provide a consistent API for the editor, and expose it
     * in a Svelte-y way for consumers of this common component.
     *
     * See https://tiptap.dev/ for more details.
     */

    // Let users bind to the editor directly
    export let editor: Editor | undefined = undefined;
    let element: HTMLElement;

    export let editable: boolean = true;
    export let getSuggestionItems: FindFilesFn = () => Promise.resolve([]);
    export let updateFilepaths: (
        added: IQualifiedPathName[],
        removed: IQualifiedPathName[],
    ) => void = () => {};

    // Let parent component bind to editor
    export let onContentChanged: (content: string) => void;
    export let onFocus: () => void;
    export let onBlur: () => void;

    // Some common key handlers
    export let onEnter: KeyboardShortcutCommand = () => false;

    // Expose functions to parent components
    let focusOnMount: boolean = false;

    export const focus = () => {
        if (!editor) {
            focusOnMount = true;
            return;
        }
        editor?.commands.focus();
    };
    export const blur = () => editor?.commands.blur();
    export const clearContent = () => editor?.commands.clearContent();
    export const setContent = (content: string) =>
        editor?.commands.setContent(content, false, { preserveWhitespace: true });

    // Sync editor state with props
    $: editor?.setEditable(editable);

    // Format raw text for TipTap to use. See comments below. This solves the issue
    // where the raw text itself contains HTML tags, which is open for HTML injection.
    // It also prevents newlines from being interpreted and parsed into new paragraph blocks.
    const maybeGetTextContent = (rawText: string): JSONContent[] => {
        return rawText.length === 0 ? [] : [{ type: "text", text: rawText }];
    };
    const formatRawText = (rawText: string): JSONContent[] => {
        return rawText
            .split("\n")
            .map((line: string, index: number) => {
                if (index !== 0) {
                    return [{ type: "hardBreak" }, ...maybeGetTextContent(line)];
                }
                return maybeGetTextContent(line);
            })
            .flat();
    };

    // Initialize the editor on mount
    let mentionElement: Mention;
    onMount(() => {
        editor = new Editor({
            injectCSS: true,
            element,
            // Enable placeholders, undo/redo, and basic formatting
            extensions: [
                Document.extend({
                    addKeyboardShortcuts() {
                        return {
                            // eslint-disable-next-line @typescript-eslint/naming-convention
                            Enter: (props) => onEnter(props),
                        };
                    },
                }),
                Paragraph,
                Text,
                HardBreak,
                Placeholder.configure({
                    placeholder: "Type a message... @ to add files to current context",
                }),
                History.configure({
                    depth: 100,
                    newGroupDelay: 750,
                }),
                mentionElement.getCustomMention(),
            ],
            // Handle a new transaction
            onUpdate: (props: EditorEvents["update"]) => {
                const e = props.editor;
                onContentChanged(
                    e.getText({
                        textSerializers: {
                            mention: ({ node }) => node.attrs.id,
                        },
                    }),
                );
            },
            onFocus,
            onBlur,
            editorProps: {
                /* Until we support full rich text editing, we want to ensure a few guarantees:
                1. All text on clipboard is interpreted as plaintext. Copies from e.g. vscode will be
                   rich text on the clipboard, so we need to get out plaintext manually or else
                   TipTap will interpret it as rich text.
                2. All pastes should be done with plaintext. Right now, TipTap will try to do some
                   intelligent detection of if the clipboard has rich text or plain text, and route to
                   choose the appropriate parser. There are two problems: the default parsers for both
                   plaintext and rich text are the same, and will attempt to insert paragraphs, etc., and
                   there are no flags for disabling this behavior.

                TODO(Eric): Remove this when we support full rich text editing
                   */
                handlePaste: (_, event: ClipboardEvent, __) => {
                    // All data going into/out of tiptap should be interpreted as plaintext
                    const rawText = event.clipboardData?.getData("text/plain");
                    if (rawText) {
                        editor?.commands.insertContent(formatRawText(rawText));
                        return true;
                    }
                    return false;
                },
                attributes: {
                    style: "min-height: 100%; outline: none;",
                },
            },
        });
        if (focusOnMount) {
            focus();
        }
    });

    onDestroy(() => {
        editor?.destroy();
    });
</script>

<!-- Container for positioning rich text components inside the input -->
<div class="c-rich-text-input" bind:this={element} />
<Mention bind:this={mentionElement} {updateFilepaths} {getSuggestionItems} />

<style>
    .c-rich-text-input {
        position: relative;

        /* No horizontal scroll */
        width: 100%;
        overflow-x: hidden;
        overflow-y: auto;
    }

    /* Remove top margin from first child for better scrolling behavior */
    :global(.tiptap > :first-child) {
        margin-top: 0;
    }
    /* Remove bottom margin from last child for better scrolling behavior */
    :global(.tiptap > :last-child) {
        margin-bottom: 0;
    }

    /* Needed for placeholder support. See
    https://tiptap.dev/docs/editor/api/extensions/placeholder
    for more details */
    :global(.tiptap p.is-editor-empty:first-child::before) {
        opacity: 0.5;
        color: var(--vscode-foreground);
        content: attr(data-placeholder);
        float: left;
        height: 0;
        pointer-events: none;
    }
</style>

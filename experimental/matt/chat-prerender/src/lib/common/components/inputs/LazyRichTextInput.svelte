<script lang="ts">
    import type { Editor, KeyboardShortcutCommand } from "@tiptap/core";
    import type RichTextInput from "./RichTextInput.svelte";
    import { afterUpdate } from "svelte";

    // Props required by RichTextInput
    export let onContentChanged: (content: string) => void;
    export let onFocus: () => void;
    export let onBlur: () => void;

    // Some common key handlers
    export let onEnter: KeyboardShortcutCommand = () => false;

    // Bind to the editor directly
    export let editor: Editor | undefined = undefined;
    let component: RichTextInput;
    let focusOnMount: boolean = false;

    afterUpdate(() => {
        if (focusOnMount && component) {
            component.focus();
            focusOnMount = false;
        }
    });

    export const focus = () => {
        if (!component) {
            focusOnMount = true;
        } else {
            component?.focus();
        }
    };
    export const blur = () => component?.blur();
    export const clearContent = () => component?.clearContent();
</script>

{#await import("./RichTextInput.svelte") then c}
    <svelte:component
        this={c.default}
        bind:this={component}
        bind:editor
        {onContentChanged}
        {onFocus}
        {onBlur}
        {onEnter}
        {...$$restProps}
    />
{/await}

<script lang="ts">
    import { onDestroy, onMount } from "svelte";
    import type * as Monaco from "monaco-editor/esm/vs/editor/editor.api";
    import attachThemeObserver from "$lib/common/syntax-highlighting/observe-theme";
    import { editor } from "monaco-editor";

    const VSCODE_THEME_TO_MONACO_THEME: Map<string, string> = new Map([
        ["vscode-light", "vs"],
        ["vscode-dark", "vs-dark"],
        ["vscode-high-contrast", "hc-black"],
        ["vscode-high-contrast-light", "hc-light"],
    ]);
    const DEFAULT_OPTIONS: Monaco.editor.IStandaloneEditorConstructionOptions = {
        tabSize: 4,
        readOnly: true,
        scrollBeyondLastLine: false,
        // Disable the right hand overview of the editor contents
        minimap: {
            enabled: false,
        },
        scrollbar: {
            alwaysConsumeMouseWheel: false,
            vertical: "hidden",
        },
        // Hide
        wordWrap: "on",
        theme: "vs-dark",
        unicodeHighlight: {
            ambiguousCharacters: false,
            invisibleCharacters: false,
        },
        overviewRulerBorder: false,
        lineNumbers: "on",
        // Sets the minimum character space for line numbers to 3.
        // This value auto-adjusts if more digits are needed to fit the line count.
        // Default value is 5, which feels like an overkill
        lineNumbersMinChars: 3,
        lineDecorationsWidth: 10,
        // No reason to have folding code blocks are typically short
        folding: false,
    };

    export let options: Partial<Monaco.editor.IStandaloneEditorConstructionOptions> = {};
    export let model: Monaco.editor.ITextModel;
    export let decorations: Monaco.editor.IModelDeltaDecoration[] | undefined = undefined;

    // Store all monaco state. Allow external users to bind to the editor
    export let editorInstance: Monaco.editor.IStandaloneCodeEditor | undefined = undefined;
    let editorContainer: HTMLElement;
    let resizeObserver: ResizeObserver;
    let themeObserver: MutationObserver;
    let decoratorCollection: Monaco.editor.IEditorDecorationsCollection | undefined = undefined;

    const updateEditorHeight = () => {
        if (!editorContainer || !editor) return;

        const heightPx = editorInstance?.getContentHeight();
        editorContainer.style.height = `${heightPx}px`;
        editorInstance?.layout();
    };

    const replaceDecorations = (decorations: Monaco.editor.IModelDeltaDecoration[]) => {
        decoratorCollection?.clear();
        if (decorations) {
            decoratorCollection = editorInstance?.createDecorationsCollection(decorations);
        }
    };

    /**
     * Performs all of the setup for a new model when it gets passed in. This is called for
     * both initialization and subsequent model updates.
     * @param model
     */
    const setupNewModel = (
        model: Monaco.editor.ITextModel,
        decorations: Monaco.editor.IModelDeltaDecoration[],
    ) => {
        if (!editorInstance) {
            return;
        }

        editorInstance.setModel(model);
        replaceDecorations(decorations);
        updateEditorHeight();
        model.onDidChangeContent(updateEditorHeight);
    };
    // Whenever model changes, we want to try setting it up again
    $: setupNewModel(model, decorations || []);

    onMount(async () => {
        const opts = { ...DEFAULT_OPTIONS, ...options };
        editorInstance = editor.create(editorContainer, opts);
        setupNewModel(model, decorations || []);

        themeObserver = attachThemeObserver((theme) => {
            const monacoTheme = VSCODE_THEME_TO_MONACO_THEME.get(
                theme || DEFAULT_OPTIONS.theme || "",
            );
            editorInstance?.updateOptions({ theme: monacoTheme });
            editorInstance?.layout();
        });

        editorInstance.onDidChangeModel(updateEditorHeight);
        resizeObserver = new ResizeObserver(updateEditorHeight);
        resizeObserver.observe(editorContainer);
    });

    onDestroy(() => {
        editorInstance?.dispose();
        themeObserver?.disconnect();
        resizeObserver?.disconnect();
    });
</script>

<div class="c-codeblock">
    <div class="c-codeblock__monaco" bind:this={editorContainer} />
</div>

<style>
    .c-codeblock {
        position: relative;
    }

    .c-codeblock__monaco {
        --border-width: 1px;
        width: calc(100% - (2 * var(--border-width)));
        border: var(--border-width) solid var(--vscode-editorWidget-border);
        box-sizing: content-box;
    }

    /**
   * Monaco editor adds a `focused` class to the editor container when it is focused.
   */
    .c-codeblock__monaco:has(.monaco-editor.focused) {
        border: 1px solid var(--vscode-focusBorder);
    }

    :global(.c-codeblock:has(.monaco-editor.focused) ~ .c-codeblocks__bottom-action-bar) {
        border-color: var(--vscode-focusBorder);
    }

    :global(.c-codeblock__monaco .monaco-editor .unexpected-closing-bracket) {
        color: inherit;
    }
</style>

<script lang="ts">
    import type { Tokens } from "marked";
    import vscode from "$lib/common/vscode/vscode";

    import { editor, Range, Selection } from "monaco-editor";
    import SimpleMonaco from "./SimpleMonaco.svelte";
    import CodeblockActions from "./CodeblockActions.svelte";
    import CodeblockButton from "./CodeblockButton.svelte";

    export let token: Tokens.Code;

    let editorInstance: editor.IStandaloneCodeEditor | undefined;
    const getSelectionOrContents = (): string => {
        if (!editorInstance) {
            return "";
        }
        // If no selections, return the entire code block
        const selections = editorInstance.getSelections();
        if (!selections?.length) {
            return editorInstance.getValue() || "";
        }

        // If selections cover no characters, return the entire code block
        const model = editorInstance.getModel();
        const totalSelectedCharacters = selections
            .map((s: Selection) => model?.getValueLengthInRange(s) || 0)
            .reduce((a: number, b: number) => a + b, 0);
        if (totalSelectedCharacters === 0) {
            return editorInstance.getValue() || "";
        }

        // If selections have characters, return the selected code
        const selectionValues = selections
            .sort(Range.compareRangesUsingStarts)
            .map((s: Selection) => model?.getValueInRange(s) || "");
        return selectionValues.join("\n");
    };

    const onCopy = () => {
        // Copies the code block to the clipboard.
        // Refer to the comments in the `onInsertCodeblockCode` function for additional details.
        navigator.clipboard.writeText(getSelectionOrContents());
    };

    const onInsertCodeblockCode = () => {
        // Inserts code into the editor, potentially replacing any code currently selected.
        /*vscode.postMessage({
            type: WebViewMessageType.chatInsertCodeblockCode,
            data: getSelectionOrContents(),
        });*/
    };
</script>

<div class="c-codeblock">
    <SimpleMonaco text={token.text} lang={token.lang} bind:editorInstance />
    <CodeblockActions>
        <CodeblockButton
            title="Copy to clipboard"
            icon="file_copy"
            onClick={onCopy}
            onSuccessMessage="Copied!"
        />
        <CodeblockButton
            title="Insert code block"
            icon="format_indent_increase"
            onClick={onInsertCodeblockCode}
            onSuccessMessage="Inserted!"
        />
    </CodeblockActions>
</div>

<style>
    .c-codeblock {
        position: relative;
    }
</style>

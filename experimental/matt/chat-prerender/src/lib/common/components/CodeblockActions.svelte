<script lang="ts">
    import { setContext } from "svelte";

    let messageText: string;
    let timeoutID: NodeJS.Timeout | undefined;
    const MSG_DURATION_MS = 2500;

    // This function sets the message text and starts a timeout to clear the message after a certain duration
    let setMessageText = (text: string) => {
        messageText = text;
        if (timeoutID) {
            clearTimeout(timeoutID);
        }

        // A new timeout is set to clear the message after the specified duration
        // The ID of this new timeout is stored in the timeoutID variable
        timeoutID = setTimeout(() => {
            messageText = "";
        }, MSG_DURATION_MS);
    };

    setContext("augment-codeblock-actions-message", { setMessageText });
</script>

<div class="c-codeblocks__bottom-action-bar">
    <slot />
    <div class="c-codeblocks__action-bar-status">
        {messageText || ""}
    </div>
</div>

<style>
    .c-codeblocks__bottom-action-bar {
        background-color: var(--vscode-editor-background);
        border: 1px solid var(--vscode-editorWidget-border);
        border-top: none;
        padding: 0.2em;
        display: flex;
    }

    .c-codeblocks__action-bar-status {
        margin: 0.2em 0.5em;
        height: 100%;
        display: inline-block;
        font-size: 1em;
        align-items: center;
    }
</style>

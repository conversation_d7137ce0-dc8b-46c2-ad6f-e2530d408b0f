<script lang="ts">
    import MaterialIcon from "./MaterialIcon.svelte";

    export let className: string = "";
    export let filepath: string;

    // Remove all empty parts from the path
    $: parts = filepath.split("/");
    $: filename = parts[parts.length - 1];
    $: dir = parts.slice(0, parts.length - 1).join("/");
</script>

<div class={`c-filespan ${className}`}>
    <slot name="leftIcon">
        <MaterialIcon iconName="draft" />
    </slot>
    <span class="c-filespan__filename">{filename}</span>
    <div class="c-filespan__dir">
        <!-- We wrap directory in RTL for ellipses on left, but the actual
        text contents are LTR, so we need to wrap the contents -->
        <div class="c-filespan__dir-text">{dir}</div>
    </div>
    <slot name="rightIcon" />
</div>

<style>
    .c-filespan {
        display: flex;
        width: 100%;

        flex-direction: row;
        align-items: center;
    }
    .c-filespan > .c-filespan__filename,
    .c-filespan > .c-filespan__dir {
        /* This will always attempt to take up any available space (grow 0)
        but if it needs to shrink, it will do so after the filename since it has
        a non-zero flex-basis */
        flex: 0 1 auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0 var(--p-1);
    }
    .c-filespan > .c-filespan__dir {
        /* This will allow the directory to grow after the filename,
        and will shrink before the filename */
        flex: 1 1 0px;

        overflow: hidden;
        display: inline-block;

        /* We want the ellipsis to be on the left side, so we do RTL */
        text-overflow: ellipsis;
        direction: rtl;
        text-align: left;

        /* Lighter color for the directory path */
        opacity: 50%;
    }

    .c-filespan > .c-filespan__dir > .c-filespan__dir-text {
        display: inline;
        width: fit-content;
        direction: ltr;
    }

    .c-filespan > :global(:first-child),
    :global(.material-symbols-outlined:first-child) {
        margin-left: 0;
    }

    .c-filespan > :global(:last-child),
    :global(.material-symbols-outlined:last-child) {
        margin-right: 0;
    }
</style>

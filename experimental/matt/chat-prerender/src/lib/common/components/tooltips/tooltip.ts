import tippy, { type Props as <PERSON>ip<PERSON><PERSON><PERSON> } from "tippy.js";
import type { SvelteComponent } from "svelte";

/**
 * P and Q are both types of props, but P is used to type the props for the component,
 * and Q is used to type the props passed into the tooltip. This allows for type inference --
 * the props that are passed in as Q may be more strict than that of P (for example, more required
 * props), but Q can still conform to P, which is what the component expects
 */
export interface TooltipProps<
    T extends typeof SvelteComponent<P>,
    P extends { [key: string]: any },
    Q extends P,
> {
    tippy: Partial<Omit<TippyProps, "content">>;
    component: T;
    componentProps: Q;
}

/**
 * @param node the target HTMLElement to apply the tooltip to. Generally, this
 *             should be the body element, since tooltips are usually displayed globally
 *             but positioned relative to some other element.
 * @param props
 */

export default function tooltip<
    T extends typeof SvelteComponent<P>,
    P extends { [key: string]: any },
    Q extends P,
>(node: HTMLElement, props: TooltipProps<T, P, Q>) {
    // Create a new DOM target to render onto
    const svelteTarget = document.createElement("div");
    svelteTarget.className = "tooltip-target";
    const contents = new props.component({
        target: svelteTarget,
        props: { ...props.componentProps },
    });

    // Instantiate tippy instance with the content as our new svelte target
    const tooltip = tippy(node, {
        ...props.tippy,
        content: svelteTarget,
        interactive: true,
    });

    // Return a function to update the tooltip and a destructor
    return {
        ...tooltip,
        update: (props: TooltipProps<T, P, Q>) => {
            contents.$set({ ...props.componentProps });
            tooltip.setProps({
                ...props.tippy,
                content: svelteTarget,
            });
        },
        destroy: () => {
            tooltip.destroy();
            contents.$destroy();
        },
    };
}

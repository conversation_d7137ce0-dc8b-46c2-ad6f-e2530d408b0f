import tooltip, { type TooltipProps } from "./tooltip";
import type { SvelteComponent } from "svelte";

/**
 * This file is a wrapper around the tooltip.ts file that performs type narrowing and
 * some props adjustment for suggestion-like tooltips. It is a generic component and is not
 * opinionated on what component is used to render the contents -- it is up to the caller to
 * provide the component and props for the contents.
 *
 * Handled for `suggestionTooltip` user:
 * - Showing/hiding the tooltip based on whether there are suggestions or not
 * - Updating the tippy props to avoid tippy taking control of showing the tooltip
 *
 * This means different types of suggestion dropdowns are possible, and this is simply used to
 * provide a means of attaching a generic suggestion tooltip to a target element.
 */

// We use an `I extends any` instead of just `I` to avoid null, undefined, etc. types
export interface SuggestionComponentProps<I extends any> {
    suggestions: I[];
    selectedIdx?: number;
    onSelectItem?: (item: I) => void;
}

// Type narrowing for suggestion tooltips
export interface SuggestionTooltipProps<
    T extends typeof SvelteComponent<P>,
    P extends SuggestionComponentProps<any>,
    Q extends P,
> extends TooltipProps<T, P, Q> {
    // We exclude these props because we *only* want to trigger tippy manually.
    // Specifically, we only want to show the tooltip if there are suggestions.
    tippy: Partial<Omit<TooltipProps<T, P, Q>["tippy"], "trigger" | "showOnCreate">>;
}

export default function suggestionTooltip<
    T extends typeof SvelteComponent<P>,
    P extends SuggestionComponentProps<any>,
    Q extends P,
>(node: HTMLElement, props: SuggestionTooltipProps<T, P, Q>) {
    // We show the tooltip only if there are suggestions,
    // so we need to take control of triggering the tooltip here.
    const injectProps = (props: SuggestionTooltipProps<T, P, Q>) => {
        return {
            ...props,
            tippy: {
                ...props.tippy,
                trigger: "manual",
                showOnCreate: props.componentProps.suggestions.length > 0,
            },
        };
    };

    const baseTooltip = tooltip<T, P, Q>(node, injectProps(props));

    return {
        update: (props: SuggestionTooltipProps<T, P, Q>) => {
            props = injectProps(props);
            baseTooltip.update(props);

            // If we have suggestions, we show them, otherwise we hide them.
            if (props.componentProps.suggestions.length > 0) {
                baseTooltip.show();
            } else {
                baseTooltip.hide();
            }
        },
        destroy: baseTooltip.destroy,
    };
}

<script lang="ts">
    import { type SuggestionComponentProps } from "./suggestionTooltip";
    import Filespan from "../Filespan.svelte";
    import { onKey } from "../../utils/keypress";

    type Props = SuggestionComponentProps<any>;
    export let suggestions: Props["suggestions"];
    export let selectedIdx: Props["selectedIdx"];
    export let onSelectItem: Props["onSelectItem"];
</script>

<div class="c-dropdown-contents">
    {#if suggestions}
        <div class="c-inline-dropdown">
            {#each suggestions as option, index}
                <vscode-option
                    role="button"
                    value={option}
                    selected={selectedIdx === index}
                    on:click={() => onSelectItem?.(option)}
                    on:keydown={onKey("Enter", () => onSelectItem?.(option))}
                    tabindex={index}
                >
                    <Filespan filepath={option.pathName} />
                </vscode-option>
            {/each}
        </div>
    {/if}
</div>

<style>
    .c-inline-dropdown {
        border: 1px solid var(--vscode-dropdown-border);
        background: var(--vscode-dropdown-background);
        display: flex;
        flex-direction: column;
        gap: var(--p-1);
    }

    /* Padding should be small for an inline dropdown */
    vscode-option {
        padding-left: var(--p-1);
        padding-right: var(--p-1);
        cursor: pointer;
    }
</style>

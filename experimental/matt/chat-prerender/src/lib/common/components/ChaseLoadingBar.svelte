<script lang="ts">
    export let loading: boolean = false;
    export let loadingText: string = "Loading...";

    /**
     * This component is used to render a horizontal bar that chases from left to right in a loop
     * The outer div handles the margins, overflow, etc.
     * The inner div is the actual bar that chases.
     * There is an optional loadingText as well.
     */
</script>

<div class="chase-loading-bar" class:chase-loading-bar--loading={loading}>
    <div class="chase-loading-bar__overflow-container">
        <div class="chase-loading-bar__inner" />
    </div>
    <div class="chase-loading-bar__text">
        <slot>
            {loadingText}
        </slot>
    </div>
</div>

<style>
    .chase-loading-bar {
        display: flex;
        flex-direction: column;
        display: none;

        overflow: hidden;
    }

    .chase-loading-bar__overflow-container {
        overflow: hidden;
    }

    .chase-loading-bar--loading {
        display: block;
    }

    .chase-loading-bar__inner {
        width: 100%;
        background-color: var(--vscode-editorMarkerNavigationInfo-background);
        border: none;
        padding-top: var(--p-0-5);
    }

    .chase-loading-bar--loading .chase-loading-bar__inner {
        animation: chase-loading-bar-inner 1.2s infinite linear;
    }

    .chase-loading-bar__text {
        margin: var(--p-1) 0;
        opacity: 50%;
    }

    @keyframes chase-loading-bar-inner {
        0% {
            transform: translateX(-100%);
        }
        100% {
            transform: translateX(100%);
        }
    }
</style>

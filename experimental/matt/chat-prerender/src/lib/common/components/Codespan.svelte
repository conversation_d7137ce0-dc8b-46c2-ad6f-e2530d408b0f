<script lang="ts">
    import { getContext } from "svelte";
    import type { Tokens } from "marked";

    import type { ResolvePathFn, OpenLocalFileFn } from "../../apps/chat/models/types";
    import { onKey } from "../utils/keypress";

    export let token: Tokens.Codespan;

    const resolvePath: ResolvePathFn = getContext("resolvePath");
    const openLocalFile: OpenLocalFileFn = getContext("openLocalFile");
    $: codespanContents = token.raw.slice(1, token.raw.length - 1);
</script>

<span>
    {#await resolvePath({ rootPath: "", relPath: codespanContents })}
        <code class="markdown-codespan">{codespanContents}</code>
    {:then maybeFileDetails}
        <code class="markdown-codespan">
            {#if maybeFileDetails}
                <span
                    class="markdown-codespan-linkable"
                    on:click={() => maybeFileDetails && openLocalFile(maybeFileDetails)}
                    on:keydown={onKey(
                        "Enter",
                        () => maybeFileDetails && openLocalFile(maybeFileDetails),
                    )}
                    role="button"
                    tabindex="0"
                >
                    {codespanContents}
                </span>
            {:else}
                {codespanContents}
            {/if}
        </code>
    {/await}
</span>

<style>
    .markdown-codespan {
        font-family: var(--vscode-editor-font-family);
        font-weight: var(--vscode-editor-font-weight);
        background-color: var(--vscode-editor-inactiveSelectionBackground);
        color: var(--vscode-editorLink-activeForeground);
        border-radius: 2px;
        padding: 0.15em 0.3em;

        /* Fixes line wrapping */
        word-break: break-all;
        display: inline;
    }

    .markdown-codespan .markdown-codespan-linkable {
        /* Style this like a link */
        cursor: pointer;
        text-decoration: underline;
    }

    .markdown-codespan > :global(.markdown-codespan-file) {
        background-color: var(--vscode-editor-inactiveSelectionBackground);
    }
</style>

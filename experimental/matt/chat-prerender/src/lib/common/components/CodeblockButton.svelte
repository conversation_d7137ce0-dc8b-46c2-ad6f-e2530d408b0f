<script lang="ts">
    import { getContext } from "svelte";
    import MaterialIcon from "./MaterialIcon.svelte";

    export let onClick: () => void;
    export let title: string;
    export let icon: string;
    export let onSuccessMessage: string = "";

    // Defined in CodeblockActions.svelte
    const codeblockActionCtx: { setMessageText: (text: string) => void } = getContext(
        "augment-codeblock-actions-message",
    );
    const onClickWrapper = () => {
        onClick();
        codeblockActionCtx.setMessageText(onSuccessMessage);
    };
</script>

<button on:click={onClickWrapper} {title}>
    <MaterialIcon iconName={icon} />
</button>

<style>
    button {
        padding: 0.2em;
        background-color: transparent;
        color: var(--vscode-editor-foreground);
        transition: background-color 0.2s; /* Smooth transition a bit */

        border-radius: 0.2em;
        border: none;
        cursor: pointer;
    }

    button:hover {
        background-color: rgba(255, 255, 255, 0.2); /* Darken the button a bit when hovered */
    }
</style>

import { type IQualifiedPathName } from "../../../../../../../../clients/vscode/src/workspace/types";
import { type FileDetails } from "../../../../../../../../clients/vscode/src/webview-providers/webview-messages";

export interface ResolvePathFn {
    (path: IQualifiedPathName): Promise<FileDetails | undefined>;
}

export interface OpenLocalFileFn {
    (details: FileDetails): void;
}

export interface FindFilesFn {
    (query: IQualifiedPathName): Promise<FileDetails[]>;
}

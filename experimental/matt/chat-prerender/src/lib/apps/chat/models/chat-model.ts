import { type J<PERSON>NContent } from "@tiptap/core";

import { type ExchangeWithStatus, ExchangeStatus } from "../types/chat-message";
import {
    type FindFileRequest,
    type FindFileResponse,
    WebViewMessageType,
    type ChatModelReply,
    type WebViewMessage,
    type ChatUserMessage,
    type ChatUserCancel,
    type ChatInitialize,
    type ChatLoaded,
    type FileDetails,
} from "../../../../../../../../clients/vscode/src/webview-providers/webview-messages";
import { type IQualifiedPathName } from "../../../../../../../../clients/vscode/src/workspace/types";
import type { ResolvePathFn, FindFilesFn } from "./types";

import { SpecialContextInputModel } from "./context-model";
import { getNonce } from "../../../../../../../../clients/vscode/src/webview-providers/webview-utils";
// import { AsyncMsgSender } from "../../../../../../../../clients/vscode/src/utils/webviews/messaging";

/**
 * This class is used to store the state of the chat webview.
 *
 * It conforms to the svelte store pattern to allow for easy integration with
 * the svelte framework.
 */
class ChatModel {
    private _state: StoredState = {
        chatHistory: [],
        draftMessage: undefined,
    };

    private _specialContextInputModel: SpecialContextInputModel;
    private subscribers: Set<(chatHistory: ChatModel) => void> = new Set();
    // private asyncMsgSender: AsyncMsgSender;

    // Chat-specific flags pulled from the extension.
    // The source of these should be either LaunchDarkly, the extension config, or chat config.
    private _enablePreferenceCollection: boolean = false;
    private _enableDebugFeatures: boolean = false;

    constructor() {
        this.initialize();
        // this.asyncMsgSender = AsyncMsgSender.createForWebview(() => {});
        this._specialContextInputModel = new SpecialContextInputModel(this.asyncMsgSender);
        this.onLoaded();
    }

    /**
     * Initialize the chat model.
     * Notifies subscribers at the end of loading.
     */
    onLoaded = async (): Promise<void> => {
        /*const chatInitData = await this.asyncMsgSender.send<ChatLoaded, ChatInitialize>({
            type: WebViewMessageType.chatLoaded,
        });
        this._enablePreferenceCollection = chatInitData.data.enablePreferenceCollection ?? false;
        this._enableDebugFeatures = chatInitData.data.enableDebugFeatures ?? false;*/
        this.initialize();
        this.notifySubscribers();
    };

    /**
     * Subscribe to the state of the chat model.
     * Conforms to the svelte store pattern.
     *
     * @param subscriber A function that will be called whenever the state changes
     */
    subscribe = (sub: (chatModel: ChatModel) => void): (() => void) => {
        this.subscribers.add(sub);
        sub(this);
        return () => {
            this.subscribers.delete(sub);
        };
    };

    private initialize = () => {
        this._state = { ...this._state, };
        // When loading in state, we need to remove any messages that are not successful
        this._state.chatHistory = this._state.chatHistory.filter(
            (m) => m.status === ExchangeStatus.success,
        );
        // vscode.setState(this._state);
    };

    private updateChatState = (state: Partial<StoredState>) => {
        this._state = { ...this._state, ...state };
        // vscode.setState(this._state);
        this.notifySubscribers();
    };

    private notifySubscribers = () => {
        this.subscribers.forEach((sub) => sub(this));
    };

    private addExchange = (exchange: ExchangeWithStatus) => {
        this.updateChatState({
            chatHistory: [...this.chatHistory, exchange],
        });
    };

    private updateExchangeById = (exchange: Partial<ExchangeWithStatus>, requestId: string) => {
        // See if the exchange exists
        const exchangeWithId = this.exchangeWithRequestId(requestId);
        if (exchangeWithId === null) {
            console.warn("No exchange with this request ID found.");
            return;
        }

        // If it exists, modify the message
        this.updateChatState({
            chatHistory: this.chatHistory.map((m: ExchangeWithStatus) =>
                m.request_id === requestId ? { ...m, ...exchange } : m,
            ),
        });
    };

    private updateLastExchangeStream = (exchangeChunk: Partial<ExchangeWithStatus>) => {
        if (this.lastExchange === null) {
            console.warn("Cannot update last exchange when chat history is empty");
            return;
        }
        const chatHistory = this._state.chatHistory.slice(0, -1);
        const lastExchange = { ...this.lastExchange };
        if (exchangeChunk.status) {
            lastExchange.status = exchangeChunk.status;
        }
        if (exchangeChunk.response_text) {
            if (!this.lastExchange.response_text) {
                lastExchange.response_text = "";
            }
            lastExchange.response_text += exchangeChunk.response_text;
        }
        chatHistory.push(lastExchange);
        this.updateChatState({ chatHistory });
    };

    private removeUnsuccessfulTurn = (turn: ExchangeWithStatus) => {
        this.updateChatState({
            chatHistory: this.chatHistory.filter(
                (m) => m !== turn || m.status === ExchangeStatus.success,
            ),
        });
    };

    clearHistory = () => {
        this.updateChatState({ chatHistory: [] });
    };

    resendUnsuccessfulTurn = (turn: ExchangeWithStatus) => {
        if (this.awaitingReply) {
            return;
        }
        this.removeUnsuccessfulTurn(turn);
        this.onSendUserMessage(turn.request_message);
    };

    private exchangeWithRequestId(requestId: string): ExchangeWithStatus | null {
        return this.chatHistory.find((m) => m.request_id === requestId) || null;
    }

    get enableDebugFeatures(): boolean {
        return this._enableDebugFeatures;
    }

    get specialContextInputModel(): SpecialContextInputModel {
        return this._specialContextInputModel;
    }

    get draftMessage(): JSONContent | undefined {
        return this._state.draftMessage;
    }

    get lastExchange(): ExchangeWithStatus | null {
        if (this._state.chatHistory.length === 0) {
            return null;
        }
        return this._state.chatHistory[this._state.chatHistory.length - 1];
    }

    get chatHistory(): ExchangeWithStatus[] {
        return this._state.chatHistory;
    }

    get successfulMessages(): ExchangeWithStatus[] {
        const { chatHistory } = this._state;
        return chatHistory.filter((m) => m.status === ExchangeStatus.success && m.response_text);
    }

    get awaitingReply(): boolean {
        if (this.lastExchange === null) {
            return false;
        }
        return this.lastExchange.status === ExchangeStatus.sent;
    }

    handleMessageFromExtension = (e: MessageEvent<WebViewMessage>) => {
        const msg = e.data;
        switch (msg.type) {
            case WebViewMessageType.chatModelReplyStream: {
                this.updateLastExchangeStream({
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    response_text: msg.data.text,
                });
                break;
            }
            case WebViewMessageType.chatModelReplyStreamComplete: {
                this.updateLastExchangeStream({
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    request_id: msg.data.requestId,
                    status: ExchangeStatus.success,
                });
                break;
            }
            case WebViewMessageType.chatModelReplyFailed: {
                this.updateLastExchangeStream({
                    // eslint-disable-next-line @typescript-eslint/naming-convention
                    request_id: msg.data.requestId,
                    status: ExchangeStatus.failed,
                });
                break;
            }
            case WebViewMessageType.sourceFoldersUpdated: {
                this._specialContextInputModel.onSourceFoldersUpdated(msg.data.sourceFolders);
                break;
            }
            case WebViewMessageType.sourceFoldersSyncStatus: {
                this._specialContextInputModel.onSyncStatusUpdated(msg.data);
                break;
            }
            case WebViewMessageType.fileRangesSelected: {
                this._specialContextInputModel.updateselections(msg.data);
                break;
            }
            case WebViewMessageType.currentlyOpenFiles: {
                this._specialContextInputModel.setCurrentlyOpenFiles(msg.data);
                break;
            }
        }
    };

    resolvePath: ResolvePathFn = async (
        path: IQualifiedPathName,
    ): Promise<FileDetails | undefined> => {
        /*const response = await this.asyncMsgSender.send<FindFileRequest, FindFileResponse>(
            {
                type: WebViewMessageType.findFileRequest,
                data: { ...path, exactMatch: true, maxResults: 1 },
            },
            5000,
        );*/
        return response.data[0];
    };

    findFiles: FindFilesFn = async (query: IQualifiedPathName): Promise<FileDetails[]> => {
        /*const response = await this.asyncMsgSender.send<FindFileRequest, FindFileResponse>(
            {
                type: WebViewMessageType.findFileRequest,
                data: query,
            },
            5000,
        );*/
        return response.data;
    };

    saveDraftMessage = (draftMessage: JSONContent | undefined): void => {
        this.updateChatState({ draftMessage });
    };

    clearDraftMessage = (): void => {
        this.updateChatState({ draftMessage: undefined });
    };

    onSendUserMessage = async (newMessage: string): Promise<void> => {
        const exchange = this.createNewExchange(newMessage);
        this.addExchange(exchange);
        const responseExchange = await this.sendUserMessage(exchange);
        this.updateExchangeById(responseExchange, exchange.request_id || "");
    };
    onCancelPrevMessage = async (): Promise<void> => {
        /*await this.asyncMsgSender.send<ChatUserCancel, ChatModelReply>(
            {
                type: WebViewMessageType.chatUserCancel,
            },
            10000,
        );*/
    };

    private createNewExchange = (newMessage: string): ExchangeWithStatus => {
        return {
            /* eslint-disable @typescript-eslint/naming-convention */
            request_id: `temp-fe-${getNonce()}`,
            request_message: newMessage,
            status: ExchangeStatus.sent,
            /* eslint-enable @typescript-eslint/naming-convention */
        };
    };

    private sendUserMessage = async (exchange: ExchangeWithStatus): Promise<ExchangeWithStatus> => {
        const responseExchange = { ...exchange };
        try {
            /*const reply = await this.asyncMsgSender.send<ChatUserMessage, ChatModelReply>(
                {
                    type: WebViewMessageType.chatUserMessage,
                    data: {
                        text: exchange.request_message,
                        userSpecifiedFiles: Object.values(this.specialContextInputModel.files),
                        chatHistory: this.successfulMessages.map((m) => ({
                            request_message: m.request_message,
                            response_text: m.response_text || "",
                            request_id: m.request_id || "",
                        })),
                    },
                },
                this._enablePreferenceCollection ? 1e9 : 60000,
            );

            responseExchange.request_id = reply.data.requestId;
            responseExchange.response_text = reply.data.text;
            responseExchange.status = reply.data.streaming
                ? ExchangeStatus.sent
                : ExchangeStatus.success;*/
        } catch (e) {
            responseExchange.status = ExchangeStatus.failed;
        }
        return responseExchange;
    };

    openFile(details: FileDetails) {
        /* vscode.postMessage({
            type: WebViewMessageType.openFile,
            data: details,
        });*/
    }
}

interface StoredState {
    chatHistory: ExchangeWithStatus[];
    draftMessage?: JSONContent;
}

export { ChatModel };

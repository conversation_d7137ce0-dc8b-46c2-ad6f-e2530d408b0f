import { SyncingStatus, pathNameToAbsPath } from "../../../../../../../../clients/vscode/src/workspace/types";
import type { IQualifiedPathName, ISourceFolderInfo } from "../../../../../../../../clients/vscode/src/workspace/types";
import type { FileDetails } from "../../../../../../../../clients/vscode/src/webview-providers/webview-messages";
import { AsyncMsgSender } from "../../../../../../../../clients/vscode/src/utils/webviews/messaging";

export class SpecialContextInputModel {
    private _files: { [key: string]: FileContextInfo } = {};
    private _selections: { [key: string]: FileSelectionInfo } = {};
    private _sourceFolders: { [key: string]: SourceFolderContextInfo } = {};
    private _syncStatus: SyncingStatus = SyncingStatus.done;
    private _currentlyOpenFiles: FileDetails[] = [];

    private subscribers: Set<(model: SpecialContextInputModel) => void> = new Set();

    constructor(private _asyncMsgSender: AsyncMsgSender) {
        // TODO: Remove when we actually handle files
        this.clearFiles();
    }

    subscribe = (sub: (model: SpecialContextInputModel) => void): (() => void) => {
        this.subscribers.add(sub);
        sub(this);
        return () => {
            this.subscribers.delete(sub);
        };
    };

    get numSourceFolders(): number {
        return Object.values(this.sourceFolders).length;
    }

    get numFiles(): number {
        return Object.values(this.files).length;
    }

    get numSelections(): number {
        return Object.values(this._selections).length;
    }

    get files(): { [key: string]: FileContextInfo } {
        const files: { [key: string]: FileContextInfo } = Object.fromEntries(
            Object.values(this._currentlyOpenFiles).map((f: FileDetails) => [
                f.pathName,
                {
                    rootPath: f.repoRoot,
                    relPath: f.pathName,
                    name: f.pathName.split("/").pop() || "",
                    referenceCount: 1,
                    status: ContextStatus.active,
                },
            ]),
        );
        return mergeFileContextInfoStore(files, this._files);
    }

    get selections(): { [key: string]: FileSelectionInfo } {
        return this._selections;
    }

    get sourceFolders(): { [key: string]: SourceFolderContextInfo } {
        return this._sourceFolders;
    }

    get syncStatus(): SyncingStatus {
        return this._syncStatus;
    }

    setCurrentlyOpenFiles = (files: FileDetails[]) => {
        this._currentlyOpenFiles = files;
        this.notifySubscribers();
    };

    onSourceFoldersUpdated = (sourceFolders: ISourceFolderInfo[]) => {
        const sourceFoldersObj = Object.fromEntries(
            sourceFolders.map((f: ISourceFolderInfo) => [
                f.folderRoot,
                { ...f, status: ContextStatus.active },
            ]),
        );
        this._sourceFolders = sourceFoldersObj;
        this.notifySubscribers();
    };

    onSyncStatusUpdated = (syncStatus: SyncingStatus) => {
        this._syncStatus = syncStatus;
        this.notifySubscribers();
    };

    addFile = (file: IQualifiedPathName) => {
        this.addFileInPlace(file);
        this.notifySubscribers();
    };

    addFiles = (files: IQualifiedPathName[]) => {
        files.forEach(this.addFileInPlace);
        this.notifySubscribers();
    };

    removeFile = (file: IQualifiedPathName) => {
        this.removeFileInPlace(file);
        this.notifySubscribers();
    };

    removeFiles = (files: IQualifiedPathName[]) => {
        files.forEach(this.removeFileInPlace);
        this.notifySubscribers();
    };

    updateFiles = (added: IQualifiedPathName[], removed: IQualifiedPathName[]) => {
        added.forEach(this.addFileInPlace);
        removed.forEach(this.removeFileInPlace);
        this.notifySubscribers();
    };

    toggleFileStatus = (file: IQualifiedPathName) => {
        const absPath = pathNameToAbsPath(file);
        if (this._files[absPath]?.status === ContextStatus.active) {
            this.markFileInactive(file);
        } else {
            this.markFileActive(file);
        }
    };

    markFileActive = (file: IQualifiedPathName) => {
        this.markFileStatus(file, ContextStatus.active);
    };

    markFileInactive = (file: IQualifiedPathName) => {
        this.markFileStatus(file, ContextStatus.inactive);
    };

    private markFileStatus = (file: IQualifiedPathName, status: ContextStatus) => {
        // TODO: Remove when default model is swapped to one that supports special context guiding
        return;
        const absPath = pathNameToAbsPath(file);
        this._files[absPath] = {
            ...this._files[absPath],
            status,
        };
        this.notifySubscribers();
    };

    private addFileInPlace = (file: IQualifiedPathName) => {
        // TODO: Remove when default model is swapped to one that supports special context guiding
        return;
        const absPath = pathNameToAbsPath(file);
        if (this._files[absPath]) {
            const fileInfo = this._files[absPath];
            fileInfo.referenceCount++;
        } else {
            this._files[absPath] = {
                ...file,
                name: file.relPath.split("/").pop() || "",
                referenceCount: 1,
                status: ContextStatus.active,
            };
        }
    };

    private removeFileInPlace = (file: IQualifiedPathName) => {
        // TODO: Remove when default model is swapped to one that supports special context guiding
        return;
        const absPath = pathNameToAbsPath(file);
        if (!this._files[absPath]) {
            return;
        }
        const fileInfo = this._files[absPath];
        fileInfo.referenceCount--;
        if (fileInfo.referenceCount === 0) {
            delete this._files[absPath];
        }
    };

    clearFiles = () => {
        this._files = {};
        this.notifySubscribers();
    };

    updateselections = (ranges: FileDetails[]) => {
        this._selections = Object.fromEntries(
            ranges.map((r) => [r.pathName, { ...r, status: ContextStatus.active }]),
        );
        this.notifySubscribers();
    };

    private notifySubscribers = () => {
        this.subscribers.forEach((sub) => sub(this));
    };
}

export enum ContextStatus {
    active,
    inactive,
}

export interface ContextInfo {
    status: ContextStatus;
}

export interface FileContextInfo extends IQualifiedPathName, ContextInfo {
    name: string;
    referenceCount: number;
}

/**
 * This is a naive merge function with no guarantees on structure or contents of the objects.
 * We perform a navie merge of the two objects, and then add the reference counts.
 */
function mergeFileContextInfos(a: FileContextInfo, b: FileContextInfo): FileContextInfo {
    if (pathNameToAbsPath(a) !== pathNameToAbsPath(b)) {
        throw new Error("Cannot merge two files with different paths");
    }
    return {
        ...a,
        ...b,
        referenceCount: a.referenceCount + b.referenceCount,
    };
}

function mergeFileContextInfoStore(
    a: { [key: string]: FileContextInfo },
    b: { [key: string]: FileContextInfo },
): { [key: string]: FileContextInfo } {
    const allKeys = [...Object.keys(a), ...Object.keys(b)];
    return Object.fromEntries(
        allKeys.map((key: string) => {
            if (!a[key] || !b[key]) {
                return [key, a[key] ?? b[key]];
            }
            return [key, mergeFileContextInfos(a[key], b[key])];
        }),
    );
}

export interface FileSelectionInfo extends ContextInfo, FileDetails {}

export interface SourceFolderContextInfo extends ContextInfo, ISourceFolderInfo {}

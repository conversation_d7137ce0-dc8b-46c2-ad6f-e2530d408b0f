<div class="c-chat-welcome__wrapper">
    <div class="c-chat-welcome">
        <p>Welcome to Augment Chat!</p>
        <ul class="c-chat-welcome__tips-list">
            <li>
                <PERSON><PERSON> is aware of files in this workspace. Ask Augment how to proceed based on other
                files in your project.
            </li>
            <li>
                Augment will pay special attention to code you have selected. Ask Augment to explain
                or modify your selection.
            </li>
            <li>
                Your feedback is critical and appreciated as we improve Chat. Please share any bugs,
                along with the recent chat IDs found on the extension status page!
            </li>
        </ul>
    </div>
</div>

<style>
    .c-chat-welcome__wrapper {
        padding: 0 var(--p-12);

        max-width: 100%;
        overflow-x: hidden;
        overflow-y: scroll;

        /* Center the content */
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .c-chat-welcome {
        margin-top: auto;
        max-width: 500px;

        line-height: 1.5em;
    }

    .c-chat-welcome__tips-list {
        opacity: 50%;
        padding-inline-start: 0;
    }

    .c-chat-welcome__tips-list > li {
        margin: var(--p-4) 0;
    }
</style>

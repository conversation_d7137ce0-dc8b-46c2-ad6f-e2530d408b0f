import tooltip, { type TooltipProps } from "$lib/common/components/tooltips/tooltip";
import type { SvelteComponent } from "svelte";
import { type SpecialContextInputModel } from "../../models/context-model";

export interface ContextContentProps {
    contextModel: SpecialContextInputModel;
}

// Type narrowing for the context pop-up
export interface ContextPopupProps<
    T extends typeof SvelteComponent<P>,
    P extends ContextContentProps,
    Q extends P,
> extends TooltipProps<T, P, Q> {
    show: boolean;
}

export default function contextPopup<
    T extends typeof SvelteComponent<P>,
    P extends ContextContentProps,
    Q extends P,
>(node: HTMLElement, props: ContextPopupProps<T, P, Q>) {
    const injectProps = (props: ContextPopupProps<T, P, Q>) => {
        return {
            ...props,
            tippy: {
                ...props.tippy,
                trigger: "manual",
                showOnCreate: props.show,
            },
        };
    };
    const baseTooltip = tooltip<T, P, Q>(node, props);

    return {
        update: (props: ContextPopupProps<T, P, Q>) => {
            props = injectProps(props);
            baseTooltip.update(props);

            if (props.show) {
                baseTooltip.show();
            } else {
                baseTooltip.hide();
            }
        },
        destroy: baseTooltip.destroy,
    };
}

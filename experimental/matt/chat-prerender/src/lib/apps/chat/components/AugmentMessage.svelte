<script lang="ts">
    import type { Instance } from "tippy.js";
    import type { OpenLocalFileFn, ResolvePathFn } from "../models/types";

    import ChatMessage from "./ChatMessage.svelte";
    import Markdown from "./Markdown.svelte";
    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";
    import tooltip from "$lib/common/components/tooltips/tooltip";
    import TextContents from "$lib/common/components/tooltips/TextContents.svelte";
    import augmentAvatar from "../assets/chat-avatar-augment.svg";

    export let markdown: string;
    export let resolvePath: ResolvePathFn = async () => undefined;
    export let openLocalFile: OpenLocalFileFn = () => {};
    export let requestId: string | undefined = undefined;

    function copyRequestId() {
        if (!requestId) {
            return;
        }
        navigator.clipboard.writeText(requestId);
        // If the base tooltip exists, show it for a second and then hide it.
        if (baseTooltip) {
            clearTimeout(hideTooltipTimeoutId);
            baseTooltip.show();
            hideTooltipTimeoutId = setTimeout(() => {
                baseTooltip.hide();
            }, 1500 /* 1.5 second */);
        }
    }

    // Some variables to track tooltip state and allow the copy function to
    // trigger the tooltip. The copyTooltip function is an action that wraps the
    // base tooltip and binds it to a variable we can call.
    let baseTooltip: Instance;
    let hideTooltipTimeoutId: NodeJS.Timeout | undefined;
    const copyTooltip = (node: HTMLElement) => {
        baseTooltip = tooltip(node, {
            tippy: { placement: "top" },
            component: TextContents,
            componentProps: { contents: `Copied request ID!` },
        });
        return baseTooltip;
    };
</script>

<ChatMessage isAugment avatar={augmentAvatar}>
    <svelte:fragment slot="nameIcons">
        {#if requestId}
            <vscode-button
                class="c-aug-msg-metadata__ri-btn"
                appearance="icon"
                aria-label="Request ID"
                on:click={copyRequestId}
                on:keyup={() => {}}
                title={`Copy request ID`}
                role="button"
                tabindex="0"
            >
                <MaterialIcon iconName="link" />
                <div use:copyTooltip />
            </vscode-button>
        {/if}
    </svelte:fragment>
    <Markdown {resolvePath} {openLocalFile} {markdown} />
</ChatMessage>

<style>
    vscode-button.c-aug-msg-metadata__ri-btn {
        margin-left: var(--p-1);
        cursor: pointer;
    }
    vscode-button.c-aug-msg-metadata__ri-btn :global(.material-symbols-outlined) {
        opacity: 0.5;
    }
</style>

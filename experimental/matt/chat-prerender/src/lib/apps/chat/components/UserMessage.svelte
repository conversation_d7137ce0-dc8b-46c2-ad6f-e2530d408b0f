<script lang="ts">
    import ChatMessage from "./ChatMessage.svelte";
    import Markdown from "./Markdown.svelte";
    import { ExchangeStatus } from "../types/chat-message";
    import userAvatar from "../assets/chat-avatar-user.svg";

    export let msg: string;
    export let status: ExchangeStatus;
    export let resendMessage: () => void = () => {};
</script>

<ChatMessage {status} avatar={userAvatar} {resendMessage}>
    <div class="c-user-message__content-raw">
        {msg}
    </div>
</ChatMessage>

<style>
    .c-user-message__content-raw {
        white-space: pre-wrap;
        word-break: break-word;
        overflow-wrap: break-word;
    }
</style>

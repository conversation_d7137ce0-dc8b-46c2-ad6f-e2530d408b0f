<script lang="ts">
    import { type FileDetails } from "../../../../../../../../../clients/vscode/src/webview-providers/webview-messages";
    import Filespan from "$lib/common/components/Filespan.svelte";
    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";
    import Chip from "./Chip.svelte";

    // We rely on upstream providers to give us valid, non-empty selections here.
    // This component will not do validation of the selections.
    export let fileSelection: FileDetails;

    $: filepath = fileSelection.pathName || "";
    let startLine: number | undefined, endLine: number | undefined;
    $: {
        if (fileSelection.fullRange) {
            startLine = fileSelection.fullRange.startLineNumber;
            endLine = fileSelection.fullRange.endLineNumber;
        } else if (fileSelection.range) {
            startLine = fileSelection.range.start;
            endLine = fileSelection.range.stop;
        }
    }
</script>

<vscode-option class="c-file-selection">
    <Filespan {filepath}>
        <MaterialIcon slot="leftIcon" iconName="text_select_start" />
        <svelte:fragment slot="rightIcon">
            {#if startLine !== undefined && endLine !== undefined}
                {#if startLine === endLine}
                    <Chip highlighted>L{startLine + 1}</Chip>
                {:else}
                    <Chip highlighted>L{startLine + 1}-{endLine + 1}</Chip>
                {/if}
            {/if}
        </svelte:fragment>
    </Filespan>
</vscode-option>

<style>
    .c-file-selection {
        /* Necessary for flex growth to work properly */
        display: block;
        padding-top: var(--p-0-5);
        padding-bottom: var(--p-0-5);
    }

    .c-file-selection:last-child {
        padding-bottom: var(--p-1);
    }

    .c-file-selection:first-child {
        padding-top: var(--p-1);
    }
</style>

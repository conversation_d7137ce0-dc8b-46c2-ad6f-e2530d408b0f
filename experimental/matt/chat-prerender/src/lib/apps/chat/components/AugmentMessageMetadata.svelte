<script lang="ts" context="module">
    import type { IRange } from "monaco-editor";

    export type LocalFile = {
        repoRoot: string;
        path: string;
        range?: IRange;
    };
</script>

<script lang="ts">
    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";
    import LocalFileLink from "$lib/common/components/LocalFileLink.svelte";
    import { type FileDetails } from "$lib/src/webview-providers/webview-messages";

    export let collapsed: boolean = true;
    export let sources: LocalFile[] = [];
    export let onLocalFileClick: (details: FileDetails) => void;
</script>

<div class="c-aug-msg-metadata">
    <div class="c-aug-msg-metadata__bar">
        {#if sources.length > 0}
            <button class="c-aug-msg-metadata__sources" on:click={() => (collapsed = !collapsed)}>
                Sources <MaterialIcon
                    iconName={collapsed ? "keyboard_arrow_right" : "keyboard_arrow_down"}
                />
            </button>
        {/if}

        <div class="c-aug-msg-metadata__actions">
            <vscode-button appearance="icon"><MaterialIcon iconName="thumb_up" /></vscode-button>
            <vscode-button appearance="icon"><MaterialIcon iconName="thumb_down" /></vscode-button>
            <vscode-button appearance="icon"><MaterialIcon iconName="cached" /></vscode-button>
        </div>
    </div>
    <div class="c-aug-msg-metadata__sources-container">
        {#if !collapsed}
            <ul>
                {#each sources as source}
                    <li>
                        <LocalFileLink
                            repoRoot={source.repoRoot}
                            path={source.path}
                            range={source.range}
                            onClick={onLocalFileClick}
                        />
                    </li>
                {/each}
            </ul>
        {/if}
    </div>
</div>

<style>
    .c-aug-msg-metadata :global(.material-symbols-outlined) {
        font-size: inherit;
        line-height: inherit;
        vertical-align: bottom;
    }

    .c-aug-msg-metadata__bar {
        display: grid;
        grid-template-columns: auto 1fr auto;
        grid-template-rows: 1fr;
        align-items: center;
        opacity: 0.5;
    }

    .c-aug-msg-metadata__sources {
        grid-area: 1 / 1 / 2 / 2;

        display: flex;
        flex-direction: row;
        gap: var(--p-1);
        background: none;
        padding: 0;
        margin: 0;
        border: none;
        font-size: inherit;
        color: inherit;
        cursor: pointer;
    }

    .c-aug-msg-metadata__actions {
        grid-area: 1 / 3 / 2 / 4;

        display: flex;
        flex-direction: row;
        gap: var(--p-2);
        background: none;
        padding: 0;
        margin: 0;
        border: none;
        font-size: inherit;
    }

    .c-aug-msg-metadata__actions vscode-button {
        color: inherit;
    }

    .c-aug-msg-metadata__sources-container ul {
        display: flex;
        flex-direction: column;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: var(--p-1);
    }
</style>

<script lang="ts">
    import { type ChatModel } from "../models/chat-model";
    import WelcomePage from "./WelcomePage.svelte";

    export let chatModel: ChatModel;
</script>

{#await import("./MessageList.svelte")}
    {#if $chatModel.chatHistory.length === 0}
        <WelcomePage />
    {/if}
{:then c}
    {#if $chatModel.chatHistory.length === 0}
        <WelcomePage />
    {:else}
        <svelte:component this={c.default} {chatModel} {...$$restProps} />
    {/if}
{/await}

<script lang="ts">
    import { onKey } from "$lib/common/utils/keypress";
    import { SpecialContextInputModel } from "../../models/context-model";

    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";
    import Chip from "./Chip.svelte";
    import DropdownContents from "./ContextContents.svelte";
    import popup from "./contextPopup";

    export let contextModel: SpecialContextInputModel;

    $: disabled = $contextModel.numFiles === 0 && $contextModel.numSelections === 0;
    let active: boolean = false;
    $: {
        if ($contextModel.numFiles === 0) {
            active = false;
        }
    }
</script>

<vscode-button
    class="c-context-dropdown"
    appearance="secondary"
    {disabled}
    on:click={() => (active = !active)}
    on:keydown={onKey("Enter", () => (active = !active))}
    tabindex="0"
    role="button"
    use:popup={{
        tippy: {
            popperOptions: {
                strategy: "fixed",
                modifiers: [
                    {
                        name: "offset",
                        options: {
                            offset: [0, 4],
                        },
                    },
                ],
            },
            onClickOutside: () => (active = false),
        },
        show: active,
        component: DropdownContents,
        componentProps: {
            contextModel,
        },
    }}
>
    <MaterialIcon iconName="alternate_email" />
    <Chip>{$contextModel.numFiles}</Chip>
    {#if $contextModel.numSelections > 0}
        <Chip highlighted>SELECT</Chip>
    {/if}
    {#if !disabled}
        <MaterialIcon iconName={active ? "keyboard_arrow_up" : "keyboard_arrow_right"} />
    {/if}
</vscode-button>

<style>
    .c-context-dropdown {
        width: fit-content;
    }
</style>

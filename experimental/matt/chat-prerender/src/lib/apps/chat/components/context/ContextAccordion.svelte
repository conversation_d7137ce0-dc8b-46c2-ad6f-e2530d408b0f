<script lang="ts">
    import { onKey } from "$lib/common/utils/keypress";
    import { type SpecialContextInputModel } from "../../models/context-model";

    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";
    import Chip from "./Chip.svelte";

    export let contextModel: SpecialContextInputModel;

    let active: boolean = true;
    const onClickHeader = () => {
        active = !active;
    };
    $: numContextElements =
        $contextModel.numFiles + $contextModel.numSelections + $contextModel.numSourceFolders;
</script>

<div
    class="c-context-accordion__header"
    on:click={onClickHeader}
    on:keydown={onKey("Enter", onClickHeader)}
    tabindex="0"
    role="button"
>
    <MaterialIcon iconName={active ? "keyboard_arrow_down" : "keyboard_arrow_right"} />
    CONTEXT
    <Chip>{numContextElements}</Chip>
</div>
<div class="c-context-accordion__content" class:active={active && numContextElements > 0}>
    <slot />
</div>

<style>
    .c-context-accordion__header,
    .c-context-accordion__content {
        background-color: var(--vscode-dropdown-background);
    }

    .c-context-accordion__header {
        display: flex; /* Needed to align children vertically */
        align-items: center;
        padding: var(--p-1);
        cursor: pointer;
    }

    .c-context-accordion__content {
        display: none;
        min-height: 20px;
        flex: 0 1 auto; /* Allow the content to grow to fill available space, should not shrink if possible */
    }

    .c-context-accordion__content.active {
        display: block;
        scrollbar-color: var(--vscode-scrollbarSlider-background) var(--vscode-dropdown-background);
        padding-bottom: var(--p-1);

        /* Don't reserve space for scrollbar unless needed */
        overflow-y: auto;
    }
</style>

<script lang="ts">
    import { type ISourceFolderInfo } from "../../../../../../../../../clients/vscode/src/workspace/types";

    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";
    import Filespan from "$lib/common/components/Filespan.svelte";

    export let sourceFolder: ISourceFolderInfo;
</script>

<vscode-option class="c-source-folder-item">
    <Filespan filepath={sourceFolder.folderRoot} className="c-source-folder-item">
        <MaterialIcon slot="leftIcon" iconName="folder_managed" />
    </Filespan>
</vscode-option>

<style>
    .c-source-folder-item :global(.material-symbols-outlined) {
        margin: 0;
    }

    .c-source-folder-item {
        display: block;
        margin-top: 0;
        margin-bottom: 0;
    }
</style>

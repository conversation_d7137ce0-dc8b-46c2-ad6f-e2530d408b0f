<script lang="ts">
    import { type ChatModel } from "../models/chat-model";
    import { onKey } from "$lib/common/utils/keypress";
    // import ContextDropdown from "./context/ContextDropdown.svelte";
    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";

    export let chatModel: ChatModel;
    export let canClearHistory: boolean;
    export let canSendMsg: boolean;
    export let canCancelMsg: boolean;
    export let awaitingReply: boolean;
    export let clearHistory: () => void;
    export let sendMsg: () => void;
    export let cancelMsg: () => void;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    $: specialContextInputModel = $chatModel.specialContextInputModel;
</script>

<div class="l-input-area__actions">
    <div class="c-left-align-actions">
        <!-- <ContextDropdown contextModel={specialContextInputModel} /> -->
    </div>
    <div class="c-right-align-actions">
        <vscode-button
            role="button"
            tabindex="0"
            appearance="icon"
            title="Clear history"
            disabled={!canClearHistory}
            on:keyup={() => {}}
            on:click={clearHistory}
        >
            <MaterialIcon iconName="delete" />
        </vscode-button>
        <div class="c-send-or-progress" class:c-send-or-progress--sending={awaitingReply}>
            {#if $chatModel.awaitingReply}
                <vscode-button
                    id="cancel"
                    role="button"
                    tabindex="0"
                    appearance="secondary"
                    title="Cancel"
                    disabled={!canCancelMsg}
                    on:keyup={onKey("Enter", cancelMsg)}
                    on:click={cancelMsg}
                >
                    <MaterialIcon iconName="cancel" />
                </vscode-button>
            {:else}
                <vscode-button
                    id="send"
                    role="button"
                    tabindex="0"
                    appearance="primary"
                    title="Send message"
                    disabled={!canSendMsg}
                    on:keyup={onKey("Enter", sendMsg)}
                    on:click={sendMsg}
                >
                    <MaterialIcon iconName="send" />
                </vscode-button>
            {/if}
        </div>
    </div>
</div>

<style>
    /* The action bar should shrink to fit the content, and not take up more
    space than necessary. The input area should take up as much available space
    as possible */
    .l-input-area__actions {
        flex-shrink: 0;
        width: 100%;

        display: flex;
        padding-bottom: var(--p-4);
        padding-right: var(--p-2);
        padding-left: var(--p-2);
        height: fit-content;
    }
    .l-input-area__actions vscode-button :global(.material-symbols-outlined) {
        font-size: 16px;
    }

    .l-input-area__actions .c-left-align-actions,
    .c-right-align-actions {
        display: flex;
        flex-direction: row;
        height: fit-content;
    }

    .l-input-area__actions .c-right-align-actions {
        margin-left: auto; /* Position right-aligned actions to the right side of action area */
    }

    .l-input-area__actions .c-right-align-actions > *:not(:first-child) {
        margin-left: var(--p-2);
    }

    .c-send-or-progress {
        position: relative;
    }
</style>

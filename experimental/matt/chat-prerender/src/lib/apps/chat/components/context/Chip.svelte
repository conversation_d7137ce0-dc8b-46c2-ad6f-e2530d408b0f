<script lang="ts">
    export let highlighted: boolean = false;
</script>

<div class="chip" class:highlighted>
    <slot />
</div>

<style>
    .chip {
        display: inline-block;
        padding: 0 var(--p-2);
        margin: 0 var(--p-1);
        border-radius: var(--p-1);
        background-color: var(--vscode-badge-background);
        text-align: center;
    }

    .chip.highlighted {
        background-color: var(--vscode-editorMarkerNavigationInfo-background);
    }
</style>

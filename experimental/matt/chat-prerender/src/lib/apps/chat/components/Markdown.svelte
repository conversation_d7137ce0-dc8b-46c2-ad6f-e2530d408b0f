<script lang="ts">
    import { setContext } from "svelte";
    import Markdown from "@magidoc/plugin-svelte-marked";

    import type { ResolvePathFn, OpenLocalFileFn } from "../models/types";

    import CodeBlock from "$lib/common/components/Codeblock.svelte";
    import Codespan from "$lib/common/components/Codespan.svelte";
    import MarkdownParagraph from "$lib/common/components/MarkdownParagraph.svelte";

    export let markdown: string;
    export let resolvePath: ResolvePathFn = async () => undefined;
    export let openLocalFile: OpenLocalFileFn = () => {};
    setContext("resolvePath", resolvePath);
    setContext("openLocalFile", openLocalFile);
</script>

<div class="c-markdown">
    <Markdown
        source={markdown}
        renderers={{
            codespan: Codespan,
            code: CodeBlock,
            paragraph: MarkdownParagraph,
        }}
    />
</div>

<style>
    .c-markdown {
        width: 100%;
    }

    :global(.c-markdown > p) {
        word-break: break-word;
    }

    /* Smaller default left-padding for lists */
    :global(.c-markdown > ul, ol) {
        padding-inline-start: var(--p-6);
    }

    :global(.c-markdown > table) {
        display: block;
        overflow-x: auto;
    }

    :global(.c-markdown > *:first-child) {
        margin-top: 0;
    }

    :global(.c-markdown > *:last-child) {
        margin-bottom: 0;
    }
</style>

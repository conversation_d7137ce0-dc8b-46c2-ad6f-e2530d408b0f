import scroll from "scroll";

export interface TrackScrollBehaviorProps {
    onScrollIntoBottom?: () => void;
    onScrollAwayFromBottom?: () => void;
}

export interface FollowMessageProps {
    msgString: string;
    scrollOnFirstRender?: boolean;
    shouldFollow?: boolean;
}

export interface ScrollToTurnOptions {
    smooth?: boolean;
    bottom?: boolean;
    disableScrollUp?: boolean;
    disableScrollDown?: boolean;
}

// Detects whether the current scroll state is near the bottom of the element
const BOTTOM_BUFFER_PX = 40;
const isScrollNearBottom = (element: HTMLElement, buffer: number = BOTTOM_BUFFER_PX): boolean => {
    return distanceFromBottom(element) <= buffer;
};
const distanceFromBottom = (element: HTMLElement): number => {
    const { scrollTop, offsetHeight, scrollHeight } = element;
    const scrollBottom = scrollTop + offsetHeight;
    return scrollHeight - scrollBottom;
};

/**
 * This function tracks some key scroll-related metadata for the element it is attached to.
 * - scrollTop
 * - Whether the element *can* scroll
 * - Whether the scroll is near the bottom of the element
 * - Whether the scroll is scrolling down or up
 *
 * It then uses this information to call the appropriate callbacks when the scroll
 * state changes. The callbacks are called on scroll state transitions, not on
 * scroll events.
 * - When the user first scrolls into the bottom of the element, the
 *   onScrollIntoBottom callback is called. The callback is only called again
 *   once the user scrolls away from the bottom and back into the bottom.
 * - When the user first scrolls up, the onScrollUp callback is called. The
 *   callback is only called again once the user scrolls down and back up.
 *
 * @param element: The element to track scrolling behavior for
 * @param props: The callbacks to call when the scroll state changes
 * @returns
 */
export function trackScrollBehavior(element: HTMLElement, props: TrackScrollBehaviorProps = {}) {
    let currProps = props;

    let prevState = {
        scrollTop: 0,
        scrollBottom: 0,
        scrollHeight: 0,
        scrolledIntoBottom: true,
        scrolledAwayFromBottom: true,
    };

    const onScrollHandler = () => {
        const { scrollTop, scrollHeight, offsetHeight } = element;
        const scrollBottom = distanceFromBottom(element);

        // Derive whether we are scrolling up or scrolling down
        const scrollingDown = scrollTop > prevState.scrollTop + 1;

        // Our parent element shrank more than the distance to the last scrollBottom
        const heightDiff = scrollHeight - prevState.scrollHeight;
        const forcedUp = heightDiff < 0 && prevState.scrollBottom < -heightDiff;
        const scrollingUp =
            !forcedUp &&
            scrollTop < prevState.scrollTop - 1 &&
            scrollBottom > prevState.scrollBottom + 1;

        // Derive whether the element can scroll and is near the bottom
        const canScroll = scrollHeight > offsetHeight;
        const nearBottom = isScrollNearBottom(element);

        // Derive the scroll behavior
        const scrolledIntoBottom = nearBottom && canScroll && scrollingDown;
        const scrolledAwayFromBottom = scrollingUp || !canScroll;
        if (scrolledIntoBottom && !prevState.scrolledIntoBottom) {
            currProps.onScrollIntoBottom?.();
        } else if (scrolledAwayFromBottom && !prevState.scrolledAwayFromBottom) {
            currProps.onScrollAwayFromBottom?.();
        }

        prevState = {
            scrollTop,
            scrollBottom,
            scrolledIntoBottom,
            scrolledAwayFromBottom,
            scrollHeight,
        };
    };

    // Set up the event listener
    element.addEventListener("scroll", onScrollHandler);
    return {
        update(props: TrackScrollBehaviorProps) {
            // Update the props reference to the new props so our callback
            // can access the new functions passed in
            currProps = props;
        },
        destroy() {
            // Remove the event listener
            element.removeEventListener("scroll", onScrollHandler);
        },
    };
}

const OFFSET_BUFFER_PX = 40;
const SCROLL_DURATION = 250;
function computeScrollToTurnY(turnElement: HTMLElement, options: ScrollToTurnOptions = {}): number {
    const { bottom } = options;
    if (bottom) {
        return turnElement.offsetTop + turnElement.offsetHeight - OFFSET_BUFFER_PX;
    } else {
        return turnElement.offsetTop - OFFSET_BUFFER_PX;
    }
}

export function canScrollToTurn(
    turnParent: HTMLElement,
    turnElement: HTMLElement,
    options: ScrollToTurnOptions = {},
): boolean {
    const { disableScrollUp, disableScrollDown } = options;
    const scrollToY = computeScrollToTurnY(turnElement, options);

    if (disableScrollUp && scrollToY < turnParent.scrollTop) {
        return false;
    } else if (disableScrollDown && scrollToY > turnParent.scrollTop) {
        return false;
    }

    return true;
}

/**
 * Scrolls to the last turn in the turnParent element.
 * If smooth is true, the scroll will be animated and return a cancel function.
 *
 * @param turnParent: The element containing the turn elements and is scrollable
 * @param turnElement: The turn element to scroll to
 * @param options: Configuration options for the scroll
 * @returns: A cancel function if smooth is true
 */
export function scrollToTurn(
    turnParent: HTMLElement,
    turnElement: HTMLElement,
    options: ScrollToTurnOptions = {},
): (() => void) | undefined {
    if (!canScrollToTurn(turnParent, turnElement, options)) {
        return;
    }

    const scrollToY = computeScrollToTurnY(turnElement, options);

    // If smooth is true, return the cancel function
    if (options.smooth) {
        return scroll.top(turnParent, scrollToY, { duration: SCROLL_DURATION });
    } else {
        turnParent.scroll({ top: scrollToY, behavior: "instant" });
    }
}

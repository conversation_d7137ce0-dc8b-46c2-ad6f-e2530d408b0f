<script lang="ts">
    // Track the variables associated with the draggable
    export let highlighted: boolean;
    export let inputArea: HTMLElement;

    // If `dragInfo` is defined, we are currently dragging the divider
    interface DragInfo {
        startY: number;
        initialHeight: number;
    }
    let dragInfo: DragInfo | undefined;

    const startDrag = (event: MouseEvent) => {
        dragInfo = { startY: event.clientY, initialHeight: inputArea.offsetHeight };
    };

    const duringDrag = (event: MouseEvent) => {
        if (!dragInfo) {
            return;
        }
        const deltaY = event.clientY - dragInfo.startY;
        inputArea.style.height = `${dragInfo.initialHeight - deltaY}px`;
    };

    const endDrag = () => {
        dragInfo = undefined;
    };
</script>

<!-- During lifecycle of the component, we want to listen for mouse events on the document -->
<svelte:document on:mousemove={duringDrag} on:mouseup={endDrag} />

<!-- Ignore the below because the default behavior is to expand the input area.
This is purely for a bit of UX polish for the user-->
<!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
<div class="c-chat-input-dialog-hr-handle" on:mousedown={startDrag} role="separator">
    <div class="c-chat-input-dialog-hr" class:l-chat-divider={highlighted} />
</div>

<style>
    .c-chat-input-dialog-hr-handle {
        cursor: ns-resize;
        position: relative;
    }

    /* Add a pseudo-element to the handle to make it easier to grab */
    .c-chat-input-dialog-hr-handle::before {
        content: ""; /* Required to render the pseudo-element */

        position: absolute; /* Position the pseudo-element relative to the handle */
        height: var(--p-2); /* Interactive height is above the handle */
        bottom: 0; /* Position the pseudo-element right on top of the handle */
        width: 100%; /* Make the pseudo-element the same width as the handle */

        margin: 0px;
        cursor: ns-resize;
    }

    .c-chat-input-dialog-hr {
        height: 1px;
        width: 100%;
        background-color: var(--vscode-editorWidget-border);
    }

    .c-chat-input-dialog-hr-handle:hover .c-chat-input-dialog-hr {
        background-color: var(--vscode-sash-hoverBorder);
    }

    .l-chat-divider {
        background-color: var(--vscode-focusBorder);
    }
</style>

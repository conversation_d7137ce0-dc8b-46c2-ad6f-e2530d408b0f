<script lang="ts">
    import { ExchangeStatus } from "../types/chat-message";

    export let avatar: string;
    export let status: ExchangeStatus | undefined = undefined;
    export let isAugment: boolean = false;
    export let resendMessage: () => void = () => {};
</script>

<div class="c-chat-message" class:c-chat-message--augment={isAugment}>
    <div class="c-chat-message__avatar">
        <img src={avatar} alt="User avatar" />
    </div>
    <div class="c-chat-message__body">
        <div class="c-chat-message__content">
            {#if isAugment}
                <div class="c-chat-message__name c-augment-name">
                    Augment
                    <slot name="nameIcons" />
                </div>
            {:else}
                <div class="c-chat-message__name c-user-name">You</div>
            {/if}
            <slot />
        </div>
        {#if status === ExchangeStatus.failed}
            <div class="c-chat-message__failure-note">
                Oops, sorry we were unable to send your message, please
                <button on:click|preventDefault={resendMessage}>try again</button>
            </div>
        {/if}
    </div>
</div>

<style>
    .c-chat-message {
        display: grid;
        grid-template-columns: auto 1fr;
        gap: var(--p-2);

        max-width: 100%;
    }

    .c-chat-message--augment {
        margin-bottom: var(--p-2);
    }

    .c-chat-message__failure-note {
        font-style: italic;
    }

    .c-chat-message__failure-note > button {
        all: unset;
        color: var(--vscode-textLink-foreground);
        text-decoration: underline;
        cursor: pointer;
    }

    .c-chat-message__avatar {
        position: relative;
        padding-top: var(--p-0-5);
    }

    .c-chat-message__avatar > img {
        position: sticky;
        top: 0;
        width: 1.4lh;
        aspect-ratio: 1;
    }

    .c-chat-message__body {
        display: flex;
        flex-direction: column;
        gap: var(--p-2);

        width: 100%;
        overflow: hidden;
    }

    .c-chat-message__name {
        margin-bottom: var(--p-1);
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .c-chat-message__name.c-augment-name {
        color: var(--augment-primary-color);
    }

    .c-chat-message__name.c-user-name {
        color: var(--user-primary-color);
    }

    .c-chat-message__content {
        max-width: 100%;
        overflow: hidden;
    }
</style>

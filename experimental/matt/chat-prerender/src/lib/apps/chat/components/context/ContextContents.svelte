<script lang="ts">
    import { type ContextContentProps } from "./contextPopup";
    import { on<PERSON>ey } from "$lib/common/utils/keypress";
    import MaterialIcon from "$lib/common/components/MaterialIcon.svelte";
    import Filespan from "$lib/common/components/Filespan.svelte";
    import SelectionItem from "./SelectionItem.svelte";
    import { ContextStatus } from "../../models/context-model";
    import SourceFolderItem from "./SourceFolderItem.svelte";
    import ChaseLoadingBar from "$lib/common/components/ChaseLoadingBar.svelte";

    export let contextModel: ContextContentProps["contextModel"];

    $: syncStatus = $contextModel.syncStatus;
    $: sourceFolders = Object.values($contextModel.sourceFolders).sort((a, b) => {
        return a.folderRoot.localeCompare(b.folderRoot);
    });
    $: selections = Object.values($contextModel.selections).sort((a, b) => {
        return a.pathName.localeCompare(b.pathName);
    });
    $: files = Object.values($contextModel.files).sort((a, b) =>
        a.relPath.localeCompare(b.relPath),
    );
</script>

<div class="c-context-contents">
    {#if sourceFolders?.length > 0}
        {#each sourceFolders as sf}
            <SourceFolderItem sourceFolder={sf} />
        {/each}
        <ChaseLoadingBar loading={syncStatus !== "123"}>
            Syncing your workspace...
        </ChaseLoadingBar>
    {/if}
    {#if files?.length}
        {#each files as f}
            <vscode-option
                value={f.relPath}
                on:click={() => contextModel.toggleFileStatus(f)}
                on:keydown={onKey("Enter", () => contextModel.toggleFileStatus(f))}
                role="button"
                tabindex="0"
            >
                <Filespan
                    filepath={f.relPath}
                    className={f.status === ContextStatus.active ? "" : "c-context-file-inactive"}
                >
                    <!-- Enable users to hide files from the message context -->
                    <MaterialIcon
                        slot="rightIcon"
                        iconName={f.status === ContextStatus.active
                            ? "visibility"
                            : "visibility_off"}
                    />
                </Filespan>
            </vscode-option>
        {/each}
    {/if}
    {#if selections?.length > 0}
        {#each selections as s}
            <SelectionItem fileSelection={s} />
        {/each}
    {/if}
</div>

<style>
    .c-context-contents {
        display: flex;
        flex-direction: column;
        gap: var(--p-1); /* Add a default spacing between all our items */
    }

    .c-context-contents :global(.c-context-file-inactive) {
        opacity: 50%;
    }

    .c-context-contents > :global(*) {
        padding: 0 var(--p-2);
    }

    .c-context-contents vscode-option {
        cursor: pointer;
        display: block;
    }
</style>

<script lang="ts">
    import throttle from "lodash/throttle";
    import { onMount } from "svelte";

    import UserMessage from "./UserMessage.svelte";
    import AugmentMessage from "./AugmentMessage.svelte";
    import { type ChatModel } from "../models/chat-model";
    import {
        type ScrollToTurnOptions,
        type FollowMessageProps,
        scrollToTurn,
        trackScrollBehavior,
        canScrollToTurn,
    } from "./actions/trackScrollBehavior";

    export let chatModel: ChatModel;
    let msgListElement: HTMLElement;
    $: chatHistory = $chatModel.chatHistory;

    // Scroll behavior handlers. We use these to determine whether the user
    // controls scroll or Augment controls scroll, and whether we should
    // follow the bottom of the message list
    let userControlsScroll: boolean = false;
    let shouldFollowBottom: boolean = false;
    let cancelCurrAutoScroll: (() => void) | undefined;
    function onScrollIntoBottom(): void {
        userControlsScroll = false;
        shouldFollowBottom = true;
    }
    function onScrollAwayFromBottom(): void {
        cancelCurrAutoScroll?.();
        userControlsScroll = true;
        shouldFollowBottom = false;
    }

    // A throttled function that will scroll to the turn element if the user doesn't control scroll
    const tryScrollToTurn = throttle(
        (
            turnParent: HTMLElement | undefined,
            e: HTMLElement | undefined,
            options: ScrollToTurnOptions,
        ) => {
            if (!turnParent || !e || userControlsScroll) {
                return;
            }
            if (canScrollToTurn(turnParent, e, options)) {
                cancelCurrAutoScroll?.();
                cancelCurrAutoScroll = scrollToTurn(turnParent, e, options);
            }
        },
        200,
        { leading: true, trailing: true },
    );

    // When messages are created, we try to scroll to it.
    // When messages are updated, we also try to scroll to it.
    // This should only be active on the last turn
    function followMessage(
        e: HTMLElement,
        { scrollOnFirstRender, shouldFollow }: FollowMessageProps,
    ) {
        if (shouldFollow) {
            // Remove control from user if scrollOnFirstRender is true
            userControlsScroll = scrollOnFirstRender ? false : userControlsScroll;
            tryScrollToTurn(msgListElement, e, { smooth: true, bottom: false });
        }

        return {
            // When text updates, scroll *down* to the bottom
            update({ shouldFollow }: FollowMessageProps) {
                if (shouldFollow) {
                    tryScrollToTurn(msgListElement, e, {
                        smooth: true,
                        bottom: true,
                        disableScrollUp: true,
                    });
                }
            },
        };
    }

    // When we first load the message list, we try to scroll to the last turn.
    onMount(() => {
        // Get the last child of the turn, whether it's a request or response
        const lastTurnMessage = msgListElement?.lastElementChild?.lastElementChild;
        if (lastTurnMessage) {
            tryScrollToTurn(msgListElement, lastTurnMessage as HTMLElement, {
                smooth: false,
                bottom: false,
            });
        }
    });
</script>

<div class="c-msg-list-container">
    <div
        class="c-msg-list"
        bind:this={msgListElement}
        use:trackScrollBehavior={{
            onScrollIntoBottom,
            onScrollAwayFromBottom,
        }}
    >
        {#each chatHistory as turn, idx}
            <div class="c-msg-list__turn">
                <div
                    class="c-msg-list__turn-request"
                    use:followMessage={{
                        msgString: turn.request_message,
                        scrollOnFirstRender: true,
                        shouldFollow: idx === chatHistory.length - 1,
                    }}
                >
                    <UserMessage
                        status={turn.status}
                        msg={turn.request_message}
                        resendMessage={() => {
                            chatModel.resendUnsuccessfulTurn(turn);
                        }}
                    />
                </div>
                {#if turn.response_text}
                    <div
                        class="c-msg-list__turn-response"
                        use:followMessage={{
                            msgString: turn.response_text,
                            shouldFollow: idx === chatHistory.length - 1,
                        }}
                    >
                        <AugmentMessage
                            requestId={turn.request_id}
                            resolvePath={chatModel.resolvePath}
                            markdown={turn.response_text}
                            openLocalFile={chatModel.openFile}
                        />
                    </div>
                {/if}
            </div>
        {/each}
    </div>
</div>

<style>
    /* Outermost component for the message list */
    .c-msg-list-container {
        height: 100%;
        width: 100%;
        overflow: hidden;
        display: flex;
    }

    /* Aligns the messages to the bottom instead of top */
    .c-msg-list {
        /* Allows for `offsetTop` to be relative to this */
        position: relative;

        display: flex;
        flex-direction: column;
        /* Spaces out the actual messages */
        gap: var(--p-2);
        margin-top: auto;
        overflow-y: scroll;
        max-height: 100%;
        width: 100%;
        /* Adds padding around the message list container */
        padding: var(--p-4) var(--p-2);
    }

    .c-msg-list__turn-request,
    .c-msg-list__turn-response {
        margin-bottom: var(--p-2);
    }
</style>

export const enum ExchangeStatus {
    sent = "sent",
    failed = "failed",
    success = "success",
}

export interface ExchangeWithStatus {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    request_message: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    response_text?: string;
    // eslint-disable-next-line @typescript-eslint/naming-convention
    request_id?: string;
    status: ExchangeStatus;
}

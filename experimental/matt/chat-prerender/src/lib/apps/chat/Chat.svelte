<script lang="ts">
    import {
        provideVSCodeDesignSystem,
        vsCodeDivider,
        vsCodeButton,
        vsCodeProgressRing,
        vsCodeOption,
    } from "@vscode/webview-ui-toolkit";
    import { onMount, onDestroy } from "svelte";
    import { type Editor } from "@tiptap/core";

    import { onKey } from "$lib/common/utils/keypress";
    import { ChatModel } from "./models/chat-model";

    import WelcomeBanner from "./components/WelcomeBanner.svelte";
    import ChatDivider from "./components/DraggableChatDivider.svelte";
    import ActionBar from "./components/ActionBar.svelte";
    import LazyMessageList from "./components/LazyMessageList.svelte";
    import LazyRichTextInput from "$lib/common/components/inputs/LazyRichTextInput.svelte";
    import ContextAccordion from "./components/context/ContextAccordion.svelte";
    import ContextContents from "./components/context/ContextContents.svelte";

    // Load the VSCode Webview UI Toolkit
    /* provideVSCodeDesignSystem().register(
        vsCodeDivider(),
        vsCodeButton(),
        vsCodeProgressRing(),
        vsCodeOption(),
    );*/

    let chatModel = new ChatModel();
    let element: LazyRichTextInput;
    let newMessage = "";
    let inputFocused: boolean = false;

    // See if we can send the message -- if we can, we send it and clear the input
    $: canSendMsg = newMessage.trim() !== "" && !$chatModel.awaitingReply;
    $: canCancelMsg = $chatModel.awaitingReply;

    const sendMsg = (): boolean => {
        if (!canSendMsg) {
            return false;
        }
        chatModel.onSendUserMessage(newMessage);
        chatModel.clearDraftMessage();
        chatModel.specialContextInputModel.clearFiles();
        element?.clearContent();
        newMessage = "";

        return true;
    };
    const cancelMsg = () => {
        if (!canCancelMsg) {
            return;
        }
        chatModel.onCancelPrevMessage();
    };
    // When we clear history, we need to focus the textarea so
    // that the user can start typing again
    $: canClearHistory = $chatModel.chatHistory.length !== 0 && !$chatModel.awaitingReply;
    const clearHistory = () => {
        element?.focus();
        chatModel.clearHistory();
    };

    let editor: Editor | undefined;
    const saveDraftMessage = () => {
        chatModel.saveDraftMessage(editor?.getJSON());
    };

    // When draft message is updated, or the editor is updated, we update the content with the latest draft
    $: if (editor && $chatModel.draftMessage) {
        editor.commands.setContent($chatModel.draftMessage);
        newMessage = editor.getText();
    }

    // When the element is first rendered and goes from undefined => an HTMLElement,
    // we need to focus it so that the us er can start typing
    onMount(() => {
        element?.focus();
    });
    onDestroy(saveDraftMessage);

    // Track the variables associated with the draggable
    let inputArea: HTMLElement;
</script>

<!--
<svelte:window
    on:message={chatModel.handleMessageFromExtension}
    on:focus={() => element?.focus()}
    on:blur={saveDraftMessage}
    on:beforeunload={saveDraftMessage}
/>
-->

<main class="l-chat-container">
    <WelcomeBanner />
    <LazyMessageList {chatModel} />
    <ChatDivider bind:inputArea highlighted={inputFocused} />
    <div
        bind:this={inputArea}
        class="l-input-area"
        on:click={() => element?.focus()}
        on:keyup={() => {}}
        role="button"
        tabindex="0"
    >
        {#if $chatModel.enableDebugFeatures}
            <ContextAccordion contextModel={$chatModel.specialContextInputModel}>
                <ContextContents contextModel={$chatModel.specialContextInputModel} />
            </ContextAccordion>
        {/if}
        <div class="l-input-area__input">
            <LazyRichTextInput
                bind:this={element}
                bind:editor
                editable
                onContentChanged={(msg) => (newMessage = msg)}
                onFocus={() => (inputFocused = true)}
                onBlur={() => (inputFocused = false)}
                onEnter={sendMsg}
                getSuggestionItems={chatModel.findFiles}
                updateFilepaths={chatModel.specialContextInputModel.updateFiles}
            />
        </div>
        <ActionBar
            {chatModel}
            {canClearHistory}
            {canSendMsg}
            {canCancelMsg}
            awaitingReply={$chatModel.awaitingReply}
            {clearHistory}
            {sendMsg}
            {cancelMsg}
        />
    </div>
</main>

<style>
    .l-chat-container {
        display: grid;
        width: 100%;
        max-width: 100%;

        height: 100vh;
        margin: auto;
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto auto;
    }

    .l-input-area {
        position: relative;
        display: flex;
        flex-direction: column;

        width: 100%;
        overflow: hidden;
        /* The input and dialog areas should both be at least 100px tall,
        so we restrict the dimensions the input area can take up */
        min-height: 150px;
        height: 100%;
        max-height: 75vh;
    }

    .l-input-area__input {
        flex: 1 1 auto; /* Input grows to fill the available space minus action bar */
        min-height: 40px;

        display: flex;

        /* Add margin to input area */
        margin: var(--p-2);
        /* Remove margin from the right of the input area for the scrollbar to hug right side*/
        margin-right: 0;
        overflow: hidden;
    }
</style>

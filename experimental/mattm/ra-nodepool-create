#!/bin/bash

set -euo pipefail

# ra-nodepool-create gcp-agent0             n2-2  n2-standard-80  general
# ra-nodepool-create gcp-prod-agent0        n2-2  n2-standard-80  general
# ra-nodepool-create gcp-prod-agent0        n2-2  n2-standard-80  staging
# ra-nodepool-create gcp-eu-w4-prod-agent0  n2-2  n2-standard-80  general

create() {
	declare -r cluster="$1"
	declare -r pool_name_suffix="$2"
	declare -r machine_type="$3"
	declare -r pool_group="${4:-"general"}"

	if [[ "$cluster" == "gcp-prod-agent0" ]]; then
		declare -r project="agent-sandbox-prod"
		declare -r region="us-central1"
		declare -r node_locations="$region-a,$region-b,$region-c,$region-f"
		declare -r node_sa="${cluster}-gke-nodepool@${project}.iam.gserviceaccount.com"
	elif [[ "$cluster" == "gcp-eu-w4-prod-agent0" ]]; then
		declare -r project="agent-sandbox-prod"
		declare -r region="europe-west4"
		declare -r node_locations="$region-a,$region-b,$region-c"
		declare -r node_sa="${cluster}-node@${project}.iam.gserviceaccount.com"
	elif [[ "$cluster" == "gcp-us1" ]]; then
		declare -r project="augment-research-gsc"
		declare -r region="us-central1"
		declare -r node_locations="$region-a,$region-b,$region-c,$region-f"
		declare -r node_sa="${cluster}-gke-nodepool@${project}.iam.gserviceaccount.com"
	else
		declare -r project="augment-research-gsc"
		declare -r region="us-central1"
		declare -r node_locations="$region-a,$region-b,$region-c,$region-f"
		declare -r node_sa="${cluster}-gke-nodepool@${project}.iam.gserviceaccount.com"
	fi

	declare -ra cmd=(
		gcloud container node-pools create
		--project="$project"
		--region="$region"
		--cluster="$cluster"

		"ws-${pool_group}-${pool_name_suffix}"

		--node-labels=raws.augmentcode.com/pool-group="$pool_group"
		--node-taints=raws.augmentcode.com/pool-group="$pool_group":NoSchedule

		--machine-type="$machine_type"
		--disk-type=pd-balanced
		--disk-size=128
		--ephemeral-storage-local-ssd=count=0

		--node-locations="$node_locations"

		--enable-autoscaling
		--location-policy=ANY
		--total-min-nodes=1
		--total-max-nodes=32
		--num-nodes=0

		--enable-autoupgrade
		--enable-autorepair
		--enable-surge-upgrade
		--max-surge-upgrade=1
		--max-unavailable-upgrade=0

		--image-type=COS_CONTAINERD
		--enable-nested-virtualization
		--shielded-secure-boot
		--shielded-integrity-monitoring
		--service-account="$node_sa"
	)

	printf "$(tput bold)%s$(tput sgr0)\n" "${cmd[*]}"
	"${cmd[@]}"
}

create "$@"

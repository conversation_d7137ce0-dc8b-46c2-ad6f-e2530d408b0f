# Notes on how we break down what runs and where.

augi-console                | main interactive, can be all interactive, batch, etc
cceval-prober-dogfood       | main interactive
expose-proxy                | main interactive
hindsight-prober-dogfood    | main interactive
webserver                   | main interactive
metastore ("spark guys")    | main interactive
gh-runner                   | main interactive
->ci-worker                 | main interactive

determined                  | batch

jumphost                    | interactive, batch
augi-user-sa-sync           | interactive, batch?, not core

grafana.helm                | core
ssh-keys-syncer             | core
userauth (UI)               | core
config-connector            | core, maybe all
devex metrics               | core
harbor (master)             | core
harbor (leaf)               | non-core, non-gcp

external-dns                | all
cert-manager                | all
ingress-nginx               | all
bazel-cache                 | all (geographical?) (not-core?)
bitnami-ss.helm             | all
loki                        | all

object store                | geographical
filesystem                  | interactive, batch, (not-core?)

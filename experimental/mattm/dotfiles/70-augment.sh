# vim: set ft=bash:

################################################################################
#
# AUGMENT prompt_command
#
# Keep an $AUGMENT (and $A) environmental variable set which points to the
# most appropriate augment repo path. Use `.augmentroot` as the sentinal, using
# anything git related won't work with unpacked archives.
#

_augment_set_AUGMENT() {
	declare -a paths=()

	# Search path starts with P<PERSON> and up
	declare cur="$PWD"
	while [[ "$cur" ]]; do
		paths+=("$cur")
		cur="${cur%/*}"
	done

	# Also search some well-known locations
	paths+=(
		"$HOME/augment"
		"$HOME/augment.git"
		"$HOME/src/augment"
		"$HOME/src/augment.git"
	)

	for d in "${paths[@]}"; do
		if [[ -e "$d/.augmentroot" ]]; then
			declare -g AUGMENT="$d" A="$d"
			return
		fi
	done

	declare -g AUGMENT= A=
}

PROMPT_COMMAND+=(_augment_set_AUGMENT)
_augment_set_AUGMENT # also run at startup

################################################################################
#
# `adot` setup
#

adot() { git --git-dir="$HOME/.config/dotfiles.augment.git" --work-tree="$HOME" "$@"; }
__git_complete adot __git_main  # Ack NOT public function warning

################################################################################
#
# `k-*` `kubectl` aliases

_completion_loader kubectl
_kubectl_alias() {
	declare -r name="$1" ctx="${2:-}" ns="${3:-}"
	declare -a a=(kubectl)
	[[ "$ctx" ]] && a+=(--context="$ctx")
	[[ "$ns"  ]] && a+=(-n="$ns")

	alias "$name"="${a[*]}"

	if [[ $(type -t compopt) = "builtin" ]]; then
		complete -o default -F __start_kubectl "$name"
	else
		complete -o default -o nospace -F __start_kubectl "$name"
	fi
}

_kubectl_alias k
# Research
_kubectl_alias k-core       gcp-core0
_kubectl_alias k-gcp        gcp-us1
_kubectl_alias k4           cw-east4
_kubectl_alias k4-adm       cw-east4-admin
# Dev
_kubectl_alias k-dev        us-central1-dev
_kubectl_alias k-agent      gcp-agent0
# Prod
_kubectl_alias k-prod       us-central1-prod
_kubectl_alias k-prod-eu    eu-west4-prod
_kubectl_alias k-prod-gsc   prod-gsc
_kubectl_alias k-prod-agent gcp-prod-agent0
_kubectl_alias k-eu-agent   gcp-eu-w4-prod-agent0


################################################################################
#
# `go` aliases
#

alias go-test='go test -tags=testing -vet=all -v --coverprofile=/tmp/coverage && go tool cover --html=/tmp/coverage && [[ -d /mnt/efs/augment/public_html/mattm/ ]] && mv -f /tmp/cover*/coverage.html /mnt/efs/augment/public_html/mattm/'
alias go-test-r='go test -tags=testing -vet=all -v --coverprofile=/tmp/coverage ./...; go tool cover --html=/tmp/coverage && [[ -d /mnt/efs/augment/public_html/mattm/ ]] && mv -f /tmp/cover*/coverage.html /mnt/efs/augment/public_html/mattm/'
alias go-test-all='go test -tags=testing -vet=all -v --coverprofile=/tmp/coverage all; go tool cover --html=/tmp/coverage  && [[ -d /mnt/efs/augment/public_html/mattm/ ]]&& mv -f /tmp/cover*/coverage.html /mnt/efs/augment/public_html/mattm/'
alias gofmt-d='find -type f -name "*.go" -exec gofmt -d {} +'
alias gofmt-w='find -type f -name "*.go" -exec gofmt -w {} +'

################################################################################
#
# Augment Research Infra
#

_grafana() {
	declare -r hostname="$1" ctx="$2" ns="$3" sec="$4" path="$5"
	shift 3
	cmd=(
		curl -sL -u "admin:$(kubectl --context="$ctx" -n "$ns" get secret/"$sec" --template='{{index .data "admin-password" | base64decode}}')"
		https://"$hostname"/api/"$path"
		"$@"
	)
	"${cmd[@]}"
}
alias grafana='_grafana grafana.r.augmentcode.com gcp-core0 grafana grafana0-admin'


################################################################################
#
# With access to `augment.git` (so, on a DevPod)
#

if [[ -d "$HOME"/augment ]]; then
	bashrc-firstpass && cd "$AUGMENT"

	alias devpod='augi build -NU -- devpod --auto-user-prefix'

	########################################################################
	#
	# Bazel aliases
	#

	alias gazelle='bazel run //:gazelle -- update $PWD'
	alias bazel-test='bazel test :all --test_output=all --test_arg="-test.v"'
	alias crane='bazel run @com_github_google_go_containerregistry//cmd/crane --'

	########################################################################
	#
	# `cd` aliases
	#

	declare -A _augment_cd_aliases

	_augment_cd() {
		declare -r name="$1" subdir="$2"
		declare -r target="${_augment_cd_aliases[$name]}"
		declare -r target_abs="$A/$target"
		cd "$target_abs/$subdir"
	}

	_augment_cd_completion() {
		declare -r cmd="$1" word="$2" prev="$3"
		declare -r name="${cmd#cd-}"
		declare -r target="${_augment_cd_aliases[$name]}"
		declare -r target_abs="$A/$target"
		COMPREPLY=(
			$(compgen -d -S/ -- "$target_abs/$word" | sed "s|^$target_abs/||")
		)

	}

	augment-cd-alias() {
		declare -r name="$1" target="$2"
		declare -r aname="cd-$name"

		_augment_cd_aliases["$name"]="$target"
		alias "$aname"="_augment_cd '$name'"
		complete -o nospace -F _augment_cd_completion "$aname"
	}

	augment-cd-alias augment      ""
	augment-cd-alias experimental experimental
	augment-cd-alias home         experimental/mattm
	augment-cd-alias services     services
	augment-cd-alias infra        research/infra
	augment-cd-alias lib          research/infra/lib
	augment-cd-alias devpod       research/infra/lib/augment/devpod
	augment-cd-alias svc          research/infra/svc
	augment-cd-alias img          research/environments/containers
	augment-cd-alias devvm        deploy/quickstart/services/dev_vm
	augment-cd-alias api-proxy    services/api_proxy
	augment-cd-alias clients      clients
	augment-cd-alias agent-guy    experimental/guy/agent_qa
	augment-cd-alias agent-service services/remote_agents
	augment-cd-alias agent-bh     clients/beachhead
	augment-cd-alias agent-img    clients/beachhead/img


	########################################################################
	#
	# API Proxy helpers
	#

	bashrc-firstpass && devns='dev-mattm'

	k-devns() {
		declare context=us-central1-dev
		if [[ "$devns" == staging* ]]; then
			context=us-central1-prod
		fi
		kubectl --context="$context" -n "$devns" "$@"
	}
	k-remote-agent() {
		declare context=gcp-agent0
		if [[ "$devns" == staging* ]]; then
			context=gcp-prod-agent0
		fi
		kubectl --context="$context" -n "$devns" "$@"
	}

	api-proxy() {
		declare -r path="$1"; shift
		declare hostname="$devns.us-central.api.augmentcode.com"
		declare secret="$HOME/.config/augment/secrets."$devns".json"
		if [[ "$devns" == staging* ]]; then
			hostname="$devns.api.augmentcode.com"
		fi
		cmd=(
			curl -sL
			-H "Authorization: Bearer $(cat "$secret" | jq '."augment.sessions" | fromjson | .accessToken' -r)"
			"https://$hostname/$path"
			"$@"
		)
		"${cmd[@]}"
	}
	api-proxy-post() {
		api-proxy "$@" -H "Content-Type: application/json" -d@-
	}
	remote-agent-list() {
		printf '{}' | api-proxy-post "remote-agents/list" "$@"
	}
	remote-agent-update-id() {
		declare -g ID="$(remote-agent-list | jq '.remote_agents | max_by(.started_at) | .remote_agent_id' -r)"
		printf "%s\n" "$ID"
	}
	remote-agent-create() {
		declare prompt="${@}"
		if [[ -z "$prompt" ]]; then
			prompt='Please pause and wait for further instructions.'
			printf "WARN: Using default prompt: %s.\n" "$prompt" >&2
		fi
		prompt="${IMG:+"_flags:{img_tag=$IMG}"}$prompt"
		api-proxy-post "remote-agents/create" "$@" <<-EOF
		{
		    "workspace_setup": {
		        "starting_files": {
		            "github_commit_ref": {
		                "repository_url": "https://github.com/augmentcode/augment",
		                "git_ref": "main"
		            }
		        }
		    },
		    "initial_request_details": {
		        "request_nodes": [
		            {
		                "id": 0,
		                "type": 0,
		                "text_node": { "content": "$prompt" }
		            }
		        ],
		        "user_guidelines": "",
		        "workspace_guidelines": "",
		        "agent_memories": ""
		    },
		    "model": "",
		    "setup_script": "",
		    "token": ""
		}
		EOF
	}
	remote-agent-chat() {
		declare -r id="$1"; shift
		api-proxy-post "remote-agents/chat" <<-EOF
		{
		    "remote_agent_id": "$id",
		    "request_details": {
		        "request_nodes": [
		            {
		                "id": 0,
		                "type": 0,
		                "text_node": { "content": "$*" }
		            }
		        ],
		        "user_guidelines": "",
		        "workspace_guidelines": "",
		        "agent_memories": ""
		    }
		}
		EOF
	}
	remote-agent-chat-history() {
		declare -r id="$1"; shift
		printf '{"remote_agent_id": "%s"}' "$id" | api-proxy-post "remote-agents/get-chat-history" "$@"
	}
	remote-agent-interrupt() {
		declare -r id="$1"; shift
		printf '{"remote_agent_id": "%s"}' "$id" | api-proxy-post "remote-agents/interrupt" "$@"
	}
	remote-agent-delete() {
		declare -r id="$1"; shift
		printf '{"remote_agent_id": "%s"}' "$id" | api-proxy-post "remote-agents/delete" "$@"
	}
	remote-agent-add-ssh-key() {
		declare -r id="$1"; shift
		jq -R -s 'split("\n") | map(select(length > 0))' | jq --arg id "$id" '{"remote_agent_id": $id, "public_keys": .}' | \
		api-proxy-post "remote-agents/add-ssh-key" "$@"
	}
	remote-agent-delete-all() {
		for id in $(remote-agent-list | jq '.remote_agents[].remote_agent_id' -r); do
			remote-agent-delete "$id"; echo
		done
	}
	remote-agent-delete-all-k8s() {
		remote-agent-list | jq '.remote_agents[].remote_agent_id | "raws-" + .' -r | xargs -r kubectl --context=gcp-agent0 delete secret
	}

	log-api-proxy() {
		k-devns -n "$devns" logs deploy/api-proxy --all-pods --max-log-requests=99 --prefix=false "$@"
	}
	log-remote-agents() {
		k-devns -n "$devns" logs deploy/remote-agents --all-pods --max-log-requests=99 --prefix=false "$@"
	}
	remote-agent-config() {
		declare -r id="$1"; shift
		k-remote-agent -n "$devns" get secret/raws-"$id" --template='{{index .data "workspace-agent-config.json.gz" | base64decode}}' | gunzip
	}
	remote-agent-logs() {
		declare -r id="$1"; shift
		k-remote-agent -n "$devns" logs raws-"$id"-0 "$@"
	}
	remote-agent-exec() {
		declare -r id="$1"; shift
		declare cmd=( "$@" )
		(( ${#cmd[@]} == 0 )) && cmd=(/bin/bash)
		k-remote-agent -n "$devns" exec -ti raws-"$id"-0 -- "${cmd[@]}"
	}

	dev-deploy() {
		if (( $# == 0 )); then
			declare -ra services=(remote_agents_all)
		else
			declare -ra services=("$@")
		fi
		declare -a cmd=(bazel run //services/deploy:dev_deploy -- --namespace="$devns" --operation=Apply --services "${services[@]}")
		printf "%s%s%s\n" "$(tput bold)" "${cmd[*]}" "$(tput sgr0)"
		"${cmd[@]}"
	}

fi

################################################################################
#
# vim plugin
#

augment-vim-update() {
	declare -r dir="$HOME/.config/vim/pack/augment/opt/augment.vim.git"
	if [[ -d "$dir" ]]; then
		(
			cd "$dir" && git pull
		)
	fi
}

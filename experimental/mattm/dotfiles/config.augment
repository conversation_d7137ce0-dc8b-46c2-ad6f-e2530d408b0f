# vim: ft=gitconfig

[user]
        name = <PERSON>
        email = <EMAIL>

[alias]
        s = !cd ${GIT_PREFIX:-./} && git status && git stash list --pretty='%C(yellow)%gd %C(blue)%gs' && git log -n1 aug/main^..
        pfl = !git push --force-with-lease aug HEAD:mattm-$(git rev-parse --abbrev-ref HEAD)

[remote "aug"]
        url = **************:augmentcode/augment.git
        fetch = +refs/heads/main:refs/remotes/aug/main
        fetch = +refs/heads/mattm-*:refs/remotes/aug/mattm-*
        push = +refs/heads/*:refs/heads/mattm-*

[branch "default"]
        remote = aug
        merge = refs/heads/main

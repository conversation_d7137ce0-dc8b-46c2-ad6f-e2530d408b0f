{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id f56e274f-f891-4702-9048-0d349e4c9ecc.\n"]}], "source": ["from pathlib import Path\n", "\n", "from research.models.fastforward_models import StarCoder2_FastForward\n", "from base.fastforward.fwd_utils import get_checkpoint_sha\n", "\n", "AUGMENT_CHECKPOINTS_ROOT = Path(\"/mnt/efs/augment/checkpoints\")\n", "RAVEN_V6_CKPT = (\n", "    AUGMENT_CHECKPOINTS_ROOT\n", "    / \"next-edit-gen\"\n", "    / \"S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b-ffw\"\n", ")\n", "\n", "def load_edit_gen():\n", "    edit_model = StarCoder2_FastForward(\n", "        model_path=RAVEN_V6_CKPT,\n", "        checkpoint_path=RAVEN_V6_CKPT,\n", "        checkpoint_sha256=get_checkpoint_sha(RAVEN_V6_CKPT),\n", "    )\n", "    edit_model.load()\n", "    return edit_model\n", "\n", "edit_model = load_edit_gen()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_next_edit.gen_prompt_formatter import EditGenPromptInput\n", "from base.prompt_format_next_edit.gen_prompt_formatter import EditGenPromptFormatter\n", "from base.diff_utils.diff_utils import File\n", "from base.retrieval.chunking.line_based_chunking import LineChunkContents\n", "from base.diff_utils.changes import Changed\n", "from collections.abc import Sequence\n", "import torch\n", "from research.core.types import Chunk\n", "\n", "def get_edit_model_scores(\n", "    current_file: File,\n", "    chunks: list[LineChunkContents],\n", "    recently_edited_files: Sequence[Changed[File]],\n", "    edit_model: StarCoder2_FastForward,\n", "    edit_gen_prompt_formatter: EditGenPromptFormatter,\n", ") -> list[float]:\n", "    scores = []\n", "    for chunk in chunks:\n", "        edit_model_input = EditGenPromptInput(\n", "            current_file=current_file,\n", "            edit_region=chunk.crange(),\n", "            instruction=\"\",\n", "            recent_changes=tuple(recently_edited_files),\n", "            retrieval_chunks=[],\n", "        )\n", "        tokens = edit_gen_prompt_formatter.format_input_prompt(edit_model_input).tokens\n", "        logits = edit_model.forward_pass_single_logits(torch.tensor(tokens))\n", "        score = (\n", "            torch.log_softmax(logits[-1], dim=-1)[\n", "                edit_gen_prompt_formatter.tokenizer.special_tokens.has_change\n", "            ]\n", "            .cpu()\n", "            .tolist()\n", "        )\n", "        scores.append(score)\n", "    return scores\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/vaibhav/dogfood-v2_2025_02_03_28days_10s_task_inputs_2000.pkl\",\n", "    \"rb\",\n", ") as f:\n", "    from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (\n", "        NextEditHindsightTaskInput,\n", "    )\n", "\n", "    task_inputs: list[NextEditHindsightTaskInput] = pickle.load(f)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[LineChunkContents(text='/* eslint-disable @typescript-eslint/no-unsafe-member-access */\\n\\n/* eslint-disable @typescript-eslint/no-unsafe-call */\\n\\n/* eslint-disable @typescript-eslint/no-unsafe-assignment */\\nimport * as vscode from \"vscode\";\\n\\nimport { type AugmentLogger, getLogger } from \"./logging\";\\nimport { MonitoredParameter } from \"./monitored-parameter\";\\nimport { DisposableService } from \"./utils/disposable-service\";\\n\\nexport type CodeInstructionConfig = {\\n    model?: string;\\n};\\n\\nconst DEFAULT_MODEL_DISPLAY_NAME_TO_ID = {\\n    // eslint-disable-next-line @typescript-eslint/naming-convention\\n    Augment: null,\\n};\\nexport type ChatConfig = {\\n    url?: string;\\n    model?: string;\\n    modelDisplayNameToId?: {\\n        [key: string]: string | null;\\n    };\\n    stream?: boolean;\\n    useRichTextHistory?: boolean;\\n    smartPasteUsePrecomputation?: boolean;\\n    experimentalFullFilePaste?: boolean;\\n    enableEditableHistory?: boolean;\\n    userGuidelines?: string;\\n};\\n\\nexport type AutofixConfig = {\\n    enabled?: boolean;\\n    locationUrl?: string;\\n    autofixUrl?: string;\\n};\\n\\nexport type OAuthConfig = {\\n    clientID?: string;\\n    url?: string;\\n};\\n\\nexport type RecencySignalManagerConfig = {\\n    collectTabSwitchEvents: boolean;\\n};\\n', line_offset=0, char_offset=0, length_in_lines=47, header=''),\n", " LineChunkContents(text='\\nexport type NextEditConfig = {\\n    enabled?: boolean; // TODO is this deprecated too?\\n    // TODO: Deprecate and replace with augment.enableBackgroundSuggestions.\\n    backgroundEnabled: boolean;\\n\\n    url?: string;\\n    locationUrl?: string;\\n    generationUrl?: string;\\n\\n    model?: string;\\n\\n    showInstructionTextbox: boolean;\\n\\n    /** How long to wait before sending a new request. */\\n    useDebounceMs?: number;\\n\\n    /** When enabled, use cursor decorations instead of bottom decorations. */\\n    useCursorDecorations?: boolean;\\n\\n    useSmallHover?: boolean;\\n\\n    noDiffMode?: boolean;\\n    animateNoDiffMode?: boolean;\\n\\n    allowDuringDebugging?: boolean;\\n\\n    /** Use mock results if $filename.next-edit-results.json5 exists. */\\n    useMockResults?: boolean;\\n\\n    noDiffModeUseCodeLens?: boolean;\\n\\n    showDiffByDefault?: boolean;\\n\\n    enableBackgroundSuggestions: boolean;\\n\\n    enableGlobalBackgroundSuggestions: boolean;\\n\\n    showAllBackgroundSuggestionLineHighlights: boolean;\\n};\\n\\nexport type PreferenceCollectionConfig = {\\n    enable: boolean;\\n    enableRetrievalDataCollection: boolean;\\n    enableRandomizedMode: boolean;\\n};\\n\\nexport type SmartPasteConfig = {\\n    url?: string;\\n    model?: string;\\n};\\n\\nexport type InstructionsConfig = {\\n    model?: string;\\n};\\n', line_offset=47, char_offset=1210, length_in_lines=55, header=''),\n", " LineChunkContents(text='\\n// NOTE: Prefer `enable*` for naming in config.\\nexport type AugmentConfig = {\\n    apiToken: string;\\n    completionURL: string;\\n    codeInstruction: CodeInstructionConfig;\\n    chat: ChatConfig;\\n    autofix: AutofixConfig;\\n    modelName: string;\\n    enableUpload: boolean;\\n    enableShortcutsAboveSelectedText: boolean;\\n    shortcutsDisplayDelayMS: number;\\n    enableEmptyFileHint: boolean;\\n    enableDataCollection: boolean;\\n    enableDebugFeatures: boolean;\\n    enableReviewerWorkflows: boolean;\\n    oauth: OAuthConfig;\\n    completions: {\\n        // User settings\\n        enableAutomaticCompletions: boolean;\\n        disableCompletionsByLanguage: Set<string>;\\n        enableQuickSuggestions: boolean;\\n\\n        // Advanced\\n        timeoutMs: number;\\n        maxWaitMs: number;\\n        addIntelliSenseSuggestions: boolean;\\n        filterThreshold?: number;\\n    };\\n    openFileManagerV2: {\\n        enabled: boolean;\\n    };\\n    nextEdit: NextEditConfig;\\n    recencySignalManager: RecencySignalManagerConfig;\\n    preferenceCollection: PreferenceCollectionConfig;\\n    vcs: {\\n        watcherEnabled: boolean;\\n    };\\n    conflictingCodingAssistantCheck: boolean;\\n    smartPaste: SmartPasteConfig;\\n    instructions: InstructionsConfig;\\n};\\nexport type AugmentConfigKey = keyof AugmentConfig;\\n', line_offset=102, char_offset=2477, length_in_lines=43, header=''),\n", " LineChunkContents(text='\\nexport interface UserConfig {\\n    completions: {\\n        enableAutomaticCompletions: boolean;\\n        disableCompletionsByLanguage: Array<string>;\\n        enableQuickSuggestions: boolean;\\n    };\\n\\n    enableShortcutsAboveSelectedText: boolean;\\n    shortcutsDisplayDelayMS: number;\\n    enableEmptyFileHint: boolean;\\n    conflictingCodingAssistantCheck: boolean;\\n    chat: {\\n        userGuidelines?: string;\\n    };\\n    nextEdit: {\\n        enableBackgroundSuggestions: boolean;\\n        enableGlobalBackgroundSuggestions: boolean;\\n        showAllBackgroundSuggestionLineHighlights: boolean;\\n    };\\n', line_offset=145, char_offset=3760, length_in_lines=20, header=''),\n", " LineChunkContents(text='    advanced: {\\n        apiToken: string;\\n        completionURL: string;\\n\\n        // The following are internal only\\n        oauth: OAuthConfig;\\n        model: string;\\n        codeInstruction: CodeInstructionConfig;\\n        chat: ChatConfig;\\n        autofix: AutofixConfig;\\n        enableDebugFeatures: boolean;\\n        enableWorkspaceUpload: boolean;\\n        enableReviewerWorkflows: boolean;\\n        completions: {\\n            timeoutMs: number;\\n            maxWaitMs: number;\\n            addIntelliSenseSuggestions: boolean;\\n            filterThreshold?: number;\\n        };\\n        openFileManagerV2: {\\n            enabled: boolean;\\n        };\\n        enableDataCollection: boolean;\\n        nextEditURL?: string;\\n        nextEditLocationURL?: string;\\n        nextEditGenerationURL?: string;\\n        nextEditBackgroundGeneration?: boolean;\\n        nextEdit: NextEditConfig;\\n        recencySignalManager: RecencySignalManagerConfig;\\n        preferenceCollection: PreferenceCollectionConfig;\\n        vcs: {\\n            watcherEnabled: boolean;\\n        };\\n        smartPaste?: SmartPasteConfig;\\n        instructions?: InstructionsConfig;\\n    };\\n}\\n', line_offset=165, char_offset=4354, length_in_lines=37, header='export interface UserConfig {\\n'),\n", " LineChunkContents(text='\\n/**\\n * AugmentConfigListener is a class that listens for changes to the extension\\n * configuration. It logs configuration changes of interest and notifies listeners\\n * when the config has changed. It is preferrable to listen to configuration changes\\n * here than to use your own `onDidChangeConfiguration` event listener, as this class\\n * will log configuration changes before notifying listeners.\\n */\\nexport class AugmentConfigListener extends DisposableService {\\n    private _config!: AugmentConfig;\\n    private _configChanged = new vscode.EventEmitter<ConfigChanges>();\\n\\n    private _configMonitor: MonitoredParameter<AugmentConfig>;\\n\\n    private readonly _logger: AugmentLogger = getLogger(\"AugmentConfigListener\");\\n\\n    constructor() {\\n        super();\\n\\n        this._configMonitor = new MonitoredParameter<AugmentConfig>(\"Config\", this._logger);\\n\\n        this._refreshConfig();\\n        this.addDisposable(\\n            vscode.workspace.onDidChangeConfiguration(() => {\\n                return this._refreshConfig();\\n            })\\n        );\\n    }\\n\\n    // `config` is the current configuration.\\n    get config(): Readonly<AugmentConfig> {\\n        return this._config;\\n    }\\n\\n    // onDidChange is an event that clients can listen on to be notified of changes\\n    // to the extension configuration.\\n    get onDidChange(): vscode.Event<ConfigChanges> {\\n        return this._configChanged.event;\\n    }\\n', line_offset=202, char_offset=5500, length_in_lines=39, header='export interface UserConfig {\\n'),\n", " LineChunkContents(text='\\n    // _refreshConfig caches the current extension configuration and logs changes of\\n    // interest.\\n    private _refreshConfig() {\\n        const previousConfig = this._config;\\n        this._config = AugmentConfigListener.normalizeConfig(this._getUserConfig());\\n        if (this._configMonitor.update(this._config)) {\\n            this._configChanged.fire({\\n                previousConfig,\\n                newConfig: this._config,\\n            });\\n        }\\n    }\\n', line_offset=241, char_offset=6904, length_in_lines=13, header='export class AugmentConfigListener extends DisposableService {\\n'),\n", " LineChunkContents(text='\\n    // normalizeConfig converts UserConfig to an AugmentConfig.\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n        return {\\n            apiToken: config.advanced.apiToken,\\n            completionURL: config.advanced.completionURL,\\n            modelName: config.advanced.model,\\n            conflictingCodingAssistantCheck: config.conflictingCodingAssistantCheck,\\n            codeInstruction: {\\n                model: config.advanced.codeInstruction.model || undefined,\\n            },\\n            chat: {\\n                url: config.advanced.chat.url || undefined,\\n                model: config.advanced.chat.model || undefined,\\n                stream: config.advanced.chat.stream ?? undefined,\\n                enableEditableHistory: config.advanced.chat.enableEditableHistory ?? false,\\n                useRichTextHistory: config.advanced.chat.useRichTextHistory ?? true,\\n                smartPasteUsePrecomputation:\\n                    config.advanced.chat.smartPasteUsePrecomputation ?? true,\\n                experimentalFullFilePaste: config.advanced.chat.experimentalFullFilePaste ?? false,\\n                modelDisplayNameToId:\\n                    config.advanced.chat.modelDisplayNameToId || DEFAULT_MODEL_DISPLAY_NAME_TO_ID,\\n                userGuidelines: config.chat.userGuidelines || \"\",\\n            },\\n            autofix: {\\n                enabled: config.advanced.autofix.enabled,\\n                locationUrl: config.advanced.autofix.locationUrl || undefined,\\n                autofixUrl: config.advanced.autofix.autofixUrl || undefined,\\n            },\\n', line_offset=254, char_offset=7368, length_in_lines=29, header='export class AugmentConfigListener extends DisposableService {\\n'),\n", " LineChunkContents(text='            oauth: {\\n                clientID: config.advanced.oauth.clientID || \"augment-vscode-extension\",\\n                url: config.advanced.oauth.url || \"https://auth.augmentcode.com\",\\n            },\\n            enableUpload: config.advanced.enableWorkspaceUpload,\\n            enableShortcutsAboveSelectedText: config.enableShortcutsAboveSelectedText,\\n            shortcutsDisplayDelayMS: config.shortcutsDisplayDelayMS,\\n            enableEmptyFileHint: config.enableEmptyFileHint,\\n            enableDataCollection: config.advanced.enableDataCollection,\\n            enableDebugFeatures: config.advanced.enableDebugFeatures,\\n            enableReviewerWorkflows: config.advanced.enableReviewerWorkflows,\\n            completions: {\\n                enableAutomaticCompletions: config.completions.enableAutomaticCompletions,\\n                disableCompletionsByLanguage: new Set(\\n                    config.completions.disableCompletionsByLanguage\\n                ),\\n                enableQuickSuggestions: config.completions.enableQuickSuggestions,\\n\\n                timeoutMs: config.advanced.completions.timeoutMs,\\n                maxWaitMs: config.advanced.completions.maxWaitMs,\\n                addIntelliSenseSuggestions: config.advanced.completions.addIntelliSenseSuggestions,\\n                filterThreshold: config.advanced.completions.filterThreshold,\\n            },\\n            openFileManagerV2: {\\n                enabled: config.advanced.openFileManagerV2.enabled,\\n            },\\n', line_offset=283, char_offset=8959, length_in_lines=26, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n'),\n", " LineChunkContents(text='            nextEdit: {\\n                enabled: config.advanced.nextEdit.enabled,\\n                backgroundEnabled: config.advanced.nextEdit.backgroundEnabled,\\n\\n                url: config.advanced.nextEdit.url,\\n                locationUrl: config.advanced.nextEdit.locationUrl || config.advanced.nextEdit.url,\\n                generationUrl:\\n                    config.advanced.nextEdit.generationUrl || config.advanced.nextEdit.url,\\n                showInstructionTextbox: config.advanced.nextEdit.showInstructionTextbox,\\n                model: config.advanced.nextEdit.model,\\n                useDebounceMs: config.advanced.nextEdit.useDebounceMs,\\n                useCursorDecorations: config.advanced.nextEdit.useCursorDecorations,\\n                useSmallHover: config.advanced.nextEdit.useSmallHover,\\n                noDiffMode: config.advanced.nextEdit.noDiffMode,\\n                animateNoDiffMode: config.advanced.nextEdit.animateNoDiffMode,\\n                allowDuringDebugging: config.advanced.nextEdit.allowDuringDebugging,\\n                useMockResults: config.advanced.nextEdit.useMockResults,\\n                noDiffModeUseCodeLens: config.advanced.nextEdit.noDiffModeUseCodeLens,\\n                showDiffByDefault: config.advanced.nextEdit.showDiffByDefault,\\n                enableBackgroundSuggestions: config.nextEdit.enableBackgroundSuggestions,\\n                enableGlobalBackgroundSuggestions:\\n                    config.nextEdit.enableGlobalBackgroundSuggestions,\\n                showAllBackgroundSuggestionLineHighlights:\\n                    config.nextEdit.showAllBackgroundSuggestionLineHighlights,\\n            },\\n', line_offset=309, char_offset=10452, length_in_lines=25, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n'),\n", " LineChunkContents(text='            recencySignalManager: config.advanced.recencySignalManager,\\n            preferenceCollection: {\\n                enable: config.advanced.preferenceCollection.enable,\\n                enableRetrievalDataCollection:\\n                    config.advanced.preferenceCollection.enableRetrievalDataCollection,\\n                enableRandomizedMode: config.advanced.preferenceCollection.enableRandomizedMode,\\n            },\\n            vcs: {\\n                watcherEnabled: config.advanced.vcs.watcherEnabled,\\n            },\\n            smartPaste: {\\n                url: config.advanced.smartPaste?.url,\\n                model: config.advanced.smartPaste?.model,\\n            },\\n            instructions: {\\n                model: config.advanced.instructions?.model,\\n            },\\n        };\\n    }\\n\\n    private _getUserConfig(): UserConfig {\\n        const config = vscode.workspace.getConfiguration(\"augment\");\\n        return AugmentConfigListener.normalizeUserConfig(config);\\n    }\\n', line_offset=334, char_offset=12092, length_in_lines=24, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n'),\n", " LineChunkContents(text='\\n    /**\\n     * This method will move old settings to a new key.\\n     *\\n     * Because the APIs for this are not synchronous, you must either\\n     * check the old and new keys when normalizing the config OR you\\n     * must anticipate the new values getting updated shortly after\\n     * initialization.\\n     */\\n    async migrateLegacyConfig() {\\n        const config = vscode.workspace.getConfiguration(\"augment\");\\n        // Setting was moved on 2024-09 (approx. v0.211.0)\\n        await this._moveConfig(\\n            config,\\n            \"enableAutomaticCompletions\",\\n            \"completions.enableAutomaticCompletions\"\\n        );\\n\\n        // Setting was moved on 2024-09 (approx. v0.211.0)\\n        await this._moveConfig(\\n            config,\\n            \"disableCompletionsByLanguage\",\\n            \"completions.disableCompletionsByLanguage\"\\n        );\\n\\n        await this._moveConfig(\\n            config,\\n            \"enableBackgroundSuggestions\",\\n            \"nextEdit.enableBackgroundSuggestions\"\\n        );\\n        await this._moveConfig(\\n            config,\\n            \"enableGlobalBackgroundSuggestions\",\\n            \"nextEdit.enableGlobalBackgroundSuggestions\"\\n        );\\n        await this._moveConfig(\\n            config,\\n            \"showAllBackgroundSuggestionLineHighlights\",\\n            \"nextEdit.showAllBackgroundSuggestionLineHighlights\"\\n        );\\n    }\\n', line_offset=358, char_offset=13076, length_in_lines=41, header='export class AugmentConfigListener extends DisposableService {\\n    private _getUserConfig(): UserConfig {\\n'),\n", " LineChunkContents(text=\"\\n    /**\\n     * This method moves a users setting from one key to another.\\n     *\\n     * VSCode has two locations where settings can be kept, at\\n     * the global level and at the workspace level. To move a config\\n     * we need to copy values from both global and workspace level\\n     * to the new key.\\n     *\\n     * If there is an old key AND a new key value, we drop the old\\n     * key and leave the new value as is.\\n     *\\n     * @param config\\n     * @param oldKey\\n     * @param newKey\\n     * @returns\\n     */\\n    private async _moveConfig(\\n        config: vscode.WorkspaceConfiguration,\\n        oldKey: string,\\n        newKey: string\\n    ) {\\n        const oldValue = config.inspect(oldKey);\\n        if (!oldValue) {\\n            return;\\n        }\\n\\n        const newValue = config.inspect(newKey);\\n        const targets = [\\n            {\\n                target: vscode.ConfigurationTarget.Workspace,\\n                oldValue: oldValue.workspaceValue,\\n                newValue: newValue?.workspaceValue,\\n            },\\n            {\\n                target: vscode.ConfigurationTarget.Global,\\n                oldValue: oldValue.globalValue,\\n                newValue: newValue?.globalValue,\\n            },\\n        ];\\n        for (const t of targets) {\\n            if (t.oldValue === undefined) {\\n                // There is no old value to copy over, so do nothing.\\n                continue;\\n            }\\n            if (t.newValue === undefined) {\\n                // Update config for target if the new config hasn't been\\n                // set to a user value.\\n                await config.update(newKey, t.oldValue, t.target);\\n            }\\n            // Remove the old config value\\n            await config.update(oldKey, undefined, t.target);\\n        }\\n    }\\n\", line_offset=399, char_offset=14446, length_in_lines=54, header='export class AugmentConfigListener extends DisposableService {\\n    async migrateLegacyConfig() {\\n'),\n", " LineChunkContents(text='\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n        return {\\n            completions: {\\n                enableAutomaticCompletions: booleanWithDefault(\\n                    config.enableAutomaticCompletions ??\\n                        config.completions?.enableAutomaticCompletions,\\n                    true\\n                ),\\n                disableCompletionsByLanguage:\\n                    config.disableCompletionsByLanguage ||\\n                    config.completions?.disableCompletionsByLanguage ||\\n                    [],\\n                enableQuickSuggestions: booleanWithDefault(\\n                    config.completions?.enableQuickSuggestions,\\n                    true\\n                ),\\n            },\\n            chat: {\\n                userGuidelines: config.chat?.userGuidelines || \"\",\\n            },\\n            enableShortcutsAboveSelectedText: booleanWithDefault(\\n                config.enableShortcutsAboveSelectedText,\\n                false\\n            ),\\n            shortcutsDisplayDelayMS: intWithDefault(config.shortcutsDisplayDelayMS, 2000),\\n            enableEmptyFileHint: booleanWithDefault(config.enableEmptyFileHint, true),\\n            conflictingCodingAssistantCheck: booleanWithDefault(\\n                config.conflictingCodingAssistantCheck,\\n                true\\n            ),\\n', line_offset=453, char_offset=16212, length_in_lines=31, header='export class AugmentConfigListener extends DisposableService {\\n'),\n", " LineChunkContents(text='            advanced: {\\n                // These options were moved from top level to advanced, so\\n                // check both places.\\n                apiToken: (config.advanced?.apiToken || config.apiToken || \"\").trim().toUpperCase(),\\n                completionURL: (\\n                    config.advanced?.completionURL ||\\n                    config.completionURL ||\\n                    \"\"\\n                ).trim(),\\n\\n                // Internal settings\\n                enableWorkspaceUpload: booleanWithDefault(\\n                    config.advanced?.enableWorkspaceUpload,\\n                    true\\n                ),\\n\\n                model: config.advanced?.model || \"\",\\n\\n                enableDebugFeatures: booleanWithDefault(\\n                    config.advanced?.enableDebugFeatures,\\n                    false\\n                ),\\n\\n                enableReviewerWorkflows: booleanWithDefault(\\n                    config.advanced?.enableReviewerWorkflows,\\n                    false\\n                ),\\n                enableDataCollection: booleanWithDefault(\\n                    config.advanced?.enableDataCollection,\\n                    false\\n                ),\\n\\n                codeInstruction: {\\n                    model: config.advanced?.codeInstruction?.model || undefined,\\n                },\\n', line_offset=484, char_offset=17566, length_in_lines=35, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n'),\n", " LineChunkContents(text='\\n                chat: {\\n                    url: config.advanced?.chat?.url || undefined,\\n                    model: config.advanced?.chat?.model || undefined,\\n                    stream: config.advanced?.chat?.stream ?? undefined,\\n                    enableEditableHistory: config.advanced?.chat?.enableEditableHistory,\\n                    useRichTextHistory: config.advanced?.chat?.useRichTextHistory,\\n                    smartPasteUsePrecomputation: config.advanced?.chat?.smartPasteUsePrecomputation,\\n                    modelDisplayNameToId: config.advanced?.chat?.modelDisplayNameToId,\\n                    experimentalFullFilePaste: config.advanced?.chat?.experimentalFullFilePaste,\\n                },\\n\\n                autofix: {\\n                    enabled: booleanWithDefault(config.advanced?.autofix?.enabled, false),\\n                    locationUrl: config.advanced?.autofix?.locationUrl || undefined,\\n                    autofixUrl: config.advanced?.autofix?.autofixUrl || undefined,\\n                },\\n\\n                oauth: {\\n                    clientID: config.advanced?.oauth?.clientID,\\n                    url: config.advanced?.oauth?.url,\\n                },\\n\\n                completions: {\\n                    timeoutMs: config.advanced?.completions?.timeoutMs ?? 800,\\n                    maxWaitMs: config.advanced?.completions?.maxWaitMs ?? 1600,\\n                    addIntelliSenseSuggestions:\\n                        config.advanced?.completions?.addIntelliSenseSuggestions ?? true,\\n                    filterThreshold: config.advanced?.completions?.filter_threshold ?? undefined,\\n                },\\n                openFileManagerV2: {\\n                    enabled: booleanWithDefault(config.advanced?.openFileManager?.useV2, false),\\n                },\\n', line_offset=519, char_offset=18867, length_in_lines=33, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='                nextEdit: {\\n                    enabled: config.advanced?.nextEdit?.enabled,\\n                    backgroundEnabled: booleanWithDefault(\\n                        config.advanced?.nextEdit?.backgroundEnabled,\\n                        true\\n                    ),\\n\\n                    url: config.advanced?.nextEdit?.url,\\n                    locationUrl: config.advanced?.nextEdit?.locationUrl,\\n                    generationUrl: config.advanced?.nextEdit?.generationUrl,\\n                    showInstructionTextbox: booleanWithDefault(\\n                        config.advanced?.nextEdit?.showInstructionTextbox,\\n                        false\\n                    ),\\n                    model: config.advanced?.nextEdit?.model,\\n                    useDebounceMs: config.advanced?.nextEdit?.useDebounceMs,\\n                    useCursorDecorations: booleanWithDefault(\\n                        config.advanced?.nextEdit?.useCursorDecorations,\\n                        false\\n                    ),\\n                    useSmallHover: booleanWithDefault(\\n                        config.advanced?.nextEdit?.useSmallHover,\\n                        true\\n                    ),\\n                    // the defaults for noDiffMode, animateNoDiffMode, showDiffByDefault will instead come from\\n                    // the feature flags for \"ux1\" and \"ux2\"\\n                    noDiffMode: config.advanced?.nextEdit?.noDiffMode, // allow undefined\\n                    animateNoDiffMode: config.advanced?.nextEdit?.animateNoDiffMode, // allow undefined\\n                    showDiffByDefault: config.advanced?.nextEdit?.showDiffByDefault, // allow undefined\\n                    allowDuringDebugging: booleanWithDefault(\\n                        config.advanced?.nextEdit?.allowDuringDebugging,\\n                        false\\n                    ),\\n                    useMockResults: booleanWithDefault(\\n                        config.advanced?.nextEdit?.useMockResults,\\n                        false\\n                    ),\\n', line_offset=552, char_offset=20644, length_in_lines=37, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='                    noDiffModeUseCodeLens: booleanWithDefault(\\n                        config.advanced?.nextEdit?.noDiffModeUseCodeLens,\\n                        false\\n                    ),\\n                    enableBackgroundSuggestions: booleanWithDefault(\\n                        config.nextEdit?.enableBackgroundSuggestions,\\n                        true\\n                    ),\\n                    enableGlobalBackgroundSuggestions: booleanWithDefault(\\n                        config.nextEdit?.enableGlobalBackgroundSuggestions,\\n                        false\\n                    ),\\n                    showAllBackgroundSuggestionLineHighlights: booleanWithDefault(\\n                        ignorePackageJsonDefault(\\n                            config.nextEdit?.showAllBackgroundSuggestionLineHighlights,\\n                            config.inspect(\"showAllBackgroundSuggestionLineHighlights\")\\n                        ),\\n                        // NOTE(arun): Per UX specs, we are hiding line highlights in the new\\n                        // experience, which is well approximated by \"noDiffMode\".\\n                        !config.advanced?.nextEdit?.noDiffMode\\n                    ),\\n                },\\n\\n                recencySignalManager: {\\n                    collectTabSwitchEvents:\\n                        config.advanced?.recencySignalManager?.collectTabSwitchEvents ?? false,\\n                },\\n                preferenceCollection: {\\n                    enable: booleanWithDefault(\\n                        config.advanced?.preferenceCollection?.enable,\\n                        false\\n                    ),\\n                    enableRetrievalDataCollection: booleanWithDefault(\\n                        config.advanced?.preferenceCollection?.enableRetrievalDataCollection,\\n                        false\\n                    ),\\n                    enableRandomizedMode: booleanWithDefault(\\n                        config.advanced?.preferenceCollection?.enableRandomizedMode,\\n                        true\\n                    ),\\n                },\\n                vcs: {\\n                    watcherEnabled: booleanWithDefault(config.advanced?.vcs?.watcherEnabled, false),\\n                },\\n', line_offset=589, char_offset=22652, length_in_lines=44, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='                smartPaste: {\\n                    url: config.advanced?.smartPaste?.url,\\n                    model: config.advanced?.smartPaste?.model,\\n                },\\n                instructions: {\\n                    model: config.advanced?.instructions?.model,\\n                },\\n            },\\n        };\\n    }\\n}\\n\\n// This is needed for settings that are internal and VSCode won\\'t provide\\n// defaults from package.json.\\nfunction booleanWithDefault(value: any, valueDefault: boolean): boolean {\\n    if (value === undefined || value === null) {\\n        return valueDefault;\\n    }\\n    if (typeof value === \"string\") {\\n        return value.toLowerCase() !== \"false\";\\n    }\\n    return !!value;\\n}\\n', line_offset=633, char_offset=24846, length_in_lines=23, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='\\n// Configuration flags in package.json can have a prescribed default value which needs\\n// to be overriden if we want to roll out a new default value.\\n// This function parses `vscode.WorkspaceConfig.inspect` to see if the value was set by\\n// the user or not -- in the latter case, returns undefined so we can use e.g.\\n// booleanWithDefault.\\n// IMPORTANT(arun): The \"default\" setting value users see in the settings page is the\\n// one in package.json, so this approach will cause the settings page to be out of sync\\n// with what the user experiences.\\nfunction ignorePackageJsonDefault<T>(\\n    value: T | undefined,\\n    inpsectResult: ReturnType<vscode.WorkspaceConfiguration[\"inspect\"]> | undefined\\n): T | undefined {\\n    if (\\n        inpsectResult?.globalValue === undefined &&\\n        inpsectResult?.workspaceValue === undefined &&\\n        inpsectResult?.workspaceFolderValue === undefined\\n    ) {\\n        return undefined;\\n    } else {\\n        return value;\\n    }\\n}\\n\\nfunction intWithDefault(value: any, valueDefault: number): number {\\n    if (value === undefined || value === null) {\\n        return valueDefault;\\n    }\\n    if (typeof value === \"string\") {\\n        return parseInt(value);\\n    }\\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-return\\n    return value;\\n}\\n\\nexport type ConfigChanges = {\\n    previousConfig: AugmentConfig;\\n    newConfig: AugmentConfig;\\n};\\n', line_offset=656, char_offset=25544, length_in_lines=39, header='')]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# import SmartChunker\n", "from base.retrieval.chunking.smart_chunking import SmartChunker\n", "\n", "one_input = task_inputs[0]\n", "chunker = SmartChunker(max_chunk_chars=1280)\n", "chunks = chunker.split_chunks(one_input.prompt.current_file.contents, None)\n", "chunks"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_next_edit.gen_prompt_formatter import (\n", "    EditGenFormatterConfig,\n", ")\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "sc2_tokenizer = StarCoder2Tokenizer()\n", "\n", "edit_gen_prompt_formatter = EditGenPromptFormatter(\n", "    sc2_tokenizer,\n", "    config=EditGenFormatterConfig(\n", "        diff_context_lines=9,\n", "        max_prompt_tokens=10200,  # Fixed: changed : to =\n", "        section_budgets={  # Fixed: changed : to =\n", "            \"suffix_tks\": 1200,  # Fixed: added quotes and changed : to =\n", "            \"prefix_tks\": 2800,\n", "            \"diff_tks\": 3700,\n", "            \"filename_tks\": 100,\n", "            \"instruction_tks\": 200,\n", "            \"retrieval_tks\": 2000,\n", "        },\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-2.71875,\n", " -1.828125,\n", " -3.203125,\n", " -3.71875,\n", " -1.703125,\n", " -5.21875,\n", " -5.5625,\n", " -4.6875,\n", " -4.4375,\n", " -2.375,\n", " -4.71875,\n", " -3.5,\n", " -4.625,\n", " -2.90625,\n", " -2.78125,\n", " -3.984375,\n", " -2.984375,\n", " -2.203125,\n", " -1.8828125,\n", " -4.875]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["get_edit_model_scores(\n", "    one_input.prompt.current_file,\n", "    chunks,\n", "    one_input.prompt.recent_changes,\n", "    edit_model,\n", "    edit_gen_prompt_formatter,\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[LineChunkContents(text='    advanced: {\\n        apiToken: string;\\n        completionURL: string;\\n\\n        // The following are internal only\\n        oauth: OAuthConfig;\\n        model: string;\\n        codeInstruction: CodeInstructionConfig;\\n        chat: ChatConfig;\\n        autofix: AutofixConfig;\\n        enableDebugFeatures: boolean;\\n        enableWorkspaceUpload: boolean;\\n        enableReviewerWorkflows: boolean;\\n        completions: {\\n            timeoutMs: number;\\n            maxWaitMs: number;\\n            addIntelliSenseSuggestions: boolean;\\n            filterThreshold?: number;\\n        };\\n        openFileManagerV2: {\\n            enabled: boolean;\\n        };\\n        enableDataCollection: boolean;\\n        nextEditURL?: string;\\n        nextEditLocationURL?: string;\\n        nextEditGenerationURL?: string;\\n        nextEditBackgroundGeneration?: boolean;\\n        nextEdit: NextEditConfig;\\n        recencySignalManager: RecencySignalManagerConfig;\\n        preferenceCollection: PreferenceCollectionConfig;\\n        vcs: {\\n            watcherEnabled: boolean;\\n        };\\n        smartPaste?: SmartPasteConfig;\\n        instructions?: InstructionsConfig;\\n    };\\n}\\n', line_offset=165, char_offset=4354, length_in_lines=37, header='export interface UserConfig {\\n'),\n", " LineChunkContents(text='\\nexport type NextEditConfig = {\\n    enabled?: boolean; // TODO is this deprecated too?\\n    // TODO: Deprecate and replace with augment.enableBackgroundSuggestions.\\n    backgroundEnabled: boolean;\\n\\n    url?: string;\\n    locationUrl?: string;\\n    generationUrl?: string;\\n\\n    model?: string;\\n\\n    showInstructionTextbox: boolean;\\n\\n    /** How long to wait before sending a new request. */\\n    useDebounceMs?: number;\\n\\n    /** When enabled, use cursor decorations instead of bottom decorations. */\\n    useCursorDecorations?: boolean;\\n\\n    useSmallHover?: boolean;\\n\\n    noDiffMode?: boolean;\\n    animateNoDiffMode?: boolean;\\n\\n    allowDuringDebugging?: boolean;\\n\\n    /** Use mock results if $filename.next-edit-results.json5 exists. */\\n    useMockResults?: boolean;\\n\\n    noDiffModeUseCodeLens?: boolean;\\n\\n    showDiffByDefault?: boolean;\\n\\n    enableBackgroundSuggestions: boolean;\\n\\n    enableGlobalBackgroundSuggestions: boolean;\\n\\n    showAllBackgroundSuggestionLineHighlights: boolean;\\n};\\n\\nexport type PreferenceCollectionConfig = {\\n    enable: boolean;\\n    enableRetrievalDataCollection: boolean;\\n    enableRandomizedMode: boolean;\\n};\\n\\nexport type SmartPasteConfig = {\\n    url?: string;\\n    model?: string;\\n};\\n\\nexport type InstructionsConfig = {\\n    model?: string;\\n};\\n', line_offset=47, char_offset=1210, length_in_lines=55, header=''),\n", " LineChunkContents(text='                smartPaste: {\\n                    url: config.advanced?.smartPaste?.url,\\n                    model: config.advanced?.smartPaste?.model,\\n                },\\n                instructions: {\\n                    model: config.advanced?.instructions?.model,\\n                },\\n            },\\n        };\\n    }\\n}\\n\\n// This is needed for settings that are internal and VSCode won\\'t provide\\n// defaults from package.json.\\nfunction booleanWithDefault(value: any, valueDefault: boolean): boolean {\\n    if (value === undefined || value === null) {\\n        return valueDefault;\\n    }\\n    if (typeof value === \"string\") {\\n        return value.toLowerCase() !== \"false\";\\n    }\\n    return !!value;\\n}\\n', line_offset=633, char_offset=24846, length_in_lines=23, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='                    noDiffModeUseCodeLens: booleanWithDefault(\\n                        config.advanced?.nextEdit?.noDiffModeUseCodeLens,\\n                        false\\n                    ),\\n                    enableBackgroundSuggestions: booleanWithDefault(\\n                        config.nextEdit?.enableBackgroundSuggestions,\\n                        true\\n                    ),\\n                    enableGlobalBackgroundSuggestions: booleanWithDefault(\\n                        config.nextEdit?.enableGlobalBackgroundSuggestions,\\n                        false\\n                    ),\\n                    showAllBackgroundSuggestionLineHighlights: booleanWithDefault(\\n                        ignorePackageJsonDefault(\\n                            config.nextEdit?.showAllBackgroundSuggestionLineHighlights,\\n                            config.inspect(\"showAllBackgroundSuggestionLineHighlights\")\\n                        ),\\n                        // NOTE(arun): Per UX specs, we are hiding line highlights in the new\\n                        // experience, which is well approximated by \"noDiffMode\".\\n                        !config.advanced?.nextEdit?.noDiffMode\\n                    ),\\n                },\\n\\n                recencySignalManager: {\\n                    collectTabSwitchEvents:\\n                        config.advanced?.recencySignalManager?.collectTabSwitchEvents ?? false,\\n                },\\n                preferenceCollection: {\\n                    enable: booleanWithDefault(\\n                        config.advanced?.preferenceCollection?.enable,\\n                        false\\n                    ),\\n                    enableRetrievalDataCollection: booleanWithDefault(\\n                        config.advanced?.preferenceCollection?.enableRetrievalDataCollection,\\n                        false\\n                    ),\\n                    enableRandomizedMode: booleanWithDefault(\\n                        config.advanced?.preferenceCollection?.enableRandomizedMode,\\n                        true\\n                    ),\\n                },\\n                vcs: {\\n                    watcherEnabled: booleanWithDefault(config.advanced?.vcs?.watcherEnabled, false),\\n                },\\n', line_offset=589, char_offset=22652, length_in_lines=44, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='            nextEdit: {\\n                enabled: config.advanced.nextEdit.enabled,\\n                backgroundEnabled: config.advanced.nextEdit.backgroundEnabled,\\n\\n                url: config.advanced.nextEdit.url,\\n                locationUrl: config.advanced.nextEdit.locationUrl || config.advanced.nextEdit.url,\\n                generationUrl:\\n                    config.advanced.nextEdit.generationUrl || config.advanced.nextEdit.url,\\n                showInstructionTextbox: config.advanced.nextEdit.showInstructionTextbox,\\n                model: config.advanced.nextEdit.model,\\n                useDebounceMs: config.advanced.nextEdit.useDebounceMs,\\n                useCursorDecorations: config.advanced.nextEdit.useCursorDecorations,\\n                useSmallHover: config.advanced.nextEdit.useSmallHover,\\n                noDiffMode: config.advanced.nextEdit.noDiffMode,\\n                animateNoDiffMode: config.advanced.nextEdit.animateNoDiffMode,\\n                allowDuringDebugging: config.advanced.nextEdit.allowDuringDebugging,\\n                useMockResults: config.advanced.nextEdit.useMockResults,\\n                noDiffModeUseCodeLens: config.advanced.nextEdit.noDiffModeUseCodeLens,\\n                showDiffByDefault: config.advanced.nextEdit.showDiffByDefault,\\n                enableBackgroundSuggestions: config.nextEdit.enableBackgroundSuggestions,\\n                enableGlobalBackgroundSuggestions:\\n                    config.nextEdit.enableGlobalBackgroundSuggestions,\\n                showAllBackgroundSuggestionLineHighlights:\\n                    config.nextEdit.showAllBackgroundSuggestionLineHighlights,\\n            },\\n', line_offset=309, char_offset=10452, length_in_lines=25, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n'),\n", " LineChunkContents(text='/* eslint-disable @typescript-eslint/no-unsafe-member-access */\\n\\n/* eslint-disable @typescript-eslint/no-unsafe-call */\\n\\n/* eslint-disable @typescript-eslint/no-unsafe-assignment */\\nimport * as vscode from \"vscode\";\\n\\nimport { type AugmentLogger, getLogger } from \"./logging\";\\nimport { MonitoredParameter } from \"./monitored-parameter\";\\nimport { DisposableService } from \"./utils/disposable-service\";\\n\\nexport type CodeInstructionConfig = {\\n    model?: string;\\n};\\n\\nconst DEFAULT_MODEL_DISPLAY_NAME_TO_ID = {\\n    // eslint-disable-next-line @typescript-eslint/naming-convention\\n    Augment: null,\\n};\\nexport type ChatConfig = {\\n    url?: string;\\n    model?: string;\\n    modelDisplayNameToId?: {\\n        [key: string]: string | null;\\n    };\\n    stream?: boolean;\\n    useRichTextHistory?: boolean;\\n    smartPasteUsePrecomputation?: boolean;\\n    experimentalFullFilePaste?: boolean;\\n    enableEditableHistory?: boolean;\\n    userGuidelines?: string;\\n};\\n\\nexport type AutofixConfig = {\\n    enabled?: boolean;\\n    locationUrl?: string;\\n    autofixUrl?: string;\\n};\\n\\nexport type OAuthConfig = {\\n    clientID?: string;\\n    url?: string;\\n};\\n\\nexport type RecencySignalManagerConfig = {\\n    collectTabSwitchEvents: boolean;\\n};\\n', line_offset=0, char_offset=0, length_in_lines=47, header=''),\n", " LineChunkContents(text='            advanced: {\\n                // These options were moved from top level to advanced, so\\n                // check both places.\\n                apiToken: (config.advanced?.apiToken || config.apiToken || \"\").trim().toUpperCase(),\\n                completionURL: (\\n                    config.advanced?.completionURL ||\\n                    config.completionURL ||\\n                    \"\"\\n                ).trim(),\\n\\n                // Internal settings\\n                enableWorkspaceUpload: booleanWithDefault(\\n                    config.advanced?.enableWorkspaceUpload,\\n                    true\\n                ),\\n\\n                model: config.advanced?.model || \"\",\\n\\n                enableDebugFeatures: booleanWithDefault(\\n                    config.advanced?.enableDebugFeatures,\\n                    false\\n                ),\\n\\n                enableReviewerWorkflows: booleanWithDefault(\\n                    config.advanced?.enableReviewerWorkflows,\\n                    false\\n                ),\\n                enableDataCollection: booleanWithDefault(\\n                    config.advanced?.enableDataCollection,\\n                    false\\n                ),\\n\\n                codeInstruction: {\\n                    model: config.advanced?.codeInstruction?.model || undefined,\\n                },\\n', line_offset=484, char_offset=17566, length_in_lines=35, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n'),\n", " LineChunkContents(text='\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n        return {\\n            completions: {\\n                enableAutomaticCompletions: booleanWithDefault(\\n                    config.enableAutomaticCompletions ??\\n                        config.completions?.enableAutomaticCompletions,\\n                    true\\n                ),\\n                disableCompletionsByLanguage:\\n                    config.disableCompletionsByLanguage ||\\n                    config.completions?.disableCompletionsByLanguage ||\\n                    [],\\n                enableQuickSuggestions: booleanWithDefault(\\n                    config.completions?.enableQuickSuggestions,\\n                    true\\n                ),\\n            },\\n            chat: {\\n                userGuidelines: config.chat?.userGuidelines || \"\",\\n            },\\n            enableShortcutsAboveSelectedText: booleanWithDefault(\\n                config.enableShortcutsAboveSelectedText,\\n                false\\n            ),\\n            shortcutsDisplayDelayMS: intWithDefault(config.shortcutsDisplayDelayMS, 2000),\\n            enableEmptyFileHint: booleanWithDefault(config.enableEmptyFileHint, true),\\n            conflictingCodingAssistantCheck: booleanWithDefault(\\n                config.conflictingCodingAssistantCheck,\\n                true\\n            ),\\n', line_offset=453, char_offset=16212, length_in_lines=31, header='export class AugmentConfigListener extends DisposableService {\\n'),\n", " LineChunkContents(text='                nextEdit: {\\n                    enabled: config.advanced?.nextEdit?.enabled,\\n                    backgroundEnabled: booleanWithDefault(\\n                        config.advanced?.nextEdit?.backgroundEnabled,\\n                        true\\n                    ),\\n\\n                    url: config.advanced?.nextEdit?.url,\\n                    locationUrl: config.advanced?.nextEdit?.locationUrl,\\n                    generationUrl: config.advanced?.nextEdit?.generationUrl,\\n                    showInstructionTextbox: booleanWithDefault(\\n                        config.advanced?.nextEdit?.showInstructionTextbox,\\n                        false\\n                    ),\\n                    model: config.advanced?.nextEdit?.model,\\n                    useDebounceMs: config.advanced?.nextEdit?.useDebounceMs,\\n                    useCursorDecorations: booleanWithDefault(\\n                        config.advanced?.nextEdit?.useCursorDecorations,\\n                        false\\n                    ),\\n                    useSmallHover: booleanWithDefault(\\n                        config.advanced?.nextEdit?.useSmallHover,\\n                        true\\n                    ),\\n                    // the defaults for noDiffMode, animateNoDiffMode, showDiffByDefault will instead come from\\n                    // the feature flags for \"ux1\" and \"ux2\"\\n                    noDiffMode: config.advanced?.nextEdit?.noDiffMode, // allow undefined\\n                    animateNoDiffMode: config.advanced?.nextEdit?.animateNoDiffMode, // allow undefined\\n                    showDiffByDefault: config.advanced?.nextEdit?.showDiffByDefault, // allow undefined\\n                    allowDuringDebugging: booleanWithDefault(\\n                        config.advanced?.nextEdit?.allowDuringDebugging,\\n                        false\\n                    ),\\n                    useMockResults: booleanWithDefault(\\n                        config.advanced?.nextEdit?.useMockResults,\\n                        false\\n                    ),\\n', line_offset=552, char_offset=20644, length_in_lines=37, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='\\n// NOTE: Prefer `enable*` for naming in config.\\nexport type AugmentConfig = {\\n    apiToken: string;\\n    completionURL: string;\\n    codeInstruction: CodeInstructionConfig;\\n    chat: ChatConfig;\\n    autofix: AutofixConfig;\\n    modelName: string;\\n    enableUpload: boolean;\\n    enableShortcutsAboveSelectedText: boolean;\\n    shortcutsDisplayDelayMS: number;\\n    enableEmptyFileHint: boolean;\\n    enableDataCollection: boolean;\\n    enableDebugFeatures: boolean;\\n    enableReviewerWorkflows: boolean;\\n    oauth: OAuthConfig;\\n    completions: {\\n        // User settings\\n        enableAutomaticCompletions: boolean;\\n        disableCompletionsByLanguage: Set<string>;\\n        enableQuickSuggestions: boolean;\\n\\n        // Advanced\\n        timeoutMs: number;\\n        maxWaitMs: number;\\n        addIntelliSenseSuggestions: boolean;\\n        filterThreshold?: number;\\n    };\\n    openFileManagerV2: {\\n        enabled: boolean;\\n    };\\n    nextEdit: NextEditConfig;\\n    recencySignalManager: RecencySignalManagerConfig;\\n    preferenceCollection: PreferenceCollectionConfig;\\n    vcs: {\\n        watcherEnabled: boolean;\\n    };\\n    conflictingCodingAssistantCheck: boolean;\\n    smartPaste: SmartPasteConfig;\\n    instructions: InstructionsConfig;\\n};\\nexport type AugmentConfigKey = keyof AugmentConfig;\\n', line_offset=102, char_offset=2477, length_in_lines=43, header=''),\n", " LineChunkContents(text='\\n    /**\\n     * This method will move old settings to a new key.\\n     *\\n     * Because the APIs for this are not synchronous, you must either\\n     * check the old and new keys when normalizing the config OR you\\n     * must anticipate the new values getting updated shortly after\\n     * initialization.\\n     */\\n    async migrateLegacyConfig() {\\n        const config = vscode.workspace.getConfiguration(\"augment\");\\n        // Setting was moved on 2024-09 (approx. v0.211.0)\\n        await this._moveConfig(\\n            config,\\n            \"enableAutomaticCompletions\",\\n            \"completions.enableAutomaticCompletions\"\\n        );\\n\\n        // Setting was moved on 2024-09 (approx. v0.211.0)\\n        await this._moveConfig(\\n            config,\\n            \"disableCompletionsByLanguage\",\\n            \"completions.disableCompletionsByLanguage\"\\n        );\\n\\n        await this._moveConfig(\\n            config,\\n            \"enableBackgroundSuggestions\",\\n            \"nextEdit.enableBackgroundSuggestions\"\\n        );\\n        await this._moveConfig(\\n            config,\\n            \"enableGlobalBackgroundSuggestions\",\\n            \"nextEdit.enableGlobalBackgroundSuggestions\"\\n        );\\n        await this._moveConfig(\\n            config,\\n            \"showAllBackgroundSuggestionLineHighlights\",\\n            \"nextEdit.showAllBackgroundSuggestionLineHighlights\"\\n        );\\n    }\\n', line_offset=358, char_offset=13076, length_in_lines=41, header='export class AugmentConfigListener extends DisposableService {\\n    private _getUserConfig(): UserConfig {\\n'),\n", " LineChunkContents(text='\\nexport interface UserConfig {\\n    completions: {\\n        enableAutomaticCompletions: boolean;\\n        disableCompletionsByLanguage: Array<string>;\\n        enableQuickSuggestions: boolean;\\n    };\\n\\n    enableShortcutsAboveSelectedText: boolean;\\n    shortcutsDisplayDelayMS: number;\\n    enableEmptyFileHint: boolean;\\n    conflictingCodingAssistantCheck: boolean;\\n    chat: {\\n        userGuidelines?: string;\\n    };\\n    nextEdit: {\\n        enableBackgroundSuggestions: boolean;\\n        enableGlobalBackgroundSuggestions: boolean;\\n        showAllBackgroundSuggestionLineHighlights: boolean;\\n    };\\n', line_offset=145, char_offset=3760, length_in_lines=20, header=''),\n", " LineChunkContents(text='\\n                chat: {\\n                    url: config.advanced?.chat?.url || undefined,\\n                    model: config.advanced?.chat?.model || undefined,\\n                    stream: config.advanced?.chat?.stream ?? undefined,\\n                    enableEditableHistory: config.advanced?.chat?.enableEditableHistory,\\n                    useRichTextHistory: config.advanced?.chat?.useRichTextHistory,\\n                    smartPasteUsePrecomputation: config.advanced?.chat?.smartPasteUsePrecomputation,\\n                    modelDisplayNameToId: config.advanced?.chat?.modelDisplayNameToId,\\n                    experimentalFullFilePaste: config.advanced?.chat?.experimentalFullFilePaste,\\n                },\\n\\n                autofix: {\\n                    enabled: booleanWithDefault(config.advanced?.autofix?.enabled, false),\\n                    locationUrl: config.advanced?.autofix?.locationUrl || undefined,\\n                    autofixUrl: config.advanced?.autofix?.autofixUrl || undefined,\\n                },\\n\\n                oauth: {\\n                    clientID: config.advanced?.oauth?.clientID,\\n                    url: config.advanced?.oauth?.url,\\n                },\\n\\n                completions: {\\n                    timeoutMs: config.advanced?.completions?.timeoutMs ?? 800,\\n                    maxWaitMs: config.advanced?.completions?.maxWaitMs ?? 1600,\\n                    addIntelliSenseSuggestions:\\n                        config.advanced?.completions?.addIntelliSenseSuggestions ?? true,\\n                    filterThreshold: config.advanced?.completions?.filter_threshold ?? undefined,\\n                },\\n                openFileManagerV2: {\\n                    enabled: booleanWithDefault(config.advanced?.openFileManager?.useV2, false),\\n                },\\n', line_offset=519, char_offset=18867, length_in_lines=33, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeUserConfig(config: vscode.WorkspaceConfiguration): UserConfig {\\n            advanced: {\\n'),\n", " LineChunkContents(text='            oauth: {\\n                clientID: config.advanced.oauth.clientID || \"augment-vscode-extension\",\\n                url: config.advanced.oauth.url || \"https://auth.augmentcode.com\",\\n            },\\n            enableUpload: config.advanced.enableWorkspaceUpload,\\n            enableShortcutsAboveSelectedText: config.enableShortcutsAboveSelectedText,\\n            shortcutsDisplayDelayMS: config.shortcutsDisplayDelayMS,\\n            enableEmptyFileHint: config.enableEmptyFileHint,\\n            enableDataCollection: config.advanced.enableDataCollection,\\n            enableDebugFeatures: config.advanced.enableDebugFeatures,\\n            enableReviewerWorkflows: config.advanced.enableReviewerWorkflows,\\n            completions: {\\n                enableAutomaticCompletions: config.completions.enableAutomaticCompletions,\\n                disableCompletionsByLanguage: new Set(\\n                    config.completions.disableCompletionsByLanguage\\n                ),\\n                enableQuickSuggestions: config.completions.enableQuickSuggestions,\\n\\n                timeoutMs: config.advanced.completions.timeoutMs,\\n                maxWaitMs: config.advanced.completions.maxWaitMs,\\n                addIntelliSenseSuggestions: config.advanced.completions.addIntelliSenseSuggestions,\\n                filterThreshold: config.advanced.completions.filterThreshold,\\n            },\\n            openFileManagerV2: {\\n                enabled: config.advanced.openFileManagerV2.enabled,\\n            },\\n', line_offset=283, char_offset=8959, length_in_lines=26, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n'),\n", " LineChunkContents(text=\"\\n    /**\\n     * This method moves a users setting from one key to another.\\n     *\\n     * VSCode has two locations where settings can be kept, at\\n     * the global level and at the workspace level. To move a config\\n     * we need to copy values from both global and workspace level\\n     * to the new key.\\n     *\\n     * If there is an old key AND a new key value, we drop the old\\n     * key and leave the new value as is.\\n     *\\n     * @param config\\n     * @param oldKey\\n     * @param newKey\\n     * @returns\\n     */\\n    private async _moveConfig(\\n        config: vscode.WorkspaceConfiguration,\\n        oldKey: string,\\n        newKey: string\\n    ) {\\n        const oldValue = config.inspect(oldKey);\\n        if (!oldValue) {\\n            return;\\n        }\\n\\n        const newValue = config.inspect(newKey);\\n        const targets = [\\n            {\\n                target: vscode.ConfigurationTarget.Workspace,\\n                oldValue: oldValue.workspaceValue,\\n                newValue: newValue?.workspaceValue,\\n            },\\n            {\\n                target: vscode.ConfigurationTarget.Global,\\n                oldValue: oldValue.globalValue,\\n                newValue: newValue?.globalValue,\\n            },\\n        ];\\n        for (const t of targets) {\\n            if (t.oldValue === undefined) {\\n                // There is no old value to copy over, so do nothing.\\n                continue;\\n            }\\n            if (t.newValue === undefined) {\\n                // Update config for target if the new config hasn't been\\n                // set to a user value.\\n                await config.update(newKey, t.oldValue, t.target);\\n            }\\n            // Remove the old config value\\n            await config.update(oldKey, undefined, t.target);\\n        }\\n    }\\n\", line_offset=399, char_offset=14446, length_in_lines=54, header='export class AugmentConfigListener extends DisposableService {\\n    async migrateLegacyConfig() {\\n'),\n", " LineChunkContents(text='\\n    // normalizeConfig converts UserConfig to an AugmentConfig.\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n        return {\\n            apiToken: config.advanced.apiToken,\\n            completionURL: config.advanced.completionURL,\\n            modelName: config.advanced.model,\\n            conflictingCodingAssistantCheck: config.conflictingCodingAssistantCheck,\\n            codeInstruction: {\\n                model: config.advanced.codeInstruction.model || undefined,\\n            },\\n            chat: {\\n                url: config.advanced.chat.url || undefined,\\n                model: config.advanced.chat.model || undefined,\\n                stream: config.advanced.chat.stream ?? undefined,\\n                enableEditableHistory: config.advanced.chat.enableEditableHistory ?? false,\\n                useRichTextHistory: config.advanced.chat.useRichTextHistory ?? true,\\n                smartPasteUsePrecomputation:\\n                    config.advanced.chat.smartPasteUsePrecomputation ?? true,\\n                experimentalFullFilePaste: config.advanced.chat.experimentalFullFilePaste ?? false,\\n                modelDisplayNameToId:\\n                    config.advanced.chat.modelDisplayNameToId || DEFAULT_MODEL_DISPLAY_NAME_TO_ID,\\n                userGuidelines: config.chat.userGuidelines || \"\",\\n            },\\n            autofix: {\\n                enabled: config.advanced.autofix.enabled,\\n                locationUrl: config.advanced.autofix.locationUrl || undefined,\\n                autofixUrl: config.advanced.autofix.autofixUrl || undefined,\\n            },\\n', line_offset=254, char_offset=7368, length_in_lines=29, header='export class AugmentConfigListener extends DisposableService {\\n'),\n", " LineChunkContents(text='            recencySignalManager: config.advanced.recencySignalManager,\\n            preferenceCollection: {\\n                enable: config.advanced.preferenceCollection.enable,\\n                enableRetrievalDataCollection:\\n                    config.advanced.preferenceCollection.enableRetrievalDataCollection,\\n                enableRandomizedMode: config.advanced.preferenceCollection.enableRandomizedMode,\\n            },\\n            vcs: {\\n                watcherEnabled: config.advanced.vcs.watcherEnabled,\\n            },\\n            smartPaste: {\\n                url: config.advanced.smartPaste?.url,\\n                model: config.advanced.smartPaste?.model,\\n            },\\n            instructions: {\\n                model: config.advanced.instructions?.model,\\n            },\\n        };\\n    }\\n\\n    private _getUserConfig(): UserConfig {\\n        const config = vscode.workspace.getConfiguration(\"augment\");\\n        return AugmentConfigListener.normalizeUserConfig(config);\\n    }\\n', line_offset=334, char_offset=12092, length_in_lines=24, header='export class AugmentConfigListener extends DisposableService {\\n    public static normalizeConfig(config: UserConfig): AugmentConfig {\\n'),\n", " LineChunkContents(text='\\n// Configuration flags in package.json can have a prescribed default value which needs\\n// to be overriden if we want to roll out a new default value.\\n// This function parses `vscode.WorkspaceConfig.inspect` to see if the value was set by\\n// the user or not -- in the latter case, returns undefined so we can use e.g.\\n// booleanWithDefault.\\n// IMPORTANT(arun): The \"default\" setting value users see in the settings page is the\\n// one in package.json, so this approach will cause the settings page to be out of sync\\n// with what the user experiences.\\nfunction ignorePackageJsonDefault<T>(\\n    value: T | undefined,\\n    inpsectResult: ReturnType<vscode.WorkspaceConfiguration[\"inspect\"]> | undefined\\n): T | undefined {\\n    if (\\n        inpsectResult?.globalValue === undefined &&\\n        inpsectResult?.workspaceValue === undefined &&\\n        inpsectResult?.workspaceFolderValue === undefined\\n    ) {\\n        return undefined;\\n    } else {\\n        return value;\\n    }\\n}\\n\\nfunction intWithDefault(value: any, valueDefault: number): number {\\n    if (value === undefined || value === null) {\\n        return valueDefault;\\n    }\\n    if (typeof value === \"string\") {\\n        return parseInt(value);\\n    }\\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-return\\n    return value;\\n}\\n\\nexport type ConfigChanges = {\\n    previousConfig: AugmentConfig;\\n    newConfig: AugmentConfig;\\n};\\n', line_offset=656, char_offset=25544, length_in_lines=39, header=''),\n", " LineChunkContents(text='\\n/**\\n * AugmentConfigListener is a class that listens for changes to the extension\\n * configuration. It logs configuration changes of interest and notifies listeners\\n * when the config has changed. It is preferrable to listen to configuration changes\\n * here than to use your own `onDidChangeConfiguration` event listener, as this class\\n * will log configuration changes before notifying listeners.\\n */\\nexport class AugmentConfigListener extends DisposableService {\\n    private _config!: AugmentConfig;\\n    private _configChanged = new vscode.EventEmitter<ConfigChanges>();\\n\\n    private _configMonitor: MonitoredParameter<AugmentConfig>;\\n\\n    private readonly _logger: AugmentLogger = getLogger(\"AugmentConfigListener\");\\n\\n    constructor() {\\n        super();\\n\\n        this._configMonitor = new MonitoredParameter<AugmentConfig>(\"Config\", this._logger);\\n\\n        this._refreshConfig();\\n        this.addDisposable(\\n            vscode.workspace.onDidChangeConfiguration(() => {\\n                return this._refreshConfig();\\n            })\\n        );\\n    }\\n\\n    // `config` is the current configuration.\\n    get config(): Readonly<AugmentConfig> {\\n        return this._config;\\n    }\\n\\n    // onDidChange is an event that clients can listen on to be notified of changes\\n    // to the extension configuration.\\n    get onDidChange(): vscode.Event<ConfigChanges> {\\n        return this._configChanged.event;\\n    }\\n', line_offset=202, char_offset=5500, length_in_lines=39, header='export interface UserConfig {\\n'),\n", " LineChunkContents(text='\\n    // _refreshConfig caches the current extension configuration and logs changes of\\n    // interest.\\n    private _refreshConfig() {\\n        const previousConfig = this._config;\\n        this._config = AugmentConfigListener.normalizeConfig(this._getUserConfig());\\n        if (this._configMonitor.update(this._config)) {\\n            this._configChanged.fire({\\n                previousConfig,\\n                newConfig: this._config,\\n            });\\n        }\\n    }\\n', line_offset=241, char_offset=6904, length_in_lines=13, header='export class AugmentConfigListener extends DisposableService {\\n')]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_top_k_chunks(\n", "    current_file: File,\n", "    chunks: list[LineChunkContents],\n", "    recently_edited_files: Sequence[Changed[File]],\n", "    edit_model: StarCoder2_FastForward,\n", "    edit_gen_prompt_formatter: EditGenPromptFormatter,\n", "    top_k: int = 10,\n", ") -> list[LineChunkContents]:\n", "    scores = get_edit_model_scores(\n", "        current_file, chunks, recently_edited_files, edit_model, edit_gen_prompt_formatter\n", "    )\n", "    return [chunk for _, chunk in sorted(zip(scores, chunks), reverse=True)][:top_k]\n", "\n", "get_top_k_chunks(\n", "    one_input.prompt.current_file,\n", "    chunks,\n", "    one_input.prompt.recent_changes,\n", "    edit_model,\n", "    edit_gen_prompt_formatter,\n", "    top_k=32,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
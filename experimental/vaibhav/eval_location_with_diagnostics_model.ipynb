{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# README\n", "\n", "You need to run `experimental/jiayi/next_edit/inspect_next_edit_hindsight.ipynb` first to generate the dataset. Then run `experimental/vaibhav/convert_ground_truth_to_input.py` to convert the dataset to task inputs. Then run `experimental/vaibhav/location_retriever_eval.py` to evaluate the model. Finally, run this notebook to plot the metrics.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.diagnostic_analysis_util import (\n", "    calculate_diagnostic_metrics_at_most_l_swaps,\n", ")\n", "\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/vaibhav/dogfood-v2_2025_02_03_28days_10s_task_inputs_2000.pkl\",\n", "    \"rb\",\n", ") as f:\n", "    from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (\n", "        NextEditHindsightTaskInput,\n", "    )\n", "\n", "    task_inputs: list[NextEditHindsightTaskInput] = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raven_eval_output_path = Path(\"~/tmp/raven_eval_curr_file_2000/next_edit_hindsight_location_NextEditHindsightLocationEvalTask_output.pkl\").expanduser()\n", "with open(raven_eval_output_path, \"rb\") as f:\n", "    output_old = pickle.load(f)\n", "\n", "raven_eval_output_path_new = Path(\"~/tmp/raven_distill_eval_curr_file_2000/next_edit_hindsight_location_NextEditHindsightLocationEvalTask_output.pkl\").expanduser()\n", "\n", "with open(raven_eval_output_path_new, \"rb\") as f:\n", "    output_new = pickle.load(f)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raven_eval_output_path_v6 = Path(\"~/tmp/raven_v6_eval_2000/output.pkl\").expanduser()\n", "\n", "with open(raven_eval_output_path_v6, \"rb\") as f:\n", "    output= pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metrics = calculate_diagnostic_metrics_at_most_l_swaps(\n", "    output, task_inputs, [1, 3, 8, 32], list(range(9))\n", ")\n", "\n", "metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metrics_old = calculate_diagnostic_metrics_at_most_l_swaps(\n", "    output_old, task_inputs, [1, 3, 8, 32], list(range(9))\n", ")\n", "\n", "metrics_new = calculate_diagnostic_metrics_at_most_l_swaps(\n", "    output_new, task_inputs, [1, 3, 8, 32], list(range(9))\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from typing import Dict, Any\n", "\n", "def plot_k0_comparison(\n", "    metrics_old: Dict[str, Dict[int, Dict[int, float]]],\n", "    metrics_new: Dict[str, Dict[int, Dict[int, float]]],\n", "    metrics_third: Dict[str, Dict[int, Dict[int, float]]],\n", "    figsize: tuple = (10, 6),\n", "    save_path: str = \"output/k0_comparison.png\"\n", ") -> None:\n", "    \"\"\"\n", "    Plot k=0 values comparison between old, new, and third metrics for current_file_recall.\n", "    \n", "    Args:\n", "        metrics_old: Dictionary containing old metrics\n", "        metrics_new: Dictionary containing new metrics\n", "        metrics_third: Dictionary containing third model metrics\n", "        figsize: Figure size tuple (width, height)\n", "        save_path: Optional path to save the plot\n", "    \"\"\"\n", "    plt.style.use(\"bmh\")\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    \n", "    # Extract k=0 values for each top_k\n", "    top_ks = sorted(metrics_old[\"current_file_recall\"].keys())\n", "    k0_values_old = [metrics_old[\"current_file_recall\"][k][0] for k in top_ks]\n", "    k0_values_new = [metrics_new[\"current_file_recall\"][k][0] for k in top_ks]\n", "    k0_values_third = [metrics_third[\"current_file_recall\"][k][0] for k in top_ks]\n", "    \n", "    x = np.arange(len(top_ks))\n", "    width = 0.25  # Reduced width to accommodate third bar\n", "    \n", "    # Create bars\n", "    ax.bar(x - width, k0_values_old, width, label='Old Location Model', color='#2E86C1', alpha=0.8)\n", "    ax.bar(x, k0_values_new, width, label='New Location Model', color='#27AE60', alpha=0.8)\n", "    ax.bar(x + width, k0_values_third, width, label='Editor Model', color='#E74C3C', alpha=0.8)\n", "    \n", "    # Customize plot\n", "    ax.set_ylabel('Recall at k=0')\n", "    ax.set_xlabel('Top k')\n", "    ax.set_title('Comparison Location Models for Current File Recall')\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(top_ks)\n", "    ax.legend()\n", "    \n", "    # Add value labels on bars\n", "    def autolabel(rects):\n", "        for rect in rects:\n", "            height = rect.get_height()\n", "            ax.annotate(f'{height:.3f}',\n", "                       xy=(rect.get_x() + rect.get_width() / 2, height),\n", "                       xytext=(0, 3),  # 3 points vertical offset\n", "                       textcoords=\"offset points\",\n", "                       ha='center', va='bottom',\n", "                       rotation=90)\n", "    \n", "    autolabel(ax.containers[0])\n", "    autolabel(ax.containers[1])\n", "    autolabel(ax.containers[2])\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n", "    \n", "    plt.show()\n", "\n", "plot_k0_comparison(metrics_old, metrics_new, metrics, save_path=\"output/k0_comparison_same_chunks.png\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from typing import Dict, Any\n", "\n", "\n", "def plot_recall_metrics(\n", "    metrics: Dict[str, Any], figsize: tuple = (15, 7), save_path: str = None\n", ") -> None:\n", "    \"\"\"\n", "    Create professional plots showing recall metrics with baseline indicators.\n", "    \"\"\"\n", "    plt.style.use(\"bmh\")\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)\n", "\n", "    k_colors = {\n", "        1: \"#2E86C1\",  # Steel Blue\n", "        3: \"#27AE60\",  # <PERSON>\n", "        8: \"#F39C12\",  # Orange\n", "        32: \"#C0392B\",  # Dark Red\n", "    }\n", "\n", "    def plot_recall_data(ax, data, title):\n", "        # Plot baseline (k=0) lines and shading first\n", "        baselines = {k: data[k][0] for k in metrics[\"top_ks\"]}\n", "        y_max = max(max(values.values()) for values in data.values())\n", "\n", "        # Sort baselines for proper shading\n", "        sorted_baselines = sorted(baselines.items(), key=lambda x: x[1])\n", "\n", "        # Add shaded regions between baseline values\n", "        prev_y = 0\n", "        for k, baseline in sorted_baselines:\n", "            ax.axhspan(prev_y, baseline, color=k_colors[k], alpha=0.05, zorder=1)\n", "            prev_y = baseline\n", "\n", "            # Add horizontal baseline lines\n", "            ax.axhline(\n", "                y=baseline,\n", "                color=k_colors[k],\n", "                linestyle=\"--\",\n", "                alpha=0.5,\n", "                zorder=2,\n", "                linewidth=1.5,\n", "            )\n", "\n", "            # Add baseline labels on the right\n", "            ax.text(\n", "                ax.get_xlim()[1] * 1.02,\n", "                baseline,\n", "                f\"k=0: {baseline:.2f}\",\n", "                color=k_colors[k],\n", "                verticalalignment=\"center\",\n", "                fontsize=9,\n", "                fontweight=\"bold\",\n", "            )\n", "\n", "        # Plot the actual data\n", "        for k in metrics[\"top_ks\"]:\n", "            recall_data = data[k]\n", "            x = list(recall_data.keys())\n", "            y = list(recall_data.values())\n", "\n", "            # Plot initial point\n", "            ax.plot(\n", "                [0],\n", "                [recall_data[0]],\n", "                \"o\",\n", "                color=k_colors[k],\n", "                markersize=12,\n", "                label=f\"k={k}\",\n", "                zorder=4,\n", "                markeredgecolor=\"white\",\n", "                markeredgewidth=2,\n", "            )\n", "\n", "            # Plot line and points\n", "            if len(x) > 1:\n", "                ax.plot(\n", "                    x[1:],\n", "                    y[1:],\n", "                    \"-\",\n", "                    color=k_colors[k],\n", "                    linewidth=2.5,\n", "                    alpha=0.8,\n", "                    zorder=3,\n", "                )\n", "                ax.plot(\n", "                    x[1:],\n", "                    y[1:],\n", "                    \"o\",\n", "                    color=k_colors[k],\n", "                    markersize=8,\n", "                    alpha=0.8,\n", "                    zorder=3,\n", "                    markeredgecolor=\"white\",\n", "                    markeredgewidth=1.5,\n", "                )\n", "\n", "            # Mark and annotate the highest point\n", "            max_y = max(y)\n", "            max_x = x[y.index(max_y)]\n", "            ax.plot(\n", "                max_x,\n", "                max_y,\n", "                \"*\",\n", "                color=k_colors[k],\n", "                markersize=15,\n", "                zorder=5,\n", "                markeredgecolor=\"white\",\n", "                markeredgewidth=1.5,\n", "            )\n", "            ax.annotate(\n", "                f\"{max_y:.2f}\",\n", "                xy=(max_x, max_y),\n", "                xytext=(5, 5),\n", "                textcoords=\"offset points\",\n", "                fontsize=9,\n", "                fontweight=\"bold\",\n", "                color=k_colors[k],\n", "                bbox=dict(\n", "                    facecolor=\"white\",\n", "                    edgecolor=k_colors[k],\n", "                    alpha=0.7,\n", "                    pad=2,\n", "                    boxstyle=\"round,pad=0.5\",\n", "                ),\n", "                zorder=6,\n", "            )\n", "\n", "        # Customize appearance\n", "        ax.set_title(title, fontsize=14, fontweight=\"bold\", pad=20)\n", "        ax.set_xlabel(\"Number of Swaps\", fontsize=12)\n", "        ax.set_ylabel(\"Recall\", fontsize=12)\n", "        ax.grid(True, alpha=0.3, linestyle=\"--\", zorder=0)\n", "        ax.spines[\"top\"].set_visible(False)\n", "        ax.spines[\"right\"].set_visible(False)\n", "\n", "        # Set axis limits with padding\n", "        ax.set_ylim(0, y_max * 1.1)\n", "        ax.set_xlim(\n", "            ax.get_xlim()[0], ax.get_xlim()[1] * 1.15\n", "        )  # Extra space for baseline labels\n", "\n", "        # Add legend\n", "        legend = ax.legend(\n", "            bbox_to_anchor=(1.02, 1),\n", "            loc=\"upper left\",\n", "            frameon=True,\n", "            fancybox=True,\n", "            shadow=True,\n", "            fontsize=10,\n", "        )\n", "        legend.get_frame().set_alpha(0.9)\n", "\n", "    # Plot both subplots\n", "    plot_recall_data(ax1, metrics[\"current_file_recall\"], \"Current File Recall\")\n", "    plot_recall_data(ax2, metrics[\"non_current_file_recall\"], \"Non-Current File Recall\")\n", "\n", "    # Add subtle background color\n", "    fig.patch.set_facecolor(\"#f8f9fa\")\n", "    ax1.set_facecolor(\"#ffffff\")\n", "    ax2.set_facecolor(\"#ffffff\")\n", "\n", "    # Adjust layout\n", "    plt.tight_layout(w_pad=4)\n", "\n", "    # Add a super title\n", "    fig.suptitle(\"Recall Metrics Analysis\", fontsize=16, fontweight=\"bold\", y=1.05)\n", "\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches=\"tight\", facecolor=\"white\")\n", "\n", "    plt.show()\n", "\n", "\n", "# Call the function with your metrics\n", "plot_recall_metrics(\n", "    metrics_old, save_path=\"output/recall_metrics_at_most_l_swaps_chunked_no_cursor_overlap.png\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Call the function with your metrics\n", "plot_recall_metrics(\n", "    metrics_new, save_path=\"output/recall_metrics_at_most_l_swaps_chunked_new_no_cursor_overlap.png\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_diagnostic_proportions(\n", "    metrics: Dict[str, Dict[str, float]],\n", "    figsize: tuple = (12, 6),\n", "    title: str = \"Proportion of Diagnostic Coverage\",\n", "    save_path: str = None,\n", ") -> None:\n", "    \"\"\"\n", "    Create a professional bar plot showing the proportion of useful diagnostics.\n", "\n", "    Args:\n", "        metrics: Dictionary containing diagnostic metrics with keys:\n", "                'covered_diagnostic_types' and 'total_diagnostics_types'\n", "        figsize: <PERSON><PERSON> specifying figure dimensions (width, height)\n", "        title: Plot title string\n", "        save_path: Optional path to save the plot\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    if not isinstance(metrics, dict):\n", "        raise TypeError(\"metrics must be a dictionary\")\n", "\n", "    required_keys = {\"covered_diagnostic_types\", \"total_diagnostics_types\"}\n", "    if not all(key in metrics for key in required_keys):\n", "        raise ValueError(f\"metrics must contain keys: {required_keys}\")\n", "\n", "    covered = metrics[\"covered_diagnostic_types\"]\n", "    total = metrics[\"total_diagnostics_types\"]\n", "\n", "    # Validate data consistency\n", "    if not covered.keys() == total.keys():\n", "        raise ValueError(\n", "            \"Diagnostic types must match in covered and total dictionaries\"\n", "        )\n", "\n", "    # Calculate proportions\n", "    proportions = {\n", "        type_: (count / total[type_] if total[type_] > 0 else 0)\n", "        for type_, count in covered.items()\n", "    }\n", "\n", "    # Create figure\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "\n", "    # Plot bars with custom colors\n", "    x = np.arange(len(proportions))\n", "    colors = [\"#2ecc71\", \"#3498db\", \"#e74c3c\"]  # Professional color scheme\n", "    bars = ax.bar(\n", "        x,\n", "        proportions.values(),\n", "        width=0.6,\n", "        edgecolor=\"black\",\n", "        linewidth=1,\n", "        color=colors[: len(proportions)],\n", "    )\n", "\n", "    # Customize appearance\n", "    ax.set_title(title, pad=20, fontsize=14, fontweight=\"bold\")\n", "    ax.set_xlabel(\"Diagnostic Type\", fontsize=12)\n", "    ax.set_ylabel(\"Proportion of Diagnostics\", fontsize=12)\n", "    ax.set_xticks(x)\n", "    ax.set_xticklabels(proportions.keys(), rotation=45, ha=\"right\")\n", "    ax.grid(True, axis=\"y\", alpha=0.3)\n", "    ax.set_ylim(0, 0.15)\n", "\n", "    # Add value labels on bars\n", "    for bar in bars:\n", "        height = bar.get_height()\n", "        ax.text(\n", "            bar.get_x() + bar.get_width() / 2,\n", "            height,\n", "            f\"{height:.1%}\",\n", "            ha=\"center\",\n", "            va=\"bottom\",\n", "        )\n", "\n", "    # Adjust layout\n", "    plt.tight_layout()\n", "\n", "    # Save if path provided\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n", "\n", "    plt.show()\n", "\n", "\n", "plot_diagnostic_proportions(\n", "    metrics, save_path=\"output/diagnostic_proportions_chunked_new.png\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_helpful_messages_data(\n", "    metrics: Dict[str, float],\n", "    figsize: tuple = (12, 6),\n", "    title: str = \"Proportion of Useful Messages by File Category\",\n", "    save_path: str = None,\n", ") -> None:\n", "    \"\"\"\n", "    Create a professional bar plot showing the proportion of helpful messages.\n", "\n", "    Args:\n", "        metrics: Dictionary containing metrics with keys:\n", "                'proportion_helpful_messages',\n", "                'proportion_helpful_messages_current_file',\n", "                'proportion_helpful_messages_non_current_file'\n", "        figsize: <PERSON><PERSON> specifying figure dimensions (width, height)\n", "        title: Plot title string\n", "        save_path: Optional path to save the plot\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    # Input validation\n", "    required_keys = {\n", "        \"proportion_helpful_messages\",\n", "        \"proportion_helpful_messages_current_file\",\n", "        \"proportion_helpful_messages_non_current_file\",\n", "    }\n", "    if not all(key in metrics for key in required_keys):\n", "        raise ValueError(f\"metrics must contain keys: {required_keys}\")\n", "\n", "    # Create figure\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "    plt.style.use(\"bmh\")\n", "\n", "    # Data preparation\n", "    categories = [\"All Files\", \"Current File\", \"Non-Current File\"]\n", "    values = [\n", "        metrics[\"proportion_helpful_messages\"],\n", "        metrics[\"proportion_helpful_messages_current_file\"],\n", "        metrics[\"proportion_helpful_messages_non_current_file\"],\n", "    ]\n", "\n", "    # Custom colors for professional look\n", "    colors = [\"#3498db\", \"#2ecc71\", \"#e74c3c\"]\n", "\n", "    # Plot bars\n", "    bars = ax.bar(\n", "        categories, values, width=0.6, color=colors, edgecolor=\"black\", linewidth=1\n", "    )\n", "\n", "    # Customize appearance\n", "    ax.set_title(title, pad=20, fontsize=14, fontweight=\"bold\")\n", "    ax.set_xlabel(\"File Category\", fontsize=12)\n", "    ax.set_ylabel(\"Proportion of Helpful Messages\", fontsize=12)\n", "\n", "    # Set y-axis limits with some padding\n", "    ax.set_ylim(0, max(values) * 1.1)\n", "\n", "    # Add grid for better readability\n", "    ax.grid(True, axis=\"y\", alpha=0.3)\n", "\n", "    # Rotate labels if needed\n", "    plt.xticks(rotation=0)\n", "\n", "    # Add value labels on top of bars\n", "    for bar in bars:\n", "        height = bar.get_height()\n", "        ax.text(\n", "            bar.get_x() + bar.get_width() / 2,\n", "            height,\n", "            f\"{height:.1%}\",\n", "            ha=\"center\",\n", "            va=\"bottom\",\n", "        )\n", "\n", "    # Add subtle background color\n", "    ax.set_facecolor(\"#f8f9fa\")\n", "\n", "    # Adjust layout\n", "    plt.tight_layout()\n", "\n", "    # Save if path provided\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n", "\n", "    plt.show()\n", "\n", "\n", "# Example usage:\n", "\"\"\"\n", "metrics = {\n", "    'proportion_helpful_messages': 0.75,\n", "    'proportion_helpful_messages_current_file': 0.85,\n", "    'proportion_helpful_messages_non_current_file': 0.65\n", "}\n", "plot_helpful_messages_data(metrics)\n", "\"\"\"\n", "\n", "plot_helpful_messages_data(metrics, save_path=\"output/helpful_messages_data_new.png\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get top useful diagnostic messages\n", "\n", "\n", "def print_top_useful_diagnostic_messages(metrics, k=10):\n", "    sorted_messages = sorted(\n", "        metrics[\"helpful_messages\"].items(), key=lambda x: x[1], reverse=True\n", "    )\n", "    for message, count in sorted_messages[:k]:\n", "        print(f\"{message}: {count}\")\n", "\n", "\n", "print_top_useful_diagnostic_messages(metrics)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print total number of helpful messages\n", "print(f\"Total number of helpful messages: {metrics['total_helpful_messages']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Total number of diagnostics: {metrics['num_samples_with_diagnostic']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
"""Generate PR instructions with Gemini API client."""

import argparse
from dataclasses import dataclass
from pathlib import Path
import pandas as pd
import pickle
import tqdm

from research.llm_apis.llm_client import GeminiVertexClient, TextPrompt
from research.data.spark.pipelines.utils import map_parquet
from experimental.vaibhav.next_edit_descriptions.generate_diffs import (
    get_diffs_from_truncated_tuples,
)
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import get_session


GEMINI_PROMPT_TEMPLATE = """\
Your task: Create a concise, imperative description of the code change below, such that a developer can use the summary to decide whether to review the change in detail.

Guidelines:
- Start with an action verb (Add, Update, Remove, Fix, Refactor, Format, etc.)
- Maximum 10 words in a single line only
- Focus on WHAT is changing, not WHERE in the codebase
- Look out for comments changes and just give a simple description in such a case.
- For formatting changes just say: "Reformat code"
- Summarize the most important details if there is space otherwise focus on the high level change


Code change:
```
{diff}
```
"""


def get_gemini_flash_client():
    """Get the Gemini flash client."""
    return GeminiVertexClient(model_name="gemini-2.0-flash")


def generate_gemini_response(prompt: str, gemini_client: GeminiVertexClient) -> str:
    """Generate a response from Gemini flash."""
    llm_message = [[TextPrompt(text=prompt)]]
    response = gemini_client.generate(messages=llm_message, max_tokens=48)  # type: ignore
    return response[0][0].text  # type: ignore


def generate_gemini_descriptions(diffs: list[str], gemini_client) -> list[str]:
    """Generate descriptions for a list of diffs."""
    descriptions = []
    for diff in tqdm.tqdm(diffs, desc="Generating gemini descriptions"):
        descriptions.append(
            generate_gemini_response(
                GEMINI_PROMPT_TEMPLATE.format(diff=diff), gemini_client
            )
        )
    return descriptions


def get_all_descriptions(
    input_path: str,
    output_path: str,
    spark,
    task_info_location: str | Path | None = None,
):
    """Get all descriptions."""

    def get_descriptions_from_bytes(pickled_results: bytes) -> pd.Series:
        truncated_tuples: list[tuple[str, str, str]] = pickle.loads(pickled_results)
        diffs = get_diffs_from_truncated_tuples(truncated_tuples, num_context_lines=5)
        descriptions = generate_gemini_descriptions(diffs, get_gemini_flash_client())
        description_datums = list(zip(diffs, descriptions))
        return pd.Series(
            {
                "num_examples": len(description_datums),
                "pickled_results": pickle.dumps(description_datums),
            }
        )

    return map_parquet.apply(
        spark,
        get_descriptions_from_bytes,
        input_path,
        output_path,
        input_columns=["pickled_results"],
        task_info_location=task_info_location,
        batch_size=1,
        ignore_error=True,
        row_size_limit_mb=100,
    )


def main():
    """Main function."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_path",
        type=str,
        required=True,
        help="The input path to the parquet files.",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        required=True,
        help="Directory in which to save the parquet files.",
    )
    parser.add_argument(
        "--write_every",
        type=int,
        default=1,
        help="Write every N samples to disk.",
    )
    args = parser.parse_args()

    input_path = Path(args.input_path).expanduser()
    output_path = Path(args.output_path).expanduser()
    output_path.mkdir(parents=True, exist_ok=True)

    with get_session(
        use_gpu=False,
        name=str(output_path),
        max_workers=32,
    ) as spark:
        task_info_location = output_path.with_name(f"{output_path.name}.logs")
        get_all_descriptions(
            map_parquet.to_gcp_path(str(input_path)),
            map_parquet.to_gcp_path(str(output_path)),
            spark,
            task_info_location,
        )


if __name__ == "__main__":
    main()

import tqdm
from research.next_edits.edit_gen_sampler import EditGenProblem
from experimental.vaibhav.next_edit_descriptions.truncate_diffs import (
    simulate_token_truncation,
)

from experimental.vaibhav.next_edit_descriptions.description_heuristics import (
    get_heuristic_descriptions,
)
from base.diff_utils.str_diff import (
    align_spans_to_word_boundaries,
    precise_char_diff,
)
from services.next_edit_host.server.handler import ScoredFileHunk
from base.diff_utils.diff_utils import File, compute_file_diff


def parse_diff_into_hunks(diff: str) -> list[str]:
    all_hunks = []
    current_hunk = []
    first_hunk = False
    file_header = "".join(diff.splitlines(True)[:2])
    for line in diff.splitlines(True)[2:]:
        if line.startswith("@@") and not first_hunk:
            first_hunk = True
            continue
        if line.startswith("@@"):
            if len(current_hunk) > 0:
                all_hunks.append("".join(current_hunk))
                current_hunk = []
            indxs_line = line.split("@@")[1]
            cur_line = f"@@{indxs_line}@@\n"
            current_hunk.append(cur_line)
        else:
            current_hunk.append(line)
    if len(current_hunk) > 0:
        all_hunks.append("".join(current_hunk))

    # Readd the file headers
    all_hunks = [file_header + hunk for hunk in all_hunks]
    return all_hunks


def get_truncated_tuple(problems: list[EditGenProblem]) -> list[tuple[str, str, str]]:
    """
    Get a tuple of (path, original before, reconstructed after) for all the problems that don't have a heuristic description.
    """
    truncated_tuples = []
    for problem in tqdm.tqdm(problems, desc="Getting truncated tuples"):
        selected_code = problem.selected_code
        new_code = problem.output.replacement
        if get_heuristic_descriptions(
            ScoredFileHunk(
                path=str(problem.current_path),
                blob_name="",
                char_start=0,
                char_end=len(selected_code),
                localization_score=0.0,
                editing_score=0.0,
                truncation_char=None,
                existing_code=selected_code,
                suggested_code=new_code,
                diff_spans=align_spans_to_word_boundaries(
                    precise_char_diff(selected_code, new_code).spans
                ),
            )
        ) != (None, None):
            continue
        _, reconstructed_replacement, _ = simulate_token_truncation(
            selected_code, new_code, 96
        )
        # create a new problem
        if get_heuristic_descriptions(
            ScoredFileHunk(
                path=str(problem.current_path),
                blob_name="",
                char_start=0,
                char_end=len(selected_code),
                localization_score=0.0,
                editing_score=0.0,
                truncation_char=None,
                existing_code=selected_code,
                suggested_code=reconstructed_replacement,
                diff_spans=align_spans_to_word_boundaries(
                    precise_char_diff(selected_code, reconstructed_replacement).spans
                ),
            )
        ) != (None, None):
            continue

        truncated_tuples.append(
            (
                problem.current_path,
                problem.current_code,
                problem.prefix + reconstructed_replacement + problem.suffix,
            )
        )
    return truncated_tuples


def get_diffs_from_problems(
    problems: list[EditGenProblem], num_context_lines: int = 3
) -> list[str]:
    diffs = []
    for path, before, after in tqdm.tqdm(
        get_truncated_tuple(problems), desc="Getting diffs"
    ):
        diff = compute_file_diff(
            before_file=File(path=str(path), contents=before),
            after_file=File(path=str(path), contents=after),
            use_smart_header=True,
            num_context_lines=num_context_lines,
        )
        diffs.extend(parse_diff_into_hunks(diff))
    return diffs


def get_diffs_from_truncated_tuples(
    problems: list[tuple[str, str, str]], num_context_lines: int = 3
) -> list[str]:
    diffs = []
    for path, before, after in tqdm.tqdm(problems, desc="Getting diffs"):
        diff = compute_file_diff(
            before_file=File(path=str(path), contents=before),
            after_file=File(path=str(path), contents=after),
            use_smart_header=True,
            num_context_lines=num_context_lines,
        )
        diffs.extend(parse_diff_into_hunks(diff))
    return diffs

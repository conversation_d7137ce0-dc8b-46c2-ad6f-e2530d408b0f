"""A script to figure out the best num_context_lines to use for generating diffs."""

from pathlib import Path
import argparse
import pandas as pd
import tqdm
import pickle
import random
from research.models.fastbackward_models import FastBackwardLLM
from megatron.tokenizer.tokenizer import get_tokenizer
from research.models.meta_model import GenerationOptions

from experimental.vaibhav.next_edit_descriptions.generate_diffs import (
    get_diffs_from_truncated_tuples,
)

from experimental.vaibhav.next_edit_descriptions.generate_next_edit_descriptions import (
    get_gemini_flash_client,
    generate_gemini_response,
    generate_gemini_descriptions,
)

LLAMA_PROMPT_TEMPLATE = """\
<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are a developer working in my codebase.
Your task is to extract relevant information from the code change below and describe it using imperative verb form. The purpose of your description is to help other developers quickly understand key aspects of the change in the context of the codebase.<|eot_id|><|start_header_id|>user<|end_header_id|>

Here is the code change delimited by triple backticks:
```
{diff}
```
Directly start the response with the description and say nothing else. Write no more than 10 words.
Focus on the fields that are changing but don't include where they are.
<|eot_id|><|start_header_id|>assistant<|end_header_id|>
"""

EVAL_PROMPT_TEMPLATE = """\
You are an expert code reviewer. Your task is to evaluate the quality of a description of a code change. You will be given the code change and the description.
Use the following rubric:

Guidelines:
- Start with an action verb (Add, Update, Remove, Fix, Refactor, etc.)
- Maximum 10 words in a single line only
- Summarize the most important details if there is space otherwise focus on the high level change

Give a score of 0 to 3 based on how many of the guidelines the description follows.

Reply with only the score and nothing else.

Code change:
```
{diff}
```

Description:
{description}

Score:
"""

COMPARE_PROMPT_TEMPLATE = """\
You are an expert code reviewer. Your task is to compare two descriptions of the same code change. You will be given the code change and the two descriptions.
Use the following rubric:

Guidelines:
- Start with an action verb (Add, Update, Remove, Fix, Refactor, etc.)
- Maximum 10 words in a single line only
- Summarize the most important details if there is space otherwise focus on the high level change

Give a score of -1 to 1 based on how much better the first description is compared to the second description. A score of 0 means both are equally good. A score of 1 means the first is much better. A score of -1 means the second is much better.

Reply with only the score and nothing else.

Code change:
```
{diff}
```

Description 1:
{description1}

Description 2:
{description2}

Score:
"""


def get_llama_model_and_tokenizer():
    """Get the llama model and tokenizer."""
    tokenizer = get_tokenizer("Llama3InstructTokenizer")
    trained_model = FastBackwardLLM(
        checkpoint_path=Path(
            "/mnt/efs/augment/user/guy/checkpoints/next-edit-descriptions/llama3-8b-instruct-next-edit-descriptions-v1-randomctx-shortdesc"
        ),
        seq_length=4096,
        model_parallel_size=1,
    )
    trained_model.load()
    return trained_model, tokenizer


# Generate output witth our trained model
def generate_llama_output(model, tokenizer, prompt: str, max_tokens=48) -> str:
    """Generate output from a raw prompt."""
    try:
        prompt_tokens = tokenizer.tokenize_unsafe(prompt)
        result = model.raw_generate_tokens(
            prompt_tokens, GenerationOptions(max_generated_tokens=max_tokens)
        ).tokens

        if not result[-1] == tokenizer.eod_id:
            return "Error"

        result = result[: result.index(tokenizer.eod_id)]
        output = tokenizer.detokenize(result)
        return output
    except Exception:
        return "Error"


def generate_llama_descriptions(diffs: list[str], model, tokenizer) -> list[str]:
    """Generate descriptions for a list of diffs."""
    descriptions = []
    for diff in tqdm.tqdm(diffs, desc="Generating llama descriptions"):
        descriptions.append(
            generate_llama_output(
                model, tokenizer, LLAMA_PROMPT_TEMPLATE.format(diff=diff)
            )
        )
    return descriptions


def evaluate_descriptions(
    diffs: list[str], descriptions: list[str], gemini_client
) -> list[int]:
    """Evaluate descriptions."""
    scores = []
    for diff, description in tqdm.tqdm(
        zip(diffs, descriptions), desc="Evaluating descriptions"
    ):
        scores.append(
            int(
                generate_gemini_response(
                    EVAL_PROMPT_TEMPLATE.format(diff=diff, description=description),
                    gemini_client,
                )
            )
        )
    return scores


def compare_descriptions(
    diffs: list[str], descriptions1: list[str], descriptions2: list[str], gemini_client
) -> list[int]:
    """Compare descriptions."""
    scores = []
    for diff, description1, description2 in tqdm.tqdm(
        zip(diffs, descriptions1, descriptions2), desc="Comparing descriptions"
    ):
        scores.append(
            int(
                generate_gemini_response(
                    COMPARE_PROMPT_TEMPLATE.format(
                        diff=diff, description1=description1, description2=description2
                    ),
                    gemini_client,
                )
            )
        )
    return scores


def evaluate_num_context_lines(
    truncated_tuples: list[tuple[str, str, str]],
    num_context_lines: int,
    llama_model,
    tokenizer,
    gemini_client,
) -> pd.DataFrame:
    """Evaluate the number of context lines."""
    diffs = get_diffs_from_truncated_tuples(truncated_tuples, num_context_lines)
    diffs = random.sample(diffs, 1000)
    llama_descriptions = generate_llama_descriptions(diffs, llama_model, tokenizer)
    llama_scores = evaluate_descriptions(diffs, llama_descriptions, gemini_client)

    gemini_descriptions = generate_gemini_descriptions(diffs, gemini_client)
    gemini_scores = evaluate_descriptions(diffs, gemini_descriptions, gemini_client)

    compare_scores = compare_descriptions(
        diffs, gemini_descriptions, llama_descriptions, gemini_client
    )

    # make a pandas df with all this data
    df = pd.DataFrame(
        {
            "diff": diffs,
            "llama_description": llama_descriptions,
            "llama_score": llama_scores,
            "gemini_description": gemini_descriptions,
            "gemini_score": gemini_scores,
            "compare_score": compare_scores,
        }
    )
    return df


def run_eval(
    truncated_tuples: list[tuple[str, str, str]],
    num_context_lines: list[int],
    output_path: Path,
    llama_model,
    tokenizer,
    gemini_client,
):
    """Run eval."""
    output_path.mkdir(parents=True, exist_ok=True)
    for num_context in num_context_lines:
        print(f"Running eval for {num_context} context lines...")
        df = evaluate_num_context_lines(
            truncated_tuples, num_context, llama_model, tokenizer, gemini_client
        )
        df.to_parquet(output_path / f"eval_results_{num_context}.parquet")
        print(
            f"Saved results for {num_context} context lines to {output_path / f'eval_results_{num_context}.parquet'}"
        )
        print(f"Average llama score: {df['llama_score'].mean()}")
        print(f"Average gemini score: {df['gemini_score'].mean()}")
        print(f"Average compare score: {df['compare_score'].mean()}")
        print()
    print("Done!")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_path",
        type=str,
        required=True,
        help="The input path to the truncated tuple parquet files.",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        required=True,
        help="Directory in which to save the eval results.",
    )
    args = parser.parse_args()
    output_path = Path(args.output_path).expanduser()
    output_path.mkdir(parents=True, exist_ok=True)

    if (output_path / "sampled_problems.parquet").exists():
        df = pd.read_parquet(output_path / "sampled_problems.parquet")
        truncated_tuples = list(zip(df["path"], df["before"], df["after"]))
    else:
        # Read the parquet files
        parquet_files = list(Path(args.input_path).glob("*.parquet"))
        all_problems = []

        for parquet_file in tqdm.tqdm(parquet_files, desc="Reading parquet files"):
            df = pd.read_parquet(parquet_file)
            for row in df["pickled_results"]:
                all_problems.extend(pickle.loads(row))

        # Sample 1000 problems
        truncated_tuples = random.sample(all_problems, 1000)
        pd.DataFrame(
            {
                "path": [str(t[0]) for t in truncated_tuples],
                "before": [t[1] for t in truncated_tuples],
                "after": [t[2] for t in truncated_tuples],
            }
        ).to_parquet(output_path / "sampled_problems.parquet")

    # Get the model and tokenizer
    llama_model, tokenizer = get_llama_model_and_tokenizer()
    gemini_client = get_gemini_flash_client()

    # Evaluate the num_context_lines
    num_context_lines = [3, 5, 10, 30, 50]

    run_eval(
        truncated_tuples,
        num_context_lines,
        output_path,
        llama_model,
        tokenizer,
        gemini_client,
    )

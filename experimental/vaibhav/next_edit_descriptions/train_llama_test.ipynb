{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# autoreload\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pickle\n", "import random\n", "import tqdm\n", "from pathlib import Path\n", "\n", "# import llama tokenizer\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.models.fastbackward_models import FastBackwardLLM\n", "\n", "tokenizer = create_tokenizer_by_name(\"llama3_instruct\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load in a parquet file to experiment with diff descriptions examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load data\n", "df = pd.read_parquet(\n", "    \"/mnt/efs/spark-data/shared/next-edit/diffs/descriptionsFinal/part-09418-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet\"\n", ")\n", "data = []\n", "for row in df[\"pickled_results\"]:\n", "    data.extend(pickle.loads(row))\n", "len(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fastbackward.loss_masking import unmask_token_np\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example = random.choice(data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Add the description formatter to process the example"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vaibhav.next_edit_descriptions.get_training_data import (\n", "    Des<PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "formatter = DescriptionFormatter()\n", "tokens = formatter.format_prompt(example[1], example[0], seq_len=1028)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "\n", "tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "\n", "print(tokenizer.detokenize(unmask_token_np(tokens)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check the results of the indexed dataset "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# open a file in write mode\n", "from research.data.dataset import indexed_dataset\n", "\n", "dataset = Path(\n", "    \"/mnt/efs/augment/data/processed/gemini_diff_description/dataFinal/train.idx\"\n", ")\n", "ds = indexed_dataset.make_dataset(\n", "    str(dataset.with_suffix(\"\")), impl=\"mmap\", skip_warmup=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.detokenize(unmask_token_np(ds[0])))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Code to convert to ffw"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.jiayi.next_edit.download_checkpoints import convert_llama\n", "from base.fastforward.llama.model_specs import LlamaModelSpec\n", "from pathlib import Path\n", "\n", "manifest = convert_llama(\n", "    Path(\"/mnt/efs/augment/checkpoints/next-edit-description/llama3b_gemini_distilled\"),\n", "    Path(\n", "        \"/mnt/efs/augment/checkpoints/next-edit-description/llama3b_gemini_distilled_ffw\"\n", "    ),\n", ")\n", "# save_llama_params(None, manifest, Path(\"mnt/efs/augment/checkpoints/next-edit-description/llama3b_gemini_distilled_ffw\"))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def copy_to_gcp_command(checkpoint_name: str):\n", "    target_dir = f\"/mnt/efs/augment/checkpoints/{checkpoint_name}\"\n", "    command = (\n", "        f\"gsutil -m rsync -r gs://gcp-us1-checkpoints/{checkpoint_name} {target_dir}\"\n", "    )\n", "    mkdir_command = f\"mkdir -p {target_dir}\"\n", "    return f\"{mkdir_command} && {command}\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'mkdir -p /mnt/efs/augment/checkpoints/next-edit-description/llama3i-8b-gemini-distilled-ffwd-fp8 && gsutil -m rsync -r gs://gcp-us1-checkpoints/next-edit-description/llama3i-8b-gemini-distilled-ffwd-fp8 /mnt/efs/augment/checkpoints/next-edit-description/llama3i-8b-gemini-distilled-ffwd-fp8'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["copy_to_gcp_command(\"next-edit-description/llama3i-8b-gemini-distilled-ffwd-fp8\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}
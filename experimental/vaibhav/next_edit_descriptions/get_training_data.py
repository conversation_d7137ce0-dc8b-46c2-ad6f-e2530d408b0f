from pathlib import Path
from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput
from base.third_party_clients.token_counter.token_counter import (
    TokenizerBasedTokenCounter,
)
from base.tokenizers.tokenizer import Tokenizer
from research.fastbackward.loss_masking import MASKED_ZERO_TOKEN
import numpy as np
from base.tokenizers import create_tokenizer_by_name
from base.prompt_format_next_edit.description_prompt_formatter import (
    EditDescriptionPromptFormatter,
    EditDescriptionPromptInput,
)
from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name
import argparse
import tqdm
import gc
from research.data.dataset import indexed_dataset
from research.utils.token_array_utils import split_by_ratios
import random
import pandas as pd
import pickle
import tempfile
import subprocess
import logging
from dataclasses import dataclass
from dataclasses_json import dataclass_json


@dataclass
class DescribePromptFormatter(EditDescriptionPromptFormatter):
    """Prompt formatter for edit generation."""

    @dataclass_json
    @dataclass
    class Config:
        max_prompt_tokens: int = 1024
        """The maximum number of tokens to be used by the input prompt."""

        diff_context_lines: int = 5
        """The number of hunk context lines to show in the diff section."""

    tokenizer: Tokenizer
    config: Config

    system_prompt = """\
Your task: Create a concise, imperative description of the code change below, such that a developer can use the summary to decide whether to review the change in detail.

Guidelines:
- Start with an action verb (Add, Update, Remove, Fix, Refactor, Format, etc.)
- Maximum 10 words in a single line only
- Focus on WHAT is changing, not WHERE in the codebase
- Look out for comments changes and just give a simple description in such a case.
- For formatting changes just say: "Reformat code"
- Summarize the most important details if there is space otherwise focus on the high level change
"""
    message = """\
Here is the code change delimited by triple backticks:
```
{diff}
```
ONLY respond with the description.
"""

    def __post_init__(self):
        assert self.config.max_prompt_tokens > 0
        self._token_counter = TokenizerBasedTokenCounter(self.tokenizer)

    def format_input_from_diff_str(self, diff_str: str) -> StructuredChatPromptOutput:
        """Format the input into a structured chat prompt."""
        return StructuredChatPromptOutput(
            system_prompt=self.system_prompt,
            message=self.message.format(diff=diff_str),
            chat_history=[],
            retrieved_chunks_in_prompt=[],
        )

    def format_input(
        self,
        prompt_input: EditDescriptionPromptInput,
    ) -> StructuredChatPromptOutput:
        """Format the input into a structured chat prompt."""
        raise NotImplementedError("Not needed for this prompt formatter.")


class DescriptionFormatter:
    def __init__(self):
        # Taken from: services/deploy/raven_edit_v2_15b_deploy.jsonnet
        tokenizer_name = "llama3_instruct"
        prompt_formatter_config = DescribePromptFormatter.Config()
        chat_prompt_formatter_name = "llama3"

        self.tokenizer = create_tokenizer_by_name(tokenizer_name)
        self.description_prompt_formatter = DescribePromptFormatter(
            self.tokenizer,
            config=prompt_formatter_config,
        )
        # use the system prompt and user prompt to format the input

        self.chat_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(
            chat_prompt_formatter_name,
            self.tokenizer,
        )

    def format_prompt(
        self,
        description: str,
        diff: str,
        seq_len: int,
        mask_out_input_tokens: bool = True,
    ) -> list[int]:
        structured_prompt = (
            self.description_prompt_formatter.format_input_from_diff_str(diff)
        )
        tokenized_prompt = self.chat_prompt_formatter.format_prompt(
            structured_prompt
        ).tokens

        # Swap out the \n\n for \n
        if tokenized_prompt[-1] == 271:
            tokenized_prompt[-1] = 198

        eos = self.tokenizer.special_tokens.eos

        if mask_out_input_tokens:
            tokenized_prompt = -1 * np.array(tokenized_prompt)
            tokenized_prompt[tokenized_prompt == 0] = MASKED_ZERO_TOKEN
            tokenized_prompt = list(tokenized_prompt)

        label_tokens = self.tokenizer.tokenize_unsafe(description) + [eos]

        sample_tokens = tokenized_prompt + label_tokens

        if len(sample_tokens) < seq_len:
            sample_tokens += [-eos] * (seq_len - len(sample_tokens))
        else:
            sample_tokens = sample_tokens[:seq_len]

        assert len(sample_tokens) == seq_len
        return sample_tokens


def load_diff_descriptions(parquet_files: list[Path], garbage_collect_every=128):
    i = 0
    for shard in parquet_files:
        i += 1
        df = pd.read_parquet(shard, columns=["pickled_results"])
        for row in df.to_dict(orient="records"):
            for obj in pickle.loads(row["pickled_results"]):
                diff, description = obj
                yield diff, description
        if i % garbage_collect_every == 0:
            del df
            print(f"Garbage collecting after {i} shards")
            gc.collect()


def save_generated_descriptions_to_indexed_dataset(
    input_path: Path,
    output_path: Path,
    pad_length: int,
):
    result_shards = list(Path(input_path).glob("*.parquet"))
    print(f"Got results for {len(result_shards)} shards.")
    formatter = DescriptionFormatter()

    output_path.mkdir(parents=True, exist_ok=True)
    assert output_path.is_absolute(), f"output_path must be absolute: {output_path=}"

    print(f"Starting create_indexed_dataset: {output_path=}.")

    def _create_indexed_dataset(save_dir: Path, data_name: str, shards: list[Path]):
        builder = indexed_dataset.MMapIndexedDatasetBuilder(
            str(save_dir / f"{data_name}.bin"),
            dtype=np.int32,
        )
        for diff, description in tqdm.tqdm(load_diff_descriptions(shards)):
            tokens = formatter.format_prompt(description, diff, seq_len=pad_length)
            builder.add_item(tokens)
        builder.finalize(str(save_dir / f"{data_name}.idx"))
        print(f"Result saved to: {save_dir}")

    # split the data into batches
    result_splits = split_by_ratios(
        result_shards, {"train": 0.9, "val": 0.1}, random.Random(42)
    )
    for split_name, split_result_shards in result_splits.items():
        Path("/home/<USER>/tmp").mkdir(exist_ok=True, parents=True)
        with tempfile.TemporaryDirectory(
            dir="/home/<USER>/tmp", prefix="save_stage3_results_as_datasets"
        ) as tmp_dir:
            tmp_dir = Path(tmp_dir)
            logging.info(f"Building a local indexed dataset at {tmp_dir}.")
            _create_indexed_dataset(tmp_dir, split_name, split_result_shards)
            # upload all files under tmp_dir to gcloud
            logging.info(f"Uploading {tmp_dir} to gcloud.")
            for file in tmp_dir.glob("*"):
                save_path_gcp = str(output_path / file.name).replace(
                    "/mnt/efs/spark-data", "gs://gcp-us1-spark-data"
                )
                subprocess.run(
                    f"gcloud storage cp {file} {save_path_gcp}",
                    shell=True,
                    check=True,
                )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_path",
        type=str,
        required=True,
        help="The input path to the parquet files.",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        required=True,
        help="Directory in which to save the indexed dataset files.",
    )
    parser.add_argument(
        "--pad_length",
        type=int,
        default=1028,
        help="The length to pad the tokens to.",
    )
    args = parser.parse_args()
    input_path = Path(args.input_path).expanduser()
    output_path = Path(args.output_path).expanduser()
    pad_length = args.pad_length
    save_generated_descriptions_to_indexed_dataset(input_path, output_path, pad_length)

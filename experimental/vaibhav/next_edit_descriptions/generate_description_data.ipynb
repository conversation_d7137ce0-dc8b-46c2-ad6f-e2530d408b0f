{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Generate descriptions for diffs "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# autoreload\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# A function to read all the parquet files under a directory\n", "import pandas as pd\n", "from pathlib import Path\n", "import tqdm\n", "import random\n", "\n", "\n", "def read_parquet_files(directory_path: Path, sample_size: int = 100):\n", "    parquet_files = list(directory_path.glob(\"*.parquet\"))\n", "    dfs = []\n", "    random.shuffle(parquet_files)\n", "    parquet_files = parquet_files[:sample_size]\n", "    for parquet_file in tqdm.tqdm(parquet_files, desc=\"Reading parquet files\"):\n", "        df = pd.read_parquet(parquet_file)\n", "        dfs.append(df)\n", "    return pd.concat(dfs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = read_parquet_files(\n", "    Path(\"/mnt/efs/spark-data/shared/next-edit/stage1/prv2-pr_grouped_10k/S28_16000p\"),\n", "    100,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "all_data = []\n", "for row in tqdm.tqdm(df[\"pickled_results\"]):\n", "    data = pickle.loads(row)\n", "    all_data.extend(data)\n", "\n", "len(all_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = random.sample(all_data, 10000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# save the data in a pandas csv\n", "\n", "# make a list of pickled string from data\n", "pickled_data = [pickle.dumps(row) for row in data]\n", "df = pd.DataFrame(pickled_data, columns=[\"pickled_results\"])\n", "df.to_parquet(\"~/tmp/data.parquet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pickle\n", "\n", "df = pd.read_parquet(\"~/tmp/data.parquet\")\n", "data = []\n", "for row in df[\"pickled_results\"]:\n", "    data.append(pickle.loads(row))\n", "len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vaibhav.next_edit_descriptions.generate_diffs import (\n", "    get_diffs_from_problems,\n", ")\n", "\n", "diffs = get_diffs_from_problems(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Make a plt of the number of lines in the diff\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "num_lines = [len(diff.splitlines()) for diff in diffs]\n", "plt.hist(num_lines, bins=100)\n", "plt.show()\n", "\n", "# sort the diffs by number of lines\n", "diffs.sort(key=lambda x: len(x.splitlines()))\n", "\n", "print(diffs[-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diffs = list(set(diffs))\n", "print(len(diffs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# make a pandas df\n", "df = pd.DataFrame(diffs, columns=[\"diff\"])\n", "df.to_parquet(\"~/tmp/diffs.parquet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read the diffs\n", "import pandas as pd\n", "\n", "df = pd.read_parquet(\"~/tmp/diffs.parquet\")\n", "diffs = df[\"diff\"].tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "diffs = random.sample(diffs, 5000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate descriptions\n", "import pandas as pd\n", "from experimental.vaibhav.next_edit_descriptions.generate_diffs import (\n", "    get_diffs_from_truncated_tuples,\n", ")\n", "\n", "tuple_data = pd.read_parquet(\"~/tmp/diff_metrics/sampled_problems.parquet\")\n", "diffs = get_diffs_from_truncated_tuples(tuple_data.values.tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set up the clients"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vaibhav.next_edit_descriptions.generate_next_edit_descriptions import (\n", "    generate_gemini_response,\n", "    get_gemini_flash_client,\n", ")\n", "from experimental.vaibhav.next_edit_descriptions.hyperparameter_tuning import (\n", "    generate_llama_output,\n", "    get_llama_model_and_tokenizer,\n", ")\n", "\n", "llama_model, tokenizer = get_llama_model_and_tokenizer()\n", "gemini_client = get_gemini_flash_client()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "descriptions = {}\n", "for diff in tqdm.tqdm(diffs):\n", "    if not diff.strip():\n", "        continue\n", "    try:\n", "        descriptions[diff] = (\n", "            generate_gemini_response(diff, gemini_client),\n", "            generate_llama_output(llama_model, tokenizer, diff),\n", "        )\n", "    except Exception as e:\n", "        print(e)\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(descriptions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# make a pd dataframe from descriptions\n", "\n", "df = pd.DataFrame.from_dict(descriptions, orient=\"index\", columns=[\"gemini\", \"llama\"])\n", "df = df.sample(500)\n", "df.reset_index(inplace=True)\n", "df.rename(columns={\"index\": \"diff\"}, inplace=True)\n", "df.to_parquet(\"~/tmp/descriptions.parquet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
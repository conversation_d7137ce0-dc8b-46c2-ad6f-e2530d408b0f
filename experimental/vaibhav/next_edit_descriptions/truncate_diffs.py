"""
Utility to simulate how next edit output is cut off due to token limits.
This script provides functions to:
1. Convert a diff (before, after) into tokens
2. Truncate the tokens to a maximum limit
3. Convert the truncated tokens back into a diff
"""

from typing import Tuple, List, Optional, Sequence
import difflib
import re
from dataclasses import dataclass

# Import the actual utilities from the codebase
from base.prompt_format_next_edit.gen_prompt_formatter import (
    encode_model_diff_input,
    encode_model_diff_output,
    decode_model_diff,
    cleanup_truncated_model_diff,
    DecodedChange,
)
from base.tokenizers import create_tokenizer_by_name
from base.prompt_format.common import TokenList


@dataclass
class TokenizedDiff:
    """Represents a tokenized diff."""

    tokens: TokenList
    original_diff: str
    diff_chunks: List[str]


# We'll use the actual encode_model_diff_output from the codebase instead of this simplified version
def diff_to_tokens(
    before: str,
    after: str,
    tokenizer_name: str = "rogue",
    max_pause_chars: int = 800,
    lang: Optional[str] = None,
) -> TokenizedDiff:
    """
    Convert a diff (before, after) into a list of tokens using the actual tokenizer.
    Args:
        before: The original text
        after: The modified text
        tokenizer_name: Name of the tokenizer to use (default: "rogue")
        max_pause_chars: Maximum characters per chunk before inserting a pause token
        lang: Optional language identifier
    Returns:
        TokenizedDiff object containing tokens and original diff
    """
    # Get the actual diff chunks using the codebase utility
    diff_chunks = encode_model_diff_output(
        before, after, max_pause_chars=max_pause_chars, lang=None
    )
    diff_text = "".join(diff_chunks)
    # Use the actual tokenizer from the codebase
    tokenizer = create_tokenizer_by_name(tokenizer_name)
    tokens = TokenList()
    # Tokenize each chunk and combine them
    for chunk in diff_chunks:
        tokens.extend(tokenizer.tokenize_safe(chunk))
    return TokenizedDiff(
        tokens=tokens, original_diff=diff_text, diff_chunks=diff_chunks
    )


def truncate_tokens(
    tokenized_diff: TokenizedDiff, max_tokens: int, tokenizer_name: str = "rogue"
) -> TokenizedDiff:
    """
    Truncate tokens to a maximum limit.
    Args:
        tokenized_diff: The TokenizedDiff object
        max_tokens: Maximum number of tokens to keep
        tokenizer_name: Name of the tokenizer to use (default: "rogue")
    Returns:
        TokenizedDiff with truncated tokens
    """
    if len(tokenized_diff.tokens) <= max_tokens:
        return tokenized_diff
    # Truncate the tokens
    truncated_tokens = tokenized_diff.tokens[:max_tokens]
    # Get the tokenizer to detokenize
    tokenizer = create_tokenizer_by_name(tokenizer_name)
    truncated_diff = tokenizer.detokenize(truncated_tokens)
    # We don't have the original chunks anymore, so we'll just put it all in one chunk
    return TokenizedDiff(
        tokens=truncated_tokens,
        original_diff=truncated_diff,
        diff_chunks=[truncated_diff],
    )


def tokens_to_diff(tokenized_diff: TokenizedDiff, tokenizer_name: str = "rogue") -> str:
    """
    Convert tokens back into a diff string using the tokenizer's detokenize method.
    Args:
        tokenized_diff: The TokenizedDiff object
        tokenizer_name: Name of the tokenizer to use (default: "rogue")
    Returns:
        Reconstructed diff string
    """
    # Use the actual tokenizer from the codebase
    tokenizer = create_tokenizer_by_name(tokenizer_name)
    return tokenizer.detokenize(tokenized_diff.tokens)


def apply_diff_to_text(text: str, diff: str) -> Optional[str]:
    """
    Apply a diff to a text using the codebase's decode_model_diff function.
    Args:
        text: The original text
        diff: The diff to apply
    Returns:
        The text after applying the diff, or None if the diff couldn't be applied
    """
    try:
        # First, encode the input text in the format expected by decode_model_diff
        input_str = encode_model_diff_input(text)
        # Clean up the diff if it might be truncated
        cleaned_diff = cleanup_truncated_model_diff(diff)
        # Decode the diff to get the modified text
        decoded = decode_model_diff(input_str, cleaned_diff)
        return decoded.after
    except Exception as e:
        print(f"Error applying diff: {e}")
        return None


def simulate_token_truncation(
    before: str,
    after: str,
    max_tokens: int,
    tokenizer_name: str = "starcoder2",
    max_pause_chars: int = 800,
    lang: Optional[str] = None,
) -> Tuple[str, str, DecodedChange]:
    """
    Simulate how next edit output is cut off due to token limits.
    Args:
        before: The original text
        after: The modified text
        max_tokens: Maximum number of tokens to keep
        tokenizer_name: Name of the tokenizer to use (default: "rogue")
        max_pause_chars: Maximum characters per chunk before inserting a pause token
        lang: Optional language identifier
    Returns:
        Tuple of (truncated diff, reconstructed after text, decoded change object)
    """
    # Convert diff to tokens using the actual tokenizer
    tokenized_diff = diff_to_tokens(
        before, after, tokenizer_name, max_pause_chars, lang
    )
    # Print token count information
    # Truncate tokens to the specified limit
    truncated_tokenized_diff = truncate_tokens(
        tokenized_diff, max_tokens, tokenizer_name
    )
    # Convert truncated tokens back to diff
    truncated_diff = tokens_to_diff(truncated_tokenized_diff, tokenizer_name)
    # Clean up the truncated diff
    cleaned_diff = cleanup_truncated_model_diff(truncated_diff)
    # Encode the input for decode_model_diff
    input_str = encode_model_diff_input(before)
    # Decode the diff to get the modified text and other information
    try:
        decoded_change = decode_model_diff(input_str, cleaned_diff)
        reconstructed_after = decoded_change.after
    except Exception as e:
        print(f"Error decoding diff: {e}")
        reconstructed_after = before
        decoded_change = DecodedChange(before, before, 0)
    return truncated_diff, reconstructed_after, decoded_change

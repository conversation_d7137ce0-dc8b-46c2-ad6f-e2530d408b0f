from services.next_edit_host.server.handler import ScoredFileHunk
from base.diff_utils.str_diff import (
    AddedSpan,
    DeletedSpan,
    DiffSpan,
    ModSpan,
    NoopSpan,
    align_spans_to_word_boundaries,
    combine_spans_on_same_line,
    precise_char_diff,
)
from typing import Optional, Sequence
import re

from enum import Enum


class DescriptionStyle(Enum):
    NO_STYLE = 1
    MARKDOWN = 2


class DescriptionHeuristicConfig:
    delete_start: str = "Delete: "
    add_start: str = "Add: "
    mod_start: str = "Change: "
    mod_middle: str = " ➜ "
    whitespace_change_description: str = "Fix whitespace"
    markdown_code_delimiter: str = r"`"
    char_limit: int = 40


heuristic_config = DescriptionHeuristicConfig()


def apply_style(text: str, style: DescriptionStyle) -> str:
    if style is DescriptionStyle.NO_STYLE:
        return text
    if style is DescriptionStyle.MARKDOWN:
        escaped_code = text.replace("`", r"\`")
        return f"{heuristic_config.markdown_code_delimiter}{escaped_code}{heuristic_config.markdown_code_delimiter}"
    else:
        raise ValueError(f"Unknown style: {style}")


def format_heuristic_description(
    diff_spans: Sequence[DiffSpan],
    style: DescriptionStyle,
) -> str | None:
    """
    Format a heuristic description for the given diff spans.

    Args:
        diff_spans (Sequence[DiffSpan]): The diff spans to format the description for.
        style (DescriptionHeuristicConfig.DescriptionStyle): The style to use for the description.

    Returns:
        str | None: The formatted description, or None if no description could be generated.
    """

    # Check for no change
    if all(isinstance(diff_span, NoopSpan) for diff_span in diff_spans):
        return ""

    # Check for whitespace only change
    only_whitespace = True
    for span in diff_spans:
        if isinstance(span, NoopSpan):
            continue
        elif isinstance(span, ModSpan):
            # if the change is different modulo whitespace, then we have a non-whitespace change
            if re.sub(r"\s+", "", span.before) != re.sub(r"\s+", "", span.after):
                only_whitespace = False
                break
        elif span.after.strip() or span.before.strip():
            only_whitespace = False
            break

    if only_whitespace:
        # return the whitespace change description
        return heuristic_config.whitespace_change_description

    combined_spans = combine_spans_on_same_line(diff_spans)

    if len(combined_spans) > 3:
        return None  # for a single line change, we will have at most 3 spans [Noop, Change, Noop]

    # TODO: Get the number of available characters on the line...

    # check for only deletion
    if len(combined_spans) == 1 and isinstance(combined_spans[0], DeletedSpan):
        before_text = combined_spans[0].before

        num_full_lines_deleted = before_text.count("\n")

        if num_full_lines_deleted > 1:
            return f"Delete {num_full_lines_deleted} lines"

        before_text = before_text.strip()

        return "Delete ..."

    # Get rid of the noop spans
    spans_to_describe = list(
        filter(
            lambda x: not isinstance(x, NoopSpan)
            and (x.after.strip() or x.before.strip()),
            combined_spans,
        )
    )

    if len(spans_to_describe) != 1:
        # we should have one and only one span to describe
        return None

    span_to_describe = spans_to_describe[0]

    match span_to_describe:
        case AddedSpan(after):
            lines_to_add = [
                line.strip() for line in after.split("\n") if line.strip() != ""
            ]

            if len(lines_to_add) != 1:
                return None

            return "Add ..."

        case DeletedSpan(before):
            lines_to_delete = [
                line.strip() for line in before.split("\n") if line.strip() != ""
            ]

            if len(lines_to_delete) != 1:
                return None

            return "Delete ..."

        case ModSpan(before, after):
            lines_to_add = [
                line.strip() for line in after.split("\n") if line.strip() != ""
            ]

            lines_to_delete = [
                line.strip() for line in before.split("\n") if line.strip() != ""
            ]

            if len(lines_to_add) != 1 or len(lines_to_delete) != 1:
                return None

            description = f"{heuristic_config.mod_start}{apply_style(lines_to_delete[0], style)}{heuristic_config.mod_middle}{apply_style(lines_to_add[0], style)}"

            if len(description) > heuristic_config.char_limit:
                # we don't truncate a modification -- fall back to the model
                return None

            return description


def get_heuristic_descriptions(
    edit_suggestion: ScoredFileHunk,
) -> tuple[Optional[str], Optional[str]]:
    """
    Generate heuristic descriptions for the given edit suggestion.

    Args:
        edit_suggestion (ScoredFileHunk): The edit suggestion to generate descriptions for.

    Returns:
        tuple[Optional[str], Optional[str]]: A tuple containing two descriptions:
            1. A plain text description without any styling.
            2. A markdown-formatted description.
    """

    # we need diff spans to generate the heuristic description
    if not edit_suggestion.diff_spans:
        edit_suggestion.diff_spans = align_spans_to_word_boundaries(
            precise_char_diff(
                edit_suggestion.existing_code, edit_suggestion.suggested_code
            ).spans
        )

    heuristic_no_style_description = format_heuristic_description(
        edit_suggestion.diff_spans,
        DescriptionStyle.NO_STYLE,
    )

    heuristic_markdown_description = format_heuristic_description(
        edit_suggestion.diff_spans,
        DescriptionStyle.MARKDOWN,
    )

    return heuristic_no_style_description, heuristic_markdown_description

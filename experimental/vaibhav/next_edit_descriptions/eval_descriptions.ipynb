{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate Descriptions generated by Gemini and LLaMA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "diff_descriptions = pd.read_parquet(\"~/tmp/descriptions.parquet\")\n", "\n", "diff_descriptions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vaibhav.next_edit_descriptions.hyperparameter_tuning import (\n", "    evaluate_descriptions,\n", "    get_gemini_flash_client,\n", ")\n", "\n", "gemini_client = get_gemini_flash_client()\n", "\n", "gemini_scores = evaluate_descriptions(\n", "    diff_descriptions[\"diff\"].tolist(),\n", "    diff_descriptions[\"gemini\"].tolist(),\n", "    gemini_client,\n", ")\n", "llama_scores = evaluate_descriptions(\n", "    diff_descriptions[\"diff\"].tolist(),\n", "    diff_descriptions[\"llama\"].tolist(),\n", "    gemini_client,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gemini_scores.count(None), llama_scores.count(None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# plt the distribution of scores\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "plt.hist(gemini_scores, bins=[0, 1, 2, 3, 4], alpha=0.5, label=\"gemini\")\n", "plt.hist(llama_scores, bins=[0, 1, 2, 3, 4], alpha=0.5, label=\"llama\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(diff_descriptions[diff_descriptions[\"gemini_score\"] == 0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print the average score for each model\n", "\n", "print(f\"Gemini average score: {np.mean([x for x in gemini_scores if x is not None])}\")\n", "print(f\"Llama average score: {np.mean([x for x in llama_scores if x is not None])}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grab the rows in diff_descriptions where gemini_score is 1\n", "\n", "for i, row in diff_descriptions[diff_descriptions[\"gemini_score\"] == 0].iterrows():\n", "    print(row[\"diff\"])\n", "    print(row[\"gemini\"])\n", "    print(row[\"gemini_score\"])\n", "    print(row[\"llama\"])\n", "    print(row[\"llama_score\"])\n", "    print()\n", "    print(\"=\" * 120)\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.vaibhav.next_edit_descriptions.hyperparameter_tuning import (\n", "    compare_descriptions,\n", ")\n", "\n", "comparison_scores = compare_descriptions(\n", "    diff_descriptions[\"diff\"].tolist(),\n", "    diff_descriptions[\"gemini\"].tolist(),\n", "    diff_descriptions[\"llama\"].tolist(),\n", "    gemini_client,\n", ")\n", "\n", "diff_descriptions[\"comparison_score\"] = comparison_scores\n", "\n", "diff_descriptions.to_parquet(\"~/tmp/descriptions_with_scores.parquet\")\n", "\n", "# plt the distribution of scores\n", "plt.hist(comparison_scores, bins=3, alpha=0.5, label=\"score\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mean of comparison scores\n", "print(\n", "    f\"Mean comparison score: {np.mean([x for x in comparison_scores if x is not None])}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# how many %age were -1, 0, 1\n", "print(\n", "    f\"%age -1: {100 * sum(1 for x in comparison_scores if x == -1) / len(comparison_scores)}\"\n", ")\n", "print(\n", "    f\"%age 0: {100 * sum(1 for x in comparison_scores if x == 0) / len(comparison_scores)}\"\n", ")\n", "print(\n", "    f\"%age 1: {100 * sum(1 for x in comparison_scores if x == 1) / len(comparison_scores)}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Look at the examples with comparison score of 0\n", "\n", "for i, row in diff_descriptions[diff_descriptions[\"comparison_score\"] == -1].iterrows():\n", "    print(row[\"diff\"])\n", "    print(row[\"gemini\"])\n", "    print(row[\"llama\"])\n", "    print()\n", "    print(\"=\" * 120)\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
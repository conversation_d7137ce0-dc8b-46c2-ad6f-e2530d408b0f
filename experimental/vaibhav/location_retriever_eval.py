import argparse

from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (
    NextEditHindsightLocationEvalTask,
)
import yaml
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
)
from research.core.constants import AUGMENT_ROOT
from pathlib import Path

from datetime import datetime
import pytz

pacific_tz = pytz.timezone("America/Los_Angeles")
current_time = datetime.now(pacific_tz)


def init_raven_location_system() -> BasicNextEditLocationSystem:
    # Load the raven config
    config_path = (
        AUGMENT_ROOT / "research/model_server/configs/next_edit_location_raven.yaml"
    )
    with config_path.open(encoding="utf8") as f:
        config = yaml.safe_load(f)

    config["retriever"]["scorer"]["checkpoint_path"] = "/mnt/efs/augment/checkpoints/vzhao/next_edit_location/raven_location_distill_2560k_gbs512_lr2e-05_wd1e-01_gc1e+00_temp5e-02_1e+03"

    # Initialize the system using the factory method
    system = BasicNextEditLocationSystem.from_yaml_config(config)

    # Load the model/embeddings
    system.load()

    return system


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_path",
        type=Path,
        required=True,
        help="Path to input _task_inputs file.",
    )
    parser.add_argument(
        "--output_path", type=Path, required=True, help="Path to output directory."
    )
    args = parser.parse_args()

    # Convert the input dataset to task inputs
    hindsight_eval_task = NextEditHindsightLocationEvalTask(
        datset_path=args.input_path,
        top_ks=(1, 3, 8, 32),
        drop_instructions=True,
    )

    # Initialize the Raven system
    raven_system = init_raven_location_system()
    hindsight_eval_task.run(
        raven_system, Path(args.output_path).expanduser(), run_with_diagnostics=True
    )

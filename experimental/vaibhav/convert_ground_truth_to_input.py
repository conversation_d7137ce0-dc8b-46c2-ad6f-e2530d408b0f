"""Convert ground truth to input for next edit location eval task."""

import argparse

from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (
    convert_dataset_with_ground_truths_to_task_inputs,
)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_path", type=str, required=True)
    parser.add_argument("--output_path", type=str, required=True)
    parser.add_argument("--limit_examples", type=int, default=5000)
    args = parser.parse_args()

    convert_dataset_with_ground_truths_to_task_inputs(
        input_path=args.input_path,
        output_path=args.output_path + f"_{args.limit_examples}.pkl",
        limit_examples=args.limit_examples,
    )

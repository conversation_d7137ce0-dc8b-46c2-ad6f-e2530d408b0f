# Copied from <PERSON><PERSON>'s repo.
# This is currently a local non-hermetic repo. Will not compile outside <PERSON><PERSON>'s VM.

load("@rules_cuda//cuda:defs.bzl", "cuda_library")
load("@pybind11_bazel//:build_defs.bzl", "pybind_extension")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:python.bzl", "py_library")

PYBIND_FEATURES = [
    "-use_header_modules",  # Required for pybind11.
    "-parse_headers",
]

#cuda_library(
#    name = "matmul_kernel",
#    srcs = [
#        "matmul_kernel.cu",
#    ],
#    copts = [
#        "-Xcompiler=-Wno-conversion",
#        "-Xcompiler=-Wno-format",
#        "-Xcompiler=-Wno-abi",
#        "-Xcompiler=-fno-strict-aliasing",
#        "-Xcompiler=-Wfatal-errors",
#        "-lineinfo",
#        "-I/usr/local/cuda/include",
#        "-I/home/<USER>/src/cutlass-3.4/include",
#        "-I/home/<USER>/src/cutlass-3.4/tools/util/include",
#    ],
#)

#pybind_extension(
#    name = "matmul_api",
#    srcs = [
#        "matmul_api.cc",
#    ],
#    copts = [
#        "--std=c++17",
#        "-O3",
#    ],
#    linkopts = [
#        "-Wl",
#        "-Bsymbolic",
#        "-lcuda",
#    ],
#    deps = [
#        ":matmul_kernel",
#    ],
#)

# py_library(
#    name = "matmul_lib",
#    srcs = ["matmul_lib.py"],
#    bandit = False,  # requires compiling
#    data = [
#        ":matmul_api.so",
#        "@pybind11",
#    ],
#    pyright = False,  # requires compiling
#    ruff = False,  # requires compiling
#    visibility = ["//visibility:public"],
#    deps = [
#        requirement("torch"),
#        requirement("numpy"),
#    ],
#)

#pytest_test(
#    name = "matmul_lib_test",
#    srcs = [
#        "matmul_lib_test.py",
#    ],
#    tags = [
#        "gpu",
#        "manual",
#    ],
#    deps = [
#        ":matmul_lib",
#        "//base/fastforward:torch_utils",
#        requirement("pybind11"),
#        requirement("torch"),
#    ],
#)

#sh_binary(
#    name = "install",
#    srcs = ["install.sh"],
#    data = [":matmul_api.so"],
#    visibility = ["//visibility:public"],
#)

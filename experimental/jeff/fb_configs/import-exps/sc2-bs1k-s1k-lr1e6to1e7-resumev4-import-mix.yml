#
# python research/fastbackward/determined/launch.py experimental/jeff/fb_configs/import-exps/sc2-bs1k-s1k-lr1e6to1e7-resumev4-import-mix.yml
#
# Global batch size is 64 * 8 * 4 / 2 = 1024.
#
# Take about 12 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/85457
# bash research/utils/download_checkpoint.sh 116326f8-2312-4995-b792-b6ecab9a6f29 jeff/eldenv4-plus-imports
#

determined:
  description: null
  workspace: Dev
  project: jeff
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 8
  max_iters: 1000
  warmup_iters: 0
  lr_decay_iters: 1000
  block_size: 7936
  min_lr: 1.0e-7
  learning_rate: 1.0e-6
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/import-mix-dataset-elden-sc2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/import-mix-dataset-elden-sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2/
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: SC2-15B-IMPORT-MIX-BS1K-S1K-LR1e6to1e7-ResumeV4
  wandb_project: jeff-rag

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = k8s_session(max_workers=100)\n", "df = spark.read.parquet(\"/mnt/efs/spark-data/shared/repo/2024-0619_90k_repartitioned\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.rdd.getNumPartitions()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_repos = 900\n", "# smaller repos per partition may be faster (e.g. for testing),\n", "# but might be less similar to the original dataset?\n", "repos_per_partition = 10"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.coalesce(1).limit(num_repos).repartition(num_repos // repos_per_partition)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read the 90k repo dataset and produce a smaller dataset with, e.g. 900 repos.\n", "df.write.parquet(\n", "    f\"/mnt/efs/spark-data/user/jeff/repo/2024-0619_90k_repartitioned-{num_repos}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = \"/mnt/efs/spark-data/user/jeff/repo/2024-0619_90k_repartitioned-90\""]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
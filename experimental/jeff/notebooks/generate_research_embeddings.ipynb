{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import research.eval.harness.factories as factories\n", "from research.eval.harness.systems.all_systems import RAGSystem\n", "from research.eval.harness.tasks.cceval import CCEval\n", "from research.core.model_input import ModelInput\n", "import yaml\n", "from typing import cast\n", "from research.core.artifacts import (\n", "    artifacts_enabled,\n", "    post_artifact,\n", "    collect_artifacts,\n", "    _ArtifactsCollector,\n", ")\n", "from research.core.types import Document\n", "from pathlib import Path\n", "import json\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# NOTE: to run this file, the following needs to be in dense_scorer.add_doc\n", "# namely, chunk_tokens and chunk_embeddings.\n", "\"\"\"\n", "if artifacts_enabled():\n", "    post_artifact(\n", "        {\n", "            \"doc\": doc.path,\n", "            \"chunks\": [\n", "                self.embedding_model.detokenize(chunk) for chunk in chunk_toks\n", "            ],\n", "            \"chunk_tokens\": chunk_toks,\n", "            \"chunk_embeddings\": emb_arr.tolist(),\n", "        }\n", "    )\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["with open(\"../configs/cceval-rogue16b.yml\") as f:\n", "    config = yaml.safe_load(f)\n", "config[\"system\"][\"model\"] = {\"name\": \"null\"}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'null'}\n"]}], "source": ["system = factories.create_system(config[\"system\"])\n", "system = cast(RAGSystem, system)\n", "task = CCEval.from_yaml_config(config[\"task\"])\n", "task = cast(<PERSON><PERSON><PERSON>, task)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get documents from CCEval"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Not sure if we want to use cceval in our unit tests (e.g. data leakage), so leaving this commented."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# all_patches_by_repo, all_documents_by_repo = task.prepare_evaluation_data(task.dataset_path)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# repo = \"stevencrader/transcriptator\"\n", "# docs = all_documents_by_repo[repo]\n", "# docs = [doc for doc in docs if doc.id in [\"26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61\"]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get documents from augment codebase"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["PosixPath('/home/<USER>/augment')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get augment_dir, robust to where the notebook is.\n", "import research.core.types\n", "augment_dir = Path(research.core.types.__file__).parent.parent.parent\n", "augment_dir"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_model_v0.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_batching.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_model.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_handler_test.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/conftest.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_handler.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_model_test.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_batching_test.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_service.py'),\n", " PosixPath('/home/<USER>/augment/services/embedder_host/server/embedder_model_v0_test.py')]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["paths = list(augment_dir.glob(\"services/embedder_host/server/*.py\"))\n", "paths"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["['services/embedder_host/server/embedder_model_v0.py',\n", " 'services/embedder_host/server/embedder_batching.py',\n", " 'services/embedder_host/server/embedder_model.py',\n", " 'services/embedder_host/server/embedder_handler_test.py',\n", " 'services/embedder_host/server/conftest.py',\n", " 'services/embedder_host/server/embedder_handler.py',\n", " 'services/embedder_host/server/embedder_model_test.py',\n", " 'services/embedder_host/server/embedder_batching_test.py',\n", " 'services/embedder_host/server/embedder_service.py',\n", " 'services/embedder_host/server/embedder_model_v0_test.py']"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["docs = [Document.new(path.read_text(), str(path.relative_to(augment_dir))) for path in paths]\n", "[doc.path for doc in docs]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.load()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.basic_RAG_system:Adding 10 documents.\n", "INFO:research.retrieval.retrieval_database:Starting document embedding.\n", "/home/<USER>/augment/research/gpt-neox/megatron/mpu/data.py:51: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)\n", "  sizes_cuda = torch.cuda.LongTensor(sizes)\n", "INFO:research.retrieval.retrieval_database:Finished document embedding in 1.9286112785339355.\n", "INFO:research.eval.harness.systems.basic_RAG_system:There are now 10 total docs.\n"]}], "source": ["# add documents and collect chunk_tokens and embeddings into artifacts\n", "with collect_artifacts() as collector_manager:\n", "    system.clear_retriever()\n", "    system.add_docs(docs)\n", "    artifacts = collector_manager.get_artifacts()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["(50306, '<|ret-endofkey|>')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# This is awful, but the research embedder adds this tokens deep in the bowels of GPT NeoX\n", "# https://github.com/augmentcode/augment/blob/c5912a36c4b085bb3728638a80c38328873769a1/research/gpt-neox/megatron/model/contrastive.py#L204\n", "ret_endofkey_id = system.retriever.scorer.document_formatter.tokenizer.ret_endofkey_id\n", "ret_endofkey_token = system.retriever.scorer.document_formatter.tokenizer.detokenize(ret_endofkey_id)\n", "ret_endofkey_id, ret_endofkey_token"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["(99,\n", " dict_keys(['path', 'chunk_index', 'chunk_text', 'chunk_tokens']),\n", " 'services/embedder_host/server/embedder_model_v0.py',\n", " 0,\n", " '<|startofsequence|>services/embedder_host/server/embedder_model_v0.py<|fim-mid|>\"\"\"Embedder service ',\n", " '_round_size  # pylint: disable=protected-access\\n\\n_NUM_ATTENTION_CACHES_DEFAULT = 10\\n<|ret-endofkey|>',\n", " 305,\n", " 1024)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["test_data = []\n", "test_embeddings = []\n", "for artifact in artifacts:\n", "    doc_path = artifact[\"doc\"]\n", "    for index, (chunk, chunk_tokens, chunk_embeddings) in enumerate(zip(artifact[\"chunks\"], artifact[\"chunk_tokens\"], artifact[\"chunk_embeddings\"])):\n", "        chunk_tokens = chunk_tokens + [ret_endofkey_id]\n", "        chunk = chunk + ret_endofkey_token\n", "        test_data.append({\n", "            \"path\": doc_path,\n", "            \"chunk_index\": index,\n", "            \"chunk_text\": chunk,\n", "            \"chunk_tokens\": chunk_tokens,\n", "        })\n", "        test_embeddings.append(chunk_embeddings)\n", "len(test_data), test_data[0].keys(), test_data[0][\"path\"], test_data[0][\"chunk_index\"], test_data[0][\"chunk_text\"][:100], test_data[0][\"chunk_text\"][-100:], len(test_data[0][\"chunk_tokens\"]), len(test_embeddings[0])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["with open(augment_dir / \"services/embedder_host/server/ethanol6-04.1_test_data.jsonl\", \"w\", encoding=\"utf-8\") as f:\n", "    for entry in test_data:\n", "        json.dump(entry, f)\n", "        f.write(\"\\n\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["with open(augment_dir / \"services/embedder_host/server/ethanol6-04.1_test_embeddings.npy\", \"wb\") as f:\n", "    np.save(f, np.array(test_embeddings, dtype=np.float16))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
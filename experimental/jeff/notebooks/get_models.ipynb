{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.replay_utils import get_augment_client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# client = get_augment_client(url=\"https://dogfood.api.augmentcode.com\")\n", "client = get_augment_client(url=\"https://staging-shard-0.api.augmentcode.com\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client.get_models().models"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
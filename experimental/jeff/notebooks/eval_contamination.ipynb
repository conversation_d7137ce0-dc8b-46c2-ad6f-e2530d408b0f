{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TLDR: CCE<PERSON> does not show up in our trianing dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from research.data.spark import k8s_session\n", "\n", "from research.eval.harness.tasks.cceval import _prepare_dataset, CCEval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# spark = k8s_session(max_workers=128)\n", "# data = spark.read.parquet(\"/mnt/efs/spark-data/shared/repo/2024-0619_90k_repartitioned/*zstd.parquet\")\n", "# repo_names = [x[\"max_stars_repo_name\"] for x in data.select(\"max_stars_repo_name\").collect()]\n", "# with open(\"/mnt/efs/augment/user/jeff/stack/2024-0619_90k_repartitioned_repo_names.json\", \"w\") as f:\n", "#     json.dump(repo_names, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\n", "    \"/mnt/efs/augment/user/jeff/stack/2024-0619_90k_repartitioned_repo_names.json\"\n", ") as f:\n", "    repo_names = json.load(f)\n", "stack_set = set(repo_names)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_patches_by_repo, all_documents_by_repo = _prepare_dataset(CCEval.DEFAULT_DATASET_PATH)\n", "cceval_set = set(all_documents_by_repo.keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(stack_set & cceval_set)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lower_cceval_set = set([x.lower() for x in cceval_set])\n", "lower_stack_set = set([x.lower() for x in stack_set])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(lower_cceval_set & lower_stack_set)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
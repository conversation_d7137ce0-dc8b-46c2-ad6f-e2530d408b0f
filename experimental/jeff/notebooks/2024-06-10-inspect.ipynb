{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "import json\n", "from collections import Counter\n", "from random import Random\n", "from datetime import datetime\n", "\n", "import zstandard as zstd\n", "from base.datasets.hindsight_completion_dataset import HindsightCompletionDataset\n", "from research.eval.harness.metrics import compute_edit_similarity, compute_exact_match\n", "from research.core.utils_for_str import get_first_n_lines\n", "\n", "import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def longest_common_prefix(a: str, b: str):\n", "    for i, (x, y) in enumerate(zip(a, b)):\n", "        if x != y:\n", "            return a[:i]\n", "    return a[:min(len(a), len(b))]\n", "\n", "def compute_exact_match_at_lines(target: str, predictions: list[str], lines: int = 1) -> float:\n", "    target_1_line = get_first_n_lines(target, n=1)\n", "    scores: list[float] = []\n", "    for pred in predictions:\n", "        pred_1_line = get_first_n_lines(pred, n=1)\n", "        scores.append(float(pred_1_line == target_1_line))\n", "    return max(scores)\n", "\n", "def compute_prefix_recall(target: str, predictions: list[str]) -> float:\n", "    scores: list[float] = []\n", "    for pred in predictions:\n", "        scores.append((len(longest_common_prefix(target, pred)) + 1) / (len(target) + 1))\n", "    return max(scores)\n", "\n", "def compute_prefix_precision(target: str, predictions: list[str]) -> float:\n", "    scores: list[float] = []\n", "    for pred in predictions:\n", "        scores.append((len(longest_common_prefix(target, pred)) + 1) / (len(pred) + 1))\n", "    return max(scores)\n", "\n", "def compute_prefix_f1(target: str, predictions: list[str]) -> float:\n", "    scores: list[float] = []\n", "    for pred in predictions:\n", "        scores.append(2 * (len(longest_common_prefix(target, pred)) + 1) / (len(target) + len(pred) + 2))\n", "    return max(scores)\n", "\n", "def whitespace_norm(code: str):\n", "    lines = [line.strip() for line in code.splitlines() if line.strip()]\n", "    return \"\\n\".join(lines)\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def compute_metrics(datum):\n", "    return {\n", "        \"prefix_recall\": compute_prefix_recall(\n", "            a := datum[\"ground_truth\"], [b := datum[\"generation\"]]\n", "        ),\n", "        \"prefix_precision\": compute_prefix_precision(a, [b]),\n", "        \"prefix_f1\": compute_prefix_f1(a, [b]),\n", "        # \"whitespace_norm_prefix_recall\": compute_prefix_recall(c := whitespace_norm(a), [d := whitespace_norm(b)]),\n", "        # \"whitespace_norm_prefix_precision\": compute_prefix_precision(c, [d]),\n", "        # \"whitespace_norm_prefix_f1\": compute_prefix_f1(c, [d]),\n", "        \"==@1_line\": compute_exact_match_at_lines(a, [b], 1),\n", "        \"==\": float(a == b),\n", "        \"exact_match\": compute_exact_match(a, [b]),\n", "        \"edit_similarity\": compute_edit_similarity(a, [b]),\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Metrics"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["runs = {\n", "    \"rogue_v3\": \"/mnt/efs/augment/eval/jobs/gTz8RrN8/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\",\n", "    \"rogue_v2\": \"/mnt/efs/augment/eval/jobs/4DzJvStx/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\",\n", "    \"rogue_v3_rec\": \"/mnt/efs/augment/eval/jobs/PSV3C94d/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\",\n", "    \"rogue_v2_rec\": \"/mnt/efs/augment/eval/jobs/X5MfsuEC/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\",\n", "    \"star2sl_rec\": \"/mnt/efs/augment/eval/jobs/i86AoXSY/000_null_HindsightCompletionTask_completed_patches.jsonl.zst\",\n", "    \"rogue_v2_research\": \"/mnt/efs/augment/eval/jobs/kZnqBJvp/000_rogue_statelesscache_HindsightCompletionTask_completed_patches.jsonl.zst\",\n", "    \"rogue_v2_research_longcon\": \"/mnt/efs/augment/eval/jobs/HPhUaz4R/000_rogue_statelesscache_HindsightCompletionTask_completed_patches.jsonl.zst\",\n", "}"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["runs = {key: read_jsonl_zst(path) for key, path in runs.items()}"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["data = read_jsonl_zst(\"/mnt/efs/augment/data/eval/hindsight/2024-04-25-v0.7/dogfood/data.jsonl.zst\")"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model</th>\n", "      <th>response_model</th>\n", "      <th>prefix_recall</th>\n", "      <th>prefix_precision</th>\n", "      <th>prefix_f1</th>\n", "      <th>==@1_line</th>\n", "      <th>==</th>\n", "      <th>exact_match</th>\n", "      <th>edit_similarity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>rogue_v2</td>\n", "      <td></td>\n", "      <td>0.575601</td>\n", "      <td>0.681165</td>\n", "      <td>0.570679</td>\n", "      <td>0.433686</td>\n", "      <td>0.422295</td>\n", "      <td>0.423108</td>\n", "      <td>0.624767</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>rogue_v2_rec</td>\n", "      <td></td>\n", "      <td>0.587524</td>\n", "      <td>0.678845</td>\n", "      <td>0.583465</td>\n", "      <td>0.442636</td>\n", "      <td>0.434500</td>\n", "      <td>0.436941</td>\n", "      <td>0.638016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>rogue_v2_research</td>\n", "      <td></td>\n", "      <td>0.621252</td>\n", "      <td>0.652176</td>\n", "      <td>0.596143</td>\n", "      <td>0.450000</td>\n", "      <td>0.430000</td>\n", "      <td>0.435000</td>\n", "      <td>0.662515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>rogue_v2_research_longcon</td>\n", "      <td></td>\n", "      <td>0.628672</td>\n", "      <td>0.658112</td>\n", "      <td>0.602099</td>\n", "      <td>0.465000</td>\n", "      <td>0.440000</td>\n", "      <td>0.445000</td>\n", "      <td>0.665237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>rogue_v3</td>\n", "      <td></td>\n", "      <td>0.582519</td>\n", "      <td>0.686189</td>\n", "      <td>0.581692</td>\n", "      <td>0.447518</td>\n", "      <td>0.437754</td>\n", "      <td>0.438568</td>\n", "      <td>0.635556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>rogue_v3_rec</td>\n", "      <td></td>\n", "      <td>0.585382</td>\n", "      <td>0.687402</td>\n", "      <td>0.583878</td>\n", "      <td>0.453214</td>\n", "      <td>0.440195</td>\n", "      <td>0.441823</td>\n", "      <td>0.635901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>star2sl_rec</td>\n", "      <td></td>\n", "      <td>0.597091</td>\n", "      <td>0.658751</td>\n", "      <td>0.587952</td>\n", "      <td>0.447518</td>\n", "      <td>0.439382</td>\n", "      <td>0.441823</td>\n", "      <td>0.648102</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>rogue_v2</td>\n", "      <td>roguesl-v2-16b-seth616-rec</td>\n", "      <td>0.599617</td>\n", "      <td>0.691634</td>\n", "      <td>0.595750</td>\n", "      <td>0.446779</td>\n", "      <td>0.436975</td>\n", "      <td>0.436975</td>\n", "      <td>0.650086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>rogue_v2_rec</td>\n", "      <td>roguesl-v2-16b-seth616-rec</td>\n", "      <td>0.605390</td>\n", "      <td>0.685441</td>\n", "      <td>0.602555</td>\n", "      <td>0.448179</td>\n", "      <td>0.442577</td>\n", "      <td>0.445378</td>\n", "      <td>0.658430</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>rogue_v2_research</td>\n", "      <td>roguesl-v2-16b-seth616-rec</td>\n", "      <td>0.616155</td>\n", "      <td>0.639344</td>\n", "      <td>0.576484</td>\n", "      <td>0.415929</td>\n", "      <td>0.389381</td>\n", "      <td>0.389381</td>\n", "      <td>0.632713</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>rogue_v2_research_longcon</td>\n", "      <td>roguesl-v2-16b-seth616-rec</td>\n", "      <td>0.636447</td>\n", "      <td>0.664224</td>\n", "      <td>0.598991</td>\n", "      <td>0.451327</td>\n", "      <td>0.424779</td>\n", "      <td>0.424779</td>\n", "      <td>0.650359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>rogue_v3</td>\n", "      <td>roguesl-v2-16b-seth616-rec</td>\n", "      <td>0.603181</td>\n", "      <td>0.696095</td>\n", "      <td>0.604024</td>\n", "      <td>0.457983</td>\n", "      <td>0.446779</td>\n", "      <td>0.448179</td>\n", "      <td>0.659191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>rogue_v3_rec</td>\n", "      <td>roguesl-v2-16b-seth616-rec</td>\n", "      <td>0.600822</td>\n", "      <td>0.683562</td>\n", "      <td>0.598708</td>\n", "      <td>0.452381</td>\n", "      <td>0.438375</td>\n", "      <td>0.441176</td>\n", "      <td>0.657524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>star2sl_rec</td>\n", "      <td>roguesl-v2-16b-seth616-rec</td>\n", "      <td>0.617826</td>\n", "      <td>0.672088</td>\n", "      <td>0.610267</td>\n", "      <td>0.457983</td>\n", "      <td>0.448179</td>\n", "      <td>0.449580</td>\n", "      <td>0.673296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>rogue_v2</td>\n", "      <td>roguesl-v3-16b-seth616-rec</td>\n", "      <td>0.542306</td>\n", "      <td>0.666651</td>\n", "      <td>0.535921</td>\n", "      <td>0.415534</td>\n", "      <td>0.401942</td>\n", "      <td>0.403883</td>\n", "      <td>0.589664</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>rogue_v2_rec</td>\n", "      <td>roguesl-v3-16b-seth616-rec</td>\n", "      <td>0.562754</td>\n", "      <td>0.669701</td>\n", "      <td>0.556998</td>\n", "      <td>0.434951</td>\n", "      <td>0.423301</td>\n", "      <td>0.425243</td>\n", "      <td>0.609714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>rogue_v2_research</td>\n", "      <td>roguesl-v3-16b-seth616-rec</td>\n", "      <td>0.627871</td>\n", "      <td>0.668843</td>\n", "      <td>0.621678</td>\n", "      <td>0.494253</td>\n", "      <td>0.482759</td>\n", "      <td>0.494253</td>\n", "      <td>0.701223</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>rogue_v2_research_longcon</td>\n", "      <td>roguesl-v3-16b-seth616-rec</td>\n", "      <td>0.618573</td>\n", "      <td>0.650173</td>\n", "      <td>0.606135</td>\n", "      <td>0.482759</td>\n", "      <td>0.459770</td>\n", "      <td>0.471264</td>\n", "      <td>0.684561</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>rogue_v3</td>\n", "      <td>roguesl-v3-16b-seth616-rec</td>\n", "      <td>0.553872</td>\n", "      <td>0.672454</td>\n", "      <td>0.550730</td>\n", "      <td>0.433010</td>\n", "      <td>0.425243</td>\n", "      <td>0.425243</td>\n", "      <td>0.602789</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>rogue_v3_rec</td>\n", "      <td>roguesl-v3-16b-seth616-rec</td>\n", "      <td>0.563976</td>\n", "      <td>0.692726</td>\n", "      <td>0.563318</td>\n", "      <td>0.454369</td>\n", "      <td>0.442718</td>\n", "      <td>0.442718</td>\n", "      <td>0.605922</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>star2sl_rec</td>\n", "      <td>roguesl-v3-16b-seth616-rec</td>\n", "      <td>0.568344</td>\n", "      <td>0.640260</td>\n", "      <td>0.557015</td>\n", "      <td>0.433010</td>\n", "      <td>0.427184</td>\n", "      <td>0.431068</td>\n", "      <td>0.613174</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        model              response_model  prefix_recall  \\\n", "0                    rogue_v2                                   0.575601   \n", "3                rogue_v2_rec                                   0.587524   \n", "6           rogue_v2_research                                   0.621252   \n", "9   rogue_v2_research_longcon                                   0.628672   \n", "12                   rogue_v3                                   0.582519   \n", "15               rogue_v3_rec                                   0.585382   \n", "18                star2sl_rec                                   0.597091   \n", "1                    rogue_v2  roguesl-v2-16b-seth616-rec       0.599617   \n", "4                rogue_v2_rec  roguesl-v2-16b-seth616-rec       0.605390   \n", "7           rogue_v2_research  roguesl-v2-16b-seth616-rec       0.616155   \n", "10  rogue_v2_research_longcon  roguesl-v2-16b-seth616-rec       0.636447   \n", "13                   rogue_v3  roguesl-v2-16b-seth616-rec       0.603181   \n", "16               rogue_v3_rec  roguesl-v2-16b-seth616-rec       0.600822   \n", "19                star2sl_rec  roguesl-v2-16b-seth616-rec       0.617826   \n", "2                    rogue_v2  roguesl-v3-16b-seth616-rec       0.542306   \n", "5                rogue_v2_rec  roguesl-v3-16b-seth616-rec       0.562754   \n", "8           rogue_v2_research  roguesl-v3-16b-seth616-rec       0.627871   \n", "11  rogue_v2_research_longcon  roguesl-v3-16b-seth616-rec       0.618573   \n", "14                   rogue_v3  roguesl-v3-16b-seth616-rec       0.553872   \n", "17               rogue_v3_rec  roguesl-v3-16b-seth616-rec       0.563976   \n", "20                star2sl_rec  roguesl-v3-16b-seth616-rec       0.568344   \n", "\n", "    prefix_precision  prefix_f1  ==@1_line        ==  exact_match  \\\n", "0           0.681165   0.570679   0.433686  0.422295     0.423108   \n", "3           0.678845   0.583465   0.442636  0.434500     0.436941   \n", "6           0.652176   0.596143   0.450000  0.430000     0.435000   \n", "9           0.658112   0.602099   0.465000  0.440000     0.445000   \n", "12          0.686189   0.581692   0.447518  0.437754     0.438568   \n", "15          0.687402   0.583878   0.453214  0.440195     0.441823   \n", "18          0.658751   0.587952   0.447518  0.439382     0.441823   \n", "1           0.691634   0.595750   0.446779  0.436975     0.436975   \n", "4           0.685441   0.602555   0.448179  0.442577     0.445378   \n", "7           0.639344   0.576484   0.415929  0.389381     0.389381   \n", "10          0.664224   0.598991   0.451327  0.424779     0.424779   \n", "13          0.696095   0.604024   0.457983  0.446779     0.448179   \n", "16          0.683562   0.598708   0.452381  0.438375     0.441176   \n", "19          0.672088   0.610267   0.457983  0.448179     0.449580   \n", "2           0.666651   0.535921   0.415534  0.401942     0.403883   \n", "5           0.669701   0.556998   0.434951  0.423301     0.425243   \n", "8           0.668843   0.621678   0.494253  0.482759     0.494253   \n", "11          0.650173   0.606135   0.482759  0.459770     0.471264   \n", "14          0.672454   0.550730   0.433010  0.425243     0.425243   \n", "17          0.692726   0.563318   0.454369  0.442718     0.442718   \n", "20          0.640260   0.557015   0.433010  0.427184     0.431068   \n", "\n", "    edit_similarity  \n", "0          0.624767  \n", "3          0.638016  \n", "6          0.662515  \n", "9          0.665237  \n", "12         0.635556  \n", "15         0.635901  \n", "18         0.648102  \n", "1          0.650086  \n", "4          0.658430  \n", "7          0.632713  \n", "10         0.650359  \n", "13         0.659191  \n", "16         0.657524  \n", "19         0.673296  \n", "2          0.589664  \n", "5          0.609714  \n", "8          0.701223  \n", "11         0.684561  \n", "14         0.602789  \n", "17         0.605922  \n", "20         0.613174  "]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import defaultdict\n", "\n", "rows = []\n", "for name, results in runs.items():\n", "    by_default_model = defaultdict(list)\n", "    for result, datum in zip(results, data):\n", "        if (\n", "            datum[\"completion\"][\"response\"][\"model\"]\n", "            == \"deeproguesl-farpref-33B-eth6-04-1\"\n", "        ):\n", "            continue\n", "\n", "        # skip if not mid-word\n", "        if not (\n", "            datum[\"ground_truth\"][:1].isalnum()\n", "            and datum[\"completion\"][\"request\"][\"prefix\"][-1:].isalnum()\n", "        ):\n", "            continue\n", "\n", "        # By response model\n", "        metrics = {}\n", "        metrics.update(compute_metrics(result))\n", "        metrics[\"model\"] = name\n", "        metrics[\"response_model\"] = datum[\"completion\"][\"response\"][\"model\"]\n", "        rows.append(metrics)\n", "\n", "        # All\n", "        metrics = metrics.copy()\n", "        metrics[\"response_model\"] = \"\"\n", "        rows.append(metrics)\n", "\n", "# Aggregate\n", "df = pd.DataFrame(rows)\n", "df.groupby([\"model\", \"response_model\"]).mean().reset_index().sort_values([\"response_model\", \"model\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'rogue_v3': 32.2561797752809,\n", " 'rogue_v2': 32.39900377877018,\n", " 'rogue_v3_rec': 32.5984667802385,\n", " 'rogue_v2_rec': 32.794315506222674,\n", " 'star2sl_rec': 33.11178100048884,\n", " 'rogue_v2_research': 66.08510638297872,\n", " 'rogue_v2_research_longcon': 69.41624365482234}"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["# Mean generation length\n", "{\n", "    name: np.mean(\n", "        [len(datum[\"generation\"]) for datum in run if len(datum[\"generation\"]) != 0]\n", "    )\n", "    for name, run in runs.items()\n", "}"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({True: 6208, False: 53})"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["# empty or whitespace ground truths\n", "Counter(len(datum[\"ground_truth\"].strip()) != 0 for datum in data)"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'rogue_v3': Counter({True: 5785, False: 476}),\n", " 'rogue_v2': Counter({True: 5822, False: 439}),\n", " 'rogue_v3_rec': Counter({True: 5870, False: 391}),\n", " 'rogue_v2_rec': Counter({True: 5946, False: 315}),\n", " 'star2sl_rec': Counter({True: 6137, False: 124}),\n", " 'rogue_v2_research': Counter({True: 987, False: 13}),\n", " 'rogue_v2_research_longcon': Counter({True: 985, False: 15})}"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["# Num empty texts\n", "{\n", "    name: Counter(len(datum[\"generation\"]) != 0 for datum in run)\n", "    for name, run in runs.items()\n", "}"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({(False, False): 1371, (True, False): 154})"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["# EMs where star2sl differs from gronund_truth at first char\n", "Counter(\n", "    [\n", "        (\n", "            a[\"generation\"] == a[\"ground_truth\"],\n", "            # b[\"generation\"] == b[\"ground_truth\"],\n", "            c[\"generation\"] == c[\"ground_truth\"],\n", "            # datum[\"completion\"][\"response\"][\"model\"],\n", "        )\n", "        for a, b, c, datum in zip(\n", "            runs[\"rogue_v2_rec\"], runs[\"rogue_v3_rec\"], runs[\"star2sl_rec\"], data\n", "        )\n", "        if not c[\"ground_truth\"].startswith(c[\"generation\"][:1])\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Manually look at examples:\n", "for a, b, datum in zip(runs[\"rogue_v2_rec\"], runs[\"star2sl_rec\"], data):\n", "    # if not b[\"ground_truth\"].startswith(b[\"generation\"][:1]) and a[\"generation\"] == a[\"ground_truth\"]:\n", "\n", "    if (\n", "        not (\n", "            datum[\"ground_truth\"][:1].isalnum()\n", "            and datum[\"completion\"][\"request\"][\"prefix\"][-1:].isalnum()\n", "        )\n", "        and a[\"generation\"] == a[\"ground_truth\"]\n", "        and b[\"generation\"] != b[\"ground_truth\"]\n", "    ):\n", "        print(\n", "            b[\"artifacts\"][0][\"request_ids\"][0]\n", "            if \"artifacts\" in b and b[\"artifacts\"][0][\"request_ids\"]\n", "            else None\n", "        )\n", "        print(a[\"path\"])\n", "        print(repr(b[\"prefix\"][-100:]))\n", "        print(repr(a[\"generation\"]))\n", "        print(repr(b[\"generation\"]))\n", "        print(repr(datum[\"ground_truth\"]))\n", "        print(repr(b[\"suffix\"][:100]))\n", "        print(\"---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import research.eval.harness.utils as utils\n", "from pathlib import Path\n", "import re\n", "from collections import Counter"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["(961, 22, '72f78bd3-cb10-4404-b9f3-285bea96975b')"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Dogfood, limit: 100 repos, with request ids\n", "x = utils.read_jsonl_zst(\"/mnt/efs/augment/eval/jobs/HJcB7FM9/000_null_CCEval_completed_patches.jsonl.zst\")\n", "# Research, limit: 100 repos\n", "z = utils.read_jsonl_zst(\"/mnt/efs/augment/eval/jobs/8ZpCbypC/000_rogue_statelesscache_CCEval_completed_patches.jsonl.zst\")\n", "# Research, limit: 1 repo, but this run also logs the embedding ids and similarity scores\n", "y = utils.read_jsonl_zst(\"/mnt/efs/augment/eval/jobs/FK4DUAQ5/000_rogue_statelesscache_CCEval_completed_patches.jsonl.zst\")\n", "len(x), len(y), x[1][\"artifacts\"][0][\"request_ids\"][0]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({(True, True): 402,\n", "         (<PERSON><PERSON><PERSON>, F<PERSON>e): 387,\n", "         (<PERSON><PERSON><PERSON>, True): 139,\n", "         (True, False): 33})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Matrix of dogfood correct vs research correct\n", "Counter(zip([a[\"generation\"] == a[\"ground_truth\"] for a in x], [a[\"generation\"] == a[\"ground_truth\"] for a in z]))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["dogfood_prompt = Path(\"/mnt/efs/augment/user/jeff/2024-03-14-prod-reg/dogfood_prompt\").read_text()\n", "dogfood_retriever_prompt = Path(\"/mnt/efs/augment/user/jeff/2024-03-14-prod-reg/dogfood_retriever_prompt\").read_text()\n", "research_prompt = y[1][\"prompt\"]\n", "research_retriever_prompt = y[1][\"artifacts\"][0][\"retriever_prompt\"]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["('                outSegments = addSegment(segment, outSegments)\\n            }',\n", " '                outSegments.push(segment)\\n            }',\n", " '                outSegments = addSegment(segment, outSegments)\\n            }',\n", " '                outSegments = addSegment(segment, outSegments)\\n            }')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# The generation for dogfood is incorrect, because it fails to call `addSegment`\n", "x[1][\"ground_truth\"], x[1][\"generation\"], y[1][\"ground_truth\"], y[1][\"generation\"]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(True,\n", " 'src/formats/html.ts<|fim-prefix|>gment parts if expected HTML segment\\n *\\n * @param element HTML segm',\n", " '                    outSegments[totalSegments - 1].endTime\\n                    )\\n                }\\n\\n')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# The retriever prompt is the same as request insights, modulo the end of query token (which gets added later in research).\n", "dogfood_retriever_prompt == (research_retriever_prompt + \"<|ret-endofquery|>\"), research_retriever_prompt[:100], research_retriever_prompt[-100:]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(['test/html.test.ts',\n", "  'test/segments.test.ts',\n", "  'src/segments.ts',\n", "  'test/segments.test.ts',\n", "  'src/index.ts',\n", "  'test/html.test.ts',\n", "  'test/html.test.ts',\n", "  'src/segments.ts',\n", "  'src/segments.ts',\n", "  'src/formats/srt.ts',\n", "  'src/formats/json.ts'],\n", " ['src/formats/json.ts',\n", "  'src/segments.ts',\n", "  'src/types.ts',\n", "  'src/segments.ts',\n", "  'src/formats/json.ts',\n", "  'src/segments.ts',\n", "  'src/formats/srt.ts',\n", "  'src/formats/srt.ts',\n", "  'src/formats/srt.ts',\n", "  'src/formats/json.ts'])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Dogfood is able to retrieve from mostly all of the files that research is able to \n", "p = re.compile(\"<filename>(.*)<\\\\|ret-body\\\\|>\")\n", "p.findall(dogfood_prompt), p.findall(research_prompt)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(['import { addSegment } from \"../src/segments\"',\n", "  '<|ret-start|><filename>src/formats/srt.ts<|ret-body|>import { addSegment } from \"../segments\"',\n", "  '<|ret-start|><filename>src/formats/json.ts<|ret-body|>import { addSegment } from \"../segments\"'],\n", " ['import { addSegment } from \"../segments\"',\n", "  '<|retrieval_section|><|ret-start|><filename>src/formats/json.ts<|ret-body|>import { addSegment } from \"../segments\"',\n", "  'export const addSegment = (newSegment: Segment, priorSegments: Array<Segment>): Array<Segment> => {',\n", "  '                    outSegments = addSegment(createSegmentFromSRTLines(segmentLines, lastSpeaker), outSegments)',\n", "  '            outSegments = addSegment(createSegmentFromSRTLines(segmentLines, lastSpeaker), outSegments)',\n", "  '            outSegments = addSegment(subtitleSegment, outSegments)'])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# We note that research finds several usages of addSegment(), whereas dogfood does not\n", "# Note: addSegment does not show up in the retriever prompt\n", "# but research might be fetching the similar looking outSegments code pattern.\n", "p = re.compile(\"(.*addSegment.*)\")\n", "p.findall(dogfood_prompt), p.findall(research_prompt)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(5224.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_2603006936'),\n", " (4824.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_3027275665'),\n", " (5044.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_2712819270'),\n", " (4972.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_1206578561'),\n", " (5312.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_640766099'),\n", " (5452.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_2726116810'),\n", " (4912.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_1197814136'),\n", " (5008.0,\n", "  '26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61_3520440978')]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Dogfood was also able to retrieve from json.ts, which has blob_name 26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61\n", "# However, it is only able to retrieve chunk 0, which it assigns a score of 5507.919\n", "# It should match the first chunk. However, this does not match any of the research scores\n", "research_emb_ids = y[1][\"artifacts\"][0][\"embedding_ids\"]\n", "research_scores = y[1][\"artifacts\"][0][\"similarity_scores\"]\n", "[(score, chunk_id) for score, (doc_id, chunk_id) in zip(research_scores, research_emb_ids) if doc_id == \"26a93620ec5c4d1afc440ae922e967302c7ff23204f8668c8a1fd114b5e34a61\"]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["(True, True)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# That specific chunk is also exactly contained inside the research prompt\n", "# so even though chunking may be different between dogfood and research,\n", "# This one also has different scores.\n", "dogfood_json_0 = Path(\"/mnt/efs/augment/user/jeff/2024-03-14-prod-reg/dogfood_json_0.ts\").read_text()\n", "dogfood_json_0 = \"<|ret-body|>\" + dogfood_json_0 + \"<\"\n", "dogfood_json_0 in dogfood_prompt, dogfood_json_0 in research_prompt"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(4876.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_3500322817'),\n", " (4908.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_41750745'),\n", " (4216.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_390676154'),\n", " (4976.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_643158591'),\n", " (4836.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_2550836841'),\n", " (4736.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_235164597'),\n", " (4868.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_698504092'),\n", " (4844.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_1160586143'),\n", " (4708.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_872635804'),\n", " (4164.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_3871855905'),\n", " (4552.0,\n", "  '08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825_2192022895')]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Dogfood also likes the html.test.ts file, which has blob_name 08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825\n", "# It assigns chunk 9 a score of 5177.6475, chunk 0: 5142.255, and chunk 17: 4957.037.\n", "# Lets see what research says\n", "[(score, chunk_id) for score, (doc_id, chunk_id) in zip(research_scores, research_emb_ids) if doc_id == \"08727002f16e7927b6d08be491c8b54a56bb3e2fa776ef07302e23b30d9f4825\"]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
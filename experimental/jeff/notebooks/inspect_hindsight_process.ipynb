{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "from collections import defaultdict\n", "from datetime import datetime, timedelta, timezone\n", "\n", "from base.datasets.hindsight_completion_dataset import (\n", "    HindsightCompletionProcessArgs,\n", "    HindsightCompletionQuery<PERSON>rgs,\n", "    HindsightCompletionQueryResults,\n", "    _process,\n", "    query_and_process,\n", ")\n", "from base.datasets.hindsight_lib import TextRange, UninterruptedHeuristic\n", "from base.datasets.tenants import get_tenant\n", "from base.datasets.user_event_lib import CompletionRequestIdIssuedEvent, TextEditEvent\n", "from research.tools.export_datasets.export_hindsight_completions import generate_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["timestamp_begin = datetime(2024, 5, 10, tzinfo=timezone.utc)\n", "timestamp_end = datetime(2024, 5, 15, tzinfo=timezone.utc)\n", "tenant = get_tenant(\"dogfood\")\n", "# tenant = get_tenant(\"aitutor-pareto\")\n", "# tenant = get_tenant(\"aitutor-turing\")\n", "user_event_limit = None\n", "sample_limit = 1000\n", "time_limit = <PERSON><PERSON>ta(hours=1)\n", "max_interruption_chars = 0\n", "allow_overlaps = False\n", "allow_empty_ground_truth = False\n", "min_blobs = 50"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_args = HindsightCompletionQueryArgs(\n", "    user_event_limit=user_event_limit,\n", "    sample_limit=sample_limit,\n", ")\n", "process_args = HindsightCompletionProcessArgs(\n", "    time_limit=time_limit,\n", "    max_interruption_chars=max_interruption_chars,\n", "    allow_overlaps=allow_overlaps,\n", "    allow_empty_ground_truth=allow_empty_ground_truth,\n", "    min_blobs=min_blobs,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# May take a 3m or so per week of data\n", "query_results, process_results = query_and_process(\n", "    tenant,\n", "    timestamp_begin,\n", "    timestamp_end,\n", "    query_args=query_args,\n", "    process_args=process_args,\n", ")\n", "\n", "# Query takes most of the time, so it's easier to just reprocess with different settings.\n", "# process_results = _process(\n", "#     query_results,\n", "#     timestamp_end,\n", "#     process_args,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["summary = generate_summary(\n", "    tenant.name,\n", "    timestamp_begin,\n", "    timestamp_end,\n", "    query_args,\n", "    process_args,\n", "    query_results,\n", "    process_results,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# summary[\"process\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# build dict of request_id to subsequent user_events\n", "def get_user_events_by_request(\n", "    data: HindsightCompletionQueryResults, time_limit: <PERSON><PERSON><PERSON>\n", ") -> dict[str, list[TextEditEvent]]:\n", "    requests = defaultdict(dict)\n", "    user_events = defaultdict(list)\n", "\n", "    completions_map = {\n", "        completion.request_id: completion for completion in data.completions\n", "    }\n", "\n", "    for event in data.events:\n", "        session = (event.tenant, event.user_id, event.session_id)\n", "        if isinstance(event, CompletionRequestIdIssuedEvent):\n", "            if event.request_id not in completions_map:\n", "                continue\n", "            requests[session][event.request_id] = event.time\n", "\n", "        if isinstance(event, TextEditEvent):\n", "            for request_id in list(requests[session]):\n", "                if event.time - requests[session][request_id] >= time_limit:\n", "                    requests[session].pop(request_id)\n", "                else:\n", "                    user_events[request_id].append(event)\n", "\n", "    return user_events"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["post_events = get_user_events_by_request(query_results, process_args.time_limit)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for error in process_results.errors[:200]:\n", "    if error.error_type not in {\"overlap_disallowed\", \"interruption_chars_exceeded\"}:\n", "        continue\n", "    print(error.error_type)\n", "    print(error.event.request_id)\n", "    print(error.event.user_id)\n", "    if error.datum is not None:\n", "        print(error.datum.request.path)\n", "        print(repr(error.datum.request.prefix[-40:]))\n", "        print(repr(error.datum.request.suffix[:40]))\n", "        assert error.datum.request.position is not None\n", "        print(error.datum.request.position.cursor_position)\n", "    heuristic = UninterruptedHeuristic(\n", "        process_args.time_limit, max_interruption_chars=0, allow_overlaps=False\n", "    )\n", "    assert error.datum is not None\n", "    assert error.datum.request.position is not None\n", "    heuristic.add_request(\n", "        error.event.time,\n", "        error.event.request_id,\n", "        error.event.file_path,\n", "        TextRange.from_text(error.datum.request.position.cursor_position, \"\"),\n", "    )\n", "    for event in post_events[error.event.request_id]:\n", "        print(event.file_path, event.content_changes)\n", "        for content_change in event.content_changes:\n", "            heuristic.add_change(event.time, event.file_path, content_change)\n", "        if error.event.request_id in heuristic.results:\n", "            print(heuristic.results[error.event.request_id])\n", "        else:\n", "            print(heuristic.range_tracker.get_range(error.event.request_id))\n", "\n", "    print(\"=\" * 80)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
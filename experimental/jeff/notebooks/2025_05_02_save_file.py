#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to read a JSON file and rewrite save_file tool calls.
"""

import json
import os
import argparse
from typing import Callable, Dict, Any
from dataclasses import dataclass, field


def load_json_file(file_path: str) -> Dict[str, Any]:
    """Load and parse a JSON file."""
    with open(file_path, "r") as f:
        return json.load(f)


def save_json_file(data: Dict[str, Any], file_path: str) -> None:
    """Save data to a JSON file."""
    with open(file_path, "w") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


@dataclass
class ChunkSaveFileCalls:
    chunk_lines: int
    instructions_reminder: str
    description: str
    input_schema: dict
    stop_at_max_lines: int = 300
    prepend_line_number: bool = False

    def __post_init__(self):
        # Format the instructions_reminder description
        schema = self.input_schema["properties"]["instructions_reminder"]
        schema["description"] = schema["description"].format(self.instructions_reminder)

    def __call__(self, data: dict):
        chatHistory = []
        for chat_entry in data["chatHistory"]:
            if self.update_exchange(chat_entry):
                chatHistory.append(chat_entry)
            else:
                data["nodes"] = chat_entry["requestNodes"]
                break

        data["chatHistory"] = chatHistory

        for definition in data["toolDefinitions"]:
            if definition["name"] == "save-file":
                definition["inputSchemaJson"] = json.dumps(self.input_schema)
                definition["description"] = self.description

    def update_exchange(self, exchange: dict):
        for node in exchange["responseNodes"]:
            if not self.update_node(node):
                return False
        return True

    def update_node(self, node: dict):
        if node.get("type") != "TOOL_USE":
            return True

        if node["toolUse"]["toolName"] != "save-file":
            return True

        input_json = json.loads(node["toolUse"]["inputJson"])
        input_json["instructions_reminder"] = self.instructions_reminder

        if self.prepend_line_number:
            file_content = input_json["file_content"]
            lines = file_content.splitlines(keepends=True)
            lines = [f"{i+1:>6}\t{line}" for i, line in enumerate(lines)]
            input_json["file_content"] = "".join(lines)

        if self.stop_at_max_lines > 0:
            file_content = input_json["file_content"]
            lines = file_content.splitlines(keepends=True)
            if len(lines) > self.stop_at_max_lines:
                print(
                    f"Stopped before: Path: {input_json['file_path']}, Number of lines: {len(lines)}"
                )
                return False

        if self.chunk_lines != 0:
            file_content = input_json["file_content"]
            lines = file_content.splitlines(keepends=True)
            chunks = []
            for i in range(0, len(lines), self.chunk_lines):
                chunk = lines[i : i + self.chunk_lines]
                chunk_content = "".join(chunk)
                chunks.append(chunk_content)

            print(
                f"Path: {input_json['file_path']}, Number of lines: {len(lines)}, Number of chunks: {len(chunks)}"
            )

            del input_json["file_content"]
            for i, chunk in enumerate(chunks):
                input_json[f"file_content_{i+1}"] = chunk
        else:
            file_content = input_json["file_content"]
            lines = file_content.splitlines(keepends=True)
            print(f"Path: {input_json['file_path']}, Number of lines: {len(lines)}")

        node["toolUse"]["inputJson"] = json.dumps(input_json, separators=(", ", ": "))
        return True


def process_json_file(
    input_file: str, output_file: str, fn: Callable[[dict], None]
) -> None:
    """
    Process a JSON file to split save-file tool use calls into chunks.

    Args:
        input_file: Path to the input JSON file
        output_file: Path to the output JSON file
        max_lines: Maximum number of lines per chunk
    """
    data = load_json_file(input_file)
    fn(data)
    save_json_file(data, output_file)

    print(f"Processed {input_file} and saved to {output_file}")


ORIGINAL_REMINDER = (
    "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. "
    "IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."
)

REMINDER_STOP_IMMEDIATELY = (
    "LIMIT THE FILE CONTENT TO AT MOST 300 LINES. "
    "YOU MUST STOP IMMEDIATELY EVEN IF THE FILE IS INCOMPLETE. "
    "IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED."
)

ORIGINAL_DESCRIPTION = (
    "Save a new file. "
    "Use this tool to write new files with the attached content. "
    "Generate `instructions_reminder` first to remind yourself to limit the file content to at most 300 lines. "
    "It CANNOT modify existing files. "
    "Do NOT use this tool to edit an existing file by overwriting it entirely. "
    "Use the str-replace-editor tool to edit existing files instead."
)


LINE_NUMBERS_DESCRIPTION = """\
Save a new file.
Use this tool to write new files with the attached content.
Generate `instructions_reminder` first to remind yourself to limit the file content to at most 300 lines.

The content should be in a line-numbered format (like output from `cat -n`), with each line starting with line number(padded to 6 digits) and tab\n
Example:
```
     1\tdef my_function():
     2\t    print("Hello World")
     3\t
     4\tdef my_other_function():
     5\t    print("Hello World Again")
```

It CANNOT modify existing files.
Do NOT use this tool to edit an existing file by overwriting it entirely.
Use the str-replace-editor tool to edit existing files instead.
"""

ORIGINAL_SCHEMA = {
    "type": "object",
    "properties": {
        "instructions_reminder": {
            "type": "string",
            "description": "Should be exactly this string: '{}'",
        },
        "file_path": {
            "type": "string",
            "description": "The path of the file to save.",
        },
        "file_content": {
            "type": "string",
            "description": "The content of the file.",
        },
        "add_last_line_newline": {
            "type": "boolean",
            "description": "Whether to add a newline at the end of the file (default: true).",
        },
    },
    "required": [
        "instructions_reminder",
        "file_path",
        "file_content",
    ],
}

CHUNKERS = {
    # v1 = Original
    1: ChunkSaveFileCalls(
        chunk_lines=0,
        instructions_reminder=ORIGINAL_REMINDER,
        description=ORIGINAL_DESCRIPTION,
        input_schema=ORIGINAL_SCHEMA,
        stop_at_max_lines=0,
    ),
    # v2 = Add "YOU MUST STOP IMMEDIATELY EVEN IF THE FILE IS INCOMPLETE"
    2: ChunkSaveFileCalls(
        chunk_lines=0,
        instructions_reminder=REMINDER_STOP_IMMEDIATELY,
        description=ORIGINAL_DESCRIPTION,
        input_schema=ORIGINAL_SCHEMA,
    ),
    # v3 = Add 50 line chunking.
    3: ChunkSaveFileCalls(
        chunk_lines=50,
        instructions_reminder=REMINDER_STOP_IMMEDIATELY,
        description=(
            "Save a new file. "
            "Use this tool to write new files with the attached content. "
            "Generate `instructions_reminder` first to remind yourself to limit the file content to at most 300 lines. "
            "Specify `file_content_1` for the first chunk of 50 lines, `file_content_2` for the second chunk, and so on. "
            "It CANNOT modify existing files. "
            "Do NOT use this tool to edit an existing file by overwriting it entirely. "
            "Use the str-replace-editor tool to edit existing files instead."
        ),
        input_schema={
            "type": "object",
            "properties": {
                "instructions_reminder": {
                    "type": "string",
                    "description": "Should be exactly this string: '{}'",
                },
                "file_path": {
                    "type": "string",
                    "description": "The path of the file to save.",
                },
                "file_content_1": {
                    "type": "string",
                    "description": "A chunk of content of the file.",
                },
                "add_last_line_newline": {
                    "type": "boolean",
                    "description": "Whether to add a newline at the end of the file (default: true).",
                },
            },
            "required": [
                "instructions_reminder",
                "file_path",
                "file_content_1",
            ],
        },
    ),
    # v4 = v2 + Prepend line numbers
    4: ChunkSaveFileCalls(
        chunk_lines=0,
        instructions_reminder=REMINDER_STOP_IMMEDIATELY,
        description=LINE_NUMBERS_DESCRIPTION,
        input_schema=ORIGINAL_SCHEMA,
        prepend_line_number=True,
    ),
    # v5 = v4 + no stop in chat history
    5: ChunkSaveFileCalls(
        chunk_lines=0,
        instructions_reminder=REMINDER_STOP_IMMEDIATELY,
        description=LINE_NUMBERS_DESCRIPTION,
        input_schema=ORIGINAL_SCHEMA,
        prepend_line_number=True,
        stop_at_max_lines=0,
    ),
}


def main():
    parser = argparse.ArgumentParser(
        description="Split save-file tool use calls into chunks"
    )
    parser.add_argument("input_file", help="Path to the input JSON file")
    parser.add_argument(
        "--output-file",
        help="Path to the output JSON file (default: input_file with version suffix)",
    )
    parser.add_argument(
        "--version",
        type=int,
        default=1,
        help="Version of the chunker to use (default: 1)",
    )

    args = parser.parse_args()

    # Set default output file if not provided
    if not args.output_file:
        base, ext = os.path.splitext(args.input_file)
        args.output_file = f"{base}_v{args.version}{ext}"

    chunker = CHUNKERS[args.version]
    process_json_file(args.input_file, args.output_file, chunker)


if __name__ == "__main__":
    main()

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import json\n", "import pathlib\n", "\n", "from research.fim.fim_sampling import CSTFimSampler, FimProblem\n", "from research.fim.fim_augment import (\n", "    FileAugment<PERSON>,\n", "    AbstractFimAugmenter,\n", "    ImportShufflingAugmenter,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.languages.language_guesser import guess_language\n", "from base.languages.languages import to_vscode_name\n", "from research.eval.harness.tasks.cceval import _prepare_dataset, CCEval\n", "from tqdm.auto import tqdm\n", "from collections import Counter\n", "from pathlib import Path\n", "from typing import Any\n", "from research.core.types import Document\n", "from research.data.spark.pipelines.utils.map_parquet import FlatMapFn\n", "from research.data.rag import common, constants"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read CCEval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_patches_by_repo, all_documents_by_repo = _prepare_dataset(\n", "    CCEval.DEFAULT_DATASET_PATH\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(\n", "    guess_language(doc.path)\n", "    for repo, docs in all_documents_by_repo.items()\n", "    for doc in docs\n", ").most_common(10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.languages.languages import LanguageId\n", "\n", "BASE_LANG_TO_LANGPART: dict[LanguageId | None, str] = {\n", "    \"C\": \"c\",\n", "    \"C++\": \"c++\",\n", "    \"Go\": \"go\",\n", "    \"Java\": \"java\",\n", "    \"JavaScript\": \"javascript\",\n", "    \"Python\": \"python\",\n", "    \"Rust\": \"rust\",\n", "    \"TypeScript\": \"typescript\",\n", "    \"CSharp\": \"c-sharp\",\n", "    \"Ruby\": \"ruby\",\n", "    \"PHP\": \"php\",\n", "    \"TypeScript JSX\": \"tsx\",\n", "    \"JavaScript JSX\": \"jsx\",\n", "    \"CSS\": \"css\",\n", "    \"Shell\": \"shell\",\n", "    \"Scala\": \"scala\",\n", "    \"Lua\": \"lua\",\n", "    \"Kotlin\": \"kotlin\",\n", "    \"SQL\": \"sql\",\n", "    \"Markdown\": \"markdown\",\n", "}\n", "\n", "\n", "def get_langpart(path: pathlib.Path | str) -> str | None:\n", "    return BASE_LANG_TO_LANGPART.get(guess_language(path))\n", "\n", "\n", "def get_file_lists_by_repo(documents_by_repo: dict[str, list[Document]]):\n", "    return {\n", "        repo: [\n", "            {\n", "                \"max_stars_repo_path\": str(doc.path),\n", "                \"content\": doc.text,\n", "                \"size\": len(doc.text),\n", "                \"langpart\": get_langpart(doc.path),\n", "            }\n", "            for doc in docs\n", "        ]\n", "        for repo, docs in documents_by_repo.items()\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file_lists_by_repo = get_file_lists_by_repo(all_documents_by_repo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fim Problem"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RETRIEVAL_LANGUAGES = constants.RETRIEVAL_LANGUAGES\n", "\n", "# c-sharp is not supported by import noising\n", "SAMPLE_LANGUAGES = {\"java\", \"python\", \"typescript\"}\n", "assert SAMPLE_LANGUAGES.issubset(constants.SAMPLE_LANGUAGES)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def make_file_filter():\n", "    return common.FileFilter(\n", "        small_filter_char_threshold=500,\n", "        small_downsampled_probability=0.1,\n", "        small_downsample_char_threshold=1500,\n", "        sample_languages=SAMPLE_LANGUAGES,\n", "        only_keep_unit_test_file=False,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def make_generator(every_n_lines=200, max_problems_per_file=5, random_seed=74912):\n", "    sampler = CSTFimSampler()\n", "    get_node_weight = None\n", "    return common.RobustFIMSampler(\n", "        make_file_filter(),\n", "        max_problems_per_file=max_problems_per_file,\n", "        every_n_lines=every_n_lines,\n", "        random_seed=random_seed,\n", "        sampler=sampler,\n", "        get_node_weight=get_node_weight,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "file_filter = make_file_filter()\n", "line_counts = list(\n", "    file[\"content\"].count(\"\\n\")\n", "    for file_list in file_lists_by_repo.values()\n", "    for file in file_list\n", "    if not file_filter.filter(file)\n", ")\n", "\n", "plt.hist(line_counts, bins=20, range=(0, 1000))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_fim_problems(\n", "    repo_file_lists: dict[str, list[dict[str, Any]]], gen: FlatMapFn\n", ") -> dict[str, dict[str, Any]]:\n", "    problems: dict[str, dict[str, Any]] = {}\n", "    for repo, file_list in tqdm(repo_file_lists.items()):\n", "        gen_outputs = list(gen(file_list))\n", "        if gen_outputs:\n", "            assert isinstance(gen_outputs[0], dict)\n", "            problems[repo] = gen_outputs[0]\n", "\n", "    return problems"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gen = make_generator(every_n_lines=50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fim_problems_by_repo = get_fim_problems(file_lists_by_repo, gen)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_problem_count(fim_problems: dict[str, dict[str, Any]]):\n", "    return sum(len(problems[\"fim_problems\"]) for problems in fim_problems.values())\n", "\n", "get_problem_count(fim_problems_by_repo)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fim Augment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.static_analysis.parsing import TsParsedFile\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from random import Random"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def augment_fim_problems(\n", "    fim_problems_by_repo: dict[str, dict[str, Any]],\n", "    aug: AbstractFimAug<PERSON><PERSON>,\n", "    rng: Random,\n", "):\n", "    aug_problems_by_repo = {}\n", "    filtered_problems_by_repo = {}\n", "    for repo, repo_problems in tqdm(fim_problems_by_repo.items()):\n", "        file_list = repo_problems[\"file_list\"]\n", "        file_map = {file[\"max_stars_repo_path\"]: file[\"content\"] for file in file_list}\n", "        aug_problems = []\n", "        filtered_problems = []\n", "        for fim_problem in repo_problems[\"fim_problems\"]:\n", "            assert isinstance(fim_problem, FimProblem)\n", "            file = file_map[str(fim_problem.file_path)]\n", "            lang = guess_lang_from_fp(fim_problem.file_path)\n", "            assert lang is not None\n", "            pfile = TsParsedFile.parse(\n", "                path=pathlib.Path(fim_problem.file_path),\n", "                lang=lang,\n", "                code=file,\n", "            )\n", "            aug_problem = aug.transform(fim_problem, pfile, rng)\n", "            if aug_problem is not None:\n", "                aug_problems.append(aug_problem)\n", "                filtered_problems.append(fim_problem)\n", "\n", "        aug_problems_by_repo[repo] = repo_problems.copy()\n", "        aug_problems_by_repo[repo][\"fim_problems\"] = aug_problems\n", "        filtered_problems_by_repo[repo] = repo_problems.copy()\n", "        filtered_problems_by_repo[repo][\"fim_problems\"] = filtered_problems\n", "    return aug_problems_by_repo, filtered_problems_by_repo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["aug_import_problems_by_repo, import_problems_by_repo = augment_fim_problems(\n", "    fim_problems_by_repo, ImportShufflingAugmenter(), <PERSON>(42)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(\n", "    get_problem_count(fim_problems_by_repo),\n", "    get_problem_count(aug_import_problems_by_repo),\n", "    get_problem_count(import_problems_by_repo),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tuple(\n", "    Counter(\n", "        [\n", "            len(problem.middle_spans)\n", "            for problems in fim.values()\n", "            for problem in problems[\"fim_problems\"]\n", "        ]\n", "    ).most_common(10)\n", "    for fim in [\n", "        fim_problems_by_repo,\n", "        aug_import_problems_by_repo,\n", "        import_problems_by_repo,\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tuple(\n", "    Counter(\n", "        [\n", "            any(span.skipped for span in problem.middle_spans)\n", "            for problems in problem_by_repo.values()\n", "            for problem in problems[\"fim_problems\"]\n", "        ]\n", "    ).most_common(10)\n", "    for problem_by_repo in [\n", "        fim_problems_by_repo,\n", "        aug_import_problems_by_repo,\n", "        import_problems_by_repo,\n", "    ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Convert to CCEval Input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.cceval import CCEvalInput\n", "from typing import Callable"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_cceval_lang(path: str | pathlib.Path) -> str | None:\n", "    # This matches for csharp, java, typescript, and python\n", "    return to_vscode_name(guess_language(path))\n", "\n", "\n", "def fim_problem_to_cceval_input(\n", "    problem: FimProblem, lang_counter: Counter, repo_name: str\n", ") -> CCEvalInput | None:\n", "    ground_truth = []\n", "    if any(span.skipped for span in problem.middle_spans):\n", "        return None\n", "    for span in problem.middle_spans:\n", "        ground_truth.append(span.content.code)\n", "    ground_truth = \"\\n\".join(ground_truth)\n", "    lang = get_cceval_lang(problem.file_path)\n", "    assert lang is not None\n", "    lang_count = lang_counter[lang]\n", "    lang_counter[lang] += 1\n", "    return CCEvalInput(\n", "        prompt=problem.prefix.code,\n", "        right_context=problem.suffix.code,\n", "        groundtruth=ground_truth,\n", "        metadata=CCEvalInput.Meta(\n", "            task_id=f\"project_cc_{lang}/{lang_count}\",\n", "            repository=repo_name,\n", "            file=str(problem.file_path),\n", "            context_start_lineno=0,\n", "            groundtruth_start_lineno=-1,\n", "            right_context_start_lineno=-1,\n", "        ),\n", "    )\n", "\n", "\n", "def fim_problems_to_cceval_inputs(fim_problems: dict[str, dict[str, Any]]):\n", "    lang_counter = Counter()\n", "    cceval_inputs_by_repo = {\n", "        repo: [\n", "            cceval_input\n", "            for problem in problems[\"fim_problems\"]\n", "            if isinstance(problem, FimProblem)\n", "            if (\n", "                cceval_input := fim_problem_to_cceval_input(problem, lang_counter, repo)\n", "            )\n", "        ]\n", "        for repo, problems in fim_problems.items()\n", "    }\n", "    return cceval_inputs_by_repo\n", "\n", "\n", "def filter_cceval_input(cceval_input: CCEvalInput):\n", "    \"\"\"Returns true if should include.\"\"\"\n", "    if not 0 < len(cceval_input.groundtruth) < 100:\n", "        return False\n", "    return True\n", "\n", "\n", "def filter_cceval_inputs(\n", "    cceval_inputs_by_repo: dict[str, list[CCEvalInput]],\n", "    filter_input: Callable[[CCEvalInput], bool] = filter_cceval_input,\n", "):\n", "    new_inputs_by_repo = {}\n", "    for repo, cceval_inputs in cceval_inputs_by_repo.items():\n", "        inputs = [\n", "            cceval_input\n", "            for cceval_input in cceval_inputs\n", "            if filter_input(cceval_input)\n", "        ]\n", "        if inputs:\n", "            new_inputs_by_repo[repo] = inputs\n", "    return new_inputs_by_repo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cceval_inputs_by_repo = fim_problems_to_cceval_inputs(fim_problems_by_repo)\n", "cceval_imports_by_repo = fim_problems_to_cceval_inputs(import_problems_by_repo)\n", "cceval_aug_imports_by_repo = fim_problems_to_cceval_inputs(aug_import_problems_by_repo)\n", "cceval_inputs_by_repo = filter_cceval_inputs(cceval_inputs_by_repo)\n", "cceval_imports_by_repo = filter_cceval_inputs(cceval_imports_by_repo)\n", "cceval_aug_imports_by_repo = filter_cceval_inputs(cceval_aug_imports_by_repo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def round_sig_figs_1(x: int) -> int:\n", "    \"\"\"Round to 1 significant figure, e.g. 19 -> 20.\"\"\"\n", "    return int(float(f\"{x:.1g}\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Count ground_truth lens\n", "tuple(\n", "    sorted(\n", "        Counter(\n", "            round_sig_figs_1(len(input.groundtruth))\n", "            for inputs in cceval_inputs.values()\n", "            for input in inputs\n", "        ).most_common(100)\n", "    )\n", "    for cceval_inputs in [cceval_inputs_by_repo, cceval_imports_by_repo, cceval_aug_imports_by_repo]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Count ground_truth lines\n", "tuple(\n", "    sorted(\n", "        Counter(\n", "            round_sig_figs_1(input.groundtruth.count(\"\\n\"))\n", "            for inputs in cceval_inputs.values()\n", "            for input in inputs\n", "        ).most_common(100)\n", "    )\n", "    for cceval_inputs in [cceval_inputs_by_repo, cceval_imports_by_repo, cceval_aug_imports_by_repo]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "flattened = [\n", "    (repo, input)\n", "    for repo, inputs in cceval_aug_imports_by_repo.items()\n", "    for input in inputs\n", "]\n", "random.<PERSON>(42).shuffle(flattened)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for repo, input in flattened[:10]:\n", "    print(f\"{repo}: {input.metadata.file}\")\n", "    print(input.prompt)\n", "    print(\"====\")\n", "    print(input.groundtruth)\n", "    print(\"====\")\n", "    print(input.right_context)\n", "    print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Convert to jsonl"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def write_dataset(\n", "    output_path: Path,\n", "    inputs_by_repo: dict[str, list[CCEvalInput]],\n", "    documents_by_repo: dict[str, list[Document]],\n", "):\n", "    output_path.mkdir(exist_ok=True, parents=True)\n", "    for repo, inputs in inputs_by_repo.items():\n", "        org, repo_name = repo.split(\"/\")\n", "        org_path = output_path / org\n", "        org_path.mkdir(exist_ok=True, parents=True)\n", "        with open(org_path / f\"{repo_name}_patches.jsonl\", \"w\") as f:\n", "            for input in inputs:\n", "                f.write(json.dumps(dataclasses.asdict(input)) + \"\\n\")\n", "\n", "        docs = documents_by_repo[repo]\n", "        with open(org_path / f\"{repo_name}_retrieval_db.jsonl\", \"w\") as f:\n", "            for doc in docs:\n", "                f.write(json.dumps(dataclasses.asdict(doc)) + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["write_dataset(\n", "    Path(\"/mnt/efs/augment/user/jeff/data/eval/cceval_fim_imports/\"),\n", "    cceval_imports_by_repo,\n", "    all_documents_by_repo,\n", ")\n", "write_dataset(\n", "    Path(\"/mnt/efs/augment/user/jeff/data/eval/cceval_fim_imports_shuffle/\"),\n", "    cceval_aug_imports_by_repo,\n", "    all_documents_by_repo,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Make sure we can re-read\n", "fim_patches_by_repo, fim_documents_by_repo = _prepare_dataset(\n", "    Path(\"/mnt/efs/augment/user/jeff/data/eval/cceval_fim_imports/\")\n", ")\n", "fim_patches_by_repo_augment, fim_documents_by_repo_augment = _prepare_dataset(\n", "    Path(\"/mnt/efs/augment/user/jeff/data/eval/cceval_fim_imports_shuffle/\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(\n", "    len(fim_patches_by_repo),\n", "    len(fim_patches_by_repo_augment),\n", "    sum(len(patches) for patches in fim_patches_by_repo.values()),\n", "    sum(len(patches) for patches in fim_patches_by_repo_augment.values()),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
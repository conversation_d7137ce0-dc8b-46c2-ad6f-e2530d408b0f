{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import re\n", "from datetime import datetime, timedelta\n", "\n", "from base.datasets.completion_dataset import CompletionDataset\n", "from base.datasets.tenants import get_tenant\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Any of the timestamps in isoformat from the raw page should work\n", "timestamp = \"2024-05-29T18:08:04.352Z\"\n", "timestamp_begin = datetime.fromisoformat(timestamp) - <PERSON><PERSON><PERSON>(hours=1)\n", "timestamp_end = datetime.fromisoformat(timestamp) + timed<PERSON><PERSON>(hours=1)\n", "\n", "# Paste in request_id below, script assumes only one at a time.\n", "request_ids = \"\"\"\n", "\t028c7230-b2b3-419d-8c43-59966009861a\n", "\"\"\"\n", "request_ids = str(request_ids).split()\n", "assert len(request_ids) == 1\n", "\n", "# Get the completion and the blobs for the first completion.\n", "# Might take up to 1m to get all the blob contents.\n", "dataset = CompletionDataset.create(\n", "    tenant=get_tenant(\"dogfood\"),\n", "    filters=CompletionDataset.Filters(\n", "        request_ids=request_ids,\n", "        timestamp_begin=timestamp_begin,\n", "        timestamp_end=timestamp_end,\n", "    ),\n", "    limit=1,\n", "    with_reconstructed_files=False,\n", ")\n", "completions = list(dataset.get_completions())\n", "blobs = list(dataset.get_blobs(completions[0].request.blob_names))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print paths matching some regex\n", "path_re = r\"\"\n", "content_re = r\"\"\n", "for blob_name, blob in zip(completions[0].request.blob_names, blobs):\n", "    assert blob is not None\n", "    if not re.search(path_re, str(blob.path)):\n", "        continue\n", "    if not re.search(content_re, str(blob.content)):\n", "        continue\n", "    print(blob.path, blob_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.fastforward.starcoder.fwd_starcoder import generate_step_fn, StarcoderAttentionFactory\n", "from base.tokenizers import create_tokenizer_by_name\n", "\n", "import base.fastforward.fwd as fwd\n", "from research.retrieval.scorers.dense_scorer import GenericNeoXScorer\n", "\n", "from base.prompt_format_retrieve import get_retrieval_prompt_formatter_by_name\n", "from base.prompt_format_retrieve.prompt_formatter import DocumentRetrieverPromptInput"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scorer = GenericNeoXScorer(\n", "    checkpoint_path=\"/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000\",\n", "    query_formatter=None,\n", "    document_formatter=None,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer = create_tokenizer_by_name(\"rogue\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_formatter = get_retrieval_prompt_formatter_by_name(\n", "    \"ethanol6-embedding-with-path-key\", tokenizer=tokenizer\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scorer.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Doc + Query\n", "Edit the query and doc str (actually a chunk). The query str should be formatted already. The document is pre-formatted."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_str = '''services/support/frontend/src/routes/request.tsx<fim_prefix> || 0}\n", "          </BlobChunkLink>\n", "        );\n", "      },\n", "    },\n", "    {\n", "      title: \"Score\",\n", "      dataIndex: \"value\",\n", "      key: \"number\",\n", "      render: (score: number | undefined) => {\n", "        return <div>{score || 0}</div>;\n", "      },\n", "    },\n", "  ];\n", "\n", "  let prompt = <div></div>;\n", "  let embeddingsPrompt = requestData.completionHostResponse?.embeddingsPrompt;\n", "  if (\n", "    requestData.retrievalResponseList !== undefined &&\n", "    requestData.retrievalResponseList.length > 0\n", "  ) {\n", "    embeddingsPrompt = requestData.retrievalResponseList\n", "      .map((r) => getTokens(r.embeddingsPrompt, r.queryPrompt))\n", "      .flat();\n", "  }\n", "  if (embeddingsPrompt !== undefined) {\n", "    prompt = (\n", "      <>\n", "        <Descriptions bordered column={1}>\n", "          <Descriptions.Item label=\"Prompt\">\n", "            <pre className=\"language-plaintext\">\n", "              {embeddingsPrompt.map((t, index) => {\n", "                t.logProbs = undefined; // these tokens don't have log probs info\n", "                return <TokenComponent token={t} index={index} />;\n", "              })}\n", "            </pre>\n", "          </Descriptions.Item>\n", "        </Descriptions>\n", "        <Divider></Divider>\n", "      </>\n", "    );\n", "\n", "    return (\n", "      <>\n", "        {prompt}\n", "        <Table\n", "          dataSource={requestData.embeddingsSearchResponse?.results || []}\n", "          columns={columns}\n", "        />\n", "      </>\n", "    );\n", "  }\n", "\n", "  return (\n", "    <Table\n", "      dataSource={requestData.embeddingsSearchResponse?.results || []}\n", "      columns={columns}\n", "    />\n", "  );\n", "}\n", "\n", "function StatusComponent({ requestData }: { requestData: RequestData }) {\n", "  let status: \"success\" | \"error\" | \"processing\" | \"default\" = \"success\";\n", "  let statusText = \"Success\";\n", "  const r = requestData;\n", "  if (r.apiHttpResponse?.code === 499) {\n", "    statusText = \"Cancelled\";\n", "  } else if (\n", "    r.apiHttpResponse !== undefined &&\n", "    r.apiHttpResponse?.code !== 200\n", "  ) {\n", "    status = \"error\";\n", "    statusText = `Request Failed: ${r.apiHttpResponse!.code}`;\n", "  } else if (\n", "    r.inferRequest === undefined &&\n", "    r.editHostResponse === undefined &&\n", "    r.chatHostResponse === undefined &&\n", "    r.completionHostResponse === undefined\n", "  ) {\n", "    status = \"default\";\n", "    statusText = \"N/A\";\n", "  } else if (r.inferRequest !== undefined && r.apiHttpResponse === undefined) {\n", "    status = \"processing\";\n", "    statusText = \"Processing\";\n", "  }\n", "  return <Badge status={status} text={statusText} />;\n", "}\n", "\n", "export function RequestComponent({\n", "  requestId,\n", "  title,\n", "  desiredEvents,\n", "}: {\n", "  requestId: string;\n", "  title: string;\n", "  desiredEvents: string[];\n", "}) {\n", "  const [requestData, setRequestData] = useState<\n", "    RequestData | null | undefined\n", "  >(undefined);\n", "  const [errorAlert, setErrorAlert] = useState<Alert | undefined>(undefined);\n", "  const [isLoading, setIsLoading] = useState(true);\n", "  const flags = useFeatureFlags();\n", "\n", "  useEffect(() => {\n", "    const fetchRequest = async () => {\n", "      try {\n", "        setIsLoading(true);\n", "        const request = await getRequest(requestId, desiredEvents);\n", "        console.log(`request ${JSON.stringify(request)}`);\n", "        setRequestData(request);\n", "        setIsLoading(false);\n", "      } catch (e) {\n", "        console.log(`Error while loading the request data: ${e}`);\n", "        if (axios.isAxiosError(e)) {\n", "          console.log(`axios ${JSON.stringify(e)} ${e.response?.status}`);\n", "          if (e.response?.status === 404) {\n", "            // request not found\n", "            setIsLoading(false);\n", "            setRequestData(null);\n", "          } else if (e.response?.status === 403) {\n", "            setIsLoading(false);\n", "            setError<PERSON><PERSON><PERSON>(\n", "              <Alert\n", "                message=\"You do not have access to this request\"\n", "                type=\"error\"\n", "                action={\n", "                  // This always links to prod for simplicity, since dev doesn't have access control\n", "                  // enabled by default.\n", "                  <Link to={fl<fim_suffix>}>\n", "                    Request Access\n", "                  </Link>\n", "                }\n", "              />,\n", "            );\n", "          } else {\n", "            setIsLoading(false);\n", "            setErrorAlert(<Alert message=\"Failed to load data\" type=\"error\" />);\n", "          }\n", "        } else {\n", "          setIsLoading(false);\n", "          setErrorAlert(<Alert message=\"Failed to load data\" type=\"error\" />);\n", "        }\n", "      }\n", "    };\n", "    fetchRequest();\n", "  }, [requestId]);\n", "\n", "  if (errorAlert !== undefined) {\n", "    return errorAlert;\n", " <|ret-endofquery|>\n", "Prompt - <PERSON><PERSON> Retriever\t\n", "services/support/frontend/src/routes/request.tsx<fim_prefix> || 0}\n", "          </BlobChunkLink>\n", "        );\n", "      },\n", "    },\n", "    {\n", "      title: \"Score\",\n", "      dataIndex: \"value\",\n", "      key: \"number\",\n", "      render: (score: number | undefined) => {\n", "        return <div>{score || 0}</div>;\n", "      },\n", "    },\n", "  ];\n", "\n", "  let prompt = <div></div>;\n", "  let embeddingsPrompt = requestData.completionHostResponse?.embeddingsPrompt;\n", "  if (\n", "    requestData.retrievalResponseList !== undefined &&\n", "    requestData.retrievalResponseList.length > 0\n", "  ) {\n", "    embeddingsPrompt = requestData.retrievalResponseList\n", "      .map((r) => getTokens(r.embeddingsPrompt, r.queryPrompt))\n", "      .flat();\n", "  }\n", "  if (embeddingsPrompt !== undefined) {\n", "    prompt = (\n", "      <>\n", "        <Descriptions bordered column={1}>\n", "          <Descriptions.Item label=\"Prompt\">\n", "            <pre className=\"language-plaintext\">\n", "              {embeddingsPrompt.map((t, index) => {\n", "                t.logProbs = undefined; // these tokens don't have log probs info\n", "                return <TokenComponent token={t} index={index} />;\n", "              })}\n", "            </pre>\n", "          </Descriptions.Item>\n", "        </Descriptions>\n", "        <Divider></Divider>\n", "      </>\n", "    );\n", "\n", "    return (\n", "      <>\n", "        {prompt}\n", "        <Table\n", "          dataSource={requestData.embeddingsSearchResponse?.results || []}\n", "          columns={columns}\n", "        />\n", "      </>\n", "    );\n", "  }\n", "\n", "  return (\n", "    <Table\n", "      dataSource={requestData.embeddingsSearchResponse?.results || []}\n", "      columns={columns}\n", "    />\n", "  );\n", "}\n", "\n", "function StatusComponent({ requestData }: { requestData: RequestData }) {\n", "  let status: \"success\" | \"error\" | \"processing\" | \"default\" = \"success\";\n", "  let statusText = \"Success\";\n", "  const r = requestData;\n", "  if (r.apiHttpResponse?.code === 499) {\n", "    statusText = \"Cancelled\";\n", "  } else if (\n", "    r.apiHttpResponse !== undefined &&\n", "    r.apiHttpResponse?.code !== 200\n", "  ) {\n", "    status = \"error\";\n", "    statusText = `Request Failed: ${r.apiHttpResponse!.code}`;\n", "  } else if (\n", "    r.inferRequest === undefined &&\n", "    r.editHostResponse === undefined &&\n", "    r.chatHostResponse === undefined &&\n", "    r.completionHostResponse === undefined\n", "  ) {\n", "    status = \"default\";\n", "    statusText = \"N/A\";\n", "  } else if (r.inferRequest !== undefined && r.apiHttpResponse === undefined) {\n", "    status = \"processing\";\n", "    statusText = \"Processing\";\n", "  }\n", "  return <Badge status={status} text={statusText} />;\n", "}\n", "\n", "export function RequestComponent({\n", "  requestId,\n", "  title,\n", "  desiredEvents,\n", "}: {\n", "  requestId: string;\n", "  title: string;\n", "  desiredEvents: string[];\n", "}) {\n", "  const [requestData, setRequestData] = useState<\n", "    RequestData | null | undefined\n", "  >(undefined);\n", "  const [errorAlert, setErrorAlert] = useState<Alert | undefined>(undefined);\n", "  const [isLoading, setIsLoading] = useState(true);\n", "  const flags = useFeatureFlags();\n", "\n", "  useEffect(() => {\n", "    const fetchRequest = async () => {\n", "      try {\n", "        setIsLoading(true);\n", "        const request = await getRequest(requestId, desiredEvents);\n", "        console.log(`request ${JSON.stringify(request)}`);\n", "        setRequestData(request);\n", "        setIsLoading(false);\n", "      } catch (e) {\n", "        console.log(`Error while loading the request data: ${e}`);\n", "        if (axios.isAxiosError(e)) {\n", "          console.log(`axios ${JSON.stringify(e)} ${e.response?.status}`);\n", "          if (e.response?.status === 404) {\n", "            // request not found\n", "            setIsLoading(false);\n", "            setRequestData(null);\n", "          } else if (e.response?.status === 403) {\n", "            setIsLoading(false);\n", "            setError<PERSON><PERSON><PERSON>(\n", "              <Alert\n", "                message=\"You do not have access to this request\"\n", "                type=\"error\"\n", "                action={\n", "                  // This always links to prod for simplicity, since dev doesn't have access control\n", "                  // enabled by default.\n", "                  <Link to={fl<fim_suffix>}>\n", "                    Request Access\n", "                  </Link>\n", "                }\n", "              />,\n", "            );\n", "          } else {\n", "            setIsLoading(false);\n", "            setErrorAlert(<Alert message=\"Failed to load data\" type=\"error\" />);\n", "          }\n", "        } else {\n", "          setIsLoading(false);\n", "          setErrorAlert(<Alert message=\"Failed to load data\" type=\"error\" />);\n", "        }\n", "      }\n", "    };\n", "    fetchRequest();\n", "  }, [requestId]);\n", "\n", "  if (errorAlert !== undefined) {\n", "    return errorAlert;\n", " <|ret-endofquery|>'''\n", "query_tokens = tokenizer.tokenize_unsafe(query_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_text = '''              : \"Login Requested\",\n", "        });\n", "      })\n", "      .catch((error) => {\n", "        const message = error.response?.data ?? error.message;\n", "        messageApi.open({\n", "          type: \"error\",\n", "          content: `Error while processing the request: ${message}`,\n", "        });\n", "      });\n", "  };\n", "\n", "  return (\n", "    <div>\n", "      {contextHolder}\n", "      <Text>\n", "        <h3>Request Access to Production Namespace</h3>\n", "        <p>\n", "          Please fill out the form below to request access to the production\n", "          namespace. access requests are logged and audited.\n", "        </p>\n", "        <p>\n", "          See{\" \"}\n", "          <Link href=\"https://www.notion.so/Access\"\n", "            target=\"_blank\"\n", "          >\n", "            here\n", "          </Link>{\" \"}\n", "          for more details.\n", "'''\n", "doc_path = \"services/support/frontend/src/contexts/FeatureFlagsProvider.tsx\"\n", "prompt = DocumentRetrieverPromptInput(text=doc_text, path=doc_path)\n", "doc_tokens = doc_formatter.format_prompt(prompt).tokens()\n", "len(doc_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_text = '''import React, { createContext, useContext, useState, useEffect } from \"react\";\n", "import axios from \"axios\";\n", "\n", "const FeatureFlagsContext = createContext<FeatureFlags>({\n", "  individualAuthEnabled: false,\n", "  // This url is used to provide links for requesting access to production\n", "  // You can do this by calling flags.genieUrl <Link to={flags.genieUrl}></Link>\n", "  // error message\n", "  genieUrl: \"\",\n", "});\n", "\n", "async function getFeatureFlags(): Promise<any> {\n", "  const { data: response }: { data: FeatureFlagsResponse } =\n", "    await axios.get(\"/api/feature-flags\");\n", "  return response.flags;\n", "}\n", "\n", "export function useFeatureFlags() {\n", "  const context = useContext(FeatureFlagsContext);\n", "  if (!context) {\n", "    throw new Error(\n", "      \"useFeatureFlags must be used within a FeatureFlagsProvider\",\n", "    );\n", "  } \n", "  return context;\n", "}\n", "\n", "'''\n", "doc_path = \"services/support/frontend/src/contexts/FeatureFlagsProvider.tsx\"\n", "prompt = DocumentRetrieverPromptInput(text=doc_text, path=doc_path)\n", "doc_tokens = doc_formatter.format_prompt(prompt).tokens()\n", "len(doc_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compute score"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = scorer.embedding_model.contrastive_embed(queries=[query_tokens], documents=[doc_tokens])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result[0][0].dot(result[1][0])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 2}
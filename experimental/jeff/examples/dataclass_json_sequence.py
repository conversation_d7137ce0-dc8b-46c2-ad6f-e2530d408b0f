"""Example of dataclass_json with Sequence.

This shows why you should only use list and tuple for sequences.
"""

import dataclasses_json
from dataclasses import dataclass
from typing import Sequence


@dataclass
class Foo(dataclasses_json.DataClassJsonMixin):
    x: Sequence[int]


# Initializing the schema gives:
# /opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:288: UserWarning:
# Unknown type typing.Sequence[int] at Foo.x: typing.Sequence[int] It's advised to
# pass the correct marshmallow type to `mm_field`
print(Foo.schema().dumps(Foo((1, 2))))
print(Foo.schema().loads('{"x": [1, 2]}'))

# passes validation (bad)
print(Foo.schema().loads('{"x": "123"}'))


@dataclass
class Bar(dataclasses_json.DataClassJsonMixin):
    y: list[int]


print(Bar.schema().dumps(Bar([1, 2])))
print(Bar.schema().loads('{"y": [1, 2]}'))
try:
    print(Bar.schema().loads('{"y": "123"}'))
except Exception as e:
    print(e)


@dataclass
class Baz(dataclasses_json.DataClassJsonMixin):
    z: tuple[int, ...]


print(Baz.schema().dumps(Baz((1, 2))))
print(Baz.schema().loads('{"z": [1, 2]}'))
try:
    print(Baz.schema().loads('{"z": "123"}'))
except Exception as e:
    print(e)

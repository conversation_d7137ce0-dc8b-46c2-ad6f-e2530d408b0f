"""A prototype of a new pending completions algorithm in Python.

(This is currently out of date).
"""

from base.datasets.user_event import ContentChange, TextEditEvent
from dataclasses import dataclass
from typing import Optional
from base.ranges import Char<PERSON>ange


@dataclass
class CompletionDetails:
    text: str
    suffix_replacement_text: str
    skipped_suffix: str
    crange: Char<PERSON><PERSON><PERSON>


@dataclass
class PendingCompletionDetails:
    file_path: str
    completion: CompletionDetails
    consumed: int
    skipped: int


@dataclass
class NormalizedChange:
    range_length: int
    range_offset: int
    text: str


@dataclass
class PendingCompletion:
    """A class to manage pending completions."""

    pending_completion: PendingCompletionDetails
    accepted: Optional[bool] = None

    def finish(self, accepted: bool):
        self.accepted = accepted

    def _normalize_change(self, change: ContentChange) -> NormalizedChange | None:
        range_length = len(change.crange)
        range_offset = change.crange.start
        text = change.text

        completion = self.pending_completion.completion
        consumed = self.pending_completion.consumed
        skipped = self.pending_completion.skipped

        if change.crange.start < completion.crange.start:
            range_diff = completion.crange.start - change.crange.start
            if range_diff > range_length:
                return None
            range_length -= range_diff
            range_offset += range_diff
            text = text[range_diff:]

        consumed_end = completion.crange.start + consumed
        if change.crange.start > consumed_end:
            range_diff = range_offset - consumed_end
            if skipped + range_diff > len(completion.skipped_suffix):
                return None
            range_length += range_diff
            range_offset -= range_diff
            text = completion.skipped_suffix[skipped : skipped + range_diff] + text

        return NormalizedChange(range_length, range_offset, text)

    def on_text_document_change(self, event: TextEditEvent):
        if self.pending_completion.file_path != event.file_path:
            return

        if len(event.content_changes) == 0:
            return

        if len(event.content_changes) > 1:
            self.finish(accepted=False)
            return

        normalized_change = self._normalize_change(event.content_changes[0])
        if normalized_change is None:
            self.finish(accepted=False)
            return

        completion = self.pending_completion.completion
        consumed = self.pending_completion.consumed
        skipped = self.pending_completion.skipped

        # There are two cases here:
        # 1. Some deletions are before the cursor: we "unconsume" the completion
        # 2. Some deletions are after the cursor: we "skip" the skipped suffix
        # So we first need to check that (1) and (2) are nonnegative, (2) is no more
        # than the skipped suffix, and that (1) would get added back by the change.
        # Note that (1) is already guaranteed to be less than the consumed
        # by _normalize_change.
        range_end = normalized_change.range_offset + normalized_change.range_length
        cursor_position = completion.crange.start + consumed
        if (
            range_end < cursor_position
            or normalized_change.range_offset > cursor_position
        ):
            self.finish(accepted=False)
            return

        new_skipped = range_end - cursor_position
        unconsumed = cursor_position - normalized_change.range_offset
        if new_skipped + skipped > len(completion.skipped_suffix) or unconsumed > len(
            normalized_change.text
        ):
            self.finish(accepted=False)
            return

        skipped += new_skipped
        consumed -= unconsumed

        full_completion = completion.text + completion.suffix_replacement_text

        # The change can extend past the completion even when accepting, since
        # VSCode can add closing brackets/braces/etc., so we only look at the matching
        # characters.
        next_text = full_completion[consumed : consumed + len(normalized_change.text)]
        if normalized_change.text[: len(next_text)] != next_text:
            self.finish(accepted=False)
            return

        self.pending_completion.consumed = consumed + len(next_text)
        self.pending_completion.skipped = skipped

        # There's one last possible case, if the suffix_replacement_text and skipped_suffix,
        # share the same end characters, and we have consumed everything else, the completion
        # should be counted as "consumed", but we may not terminate.
        # Currently, it seems like VSCode will keep those characters in, despite technically being
        # redundant (e.g. ")" -> "foo)"), so it's not a big issue.
        if self.pending_completion.consumed == len(completion.text) + len(
            completion.suffix_replacement_text
        ):
            if self.pending_completion.skipped == len(completion.skipped_suffix):
                self.finish(accepted=True)
            else:
                self.finish(accepted=False)

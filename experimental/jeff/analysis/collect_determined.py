"""Collect configuration data from Determined experiments.

Note that this will come from the runtime config, so, e.g.
hyperparameters.task.dataset.val instead of hyperparameters.task.dataset


Usage:
python experimental/jeff/analysis/collect_determined.py \
    --exp-ids 2424 2425 2426 --key-paths "data.augment.eval_outdir"
echo 2424 2425 2426 | \
    xargs python experimental/jeff/analysis/collect_determined.py \
    --key-paths "data.augment.eval_outdir" --exp-ids
"""

import argparse
import subprocess
import yaml

DET_MASTER = "https://determined.gcp-us1.r.augmentcode.com"


def process(config: dict, key_paths: list[list[str]]) -> list[str]:
    results = []
    for key_path in key_paths:
        cur = config
        for key in key_path:
            cur = cur[key]
        results.append(str(cur))
    return results


def collect(key_paths: list[list[str]], exp_ids: list[int]) -> list[list[str]]:
    results = []
    for exp_id in exp_ids:
        proc = subprocess.run(
            f"det -m {DET_MASTER} experiment config {exp_id}",
            shell=True,
            check=False,
            capture_output=True,
            text=True,
        )
        config = yaml.safe_load(proc.stdout)
        # print(config)
        results.append(process(config, key_paths))
    return results


def main():
    parser = argparse.ArgumentParser(
        description="Collect configuration data from Determined experiments"
    )
    parser.add_argument(
        "--exp-ids",
        type=int,
        nargs="+",
        required=True,
        help="List of experiment IDs to process",
    )
    parser.add_argument(
        "--key-paths",
        type=str,
        nargs="+",
        default=["data.augment.eval_outdir"],
        help="List of dot-separated key paths to extract (default: 'data.augment.eval_outdir')",
    )

    args = parser.parse_args()

    # Convert dot-notation key paths to lists
    key_paths = [path.split(".") for path in args.key_paths]

    # Collect results
    results = collect(key_paths, args.exp_ids)

    print(",".join(["exp_id"] + args.key_paths))
    for exp_id, result in zip(args.exp_ids, results):
        print(",".join([str(exp_id)] + result))


if __name__ == "__main__":
    main()

import argparse
from pathlib import Path

import pandas as pd


def make_2024_05_01(records_df: pd.DataFrame):
    return records_df[
        records_df["dataset"].isin(
            ["2024-05-01-v1.0/dogfood", "2024-06-01-v1.0/dogfood"]
        )
    ]


def make_2024_07_01(records_df: pd.DataFrame):
    return records_df[
        records_df["dataset"].isin(
            [
                "2024-07-01-v1.2/dogfood-shard",
                "2024-08-01-v1.2/dogfood-shard",
                "2024-09-01-v1.3/dogfood-shard",
                "2024-10-01-8d-v1.3/dogfood-shard",
            ]
        )
        # Missing some runs
        & ~records_df["model"].isin(["eldenv5-1-15b"])
    ]


def make_2024_11_01(records_df: pd.DataFrame):
    return records_df[
        records_df["dataset"].isin(
            [
                "2024-10-28-14d-v1.3/dogfood-shard",
            ]
        )
    ]


def make_2024_12_01(records_df: pd.DataFrame):
    return records_df[
        records_df["dataset"].isin(
            [
                "2024-11-17-18d-v1.3/dogfood-shard",
            ]
        )
    ]


def make_2025_01_01(records_df: pd.DataFrame):
    return records_df[
        records_df["dataset"].isin(
            [
                "2025-01-15-14d-v1.4/dogfood-shard",
            ]
        )
    ]


def make_2025_03_01(records_df: pd.DataFrame):
    return records_df[
        records_df["dataset"].isin(
            [
                "2025-03-24-7d-v1.5/dogfood-shard",
            ]
        )
    ]


def make_2025_04_01(records_df: pd.DataFrame):
    return records_df[
        records_df["dataset"].isin(
            [
                "2025-04-12-5d-v1.6/dogfood-shard",
            ]
        )
    ]


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=Path, required=True)
    parser.add_argument("--output", type=Path, required=True)
    args = parser.parse_args()

    records_df = pd.read_csv(args.input)

    make_2024_05_01(records_df).to_csv(
        args.output / "records-2024-05-01.csv", index=False
    )
    make_2024_07_01(records_df).to_csv(
        args.output / "records-2024-07-01.csv", index=False
    )
    make_2024_11_01(records_df).to_csv(
        args.output / "records-2024-11-01.csv", index=False
    )
    make_2024_12_01(records_df).to_csv(
        args.output / "records-2024-12-01.csv", index=False
    )
    make_2025_01_01(records_df).to_csv(
        args.output / "records-2025-01-01.csv", index=False
    )
    make_2025_03_01(records_df).to_csv(
        args.output / "records-2025-03-01.csv", index=False
    )
    make_2025_04_01(records_df).to_csv(
        args.output / "records-2025-04-01.csv", index=False
    )


if __name__ == "__main__":
    main()

"""Collect configuration data from Determined experiments and format it as records.

Usage:
python experimental/jeff/analysis/hindsight/collect_to_records.py --exp_ids 2424 2425 2426
echo 2424 2425 2426 | \
    xargs python experimental/jeff/analysis/hindsight/collect_to_records.py --exp_ids
"""

import argparse
import sys
import pandas as pd

from experimental.jeff.analysis.collect_determined import collect, process


def collect_to_records_df(
    exp_ids: list[int],
    link_format: str,
    output_path_key: str,
    dataset_key: str,
    tenant_key: str,
    model_key: str,
) -> pd.DataFrame:
    keys = [output_path_key, dataset_key, tenant_key, model_key]
    key_paths = [path.split(".") for path in keys]

    # Collect results
    results = collect(key_paths, exp_ids)

    records = []

    for exp_id, (output_path, dataset, tenant, model) in zip(exp_ids, results):
        link = link_format.format(exp_id)
        override_model = False
        records.append(
            ["/".join([dataset, tenant]), model, override_model, link, output_path]
        )

    df = pd.DataFrame(
        records, columns=["dataset", "model", "override_model", "link", "output_path"]
    )
    return df


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--exp_ids",
        type=int,
        nargs="+",
        required=True,
        help="List of experiment IDs to process",
    )
    parser.add_argument(
        "--output_path_key",
        type=str,
        default="data.augment.eval_outdir",
        help="Dot-separated key path to the output path in the experiment config",
    )
    parser.add_argument(
        "--dataset_key",
        type=str,
        default="hyperparameters.task.dataset.val",
        help="Dot-separated key path to the dataset in the experiment config",
    )
    parser.add_argument(
        "--tenant_key",
        type=str,
        default="hyperparameters.task.tenant_name.val",
        help="Dot-separated key path to the tenant in the experiment config",
    )
    parser.add_argument(
        "--model_key",
        type=str,
        default="hyperparameters.system.model_name.val",
        help="Dot-separated key path to the model in the experiment config",
    )
    parser.add_argument(
        "--link_format",
        type=str,
        default="https://determined.gcp-us1.r.augmentcode.com/det/experiments/{}",
        help="Format string for creating links to experiments",
    )
    args = parser.parse_args()
    df = collect_to_records_df(
        exp_ids=args.exp_ids,
        link_format=args.link_format,
        output_path_key=args.output_path_key,
        dataset_key=args.dataset_key,
        tenant_key=args.tenant_key,
        model_key=args.model_key,
    )
    df.to_csv(sys.stdout, index=False)


if __name__ == "__main__":
    main()

import argparse
import subprocess
import os
from pathlib import Path


def run_bazel_command(target, output_dir):
    command = f"bazel run //services/deploy:{target} -- dump"
    output_file = Path(output_dir) / f"{target}.json"
    try:
        with output_file.open("w") as f:
            subprocess.run(
                command,
                shell=True,
                check=True,
                stdout=f,
                stderr=subprocess.PIPE,
                text=True,
            )
        print(f"Output for {target} written to {output_file}")
    except subprocess.CalledProcessError as e:
        print(f"Error running command for {target}: {e}")
        print(f"Error output: {e.stderr}")


def main():
    parser = argparse.ArgumentParser(
        description="Run Bazel commands and output results to JSON files."
    )
    parser.add_argument("targets", nargs="+", help="List of targets to run")
    parser.add_argument(
        "--output-dir", default=".", help="Directory to output JSON files"
    )

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    for target in args.targets:
        run_bazel_command(target, args.output_dir)


if __name__ == "__main__":
    main()

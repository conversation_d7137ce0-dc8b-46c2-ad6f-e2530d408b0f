languages_file="/home/<USER>/augment/base/languages/languages.yaml"
extensions=$(python -c "import yaml
import re
with open('$languages_file') as f:
  print('|'.join(re.escape(e) for k,v in yaml.safe_load(f).items() for e in v['extensions']) + '$')
")

# You can clone a repo, latest commit only + no actual files via:
# git clone --no-checkout --depth=1 <repo>

# ls-files might also show things in index but not in the working tree.
# git ls-files --format='%(objectsize) %(path)'
git ls-tree -r --full-tree --format='%(objectsize) %(path)' HEAD |

# filter by language extensions
grep -E "$extensions" |

# sort by size
sort -nr |

### Post-processing, alternatively, comment out and pipe to a file:
# e.g. > asdf.txt

# print num files >= 100k and 512k bytes
awk '$1>=102400{c++} $1>=512000{d++} END{print (c+0)/NR, (d+0)/NR, c+0, d+0, NR}'

# OR, just get num files
# wc -l

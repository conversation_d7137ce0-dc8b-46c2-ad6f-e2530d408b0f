import argparse
import copy
import logging
from dataclasses import dataclass
from dataclasses_json import DataClassJsonMixin
from pathlib import Path
from typing import TypeVar


import yaml

logger = logging.getLogger(__name__)


def combine_dict(a: dict, b: dict, override: bool = False):
    """Recursively adds items from b into a."""
    for k, v in b.items():
        if k not in a:
            a[k] = copy.deepcopy(v)
            continue

        if isinstance(a[k], dict) and isinstance(v, dict):
            combine_dict(a[k], v)
            continue

        if override:
            a[k] = copy.deepcopy(v)
            continue

        if a[k] != v:
            raise ValueError(f"Conflicting keys {k} in {a} and {b}")
    return a


T = TypeVar("T")


def get_combinations(choices: list[list[T]]) -> list[list[T]]:
    """Get all combinations of the choices."""
    combos = [[]]

    # Fast path for zero combinations
    if any(len(choice) == 0 for choice in choices):
        return []

    for choice_list in choices:
        new_combos = []
        for combo in combos:
            for choice in choice_list:
                new_combos.append(combo + [choice])
        combos = new_combos

    return combos


@dataclass
class ConfigPiece(DataClassJsonMixin):
    """Represents a reusable piece of configuration."""

    name: str
    """The name of the config piece."""

    config_type: str
    """The type of the config piece."""

    config_path: str
    """The path to the config piece, relative to current directory."""


@dataclass
class ConfigChoice(DataClassJsonMixin):
    """Represents a choice of config pieces."""

    config_type: str
    """The type of the config piece."""

    names: list[str]
    """The names of the config piece."""

    include_in_full_name: bool | None = None
    """Whether to include the name in the full name.

    If None, then True iff len(names) > 1.
    """

    override: bool = False
    """Whether to override the config piece if it already exists."""


def determined_postprocess(
    config: dict,
    full_name_list: list[str],
):
    """Adds the determined name to the config."""
    full_name = ", ".join(full_name_list)
    config.setdefault("determined", {}).setdefault("name", full_name)


def postprocess(config: dict, full_name_list: list[str], postprocess: str):
    if postprocess == "determined":
        determined_postprocess(config, full_name_list)
    else:
        raise ValueError(f"Unknown postprocess type {postprocess}")


@dataclass
class ConfigCombination(DataClassJsonMixin):
    """A series of config choices."""

    choices: list[ConfigChoice]
    """The choices."""

    postprocess: str = "determined"
    """How to postprocess the resulting configs."""


def get_configs(
    pieces_dir: Path, pieces: list[ConfigPiece], combinations: list[ConfigCombination]
) -> dict[str, dict]:
    """Get the configs."""
    piece_map = {}
    for piece in pieces:
        with open(pieces_dir / piece.config_path, "r") as f:
            piece_config = yaml.safe_load(f)
            piece_map[(piece.config_type, piece.name)] = piece_config

    configs = {}
    for combination in combinations:
        names_choices = [choice.names for choice in combination.choices]
        names_combos = get_combinations(names_choices)
        for names in names_combos:
            config = {}
            full_name_list = []
            for choice, name in zip(combination.choices, names):
                piece_config = piece_map[(choice.config_type, name)]
                combine_dict(config, piece_config, override=choice.override)

                include_in_full_name = (
                    choice.include_in_full_name
                    if choice.include_in_full_name is not None
                    else len(choice.names) > 1
                )
                if include_in_full_name:
                    full_name_list.append(name)

            postprocess(config, full_name_list, combination.postprocess)
            full_name = "-".join(full_name_list)
            if full_name in configs:
                raise ValueError(f"Duplicate config name {full_name}")
            configs[full_name] = config

    return configs


def parse_args():
    default_root_dir = Path(__file__).parent / "evals"
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--root_dir",
        type=Path,
        default=default_root_dir,
        help="The root directory of the config combinator.",
    )
    parser.add_argument(
        "--pieces_file",
        type=Path,
        default="pieces.yml",
        help="A yaml file containing a list of config pieces, relative to root_dir.",
    )
    parser.add_argument(
        "--combinations_file",
        type=Path,
        default="combinations.yml",
        help="A yaml file containing a list of config combinations, relative to root_dir.",
    )
    parser.add_argument(
        "--output_dir",
        type=Path,
        default="output",
        help="The directory to output the generated configs, relative to root_dir.",
    )

    return parser.parse_args()


def sync_configs(output_dir: Path, configs: dict[str, dict]):
    """Write configs to output_dir, overriding and removing any existing files."""
    output_dir.mkdir(parents=True, exist_ok=True)

    # Write files to output
    for name, config in configs.items():
        with open(output_dir / f"{name}.yml", "w") as f:
            yaml.dump(config, f)

    # Remove files that are no longer in configs
    for file in output_dir.iterdir():
        if file.suffix == ".yml" and file.stem not in configs:
            file.unlink()


def main():
    logging.basicConfig(level=logging.INFO)

    args = parse_args()
    with open(args.root_dir / args.pieces_file, "r") as f:
        pieces = yaml.safe_load(f)
    with open(args.root_dir / args.combinations_file, "r") as f:
        combinations = yaml.safe_load(f)

    pieces = [ConfigPiece.schema().load(piece) for piece in pieces]
    combinations = [
        ConfigCombination.schema().load(combination) for combination in combinations
    ]
    logger.info(f"Loaded {len(pieces)} pieces and {len(combinations)} combinations.")

    configs = get_configs(args.root_dir, pieces, combinations)
    logger.info(f"Generated {len(configs)} configs.")

    output_dir = args.root_dir / args.output_dir
    logger.info(f"Saving configs to {output_dir}")
    sync_configs(output_dir, configs)


if __name__ == "__main__":
    main()

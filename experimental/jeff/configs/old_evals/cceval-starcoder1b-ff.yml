system:
    name: basic_rag

    experimental:
      remove_suffix: false
      retriever_top_k: 25
      trim_on_dedent: false

    model:
        name: starcoderbase_1b_fastforward
        prompt:
            max_prefix_tokens: 2048
            max_suffix_tokens: 2048
            max_prompt_tokens: 8142
            retrieval_layout_style: "comment2"
            max_number_chunks: 5

    retriever:
        name: bm25
        # name: null
        chunker:
            name: line_level
            max_lines_per_chunk: 10
        max_query_lines: 10

    generation_options:
        temperature: 0
        top_k: 0
        top_p: 0
        max_generated_tokens: 50

task:
    name: cceval
    limit: 100

podspec: A5000.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: CCEval, Starcoder 1B ff, w fim, bm25 retrieval
  project: jeff-eval
  workspace: Dev

system:
    name: remote_completion

    # model_name: roguesl-farpref-16B-eth6-c
    # model_name: roguesl-v2-16b-eth6-04-1
    model_name: eldenv4-0b-15b

    client:
        url: https://dev-jeff.us-central.api.augmentcode.com

    retriever:
        disable_extension_filtering: true
        wait_indexing_retry_sleep_secs: 4.0
        wait_indexing_retry_count: 256
        # disable_wait_indexing: True


task:
    name: cceval
    limit: 100

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: CCEval, eldenv4-0b-15b, remote, dev
  project: jeff-eval
  workspace: Dev

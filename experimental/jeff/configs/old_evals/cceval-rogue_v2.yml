determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: cceval, rogue v2, w/ retrieval
  project: jeff-eval
  workspace: Dev
podspec: A40.yaml
system:
  experimental:
    remove_suffix: false
    retriever_top_k: 25
    trim_on_dedent: false
    trim_on_max_lines: null
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    checkpoint_path: roguesl/16b_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015
    name: rogue_statelesscache
    prompt:
      component_order:
        - prefix
        - suffix
        - retrieval
        - nearby_prefix
      context_quant_token_len: 50
      max_prefix_tokens: 1030
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      nearby_prefix_token_len: 250
      nearby_prefix_token_overlap: 0
      nearby_suffix_token_len: 0
      nearby_suffix_token_overlap: 0
      use_far_prefix_token: true
  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    query_formatter:
      add_path: true
      max_tokens: 1023
      name: ethanol6_query
    scorer:
      checkpoint_path: ethanol/ethanol6-04.1
      name: ethanol
  fim_gen_mode: interactive

task:
  name: cceval
  limit: 100

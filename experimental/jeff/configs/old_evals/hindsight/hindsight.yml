# An example config for hindsight with roguesl_v2, eth616
system:
  client:
    url: https://dogfood.api.augmentcode.com
    # url: https://staging-shard-0.api.augmentcode.com
    timeout: 60
    retry_count: 5
    retry_sleep: 5.0
  # model_name: eldenv3-15b
  # model_name: eldenv5-1-15b
  # model_name: eldenv4-0c-15b
  # model_name: eldenv4-15b
  model_name: eldenv4-3-15b
  name: remote_completion
  retriever:
    disable_extension_filtering: true
    wait_indexing_retry_count: 256
    wait_indexing_retry_sleep_secs: 4
    # warn_on_indexing_timeout: true
    # disable_retrieval: true
  completion:
    retry_count: 8
    # warn_on_unknown_blobs: true
    # disable_recency: true

task:
  name: hindsight
  # dataset: 2024-08-01-v1.2
  # tenant_name: dogfood-shard
  # blob_limit: 10000
  dataset: 2024-06-01-v1.0
  tenant_name: dogfood
  blob_limit: 9000
  # Required to query GCS for blobs.
  # Populated by augment.dai_gcp_service_accounts below.
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json

augment:
  # Mapping of secret name to mountpoint *when not running with --local*.
  dai_gcp_service_accounts:
    - secret: aug-prod-cw-ri-importer  # pragma: allowlist secret
      mountpoint: /mnt/augment/secrets/cw-ri-importer

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: hindsight, 2024-06-01-v1.0/dogfood, eldenv4-3-15b, remote, dogfood
  project: playground
  workspace: Dev

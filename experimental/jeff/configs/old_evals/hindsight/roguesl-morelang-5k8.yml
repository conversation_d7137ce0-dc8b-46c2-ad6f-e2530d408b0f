determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: hindsight, 2024-03-15-v0.5/aitutor-pareto, roguesl/16b_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015, ethanol616, 5k8
  project: jeff-eval
  workspace: Dev
podspec: A40.yaml
system:
  experimental:
    remove_suffix: false
    retriever_top_k: 25
    trim_on_dedent: false
  fim_gen_mode: evaluation
  generation_options:
    max_generated_tokens: 280
  model:
    checkpoint_path: roguesl/16b_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015
    name: rogue_statelesscache
    prompt:
      component_order:
        - prefix
        - suffix
        - retrieval
        - nearby_prefix
      context_quant_token_len: 50
      max_prefix_tokens: 1030
      max_prompt_tokens: 5816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      nearby_prefix_token_len: 250
      nearby_prefix_token_overlap: 0
      nearby_suffix_token_len: 0
      nearby_suffix_token_overlap: 0
      use_far_prefix_token: true
  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9
    scorer:
      checkpoint_path: ethanol/ethanol6-16.1
      name: ethanol

task:
  name: hindsight
  dataset: 2024-03-15-v0.5
  tenant_name: aitutor-pareto
  dataset_base_dir: /mnt/efs/augment/user/jeff/hindsight
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json
  limit: 1000

augment:
  # Mapping of secret name to mountpoint *when not running with --local*.
  dai_gcp_service_accounts:
    - secret: aug-prod-cw-ri-importer  # pragma: allowlist secret
      mountpoint: /mnt/augment/secrets/cw-ri-importer

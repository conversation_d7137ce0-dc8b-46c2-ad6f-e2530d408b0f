augment:
  gpu_count: 2
  project_group: finetuning
podspec: 2xH100.yaml
system:
  formatter_config:
    apportionment_config:
      input_fraction: 0.3333333333333333
      max_content_len: 6401
      max_path_tokens: 50
      per_retriever_max_tokens:
        dense_signature: 1024
        recency_retriever: 1024
      prefix_fraction: 0.75
    name: ender
    prompt_formatter_config:
      component_order:
        - path
        - prefix
        - retrieval
        - signature
        - nearby_prefix
        - suffix
      signature_chunk_origin: dense_signature
      stateless_caching_config:
        nearby_prefix_token_len: 512
        quantize_char_len: 250
        quantize_token_len: 64
  generation_options:
    max_generated_tokens: 256
  model:
    checkpoint_path: /mnt/efs/augment/checkpoints/jeff/eldenv4-plus-imports-v2
    model_parallel_size: 2
    name: fastbackward
    seq_length: 6401
  tokenizer: starcoder2

task:
  name: hindsight
  # Required to query GCS for blobs.
  # Populated by augment.dai_gcp_service_accounts below.
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json

augment:
  # Mapping of secret name to mountpoint *when not running with --local*.
  dai_gcp_service_accounts:
    - secret: aug-prod-cw-ri-importer  # pragma: allowlist secret
      mountpoint: /mnt/augment/secrets/cw-ri-importer

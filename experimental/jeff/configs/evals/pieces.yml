# Determined config
- name: determined
  config_type: determined
  config_path: determined/determined.yml

# Remote configs
- name: dogfood
  config_type: remote
  config_path: remote/endpoints/dogfood.yml
- name: dogfood-shard
  config_type: remote
  config_path: remote/endpoints/dogfood-shard.yml

# Remote models
- name: eldenv3-15b
  config_type: remote-model
  config_path: remote/models/eldenv3-15b.yml
- name: eldenv4-0c-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-0c-15b.yml
- name: eldenv4-0d-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-0d-15b.yml
- name: eldenv4-0e-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-0e-15b.yml
- name: eldenv4-0f-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-0f-15b.yml
- name: eldenv4-3-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-3-15b.yml
- name: eldenv4-4a-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-4a-15b.yml
- name: eldenv4-4b-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-4b-15b.yml
- name: eldenv4-15b
  config_type: remote-model
  config_path: remote/models/eldenv4-15b.yml
- name: eldenv5-1-15b
  config_type: remote-model
  config_path: remote/models/eldenv5-1-15b.yml
- name: eldenv6-1-15b
  config_type: remote-model
  config_path: remote/models/eldenv6-1-15b.yml
- name: eldenv6-15b
  config_type: remote-model
  config_path: remote/models/eldenv6-15b.yml
- name: eldenv7-0-15b
  config_type: remote-model
  config_path: remote/models/eldenv7-0-15b.yml
- name: qweldenv1-14b
  config_type: remote-model
  config_path: remote/models/qweldenv1-14b.yml
- name: qweldenv1-1-14b
  config_type: remote-model
  config_path: remote/models/qweldenv1-1-14b.yml
- name: qweldenv2-14b
  config_type: remote-model
  config_path: remote/models/qweldenv2-14b.yml
- name: qweldenv2-1-14b
  config_type: remote-model
  config_path: remote/models/qweldenv2-1-14b.yml
- name: qweldenv3-1-14b
  config_type: remote-model
  config_path: remote/models/qweldenv3-1-14b.yml
- name: qweldenv3-2-14b
  config_type: remote-model
  config_path: remote/models/qweldenv3-2-14b.yml

# Elden systems
- name: prod-elden
  config_type: system
  config_path: elden/systems/prod-elden.yml

# Elden models
- name: eldenv4-fbw
  config_type: model
  config_path: elden/models/eldenv4-fbw.yml
- name: eldenv4-w-imports-v2-fbw
  config_type: model
  config_path: elden/models/eldenv4-w-imports-v2-fbw.yml

# Elden retrievers
- name: methanol
  config_type: dense-retriever
  config_path: elden/retrievers/methanol.yml
- name: starethanol
  config_type: signature-retriever
  config_path: elden/retrievers/starethanol.yml

# Tasks
- name: hindsight
  config_type: task
  config_path: tasks/hindsight/hindsight.yml

# Hindsight datasets
- name: 2024-05-01-v1.0-dogfood
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-05-01-v1.0-dogfood.yml
- name: 2024-06-01-v1.0-dogfood
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-06-01-v1.0-dogfood.yml
- name: 2024-07-01-v1.2-dogfood
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-07-01-v1.2-dogfood.yml
- name: 2024-07-01-v1.2-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-07-01-v1.2-dogfood-shard.yml
- name: 2024-08-01-v1.2-dogfood
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-08-01-v1.2-dogfood.yml
- name: 2024-08-01-v1.2-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-08-01-v1.2-dogfood-shard.yml
- name: 2024-09-01-v1.3-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-09-01-v1.3-dogfood-shard.yml
- name: 2024-10-01-8d-v1.3-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-10-01-8d-v1.3-dogfood-shard.yml
- name: 2024-10-28-14d-v1.3-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-10-28-14d-v1.3-dogfood-shard.yml
- name: 2024-11-17-18d-v1.3-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2024-11-17-18d-v1.3-dogfood-shard.yml
- name: 2025-01-15-14d-v1.4-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2025-01-15-14d-v1.4-dogfood-shard.yml
- name: 2025-03-24-7d-v1.5-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2025-03-24-7d-v1.5-dogfood-shard.yml
- name: 2025-04-12-5d-v1.6-dogfood-shard
  config_type: dataset
  config_path: tasks/hindsight/datasets/2025-04-12-5d-v1.6-dogfood-shard.yml

# CCEval datasets
- name: cceval
  config_type: task
  config_path: tasks/cceval/datasets/cceval.yml
- name: cceval-fim-imports-shuffle
  config_type: task
  config_path: tasks/cceval/datasets/cceval-fim-imports-shuffle.yml
- name: cceval-fim-dropout
  config_type: task
  config_path: tasks/cceval/datasets/cceval-fim-dropout.yml

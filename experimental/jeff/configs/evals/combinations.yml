# Hindsight, remote, dogfood
- choices:
  - config_type: determined
    names:
      - determined
  - config_type: remote
    include_in_full_name: true
    names:
      - dogfood
  - config_type: remote-model
    names:
      - eldenv3-15b
      - eldenv4-0c-15b
      - eldenv4-0d-15b
      - eldenv4-0e-15b
      - eldenv4-0f-15b
      - eldenv4-3-15b
      - eldenv4-4a-15b
      - eldenv4-4b-15b
      - eldenv4-15b
      - eldenv5-1-15b
      - eldenv6-1-15b
      - eldenv6-15b
      - eldenv7-0-15b
  - config_type: task
    include_in_full_name: true
    names:
      - hindsight
  - config_type: dataset
    names:
      - 2024-05-01-v1.0-dogfood
      - 2024-06-01-v1.0-dogfood
      - 2024-07-01-v1.2-dogfood
      - 2024-08-01-v1.2-dogfood

# Hindsight, remote, dogfood-shard
- choices:
  - config_type: determined
    names:
      - determined
  - config_type: remote
    include_in_full_name: true
    names:
      - dogfood-shard
  - config_type: remote-model
    names:
      - eldenv3-15b
      - eldenv4-0c-15b
      # - eldenv4-0d-15b
      # - eldenv4-0e-15b
      # - eldenv4-0f-15b
      # - eldenv4-3-15b
      # - eldenv4-4a-15b
      # - eldenv4-4b-15b
      - eldenv4-15b
      # - eldenv5-1-15b
      # - eldenv6-1-15b
      # - eldenv6-15b
      # - eldenv7-0-15b
      # - qweldenv1-14b
      - qweldenv1-1-14b
      # - qweldenv2-14b
      - qweldenv2-1-14b
      - qweldenv3-1-14b
      - qweldenv3-2-14b
  - config_type: task
    include_in_full_name: true
    names:
      - hindsight
  - config_type: dataset
    names:
      - 2024-07-01-v1.2-dogfood-shard
      - 2024-08-01-v1.2-dogfood-shard
      - 2024-09-01-v1.3-dogfood-shard
      - 2024-10-01-8d-v1.3-dogfood-shard
      - 2024-10-28-14d-v1.3-dogfood-shard
      # - 2024-11-17-18d-v1.3-dogfood-shard
      - 2025-01-15-14d-v1.4-dogfood-shard
      - 2025-03-24-7d-v1.5-dogfood-shard
      - 2025-04-12-5d-v1.6-dogfood-shard

# cceval, remote
- choices:
  - config_type: determined
    names:
      - determined
  - config_type: remote
    names:
      # - dogfood
      - dogfood-shard
    include_in_full_name: true
  - config_type: remote-model
    names:
      - eldenv3-15b
      - eldenv4-0c-15b
      - eldenv4-0d-15b
      - eldenv4-0e-15b
      - eldenv4-0f-15b
      - eldenv4-3-15b
      - eldenv4-4a-15b
      - eldenv4-15b
      - eldenv5-1-15b
      - eldenv6-15b
      - eldenv6-1-15b
      - eldenv7-0-15b
  - config_type: task
    include_in_full_name: true
    names:
      - cceval

# elden, cceval
- choices:
  - config_type: determined
    names:
      - determined
  - config_type: system
    include_in_full_name: true
    names:
      - prod-elden
  - config_type: dense-retriever
    include_in_full_name: false
    names:
      - methanol
  - config_type: signature-retriever
    include_in_full_name: false
    names:
      - starethanol
  - config_type: model
    names:
      - eldenv4-fbw
      - eldenv4-w-imports-v2-fbw
  - config_type: task
    names:
      - cceval-fim-imports-shuffle
      - cceval-fim-dropout
      - cceval

# elden, hindsight
- choices:
  - config_type: determined
    names:
      - determined
  - config_type: system
    include_in_full_name: true
    names:
      - prod-elden
  - config_type: dense-retriever
    include_in_full_name: false
    names:
      - methanol
  - config_type: signature-retriever
    include_in_full_name: false
    names:
      - starethanol
  - config_type: model
    names:
      - eldenv4-fbw
      - eldenv4-w-imports-v2-fbw
  - config_type: task
    include_in_full_name: true
    names:
      - hindsight
  - config_type: dataset
    names:
      - 2024-05-01-v1.0-dogfood
      - 2024-06-01-v1.0-dogfood
      - 2024-07-01-v1.2-dogfood
      - 2024-08-01-v1.2-dogfood
      - 2024-07-01-v1.2-dogfood-shard
      - 2024-08-01-v1.2-dogfood-shard
      - 2024-09-01-v1.3-dogfood-shard
      - 2024-10-01-8d-v1.3-dogfood-shard


const fs = require("fs");
const { isUtf8 } = require("node:buffer");


function main() {

    const filename = "expanding_file.txt";
    const file = fs.readFileSync(filename);


    console.log("File length = ",file.length);

    const isUtf = isUtf8(file);

    console.log("Is UTF = ",isUtf);

    const stringContent = file.toString();
    console.log("String content = ",stringContent.length);

    const payload = {
        t: stringContent
    };
    console.log("Payload length = ",JSON.stringify(payload).length);

}

main()

# import logging
import nbformat
from base.logging.struct_logging import setup_struct_logging
from nbformat.validator import NotebookValidationError

import json
# logging.getLogger('nbformat').setLevel(logging.CRITICAL)
# logging.basicConfig(level=logging.INFO)

setup_struct_logging()

# nbformat_logger = logging.getLogger('nbformat')
# nbformat_logger.propagate = False  # Prevent propagation to parent loggers
# nbformat_logger.handlers = []      # Remove any handlers
# nbformat_logger.setLevel(logging.CRITICAL)
print("hello world")

# setup logging
nb_version = 4
# content_str = """
# {

# }
# """


content_str = """
{
  "cells": [
    {
      "moogi": "test",
      "cell_type": "code",
      "execution_count": null,
      "metadata": {},
      "outputs": [],
      "source": [
        "print('Hello, world!')"
      ]
    }
  ],
  "metadata": {
    "kernelspec": {
      "display_name": "Python 3",
      "language": "python",
      "name": "python3"
    },
    "language_info": {
      "codemirror_mode": {
        "name": "ipython",
        "version": 3
      },
      "file_extension": ".py",
      "mimetype": "text/x-python",
      "name": "python",
      "nbconvert_exporter": "python",
      "pygments_lexer": "ipython3",
      "version": "3.9.16"
    }
  },
  "nbformat": 4,
  "nbformat_minor": 2
}
"""

try:
    # content = json.loads(content_str)
    content = content_str
    nb = nbformat.reads(content, as_version=nb_version)
    _, nb = nbformat.validator.normalize(nb, version=nb_version)
    nbformat.validate(nb, version=nb_version)
except NotebookValidationError as e:
    # print(e.__dict__)
    print(f"~~~~~{e.message}~~~~")
    print(f"!!!!!!error while parsing notebook {e}!!!!!!")
# pass
# print(f"Error: {e}")
print("after everything")

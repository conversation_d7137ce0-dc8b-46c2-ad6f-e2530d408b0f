import logging

# Setting up basic logging configuration
logging.basicConfig(level=logging.INFO)

# Example documents
documents = [
    {"sub_key": "key1", "content": "some content here"},
    {"sub_key": "key2", "content": "another document content"},
]

# Correct logging example
blob_name = "example_blob"
request_id = "req_123"

# This is the correct logging format
logging.info(
    "embedding result %s for blob_name=%s, request_id=%s",
    [(document["sub_key"], len(document["content"])) for document in documents],
    blob_name,
    request_id,
)

# Now let's simulate an incorrect case to see how it behaves
try:
    # Simulating an incorrect case where we try to pass the list in the wrong position
    logging.info(
        "embedding result %s for blob_name=%s, request_id=%s",
        blob_name,
        request_id,
        [(document["sub_key"], len(document["content"])) for document in documents],
    )
except Exception as e:
    print(f"Error occurred: {e}")

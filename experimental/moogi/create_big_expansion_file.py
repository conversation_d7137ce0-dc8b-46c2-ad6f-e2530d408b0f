## This script will create a file with \u0001 control characters that will expand to a large size when JSON-encoded.
## See incident 13177

"""Create a file filled with control characters (ASCII 1 / \u0001)."""

import argparse


def create_control_char_file(filename, size_kb):
    """Create a file filled with \u0001 control characters."""
    # Calculate size in bytes
    size_bytes = size_kb * 1024

    # Create a byte array filled with ASCII 1 (\u0001)
    control_char = bytes([1])  # ASCII 1 (SOH - Start of Heading)
    content = control_char * size_bytes

    # Write to file in binary mode
    with open(filename, "wb") as f:
        f.write(content)

    print(
        f"Created file '{filename}' with {size_bytes} bytes of ASCII 1 (\\u0001) characters"
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Create a file filled with control characters"
    )
    parser.add_argument("filename", help="Output filename")
    parser.add_argument(
        "--size", type=int, default=384, help="File size in KB (default: 384)"
    )
    args = parser.parse_args()

    create_control_char_file(args.filename, args.size)

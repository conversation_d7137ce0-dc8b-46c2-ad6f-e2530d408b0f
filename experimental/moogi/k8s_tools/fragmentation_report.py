import json
import subprocess
from dataclasses import dataclass
from collections import defaultdict

CONTEXTS = [
    "gke_system-services-prod_us-central1_us-central1-prod",
    "gke_system-services-prod-gsc_us-central1_prod-gsc",
    "gke_system-services-prod_europe-west4_eu-west4-prod",
]

_KUBECTL_BIN = "../k8s_binary/file/kubectl"


def get_kubectl_json(command):
    """Runs a kubectl command and returns the JSON output."""
    result = subprocess.run(command, stdout=subprocess.PIPE, check=True)
    return json.loads(result.stdout)


def get_nodes_in_cluster(context):
    """Get all nodes in a cluster."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "nodes",
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


def get_pods_in_cluster(context):
    """Get all pods in a cluster."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "pods",
        "--all-namespaces",
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


@dataclass
class NodeUsage:
    name: str
    total_gpus: int
    used_gpus: int

    @property
    def free_gpus(self):
        return self.total_gpus - self.used_gpus

    @property
    def utilization_pct(self):
        return (self.used_gpus / self.total_gpus) * 100 if self.total_gpus > 0 else 0


def main():
    """Main function."""
    for context in CONTEXTS:
        print(f"\n{context}")

        # Get all nodes
        nodes_data = get_nodes_in_cluster(context)

        # Filter for H100 nodes
        h100_nodes = {}
        for node in nodes_data["items"]:
            node_name = node["metadata"]["name"]
            gpu_count = int(node["status"]["allocatable"].get("nvidia.com/gpu", "0"))

            # Only track nodes with 8 GPUs (assuming these are H100s)
            if gpu_count == 8:
                h100_nodes[node_name] = NodeUsage(
                    name=node_name, total_gpus=gpu_count, used_gpus=0
                )

        if not h100_nodes:
            print("  No H100 nodes found in this cluster")
            continue

        # Get all pods
        pods_data = get_pods_in_cluster(context)

        # Track GPU usage per node
        for pod in pods_data["items"]:
            # Skip pods not scheduled on H100 nodes
            node_name = pod["spec"].get("nodeName")
            if not node_name or node_name not in h100_nodes:
                continue

            # Count GPUs used by this pod
            containers = pod["spec"]["containers"]
            pod_gpus = sum(
                int(
                    container.get("resources", {})
                    .get("limits", {})
                    .get("nvidia.com/gpu", "0")
                )
                for container in containers
            )

            # Add to node's used GPU count
            h100_nodes[node_name].used_gpus += pod_gpus

        # Print report
        row_format = "{:<50} {:>10} {:>10} {:>10} {:>15}"
        print(
            row_format.format(
                "Node Name", "Total GPUs", "Used GPUs", "Free GPUs", "Utilization %"
            )
        )
        print("-" * 95)

        total_gpus = 0
        used_gpus = 0

        for node in sorted(h100_nodes.values(), key=lambda x: x.name):
            print(
                row_format.format(
                    node.name,
                    node.total_gpus,
                    node.used_gpus,
                    node.free_gpus,
                    f"{node.utilization_pct:.1f}%",
                )
            )
            total_gpus += node.total_gpus
            used_gpus += node.used_gpus

        # Print summary
        print("-" * 95)
        print(
            row_format.format(
                "TOTAL",
                total_gpus,
                used_gpus,
                total_gpus - used_gpus,
                f"{(used_gpus / total_gpus) * 100:.1f}%" if total_gpus > 0 else "0.0%",
            )
        )


if __name__ == "__main__":
    main()

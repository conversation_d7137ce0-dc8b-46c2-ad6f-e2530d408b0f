load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "fragmentation_report",
    srcs = [
        "fragmentation_report.py",
    ],
    data = [
        "@k8s_binary//file:kubectl",
    ],
    deps = [
        "//base/logging:struct_logging",
    ],
)

py_binary(
    name = "oom_pods_report",
    srcs = [
        "oom_pods_report.py",
    ],
    data = [
        "@k8s_binary//file:kubectl",
    ],
    deps = [
        "//base/logging:struct_logging",
    ],
)

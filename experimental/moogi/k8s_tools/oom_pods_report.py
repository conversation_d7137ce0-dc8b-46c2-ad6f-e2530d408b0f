#!/usr/bin/env python3
"""Report on pods that were recently killed due to OOM across clusters."""

import json
import subprocess
import argparse
import sys
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Dict, Any
from collections import defaultdict

# Use the same kubectl binary path as in other k8s tools
_KUBECTL_BIN = "../k8s_binary/file/kubectl"

# Default contexts from gpu_deployment_report.py
CONTEXTS = [
    "gke_system-services-prod_us-central1_us-central1-prod",
    "gke_system-services-prod-gsc_us-central1_prod-gsc",
    "gke_system-services-prod_europe-west4_eu-west4-prod",
]


def get_kubectl_json(command):
    """Runs a kubectl command and returns the JSON output."""
    try:
        result = subprocess.run(
            command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
        )
        return json.loads(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error executing kubectl command: {e}")
        print(f"Command output: {e.stderr.decode()}")
        return {"items": []}
    except json.JSONDecodeError:
        print("Error parsing JSON output from kubectl")
        return {"items": []}


def get_pods_in_namespace(context, namespace):
    """Get all pods in a specific namespace."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "pods",
        "-n",
        namespace,
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


def get_pods_in_all_namespaces(context):
    """Get all pods across all namespaces."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "pods",
        "--all-namespaces",
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


def get_namespaces(context):
    """Get all namespaces in the cluster."""
    command = [
        _KUBECTL_BIN,
        "--context",
        context,
        "get",
        "namespaces",
        "-o",
        "json",
    ]
    return get_kubectl_json(command)


@dataclass
class OOMEvent:
    """Represents a container OOM kill event."""

    context: str
    pod_name: str
    namespace: str
    container_name: str
    terminated_at: datetime
    restart_count: int
    node_name: str
    restarted: bool = False
    memory_limit: str = "N/A"


def parse_iso_datetime(dt_str):
    """Parse ISO format datetime string to datetime object."""
    if not dt_str:
        return None
    try:
        # Handle both formats with and without timezone info
        if "Z" in dt_str:
            dt_str = dt_str.replace("Z", "+00:00")
        return datetime.fromisoformat(dt_str)
    except ValueError:
        print(f"Warning: Could not parse datetime string: {dt_str}")
        return None


def get_container_memory_limit(container):
    """Extract memory limit from container resources if available."""
    try:
        resources = container.get("resources", {})
        limits = resources.get("limits", {})
        return limits.get("memory", "N/A")
    except (KeyError, AttributeError):
        return "N/A"


def process_pod(pod, context, time_threshold):
    """Process a single pod and yield OOM events."""
    pod_name = pod.get("metadata", {}).get("name", "unknown")
    pod_namespace = pod.get("metadata", {}).get("namespace", "unknown")
    node_name = pod.get("spec", {}).get("nodeName", "unknown")

    # Get container specs to extract resource limits
    container_specs = pod.get("spec", {}).get("containers", []) + pod.get(
        "spec", {}
    ).get("initContainers", [])
    container_limits = {}
    for container in container_specs:
        container_name = container.get("name", "")
        memory_limit = get_container_memory_limit(container)
        container_limits[container_name] = memory_limit

    # Check container statuses for OOM kills
    container_statuses = pod.get("status", {}).get("containerStatuses", []) + pod.get(
        "status", {}
    ).get("initContainerStatuses", [])

    for container in container_statuses:
        container_name = container.get("name", "unknown")
        memory_limit = container_limits.get(container_name, "N/A")

        # Check current state for OOM kills
        state = container.get("state", {})
        if state.get("terminated", {}).get("reason") == "OOMKilled":
            terminated_time_str = state.get("terminated", {}).get("finishedAt")
            terminated_time = parse_iso_datetime(terminated_time_str)

            if (
                terminated_time
                and terminated_time.replace(tzinfo=None) >= time_threshold
            ):
                yield OOMEvent(
                    context=context,
                    pod_name=pod_name,
                    namespace=pod_namespace,
                    container_name=container_name,
                    terminated_at=terminated_time,
                    restart_count=container.get("restartCount", 0),
                    node_name=node_name,
                    memory_limit=memory_limit,
                )

        # Check last state for containers that have restarted after OOM
        last_state = container.get("lastState", {})
        if last_state.get("terminated", {}).get("reason") == "OOMKilled":
            terminated_time_str = last_state.get("terminated", {}).get("finishedAt")
            terminated_time = parse_iso_datetime(terminated_time_str)

            if (
                terminated_time
                and terminated_time.replace(tzinfo=None) >= time_threshold
            ):
                yield OOMEvent(
                    context=context,
                    pod_name=pod_name,
                    namespace=pod_namespace,
                    container_name=container_name,
                    terminated_at=terminated_time,
                    restart_count=container.get("restartCount", 0),
                    node_name=node_name,
                    restarted=True,
                    memory_limit=memory_limit,
                )


def find_oom_killed_pods(
    context,
    namespace=None,
    hours=24,
    all_namespaces=False,
    row_format=None,
    print_immediately=True,
):
    """Find pods that were killed due to OOM within the specified time period."""
    now = datetime.now()
    time_threshold = now - timedelta(hours=hours)

    event_count = 0

    # Determine which namespaces to check
    if all_namespaces:
        pods_data = get_pods_in_all_namespaces(context)
    elif namespace:
        pods_data = get_pods_in_namespace(context, namespace)
    else:
        # Default to all namespaces if none specified
        pods_data = get_pods_in_all_namespaces(context)

    # Process each pod
    for pod in pods_data.get("items", []):
        for event in process_pod(pod, context, time_threshold):
            event_count += 1

            # Print immediately if requested
            if print_immediately and row_format:
                status = "restarted" if event.restarted else "terminated"
                terminated_at = event.terminated_at.strftime("%Y-%m-%d %H:%M:%S")
                print(
                    row_format.format(
                        event.context,
                        event.namespace,
                        event.pod_name[:38] + ".."
                        if len(event.pod_name) > 40
                        else event.pod_name,
                        event.container_name[:23] + ".."
                        if len(event.container_name) > 25
                        else event.container_name,
                        f"{terminated_at} ({status})"[:20],
                        event.restart_count,
                        event.memory_limit,
                        event.node_name[:13] + ".."
                        if len(event.node_name) > 15
                        else event.node_name,
                    )
                )

            yield event

    return event_count


def main():
    """Main function to run the OOM pods report."""
    parser = argparse.ArgumentParser(
        description="Find pods that were killed due to OOM"
    )
    parser.add_argument(
        "-c",
        "--contexts",
        nargs="+",
        default=CONTEXTS,
        help="Kubernetes contexts to search in (default: production contexts)",
    )
    parser.add_argument("-n", "--namespace", help="Kubernetes namespace to search in")
    parser.add_argument(
        "--all-namespaces", action="store_true", help="Search in all namespaces"
    )
    parser.add_argument(
        "--hours",
        type=int,
        default=24,
        help="Look for OOM kills in the past N hours (default: 24)",
    )
    parser.add_argument(
        "--sort",
        choices=["time", "namespace", "node"],
        default="time",
        help="Sort output by: time, namespace, or node (default: time)",
    )
    parser.add_argument(
        "--no-immediate",
        action="store_true",
        help="Don't print results immediately (wait to sort them)",
    )
    args = parser.parse_args()

    if args.all_namespaces and args.namespace:
        parser.error("--all-namespaces and --namespace cannot be used together")

    # Define the table format
    row_format = "{:<30} {:<20} {:<40} {:<25} {:<20} {:<10} {:<15} {:<15}"

    # Print the header
    print(
        row_format.format(
            "Context",
            "Namespace",
            "Pod",
            "Container",
            "Terminated At",
            "Restarts",
            "Memory Limit",
            "Node",
        )
    )
    print("-" * 175)

    # Track all events if we need to sort them
    all_events = []
    total_events = 0

    # Process each context
    for context in args.contexts:
        try:
            if args.no_immediate:
                # Collect events for sorting later
                events = list(
                    find_oom_killed_pods(
                        context,
                        namespace=args.namespace,
                        hours=args.hours,
                        all_namespaces=args.all_namespaces or not args.namespace,
                        print_immediately=False,
                    )
                )
                all_events.extend(events)
                total_events += len(events)
            else:
                # Print events immediately
                count = 0
                for _ in find_oom_killed_pods(
                    context,
                    namespace=args.namespace,
                    hours=args.hours,
                    all_namespaces=args.all_namespaces or not args.namespace,
                    row_format=row_format,
                    print_immediately=True,
                ):
                    count += 1
                total_events += count

        except Exception as e:
            print(f"Error processing context {context}: {e}")

    # If we're sorting, print the sorted events now
    if args.no_immediate and all_events:
        # Sort based on user preference
        if args.sort == "time":
            all_events.sort(key=lambda x: x.terminated_at, reverse=True)
        elif args.sort == "namespace":
            all_events.sort(key=lambda x: (x.namespace, x.pod_name))
        elif args.sort == "node":
            all_events.sort(key=lambda x: (x.node_name, x.namespace, x.pod_name))

        # Print all events
        for event in all_events:
            status = "restarted" if event.restarted else "terminated"
            terminated_at = event.terminated_at.strftime("%Y-%m-%d %H:%M:%S")
            print(
                row_format.format(
                    event.context,
                    event.namespace,
                    event.pod_name[:38] + ".."
                    if len(event.pod_name) > 40
                    else event.pod_name,
                    event.container_name[:23] + ".."
                    if len(event.container_name) > 25
                    else event.container_name,
                    f"{terminated_at} ({status})"[:20],
                    event.restart_count,
                    event.memory_limit,
                    event.node_name[:13] + ".."
                    if len(event.node_name) > 15
                    else event.node_name,
                )
            )

    # Print summary
    print("-" * 175)
    if total_events > 0:
        print(
            f"Total OOM events across all contexts in the past {args.hours} hours: {total_events}"
        )
    else:
        print(f"No OOM events found in any context in the past {args.hours} hours.")


if __name__ == "__main__":
    main()

#!/usr/bin/env node
const { execSync } = require('child_process');

function getGitHistory(folderPath, tagPrefix) {
    // Get all tags sorted by date (newest first)
    const tags = execSync('git tag --sort=-creatordate')
        .toString()
        .split('\n')
        .filter(tag => tag && tag.startsWith(tagPrefix));

    let changelog = '';

    // For each tag, get commits until the previous tag
    for (let i = 0; i < tags.length; i++) {
        const currentTag = tags[i];
        const nextTag = tags[i + 1];

        // Get tag date
        const tagDate = execSync(`git log -1 --format=%ai ${currentTag}`).toString().trim();
        changelog += `\n${currentTag} (${tagDate})\n`;

        // Get commits between tags
        const rangeEnd = currentTag;
        const rangeStart = nextTag || '';
        const commitRange = rangeStart ? `${rangeStart}..${rangeEnd}` : rangeEnd;

        try {
            const commits = execSync(
                `git log ${commitRange} --pretty=format:"%an - %s" -- ${folderPath}/`
            ).toString();

            if (commits) {
                changelog += commits.split('\n').map(line => `- ${line}`).join('\n');
                changelog += '\n';
            }
        } catch (error) {
            console.error(`Error getting commits for ${commitRange}:`, error.message);
        }
    }

    return changelog;
}

function getGitHistoryByDate(folderPath, startDate, endDate) {
    // Format should be YYYY-MM-DD
    const dateRange = `--after="${startDate}" --before="${endDate}"`;

    try {
        // Get all commits within date range, grouped by day
        const commits = execSync(
            `git log ${dateRange} --pretty=format:"%ad|%an - %s" --date=short -- ${folderPath}/`
        ).toString();

        if (!commits) {
            return 'No commits found in the specified date range.\n';
        }

        // Process commits and group by date
        const commitsByDate = {};
        commits.split('\n').forEach(line => {
            const [date, message] = line.split('|');
            if (!commitsByDate[date]) {
                commitsByDate[date] = [];
            }
            commitsByDate[date].push(message);
        });

        // Build changelog string
        let changelog = '';
        Object.keys(commitsByDate)
            .sort()
            .reverse()
            .forEach(date => {
                changelog += `\n${date}\n`;
                changelog += commitsByDate[date].map(msg => `- ${msg}`).join('\n');
                changelog += '\n';
            });

        return changelog;
    } catch (error) {
        console.error('Error getting commit history:', error.message);
        return '';
    }
}

function main() {
    const command = process.argv[2];

    if (command === 'by-tag') {
        const folderPath = process.argv[3];
        const tagPrefix = process.argv[4];

        if (!folderPath || !tagPrefix) {
            console.error('Usage: build-changelog.js by-tag <folder-path> <tag-prefix>');
            console.error('Example: build-changelog.js by-tag src/myproject v1');
            process.exit(1);
        }

        console.log(getGitHistory(folderPath, tagPrefix));
    } else if (command === 'by-date') {
        const folderPath = process.argv[3];
        const startDate = process.argv[4];
        const endDate = process.argv[5];

        if (!folderPath || !startDate || !endDate) {
            console.error('Usage: build-changelog.js by-date <folder-path> <start-date> <end-date>');
            console.error('Example: build-changelog.js by-date src/myproject 2023-01-01 2023-12-31');
            process.exit(1);
        }

        console.log(getGitHistoryByDate(folderPath, startDate, endDate));
    } else {
        console.error('Usage:');
        console.error('  For tag-based changelog:');
        console.error('    build-changelog.js by-tag <folder-path> <tag-prefix>');
        console.error('  For date-based changelog:');
        console.error('    build-changelog.js by-date <folder-path> <start-date> <end-date>');
        process.exit(1);
    }
}

main();

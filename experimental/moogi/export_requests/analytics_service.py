from google.cloud import bigquery  # pip install google-cloud-bigquery
from google.protobuf.json_format import ParseDict

from base.datasets import tenants
from base.datasets.gcp_creds import get_gcp_creds
import services.request_insight.request_insight_pb2 as request_insight_pb2
import services.next_edit_host.next_edit_pb2 as next_edit_pb2

import structlog

QUERY = """
SELECT
  request_id,
  raw_json as request_json,
  time
FROM
  `staging_request_insight_full_export_dataset.next_edit_host_request`
WHERE
  tenant = 'dogfood-shard'
  AND request_id IN UNNEST(@request_ids)
LIMIT 1
"""

logger = structlog.get_logger("get_request_details")


def get_request_details(
    request_id: str, tenant: tenants.DatasetTenant
) -> next_edit_pb2.NextEditRequest:
    bigquery_client = bigquery.Client(
        project=tenant.project_id, credentials=get_gcp_creds()[0]
    )

    query_parameters = [
        bigquery.ArrayQueryParameter("request_ids", "STRING", [request_id])
    ]
    job_config = bigquery.QueryJobConfig(query_parameters=query_parameters)

    rows = list(
        bigquery_client.query_and_wait(QUERY, job_config=job_config, page_size=128)
    )
    if len(rows) > 1:
        logger.warn(f"Found {len(rows)} rows for request {request_id}")
    assert len(rows) > 0, f"Found no rows for request {request_id}"
    logger.info(f"Found {len(rows)} requests")

    ri_request = ParseDict(
        rows[0]["request_json"], request_insight_pb2.RINextEditRequest()
    )
    return ri_request.request

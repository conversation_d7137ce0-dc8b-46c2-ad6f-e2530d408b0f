# Export requests from staging

## Replay requests

```
bazel run //experimental/moogi/export_requests -- replay --request_id=<request_id> --env=dev-$USER --model_name=<model_name>
```

 - request_id defaults to a value for easy testing
 - env defaults to your dev environment
 - model_name defaults to raven-edit-v5-15b


This command will
1. pull the details from bigquery,
2.  extract the blobs and upload them to your dev environment
3. replay the request.

It will print out the request id of the replayed request.



## Export blob

Sometimes we just need to export a sinlge blob

```
bazel run //experimental/moogi/export_requests -- blob --blob_name=<blob_name> --env=dev-$USER --model_name=<model_name>
```

from experimental.moogi.export_requests import (
    app_context,
)

from rich import pretty
from rich.console import Console
from rich.logging import RichHandler

import structlog
import argparse
import os

import logging
from structlog.stdlib import LoggerFactory
from rich.traceback import install

# Install rich traceback for uncaught exceptions
install(show_locals=False, locals_max_length=5, locals_max_string=5, width=300)


# Create a rich console instance
console = Console()
pretty.install()

# Configure the logging library
logging.basicConfig(
    format="%(message)s",
    level=logging.INFO,
    handlers=[RichHandler(rich_tracebacks=True, markup=True)],
)


# Configure structlog
structlog.configure(
    processors=[
        structlog.dev.ConsoleRenderer(colors=False),
    ],
    logger_factory=LoggerFactory(),
)

logger = structlog.get_logger("replay_request")

DEFAULT_REQUEST_ID = "7e02ba78-5fff-4578-930e-2837e0031f27"


def replay_request(args):
    """Replay a request from staging to a dev deploy."""
    context = app_context.initialize_app_context(args.env, args.model_name)
    context.app.replay_request(args.request_id, args.env)


def blob_request(args):
    """Upload blobs from a request to a dev deploy."""
    context = app_context.initialize_app_context(args.env, args.model_name)
    context.app.export_blob(args.blob_name, args.env)


def configure_replay_parser(subparsers):
    """Configure the replay subcommand parser.

    Args:
        subparsers: argparse._SubParsersAction object to add the replay subcommand to
    """
    replay_parser = subparsers.add_parser(
        "replay", help="Replay a request from staging"
    )
    replay_parser.add_argument(
        "--request_id",
        type=str,
        default=DEFAULT_REQUEST_ID,
        help="request id to replay",
    )
    # get the username from the machine
    username = os.getlogin()
    replay_parser.add_argument(
        "--env",
        type=str,
        default=f"dev-{username}",
        help="environment to use",
    )
    replay_parser.add_argument(
        "--model_name",
        type=str,
        default="raven-edit-v5-15b",
        help="model name to use",
    )
    return replay_parser


def configure_blob_parser(subparsers):
    """Configure the blob subcommand parser.

    Args:
        subparsers: argparse._SubParsersAction object to add the blob subcommand to
    """
    blob_parser = subparsers.add_parser("blob", help="Upload blobs from a request")
    blob_parser.add_argument(
        "--blob_name",
        type=str,
        default=DEFAULT_REQUEST_ID,
        help="request id to get blobs from",
    )

    # we need this to check if blob is indexed
    blob_parser.add_argument(
        "--model_name",
        type=str,
        default="raven-edit-v5-15b",
        help="model name to use",
    )
    # get the username from the machine
    username = os.getlogin()
    blob_parser.add_argument(
        "--env",
        type=str,
        default=f"dev-{username}",
        help="environment to use",
    )
    return blob_parser


def main():
    parser = argparse.ArgumentParser(
        description="""
Tool for replaying requests from staging to a dev deploy.

This script allows you to:
1. Get request details from staging
2. Upload associated blobs to the target environment
3. Replay the request in the target environment

Example usage:
  python main.py replay --request_id abc-123 --env dev-moogi
  python main.py blob --blob_name abc-123 --env dev-moogi
""",
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    # Create subparsers
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Configure subcommands
    configure_replay_parser(subparsers)
    configure_blob_parser(subparsers)

    # Global arguments
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="enable debug logging",
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logging.debug("Debug logging enabled")
    else:
        logging.getLogger().setLevel(logging.INFO)
        logging.info("Debug logging disabled")

    if args.command == "replay":
        replay_request(args)
    elif args.command == "blob":
        blob_request(args)
    elif not args.command:
        parser.print_help()


if __name__ == "__main__":
    main()

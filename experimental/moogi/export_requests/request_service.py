from collections.abc import Iterable
from dataclasses import dataclass
from google.protobuf.json_format import MessageToDict

from base.datasets import replay_utils
from base.augment_client.client import AugmentModelClient, NextEditResponse
from services.next_edit_host import next_edit_pb2

import structlog

logger = structlog.get_logger("replay_request")


@dataclass
class RequestService:
    """Class to replay a request."""

    checkpoint_cache: replay_utils.CheckpointCache
    augment_model_client: AugmentModelClient

    def _replay_request(
        self,
        request: next_edit_pb2.NextEditRequest,
    ) -> Iterable[NextEditResponse]:
        return self.augment_model_client.next_edit_stream(
            sequence_id=request.sequence_id,
            lang=request.lang,
            # The dev deploy system might not have `checkpoint_id`. Therefore, we use
            # the list of blob names directly.
            blobs={
                "checkpoint_id": None,
                "added_blobs": replay_utils.resolve_checkpoint(
                    request.blobs, self.checkpoint_cache
                ),
                "deleted_blobs": [],
            },
            recent_changes=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,  # type: ignore
                    preserving_proto_field_name=True,
                )
                for e in request.recent_changes
            ],
            instruction=request.instruction,
            path=request.path,
            blob_name=request.blob_name,
            selection_begin_char=request.selection_begin_char,
            selection_end_char=request.selection_end_char,
            prefix=request.prefix,
            selected_text=request.selected_text,
            suffix=request.suffix,
            diagnostics=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,  # type: ignore
                    preserving_proto_field_name=True,
                )
                for e in request.diagnostics
            ],
            mode=next_edit_pb2.NextEditMode.Name(request.mode),
            scope=next_edit_pb2.NextEditScope.Name(request.scope),
            edit_events=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,  # type: ignore
                    preserving_proto_field_name=True,
                )
                for e in request.edit_events
            ],
            blocked_locations=[
                MessageToDict(
                    e,
                    including_default_value_fields=True,  # type: ignore
                    preserving_proto_field_name=True,
                )
                for e in request.blocked_locations
            ],
            # TODO: The response json decoding is not working properly. Use the new
            # request id to see results on the support site.
            warn_on_parse_error=True,
        )

    def replay(self, request: next_edit_pb2.NextEditRequest) -> list[NextEditResponse]:
        logger.info(
            "getting blob names from checkpoint cache",
            checkpoint_cache=self.checkpoint_cache,
        )
        blob_names = replay_utils.resolve_checkpoint(
            request.blobs, self.checkpoint_cache
        )
        logger.info("got blob names", blob_names_size=len(blob_names))
        # NOTE: We are sending the same request to the dev deploy system for reply. See
        # `FrontNextEditRequest` for all the request fields.
        logger.info("sending request to model client")
        responses: list[NextEditResponse] = list(self._replay_request(request))

        return responses

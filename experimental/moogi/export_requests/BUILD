load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library")

py_library(
    name = "export_requests_lib",
    srcs = [
        "analytics_service.py",
        "app.py",
        "app_context.py",
        "blob_service.py",
        "request_service.py",
    ],
    deps = [
        "//base/datasets:gcp_creds",
        "//base/datasets:replay_utils",
        "//base/datasets:tenants",
        requirement("dataclasses-json"),
        requirement("google-cloud-bigquery"),
        requirement(
            "google-cloud-storage",
        ),
        requirement("structlog"),
        requirement("lru-dict"),
        requirement("pympler"),
        requirement("protobuf"),
        requirement("rich"),
        requirement("tqdm"),
    ],
)

py_binary(
    name = "export_requests",
    srcs = ["main.py"],
    deps = [
        ":export_requests_lib",
    ],
)

from dataclasses import dataclass
from experimental.moogi.export_requests import blob_service, request_service
from experimental.moogi.export_requests.analytics_service import get_request_details
import services.next_edit_host.next_edit_pb2 as next_edit_pb2
from base.blob_names import blob_names_pb2
from base.datasets.tenants import DOGFOOD_SHARD

import structlog

logger = structlog.get_logger("app")

tenant = DOGFOOD_SHARD


@dataclass
class App:
    """Class to replay a request."""

    request_service: request_service.RequestService
    blob_service: blob_service.BlobService

    def replay_request(self, request_id: str, env: str):
        logger.info("Starting replay", request_id=request_id, env=env, tenant=tenant)

        request: next_edit_pb2.NextEditRequest = get_request_details(request_id, tenant)

        logger.info("Uploading blobs and replaying request", env=env)
        self.blob_service.upload(request.blobs)
        responses = self.request_service.replay(request)
        logger.info(f"Before: {request_id}; After: {responses[0].request_id}")
        logger.info(
            f"https://support.{env}.t.us-central1.dev.augmentcode.com/t/augment/request/{responses[0].request_id}"
        )

    def export_blob(self, blob_name: str, env: str):
        logger.info("Exporting blob", blob_name=blob_name, env=env, tenant=tenant)
        # Convert hex string blob name to bytes
        blob_bytes = bytes.fromhex(blob_name)
        # Create Blobs proto with the blob in added list
        blobs = blob_names_pb2.Blobs(
            added=[blob_bytes],
            deleted=[],
        )
        self.blob_service.upload(blobs)

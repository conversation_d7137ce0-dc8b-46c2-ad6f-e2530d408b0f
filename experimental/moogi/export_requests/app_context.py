"""Context for the app."""

from base.augment_client.client import AugmentModelClient
from base.datasets import tenants, replay_utils
from experimental.moogi.export_requests.app import App
from experimental.moogi.export_requests.blob_service import BlobService
from experimental.moogi.export_requests.request_service import RequestService

tenant = tenants.DOGFOOD_SHARD


class AppContext:
    def __init__(self, env: str, model_name: str):
        self.env = env
        self.model_name = model_name

    @property
    def tenant(self):
        return tenant

    @property
    def blob_cache(self) -> replay_utils.BlobCache:
        if not hasattr(self, "_blob_cache"):
            self._blob_cache = replay_utils.get_blob_cache(self.tenant)
        return self._blob_cache

    @property
    def checkpoint_cache(self):
        if not hasattr(self, "_checkpoint_cache"):
            self._checkpoint_cache = replay_utils.get_checkpoint_cache(self.tenant)
        return self._checkpoint_cache

    @property
    def augment_client(self):
        if not hasattr(self, "_augment_client"):
            self._augment_client = replay_utils.get_augment_client(
                url=f"https://{self.env}.us-central.api.augmentcode.com",
                user_agent="export_requests/0",
            )
        return self._augment_client

    @property
    def augment_model_client(self) -> AugmentModelClient:
        return self.augment_client.client_for_model(self.model_name)

    @property
    def next_edit_host_name(self):
        return self.model_name

    @property
    def replay_request_service(self):
        return RequestService(
            checkpoint_cache=self.checkpoint_cache,
            augment_model_client=self.augment_model_client,
        )

    @property
    def blob_service(self):
        return BlobService(
            augment_client=self.augment_client,
            blob_cache=self.blob_cache,
            checkpoint_cache=self.checkpoint_cache,
            next_edit_host_name=self.next_edit_host_name,
        )

    @property
    def request_service(self):
        return RequestService(
            checkpoint_cache=self.checkpoint_cache,
            augment_model_client=self.augment_model_client,
        )

    @property
    def app(self):
        return App(
            blob_service=self.blob_service,
            request_service=self.request_service,
        )


app_context: AppContext | None = None


def initialize_app_context(env: str, model_name: str):
    global app_context
    app_context = AppContext(env, model_name)
    return get_app_context()


def get_app_context() -> AppContext:
    assert app_context is not None
    return app_context

from dataclasses import dataclass
from base.blob_names import blob_names_pb2

from base.datasets import replay_utils
from base.augment_client.client import AugmentClient

all_blob_names = set()


@dataclass
class BlobService:
    """Class to upload blobs to a target environment."""

    augment_client: AugmentClient
    blob_cache: replay_utils.BlobCache
    checkpoint_cache: replay_utils.CheckpointCache
    next_edit_host_name: str

    def upload(self, blobs_proto: blob_names_pb2.Blobs):
        blob_names = replay_utils.resolve_checkpoint(blobs_proto, self.checkpoint_cache)
        replay_utils.upload_and_ensure_blobs_exist(
            self.augment_client,
            self.blob_cache,
            set(blob_names),
            self.next_edit_host_name,
        )
        all_blob_names.update(blob_names)

        # Verifies that all the blobs are indexed.
        replay_utils.index_status(
            self.augment_client, all_blob_names, self.next_edit_host_name
        )

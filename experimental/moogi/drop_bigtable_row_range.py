from google.cloud import bigtable  # type: ignore
import argparse
import re
from google.cloud.bigtable import row_filters

PROJECT_ID = "system-services-dev"


def drop_row_range(instance_id, table_id, row_key_prefix):
    client = bigtable.Client(project=PROJECT_ID, admin=True)
    instance = client.instance(instance_id)
    table = instance.table(table_id)

    if row_key_prefix == "":
        # Drop all rows
        print("Dropping all rows from table")
        table.truncate(timeout=200)
    else:
        # Drop rows with the specified prefix
        print(f"Dropping rows with prefix: {row_key_prefix}")
        table.drop_by_prefix(row_key_prefix, timeout=200)


def drop_rows_by_regex(instance_id, table_id, row_key_regex, batch_size=1000):
    """
    Delete rows that match a regex pattern.

    Args:
        instance_id: Bigtable instance ID
        table_id: Bigtable table ID
        row_key_regex: Regular expression pattern for row keys to delete
        batch_size: Number of rows to delete in each batch
    """
    client = bigtable.Client(project=PROJECT_ID, admin=True)
    instance = client.instance(instance_id)
    table = instance.table(table_id)

    # Create a row filter for the regex
    row_filter = row_filters.RowKeyRegexFilter(row_key_regex.encode("utf-8"))

    # Read rows matching the regex
    print(f"Finding rows matching regex: {row_key_regex}")
    rows = table.read_rows(filter_=row_filter)

    # Delete matching rows in batches
    row_keys = []
    deleted_count = 0

    for row in rows:
        row_keys.append(row.row_key)

        if len(row_keys) >= batch_size:
            _delete_batch(table, row_keys)
            deleted_count += len(row_keys)
            print(f"Deleted {deleted_count} rows so far...")
            row_keys = []

    # Delete any remaining rows
    if row_keys:
        _delete_batch(table, row_keys)
        deleted_count += len(row_keys)

    print(f"Total rows deleted: {deleted_count}")


def _delete_batch(table, row_keys):
    """Helper function to delete a batch of rows by key"""
    rows = []
    for row_key in row_keys:
        row = table.direct_row(row_key)
        row.delete()
        rows.append(row)

    # Commit all mutations (deletions)
    table.mutate_rows(rows)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--instance-id", help="Instance ID", required=True)
    parser.add_argument("--table-id", help="Table ID", required=True)
    parser.add_argument(
        "--row-key-prefix", help="Row prefix", default="", required=False
    )
    parser.add_argument("--row-key-regex", help="Row key regex pattern", required=False)
    args = parser.parse_args()

    if args.row_key_regex:
        drop_rows_by_regex(args.instance_id, args.table_id, args.row_key_regex)
    else:
        drop_row_range(args.instance_id, args.table_id, args.row_key_prefix)

# BigTable Utilities

This directory contains utilities for working with BigTable in the development environment.

## bigtable_row_range.py

A script to read and analyze data from BigTable in the development environment.

### Prerequisites

Make sure you have the necessary permissions to access the BigTable instance and that you're authenticated with Google Cloud:

```bash
gcloud auth login
```

### Usage

The script provides several ways to interact with BigTable:

#### List all tables in the instance

```bash
python bigtable_row_range.py --list-tables
```

#### Read a specific row by key

```bash
python bigtable_row_range.py --table dev-mike-content-manager --row-key "your-row-key"
```

#### Read rows with a specific prefix

```bash
python bigtable_row_range.py --table dev-mike-content-manager --prefix "your-prefix"
```

#### Read a range of rows between start and end keys

```bash
python bigtable_row_range.py --table dev-mike-content-manager --start-key "start" --end-key "end"
```

#### Count rows by prefix

This will group rows by the prefix (part before the first "#" in the row key) and count them:

```bash
# Count all rows and group by prefix
python bigtable_row_range.py --table dev-mike-content-manager --count-prefix ""

# Count only rows with a specific prefix
python bigtable_row_range.py --table dev-mike-content-manager --count-prefix "your-prefix"
```

#### Delete rows with a specific prefix

This allows you to delete rows with a specific prefix. By default, it runs in dry-run mode to show what would be deleted without actually deleting anything:

```bash
# Dry run (default) - shows what would be deleted but doesn't delete anything
python bigtable_row_range.py --table dev-mike-content-manager --delete-prefix "your-prefix"
# or explicitly specify dry run
python bigtable_row_range.py --table dev-mike-content-manager --delete-prefix "your-prefix" --dry-run

# Execute the deletion (will prompt for confirmation)
python bigtable_row_range.py --table dev-mike-content-manager --delete-prefix "your-prefix" --execute
```

**CAUTION**: The delete operation is permanent and cannot be undone. Always run with `--dry-run` first to verify what will be deleted.

### Examples

1. List all tables:
   ```bash
   python bigtable_row_range.py --list-tables
   ```

2. Read a specific row:
   ```bash
   python bigtable_row_range.py --table dev-mike-content-manager --row-key "RemoteAgent#12345"
   ```

3. Read all remote agent rows:
   ```bash
   python bigtable_row_range.py --table dev-mike-content-manager --prefix "RemoteAgent"
   ```

4. Count rows by type:
   ```bash
   python bigtable_row_range.py --table dev-mike-content-manager --count-prefix ""
   ```

5. Delete rows with a specific prefix (dry run):
   ```bash
   python bigtable_row_range.py --table dev-mike-content-manager --delete-prefix "RemoteAgent#12345" --dry-run
   ```

6. Delete rows with a specific prefix (execute):
   ```bash
   python bigtable_row_range.py --table dev-mike-content-manager --delete-prefix "RemoteAgent#12345" --execute
   ```

### Notes

- The script connects to the `bigtable-central-dev` instance in the `system-services-dev` project.
- For large tables, consider adding limits to your queries to avoid processing too much data.
- Binary data will be displayed as `<binary data of length N>` rather than attempting to decode it.
- The delete operation processes rows in batches of 100 to avoid timeouts and server overload.
- Always use `--dry-run` first when deleting to verify what will be deleted.
- The delete operation requires typing 'yes' to confirm, providing an additional safety check.

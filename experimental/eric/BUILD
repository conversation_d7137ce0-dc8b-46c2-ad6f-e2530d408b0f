load("//tools/bzl:python.bzl", "py_binary")
load("@python_pip//:requirements.bzl", "requirement")

py_binary(
    name = "pull_request_event",
    srcs = ["pull_request_event.py"],
    deps = [
        requirement("google-cloud-bigquery"),
    ],
)

py_binary(
    name = "generate_profiles",
    srcs = ["generate_profiles.py"],
    deps = [
        requirement("google-cloud-bigquery"),
        requirement("matplotlib"),
        requirement("scikit-learn"),
        requirement("numpy"),
    ],
)

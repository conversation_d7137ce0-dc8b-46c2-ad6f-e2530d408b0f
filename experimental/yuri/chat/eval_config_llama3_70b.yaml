system:
    name: remote_chat

    # The model name is optional, will select default if not present
    # model_name: binks-llama3-70B-FP8-chatanol1-11-chat
    # model_name: binks-ug-chatanol1-18-reranker-chat
    model_name: binks-l3-w-refinement-v1

    client:
        url: https://dev-yuri.us-central.api.augmentcode.com
        # url: https://dogfood.api.augmentcode.com
    chat:
      indexing_retry_count: 30
# system:
#   name: "chat_rag"
#   model:
#     name: "fastforward_llama3_70b_instruct"
#     sequence_length: 8192
#   prompt_formatter:
#     tokenizer_name: llama3_instruct
#     prompt_formatter_name: binks_llama3
#     prefix_len: 1024
#     suffix_len: 1024
#     path_len: 256
#     message_len: 0  # Not used by the binks_llama3 prompt formatter
#     selected_code_len: 0  # Not used by the binks_llama3 prompt formatter
#     chat_history_len: 2048
#     retrieval_len_per_each_user_guided_file: 2000
#     retrieval_len_for_user_guided: 3000
#     retrieval_len: -1  # Fill the rest of the input prompt with retrievals
#     max_prompt_len: 6144  # 8192 - 2048, the last 2048 reserved for output tokens
#   generation_options:
#     temperature: 0
#     top_k: 0
#     top_p: 0
#     max_generated_tokens: 1024
#   retriever:
#     # scorer:
#     #   name: dense_scorer_v2_fbwd
#     #   checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid
#     #   model_key: model.models.0
#     scorer:
#       name: dense_scorer_v2_fbwd
#       checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3
#     chunker:
#       name: line_level
#       max_lines_per_chunk: 30
#     query_formatter:
#       name: base:chatanol6
#       tokenizer_name: rogue
#       max_tokens: 1024
#     document_formatter:
#       name: base:ethanol6-embedding-with-path-key
#       tokenizer_name: rogue
#       add_path: true
#       max_tokens: 1024
#   experimental:
#     retriever_top_k: 32
#   verbose: False

task:
  name: augment_qa
  dataset_path: /mnt/efs/augment/data/processed/augment_qa/v2
  html_report_output_dir: /mnt/efs/augment/public_html/augment_qa/v2
# task:
#     name: humaneval_instruct
#     variant: synthetic
    # variant: standard
    # limit: 40
# task:
#   name: inficoder_eval_task
#   dataset_path: "/mnt/efs/augment/data/eval/chat/inficoder_apr16/suite_v2.0.0_dev_filtered.json"

podspec: 4xH100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  # name: augment-qa-eval-filepaths-plus-language-labels-mix-v2-ffw-chatanol1-16-2
  # name: inficoder-filepaths-plus-language-labels-mix-v2-chatanol1-16-3
  # name: inficoder-llama-original-chatanol1-16-3
  # name: humaneval-instruct-standard-llama-original-chatanol1-16-3
  name: "reproduce-aug5-1"
  project: yuri
  workspace: Dev

augment:
  gpu_count: 4

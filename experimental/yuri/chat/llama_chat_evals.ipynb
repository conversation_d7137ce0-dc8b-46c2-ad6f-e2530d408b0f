{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/.local/lib/python3.11/site-packages/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import html\n", "\n", "from google.cloud import bigquery\n", "from tqdm import tqdm\n", "from typing import Any\n", "from collections import defaultdict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.fastforward.llama import model_specs as llama_model_specs\n", "from base.fastforward.llama.model_specs import _MODEL_SPECS\n", "from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from research.models.meta_model import GenerationOptions, get_model, register_model\n", "\n", "LLAMA3_70B_MODEL_SPECT = _MODEL_SPECS[\"llama3-70b\"]\n", "LLAMA3_70B_MODEL_SPECT.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp_ckpts/llama3-jul21-smartpaste-v3-ffw\"\n", "# LLAMA3_70B_MODEL_SPECT.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp/llama3-jun20-forced-comment-mix-w-real-data-v2-1286-ffw\"\n", "# LLAMA3_70B_MODEL_SPECT.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp/llama3-jun20-basic-mix-w-real-data-v2-1286-ffw\"\n", "\n", "@register_model(\"fastforward_llama3_70b_instruct\")\n", "class FastForwardLLAMA3_70B_Instruct(LLAMA_FastForwardModel):\n", "    \"\"\"The open-source LLAMA3-70B-Instruct model.\"\"\"\n", "\n", "    seq_length: int = 8192\n", "\n", "    model_spec: llama_model_specs.LlamaModelSpec = LLAMA3_70B_MODEL_SPECT\n", "\n", "    @classmethod\n", "    def create_default_formatter(cls):\n", "        tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "        apportionment = ChatTokenApportionment(\n", "            path_len=256,\n", "            message_len=0,\n", "            chat_history_len=2048,\n", "            prefix_len=1024,\n", "            selected_code_len=0,\n", "            suffix_len=1024,\n", "            max_prompt_len=6144,\n", "            retrieval_len=-1\n", "        )\n", "\n", "        prompt_formatter = get_chat_prompt_formatter_by_name(\n", "            \"binks_llama3\",\n", "            tokenizer,\n", "            apportionment,\n", "        )\n", "        return prompt_formatter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"staging_request_insight_full_export_dataset\"\n", "# TENANT_NAME = \"dogfood\"\n", "\n", "def get_chat_sample(request_id):\n", "    query = f\"\"\"\n", "SELECT *\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_event\n", "WHERE request_id=\"{request_id}\"\n", "AND event_type IN (\"chat_host_request\", \"chat_host_response\", \"request_metadata\")\n", "ORDER BY time DESC\n", "LIMIT 100000\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "\n", "    request_rows = [row for row in all_rows if row.event_type == \"chat_host_request\"]\n", "    response_rows = [row for row in all_rows if row.event_type == \"chat_host_response\"]\n", "    meta_rows = [row for row in all_rows if row.event_type == \"request_metadata\"]\n", "\n", "    assert len(request_rows) == 1, f\"Expected 1 request, got {len(request_rows)}\"\n", "    assert len(response_rows) == 1, f\"Expected 1 response, got {len(response_rows)}\"\n", "    assert len(meta_rows) == 1, f\"Expected 1 metadata, got {len(meta_rows)}\"\n", "\n", "    return {\n", "        \"request\": request_rows[0][\"raw_json\"][\"request\"],\n", "        \"response\": response_rows[0][\"raw_json\"][\"response\"],\n", "        \"metadata\": meta_rows[0][\"raw_json\"],\n", "        \"request_token_ids\": request_rows[0][\"raw_json\"][\"tokenization\"][\"token_ids\"],\n", "    }\n", "\n", "# def get_chat_sample(request_id):\n", "#     request_query = f\"\"\"\n", "# SELECT *\n", "# FROM {PROJECT_ID}.{DATASET_NAME}.request_event\n", "# WHERE event_type=\"chat_host_request\" AND tenant=\"{TENANT_NAME}\" AND request_id=\"{request_id}\"\n", "# ORDER BY time DESC\n", "# LIMIT 100000\n", "# \"\"\"\n", "#     response_query = f\"\"\"\n", "# SELECT *\n", "# FROM {PROJECT_ID}.{DATASET_NAME}.request_event\n", "# WHERE event_type=\"chat_host_response\" AND tenant=\"{TENANT_NAME}\" AND request_id=\"{request_id}\"\n", "# ORDER BY time DESC\n", "# LIMIT 100000\n", "# \"\"\"\n", "\n", "#     meta_query = f\"\"\"\n", "# SELECT *\n", "# FROM {PROJECT_ID}.{DATASET_NAME}.request_event\n", "# WHERE event_type=\"request_metadata\" AND tenant=\"{TENANT_NAME}\" AND request_id=\"{request_id}\"\n", "# ORDER BY time DESC\n", "# LIMIT 100000\n", "# \"\"\"\n", "\n", "#     client = bigquery.Client(project=PROJECT_ID)\n", "#     request_rows = [*client.query_and_wait(request_query)]\n", "#     response_rows = [*client.query_and_wait(response_query)]\n", "#     meta_rows = [*client.query_and_wait(meta_query)]\n", "#     assert len(request_rows) == 1, len(request_rows)\n", "#     assert len(response_rows) == 1, len(response_rows)\n", "#     assert len(meta_rows) == 1, len(meta_rows)\n", "#     return {\n", "#         \"request\": dict(request_rows[0])[\"raw_json\"][\"request\"],\n", "#         \"response\": dict(response_rows[0])[\"raw_json\"][\"response\"],\n", "#         \"metadata\": dict(meta_rows[0])[\"raw_json\"],\n", "#         \"request_token_ids\": dict(request_rows[0])[\"raw_json\"][\"tokenization\"][\"token_ids\"],\n", "#         # \"request\": dict(request_rows[0]),\n", "#         # \"response\": dict(response_rows[0]),\n", "#         # \"metadata\": dict(meta_rows[0]),\n", "#     }\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from markdown import markdown\n", "\n", "\n", "def render_simple_message(message, response, selected_code=None):\n", "    message = markdown(message, extensions=[\"fenced_code\", \"codehilite\"])\n", "    response = markdown(response, extensions=[\"fenced_code\", \"codehilite\"])\n", "\n", "    if selected_code is not None:\n", "        selected_code = f\"\"\"```\n", "{selected_code}\n", "```\"\"\"\n", "        selected_code = markdown(selected_code, extensions=[\"fenced_code\", \"codehilite\"])\n", "        html_content = f\"\"\"\n", "    <div class=\"row\">\n", "        <div class=\"answer\" style=\"flex: 1;\">{selected_code}</div>\n", "    </div>\n", "        \"\"\"\n", "    else:\n", "        html_content = \"\"\n", "\n", "    html_content += f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row\">\n", "    <div class=\"answer\" style=\"flex: 1;\">{response}</div>\n", "</div>\n", "\"\"\"\n", "\n", "    return html_content\n", "\n", "\n", "def wrap_html(html_content):\n", "    start = \"\"\"\n", "    <html>\n", "    <head>\n", "        <title>Question and Answers</title>\n", "        <style>\n", "            .row {\n", "                display: flex;\n", "                justify-content: center;\n", "                align-items: center;\n", "                margin-bottom: 20px;\n", "            }\n", "            .feedback, .answer {\n", "                margin: 10px;\n", "                padding: 10px;\n", "                border: 1px solid #ddd;\n", "                border-radius: 5px;\n", "                background-color: #f9f9f9;\n", "            }\n", "        </style>\n", "    </head>\n", "    <body>\n", "\"\"\"\n", "\n", "    end = \"\"\"\n", "        </body>\n", "    </html>\"\"\"\n", "\n", "    return \"\\n\".join([start, html_content, end])\n", "\n", "def render_sample(sample):\n", "    cur_index = 0\n", "    html_content = \"\"\n", "    if \"chat_history\" in sample[\"request\"]:\n", "        for exchange in sample[\"request\"][\"chat_history\"]:\n", "            html_content += f\"<h3>Message {cur_index}</h3>\"\n", "            cur_index += 1\n", "            html_content += render_simple_message(exchange[\"request_message\"], exchange[\"response_text\"])\n", "            html_content += \"<hr>\"\n", "        html_content = f\"\"\"\n", "        <details>\n", "            <summary>Chat history</summary>\n", "            {html_content}\n", "        </details>\n", "        \"\"\"\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "    html_content += render_simple_message(\n", "        sample[\"request\"][\"message\"],\n", "        sample[\"generated_text\"],\n", "        sample[\"request\"][\"selected_code\"] if \"selected_code\" in sample[\"request\"] else None\n", "    )\n", "\n", "    return html_content\n", "\n", "\n", "def request_id_to_link(request_id):\n", "    return f\"https://support.dogfood.t.us-central1.prod.augmentcode.com/t/dogfood/request/{request_id}\"\n", "\n", "\n", "def render_category_report(samples, request_ids, comments, model, ):\n", "    multisample_html = \"\"\n", "    for i, (sample, request_id, comment) in tqdm(enumerate(zip(samples, request_ids, comments))):\n", "        multisample_html += f\"<h2>Chain {i}</h2>\"\n", "        multisample_html += f'Request ID: <a href=\"{request_id_to_link(request_id)}\">{request_id}</a>'\n", "\n", "        multisample_html += f\"\"\"\n", "        <details>\n", "            <summary>Full prompt</summary>\n", "            <pre>{model.tokenizer.detokenize(sample[\"request_token_ids\"])}</pre>\n", "        </details>\n", "        \"\"\"\n", "\n", "        multisample_html += f\"\"\"\n", "        <details>\n", "            <summary>Comment</summary>\n", "            <pre>{comment}</pre>\n", "        </details>\n", "        \"\"\"\n", "\n", "        multisample_html += f\"\"\"\n", "        <details>\n", "            <summary>Raw output</summary>\n", "            <pre>{html.escape(sample[\"generated_text\"])}</pre>\n", "        </details>\n", "        \"\"\"\n", "\n", "        multisample_html += render_sample(sample)\n", "        multisample_html += \"<hr>\"\n", "\n", "    return multisample_html\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model(\"fastforward_llama3_70b_instruct\")\n", "model.load()\n", "\n", "model.tokenizer.eod_id = model.tokenizer.special_tokens.eos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_response(sample, model):\n", "\n", "    generated_text = model.raw_generate(\n", "        sample[\"request_token_ids\"],\n", "        GenerationOptions(max_generated_tokens=2048)\n", "    )\n", "    sample[\"generated_text\"] = generated_text\n", "\n", "    # sample[\"generated_text\"] = sample[\"response\"][\"text\"]\n", "\n", "    return sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_SAMPLES: dict[str, Any] = {\n", "    \"code_edits\": [\n", "        (\"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\", \"Model should modify the whole selected code\"),\n", "        (\"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\", \"Model should modify only the import line.\"),\n", "        (\"75aedc45-11f6-4a5a-98d9-43798ba29479\", \"Model should print only modified code, without repeating functions.\"),\n", "    ],\n", "    \"code_edits_one_turn\" : [\n", "        (\"4b958757-6818-44f8-af55-2a63e82b8458\", \"Model should modify only selected code.\"),\n", "        (\"7fd0623b-c217-4658-89f9-af27246f7bfd\", \"Model shouldn't lose T = TypeVar(\\\"T\\\")\"),\n", "        (\"974831ff-997d-4de8-984b-94e6c6bd42dc\", \"Model should preserve new line character at the end of selection\"),\n", "    ],\n", "    \"regression_tests\": [\n", "        (\"5199b947-b45a-4e22-a087-e59a12beda91\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718823357108449?thread_ts=1718821966.276869&cid=C06R495KUSD\"),\n", "        (\"3bb42ee8-c40b-4c69-96cf-9bf23e8548fb\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1717614811054099\"),\n", "        (\"f08c5194-6cb6-4480-87a1-b256ab2f52e2\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718918465955939\"),\n", "        (\"40176c9d-2acb-44e9-b759-b16a21e5aeb6\", \"Explanation of selected code.\"),\n", "        (\"cd09df68-6189-4cb9-9032-87810deda180\", \"Explanation of selected code on 2nd turn.\"),\n", "    ]\n", "}\n", "\n", "\n", "for category, samples in EVAL_SAMPLES.items():\n", "    for i in tqdm(range(len(samples)), desc=f\"Loading samples for {category}\"):\n", "        request_id, comment = samples[i]\n", "        sample = get_chat_sample(request_id)\n", "        samples[i] = sample, request_id, comment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for category, samples in EVAL_SAMPLES.items():\n", "    for i in tqdm(range(len(samples)), desc=f\"Generating samples for {category}\"):\n", "        sample, request_id, comment = samples[i]\n", "        samples[i] = generate_response(sample, model), request_id, comment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["main_html = \"\"\n", "\n", "for category, eval_samples in EVAL_SAMPLES.items():\n", "    samples, request_ids, comments = [], [], []\n", "\n", "    samples = [x[0] for x in eval_samples]\n", "    request_ids = [x[1] for x in eval_samples]\n", "    comments = [x[2] for x in eval_samples]\n", "\n", "    cur_html = render_category_report(samples, request_ids, comments, model)\n", "\n", "    main_html += f\"\"\"\n", "    <details>\n", "        <summary>Category: {category}</summary>\n", "        {cur_html}\n", "    </details>\n", "    \"\"\"\n", "\n", "main_html = wrap_html(main_html)\n", "\n", "with open(\"./eval-jul21-v1.html\", \"w\") as f:\n", "    f.write(main_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples[0][\"request\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_SAMPLES[\"code_edits_one_turn\"][0][0][\"generated_text\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_SAMPLES: dict[str, Any] = {\n", "    \"code_edits\": [\n", "        (\"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\", \"Model should modify the whole selected code\"),\n", "        (\"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\", \"Model should modify only the import line.\"),\n", "        (\"75aedc45-11f6-4a5a-98d9-43798ba29479\", \"Model should print only modified code, without repeating functions.\"),\n", "    ],\n", "    \"code_edits_one_turn\" : [\n", "        (\"4b958757-6818-44f8-af55-2a63e82b8458\", \"Model should modify only selected code.\"),\n", "        (\"7fd0623b-c217-4658-89f9-af27246f7bfd\", \"Model shouldn't lose T = TypeVar(\\\"T\\\")\"),\n", "        (\"974831ff-997d-4de8-984b-94e6c6bd42dc\", \"Model should preserve new line character at the end of selection\"),\n", "    ],\n", "    \"regression_tests\": [\n", "        (\"5199b947-b45a-4e22-a087-e59a12beda91\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718823357108449?thread_ts=1718821966.276869&cid=C06R495KUSD\"),\n", "        (\"3bb42ee8-c40b-4c69-96cf-9bf23e8548fb\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1717614811054099\"),\n", "        (\"f08c5194-6cb6-4480-87a1-b256ab2f52e2\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718918465955939\"),\n", "        (\"40176c9d-2acb-44e9-b759-b16a21e5aeb6\", \"Explanation of selected code.\"),\n", "        (\"cd09df68-6189-4cb9-9032-87810deda180\", \"Explanation of selected code on 2nd turn.\"),\n", "    ],\n", "    \"filepaths\": [\n", "        (\"e6ef9a30-f94f-4d01-aaf8-7ac3b4e14c95\", \"dense_scorer.py, dense_scorer_v2.py\"),\n", "        (\"bb87242c-ae55-4c6a-9d7b-925129b1dada\", \"edit_gen_stages.py\"),  # staging shard\n", "        (\"9aa4f9e0-77bd-4b34-ae1a-d3b5ba060035\", \"deploy_lib.jsonnet, flags.yaml\"),\n", "        (\"700d81ea-509f-43c9-a397-7307ee7eb153\", \"post.rb has to show to app/models/post.rb, not lib/discourse_dev/post.rb\")  # staging shard\n", "    ],\n", "    \"language_labels\": [\n", "        (\"04d5128b-92ec-47f8-8ae2-2ebb48ae0356\", \"python\"),\n", "        (\"80ee77d1-ba36-480e-89e1-e7b55a9efab5\", \"python\"),\n", "        (\"5f0e2497-e1a7-4bfb-82c6-3b5949ee3794\", \"python\"),\n", "        (\"7636a168-e3fe-4e7e-b343-2c45de4da1cb\", \"python\")\n", "    ]\n", "}\n", "\n", "loaded_eval_samples = defaultdict(list)\n", "\n", "for category, eval_samples_info in EVAL_SAMPLES.items():\n", "    for sample_info in tqdm(eval_samples_info):\n", "        request_id, comment = sample_info\n", "        sample = get_chat_sample(request_id)\n", "        loaded_eval_samples[category].append(\n", "            {\n", "                \"sample\": sample,\n", "                \"request_id\": request_id,\n", "                \"comment\": comment\n", "            }\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_chat_sample(\"bb87242c-ae55-4c6a-9d7b-925129b1dada\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"staging_request_insight_full_export_dataset\"\n", "# TENANT_NAME = \"dogfood\"\n", "\n", "def get_chat_sample(request_id):\n", "    query = f\"\"\"\n", "SELECT *\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_event\n", "WHERE request_id=\"{request_id}\"\n", "AND event_type IN (\"chat_host_request\", \"chat_host_response\", \"request_metadata\")\n", "ORDER BY time DESC\n", "LIMIT 100000\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "\n", "    request_rows = [row for row in all_rows if row.event_type == \"chat_host_request\"]\n", "    response_rows = [row for row in all_rows if row.event_type == \"chat_host_response\"]\n", "    meta_rows = [row for row in all_rows if row.event_type == \"request_metadata\"]\n", "\n", "    assert len(request_rows) == 1, f\"Expected 1 request, got {len(request_rows)}\"\n", "    assert len(response_rows) == 1, f\"Expected 1 response, got {len(response_rows)}\"\n", "    assert len(meta_rows) == 1, f\"Expected 1 metadata, got {len(meta_rows)}\"\n", "\n", "    return {\n", "        \"request\": request_rows[0][\"raw_json\"][\"request\"],\n", "        \"response\": response_rows[0][\"raw_json\"][\"response\"],\n", "        \"metadata\": meta_rows[0][\"raw_json\"],\n", "        \"request_token_ids\": request_rows[0][\"raw_json\"][\"tokenization\"][\"token_ids\"],\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
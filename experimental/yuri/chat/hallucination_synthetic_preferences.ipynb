{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "\n", "import os\n", "\n", "os.environ[\"PYTHONPATH\"] += \":/home/<USER>/repos/augment/research/gpt-neox:/home/<USER>/repos/augment\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import requests\n", "import yaml\n", "from tqdm import tqdm\n", "\n", "from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format_completion.prompt_formatter import PromptChunk\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "from research.eval.harness.factories import create_retriever\n", "from research.eval.harness.systems.abs_system import get_system\n", "from research.eval.harness.systems.basic_RAG_system import (\n", "    RAGSystem,  # just so that it registered  # noqa: F401\n", ")\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from markdown import markdown"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["LLAMA_CPP_URL = \"http://127.0.0.1:8080\"\n", "DATA_PATH = \"/mnt/efs/augment/user/yury/binks/binks-v3/repos_with_qa.jsonl\"\n", "\n", "RETRIEVER_CONFIG = {\n", "    \"scorer\": {\n", "        \"name\": \"generic_neox\",\n", "        \"checkpoint_path\": \"chatanol/chatanol1-11\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query_simple_chat\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "        \"verbose\": True,\n", "        \"tokenizer_name\": \"starcodertokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=0,\n", "    chat_history_len=2048,\n", "    prefix_len=1024,\n", "    selected_code_len=0,\n", "    suffix_len=1024,\n", "    max_prompt_len=6144,\n", "    retrieval_len=-1\n", ")\n", "PROMPT_FORMATTER = get_chat_prompt_formatter_by_name(\n", "    \"binks_llama3\",\n", "    TOKENIZER,\n", "    APPORTIONMENT,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\n", "def get_docs(sample):\n", "    docs = []\n", "    for doc_dict in sample[\"file_list\"]:\n", "        cur_doc = Document.new(\n", "            text=doc_dict[\"content\"],\n", "            path=doc_dict[\"max_stars_repo_path\"],\n", "        )\n", "        docs.append(cur_doc)\n", "\n", "    return docs\n", "\n", "\n", "def load_data(path):\n", "    data = []\n", "\n", "    with open(path) as f:\n", "        for line in tqdm(f):\n", "            data.append(json.loads(line))\n", "\n", "    return data\n", "\n", "\n", "def generate_llama_cpp(tokens):\n", "    data = {\n", "        \"prompt\": tokens,\n", "        \"temperature\": 0.8,\n", "    }\n", "\n", "    response = requests.post(\n", "        f\"{LLAMA_CPP_URL}/completion\",\n", "        json=data,\n", "        headers={\"Content-Type\": \"application/json\"},\n", "        timeout=120.0,\n", "    )\n", "\n", "    decoded_response = response.json()\n", "\n", "    return decoded_response[\"content\"]\n", "\n", "\n", "def convert_chunk(chunk: Chunk):\n", "    return PromptChunk(\n", "        text=chunk.text,\n", "        path=chunk.parent_doc.path,\n", "        char_start=chunk.char_offset,\n", "        char_end=chunk.char_offset + chunk.length,\n", "        unique_id=chunk.id,\n", "    )\n", "\n", "\n", "def generate_responses(message, retriever, N=1):\n", "    chunks, _ = retriever.query(\n", "        model_input=ModelInput(\n", "            extra={\n", "                \"message\": message\n", "            }\n", "        ),\n", "        top_k=32\n", "    )\n", "\n", "    chunks = [*map(convert_chunk, chunks)]\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=\"\",\n", "        prefix=\"\",\n", "        selected_code=\"\",\n", "        suffix=\"\",\n", "        message=message,\n", "        chat_history=[],\n", "        prefix_begin=0,\n", "        suffix_end=0,\n", "        retrieved_chunks=chunks,\n", "    )\n", "\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    outputs = []\n", "    for _ in tqdm(range(N)):\n", "        cur_gen = generate_llama_cpp(prompt_output.tokens)\n", "        outputs.append(\n", "            {\n", "                \"text\": cur_gen,\n", "            }\n", "        )\n", "\n", "    misc_info = {\n", "        \"full_prompt\": TOKENIZER.detokenize(prompt_output.tokens),\n", "        \"chunks\": chunks,\n", "    }\n", "    return outputs, misc_info\n", "\n", "def render_chunks(chunks: list[PromptChunk]):\n", "    per_chunk = []\n", "    for c in chunks:\n", "        cur_text = f\"\"\"\n", "\n", "Here is the snippet from `{c.path}`:\n", "\n", "```\n", "{c.text}\n", "```\n", "\n", "\"\"\"\n", "        per_chunk.append(cur_text)\n", "\n", "    return \"\".join(per_chunk)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["UNARY_PROMPT = \"\"\"Here are code snippets from codebase:\n", "{rendered_chunks}\n", "\n", "Here is a question asked about this codebase:\n", "{question}\n", "\n", "Here is the answer to this question generated by 3rd party service, enclosed between ===:\n", "===\n", "{answer}\n", "===\n", "\n", "Your task is to detect hallucinations in the answer.\n", "Hallucination - is any information that is not grounded in the provided code snippets or general knowledge.\n", "Examples: fabricated facts, nonexistent entities (e.g. files, variables, functions, classes, fields, etc), etc\n", "\n", "Follow these steps to complete this task:\n", "1. <PERSON><PERSON><PERSON> provided code snippets, question and answer.\n", "2. Describe your thinking process on how to complete this task.\n", "3. Explicitly write YES (if hallucination is detected) or NO (if hallucination is NOT detected).\n", "\"\"\"\n", "\n", "UNARY_EXTRACTING_PROMPT = \"\"\"Return results as a JSON with 2 keys:\n", "- is_hallucination\n", "- explanation\n", "\n", "If answer contains hallucinations (YES), then is_hallucination must be True and explanation should contain what hallucinations are detected.\n", "If answer does not contain hallucinations (NO), then is_hallucination must be False and explanation should be empty.\n", "\"\"\"\n", "\n", "def unary_run(question, answer, chunks, gpt):\n", "    \"\"\"\n", "        Returns:\n", "            - main_prompt : string\n", "            - gpt4_response : string\n", "            - is_hallucination : boolean\n", "            - explanation : string\n", "    \"\"\"\n", "\n", "    rendered_chunks = render_chunks(chunks)\n", "\n", "    prompt = UNARY_PROMPT.format(\n", "        rendered_chunks=rendered_chunks,\n", "        question=question,\n", "        answer=answer,\n", "    )\n", "\n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4o\", temperature=0)\n", "\n", "    messages.append({\"role\": \"assistant\", \"content\": gpt4_response})\n", "    messages.append({\"role\": \"user\", \"content\": UNARY_EXTRACTING_PROMPT})\n", "\n", "    extracted_response = gpt(messages, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0)\n", "\n", "    return {\n", "        \"main_prompt\": prompt,\n", "        \"gpt4_response\": gpt4_response,\n", "        \"rendered_chunks\": rendered_chunks,\n", "        **extracted_response,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_answers_list(f_entry):\n", "    html = \"<ul>\\n\"\n", "    for response, result in zip(f_entry[\"responses\"], f_entry[\"unary_run_results\"]):\n", "        color = \"red\" if result[\"is_hallucination\"] else \"green\"\n", "        circle = f'<span style=\"color:{color};\">&#9679;</span>'\n", "\n", "        rendered_response = markdown(response[\"text\"], extensions=[\"fenced_code\", \"codehilite\"])\n", "        rendered_explanation = markdown(result[\"explanation\"], extensions=[\"fenced_code\", \"codehilite\"])\n", "        gpt_response = markdown(result[\"gpt4_response\"], extensions=[\"fenced_code\", \"codehilite\"])\n", "\n", "        if result[\"is_hallucination\"]:\n", "            html += f\"\"\"<li>\n", "<details>\n", "    <summary>{circle}</summary>\n", "    <div class='highlighted-text'>{rendered_response}</div>\n", "    <div class='highlighted-text'>{rendered_explanation}</div>\n", "</details>\n", "</li>\"\"\"\n", "            # html += f\"  <li class='highlighted-text'>{circle} {rendered_response} - {rendered_explanation}</li>\\n\"\n", "        else:\n", "            html += f\"\"\"<li>\n", "<details>\n", "    <summary>{circle}</summary>\n", "    <div class='highlighted-text'>{rendered_response}</div>\n", "</details>\n", "</li>\"\"\"\n", "            # html += f\"  <li class='highlighted-text'>{circle} {rendered_response}</li>\\n\"\n", "\n", "        html += f\"\"\"\n", "    <details>\n", "        <summary>GPT analysis</summary>\n", "        <p>{gpt_response}</p>\n", "    </details>\n", "    \"\"\"\n", "    html += \"</ul>\"\n", "\n", "    return html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RETRIEVER = create_retriever(RETRIEVER_CONFIG)\n", "RETRIEVER.load()\n", "\n", "gpt = GptWrapper()\n", "\n", "data = load_data(DATA_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = data[0]\n", "RETRIEVER.remove_all_docs()\n", "RETRIEVER.add_docs(get_docs(sample))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["full_entries = []\n", "\n", "for j in range(5):\n", "    sample = data[j]\n", "    RETRIEVER.remove_all_docs()\n", "    RETRIEVER.add_docs(get_docs(sample))\n", "\n", "    for i in range(5):\n", "        question = sample[\"documents_with_questions\"][i][\"question\"]\n", "\n", "        print(f\"[{i}] Generating responses\")\n", "        responses, misc_info = generate_responses(question, RETRIEVER, N=5)\n", "\n", "        print(f\"[{i}] Running GPT4\")\n", "        unary_run_results = []\n", "        for response in tqdm(responses):\n", "            cur_result = unary_run(question, response[\"text\"], misc_info[\"chunks\"], gpt)\n", "            unary_run_results.append(cur_result)\n", "\n", "        full_entries.append({\n", "            \"question\": question,\n", "            \"responses\": responses,\n", "            \"unary_run_results\": unary_run_results\n", "        })"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for f_entry in full_entries:\n", "    print([[u[\"is_hallucination\"], u[\"explanation\"]] for u in f_entry[\"unary_run_results\"]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "\n", "counter = defaultdict(int)\n", "\n", "for f_entry in full_entries:\n", "    cur_total = sum([u[\"is_hallucination\"] for u in f_entry[\"unary_run_results\"]])\n", "    counter[cur_total] += 1\n", "    # print([[u[\"is_hallucination\"], u[\"explanation\"]] for u in f_entry[\"unary_run_results\"]])\n", "    \n", "counter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "HTML_START = \"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Synthetic Preferences</title>\n", "    <style>\n", "        .wide-line {\n", "            width: 100%;\n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }\n", "        .highlighted-text {\n", "            background-color: #f0f0f0; /* Light grey background */\n", "            color: #333; /* Dark grey text color */\n", "            padding: 15px; /* Padding around the text */\n", "            margin: 10px 0; /* Margin for spacing between items */\n", "            border-radius: 8px; /* Rounded corners */\n", "            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */\n", "            list-style-type: none; /* Remove default list styling */\n", "            font-family: Arial, sans-serif; /* Modern font */\n", "        }\n", "    </style>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for responses, unary_run_results in zip(all_responses, all_unary_results):\n", "\n", "rendered_parts = []\n", "for f_entry in tqdm(full_entries):\n", "    rendered_chunks = f_entry[\"unary_run_results\"][0][\"rendered_chunks\"]\n", "    rendered_chunks = markdown(rendered_chunks, extensions=[\"fenced_code\", \"codehilite\"])\n", "\n", "    rendered_answer_list = render_answers_list(f_entry)\n", "\n", "    cur_html = f\"\"\"<hr>\n", "    <h3>Question: {f_entry[\"question\"]}\n", "    <details>\n", "        <summary>Retrieved chunks</summary>\n", "        <p>{rendered_chunks}</p>\n", "    </details>\n", "    {rendered_answer_list}\n", "    \"\"\"\n", "\n", "    rendered_parts.append(cur_html)\n", "\n", "full_html = HTML_START + \"\".join(rendered_parts) + HTML_END\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"./t36.html\", \"w\") as f:\n", "    f.write(full_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(full_html)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Triton"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# python -m pip install tiktoken blobfile\n", "\n", "import requests\n", "\n", "from experimental.guy.apis.llama3_tokenizer import ChatFormat, Message, Tokenizer"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["class TritonClient:\n", "    def __init__(self, url: str):\n", "        self.tokenizer = Tokenizer(\"/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct/tokenizer.model\")\n", "        self.prompt_formatter = ChatFormat(self.tokenizer)\n", "        self.url = url\n", "\n", "    def generate(self, user_message: str):\n", "        dialog = [\n", "            Message(role=\"user\", content=user_message)\n", "        ]\n", "\n", "        full_prompt = self.tokenizer.decode(\n", "            self.prompt_formatter.encode_dialog_prompt(dialog)\n", "        )\n", "        payload = self.get_payload(full_prompt)\n", "        response_json = self.send_request(payload)\n", "\n", "        return response_json[\"text_output\"]\n", "\n", "    def get_payload(self, full_prompt):\n", "        payload = {\n", "            \"text_input\": full_prompt,\n", "            \"max_tokens\": 1000,\n", "            \"end_id\": self.tokenizer.special_tokens[\"<|eot_id|>\"],\n", "            \"stream\": <PERSON><PERSON><PERSON>,\n", "            \"temperature\": 0.8,\n", "            \"top_k\": 40,\n", "            \"top_p\": 0.95,\n", "            \"random_seed\": 49,\n", "            \"return_context_logits\": <PERSON><PERSON><PERSON>,\n", "            \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "            \"return_generation_logits\": <PERSON><PERSON><PERSON>,\n", "        }\n", "\n", "        return payload\n", "\n", "    def send_request(self, payload):\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "        response = requests.post(\n", "            f\"http://{self.url}/v2/models/ensemble/generate\",\n", "            headers=headers,\n", "            data=json.dumps(payload),\n", "            timeout=100,\n", "        )\n", "        response_json = response.json()\n", "\n", "        return response_json\n"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*************\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[59], line 18\u001b[0m\n\u001b[1;32m     16\u001b[0m     \u001b[38;5;28mprint\u001b[39m(ip)\n\u001b[1;32m     17\u001b[0m     client \u001b[38;5;241m=\u001b[39m TritonClient(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mip\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m:8004\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 18\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mBye!\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m)\n\u001b[1;32m     20\u001b[0m \u001b[38;5;66;03m# client = TritonClient(\"**************:8000\")\u001b[39;00m\n\u001b[1;32m     21\u001b[0m \n\u001b[1;32m     22\u001b[0m \u001b[38;5;66;03m# a = client.generate(\"Bye!\")\u001b[39;00m\n", "Cell \u001b[0;32mIn[39], line 16\u001b[0m, in \u001b[0;36mTritonClient.generate\u001b[0;34m(self, user_message)\u001b[0m\n\u001b[1;32m     12\u001b[0m full_prompt \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m<PERSON><PERSON><PERSON>\u001b[38;5;241m.\u001b[39mdecode(\n\u001b[1;32m     13\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprompt_formatter\u001b[38;5;241m.\u001b[39mencode_dialog_prompt(dialog)\n\u001b[1;32m     14\u001b[0m )\n\u001b[1;32m     15\u001b[0m payload \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_payload(full_prompt)\n\u001b[0;32m---> 16\u001b[0m response_json \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpayload\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response_json[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtext_output\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "Cell \u001b[0;32mIn[39], line 39\u001b[0m, in \u001b[0;36mTritonClient.send_request\u001b[0;34m(self, payload)\u001b[0m\n\u001b[1;32m     37\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msend_request\u001b[39m(\u001b[38;5;28mself\u001b[39m, payload):\n\u001b[1;32m     38\u001b[0m     headers \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mContent-Type\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mapplication/json\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[0;32m---> 39\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpost\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     40\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mhttp://\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murl\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m/v2/models/ensemble/generate\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     41\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     42\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdumps\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpayload\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     43\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m100\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     44\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     45\u001b[0m     response_json \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mjson()\n\u001b[1;32m     47\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response_json\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/api.py:115\u001b[0m, in \u001b[0;36mpost\u001b[0;34m(url, data, json, **kwargs)\u001b[0m\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    104\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request.\u001b[39;00m\n\u001b[1;32m    105\u001b[0m \n\u001b[1;32m    106\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    113\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 115\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpost\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjson\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjson\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[0;34m(method, url, **kwargs)\u001b[0m\n\u001b[1;32m     55\u001b[0m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[1;32m     56\u001b[0m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[1;32m     58\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m sessions\u001b[38;5;241m.\u001b[39mSession() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[0;32m---> 59\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msession\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/requests/adapters.py:486\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    483\u001b[0m     timeout \u001b[38;5;241m=\u001b[39m TimeoutSauce(connect\u001b[38;5;241m=\u001b[39mtimeout, read\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    485\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 486\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    487\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    488\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    489\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    494\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    495\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    496\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    497\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    498\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    500\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    501\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(err, request\u001b[38;5;241m=\u001b[39mrequest)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connectionpool.py:715\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    712\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_proxy(conn)\n\u001b[1;32m    714\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 715\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    716\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    717\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    718\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    719\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    720\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    721\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    722\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    723\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    725\u001b[0m \u001b[38;5;66;03m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    726\u001b[0m \u001b[38;5;66;03m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[38;5;66;03m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    728\u001b[0m \u001b[38;5;66;03m# mess.\u001b[39;00m\n\u001b[1;32m    729\u001b[0m response_conn \u001b[38;5;241m=\u001b[39m conn \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m release_conn \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connectionpool.py:416\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    414\u001b[0m         conn\u001b[38;5;241m.\u001b[39mrequest_chunked(method, url, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mhttplib_request_kw)\n\u001b[1;32m    415\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 416\u001b[0m         \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mhttplib_request_kw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    418\u001b[0m \u001b[38;5;66;03m# We are swallowing BrokenPipeError (errno.EPIPE) since the server is\u001b[39;00m\n\u001b[1;32m    419\u001b[0m \u001b[38;5;66;03m# legitimately able to close the connection after sending a valid response.\u001b[39;00m\n\u001b[1;32m    420\u001b[0m \u001b[38;5;66;03m# With this behaviour, the received response is still readable.\u001b[39;00m\n\u001b[1;32m    421\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBrokenPipeError\u001b[39;00m:\n\u001b[1;32m    422\u001b[0m     \u001b[38;5;66;03m# Python 3\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connection.py:244\u001b[0m, in \u001b[0;36mHTTPConnection.request\u001b[0;34m(self, method, url, body, headers)\u001b[0m\n\u001b[1;32m    242\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser-agent\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m (six\u001b[38;5;241m.\u001b[39mensure_str(k\u001b[38;5;241m.\u001b[39mlower()) \u001b[38;5;28;01mfor\u001b[39;00m k \u001b[38;5;129;01min\u001b[39;00m headers):\n\u001b[1;32m    243\u001b[0m     headers[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUser-Agent\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m _get_default_user_agent()\n\u001b[0;32m--> 244\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mHTTPConnection\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:1294\u001b[0m, in \u001b[0;36mHTTPConnection.request\u001b[0;34m(self, method, url, body, headers, encode_chunked)\u001b[0m\n\u001b[1;32m   1291\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrequest\u001b[39m(\u001b[38;5;28mself\u001b[39m, method, url, body\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, headers\u001b[38;5;241m=\u001b[39m{}, \u001b[38;5;241m*\u001b[39m,\n\u001b[1;32m   1292\u001b[0m             encode_chunked\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m):\n\u001b[1;32m   1293\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Send a complete request to the server.\"\"\"\u001b[39;00m\n\u001b[0;32m-> 1294\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:1340\u001b[0m, in \u001b[0;36mHTTPConnection._send_request\u001b[0;34m(self, method, url, body, headers, encode_chunked)\u001b[0m\n\u001b[1;32m   1336\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(body, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m   1337\u001b[0m     \u001b[38;5;66;03m# RFC 2616 Section 3.7.1 says that text default has a\u001b[39;00m\n\u001b[1;32m   1338\u001b[0m     \u001b[38;5;66;03m# default charset of iso-8859-1.\u001b[39;00m\n\u001b[1;32m   1339\u001b[0m     body \u001b[38;5;241m=\u001b[39m _encode(body, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbody\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m-> 1340\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mendheaders\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencode_chunked\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:1289\u001b[0m, in \u001b[0;36mHTTPConnection.endheaders\u001b[0;34m(self, message_body, encode_chunked)\u001b[0m\n\u001b[1;32m   1287\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1288\u001b[0m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m CannotSendHeader()\n\u001b[0;32m-> 1289\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_output\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessage_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencode_chunked\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:1048\u001b[0m, in \u001b[0;36mHTTPConnection._send_output\u001b[0;34m(self, message_body, encode_chunked)\u001b[0m\n\u001b[1;32m   1046\u001b[0m msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\r\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_buffer)\n\u001b[1;32m   1047\u001b[0m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_buffer[:]\n\u001b[0;32m-> 1048\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmsg\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1050\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m message_body \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1051\u001b[0m \n\u001b[1;32m   1052\u001b[0m     \u001b[38;5;66;03m# create a consistent interface to message_body\u001b[39;00m\n\u001b[1;32m   1053\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(message_body, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mread\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m   1054\u001b[0m         \u001b[38;5;66;03m# Let file-like take precedence over byte-like.  This\u001b[39;00m\n\u001b[1;32m   1055\u001b[0m         \u001b[38;5;66;03m# is needed to allow the current position of mmap'ed\u001b[39;00m\n\u001b[1;32m   1056\u001b[0m         \u001b[38;5;66;03m# files to be taken into account.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/http/client.py:986\u001b[0m, in \u001b[0;36mHTTPConnection.send\u001b[0;34m(self, data)\u001b[0m\n\u001b[1;32m    984\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    985\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mauto_open:\n\u001b[0;32m--> 986\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    987\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    988\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m NotConnected()\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connection.py:205\u001b[0m, in \u001b[0;36mHTTPConnection.connect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    204\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 205\u001b[0m     conn \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    206\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_conn(conn)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/connection.py:174\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    171\u001b[0m     extra_kw[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msocket_options\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msocket_options\n\u001b[1;32m    173\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 174\u001b[0m     conn \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_connection\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    175\u001b[0m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_dns_host\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mport\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mextra_kw\u001b[49m\n\u001b[1;32m    176\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    178\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketTimeout:\n\u001b[1;32m    179\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ConnectTimeoutError(\n\u001b[1;32m    180\u001b[0m         \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    181\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection to \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m timed out. (connect timeout=\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m)\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    182\u001b[0m         \u001b[38;5;241m%\u001b[39m (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtimeout),\n\u001b[1;32m    183\u001b[0m     )\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/urllib3/util/connection.py:85\u001b[0m, in \u001b[0;36mcreate_connection\u001b[0;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[1;32m     83\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m source_address:\n\u001b[1;32m     84\u001b[0m         sock\u001b[38;5;241m.\u001b[39mbind(source_address)\n\u001b[0;32m---> 85\u001b[0m     \u001b[43msock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43msa\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     86\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m sock\n\u001b[1;32m     88\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m socket\u001b[38;5;241m.\u001b[39merror \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["ALL_IPS = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "]\n", "\n", "for ip in ALL_IPS:\n", "    print(ip)\n", "    client = TritonClient(f\"{ip}:8004\")\n", "    print(client.generate(\"Bye!\"))\n", "\n", "# client = TritonClient(\"**************:8000\")\n", "\n", "# a = client.generate(\"Bye!\")"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bye! It was nice chatting with you. Have a great day!\n"]}], "source": ["print(a)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# tokenizer = Tokenizer(\"/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct/tokenizer.model\")\n", "# prompt_formatter = ChatFormat(tokenizer)\n", "\n", "# dialog = []\n", "\n", "# message = Message(role=\"user\", content=\"Hey!\")\n", "# dialog.append(message)\n", "\n", "# full_prompt = tokenizer.decode(\n", "#     prompt_formatter.encode_dialog_prompt(dialog)\n", "# )\n", "\n", "# dialog = []\n", "\n", "# message = Message(role=\"user\", content=\"Hey!\")\n", "# dialog.append(message)\n", "\n", "# full_prompt = tokenizer.decode(\n", "#     prompt_formatter.encode_dialog_prompt(dialog)\n", "# )\n", "\n", "# print(full_prompt)\n", "\n", "\n", "# payload = {\n", "#     \"text_input\": full_prompt,\n", "#     \"max_tokens\": 1000,\n", "#     \"end_id\": tokenizer.special_tokens[\"<|eot_id|>\"],\n", "#     \"stream\": <PERSON><PERSON><PERSON>,\n", "#     \"temperature\": 0.8,\n", "#     \"top_k\": 40,\n", "#     \"top_p\": 0.95,\n", "#     \"random_seed\": 49,\n", "#     \"return_context_logits\": <PERSON>als<PERSON>,\n", "#     \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "#     \"return_generation_logits\": <PERSON>als<PERSON>,\n", "# }\n", "\n", "# headers = {\"Content-Type\": \"application/json\"}\n", "\n", "# response = requests.post(\n", "#     \"http://**************:8000/v2/models/ensemble/generate\",\n", "#     headers=headers,\n", "#     data=json.dumps(payload),\n", "#     timeout=100,\n", "# )\n", "\n", "# response_json = response.json()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
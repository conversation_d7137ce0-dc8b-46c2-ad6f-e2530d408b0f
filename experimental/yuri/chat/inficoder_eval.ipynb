{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import yaml\n", "import json\n", "\n", "from pathlib import Path\n", "from dataclasses import dataclass\n", "from typing import Optional\n", "\n", "from research.eval.harness.systems.abs_system import get_system\n", "from research.eval.harness.systems.basic_RAG_system import RAGSystem # just so that it registered\n", "from research.core.model_input import ModelInput"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_INPUT_DATASET_PATH = Path(\"/home/<USER>/repos/inficoder-eval-framework/batched_prompts/suite_v2.0.0_dev.csv\")\n", "_CONFIG_DIR = Path(\"/home/<USER>/repos/inficoder-eval-framework\")\n", "dataset = pd.read_csv(_INPUT_DATASET_PATH, index_col=0)\n", "print(dataset.shape)\n", "\n", "# Check all grading keys\n", "# grading_keys = []\n", "# for i in range(len(dataset)):\n", "#     sample = dataset.iloc[i].to_dict()\n", "#     with open(CONFIG_DIR / sample[\"filename\"]) as f:\n", "#         sample_config = yaml.load(f, yaml.Loader)\n", "#     grading_keys.append(sample_config[\"grading\"].keys())\n", "# grading_keys\n", "\n", "# Fitlering out samples with unit tests\n", "filtered_samples = []\n", "for i in range(len(dataset)):\n", "    sample = dataset.iloc[i].to_dict()\n", "    with open(_CONFIG_DIR / sample[\"filename\"]) as f:\n", "        sample_config = yaml.load(f, yaml.Loader)\n", "    if \"unit_test\" in sample_config[\"grading\"]:\n", "        continue\n", "    if \"customized\" in sample_config[\"grading\"]:\n", "        continue\n", "    if \"blank_filling\" in sample_config[\"grading\"] and \"post_handler\" in sample_config[\"grading\"][\"blank_filling\"]:\n", "        continue\n", "    filtered_samples.append(sample)\n", "\n", "print(len(filtered_samples))\n", "\n", "with open(\"/mnt/efs/augment/data/eval/chat/inficoder_apr16/suite_v2.0.0_dev_filtered.json\", \"w\") as f:\n", "    json.dump(filtered_samples, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INPUT_DATASET_PATH = \"/mnt/efs/augment/data/eval/chat/inficoder_apr16/suite_v2.0.0_dev_filtered.json\"\n", "CONFIG_DIR = Path(\"/mnt/efs/augment/data/eval/chat/inficoder_apr16\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(INPUT_DATASET_PATH) as f:\n", "    dataset = json.load(f)\n", "dataset[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grading_keys = []\n", "for i in range(len(dataset)):\n", "    sample = dataset[i]\n", "    with open(CONFIG_DIR / sample[\"filename\"]) as f:\n", "        sample_config = yaml.load(f, yaml.Loader)\n", "    grading_keys.append(sample_config[\"grading\"].keys())\n", "grading_keys"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(dataset[1]['content_prompt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CONFIG = {\n", "    \"name\": \"basic_rag\",\n", "    \"model\": {\n", "        \"name\": \"fastbackward_deepseek_instruct\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/yury/binks/binks_v2_50781/\",\n", "        \"model_parallel_size\": 4,\n", "        \"seq_length\": 16384,\n", "        \"override_prompt_formatter\": {\n", "            \"name\": \"chat_template\",\n", "            \"template_file\": \"/home/<USER>/repos/augment/research/model_server/prompts/chat/deepseek_instruct_with_retrieval_template.txt\",\n", "            \"tokenizer_name\": \"deepseekcoderinstructtokenizer\",\n", "            \"lines_in_prefix_suffix\": 0,\n", "            \"lines_in_prefix_suffix_empty_selection\": 75,\n", "            \"last_n_turns_in_chat_history\": 10\n", "        }\n", "    },\n", "    \"generation_options\": {\n", "        \"temperature\": 0,\n", "        \"top_k\": 0,\n", "        \"top_p\": 0,\n", "        \"max_generated_tokens\": 1024\n", "    },\n", "    \"retriever\": {\n", "        \"scorer\": {\n", "            \"name\": \"ethanol\",\n", "            \"checkpoint_path\": \"ethanol/ethanol6-16.1\"\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 30\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"ethanol6_query_simple_chat\",\n", "            \"max_tokens\": 1023,\n", "            \"add_path\": True,\n", "            \"verbose\": True\n", "        },\n", "        \"document_formatter\": {\n", "            \"name\": \"ethanol6_document\",\n", "            \"max_tokens\": 999,\n", "            \"add_path\": True\n", "        }\n", "    },\n", "    \"experimental\": {\n", "        \"remove_suffix\": False,\n", "        \"retriever_top_k\": 25,\n", "        \"trim_on_dedent\": <PERSON>alse,\n", "        \"trim_on_max_lines\": None\n", "    },\n", "    \"verbose\": True\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system = get_system(CONFIG[\"name\"], **CONFIG)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = dataset[100]\n", "\n", "sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = sample_to_model_input(sample)\n", "\n", "model_input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(CONFIG_DIR / sample[\"filename\"]) as f:\n", "    sample_config = yaml.load(f, yaml.Loader)\n", "\n", "sample_config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = system.generate(model_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_grad = grade_responses(sample_config, CONFIG_DIR / \"cases_dev\", [result.generated_text], 'avg', 1.0, 0.0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_grad"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/.local/lib/python3.11/site-packages/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "import json\n", "import re\n", "import random\n", "\n", "from google.cloud import bigquery\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from tqdm import tqdm\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from multiprocessing import Pool\n", "from collections import Counter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "# DATASET_NAME = \"staging_request_insight_full_export_dataset\"\n", "DATASET_NAME = \"prod_request_insight_full_export_dataset\"\n", "# TENANT_NAME = \"dogfood\"\n", "# TENANT_NAME = \"aitutor-mercor\"\n", "TENANT_NAME = \"aitutor-turing\"\n", "\n", "START_TIME = \"2024-06-01\"\n", "\n", "def get_all_samples():\n", "    query = f\"\"\"\n", "SELECT\n", "  REQUEST.request_id AS request_id_request,\n", "  REQUEST.raw_json as raw_json_request,\n", "\n", "  RESPONSE.request_id AS request_id_response,\n", "  RESPONSE.raw_json as raw_json_response,\n", "\n", "  META.request_id AS request_id_meta,\n", "  META.raw_json as raw_json_meta\n", "FROM\n", "  `{PROJECT_ID}.{DATASET_NAME}.request_event` AS REQUEST\n", "JOIN\n", "  `{PROJECT_ID}.{DATASET_NAME}.request_event` AS RESPONSE ON REQUEST.request_id = RESPONSE.request_id\n", "JOIN\n", "  `{PROJECT_ID}.{DATASET_NAME}.request_event` AS META ON REQUEST.request_id = META.request_id\n", "WHERE\n", "  REQUEST.event_type = 'chat_host_request'\n", "  AND RESPONSE.event_type = 'chat_host_response'\n", "  AND META.event_type = 'request_metadata'\n", "  AND REQUEST.tenant = '{TENANT_NAME}'\n", "  AND REQUEST.time > TIMESTAMP(\"{START_TIME}\")\n", "  AND NOT JSON_EXTRACT_SCALAR(META.raw_json, '$.user_id') IN ('intern-chat-proto', 'health-check-1')\n", "LIMIT\n", "  10000;\n", "\"\"\"\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    rows = [*client.query_and_wait(query)]\n", "\n", "    return rows\n", "\n", "def get_preferences_samples():\n", "    query = f\"\"\"\n", "SELECT *\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_event\n", "WHERE event_type=\"preference_sample\" AND tenant=\"{TENANT_NAME}\" \n", "AND time > TIMESTAMP(\"{START_TIME}\")\n", "ORDER BY time DESC\n", "LIMIT 1000000\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    rows = [*client.query_and_wait(query)]\n", "\n", "    clean_rows = []\n", "    for row in rows:\n", "        clean_rows.append({\n", "            \"preference_request_id\": row.request_id,\n", "            **dict(row)[\"raw_json\"]\n", "        })\n", "\n", "    return clean_rows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_samples = get_all_samples()\n", "print(len(all_samples))\n", "for sample in tqdm(all_samples):\n", "    assert sample.request_id_request == sample.request_id_response == sample.request_id_meta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_samples = list(map(dict, all_samples))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sample2id(m):\n", "    return f\"{m['raw_json_request']['request']['message']}_{m['raw_json_request']['request'].get('selected_code', '')}_{m['raw_json_request']['request'].get('path', '')}\"\n", "\n", "sample_id_set = set()\n", "deduplicated_samples = []\n", "for sample in tqdm(all_samples):\n", "    cur_id = sample2id(sample)\n", "    if cur_id not in sample_id_set:\n", "        sample_id_set.add(cur_id)\n", "        deduplicated_samples.append(sample)\n", "\n", "len(deduplicated_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples_with_selected_code = list(\n", "    filter(\n", "        lambda s: \"selected_code\" in s['raw_json_request'][\"request\"],\n", "        deduplicated_samples,\n", "    )\n", ")\n", "\n", "len(samples_with_selected_code)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CLASSIFICATION_PROMPT = \"\"\"{formatted_sample}\n", "\n", "Your task is to detect if user's last message instructs AI coding assistant to modify the highlighted code.\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze user's last message and it's relation to the highlighted code.\n", "2. Explicitly write YES (if user's last message instructs AI coding assistant to modify the highlighted code) or NO (if not).\n", "\n", "Use output format:\n", "Analysis: ...\n", "Verdict: YES/NO\n", "\"\"\"\n", "\n", "def format_history(history):\n", "    result_text = \"\"\n", "    for exchange in history:\n", "        result_text += f\"User: {exchange['request_message']}\\n\"\n", "        result_text += f\"Assistant: {exchange['response_text']}\\n\\n\\n\"\n", "    return result_text\n", "\n", "def format_sample(sample):\n", "    if \"chat_history\" in sample[\"raw_json_request\"][\"request\"]:\n", "        result_text = f\"\"\"Here is a history of conversation between user and AI coding assistant builtin into VSCode:\n", "    {format_history(sample[\"raw_json_request\"][\"request\"][\"chat_history\"])}\n", "    \"\"\"\n", "        result_text += f\"\"\"Here is the last message from user:\n", "{sample[\"raw_json_request\"][\"request\"][\"message\"]}\n", "\"\"\"\n", "    else:\n", "        result_text = f\"\"\"Here is a message from user to AI coding assistant builtin into VSCode:\n", "{sample[\"raw_json_request\"][\"request\"][\"message\"]}\n", " \"\"\"\n", "         \n", "    result_text += f\"\"\"Here is a piece of code that is highlighted in VSCode. \n", "File {sample[\"raw_json_request\"][\"request\"][\"path\"]}:\n", "```\n", "{sample[\"raw_json_request\"][\"request\"][\"selected_code\"]}\n", "```\n", "\"\"\"\n", "    return result_text\n", "\n", "def classify_sample(inputs):\n", "    sample, gpt = inputs\n", "    prompt = CLASSIFICATION_PROMPT.format(formatted_sample=format_sample(sample))\n", "    \n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "    \n", "    result = {\n", "        \"prompt\": prompt,\n", "        \"full_gpt_response\": gpt4_response,\n", "    }\n", "    verdict_match = re.search(r\"Verdict:\\s*(.*)\", gpt4_response)\n", "    if verdict_match:\n", "        verdict = verdict_match.group(1).strip()\n", "        if verdict in [\"YES\", \"Yes\", \"yes\"]:\n", "            result[\"verdict\"] = True\n", "        elif verdict in [\"NO\", \"No\", \"no\"]:\n", "            result[\"verdict\"] = False\n", "        else:\n", "            result[\"verdict\"] = None\n", "    else:\n", "        result[\"verdict\"] = None\n", "    \n", "    return result\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from markdown import markdown\n", "\n", "\n", "def render_simple_message(message, response, selected_code=None):\n", "    message = markdown(message, extensions=[\"fenced_code\", \"codehilite\"])\n", "    response = markdown(response, extensions=[\"fenced_code\", \"codehilite\"])\n", "\n", "    if selected_code is not None:\n", "        selected_code = f\"\"\"```\n", "{selected_code}\n", "```\"\"\"\n", "        selected_code = markdown(selected_code, extensions=[\"fenced_code\", \"codehilite\"])\n", "        html_content = f\"\"\"\n", "    <div class=\"row\">\n", "        <div class=\"answer\" style=\"flex: 1;\">{selected_code}</div>\n", "    </div>\n", "        \"\"\"\n", "    else:\n", "        html_content = \"\"\n", "\n", "    html_content += f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row\">\n", "    <div class=\"answer\" style=\"flex: 1;\">{response}</div>\n", "</div>\n", "\"\"\"\n", "\n", "    return html_content\n", "\n", "\n", "def wrap_html(html_content):\n", "    start = \"\"\"\n", "    <html>\n", "    <head>\n", "        <title>Question and Answers</title>\n", "        <style>\n", "            .row {\n", "                display: flex;\n", "                justify-content: center;\n", "                align-items: center;\n", "                margin-bottom: 20px;\n", "            }\n", "            .feedback, .answer {\n", "                margin: 10px;\n", "                padding: 10px;\n", "                border: 1px solid #ddd;\n", "                border-radius: 5px;\n", "                background-color: #f9f9f9;\n", "            }\n", "        </style>\n", "    </head>\n", "    <body>\n", "\"\"\"\n", "\n", "    end = \"\"\"\n", "        </body>\n", "    </html>\"\"\"\n", "\n", "    return \"\\n\".join([start, html_content, end])\n", "\n", "def render_sample(sample):\n", "    cur_index = 0\n", "    html_content = \"\"\n", "    if \"chat_history\" in sample[\"raw_json_request\"][\"request\"]:\n", "        for exchange in sample[\"raw_json_request\"][\"request\"][\"chat_history\"]:\n", "            html_content += f\"<h3>Message {cur_index}</h3>\"\n", "            cur_index += 1\n", "            html_content += render_simple_message(exchange[\"request_message\"], exchange[\"response_text\"])\n", "            html_content += \"<hr>\"\n", "        html_content = f\"\"\"\n", "        <details>\n", "            <summary>Chat history</summary>\n", "            {html_content}\n", "        </details>\n", "        \"\"\"\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "    html_content += render_simple_message(\n", "        sample[\"raw_json_request\"][\"request\"][\"message\"],\n", "        sample[\"raw_json_response\"][\"response\"][\"text\"],\n", "        sample[\"raw_json_request\"][\"request\"][\"selected_code\"] if \"selected_code\" in sample[\"raw_json_request\"][\"request\"] else None\n", "    )\n", "\n", "    return html_content\n", "\n", "def request_id_to_link(request_id):\n", "    return f\"https://support.dogfood.t.us-central1.prod.augmentcode.com/t/dogfood/request/{request_id}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()\n", "# cur_result = classify_sample((sample, gpt))\n", "# print(cur_result[\"verdict\"])\n", "# print(cur_result[\"full_gpt_response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt_inputs = [\n", "    (sample, gpt) for sample in samples_with_selected_code\n", "]\n", "\n", "with Pool(50) as pool:\n", "    classification_results = list(tqdm(pool.imap(classify_sample, gpt_inputs), total=len(gpt_inputs)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(Counter([result[\"verdict\"] for result in classification_results]))\n", "gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html = \"\"\n", "\n", "for i, (sample, classification_result) in tqdm(enumerate(zip(samples_with_selected_code, classification_results))):\n", "    cur_html = f\"<h2>Chain {i}</h2>\"\n", "    cur_html += f'Request ID: <a href=\"{request_id_to_link(sample[\"request_id_request\"])}\">{sample[\"request_id_request\"]}</a>'\n", "    cur_html += f\"<p>Verdict: {classification_result['verdict']}</p>\"\n", "    cur_html += f\"\"\"\n", "    <details>\n", "        <summary>Classification prompt</summary>\n", "        <p>{markdown(classification_result[\"prompt\"])}</p>\n", "    </details>\n", "    \n", "    <details>\n", "        <summary>Full GPT response</summary>\n", "        <p>{markdown(classification_result[\"full_gpt_response\"])}</p>\n", "    </details>\n", "    \"\"\"\n", "    \n", "    cur_html += render_sample(sample)\n", "    \n", "    html += cur_html\n", "    html += \"<hr>\"\n", "\n", "html = wrap_html(html)\n", "\n", "with open(\"./q4.html\", \"w\") as f:\n", "    f.write(html)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_samples = [\n", "    sample for (sample, classification_result) in zip(samples_with_selected_code, classification_results) if classification_result[\"verdict\"]\n", "]\n", "len(target_samples)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REFINEMENT_PROMPT = \"\"\"{formatted_sample}\n", "\n", "If user's last message instructs AI coding assistant to modify the highlighted code, and it can be reasonably done within boundaries the highlighted code, \n", "then response should include a full version of modified code within the boundaries of the highlighted code.\n", "\n", "I.e if lines [A, B] selected:\n", "- Code snippets in output should include modified lines [A, B]. Not [A-N, B+N]\n", "- Output should not use any shortenings like ... or [REST OF THE CODE]\n", "\n", "Your task is to refine AI coding assistant's response if it doesn't meet the described criteria.\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze previous message, highlighted code and AI coding assistant's response.\n", "2. Decide if last message instructs to modify the highlighted code.\n", "3. If last message instructs to modify the highlighted code, decide if previous \n", "\n", "Use output format:\n", "Analysis: ...\n", "Refinement needed: YES/NO\n", "Refined response: ...\n", "\"\"\"\n", "\n", "def render_chunks(chunks: list[dict]):\n", "    per_chunk = []\n", "    for c in chunks:\n", "        cur_text = f\"\"\"\n", "\n", "Here is the snippet from `{c['path']}`:\n", "\n", "```\n", "{c.get('text', '')}\n", "```\n", "\n", "\"\"\"\n", "        per_chunk.append(cur_text)\n", "\n", "    return \"\".join(per_chunk)\n", "\n", "\n", "def refine_sample(inputs):\n", "    sample, gpt = inputs\n", "    \n", "    messages = []\n", "\n", "    assert len(sample[\"raw_json_request\"][\"request\"].get(\"selected_code\", \"\")) > 0\n", "    if len(sample[\"raw_json_request\"][\"request\"].get(\"prefix\", \"\")) > 0 or len(sample[\"raw_json_request\"][\"request\"].get(\"suffix\", \"\")) > 0:\n", "        selected_prompt = f\"\"\"I have a file `{sample[\"raw_json_request\"][\"request\"][\"path\"]}` open in VSCode and have selected (highlighted) part of the code. The content of this file is\n", "```\n", "{sample[\"raw_json_request\"][\"request\"].get(\"prefix\", \"\")}\n", "[START SELECTED REGION]\n", "...\n", "[selected code goes here]\n", "...\n", "[END SELECTED REGION]\n", "{sample[\"raw_json_request\"][\"request\"].get(\"suffix\", \"\")}\n", "```\n", "\n", "Here is the selected code\n", "```\n", "{sample[\"raw_json_request\"][\"request\"][\"selected_code\"]}\n", "```\n", "\"\"\"\n", "    else:\n", "        selected_prompt = f\"\"\"I have a file `{sample[\"raw_json_request\"][\"request\"][\"path\"]}` open in VSCode and have selected (highlighted) the whole file. The content of this file is\n", "```\n", "{sample[\"raw_json_request\"][\"request\"][\"selected_code\"]}\n", "```\n", "\"\"\"\n", "\n", "    system_prompt = f\"\"\"Here are chunks from my codebase:\n", "{render_chunks(sample[\"raw_json_request\"].get(\"retrieved_chunks\", []))}\n", "\n", "{selected_prompt}\n", "\"\"\"\n", "    for exchange in sample[\"raw_json_request\"][\"request\"].get(\"chat_history\", []):\n", "        messages.append({\"role\": \"user\", \"content\": exchange[\"request_message\"]})\n", "        messages.append({\"role\": \"assistant\", \"content\": exchange[\"response_text\"]})\n", "\n", "    messages.append({\"role\": \"user\", \"content\": sample[\"raw_json_request\"][\"request\"][\"message\"]})\n", "    messages.append({\"role\": \"assistant\", \"content\": sample[\"raw_json_response\"][\"response\"][\"text\"]})\n", "    \n", "    refinement_message = f\"\"\"\n", "Here is response you just provided enclosed between ~~~:\n", "~~~\n", "{sample[\"raw_json_response\"][\"response\"][\"text\"]}\n", "~~~\n", "    \n", "Please refine this response by ensuring that code snippets can be easily copy-pasted into codebase instead of selected code. \n", "I.e. Whenever possible, respect boundaries of the highlighted code. Never use any shortenings like ... or [REST OF THE CODE]\n", "\n", "Examples:\n", "1. \n", "    ### Selected code: \n", "        ```\n", "        def func(a, b):\n", "            c = a + b\n", "        ```\n", "    ### Instruction: \"Add docstring to function\"\n", "    ### Response:\n", "        ~~~\n", "        Here is the modified code:\n", "        ```\n", "        def func(a, b):\n", "            \\\"\\\"\\\"\n", "            Adds two numbers\n", "            \\\"\\\"\\\"\n", "            c = a + b\n", "            return c\n", "        ```\n", "        I've added docstring to function `func`\n", "        ~~~\n", "    ### Refined response:\n", "        ~~~\n", "        Here is the modified code:\n", "        ```\n", "        def func(a, b):\n", "            \\\"\\\"\\\"\n", "            Adds two numbers\n", "            \\\"\\\"\\\"\n", "            c = a + b\n", "        ```\n", "        I've added docstring to function `func`\n", "        ~~~\n", "\n", "2. \n", "    ### Selected code: \n", "        ```\n", "        GLOBAL_VARIABLE = 1\n", "        def func(a, b):\n", "            c = a + b\n", "            return c\n", "        ```\n", "    ### Instruction: \"Change func to multiply\"\n", "    ### Response:\n", "        ~~~\n", "        Sure, here is the updated code:\n", "        ```\n", "        def func(a, b):\n", "            c = a * b\n", "            return c\n", "        ```\n", "        ~~~\n", "    ### Refined response:\n", "        ~~~\n", "        Sure, here is the updated code:\n", "        ```\n", "        GLOBAL_VARIABLE = 1\n", "        def func(a, b):\n", "            c = a * b\n", "            return c\n", "        ```\n", "        ~~~\n", "        \n", "Please, return JUST the refined response and enclose it between ~~~.\n", "\"\"\"\n", "\n", "    messages.append({\"role\": \"user\", \"content\": refinement_message})\n", "    # return messages, system_prompt\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0, system_prompt=system_prompt)\n", "    \n", "    result = {\n", "        \"prompt\": refinement_message,\n", "        \"full_gpt_response\": gpt4_response,\n", "    }\n", "    \n", "    \n", "    return result\n", "\n", "\n", "# def format_sample_for_refinement(sample):\n", "#     assert len(sample[\"raw_json_request\"][\"retrieved_chunks\"]) > 0\n", "#     result_text = f\"\"\"Here are chunks retrieved from codebase by AI coding assistant:\n", "# {render_chunks(sample[\"raw_json_request\"][\"retrieved_chunks\"])}\n", "# \"\"\"\n", "#     if \"chat_history\" in sample[\"raw_json_request\"][\"request\"]:\n", "#         result_text += f\"\"\"Here is a history of conversation between user and AI coding assistant builtin into VSCode:\n", "# {format_history(sample[\"raw_json_request\"][\"request\"][\"chat_history\"])}\n", "# \"\"\"\n", "#         result_text += f\"\"\"Here is the last message from user:\n", "# {sample[\"raw_json_request\"][\"request\"][\"message\"]}\n", "# \"\"\"\n", "#     else:\n", "#         result_text += f\"\"\"Here is a message from user to AI coding assistant builtin into VSCode:\n", "# {sample[\"raw_json_request\"][\"request\"][\"message\"]}\n", "#  \"\"\"\n", "#     result_text += f\"\"\"Here is a piece of code that is highlighted in VSCode. \n", "# File {sample[\"raw_json_request\"][\"request\"][\"path\"]}:\n", "# ```\n", "# {sample[\"raw_json_request\"][\"request\"][\"selected_code\"]}\n", "# ```\n", "# \"\"\"\n", "\n", "#     result_text += f\"\"\"Here is a response from AI coding assistant:\n", "# {sample[\"raw_json_response\"][\"response\"][\"text\"]}\n", "# \"\"\"\n", "\n", "#     return result_text\n", "\n", "# def refine_sample(inputs):\n", "#     sample, gpt = inputs\n", "#     prompt = REFINEMENT_PROMPT.format(\n", "#         formatted_sample=format_sample_for_refinement(sample)\n", "#     )\n", "    \n", "#     messages = [{\"role\": \"user\", \"content\": prompt}]\n", "#     gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "    \n", "#     result = {\n", "#         \"prompt\": prompt,\n", "#         \"full_gpt_response\": gpt4_response,\n", "#     }\n", "    \n", "#     verdict_match = re.search(r\"Refinement needed:\\s*(.*)\", gpt4_response)\n", "#     if verdict_match:\n", "#         verdict = verdict_match.group(1).strip()\n", "#         if verdict in [\"YES\", \"Yes\", \"yes\"]:\n", "#             result[\"refinement_needed\"] = True\n", "#         elif verdict in [\"NO\", \"No\", \"no\"]:\n", "#             result[\"refinement_needed\"] = False\n", "#         else:\n", "#             result[\"refinement_needed\"] = None\n", "#     else:\n", "#         result[\"refinement_needed\"] = None\n", "    \n", "#     refinement_match = re.search(r\"Refined response:\\s*(.*)\", gpt4_response)\n", "#     if refinement_match:\n", "#         result[\"refined_response\"] = refinement_match.group(1).strip()\n", "#     else:\n", "#         result[\"refined_response\"] = None\n", "    \n", "#     return result, sample\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt_inputs = [\n", "    (sample, gpt) for sample in target_samples\n", "]\n", "\n", "with Pool(50) as pool:\n", "    refinement_results = list(tqdm(pool.imap(refine_sample, gpt_inputs), total=len(gpt_inputs)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compare_responses(inputs):\n", "    sample, gpt, refined_response = inputs\n", "\n", "    assert not refined_response.startswith(\"~~~\\n\") \n", "    # assert refined_response.startswith(\"~~~\\n\") and refined_response.endswith(\"\\n~~~\")\n", "    # refined_response = refined_response[4:-4]\n", "\n", "    messages = []\n", "\n", "    assert len(sample[\"raw_json_request\"][\"request\"].get(\"selected_code\", \"\")) > 0\n", "    if len(sample[\"raw_json_request\"][\"request\"].get(\"prefix\", \"\")) > 0 or len(sample[\"raw_json_request\"][\"request\"].get(\"suffix\", \"\")) > 0:\n", "        selected_prompt = f\"\"\"I have a file `{sample[\"raw_json_request\"][\"request\"][\"path\"]}` open in VSCode and have selected (highlighted) part of the code. The content of this file is\n", "    ```\n", "    {sample[\"raw_json_request\"][\"request\"].get(\"prefix\", \"\")}\n", "    [START SELECTED REGION]\n", "    ...\n", "    [selected code goes here]\n", "    ...\n", "    [END SELECTED REGION]\n", "    {sample[\"raw_json_request\"][\"request\"].get(\"suffix\", \"\")}\n", "    ```\n", "\n", "    Here is the selected code\n", "    ```\n", "    {sample[\"raw_json_request\"][\"request\"][\"selected_code\"]}\n", "    ```\n", "    \"\"\"\n", "    else:\n", "        selected_prompt = f\"\"\"I have a file `{sample[\"raw_json_request\"][\"request\"][\"path\"]}` open in VSCode and have selected (highlighted) the whole file. The content of this file is\n", "    ```\n", "    {sample[\"raw_json_request\"][\"request\"][\"selected_code\"]}\n", "    ```\n", "    \"\"\"\n", "\n", "    system_prompt = f\"\"\"Here are chunks from my codebase:\n", "    {render_chunks(sample[\"raw_json_request\"].get(\"retrieved_chunks\", []))}\n", "\n", "    {selected_prompt}\n", "    \"\"\"\n", "    for exchange in sample[\"raw_json_request\"][\"request\"].get(\"chat_history\", []):\n", "        messages.append({\"role\": \"user\", \"content\": exchange[\"request_message\"]})\n", "        messages.append({\"role\": \"assistant\", \"content\": exchange[\"response_text\"]})\n", "\n", "    comparison_message = f\"\"\"Here is next message from user:\n", "    {sample[\"raw_json_request\"][\"request\"][\"message\"]}\n", "\n", "    And here are two responses enclosed between ~~~:\n", "    1. \n", "    ~~~\n", "    {sample[\"raw_json_response\"][\"response\"][\"text\"]}\n", "    ~~~\n", "    2.\n", "    ~~~\n", "    {refined_response}\n", "    ~~~\n", "\n", "    Your task is to compare these two responses and decide which one\n", "    includes code snippets that can be easier copy-pasted into codebase instead of selected code. \n", "    I.e. They respect boundaries of the highlighted code more. Never use any shortenings like ... or [REST OF THE CODE]\n", "\n", "    If both responses are similar in that regard, then return Same.\n", "\n", "    Use output format:\n", "    Analysis: ...\n", "    Verdict: 1/2/Same\n", "    \"\"\"\n", "\n", "    messages.append({\"role\": \"user\", \"content\": comparison_message})\n", "    \n", "    try:\n", "        gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0, system_prompt=system_prompt)\n", "    except Exception as e:\n", "        print(f\"Failed to compare responses for sample {sample['request_id_request']}: {e}\")\n", "        return {\n", "            \"prompt\": comparison_message,\n", "            \"full_gpt_response\": str(e),\n", "            \"verdict\": None,\n", "        }\n", "    \n", "    result = {\n", "        \"prompt\": comparison_message,\n", "        \"full_gpt_response\": gpt4_response,\n", "    }\n", "    verdict_match = re.search(r\"Verdict:\\s*(.*)\", gpt4_response)\n", "    if verdict_match:\n", "        verdict = verdict_match.group(1).strip()\n", "        if verdict in [\"1\"]:\n", "            result[\"verdict\"] = 1\n", "        elif verdict in [\"2\"]:\n", "            result[\"verdict\"] = 2\n", "        elif verdict in [\"Same\", \"same\"]:\n", "            result[\"verdict\"] = 0\n", "        else:\n", "            result[\"verdict\"] = None\n", "    else:\n", "        result[\"verdict\"] = None\n", "        \n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt_inputs = [\n", "    (sample, gpt, refinement_result[\"full_gpt_response\"]) for sample, refinement_result in zip(target_samples, refinement_results)\n", "]\n", "\n", "with Pool(50) as pool:\n", "    comparison_results = list(tqdm(pool.imap(compare_responses, gpt_inputs), total=len(gpt_inputs)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(map(lambda x: x[\"verdict\"], comparison_results))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html = \"\"\n", "\n", "for i, (sample, refinement_result, comparison_result) in tqdm(enumerate(zip(target_samples, refinement_results, comparison_results))):\n", "    cur_html = f\"<h2>Chain {i}</h2>\"\n", "    cur_html += f'Request ID: <a href=\"{request_id_to_link(sample[\"request_id_request\"])}\">{sample[\"request_id_request\"]}</a>'\n", "    # cur_html += f\"<p>Previous message instructs to modify the highlighted code : {refinement_result['is_instruct']}</p>\"\n", "    cur_html += f\"<p>Comparison result : {comparison_result['verdict']}</p>\"\n", "    cur_html += f\"\"\"\n", "    <details>\n", "        <summary>Refinement prompt</summary>\n", "        <p>{markdown(refinement_result[\"prompt\"], extensions=[\"fenced_code\", \"codehilite\"])}</p>\n", "    </details>\n", "    \n", "    <details>\n", "        <summary>Full GPT refinement response</summary>\n", "        <p>{markdown(refinement_result[\"full_gpt_response\"], extensions=[\"fenced_code\", \"codehilite\"])}</p>\n", "    </details>\n", "    \n", "    <details>\n", "        <summary>Comparison prompt</summary>\n", "        <p>{markdown(comparison_result[\"prompt\"], extensions=[\"fenced_code\", \"codehilite\"])}</p>\n", "    </details>\n", "    \n", "    <details>\n", "        <summary>Full GPT comparison response</summary>\n", "        <p>{markdown(comparison_result[\"full_gpt_response\"], extensions=[\"fenced_code\", \"codehilite\"])}</p>\n", "    </details>\n", "    \n", "    \"\"\"\n", "    \n", "    cur_html += render_sample(sample)\n", "    \n", "    cur_clean_response = refinement_result[\"full_gpt_response\"]\n", "    if cur_clean_response.startswith(\"~~~\\n\") and cur_clean_response.endswith(\"\\n~~~\"):\n", "        cur_clean_response = cur_clean_response[4:-4]\n", "        cur_clean_response = markdown(cur_clean_response, extensions=[\"fenced_code\", \"codehilite\"])\n", "    else:\n", "        cur_clean_response = \"FAILED PARSING\"\n", "    \n", "    cur_html += f\"\"\"\n", "<div class=\"row\">\n", "    <div class=\"answer\" style=\"flex: 1;\">{cur_clean_response}</div>\n", "</div>  \n", "\"\"\"\n", "    \n", "    html += cur_html\n", "    html += \"<hr>\"\n", "\n", "html = wrap_html(html)\n", "\n", "with open(\"./r25.html\", \"w\") as f:\n", "    f.write(html)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=0,\n", "    chat_history_len=2048,\n", "    prefix_len=1024,\n", "    selected_code_len=0,\n", "    suffix_len=1024,\n", "    max_prompt_len=6144,\n", "    retrieval_len=-1\n", ")\n", "PROMPT_FORMATTER = get_chat_prompt_formatter_by_name(\n", "    \"binks_llama3\",\n", "    TOKENIZER,\n", "    APPORTIONMENT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SEQUENCE_LENGTH = 8192\n", "\n", "def tokenize_sample(sample, output_text):    \n", "    from base.prompt_format.common import Exchange, PromptChunk\n", "\n", "    cur_chunks = [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            char_start=chunk.get(\"char_offset\", 0),\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk[\"blob_name\"],\n", "            origin=chunk[\"origin\"],\n", "        )\n", "        for chunk in sample[\"raw_json_request\"].get(\"retrieved_chunks\", []) if \"text\" in chunk\n", "    ]\n", "\n", "    cur_history = [\n", "        Exchange(\n", "            request_message=exchange[\"request_message\"],\n", "            response_text=exchange[\"response_text\"],\n", "        )\n", "        for exchange in sample[\"raw_json_request\"][\"request\"].get(\"chat_history\", [])\n", "    ]\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=sample[\"raw_json_request\"][\"request\"].get(\"path\", \"\"),\n", "        prefix=sample[\"raw_json_request\"][\"request\"].get(\"prefix\", \"\"),\n", "        selected_code=sample[\"raw_json_request\"][\"request\"].get(\"selected_code\", \"\"),\n", "        suffix=sample[\"raw_json_request\"][\"request\"].get(\"suffix\", \"\"),\n", "        message=sample[\"raw_json_request\"][\"request\"][\"message\"],\n", "        chat_history=cur_history,\n", "        prefix_begin=sample[\"raw_json_request\"][\"request\"].get(\"prefix_begin\", 0),\n", "        suffix_end=sample[\"raw_json_request\"][\"request\"].get(\"suffix_end\", 0),\n", "        retrieved_chunks=cur_chunks,\n", "    )\n", "    \n", "    tokenized_input = PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "    tokenized_output = TOKENIZER.tokenize_safe(output_text) + [TOKENIZER.special_tokens.eos]\n", "    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output\n", "    complete_prompt += [-1 * TOKENIZER.special_tokens.eos] * (\n", "        SEQUENCE_LENGTH - len(complete_prompt) + 1\n", "    )\n", "\n", "    return complete_prompt\n", "    \n", "    \n", "def tokenize_input(sample, output_text):    \n", "    from base.prompt_format.common import Exchange, PromptChunk\n", "\n", "    cur_chunks = [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            char_start=chunk.get(\"char_offset\", 0),\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk[\"blob_name\"],\n", "            origin=chunk[\"origin\"],\n", "        )\n", "        for chunk in sample[\"raw_json_request\"].get(\"retrieved_chunks\", []) if \"text\" in chunk\n", "    ]\n", "\n", "    cur_history = [\n", "        Exchange(\n", "            request_message=exchange[\"request_message\"],\n", "            response_text=exchange[\"response_text\"],\n", "        )\n", "        for exchange in sample[\"raw_json_request\"][\"request\"].get(\"chat_history\", [])\n", "    ]\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=sample[\"raw_json_request\"][\"request\"].get(\"path\", \"\"),\n", "        prefix=sample[\"raw_json_request\"][\"request\"].get(\"prefix\", \"\"),\n", "        selected_code=sample[\"raw_json_request\"][\"request\"].get(\"selected_code\", \"\"),\n", "        suffix=sample[\"raw_json_request\"][\"request\"].get(\"suffix\", \"\"),\n", "        message=sample[\"raw_json_request\"][\"request\"][\"message\"],\n", "        chat_history=cur_history,\n", "        prefix_begin=sample[\"raw_json_request\"][\"request\"].get(\"prefix_begin\", 0),\n", "        suffix_end=sample[\"raw_json_request\"][\"request\"].get(\"suffix_end\", 0),\n", "        retrieved_chunks=cur_chunks,\n", "    )\n", "    \n", "    tokenized_input = PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "\n", "    return tokenized_input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_target_samples = []\n", "for sample, refinement_result in tqdm(zip(target_samples, refinement_results)):\n", "    gt = refinement_result[\"full_gpt_response\"]\n", "    assert gt.startswith(\"~~~\\n\") and gt.endswith(\"\\n~~~\")\n", "    gt = gt[4:-4]\n", "    \n", "    tokenized_target_samples.append(tokenize_sample(sample, gt))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(list(map(abs, tokenized_target_samples[0]))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_target_samples[0][:10], tokenized_target_samples[0][-10:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples_without_selected_code = list(\n", "    filter(\n", "        lambda s: \"selected_code\" not in s['raw_json_request'][\"request\"],\n", "        deduplicated_samples,\n", "    )\n", ")\n", "\n", "non_target_samples = [\n", "    sample for (sample, classification_result) in zip(samples_with_selected_code, classification_results) if not classification_result[\"verdict\"]\n", "]\n", "\n", "len(samples_without_selected_code), len(non_target_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_general_samples = []\n", "for sample in tqdm(samples_without_selected_code + non_target_samples):\n", "    cur_prompt = [-1 * t for t in sample[\"raw_json_request\"][\"tokenization\"][\"token_ids\"]] + sample[\"raw_json_response\"][\"tokenization\"][\"token_ids\"]\n", "    cur_prompt += [TOKENIZER.special_tokens.eos]\n", "    cur_prompt += [-1 * TOKENIZER.special_tokens.eos] * (SEQUENCE_LENGTH - len(cur_prompt) + 1)\n", "\n", "    tokenized_general_samples.append(cur_prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(list(map(abs, tokenized_general_samples[0]))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_target_samples[0][:10], tokenized_target_samples[0][-10:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_tokenized_samples = tokenized_target_samples + tokenized_general_samples\n", "random.shuffle(all_tokenized_samples)\n", "\n", "len(all_tokenized_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_fraction = 0.95\n", "\n", "train_samples = all_tokenized_samples[: int(len(all_tokenized_samples) * train_fraction)]\n", "valid_samples = all_tokenized_samples[int(len(all_tokenized_samples) * train_fraction) :]\n", "\n", "len(train_samples), len(valid_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yuri/data/fix_pipeline_jun_30_v1\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "\n", "def save_dataset(samples, output_path):\n", "    random.shuffle(samples)\n", "    if not output_path.parent.exists():\n", "        output_path.parent.mkdir()\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sample in tqdm(samples):\n", "        builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    \n", "def get_mmap_samples(path: Path):\n", "    dataset = MMapIndexedDataset(str(path))\n", "    all_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        all_samples.append(dataset[i].tolist())\n", "    return all_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "save_dataset(train_samples, OUTPUT_PATH / \"train\")\n", "save_dataset(valid_samples, OUTPUT_PATH / \"valid\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1 = get_mmap_samples(Path(\"/mnt/efs/augment/user/yuri/data/fix_pipeline_jun_30_v1/train\"))\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in tqdm(range(len(ds1))):\n", "    cur_prompt = TOKENIZER.detokenize(list(map(abs, ds1[i])))\n", "    if \"replace section with a no data section if tabItemsData is empty\" in cur_prompt:\n", "        print(i)\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(list(map(abs, ds1[i]))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_samples = get_mmap_samples(OUTPUT_PATH / \"train\")\n", "valid_samples = get_mmap_samples(OUTPUT_PATH / \"valid\")\n", "\n", "len(train_samples), len(valid_samples)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_samples = train_samples + valid_samples\n", "\n", "random.shuffle(all_samples)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_fraction = 0.90\n", "\n", "train_samples = all_samples[: int(len(all_samples) * train_fraction)]\n", "valid_samples = all_samples[int(len(all_samples) * train_fraction) :]\n", "\n", "len(train_samples), len(valid_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yuri/data/fix_pipeline_jun_30_v3\")\n", "save_dataset(train_samples, OUTPUT_PATH / \"train\")\n", "save_dataset(valid_samples, OUTPUT_PATH / \"valid\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.fastforward.llama import model_specs as llama_model_specs\n", "from base.fastforward.llama.model_specs import _MODEL_SPECS\n", "from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from research.models.meta_model import GenerationOptions, get_model, register_model\n", "\n", "LLAMA3_70B_MODEL_SPECT = _MODEL_SPECS[\"llama3-70b\"]\n", "LLAMA3_70B_MODEL_SPECT.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp/llama3-jun30-v1-ffw\"\n", "# LLAMA3_70B_MODEL_SPECT.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp/llama3-jun20-forced-comment-mix-w-real-data-v2-1286-ffw\"\n", "# LLAMA3_70B_MODEL_SPECT.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp/llama3-jun20-basic-mix-w-real-data-v2-1286-ffw\"\n", "\n", "@register_model(\"fastforward_llama3_70b_instruct\")\n", "class FastForwardLLAMA3_70B_Instruct(LLAMA_FastForwardModel):\n", "    \"\"\"The open-source LLAMA3-70B-Instruct model.\"\"\"\n", "\n", "    seq_length: int = 8192\n", "\n", "    model_spec: llama_model_specs.LlamaModelSpec = LLAMA3_70B_MODEL_SPECT\n", "\n", "    @classmethod\n", "    def create_default_formatter(cls):\n", "        tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "        apportionment = ChatTokenApportionment(\n", "            path_len=256,\n", "            message_len=0,\n", "            chat_history_len=2048,\n", "            prefix_len=1024,\n", "            selected_code_len=0,\n", "            suffix_len=1024,\n", "            max_prompt_len=6144,\n", "            retrieval_len=-1\n", "        )\n", "\n", "        prompt_formatter = get_chat_prompt_formatter_by_name(\n", "            \"binks_llama3\",\n", "            tokenizer,\n", "            apportionment,\n", "        )\n", "        return prompt_formatter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model(\"fastforward_llama3_70b_instruct\")\n", "model.load()\n", "\n", "model.tokenizer.eod_id = model.tokenizer.special_tokens.eos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_target_inputs = []\n", "for sample, refinement_result in tqdm(zip(target_samples, refinement_results)):\n", "    gt = refinement_result[\"full_gpt_response\"]\n", "    assert gt.startswith(\"~~~\\n\") and gt.endswith(\"\\n~~~\")\n", "    gt = gt[4:-4]\n", "    \n", "    tokenized_target_inputs.append(tokenize_input(sample, gt))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(tokenized_target_inputs[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["generated_responses_for_target_samples = []\n", "for tokenized_input in tqdm(tokenized_target_inputs[:20]):\n", "    generated_responses_for_target_samples.append(\n", "        model.raw_generate(\n", "            tokenized_input,\n", "            GenerationOptions(max_generated_tokens=2048)\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt_inputs = [\n", "    (sample, gpt, inference_result) for sample, inference_result in zip(target_samples, generated_responses_for_target_samples)\n", "]\n", "\n", "with Pool(50) as pool:\n", "    comparison_results = list(tqdm(pool.imap(compare_responses, gpt_inputs), total=len(gpt_inputs)))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(map(lambda x: x[\"verdict\"], comparison_results))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "import pandas as pd\n", "import pyspark.sql\n", "\n", "from pathlib import Path\n", "from research.data.spark import k8s_session\n", "from collections import defaultdict\n", "from pyspark.sql import functions as F\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from pyspark.sql import SparkSession\n", "from base.prompt_format.common import TokenList\n", "from functools import partial\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from tqdm import tqdm\n", "from collections import Counter\n", "from base.tokenizers import create_tokenizer_by_name\n", "from dataclasses import asdict\n", "\n", "# For synthetic data\n", "REPOS_PATTERN = Path(\"/mnt/efs/augment/user/yury/binks/binks-v3.1\") / \"03_raw_repos/*.zstd.parquet\"\n", "SAMPLES_PATH = Path(\"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul27_5k_pro_simple_boundaries.json\")\n", "IS_BINKS_FORMAT = False\n", "\n", "# For binks data\n", "# REPOS_PATTERN = Path(\"/mnt/efs/augment/user/yury/binks/binks-v4\") / \"01_raw_repos/*.zstd.parquet\"\n", "# SAMPLES_PATH = Path(\"/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_withanswers.jsonl\")\n", "# IS_BINKS_FORMAT = True\n", "\n", "\n", "OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul27_5k_pro_simple_boundaries_new_prompt\")\n", "NUM_WORKERS = 75\n", "SEQ_LENGTH = 8192\n", "TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/jul_25_24_process_v10\"\n", "\n", "RETRIEVER_CONFIG = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_fbwd\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "        \"tokenizer_name\": \"StarCoderTokenizer\",\n", "    },\n", "}\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=0,\n", "    chat_history_len=2048,\n", "    prefix_len=1024,\n", "    selected_code_len=0,\n", "    suffix_len=1024,\n", "    max_prompt_len=6144,\n", "    retrieval_len=-1\n", ")\n", "SEQUENCE_LENGTH = 8192\n", "IS_FOR_INFERENCE = True # If True, it returns the full prompt without last assistant response\n", "\n", "\n", "OUTPUT_DIR.mkdir(parents=False, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_spark(job_name: str, max_workers: int) -> SparkSession:\n", "    return k8s_session(\n", "        name=job_name,\n", "        gpu_type=[\"A40\", \"RTX_A6000\"],\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        max_workers=max_workers,\n", "    )\n", "    \n", "def format_sample_for_retrieval(sample):\n", "    prompt = \"\"\n", "    for turn in sample.get(\"chat_history\", []):\n", "        prompt += \"### Instruction:\\n\" + turn[\"user\"]\n", "        prompt += \"### Response:\\n\" + turn[\"assistant\"]\n", "    prompt += \"### Instruction:\\n\" + sample[\"message\"]\n", "    return prompt\n", "\n", "\n", "def _parse_binks_format(binks_sample):\n", "    history = binks_sample[\"question\"]\n", "    history_parts = [part for part in re.split(r'### (Instruction:|Response:)\\n', history) if len(part) > 0]\n", "    \n", "    history_parsed = []\n", "    \n", "    cur_exchange = []\n", "    for i in range(0, len(history_parts), 2):\n", "        if history_parts[i] == \"Instruction:\":\n", "            assert len(cur_exchange) == 0\n", "            cur_exchange.append(history_parts[i + 1])\n", "        elif history_parts[i] == \"Response:\":\n", "            assert len(cur_exchange) == 1\n", "            cur_exchange.append(history_parts[i + 1])\n", "            history_parsed.append(\n", "                {\n", "                    \"user\": cur_exchange[0],\n", "                    \"assistant\": cur_exchange[1],\n", "                }\n", "            )\n", "            cur_exchange = []\n", "        else:\n", "            raise ValueError(f\"Unknown part: {history_parts[i]}\")\n", "    assert len(cur_exchange) == 1\n", "    \n", "    sample = {\n", "        \"chat_history\": history_parsed,\n", "        \"message\": cur_exchange[0],\n", "        \"response\": binks_sample[\"answer\"],\n", "    }\n", "    \n", "    # Just to be sure\n", "    assert history == format_sample_for_retrieval(sample)\n", "    \n", "    return sample\n", "    \n", "\n", "def join_samples_with_repos(spark: SparkSession, samples_path: Path, raw_repos_patterns: str, is_binks_format=False) -> pyspark.sql.DataFrame:\n", "    repos_df = spark.read.parquet(raw_repos_patterns)\n", "    print(f\"Loaded {repos_df.count()} repos.\")\n", "    \n", "    if is_binks_format:\n", "        binks_repos = []\n", "        with samples_path.open() as f:\n", "            for i, line in tqdm(enumerate(f), desc=\"Loading binks repos\"):\n", "                binks_repos.append(json.loads(line))\n", "                # if i > 300:\n", "                #     break\n", "        print(f\"Loaded {len(binks_repos)} binks repos.\")\n", "        \n", "        samples_by_repo = defaultdict(list)\n", "        for _repo in tqdm(binks_repos, desc=\"Parsing binks samples\"):\n", "            for sample in _repo[\"documents_with_questions\"]:\n", "                repo_id = _repo[\"repo_uuid\"]\n", "                sample = _parse_binks_format(sample)\n", "                sample[\"extra\"] = {\n", "                    \"repo_id\": repo_id,\n", "                }\n", "                samples_by_repo[repo_id].append(json.dumps(sample))\n", "    else:    \n", "        with samples_path.open() as f:\n", "            samples = json.load(f)\n", "        print(f\"Loaded {len(samples)} samples.\")\n", "    \n", "        samples_by_repo = defaultdict(list)\n", "        for sample in samples:\n", "            sample[\"chat_history\"] = sample[\"history\"]\n", "            repo_id = sample[\"extra\"][\"repo_id\"]\n", "            samples_by_repo[repo_id].append(json.dumps(sample))\n", "\n", "    samples_by_repo_df = pd.DataFrame(\n", "        [\n", "            {\"repo_id\": repo_id, \"samples\": samples}\n", "            for repo_id, samples in samples_by_repo.items()\n", "        ]\n", "    )\n", "    samples_by_repo_df = spark.createDataFrame(samples_by_repo_df)\n", "\n", "    samples_join_w_repos_df = samples_by_repo_df.join(\n", "        repos_df, <PERSON>.col(\"repo_id\") == F.col(\"repo_uuid\"), \"inner\"\n", "    )\n", "    \n", "    return samples_join_w_repos_df\n", "\n", "\n", "def tokenize(row_joined, token_apportionment, retriever_config, seq_length, is_for_inference):\n", "    from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "\n", "    from base.tokenizers import create_tokenizer_by_name\n", "    from research.eval.harness.factories import create_retriever\n", "    from base.prompt_format_completion.prompt_formatter import PromptChunk\n", "    from research.core.types import Document, Chunk\n", "    from research.core.model_input import ModelInput\n", "    from base.prompt_format.common import Exchange\n", "    from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "    from base.prompt_format_chat.prompt_formatter import ExceedContextLength\n", "    \n", "    def convert_chunk(chunk: Chunk):\n", "        return PromptChunk(\n", "            text=chunk.text,\n", "            path=chunk.parent_doc.path,\n", "            char_start=chunk.char_offset,\n", "            char_end=chunk.char_offset + chunk.length,\n", "            unique_id=chunk.id,\n", "            origin=\"dense_retriever\",\n", "            blob_name=chunk.parent_doc.path,\n", "        )\n", "\n", "    #### Preparation\n", "    tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "    prompt_formatter = get_chat_prompt_formatter_by_name(\n", "        \"binks_llama3\",\n", "        tokenizer,\n", "        token_apportionment,\n", "    )\n", "    retriever = create_retriever(retriever_config)\n", "    retriever.load()\n", "    \n", "    #### Indexing\n", "    file_list = row_joined[\"file_list\"]\n", "    docs = [\n", "        Document(\n", "            id=file[\"hexsha\"], text=file[\"content\"], path=file[\"max_stars_repo_path\"]\n", "        )\n", "        for file in file_list\n", "    ]\n", "    retriever.add_docs(docs)\n", "\n", "    tokenized_prompts = []\n", "    for sample in row_joined[\"samples\"]:\n", "        sample = json.loads(sample)\n", "        \n", "        #### Retrieval\n", "        retriever_input = format_sample_for_retrieval(sample)\n", "        chunks, _ = retriever.query(\n", "            model_input=ModelInput(retriever_input),\n", "            top_k=32,\n", "        )\n", "        chunks = [*map(convert_chunk, chunks)]\n", "        \n", "        #### Prompt formatting\n", "        history = [\n", "            Exchange(\n", "                request_message=exchange[\"user\"],\n", "                response_text=exchange[\"assistant\"],\n", "                # request_id=str(i)\n", "            )\n", "            for i, exchange in enumerate(sample.get(\"chat_history\", []))\n", "        ]\n", "        prompt_input = ChatPromptInput(\n", "            path=sample.get(\"path\", \"\"),\n", "            prefix=sample.get(\"prefix\", \"\"),\n", "            selected_code=sample.get(\"selected_code\", \"\"),\n", "            suffix=sample.get(\"suffix\", \"\"),\n", "            message=sample[\"message\"],\n", "            chat_history=history,\n", "            prefix_begin=0,\n", "            suffix_end=len(sample.get(\"prefix\", \"\") + sample.get(\"selected_code\", \"\") + sample.get(\"suffix\", \"\")),\n", "            retrieved_chunks=chunks,\n", "            context_code_exchange_request_id=\"new\",#str(len(history) - 1),\n", "        )\n", "        \n", "        try:\n", "            prompt_output = prompt_formatter.format_prompt(prompt_input)\n", "        except ExceedContextLength:\n", "            print(\n", "                f\"Skipping sample (ExceedContextLength in prepare_prompt) with path={sample['path']}.\"\n", "            )\n", "            continue\n", "        \n", "        #### Tokenization\n", "        if is_for_inference:\n", "            complete_prompt = prompt_output.tokens\n", "        else:\n", "            tokenized_output = tokenizer.tokenize_safe(sample[\"response\"]) + [\n", "                tokenizer.special_tokens.eos\n", "            ]\n", "            complete_prompt = [-1 * t for t in prompt_output.tokens] + tokenized_output\n", "            if len(complete_prompt) > seq_length:\n", "                print(\n", "                    f\"Skipping sample (len(complete_prompt) > seq_length) with path={sample['path']}.\"\n", "                )\n", "                continue\n", "            complete_prompt += [-1 * tokenizer.special_tokens.eos] * (\n", "                seq_length - len(complete_prompt) + 1\n", "            )  # +1 to make total prompt of length SEQUENCE_LENGTH + 1\n", "\n", "        sample[\"prompt_tokens\"] = complete_prompt\n", "        sample[\"chunks\"] = [asdict(c) for c in chunks]\n", "        tokenized_prompts.append(json.dumps(sample))    \n", "    return tokenized_prompts\n", "\n", "def tokenize_batch(\n", "    batch: pd.DataFrame,\n", "    token_apportionment,\n", "    retriever_config,\n", "    seq_length,\n", "    is_for_inference,\n", "    progress_file: Path,\n", ") -> pd.DataFrame:\n", "    tokenized_prompts: list[TokenList] = []\n", "    for i in range(batch.shape[0]):\n", "        cur_result = tokenize(\n", "            batch.iloc[i],\n", "            token_apportionment,\n", "            retriever_config,\n", "            seq_length,\n", "            is_for_inference,\n", "        )\n", "        with progress_file.open(\"a\") as f:\n", "            f.write(f\"{len(cur_result)}\\n\")\n", "        tokenized_prompts.extend(cur_result)\n", "\n", "    # Otherwise we get a schema mismatch. We filter out such samples later in `save_dataset`.\n", "    if len(tokenized_prompts) == 0:\n", "        tokenized_prompts = [[-1]]\n", "\n", "    return pd.DataFrame(\n", "        {\n", "            \"prompt_tokens\": tokenized_prompts,\n", "        },\n", "    )\n", "        "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_spark(\"yuri_process_synthetic_data\", NUM_WORKERS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repos_df = spark.read.parquet(str(REPOS_PATTERN))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repos_df.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples_join_w_repos_df = join_samples_with_repos(\n", "    spark, SAMPLES_PATH, str(REPOS_PATTERN), IS_BINKS_FORMAT\n", ")\n", "samples_join_w_repos_df.repartition(NUM_WORKERS * 4).write.parquet(\n", "    f\"{TMP_BUCKET}/samples_join_w_repos\"\n", ")\n", "print(samples_join_w_repos_df.count())\n", "samples_join_w_repos_df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Progress file: {OUTPUT_DIR / 'spark_progress.txt'}\")\n", "\n", "spark_result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(\n", "        tokenize_batch,\n", "        token_apportionment=TOKEN_APPORTIONMENT,\n", "        retriever_config=RETRIEVER_CONFIG,\n", "        seq_length=SEQ_LENGTH,\n", "        is_for_inference=IS_FOR_INFERENCE,\n", "        progress_file=OUTPUT_DIR / \"spark_progress.txt\",\n", "    ),\n", "    input_path=f\"{TMP_BUCKET}/samples_join_w_repos\",\n", "    output_path=f\"{TMP_BUCKET}/tokenized_samples\",\n", "    output_column=\"prompt_tokens\",\n", "    # drop_original_columns=True,\n", "    timeout=int(1e6),\n", "    batch_size=4,\n", "    ignore_error=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_result[\"task_info\"].to_csv(f\"{OUTPUT_DIR}/spark_logs.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(spark_result[\"task_info\"][\"status\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_samples = (\n", "    spark.read.parquet(f\"{TMP_BUCKET}/tokenized_samples\")\n", "    .toPandas()[\"prompt_tokens\"]\n", "    .tolist()\n", ")\n", "len(tokenized_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len([s for s in tokenized_samples if len(json.loads(s)[\"prompt_tokens\"]) > SEQ_LENGTH or len(json.loads(s)[\"prompt_tokens\"]) < 10]))   # Just checking if something broke\n", "tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "print(tokenizer.detokenize(json.loads(tokenized_samples[0])[\"prompt_tokens\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.detokenize(json.loads(tokenized_samples[3])[\"prompt_tokens\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tokenized_samples_loaded = [json.loads(s) for s in tokenized_samples]\n", "with (OUTPUT_DIR / \"tokenized_samples.json\").open(\"w\") as f:\n", "    json.dump(tokenized_samples, f)\n", "    \n", "print(OUTPUT_DIR / \"tokenized_samples.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
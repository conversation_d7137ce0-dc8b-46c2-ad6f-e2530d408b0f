{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", "\n", "with open(\"/home/<USER>/.openai\", \"r\") as f:\n", "    os.environ[\"OPENAI_API_KEY\"] = f.read().strip()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import json\n", "import re\n", "\n", "from experimental.yuri.preferences.utils import get_preferences_samples, get_chat_sample, markdown, details_html, row_html, request_id_to_link, render_simple_message, wrap_html\n", "from tqdm import tqdm\n", "from markdown import markdown as markdown_lib\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from multiprocessing import Pool\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"prod_request_insight_full_export_dataset\"\n", "TENANT_NAME = \"aitutor-mercor\"\n", "START_TIME = \"2024-07-01\"\n", "HISTORY_LIMIT = 5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# HTML rendering\n", "\n", "def render_sample(sample):\n", "    history = sample[\"option_a\"][\"request\"].get(\"chat_history\", [])\n", "    selected_code = sample[\"option_a\"][\"request\"].get(\"selected_code\", None)\n", "    message = sample[\"option_a\"][\"request\"][\"message\"]\n", "    \n", "    response = sample[\"option_a\"][\"response\"][\"text\"]\n", "\n", "    cur_index = 0\n", "    html_content = \"\"\n", "    for exchange in history[-HISTORY_LIMIT:]:\n", "        html_content += f\"<h3>Message {cur_index}</h3>\"\n", "        cur_index += 1\n", "        html_content += render_simple_message(exchange[\"request_message\"], exchange[\"response_text\"])\n", "        html_content += \"<hr>\"\n", "    if len(history) > 0:\n", "        html_content = details_html(\"Chat history\", html_content)\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "    html_content += render_simple_message(message, response, selected_code)\n", "    \n", "    return html_content\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrap_message(message):\n", "    return f\"\\n~~~\\n{message}\\n~~~\\n\"\n", "\n", "def format_sample(sample, num_history_turns):\n", "    prompt = \"Here is a conversation between user and AI software development assistant (messages are enclosed between ~~~):\\n\"\n", "    for turn in sample[\"option_a\"][\"request\"].get(\"chat_history\", [])[-num_history_turns:]:\n", "        prompt += f\"User: {wrap_message(turn['request_message'])}\\n\"\n", "        prompt += f\"Assistant: {wrap_message(turn['response_text'])}\\n\\n\\n\"\n", "    \n", "    current_user_message = \"\"\n", "    if \"selected_code\" in sample[\"option_a\"][\"request\"]:\n", "        current_user_message += f\"\"\"I have selected this code in file {sample[\"option_a\"][\"request\"][\"path\"]}:\n", "```\n", "{sample[\"option_a\"][\"request\"][\"selected_code\"]}\n", "```\n", "\"\"\"\n", "    current_user_message += f\"{sample['option_a']['request']['message']}\"\n", "    prompt += f\"User: {wrap_message(current_user_message)}\\n\"\n", "    prompt += f\"Assistant: {wrap_message(sample['option_a']['response']['text'])}\"\n", "    \n", "    return prompt\n", "\n", "\n", "def load_meta_examples():\n", "    with open(\"/home/<USER>/repos/augment/experimental/yury/binks/datav4/prompts.json\", \"r\") as f:\n", "        meta_examples_raw = json.load(f)\n", "\n", "    meta_examples = []\n", "    def _unroll_meta_examples(meta_examples_raw):\n", "        if isinstance(meta_examples_raw, str):\n", "            meta_examples.append(meta_examples_raw)\n", "        else:\n", "            for elem in meta_examples_raw:\n", "                _unroll_meta_examples(elem)\n", "\n", "    for _, v in meta_examples_raw.items():\n", "        _unroll_meta_examples(v)\n", "\n", "    return meta_examples\n", "\n", "\n", "class MetaConversationGenerator:\n", "    def __init__(self, prompt_template, refine_prompt, meta_examples):\n", "        self.prompt_template = prompt_template\n", "        self.gpt = GptWrapper()\n", "        self.refine_prompt = refine_prompt\n", "        self.meta_examples = meta_examples\n", "        \n", "    def _generate_single_meta_conversation(self, sample):\n", "        random.shuffle(self.meta_examples)\n", "        \n", "        total_turns = len(sample[\"option_a\"][\"request\"].get(\"chat_history\", []))\n", "        num_history_turns = random.randint(3, HISTORY_LIMIT)\n", "        num_history_turns = min(num_history_turns, total_turns)\n", "        \n", "        prompt = self.prompt_template.format(\n", "            conversation=format_sample(sample, num_history_turns),\n", "            meta_examples=\"\\n- \".join([\"\"] + self.meta_examples),\n", "            num_turns=num_history_turns + 1\n", "        )\n", "        gpt4_response = self.gpt([prompt], model=\"gpt-4-1106-preview\", temperature=0)\n", "        # gpt4_response = self.gpt([prompt], model=\"gpt-4o\", temperature=0)\n", "        assert isinstance(gpt4_response, str)\n", "        \n", "        gpt_history = [prompt, gpt4_response]\n", "        for i in range(2):\n", "            gpt_history.append(self.refine_prompt)\n", "            gpt_history.append(\n", "                self.gpt(gpt_history, model=\"gpt-4-1106-preview\", temperature=0)\n", "                # self.gpt(gpt_history, model=\"gpt-4o\", temperature=0)\n", "            )\n", "        \n", "        match = re.search(r\"Meta-descriptions:\\n(.*)\", gpt_history[-1], re.DOTALL)\n", "        if match:\n", "            parsed_response = match.group(1)\n", "            try:\n", "                meta_conversation_turns = self._parse_raw_meta_conversation(parsed_response)\n", "                if len(meta_conversation_turns) != num_history_turns + 1:\n", "                    print(\"Warning: Incorrect number of turns in meta conversation\")\n", "            except Exception:\n", "                print(\"Failed to parse meta conversation\")\n", "                meta_conversation_turns = None\n", "        else:\n", "            parsed_response = None\n", "            \n", "        return {\n", "            \"prompt\": prompt,\n", "            \"parsed_response\": parsed_response,\n", "            \"full_gpt_history\": gpt_history,\n", "            \"is_selected_code\": \"selected_code\" in sample[\"option_a\"][\"request\"],\n", "            \"meta_conversation_turns\": meta_conversation_turns,\n", "            \"sample_option_a\": sample[\"option_a\"],\n", "        }\n", "        \n", "    def generate_meta_conversations(self, samples):\n", "        with Pool(50) as pool:\n", "            results = list(tqdm(\n", "                pool.imap(self._generate_single_meta_conversation, samples),\n", "                total=len(samples)\n", "            ))\n", "            \n", "        print(self.gpt.get_stats())\n", "        return results\n", "    \n", "    def _parse_raw_meta_conversation(self, raw_meta_conversation):\n", "        meta_conversation_turns = []\n", "        \n", "        cur_line = 0\n", "        for line in raw_meta_conversation.splitlines():\n", "            if cur_line % 2 == 0:\n", "                match = re.search(r\"^\\s*User \\(\\d+\\): (.*)\", line, re.DOTALL)\n", "                assert match\n", "                meta_conversation_turns.append({\"user\": match.group(1)})\n", "            else:\n", "                match = re.search(r\"^\\s*Assistant \\(\\d+\\): (.*)\", line, re.DOTALL)\n", "                assert match\n", "                meta_conversation_turns[-1][\"assistant\"] = match.group(1)\n", "            cur_line += 1\n", "            \n", "            \n", "        return meta_conversation_turns\n", "    \n", "    def generate_report(self, samples, meta_conversations, max_samples=100):\n", "        samples = samples[:max_samples]\n", "        meta_conversations = meta_conversations[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, (sample, meta_conversation) in enumerate(zip(samples, meta_conversations)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f'Request ID: <a href=\"{request_id_to_link(sample[\"option_a\"][\"request_id\"])}\">{sample[\"option_a\"][\"request_id\"]}</a>'\n", "            cur_html += f\"<p>Is selected code: {meta_conversation['is_selected_code']}</p>\"\n", "            cur_html += details_html(\n", "                \"Meta-generation prompt\",\n", "                markdown(meta_conversation[\"prompt\"])\n", "            )\n", "            # cur_html += details_html(\n", "            #     \"Full GPT response\",\n", "            #     markdown(meta_conversation[\"full_gpt_history\"])\n", "            # )\n", "            \n", "            meta_conversation_str = \"\"\n", "            for i, turn in enumerate(meta_conversation[\"meta_conversation_turns\"]):\n", "                meta_conversation_str += f\"\"\"\n", "            User ({i + 1}): {turn[\"user\"]}\n", "            Assistant ({i + 1}): {turn[\"assistant\"]}\n", "\n", "            \"\"\"\n", "            \n", "            cur_html += markdown(meta_conversation_str)\n", "            cur_html += render_sample(sample)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        return wrap_html(html)\n", "            "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# preference_samples = get_preferences_samples(PROJECT_ID, DATASET_NAME, TENANT_NAME, START_TIME, 21000000)\n", "\n", "# for sample in tqdm(preference_samples):\n", "#     option_a = get_chat_sample(sample[\"request_ids\"][0], PROJECT_ID, DATASET_NAME, TENANT_NAME)\n", "#     option_b = get_chat_sample(sample[\"request_ids\"][1], PROJECT_ID, DATASET_NAME, TENANT_NAME)\n", "\n", "#     sample[\"option_a\"] = option_a\n", "#     sample[\"option_b\"] = option_b\n", "    \n", "# for sample in preference_samples:\n", "#     # To make local and BQ version the same\n", "#     sample[\"option_a\"][\"user_id\"] = sample[\"option_a\"][\"metadata\"][\"user_id\"]\n", "#     sample[\"option_b\"][\"user_id\"] = sample[\"option_b\"][\"metadata\"][\"user_id\"]\n", "    \n", "#     sample[\"user_agent\"] = sample[\"option_a\"][\"metadata\"][\"user_agent\"]\n", "    \n", "#     sample[\"option_a\"][\"request_id\"] = sample[\"request_ids\"][0]\n", "#     sample[\"option_b\"][\"request_id\"] = sample[\"request_ids\"][1]\n", "\n", "# for sample in preference_samples:\n", "#     sample[\"option_a\"][\"datetime\"] = sample[\"option_a\"][\"datetime\"].isoformat()\n", "#     sample[\"option_b\"][\"datetime\"] = sample[\"option_b\"][\"datetime\"].isoformat()\n", "\n", "# with open(\"./preference_samples_mercor_jul1_jul17.json\", \"w\") as f:\n", "#     json.dump(preference_samples, f, indent=2)\n", "\n", "with open(\"/mnt/efs/augment/user/yuri/tmp/preference_samples_mercor_jul1_jul17.json\", \"r\") as f:\n", "    preference_samples = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prompts\n", "\n", "META_GENERATION_PROMPT = \"\"\"{conversation}\n", "\n", "Your task is to generate meta-description for each message in the conversation above.\n", "\n", "\n", "- Meta description should describe ONLY the intent of the message, NOT it's content.\n", "- Meta description should be absolutely repository- and domain-agnostic\n", "- Meta description should NOT mention any specific entities (files, functions, classes, etc.) or implementation details.\n", "- If message contains code snippets, then always mention that in meta-description.\n", "- Provided conversation contains {num_turns} turns, so you should provide {num_turns} turns of meta-descriptions.\n", "\n", "Examples:\n", "{meta_examples}\n", "\n", "Use output format:\n", "Meta-descriptions:\n", "    User (1): meta_description_for_user_message_1\n", "    Assistant (1): meta_description_for_assistant_message_1\n", "    User (2): meta_description_for_user_message_2\n", "    Assistant (2): meta_description_for_assistant_message_2\n", "    ...\n", "\"\"\"\n", "\n", "META_REFINEMENT_PROMPT = \"Rewrite you response by removing all things that might be relevant to some repository or domain, \"\\\n", "\"and removing anything that looks like implementation detail. \" \\\n", "\"Also, make sure that response contains number of turns as in the original conversation\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["meta_examples = load_meta_examples()\n", "meta_generator = MetaConversationGenerator(META_GENERATION_PROMPT, META_REFINEMENT_PROMPT, meta_examples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = meta_generator._generate_single_meta_conversation(preference_samples[0])\n", "print(test[\"parsed_response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["meta_conversations = meta_generator.generate_meta_conversations(preference_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indexes = random.sample(range(len(preference_samples)), 10)\n", "\n", "with open(\"./meta_conversations_jul1_jul16_mercor_763.html\", \"w\") as f:\n", "    f.write(meta_generator.generate_report([preference_samples[i] for i in indexes], [meta_conversations[i] for i in indexes]))\n", "    \n", "with open(\"./meta_conversations_jul1_jul16_mercor_763.json\", \"w\") as f:\n", "    json.dump(meta_conversations, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
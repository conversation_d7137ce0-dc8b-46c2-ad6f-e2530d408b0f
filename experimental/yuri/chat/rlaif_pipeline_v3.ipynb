{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENT_DIR = \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/smartpaste_new_prompt_7k_simple_jul27_v1\"\n", "# EXPERIMENT_DIR = \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/ignore_selected_code_new_prompt_7k_simple_jul27_v1\"\n", "SEQUENCE_LENGTH = 8192 # Used for training data preparation\n", "\n", "# These come from `run_llama_tokenized_data.ipynb`\n", "INPUT_SAMPLES_PATHS = [\n", "    # \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul18_5k_pro_new_prompt/llama_inference_results.json\",\n", "    \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul21_2k_pro_simple_boundaries_new_prompt/llama_inference_results.json\",\n", "    \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul27_5k_pro_simple_boundaries_new_prompt/llama_inference_results.json\",\n", "    # \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/binks_v4_subset/llama_inference_results.json\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", "\n", "with open(\"/home/<USER>/.openai\", \"r\") as f:\n", "    os.environ[\"OPENAI_API_KEY\"] = f.read().strip()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "import requests\n", "import random\n", "import torch\n", "import numpy as np\n", "import hashlib\n", "\n", "\n", "from pathlib import Path\n", "from experimental.yuri.preferences.utils import markdown, details_html, wrap_html, TritonClient, row_html\n", "from typing import Any\n", "from tqdm import tqdm\n", "from multiprocessing import Pool\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from base.tokenizers import create_tokenizer_by_name\n", "from functools import partial\n", "from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from collections import defaultdict, Counter\n", "\n", "\n", "EXPERIMENT_DIR = Path(EXPERIMENT_DIR)\n", "EXPERIMENT_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# General\n", "def filter_sequences(seq1, seq2, fn):\n", "    seq1_new, seq2_new = zip(*filter(fn, zip(seq1, seq2)))\n", "    return seq1_new, seq2_new\n", "\n", "def run_or_load_results(fn, ckpt_name):\n", "    if (EXPERIMENT_DIR / ckpt_name).exists():\n", "        with open(EXPERIMENT_DIR / ckpt_name, \"r\") as f:\n", "            return json.load(f)\n", "    else:\n", "        results = fn()\n", "        with open(EXPERIMENT_DIR / ckpt_name, \"w\") as f:\n", "            json.dump(results, f)\n", "        return results\n", "\n", "def parse_gpt_response(\n", "    response_text, regexp, category2values: dict[Any, list]\n", "):\n", "    match = re.search(regexp, response_text)\n", "    if not match:\n", "        return None\n", "    value = match.group(1).strip()\n", "    for category, values in category2values.items():\n", "        if value in values:\n", "            return category\n", "    return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_simple_message(message, response, selected_code=None, another_response=None, use_markdown=True):\n", "    message = markdown(message)\n", "    \n", "    if use_markdown:\n", "        response = markdown(response)\n", "        another_response = markdown(another_response) if another_response is not None else None\n", "    else:\n", "        response = response.replace(\"\\n\", \"<br>\")\n", "        another_response = another_response.replace(\"\\n\", \"<br>\") if another_response is not None else None\n", "\n", "    html_content = \"\"\n", "    if selected_code is not None:\n", "        selected_code = f\"```\\n{selected_code}\\n```\"\n", "        selected_code = markdown(selected_code)\n", "        html_content += f'<div class=\"code\" style=\"flex: 1;\">{selected_code}</div>'\n", "    html_content += f'<div class=\"code\">{message}</div>'\n", "    \n", "    if another_response is None:\n", "        html_content += f'<div class=\"code\">{response}</div>'\n", "    else:\n", "        side_by_side_html = f'<div class=\"code\">{response}</div>'\n", "        side_by_side_html += f'<div class=\"code\">{another_response}</div>'\n", "        html_content += row_html(side_by_side_html)\n", "\n", "    return html_content\n", "\n", "\n", "# HTML rendering\n", "def render_sample(sample, another_response=None, use_markdown=True):\n", "    history = sample.get(\"history\", [])\n", "    selected_code = sample.get(\"selected_code\", None)\n", "    message = sample[\"message\"]\n", "    response = sample[\"llama_response\"]\n", "\n", "    cur_index = 0\n", "    html_content = \"\"\n", "    \n", "    for exchange in history:\n", "        html_content += f\"<h3>Message {cur_index}</h3>\"\n", "        cur_index += 1\n", "        html_content += render_simple_message(exchange[\"user\"], exchange[\"assistant\"])\n", "        html_content += \"<hr>\"\n", "    if len(history) > 0:\n", "        html_content = details_html(\"Chat history\", html_content)\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "    html_content += render_simple_message(message, response, selected_code, another_response, use_markdown)\n", "    \n", "    return html_content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare some general things\n", "\n", "def wrap_message(message):\n", "    return f\"~~~\\n{message}\\n~~~\\n\"\n", "\n", "def format_history(history):\n", "    result_text = \"\"\n", "    for exchange in history:\n", "        result_text += f\"User: {wrap_message(exchange['user'])}\\n\"\n", "        result_text += f\"Assistant: {wrap_message(exchange['assistant'])}\\n\\n\\n\"\n", "    return result_text\n", "\n", "def format_sample(sample):\n", "    chat_history = sample.get(\"history\", [])\n", "    message = sample[\"message\"]\n", "    path = sample.get(\"path\", None)\n", "    selected_code = sample.get(\"selected_code\", None)\n", "    prefix = sample.get(\"prefix\", \"\")\n", "    suffix = sample.get(\"suffix\", \"\")\n", "    \n", "    if len(chat_history) > 0:\n", "        result_text = (\n", "            \"Here is a history of conversation between user and AI coding assistant builtin into VSCode \" +\n", "            \"(each message is enclosed between ~~~):\\n \"\n", "            f\"{format_history(chat_history)}\\n\\n\" + \n", "            f\"Here is the last message (enclosed between ~~~) from user:\\n{wrap_message(message)}\"\n", "        )\n", "    else:\n", "        result_text = (\n", "            \"Here is a message (enclosed between ~~~) from user to AI coding assistant builtin into VSCode:\\n\" +\n", "            f\"{wrap_message(message)}\\n\"\n", "        )\n", "\n", "    if path is not None:\n", "        result_text += (\n", "            f\"User has file `{path}` open and has highlighted part of the code.\\n\" +\n", "            \"Here is the full file:\\n\"\n", "            f\"```\\n{prefix}[START HIGHLIGHTED REGION]\\n...\\n\" +\n", "            \"[selected code goes here]\\n...\\n[END HIGHLIGHTED REGION]\\n\" +\n", "            f\"{suffix}\\n```\\n\\n\"\n", "        )\n", "        result_text += (\n", "            f\"Here is the highlighted code:\\n\" +\n", "            f\"```\\n{selected_code}\\n```\"\n", "        )\n", "        # result_text += (\n", "        #     f\"\\n\\nHere is a piece of code that is highlighted in VSCode. File {path}:\\n\" +\n", "        #     f\"```\\n{selected_code}\\n```\"\n", "        # )\n", "\n", "    return result_text\n", "\n", "\n", "def format_retrieval(sample):\n", "    result_text = \"Here are relevant code excerpts from repository:\\n\"\n", "    for chunk in sample[\"chunks\"]:\n", "        result_text += f\"\"\"Excerpt from file `{chunk[\"path\"]}`:\\n```\\n{chunk[\"text\"]}\\n```\\n\"\"\"\n", "    return result_text\n", "\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=0,\n", "    chat_history_len=2048,\n", "    prefix_len=1024,\n", "    selected_code_len=0,\n", "    suffix_len=1024,\n", "    max_prompt_len=6144,\n", "    retrieval_len=-1\n", ")\n", "PROMPT_FORMATTER = get_chat_prompt_formatter_by_name(\n", "    \"binks_llama3\",\n", "    TOKENIZER,\n", "    APPORTIONMENT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load input prompt\n", "\n", "chat_samples = []\n", "for _input_path in INPUT_SAMPLES_PATHS:\n", "    with open(_input_path, \"r\") as f:\n", "        cur_samples = json.load(f)\n", "    print(f\"Loaded {len(cur_samples)} samples from {_input_path}\")\n", "    chat_samples += cur_samples\n", "    \n", "for sample in tqdm(chat_samples):\n", "    sample[\"prompt_text\"] = TOKENIZER.detokenize(sample[\"prompt_tokens\"])\n", "    if \"response\" in sample:  # Just to not confuse with llama response\n", "        sample[\"gemini_response\"] = sample[\"response\"]\n", "        del sample[\"response\"]\n", "    if \"chat_history\" in sample:\n", "        assert \"history\" not in sample or json.dumps(sample[\"history\"]) == json.dumps(sample[\"chat_history\"]), \"Both history and chat_history are present\"\n", "        sample[\"history\"] = sample[\"chat_history\"]\n", "        del sample[\"chat_history\"]\n", "\n", "all_repo_ids = set(map(lambda s: s[\"extra\"][\"repo_id\"], chat_samples))\n", "print(\"Number of repos:\", len(all_repo_ids))\n", "\n", "\n", "# chat_samples = [\n", "#     sample for sample in chat_samples\n", "#     if \"TripsNavPilsWitCards.js\" not in sample[\"llama_response\"]\n", "# ]\n", "    \n", "# TMP\n", "# chat_samples = chat_samples[:100]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Takes ~3.5 minutes * 2\n", "\n", "\n", "from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n", "REPOS_PATTERN_LIST = [\n", "    Path(\"/mnt/efs/augment/user/yury/binks/binks-v3.1\") / \"03_raw_repos/*.zstd.parquet\",\n", "    Path(\"/mnt/efs/augment/user/yury/binks/binks-v4\") / \"01_raw_repos/*.zstd.parquet\"\n", "]\n", "\n", "repos = []\n", "for REPOS_PATTERN in REPOS_PATTERN_LIST:\n", "    repo_dataset = ParquetRepoDataset(REPOS_PATTERN)\n", "    for i, repo in tqdm(enumerate(repo_dataset)):\n", "        if repo.id not in all_repo_ids:\n", "            continue\n", "        repos.append(repo)\n", "print(f\"Loaded: {len(repos)} repos\")\n", "\n", "repo_id_to_repo = {repo.id: repo for repo in repos}\n", "\n", "\n", "def get_file_list(repo_id):\n", "    repo = repo_id_to_repo[repo_id]\n", "    return set(map(lambda x: x[\"max_stars_repo_path\"], repo.get_files()))\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# with (EXPERIMENT_DIR / \"repo_id_to_file_list.json\").open(\"w\") as f:\n", "#     json.dump(repo_id_to_repo, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Classification: language labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BinaryClassifierLanguageLabels:\n", "    def __init__(self):\n", "        pass\n", "    \n", "    def classify_single_sample(self, sample):\n", "        pattern = re.compile(r\"```([^\\s]*?)\\n(.*?)\\n```\", re.DOTALL)\n", "        matches = list(pattern.finditer(sample[\"llama_response\"]))\n", "        \n", "        return {\n", "            \"verdict\": len(matches) > 0\n", "        }\n", "        \n", "    def classify_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            classification_results = list(tqdm(\n", "                pool.imap(self.classify_single_sample, samples),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return classification_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "\n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Verdict: {sample['classification_result']['verdict']}</p>\"\n", "\n", "            cur_html += render_sample(sample, use_markdown=False)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "            "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["classifier = BinaryClassifierLanguageLabels()\n", "_classification_results = classifier.classify_samples(chat_samples)\n", "\n", "for i, (sample, classification_result) in enumerate(zip(chat_samples, _classification_results)):\n", "    sample[\"classification_result\"] = classification_result\n", "    \n", "positive_samples = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"verdict\"] \n", "]\n", "negative_samples_using_fn = [\n", "    sample for sample in chat_samples\n", "    if not sample[\"classification_result\"][\"verdict\"] \n", "]\n", "negative_samples_using_gpt = []  # Just because code edits has it\n", "\n", "print(f\"\"\"Number of positive samples: {len(positive_samples)}\n", "Number of negative samples using filter function: {len(negative_samples_using_fn)}\n", "Number of negative samples using GPT: {len(negative_samples_using_gpt)}\"\"\")\n", "\n", "_ = classifier.generate_report(positive_samples, file_name_to_save=\"classification_report_positive_samples.html\")\n", "_ = classifier.generate_report(negative_samples_using_fn, file_name_to_save=\"classification_report_negative_samples_using_fn.html\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Classification: code edits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BinaryClassifier:\n", "    def __init__(self, prompt_template, addition_filter_fn=None, additional_prompt=None, revert_responses=False):\n", "        self.prompt_template = prompt_template\n", "        self.addition_filter_fn = addition_filter_fn\n", "        self.gpt = GptWrapper()\n", "        self.additional_prompt = \"\" if additional_prompt is None else additional_prompt\n", "        self.revert_responses = revert_responses\n", "        \n", "    def classify_single_sample(self, sample):\n", "        if (\n", "            self.addition_filter_fn is not None and\n", "            not self.addition_filter_fn(sample)\n", "        ):\n", "            return {\n", "                \"prompt\": None,\n", "                \"full_gpt_response\": None,\n", "                \"verdict\": <PERSON><PERSON><PERSON>,\n", "            }\n", "        prompt = self.prompt_template.format(\n", "            formatted_sample=format_sample(sample),\n", "            additional_prompt=self.additional_prompt\n", "        )\n", "        gpt4_response = self.gpt([prompt], model=\"gpt-4-1106-preview\", temperature=0)\n", "        \n", "        if self.revert_responses:\n", "            verdict = parse_gpt_response(\n", "                gpt4_response,\n", "                r\"Verdict:\\s*(.*)\", \n", "                {\n", "                    False: [\"YES\", \"Yes\", \"yes\"],\n", "                    True: [\"NO\", \"No\", \"no\"]\n", "                }\n", "            )            \n", "        else:\n", "            verdict = parse_gpt_response(\n", "                gpt4_response,\n", "                r\"Verdict:\\s*(.*)\", \n", "                {\n", "                    True: [\"YES\", \"Yes\", \"yes\"],\n", "                    False: [\"NO\", \"No\", \"no\"]\n", "                }\n", "            )\n", "        \n", "        return {\n", "            \"prompt\": prompt,\n", "            \"full_gpt_response\": gpt4_response,\n", "            \"verdict\": verdict,\n", "        }\n", "        \n", "    def classify_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            classification_results = list(tqdm(\n", "                pool.imap(self.classify_single_sample, samples),\n", "                total=len(samples)\n", "            ))\n", "\n", "        print(self.gpt.get_stats())\n", "        return classification_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "\n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Verdict: {sample['classification_result']['verdict']}</p>\"\n", "            cur_html += details_html(\n", "                \"Classification prompt\",\n", "                markdown(str(sample[\"classification_result\"][\"prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[\"classification_result\"][\"full_gpt_response\"]))\n", "            )\n", "            cur_html += render_sample(sample)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "            "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# AND modification can be done within boundaries the highlighted code (i.e. without modifying ANY code outside of the highlighted code)\n", "# and user instructions can be reasonably done within boundaries the highlighted code)\n", "\n", "CLASSIFICATION_PROMPT = \"\"\"{formatted_sample}\n", "\n", "Your task is to detect if user's last message EXPLICITLY instructs AI coding assistant to modify the highlighted code\n", "{additional_prompt}\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze user's last message and it's relation to the highlighted code.\n", "2. Explicitly write YES (if user's last message EXPLICITLY instructs AI coding assistant to modify the highlighted code or NO (if not).\n", "\n", "Use output format:\n", "Analysis: ...\n", "Verdict: YES/NO\n", "\"\"\"\n", "\n", "# CLASSIFICATION_ADDITIONAL_PROMPT =(\n", "#     \"If user's last message is applicable to both the highlighted code and code from last message,\" +\\\n", "#     \"then consider that user asks to modify the code from last message (not the highlighted code) and return NO.\"\n", "# )\n", "CLASSIFICATION_ADDITIONAL_PROMPT = \"\"\n", "\n", "def is_selected_code_in_sample(sample):\n", "    return \"selected_code\" in sample\n", "\n", "classifier = BinaryClassifier(\n", "    CLASSIFICATION_PROMPT,\n", "    is_selected_code_in_sample,\n", "    CLASSIFICATION_ADDITIONAL_PROMPT\n", ")\n", "\n", "_classification_results = run_or_load_results(\n", "    partial(classifier.classify_samples, chat_samples),\n", "    \"classification_results.json\"\n", ")\n", "for i, (sample, classification_result) in enumerate(zip(chat_samples, _classification_results)):\n", "    sample[\"classification_result\"] = classification_result\n", "    \n", "positive_samples = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"verdict\"] is True\n", "]\n", "negative_samples_using_fn = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"prompt\"] is None\n", "]\n", "negative_samples_using_gpt = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"prompt\"] is not None and sample[\"classification_result\"][\"verdict\"] is False\n", "]\n", "print(f\"\"\"Number of positive samples: {len(positive_samples)}\n", "Number of negative samples using filter function: {len(negative_samples_using_fn)}\n", "Number of negative samples using GPT: {len(negative_samples_using_gpt)}\"\"\")\n", "\n", "_ = classifier.generate_report(positive_samples, file_name_to_save=\"classification_report_positive_samples.html\")\n", "_ = classifier.generate_report(negative_samples_using_fn, file_name_to_save=\"classification_report_negative_samples_using_fn.html\")\n", "_ = classifier.generate_report(negative_samples_using_gpt, file_name_to_save=\"classification_report_negative_samples_using_gpt.html\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Classification: ignore selected code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SELECTED_CODE_IMPORTANCE_CLASSIFICATION_PROMPT = \"\"\"{formatted_sample}\n", "\n", "Your task is to detect if highlighted code is needed to answer user's last message.\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze user's last message and it's relation to the highlighted code.\n", "2. Explicitly write YES (if highlighted code is needed to answer user's last message) \n", "    or NO (if highlighted code is irrelevant to the user's last message and can be safely removed).\n", "\n", "Use output format:\n", "Analysis: ...\n", "Verdict: YES/NO\n", "\"\"\"\n", "\n", "\n", "def is_selected_code_in_sample(sample):\n", "    return \"selected_code\" in sample\n", "\n", "classifier = BinaryClassifier(\n", "    SELECTED_CODE_IMPORTANCE_CLASSIFICATION_PROMPT,\n", "    is_selected_code_in_sample,\n", "    revert_responses=True\n", ")\n", "\n", "_classification_results = run_or_load_results(\n", "    partial(classifier.classify_samples, chat_samples),\n", "    \"classification_results.json\"\n", ")\n", "\n", "for i, (sample, classification_result) in enumerate(zip(chat_samples, _classification_results)):\n", "    sample[\"classification_result\"] = classification_result\n", "    \n", "positive_samples = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"verdict\"] is True\n", "]\n", "negative_samples_using_fn = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"prompt\"] is None\n", "]\n", "negative_samples_using_gpt = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"prompt\"] is not None and sample[\"classification_result\"][\"verdict\"] is False\n", "]\n", "print(f\"\"\"Number of positive samples: {len(positive_samples)}\n", "Number of negative samples using filter function: {len(negative_samples_using_fn)}\n", "Number of negative samples using GPT: {len(negative_samples_using_gpt)}\"\"\")\n", "\n", "_ = classifier.generate_report(positive_samples, file_name_to_save=\"classification_report_positive_samples.html\")\n", "_ = classifier.generate_report(negative_samples_using_fn, file_name_to_save=\"classification_report_negative_samples_using_fn.html\")\n", "_ = classifier.generate_report(negative_samples_using_gpt, file_name_to_save=\"classification_report_negative_samples_using_gpt.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Classification: paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PATHS_DETECTION_PROMPT = \"\"\"Here is a message from coding assistant (enclosed between ~~~ below):\n", "# {formatted_response}\n", "\n", "# You task is to extract all files that are present in this message.\n", " \n", "# Requirements:\n", "# - If file is mentioned as a part of code snippet, like `open(\"my_file.txt\")` or inside ```...```, and it's not present anywhere else, then do not return it!\n", "# - Do not return paths to a general (repository-agnostic) files, like .bashrc, .gitignore, etc\n", "# - Return paths in EXACTLY the same form as they are present in the message.\n", "\n", "# Example 1:\n", "# ```\n", "# The gpb.h file is a header file for working with Google Protocol Buffers (GPB) in the OpenStreetMap (OSM) project. It provides functions and macros for parsing and encoding GPB messages, which are used to store and transmit data in the OSM ecosystem.\n", "\n", "# This file is part of the GDAL library and is used to handle GPB data, which is a format used by Google to store geographic data. The functions and macros in gpb.h allow GDAL to read and write GPB data, enabling the integration of OSM data with other geospatial formats.\n", "\n", "# In essence, gpb.h facilitates the interaction between GDAL and OSM, enabling the use of GPB data in GDAL-based applications.\n", "# ```\n", "# detected_paths = [\"gpb.h\"]\n", "\n", "# Example 2:\n", "# ```\n", "# The ScrollLink component is imported from the react-scroll library, so you won't find its definition in the project's codebase. Instead, you should look for imports like import {{ Link as ScrollLink }} from \"react-scroll\"; in the project files.\n", "\n", "# In the provided excerpts, I see that ScrollLink is imported from react-scroll in several files, such as `/src/components/GalleryNavPillsWithCards.js`, EventNavPills.js, and /dist/TripsNavPilsWitCards.js.\n", "# ```\n", "# detected_paths = [\"/src/components/GalleryNavPillsWithCards.js\", \"EventNavPills.js\", \"/dist/TripsNavPilsWitCards.js\"]\n", "\n", "# Example 3:\n", "# ```\n", "# def read_file(file_path):\n", "#     with open(file_path, \"r\") as f:\n", "#         content = f.read()\n", "#     return content\n", "# contet = read_file(\"/src/file_path.txt\")\n", "# ```\n", "# detected_paths = []\n", "\n", "\n", "# Return JSON with 2 keys:\n", "# 1. `analysis` - a string with your analysis of the provided message and requirements.\n", "# 2. `detected_paths` with a list of detected paths.\n", "# \"\"\"\n", "\n", "# PATHS_DETECTION_PROMPT = \"\"\"{formatted_retrieval}\n", "\n", "# Here is a **message from coding assistant** (enclosed between ~~~ below):\n", "# {formatted_response}\n", "\n", "# You task is to extract all file paths and file names that are mentioned in the **message from coding assistant** and are likely a part of repository.\n", " \n", "# Requirements:\n", "# - Do not extract a general (repository-agnostic) files, like .bashrc, .gitignore, /tmp* etc\n", "# - Do not extract folders\n", "# - Return paths and names in EXACTLY the same form as they are present in the message.\n", "\n", "# Example 1:\n", "# ```\n", "# The gpb.h file is a header file for working with Google Protocol Buffers (GPB) in the OpenStreetMap (OSM) project. It provides functions and macros for parsing and encoding GPB messages, which are used to store and transmit data in the OSM ecosystem.\n", "\n", "# This file is part of the GDAL library and is used to handle GPB data, which is a format used by Google to store geographic data. The functions and macros in gpb.h allow GDAL to read and write GPB data, enabling the integration of OSM data with other geospatial formats.\n", "\n", "# In essence, gpb.h facilitates the interaction between GDAL and OSM, enabling the use of GPB data in GDAL-based applications.\n", "# ```\n", "# detected_files = [\"gpb.h\"]\n", "\n", "# Example 2:\n", "# ```\n", "# The ScrollLink component is imported from the react-scroll library, so you won't find its definition in the project's codebase. Instead, you should look for imports like import {{ Link as ScrollLink }} from \"react-scroll\"; in the project files.\n", "\n", "# In the provided excerpts, I see that ScrollLink is imported from react-scroll in several files, such as `/src/components/GalleryNavPillsWithCards.js`, EventNavPills.js, and /dist/TripsNavPilsWitCards.js.\n", "# ```\n", "# detected_files = [\"/src/components/GalleryNavPillsWithCards.js\", \"EventNavPills.js\", \"/dist/TripsNavPilsWitCards.js\"]\n", "\n", "# Return JSON with 2 keys:\n", "# 1. `analysis` - a string with your analysis of the provided message and requirements.\n", "# 2. `detected_files` with a list of detected files.\n", "# \"\"\"\n", "\n", "PATHS_DETECTION_PROMPT = \"\"\"Software engineer is working on some reposiory.\n", "Software engineer asked coding assistant some question and received the following response (enclosed between ~~~ below):\n", "{formatted_response}\n", "\n", "You task is to extract all file paths and file names that are mentioned in the **response from coding assistant** and are likely a part of repository.\n", " \n", "Requirements:\n", "- Do not extract a general (repository-agnostic) files, like .bashrc, .gitignore, /tmp* etc\n", "- Do not extract any file that is clearly not a part of repository\n", "- Do not extract any files that responses suggests to create\n", "- Do not extract folders\n", "- Return paths and names in EXACTLY the same form as they are present in the message.\n", "\n", "Return JSON with 2 keys:\n", "1. `analysis` - a string with your analysis of the provided message and requirements.\n", "2. `detected_files` with a list of detected files.\n", "\"\"\"\n", "\n", "def extract_paths(sample, response_text, gpt):\n", "    response_text = re.sub(r\"```.+?```\", \"```\\n...\\n```\", response_text, flags=re.DOTALL)\n", "    \n", "    detection_prompt = PATHS_DETECTION_PROMPT.format(\n", "        # formatted_sample=format_sample(sample),\n", "        formatted_response=wrap_message(response_text),\n", "        # formatted_retrieval=format_retrieval(sample)\n", "    )\n", "    gpt4_response = gpt([detection_prompt], model=\"gpt-4-1106-preview\", temperature=0, use_json=True)\n", "    assert isinstance(gpt4_response, dict)\n", "    \n", "    if len(gpt4_response[\"detected_files\"]) != len(set(gpt4_response[\"detected_files\"])):\n", "        print(f\"WARNING: Duplicate paths detected: {gpt4_response['detected_files']}\")\n", "        \n", "    # filtered_paths = []\n", "    # for path in gpt4_response[\"detected_files\"]:\n", "    #     path = path.strip(\"`\")\n", "    #     if f\"`{path}`\" in response_text:\n", "    #         filtered_paths.append(path)\n", "    #     else:\n", "    #         print(f\"WARNING: Path {path} is not present in the response\")\n", "    filtered_paths = gpt4_response[\"detected_files\"]\n", "\n", "    return {\n", "        \"prompt\": detection_prompt,\n", "        \"extracted_paths\": list(set(filtered_paths)),\n", "        \"gpt4_response\": gpt4_response,\n", "    }\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BinaryClassifierFilePaths:\n", "    def __init__(self):\n", "        # self.prompt_template = prompt_template\n", "        self.gpt = GptWrapper()\n", "        \n", "    # def classify_single_sample(self, sample):\n", "    #     last_models_response = sample[\"llama_response\"]\n", "    #     last_models_response = re.sub(r\"```.+?```\", \"```\\n...\\n```\", last_models_response, flags=re.DOTALL)\n", "        \n", "    #     prompt = self.prompt_template.format(\n", "    #         message=last_models_response,\n", "    #     )\n", "    #     gpt4_response = self.gpt([prompt], model=\"gpt-4-1106-preview\", temperature=0)\n", "    #     assert isinstance(gpt4_response, str)\n", "        \n", "    #     verdict = parse_gpt_response(\n", "    #         gpt4_response,\n", "    #         r\"Verdict:\\s*(.*)\", \n", "    #         {\n", "    #             True: [\"YES\", \"Yes\", \"yes\"],\n", "    #             False: [\"NO\", \"No\", \"no\"]\n", "    #         }\n", "    #     )\n", "        \n", "    #     return {\n", "    #         \"prompt\": prompt,\n", "    #         \"full_gpt_response\": gpt4_response,\n", "    #         \"verdict\": verdict,\n", "    #     }\n", "    def classify_single_sample(self, sample):\n", "        try:\n", "            extraction_result = extract_paths(sample, sample[\"llama_response\"], self.gpt)\n", "        except Exception as e:\n", "            print(e)\n", "            return {\n", "                \"prompt\": None,\n", "                \"full_gpt_response\": None,\n", "                \"verdict\": <PERSON><PERSON><PERSON>,\n", "                \"extracted_paths\": None,\n", "            }\n", "        return {\n", "            \"prompt\": extraction_result[\"prompt\"],\n", "            \"full_gpt_response\": extraction_result[\"gpt4_response\"],\n", "            \"verdict\": len(extraction_result[\"extracted_paths\"]) > 0,\n", "            \"extracted_paths\": extraction_result[\"extracted_paths\"],\n", "        }\n", "        \n", "        \n", "    def classify_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            classification_results = list(tqdm(\n", "                pool.imap(self.classify_single_sample, samples),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return classification_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Verdict: {sample['classification_result']['verdict']}</p>\"\n", "            cur_html += details_html(\n", "                \"Classification prompt\",\n", "                markdown(str(sample[\"classification_result\"][\"prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[\"classification_result\"][\"full_gpt_response\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Extracted paths\",\n", "                markdown(str(sample[\"classification_result\"][\"extracted_paths\"]))\n", "            )\n", "            cur_html += render_sample(sample)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# l = [c_s for c_s in chat_samples if \"TripsNavPilsWitCards.js\" in c_s[\"llama_response\"]]\n", "# len(l)\n", "# print(l[0][\"llama_response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CLASSIFICATION_PROMPT_FILEPATHS = \"\"\"Here is an answer generated by AI coding assistant (enclosed between ~~~):\n", "# ~~~\n", "# {message}\n", "# ~~~\n", "\n", "# Your task is to detect if this message mentions any files. Ignore ```...``` if present.\n", "# If file/directory is mentioned as a part of some code snippet, that DOES NOT count as a mention!\n", "\n", "# Follow these steps to complete this task:\n", "# 1. <PERSON><PERSON><PERSON> provided message.\n", "# 2. Explicitly write YES (if message mentions any files) or NO (if message DOES NOT mention any files).\n", "\n", "# Use output format:\n", "# Analysis: ...\n", "# Verdict: YES/NO\n", "# \"\"\"\n", "\n", "classifier_filepaths = BinaryClassifierFilePaths()\n", "_classification_results = run_or_load_results(\n", "    partial(classifier_filepaths.classify_samples, chat_samples),\n", "    \"classification_results_filepaths.json\"\n", ")\n", "for i, (sample, classification_result) in enumerate(zip(chat_samples, _classification_results)):\n", "    sample[\"classification_result\"] = classification_result\n", "\n", "positive_samples = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"verdict\"] is True\n", "]\n", "negative_samples_using_fn = []\n", "negative_samples_using_gpt = [\n", "    sample for sample in chat_samples\n", "    if sample[\"classification_result\"][\"verdict\"] is False\n", "]\n", "print(f\"\"\"Number of positive samples: {len(positive_samples)}\n", "Number of negative samples using filter function: {len(negative_samples_using_fn)}\n", "Number of negative samples using GPT: {len(negative_samples_using_gpt)}\"\"\")\n", "\n", "_ = classifier_filepaths.generate_report(positive_samples, file_name_to_save=\"classification_report_positive_samples_filepaths.html\")\n", "_ = classifier_filepaths.generate_report(negative_samples_using_gpt, file_name_to_save=\"classification_report_negative_samples_using_gpt_filepaths.html\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_t = []\n", "for sample in positive_samples:\n", "    missing_file = False\n", "    for file in sample[\"classification_result\"][\"extracted_paths\"]:\n", "        if file not in sample[\"llama_response\"]:\n", "            print(f\"Warning: file {file} is not present in the response\")\n", "            missing_file = True\n", "            break\n", "    if not missing_file:\n", "        _t.append(sample)\n", "# positive_samples[0][\"classification_result\"][\"full_gpt_response\"][\"detected_files\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Refinement: language labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class RefinerLanguageLabels:\n", "    def __init__(self, prompt_template):\n", "        self.prompt_template = prompt_template\n", "        self.gpt = GptWrapper()\n", "        \n", "    def refine_single_sample(self, sample):\n", "        pattern = re.compile(r\"```([^\\s]*?)\\n(.*?)\\n```\", re.DOTALL)\n", "        matches = list(pattern.finditer(sample[\"llama_response\"]))\n", "        \n", "        if len(matches) == 0:\n", "            assert False, \"Failed to parse\"\n", "            \n", "        num_code_blocks = len(matches)\n", "        rendered_code_blocks = \"\"\n", "        for i, match in enumerate(matches):\n", "            language, code = match.groups()\n", "            rendered_code_blocks += f\"Code block {i}:\\n```{language}\\n{code}\\n```\\n\"\n", "            \n", "        prompt = self.prompt_template.format(\n", "            formatted_sample=format_sample(sample),\n", "            formatted_response=wrap_message(sample[\"llama_response\"]),\n", "            code_blocks_list=rendered_code_blocks,\n", "            num_code_blocks=num_code_blocks\n", "        )\n", "        gpt4_response = self.gpt([prompt], model=\"gpt-4-1106-preview\", temperature=0, use_json=True)\n", "        assert isinstance(gpt4_response, dict)\n", "        \n", "        detected_languages = gpt4_response[\"detected_languages\"]\n", "        assert len(detected_languages) == len(matches)\n", "        \n", "        shift = 0\n", "        cur_response = sample[\"llama_response\"]\n", "        for i, (match, detected_language) in enumerate(zip(matches, detected_languages)):\n", "            cur_language = match.group(1)  # Indexing in group() starts from 1\n", "            if len(cur_language) > 0:\n", "                if cur_language != detected_language:\n", "                    print(f\"Warning: language mismatch: {cur_language} != {detected_language}\")\n", "                continue\n", "            \n", "            match_start, _ = match.span()\n", "            match_start += shift\n", "            prefix = cur_response[:match_start + 3]  # 3 for ```\n", "            suffix = cur_response[match_start + 3:]\n", "            \n", "            cur_response = prefix + detected_language + suffix\n", "            shift += len(detected_language)\n", "        \n", "        return {\n", "            \"prompt\": prompt,\n", "            \"full_gpt_response\": gpt4_response,\n", "            \"refined_response\": cur_response,\n", "        }\n", "        \n", "    def refine_single_sample_safe(self, sample):\n", "        try:\n", "            return self.refine_single_sample(sample)\n", "        except AssertionError as e:\n", "            print(e)\n", "            return None\n", "        \n", "    def refine_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            refinement_results = list(tqdm(\n", "                pool.imap(self.refine_single_sample_safe, samples),\n", "                total=len(samples)\n", "            ))\n", "        print(self.gpt.get_stats())\n", "        return refinement_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += details_html(\n", "                \"Refinement prompt\",\n", "                markdown(str(sample[\"refinement_result\"][\"prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[\"refinement_result\"][\"full_gpt_response\"]))\n", "            )\n", "            cur_html += render_sample(sample, sample[\"refinement_result\"][\"refined_response\"], False)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LANGUAGE_DETECTION_PROMPT = \"\"\"{formatted_sample}\n", "\n", "Here is a response (enclosed between ~~~) to user's last message generated by AI coding assistant:\n", "{formatted_response}\n", "\n", "Here is a list of {num_code_blocks} code blocks detected in response:\n", "{code_blocks_list}\n", "\n", "Your task is to detect language for each code block.\n", "Every language should be written in the format that can be used in Markdown to enable syntax highlighting.\n", "If language already present in the codeblock (after first ```), then just return it exactly the same.\n", "For plain text, return `text`.\n", "\n", "Return JSON with a single key: `detected_languages` with a list of {num_code_blocks} elements.\n", "\"\"\"\n", "\n", "refiner_language_labels = RefinerLanguageLabels(LANGUAGE_DETECTION_PROMPT)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_refinement_results = run_or_load_results(\n", "    partial(refiner_language_labels.refine_samples, positive_samples),\n", "    \"refinement_language_labels_results.json\"\n", ")\n", "\n", "for i, (positive_sample, refinement_result) in enumerate(zip(positive_samples, _refinement_results)):\n", "    positive_sample[\"refinement_result\"] = refinement_result\n", "    \n", "positive_samples_refined = [\n", "    positive_sample for positive_sample in positive_samples\n", "    if positive_sample[\"refinement_result\"] is not None\n", "]\n", "print(f\"Succesfully refined {len(positive_samples_refined)} / {len(positive_samples)} samples\")\n", "\n", "_ = refiner_language_labels.generate_report(positive_samples_refined, file_name_to_save=\"refinement_language_labels_report.html\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reward: language labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class RewardLanguageLabels:\n", "    def __init__(self):\n", "        pass\n", "    \n", "    def reward_single_sample(self, args):\n", "        _, response = args\n", "        \n", "        pattern = re.compile(r\"```([^\\s]*?)\\n(.*?)\\n```\", re.DOTALL)\n", "        matches = pattern.findall(response)\n", "        \n", "        if len(matches) == 0:\n", "            print(f\"Didn't find code in response: {response}\")\n", "            return {\n", "                \"reward_value\": 0,\n", "                \"matches\": None,\n", "            }\n", "            \n", "        num_good_samples = 0\n", "        for match in matches:\n", "            language, _ = match\n", "            num_good_samples += int(len(language) > 0)\n", "        \n", "        return {\n", "            \"reward_value\": int(num_good_samples == len(matches)),\n", "            \"matches\": matches,\n", "        }\n", "        \n", "    def reward_samples(self, samples, responses):\n", "        args = []\n", "        for sample, response in zip(samples, responses):\n", "            args.append((sample, response))\n", "        with Pool(50) as pool:\n", "            reward_results = list(tqdm(\n", "                pool.imap(self.reward_single_sample, args),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return reward_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model = RewardLanguageLabels()\n", "\n", "#### For llama response\n", "reward_model_outputs = reward_model.reward_samples(positive_samples, [s[\"llama_response\"] for s in positive_samples])\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "print(f\"Total number of samples: {len(reward_scalars)}\")\n", "print(f\"Average LLama response reward: {sum(reward_scalars) / len(reward_scalars)}\")\n", "\n", "#### For Gemini response\n", "reward_model_outputs = reward_model.reward_samples(positive_samples, [s[\"gemini_response\"] for s in positive_samples])\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "print(f\"Average Gemini response reward: {sum(reward_scalars) / len(reward_scalars)}\")\n", "\n", "#### For refined response\n", "reward_model_outputs = reward_model.reward_samples(positive_samples, [s[\"refinement_result\"][\"refined_response\"] for s in positive_samples])\n", "for i, (p_s_r, reward_model_output) in enumerate(zip(positive_samples, reward_model_outputs)):\n", "    p_s_r[\"reward_model_output\"] = reward_model_output\n", "\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "print(f\"Average refined response reward: {sum(reward_scalars) / len(reward_scalars)}\")\n", "\n", "positive_samples_refined_validated = [\n", "    p_s_r for p_s_r in positive_samples_refined\n", "    if p_s_r[\"reward_model_output\"][\"reward_value\"] == 1\n", "]\n", "print(f\"Number of samples with positive reward left: {len(positive_samples_refined_validated)} / {len(positive_samples_refined)} samples\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Refinement: smartpaste"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Triton part\n", "\n", "from multiprocessing import Queue\n", "\n", "ALL_IPS = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "    ]\n", "PORTS = [8000, 8010, 8020, 8030, 8040, 8050, 8060, 8070]\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]\n", "\n", "\n", "TRITON_CLIENT_QUEUE = Queue()\n", "for url in tqdm(ALL_URLS):\n", "    TRITON_CLIENT_QUEUE.put(TritonClient(url, TOKENIZER.special_tokens.eod_token))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SMARTPASTE_PROMPT = \"\"\"\n", "\n", "Here is the selected code:\n", "\n", "```\n", "{selected_code}\n", "```\n", "\n", "Rewrite the selected code to apply all these changes.\n", "Fit all new and modified code in your response.\n", "Do not repeat any code that already exists outside the selected code.\n", "Include all parts of the selected code that weren't changed.\n", "\n", "\"\"\"\n", "\n", "\n", "def get_smartpaste_llama_code(sample):\n", "    history = [Exchange(request_message=x[\"user\"], response_text=x[\"assistant\"]) for x in sample[\"history\"]]\n", "    history.append(Exchange(request_message=sample[\"message\"], response_text=sample[\"llama_response\"]))\n", "    message = SMARTPASTE_PROMPT.format(selected_code=sample[\"selected_code\"])\n", "    prompt_input = ChatPromptInput(\n", "        path=sample[\"path\"],\n", "        prefix=sample[\"prefix\"],\n", "        selected_code=sample[\"selected_code\"],\n", "        suffix=sample[\"suffix\"],\n", "        message=message,\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(sample[\"prefix\"] + sample[\"selected_code\"] + sample[\"suffix\"]),\n", "        retrieved_chunks=[PromptChunk(**chunk) for chunk in sample[\"chunks\"]],\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "    smartpaste_prompt = TOKENIZER.detokenize(\n", "        PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "    )\n", "    \n", "    triton_client = TRITON_CLIENT_QUEUE.get()\n", "    try:\n", "        smartpaste_response = triton_client.generate(smartpaste_prompt)\n", "    except requests.ConnectionError as e:\n", "        # Deliberately not putting the client back to the queue\n", "        print(f\"Connection error from {triton_client.url}\")\n", "        return get_smartpaste_llama_code(sample)\n", "\n", "    TRITON_CLIENT_QUEUE.put(triton_client)\n", "    assert smartpaste_response is not None\n", "    \n", "    match = re.search(r\"```[^\\s]*\\n*(.*?)\\n```\", smartpaste_response, re.DOTALL)\n", "    assert match, \"Failed to parse\"\n", "    smartpaste_code = match.group(1)\n", "    return smartpaste_prompt, smartpaste_response, smartpaste_code\n", "\n", "class RefinerSmartPaste:\n", "    def __init__(self, prompt_template):\n", "        self.prompt_template = prompt_template\n", "        self.gpt = GptWrapper()\n", "        \n", "    def refine_single_sample(self, sample):\n", "        smartpaste_prompt, smartpaste_response, smartpaste_code = get_smartpaste_llama_code(sample)\n", "        \n", "        refine_prompt = self.prompt_template.format(\n", "            formatted_sample=format_sample(sample),\n", "            formatted_response=wrap_message(sample[\"llama_response\"]),\n", "            smartpaste_llama_code=smartpaste_code\n", "        )\n", "        gpt4_response = self.gpt([refine_prompt], model=\"gpt-4-1106-preview\", temperature=0)\n", "        assert isinstance(gpt4_response, str)\n", "        \n", "        match = re.search(r\"~~~\\n*(.*)\\n~~~\", gpt4_response, re.DOTALL)\n", "        assert match, \"Failed to parse\"\n", "        refined_response = match.group(1)\n", "        \n", "        return {\n", "            \"smartpaste_prompt\": smartpaste_prompt,\n", "            \"smartpaste_response\": smartpaste_response,\n", "            \"smartpaste_code\": smartpaste_code,\n", "            \"refine_prompt\": refine_prompt,\n", "            \"gpt4_response\": gpt4_response,\n", "            \"refined_response\": refined_response,\n", "        }\n", "        \n", "    def refine_single_sample_safe(self, sample):\n", "        try:\n", "            return self.refine_single_sample(sample)\n", "        except AssertionError as e:\n", "            print(e)\n", "            return None\n", "    \n", "    def refine_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            refinement_results = list(tqdm(\n", "                pool.imap(self.refine_single_sample_safe, samples),\n", "                total=len(samples)\n", "            ))\n", "\n", "        print(self.gpt.get_stats())\n", "        return refinement_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += details_html(\n", "                \"Smartpaste prompt\",\n", "                markdown(str(sample[\"refinement_result\"][\"smartpaste_prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Smartpaste response\",\n", "                markdown(str(sample[\"refinement_result\"][\"smartpaste_response\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Smartpaste code\",\n", "                markdown(str(sample[\"refinement_result\"][\"smartpaste_code\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Refinement prompt\",\n", "                markdown(str(sample[\"refinement_result\"][\"refine_prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[\"refinement_result\"][\"gpt4_response\"]))\n", "            )\n", "            cur_html += render_sample(sample, sample[\"refinement_result\"][\"refined_response\"])\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "            \n", "        "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REFINEMENT_PROMPT = \"\"\"{formatted_sample}\n", "\n", "Here is a response (enclosed between ~~~) to user's last message generated by AI coding assistant:\n", "{formatted_response}\n", "\n", "Your task is to rewrite AI coding assistant's response such that it includes EXACTLY this modified code chunk:\n", "```\n", "{smartpaste_llama_code}\n", "```\n", "\n", "Requirements:\n", "- Make sure to keep modified code chunk EXACTLY the same, including all formatting, boundaries on edges, etc etc.\n", "- Do not split modified code chunk or merge it with any other code.\n", "\n", "Return just the refined response (enclosed between ~~~) and nothing else.\n", "\"\"\"\n", "\n", "\n", "refiner_smartpaste = RefinerSmartPaste(REFINEMENT_PROMPT)\n", "\n", "_refinement_results = run_or_load_results(\n", "    partial(refiner_smartpaste.refine_samples, positive_samples),\n", "    \"refinement_results.json\"\n", ")\n", "for i, (positive_sample, refinement_result) in enumerate(zip(positive_samples, _refinement_results)):\n", "    positive_sample[\"refinement_result\"] = refinement_result\n", "    \n", "positive_samples_refined = [\n", "    positive_sample for positive_sample in positive_samples\n", "    if positive_sample[\"refinement_result\"] is not None\n", "]\n", "print(f\"Succesfully refined {len(positive_samples_refined)} / {len(positive_samples)} samples\")\n", "\n", "_ = refiner_smartpaste.generate_report(positive_samples_refined, file_name_to_save=\"refinement_report.html\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reward: smartpaste"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class RewardSmartpaste:\n", "    def __init__(self):\n", "        self.gpt = GptWrapper()\n", "        \n", "    def reward_single_sample(self, args):\n", "        sample, response = args\n", "\n", "        _, _, smartpaste_code = get_smartpaste_llama_code(sample)\n", "        \n", "        match = re.search(r\"```[^\\s]*\\n*(.*?)\\n```\", response, re.DOTALL)\n", "        if not match:\n", "            print(f\"Didn't find code in response: {response}\")\n", "            return {\n", "                \"reward_value\": 0,\n", "                \"smartpaste_code\": smartpaste_code,\n", "                \"code_in_response\": None,\n", "            }\n", "        code_in_response = match.group(1)\n", "        \n", "        # strict match\n", "        # reward_value = int(smartpaste_code.strip() == code_in_response.strip())\n", "        \n", "        # easier\n", "        smartpaste_code_s = smartpaste_code.strip()\n", "        code_in_response_s = code_in_response.strip()\n", "        \n", "        if len(smartpaste_code_s.splitlines()) != len(code_in_response_s.splitlines()):\n", "            reward_value = 0\n", "        elif len(smartpaste_code_s.splitlines()) < 5:\n", "            reward_value = int(smartpaste_code_s == code_in_response_s)\n", "        else:\n", "            first_two_lines_match = smartpaste_code_s.splitlines(True)[:2] == code_in_response_s.splitlines(True)[:2]\n", "            last_two_lines_match = smartpaste_code_s.splitlines(True)[-2:] == code_in_response_s.splitlines(True)[-2:]\n", "            reward_value = int(first_two_lines_match and last_two_lines_match)\n", "            \n", "        return {\n", "            \"reward_value\": reward_value,\n", "            \"smartpaste_code\": smartpaste_code,\n", "            \"code_in_response\": code_in_response,\n", "        }\n", "        \n", "    def reward_single_sample_safe(self, args):\n", "        try:\n", "            return self.reward_single_sample(args)\n", "        except AssertionError as e:\n", "            print(e)\n", "            return None\n", "    \n", "    def reward_samples(self, samples, responses):\n", "        args = []\n", "        for sample, response in zip(samples, responses):\n", "            args.append((sample, response))\n", "        with Pool(50) as pool:\n", "            reward_results = list(tqdm(\n", "                pool.imap(self.reward_single_sample_safe, args),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return reward_results\n", "    \n", "    def generate_report(self, samples, responses, reward_results, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, (sample, response, reward_result) in tqdm(enumerate(zip(samples, responses, reward_results))):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Reward value: {reward_result['reward_value']}</p>\"\n", "            cur_html += details_html(\n", "                \"Smartpaste response\",\n", "                markdown(str(sample[\"refinement_result\"][\"smartpaste_response\"]))\n", "            )\n", "            cur_html += render_sample(sample, another_response=response)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model = RewardSmartpaste()\n", "\n", "reward_model_outputs = run_or_load_results(\n", "    partial(reward_model.reward_samples, positive_samples_refined, [s[\"refinement_result\"][\"refined_response\"] for s in positive_samples_refined]),\n", "    \"reward_model_outputs.json\"\n", ")\n", "for i, (p_s_r, reward_model_output) in enumerate(zip(positive_samples_refined, reward_model_outputs)):\n", "    p_s_r[\"reward_model_output\"] = reward_model_output\n", "\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "print(f\"Total number of samples: {len(reward_scalars)}\")\n", "print(f\"Average refined reward: {sum(reward_scalars) / len(reward_scalars)}\")\n", "\n", "\n", "_ = reward_model.generate_report(\n", "    positive_samples_refined,\n", "    [s[\"refinement_result\"][\"refined_response\"] for s in positive_samples_refined],\n", "    reward_model_outputs,\n", "    file_name_to_save=\"reward_model_report.html\"\n", ")\n", "\n", "positive_samples_refined_validated = [\n", "    p_s_r for p_s_r in positive_samples_refined\n", "    if p_s_r[\"reward_model_output\"][\"reward_value\"] == 1\n", "]\n", "print(f\"Number of samples with positive reward left: {len(positive_samples_refined_validated)} / {len(positive_samples_refined)} samples\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# reward_model_outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["original_llama_reward_model_outputs = run_or_load_results(\n", "    partial(reward_model.reward_samples, positive_samples_refined, [s[\"llama_response\"] for s in positive_samples_refined]),\n", "    \"reward_model_outputs_for_original_llama.json\"\n", ")\n", "print(f\"Average reward for original Llama: {sum(r['reward_value'] for r in original_llama_reward_model_outputs) / len(original_llama_reward_model_outputs)}\")\n", "\n", "_ = reward_model.generate_report(\n", "    positive_samples_refined,\n", "    [s[\"llama_response\"] for s in positive_samples_refined],\n", "    original_llama_reward_model_outputs,\n", "    file_name_to_save=\"reward_model_report_for_original_llama.html\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Refinement: Paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class RefinerFilepaths:\n", "    def __init__(self, prompt_template):\n", "        self.prompt_template = prompt_template\n", "        self.gpt = GptWrapper()\n", "        \n", "    def refine_single_sample(self, sample):\n", "        prompt = self.prompt_template.format(\n", "            formatted_retrieval=format_retrieval(sample),\n", "            formatted_sample=format_sample(sample),\n", "            response=wrap_message(sample[\"llama_response\"])\n", "        )\n", "        gpt4_response = self.gpt([prompt], model=\"gpt-4-1106-preview\", temperature=0, use_json=True)\n", "        assert isinstance(gpt4_response, dict)\n", "        \n", "        return {\n", "            \"prompt\": prompt,\n", "            \"full_gpt_response\": gpt4_response,\n", "            \"refined_response\": gpt4_response[\"refined_response\"],\n", "        }\n", "        \n", "    def refine_single_sample_safe(self, sample):\n", "        try:\n", "            return self.refine_single_sample(sample)\n", "        except Exception as e:\n", "            print(e)\n", "            return None\n", "    \n", "    def refine_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            refinement_results = list(tqdm(\n", "                pool.imap(self.refine_single_sample_safe, samples),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return refinement_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += details_html(\n", "                \"Refinement prompt\",\n", "                markdown(str(sample[\"refinement_result\"][\"prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[\"refinement_result\"][\"full_gpt_response\"]))\n", "            )\n", "            cur_html += render_sample(sample, sample[\"refinement_result\"][\"refined_response\"], False)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PATHS_REFINEMENT_PROMPT = \"\"\"{formatted_retrieval}\n", "\n", "{formatted_sample}\n", "\n", "Here is a response (enclosed between ~~~) to user's last message generated by AI coding assistant:\n", "{response}\n", "\n", "Your task is to rewrite AI coding assistant's response such that every referenced file from a repository is written as a full path relative to the root of the project.\n", "(see examples on code excerpts above).\n", "\n", "Requirements:\n", "- Do not rewrite paths to a general (repository-agnostic) files, like .bashrc, .gitignore, etc\n", "- Do not rewrite paths to files that are not a part of repository\n", "- Be careful with changing anything inside code blocks, to not break code\n", "- Every path to referenced file should be separately enclosed in `` (single backticks) \n", "\n", "Return JSON with a single key: `refined_response` with a string value.\n", "\"\"\"\n", "\n", "refiner_filepaths = RefinerFilepaths(PATHS_REFINEMENT_PROMPT)\n", "\n", "_refinement_results = run_or_load_results(\n", "    partial(refiner_filepaths.refine_samples, positive_samples),\n", "    \"refinement_filepaths_results.json\"\n", ")\n", "for i, (positive_sample, refinement_result) in enumerate(zip(positive_samples, _refinement_results)):\n", "    positive_sample[\"refinement_result\"] = refinement_result\n", "    \n", "positive_samples_refined = [\n", "    positive_sample for positive_sample in positive_samples\n", "    if positive_sample[\"refinement_result\"] is not None\n", "]\n", "\n", "print(f\"Succesfully refined {len(positive_samples_refined)} / {len(positive_samples)} samples\")\n", "_ = refiner_filepaths.generate_report(positive_samples_refined, file_name_to_save=\"refinement_filepaths_report.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["refiner_filepaths.gpt.get_stats()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reward: Paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class RewardFilepaths:\n", "    def __init__(self):\n", "        self.gpt = GptWrapper()\n", "        \n", "    def reward_single_sample(self, args):\n", "        sample, response = args\n", "        \n", "        extracted_paths_results = extract_paths(sample, response, self.gpt)\n", "        extracted_paths = extracted_paths_results[\"extracted_paths\"]\n", "        if len(extracted_paths) == 0:\n", "            return {\n", "                \"reward_value\": None,\n", "                \"extracted_paths\": extracted_paths,\n", "                \"all_extract_results\": extracted_paths_results,\n", "            }\n", "        \n", "        real_files = get_file_list(sample[\"extra\"][\"repo_id\"])\n", "        \n", "        for _path in extracted_paths:\n", "            _path = _path.strip(\"`\")\n", "            _path = _path.lstrip(\"/\")\n", "            if _path not in real_files and f\"/{_path}\" not in real_files:\n", "                return {\n", "                    \"reward_value\": 0,\n", "                    \"extracted_paths\": extracted_paths,\n", "                    \"all_extract_results\": extracted_paths_results,\n", "                }\n", "        return {\n", "            \"reward_value\": 1,\n", "            \"extracted_paths\": extracted_paths,\n", "            \"all_extract_results\": extracted_paths_results,\n", "        }\n", "        \n", "    def reward_single_sample_safe(self, args):\n", "        try:\n", "            return self.reward_single_sample(args)\n", "        except Exception as e:\n", "            print(e)\n", "            return {\n", "                \"reward_value\": None,\n", "                \"extracted_paths\": [],\n", "                \"all_extract_results\": {},\n", "            }\n", "        \n", "    def reward_samples(self, samples, responses):\n", "        args = []\n", "        for sample, response in zip(samples, responses):\n", "            args.append((sample, response))\n", "        with Pool(50) as pool:\n", "            reward_results = list(tqdm(\n", "                pool.imap(self.reward_single_sample_safe, args),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return reward_results   \n", "    \n", "    def generate_report(self, samples, responses, reward_results, max_samples=100, file_name_to_save=None):\n", "         samples = samples[:max_samples]\n", "         \n", "         html = \"\"\n", "         for i, (sample, response, reward_result) in tqdm(enumerate(zip(samples, responses, reward_results))):\n", "             cur_html = f\"<h2>Chain {i}</h2>\"\n", "             cur_html += f\"<p>Reward value: {reward_result['reward_value']}</p>\"\n", "             cur_html += f\"<p>Extracted paths: {reward_result['extracted_paths']}</p>\"\n", "             cur_html += render_sample(sample, another_response=response)\n", "             html += cur_html\n", "             html += \"<hr>\"\n", "             \n", "         result = wrap_html(html)\n", "         if file_name_to_save is not None:\n", "             with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                 f.write(result)\n", "         return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_filepaths = RewardFilepaths()\n", "\n", "reward_model_outputs = run_or_load_results(\n", "    partial(reward_filepaths.reward_samples, positive_samples_refined, [s[\"refinement_result\"][\"refined_response\"] for s in positive_samples_refined]),\n", "    \"reward_model_outputs.json\"\n", ")\n", "for i, (p_s_r, reward_model_output) in enumerate(zip(positive_samples_refined, reward_model_outputs)):\n", "    p_s_r[\"reward_model_output\"] = reward_model_output\n", "\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "print(f\"Total number of samples: {len(reward_scalars)}\")\n", "\n", "num_none = sum(map(lambda x: x is None, reward_scalars))\n", "num_0 = sum(map(lambda x: x == 0, reward_scalars))\n", "num_1 = sum(map(lambda x: x == 1, reward_scalars))\n", "\n", "print(f\"Fraction of None (didn't find any paths in response): {num_none / len(reward_scalars)}\")\n", "print(f\"Fraction of 0 (found paths, but they are not in the repo): {num_0 / len(reward_scalars)}\")\n", "print(f\"Fraction of 1 (found paths, and they are in the repo): {num_1 / len(reward_scalars)}\")\n", "\n", "print(f\"Average reward (ignoring None): {num_1 / (len(reward_scalars) - num_none)}\")\n", "\n", "\n", "positive_samples_refined_validated = [\n", "    p_s_r for p_s_r in positive_samples_refined\n", "    if p_s_r[\"reward_model_output\"][\"reward_value\"] == 1\n", "]\n", "\n", "_ = reward_filepaths.generate_report(\n", "    positive_samples_refined,\n", "    [s[\"refinement_result\"][\"refined_response\"] for s in positive_samples_refined],\n", "    reward_model_outputs,\n", "    file_name_to_save=\"reward_filepaths_report.html\"\n", ")\n", "print(f\"Number of samples with positive reward left: {len(positive_samples_refined_validated)} / {len(positive_samples_refined)} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# _s = positive_samples_refined[12]\n", "# _ps = _s[\"reward_model_output\"][\"extracted_paths\"]\n", "# _fs = get_file_list(_s[\"extra\"][\"repo_id\"])\n", "\n", "# _ps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_filepaths = RewardFilepaths()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["original_llama_reward_model_outputs = run_or_load_results(\n", "    partial(reward_filepaths.reward_samples, positive_samples_refined, [s[\"llama_response\"] for s in positive_samples_refined]),\n", "    \"reward_model_outputs_for_original_llama.json\"\n", ")\n", "reward_scalars = [r[\"reward_value\"] for r in original_llama_reward_model_outputs]\n", "print(f\"Total number of samples: {len(reward_scalars)}\")\n", "\n", "num_none = sum(map(lambda x: x is None, reward_scalars))\n", "num_0 = sum(map(lambda x: x == 0, reward_scalars))\n", "num_1 = sum(map(lambda x: x == 1, reward_scalars))\n", "print(f\"Fraction of None (didn't find any paths in response): {num_none / len(reward_scalars)}\")\n", "print(f\"Fraction of 0 (found paths, but they are not in the repo): {num_0 / len(reward_scalars)}\")\n", "print(f\"Fraction of 1 (found paths, and they are in the repo): {num_1 / len(reward_scalars)}\")\n", "\n", "print(f\"Average reward for original Llama: {num_1 / (len(reward_scalars) - num_none)}\")\n", "\n", "_ = reward_filepaths.generate_report(\n", "    positive_samples_refined,\n", "    [s[\"llama_response\"] for s in positive_samples_refined],\n", "    original_llama_reward_model_outputs,\n", "    file_name_to_save=\"reward_filepaths_report_for_original_llama.html\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare training data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TRAIN_FRACTION = 0.9\n", "\n", "def tokenize_sample_for_training(sample, is_refined=False):\n", "    # Only for smartpaste\n", "    # if is_refined:\n", "    #     for field in [\"path\", \"prefix\", \"selected_code\", \"suffix\"]:\n", "    #         assert field in sample, f\"{field} is not in sample\"\n", "            \n", "    history = [Exchange(request_message=x[\"user\"], response_text=x[\"assistant\"]) for x in sample[\"history\"]]\n", "    prompt_input = ChatPromptInput(\n", "        path=sample.get(\"path\", \"\"),\n", "        prefix=sample.get(\"prefix\", \"\"),\n", "        selected_code=sample.get(\"selected_code\", \"\"),\n", "        suffix=sample.get(\"suffix\", \"\"),\n", "        message=sample[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(sample.get(\"prefix\", \"\") + sample.get(\"selected_code\", \"\") + sample.get(\"suffix\", \"\")),\n", "        retrieved_chunks=[PromptChunk(**chunk) for chunk in sample[\"chunks\"]],\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "    tokenized_input = PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "    if is_refined:\n", "        target = sample[\"refinement_result\"][\"refined_response\"]\n", "    else:\n", "        target = sample[\"llama_response\"]\n", "    tokenized_output = PROMPT_FORMATTER.tokenizer.tokenize_safe(target) + [\n", "        TOKENIZER.special_tokens.eod_token\n", "    ]\n", "    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output\n", "    complete_prompt += [-1 * TOKENIZER.special_tokens.eod_token] * (\n", "        SEQUENCE_LENGTH - len(complete_prompt) + 1\n", "    )\n", "\n", "    return complete_prompt\n", "\n", "def save_dataset(samples, output_path):\n", "    random.shuffle(samples)\n", "    if not output_path.parent.exists():\n", "        output_path.parent.mkdir()\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sample in tqdm(samples):\n", "        builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    \n", "\n", "def get_mmap_samples(path: Path):\n", "    dataset = MMapIndexedDataset(str(path))\n", "    all_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        all_samples.append(dataset[i].tolist())\n", "    return all_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Binks v4 data (loading ~1min)\n", "\n", "# NUM_BINKS_SAMPLES_TO_USE = 300\n", "# BINKS_V4_PROMPTS_PATH = Path(\"/mnt/efs/augment/user/yuri/data/binks_v4_for_infer/tokenized_samples.json\")\n", "# BINKS_V4_RESPONSES_PATH = Path(\"/mnt/efs/augment/user/yuri/data/binks_v4_for_infer/llama_inference_results.json\")\n", "\n", "# with open(BINKS_V4_PROMPTS_PATH, \"r\") as f:\n", "#     binks_v4_samples = json.load(f)\n", "# with open(BINKS_V4_RESPONSES_PATH, \"r\") as f:\n", "#     binks_v4_responses = json.load(f)\n", "\n", "# print(len(binks_v4_samples), len(binks_v4_responses))\n", "\n", "# _idxs = random.sample(range(len(binks_v4_samples)), NUM_BINKS_SAMPLES_TO_USE)\n", "# tokenized_binks_v4_prompts = []\n", "\n", "# for _idx in tqdm(_idxs):\n", "#     full_tokenized_prompt = [-1 * t for t in binks_v4_samples[_idx]] + TOKENIZER.tokenize_safe(binks_v4_responses[_idx]) + [\n", "#         TOKENIZER.special_tokens.eod_token\n", "#     ]\n", "#     assert len(full_tokenized_prompt) <= SEQUENCE_LENGTH, f\"Sequence is too long: {len(full_tokenized_prompt)}\"\n", "#     full_tokenized_prompt += [-1 * TOKENIZER.special_tokens.eod_token] * (\n", "#         SEQUENCE_LENGTH - len(full_tokenized_prompt) + 1\n", "#     )\n", "#     tokenized_binks_v4_prompts.append(full_tokenized_prompt)\n", "#     # print(TOKENIZER.detokenize(list(map(abs, full_tokenized_prompt))))\n", "\n", "# binks_v4_train = tokenized_binks_v4_prompts[:int(len(tokenized_binks_v4_prompts) * TRAIN_FRACTION)]\n", "# binks_v4_valid = tokenized_binks_v4_prompts[int(len(tokenized_binks_v4_prompts) * TRAIN_FRACTION):]\n", "\n", "# print(f\"Number of Binks v4 samples train: {len(binks_v4_train)}\")\n", "# print(f\"Number of Binks v4 samples valid: {len(binks_v4_valid)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model = RewardSmartpaste()\n", "reward_model_outputs = reward_model.reward_samples(positive_samples_refined_validated,\n", "                                   [s[\"refinement_result\"][\"refined_response\"] for s in positive_samples_refined_validated])\n", "\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "\n", "num_none = sum(map(lambda x: x is None, reward_scalars))\n", "num_0 = sum(map(lambda x: x == 0, reward_scalars))\n", "num_1 = sum(map(lambda x: x == 1, reward_scalars))\n", "\n", "print(f\"Fraction of None (didn't find any paths in response): {num_none / len(reward_scalars)}\")\n", "print(f\"Fraction of 0 (found paths, but they are not in the repo): {num_0 / len(reward_scalars)}\")\n", "print(f\"Fraction of 1 (found paths, and they are in the repo): {num_1 / len(reward_scalars)}\")\n", "\n", "print(f\"Average reward (ignoring None): {num_1 / (len(reward_scalars) - num_none)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (EXPERIMENT_DIR / \"sft_data_prepared.json\").exists():\n", "    with open(EXPERIMENT_DIR / \"sft_data_prepared.json\", \"r\") as f:\n", "        prepared_training_data = json.load(f)\n", "    positive_samples_refined_validated_train = prepared_training_data[\"positive_samples_refined_validated_train\"]\n", "    positive_samples_refined_validated_valid = prepared_training_data[\"positive_samples_refined_validated_valid\"]\n", "    positive_train = prepared_training_data[\"positive_train\"]\n", "    positive_valid = prepared_training_data[\"positive_valid\"]\n", "    negative_tokenized = prepared_training_data[\"negative_tokenized\"]\n", "    negative_train = prepared_training_data[\"negative_train\"]\n", "    negative_valid = prepared_training_data[\"negative_valid\"]\n", "else:\n", "    random.seed(42)\n", "    random.shuffle(positive_samples_refined_validated)\n", "    random.shuffle(negative_samples_using_fn)\n", "    random.shuffle(negative_samples_using_gpt)\n", "    positive_samples_refined_validated_train = positive_samples_refined_validated[:int(len(positive_samples_refined_validated) * TRAIN_FRACTION)]\n", "    positive_samples_refined_validated_valid = positive_samples_refined_validated[int(len(positive_samples_refined_validated) * TRAIN_FRACTION):]\n", "    positive_train = [tokenize_sample_for_training(s, is_refined=True) for s in tqdm(positive_samples_refined_validated_train)]\n", "    positive_valid = [tokenize_sample_for_training(s, is_refined=True) for s in tqdm(positive_samples_refined_validated_valid)]\n", "\n", "    negative_tokenized = [tokenize_sample_for_training(s, is_refined=False) for s in tqdm(negative_samples_using_fn + negative_samples_using_gpt)]\n", "    negative_train = negative_tokenized[:int(len(negative_tokenized) * TRAIN_FRACTION)]\n", "    negative_valid = negative_tokenized[int(len(negative_tokenized) * TRAIN_FRACTION):]\n", "    \n", "    to_save = {\n", "        \"positive_samples_refined_validated_train\": positive_samples_refined_validated_train,\n", "        \"positive_samples_refined_validated_valid\": positive_samples_refined_validated_valid,\n", "        \"positive_train\": positive_train,\n", "        \"positive_valid\": positive_valid,\n", "        \"negative_tokenized\": negative_tokenized,\n", "        \"negative_train\": negative_train,\n", "        \"negative_valid\": negative_valid,\n", "    }\n", "    with open(EXPERIMENT_DIR / \"sft_data_prepared.json\", \"w\") as f:\n", "        json.dump(to_save, f)\n", "\n", "print(f\"Number of positive samples train: {len(positive_train)}\")\n", "print(f\"Number of positive samples valid: {len(positive_valid)}\")\n", "print(f\"Number of negative samples train: {len(negative_train)}\")\n", "print(f\"Number of negative samples valid: {len(negative_valid)}\")\n", "\n", "train_data = positive_train #+ negative_train # + binks_v4_train\n", "valid_data = positive_valid #+ negative_valid # + binks_v4_valid\n", "\n", "print(f\"Number of train samples: {len(train_data)}\")\n", "print(f\"Number of valid samples: {len(valid_data)}\")\n", "\n", "\n", "if not (EXPERIMENT_DIR / \"sft_data\").exists():\n", "    (EXPERIMENT_DIR / \"sft_data\").mkdir(exist_ok=True)\n", "    save_dataset(train_data, EXPERIMENT_DIR / \"sft_data\" / \"train.json\")\n", "    save_dataset(valid_data, EXPERIMENT_DIR / \"sft_data\" / \"valid.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENT_DIR / \"sft_data\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_check_dataset = get_mmap_samples(EXPERIMENT_DIR / \"sft_data\" / \"train\")\n", "print(\"selected code\" in TOKENIZER.detokenize(list(map(abs, _check_dataset[0]))))\n", "print(TOKENIZER.detokenize(list(map(abs, _check_dataset[0]))))\n", "\n", "# _hashes = set()\n", "# for s in tqdm(_check_dataset):\n", "#     text = TOKENIZER.detokenize(list(map(abs, s)))\n", "#     _hashes.add(hashlib.sha256(text.encode()).hexdigest())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for s in tqdm(_check_dataset):\n", "    text = TOKENIZER.detokenize(list(map(abs, s)))\n", "    if \"Can you enhance this code to handle scenarios where the CFA offset might \" in text:\n", "        print(text)\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Temperature sampling: language labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Triton part\n", "\n", "from multiprocessing import Queue\n", "\n", "ALL_IPS = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "    ]\n", "PORTS = [8000, 8010, 8020, 8030, 8040, 8050, 8060, 8070]\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]\n", "\n", "\n", "TRITON_CLIENT_QUEUE = Queue()\n", "for url in tqdm(ALL_URLS):\n", "    TRITON_CLIENT_QUEUE.put(TritonClient(url, TOKENIZER.special_tokens.eod_token))\n", "    \n", "def generate_llama_response(sample, temperature, random_seed):\n", "    history = [Exchange(request_message=x[\"user\"], response_text=x[\"assistant\"]) for x in sample[\"history\"]]\n", "    prompt_input = ChatPromptInput(\n", "        path=sample.get(\"path\", \"\"),\n", "        prefix=sample.get(\"prefix\", \"\"),\n", "        selected_code=sample.get(\"selected_code\", \"\"),\n", "        suffix=sample.get(\"suffix\", \"\"),\n", "        message=sample[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(sample.get(\"prefix\", \"\") + sample.get(\"selected_code\", \"\") + sample.get(\"suffix\", \"\")),\n", "        retrieved_chunks=[PromptChunk(**chunk) for chunk in sample[\"chunks\"]],\n", "    )\n", "    tokenized_input = PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "    input_prompt = TOKENIZER.detokenize(tokenized_input)\n", "    \n", "    triton_client = TRITON_CLIENT_QUEUE.get()\n", "    try:\n", "        response = triton_client.generate(input_prompt, temperature=temperature, random_seed=random_seed)\n", "    except requests.ConnectionError as e:\n", "        # Deliberately not putting the client back to the queue\n", "        print(f\"Connection error from {triton_client.url}\")\n", "        return generate_llama_response(sample, temperature, random_seed)\n", "    except Exception as e:\n", "        print(e)\n", "        response = None\n", "\n", "    TRITON_CLIENT_QUEUE.put(triton_client)\n", "    return response\n", "    \n", "def generate_n_llama_responses(sample, n, temperature):\n", "    result = []\n", "    for _ in range(n):\n", "        random_seed = random.randint(0, int(1e9))\n", "        result.append(generate_llama_response(sample, temperature, random_seed))\n", "    return result\n", "\n", "def generate_n_llama_responses_parallel(samples, n, temperature):\n", "    with Pool(80) as pool:\n", "        llama_responses = list(tqdm(\n", "            pool.imap(partial(generate_n_llama_responses, n=n, temperature=temperature), samples),\n", "            total=len(samples)\n", "        ))\n", "    return llama_responses"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["N_RESPONSES = 5\n", "reward_model = RewardSmartpaste()  # Ideally, you need to change only this\n", "\n", "original_llama_reward_model_outputs = run_or_load_results(\n", "    partial(reward_model.reward_samples, positive_samples, [s[\"llama_response\"] for s in positive_samples]),\n", "    \"reward_model_outputs_for_original_llama_used_in_temp_sampling.json\"\n", ")\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "print(f\"Total number of samples: {len(reward_scalars)}\")\n", "print(f\"Average LLama response reward: {sum(reward_scalars) / len(reward_scalars)}\")\n", "\n", "\n", "failed_samples = [\n", "    positive_samples[i] for i, r in enumerate(reward_model_outputs)\n", "    if r[\"reward_value\"] == 0\n", "]\n", "\n", "good_samples = [\n", "    positive_samples[i] for i, r in enumerate(reward_model_outputs)\n", "    if r[\"reward_value\"] == 1\n", "]\n", "print(f\"Number of good samples: {len(good_samples)}\")\n", "print(f\"Number of failed samples: {len(failed_samples)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llama_responses_for_failed_samples = run_or_load_results(\n", "    partial(generate_n_llama_responses_parallel, failed_samples, n=N_RESPONSES, temperature=1),\n", "    f\"llama_responses_for_failed_samples_temp_sampling_{N_RESPONSES}.json\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, response in enumerate(llama_responses_for_failed_samples[2]):\n", "    print(f\"Sample {i}\")\n", "    print(response)\n", "    print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rewards_per_sample = defaultdict(list)\n", "\n", "reward_model = RewardLanguageLabels()\n", "\n", "for i in range(N_RESPONSES):\n", "    cur_responses = [r[i] for r in llama_responses_for_failed_samples]\n", "    cur_rewards = reward_model.reward_samples(failed_samples, cur_responses)\n", "    for j, reward in enumerate(cur_rewards):\n", "        rewards_per_sample[j].append(reward[\"reward_value\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sum_reward = [sum(r) for r in rewards_per_sample.values()]\n", "Counter(sum_reward)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sum_reward"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check finetuned model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Inference\n", "from research.models.meta_model import GenerationOptions, get_model, register_model\n", "from base.fastforward.llama.model_specs import _MODEL_SPECS\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from base.fastforward.llama import model_specs as llama_model_specs\n", "\n", "LLAMA3_70B_MODEL_SPEC = _MODEL_SPECS[\"llama3-70b\"]\n", "# LLAMA3_70B_MODEL_SPEC.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp/llama3-jul5-v1-ffw\"\n", "LLAMA3_70B_MODEL_SPEC.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp_ckpts/llama3-jul27-smartpaste-new-prompt-simple-7k-v2-ffw\"\n", "# LLAMA3_70B_MODEL_SPEC.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp_ckpts/llama3-jul21-language-labels-v3-ffw\"\n", "\n", "@register_model(\"fastforward_llama3_70b_instruct\")\n", "class FastForwardLLAMA3_70B_Instruct(LLAMA_FastForwardModel):\n", "    \"\"\"The open-source LLAMA3-70B-Instruct model.\"\"\"\n", "\n", "    seq_length: int = 8192\n", "\n", "    model_spec: llama_model_specs.LlamaModelSpec = LLAMA3_70B_MODEL_SPEC\n", "\n", "    @classmethod\n", "    def create_default_formatter(cls):\n", "        tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "        apportionment = ChatTokenApportionment(\n", "            path_len=256,\n", "            message_len=0,\n", "            chat_history_len=2048,\n", "            prefix_len=1024,\n", "            selected_code_len=0,\n", "            suffix_len=1024,\n", "            max_prompt_len=6144,\n", "            retrieval_len=-1\n", "        )\n", "\n", "        prompt_formatter = get_chat_prompt_formatter_by_name(\n", "            \"binks_llama3\",\n", "            tokenizer,\n", "            apportionment,\n", "        )\n", "        return prompt_formatter\n", "    \n", "def tokenize_sample_for_inference(sample):\n", "    history = [Exchange(request_message=x[\"user\"], response_text=x[\"assistant\"]) for x in sample[\"history\"]]\n", "    prompt_input = ChatPromptInput(\n", "        path=sample.get(\"path\", \"\"),\n", "        prefix=sample.get(\"prefix\", \"\"),\n", "        selected_code=sample.get(\"selected_code\", \"\"),\n", "        suffix=sample.get(\"suffix\", \"\"),\n", "        message=sample[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(sample.get(\"prefix\", \"\") + sample.get(\"selected_code\", \"\") + sample.get(\"suffix\", \"\")),\n", "        retrieved_chunks=[PromptChunk(**chunk) for chunk in sample[\"chunks\"]],\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "    tokenized_input = PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "    return tokenized_input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Around 3 mins\n", "\n", "model = get_model(\"fastforward_llama3_70b_instruct\")\n", "model.load()\n", "model.tokenizer.eod_id = model.tokenizer.special_tokens.eos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sft_generations = []\n", "\n", "for i, sample in tqdm(enumerate(positive_samples_refined_validated_valid[:20]), total=len(positive_samples_refined_validated_valid[:20])):\n", "    tokenized_sample = tokenize_sample_for_inference(sample)\n", "    cur_response = model.raw_generate(\n", "        tokenized_sample,\n", "        GenerationOptions(max_generated_tokens=2048)\n", "    )\n", "    sft_generations.append(cur_response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(tokenized_sample))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model = RewardSmartpaste()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model_outputs = reward_model.reward_samples(positive_samples_refined_validated_valid[:20], sft_generations)\n", "\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "\n", "num_none = sum(map(lambda x: x is None, reward_scalars))\n", "num_0 = sum(map(lambda x: x == 0, reward_scalars))\n", "num_1 = sum(map(lambda x: x == 1, reward_scalars))\n", "\n", "print(f\"Fraction of None (didn't find any paths in response): {num_none / len(reward_scalars)}\")\n", "print(f\"Fraction of 0 (found paths, but they are not in the repo): {num_0 / len(reward_scalars)}\")\n", "print(f\"Fraction of 1 (found paths, and they are in the repo): {num_1 / len(reward_scalars)}\")\n", "\n", "print(f\"Average reward (ignoring None): {num_1 / (len(reward_scalars) - num_none)}\")\n", "\n", "_ = reward_model.generate_report(\n", "    positive_samples_refined_validated_valid,\n", "    sft_generations,\n", "    reward_model_outputs,\n", "    file_name_to_save=\"check_finetuned_model_rewards_finetuned.html\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = reward_model.reward_single_sample((positive_samples_refined_validated_valid[6], sft_generations[6]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_a = {'reward_value': 0,\n", " 'smartpaste_code': '            fontType = cv2.FONT_HERSHEY_TRIPLEX\\n            cv2.rectangle(depthFrameColor, (xmin, ymin), (xmax, ymax), color, cv2.FONT_HERSHEY_SCRIPT_SIMPLEX)\\n            cv2.putText(depthFrameColor, f\"X: {int(depthData.spatialCoordinates.x)} mm\", (xmin + 10, ymin + 20), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Y: {int(depthData.spatialCoordinates.y)} mm\", (xmin + 10, ymin + 35), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Z: {int(depthData.spatialCoordinates.z)} mm\", (xmin + 10, ymin + 50), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Min Depth: {int(depthMin)} mm\", (xmin + 10, ymin + 65), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Max Depth: {int(depthMax)} mm\", (xmin + 10, ymin + 80), fontType, 0.5, 255)',\n", " 'code_in_response': '            fontType = cv2.FONT_HERSHEY_TRIPLEX\\n            cv2.rectangle(depthFrameColor, (xmin, ymin), (xmax, ymax), color, cv2.FONT_HERSHEY_SCRIPT_SIMPLEX)\\n            cv2.putText(depthFrameColor, f\"X: {int(depthData.spatialCoordinates.x)} mm\", (xmin + 10, ymin + 20), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Y: {int(depthData.spatialCoordinates.y)} mm\", (xmin + 10, ymin + 35), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Z: {int(depthData.spatialCoordinates.z)} mm\", (xmin + 10, ymin + 50), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Depth Min: {depthMin} mm\", (xmin + 10, ymin + 65), fontType, 0.5, 255)\\n            cv2.putText(depthFrameColor, f\"Depth Max: {depthMax} mm\", (xmin + 10, ymin + 80), fontType, 0.5, 255)'}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model_outputs = reward_model.reward_samples(positive_samples_refined_validated_valid[:20], [s[\"llama_response\"] for s in positive_samples_refined_validated_valid])\n", "\n", "reward_scalars = [r[\"reward_value\"] for r in reward_model_outputs]\n", "\n", "num_none = sum(map(lambda x: x is None, reward_scalars))\n", "num_0 = sum(map(lambda x: x == 0, reward_scalars))\n", "num_1 = sum(map(lambda x: x == 1, reward_scalars))\n", "\n", "print(f\"Fraction of None (didn't find any paths in response): {num_none / len(reward_scalars)}\")\n", "print(f\"Fraction of 0 (found paths, but they are not in the repo): {num_0 / len(reward_scalars)}\")\n", "print(f\"Fraction of 1 (found paths, and they are in the repo): {num_1 / len(reward_scalars)}\")\n", "\n", "print(f\"Average reward (ignoring None): {num_1 / (len(reward_scalars) - num_none)}\")\n", "\n", "_ = reward_model.generate_report(\n", "    positive_samples_refined_validated_valid,\n", "    [s[\"llama_response\"] for s in positive_samples_refined_validated_valid],\n", "    reward_model_outputs,\n", "    file_name_to_save=\"check_finetuned_model_rewards_original_llama.html\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = reward_filepaths.generate_report(\n", "    positive_samples_refined_validated_valid,\n", "    [s[\"llama_response\"] for s in positive_samples_refined_validated_valid],\n", "    reward_model_outputs,\n", "    file_name_to_save=\"check_finetuned_model_rewards_original_llama.html\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_file_list(positive_samples_refined_validated_valid[58][\"extra\"][\"repo_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_SAMPLES: dict[str, Any] = {\n", "    \"code_edits\": [\n", "        (\"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\", \"Model should modify the whole selected code\"),\n", "        (\"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\", \"Model should modify only the import line.\"),\n", "        (\"75aedc45-11f6-4a5a-98d9-43798ba29479\", \"Model should print only modified code, without repeating functions.\"),\n", "    ],\n", "    \"code_edits_one_turn\" : [\n", "        (\"4b958757-6818-44f8-af55-2a63e82b8458\", \"Model should modify only selected code.\"),\n", "        (\"7fd0623b-c217-4658-89f9-af27246f7bfd\", \"Model shouldn't lose T = TypeVar(\\\"T\\\")\"),\n", "        (\"974831ff-997d-4de8-984b-94e6c6bd42dc\", \"Model should preserve new line character at the end of selection\"),\n", "    ],\n", "    \"regression_tests\": [\n", "        (\"5199b947-b45a-4e22-a087-e59a12beda91\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718823357108449?thread_ts=1718821966.276869&cid=C06R495KUSD\"),\n", "        (\"3bb42ee8-c40b-4c69-96cf-9bf23e8548fb\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1717614811054099\"),\n", "        (\"f08c5194-6cb6-4480-87a1-b256ab2f52e2\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718918465955939\"),\n", "        (\"40176c9d-2acb-44e9-b759-b16a21e5aeb6\", \"Explanation of selected code.\"),\n", "        (\"cd09df68-6189-4cb9-9032-87810deda180\", \"Explanation of selected code on 2nd turn.\"),\n", "    ],\n", "    \"filepaths\": [\n", "        \n", "    ]\n", "}"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
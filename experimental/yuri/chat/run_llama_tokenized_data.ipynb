{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from typing import Any\n", "from tqdm import tqdm\n", "from experimental.yuri.preferences.utils import TritonClient\n", "from pathlib import Path\n", "\n", "\n", "ALL_IPS = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "    ]\n", "PORTS = [8000, 8010, 8020, 8030, 8040, 8050, 8060, 8070]\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "PROMPTS_PATH = Path(\"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul27_5k_pro_simple_boundaries_new_prompt/tokenized_samples.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_generated = 0\n", "num_failed = 0\n", "results = None\n", "\n", "def run_prompt_triton(prompts):\n", "    import threading\n", "    from queue import Queue\n", "    \n", "    eod_token = TOKENIZER.special_tokens.eod_token\n", "    \n", "    FAILED_URLS = []\n", "    client_queue = Queue()\n", "    \n", "    for url in tqdm(ALL_URLS):\n", "        client_queue.put(TritonClient(url, eod_token))\n", "        \n", "    threads = []\n", "    lock = threading.Lock()\n", "    \n", "    def generate_llama(prompt, index):\n", "        client = client_queue.get()\n", "        try:\n", "            response = client.generate(prompt)\n", "        except requests.exceptions.ConnectionError as e:\n", "            print(f\"Connection error from {client.url}\")\n", "            FAILED_URLS.append(client.url)\n", "            return generate_llama(prompt, index)\n", "        except Exception as e:\n", "            print(type(e))\n", "            with lock:\n", "                global num_failed\n", "                num_failed += 1\n", "            response = None\n", "            \n", "        with lock:\n", "            global results\n", "            results[index] = response # type: ignore\n", "            global num_generated\n", "            num_generated += 1\n", "            if num_generated % 50 == 0:\n", "                print(f\"Generated {num_generated} samples\")\n", "        client_queue.put(client)\n", "        \n", "    global num_generated, num_failed, results\n", "    num_generated = 0\n", "    num_failed = 0\n", "    results = [None] * len(prompts)\n", "    \n", "    for i, prompt in enumerate(tqdm(prompts)):\n", "        cur_thread = threading.Thread(target=generate_llama, args=(prompt, i))\n", "        cur_thread.start()\n", "        threads.append(cur_thread)\n", "        \n", "    for cur_thread in threads:\n", "        cur_thread.join()\n", "        \n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with PROMPTS_PATH.open(\"r\") as f:\n", "    prompts_tokenized = json.load(f)\n", "    for i in range(len(prompts_tokenized)):\n", "        prompts_tokenized[i] = json.loads(prompts_tokenized[i])\n", "\n", "len(prompts_tokenized)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompts_text = [TOKENIZER.detokenize(x[\"prompt_tokens\"]) for x in tqdm(prompts_tokenized)]\n", "print(prompts_text[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = run_prompt_triton(prompts_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert len(prompts_tokenized) == len(results) == len(prompts_text)\n", "\n", "for i in range(len(results)):\n", "    prompts_tokenized[i][\"llama_response\"] = results[i]\n", "\n", "# combined_results = [\n", "#     {\n", "#         \"prompt_tokens\": prompts_tokenized[i],\n", "#         \"prompt_text\": prompts_text[i],\n", "#         \"response_tokens\": results[i],\n", "#     }\n", "#     for i in range(len(prompts_tokenized))\n", "# ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with (PROMPTS_PATH.parent / \"llama_inference_results.json\").open(\"w\") as f:\n", "    json.dump(prompts_tokenized, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(results[0])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPTS_PATH.parent / \"llama_inference_results.json\""]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
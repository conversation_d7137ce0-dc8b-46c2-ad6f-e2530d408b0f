{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "\n", "import os\n", "\n", "os.environ[\"PYTHONPATH\"] += \":/home/<USER>/repos/augment/research/gpt-neox:/home/<USER>/repos/augment\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import threading\n", "from collections import Counter, defaultdict\n", "from multiprocessing import Pool\n", "from queue import Queue\n", "\n", "import requests\n", "from markdown import markdown\n", "from tqdm import tqdm\n", "\n", "from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format_completion.prompt_formatter import PromptChunk\n", "from base.tokenizers import create_tokenizer_by_name\n", "from experimental.guy.apis.llama3_tokenizer import ChatFormat, Message, Tokenizer\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from research.eval.harness.factories import create_retriever\n", "\n", "DATA_PATH = \"/mnt/efs/augment/user/yury/binks/binks-v3/repos_with_qa.jsonl\"\n", "\n", "RETRIEVER_CONFIG = {\n", "    \"scorer\": {\n", "        \"name\": \"generic_neox\",\n", "        \"checkpoint_path\": \"chatanol/chatanol1-11\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query_simple_chat\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "        \"verbose\": True,\n", "        \"tokenizer_name\": \"starcodertokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=0,\n", "    chat_history_len=2048,\n", "    prefix_len=1024,\n", "    selected_code_len=0,\n", "    suffix_len=1024,\n", "    max_prompt_len=6144,\n", "    retrieval_len=-1\n", ")\n", "PROMPT_FORMATTER = get_chat_prompt_formatter_by_name(\n", "    \"binks_llama3\",\n", "    TOKENIZER,\n", "    APPORTIONMENT,\n", ")\n", "\n", "\n", "\n", "ALL_IPS = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "\n", "    \"**************\",\n", "    \"*************\",\n", "]\n", "\n", "PORTS = [\"8000\"]\n", "\n", "NUM_REPOS = 10\n", "NUM_QUESTIONS = 3\n", "NUM_RETRIES = 5\n", "\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RETRIEVER = create_retriever(RETRIEVER_CONFIG)\n", "RETRIEVER.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_data(path):\n", "    data = []\n", "\n", "    with open(path) as f:\n", "        for line in tqdm(f):\n", "            data.append(json.loads(line))\n", "\n", "    return data\n", "\n", "\n", "def get_docs(sample):\n", "    docs = []\n", "    for doc_dict in sample[\"file_list\"]:\n", "        cur_doc = Document.new(\n", "            text=doc_dict[\"content\"],\n", "            path=doc_dict[\"max_stars_repo_path\"],\n", "        )\n", "        docs.append(cur_doc)\n", "\n", "    return docs\n", "\n", "\n", "def convert_chunk(chunk: Chunk):\n", "    return PromptChunk(\n", "        text=chunk.text,\n", "        path=chunk.parent_doc.path,\n", "        char_start=chunk.char_offset,\n", "        char_end=chunk.char_offset + chunk.length,\n", "        unique_id=chunk.id,\n", "    )\n", "\n", "def get_full_prompt(question: str):\n", "    chunks, _ = RETRIEVER.query(\n", "    model_input=ModelInput(\n", "        extra={\n", "            \"message\": question\n", "        }\n", "    ),\n", "    top_k=32\n", "    )\n", "\n", "    chunks = [*map(convert_chunk, chunks)]\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=\"\",\n", "        prefix=\"\",\n", "        selected_code=\"\",\n", "        suffix=\"\",\n", "        message=question,\n", "        chat_history=[],\n", "        prefix_begin=0,\n", "        suffix_end=0,\n", "        retrieved_chunks=chunks,\n", "    )\n", "\n", "    prompt_text = TOKENIZER.detokenize(\n", "        PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "    )\n", "\n", "    return prompt_text, chunks\n", "\n", "class TritonClient:\n", "    def __init__(self, url: str):\n", "        self.tokenizer = Tokenizer(\"/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct/tokenizer.model\")\n", "        self.prompt_formatter = ChatFormat(self.tokenizer)\n", "        self.url = url\n", "\n", "    def generate(self, user_message: str):\n", "        dialog = [\n", "            Message(role=\"user\", content=user_message)\n", "        ]\n", "\n", "        full_prompt = self.tokenizer.decode(\n", "            self.prompt_formatter.encode_dialog_prompt(dialog)\n", "        )\n", "        payload = self.get_payload(full_prompt)\n", "        response_json = self.send_request(payload)\n", "\n", "        return response_json[\"text_output\"]\n", "\n", "    def get_payload(self, full_prompt):\n", "        payload = {\n", "            \"text_input\": full_prompt,\n", "            \"max_tokens\": 1000,\n", "            \"end_id\": self.tokenizer.special_tokens[\"<|eot_id|>\"],\n", "            \"stream\": <PERSON><PERSON><PERSON>,\n", "            \"temperature\": 0.8,\n", "            \"top_k\": 40,\n", "            \"top_p\": 0.95,\n", "            \"random_seed\": random.randint(0, int(2 ** 31)),\n", "            \"return_context_logits\": <PERSON><PERSON><PERSON>,\n", "            \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "            \"return_generation_logits\": <PERSON><PERSON><PERSON>,\n", "        }\n", "\n", "        return payload\n", "\n", "    def send_request(self, payload):\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "\n", "        response = requests.post(\n", "            f\"http://{self.url}/v2/models/ensemble/generate\",\n", "            headers=headers,\n", "            data=json.dumps(payload),\n", "            timeout=100,\n", "        )\n", "        response_json = response.json()\n", "\n", "        return response_json\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = load_data(DATA_PATH)\n", "print(len(data))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = [x for x in data if x[\"max_file_lang\"][\"langpart\"] == \"python\"]\n", "len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["full_samples = {}\n", "\n", "random.seed(42)\n", "\n", "for i in tqdm(random.sample(\n", "    range(0, len(data)), NUM_REPOS\n", ")):\n", "    cur_sample = data[i]\n", "    RETRIEVER.remove_all_docs()\n", "    RETRIEVER.add_docs(get_docs(cur_sample))\n", "\n", "    num_questions = len(cur_sample[\"documents_with_questions\"])\n", "\n", "    for j in random.sample(\n", "        range(0, num_questions), NUM_QUESTIONS\n", "    ):\n", "        cur_question = cur_sample[\"documents_with_questions\"][j][\"question\"]\n", "        cur_prompt, cur_chunks = get_full_prompt(cur_question)\n", "\n", "        full_samples[(i, j)] = {\n", "            \"prompt\": cur_prompt,\n", "            \"chunks\": cur_chunks,\n", "            \"question\": cur_question,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client_queue = Queue()\n", "\n", "for url in tqdm(ALL_URLS):\n", "    client_queue.put(TritonClient(url))\n", "\n", "threads = []\n", "lock = threading.Lock()\n", "num_generated = 0\n", "\n", "def generate_llama(prompt, indices):\n", "    client = client_queue.get()\n", "    response = client.generate(prompt)\n", "    with lock:\n", "        if \"responses\" not in full_samples[indices]:\n", "            full_samples[indices][\"responses\"] = []\n", "        full_samples[indices][\"responses\"].append(response)\n", "        global num_generated\n", "        num_generated += 1\n", "        print(f\"Generated {num_generated} samples\")\n", "    client_queue.put(client)\n", "\n", "print(f\"Total to generate: {NUM_REPOS * NUM_QUESTIONS * NUM_RETRIES}\")\n", "\n", "for (i, j), full_sample in full_samples.items():\n", "    for k in range(NUM_RETRIES):\n", "        cur_thread = threading.Thread(\n", "            target=generate_llama,\n", "            args=(  full_sample[\"prompt\"], (i, j) )\n", "        )\n", "        threads.append(cur_thread)\n", "        cur_thread.start()\n", "\n", "for thread in threads:\n", "    thread.join()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# UNARY_PROMPT = \"\"\"Here are code snippets from codebase:\n", "# {rendered_chunks}\n", "\n", "# Here is a question asked about this codebase:\n", "# {question}\n", "\n", "# Here is the answer to this question generated by 3rd party service, enclosed between ===:\n", "# ===\n", "# {answer}\n", "# ===\n", "\n", "# Your task is to detect hallucinations in the answer.\n", "# Hallucination - is any information that is not grounded in the provided code snippets or general knowledge.\n", "# Examples: fabricated facts, nonexistent entities (e.g. files, variables, functions, classes, fields, etc), etc\n", "\n", "# Follow these steps (IN THE FOLLOWING ORDER) to complete this task:\n", "# 1. <PERSON><PERSON><PERSON> provided code snippets and question.\n", "# 2. Write a thorough analysis of whether hallucination present in the answer.\n", "# 3. Explicitly write YES (if hallucination is detected) or NO (if hallucination is NOT detected).\n", "# \"\"\"\n", "\n", "\n", "UNARY_PROMPT = \"\"\"Here are code snippets from project's codebase:\n", "{rendered_chunks}\n", "\n", "Here is a question asked about this codebase:\n", "{question}\n", "\n", "Here is the answer to this question generated by 3rd party service, enclosed between ===:\n", "===\n", "{answer}\n", "===\n", "\n", "Your task is to detect hallucinations in the answer.\n", "A hallucination is any information that is either not supported by the provided code snippets or not part of established general knowledge. This includes fabricated details, as well as references to non-existent entities such as files, variables, functions, classes, or fields.\n", "\n", "Follow these steps (IN THE FOLLOWING ORDER) to complete this task:\n", "1. <PERSON><PERSON><PERSON> provided code snippets and question.\n", "2. Write a thorough analysis of whether hallucinations are present in the answer.\n", "3. Explicitly write \"YES\" (if hallucinations are detected) or \"NO\" (if hallucinations are NOT detected).\n", "\n", "Additionally, ensure that:\n", "- You verify the existence of all named entities (functions, variables, classes, etc.) directly against the code snippets provided.\n", "- You consider both the presence of the entities and their contextual usage when determining the accuracy of the response.\n", "- You are paying close attention to all the code snippets provided!\n", "\n", "\"\"\"\n", "\n", "UNARY_EXTRACTING_PROMPT = \"\"\"Return results as a JSON with 2 keys:\n", "- is_hallucination\n", "- explanation\n", "\n", "If answer contains hallucinations (YES), then is_hallucination must be True and explanation should contain short description of what hallucinations are detected.\n", "If answer does not contain hallucinations (NO), then is_hallucination must be False and explanation should be empty.\n", "\"\"\"\n", "\n", "\n", "def render_chunks(chunks: list[PromptChunk]):\n", "    per_chunk = []\n", "    for c in chunks:\n", "        cur_text = f\"\"\"\n", "\n", "Here is the snippet from `{c.path}`:\n", "\n", "```\n", "{c.text}\n", "```\n", "\n", "\"\"\"\n", "        per_chunk.append(cur_text)\n", "\n", "    return \"\".join(per_chunk)\n", "\n", "\n", "def unary_run(inputs):\n", "    \"\"\"\n", "        Returns:\n", "            - main_prompt : string\n", "            - gpt4_response : string\n", "            - is_hallucination : boolean\n", "            - explanation : string\n", "    \"\"\"\n", "    full_sample, gpt, (_, _, k) = inputs\n", "\n", "    rendered_chunks = render_chunks(full_sample[\"chunks\"])\n", "\n", "    prompt = UNARY_PROMPT.format(\n", "        rendered_chunks=rendered_chunks,\n", "        question=full_sample[\"question\"],\n", "        answer=full_sample[\"responses\"][k],\n", "    )\n", "\n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "\n", "    messages.append({\"role\": \"assistant\", \"content\": gpt4_response})\n", "    messages.append({\"role\": \"user\", \"content\": UNARY_EXTRACTING_PROMPT})\n", "\n", "    extracted_response = gpt(messages, model=\"gpt-4o\", use_json=True, temperature=0)\n", "\n", "    return {\n", "        \"gpt4_prompt\": prompt,\n", "        \"gpt4_response\": gpt4_response,\n", "        \"rendered_chunks\": rendered_chunks,\n", "        **extracted_response,\n", "    }\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()\n", "\n", "gpt_inputs = []\n", "for (i, j), full_sample in full_samples.items():\n", "    for k in range(NUM_RETRIES):\n", "        cur_input = (\n", "            full_sample, gpt, (i, j, k)\n", "        )\n", "        gpt_inputs.append(cur_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with Pool(50) as pool:\n", "    gpt4_results = list(tqdm(pool.imap(unary_run, gpt_inputs), total=len(gpt_inputs)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(gpt4_results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for _, full_sample in full_samples.items():\n", "    full_sample[\"gpt4_result\"] = [None] * NUM_RETRIES\n", "\n", "for gpt4_result, gpt4_input in zip(gpt4_results, gpt_inputs):\n", "    _, _, (i, j, k) = gpt4_input\n", "    full_samples[(i, j)][\"gpt4_result\"][k] = gpt4_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_hallucinations = []\n", "\n", "all_total = 0\n", "num_samples = 0\n", "for (i, j), full_sample in full_samples.items():\n", "    cur_total = 0\n", "    for gpt_result in full_sample[\"gpt4_result\"]:\n", "        cur_total += gpt_result[\"is_hallucination\"]\n", "        all_total += gpt_result[\"is_hallucination\"]\n", "        num_samples += 1\n", "    num_hallucinations.append(cur_total)\n", "\n", "print(f\"P(hallucination): {all_total / num_samples}\")\n", "\n", "print(\"P(number of good retries):\")\n", "counter = Counter(num_hallucinations)\n", "for k in sorted(counter.keys()):\n", "    print(f\"{k}: {counter[k]}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_answers_list(full_sample):\n", "    html = \"<ul>\\n\"\n", "    for response, gpt4_result in zip(full_sample[\"responses\"], full_sample[\"gpt4_result\"]):\n", "        color = \"red\" if gpt4_result[\"is_hallucination\"] else \"green\"\n", "        circle = f'<span style=\"color:{color};\">&#9679;</span>'\n", "\n", "        rendered_response = markdown(response, extensions=[\"fenced_code\", \"codehilite\"])\n", "\n", "        if isinstance(gpt4_result[\"explanation\"], list):\n", "            gpt4_result[\"explanation\"] = \"\".join(gpt4_result[\"explanation\"])\n", "        rendered_explanation = markdown(gpt4_result[\"explanation\"], extensions=[\"fenced_code\", \"codehilite\"])\n", "        gpt_response = markdown(gpt4_result[\"gpt4_response\"], extensions=[\"fenced_code\", \"codehilite\"])\n", "        gpt_prompt = markdown(gpt4_result[\"gpt4_prompt\"], extensions=[\"fenced_code\", \"codehilite\"])\n", "\n", "        if gpt4_result[\"is_hallucination\"]:\n", "            html += f\"\"\"<li>\n", "<details>\n", "    <summary>{circle}</summary>\n", "    <div class='highlighted-text'>{rendered_response}</div>\n", "    <div class='highlighted-text'>{rendered_explanation}</div>\n", "</details>\n", "</li>\"\"\"\n", "            # html += f\"  <li class='highlighted-text'>{circle} {rendered_response} - {rendered_explanation}</li>\\n\"\n", "        else:\n", "            html += f\"\"\"<li>\n", "<details>\n", "    <summary>{circle}</summary>\n", "    <div class='highlighted-text'>{rendered_response}</div>\n", "</details>\n", "</li>\"\"\"\n", "            # html += f\"  <li class='highlighted-text'>{circle} {rendered_response}</li>\\n\"\n", "\n", "        html += f\"\"\"\n", "    <details>\n", "        <summary>GPT analysis</summary>\n", "        <p>{gpt_response}</p>\n", "    </details>\n", "    <details>\n", "        <summary>GPT prompt</summary>\n", "        <p>{gpt_prompt}</p>\n", "    </details>\n", "    \"\"\"\n", "        html += \"<hr>\"\n", "    html += \"</ul>\"\n", "\n", "    return html\n", "\n", "\n", "\n", "HTML_START = \"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Synthetic Preferences</title>\n", "    <style>\n", "        .wide-line {\n", "            width: 100%;\n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }\n", "        .highlighted-text {\n", "            background-color: #f0f0f0; /* Light grey background */\n", "            color: #333; /* Dark grey text color */\n", "            padding: 15px; /* Padding around the text */\n", "            margin: 10px 0; /* Margin for spacing between items */\n", "            border-radius: 8px; /* Rounded corners */\n", "            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */\n", "            list-style-type: none; /* Remove default list styling */\n", "            font-family: Arial, sans-serif; /* Modern font */\n", "        }\n", "    </style>\n", "    <meta charset=\"UTF-8\">\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for responses, unary_run_results in zip(all_responses, all_unary_results):\n", "\n", "rendered_parts = []\n", "for (i, j), full_sample in tqdm(full_samples.items()):\n", "    rendered_chunks = full_sample[\"gpt4_result\"][0][\"rendered_chunks\"]\n", "    rendered_chunks = markdown(rendered_chunks, extensions=[\"fenced_code\", \"codehilite\"])\n", "    llama_prompt = markdown(full_sample[\"prompt\"], extensions=[\"fenced_code\", \"codehilite\"])\n", "\n", "    rendered_answer_list = render_answers_list(full_sample)\n", "\n", "    cur_html = f\"\"\"<hr>\n", "    <h3>Question: {full_sample[\"question\"]}\n", "    <details>\n", "        <summary>Retrieved chunks</summary>\n", "        <p>{rendered_chunks}</p>\n", "    </details>\n", "    <details>\n", "        <summary>LLama Prompt</summary>\n", "        <p>{llama_prompt}</p>\n", "    </details>\n", "    {rendered_answer_list}\n", "    \"\"\"\n", "\n", "    rendered_parts.append(cur_html)\n", "\n", "full_html = HTML_START + \"\".join(rendered_parts) + HTML_END\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"./n16.html\", \"w\") as f:\n", "    f.write(full_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
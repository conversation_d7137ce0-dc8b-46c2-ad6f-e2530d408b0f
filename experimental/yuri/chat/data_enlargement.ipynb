{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", "\n", "with open(\"/home/<USER>/.openai\", \"r\") as f:\n", "    os.environ[\"OPENAI_API_KEY\"] = f.read().strip()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import re\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "\n", "from copy import deepcopy\n", "from multiprocessing import Pool\n", "\n", "import experimental.yury.binks.datav3.gemini_api as gemini_api\n", "from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n", "from experimental.yuri.preferences.utils import markdown, details_html, render_simple_message, wrap_html\n", "\n", "\n", "META_CONVERSATIONS_PATH = Path(\"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/meta_conversations_jul1_jul16_mercor_763.json\")\n", "META_CONVERSATIONS_PATH_2 = Path(\"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/meta_conversations_jul1_jul16_turing_869.json\")\n", "# META_CONVERSATIONS_PATH_2 = None\n", "REPOS_PATTERN = Path(\"/mnt/efs/augment/user/yury/binks/binks-v3.1\") / \"03_raw_repos/*.zstd.parquet\"\n", "PERSONAS_PATH = Path(\"/mnt/efs/augment/user/yuri/data/personas_69k_jul7.json\")\n", "\n", "OUTPUT_PATH = \"/home/<USER>/repos/augment/experimental/yuri/chat/jul27_5k_pro_simple_boundaries.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# HTML rendering\n", "\n", "def render_sample(sample):\n", "    history = sample[\"history\"]\n", "    \n", "    cur_index = 1\n", "    history_html = \"\"\n", "    for turn in history:\n", "        history_html += f\"<h3>Message {cur_index}</h3>\"\n", "        cur_index += 1\n", "        history_html += render_simple_message(turn[\"user\"], turn[\"assistant\"])\n", "        history_html += \"<hr>\"\n", "    if len(history) > 0:\n", "        history_html = details_html(\"Chat history\", history_html)\n", "        \n", "    history_html += f\"<h3>Message {cur_index}</h3>\"\n", "\n", "    history_html += render_simple_message(\n", "        sample[\"message\"],\n", "        sample[\"response\"],\n", "        sample.get(\"selected_code\", None)\n", "        )\n", "    \n", "    return history_html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Main logic\n", "\n", "def get_prompt(repo, meta_conversation, persona, selected_code=\"$SELECTED_CODE\"):\n", "    meta_conversation = deepcopy(meta_conversation)\n", "    \n", "    meta_conversation_turns = meta_conversation[\"meta_conversation_turns\"]\n", "    num_turns = len(meta_conversation_turns)\n", "    \n", "    if meta_conversation[\"is_selected_code\"]:\n", "        meta_conversation_turns[-1][\"user\"] = f\"User message MANDATORY starts with 'I have selected piece of code in file `$FILE_PATH` : \\n```(language)\\n{selected_code}\\n```\\n'. \" + \\\n", "                                              (\"$SELECTED_CODE is a precise excerpt (<50 lines) from the file $FILE_PATH, ALWAYS enclosed in ```(language)```). \" if selected_code == \"$SELECTED_CODE\" else \"\") + \\\n", "                                              \"$FILE_PATH is a full path to the existing file in repo. \" + \\\n", "                                              meta_conversation_turns[-1][\"user\"]\n", "\n", "    \n", "    meta_conversation_str = \"\"\n", "    for i, turn in enumerate(meta_conversation_turns):\n", "        meta_conversation_str += f\"\"\"\n", "User ({i + 1}): {turn[\"user\"]}\n", "Assistant ({i + 1}): {turn[\"assistant\"]}\n", "\n", "\"\"\"\n", "    \n", "    prompt = f\"\"\"{repo.concat_repo_to_string()}\n", "    \n", "---\n", "\n", "Software engineer is working on the repository above. Here is a description of software developer's persona:\n", "```\n", "{persona}\n", "```\n", "\n", "Your task is to simulate a {num_turns}-turn conversation between this software engineer (the user) and the AI assistant.\n", "\n", "Instructions: \n", "- Each conversation turn consists of a User message and Assistant response.\n", "- Code snippets in messages shouldn't be longer than 50 lines.\n", "- Each user message should start with #User_N, where N is the turn number.\n", "- Each assistant message should start with #Assistant_N, where N is the turn number.\n", "- Output the simulated conversation, nothing else.\n", "- Detailed description of each conversation turn is provided below. You MUST follow these instructions closely.\n", "- If possible, conversation should be relevant to developer's persona.\n", "\n", "Output format (for N-turn conversation):\n", "#User_1\n", "...\n", "#Assistant_1\n", "...\n", "#User_2\n", "...\n", "#Assistant_2\n", "...\n", "#User_N\n", "...\n", "#Assistant_N\n", "...\n", "\n", "\n", "Descriptions of each conversation turn:\n", "{meta_conversation_str}\n", "\"\"\"\n", "\n", "    return prompt\n", "\n", "\n", "def parse_generated_conversation(response, num_expected_turns):\n", "    generated_conversation = []\n", "    \n", "    for i in range(1, num_expected_turns + 1):\n", "        # Parse user message\n", "        user_match = re.search(rf\"#User_{i}\\s*(.*?)\\n#Assistant_{i}\", response, re.DOTALL)\n", "        assert user_match, f\"Failed user message {i} / {num_expected_turns}. {response}\"\n", "        user_message = user_match.group(1)\n", "        \n", "        # Parse assistant message\n", "        if i == num_expected_turns:\n", "            assistant_match = re.search(rf\"#Assistant_{i}\\s*(.*?)$\", response, re.DOTALL)\n", "        else:\n", "            assistant_match = re.search(rf\"#Assistant_{i}\\s*(.*?)\\n#User_{i + 1}\", response, re.DOTALL)\n", "        assert assistant_match, f\"Failed assistant message {i} / {num_expected_turns}. {response}\" \n", "        assistant_message = assistant_match.group(1)\n", "        \n", "        generated_conversation.append({\n", "            \"user\": user_message,\n", "            \"assistant\": assistant_message\n", "        })\n", "        \n", "    assert len(generated_conversation) == num_expected_turns, f\"Incorrect number of turns: {len(generated_conversation)} vs expected {num_expected_turns}\"\n", "    \n", "    return generated_conversation\n", "    \n", "\n", "def parse_message_with_selected_code(user_message, repo):\n", "    match = re.search(r\"I have selected piece of code in file `(.*?)`\\s*:\\s?\\n```([\\w+#]*)\\n(.*?)\\n```(.*)\", user_message, re.DOTALL)\n", "    assert match, f\"Failed to parse selected code from user message: {user_message}\"\n", "    \n", "    file_path = match.group(1)\n", "    language = match.group(2)\n", "    selected_code = match.group(3)\n", "    message = match.group(4)\n", "    \n", "    # Find file from which selected code was taken\n", "    file = [file for file in repo.repo[\"file_list\"] if file[\"max_stars_repo_path\"] == file_path]\n", "    if len(file) == 0:\n", "        file = [file for file in repo.repo[\"file_list\"] if file[\"max_stars_repo_path\"].endswith(file_path)]\n", "    assert len(file) == 1, f\"Number of selected files: {len(file)}\"\n", "    file = file[0]\n", "    \n", "    # Find position in file where selected code is located\n", "    start_line, num_lines = approximate_search(file[\"content\"], selected_code)\n", "    if start_line == -1:\n", "        assert False, f\"Failed to find selected code in file: {file_path}\"\n", "        \n", "    prefix = \"\".join(file[\"content\"].splitlines(keepends=True)[:start_line])\n", "    suffix = \"\".join(file[\"content\"].splitlines(keepends=True)[start_line + num_lines:])\n", "    \n", "    message = message.lstrip(\"\\n\\t .\")\n", "    \n", "    assert len(message) > 0, f\"Empty message\"\n", "\n", "    return {\n", "        \"path\": file_path,\n", "        \"language\": language,\n", "        \"prefix\": prefix,\n", "        \"suffix\": suffix,\n", "        \"selected_code\": selected_code,\n", "        \"message\": message,\n", "        \"full_file_content\": file[\"content\"],\n", "        \"start_line\": start_line,\n", "        \"end_line\": start_line + num_lines\n", "    }\n", "\n", "def approximate_search(string, substring):\n", "    def remove_spaces(s):\n", "        lines = s.splitlines()\n", "        lines = [line.strip() for line in lines]\n", "        return \"\\n\".join(lines)\n", "    string_aligned = remove_spaces(string)\n", "    substring_aligned = remove_spaces(substring)\n", "    \n", "    start_index = string_aligned.find(substring_aligned)\n", "    if start_index == -1:\n", "        return -1, -1\n", "    first_line = string_aligned[:start_index].count(\"\\n\")\n", "    num_lines = substring.count(\"\\n\") + 1\n", "    \n", "    return first_line, num_lines\n", "    \n", "\n", "def augment_boundaries(full_file_content, start_line, end_line, increase_only=False):\n", "    if end_line - start_line > 15:\n", "        if increase_only:\n", "            new_start_line = start_line + random.randint(-5, 0)\n", "            new_end_line = end_line + random.randint(0, 5)\n", "        else:\n", "            new_start_line = start_line + random.randint(-5, 5)\n", "            new_end_line = end_line + random.randint(-5, 5)\n", "    elif end_line - start_line > 10:\n", "        if increase_only:\n", "            new_start_line = start_line + random.randint(-3, 0)\n", "            new_end_line = end_line + random.randint(0, 3)\n", "        else:\n", "            new_start_line = start_line + random.randint(-3, 3)\n", "            new_end_line = end_line + random.randint(-3, 3)\n", "    elif end_line - start_line > 1:\n", "        if increase_only:\n", "            new_start_line = start_line + random.randint(-2, 0)\n", "            new_end_line = end_line + random.randint(0, 2)\n", "        else:\n", "            new_start_line = start_line + random.randint(-2, 0)\n", "            new_end_line = end_line + random.randint(0, 2)\n", "    else:\n", "        new_start_line = start_line\n", "        new_end_line = end_line\n", "        \n", "    new_selected_code = \"\".join(full_file_content.splitlines(keepends=True)[new_start_line : new_end_line])\n", "    new_prefix = \"\".join(full_file_content.splitlines(keepends=True)[:new_start_line])\n", "    new_suffix = \"\".join(full_file_content.splitlines(keepends=True)[new_end_line:])\n", "    \n", "    return new_selected_code, new_prefix, new_suffix\n", "\n", "\n", "def generate_sample(inputs):\n", "    gemini_model, repo, meta_conversation, persona, extra_info = inputs\n", "    \n", "    sample = {}\n", "    \n", "    try:\n", "        prompt = get_prompt(repo, meta_conversation, persona)\n", "        response = gemini_model.generate_response(prompt)\n", "        assert \"n_output_tokens\" in response, f\"Missing n_output_tokens: {response}\"\n", "        assert response[\"n_output_tokens\"] < gemini_model.max_output_tokens, \"Too many output tokens\"\n", "        \n", "        if response[\"text\"] is None:\n", "            raise AssertionError(f\"Generated text is None. Status: {response['status']}\")\n", "        \n", "        generated_conversation = parse_generated_conversation(response[\"text\"], len(meta_conversation[\"meta_conversation_turns\"]))\n", "        \n", "        if meta_conversation[\"is_selected_code\"]:        \n", "            parsed_message = parse_message_with_selected_code(generated_conversation[-1][\"user\"], repo)\n", "            augmented_selected_code, _, _ = augment_boundaries(\n", "                parsed_message[\"full_file_content\"],\n", "                parsed_message[\"start_line\"],\n", "                parsed_message[\"end_line\"]\n", "            )\n", "            assert len(augmented_selected_code.strip()) > 0, \"Empty selected code\"\n", "            \n", "            prompt_with_augmented_code = get_prompt(repo, meta_conversation, persona, augmented_selected_code)\n", "            response_with_augmented_code = gemini_model.generate_response(prompt_with_augmented_code)\n", "            assert \"n_output_tokens\" in response_with_augmented_code, f\"Missing n_output_tokens: {response_with_augmented_code}\"\n", "            assert response_with_augmented_code[\"n_output_tokens\"] < gemini_model.max_output_tokens, \"Too many output tokens\"\n", "            \n", "\n", "            generated_conversation_with_augmented_code = parse_generated_conversation(\n", "                response_with_augmented_code[\"text\"],\n", "                len(meta_conversation[\"meta_conversation_turns\"])\n", "            )\n", "            parsed_message = parse_message_with_selected_code(\n", "                generated_conversation_with_augmented_code[-1][\"user\"],\n", "                repo\n", "            )\n", "            sample = {\n", "                \"history\": generated_conversation_with_augmented_code[:-1],\n", "                \"message\": parsed_message[\"message\"],\n", "                \"response\": generated_conversation_with_augmented_code[-1][\"assistant\"],\n", "                \"selected_code\": parsed_message[\"selected_code\"],\n", "                \"path\": parsed_message[\"path\"],\n", "                \"prefix\": parsed_message[\"prefix\"],\n", "                \"suffix\": parsed_message[\"suffix\"],\n", "                \"extra\": {\n", "                    \"n_prompt_tokens\": response_with_augmented_code[\"n_prompt_tokens\"],\n", "                    \"n_output_tokens\": response_with_augmented_code[\"n_output_tokens\"],\n", "                    \"cost\": response[\"cost\"],\n", "                    \"prompt_cost\": response[\"prompt_cost\"],\n", "                    \"output_cost\": response[\"output_cost\"],\n", "                    **extra_info\n", "                }\n", "            }\n", "        else:\n", "            sample = {\n", "                \"history\": generated_conversation[:-1],\n", "                \"message\": generated_conversation[-1][\"user\"],\n", "                \"response\": generated_conversation[-1][\"assistant\"],\n", "                \"extra\": {\n", "                    \"n_prompt_tokens\": response[\"n_prompt_tokens\"],\n", "                    \"n_output_tokens\": response[\"n_output_tokens\"],\n", "                    \"cost\": response[\"cost\"],\n", "                    \"prompt_cost\": response[\"prompt_cost\"],\n", "                    \"output_cost\": response[\"output_cost\"],\n", "                    **extra_info\n", "                }\n", "            }\n", "    except AssertionError as e:\n", "        print(f\"Assertion Error: {e}\")\n", "        sample[\"error\"] = str(e)\n", "    except Exception as e:\n", "        print(f\"General Exception: {e}\")\n", "        sample[\"error\"] = str(e)\n", "    \n", "    return sample\n", "        \n", "\n", "def generate_sample_simple(inputs):\n", "    gemini_model, repo, meta_conversation, persona, extra_info = inputs\n", "    \n", "    sample = {}\n", "    \n", "    try:\n", "        prompt = get_prompt(repo, meta_conversation, persona)\n", "        response = gemini_model.generate_response(prompt)\n", "        assert \"n_output_tokens\" in response, f\"Missing n_output_tokens: {response}\"\n", "        assert response[\"n_output_tokens\"] < gemini_model.max_output_tokens, \"Too many output tokens\"\n", "        \n", "        if response[\"text\"] is None:\n", "            raise AssertionError(f\"Generated text is None. Status: {response['status']}\")\n", "        \n", "        generated_conversation = parse_generated_conversation(response[\"text\"], len(meta_conversation[\"meta_conversation_turns\"]))\n", "        \n", "        if meta_conversation[\"is_selected_code\"]:        \n", "            parsed_message = parse_message_with_selected_code(generated_conversation[-1][\"user\"], repo)\n", "            augmented_selected_code, augmented_prefix, augmented_suffix = augment_boundaries(\n", "                parsed_message[\"full_file_content\"],\n", "                parsed_message[\"start_line\"],\n", "                parsed_message[\"end_line\"],\n", "                increase_only=True\n", "            )\n", "            sample = {\n", "                \"history\": generated_conversation[:-1],\n", "                \"message\": parsed_message[\"message\"],\n", "                \"response\": generated_conversation[-1][\"assistant\"],\n", "                \"selected_code\": augmented_selected_code,\n", "                \"path\": parsed_message[\"path\"],\n", "                \"prefix\": augmented_prefix,\n", "                \"suffix\": augmented_suffix,\n", "                \"extra\": {\n", "                    \"n_prompt_tokens\": response[\"n_prompt_tokens\"],\n", "                    \"n_output_tokens\": response[\"n_output_tokens\"],\n", "                    \"cost\": response[\"cost\"],\n", "                    \"prompt_cost\": response[\"prompt_cost\"],\n", "                    \"output_cost\": response[\"output_cost\"],\n", "                    **extra_info\n", "                }\n", "            }\n", "        else:\n", "            sample = {\n", "                \"history\": generated_conversation[:-1],\n", "                \"message\": generated_conversation[-1][\"user\"],\n", "                \"response\": generated_conversation[-1][\"assistant\"],\n", "                \"extra\": {\n", "                    \"n_prompt_tokens\": response[\"n_prompt_tokens\"],\n", "                    \"n_output_tokens\": response[\"n_output_tokens\"],\n", "                    \"cost\": response[\"cost\"],\n", "                    \"prompt_cost\": response[\"prompt_cost\"],\n", "                    \"output_cost\": response[\"output_cost\"],\n", "                    **extra_info\n", "                }\n", "            }\n", "    except AssertionError as e:\n", "        print(f\"Assertion Error: {e}\")\n", "        sample[\"error\"] = str(e)\n", "    except Exception as e:\n", "        print(f\"General Exception: {e}\")\n", "        sample[\"error\"] = str(e)\n", "    \n", "    return sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with META_CONVERSATIONS_PATH.open(\"r\") as f:\n", "    meta_conversations = json.load(f)\n", "    \n", "if META_CONVERSATIONS_PATH_2 is not None:\n", "    with META_CONVERSATIONS_PATH_2.open(\"r\") as f:\n", "        meta_conversations_2 = json.load(f)\n", "    meta_conversations = meta_conversations + meta_conversations_2\n", "print(len(meta_conversations))\n", "    \n", "with PERSONAS_PATH.open(\"r\") as f:\n", "    personas = json.load(f)\n", "    \n", "repo_dataset = ParquetRepoDataset(REPOS_PATTERN)\n", "\n", "repos = []\n", "for i, repo in tqdm(enumerate(repo_dataset)):\n", "    if not (20 < len(repo.get_files()) < 100):\n", "        continue\n", "    if not (200000 < len(repo.concat_repo_to_string()) < 400000):\n", "        continue\n", "    \n", "    repos.append(repo)\n", "    if i > 10000:\n", "        break\n", "len(repos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gemini_model = gemini_api.GeminiAPI(\n", "    \"system-services-dev\",\n", "    \"us-west1\",\n", "    gemini_api.GeminiModelName.GEMINI_PRO_MODEL_NAME,\n", "    # gemini_api.GeminiModelName.GEMINI_FLASH_MODEL_NAME,\n", "    0.,\n", "    4096,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# repos_sizes_concat = []\n", "# repo_sizes_files = []\n", "# for repo in tqdm(repos):\n", "#     repos_sizes_concat.append(len(repo.concat_repo_to_string()))\n", "#     repo_sizes_files.append(len(repo.get_files()))\n", "    \n", "# len(repo_sizes_files),\\\n", "#     len([s for s in repos_sizes_concat if 200000 < s < 400000]),\\\n", "#     len([s for s in repo_sizes_files if 20 < s < 100]),\\\n", "#     len([(c, f) for c, f in zip(repos_sizes_concat, repo_sizes_files) if ((200000 < c < 400000) and (20 < f < 100))])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["random.seed(44)\n", "repo_indices = random.sample(range(len(repos)), 1200)\n", "\n", "all_inputs = []\n", "\n", "for repo_i in repo_indices:\n", "    for _ in range(5):\n", "        cur_repo = repos[repo_i]\n", "        \n", "        meta_conversation_idx = random.randint(0, len(meta_conversations) - 1)\n", "        cur_meta_conversation = meta_conversations[meta_conversation_idx]\n", "        is_bad_meta = False\n", "        for turn in cur_meta_conversation[\"meta_conversation_turns\"]:\n", "            if \"user\" not in turn or \"assistant\" not in turn:\n", "                is_bad_meta = True\n", "                break\n", "        if is_bad_meta:\n", "            print(\"Bad meta\")\n", "            continue\n", "        \n", "        cur_persona = random.choice(personas)\n", "        extra_info = {\n", "            \"persona\": cur_persona,\n", "            \"meta_conversation_idx\": meta_conversation_idx,\n", "            \"repo_id\": cur_repo.id,\n", "        }\n", "        all_inputs.append((gemini_model, cur_repo, cur_meta_conversation, cur_persona, extra_info))\n", "len(all_inputs)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with Pool(20) as pool:\n", "    samples = list(tqdm(\n", "        pool.imap(generate_sample_simple, all_inputs),\n", "        total=len(all_inputs)\n", "    ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["costs = [s[\"extra\"][\"cost\"] if \"extra\" in s else -1 for s in samples]\n", "costs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Failed: {len([s for s in samples if 'error' in s])}\")\n", "print(f\"Good: {len([s for s in samples if 'error' not in s])}\")\n", "print(f\"Good with selected code: {len([s for s in samples if ('error' not in s and 'selected_code' in s)])}\")\n", "for i, sample in enumerate(samples):\n", "    if \"error\" in sample:\n", "        print(i)\n", "        print(sample[\"error\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_samples = []\n", "for sample, inputs in tqdm(zip(samples, all_inputs)):\n", "    if \"error\" in sample:\n", "        continue\n", "    good_samples.append(sample)\n", "\n", "print(len(good_samples))\n", "with open(OUTPUT_PATH, \"w\") as f:\n", "    json.dump(good_samples, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html = \"\"\n", "\n", "chain_index = 0\n", "for sample, inputs in tqdm(zip(samples[:100], all_inputs[:100])):\n", "    if \"error\" in sample:\n", "        html += f\"<h2>Chain {chain_index} failed</h2>\"\n", "        html += \"<hr>\"\n", "        chain_index += 1\n", "        continue\n", "    _, repo, meta_conversation, persona, _ = inputs\n", "    \n", "    html += f\"<h2>Chain {chain_index}</h2>\"\n", "    chain_index += 1\n", "    html += f\"<p>Is selected code: {'selected_code' in sample}</p>\"\n", "    html += f\"<p>Persona: {persona}</p>\"\n", "\n", "    meta_conversation_str = \"\"\n", "    for i, turn in enumerate(meta_conversation[\"meta_conversation_turns\"]):\n", "        meta_conversation_str += f\"\"\"\n", "    User ({i + 1}): {turn[\"user\"]}\n", "    Assistant ({i + 1}): {turn[\"assistant\"]}\n", "\n", "    # \"\"\"\n", "    html += markdown(meta_conversation_str)\n", "    \n", "    html += render_sample(sample)\n", "    html += \"<hr>\"\n", "    \n", "html = wrap_html(html)\n", "\n", "with open(\"./jul27_test_v1.html\", \"w\") as f:\n", "    f.write(html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
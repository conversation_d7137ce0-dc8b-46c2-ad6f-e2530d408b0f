{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/.local/lib/python3.11/site-packages/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from google.cloud import bigquery\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "from pathlib import Path\n", "\n", "import numpy as np\n", "import torch\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from tqdm import tqdm\n", "\n", "DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2\")\n", "OUTPUT_PATH = Path(str(DATA_PATH) + \"_mix_w_turing_data_v1\")\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "\n", "def get_mmap_samples(path: Path):\n", "    dataset = MMapIndexedDataset(str(path))\n", "    all_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        all_samples.append(dataset[i].tolist())\n", "    return all_samples\n", "\n", "\n", "def save_dataset(samples, output_path):\n", "    random.shuffle(samples)\n", "    if not output_path.parent.exists():\n", "        output_path.parent.mkdir()\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sample in tqdm(samples):\n", "        builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "# DATASET_NAME = \"staging_request_insight_full_export_dataset\"\n", "DATASET_NAME = \"prod_request_insight_full_export_dataset\"\n", "# TENANT_NAME = \"dogfood\"\n", "# TENANT_NAME = \"aitutor-mercor\"\n", "TENANT_NAME = \"aitutor-turing\"\n", "\n", "START_TIME = \"2024-06-01\"\n", "\n", "def get_all_samples():\n", "    query = f\"\"\"\n", "SELECT\n", "  REQUEST.request_id AS request_id_request,\n", "  REQUEST.raw_json as raw_json_request,\n", "\n", "  RESPONSE.request_id AS request_id_response,\n", "  RESPONSE.raw_json as raw_json_response,\n", "\n", "  META.request_id AS request_id_meta,\n", "  META.raw_json as raw_json_meta\n", "FROM\n", "  `{PROJECT_ID}.{DATASET_NAME}.request_event` AS REQUEST\n", "JOIN\n", "  `{PROJECT_ID}.{DATASET_NAME}.request_event` AS RESPONSE ON REQUEST.request_id = RESPONSE.request_id\n", "JOIN\n", "  `{PROJECT_ID}.{DATASET_NAME}.request_event` AS META ON REQUEST.request_id = META.request_id\n", "WHERE\n", "  REQUEST.event_type = 'chat_host_request'\n", "  AND RESPONSE.event_type = 'chat_host_response'\n", "  AND META.event_type = 'request_metadata'\n", "  AND REQUEST.tenant = '{TENANT_NAME}'\n", "  AND REQUEST.time > TIMESTAMP(\"{START_TIME}\")\n", "  AND NOT JSON_EXTRACT_SCALAR(META.raw_json, '$.user_id') IN ('intern-chat-proto', 'health-check-1')\n", "LIMIT\n", "  10000;\n", "\"\"\"\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    rows = [*client.query_and_wait(query)]\n", "\n", "    return rows\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_samples = get_all_samples()\n", "print(len(all_samples))\n", "for sample in tqdm(all_samples):\n", "    assert sample.request_id_request == sample.request_id_response == sample.request_id_meta"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"./turing-internal-dump-jun24.jsonl\", \"w\") as f:\n", "    for sample in all_samples:\n", "        f.write(json.dumps(dict(sample)))\n", "        f.write(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_samples = list(\n", "    filter(\n", "        lambda s: \"selected_code\" not in s.raw_json_request[\"request\"],\n", "        all_samples,\n", "    )\n", ")\n", "\n", "len(all_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message_set = set()\n", "deduplicated_samples = []\n", "for sample in tqdm(all_samples):\n", "    cur_message = sample.raw_json_request[\"request\"][\"message\"]\n", "    if cur_message not in message_set:\n", "        message_set.add(cur_message)\n", "        deduplicated_samples.append(sample)\n", "\n", "len(deduplicated_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_mmap_samples = get_mmap_samples(DATA_PATH / \"train\")\n", "valid_mmap_samples = get_mmap_samples(DATA_PATH / \"valid\")\n", "\n", "seq_length = len(train_mmap_samples[0]) - 1\n", "train_fraction = len(train_mmap_samples) / (len(train_mmap_samples) + len(valid_mmap_samples))\n", "\n", "seq_length, train_fraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_prompts = []\n", "for sample in all_samples:\n", "    cur_prompt = [-1 * t for t in sample.raw_json_request[\"tokenization\"][\"token_ids\"]] + sample.raw_json_response[\"tokenization\"][\"token_ids\"]\n", "    cur_prompt += [TOKENIZER.special_tokens.eos]\n", "    cur_prompt += [-1 * TOKENIZER.special_tokens.eos] * (seq_length - len(cur_prompt) + 1)\n", "\n", "    tokenized_prompts.append(cur_prompt)\n", "\n", "random.shuffle(tokenized_prompts)\n", "len(tokenized_prompts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_real_samples = tokenized_prompts[: int(len(tokenized_prompts) * train_fraction)]\n", "valid_real_samples = tokenized_prompts[int(len(tokenized_prompts) * train_fraction) :]\n", "\n", "len(train_real_samples), len(valid_real_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_final = train_mmap_samples + train_real_samples\n", "valid_final = valid_mmap_samples + valid_real_samples\n", "\n", "random.shuffle(train_final)\n", "random.shuffle(valid_final)\n", "\n", "len(train_final), len(valid_final)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTPUT_PATH.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_dataset(train_final, OUTPUT_PATH / \"train\")\n", "save_dataset(valid_final, OUTPUT_PATH / \"valid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds1 = get_mmap_samples(Path(\"/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2_mix_w_turing_data_v1/train\"))\n", "# ds2 = get_mmap_samples(Path(\"/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2_just_header_mix_w_real_data_v2/train\"))\n", "# ds3 = get_mmap_samples(Path(\"/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2_forced_comment_mix_w_real_data_v2/train\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(list(map(abs, ds1[2]))))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
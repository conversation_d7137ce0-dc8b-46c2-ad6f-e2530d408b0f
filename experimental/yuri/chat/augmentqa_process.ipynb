{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import io\n", "import zstandard as zstd\n", "import json\n", "\n", "def read_logs(path):\n", "    dctx = zstd.ZstdDecompressor()\n", "\n", "    samples = []\n", "    with open(path, 'rb') as compressed:\n", "        with dctx.stream_reader(compressed) as reader:\n", "            text_stream = io.TextIOWrapper(reader, encoding='utf-8')\n", "            for line in text_stream:\n", "                data = json.loads(line)\n", "                samples.append(data)\n", "    \n", "    return samples\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXP1_PATH = \"/mnt/efs/augment/eval/jobs/NijiyRP3/000__ChatRAGSystem_xara/samples.jsonl.zst\"\n", "EXP2_PATH = \"/mnt/efs/augment/eval/jobs/AA8xzEKY/000__ChatRAGSystem_g6za/samples.jsonl.zst\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exp1_data = read_logs(EXP1_PATH)\n", "exp2_data = read_logs(EXP2_PATH)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total = \"\"\n", "for s1, s2 in zip(exp1_data, exp2_data):\n", "    total += str(s1[\"answer_keyword_recall\"]) + \"\\t\" + str(s2[\"answer_keyword_recall\"]) + \"\"\"\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(total)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"./t\", \"w\") as f:\n", "    f.write(total) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
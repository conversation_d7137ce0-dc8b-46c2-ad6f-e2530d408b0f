{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] += \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "\n", "\n", "import json\n", "import hashlib\n", "\n", "from dataclasses import dataclass\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk\n", "from pathlib import Path\n", "from research.retrieval.utils import convert_repository_to_documents\n", "from dataclasses import asdict\n", "from research.core.types import Document\n", "from research.core import utils_for_file\n", "from research.eval.harness.systems.abs_system import AbstractSystem\n", "from research.core import utils_for_str\n", "from research.eval.harness.systems.abs_system import get_system\n", "from research.eval.harness.systems.basic_RAG_system import RAGSystem # just so that it registered\n", "from jinja2 import Environment, Template\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.tasks.chat_eval_task import ResearchChatPromptInput, string_hash, ChatEvalTask\n", "from datetime import datetime\n", "\n", "\n", "TEMPLATE_PATH = Path(\"/home/<USER>/repos/augment/experimental/yuri/chat/binks_eval_template.txt\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repos = {\n", "    \"augment_apr_10_2024\": Path(\"/home/<USER>/tmp/prepare_data/augment\"),\n", "}\n", "\n", "augment_apr_10_2024_samples = [\n", "    (\"Where does filtering retrieved chunks vs prefix/suffix happen in services?\", \n", "\"\"\"services/completion_host/single_model_server/overlap.py\n", "services/completion_host/single_model_server/single_round_handler.py, in Completion._retrieve\"\"\"),\n", "\n", "    (\"What retrieval chunks are filtered out by prefix/suffix filtering in services?\",\n", "\"\"\"Chunks that are fully contained within prefix/suffix range\"\"\"),\n", "\n", "    (\"Where does the actual search over document embeddings happen for dense retrieval?\",\n", "\"\"\"services/embeddings_search_host/cpu_server/src/index_search.rs\n", "research/retrieval/retrieval_database.py\"\"\"),\n", "\n", "    (\"Is loading the document embeddings in the searcher sequential with computing scores?\",\n", "\"\"\"Loading is overlapped with computing scores to save time\"\"\"),\n", "\n", "    (\"Where is the Rogue data pipeline located?\",\n", "\"\"\"augment/research/data/rag/rogue.py\n", "augment/experimental/michiel/notebooks/rogue\"\"\"),\n", "\n", "    (\"In the Rogue data pipeline, where does the retrieval happen?\",\n", "\"\"\"In process repo, after calling _file_to_samples\"\"\"),\n", "\n", "    (\"In the Rogue data pipeline, how do I change what languages we retrieve during training\",\n", "\"\"\"change config.retrieval_languages\"\"\"),\n", "\n", "    (\"Where in the repo do we implement non-neural speculative decoding model?\",\n", "\"\"\"LongestOverlapLM, NullModel (dummy implementation)\"\"\"),\n", "\n", "    (\"Where in context manager service do we delete old blobs?\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"What does VSCode extension sends to the chat backend as context?\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"Which batch size do we use for ethanol retrieval during evaluation?\",\n", "\"\"\"\"\"\")\n", "]\n", "\n", "general_questions_samples = [\n", "    (\"How do you create a new dictionary from two python dictionaries?\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"How can you use an unpickleable object in a Pyspark udf?\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"How do you run a specific test in pytest\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"How can you call arguments by name in typescript?\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"\"\"I get this error when running a bazel command. What is the most likely problem?\n", "Server terminated abruptly (error code: 14, error message: 'Socket closed')\"\"\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"Write an rsync to copy a directory, except for any 1st or 2nd level subdirectory that ends in _test\",\n", "\"\"\"rsync -av --exclude='/_test/' --exclude='//*_test/' /path/to/source/ /path/to/destination/\"\"\"),\n", "\n", "    (\"Write my Python function that parses this string into data format: 2024-03-08 18:56:09.595356 UTC\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"I have data in JSONL format. How can I sort it by one of the keys with jq in bash\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"I have a column that contains JSON, specifically, a list of objects. I want to return this column, but only keep some fields in each list element. How can I do that?\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"\"\"I'm working on VSCode extension. I want to paste some code at the cursor position, but I want VSCode to do that in a smart way -- perhaps, adjust indentations and format. I know that VSCode has API that covers this use case. How can I use this API?\"\"\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"\"\"Implement a Future class that mimics the Promises API for handling the result of an asynchronous operation. The Future should have:\n", "\n", "- A constructor that takes an executor function with resolve and reject functions\n", "- A then() method that takes onFulfilled and onRejected callback functions\n", "- A catch() method to handle rejection\n", "- The ability to chain multiple thens and catches\n", "\n", "The Future should handle both synchronous and asynchronous operations. Demonstrate the usage of the Future class by:\n", "\n", "- Creating a Future with a synchronous executor that resolves or rejects randomly\n", "- Creating a Future with an asynchronous executor that resolves or rejects after 1 second\n", "- Chaining multiple thens and a catch to handle the results of the Futures\"\"\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"\"\"Do Python dataclasses automatically have hash function implemented?\"\"\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"\"\"I want to inherit multiprocessing.Pool class in Python to provide additional caching. Essentially, before sending a input in a queue I want to first check whether we have results in cache. and if yes, then return the result. Note that cache access should happen in a single process since I'm going to use file-based cache and it's not thread-safe.\"\"\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"How to do COUNT DISTINCT in SQL for two columns?\",\n", "\"\"\"\"\"\"),\n", "\n", "    (\"In Python I have nested dataclasses structures. I want to parse a json string into this dataclass object, but when I do then internal dataclasses remain just dictionary. what shall I do?\",\n", "\"\"\"\"\"\"),\n", "\n", "]\n", "\n", "all_data = {\n", "    # \"augment_apr_10_2024\": augment_apr_10_2024_samples[:2] + general_questions_samples[:2]\n", "    \"augment_apr_10_2024\": augment_apr_10_2024_samples + general_questions_samples\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUT_DIR = Path(\"/home/<USER>/tmp/chat_basic_eval26_apr15_combined6\")\n", "example_output = OUT_DIR / \"examples.json\"\n", "repos_dir = OUT_DIR / \"repos\"\n", "\n", "OUT_DIR.mkdir(exist_ok=True)\n", "repos_dir.mkdir(exist_ok=True)\n", "\n", "for repo_name, repo_path in repos.items():\n", "    docs = convert_repository_to_documents(str(repo_path))\n", "\n", "    docs_as_dict = {\n", "        \"repo_url\": repo_name,\n", "        \"docs\": [asdict(d) for d in docs] # [:100] # ONLY FOR TESTING!!!\n", "    }\n", "\n", "    repo_path = repos_dir / f\"{repo_name}.json\"\n", "    with repo_path.open(\"w\") as f:\n", "        json.dump(docs_as_dict, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_samples = []\n", "\n", "for repo_name, samples in all_data.items():\n", "    for sample in samples:\n", "        q, a = sample\n", "        chat_sample = ResearchChatPromptInput(\n", "            q, a, {\"repo_url\": repo_name}\n", "        )\n", "\n", "        combined_samples.append(asdict(chat_sample))\n", "\n", "assert not example_output.exists()\n", "with example_output.open(\"w\") as f:\n", "    json.dump(combined_samples, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CONFIG = {\n", "    \"name\": \"basic_rag\",\n", "    \"model\": {\n", "        \"name\": \"fastbackward_deepseek_instruct\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/yury/binks/binks_v1_48853\",\n", "        \"model_parallel_size\": 4,\n", "        \"seq_length\": 16384,\n", "        \"override_prompt_formatter\": {\n", "            \"name\": \"chat_template\",\n", "            \"template_file\": \"/home/<USER>/repos/augment/research/model_server/prompts/chat/deepseek_instruct_with_retrieval_template.txt\",\n", "            \"tokenizer_name\": \"deepseekcoderinstructtokenizer\",\n", "            \"lines_in_prefix_suffix\": 0,\n", "            \"lines_in_prefix_suffix_empty_selection\": 75,\n", "            \"last_n_turns_in_chat_history\": 10\n", "        }\n", "    },\n", "    \"generation_options\": {\n", "        \"temperature\": 0,\n", "        \"top_k\": 0,\n", "        \"top_p\": 0,\n", "        \"max_generated_tokens\": 1024\n", "    },\n", "    \"retriever\": {\n", "        \"scorer\": {\n", "            \"name\": \"ethanol\",\n", "            \"checkpoint_path\": \"ethanol/ethanol6-16.1\"\n", "        },\n", "        \"chunker\": {\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 30\n", "        },\n", "        \"query_formatter\": {\n", "            \"name\": \"ethanol6_query_simple_chat\",\n", "            \"max_tokens\": 1023,\n", "            \"add_path\": True,\n", "            \"verbose\": True\n", "        },\n", "        \"document_formatter\": {\n", "            \"name\": \"ethanol6_document\",\n", "            \"max_tokens\": 999,\n", "            \"add_path\": True\n", "        }\n", "    },\n", "    \"experimental\": {\n", "        \"remove_suffix\": False,\n", "        \"retriever_top_k\": 25,\n", "        \"trim_on_dedent\": <PERSON>alse,\n", "        \"trim_on_max_lines\": None\n", "    },\n", "    \"verbose\": True\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system = get_system(CONFIG[\"name\"], **CONFIG)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task = ChatEvalTask(dataset_path=str(OUT_DIR), html_report_template_path=str(TEMPLATE_PATH))\n", "\n", "result = task.run(system, OUT_DIR)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "# result_dc = copy.deepcopy(result)\n", "result = copy.deepcopy(result_dc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import markdown\n", "\n", "for _s in result[\"_results\"]:\n", "    _s.generated_text = markdown.markdown(_s.generated_text, extensions=['fenced_code'])\n", "\n", "for _s in task.examples:\n", "    _s.expected_reply = markdown.markdown(_s.expected_reply, extensions=['fenced_code'])\n", "\n", "\n", "template_txt = Path(\"/home/<USER>/repos/augment/experimental/yuri/chat/binks_eval_template.txt\").read_text()\n", "env = Environment(keep_trailing_newline=True)\n", "env.globals.update(zip=zip)\n", "template = env.from_string(template_txt)\n", "\n", "header = f\"Chat evaluation -- {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\"\n", "rendered_html = template.render(samples=task.examples, results=result[\"_results\"], eval_header=header)\n", "\n", "\n", "\n", "with open(\"./v27.html\", \"w\") as f:\n", "    f.write(rendered_html)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task.examples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["asdict(result[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["asdict(result[0]).keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result[0].retrieved_chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result[0].retrieved_chunks[0].text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code = \"\"\"\n", "def foo():\n", "    print(\"foo\")\n", "    print(\"bar\")\n", "    return 1\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/repos/augment/experimental/yuri/chat/binks_eval_template.txt\", \"r\") as f:\n", "    template_txt = f.read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["env = Environment(keep_trailing_newline=True)\n", "env.globals.update(zip=zip)\n", "template = env.from_string(template_txt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rendered_html = template.render(\n", "    samples=, results=results, eval_header=header\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
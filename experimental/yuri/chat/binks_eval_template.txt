<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Research Chat Prompts</title>
<style>
  body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f4f4; }
  .container { padding: 20px; }
  .card { background-color: #fff; margin-bottom: 1rem; padding: 10px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); }
  .toggle-button, .global-toggle { background: #007bff; color: white; padding: 8px 16px; margin: 10px 0; cursor: pointer; border: none; border-radius: 5px; display: inline-block; }
  .toggle-content, .chunk-text { display: none; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }
  .extra-info { padding: 10px; background-color: #eee; margin-top: 5px; border-radius: 5px; }
  .chunk-info { background-color: #f0f0f0; margin: 5px 0; padding: 5px; border-radius: 5px; }
  .chunk-toggle { background: none; color: #007bff; cursor: pointer; border: none; display: block; padding: 5px; text-align: left; width: 100%; }
  .inline-block { display: inline-block; vertical-align: top; /* Keeps elements aligned at the top */}
  .markdown-content {
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
    /* Add additional styling as needed */
  }
</style>
</head>
<body>
<div class="container">
  <h2>{{ eval_header }}</h2>
  <button class="global-toggle">Toggle All Samples</button>

  {% for sample, result in zip(samples, results) %}
    <div class="card">
      <div class="toggle-button">Sample #{{ loop.index }}</div>
      <div class="toggle-content">

        <div class="extra-info">
          <strong>Question:</strong> {{ sample.question }}
        </div>
        <div class="extra-info">
          <strong>Expected Reply:</strong>
          <div class="markdown-content" style="border: 1px solid #ccc; padding: 10px; border-radius: 5px;">
            {{ sample.expected_reply | safe }}
          </div>
        </div>
        <div class="extra-info">
          <strong>Generated Text:</strong>
          <div class="markdown-content" style="border: 1px solid #ccc; padding: 10px; border-radius: 5px;">
            {{ result.generated_text | safe }}
          </div>
        </div>

        <div class="extra-info">
          <strong>Retrieved chunks:</strong>
          {% for chunk in result.retrieved_chunks %}
            <div class="chunk-info">
              <button class="chunk-toggle">Path: {{ chunk.path }}</button>
              <div class="chunk-text">
                <strong>Text:</strong>
                <pre>{{ chunk.text | escape }}</pre>
              </div>
            </div>
          {% endfor %}
        </div>

      </div>
    </div>
  {% endfor %}

</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
  document.querySelectorAll('.toggle-button, .chunk-toggle').forEach(button => {
    button.addEventListener('click', function() {
      const content = this.nextElementSibling;
      content.style.display = content.style.display === "none" ? "block" : "none";
    });
  });

  const globalToggleButton = document.querySelector('.global-toggle');
  globalToggleButton.addEventListener('click', function() {
    const allContents = document.querySelectorAll('.toggle-content');
    const isAnyVisible = Array.from(allContents).some(content => content.style.display === "block");
    allContents.forEach(content => {
      content.style.display = isAnyVisible ? "none" : "block";
    });
  });
});
</script>
</body>
</html>

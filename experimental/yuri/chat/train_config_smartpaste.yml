determined:
  name: "llama3-jul27-smartpaste-new-prompt-simple-7k-v2"
  description: null
  workspace: Dev
  project: yuri

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32
  project_group: "finetuning"

fastbackward_configs:
 - configs/llama3_70b.py

fastbackward_args:
  block_size: 8192
  learning_rate: 5e-6
  min_lr: 1e-6
  decay_lr: True
  max_iters: 0
  max_epochs: 1
  eval_interval: 10000
  weight_decay: 0.1
  warmup_iters: 0
  train_data_path: /mnt/efs/augment/user/yuri/data/gemini_data_jul22/smartpaste_new_prompt_7k_simple_jul27_v1/sft_data/train
  eval_data_path: /mnt/efs/augment/user/yuri/data/gemini_data_jul22/smartpaste_new_prompt_7k_simple_jul27_v1/sft_data/valid
  model_vocab_size: 128256
  batch_size: 2
  gradient_accumulation_steps: 1
  checkpoint_optimizer_state: True
  wandb_project: llama3-ft
  run_name: llama3-jul27-smartpaste-new-prompt-simple-7k-v2
  checkpoint_dir: /mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct-fb

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENT_DIR = \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/_t\"\n", "\n", "SEQUENCE_LENGTH = 8192 # Used for training data preparation\n", "\n", "# These come from `run_llama_tokenized_data.ipynb`\n", "INPUT_SAMPLES_PATHS = [\n", "    # \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul18_5k_pro_new_prompt/llama_inference_results.json\",\n", "    # \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul21_2k_pro_simple_boundaries_new_prompt/llama_inference_results.json\",\n", "    # \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/jul27_5k_pro_simple_boundaries_new_prompt/llama_inference_results.json\",\n", "    # \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/binks_v4_subset/llama_inference_results.json\",\n", "    \"/mnt/efs/augment/user/yuri/data/gemini_data_jul22/binks_v4_subset_v2/llama_inference_results.json\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", "\n", "with open(\"/home/<USER>/.openai\", \"r\") as f:\n", "    os.environ[\"OPENAI_API_KEY\"] = f.read().strip()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import json\n", "import requests\n", "import random\n", "import torch\n", "import numpy as np\n", "import hashlib\n", "\n", "\n", "from pathlib import Path\n", "from experimental.yuri.preferences.utils import markdown, details_html, wrap_html, TritonClient, row_html\n", "from typing import Any\n", "from tqdm import tqdm\n", "from multiprocessing import Pool\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from base.tokenizers import create_tokenizer_by_name\n", "from functools import partial\n", "from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from collections import defaultdict, Counter\n", "from google.cloud import bigquery\n", "\n", "\n", "EXPERIMENT_DIR = Path(EXPERIMENT_DIR)\n", "EXPERIMENT_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# General\n", "def filter_sequences(seq1, seq2, fn):\n", "    seq1_new, seq2_new = zip(*filter(fn, zip(seq1, seq2)))\n", "    return seq1_new, seq2_new\n", "\n", "def run_or_load_results(fn, ckpt_name):\n", "    if (EXPERIMENT_DIR / ckpt_name).exists():\n", "        with open(EXPERIMENT_DIR / ckpt_name, \"r\") as f:\n", "            return json.load(f)\n", "    else:\n", "        results = fn()\n", "        with open(EXPERIMENT_DIR / ckpt_name, \"w\") as f:\n", "            json.dump(results, f)\n", "        return results\n", "\n", "def parse_gpt_response(\n", "    response_text, regexp, category2values: dict[Any, list]\n", "):\n", "    match = re.search(regexp, response_text)\n", "    if not match:\n", "        return None\n", "    value = match.group(1).strip()\n", "    for category, values in category2values.items():\n", "        if value in values:\n", "            return category\n", "    return None\n", "\n", "def summarize_reward_results(reward_results):\n", "    num_none, num_1, num_0 = 0, 0, 0\n", "    for reward_result in reward_results:\n", "        if reward_result is None or reward_result[\"reward_value\"] is None:\n", "            num_none += 1\n", "        elif reward_result[\"reward_value\"] == 1:\n", "            num_1 += 1\n", "        else:\n", "            num_0 += 1\n", "            \n", "    num_total = len(reward_results)\n", "    \n", "    print(f\"Total: {num_total}\")\n", "    print(f\"None: {num_none / num_total:.2f}\")\n", "    print(f\"0: {num_0 / num_total:.2f}\")\n", "    print(f\"1: {num_1 / num_total:.2f}\")\n", "    print(f\"1 / (0 + 1): {num_1 / (num_0 + num_1):.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# HTML rendering\n", "\n", "def render_simple_message(message, response, selected_code=None, another_response=None, use_markdown=True):\n", "    message = markdown(message)\n", "    \n", "    if use_markdown:\n", "        response = markdown(response)\n", "        another_response = markdown(another_response) if another_response is not None else None\n", "    else:\n", "        response = response.replace(\"\\n\", \"<br>\")\n", "        another_response = another_response.replace(\"\\n\", \"<br>\") if another_response is not None else None\n", "\n", "    html_content = \"\"\n", "    if selected_code is not None:\n", "        selected_code = f\"```\\n{selected_code}\\n```\"\n", "        selected_code = markdown(selected_code)\n", "        html_content += f'<div class=\"code\" style=\"flex: 1;\">{selected_code}</div>'\n", "    html_content += f'<div class=\"code\">{message}</div>'\n", "    \n", "    if another_response is None:\n", "        html_content += f'<div class=\"code\">{response}</div>'\n", "    else:\n", "        side_by_side_html = f'<div class=\"code\">{response}</div>'\n", "        side_by_side_html += f'<div class=\"code\">{another_response}</div>'\n", "        html_content += row_html(side_by_side_html)\n", "\n", "    return html_content\n", "\n", "def render_sample(sample, another_response=None, use_markdown=True, response_field_name=\"llama_response\"):\n", "    history = sample.get(\"history\", [])\n", "    selected_code = sample.get(\"selected_code\", None)\n", "    message = sample[\"message\"]\n", "    response = sample[response_field_name]\n", "\n", "    cur_index = 0\n", "    html_content = \"\"\n", "    \n", "    for exchange in history:\n", "        html_content += f\"<h3>Message {cur_index}</h3>\"\n", "        cur_index += 1\n", "        html_content += render_simple_message(exchange[\"user\"], exchange[\"assistant\"])\n", "        html_content += \"<hr>\"\n", "    if len(history) > 0:\n", "        html_content = details_html(\"Chat history\", html_content)\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "    html_content += render_simple_message(message, response, selected_code, another_response, use_markdown)\n", "    \n", "    return html_content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare some general things\n", "\n", "def wrap_message(message):\n", "    return f\"~~~\\n{message}\\n~~~\\n\"\n", "\n", "def format_history(history):\n", "    result_text = \"\"\n", "    for exchange in history:\n", "        result_text += f\"User: {wrap_message(exchange['user'])}\\n\"\n", "        result_text += f\"Assistant: {wrap_message(exchange['assistant'])}\\n\\n\\n\"\n", "    return result_text\n", "\n", "def format_sample(sample):\n", "    chat_history = sample.get(\"history\", [])\n", "    message = sample[\"message\"]\n", "    path = sample.get(\"path\", None)\n", "    selected_code = sample.get(\"selected_code\", None)\n", "    prefix = sample.get(\"prefix\", \"\")\n", "    suffix = sample.get(\"suffix\", \"\")\n", "    \n", "    if len(chat_history) > 0:\n", "        result_text = (\n", "            \"Here is a history of conversation between user and AI coding assistant builtin into VSCode \" +\n", "            \"(each message is enclosed between ~~~):\\n \"\n", "            f\"{format_history(chat_history)}\\n\\n\" + \n", "            f\"Here is the last message (enclosed between ~~~) from user:\\n{wrap_message(message)}\"\n", "        )\n", "    else:\n", "        result_text = (\n", "            \"Here is a message (enclosed between ~~~) from user to AI coding assistant builtin into VSCode:\\n\" +\n", "            f\"{wrap_message(message)}\\n\"\n", "        )\n", "\n", "    if path is not None:\n", "        result_text += (\n", "            f\"User has file `{path}` open and has highlighted part of the code.\\n\" +\n", "            \"Here is the full file:\\n\"\n", "            f\"```\\n{prefix}[START HIGHLIGHTED REGION]\\n...\\n\" +\n", "            \"[selected code goes here]\\n...\\n[END HIGHLIGHTED REGION]\\n\" +\n", "            f\"{suffix}\\n```\\n\\n\"\n", "        )\n", "        result_text += (\n", "            f\"Here is the highlighted code:\\n\" +\n", "            f\"```\\n{selected_code}\\n```\"\n", "        )\n", "\n", "    return result_text\n", "\n", "\n", "def format_retrieval(sample):\n", "    result_text = \"Here are relevant code excerpts from repository:\\n\"\n", "    for chunk in sample[\"chunks\"]:\n", "        result_text += f\"\"\"Excerpt from file `{chunk[\"path\"]}`:\\n```\\n{chunk[\"text\"]}\\n```\\n\"\"\"\n", "    return result_text\n", "\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=0,\n", "    chat_history_len=2048,\n", "    prefix_len=1024,\n", "    selected_code_len=0,\n", "    suffix_len=1024,\n", "    max_prompt_len=6144,\n", "    retrieval_len=-1\n", ")\n", "PROMPT_FORMATTER = get_chat_prompt_formatter_by_name(\n", "    \"binks_llama3\",\n", "    TOKENIZER,\n", "    APPORTIONMENT,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load input prompt\n", "\n", "chat_samples = []\n", "for _input_path in INPUT_SAMPLES_PATHS:\n", "    with open(_input_path, \"r\") as f:\n", "        cur_samples = json.load(f)\n", "    print(f\"Loaded {len(cur_samples)} samples from {_input_path}\")\n", "    chat_samples += cur_samples\n", "    \n", "for sample in tqdm(chat_samples):\n", "    sample[\"prompt_text\"] = TOKENIZER.detokenize(sample[\"prompt_tokens\"])\n", "    if \"response\" in sample:  # Just to not confuse with llama response\n", "        sample[\"gemini_response\"] = sample[\"response\"]\n", "        del sample[\"response\"]\n", "    if \"chat_history\" in sample:\n", "        assert \"history\" not in sample or json.dumps(sample[\"history\"]) == json.dumps(sample[\"chat_history\"]), \"Both history and chat_history are present\"\n", "        sample[\"history\"] = sample[\"chat_history\"]\n", "        del sample[\"chat_history\"]\n", "\n", "all_repo_ids = set(map(lambda s: s[\"extra\"][\"repo_id\"], chat_samples))\n", "print(\"Number of repos:\", len(all_repo_ids))\n", "\n", "\n", "# TMP\n", "# chat_samples = chat_samples[:200]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load repositories (Takes ~3.5 minutes * 2)\n", "\n", "from experimental.yury.binks.datav3.parquet_repo_dataset import ParquetRepoDataset\n", "REPOS_PATTERN_LIST = [\n", "    Path(\"/mnt/efs/augment/user/yury/binks/binks-v3.1\") / \"03_raw_repos/*.zstd.parquet\",\n", "    Path(\"/mnt/efs/augment/user/yury/binks/binks-v4\") / \"01_raw_repos/*.zstd.parquet\"\n", "]\n", "\n", "repos = []\n", "for REPOS_PATTERN in REPOS_PATTERN_LIST:\n", "    repo_dataset = ParquetRepoDataset(REPOS_PATTERN)\n", "    for i, repo in tqdm(enumerate(repo_dataset)):\n", "        if repo.id not in all_repo_ids:\n", "            continue\n", "        repos.append(repo)\n", "print(f\"Loaded: {len(repos)} repos\")\n", "\n", "repo_id_to_repo = {repo.id: repo for repo in repos}\n", "\n", "def get_file_list(repo_id):\n", "    repo = repo_id_to_repo[repo_id]\n", "    return set(map(lambda x: x[\"max_stars_repo_path\"], repo.get_files()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Language labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BinaryClassifierLanguageLabels:\n", "    def __init__(self, response_field_name):\n", "        self.response_field_name = response_field_name\n", "    \n", "    def classify_single_sample(self, sample):\n", "        pattern = re.compile(r\"```([^\\s]*?)\\n(.*?)\\n```\", re.DOTALL)\n", "        matches = list(pattern.finditer(sample[self.response_field_name]))\n", "        \n", "        return {\n", "            \"verdict\": len(matches) > 0\n", "        }\n", "        \n", "    def classify_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            classification_results = list(tqdm(\n", "                pool.imap(self.classify_single_sample, samples),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return classification_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None, full_result_field_name=None):\n", "        assert full_result_field_name is not None\n", "        \n", "        samples = samples[:max_samples]\n", "\n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Verdict: {sample[full_result_field_name]['verdict']}</p>\"\n", "\n", "            cur_html += render_sample(sample, use_markdown=False, response_field_name=self.response_field_name)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "            \n", "            \n", "class RefinerLanguageLabels:\n", "    def __init__(self, prompt_template, response_field_name):\n", "        self.prompt_template = prompt_template\n", "        self.gpt = GptWrapper()\n", "        self.response_field_name = response_field_name\n", "        \n", "    def refine_single_sample(self, sample):\n", "        pattern = re.compile(r\"```([^\\s]*?)\\n(.*?)\\n```\", re.DOTALL)\n", "        matches = list(pattern.finditer(sample[self.response_field_name]))\n", "        \n", "        if len(matches) == 0:\n", "            assert False, \"Failed to parse\"\n", "            \n", "        num_code_blocks = len(matches)\n", "        rendered_code_blocks = \"\"\n", "        for i, match in enumerate(matches):\n", "            language, code = match.groups()\n", "            rendered_code_blocks += f\"Code block {i}:\\n```{language}\\n{code}\\n```\\n\"\n", "        \n", "        prompt = self.prompt_template.format(\n", "            formatted_sample=format_sample(sample),\n", "            formatted_response=wrap_message(sample[self.response_field_name]),\n", "            code_blocks_list=rendered_code_blocks,\n", "            num_code_blocks=num_code_blocks\n", "        )\n", "        gpt4_response = self.gpt([prompt], model=\"gpt-4-1106-preview\", temperature=0, use_json=True)\n", "        assert isinstance(gpt4_response, dict)\n", "        \n", "        detected_languages = gpt4_response[\"detected_languages\"]\n", "        assert len(detected_languages) == len(matches)\n", "        \n", "        shift = 0\n", "        cur_response = sample[self.response_field_name]\n", "        for i, (match, detected_language) in enumerate(zip(matches, detected_languages)):\n", "            cur_language = match.group(1)  # Indexing in group() starts from 1\n", "            if len(cur_language) > 0:\n", "                if cur_language != detected_language:\n", "                    print(f\"Warning: language mismatch: {cur_language} != {detected_language}\")\n", "                    # print(f\"Original response: {cur_response}\")\n", "                # continue\n", "            \n", "            match_start, _ = match.span()\n", "            match_start += shift\n", "            prefix = cur_response[:match_start + 3]  # 3 for ```\n", "            suffix = cur_response[match_start + 3 + len(cur_language):]\n", "            \n", "            cur_response = prefix + detected_language + suffix\n", "            shift += len(detected_language) - len(cur_language)\n", "            \n", "            # if len(cur_language) > 0 and cur_language != detected_language:\n", "            #     print(f\"Refined response: {cur_response}\")\n", "        \n", "        return {\n", "            \"prompt\": prompt,\n", "            \"full_gpt_response\": gpt4_response,\n", "            \"refined_response\": cur_response,\n", "        }\n", "        \n", "    def refine_single_sample_safe(self, sample):\n", "        try:\n", "            return self.refine_single_sample(sample)\n", "        except Exception as e:\n", "            print(e)\n", "            return None\n", "    \n", "    def refine_samples(self, samples):\n", "        with Pool(25) as pool:\n", "            refinement_results = list(tqdm(\n", "                pool.imap(self.refine_single_sample_safe, samples),\n", "                total=len(samples)\n", "            ))\n", "        print(self.gpt.get_stats())\n", "        return refinement_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None, full_result_field_name=None):\n", "        assert full_result_field_name is not None\n", "        \n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += details_html(\n", "                \"Refinement prompt\",\n", "                markdown(str(sample[full_result_field_name][\"prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[full_result_field_name][\"full_gpt_response\"]))\n", "            )\n", "            cur_html += render_sample(sample, sample[full_result_field_name][\"refined_response\"], False)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "    \n", "\n", "class RewardLanguageLabels:\n", "    def reward_single_sample(self, sample, response):\n", "        pattern = re.compile(r\"```([^\\s]*?)\\n(.*?)\\n```\", re.DOTALL)\n", "        matches = pattern.findall(response)\n", "        \n", "        if len(matches) == 0:\n", "            print(f\"Didn't find code in response: {response}\")\n", "            return None\n", "        \n", "        num_good_samples = 0\n", "        for match in matches:\n", "            language, _ = match\n", "            num_good_samples += int(len(language) > 0)\n", "            \n", "        return {\n", "            \"reward_value\": int(num_good_samples == len(matches)),\n", "            \"matches\": matches,\n", "        }\n", "        \n", "    def reward_samples(self, samples, responses):\n", "        reward_results = []\n", "        for sample, response in zip(samples, responses):\n", "            reward_results.append(self.reward_single_sample(sample, response))\n", "        return reward_results\n", "    \n", "    def generate_report(self, samples, responses, reward_results, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, (sample, response, reward_result) in tqdm(enumerate(zip(samples, responses, reward_results))):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Reward value: {reward_result['reward_value']}</p>\"\n", "            cur_html += render_sample(sample, another_response=response)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INPUT_RESPONSE_FIELD_NAME = \"file_paths_refinement_result\"\n", "OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME = \"language_labels_classification_verdict\"\n", "OUTPUT_CLASSIFICATION_FULL_FIELD_NAME = \"language_labels_classification_full_response\"\n", "\n", "classifier = BinaryClassifierLanguageLabels(INPUT_RESPONSE_FIELD_NAME)\n", "_classification_results = run_or_load_results(\n", "    partial(classifier.classify_samples, chat_samples),\n", "    \"language_labels_classification_results.json\"\n", ")\n", "\n", "for i, (sample, classification_result) in enumerate(zip(chat_samples, _classification_results)):\n", "    sample[OUTPUT_CLASSIFICATION_FULL_FIELD_NAME] = classification_result\n", "    sample[OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME] = classification_result[\"verdict\"]\n", "    \n", "positive_samples = [\n", "    sample for sample in chat_samples\n", "    if sample[OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME] \n", "]\n", "negative_samples_using_fn = [\n", "    sample for sample in chat_samples\n", "    if not sample[OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME] \n", "]\n", "negative_samples_using_gpt = []  # We don't user GPT for this\n", "\n", "print(f\"\"\"Number of positive samples: {len(positive_samples)}\n", "Number of negative samples using filter function: {len(negative_samples_using_fn)}\n", "Number of negative samples using GPT: {len(negative_samples_using_gpt)}\"\"\")\n", "\n", "_ = classifier.generate_report(positive_samples, file_name_to_save=\"language_labels_classification_report_positive_samples.html\", full_result_field_name=OUTPUT_CLASSIFICATION_FULL_FIELD_NAME)\n", "_ = classifier.generate_report(negative_samples_using_fn, file_name_to_save=\"language_labels_classification_report_negative_samples_using_fn.html\", full_result_field_name=OUTPUT_CLASSIFICATION_FULL_FIELD_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTPUT_REFINEMENT_RESULT_FIELD_NAME = \"language_labels_refinement_result\"\n", "OUTPUT_REFINEMENT_FULL_FIELD_NAME = \"language_labels_refinement_full_response\"\n", "\n", "LANGUAGE_DETECTION_PROMPT = \"\"\"{formatted_sample}\n", "\n", "Here is a response (enclosed between ~~~) to user's last message generated by AI coding assistant:\n", "{formatted_response}\n", "\n", "Here is a list of {num_code_blocks} code blocks detected in response:\n", "{code_blocks_list}\n", "\n", "Your task is to detect language for each code block.\n", "Every language should be written in the format that can be used in Markdown to enable syntax highlighting.\n", "If language already present in the codeblock (after first ```), then just return it exactly the same.\n", "For plain text, return `text`.\n", "\n", "Return JSON with a single key: `detected_languages` with a list of {num_code_blocks} elements.\n", "\"\"\"\n", "\n", "refiner_language_labels = RefinerLanguageLabels(LANGUAGE_DETECTION_PROMPT, INPUT_RESPONSE_FIELD_NAME)\n", "_refinement_results = run_or_load_results(\n", "    partial(refiner_language_labels.refine_samples, positive_samples),\n", "    \"language_labels_refinement_language_labels_results.json\"\n", ")\n", "\n", "for i, (sample, refinement_result) in enumerate(zip(positive_samples, _refinement_results)):\n", "    sample[OUTPUT_REFINEMENT_FULL_FIELD_NAME] = refinement_result\n", "    sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] = refinement_result[\"refined_response\"] if refinement_result is not None else None\n", "\n", "positive_samples_refined = [\n", "    sample for sample in positive_samples\n", "    if sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] is not None\n", "]\n", "print(f\"Successfully refined {len(positive_samples_refined)} out of {len(positive_samples)} positive samples.\")\n", "\n", "_ = refiner_language_labels.generate_report(\n", "    positive_samples_refined,\n", "    file_name_to_save=\"language_labels_refinement_report_positive_samples.html\",\n", "    full_result_field_name=OUTPUT_REFINEMENT_FULL_FIELD_NAME\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_chat_samples = positive_samples + negative_samples_using_fn + negative_samples_using_gpt\n", "\n", "print(f\"Total samples: {len(new_chat_samples)}\")\n", "num_default = 0\n", "\n", "for sample in new_chat_samples:\n", "    if len(sample.get(OUTPUT_REFINEMENT_RESULT_FIELD_NAME, \"\")) == 0:\n", "        sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] = sample[INPUT_RESPONSE_FIELD_NAME]\n", "        num_default += 1\n", "        \n", "print(f\"Number of samples with default response: {num_default}\")    \n", "chat_samples = new_chat_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(OUTPUT_REFINEMENT_RESULT_FIELD_NAME)  # This should be used in next step"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_language_labels = RewardLanguageLabels()\n", "\n", "#### For input responses\n", "reward_model_outputs = run_or_load_results(\n", "    partial(reward_language_labels.reward_samples, positive_samples_refined, [sample[INPUT_RESPONSE_FIELD_NAME] for sample in positive_samples_refined]),\n", "    \"language_labels_reward_results_input_responses.json\"\n", ")\n", "print(\"Reward model outputs for input responses:\")\n", "summarize_reward_results(reward_model_outputs)\n", "print(\"#\" * 20)\n", "\n", "#### For refined responses\n", "reward_model_outputs = run_or_load_results(\n", "    partial(reward_language_labels.reward_samples, positive_samples_refined, [sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] for sample in positive_samples_refined]),\n", "    \"language_labels_reward_results_refined_responses.json\"\n", ")\n", "print(\"Reward model outputs for refined responses:\")\n", "summarize_reward_results(reward_model_outputs)\n", "print(\"#\" * 20)\n", "\n", "positive_samples_refined_validated = [  # Not used later, just for viz\n", "    sample for sample, reward_model_output in zip(positive_samples_refined, reward_model_outputs)\n", "    if reward_model_output is not None and reward_model_output[\"reward_value\"] == 1\n", "]\n", "print(f\"Number of samples with positive reward left: {len(positive_samples_refined_validated)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# If training directly on it\n", "samples_ready = positive_samples_refined_validated + negative_samples_using_fn + negative_samples_using_gpt\n", "len(samples_ready), OUTPUT_REFINEMENT_RESULT_FIELD_NAME"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Filepaths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PATHS_DETECTION_PROMPT = \"\"\"Software engineer is working on some reposiory.\n", "Software engineer asked coding assistant some question and received the following response (enclosed between ~~~ below):\n", "{formatted_response}\n", "\n", "You task is to extract all file paths and file names that are mentioned in the **response from coding assistant** and are likely a part of repository.\n", " \n", "Requirements:\n", "- Do not extract any files that are clearly not a part of repository (like .bashrc, /tmp/*)\n", "- Do not extract any files that response suggests to create\n", "- Do not extract folders\n", "- Return paths and names in EXACTLY the same form as they are present in the message. \n", "- If file is mentioned in a markdown link format (i.e. [displayed_name](file_path) ), return just file_path part.\n", "\n", "Return JSON with 1 key:\n", "1. `analysis` - a string with your analysis of the provided message and requirements.\n", "2. `detected_files` with a list of detected files.\n", "\"\"\"\n", "\n", "def extract_paths(response_text, gpt):\n", "    response_text = re.sub(r\"```.+?```\", \"```\\n...\\n```\", response_text, flags=re.DOTALL)\n", "    \n", "    detection_prompt = PATHS_DETECTION_PROMPT.format(\n", "        formatted_response=wrap_message(response_text),\n", "    )\n", "    gpt4_response = gpt([detection_prompt], model=\"gpt-4-1106-preview\", temperature=0, use_json=True)\n", "    assert isinstance(gpt4_response, dict)\n", "    \n", "    if len(gpt4_response[\"detected_files\"]) != len(set(gpt4_response[\"detected_files\"])):\n", "        print(f\"WARNING: Duplicate paths detected: {gpt4_response['detected_files']}\")\n", "        \n", "    filtered_paths = gpt4_response[\"detected_files\"]\n", "\n", "    return {\n", "        \"prompt\": detection_prompt,\n", "        \"extracted_paths\": list(set(filtered_paths)),\n", "        \"gpt4_response\": gpt4_response,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BinaryClassifierFilePaths:\n", "    def __init__(self, response_field_name):\n", "        self.response_field_name = response_field_name\n", "        self.gpt = GptWrapper()\n", "        \n", "    def classify_single_sample(self, sample):\n", "        try:\n", "            extraction_result = extract_paths(sample[self.response_field_name], self.gpt)\n", "        except Exception as e:\n", "            print(e)\n", "            return {\n", "                \"prompt\": None,\n", "                \"full_gpt_response\": None,\n", "                \"verdict\": <PERSON><PERSON><PERSON>,\n", "                \"extracted_paths\": None,\n", "            }\n", "        return {\n", "            \"prompt\": extraction_result[\"prompt\"],\n", "            \"full_gpt_response\": extraction_result[\"gpt4_response\"],\n", "            \"verdict\": len(extraction_result[\"extracted_paths\"]) > 0,\n", "            \"extracted_paths\": extraction_result[\"extracted_paths\"],\n", "        }\n", "        \n", "    def classify_samples(self, samples):\n", "        with Pool(50) as pool:\n", "            classification_results = list(tqdm(\n", "                pool.imap(self.classify_single_sample, samples),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return classification_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None, full_result_field_name=None):\n", "        assert full_result_field_name is not None\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Verdict: {sample[full_result_field_name]['verdict']}</p>\"\n", "            cur_html += details_html(\n", "                \"Classification prompt\",\n", "                markdown(str(sample[full_result_field_name][\"prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[full_result_field_name][\"full_gpt_response\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Extracted paths\",\n", "                markdown(str(sample[full_result_field_name][\"extracted_paths\"]))\n", "            )\n", "            cur_html += render_sample(sample)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "            \n", "            \n", "class RefinerFilePaths:\n", "    def __init__(self, prompt_template, response_field_name):\n", "        self.prompt_template = prompt_template\n", "        self.gpt = GptWrapper()\n", "        self.response_field_name = response_field_name\n", "        \n", "    def refine_single_sample(self, sample):\n", "        prompt = self.prompt_template.format(\n", "            formatted_retrieval=format_retrieval(sample),\n", "            formatted_sample=format_sample(sample),\n", "            response=wrap_message(sample[self.response_field_name])\n", "        )\n", "        gpt4_response = self.gpt([prompt], model=\"gpt-4-1106-preview\", temperature=0, use_json=True)\n", "        assert isinstance(gpt4_response, dict)\n", "\n", "        return {\n", "            \"prompt\": prompt,\n", "            \"full_gpt_response\": gpt4_response,\n", "            \"refined_response\": gpt4_response[\"refined_response\"],\n", "        }\n", "        \n", "    def refine_single_sample_safe(self, sample):\n", "        try:\n", "            return self.refine_single_sample(sample)\n", "        except Exception as e:\n", "            print(e)\n", "            return None\n", "        \n", "    def refine_samples(self, samples):\n", "        with Pool(30) as pool:\n", "            refinement_results = list(tqdm(\n", "                pool.imap(self.refine_single_sample_safe, samples),\n", "                total=len(samples)\n", "            ))\n", "        print(self.gpt.get_stats())\n", "        return refinement_results\n", "    \n", "    def generate_report(self, samples, max_samples=100, file_name_to_save=None, full_result_field_name=None):\n", "        assert full_result_field_name is not None\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, sample in tqdm(enumerate(samples)):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += details_html(\n", "                \"Refinement prompt\",\n", "                markdown(str(sample[full_result_field_name][\"prompt\"]))\n", "            )\n", "            cur_html += details_html(\n", "                \"Full GPT response\",\n", "                markdown(str(sample[full_result_field_name][\"full_gpt_response\"]))\n", "            )\n", "            cur_html += render_sample(sample, sample[full_result_field_name][\"refined_response\"], False)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result\n", "    \n", "\n", "class RewardFilepaths:\n", "    def __init__(self):\n", "        self.gpt = GptWrapper()\n", "        \n", "    def reward_single_sample(self, args):\n", "        sample, response = args\n", "        \n", "        extracted_paths_results = extract_paths(response, self.gpt)\n", "        extracted_paths = extracted_paths_results[\"extracted_paths\"]\n", "        if len(extracted_paths) == 0:\n", "            return {\n", "                \"reward_value\": None,\n", "                \"extracted_paths\": extracted_paths,\n", "                \"all_extract_results\": extracted_paths_results,\n", "            }\n", "        real_files = get_file_list(sample[\"extra\"][\"repo_id\"])\n", "        \n", "        for _path in extracted_paths:\n", "            if f\"]({_path})\" not in response:\n", "                return {\n", "                    \"reward_value\": 0,\n", "                    \"extracted_paths\": extracted_paths,\n", "                    \"all_extract_results\": extracted_paths_results,\n", "                }\n", "            _path = _path.strip(\"`\")\n", "            _path = _path.lstrip(\"/\")\n", "            if _path not in real_files and f\"/{_path}\" not in real_files:\n", "                return {\n", "                    \"reward_value\": 0,\n", "                    \"extracted_paths\": extracted_paths,\n", "                    \"all_extract_results\": extracted_paths_results,\n", "                }\n", "        return {\n", "            \"reward_value\": 1,\n", "            \"extracted_paths\": extracted_paths,\n", "            \"all_extract_results\": extracted_paths_results,\n", "        }\n", "        \n", "    def reward_single_sample_safe(self, args):\n", "        try:\n", "            return self.reward_single_sample(args)\n", "        except Exception as e:\n", "            print(e)\n", "            return {\n", "                \"reward_value\": None,\n", "                \"extracted_paths\": [],\n", "                \"all_extract_results\": {},\n", "            }\n", "        \n", "    def reward_samples(self, samples, responses):\n", "        args = []\n", "        for sample, response in zip(samples, responses):\n", "            args.append((sample, response))\n", "        with Pool(50) as pool:\n", "            reward_results = list(tqdm(\n", "                pool.imap(self.reward_single_sample_safe, args),\n", "                total=len(samples)\n", "            ))\n", "\n", "        return reward_results   \n", "    \n", "    def generate_report(self, samples, responses, reward_results, max_samples=100, file_name_to_save=None):\n", "        samples = samples[:max_samples]\n", "        \n", "        html = \"\"\n", "        for i, (sample, response, reward_result) in tqdm(enumerate(zip(samples, responses, reward_results))):\n", "            cur_html = f\"<h2>Chain {i}</h2>\"\n", "            cur_html += f\"<p>Reward value: {reward_result['reward_value']}</p>\"\n", "            cur_html += f\"<p>Extracted paths: {reward_result['extracted_paths']}</p>\"\n", "            cur_html += render_sample(sample, another_response=response, use_markdown=False)\n", "            html += cur_html\n", "            html += \"<hr>\"\n", "            \n", "        result = wrap_html(html)\n", "        if file_name_to_save is not None:\n", "            with open(EXPERIMENT_DIR / file_name_to_save, \"w\") as f:\n", "                f.write(result)\n", "        return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INPUT_RESPONSE_FIELD_NAME = \"llama_response\"\n", "OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME = \"file_paths_classification_verdict\"\n", "OUTPUT_CLASSIFICATION_FULL_FIELD_NAME = \"file_paths_classification_full_response\"\n", "\n", "\n", "classifier = BinaryClassifierFilePaths(INPUT_RESPONSE_FIELD_NAME)\n", "_classification_results = run_or_load_results(\n", "    partial(classifier.classify_samples, chat_samples),\n", "    \"file_paths_classification_results.json\"\n", ")\n", "\n", "for i, (sample, classification_result) in enumerate(zip(chat_samples, _classification_results)):\n", "    sample[OUTPUT_CLASSIFICATION_FULL_FIELD_NAME] = classification_result\n", "    sample[OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME] = classification_result[\"verdict\"]\n", "    \n", "positive_samples = [\n", "    sample for sample in chat_samples\n", "    if sample[OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME] \n", "]\n", "negative_samples_using_fn = []\n", "negative_samples_using_gpt = [\n", "    sample for sample in chat_samples\n", "    if sample[OUTPUT_CLASSIFICATION_VERDICT_FIELD_NAME] is False\n", "]\n", "print(f\"\"\"Number of positive samples: {len(positive_samples)}\n", "Number of negative samples using filter function: {len(negative_samples_using_fn)}\n", "Number of negative samples using GPT: {len(negative_samples_using_gpt)}\"\"\")\n", "\n", "_ = classifier.generate_report(\n", "    positive_samples,\n", "    file_name_to_save=\"file_paths_classification_report_positive_samples_filepaths.html\",\n", "    full_result_field_name=OUTPUT_CLASSIFICATION_FULL_FIELD_NAME\n", ")\n", "_ = classifier.generate_report(\n", "    negative_samples_using_gpt,\n", "    file_name_to_save=\"file_paths_classification_report_negative_samples_using_gpt_filepaths.html\",\n", "    full_result_field_name=OUTPUT_CLASSIFICATION_FULL_FIELD_NAME\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["classifier.gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTPUT_REFINEMENT_RESULT_FIELD_NAME = \"file_paths_refinement_result\"\n", "OUTPUT_REFINEMENT_FULL_FIELD_NAME = \"file_paths_refinement_full_response\"\n", "\n", "# PATHS_REFINEMENT_PROMPT = \"\"\"{formatted_retrieval}\n", "\n", "# {formatted_sample}\n", "\n", "# Here is a response (enclosed between ~~~) to user's last message generated by AI coding assistant:\n", "# {response}\n", "\n", "# Your task is to rewrite AI coding assistant's response such that every referenced file from a repository is written as a full path relative to the root of the project.\n", "# (see examples on code excerpts above).\n", "\n", "# Requirements:\n", "# - Do not rewrite paths to a general (repository-agnostic) files, like .bashrc, .gitignore, etc\n", "# - Do not rewrite paths to files that are not a part of repository\n", "# - Be careful with changing anything inside code blocks, to not break code\n", "# - Every path to referenced file should be separately enclosed in ` (single backticks) \n", "\n", "# Return JSON with a single key: `refined_response` with a string value.\n", "# \"\"\"\n", "\n", "PATHS_REFINEMENT_PROMPT = \"\"\"{formatted_retrieval}\n", "\n", "{formatted_sample}\n", "\n", "Here is a response (enclosed between ~~~) to user's last message generated by AI coding assistant:\n", "{response}\n", "\n", "Your task is to rewrite AI coding assistant's response such that every referenced file from a repository is written in a markdown link format.\n", "I.e.: [displayed_file_string](full_path_to_file_relative_to_root_of_project)\n", "displayed_file_string should be equal to how file is presented in the original response.\n", "(see examples for full paths in code excerpts above).\n", "\n", "Requirements:\n", "- Do not rewrite paths to files that are not a part of repository (like .bashrc, /tmp/*)\n", "- Do not rewrite paths to folders\n", "- Do not change anything inside code blocks, to not break code\n", "\n", "Return JSON with a single key: `refined_response` with a string value.\n", "\"\"\"\n", "\n", "refiner_filepaths = RefinerFilePaths(PATHS_REFINEMENT_PROMPT, INPUT_RESPONSE_FIELD_NAME)\n", "_refinement_results = run_or_load_results(\n", "    partial(refiner_filepaths.refine_samples, positive_samples),\n", "    \"refinement_filepaths_results.json\"\n", ")\n", "for i, (positive_sample, refinement_result) in enumerate(zip(positive_samples, _refinement_results)):\n", "    positive_sample[OUTPUT_REFINEMENT_FULL_FIELD_NAME] = refinement_result\n", "    positive_sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] = refinement_result[\"refined_response\"] if refinement_result is not None else None\n", "\n", "positive_samples_refined = [\n", "    positive_sample for positive_sample in positive_samples\n", "    if positive_sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] is not None\n", "]\n", "\n", "print(f\"Succesfully refined {len(positive_samples_refined)} / {len(positive_samples)} samples\")\n", "_ = refiner_filepaths.generate_report(\n", "    positive_samples_refined,\n", "    file_name_to_save=\"refinement_filepaths_report.html\",\n", "    full_result_field_name=OUTPUT_REFINEMENT_FULL_FIELD_NAME\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_chat_samples = positive_samples + negative_samples_using_fn + negative_samples_using_gpt\n", "\n", "print(f\"Total samples: {len(new_chat_samples)}\")\n", "num_default = 0\n", "\n", "for sample in new_chat_samples:\n", "    if len(sample.get(OUTPUT_REFINEMENT_RESULT_FIELD_NAME, \"\")) == 0:\n", "        sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] = sample[INPUT_RESPONSE_FIELD_NAME]\n", "        num_default += 1\n", "        \n", "print(f\"Number of samples with default response: {num_default}\")    \n", "chat_samples = new_chat_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(OUTPUT_REFINEMENT_RESULT_FIELD_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_filepaths = RewardFilepaths()\n", "\n", "#### For input responses\n", "reward_model_outputs = run_or_load_results(\n", "    partial(reward_filepaths.reward_samples, positive_samples_refined, [sample[INPUT_RESPONSE_FIELD_NAME] for sample in positive_samples_refined]),\n", "    \"file_paths_reward_results_input_responses.json\"\n", ")\n", "print(\"Reward model outputs for input responses:\")\n", "summarize_reward_results(reward_model_outputs)\n", "print(\"#\" * 20)\n", "\n", "#### For refined responses\n", "reward_model_outputs = run_or_load_results(\n", "    partial(reward_filepaths.reward_samples, positive_samples_refined, [sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] for sample in positive_samples_refined]),\n", "    \"file_paths_reward_results_refined_responses.json\"\n", ")\n", "print(\"Reward model outputs for refined responses:\")\n", "summarize_reward_results(reward_model_outputs)\n", "print(\"#\" * 20)\n", "\n", "positive_samples_refined_validated = [\n", "    sample for sample, reward_model_output in zip(positive_samples_refined, reward_model_outputs)\n", "    if reward_model_output is not None and reward_model_output[\"reward_value\"] == 1\n", "]\n", "print(f\"Number of samples with positive reward left: {len(positive_samples_refined_validated)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_filepaths.gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = reward_filepaths.generate_report(\n", "    positive_samples_refined,\n", "    [sample[OUTPUT_REFINEMENT_RESULT_FIELD_NAME] for sample in positive_samples_refined],\n", "    reward_model_outputs,\n", "    file_name_to_save=\"file_paths_reward_report_refined_responses.html\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# To skip filtering for this specific case\n", "chat_samples = positive_samples_refined_validated + negative_samples_using_fn + negative_samples_using_gpt\n", "\n", "print(f\"Total samples: {len(chat_samples)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# If training directly on it\n", "# samples_ready = positive_samples_refined_validated + negative_samples_using_fn + negative_samples_using_gpt\n", "# len(samples_ready), OUTPUT_REFINEMENT_RESULT_FIELD_NAME"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Reward filtering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INPUT_RESPONSE_FIELD_NAME = \"file_paths_refinement_result\"\n", "\n", "classifiers = [\n", "    BinaryClassifierFilePaths(INPUT_RESPONSE_FIELD_NAME),\n", "    BinaryClassifierLanguageLabels(INPUT_RESPONSE_FIELD_NAME),\n", "]\n", "\n", "reward_models = [\n", "    RewardFilepaths(),\n", "    RewardLanguageL<PERSON><PERSON>(),\n", "]\n", "\n", "stage_names = [\n", "    \"final_filtering_file_paths\",\n", "    \"final_filtering_language_labels\",\n", "]\n", "\n", "cur_samples = chat_samples\n", "\n", "for i, (classifier, reward_model, stage_name) in enumerate(zip(classifiers, reward_models, stage_names)):\n", "    print(f\"Stage {i}: {stage_name}. Samples left: {len(cur_samples)}\")\n", "    \n", "    # Classification step\n", "    # cur_classification_results = classifier.classify_samples(cur_samples)\n", "    cur_classification_results = run_or_load_results(\n", "        partial(classifier.classify_samples, cur_samples),\n", "        f\"{stage_name}_classification_results.json\"\n", "    )\n", "    for i, (sample, classification_result) in enumerate(zip(cur_samples, cur_classification_results)):\n", "        sample[f\"{stage_name}_classification_full_response\"] = classification_result\n", "        sample[f\"{stage_name}_classification_verdict\"] = classification_result[\"verdict\"]\n", "    \n", "    cur_positive_samples = [\n", "        sample for sample, classification_result in zip(cur_samples, cur_classification_results)\n", "        if classification_result[\"verdict\"]\n", "    ]\n", "    cur_negative_samples = [\n", "        sample for sample, classification_result in zip(cur_samples, cur_classification_results)\n", "        if classification_result[\"verdict\"] is False\n", "    ]\n", "    print(f\"Number of positive samples: {len(cur_positive_samples)}\")\n", "    print(f\"Number of negative samples: {len(cur_negative_samples)}\")\n", "    \n", "    _ = classifier.generate_report(\n", "        cur_positive_samples,\n", "        file_name_to_save=f\"{stage_name}_classification_report_positive_samples.html\",\n", "        full_result_field_name=f\"{stage_name}_classification_full_response\"\n", "    )\n", "    _ = classifier.generate_report(\n", "        cur_negative_samples,\n", "        file_name_to_save=f\"{stage_name}_classification_report_negative_samples.html\",\n", "        full_result_field_name=f\"{stage_name}_classification_full_response\"\n", "    )\n", "    \n", "    # Reward filtering\n", "    # reward_model_outputs = reward_model.reward_samples(\n", "    #     cur_positive_samples,\n", "    #     [sample[INPUT_RESPONSE_FIELD_NAME] for sample in cur_positive_samples]\n", "    # )\n", "    reward_model_outputs = run_or_load_results(\n", "        partial(reward_model.reward_samples, cur_positive_samples, [sample[INPUT_RESPONSE_FIELD_NAME] for sample in cur_positive_samples]),\n", "        f\"{stage_name}_reward_results.json\"\n", "    )\n", "    _ = reward_model.generate_report(\n", "        cur_positive_samples,\n", "        [sample[INPUT_RESPONSE_FIELD_NAME] for sample in cur_positive_samples],\n", "        reward_model_outputs,\n", "        file_name_to_save=f\"{stage_name}_reward_report.html\"\n", "    )\n", "    summarize_reward_results(reward_model_outputs)\n", "    cur_positive_samples_validated = [\n", "        sample for sample, reward_model_output in zip(cur_positive_samples, reward_model_outputs)\n", "        if reward_model_output is not None and reward_model_output[\"reward_value\"] == 1\n", "    ]\n", "    cur_samples = cur_positive_samples_validated + cur_negative_samples\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples_ready = cur_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Number of samples ready for training: {len(samples_ready)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare training data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TRAIN_FRACTION = 0.9\n", "\n", "def tokenize_sample_for_training(sample, response_field_name):            \n", "    history = [Exchange(request_message=x[\"user\"], response_text=x[\"assistant\"]) for x in sample[\"history\"]]\n", "    prompt_input = ChatPromptInput(\n", "        path=sample.get(\"path\", \"\"),\n", "        prefix=sample.get(\"prefix\", \"\"),\n", "        selected_code=sample.get(\"selected_code\", \"\"),\n", "        suffix=sample.get(\"suffix\", \"\"),\n", "        message=sample[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(sample.get(\"prefix\", \"\") + sample.get(\"selected_code\", \"\") + sample.get(\"suffix\", \"\")),\n", "        retrieved_chunks=[PromptChunk(**chunk) for chunk in sample[\"chunks\"]],\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "    tokenized_input = PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "\n", "    target = sample[response_field_name]\n", "    tokenized_output = PROMPT_FORMATTER.tokenizer.tokenize_safe(target) + [\n", "        TOKENIZER.special_tokens.eod_token\n", "    ]\n", "    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output\n", "    complete_prompt += [-1 * TOKENIZER.special_tokens.eod_token] * (\n", "        SEQUENCE_LENGTH - len(complete_prompt) + 1\n", "    )\n", "\n", "    return complete_prompt\n", "\n", "def save_dataset(samples, output_path):\n", "    random.shuffle(samples)\n", "    if not output_path.parent.exists():\n", "        output_path.parent.mkdir()\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sample in tqdm(samples):\n", "        builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    \n", "\n", "def get_mmap_samples(path: Path):\n", "    dataset = MMapIndexedDataset(str(path))\n", "    all_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        all_samples.append(dataset[i].tolist())\n", "    return all_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INPUT_RESPONSE_FIELD_NAME = \"language_labels_refinement_result\" # -- Reusing from reward filtering step\n", "\n", "if (EXPERIMENT_DIR / \"sft_data_prepared.json\").exists():\n", "    with open(EXPERIMENT_DIR / \"sft_data_prepared.json\", \"r\") as f:\n", "        prepared_training_data = json.load(f)\n", "    samples_ready_train = prepared_training_data[\"samples_ready_train\"]\n", "    samples_ready_valid = prepared_training_data[\"samples_ready_valid\"]\n", "    samples_ready_train_tokenized = prepared_training_data[\"samples_ready_train_tokenized\"]\n", "    samples_ready_valid_tokenized = prepared_training_data[\"samples_ready_valid_tokenized\"]\n", "else:\n", "    random.seed(42)\n", "    random.shuffle(samples_ready)\n", "    \n", "    samples_ready_train = samples_ready[:int(len(samples_ready) * TRAIN_FRACTION)]\n", "    samples_ready_valid = samples_ready[int(len(samples_ready) * TRAIN_FRACTION):]\n", "    \n", "    samples_ready_train_tokenized = [tokenize_sample_for_training(s, response_field_name=INPUT_RESPONSE_FIELD_NAME) for s in tqdm(samples_ready_train)]\n", "    samples_ready_valid_tokenized = [tokenize_sample_for_training(s, response_field_name=INPUT_RESPONSE_FIELD_NAME) for s in tqdm(samples_ready_valid)]\n", "    \n", "    to_save = {\n", "        \"samples_ready_train\": samples_ready_train,\n", "        \"samples_ready_valid\": samples_ready_valid,\n", "        \"samples_ready_train_tokenized\": samples_ready_train_tokenized,\n", "        \"samples_ready_valid_tokenized\": samples_ready_valid_tokenized,\n", "    }\n", "    with open(EXPERIMENT_DIR / \"sft_data_prepared.json\", \"w\") as f:\n", "        json.dump(to_save, f)\n", "\n", "print(f\"Number of train samples: {len(samples_ready_train_tokenized)}\")\n", "print(f\"Number of valid samples: {len(samples_ready_valid_tokenized)}\")\n", "\n", "\n", "if not (EXPERIMENT_DIR / \"sft_data\").exists():\n", "    (EXPERIMENT_DIR / \"sft_data\").mkdir(exist_ok=True)\n", "    save_dataset(samples_ready_train_tokenized, EXPERIMENT_DIR / \"sft_data\" / \"train.json\")\n", "    save_dataset(samples_ready_valid_tokenized, EXPERIMENT_DIR / \"sft_data\" / \"valid.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENT_DIR / \"sft_data\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_check_dataset = get_mmap_samples(EXPERIMENT_DIR / \"sft_data\" / \"train\")\n", "print(\"selected code\" in TOKENIZER.detokenize(list(map(abs, _check_dataset[0]))))\n", "print(TOKENIZER.detokenize(list(map(abs, _check_dataset[0]))))\n", "\n", "# _hashes = set()\n", "# for s in tqdm(_check_dataset):\n", "#     text = TOKENIZER.detokenize(list(map(abs, s)))\n", "#     _hashes.add(hashlib.sha256(text.encode()).hexdigest())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(list(map(abs, _check_dataset[15]))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# _total = 0\n", "# for i in range(len(_check_dataset)):\n", "#     _t = TOKENIZER.detokenize(list(map(abs, _check_dataset[i])))\n", "#     _l_indx = _t.rfind(\"<|start_header_id|>assistant<|end_header_id|>\")\n", "#     _t = _t[_l_indx:]\n", "#     if \"](\" in _t:\n", "#         print(i)\n", "#         _total += 1\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(TOKENIZER.detokenize(list(map(abs, _check_dataset[19]))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check finetuned model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inference\n", "from research.models.meta_model import GenerationOptions, get_model, register_model\n", "from base.fastforward.llama.model_specs import _MODEL_SPECS\n", "from research.models.fastforward_llama_models import LLAMA_FastForwardModel\n", "from base.fastforward.llama import model_specs as llama_model_specs\n", "\n", "LLAMA3_70B_MODEL_SPEC = _MODEL_SPECS[\"llama3-70b\"]\n", "LLAMA3_70B_MODEL_SPEC.checkpoint_path = \"/mnt/efs/augment/user/yuri/tmp_ckpts/llama3-aug11-filepaths-markdown-plus-language-labels-mix-v12-refilter-additional-ffw\"\n", "\n", "\n", "@register_model(\"fastforward_llama3_70b_instruct\")\n", "class FastForwardLLAMA3_70B_Instruct(LLAMA_FastForwardModel):\n", "    \"\"\"The open-source LLAMA3-70B-Instruct model.\"\"\"\n", "\n", "    seq_length: int = 8192\n", "\n", "    model_spec: llama_model_specs.LlamaModelSpec = LLAMA3_70B_MODEL_SPEC\n", "\n", "    @classmethod\n", "    def create_default_formatter(cls):\n", "        tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "        apportionment = ChatTokenApportionment(\n", "            path_len=256,\n", "            message_len=0,\n", "            chat_history_len=2048,\n", "            prefix_len=1024,\n", "            selected_code_len=0,\n", "            suffix_len=1024,\n", "            max_prompt_len=6144,\n", "            retrieval_len=-1\n", "        )\n", "\n", "        prompt_formatter = get_chat_prompt_formatter_by_name(\n", "            \"binks_llama3\",\n", "            tokenizer,\n", "            apportionment,\n", "        )\n", "        return prompt_formatter\n", "    \n", "def tokenize_sample_for_inference(sample):\n", "    history = [Exchange(request_message=x[\"user\"], response_text=x[\"assistant\"]) for x in sample[\"history\"]]\n", "    prompt_input = ChatPromptInput(\n", "        path=sample.get(\"path\", \"\"),\n", "        prefix=sample.get(\"prefix\", \"\"),\n", "        selected_code=sample.get(\"selected_code\", \"\"),\n", "        suffix=sample.get(\"suffix\", \"\"),\n", "        message=sample[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(sample.get(\"prefix\", \"\") + sample.get(\"selected_code\", \"\") + sample.get(\"suffix\", \"\")),\n", "        retrieved_chunks=[PromptChunk(**chunk) for chunk in sample[\"chunks\"]],\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "    tokenized_input = PROMPT_FORMATTER.format_prompt(prompt_input).tokens\n", "    return tokenized_input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"staging_request_insight_full_export_dataset\"\n", "\n", "def get_chat_sample(request_id):\n", "    query = f\"\"\"\n", "SELECT *\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_event\n", "WHERE request_id=\"{request_id}\"\n", "AND event_type IN (\"chat_host_request\", \"chat_host_response\", \"request_metadata\")\n", "ORDER BY time DESC\n", "LIMIT 100000\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "\n", "    request_rows = [row for row in all_rows if row.event_type == \"chat_host_request\"]\n", "    response_rows = [row for row in all_rows if row.event_type == \"chat_host_response\"]\n", "    meta_rows = [row for row in all_rows if row.event_type == \"request_metadata\"]\n", "\n", "    assert len(request_rows) == 1, f\"Expected 1 request, got {len(request_rows)}\"\n", "    assert len(response_rows) == 1, f\"Expected 1 response, got {len(response_rows)}\"\n", "    assert len(meta_rows) == 1, f\"Expected 1 metadata, got {len(meta_rows)}\"\n", "\n", "    return {\n", "        \"request\": request_rows[0][\"raw_json\"][\"request\"],\n", "        \"response\": response_rows[0][\"raw_json\"][\"response\"],\n", "        \"metadata\": meta_rows[0][\"raw_json\"],\n", "        \"request_token_ids\": request_rows[0][\"raw_json\"][\"tokenization\"][\"token_ids\"],\n", "    }\n", "    \n", "def generate_response(sample, model):\n", "    generated_text = model.raw_generate(\n", "        sample[\"request_token_ids\"],\n", "        GenerationOptions(max_generated_tokens=2048)\n", "    )\n", "\n", "    return generated_text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Around 3 mins\n", "\n", "model = get_model(\"fastforward_llama3_70b_instruct\")\n", "model.load()\n", "model.tokenizer.eod_id = model.tokenizer.special_tokens.eos"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eval_samples = [\n", "    # s for s in samples_ready_valid if s[\"final_filtering_file_paths_classification_verdict\"]\n", "    s for s in samples_ready_valid if s[\"final_filtering_language_labels_classification_verdict\"]\n", "]\n", "len(eval_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eval_samples = eval_samples[:102]\n", "len(eval_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model = RewardLanguageLabels()\n", "# reward_model = RewardFilepaths()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sft_generations = []\n", "\n", "for i, sample in tqdm(enumerate(eval_samples), total=len(eval_samples)):\n", "    tokenized_sample = tokenize_sample_for_inference(sample)\n", "    cur_response = model.raw_generate(\n", "        tokenized_sample,\n", "        GenerationOptions(max_generated_tokens=2048)\n", "    )\n", "    sft_generations.append(cur_response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model_outputs = reward_model.reward_samples(\n", "    eval_samples,\n", "    sft_generations\n", ")\n", "summarize_reward_results(reward_model_outputs)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = reward_model.generate_report(\n", "    eval_samples,\n", "    sft_generations,\n", "    reward_model_outputs,\n", "    file_name_to_save=\"check_finetuned_model_rewards_finetuned_language_labels.html\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reward_model_outputs = reward_model.reward_samples(\n", "    eval_samples,\n", "    [sample[\"llama_response\"] for sample in eval_samples]\n", ")\n", "summarize_reward_results(reward_model_outputs)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = reward_model.generate_report(\n", "    eval_samples,\n", "    sft_generations,\n", "    reward_model_outputs,\n", "    file_name_to_save=\"check_finetuned_model_rewards_llama.html\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_SAMPLES: dict[str, Any] = {\n", "    \"code_edits\": [\n", "        (\"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\", \"Model should modify the whole selected code\"),\n", "        (\"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\", \"Model should modify only the import line.\"),\n", "        (\"75aedc45-11f6-4a5a-98d9-43798ba29479\", \"Model should print only modified code, without repeating functions.\"),\n", "    ],\n", "    \"code_edits_one_turn\" : [\n", "        (\"4b958757-6818-44f8-af55-2a63e82b8458\", \"Model should modify only selected code.\"),\n", "        (\"7fd0623b-c217-4658-89f9-af27246f7bfd\", \"Model shouldn't lose T = TypeVar(\\\"T\\\")\"),\n", "        (\"974831ff-997d-4de8-984b-94e6c6bd42dc\", \"Model should preserve new line character at the end of selection\"),\n", "    ],\n", "    \"regression_tests\": [\n", "        (\"5199b947-b45a-4e22-a087-e59a12beda91\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718823357108449?thread_ts=1718821966.276869&cid=C06R495KUSD\"),\n", "        (\"3bb42ee8-c40b-4c69-96cf-9bf23e8548fb\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1717614811054099\"),\n", "        (\"f08c5194-6cb6-4480-87a1-b256ab2f52e2\", \"https://augment-wic8570.slack.com/archives/C06R495KUSD/p1718918465955939\"),\n", "        (\"40176c9d-2acb-44e9-b759-b16a21e5aeb6\", \"Explanation of selected code.\"),\n", "        (\"cd09df68-6189-4cb9-9032-87810deda180\", \"Explanation of selected code on 2nd turn.\"),\n", "    ],\n", "    \"filepaths\": [\n", "        (\"e6ef9a30-f94f-4d01-aaf8-7ac3b4e14c95\", \"dense_scorer.py, dense_scorer_v2.py\"),\n", "        (\"bb87242c-ae55-4c6a-9d7b-925129b1dada\", \"edit_gen_stages.py\"),  # staging shard\n", "        (\"9aa4f9e0-77bd-4b34-ae1a-d3b5ba060035\", \"deploy_lib.jsonnet, flags.yaml\"),\n", "        (\"700d81ea-509f-43c9-a397-7307ee7eb153\", \"post.rb has to show to app/models/post.rb, not lib/discourse_dev/post.rb\")  # staging shard\n", "    ],\n", "    \"language_labels\": [\n", "        (\"04d5128b-92ec-47f8-8ae2-2ebb48ae0356\", \"python\"),\n", "        (\"80ee77d1-ba36-480e-89e1-e7b55a9efab5\", \"python\"),\n", "        (\"5f0e2497-e1a7-4bfb-82c6-3b5949ee3794\", \"python\"),\n", "        (\"7636a168-e3fe-4e7e-b343-2c45de4da1cb\", \"python\")\n", "    ]\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_chat_samples(request_ids):\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_metadata AS metadata\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "        \"request\": row.request[\"request\"],\n", "        \"response\": row.response[\"response\"],\n", "        \"metadata\": row.metadata,\n", "        \"datetime\": row.time,\n", "        \"request_token_ids\": row.request[\"tokenization\"][\"token_ids\"],\n", "    }\n", "\n", "    return chat_rows_dic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_eval_samples_request_ids = []\n", "category_to_request_ids = defaultdict(list)\n", "for category, samples in EVAL_SAMPLES.items():\n", "    request_ids = [s[0] for s in samples]\n", "    all_eval_samples_request_ids += request_ids\n", "    category_to_request_ids[category].extend(request_ids)\n", "all_loaded_eval_samples = get_chat_samples(all_eval_samples_request_ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["loaded_eval_samples = defaultdict(list)\n", "\n", "for category, eval_samples_info in EVAL_SAMPLES.items():\n", "    for sample_info in tqdm(eval_samples_info):\n", "        request_id, comment = sample_info\n", "        sample = all_loaded_eval_samples[request_id]\n", "        loaded_eval_samples[category].append(\n", "            {\n", "                \"sample\": sample,\n", "                \"request_id\": request_id,\n", "                \"comment\": comment\n", "            }\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for category, samples in loaded_eval_samples.items():\n", "    for i in tqdm(range(len(samples)), desc=f\"Generating samples for {category}\"):\n", "        samples[i][\"generated_text\"] = generate_response(samples[i][\"sample\"], model)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(loaded_eval_samples[\"language_labels\"][0][\"generated_text\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(loaded_eval_samples[\"language_labels\"][0][\"sample\"][\"response\"][\"text\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_request_sample(sample, another_response=None, use_markdown=True):\n", "    request = sample[\"request\"]\n", "    \n", "    history = request.get(\"chat_history\", [])\n", "    selected_code = request.get(\"selected_code\", None)\n", "    message = request[\"message\"]\n", "    response = sample[\"response\"][\"text\"]\n", "    \n", "    cur_index = 0\n", "    html_content = \"\"\n", "    \n", "    for exchange in history:\n", "        html_content += f\"<h3>Message {cur_index}</h3>\"\n", "        cur_index += 1\n", "        html_content += render_simple_message(exchange[\"request_message\"], exchange[\"response_text\"])\n", "        html_content += \"<hr>\"\n", "    if len(history) > 0:\n", "        html_content = details_html(\"Chat history\", html_content)\n", "        \n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "    html_content += render_simple_message(message, response, selected_code, another_response, use_markdown)\n", "    \n", "    return html_content\n", "\n", "def render_category_report(samples, use_markdown=True):\n", "    html = \"\"\n", "    for i, sample in enumerate(samples):\n", "        comment = sample[\"comment\"]\n", "        generated_text = sample[\"generated_text\"]\n", "        sample = sample[\"sample\"]\n", "        cur_html = f\"<h2>Chain {i}</h2>\"\n", "        \n", "        cur_html += details_html(\"Full Chat prompt\", TOKENIZER.detokenize(sample[\"request_token_ids\"]))\n", "        cur_html += details_html(\"Comment\", comment)\n", "        cur_html += render_request_sample(sample, generated_text, use_markdown)\n", "        html += cur_html\n", "        html += \"<hr>\"\n", "        \n", "    result = wrap_html(html)\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["real_requests_report_html = \"\"\n", "\n", "for category, samples in loaded_eval_samples.items():\n", "    real_requests_report_html += f\"\"\"\n", "    <details>\n", "        <summary>Category: {category}</summary>\n", "        {render_category_report(samples, use_markdown=False)}\n", "    </details>\n", "    \"\"\"\n", "\n", "real_requests_report_html = wrap_html(real_requests_report_html)\n", "# with open(EXPERIMENT_DIR / \"real_requests_report_v3.html\", \"w\") as f:\n", "#     f.write(real_requests_report_html)\n", "\n", "with open(\"real_requests_report_v4.html\", \"w\") as f:\n", "    f.write(real_requests_report_html)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples[0][\"sample\"][\"response\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
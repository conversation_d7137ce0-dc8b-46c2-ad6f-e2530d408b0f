{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from google.cloud import bigquery\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from tqdm import tqdm\n", "from base.stream_processor.claude_stream_processor_v3 import ClaudeStreamProcessorV3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODEL_TO_CLIENT = {\n", "    \"claude-3-5-sonnet@20240620\": AnthropicVertexAiClient(\n", "        \"augment-387916\",\n", "        \"us-east5\",\n", "        \"claude-3-5-sonnet@20240620\",\n", "        0,\n", "        1024 * 5,\n", "    ),\n", "    \"claude-3-haiku@20240307\": AnthropicVertexAiClient(\n", "        \"augment-387916\",\n", "        \"us-east5\",\n", "        \"claude-3-haiku@20240307\",\n", "        0,\n", "        4000,\n", "    ),\n", "}\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", ")\n", "PROMPT_FORMATTER = get_structured_chat_prompt_formatter_by_name(\n", "    \"binks-claude-v4\", TOKEN_APPORTIONMENT\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_chat_samples(\n", "    request_ids,\n", "    project_id: str = \"system-services-prod\",\n", "    dataset_name: str = \"staging_request_insight_full_export_dataset\",\n", ") -> dict:\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {project_id}.{dataset_name}.request_metadata AS metadata\n", "JOIN {project_id}.{dataset_name}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {project_id}.{dataset_name}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=project_id)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "            \"request\": row.request[\"request\"],\n", "            \"response\": row.response[\"response\"],\n", "            \"metadata\": row.metadata,\n", "            \"datetime\": row.time.isoformat(),\n", "            \"row\": row,\n", "        }\n", "\n", "    return chat_rows_dic\n", "\n", "\n", "def format_sample(raw_sample) -> tuple[ChatPromptInput, StructuredChatPromptOutput]:\n", "    history = [\n", "        Exchange(e[\"request_message\"], e[\"response_text\"], e[\"request_id\"])\n", "        for e in raw_sample[\"row\"].request[\"request\"].get(\"chat_history\", [])\n", "    ]\n", "\n", "    chunks = [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            char_start=chunk.get(\"char_offset\", 0),\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk.get(\"blob_name\", \"\"),\n", "            origin=chunk[\"origin\"],\n", "        )\n", "        for chunk in raw_sample[\"row\"].request.get(\"retrieved_chunks\", [])\n", "    ]\n", "\n", "    request_entry = raw_sample[\"row\"].request[\"request\"]\n", "    if request_entry.get(\"suffix\", \"\").startswith(\"\\n\") and request_entry.get(\n", "        \"selected_code\", \"\"\n", "    ):\n", "        # IDK why it happens, but it does\n", "        request_entry[\"selected_code\"] += \"\\n\"\n", "        request_entry[\"suffix\"] = request_entry[\"suffix\"][1:]\n", "        print(\"Suffix starts with new line\")\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=request_entry[\"path\"],\n", "        prefix=request_entry.get(\"prefix\", \"\"),\n", "        selected_code=request_entry.get(\"selected_code\", \"\"),\n", "        suffix=request_entry.get(\"suffix\", \"\"),\n", "        message=request_entry[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(\n", "            request_entry.get(\"prefix\", \"\")\n", "            + request_entry.get(\"selected_code\", \"\")\n", "            + request_entry.get(\"suffix\", \"\")\n", "        ),\n", "        retrieved_chunks=chunks,\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    return prompt_input, prompt_output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGING_SHARD0_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    \"0696c744-9fb8-4b50-b655-fad32ddb38d5\",\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\",\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "    # <PERSON>'s new samples\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\",\n", "]\n", "\n", "OLD_DOGFOOD_SAMPLES = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "]\n", "\n", "PLAYTEST_SMARTPASTE_SAMPLES = [\n", "    \"7d544538-4031-490d-889c-2f0806448f87\",\n", "    \"a616ea44-8240-410e-855a-fdd5ec5d43f1\",  # original bad edit: \"52e92730-3411-48d7-b7e7-d98a98df4801\",\n", "    \"4028990b-d670-43f1-9e66-23d8b62c73b1\",  # original bad edit: \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/93c6c898-14dd-4512-8f7d-231a492ab3a9\"\n", "    \"d56ba211-6d34-499e-ae72-aa784e7d653e\",  # original bad edit: \"6616d2c7-8a3d-4e63-890d-1b8add19fb12\"\n", "    \"7d19a934-3695-40d4-ae2c-f2bbf71225d5\",\n", "    (\n", "        \"01159bf5-a5b7-4e6a-9712-f140813f885b\",\n", "        \"/home/<USER>/repos/augment/clients/common/webviews/src/common/components/markdown/Markdown.svelte\",\n", "        3,\n", "    ),\n", "    \"7cc4fa2b-bf3d-471a-a59b-67baa5769043\",  # https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/0dfbb331-586f-42b4-95aa-9a4d54837351\n", "]\n", "\n", "PURE_ADDITIONS = [\n", "    \"e267315b-5cb7-4c8d-ac22-d543dd492865\",\n", "    \"c4af6731-a50e-4a77-8589-01b46894c5fc\",\n", "    (\n", "        \"5e940f35-3e6f-4622-ace6-3f3b8e7ed952\",  # \"1c0dd859-1182-4d2a-a4cd-faf9cd5adf35\",\n", "        None,\n", "        2,\n", "    ),\n", "    \"bf88ad7b-0e30-482f-9a70-0e24679b3d0e\",  # \"b97c8092-0aa1-46ab-870a-df0e10f0236c\",\n", "]\n", "\n", "SAMPLES = (\n", "    OLD_DOGFOOD_SAMPLES\n", "    + STAGING_SHARD0_SAMPLES\n", "    + PLAYTEST_SMARTPASTE_SAMPLES\n", "    + PURE_ADDITIONS\n", ")\n", "\n", "download_samples = get_chat_samples(\n", "    [s if isinstance(s, str) else s[0] for s in SAMPLES]\n", ")\n", "download_samples_dev_deploy = get_chat_samples(\n", "    [s if isinstance(s, str) else s[0] for s in SAMPLES],\n", "    project_id=\"system-services-dev\",  # type: ignore\n", "    dataset_name=\"dev_yuri_request_insight_full_export_dataset\",  # type: ignore\n", ")\n", "\n", "print(f\"Downloaded from staging: {len(download_samples)}\")\n", "print(f\"Downloaded from dev_deploy: {len(download_samples_dev_deploy)}\")\n", "print(f\"Total downloaded: {len(download_samples) + len(download_samples_dev_deploy) }\")\n", "\n", "download_samples = download_samples | download_samples_dev_deploy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_anthropic(cur_message, history, system_prompt, model_name, prefill=None):\n", "    formatted_messages = []\n", "    for message in history:\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": message[0],\n", "            }\n", "        )\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": message[1],\n", "            }\n", "        )\n", "\n", "    # Add cur message\n", "    formatted_messages.append(\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": cur_message,\n", "        }\n", "    )\n", "\n", "    if prefill:\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": prefill,\n", "            }\n", "        )\n", "\n", "    response = MODEL_TO_CLIENT[model_name].client.messages.create(\n", "        model=model_name,\n", "        max_tokens=4000,\n", "        messages=formatted_messages,\n", "        system=system_prompt,\n", "        temperature=0,\n", "    )\n", "\n", "    return response\n", "\n", "\n", "def run_anthropic_w_retries(\n", "    cur_message,\n", "    history,\n", "    system_prompt,\n", "    model_name,\n", "    max_retries=3,\n", "    prefill=None,\n", "):\n", "    result = None\n", "    for i in range(max_retries):\n", "        try:\n", "            result = run_anthropic(\n", "                cur_message, history, system_prompt, model_name, prefill\n", "            )\n", "            break\n", "        except Exception as e:\n", "            print(f\"Failed to run anthropic with tools, retrying {i+1}/{max_retries}\")\n", "            print(e)\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CLAUDE_CODEBLOCKS_XML_AND_SUGGESTED_QUESTIONS_SYSTEM_PROMPT = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "- At the end of every answer, you should write your 2 best guesses of what user will ask next. Enclose themn in:\n", "<guess_of_next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "</guess_of_next_user_question>\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "```python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "```yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "```\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "```ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "```\n", "</augment_code_snippet>\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generation logic\n", "class ChatWithSuggestedFollowups:\n", "    @classmethod\n", "    def get_response(cls, prompt_output: StructuredChatPromptOutput) -> dict:\n", "        claude_response_original = (\n", "            run_anthropic_w_retries(\n", "                prompt_output.message,\n", "                [\n", "                    (item.request_message, item.response_text)\n", "                    for item in prompt_output.chat_history\n", "                ],\n", "                prompt_output.system_prompt,\n", "                \"claude-3-5-sonnet@20240620\",\n", "            )\n", "            .content[0]\n", "            .text\n", "        )\n", "\n", "        claude_response_modified = (\n", "            run_anthropic_w_retries(\n", "                prompt_output.message,\n", "                [\n", "                    (item.request_message, item.response_text)\n", "                    for item in prompt_output.chat_history\n", "                ],\n", "                CLAUDE_CODEBLOCKS_XML_AND_SUGGESTED_QUESTIONS_SYSTEM_PROMPT,\n", "                \"claude-3-5-sonnet@20240620\",\n", "            )\n", "            .content[0]\n", "            .text\n", "        )\n", "\n", "        return {\n", "            \"original_response\": claude_response_original,\n", "            \"modified_response\": claude_response_modified,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SeparateSuggestedFollowups:\n", "    @classmethod\n", "    def get_response(cls, prompt_output: StructuredChatPromptOutput) -> dict:\n", "        claude_response_original = (\n", "            run_anthropic_w_retries(\n", "                prompt_output.message,\n", "                [\n", "                    (item.request_message, item.response_text)\n", "                    for item in prompt_output.chat_history\n", "                ],\n", "                prompt_output.system_prompt,\n", "                \"claude-3-5-sonnet@20240620\",\n", "            )\n", "            .content[0]\n", "            .text\n", "        )\n", "\n", "        prompt = \"\"\"Write your 2 best guesses of what user will ask next. Keep them short. Enclose them in:\n", "<guess_of_next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "</guess_of_next_user_question>\"\"\"\n", "        prefill = \"\"\"<guess_of_next_user_question>\n", "    <next_user_question>\"\"\"\n", "\n", "        suggested_qs = (\n", "            run_anthropic_w_retries(\n", "                prompt,\n", "                [\n", "                    (item.request_message, item.response_text)\n", "                    for item in prompt_output.chat_history\n", "                ]\n", "                + [(prompt_output.message, claude_response_original)],\n", "                prompt_output.system_prompt,\n", "                \"claude-3-haiku@20240307\",\n", "                prefill=prefill,\n", "            )\n", "            .content[0]\n", "            .text\n", "        )\n", "\n", "        return {\n", "            \"original_response\": claude_response_original,\n", "            \"modified_response\": prefill + suggested_qs,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.stream_processor.basic_stream_processor import StreamProcessorOutputType\n", "\n", "\n", "for sample in tqdm(SAMPLES):\n", "    if isinstance(sample, str):\n", "        request_id = sample\n", "    else:\n", "        request_id = sample[0]\n", "    sample = download_samples[request_id]\n", "\n", "    _, prompt_output = format_sample(sample)\n", "    # response = ChatWithSuggestedFollowups.get_response(prompt_output)\n", "    response = SeparateSuggestedFollowups.get_response(prompt_output)\n", "\n", "    if not response[\"modified_response\"].endswith(\"\\n\"):\n", "        response[\"modified_response\"] += \"\\n\"\n", "    parsed_response = list(\n", "        ClaudeStreamProcessorV3(1).process_next_chunk(response[\"modified_response\"])\n", "    )\n", "\n", "    compiled_text = f\"\"\"### REQUEST ID: {request_id}\n", "### ORIGINAL RESPONSE\n", "{response[\"original_response\"]}\n", "### GENERATED RESPONSE\n", "{response[\"modified_response\"]}\n", "### PARSED RESPONSE ({len(parsed_response)})\n", "\"\"\"\n", "\n", "    questions_only = \"\"\n", "    for p_s in parsed_response:\n", "        compiled_text += f\"\"\"\n", "##### TYPE: {p_s.type}\n", "{p_s.text}\n", "#####\n", "\"\"\"\n", "        if p_s.type == StreamProcessorOutputType.SUGGESTED_QUESTIONS:\n", "            questions_only += p_s.text + \"\\n\"\n", "\n", "    log_file = \"/tmp/log15_short_old_haiku\"\n", "    with open(log_file, \"a\") as f:\n", "        f.write(compiled_text)\n", "        f.write(\"\\n\" * 20)\n", "\n", "    with open(log_file + \"_qs\", \"a\") as f:\n", "        f.write(questions_only)\n", "        f.write(\"\\n\" * 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parsed_response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response[\"modified_response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["str(parsed_response[0].type)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
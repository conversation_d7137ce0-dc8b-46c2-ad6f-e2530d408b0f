{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import json\n", "from collections import defaultdict\n", "from functools import partial\n", "from pathlib import Path\n", "from random import random\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import pyspark.sql\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql import functions as F\n", "from tqdm import tqdm\n", "\n", "from base.prompt_format_completion.prompt_formatter import TokenList\n", "from base.prompt_format_edit.prompt_formatter import ExceedContextLength\n", "from research.core.utils_for_log import create_logger\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "\n", "\n", "LOGGER = create_logger(\"droid_data_convert\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SYNTHETIC_SAMPLES_PATH = Path(\"/mnt/efs/augment/user/yuri/data/droid_clean_data_jun20_prompts.json\")\n", "RAW_REPOS_URI = \"s3a://yuri-dev-bucket/droid/raw_repos_feb_14/\"\n", "NUM_WORKERS = 50\n", "TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/jun_20_24_process_v7\"\n", "SEQ_LENGTH = 8192\n", "OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2\")\n", "TRAIN_FRACTION = 0.95\n", "\n", "RETRIEVER_CONFIG = {\n", "    \"scorer\": {\n", "        \"name\": \"generic_neox\",\n", "        \"checkpoint_path\": \"chatanol/chatanol1-11\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query_simple_chat\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "        \"verbose\": True,\n", "        \"tokenizer_name\": \"starcodertokenizer\",\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "}\n", "\n", "APPORTIONMENT = ChatTokenApportionment(\n", "    path_len=256,\n", "    message_len=0,\n", "    chat_history_len=2048,\n", "    prefix_len=1024,\n", "    selected_code_len=0,\n", "    suffix_len=1024,\n", "    max_prompt_len=6144,\n", "    retrieval_len=-1\n", ")\n", "\n", "OUTPUT_DIR.mkdir(parents=False, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_spark(job_name: str, max_workers: int) -> SparkSession:\n", "    return k8s_session(\n", "        name=job_name,\n", "        gpu_type=[\"A40\", \"RTX_A6000\"],\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        max_workers=max_workers,\n", "    )\n", "\n", "\n", "def join_samples_with_repos(\n", "    spark: SparkSession, synthetic_samples_path: Path, raw_repos_uri: str\n", ") -> pyspark.sql.DataFrame:\n", "    with synthetic_samples_path.open() as f:\n", "        synthetic_samples = json.load(f)\n", "    LOGGER.info(f\"Loaded {len(synthetic_samples)} synthetic samples.\")\n", "\n", "    repos_df = spark.read.parquet(raw_repos_uri)\n", "\n", "    samples_by_repo = defaultdict(list)\n", "    for sample in synthetic_samples:\n", "        repo_url = sample[\"repo_url\"]\n", "        samples_by_repo[repo_url].append(json.dumps(sample))\n", "\n", "    samples_by_repo_df = pd.DataFrame(\n", "        [\n", "            {\"repo_url\": repo_url, \"samples\": samples}\n", "            for repo_url, samples in samples_by_repo.items()\n", "        ]\n", "    )\n", "    samples_by_repo_df = spark.createDataFrame(samples_by_repo_df)\n", "\n", "    samples_join_w_repos_df = samples_by_repo_df.join(\n", "        repos_df, <PERSON>.col(\"repo_url\") == F.col(\"repo_uuid\"), \"inner\"\n", "    )\n", "\n", "    return samples_join_w_repos_df\n", "\n", "\n", "# RETRIEVER = None\n", "\n", "def tokenize(row_joined, token_apportionment, retriever_config, seq_length):\n", "    #### Initialize everything\n", "    from base.prompt_format_chat import get_chat_prompt_formatter_by_name\n", "    from base.prompt_format_completion.prompt_formatter import PromptChunk\n", "    from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "    from base.tokenizers import create_tokenizer_by_name\n", "    from research.core.model_input import ModelInput\n", "    from research.eval.harness.factories import create_retriever\n", "    from research.core.types import Chunk, Document\n", "\n", "    def convert_chunk(chunk: Chunk):\n", "        return PromptChunk(\n", "            text=chunk.text,\n", "            path=chunk.parent_doc.path,\n", "            char_start=chunk.char_offset,\n", "            char_end=chunk.char_offset + chunk.length,\n", "            unique_id=chunk.id,\n", "        )\n", "    \n", "    tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "    prompt_formatter = get_chat_prompt_formatter_by_name(\n", "        \"binks_llama3\",\n", "        tokenizer,\n", "        token_apportionment,\n", "    )\n", "\n", "    #### Add documents to retriever\n", "    # global RETRIEVER\n", "    # if RETRIEVER is None:\n", "    RETRIEVER = create_retriever(retriever_config)\n", "    RETRIEVER.load()\n", "\n", "    file_list = row_joined[\"file_list\"]\n", "    docs = [\n", "        Document(\n", "            id=file[\"hexsha\"], text=file[\"content\"], path=file[\"max_stars_repo_path\"]\n", "        )\n", "        for file in file_list\n", "    ]\n", "    RETRIEVER.add_docs(docs)\n", "    \n", "    #### Main loop\n", "    tokenized_prompts = []\n", "    for sample in row_joined[\"samples\"]:\n", "        sample = json.loads(sample)\n", "\n", "        #### Some preparation and filtering\n", "        modified_file = list(\n", "            filter(\n", "                lambda f: f[\"max_stars_repo_path\"] == sample[\"path\"],\n", "                row_joined[\"file_list\"],\n", "            )\n", "        )\n", "        if len(modified_file) != 1:\n", "            print(\n", "                f\"Skipping sample (len(modified_file) == {len(modified_file)}) with path={sample['path']}.\"\n", "            )\n", "            continue\n", "\n", "        modified_file = modified_file[0]\n", "        if sample[\"prefix\"] + sample[\"new_middle\"] + sample[\"suffix\"] != modified_file[\"content\"]:\n", "            print(\n", "                f\"Skipping sample (new_middle != modified_file['content']) with path={sample['path']}.\"\n", "            )\n", "            continue\n", "        \n", "        #### Retrieval\n", "        chunks, _ = RETRIEVER.query(\n", "            model_input=ModelInput(\n", "                extra={\n", "                    \"message\": sample[\"instruction\"]\n", "                }\n", "            ),\n", "            top_k=32,\n", "        )\n", "        chunks = [*map(convert_chunk, chunks)]\n", "        \n", "        #### Prompt formatting\n", "        prompt_input = ChatPromptInput(\n", "            path=sample[\"path\"],\n", "            prefix=sample[\"prefix\"],\n", "            selected_code=sample[\"old_middle\"],\n", "            suffix=sample[\"suffix\"],\n", "            message=sample[\"instruction\"],\n", "            chat_history=[],\n", "            prefix_begin=0,\n", "            suffix_end=len(sample[\"prefix\"] + sample[\"old_middle\"] + sample[\"suffix\"]),\n", "            retrieved_chunks=chunks,\n", "        )\n", "        \n", "        try:\n", "            prompt_output = prompt_formatter.format_prompt(prompt_input)\n", "        except ExceedContextLength:\n", "            print(\n", "                f\"Skipping sample (ExceedContextLength in prepare_prompt) with path={sample['path']}.\"\n", "            )\n", "            continue\n", "        \n", "        #### Tokenization\n", "        tokenized_output = tokenizer.tokenize_safe(\"```\\n\" + sample[\"new_middle\"] + \"\\n```\") + [\n", "            tokenizer.special_tokens.eos\n", "        ]\n", "        \n", "        complete_prompt = [-1 * t for t in prompt_output.tokens] + tokenized_output\n", "        if len(complete_prompt) > seq_length:\n", "            print(\n", "                f\"Skipping sample (len(complete_prompt) > seq_length) with path={sample['path']}.\"\n", "            )\n", "            continue\n", "        complete_prompt += [-1 * tokenizer.special_tokens.eos] * (\n", "            seq_length - len(complete_prompt) + 1\n", "        )  # +1 to make total prompt of length SEQUENCE_LENGTH + 1\n", "        \n", "        tokenized_prompts.append(complete_prompt)\n", "        \n", "    return tokenized_prompts\n", "\n", "\n", "def tokenize_batch(\n", "    batch: pd.DataFrame,\n", "    token_apportionment,\n", "    retriever_config,\n", "    seq_length,\n", "    progress_file: Path,\n", ") -> pd.DataFrame:\n", "    tokenized_prompts: list[TokenList] = []\n", "    for i in range(batch.shape[0]):\n", "        cur_result = tokenize(\n", "            batch.iloc[i],\n", "            token_apportionment,\n", "            retriever_config,\n", "            seq_length,\n", "        )\n", "        with progress_file.open(\"a\") as f:\n", "            f.write(f\"{len(cur_result)}\\n\")\n", "        tokenized_prompts.extend(cur_result)\n", "\n", "    # Otherwise we get a schema mismatch. We filter out such samples later in `save_dataset`.\n", "    if len(tokenized_prompts) == 0:\n", "        tokenized_prompts = [[-1]]\n", "\n", "    return pd.DataFrame(\n", "        {\n", "            \"prompt_tokens\": tokenized_prompts,\n", "        },\n", "    )\n", "\n", "\n", "def save_dataset(train_dataset, eval_dataset, output_path):\n", "    from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "\n", "    for f_name, dataset in [(\"train\", train_dataset), (\"valid\", eval_dataset)]:\n", "        cur_output_path = output_path / f_name\n", "        builder = MMapIndexedDatasetBuilder(\n", "            cur_output_path.with_suffix(\".bin\"), dtype=np.int32\n", "        )\n", "        for sample in tqdm(dataset):\n", "            if len(sample) == 1:\n", "                LOGGER.info(\"Skipping [-1] sample.\")\n", "                continue\n", "            builder.add_item(sample)\n", "            builder.end_document()\n", "        builder.finalize(cur_output_path.with_suffix(\".idx\"))\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_spark(\"yuri_data_process\", NUM_WORKERS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples_join_w_repos_df = join_samples_with_repos(\n", "    spark, SYNTHET<PERSON>_SAMPLES_PATH, RAW_REPOS_URI\n", ")\n", "\n", "samples_join_w_repos_df.repartition(NUM_WORKERS * 4).write.parquet(\n", "    f\"{TMP_BUCKET}/samples_join_w_repos\"\n", ")\n", "\n", "print(samples_join_w_repos_df.count())\n", "samples_join_w_repos_df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(\n", "        tokenize_batch,\n", "        token_apportionment=APPORTIONMENT,\n", "        retriever_config=RETRIEVER_CONFIG,\n", "        seq_length=SEQ_LENGTH,\n", "        progress_file=OUTPUT_DIR / \"spark_progress.txt\",\n", "    ),\n", "    input_path=f\"{TMP_BUCKET}/samples_join_w_repos\",\n", "    output_path=f\"{TMP_BUCKET}/tokenized_samples\",\n", "    output_column=\"prompt_tokens\",\n", "    # drop_original_columns=True,\n", "    timeout=int(1e6),\n", "    batch_size=4,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_result[\"task_info\"].to_csv(f\"{OUTPUT_DIR}/spark_logs.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_samples = (\n", "    spark.read.parquet(f\"{TMP_BUCKET}/tokenized_samples\")\n", "    .toPandas()[\"prompt_tokens\"]\n", "    .tolist()\n", ")\n", "num_train_samples = int(len(tokenized_samples) * TRAIN_FRACTION)\n", "save_dataset(\n", "    tokenized_samples[:num_train_samples],\n", "    tokenized_samples[num_train_samples:],\n", "    OUTPUT_DIR,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from base.tokenizers import create_tokenizer_by_name\n", "\n", "\n", "def get_all_samples(path: Path):\n", "    dataset = MMapIndexedDataset(str(path))\n", "    all_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        all_samples.append(dataset[i].copy())\n", "    return all_samples\n", "\n", "samples = get_all_samples(OUTPUT_DIR / \"train\")\n", "tokenizer = create_tokenizer_by_name(\"llama3_instruct\")\n", "\n", "print(tokenizer.detokenize(list(map(abs, samples[0])))) # type: ignore"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "\n", "import os\n", "\n", "os.environ[\"PYTHONPATH\"] += \":/home/<USER>/repos/augment/research/gpt-neox:/home/<USER>/repos/augment\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import random\n", "import numpy as np\n", "import torch\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from experimental.guy.apis.llama3_tokenizer import ChatFormat, Message, Tokenizer\n", "\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"llama3_instruct\")\n", "DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2\")\n", "OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yuri/data/droid_llama/real/droid_clean_data_jun20_llama_v2_just_header\")\n", "SPLIT_NAME = \"valid\"\n", "SEQ_LENGTH = 8192\n", "\n", "OUTPUT_PATH.mkdir(exist_ok=True)\n", "\n", "ALL_IPS = [\n", "    \"*************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "    \"**************\",\n", "\n", "    \"**************\",\n", "    # \"*************\",\n", "]\n", "\n", "PORTS = [\"8000\"]\n", "\n", "ALL_URLS = [f\"{ip}:{port}\" for ip in ALL_IPS for port in PORTS]\n", "\n", "\n", "def get_all_samples(path: Path):\n", "    dataset = MMapIndexedDataset(str(path))\n", "    all_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        all_samples.append(dataset[i].tolist())\n", "    return all_samples\n", "\n", "\n", "def save_dataset(samples, output_path):\n", "    random.shuffle(samples)\n", "    if not output_path.parent.exists():\n", "        output_path.parent.mkdir()\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sample in tqdm(samples):\n", "        builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "\n", "\n", "class TritonClient:\n", "    def __init__(self, url: str):\n", "        self.tokenizer = Tokenizer(\"/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct/tokenizer.model\")\n", "        self.prompt_formatter = ChatFormat(self.tokenizer)\n", "        self.url = url\n", "\n", "    def generate(self, user_message: str):\n", "        dialog = [\n", "            Message(role=\"user\", content=user_message)\n", "        ]\n", "\n", "        full_prompt = self.tokenizer.decode(\n", "            self.prompt_formatter.encode_dialog_prompt(dialog)\n", "        )\n", "        payload = self.get_payload(full_prompt)\n", "        \n", "        response_json = self.send_request(payload)\n", "\n", "        return response_json[\"text_output\"]\n", "    \n", "    def generate_raw(self, prompt: str):\n", "        payload = self.get_payload(prompt)\n", "        response_json = self.send_request(payload)\n", "\n", "        return response_json[\"text_output\"]\n", "\n", "    def get_payload(self, full_prompt):\n", "        payload = {\n", "            \"text_input\": full_prompt,\n", "            \"max_tokens\": 1000,\n", "            \"end_id\": self.tokenizer.special_tokens[\"<|eot_id|>\"],\n", "            \"stream\": <PERSON><PERSON><PERSON>,\n", "            \"temperature\": 0.8,\n", "            \"top_k\": 40,\n", "            \"top_p\": 0.95,\n", "            \"random_seed\": random.randint(0, int(2 ** 31)),\n", "            \"return_context_logits\": <PERSON><PERSON><PERSON>,\n", "            \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "            \"return_generation_logits\": <PERSON><PERSON><PERSON>,\n", "        }\n", "\n", "        return payload\n", "\n", "    def send_request(self, payload):\n", "        headers = {\"Content-Type\": \"application/json\"}\n", "\n", "        response = requests.post(\n", "            f\"http://{self.url}/v2/models/ensemble/generate\",\n", "            headers=headers,\n", "            data=json.dumps(payload),\n", "            timeout=100,\n", "        )\n", "        response_json = response.json()\n", "\n", "        return response_json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = get_all_samples(DATA_PATH / SPLIT_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from queue import Queue\n", "import threading\n", "from typing import Any\n", "\n", "client_queue = Queue()\n", "\n", "threads = []\n", "lock = threading.Lock()\n", "num_generated = 0\n", "\n", "prefixes_tokens: list[Any] = [None] * len(samples)  # Common prefix for all samples\n", "new_outputs_str: list[Any] = [None] * len(samples)  # New output for all samples (excluding completion)\n", "completions_str: list[Any] = [None] * len(samples) # Completions generated by LLaMA\n", "\n", "\n", "for url in tqdm(ALL_URLS):\n", "    client_queue.put(TritonClient(url))\n", "\n", "\n", "\n", "def process_sample(modified_prompt, i):\n", "    client = client_queue.get()\n", "\n", "    # completion = client.generate_raw(modified_prompt)\n", "    completion = \"\"\n", "    \n", "    with lock:\n", "        completions_str[i] = completion\n", "        global num_generated\n", "        num_generated += 1\n", "        print(f\"Generated {num_generated} samples\")\n", "\n", "    client_queue.put(client)\n", "    \n", "\n", "# for i, sample in enumerate(tqdm(samples)):\n", "for i, sample in enumerate(samples):\n", "    while abs(sample[-1]) == TOKENIZER.special_tokens.eos:\n", "        sample = sample[:-1]\n", "    \n", "    full_prompt = TOKENIZER.detokenize(list(map(abs, sample))) # type: ignore\n", "      \n", "    last_header_idx = full_prompt.rfind(\"<|eot_id|><|start_header_id|>assistant<|end_header_id|>\")\n", "    code_section_start = full_prompt.find(\"```\", last_header_idx)\n", "    \n", "    previous_output_tokenized = TOKENIZER.tokenize_safe(full_prompt[code_section_start:])\n", "    assert sample[-len(previous_output_tokenized):] == previous_output_tokenized\n", "    prefixes_tokens[i] = sample[:-len(previous_output_tokenized)]\n", "    \n", "    # new_output = \"Here is the modified code:\\n\" + full_prompt[code_section_start:] + \"\\n\"\n", "    new_output = \"Here is the modified code:\\n\" + full_prompt[code_section_start:]\n", "    new_outputs_str[i] = new_output\n", "    \n", "    llama_input = TOKENIZER.detokenize(list(map(abs, prefixes_tokens[i]))) + new_output # type: ignore\n", "    cur_thread = threading.Thread(\n", "        target=process_sample,\n", "        args=(llama_input, i)\n", "    )\n", "    threads.append(cur_thread)\n", "    cur_thread.start()\n", "    \n", "for thread in threads:\n", "    thread.join()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_samples = []\n", "\n", "for prefix, new_output, completion in tqdm(zip(prefixes_tokens, new_outputs_str, completions_str)):\n", "    new_sample = prefix + TOKENIZER.tokenize_safe(new_output) + TOKENIZER.tokenize_safe(completion)\n", "    new_sample += [TOKENIZER.special_tokens.eos]\n", "    new_sample += [-1 * TOKENIZER.special_tokens.eos] * (SEQ_LENGTH - len(new_sample) + 1)    \n", "\n", "    new_samples.append(new_sample)\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "\n", "\n", "to_check_samples = copy.deepcopy(samples)\n", "to_check_new_samples = copy.deepcopy(new_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_dataset(new_samples, OUTPUT_PATH / SPLIT_NAME)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Check"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CHECK_IDX = 5\n", "\n", "assert len(to_check_samples[CHECK_IDX]) == len(to_check_new_samples[CHECK_IDX])\n", "\n", "first_diff_ifx = 0\n", "while to_check_samples[CHECK_IDX][first_diff_ifx] == to_check_new_samples[CHECK_IDX][first_diff_ifx]:\n", "    first_diff_ifx += 1\n", "    \n", "print(first_diff_ifx)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"The same part:\")\n", "print(TOKENIZER.detokenize(list(map(abs, to_check_samples[CHECK_IDX][:first_diff_ifx]))))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(list(map(abs, to_check_samples[CHECK_IDX][first_diff_ifx:]))))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(list(map(abs, to_check_new_samples[CHECK_IDX][first_diff_ifx:]))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_samples[CHECK_IDX][:first_diff_ifx]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_samples[CHECK_IDX][first_diff_ifx:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
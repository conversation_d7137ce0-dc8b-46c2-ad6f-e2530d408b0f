{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "\n", "os.environ[\"PYTHONPATH\"] = \"/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import difflib\n", "import random\n", "import pandas as pd\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "\n", "from research.data.synthetic_code_edit.util_lib import get_unified_diff\n", "from multiprocessing import Pool\n", "\n", "from experimental.yuri.pr_edits.droid_data import save_dataset\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_spark(job_name: str, max_workers: int):\n", "    return k8s_session(\n", "        name=job_name,\n", "        gpu_type=[\"A40\", \"Quadro_RTX_5000\", \"RTX_A5000\", \"RTX_A6000\"],\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"100G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "            \"spark.rpc.message.maxSize\": \"2047\"\n", "        },\n", "        max_workers=max_workers,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GENERATED_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k/\")\n", "STAGE1_URI = \"s3a://yuri-dev-bucket/tmp/mar30_stage1_v9\"\n", "STAGE2_URI = \"s3a://yuri-dev-bucket/tmp/mar30_stage2_v9\"\n", "PROGRESS_FILE = Path(\"/mnt/efs/augment/user/yuri/tmp/pb_mar30_v9\")\n", "FINAL_DATASET_DIR = Path(\"/mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k_filtered_tokenized\") "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = []\n", "\n", "for file in tqdm(GENERATED_DATA_PATH.glob(\"*.json\")):\n", "    with file.open(\"r\") as f:\n", "        data.append(json.load(f))\n", "    # if len(data) > 100:\n", "    #     break\n", "        \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v13_1k\n", "SKIPPED = [\n", "    0, 14, 28, 31, 34, 36, 49, 52, 55, 67, 73, 79, 81, 82, 89, 99, 100, 107, 111,\n", "    114, 121, 129, 133, 137, 142, 149, 159, 163, 176, 182, 184, 185, 189, 196, 208,\n", "    209, 218, 228, 230, 238, 245, 249, 250, 251, 256, 272, 279, 291, 295, 297, 304,\n", "    305, 308, 310, 322, 332, 335, 348, 351, 359, 374, 380, 387, 395, 404, 428, 431,\n", "    441, 448, 460, 469, 479, 480, 493, 503, 512, 516, 524, 528, 535, 545, 546, 548,\n", "    549, 560, 567, 570, 574, 575, 593, 607, 612, 614, 618, 630, 635, 640, 641, 642,\n", "    651, 654, 656, 658, 665, 673, 683, 684, 685, 688, 689, 692, 693, 699, 712\n", "]\n", "\n", "len(SKIPPED)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_filtered = []\n", "for sample_id, sample in enumerate(data):\n", "    if sample_id in SKIPPED:\n", "        continue\n", "    data_filtered.append(sample)\n", "data = data_filtered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = list(\n", "    filter(\n", "        lambda s: sum(len(d[\"text\"]) for d in s[\"docs\"]) < 100000000,\n", "        data\n", "    )\n", ")\n", "\n", "len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_spark(\"yuri-process\", 64)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["parts_urls = []\n", "\n", "for part_start in range(0, len(data), 64):\n", "    cur_data_df = pd.DataFrame(data[part_start:part_start + 64])\n", "    spark.createDataFrame(cur_data_df).repartition(32).write.parquet(\n", "        f\"{STAGE1_URI}_part{part_start}\"\n", "    )\n", "    parts_urls.append(f\"{STAGE1_URI}_part{part_start}\")\n", "\n", "parts_urls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df = spark.read.parquet(parts_urls[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for next_url in tqdm(parts_urls[1:]):\n", "    combined_df = combined_df.union(spark.read.parquet(next_url))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df.repartition(128).write.parquet(STAGE1_URI)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RETRIEVER = None\n", "RETRIEVER_CONFIG = {\n", "    \"scorer\": {\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "        \"max_tokens\": 1023,\n", "        \"prefix_ratio\": 0.9,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "        \"max_tokens\": 999,\n", "    },\n", "}\n", "\n", "POSSIBLE_INSTRUCTIONS = [\n", "    \"Fix imports\",\n", "    \"Fix import statements.\",\n", "    \"Check and fix imports.\",\n", "    \"Adjust import statements.\",\n", "    \"Review and correct imports.\",\n", "    \"Update imports.\",\n", "    \"Ensure imports are correct.\",\n", "    \"Refine imports.\",\n", "    \"Correct import errors.\",\n", "    \"Address import issues.\",\n", "    \"Streamline imports.\",\n", "    \"Revisit import section.\",\n", "    \"Repair import declarations.\",\n", "    \"Solve import problems\",\n", "    \"Tidy up imports.\",\n", "]\n", "\n", "\n", "\n", "def tokenize(sample):\n", "    from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "    from research.eval.harness.factories import create_retriever\n", "    from research.core.model_input import ModelInput\n", "    from research.core.types import Document\n", "    #### Initialization section\n", "    global RETRIEVER  # pylint: disable=global-statement\n", "    if RETRIEVER is None:\n", "        RETRIEVER = create_retriever(RETRIEVER_CONFIG)\n", "        RETRIEVER.load()\n", "\n", "    TOKEN_APPORTIONMENT = {\n", "        \"path_len\": 256,\n", "        \"instruction_len\": 512,\n", "        \"prefix_len\": 1536,\n", "        \"selected_code_len\": 4096,\n", "        \"suffix_len\": 1024,\n", "        \"max_prompt_tokens\": 16384 - 4096,  # 4096 represents the max output tokens\n", "    }\n", "    SEQUENCE_LENGTH = 16384\n", "    PROMPT_FORMATTER = get_prompt_formatter(\"droid\", **TOKEN_APPORTIONMENT)\n", "    #### End of initialization section\n", "\n", "    all_docs = list(map(dict, sample[\"docs\"]))\n", "    cur_docs = [Document(id=d[\"id\"], text=d[\"text\"], path=d[\"path\"]) for d in all_docs]\n", "    RETRIEVER.add_docs(cur_docs)\n", "\n", "    query_input = ModelInput(\n", "        prefix=sample[\"prefix\"],\n", "        suffix=sample[\"suffix\"],\n", "        path=sample[\"path\"],\n", "    )\n", "    retrieved_chunks, _ = RETRIEVER.query(query_input, top_k=128)\n", "    # retrieved_chunks = []\n", "\n", "    model_input = ModelInput(\n", "        prefix=sample[\"prefix\"],\n", "        suffix=sample[\"suffix\"],\n", "        path=sample[\"path\"],\n", "        retrieved_chunks=retrieved_chunks,\n", "        extra={\n", "            \"instruction\": random.choice(POSSIBLE_INSTRUCTIONS),\n", "            \"selected_code\": sample[\"selected_code\"],\n", "            \"prefix_begin\": 0,\n", "            \"suffix_end\": len(sample[\"prefix\"] + sample[\"selected_code\"] + sample[\"suffix\"]),\n", "        }\n", "    )\n", "\n", "    tokenized_input, _ = PROMPT_FORMATTER.prepare_prompt(model_input)\n", "    tokenized_output = PROMPT_FORMATTER.tokenizer.tokenize(sample[\"updated_code\"] + \"\\n```\\n\") + [PROMPT_FORMATTER.tokenizer.eod_id]\n", "    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output\n", "    complete_prompt += [-1 * PROMPT_FORMATTER.tokenizer.eod_id] * (SEQUENCE_LENGTH - len(complete_prompt) + 1)  # +1 to make total prompt of length SEQUENCE_LENGTH + 1\n", "\n", "    return complete_prompt\n", "\n", "\n", "\n", "def tokenize_batch(\n", "    batch: pd.DataFrame\n", "):\n", "    tokenized_prompts = []\n", "    for i in range(batch.shape[0]):\n", "        cur_result = tokenize(batch.iloc[i])\n", "        with PROGRESS_FILE.open(\"a\") as f:\n", "            f.write(f\"{len(cur_result)}\\n\")\n", "        tokenized_prompts.append(cur_result)\n", "    \n", "    # Otherwise we get a schema mismatch. We filter out such samples later in `save_dataset`.\n", "    if len(tokenized_prompts) == 0:\n", "        tokenized_prompts = [[-1]]\n", "    return pd.DataFrame(\n", "        {\n", "            \"prompt_tokens\": tokenized_prompts,\n", "        },\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_result = map_parquet.apply_pandas(\n", "    spark,\n", "    tokenize_batch,\n", "    input_path=STAGE1_URI,\n", "    output_path=STAGE2_URI,\n", "    drop_original_columns=True,\n", "    timeout=int(1e6),\n", "    batch_size=2\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_samples = (\n", "    spark.read.parquet(f\"{STAGE2_URI}\")\n", "    .toPandas()[\"prompt_tokens\"]\n", "    .tolist()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(tokenized_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FINAL_DATASET_DIR.mkdir(exist_ok=True)\n", "\n", "save_dataset(tokenized_samples[:380], tokenized_samples[380:], FINAL_DATASET_DIR)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "\n", "os.environ[\"PYTHONPATH\"] = \"/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import difflib\n", "import random\n", "import pandas as pd\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.eval.harness.factories import create_retriever\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Document\n", "from research.data.synthetic_code_edit.util_lib import get_unified_diff\n", "from multiprocessing import Pool\n", "\n", "from experimental.yuri.pr_edits.droid_data import save_dataset\n", "from research.data.spark import k8s_session"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_spark(job_name: str, max_workers: int):\n", "    return k8s_session(\n", "        name=job_name,\n", "        gpu_type=[\"A40\", \"Quadro_RTX_5000\", \"RTX_A5000\", \"RTX_A6000\"],\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"30G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        max_workers=max_workers,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GENERATED_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = []\n", "\n", "for file in tqdm(GENERATED_DATA_PATH.glob(\"*.json\")):\n", "    with file.open(\"r\") as f:\n", "        data.append(json.load(f))\n", "    if len(data) > 20:\n", "        break\n", "        \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# v13_1k\n", "SKIPPED = [\n", "    0, 14, 28, 31, 34, 36, 49, 52, 55, 67, 73, 79, 81, 82, 89, 99, 100, 107, 111,\n", "    114, 121, 129, 133, 137, 142, 149, 159, 163, 176, 182, 184, 185, 189, 196, 208,\n", "    209, 218, 228, 230, 238, 245, 249, 250, 251, 256, 272, 279, 291, 295, 297, 304,\n", "    305, 308, 310, 322, 332, 335, 348, 351, 359, 374, 380, 387, 395, 404, 428, 431,\n", "    441, 448, 460, 469, 479, 480, 493, 503, 512, 516, 524, 528, 535, 545, 546, 548,\n", "    549, 560, 567, 570, 574, 575, 593, 607, 612, 614, 618, 630, 635, 640, 641, 642,\n", "    651, 654, 656, 658, 665, 673, 683, 684, 685, 688, 689, 692, 693, 699, 712\n", "]\n", "\n", "len(SKIPPED)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_filtered = []\n", "for sample_id, sample in enumerate(data):\n", "    if sample_id in SKIPPED:\n", "        continue\n", "    data_filtered.append(sample)\n", "data = data_filtered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_df = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_spark(\"yuri-process\", 32)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "# Visualize"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;|\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "def get_diff_html(input_code, output_code, comment, another_header=None):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><strong>{comment}</strong></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Comment: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    if another_header is not None:\n", "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n", "    return html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "for sample_id, sample in enumerate(data):\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {sample_id} {sample['type']} {sample['N']}</h2><hr>\"\n", "\n", "    cur_diff = get_diff_html(\n", "        sample[\"selected_code\"],\n", "        sample[\"updated_code\"],\n", "        \"\",\n", "        \"\",\n", "    )\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./imports_mar30_v13_1k_after_filtering2.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[0].keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tokenize"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GOOD_INDICES = list(range(len(data)))\n", "len(GOOD_INDICES)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["POSSIBLE_INSTRUCTIONS = [\n", "    \"Fix imports\",\n", "    \"Fix import statements.\",\n", "    \"Check and fix imports.\",\n", "    \"Adjust import statements.\",\n", "    \"Review and correct imports.\",\n", "    \"Update imports.\",\n", "    \"Ensure imports are correct.\",\n", "    \"Refine imports.\",\n", "    \"Correct import errors.\",\n", "    \"Address import issues.\",\n", "    \"Streamline imports.\",\n", "    \"Revisit import section.\",\n", "    \"Repair import declarations.\",\n", "    \"Solve import problems\",\n", "    \"Tidy up imports.\",\n", "]\n", "\n", "\n", "RETRIEVER_CONFIG = {\n", "    \"scorer\": {\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "        \"max_tokens\": 1023,\n", "        \"prefix_ratio\": 0.9,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "        \"max_tokens\": 999,\n", "    },\n", "}\n", "\n", "\n", "RETRIEVER = create_retriever(RETRIEVER_CONFIG)\n", "RETRIEVER.load()\n", "\n", "TOKEN_APPORTIONMENT = {\n", "    \"path_len\": 256,\n", "    \"instruction_len\": 512,\n", "    \"prefix_len\": 1536,\n", "    \"selected_code_len\": 4096,\n", "    \"suffix_len\": 1024,\n", "    \"max_prompt_tokens\": 16384 - 4096,  # 4096 represents the max output tokens\n", "}\n", "SEQUENCE_LENGTH = 16384\n", "\n", "PROMPT_FORMATTER = get_prompt_formatter(\"droid\", **TOKEN_APPORTIONMENT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def tokenize(sample):\n", "    cur_docs = [Document(id=d[\"id\"], text=d[\"text\"], path=d[\"path\"]) for d in sample[\"docs\"]]\n", "    RETRIEVER.add_docs(cur_docs)\n", "\n", "    query_input = ModelInput(\n", "        prefix=sample[\"prefix\"],\n", "        suffix=sample[\"suffix\"],\n", "        path=sample[\"path\"],\n", "    )\n", "    retrieved_chunks, _ = RETRIEVER.query(query_input, top_k=128)\n", "    # retrieved_chunks = []\n", "\n", "    model_input = ModelInput(\n", "        prefix=sample[\"prefix\"],\n", "        suffix=sample[\"suffix\"],\n", "        path=sample[\"path\"],\n", "        retrieved_chunks=retrieved_chunks,\n", "        extra={\n", "            \"instruction\": random.choice(POSSIBLE_INSTRUCTIONS),\n", "            \"selected_code\": sample[\"selected_code\"],\n", "            \"prefix_begin\": 0,\n", "            \"suffix_end\": len(sample[\"prefix\"] + sample[\"selected_code\"] + sample[\"suffix\"]),\n", "        }\n", "    )\n", "\n", "    tokenized_input, _ = PROMPT_FORMATTER.prepare_prompt(model_input)\n", "    tokenized_output = PROMPT_FORMATTER.tokenizer.tokenize(sample[\"updated_code\"] + \"\\n```\\n\") + [PROMPT_FORMATTER.tokenizer.eod_id]\n", "    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output\n", "    complete_prompt += [-1 * PROMPT_FORMATTER.tokenizer.eod_id] * (SEQUENCE_LENGTH - len(complete_prompt) + 1)  # +1 to make total prompt of length SEQUENCE_LENGTH + 1\n", "\n", "    return complete_prompt\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["t = tokenize(data[GOOD_INDICES[0]])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(PROMPT_FORMATTER.tokenizer.detokenize(list(map(abs, t))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data[0]['suffix'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FINAL_DATASET_DIR = Path(\"/mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k_noretrieval_tokenized\")\n", "\n", "tokenized_samples = []\n", "for indx in tqdm(GOOD_INDICES):\n", "    try:\n", "        t = tokenize(data[indx])\n", "    except Exception as e:\n", "        print(f\"Failed to process sample {indx}: {e}\")\n", "        continue\n", "    tokenized_samples.append(t)\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(tokenized_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FINAL_DATASET_DIR.mkdir(exist_ok=True)\n", "\n", "save_dataset(tokenized_samples[:560], tokenized_samples[560:], FINAL_DATASET_DIR)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(PROMPT_FORMATTER.tokenizer.detokenize(list(map(abs, tokenized_samples[1]))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
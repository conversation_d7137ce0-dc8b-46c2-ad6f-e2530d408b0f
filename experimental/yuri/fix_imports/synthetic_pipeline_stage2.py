import json
import sys
from pathlib import Path

from tqdm import tqdm

from research.core.abstract_prompt_formatter import get_prompt_formatter
from research.core.model_input import ModelInput
from research.core.types import Document
from research.eval.harness.factories import create_retriever

GENERATED_DATA_PATH = Path(
    "/mnt/efs/augment/user/yuri/data/fix_imports_mar29_v8_3lines/"
)
OUTPUT_DIR_PATH = Path("/tmp/fix_imports_mar29_v8_3lines_stage2_v1")

# OUTPUT_DIR_PATH.mkdir()

PART = int(sys.argv[1])

data = []
for file in tqdm(GENERATED_DATA_PATH.glob("*.json")):
    with file.open("r") as f:
        data.append(json.load(f))

indices = list(range(len(data)))[PART::10]

POSSIBLE_INSTRUCTIONS = [
    "Fix imports",
    "Fix import statements.",
    "Check and fix imports.",
    "Adjust import statements.",
    "Review and correct imports.",
    "Update imports.",
    "Ensure imports are correct.",
    "Refine imports.",
    "Correct import errors.",
    "Address import issues.",
    "Streamline imports.",
    "Revisit import section.",
    "Repair import declarations.",
    "Solve import problems",
    "Tidy up imports.",
]


RETRIEVER_CONFIG = {
    "scorer": {
        "name": "ethanol",
        "checkpoint_path": "ethanol/ethanol6-16.1",
    },
    "chunker": {
        "name": "line_level",
        "max_lines_per_chunk": 30,
    },
    "query_formatter": {
        "name": "ethanol6_query",
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "prefix_ratio": 0.9,
    },
    "document_formatter": {
        "name": "ethanol6_document",
        "add_path": True,
        "max_tokens": 999,
    },
}


RETRIEVER = create_retriever(RETRIEVER_CONFIG)
RETRIEVER.load()

TOKEN_APPORTIONMENT = {
    "path_len": 256,
    "instruction_len": 512,
    "prefix_len": 1536,
    "selected_code_len": 4096,
    "suffix_len": 1024,
    "max_prompt_tokens": 16384 - 4096,  # 4096 represents the max output tokens
}
SEQUENCE_LENGTH = 16384

PROMPT_FORMATTER = get_prompt_formatter("droid", **TOKEN_APPORTIONMENT)


def tokenize(sample):
    cur_docs = [
        Document(id=d["id"], text=d["text"], path=d["path"]) for d in sample["docs"]
    ]
    RETRIEVER.add_docs(cur_docs)

    query_input = ModelInput(
        prefix=sample["prefix"],
        suffix=sample["suffix"],
        path=sample["path"],
    )
    retrieved_chunks, _ = RETRIEVER.query(query_input, top_k=128)

    model_input = ModelInput(
        prefix=sample["prefix"],
        suffix=sample["suffix"],
        path=sample["path"],
        retrieved_chunks=retrieved_chunks,
        extra={
            "instruction": "Fix imports",
            "selected_code": sample["selected_code"],
            "prefix_begin": 0,
            "suffix_end": len(
                sample["prefix"] + sample["selected_code"] + sample["suffix"]
            ),
        },
    )

    tokenized_input, _ = PROMPT_FORMATTER.prepare_prompt(model_input)
    tokenized_output = PROMPT_FORMATTER.tokenizer.tokenize(
        sample["updated_code"] + "\n```\n"
    ) + [PROMPT_FORMATTER.tokenizer.eod_id]
    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output
    complete_prompt += [-1 * PROMPT_FORMATTER.tokenizer.eod_id] * (
        SEQUENCE_LENGTH - len(complete_prompt) + 1
    )  # +1 to make total prompt of length SEQUENCE_LENGTH + 1

    return complete_prompt


for indx in tqdm(indices):
    t = tokenize(data[indx])
    with open(OUTPUT_DIR_PATH / f"{indx}.json", "w") as f:
        json.dump(t, f)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "# os.environ[\"OPENAI_API_KEY\"] = \"\"\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "\n", "os.environ[\"PYTHONPATH\"] = \"/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "import random\n", "import difflib\n", "\n", "from pathlib import Path\n", "from experimental.yuri.pr_edits.codereviewer2train_v2 import read_cr_data, process_cr_sample, get_lines\n", "from research.data.synthetic_code_edit.util_lib import get_unified_diff\n", "from collections import Counter\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from dataclasses import asdict\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from tqdm import tqdm\n", "from research.eval.harness.factories import create_retriever\n", "from research.core.types import Document\n", "from research.core.model_input import ModelInput\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "\n", "OUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/test_mar27_v2\")\n", "CODEREVIEWER_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/Code_Refinement/ref-train.jsonl\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "def get_diff_html(input_code, output_code, comment, another_header=None):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><strong>{comment}</strong></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Comment: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    if another_header is not None:\n", "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n", "    return html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT = \"\"\"\n", "Here is a file content:\n", "```\n", "{code}\n", "```\n", "\n", "Please, mess up imports in the \"import\" zone. You can delete some of them, replace with unused, introduce typos, etc.\n", "You should introduce strictly {N} change(s) in the import zone.\n", "\n", "Print the original import zone, messed up import zone and list of changes that you've made.\n", "When printing the messed up import zone, PLEASE, do NOT add any new comments to it!!!\n", "\"\"\"\n", "\n", "EXTRACT_PROMPT = \"\"\"Return result as a JSON with 3 keys:\n", "- original_imports\n", "- messed_up_imports\n", "- list of changes\n", "\"\"\"\n", "\n", "\n", "def extract_python_code_blocks(text):\n", "    pattern = r\"```python(.*?)```\"\n", "    \n", "    matches = re.findall(pattern, text, re.DOTALL)\n", "    \n", "    if len(matches) == 2:\n", "        return matches[0].strip(), matches[1].strip()\n", "    else:\n", "        assert False, text\n", "    \n", "\n", "def convert_doc(doc):\n", "    return {\n", "        \"id\": doc[\"hexsha\"],\n", "        \"text\": doc[\"content\"],\n", "        \"path\": doc[\"max_stars_repo_path\"],\n", "    }\n", "\n", "\n", "def handle_sample(cr_sample, gpt, N):\n", "    cr_sample_pp = process_cr_sample(cr_sample)\n", "    assert len(cr_sample_pp[\"samples\"]) == 1\n", "    sample = json.loads(cr_sample_pp[\"samples\"][0])\n", "    docs = list(map(convert_doc, json.loads(cr_sample_pp[\"file_list\"])))\n", "\n", "    full_text = sample[\"prefix\"] + sample[\"old_middle\"] + sample[\"suffix\"]\n", "\n", "    messages = [{\"role\": \"user\", \"content\": PROMPT.format(code=full_text, N=N)}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=1)\n", "\n", "    updated_code, selected_code = extract_python_code_blocks(gpt4_response)\n", "    # print(\"Updated code:\\n\\n\")\n", "    # print(updated_code)\n", "    # print(\"=\" * 20)\n", "    # print(\"Selected code:\\n\\n\")\n", "    # print(selected_code)\n", "\n", "\n", "    start_idx = full_text.find(updated_code)\n", "    last_idx = start_idx + len(updated_code)\n", "\n", "    full_input_text = full_text[:start_idx] + selected_code + full_text[last_idx:]\n", "    full_output_text = full_text[:start_idx] + updated_code + full_text[last_idx:]\n", "\n", "    print(get_unified_diff(full_input_text, full_output_text))\n", "\n", "\n", "    full_input_lines = full_input_text.splitlines(True)\n", "    full_output_lines = full_output_text.splitlines(True)\n", "\n", "    full_input_lines_r = [*reversed(full_input_lines)]\n", "    full_output_lines_r = [*reversed(full_output_lines)]\n", "\n", "    prefix_lines = []\n", "    for i in range(min(len(full_input_lines), len(full_output_lines))):\n", "        if full_input_lines[i] != full_output_lines[i]:\n", "            break\n", "        prefix_lines.append(full_input_lines[i])\n", "\n", "    suffix_lines = []\n", "    for i in range(min(len(full_input_lines_r), len(full_output_lines_r))):\n", "        if full_input_lines_r[i] != full_output_lines_r[i]:\n", "            break\n", "        suffix_lines.append(full_input_lines_r[i])\n", "    suffix_lines = [*reversed(suffix_lines)]\n", "\n", "    selected_lines = full_input_lines[len(prefix_lines) : len(full_input_lines) - len(suffix_lines)]\n", "    updated_lines = full_output_lines[len(prefix_lines) : len(full_input_lines) - len(suffix_lines)]\n", "\n", "    borrow_lines_from_prefix = min(random.randint(0, 20), len(prefix_lines))\n", "    borrow_lines_from_suffix = min(random.randint(0, 20), len(suffix_lines))\n", "\n", "    if borrow_lines_from_prefix > 0:\n", "        prefix_lines, prefix_lines_borrowed = prefix_lines[:-borrow_lines_from_prefix], prefix_lines[-borrow_lines_from_prefix:]\n", "    else:\n", "        prefix_lines_borrowed = []\n", "\n", "    suffix_lines, suffix_lines_borrowed = suffix_lines[borrow_lines_from_suffix:], suffix_lines[:borrow_lines_from_suffix]\n", "\n", "    selected_lines = [*prefix_lines_borrowed, *selected_lines, *suffix_lines_borrowed]\n", "    updated_lines = [*prefix_lines_borrowed, *updated_lines, *suffix_lines_borrowed]\n", "\n", "    prefix = \"\".join(prefix_lines)\n", "    suffix = \"\".join(suffix_lines)\n", "    selected_code = \"\".join(selected_lines)\n", "    updated_code = \"\".join(updated_lines)\n", "\n", "    assert prefix + selected_code + suffix == full_input_text\n", "    assert prefix + updated_code + suffix == full_output_text\n", "\n", "\n", "    return {\"prefix\": prefix, \"selected_code\": selected_code, \"updated_code\": updated_code, \"suffix\": suffix, \"docs\": docs, \"path\": sample[\"path\"]}\n", "\n", "RETRIEVER_CONFIG = {\n", "    \"scorer\": {\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-16.1\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"ethanol6_query\",\n", "        \"add_path\": True,\n", "        \"add_suffix\": True,\n", "        \"max_tokens\": 1023,\n", "        \"prefix_ratio\": 0.9,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"ethanol6_document\",\n", "        \"add_path\": True,\n", "        \"max_tokens\": 999,\n", "    },\n", "}\n", "\n", "\n", "RETRIEVER = create_retriever(RETRIEVER_CONFIG)\n", "RETRIEVER.load()\n", "\n", "TOKEN_APPORTIONMENT = {\n", "    \"path_len\": 256,\n", "    \"instruction_len\": 512,\n", "    \"prefix_len\": 1536,\n", "    \"selected_code_len\": 4096,\n", "    \"suffix_len\": 1024,\n", "    \"max_prompt_tokens\": 16384 - 4096,  # 4096 represents the max output tokens\n", "}\n", "SEQUENCE_LENGTH = 16384\n", "\n", "PROMPT_FORMATTER = get_prompt_formatter(\"droid\", **TOKEN_APPORTIONMENT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = read_cr_data(CODEREVIEWER_DATA_PATH)\n", "print(Counter(map(lambda s: s[\"lang\"], data)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_language = list(filter(lambda s: s[\"lang\"] == \"py\", data))\n", "len(data_language)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cr_sample = data_language[13]\n", "cr_sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prepped_samples_1 = []\n", "prepped_samples_3 = []\n", "prepped_samples_5 = []\n", "\n", "NUM_PER_CAT = 10\n", "\n", "for cr_sample in tqdm(data_language):\n", "    print(\"&\" * 30)\n", "    print(f\"Current length: {len(prepped_samples_1)}, {len(prepped_samples_3)}, {len(prepped_samples_5)}\")\n", "    if len(prepped_samples_1) < NUM_PER_CAT:\n", "        cur_N = 1\n", "        cur_prepped = prepped_samples_1\n", "    elif len(prepped_samples_3) < NUM_PER_CAT:\n", "        cur_N = 3\n", "        cur_prepped = prepped_samples_3\n", "    elif len(prepped_samples_5) < NUM_PER_CAT:\n", "        cur_N = 5\n", "        cur_prepped = prepped_samples_5\n", "    else:\n", "        break       \n", "    try:\n", "        output = handle_sample(cr_sample, gpt, cur_N)\n", "    except Exception as e:\n", "        print(e)\n", "        continue\n", "    cur_prepped.append(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_prepped_samples = prepped_samples_1 + prepped_samples_3 + prepped_samples_5\n", "len(total_prepped_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/mnt/efs/augment/user/yuri/tmp/fix_imports_test_mar28_v2.json\", \"w\") as f:\n", "    json.dump(total_prepped_samples, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/mnt/efs/augment/user/yuri/tmp/fix_imports_test_mar28_v2.json\", \"r\") as f:\n", "    total_prepped_samples = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GOOD_INDICES = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "for sample_id, sample in enumerate(total_prepped_samples):\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {sample_id}</h2><hr>\"\n", "\n", "    cur_diff = get_diff_html(\n", "        sample[\"selected_code\"],\n", "        sample[\"updated_code\"],\n", "        \"\",c \n", "        \"\",\n", "    )\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_imports_mar28_v4.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cur_sample = prepped_samples_1[0]\n", "\n", "\n", "cur_docs = [Document(id=d[\"id\"], text=d[\"text\"], path=d[\"path\"]) for d in prepped_samples_1[0][\"docs\"]]\n", "RETRIEVER.add_docs(cur_docs)\n", "\n", "query_input = ModelInput(\n", "    prefix=cur_sample[\"prefix\"],\n", "    suffix=cur_sample[\"suffix\"],\n", "    path=cur_sample[\"path\"],\n", ")\n", "retrieved_chunks, _ = RETRIEVER.query(query_input, top_k=128)\n", "\n", "\n", "model_input = ModelInput(\n", "    prefix=cur_sample[\"prefix\"],\n", "    suffix=cur_sample[\"suffix\"],\n", "    path=cur_sample[\"path\"],\n", "    retrieved_chunks=retrieved_chunks,\n", "    extra={\n", "        \"instruction\": \"Fix imports\",\n", "        \"selected_code\": cur_sample[\"selected_code\"],\n", "        \"prefix_begin\": 0,\n", "        \"suffix_end\": len(cur_sample[\"prefix\"] + cur_sample[\"selected_code\"] + cur_sample[\"suffix\"]),\n", "    }\n", ")\n", "\n", "tokenized_input, _ = PROMPT_FORMATTER.prepare_prompt(model_input)\n", "tokenized_output = PROMPT_FORMATTER.tokenizer.tokenize(cur_sample[\"updated_code\"] + \"\\n```\\n\") + [PROMPT_FORMATTER.tokenizer.eod_id]\n", "complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output\n", "complete_prompt += [-1 * PROMPT_FORMATTER.tokenizer.eod_id] * (SEQUENCE_LENGTH - len(complete_prompt) + 1)  # +1 to make total prompt of length SEQUENCE_LENGTH + 1\n", "\n", "# print(prompt_formatter.tokenizer.detokenize(list(map(abs, complete_prompt))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(cur_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
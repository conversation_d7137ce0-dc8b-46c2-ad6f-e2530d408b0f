import argparse
import json
import random
import re
from collections import Counter
from multiprocessing import Pool
from pathlib import Path

from tqdm import tqdm

from experimental.yuri.pr_edits.codereviewer2train_v2 import (
    process_cr_sample,
    read_cr_data,
)
from research.data.synthetic_code_edit.api_lib import GptWrapper
from research.data.synthetic_code_edit.util_lib import get_unified_diff

PROMPT = """
Here is a file content:
```
{code}
```

Please, remove some of the imports from the import zone. You should remove {N} imports.

Print the original import zone, updated import zone and list of changes that you've made.
When printing the updated import zone (with removed imports), PLEASE, do NOT add any new comments to it!!!
"""


def extract_python_code_blocks(text):
    pattern = r"```python(.*?)```"

    matches = re.findall(pattern, text, re.DOTALL)

    if len(matches) == 2:
        return matches[0].strip(), matches[1].strip()
    else:
        raise AssertionError(text)


def convert_doc(doc):
    return {
        "id": doc["hexsha"],
        "text": doc["content"],
        "path": doc["max_stars_repo_path"],
    }


def handle_sample(cr_sample, gpt, N, empty_selection=False):
    cr_sample_pp = process_cr_sample(cr_sample)
    assert len(cr_sample_pp["samples"]) == 1
    sample = json.loads(cr_sample_pp["samples"][0])
    docs = list(map(convert_doc, json.loads(cr_sample_pp["file_list"])))

    full_text = sample["prefix"] + sample["new_middle"] + sample["suffix"]

    messages = [{"role": "user", "content": PROMPT.format(code=full_text, N=N)}]
    gpt4_response = gpt(messages, model="gpt-4-1106-preview", temperature=1)

    updated_code, selected_code = extract_python_code_blocks(gpt4_response)
    if len(updated_code.splitlines(True)) > 100:
        print("Code is too long, skipping...")
        raise AssertionError()
    # print("Updated code:\n\n")
    # print(updated_code)
    # print("=" * 20)
    # print("Selected code:\n\n")
    # print(selected_code)

    # We just drop some contiguous chunk of imports
    if empty_selection:
        selected_code = updated_code
        total_lines = len(updated_code.splitlines(True))
        first_line = random.randint(0, total_lines)
        num_lines = random.randint(1, 5)
        num_lines = min(num_lines, total_lines - first_line)
        _lines = updated_code.splitlines(True)
        selected_code = _lines[:first_line] + _lines[first_line + num_lines :]
        selected_code = "".join(selected_code)

    start_idx = full_text.find(updated_code)
    last_idx = start_idx + len(updated_code)

    full_input_text = full_text[:start_idx] + selected_code + full_text[last_idx:]
    full_output_text = full_text[:start_idx] + updated_code + full_text[last_idx:]

    print(get_unified_diff(full_input_text, full_output_text))

    full_input_lines = full_input_text.splitlines(True)
    full_output_lines = full_output_text.splitlines(True)

    full_input_lines_r = [*reversed(full_input_lines)]
    full_output_lines_r = [*reversed(full_output_lines)]

    prefix_lines = []
    for i in range(min(len(full_input_lines), len(full_output_lines))):
        if full_input_lines[i] != full_output_lines[i]:
            break
        prefix_lines.append(full_input_lines[i])

    suffix_lines = []
    for i in range(min(len(full_input_lines_r), len(full_output_lines_r))):
        if full_input_lines_r[i] != full_output_lines_r[i]:
            break
        suffix_lines.append(full_input_lines_r[i])
    suffix_lines = [*reversed(suffix_lines)]

    selected_lines = full_input_lines[
        len(prefix_lines) : len(full_input_lines) - len(suffix_lines)
    ]
    updated_lines = full_output_lines[
        len(prefix_lines) : len(full_output_lines) - len(suffix_lines)
    ]

    borrow_lines_from_prefix = min(random.randint(0, 20), len(prefix_lines))
    borrow_lines_from_suffix = min(random.randint(0, 20), len(suffix_lines))

    if empty_selection:
        borrow_lines_from_prefix = borrow_lines_from_suffix = 0

    if borrow_lines_from_prefix > 0:
        prefix_lines, prefix_lines_borrowed = (
            prefix_lines[:-borrow_lines_from_prefix],
            prefix_lines[-borrow_lines_from_prefix:],
        )
    else:
        prefix_lines_borrowed = []

    suffix_lines, suffix_lines_borrowed = (
        suffix_lines[borrow_lines_from_suffix:],
        suffix_lines[:borrow_lines_from_suffix],
    )

    selected_lines = [*prefix_lines_borrowed, *selected_lines, *suffix_lines_borrowed]
    updated_lines = [*prefix_lines_borrowed, *updated_lines, *suffix_lines_borrowed]

    prefix = "".join(prefix_lines)
    suffix = "".join(suffix_lines)
    selected_code = "".join(selected_lines)
    updated_code = "".join(updated_lines)

    assert prefix + selected_code + suffix == full_input_text
    assert prefix + updated_code + suffix == full_output_text

    return {
        "prefix": prefix,
        "selected_code": selected_code,
        "updated_code": updated_code,
        "suffix": suffix,
        "docs": docs,
        "path": sample["path"],
        "gpt4_response": gpt4_response,
        "N": N,
    }


def handle_sample_wrapper(task):
    sample, gpt, output_dir, index = task
    random_x = random.random()  # * 0.1 # * 0.2 + 0.1

    try:
        N = random.randint(1, 5)
        if 0.1 <= random_x < 0.3:  # For empty_selection we want no expansion
            empty_selection = True
        else:
            empty_selection = False
        output = handle_sample(sample, gpt, N, empty_selection)
    except Exception as e:
        print(f"Failed to process sample {index}: {e}")
        return

    if random_x < 0.1:  # Cases when no updates needed
        output["selected_code"] = output["updated_code"]
        output["type"] = "no_update"
    elif random_x < 0.3:  # Cases when we generate without selecting any code
        output["selected_code"] = ""
        output["type"] = "empty_selection"
    else:
        output["type"] = "full_selection"

    with open(output_dir / f"{index}.json", "w") as f:
        json.dump(output, f, indent=2)


def main(input_file, output_dir, num_processes, num_samples):
    output_dir.mkdir(parents=False, exist_ok=True)

    data = read_cr_data(input_file)
    print(Counter(map(lambda s: s["lang"], data)))

    data_language = list(filter(lambda s: s["lang"] == "py", data))

    gpt = GptWrapper()
    tasks = []
    for i, sample in enumerate(data_language[:num_samples]):
        tasks.append((sample, gpt, output_dir, i))

    if num_processes <= 1:
        for task in tqdm(tasks):
            handle_sample_wrapper(task)
    else:
        with Pool(num_processes) as pool:
            _ = list(tqdm(pool.imap(handle_sample_wrapper, tasks), total=len(tasks)))


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for generating code edits synthetic data.",
    )
    parser.add_argument(
        "--input_file",
        "-i",
        type=Path,
        required=True,
        help="Input file with CR samples",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory for saving the data",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        required=True,
        help="Number of samples to generate",
    )
    args = parser.parse_args()

    return args


if __name__ == "__main__":
    xargs = parse_args()
    main(
        input_file=xargs.input_file,
        output_dir=xargs.output_dir,
        num_processes=xargs.num_processes,
        num_samples=xargs.num_samples,
    )

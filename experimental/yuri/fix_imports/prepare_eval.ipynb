{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from pathlib import Path\n", "from experimental.yuri.pr_edits.codereviewer2train_v2 import read_cr_data, process_cr_sample, get_lines\n", "from research.data.synthetic_code_edit.util_lib import get_unified_diff\n", "from collections import Counter\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from dataclasses import asdict\n", "\n", "OUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/test_mar27_v2\")\n", "CODEREVIEWER_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/Code_Refinement/ref-valid.jsonl\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def convert_doc(doc):\n", "    return {\n", "        \"id\": doc[\"hexsha\"],\n", "        \"text\": doc[\"content\"],\n", "        \"path\": doc[\"max_stars_repo_path\"],\n", "    }\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Reading data: 13103it [00:01, 10874.33it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Counter({'go': 2865, 'py': 2834, 'java': 2177, 'cpp': 1362, 'js': 1128, 'php': 973, '.cs': 717, 'c': 543, 'rb': 504})\n"]}], "source": ["data = read_cr_data(CODEREVIEWER_DATA_PATH)\n", "print(Counter(map(lambda s: s[\"lang\"], data)))\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["2834"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data_language = list(filter(lambda s: s[\"lang\"] == \"py\", data))\n", "len(data_language)"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'old_hunk': \"@@ -21,10 +21,14 @@\\n \\n import os\\n import re\\n-import azurelinuxagent.logger as logger\\n+import azurelinuxagent.common.logger as logger\\n+import azurelinuxagent.common.utils.shellutil as shellutil\\n \\n \\n class RDMAHandler(object):\\n+    def __init__(self):\\n+        self.driver_module_name = 'hv_network_direct'\",\n", " 'oldf': '# Windows Azure Linux Agent\\n#\\n# Copyright 2016 Microsoft Corporation\\n#\\n# Licensed under the Apache License, Version 2.0 (the \"License\");\\n# you may not use this file except in compliance with the License.\\n# You may obtain a copy of the License at\\n#\\n#     http://www.apache.org/licenses/LICENSE-2.0\\n#\\n# Unless required by applicable law or agreed to in writing, software\\n# distributed under the License is distributed on an \"AS IS\" BASIS,\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n# See the License for the specific language governing permissions and\\n# limitations under the License.\\n#\\n\\n\"\"\"\\nHandle packages and modules to enable RDMA for IB networking\\n\"\"\"\\n\\nimport os\\nimport re\\nimport azurelinuxagent.common.logger as logger\\nimport azurelinuxagent.common.utils.shellutil as shellutil\\n\\n\\nclass RDMAHandler(object):\\n    def __init__(self):\\n        self.driver_module_name = \\'hv_network_direct\\'\\n\\n    def __get_rdma_version(self):\\n        \"\"\"Retrieve the firmware version information from the system.\\n           This depends on information provided by the Linux kernel.\"\"\"\\n\\n        driver_info_source = \\'/var/lib/hyperv/.kvp_pool_0\\'\\n        base_kernel_err_msg = \\'Kernel does not provide the necessary \\'\\n        base_kernel_err_msg += \\'information or the hv_kvp_daemon is not \\'\\n        base_kernel_err_msg += \\'running.\\'\\n        if not os.path.isfile(driver_info_source):\\n            error_msg = \\'Source file \"%s\" does not exist. \\'\\n            error_msg += base_kernel_err_msg\\n            logger.error(error_msg % driver_info_source)\\n            return\\n\\n        lines = open(driver_info_source).read()\\n        if not lines:\\n            error_msg = \\'Source file \"%s\" is empty. \\'\\n            error_msg += base_kernel_err_msg\\n            logger.error(error_msg % driver_info_source)\\n            return\\n\\n        r = re.search(\"NdDriverVersion\\\\0+(\\\\d\\\\d\\\\d\\\\.\\\\d)\", lines)\\n        if r:\\n            NdDriverVersion = r.groups()[0]\\n            return NdDriverVersion\\n        else:\\n            error_msg = \\'NdDriverVersion not found in \"%s\"\\'\\n            logger.error(error_msg % driver_info_source)\\n            return\\n\\n    def load_driver_module(self):\\n        \"\"\"Load the kernel driver, this depends on the proper driver\\n           to be installed with the install_driver() method\"\"\"\\n        result = shellutil.run(\\'modprobe %s\\' % self.driver_module_name)\\n        if result != 0:\\n            error_msg = \\'Could not load \"%s\" kernel module. \\'\\n            error_msg += \\'Run \"modprobe %s\" as root for more details\\'\\n            logger.error(\\n                error_msg % (driver_module_name, self.driver_module_name)\\n            )\\n            return\\n\\n        return True\\n\\n    def install_driver(self):\\n        \"\"\"Install the driver. This is distribution specific and must\\n           be overwritten in the child implementation.\"\"\"\\n\\n        raise Exception(\\'RDMAHandler.install_driver not implemented\\')\\n\\n    def remove_driver_module(self):\\n        \"\"\"Force remove the kernel driver\"\"\"\\n        # Force remove the loaded driver\\n        # Potential failure if the module is currently not loaded is ignored\\n        shellutil.run(\\'rmmod --force %s\\' % self.driver_module_name)\\n',\n", " 'hunk': '@@ -26,8 +26,8 @@ import azurelinuxagent.common.utils.shellutil as shellutil\\n \\n \\n class RDMAHandler(object):\\n-    def __init__(self):\\n-        self.driver_module_name = \\'hv_network_direct\\'\\n+\\n+    driver_module_name = \\'hv_network_direct\\'\\n \\n     def __get_rdma_version(self):\\n         \"\"\"Retrieve the firmware version information from the system.\\n',\n", " 'comment': 'can we declare this just under `class RDMAHander`',\n", " 'ids': [10612,\n", "  '5e90929ae911ba937f96e60ff00b90936db41afa',\n", "  'c74b712dd29fe46070acaf45cd55cad2a046bf2b'],\n", " 'repo': 'Azure/WALinuxAgent',\n", " 'ghid': 239,\n", " 'old': ' class RDMAHandler(object):\\n-    def __init__(self):\\n-        self.driver_module_name = \\'hv_network_direct\\'\\n     def __get_rdma_version(self):\\n         \"\"\"Retrieve the firmware version information from the system.',\n", " 'new': ' class RDMAHandler(object):\\n+\\n+    driver_module_name = \\'hv_network_direct\\'\\n     def __get_rdma_version(self):\\n         \"\"\"Retrieve the firmware version information from the system.',\n", " 'lang': 'py'}"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["cr_sample = data_language[13]\n", "cr_sample"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DIDN'T FIND CLONED: Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa. Cloning it.\n", "upload: '/tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa.tar' -> 's3://yuri-dev-bucket/tmp/repo_cache/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa.tar' (4229120 bytes in 0.2 seconds, 19.61 MB/s) [1 of 1]\n", "upload: 'emptyfile' -> 's3://yuri-dev-bucket/tmp/repo_cache/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa_FULLY_CLONED_MARKER' (0 bytes in 0.2 seconds, -1.00 B/s) [1 of 1]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Searching for matching files: 267it [00:00, 26962.95it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/.git/index with error 'utf-8' codec can't decode byte 0xaa in position 11: invalid start byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/.git/objects/pack/pack-4d6c3847c02b8f4ded3fd35aadce7b284bb113c7.pack with error 'utf-8' codec can't decode bytes in position 11-12: invalid continuation byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/.git/objects/pack/pack-4d6c3847c02b8f4ded3fd35aadce7b284bb113c7.rev with error 'utf-8' codec can't decode byte 0x8a in position 27: invalid start byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/.git/objects/pack/pack-4d6c3847c02b8f4ded3fd35aadce7b284bb113c7.idx with error 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/.git/objects/pack/pack-4da9ee80f98abb51956d1667d84191f2447fea85.pack with error 'utf-8' codec can't decode bytes in position 11-12: invalid continuation byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/.git/objects/pack/pack-4da9ee80f98abb51956d1667d84191f2447fea85.rev with error 'utf-8' codec can't decode byte 0xbc in position 19: invalid start byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/.git/objects/pack/pack-4da9ee80f98abb51956d1667d84191f2447fea85.idx with error 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/tests/data/dhcp with error 'utf-8' codec can't decode byte 0xd1 in position 6: invalid continuation byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/tests/data/ext/sample_ext.zip with error 'utf-8' codec can't decode byte 0x9f in position 12: invalid start byte. Skipping...\n", "Failed on file /tmp/Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa/tests/data/ga/WALinuxAgent-2.1.5.rc0.zip with error 'utf-8' codec can't decode byte 0xfb in position 17: invalid start byte. Skipping...\n", "Processed 267 total docs, skipping 74 of them (27%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["cr_sample_pp = process_cr_sample(cr_sample)\n", "\n", "assert len(cr_sample_pp[\"samples\"]) == 1\n", "\n", "sample = json.loads(cr_sample_pp[\"samples\"][0])\n", "file_list = json.loads(cr_sample_pp[\"file_list\"])\n", "\n", "file_list = list(map(convert_doc, file_list))\n", "\n", "full_text = sample[\"prefix\"] + sample[\"old_middle\"] + sample[\"suffix\"]\n", "\n"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Windows Azure Linux Agent\n", "#\n", "# Copyright 2016 Microsoft Corporation\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n", "#\n", "\n", "\"\"\"\n", "Handle packages and modules to enable RDMA for IB networking\n", "\"\"\"\n", "\n", "import os\n", "import re\n", "import azurelinuxagent.common.logger as logger\n", "import azurelinuxagent.common.utils.shellutil as shellutil\n", "\n", "\n", "class RDMAH<PERSON>ler(object):\n", "    def __init__(self):\n", "        self.driver_module_name = 'hv_network_direct'\n", "\n", "    def __get_rdma_version(self):\n", "        \"\"\"Retrieve the firmware version information from the system.\n", "           This depends on information provided by the Linux kernel.\"\"\"\n", "\n", "        driver_info_source = '/var/lib/hyperv/.kvp_pool_0'\n", "        base_kernel_err_msg = 'Kernel does not provide the necessary '\n", "        base_kernel_err_msg += 'information or the hv_kvp_daemon is not '\n", "        base_kernel_err_msg += 'running.'\n", "        if not os.path.isfile(driver_info_source):\n", "            error_msg = 'Source file \"%s\" does not exist. '\n", "            error_msg += base_kernel_err_msg\n", "            logger.error(error_msg % driver_info_source)\n", "            return\n", "\n", "        lines = open(driver_info_source).read()\n", "        if not lines:\n", "            error_msg = 'Source file \"%s\" is empty. '\n", "            error_msg += base_kernel_err_msg\n", "            logger.error(error_msg % driver_info_source)\n", "            return\n", "\n", "        r = re.search(\"NdDriverVersion\\0+(\\d\\d\\d\\.\\d)\", lines)\n", "        if r:\n", "            NdDriverVersion = r.groups()[0]\n", "            return NdDriverVersion\n", "        else:\n", "            error_msg = 'NdDriverVersion not found in \"%s\"'\n", "            logger.error(error_msg % driver_info_source)\n", "            return\n", "\n", "    def load_driver_module(self):\n", "        \"\"\"Load the kernel driver, this depends on the proper driver\n", "           to be installed with the install_driver() method\"\"\"\n", "        result = shellutil.run('modprobe %s' % self.driver_module_name)\n", "        if result != 0:\n", "            error_msg = 'Could not load \"%s\" kernel module. '\n", "            error_msg += 'Run \"modprobe %s\" as root for more details'\n", "            logger.error(\n", "                error_msg % (driver_module_name, self.driver_module_name)\n", "            )\n", "            return\n", "\n", "        return True\n", "\n", "    def install_driver(self):\n", "        \"\"\"Install the driver. This is distribution specific and must\n", "           be overwritten in the child implementation.\"\"\"\n", "\n", "        raise Exception('RDMAHandler.install_driver not implemented')\n", "\n", "    def remove_driver_module(self):\n", "        \"\"\"Force remove the kernel driver\"\"\"\n", "        # Force remove the loaded driver\n", "        # Potential failure if the module is currently not loaded is ignored\n", "        shellutil.run('rmmod --force %s' % self.driver_module_name)\n", "\n"]}], "source": ["print(full_text)"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [], "source": ["r = [0, 4]\n", "\n", "prefix = get_lines(full_text, 0, r[0])\n", "updated_code = get_lines(full_text, r[0], r[1])\n", "suffix = get_lines(full_text, r[1], int(1e9))"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import datetime as dt\n", "\n", "import numpy as np\n", "\n", "\n"]}], "source": ["print(updated_code)"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [], "source": ["selected_code = \"\"\"\"\"\""]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- \n", "\n", "+++ \n", "\n", "@@ -0,0 +1,4 @@\n", "\n", "+import datetime as dt\n", "+\n", "+import numpy as np\n", "+\n"]}], "source": ["print(get_unified_diff(selected_code, updated_code))"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["holoviz__holoviews___c9113d4ba2a99dbb8dbb99d4f2716254f72312f7 holoviz__holoviews___c9113d4ba2a99dbb8dbb99d4f2716254f72312f7___24719\n"]}], "source": ["repo_id = f\"{cr_sample['repo'].replace('/', '__')}___{cr_sample['ids'][1]}\"\n", "sample_id = f\"{repo_id}___{cr_sample['ids'][0]}\"  # Not sure what ['ids'][0] denotes, but it is unique across dataset\n", "\n", "print(repo_id, sample_id)\n", "\n", "\n", "code_edit_sample = ResearchEditPromptInput(\n", "    path=sample[\"path\"],\n", "    prefix=prefix,\n", "    selected_code=selected_code,\n", "    suffix=suffix,\n", "    instruction=\"Fix imports\",\n", "    retrieved_chunks=[],\n", "    updated_code=updated_code,\n", "    extra={\n", "        \"repo_url\": repo_id\n", "    },\n", ")\n", "\n", "docs_as_dict = {\n", "    \"repo_url\": repo_id,\n", "    \"docs\": file_list\n", "}\n", "\n", "OUT_DIR.mkdir(exist_ok=True)\n", "\n", "examples_dir = OUT_DIR / \"examples\"\n", "repos_dir = OUT_DIR / \"repos\"\n", "\n", "examples_dir.mkdir(exist_ok=True)\n", "repos_dir.mkdir(exist_ok=True)\n", "\n", "repo_path = repos_dir / f\"{repo_id}.json\"\n", "sample_path = examples_dir / f\"{sample_id}.json\"\n", "\n", "assert not sample_path.exists()\n", "\n", "with sample_path.open(\"w\") as f:\n", "    json.dump(asdict(code_edit_sample), f, indent=2)\n", "\n", "if repo_path.exists():\n", "    print(f\"Reusing {repo_id}\")\n", "else:\n", "    with repo_path.open(\"w\") as f:\n", "        json.dump(docs_as_dict, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"text/plain": ["PosixPath('/mnt/efs/augment/user/yuri/tmp/test_mar27_v2')"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["OUT_DIR"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
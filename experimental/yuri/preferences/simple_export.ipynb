{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PROJECT_ID = \"system-services-dev\"\n", "# DATASET_NAME = \"dev_request_insight_full_export_dataset\"\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"staging_request_insight_full_export_dataset\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def build_query(\n", "    project_id,\n", "    dataset_name,\n", "):\n", "    return f\"\"\"\n", "SELECT *\n", "FROM {project_id}.{dataset_name}.request_event\n", "WHERE event_type=\"preference_sample\"\n", "ORDER BY time DESC\n", "LIMIT 100\n", "\"\"\"\n", "\n", "# WHERE event_type=\"edit_host_request\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = build_query(\n", "    project_id=PROJECT_ID,\n", "    dataset_name=DATASET_NAME,\n", ")\n", "\n", "print(query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = bigquery.Client(project=PROJECT_ID)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rows = [*client.query_and_wait(query)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(rows)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rows[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"prod_request_insight_full_export_dataset\"\n", "\n", "# TENANT_NAME = \"aitutor-mercor\"\n", "\n", "TURING_BUCKET_NAME = \"augment_ai\"\n", "\n", "TENANT_NAME = \"aitutor-turing\"\n", "TURING_PATH = \"/home/<USER>/augment/experimental/tamuz/preferences/turing_data\"\n", "TURING_PREFIX = \"to_augment_ai\"\n", "START_DAY = \"2024-07-15\" # Must be in date format ex: YYYY-MM-DD\n", "HISTORY_LIMIT = 10"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "from pathlib import Path\n", "from collections import Counter\n", "from google.cloud import bigquery\n", "from markdown2 import markdown as _markdown2\n", "from markdown import markdown as _markdown1\n", "from tqdm import tqdm\n", "import pandas as pd\n", "from copy import deepcopy\n", "import os\n", "import zipfile\n", "import datetime\n", "from google.cloud.storage import Client, transfer_manager"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["def markdown2(text):\n", "    return _markdown2(text, extras=[\"fenced-code-blocks\", \"code-friendly\"], safe_mode=\"escape\")\n", "\n", "def markdown1(text):\n", "    return _markdown1(text, extensions=[\"fenced_code\", \"codehilite\"])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def get_preferences_samples():\n", "    query = f\"\"\"\n", "SELECT *\n", "FROM {PROJECT_ID}.{DATASET_NAME}.preference_sample\n", "WHERE tenant=\"{TENANT_NAME}\" \n", "AND day >= \"{START_DAY}\"\n", "ORDER BY time DESC\n", "LIMIT 1000000\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    rows = [*client.query_and_wait(query)]\n", "\n", "    clean_rows = []\n", "    for row in rows:\n", "        clean_rows.append({\n", "            \"preference_request_id\": row.request_id,\n", "            **dict(row)[\"raw_json\"]\n", "        })\n", "\n", "    return clean_rows\n", "\n", "\n", "\n", "def get_chat_sample(request_id, chat_dict):\n", "    if request_id in chat_dict:\n", "        return chat_dict[request_id]\n", "    else:\n", "        return None\n", "\n", "\n", "def get_chat_samples(request_ids):\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_metadata AS metadata\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\tAND metadata.day > \"{START_DAY}\"\n", "\tAND request.day > \"{START_DAY}\"\n", "\tAND response.day > \"{START_DAY}\"\n", "    AND metadata.tenant=\"{TENANT_NAME}\"\n", "\tAND request.tenant=\"{TENANT_NAME}\"\n", "\tAND response.tenant=\"{TENANT_NAME}\"\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "        \"request\": row.request[\"request\"],\n", "        \"response\": row.response[\"response\"],\n", "        \"metadata\": row.metadata,\n", "        \"datetime\": row.time,\n", "    }\n", "\n", "    return chat_rows_dic"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import html\n", "\n", "\n", "def rating_to_letter(rating):\n", "    if rating == -100:\n", "        return \"Empty\"\n", "\n", "    if rating == 0:\n", "        return \"=\"\n", "    elif rating < 0:\n", "        return f\"A{abs(rating)}\"\n", "    else:\n", "        return f\"B{rating}\"\n", "\n", "\n", "def render_sample(sample):\n", "    assert sample[\"option_a\"][\"request\"][\"message\"] == sample[\"option_b\"][\"request\"][\"message\"]\n", "\n", "    message = markdown2(sample[\"option_a\"][\"request\"][\"message\"])\n", "    answers = [\n", "        markdown2(sample[\"option_a\"][\"response\"][\"text\"]),\n", "        markdown2(sample[\"option_b\"][\"response\"][\"text\"]),\n", "    ]\n", "    feedback = {\n", "        \"formattingRating\": rating_to_letter(sample[\"scores\"][\"formattingRating\"]),\n", "        \"hallucinationRating\": rating_to_letter(sample[\"scores\"][\"hallucinationRating\"]),\n", "        \"instructionFollowingRating\": rating_to_letter(sample[\"scores\"][\"instructionFollowingRating\"]),\n", "        \"overallRating\": rating_to_letter(sample[\"scores\"][\"overallRating\"]),\n", "        \"isHighQuality\": \"Yes\" if sample[\"scores\"][\"isHighQuality\"] else \"No\",\n", "        \"feedback\": markdown1(sample.get(\"feedback\", \"\").replace(\"\\n\", \"<br>\")),\n", "    }\n", "\n", "    html_content = f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row feedback\">\n", "    <ul>\n", "\"\"\"\n", "\n", "    for key, value in feedback.items():\n", "        html_content += f\"<li><strong>{key}:</strong> {value}</li>\"\n", "\n", "    html_content += \"\"\"\n", "            </ul>\n", "        </div>\n", "        <div class=\"row\">\n", "    \"\"\"\n", "\n", "    for answer in answers:\n", "        html_content += f'<div class=\"answer\" style=\"flex: 1;\">{answer}</div>'\n", "\n", "    html_content += \"</div>\"\n", "\n", "    return html_content\n", "\n", "\n", "def render_simple_message(message, response):\n", "    message = markdown2(message)\n", "    response = markdown2(response)\n", "\n", "    html_content = f\"\"\"\n", "<div class=\"row feedback\">\n", "    <div>{message}</div>\n", "</div>\n", "<div class=\"row\">\n", "    <div class=\"answer\" style=\"flex: 1;\">{response}</div>\n", "</div>\n", "\"\"\"\n", "\n", "    return html_content\n", "\n", "def render_sample_with_history(sample):\n", "    cur_index = 0\n", "    html_content = \"\"\n", "    \n", "    if \"chat_history\" in sample[\"option_a\"][\"request\"]:\n", "        for exchange in sample[\"option_a\"][\"request\"][\"chat_history\"][-HISTORY_LIMIT:]:\n", "            html_content += f\"<h3>Message {cur_index}</h3>\"\n", "            cur_index += 1\n", "            html_content += render_simple_message(exchange[\"request_message\"], exchange[\"response_text\"])\n", "            html_content += \"<hr>\"\n", "\n", "        html_content = f\"\"\"\n", "        <details>\n", "            <summary>Chat history</summary>\n", "            {html_content}\n", "        </details>\n", "        \"\"\"\n", "\n", "    if len(sample[\"option_a\"][\"request\"].get(\"selected_code\", \"\")) > 0:\n", "        cur_selected_code = f\"\"\"```\n", "{sample[\"option_a\"][\"request\"][\"selected_code\"]}\n", "```\n", "\"\"\"\n", "        html_content += f\"\"\"\n", "        <details>\n", "            <summary>Selected code</summary>\n", "            <div class=\"row\">\n", "                <div class=\"answer\" style=\"flex: 1;\">{markdown2(cur_selected_code)}</div>\n", "            </div>\n", "        </details>\n", "        \"\"\"\n", "    else:\n", "        html_content += \"\"\"\n", "        <div>\n", "            <p>Selected code: N/A</p>\n", "        </div>\n", "\"\"\"\n", "\n", "    html_content += f\"<h3>Message {cur_index}</h3>\"\n", "\n", "\n", "\n", "    html_content += render_sample(sample)\n", "\n", "    return html_content\n", "\n", "def wrap_html(html_content):\n", "    start = \"\"\"\n", "    <html>\n", "    <head>\n", "        <title>Question and Answers</title>\n", "        <style>\n", "            .row {\n", "                display: flex;\n", "                justify-content: center;\n", "                align-items: center;\n", "                margin-bottom: 20px;\n", "            }\n", "            .feedback, .answer {\n", "                margin: 10px;\n", "                padding: 10px;\n", "                border: 1px solid #ddd;\n", "                border-radius: 5px;\n", "                background-color: #f9f9f9;\n", "            }\n", "        </style>\n", "    </head>\n", "    <body>\n", "\"\"\"\n", "\n", "    end = \"\"\"\n", "        </body>\n", "    </html>\"\"\"\n", "\n", "    return \"\\n\".join([start, html_content, end])\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["def download_turing_samples(workers=8, max_results=1000):\n", "    storage_client = Client()\n", "    bucket = storage_client.bucket(TURING_BUCKET_NAME)\n", "\n", "    # List all blobs in the bucket\n", "    blobs = list(bucket.list_blobs(prefix=TURING_PREFIX, max_results=max_results))\n", "\n", "    # Filter out blobs that already exist locally\n", "    blobs_to_download = []\n", "    for blob in blobs:\n", "        local_path = os.path.join(TURING_PATH, blob.name)\n", "        if not os.path.exists(local_path):\n", "            blobs_to_download.append(blob)\n", "\n", "    if not blobs_to_download:\n", "        print(\"No New Data\")\n", "        return\n", "\n", "    # Prepare the list of blob names to download\n", "    blob_names = [blob.name for blob in blobs_to_download if blob.name.endswith('.zip')]\n", "\n", "    # Download the files\n", "    results = transfer_manager.download_many_to_path(\n", "        bucket, blob_names, destination_directory=TURING_PATH, max_workers=workers\n", "    )\n", "\n", "    # Process the results and unzip files\n", "    for name, result in zip(blob_names, results):\n", "        if isinstance(result, Exception):\n", "            print(f\"Failed to download {name} due to exception: {result}\")\n", "        else:\n", "            local_path = os.path.join(TURING_PATH, name)\n", "            print(f\"Downloaded {name} to {local_path}\")\n", "\n", "            try:\n", "                with zipfile.ZipFile(local_path, 'r') as zip_ref:\n", "                    for zip_info in zip_ref.infolist():\n", "                        if zip_info.filename[-1] == '/':\n", "                            continue  # skip directories\n", "                        zip_info.filename = os.path.basename(zip_info.filename)  # remove directory structure\n", "                        zip_ref.extract(zip_info, TURING_PATH)\n", "                print(f\"Unzipped {name} to {TURING_PATH}\")\n", "            except Exception as e:\n", "                print(f\"Failed to unzip {name}: {str(e)}\")\n", "\n", "def get_turing_samples():\n", "\n", "    download_turing_samples()\n", "\n", "    request_ids = []\n", "    for file_path in Path(TURING_PATH).glob(\"*.json\"):\n", "        if file_path.name.startswith(\".\") or \"sample\" in file_path.name:\n", "            continue\n", "        with file_path.open() as f:\n", "            try:\n", "                sample = json.load(f)\n", "            except Exception as e:\n", "                print(f\"Failed to load {file_path}: {str(e)}\")\n", "                continue\n", "        if \"request_id\" in sample:\n", "            request_ids.append(sample[\"request_id\"])\n", "\n", "    # request_ids = [\n", "    #     \"d7a79b62-d771-4be3-ac08-bbfb59507ee7\",\n", "    #     \"c84edb3f-abb5-4137-9efe-35f13372be2c\",\n", "    #     \"2f4bfe7a-6782-498f-8284-386ea53e1798\",\n", "    #     \"3cc086a6-459f-4a24-9a1a-ed3be459b19a\",\n", "    #     \"a54c2864-1d7a-4a0b-a69d-4f9442641711\",\n", "    # ]\n", "\n", "    all_samples = get_preferences_samples()\n", "    result = list(\n", "        filter(\n", "            lambda sample: sample[\"preference_request_id\"] in request_ids,\n", "            all_samples,\n", "        )\n", "    )\n", "    print(f\"Total samples in BQ: {len(all_samples)}. Number of requests in Turing: {len(result)}. Final number of samples: {len(result)}\")\n", "    return result\n", "\n", "\n", "def get_turing_samples_local():\n", "    samples = []\n", "\n", "    download_turing_samples()\n", "    request_ids = []\n", "    for file_path in tqdm(Path(TURING_PATH).glob(\"*.json\")):\n", "        if file_path.name.startswith(\".\") or \"sample\" in file_path.name:\n", "            continue\n", "        with file_path.open() as f:\n", "            try:\n", "                sample = json.load(f)\n", "            except Exception as e:\n", "                print(f\"Failed to load {file_path}: {str(e)}\")\n", "                continue\n", "            if \"request_a\" not in sample:\n", "                continue\n", "            sample[\"option_a\"] = sample[\"request_a\"]\n", "            sample[\"option_b\"] = sample[\"request_b\"]\n", "\n", "            for e in sample[\"request_a\"][\"request\"][\"chat_history\"]:\n", "                e[\"request_message\"] = e[\"message\"]\n", "                e[\"response_text\"] = e[\"response\"]\n", "\n", "            for e in sample[\"request_b\"][\"request\"][\"chat_history\"]:\n", "                e[\"request_message\"] = e[\"message\"]\n", "                e[\"response_text\"] = e[\"response\"]\n", "\n", "            for k, v in sample[\"scores\"].items():\n", "                sample[\"scores\"][k] = int(v)\n", "        samples.append(sample)\n", "        request_ids.append(sample[\"option_a\"][\"request_id\"])\n", "    chat_dict = get_chat_samples(request_ids)\n", "    samples_in_time_range = []\n", "\n", "    for sample in samples:\n", "        pulled_chat_sample = get_chat_sample(sample[\"option_a\"][\"request_id\"], chat_dict)\n", "        if pulled_chat_sample:\n", "            sample[\"user_agent\"] = pulled_chat_sample[\"metadata\"][\"user_agent\"]\n", "            sample[\"option_a\"][\"datetime\"] = pulled_chat_sample[\"datetime\"]\n", "            samples_in_time_range.append(sample)\n", "    return samples_in_time_range\n", "\n", "\n", "\n", "def get_data_df(samples):\n", "    all_data = []\n", "    for sample in samples:\n", "        cur_scores = deepcopy(sample[\"scores\"])\n", "        \n", "        cur_scores[\"user_id\"] = sample[\"option_a\"][\"user_id\"]\n", "\n", "        all_data.append(cur_scores)\n", "    df = pd.DataFrame(all_data)\n", "    return df\n", "\n", "def request_id_to_link(request_id):\n", "    return f\"https://support.{TENANT_NAME}.t.us-central1.prod.augmentcode.com/t/{TENANT_NAME}/request/{request_id}\""]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# samples = get_turing_samples()\n", "if TENANT_NAME == \"aitutor-turing\":\n", "    samples = get_turing_samples_local()\n", "elif <PERSON>_NAME == \"aitutor-mercor\":\n", "    samples = get_preferences_samples()\n", "else:\n", "    raise Exception(\"Invalid tenant name\")\n", "print(samples[0])\n", "len(samples)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# Only if using BQ version\n", "if TENANT_NAME == \"aitutor-mercor\":\n", "    chat_dict = get_chat_samples(list(set(list(map(lambda x: x[\"request_ids\"][0], samples)) + list(map(lambda x: x[\"request_ids\"][1], samples)))))\n", "    samples_with_chats = []\n", "    for sample in tqdm(samples):\n", "        option_a = get_chat_sample(sample[\"request_ids\"][0], chat_dict)\n", "        option_b = get_chat_sample(sample[\"request_ids\"][1], chat_dict)\n", "        if option_a is None:\n", "            print(f\"Failed to find chat sample for option_a: {sample['request_ids'][0]}\")\n", "            continue\n", "        if option_b is None:\n", "            print(f\"Failed to find chat sample for option_b: {sample['request_ids'][1]}\")\n", "            continue\n", "\n", "        sample[\"option_a\"] = option_a\n", "        sample[\"option_b\"] = option_b\n", "        samples_with_chats.append(sample)\n", "        \n", "    for sample in samples_with_chats:\n", "        # To make local and BQ version the same\n", "        sample[\"option_a\"][\"user_id\"] = sample[\"option_a\"][\"metadata\"][\"user_id\"]\n", "        sample[\"option_b\"][\"user_id\"] = sample[\"option_b\"][\"metadata\"][\"user_id\"]\n", "        \n", "        sample[\"user_agent\"] = sample[\"option_a\"][\"metadata\"][\"user_agent\"]\n", "        \n", "        sample[\"option_a\"][\"request_id\"] = sample[\"request_ids\"][0]\n", "        sample[\"option_b\"][\"request_id\"] = sample[\"request_ids\"][1]\n", "\n", "    samples = samples_with_chats"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["analysis_df = get_data_df(samples)\n", "samples_per_user = Counter(analysis_df.user_id)\n", "\n", "for name, num in samples_per_user.most_common(100):\n", "    print(f\"{name}: {num}\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# label_names = sorted(samples[0][\"scores\"].keys())\n", "label_names = list([\"formattingRating\", \"instructionFollowingRating\", \"isHighQuality\", \"overallRating\"])\n", "user_names = list(map(lambda x: x[0], samples_per_user.most_common(100)))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "\n", "custom_metrics = defaultdict(dict)\n", "\n", "for user_name in user_names:\n", "    print(f\"{user_name}\")\n", "    cur_samples = [sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_name]\n", "    print(\"\\n\".join(map(str, Counter(list(map(lambda x: x[\"user_agent\"], cur_samples))).most_common(100))))\n", "    print(\"#\" * 20)\n", "    \n", "    num_w_selected_code = len(list(filter(lambda x: len(x[\"option_a\"][\"request\"].get(\"selected_code\", \"\")) > 0, cur_samples)))\n", "    num_wo_selected_code = len(cur_samples) - num_w_selected_code\n", "    \n", "    custom_metrics[user_name][\"num_w_selected_code\"] = num_w_selected_code\n", "    custom_metrics[user_name][\"num_wo_selected_code\"] = num_wo_selected_code"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["for user_name in user_names:\n", "    print(f\"{user_name}: {custom_metrics[user_name]['num_w_selected_code'] / (custom_metrics[user_name]['num_w_selected_code'] + custom_metrics[user_name]['num_wo_selected_code']):.2f}\")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["fig, axes = plt.subplots(nrows=len(user_names) + 1, ncols=len(label_names), figsize=(8 * len(label_names), 8 * len(user_names)))\n", "\n", "for i, user_name in enumerate(user_names):\n", "    for j, label_name in enumerate(label_names):\n", "        sns.countplot(x=label_name, data=analysis_df[analysis_df[\"user_id\"] == user_name], ax=axes[i][j]) #, stat='probability')\n", "        axes[i, j].set_title(f\"{user_name} ({samples_per_user[user_name]}) - {label_name}\")\n", "        axes[i, j].set_xlabel(\"\")\n", "        axes[i, j].set_ylabel(\"\")\n", "        \n", "for j, label_name in enumerate(label_names):\n", "    sns.countplot(x=label_name, data=analysis_df, ax=axes[len(user_names)][j]) #, stat='probability')\n", "    axes[len(user_names), j].set_title(f\"Overall - {label_name}\")\n", "    axes[len(user_names), j].set_xlabel(\"\")\n", "    axes[len(user_names), j].set_ylabel(\"\")\n", "    "]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["96it [00:03, 24.51it/s]\n", "93it [00:01, 61.32it/s]\n", "91it [00:00, 93.88it/s] \n", "90it [00:05, 15.98it/s]\n", "59it [00:00, 104.48it/s]\n", "5it [00:00, 71.73it/s]\n"]}], "source": ["multisample_html = \"\"\n", "\n", "for user_id in user_names:\n", "    cur_samples = [sample for sample in samples if sample[\"option_a\"][\"user_id\"] == user_id]\n", "    # random.seed(42)\n", "    # random.shuffle(cur_samples)\n", "    cur_samples = list(reversed(sorted(cur_samples, key=lambda x: x[\"option_a\"][\"datetime\"])))\n", "    cur_user_html = \"\"\n", "    for i, sample in tqdm(enumerate(cur_samples)):\n", "        # Handles previous samples that don't have ratings\n", "        if \"formattingRating\" not in sample[\"scores\"]:\n", "            sample[\"scores\"][\"formattingRating\"] = -100\n", "\n", "        if \"instructionFollowingRating\" not in sample[\"scores\"]:\n", "            sample[\"scores\"][\"instructionFollowingRating\"] = -100\n", "\n", "        if \"hallucinationRating\" not in sample[\"scores\"]:\n", "            sample[\"scores\"][\"hallucinationRating\"] = -100\n", "\n", "        cur_user_html += f\"<h1>Chain {i}</h1>\"\n", "        cur_user_html += f\"<div>User ID: {user_id}</div>\"\n", "        cur_user_html += f\"<div>Time: {sample['option_a']['datetime'].strftime('%m-%d-%Y %H:%M:%S')}</div>\"\n", "        cur_user_html += f'<div>Request ID (A): <a href=\"{request_id_to_link(sample[\"option_a\"][\"request_id\"])}\">{sample[\"option_a\"][\"request_id\"]}</a></div>'\n", "        cur_user_html += f'<div>Request ID (B): <a href=\"{request_id_to_link(sample[\"option_b\"][\"request_id\"])}\">{sample[\"option_b\"][\"request_id\"]}</a></div>'\n", "        cur_user_html += render_sample_with_history(sample)\n", "        cur_user_html += \"<hr>\"\n", "    multisample_html += f\"<details><summary>User ID: {user_id} -- {len(cur_samples)} samples</summary>{cur_user_html}</details>\"\n", "\n", "whole_html = wrap_html(multisample_html)\n", "\n", "with open(f\"./{TENANT_NAME}-review-{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}.html\", \"w\") as f:\n", "    f.write(whole_html)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Lightweight export for Mercor"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["if TENANT_NAME == \"aitutor-mercor\":\n", "    exported_samples = []\n", "\n", "    for sample in samples:\n", "        exported_sample = {\n", "            \"preference_request_id\": sample[\"preference_request_id\"],\n", "            \"chat_history\": sample[\"option_a\"][\"request\"].get(\"chat_history\", []),\n", "            \"message\": sample[\"option_a\"][\"request\"][\"message\"],\n", "            \"path\": sample[\"option_a\"][\"request\"].get(\"path\", \"\"),\n", "            \"prefix\": sample[\"option_a\"][\"request\"].get(\"prefix\", \"\"),\n", "            \"selected_code\": sample[\"option_a\"][\"request\"].get(\"selected_code\", \"\"),\n", "            \"suffix\": sample[\"option_a\"][\"request\"].get(\"suffix\", \"\"),\n", "            \"option_a\": sample[\"option_a\"][\"response\"],\n", "            \"option_b\": sample[\"option_b\"][\"response\"],\n", "            \"scores\": sample[\"scores\"],\n", "            \"feedback\": sample.get(\"feedback\", \"\"),\n", "            \"metadata\": sample[\"option_a\"][\"metadata\"],\n", "            \"datetime\": sample[\"option_a\"][\"datetime\"].isoformat(),\n", "        }\n", "\n", "        exported_samples.append(exported_sample)\n", "\n", "    with open(f\"./{TENANT_NAME}-{datetime.datetime(*map(int, START_DAY.split('-'))).strftime('%m-%d')}_{datetime.datetime.now().strftime('%m-%d')}-dump-w-timestamps.jsonl\", \"w\") as f:\n", "        for sample in exported_samples:\n", "            f.write(json.dumps(sample))\n", "            f.write(\"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
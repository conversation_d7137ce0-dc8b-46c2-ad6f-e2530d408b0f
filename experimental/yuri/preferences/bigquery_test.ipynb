{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.edit_dataset import EditDataset\n", "from base.datasets import tenants\n", "from base.datasets.tenants import DatasetTenant\n", "from research.tools.export_edit_data.export_edits_to_jsonl import process_edit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DEV_TENANT = DatasetTenant(\n", "    name=\"dev-yuri\",\n", "    project_id=\"system-services-dev\",\n", "    dataset_name=\"dev_request_insight_full_export_dataset\",\n", "    blob_bucket_name=\"augment-blob-exporter-dev-yuri-dev\",\n", "    blob_bucket_prefix=\"blobs\",\n", "    checkpoint_bucket_name=\"augment-blob-exporter-dev-yuri-dev\",\n", "    checkpoint_bucket_prefix=\"checkpoints\",\n", ")\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = EditDataset.create(\n", "        DEV_TENANT,\n", "        filters=EditDataset.Filters(\n", "            with_resolution=True,\n", "            denied_request_ids=[],\n", "        ),\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rows = [*dataset._rows]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rows[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rows[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rows[0][\"resolution_json\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = list(dataset.get_entries())\n", "data = [process_edit(datum) for datum in data]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}
import requests
import json

from google.cloud import bigquery
from markdown import markdown as markdown_lib


def get_preferences_samples(
    project_id, dataset_name, tenant_name, start_time, limit=1000000
):
    query = f"""
SELECT *
FROM {project_id}.{dataset_name}.request_event
WHERE event_type="preference_sample" AND tenant="{tenant_name}"
AND time > TIMESTAMP("{start_time}")
ORDER BY time DESC
LIMIT {limit}
"""

    client = bigquery.Client(project=project_id)
    rows = [*client.query_and_wait(query)]

    clean_rows = []
    for row in rows:
        clean_rows.append(
            {"preference_request_id": row.request_id, **dict(row)["raw_json"]}
        )

    return clean_rows


def get_chat_sample(request_id, project_id, dataset_name, tenant_name):
    query = f"""
SELECT *
FROM {project_id}.{dataset_name}.request_event
WHERE request_id="{request_id}" AND tenant="{tenant_name}"
AND event_type IN ("chat_host_request", "chat_host_response", "request_metadata")
ORDER BY time DESC
LIMIT 100000
"""

    client = bigquery.Client(project=project_id)
    all_rows = list(client.query(query).result())

    request_rows = [row for row in all_rows if row.event_type == "chat_host_request"]
    response_rows = [row for row in all_rows if row.event_type == "chat_host_response"]
    meta_rows = [row for row in all_rows if row.event_type == "request_metadata"]

    assert len(request_rows) == 1, f"Expected 1 request, got {len(request_rows)}"
    assert len(response_rows) == 1, f"Expected 1 response, got {len(response_rows)}"
    assert len(meta_rows) == 1, f"Expected 1 metadata, got {len(meta_rows)}"

    return {
        "request": request_rows[0]["raw_json"]["request"],
        "response": response_rows[0]["raw_json"]["response"],
        "metadata": meta_rows[0]["raw_json"],
        "datetime": request_rows[0].time,
    }


def markdown(text):
    return markdown_lib(text, extensions=["fenced_code", "codehilite"])


def details_html(summary, html_content):
    return f"<details><summary>{summary}</summary>{html_content}</details>"


def row_html(html_content):
    return f'<div class="row">{html_content}</div>'


def request_id_to_link(request_id, tenant_name="aitutor-mercor"):
    return f"https://support.{tenant_name}.t.us-central1.prod.augmentcode.com/t/{tenant_name}/request/{request_id}"


def render_simple_message(message, response, selected_code=None):
    message = markdown(message)
    response = markdown(response)

    html_content = ""
    if selected_code is not None:
        selected_code = f"```\n{selected_code}\n```"
        selected_code = markdown(selected_code)
        html_content += f'<div class="code" style="flex: 1;">{selected_code}</div>'
    html_content += f'<div class="code">{message}</div>'
    html_content += f'<div class="code">{response}</div>'

    return html_content


def wrap_html(html_content):
    result = f"""
    <html>
    <head>
        <title>Question and Answers</title>
        <style>
            .row {{
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 20px;
            }}
            .code {{
                margin: 10px;
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
            }}
        </style>
    </head>
    <body>
        {html_content}
    </body>
    </html>
"""
    return result


class TritonClient:
    def __init__(self, url: str, eod_token):
        self.url = url
        self.eod_token = eod_token

    def generate(
        self, full_prompt: str, temperature: float = 0.8, random_seed: int = 49
    ):
        payload = self.get_payload(full_prompt, temperature, random_seed)
        response_json = self.send_request(payload)

        return response_json["text_output"]

    def get_payload(self, full_prompt, temperature, random_seed):
        payload = {
            "text_input": full_prompt,
            "max_tokens": 1000,
            "end_id": self.eod_token,
            "stream": False,
            "temperature": temperature,
            "top_k": 40,
            "top_p": 0.95,
            "random_seed": random_seed,
            "return_context_logits": False,
            "return_log_probs": False,
            "return_generation_logits": False,
        }

        return payload

    def send_request(self, payload):
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            f"http://{self.url}/v2/models/ensemble/generate",
            headers=headers,
            data=json.dumps(payload),
            timeout=100,
        )
        response_json = response.json()

        return response_json

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from pyspark.sql import SparkSession\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from pathlib import Path\n", "from functools import partial\n", "from tqdm import tqdm\n", "from research.models.meta_model import GenerationOptions\n", "import plotly.express as px\n", "\n", "\n", "TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/aug31_v1_1m\"\n", "OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/data/alignment_data_aug31_v1_1m\")\n", "NUM_WORKERS = 128\n", "\n", "OUTPUT_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_spark(job_name: str, max_workers: int) -> SparkSession:\n", "    return k8s_session(\n", "        name=job_name,\n", "        # gpu_type=[\"A40\", \"RTX_A6000\"],\n", "        gpu_type=[\"H100_NVLINK_80GB\"],\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"120G\",\n", "            \"spark.driver.memory\": \"120G\",\n", "            \"spark.driver.maxResultSize\": \"16G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        max_workers=max_workers,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare parquet"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "import megatron.data.indexed_dataset as indexed_dataset\n", "\n", "from pathlib import Path\n", "\n", "DATAPATH = Path(\n", "    \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset\"\n", ")\n", "TOKENIZER = create_tokenizer_by_name(\"starcoder2\")\n", "NUM_SAMPLES_TO_TAKE = 1_000_000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_prompt_target(tokens: list[int], tokenizer) -> tuple[list[int], list[int]]:\n", "    \"\"\"Get the prompt and target from the tokens.\"\"\"\n", "    # Remove the padding tokens.\n", "    tokens = tokens[: tokens.index(tokenizer.special_tokens.padding)]\n", "    fim_middle_token = tokenizer.special_tokens.fim_middle\n", "    assert fim_middle_token in tokens, \"The fim_middle token is not in the tokens.\"\n", "    index = tokens.index(fim_middle_token)\n", "    prompt_tokens = tokens[: index + 1]\n", "    target_tokens = tokens[index + 1 :]\n", "    return prompt_tokens, target_tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = indexed_dataset.MMapIndexedDataset(str(DATAPATH), skip_warmup=True)\n", "print(f\"The dataset has {len(dataset)} records.\")\n", "special_tokens = TOKENIZER.special_tokens\n", "# Show some special tokens\n", "print(f\"{special_tokens.eos = }\")\n", "print(f\"{special_tokens.pause = }\")\n", "print(f\"{special_tokens.skip = }\")\n", "print(f\"{special_tokens.padding = }\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = []\n", "\n", "for i in tqdm(range(NUM_SAMPLES_TO_TAKE)):\n", "    prompt_tokens, target_tokens = get_prompt_target(dataset[i].tolist(), TOKENIZER)\n", "    cur_sample = {\n", "        \"prompt_tokens\": prompt_tokens,\n", "        \"target_tokens\": target_tokens,\n", "        \"index\": i,\n", "    }\n", "    samples.append({\"sample\": json.dumps(cur_sample)})\n", "len(samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_spark(\"yuri_process\", NUM_WORKERS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples_df = pd.DataFrame(samples)\n", "samples_df = spark.createDataFrame(samples_df)\n", "\n", "samples_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples_df.repartition(NUM_WORKERS * 64).write.parquet(f\"{TMP_BUCKET}/samples_df\")  # type: ignore\n", "# samples_df.repartition(4).write.parquet(f\"{TMP_BUCKET}/samples_df\") # type: ignore"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for i in tqdm(range(NUM_SAMPLES_TO_TAKE)):\n", "#     prompt_tokens, target_tokens = get_prompt_target(dataset[i].tolist(), TOKENIZER)\n", "#     assert len(prompt_tokens) < 8000\n", "#     assert len(target_tokens) <= 256"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generating responses"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NUM_RESPONSES_TO_GENERATE = 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def _log(txt, f):\n", "    with open(f, \"a\") as f:\n", "        f.write(f\"{txt}\\n\")\n", "\n", "\n", "def generate_responses(prompt_tokens, n, progress_file):\n", "    from research.eval.harness import factories\n", "\n", "    global COMPLETION_MODEL\n", "\n", "    if \"COMPLETION_MODEL\" not in globals():\n", "        globals()[\"COMPLETION_MODEL\"] = None\n", "\n", "    if COMPLETION_MODEL is None:\n", "        _log(\"Start loading model\", progress_file)\n", "        model_config = {\n", "            \"name\": \"fastbackward\",\n", "            \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1\",\n", "            \"override_tokenizer\": \"starcoder2\",\n", "            \"seq_length\": 7936,\n", "        }\n", "        COMPLETION_MODEL = factories.create_model(model_config)\n", "        COMPLETION_MODEL.load()\n", "        _log(\"Finish loading model\", progress_file)\n", "    else:\n", "        _log(\"Model already loaded\", progress_file)\n", "\n", "    generation_options = GenerationOptions(\n", "        temperature=1.0, top_p=0.9, max_generated_tokens=64\n", "    )\n", "    responses = []\n", "    for _ in range(n):\n", "        cur_response = COMPLETION_MODEL.raw_generate_tokens(\n", "            prompt_tokens, options=generation_options\n", "        )\n", "        cur_response.tokens = cur_response.tokens[: len(cur_response.logits)]\n", "        cur_response = {\n", "            \"tokens\": cur_response.tokens,\n", "            \"probs\": cur_response.token_probs(),\n", "        }\n", "        responses.append(cur_response)\n", "    return responses\n", "\n", "\n", "def get_log_probs(probs):\n", "    return list(map(lambda x: np.log(x), probs))\n", "\n", "\n", "def generate_responses_spark(batch: pd.DataFrame, progress_file: Path):\n", "    sample = json.loads(batch.iloc[0][\"sample\"])\n", "    prompt_tokens = sample[\"prompt_tokens\"]\n", "    target_tokens = sample[\"target_tokens\"]\n", "\n", "    _log(\"Start generating responses\", progress_file)\n", "    try:\n", "        responses = generate_responses(\n", "            prompt_tokens, NUM_RESPONSES_TO_GENERATE, progress_file\n", "        )\n", "    except Exception as e:\n", "        _log(f\"Failed with exception: {e}\", progress_file)\n", "        responses = []\n", "    _log(\"Finish generating responses\", progress_file)\n", "\n", "    if len(responses) == 0:\n", "        _log(\"No responses generated, adding empty response\", progress_file)\n", "        responses = [{\"tokens\": [], \"probs\": []}]\n", "\n", "    return pd.DataFrame(\n", "        {\n", "            \"generate_responses\": [json.dumps(responses)],\n", "            \"prompt_tokens\": [json.dumps(prompt_tokens)],\n", "            \"target_tokens\": [json.dumps(target_tokens)],\n", "            \"index\": [sample[\"index\"]],\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for sample in tqdm(samples[1:]):\n", "#     _r = generate_responses(\n", "#         json.loads(sample[\"sample\"])[\"prompt_tokens\"],\n", "#         1,\n", "#         \"/tmp/t1\"\n", "#     )\n", "#     break\n", "\n", "# Reusing first spark\n", "# spark = create_spark(\"yuri_step2\", NUM_WORKERS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(OUTPUT_DIR / \"spark_progress.txt\")\n", "\n", "spark_result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(\n", "        generate_responses_spark,\n", "        progress_file=OUTPUT_DIR / \"spark_progress.txt\",\n", "    ),\n", "    input_path=f\"{TMP_BUCKET}/samples_df\",\n", "    output_path=f\"{TMP_BUCKET}/responses_df\",\n", "    timeout=60 * 60,  # 60 minutes\n", "    batch_size=1,\n", "    ignore_error=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_result[\"task_info\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Predict quality"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import xgboost as xgb\n", "\n", "from base.completion_filter.extract_completion_filter_features import (\n", "    extract_completion_filter_features,\n", ")\n", "from research.prism.train import to_dmatrix\n", "\n", "QUALITY_MODEL_PATH = \"/home/<USER>/repos/augment/services/completion_host/single_model_server/prism_models/prism_eldenv3.json\"\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"starcoder2\")\n", "QUALITY_FILTER_MODEL = xgb.Booster(model_file=QUALITY_MODEL_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_quality(token_ids: list[int], log_probs: list[float]):\n", "    assert min(token_ids) >= 0, \"Token ids should be non-negative\"\n", "\n", "    features = extract_completion_filter_features(\n", "        token_ids=token_ids,\n", "        log_probs=log_probs,\n", "        tokenizer=TOKENIZER,\n", "    )\n", "\n", "    dmatrix = to_dmatrix(\n", "        [features],\n", "        [0],  # GT, unused for prediction\n", "        list(features.keys()),\n", "    )\n", "\n", "    predictions = QUALITY_FILTER_MODEL.predict(dmatrix)\n", "\n", "    assert predictions.shape == (1,)\n", "\n", "    # IMPORTANT: the bigger the WORSE. So we use 1 - here\n", "    return 1 - predictions[0].item()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df = spark.read.parquet(f\"{TMP_BUCKET}/responses_df\").toPandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_0 = 0\n", "num_1 = 0\n", "\n", "for _, row in tqdm(processed_df.iterrows()):\n", "    row_d = row.to_dict()\n", "    generated_responses = json.loads(row_d[\"generate_responses\"])\n", "    prompt_tokens = json.loads(row_d[\"prompt_tokens\"])\n", "    target_tokens = json.loads(row_d[\"target_tokens\"])\n", "\n", "    for response in generated_responses:\n", "        if len(response[\"tokens\"]) == 0:\n", "            num_0 += 1\n", "        if len(response[\"tokens\"]) == 1 and response[\"tokens\"][0] == 0:\n", "            num_1 += 1\n", "\n", "num_0, num_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diffs_all = []\n", "diffs_training = []\n", "samples_for_training = []\n", "\n", "skipped_0 = 0\n", "skipped_1 = 0\n", "\n", "for _, row in tqdm(processed_df.iterrows()):\n", "    row_d = row.to_dict()\n", "    generated_responses = json.loads(row_d[\"generate_responses\"])\n", "    prompt_tokens = json.loads(row_d[\"prompt_tokens\"])\n", "    target_tokens = json.loads(row_d[\"target_tokens\"])\n", "    index = row_d[\"index\"]\n", "\n", "    rewards = []\n", "    for response in generated_responses:\n", "        if len(response[\"tokens\"]) == 0:\n", "            skipped_0 += 1\n", "            rewards = None\n", "            break\n", "        if len(response[\"tokens\"]) == 1:\n", "            skipped_1 += 1\n", "            rewards = None\n", "            break\n", "        assert len(response[\"tokens\"]) == len(response[\"probs\"])\n", "        cur_reward = predict_quality(\n", "            response[\"tokens\"], get_log_probs(response[\"probs\"])\n", "        )\n", "        rewards.append(cur_reward)\n", "    if rewards is None:\n", "        continue\n", "    cur_diff = max(rewards) - min(rewards)\n", "    diffs_all.append(cur_diff)\n", "    if cur_diff < 0.05:\n", "        continue\n", "    diffs_training.append(cur_diff)\n", "\n", "    chosen_response = generated_responses[np.argmax(rewards)]\n", "    rejected_response = generated_responses[np.argmin(rewards)]\n", "\n", "    cur_training_sample = {\n", "        \"prompt_tokens\": prompt_tokens,\n", "        \"target_tokens\": target_tokens,\n", "        \"chosen_response\": chosen_response,\n", "        \"rejected_response\": rejected_response,\n", "        \"chosen_reward\": max(rewards),\n", "        \"rejected_reward\": min(rewards),\n", "        \"index\": index,\n", "    }\n", "\n", "    samples_for_training.append(cur_training_sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["skipped_0, skipped_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(diffs_all, bins=30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(diffs_training, bins=30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(samples_for_training)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# with open(OUTPUT_DIR / \"samples_for_training.json\", \"w\") as f:\n", "#     json.dump(samples_for_training, f)\n", "\n", "with open(OUTPUT_DIR / \"samples_for_training.json\", \"r\") as f:\n", "    samples_for_training = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(samples_for_training[0].keys())\n", "print(samples_for_training[0][\"chosen_response\"].keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset, DatasetDict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TRAIN_FRACTION = 0.95\n", "\n", "\n", "def get_log_probs(probs):\n", "    return list(map(lambda x: float(np.log(x)), probs))\n", "\n", "\n", "def sample_reformat(sample):\n", "    return {\n", "        \"prompt_tokens\": sample[\"prompt_tokens\"],\n", "        \"chosen_completion_tokens\": sample[\"chosen_response\"][\"tokens\"],\n", "        \"rejected_completion_tokens\": sample[\"rejected_response\"][\"tokens\"],\n", "        \"chosen_completion_logprobs\": get_log_probs(sample[\"chosen_response\"][\"probs\"]),\n", "        \"rejected_completion_logprobs\": get_log_probs(\n", "            sample[\"rejected_response\"][\"probs\"]\n", "        ),\n", "    }\n", "\n", "\n", "def transpose(arr):\n", "    keys = arr[0].keys()\n", "    result = {}\n", "    for key in keys:\n", "        result[key] = [sample[key] for sample in arr]\n", "    return result\n", "\n", "\n", "samples_for_training_formatted = list(map(sample_reformat, tqdm(samples_for_training)))\n", "\n", "train_samples = samples_for_training_formatted[\n", "    : int(len(samples_for_training) * TRAIN_FRACTION)\n", "]\n", "eval_samples = samples_for_training_formatted[\n", "    int(len(samples_for_training) * TRAIN_FRACTION) :\n", "]\n", "\n", "print(\"Step 1\")\n", "train_dataset = Dataset.from_dict(transpose(train_samples))\n", "print(\"Step 2\")\n", "eval_dataset = Dataset.from_dict(transpose(eval_samples))\n", "print(\"Step 3\")\n", "\n", "combined_dataset = DatasetDict(\n", "    {\n", "        \"train\": train_dataset,\n", "        \"eval\": eval_dataset,\n", "    }\n", ")\n", "print(\"Step 4\")\n", "\n", "combined_dataset.save_to_disk(OUTPUT_DIR / \"preference_dataset\")\n", "\n", "len(combined_dataset[\"train\"]), len(combined_dataset[\"eval\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTPUT_DIR / \"preference_dataset\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
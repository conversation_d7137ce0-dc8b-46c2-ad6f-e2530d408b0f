{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from datasets import Dataset as HFDataset\n", "from base.tokenizers import create_tokenizer_by_name\n", "from tqdm import tqdm\n", "from functools import partial\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from pathlib import Path\n", "from research.data.spark import k8s_session\n", "from pyspark.sql import SparkSession\n", "from datasets import Dataset, DatasetDict\n", "\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"starcoder2\")\n", "MAX_SEQ_LEN = 7936\n", "MAX_COMPLETION_LEN = 128\n", "MAX_PROMPT_LEN = MAX_SEQ_LEN - MAX_COMPLETION_LEN"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/offline-rl-240903/train\"\n", ")  # .select(range(100))\n", "valid_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/offline-rl-240903/valid\"\n", ")  # .select(range(80))\n", "\n", "print(len(train_dataset), len(valid_dataset))\n", "print(train_dataset[0].keys())\n", "print(TOKENIZER.detokenize(train_dataset[0][\"prompt_tokens\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TOKENIZER.detokenize(train_dataset[0][\"pos_tokens\"]))\n", "print(\"#\" * 20)\n", "print(TOKENIZER.detokenize(train_dataset[0][\"neg_tokens\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocess_samples(dataset):\n", "    result = []\n", "    for i in tqdm(range(len(dataset))):\n", "        sample = dataset[i]\n", "        pos_s = TOKENIZER.detokenize(train_dataset[i][\"pos_tokens\"])\n", "        neg_s = TOKENIZER.detokenize(train_dataset[i][\"neg_tokens\"])\n", "\n", "        if len(sample[\"pos_tokens\"]) == 0 or len(sample[\"neg_tokens\"]) == 0:\n", "            # print(f\"Skipping {i}\")\n", "            continue\n", "        if pos_s.rstrip() == neg_s.rstrip():\n", "            # print(f\"[strip] Skipping {i}\")\n", "            continue\n", "\n", "        len_pos = len(sample[\"pos_tokens\"])\n", "        len_neg = len(sample[\"neg_tokens\"])\n", "        len_prompt = len(sample[\"prompt_tokens\"])\n", "        if len_prompt > MAX_PROMPT_LEN:\n", "            print(f\"[max_prompt_len] Skipping {i}\")\n", "            continue\n", "        if max([len_pos, len_neg]) > MAX_COMPLETION_LEN - 1:  # -1 for EOS\n", "            print(f\"[max_completion_len] Skipping {i}\")\n", "            continue\n", "\n", "        if sample[\"pos_tokens\"][-1] != TOKENIZER.special_tokens.eos:\n", "            sample[\"pos_tokens\"].append(TOKENIZER.special_tokens.eos)\n", "        else:\n", "            assert False\n", "\n", "        if sample[\"neg_tokens\"][-1] != TOKENIZER.special_tokens.eos:\n", "            sample[\"neg_tokens\"].append(TOKENIZER.special_tokens.eos)\n", "        else:\n", "            assert False\n", "\n", "        result.append(sample)\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FB_CHECKPOINT_PATH = \"/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1\"\n", "TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/hindsight_convert_v3_sep7\"\n", "OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/hindsight_v3_sep7\")\n", "MAX_WORKERS = 192\n", "\n", "OUTPUT_DIR.mkdir(exist_ok=True)\n", "\n", "\n", "def create_spark(job_name: str, max_workers: int) -> SparkSession:\n", "    return k8s_session(\n", "        name=job_name,\n", "        # gpu_type=[\"A40\", \"RTX_A6000\"],\n", "        gpu_type=[\"H100_NVLINK_80GB\", \"A100_NVLINK_80GB\"],\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"120G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        max_workers=max_workers,\n", "    )\n", "\n", "\n", "def _log(txt, f):\n", "    with open(f, \"a\") as f:\n", "        f.write(f\"{txt}\\n\")\n", "\n", "\n", "def get_log_probs(prompt_tokens, pos_tokens, neg_tokens, progress_file):\n", "    import torch\n", "    from research.eval.harness import factories\n", "\n", "    global COMPLETION_MODEL\n", "    if \"COMPLETION_MODEL\" not in globals():\n", "        globals()[\"COMPLETION_MODEL\"] = None\n", "\n", "    if COMPLETION_MODEL is None:\n", "        _log(\"Start loading model\", progress_file)\n", "        model_config = {\n", "            \"name\": \"fastbackward\",\n", "            \"checkpoint_path\": FB_CHECKPOINT_PATH,\n", "            \"override_tokenizer\": \"starcoder2\",\n", "            \"seq_length\": MAX_SEQ_LEN,\n", "        }\n", "        COMPLETION_MODEL = factories.create_model(model_config)\n", "        COMPLETION_MODEL.load()\n", "        _log(\"Finish loading model\", progress_file)\n", "    else:\n", "        _log(\"Model already loaded\", progress_file)\n", "\n", "    chosen_full_prompt = torch.tensor(prompt_tokens + pos_tokens).cuda()\n", "    rejected_full_prompt = torch.tensor(prompt_tokens + neg_tokens).cuda()\n", "\n", "    chosen_tokens_t = torch.tensor(pos_tokens).unsqueeze(1).cuda()\n", "    rejected_tokens_t = torch.tensor(neg_tokens).unsqueeze(1).cuda()\n", "\n", "    chosen_logits = COMPLETION_MODEL.forward_pass_single_logits(chosen_full_prompt)\n", "    rejected_logits = COMPLETION_MODEL.forward_pass_single_logits(rejected_full_prompt)\n", "\n", "    chosen_logits = chosen_logits[-len(pos_tokens) - 1 : -1]\n", "    rejected_logits = rejected_logits[-len(neg_tokens) - 1 : -1]\n", "\n", "    chosen_probs = (\n", "        torch.softmax(chosen_logits, dim=-1)\n", "        .gather(dim=-1, index=chosen_tokens_t)\n", "        .squeeze(1)\n", "        .tolist()\n", "    )\n", "    rejected_probs = (\n", "        torch.softmax(rejected_logits, dim=-1)\n", "        .gather(dim=-1, index=rejected_tokens_t)\n", "        .squeeze(1)\n", "        .tolist()\n", "    )\n", "\n", "    chosen_logprobs = list(map(lambda x: float(np.log(x)), chosen_probs))\n", "    rejected_logprobs = list(map(lambda x: float(np.log(x)), rejected_probs))\n", "\n", "    return chosen_logprobs, rejected_logprobs\n", "\n", "\n", "def get_log_probs_spark(batch: pd.DataFrame, progress_file: Path):\n", "    import torch\n", "\n", "    # batch size == 1\n", "    sample = json.loads(batch.iloc[0][\"sample\"])\n", "\n", "    prompt_tokens = sample[\"prompt_tokens\"]\n", "    pos_tokens = sample[\"pos_tokens\"]\n", "    neg_tokens = sample[\"neg_tokens\"]\n", "\n", "    _log(\"Start getting logprobs\", progress_file)\n", "    try:\n", "        with torch.inference_mode():\n", "            chosen_probs, rejected_probs = get_log_probs(\n", "                prompt_tokens, pos_tokens, neg_tokens, progress_file\n", "            )\n", "\n", "        assert len(chosen_probs) == len(pos_tokens)\n", "        assert len(rejected_probs) == len(neg_tokens)\n", "    except Exception as e:\n", "        import traceback\n", "\n", "        _log(f\"Failed with exception: {e}\", progress_file)\n", "        _log(traceback.format_exc(), progress_file)\n", "        chosen_probs = []\n", "        rejected_probs = []\n", "    _log(\"Finish getting logprobs\", progress_file)\n", "\n", "    return pd.DataFrame(\n", "        {\n", "            \"prompt_tokens\": [json.dumps(prompt_tokens)],\n", "            \"pos_tokens\": [json.dumps(pos_tokens)],\n", "            \"neg_tokens\": [json.dumps(neg_tokens)],\n", "            \"chosen_logprobs\": [json.dumps(chosen_probs)],\n", "            \"rejected_logprobs\": [json.dumps(rejected_probs)],\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def convert_hindsight_dataset_to_preference(spark, dataset, prefix):\n", "    spark_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        spark_samples.append({\"sample\": json.dumps(dataset[i])})\n", "\n", "    samples_df = pd.DataFrame(spark_samples)\n", "    samples_df = spark.createDataFrame(samples_df)\n", "\n", "    samples_df.repartition(MAX_WORKERS * 4).write.parquet(\n", "        f\"{TMP_BUCKET}/{prefix}_hindsight_samples_df\"\n", "    )  # type: ignore\n", "\n", "    print(OUTPUT_DIR / f\"{prefix}_spark_progress.txt\")\n", "\n", "    spark_output = map_parquet.apply_pandas(\n", "        spark,\n", "        partial(\n", "            get_log_probs_spark,\n", "            progress_file=OUTPUT_DIR / f\"{prefix}_spark_progress.txt\",\n", "        ),\n", "        input_path=f\"{TMP_BUCKET}/{prefix}_hindsight_samples_df\",\n", "        output_path=f\"{TMP_BUCKET}/{prefix}_hindsight_logprobs_df\",\n", "        timeout=30 * 60,  # 30 minutes\n", "        batch_size=1,\n", "        ignore_error=True,\n", "    )\n", "\n", "    processed_df = spark.read.parquet(\n", "        f\"{TMP_BUCKET}/{prefix}_hindsight_logprobs_df\"\n", "    ).<PERSON><PERSON><PERSON><PERSON>()\n", "\n", "    prepared_samples = []\n", "\n", "    for i, row in processed_df.iterrows():\n", "        row_d = row.to_dict()\n", "\n", "        for k, v in row_d.items():\n", "            if isinstance(v, str):\n", "                row_d[k] = json.loads(v)\n", "\n", "        if len(row_d[\"chosen_logprobs\"]) == 0 or len(row_d[\"rejected_logprobs\"]) == 0:\n", "            print(f\"Skipping {i}\")\n", "            continue\n", "\n", "        cur_prepared_sample = {\n", "            \"prompt_tokens\": row_d[\"prompt_tokens\"],\n", "            \"chosen_continuation_tokens\": row_d[\"pos_tokens\"],\n", "            \"rejected_continuation_tokens\": row_d[\"neg_tokens\"],\n", "            \"chosen_continuation_logprobs\": row_d[\"chosen_logprobs\"],\n", "            \"rejected_continuation_logprobs\": row_d[\"rejected_logprobs\"],\n", "        }\n", "        prepared_samples.append(cur_prepared_sample)\n", "\n", "    return prepared_samples, spark_output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_dataset[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_dataset_prepared = preprocess_samples(train_dataset)\n", "eval_dataset_prepared = preprocess_samples(valid_dataset)\n", "\n", "len(training_dataset_prepared), len(eval_dataset_prepared)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(training_dataset_prepared), len(eval_dataset_prepared)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_spark(\"yuri_process_hindsight\", MAX_WORKERS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_dataset_prepared = convert_hindsight_dataset_to_preference(\n", "    spark, training_dataset_prepared, \"train\"\n", ")\n", "eval_dataset_prepared = convert_hindsight_dataset_to_preference(\n", "    spark, eval_dataset_prepared, \"eval\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_dataset_prepared, train_spark_output = train_dataset_prepared\n", "eval_dataset_prepared, eval_spark_output = eval_dataset_prepared"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_spark_output[\"task_info\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(train_dataset_prepared), len(eval_dataset_prepared)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def transpose(arr):\n", "    keys = arr[0].keys()\n", "    result = {}\n", "    for key in keys:\n", "        result[key] = [sample[key] for sample in arr]\n", "    return result\n", "\n", "\n", "combined_dataset = DatasetDict(\n", "    {\n", "        \"train\": Dataset.from_dict(transpose(train_dataset_prepared)),\n", "        \"eval\": Dataset.from_dict(transpose(eval_dataset_prepared)),\n", "    }\n", ")\n", "\n", "combined_dataset.save_to_disk(OUTPUT_DIR / \"preference_dataset\")\n", "print(OUTPUT_DIR / \"preference_dataset\")\n", "len(combined_dataset[\"train\"]), len(combined_dataset[\"eval\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_dataset[\"train\"][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(combined_dataset[\"train\"])):\n", "    sample = combined_dataset[\"train\"][i]\n", "    prompt_text = TOKENIZER.detokenize(sample[\"prompt_tokens\"])\n", "    pos_text = TOKENIZER.detokenize(sample[\"chosen_continuation_tokens\"])\n", "    neg_text = TOKENIZER.detokenize(sample[\"rejected_continuation_tokens\"])\n", "\n", "    if (\n", "        \"doc_tokens.extend(-item for item in tokenizer.tokenize_safe(header))\"\n", "        in pos_text\n", "    ):\n", "        print(i)\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pos_text, neg_text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import load_from_disk as load_from_disk_hf\n", "\n", "_d = load_from_disk_hf(\n", "    \"/mnt/efs/augment/user/yuri/tmp/hindsight_v1_sep7/preference_dataset\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(_d[\"train\"]), len(_d[\"eval\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_d[\"train\"][0][\"promdd\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(_d[\"eval\"])):\n", "    sample = _d[\"eval\"][i]\n", "    if len(sample[\"chosen_continuation_tokens\"]) > 200:\n", "        print(i)\n", "    # print(\n", "    #     len(sample[\"prompt_tokens\"]), len(sample[\"chosen_continuation_tokens\"]), len(sample[\"rejected_continuation_tokens\"]),\n", "    #     len(sample[\"chosen_continuation_logprobs\"]), len(sample[\"rejected_continuation_logprobs\"])\n", "    # )"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
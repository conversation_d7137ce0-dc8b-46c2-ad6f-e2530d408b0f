{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import subprocess\n", "from pathlib import Path\n", "import numpy as np\n", "import pandas as pd\n", "from research.models.meta_model import GenerationOptions\n", "from datasets import load_from_disk\n", "from pyspark.sql import SparkSession\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from functools import partial\n", "from tqdm import tqdm\n", "from collections import defaultdict\n", "import plotly.express as px"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATASET_PATH = \"/mnt/efs/augment/user/yuri/data/alignment_data_aug31_v1_1m/preference_dataset_wo_empty\"\n", "NUM_WORKERS = 160"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prepare_checkpoint(ckpt_id: str, output_dir: Path, is_gcp=False):\n", "    output_dir.mkdir(exist_ok=True)\n", "\n", "    if (output_dir / \"mp1\").exists():\n", "        return output_dir / \"mp1\"\n", "\n", "    if not (output_dir / \"dai_ckpt\").exists():\n", "        print(f\"Downloading checkpoint {ckpt_id} to {output_dir / 'dai_ckpt'}\")\n", "        if is_gcp:\n", "            copy_command = f\"gsutil -m rsync -r gs://determined-checkpoint-storage/{ckpt_id} {output_dir}/dai_ckpt/{ckpt_id}\"\n", "            (output_dir / \"dai_ckpt\").mkdir(exist_ok=True)\n", "            (output_dir / \"dai_ckpt\" / ckpt_id).mkdir(exist_ok=True)\n", "        else:\n", "            copy_command = (\n", "                f\"s3cmd sync s3://dev-training-dai/{ckpt_id} {output_dir}/dai_ckpt\"\n", "            )\n", "        print(copy_command)\n", "\n", "        subprocess.run(\n", "            copy_command,\n", "            shell=True,\n", "            check=True,\n", "            text=True,\n", "            stdout=sys.stdout,\n", "            stderr=sys.stderr,\n", "        )\n", "    else:\n", "        print(f\"Using checkpoint saved in {output_dir / 'dai_ckpt'}\")\n", "    subprocess.run(\n", "        f\"cp /mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1/params.json {output_dir}/dai_ckpt/{ckpt_id}/params.json\",\n", "        shell=True,\n", "        check=True,\n", "        text=True,\n", "        stdout=sys.stdout,\n", "        stderr=sys.stderr,\n", "    )\n", "    subprocess.run(\n", "        f\"cd /home/<USER>/repos/augment && python research/tools/ckp_converter/fbw2fbw_mp.py --i {output_dir}/dai_ckpt/{ckpt_id} --o {output_dir}/mp1 --mp 1\",\n", "        shell=True,\n", "        check=True,\n", "        text=True,\n", "        # stdout=sys.stdout,\n", "        # stderr=sys.stderr\n", "    )\n", "    return output_dir / \"mp1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# V1\n", "\n", "# Original model\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/aug31_eval_original_sc2\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/aug31_eval_original_sc2\")\n", "# FB_CHECKPOINT_PATH = \"/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1\"\n", "\n", "# dpo_beta01_ce2_v3_aug30, ckpt 100\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84059/overview\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/aug31_eval_dpo_beta01_ce2_v3_aug30_100\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce2_v3_aug30_100\")\n", "# FB_CHECKPOINT_PATH = prepare_checkpoint(\"be1b20cc-e921-4b49-92c0-0ef135e5d792\", OUTPUT_DIR)\n", "\n", "# dpo_beta01_ce_only_v3_aug30\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84060/checkpoints\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/aug31_eval_dpo_beta01_ce_only_v3_aug30_100\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce_only_v3_aug30_100\")\n", "# FB_CHECKPOINT_PATH = prepare_checkpoint(\"4c0c728a-6ec2-4956-8d54-f77dfa4d289c\", OUTPUT_DIR)\n", "\n", "# dpo_beta01_ce2_1M_v1_sep1, ckpt 700\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84140/checkpoints\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/aug31_eval_dpo_beta01_ce2_1M_v1_sep1_700\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce2_1M_v1_sep1_700\")\n", "# FB_CHECKPOINT_PATH = prepare_checkpoint(\"a742a737-a579-4f2b-928c-90785a23ae3c\", OUTPUT_DIR)\n", "\n", "# dpo_beta01_ce6_1M_v1_sep1, ckpt 700\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84144/checkpoints\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/aug31_eval_dpo_beta01_ce6_1M_v1_sep1_700\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce6_1M_v1_sep1_700\")\n", "# FB_CHECKPOINT_PATH = prepare_checkpoint(\"29887648-5de7-42db-92a4-7f19d656b49d\", OUTPUT_DIR)\n", "\n", "# OUTPUT_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# V2\n", "\n", "# Original model\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/sep3_eval_original_v1\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/sep3_eval_original_v1\")\n", "# FB_CHECKPOINT_PATH = \"/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1\"\n", "\n", "# dpo_beta01_ce2_1M_v1_sep1, ckpt 1071\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84140/overview\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/sep3_eval_dpo_beta01_ce2_1M_v1_sep1_1071\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce2_1M_v1_sep1_1071\")\n", "# FB_CHECKPOINT_PATH = prepare_checkpoint(\"2d2f3f82-df10-4d93-871f-785770286fa6\", OUTPUT_DIR)\n", "\n", "# dpo_beta01_ce4_1M_v2_sep1, ckpt 1071\n", "# https://determined-gcp.eng.augmentcode.com/det/experiments/998/overview\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/sep3_eval_dpo_beta01_ce4_1M_v2_sep1_1071\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce4_1M_v2_sep1_1071\")\n", "# FB_CHECKPOINT_PATH = prepare_checkpoint(\"f736bfb2-610c-4096-b271-c49b7400bb37\", OUTPUT_DIR, is_gcp=True)\n", "\n", "# dpo_beta01_ce6_1M_v1_sep1, ckpt 1071\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84144/overview\n", "# TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/sep3_eval_dpo_beta01_ce6_1M_v1_sep1_1071\"\n", "# OUTPUT_DIR = Path(\"/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce6_1M_v1_sep1_1071\")\n", "# FB_CHECKPOINT_PATH = prepare_checkpoint(\"06f50d64-8b40-49f6-954e-f372849acae3\", OUTPUT_DIR)\n", "\n", "# dpo_beta01_ce_only_1M_v1_sep1, ckpt 1071\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84148/overview\n", "TMP_BUCKET = \"s3a://yuri-dev-bucket/tmp/sep3_eval_dpo_beta01_ce_only_1M_v1_sep1_1071\"\n", "OUTPUT_DIR = Path(\n", "    \"/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce_only_1M_v1_sep1_1071\"\n", ")\n", "FB_CHECKPOINT_PATH = prepare_checkpoint(\n", "    \"cc8d7412-7593-40fc-9c05-da0859342db3\", OUTPUT_DIR\n", ")\n", "\n", "OUTPUT_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FB_CHECKPOINT_PATH"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_spark(job_name: str, max_workers: int) -> SparkSession:\n", "    return k8s_session(\n", "        name=job_name,\n", "        # gpu_type=[\"A40\", \"RTX_A6000\"],\n", "        gpu_type=[\"H100_NVLINK_80GB\", \"A100_NVLINK_80GB\"],\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "            \"spark.executor.memory\": \"120G\",\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "            \"spark.task.cpus\": \"5\",\n", "        },\n", "        max_workers=max_workers,\n", "    )\n", "\n", "\n", "def plot_ecdf(lists: list[list], labels: list[str]):\n", "    all_values = []\n", "    indicators = []\n", "    for i, _l in enumerate(lists):\n", "        all_values.extend(_l)\n", "        indicators.extend([labels[i]] * len(_l))\n", "\n", "    df = pd.DataFrame(\n", "        {\n", "            \"Value\": all_values,\n", "            \"List\": indicators,\n", "        }\n", "    )\n", "\n", "    fig = px.ecdf(df, x=\"Value\", color=\"List\")\n", "    fig.show()\n", "\n", "\n", "def get_log_probs(probs):\n", "    return list(map(lambda x: np.log(x), probs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = load_from_disk(DATASET_PATH)\n", "\n", "eval_dataset = dataset[\"eval\"]  # .select(range(200))\n", "\n", "print(len(eval_dataset))\n", "eval_dataset[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from base.tokenizers import create_tokenizer_by_name\n", "# TOKENIZER = create_tokenizer_by_name(\"starcoder2\")\n", "\n", "# print(sum(eval_dataset[3][\"chosen_completion_logprobs\"]))\n", "\n", "# print(TOKENIZER.detokenize(eval_dataset[3][\"chosen_completion_tokens\"]))\n", "# print(\"#\" * 20)\n", "# print(TOKENIZER.detokenize(eval_dataset[3][\"rejected_completion_tokens\"]))\n", "\n", "# plot_ecdf([\n", "#     [sum(s[\"chosen_completion_logprobs\"]) for s in eval_dataset],\n", "#     [sum(s[\"rejected_completion_logprobs\"]) for s in eval_dataset],\n", "# ],\n", "#     [\"chosen_completion_logprobs\", \"rejected_completion_logprobs\"]\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prepare inference"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_samples = []\n", "\n", "for i in tqdm(range(len(eval_dataset))):\n", "    sample = eval_dataset[i]\n", "    cur_sample = {\n", "        \"prompt_tokens\": sample[\"prompt_tokens\"],\n", "        \"chosen_completion_tokens\": sample[\"chosen_completion_tokens\"],\n", "        \"rejected_completion_tokens\": sample[\"rejected_completion_tokens\"],\n", "        \"original_chosen_completion_logprobs\": sample[\"chosen_completion_logprobs\"],\n", "        \"original_rejected_completion_logprobs\": sample[\"rejected_completion_logprobs\"],\n", "        \"index\": i,\n", "    }\n", "    spark_samples.append({\"sample\": json.dumps(cur_sample)})\n", "len(spark_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_spark(\"yuri_process\", NUM_WORKERS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DISABLE IF INPUT ALREADY EXISTS (provided as `input_path` to map_parquet.apply_pandas)\n", "\n", "# samples_df = pd.DataFrame(spark_samples)\n", "# samples_df = spark.createDataFrame(samples_df)\n", "\n", "# samples_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# samples_df.repartition(NUM_WORKERS * 16).write.parquet(f\"{TMP_BUCKET}/eval_samples_df\") # type: ignore\n", "# samples_df.repartition(10).write.parquet(f\"{TMP_BUCKET}/eval_samples_df\") # type: ignore"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inference stage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NUM_RESPONSES_TO_GENERATE = 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def _log(txt, f):\n", "    with open(f, \"a\") as f:\n", "        f.write(f\"{txt}\\n\")\n", "\n", "\n", "def generate_responses(prompt_tokens, n, progress_file, chosen_tokens, rejected_tokens):\n", "    import torch\n", "    from research.eval.harness import factories\n", "\n", "    global COMPLETION_MODEL\n", "\n", "    if \"COMPLETION_MODEL\" not in globals():\n", "        globals()[\"COMPLETION_MODEL\"] = None\n", "\n", "    if COMPLETION_MODEL is None:\n", "        _log(\"Start loading model\", progress_file)\n", "        model_config = {\n", "            \"name\": \"fastbackward\",\n", "            \"checkpoint_path\": FB_CHECKPOINT_PATH,\n", "            \"override_tokenizer\": \"starcoder2\",\n", "            \"seq_length\": 7936,\n", "        }\n", "        COMPLETION_MODEL = factories.create_model(model_config)\n", "        COMPLETION_MODEL.load()\n", "        _log(\"Finish loading model\", progress_file)\n", "    else:\n", "        _log(\"Model already loaded\", progress_file)\n", "\n", "    # generation_options = GenerationOptions(\n", "    #     temperature=1.0, top_p=0.9, max_generated_tokens=64\n", "    # )\n", "    responses = []\n", "    # for _ in range(n):\n", "    #     cur_response = COMPLETION_MODEL.raw_generate_tokens(prompt_tokens, options=generation_options)\n", "    #     cur_response.tokens = cur_response.tokens[:len(cur_response.logits)]\n", "    #     cur_response = {\n", "    #         \"tokens\": cur_response.tokens,\n", "    #         \"probs\": cur_response.token_probs(),\n", "    #     }\n", "    #     responses.append(cur_response)\n", "\n", "    greedy_generation_options = GenerationOptions(\n", "        temperature=0, max_generated_tokens=64\n", "    )\n", "    greedy_response = COMPLETION_MODEL.raw_generate_tokens(\n", "        prompt_tokens, options=greedy_generation_options\n", "    )\n", "    greedy_response.tokens = greedy_response.tokens[: len(greedy_response.logits)]\n", "    greedy_response = {\n", "        \"tokens\": greedy_response.tokens,\n", "        \"probs\": greedy_response.token_probs(),\n", "    }\n", "    responses.append(greedy_response)\n", "\n", "    chosen_full_prompt = torch.tensor(prompt_tokens + chosen_tokens).cuda()\n", "    rejected_full_prompt = torch.tensor(prompt_tokens + rejected_tokens).cuda()\n", "\n", "    chosen_tokens_t = torch.tensor(chosen_tokens).unsqueeze(1).cuda()\n", "    rejected_tokens_t = torch.tensor(rejected_tokens).unsqueeze(1).cuda()\n", "\n", "    chosen_logits = COMPLETION_MODEL.forward_pass_single_logits(chosen_full_prompt)\n", "    rejected_logits = COMPLETION_MODEL.forward_pass_single_logits(rejected_full_prompt)\n", "\n", "    chosen_logits = chosen_logits[-len(chosen_tokens) - 1 : -1]\n", "    rejected_logits = rejected_logits[-len(rejected_tokens) - 1 : -1]\n", "\n", "    chosen_probs = (\n", "        torch.softmax(chosen_logits, dim=-1)\n", "        .gather(dim=-1, index=chosen_tokens_t)\n", "        .squeeze(1)\n", "        .tolist()\n", "    )\n", "    rejected_probs = (\n", "        torch.softmax(rejected_logits, dim=-1)\n", "        .gather(dim=-1, index=rejected_tokens_t)\n", "        .squeeze(1)\n", "        .tolist()\n", "    )\n", "\n", "    return responses, chosen_probs, rejected_probs\n", "\n", "\n", "def generate_responses_spark(batch: pd.DataFrame, progress_file: Path):\n", "    sample = json.loads(batch.iloc[0][\"sample\"])\n", "    prompt_tokens = sample[\"prompt_tokens\"]\n", "    chosen_tokens = sample[\"chosen_completion_tokens\"]\n", "    rejected_tokens = sample[\"rejected_completion_tokens\"]\n", "    original_chosen_completion_logprobs = sample[\"original_chosen_completion_logprobs\"]\n", "    original_rejected_completion_logprobs = sample[\n", "        \"original_rejected_completion_logprobs\"\n", "    ]\n", "    index = sample[\"index\"]\n", "\n", "    _log(\"Start generating responses\", progress_file)\n", "    try:\n", "        responses, chosen_probs, rejected_probs = generate_responses(\n", "            prompt_tokens,\n", "            NUM_RESPONSES_TO_GENERATE,\n", "            progress_file,\n", "            chosen_tokens,\n", "            rejected_tokens,\n", "        )\n", "    except Exception as e:\n", "        _log(f\"Failed with exception: {e}\", progress_file)\n", "        responses = []\n", "        chosen_probs = []\n", "        rejected_probs = []\n", "    _log(\"Finish generating responses\", progress_file)\n", "\n", "    if len(responses) == 0:\n", "        _log(\"No responses generated, adding empty response\", progress_file)\n", "        responses = [{\"tokens\": [], \"probs\": []}]\n", "        chosen_probs = []\n", "        rejected_probs = []\n", "\n", "    return pd.DataFrame(\n", "        {\n", "            \"generate_responses\": [json.dumps(responses)],\n", "            \"prompt_tokens\": [json.dumps(prompt_tokens)],\n", "            \"chosen_completion_tokens\": [json.dumps(chosen_tokens)],\n", "            \"rejected_completion_tokens\": [json.dumps(rejected_tokens)],\n", "            \"chosen_completion_probs\": [json.dumps(chosen_probs)],\n", "            \"rejected_completion_probs\": [json.dumps(rejected_probs)],\n", "            \"original_chosen_completion_logprobs\": [\n", "                json.dumps(original_chosen_completion_logprobs)\n", "            ],\n", "            \"original_rejected_completion_logprobs\": [\n", "                json.dumps(original_rejected_completion_logprobs)\n", "            ],\n", "            \"index\": [index],\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(OUTPUT_DIR / \"spark_progress.txt\")\n", "\n", "spark_result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(\n", "        generate_responses_spark,\n", "        progress_file=OUTPUT_DIR / \"spark_progress.txt\",\n", "    ),\n", "    # input_path=f\"{TMP_BUCKET}/eval_samples_df\",\n", "    input_path=\"s3a://yuri-dev-bucket/tmp/sep3_eval_original_v1/eval_samples_df\",  # Only for 1M run on sep3\n", "    output_path=f\"{TMP_BUCKET}/eval_responses_v2_df\",\n", "    timeout=6 * 60,  # 6 minutes\n", "    batch_size=1,\n", "    ignore_error=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processed_df = spark.read.parquet(f\"{TMP_BUCKET}/eval_responses_v2_df\").toPandas()\n", "\n", "inference_results = {}\n", "\n", "for _, row in processed_df.iterrows():\n", "    row_d = row.to_dict()\n", "\n", "    for k, v in row_d.items():\n", "        if isinstance(v, str):\n", "            row_d[k] = json.loads(v)\n", "\n", "    if len(row_d[\"chosen_completion_probs\"]) == 0:\n", "        print(f\"Skipping {row_d['index']}\")\n", "        continue\n", "\n", "    inference_results[row_d[\"index\"]] = row_d\n", "\n", "print(len(inference_results))\n", "\n", "with (OUTPUT_DIR / \"inference_results.json\").open(\"w\") as f:\n", "    json.dump(inference_results, f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import xgboost as xgb\n", "\n", "from base.completion_filter.extract_completion_filter_features import (\n", "    extract_completion_filter_features,\n", ")\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.prism.train import to_dmatrix\n", "\n", "QUALITY_MODEL_PATH = \"/home/<USER>/repos/augment/services/completion_host/single_model_server/prism_models/prism_eldenv3.json\"\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"starcoder2\")\n", "QUALITY_FILTER_MODEL = xgb.Booster(model_file=QUALITY_MODEL_PATH)\n", "\n", "\n", "def predict_quality(token_ids: list[int], log_probs: list[float]):\n", "    assert min(token_ids) >= 0, \"Token ids should be non-negative\"\n", "\n", "    features = extract_completion_filter_features(\n", "        token_ids=token_ids,\n", "        log_probs=log_probs,\n", "        tokenizer=TOKENIZER,\n", "    )\n", "\n", "    dmatrix = to_dmatrix(\n", "        [features],\n", "        [0],  # GT, unused for prediction\n", "        list(features.keys()),\n", "    )\n", "\n", "    predictions = QUALITY_FILTER_MODEL.predict(dmatrix)\n", "\n", "    assert predictions.shape == (1,)\n", "\n", "    # IMPORTANT: the bigger the WORSE. So we use 1 - here\n", "    return 1 - predictions[0].item()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OUTPUT_DIR / \"inference_results.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# V1\n", "\n", "# with (OUTPUT_DIR / \"inference_results.json\").open(\"r\") as f:\n", "#     model_a_results = json.load(f)\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/aug31_eval_original_sc2/inference_results.json').open(\"r\") as f:\n", "#     model_a_results = json.load(f)\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce_only_v3_aug30_100/inference_results.json').open(\"r\") as f:\n", "#     model_a_results = json.load(f)\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce2_v3_aug30_100/inference_results.json').open(\"r\") as f:\n", "#     model_b_results = json.load(f)\n", "\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce2_1M_v1_sep1_700/inference_results.json').open(\"r\") as f:\n", "#     model_b_results = json.load(f)\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/aug31_eval_dpo_beta01_ce6_1M_v1_sep1_700/inference_results.json').open(\"r\") as f:\n", "#     model_b_results = json.load(f)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# V2\n", "\n", "with Path(\n", "    \"/mnt/efs/augment/user/yuri/tmp/sep3_eval_original_v1/inference_results.json\"\n", ").open(\"r\") as f:\n", "    model_a_results = json.load(f)\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce2_1M_v1_sep1_1071/inference_results.json').open(\"r\") as f:\n", "#     model_b_results = json.load(f)\n", "\n", "with Path(\n", "    \"/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce4_1M_v2_sep1_1071/inference_results.json\"\n", ").open(\"r\") as f:\n", "    model_b_results = json.load(f)\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce6_1M_v1_sep1_1071/inference_results.json').open(\"r\") as f:\n", "#     model_b_results = json.load(f)\n", "\n", "# with Path('/mnt/efs/augment/user/yuri/tmp/sep3_eval_dpo_beta01_ce_only_1M_v1_sep1_1071/inference_results.json').open(\"r\") as f:\n", "#     model_b_results = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stats = defaultdict(list)\n", "\n", "for i in tqdm(model_a_results.keys()):\n", "    if i not in model_b_results:\n", "        # print(f\"Skipping {i}\")\n", "        continue\n", "\n", "    a = model_a_results[i]\n", "    b = model_b_results[i]\n", "\n", "    assert a[\"prompt_tokens\"] == b[\"prompt_tokens\"]\n", "    assert a[\"chosen_completion_tokens\"] == b[\"chosen_completion_tokens\"]\n", "    assert a[\"rejected_completion_tokens\"] == b[\"rejected_completion_tokens\"]\n", "    assert (\n", "        a[\"original_chosen_completion_logprobs\"]\n", "        == b[\"original_chosen_completion_logprobs\"]\n", "    )\n", "    assert (\n", "        a[\"original_rejected_completion_logprobs\"]\n", "        == b[\"original_rejected_completion_logprobs\"]\n", "    )\n", "\n", "    greedy_responseA = a[\"generate_responses\"][-1]\n", "    greedy_responseB = b[\"generate_responses\"][-1]\n", "\n", "    greedy_rewardA = predict_quality(\n", "        greedy_responseA[\"tokens\"], get_log_probs(greedy_responseA[\"probs\"])\n", "    )\n", "    greedy_rewardB = predict_quality(\n", "        greedy_responseB[\"tokens\"], get_log_probs(greedy_responseB[\"probs\"])\n", "    )\n", "\n", "    stats[\"greedy_rewardA\"].append(greedy_rewardA)\n", "    stats[\"greedy_rewardB\"].append(greedy_rewardB)\n", "\n", "    stats[\"greedy_reward_diff\"].append(greedy_rewardB - greedy_rewardA)\n", "\n", "    chosen_logprobA = np.sum(np.log(a[\"chosen_completion_probs\"]))\n", "    rejected_logprobA = np.sum(np.log(a[\"rejected_completion_probs\"]))\n", "\n", "    chosen_logprobB = np.sum(np.log(b[\"chosen_completion_probs\"]))\n", "    rejected_logprobB = np.sum(np.log(b[\"rejected_completion_probs\"]))\n", "\n", "    stats[\"chosen_logprobA\"].append(chosen_logprobA)\n", "    stats[\"rejected_logprobA\"].append(rejected_logprobA)\n", "\n", "    stats[\"chosen_logprobB\"].append(chosen_logprobB)\n", "    stats[\"rejected_logprobB\"].append(rejected_logprobB)\n", "\n", "    stats[\"chosen_logprob_diff\"].append(chosen_logprobB - chosen_logprobA)\n", "    stats[\"rejected_logprob_diff\"].append(rejected_logprobB - rejected_logprobA)\n", "\n", "    stats[\"original_chosen_completion_logprobs\"].append(\n", "        np.sum(a[\"original_chosen_completion_logprobs\"])\n", "    )\n", "    stats[\"original_rejected_completion_logprobs\"].append(\n", "        np.sum(a[\"original_rejected_completion_logprobs\"])\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.mean(stats[\"greedy_rewardA\"]), np.mean(stats[\"greedy_rewardB\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.mean(stats[\"greedy_reward_diff\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(\n", "    np.mean(stats[\"original_chosen_completion_logprobs\"]),\n", "    np.mean(stats[\"original_rejected_completion_logprobs\"]),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.mean(stats[\"chosen_logprobA\"]), np.mean(stats[\"rejected_logprobA\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.mean(stats[\"chosen_logprobB\"]), np.mean(stats[\"rejected_logprobB\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["px.ecdf(stats[\"original_chosen_completion_logprobs\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(stats[\"greedy_reward_diff\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.mean(stats[\"greedy_reward_diff\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["px.ecdf(stats[\"greedy_reward_diff\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_ecdf([stats[\"greedy_rewardA\"], stats[\"greedy_rewardB\"]], [\"A\", \"B\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_ecdf([stats[\"chosen_logprobA\"], stats[\"chosen_logprobB\"]], [\"A\", \"B\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_ecdf([stats[\"rejected_logprobA\"], stats[\"rejected_logprobB\"]], [\"A\", \"B\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_ecdf([stats[\"chosen_logprob_diff\"]], [\"chosen_logprob\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_ecdf([stats[\"rejected_logprob_diff\"]], [\"rejected_logprob_diff\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
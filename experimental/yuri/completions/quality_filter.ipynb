{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import xgboost as xgb\n", "import numpy as np\n", "\n", "from research.prism.train import load_completion_datum, datum_to_dmatrix\n", "from base.datasets.completion import CompletionDatum\n", "from base.tokenizers import create_tokenizer_by_name\n", "from base.datasets.completion_dataset import CompletionDataset\n", "from base.datasets import tenants\n", "from base.completion_filter.extract_completion_filter_features import (\n", "    extract_completion_filter_features,\n", ")\n", "from research.prism.train import to_dmatrix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MODEL_PATH = \"/home/<USER>/repos/augment/services/completion_host/single_model_server/prism_models/prism_eldenv3.json\"\n", "\n", "TOKENIZER = create_tokenizer_by_name(\"starcoder2\")\n", "\n", "MODEL = xgb.Booster(model_file=MODEL_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_test_prism_data():\n", "    test_data_path = (\n", "        \"/mnt/efs/augment/data/prism/2024-07-03_2024-07-11_elden_20k/test.jsonl\"\n", "    )\n", "    test_datum: list[CompletionDatum] = load_completion_datum(test_data_path)\n", "    test_datum = [\n", "        *filter(\n", "            lambda d: (d.resolution) and (d.resolution.accepted is not None),  # \\\n", "            #   and (d.user_agent is None or \"vscode\" in d.user_agent or \"Eval\" in d.user_agent),\n", "            test_datum,\n", "        )\n", "    ]\n", "    return test_datum\n", "\n", "\n", "def get_completions(request_ids):\n", "    completion_filters = CompletionDataset.Filters(\n", "        request_ids=request_ids,\n", "    )\n", "    completions = CompletionDataset.create(\n", "        tenant=tenants.get_tenant(\"dogfood\"),\n", "        filters=completion_filters,\n", "        limit=100,\n", "        order_by=\"request_id\",\n", "        page_size=8192,\n", "    )\n", "\n", "    return completions\n", "\n", "\n", "def find_in_list(arr, e):\n", "    return [i for i, x in enumerate(arr) if x == e]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prism_data_datum = load_test_prism_data()[:1000]\n", "\n", "dmatrix = datum_to_dmatrix(prism_data_datum, TOKENIZER)\n", "print(dmatrix.get_label().shape)\n", "\n", "predictions = MODEL.predict(dmatrix)\n", "\n", "((predictions > 0.5) == dmatrix.get_label()).mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_quality(token_ids: list[int], log_probs: list[float]):\n", "    features = extract_completion_filter_features(\n", "        token_ids=token_ids,\n", "        log_probs=log_probs,\n", "        tokenizer=TOKENIZER,\n", "    )\n", "\n", "    dmatrix = to_dmatrix(\n", "        [features],\n", "        [0],  # GT, unused for prediction\n", "        list(features.keys()),\n", "    )\n", "\n", "    predictions = MODEL.predict(dmatrix)\n", "    return predictions[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions2 = []\n", "for d in prism_data_datum:\n", "    predictions2.append(\n", "        predict_quality(d.response.token_ids, d.response.token_log_probs)\n", "    )\n", "\n", "((np.array(predictions2) > 0.5) == dmatrix.get_label()).mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d.response.token_log_probs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.log([0.5229, 0.7682])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Inputs:\n", "# prompt_tokens = completions[0].response.prompt_tokens\n", "# generated_token_ids = completions[0].response.token_ids\n", "# generated_logprobs = completions[0].response.token_log_probs\n", "# ####\n", "\n", "# fim_prefix_pos = find_in_list(prompt_tokens, \"<fim_prefix>\")\n", "# fim_suffix_pos = find_in_list(prompt_tokens, \"<fim_suffix>\")\n", "# fim_middle_pos = find_in_list(prompt_tokens, \"<fim_middle>\")\n", "\n", "# assert len(fim_prefix_pos) == len(fim_suffix_pos) == len(fim_middle_pos) == 1\n", "\n", "# prefix = prompt_tokens[fim_prefix_pos[0] + 1 : fim_suffix_pos[0]]\n", "# suffix = prompt_tokens[fim_suffix_pos[0] + 1 : fim_middle_pos[0]]\n", "# completions = list(get_completions([d.request_id for d in prism_data_datum]).get_completions())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# set([c.request_id for c in completions]) == set([d.request_id for d in prism_data_datum])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
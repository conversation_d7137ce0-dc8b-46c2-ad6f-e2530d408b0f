determined:
  name: test-aug26-c-v1
  description: null
  workspace: Dev
  project: yuri

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 32

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 4
  max_iters: 9000
  warmup_iters: 200
  lr_decay_iters: 9000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/user/yuri/ckpts/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3
  train_data_path: /mnt/efs/augment/user/yuri/data/elden-dataset-unittest-literal-aug26/dataset
  eval_data_path: /mnt/efs/augment/user/yuri/data/elden-dataset-unittest-literal-aug26/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: test-aug26-c-v1
  wandb_project: yuri

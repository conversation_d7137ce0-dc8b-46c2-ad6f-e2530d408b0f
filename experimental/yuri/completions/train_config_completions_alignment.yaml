determined:
  name: dpo_beta01_ce4_1M_v1_sep1
  description: null
  workspace: Dev
  project: yuri

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 80
  project_group: "finetuning"
  keep_last_n_checkpoints: 20

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  gradient_accumulation_steps: 2
  batch_size: 4
  max_iters: 0
  max_epochs: 1

  block_size: 7936
  learning_rate: 1.0e-6
  log_interval: 5
  eval_interval: 50

  # GCP
  # checkpoint: /mnt/efs/augment/user/yuri/ckpts/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3
  # train_data_path: /mnt/efs/augment/user/yuri/data/alignment_data_aug27_v5_100k_preference_dataset

  # CW
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3
  train_data_path: /mnt/efs/augment/user/yuri/data/alignment_data_aug31_v1_1m/preference_dataset_wo_empty

  model_vocab_size: 49176

  dpo_ce_loss_coeff: 4.0
  dpo_beta: 0.1
  rlhf_checkpoint_interval: 100

  wandb_project: yuri
  run_name: dpo_beta01_ce4_1M_v1_sep1
  tokenizer_name: "starcoder2"

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.agents.tools import DialogMessages\n", "from research.llm_apis.llm_client import AnthropicDirectClient\n", "from research.agents.tools import ToolCallLogger\n", "from typing import Any\n", "from research.agents.tools import LLMTool, ToolImplOutput\n", "from research.agents.tools import call_tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class WeatherTool(LLMTool):\n", "    name = \"weather\"\n", "    description = \"Get the weather for a location.\"\n", "    input_schema = {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"location\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The location to get the weather for.\",\n", "            }\n", "        },\n", "        \"required\": [\"location\"],\n", "    }\n", "\n", "    def run_impl(self, tool_input: dict[str, Any], dialog_messages=None):\n", "        return ToolImplOutput(\n", "            tool_output=f\"The weather in {tool_input['location']} is sunny.\",\n", "            tool_result_message=\"Weather retrieved\",\n", "        )\n", "\n", "\n", "class CalculatorTool(LLMTool):\n", "    name = \"calculator\"\n", "    description = \"Perform a calculation.\"\n", "    input_schema = {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"expression\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The expression to evaluate.\",\n", "            }\n", "        },\n", "        \"required\": [\"expression\"],\n", "    }\n", "\n", "    def run_impl(self, tool_input: dict[str, Any], dialog_messages=None):\n", "        return ToolImplOutput(\n", "            tool_output=str(eval(tool_input[\"expression\"])),\n", "            tool_result_message=\"Calculation complete\",\n", "        )\n", "\n", "\n", "class ClimateTool(LLMTool):\n", "    name = \"climate\"\n", "    description = \"Get the climate for a location.\"\n", "    input_schema = {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"location\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The location to get the climate for.\",\n", "            }\n", "        },\n", "        \"required\": [\"location\"],\n", "    }\n", "\n", "    def run_impl(self, tool_input: dict[str, Any], dialog_messages=None):\n", "        return ToolImplOutput(\n", "            tool_output=f\"The climate in {tool_input['location']} is warm.\",\n", "            tool_result_message=\"Climate retrieved\",\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dialog = DialogMessages()\n", "dialog.add_user_prompt(\"What's the weather like in New York?\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llm_client = AnthropicDirectClient(\n", "    model_name=\"claude-3-5-sonnet-20241022\", max_retries=50\n", ")\n", "\n", "tool_call_logger = ToolCallLogger(\n", "    verbose=True,\n", "    verbose_llm_calls=True,\n", "    use_tool_supplied_messages=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response, metadata = llm_client.generate(\n", "    messages=dialog.get_messages_for_llm_client(),\n", "    max_tokens=1024,\n", "    tools=[\n", "        WeatherTool(tool_call_logger).get_tool_param(),\n", "        CalculatorTool(tool_call_logger).get_tool_param(),\n", "    ],\n", "    system_prompt=\"You are a helpful assistant.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dialog.add_model_response(response)\n", "call_tools(\n", "    [WeatherTool(tool_call_logger), CalculatorTool(tool_call_logger)],\n", "    dialog.get_pending_tool_calls(),\n", "    dialog_messages=dialog,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(dialog._message_lists))\n", "dialog._message_lists[2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response, metadata = llm_client.generate(\n", "    messages=dialog.get_messages_for_llm_client(),\n", "    max_tokens=1024,\n", "    tools=[\n", "        WeatherTool(tool_call_logger).get_tool_param(),\n", "        CalculatorTool(tool_call_logger).get_tool_param(),\n", "    ],\n", "    system_prompt=\"You are a helpful assistant.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dialog.add_model_response(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dialog.add_user_prompt(\"Thanks! How much is 2 + 2?\")\n", "dialog.add_user_prompt(\"Thanks! Now what's the weather like in San Francisco?\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dialog._message_lists[4]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response, metadata = llm_client.generate(\n", "    messages=dialog.get_messages_for_llm_client(),\n", "    max_tokens=1024,\n", "    # tools=[WeatherTool(tool_call_logger).get_tool_param(), CalculatorTool(tool_call_logger).get_tool_param()],\n", "    tools=[CalculatorTool(tool_call_logger).get_tool_param()],\n", "    # tools=[ClimateTool(tool_call_logger).get_tool_param(), CalculatorTool(tool_call_logger).get_tool_param()],\n", "    system_prompt=\"You are a helpful assistant.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dialog._message_lists[-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# dialog.add_model_response(response)\n", "call_tools(\n", "    [\n", "        WeatherTool(tool_call_logger),\n", "        CalculatorTool(tool_call_logger),\n", "        ClimateTool(tool_call_logger),\n", "    ],\n", "    dialog.get_pending_tool_calls(),\n", "    dialog_messages=dialog,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response, metadata = llm_client.generate(\n", "    messages=dialog.get_messages_for_llm_client(),\n", "    max_tokens=1024,\n", "    tools=[\n", "        WeatherTool(tool_call_logger).get_tool_param(),\n", "        CalculatorTool(tool_call_logger).get_tool_param(),\n", "    ],\n", "    system_prompt=\"You are a helpful assistant.\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
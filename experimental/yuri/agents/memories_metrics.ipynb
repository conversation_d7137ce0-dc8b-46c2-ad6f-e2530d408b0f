{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "from google.cloud import bigquery, logging\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "import services.request_insight.request_insight_pb2 as request_insight_pb2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import List, Union, Optional, Tuple\n", "from collections import Counter, defaultdict\n", "from tqdm import tqdm\n", "import random\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "\n", "# START_TIME = \"2025-03-17 01:00:00 UTC\"\n", "START_TIME = (datetime.utcnow() - timed<PERSON>ta(hours=24)).strftime(\"%Y-%m-%d %H:%M:%S UTC\")\n", "\n", "LIMIT = 5000\n", "\n", "CLIENT_VERSION_SUBSTR = None\n", "\n", "# dogfood\n", "DATASET_NAME = \"us_staging_request_insight_analytics_dataset\"\n", "TENANT_NAME = \"dogfood-shard\"\n", "\n", "# prod\n", "# DATASET_NAME = \"us_prod_request_insight_analytics_dataset\"\n", "# TENANT_NAME = \"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def search_google_logs(query, project_id=PROJECT_ID):\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    client = logging.Client(project=project_id, credentials=gcp_creds)\n", "    filter_str = f'resource.labels.container_name=\"chat\" {query}'\n", "    entries = client.list_entries(filter_=filter_str)\n", "\n", "    results = []\n", "    for entry in entries:\n", "        results.append(\n", "            {\n", "                \"timestamp\": entry.timestamp,\n", "                \"payload\": entry.payload,\n", "                \"log_name\": entry.log_name,\n", "                \"severity\": entry.severity,\n", "            }\n", "        )\n", "\n", "    return results\n", "\n", "\n", "def extract_token_metrics(request_id: str) -> dict:\n", "    logs = search_google_logs(request_id)\n", "\n", "    metrics = {}\n", "\n", "    for log in logs:\n", "        if log[\"severity\"] == \"INFO\" and \"Prompt cache usage:\" in str(log[\"payload\"]):\n", "            payload = log[\"payload\"]\n", "\n", "            message = payload.get(\"message\", \"\")\n", "            if \"Prompt cache usage:\" in message:\n", "                metrics_part = message.split(\"Prompt cache usage:\")[1].strip()\n", "                for metric_pair in metrics_part.split(\", \"):\n", "                    if \"=\" in metric_pair:\n", "                        key, value = metric_pair.split(\"=\")\n", "                        try:\n", "                            metrics[key] = float(value) if \".\" in value else int(value)\n", "                        except ValueError:\n", "                            metrics[key] = value\n", "        elif log[\"severity\"] == \"INFO\" and \"output_tokens:\" in str(log[\"payload\"]):\n", "            payload = log[\"payload\"]\n", "\n", "            message = payload.get(\"message\", \"\")\n", "            if \"output_tokens:\" in message:\n", "                output_tokens = message.split(\"output_tokens:\")[1].strip()\n", "                try:\n", "                    metrics[\"output_tokens\"] = int(output_tokens)\n", "                except ValueError:\n", "                    metrics[\"output_tokens\"] = output_tokens\n", "    return metrics\n", "\n", "\n", "def shuffle_paired_lists(list1, list2):\n", "    \"\"\"\n", "    Shuffle two lists together while maintaining the correspondence between their elements.\n", "\n", "    Args:\n", "        list1: First list to shuffle\n", "        list2: Second list to shuffle (must be same length as list1)\n", "\n", "    Returns:\n", "        tuple: (shuffled_list1, shuffled_list2)\n", "    \"\"\"\n", "    if len(list1) != len(list2):\n", "        raise ValueError(\"Lists must be of equal length\")\n", "\n", "    indices = list(range(len(list1)))\n", "    random.shuffle(indices)\n", "\n", "    shuffled_list1 = [list1[i] for i in indices]\n", "    shuffled_list2 = [list2[i] for i in indices]\n", "\n", "    return shuffled_list1, shuffled_list2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_requests_requests(event_name: str, session_id: Optional[str] = None):\n", "    \"\"\"\n", "    \"remember-tool-call\"\n", "    \"\"\"\n", "\n", "    tenant_filter = \"\"\n", "    if len(TENANT_NAME) > 0:\n", "        tenant_filter = f\"AND tenant = '{TENANT_NAME}'\"\n", "\n", "    session_filter = \"\"\n", "    if session_id is not None:\n", "        session_filter = f\"AND session_id = '{session_id}'\"\n", "\n", "    query = f\"\"\"\n", "SELECT *   \n", "FROM `{PROJECT_ID}.{DATASET_NAME}.agent_session_event`   \n", "WHERE time >= TIMESTAMP(\"{START_TIME}\")  \n", "  AND JSON_EXTRACT_SCALAR(sanitized_json, '$.event_name') = '{event_name}'  \n", "  {tenant_filter}\n", "  {session_filter}\n", "ORDER BY time DESC  \n", "LIMIT {LIMIT}  \n", "\"\"\"\n", "    print(query)\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    query_job = client.query(query)\n", "    dicts = list(map(lambda row: dict(row.items()), query_job.result()))\n", "    from google.protobuf.json_format import ParseDict\n", "\n", "    parsed_dicts = [\n", "        ParseDict(d[\"sanitized_json\"], request_insight_pb2.AgentSessionEvent())\n", "        for d in dicts\n", "    ]\n", "    if CLIENT_VERSION_SUBSTR is not None:\n", "        parsed_dicts_filtered, dicts_filtered = [], []\n", "        for a, b in zip(parsed_dicts, dicts):\n", "            if CLIENT_VERSION_SUBSTR in b[\"sanitized_json\"][\"user_agent\"]:\n", "                parsed_dicts_filtered.append(a)\n", "                dicts_filtered.append(b)\n", "        return parsed_dicts_filtered, dicts_filtered\n", "    return parsed_dicts, dicts\n", "\n", "\n", "def plot_distribution(\n", "    data: Union[List[float], List[int], np.ndarray],\n", "    title: str = \"Data Distribution\",\n", "    xlabel: str = \"Value\",\n", "    figsize: Tuple[int, int] = (12, 10),\n", "    hist_bins: int = 30,\n", "    hist_kde: bool = True,\n", "    save_path: Optional[str] = None,\n", "):\n", "    \"\"\"\n", "    Plot both histogram and ECDF for a given array of numbers.\n", "\n", "    Args:\n", "        data: Array-like object containing numerical data\n", "        title: Main title for the figure\n", "        xlabel: Label for x-axis (applies to both plots)\n", "        figsize: Figure size as (width, height) in inches\n", "        hist_bins: Number of bins for histogram\n", "        hist_kde: Whether to show KDE curve on histogram\n", "        save_path: If provided, save figure to this path\n", "\n", "    Returns:\n", "        fig, axes: The figure and axes objects for further customization\n", "    \"\"\"\n", "    # Convert input to numpy array if it's not already\n", "    data_array = np.asarray(data)\n", "\n", "    # Create figure with two subplots (2 rows, 1 column)\n", "    fig, axes = plt.subplots(2, 1, figsize=figsize)\n", "    fig.suptitle(title, fontsize=16)\n", "\n", "    # Plot 1: Histogram with K<PERSON>\n", "    sns.histplot(\n", "        data_array,\n", "        bins=hist_bins,\n", "        kde=hist_kde,\n", "        color=\"skyblue\",\n", "        edgecolor=\"black\",\n", "        alpha=0.7,\n", "        ax=axes[0],\n", "    )\n", "    axes[0].set_title(\"Histogram\", fontsize=14)\n", "    axes[0].set_xlabel(xlabel, fontsize=12)\n", "    axes[0].set_ylabel(\"Frequency\", fontsize=12)\n", "    axes[0].grid(True, linestyle=\"--\", alpha=0.7)\n", "\n", "    # Plot 2: ECDF\n", "    sns.ecdfplot(data_array, ax=axes[1], linewidth=2)\n", "    axes[1].set_title(\"Empirical Cumulative Distribution Function\", fontsize=14)\n", "    axes[1].set_xlabel(xlabel, fontsize=12)\n", "    axes[1].set_ylabel(\"Cumulative Probability\", fontsize=12)\n", "    axes[1].grid(True, linestyle=\"--\", alpha=0.7)\n", "\n", "    # Adjust layout\n", "    plt.tight_layout()\n", "\n", "    # Save figure if path is provided\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n", "        print(f\"Figure saved to {save_path}\")\n", "\n", "    return fig, axes\n", "\n", "\n", "def plot_scatter(\n", "    x: Union[List[float], List[int], np.ndarray],\n", "    y: Union[List[float], List[int], np.ndarray],\n", "    title: str = \"Scatter Plot\",\n", "    xlabel: str = \"X\",\n", "    ylabel: str = \"Y\",\n", "    figsize: Tuple[int, int] = (10, 6),\n", "    marker: str = \"o\",\n", "    color: str = \"blue\",\n", "    alpha: float = 0.7,\n", "    save_path: Optional[str] = None,\n", "):\n", "    \"\"\"\n", "    Create a simple scatter plot with dots for x and y coordinates.\n", "\n", "    Args:\n", "        x: Array-like object containing x-coordinates\n", "        y: Array-like object containing y-coordinates\n", "        title: Title for the plot\n", "        xlabel: Label for x-axis\n", "        ylabel: Label for y-axis\n", "        figsize: Figure size as (width, height) in inches\n", "        marker: Marker style for points\n", "        color: Color for the points\n", "        alpha: Transparency of points (0 to 1)\n", "        save_path: If provided, save figure to this path\n", "\n", "    Returns:\n", "        fig, ax: The figure and axis objects for further customization\n", "    \"\"\"\n", "    # Create figure and axis\n", "    fig, ax = plt.subplots(figsize=figsize)\n", "\n", "    # Create scatter plot\n", "    ax.scatter(x, y, marker=marker, color=color, alpha=alpha)\n", "\n", "    # Add labels and title\n", "    ax.set_title(title, fontsize=14)\n", "    ax.set_xlabel(xlabel, fontsize=12)\n", "    ax.set_ylabel(ylabel, fontsize=12)\n", "\n", "    # Add grid\n", "    ax.grid(True, linestyle=\"--\", alpha=0.7)\n", "\n", "    # Adjust layout\n", "    plt.tight_layout()\n", "\n", "    # Save figure if path is provided\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches=\"tight\")\n", "        print(f\"Figure saved to {save_path}\")\n", "\n", "    return fig, ax"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Remember tool calls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["requests, raw_requests = fetch_requests_requests(\n", "    \"remember-tool-call\",\n", ")\n", "len(requests), requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for request in requests:\n", "#     print(\n", "#         request.remember_tool_call_data.tracing_data.request_ids[\"injectionRequestId\"].value\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(list(map(lambda x: x[\"sanitized_json\"][\"user_agent\"], raw_requests)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["successful_requests = []\n", "failed_requests = []\n", "for request in requests:\n", "    if request.remember_tool_call_data.tracing_data.flags[\"toolOutputIsError\"].value:\n", "        failed_requests.append(request)\n", "    else:\n", "        successful_requests.append(request)\n", "\n", "len(successful_requests), len(failed_requests), len(requests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_stats = {\n", "    \"time\": [],\n", "    \"cur_memories_lines\": [],\n", "    \"cur_memories_chars\": [],\n", "    \"updated_memories_lines\": [],\n", "    \"updated_memories_chars\": [],\n", "    \"no_change\": [],\n", "    \"caller\": [],\n", "    \"nonEmptyLines\": [],\n", "    \"timestamp_start\": [],\n", "}\n", "\n", "for request in successful_requests:\n", "    start_time = request.remember_tool_call_data.tracing_data.flags[\"injectionStarted\"]\n", "    end_time = request.remember_tool_call_data.tracing_data.flags[\"toolOutputIsError\"]\n", "\n", "    execution_stats[\"time\"].append(end_time.timestamp.seconds - start_time.timestamp.seconds)\n", "    execution_stats[\"cur_memories_lines\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"injectionCurrentMemoriesStats\"\n", "        ].value.num_lines\n", "    )\n", "    execution_stats[\"cur_memories_chars\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"injectionCurrentMemoriesStats\"\n", "        ].value.num_chars\n", "    )\n", "    execution_stats[\"updated_memories_lines\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"injectionUpdatedMemoriesStats\"\n", "        ].value.num_lines\n", "    )\n", "    execution_stats[\"updated_memories_chars\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"injectionUpdatedMemoriesStats\"\n", "        ].value.num_chars\n", "    )\n", "    execution_stats[\"no_change\"].append(\n", "        request.remember_tool_call_data.tracing_data.flags[\"noChangesMade\"].value\n", "    )\n", "    execution_stats[\"caller\"].append(request.remember_tool_call_data.caller)\n", "\n", "    if (\n", "        request.remember_tool_call_data.tracing_data.nums[\"nonEmptyLines\"].ByteSize()\n", "        > 0\n", "    ):\n", "        non_empty_lines = request.remember_tool_call_data.tracing_data.nums[\n", "            \"nonEmptyLines\"\n", "        ].value\n", "    else:\n", "        non_empty_lines = 0\n", "    execution_stats[\"nonEmptyLines\"].append(non_empty_lines)\n", "    execution_stats[\"timestamp_start\"].append(start_time.timestamp.ToDatetime())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Longest execution times (slowest first)\n", "print(list(reversed(np.argsort(execution_stats[\"time\"]))))\n", "\n", "requests[1].remember_tool_call_data.tracing_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 0 - unknown (agent)\n", "# 1 - classify and distill\n", "# 2 - orientation\n", "\n", "Counter(execution_stats[\"caller\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(execution_stats[\"no_change\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\n", "    \"time\",\n", "    \"cur_memories_lines\",\n", "    \"cur_memories_chars\",\n", "    \"updated_memories_lines\",\n", "    \"updated_memories_chars\",\n", "    \"nonEmptyLines\",\n", "]:\n", "    plot_distribution(execution_stats[k], title=k, xlabel=k)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x, y in [\n", "    (\"cur_memories_lines\", \"time\"),\n", "    (\"cur_memories_chars\", \"time\"),\n", "    (\"nonEmptyLines\", \"time\"),\n", "    (\"cur_memories_lines\", \"updated_memories_lines\"),\n", "    (\"cur_memories_chars\", \"updated_memories_chars\"),\n", "]:\n", "    plot_scatter(\n", "        execution_stats[x], execution_stats[y], title=f\"{x} vs {y}\", xlabel=x, ylabel=y\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x, y in [(\"timestamp_start\", \"time\")]:\n", "    plot_scatter(\n", "        execution_stats[x], execution_stats[y], title=f\"{x} vs {y}\", xlabel=x, ylabel=y\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["compression_requests = [\n", "    r\n", "    for r in successful_requests\n", "    if r.remember_tool_call_data.tracing_data.flags[\"compressionStarted\"].value\n", "]\n", "successful_compression_requests = []\n", "failed_compression_requests = []\n", "\n", "for r in compression_requests:\n", "    if r.remember_tool_call_data.tracing_data.flags[\"compressionFailed\"].value:\n", "        failed_compression_requests.append(r)\n", "    else:\n", "        successful_compression_requests.append(r)\n", "\n", "(\n", "    len(successful_compression_requests),\n", "    len(failed_compression_requests),\n", "    len(compression_requests),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for r in successful_compression_requests:\n", "#     print(\n", "#         r.remember_tool_call_data.tracing_data.request_ids[\n", "#             \"compressionRequestId\"\n", "#         ].value\n", "#     )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["compression_stats = {\n", "    \"time\": [],\n", "    \"updated_memories_lines\": [],\n", "    \"updated_memories_chars\": [],\n", "    \"compressed_memories_lines\": [],\n", "    \"compressed_memories_chars\": [],\n", "}\n", "\n", "for request in successful_compression_requests:\n", "    start_time = request.remember_tool_call_data.tracing_data.flags[\n", "        \"compressionStarted\"\n", "    ]\n", "    end_time = request.remember_tool_call_data.tracing_data.string_stats[\n", "        \"compressedMemoriesStats\"\n", "    ]\n", "\n", "    compression_stats[\"time\"].append(end_time.timestamp.seconds - start_time.timestamp.seconds)\n", "    compression_stats[\"updated_memories_lines\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"injectionUpdatedMemoriesStats\"\n", "        ].value.num_lines\n", "    )\n", "    compression_stats[\"updated_memories_chars\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"injectionUpdatedMemoriesStats\"\n", "        ].value.num_chars\n", "    )\n", "    compression_stats[\"compressed_memories_lines\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"compressedMemoriesStats\"\n", "        ].value.num_lines\n", "    )\n", "    compression_stats[\"compressed_memories_chars\"].append(\n", "        request.remember_tool_call_data.tracing_data.string_stats[\n", "            \"compressedMemoriesStats\"\n", "        ].value.num_chars\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\"time\"]:\n", "    plot_distribution(compression_stats[k], title=k, xlabel=k)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x, y in [\n", "    (\"updated_memories_lines\", \"compressed_memories_lines\"),\n", "    (\"updated_memories_chars\", \"compressed_memories_chars\"),\n", "]:\n", "    plot_scatter(\n", "        compression_stats[x],\n", "        compression_stats[y],\n", "        title=f\"{x} vs {y}\",\n", "        xlabel=x,\n", "        ylabel=y,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Classify and distill"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["requests, raw_requests = fetch_requests_requests(\"classify-and-distill\")\n", "len(requests), requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Shuffle requests\n", "requests, raw_requests = shuffle_paired_lists(requests, raw_requests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_session_ids = []\n", "for raw_request in raw_requests:\n", "    _session_ids.append(raw_request[\"session_id\"])\n", "\n", "total_unique_session_ids = len(set(_session_ids))\n", "total_requests = len(_session_ids)\n", "avg_requests_per_session = total_requests / total_unique_session_ids\n", "\n", "print(f\"\"\"\n", "Total unique session IDs: {total_unique_session_ids}\n", "Total requests: {total_requests}\n", "Avg requests per session: {avg_requests_per_session:.2f}\n", "\"\"\")\n", "\n", "plot_distribution(\n", "    list(Counter(_session_ids).values()),\n", "    title=\"Number of requests per session\",\n", "    xlabel=\"Number of requests\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["successful_requests = []\n", "failed_requests = []\n", "for request in requests:\n", "    if request.classify_and_distill_data.tracing_data.flags[\"exceptionThrown\"].value:\n", "        failed_requests.append(request)\n", "    else:\n", "        successful_requests.append(request)\n", "\n", "len(successful_requests), len(failed_requests), len(requests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(map(lambda x: x.user_agent, requests))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(map(lambda x: x.user_agent, failed_requests))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(map(lambda x: x.user_agent, successful_requests))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["failed_requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fail_types = {\n", "    \"invalidResponse\": [],\n", "    \"parsingFailed\": [],\n", "    \"unknown\": [],\n", "}\n", "for request in failed_requests:\n", "    if request.classify_and_distill_data.tracing_data.flags[\"invalidResponse\"].value:\n", "        fail_types[\"invalidResponse\"].append(request)\n", "    elif (\n", "        request.classify_and_distill_data.tracing_data.string_stats[\n", "            \"sendSilentExchangeResponseStats\"\n", "        ].ByteSize()\n", "        > 0\n", "        and request.classify_and_distill_data.tracing_data.string_stats[\n", "            \"explanationStats\"\n", "        ].ByteSize()\n", "        == 0\n", "    ):\n", "        fail_types[\"parsingFailed\"].append(request)\n", "    else:\n", "        fail_types[\"unknown\"].append(request)\n", "\n", "(\n", "    len(fail_types[\"invalidResponse\"]),\n", "    len(fail_types[\"parsingFailed\"]),\n", "    len(fail_types[\"unknown\"]),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stats = {\n", "    \"time\": [],\n", "    \"worthRemembering\": [],\n", "    \"lastUserExchangeRequestId\": [],\n", "    \"contentNumLines\": [],\n", "    \"contentNumChars\": [],\n", "    \"explanationNumLines\": [],\n", "    \"explanationNumChars\": [],\n", "}\n", "for i, request in enumerate(successful_requests):\n", "    start_time = request.classify_and_distill_data.tracing_data.flags[\"start\"]\n", "    end_time = request.classify_and_distill_data.tracing_data.flags[\"end\"]\n", "\n", "    stats[\"time\"].append(end_time.timestamp.seconds - start_time.timestamp.seconds)\n", "    stats[\"worthRemembering\"].append(\n", "        request.classify_and_distill_data.tracing_data.flags[\"worthRemembering\"].value\n", "    )\n", "    if stats[\"worthRemembering\"][-1]:\n", "        stats[\"lastUserExchangeRequestId\"].append(\n", "            request.classify_and_distill_data.tracing_data.request_ids[\n", "                \"lastUserExchangeRequestId\"\n", "            ].value\n", "        )\n", "        # TODO: figure out why it happens sometimes\n", "        if (\n", "            request.classify_and_distill_data.tracing_data.request_ids[\n", "                \"lastUserExchangeRequestId\"\n", "            ].ByteSize()\n", "            == 0\n", "        ):\n", "            print(f\"Empty request ID for {i}\")\n", "        stats[\"contentNumLines\"].append(\n", "            request.classify_and_distill_data.tracing_data.string_stats[\n", "                \"contentStats\"\n", "            ].value.num_lines\n", "        )\n", "        stats[\"contentNumChars\"].append(\n", "            request.classify_and_distill_data.tracing_data.string_stats[\n", "                \"contentStats\"\n", "            ].value.num_chars\n", "        )\n", "        stats[\"explanationNumLines\"].append(\n", "            request.classify_and_distill_data.tracing_data.string_stats[\n", "                \"explanationStats\"\n", "            ].value.num_lines\n", "        )\n", "        stats[\"explanationNumChars\"].append(\n", "            request.classify_and_distill_data.tracing_data.string_stats[\n", "                \"explanationStats\"\n", "            ].value.num_chars\n", "        )\n", "    else:\n", "        stats[\"lastUserExchangeRequestId\"].append(None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(stats[\"worthRemembering\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\n", "    \"time\",\n", "    \"contentNumLines\",\n", "    \"contentNumChars\",\n", "    \"explanationNumLines\",\n", "    \"explanationNumChars\",\n", "]:\n", "    plot_distribution(stats[k], title=k, xlabel=k)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token_metrics = []\n", "successful_requests_metrics = []\n", "\n", "# Note: requests were shuffled before\n", "for request in tqdm(successful_requests[:100]):\n", "    assert (\n", "        request.classify_and_distill_data.tracing_data.request_ids[\n", "            \"sendSilentExchangeRequestId\"\n", "        ].ByteSize()\n", "        > 0\n", "    )\n", "\n", "    request_id = request.classify_and_distill_data.tracing_data.request_ids[\n", "        \"sendSilentExchangeRequestId\"\n", "    ].value\n", "\n", "    cur_metrics = extract_token_metrics(request_id)\n", "    if any(\n", "        k not in cur_metrics\n", "        for k in [\n", "            \"total input_tokens\",\n", "            \"input_tokens\",\n", "            \"cache_read_input_tokens\",\n", "            \"cache_creation_input_tokens\",\n", "            \"output_tokens\",\n", "        ]\n", "    ):\n", "        print(f\"Missing keys for {request_id}. Skipping...\")\n", "        continue\n", "    token_metrics.append(cur_metrics)\n", "    successful_requests_metrics.append(request)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, v in enumerate(token_metrics):\n", "    try:\n", "        if (\n", "            v[\"total input_tokens\"]\n", "            != v[\"cache_read_input_tokens\"]\n", "            + v[\"cache_creation_input_tokens\"]\n", "            + v[\"input_tokens\"]\n", "        ):\n", "            print(f\"\"\"\n", "[{i}]: {v['total input_tokens']} != {v['cache_read_input_tokens']} + {v['cache_creation_input_tokens'] + v['input_tokens']}\n", "\"\"\")\n", "\n", "        v_price = (\n", "            v[\"cache_creation_input_tokens\"] * 3.75 / 1_000_000,\n", "            v[\"cache_read_input_tokens\"] * 0.3 / 1_000_000,\n", "            v[\"input_tokens\"] * 3 / 1_000_000,\n", "            v[\"output_tokens\"] * 15 / 1_000_000,\n", "        )\n", "        v[\"price\"] = sum(v_price)\n", "    except KeyError:\n", "        print(f\"Missing keys for {i}\")\n", "        continue\n", "\n", "total_price = sum(list(map(lambda x: x[\"price\"], token_metrics)))\n", "avg = total_price / len(token_metrics)\n", "print(f\"Total price: {total_price:.4f}, avg: {avg:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Most expensive requests\n", "# Search in BQ: https://console.cloud.google.com/logs/query;query=087c7344-77ad-4d0a-93ee-60a6450c64ce%0Aresource.labels.container_name%3D%22chat%22;cursorTimestamp=2025-03-20T17:07:45.277647Z;duration=P2D?referrer=search&hl=en&project=system-services-prod\n", "print(list(reversed(np.argsort([m[\"price\"] for m in token_metrics]))))\n", "\n", "(\n", "    token_metrics[64],\n", "    successful_requests_metrics[64].classify_and_distill_data.tracing_data,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\n", "    \"total input_tokens\",\n", "    \"input_tokens\",\n", "    \"cache_read_input_tokens\",\n", "    \"cache_creation_input_tokens\",\n", "    \"cache_ratio\",\n", "    \"output_tokens\",\n", "    \"price\",\n", "]:\n", "    plot_distribution([m[k] for m in token_metrics if k in m], title=k, xlabel=k)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Flush memories"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["requests, _ = fetch_requests_requests(\"flush-memories\")\n", "len(requests), requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["successful_requests = []\n", "failed_requests = []\n", "for request in requests:\n", "    if request.flush_memories_data.tracing_data.flags[\"exceptionThrown\"].value:\n", "        failed_requests.append(request)\n", "    else:\n", "        successful_requests.append(request)\n", "\n", "len(successful_requests), len(failed_requests), len(requests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stats = {\n", "    \"isEmptyMemory\": [],\n", "    \"isAgeneticTurnHasRememberToolCall\": [],\n", "    \"time\": [],\n", "    \"lastUserExchangeRequestId\": [],\n", "}\n", "\n", "for request in successful_requests:\n", "    stats[\"isEmptyMemory\"].append(\n", "        request.flush_memories_data.tracing_data.flags[\"emptyMemory\"].value\n", "    )\n", "\n", "    stats[\"isAgeneticTurnHasRememberToolCall\"].append(\n", "        request.flush_memories_data.tracing_data.flags[\n", "            \"agenticTurnHasRememberToolCall\"\n", "        ].value\n", "    )\n", "    start_time = request.flush_memories_data.tracing_data.flags[\"start\"]\n", "    end_time = request.flush_memories_data.tracing_data.flags[\"end\"]\n", "\n", "    stats[\"time\"].append(end_time.timestamp.seconds - start_time.timestamp.seconds)\n", "\n", "    if (\n", "        request.flush_memories_data.tracing_data.request_ids[\n", "            \"lastUserExchangeRequestId\"\n", "        ].ByteSize()\n", "        > 0\n", "    ):\n", "        stats[\"lastUserExchangeRequestId\"].append(\n", "            request.flush_memories_data.tracing_data.request_ids[\n", "                \"lastUserExchangeRequestId\"\n", "            ].value\n", "        )\n", "    else:\n", "        stats[\"lastUserExchangeRequestId\"].append(None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# There was a bug where we were calling flush memories multiple times with same memory (when reloading vscode)\n", "# In PR (???), we added removal of memory from exchange after tool was called. So here should never be duplicates\n", "(\n", "    None in stats[\"lastUserExchangeRequestId\"],\n", "    Counter(stats[\"lastUserExchangeRequestId\"]).most_common(10),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Counter(stats[\"isEmptyMemory\"]), Counter(stats[\"isAgeneticTurnHasRememberToolCall\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\"time\"]:\n", "    plot_distribution(stats[k], title=k, xlabel=k)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Memories file open"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["requests, raw_requests = fetch_requests_requests(\"opened-memories-file\")\n", "len(requests), requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["session_id_to_memory_open = defaultdict(int)\n", "for raw_request in raw_requests:\n", "    session_id = raw_request[\"session_id\"]\n", "    session_id_to_memory_open[session_id] += 1\n", "\n", "print(len(session_id_to_memory_open.keys()))\n", "session_id_to_memory_open"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_distribution(\n", "    list(session_id_to_memory_open.values()),\n", "    title=\"Number of times memories file was opened\",\n", "    xlabel=\"Number of times memories file was opened\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Orientation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["requests, raw_requests = fetch_requests_requests(\"initial-orientation\")\n", "len(requests), requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["successful_requests = []\n", "failed_requests = []\n", "for request in requests:\n", "    if (\n", "        request.initial_orientation_data.tracing_data.flags[\"exceptionThrown\"].value\n", "        or request.initial_orientation_data.tracing_data.flags[\n", "            \"rememberEnded\"\n", "        ].ByteSize()\n", "        == 0\n", "    ):\n", "        failed_requests.append(request)\n", "    else:\n", "        successful_requests.append(request)\n", "\n", "len(successful_requests), len(failed_requests), len(requests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stats = {\n", "    \"time\": [],\n", "    \"caller\": [],\n", "}\n", "\n", "for request in successful_requests:\n", "    start_time = request.initial_orientation_data.tracing_data.flags[\"start\"]\n", "    end_time = request.initial_orientation_data.tracing_data.flags[\"end\"]\n", "\n", "    stats[\"time\"].append(end_time.timestamp.seconds - start_time.timestamp.seconds)\n", "    stats[\"caller\"].append(request.initial_orientation_data.caller)\n", "\n", "Counter(stats[\"caller\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\"time\"]:\n", "    plot_distribution(stats[k], title=k, xlabel=k)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# General agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tenant_filter = \"\"\n", "if len(TENANT_NAME) > 0:\n", "    tenant_filter = f\"AND tenant = '{TENANT_NAME}'\"\n", "\n", "query = f\"\"\"\n", "SELECT *   \n", "FROM `{PROJECT_ID}.{DATASET_NAME}.chat_host_request`\n", "WHERE time >= TIMESTAMP(\"{START_TIME}\")  \n", "  AND tenant = '{TENANT_NAME}'  \n", "  AND JSON_EXTRACT_SCALAR(sanitized_json, '$.request.model_name') IN (  \n", "    'claude-sonnet-3-7-200k-v2-agent',  \n", "    'claude-sonnet-3-7-200k-v2-direct-agent'  \n", "  )  \n", "ORDER BY time DESC  \n", "LIMIT {LIMIT}  \n", "\"\"\"\n", "print(query)\n", "gcp_creds, _ = get_gcp_creds(None)\n", "client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "query_job = client.query(query)\n", "dicts = list(map(lambda row: dict(row.items()), query_job.result()))\n", "\n", "random.shuffle(dicts)\n", "len(dicts), dicts[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token_metrics = []\n", "general_requests = []\n", "\n", "# Shuffle before\n", "for request in tqdm(dicts[:100]):\n", "    request_id = request[\"request_id\"]\n", "    cur_metrics = extract_token_metrics(request_id)\n", "    if any(\n", "        k not in cur_metrics\n", "        for k in [\n", "            \"total input_tokens\",\n", "            \"input_tokens\",\n", "            \"cache_read_input_tokens\",\n", "            \"cache_creation_input_tokens\",\n", "            \"output_tokens\",\n", "        ]\n", "    ):\n", "        print(f\"Missing keys for {request_id}. Skipping...\")\n", "        continue\n", "    cur_metrics[\"model_name\"] = request[\"sanitized_json\"][\"request\"][\"model_name\"]\n", "\n", "    token_metrics.append(cur_metrics)\n", "    general_requests.append(request)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, v in enumerate(token_metrics):\n", "    try:\n", "        if (\n", "            v[\"total input_tokens\"]\n", "            != v[\"cache_read_input_tokens\"]\n", "            + v[\"cache_creation_input_tokens\"]\n", "            + v[\"input_tokens\"]\n", "        ):\n", "            print(f\"\"\"\n", "[{i}]: {v['total input_tokens']} != {v['cache_read_input_tokens']} + {v['cache_creation_input_tokens'] + v['input_tokens']}\n", "\"\"\")\n", "\n", "        v_price = (\n", "            v[\"cache_creation_input_tokens\"] * 3.75 / 1_000_000,\n", "            v[\"cache_read_input_tokens\"] * 0.3 / 1_000_000,\n", "            v[\"input_tokens\"] * 3 / 1_000_000,\n", "            v[\"output_tokens\"] * 15 / 1_000_000,\n", "        )\n", "        v[\"price\"] = sum(v_price)\n", "    except KeyError:\n", "        print(f\"Missing keys for {i}\")\n", "        continue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\n", "    \"total input_tokens\",\n", "    \"input_tokens\",\n", "    \"cache_read_input_tokens\",\n", "    \"cache_creation_input_tokens\",\n", "    \"cache_ratio\",\n", "    \"output_tokens\",\n", "    \"price\",\n", "    \"model_name\",\n", "]:\n", "    plot_distribution([m[k] for m in token_metrics if k in m], title=k, xlabel=k)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
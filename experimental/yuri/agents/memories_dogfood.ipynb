{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# https://console.cloud.google.com/bigquery?project=system-services-prod&ws=!1m14!1m4!4m3!1ssystem-services-prod!2sprod_request_insight_full_export_dataset!3schat_host_request!1m4!1m3!1ssystem-services-prod!2sbquxjob_3e7d4da3_194e2a6b5f1!3sUS!1m3!3m2!1ssystem-services-prod!2sus_staging_request_insight_search_all_tenants_dataset\n", "# SELECT\n", "#     request_id,\n", "#     tenant_id,\n", "#     tenant,\n", "#     time\n", "# FROM `us_staging_request_insight_search_nonenterprise_dataset.request_event`\n", "# WHERE\n", "#     event_type = 'chat_host_request'\n", "#     AND tenant = 'dogfood-shard'  -- Added this line to filter for dogfood-shard\n", "#     AND time >= TIMESTAMP('2025-01-29 01:00:00 UTC')  -- This is 5 PM PST on Jan 28, 2025\n", "# ORDER BY time DESC\n", "# LIMIT 100000  -- Adjust this limit as needed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import multiprocessing\n", "import random\n", "\n", "from base.datasets.gcs_client import GCSRequestInsightFetcher\n", "from pathlib import Path\n", "from collections import Counter\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "from services.chat_host.chat_proto_util import request_to_message, response_to_message\n", "from services.chat_host.server.chat_third_party_handler import (\n", "    PromptExchange,\n", "    ToolDefinition,\n", ")\n", "from services.lib.retrieval.retriever import RetrievalResult\n", "\n", "from tqdm import tqdm\n", "from base.third_party_clients.anthropic_direct_client import UnavailableRpcError\n", "\n", "\n", "MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    \"augment-387916\",\n", "    \"us-east5\",\n", "    MODEL_NAME,\n", "    0,\n", "    1024 * 5,\n", ")\n", "\n", "# dogfood\n", "GCS_FETCHER = GCSRequestInsightFetcher.from_tenant_id(\n", "    project=\"system-services-prod\",\n", "    bucket_name=\"us-staging-request-insight-events-nonenterprise\",\n", "    tenant_id=\"352a91ac7d4283558ccfbc094a527746\",\n", ")\n", "\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 60,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=0,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 70,\n", "    inject_current_file_into_retrievals=True,\n", "    tool_results_len=1024 * 120,\n", "    retrieval_as_tool=True,\n", ")\n", "PROMPT_FORMATTER = get_structured_chat_prompt_formatter_by_name(\n", "    \"binks-claude-v13\", TOKEN_APPORTIONMENT\n", ")\n", "\n", "\n", "BQ_EXPORT_PATH = Path(\n", "    \"/home/<USER>/repos/augment/experimental/yuri/agents/bquxjob_1f3a1c56_195257d3373.json\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT_V1 = \"\"\"Here is the last message from the user:\n", "```\n", "{message}\n", "```\n", "\n", "Your task is to detect if the last message contains some information worth remembering in long-term.\n", "Information is worth remembering if in the last message user gives some new information, asks to do something differently or describes user preferences.\n", "Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!\n", "Return JSON with three keys (in provided order): \"explanation\" (str), \"worthRemembering\" (bool) and \"content\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes why the information is worth remembering or not.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "If \"worthRemembering\" is False, then \"content\" should be empty.\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "Example: {{\"explanation\": \"some explanation\", \"worthRemembering\": true or false, \"content\": \"memory content\"}}\"\"\"\n", "\n", "\n", "PROMPT_V2 = \"\"\"Here is the last message from the user:\n", "```\n", "{message}\n", "```\n", "\n", "Your task is to detect if the last message contains some information worth remembering in long-term.\n", "Information is worth remembering if all of these are true:\n", " - User corrects assistant and asks it to use different framework\n", " - Information is relevant NOT only to the current task, but will be clearly useful for other tasks in the future\n", " - It will be very useful for future tasks\n", "\n", "If information/user request is clearly specific to the current task / just asks to change something in specific task, then it is NOT worth remembering.\n", "\n", "Return JSON with three keys (in provided order): \"explanation\" (str), \"worthRemembering\" (bool) and \"content\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes why the information is worth remembering or not.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "If \"worthRemembering\" is False, then \"content\" should be empty.\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "Example: {{\"explanation\": \"some explanation\", \"worthRemembering\": true or false, \"content\": \"memory content\"}}\"\"\"\n", "\n", "\n", "PROMPT_V3 = \"\"\"Here is the last message from the user:\n", "```\n", "{message}\n", "```\n", "\n", "Your task is to detect if the last message contains information worth remembering in long-term.\n", "Information is worth remembering if:\n", " - In last message user asks assistant to use different framework/library instead of the one assistant just used\n", "\n", "Return JSON with three keys (in provided order): \"explanation\" (str), \"worthRemembering\" (bool) and \"content\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes why the information is worth remembering or not.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "If \"worthRemembering\" is False, then \"content\" should be empty.\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "Example: {{\"explanation\": \"some explanation\", \"worthRemembering\": true or false, \"content\": \"memory content\"}}\"\"\"\n", "\n", "\n", "PROMPT_V4 = \"\"\"Here is the last message from the user:\n", "```\n", "{message}\n", "```\n", "\n", "Your task is to detect if in the last message user explicitly mentiones their preferences: tools, libraries, coding styles or design approaches.\n", "\n", "Return JSON with three keys (in provided order): \"explanation\" (str), \"isPreference\" (bool) and \"contentOfPreference\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes reasoning.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "If \"isPreference\" is False, then \"contentOfPreference\" should be empty.\n", "\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "Example: {{\"explanation\": \"some explanation\", \"isPreference\": true or false, \"contentOfPreference\": \"memory content\"}}\"\"\"\n", "\n", "\n", "PROMPT_V5 = \"\"\"Here is the last message from the user:\n", "```\n", "{message}\n", "```\n", "\n", "Your task is to detect if in the last message the user explicitly expresses a long-term coding preference — such as a habitual choice of tools, libraries, coding styles, or design approaches — rather than a one-off or task-specific instruction. \n", "Only classify a statement as a preference if it indicates a recurring or general approach (e.g., using \"always\", \"usually\", or \"prefer to\") rather than instructions meant only for the current task.\n", "\n", "Return JSON with exactly three keys (in the provided order): \"explanation\" (a one-sentence explanation of your reasoning), \"isPreference\" (a boolean indicating if a long-term preference was detected), and \"contentOfPreference\" (a one-sentence summary of the preference, or an empty string if none).\n", "\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "Example: {{\"explanation\": \"The user consistently prefers to use X for Y\", \"isPreference\": true, \"contentOfPreference\": \"Prefers to use X for Y tasks\"}}\n", "\n", "\"\"\"\n", "\n", "\n", "PROMPT_V6 = \"\"\"Here is the last message from the user:\n", "```\n", "{message}\n", "```\n", "\n", "Your task is to determine if the user's message contains information that should be remembered long-term. Only consider information as worth remembering if it meets all of the following criteria:\n", "1. It introduces new, recurring, or persistent context (e.g., long-term coding preferences, stable policies, or recurring design/technology choices).\n", "2. It is directly relevant to the codebase, internal conventions, or preferred technologies/patterns.\n", "3. It is not a one-time instruction, temporary UI arrangement, debug detail, or a correction that may change quickly.\n", "\n", "Return JSON with three keys (in provided order): \"explanation\" (str), \"worthRemembering\" (bool) and \"content\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes why the information is worth remembering or not.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "If \"worthRemembering\" is False, then \"content\" should be empty.\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "Example: {{\"explanation\": \"some explanation\", \"worthRemembering\": true or false, \"content\": \"memory content\"}}\"\"\"\n", "\n", "\n", "PROMPT_V7 = \"\"\"Here is the last message from the user:\n", "```\n", "{message}\n", "```\n", "\n", "Your task is to determine if the user's message contains information that should be remembered long-term. Only consider information as worth remembering if it meets all of the following criteria:\n", "1. It introduces new, recurring, or persistent context (e.g., long-term coding preferences, stable policies, or recurring design/technology choices).\n", "2. It is directly relevant to the codebase, internal conventions, or preferred technologies/patterns.\n", "3. It is not a one-time instruction, temporary UI arrangement, debug detail, or a correction that may change quickly.\n", "\n", "Return JSON with three keys (in provided order): \"explanation\" (str), \"worthRememberingRating\" (int - [0, 5]) and \"content\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes why the information is worth remembering or not.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "\"worthRememberingRating\" is value from 0 to 5 (inclusive). The bigger the number, the more information satfisfies criterias.\n", "(0 - no need to remember, 5 - absolutely necessary to remember). \n", "\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "Example: {{\"explanation\": \"some explanation\", \"worthRememberingRating\": (int - [0, 5]), \"content\": \"memory content\"}}\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bq_export = json.loads(BQ_EXPORT_PATH.read_text())\n", "random.seed(42)\n", "request_ids = random.sample([bq_row[\"request_id\"] for bq_row in bq_export], 500)\n", "# request_ids = random.sample([bq_row[\"request_id\"] for bq_row in bq_export], 2000)\n", "requests = list(GCS_FETCHER.get_requests(request_ids))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["legit_requests = []\n", "user_to_requests = {}\n", "\n", "for request in requests:\n", "    try:\n", "        chat_host_request = [\n", "            event for event in request.events if event.HasField(\"chat_host_request\")\n", "        ][0].chat_host_request\n", "        request_metadata = [\n", "            event for event in request.events if event.HasField(\"request_metadata\")\n", "        ][0].request_metadata\n", "\n", "        if (\n", "            \"Please provide a clear and concise summary of our conversation so far.\"\n", "            in chat_host_request.request.message\n", "        ):\n", "            continue\n", "        if \"Here are the memories already saved:\" in chat_host_request.request.message:\n", "            continue\n", "        if (\n", "            \"Here is the last message from the user:\"\n", "            in chat_host_request.request.message\n", "        ):\n", "            continue\n", "        if \"Basic context:\" in chat_host_request.request.message:\n", "            continue\n", "        # if chat_host_request.request.model_name != \"claude-sonnet-3-5-200k-v13-agent-chat\":\n", "        #     print(f\"Model {chat_host_request.request.model_name}\")\n", "        #     continue\n", "        legit_requests.append(request)\n", "\n", "        user_id = request_metadata.user_id\n", "        if user_id not in user_to_requests:\n", "            user_to_requests[user_id] = []\n", "        user_to_requests[user_id].append(request)\n", "    except Exception as e:\n", "        print(f\"Error processing request {request.request_id}: {e}\")\n", "\n", "\n", "len(requests), len(legit_requests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["legit_requests[0].events[1].chat_host_request.request"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["legit_requests[0].events[2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_message_requests = []\n", "\n", "stats = {\n", "    \"multiple_host_requests\": 0,\n", "    \"message_present\": 0,\n", "    \"no_text_node\": 0,\n", "    \"text_node\": 0,\n", "    \"multiple_chat_host_requests\": 0,\n", "}\n", "\n", "for request in legit_requests:\n", "    chat_host_requests = [\n", "        event for event in request.events if event.HasField(\"chat_host_request\")\n", "    ]\n", "    if len(chat_host_requests) != 1:\n", "        stats[\"multiple_chat_host_requests\"] += 1\n", "        continue\n", "    chat_host_request = chat_host_requests[0].chat_host_request.request\n", "\n", "    if len(chat_host_request.message) > 0:\n", "        assert 0 not in [n.type for n in chat_host_request.nodes]\n", "        user_message_requests.append(chat_host_request)\n", "        stats[\"message_present\"] += 1\n", "        continue\n", "\n", "    text_nodes = [n for n in chat_host_request.nodes if n.type == 0]\n", "\n", "    if len(text_nodes) == 0:\n", "        stats[\"no_text_node\"] += 1\n", "        continue\n", "\n", "    assert len(text_nodes) == 1, len(text_nodes)\n", "    assert len(text_nodes[0].text_node.content) > 0\n", "    user_message_requests.append(chat_host_request)\n", "\n", "len(user_message_requests)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_message_requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process(request, prompt):\n", "    retrieved_chunks = []\n", "\n", "    # History\n", "    chat_history = [\n", "        PromptExchange(\n", "            request_message=request_to_message(\n", "                item.request_message,\n", "                item.request_nodes,\n", "            ),\n", "            response_text=response_to_message(\n", "                item.response_text,\n", "                item.response_nodes,\n", "            ),\n", "            request_id=item.request_id if item.HasField(\"request_id\") else None,\n", "        )\n", "        for item in request.chat_history\n", "    ]\n", "\n", "    message = prompt.format(message=request_to_message(request.message, request.nodes))\n", "\n", "    # Prompt formatting\n", "    prompt_input = ChatPromptInput(\n", "        path=request.path,\n", "        prefix=request.prefix,\n", "        selected_code=request.selected_code,\n", "        suffix=request.suffix,\n", "        message=message,\n", "        chat_history=chat_history,\n", "        prefix_begin=request.position.prefix_begin,\n", "        suffix_end=request.position.suffix_end,\n", "        retrieved_chunks=retrieved_chunks,\n", "        context_code_exchange_request_id=(\n", "            request.context_code_exchange_request_id\n", "            if len(request.context_code_exchange_request_id) > 0\n", "            else None\n", "        ),\n", "        diff=request.diff,\n", "        relevant_commit_messages=list(request.relevant_commit_messages),\n", "        example_commit_messages=list(request.example_commit_messages),\n", "        workspace_guidelines=request.workspace_guidelines,\n", "        user_guidelines=request.user_guidelines,\n", "    )\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    tool_definitions = [\n", "        ToolDefinition(\n", "            tool.name,\n", "            tool.description,\n", "            tool.input_schema_json,\n", "        )\n", "        for tool in request.tool_definitions\n", "    ]\n", "    tools = []\n", "\n", "    response_iterator = ANTHROPIC_CLIENT.generate_response_stream(\n", "        cur_message=prompt_output.message,\n", "        chat_history=list(prompt_output.chat_history),\n", "        system_prompt=prompt_output.system_prompt,\n", "        tools=tools,\n", "        tool_definitions=tool_definitions,\n", "    )\n", "    full_response = \"\"\n", "    for response in response_iterator:\n", "        full_response += response.text\n", "\n", "    return full_response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_request(args):\n", "    request, prompt = args\n", "    try:\n", "        return request, process(request, prompt)\n", "    except UnavailableRpcError:\n", "        return request, \"\"\n", "    except Exception:\n", "        return request, \"\"\n", "\n", "\n", "# Prepare the arguments for parallel processing\n", "args = [(request, PROMPT_V7) for request in user_message_requests]\n", "\n", "# Process requests in parallel using context managers for both pool and progress bar\n", "results = []\n", "with (\n", "    multiprocessing.Pool(processes=16) as pool,\n", "    tqdm(total=len(args), desc=\"Processing requests\") as pbar,\n", "):\n", "    for result in pool.imap_unordered(process_request, args):\n", "        results.append(result)\n", "        pbar.update()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remembered_results = []\n", "non_remembered = []\n", "\n", "for request, response in results:\n", "    if len(response) == 0:\n", "        print(\"Skipping empty\")\n", "        continue\n", "\n", "    try:\n", "        response_dict = json.loads(response)\n", "    except Exception:\n", "        print(\"Parsing\")\n", "        continue\n", "    # if response_dict[\"worthRemembering\"]:\n", "    # if response_dict[\"isPreference\"]:\n", "    if response_dict[\"worthRememberingRating\"] == 5:\n", "        remembered_results.append([request, response_dict])\n", "    else:\n", "        non_remembered.append([request, response_dict])\n", "\n", "len(remembered_results), len(non_remembered)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, (request, response) in enumerate(remembered_results):\n", "    print(f\"{i} \" + \"=\" * 30)\n", "    print(request.message)\n", "    print(\"\\n\\n\")\n", "    print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(remembered_results[1][0].message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remembered_results[0][0].message"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT_V1.format(message=\"AA\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sum_ide_percentages():\n", "    \"\"\"Sum the percentages of IDE usage.\"\"\"\n", "    percentages = {\n", "        \"Visual Studio Code\": 73.6,\n", "        \"Visual Studio\": 29.3,\n", "        \"IntelliJ IDEA\": 26.8,\n", "        \"Notepad++\": 23.9,\n", "        \"Vim\": 21.6,\n", "        \"Android Studio\": 16.1,\n", "        \"PyCharm\": 15.1,\n", "        \"Jupyter Notebook/JupyterLab\": 12.8,\n", "        \"Neovim\": 12.5,\n", "        \"Sublime Text\": 10.9,\n", "        \"Eclipse\": 9.4,\n", "        \"Xcode\": 9.3,\n", "        \"Nano\": 9.2,\n", "        \"WebStorm\": 7.5,\n", "        \"PhpStorm\": 5.9,\n", "        \"Rider\": 5.7,\n", "        \"DataGrip\": 5.1,\n", "        \"VSCodium\": 4.8,\n", "        \"IPython\": 4.7,\n", "        \"CLion\": 4.6,\n", "        \"Emacs\": 4.2,\n", "        \"Goland\": 3.3,\n", "        \"Netbeans\": 2.9,\n", "        \"RStudio\": 2.8,\n", "        \"Qt Creator\": 2.7,\n", "        \"Code::Blocks\": 2.1,\n", "        \"Kate\": 1.8,\n", "        \"Fleet\": 1.7,\n", "        \"Helix\": 1.7,\n", "        \"Spyder\": 1.4,\n", "        \"RubyMine\": 1.1,\n", "        \"Geany\": 1.0,\n", "        \"Rad Studio\": 1.0,\n", "        \"BBEdit\": 0.9,\n", "        \"Spacemacs\": 0.4,\n", "    }\n", "\n", "    return sum(percentages.values())\n", "\n", "\n", "# Calculate and print the sum\n", "total = sum_ide_percentages()\n", "print(f\"Total percentage: {total:.1f}%\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
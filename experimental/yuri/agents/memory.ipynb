{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "import copy\n", "import random\n", "\n", "from pathlib import Path\n", "from research.agents.tools import LoggedToolCall\n", "from research.agents.tools import LoggedLanguageModelCall\n", "from research.agents.tools import DialogMessages\n", "from research.llm_apis.llm_client import (\n", "    TextPrompt,\n", "    Text<PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    ToolFormattedResult,\n", ")\n", "from research.llm_apis.llm_client import AnthropicDirectClient\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CLIENT = AnthropicDirectClient(model_name=\"claude-3-5-sonnet-20241022\", max_retries=50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_raw_logs(logs):\n", "    for i, log in enumerate(logs):\n", "        print(f\"{i}\" + \"=\" * 20)\n", "        if isinstance(log, LoggedToolCall):\n", "            print(log.tool)\n", "        elif isinstance(log, LoggedLanguageModelCall):\n", "            if log.started:\n", "                print(log.messages[-1])\n", "            else:\n", "                print(log.response)\n", "        else:\n", "            assert False\n", "\n", "\n", "def dialog_to_str(dialog: DialogMessages):\n", "    result = \"\"\n", "    for i, message_list in enumerate(dialog.get_messages_for_llm_client()):\n", "        if i % 2 == 0:\n", "            result += f\"<user message_number={i // 2}>\\n\"\n", "            for msg in message_list:\n", "                if isinstance(msg, TextPrompt):\n", "                    result += msg.text + \"\\n\"\n", "                elif isinstance(msg, ToolFormattedResult):\n", "                    result += f\"Tool ({msg.tool_name}): {msg.tool_output}\\n\"\n", "                else:\n", "                    raise ValueError(f\"Unknown message type: {type(msg)}\")\n", "            result += f\"</user message_number={i // 2}>\\n\\n\"\n", "        else:\n", "            result += f\"<assistant message_number={i // 2}>\\n\"\n", "            for msg in message_list:\n", "                if isinstance(msg, TextResult):\n", "                    result += msg.text + \"\\n\"\n", "                elif isinstance(msg, ToolCall):\n", "                    result += f\"Tool call: {msg.tool_name} {msg.tool_input}\\n\"\n", "                else:\n", "                    raise ValueError(f\"Unknown message type: {type(msg)}\")\n", "            result += f\"</assistant message_number={i // 2}>\\n\\n\"\n", "    return result\n", "\n", "\n", "def get_samples_from_logs(logs):\n", "    samples = []\n", "\n", "    # tool_stack = []\n", "\n", "    for i, log in enumerate(logs):\n", "        if isinstance(log, LoggedToolCall):\n", "            # if log.started:\n", "            #     tool_stack.append(log.tool.name)\n", "            # else:\n", "            #     top_tool = tool_stack.pop()\n", "            #     assert top_tool == log.tool.name, f\"{top_tool} != {log.tool.name}, {i}\"\n", "            continue\n", "\n", "        assert isinstance(log, LoggedLanguageModelCall)\n", "\n", "        if log.started:\n", "            # print(f\"Skipping started {i}\")\n", "            continue\n", "\n", "        # assert len(tool_stack) > 0\n", "        # if tool_stack[-1] != \"general_agent\":\n", "        #     print(f\"Skipping non-general_agent {i}\")\n", "        #     continue\n", "\n", "        text_prompts = [\n", "            message for message in log.messages[-1] if isinstance(message, TextPrompt)\n", "        ]\n", "        if len(text_prompts) != 1:\n", "            # print(f\"Skipping non-single-text-prompt {i} (text_prompts={len(text_prompts)})\")\n", "            continue\n", "\n", "        # print(f\"Recording {i}\")\n", "\n", "        dialog = DialogMessages()\n", "        dialog._message_lists = log.messages\n", "\n", "        dialog._assert_assistant_turn()\n", "\n", "        samples.append(\n", "            {\n", "                \"dialog\": dialog,\n", "                \"response\": log.response,\n", "                \"response_metadata\": log.response_metadata,\n", "                \"max_tokens\": log.max_tokens,\n", "                \"system_prompt\": log.system_prompt,\n", "                \"temperature\": log.temperature,\n", "                \"tools\": log.tools,\n", "                \"tool_choice\": log.tool_choice,\n", "            }\n", "        )\n", "\n", "    return samples\n", "\n", "\n", "def process_sample(sample):\n", "    dialog = copy.deepcopy(sample[\"dialog\"])\n", "\n", "    last_user_message = dialog._message_lists.pop()\n", "    assert len(last_user_message) == 1\n", "    last_user_message = last_user_message[0]\n", "    assert isinstance(last_user_message, TextPrompt)\n", "    last_user_message = last_user_message.text\n", "\n", "    prompt = MEMORY_CLASSIFICATION_PROMPT.format(\n", "        dialog_str=dialog_to_str(dialog),\n", "        msg_num=len(dialog._message_lists) // 2,\n", "        last_user_message=last_user_message,\n", "    )\n", "\n", "    _, metadata = CLIENT.generate(\n", "        messages=[[TextPrompt(text=prompt)]],\n", "        max_tokens=sample[\"max_tokens\"],\n", "        system_prompt=sample[\"system_prompt\"],\n", "        temperature=sample[\"temperature\"],\n", "        tools=sample[\"tools\"],\n", "        tool_choice=sample[\"tool_choice\"],\n", "    )\n", "\n", "    assert len(metadata[\"raw_response\"].content) == 1\n", "\n", "    return {\n", "        \"prompt\": prompt,\n", "        \"response\": metadata[\"raw_response\"].content[0].text,\n", "        \"last_user_message\": last_user_message,\n", "        \"dialog\": dialog,\n", "    }\n", "\n", "\n", "def print_process_out(out):\n", "    print(\"Last user message \" + \"=\" * 20)\n", "    print(out[\"last_user_message\"])\n", "    print(\"Response \" + \"=\" * 20)\n", "    print(out[\"response\"])\n", "\n", "\n", "# MEMORY_CLASSIFICATION_PROMPT = \"\"\"{dialog_str}\n", "\n", "# Here is the last message from the user:\n", "# <user message_number={msg_num}>\n", "# {last_user_message}\n", "# </user message_number={msg_num}>\n", "\n", "# Your task is to detect if the last message from the user contains some hint or knowledge about the codebase, policies, preferred technologies or patterns, etc.\n", "# Return JSON with two keys: \"worth_remembering\" (bool) and \"content\" (str).\n", "# If \"worth_remembering\" is True, then \"content\" should contain the relevant information from the last message.\n", "# If \"worth_remembering\" is False, then \"content\" should be empty.\n", "\n", "# Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!\n", "# Return only <PERSON><PERSON><PERSON> and nothing else.\n", "# \"\"\"\n", "\n", "MEMORY_CLASSIFICATION_PROMPT = \"\"\"{dialog_str}\n", "\n", "Here is the last message from the user:\n", "<user message_number={msg_num}>\n", "{last_user_message}\n", "</user message_number={msg_num}>\n", "\n", "Your task is to detect if the last message contains some information worth remembering in long-term.\n", "Information is worth remembering if in the last information user gives some new information, asks to do something differently or describes user preferences.\n", "Information is only worth remembering if it might be useful in long-term and might be applicable to other tasks/situations, not just current one.\n", "\n", "Return JSON with three keys: \"explanation\" (str), \"worth_remembering\" (bool) and \"content\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes why the information is worth remembering or not.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "If \"worth_remembering\" is False, then \"content\" should be empty.\n", "\n", "Return only <PERSON><PERSON><PERSON> and nothing else.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LOGS_PATH = Path(\"/tmp/t.pickle\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(LOGS_PATH, \"rb\") as f:\n", "    logs = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = get_samples_from_logs(logs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out = process_sample(samples[3])\n", "print_process_out(out)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(out[\"prompt\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# guy_logs_files = random.sample(list(Path(\"/mnt/efs/augment/user/guy/agent_logs/guy\").glob(\"*.pickle\")), 500)\n", "guy_logs_files = list(Path(\"/mnt/efs/augment/user/guy/agent_logs/guy\").glob(\"*.pickle\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for path in tqdm(guy_logs_files):\n", "    try:\n", "        with open(path, \"rb\") as f:\n", "            logs = pickle.load(f)\n", "            samples = get_samples_from_logs(logs)\n", "            for i, sample in enumerate(samples):\n", "                out = process_sample(sample)\n", "                if \"true\" in out[\"response\"].lower():\n", "                    print(\"$\" * 20)\n", "                    print(path)\n", "                    print(f\"sample {i} \" + \">\" * 20)\n", "                    print_process_out(out)\n", "                    print(\"\\n\" * 2)\n", "    except Exception as e:\n", "        print(f\"Failed to process {path}: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from multiprocessing import Pool\n", "from tqdm import tqdm\n", "import pickle\n", "from functools import partial\n", "\n", "\n", "def process_file(path):\n", "    try:\n", "        with open(path, \"rb\") as f:\n", "            logs = pickle.load(f)\n", "\n", "        samples = get_samples_from_logs(logs)\n", "        results = []\n", "\n", "        for i, sample in enumerate(samples):\n", "            out = process_sample(sample)\n", "            if \"true\" in out[\"response\"].lower():\n", "                results.append({\"path\": path, \"sample_index\": i, \"output\": out})\n", "\n", "        return results\n", "    except Exception as e:\n", "        return [{\"error\": f\"Failed to process {path}: {e}\"}]\n", "\n", "\n", "def print_results(results):\n", "    for result in results:\n", "        if \"error\" in result:\n", "            # print(result['error'])\n", "            continue\n", "\n", "        print(\"$\" * 20)\n", "        print(result[\"path\"])\n", "        print(f\"sample {result['sample_index']} \" + \">\" * 20)\n", "        print_process_out(result[\"output\"])\n", "        print(\"\\n\" * 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_processes = 50\n", "\n", "# Create a process pool\n", "with Pool(processes=num_processes) as pool:\n", "    # Process files in parallel with progress bar\n", "    all_results = list(\n", "        tqdm(pool.imap(process_file, guy_logs_files), total=len(guy_logs_files))\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Flatten results list and print\n", "for results in all_results:\n", "    if results:  # Skip empty results\n", "        print_results(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
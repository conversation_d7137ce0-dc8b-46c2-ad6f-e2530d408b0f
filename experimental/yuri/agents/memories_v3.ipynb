{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import multiprocessing\n", "import json\n", "import random\n", "import re\n", "\n", "from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.gcs_client import GCSRequestInsightFetcher\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from tqdm import tqdm\n", "from services.chat_host.server.chat_third_party_handler import (\n", "    PromptExchange,\n", "    ToolDefinition,\n", "    VertexAiClient,\n", ")\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "from services.chat_host.chat_proto_util import request_to_message, response_to_message\n", "from base.third_party_clients.anthropic_direct_client import UnavailableRpcError\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"us_staging_request_insight_search_nonenterprise_dataset\"\n", "START_TIME = \"2025-01-29 01:00:00 UTC\"\n", "TENANT_NAME = \"dogfood-shard\"\n", "LIMIT = 2000\n", "\n", "\n", "# MODEL_NAME = \"claude-3-7-sonnet@20250219\"\n", "# MODEL_CLIENT = AnthropicVertexAiClient(\n", "#     \"augment-387916\",\n", "#     \"us-east5\",\n", "#     MODEL_NAME,\n", "#     0,\n", "#     1024 * 5,\n", "# )\n", "\n", "MODEL_NAME = \"gemini-2.0-flash-001\"\n", "MODEL_CLIENT = VertexAiClient(\n", "    \"augment-387916\",\n", "    \"us-east5\",\n", "    MODEL_NAME,\n", "    0,\n", "    1024 * 5,\n", ")\n", "\n", "\n", "GCS_FETCHER = GCSRequestInsightFetcher.from_tenant_id(\n", "    project=\"system-services-prod\",\n", "    bucket_name=\"us-staging-request-insight-events-nonenterprise\",\n", "    tenant_id=\"352a91ac7d4283558ccfbc094a527746\",\n", ")\n", "\n", "FILTER_OUT_MESSAGES = [\n", "    \"Please provide a clear and concise summary of our conversation so far.\",\n", "    \"Here are the memories already saved:\",\n", "    \"Here is the last message from the user:\",\n", "    \"Basic context:\",\n", "    \"Here are the full memories assembled\",\n", "]\n", "\n", "KEEP_ONLY_INJECTION_MESSAGES = [\n", "    \"Here are the memories already saved:\",\n", "]\n", "\n", "# Values from services/deploy/chatanol3_third_party_chat_deploy.jsonnet\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=0,\n", "    selected_code_len=0,\n", "    chat_history_len=0,\n", "    retrieval_len_per_each_user_guided_file=0,\n", "    retrieval_len_for_user_guided=0,\n", "    retrieval_len=0,\n", "    max_prompt_len=1024 * 200,\n", "    inject_current_file_into_retrievals=True,\n", "    tool_results_len=1024 * 120,\n", "    token_budget_to_trigger_truncation=1024 * 120,\n", ")\n", "\n", "\n", "PROMPT_FORMATTER = get_structured_chat_prompt_formatter_by_name(\n", "    \"agent-binks-claude-v2\", TOKEN_APPORTIONMENT\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fetch_dogfood_requests():\n", "    query = f\"\"\"\n", "    SELECT\n", "        request_id,\n", "        tenant_id,\n", "        tenant,\n", "        time\n", "    FROM `{DATASET_NAME}.request_event`\n", "    WHERE\n", "        event_type = 'chat_host_request'\n", "        AND tenant = '{TENANT_NAME}'\n", "        AND time >= TIMESTAMP('{START_TIME}')\n", "    ORDER BY time DESC\n", "    LIMIT {LIMIT}\n", "    \"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "    query_job = client.query(query)\n", "    return list(map(lambda row: dict(row.items()), query_job.result()))\n", "\n", "\n", "def filter_regular_requests(requests):\n", "    result_list = []\n", "\n", "    for request in tqdm(requests):\n", "        chat_host_request = get_chat_request(request)\n", "        if chat_host_request is None:\n", "            continue\n", "\n", "        if len(chat_host_request.message) == 0:\n", "            continue\n", "\n", "        message = get_message_text(chat_host_request)\n", "        if message is None:\n", "            continue\n", "        if any(msg in message for msg in FILTER_OUT_MESSAGES):\n", "            continue\n", "\n", "        result_list.append(request)\n", "\n", "    return result_list\n", "\n", "\n", "def filter_injection_requests(requests):\n", "    result_list = []\n", "\n", "    for request in tqdm(requests):\n", "        chat_host_request = get_chat_request(request)\n", "        if chat_host_request is None:\n", "            continue\n", "\n", "        if len(chat_host_request.message) == 0:\n", "            continue\n", "\n", "        message = get_message_text(chat_host_request)\n", "        if message is None:\n", "            continue\n", "        if not any(msg in message for msg in KEEP_ONLY_INJECTION_MESSAGES):\n", "            continue\n", "\n", "        result_list.append(request)\n", "\n", "    return result_list\n", "\n", "\n", "def get_chat_request(request):\n", "    chat_host_requests = [\n", "        event for event in request.events if event.HasField(\"chat_host_request\")\n", "    ]\n", "    if len(chat_host_requests) != 1:\n", "        return None\n", "    chat_host_request = chat_host_requests[0].chat_host_request.request\n", "    return chat_host_request\n", "\n", "\n", "def get_message_text(chat_request):\n", "    text_node = [node for node in chat_request.nodes if node.<PERSON><PERSON><PERSON>(\"text_node\")]\n", "\n", "    if len(text_node) == 0:\n", "        return chat_request.message\n", "\n", "    if len(text_node) != 1:\n", "        return None\n", "    return text_node[0].text_node.content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["requests_meta = fetch_dogfood_requests()\n", "# requests_meta = random.sample(requests_meta, 250)\n", "request_ids = [row[\"request_id\"] for row in requests_meta]\n", "request_ids = request_ids + [\n", "    # Additional requests for classify and distill\n", "    \"af4d4eb0-2e6a-40e0-923c-741dd60a578f\",\n", "    \"bbedc886-358e-4a47-ad4a-6385c87e8bc6\",\n", "]\n", "\n", "len(requests_meta), requests_meta[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["full_requests = list(GCS_FETCHER.get_requests(request_ids=request_ids))\n", "len(full_requests), full_requests[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Classify and distill"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process(request, prompt):\n", "    retrieved_chunks = []\n", "\n", "    # History\n", "    chat_history = [\n", "        PromptExchange(\n", "            request_message=request_to_message(\n", "                item.request_message,\n", "                item.request_nodes,\n", "            ),\n", "            response_text=response_to_message(\n", "                item.response_text,\n", "                item.response_nodes,\n", "            ),\n", "            request_id=item.request_id if item.HasField(\"request_id\") else None,\n", "        )\n", "        for item in request.chat_history\n", "    ]\n", "\n", "    message = prompt.format(message=request_to_message(request.message, request.nodes))\n", "\n", "    # Prompt formatting\n", "    prompt_input = ChatPromptInput(\n", "        path=request.path,\n", "        prefix=request.prefix,\n", "        selected_code=request.selected_code,\n", "        suffix=request.suffix,\n", "        message=message,\n", "        chat_history=chat_history,\n", "        prefix_begin=request.position.prefix_begin,\n", "        suffix_end=request.position.suffix_end,\n", "        retrieved_chunks=retrieved_chunks,\n", "        context_code_exchange_request_id=(\n", "            request.context_code_exchange_request_id\n", "            if len(request.context_code_exchange_request_id) > 0\n", "            else None\n", "        ),\n", "        diff=request.diff,\n", "        relevant_commit_messages=list(request.relevant_commit_messages),\n", "        example_commit_messages=list(request.example_commit_messages),\n", "        workspace_guidelines=request.workspace_guidelines,\n", "        user_guidelines=request.user_guidelines,\n", "    )\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    tool_definitions = [\n", "        ToolDefinition(\n", "            tool.name,\n", "            tool.description,\n", "            tool.input_schema_json,\n", "        )\n", "        for tool in request.tool_definitions\n", "    ]\n", "    tools = []\n", "\n", "    response_iterator = MODEL_CLIENT.generate_response_stream(\n", "        model_caller=\"agent-binks-claude-v2\",\n", "        cur_message=prompt_output.message,\n", "        chat_history=list(prompt_output.chat_history),\n", "        system_prompt=prompt_output.system_prompt,\n", "        tools=tools,\n", "        tool_definitions=tool_definitions,\n", "    )\n", "    full_response = \"\"\n", "    for response in response_iterator:\n", "        full_response += response.text\n", "\n", "    return full_response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_requests = filter_regular_requests(full_requests)\n", "len(filtered_requests), filtered_requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["CLASSIFY_AND_DISTILL_PROMPT = \"\"\"\n", "###\n", "# ENTER MESSAGE ANALYSIS MODE \n", "# IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING\n", "# YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF\n", "# YOU RETURN ONLY JSON \n", "# ###\n", "\n", "Here is the next message from the user:\n", "```\n", "{message}\n", "```\n", "Your task is to detect if the next message contains some information worth remembering in long-term.\n", "Information is worth remembering if in the next message user gives some new information, asks to do something differently or describes user preferences.\n", "Knowledge is worth remembering if it is relevant to the codebase, policies, preferred technologies or patterns, etc AND if it will be useful in the long-term!\n", "Also, if user hints to how/where tests should be written, it is also worth remembering.\n", "If knowledge is overly specific to the current task, then it is NOT worth remembering.\n", "If user reports some task specific bug, it is NOT worth remembering.\n", "\n", "Exceptions (do not remember such information):\n", "- If user asks not to use some existing tools\n", "\n", "Return JSON with three keys (in provided order): \"explanation\" (str), \"worthRemembering\" (bool) and \"content\" (str).\n", "\"explanation\" should be short (1 sentence) text that describes why the information is worth remembering or not.\n", "\"content\" should be short (1 sentence) text that describes the information worth remembering.\n", "If \"worthRemembering\" is false, then \"content\" should be empty.\n", "\n", "Write ONLY JSON and no other text (start response with \"{{\"). All planning/reasoning/etc should be put into \"explanation\". Don't use any tools for it.\n", "Example: {{\"explanation\": \"some explanation\", \"worthRemembering\": true or false, \"content\": \"memory content\"}}\n", "\"\"\"\n", "\n", "print(CLASSIFY_AND_DISTILL_PROMPT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cur_request = filtered_requests[0]\n", "cur_chat_request = get_chat_request(cur_request)\n", "print(f\"Request ID: {cur_request.request_id}\")\n", "\n", "\n", "response = process(cur_chat_request, CLASSIFY_AND_DISTILL_PROMPT)\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_request(args):\n", "    request, prompt = args\n", "    try:\n", "        return request, process(get_chat_request(request), prompt)\n", "    except UnavailableRpcError:\n", "        return request, \"UnavailableRpcError\"\n", "    except Exception as e:\n", "        return request, f\"EXCEPTION {e}\"\n", "\n", "\n", "# Prepare the arguments for parallel processing\n", "args = [(request, CLASSIFY_AND_DISTILL_PROMPT) for request in filtered_requests]\n", "\n", "# Process requests in parallel using context managers for both pool and progress bar\n", "results = []\n", "with (\n", "    multiprocessing.Pool(processes=16) as pool,\n", "    tqdm(total=len(args), desc=\"Processing requests\") as pbar,\n", "):\n", "    for result in pool.imap_unordered(process_request, args):\n", "        results.append(result)\n", "        pbar.update()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["remembered_results = []\n", "non_remembered = []\n", "parsing_errors = []\n", "rpc_errors = []\n", "\n", "for request, response in results:\n", "    if len(response) == 0:\n", "        print(\"Skipping empty\")\n", "        continue\n", "    if response == \"UnavailableRpcError\":\n", "        rpc_errors.append(request)\n", "        continue\n", "\n", "    start_idx = response.find(\"{\")\n", "    end_idx = response.rfind(\"}\")\n", "    if start_idx == -1 or end_idx == -1:\n", "        parsing_errors.append([request, response])\n", "        continue\n", "    response = response[start_idx : end_idx + 1]\n", "    try:\n", "        response_dict = json.loads(response)\n", "    except Exception:\n", "        parsing_errors.append([request, response])\n", "        continue\n", "    if response_dict[\"worthRemembering\"]:\n", "        remembered_results.append([request, response_dict])\n", "    else:\n", "        non_remembered.append([request, response_dict])\n", "\n", "(\n", "    len(remembered_results),\n", "    len(non_remembered),\n", "    len(parsing_errors),\n", "    len(rpc_errors),\n", "    len(results),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, (request, response) in enumerate(non_remembered):\n", "    print(f\"{i} \" + \"=\" * 30)\n", "    print(f\"Request ID: {request.request_id}\")\n", "    print(get_message_text(get_chat_request(request)))\n", "    print(\"\\n\\n\")\n", "    print(f\"--> {response}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Injection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process(request, prompt):\n", "    request_message = get_message_text(request)\n", "    pattern = r\"Here are the memories already saved:\\s*```(.*?)```\\s*Here is the new memory to remember:\\s*```(.*?)```\"\n", "    match = re.search(pattern, request_message, re.DOTALL)\n", "    if match:\n", "        current_memories = match.group(1)\n", "        new_memory = match.group(2)\n", "    else:\n", "        print(request_message)\n", "        assert False\n", "\n", "    # Prompt formatting\n", "    prompt_input = ChatPromptInput(\n", "        path=\"\",\n", "        prefix=\"\",\n", "        selected_code=\"\",\n", "        suffix=\"\",\n", "        message=prompt.format(currentMemories=current_memories, newMemory=new_memory),\n", "        chat_history=[],\n", "        prefix_begin=request.position.prefix_begin,\n", "        suffix_end=request.position.suffix_end,\n", "        retrieved_chunks=[],\n", "    )\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    tool_definitions = []\n", "    tools = []\n", "\n", "    response_iterator = MODEL_CLIENT.generate_response_stream(\n", "        model_caller=\"agent-binks-claude-v2\",\n", "        cur_message=prompt_output.message,\n", "        chat_history=list(prompt_output.chat_history),\n", "        system_prompt=prompt_output.system_prompt,\n", "        tools=tools,\n", "        tool_definitions=tool_definitions,\n", "    )\n", "    full_response = \"\"\n", "    for response in response_iterator:\n", "        full_response += response.text\n", "\n", "    return full_response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_requests = filter_injection_requests(full_requests)\n", "len(filtered_requests), filtered_requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INJECTION_PROMPT_PROD = \"\"\"\n", "Here are the memories already saved:\n", "```\n", "{currentMemories}\n", "```\n", "Here is the new memory to remember:\n", "```\n", "- {new<PERSON><PERSON>ory}\n", "```\n", "Incorporate the new memory into the current memories, by adding/removing/modifying memories as needed.\n", "If new memory is already present in current memories, just return current memories as is.\n", "\n", "Write ONLY full updated memories and NOTHING else. Start your response with \"```\" and end it with \"```\". Don't do AN<PERSON> preamble or postamble.\n", "\"\"\"\n", "\n", "INJECTION_PROMPT = \"\"\"\n", "Here are the memories already saved:\n", "```\n", "{currentMemories}\n", "```\n", "Here is the new memory to remember:\n", "```\n", "- {new<PERSON><PERSON>ory}\n", "```\n", "Incorporate the new memory into the current memories, by adding/removing/modifying memories as needed.\n", "If new memory is already present in current memories, just return current memories as is.\n", "\n", "Memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)\n", "Example format of updated memories:\n", "```\n", "# Group 1\n", "- Memory 1\n", "- Memory 2\n", "\n", "# Group 2\n", "- Memory 3\n", "- Memory 4\n", "\n", "...\n", "```\n", "\n", "Write ONLY full updated memories and NOTHING else. Start your response with \"```\" and end it with \"```\". Don't do AN<PERSON> preamble or postamble.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cur_request = filtered_requests[0]\n", "cur_chat_request = get_chat_request(cur_request)\n", "print(f\"Request ID: {cur_request.request_id}\")\n", "print(cur_chat_request.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = process(cur_chat_request, INJECTION_PROMPT)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_request(args):\n", "    request, prompt = args\n", "    try:\n", "        return request, process(get_chat_request(request), prompt)\n", "    except UnavailableRpcError:\n", "        return request, \"UnavailableRpcError\"\n", "    except Exception as e:\n", "        print(f\"EXCEPTION {e}\")\n", "        return request, f\"EXCEPTION {e}\"\n", "\n", "\n", "# Prepare the arguments for parallel processing\n", "args = [(request, INJECTION_PROMPT) for request in filtered_requests]\n", "\n", "# Process requests in parallel using context managers for both pool and progress bar\n", "results = []\n", "with (\n", "    multiprocessing.Pool(processes=1) as pool,\n", "    tqdm(total=len(args), desc=\"Processing requests\") as pbar,\n", "):\n", "    for result in pool.imap_unordered(process_request, args):\n", "        results.append(result)\n", "        pbar.update()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correct_results = []\n", "parsing_errors = []\n", "\n", "for request, response in results:\n", "    if len(response) == 0:\n", "        print(\"Skipping empty\")\n", "        continue\n", "    if not (response.strip().startswith(\"```\") and response.strip().endswith(\"```\")):\n", "        parsing_errors.append([request, response])\n", "        continue\n", "    correct_results.append([request, response])\n", "\n", "len(correct_results), len(parsing_errors), len(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, (request, response) in enumerate(correct_results):\n", "    print(f\"{i} \" + \"=\" * 30)\n", "    print(f\"Request ID: {request.request_id}\")\n", "    print(get_message_text(get_chat_request(request)))\n", "    print(\"\\n\\n\")\n", "    print(f\"--> {response}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Compression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process(request, prompt, overwrite=None):\n", "    if overwrite is None:\n", "        request_message = get_message_text(request)\n", "        pattern = r\"Here are the memories already saved:\\s*```(.*?)```\\s*Here is the new memory to remember:\\s*```(.*?)```\"\n", "        match = re.search(pattern, request_message, re.DOTALL)\n", "        if match:\n", "            current_memories = match.group(1)\n", "        else:\n", "            print(request_message)\n", "            assert False\n", "    else:\n", "        current_memories = overwrite\n", "\n", "    # Prompt formatting\n", "    prompt_input = ChatPromptInput(\n", "        path=\"\",\n", "        prefix=\"\",\n", "        selected_code=\"\",\n", "        suffix=\"\",\n", "        message=prompt.format(memories=current_memories, compressionTarget=30),\n", "        chat_history=[],\n", "        prefix_begin=request.position.prefix_begin,\n", "        suffix_end=request.position.suffix_end,\n", "        retrieved_chunks=[],\n", "    )\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    tool_definitions = []\n", "    tools = []\n", "\n", "    response_iterator = MODEL_CLIENT.generate_response_stream(\n", "        model_caller=\"agent-binks-claude-v2\",\n", "        cur_message=prompt_output.message,\n", "        chat_history=list(prompt_output.chat_history),\n", "        system_prompt=prompt_output.system_prompt,\n", "        tools=tools,\n", "        tool_definitions=tool_definitions,\n", "    )\n", "    full_response = \"\"\n", "    for response in response_iterator:\n", "        full_response += response.text\n", "\n", "    return full_response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using the same filtering function as for injection\n", "filtered_requests = filter_injection_requests(full_requests)\n", "len(filtered_requests), filtered_requests[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["COMPRESSION_PROMPT = \"\"\"\n", "Here are the full memories assembled through all interactions of user with coding agent:\n", "```\n", "{memories}\n", "```\n", "You task is to summarize and merge related or redundant memories to retain the most informative and distinct ones.\n", "Result should be ~{compressionTarget} lines long.\n", "\n", "Prioritize preserving information that is:\n", "- Relevant to codebase, policies, preferred technologies or patterns\n", "- Will be useful in long-term\n", "- Describes user, user knowledge, user preferences or long-term information about user (like name/email)\n", "\n", "Updated memories should be grouped to improve readability. (Use only one level, i.e. no ##Subcategory / -- / etc)\n", "Example format of updated memories:\n", "```\n", "# Group 1\n", "- Memory 1\n", "- Memory 2\n", "\n", "# Group 2\n", "- Memory 3\n", "- Memory 4\n", "\n", "...\n", "```\n", "\n", "Write ONLY full updated memories and NOTHING else. Start your response with \"```\" and end it with \"```\". Don't do AN<PERSON> preamble or postamble.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_t = \"\"\"\n", "???\n", "\"\"\"\n", "\n", "print(process(get_chat_request(filtered_requests[0]), COMPRESSION_PROMPT, overwrite=_t))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Request ID: {cur_request.request_id}\")\n", "cur_request = filtered_requests[1]\n", "cur_chat_request = get_chat_request(cur_request)\n", "print(cur_chat_request.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = process(cur_chat_request, COMPRESSION_PROMPT)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_request(args):\n", "    request, prompt = args\n", "    try:\n", "        return request, process(get_chat_request(request), prompt)\n", "    except UnavailableRpcError:\n", "        return request, \"UnavailableRpcError\"\n", "    except Exception as e:\n", "        print(f\"EXCEPTION {e}\")\n", "        return request, f\"EXCEPTION {e}\"\n", "\n", "\n", "# Prepare the arguments for parallel processing\n", "args = [(request, COMPRESSION_PROMPT) for request in filtered_requests]\n", "\n", "# Process requests in parallel using context managers for both pool and progress bar\n", "results = []\n", "with (\n", "    multiprocessing.Pool(processes=1) as pool,\n", "    tqdm(total=len(args), desc=\"Processing requests\") as pbar,\n", "):\n", "    for result in pool.imap_unordered(process_request, args):\n", "        results.append(result)\n", "        pbar.update()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correct_results = []\n", "parsing_errors = []\n", "\n", "for request, response in results:\n", "    if len(response) == 0:\n", "        print(\"Skipping empty\")\n", "        continue\n", "    if not (response.strip().startswith(\"```\") and response.strip().endswith(\"```\")):\n", "        parsing_errors.append([request, response])\n", "        continue\n", "    correct_results.append([request, response])\n", "\n", "len(correct_results), len(parsing_errors), len(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(results[4][1])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
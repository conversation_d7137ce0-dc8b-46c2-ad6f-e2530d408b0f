{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "from pathlib import Path\n", "from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# NOTE: after you get request id, go to `https://support-v2.augmentcode.com/cross-tenant`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROJECT_ID = \"system-services-prod\"\n", "gcp_creds, _ = get_gcp_creds(None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get request IDs by opaque user id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_agentic_requests_by_opaque_user_id(\n", "    opaque_user_id: str,\n", "    limit: int = 100,\n", "):\n", "    client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "\n", "    query = f\"\"\"\n", "    SELECT request_id\n", "    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`  \n", "    WHERE request_type = \"AGENT_CHAT\"\n", "    AND opaque_user_id = \"{opaque_user_id}\"\n", "    LIMIT {limit}\n", "    \"\"\"\n", "    print(f\"Using query: {query}\")\n", "\n", "    query_job = client.query(query)\n", "    results = query_job.result()\n", "\n", "    return list(\n", "        map(\n", "            lambda x: x.values()[0],\n", "            results,\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_ids = get_agentic_requests_by_opaque_user_id(\n", "    \"d3f036c1-73d2-4bef-b010-f20df2614ca6\"\n", ")\n", "request_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_ids = get_agentic_requests_by_opaque_user_id(\n", "    \"d3f036c1-73d2-4bef-b010-f20df2614ca6\"\n", ")\n", "request_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_ids[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get all request ids from community"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_agentic_requests_from_community(\n", "    limit: int = 100,\n", "):\n", "    client = bigquery.Client(project=PROJECT_ID, credentials=gcp_creds)\n", "\n", "    query = f\"\"\"\n", "    SELECT request_id\n", "    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`  \n", "    WHERE request_type = \"AGENT_CHAT\" AND\n", "    tenant_id IN (  \n", "        '51b96ec9cd1b02a7c48e30f8503092be',  \n", "        '789b1a18a6970fc4de4cf3aa89a35827',  \n", "        'eac8863de00e749b73eae23300f69e92'  \n", "    ) \n", "    LIMIT {limit}\n", "    \"\"\"\n", "    print(f\"Using query: {query}\")\n", "\n", "    query_job = client.query(query)\n", "    results = query_job.result()\n", "\n", "    return list(\n", "        map(\n", "            lambda x: x.values()[0],\n", "            results,\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_ids = get_agentic_requests_from_community()\n", "request_ids"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
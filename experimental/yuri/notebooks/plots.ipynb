{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import json\n", "\n", "from os.path import join\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["RESULT_DIR = '/home/<USER>/repos/augment/research/eval/edit/ev27'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["['DeepSeek1BCodeEditSystemHF.json',\n", " 'DeepSeek6BCodeEditSystemHF.json',\n", " 'DeepSeek33BCodeEditSystemHF.json',\n", " 'CodeLLamaInstruct7BCodeEditSystemHF.json',\n", " 'CodeLLamaInstruct13BCodeEditSystemHF.json',\n", " 'CodeLLamaInstruct34BCodeEditSystemHF.json',\n", " 'WizardCoder7BCodeEditSystemHF.json',\n", " 'WizardCoder13BCodeEditSystemHF.json',\n", " 'WizardCoder34BCodeEditSystemHF.json']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["json_paths = os.listdir(RESULT_DIR)\n", "json_paths"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["all_results = {}\n", "\n", "for j in json_paths:\n", "    with open(join(RESULT_DIR, j)) as f:\n", "        cur_result = json.load(f)\n", "    j = j.split('.')[0]\n", "\n", "    all_results[j] = cur_result"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'exact_match': [0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1],\n", " 'll': [-0.4012620449066162,\n", "  -0.10258156806230545,\n", "  -0.4373888671398163,\n", "  -0.09265628457069397,\n", "  -0.0953269898891449,\n", "  -0.09156820178031921,\n", "  -0.11518154293298721,\n", "  -0.09728819876909256,\n", "  -0.21079878509044647,\n", "  -0.1974497139453888,\n", "  -0.225174218416214,\n", "  -0.43078091740608215,\n", "  -0.058021463453769684],\n", " 'agg_metrics': {'exact_match': 3, 'll': -0.1965752920279136}}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["all_results['DeepSeek1BCodeEditSystemHF']"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["model_ids = [*all_results.keys()]\n", "x_values = [*map(lambda n: all_results[n]['agg_metrics']['exact_match'], model_ids)]\n", "y_values = [*map(lambda n: all_results[n]['agg_metrics']['ll'], model_ids)]\n", "\n", "model_names = [*map(lambda id_: id_.split('CodeEditSystemHF')[0], model_ids)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.figure(figsize=(8, 6))\n", "\n", "for i, model in enumerate(model_names):\n", "    if model == \"DeepSeek6B\":\n", "        offset = -0.015 * 0\n", "    else:\n", "        offset = 0.005 * 0\n", "\n", "    if model.startswith(\"CodeLLama\"):\n", "        color = 'b'\n", "    elif model.startswith(\"Wizard\"):\n", "        color = 'r'\n", "    elif model.startswith(\"DeepSeek\"):\n", "        color = 'g'\n", "    else:\n", "        raise ValueError(f\"Unknown model: {model}\")\n", "    \n", "    plt.scatter(x_values[i], y_values[i], color=color, s=100)\n", "\n", "    plt.annotate(model, (x_values[i], y_values[i] + offset), fontsize=10, ha='center', va='bottom', color=color)\n", "\n", "plt.xlabel(\"Exact matches\")\n", "plt.ylabel(\"Log likelihood\")\n", "plt.title(\"Edit models evaluation (13 curated data samples, fp16)\")\n", "\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
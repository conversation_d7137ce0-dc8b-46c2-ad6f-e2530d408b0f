{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Sample 100 random samples with 2+ lines in the updated code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits.raw.v1.json\") as f:\n", "    full_data = json.load(f)\n", "\n", "len(full_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_2plus = [*filter(lambda s: len(s[\"new_code\"].splitlines()) >= 2, tqdm(full_data))]\n", "\n", "data_subset = random.sample(data_2plus, 100)\n", "\n", "len(data_2plus), len(data_subset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(Path(\"/mnt/efs/augment/user/yuri/data/test\") / \"30k_filtered_pr_data.json\", \"w\") as f:\n", "    json.dump(data_2plus, f, indent=2)\n", "\n", "with open(Path(\"/mnt/efs/augment/user/yuri/data/test\") / \"100_eval_pr_data.json\", \"w\") as f:\n", "    json.dump(data_subset, f, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Convert to git conflict format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/test\") / \"100_eval_pr_data.json\"\n", "RESULT_PATH = Path(\"/mnt/efs/augment/user/yuri/data/test\") / \"git_conflict_data\"\n", "\n", "RESULT_PATH.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def combine(prefix, code, gt, suffix):\n", "    # We need it so that separators (>>>>>>>, <<<<<< and =======) are written on a separate line\n", "    if not prefix.endswith(\"\\n\"):\n", "        prefix += \"\\n\"\n", "    if not code.endswith(\"\\n\"):\n", "        code += \"\\n\"\n", "    if not gt.endswith(\"\\n\"):\n", "        gt += \"\\n\"\n", "\n", "    p_lines = prefix.splitlines(True)\n", "    c_lines = code.splitlines(True)\n", "    g_lines = gt.splitlines(True)\n", "    s_lines = suffix.splitlines(True)\n", "\n", "    lines = p_lines + [\"<<<<<<<\\n\"] + c_lines + [\"=======\\n\"] + g_lines + [\">>>>>>>\\n\"] + s_lines\n", "\n", "    return \"\".join(lines)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(DATA_PATH) as f:\n", "    data = json.load(f)\n", "\n", "print(type(data), len(data))\n", "\n", "data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prev_ids = set()\n", "\n", "for sample in data:\n", "    code = combine(sample[\"prefix\"], sample[\"old_code\"], sample[\"new_code\"], sample[\"suffix\"])\n", "    meta = {\n", "        \"repo_url\": sample[\"html_url\"],\n", "        \"file_name\": sample[\"path\"],\n", "        \"instructions\": [\n", "            sample[\"instruction\"]\n", "        ],\n", "        \"category\": \"pr-coments\",\n", "        \"language\": \"\",\n", "        \"inverse_instructions\": []\n", "    }\n", "\n", "    cur_id = sample[\"id\"]\n", "    if cur_id in prev_ids:\n", "        raise RuntimeError(f\"Duplicate id {cur_id}\")\n", "    prev_ids.add(cur_id)\n", "    \n", "    txt_path = RESULT_PATH / f\"{cur_id}.txt\"\n", "    json_path = RESULT_PATH / f\"{cur_id}.json\"\n", "    with txt_path.open(\"w\") as f:\n", "        f.write(code)\n", "    with json_path.open(\"w\") as f:\n", "        json.dump(meta, f, indent=2)\n", "    prev_ids.add(cur_id)\n", "\n", "    "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
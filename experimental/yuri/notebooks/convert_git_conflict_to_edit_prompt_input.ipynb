{"cells": [{"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from research.eval.edit.data_tools.collected import load_data_git_conflict_format\n", "from pathlib import Path\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from collections import defaultdict\n", "import json\n", "\n", "from dataclasses import asdict"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["git_conflict_data = load_data_git_conflict_format(\n", "    Path(\"/home/<USER>/repos/augment/research/eval/edit/data\"),\n", "    lines_in_prefix_suffix=int(1e12),\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'code': '                    body += \\'if \"{result_key}\" not in res:\\'.format(key=result_key)\\n                    with body.indent():\\n                        body += \\'raise ValueError()\\'\\n                if field_obj.allow_none is not True:\\n                    body += \\'if __res_get(\"{result_key}\", res) is None:\\'.format(\\n                        key=result_key)\\n',\n", " 'prefix': 'import base64\\nimport keyword\\nimport re\\nfrom abc import ABCMeta, abstractmethod\\nfrom collections import Mapping\\n\\nimport attr\\nfrom six import exec_, iteritems, add_metaclass, text_type, string_types\\nfrom marshmallow import missing, Schema, fields\\nfrom marshmallow.base import SchemaABC\\n\\nfrom .compat import is_overridden\\nfrom .utils import IndentedString\\n\\n\\n# Regular Expression for identifying a valid Python identifier name.\\n_VALID_IDENTIFIER = re.compile(r\\'[a-zA-Z_][a-zA-Z0-9_]*\\')\\n\\nif False:  # pylint: disable=using-constant-test\\n    # pylint: disable=unused-import\\n    from typing import Any, Callable, Dict, Optional, Tuple, Union, Set\\n\\n\\ndef field_symbol_name(field_name):\\n    # type: (str) -> str\\n    \"\"\"Generates the symbol name to be used when accessing a field in generated\\n    code.\\n\\n    If the field name isn\\'t a valid identifier name, synthesizes a name by\\n    base64 encoding the fieldname.\\n    \"\"\"\\n    if not _VALID_IDENTIFIER.match(field_name):\\n        field_name = str(base64.b64encode(\\n            field_name.encode(\\'utf-8\\')).decode(\\'utf-8\\').strip(\\'=\\'))\\n    return \\'_field_{field_name}\\'.format(field_name=field_name)\\n\\n\\ndef attr_str(attr_name):\\n    # type: (str) -> str\\n    \"\"\"Gets the string to use when accessing an attribute on an object.\\n\\n    Handles case where the attribute name collides with a keyword and would\\n    therefore be illegal to access with dot notation.\\n    \"\"\"\\n    if keyword.iskeyword(attr_name):\\n        return \\'getattr(obj, \"{0}\")\\'.format(attr_name)\\n    return \\'obj.{0}\\'.format(attr_name)\\n\\n\\n@add_metaclass(ABCMeta)\\nclass FieldSerializer(object):\\n    \"\"\"Base class for generating code to serialize a field.\\n    \"\"\"\\n    def __init__(self, context=None):\\n        # type: (JitContext) -> None\\n        \"\"\"\\n        :param context: The context for the current Jit\\n        \"\"\"\\n        self.context = context or JitContext()\\n\\n    @abstractmethod\\n    def serialize(self, attr_name, field_symbol,\\n                  assignment_template, field_obj):\\n        # type: (str, str, str, fields.Field) -> IndentedString\\n        \"\"\"Generates the code to pull a field off of an object into the result.\\n\\n        :param attr_name: The name of the attribute being accessed/\\n        :param field_symbol: The symbol to use when accessing the field.  Should\\n            be generated via field_symbol_name.\\n        :param assignment_template: A string template to use when generating\\n            code.  The assignment template is passed into the serializer and\\n            has a single possitional placeholder for string formatting.  An\\n            example of a value that may be passed into assignment_template is:\\n            `res[\\'some_field\\'] = {0}`\\n        :param field_obj: The instance of the Marshmallow field being\\n            serialized.\\n        :return: The code to pull a field off of the object passed in.\\n        \"\"\"\\n        pass  # pragma: no cover\\n\\n\\nclass InstanceSerializer(FieldSerializer):\\n    \"\"\"Generates code for accessing fields as if they were instance variables.\\n\\n    For example, generates:\\n\\n    res[\\'some_value\\'] = obj.some_value\\n    \"\"\"\\n    def serialize(self, attr_name, field_symbol,\\n                  assignment_template, field_obj):\\n        # type: (str, str, str, fields.Field) -> IndentedString\\n        return IndentedString(assignment_template.format(attr_str(attr_name)))\\n\\n\\nclass DictSerializer(FieldSerializer):\\n    \"\"\"Generates code for accessing fields as if they were a dict, generating\\n    the proper code for handing missing fields as well.  For example, generates:\\n\\n    # Required field with no default\\n    res[\\'some_value\\'] = obj[\\'some_value\\']\\n\\n    # Field with a default.  some_value__default will be injected at exec time.\\n    res[\\'some_value\\'] = obj.get(\\'some_value\\', some_value__default)\\n\\n    # Non required field:\\n    if \\'some_value\\' in obj:\\n        res[\\'some_value\\'] = obj[\\'some_value\\']\\n    \"\"\"\\n    def serialize(self, attr_name, field_symbol,\\n                  assignment_template, field_obj):\\n        # type: (str, str, str, fields.Field) -> IndentedString\\n        body = IndentedString()\\n        if self.context.is_serializing:\\n            default_str = \\'default\\'\\n            default_value = field_obj.default\\n        else:\\n            default_str = \\'missing\\'\\n            default_value = field_obj.missing\\n            if field_obj.required:\\n                body += assignment_template.format(\\'obj[\"{attr_name}\"]\\'.format(\\n                    attr_name=attr_name))\\n                return body\\n        if default_value == missing:\\n            body += \\'if \"{attr_name}\" in obj:\\'.format(attr_name=attr_name)\\n            with body.indent():\\n                body += assignment_template.format(\\'obj[\"{attr_name}\"]\\'.format(\\n                    attr_name=attr_name))\\n        else:\\n            if callable(default_value):\\n                default_str += \\'()\\'\\n\\n            body += assignment_template.format(\\n                \\'obj.get(\"{attr_name}\", {field_symbol}__{default_str})\\'.format(\\n                    attr_name=attr_name, field_symbol=field_symbol,\\n                    default_str=default_str))\\n        return body\\n\\n\\nclass HybridSerializer(FieldSerializer):\\n    \"\"\"Generates code for accessing fields as if they were a hybrid object.\\n\\n    Hybrid objects are objects that don\\'t inherit from `Mapping`, but do\\n    implement `__getitem__`.  This means we first have to attempt a lookup by\\n    key, then fall back to looking up by instance variable.\\n\\n    For example, generates:\\n\\n    try:\\n        value = obj[\\'some_value\\']\\n    except (KeyError, AttributeError, IndexError, TypeError):\\n        value = obj.some_value\\n    res[\\'some_value\\'] = value\\n    \"\"\"\\n    def serialize(self, attr_name, field_symbol,\\n                  assignment_template, field_obj):\\n        # type: (str, str, str, fields.Field) -> IndentedString\\n        body = IndentedString()\\n        body += \\'try:\\'\\n        with body.indent():\\n            body += \\'value = obj[\"{attr_name}\"]\\'.format(attr_name=attr_name)\\n        body += \\'except (KeyError, AttributeError, IndexError, TypeError):\\'\\n        with body.indent():\\n            body += \\'value = {attr_str}\\'.format(attr_str=attr_str(attr_name))\\n        body += assignment_template.format(\\'value\\')\\n        return body\\n\\n\\n@attr.s\\nclass JitContext(object):\\n    \"\"\" Bag of properties to keep track of the context of what\\'s being jitted.\\n\\n    \"\"\"\\n    namespace = attr.ib(default={})  # type: Dict[str, Any]\\n    use_inliners = attr.ib(default=True)  # type: bool\\n    schema_stack = attr.ib(default=attr.Factory(set))  # type: Set[str]\\n    only = attr.ib(default=None)  # type: Optional[Set[str]]\\n    exclude = attr.ib(default=set())  # type: Set[str]\\n    is_serializing = attr.ib(default=True)  # type: bool\\n\\n\\n@add_metaclass(ABCMeta)\\nclass FieldInliner(object):\\n    \"\"\"Base class for generating code to serialize a field.\\n\\n    Inliners are used to generate the code to validate/parse fields without\\n    having to bounce back into the underlying marshmallow code.  While this is\\n    somewhat fragile as it requires the inliners to be kept in sync with the\\n    underlying implementation, it\\'s good for a >2X speedup on benchmarks.\\n    \"\"\"\\n    @abstractmethod\\n    def inline(self, field, context):\\n        # type: (fields.Field, JitContext) -> Optional[str]\\n        pass  # pragma: no cover\\n\\n\\nclass StringInliner(FieldInliner):\\n    def inline(self, field, context):\\n        # type: (fields.Field, JitContext) -> Optional[str]\\n        \"\"\"Generates a template for inlining string serialization.\\n\\n        For example, generates \"unicode(value) if value is not None else None\"\\n        to serialize a string in Python 2.7\\n        \"\"\"\\n        if is_overridden(field._serialize, fields.String._serialize):\\n            return None\\n        result = text_type.__name__ + \\'({0})\\'\\n        result += \\' if {0} is not None else None\\'\\n        if not context.is_serializing:\\n            string_type_strings = \\',\\'.join([x.__name__ for x in string_types])\\n            result = (\\'(\\' + result + \\') if \\'\\n                      \\'(isinstance({0}, (\\' + string_type_strings +\\n                      \\')) or {0} is None) else dict()[\"error\"]\\')\\n        return result\\n\\n\\nclass BooleanInliner(FieldInliner):\\n    def inline(self, field, context):\\n        # type: (fields.Field, JitContext) -> Optional[str]\\n        \"\"\"Generates a template for inlining boolean serialization.\\n\\n        For example, generates:\\n\\n        (\\n            (value in __some_field_truthy) or\\n            (False if value in __some_field_falsy else bool(value))\\n        )\\n\\n        This is somewhat fragile but it tracks what Marshmallow does.\\n        \"\"\"\\n        if is_overridden(field._serialize, fields.Boolean._serialize):\\n            return None\\n        truthy_symbol = \\'__{0}_truthy\\'.format(field.name)\\n        falsy_symbol = \\'__{0}_falsy\\'.format(field.name)\\n        context.namespace[truthy_symbol] = field.truthy\\n        context.namespace[falsy_symbol] = field.falsy\\n        result = (\\'(({0} in \\' + truthy_symbol +\\n                  \\') or (False if {0} in \\' + falsy_symbol +\\n                  \\' else dict()[\"error\"]))\\')\\n        return result + \\' if {0} is not None else None\\'\\n\\n\\nclass NumberInliner(FieldInliner):\\n    def inline(self, field, context):\\n        # type: (fields.Field, JitContext) -> Optional[str]\\n        \"\"\"Generates a template for inlining string serialization.\\n\\n        For example, generates \"float(value) if value is not None else None\"\\n        to serialize a float.  If `field.as_string` is `True` the result will\\n        be coerced to a string if not None.\\n        \"\"\"\\n        if (is_overridden(field._validated, fields.Number._validated) or\\n                is_overridden(field._serialize, fields.Number._serialize)):\\n            return None\\n        result = field.num_type.__name__ + \\'({0})\\'\\n        if field.as_string and context.is_serializing:\\n            result = \\'str({0})\\'.format(result)\\n        if field.allow_none is True:\\n            # Only emit the Null checking code if nulls are allowed.  If they\\n            # aren\\'t allowed casting `None` to an integer will throw and the\\n            # slow path will take over.\\n            result += \\' if {0} is not None else None\\'\\n        return result\\n\\n\\nclass NestedInliner(FieldInliner):  # pragma: no cover\\n    def inline(self, field, context):\\n        \"\"\"Generates a template for inlining nested field.\\n\\n        This doesn\\'t pass tests yet in Marshmallow, namely due to issues around\\n        code expecting the context of nested schema to be populated on first\\n        access, so disabling for now.\\n        \"\"\"\\n        if is_overridden(field._serialize, fields.Nested._serialize):\\n            return None\\n\\n        if not (isinstance(field.nested, type) and\\n                issubclass(field.nested, SchemaABC)):\\n            return None\\n\\n        if field.nested.__class__ in context.schema_stack:\\n            return None\\n\\n        method_name = \\'__nested_{}_serialize\\'.format(\\n            field_symbol_name(field.name))\\n\\n        old_only = context.only\\n        old_exclude = context.exclude\\n        old_namespace = context.namespace\\n\\n        context.only = set(field.only) if field.only else None\\n        context.exclude = set(field.exclude)\\n        context.namespace = {}\\n\\n        for only_field in old_only or []:\\n            if only_field.startswith(field.name + \\'.\\'):\\n                if not context.only:\\n                    context.only = set()\\n                context.only.add(only_field[len(field.name + \\'.\\'):])\\n        for only_field in list((context.only or [])):\\n            if \\'.\\' in only_field:\\n                if not context.only:\\n                    context.only = set()\\n                context.only.add(only_field.split(\\'.\\')[0])\\n\\n        for exclude_field in old_exclude:\\n            if exclude_field.startswith(field.name + \\'.\\'):\\n                context.exclude.add(exclude_field[len(field.name + \\'.\\'):])\\n\\n        serialize_method = generate_marshall_method(field.schema, context)\\n        if serialize_method is None:\\n            return None\\n\\n        context.namespace = old_namespace\\n        context.only = old_only\\n        context.exclude = old_exclude\\n\\n        context.namespace[method_name] = serialize_method\\n\\n        if field.many:\\n            return (\\'[\\' + method_name +\\n                    \\'(_x) for _x in {0}] if {0} is not None else None\\')\\n        return method_name + \\'({0}) if {0} is not None else None\\'\\n\\n\\nINLINERS = {\\n    fields.String: StringInliner(),\\n    fields.Number: NumberInliner(),\\n    fields.Boolean: BooleanInliner(),\\n}\\n\\nEXPECTED_TYPE_TO_CLASS = {\\n    \\'object\\': InstanceSerializer,\\n    \\'dict\\': DictSerializer,\\n    \\'hybrid\\': HybridSerializer\\n}\\n\\n\\ndef _should_skip_field(field_name, field_obj, context):\\n    # type: (str, fields.Field, JitContext) -> bool\\n    if (getattr(field_obj, \\'load_only\\', False) and\\n            context.is_serializing):\\n        return True\\n    if (getattr(field_obj, \\'dump_only\\', False) and\\n            not context.is_serializing):\\n        return True\\n    if context.only and field_name not in context.only:\\n        return True\\n    if context.exclude and field_name in context.exclude:\\n        return True\\n    return False\\n\\n\\ndef generate_transform_method_body(schema, on_field, context):\\n    # type: (Schema, FieldSerializer, JitContext) -> IndentedString\\n    \"\"\"Generates the method body for a schema and a given field serialization\\n    strategy.\\n    \"\"\"\\n    body = IndentedString()\\n    body += \\'def {method_name}(obj):\\'.format(\\n        method_name=on_field.__class__.__name__)\\n    with body.indent():\\n        if schema.dict_class is dict:\\n            # Declaring dictionaries via `{}` is faster than `dict()` since it\\n            # avoids the global lookup.\\n            body += \\'res = {}\\'\\n        else:\\n            # dict_class will be injected before `exec` is called.\\n            body += \\'res = dict_class()\\'\\n        if not context.is_serializing:\\n            body += \\'__res_get = res.get\\'\\n        for field_name, field_obj in iteritems(schema.fields):\\n            if _should_skip_field(field_name, field_obj, context):\\n                continue\\n\\n            attr_name, destination = _get_attr_and_destination(context,\\n                                                               field_name,\\n                                                               field_obj)\\n\\n            result_key = \\'\\'.join(\\n                [schema.prefix or \\'\\', destination])\\n\\n            field_symbol = field_symbol_name(field_name)\\n            assignment_template = \\'\\'\\n            value_key = \\'{0}\\'\\n\\n            # If we have to assume any field can be callable we always have to\\n            # check to see if we need to invoke the method first.\\n            # We can investigate tracing this as well.\\n            jit_options = getattr(schema.opts, \\'jit_options\\', {})\\n            no_callable_fields = (jit_options.get(\\'no_callable_fields\\') or\\n                                  not context.is_serializing)\\n            if not no_callable_fields:\\n                assignment_template = (\\n                    \\'value = {0}; \\'\\n                    \\'value = value() if callable(value) else value; \\')\\n                value_key = \\'value\\'\\n\\n            # Attempt to see if this field type can be inlined.\\n            inliner = inliner_for_field(context, field_obj)\\n\\n            if inliner:\\n                assignment_template += _generate_inlined_access_template(\\n                    inliner, result_key, no_callable_fields)\\n\\n            else:\\n                assignment_template += _generate_fallback_access_template(\\n                    context, field_name, field_obj, result_key, value_key)\\n            if not field_obj._CHECK_ATTRIBUTE:\\n                # fields like \\'Method\\' expect to have `None` passed in when\\n                # invoking their _serialize method.\\n                body += assignment_template.format(\\'None\\')\\n                context.namespace[\\'__marshmallow_missing\\'] = missing\\n                body += \\'if res[\"{key}\"] is __marshmallow_missing:\\'.format(\\n                    key=result_key)\\n                with body.indent():\\n                    body += \\'del res[\"{key}\"]\\'.format(key=result_key)\\n\\n            else:\\n                serializer = on_field\\n                if not _VALID_IDENTIFIER.match(attr_name):\\n                    # If attr_name is not a valid python identifier, it can only\\n                    # be accessed via key lookups.\\n                    serializer = DictSerializer(context)\\n\\n                body += serializer.serialize(\\n                    attr_name, field_symbol, assignment_template, field_obj)\\n\\n                if not context.is_serializing and field_obj.load_from:\\n                    # Marshmallow has a somewhat counter intuitive behavior.\\n                    # It will first load from the name of the field, then,\\n                    # should that fail, will load from the field specified in\\n                    # \\'load_from\\'.\\n                    #\\n                    # For example:\\n                    #\\n                    # class TestSchema(Schema):\\n                    #     foo = StringField(load_from=\\'bar\\')\\n                    # TestSchema().load({\\'foo\\': \\'haha\\'}).result\\n                    #\\n                    # Works just fine with no errors.\\n                    #\\n                    # class TestSchema(Schema):\\n                    #     foo = StringField(load_from=\\'bar\\')\\n                    # TestSchema().load({\\'foo\\': \\'haha\\', \\'bar\\': \\'value\\'}).result\\n                    #\\n                    # Results in {\\'foo\\': \\'haha\\'}\\n                    #\\n                    # Therefore, we generate code to mimic this behavior in\\n                    # cases where `load_from` is specified.\\n                    body += \\'if \"{key}\" not in res:\\'.format(key=result_key)\\n                    with body.indent():\\n                        body += serializer.serialize(\\n                            field_obj.load_from, field_symbol,\\n                            assignment_template, field_obj)\\n            if not context.is_serializing:\\n                if field_obj.required:\\n',\n", " 'suffix': '\\n                    with body.indent():\\n                        body += \\'raise ValueError()\\'\\n                if (field_obj.validators or\\n                        is_overridden(field_obj._validate,\\n                                      fields.Field._validate)):\\n                    body += \\'if \"{key}\" in res:\\'.format(key=result_key)\\n                    with body.indent():\\n                        body += \\'{field_symbol}__validate(res[\"{result_key}\"])\\'.format(\\n                            field_symbol=field_symbol, result_key=result_key\\n                        )\\n\\n        body += \\'return res\\'\\n    return body\\n\\n\\ndef _generate_fallback_access_template(context, field_name, field_obj,\\n                                       result_key, value_key):\\n    field_symbol = field_symbol_name(field_name)\\n    transform_method_name = \\'serialize\\'\\n    if not context.is_serializing:\\n        transform_method_name = \\'deserialize\\'\\n    key_name = field_name\\n    if not context.is_serializing:\\n        key_name = field_obj.load_from or field_name\\n    return (\\n        \\'res[\"{key}\"] = {field_symbol}__{transform}(\\'\\n        \\'{value_key}, \"{key_name}\", obj)\\'.format(\\n            key=result_key, field_symbol=field_symbol,\\n            transform=transform_method_name,\\n            key_name=key_name, value_key=value_key))\\n\\n\\ndef _get_attr_and_destination(context, field_name, field_obj):\\n    # type: (JitContext, str, fields.Field) -> Tuple[str, str]\\n    # The name of the attribute to pull off the incoming object\\n    attr_name = field_name\\n    # The destination of the field in the result dictionary.\\n    destination = field_name\\n    if context.is_serializing:\\n        destination = field_obj.dump_to or field_name\\n    if field_obj.attribute:\\n        if context.is_serializing:\\n            attr_name = field_obj.attribute\\n        else:\\n            destination = field_obj.attribute\\n    return attr_name, destination\\n\\n\\ndef _generate_inlined_access_template(inliner, key, no_callable_fields):\\n    # type: (str, str, bool) -> str\\n    \"\"\"Generates the code to access a field with an inliner.\"\"\"\\n    value_key = \\'value\\'\\n    assignment_template = \\'\\'\\n    if not no_callable_fields:\\n        assignment_template += \\'value = {0}; \\'.format(\\n            inliner.format(value_key))\\n    else:\\n        assignment_template += \\'value = {0}; \\'\\n        value_key = inliner.format(\\'value\\')\\n    assignment_template += \\'res[\"{key}\"] = {value_key}\\'.format(\\n        key=key, value_key=value_key)\\n    return assignment_template\\n\\n\\ndef inliner_for_field(context, field_obj):\\n    # type: (JitContext, fields.Field) -> Optional[str]\\n    if context.use_inliners:\\n        inliner = None\\n        for field_type, inliner_class in iteritems(INLINERS):\\n            if isinstance(field_obj, field_type):\\n                inliner = inliner_class.inline(field_obj, context)\\n                if inliner:\\n                    break\\n        return inliner\\n    return None\\n\\n\\ndef generate_method_bodies(schema, context):\\n    # type: (Schema, JitContext) -> str\\n    \"\"\"Generate 3 method bodies for marshalling objects, dictionaries, or hybrid\\n    objects.\\n    \"\"\"\\n    result = IndentedString()\\n\\n    result += generate_transform_method_body(schema,\\n                                             InstanceSerializer(context),\\n                                             context)\\n    result += generate_transform_method_body(schema,\\n                                             DictSerializer(context),\\n                                             context)\\n    result += generate_transform_method_body(schema,\\n                                             HybridSerializer(context),\\n                                             context)\\n    return str(result)\\n\\n\\nclass SerializeProxy(object):\\n    \"\"\"Proxy object for calling serializer methods.\\n\\n    Initially trace calls to serialize and if the number of calls\\n    of a specific type crosses `threshold` swaps out the implementation being\\n    used for the most specialized one available.\\n    \"\"\"\\n    def __init__(self, dict_serializer, hybrid_serializer,\\n                 instance_serializer,\\n                 threshold=100):\\n        # type: (Callable, Callable, Callable, int) -> None\\n        self.dict_serializer = dict_serializer\\n        self.hybrid_serializer = hybrid_serializer\\n        self.instance_serializer = instance_serializer\\n        self.threshold = threshold\\n        self.dict_count = 0\\n        self.hybrid_count = 0\\n        self.instance_count = 0\\n        self._call = self.tracing_call\\n\\n        if not threshold:\\n            self._call = self.no_tracing_call\\n\\n    def __call__(self, obj):\\n        return self._call(obj)\\n\\n    def tracing_call(self, obj):\\n        # type: (Any) -> Any\\n        \"\"\"Dispatcher which traces calls and specializes if possible.\\n        \"\"\"\\n        try:\\n            if isinstance(obj, Mapping):\\n                self.dict_count += 1\\n                return self.dict_serializer(obj)\\n            elif hasattr(obj, \\'__getitem__\\'):\\n                self.hybrid_count += 1\\n                return self.hybrid_serializer(obj)\\n            self.instance_count += 1\\n            return self.instance_serializer(obj)\\n        finally:\\n            non_zeros = [x for x in\\n                         [self.dict_count,\\n                          self.hybrid_count,\\n                          self.instance_count] if x > 0]\\n            if len(non_zeros) > 1:\\n                self._call = self.no_tracing_call\\n            elif self.dict_count >= self.threshold:\\n                self._call = self.dict_serializer\\n            elif self.hybrid_count >= self.threshold:\\n                self._call = self.hybrid_serializer\\n            elif self.instance_count >= self.threshold:\\n                self._call = self.instance_serializer\\n\\n    def no_tracing_call(self, obj):\\n        # type: (Any) -> Any\\n        \"\"\"Dispatcher with no tracing.\\n        \"\"\"\\n        if isinstance(obj, Mapping):\\n            return self.dict_serializer(obj)\\n        elif hasattr(obj, \\'__getitem__\\'):\\n            return self.hybrid_serializer(obj)\\n        return self.instance_serializer(obj)\\n\\n\\ndef generate_marshall_method(schema, context=missing, threshold=100):\\n    # type: (Schema, JitContext, int) -> Union[SerializeProxy, Callable, None]\\n    \"\"\"Generates a function to marshall objects for a given schema.\\n\\n    :param schema: The Schema to generate a marshall method for.\\n    :param threshold: The number of calls of the same type to observe before\\n        specializing the marshal method for that type.\\n    :return: A Callable that can be used to marshall objects for the schema\\n    \"\"\"\\n    if is_overridden(schema.get_attribute, Schema.get_attribute):\\n        # Bail if get_attribute is overridden.  This provides the schema author\\n        # too much control to reasonably JIT.\\n        return None\\n\\n    if context is missing:\\n        context = JitContext()\\n\\n    context.namespace = {}\\n    context.namespace[\\'dict_class\\'] = lambda: schema.dict_class()  # pylint: disable=unnecessary-lambda\\n\\n    jit_options = getattr(schema.opts, \\'jit_options\\', {})\\n\\n    context.schema_stack.add(schema.__class__)\\n\\n    result = generate_method_bodies(schema, context)\\n\\n    context.schema_stack.remove(schema.__class__)\\n\\n    namespace = context.namespace\\n\\n    for key, value in iteritems(schema.fields):\\n        if value.attribute and \\'.\\' in value.attribute:\\n            # We\\'re currently unable to handle dotted attributes.  These don\\'t\\n            # seem to be widely used so punting for now.  For more information\\n            # see\\n            # https://github.com/marshmallow-code/marshmallow/issues/450\\n            return None\\n        namespace[field_symbol_name(key) + \\'__serialize\\'] = value._serialize\\n        namespace[field_symbol_name(key) + \\'__deserialize\\'] = value._deserialize\\n        namespace[field_symbol_name(key) + \\'__validate_missing\\'] = value._validate_missing\\n        namespace[field_symbol_name(key) + \\'__validate\\'] = value._validate\\n\\n        if value.default is not missing:\\n            namespace[field_symbol_name(key) + \\'__default\\'] = value.default\\n        if value.missing is not missing:\\n            namespace[field_symbol_name(key) + \\'__missing\\'] = value.missing\\n\\n    exec_(result, namespace)\\n\\n    proxy = None  # type: Optional[SerializeProxy]\\n    marshall_method = None  # type: Union[SerializeProxy, Callable, None]\\n    if not context.is_serializing:\\n        # Deserialization always expects a dictionary.\\n        marshall_method = namespace[DictSerializer.__name__]\\n    elif jit_options.get(\\'expected_marshal_type\\') in EXPECTED_TYPE_TO_CLASS:\\n        marshall_method = namespace[EXPECTED_TYPE_TO_CLASS[\\n            jit_options[\\'expected_marshal_type\\']].__name__]\\n    else:\\n        marshall_method = SerializeProxy(\\n            namespace[DictSerializer.__name__],\\n            namespace[HybridSerializer.__name__],\\n            namespace[InstanceSerializer.__name__],\\n            threshold=threshold)\\n        proxy = marshall_method\\n\\n    def marshall(obj, many=False):\\n        if many:\\n            return [marshall_method(x) for x in obj]\\n        return marshall_method(obj)\\n\\n    if proxy:\\n        # Used to allow tests to introspect the proxy.\\n        marshall.proxy = proxy  # type: ignore\\n    marshall._source = result  # type: ignore\\n    return marshall\\n\\n\\ndef generate_unmarshall_method(schema, context=missing):\\n    context = context or JitContext()\\n    context.is_serializing = False\\n    return generate_marshall_method(schema, context)\\n',\n", " 'instruction': 'Fix a bug in string formatting',\n", " 'gt': '                    body += \\'if \"{key}\" not in res:\\'.format(key=result_key)\\n                    with body.indent():\\n                        body += \\'raise ValueError()\\'\\n                if field_obj.allow_none is not True:\\n                    body += \\'if __res_get(\"{key}\", res) is None:\\'.format(\\n                        key=result_key)\\n',\n", " 'meta': {'code_index': 'Bug-Fixing_001',\n", "  'category': 'Bug-Fixing',\n", "  'file_name': 'toastedmarshmallow/jit.py'}}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["git_conflict_data[0]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["new_samples = []\n", "metadatas = []\n", "\n", "for old_sample in git_conflict_data:\n", "    new_sample = ResearchEditPromptInput(\n", "        path=old_sample[\"meta\"][\"file_name\"],\n", "        prefix=old_sample[\"prefix\"],\n", "        selected_code=old_sample[\"code\"],\n", "        suffix=old_sample[\"suffix\"],\n", "        instruction=old_sample[\"instruction\"],\n", "        prefix_begin=0,\n", "        suffix_end=len(old_sample[\"prefix\"] + old_sample[\"code\"] + old_sample[\"suffix\"]),\n", "        retrieved_chunks=[],\n", "        updated_code=old_sample[\"gt\"]\n", "    )\n", "\n", "    meta = {\n", "        \"category\": old_sample[\"meta\"][\"category\"]\n", "    }\n", "\n", "    new_samples.append(new_sample)\n", "    metadatas.append(meta)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["OUT_DIR = Path(\"/tmp/test_91\")\n", "\n", "OUT_DIR.mkdir(exist_ok=True)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["dir_max = defaultdict(lambda: 0)\n", "\n", "for sample, meta in zip(new_samples, metadatas):\n", "    cur_dir = OUT_DIR / meta[\"category\"] \n", "    cur_dir.mkdir(exist_ok=True)\n", "\n", "    cur_idx = dir_max[cur_dir]\n", "    dir_max[cur_dir] += 1\n", "\n", "    sample_path = cur_dir / f\"sample_{cur_idx}.json\"\n", "    meta_path = cur_dir / f\"meta_{cur_idx}.json\"\n", "\n", "    with sample_path.open(\"w\") as f:\n", "        json.dump(asdict(sample), f, indent=2)\n", "\n", "    with meta_path.open(\"w\") as f:\n", "        json.dump(meta, f, indent=2)\n", "\n", "    \n", "    "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
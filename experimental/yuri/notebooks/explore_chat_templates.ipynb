{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import os\n", "os.environ['PYTHONPATH'] += \":/home/<USER>/repos/augment\"\n", "\n", "from research.eval.edit.data_tools.collected import load_data\n", "from research.eval.edit.utils import pretty_json \n", "from research.eval.edit.prompts import make_full_code_v1, make_prompt_v1\n", "from transformers import AutoTokenizer\n", "from research.core.llama_prompt_formatters import CodeLlamaChatFormatter,\\\n", "                                                  Dialog,\\\n", "                                                  DeepSeekInstructForm<PERSON>er,\\\n", "                                                  WizardCoderChatFormatter\n", "\n", "\n", "DATA_DIR = \"/home/<USER>/repos/augment/experimental/dxy/edits/eval/data\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"code\": \"data = []\\n\\nwith open(PATH) as f:\\n\",\n", "    \"prefix\": \"PATH = '~/datasets/edit/commitpackft/data.jsonl'\\n\\n\",\n", "    \"suffix\": \"    for line in f:\\n        datum = json.loads(line[:-1])\\n        data.append(datum)\\n\",\n", "    \"instruction\": \"use pathlib library to open file\",\n", "    \"gt\": \"import pathlib\\n\\ndata = []\\n\\nwith pathlib.Path(PATH).open(\\\"r\\\") as f:\\n\"\n", "}\n"]}], "source": ["data = load_data(DATA_DIR)\n", "sample = data[0]\n", "\n", "print(pretty_json(sample))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results are equal\n"]}], "source": ["# LLama\n", "tokenizer = AutoTokenizer.from_pretrained(\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Instruct-hf\")\n", "prompt = make_prompt_v1(sample[\"instruction\"], \n", "                        sample[\"code\"],\n", "                        sample[\"prefix\"],\n", "                        sample[\"suffix\"])\n", "\n", "#### Using HF\n", "messages=[{ 'role': 'user', 'content': prompt}]\n", "hf_prompt = tokenizer.apply_chat_template(messages, return_tensors=\"pt\")\n", "hf_prompt = hf_prompt[0].tolist()\n", "\n", "#### Using in-house formatters\n", "formatter = CodeLlamaChatFormatter()\n", "dialog = Dialog(messages=[prompt])\n", "fmt_prompt = formatter.prepare_chat_prompt(dialog) \n", "\n", "assert len(hf_prompt) == len(fmt_prompt), \"Diff length\"\n", "for i, (a, b) in enumerate(zip(hf_prompt, fmt_prompt)):\n", "    assert a == b, f\"Diff at position {i}\"\n", "\n", "print(\"Results are equal\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results are equal\n"]}], "source": ["DEEPSEEK_JINJA_TEMPLATE = \"\"\"<｜begin▁of▁sentence｜>\n", "{%- set ns = namespace(found_item=false) -%}\n", "{%- for message in messages -%}\n", "    {%- if message['role'] == 'system' -%}\n", "        {%- set ns.found_item = true -%}\n", "   {%- endif -%}\n", "{%- endfor -%}\n", "{%- if not ns.found_item -%}\n", "{{'You are an AI programming assistant, utilizing the Deepseek Coder model, developed by Deepseek Company, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\\n'}}\n", "{%- endif %}\n", "{%- for message in messages %}\n", "    {%- if message['role'] == 'system' %}\n", "{{ message['content'] }}\n", "    {%- else %}\n", "        {%- if message['role'] == 'user' %}\n", "{{'\\n\\n### Instruction:\\n\\n' + message['content'] + '\\n'}}\n", "        {%- else %}\n", "{{'### Response:\\n' + message['content'] + '\\n<|EOT|>\\n'}}\n", "        {%- endif %}\n", "    {%- endif %}\n", "{%- endfor %}\n", "{{'### Response:\\n'}}\n", "\"\"\"\n", "\n", "\n", "# Deepseek\n", "tokenizer = AutoTokenizer.from_pretrained(\"/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-instruct\")\n", "prompt = make_prompt_v1(sample[\"instruction\"], \n", "                        sample[\"code\"],\n", "                        sample[\"prefix\"],\n", "                        sample[\"suffix\"])\n", "tokenizer.chat_template = DEEPSEEK_JINJA_TEMPLATE\n", "\n", "#### Using HF\n", "messages=[\n", "    { 'role': 'system', 'content': DeepSeekInstructFormatter._DEFAULT_SYSTEM_PROMPT},\n", "    { 'role': 'user', 'content': prompt}\n", "]\n", "hf_prompt = tokenizer.apply_chat_template(messages, return_tensors=\"pt\")\n", "hf_prompt = hf_prompt[0].tolist()\n", "\n", "#### Using in-house formatters\n", "formatter = DeepSeekInstructFormatter()\n", "dialog = Dialog(messages=[prompt])\n", "fmt_prompt = formatter.prepare_chat_prompt(dialog) \n", "\n", "assert len(hf_prompt) == len(fmt_prompt), \"Diff length\"\n", "for i, (a, b) in enumerate(zip(hf_prompt, fmt_prompt)):\n", "    assert a == b, f\"Diff at position {i}\"\n", "\n", "print(\"Results are equal\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results are equal\n"]}], "source": ["WIZARDCODER_JINJA_TEMPLATE = \"\"\"\n", "{%- for message in messages %}\n", "    {%- if message['role'] == 'system' %}\n", "{{ message['content'] }}\n", "    {%- else %}\n", "        {%- if message['role'] == 'user' %}\n", "{{'\\n\\n### Instruction:\\n\\n' + message['content'] + '\\n'}}\n", "        {%- else %}\n", "{{'### Response:\\n' + message['content'] + '\\n<|EOT|>\\n'}}\n", "        {%- endif %}\n", "    {%- endif %}\n", "{%- endfor %}\n", "{{'### Response:\\n'}}\n", "\"\"\"\n", "\n", "# WizardCoder\n", "tokenizer = AutoTokenizer.from_pretrained(\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0\")\n", "prompt = make_prompt_v1(sample[\"instruction\"], \n", "                        sample[\"code\"],\n", "                        sample[\"prefix\"],\n", "                        sample[\"suffix\"])\n", "tokenizer.chat_template = WI<PERSON>AR<PERSON>ODER_JINJA_TEMPLATE\n", "\n", "#### Using HF\n", "messages=[\n", "    { 'role': 'system', 'content': WizardCoderChatFormatter._DEFAULT_SYSTEM_PROMPT},\n", "    { 'role': 'user', 'content': prompt}\n", "]\n", "hf_prompt = tokenizer.apply_chat_template(messages, return_tensors=\"pt\")\n", "hf_prompt = hf_prompt[0].tolist()\n", "\n", "#### Using in-house formatters\n", "formatter = WizardCoderChatFormatter()\n", "dialog = Dialog(messages=[prompt])\n", "fmt_prompt = formatter.prepare_chat_prompt(dialog) \n", "\n", "assert len(hf_prompt) == len(fmt_prompt), \"Diff length\"\n", "for i, (a, b) in enumerate(zip(hf_prompt, fmt_prompt)):\n", "    assert a == b, f\"Diff at position {i}\"\n", "\n", "print(\"Results are equal\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# for c in [32013, 2042, 185, 3495, 13, 13518, 3649]:\n", "# for c in [3475, 25, 9913, 17858, 13518, 21289, 32013]:\n", "#     print(f\"|{tokenizer.decode([c])}|\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for i, c in enumerate([*zip(hf_prompt, fmt_prompt)]):\n", "#     print(f\"{'+' if c[0] == c[1] else '-'} {i} {c}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# This notebook is supposed to be run on GCP, since we need access to buckets with blobs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import hashlib\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from google.cloud import storage\n", "from google.cloud.exceptions import NotFound\n", "from multiprocessing import Pool\n", "\n", "from base.datasets.gcs_blob_cache import GCSBlobCache"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Example data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Relevant config: experimental/colin/projects/code_edits/api_eval/with_retrieval.yml\n", "PATCHES_PATH = Path(\"/home/<USER>/data/api_task_COLINS_CODEEDITS.dev/google/pyglove_patches.jsonl\")\n", "DOCUMENTS_PATH = Path(\"/home/<USER>/data/api_task_COLINS_CODEEDITS.dev/google/pyglove_retrieval_db.jsonl\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(PATCHES_PATH) as f:\n", "    patches = [json.loads(line) for line in f.readlines()]\n", "\n", "with open(DOCUMENTS_PATH) as f:\n", "    documents = [json.loads(line) for line in f.readlines()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["patches[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON>'s data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VENDOR_NAME = \"turing\"\n", "\n", "# Copy of data stored on CoreWeave: /mnt/efs/aug-cw-las1-hdc/code-edit-logs/aitutor-turing (pareto)\n", "VENDOR_DATA = Path(f\"/home/<USER>/data/data-export-jan-31/aitutor-{VENDOR_NAME}\")\n", "\n", "BUCKET = storage.Client().bucket(f\"augment-blob-exporter-aitutor-{VENDOR_NAME}-prod\")\n", "\n", "# Where we store data in Hydra format\n", "OUTPUT_DIR = Path(f\"/home/<USER>/data/hydra-data-jan-31/{VENDOR_NAME}\")\n", "OUTPUT_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_blobs = list(BUCKET.list_blobs())\n", "print(f\"Total blobs: {len(all_blobs)}\")\n", "print(f\"Num blobs with metadata: {len(list(filter(lambda x: x.metadata is not None, all_blobs)))}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GCS_BLOB_CACHE = GCSBlobCache(BUCKET, \"blobs\", int(1e9), num_threads=16)\n", "\n", "def get_blobs_as_documents(blob_names):\n", "    patches = GCS_BLOB_CACHE.get(blob_names)\n", "    if patches is None:\n", "        raise ValueError(\"Failed to download blobs\")\n", "    docs = []\n", "    for patch in patches:\n", "        if patch is None:\n", "            continue\n", "        patch_content = (patch.content).replace(\"\\r\\n\", \"\\n\")\n", "        doc = {\n", "            \"text\": patch_content,\n", "            \"path\": str(patch.path),\n", "            \"id\": doc_to_id(patch_content),\n", "        }\n", "        docs.append(doc)\n", "\n", "    num_success = len(list(filter(lambda d: d is not None, docs)))\n", "    print(f\"Successfully downloaded {num_success} out of {len(blob_names)} blobs.\")\n", "    return docs\n", "\n", "def doc_to_id(text: str) -> str:\n", "    digester = hashlib.sha256()\n", "    digester.update(bytearray(text, \"utf8\"))\n", "    return digester.hexdigest()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_paths = list(VENDOR_DATA.glob(\"*-Call.json\"))\n", "print(len(all_paths))\n", "all_paths[:4]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hydra_samples = {}\n", "\n", "for sample_path in tqdm(all_paths):\n", "    request_id = sample_path.stem[:-5]\n", "    feedback_path = VENDOR_DATA / f\"{request_id}-Feedback.json\"\n", "    if not feedback_path.exists():\n", "        print(f\"Feedback file {feedback_path} does not exist.\")\n", "        continue\n", "\n", "    with sample_path.open() as f:\n", "        sample = json.load(f)\n", "    with feedback_path.open() as f:\n", "        feedback = json.load(f)\n", "\n", "    # For eval/train we only care about accept-annotation and/or accept\n", "    if feedback[\"status\"] in {\"reject\", \"change_prompt\", \"change-prompt\", \"reject-by-undo\"}:\n", "        continue\n", "    assert feedback[\"status\"] in {\"accept-annotation\", \"accept\"}, feedback[\"status\"]\n", "\n", "    # https://github.com/augmentcode/augment/blob/0b30fd01146dc1b12d74772c5e3fe8b6974ddbbb/research/eval/harness/tasks/hydra_task.py#L219\n", "    for field in [\"instruction\", \"selected_code\", \"prefix\", \"suffix\", \"path\", \"response\"]:\n", "        sample[field] = sample[field].replace(\"\\r\\n\", \"\\n\")\n", "    for field in [\"human_annotated_instruction\", \"human_annotated_text\"]:\n", "        if field in feedback and feedback[field] is not None:\n", "            feedback[field] = feedback[field].replace(\"\\r\\n\", \"\\n\")\n", "\n", "    if feedback[\"status\"] == \"accept\":\n", "        instruction = sample[\"instruction\"]\n", "        target_code = sample[\"response\"]\n", "    else:\n", "        instruction = feedback[\"human_annotated_instruction\"]\n", "        target_code = feedback[\"human_annotated_text\"]\n", "    file_content = sample[\"prefix\"] + target_code + sample[\"suffix\"]\n", "\n", "    # Roughly 2k tokens\n", "    if (len(target_code) > 6000) or (len(sample[\"selected_code\"]) > 6000): \n", "        print(f\"Skipping {request_id} due to too long target_code length.\")\n", "        continue\n", "\n", "    patch = {\n", "        \"char_start\": len(sample[\"prefix\"]),\n", "        \"char_end\": len(sample[\"prefix\"]) + len(target_code),\n", "        \"file_name\": sample[\"path\"],\n", "        \"repository\": f\"{request_id}/repo\",\n", "        \"file_content\": file_content,\n", "        \"patch_content\": target_code,\n", "        \"_extra\": {\n", "            \"buggy_version\": sample[\"selected_code\"],\n", "            \"instruction\": instruction,\n", "        }\n", "    }\n", "    # Use separate database for each sample\n", "    retrieval_db = get_blobs_as_documents(sample[\"blobs\"])\n", "\n", "    hydra_samples[request_id] = {\n", "        \"patches\": [patch],\n", "        \"retrieval_db\": retrieval_db,\n", "    }\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list(filter(lambda s: len(s[1][\"retrieval_db\"]) > 0, hydra_samples.items()))[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for request_id, hydra_sample in hydra_samples.items():\n", "    repo_dir = OUTPUT_DIR / request_id\n", "    repo_dir.mkdir(exist_ok=True)\n", "\n", "    with (repo_dir / \"repo_patches.jsonl\").open(\"w\") as f:\n", "        assert len(hydra_sample[\"patches\"]) == 1\n", "        f.write(json.dumps(hydra_sample[\"patches\"][0]))\n", "        f.write(\"\\n\")\n", "\n", "    with (repo_dir / \"repo_retrieval_db.jsonl\").open(\"w\") as f:\n", "        for doc_dict in hydra_sample[\"retrieval_db\"]:\n", "            f.write(json.dumps(doc_dict))\n", "            f.write(\"\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["OUTPUT_DIR"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 2}
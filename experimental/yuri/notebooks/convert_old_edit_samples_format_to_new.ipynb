{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import json\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos_an/augment/research/gpt-neox\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from pathlib import Path\n", "from research.eval.edit.data_tools.collected import load_raw_data, _has_keys, get_lines, load_data, load_data_git_conflict_format\n", "from collections import defaultdict\n", "from typing import Any, Dict, List, Tuple"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def load_data_tmp(data_dir: Path) -> List[Dict[str, Any]]:\n", "    \"\"\"Returns a list of data samples.\n", "\n", "    Each sample is a dict that contains code, prefix, suffix, instruction, respective gt and some meta information.\n", "    \"\"\"\n", "\n", "    edits_samples, code_samples = load_raw_data(data_dir)\n", "\n", "    samples: List[Dict[str, str]] = []\n", "\n", "    for index, edits in edits_samples.items():\n", "        if index not in code_samples:\n", "            print(f\"WARNING: No code sample for index {index}\")\n", "            continue\n", "        if \"code\" not in code_samples[index]:\n", "            print(f\"WARNING: No code for index {index}\")\n", "            continue\n", "\n", "        full_code = code_samples[index][\"code\"]\n", "        for j, e in enumerate(edits):\n", "            if not _has_keys(e, [\"lrange\", \"instruction\", \"response\"]):\n", "                print(f\"WARNING: Missing key in the {index}-{j}-th edit data.\")\n", "                continue\n", "            refuse_edit = e.get(\"refuse_edit\", False)\n", "            if refuse_edit or e[\"response\"] is None:\n", "                print(\n", "                    f\"Skipping the {index}-{j}-th edit data because refuse_edit is True\"\n", "                )\n", "                continue\n", "            # for instruction in :\n", "            i, j = e[\"lrange\"]\n", "            cur_sample = {\n", "                \"code\": get_lines(full_code, i, j),\n", "                \"prefix\": get_lines(full_code, to_=i),\n", "                \"suffix\": get_lines(full_code, from_=j),\n", "                \"instruction\": e[\"instruction\"],\n", "                \"gt\": e[\"response\"],\n", "                \"meta\": {\n", "                    \"code_index\": index,\n", "                },\n", "            }\n", "            samples.append(cur_sample)\n", "\n", "    return samples\n", "\n", "def combine(prefix, code, gt, suffix):\n", "    p_lines = prefix.splitlines(True)\n", "    c_lines = code.splitlines(True)\n", "    g_lines = gt.splitlines(True)\n", "    s_lines = suffix.splitlines(True)\n", "\n", "    if len(s_lines) == 0:\n", "        s_lines = [\"\"]\n", "\n", "    lines = p_lines + [\"<<<<<<<\\n\"] + c_lines + [\"=======\\n\"] + g_lines + [\">>>>>>>\\n\"] + s_lines\n", "\n", "    return \"\".join(lines)\n", "\n", "\n", "def check_eq(a, b):\n", "    return (\n", "        a[\"code\"] == b[\"code\"] \n", "        and a[\"prefix\"] == b[\"prefix\"]\n", "        and a[\"suffix\"] == b[\"suffix\"]\n", "        and a[\"gt\"] == b[\"gt\"] \n", "        and a[\"instruction\"] == b[\"instruction\"]\n", "    )\n", "    "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/home/<USER>/repos/augment/research/eval/edit/data_new/manual'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m OLD_SAMPLES_PATH \u001b[38;5;241m=\u001b[39m Path(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/home/<USER>/repos/augment/research/eval/edit/data\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      2\u001b[0m NEW_SAMPLES_PATH \u001b[38;5;241m=\u001b[39m Path(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/home/<USER>/repos/augment/research/eval/edit/data_new/manual\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 4\u001b[0m \u001b[43mNEW_SAMPLES_PATH\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmkdir\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexist_ok\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/pathlib.py:1323\u001b[0m, in \u001b[0;36mPath.mkdir\u001b[0;34m(self, mode, parents, exist_ok)\u001b[0m\n\u001b[1;32m   1319\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1320\u001b[0m \u001b[38;5;124;03<PERSON><PERSON>reate a new directory at this given path.\u001b[39;00m\n\u001b[1;32m   1321\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1322\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1323\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_accessor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmkdir\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1324\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mFileNotFoundError\u001b[39;00m:\n\u001b[1;32m   1325\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m parents \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mparent \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m:\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/home/<USER>/repos/augment/research/eval/edit/data_new/manual'"]}], "source": ["OLD_SAMPLES_PATH = Path(\"/home/<USER>/repos/augment/research/eval/edit/data\")\n", "NEW_SAMPLES_PATH = Path(\"/home/<USER>/repos/augment/research/eval/edit/data_new/manual\")\n", "\n", "NEW_SAMPLES_PATH.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["21"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["old_data = load_data_tmp(OLD_SAMPLES_PATH)\n", "len(old_data)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["sample_id_dict = defaultdict(int)\n", "\n", "for sample in old_data:\n", "    code = combine(sample[\"prefix\"], sample[\"code\"], sample[\"gt\"], sample[\"suffix\"])\n", "    meta = {\n", "        \"repo_url\": \"N/A\",\n", "        \"file_name\": \"N/A\",\n", "        \"instructions\": sample[\"instruction\"],\n", "        \"category\": \"manual\",\n", "        \"language\": \"python\",\n", "        \"inverse_instructions\": []\n", "    }\n", "    code_index = sample['meta']['code_index']\n", "    sample_index = sample_id_dict[code_index]\n", "    sample_id_dict[code_index] += 1\n", "    cur_id = f\"{code_index}_{sample_index}\"\n", "\n", "    txt_path = NEW_SAMPLES_PATH / f\"{cur_id}.txt\"\n", "    json_path = NEW_SAMPLES_PATH / f\"{cur_id}.json\"\n", "    with open(txt_path, 'w') as f:\n", "        f.write(code)\n", "    with open(json_path, 'w') as f:\n", "        json.dump(meta, f, indent=2)\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(29, 29)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["new_samples = load_data_git_conflict_format(Path(\"/home/<USER>/repos/augment/research/eval/edit/data_new\"))\n", "old_samples = load_data(OLD_SAMPLES_PATH)\n", "\n", "len(new_samples), len(old_samples)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n", "True\n"]}], "source": ["for ns, os in zip(new_samples, old_samples):\n", "    print(check_eq(ns, os))\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["78"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["all_new_samples = load_data_git_conflict_format(Path(\"/home/<USER>/repos/augment/research/eval/edit/data\"))\n", "len(all_new_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
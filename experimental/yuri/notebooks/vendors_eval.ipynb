{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HYDRA_EVAL_RESULTS_PATH = Path(\"/mnt/efs/augment/eval/jobs/DLYwASJB/yuri-a100_DroidRepoCodeEditSystem_hydra_completed_patches.jsonl\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_samples = []\n", "with HYDRA_EVAL_RESULTS_PATH.open(\"r\") as f:\n", "    for line in f:\n", "        all_samples.append(json.loads(line))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[6]\n", "sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sample[\"patch\"][\"_extra\"][\"instruction\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Full prompt\n", "print(sample[\"prompt\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Selected Code\n", "print(sample[\"patch\"][\"_extra\"][\"buggy_version\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ground Truth\n", "print(sample[\"patch\"][\"patch_content\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generated\n", "print(sample[\"generation\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample[\"patch\"][\"_extra\"][\"buggy_version\"] == sample[\"generation\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample[\"patch\"][\"patch_content\"] == sample[\"generation\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_correct = 0\n", "num_copy = 0\n", "num_wtf = 0\n", "num_correct_only_because_of_copy = 0\n", "\n", "for sample in all_samples:\n", "    selected_code = sample[\"patch\"][\"_extra\"][\"buggy_version\"]\n", "    gt = sample[\"patch\"][\"patch_content\"]\n", "    generated = sample[\"generation\"]\n", "\n", "    num_correct += (gt == generated)\n", "    num_copy += (selected_code == generated)\n", "    num_wtf += (selected_code == gt)\n", "\n", "    num_correct_only_because_of_copy += (gt == generated == selected_code)\n", "\n", "num_correct, num_copy, num_wtf, num_correct_only_because_of_copy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
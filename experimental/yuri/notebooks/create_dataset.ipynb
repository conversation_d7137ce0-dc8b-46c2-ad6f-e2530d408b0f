{"cells": [{"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import pickle\n", "import numpy as np\n", "import torch\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from megatron.tokenizer.tokenizer import DeepSeekInstructTokenizer\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "\n", "\n", "\n", "DATA_PATH = Path('/home/<USER>/data/InstructCoder/all_data.json')\n", "OUTPUT_PATH = Path('/home/<USER>/data/InstructCoder/processed/v1')"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass \n", "\n", "\n", "def pretty_json(json_s) -> str:\n", "    return json.dumps(json_s, indent=4)\n", "\n", "@dataclass\n", "class VerySimpleEditData:\n", "    \"\"\"Very simple edit data class.\"\"\"\n", "\n", "    selected_code: str\n", "    modified_code: str\n", "    instruction: str\n", "\n", "\n", "def create_edit_data(data):\n", "    result = []\n", "    for raw_sample in tqdm(data):\n", "        sample = VerySimpleEditData(selected_code=raw_sample[\"input\"],\n", "                                    modified_code=raw_sample[\"output\"],\n", "                                    instruction=raw_sample[\"instruction\"])\n", "        result.append(sample)\n", "    return result\n", "\n", "def split_train_val(data, ratio=0.95):\n", "    indxs = [*range(len(data))]\n", "    random.seed(42)\n", "\n", "    random.shuffle(indxs)\n", "    \n", "    train_size = int(len(data) * ratio)\n", "    train_indxs = indxs[:train_size]\n", "    val_indxs = indxs[train_size:]\n", "    return [data[i] for i in train_indxs], [data[i] for i in val_indxs]\n", "    \n", "def prompt_v1(sample, tokenizer):\n", "    input_prompt = (f\"{tokenizer.bos_token}\" + \n", "                    f\"<INST>{sample.instruction}\" + \n", "                    f\"<SELECTED>{sample.selected_code}\" + \n", "                    f\"<MODIFIED>\"\n", "                   )\n", "    target_prompt = (f\"{sample.modified_code}\" +\n", "                     f\"{tokenizer.eod_token}\")\n", "    \n", "    return {\"input\" : input_prompt, \n", "            \"target\": target_prompt}\n", "    \n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["original_data = json.loads(DATA_PATH.read_text())"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of samples: 114239\n", "{\n", "    \"instruction\": \"Prefer list slicing over concatenation for appending elements to a list.\",\n", "    \"input\": \"data = []\\nwith open(\\\"large_file.txt\\\", \\\"r\\\") as f:\\n    for line in f:\\n        # Process the line\\n        processed_line = line.strip().split(\\\",\\\")\\n        data += processed_line  # Append to the list\",\n", "    \"output\": \"data = []\\nwith open(\\\"large_file.txt\\\", \\\"r\\\") as f:\\n    for line in f:\\n        # Process the line\\n        processed_line = line.strip().split(\\\",\\\")\\n        data[len(data):] = processed_line  # Append to the list using slicing\",\n", "    \"scenario\": \"A data processing script that reads a large file and appends the processed data to a list, using list slicing for efficiency.\",\n", "    \"scenario_list\": \"Scenario 1: A data processing script that reads a large file and appends the processed data to a list, using list slicing for efficiency.\\n\\nScenario 2: An image manipulation tool that creates a list of pixel values and uses list slicing to add new pixels or modify existing ones.\\n\\nScenario 3: A machine learning algorithm that generates training data in batches and stores them in a list, preferring list slicing over concatenation for performance reasons.\\n\\nScenario 4: A web scraper that extracts data from multiple pages and adds them to a list, using list slicing to optimize memory usage.\\n\\nScenario 5: A chatbot application that captures user inputs and generates responses by appending them to a list, using list slicing to avoid excessive memory consumption.\\n\\nScenario 6: A game development project that maintains a list of player scores and ranks, utilizing list slicing for efficient updates and sorting.\\n\\nScenario 7: A testing framework that logs test results to a list and uses list slicing to append new results without creating unnecessary copies.\\n\\nScenario 8: A natural language processing task that requires building a vocabulary list from a corpus of text, using list slicing to efficiently add new words.\\n\\nScenario 9: A scientific simulation that generates a list of numerical values at each time step, using list slicing to update the list without creating redundant copies.\\n\\nScenario 10: A music recommendation system that builds a list of recommended songs based on user preferences, employing list slicing to add new recommendations while preserving previous ones.\"\n", "}\n"]}], "source": ["print(f'Total number of samples: {len(original_data)}')\n", "print(pretty_json(original_data[0]))"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 114239/114239 [00:00<00:00, 570741.73it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Train size: 108527. Validation size: 5712\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["edit_data = create_edit_data(original_data)\n", "\n", "train_data, val_data = split_train_val(edit_data)\n", "\n", "print(f\"Train size: {len(train_data)}. Validation size: {len(val_data)}\")"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["bos token : <｜begin▁of▁sentence｜>, eos token: <|EOT|>, vocab size: 32035\n"]}], "source": ["tokenizer = DeepSeekInstructTokenizer(\"/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/\")\n", "print(f\"bos token : {tokenizer.bos_token}, eos token: {tokenizer.eod_token}, vocab size: {tokenizer.vocab_size}\")"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 108527/108527 [00:00<00:00, 464834.03it/s]\n", "100%|██████████| 5712/5712 [00:00<00:00, 379164.13it/s]\n"]}], "source": ["train_prompts = [*map(lambda s: prompt_v1(s, tokenizer), tqdm(train_data))]\n", "val_prompts = [*map(lambda s: prompt_v1(s, tokenizer), tqdm(val_data))]"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<｜begin▁of▁sentence｜><INST>Add error handling to ensure that the application gracefully handles unexpected errors raised during multi-threaded or multi-process execution.<SELECTED>import multiprocessing\n", "\n", "def task1(data):\n", "    # do some processing\n", "    return processed_data1\n", "\n", "def task2(data):\n", "    # do some processing\n", "    return processed_data2\n", "\n", "def task3(data):\n", "    # do some processing\n", "    return processed_data3\n", "\n", "data = [1, 2, 3, 4, 5]\n", "\n", "with multiprocessing.Pool(processes=3) as pool:\n", "    results1 = pool.apply_async(task1, args=(data,))\n", "    results2 = pool.apply_async(task2, args=(data,))\n", "    results3 = pool.apply_async(task3, args=(data,))\n", "\n", "    processed_data1 = results1.get()\n", "    processed_data2 = results2.get()\n", "    processed_data3 = results3.get()\n", "\n", "print(processed_data1)\n", "print(processed_data2)\n", "print(processed_data3)<MODIFIED>\n", "==============================\n", "import multiprocessing\n", "import logging\n", "\n", "def task1(data):\n", "    try:\n", "        # do some processing\n", "        return processed_data1\n", "    except Exception as e:\n", "        logging.exception(e)\n", "        return None\n", "\n", "def task2(data):\n", "    try:\n", "        # do some processing\n", "        return processed_data2\n", "    except Exception as e:\n", "        logging.exception(e)\n", "        return None\n", "\n", "def task3(data):\n", "    try:\n", "        # do some processing\n", "        return processed_data3\n", "    except Exception as e:\n", "        logging.exception(e)\n", "        return None\n", "\n", "data = [1, 2, 3, 4, 5]\n", "\n", "with multiprocessing.Pool(processes=3) as pool:\n", "    results1 = pool.apply_async(task1, args=(data,))\n", "    results2 = pool.apply_async(task2, args=(data,))\n", "    results3 = pool.apply_async(task3, args=(data,))\n", "\n", "    processed_data1 = results1.get()\n", "    processed_data2 = results2.get()\n", "    processed_data3 = results3.get()\n", "\n", "if processed_data1:\n", "    print(processed_data1)\n", "else:\n", "    print(\"Error occurred during task1 processing.\")\n", "if processed_data2:\n", "    print(processed_data2)\n", "else:\n", "    print(\"Error occurred during task2 processing.\")\n", "if processed_data3:\n", "    print(processed_data3)\n", "else:\n", "    print(\"Error occurred during task3 processing.\")<|EOT|>\n"]}], "source": ["print(train_prompts[0][\"input\"])\n", "print(\"=\" * 30)\n", "print(train_prompts[0][\"target\"])"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 108527/108527 [02:04<00:00, 870.92it/s]\n", "100%|██████████| 5712/5712 [00:06<00:00, 907.78it/s]\n"]}], "source": ["def encode(prompt, tokenizer):\n", "    input_tokens = tokenizer.tokenize(prompt[\"input\"])\n", "    target_tokens = tokenizer.tokenize(prompt[\"target\"])\n", "\n", "    result = torch.cat([\n", "        -1 * torch.tensor(input_tokens, dtype=torch.int32),\n", "        torch.tensor(target_tokens, dtype=torch.int32)\n", "    ])\n", "\n", "    return result\n", "\n", "train_tokenized = [*map(lambda p: encode(p, tokenizer), tqdm(train_prompts))]\n", "val_tokenized = [*map(lambda p: encode(p, tokenizer), tqdm(val_prompts))]\n"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([-32013,    -27, -24607,    -29,  -3511,  -2179, -14326,   -276,  -5450,\n", "          -344,   -254,  -3708, -11902,  -3189, -22277, -14522,  -6615,  -8475,\n", "         -2310,  -5249,    -12, -12355,   -271,   -409,  -5249,    -12,  -6960,\n", "        -11477,    -13,    -27,  -7507,  -2289,    -29,  -1892, -14798,   -295,\n", "          -999,   -272,   -185,   -185,  -1551,  -5256,    -16,     -7,  -2448,\n", "         -1772,   -185,   -315,  -1494,   -533,   -738,  -8430,   -185,   -315,\n", "          -967, -18526,    -62,  -2448,    -16,   -185,   -185,  -1551,  -5256,\n", "           -17,     -7,  -2448,  -1772,   -185,   -315,  -1494,   -533,   -738,\n", "         -8430,   -185,   -315,   -967, -18526,    -62,  -2448,    -17,   -185,\n", "          -185,  -1551,  -5256,    -18,     -7,  -2448,  -1772,   -185,   -315,\n", "         -1494,   -533,   -738,  -8430,   -185,   -315,   -967, -18526,    -62,\n", "         -2448,    -18,   -185,   -185,  -2448,   -405,   -821,    -16,    -11,\n", "          -207,    -17,    -11,   -207,    -18,    -11,   -207,    -19,    -11,\n", "          -207,    -20,    -60,   -185,   -185,  -2287, -14798,   -295,   -999,\n", "          -272,    -13, -17328,     -7,  -6960,   -257,    -28,    -18,     -8,\n", "          -372,  -7435,    -25,   -185,   -315,  -2461,    -16,   -405,  -7435,\n", "           -13, -16922,    -62, -22480,     -7, -11500,    -16,    -11, -10936,\n", "           -28,     -7,  -2448,    -11,  -1435,   -185,   -315,  -2461,    -17,\n", "          -405,  -7435,    -13, -16922,    -62, -22480,     -7, -11500,    -17,\n", "           -11, -10936,    -28,     -7,  -2448,    -11,  -1435,   -185,   -315,\n", "         -2461,    -18,   -405,  -7435,    -13, -16922,    -62, -22480,     -7,\n", "        -11500,    -18,    -11, -10936,    -28,     -7,  -2448,    -11,  -1435,\n", "          -185,   -185,   -315, -18526,    -62,  -2448,    -16,   -405,  -2461,\n", "           -16,    -13,   -703,   -822,   -185,   -315, -18526,    -62,  -2448,\n", "           -17,   -405,  -2461,    -17,    -13,   -703,   -822,   -185,   -315,\n", "        -18526,    -62,  -2448,    -18,   -405,  -2461,    -18,    -13,   -703,\n", "          -822,   -185,   -185,  -4128,     -7,  -6960,   -271,    -62,  -2448,\n", "           -16,     -8,   -185,  -4128,     -7,  -6960,   -271,    -62,  -2448,\n", "           -17,     -8,   -185,  -4128,     -7,  -6960,   -271,    -62,  -2448,\n", "           -18,     -8,    -27, -17898, -25610,  -2289,    -29,   1892,  14798,\n", "           295,    999,    272,    185,   1892,  17932,    185,    185,   1551,\n", "          5256,     16,      7,   2448,   1772,    185,    315,   1675,     25,\n", "           185,    436,   1494,    533,    738,   8430,    185,    436,    967,\n", "         18526,     62,   2448,     16,    185,    315,   5069,  12982,    372,\n", "           300,     25,    185,    436,  17932,     13,  15726,      7,     68,\n", "             8,    185,    436,    967,   7747,    185,    185,   1551,   5256,\n", "            17,      7,   2448,   1772,    185,    315,   1675,     25,    185,\n", "           436,   1494,    533,    738,   8430,    185,    436,    967,  18526,\n", "            62,   2448,     17,    185,    315,   5069,  12982,    372,    300,\n", "            25,    185,    436,  17932,     13,  15726,      7,     68,      8,\n", "           185,    436,    967,   7747,    185,    185,   1551,   5256,     18,\n", "             7,   2448,   1772,    185,    315,   1675,     25,    185,    436,\n", "          1494,    533,    738,   8430,    185,    436,    967,  18526,     62,\n", "          2448,     18,    185,    315,   5069,  12982,    372,    300,     25,\n", "           185,    436,  17932,     13,  15726,      7,     68,      8,    185,\n", "           436,    967,   7747,    185,    185,   2448,    405,    821,     16,\n", "            11,    207,     17,     11,    207,     18,     11,    207,     19,\n", "            11,    207,     20,     60,    185,    185,   2287,  14798,    295,\n", "           999,    272,     13,  17328,      7,   6960,    257,     28,     18,\n", "             8,    372,   7435,     25,    185,    315,   2461,     16,    405,\n", "          7435,     13,  16922,     62,  22480,      7,  11500,     16,     11,\n", "         10936,     28,      7,   2448,     11,   1435,    185,    315,   2461,\n", "            17,    405,   7435,     13,  16922,     62,  22480,      7,  11500,\n", "            17,     11,  10936,     28,      7,   2448,     11,   1435,    185,\n", "           315,   2461,     18,    405,   7435,     13,  16922,     62,  22480,\n", "             7,  11500,     18,     11,  10936,     28,      7,   2448,     11,\n", "          1435,    185,    185,    315,  18526,     62,   2448,     16,    405,\n", "          2461,     16,     13,    703,    822,    185,    315,  18526,     62,\n", "          2448,     17,    405,   2461,     17,     13,    703,    822,    185,\n", "           315,  18526,     62,   2448,     18,    405,   2461,     18,     13,\n", "           703,    822,    185,    185,    351,  18526,     62,   2448,     16,\n", "            25,    185,    315,   3628,      7,   6960,    271,     62,   2448,\n", "            16,      8,    185,   7736,     25,    185,    315,   3628,   1195,\n", "          3419,  11152,   2310,   5256,     16,   8430,  29093,    185,    351,\n", "         18526,     62,   2448,     17,     25,    185,    315,   3628,      7,\n", "          6960,    271,     62,   2448,     17,      8,    185,   7736,     25,\n", "           185,    315,   3628,   1195,   3419,  11152,   2310,   5256,     17,\n", "          8430,  29093,    185,    351,  18526,     62,   2448,     18,     25,\n", "           185,    315,   3628,      7,   6960,    271,     62,   2448,     18,\n", "             8,    185,   7736,     25,    185,    315,   3628,   1195,   3419,\n", "         11152,   2310,   5256,     18,   8430,  29093,  32021],\n", "       dtype=torch.int32)"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["train_tokenized[0]"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Min len: 47 | Max len: 1575 | Mean len: 381.34411713214223\n", "Min len: 56 | Max len: 1422 | Mean len: 384.9360994397759\n"]}], "source": ["train_length = [*map(len, train_tokenized)]\n", "val_length = [*map(len, val_tokenized)]\n", "\n", "print_sizes = lambda ds : print(f\"Min len: {min(ds)} | Max len: {max(ds)} | Mean len: {np.mean(ds)}\")\n", "\n", "print_sizes(train_length)\n", "print_sizes(val_length)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 108527/108527 [00:00<00:00, 267490.16it/s]\n", "100%|██████████| 5712/5712 [00:00<00:00, 235127.68it/s]\n"]}], "source": ["for (name, dataset) in ([(\"train\", train_tokenized), (\"valid\", val_tokenized)]):\n", "    output_path = OUTPUT_PATH / name\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sample in tqdm(dataset):\n", "        builder.add_item(sample)\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'bos_token': '<｜begin▁of▁sentence｜>',\n", " 'eos_token': '<｜end▁of▁sentence｜>',\n", " 'pad_token': '<｜end▁of▁sentence｜>'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from transformers import AutoTokenizer\n", "\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"deepseek-ai/deepseek-coder-33b-instruct\", trust_remote_code=True)\n", "tokenizer.special_tokens_map"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'bos_token': '<｜begin▁of▁sentence｜>',\n", " 'eos_token': '<｜end▁of▁sentence｜>',\n", " 'pad_token': '<｜end▁of▁sentence｜>'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from megatron.tokenizer.tokenizer import DeepSeekInstructTokenizer\n", "\n", "\n", "tokenizer2 = DeepSeekInstructTokenizer(\"deepseek-ai/deepseek-coder-33b-instruct\")\n", "tokenizer2._raw_tokenizer.special_tokens_map\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["<megatron.tokenizer.tokenizer.DeepSeekInstructTokenizer at 0x7f685a9bfdc0>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "\n", "tokenizer2 = DeepSeekInstructTokenizer(\"/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/\")\n", "tokenizer2\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["l = [1, 2, 3, 10]\n", "with open('/tmp/t', 'w') as f:\n", "    json.dump(l, f)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"ename": "JSONDecodeError", "evalue": "Expecting value: line 1 column 1 (char 0)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mJSONDecodeError\u001b[0m                           Traceback (most recent call last)", "\u001b[1;32m/home/<USER>/repos/augment/experimental/yuri/notebooks/create_dataset.ipynb Cell 14\u001b[0m line \u001b[0;36m2\n\u001b[1;32m      <a href='vscode-notebook-cell://ssh-remote%2Byuri-h100/home/<USER>/repos/augment/experimental/yuri/notebooks/create_dataset.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mwith\u001b[39;00m \u001b[39mopen\u001b[39m(\u001b[39m'\u001b[39m\u001b[39m/tmp/t\u001b[39m\u001b[39m'\u001b[39m, \u001b[39m'\u001b[39m\u001b[39mr\u001b[39m\u001b[39m'\u001b[39m) \u001b[39mas\u001b[39;00m f:\n\u001b[0;32m----> <a href='vscode-notebook-cell://ssh-remote%2Byuri-h100/home/<USER>/repos/augment/experimental/yuri/notebooks/create_dataset.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m     l2 \u001b[39m=\u001b[39m json\u001b[39m.\u001b[39;49mload(f)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/__init__.py:293\u001b[0m, in \u001b[0;36mload\u001b[0;34m(fp, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[0m\n\u001b[1;32m    274\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mload\u001b[39m(fp, \u001b[39m*\u001b[39m, \u001b[39mcls\u001b[39m\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, object_hook\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, parse_float\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m,\n\u001b[1;32m    275\u001b[0m         parse_int\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, parse_constant\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, object_pairs_hook\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkw):\n\u001b[1;32m    276\u001b[0m \u001b[39m    \u001b[39m\u001b[39m\"\"\"Deserialize ``fp`` (a ``.read()``-supporting file-like object containing\u001b[39;00m\n\u001b[1;32m    277\u001b[0m \u001b[39m    a JSON document) to a Python object.\u001b[39;00m\n\u001b[1;32m    278\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    291\u001b[0m \u001b[39m    kwarg; otherwise ``JSONDecoder`` is used.\u001b[39;00m\n\u001b[1;32m    292\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 293\u001b[0m     \u001b[39mreturn\u001b[39;00m loads(fp\u001b[39m.\u001b[39;49mread(),\n\u001b[1;32m    294\u001b[0m         \u001b[39mcls\u001b[39;49m\u001b[39m=\u001b[39;49m\u001b[39mcls\u001b[39;49m, object_hook\u001b[39m=\u001b[39;49mobject_hook,\n\u001b[1;32m    295\u001b[0m         parse_float\u001b[39m=\u001b[39;49mparse_float, parse_int\u001b[39m=\u001b[39;49mparse_int,\n\u001b[1;32m    296\u001b[0m         parse_constant\u001b[39m=\u001b[39;49mparse_constant, object_pairs_hook\u001b[39m=\u001b[39;49mobject_pairs_hook, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkw)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/__init__.py:346\u001b[0m, in \u001b[0;36mloads\u001b[0;34m(s, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[0m\n\u001b[1;32m    341\u001b[0m     s \u001b[39m=\u001b[39m s\u001b[39m.\u001b[39mdecode(detect_encoding(s), \u001b[39m'\u001b[39m\u001b[39msurrogatepass\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[1;32m    343\u001b[0m \u001b[39mif\u001b[39;00m (\u001b[39mcls\u001b[39m \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m object_hook \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m\n\u001b[1;32m    344\u001b[0m         parse_int \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m parse_float \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m\n\u001b[1;32m    345\u001b[0m         parse_constant \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m object_pairs_hook \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m \u001b[39mnot\u001b[39;00m kw):\n\u001b[0;32m--> 346\u001b[0m     \u001b[39mreturn\u001b[39;00m _default_decoder\u001b[39m.\u001b[39;49mdecode(s)\n\u001b[1;32m    347\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mcls\u001b[39m \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[1;32m    348\u001b[0m     \u001b[39mcls\u001b[39m \u001b[39m=\u001b[39m JSONDecoder\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/decoder.py:337\u001b[0m, in \u001b[0;36mJSONDecoder.decode\u001b[0;34m(self, s, _w)\u001b[0m\n\u001b[1;32m    332\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mdecode\u001b[39m(\u001b[39mself\u001b[39m, s, _w\u001b[39m=\u001b[39mWHITESPACE\u001b[39m.\u001b[39mmatch):\n\u001b[1;32m    333\u001b[0m \u001b[39m    \u001b[39m\u001b[39m\"\"\"Return the Python representation of ``s`` (a ``str`` instance\u001b[39;00m\n\u001b[1;32m    334\u001b[0m \u001b[39m    containing a JSON document).\u001b[39;00m\n\u001b[1;32m    335\u001b[0m \n\u001b[1;32m    336\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 337\u001b[0m     obj, end \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mraw_decode(s, idx\u001b[39m=\u001b[39;49m_w(s, \u001b[39m0\u001b[39;49m)\u001b[39m.\u001b[39;49mend())\n\u001b[1;32m    338\u001b[0m     end \u001b[39m=\u001b[39m _w(s, end)\u001b[39m.\u001b[39mend()\n\u001b[1;32m    339\u001b[0m     \u001b[39mif\u001b[39;00m end \u001b[39m!=\u001b[39m \u001b[39mlen\u001b[39m(s):\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/decoder.py:355\u001b[0m, in \u001b[0;36mJSONDecoder.raw_decode\u001b[0;34m(self, s, idx)\u001b[0m\n\u001b[1;32m    353\u001b[0m     obj, end \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mscan_once(s, idx)\n\u001b[1;32m    354\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mStopIteration\u001b[39;00m \u001b[39mas\u001b[39;00m err:\n\u001b[0;32m--> 355\u001b[0m     \u001b[39mraise\u001b[39;00m JSONDecodeError(\u001b[39m\"\u001b[39m\u001b[39mExpecting value\u001b[39m\u001b[39m\"\u001b[39m, s, err\u001b[39m.\u001b[39mvalue) \u001b[39mfrom\u001b[39;00m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m    356\u001b[0m \u001b[39mreturn\u001b[39;00m obj, end\n", "\u001b[0;31mJSONDecodeError\u001b[0m: Expecting value: line 1 column 1 (char 0)"]}], "source": ["with open('/tmp/t', 'r') as f:\n", "    l2 = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
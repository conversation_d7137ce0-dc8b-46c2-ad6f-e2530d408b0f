{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from pathlib import Path\n", "\n", "from research.eval.edit.data_tools.collected import load_raw_data, load_data\n", "from research.eval.edit.utils import pretty_json\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of code samples: 7\n", "Number of edits samples: 11\n", "Example code:\n", "{\n", "    \"fname\": \"0001.txt\",\n", "    \"code\": \"PATH = '~/datasets/edit/commitpackft/data.jsonl'\\n\\ndata = []\\n\\nwith open(PATH) as f:\\n    for line in f:\\n        datum = json.loads(line[:-1])\\n        data.append(datum)\\n\"\n", "}\n", "Example edit:\n", "[\n", "    {\n", "        \"lrange\": [\n", "            2,\n", "            5\n", "        ],\n", "        \"instruction\": [\n", "            \"use pathlib library to open file\"\n", "        ],\n", "        \"response\": \"import pathlib\\n\\ndata = []\\n\\nwith pathlib.Path(PATH).open(\\\"r\\\") as f:\\n\",\n", "        \"misc\": {\n", "            \"reason_to_lrange\": \"Improving File Path Handling\",\n", "            \"category_of_instruction\": \"Code Modernization and Standardization\"\n", "        }\n", "    },\n", "    {\n", "        \"lrange\": [\n", "            0,\n", "            8\n", "        ],\n", "        \"instruction\": [\n", "            \"add missing import\"\n", "        ],\n", "        \"response\": \"import json\\n\\nPATH = '~/datasets/edit/commitpackft/data.jsonl'\\n\\ndata = []\\n\\nwith open(PATH) as f:\\n    for line in f:\\n        datum = json.loads(line[:-1])\\n        data.append(datum)\\n\",\n", "        \"misc\": {\n", "            \"reason_to_lrange\": \"Addressing Missing Dependencies\",\n", "            \"category_of_instruction\": \"Code Completeness and Error Correction\"\n", "        }\n", "    },\n", "    {\n", "        \"lrange\": [\n", "            0,\n", "            9\n", "        ],\n", "        \"instruction\": [\n", "            \"refactor the codes in good practice\"\n", "        ],\n", "        \"response\": \"import json\\nimport pathlib\\n\\npath_to_data = pathlib.Path(\\\"~/datasets/edit/commitpackft/data.jsonl\\\").expanduser()\\ndata = []\\n\\nwith path_to_data.open(\\\"r\\\") as f:\\n    for line in f:\\n        datum = json.loads(line[:-1])\\n        data.append(datum)\\n\",\n", "        \"misc\": {\n", "            \"reason_to_lrange\": \"Comprehensive Code Improvement\",\n", "            \"category_of_instruction\": \"Code Refactoring for Best Practices\"\n", "        }\n", "    }\n", "]\n", "All instructions in data: \n", "['use pathlib library to open file']\n", "['add missing import']\n", "['refactor the codes in good practice']\n", "['Use single quote instead of double quotes', 'replace double quotes with single quote', 'replace \" with \\'']\n", "['delete all the type annotations']\n", "['rename real_output_dir by xyz']\n", "['use string format() instead of f-string']\n", "['make the for loop enumerate']\n", "['make all for loops enumerate']\n", "['turn it into Python code']\n", "['correct its format as others']\n"]}], "source": ["DATA_DIR = Path(\"/home/<USER>/repos/augment/experimental/dxy/edits/eval/data\")\n", "\n", "edits_samples, code_samples = load_raw_data(DATA_DIR)\n", "\n", "print(f\"Number of code samples: {len(code_samples)}\")\n", "print(f\"Number of edits samples: {sum(len(e) for e in edits_samples.values())}\")\n", "\n", "example_id = \"0001\"\n", "print(f\"Example code:\\n{pretty_json(code_samples[example_id])}\")\n", "print(f\"Example edit:\\n{pretty_json(edits_samples[example_id])}\")\n", "\n", "print(\"All instructions in data: \")\n", "for edits in edits_samples.values():\n", "    for edit in edits:\n", "        print(edit[\"instruction\"])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total samples: 13\n", "Sample example: {\n", "    \"code\": \"data = []\\n\\nwith open(PATH) as f:\\n\",\n", "    \"prefix\": \"PATH = '~/datasets/edit/commitpackft/data.jsonl'\\n\\n\",\n", "    \"suffix\": \"    for line in f:\\n        datum = json.loads(line[:-1])\\n        data.append(datum)\\n\",\n", "    \"instruction\": \"use pathlib library to open file\",\n", "    \"gt\": \"import pathlib\\n\\ndata = []\\n\\nwith pathlib.Path(PATH).open(\\\"r\\\") as f:\\n\"\n", "}\n"]}], "source": ["data = load_data(DATA_DIR)\n", "print(f\"Total samples: {len(data)}\")\n", "print(f\"Sample example: {pretty_json(data[0])}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
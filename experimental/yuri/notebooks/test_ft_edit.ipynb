{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from megatron.tokenizer.tokenizer import Llama2Tokenizer\n", "from research.eval.edit.data_tools.collected import load_data\n", "from research.models.llama2_models import LLAMA2Model\n", "\n", "from pathlib import Path\n", "from dataclasses import dataclass\n", "from research.models.llama2_models import DeepSeekCoderInstruct\n", "from megatron.tokenizer.tokenizer import DeepSeekCoderInstructTokenizer\n", "from tqdm import tqdm\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def prompt_v1(sample, tokenizer):\n", "    input_prompt = (f\"<INST>{sample.instruction}\" + \n", "                    f\"<SELECTED>{sample.selected_code}\" + \n", "                    f\"<SUFFIX>{sample.suffix}\" +\n", "                    f\"<PREFIX>{sample.prefix}\" +\n", "                    f\"<MODIFIED>\"\n", "                   )\n", "    target_prompt = f\"{sample.modified_code}\"\n", "    \n", "    return {\"input\" : input_prompt, \n", "            \"target\": target_prompt}\n", "\n", "\n", "\n", "@dataclass\n", "class VerySimpleEditData:\n", "    \"\"\"Very simple edit data class.\"\"\"\n", "\n", "    prefix: str\n", "    suffix: str\n", "    selected_code: str\n", "    modified_code: str\n", "    instruction: str\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["t = DeepSeekCoderInstructTokenizer()\n", "t.eos_id"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["32021"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data = load_data(Path(\"/home/<USER>/repos/augment/research/eval/edit/data\"))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["model = DeepSeekCoderInstruct(Path(\"/mnt/efs/augment/user/yuri/tmp/deepseek_75_ckpt_hf2\"))\n", "model.load()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["assert model.tokenizer.bos_id == model.model.generation_config.bos_token_id, \"bos id in tokenizer and model.generation config are different. Please, look into it\"\n", "assert model.tokenizer.eos_id == model.model.generation_config.eos_token_id, \"eos id in tokenizer and model.generation config are different. Please, look into it\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["(32013, 32021)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["model.tokenizer.bos_id, model.tokenizer.eod_id"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["J = 12"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'code': '    type terminal\\n    instruction list the environment variables\\n    command env\\n  },\\n',\n", " 'prefix': '[\\n  {\\n    \"type\": \"https\",\\n    \"instruction\": \"what is my ip\",\\n    \"command\": \"hostname -i\"\\n  },\\n  {\\n    \"type\": \"configuring\",\\n    \"instruction\": \"list all configuration files\",\\n    \"command\": \"ls -l /etc\"\\n  },\\n  {\\n    \"type\": \"compression\",\\n    \"instruction\": \"compress the file.txt\",\\n    \"command\": \"gzip file.txt\"\\n  },\\n  {\\n',\n", " 'suffix': '  {\\n    \"type\": \"versioning\",\\n    \"instruction\": \"get the current branch\",\\n    \"command\": \"git branch\"\\n  }\\n]\\n',\n", " 'instruction': 'correct its format as others',\n", " 'gt': '    \"type\": \"terminal\",\\n    \"instruction\": \"list the environment variables\",\\n    \"command\": \"env\"\\n  },\\n',\n", " 'meta': {'code_index': '0007'}}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["data[J]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'input': '<INST>correct its format as others<SELECTED>    type terminal\\n    instruction list the environment variables\\n    command env\\n  },\\n<SUFFIX>  {\\n    \"type\": \"versioning\",\\n    \"instruction\": \"get the current branch\",\\n    \"command\": \"git branch\"\\n  }\\n]\\n<PREFIX>[\\n  {\\n    \"type\": \"https\",\\n    \"instruction\": \"what is my ip\",\\n    \"command\": \"hostname -i\"\\n  },\\n  {\\n    \"type\": \"configuring\",\\n    \"instruction\": \"list all configuration files\",\\n    \"command\": \"ls -l /etc\"\\n  },\\n  {\\n    \"type\": \"compression\",\\n    \"instruction\": \"compress the file.txt\",\\n    \"command\": \"gzip file.txt\"\\n  },\\n  {\\n<MODIFIED>',\n", " 'target': '    \"type\": \"terminal\",\\n    \"instruction\": \"list the environment variables\",\\n    \"command\": \"env\"\\n  },\\n'}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["sample = VerySimpleEditData(prefix=data[J]['prefix'], suffix=data[J]['suffix'], selected_code=data[J]['code'], modified_code=data[J]['gt'], instruction=data[J]['instruction'])\n", "prompt = prompt_v1(sample, tokenizer)\n", "prompt"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<INST>correct its format as others<SELECTED>    type terminal\n", "    instruction list the environment variables\n", "    command env\n", "  },\n", "<SUFFIX>  {\n", "    \"type\": \"versioning\",\n", "    \"instruction\": \"get the current branch\",\n", "    \"command\": \"git branch\"\n", "  }\n", "]\n", "<PREFIX>[\n", "  {\n", "    \"type\": \"https\",\n", "    \"instruction\": \"what is my ip\",\n", "    \"command\": \"hostname -i\"\n", "  },\n", "  {\n", "    \"type\": \"configuring\",\n", "    \"instruction\": \"list all configuration files\",\n", "    \"command\": \"ls -l /etc\"\n", "  },\n", "  {\n", "    \"type\": \"compression\",\n", "    \"instruction\": \"compress the file.txt\",\n", "    \"command\": \"gzip file.txt\"\n", "  },\n", "  {\n", "<MODIFIED>\n"]}], "source": ["print(prompt['input'])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["[32013,\n", " 27,\n", " 24607,\n", " 29,\n", " 21390,\n", " 891,\n", " 4797,\n", " 372,\n", " 3060,\n", " 27,\n", " 7507,\n", " 2289,\n", " 29,\n", " 315,\n", " 1443,\n", " 6797,\n", " 185,\n", " 315,\n", " 12271,\n", " 1517,\n", " 254,\n", " 4329,\n", " 7125,\n", " 185,\n", " 315,\n", " 2514,\n", " 2928,\n", " 185,\n", " 207,\n", " 4637,\n", " 185,\n", " 27,\n", " 13063,\n", " 3681,\n", " 10322,\n", " 7471,\n", " 507,\n", " 185,\n", " 315,\n", " 440,\n", " 2139,\n", " 2828,\n", " 440,\n", " 1890,\n", " 272,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 2827,\n", " 3475,\n", " 2828,\n", " 440,\n", " 703,\n", " 254,\n", " 1642,\n", " 10601,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 7397,\n", " 2828,\n", " 440,\n", " 12232,\n", " 10601,\n", " 1,\n", " 185,\n", " 207,\n", " 611,\n", " 185,\n", " 60,\n", " 185,\n", " 27,\n", " 11787,\n", " 30383,\n", " 29,\n", " 58,\n", " 185,\n", " 207,\n", " 507,\n", " 185,\n", " 315,\n", " 440,\n", " 2139,\n", " 2828,\n", " 440,\n", " 3959,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 2827,\n", " 3475,\n", " 2828,\n", " 440,\n", " 5003,\n", " 317,\n", " 597,\n", " 15121,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 7397,\n", " 2828,\n", " 440,\n", " 4762,\n", " 1523,\n", " 567,\n", " 72,\n", " 1,\n", " 185,\n", " 207,\n", " 4637,\n", " 185,\n", " 207,\n", " 507,\n", " 185,\n", " 315,\n", " 440,\n", " 2139,\n", " 2828,\n", " 440,\n", " 4130,\n", " 1546,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 2827,\n", " 3475,\n", " 2828,\n", " 440,\n", " 2493,\n", " 519,\n", " 6001,\n", " 3182,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 7397,\n", " 2828,\n", " 440,\n", " 2724,\n", " 567,\n", " 75,\n", " 889,\n", " 5362,\n", " 1,\n", " 185,\n", " 207,\n", " 4637,\n", " 185,\n", " 207,\n", " 507,\n", " 185,\n", " 315,\n", " 440,\n", " 2139,\n", " 2828,\n", " 440,\n", " 685,\n", " 4824,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 2827,\n", " 3475,\n", " 2828,\n", " 440,\n", " 685,\n", " 1476,\n", " 254,\n", " 1753,\n", " 13,\n", " 9313,\n", " 950,\n", " 185,\n", " 315,\n", " 440,\n", " 7397,\n", " 2828,\n", " 440,\n", " 70,\n", " 15526,\n", " 1753,\n", " 13,\n", " 9313,\n", " 1,\n", " 185,\n", " 207,\n", " 4637,\n", " 185,\n", " 207,\n", " 507,\n", " 185,\n", " 27,\n", " 17898,\n", " 25610,\n", " 2289,\n", " 29]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenized = [tokenizer.bos_id] + tokenizer.tokenize(prompt['input'])\n", "tokenized"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from research.models.meta_model import GenerationOptions\n", "\n", "\n", "gen_options = GenerationOptions(\n", "    temperature=0.0, top_k=0, top_p=0, max_generated_tokens=512\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/transformers/generation/configuration_utils.py:362: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.0` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`. This was detected when initializing the generation config instance, which means the corresponding file may hold incorrect parameterization and should be fixed.\n", "  warnings.warn(\n", "/opt/conda/lib/python3.9/site-packages/transformers/generation/configuration_utils.py:362: UserWarning: `do_sample` is set to `False`. However, `temperature` is set to `0.0` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `temperature`.\n", "  warnings.warn(\n", "/opt/conda/lib/python3.9/site-packages/transformers/generation/configuration_utils.py:367: UserWarning: `do_sample` is set to `False`. However, `top_p` is set to `0` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_p`.\n", "  warnings.warn(\n", "/opt/conda/lib/python3.9/site-packages/transformers/generation/configuration_utils.py:377: UserWarning: `do_sample` is set to `False`. However, `top_k` is set to `0` -- this flag is only used in sample-based generation modes. You should set `do_sample=True` or unset `top_k`.\n", "  warnings.warn(\n"]}], "source": ["o = model.raw_generate(tokenized, gen_options)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    \"type\": \"terminal\",\n", "    \"instruction\": \"list the environment variables\",\n", "    \"command\": \"env\"\n", "  },\n", "\n"]}], "source": ["print(o)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    \"type\": \"terminal\",\n", "    \"instruction\": \"list the environment variables\",\n", "    \"command\": \"env\"\n", "  },\n", "\n"]}], "source": ["print(data[J]['gt'])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["o == data[J]['gt']"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 26/26 [01:43<00:00,  3.98s/it]\n"]}], "source": ["# J = 12\n", "c = 0\n", "for J in tqdm(range(len(data))):\n", "    sample = VerySimpleEditData(prefix=data[J]['prefix'], suffix=data[J]['suffix'], selected_code=data[J]['code'], modified_code=data[J]['gt'], instruction=data[J]['instruction'])\n", "    prompt = prompt_v1(sample, tokenizer)\n", "    tokenized = [tokenizer.bos_id] + tokenizer.tokenize(prompt['input'])\n", "\n", "    gen_options = GenerationOptions(\n", "        temperature=0.0, top_k=0, top_p=0, max_generated_tokens=512\n", "    )\n", "\n", "    o = model.raw_generate(tokenized, gen_options)\n", "    c += (o == data[J]['gt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["INIT_DATA = Path(\"/mnt/efs/augment/user/igor/data/droid/droid-repo-47/prompts.json\")\n", "FILTERING_RESULT = Path(\"/mnt/efs/augment/user/yuri/data/droid-repo-47-filtering-run-v1\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["16955"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["with INIT_DATA.open() as f:\n", "    init_data = json.load(f)\n", "\n", "len(init_data)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["13750it [00:01, 12766.43it/s]\n"]}], "source": ["good_samples = []\n", "for name in tqdm((FILTERING_RESULT / \"good\").glob(\"*.json\")):\n", "    with name.open() as f:\n", "        cur_sample = json.load(f)\n", "    good_samples.append(cur_sample)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["3205it [00:04, 663.81it/s]\n"]}], "source": ["bad_samples = []\n", "for name in tqdm((FILTERING_RESULT / \"bad\").glob(\"*.json\")):\n", "    with name.open() as f:\n", "        cur_sample = json.load(f)\n", "    bad_samples.append(cur_sample)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def find_sample(samples, s):\n", "    for _s in samples:\n", "        if (_s[\"prefix\"] == s[\"prefix\"]) and (_s[\"suffix\"] == s[\"suffix\"]) and (_s[\"old_middle\"] == s[\"old_middle\"]) and (_s[\"new_middle\"] == s[\"new_middle\"]) and (_s[\"instruction\"] == s[\"instruction\"]):\n", "            return True\n", "    return False"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 16955/16955 [00:15<00:00, 1090.08it/s]\n"]}], "source": ["updated_data = []\n", "for sample in tqdm(init_data):\n", "    is_good = find_sample(good_samples, sample)\n", "    is_bad = find_sample(bad_samples, sample)\n", "\n", "    assert is_good != is_bad\n", "\n", "    if is_good:\n", "        updated_data.append(sample)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["13750"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(updated_data)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["with open(\"/mnt/efs/augment/user/yuri/data/droid-repo-47-filtering-run-v1/filtered.json\", \"w\") as f:\n", "    json.dump(updated_data, f)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["with open(\"/mnt/efs/augment/user/yuri/data/droid-repo-47-filtering-run-v1/filtered.json\", \"r\") as f:\n", "    updated_data_t = json.load(f)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'instruction': 'Remove the unnecessary else block handling missing states.',\n", " 'instructions': ['Remove the unnecessary else block handling missing states.',\n", "  'Delete the else block for unhandled states.',\n", "  'Eliminate redundant else clause.',\n", "  'Remove else.',\n", "  'Delete else.'],\n", " 'path': 'src/main/java/com/phylogeny/extrabitmanipulation/client/gui/GuiBitMapping.java',\n", " 'prefix': 'package com.phylogeny.extrabitmanipulation.client.gui;\\n\\nimport java.io.IOException;\\nimport java.util.ArrayList;\\nimport java.util.Arrays;\\nimport java.util.Comparator;\\nimport java.util.HashMap;\\nimport java.util.LinkedHashMap;\\nimport java.util.Map;\\nimport java.util.Map.Entry;\\n\\nimport mod.chiselsandbits.api.APIExceptions.InvalidBitItem;\\nimport mod.chiselsandbits.api.APIExceptions.SpaceOccupied;\\nimport mod.chiselsandbits.api.IBitAccess;\\nimport mod.chiselsandbits.api.IBitBrush;\\nimport mod.chiselsandbits.api.IChiselAndBitsAPI;\\nimport mod.chiselsandbits.api.ItemType;\\nimport net.minecraft.block.Block;\\nimport net.minecraft.block.state.IBlockState;\\nimport net.minecraft.client.gui.FontRenderer;\\nimport net.minecraft.client.gui.GuiButton;\\nimport net.minecraft.client.gui.GuiTextField;\\nimport net.minecraft.client.gui.inventory.GuiContainer;\\nimport net.minecraft.client.renderer.GlStateManager;\\nimport net.minecraft.client.renderer.RenderHelper;\\nimport net.minecraft.entity.player.EntityPlayer;\\nimport net.minecraft.init.Blocks;\\nimport net.minecraft.init.Items;\\nimport net.minecraft.item.Item;\\nimport net.minecraft.item.ItemStack;\\nimport net.minecraft.nbt.NBTTagCompound;\\nimport net.minecraft.util.ResourceLocation;\\nimport net.minecraft.util.math.AxisAlignedBB;\\nimport net.minecraft.util.math.Vec3d;\\nimport net.minecraft.util.text.TextFormatting;\\nimport net.minecraftforge.fml.client.config.GuiButtonExt;\\n\\nimport org.apache.commons.lang3.tuple.Pair;\\nimport org.apache.commons.lang3.tuple.Triple;\\nimport org.lwjgl.input.Keyboard;\\nimport org.lwjgl.opengl.GL11;\\n\\nimport com.phylogeny.extrabitmanipulation.ExtraBitManipulation;\\nimport com.phylogeny.extrabitmanipulation.api.ChiselsAndBitsAPIAccess;\\nimport com.phylogeny.extrabitmanipulation.client.ClientHelper;\\nimport com.phylogeny.extrabitmanipulation.client.GuiHelper;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonBase;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonGradient;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonSelect;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonTab;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonTextured;\\nimport com.phylogeny.extrabitmanipulation.client.render.RenderState;\\nimport com.phylogeny.extrabitmanipulation.config.ConfigHandlerExtraBitManipulation;\\nimport com.phylogeny.extrabitmanipulation.helper.BitIOHelper;\\nimport com.phylogeny.extrabitmanipulation.helper.BitInventoryHelper;\\nimport com.phylogeny.extrabitmanipulation.helper.BitToolSettingsHelper;\\nimport com.phylogeny.extrabitmanipulation.helper.ItemStackHelper;\\nimport com.phylogeny.extrabitmanipulation.item.ItemModelingTool;\\nimport com.phylogeny.extrabitmanipulation.item.ItemModelingTool.BitCount;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketAddBitMapping;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketBitMappingsPerTool;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketClearStackBitMappings;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketCursorStack;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketOverwriteStackBitMappings;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketSetDesign;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketSetTabAndStateBlockButton;\\nimport com.phylogeny.extrabitmanipulation.proxy.ProxyCommon;\\nimport com.phylogeny.extrabitmanipulation.reference.ChiselsAndBitsReferences;\\nimport com.phylogeny.extrabitmanipulation.reference.Configs;\\nimport com.phylogeny.extrabitmanipulation.reference.NBTKeys;\\nimport com.phylogeny.extrabitmanipulation.reference.Reference;\\n\\npublic class GuiBitMapping extends GuiContainer\\n{\\n\\tpublic static final ResourceLocation GUI_TEXTURE = new ResourceLocation(Reference.MOD_ID, \"textures/guis/modeling_tool.png\");\\n\\tpublic static final ResourceLocation SETTINGS_MAIN = new ResourceLocation(Reference.MOD_ID, \"textures/guis/settings_main.png\");\\n\\tpublic static final ResourceLocation SETTINGS_BACK = new ResourceLocation(Reference.MOD_ID, \"textures/guis/settings_back.png\");\\n\\tprivate IChiselAndBitsAPI api;\\n\\tprivate GuiListBitMapping bitMappingList;\\n\\tprivate ItemStack previewStack, previewResultStack;\\n\\tprivate IBlockState[][][] stateArray;\\n\\tprivate Map<IBlockState, Integer> stateMap;\\n\\tprivate Map<IBlockState, ArrayList<BitCount>> stateToBitCountArray;\\n\\tprivate Map<IBlockState, IBitBrush> stateToBitMapPermanent, stateToBitMapManual, blockToBitMapPermanent, blockToBitMapManual, blockToBitMapAllBlocks;\\n\\tprivate GuiButtonSelect buttonStates, buttonBlocks;\\n\\tprivate GuiButtonTextured buttonSettings, buttonBitMapPerTool;\\n\\tprivate GuiButtonGradient buttonOverwriteStackMapsWithConfig, buttonOverwriteConfigMapsWithStack, buttonRestoreConfigMaps, buttonClearStackMaps;\\n\\tprivate GuiButtonTab[] tabButtons = new GuiButtonTab[4];\\n\\tprivate static final String[] TAB_HOVER_TEXT = new String[]{\"Current Model\", \"All Saved Mappings\", \"All Minecraft Blocks\", \"Model Result\"};\\n\\tprivate int savedTab, mouseInitialX, mouseInitialY;\\n\\tprivate boolean stateMauallySelected, showSettings, bitMapPerTool, designMode, previewStackBoxClicked;\\n\\tprivate String searchText = \"\";\\n\\tprivate GuiTextField searchField;\\n\\tprivate float previewStackScale;\\n\\tprivate Vec3d previewStackRotation, previewStackTranslation, previewStackTranslationInitial;\\n\\tprivate AxisAlignedBB previewStackBox;\\n\\t\\n\\tpublic GuiBitMapping(EntityPlayer player, boolean designMode)\\n\\t{\\n\\t\\tsuper(ProxyCommon.createBitMappingContainer(player));\\n\\t\\tthis.designMode = designMode;\\n\\t\\tapi = ChiselsAndBitsAPIAccess.apiInstance;\\n\\t\\txSize = 254;\\n\\t\\tySize = 219;\\n\\t\\tpreviewStackScale = 3.8F;\\n\\t\\tpreviewStackRotation = new Vec3d(30, 225, 0);\\n\\t\\tpreviewStackTranslation = Vec3d.ZERO;\\n\\t\\tpreviewStackTranslationInitial = Vec3d.ZERO;\\n\\t\\tif (designMode)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tNBTTagCompound nbt = ItemStackHelper.getNBTOrNew(player.inventory.getCurrentItem());\\n\\t\\tstateMauallySelected = nbt.getBoolean(NBTKeys.BUTTON_STATE_BLOCK_SETTING);\\n\\t\\tsavedTab = nbt.getInteger(NBTKeys.TAB_SETTING);\\n\\t\\tbitMapPerTool = nbt.getBoolean(NBTKeys.BIT_MAPS_PER_TOOL);\\n\\t\\tpreviewStack = previewResultStack = ItemStack.EMPTY;\\n\\t}\\n\\t\\n\\tprivate void constructManualMaps()\\n\\t{\\n\\t\\tstateToBitMapManual = new LinkedHashMap<IBlockState, IBitBrush>();\\n\\t\\tblockToBitMapManual = new LinkedHashMap<IBlockState, IBitBrush>();\\n\\t\\tblockToBitMapAllBlocks = new LinkedHashMap<IBlockState, IBitBrush>();\\n\\t\\tif (stateMap.isEmpty())\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tif (!designMode && tabButtons[2].selected)\\n\\t\\t{\\n\\t\\t\\tfor (Block block : Block.REGISTRY)\\n\\t\\t\\t{\\n\\t\\t\\t\\tResourceLocation regName = block.getRegistryName();\\n\\t\\t\\t\\tif (regName == null)\\n\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tif (regName.getResourceDomain().equals(ChiselsAndBitsReferences.MOD_ID))\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tItem item = Item.getItemFromBlock(block);\\n\\t\\t\\t\\t\\tif (item != Items.AIR)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tItemType itemType = api.getItemType(new ItemStack(item));\\n\\t\\t\\t\\t\\t\\tif (itemType != null && itemType == ItemType.CHISLED_BLOCK)\\n\\t\\t\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (BitIOHelper.isAir(block))\\n\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tIBlockState state = block.getDefaultState();\\n\\t\\t\\t\\taddBitToManualMap(state.getBlock().getDefaultState(), blockToBitMapPermanent, blockToBitMapAllBlocks);\\n\\t\\t\\t}\\n\\t\\t\\tblockToBitMapAllBlocks = getSortedLinkedBitMap(blockToBitMapAllBlocks);\\n\\t\\t}\\n\\t\\tfor (IBlockState state : stateMap.keySet())\\n\\t\\t{\\n\\t\\t\\taddBitToManualMap(state, stateToBitMapPermanent, stateToBitMapManual);\\n\\t\\t\\tif (!designMode)\\n\\t\\t\\t\\taddBitToManualMap(state.getBlock().getDefaultState(), blockToBitMapPermanent, blockToBitMapManual);\\n\\t\\t}\\n\\t\\tstateToBitMapManual = getSortedLinkedBitMap(stateToBitMapManual);\\n\\t\\tblockToBitMapManual = getSortedLinkedBitMap(blockToBitMapManual);\\n\\t}\\n\\t\\n\\tprivate void addBitToManualMap(IBlockState state, Map<IBlockState, IBitBrush> bitMapPermanent, Map<IBlockState, IBitBrush> bitMapManual)\\n\\t{\\n\\t\\tIBitBrush bit = null;\\n\\t\\tif (bitMapPermanent.containsKey(state))\\n\\t\\t{\\n\\t\\t\\tbit = bitMapPermanent.get(state);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\ttry\\n\\t\\t\\t{\\n\\t\\t\\t\\tbit = api.createBrushFromState(state);\\n\\t\\t\\t}\\n\\t\\t\\tcatch (InvalidBitItem e) {}\\n\\t\\t}\\n\\t\\tbitMapManual.put(state, bit);\\n\\t}\\n\\t\\n\\tpublic void addPermanentMapping(IBlockState state, IBitBrush bit)\\n\\t{\\n\\t\\tMap<IBlockState, IBitBrush> bitMapPermanent = getBitMapPermanent();\\n\\t\\tif (bit != null)\\n\\t\\t{\\n\\t\\t\\tbitMapPermanent.put(state, bit);\\n\\t\\t\\tMap<IBlockState, IBitBrush> blockToBitMap = getBitMapManual();\\n\\t\\t\\tif (blockToBitMap.containsKey(state))\\n\\t\\t\\t\\tblockToBitMap.put(state, bit);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tbitMapPermanent.remove(state);\\n\\t\\t\\tconstructManualMaps();\\n\\t\\t}\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\trefreshList();\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tif (bitMapPerTool)\\n\\t\\t{\\n\\t\\t\\tString nbtKey = buttonStates.selected ? NBTKeys.STATE_TO_BIT_MAP_PERMANENT : NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT;\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketAddBitMapping(nbtKey, state, bit, Configs.saveStatesById));\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tMap<IBlockState, IBitBrush> bitMap = buttonStates.selected ? Configs.modelStateToBitMap : Configs.modelBlockToBitMap;\\n\\t\\t\\tif (bit != null)\\n\\t\\t\\t{\\n\\t\\t\\t\\tbitMap.put(state, bit);\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tbitMap.remove(state);\\n\\t\\t\\t}\\n\\t\\t\\tString[] entryStrings = BitIOHelper.getEntryStringsFromModelBitMap(bitMap);\\n\\t\\t\\tif (buttonStates.selected)\\n\\t\\t\\t{\\n\\t\\t\\t\\tConfigs.modelStateToBitMapEntryStrings = entryStrings;\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tConfigs.modelBlockToBitMapEntryStrings = entryStrings;\\n\\t\\t\\t}\\n\\t\\t\\tBitToolSettingsHelper.setBitMapProperty(buttonStates.selected, entryStrings);\\n\\t\\t}\\n\\t\\trefreshList();\\n\\t}\\n\\t\\n\\tprivate void refreshList()\\n\\t{\\n\\t\\tconstructStateToBitCountArray();\\n\\t\\tMap<IBlockState, IBitBrush> bitMapPermanent = getBitMapPermanent();\\n\\t\\tbitMappingList.refreshList(designMode || tabButtons[0].selected || tabButtons[2].selected ? getBitMapManual()\\n\\t\\t\\t\\t: (isResultsTabSelected() ? null : bitMapPermanent), bitMapPermanent, isResultsTabSelected() ? stateToBitCountArray : null,\\n\\t\\t\\t\\t\\t\\tsearchField.getText(), designMode || buttonStates.selected);\\n\\t\\tsetPreviewStack();\\n\\t\\tif (!designMode)\\n\\t\\t\\ttabButtons[0].setIconStack(previewStack);\\n\\t}\\n\\t\\n\\t@SuppressWarnings(\"null\")\\n\\tprivate void constructStateToBitCountArray()\\n\\t{\\n\\t\\tstateToBitCountArray = new LinkedHashMap<IBlockState, ArrayList<BitCount>>();\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tfor (Entry<IBlockState, Integer> entry : stateMap.entrySet())\\n\\t\\t\\t{\\n\\t\\t\\t\\tArrayList<BitCount> bitCountArray = new ArrayList<BitCount>();\\n\\t\\t\\t\\ttry\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tbitCountArray.add(new BitCount(api.createBrushFromState(entry.getKey()), entry.getValue()));\\n\\t\\t\\t\\t\\tstateToBitCountArray.put(entry.getKey(), bitCountArray);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tcatch (InvalidBitItem e) {}\\n\\t\\t\\t}\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tMap<IBitBrush, Integer> bitMap = new HashMap<IBitBrush, Integer>();\\n\\t\\tEntityPlayer player = mc.player;\\n\\t\\tItemModelingTool itemModelingTool = (ItemModelingTool) getHeldStack().getItem();\\n\\t\\tif (itemModelingTool.mapBitsToStates(api, Configs.replacementBitsUnchiselable, Configs.replacementBitsInsufficient,\\n\\t\\t\\t\\tBitInventoryHelper.getInventoryBitCounts(api, player), stateMap, stateToBitCountArray,\\n\\t\\t\\t\\tstateToBitMapPermanent, blockToBitMapPermanent, bitMap, player.capabilities.isCreativeMode).isEmpty())\\n\\t\\t{\\n\\t\\t\\tstateToBitCountArray = getSortedLinkedBitMap(stateToBitCountArray);\\n\\t\\t\\tIBitAccess bitAccess = api.createBitItem(ItemStack.EMPTY);\\n\\t\\t\\tMap<IBlockState, ArrayList<BitCount>> stateToBitCountArrayCopy = new HashMap<IBlockState, ArrayList<BitCount>>();\\n\\t\\t\\tfor (Entry<IBlockState, ArrayList<BitCount>> entry : stateToBitCountArray.entrySet())\\n\\t\\t\\t{\\n\\t\\t\\t\\tArrayList<BitCount> bitCountArray = new ArrayList<BitCount>();\\n\\t\\t\\t\\tfor (BitCount bitCount : entry.getValue())\\n\\t\\t\\t\\t\\tbitCountArray.add(new BitCount(bitCount.getBit(), bitCount.getCount()));\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tstateToBitCountArrayCopy.put(entry.getKey(), bitCountArray);\\n\\t\\t\\t}\\n\\t\\t\\tpreviewResultStack = itemModelingTool.createModel(null, null, getHeldStack(), stateArray, stateToBitCountArrayCopy, bitAccess)\\n\\t\\t\\t\\t\\t? bitAccess.getBitsAsItem(null, ItemType.CHISLED_BLOCK, false) : ItemStack.EMPTY;\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tpreviewResultStack = ItemStack.EMPTY;\\n\\t\\t}\\n\\t}\\n\\t\\n\\tprivate Map<IBlockState, IBitBrush> getBitMapManual()\\n\\t{\\n\\t\\tif (designMode)\\n\\t\\t\\treturn stateToBitMapManual;\\n\\t\\t\\n\\t\\treturn tabButtons[2].selected ? blockToBitMapAllBlocks : (buttonStates.selected ? stateToBitMapManual : blockToBitMapManual);\\n\\t}\\n\\t\\n\\tprivate Map<IBlockState, IBitBrush> getBitMapPermanent()\\n\\t{\\n\\t\\treturn designMode || buttonStates.selected ? stateToBitMapPermanent : blockToBitMapPermanent;\\n\\t}\\n\\t\\n\\t@SuppressWarnings(\"null\")\\n\\tpublic void setPreviewStack()\\n\\t{\\n\\t\\tIBitAccess bitAccess = api.createBitItem(ItemStack.EMPTY);\\n\\t\\tIBitBrush defaultBit = null;\\n\\t\\ttry\\n\\t\\t{\\n\\t\\t\\tdefaultBit = api.createBrushFromState((Configs.replacementBitsUnchiselable.getDefaultReplacementBit().getDefaultState()));\\n\\t\\t}\\n\\t\\tcatch (InvalidBitItem e) {}\\n\\t\\tfor (int i = 0; i < 16; i++)\\n\\t\\t{\\n\\t\\t\\tfor (int j = 0; j < 16; j++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tfor (int k = 0; k < 16; k++)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tIBlockState state = stateArray[i][j][k];\\n\\t\\t\\t\\t\\tif (designMode)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\ttry\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\tbitAccess.setBitAt(i, j, k, stateToBitMapManual.get(state));\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\tcatch (SpaceOccupied e) {}\\n\\t\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tIBlockState state2 = state.getBlock().getDefaultState();\\n\\t\\t\\t\\t\\tboolean stateFound = stateToBitMapManual.containsKey(state);\\n\\t\\t\\t\\t\\tboolean savedStateFound = stateToBitMapPermanent.containsKey(state);\\n\\t\\t\\t\\t\\tboolean savedBlockFound = blockToBitMapPermanent.containsKey(state2);\\n\\t\\t\\t\\t\\tif (stateFound || savedBlockFound)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tIBitBrush bit = savedBlockFound && !savedStateFound ? blockToBitMapPermanent.get(state2) : stateToBitMapManual.get(state);\\n\\t\\t\\t\\t\\t\\ttry\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\tbitAccess.setBitAt(i, j, k, bit != null ? bit : defaultBit);\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\tcatch (SpaceOccupied e) {}\\n\\t\\t\\t\\t\\t}\\n',\n", " 'suffix': '\\t\\t\\t}\\n\\t\\t}\\n\\t\\tpreviewStack = bitAccess.getBitsAsItem(null, ItemType.CHISLED_BLOCK, false);\\n\\t}\\n\\t\\n\\tpublic ItemStack getHeldStack()\\n\\t{\\n\\t\\treturn mc.player.getHeldItemMainhand();\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic int getGuiLeft()\\n\\t{\\n\\t\\treturn guiLeft + 24;\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void initGui()\\n\\t{\\n\\t\\tsuper.initGui();\\n\\t\\tguiLeft -= 12;\\n\\t\\tint l = guiLeft + 128;\\n\\t\\tint t = guiTop + 21;\\n\\t\\tpreviewStackBox = new AxisAlignedBB(l, t, -1, l + 107, t + 100, 1);\\n\\t\\tsearchField = new GuiTextField(6, <PERSON><PERSON><PERSON><PERSON>, gui<PERSON><PERSON><PERSON> + 44, guiTop + 8, 65, 9);\\n\\t\\tsearchField.setEnableBackgroundDrawing(false);\\n\\t\\tsearchField.setTextColor(-1);\\n\\t\\tsearchField.setText(searchText);\\n\\t\\tint slotHeight = 24;\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tstateToBitMapPermanent = new HashMap<IBlockState, IBitBrush>();\\n\\t\\t\\tblockToBitMapPermanent = new HashMap<IBlockState, IBitBrush>();\\n\\t\\t\\tinitDesignMode();\\n\\t\\t\\tString buttonText = \"Save Changes\";\\n\\t\\t\\tint buttonWidth = fontRenderer.getStringWidth(buttonText) + 6;\\n\\t\\t\\tbuttonList.add(new GuiButtonExt(0, guiLeft + xSize - buttonWidth - 5, guiTop + 5, buttonWidth, 14, buttonText));\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tbuttonSettings = new GuiButtonTextured(7, guiLeft + 237, guiTop + 6, 12, 12, \"Bit Mapping Settings\", SETTINGS_BACK, SETTINGS_MAIN, null, null);\\n\\t\\t\\tbuttonSettings.setHoverTextSelected(\"Back To Preview\");\\n\\t\\t\\tbuttonBitMapPerTool = GuiButtonTextured.createCheckBox(8, guiLeft + 143, guiTop + 26, 12, 12, \"Save/access mappings per tool or per client config\");\\n\\t\\t\\tif (showSettings)\\n\\t\\t\\t\\tbuttonSettings.selected = true;\\n\\t\\t\\t\\n\\t\\t\\tint y = guiTop + 44;\\n\\t\\t\\tint offsetY = 19;\\n\\t\\t\\tString hovertext = \"Overwrite mappings saved in 1 with the mappings saved in 2\";\\n\\t\\t\\tString stackText = \"this Modeling Tool\\'s NBT\";\\n\\t\\t\\tString configText = \"the client config file\";\\n\\t\\t\\tbuttonOverwriteStackMapsWithConfig = new GuiButtonGradient(9, guiLeft + 130, y, 102, 14,\\n\\t\\t\\t\\t\\t\"Write Config->Stack\", hovertext.replace(\"1\", stackText).replace(\"2\", configText));\\n\\t\\t\\tbuttonOverwriteConfigMapsWithStack = new GuiButtonGradient(10, guiLeft + 130, y + offsetY, 102, 14,\\n\\t\\t\\t\\t\\t\"Write Stack->Config\", hovertext.replace(\"2\", stackText).replace(\"1\", configText));\\n\\t\\t\\tbuttonRestoreConfigMaps = new GuiButtonGradient(11, guiLeft + 130, y + offsetY * 2, 102, 14,\\n\\t\\t\\t\\t\\t\"Reset Config Maps\", \"Reset \" + configText + \" mapping data to their default values\");\\n\\t\\t\\tbuttonClearStackMaps = new GuiButtonGradient(12, guiLeft + 130, y + offsetY * 3, 102, 14,\\n\\t\\t\\t\\t\\t\"Clear Stack Data\", \"Delete all saved mappping data from \" + stackText);\\n\\t\\t\\tupdateButtons();\\n\\t\\t\\tif (bitMapPerTool)\\n\\t\\t\\t{\\n\\t\\t\\t\\tbuttonBitMapPerTool.selected = true;\\n\\t\\t\\t\\tstateToBitMapPermanent = BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.STATE_TO_BIT_MAP_PERMANENT);\\n\\t\\t\\t\\tblockToBitMapPermanent = BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT);\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tstateToBitMapPermanent = Configs.modelStateToBitMap;\\n\\t\\t\\t\\tblockToBitMapPermanent = Configs.modelBlockToBitMap;\\n\\t\\t\\t}\\n\\t\\t\\tstateToBitMapPermanent = getSortedLinkedBitMap(stateToBitMapPermanent);\\n\\t\\t\\tblockToBitMapPermanent = getSortedLinkedBitMap(blockToBitMapPermanent);\\n\\t\\t\\tbuttonList.add(buttonSettings);\\n\\t\\t\\tbuttonList.add(buttonBitMapPerTool);\\n\\t\\t\\tbuttonList.add(buttonOverwriteStackMapsWithConfig);\\n\\t\\t\\tbuttonList.add(buttonOverwriteConfigMapsWithStack);\\n\\t\\t\\tbuttonList.add(buttonRestoreConfigMaps);\\n\\t\\t\\tbuttonList.add(buttonClearStackMaps);\\n\\t\\t\\tstateMap = new HashMap<IBlockState, Integer>();\\n\\t\\t\\tstateArray = new IBlockState[16][16][16];\\n\\t\\t\\tBitIOHelper.readStatesFromNBT(ItemStackHelper.getNBTOrNew(getHeldStack()), stateMap, stateArray);\\n\\t\\t\\tconstructStateToBitCountArray();\\n\\t\\t\\tfor (int i = 0; i < tabButtons.length; i++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tItemStack iconStack = i == 0 ? previewStack : (i == 1 ? ItemStack.EMPTY : (i == 2 ? new ItemStack(Blocks.GRASS) : ItemStack.EMPTY));\\n\\t\\t\\t\\tfloat u = 0;\\n\\t\\t\\t\\tfloat v = 0;\\n\\t\\t\\t\\tint uWidth = 0;\\n\\t\\t\\t\\tint vHeight = 0;\\n\\t\\t\\t\\tif (i == 1 || i == 3)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tu = i == 1 ? 104 : 67;\\n\\t\\t\\t\\t\\tv = 219;\\n\\t\\t\\t\\t\\tuWidth = 36;\\n\\t\\t\\t\\t\\tvHeight = 36;\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tGuiButtonTab tab = new GuiButtonTab(i, guiLeft, guiTop + 21 + i * 25, 24, 25,\\n\\t\\t\\t\\t\\t\\tTAB_HOVER_TEXT[i], true, iconStack, u, v, uWidth, vHeight, 141, 219, 0, 256, GUI_TEXTURE);\\n\\t\\t\\t\\tif (i == savedTab)\\n\\t\\t\\t\\t\\ttab.selected = true;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\ttabButtons[i] = tab;\\n\\t\\t\\t\\tbuttonList.add(tab);\\n\\t\\t\\t}\\n\\t\\t\\t\\n\\t\\t\\tint x = guiLeft + 42;\\n\\t\\t\\ty = guiTop + 122;\\n\\t\\t\\tint colorSelected = -16726016;\\n\\t\\t\\tint colorDeselected = -8882056;\\n\\t\\t\\tbuttonStates = new GuiButtonSelect(4, x, y, 37, 12, \"States\", \"Map bits to individual block states\", colorSelected, colorDeselected);\\n\\t\\t\\tbuttonBlocks = new GuiButtonSelect(5, x + 37, y, 36, 12, \"Blocks\",\\n\\t\\t\\t\\t\\t\"Map bits to all posible states of a given block\", colorSelected, colorDeselected);\\n\\t\\t\\tbuttonStates.enabled = !tabButtons[2].selected;\\n\\t\\t\\tbuttonBlocks.enabled = !isResultsTabSelected();\\n\\t\\t\\tint selectedTab = getSelectedTab();\\n\\t\\t\\tboolean buttonStatesSlected = selectedTab > 1 ? selectedTab == 3 : stateMauallySelected;\\n\\t\\t\\tbuttonStates.selected = buttonStatesSlected;\\n\\t\\t\\tbuttonBlocks.selected = !buttonStatesSlected;\\n\\t\\t\\tbuttonList.add(buttonStates);\\n\\t\\t\\tbuttonList.add(buttonBlocks);\\n\\t\\t}\\n\\t\\tbitMappingList = new GuiListBitMapping(this, 150, height, guiTop + 21, guiTop + 121, slotHeight, designMode);\\n\\t\\tconstructManualMaps();\\n\\t\\trefreshList();\\n\\t}\\n\\t\\n\\tprivate boolean isResultsTabSelected()\\n\\t{\\n\\t\\treturn designMode || tabButtons[3].selected;\\n\\t}\\n\\t\\n\\tprivate LinkedHashMap getSortedLinkedBitMap(Map bitMap)\\n\\t{\\n\\t\\treturn BitInventoryHelper.getSortedLinkedHashMap(bitMap, new Comparator<Object>() {\\n\\t\\t\\t@Override\\n\\t\\t\\tpublic int compare(Object object1, Object object2)\\n\\t\\t\\t{\\n\\t\\t\\t\\treturn getName(object1).compareTo(getName(object2));\\n\\t\\t\\t}\\n\\t\\t\\t\\n\\t\\t\\t@SuppressWarnings(\"unchecked\")\\n\\t\\t\\tprivate String getName(Object object)\\n\\t\\t\\t{\\n\\t\\t\\t\\tIBlockState state = (IBlockState) ((Map.Entry) object).getKey();\\n\\t\\t\\t\\tBlock block = state.getBlock();\\n\\t\\t\\t\\tResourceLocation regName = block.getRegistryName();\\n\\t\\t\\t\\treturn regName == null ? \"\" : (regName.getResourceDomain() + regName.getResourcePath() + block.getMetaFromState(state));\\n\\t\\t\\t}\\n\\t\\t});\\n\\t}\\n\\t\\n\\tprivate void updateButtons()\\n\\t{\\n\\t\\tif (designMode)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tbuttonBitMapPerTool.visible = buttonOverwriteStackMapsWithConfig.visible = buttonOverwriteConfigMapsWithStack.visible\\n\\t\\t\\t\\t= buttonOverwriteConfigMapsWithStack.visible = buttonRestoreConfigMaps.visible = buttonClearStackMaps.visible = showSettings;\\n\\t\\tif (!showSettings)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tLinkedHashMap stateToBitMapSorted = getSortedLinkedBitMap(Configs.modelStateToBitMap);\\n\\t\\tLinkedHashMap blockToBitMapSorted = getSortedLinkedBitMap(Configs.modelBlockToBitMap);\\n\\t\\tbuttonOverwriteStackMapsWithConfig.enabled = buttonOverwriteConfigMapsWithStack.enabled\\n\\t\\t\\t\\t= !BitIOHelper.areSortedBitMapsIdentical(stateToBitMapSorted,\\n\\t\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.STATE_TO_BIT_MAP_PERMANENT)))\\n\\t\\t\\t\\t|| !BitIOHelper.areSortedBitMapsIdentical(blockToBitMapSorted,\\n\\t\\t\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT)));\\n\\t\\tbuttonRestoreConfigMaps.enabled = !BitIOHelper.areSortedBitMapsIdentical(stateToBitMapSorted,\\n\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.getModelBitMapFromEntryStrings(ConfigHandlerExtraBitManipulation.STATE_TO_BIT_MAP_DEFAULT_VALUES)))\\n\\t\\t\\t|| !BitIOHelper.areSortedBitMapsIdentical(blockToBitMapSorted,\\n\\t\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.getModelBitMapFromEntryStrings(ConfigHandlerExtraBitManipulation.BLOCK_TO_BIT_MAP_DEFAULT_VALUES)));\\n\\t\\tbuttonClearStackMaps.enabled = BitIOHelper.hasBitMapsInNbt(getHeldStack());\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void keyTyped(char typedChar, int keyCode) throws IOException\\n\\t{\\n\\t\\tif (searchField.textboxKeyTyped(typedChar, keyCode))\\n\\t\\t{\\n\\t\\t\\trefreshList();\\n\\t\\t\\tsearchText = searchField.getText();\\n\\t\\t}\\n\\t\\telse if (Keyboard.isKeyDown(Keyboard.KEY_C))\\n\\t\\t{\\n\\t\\t\\tpreviewStackTranslation = Vec3d.ZERO;\\n\\t\\t}\\n\\t\\telse if (showSettings)\\n\\t\\t{\\n\\t\\t\\tif (keyCode == Keyboard.KEY_ESCAPE || mc.gameSettings.keyBindInventory.isActiveAndMatches(keyCode))\\n\\t\\t\\t{\\n\\t\\t\\t\\tshowSettings = buttonSettings.selected = false;\\n\\t\\t\\t\\tupdateButtons();\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tsuper.keyTyped(typedChar, keyCode);\\n\\t\\t}\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void handleMouseInput() throws IOException\\n\\t{\\n\\t\\tsuper.handleMouseInput();\\n\\t\\tif (!previewStackBoxClicked)\\n\\t\\t\\tbitMappingList.handleMouseInput();\\n\\t\\t\\n\\t\\tPair<Vec3d, Float> pair = GuiHelper.scaleObjectWithMouseWheel(this, previewStackBox, previewStackTranslation, previewStackScale, 30.0F, 0.0F);\\n\\t\\tpreviewStackTranslation = pair.getLeft();\\n\\t\\tpreviewStackScale = pair.getRight();\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void mouseClickMove(int mouseX, int mouseY, int clickedMouseButton, long timeSinceLastClick)\\n\\t{\\n\\t\\tsuper.mouseClickMove(mouseX, mouseY, clickedMouseButton, timeSinceLastClick);\\n\\t\\tif (!previewStackBoxClicked)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tfloat deltaX = mouseInitialX - mouseX;\\n\\t\\tfloat deltaY = mouseInitialY - mouseY;\\n\\t\\tif (clickedMouseButton == 0)\\n\\t\\t{\\n\\t\\t\\tmouseInitialX = mouseX;\\n\\t\\t\\tmouseInitialY = mouseY;\\n\\t\\t}\\n\\t\\tTriple<Vec3d, Vec3d, Float> triple = GuiHelper.dragObject(clickedMouseButton, deltaX, deltaY,\\n\\t\\t\\t\\tpreviewStackTranslationInitial, previewStackRotation, previewStackScale, 30.0F, 4.5F, 4.5F, true);\\n\\t\\tpreviewStackTranslation = triple.getLeft();\\n\\t\\tpreviewStackRotation = triple.getMiddle();\\n\\t\\tpreviewStackScale = triple.getRight();\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void updateScreen()\\n\\t{\\n\\t\\tsuper.updateScreen();\\n\\t\\tsearchField.updateCursorCounter();\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void mouseClicked(int mouseX, int mouseY, int mouseButton) throws IOException\\n\\t{\\n\\t\\tsuper.mouseClicked(mouseX, mouseY, mouseButton);\\n\\t\\tsearchField.mouseClicked(mouseX, mouseY, mouseButton);\\n\\t\\tbitMappingList.mouseClicked(mouseX, mouseY, mouseButton);\\n\\t\\tpreviewStackBoxClicked = GuiHelper.isCursorInsideBox(previewStackBox, mouseX, mouseY);\\n\\t\\tmouseInitialX = mouseX;\\n\\t\\tmouseInitialY = mouseY;\\n\\t\\tpreviewStackTranslationInitial = new Vec3d(previewStackTranslation.x, previewStackTranslation.y, 0);\\n\\t\\tif (mc.player.inventory.getItemStack().isEmpty() && mouseButton == 2 && mc.player.capabilities.isCreativeMode && previewStackBoxClicked)\\n\\t\\t{\\n\\t\\t\\tItemStack previewStack = getPreviewStack();\\n\\t\\t\\tif (previewStack.isEmpty())\\n\\t\\t\\t\\treturn;\\n\\t\\t\\t\\n\\t\\t\\tItemStack stack = previewStack.copy();\\n\\t\\t\\tmc.player.inventory.setItemStack(stack);\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketCursorStack(stack));\\n\\t\\t}\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void mouseReleased(int mouseX, int mouseY, int state)\\n\\t{\\n\\t\\tsuper.mouseReleased(mouseX, mouseY, state);\\n\\t\\tbitMappingList.mouseReleased(mouseX, mouseY, state);\\n\\t\\tupdateButtons();\\n\\t\\tmouseInitialX = 0;\\n\\t\\tmouseInitialY = 0;\\n\\t\\tpreviewStackBoxClicked = false;\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void drawScreen(int mouseX, int mouseY, float partialTicks)\\n\\t{\\n\\t\\tdrawDefaultBackground();\\n\\t\\tsuper.drawScreen(mouseX, mouseY, partialTicks);\\n\\t\\tif (previewStackBoxClicked)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\trenderHoveredToolTip(mouseX, mouseY);\\n\\t\\tfor (int i = 0; i < bitMappingList.getSize(); i++)\\n\\t\\t{\\n\\t\\t\\tGuiListBitMappingEntry entry = bitMappingList.getListEntry(i);\\n\\t\\t\\tif (mouseY >= bitMappingList.top && mouseY <= bitMappingList.bottom)\\n\\t\\t\\t{\\n\\t\\t\\t\\tRenderHelper.enableGUIStandardItemLighting();\\n\\t\\t\\t\\tint slotWidth = 19;\\n\\t\\t\\t\\tint k = bitMappingList.left + bitMappingList.width / 2 - bitMappingList.width / 2 + 5;\\n\\t\\t\\t\\tint l = bitMappingList.top + 4 + i * (bitMappingList.slotHeight) - bitMappingList.getAmountScrolled();\\n\\t\\t\\t\\tAxisAlignedBB slot = new AxisAlignedBB(k, l, -1, k + slotWidth, l + bitMappingList.slotHeight - 5, 1);\\n\\t\\t\\t\\tVec3d mousePos = new Vec3d(mouseX, mouseY, 0);\\n\\t\\t\\t\\tif (slot.offset(38, 0, 0).contains(mousePos))\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tArrayList<String> hoverTextList = new ArrayList<String>();\\n\\t\\t\\t\\t\\tfinal String unmappedText = \"The blockstate is currently mapped to nothing, as it cannot be chiseled.\";\\n\\t\\t\\t\\t\\tArrayList<BitCount> bitCountArray = entry.getBitCountArray();\\n\\t\\t\\t\\t\\tfor (int j = 0; j < bitCountArray.size(); j++)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tBitCount bitCount = bitCountArray.get(j);\\n\\t\\t\\t\\t\\t\\tIBitBrush bit = bitCount.getBit();\\n\\t\\t\\t\\t\\t\\tItemStack bitStack = bit != null ? bit.getItemStack(1) : ItemStack.EMPTY;\\n\\t\\t\\t\\t\\t\\tboolean isAir = bit != null && bit.isAir();\\n\\t\\t\\t\\t\\t\\tString text = !bitStack.isEmpty() ? BitToolSettingsHelper.getBitName(bitStack) : (isAir ? \"Empty / Air\" : unmappedText);\\n\\t\\t\\t\\t\\t\\tif (!bitStack.isEmpty() || entry.isAir())\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\tString text2 = TextFormatting.DARK_RED + (j == 0 ? \"Bit:\" : \"\\t\") + \" \" + TextFormatting.RESET;\\n\\t\\t\\t\\t\\t\\t\\tif (bitCountArray.size() > 1)\\n\\t\\t\\t\\t\\t\\t\\t\\ttext2 = (j == 0 ? \"\" : \" \") + text2.replace(\"Bit:\", \"Bits:\");\\n\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\ttext = text2 + text;\\n\\t\\t\\t\\t\\t\\t\\tif (designMode || !entry.isInteractive())\\n\\t\\t\\t\\t\\t\\t\\t\\ttext += \" (\" + bitCount.getCount() + \")\";\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\tif (!designMode && (buttonStates.selected ? stateToBitMapPermanent.containsKey(entry.getState())\\n\\t\\t\\t\\t\\t\\t\\t\\t: blockToBitMapPermanent.containsKey(entry.getState())))\\n\\t\\t\\t\\t\\t\\t\\ttext += TextFormatting.BLUE + \" (manually mapped)\";\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\thoverTextList.add(text);\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tif (entry.getBitCountArray().isEmpty())\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\thoverTextList.add(unmappedText);\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tif (entry.isInteractive())\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\thoverTextList.add(!isShiftKeyDown() ? TextFormatting.AQUA + \"  Hold SHIFT for usage instructions.\"\\n\\t\\t\\t\\t\\t\\t\\t\\t: TextFormatting.AQUA + \"  - Click with bit or block on cursor to add mapping.\");\\n\\t\\t\\t\\t\\t\\tif (isShiftKeyDown())\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\thoverTextList.add(TextFormatting.AQUA + \"  - Shift click with empty cursor to map to air.\");\\n\\t\\t\\t\\t\\t\\t\\thoverTextList.add(TextFormatting.AQUA + \"  - Control click with empty cursor to remove mapping.\");\\n\\t\\t\\t\\t\\t\\t\\thoverTextList.add(TextFormatting.AQUA + \"  - Middle mouse click blocks or bits in creative mode to get stack.\");\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tdrawHoveringText(hoverTextList, mouseX, mouseY, mc.fontRenderer);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\telse if (slot.contains(mousePos))\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tboolean stateMode = designMode || buttonStates.selected;\\n\\t\\t\\t\\t\\tdrawHoveringText(Arrays.<String>asList(new String[] {TextFormatting.DARK_RED + (stateMode ? \"State\" : \"Block\")\\n\\t\\t\\t\\t\\t\\t\\t+ \": \" + TextFormatting.RESET + (stateMode ? entry.getState().toString()\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t: Block.REGISTRY.getNameForObject(entry.getState().getBlock()))}), mouseX, mouseY, mc.fontRenderer);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tRenderHelper.disableStandardItemLighting();\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\t\\n\\t\\tfor (GuiButton button : buttonList)\\n\\t\\t{\\n\\t\\t\\tif (!(button instanceof GuiButtonBase))\\n\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\n\\t\\t\\tGuiButtonBase buttonBase = (GuiButtonBase) button;\\n\\t\\t\\tif (button.isMouseOver() && button.visible)\\n\\t\\t\\t\\tdrawHoveringText(buttonBase.getHoverText(), mouseX, mouseY, mc.fontRenderer);\\n\\t\\t}\\n\\t\\tif (!designMode)\\n\\t\\t{\\n\\t\\t\\tfor (int i = 0; i < tabButtons.length; i++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tif (tabButtons[i].isMouseOver())\\n\\t\\t\\t\\t\\tdrawHoveringText(tabButtons[i].getHoverText(), mouseX, mouseY, mc.fontRenderer);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tGlStateManager.enableLighting();\\n\\t\\tGlStateManager.enableDepth();\\n\\t\\tRenderHelper.enableStandardItemLighting();\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void drawGuiContainerBackgroundLayer(float partialTicks, int mouseX, int mouseY)\\n\\t{\\n\\t\\tGlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);\\n\\t\\tClientHelper.bindTexture(GUI_TEXTURE);\\n\\t\\tint i = (width - xSize) / 2;\\n\\t\\tint j = (height - ySize) / 2;\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tdrawTexturedModalRect(i + 12, j, 24, 0, xSize - 24, ySize);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tdrawTexturedModalRect(i - 12, j, 0, 0, xSize, ySize);\\n\\t\\t}\\n\\t\\tbitMappingList.drawScreen(mouseX, mouseY, partialTicks);\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void drawGuiContainerForegroundLayer(int mouseX, int mouseY)\\n\\t{\\n\\t\\tGlStateManager.pushMatrix();\\n\\t\\tGlStateManager.translate(-guiLeft, -guiTop, 0);\\n\\t\\tGlStateManager.clear(GL11.GL_DEPTH_BUFFER_BIT);\\n\\t\\tGlStateManager.disableLighting();\\n\\t\\tsearchField.drawTextBox();\\n\\t\\tif (!searchField.isFocused() && searchField.getText().isEmpty())\\n\\t\\t\\tfontRenderer.drawString(\"search\", searchField.x, searchField.y, -10197916);\\n\\t\\t\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tfontRenderer.drawString(\"Design\", getGuiLeft() + 103, guiTop + 8, 4210752);\\n\\t\\t\\tfontRenderer.drawString(mc.player.inventory.getDisplayName().getUnformattedText(), guiLeft + 60, guiTop + ySize - 96 + 2, 4210752);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tfor (int i = 0; i < tabButtons.length; i++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tGuiButtonTab tab = tabButtons[i];\\n\\t\\t\\t\\ttab.renderIconStack();\\n\\t\\t\\t\\tif (tab.selected)\\n\\t\\t\\t\\t\\tfontRenderer.drawString(TAB_HOVER_TEXT[i], getGuiLeft() + 103, guiTop + 7, 4210752);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tif (!designMode && showSettings)\\n\\t\\t{\\n\\t\\t\\tGlStateManager.pushMatrix();\\n\\t\\t\\tGlStateManager.translate(0, 0.5, 0);\\n\\t\\t\\tfontRenderer.drawString(\"Map Per Tool\", getGuiLeft() + 133, guiTop + 29, 4210752);\\n\\t\\t\\tGlStateManager.popMatrix();\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tItemStack previewStack = getPreviewStack();\\n\\t\\t\\tif (!previewStack.isEmpty())\\n\\t\\t\\t{\\n\\t\\t\\t\\tGL11.glEnable(GL11.GL_SCISSOR_TEST);\\n\\t\\t\\t\\tGuiHelper.glScissor((int) previewStackBox.minX, (int) previewStackBox.minY,\\n\\t\\t\\t\\t\\t\\t(int) (previewStackBox.maxX - previewStackBox.minX),\\n\\t\\t\\t\\t\\t\\t(int) (previewStackBox.maxY - previewStackBox.minY));\\n\\t\\t\\t\\tRenderHelper.enableGUIStandardItemLighting();\\n\\t\\t\\t\\tGlStateManager.pushMatrix();\\n\\t\\t\\t\\tGlStateManager.translate(0.5 + previewStackTranslation.x, previewStackTranslation.y, 0);\\n\\t\\t\\t\\tRenderState.renderStateModelIntoGUI(null, RenderState.getItemModelWithOverrides(previewStack), previewStack, false,\\n\\t\\t\\t\\t\\t\\tguiLeft + 167, guiTop + 61, (float) previewStackRotation.x,\\n\\t\\t\\t\\t\\t\\t(float) previewStackRotation.y, previewStackScale);\\n\\t\\t\\t\\tGlStateManager.popMatrix();\\n\\t\\t\\t\\tRenderHelper.disableStandardItemLighting();\\n\\t\\t\\t\\tGuiHelper.glScissorDisable();\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tfontRenderer.drawSplitString(\"No Preview   Available\", getGuiLeft() + 131, guiTop + 63, 60, 4210752);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tif (bitMappingList.getSize() == 0)\\n\\t\\t\\tfontRenderer.drawSplitString(\"No \" + (designMode || buttonStates.selected ? \"States\" : \"Blocks\") \\n\\t\\t\\t\\t\\t+ \"      Found\", getGuiLeft() + 31, guiTop + 63, 60, 4210752);\\n\\t\\t\\n\\t\\tRenderHelper.enableGUIStandardItemLighting();\\n\\t\\tItemStack stack = mc.player.inventory.getItemStack();\\n\\t\\tGlStateManager.translate(0.0F, 0.0F, 32.0F);\\n\\t\\tzLevel = 800.0F;\\n\\t\\titemRender.zLevel = 800.0F;\\n\\t\\tFontRenderer font = null;\\n\\t\\tif (!stack.isEmpty())\\n\\t\\t\\tfont = stack.getItem().getFontRenderer(stack);\\n\\t\\t\\n\\t\\tif (font == null)\\n\\t\\t\\tfont = fontRenderer;\\n\\t\\t\\n\\t\\tint x = mouseX - 8;\\n\\t\\tint y = mouseY - 8;\\n\\t\\titemRender.renderItemAndEffectIntoGUI(stack, x, y);\\n\\t\\titemRender.renderItemOverlayIntoGUI(font, stack, x, y, null);\\n\\t\\tzLevel = 0.0F;\\n\\t\\titemRender.zLevel = 0.0F;\\n\\t\\tGlStateManager.popMatrix();\\n\\t}\\n\\t\\n\\tprivate ItemStack getPreviewStack()\\n\\t{\\n\\t\\treturn !designMode && isResultsTabSelected() ? previewResultStack : previewStack;\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void actionPerformed(GuiButton button) throws IOException\\n\\t{\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tif (button.id == 0)\\n\\t\\t\\t{\\n\\t\\t\\t\\tBitInventoryHelper.setHeldDesignStack(mc.player, previewStack);\\n\\t\\t\\t\\tstateToBitMapPermanent.clear();\\n\\t\\t\\t\\tinitDesignMode();\\n\\t\\t\\t\\tconstructManualMaps();\\n\\t\\t\\t\\trefreshList();\\n\\t\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketSetDesign(previewStack));\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tsuper.actionPerformed(button);\\n\\t\\t\\t}\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tint id = button.id;\\n\\t\\tif (id >= 0 && id <= 5)\\n\\t\\t{\\n\\t\\t\\tif (id > 3)\\n\\t\\t\\t{\\n\\t\\t\\t\\tstateMauallySelected = id == 4;\\n\\t\\t\\t\\tselectButtonStatesBlocks(stateMauallySelected);\\n\\t\\t\\t\\tconstructManualMaps();\\n\\t\\t\\t\\trefreshList();\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tif (getSelectedTab() == id)\\n\\t\\t\\t\\t\\treturn;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tboolean allBlocksPrev = tabButtons[2].selected;\\n\\t\\t\\t\\tboolean resultsPrev = isResultsTabSelected();\\n\\t\\t\\t\\tfor (GuiButtonTab tab : tabButtons)\\n\\t\\t\\t\\t\\ttab.selected = tab.id == id;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tsavedTab = id;\\n\\t\\t\\t\\tboolean allBlocks = tabButtons[2].selected;\\n\\t\\t\\t\\tboolean results = isResultsTabSelected();\\n\\t\\t\\t\\tbuttonStates.enabled = !allBlocks;\\n\\t\\t\\t\\tbuttonBlocks.enabled = !results;\\n\\t\\t\\t\\tif (allBlocks)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(false);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\telse if (allBlocksPrev && stateMauallySelected)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(true);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (results)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(true);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\telse if (resultsPrev && !stateMauallySelected)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(false);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (allBlocksPrev != allBlocks)\\n\\t\\t\\t\\t\\tconstructManualMaps();\\n\\t\\t\\t\\t\\n\\t\\t\\t\\trefreshList();\\n\\t\\t\\t}\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketSetTabAndStateBlockButton(getSelectedTab(), stateMauallySelected));\\n\\t\\t}\\n\\t\\telse if (id == 7)\\n\\t\\t{\\n\\t\\t\\tshowSettings ^= true;\\n\\t\\t\\tbuttonSettings.selected = showSettings;\\n\\t\\t\\tupdateButtons();\\n\\t\\t}\\n\\t\\telse if (id == 8)\\n\\t\\t{\\n\\t\\t\\tbitMapPerTool ^= true;\\n\\t\\t\\tbuttonBitMapPerTool.selected = bitMapPerTool;\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketBitMappingsPerTool(bitMapPerTool));\\n\\t\\t}\\n\\t\\telse if (id == 9)\\n\\t\\t{\\n\\t\\t\\tItemStack stack = getHeldStack();\\n\\t\\t\\toverwriteStackBitMappings(stack, Configs.modelBlockToBitMap, NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT);\\n\\t\\t\\toverwriteStackBitMappings(stack, Configs.modelStateToBitMap, NBTKeys.STATE_TO_BIT_MAP_PERMANENT);\\n\\t\\t}\\n\\t\\telse if (id == 10 || id == 11)\\n\\t\\t{\\n\\t\\t\\tConfigs.modelBlockToBitMapEntryStrings = id == 10 ? overwriteConfigMapWithStackMap(Configs.modelBlockToBitMap, NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT)\\n\\t\\t\\t\\t\\t: ConfigHandlerExtraBitManipulation.BLOCK_TO_BIT_MAP_DEFAULT_VALUES;\\n\\t\\t\\tConfigs.modelStateToBitMapEntryStrings = id == 10 ? overwriteConfigMapWithStackMap(Configs.modelStateToBitMap, NBTKeys.STATE_TO_BIT_MAP_PERMANENT)\\n\\t\\t\\t\\t\\t: ConfigHandlerExtraBitManipulation.STATE_TO_BIT_MAP_DEFAULT_VALUES;\\n\\t\\t\\tif (id == 11)\\n\\t\\t\\t{\\n\\t\\t\\t\\tBitToolSettingsHelper.setBitMapProperty(true, Configs.modelStateToBitMapEntryStrings);\\n\\t\\t\\t\\tBitToolSettingsHelper.setBitMapProperty(false, Configs.modelBlockToBitMapEntryStrings);\\n\\t\\t\\t}\\n\\t\\t\\tConfigs.initModelingBitMaps();\\n\\t\\t}\\n\\t\\telse if (id == 12)\\n\\t\\t{\\n\\t\\t\\tBitIOHelper.clearAllBitMapsFromNbt(getHeldStack());\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketClearStackBitMappings());\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tsuper.actionPerformed(button);\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tif (id >= 8)\\n\\t\\t\\tsetWorldAndResolution(mc, width, height);\\n\\t}\\n\\t\\n\\tprivate void initDesignMode()\\n\\t{\\n\\t\\tstateMap = new HashMap<IBlockState, Integer>();\\n\\t\\tstateArray = new IBlockState[16][16][16];\\n\\t\\tIBitAccess pattern = api.createBitItem(getHeldStack());\\n\\t\\tif (pattern == null)\\n\\t\\t\\tpattern = api.createBitItem(ItemStack.EMPTY);\\n\\t\\t\\n\\t\\tfor (int i = 0; i < 16; i++)\\n\\t\\t{\\n\\t\\t\\tfor (int j = 0; j < 16; j++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tfor (int k = 0; k < 16; k++)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t@SuppressWarnings(\"null\")\\n\\t\\t\\t\\t\\tIBitBrush bit = pattern.getBitAt(i, j, k);\\n\\t\\t\\t\\t\\tIBlockState state = bit.getState();\\n\\t\\t\\t\\t\\tstateArray[i][j][k] = state;\\n\\t\\t\\t\\t\\tif (!bit.isAir())\\n\\t\\t\\t\\t\\t\\tstateMap.put(state, 1 + (stateMap.containsKey(state) ? stateMap.get(state) : 0));\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tconstructStateToBitCountArray();\\n\\t}\\n\\t\\n\\tprivate void overwriteStackBitMappings(ItemStack stack, Map<IBlockState, IBitBrush> bitMap, String key)\\n\\t{\\n\\t\\tBitIOHelper.writeStateToBitMapToNBT(stack, key, bitMap, Configs.saveStatesById);\\n\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketOverwriteStackBitMappings(bitMap, key, Configs.saveStatesById));\\n\\t}\\n\\t\\n\\tprivate String[] overwriteConfigMapWithStackMap(Map<IBlockState, IBitBrush> bitMap, String key)\\n\\t{\\n\\t\\tString[] entryStrings = BitIOHelper.getEntryStringsFromModelBitMap(BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), key));\\n\\t\\tBitToolSettingsHelper.setBitMapProperty(bitMap.equals(Configs.modelStateToBitMap), entryStrings);\\n\\t\\treturn entryStrings;\\n\\t}\\n\\t\\n\\tprivate int getSelectedTab()\\n\\t{\\n\\t\\tfor (GuiButtonTab tab : tabButtons)\\n\\t\\t{\\n\\t\\t\\tif (tab.selected)\\n\\t\\t\\t\\treturn tab.id;\\n\\t\\t}\\n\\t\\treturn 0;\\n\\t}\\n\\t\\n\\tprivate void selectButtonStatesBlocks(boolean selectStates)\\n\\t{\\n\\t\\tbuttonStates.selected = selectStates;\\n\\t\\tbuttonBlocks.selected = !selectStates;\\n\\t}\\n\\t\\n}',\n", " 'old_middle': '\\t\\t\\t\\t\\telse\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t// TODO: Handle the case where the state is not found in either map\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n',\n", " 'new_middle': '\\t\\t\\t\\t}\\n',\n", " 'repo_url': '11cf11d8-b5df-41d5-b8c7-4912f1f5429b'}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["updated_data_t[3]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'instruction': 'Remove the unnecessary else block handling missing states.',\n", "  'instructions': ['Remove the unnecessary else block handling missing states.',\n", "   'Delete the else block for unhandled states.',\n", "   'Eliminate redundant else clause.',\n", "   'Remove else.',\n", "   'Delete else.'],\n", "  'path': 'src/main/java/com/phylogeny/extrabitmanipulation/client/gui/GuiBitMapping.java',\n", "  'prefix': 'package com.phylogeny.extrabitmanipulation.client.gui;\\n\\nimport java.io.IOException;\\nimport java.util.ArrayList;\\nimport java.util.Arrays;\\nimport java.util.Comparator;\\nimport java.util.HashMap;\\nimport java.util.LinkedHashMap;\\nimport java.util.Map;\\nimport java.util.Map.Entry;\\n\\nimport mod.chiselsandbits.api.APIExceptions.InvalidBitItem;\\nimport mod.chiselsandbits.api.APIExceptions.SpaceOccupied;\\nimport mod.chiselsandbits.api.IBitAccess;\\nimport mod.chiselsandbits.api.IBitBrush;\\nimport mod.chiselsandbits.api.IChiselAndBitsAPI;\\nimport mod.chiselsandbits.api.ItemType;\\nimport net.minecraft.block.Block;\\nimport net.minecraft.block.state.IBlockState;\\nimport net.minecraft.client.gui.FontRenderer;\\nimport net.minecraft.client.gui.GuiButton;\\nimport net.minecraft.client.gui.GuiTextField;\\nimport net.minecraft.client.gui.inventory.GuiContainer;\\nimport net.minecraft.client.renderer.GlStateManager;\\nimport net.minecraft.client.renderer.RenderHelper;\\nimport net.minecraft.entity.player.EntityPlayer;\\nimport net.minecraft.init.Blocks;\\nimport net.minecraft.init.Items;\\nimport net.minecraft.item.Item;\\nimport net.minecraft.item.ItemStack;\\nimport net.minecraft.nbt.NBTTagCompound;\\nimport net.minecraft.util.ResourceLocation;\\nimport net.minecraft.util.math.AxisAlignedBB;\\nimport net.minecraft.util.math.Vec3d;\\nimport net.minecraft.util.text.TextFormatting;\\nimport net.minecraftforge.fml.client.config.GuiButtonExt;\\n\\nimport org.apache.commons.lang3.tuple.Pair;\\nimport org.apache.commons.lang3.tuple.Triple;\\nimport org.lwjgl.input.Keyboard;\\nimport org.lwjgl.opengl.GL11;\\n\\nimport com.phylogeny.extrabitmanipulation.ExtraBitManipulation;\\nimport com.phylogeny.extrabitmanipulation.api.ChiselsAndBitsAPIAccess;\\nimport com.phylogeny.extrabitmanipulation.client.ClientHelper;\\nimport com.phylogeny.extrabitmanipulation.client.GuiHelper;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonBase;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonGradient;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonSelect;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonTab;\\nimport com.phylogeny.extrabitmanipulation.client.gui.button.GuiButtonTextured;\\nimport com.phylogeny.extrabitmanipulation.client.render.RenderState;\\nimport com.phylogeny.extrabitmanipulation.config.ConfigHandlerExtraBitManipulation;\\nimport com.phylogeny.extrabitmanipulation.helper.BitIOHelper;\\nimport com.phylogeny.extrabitmanipulation.helper.BitInventoryHelper;\\nimport com.phylogeny.extrabitmanipulation.helper.BitToolSettingsHelper;\\nimport com.phylogeny.extrabitmanipulation.helper.ItemStackHelper;\\nimport com.phylogeny.extrabitmanipulation.item.ItemModelingTool;\\nimport com.phylogeny.extrabitmanipulation.item.ItemModelingTool.BitCount;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketAddBitMapping;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketBitMappingsPerTool;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketClearStackBitMappings;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketCursorStack;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketOverwriteStackBitMappings;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketSetDesign;\\nimport com.phylogeny.extrabitmanipulation.packet.PacketSetTabAndStateBlockButton;\\nimport com.phylogeny.extrabitmanipulation.proxy.ProxyCommon;\\nimport com.phylogeny.extrabitmanipulation.reference.ChiselsAndBitsReferences;\\nimport com.phylogeny.extrabitmanipulation.reference.Configs;\\nimport com.phylogeny.extrabitmanipulation.reference.NBTKeys;\\nimport com.phylogeny.extrabitmanipulation.reference.Reference;\\n\\npublic class GuiBitMapping extends GuiContainer\\n{\\n\\tpublic static final ResourceLocation GUI_TEXTURE = new ResourceLocation(Reference.MOD_ID, \"textures/guis/modeling_tool.png\");\\n\\tpublic static final ResourceLocation SETTINGS_MAIN = new ResourceLocation(Reference.MOD_ID, \"textures/guis/settings_main.png\");\\n\\tpublic static final ResourceLocation SETTINGS_BACK = new ResourceLocation(Reference.MOD_ID, \"textures/guis/settings_back.png\");\\n\\tprivate IChiselAndBitsAPI api;\\n\\tprivate GuiListBitMapping bitMappingList;\\n\\tprivate ItemStack previewStack, previewResultStack;\\n\\tprivate IBlockState[][][] stateArray;\\n\\tprivate Map<IBlockState, Integer> stateMap;\\n\\tprivate Map<IBlockState, ArrayList<BitCount>> stateToBitCountArray;\\n\\tprivate Map<IBlockState, IBitBrush> stateToBitMapPermanent, stateToBitMapManual, blockToBitMapPermanent, blockToBitMapManual, blockToBitMapAllBlocks;\\n\\tprivate GuiButtonSelect buttonStates, buttonBlocks;\\n\\tprivate GuiButtonTextured buttonSettings, buttonBitMapPerTool;\\n\\tprivate GuiButtonGradient buttonOverwriteStackMapsWithConfig, buttonOverwriteConfigMapsWithStack, buttonRestoreConfigMaps, buttonClearStackMaps;\\n\\tprivate GuiButtonTab[] tabButtons = new GuiButtonTab[4];\\n\\tprivate static final String[] TAB_HOVER_TEXT = new String[]{\"Current Model\", \"All Saved Mappings\", \"All Minecraft Blocks\", \"Model Result\"};\\n\\tprivate int savedTab, mouseInitialX, mouseInitialY;\\n\\tprivate boolean stateMauallySelected, showSettings, bitMapPerTool, designMode, previewStackBoxClicked;\\n\\tprivate String searchText = \"\";\\n\\tprivate GuiTextField searchField;\\n\\tprivate float previewStackScale;\\n\\tprivate Vec3d previewStackRotation, previewStackTranslation, previewStackTranslationInitial;\\n\\tprivate AxisAlignedBB previewStackBox;\\n\\t\\n\\tpublic GuiBitMapping(EntityPlayer player, boolean designMode)\\n\\t{\\n\\t\\tsuper(ProxyCommon.createBitMappingContainer(player));\\n\\t\\tthis.designMode = designMode;\\n\\t\\tapi = ChiselsAndBitsAPIAccess.apiInstance;\\n\\t\\txSize = 254;\\n\\t\\tySize = 219;\\n\\t\\tpreviewStackScale = 3.8F;\\n\\t\\tpreviewStackRotation = new Vec3d(30, 225, 0);\\n\\t\\tpreviewStackTranslation = Vec3d.ZERO;\\n\\t\\tpreviewStackTranslationInitial = Vec3d.ZERO;\\n\\t\\tif (designMode)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tNBTTagCompound nbt = ItemStackHelper.getNBTOrNew(player.inventory.getCurrentItem());\\n\\t\\tstateMauallySelected = nbt.getBoolean(NBTKeys.BUTTON_STATE_BLOCK_SETTING);\\n\\t\\tsavedTab = nbt.getInteger(NBTKeys.TAB_SETTING);\\n\\t\\tbitMapPerTool = nbt.getBoolean(NBTKeys.BIT_MAPS_PER_TOOL);\\n\\t\\tpreviewStack = previewResultStack = ItemStack.EMPTY;\\n\\t}\\n\\t\\n\\tprivate void constructManualMaps()\\n\\t{\\n\\t\\tstateToBitMapManual = new LinkedHashMap<IBlockState, IBitBrush>();\\n\\t\\tblockToBitMapManual = new LinkedHashMap<IBlockState, IBitBrush>();\\n\\t\\tblockToBitMapAllBlocks = new LinkedHashMap<IBlockState, IBitBrush>();\\n\\t\\tif (stateMap.isEmpty())\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tif (!designMode && tabButtons[2].selected)\\n\\t\\t{\\n\\t\\t\\tfor (Block block : Block.REGISTRY)\\n\\t\\t\\t{\\n\\t\\t\\t\\tResourceLocation regName = block.getRegistryName();\\n\\t\\t\\t\\tif (regName == null)\\n\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tif (regName.getResourceDomain().equals(ChiselsAndBitsReferences.MOD_ID))\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tItem item = Item.getItemFromBlock(block);\\n\\t\\t\\t\\t\\tif (item != Items.AIR)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tItemType itemType = api.getItemType(new ItemStack(item));\\n\\t\\t\\t\\t\\t\\tif (itemType != null && itemType == ItemType.CHISLED_BLOCK)\\n\\t\\t\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (BitIOHelper.isAir(block))\\n\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tIBlockState state = block.getDefaultState();\\n\\t\\t\\t\\taddBitToManualMap(state.getBlock().getDefaultState(), blockToBitMapPermanent, blockToBitMapAllBlocks);\\n\\t\\t\\t}\\n\\t\\t\\tblockToBitMapAllBlocks = getSortedLinkedBitMap(blockToBitMapAllBlocks);\\n\\t\\t}\\n\\t\\tfor (IBlockState state : stateMap.keySet())\\n\\t\\t{\\n\\t\\t\\taddBitToManualMap(state, stateToBitMapPermanent, stateToBitMapManual);\\n\\t\\t\\tif (!designMode)\\n\\t\\t\\t\\taddBitToManualMap(state.getBlock().getDefaultState(), blockToBitMapPermanent, blockToBitMapManual);\\n\\t\\t}\\n\\t\\tstateToBitMapManual = getSortedLinkedBitMap(stateToBitMapManual);\\n\\t\\tblockToBitMapManual = getSortedLinkedBitMap(blockToBitMapManual);\\n\\t}\\n\\t\\n\\tprivate void addBitToManualMap(IBlockState state, Map<IBlockState, IBitBrush> bitMapPermanent, Map<IBlockState, IBitBrush> bitMapManual)\\n\\t{\\n\\t\\tIBitBrush bit = null;\\n\\t\\tif (bitMapPermanent.containsKey(state))\\n\\t\\t{\\n\\t\\t\\tbit = bitMapPermanent.get(state);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\ttry\\n\\t\\t\\t{\\n\\t\\t\\t\\tbit = api.createBrushFromState(state);\\n\\t\\t\\t}\\n\\t\\t\\tcatch (InvalidBitItem e) {}\\n\\t\\t}\\n\\t\\tbitMapManual.put(state, bit);\\n\\t}\\n\\t\\n\\tpublic void addPermanentMapping(IBlockState state, IBitBrush bit)\\n\\t{\\n\\t\\tMap<IBlockState, IBitBrush> bitMapPermanent = getBitMapPermanent();\\n\\t\\tif (bit != null)\\n\\t\\t{\\n\\t\\t\\tbitMapPermanent.put(state, bit);\\n\\t\\t\\tMap<IBlockState, IBitBrush> blockToBitMap = getBitMapManual();\\n\\t\\t\\tif (blockToBitMap.containsKey(state))\\n\\t\\t\\t\\tblockToBitMap.put(state, bit);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tbitMapPermanent.remove(state);\\n\\t\\t\\tconstructManualMaps();\\n\\t\\t}\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\trefreshList();\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tif (bitMapPerTool)\\n\\t\\t{\\n\\t\\t\\tString nbtKey = buttonStates.selected ? NBTKeys.STATE_TO_BIT_MAP_PERMANENT : NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT;\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketAddBitMapping(nbtKey, state, bit, Configs.saveStatesById));\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tMap<IBlockState, IBitBrush> bitMap = buttonStates.selected ? Configs.modelStateToBitMap : Configs.modelBlockToBitMap;\\n\\t\\t\\tif (bit != null)\\n\\t\\t\\t{\\n\\t\\t\\t\\tbitMap.put(state, bit);\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tbitMap.remove(state);\\n\\t\\t\\t}\\n\\t\\t\\tString[] entryStrings = BitIOHelper.getEntryStringsFromModelBitMap(bitMap);\\n\\t\\t\\tif (buttonStates.selected)\\n\\t\\t\\t{\\n\\t\\t\\t\\tConfigs.modelStateToBitMapEntryStrings = entryStrings;\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tConfigs.modelBlockToBitMapEntryStrings = entryStrings;\\n\\t\\t\\t}\\n\\t\\t\\tBitToolSettingsHelper.setBitMapProperty(buttonStates.selected, entryStrings);\\n\\t\\t}\\n\\t\\trefreshList();\\n\\t}\\n\\t\\n\\tprivate void refreshList()\\n\\t{\\n\\t\\tconstructStateToBitCountArray();\\n\\t\\tMap<IBlockState, IBitBrush> bitMapPermanent = getBitMapPermanent();\\n\\t\\tbitMappingList.refreshList(designMode || tabButtons[0].selected || tabButtons[2].selected ? getBitMapManual()\\n\\t\\t\\t\\t: (isResultsTabSelected() ? null : bitMapPermanent), bitMapPermanent, isResultsTabSelected() ? stateToBitCountArray : null,\\n\\t\\t\\t\\t\\t\\tsearchField.getText(), designMode || buttonStates.selected);\\n\\t\\tsetPreviewStack();\\n\\t\\tif (!designMode)\\n\\t\\t\\ttabButtons[0].setIconStack(previewStack);\\n\\t}\\n\\t\\n\\t@SuppressWarnings(\"null\")\\n\\tprivate void constructStateToBitCountArray()\\n\\t{\\n\\t\\tstateToBitCountArray = new LinkedHashMap<IBlockState, ArrayList<BitCount>>();\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tfor (Entry<IBlockState, Integer> entry : stateMap.entrySet())\\n\\t\\t\\t{\\n\\t\\t\\t\\tArrayList<BitCount> bitCountArray = new ArrayList<BitCount>();\\n\\t\\t\\t\\ttry\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tbitCountArray.add(new BitCount(api.createBrushFromState(entry.getKey()), entry.getValue()));\\n\\t\\t\\t\\t\\tstateToBitCountArray.put(entry.getKey(), bitCountArray);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tcatch (InvalidBitItem e) {}\\n\\t\\t\\t}\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tMap<IBitBrush, Integer> bitMap = new HashMap<IBitBrush, Integer>();\\n\\t\\tEntityPlayer player = mc.player;\\n\\t\\tItemModelingTool itemModelingTool = (ItemModelingTool) getHeldStack().getItem();\\n\\t\\tif (itemModelingTool.mapBitsToStates(api, Configs.replacementBitsUnchiselable, Configs.replacementBitsInsufficient,\\n\\t\\t\\t\\tBitInventoryHelper.getInventoryBitCounts(api, player), stateMap, stateToBitCountArray,\\n\\t\\t\\t\\tstateToBitMapPermanent, blockToBitMapPermanent, bitMap, player.capabilities.isCreativeMode).isEmpty())\\n\\t\\t{\\n\\t\\t\\tstateToBitCountArray = getSortedLinkedBitMap(stateToBitCountArray);\\n\\t\\t\\tIBitAccess bitAccess = api.createBitItem(ItemStack.EMPTY);\\n\\t\\t\\tMap<IBlockState, ArrayList<BitCount>> stateToBitCountArrayCopy = new HashMap<IBlockState, ArrayList<BitCount>>();\\n\\t\\t\\tfor (Entry<IBlockState, ArrayList<BitCount>> entry : stateToBitCountArray.entrySet())\\n\\t\\t\\t{\\n\\t\\t\\t\\tArrayList<BitCount> bitCountArray = new ArrayList<BitCount>();\\n\\t\\t\\t\\tfor (BitCount bitCount : entry.getValue())\\n\\t\\t\\t\\t\\tbitCountArray.add(new BitCount(bitCount.getBit(), bitCount.getCount()));\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tstateToBitCountArrayCopy.put(entry.getKey(), bitCountArray);\\n\\t\\t\\t}\\n\\t\\t\\tpreviewResultStack = itemModelingTool.createModel(null, null, getHeldStack(), stateArray, stateToBitCountArrayCopy, bitAccess)\\n\\t\\t\\t\\t\\t? bitAccess.getBitsAsItem(null, ItemType.CHISLED_BLOCK, false) : ItemStack.EMPTY;\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tpreviewResultStack = ItemStack.EMPTY;\\n\\t\\t}\\n\\t}\\n\\t\\n\\tprivate Map<IBlockState, IBitBrush> getBitMapManual()\\n\\t{\\n\\t\\tif (designMode)\\n\\t\\t\\treturn stateToBitMapManual;\\n\\t\\t\\n\\t\\treturn tabButtons[2].selected ? blockToBitMapAllBlocks : (buttonStates.selected ? stateToBitMapManual : blockToBitMapManual);\\n\\t}\\n\\t\\n\\tprivate Map<IBlockState, IBitBrush> getBitMapPermanent()\\n\\t{\\n\\t\\treturn designMode || buttonStates.selected ? stateToBitMapPermanent : blockToBitMapPermanent;\\n\\t}\\n\\t\\n\\t@SuppressWarnings(\"null\")\\n\\tpublic void setPreviewStack()\\n\\t{\\n\\t\\tIBitAccess bitAccess = api.createBitItem(ItemStack.EMPTY);\\n\\t\\tIBitBrush defaultBit = null;\\n\\t\\ttry\\n\\t\\t{\\n\\t\\t\\tdefaultBit = api.createBrushFromState((Configs.replacementBitsUnchiselable.getDefaultReplacementBit().getDefaultState()));\\n\\t\\t}\\n\\t\\tcatch (InvalidBitItem e) {}\\n\\t\\tfor (int i = 0; i < 16; i++)\\n\\t\\t{\\n\\t\\t\\tfor (int j = 0; j < 16; j++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tfor (int k = 0; k < 16; k++)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tIBlockState state = stateArray[i][j][k];\\n\\t\\t\\t\\t\\tif (designMode)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\ttry\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\tbitAccess.setBitAt(i, j, k, stateToBitMapManual.get(state));\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\tcatch (SpaceOccupied e) {}\\n\\t\\t\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tIBlockState state2 = state.getBlock().getDefaultState();\\n\\t\\t\\t\\t\\tboolean stateFound = stateToBitMapManual.containsKey(state);\\n\\t\\t\\t\\t\\tboolean savedStateFound = stateToBitMapPermanent.containsKey(state);\\n\\t\\t\\t\\t\\tboolean savedBlockFound = blockToBitMapPermanent.containsKey(state2);\\n\\t\\t\\t\\t\\tif (stateFound || savedBlockFound)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tIBitBrush bit = savedBlockFound && !savedStateFound ? blockToBitMapPermanent.get(state2) : stateToBitMapManual.get(state);\\n\\t\\t\\t\\t\\t\\ttry\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\tbitAccess.setBitAt(i, j, k, bit != null ? bit : defaultBit);\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\tcatch (SpaceOccupied e) {}\\n\\t\\t\\t\\t\\t}\\n',\n", "  'suffix': '\\t\\t\\t}\\n\\t\\t}\\n\\t\\tpreviewStack = bitAccess.getBitsAsItem(null, ItemType.CHISLED_BLOCK, false);\\n\\t}\\n\\t\\n\\tpublic ItemStack getHeldStack()\\n\\t{\\n\\t\\treturn mc.player.getHeldItemMainhand();\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic int getGuiLeft()\\n\\t{\\n\\t\\treturn guiLeft + 24;\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void initGui()\\n\\t{\\n\\t\\tsuper.initGui();\\n\\t\\tguiLeft -= 12;\\n\\t\\tint l = guiLeft + 128;\\n\\t\\tint t = guiTop + 21;\\n\\t\\tpreviewStackBox = new AxisAlignedBB(l, t, -1, l + 107, t + 100, 1);\\n\\t\\tsearchField = new GuiTextField(6, <PERSON><PERSON><PERSON><PERSON>, gui<PERSON><PERSON><PERSON> + 44, guiTop + 8, 65, 9);\\n\\t\\tsearchField.setEnableBackgroundDrawing(false);\\n\\t\\tsearchField.setTextColor(-1);\\n\\t\\tsearchField.setText(searchText);\\n\\t\\tint slotHeight = 24;\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tstateToBitMapPermanent = new HashMap<IBlockState, IBitBrush>();\\n\\t\\t\\tblockToBitMapPermanent = new HashMap<IBlockState, IBitBrush>();\\n\\t\\t\\tinitDesignMode();\\n\\t\\t\\tString buttonText = \"Save Changes\";\\n\\t\\t\\tint buttonWidth = fontRenderer.getStringWidth(buttonText) + 6;\\n\\t\\t\\tbuttonList.add(new GuiButtonExt(0, guiLeft + xSize - buttonWidth - 5, guiTop + 5, buttonWidth, 14, buttonText));\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tbuttonSettings = new GuiButtonTextured(7, guiLeft + 237, guiTop + 6, 12, 12, \"Bit Mapping Settings\", SETTINGS_BACK, SETTINGS_MAIN, null, null);\\n\\t\\t\\tbuttonSettings.setHoverTextSelected(\"Back To Preview\");\\n\\t\\t\\tbuttonBitMapPerTool = GuiButtonTextured.createCheckBox(8, guiLeft + 143, guiTop + 26, 12, 12, \"Save/access mappings per tool or per client config\");\\n\\t\\t\\tif (showSettings)\\n\\t\\t\\t\\tbuttonSettings.selected = true;\\n\\t\\t\\t\\n\\t\\t\\tint y = guiTop + 44;\\n\\t\\t\\tint offsetY = 19;\\n\\t\\t\\tString hovertext = \"Overwrite mappings saved in 1 with the mappings saved in 2\";\\n\\t\\t\\tString stackText = \"this Modeling Tool\\'s NBT\";\\n\\t\\t\\tString configText = \"the client config file\";\\n\\t\\t\\tbuttonOverwriteStackMapsWithConfig = new GuiButtonGradient(9, guiLeft + 130, y, 102, 14,\\n\\t\\t\\t\\t\\t\"Write Config->Stack\", hovertext.replace(\"1\", stackText).replace(\"2\", configText));\\n\\t\\t\\tbuttonOverwriteConfigMapsWithStack = new GuiButtonGradient(10, guiLeft + 130, y + offsetY, 102, 14,\\n\\t\\t\\t\\t\\t\"Write Stack->Config\", hovertext.replace(\"2\", stackText).replace(\"1\", configText));\\n\\t\\t\\tbuttonRestoreConfigMaps = new GuiButtonGradient(11, guiLeft + 130, y + offsetY * 2, 102, 14,\\n\\t\\t\\t\\t\\t\"Reset Config Maps\", \"Reset \" + configText + \" mapping data to their default values\");\\n\\t\\t\\tbuttonClearStackMaps = new GuiButtonGradient(12, guiLeft + 130, y + offsetY * 3, 102, 14,\\n\\t\\t\\t\\t\\t\"Clear Stack Data\", \"Delete all saved mappping data from \" + stackText);\\n\\t\\t\\tupdateButtons();\\n\\t\\t\\tif (bitMapPerTool)\\n\\t\\t\\t{\\n\\t\\t\\t\\tbuttonBitMapPerTool.selected = true;\\n\\t\\t\\t\\tstateToBitMapPermanent = BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.STATE_TO_BIT_MAP_PERMANENT);\\n\\t\\t\\t\\tblockToBitMapPermanent = BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT);\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tstateToBitMapPermanent = Configs.modelStateToBitMap;\\n\\t\\t\\t\\tblockToBitMapPermanent = Configs.modelBlockToBitMap;\\n\\t\\t\\t}\\n\\t\\t\\tstateToBitMapPermanent = getSortedLinkedBitMap(stateToBitMapPermanent);\\n\\t\\t\\tblockToBitMapPermanent = getSortedLinkedBitMap(blockToBitMapPermanent);\\n\\t\\t\\tbuttonList.add(buttonSettings);\\n\\t\\t\\tbuttonList.add(buttonBitMapPerTool);\\n\\t\\t\\tbuttonList.add(buttonOverwriteStackMapsWithConfig);\\n\\t\\t\\tbuttonList.add(buttonOverwriteConfigMapsWithStack);\\n\\t\\t\\tbuttonList.add(buttonRestoreConfigMaps);\\n\\t\\t\\tbuttonList.add(buttonClearStackMaps);\\n\\t\\t\\tstateMap = new HashMap<IBlockState, Integer>();\\n\\t\\t\\tstateArray = new IBlockState[16][16][16];\\n\\t\\t\\tBitIOHelper.readStatesFromNBT(ItemStackHelper.getNBTOrNew(getHeldStack()), stateMap, stateArray);\\n\\t\\t\\tconstructStateToBitCountArray();\\n\\t\\t\\tfor (int i = 0; i < tabButtons.length; i++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tItemStack iconStack = i == 0 ? previewStack : (i == 1 ? ItemStack.EMPTY : (i == 2 ? new ItemStack(Blocks.GRASS) : ItemStack.EMPTY));\\n\\t\\t\\t\\tfloat u = 0;\\n\\t\\t\\t\\tfloat v = 0;\\n\\t\\t\\t\\tint uWidth = 0;\\n\\t\\t\\t\\tint vHeight = 0;\\n\\t\\t\\t\\tif (i == 1 || i == 3)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tu = i == 1 ? 104 : 67;\\n\\t\\t\\t\\t\\tv = 219;\\n\\t\\t\\t\\t\\tuWidth = 36;\\n\\t\\t\\t\\t\\tvHeight = 36;\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tGuiButtonTab tab = new GuiButtonTab(i, guiLeft, guiTop + 21 + i * 25, 24, 25,\\n\\t\\t\\t\\t\\t\\tTAB_HOVER_TEXT[i], true, iconStack, u, v, uWidth, vHeight, 141, 219, 0, 256, GUI_TEXTURE);\\n\\t\\t\\t\\tif (i == savedTab)\\n\\t\\t\\t\\t\\ttab.selected = true;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\ttabButtons[i] = tab;\\n\\t\\t\\t\\tbuttonList.add(tab);\\n\\t\\t\\t}\\n\\t\\t\\t\\n\\t\\t\\tint x = guiLeft + 42;\\n\\t\\t\\ty = guiTop + 122;\\n\\t\\t\\tint colorSelected = -16726016;\\n\\t\\t\\tint colorDeselected = -8882056;\\n\\t\\t\\tbuttonStates = new GuiButtonSelect(4, x, y, 37, 12, \"States\", \"Map bits to individual block states\", colorSelected, colorDeselected);\\n\\t\\t\\tbuttonBlocks = new GuiButtonSelect(5, x + 37, y, 36, 12, \"Blocks\",\\n\\t\\t\\t\\t\\t\"Map bits to all posible states of a given block\", colorSelected, colorDeselected);\\n\\t\\t\\tbuttonStates.enabled = !tabButtons[2].selected;\\n\\t\\t\\tbuttonBlocks.enabled = !isResultsTabSelected();\\n\\t\\t\\tint selectedTab = getSelectedTab();\\n\\t\\t\\tboolean buttonStatesSlected = selectedTab > 1 ? selectedTab == 3 : stateMauallySelected;\\n\\t\\t\\tbuttonStates.selected = buttonStatesSlected;\\n\\t\\t\\tbuttonBlocks.selected = !buttonStatesSlected;\\n\\t\\t\\tbuttonList.add(buttonStates);\\n\\t\\t\\tbuttonList.add(buttonBlocks);\\n\\t\\t}\\n\\t\\tbitMappingList = new GuiListBitMapping(this, 150, height, guiTop + 21, guiTop + 121, slotHeight, designMode);\\n\\t\\tconstructManualMaps();\\n\\t\\trefreshList();\\n\\t}\\n\\t\\n\\tprivate boolean isResultsTabSelected()\\n\\t{\\n\\t\\treturn designMode || tabButtons[3].selected;\\n\\t}\\n\\t\\n\\tprivate LinkedHashMap getSortedLinkedBitMap(Map bitMap)\\n\\t{\\n\\t\\treturn BitInventoryHelper.getSortedLinkedHashMap(bitMap, new Comparator<Object>() {\\n\\t\\t\\t@Override\\n\\t\\t\\tpublic int compare(Object object1, Object object2)\\n\\t\\t\\t{\\n\\t\\t\\t\\treturn getName(object1).compareTo(getName(object2));\\n\\t\\t\\t}\\n\\t\\t\\t\\n\\t\\t\\t@SuppressWarnings(\"unchecked\")\\n\\t\\t\\tprivate String getName(Object object)\\n\\t\\t\\t{\\n\\t\\t\\t\\tIBlockState state = (IBlockState) ((Map.Entry) object).getKey();\\n\\t\\t\\t\\tBlock block = state.getBlock();\\n\\t\\t\\t\\tResourceLocation regName = block.getRegistryName();\\n\\t\\t\\t\\treturn regName == null ? \"\" : (regName.getResourceDomain() + regName.getResourcePath() + block.getMetaFromState(state));\\n\\t\\t\\t}\\n\\t\\t});\\n\\t}\\n\\t\\n\\tprivate void updateButtons()\\n\\t{\\n\\t\\tif (designMode)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tbuttonBitMapPerTool.visible = buttonOverwriteStackMapsWithConfig.visible = buttonOverwriteConfigMapsWithStack.visible\\n\\t\\t\\t\\t= buttonOverwriteConfigMapsWithStack.visible = buttonRestoreConfigMaps.visible = buttonClearStackMaps.visible = showSettings;\\n\\t\\tif (!showSettings)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tLinkedHashMap stateToBitMapSorted = getSortedLinkedBitMap(Configs.modelStateToBitMap);\\n\\t\\tLinkedHashMap blockToBitMapSorted = getSortedLinkedBitMap(Configs.modelBlockToBitMap);\\n\\t\\tbuttonOverwriteStackMapsWithConfig.enabled = buttonOverwriteConfigMapsWithStack.enabled\\n\\t\\t\\t\\t= !BitIOHelper.areSortedBitMapsIdentical(stateToBitMapSorted,\\n\\t\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.STATE_TO_BIT_MAP_PERMANENT)))\\n\\t\\t\\t\\t|| !BitIOHelper.areSortedBitMapsIdentical(blockToBitMapSorted,\\n\\t\\t\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT)));\\n\\t\\tbuttonRestoreConfigMaps.enabled = !BitIOHelper.areSortedBitMapsIdentical(stateToBitMapSorted,\\n\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.getModelBitMapFromEntryStrings(ConfigHandlerExtraBitManipulation.STATE_TO_BIT_MAP_DEFAULT_VALUES)))\\n\\t\\t\\t|| !BitIOHelper.areSortedBitMapsIdentical(blockToBitMapSorted,\\n\\t\\t\\t\\t\\tgetSortedLinkedBitMap(BitIOHelper.getModelBitMapFromEntryStrings(ConfigHandlerExtraBitManipulation.BLOCK_TO_BIT_MAP_DEFAULT_VALUES)));\\n\\t\\tbuttonClearStackMaps.enabled = BitIOHelper.hasBitMapsInNbt(getHeldStack());\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void keyTyped(char typedChar, int keyCode) throws IOException\\n\\t{\\n\\t\\tif (searchField.textboxKeyTyped(typedChar, keyCode))\\n\\t\\t{\\n\\t\\t\\trefreshList();\\n\\t\\t\\tsearchText = searchField.getText();\\n\\t\\t}\\n\\t\\telse if (Keyboard.isKeyDown(Keyboard.KEY_C))\\n\\t\\t{\\n\\t\\t\\tpreviewStackTranslation = Vec3d.ZERO;\\n\\t\\t}\\n\\t\\telse if (showSettings)\\n\\t\\t{\\n\\t\\t\\tif (keyCode == Keyboard.KEY_ESCAPE || mc.gameSettings.keyBindInventory.isActiveAndMatches(keyCode))\\n\\t\\t\\t{\\n\\t\\t\\t\\tshowSettings = buttonSettings.selected = false;\\n\\t\\t\\t\\tupdateButtons();\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tsuper.keyTyped(typedChar, keyCode);\\n\\t\\t}\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void handleMouseInput() throws IOException\\n\\t{\\n\\t\\tsuper.handleMouseInput();\\n\\t\\tif (!previewStackBoxClicked)\\n\\t\\t\\tbitMappingList.handleMouseInput();\\n\\t\\t\\n\\t\\tPair<Vec3d, Float> pair = GuiHelper.scaleObjectWithMouseWheel(this, previewStackBox, previewStackTranslation, previewStackScale, 30.0F, 0.0F);\\n\\t\\tpreviewStackTranslation = pair.getLeft();\\n\\t\\tpreviewStackScale = pair.getRight();\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void mouseClickMove(int mouseX, int mouseY, int clickedMouseButton, long timeSinceLastClick)\\n\\t{\\n\\t\\tsuper.mouseClickMove(mouseX, mouseY, clickedMouseButton, timeSinceLastClick);\\n\\t\\tif (!previewStackBoxClicked)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\tfloat deltaX = mouseInitialX - mouseX;\\n\\t\\tfloat deltaY = mouseInitialY - mouseY;\\n\\t\\tif (clickedMouseButton == 0)\\n\\t\\t{\\n\\t\\t\\tmouseInitialX = mouseX;\\n\\t\\t\\tmouseInitialY = mouseY;\\n\\t\\t}\\n\\t\\tTriple<Vec3d, Vec3d, Float> triple = GuiHelper.dragObject(clickedMouseButton, deltaX, deltaY,\\n\\t\\t\\t\\tpreviewStackTranslationInitial, previewStackRotation, previewStackScale, 30.0F, 4.5F, 4.5F, true);\\n\\t\\tpreviewStackTranslation = triple.getLeft();\\n\\t\\tpreviewStackRotation = triple.getMiddle();\\n\\t\\tpreviewStackScale = triple.getRight();\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void updateScreen()\\n\\t{\\n\\t\\tsuper.updateScreen();\\n\\t\\tsearchField.updateCursorCounter();\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void mouseClicked(int mouseX, int mouseY, int mouseButton) throws IOException\\n\\t{\\n\\t\\tsuper.mouseClicked(mouseX, mouseY, mouseButton);\\n\\t\\tsearchField.mouseClicked(mouseX, mouseY, mouseButton);\\n\\t\\tbitMappingList.mouseClicked(mouseX, mouseY, mouseButton);\\n\\t\\tpreviewStackBoxClicked = GuiHelper.isCursorInsideBox(previewStackBox, mouseX, mouseY);\\n\\t\\tmouseInitialX = mouseX;\\n\\t\\tmouseInitialY = mouseY;\\n\\t\\tpreviewStackTranslationInitial = new Vec3d(previewStackTranslation.x, previewStackTranslation.y, 0);\\n\\t\\tif (mc.player.inventory.getItemStack().isEmpty() && mouseButton == 2 && mc.player.capabilities.isCreativeMode && previewStackBoxClicked)\\n\\t\\t{\\n\\t\\t\\tItemStack previewStack = getPreviewStack();\\n\\t\\t\\tif (previewStack.isEmpty())\\n\\t\\t\\t\\treturn;\\n\\t\\t\\t\\n\\t\\t\\tItemStack stack = previewStack.copy();\\n\\t\\t\\tmc.player.inventory.setItemStack(stack);\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketCursorStack(stack));\\n\\t\\t}\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void mouseReleased(int mouseX, int mouseY, int state)\\n\\t{\\n\\t\\tsuper.mouseReleased(mouseX, mouseY, state);\\n\\t\\tbitMappingList.mouseReleased(mouseX, mouseY, state);\\n\\t\\tupdateButtons();\\n\\t\\tmouseInitialX = 0;\\n\\t\\tmouseInitialY = 0;\\n\\t\\tpreviewStackBoxClicked = false;\\n\\t}\\n\\t\\n\\t@Override\\n\\tpublic void drawScreen(int mouseX, int mouseY, float partialTicks)\\n\\t{\\n\\t\\tdrawDefaultBackground();\\n\\t\\tsuper.drawScreen(mouseX, mouseY, partialTicks);\\n\\t\\tif (previewStackBoxClicked)\\n\\t\\t\\treturn;\\n\\t\\t\\n\\t\\trenderHoveredToolTip(mouseX, mouseY);\\n\\t\\tfor (int i = 0; i < bitMappingList.getSize(); i++)\\n\\t\\t{\\n\\t\\t\\tGuiListBitMappingEntry entry = bitMappingList.getListEntry(i);\\n\\t\\t\\tif (mouseY >= bitMappingList.top && mouseY <= bitMappingList.bottom)\\n\\t\\t\\t{\\n\\t\\t\\t\\tRenderHelper.enableGUIStandardItemLighting();\\n\\t\\t\\t\\tint slotWidth = 19;\\n\\t\\t\\t\\tint k = bitMappingList.left + bitMappingList.width / 2 - bitMappingList.width / 2 + 5;\\n\\t\\t\\t\\tint l = bitMappingList.top + 4 + i * (bitMappingList.slotHeight) - bitMappingList.getAmountScrolled();\\n\\t\\t\\t\\tAxisAlignedBB slot = new AxisAlignedBB(k, l, -1, k + slotWidth, l + bitMappingList.slotHeight - 5, 1);\\n\\t\\t\\t\\tVec3d mousePos = new Vec3d(mouseX, mouseY, 0);\\n\\t\\t\\t\\tif (slot.offset(38, 0, 0).contains(mousePos))\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tArrayList<String> hoverTextList = new ArrayList<String>();\\n\\t\\t\\t\\t\\tfinal String unmappedText = \"The blockstate is currently mapped to nothing, as it cannot be chiseled.\";\\n\\t\\t\\t\\t\\tArrayList<BitCount> bitCountArray = entry.getBitCountArray();\\n\\t\\t\\t\\t\\tfor (int j = 0; j < bitCountArray.size(); j++)\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\tBitCount bitCount = bitCountArray.get(j);\\n\\t\\t\\t\\t\\t\\tIBitBrush bit = bitCount.getBit();\\n\\t\\t\\t\\t\\t\\tItemStack bitStack = bit != null ? bit.getItemStack(1) : ItemStack.EMPTY;\\n\\t\\t\\t\\t\\t\\tboolean isAir = bit != null && bit.isAir();\\n\\t\\t\\t\\t\\t\\tString text = !bitStack.isEmpty() ? BitToolSettingsHelper.getBitName(bitStack) : (isAir ? \"Empty / Air\" : unmappedText);\\n\\t\\t\\t\\t\\t\\tif (!bitStack.isEmpty() || entry.isAir())\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\tString text2 = TextFormatting.DARK_RED + (j == 0 ? \"Bit:\" : \"\\t\") + \" \" + TextFormatting.RESET;\\n\\t\\t\\t\\t\\t\\t\\tif (bitCountArray.size() > 1)\\n\\t\\t\\t\\t\\t\\t\\t\\ttext2 = (j == 0 ? \"\" : \" \") + text2.replace(\"Bit:\", \"Bits:\");\\n\\t\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t\\ttext = text2 + text;\\n\\t\\t\\t\\t\\t\\t\\tif (designMode || !entry.isInteractive())\\n\\t\\t\\t\\t\\t\\t\\t\\ttext += \" (\" + bitCount.getCount() + \")\";\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t\\tif (!designMode && (buttonStates.selected ? stateToBitMapPermanent.containsKey(entry.getState())\\n\\t\\t\\t\\t\\t\\t\\t\\t: blockToBitMapPermanent.containsKey(entry.getState())))\\n\\t\\t\\t\\t\\t\\t\\ttext += TextFormatting.BLUE + \" (manually mapped)\";\\n\\t\\t\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\thoverTextList.add(text);\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tif (entry.getBitCountArray().isEmpty())\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\thoverTextList.add(unmappedText);\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tif (entry.isInteractive())\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\thoverTextList.add(!isShiftKeyDown() ? TextFormatting.AQUA + \"  Hold SHIFT for usage instructions.\"\\n\\t\\t\\t\\t\\t\\t\\t\\t: TextFormatting.AQUA + \"  - Click with bit or block on cursor to add mapping.\");\\n\\t\\t\\t\\t\\t\\tif (isShiftKeyDown())\\n\\t\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t\\thoverTextList.add(TextFormatting.AQUA + \"  - Shift click with empty cursor to map to air.\");\\n\\t\\t\\t\\t\\t\\t\\thoverTextList.add(TextFormatting.AQUA + \"  - Control click with empty cursor to remove mapping.\");\\n\\t\\t\\t\\t\\t\\t\\thoverTextList.add(TextFormatting.AQUA + \"  - Middle mouse click blocks or bits in creative mode to get stack.\");\\n\\t\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t\\tdrawHoveringText(hoverTextList, mouseX, mouseY, mc.fontRenderer);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\telse if (slot.contains(mousePos))\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tboolean stateMode = designMode || buttonStates.selected;\\n\\t\\t\\t\\t\\tdrawHoveringText(Arrays.<String>asList(new String[] {TextFormatting.DARK_RED + (stateMode ? \"State\" : \"Block\")\\n\\t\\t\\t\\t\\t\\t\\t+ \": \" + TextFormatting.RESET + (stateMode ? entry.getState().toString()\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t: Block.REGISTRY.getNameForObject(entry.getState().getBlock()))}), mouseX, mouseY, mc.fontRenderer);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tRenderHelper.disableStandardItemLighting();\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\t\\n\\t\\tfor (GuiButton button : buttonList)\\n\\t\\t{\\n\\t\\t\\tif (!(button instanceof GuiButtonBase))\\n\\t\\t\\t\\tcontinue;\\n\\t\\t\\t\\n\\t\\t\\tGuiButtonBase buttonBase = (GuiButtonBase) button;\\n\\t\\t\\tif (button.isMouseOver() && button.visible)\\n\\t\\t\\t\\tdrawHoveringText(buttonBase.getHoverText(), mouseX, mouseY, mc.fontRenderer);\\n\\t\\t}\\n\\t\\tif (!designMode)\\n\\t\\t{\\n\\t\\t\\tfor (int i = 0; i < tabButtons.length; i++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tif (tabButtons[i].isMouseOver())\\n\\t\\t\\t\\t\\tdrawHoveringText(tabButtons[i].getHoverText(), mouseX, mouseY, mc.fontRenderer);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tGlStateManager.enableLighting();\\n\\t\\tGlStateManager.enableDepth();\\n\\t\\tRenderHelper.enableStandardItemLighting();\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void drawGuiContainerBackgroundLayer(float partialTicks, int mouseX, int mouseY)\\n\\t{\\n\\t\\tGlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);\\n\\t\\tClientHelper.bindTexture(GUI_TEXTURE);\\n\\t\\tint i = (width - xSize) / 2;\\n\\t\\tint j = (height - ySize) / 2;\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tdrawTexturedModalRect(i + 12, j, 24, 0, xSize - 24, ySize);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tdrawTexturedModalRect(i - 12, j, 0, 0, xSize, ySize);\\n\\t\\t}\\n\\t\\tbitMappingList.drawScreen(mouseX, mouseY, partialTicks);\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void drawGuiContainerForegroundLayer(int mouseX, int mouseY)\\n\\t{\\n\\t\\tGlStateManager.pushMatrix();\\n\\t\\tGlStateManager.translate(-guiLeft, -guiTop, 0);\\n\\t\\tGlStateManager.clear(GL11.GL_DEPTH_BUFFER_BIT);\\n\\t\\tGlStateManager.disableLighting();\\n\\t\\tsearchField.drawTextBox();\\n\\t\\tif (!searchField.isFocused() && searchField.getText().isEmpty())\\n\\t\\t\\tfontRenderer.drawString(\"search\", searchField.x, searchField.y, -10197916);\\n\\t\\t\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tfontRenderer.drawString(\"Design\", getGuiLeft() + 103, guiTop + 8, 4210752);\\n\\t\\t\\tfontRenderer.drawString(mc.player.inventory.getDisplayName().getUnformattedText(), guiLeft + 60, guiTop + ySize - 96 + 2, 4210752);\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tfor (int i = 0; i < tabButtons.length; i++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tGuiButtonTab tab = tabButtons[i];\\n\\t\\t\\t\\ttab.renderIconStack();\\n\\t\\t\\t\\tif (tab.selected)\\n\\t\\t\\t\\t\\tfontRenderer.drawString(TAB_HOVER_TEXT[i], getGuiLeft() + 103, guiTop + 7, 4210752);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tif (!designMode && showSettings)\\n\\t\\t{\\n\\t\\t\\tGlStateManager.pushMatrix();\\n\\t\\t\\tGlStateManager.translate(0, 0.5, 0);\\n\\t\\t\\tfontRenderer.drawString(\"Map Per Tool\", getGuiLeft() + 133, guiTop + 29, 4210752);\\n\\t\\t\\tGlStateManager.popMatrix();\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tItemStack previewStack = getPreviewStack();\\n\\t\\t\\tif (!previewStack.isEmpty())\\n\\t\\t\\t{\\n\\t\\t\\t\\tGL11.glEnable(GL11.GL_SCISSOR_TEST);\\n\\t\\t\\t\\tGuiHelper.glScissor((int) previewStackBox.minX, (int) previewStackBox.minY,\\n\\t\\t\\t\\t\\t\\t(int) (previewStackBox.maxX - previewStackBox.minX),\\n\\t\\t\\t\\t\\t\\t(int) (previewStackBox.maxY - previewStackBox.minY));\\n\\t\\t\\t\\tRenderHelper.enableGUIStandardItemLighting();\\n\\t\\t\\t\\tGlStateManager.pushMatrix();\\n\\t\\t\\t\\tGlStateManager.translate(0.5 + previewStackTranslation.x, previewStackTranslation.y, 0);\\n\\t\\t\\t\\tRenderState.renderStateModelIntoGUI(null, RenderState.getItemModelWithOverrides(previewStack), previewStack, false,\\n\\t\\t\\t\\t\\t\\tguiLeft + 167, guiTop + 61, (float) previewStackRotation.x,\\n\\t\\t\\t\\t\\t\\t(float) previewStackRotation.y, previewStackScale);\\n\\t\\t\\t\\tGlStateManager.popMatrix();\\n\\t\\t\\t\\tRenderHelper.disableStandardItemLighting();\\n\\t\\t\\t\\tGuiHelper.glScissorDisable();\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tfontRenderer.drawSplitString(\"No Preview   Available\", getGuiLeft() + 131, guiTop + 63, 60, 4210752);\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tif (bitMappingList.getSize() == 0)\\n\\t\\t\\tfontRenderer.drawSplitString(\"No \" + (designMode || buttonStates.selected ? \"States\" : \"Blocks\") \\n\\t\\t\\t\\t\\t+ \"      Found\", getGuiLeft() + 31, guiTop + 63, 60, 4210752);\\n\\t\\t\\n\\t\\tRenderHelper.enableGUIStandardItemLighting();\\n\\t\\tItemStack stack = mc.player.inventory.getItemStack();\\n\\t\\tGlStateManager.translate(0.0F, 0.0F, 32.0F);\\n\\t\\tzLevel = 800.0F;\\n\\t\\titemRender.zLevel = 800.0F;\\n\\t\\tFontRenderer font = null;\\n\\t\\tif (!stack.isEmpty())\\n\\t\\t\\tfont = stack.getItem().getFontRenderer(stack);\\n\\t\\t\\n\\t\\tif (font == null)\\n\\t\\t\\tfont = fontRenderer;\\n\\t\\t\\n\\t\\tint x = mouseX - 8;\\n\\t\\tint y = mouseY - 8;\\n\\t\\titemRender.renderItemAndEffectIntoGUI(stack, x, y);\\n\\t\\titemRender.renderItemOverlayIntoGUI(font, stack, x, y, null);\\n\\t\\tzLevel = 0.0F;\\n\\t\\titemRender.zLevel = 0.0F;\\n\\t\\tGlStateManager.popMatrix();\\n\\t}\\n\\t\\n\\tprivate ItemStack getPreviewStack()\\n\\t{\\n\\t\\treturn !designMode && isResultsTabSelected() ? previewResultStack : previewStack;\\n\\t}\\n\\t\\n\\t@Override\\n\\tprotected void actionPerformed(GuiButton button) throws IOException\\n\\t{\\n\\t\\tif (designMode)\\n\\t\\t{\\n\\t\\t\\tif (button.id == 0)\\n\\t\\t\\t{\\n\\t\\t\\t\\tBitInventoryHelper.setHeldDesignStack(mc.player, previewStack);\\n\\t\\t\\t\\tstateToBitMapPermanent.clear();\\n\\t\\t\\t\\tinitDesignMode();\\n\\t\\t\\t\\tconstructManualMaps();\\n\\t\\t\\t\\trefreshList();\\n\\t\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketSetDesign(previewStack));\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tsuper.actionPerformed(button);\\n\\t\\t\\t}\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tint id = button.id;\\n\\t\\tif (id >= 0 && id <= 5)\\n\\t\\t{\\n\\t\\t\\tif (id > 3)\\n\\t\\t\\t{\\n\\t\\t\\t\\tstateMauallySelected = id == 4;\\n\\t\\t\\t\\tselectButtonStatesBlocks(stateMauallySelected);\\n\\t\\t\\t\\tconstructManualMaps();\\n\\t\\t\\t\\trefreshList();\\n\\t\\t\\t}\\n\\t\\t\\telse\\n\\t\\t\\t{\\n\\t\\t\\t\\tif (getSelectedTab() == id)\\n\\t\\t\\t\\t\\treturn;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tboolean allBlocksPrev = tabButtons[2].selected;\\n\\t\\t\\t\\tboolean resultsPrev = isResultsTabSelected();\\n\\t\\t\\t\\tfor (GuiButtonTab tab : tabButtons)\\n\\t\\t\\t\\t\\ttab.selected = tab.id == id;\\n\\t\\t\\t\\t\\n\\t\\t\\t\\tsavedTab = id;\\n\\t\\t\\t\\tboolean allBlocks = tabButtons[2].selected;\\n\\t\\t\\t\\tboolean results = isResultsTabSelected();\\n\\t\\t\\t\\tbuttonStates.enabled = !allBlocks;\\n\\t\\t\\t\\tbuttonBlocks.enabled = !results;\\n\\t\\t\\t\\tif (allBlocks)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(false);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\telse if (allBlocksPrev && stateMauallySelected)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(true);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (results)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(true);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\telse if (resultsPrev && !stateMauallySelected)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\tselectButtonStatesBlocks(false);\\n\\t\\t\\t\\t}\\n\\t\\t\\t\\tif (allBlocksPrev != allBlocks)\\n\\t\\t\\t\\t\\tconstructManualMaps();\\n\\t\\t\\t\\t\\n\\t\\t\\t\\trefreshList();\\n\\t\\t\\t}\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketSetTabAndStateBlockButton(getSelectedTab(), stateMauallySelected));\\n\\t\\t}\\n\\t\\telse if (id == 7)\\n\\t\\t{\\n\\t\\t\\tshowSettings ^= true;\\n\\t\\t\\tbuttonSettings.selected = showSettings;\\n\\t\\t\\tupdateButtons();\\n\\t\\t}\\n\\t\\telse if (id == 8)\\n\\t\\t{\\n\\t\\t\\tbitMapPerTool ^= true;\\n\\t\\t\\tbuttonBitMapPerTool.selected = bitMapPerTool;\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketBitMappingsPerTool(bitMapPerTool));\\n\\t\\t}\\n\\t\\telse if (id == 9)\\n\\t\\t{\\n\\t\\t\\tItemStack stack = getHeldStack();\\n\\t\\t\\toverwriteStackBitMappings(stack, Configs.modelBlockToBitMap, NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT);\\n\\t\\t\\toverwriteStackBitMappings(stack, Configs.modelStateToBitMap, NBTKeys.STATE_TO_BIT_MAP_PERMANENT);\\n\\t\\t}\\n\\t\\telse if (id == 10 || id == 11)\\n\\t\\t{\\n\\t\\t\\tConfigs.modelBlockToBitMapEntryStrings = id == 10 ? overwriteConfigMapWithStackMap(Configs.modelBlockToBitMap, NBTKeys.BLOCK_TO_BIT_MAP_PERMANENT)\\n\\t\\t\\t\\t\\t: ConfigHandlerExtraBitManipulation.BLOCK_TO_BIT_MAP_DEFAULT_VALUES;\\n\\t\\t\\tConfigs.modelStateToBitMapEntryStrings = id == 10 ? overwriteConfigMapWithStackMap(Configs.modelStateToBitMap, NBTKeys.STATE_TO_BIT_MAP_PERMANENT)\\n\\t\\t\\t\\t\\t: ConfigHandlerExtraBitManipulation.STATE_TO_BIT_MAP_DEFAULT_VALUES;\\n\\t\\t\\tif (id == 11)\\n\\t\\t\\t{\\n\\t\\t\\t\\tBitToolSettingsHelper.setBitMapProperty(true, Configs.modelStateToBitMapEntryStrings);\\n\\t\\t\\t\\tBitToolSettingsHelper.setBitMapProperty(false, Configs.modelBlockToBitMapEntryStrings);\\n\\t\\t\\t}\\n\\t\\t\\tConfigs.initModelingBitMaps();\\n\\t\\t}\\n\\t\\telse if (id == 12)\\n\\t\\t{\\n\\t\\t\\tBitIOHelper.clearAllBitMapsFromNbt(getHeldStack());\\n\\t\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketClearStackBitMappings());\\n\\t\\t}\\n\\t\\telse\\n\\t\\t{\\n\\t\\t\\tsuper.actionPerformed(button);\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tif (id >= 8)\\n\\t\\t\\tsetWorldAndResolution(mc, width, height);\\n\\t}\\n\\t\\n\\tprivate void initDesignMode()\\n\\t{\\n\\t\\tstateMap = new HashMap<IBlockState, Integer>();\\n\\t\\tstateArray = new IBlockState[16][16][16];\\n\\t\\tIBitAccess pattern = api.createBitItem(getHeldStack());\\n\\t\\tif (pattern == null)\\n\\t\\t\\tpattern = api.createBitItem(ItemStack.EMPTY);\\n\\t\\t\\n\\t\\tfor (int i = 0; i < 16; i++)\\n\\t\\t{\\n\\t\\t\\tfor (int j = 0; j < 16; j++)\\n\\t\\t\\t{\\n\\t\\t\\t\\tfor (int k = 0; k < 16; k++)\\n\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t@SuppressWarnings(\"null\")\\n\\t\\t\\t\\t\\tIBitBrush bit = pattern.getBitAt(i, j, k);\\n\\t\\t\\t\\t\\tIBlockState state = bit.getState();\\n\\t\\t\\t\\t\\tstateArray[i][j][k] = state;\\n\\t\\t\\t\\t\\tif (!bit.isAir())\\n\\t\\t\\t\\t\\t\\tstateMap.put(state, 1 + (stateMap.containsKey(state) ? stateMap.get(state) : 0));\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n\\t\\t}\\n\\t\\tconstructStateToBitCountArray();\\n\\t}\\n\\t\\n\\tprivate void overwriteStackBitMappings(ItemStack stack, Map<IBlockState, IBitBrush> bitMap, String key)\\n\\t{\\n\\t\\tBitIOHelper.writeStateToBitMapToNBT(stack, key, bitMap, Configs.saveStatesById);\\n\\t\\tExtraBitManipulation.packetNetwork.sendToServer(new PacketOverwriteStackBitMappings(bitMap, key, Configs.saveStatesById));\\n\\t}\\n\\t\\n\\tprivate String[] overwriteConfigMapWithStackMap(Map<IBlockState, IBitBrush> bitMap, String key)\\n\\t{\\n\\t\\tString[] entryStrings = BitIOHelper.getEntryStringsFromModelBitMap(BitIOHelper.readStateToBitMapFromNBT(api, getHeldStack(), key));\\n\\t\\tBitToolSettingsHelper.setBitMapProperty(bitMap.equals(Configs.modelStateToBitMap), entryStrings);\\n\\t\\treturn entryStrings;\\n\\t}\\n\\t\\n\\tprivate int getSelectedTab()\\n\\t{\\n\\t\\tfor (GuiButtonTab tab : tabButtons)\\n\\t\\t{\\n\\t\\t\\tif (tab.selected)\\n\\t\\t\\t\\treturn tab.id;\\n\\t\\t}\\n\\t\\treturn 0;\\n\\t}\\n\\t\\n\\tprivate void selectButtonStatesBlocks(boolean selectStates)\\n\\t{\\n\\t\\tbuttonStates.selected = selectStates;\\n\\t\\tbuttonBlocks.selected = !selectStates;\\n\\t}\\n\\t\\n}',\n", "  'old_middle': '\\t\\t\\t\\t\\telse\\n\\t\\t\\t\\t\\t{\\n\\t\\t\\t\\t\\t\\t// TODO: Handle the case where the state is not found in either map\\n\\t\\t\\t\\t\\t}\\n\\t\\t\\t\\t}\\n',\n", "  'new_middle': '\\t\\t\\t\\t}\\n',\n", "  'repo_url': '11cf11d8-b5df-41d5-b8c7-4912f1f5429b'}]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["[*filter(lambda s: s[\"instruction\"] == \"Remove the unnecessary else block handling missing states.\", updated_data_t)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
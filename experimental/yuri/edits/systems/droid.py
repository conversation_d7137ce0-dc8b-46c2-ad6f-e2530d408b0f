"""Prompt formatter for Droid, a code instruct model."""

import re

from megatron.tokenizer.tokenizer import (
    AbstractTokenizer,
    DeepSeekCoderBaseTokenizer,
    DeepSeekCoderInstructTokenizer,
    DeepSeekTokenizer,
)

from research.core import utils_for_str
from research.core.model_input import ModelInput
from research.core.prompt_formatters import (
    AbstractPromptFormatter,
    filter_retrieved_chunks_that_overlap_with_prompt,
    register_prompt_formatter,
)


@register_prompt_formatter("droid")
class DroidFormatter(AbstractPromptFormatter):
    """Prompt formatter for Droid, a code instruct model."""

    max_prefix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    max_suffix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    max_prefix_lines: int = 100
    max_suffix_lines: int = 100

    version: int = 7
    """Version of the prompt formatter to use."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        """A function to create the default tokenizer."""
        if self.version in [1, 2]:
            return DeepSeekCoderBaseTokenizer()
        if self.version in [5, 6, 7, 8]:
            return DeepSeekCoderInstructTokenizer()
        raise NotImplementedError(f"Version {self.version} is not implemented.")

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        if model_input.retrieved_chunks:
            raise NotImplementedError(f"Do not support retrieved chunks for {self}.")
        if self.preamble:
            raise NotImplementedError(f"Do not support preamble for {self}.")
        tokenizer = self.tokenizer
        assert isinstance(tokenizer, DeepSeekTokenizer)

        if self.version == 3 or self.version == 4:
            raise NotImplementedError("Not implemented")
        if self.version in [5, 6, 7, 8]:
            return self._prepare_prompt_text(model_input, tokenizer, self.version)

        output_separator = (
            tokenizer.fim_middle_id if self.version >= 2 else tokenizer.pause_id
        )
        prompt = (
            [tokenizer.bos_id]
            + tokenizer.tokenize(model_input.extra["instruction"])
            + [tokenizer.fim_prefix_id]
            + tokenizer.tokenize(model_input.prefix)[-self.max_prefix_tokens :]
            + [tokenizer.fim_suffix_id]
            + tokenizer.tokenize(model_input.suffix)[: self.max_suffix_tokens]
            + [tokenizer.fim_middle_id]
            + tokenizer.tokenize(model_input.extra["selected_code"])
            + [output_separator]
        )

        return prompt, {}

    def _prepare_prompt_text(
        self,
        model_input: ModelInput,
        tokenizer: DeepSeekTokenizer,
        version: int,
    ) -> tuple[str, dict]:
        prefix = model_input.prefix
        suffix = model_input.suffix

        if version == 8:
            # clip the prefix and suffix to the max tokens
            prefix = prefix[-self.max_prefix_tokens :]
            suffix = suffix[: self.max_suffix_tokens]

            # clip the prefix and suffix to the max lines
            prefix = utils_for_str.get_last_n_lines(prefix, self.max_prefix_lines)
            suffix = utils_for_str.get_first_n_lines(suffix, self.max_suffix_lines)

        prompt = (
            [tokenizer.bos_id]
            + tokenizer.tokenize("Instruction: ")
            + tokenizer.tokenize(model_input.extra["instruction"])
            + tokenizer.tokenize("\n")
            + (
                (
                    tokenizer.tokenize("Prefix:\n```\n")
                    + tokenizer.tokenize(prefix)
                    + tokenizer.tokenize("\n```\n\n")
                    + tokenizer.tokenize("Suffix:\n```\n")
                    + tokenizer.tokenize(suffix)
                    + tokenizer.tokenize("\n```\n\n")
                )
                if version != 5
                else []
            )
            + tokenizer.tokenize("Selected Code:\n```\n")
            + tokenizer.tokenize(model_input.extra["selected_code"])
            + tokenizer.tokenize("\n```\n\n")
            + tokenizer.tokenize("Selected Code Modified:\n```\n")
        )
        return prompt, {}

    def get_prompt_footer(self) -> list[int]:
        if self.version >= 7:
            return self.tokenizer.tokenize("\n```\n") + [self.tokenizer.eod_id]
        return [self.tokenizer.eod_id]

    def post_process_response(self, response: str) -> str:
        if self.version >= 7:
            match_v0 = re.search(r"^(.*)\n```\n*$", response, re.DOTALL)
            return match_v0.group(1) if match_v0 else response
        return response


@register_prompt_formatter("droid-repo")
class DroidRepoFormatter(AbstractPromptFormatter):
    """Prompt formatter for Droid, a code instruct model."""

    max_prefix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    max_suffix_tokens: int = 1024
    """Maximum number of tokens in the prefix."""

    max_instruction_tokens: int = 512
    """Maximum number of tokens in the instruction."""

    max_output_tokens: int = 512
    """Maximum number of tokens in the output."""

    max_tokens: int = 8192
    """Maximum number of tokens in the prompt."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        """A function to create the default tokenizer."""
        return DeepSeekCoderInstructTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        if self.preamble:
            raise NotImplementedError(f"Do not support preamble for {self}.")
        tokenizer = self.tokenizer
        assert isinstance(tokenizer, DeepSeekTokenizer)

        clipped_prefix = tokenizer.tokenize(model_input.prefix)[
            -self.max_prefix_tokens :
        ]
        clipped_suffix = tokenizer.tokenize(model_input.suffix)[
            : self.max_suffix_tokens
        ]
        clipped_instruction = tokenizer.tokenize(model_input.extra["instruction"])[
            : self.max_instruction_tokens
        ]

        header_tokens = (
            [tokenizer.bos_id]
            + tokenizer.tokenize("Instruction: ")
            + clipped_instruction
            + tokenizer.tokenize("\n")
        )
        prefix_tokens = (
            tokenizer.tokenize("Prefix:\n```\n")
            + clipped_prefix
            + tokenizer.tokenize("\n```\n\n")
        )
        suffix_tokens = (
            tokenizer.tokenize("Suffix:\n```\n")
            + clipped_suffix
            + tokenizer.tokenize("\n```\n\n")
        )
        code_tokens = (
            tokenizer.tokenize(f"Code Before ({model_input.path}):\n```\n")
            + tokenizer.tokenize(model_input.extra["selected_code"])
            + tokenizer.tokenize("\n```\n\n")
            + tokenizer.tokenize("Code After:\n```\n")
        )

        budget = (
            len(header_tokens)
            + len(prefix_tokens)
            + len(suffix_tokens)
            + len(code_tokens)
            + self.max_output_tokens
        )
        assert budget <= self.max_tokens
        retrieval_budget = self.max_tokens - budget

        retrieved_chunk_tokens = []
        retrieved_chunk_token_count = 0

        num_prefix_chars_post_truncation = len(
            self.tokenizer.detokenize(clipped_prefix)
        )
        num_suffix_chars_post_truncation = len(
            self.tokenizer.detokenize(clipped_suffix)
        )

        retrieved_chunks = filter_retrieved_chunks_that_overlap_with_prompt(
            model_input=model_input,
            num_prefix_chars=num_prefix_chars_post_truncation,
            num_suffix_chars=num_suffix_chars_post_truncation,
        )
        for chunk in retrieved_chunks:
            chunk_tokens = (
                tokenizer.tokenize(f"See ({chunk.parent_doc.path}):\n")
                + tokenizer.tokenize("```\n")
                + tokenizer.tokenize(chunk.text)
                + tokenizer.tokenize("\n```\n")
            )

            if len(chunk_tokens) + retrieved_chunk_token_count > retrieval_budget:
                break

            retrieved_chunk_tokens.append(chunk_tokens)
            retrieved_chunk_token_count += len(chunk_tokens)

        retrieved_chunk_tokens.reverse()
        retrieval_tokens = sum(retrieved_chunk_tokens, [])
        assert len(retrieval_tokens) <= retrieval_budget

        assert (
            len(header_tokens)
            + len(retrieval_tokens)
            + len(prefix_tokens)
            + len(suffix_tokens)
            + len(code_tokens)
            + self.max_output_tokens
        ) <= self.max_tokens
        return (
            header_tokens
            + retrieval_tokens
            + prefix_tokens
            + suffix_tokens
            + code_tokens
        ), {}

    def get_prompt_footer(self) -> list[int]:
        return self.tokenizer.tokenize("\n```\n") + [self.tokenizer.eod_id]

    def post_process_response(self, response: str) -> str:
        match = re.search(r"^(.*)\n```\n*$", response, re.DOTALL)
        return match.group(1) if match else response

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import time\n", "\n", "from typing import Iterator, Optional\n", "from google.cloud import bigquery\n", "from pathlib import Path\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from experimental.yuri.preferences.utils import markdown, row_html, wrap_html\n", "from tqdm import tqdm\n", "from difflib import unified_diff\n", "from base.third_party_clients.anthropic_tool_response_handler import XmlEventHandler\n", "from base.third_party_clients.anthropic_tool_response_handler import (\n", "    GitConflictMark<PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "# MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    \"augment-387916\",\n", "    \"us-east5\",\n", "    MODEL_NAME,\n", "    0,\n", "    1024 * 5,\n", ")\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", ")\n", "PROMPT_FORMATTER = get_structured_chat_prompt_formatter_by_name(\n", "    \"structured-binks-claude\", TOKEN_APPORTIONMENT\n", ")\n", "DOGFOOD_URL = (\n", "    \"https://support.dogfood.t.us-central1.prod.augmentcode.com/t/dogfood/request\"\n", ")\n", "STAGING_DOGFOOD_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Misc stuff\n", "\n", "\n", "def get_chat_samples(\n", "    request_ids,\n", "    project_id: str = \"system-services-prod\",\n", "    dataset_name: str = \"staging_request_insight_full_export_dataset\",\n", ") -> dict:\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {project_id}.{dataset_name}.request_metadata AS metadata\n", "JOIN {project_id}.{dataset_name}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {project_id}.{dataset_name}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=project_id)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "            \"request\": row.request[\"request\"],\n", "            \"response\": row.response[\"response\"],\n", "            \"metadata\": row.metadata,\n", "            \"datetime\": row.time.isoformat(),\n", "            \"row\": row,\n", "        }\n", "\n", "    return chat_rows_dic\n", "\n", "\n", "def extract_between_patterns(main_string: str, p1: str, p2: str, index: int) -> str:\n", "    pattern = rf\"{re.escape(p1)}(.*?){re.escape(p2)}\"\n", "    matches = re.finditer(pattern, main_string, re.DOTALL)\n", "\n", "    for i, match in enumerate(matches):\n", "        if i == index:\n", "            return match.group(1)\n", "\n", "    raise ValueError(f\"Substring between '{p1}' and '{p2}' not found for index {index}\")\n", "\n", "\n", "def get_num_patterns(main_string: str, p1: str, p2: str) -> int:\n", "    pattern = rf\"{re.escape(p1)}(.*?){re.escape(p2)}\"\n", "    matches = re.finditer(pattern, main_string, re.DOTALL)\n", "    return len(list(matches))\n", "\n", "\n", "def format_sample(raw_sample, num_chunks_to_keep: int, drop_history: bool = False):\n", "    history = [\n", "        Exchange(e[\"request_message\"], e[\"response_text\"], e[\"request_id\"])\n", "        for e in raw_sample[\"row\"].request[\"request\"].get(\"chat_history\", [])\n", "    ]\n", "    if drop_history:\n", "        history = []\n", "    chunks = [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            char_start=chunk.get(\"char_offset\", 0),\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk.get(\"blob_name\", \"\"),\n", "            origin=chunk[\"origin\"],\n", "        )\n", "        for chunk in raw_sample[\"row\"].request.get(\"retrieved_chunks\", [])\n", "    ]\n", "    chunks = chunks[:num_chunks_to_keep]\n", "\n", "    request_entry = raw_sample[\"row\"].request[\"request\"]\n", "    if request_entry.get(\"suffix\", \"\").startswith(\"\\n\") and request_entry.get(\n", "        \"selected_code\", \"\"\n", "    ):\n", "        # IDK why it happens, but it does\n", "        request_entry[\"selected_code\"] += \"\\n\"\n", "        request_entry[\"suffix\"] = request_entry[\"suffix\"][1:]\n", "        print(\"Suffix starts with new line\")\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=request_entry[\"path\"],\n", "        prefix=request_entry.get(\"prefix\", \"\"),\n", "        selected_code=request_entry.get(\"selected_code\", \"\"),\n", "        suffix=request_entry.get(\"suffix\", \"\"),\n", "        message=request_entry[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(\n", "            request_entry.get(\"prefix\", \"\")\n", "            + request_entry.get(\"selected_code\", \"\")\n", "            + request_entry.get(\"suffix\", \"\")\n", "        ),\n", "        retrieved_chunks=chunks,\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    return prompt_input, prompt_output\n", "\n", "\n", "def run_anthropic(cur_message, history, system_prompt, prefill=None, use_tools=False):\n", "    formatted_messages = []\n", "    for message in history:\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": message[0],\n", "            }\n", "        )\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": message[1],\n", "            }\n", "        )\n", "\n", "    # Add cur message\n", "    formatted_messages.append(\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": cur_message,\n", "        }\n", "    )\n", "\n", "    if prefill is not None:\n", "        formatted_messages.append({\"role\": \"assistant\", \"content\": prefill})\n", "\n", "    if use_tools:\n", "        tools = [\n", "            {\n", "                \"name\": \"replace_text\",\n", "                \"description\": \"Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.\",\n", "                \"input_schema\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"old_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The original text.\",\n", "                        },\n", "                        \"start_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text starts, inclusive.\",\n", "                        },\n", "                        \"end_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text ends, inclusive.\",\n", "                        },\n", "                        \"replacement_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The new text.\",\n", "                        },\n", "                    },\n", "                    \"required\": [\n", "                        \"old_text\",\n", "                        \"start_line_number\",\n", "                        \"end_line_number\",\n", "                        \"replacement_text\",\n", "                    ],\n", "                },\n", "            },\n", "        ]\n", "    else:\n", "        tools = []\n", "\n", "    response = ANTHROPIC_CLIENT.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=8192,\n", "        messages=formatted_messages,\n", "        system=system_prompt,\n", "        temperature=0,\n", "        tools=tools,\n", "    )\n", "\n", "    return response\n", "\n", "\n", "def run_anthropic_w_retries(\n", "    cur_message,\n", "    history,\n", "    system_prompt,\n", "    max_retries=3,\n", "    prefill=None,\n", "    use_tools=False,\n", "):\n", "    result = None\n", "    for i in range(max_retries):\n", "        try:\n", "            result = run_anthropic(\n", "                cur_message, history, system_prompt, prefill, use_tools\n", "            )\n", "            break\n", "        except Exception as e:\n", "            print(f\"Failed to run anthropic with tools, retrying {i+1}/{max_retries}\")\n", "            print(e)\n", "            time.sleep(1)\n", "    return result"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def put_numbers(\n", "    text: str,\n", "    start_line_offset: int,\n", "    prefix: str,\n", "    mode: str,\n", "):\n", "    assert mode in [\"xml\", \"regular\"]\n", "    for i, line in enumerate(text.splitlines(keepends=True)):\n", "        if mode == \"xml\":\n", "            assert not prefix, \"Unused here\"\n", "            cur_number = i + 1 + start_line_offset\n", "            yield f\"<line number={cur_number}>{line.rstrip()}</line number={cur_number}>\\n\"\n", "        elif mode == \"regular\":\n", "            yield f\"{prefix}{i+1+start_line_offset:04d}: {line}\"\n", "\n", "\n", "def put_numbers_xml(text: str, start_line_offset: int, prefix: str):\n", "    for i, line in enumerate(text.splitlines(keepends=True)):\n", "        yield f\"{prefix}{i+1+start_line_offset:04d}: {line}\"\n", "\n", "\n", "def format_code(\n", "    prefix: str,\n", "    selected_code: str,\n", "    suffix: str,\n", "    num_lines_in_prefix_suffix: int,\n", "    number_mode: str,\n", "    is_highlighted: bool = False,\n", "):\n", "    def _n_lines(s: str):\n", "        return len(s.splitlines(keepends=True))\n", "\n", "    prefix = \"\".join(prefix.splitlines(keepends=True)[-num_lines_in_prefix_suffix:])\n", "    suffix = \"\".join(suffix.splitlines(keepends=True)[:num_lines_in_prefix_suffix])\n", "\n", "    full_file_in_prompt = prefix + selected_code + suffix\n", "\n", "    prefix_n_regular = \"\".join(put_numbers(prefix, 0, prefix=\"\", mode=number_mode))\n", "    selected_code_n_regular = \"\".join(\n", "        put_numbers(selected_code, _n_lines(prefix), prefix=\"\", mode=number_mode)\n", "    )\n", "    suffix_n_regular = \"\".join(\n", "        put_numbers(\n", "            suffix,\n", "            _n_lines(prefix) + _n_lines(selected_code),\n", "            prefix=\"\",\n", "            mode=number_mode,\n", "        )\n", "    )\n", "\n", "    if is_highlighted:\n", "        if not prefix_n_regular.endswith(\"\\n\"):\n", "            prefix_n_regular += \"\\n\"\n", "        if not selected_code_n_regular.endswith(\"\\n\"):\n", "            selected_code_n_regular += \"\\n\"\n", "        numbered_code = f\"\"\"{prefix_n_regular}<highlighted_code>\\n{selected_code_n_regular}</highlighted_code>\\n{suffix_n_regular}\"\"\"\n", "        no_numbered_code = f\"\"\"{prefix}<highlighted_code>\\n{selected_code}</highlighted_code>\\n{suffix}\"\"\"\n", "    else:\n", "        numbered_code = (\n", "            f\"\"\"{prefix_n_regular}{selected_code_n_regular}{suffix_n_regular}\"\"\"\n", "        )\n", "        no_numbered_code = f\"\"\"{prefix}{selected_code}{suffix}\"\"\"\n", "\n", "    return numbered_code, {\n", "        \"full_file_in_prompt\": full_file_in_prompt,\n", "        \"no_numbered_code\": no_numbered_code,\n", "        \"prefix\": prefix,\n", "        \"selected_code\": selected_code,\n", "        \"suffix\": suffix,\n", "    }\n", "\n", "\n", "def adjust_line_number(full_file, line_number, line):\n", "    full_file_lines = [\n", "        cur_line.rstrip() for cur_line in full_file.splitlines(keepends=True)\n", "    ]\n", "    line = line.rstrip()\n", "\n", "    if full_file_lines[line_number - 1] == line:\n", "        print(f\"MATCH {line}\")\n", "        return line_number\n", "\n", "    for i in range(20):\n", "        if line_number > i and full_file_lines[line_number - i - 1] == line:\n", "            print(f\"Adjusting line number from {line_number} to {line_number - i}\")\n", "            return line_number - i\n", "        if (\n", "            line_number + i <= len(full_file_lines)\n", "            and full_file_lines[line_number + i - 1] == line\n", "        ):\n", "            return line_number + i\n", "\n", "    print(f\"Can't find line {line} in the file\")\n", "    return line_number"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data\n", "\n", "MULTIFILE_SMARTPASTE_SAMPLES = [\n", "    (\n", "        \"0b320251-c408-4755-b474-179174f0f084\",\n", "        \"/home/<USER>/repos/augment/research/fastbackward/model.py\",\n", "        0,\n", "    ),\n", "    (\n", "        \"0b320251-c408-4755-b474-179174f0f084\",\n", "        \"/home/<USER>/repos/augment/research/fastbackward/train_rlhf.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"af449c14-1458-481d-a8a4-7d2ba396489b\",\n", "        \"/home/<USER>/repos/augment/base/prompt_format_chat/__init__.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/base/prompt_format_chat/conftest.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/base/prompt_format_chat/lib/chat_history_builder_test.py\",\n", "        2,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/clients/vscode/src/__tests__/chat/chat-model.test.ts\",\n", "        3,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/clients/vscode/src/augment-api.ts\",\n", "        4,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/clients/intellij/src/test/kotlin/com/augmentcode/intellij/chat/ChatRequestTest.kt\",\n", "        5,\n", "    ),\n", "]\n", "\n", "STAGING_SHARD0_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    \"0696c744-9fb8-4b50-b655-fad32ddb38d5\",\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\",\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "    # <PERSON>'s new samples\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\",\n", "]\n", "\n", "OLD_DOGFOOD_SAMPLES = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "]\n", "\n", "PLAYTEST_SMARTPASTE_SAMPLES = [\n", "    \"7d544538-4031-490d-889c-2f0806448f87\",\n", "    \"a616ea44-8240-410e-855a-fdd5ec5d43f1\",  # original bad edit: \"52e92730-3411-48d7-b7e7-d98a98df4801\",\n", "    \"4028990b-d670-43f1-9e66-23d8b62c73b1\",  # original bad edit: \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/93c6c898-14dd-4512-8f7d-231a492ab3a9\"\n", "    \"d56ba211-6d34-499e-ae72-aa784e7d653e\",  # original bad edit: \"6616d2c7-8a3d-4e63-890d-1b8add19fb12\"\n", "    \"7d19a934-3695-40d4-ae2c-f2bbf71225d5\",\n", "    (\n", "        \"01159bf5-a5b7-4e6a-9712-f140813f885b\",\n", "        \"/home/<USER>/repos/augment/clients/common/webviews/src/common/components/markdown/Markdown.svelte\",\n", "        3,\n", "    ),\n", "    \"7cc4fa2b-bf3d-471a-a59b-67baa5769043\",  # https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/0dfbb331-586f-42b4-95aa-9a4d54837351\n", "]\n", "\n", "PURE_ADDITIONS = [\n", "    \"e267315b-5cb7-4c8d-ac22-d543dd492865\",\n", "    \"c4af6731-a50e-4a77-8589-01b46894c5fc\",\n", "    (\n", "        \"5e940f35-3e6f-4622-ace6-3f3b8e7ed952\",  # \"1c0dd859-1182-4d2a-a4cd-faf9cd5adf35\",\n", "        None,\n", "        2,\n", "    ),\n", "    \"bf88ad7b-0e30-482f-9a70-0e24679b3d0e\",  # \"b97c8092-0aa1-46ab-870a-df0e10f0236c\",\n", "]\n", "\n", "\n", "# Smartpaste\n", "SAMPLES = (\n", "    OLD_DOGFOOD_SAMPLES\n", "    + STAGING_SHARD0_SAMPLES\n", "    + PLAYTEST_SMARTPASTE_SAMPLES\n", "    + PURE_ADDITIONS\n", ")\n", "# SAMPLES = PURE_ADDITIONS\n", "\n", "# Code edits\n", "# SAMPLES = OLD_DOGFOOD_SAMPLES + STAGING_SHARD0_SAMPLES\n", "\n", "\n", "download_samples = get_chat_samples(\n", "    [s if isinstance(s, str) else s[0] for s in SAMPLES]\n", ")\n", "download_samples_dev_deploy = get_chat_samples(\n", "    [s if isinstance(s, str) else s[0] for s in SAMPLES],\n", "    project_id=\"system-services-dev\",  # type: ignore\n", "    dataset_name=\"dev_yuri_request_insight_full_export_dataset\",  # type: ignore\n", ")\n", "\n", "print(f\"Downloaded from staging: {len(download_samples)}\")\n", "print(f\"Downloaded from dev_deploy: {len(download_samples_dev_deploy)}\")\n", "print(f\"Total downloaded: {len(download_samples) + len(download_samples_dev_deploy) }\")\n", "\n", "download_samples = download_samples | download_samples_dev_deploy"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Smartpaste\n", "class SmartpasteApplyCodeblockGitConflict:\n", "    @classmethod\n", "    def get_response(\n", "        cls,\n", "        prompt_input: ChatPromptInput,\n", "        prompt_output,\n", "        main_response: str,\n", "        code_block: str,\n", "        file_path: Optional[str],\n", "    ) -> dict:\n", "        if file_path is None:\n", "            full_file = (\n", "                prompt_input.prefix + prompt_input.selected_code + prompt_input.suffix\n", "            )\n", "            file_path = prompt_input.path\n", "        else:\n", "            with open(file_path, \"r\") as f:\n", "                full_file = f.read()\n", "\n", "        xml_code, misc_formatting_data = format_code(\n", "            prefix=\"\",\n", "            selected_code=full_file,\n", "            suffix=\"\",\n", "            num_lines_in_prefix_suffix=int(1e9),\n", "            number_mode=\"xml\",\n", "        )\n", "        regular_code, _ = format_code(\n", "            prefix=\"\",\n", "            selected_code=full_file,\n", "            suffix=\"\",\n", "            num_lines_in_prefix_suffix=int(1e9),\n", "            number_mode=\"regular\",\n", "        )\n", "        prompt = f\"\"\"Great! Now please, apply changes that you demonstrated in this codeblock:\n", "<changes_to_apply>\n", "```\n", "{code_block}\n", "```\n", "</changes_to_apply>\n", "\n", "to this file : \n", "\n", "<file path=\"{file_path}\">\n", "{xml_code.rstrip()}\n", "</file>\n", "\n", "To modify the file, please use git conflict markers format. I.e. return single or multiple git conflict markers in the following format:\n", "<<<<<<< original BRIEFLY\n", "...\n", "======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]\n", "...\n", ">>>>>>> updated FULL\n", "\n", "When you need to insert new class(es) or function(s) after line X use:\n", "<<<<<<< original BRIEFLY\n", "...\n", "======= [after line(s) <line number=X>...content of line X...</line number=X>, <line number=X+1>...content of line X+1...</line number=X+1>,...,<line number=X+k>...content of line X+k...</line number=X+k>]\n", "...\n", ">>>>>>> updated FULL\n", "\n", "- `updated` section should contain modified `original` code.\n", "- Return only git conflict markers and nothing else.\n", "- Split large changes into multiple smaller ones, and merge changes if they are close.\n", "- Make sure to always use disjoint line ranges for every conflict marker.\n", "- Line numbers in markers should ALWAYS be as in file, WITHOUT accounting for markers happened above.\n", "- For middle line use EXACTLY this format: `======= [original line range: <line number=X>...content of line X...</line number=X> - <line number=Y>...content of line Y...</line number=Y>]`. Exactly 2 lines (X and Y) have to be specified here. Or `======= [after line(s) <line number=X>...content of line X...</line number=X>, <line number=X+1>...content of line X+1...</line number=X+1>,...,<line number=X+k>...content of line X+k...</line number=X+k>]`.\n", "- Always preserve indentation style of the original file. If indentation style of file and codeblock differ, always prefer file indentation style.\n", "- To save time, please write code in `original` schematically and VERY briefly, use inline comments to indicate skipped and omitted parts.\n", "- But ALWAYS write `updated` as the FULL range X - Y from original file. Expand any shortenings that are in `original`, NEVER copy them.\n", "- `updated` section should rewrite PRECISELY lines from X to Y.\n", "\"\"\"\n", "\n", "        # Note: we are using prompt_output.message here, because it contains (optional) selected code.\n", "        # We should make sure that in production we don't lose selected code from previous messages (if it exists).\n", "        claude_response = run_anthropic_w_retries(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ]\n", "            + [(prompt_output.message, main_response)],\n", "            \"You are highly advanced and intelligent AI code assistant.\",\n", "            prefill=\"Here's the modification using git conflict markers:\",\n", "        )\n", "\n", "        compiled_text = f\"{claude_response.content[0].text}\\n{'=' * 30}\\n\"\n", "        print(compiled_text)\n", "        # return {\"compiled_text\": compiled_text}\n", "\n", "        # Convert raw events to list of edits\n", "        collected_edits = []\n", "        cur_edit = {\n", "            \"old_text\": \"\",\n", "            \"start_line\": None,\n", "            \"end_line\": None,\n", "            \"replacement_text\": \"\",\n", "            \"sequence_id\": 0,\n", "        }\n", "        handler = GitConflictMarkersHandler()\n", "        for i in range(0, len(claude_response.content[0].text), 3):\n", "            for event in handler.parse(claude_response.content[0].text[i : i + 3]):\n", "                assert event.replace_text_response is not None\n", "                if event.replace_text_response.sequence_id > cur_edit[\"sequence_id\"]:\n", "                    collected_edits.append(cur_edit)\n", "                    cur_edit = {\n", "                        \"old_text\": \"\",\n", "                        \"start_line\": None,\n", "                        \"end_line\": None,\n", "                        \"replacement_text\": \"\",\n", "                        \"sequence_id\": event.replace_text_response.sequence_id,\n", "                    }\n", "                if event.replace_text_response.old_text:\n", "                    cur_edit[\"old_text\"] += event.replace_text_response.old_text\n", "                if event.replace_text_response.start_line_number:\n", "                    cur_edit[\"start_line\"] = (\n", "                        event.replace_text_response.start_line_number\n", "                    )\n", "                if event.replace_text_response.end_line_number:\n", "                    cur_edit[\"end_line\"] = event.replace_text_response.end_line_number\n", "                if event.replace_text_response.replacement_text:\n", "                    cur_edit[\"replacement_text\"] += (\n", "                        event.replace_text_response.replacement_text\n", "                    )\n", "        if cur_edit[\"end_line\"] is None:\n", "            print(claude_response.content[0].text)\n", "            assert False\n", "        collected_edits.append(cur_edit)\n", "\n", "        # Process edits\n", "        line_ranges = []\n", "        for i, change in enumerate(collected_edits):\n", "            if change[\"end_line\"] > change[\"start_line\"]:\n", "                change[\"end_line\"] -= 1\n", "\n", "                change[\"start_line\"] = adjust_line_number(\n", "                    misc_formatting_data[\"full_file_in_prompt\"],\n", "                    change[\"start_line\"],\n", "                    change[\"old_text\"].splitlines(True)[0],\n", "                )\n", "                change[\"end_line\"] = adjust_line_number(\n", "                    misc_formatting_data[\"full_file_in_prompt\"],\n", "                    change[\"end_line\"],\n", "                    change[\"old_text\"].splitlines(True)[-1],\n", "                )\n", "            else:\n", "                # Pure insertion\n", "                print(f\"Pure insertion: {change['start_line']}\")\n", "                change[\"end_line\"] = adjust_line_number(\n", "                    misc_formatting_data[\"full_file_in_prompt\"],\n", "                    change[\"end_line\"],\n", "                    change[\"old_text\"].splitlines(True)[-1],\n", "                )\n", "                change[\"start_line\"] = change[\"end_line\"] + 1\n", "\n", "            line_ranges.append(\n", "                (\n", "                    change[\"start_line\"],\n", "                    change[\"end_line\"],\n", "                )\n", "            )\n", "            cur_original_text = \"\".join(\n", "                misc_formatting_data[\"full_file_in_prompt\"].splitlines(keepends=True)[\n", "                    change[\"start_line\"] - 1 : change[\"end_line\"]\n", "                ]\n", "            )\n", "            updated_text = change[\"replacement_text\"]\n", "            cur_diff = \"\".join(\n", "                unified_diff(\n", "                    cur_original_text.splitlines(True),\n", "                    updated_text.splitlines(True),\n", "                )\n", "            )\n", "            compiled_text += f\"\"\"{i} {change[\"start_line\"]}-{change[\"end_line\"]}\n", "```\n", "{updated_text}\n", "```\n", "            \n", "Diff:\n", "```\n", "{cur_diff}\n", "```\n", "\n", "{'=' * 30}\n", "\"\"\"\n", "        return {\n", "            \"line_ranges\": line_ranges,\n", "            \"compiled_text\": compiled_text,\n", "            \"numbered_code\": regular_code,\n", "        }"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# # Code edits\n", "class CodeEditsMix:\n", "    @classmethod\n", "    def get_response(\n", "        cls,\n", "        prompt_input: ChatPromptInput,\n", "        prompt_output,\n", "        num_lines_in_prefix_suffix: int = 500,\n", "    ) -> dict:\n", "        prefix = prompt_input.prefix\n", "        selected_code = prompt_input.selected_code\n", "        suffix = prompt_input.suffix\n", "        file_path = prompt_input.path\n", "\n", "        regular_code, misc_formatting_data = format_code(\n", "            prefix=prefix,\n", "            selected_code=selected_code,\n", "            suffix=suffix,\n", "            num_lines_in_prefix_suffix=num_lines_in_prefix_suffix,\n", "            number_mode=\"regular\",\n", "            is_highlighted=True,\n", "        )\n", "        context_prefix = \"\".join(misc_formatting_data[\"prefix\"].splitlines(True)[-5:])\n", "        context_suffix = \"\".join(misc_formatting_data[\"suffix\"].splitlines(True)[:5])\n", "\n", "        if context_prefix.endswith(\"\\n\"):\n", "            context_prefix = context_prefix[:-1]\n", "        if context_suffix.startswith(\"\\n\"):\n", "            context_suffix = context_suffix[1:]\n", "\n", "        prompt = f\"\"\"I have opened a file `{file_path}` and highlighted a part of the code (enclosed in <highlighted_code> tag):\n", "<file path=\"{file_path}\">\n", "{misc_formatting_data[\"no_numbered_code\"].rstrip()}\n", "</file>\n", "\n", "Please, rewrite the highlighted region according to the following instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\n", "Put couple lines of context before and after the highlighted region.\n", "\n", "Use this output format:\n", "```\n", "{context_prefix}\n", "<<<<<<< original\n", "...\n", "=======\n", "...\n", ">>>>>>> updated\n", "{context_suffix}\n", "```\n", "\"\"\"\n", "\n", "        selected_code = misc_formatting_data[\"selected_code\"]\n", "        if selected_code.endswith(\"\\n\"):\n", "            selected_code = selected_code[:-1]\n", "\n", "        prefill_str = f\"\"\"Here's the edited code with the requested format:\n", "\n", "```\n", "{context_prefix}\n", "<<<<<<< original\n", "{selected_code}\n", "=======\"\"\"\n", "\n", "        claude_response = run_anthropic_w_retries(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ],\n", "            \"You are highly advanced and intelligent AI code assistant.\",\n", "            prefill=prefill_str,\n", "        )\n", "\n", "        full_claude_response = prefill_str + claude_response.content[0].text\n", "\n", "        replace_response = extract_between_patterns(\n", "            full_claude_response, \"======\\n\", \">>>>>>>\", 0\n", "        )\n", "        original_file_state = (\n", "            misc_formatting_data[\"prefix\"]\n", "            + misc_formatting_data[\"selected_code\"]\n", "            + misc_formatting_data[\"suffix\"]\n", "        )\n", "        updated_file_state = (\n", "            misc_formatting_data[\"prefix\"]\n", "            + replace_response\n", "            + misc_formatting_data[\"suffix\"]\n", "        )\n", "        diff = \"\".join(\n", "            unified_diff(\n", "                original_file_state.splitlines(True),\n", "                updated_file_state.splitlines(True),\n", "            )\n", "        )\n", "\n", "        compiled_text = f\"\"\"Raw response\n", "{full_claude_response}\n", "{'=' * 30}\n", "\n", "Selected code:\n", "{'-' * 30}\n", "{misc_formatting_data[\"selected_code\"]}{'-' * 30}\n", "{'=' * 30}\n", "\n", "Diff:\n", "{diff}\n", "        \n", "\"\"\"\n", "\n", "        return {\n", "            \"line_ranges\": [(-1, -1)],\n", "            \"compiled_text\": compiled_text,\n", "            \"numbered_code\": regular_code,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_report = \"\"\n", "\n", "\n", "for sample in tqdm(SAMPLES):\n", "    if isinstance(sample, str):\n", "        request_id = sample\n", "        sample = download_samples[request_id]\n", "        file_path_to_apply = None\n", "        block_id = 0\n", "    else:\n", "        request_id, file_path_to_apply, block_id = sample  # type: ignore\n", "        sample = download_samples[request_id]\n", "\n", "    print(request_id)\n", "\n", "    # SmartpasteGit\n", "    prompt_input, prompt_output = format_sample(sample, num_chunks_to_keep=0)\n", "    code_block = extract_between_patterns(\n", "        sample[\"response\"][\"text\"], \"```\", \"```\", block_id\n", "    )\n", "    response = SmartpasteApplyCodeblockGitConflict.get_response(\n", "        prompt_input,\n", "        prompt_output,\n", "        sample[\"response\"][\"text\"],\n", "        code_block,\n", "        file_path_to_apply,\n", "    )\n", "\n", "    # Code edits\n", "    # assert file_path_to_apply is None\n", "    # prompt_input, prompt_output = format_sample(\n", "    #     sample, num_chunks_to_keep=10, drop_history=True\n", "    # )\n", "    # response = CodeEditsMix.get_response(\n", "    #     prompt_input,\n", "    #     prompt_output,\n", "    #     num_lines_in_prefix_suffix=500,\n", "    # )\n", "\n", "    with open(\"/tmp/log899\", \"a\") as f:\n", "        f.write(response[\"compiled_text\"])\n", "        f.write(\"\\n\" * 20)\n", "    # continue\n", "\n", "    # HTML report\n", "    cur_html = f\"<h2>Chain {request_id}</h2>\"\n", "    if request_id in STAGING_SHARD0_SAMPLES:\n", "        cur_html += f'<a href=\"{STAGING_DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "    else:\n", "        cur_html += f'<a href=\"{DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "\n", "    numbered_code = response[\"numbered_code\"].replace(\"\\n\", \"<br>\")\n", "    cur_html += row_html(\n", "        f'<div class=\"code\">{numbered_code}</div>'\n", "        + f'<div class=\"code\">{markdown(response[\"compiled_text\"])}</div>'\n", "        + f'<div class=\"code\">{response[\"line_ranges\"]}</div>'\n", "        + \"<hr>\"\n", "    )\n", "    html_report += cur_html\n", "\n", "html_report = wrap_html(html_report)\n", "with open(\"./claude_v899.html\", \"w\") as f:\n", "    f.write(html_report)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
system:
  name: droid_code_edit
  model:
    name: fastforward_droid
    # checkpoint_path: "/mnt/efs/augment/user/yuri/tmp/checkpoint_llama_iteration_1269_ffw"
    # checkpoint_path: "/mnt/efs/augment/user/yuri/experiments/yuri_fix_imports_mar30_v11/ffw"
    checkpoint_path: "/mnt/efs/augment/user/yuri/experiments/yuri_fix_imports_mar30_v13/ffw"
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024

task:
  name: edit_eval_task
  # dataset_path: "/mnt/efs/augment/data/eval/code_edits_91"
  # dataset_path: "/mnt/efs/augment/data/eval/code_edit_eval/pr_edits_eval_145"
  dataset_path: "/mnt/efs/augment/user/yuri/tmp/test_mar27_v2"

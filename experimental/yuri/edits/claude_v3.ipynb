{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Iterator, Optional\n", "from google.cloud import bigquery\n", "from pathlib import Path\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from experimental.yuri.preferences.utils import markdown, row_html, wrap_html\n", "from tqdm import tqdm\n", "\n", "\n", "STAGING_SHARD0_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    \"0696c744-9fb8-4b50-b655-fad32ddb38d5\",\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\",\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "    # <PERSON>'s new samples\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\",\n", "]\n", "\n", "OLD_DOGFOOD_SAMPLES = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "]\n", "\n", "REQUEST_IDS = OLD_DOGFOOD_SAMPLES + STAGING_SHARD0_SAMPLES\n", "EXPECTED_OUTPUTS_DIR = Path(\n", "    \"/mnt/efs/augment/user/yuri/tmp/smart_paste_eval_sep5_expected_outputs\"\n", ")\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    \"augment-387916\", \"us-east5\", \"claude-3-5-sonnet@20240620\", 0, 1024 * 5\n", ")\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", ")\n", "PROMPT_FORMATTER = get_structured_chat_prompt_formatter_by_name(\n", "    \"structured-binks-claude\", TOKEN_APPORTIONMENT\n", ")\n", "DOGFOOD_URL = (\n", "    \"https://support.dogfood.t.us-central1.prod.augmentcode.com/t/dogfood/request\"\n", ")\n", "STAGING_DOGFOOD_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_chat_samples(\n", "    request_ids,\n", "    project_id: str = \"system-services-prod\",\n", "    dataset_name: str = \"staging_request_insight_full_export_dataset\",\n", ") -> dict:\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {project_id}.{dataset_name}.request_metadata AS metadata\n", "JOIN {project_id}.{dataset_name}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {project_id}.{dataset_name}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=project_id)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "            \"request\": row.request[\"request\"],\n", "            \"response\": row.response[\"response\"],\n", "            \"metadata\": row.metadata,\n", "            \"datetime\": row.time.isoformat(),\n", "            \"row\": row,\n", "        }\n", "\n", "    return chat_rows_dic\n", "\n", "\n", "def number_lines(\n", "    text: str, start_line_offset: int, is_xml: bool = True, prefix: Optional[str] = None\n", ") -> Iterator[str]:\n", "    for i, line in enumerate(text.splitlines(keepends=True)):\n", "        if is_xml:\n", "            assert prefix is None, \"prefix is not used for xml\"\n", "            cur_number = i + 1 + start_line_offset\n", "            yield f\"<line number={cur_number}>{line.rstrip()}</line number={cur_number}>\\n\"\n", "        else:\n", "            assert prefix is not None\n", "            yield f\"{prefix}{i+1+start_line_offset:04d}: {line}\"\n", "\n", "\n", "def num_lines(s: str):\n", "    return len(s.splitlines(keepends=True))\n", "\n", "\n", "def format_code(prompt_input, num_lines_in_prefix_suffix):\n", "    prefix = \"\".join(\n", "        prompt_input.prefix.splitlines(keepends=True)[-num_lines_in_prefix_suffix:]\n", "    )\n", "    suffix = \"\".join(\n", "        prompt_input.suffix.splitlines(keepends=True)[:num_lines_in_prefix_suffix]\n", "    )\n", "    selected_code = prompt_input.selected_code\n", "\n", "    prefix_n_xml = \"\".join(number_lines(prefix, 0, is_xml=True))\n", "    selected_code_n_xml = \"\".join(\n", "        number_lines(selected_code, num_lines(prefix), is_xml=True)\n", "    )\n", "    suffix_n_xml = \"\".join(\n", "        number_lines(suffix, num_lines(prefix) + num_lines(selected_code), is_xml=True)\n", "    )\n", "    xml_code = f\"\"\"{prefix_n_xml}<highlighted_code>\n", "{selected_code_n_xml}</highlighted_code>\n", "{suffix_n_xml}\"\"\"\n", "\n", "    prefix_n = \"\".join(number_lines(prefix, 0, is_xml=False, prefix=\"\"))\n", "    selected_code_n = \"\".join(\n", "        number_lines(selected_code, num_lines(prefix), is_xml=False, prefix=\"*\")\n", "    )\n", "    suffix_n = \"\".join(\n", "        number_lines(\n", "            suffix,\n", "            num_lines(prefix) + num_lines(selected_code),\n", "            is_xml=False,\n", "            prefix=\"\",\n", "        )\n", "    )\n", "    regular_code = f\"\"\"{prefix_n}{selected_code_n}{suffix_n}\"\"\"\n", "\n", "    return xml_code, regular_code\n", "\n", "\n", "def run_anthropic_w_tools(cur_message, history, system_prompt):\n", "    formatted_messages = []\n", "    for message in history:\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": message[0],\n", "            }\n", "        )\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": message[1],\n", "            }\n", "        )\n", "\n", "    # Add cur message\n", "    formatted_messages.append(\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": cur_message,\n", "        }\n", "    )\n", "\n", "    response = ANTHROPIC_CLIENT.client.messages.create(\n", "        model=\"claude-3-5-sonnet@20240620\",\n", "        max_tokens=8192,\n", "        messages=formatted_messages,\n", "        system=system_prompt,\n", "        temperature=0,\n", "        tools=[\n", "            {\n", "                \"name\": \"replace_text\",\n", "                \"description\": \"Replace substring of file with new text\",\n", "                \"input_schema\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"replacement_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The new text.\",\n", "                        },\n", "                        \"start_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text starts, inclusive.\",\n", "                        },\n", "                        \"end_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text ends, inclusive.\",\n", "                        },\n", "                    },\n", "                    \"required\": [\n", "                        \"replacement_text\",\n", "                        \"start_line_number\",\n", "                        \"end_line_number\",\n", "                    ],\n", "                },\n", "            }\n", "        ],\n", "    )\n", "    return response\n", "\n", "\n", "class SmartPastePlusXml2StepCodeEdits:\n", "    @classmethod\n", "    def get_response(\n", "        cls, prompt_input: ChatPromptInput, prompt_output, main_response: str\n", "    ) -> dict:\n", "        \"\"\"This function internally uses XML-based line numbering, but returns regular one for visualization.\"\"\"\n", "        xml_code, regular_code = format_code(\n", "            prompt_input, num_lines_in_prefix_suffix=10\n", "        )\n", "\n", "        prompt = f\"\"\"Software developer highlighted a part of the code and asks you to apply changes here. You have to do it with a SINGLE call to the tool.\n", "        \n", "Make sure to follow the previous instructions precisely — introduce no additional changes, and ensure that all of the suggested modifications are applied.\n", "Treat highlighted code as only a hint of a ROUGH location where change should be applied, NOT the strict boundary.\n", "\n", "```\n", "{xml_code}\n", "```\n", "\"\"\"\n", "        claude_response = run_anthropic_w_tools(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ]\n", "            + [(prompt_input.message, main_response)],\n", "            prompt_output.system_prompt,\n", "        )\n", "        tool_result = claude_response.content[1]\n", "        assert tool_result.type == \"tool_use\"\n", "\n", "        return {\n", "            \"line_range\": (\n", "                tool_result.input[\"start_line_number\"],\n", "                tool_result.input[\"end_line_number\"],\n", "            ),\n", "            \"replacement_text\": tool_result.input[\"replacement_text\"],  # type: ignore\n", "            \"numbered_code\": regular_code,\n", "        }\n", "\n", "\n", "def format_sample(raw_sample):\n", "    history = [\n", "        Exchange(e[\"request_message\"], e[\"response_text\"], e[\"request_id\"])\n", "        for e in raw_sample[\"row\"].request[\"request\"].get(\"chat_history\", [])\n", "    ]\n", "    chunks = [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            char_start=chunk.get(\"char_offset\", 0),\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk[\"blob_name\"],\n", "            origin=chunk[\"origin\"],\n", "        )\n", "        for chunk in raw_sample[\"row\"].request[\"retrieved_chunks\"]\n", "    ]\n", "    request_entry = raw_sample[\"row\"].request[\"request\"]\n", "    if request_entry.get(\"suffix\", \"\").startswith(\"\\n\"):\n", "        # IDK why this happens, but it does\n", "        request_entry[\"selected_code\"] += \"\\n\"\n", "        request_entry[\"suffix\"] = request_entry[\"suffix\"][1:]\n", "        print(\"Suffix starts with new line\")\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=request_entry[\"path\"],\n", "        prefix=request_entry.get(\"prefix\", \"\"),\n", "        selected_code=request_entry[\"selected_code\"],\n", "        suffix=request_entry.get(\"suffix\", \"\"),\n", "        message=request_entry[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(\n", "            request_entry.get(\"prefix\", \"\")\n", "            + request_entry[\"selected_code\"]\n", "            + request_entry.get(\"suffix\", \"\")\n", "        ),\n", "        retrieved_chunks=chunks,\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    return prompt_input, prompt_output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["download_samples = get_chat_samples(REQUEST_IDS)\n", "\n", "\n", "expected_outputs = {\n", "    request_id: (EXPECTED_OUTPUTS_DIR / (request_id + \".txt\")).read_text(\n", "        encoding=\"utf8\"\n", "    )\n", "    if (EXPECTED_OUTPUTS_DIR / (request_id + \".txt\")).exists()\n", "    else \"\"\n", "    for request_id in REQUEST_IDS\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_report = \"\"\n", "\n", "for request_id in tqdm(REQUEST_IDS):\n", "    sample = download_samples[request_id]\n", "    prompt_input, prompt_output = format_sample(sample)\n", "    response = SmartPastePlusXml2StepCodeEdits.get_response(\n", "        prompt_input, prompt_output, sample[\"response\"][\"text\"]\n", "    )\n", "\n", "    cur_html = f\"<h2>Chain {request_id}</h2>\"\n", "    if request_id in STAGING_SHARD0_SAMPLES:\n", "        cur_html += f'<a href=\"{STAGING_DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "    else:\n", "        cur_html += f'<a href=\"{DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "\n", "    numbered_code = response[\"numbered_code\"].replace(\"\\n\", \"<br>\")\n", "    cur_html += row_html(\n", "        f'<div class=\"code\">{numbered_code}</div>'\n", "        + f'<div class=\"code\">{markdown(response[\"replacement_text\"])}</div>'\n", "        + f'<div class=\"code\">{response[\"line_range\"]}</div>'\n", "        + f'<div class=\"code\">{markdown(expected_outputs[request_id])}</div>'\n", "        + \"<hr>\"\n", "    )\n", "    html_report += cur_html\n", "\n", "\n", "html_report = wrap_html(html_report)\n", "with open(\"./claude_v107.html\", \"w\") as f:\n", "    f.write(html_report)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["numbered_code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
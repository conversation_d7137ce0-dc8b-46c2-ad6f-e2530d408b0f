{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import difflib\n", "\n", "from pathlib import Path\n", "from base.datasets.edit import EditDatum\n", "from tqdm import tqdm\n", "from dataclasses import asdict\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from research.data.synthetic_code_edit.util_lib import get_unified_diff\n", "from collections import defaultdict\n", "from multiprocessing import Pool"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/mnt/efs/augment/user/guy/export-code-edits/pareto_edits_augment_internal.jsonl\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = []\n", "\n", "skipped = defaultdict(int)\n", "\n", "with DATA_PATH.open(\"r\") as f:\n", "    for line in tqdm(f):\n", "        sample = EditDatum.from_json(line)\n", "        if len(sample.resolution.annotated_text) == 0:\n", "            skipped[\"empty_annotated\"] += 1\n", "            continue\n", "        if sample.request.selected_text == sample.resolution.annotated_text:\n", "            skipped[\"same_code\"] += 1\n", "            continue\n", "        data.append(sample)        \n", "\n", "len(data), skipped"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# len([*filter(lambda s: len(s.resolution.annotated_instruction) > 0, data)])\n", "len([*filter(lambda s: s.request.selected_text == s.resolution.annotated_text, data)])\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["asdict(data[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT = \"\"\"Here is a initial chunk of code: \n", "```\n", "{selected_code}\n", "```\n", "\n", "Here is the updated code:\n", "```\n", "{updated_code}\n", "```\n", "\n", "And here is the diff between them:\n", "```\n", "{diff}\n", "```\n", "\n", "\n", "Your task is to generate an instruction that developer may have followed to turn initial code into the updated code.\n", "\n", "Please, use this instruction (`{init_instruction}`) as a first version and refine it if needed. \n", "Refined instruction should not be much longer that first version. Max 5 more words than first version!\n", "\n", "Follow this steps to complete this task:\n", "1. Analyze the initial code, updated code, diff and first version of instruction.\n", "2. Describe your thinking process on how to complete this task, while keeping the refined instruction approximately the same length as the first version.\n", "3. Explicitly write the refined instruction.\n", "\"\"\"\n", "\n", "\n", "EXTRACT_PROMPT = \"\"\"Return the result as a JSON with 1 key:\n", "- refined_instruction\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = data[1]\n", "asdict(sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# processed = []\n", "\n", "# for sample in tqdm(data):\n", "#     diff = get_unified_diff(sample.request.selected_text, sample.resolution.annotated_text)\n", "\n", "#     prompt = PROMPT.format(\n", "#         selected_code=sample.request.selected_text,\n", "#         updated_code=sample.resolution.annotated_text,\n", "#         init_instruction=sample.request.instruction,\n", "#         diff=diff,\n", "#     )\n", "\n", "#     messages = [{\"role\": \"user\", \"content\": prompt}]\n", "#     gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "\n", "#     messages.append({\"role\": \"assistant\", \"content\": gpt4_response})\n", "#     messages.append({\"role\": \"user\", \"content\": EXTRACT_PROMPT})\n", "\n", "#     result = gpt(\n", "#         messages, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0\n", "#     )\n", "\n", "#     processed.append([sample, result])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def process_sample(sample):\n", "    diff = get_unified_diff(sample.request.selected_text, sample.resolution.annotated_text)\n", "\n", "    prompt = PROMPT.format(\n", "        selected_code=sample.request.selected_text,\n", "        updated_code=sample.resolution.annotated_text,\n", "        init_instruction=sample.request.instruction,\n", "        diff=diff,\n", "    )\n", "\n", "    gpt = GptWrapper()\n", "\n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "\n", "    messages.append({\"role\": \"assistant\", \"content\": gpt4_response})\n", "    messages.append({\"role\": \"user\", \"content\": EXTRACT_PROMPT})\n", "\n", "    result = gpt(\n", "        messages, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0\n", "    )\n", "\n", "    return [sample, result]\n", "\n", "# Assuming 'data' is a list of samples and you have a pool of workers initialized\n", "with Pool(processes=50) as pool:\n", "    processed = list(tqdm(pool.imap(process_sample, data), total=len(data)))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "def get_diff_html(input_code, output_code, comment, another_header=None):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><strong>{comment}</strong></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Refined instruction: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    if another_header is not None:\n", "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n", "    return html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "\n", "for i, (sample, result) in enumerate(processed):\n", "    if i == 83:\n", "        continue\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {i}</h2><hr>\"\n", "\n", "    cur_diff = get_diff_html(\n", "        sample.request.selected_text,\n", "        sample.resolution.annotated_text,\n", "        result[\"refined_instruction\"],\n", "        f\"Original instruction: {sample.request.instruction}\",\n", "    )\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_apt_4_refine_v3.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
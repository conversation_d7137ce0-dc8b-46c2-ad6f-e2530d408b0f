{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "import difflib\n", "\n", "from pathlib import Path\n", "from collections import defaultdict\n", "from base.datasets.edit import EditDatum\n", "from tqdm import tqdm\n", "from dataclasses import asdict"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "\n", "def get_diff_html(input_code, output_code, comment, another_header=None):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><strong>{comment}</strong></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Instruction: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    if another_header is not None:\n", "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n", "    return html\n", "\n", "def visualize(dataset, output_path):\n", "    MAIN_HTML = \"\"\n", "\n", "    for i, sample in enumerate(dataset):\n", "        MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "        MAIN_HTML += f\"<h2>Code sample {i}</h2><hr>\"\n", "\n", "        cur_diff = get_diff_html(\n", "            sample['selected_text'],\n", "            sample['updated_text'],\n", "            sample['instruction'],\n", "        )\n", "\n", "        MAIN_HTML += f\"{cur_diff}<hr>\"\n", "        \n", "\n", "    RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "    with open(output_path, 'w') as f:\n", "        f.write(RESULTING_HTML)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pareto"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/mnt/efs/augment/user/guy/export-code-edits/pareto_edits_augment_internal.jsonl\")\n", "\n", "data = []\n", "pareto_unified = []\n", "\n", "skipped = defaultdict(int)\n", "\n", "with DATA_PATH.open(\"r\") as f:\n", "    for line in tqdm(f):\n", "        sample = EditDatum.from_json(line)\n", "        if len(sample.resolution.annotated_text) == 0:\n", "            skipped[\"empty_annotated\"] += 1\n", "            continue\n", "        if sample.request.selected_text == sample.resolution.annotated_text:\n", "            skipped[\"same_code\"] += 1\n", "            continue\n", "        data.append(sample)        \n", "\n", "        unified_sample = {\n", "            \"selected_text\": sample.request.selected_text,\n", "            \"updated_text\": sample.resolution.annotated_text,\n", "            \"instruction\": sample.resolution.annotated_instruction if len(sample.resolution.annotated_instruction) > 0 else sample.request.instruction,\n", "            \"full_sample\": sample\n", "        }\n", "        pareto_unified.append(unified_sample)\n", "\n", "len(data), len(pareto_unified), skipped"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["visualize(pareto_unified, \"./filter_pareto_apr8_v10.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # update this such that each kwargs instructions group use a if \"wathever\" in kwargs: style of syntax???\n", "\n", "# #BAD\n", "# 4,\n", "# 6,\n", "# 7,\n", "# 8,\n", "# 9,\n", "# 10,\n", "# 13,\n", "# 15,\n", "# 18,\n", "# 20 - missing last line\n", "# 28 - missing symbol\n", "# 31,\n", "# 32,\n", "# 34, - missing last symbol\n", "# 35 - bad phrasing optimize/refactor\n", "# 37 - missing last line?\n", "# 38 - missing last character\n", "# 40 - irrelevant change\n", "# 42 - funny\n", "# 43 - bug in last two lines\n", "# 44 - non-required change\n", "# 46 - just bad\n", "# ?48 - too much comments?\n", "# 49 - just bad on boundaries\n", "# 50 - wat\n", "# 52 - irrelevant change\n", "# 53 - irrelevant change\n", "# 56 - bad?\n", "# 57\n", "# 58\n", "# 59 - not sure\n", "# 65 - too much change\n", "# 66 - missing symbol\n", "# 78 - non-required change\n", "# 82 - missing symbol\n", "# 85 - non-required change\n", "# 90 - not sure, but likely bad?\n", "# 93 - missing symbol\n", "# 95 - not sure, but looks weird?\n", "# 97 - missing symbol\n", "# 98 - ???\n", "# 99 - non-required change\n", "# 100 - non-required change\n", "# 101 - missing symbol\n", "# 102 - missing symbol\n", "# 103 - missing symbol\n", "# 105 - missing symbol\n", "# 106 - missing symbol\n", "# 107 - too much?\n", "# 108 - same code?\n", "# 112 - broken at the end\n", "# 115 - not sure?\n", "# 118 - missing symbol\n", "# 120 - missing symbol\n", "# 122 - missing symbols\n", "# 123 - missing symbol\n", "# 127 - non-required change\n", "# 132 - likely non-required change\n", "# 136 - not really good\n", "# 138 - bad?\n", "# 140 - bad\n", "# 146 - same problem of defining a function, but not implementing \n", "# 147 - non-required change\n", "# 148 - too much comments\n", "# 151 - missing symbol\n", "\n", "instruction_updates = {\n", "    4: \"missing last line\",\n", "    6: \"\",\n", "    7: \"\",\n", "    8: \"\",\n", "    9: \"\",\n", "    10: \"\",\n", "    13: \"\",\n", "    15: \"\",\n", "    18: \"\",\n", "    20: \"missing last line\",\n", "    28: \"missing symbol\",\n", "    31: \"\",\n", "    32: \"\",\n", "    34: \"missing last symbol\",\n", "    35: \"bad phrasing optimize/refactor\",\n", "    37: \"missing last line?\",\n", "    38: \"missing last character\",\n", "    40: \"irrelevant change\",\n", "    42: \"funny\",\n", "    43: \"bug in last two lines\",\n", "    44: \"non-required change\",\n", "    46: \"just bad\",\n", "    48: \"too much comments?\",\n", "    49: \"just bad on boundaries\",\n", "    50: \"wat\",\n", "    52: \"irrelevant change\",\n", "    53: \"irrelevant change\",\n", "    56: \"bad?\",\n", "    57: \"\",\n", "    58: \"\",\n", "    59: \"not sure\",\n", "    65: \"too much change\",\n", "    66: \"missing symbol\",\n", "    78: \"non-required change\",\n", "    82: \"missing symbol\",\n", "    85: \"non-required change\",\n", "    90: \"not sure, but likely bad?\",\n", "    93: \"missing symbol\",\n", "    95: \"not sure, but looks weird?\",\n", "    97: \"missing symbol\",\n", "    98: \"???\",\n", "    99: \"non-required change\",\n", "    100: \"non-required change\",\n", "    101: \"missing symbol\",\n", "    102: \"missing symbol\",\n", "    103: \"missing symbol\",\n", "    105: \"missing symbol\",\n", "    106: \"missing symbol\",\n", "    107: \"too much?\",\n", "    108: \"same code?\",\n", "    112: \"broken at the end\",\n", "    115: \"not sure?\",\n", "    118: \"missing symbol\",\n", "    120: \"missing symbol\",\n", "    122: \"missing symbols\",\n", "    123: \"missing symbol\",\n", "    127: \"non-required change\",\n", "    132: \"likely non-required change\",\n", "    136: \"not really good\",\n", "    138: \"bad?\",\n", "    140: \"bad\",\n", "    146: \"same problem of defining a function, but not implementing\",\n", "    147: \"non-required change\",\n", "    148: \"too much comments\",\n", "    151: \"missing symbol\",\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["missing_samples = []\n", "\n", "for k, v in instruction_updates.items():\n", "    if \"missing\" in v:\n", "        missing_samples.append(k)\n", "\n", "len(missing_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["missing_samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in missing_samples:\n", "    print(data[k].request_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["asdict(data[56])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_good = []\n", "data_bad = []\n", "pareto_unified_good = []\n", "pareto_unified_bad = []\n", "for i in range(len(data)):\n", "    if i in instruction_updates.keys():\n", "        data_bad.append(data[i])\n", "        pareto_unified_bad.append(pareto_unified[i])\n", "    else:\n", "        data_good.append(data[i])\n", "        pareto_unified_good.append(pareto_unified[i])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["visualize(pareto_unified_bad, \"./filter_pareto_bad_apr8_v1.html\")\n", "visualize(pareto_unified_good, \"./filter_pareto_good_apr8_v1.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FILTERED_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/pareto_edits_augment_internal_filtered_apr8.jsonl\")\n", "with open(FILTERED_DATA_PATH, \"a\") as f:\n", "    for good_sample in data_good:\n", "        f.write(good_sample.to_json() + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[30]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Turing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TURING_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/turing-03-Apr-2024/output\")\n", "\n", "turing_data = []\n", "turing_unified = []\n", "\n", "skipped = defaultdict(int)\n", "\n", "for f_path in tqdm(TURING_DATA_PATH.glob(\"*.json\")):\n", "    with f_path.open() as f:\n", "        sample = json.load(f)\n", "        if len(sample['feedback']['annotated_text']) == 0:\n", "            skipped[\"empty_annotated\"] += 1\n", "            continue\n", "        if sample['request']['selected_text'] == sample['feedback']['annotated_text']:\n", "            skipped[\"same_code\"] += 1\n", "            continue\n", "\n", "        turing_data.append(sample)\n", "        unified_sample = {\n", "            \"selected_text\": sample['request']['selected_text'],\n", "            \"updated_text\": sample['feedback']['annotated_text'],\n", "            # \"instruction\": sample['feedback']['annotated_instruction'] if len(sample['feedback']['annotated_instruction']) > 0 else sample['request']['instruction']\n", "            \"instruction\": sample['request']['instruction'],\n", "        }\n", "        turing_unified.append(unified_sample)\n", "\n", "len(turing_data), len(turing_unified), skipped"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(\n", "    [*filter(\n", "        lambda s: s['feedback']['annotated_text'] != '',\n", "        turing_data\n", "    )]\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["visualize(turing_unified, \"./filter_turing_apr8_v11.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BAD\n", "\n", "1 - replace?\n", "7 - weird samples\n", "10 - missing symbol\n", "16 - missing last line\n", "17 - missing last symbo\n", "21 - missing first line and last symbol\n", "25 - missing ending\n", "30 - missing symbol\n", "31 - ???\n", "32 - like non-required change\n", "33 - just bad?\n", "34 - bad?\n", "37 - missing\n", "38 - ???\n", "39 - missing\n", "42 - missing\n", "45 - missing\n", "47 - missing\n", "48 - missing\n", "52 - missing line\n", "58 - missing symbol\n", "61 - missing\n", "65 - missing\n", "69 - missing\n", "76 - weird instruction\n", "88 - explanation in instruction\n", "95 - missing symbol\n", "108 - ? symbol\n", "111 - missing symbol\n", "112 - missing symbol\n", "113 - as string?\n", "117 - missing\n", "118 - missing symbol + large comment\n", "122 - weird instruction\n", "123 - why just deleted?\n", "127 - missing \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["turing_data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["turing_data[80]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["visualize(turing_unified, \"./filter_turing_apr8_v11.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Turing Apr 14"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TURING_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/turing-11-Apr-2024/client/11-Apr-2024\")\n", "\n", "turing_data = []\n", "turing_unified = []\n", "\n", "skipped = defaultdict(int)\n", "\n", "for f_path in tqdm(TURING_DATA_PATH.glob(\"*.json\")):\n", "    with f_path.open() as f:\n", "        sample = json.load(f)\n", "\n", "        if \"request\" not in sample:\n", "            skipped[\"no_request\"] += 1\n", "            continue\n", "        if len(sample['feedback']['annotated_text']) == 0:\n", "            skipped[\"empty_annotated\"] += 1\n", "            continue\n", "        if sample['request']['selected_text'] == sample['feedback']['annotated_text']:\n", "            skipped[\"same_code\"] += 1\n", "            continue\n", "\n", "        turing_data.append(sample)\n", "        unified_sample = {\n", "            \"selected_text\": sample['request']['selected_text'],\n", "            \"updated_text\": sample['feedback']['annotated_text'],\n", "            # \"instruction\": sample['feedback']['annotated_instruction'] if len(sample['feedback']['annotated_instruction']) > 0 else sample['request']['instruction']\n", "            \"instruction\": sample['request']['instruction'],\n", "        }\n", "        turing_unified.append(unified_sample)\n", "\n", "len(turing_data), len(turing_unified), skipped"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["visualize(turing_unified, \"./filter_turing_apr11_v1.html\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["1 - Unrequired change (seems like) \n", "6 - Weird sample\n", "9 - unrequired changes\n", "12 - unrequired changes \n", "13 - missing ending\n", "15 - Weird sample\n", "16 - Unrequired change (seems like)\n", "17 - Weird sample\n", "22 - missing ending\n", "26 - Unrelated instruction?\n", "34 - unrequired change\n", "37 - weird instruction\n", "38 - Weird sample\n", "40 - Weird boundaries\n", "41 - Weird boundaries\n", "43 - Bad sample\n", "44 - Weird boundaries\n", "45 - wtf?\n", "46 - Weird boundaries\n", "48 - missing ending\n", "49 - missing ending\n", "56 - missing ending token\n", "58 - lets not do this\n", "59 - weird sample\n", "61 - unrequired change\n", "64 - Unrelated change?\n", "66 - Weird boundaries\n", "76 - Weird boundaries\n", "77 - Unrelated change + missing ending"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = {\n", "    1: \"Unrequired change (seems like)\",\n", "    6: \"Weird sample\",\n", "    9: \"unrequired changes\",\n", "    12: \"unrequired changes\",\n", "    13: \"missing ending\",\n", "    15: \"Weird sample\",\n", "    16: \"Unrequired change (seems like)\",\n", "    17: \"Weird sample\",\n", "    22: \"missing ending\",\n", "    26: \"Unrelated instruction?\",\n", "    34: \"unrequired change\",\n", "    37: \"weird instruction\",\n", "    38: \"Weird sample\",\n", "    40: \"Weird boundaries\",\n", "    41: \"Weird boundaries\",\n", "    43: \"Bad sample\",\n", "    44: \"Weird boundaries\",\n", "    45: \"wtf?\",\n", "    46: \"Weird boundaries\",\n", "    48: \"missing ending\",\n", "    49: \"missing ending\",\n", "    56: \"missing ending token\",\n", "    58: \"lets not do this\",\n", "    59: \"weird sample\",\n", "    61: \"unrequired change\",\n", "    64: \"Unrelated change?\",\n", "    66: \"Weird boundaries\",\n", "    76: \"Weird boundaries\",\n", "    77: \"Unrelated change + missing ending\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unrequired_changes = [\n", "    1, 9, 16, 61, 64, 77\n", "]\n", "\n", "bad_sample = [\n", "    6, 15, 17, 38, 43, 45, 58, 59, \n", "]\n", "\n", "missing_ending = [\n", "    13, 22, 48, 49, 56, \n", "]\n", "\n", "weird_boundaries = [\n", "    12, 34, 40, 41, 44, 46, 61, 66, 76\n", "]\n", "\n", "\n", "len(d.keys()), len(unrequired_changes + bad_sample + missing_ending + weird_boundaries)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["turing_data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k, v in d.items():\n", "    print(k, v, turing_data[k][\"request_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in missing_ending:\n", "    print(k, d[k], turing_data[k][\"request_id\"])\n", "    # print(turing_data[k][\"request_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in weird_boundaries:\n", "    # print(k, d[k], turing_data[k][\"request_id\"])\n", "    print(turing_data[k][\"request_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["turing_data[12]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(turing_data[12][\"request\"][\"suffix\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["a = \"\"\"<a class=\"{{if .PageIsInsights}}active{{end}} item\" href=\"{{.RepoLink}}/insights\">\"\"\"\n", "b = \"\"\"<a class=\"{{if .PageIsInsights}}active{{end}} item\" href=\"{{.RepoLink}}/insights\">\"\"\"\n", "\n", "a == b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Groups:\n", "- Unrequired change\n", "- Just bad sample\n", "- Missing ending (not updated extension?)\n", "- Weird boundarues"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Turing May 20"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["84it [00:00, 7907.21it/s]\n"]}, {"data": {"text/plain": ["(84, 84, defaultdict(int, {}))"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["TURING_DATA_PATH = Path(\"/home/<USER>/repos/augment/experimental/yuri/edits/turing_may20\")\n", "\n", "turing_data = []\n", "turing_unified = []\n", "\n", "skipped = defaultdict(int)\n", "\n", "for f_path in tqdm(TURING_DATA_PATH.glob(\"*.json\")):\n", "    with f_path.open() as f:\n", "        sample = json.load(f)\n", "\n", "        if \"request\" not in sample:\n", "            skipped[\"no_request\"] += 1\n", "            continue\n", "        if len(sample['feedback']['annotated_text']) == 0:\n", "            skipped[\"empty_annotated\"] += 1\n", "            continue\n", "        if sample['request']['selected_text'] == sample['feedback']['annotated_text']:\n", "            skipped[\"same_code\"] += 1\n", "            continue\n", "\n", "        turing_data.append(sample)\n", "        unified_sample = {\n", "            \"selected_text\": sample['request']['selected_text'],\n", "            \"updated_text\": sample['feedback']['annotated_text'],\n", "            # \"instruction\": sample['feedback']['annotated_instruction'] if len(sample['feedback']['annotated_instruction']) > 0 else sample['request']['instruction']\n", "            \"instruction\": sample['request']['instruction'],\n", "        }\n", "        turing_unified.append(unified_sample)\n", "\n", "len(turing_data), len(turing_unified), skipped"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["visualize(turing_unified, \"./filter_turing_may20_v1.html\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'event_type': 'edit',\n", " 'request_id': '2044667a-b63b-412a-83d6-ff1a991061a1',\n", " 'feedback': {'is_accepted': True,\n", "  'annotated_text': \" * Formik form event handlers\\n * import { NativeSyntheticEvent } from 'react-native';\\n */\\nexport interface FormikHandlers {\\n  /** Form submit handler */\\n  handleSubmit: (e?: React.FormEvent<HTMLFormElement> |  NativeSyntheticEvent<HTMLFormElement>) => void;\\n  /** Reset form event handler  */\\n  handleReset: (e?: React.SyntheticEvent<any> | NativeSyntheticEvent<any>) => void;\\n  handleBlur: {\\n    /** Classic React blur handler, keyed by input name */\\n    (e: React.FocusEvent<any> | NativeSyntheticEvent<any>): void;\\n    /** Preact-like linkState. Will return a handleBlur function. */\\n    <T = string | NativeSyntheticEvent<any> | any>(fieldOrEvent: T): T extends string\\n      ? (e:  NativeSyntheticEvent<any> | any) => void\\n      : void;\\n  };\\n  handleChange: {\\n    /** Classic React change handler, keyed by input name */\\n    (e: React.ChangeEvent<any> | NativeSyntheticEvent<any>): void;\\n    /** Preact-like linkState. Will return a handleChange function.  */\\n    <T = string |\\xa0NativeSyntheticEvent<any> | React.ChangeEvent<any>>(\\n      field: T\\n    ): T extends React.ChangeEvent<any> | NativeSyntheticEvent<any>\\n      ? void\\n      : (e: string | React.ChangeEvent<any> | NativeSyntheticEvent<any>) => void;\\n  };\\n\\n  getFieldProps: <Value = any>(\\n    props: string | FieldConfig<Value>\\n  ) => FieldInputProps<Value>;\\n  getFieldMeta: <Value>(name: string) => FieldMetaProps<Value>;\\n  getFieldHelpers: <Value = any>(name: string) => FieldHelperProps<Value>;\\n}\",\n", "  'annotated_instruction': ''},\n", " 'request': {'path': 'packages/formik/src/types.tsx',\n", "  'prefix': \"import * as React from 'react';\\nimport { FieldConfig } from './Field';\\n/**\\n * Values of fields in the form\\n */\\nexport interface FormikValues {\\n  [field: string]: any;\\n}\\n\\n/**\\n * An object containing error messages whose keys correspond to FormikValues.\\n * Should always be an object of strings, but any is allowed to support i18n libraries.\\n */\\nexport type FormikErrors<Values> = {\\n  [K in keyof Values]?: Values[K] extends any[]\\n    ? Values[K][number] extends object // [number] is the special sauce to get the type of array's element. More here https://github.com/Microsoft/TypeScript/pull/21316\\n      ? FormikErrors<Values[K][number]>[] | string | string[]\\n      : string | string[]\\n    : Values[K] extends object\\n    ? FormikErrors<Values[K]>\\n    : string;\\n};\\n\\n/**\\n * An object containing touched state of the form whose keys correspond to FormikValues.\\n */\\nexport type FormikTouched<Values> = {\\n  [K in keyof Values]?: Values[K] extends any[]\\n    ? Values[K][number] extends object // [number] is the special sauce to get the type of array's element. More here https://github.com/Microsoft/TypeScript/pull/21316\\n      ? FormikTouched<Values[K][number]>[]\\n      : boolean\\n    : Values[K] extends object\\n    ? FormikTouched<Values[K]>\\n    : boolean;\\n};\\n\\n/**\\n * Formik state tree\\n */\\nexport interface FormikState<Values> {\\n  /** Form values */\\n  values: Values;\\n  /** map of field names to specific error for that field */\\n  errors: FormikErrors<Values>;\\n  /** map of field names to whether the field has been touched */\\n  touched: FormikTouched<Values>;\\n  /** whether the form is currently submitting */\\n  isSubmitting: boolean;\\n  /** whether the form is currently validating (prior to submission) */\\n  isValidating: boolean;\\n  /** Top level status state, in case you need it */\\n  status?: any;\\n  /** Number of times user tried to submit the form */\\n  submitCount: number;\\n}\\n\\n/**\\n * Formik computed properties. These are read-only.\\n */\\nexport interface FormikComputedProps<Values> {\\n  /** True if any input has been touched. False otherwise. */\\n  readonly dirty: boolean;\\n  /** True if state.errors is empty */\\n  readonly isValid: boolean;\\n  /** The initial values of the form */\\n  readonly initialValues: Values;\\n  /** The initial errors of the form */\\n  readonly initialErrors: FormikErrors<Values>;\\n  /** The initial visited fields of the form */\\n  readonly initialTouched: FormikTouched<Values>;\\n  /** The initial status of the form */\\n  readonly initialStatus?: any;\\n}\\n\\n/**\\n * Formik state helpers\\n */\\nexport interface FormikHelpers<Values> {\\n  /** Manually set top level status. */\\n  setStatus: (status?: any) => void;\\n  /** Manually set errors object */\\n  setErrors: (errors: FormikErrors<Values>) => void;\\n  /** Manually set isSubmitting */\\n  setSubmitting: (isSubmitting: boolean) => void;\\n  /** Manually set touched object */\\n  setTouched: (\\n    touched: FormikTouched<Values>,\\n    shouldValidate?: boolean\\n  ) => Promise<void | FormikErrors<Values>>;\\n  /** Manually set values object  */\\n  setValues: (\\n    values: React.SetStateAction<Values>,\\n    shouldValidate?: boolean\\n  ) => Promise<void | FormikErrors<Values>>;\\n  /** Set value of form field directly */\\n  setFieldValue: (\\n    field: string,\\n    value: any,\\n    shouldValidate?: boolean\\n  ) => Promise<void | FormikErrors<Values>>;\\n  /** Set error message of a form field directly */\\n  setFieldError: (field: string, message: string | undefined) => void;\\n  /** Set whether field has been touched directly */\\n  setFieldTouched: (\\n    field: string,\\n    isTouched?: boolean,\\n    shouldValidate?: boolean\\n  ) =>  Promise<void | FormikErrors<Values>>;\\n  /** Validate form values */\\n  validateForm: (values?: any) => Promise<FormikErrors<Values>>;\\n  /** Validate field value */\\n  validateField: (field: string) => Promise<void> | Promise<string | undefined>;\\n  /** Reset form */\\n  resetForm: (nextState?: Partial<FormikState<Values>>) => void;\\n  /** Submit the form imperatively */\\n  submitForm: () => Promise<void>;\\n  /** Set Formik state, careful! */\\n  setFormikState: (\\n    f:\\n      | FormikState<Values>\\n      | ((prevState: FormikState<Values>) => FormikState<Values>),\\n    cb?: () => void\\n  ) => void;\\n}\\n\\n/**\\n * Formik form event handlers\\n */\\n\",\n", "  'selected_text': 'export interface FormikHandlers {\\n  /** Form submit handler */\\n  handleSubmit: (e?: React.FormEvent<HTMLFormElement>) => void;\\n  /** Reset form event handler  */\\n  handleReset: (e?: React.SyntheticEvent<any>) => void;\\n  handleBlur: {\\n    /** Classic React blur handler, keyed by input name */\\n    (e: React.FocusEvent<any>): void;\\n    /** Preact-like linkState. Will return a handleBlur function. */\\n    <T = string | any>(fieldOrEvent: T): T extends string\\n      ? (e: any) => void\\n      : void;\\n  };\\n  handleChange: {\\n    /** Classic React change handler, keyed by input name */\\n    (e: React.ChangeEvent<any>): void;\\n    /** Preact-like linkState. Will return a handleChange function.  */\\n    <T = string | React.ChangeEvent<any>>(\\n      field: T\\n    ): T extends React.ChangeEvent<any>\\n      ? void\\n      : (e: string | React.ChangeEvent<any>) => void;\\n  };\\n\\n  getFieldProps: <Value = any>(\\n    props: string | FieldConfig<Value>\\n  ) => FieldInputProps<Value>;\\n  getFieldMeta: <Value>(name: string) => FieldMetaProps<Value>;\\n  getFieldHelpers: <Value = any>(name: string) => FieldHelperProps<Value>;\\n}\\n',\n", "  'suffix': \"\\n/**\\n * Base formik configuration/props shared between the HoC and Component.\\n */\\nexport interface FormikSharedConfig<Props = {}> {\\n  /** Tells Formik to validate the form on each input's onChange event */\\n  validateOnChange?: boolean;\\n  /** Tells Formik to validate the form on each input's onBlur event */\\n  validateOnBlur?: boolean;\\n  /** Tells Formik to validate upon mount */\\n  validateOnMount?: boolean;\\n  /** Tell Formik if initial form values are valid or not on first render */\\n  isInitialValid?: boolean | ((props: Props) => boolean);\\n  /** Should Formik reset the form when new initialValues change */\\n  enableReinitialize?: boolean;\\n}\\n\\n/**\\n * <Formik /> props\\n */\\nexport interface FormikConfig<Values> extends FormikSharedConfig {\\n  /**\\n   * Form component to render\\n   */\\n  component?: React.ComponentType<FormikProps<Values>>;\\n\\n  /**\\n   * Render prop (works like React router's <Route render={props =>} />)\\n   * @deprecated\\n   */\\n  render?: (props: FormikProps<Values>) => React.ReactNode;\\n\\n  /**\\n   * React children or child render callback\\n   */\\n  children?:\\n    | ((props: FormikProps<Values>) => React.ReactNode)\\n    | React.ReactNode;\\n\\n  /**\\n   * Initial values of the form\\n   */\\n  initialValues: Values;\\n\\n  /**\\n   * Initial status\\n   */\\n  initialStatus?: any;\\n\\n  /** Initial object map of field names to specific error for that field */\\n  initialErrors?: FormikErrors<Values>;\\n\\n  /** Initial object map of field names to whether the field has been touched */\\n  initialTouched?: FormikTouched<Values>;\\n\\n  /**\\n   * Reset handler\\n   */\\n  onReset?: (values: Values, formikHelpers: FormikHelpers<Values>) => void;\\n\\n  /**\\n   * Submission handler\\n   */\\n  onSubmit: (\\n    values: Values,\\n    formikHelpers: FormikHelpers<Values>\\n  ) => void | Promise<any>;\\n  /**\\n   * A Yup Schema or a function that returns a Yup schema\\n   */\\n  validationSchema?: any | (() => any);\\n\\n  /**\\n   * Validation function. Must return an error object or promise that\\n   * throws an error object where that object keys map to corresponding value.\\n   */\\n  validate?: (values: Values) => void | object | Promise<FormikErrors<Values>>;\\n\\n  /** Inner ref */\\n  innerRef?: React.Ref<FormikProps<Values>>;\\n}\\n\\n/**\\n * State, handlers, and helpers made available to form component or render prop\\n * of <Formik/>.\\n */\\nexport type FormikProps<Values> = FormikSharedConfig &\\n  FormikState<Values> &\\n  FormikHelpers<Values> &\\n  FormikHandlers &\\n  FormikComputedProps<Values> &\\n  FormikRegistration & { submitForm: () => Promise<any> };\\n\\n/** Internal Formik registration methods that get passed down as props */\\nexport interface FormikRegistration {\\n  registerField: (name: string, fns: { validate?: FieldValidator }) => void;\\n  unregisterField: (name: string) => void;\\n}\\n\\n/**\\n * State, handlers, and helpers made available to Formik's primitive components through context.\\n */\\nexport type FormikContextType<Values> = FormikProps<Values> &\\n  Pick<FormikConfig<Values>, 'validate' | 'validationSchema'>;\\n\\nexport interface SharedRenderProps<T> {\\n  /**\\n   * Field component to render. Can either be a string like 'select' or a component.\\n   */\\n  component?: keyof JSX.IntrinsicElements | React.ComponentType<T | void>;\\n\\n  /**\\n   * Render prop (works like React router's <Route render={props =>} />)\\n   */\\n  render?: (props: T) => React.ReactNode;\\n\\n  /**\\n   * Children render function <Field name>{props => ...}</Field>)\\n   */\\n  children?: (props: T) => React.ReactNode;\\n}\\n\\nexport type GenericFieldHTMLAttributes =\\n  | JSX.IntrinsicElements['input']\\n  | JSX.IntrinsicElements['select']\\n  | JSX.IntrinsicElements['textarea'];\\n\\n/** Field metadata */\\nexport interface FieldMetaProps<Value> {\\n  /** Value of the field */\\n  value: Value;\\n  /** Error message of the field */\\n  error?: string;\\n  /** Has the field been visited? */\\n  touched: boolean;\\n  /** Initial value of the field */\\n  initialValue?: Value;\\n  /** Initial touched state of the field */\\n  initialTouched: boolean;\\n  /** Initial error message of the field */\\n  initialError?: string;\\n}\\n\\n/** Imperative handles to change a field's value, error and touched */\\nexport interface FieldHelperProps<Value> {\\n  /** Set the field's value */\\n  setValue: (value: Value, shouldValidate?: boolean) => Promise<void | FormikErrors<Value>>;\\n  /** Set the field's touched value */\\n  setTouched: (value: boolean, shouldValidate?: boolean) => Promise<void | FormikErrors<Value>>;\\n  /** Set the field's error value */\\n  setError: (value: string | undefined) => void;\\n}\\n\\n/** Field input value, name, and event handlers */\\nexport interface FieldInputProps<Value> {\\n  /** Value of the field */\\n  value: Value;\\n  /** Name of the field */\\n  name: string;\\n  /** Multiple select? */\\n  multiple?: boolean;\\n  /** Is the field checked? */\\n  checked?: boolean;\\n  /** Change event handler */\\n  onChange: FormikHandlers['handleChange'];\\n  /** Blur event handler */\\n  onBlur: FormikHandlers['handleBlur'];\\n}\\n\\nexport type FieldValidator = (\\n  value: any\\n) => string | void | Promise<string | void>;\\n\",\n", "  'position': {'prefix_begin': 0, 'suffix_end': 10313},\n", "  'instruction': 'add support for NativeSyntheticEvent for react-native',\n", "  'file_count': 0},\n", " 'response': {'text': 'export interface FormikHandlers {\\n  /** Form submit handler */\\n  handleSubmit: (e?: React.FormEvent<HTMLFormElement>) => void;\\n  /** Reset form event handler  */\\n  handleReset: (e?: React.SyntheticEvent<any>) => void;\\n  handleBlur: {\\n    /** Classic React blur handler, keyed by input name */\\n    (e: React.FocusEvent<any>): void;\\n    /** Preact-like linkState. Will return a handleBlur function. */\\n    <T = string | any>(fieldOrEvent: T): T extends string\\n      ? (e: any) => void\\n      : void;\\n  };\\n  handleChange: {\\n    /** Classic React change handler, keyed by input name */\\n    (e: React.ChangeEvent<any>): void;\\n    /** Preact-like linkState. Will return a handleChange function.  */\\n    <T = string | React.ChangeEvent<any>>(\\n      field: T\\n    ): T extends React.ChangeEvent<any>\\n      ? void\\n      : (e: string | React.ChangeEvent<any>) => void;\\n  };\\n\\n  getFieldProps: <Value = any>(\\n    props: string | FieldConfig<Value>\\n  ) => FieldInputProps<Value>;\\n  getFieldMeta: <Value>(name: string) => FieldMetaProps<Value>;\\n  getFieldHelpers: <Value = any>(name: string) => FieldHelperProps<Value>;\\n}\\n'},\n", " 'user_id': '<EMAIL>'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["turing_data[8]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import * as React from 'react';\n", "import { FieldConfig } from './Field';\n", "/**\n", " * Values of fields in the form\n", " */\n", "export interface FormikValues {\n", "  [field: string]: any;\n", "}\n", "\n", "/**\n", " * An object containing error messages whose keys correspond to FormikValues.\n", " * Should always be an object of strings, but any is allowed to support i18n libraries.\n", " */\n", "export type FormikErrors<Values> = {\n", "  [K in keyof Values]?: Values[K] extends any[]\n", "    ? Values[K][number] extends object // [number] is the special sauce to get the type of array's element. More here https://github.com/Microsoft/TypeScript/pull/21316\n", "      ? FormikErrors<Values[K][number]>[] | string | string[]\n", "      : string | string[]\n", "    : Values[K] extends object\n", "    ? FormikErrors<Values[K]>\n", "    : string;\n", "};\n", "\n", "/**\n", " * An object containing touched state of the form whose keys correspond to FormikValues.\n", " */\n", "export type FormikTouched<Values> = {\n", "  [K in keyof Values]?: Values[K] extends any[]\n", "    ? Values[K][number] extends object // [number] is the special sauce to get the type of array's element. More here https://github.com/Microsoft/TypeScript/pull/21316\n", "      ? FormikTouched<Values[K][number]>[]\n", "      : boolean\n", "    : Values[K] extends object\n", "    ? FormikTouched<Values[K]>\n", "    : boolean;\n", "};\n", "\n", "/**\n", " * Formik state tree\n", " */\n", "export interface FormikState<Values> {\n", "  /** Form values */\n", "  values: Values;\n", "  /** map of field names to specific error for that field */\n", "  errors: FormikErrors<Values>;\n", "  /** map of field names to whether the field has been touched */\n", "  touched: FormikTouched<Values>;\n", "  /** whether the form is currently submitting */\n", "  isSubmitting: boolean;\n", "  /** whether the form is currently validating (prior to submission) */\n", "  isValidating: boolean;\n", "  /** Top level status state, in case you need it */\n", "  status?: any;\n", "  /** Number of times user tried to submit the form */\n", "  submitCount: number;\n", "}\n", "\n", "/**\n", " * Formik computed properties. These are read-only.\n", " */\n", "export interface FormikComputedProps<Values> {\n", "  /** True if any input has been touched. False otherwise. */\n", "  readonly dirty: boolean;\n", "  /** True if state.errors is empty */\n", "  readonly isValid: boolean;\n", "  /** The initial values of the form */\n", "  readonly initialValues: Values;\n", "  /** The initial errors of the form */\n", "  readonly initialErrors: FormikErrors<Values>;\n", "  /** The initial visited fields of the form */\n", "  readonly initialTouched: FormikTouched<Values>;\n", "  /** The initial status of the form */\n", "  readonly initialStatus?: any;\n", "}\n", "\n", "/**\n", " * Formik state helpers\n", " */\n", "export interface FormikHelpers<Values> {\n", "  /** Manually set top level status. */\n", "  setStatus: (status?: any) => void;\n", "  /** Manually set errors object */\n", "  setErrors: (errors: FormikErrors<Values>) => void;\n", "  /** Manually set isSubmitting */\n", "  setSubmitting: (isSubmitting: boolean) => void;\n", "  /** Manually set touched object */\n", "  setTouched: (\n", "    touched: <PERSON><PERSON><PERSON>ouched<Values>,\n", "    shouldValidate?: boolean\n", "  ) => Promise<void | FormikErrors<Values>>;\n", "  /** Manually set values object  */\n", "  setValues: (\n", "    values: React.SetStateAction<Values>,\n", "    shouldValidate?: boolean\n", "  ) => Promise<void | FormikErrors<Values>>;\n", "  /** Set value of form field directly */\n", "  setFieldValue: (\n", "    field: string,\n", "    value: any,\n", "    shouldValidate?: boolean\n", "  ) => Promise<void | FormikErrors<Values>>;\n", "  /** Set error message of a form field directly */\n", "  setFieldError: (field: string, message: string | undefined) => void;\n", "  /** Set whether field has been touched directly */\n", "  setFieldTouched: (\n", "    field: string,\n", "    isTouched?: boolean,\n", "    shouldValidate?: boolean\n", "  ) =>  Promise<void | FormikErrors<Values>>;\n", "  /** Validate form values */\n", "  validateForm: (values?: any) => Promise<FormikErrors<Values>>;\n", "  /** Validate field value */\n", "  validateField: (field: string) => Promise<void> | Promise<string | undefined>;\n", "  /** Reset form */\n", "  resetForm: (nextState?: Partial<FormikState<Values>>) => void;\n", "  /** Submit the form imperatively */\n", "  submitForm: () => Promise<void>;\n", "  /** Set <PERSON> state, careful! */\n", "  setFormikState: (\n", "    f:\n", "      | FormikState<Values>\n", "      | ((prevState: FormikState<Values>) => FormikState<Values>),\n", "    cb?: () => void\n", "  ) => void;\n", "}\n", "\n", "/**\n", " * Formik form event handlers\n", " */\n", "\n"]}], "source": ["print(turing_data[8][\"request\"][\"prefix\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Confirm:\n", "0 - confirm? Don't really get the instruction? Sounds just like \"Generate new static method\" ?\n", "1 - confirm? I don't understand the word \"duplicated\"\n", "3 - confirm? Instruction's too vague\n", "24 - confirm?\n", "44 - ?\n", "58 - ?\n", "Minor problems:\n", "4 - additional space appears after scopeYoutubeReadOnly\n", "8 - topmost comment in annotated code is truncated\n", "11 - maybe not the best usacese, but creative sample!\n", "14 - broken formatting\n", " 17 - removed new line\n", "19 - formatting\n", "21 - Why it rewrote the if (component)...\n", "25 - ???\n", "31 - same as 11\n", "32 - some additional changes after const (...\n", "39 - Printf vs println\n", "40 - too complex?\n", "50 - wtf? just super long suffix out of nowhere\n", "53 - like 11, but a bit strange\n", "67 - sounds like an unrequired change\n", "69 - ???\n", "72 - formatting's not great\n", "73 - too large?\n", "74 - wtf????\n", "81 - missing line\n", "82 - unrequired change again???"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
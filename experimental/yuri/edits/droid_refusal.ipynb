{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from pathlib import Path\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# We're using samples that were filtered out from the main droid data\n", "DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/droid-repo-47-filtering-run-v1/bad\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["data = []\n", "for file in DATA_PATH.glob(\"*.json\"):\n", "    with file.open() as f:\n", "        data.append(json.load(f))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["3205"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT = \"\"\"\n", "Here is a hunk of code:\n", "```\n", "{code}\n", "```\n", "\n", "Your task is to generate such an instruction that is NOT applicable to this code.\n", "For example:\n", "1. If code doesn't contain a function named \"foo\", then you can generate \"rename function foo to bar\". \n", "2. If code contains only single quotes and no double quotes, you can generate \"replace double quotes with single quotes\".\n", "3. If code contains no comments, you can generate \"remove all comments\".\n", "\n", "And after generating such an instruction, you also have to shortly explain why it is NOT applicable to the provided code.\n", "If the instruction is technically applicable to the provided code (i.e. it's applicable, but leads to syntax/logic/runtime errors), consider it's still applicable, and do not generate such instructions.\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze the provided hunk of code.\n", "2. Describe your thinking process on how to generate required instruction.\n", "3. Explicitly write the generated instruction and short explanation why it is NOT applicable to the provided code.\n", "\"\"\"\n", "\n", "\n", "EXTRACT_PROMPT = \"\"\"Return the result as JSON with 2 keys:\n", "- instruction\n", "- explanation\n", "\"\"\"\n", "\n", "PROMPT2 = \"\"\"\n", "Here is an explanation of why this instruction ('{instruction}') is not applicable to some source code:\n", "'{explanation}'.\n", "\n", "Please generate short (1 sentence) version of this explanation.\n", "And if explanation refers to the source code, please use the term 'selected code'.\n", "And don't include some preamble like \"The instruction is not applicable because\". \n", "\"\"\"\n", "\n", "EXTRACT_PROMPT2 = \"\"\"Return the result as JSON with 1 key:\n", "- explanation\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_not_applicable_instruction(sample, gpt):\n", "    prompt = PROMPT.format(\n", "        code=sample[\"new_middle\"],\n", "    )\n", "    \n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "\n", "    messages.append({\"role\": \"assistant\", \"content\": gpt4_response})\n", "    messages.append({\"role\": \"user\", \"content\": EXTRACT_PROMPT})\n", "    extracted_response = gpt(messages, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0)\n", "\n", "    prompt2 = PROMPT2.format(\n", "        instruction=extracted_response[\"instruction\"],\n", "        explanation=extracted_response[\"explanation\"],\n", "    )\n", "    messages2 = [{\"role\": \"user\", \"content\": prompt2}]\n", "    gpt4_response2 = gpt(messages2, model=\"gpt-4-1106-preview\", temperature=0)\n", "\n", "    messages2.append({\"role\": \"assistant\", \"content\": gpt4_response2})\n", "    messages2.append({\"role\": \"user\", \"content\": EXTRACT_PROMPT2})\n", "    extracted_response2 = gpt(messages2, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0)\n", "\n", "    return {\n", "        \"short_explanation\": extracted_response2[\"explanation\"],\n", "        **extracted_response,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out = generate_not_applicable_instruction(sample, gpt)\n", "sample[\"refusal_generation\"] = out\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data[0]['new_middle'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["loaded_samples[0]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2000/2000 [00:00<00:00, 522264.23it/s]\n"]}], "source": ["from tqdm import tqdm\n", "\n", "GEN_DIR = Path(\"/mnt/efs/augment/user/yuri/data/droid_refusal_mar12_v2/\")\n", "loaded_samples = []\n", "for f_name in (GEN_DIR / \"good\").glob(\"*.json\"):\n", "    with f_name.open() as f:\n", "        loaded_samples.append(json.load(f))\n", "print(len(loaded_samples))\n", "\n", "MAIN_HTML = \"\"\n", "for sample in tqdm(loaded_samples):\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h4>Instruction:</h4> <pre>{sample['refusal_generation']['instruction']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Explanation why instruction is bad:</h4> <pre>{sample['refusal_generation']['short_explanation']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Code:</h4> <pre>{sample['new_middle']}</pre>\"\n", "\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_droid_refusal_mar12_2k_v2.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
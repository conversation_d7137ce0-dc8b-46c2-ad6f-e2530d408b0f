{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import os\n", "import numpy as np\n", "\n", "from openai import OpenAI\n", "from rouge_score import rouge_scorer \n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["SEED_INSTRUCTIONS_PATH = '/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/generic_instructions_v1.json'\n", "NUM_TOTAL_EXAMPLES_IN_PROMPT = 7  # Number of in-context examples\n", "NUM_GENERATED_INSTRUCTIONS_IN_PROMPT = 1  # Out of NUM_TOTAL_EXAMPLES_IN_PROMPT, how much instructions are generated (not from seed)\n", "\n", "NUM_INSTRUCTIONS_TO_GENERATE = 100  # Total amount of instructions we want to generate\n", "INSTRUCTIONS_PER_REQUEST = 20  # How many instructions to generate per GPT request\n", "SAVE_DIR_PATH = Path('/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/generation_jan2')\n", "\n", "\n", "os.environ['OPENAI_API_KEY'] = \"\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def make_prompt(examples):\n", "    prompt = f\"\"\"\n", "Given the existing instructions, please generate a list of {INSTRUCTIONS_PER_REQUEST} diverse python code editing instructions.\n", "The new instructions should address diverse editing tasks. \n", "Please ensure that instructions are clear, diverse.\n", "\n", "Here are examples:\n", "\"\"\"\n", "    assert len(examples) == NUM_TOTAL_EXAMPLES_IN_PROMPT\n", "    for e in examples:\n", "        prompt += f\"- {e}\\n\"\n", "    \n", "    prompt += f\"\"\"\n", "Please, provide a response in a JSON format. JSON should have one entry called `new_instructions` with a list of {INSTRUCTIONS_PER_REQUEST} new instructions.\n", "Like this {{\"new_instructions\": [\"instruction1\", \"instruction2\", ...]}}\n", "\"\"\"\n", "    return prompt\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["client = OpenAI()\n", "scorer = rouge_scorer.RougeScorer([\"rougeL\"], use_stemmer=False)\n", "SAVE_DIR_PATH.mkdir(exist_ok=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of seed instructions: 28\n"]}, {"data": {"text/plain": ["['replace a part of the code with a language-specific placeholder such as `// TODO` or `throw new NotImplementedException();` or `...`',\n", " 'remove something important from the code',\n", " 'replace a function name in the code with a made-up name',\n", " 'replace a type name in the code with a made-up name',\n", " 'mess up the logic',\n", " 'remove comments',\n", " 'change code structure',\n", " 'replace a syntactic structure with equivalent',\n", " 'swap the order of operations',\n", " 'alter variable names to be misleading or non-descriptive',\n", " 'change the scope of a variable or function',\n", " 'insert an unnecessary or redundant piece of code',\n", " 'replace literal value or values',\n", " 'modify error handling to ignore or improperly handle errors',\n", " 'alter the data types in a way that causes type mismatches or errors',\n", " 'introduce typos',\n", " 'introduce an inconsistency',\n", " 'make a mistake',\n", " 'mess up the formatting',\n", " 'change the formatting',\n", " 'delete function body',\n", " 'remove a code path',\n", " 'remove a conditional branch',\n", " 'replace a logical chunk of code with TODO comments',\n", " 'replace a logical chunk of code with commented-out pseudocode',\n", " 'replace chunks of code with TODOs',\n", " 'make a syntactic change',\n", " 'change representation of some literals']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(SEED_INSTRUCTIONS_PATH, \"r\") as f:\n", "    seed_instructions = json.load(f)\n", "\n", "print(f\"Total number of seed instructions: {len(seed_instructions)}\")\n", "seed_instructions"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current generated examples: []\n", "Current seed examples: ['modify error handling to ignore or improperly handle errors', 'replace a logical chunk of code with TODO comments', 'replace literal value or values', 'change representation of some literals', 'replace a function name in the code with a made-up name', 'change the scope of a variable or function', 'delete function body']\n", "Current prompt:\n", "\n", "Given the existing instructions, please generate a list of 20 diverse python code editing instructions.\n", "The new instructions should address diverse editing tasks. \n", "Please ensure that instructions are clear, diverse.\n", "\n", "Here are examples:\n", "- replace a function name in the code with a made-up name\n", "- delete function body\n", "- modify error handling to ignore or improperly handle errors\n", "- replace literal value or values\n", "- change representation of some literals\n", "- change the scope of a variable or function\n", "- replace a logical chunk of code with TODO comments\n", "\n", "Please, provide a response in a JSON format. JSON should have one entry called `new_instructions` with a list of 20 new instructions.\n", "Like this {\"new_instructions\": [\"instruction1\", \"instruction2\", ...]}\n", "\n", "\n", "Response:\n", "{'new_instructions': [\"Convert all integers in the code to floats by adding '.0'.\", \"Change all string concatenations using '+' to use f-strings instead.\", \"Find and replace variable names containing 'temp' with names that include 'temporary'.\", \"Replace all instances of the 'print' function with a custom logging function called 'log_message'.\", \"Insert a comment above each 'if' statement explaining the condition being checked.\", 'Switch the order of arguments in all functions that have two or more parameters.', \"For every 'for' loop, add a print statement at the end of the loop body that prints 'Loop iteration complete'.\", \"Rename all classes to begin with the word 'My', e.g., 'User' should become 'MyUser'.\", \"Find all 'while' loops and convert them into equivalent 'for' loops.\", 'Encapsulate any blocks of code that appear more than once in functions or methods.', 'Change all mutable default arguments in functions to use immutable types.', \"Find all usages of the modulo operation (%) and replace them with a call to a function named 'mod_operation'.\", \"Locate all 'try-except' blocks and add an 'else' clause that prints 'No exception occurred'.\", 'Reverse the order of items in all lists that are being assigned to variables statically (e.g., my_list = [1, 2, 3] should become my_list = [3, 2, 1]).', 'Remove any unused import statements found at the top of the code files.', \"For each 'return' statement, add a comment on the preceding line describing what is being returned.\", \"In all conditional statements, replace '==' with 'is' where appropriate for comparing with None, True, or False.\", \"Wrap any floating-point division operations in a function called 'safe_division' that includes handling for division by zero.\", 'Replace all tuples with lists, ensuring that the mutability of the data structure is properly handled.', 'Identify any global variables and refactor the code to pass them as parameters to the functions that use them.']}\n", "\n", "Step: 1 | Generated 20 new instructions | Current number of generated instructions: 20 | Current number of failed generations: 0\n", "Current generated examples: ['Encapsulate any blocks of code that appear more than once in functions or methods.']\n", "Current seed examples: ['change code structure', 'replace a part of the code with a language-specific placeholder such as `// TODO` or `throw new NotImplementedException();` or `...`', 'change the scope of a variable or function', 'replace a type name in the code with a made-up name', 'mess up the formatting', 'change the formatting']\n", "Current prompt:\n", "\n", "Given the existing instructions, please generate a list of 20 diverse python code editing instructions.\n", "The new instructions should address diverse editing tasks. \n", "Please ensure that instructions are clear, diverse.\n", "\n", "Here are examples:\n", "- mess up the formatting\n", "- replace a part of the code with a language-specific placeholder such as `// TODO` or `throw new NotImplementedException();` or `...`\n", "- Encapsulate any blocks of code that appear more than once in functions or methods.\n", "- change the formatting\n", "- change code structure\n", "- change the scope of a variable or function\n", "- replace a type name in the code with a made-up name\n", "\n", "Please, provide a response in a JSON format. JSON should have one entry called `new_instructions` with a list of 20 new instructions.\n", "Like this {\"new_instructions\": [\"instruction1\", \"instruction2\", ...]}\n", "\n", "\n", "Response:\n", "{'new_instructions': ['Introduce a type hint to all function arguments and return values.', 'Replace all string concatenation with f-string formatting.', 'Refactor any for-loops to use list comprehensions where appropriate.', 'Convert all class names to follow CamelCase naming conventions.', 'Implement Error handling by adding try-except blocks around code that may throw exceptions.', 'Standardize all function documentation to NumPy style docstrings.', 'Inline any variable that is only used once within the scope of a function.', 'Remove any unused imports at the beginning of the file.', 'Replace any magic numbers with named constants to improve readability.', 'Rename all variables and functions to be more descriptive and follow snake_case naming conventions.', 'Extract hard-coded file paths or URLs into a separate configuration file or environment variables.', 'Surround any calls to external services or APIs with a timeout mechanism.', 'Convert any tuples used for data storage into namedtuples or dataclasses for clarity.', 'Add a logging mechanism to functions that perform significant logic or data manipulation.', 'Convert any global variables into function parameters to avoid side effects.', 'Remove any recursive functions and replace them with iterative solutions.', 'Replace any deprecated library functions or modules with their modern equivalents.', 'Organize all functions into classes where appropriate to encapsulate related functionality.', 'Add unit tests for any function that contains complex logic to ensure reliability.', 'Refactor any nested if-else blocks to use guard clauses or early returns to decrease indentation levels.']}\n", "\n", "Step: 2 | Generated 20 new instructions | Current number of generated instructions: 40 | Current number of failed generations: 0\n", "Current generated examples: ['Inline any variable that is only used once within the scope of a function.']\n", "Current seed examples: ['change the scope of a variable or function', 'replace a logical chunk of code with TODO comments', 'make a mistake', 'mess up the logic', 'remove something important from the code', 'alter variable names to be misleading or non-descriptive']\n", "Current prompt:\n", "\n", "Given the existing instructions, please generate a list of 20 diverse python code editing instructions.\n", "The new instructions should address diverse editing tasks. \n", "Please ensure that instructions are clear, diverse.\n", "\n", "Here are examples:\n", "- mess up the logic\n", "- replace a logical chunk of code with TODO comments\n", "- make a mistake\n", "- remove something important from the code\n", "- change the scope of a variable or function\n", "- alter variable names to be misleading or non-descriptive\n", "- Inline any variable that is only used once within the scope of a function.\n", "\n", "Please, provide a response in a JSON format. JSON should have one entry called `new_instructions` with a list of 20 new instructions.\n", "Like this {\"new_instructions\": [\"instruction1\", \"instruction2\", ...]}\n", "\n", "\n", "Response:\n", "{'new_instructions': ['Switch the positions of two unrelated blocks of code without adjusting their functionality.', 'Add a redundant loop that iterates once around a section of code that does not need it.', 'Wrap an entire function body in a try-except block, catching a generic Exception without any further handling.', 'Introduce a new function that duplicates the behavior of an existing one but with a different name.', 'Comment out all function calls to a particular function, effectively disabling its use.', 'Replace all instances of a specific string with another unrelated string.', 'Randomly reorder the parameters in several function definitions and their corresponding calls.', 'Remove any type hints present in the function definitions and variable declarations.', 'Rename a function to something that suggests the opposite of what it actually does.', \"Introduce an unused global variable with a vague name such as 'data' or 'result'.\", \"Change the iteration variable in loops from 'i' or 'j' to commonly used variable names like 'length' or 'size'.\", 'Wrap the code in a main function but forget to call it at the end of the script.', 'Create a new class that is a carbon copy of an existing class but does not inherit from it.', \"Reverse the conditions in all if-else statements (e.g., change 'if condition' to 'if not condition').\", 'Introduce an artificial delay (e.g., sleep) inside frequently called functions.', \"Replace numerical constants with their equivalent mathematical expressions (e.g., replace '2' with '1+1').\", 'Insert a print statement in a recursion function to output an unrelated message with each call.', 'Modify a list comprehension to include an irrelevant conditional that filters out valid data.', \"Replace every instance of the division operator '/' with floor division '//'.\", 'Extract strings from print statements into variables that are inappropriately named.']}\n", "\n", "Step: 3 | Generated 20 new instructions | Current number of generated instructions: 60 | Current number of failed generations: 0\n", "Current generated examples: ['Remove any unused import statements found at the top of the code files.']\n", "Current seed examples: ['remove a code path', 'replace a logical chunk of code with TODO comments', 'change the scope of a variable or function', 'insert an unnecessary or redundant piece of code', 'modify error handling to ignore or improperly handle errors', 'mess up the formatting']\n", "Current prompt:\n", "\n", "Given the existing instructions, please generate a list of 20 diverse python code editing instructions.\n", "The new instructions should address diverse editing tasks. \n", "Please ensure that instructions are clear, diverse.\n", "\n", "Here are examples:\n", "- insert an unnecessary or redundant piece of code\n", "- remove a code path\n", "- change the scope of a variable or function\n", "- mess up the formatting\n", "- modify error handling to ignore or improperly handle errors\n", "- Remove any unused import statements found at the top of the code files.\n", "- replace a logical chunk of code with TODO comments\n", "\n", "Please, provide a response in a JSON format. JSON should have one entry called `new_instructions` with a list of 20 new instructions.\n", "Like this {\"new_instructions\": [\"instruction1\", \"instruction2\", ...]}\n", "\n", "\n", "Response:\n", "{'new_instructions': ['Add type hints to function definitions that currently lack them.', 'Find and rename any variables with names shorter than three characters to more descriptive names.', 'Convert all string formatting to f-strings, if not already in use.', 'Identify any recursive functions and add a comment explaining the base case.', 'Wrap any naked print statements within a function, to allow for logging suppression.', '<PERSON><PERSON><PERSON> deeply nested if-else conditions into separate functions for clarity.', 'Change class method names to follow the snake_case naming convention.', 'Reverse the order of parameters in a function, and update all calls to it accordingly.', 'Comment out blocks of code that handle specific edge cases.', 'Find all instances of hard-coded values and extract them as constants at the top of the file.', 'Identify global variables and encapsulate them within a singleton class.', 'Insert a deliberate off-by-one error in loop iterations.', 'Replace list comprehensions with equivalent for loops.', \"Change equality checks using '==' with 'is' for singletons like None.\", 'Find and replace any tab characters with four spaces for indentation.', 'Swap the usage of single and double quotes for string literals in the code.', 'Break down any functions longer than 50 lines into smaller components.', 'Combine any adjacent string literals in the code into single literals.', 'Add an extra return statement at the beginning of functions to return None early.', 'Scramble the order of cases in switch statements or if-elif chains.']}\n", "\n", "Step: 4 | Generated 20 new instructions | Current number of generated instructions: 80 | Current number of failed generations: 0\n", "Current generated examples: ['Introduce a new function that duplicates the behavior of an existing one but with a different name.']\n", "Current seed examples: ['change representation of some literals', 'replace a type name in the code with a made-up name', 'insert an unnecessary or redundant piece of code', 'alter variable names to be misleading or non-descriptive', 'remove something important from the code', 'alter the data types in a way that causes type mismatches or errors']\n", "Current prompt:\n", "\n", "Given the existing instructions, please generate a list of 20 diverse python code editing instructions.\n", "The new instructions should address diverse editing tasks. \n", "Please ensure that instructions are clear, diverse.\n", "\n", "Here are examples:\n", "- insert an unnecessary or redundant piece of code\n", "- change representation of some literals\n", "- Introduce a new function that duplicates the behavior of an existing one but with a different name.\n", "- remove something important from the code\n", "- alter variable names to be misleading or non-descriptive\n", "- alter the data types in a way that causes type mismatches or errors\n", "- replace a type name in the code with a made-up name\n", "\n", "Please, provide a response in a JSON format. JSON should have one entry called `new_instructions` with a list of 20 new instructions.\n", "Like this {\"new_instructions\": [\"instruction1\", \"instruction2\", ...]}\n", "\n", "\n", "Response:\n", "{'new_instructions': ['Convert all string concatenations to f-string formatting.', 'Change all list comprehensions to equivalent for-loops.', 'Randomize the order of parameters in function signatures where possible.', 'Replace all instances of integer division (//) with float division (/) and vice versa.', 'Switch the usage of single and double quotes for all string literals.', 'Convert all for-loops into while-loops that perform the same operation.', 'Introduce global variables to replace local variables within functions.', \"Rename all functions to have the prefix 'custom_' regardless of their purpose.\", \"Replace all instances of the 'print' function with a custom logging function that has not been defined.\", 'Replace all boolean literals (True, False) with integer equivalents (1, 0).', 'Wrap the entire code block within an unnecessary try-except block without specifying an exception type.', \"Move the definition of a function that's used only once into the place where it's called as a nested function.\", 'Replace all mutable default arguments in functions (e.g., lists) with immutable ones (e.g., tuples).', 'Implement a new class that inherits from a built-in type but overrides none of the methods or attributes.', \"Add a docstring to every function that consists solely of 'TODO: Write a docstring'.\", 'Create a decorator that logs function calls but is never applied to any function.', \"Use 'lambda' functions to replace all single-statement functions.\", \"Swap the usage of '+' and '-' operators in arithmetic expressions.\", 'Introduce a variable that tracks the count of function calls but is never read or used.', 'Wrap all code in a main function with the \\'if __name__ == \"__main__\"\\' guard but do not call it.']}\n", "\n", "Step: 5 | Generated 20 new instructions | Current number of generated instructions: 100 | Current number of failed generations: 0\n"]}], "source": ["generated_instructions = []\n", "num_failed = 0\n", "step = 0\n", "\n", "fout_ind = open(SAVE_DIR_PATH / \"indent.jsonl\", 'a')\n", "fout_raw = open(SAVE_DIR_PATH / \"raw.jsonl\", 'a')\n", "\n", "while len(generated_instructions) < NUM_INSTRUCTIONS_TO_GENERATE:\n", "    try:\n", "        step += 1\n", "        if num_failed > 5:\n", "            print(\"Stopping due to 5 consecutive failed generations\")\n", "            break\n", "        generated_examples = random.sample(generated_instructions, min(len(generated_instructions), NUM_GENERATED_INSTRUCTIONS_IN_PROMPT))\n", "        seed_examples = random.sample(seed_instructions, NUM_TOTAL_EXAMPLES_IN_PROMPT - len(generated_examples))\n", "        print(f\"Current generated examples: {generated_examples}\")\n", "        print(f\"Current seed examples: {seed_examples}\")\n", "        cur_examples = seed_examples + generated_examples\n", "        random.shuffle(cur_examples)\n", "\n", "        prompt = make_prompt(cur_examples)\n", "        print(f\"Current prompt:\\n{prompt}\\n\")\n", "        \n", "        response = client.chat.completions.create(\n", "            model=\"gpt-4-1106-preview\",\n", "            response_format={ \"type\": \"json_object\" },\n", "            messages=[\n", "                {\"role\": \"user\", \"content\": prompt}\n", "            ]\n", "        )\n", "\n", "        response = json.loads(response.choices[0].message.content)\n", "        print(f\"Response:\\n{response}\\n\")\n", "\n", "        assert set(response.keys()) == set([\"new_instructions\"]), f\"Response must contain a single 'new_instructions' key, but it has: {response.keys()}\"\n", "        assert len(response[\"new_instructions\"]) == INSTRUCTIONS_PER_REQUEST, f\"Length of new_instructions must be 5, but it is {len(response['new_instructions'])}\"\n", "        for new_instr in response[\"new_instructions\"]:\n", "            assert isinstance(new_instr, str), f\"new_instructions must be a list of strings, but it contains {type(new_instr)}\"\n", "\n", "        all_instructions = seed_instructions + generated_instructions\n", "\n", "        num_new_instructions = 0\n", "        for new_instr in response[\"new_instructions\"]:\n", "            rouge_scores = [*map(lambda i: scorer.score(new_instr, i)[\"rougeL\"].fmeasure, all_instructions)]\n", "            if max(rouge_scores) > 0.8:\n", "                continue\n", "            num_new_instructions += 1\n", "            most_similar = [(all_instructions[i], rouge_scores[i]) for i in np.argsort(rouge_scores)[-3:][::-1]]\n", "            log_dict = {\n", "                \"instruction\": new_instr,\n", "                \"most_similar\": most_similar,\n", "                \"avg_similarity_score\": np.mean(rouge_scores)\n", "            }\n", "            fout_ind.write(json.dumps(log_dict, indent=4) + \"\\n\")\n", "            fout_raw.write(json.dumps(log_dict) + \"\\n\")\n", "            generated_instructions.append(new_instr)\n", "            num_failed = 0  # We consider a run as \"non-failed\" only at least one instruction is successfully added \n", "        \n", "        print(f\"Step: {step} | Generated {num_new_instructions} new instructions | Current number of generated instructions: {len(generated_instructions)} | Current number of failed generations: {num_failed}\")\n", "\n", "        fout_ind.flush()\n", "        fout_raw.flush()\n", "    \n", "    except AssertionError as e:\n", "        print(f\"Failed | Step: {step} | Error : {e}\")\n", "        num_failed += 1\n", "    except:\n", "        fout_ind.close()\n", "        fout_raw.close()\n", "        raise\n", "if not fout_ind.closed:\n", "    fout_ind.close()\n", "if not fout_raw.closed:\n", "    fout_raw.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Combine instructions"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["combined_instructions = []\n", "\n", "with open(SAVE_DIR_PATH / \"raw.jsonl\") as f:\n", "    for line in f:\n", "        instr = json.loads(line)[\"instruction\"]\n", "        combined_instructions.append(instr)\n", "        \n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["(120,\n", " 'change the scope of a variable from local to global, potentially leading to unexpected behaviors')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["len(combined_instructions), combined_instructions[0]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["with open(SAVE_DIR_PATH / \"generated_instructions.json\", \"w\") as f:\n", "    json.dump(combined_instructions, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from pathlib import Path\n", "from openai import OpenAI\n", "from tqdm import tqdm\n", "from sklearn.manifold import TSNE\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_embedding(text, model=\"text-embedding-ada-002\"):\n", "   text = text.replace(\"\\n\", \" \")\n", "   return client.embeddings.create(input = [text], model=model).data[0].embedding\n", "\n", "def get_direction(code_old, code_new):\n", "    emb_new = np.array(get_embedding(code_new))\n", "    emb_old = np.array(get_embedding(code_old))\n", "\n", "    direction_raw = (emb_new - emb_old)\n", "    direction = direction_raw / np.linalg.norm(direction_raw)\n", "\n", "    return np.array(direction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/mnt/efs/augment/user/igor/nlp/dataset1/prompts.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(DATA_PATH) as f:\n", "    data = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(data), len(data), data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["J = 0\n", "\n", "code_new = data[J][\"prefix\"] + data[J][\"new_middle\"] + data[J][\"suffix\"]\n", "code_old = data[J][\"prefix\"] + data[J][\"old_middle\"] + data[J][\"suffix\"]\n", "\n", "direction = get_direction(code_old, code_new)\n", "print(direction.shape)\n", "print(direction)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["direction_matrix = []\n", "\n", "for i in tqdm(range(100)):\n", "    code_new = data[i][\"prefix\"] + data[i][\"new_middle\"] + data[i][\"suffix\"]\n", "    code_old = data[i][\"prefix\"] + data[i][\"old_middle\"] + data[i][\"suffix\"]\n", "    cur_direction = get_direction(code_old, code_new)\n", "    direction_matrix.append(cur_direction)\n", "\n", "direction_matrix = np.array(direction_matrix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["direction_matrix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tsne = TSNE(n_components=2, perplexity=15, random_state=42, init='random', learning_rate=200)\n", "vis_dims = tsne.fit_transform(direction_matrix)\n", "vis_dims.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vis_dims.shape == (100, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_2d(matrix):\n", "    assert matrix.shape[1] == 2\n", "    assert len(matrix.shape) == 2\n", "\n", "    matrix = matrix[(-200 < matrix[:, 0]) & (matrix[:, 0] < 500)]\n", "\n", "    x = matrix[:, 0]\n", "    y = matrix[:, 1]\n", "\n", "    plt.scatter(x, y, alpha=0.5)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "plot_2d(vis_dims)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["direction_matrix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target_idx = 0\n", "target_emb = direction_matrix[target_idx]\n", "similarities = []\n", "for i in tqdm(range(direction_matrix.shape[0])):\n", "    cur_emb = direction_matrix[i]\n", "    if i == target_idx:\n", "        similarity = -1e9\n", "    else:\n", "        # similarity = np.dot(target_emb, cur_emb)\n", "        similarity = np.linalg.norm(target_emb - cur_emb)\n", "    similarities.append(similarity)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["similarities_sorted = np.argsort(similarities)[::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["similarities_sorted"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data[target_idx][\"instruction\"])\n", "\n", "print(data[target_idx][\"old_middle\"])\n", "print(\"#\" * 20)\n", "print(data[target_idx][\"new_middle\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["JJ = 4\n", "print(data[JJ][\"instruction\"])\n", "\n", "print(data[JJ][\"old_middle\"])\n", "print(\"#\" * 20)\n", "print(data[JJ][\"new_middle\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
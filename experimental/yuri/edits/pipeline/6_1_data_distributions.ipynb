{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "\n", "from pathlib import Path\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_distribution(data, bins=50, title=\"\", cap=1e9):\n", "    print(f\"Size of data: {len(data)}\")\n", "    data = [*filter(lambda v: v < cap, data)]\n", "    print(f\"Size of capped data: {len(data)}\")\n", "    title += f\"(capped by {cap})\"\n", "\n", "    plt.figure(figsize=(10, 6))\n", "    plt.hist(data, bins=bins, alpha=0.7, color='blue', edgecolor='black')\n", "    plt.title(title)\n", "    plt.grid(axis='y', alpha=0.75)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/mnt/efs/augment/user/igor/data/droid/droid-repo-05/prompts.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(DATA_PATH) as f:\n", "    data = json.load(f)\n", "\n", "print(len(data))\n", "\n", "data[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prefix_sizes = [*map(lambda s: len(s['prefix'].splitlines(keepends=True)), data)]\n", "\n", "plot_distribution(prefix_sizes, bins=50, title=\"Number of lines in prefix\", cap=200)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["suffix_sizes = [*map(lambda s: len(s['suffix'].splitlines(keepends=True)), data)]\n", "\n", "plot_distribution(suffix_sizes, bins=50, title=\"Number of lines in suffix\", cap=200)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_middle_sizes = [*map(lambda s: len(s['old_middle'].splitlines(keepends=True)), data)]\n", "\n", "plot_distribution(old_middle_sizes, bins=50, title=\"Number of lines in old_middle\", cap=50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_middle_sizes = [*map(lambda s: len(s['new_middle'].splitlines(keepends=True)), data)]\n", "\n", "plot_distribution(new_middle_sizes, bins=50, title=\"Number of lines in new_middle\", cap=50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def vis_response(path):\n", "    with open(path) as f:\n", "        sample = json.load(f)\n", "\n", "    print(sample[\"prefix\"])\n", "    print(\"<<<<<< (input)\")\n", "    print(sample[\"code\"])\n", "    print(\"====== (output)\")\n", "    print(sample[\"parsed_result\"])\n", "    print(\"====== (gt)\")\n", "    print(sample[\"gt\"])\n", "    print(\">>>>>>\")\n", "    print(sample[\"suffix\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vis_response(\"/home/<USER>/repos/augment/research/eval/edit/to_vis/FT_0_prefix_suffix/0001-0078.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vis_response(\"/home/<USER>/repos/augment/research/eval/edit/to_vis/FT_25_prefix_suffix/0001-0078.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vis_response(\"/home/<USER>/repos/augment/research/eval/edit/to_vis/DS_Instruct_25_prefix_suffix/0001-0078.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["/home/<USER>/repos/augment/research/eval/edit/to_vis/DS_Instruct_25_prefix_suffix/0001-0078.json"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "import json\n", "\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_DIR = Path(\"/mnt/efs/augment/user/yuri/data/remove_comment.2024-01-10_04-43-05-filtered\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_diff_html(result, name):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "    selected_code = result[\"old_code\"]\n", "    updated_code = result[\"new_code\"]\n", "\n", "    while selected_code.startswith('\\n'):\n", "        selected_code = selected_code[1:]\n", "\n", "    while updated_code.startswith('\\n'):\n", "        updated_code = updated_code[1:]\n", "\n", "    diff_html = diff_obj.make_file(\n", "        selected_code.splitlines(),\n", "        updated_code.splitlines()\n", "    )\n", "\n", "    instructions_html = \"\"\n", "    for inst in result[\"instructions\"]:\n", "        instructions_html += f\"<li>{inst}</li>\"\n", "    instructions_html = \"<ul class=\\\"instructions\\\">\" + instructions_html + \"</ul>\"\n", "\n", "    html = f\"\"\"\n", "\n", "    <h4>Instructions:</h4>\n", "    <h4>{instructions_html}</h4>\n", "\n", "    <h4>Filtering feedback:</h4>\n", "    <h4>{result[\"filtering_result\"][\"feedback\"]}</h4>\n", "    \n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    \n", "    return html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = f\"\"\"\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cur_dir = \"bad\"  # DON'T FORGET TO SET THIS CORRECTLY!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["COMPLETE_HTML = \"\"\n", "\n", "all_files = [*(MAIN_DIR / cur_dir).glob(\"*.json\")]\n", "\n", "for name in all_files:\n", "    COMPLETE_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    COMPLETE_HTML += f\"<h2>Code sample {name}</h2><hr>\"\n", "\n", "    cur_path = MAIN_DIR / cur_dir / name    \n", "    with open(cur_path) as f:\n", "        cur_result = json.load(f)\n", "\n", "    cur_diff = get_diff_html(cur_result, \"\")\n", "    cur_diff = f\"{cur_diff}<hr>\"\n", "    COMPLETE_HTML += cur_diff\n", "\n", "RESULTING_HTML = HTML_START + COMPLETE_HTML + HTML_END\n", "print(RESULTING_HTML)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('/mnt/efs/augment/user/yuri/server_share/8001/bad_comment_removal.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
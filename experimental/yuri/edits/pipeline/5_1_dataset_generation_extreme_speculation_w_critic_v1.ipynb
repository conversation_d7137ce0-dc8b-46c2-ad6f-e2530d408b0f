{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import copy\n", "import difflib\n", "import pickle\n", "\n", "from termcolor import colored\n", "from research.data.synthetic_code_edit import seed_lib\n", "from research.data.synthetic_code_edit.types import CodeEditData, Instruction\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from openai import OpenAI\n", "from research.core.utils_for_str import get_first_n_lines, get_last_n_lines\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "\n", "os.environ['OPENAI_API_KEY'] = \"\"\n", "\n", "client = OpenAI()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def render_code_edit_sample_with_diff_patch(sample: CodeEditData):\n", "    diff = difflib.unified_diff(sample.selected_code.splitlines(True), sample.updated_code.splitlines(True))\n", "    diff = ''.join(diff)\n", "\n", "    text = colored(f\"Selected code:\\n\", color=\"light_green\")\n", "    text += f\"{sample.selected_code}\\n\"\n", "    text += colored(\"#\" * 30 + '\\n', color=\"light_blue\")\n", "    text += colored(f\"Instructions(↓):\\n\", color='light_green')\n", "    for i, instr in enumerate(sample.instructions):\n", "        text += f\"\\t{i + 1}. {instr}\"\n", "    text += '\\n'\n", "    text += colored(f\"Inverse instructions(↑):\\n\", color='light_green')\n", "    for i, instr in enumerate(sample.inverse_instructions):\n", "        text += f\"\\t{i + 1}. {instr}\"\n", "    text += '\\n'\n", "    text += colored(\"#\" * 30 + '\\n', color=\"light_blue\")\n", "    text += colored(f\"Updated code:\\n\", color=\"light_green\")\n", "    text += f\"{sample.updated_code}\"\n", "    text += colored(\"#\" * 30 + '\\n', color=\"light_blue\")\n", "    text += colored(f\"Diff:\\n\", color=\"light_green\")\n", "    text += f\"{diff}\"\n", "\n", "    return text"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 3 categories: ['Formatting-Cleaning', 'Refactoring', 'Bug-Fixing']\n", "Loaded 2642 raw code samples.\n"]}, {"data": {"text/plain": ["{'instruction': 'Add type annotations to this function',\n", " 'inverse_instruction': 'Delete type annotations from this function',\n", " 'categories': ['Formatting-Cleaning']}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["GOLDEN_SEEDS = seed_lib.load_code_edit_seeds()\n", "GOLDEN_SEEDS = {k: v for k, v in GOLDEN_SEEDS.items() if len(v) > 0}\n", "print(f\"Loaded {len(GOLDEN_SEEDS)} categories: {list(GOLDEN_SEEDS.keys())}\")\n", "\n", "RAW_CODE_SAMPLES = [\n", "    utils_for_dataclass.create_from_dict(CodeEditData, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst\"\n", "    )\n", "]\n", "print(f\"Loaded {len(RAW_CODE_SAMPLES)} raw code samples.\")\n", "\n", "# Read instructions\n", "INSTRUCTIONS_PATH = \"/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/seed_instructions_with_inverse.json\"\n", "\n", "with open(INSTRUCTIONS_PATH, \"r\") as f:\n", "    ALL_INSTRUCTIONS = json.load(f)\n", "\n", "ALL_INSTRUCTIONS[0]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["instruction: 'Delete type annotations from this function'\n", "instruction: 'Change log level to info'\n", "instruction: 'Change torch.mean to np.mean'\n", "instruction: 'Change floats into numbers'\n", "instruction: 'Change strings to numbers'\n", "==================================================\n", "Example 1:\n", "example_1_middle_part:\n", "```    old_status, new_status = status_change\n", "    was_member = old_status in [\n", "        ChatMember.MEMBER,\n", "        ChatMember.OWNER,\n", "        ChatMember.ADMINISTRATOR,\n", "    ] or (old_status == ChatMember.RESTRICTED and old_is_member is True)\n", "    is_member = new_status in [\n", "        ChatMember.MEMBER,\n", "        ChatMember.OWNER,\n", "        ChatMember.ADMINISTRATOR,\n", "    ] or (new_status == ChatMember.RESTRICTED and new_is_member is True)\n", "\n", "    return was_member, is_member\n", "```\n", "example_1_instruction: 'Introduce a bug in computing whether user was a member of a chat'\n", "example_1_updated_middle_part:\n", "```    old_status, new_status = status_change\n", "    was_member = old_status in [\n", "        ChatMember.OWNER,\n", "        ChatMember.ADMINISTRATOR,\n", "    ] or (old_status == ChatMember.RESTRICTED and old_is_member is True)\n", "    is_member = new_status in [\n", "        ChatMember.MEMBER,\n", "        ChatMember.OWNER,\n", "        ChatMember.ADMINISTRATOR,\n", "    ] or (new_status == ChatMember.RESTRICTED and new_is_member is True)\n", "\n", "    return was_member, is_member\n", "```\n", "example_1_inverse_instruction: 'Fix bug in deciding whether user was a member of a chat.'\n", "\n", "Example 2:\n", "example_2_middle_part:\n", "```def get_globfiles(fileglob, minfiles=1, maxfiles=1):\n", "    \"\"\"\n", "    Get file(s) matching ``fileglob``.  If the number of matching\n", "    files is less than minfiles or more than maxfiles then an\n", "    exception is raised.\n", "\n", "    :param fileglob: Input file glob\n", "    :param minfiles: Minimum matching files (None => no minimum)\n", "    :param maxfiles: Maximum matching files (None => no maximum)\n", "    \"\"\"\n", "    files = glob.glob(fileglob)\n", "    nfiles = len(files)\n", "    if minfiles is not None and nfiles < minfiles:\n", "        raise ValueError('At least %d file(s) required for %s but %d found' % (minfiles, fileglob, nfiles))\n", "    if maxfiles is not None and nfiles > maxfiles:\n", "        raise ValueError('No more than %d file(s) required for %s but %d found' % (maxfiles, fileglob, nfiles))\n", "\n", "    return files\n", "```\n", "example_2_instruction: 'Introduce logical error in exception messages'\n", "example_2_updated_middle_part:\n", "```def get_globfiles(fileglob, minfiles=1, maxfiles=1):\n", "    \"\"\"\n", "    Get file(s) matching ``fileglob``.  If the number of matching\n", "    files is less than minfiles or more than maxfiles then an\n", "    exception is raised.\n", "\n", "    :param fileglob: Input file glob\n", "    :param minfiles: Minimum matching files (None => no minimum)\n", "    :param maxfiles: Maximum matching files (None => no maximum)\n", "    \"\"\"\n", "    files = glob.glob(fileglob)\n", "    nfiles = len(files)\n", "    if minfiles is not None and nfiles < minfiles:\n", "        raise ValueError('At least %d file(s) required for %s but %d found' % (maxfiles, nfiles, fileglob))\n", "    if maxfiles is not None and nfiles > maxfiles:\n", "        raise ValueError('No more than %d file(s) required for %s but %d found' % (minfiles, nfiles, fileglob))\n", "\n", "    return files\n", "```\n", "example_2_inverse_instruction: 'Fix exception messages in this function'\n", "\n", "Example 3:\n", "example_3_middle_part:\n", "```        if self.values is None:\n", "            plt.scatter(self.x_data_set, self.y_data_set[0], color=self.color[0], label=self.label[0])\n", "            if len(self.y_data_set) > 1:\n", "                for i in range(1, len(self.y_data_set)):\n", "                    plt.scatter(self.x_data_set, self.y_data_set[i], color=self.color[i], label=self.label[i])\n", "            plt.xlabel(self.xaxis_name, fontweight='bold', fontsize=15)\n", "            plt.ylabel(self.yaxis_name, fontweight='bold', fontsize=15)\n", "            plt.gcf().autofmt_xdate()\n", "            plt.legend()\n", "```\n", "example_3_instruction: 'Introduct an bug that leads to index out of range error and incorrect color and label indexing'\n", "example_3_updated_middle_part:\n", "```        if self.values is None:\n", "            plt.scatter(self.x_data_set, self.y_data_set[0], color=self.color[0], label=self.label[0])\n", "            if len(self.y_data_set) > 1:\n", "                for i in range(1, len(self.y_data_set) + 1):\n", "                    plt.scatter(self.x_data_set, self.y_data_set[i], color=self.color[i - 1], label=self.label[i - 1])\n", "            plt.xlabel(self.xaxis_name, fontweight='bold', fontsize=15)\n", "            plt.ylabel(self.yaxis_name, fontweight='bold', fontsize=15)\n", "            plt.gcf().autofmt_xdate()\n", "            plt.legend()\n", "```\n", "example_3_inverse_instruction: 'Fix index out of range error and incorrect color and label indexing'\n", "\n"]}], "source": ["def render_instructions_examples(instructions, add_inverse):\n", "    r = []\n", "    for i in instructions:\n", "        cur_s = f\"instruction: '{i['inverse_instruction']}'\"\n", "        if add_inverse:\n", "            cur_s += f\", inverse_instruction: '{i['instruction']}'\"\n", "        r.append(cur_s)\n", "\n", "    return '\\n'.join(r)\n", "\n", "def render_fewshot_examples(fewshot_examples, add_instruction=False, add_inverse_instruction=False, add_selected_code=False):\n", "    r = []\n", "    for i, ex in enumerate(fewshot_examples):\n", "        i += 1\n", "        cur_s = f\"Example {i}:\\n\"\n", "        cur_s += f\"example_{i}_middle_part:\\n```{ex.updated_code}```\\n\"\n", "        if add_instruction:\n", "            cur_s += f\"example_{i}_instruction: '{ex.inverse_instructions[0].text}'\\n\"\n", "        if add_selected_code:\n", "            cur_s += f\"example_{i}_updated_middle_part:\\n```{ex.selected_code}```\\n\"\n", "        if add_inverse_instruction:\n", "            cur_s += f\"example_{i}_inverse_instruction: '{ex.instructions[0].text}'\\n\"\n", "\n", "        r.append(cur_s)\n", "    return '\\n'.join(r)\n", "\n", "def render_code_reminder(sample):\n", "    return f\"\"\"Now just to remind you the original middle part, here it is:\n", "```{sample.updated_code}```\"\"\"\n", "\n", "print(render_instructions_examples(ALL_INSTRUCTIONS[:5], False))\n", "print(\"=\" * 50)\n", "print(render_fewshot_examples(GOLDEN_SEEDS[\"Bug-Fixing\"][:3], True, True, True))\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["SEP1 = \"&\" * 10\n", "SEP2 = \"$\" * 10\n", "\n", "\n", "def call_gpt(messages, use_json):\n", "    model = \"gpt-4-1106-preview\"\n", "    kwargs = {\"response_format\": {\"type\": \"json_object\"}} if use_json else {}\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        messages=messages,\n", "        **kwargs\n", "    )\n", "    response_txt = response.choices[0].message.content\n", "    messages.append({\"role\": \"assistant\", \"content\": response_txt})\n", "\n", "    if use_json:\n", "        return json.loads(response_txt)\n", "    return response_txt\n", "\n", "\n", "def generate(prompt, messages, use_json=False):\n", "    # Fix the usecase, when prompt contains JSON, but it's not about return format, just a part of code\n", "    # assert (\"JSON\" in prompt) == use_json, ( \"JSON\" in prompt, use_json)\n", "\n", "    messages.append({\"role\": \"user\", \"content\": prompt})\n", "\n", "    return call_gpt(messages, use_json)\n", "\n", "def confirm(clause, messages):\n", "    prompt = f\"\"\"Please, check that {clause}\n", "Reply Yes or No.\"\"\"\n", "    \n", "    messages.append({\"role\": \"user\", \"content\": prompt})\n", "    response_txt = call_gpt(messages, False)\n", "\n", "    return \"Yes\" in response_txt\n", "\n", "def extract(name, messages):\n", "    prompt = f\"\"\"Please, return {name} in a JSON with a single key: 'value'.\"\"\"\n", "\n", "    messages.append({\"role\": \"user\", \"content\": prompt})\n", "    value_json = call_gpt(messages, True)\n", "    value = value_json['value']\n", "\n", "    return value\n", "\n", "\n", "def critique_code(sample, instruction, generated_code):\n", "    prompt1 = f\"\"\"\n", "You are an assistant that assesses the result of code modification made to a specific chunk of code.\n", "\n", "You will be provided with an original chunk of code, modified chunk of code and instruction.\n", "You are required to evaluate whether modified code meets all of the criterias:\n", "1. Any difference you see between original and modified code are explicitly specified in instruction.\n", "2. Modified code should not contain any modifications that are not mentioned in instruction.\n", "3. Modified code should not contain any comments related to modifications made, unless it explicitly required by instruction.\n", "4. Formatting/comments/style/etc of original code should not be changed, unless it explicitly required by instruction.\n", "\n", "You have to focus only on evaluating accuracy and precision of modifications made between provided code chunks according to instruction. \n", "Even if the modified chunk contains syntax/runtime/logical or any other type of errors, but at the same time it was accurately and precisely modified according to instruction, consider it as success and mark evaluation as passed.\n", "\n", "If the modified code doesn't meet aforementioned criterias, please explain why.\n", "\n", "Here is the original code:\n", "```{sample.updated_code}```\n", "\n", "Here is the modified code:\n", "```{generated_code}```\n", "\n", "Here is the instruction: '{instruction}'\n", "\"\"\"\n", "    prompt2 = f\"\"\"\n", "Return results of evaluation as a JSON with 2 keys:\n", "- success\n", "- feedback\n", "\n", "If modified code passed evaluation, then success must be True and feedback empty.\n", "If modified code didn't pass evaluation, then success must be False and feedback should contain explanation why.\n", "\"\"\"\n", "\n", "    local_messages = []\n", "    _ = generate(prompt1, local_messages)\n", "    eval_result = generate(prompt2, local_messages, True)\n", "    if 'criqs' not in LOGS:\n", "        LOGS['criqs'] = []\n", "    LOGS['criqs'].append(local_messages)\n", "\n", "    return eval_result\n", "\n", "\n", "def critique_instruction(sample, generated_code, instruction):\n", "    prompt1 = f\"\"\"\n", "You are an assistant that assesses the instruction that contains source code transformation.\n", "\n", "You will be provided with an original chunk of code, instruction and modified chunk of code.\n", "Modified code was obtained by modifying the original code according to the instruction.\n", "\n", "You are required to evaluate whether instruction instruction meets all of the criterias:\n", "1. Instruction contain modifications required to obtain modified code based on original code.\n", "2. Any modifications present in modified code are explicitly specified in instruction.\n", "3. Instruction should not mention any modifications that are not present in modified code.\n", "\n", "You have to focus only on evaluating of how accurately and precisely instruction describes transformation between original and modified code. \n", "Even if the original or modified chunk contain syntax/runtime/logical or any other type of errors, but at the same time instruction accurately and precisely describes transformation between original and modified code, consider it as success and mark evaluation as passed.\n", "\n", "\n", "If the instruction doesn't meet aforementioned criterias, please explain why.\n", "\n", "Here is the original code:\n", "```{get_last_n_lines(sample.prefix, 50)}{SEP1}{generated_code}{SEP2}{get_first_n_lines(sample.suffix, 50)}```\n", "\n", "Here is the modified code:\n", "```{get_last_n_lines(sample.prefix, 50)}{SEP1}{sample.updated_code}{SEP2}{get_first_n_lines(sample.suffix, 50)}```\n", "\n", "Here is the instruction: '{instruction}'\n", "\"\"\"\n", "    prompt2 = f\"\"\"\n", "Return results of evaluation as a JSON with 2 keys:\n", "- success\n", "- feedback\n", "\n", "If instruction passed evaluation, then success must be True and feedback empty.\n", "If instruction didn't pass evaluation, then success must be False and feedback should contain explanation why.\n", "\"\"\"\n", "    \n", "    local_messages = []\n", "    _ = generate(prompt1, local_messages)\n", "    eval_result = generate(prompt2, local_messages, True)\n", "    if 'criqs' not in LOGS:\n", "        LOGS['criqs'] = []\n", "    LOGS['criqs'].append(local_messages)\n", "\n", "    return eval_result"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["SEP1 = \"&\" * 10\n", "SEP2 = \"$\" * 10\n", "\n", "# s: CodeEditData\n", "p1 = lambda s: f\"\"\"Here is a piece of code splitted in 3 parts (prefix, middle and suffix) using {SEP1} and {SEP2}:\n", "```{get_last_n_lines(s.prefix, 50)}{SEP1}{s.updated_code}{SEP2}{get_first_n_lines(s.suffix, 50)}```\n", "middle part is enclosed between {SEP1} and {SEP2}. {SEP1} and {SEP2} are used just as separators, they are not a part of neither prefix, middle code or suffix.\n", "Analyze this chunk of code.\"\"\"\n", "\n", "p2 = lambda s: f\"\"\"{render_code_reminder(s)}\n", "Give me a deeper analysis of middle part\n", "\"\"\"\n", "\n", "p3 = lambda s, insts, fewshot : f\"\"\"Now you need to come up with an instruction on how to modify the middle part. \n", "Keep in mind that this instruction should directly require changes to be made only to the middle part and not to prefix or suffix.\n", "\n", "Here are examples of instructions that you can use as an inspiration to deduce required instruction for previously provided middle part:\n", "{render_instructions_examples(insts, False)}\n", "\n", "Here are examples of other middle parts and their respective instructions:\n", "{render_fewshot_examples(fewshot, True)}\n", "\n", "{render_code_reminder(s)}\n", "Now you need to come up with an instruction on how to modify the middle part. \n", "Keep in mind that this instruction should directly require changes to be made only to the middle part and not to prefix or suffix.\n", "\"\"\"\n", "\n", "p4 = lambda s, insts, fewshot, gen_inst : f\"\"\"Now you need to generate updated middle part, by applying the instruction you just generated ('{gen_inst}') to the original middle part.\n", "Be carefull and only make changes that are directly required by a previously generated instruction.\n", "Unless explicitly required by the instruction, do not comment changes that you make.\n", "\n", "Here are examples of other middle parts with their respective instruction and their updated versions:\n", "{render_fewshot_examples(fewshot, True, False, True)}\n", "\n", "{render_code_reminder(s)}\n", "Now you need to generate updated middle part, by applying the instruction you just generated ('{gen_inst}') to the original middle part.\n", "Be carefull and only make changes that are directly required by a previously generated instruction.\n", "Unless explicitly required by the instruction, do not comment changes that you make.\n", "\"\"\"\n", "\n", "p5 = lambda s, inst, fewshot : f\"\"\"Now you need to deduce the inverse instruction. It's the instruction that transforms updated middle code back into the original middle code.\n", "Be carefull and make sure that inverse instruction directly and unambiguously transforms updated middle code that you just generated back into the original middle code.\n", "Also, make sure that inverse instruction should not have any references to the previously generated instruction (i.e. it should not contain phrases like 're-add', 'change back', 'rename back', etc)\n", "\n", "Here are examples of other middle parts with their respective instruction, updated versions and inverse instructions:\n", "{render_fewshot_examples(fewshot, True, True, True)}\n", "\n", "{render_code_reminder(s)}\n", "\"\"\"\n", "\n", "p6_c = lambda s, inv_inst, updated_middle_part: f\"\"\"\n", "this code: \n", "```{get_last_n_lines(s.prefix, 50)}{SEP1}{updated_middle_part}{SEP2}{get_first_n_lines(s.suffix, 50)}```\n", "directly and unambiguously transforms into this code:\n", "```{get_last_n_lines(s.prefix, 50)}{SEP1}{s.updated_code}{SEP2}{get_first_n_lines(s.suffix, 50)}```\n", "when instruction '{inv_inst}' is applied to the code chunk enclosed between {SEP1} and {SEP2}.\n", "\"\"\"\n", "\n", "p7_ta_code = lambda feedback, type_: f\"\"\"Here is a feedback on {type_} you just generated:\n", "{feedback}.\n", "Feedback was collected using these criterias:\n", "1. Any difference you see between original and updated middle code are explicitly specified in instruction.\n", "2. Updated middle code should not contain any modifications that are not mentioned in instruction.\n", "3. Updated middle code should not contain any comments related to modifications made, unless it explicitly required by instruction.\n", "4. Formatting/indentations/comments/style/etc of original code should not be changed, unless it explicitly required by instruction.\n", "\n", "Please, try generating {type_} again and take feedback into account.\n", "\"\"\"\n", "\n", "p7_ta_inst = lambda feedback, type_: f\"\"\"Here is a feedback on {type_} you just generated:\n", "{feedback}.\n", "Feedback was collected using these criterias:\n", "1. Instruction contain modifications required to obtain updated middle code based on original code.\n", "2. Any modifications present in updated code are explicitly specified in instruction.\n", "3. Instruction should not mention any modifications that are not present in updated code.\n", "\n", "Please, try generating {type_} again and take feedback into account.\n", "\"\"\"\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class Step:\n", "    def __call__(self, num_retries, messages, *args, **kwargs):\n", "        init_messages_size = len(messages)\n", "        kwargs['prev_feedback'] = None\n", "\n", "        while num_retries > 0:\n", "            print(f\"Running {self.__class__.__name__}. Retries left: {num_retries}\")\n", "            is_success, result = self.logic(messages, *args, **kwargs)\n", "\n", "            if isinstance(is_success, tuple):\n", "                LOGS[f\"{self.__class__.__name__}_critique_{num_retries}\"] = is_success\n", "                kwargs['prev_feedback'] = is_success[1]\n", "                is_success = False\n", "                \n", "            \n", "            if is_success:\n", "                return result, num_retries\n", "            \n", "            LOGS[f\"{self.__class__.__name__}_{num_retries}\"] = copy.deepcopy(messages)\n", "            num_retries -= 1\n", "            if kwargs['prev_feedback'] is None: #if feedback is None, we just drop the message and try again\n", "                # messages = messages[:init_messages_size]\n", "                del messages[init_messages_size:]\n", "        return None, 0\n", "\n", "\n", "class AnalysisStep(Step):\n", "    def logic(self, messages, sample, prev_feedback=None):\n", "        # analysis of whole code\n", "        _ = generate(p1(sample), messages)\n", "        return True, None\n", "    \n", "class AnalysisMiddleStep(Step):\n", "    def logic(self, messages, sample, prev_feedback=None):\n", "        #Check that model correctly gets the middle part\n", "        # middle_part = extract(\"middle_part\", messages)\n", "        # if middle_part.rstrip() != sample.updated_code.rstrip(): # Not the best solution with rstrip\n", "        #     return False, None\n", "\n", "        # analysis of middle part\n", "        _ = generate(p2(sample), messages)\n", "\n", "        return True, None\n", "    \n", "class InstructionGenerationStep(Step):\n", "    def logic(self, messages, sample, instructions_examples, fewshot_examples, prev_feedback=None):\n", "        # instruction generation\n", "        if prev_feedback is None:\n", "            _ = generate(p3(sample, instructions_examples, fewshot_examples), messages)\n", "        else:\n", "            _ = generate(p7_ta_inst(prev_feedback, \"instruction\"), messages)\n", "        \n", "        instruction = extract(\"instruction\", messages)\n", "        if not confirm(f\"this is the instruction you just generated: '{instruction}'\", messages):\n", "            return False, None\n", "        \n", "        #### CRITIQUE1\n", "        if not confirm(f\"This instruction: '{instruction}' requires changes to be made only to the middle part and not to prefix or suffix\", messages):\n", "           return False, None\n", "        return True, instruction\n", "    \n", "        # eval_results = critique_instruction(sample, instruction)\n", "        # if eval_results['success']:\n", "        #     return True, instruction, None\n", "        # else:\n", "        #     return False, instruction, eval_results['feedback']\n", "    \n", "class CodeGenerationStep(Step):\n", "    def logic(self, messages, sample, instructions_examples, fewshot_examples, gen_instruction, prev_feedback):\n", "        if prev_feedback is None:\n", "            _ = generate(p4(sample, instructions_examples, fewshot_examples, gen_instruction), messages)\n", "        else:\n", "            _ = generate(p7_ta_code(prev_feedback, \"middle part\"), messages)\n", "        updated_middle_part = extract('updated middle part after applying the instruction', messages)\n", "        if not confirm(f\"this is the updated middle part you just generated: ```{updated_middle_part}```\", messages):\n", "            return False, None\n", "        \n", "        #### CRITIQUE2\n", "        # if not confirm(f\"updated middle part is a result of applying instruction {gen_instruction} to the middle part: {sample.updated_code}. And it doesn't contain any other changes\", messages):\n", "        #     return False, None\n", "        eval_results = critique_code(sample, gen_instruction, updated_middle_part)\n", "        if eval_results['success']:\n", "            return True, updated_middle_part\n", "        else:\n", "            return (False, eval_results['feedback']), None\n", "        \n", "        # return True, updated_middle_part\n", "    \n", "class InverseInstructionGenerationStep(Step):\n", "    def logic(self, messages, sample, instructions_examples, fewshot_examples, gen_instruction, gen_code, prev_feedback=None):\n", "        if prev_feedback is None:\n", "            _ = generate(p5(sample, instructions_examples, fewshot_examples), messages)\n", "        else:\n", "            _ = generate(p7_ta_inst(prev_feedback, \"inverse_instruction\"), messages)\n", "        inverse_instruction = extract(\"inverse_instruction\", messages)\n", "        if not confirm(f\"this is the inverse instruction you just generated: '{inverse_instruction}'\", messages):\n", "            return False, None\n", "        \n", "        #### CRITIQUE3\n", "        # if not confirm(f\"inverse_instruction {inverse_instruction} directly and unambiguously transforms this code ```{gen_code}``` into this code ```{sample.updated_code}```\", messages):\n", "        #     return False, None\n", "        \n", "        # #### CRETIQUE4\n", "        # if not confirm(p6_c(sample, inverse_instruction, gen_code), messages):\n", "        #     return False, None\n", "        eval_results = critique_instruction(sample, gen_code, inverse_instruction)\n", "        if eval_results[\"success\"]:\n", "            return True, inverse_instruction\n", "        else:\n", "            return (False, eval_results['feedback']), None\n", "        \n", "        # return True, inverse_instruction\n", "    \n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def run_pipeline(sample, instructions_examples, fewshot_examples):\n", "    messages = []\n", "    num_retries_left = 5\n", "\n", "    analysis_step = AnalysisStep()\n", "    _, num_retries_left = analysis_step(num_retries_left, messages, sample=sample)\n", "\n", "    analysis_middle_step = AnalysisMiddleStep()\n", "    _, num_retries_left = analysis_middle_step(num_retries_left, messages, sample=sample)\n", "\n", "    instruction_step = InstructionGenerationStep()\n", "    instruction, num_retries_left = instruction_step(num_retries_left, messages,\n", "                                                     sample=sample,\n", "                                                     instructions_examples=instructions_examples,\n", "                                                     fewshot_examples=fewshot_examples)\n", "    \n", "    code_gen_step = CodeGenerationStep()\n", "    gen_code, num_retries_left = code_gen_step(num_retries_left, messages,\n", "                                            sample=sample,\n", "                                            instructions_examples=instructions_examples,\n", "                                            fewshot_examples=fewshot_examples,\n", "                                            gen_instruction=instruction)\n", "    \n", "    inverse_instruction_step = InverseInstructionGenerationStep()\n", "    inverse_instruction, num_retries_left = inverse_instruction_step(num_retries_left, messages,\n", "                                                                     sample=sample,\n", "                                                                     instructions_examples=instructions_examples,\n", "                                                                     fewshot_examples=fewshot_examples,\n", "                                                                     gen_instruction=instruction,\n", "                                                                     gen_code=gen_code)\n", "    \n", "    LOGS['finale'] = messages\n", "    return {\n", "        \"num_retries_left\": num_retries_left,\n", "        \"instruction\": instruction,\n", "        \"gen_code\": gen_code,\n", "        \"inverse_instruction\": inverse_instruction,\n", "        \"messages\": messages\n", "    }"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running AnalysisStep. Retries left: 5\n", "Running AnalysisMiddleStep. Retries left: 5\n", "Running InstructionGenerationStep. Retries left: 5\n", "Running CodeGenerationStep. Retries left: 5\n", "Running CodeGenerationStep. Retries left: 4\n", "Running InverseInstructionGenerationStep. Retries left: 4\n"]}], "source": ["LOGS = {} #used globally\n", "\n", "\n", "sample = RAW_CODE_SAMPLES[0]\n", "instructions_examples = ALL_INSTRUCTIONS[:10]\n", "fewshot_examples = GOLDEN_SEEDS[\"Bug-Fixing\"][:3]\n", "\n", "result = run_pipeline(sample, instructions_examples, fewshot_examples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["LOGS.keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for i in tqdm(range(50)):\n", "# # for i in range(3, 6):\n", "#     try:\n", "#         LOGS = {} #used globally\n", "\n", "#         sample = RAW_CODE_SAMPLES[i]\n", "#         instructions_examples = ALL_INSTRUCTIONS[:10]\n", "#         fewshot_examples = GOLDEN_SEEDS[\"Bug-Fixing\"][:3]\n", "\n", "#         result = run_pipeline(sample, instructions_examples, fewshot_examples)\n", "\n", "#         for attr in [sample.selected_code, sample.instructions, sample.inverse_instructions]:\n", "#             assert attr is None\n", "\n", "#         sample_upd = copy.deepcopy(sample)\n", "#         sample_upd.selected_code = result[\"gen_code\"]\n", "#         sample_upd.instructions = [Instruction(result[\"inverse_instruction\"], None)]\n", "#         sample_upd.inverse_instructions = [Instruction(result[\"instruction\"], None)]\n", "\n", "#         with open(f'/home/<USER>/tmp/diff20/{i}.pickle', 'wb') as f:\n", "#             pickle.dump((sample, sample_upd, result, LOGS), f)\n", "#     except KeyboardInterrupt:\n", "#         break\n", "#     except:\n", "#         print(f\"Number {i} failed\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
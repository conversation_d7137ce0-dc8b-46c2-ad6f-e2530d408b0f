{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Dog fooding"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["DOGFOOD_DIR = Path(\"/mnt/efs/augment/user/dxy/datasets/edit.raw/dogfood_edit_logs\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["1000it [00:02, 472.66it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Total unique instructions : 286\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["df_instructions = set()\n", "\n", "for log_file in tqdm(DOGFOOD_DIR.glob(\"*.json\")):\n", "    with open(log_file) as f:\n", "        sample = json.load(f)\n", "    df_instructions.add(sample[\"instruction\"])\n", "\n", "print(f\"Total unique instructions : {len(df_instructions)}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'124',\n", " 'Add type annotations',\n", " 'Add type annotations to this file',\n", " 'Add type annotations to this function',\n", " 'Change \\' to \"',\n", " 'Change code creatively',\n", " 'Change decorator',\n", " 'Change level of logs',\n", " 'Change log level to error',\n", " 'Change np.mean to tf.meam',\n", " 'Change np.mean to tf.mean',\n", " 'Change np.mean to torch.mean',\n", " 'Change numbers into floats',\n", " 'Change numbers into strings',\n", " 'Change numbers to float',\n", " 'Change numbers to floats',\n", " 'Change numbers to strings',\n", " 'Change quotes to double quotes',\n", " 'Change variables to A',\n", " 'Change variables to AAA',\n", " 'Change variables to Camel Case',\n", " 'Change variables to camel case',\n", " 'Convert to use import syntax',\n", " 'DRY up this code',\n", " 'Fix it',\n", " 'Fix ordering',\n", " \"Fix this for when there is one bottle of beer and we don't want the plural\",\n", " 'Flip the if else logic',\n", " 'Make all caps',\n", " 'Make all comments in multiple lines',\n", " 'Make all numbers float',\n", " 'Make all numbers into strings',\n", " 'Make all variables camel case',\n", " 'Make all variables snake case',\n", " 'Make camel case',\n", " 'Make it camel case',\n", " 'Make names in camel case',\n", " 'Make names in snake case',\n", " 'Make one liner',\n", " 'Make vairables camel case',\n", " 'Make variables all caps',\n", " 'Make variables camel calse',\n", " 'Make variables camel case',\n", " 'Make variables camel caser',\n", " 'Make variables in all caps',\n", " 'Make variables in camel case',\n", " 'Make variables in camel cast',\n", " 'Make variables in pytorch style',\n", " 'Make variables small caps',\n", " 'Make variables snake case',\n", " '<PERSON><PERSON> vode all caps',\n", " 'Prevent calling app.use multiple times',\n", " 'Rearrage imports',\n", " 'Refactor by creating a print_stores function',\n", " 'Refactor out of main() to print_stores() and have main call it',\n", " 'Refactor this to a print_stores function',\n", " 'Refactor this to a print_stores function that main calls',\n", " 'Refactor this to a print_stores function that main() calls',\n", " 'Refactor to create a print_stores() function that main() calls',\n", " 'Refactor to create print_stores() function that main calls',\n", " 'Remove help notes',\n", " 'Rename vairables',\n", " 'Replace \" with \\'',\n", " 'Replace 0 with i',\n", " 'Replace all l with L',\n", " 'Replace np.mean with torch.mean',\n", " 'Simplify',\n", " 'Simplify this',\n", " 'Some prompt',\n", " 'Split into multiple lines',\n", " 'Update',\n", " 'Use single quotes',\n", " 'Write a unit test for DeepSeekBasePromptFormatter',\n", " 'Write in one line',\n", " 'Write it in one line',\n", " 'adapt this class to calculate exact match on the generated code',\n", " 'add a comment explaining the code',\n", " 'add a dry run argument',\n", " 'add a dry-run argument',\n", " 'add a function to retrieve multiple users',\n", " 'add a verbose argument',\n", " 'add asdict import',\n", " 'add capture of TypeError',\n", " 'add cls at the right places',\n", " 'add contextLen as optional int, temperature as optional float, top_k as optional float',\n", " 'add encoraging comment',\n", " 'add encouraging comments',\n", " 'add error handling',\n", " 'add error handling for requests',\n", " 'add missing import',\n", " 'add more files',\n", " 'add some error handling',\n", " 'add the type \"FileBackedStore[T]\"',\n", " 'add the type annotations',\n", " 'add type annotations to this file',\n", " 'adjust to be able to bulk delete users',\n", " 'are u ok?',\n", " 'assert that it is json',\n", " 'change 1 to 2',\n", " 'change double quote to single',\n", " 'change from 1 to 2',\n", " 'change numbers to float',\n", " 'change numbers to strings',\n", " 'change single quote to double quote',\n", " 'change string to number',\n", " 'change this to work with the emtask',\n", " 'change to support the creation of multiple users',\n", " 'change to support the creation of multiple users using insertMany',\n", " 'change variables to camel case',\n", " 'cleanup',\n", " 'combine the org and repo name, then see if a directory is a part of that name. If it is, split the combined name to strip away the directory name',\n", " 'comment both lines',\n", " 'comment out these 2 lines',\n", " 'compute the file_name',\n", " 'convert to Javascript',\n", " 'convert to python',\n", " 'convert to python code',\n", " 'correct its format as others',\n", " 'correct potential bugs if possible',\n", " 'correct potential bugs if possible, be careful to be coherent with the surrounded codes',\n", " \"correct this dict's format\",\n", " 'create a license',\n", " 'delete all',\n", " 'delete all ',\n", " 'delete all the type anno',\n", " 'delete all the type annotation',\n", " 'delete all the type annotations',\n", " 'delete all these type annotation',\n", " 'delete all type annotation',\n", " 'delete from',\n", " 'delete the type annotation',\n", " 'delete them',\n", " 'delete these type annotation',\n", " 'delete these type annotations',\n", " 'delete this line',\n", " 'delete type annotation',\n", " 'delete type annotations',\n", " 'do it',\n", " \"don't return an array if only one user is created\",\n", " \"don't use let\",\n", " 'enhance the docstring',\n", " 'enhance the docstring here',\n", " 'finish support to allow for deleting multiple users',\n", " 'finish support to allow for deleting multiple users without using a loop',\n", " 'fix',\n", " 'fix the hash the users password',\n", " 'fix the selected_code and suffix',\n", " 'fix to be async await',\n", " 'fix to export the client',\n", " 'fix to return contents of root folder',\n", " 'for each entry in found_combined_name, split the second entry by the first',\n", " 'for each record in corrected_names, create a directory for the org, then move the repo directory to the org directory',\n", " 'for each tuple in found_combined_name, split the second element by the first',\n", " 'handle the case for a single user not in an array',\n", " 'hello',\n", " 'hello world!',\n", " 'hellp',\n", " 'hi',\n", " 'if no id, return all users',\n", " 'if there is an ID query param, call getUser, else call getUsers',\n", " 'implement error handling',\n", " 'import as type json',\n", " 'improve',\n", " 'improve this function',\n", " 'init as 1',\n", " 'init as 3',\n", " 'keep consistent semicolon style',\n", " 'make all caps',\n", " 'make camelcase variable',\n", " 'make docs a separate variable',\n", " 'make docs an explicit variable',\n", " 'make this clearer',\n", " 'make this get a word boundary',\n", " 'make this look more like HumanEvalOutput',\n", " 'make this lowercase',\n", " 'make this more readable',\n", " 'make this work for all numeric types but numeric types only',\n", " 'make this work for any number types',\n", " 'make variables in camel case',\n", " 'modify to provide the options to create multiple users instead of just one using insertMany',\n", " 'modify to support the creation of multiple users',\n", " 'modify to support the creation of multiple users using insertMany',\n", " 'move docs to a separate variable',\n", " 'move docs to separate var',\n", " 'move docs to separate variable',\n", " 'move file_store creation into print_files',\n", " 'move files to a separate variable',\n", " 'move files to separate var',\n", " 'move files to separate variable',\n", " 'move logic to function print_files',\n", " 'move main logic to print_files function',\n", " 'move this under a loop that iterates through a directory tree to form the repo_path',\n", " 'move this under a loop that iterates through a directory tree to form the repo_path, and use pathlib',\n", " 'move to a function',\n", " 'parse the argument',\n", " 'parse the path argument',\n", " 'polish it',\n", " 'polish the codes',\n", " 'polish them',\n", " 'polish these code',\n", " 'print more details',\n", " 'read the list from the all_repos.txt file',\n", " 'refactor docs to separat var',\n", " 'refactor docs to separat variable',\n", " 'refactor docs to separate variable',\n", " 'refactor files to separate variable',\n", " 'refactor files to variable',\n", " 'refactor get_files to a variable',\n", " 'refactor list of files to variable',\n", " 'refactor logic to function print_files',\n", " 'refactor logic to print_files function',\n", " 'refactor main logic to a print_files function',\n", " 'refactor main logic to print_docs function',\n", " 'refactor main logic to print_files',\n", " 'refactor main logic to print_files function',\n", " 'refactor main logic to print_functions',\n", " 'refactor the codes in good practice',\n", " 'refactor the logic to a function print_files',\n", " 'refactor the main logic to print_files function',\n", " 'refactor this to extract a `print_store` function that `main` calls',\n", " 'refactor to a main function',\n", " 'refactor to support adding multiple users as well as single users',\n", " 'refactor to use async await for prompt',\n", " 'refactor to variable called files',\n", " 'relo',\n", " 'remove all self.',\n", " 'remove all the type annotation',\n", " 'remove tqdm',\n", " 'rename 1 to 2',\n", " 'rename bar',\n", " 'rename foo to bar',\n", " 'rename from 1 to 2',\n", " 'rename variable to arguments',\n", " 'replace \" with \\' anywhere',\n", " 'replace 0 with i',\n", " 'replace double quote with single quote',\n", " 'replace double quotes with single quote',\n", " 'replace double quotes with single quotes',\n", " 'replace single quote with double quotes',\n", " 'revert the last change to this file',\n", " 'rewrite the os.walk using pathlib',\n", " 'rewrite using pathlib',\n", " 'sdf',\n", " 'single single quotes',\n", " 'sort these imports for isort',\n", " 'this would be better using the create method rather than insert<PERSON>any',\n", " 'translate to python',\n", " 'turn it into 3 sentences',\n", " 'turn it into Javascript',\n", " 'turn it into python code',\n", " 'turn it into python data structure',\n", " 'turn it into valid code',\n", " 'turn the path string into the pathlib style',\n", " 'turn this into Python',\n", " 'turn this into Python code',\n", " 'turn this into python',\n", " 'turn yaml into python data structure',\n", " 'uncomment the first line',\n", " 'update since now using insertMany method',\n", " 'update since now using insertMany method instead of save',\n", " 'update the function to support adding both single users and multiple users',\n", " 'update the function to support adding both single users and multiple users using create method',\n", " 'update the function to support creation of multiple users. use mongoose create method',\n", " 'use # instead of ##',\n", " 'use ## instead of #',\n", " 'use , instead of ;',\n", " 'use , instead of ; for them',\n", " 'use 2 spaces for indentation',\n", " 'use _get_token_id',\n", " 'use a 2 space indent',\n", " 'use a variable to make the if condition more readable',\n", " 'use an immutable approach here instead',\n", " 'use create instead of save method to prevent looping',\n", " 'use debug instead of info',\n", " 'use format instead',\n", " 'use insert method instead',\n", " 'use pathlib library to open file',\n", " 'use pathlib to move the directory',\n", " 'use single quote instead of double quote',\n", " 'use single quotes',\n", " 'what if I want to create only one user',\n", " 'write a new task that looks like the human_eval task',\n", " 'write a python script to read a list of org, repo, sha names and move a directory to be a subdirectory of the org',\n", " 'write more comments',\n", " 'write something encouraging',\n", " 'write this in python'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_instructions"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["DF_SEED_INSTRUCTIONS = [\n", "    \"Add type annotations to this function\", \n", "    \"Change log level to error\",\n", "    \"Change np.mean to tf.mean\",\n", "    \"Change np.mean to torch.mean\",\n", "    \"Change numbers into floats\",\n", "    \"Change numbers to strings\",\n", "    \"Change variables to camel case\",\n", "    \"Replace np.mean with torch.mean\",\n", "    \"Write it in one line\",\n", "    \"add a comment explaining the code\",\n", "    \"add error handling\",\n", "    \"add missing import\",\n", "    \"add the type annotations\",\n", "    \"change double quote to single\",\n", "    \"change single quote to double quote\",\n", "    \"change string to number\",\n", "    \"convert to python code\",\n", "    \"delete all the type annotations\",\n", "    \"remove tqdm\",\n", "    \"replace \\\" with ' anywhere\",\n", "    \"replace double quotes with single quote\",\n", "    \"rewrite using pathlib\",\n", "    \"turn this into Python code\",\n", "    \"use # instead of ##\",\n", "    \"use ## instead of #\",\n", "    \"use a 2 space indent\",\n", "    \"use a variable to make the if condition more readable\",\n", "    \"use format instead\",\n", "    \"use pathlib library to open file\",\n", "    \"write more comments\",\n", "    \"write this in python\",\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# InstructCoder seed"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["INSTRUCT_CODE_SEED_PATH = Path(\"/mnt/efs/augment/user/yuri/data/InstructCoder/github_seed.json\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(634, 634)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["with open(INSTRUCT_CODE_SEED_PATH, \"r\") as f:\n", "    instr_seed_json = json.load(f)\n", "\n", "ic_instructions = [*map(lambda x: x[\"instruction\"], instr_seed_json)]\n", "len(ic_instructions), len(set(ic_instructions))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["['Fix typo',\n", " 'Fix Flake8 errors',\n", " 'Add an error handler',\n", " 'Catch Jedi SystemError.',\n", " 'Fix bug in data setting',\n", " 'add support WSL 2 volumes',\n", " 'refactor to define function',\n", " 'fix the bug of infinite loop',\n", " 'Add bracketed paste support.',\n", " \"Make the 'do' method static.\",\n", " 'Remove unnecessary comments.',\n", " 'remove try/except in test_psd',\n", " 'Continue on decryption error.',\n", " 'Fix typo in exception message.',\n", " 'Add validation for write_mode.',\n", " 'use simplejson where available',\n", " 'Reformat to make flake8 happy.',\n", " 'consistent task name formatting',\n", " 'fix CalledProcessError for py26',\n", " 'Catch exception on hook failure.',\n", " 'Fix an index out of bounds error',\n", " 'add choices for --average-method',\n", " 'change CURRENT_SEASON to 2016-17',\n", " 'Make the condition more readable.',\n", " 'Remove the debug print statement.',\n", " 'Validate input before assignment.',\n", " 'Reset cursor object after .count()',\n", " 'Fix ImportError when using Pillow.',\n", " 'Add note to explain weird teardown',\n", " 'Add proper indentation to the code.',\n", " 'Change debug code to final version.',\n", " 'Print to stderr for status messages',\n", " 'use / instead of // in rotate_bound',\n", " 'Remove the import of nddata module.',\n", " 'Add a license to the setup.py file.',\n", " 'Specify name in exception formatting',\n", " 'Fix error handling for hash parsing.',\n", " '<PERSON>le exception while getting repo.',\n", " 'prevent createConnection if connected',\n", " 'simplify by ignoring the except cases',\n", " 'Remove the workaround for ParseError.',\n", " 'Add a newline at the end of the file.',\n", " 'Update apperance when self is visible',\n", " 'Cast the output of T<PERSON>stack to \"int8\".',\n", " \"Swap two lines in 'openFile' function.\",\n", " 'Loosen wxPython dependency to >= 4.1.0',\n", " 'Add missing argument for format string',\n", " \"Replace 'self' with 'repl' in the code.\",\n", " \"Change 'SpectrumPlot' to 'SegmentPlot'.\",\n", " 'Update the version number to \"*******\".',\n", " 'Import warnings to silence the warnings.',\n", " 'Comment out the code related to rollbar.',\n", " 'Look for `DeprecationWarning` in stderr.',\n", " \"Add 'create_all' in the list of methods.\",\n", " 'Change verbose to empty string if False.',\n", " 'Add project and version to the response.',\n", " \"Remove 'log_{10}' from scaleText string.\",\n", " 'Remove duplicates from the list of links.',\n", " 'Check if path exists and if it is a file.',\n", " \"Add 'kgh_hash' to the function arguments.\",\n", " \"Modify the 'id' field of the 'Task' model.\",\n", " 'Fix wrong typehint of icon_custom_emoji_id',\n", " 'Better user message for JSON decode errors',\n", " 'Remove the handler from the error message.',\n", " 'Add include_locale parameter to Users.info.',\n", " 'Add \"GFTT\" to _DETECTOR_FACTORY dictionary.',\n", " \"Change 'asd' to 'spec' in the example code.\",\n", " \"Add 'Content-length' header to the request.\",\n", " 'Set test intervals to 5 minutes for 5 to 30.',\n", " 'Use OperationalError for error code >= 1000.',\n", " 'Replace self.app.renderer with self.renderer',\n", " 'Verify start and end values before cropping.',\n", " 'Add a newline to the end of the progress bar.',\n", " \"Replace 'chat' with 'm' in the if statements.\",\n", " 'Convert the output of zip function to a list.',\n", " \"Add 'speedtest_cli' module to py_modules list.\",\n", " 'Refactor FakeFs.read() to remove duplicate code',\n", " 'Set the title size to 26 and grid color to gray',\n", " 'Add type annotations and function return types.',\n", " 'Change the import path for TinyDB and storages.',\n", " 'Update LoginUrl class for JSON deserialization.',\n", " 'Fix the update of metadata in the code snippet.',\n", " 'Add function names as comments to the handlers.',\n", " \"Add a docstring to the 'add_component' function.\",\n", " 'Cast k1 and k2 to int before indexing data_list.',\n", " 'Arrange import statements in alphabetical order.',\n", " \"Remove whitespace and check before adding '...'.\",\n", " 'Fix the case where the output is not valid JSON.',\n", " 'use min/max rather than conditiional assignment.',\n", " 'remove the next defined in Queue as it is unused',\n", " \"Add 'Textarea' in the list of valid widget types.\",\n", " 'Add try-except block to handle KeyboardInterrupt.',\n", " 'Remove all print statements and unnecessary code.',\n", " 'Update MySQL server version and sql_mode setting.',\n", " \"Update the version number in the code to '3.7.8'.\",\n", " 'Remove comment and remove domain from the output.',\n", " 'Remove the trailing slash from the URL in line 4.',\n", " 'Fix encoding of long and float types using str().',\n", " 'Remove unnecessary comments and the shebang line.',\n", " \"Remove 'dedupe.clustering' from the package list.\",\n", " \"Add a 'get_output_shape_for' method to the class.\",\n", " 'Spectrum: allow use of x0 and dx to set attributes',\n", " \"Update the version number from '0.3.7' to '0.3.8'.\",\n", " \"Change 'Plex' to 'Youtube' in the class docstring.\",\n", " 'Remove \"\\\\n\" from the items list before processing.',\n", " \"Add 'setuptools' to the list of required packages.\",\n", " 'Add a check for RGBA images and merge them to RGB.',\n", " 'Update the version number in setup function to 0.4.',\n", " \"Add 'enum-compat' to the list of required packages.\",\n", " 'Add error logging to traceback on libc load failure',\n", " 'Update the font awesome version from 4.6.3 to 4.7.0',\n", " 'Remove the types.coroutine compatibility statements.',\n", " \"Replace 'StateTransferLSTM' with 'StateTransferRNN'.\",\n", " \"Change 'set_edge_state' to '_get_graph' in the test.\",\n", " \"Change default 'name_localizations' to an empty dict\",\n", " 'Encode the result as UTF-8 before writing to stdout.',\n", " 'reraise unexpected exceptions during bucket creation',\n", " \"Update the version number from '1.6.16' to '1.6.17'.\",\n", " 'Shorten commit ID to 6 characters for version number.',\n", " \"Add 'comments' parameter to np.savetxt function call.\",\n", " \"Implement the '__iter__' method to support iteration.\",\n", " 'Move author information to the bottom of the imports.',\n", " \"Remove 'setuptools', 'argparse>=1.2.1;python_version<\",\n", " 'Change the position of the prompt from top to bottom.',\n", " 'Add aliases for cursorline and cursorcolumn commands.',\n", " \"Replace 'numpy.ndarray' with 'numpy.array' in line 4.\",\n", " 'Sort dictionary items for consistent parameter order.',\n", " 'Change the type check for long to include int as well.',\n", " 'Modify line 6 to ensure compatibility with Python 3.9.',\n", " 'travis build failed because line was too long. Fix it.',\n", " 'Ensure start and end times are within stream duration.',\n", " \"Create 'state' attribute in Model class and verify it.\",\n", " 'Modify proxy settings to ensure proper authentication.',\n", " \"Add 'delimiter=delimiter' to the loadtxt function call.\",\n", " 'Explicitly return the path instead of the frame object.',\n", " 'Remove Python 3.4 from the list of supported languages.',\n", " 'Fix Context.reply not working with expired interactions',\n", " 'Change log level from info to debug to reduce verbosity.',\n", " \"Change the version number to '0.9.16' in the setup file.\",\n", " 'Move \"aiodns\" to extras_require with the key \"optional\".',\n", " 'Change the version number from (0, 16, 2) to (0, 16, 3).',\n", " 'Re<PERSON>ve commented out code for TfidfIndexPredicate class.',\n", " 'Change the type of idx0 and idx1 to int instead of float.',\n", " \"Skip '# callgrind format' if it appears before 'version'.\",\n", " 'add to cache even for the first item if it does not exist',\n", " \"Replace 'OrderedDict' with 'dedupe.backport.OrderedDict'.\",\n", " \"Remove the declaration of 'id_type' since it is not used.\",\n", " \"remove 'LIGO_DATAFIND_SERVER' from environment variables.\",\n", " 'Update test cases for Redis 6.2 changes in slowlog_get().',\n", " 'Add a message to prompt the user when reading from stdin.',\n", " \"Add a yield statement to return the 'User' and its value.\",\n", " 'Change how the program matches strings and sorts results.',\n", " \"Replace 'absolute' with 'samefile' to compare file paths.\",\n", " \"Remove the 'struct.' prefix from 'calcsize' function call.\",\n", " \"Import 'rcParams' from matplotlib instead of gwpy.plotter.\",\n", " 'Fix indentation and add parentheses for better readability.',\n", " 'Solve <PERSON>r (out of bounds) while moving to predicting',\n", " \"Add 'Constant' to the import statement from '.base' module.\",\n", " 'Complete the implementation of backpropagation for Softmax.',\n", " 'Update prompt_toolkit version and explain why in a comment.',\n", " \"Add a new option 'swap_light_and_dark' and set it to False.\",\n", " 'Update the values of _MD5_CLASSES and _VERSION to 2.17.242.',\n", " \"Create 'open' method to reopen and check existence of file.\",\n", " 'Print each audio feature and its analysis in separate lines.',\n", " \"Add '.value' to 'specgram' in the percentile function calls.\",\n", " 'Update PyTweening version to 1.0.4 in the requirements list.',\n", " \"Add 'include_package_data': True to the setup configuration.\",\n", " 'Accept handler should be called before appending to history.',\n", " 'Add a comment explaining terminal type negociation in telnet',\n", " \"Cast 'ngap' to an integer before using it to create gapshape.\",\n", " \"Change the variable name 'ts' to 'series' for better clarity.\",\n", " 'Remove functools.partial and use pickle_compat.load directly.',\n", " 'Replace print statement with logging.info to improve logging.',\n", " \"Remove the unused variable 'all_channels' and its assignment.\",\n", " \"Add 'username=ANY' to the assert_called_once_with method call.\",\n", " 'Set default value for defaultdict using doc_to_ids[predicate].',\n", " \"Add 'strict' parameter to the '_format_segment' function call.\",\n", " \"Remove unnecessary list comprehension from the 'if' statement.\",\n", " \"Verify 'source' input is not empty, raise ValueError if it is.\",\n", " 'Pa<PERSON> received signature with leading zeros before verifying it.',\n", " 'Wrap webbrowser.open() in try-except and modify prompt message.',\n", " \"Ensure the track availability based on location's track policy.\",\n", " 'Cache song ids and corresponding urls, maintaining their order.',\n", " 'Add support for unicode strings in addition to regular strings.',\n", " 'Refactor the loop to iterate over states first and then models.',\n", " \"Add an optional 'offset' parameter to the 'run_forever' method.\",\n", " 'Change the superclass of GitResetHeadCommand to GitTextCommand.',\n", " \"Use 'foo' as an async callback for the 'on' method of 'Events'.\",\n", " 'Add weights.W to self[name].weights in the penalty calculation.',\n", " 'Change the order of if-elif statements to optimize performance.',\n", " 'Add a try-except block to catch <PERSON><PERSON><PERSON><PERSON> in case of Jedi issue.',\n", " \"Add 'ungroup()' function to the end of CreateCountDf() function.\",\n", " \"Add a new class 'MatchBoolPrefix' with name 'match_bool_prefix'.\",\n", " \"Swap the order of level 0 and level 1 in the 'heading' function.\",\n", " 'Use \\'self.rules[\"descriptor\"]\\' instead of \\'self.descriptor_rule\\'',\n", " 'Archive label properties that will change and reset if required.',\n", " 'Set vm.return_v to vm[vx] when ctype is None but vx is not None.',\n", " 'Handle SSLError by yielding <PERSON><PERSON> and breaking out of the loop.',\n", " 'Handle older Python versions with try-except and use `readfp()`.',\n", " 'Clarify ValueError error message for specific type requirements.',\n", " 'Handle None values in the arguments passed to self.eval() method.',\n", " \"Add a decorator to skip the test if 'nds2' dependency is missing.\",\n", " 'Use getfixturevalue() instead of the deprecated getfuncargvalue()',\n", " 'Change the URL to a codepen.io link instead of fiddle.jshell.net.',\n", " 'Remove the duplicated test case for ControlA and right arrow key.',\n", " \"Add 'DeprecationWarning' to warn message to indicate deprecation.\",\n", " 'Add \"utf8\" as second argument to the read method of configparser.',\n", " \"Add a representation of the buffer's text to its __repr__ method.\",\n", " 'Remove the assignment of open_cache.__doc__ to read_cache.__doc__.',\n", " \"Replace calls to 'get_column' with indexing using square brackets.\",\n", " 'Add a test to check if the channel carries the correct parameters.',\n", " \"Fix 'Unknown feed storage scheme' when running scrapyd on Windows.\",\n", " \"Add 'showR2: true, visibleInLegend: true' in the trendline string.\",\n", " 'Add more F keys (F21 to F24) to the list of keys that are ignored.',\n", " 'Add a condition to sleep only if there is another retry remaining.',\n", " \"Add 'mpim_aware' parameter to the 'start' method of the RTM class.\",\n", " 'Change the import statements to reflect the new package structure.',\n", " 'Fix indentation in the return statement to match the original code.',\n", " \"Save the key material to a file instead of using 'key.save' method.\",\n", " 'Strip whitespace from the arguments passed to self.eval() function.',\n", " \"Add '__or__' method to the class that returns a Union of two types.\",\n", " \"Add docstring and convert message to string for 'send_text' method.\",\n", " 'Handle both tensor and non-tensor cost functions in the given code.',\n", " 'Add an else statement to handle cases where source is not a string.',\n", " 'Clarify frequency unit conversion in comments based on filter type.',\n", " 'Minor edit to auto_refresh to use refresh() instead of canvas.draw()',\n", " 'Update expected output of test_get_title to match the actual output.',\n", " 'Add tests to create, update and delete ORMQ objects in Django admin.',\n", " 'Convert non-Quantity values to float and make a new Quantity object.',\n", " \"Delete the 'data' object after it has been frozen to free up memory.\",\n", " \"Create 'chooseMonospaceFont' function to set the editor font option.\",\n", " 'Add a counter for row_id instead of using enumerate() on the reader.',\n", " \"Encode the 'arg_data' to utf8 before passing it as the request body.\",\n", " \"Add an argument '--ref' to the parser and return it in the function.\",\n", " 'Decode the signature before passing it to _gsm.getUtility() function.',\n", " \"Replace 'str' with 'basestring' to handle both str and unicode types.\",\n", " 'Remove the else statement after raising an exception in the if block.',\n", " \"Check before using 'protract' method on span for specific conditions.\",\n", " 'Update the version number to 0.6.5 and the date to December 15, 2015.',\n", " \"Remove the 'incremental' attribute from the class and its references.\",\n", " 'Import all modules separately instead of using the wildcard operator.',\n", " 'Add parentheses to properly group the conditions in the if statement.',\n", " 'Raise a more helpful error message when the column list is not given.',\n", " 'Add proper indentation to the constant explanations in the docstring.',\n", " 'Change numpy.stack to use a list comprehension instead of generators.',\n", " \"Add a missing comma in the first 'if' statement ('t,' should be 't,').\",\n", " 'Update the version number from 1.62.0 to 1.63.0 in the setup function.',\n", " 'Add a comment explaining the purpose of sending hex ASCII character 3.',\n", " \"Uncomment all test functions, except for the function named 'test_me'.\",\n", " \"Replace concatenation with string formatting for variable 'song_info'.\",\n", " 'Add a check for USER_AGENT before calling build_user_agent() function.',\n", " \"Update 'shutdown' method to pass an optional argument to Redis server.\",\n", " \"Use 'with open' statement to write to file and close it automatically.\",\n", " 'Add a docstring explaining the purpose of the module and its contents.',\n", " \"Add a check to see if the 'plan' list is of type bytes, and decode it.\",\n", " 'Use sqlalchemy.future.create_engine instead of sqlalchemy.create_engine',\n", " 'Use f-strings to format the command string in the handleCommand method.',\n", " 'Handle case where text is empty and return D(preferred=0) in that case.',\n", " \"Remove the 'elif' condition and associated lines since it is not needed.\",\n", " 'Add a check for whether data is a numpy array and set dtype accordingly.',\n", " 'Remove the redundant line of code that calls len() function on the list.',\n", " \"Cast 'path' and 'oldpwd' variables to string in the cwd context manager.\",\n", " 'Ensure script is not frozen and modify run command to prevent buffering.',\n", " \"Reword the docstring for 'create' method to reflect its actual behavior.\",\n", " \"Decode 'kind' variable before passing it to subprocess.Popen() function.\",\n", " 'Add support for setting x-axis metadata from xindex in the Series class.',\n", " 'Handle Python2 bug of crash when filenames contain non-ascii characters.',\n", " 'Check if the config directory exists and create it if it does not exist.',\n", " 'Remove the print statement and return statement inside the except block.',\n", " 'use isscalar instead of ndim to correct the logic for grouping iterables',\n", " 'Encode page_content as bytes before writing to file in the except block.',\n", " 'Forwarded set_exception_handler in Application.run to the run_async call.',\n", " \"Make 'get_random_port' a class method by adding '@classmethod' decorator.\",\n", " \"Simplify 'process_in_list' function to return the boolean value directly.\",\n", " \"Add 'epoch' parameter to the Spectrum constructor and its __new__ method.\",\n", " 'Add a check for empty stash and print a message if no commands are found.',\n", " \"Change the version of 'python-axolotl' to 0.2.1 in the dependencies list.\",\n", " 'Change the way OAUTH_FILE is defined to support Windows operating system.',\n", " 'Sort and remove duplicates from the subdomains list before printing them.',\n", " 'Handle case where limit is 0 and avoid TypeError by setting qids to uids.',\n", " 'Add a check to ensure that out and err are not None before decoding them.',\n", " \"Add a variable 'nproc' and set it to the minimum of 'nsteps' and 'nproc'.\",\n", " \"Remove the 'use_axesgrid' parameter from the ax.colorbar() function call.\",\n", " \"Remove the second argument '2.7' from the call to 'deduper.goodThreshold'.\",\n", " 'Add a seed to the random generator and import it from the top of the code.',\n", " \"Add 'unreads' parameter to the 'history' method with a default value of 0.\",\n", " \"Add the bright colors to the 'all_colors' tuple in the first line of code.\",\n", " \"Check if the imported Black package has a 'Mode' attribute before using it.\",\n", " 'Add version specific requirements for nltk package based on python version.',\n", " \"Handle both cases where 'loop' is None or not and remove 'print' statement.\",\n", " 'Add a check to see if the event loop is running before scheduling a redraw.',\n", " \"Add package data to include all '*.tpl' files in the 'shellpython' package.\",\n", " \"Change the default branch name from 'master' to 'main' in the git commands.\",\n", " \"Remove unused imports and the 'try-except' block for importing OrderedDict.\",\n", " \"Transpose the 'weights' vector before multiplying with 'em.factors.W.data'.\",\n", " \"Update regular expression to match spaces around the equals sign in 'CN ='.\",\n", " \"Check if '%' is present in the string before applying date/time formatting.\",\n", " \"Convert 'keywords' from a string to a list of keywords separated by commas.\",\n", " 'If available, use layer name instead of class name for the pydot node label.',\n", " \"Remove the 'categorical_indices' method as it is not being used in the code.\",\n", " 'Adjust position_mappings dictionary and simplify source_to_display function.',\n", " 'Add a check for Django version and decode values only if the version is 1.7.',\n", " 'Add a check for empty history list and show a message dialog if it is empty.',\n", " \"Change the version of 'futures' package to 2.2.0 for Python 2 compatibility.\",\n", " 'Convert keys to a list if it is iterable but not a string or bytes instance.',\n", " \"Add an if statement to set the 'bgColor' variable to white for light themes.\",\n", " 'Change z and p multiplication with -2*pi to -2*pi*z and -2*pi*p respectively.',\n", " 'Add a total count of items retrieved and modify the log messages accordingly.',\n", " 'Handle errors from g.run() using try-except and add assertion for validation.',\n", " \"Use 'asynchronous' instead of 'sync' to be consistent with the Redis command.\",\n", " 'Prevent event loop consumption by converting generator to list inside thread.',\n", " 'Remove the import statement for VisitationError from parsimonious.exceptions.',\n", " 'Remove the duplicate if statement that checks for self.state == STATE_STOPPED.',\n", " \"list comprehension for 'data_d' to handle multiple samples from 'data_samples'\",\n", " 'Change the default accept_action of the buffer from RETURN_DOCUMENT to IGNORE.',\n", " \"Add more packages to the 'doc' extra_require section for Sphinx documentation.\",\n", " \"Add mode='w' to the NamedTemporaryFile call for writing to the temporary file.\",\n", " \"Remove the warning assertion and add a plot before calling 'fig.add_legend()'.\",\n", " 'Add a check to only sleep if the current host is not the last one in the list.',\n", " \"Add a new property 'columns_followed' to get the columns followed by the user.\",\n", " \"Add a check for the 'REST_SESSION_LOGIN' setting before calling django_logout.\",\n", " \"Change 'include_tags(list)' to 'include_tags(bool)' in the function signature.\",\n", " 'Add a new parameter to the function that decides whether to add quotes or not.',\n", " \"Add 'num_tracks' limit and 'aggressive' flag to SoundCloud playlist downloader.\",\n", " 'Refactor assertions to remove redundant messages and add more descriptive ones.',\n", " \"Use a configuration dictionary and extra checks in the 'test_poll_next' method.\",\n", " 'Change the frequency from 60 Hz to 331.3 Hz and update description accordingly.',\n", " 'Swap the setdefault arguments in line 3 and use index 1 instead of 0 in line 7.',\n", " \"Change the directory path of the dependency files to 'bin/' instead of 'deps/'.\",\n", " \"Access the 'value' attribute of self when using numpy.pad instead of just self.\",\n", " 'Run the test with range argument only if matplotlib version is 1.5.0 or higher.',\n", " \"Override LineParser.readline to ignore comment lines in the 'readline' function.\",\n", " \"Remove the module name prefix from 'get_lock' and 'release_lock' function calls.\",\n", " 'Update response decoding and exception handling for compatibility with Python 3.',\n", " \"Replace the implementation of 'contents_of' with a call to 'read_file' function.\",\n", " 'Fix the image flipping code by changing the order of dimensions in the indexing.',\n", " 'Handle EOFError in addition to KeyboardInterrupt in the file restoration prompt.',\n", " \"Remove the '-i' option and its related code since it is not used in the program.\",\n", " 'Set corresponding attributes only when parsing the name attribute is successful.',\n", " \"Add two new test cases to check the 'initials' predicate with different lengths.\",\n", " 'Add new test cases for the CursesMenu class constructor and test its attributes.',\n", " \"Check if the 'file_' variable is a string and open it as a file handle if it is.\",\n", " 'Modify the GET request to include the payload in the URL only if it is not empty.',\n", " 'Handle cases with no x/y coordinates for content instead of using NotImplemented.',\n", " 'Add timeout parameter to requests.get() method calls to avoid long waiting times.',\n", " 'Improve SentenceValidator class with __repr__, _is_valid, and better readability.',\n", " 'Replace the split and index method with FormatISODate method of datepicker value.',\n", " 'Create a new class to run Git commands while keeping the current class unchanged.',\n", " 'Create a function to encrypt md5 for baidu rapidupload and call it after hashing.',\n", " 'Include download links for chart image and CSV file with graph title in filename.',\n", " \"Convert boolean parameters 'all' and 'participating' to string 'true' or 'false'.\",\n", " \"Set default value for 'resample' argument and use 'get' method to avoid KeyError.\",\n", " 'Add an assertion to check if the number of words is greater than the window size.',\n", " \"Set the icon theme to Humanity if <PERSON><PERSON> doesn't support non-symbolic action icons.\",\n", " 'Add a note explaining why create_win32_event() is used instead of a regular pipe.',\n", " \"Add compatibility for Python 3 by defining raw_input as input if it doesn't exist.\",\n", " 'Sort the keys of jdata in alphabetical order and update the assertion accordingly.',\n", " 'Improve the code by updating arithmetic operations, method naming, and docstrings.',\n", " \"Convert datetime objects to strings using the 'str' function instead of isoformat.\",\n", " 'Replace env[] with env.get() to make SCRAPY_LOG_FILE and SCRAPY_FEED_URI optional.',\n", " \"Add 'in_memory' as an argument to the constructor and pass it to the super() call.\",\n", " 'Unpack list/tuple in extend method and improve TypeError message in append method.',\n", " \"Change the default value of 'self.required_args' to an empty list instead of None.\",\n", " 'Close the file descriptor returned by tempfile.mkstemp() to avoid a resource leak.',\n", " \"Remove the 'other is not None' check in the return statement, as it's unnecessary.\",\n", " 'Raise exception if coroutine-producing function does not return a coroutine object',\n", " 'Allow for nproc to be 0 or negative, which will result in single-process execution.',\n", " \"Set api_version to '1.1' if it is not provided instead of checking the domain name.\",\n", " 'Move snapshot population outside of the loop to avoid unnecessary database queries.',\n", " \"Remove the 'from __future__ import unicode_literals' statement and add 'import re'.\",\n", " 'Add Python version classifiers and implementation classifiers for CPython and PyPy.',\n", " \"Change 'images_for_download_extended' to 'images_for_download_ext' for consistency.\",\n", " 'Add a check for None value in defaultMarkup before calling setDefaultMarkup method.',\n", " \"Add a cookie to the request with key 'CONSENT' and value 'YES+US.en+20170717-00-0'.\",\n", " \"Remove the 'realpath' parameter from the constructor and move it inside the method.\",\n", " 'Replace the use of attrs.update with a loop that updates attributes in the dataset.',\n", " 'Rename previous _warn method to _warn_old and add a new _warn with a format string.',\n", " 'Set daemon attribute to False for worker target process when adding it to the pool.',\n", " 'Add an additional exception handling for invalid json files in the try-except block.',\n", " 'Handle negative row index by setting it to 0 and continue with the rest of the code.',\n", " \"Add numpy.require with 'C' requirements to ensure the data is in C-contiguous order.\",\n", " \"Decode the output file content using 'utf-8' encoding to fix the UnicodeDecodeError.\",\n", " \"Add an opening parenthesis to the 'parse_args' check in the 'has_argparse' function.\",\n", " 'Add support for fetching data with a variable buffer duration and progress tracking.',\n", " 'Add importlib module and include it in extra_install_requires if ImportError occurs.',\n", " 'Add a default value for rows and columns in case the terminal reports its size as 0.',\n", " 'Change the window size to 15000 instead of 100000, and add a comment explaining why.',\n", " \"Use lazy generator in 'parallelize' function and create 'lazy_parallelize' function.\",\n", " \"Add a 'b' prefix to the string passed to 'atomic_write' method to encode it as bytes.\",\n", " \"Remove the call to 'xform_name' function and use the original paginator name instead.\",\n", " 'Add a main function and use an if statement to call it when the script runs directly.',\n", " \"Allow user to turn off progress bar in download function with '--hide-progress' flag.\",\n", " 'Check if start is not None before attempting to convert it into a LIGOTimeGPS object.',\n", " \"Check for 'get_details' first in the if-else block and return the sequence when True.\",\n", " 'Fix the test_loops method to correctly count the number of edges with specific labels.',\n", " \"Fix the appearance of sanitized passwords to '<omitted>' in the html reporting module.\",\n", " \"Add '-a' flag to 'git branch' command in the 'run' method of GitOpenFileCommand class.\",\n", " \"Change import statement to use 'parse' instead of 'parser' for Python 3 compatibility.\",\n", " 'Encode the title to UTF-8 to avoid errors when the title contains non-ASCII characters',\n", " 'Refactor the _get_naxes method to handle inputs more flexibly and improve readability.',\n", " 'Add DOCTYPEs and fix minor typo in default_404_handler & default_error_handler methods',\n", " 'Use local web server instead of user input for access token callback in authorization.',\n", " \"Rearrange 'put' method arguments and update the corresponding assertion argument names.\",\n", " 'Remove the TODO comment about authorizing multiple domains as it is no longer relevant.',\n", " 'Remove RemoteSudo_ class and add test cases to Remote_ class according to the comments.',\n", " 'Change the description of the package to \"Use JSON files as if they\\'re python modules\".',\n", " 'Ensure proper task assignment by adjusting sleep time and adding an extra dequeue call.',\n", " \"Disable FastAPI documentation by setting the 'docs' and 'redoc_url' parameters to None.\",\n", " \"Update the version number to 0.18.0 and add a docstring for the 'get_version' function.\",\n", " 'Add a check for the existence of callback_function before calling it in the else block.',\n", " \"Add a 'pad' variable and multiply the padding array with it to fill the gap with zeros.\",\n", " 'Fix the function name in the third test case to match the actual function being tested.',\n", " \"Modify the getter method to handle KeyError and return None if 'name' key is not found.\",\n", " 'Tighten up tunnel shutdown. Make sure to close the socket and channel in case of error.',\n", " \"Change the lz4 version to be less than or equal to 0.8.2 in the 'install_requires' list.\",\n", " 'Sort dictionary keys in alphabetical order and reformat the code for better readability.',\n", " '<PERSON><PERSON> clicks below the content area by looking for a position in the last line instead.',\n", " \"Add a check for empty result before returning the callback in 'resultCallback' function.\",\n", " 'Fix the slice operation to correctly update the x0 value when a start index is provided.',\n", " \"Change the import statements to use 'django_q' as the package name instead of 'brokers'.\",\n", " 'Integrate Pyinstaller, set default path for libtesseract-3.dll, and add logging warning.',\n", " \"Add a test for auto-discovery of 'time' columns and assert that 'time' is in t3.columns.\",\n", " \"Add a new parameter 'jvm_version' to the start() method and pass it to the super() call.\",\n", " 'Update the required versions of Theano and Lasagne libraries to 0.7 and 0.1 respectively.',\n", " 'Handle multiple PTR records returned by the DNS resolver and catch additional exceptions.',\n", " 'Change `self.text` to `self.document.text_before_cursor` in `_set_history_search` method.',\n", " \"Add a comma after 'self.output_dim' in the initialization of self.b_y to make it a tuple.\",\n", " 'Add GZIP file signature check, close original file, and update docstring in the function.',\n", " \"Add the channel members to the server attachment in the 'elif' cases of 'if self.server'.\",\n", " 'Add a check for MKL in the system and disable multiprocessing if it is linked with <PERSON>umpy.',\n", " \"Use deque(reversed(items)) to handle absence of 'reverse' method in Python 2.6 and below.\",\n", " 'Refactor the code to use a single query statement instead of multiple if-else statements.',\n", " 'Set async_result.get a 3e+6 seconds timeout so that KeyboardInterrupts are handled better',\n", " \"Replace the '**' operator with 'pow()' function for better readability and compatibility.\",\n", " 'Add a check to see if the alternative trash directory exists before yielding it as found.',\n", " \"Add a deprecation warning for the 'imshow' keyword argument and replace it with 'method'.\",\n", " 'Add an __init__ method to the GWpyContentHandler class that issues a deprecation warning.',\n", " 'Unpack the first two elements of line_ranges tuple and assign them to separate variables.',\n", " 'Add input validation to check if hostname and port are provided as command line arguments.',\n", " \"Add a check for '>Apache Status<' in response text to detect possible Apache status pages.\",\n", " \"Add a default value for 'unit' parameter in the '__new__' method of StateTimeSeries class.\",\n", " \"Set default file name to the current tab's base name + '.pdf' if no file name is provided.\",\n", " \"Remove 'Programming Language :: Python :: 3.5' from the list as it is no longer supported.\",\n", " \"Remove the list comprehension in '_is_help_query' function to fix the pylint rule failure.\",\n", " \"Add a 'copy' parameter to the 'to_lal' method and modify the return statement accordingly.\",\n", " 'Refactor the function to use proper Python 3 syntax for function arguments and docstrings.',\n", " \"Update the test to check for the presence of 'Content-Length' header instead of its value.\",\n", " \"Add an optional 'meta' parameter to assertTableEqual method to compare metadata of tables.\",\n", " \"Change the name of the script from 'tesseract.py' to 'pytesseract.py' in the usage message.\",\n", " \"Remove the line 'import pdb' and 'pdb.set_trace()' as they are used for debugging purposes.\",\n", " \"Change 'welch' to 'scipy-welch' in the q_transform method and update asd calls accordingly.\",\n", " 'Add more test cases for different key combinations and commands to check the functionality.',\n", " \"Modify the 'triggers' function to return the truth value of all triggers combined with AND.\",\n", " 'Apply the factor of two to the DC component before undoing normalization in TimeSeries.fft.',\n", " 'Use if statements for optional integer and boolean parameters. Remove unnecessary comments.',\n", " \"Add a check to ensure that the 'dat' array is not empty before performing operations on it.\",\n", " \"Remove the 'initial_state' parameter from the 'get_graph' method and its usage in the code.\",\n", " \"Replace 'char' with 'char_obj' in the loop and ensure character is not None or transparent.\",\n", " \"If IPv4 bind but IPv6 succeeds don't error (using a try-except block to catch the exception)\",\n", " 'Avoid unnecessary loops and handle cases where there are no credentials for a given profile.',\n", " \"Add 'num_required_cols' and 'num_optional_cols' as arguments to the 'populate' method calls.\",\n", " 'Refactor by passing HTTPUploaderData as a parameter instead of creating a separate variable.',\n", " 'Replace the memoization decorator with a simpler one to ensure constant cashing on metadata.',\n", " 'Handle KeyboardInterrupt exception in the prompt_for_code method and reset the input buffer.',\n", " \"Add a parameter called 'file_name' to the send_data function for naming files while sending.\",\n", " 'Use dictionaries instead of lists for mapping candidate IDs and their corresponding indices.',\n", " 'Fix the ValueError message to include \"epoch is None\" instead of \"data scaling is required\".',\n", " \"Add a new test case 'lazily_caches_result' to check if the sftp connection is lazily cached.\",\n", " 'Add a try-except block to detectFileEncoding method to handle OSError when opening the file.',\n", " \"Verify git object existence prior to update and fix 'non-fast-forward' to 'non-fast forward'.\",\n", " 'Add a variable to store the current widget and use it to remove the file system watcher path.',\n", " 'Fix the bug that causes inconsistent error messages when loading modules with runtime errors.',\n", " 'Remove unwanted input values before adding data to the database and check for remaining data.',\n", " 'Add pytest.warns context manager to catch Deprecation<PERSON><PERSON>ning and update the test accordingly.',\n", " \"Fix indentation by adding proper spacing before the second 'file.blob.upload_from_file' call.\",\n", " \"Return None if items is an empty list instead of checking if it's not equal to an empty list.\",\n", " 'Replace dict.setdefault() with defaultdict(set) to improve performance and simplify the code.',\n", " 'Add a check to see if self.master is not None before removing the reader from the event loop.',\n", " \"Encode 'pub_exp' and 'pub_hex' as utf-8 before passing them to binascii.unhexlify() function.\",\n", " \"Remove the 'fields' attribute from the class initialization and remove its usage in the code.\",\n", " 'Add an example of how to describe parameters using Google, Sphinx, or Numpy style docstrings.',\n", " \"Replace 'fport.write' with the 'print' function to write to file in the last line of the code.\",\n", " 'Print the final_set and compare it with the expected output instead of using assert statement.',\n", " \"Change the uuid import statement to use 'django_q.humanhash.uuid' instead of 'humanhash.uuid'.\",\n", " \"Check if desktop_session is equal to 'default' and 'XDG_CURRENT_DESKTOP' environment variable.\",\n", " \"Add 'description=' before playlist_description in the sp.user_playlist_create() function call.\",\n", " 'Import LinearTimeInvariant from the correct module depending on the version of scipy installed.',\n", " \"Remove the 'test_until_the_restore_intgration' method as it is a misspelled and redundant test.\",\n", " 'Change default value for selected_values and reorder conditional statements in values property.',\n", " 'Remove the conditional statement for checking MongoDB version and always use the command query.',\n", " 'Fix the POINT win32 data structure (used for xy coordinates), it is using ulong instead of long',\n", " \"Fix the indentation of the second line in the '_get_result_mock' function to use 'Path' object.\",\n", " \"Remove the 'local_interrupts_send_ETX_to_remote_pty' function as it is not needed for the task.\",\n", " \"Replace the 'head_object' method with a waiter to wait until the object exists in the S3 bucket.\",\n", " \"Define 'format_date', 'format_original_location' and 'shrink_user' functions before their usage.\",\n", " \"Replace 'Fatal' function with 'raise Fatal' to raise an exception instead of calling a function.\",\n", " \"Refactor key bindings for incremental search by creating a helper function 'incremental_search'.\",\n", " 'Add a check to avoid saving unsaved views. Also, format the if statement for better readability.',\n", " 'Raise a ValueError with a more informative message that includes the declared set of categories.',\n", " 'Resolve pylint complaint, initialise output as list of 2 lists, rather than appending on-the-fly',\n", " 'Scrape more data about the IP address and add detected blacklisted sites to the displayed table.',\n", " 'Modify the major formatter by rearranging the epoch settings and updating the xlabel accordingly.',\n", " 'Replace the hardcoded values in `test_value_at` with the corresponding values from the test data.',\n", " \"Add 'mysql-connector': 'mysql.connector.django' in the dictionary of supported database backends.\",\n", " 'Replace multiprocessing.Manager().dict() with {} for wildcards and spider_blacklist dictionaries.',\n", " \"Add an exception handling block for InvalidMessageException in the 'group_create_skmsg' function.\",\n", " \"don't skip ValueError when urlopen(Request(nonvalid, ...)) to fix error handling for json parsing\",\n", " \"Fix the windows-curses issue in vscode terminal by updating the 'user_input_handlers' dictionary.\",\n", " \"Use getattr() to return the integer value of self.tag if 'name' is missing in the __repr__ method.\",\n", " \"Use native_stringify_dict to convert byte strings and pass 'egg' key as a separate BytesIO object.\",\n", " 'Change the condition to check if the user is not an administrator instead of checking if they are.',\n", " \"Replace the deprecated sha module with <PERSON><PERSON><PERSON>'s sha1 and update the import statement accordingly.\",\n", " 'Move the progress bar outside of the loop over segments and update the total duration accordingly.',\n", " \"Change 'charset' to 'encoding' in the Redis client initialization and update the test accordingly.\",\n", " 'Fix the formatting of the dictionary keys and values to use double quotes instead of single quotes.',\n", " \"Add a new key-value pair to the dictionary with 'unbound' as key and '{domain} IN A {ip}' as value.\",\n", " 'Change the logic of __or__ method to compare the length of active elements instead of all elements.',\n", " \"Cast the 'path' argument to a string before passing it as an argument to os.chdir() and Path.cwd().\",\n", " 'Add encoding=\"utf-8\" to the open function calls to avoid UnicodeDecodeError and UnicodeEncodeError.',\n", " 'Remove the special handling for OR and AND in search, by removing the quotation marks around terms.',\n", " \"Change the calculation of 'snr_chi' to use chisq raised to half of chi_pow instead of just chi_pow.\",\n", " \"Add 'PyQt5' to the install_requires list and conditionally include it based on the platform_system.\",\n", " \"Add a 'weight' parameter to the LayoutDimension constructor and include it in the return statement.\",\n", " \"Simplify the 'unit' setter method by using a conditional expression instead of an if-else statement.\",\n", " \"Detect the type of 'val' and open the file in the appropriate mode in the 'writePhoneData' function.\",\n", " \"Add '@unittest.skipUnless(HAS_NDS2, 'No module named nds2')' decorator to the test_connect function.\",\n", " \"Add raw string notation to the regular expression pattern and escape the dot in 'stackoverflow.com'.\",\n", " \"Add the 'putSecretAutoversion' function to support autoversioning when putting secrets to credstash.\",\n", " 'Add a maximum version for numpy (<=1.13.3) in the setup_requires list to avoid compatibility issues.',\n", " \"Create a 'rep' helper function to show function name instead of object in '_transition_label' method.\",\n", " 'Add __repr__ method to ConditionalContainer class that returns a string representation of the object.',\n", " \"Replace 'git-flow' with a custom command and remove 'flow' from the list to add support for git-flow.\",\n", " 'Add code to update the value of self._mouse_support_enabled when enabling or disabling mouse support.',\n", " \"Use 'inet_pton' instead of 'inet_aton' for IP addresses starting with zero in the socket family check.\",\n", " 'Add a check to verify if the LIGO_DATAFIND_SERVER environment variable is set before running the test.',\n", " \"fix crash when config path doesn't exist, s.t. ConfigManager will log the error and return None instead\",\n", " \"Handle the case where uv<PERSON> doesn't have the _ready attribute by using getattr to check its existence.\",\n", " \"Refactor the code by calling 'parse_name' method to extract first, middle and last name from full name.\",\n", " \"Check if 'username' or 'title' key exists in the chat dictionary before accessing it, to avoid KeyError.\",\n", " \"Replace equality check with numpy.isclose() function for float comparison in the 'is_contiguous' method.\",\n", " 'Add a condition to check if the input argument is a string with the correct format before processing it.',\n", " 'Change the format of keywords to a list separated by commas instead of a string separated by whitespace.',\n", " 'Add compatibility for pandas versions 0.20.x and below by importing get_timezone from the correct module.',\n", " 'Adjust zoom_factor calculation and set the same zoom factor for both x and y in normalise_zoom_transform.',\n", " \"Refactor the test cases into two separate classes named 'TestEmptyCmd_with_help' and 'TestTrashEmptyCmd'.\",\n", " \"Change the seed values for random and numpy.random to 7 instead of 6 in the 'test_randomSample' function.\",\n", " \"Add a docstring to the function 'get_backend_mod' including its description, parameters and return value.\",\n", " 'Replace deprecated ssl.wrap_socket with ssl.create_default_context and disable SSLv2 and SSLv3 protocols.',\n", " 'Refactor the _blockedSample method to use a more efficient blocking strategy and remove unnecessary code.',\n", " 'Swap the order of (END, pymongo.ASCENDING) and (SEGMENT, pymongo.ASCENDING) in the second index creation.',\n", " 'handle bits as `None` which explicitly check for the existence of the attribute regardless of the setting',\n", " \"Add the ability to use a place_id as destination by using 'place_id' as a prefix in the passed parameter.\",\n", " 'Add a comment explaining the purpose of increasing the recursion limit and remove unnecessary whitespace.',\n", " \"Change the return type of '_convert_target_sticker' function to 'GuildSticker' instead of 'StageInstance'.\",\n", " \"Remove '- batch_size + 1' from the 'range' function to allow for last batch to be smaller than batch_size.\",\n", " 'Add a try-except block to handle AttributeError and use deque(reversed(items)) instead of items.reverse().',\n", " \"Add support for returning link and position in addition to answer in the 'test_multiple_answers' function.\",\n", " \"Swap the order of 'query_dqsegdb' and 'query_segdb' in the if-else statement to check for DB2 server first.\",\n", " \"Modify the 'imread' function to convert grayscale images to RGB by stacking three copies of the same image.\",\n", " \"Add properties 'unit' and 'channel' to the TimeSeries class with appropriate getters, setters, and deleters.\",\n", " \"Add a check for 'mk' object before trying to decrypt it with password in the 'try_credential_hash' function.\",\n", " 'Add https proxies to both TelegramAPI and ZabbixAPI if proxy_to_tg or proxy_to_zbx is set in zbxtg_settings.',\n", " 'Refactor the initialization of Native class to use **keywords instead of explicitly defining each attribute.',\n", " 'feat(table): Improve `len(table)` performance by using the storage directly to count the number of documents.',\n", " \"Create a method to evaluate the model with printed test loss and perplexity when 'forward_only' flag is True.\",\n", " \"Add a try-except block to import 'unquote' from either 'urllib' or 'urllib.parse' for Python 3 compatibility.\",\n", " \"Modify the 'read_cache' function to parse the file as a cache with column type float and catch all exceptions.\",\n", " 'Add a print statement to display the data_model variable and change the input parameters for the run function.',\n", " \"Change 'new.x0 -= self.dx * pad_width[0]' to 'new.x0 = new.x0 - self.dx * pad_width[0]' in the first function.\",\n", " 'Add a try-finally block to ensure that the process pool is terminated and joined, even if an exception occurs.',\n", " \"Handle the case where filename does not contain a '-' separator by returning empty observatory and description.\",\n", " 'Add two new test cases to the existing module, one for task finishing before timeout and another for recycling.',\n", " \"Extract the '__validate_md5' code into a new function called 'validate_md5' and modify 'encrypt_md5' to use it.\",\n", " \"Handle AttributeError when 'lal.DimensionlessUnit' is not available and use 'lal.lalDimensionlessUnit' instead.\",\n", " 'Handle JSONDecodeError in _json_from_message function and log a warning message instead of raising an exception.',\n", " 'Use a new Document object in the method and update the cursor position in a separate variable before setting it.',\n", " 'Fix xpending_range() to use the correct parameters as XPENDING range queries no longer allow COUNT to be infinite',\n", " \"Modify 'clear' function to delete all jobs first, then delete specific tagged jobs. Update docstring accordingly.\",\n", " \"Add type hinting for '_path' attribute as Tu<PERSON>[str, ...] and add a type hint for the 'notest' function argument.\",\n", " 'Update the version number from 3.0 to 3.1 and change the description from \"Python wrapper\" to \"Python framework\".',\n", " 'Add hostname and username parameters to the Connection object initialization and modify the docstring accordingly.',\n", " \"Change the default values of 'unique' and 'nulls' options in the List Creator class to True and False respectively.\",\n", " \"Move the definitions of 'create_driver' and 'capture_host' inside the while loop to avoid potential race conditions.\",\n", " \"Assign the value of 'self.options['store_column']' to a variable named 'column' before using it in the if statement.\",\n", " \"Update the comments to reflect Python 3 instead of Python 2 and remove the 'register' command from the instructions.\",\n", " \"Add a boolean 'sync' parameter to flushall() and flushdb() methods to execute operation synchronously by the server.\",\n", " \"Replace 'True', 'False' and 'None' with boolean values. Use assertIs instead of assertEqual to check for None value.\",\n", " \"Delete the 'matches' object after removing the file to avoid potential issues with the object still being in memory.\",\n", " \"Add support for empty auth plugin name and separate handling for 'mysql_native_password' and 'caching_sha2_password'.\",\n", " 'Change the default foreground color to GRAY on Windows and use FOREGROUND_COLOR.GRAY as the default foreground index.',\n", " 'Correct the loop to remove keys from blocked_dict_1 and blocked_dict_2, and add a check to remove empty dictionaries.',\n", " \"Prevent processing of an empty 'records' iterable and get the first element using the 'peek' function from itertools.\",\n", " \"Add a method 'triggerPreviewUpdate' to the class and call it in 'disableLivePreview' and 'enableLivePreview' methods.\",\n", " \"Add a method named 'setup' to the class 'identity_file', and move the initialization of 'expected' attribute into it.\",\n", " \"Use POST instead of GET and 'data' instead of 'params' for 'stars.add' and 'stars.remove' methods in the Stars class.\",\n", " 'Add *args and **kwargs to the set_xscale method signature to allow for passing additional arguments to the super call.',\n", " \"Extract the test case for Hungarian clustering algorithm into a new method 'test_hungarian' in the BlockingTest class.\",\n", " \"Update _round function to round inwards/outwards depending on contract parameter and return 'null' for short segments.\",\n", " 'Refactor the function to use bytearrays instead of strings, and simplify the logic for removing the trailing CRLF pair.',\n", " \"Add a new test case 'test_import_package' to check if the package can be imported correctly using jsonsempai.imports().\",\n", " 'Add a long description to the setup function by reading README.md (if it exists) and update the version number to 0.1.6.',\n", " 'Update `get_version` to extract version number from the `__init__.py` file in the `corsheaders` folder using a filename.',\n", " \"Handle the case where 'signal' module does not have a 'SIGWINCH' attribute by setting '_previous_winch_handler' to None.\",\n", " 'Change the version requirements of Django and djangorestframework to be greater than or equal to the specified versions.',\n", " 'Handle multiple values for options and check the length of values before accessing them in case of \"message-type\" option.',\n", " \"Assign the value of 'columns' key in kwargs to the 'columns' key in reckwargs. If it does not exist, use a default value.\",\n", " 'Add \"expiresIn\" key with value of 3600 to the dictionary passed in the send_message function inside the callback function.',\n", " 'Create a test method to check compatibility with matplotlib by iterating over four plotting methods and closing the figure.',\n", " 'Refactor the code to use a single dictionary for the filter parameter in pymongo.UpdateOne() instead of separate variables.',\n", " 'Add exception handling for aiohttp.ClientConnectionError and raise a TelegramError with appropriate message and status code.',\n", " \"Remove the conditional statement for 'self.azure_protocol' and include it as a parameter in the make_blob_url function call.\",\n", " 'Change the function from an encoder to a decoder and use json.JSONEncoder.encode instead of returning python_object directly.',\n", " 'Hide and recover SerDe when calling _new_java_obj and _call_java to avoid issues with SparkContext in version 2.0.0-SNAPSHOT.',\n", " \"Add an if statement to handle empty input strings for 'needle_scope' and 'haystack_scope' in the '_is_scope_subset' function.\",\n", " 'Replace `tempfile.NamedTemporaryFile` with `utils.TemporaryFilename` in the code to avoid issues with file deletion on Windows.',\n", " \"Add a new test case 'test_remove_file_if_exists_fails_when_file_does_not_exist' to check if the file exists before removing it.\",\n", " \"Change 'size=1' to no argument for DeferredQueue initialization, and change 'if self.dq.pending:' to 'if not self.dq.waiting:'.\",\n", " \"Comment out the explicit logging setup in the code and remove the 'credstash.log' argument from the setup_logging function call.\",\n", " \"Make the class naming conventions consistent, remove extra underscores, and update _pickle_ignore by changing '_graph' to 'graph'.\",\n", " 'Limit the wait time to a maximum of 2147483647 milliseconds (the maximum value for QTimer.singleShot) in the _start_timer function.',\n", " 'Import all modules in the dedupe.variables package using pkgutil.iter_modules and OrderedDict instead of importing them one by one.',\n", " \"Simulate '_get_result' function, set up with the mock function, and define 'HTML_CACHE_PATH' and 'format_url_to_filename' functions.\",\n", " \"Make a function that picks a random background image and use it in the 'generate_im' function instead of reading the image from disk.\",\n", " 'Simplify headers and remove unnecessary Referer header. Also, call resp.json() instead of resp.json to get the JSON response content.',\n", " \"Pass 'token_info' argument to the 'is_token_expired' and '_add_custom_values_to_token_info' methods instead of using self.token_info.\",\n", " \"Remove 'output_should_contains_trashinfo_paths' and 'output_should_contains_backup_copy_paths' test methods along with their contents.\",\n", " \"Add a try/except block to handle the scenario where 'self.branch.commit' raises TypeError by returning 'self.repo.head.commit' instead.\",\n", " \"Improve the 'client_kill_filter' method's readability with better parameters and updated docstring. Use a boolean for 'skipme' parameter.\",\n", " \"Add a new test case 'test_transfer_methods_do_not_use_threads' to check if setting use_threads to False has no issues transferring files.\",\n", " \"Create a new 'get_tokens' generator function that yields tokens from the lexed lines using 'get_tokens_unprocessed' instead of 'get_tokens'.\",\n", " \"Add a condition to create multiprocessing.Manager().dict() for 'wildcards' and 'spider_blacklist' only if the operating system is not Windows.\",\n", " \"Warn users in the function's docstring about suppressing 'EditReadOnlyBuffer' exception when editing read-only buffer without 'bypass_readonly' argument.\",\n", " 'Set ARCTIC_FORWARD_POINTERS_RECONCILE to False and ARCTIC_FORWARD_POINTERS_CFG to FwPointersCfg.DISABLED, remove the try-except block and the logger.exception statement.',\n", " \"Add two new test cases 'uses_configured_key_as_pkey' and 'uses_gateway_channel_as_sock_for_SSHClient_connect' to verify that the 'key' parameter is passed to SSHClient.connect() method.\"]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted(ic_instructions, key=lambda x: len(x))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["IC_SEED_INSTRUCTIONS = [\n", "    \"Fix typo\",\n", "    \"Add an error handler\",\n", "    \"Catch Jedi SystemError.\",\n", "    \"Fix bug in data setting\",\n", "    \"fix the bug of infinite loop\",\n", "    \"Make the 'do' method static.\",\n", "    \"Remove unnecessary comments.\",\n", "    \"remove try/except in test_psd\",\n", "    \"Fix typo in exception message.\",\n", "    \"Reformat to make flake8 happy.\",\n", "    \"consistent task name formatting\",\n", "    \"Catch exception on hook failure.\",\n", "    \"Fix an index out of bounds error\",\n", "    \"add choices for --average-method\",\n", "    \"change CURRENT_SEASON to 2016-17\",\n", "    \"Make the condition more readable.\",\n", "    \"Remove the debug print statement.\",\n", "    \"Validate input before assignment.\",\n", "    \"Fix ImportError when using Pillow.\",\n", "    \"Add proper indentation to the code.\",\n", "    \"use / instead of // in rotate_bound\",\n", "    \"Remove the import of nddata module.\",\n", "    \"Handle exception while getting repo.\",\n", "    \"simplify by ignoring the except cases\",\n", "    \"Remove the workaround for ParseError.\",\n", "    \"Cast the output of <PERSON>.stack to \\\"int8\\\".\",\n", "    \"Replace 'self' with 'repl' in the code.\",\n", "    \"Update the version number to \\\"*******\\\".\",\n", "    \"Fix wrong typehint of icon_custom_emoji_id\",\n", "    \"Change 'asd' to 'spec' in the example code.\",\n", "    \"Set test intervals to 5 minutes for 5 to 30.\",\n", "    \"Use OperationalError for error code >= 1000.\",\n", "    \"Replace self.app.renderer with self.renderer\",\n", "    \"Replace 'chat' with 'm' in the if statements.\",\n", "    \"Convert the output of zip function to a list.\",\n", "    \"Set the title size to 26 and grid color to gray\",\n", "    \"Add type annotations and function return types.\",\n", "    \"Arrange import statements in alphabetical order.\",\n", "    \"Fix the case where the output is not valid JSON.\",\n", "    \"use min/max rather than conditiional assignment.\",   \n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Eval"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["EV_SEED_INSTRUCTIONS = [\n", "    \"use pathlib library to open file\",\n", "    \"add missing import\",\n", "    \"use string format() instead of f-string\",\n", "    \"make all for loops enumerate\",\n", "    \"Add single-dash flags to all arguments\",\n", "    \"Change log level to error.\",\n", "    \"Change log level to DEBUG.\",\n", "    \"Make all fields in lowercase\",\n", "    \"Change all local variables to camel case.\",\n", "    \"Replace prints with logging.error\",\n", "    \"Make method f2() static\",\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Add inverse instructions"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["82"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["SEED_INSTRUCT_DATASET = DF_SEED_INSTRUCTIONS + IC_SEED_INSTRUCTIONS + EV_SEED_INSTRUCTIONS\n", "\n", "len(SEED_INSTRUCT_DATASET)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["['Add type annotations to this function',\n", " 'Change log level to error',\n", " 'Change np.mean to tf.mean',\n", " 'Change np.mean to torch.mean',\n", " 'Change numbers into floats',\n", " 'Change numbers to strings',\n", " 'Change variables to camel case',\n", " 'Replace np.mean with torch.mean',\n", " 'Write it in one line',\n", " 'add a comment explaining the code',\n", " 'add error handling',\n", " 'add missing import',\n", " 'add the type annotations',\n", " 'change double quote to single',\n", " 'change single quote to double quote',\n", " 'change string to number',\n", " 'convert to python code',\n", " 'delete all the type annotations',\n", " 'remove tqdm',\n", " 'replace \" with \\' anywhere',\n", " 'replace double quotes with single quote',\n", " 'rewrite using pathlib',\n", " 'turn this into Python code',\n", " 'use # instead of ##',\n", " 'use ## instead of #',\n", " 'use a 2 space indent',\n", " 'use a variable to make the if condition more readable',\n", " 'use format instead',\n", " 'use pathlib library to open file',\n", " 'write more comments',\n", " 'write this in python',\n", " 'Fix typo',\n", " 'Add an error handler',\n", " 'Catch Jedi SystemError.',\n", " 'Fix bug in data setting',\n", " 'fix the bug of infinite loop',\n", " \"Make the 'do' method static.\",\n", " 'Remove unnecessary comments.',\n", " 'remove try/except in test_psd',\n", " 'Fix typo in exception message.',\n", " 'Reformat to make flake8 happy.',\n", " 'consistent task name formatting',\n", " 'Catch exception on hook failure.',\n", " 'Fix an index out of bounds error',\n", " 'add choices for --average-method',\n", " 'change CURRENT_SEASON to 2016-17',\n", " 'Make the condition more readable.',\n", " 'Remove the debug print statement.',\n", " 'Validate input before assignment.',\n", " 'Fix ImportError when using Pillow.',\n", " 'Add proper indentation to the code.',\n", " 'use / instead of // in rotate_bound',\n", " 'Remove the import of nddata module.',\n", " '<PERSON>le exception while getting repo.',\n", " 'simplify by ignoring the except cases',\n", " 'Remove the workaround for ParseError.',\n", " 'Cast the output of T<PERSON>stack to \"int8\".',\n", " \"Replace 'self' with 'repl' in the code.\",\n", " 'Update the version number to \"*******\".',\n", " 'Fix wrong typehint of icon_custom_emoji_id',\n", " \"Change 'asd' to 'spec' in the example code.\",\n", " 'Set test intervals to 5 minutes for 5 to 30.',\n", " 'Use OperationalError for error code >= 1000.',\n", " 'Replace self.app.renderer with self.renderer',\n", " \"Replace 'chat' with 'm' in the if statements.\",\n", " 'Convert the output of zip function to a list.',\n", " 'Set the title size to 26 and grid color to gray',\n", " 'Add type annotations and function return types.',\n", " 'Arrange import statements in alphabetical order.',\n", " 'Fix the case where the output is not valid JSON.',\n", " 'use min/max rather than conditiional assignment.',\n", " 'use pathlib library to open file',\n", " 'add missing import',\n", " 'use string format() instead of f-string',\n", " 'make all for loops enumerate',\n", " 'Add single-dash flags to all arguments',\n", " 'Change log level to error.',\n", " 'Change log level to DEBUG.',\n", " 'Make all fields in lowercase',\n", " 'Change all local variables to camel case.',\n", " 'Replace prints with logging.error',\n", " 'Make method f2() static']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["SEED_INSTRUCT_DATASET"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# I did asked ChatGPT4 to generate inverse instructions to each instruction in the `SEED_INSTRUCT_DATASET` using this prompt:\n", "#   ```\n", "#      Here is a list of instructions that I want to apply to a piece of code\n", "#      I need you to convert each instruction into a python list and as a second element, add reverse instruction (opposite to a provided one)\n", "#      Please, return me a python list in a single code snippet. Also, make sure that instruction and corresponding reverse instruction are independent of each other and self-contained (it means, do not use phrased like \"change back\" in reverse instruction)\n", "#   ```\n", "# And the manually filtered them to remove duplicates and ones with improper instruction or inverse instruction\n", "\n", "SEED_INSTRUCT_AND_INVERSE_DATASET = [\n", "    ['Add type annotations to this function', 'Delete type annotations from this function', [\"Formatting-Cleaning\"]], \n", "    ['Change log level to error', 'Change log level to info', [\"Logging-Debugging\"]],\n", "    # ['Change np.mean to tf.mean', 'Change tf.mean to np.mean'],\n", "    ['Change np.mean to torch.mean', 'Change torch.mean to np.mean', [\"Refactoring\", \"Library-Integration\"]],\n", "    ['Change numbers into floats', 'Change floats into numbers', [\"Refactoring\"]],\n", "    ['Change numbers to strings', 'Change strings to numbers', [\"Refactoring\"]],\n", "    ['Change variables to camel case', 'Change variables to snake case', [\"Refactoring\", \"Formatting-Cleaning\"]],\n", "    # ['Replace np.mean with torch.mean', 'Replace torch.mean with np.mean'],\n", "    ['Write it in one line', 'Format the code into multiple lines', [\"Refactoring\"]],\n", "    ['Add a comment explaining the code', 'Remove the comment explaining the code', [\"Documentation\"]],\n", "    ['Add error handling', 'Remove error handling', [\"Error-Handling\"]],\n", "    ['Add missing import', 'Remove one of the necessary imports', [\"Bug-Fixing\"]],\n", "    ['Add the type annotations', 'Remove the type annotations', [\"Formatting-Cleaning\"]],\n", "    # ['Change double quote to single', 'Change single quote to double'],\n", "    # ['Change single quote to double quote', 'Change double quote to single quote'],\n", "    # ['Change string to number', 'Change number to string'],\n", "    ['Convert to python code', 'Convert from python code to pseudocode', [\"Translation\"]],\n", "    ['Delete all the type annotations', 'Add type annotations', [\"Formatting-Cleaning\"]],\n", "    ['Remove tqdm', 'Add tqdm', [\"Library-Integration\"]],\n", "    ['Replace \" with \\' anywhere', \"Replace ' with \\\" anywhere\", [\"Formatting-Cleaning\"]],\n", "    ['Replace double quotes with single quote', 'Replace single quote with double quote', [\"Formatting-Cleaning\"]],\n", "    ['Rewrite using pathlib', 'Rewrite without using pathlib', [\"Library-Integration\", \"Refactoring\"]],\n", "    ['Turn this into Python code', 'Convert the Python code to another programming language', [\"Translation\"]],\n", "    ['Use # instead of ##', 'Use ## instead of #', [\"Formatting-Cleaning\"]],\n", "    # ['Use ## instead of #', 'Use # instead of ##'],\n", "    ['Use a 2 space indent', 'Use a 4 space indent', [\"Formatting-Cleaning\"]],\n", "    ['Use a variable to make the if condition more readable', 'Inline the variable in the if condition', [\"Refactoring\"]],\n", "    ['Use format instead', 'Use string concatenation instead', [\"Refactoring\"]],\n", "    ['Use pathlib library to open file', 'Use traditional file opening methods instead of pathlib', [\"Library-Integration\", \"Refactoring\"]],\n", "    ['Write more comments', 'Reduce the number of comments', [\"Documentation\"]],\n", "    # ['Write this in python', 'Translate this Python code into another programming language'],\n", "    ['Fix typo', 'Introduce a typo', [\"Bug-Fixing\"]],\n", "    ['Add an error handler', 'Remove the error handler', [\"<PERSON><PERSON><PERSON>-Handling\"]],\n", "    ['Catch Jedi SystemError.', 'Remove catch for Jedi SystemError.', [\"Error-Handling\"]],\n", "    ['Fix bug in data setting', 'Introduce a bug in data setting', [\"Bug-Fixing\"]],\n", "    ['Fix the bug of infinite loop', 'Introduce a bug that causes an infinite loop', [\"Bug-Fixing\"]],\n", "    [\"Make the 'do' method static.\", \"Make the 'do' method non-static.\", [\"Refactoring\"]],\n", "    ['Remove unnecessary comments.', 'Add unnecessary comments.', [\"Documentation\"]],\n", "    ['Remove try/except in test_psd', 'Add try/except in test_psd', [\"Error-Handling\", \"Refactoring\"]],\n", "    ['Fix typo in exception message.', 'Introduce a typo in exception message.', [\"Bug-Fixing\", \"Error-Handling\"]],\n", "    ['Reformat to make flake8 happy.', 'Reformat to make flake8 unhappy.', [\"Formatting-Cleaning\"]],\n", "    ['Consistent task name formatting', 'Inconsistent task name formatting', [\"Refactoring\", \"Formatting-Cleaning\"]],\n", "    ['Catch exception on hook failure.', 'Remove exception catching on hook failure.', [\"Error-Handling\"]],\n", "    ['Fix an index out of bounds error', 'Introduce an index out of bounds error', [\"Bug-Fixing\", \"Error-Handling\"]],\n", "    ['Add choices for --average-method', 'Remove choices for --average-method', [\"Refactoring\"]],\n", "    # ['Change CURRENT_SEASON to 2016-17', 'Change CURRENT_SEASON back to its original value'],\n", "    ['Make the condition more readable.', 'Make the condition less readable.', [\"Refactoring\", \"Formatting-Cleaning\"]],\n", "    ['Remove the debug print statement.', 'Add a debug print statement.', [\"Logging-Debugging\"]],\n", "    ['Validate input before assignment.', 'Remove input validation before assignment.', [\"Hardening\"]],\n", "    ['Fix ImportError when using Pillow.', 'Introduce ImportError when using Pillow.', [\"Bug-Fixing\", \"Error-Handling\", \"Library-Integration\"]],\n", "    ['Add proper indentation to the code.', 'Remove proper indentation from the code.', [\"Formatting-Cleaning\"]],\n", "    ['Use / instead of // in rotate_bound', 'Use // instead of / in rotate_bound', [\"Formatting-Cleaning\"]],\n", "    ['Remove the import of nddata module.', 'Add the import of nddata module.', [\"Refactoring\",  \"Library-Integration\"]],\n", "    ['Handle exception while getting repo.', 'Remove exception handling while getting repo.', [\"Error-Handling\"]],\n", "    ['Simplify by ignoring the except cases', 'Complicate by handling the except cases', [\"Error-Handling\", \"Refactoring\"]],\n", "    ['Remove the workaround for ParseError.', 'Add a workaround for ParseError.', [\"<PERSON>rror-Handling\", \"Refactoring\"]],\n", "    ['Cast the output of T.stack to \"int8\".', 'Do not cast the output of T.stack to \"int8\".', [\"Implementation\", \"Refactoring\"]],\n", "    [\"Replace 'self' with 'repl' in the code.\", \"Replace 'repl' with 'self' in the code.\", [\"Refactoring\"]],\n", "    # ['Update the version number to \"*******\".', 'Revert the version number from \"*******\" to the previous one.'],\n", "    ['Fix wrong typehint of icon_custom_emoji_id', 'Introduce a wrong typehint for icon_custom_emoji_id', [\"Bug-Fixing\"]],\n", "    [\"Change 'asd' to 'spec' in the example code.\", \"Change 'spec' to 'asd' in the example code.\", [\"Refactoring\"]],\n", "    # ['Set test intervals to 5 minutes for 5 to 30.', 'Revert test intervals back to their original settings.'],\n", "    ['Use OperationalError for error code >= 1000.', 'Do not use OperationalError for error code >= 1000.', [\"Error-Handling\"]],\n", "    ['Replace self.app.renderer with self.renderer', 'Replace self.renderer with self.app.renderer', [\"Refactoring\"]],\n", "    [\"Replace 'chat' with 'm' in the if statements.\", \"Replace 'm' with 'chat' in the if statements.\", [\"Refactoring\"]],\n", "    ['Convert the output of zip function to a list.', 'Keep the output of zip function as it is.', [\"Refactoring\", \"Implementation\"]],\n", "    # ['Set the title size to 26 and grid color to gray', 'Revert the title size and grid color to their original settings.'],\n", "    # ['Add type annotations and function return types.', 'Remove type annotations and function return types.'],\n", "    ['Arrange import statements in alphabetical order.', 'Disarrange import statements from alphabetical order.', [\"Formatting-Cleaning\"]],\n", "    ['Fix the case where the output is not valid JSON.', 'Introduce a case where the output is not valid JSON.', [\"Bug-Fixing\"]],\n", "    ['Use min/max rather than conditional assignment.', 'Use conditional assignment instead of min/max.', [\"Refactoring\"]],\n", "    # ['Add missing import', 'Remove the previously added import'],\n", "    ['Use string format() instead of f-string', 'Use f-string instead of string format()', [\"Refactoring\"]],\n", "    ['Make all for loops enumerate', 'Convert enumerated for loops to regular for loops', [\"Refactoring\"]],\n", "    ['Add single-dash flags to all arguments', 'Remove single-dash flags from all arguments', [\"Refactoring\"]],\n", "    #['Change log level to DEBUG.', 'Change log level to INFO.'],\n", "    ['Make all fields in lowercase', 'Make all fields in uppercase', [\"Refactoring\", \"Formatting-Cleaning\"]],\n", "    ['Change all local variables to camel case.', 'Change all local variables to snake case.', [\"Refactoring\", \"Formatting-Cleaning\"]],\n", "    ['Replace prints with logging.error', 'Replace logging.error with prints', [\"Logging-Debugging\"]],\n", "    # ['Make method f2() static', 'Make method f2() non-static'],\n", "]\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["SEED_INSTRUCT_AND_INVERSE_DATASET.append(\n", "    ['Implement function', \"Delete function body\", [\"Implementation\"]]\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total number of instructions : 66\n"]}], "source": ["print(f\"Total number of instructions : {len(SEED_INSTRUCT_AND_INVERSE_DATASET)}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["SEED_INSTRUCT_AND_INVERSE_DATASET_DICTS = [\n", "    {\n", "        \"instruction\": s[0],\n", "        \"inverse_instruction\": s[1],\n", "        \"categories\": s[2],\n", "    }\n", "    for s in SEED_INSTRUCT_AND_INVERSE_DATASET\n", "]\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'instruction': 'Add type annotations to this function',\n", " 'inverse_instruction': 'Delete type annotations from this function',\n", " 'categories': ['Formatting-Cleaning']}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["SEED_INSTRUCT_AND_INVERSE_DATASET_DICTS[0]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["with open('/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/seed_instructions_with_inverse.json', 'w') as f:\n", "    json.dump(SEED_INSTRUCT_AND_INVERSE_DATASET_DICTS, f, indent=4)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# with open('./artifacts/1_seed_instructions_with_inverse.json', 'w') as f:\n", "#     json.dump(SEED_INSTRUCT_AND_INVERSE_DATASET, f, indent=4)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
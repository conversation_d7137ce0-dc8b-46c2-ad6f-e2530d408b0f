{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "\n", "from research.eval.edit.utils import pretty_json \n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Sampling ~10k random py files and save to JSON\n", "DATA_PATH = \"/mnt/efs/augment/user/yuri/data/github/sampled_10k_py_files.json\"\n", "\n", "# from research.data.spark import k8s_session\n", "# import pyspark.sql.functions as F\n", "\n", "# def create_spark(max_workers: int = 100):\n", "#     spark = k8s_session(\n", "#         max_workers=max_workers,\n", "#         conf={\n", "#             \"spark.sql.parquet.columnarReaderBatchSize\": \"128\",\n", "#             \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "#             \"spark.task.maxFailures\": \"10\",\n", "#         },\n", "#     )\n", "#     return spark\n", "\n", "# spark = create_spark()\n", "\n", "\n", "# files_df = spark.read.parquet(\n", "#     \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", "# )\n", "# py_files_df = (\n", "#     files_df.select(\n", "#         F.col(\"max_stars_repo_name\").alias(\"repo\"),\n", "#         F.col(\"max_stars_repo_path\").alias(\"file_path\"),\n", "#         F.col(\"id\").alias(\"file_sha\"),\n", "#         <PERSON>.col(\"lang\"),\n", "#         F.col(\"content\"),\n", "#         F.col(\"repo_size\"),\n", "#     )\n", "#     .filter(<PERSON>.col(\"file_path\").endswith(\".py\"))\n", "#     .filter(F.length(\"content\") < 1e6)\n", "# )\n", "# print(f\"There are {py_files_df.count()} files.\")\n", "\n", "# sampled_data = py_files_df.sample(fraction=10000/py_files_df.count()).collect()\n", "# print(f\"Sampled {len(sampled_data)} files\")\n", "\n", "# sampled_data_dict = [*map(lambda x: x.asDict(), sampled_data)]\n", "# print(f\"Example: {sampled_data_dict[0]}\")\n", "\n", "# with open(DATA_PATH, \"w\") as f:\n", "#     json.dump(sampled_data_dict, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(DATA_PATH) as f:\n", "    all_data = json.load(f)\n", "\n", "print(len(all_data))\n", "print(pretty_json(all_data[0]))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Repo : swave2015/multimodal_exp | File: beit3/datasets.py\n"]}], "source": ["cur_sample = all_data[135]\n", "\n", "with open(\"./tmp.py\", \"w\") as f:\n", "    print(f\"Repo : {cur_sample['repo']} | File: {cur_sample['file_path']}\")\n", "    f.write(cur_sample['content'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cur_sample"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [], "source": ["SAVE_PATH = Path(\"/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/Bug-Fixing\")\n", "CUR_ID = \"010\"\n", "\n", "with open(\"./tmp.py\") as f:\n", "    updated_content = f.read()\n", "\n", "meta = {\n", "    \"file_name\": cur_sample[\"file_path\"],\n", "    \"instructions\": [\n", "        \"Replace wrong special tokens with correct ones\",\n", "        \"Fix special tokens usage\"\n", "    ],\n", "    \"category\": \"Bug-Fixing\",\n", "    \"language\": \"Python\",\n", "    \"inverse_instructions\": [\n", "        \"Introduce a bug by replacing correct special tokens with incorrect ones\"\n", "    ]\n", "}\n", "\n", "# To prevent accidental overriding\n", "assert not os.path.exists(SAVE_PATH / f\"{CUR_ID}.txt\")\n", "assert not os.path.exists(SAVE_PATH / f\"{CUR_ID}.json\")\n", "\n", "with open(SAVE_PATH / f\"{CUR_ID}.txt\", 'w') as f:\n", "    f.write(updated_content)\n", "\n", "\n", "with open(SAVE_PATH / f\"{CUR_ID}.json\", 'w') as f:\n", "    json.dump(meta, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["meta = {\n", "    \"file_name\": cur_sample[\"file_path\"],\n", "    \"instructions\": [\n", "    ],\n", "    \"category\": \"Bug-Fixing\",\n", "    \"language\": \"Python\",\n", "    \"inverse_instructions\": [\n", "    ]\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
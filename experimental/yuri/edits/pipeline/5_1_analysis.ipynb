{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import pickle\n", "\n", "from os.path import join\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["RESULTS_DIR = '/home/<USER>/tmp/diff20'\n", "VIS_DIR = '/home/<USER>/tmp/diff20_vis'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["['12.pickle',\n", " '18.pickle',\n", " '40.pickle',\n", " '42.pickle',\n", " '43.pickle',\n", " '10.pickle',\n", " '5.pickle',\n", " '27.pickle',\n", " '20.pickle',\n", " '19.pickle',\n", " '21.pickle',\n", " '9.pickle',\n", " '31.pickle',\n", " '16.pickle',\n", " '7.pickle',\n", " '29.pickle',\n", " '15.pickle',\n", " '38.pickle',\n", " '11.pickle',\n", " '3.pickle',\n", " '2.pickle',\n", " '8.pickle',\n", " '34.pickle',\n", " '44.pickle',\n", " '6.pickle',\n", " '0.pickle',\n", " '24.pickle',\n", " '32.pickle',\n", " '22.pickle',\n", " '28.pickle',\n", " '23.pickle',\n", " '47.pickle',\n", " '35.pickle',\n", " '1.pickle',\n", " '45.pickle',\n", " '39.pickle',\n", " '14.pickle',\n", " '26.pickle',\n", " '4.pickle',\n", " '13.pickle',\n", " '30.pickle',\n", " '37.pickle',\n", " '17.pickle',\n", " '46.pickle',\n", " '33.pickle',\n", " '25.pickle',\n", " '49.pickle',\n", " '36.pickle',\n", " '48.pickle',\n", " '41.pickle']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["all_results = os.listdir(RESULTS_DIR)\n", "all_results"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["with open(join(RESULTS_DIR, '16.pickle'), 'rb') as f:\n", "    sample, sample_upd, result, logs = pickle.load(f)\n", "\n", "sample_upd.metadata['info_for_report'] = {\n", "    \"Code generation critique\": any(map(lambda x: \"CodeGenerationStep\" in x, logs.keys())),\n", "    \"Inverse instruction critique\": any(map(lambda x: \"InverseInstructionGenerationStep\" in x, logs.keys()))\n", "}\n", "\n", "sample_upd.metadata['dialog'] = logs['finale']"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["sample_upd.save_into_html(Path(f'./t_d13.html'))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Seems like 18.pickle failed\n", "Seems like 42.pickle failed\n", "Seems like 27.pickle failed\n", "Seems like 31.pickle failed\n", "Seems like 46.pickle failed\n"]}], "source": ["for r in all_results:\n", "    with open(join(RESULTS_DIR, r), 'rb') as f:\n", "        sample, sample_upd, result, logs = pickle.load(f)\n", "        sample_upd.metadata[\"contain_code_generation_critique_loop\"] = any(map(lambda x: \"CodeGenerationStep\" in x, logs.keys()))\n", "        sample_upd.metadata[\"inverse_instr_critique_loop\"] = any(map(lambda x: \"InverseInstructionGenerationStep\" in x, logs.keys()))\n", "\n", "\n", "        if result['inverse_instruction'] is None:\n", "            print(f\"Seems like {r} failed\")\n", "            continue\n", "\n", "        name = r.split('.')[0]\n", "        sample_upd.save_into_html(Path(f'{VIS_DIR}/{name}.html'))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import random\n", "import matplotlib\n", "\n", "from pathlib import Path\n", "from openai import OpenAI\n", "from tqdm import tqdm\n", "from sklearn.manifold import TSNE\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_embedding(text, model=\"text-embedding-ada-002\"):\n", "   text = text.replace(\"\\n\", \" \")\n", "   return client.embeddings.create(input = [text], model=model).data[0].embedding\n", "\n", "def get_embeddings_matrix(instructions, num_elems):\n", "   embeddings_matrix = []\n", "   for i in tqdm(range(num_elems)):\n", "      cur_direction = get_embedding(instructions[i])\n", "      embeddings_matrix.append(cur_direction)\n", "\n", "   embeddings_matrix = np.array(embeddings_matrix)\n", "\n", "   return embeddings_matrix\n", "\n", "def tsne(emb_matrix):\n", "   tsne = TSNE(n_components=2, perplexity=15, random_state=42, init='random', learning_rate=200)\n", "   vis_dims = tsne.fit_transform(emb_matrix)\n", "\n", "   return vis_dims\n", "\n", "def plot_2d(matrix, color_indices, color_map):\n", "    assert matrix.shape[1] == 2\n", "    assert len(matrix.shape) == 2\n", "\n", "    matrix = matrix[(-200 < matrix[:, 0]) & (matrix[:, 0] < 500)]\n", "\n", "    x = matrix[:, 0]\n", "    y = matrix[:, 1]\n", "\n", "    plt.scatter(x, y, alpha=0.5, c=color_indices, cmap=color_map)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# In-house data\n", "\n", "DATA_PATH = Path(\"/mnt/efs/augment/user/igor/nlp/dataset1/prompts.json\")\n", "\n", "with open(DATA_PATH) as f:\n", "    data = json.load(f)\n", "\n", "print(type(data), len(data), data[0])\n", "\n", "\n", "instructions = [x[\"instruction\"] for x in data]\n", "random.seed(42)\n", "random.shuffle(instructions)\n", "\n", "print(len(instructions), instructions[0])\n", "\n", "embeddings_matrix = get_embeddings_matrix(instructions, 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# InstructCoder\n", "\n", "IC_DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/InstructCoder/all_data.json\")\n", "\n", "with open(IC_DATA_PATH) as f:\n", "    ic_data = json.load(f)\n", "\n", "print(type(ic_data), len(ic_data), ic_data[0])\n", "\n", "\n", "ic_instructions = [x[\"instruction\"] for x in ic_data]\n", "random.seed(42)\n", "random.shuffle(ic_instructions)\n", "\n", "print(len(ic_instructions), ic_instructions[0])\n", "\n", "ic_embeddings_matrix = get_embeddings_matrix(ic_instructions, 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["embeddings_matrix.shape, ic_embeddings_matrix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_embeddings_matrix = np.concatenate([embeddings_matrix, ic_embeddings_matrix], axis=0)\n", "colormap = matplotlib.colors.ListedColormap([\"blue\", \"red\"])\n", "\n", "color_indices = [0] * embeddings_matrix.shape[0] + [1] * ic_embeddings_matrix.shape[0]\n", "\n", "tsne_result = tsne(all_embeddings_matrix)\n", "\n", "plot_2d(tsne_result, color_indices, colormap)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_embeddings_matrix.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_2d(tsne(embeddings_matrix))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# target_idx = 0\n", "# target_emb = direction_matrix[target_idx]\n", "# similarities = []\n", "# for i in tqdm(range(direction_matrix.shape[0])):\n", "#     cur_emb = direction_matrix[i]\n", "#     if i == target_idx:\n", "#         similarity = -1e9\n", "#     else:\n", "#         # similarity = np.dot(target_emb, cur_emb)\n", "#         similarity = np.linalg.norm(target_emb - cur_emb)\n", "#     similarities.append(similarity)\n", "\n", "# similarities_sorted = np.argsort(similarities)[::-1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
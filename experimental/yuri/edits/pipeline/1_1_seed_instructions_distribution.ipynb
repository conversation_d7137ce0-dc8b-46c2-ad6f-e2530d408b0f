{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "\n", "from collections import defaultdict\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["with open('/home/<USER>/repos/augment/research/data/synthetic_code_edit/seeds/seed_instructions_with_inverse.json', 'r') as f:\n", "    seed_instructions = json.load(f)\n", "\n", "with open('/home/<USER>/repos/augment/research/data/synthetic_code_edit/categories.json', 'r') as f:\n", "    categories = json.load(f)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(66,\n", " ['Add type annotations to this function',\n", "  'Delete type annotations from this function',\n", "  ['Formatting-Cleaning']])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "len(seed_instructions), seed_instructions[0]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Bug-Fixing',\n", " 'Documentation',\n", " 'Error-Handling',\n", " 'Formatting-Cleaning',\n", " 'Hardening',\n", " 'Implementation',\n", " 'Library-Integration',\n", " 'Logging-Debugging',\n", " 'Optimization',\n", " 'Refactoring',\n", " 'Reviewing',\n", " 'Testing',\n", " 'Translation'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["all_categories = set(categories.keys())\n", "all_categories"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["defaultdict(int,\n", "            {'Formatting-Cleaning': 16,\n", "             'Logging-Debugging': 3,\n", "             'Refactoring': 29,\n", "             'Library-Integration': 6,\n", "             'Documentation': 3,\n", "             'Error-Handling': 12,\n", "             'Bug-Fixing': 9,\n", "             'Translation': 2,\n", "             'Hardening': 1,\n", "             'Implementation': 3})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["samples_per_category = defaultdict(int)\n", "\n", "for s_i in seed_instructions:\n", "    for c in s_i[2]:  # iterate over categories\n", "        assert c in categories.keys(), c\n", "        samples_per_category[c] += 1\n", "        \n", "\n", "samples_per_category\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_sorted_vs = [*sorted(samples_per_category.items(), key=lambda x: x[1])]\n", "_cats, _num = [], []\n", "for k, v in _sorted_vs:\n", "    _cats.append(k)\n", "    _num.append(v)\n", "\n", "plt.bar(_cats, _num)\n", "plt.xticks(rotation=75)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "from research.data.synthetic_code_edit.util_lib import get_unified_diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def render_list(elems, numbered=False):\n", "    if numbered:\n", "        r = [f'{i + 1}. {instr}' for i, instr in enumerate(elems)]\n", "    else:\n", "        r = [f'- {instr}' for i, instr in enumerate(elems)]\n", "    return '\\n'.join(r)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MIX_DIR = Path(\"/mnt/efs/augment/user/igor/data/droid/synth/mix1.2024-01-09_15-28-44\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["successful_gens = [*map(lambda x: x.stem, MIX_DIR.glob(\"*.html\"))]\n", "len(successful_gens), successful_gens[:10]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_samples = []\n", "for name in tqdm(successful_gens):\n", "    with open(MIX_DIR / \"all\" / f\"{name}.json\", \"r\") as f:\n", "        data_samples.append(json.load(f))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FILTERING_PROMPT = \"\"\"Here is the original source code:\n", "```\n", "{selected_code}\n", "```\n", "\n", "Here is updated code obtained by applying instruction ('{instruction}') to the original source code:\n", "```\n", "{updated_code}\n", "```\n", "\n", "And here is the diff between original source code and updated code:\n", "```\n", "{diff}\n", "```\n", "\n", "Please evaluate whether this updated code meets these criterias:\n", "{criterias}\n", "\n", "If the updated code doesn't meet at least one of these criterias, mark this evaluation as failed.\n", "\n", "Follow this steps to complete this task:\n", "1. Analyze the original code, updated code, diff and criterias.\n", "2. Describe your thinking process on how to complete the evaluation.\n", "3. Explicitly write whether updated code passed evaluation or not.\n", "\"\"\"\n", "\n", "\n", "FILTERING_EXTRACT_PROMPT = \"\"\"Return results of evaluation as a JSON with 2 keys:\n", "- success\n", "- feedback\n", "\n", "If updated code passed evaluation, then success must be True and feedback empty.\n", "If updated code didn't pass evaluation, then success must be False and feedback should contain why code didn't pass evaluation.\n", "\"\"\"\n", "\n", "FILTERING_CRITERIAS = [\n", "    \"Indentations and comments of the original source code should not be changed, unless changing them is required by instruction\",\n", "    \"Updated code must contain all modifications required by the instruction\",\n", "    \"Updated code must NOT contain any modifications not required by the instruction\",\n", "    \"Updated code must NOT contain any comments related to modifications made, unless it is required by instruction\"\n", "]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def filter_sample(filter_args) -> bool:\n", "#     sample, = filter_args\n", "\n", "sample = data_samples[0]\n", "\n", "diff = get_unified_diff(sample[\"old_code\"], sample[\"new_code\"])\n", "\n", "prompt = FILTERING_PROMPT.format(\n", "    selected_code=sample[\"old_code\"],\n", "    updated_code=sample[\"new_code\"],\n", "    instruction=sample[\"instructions\"][0],\n", "    criterias=render_list(FILTERING_CRITERIAS),\n", "    diff=diff\n", ")\n", "\n", "print(prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages = [{\"role\": \"user\", \"content\": prompt}]\n", "response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages.append({\"role\": \"assistant\", \"content\": response})\n", "messages.append({\"role\": \"user\", \"content\": FILTERING_EXTRACT_PROMPT})\n", "\n", "filtering_result = gpt(messages, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(filtering_result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
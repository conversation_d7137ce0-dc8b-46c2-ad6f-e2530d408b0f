import argparse
import json
from multiprocessing import Pool

from pathlib import Path
from research.data.synthetic_code_edit.api_lib import <PERSON><PERSON><PERSON><PERSON>per
from tqdm import tqdm


PROMPT = """
Here is a hunk of code:
```
{code}
```

Your task is to generate such an instruction that is NOT applicable to this code.
For example:
1. If code doesn't contain a function named "foo", then you can generate "rename function foo to bar".
2. If code contains only single quotes and no double quotes, you can generate "replace double quotes with single quotes".
3. If code contains no comments, you can generate "remove all comments".
4. If the code has no loops, generate "Optimize the for-loop for better performance."
5. If the code is purely functional with no object-oriented constructs, generate "Refactor the class method to use encapsulation more effectively."
6. If there are no global variables, generate "Restrict the scope of global variables to improve memory usage."
7. If the code doesn't use any lists or arrays, generate "Optimize the list handling to use list comprehensions for better performance."
8. If the code doesn't interact with a database, generate "Optimize SQL queries to reduce database load time."
9. If there are no conditional statements (if/else), generate "Refactor the code to use ternary operators instead of if-else statements for simplicity."
10. If the code has no function or method definitions, generate "Document all functions with appropriate docstrings."
11. If the code does not include any asynchronous operations, generate "Refactor the async functions to use await more efficiently."
12. If the code does not perform file I/O operations, generate "Implement error handling for file not found exceptions in file reading operations."

These are just examples, don't use them blindly! Come up with something specific to the provided hunk of code!

And after generating such an instruction, you also have to shortly explain why it is NOT applicable to the provided code.
If the instruction is technically applicable to the provided code (i.e. it's applicable, but leads to syntax/logic/runtime errors), consider it's still applicable, and do not generate such instructions.

Follow these steps to complete this task:
1. Analyze the provided hunk of code.
2. Describe your thinking process on how to generate required instruction.
3. Explicitly write the generated instruction and short explanation why it is NOT applicable to the provided code.
"""


EXTRACT_PROMPT = """Return the result as JSON with 2 keys:
- instruction
- explanation
"""

PROMPT2 = """
Here is an explanation of why this instruction ('{instruction}') is not applicable to some source code:
'{explanation}'.

Please generate short (1 sentence) version of this explanation.
And if explanation refers to the source code, please use the term 'selected code'.
And don't include some preamble like "The instruction is not applicable because".
"""

EXTRACT_PROMPT2 = """Return the result as JSON with 1 key:
- explanation
"""


def generate_not_applicable_instruction(sample, gpt):
    prompt = PROMPT.format(
        code=sample["new_middle"],
    )

    messages = [{"role": "user", "content": prompt}]
    gpt4_response = gpt(messages, model="gpt-4-1106-preview", temperature=0.5)

    messages.append({"role": "assistant", "content": gpt4_response})
    messages.append({"role": "user", "content": EXTRACT_PROMPT})
    extracted_response = gpt(messages, model="gpt-3.5-turbo-1106", use_json=True, temperature=0)

    prompt2 = PROMPT2.format(
        instruction=extracted_response["instruction"],
        explanation=extracted_response["explanation"],
    )
    messages2 = [{"role": "user", "content": prompt2}]
    gpt4_response2 = gpt(messages2, model="gpt-4-1106-preview", temperature=0.5)

    messages2.append({"role": "assistant", "content": gpt4_response2})
    messages2.append({"role": "user", "content": EXTRACT_PROMPT2})
    extracted_response2 = gpt(messages2, model="gpt-3.5-turbo-1106", use_json=True, temperature=0)

    return {
        "short_explanation": extracted_response2["explanation"],
        **extracted_response,
    }

def generate_sample(args):
    sample, gpt, output_dir = args
    sample, output_file_name = sample

    for _dir in ["good", "failed"]:
        if (output_dir / _dir / output_file_name).exists():
            return

    try:
        result = generate_not_applicable_instruction(sample, gpt)
    except Exception as e:
        print(f"Failed to generate for {output_file_name} sample: {e}")
        with (output_dir / "failed" / output_file_name).open("w") as f:
            json.dump(sample, f, indent=2)
        return
    sample["refusal_generation"] = result
    with (output_dir / "good" / output_file_name).open("w") as f:
        json.dump(sample, f, indent=2)

def read_data(input_dir: Path):
    data = []
    for file in input_dir.glob("*.json"):
        with file.open() as f:
            data.append((json.load(f), file.name))
    return data

def main(input_dir: Path, output_dir: Path, num_processes: int, num_samples: int):
    output_dir.mkdir(exist_ok=True, parents=False)
    (output_dir / "good").mkdir(exist_ok=True, parents=False)
    (output_dir / "bad").mkdir(exist_ok=True, parents=False)

    data = read_data(input_dir)
    print(f"Loaded {len(data)} data samples.")

    gpt = GptWrapper()
    tasks = []
    for sample in tqdm(data[:num_samples]):
        tasks.append((sample, gpt, output_dir))

    if num_processes <= 1:
        for task in tqdm(tasks):
            generate_sample(task)
    else:
        with Pool(num_processes) as pool:
            _ = list(tqdm(pool.imap(generate_sample, tasks), total=len(tasks)))

    print(f"GPT usage:\n{gpt.get_stats()}")

def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for generating droid refusal samples.",
    )
    parser.add_argument(
        "--input_dir",
        "-i",
        type=Path,
        required=True,
        help="Input directory containing data samples",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory for saving the data",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        required=True,
        help="Number of samples to generate",
    )
    args = parser.parse_args()

    return args

if __name__ == "__main__":
    xargs = parse_args()
    main(
        input_dir=xargs.input_dir,
        output_dir=xargs.output_dir,
        num_processes=xargs.num_processes,
        num_samples=xargs.num_samples,
    )

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import time\n", "\n", "from typing import Iterator, Optional\n", "from google.cloud import bigquery\n", "from pathlib import Path\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatPromptInput,\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from experimental.yuri.preferences.utils import markdown, row_html, wrap_html\n", "from tqdm import tqdm\n", "from difflib import unified_diff\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    # \"system-services-dev\", \"us-east5\", \"claude-3-5-sonnet@20240620\", 0, 1024 * 5\n", "    \"augment-387916\",\n", "    \"us-east5\",\n", "    \"claude-3-5-sonnet@20240620\",\n", "    0,\n", "    1024 * 5,\n", ")\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", ")\n", "PROMPT_FORMATTER = get_structured_chat_prompt_formatter_by_name(\n", "    \"structured-binks-claude\", TOKEN_APPORTIONMENT\n", ")\n", "DOGFOOD_URL = (\n", "    \"https://support.dogfood.t.us-central1.prod.augmentcode.com/t/dogfood/request\"\n", ")\n", "STAGING_DOGFOOD_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_chat_samples(\n", "    request_ids,\n", "    project_id: str = \"system-services-prod\",\n", "    dataset_name: str = \"staging_request_insight_full_export_dataset\",\n", ") -> dict:\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {project_id}.{dataset_name}.request_metadata AS metadata\n", "JOIN {project_id}.{dataset_name}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {project_id}.{dataset_name}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=project_id)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "            \"request\": row.request[\"request\"],\n", "            \"response\": row.response[\"response\"],\n", "            \"metadata\": row.metadata,\n", "            \"datetime\": row.time.isoformat(),\n", "            \"row\": row,\n", "        }\n", "\n", "    return chat_rows_dic\n", "\n", "\n", "def extract_between_patterns(main_string: str, p1: str, p2: str, index: int) -> str:\n", "    pattern = rf\"{re.escape(p1)}(.*?){re.escape(p2)}\"\n", "    matches = re.finditer(pattern, main_string, re.DOTALL)\n", "\n", "    for i, match in enumerate(matches):\n", "        if i == index:\n", "            return match.group(1)\n", "\n", "    raise ValueError(f\"Substring between '{p1}' and '{p2}' not found for index {index}\")\n", "\n", "\n", "def get_num_patterns(main_string: str, p1: str, p2: str) -> int:\n", "    pattern = rf\"{re.escape(p1)}(.*?){re.escape(p2)}\"\n", "    matches = re.finditer(pattern, main_string, re.DOTALL)\n", "    return len(list(matches))\n", "\n", "\n", "def format_sample(raw_sample, num_chunks_to_keep: int, drop_history: bool = False):\n", "    history = [\n", "        Exchange(e[\"request_message\"], e[\"response_text\"], e[\"request_id\"])\n", "        for e in raw_sample[\"row\"].request[\"request\"].get(\"chat_history\", [])\n", "    ]\n", "    if drop_history:\n", "        history = []\n", "    chunks = [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            char_start=chunk.get(\"char_offset\", 0),\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk[\"blob_name\"],\n", "            origin=chunk[\"origin\"],\n", "        )\n", "        for chunk in raw_sample[\"row\"].request[\"retrieved_chunks\"]\n", "    ]\n", "    chunks = chunks[:num_chunks_to_keep]\n", "\n", "    request_entry = raw_sample[\"row\"].request[\"request\"]\n", "    if request_entry.get(\"suffix\", \"\").startswith(\"\\n\") and request_entry.get(\n", "        \"selected_code\", \"\"\n", "    ):\n", "        # IDK why it happens, but it does\n", "        request_entry[\"selected_code\"] += \"\\n\"\n", "        request_entry[\"suffix\"] = request_entry[\"suffix\"][1:]\n", "        print(\"Suffix starts with new line\")\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=request_entry[\"path\"],\n", "        prefix=request_entry.get(\"prefix\", \"\"),\n", "        selected_code=request_entry.get(\"selected_code\", \"\"),\n", "        suffix=request_entry.get(\"suffix\", \"\"),\n", "        message=request_entry[\"message\"],\n", "        chat_history=history,\n", "        prefix_begin=0,\n", "        suffix_end=len(\n", "            request_entry.get(\"prefix\", \"\")\n", "            + request_entry.get(\"selected_code\", \"\")\n", "            + request_entry.get(\"suffix\", \"\")\n", "        ),\n", "        retrieved_chunks=chunks,\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    return prompt_input, prompt_output\n", "\n", "\n", "def put_numbers(\n", "    text: str, start_line_offset: int, mode: str, prefix: Optional[str] = None\n", "):\n", "    assert mode in [\"xml\", \"table\", \"regular\"]\n", "\n", "    for i, line in enumerate(text.splitlines(keepends=True)):\n", "        if mode == \"xml\":\n", "            assert prefix is None, \"prefix is not used for xml\"\n", "            cur_number = i + 1 + start_line_offset\n", "            yield f\"<line number={cur_number}>{line.rstrip()}</line number={cur_number}>\\n\"\n", "        elif mode == \"table\":\n", "            assert prefix is None, \"prefix is not used for table\"\n", "            if i == 0 and start_line_offset == 0:\n", "                yield \"\"\"\n", " Line | Content\n", " ---- | -------\n", "\"\"\"\n", "            cur_number = f\"{i+1+start_line_offset:04d}\"\n", "            yield f\" {cur_number} | {line}\"\n", "        else:\n", "            assert prefix is not None\n", "            yield f\"{prefix}{i+1+start_line_offset:04d}: {line}\"\n", "\n", "\n", "def run_anthropic(\n", "    cur_message, history, system_prompt, use_tools=True, only_replace_tool=False\n", "):\n", "    formatted_messages = []\n", "    for message in history:\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": message[0],\n", "            }\n", "        )\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": message[1],\n", "            }\n", "        )\n", "\n", "    # Add cur message\n", "    formatted_messages.append(\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": cur_message,\n", "        }\n", "    )\n", "\n", "    tools = [\n", "        {\n", "            \"name\": \"replace_text\",\n", "            \"description\": \"Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `start_line_number`, `old_text`, `end_line_number`, `replacement_text`.\",\n", "            \"input_schema\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"start_line_number\": {\n", "                        \"type\": \"integer\",\n", "                        \"description\": \"The line number where the original text starts, inclusive.\",\n", "                    },\n", "                    \"old_text\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The original text.\",\n", "                    },\n", "                    \"end_line_number\": {\n", "                        \"type\": \"integer\",\n", "                        \"description\": \"The line number where the original text ends, inclusive.\",\n", "                    },\n", "                    \"replacement_text\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The new text.\",\n", "                    },\n", "                },\n", "                \"required\": [\n", "                    \"start_line_number\",\n", "                    \"old_text\",\n", "                    \"end_line_number\",\n", "                    \"replacement_text\",\n", "                ],\n", "            },\n", "        },\n", "        {\n", "            \"name\": \"insert_text\",\n", "            \"description\": \"Insert `inserted_text` above or below line with number==`line_number`.\"\n", "            \" If you want to insert text above the X line, you should use `line_number` = X and `relative_position` = `above`.\"\n", "            \" If you want to insert text below the X line, you should use `line_number` = X and `relative_position` = `below`.\"\n", "            \" Always generate arguments in the following order: `line_number`, `line`, `relative_position`, `inserted_text`.\",\n", "            \"input_schema\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"line_number\": {\n", "                        \"type\": \"integer\",\n", "                        \"description\": \"Number of the line relative to which new text will be inserted.\",\n", "                    },\n", "                    \"line\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The line relative to which new text will be inserted.\",\n", "                    },\n", "                    \"relative_position\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"Can take ONLY two values: `above and `below`. If `above`, the new text will be inserted ABOVE the `line`. If `below`, the new text will be inserted BELOW the `line`.\",\n", "                    },\n", "                    \"inserted_text\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The new text to be inserted.\",\n", "                    },\n", "                },\n", "                \"required\": [\n", "                    \"line_number\",\n", "                    \"line\",\n", "                    \"relative_position\",\n", "                    \"inserted_text\",\n", "                ],\n", "            },\n", "        },\n", "        {\n", "            \"name\": \"delete_text\",\n", "            \"description\": \"Delete text from line `start_line_number` (inclusive) to line `end_line_number` (inclusive).\"\n", "            \" Always generate arguments in the following order: `start_line_number`, `start_line`, `end_line_number`, `end_line`.\",\n", "            \"input_schema\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"start_line_number\": {\n", "                        \"type\": \"integer\",\n", "                        \"description\": \"First line of the text to be deleted (inclusive).\",\n", "                    },\n", "                    \"start_line\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The first line of the text to be deleted.\",\n", "                    },\n", "                    \"end_line_number\": {\n", "                        \"type\": \"integer\",\n", "                        \"description\": \"Last line of the text to be deleted (inclusive).\",\n", "                    },\n", "                    \"end_line\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The last line of the text to be deleted.\",\n", "                    },\n", "                },\n", "                \"required\": [\n", "                    \"start_line_number\",\n", "                    \"start_line\",\n", "                    \"end_line_number\",\n", "                    \"end_line\",\n", "                ],\n", "            },\n", "        },\n", "    ]\n", "\n", "    if only_replace_tool:\n", "        tools = [\n", "            {\n", "                \"name\": \"replace_text\",\n", "                \"description\": \"Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.\",\n", "                \"input_schema\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"old_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The original text.\",\n", "                        },\n", "                        \"start_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text starts, inclusive.\",\n", "                        },\n", "                        \"end_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text ends, inclusive.\",\n", "                        },\n", "                        \"replacement_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The new text.\",\n", "                        },\n", "                    },\n", "                    \"required\": [\n", "                        \"old_text\",\n", "                        \"start_line_number\",\n", "                        \"end_line_number\",\n", "                        \"replacement_text\",\n", "                    ],\n", "                },\n", "            },\n", "        ]\n", "\n", "    response = ANTHROPIC_CLIENT.client.messages.create(\n", "        model=\"claude-3-5-sonnet@20240620\",\n", "        max_tokens=8192,\n", "        messages=formatted_messages,\n", "        system=system_prompt,\n", "        temperature=0,\n", "        tools=tools if use_tools else [],\n", "    )\n", "\n", "    return response\n", "\n", "\n", "def run_anthropic_w_retries(\n", "    cur_message,\n", "    history,\n", "    system_prompt,\n", "    max_retries=3,\n", "    use_tools=True,\n", "    only_replace_tool=False,\n", "):\n", "    result = None\n", "    for i in range(max_retries):\n", "        try:\n", "            result = run_anthropic(\n", "                cur_message, history, system_prompt, use_tools, only_replace_tool\n", "            )\n", "            break\n", "        except Exception as e:\n", "            print(f\"Failed to run anthropic with tools, retrying {i+1}/{max_retries}\")\n", "            print(e)\n", "            time.sleep(1)\n", "    return result\n", "\n", "\n", "def format_code(\n", "    prefix: str,\n", "    selected_code: str,\n", "    suffix: str,\n", "    num_lines_in_prefix_suffix: int,\n", "    is_highlighted: bool,\n", "):\n", "    def _n_lines(s: str):\n", "        return len(s.splitlines(keepends=True))\n", "\n", "    prefix = \"\".join(prefix.splitlines(keepends=True)[-num_lines_in_prefix_suffix:])\n", "    suffix = \"\".join(suffix.splitlines(keepends=True)[:num_lines_in_prefix_suffix])\n", "\n", "    full_file_in_prompt = prefix + selected_code + suffix\n", "\n", "    # XML\n", "    prefix_n_xml = \"\".join(put_numbers(prefix, 0, mode=\"xml\"))\n", "    selected_code_n_xml = \"\".join(\n", "        put_numbers(selected_code, _n_lines(prefix), mode=\"xml\")\n", "    )\n", "    suffix_n_xml = \"\".join(\n", "        put_numbers(suffix, _n_lines(prefix) + _n_lines(selected_code), mode=\"xml\")\n", "    )\n", "    if is_highlighted:\n", "        xml_code = f\"\"\"{prefix_n_xml}<highlighted_code>\\n{selected_code_n_xml}</highlighted_code>\\n{suffix_n_xml}\"\"\"\n", "    else:\n", "        xml_code = f\"\"\"{prefix_n_xml}{selected_code_n_xml}{suffix_n_xml}\"\"\"\n", "\n", "    # Table\n", "    prefix_n_table = \"\".join(put_numbers(prefix, 0, mode=\"table\"))\n", "    selected_code_n_table = \"\".join(\n", "        put_numbers(selected_code, _n_lines(prefix), mode=\"table\")\n", "    )\n", "    suffix_n_table = \"\".join(\n", "        put_numbers(suffix, _n_lines(prefix) + _n_lines(selected_code), mode=\"table\")\n", "    )\n", "    if is_highlighted:\n", "        table_code = f\"\"\"{prefix_n_table}<highlighted_code>\\n{selected_code_n_table}</highlighted_code>\\n{suffix_n_table}\"\"\"\n", "    else:\n", "        table_code = f\"\"\"{prefix_n_table}{selected_code_n_table}{suffix_n_table}\"\"\"\n", "\n", "    # Regular\n", "    prefix_n_regular = \"\".join(put_numbers(prefix, 0, mode=\"regular\", prefix=\" \"))\n", "    selected_code_n_regular = \"\".join(\n", "        put_numbers(selected_code, _n_lines(prefix), mode=\"regular\", prefix=\"*\")\n", "    )\n", "    suffix_n_regular = \"\".join(\n", "        put_numbers(\n", "            suffix,\n", "            _n_lines(prefix) + _n_lines(selected_code),\n", "            mode=\"regular\",\n", "            prefix=\" \",\n", "        )\n", "    )\n", "    regular_code = f\"\"\"{prefix_n_regular}{selected_code_n_regular}{suffix_n_regular}\"\"\"\n", "\n", "    # No numbering\n", "    if is_highlighted:\n", "        no_numbering_code = f\"\"\"{prefix}<highlighted_code>\\n{selected_code}</highlighted_code>\\n{suffix}\"\"\"\n", "    else:\n", "        no_numbering_code = f\"\"\"{prefix}{selected_code}{suffix}\"\"\"\n", "\n", "    return (\n", "        xml_code,\n", "        regular_code,\n", "        table_code,\n", "        {\n", "            \"full_file_in_prompt\": full_file_in_prompt,\n", "            \"first_highlighted_line_number\": _n_lines(prefix) + 1,\n", "            \"last_highlighted_line_number\": _n_lines(prefix) + _n_lines(selected_code),\n", "            \"no_numbering_code\": no_numbering_code,\n", "        },\n", "    )\n", "\n", "\n", "def unify_tool_response(tool_result, full_file):\n", "    if tool_result.name == \"replace_text\":\n", "        return {\n", "            \"start_line_number\": tool_result.input[\"start_line_number\"],\n", "            \"end_line_number\": tool_result.input[\"end_line_number\"],\n", "            \"replacement_text\": tool_result.input[\"replacement_text\"],\n", "        }\n", "\n", "    if tool_result.name == \"insert_text\":\n", "        line_number = tool_result.input[\"line_number\"]\n", "        real_line = full_file.splitlines(keepends=True)[line_number - 1]\n", "\n", "        if tool_result.input[\"relative_position\"] == \"below\":\n", "            replacement_text = real_line + tool_result.input[\"inserted_text\"] + \"\\n\"\n", "        elif tool_result.input[\"relative_position\"] == \"above\":\n", "            replacement_text = tool_result.input[\"inserted_text\"] + \"\\n\" + real_line\n", "        else:\n", "            raise Exception(\n", "                f\"Unknown relative position: {tool_result.input['relative_position']}\"\n", "            )\n", "        return {\n", "            \"start_line_number\": line_number,\n", "            \"end_line_number\": line_number,\n", "            \"replacement_text\": replacement_text,\n", "        }\n", "\n", "    if tool_result.name == \"delete_text\":\n", "        return {\n", "            \"start_line_number\": tool_result.input[\"start_line_number\"],\n", "            \"end_line_number\": tool_result.input[\"end_line_number\"],\n", "            \"replacement_text\": \"\",\n", "        }\n", "\n", "    assert False, f\"Unknown tool name: {tool_result.name}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def postprocess_claude_response(\n", "    provided_code, claude_response, call_adjust_line_number=False\n", "):\n", "    line_ranges = []\n", "    compiled_text = f\"{claude_response.content[0].text}\\n{'=' * 30}\\n\"\n", "    for i in range(1, len(claude_response.content)):\n", "        tool_result = claude_response.content[i]\n", "        assert tool_result.type == \"tool_use\"\n", "\n", "        if call_adjust_line_number:\n", "            tool_result.input[\"start_line_number\"] = adjust_line_number(\n", "                provided_code,\n", "                tool_result.input[\"start_line_number\"],\n", "                tool_result.input[\"old_text\"].splitlines(keepends=True)[0],\n", "            )\n", "            tool_result.input[\"end_line_number\"] = adjust_line_number(\n", "                provided_code,\n", "                tool_result.input[\"end_line_number\"],\n", "                tool_result.input[\"old_text\"].splitlines(keepends=True)[-1],\n", "            )\n", "\n", "        tool_result_d = unify_tool_response(tool_result, provided_code)\n", "\n", "        line_ranges.append(\n", "            (\n", "                tool_result_d[\"start_line_number\"],\n", "                tool_result_d[\"end_line_number\"],\n", "            )\n", "        )\n", "\n", "        # <PERSON> writes it w/o newline at the end\n", "        replacement_text = tool_result_d[\"replacement_text\"] + \"\\n\"\n", "\n", "        cur_original_text = \"\".join(\n", "            provided_code.splitlines(keepends=True)[\n", "                tool_result_d[\"start_line_number\"] - 1 : tool_result_d[\n", "                    \"end_line_number\"\n", "                ]\n", "            ]\n", "        )\n", "        cur_diff = \"\".join(\n", "            unified_diff(\n", "                cur_original_text.splitlines(True), replacement_text.splitlines(True)\n", "            )\n", "        )\n", "\n", "        compiled_text += f\"\"\"{i}: {tool_result.name} ({tool_result_d[\"start_line_number\"]}, {tool_result_d[\"end_line_number\"]})\n", "```\n", "{tool_result_d[\"replacement_text\"]}\n", "```\n", "Diff:\n", "```\n", "{cur_diff}\n", "```\n", "\n", "{'=' * 30}\n", "\"\"\"\n", "    return compiled_text, line_ranges\n", "\n", "\n", "def adjust_line_number(full_file, line_number, line):\n", "    full_file_lines = [\n", "        cur_line.rstrip() for cur_line in full_file.splitlines(keepends=True)\n", "    ]\n", "    line = line.rstrip()\n", "\n", "    if full_file_lines[line_number - 1] == line:\n", "        return line_number\n", "\n", "    for i in range(max(0, line_number - 5), min(len(full_file_lines), line_number + 5)):\n", "        if full_file_lines[i] == line:\n", "            print(f\"Adjusting line number from {line_number} to {i + 1}\")\n", "            return i + 1\n", "\n", "    print(f\"Can't find line {line} in the file\")\n", "\n", "    return line_number"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SmartpasteApplyCodeblock:\n", "    @classmethod\n", "    def get_response(\n", "        cls,\n", "        prompt_input: ChatPromptInput,\n", "        prompt_output,\n", "        main_response: str,\n", "        code_block: str,\n", "        file_path: Optional[str],\n", "    ) -> dict:\n", "        if file_path is None:\n", "            full_file = (\n", "                prompt_input.prefix + prompt_input.selected_code + prompt_input.suffix\n", "            )\n", "            file_path = prompt_input.path\n", "        else:\n", "            with open(file_path, \"r\") as f:\n", "                full_file = f.read()\n", "\n", "        xml_code, regular_code, table_code, _ = format_code(\n", "            prefix=\"\",\n", "            selected_code=full_file,\n", "            suffix=\"\",\n", "            num_lines_in_prefix_suffix=int(1e9),\n", "            is_highlighted=False,  # Using full file\n", "        )\n", "        prompt = f\"\"\"Great! Now please apply changes that you demonstrated in this codeblock:\n", "<change_to_apply>\n", "```{code_block}```\n", "</change_to_apply>\n", "\n", "to this file: \n", "\n", "<file path=\"{file_path}\">\n", "{xml_code.rstrip()}\n", "</file>\n", "\n", "You should apply changes in a way that keeps the code style, spacing and indentation consistent and reasonable!\n", "\n", "Please, do it using single or multiple calls to the provided tools (`replace_text`, `insert_text`, `delete_text`). \n", "Requirements to tool calls:\n", "- All these calls should be done all ALL AT ONCE, NOT sequentially.\n", "- All calls should use DISJOINT ranges of lines.\n", "\n", "Before doing tool calls:\n", "1. Describe changes demonstrated in <change_to_apply> tag.\n", "2. Describe how to apply these changes to the file.\n", "3. Identify concrete locations where changes should be applied. Locations should be as detailed as possible.\n", "\"\"\"\n", "\n", "        system_prompt = \"\"\"\n", "You are highly advanced and intelligent AI code assistant. \n", "Your task is to carefully apply changes to a file based on the conversation history and the user's instructions. \n", "\"\"\"\n", "\n", "        # Note: we are using prompt_output.message here, because it contains (optional) selected code.\n", "        # We should make sure that in production we don't lose selected code from previous messages (if it exists).\n", "        claude_response = run_anthropic_w_retries(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ]\n", "            + [(prompt_output.message, main_response)],\n", "            system_prompt,\n", "        )\n", "\n", "        compiled_text, line_ranges = postprocess_claude_response(\n", "            provided_code=full_file,\n", "            claude_response=claude_response,\n", "        )\n", "\n", "        return {\n", "            \"line_ranges\": line_ranges,\n", "            \"compiled_text\": compiled_text,\n", "            \"numbered_code\": regular_code,\n", "        }\n", "\n", "\n", "class SmartpasteApplyCodeblockV2:\n", "    @classmethod\n", "    def get_response(\n", "        cls,\n", "        prompt_input: ChatPromptInput,\n", "        prompt_output,\n", "        main_response: str,\n", "        code_block: str,\n", "        file_path: Optional[str],\n", "    ) -> dict:\n", "        if file_path is None:\n", "            full_file = (\n", "                prompt_input.prefix + prompt_input.selected_code + prompt_input.suffix\n", "            )\n", "            file_path = prompt_input.path\n", "        else:\n", "            with open(file_path, \"r\") as f:\n", "                full_file = f.read()\n", "\n", "        xml_code, regular_code, table_code, _ = format_code(\n", "            prefix=\"\",\n", "            selected_code=full_file,\n", "            suffix=\"\",\n", "            num_lines_in_prefix_suffix=int(1e9),\n", "            is_highlighted=False,  # Using full file\n", "        )\n", "        prompt = f\"\"\"Great! Now please apply changes that you demonstrated in this codeblock:\n", "<change_to_apply>\n", "```{code_block}```\n", "</change_to_apply>\n", "\n", "to this file: \n", "\n", "<file path=\"{file_path}\">\n", "{xml_code.rstrip()}\n", "</file>\n", "\n", "You should apply changes in a way that keeps the code style, spacing and indentation consistent and reasonable!\n", "\n", "Please, do it using single or multiple calls to the `replace_text` tool. \n", "Requirements to tool calls:\n", "- All these calls should be done all ALL AT ONCE, NOT sequentially.\n", "- All calls should use DISJOINT ranges of lines.\n", "- Prefer doing local changes. If you need to change small part of a large class/function/method/etc, change it directly, WITHOUT replacing the whole class/function/method/etc.\n", "\"\"\"\n", "\n", "        system_prompt = \"\"\"\n", "You are highly advanced and intelligent AI code assistant. \n", "Your task is to carefully apply changes to a file based on the conversation history and the user's instructions. \n", "\"\"\"\n", "\n", "        # Note: we are using prompt_output.message here, because it contains (optional) selected code.\n", "        # We should make sure that in production we don't lose selected code from previous messages (if it exists).\n", "        claude_response = run_anthropic_w_retries(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ]\n", "            + [(prompt_output.message, main_response)],\n", "            system_prompt,\n", "            use_tools=True,\n", "            only_replace_tool=True,\n", "        )\n", "\n", "        compiled_text, line_ranges = postprocess_claude_response(\n", "            provided_code=full_file,\n", "            claude_response=claude_response,\n", "            call_adjust_line_number=True,\n", "        )\n", "\n", "        return {\n", "            \"line_ranges\": line_ranges,\n", "            \"compiled_text\": compiled_text,\n", "            \"numbered_code\": regular_code,\n", "        }\n", "\n", "\n", "class CodeEditsWithTools:\n", "    @classmethod\n", "    def get_response(\n", "        cls,\n", "        prompt_input: ChatPromptInput,\n", "        prompt_output,\n", "        num_lines_in_prefix_suffix: int = 500,\n", "    ) -> dict:\n", "        prefix = prompt_input.prefix\n", "        selected_code = prompt_input.selected_code\n", "        suffix = prompt_input.suffix\n", "        file_path = prompt_input.path\n", "\n", "        xml_code, regular_code, table_code, mist_formatting_data = format_code(\n", "            prefix=prefix,\n", "            selected_code=selected_code,\n", "            suffix=suffix,\n", "            num_lines_in_prefix_suffix=num_lines_in_prefix_suffix,\n", "            is_highlighted=True,\n", "        )\n", "\n", "        restriction_note = \"You should focus on changing ONLY the highlighted region.\"\n", "        # V2 is less restrictive, but not much real difference:\n", "        # restriction_note = \"You should focus on changing the highlighted region, but you can also change code outside of the highlighted region if it is necessary to perform the edit correctly.\"\n", "\n", "        prompt = f\"\"\"I have opened a file `{file_path}` and highlighted a part of the code (enclosed in <highlighted_code> tag):\n", "<file path=\"{file_path}\">\n", "{xml_code.rstrip()}\n", "</file>\n", "\n", "Please, edit it according to the following instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\n", "You should make edit in a way that keeps the code style, spacing and indentation consistent and reasonable!\n", "{restriction_note}\n", "\n", "Please, do it using single or multiple calls to the provided tools (`replace_text`, `insert_text`, `delete_text`). \n", "Requirements to tool calls:\n", "- All these calls should be done all ALL AT ONCE, NOT sequentially.\n", "- All calls should use DISJOINT ranges of lines.\n", "\"\"\"\n", "\n", "        system_prompt = \"\"\"\n", "You are highly advanced and intelligent AI code assistant. \n", "Your task is to edit a file based on user's instructions. \n", "\"\"\"\n", "\n", "        claude_response = run_anthropic_w_retries(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ],\n", "            system_prompt,\n", "        )\n", "\n", "        compiled_text, line_ranges = postprocess_claude_response(\n", "            provided_code=mist_formatting_data[\"full_file_in_prompt\"],\n", "            claude_response=claude_response,\n", "        )\n", "\n", "        return {\n", "            \"line_ranges\": line_ranges,\n", "            \"compiled_text\": compiled_text,\n", "            \"numbered_code\": regular_code,\n", "        }\n", "\n", "\n", "class CodeEditsWithToolsV2:\n", "    @classmethod\n", "    def get_response(\n", "        cls,\n", "        prompt_input: ChatPromptInput,\n", "        prompt_output,\n", "        num_lines_in_prefix_suffix: int = 500,\n", "    ) -> dict:\n", "        prefix = prompt_input.prefix\n", "        selected_code = prompt_input.selected_code\n", "        suffix = prompt_input.suffix\n", "        file_path = prompt_input.path\n", "\n", "        xml_code, regular_code, table_code, mist_formatting_data = format_code(\n", "            prefix=prefix,\n", "            selected_code=selected_code,\n", "            suffix=suffix,\n", "            num_lines_in_prefix_suffix=num_lines_in_prefix_suffix,\n", "            is_highlighted=True,\n", "        )\n", "\n", "        restriction_note = \"You should focus on changing ONLY the highlighted region.\"\n", "        # V2 is less restrictive, but not much real difference:\n", "        # restriction_note = \"You should focus on changing the highlighted region, but you can also change code outside of the highlighted region if it is necessary to perform the edit correctly.\"\n", "\n", "        prompt = f\"\"\"I have opened a file `{file_path}` and highlighted a part of the code (enclosed in <highlighted_code> tag):\n", "<file path=\"{file_path}\">\n", "{xml_code.rstrip()}\n", "</file>\n", "\n", "Please, edit it according to the following instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\n", "You should make edit in a way that keeps the code style, spacing and indentation consistent and reasonable!\n", "{restriction_note}\n", "\n", "Please, do it using single or multiple calls to the `replace_text` tool. \n", "Requirements to tool calls:\n", "- All these calls should be done all ALL AT ONCE, NOT sequentially.\n", "- All calls should use DISJOINT ranges of lines.\n", "\"\"\"\n", "\n", "        system_prompt = \"\"\"\n", "You are highly advanced and intelligent AI code assistant. \n", "Your task is to edit a file based on user's instructions. \n", "\"\"\"\n", "\n", "        claude_response = run_anthropic_w_retries(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ],\n", "            system_prompt,\n", "            use_tools=True,\n", "            only_replace_tool=True,\n", "        )\n", "\n", "        compiled_text, line_ranges = postprocess_claude_response(\n", "            provided_code=mist_formatting_data[\"full_file_in_prompt\"],\n", "            claude_response=claude_response,\n", "            call_adjust_line_number=True,\n", "        )\n", "\n", "        return {\n", "            \"line_ranges\": line_ranges,\n", "            \"compiled_text\": compiled_text,\n", "            \"numbered_code\": regular_code,\n", "        }\n", "\n", "\n", "class CodeEditsRewrite:\n", "    @classmethod\n", "    def get_response(\n", "        cls,\n", "        prompt_input: ChatPromptInput,\n", "        prompt_output,\n", "        num_lines_in_prefix_suffix: int = 500,\n", "    ) -> dict:\n", "        prefix = prompt_input.prefix\n", "        selected_code = prompt_input.selected_code\n", "        suffix = prompt_input.suffix\n", "        file_path = prompt_input.path\n", "\n", "        xml_code, regular_code, table_code, mist_formatting_data = format_code(\n", "            prefix=prefix,\n", "            selected_code=selected_code,\n", "            suffix=suffix,\n", "            num_lines_in_prefix_suffix=num_lines_in_prefix_suffix,\n", "            is_highlighted=True,\n", "        )\n", "\n", "        prompt = f\"\"\"I have opened a file `{file_path}` and highlighted a part of the code (enclosed in <highlighted_code> tag):\n", "<file path=\"{file_path}\">\n", "{mist_formatting_data[\"no_numbering_code\"].rstrip()}\n", "</file>\n", "\n", "Please, rewrite the highlighted region according to the following instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\n", "Remember that you should rewrite ONLY the highlighted region.\n", "\n", "Use this output format:\n", "```\n", "<highlighted_code>\n", "INSERT EDITED CODE HERE\n", "</highlighted_code>\n", "```\n", "\"\"\"\n", "\n", "        system_prompt = \"\"\"\n", "You are highly advanced and intelligent AI code assistant. \n", "Your task is to edit code based on user's instructions. \n", "\"\"\"\n", "\n", "        claude_response = run_anthropic_w_retries(\n", "            prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ],\n", "            system_prompt,\n", "            use_tools=False,\n", "        )\n", "\n", "        #### Custom postprocessing\n", "        assert len(claude_response.content) == 1  # type: ignore\n", "        claude_response_text = claude_response.content[0].text  # type: ignore\n", "\n", "        assert get_num_patterns(claude_response_text, \"```\", \"```\") == 1\n", "        assert (\n", "            get_num_patterns(\n", "                claude_response_text, \"<highlighted_code>\\n\", \"\\n</highlighted_code>\"\n", "            )\n", "            == 1\n", "        )\n", "\n", "        replace_response = extract_between_patterns(\n", "            claude_response_text, \"<highlighted_code>\\n\", \"</highlighted_code>\", 0\n", "        )\n", "\n", "        file_in_prompt = mist_formatting_data[\"full_file_in_prompt\"]\n", "        file_in_prompt_lines = file_in_prompt.splitlines(True)\n", "        updated_file = (\n", "            file_in_prompt_lines[\n", "                : mist_formatting_data[\"first_highlighted_line_number\"] - 1\n", "            ]\n", "            + [replace_response]\n", "            + file_in_prompt_lines[\n", "                mist_formatting_data[\"last_highlighted_line_number\"] :\n", "            ]\n", "        )\n", "        updated_file = \"\".join(updated_file)\n", "        diff = \"\".join(\n", "            unified_diff(\n", "                file_in_prompt.splitlines(True),\n", "                updated_file.splitlines(True),\n", "            )\n", "        )\n", "        compiled_text = f\"\"\"Raw response:\n", "{claude_response_text}\n", "{'=' * 30}\n", "\n", "Diff:\n", "```\n", "{diff}\n", "```\n", "\"\"\"\n", "        #### Custom postprocessing\n", "\n", "        return {\n", "            \"line_ranges\": [\n", "                (\n", "                    mist_formatting_data[\"first_highlighted_line_number\"],\n", "                    mist_formatting_data[\"last_highlighted_line_number\"],\n", "                )\n", "            ],\n", "            \"compiled_text\": compiled_text,\n", "            \"numbered_code\": regular_code,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MULTIFILE_SMARTPASTE_SAMPLES = [\n", "    (\n", "        \"0b320251-c408-4755-b474-179174f0f084\",\n", "        \"/home/<USER>/repos/augment/research/fastbackward/model.py\",\n", "        0,\n", "    ),\n", "    (\n", "        \"0b320251-c408-4755-b474-179174f0f084\",\n", "        \"/home/<USER>/repos/augment/research/fastbackward/train_rlhf.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"af449c14-1458-481d-a8a4-7d2ba396489b\",\n", "        \"/home/<USER>/repos/augment/base/prompt_format_chat/__init__.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/base/prompt_format_chat/conftest.py\",\n", "        1,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/base/prompt_format_chat/lib/chat_history_builder_test.py\",\n", "        2,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/clients/vscode/src/__tests__/chat/chat-model.test.ts\",\n", "        3,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/clients/vscode/src/augment-api.ts\",\n", "        4,\n", "    ),\n", "    (\n", "        \"a2c4084a-4737-44ff-a1be-a35ca0fa989b\",\n", "        \"/home/<USER>/repos/augment/clients/intellij/src/test/kotlin/com/augmentcode/intellij/chat/ChatRequestTest.kt\",\n", "        5,\n", "    ),\n", "]\n", "\n", "STAGING_SHARD0_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    \"0696c744-9fb8-4b50-b655-fad32ddb38d5\",\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\",\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "    # <PERSON>'s new samples\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\",\n", "]\n", "\n", "OLD_DOGFOOD_SAMPLES = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "]\n", "\n", "\n", "# Smartpaste\n", "# SAMPLES = OLD_DOGFOOD_SAMPLES + STAGING_SHARD0_SAMPLES + MULTIFILE_SMARTPASTE_SAMPLES\n", "\n", "# Code edits\n", "SAMPLES = OLD_DOGFOOD_SAMPLES + STAGING_SHARD0_SAMPLES\n", "\n", "\n", "download_samples = get_chat_samples(\n", "    [s if isinstance(s, str) else s[0] for s in SAMPLES]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["html_report = \"\"\n", "\n", "\n", "for sample in tqdm(SAMPLES):\n", "    if isinstance(sample, str):\n", "        request_id = sample\n", "        sample = download_samples[request_id]\n", "        file_path_to_apply = None\n", "        block_id = 0\n", "    else:\n", "        request_id, file_path_to_apply, block_id = sample  # type: ignore\n", "        sample = download_samples[request_id]\n", "\n", "    # Smartpaste\n", "    # prompt_input, prompt_output = format_sample(sample, num_chunks_to_keep=0)\n", "    # code_block = extract_between_patterns(\n", "    #     sample[\"response\"][\"text\"], \"```\", \"```\", block_id\n", "    # )\n", "    # response = SmartpasteApplyCodeblock.get_response(\n", "    #     prompt_input,\n", "    #     prompt_output,\n", "    #     sample[\"response\"][\"text\"],\n", "    #     code_block,\n", "    #     file_path_to_apply,\n", "    # )\n", "\n", "    # SmartpasteV2\n", "    # prompt_input, prompt_output = format_sample(sample, num_chunks_to_keep=0)\n", "    # code_block = extract_between_patterns(\n", "    #     sample[\"response\"][\"text\"], \"```\", \"```\", block_id\n", "    # )\n", "    # response = SmartpasteApplyCodeblockV2.get_response(\n", "    #     prompt_input,\n", "    #     prompt_output,\n", "    #     sample[\"response\"][\"text\"],\n", "    #     code_block,\n", "    #     file_path_to_apply,\n", "    # )\n", "\n", "    # CodeEditsWithTools\n", "    # assert file_path_to_apply is None\n", "    # prompt_input, prompt_output = format_sample(sample, num_chunks_to_keep=10, drop_history=True)\n", "    # response = CodeEditsWithTools.get_response(\n", "    #     prompt_input,\n", "    #     prompt_output,\n", "    #     num_lines_in_prefix_suffix=500,\n", "    # )\n", "\n", "    # CodeEditsWithToolsV2\n", "    assert file_path_to_apply is None\n", "    prompt_input, prompt_output = format_sample(\n", "        sample, num_chunks_to_keep=10, drop_history=True\n", "    )\n", "    response = CodeEditsWithToolsV2.get_response(\n", "        prompt_input,\n", "        prompt_output,\n", "        num_lines_in_prefix_suffix=500,\n", "    )\n", "\n", "    # CodeEditsRewrite\n", "    # assert file_path_to_apply is None\n", "    # prompt_input, prompt_output = format_sample(\n", "    #     sample, num_chunks_to_keep=10, drop_history=True\n", "    # )\n", "    # response = CodeEditsRewrite.get_response(\n", "    #     prompt_input,\n", "    #     prompt_output,\n", "    #     num_lines_in_prefix_suffix=500,\n", "    # )\n", "\n", "    # HTML report\n", "    cur_html = f\"<h2>Chain {request_id}</h2>\"\n", "    if request_id in STAGING_SHARD0_SAMPLES:\n", "        cur_html += f'<a href=\"{STAGING_DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "    else:\n", "        cur_html += f'<a href=\"{DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "\n", "    numbered_code = response[\"numbered_code\"].replace(\"\\n\", \"<br>\")\n", "    cur_html += row_html(\n", "        f'<div class=\"code\">{numbered_code}</div>'\n", "        + f'<div class=\"code\">{markdown(response[\"compiled_text\"])}</div>'\n", "        + f'<div class=\"code\">{response[\"line_ranges\"]}</div>'\n", "        + \"<hr>\"\n", "    )\n", "    html_report += cur_html\n", "\n", "html_report = wrap_html(html_report)\n", "with open(\"./claude_v308_cet.html\", \"w\") as f:\n", "    f.write(html_report)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
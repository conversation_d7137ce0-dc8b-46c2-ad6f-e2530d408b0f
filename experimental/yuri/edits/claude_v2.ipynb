{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"PYTHONPATH\"] = (\n", "    \":/home/<USER>/repos/augment:/home/<USER>/repos/augment/research/gpt-neox\"\n", ")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "from google.cloud import bigquery\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "from base.prompt_format.common import Exchange\n", "from tqdm import tqdm\n", "from pathlib import Path\n", "\n", "\n", "PROJECT_ID = \"system-services-prod\"\n", "DATASET_NAME = \"staging_request_insight_full_export_dataset\"\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", ")\n", "PROMPT_FORMATTER = get_structured_chat_prompt_formatter_by_name(\n", "    \"structured-binks-claude\", TOKEN_APPORTIONMENT\n", ")\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    \"augment-387916\", \"us-east5\", \"claude-3-5-sonnet@20240620\", 0, 1024 * 5\n", ")\n", "DOGFOOD_URL = (\n", "    \"https://support.dogfood.t.us-central1.prod.augmentcode.com/t/dogfood/request\"\n", ")\n", "STAGING_DOGFOOD_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request\"\n", "\n", "\n", "def get_chat_samples(request_ids):\n", "    query = f\"\"\"\n", "SELECT\n", "\tmetadata.request_id AS request_id,\n", "\tmetadata.raw_json AS metadata,\n", "\trequest.raw_json AS request,\n", "\tresponse.raw_json AS response,\n", "    metadata.time AS time\n", "FROM {PROJECT_ID}.{DATASET_NAME}.request_metadata AS metadata\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_request AS request\n", "\tON request.request_id = metadata.request_id\n", "JOIN {PROJECT_ID}.{DATASET_NAME}.chat_host_response AS response\n", "\tON response.request_id = metadata.request_id\n", "WHERE\n", "\tmetadata.request_id IN ({','.join(f'\"{request_id}\"' for request_id in request_ids)})\n", "\"\"\"\n", "\n", "    client = bigquery.Client(project=PROJECT_ID)\n", "    all_rows = list(client.query(query).result())\n", "    chat_rows_dic = {}\n", "    for row in all_rows:\n", "        assert row.request_id not in chat_rows_dic\n", "        chat_rows_dic[row.request_id] = {\n", "            \"request\": row.request[\"request\"],\n", "            \"response\": row.response[\"response\"],\n", "            \"metadata\": row.metadata,\n", "            \"datetime\": row.time.isoformat(),\n", "            \"row\": row,\n", "        }\n", "\n", "    return chat_rows_dic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["NEW_SAMPLES = [\n", "    \"5bad9dcf-fa58-4b38-b924-cad904c8ea04\",\n", "    \"3766342c-cee1-46b1-9efd-2b27a3b8194c\",\n", "    \"e525eabe-ef8a-4afb-ae4a-783ac102b433\",\n", "    \"a0ecbb63-5f96-4d65-8dc0-0880eead8e3f\",\n", "    \"26f89fa5-8755-4e43-80a2-fab4755e2e94\",\n", "    \"7da86c2c-487e-4040-9b35-0d1e6df737b1\",\n", "    \"f0658b38-f747-41e6-b70f-be1752a48dcf\",\n", "    \"2ceea890-7bf8-4b46-9875-a87254b12351\",\n", "    \"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\",\n", "]\n", "\n", "STAGING_SAMPLES = [\n", "    # <PERSON><PERSON>'s new samples\n", "    \"0696c744-9fb8-4b50-b655-fad32ddb38d5\",\n", "    \"81cdd199-a09b-48a0-9b32-bebf5859cec2\",\n", "    \"d4461b5a-9f6d-46f5-ab2d-697dcc4e78a7\",\n", "    \"ed00fdd3-fda8-44b1-85bb-5b0096002607\",\n", "] + NEW_SAMPLES\n", "\n", "REQUEST_IDS = [\n", "    # Guy's samples\n", "    \"579cbdb3-c0a1-4d18-a247-8fb09f32f4f3\",\n", "    # \"dbc4c457-9857-457b-91ca-a46ea75b19a2\",\n", "    \"0ae73c71-d915-433d-9426-e4533ec62df5\",\n", "    \"7fd0623b-c217-4658-89f9-af27246f7bfd\",\n", "    \"58954470-3d48-4e27-b2c1-ade336fb5fd8\",\n", "    # \"48e2c7f5-f6f5-4cde-9076-093d3dfb6717\",\n", "    \"75aedc45-11f6-4a5a-98d9-43798ba29479\",\n", "    \"24c6de2b-4131-476a-a140-af99fb53d17b\",\n", "    \"17276560-0a77-4acd-917f-740f4a4e1f30\",\n", "    \"9c946c1e-1b99-4d3b-84f7-398e875a26a5\",  # ???\n", "    \"7636a168-e3fe-4e7e-b343-2c45de4da1cb\",\n", "    \"f6d7c8cc-8872-49bc-82cb-ea23eac4bb50\",\n", "] + STAGING_SAMPLES\n", "\n", "# REQUEST_IDS = NEW_SAMPLES\n", "\n", "\n", "downloaded_samples = get_chat_samples(REQUEST_IDS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPECTED_OUTPUTS_DIR = Path(\n", "    \"/mnt/efs/augment/user/yuri/tmp/smart_paste_eval_sep5_expected_outputs\"\n", ")\n", "\n", "expected_outputs = {\n", "    request_id: (EXPECTED_OUTPUTS_DIR / (request_id + \".txt\")).read_text(\n", "        encoding=\"utf8\"\n", "    )\n", "    if (EXPECTED_OUTPUTS_DIR / (request_id + \".txt\")).exists()\n", "    else \"\"\n", "    for request_id in REQUEST_IDS\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1-turn Code instruction w/ XML tags\n", "\n", "\n", "def get_lines(text: str, start_line_offset: int, prefix: str = \" \") -> list[str]:\n", "    lines = []\n", "    for i, line in enumerate(text.splitlines(keepends=True)):\n", "        lines.append(f\"{prefix}{i+1+start_line_offset:04d}: {line}\")\n", "    return lines\n", "\n", "\n", "def get_lines_w_tags(text: str, start_line_offset: int) -> list[str]:\n", "    lines = []\n", "    for i, line in enumerate(text.splitlines(keepends=True)):\n", "        # cur_id = f\"{i+1+start_line_offset:04d}\"\n", "        cur_id = i + 1 + start_line_offset\n", "        lines.append(f\"<line number={cur_id}>{line.rstrip()}</line number={cur_id}>\\n\")\n", "    return lines\n", "\n", "\n", "def extract_between_patterns(main_string: str, p1: str, p2: str) -> str:\n", "    pattern = rf\"{re.escape(p1)}(.*?){re.escape(p2)}\"\n", "    match = re.search(pattern, main_string, re.DOTALL)\n", "    if match:\n", "        return match.group(1)\n", "    raise ValueError(f\"Substring between '{p1}' and '{p2}' not found\")\n", "\n", "\n", "def code_edits_prompt(prompt_input):\n", "    prompt = f\"\"\"I have file `{prompt_input.path}` open and has selected part of the code.\n", "\n", "Here is the full file:\n", "```\n", "{prompt_input.prefix}\n", "<selected_code>\n", "{prompt_input.selected_code}\n", "</selected_code>\n", "{prompt_input.suffix}\n", "```\n", "\n", "Rewrite the selected code (enclosed in `<selected_code>`) according to this instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\n", "Your response MUST meet the following requirements:\n", "- Response contains ONLY a single code block enclosed in \n", "    <updated_code>\n", "    ...\n", "    </updated_code> tag.\n", "- Updated code should serve as a drop-in replacement for the selected code\n", "- Update code should respect boundaries and indentation of the selected code.\n", "\"\"\"\n", "    return prompt\n", "\n", "\n", "class XmlCodeEdits:\n", "    def get_response(self, prompt_input: ChatPromptInput, prompt_output) -> dict:\n", "        last_message = code_edits_prompt(prompt_input)\n", "        response = list(\n", "            ANTHROPIC_CLIENT.generate_response_stream(\n", "                cur_message=last_message,\n", "                messages=[\n", "                    (item.request_message, item.response_text)\n", "                    for item in prompt_output.chat_history\n", "                ],\n", "                system_prompt=prompt_output.system_prompt,\n", "            )\n", "        )\n", "        response = \"\".join(map(str, response))\n", "\n", "        num_prefix_lines = len(prompt_input.prefix.splitlines())\n", "        num_selected_lines = len(prompt_input.selected_code.splitlines())\n", "\n", "        line_range = (\n", "            num_prefix_lines + 1,\n", "            num_prefix_lines + num_selected_lines,\n", "        )  # +1 is because we compare with smartpaste, which starts with 1\n", "        replacement_text = extract_between_patterns(\n", "            response, \"<updated_code>\", \"</updated_code>\"\n", "        )\n", "\n", "        numbered_prefix = get_lines(prompt_input.prefix, 0, \" \")\n", "        numbered_selected_code = get_lines(\n", "            prompt_input.selected_code, len(numbered_prefix), \"*\"\n", "        )\n", "        numbered_selected_code = \"\".join(numbered_selected_code)\n", "\n", "        return {\n", "            \"line_range\": line_range,\n", "            \"replacement_text\": replacement_text,\n", "            \"numbered_selected_code\": numbered_selected_code,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Smartpaste-based code edits\n", "\n", "\n", "def code_edits_prompt_tools(prompt_input):\n", "    numbered_prefix = get_lines(prompt_input.prefix, 0, \" \")\n", "    numbered_selected_code = get_lines(\n", "        prompt_input.selected_code, len(numbered_prefix), \"*\"\n", "    )\n", "    numbered_suffix = get_lines(\n", "        prompt_input.suffix, len(numbered_prefix) + len(numbered_selected_code), \" \"\n", "    )\n", "\n", "    numbered_prefix = \"\".join(numbered_prefix)\n", "    numbered_selected_code = \"\".join(numbered_selected_code)\n", "    numbered_suffix = \"\".join(numbered_suffix)\n", "\n", "    prompt = f\"\"\"I have file `{prompt_input.path}` open and has selected part of the code.\n", "\n", "Here is the text before editing, including line numbers. The selected code lines are marked with *.\n", "\n", "```\n", "{numbered_prefix}{numbered_selected_code}{numbered_suffix}\n", "```\n", "\n", "USING TOOL rewrite the selected code according to this instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\"\"\"\n", "    return prompt, {\"numbered_selected_code\": numbered_selected_code}\n", "\n", "\n", "def run_anthropic_w_tools(cur_message, history, system_prompt):\n", "    formatted_messages = []\n", "    for message in history:\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": message[0],\n", "            }\n", "        )\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": message[1],\n", "            }\n", "        )\n", "\n", "    # Add cur message\n", "    formatted_messages.append(\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": cur_message,\n", "        }\n", "    )\n", "\n", "    response = ANTHROPIC_CLIENT.client.messages.create(\n", "        model=\"claude-3-5-sonnet@20240620\",\n", "        max_tokens=8192,\n", "        messages=formatted_messages,\n", "        system=system_prompt,\n", "        temperature=0,\n", "        tools=[\n", "            {\n", "                \"name\": \"replace_text\",\n", "                \"description\": \"Replace substring of file with new text\",\n", "                \"input_schema\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        \"replacement_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The new text.\",\n", "                        },\n", "                        \"start_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text starts, inclusive.\",\n", "                        },\n", "                        \"end_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text ends, inclusive.\",\n", "                        },\n", "                    },\n", "                    \"required\": [\n", "                        \"replacement_text\",\n", "                        \"start_line_number\",\n", "                        \"end_line_number\",\n", "                    ],\n", "                },\n", "            }\n", "        ],\n", "    )\n", "    return response\n", "\n", "\n", "class SmartPaste1StepCodeEdits:\n", "    def get_response(self, prompt_input: ChatPromptInput, prompt_output) -> dict:\n", "        last_message, prompt_additional_info = code_edits_prompt_tools(prompt_input)\n", "        claude_response = run_anthropic_w_tools(\n", "            last_message,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ],\n", "            prompt_output.system_prompt,\n", "        )\n", "        tool_result = claude_response.content[1]\n", "        assert tool_result.type == \"tool_use\"\n", "\n", "        return {\n", "            \"line_range\": (\n", "                tool_result.input[\"start_line_number\"],\n", "                tool_result.input[\"end_line_number\"],\n", "            ),  # type: ignore\n", "            \"replacement_text\": tool_result.input[\"replacement_text\"],  # type: ignore\n", "            \"numbered_selected_code\": prompt_additional_info[\"numbered_selected_code\"],\n", "        }\n", "\n", "\n", "class SmartPaste2StepCodeEdits:\n", "    def get_response(\n", "        self, prompt_input: ChatPromptInput, prompt_output, llama_response: str\n", "    ) -> dict:\n", "        numbered_prefix = get_lines(prompt_input.prefix, 0, \" \")\n", "        numbered_selected_code = get_lines(\n", "            prompt_input.selected_code, len(numbered_prefix), \"*\"\n", "        )\n", "        numbered_suffix = get_lines(\n", "            prompt_input.suffix, len(numbered_prefix) + len(numbered_selected_code), \" \"\n", "        )\n", "\n", "        numbered_prefix = \"\".join(numbered_prefix[-10:])\n", "        numbered_selected_code = \"\".join(numbered_selected_code)\n", "        numbered_suffix = \"\".join(numbered_suffix[:10])\n", "\n", "        new_prompt = f\"\"\"\\\n", "Can you please apply the changes to the selected code? Use the tool.\n", "\n", "Here is the text before editing, including line numbers. The selected code lines are marked with *.\n", "```\n", "{numbered_prefix}{numbered_selected_code}{numbered_suffix}\n", "```\n", "\"\"\"\n", "\n", "        claude_response = run_anthropic_w_tools(\n", "            new_prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ]\n", "            + [(prompt_input.message, llama_response)],\n", "            prompt_output.system_prompt,\n", "        )\n", "        tool_result = claude_response.content[1]\n", "        assert tool_result.type == \"tool_use\"\n", "\n", "        return {\n", "            \"line_range\": (\n", "                tool_result.input[\"start_line_number\"],\n", "                tool_result.input[\"end_line_number\"],\n", "            ),  # type: ignore\n", "            \"replacement_text\": tool_result.input[\"replacement_text\"],  # type: ignore\n", "            \"numbered_selected_code\": numbered_selected_code,\n", "        }\n", "\n", "\n", "def execute_replacement(start_line, end_line, replacement_text, init_code):\n", "    init_code_lines = init_code.splitlines(keepends=True)\n", "\n", "    updated_code_lines = (\n", "        init_code_lines[: start_line - 1]\n", "        + replacement_text.splitlines(keepends=True)\n", "        + init_code_lines[end_line:]\n", "    )\n", "    numbered_updated_code = get_lines(\"\".join(updated_code_lines), 0, \" \")\n", "    return \"\".join(numbered_updated_code)\n", "\n", "\n", "class SmartPasteWithCheck2StepCodeEdits:\n", "    def get_response(\n", "        self, prompt_input: ChatPromptInput, prompt_output, llama_response: str\n", "    ) -> dict:\n", "        prefix = \"\".join(prompt_input.prefix.splitlines(keepends=True)[-10:])\n", "        suffix = \"\".join(prompt_input.suffix.splitlines(keepends=True)[:10])\n", "        selected_code = prompt_input.selected_code  # + \"\\n\"\n", "\n", "        numbered_prefix = get_lines(prefix, 0, \" \")\n", "        numbered_selected_code = get_lines(selected_code, len(numbered_prefix), \"*\")\n", "        numbered_suffix = get_lines(\n", "            suffix, len(numbered_prefix) + len(numbered_selected_code), \" \"\n", "        )\n", "\n", "        numbered_prefix = \"\".join(numbered_prefix)\n", "        numbered_selected_code = \"\".join(numbered_selected_code)\n", "        numbered_suffix = \"\".join(numbered_suffix)\n", "\n", "        new_prompt = f\"\"\"\\\n", "Can you please apply the changes to the selected code? Use the tool.\n", "If that's needed to correctly accomplish the task, you can change code outside of the selected code.\n", "\n", "When you get result of tool execution, please check the correctness of applyed changes.\n", "If result is incorrect or contains issues do this step-by-step:\n", "    1. Mention that we are rolling back the first changes\n", "    2. Describe how tool parameters should be adjusted \n", "    3. Do second call (step) to the tool with ADJUSTED tool parameters. Make sure to NOT call tool again with the same parameters as the first call!!!\n", "If result is correct, please just say that result is correct.\n", "Things to check in result:\n", "- That boundaries of replaced text are correct and DON'T eat up any additional lines or create duplicate lines.\n", "- There are no leftovers outside of the replaced/selected range that should've been removed, updated or just became redundant.\n", "- That text after replacement has correct formatting, including correct indentation.\n", "\n", "Here is the text before editing, including line numbers. The selected code lines are enclosed in <selected_code> tag.\n", "```\n", "{numbered_prefix}<selected_code>\n", "{numbered_selected_code}</selected_code>\n", "{numbered_suffix}\n", "```\n", "\"\"\"\n", "\n", "        claude_response = run_anthropic_w_tools(\n", "            new_prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ]\n", "            + [(prompt_input.message, llama_response)],\n", "            prompt_output.system_prompt,\n", "        )\n", "        tool_result = claude_response.content[1]\n", "        assert tool_result.type == \"tool_use\"\n", "\n", "        if not tool_result.input[\"replacement_text\"].endswith(\"\\n\"):\n", "            tool_result.input[\"replacement_text\"] += \"\\n\"\n", "\n", "        updated_code = execute_replacement(\n", "            tool_result.input[\"start_line_number\"],\n", "            tool_result.input[\"end_line_number\"],\n", "            tool_result.input[\"replacement_text\"],\n", "            f\"{prefix}{prompt_input.selected_code}{suffix}\",\n", "        )\n", "\n", "        # Second call to <PERSON>\n", "        formatted_messages = []\n", "        for item in prompt_output.chat_history:\n", "            formatted_messages.append({\"role\": \"user\", \"content\": item.request_message})\n", "            formatted_messages.append(\n", "                {\"role\": \"assistant\", \"content\": item.response_text}\n", "            )\n", "        formatted_messages.append({\"role\": \"user\", \"content\": prompt_input.message})\n", "        formatted_messages.append({\"role\": \"assistant\", \"content\": llama_response})\n", "        formatted_messages.append({\"role\": \"user\", \"content\": new_prompt})\n", "        formatted_messages.append(\n", "            {\"role\": \"assistant\", \"content\": claude_response.content}\n", "        )\n", "        formatted_messages.append(\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": [\n", "                    {\n", "                        \"type\": \"tool_result\",\n", "                        \"tool_use_id\": tool_result.id,\n", "                        \"content\": updated_code,\n", "                    }\n", "                ],\n", "            }\n", "        )\n", "\n", "        claude_response2 = ANTHROPIC_CLIENT.client.messages.create(\n", "            model=\"claude-3-5-sonnet@20240620\",\n", "            max_tokens=8192,\n", "            messages=formatted_messages,\n", "            system=prompt_output.system_prompt,\n", "            temperature=0,\n", "            tools=[\n", "                {\n", "                    \"name\": \"replace_text\",\n", "                    \"description\": \"Replace text with new text\",\n", "                    \"input_schema\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"replacement_text\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"The new text\",\n", "                            },\n", "                            \"start_line_number\": {\n", "                                \"type\": \"integer\",\n", "                                \"description\": \"The line number where the original text starts, inclusive\",\n", "                            },\n", "                            \"end_line_number\": {\n", "                                \"type\": \"integer\",\n", "                                \"description\": \"The line number where the original text ends, inclusive\",\n", "                            },\n", "                        },\n", "                        \"required\": [\n", "                            \"start_line_number\",\n", "                            \"end_line_number\",\n", "                            \"replacement_text\",\n", "                        ],\n", "                    },\n", "                }\n", "            ],\n", "        )\n", "\n", "        if claude_response2.stop_reason == \"tool_use\":\n", "            print(\"Refinement: True\")\n", "            tool_result = claude_response2.content[1]\n", "            assert tool_result.type == \"tool_use\"\n", "\n", "            if not tool_result.input[\"replacement_text\"].endswith(\"\\n\"):\n", "                tool_result.input[\"replacement_text\"] += \"\\n\"\n", "\n", "            # updated_code2 = execute_replacement(\n", "            #     tool_result.input[\"start_line_number\"],\n", "            #     tool_result.input[\"end_line_number\"],\n", "            #     tool_result.input[\"replacement_text\"],\n", "            #     f\"{prefix}{prompt_input.selected_code}{suffix}\",\n", "            # )\n", "            is_refined = True\n", "        elif claude_response2.stop_reason == \"end_turn\":\n", "            print(\"Refinement: False\")\n", "            is_refined = False\n", "        else:\n", "            raise Exception(f\"Unknown stop reason: {claude_response2.stop_reason}\")\n", "\n", "        return {\n", "            \"line_range\": (\n", "                tool_result.input[\"start_line_number\"],\n", "                tool_result.input[\"end_line_number\"],\n", "            ),  # type: ignore\n", "            \"replacement_text\": tool_result.input[\"replacement_text\"],  # type: ignore\n", "            \"numbered_selected_code\": numbered_selected_code,\n", "            \"is_refined\": is_refined,\n", "        }\n", "\n", "\n", "class SmartPastePlusXml2StepCodeEdits:\n", "    def get_response(\n", "        self, prompt_input: ChatPromptInput, prompt_output, llama_response: str\n", "    ) -> dict:\n", "        prefix = \"\".join(prompt_input.prefix.splitlines(keepends=True)[-10:])\n", "        suffix = \"\".join(prompt_input.suffix.splitlines(keepends=True)[:10])\n", "        selected_code = prompt_input.selected_code  # + \"\\n\"\n", "\n", "        numbered_prefix = get_lines_w_tags(prefix, 0)\n", "        numbered_selected_code = get_lines_w_tags(selected_code, len(numbered_prefix))\n", "        numbered_suffix = get_lines_w_tags(\n", "            suffix, len(numbered_prefix) + len(numbered_selected_code)\n", "        )\n", "\n", "        numbered_prefix = \"\".join(numbered_prefix[-10:])\n", "        numbered_selected_code = \"\".join(numbered_selected_code)\n", "        numbered_suffix = \"\".join(numbered_suffix[:10])\n", "\n", "        new_prompt = f\"\"\"\\\n", "Software developer highlighted a part of the code and asks you to apply changes here. You have to do it with a SINGLE call to the tool.\n", "\n", "Treat highlighted code as only a hint of a ROUGH location where change should be applied, NOT the strict boundary.\n", "Make sure to follow the previous instructions precisely — introduce no additional changes, and ensure that all of the suggested modifications are applied.\n", "\n", "```\n", "{numbered_prefix}<highlighted_code>\n", "{numbered_selected_code}</highlighted_code>\n", "{numbered_suffix}\n", "```\n", "\"\"\"\n", "\n", "        claude_response = run_anthropic_w_tools(\n", "            new_prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ]\n", "            + [(prompt_input.message, llama_response)],\n", "            prompt_output.system_prompt,\n", "        )\n", "        tool_result = claude_response.content[1]\n", "        assert tool_result.type == \"tool_use\"\n", "\n", "        return {\n", "            \"line_range\": (\n", "                tool_result.input[\"start_line_number\"],\n", "                tool_result.input[\"end_line_number\"],\n", "            ),  # type: ignore\n", "            \"replacement_text\": tool_result.input[\"replacement_text\"],  # type: ignore\n", "            \"numbered_selected_code\": numbered_selected_code,\n", "        }\n", "\n", "\n", "class SmartPastePlusXml1StepCodeEdits:\n", "    def get_response(self, prompt_input: ChatPromptInput, prompt_output) -> dict:\n", "        numbered_prefix = get_lines_w_tags(prompt_input.prefix, 0)\n", "        numbered_selected_code = get_lines_w_tags(\n", "            prompt_input.selected_code, len(numbered_prefix)\n", "        )\n", "        numbered_suffix = get_lines_w_tags(\n", "            prompt_input.suffix, len(numbered_prefix) + len(numbered_selected_code)\n", "        )\n", "\n", "        numbered_prefix = \"\".join(numbered_prefix)\n", "        numbered_selected_code = \"\".join(numbered_selected_code)\n", "        numbered_suffix = \"\".join(numbered_suffix)\n", "\n", "        new_prompt = f\"\"\"I have file `{prompt_input.path}` open and has selected part of the code.\n", "\n", "Here is the file. Every line is enclosed into <line number=XXX> tag. The selected code lines are enclosed in <selected_code> tag.\n", "```\n", "{numbered_prefix}<selected_code>\n", "{numbered_selected_code}</selected_code>\n", "{numbered_suffix}\n", "```\n", "\n", "Here is the instruction:\n", "<instruction>\n", "{prompt_input.message}\n", "</instruction>\n", "\n", "Can you please apply the instruction to the selected code? Do it with a SINGLE call to the tool.\n", "Feel free to change code outside of the selected code if needed to correctly apply the instruction.\n", "\n", "\"\"\"\n", "\n", "        claude_response = run_anthropic_w_tools(\n", "            new_prompt,\n", "            [\n", "                (item.request_message, item.response_text)\n", "                for item in prompt_output.chat_history\n", "            ],\n", "            prompt_output.system_prompt,\n", "        )\n", "        tool_result = claude_response.content[1]\n", "        assert tool_result.type == \"tool_use\"\n", "\n", "        return {\n", "            \"line_range\": (\n", "                tool_result.input[\"start_line_number\"],\n", "                tool_result.input[\"end_line_number\"],\n", "            ),  # type: ignore\n", "            \"replacement_text\": tool_result.input[\"replacement_text\"],  # type: ignore\n", "            \"numbered_selected_code\": numbered_selected_code,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["responses = {}\n", "prompt_inputs = {}\n", "\n", "for request_id in [\"e3af9458-2ece-4c57-9dfd-8bf0773aec9f\"]:  # tqdm(REQUEST_IDS):\n", "    s = downloaded_samples[request_id]\n", "\n", "    s_history = [\n", "        Exchange(e[\"request_message\"], e[\"response_text\"], e[\"request_id\"])\n", "        for e in s[\"row\"].request[\"request\"].get(\"chat_history\", [])\n", "    ]\n", "    s_chunks = [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            char_start=chunk.get(\"char_offset\", 0),\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk[\"blob_name\"],\n", "            origin=chunk[\"origin\"],\n", "        )\n", "        for chunk in s[\"row\"].request[\"retrieved_chunks\"]\n", "    ]\n", "    request_entry = s[\"row\"].request[\"request\"]\n", "\n", "    if request_entry.get(\"suffix\", \"\").startswith(\"\\n\"):\n", "        request_entry[\"selected_code\"] += \"\\n\"\n", "        request_entry[\"suffix\"] = request_entry[\"suffix\"][1:]\n", "        print(\"Suffix starts with new line :/\")\n", "\n", "    prompt_input = ChatPromptInput(\n", "        path=request_entry[\"path\"],\n", "        prefix=request_entry.get(\"prefix\", \"\"),\n", "        selected_code=request_entry[\"selected_code\"],\n", "        suffix=request_entry.get(\"suffix\", \"\"),\n", "        message=request_entry[\"message\"],\n", "        chat_history=s_history,\n", "        prefix_begin=0,\n", "        suffix_end=len(\n", "            request_entry.get(\"prefix\", \"\")\n", "            + request_entry[\"selected_code\"]\n", "            + request_entry.get(\"suffix\", \"\")\n", "        ),\n", "        retrieved_chunks=s_chunks,\n", "        context_code_exchange_request_id=\"new\",\n", "    )\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(prompt_input)\n", "\n", "    # response = SmartPaste1StepCodeEdits().get_response(prompt_input, prompt_output)\n", "    # response = SmartPaste2StepCodeEdits().get_response(\n", "    #     prompt_input, prompt_output, downloaded_samples[request_id][\"response\"][\"text\"]\n", "    # )\n", "    # response = XmlCodeEdits().get_response(prompt_input, prompt_output)\n", "    # response = SmartPasteWithCheck2StepCodeEdits().get_response(\n", "    #     prompt_input, prompt_output, downloaded_samples[request_id][\"response\"][\"text\"]\n", "    # )\n", "    response = SmartPastePlusXml2StepCodeEdits().get_response(\n", "        prompt_input, prompt_output, downloaded_samples[request_id][\"response\"][\"text\"]\n", "    )\n", "    # response = SmartPastePlusXml1StepCodeEdits().get_response(\n", "    #     prompt_input, prompt_output\n", "    # )\n", "\n", "    responses[request_id] = response\n", "    prompt_inputs[request_id] = prompt_input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yuri.preferences.utils import row_html, wrap_html, markdown\n", "\n", "\n", "html = \"\"\n", "for request_id in REQUEST_IDS:\n", "    cur_html = f\"<h2>Chain {request_id}</h2>\"\n", "    if request_id in STAGING_SAMPLES:\n", "        cur_html += f'<a href=\"{STAGING_DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "    else:\n", "        cur_html += f'<a href=\"{DOGFOOD_URL}/{request_id}\">{request_id}</a>'\n", "\n", "    response = responses[request_id]\n", "\n", "    _n_selected_code = (\n", "        response[\"numbered_selected_code\"]\n", "        .replace(\"<line number=\", \"\")\n", "        .replace(\"</line number=\", \"\")\n", "    )\n", "    # _upd = []\n", "    # for _l in _n_selected_code.splitlines(keepends=True):\n", "    #     _l = _l[:4] + \":   \" + _l[5:]\n", "    #     _l = _l[:-6] + \"\\n\"\n", "    #     _upd.append(_l)\n", "    # _n_selected_code = \"\".join(_upd)\n", "    _n_selected_code = _n_selected_code.replace(\"\\n\", \"<br>\")\n", "\n", "    m_selected_code = (\n", "        f'<div class=\"code\">{markdown(prompt_inputs[request_id].selected_code)}</div>'\n", "    )\n", "    m_numbered_selected_code = f'<div class=\"code\">{_n_selected_code}</div>'\n", "    m_replacement_text = (\n", "        f'<div class=\"code\">{markdown(response[\"replacement_text\"])}</div>'\n", "    )\n", "    m_line_range = f'<div class=\"code\">{markdown(str(response[\"line_range\"]))}</div>'\n", "\n", "    m_target = f'<div class=\"code\">{markdown(expected_outputs[request_id])}</div>'\n", "    cur_html += row_html(\n", "        m_selected_code\n", "        + m_numbered_selected_code\n", "        + m_replacement_text\n", "        + m_line_range\n", "        + m_target\n", "    )\n", "\n", "    html += cur_html\n", "    html += \"<hr>\"\n", "html = wrap_html(html)\n", "\n", "# with open(\"./claude_v43_xml.html\", \"w\") as f:\n", "# with open(\"./claude_v44_sm2step.html\", \"w\") as f:\n", "# with open(\"./claude_v41_sm1step.html\", \"w\") as f:\n", "# with open(\"./claude_v69_sm2step_plus_xml.html\", \"w\") as f:\n", "with open(\"./claude_v87_sm2step_plus_xml.html\", \"w\") as f:\n", "    f.write(html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_output.system_prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
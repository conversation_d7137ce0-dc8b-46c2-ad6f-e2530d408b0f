{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import re\n", "import difflib\n", "\n", "\n", "from tools.bazel_runner.review_edit_bot.protocols import ReviewEditPrompt\n", "from tools.bazel_runner.review_edit_bot.text import PromptBuilder, _get_diff_line, resolve_diff_hunk\n", "from research.eval.harness.systems.remote_edit_system import RemoteEditSystem\n", "from research.eval.harness.systems.all_systems import DroidCodeEditSystem \n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from research.data.synthetic_code_edit.util_lib import get_unified_diff\n", "\n", "from types import SimpleNamespace\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def download_pr_comment(repo_owner, repo_name, comment_id, access_token):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/comments/{comment_id}\"\n", "    \n", "    headers = {\n", "        \"Authorization\": f\"token {access_token}\",\n", "        \"Accept\": \"application/vnd.github.v3+json\"\n", "    }\n", "\n", "    response = requests.get(url, headers=headers)\n", "\n", "    if response.status_code == 200:\n", "        return response.json()\n", "    else:\n", "        # print(f\"Failed to download review comment. Status code: {response.status_code}, Message: {response.text}\")\n", "        # return None\n", "        assert False\n", "\n", "def download_file_at_commit(repo_owner, repo_name, file_path, commit_sha, access_token):\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/contents/{file_path}?ref={commit_sha}\"\n", "    \n", "    headers = {\n", "        \"Authorization\": f\"token {access_token}\",\n", "        \"Accept\": \"application/vnd.github.v3.raw\"\n", "    }\n", "\n", "    response = requests.get(url, headers=headers)\n", "\n", "    if response.status_code == 200:\n", "        return response.content  # This is the raw content of the file\n", "    else:\n", "        # print(f\"Failed to download file. Status code: {response.status_code}, Message: {response.text}\")\n", "        # return None\n", "        assert False\n", "\n", "\n", "def clear_marked_lines(code):\n", "    result = []\n", "    for line in code.splitlines(True):\n", "        if line.startswith(\"|>\"):\n", "            result.append(line[2:])\n", "        else:\n", "            result.append(line)\n", "    return \"\".join(result)\n", "\n", "\n", "class LineContextReviewEditPromptExtractor:\n", "    \"\"\"Extracts the prompt from a comment.\n", "\n", "    It will use the context of the line where the comment is located as prompt to edit.\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        prefix_lines: int,\n", "        suffix_lines: int,\n", "        content_prefix_lines: int,\n", "        content_suffix_lines: int,\n", "    ):\n", "        self.prefix_lines = prefix_lines\n", "        self.suffix_lines = suffix_lines\n", "        self.content_prefix_lines = content_prefix_lines\n", "        self.content_suffix_lines = content_suffix_lines\n", "\n", "    def get_prompt(\n", "        self, file_content: str, comment\n", "    ):\n", "        \"\"\"Get the prompt for the given file content and line position.\"\"\"\n", "        builder = PromptBuilder(comment.side)\n", "\n", "        start_line = comment.start_line\n", "        if start_line == -1:\n", "            start_line = comment.line\n", "\n", "        diff_start_line, diff_end_line = _get_diff_line(comment.diff_hunk, comment.side)\n", "\n", "        # To which lines the comment is attached\n", "        commented_line_range = [\n", "            comment.start_line if comment.start_line > -1 else comment.line,\n", "            comment.line,\n", "        ]\n", "\n", "        # the prompt cannot be outside the diff hunk\n", "        start_line = max(diff_start_line, start_line - self.content_prefix_lines)\n", "        end_line = min(diff_end_line, comment.line + self.content_suffix_lines)\n", "\n", "        prefix_start_line = max(0, start_line - self.prefix_lines)\n", "        suffix_end_line = end_line + self.suffix_lines\n", "\n", "        for i, line in enumerate(\n", "            resolve_diff_hunk(file_content, comment.diff_hunk, comment.side)\n", "        ):\n", "            line_index = i + 1\n", "            if line_index < prefix_start_line:\n", "                builder.add_far_prefix(line)\n", "            elif line_index > suffix_end_line:\n", "                builder.add_far_suffix(line)\n", "            elif line_index < start_line:\n", "                builder.add_prefix(line)\n", "            elif line_index > end_line:\n", "                builder.add_suffix(line)\n", "            else:\n", "                # LLM expects the commented lines to be marked with a '|>'\n", "                if commented_line_range[0] <= line_index <= commented_line_range[1]:\n", "                    line = f\"|>{line}\"\n", "                builder.add_selected_code(line, line_index)\n", "        return builder.build()\n", "\n", "\n", "def parse_all_diff_numbers(diff_hunk: str):\n", "    match = re.search(r'@@ -(\\d+),(\\d+) \\+(\\d+),(\\d+) @@', diff_hunk)\n", "    if match:\n", "        return (int(match.group(1)) - 1, int(match.group(2)),\n", "                int(match.group(3)) - 1, int(match.group(4)))\n", "    assert False\n", "\n", "\n", "\n", "def get_last_line_index_in_hunk(hunk):\n", "    lines = hunk.splitlines(True)\n", "    _, _, start, _ = parse_all_diff_numbers(lines[0])\n", "    num_after_lines = len([line for line in lines if not line.startswith(\"-\")]) - 1 # -1 for the header line\n", "    return start + num_after_lines\n", "    \n", "\n", "\n", "class LineContextReviewEditPromptExtractorV2:\n", "    \"\"\"Extracts the prompt from a comment.\n", "\n", "    It will use the context of the line where the comment is located as prompt to edit.\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        content_prefix_lines: int,\n", "        content_suffix_lines: int,\n", "    ):\n", "        self.content_prefix_lines = content_prefix_lines\n", "        self.content_suffix_lines = content_suffix_lines\n", "\n", "    def get_prompt(\n", "        self, file_content: str, comment\n", "    ):\n", "        \"\"\"Get the prompt for the given file content and line position.\"\"\"\n", "        builder = PromptBuilder(comment.side)\n", "\n", "        commented_line = get_last_line_index_in_hunk(comment.diff_hunk)\n", "\n", "        # the prompt cannot be outside the diff hunk\n", "        start_line = commented_line - self.content_prefix_lines\n", "        end_line = commented_line + self.content_suffix_lines\n", "\n", "        for i, line in enumerate(\n", "            resolve_diff_hunk(file_content, comment.diff_hunk, comment.side)\n", "        ):\n", "            line_index = i + 1\n", "            if line_index < start_line:\n", "                builder.add_prefix(line)\n", "            elif line_index > end_line:\n", "                builder.add_suffix(line)\n", "            else:\n", "                # LLM expects the commented lines to be marked with a '|>'\n", "                if line_index == commented_line:\n", "                    line = f\"|>{line}\"\n", "                builder.add_selected_code(line, line_index)\n", "        return builder.build()\n", "\n", "\n", "\n", "def parse_github_url(url):\n", "    pattern = r\"https://github\\.com/([^/]+)/([^/]+)/pull/(\\d+)/files#r(\\d+)\"\n", "    match = re.match(pattern, url)\n", "\n", "    if not match:\n", "        return {\"error\": \"URL does not match the expected GitHub pull request format.\"}\n", "\n", "    repo_owner, repo_name, pr_number, comment_id = match.groups()\n", "    return {\n", "        \"repo_owner\": repo_owner,\n", "        \"repo_name\": repo_name,\n", "        \"pr_number\": pr_number,\n", "        \"comment_id\": comment_id\n", "    }\n", "\n", "\n", "def parse_github_url_v2(url):\n", "    pattern = r\"https://github\\.com/([^/]+)/([^/]+)/pull/(\\d+)(?:/files#r(\\d+)|#discussion_r(\\d+))\"\n", "    match = re.match(pattern, url)\n", "\n", "    if not match:\n", "        return {\"error\": \"URL does not match the expected GitHub pull request format.\"}\n", "\n", "    repo_owner, repo_name, pr_number, comment_id_files, comment_id_discussion = match.groups()\n", "    comment_id = comment_id_files if comment_id_files else comment_id_discussion\n", "    return {\n", "        \"repo_owner\": repo_owner,\n", "        \"repo_name\": repo_name,\n", "        \"pr_number\": pr_number,\n", "        \"comment_id\": comment_id\n", "    }\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GITHUB_TOKEN = \"??\"\n", "\n", "def process_pr(url, system, prompt_extractor):\n", "    parsed_url = parse_github_url_v2(url)\n", "\n", "    comment = download_pr_comment(\n", "        parsed_url[\"repo_owner\"],\n", "        parsed_url[\"repo_name\"],\n", "        parsed_url[\"comment_id\"],\n", "        GITHUB_TOKEN\n", "    )\n", "    \n", "    file_text = download_file_at_commit(\n", "        parsed_url[\"repo_owner\"],\n", "        parsed_url[\"repo_name\"],\n", "        comment[\"path\"],\n", "        comment[\"original_commit_id\"],\n", "        GITHUB_TOKEN\n", "    ).decode()    \n", "\n", "\n", "    comment_obj = SimpleNamespace()    \n", "    comment_obj.start_line = comment[\"original_start_line\"] \n", "    comment_obj.line = comment[\"original_line\"] if comment[\"original_line\"] is not None else comment[\"line\"]\n", "    comment_obj.side = comment[\"side\"]\n", "    comment_obj.diff_hunk = comment[\"diff_hunk\"]\n", "\n", "    if comment_obj.start_line is None:\n", "        comment_obj.start_line = -1\n", "\n", "    \n", "    prompt = prompt_extractor.get_prompt(file_text, comment_obj)\n", "\n", "    system_input = ResearchEditPromptInput(\n", "        path=comment[\"path\"],\n", "        prefix=prompt.prefix,\n", "        selected_code=prompt.selected_code,\n", "        suffix=prompt.suffix,\n", "        instruction=f\"Fix PR comment: {comment['body']}\",\n", "        # instruction=comment[\"body\"],\n", "        retrieved_chunks=[],\n", "    )\n", "\n", "    model_output = system.generate(system_input)\n", "\n", "    return {\n", "        \"model_output\": model_output,\n", "        \"system_input\": system_input,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# system = RemoteEditSystem(\n", "#     url=\"https://dev-yuri.us-central.api.augmentcode.com\",\n", "#     model=\"droid-33B-FP8-R1-edit-prs\",\n", "#     api_token=\"43025CBE-5541-4E1B-8A37-811E0229B526\",\n", "#     inference_mode=\"api_proxy\",\n", "# )\n", "\n", "\n", "config = {\n", "  \"name\": \"droid_code_edit\",\n", "  \"model\": {\n", "    \"name\": \"fastforward_droid\",\n", "    # \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/yuri_pr_edits_mar9_75k_v4_v2/checkpoint_llama_iteration_6621_ffw\"\n", "    # \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/yuri_pr_edits_mar17_40k_filtered_v1/checkpoint_llama_iteration_2771_ffw\" \n", "    # \"checkpoint_path\": \"/home/<USER>/tmp/checkpoint_llama_iteration_839_ffw\"\n", "    # \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/pr_edits_feb_29_10k_stage3_15_try2_conditional_joined_stage2_h100/yuri_pr_edits_feb_29_10k_stage3_15_try2_conditional_joined_stage2_h100/checkpoint_llama_iteration_1187_ffw\"\n", "    # \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/yuri_pr_edits_mar18_55k_filtered_stage1_droid_repo_67_v1/checkpoint_llama_iteration_5086_ffw\"\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/yuri_pr_edits_mar18_55k_filtered_stage2_droid_repo_68_5086ckpt_v1/checkpoint_llama_iteration_2582_ffw\",\n", "    # \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/yuri_pr_edits_mar20_10align_v2/checkpoint_llama_iteration_1000_ffw\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/yuri_pr_edits_mar20_10align_v2/checkpoint_llama_iteration_2500_ffw\"\n", "  },\n", "  \"generation_options\": {\n", "    \"temperature\": 0,\n", "    \"top_k\": 0,\n", "    \"top_p\": 0,\n", "    \"max_generated_tokens\": 1024\n", "  }\n", "}\n", "system = DroidCodeEditSystem.from_yaml_config(config)\n", "\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_extractor = LineContextReviewEditPromptExtractorV2(10, 10)\n", "\n", "# Remove this line: https://github.com/augmentcode/augment/pull/5164/files#r1529146420\n", "# Remove another line: https://github.com/augmentcode/augment/pull/5164/files#r1529149849\n", "\n", "# Clear comment: https://github.com/augmentcode/augment/pull/5151/files#r1525754283\n", "# Rewrite configs: https://github.com/augmentcode/augment/pull/5207/files#r1527263235\n", "# Make tuple and not list: https://github.com/augmentcode/augment/pull/5151/files#r1525717268\n", "# map in typescript: https://github.com/augmentcode/augment/pull/4885/files#r1516636349\n", "# use -1 : https://github.com/augmentcode/augment/pull/5151/files#r1525719713\n", "\n", "# something: https://github.com/augmentcode/augment/pull/5247#discussion_r1528954795\n", "# ask for docstring https://github.com/augmentcode/augment/pull/4764/files#r1511651805\n", "\n", "# \"/mnt/efs/augment/eval/jobs\"? : https://github.com/augmentcode/augment/pull/5164/files#r1529134668\n", "# Is it necessary?: https://github.com/augmentcode/augment/pull/4731/files#r1510030471\n", "\n", "output = process_pr(\"https://github.com/augmentcode/augment/pull/5348#discussion_r1532576434\", system, prompt_extractor)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output[\"system_input\"].instruction)\n", "print(\"=\" * 40)\n", "print(output[\"system_input\"].selected_code)\n", "print(\"=\" * 40)\n", "print(output[\"model_output\"].generated_text)\n", "print(\"=\" * 40)\n", "print(get_unified_diff(clear_marked_lines(output[\"system_input\"].selected_code), output[\"model_output\"].generated_text))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generate comparisons"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_diff_html(input_code, output_code, comment, another_header=None):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><pre>{comment}</pre></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Comment: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    if another_header is not None:\n", "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n", "    return html\n", "\n", "def mark_lines(code, line_range):\n", "    result = []\n", "    for i, line in enumerate(code.splitlines(True)):\n", "        if line_range[0] <= i < line_range[1]:\n", "            result.append(f\"|>{line}\")\n", "        else:\n", "            result.append(f\"  {line}\")\n", "    return \"\".join(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_edit.prompt_formatter import ExceedContextLength\n", "\n", "\n", "prompt_extractor = LineContextReviewEditPromptExtractorV2(10, 10)\n", "\n", "\n", "# urls = [\n", "#     \"https://github.com/augmentcode/augment/pull/5164/files#r1529146420\",\n", "#     \"https://github.com/augmentcode/augment/pull/5164/files#r1529149849\",\n", "#     \"https://github.com/augmentcode/augment/pull/5151/files#r1525754283\",\n", "#     \"https://github.com/augmentcode/augment/pull/5207/files#r1527263235\",\n", "#     \"https://github.com/augmentcode/augment/pull/5151/files#r1525717268\",\n", "#     \"https://github.com/augmentcode/augment/pull/4885/files#r1516636349\",\n", "#     \"https://github.com/augmentcode/augment/pull/5151/files#r1525719713\",\n", "#     \"https://github.com/augmentcode/augment/pull/5247/files#r1528954795\",\n", "#     \"https://github.com/augmentcode/augment/pull/4764/files#r1511651805\",\n", "#     \"https://github.com/augmentcode/augment/pull/5164/files#r1529134668\"\n", "# ]\n", "\n", "# urls2 = ['https://github.com/augmentcode/augment/pull/5343#discussion_r1532534320',\n", "#  'https://github.com/augmentcode/augment/pull/5342#discussion_r1532496218',\n", "#  'https://github.com/augmentcode/augment/pull/5338#discussion_r1532076166',\n", "#  'https://github.com/augmentcode/augment/pull/5338#discussion_r1532540910',\n", "#  'https://github.com/augmentcode/augment/pull/5335#discussion_r1531539558',\n", "#  'https://github.com/augmentcode/augment/pull/5330#discussion_r1531303260',\n", "#  'https://github.com/augmentcode/augment/pull/5289#discussion_r1530736835',\n", "#  'https://github.com/augmentcode/augment/pull/5272#discussion_r1531120872',\n", "#  'https://github.com/augmentcode/augment/pull/5271#discussion_r1529587988',\n", "#  'https://github.com/augmentcode/augment/pull/5271#discussion_r1529589124',\n", "#  'https://github.com/augmentcode/augment/pull/5265#discussion_r1531126137',\n", "#  'https://github.com/augmentcode/augment/pull/5254#discussion_r1529540160',\n", "#  'https://github.com/augmentcode/augment/pull/5242#discussion_r1528885940',\n", "#  'https://github.com/augmentcode/augment/pull/5242#discussion_r1528913843',\n", "#  'https://github.com/augmentcode/augment/pull/5242#discussion_r1529015251',\n", "#  'https://github.com/augmentcode/augment/pull/5242#discussion_r1529018446',\n", "#  'https://github.com/augmentcode/augment/pull/5242#discussion_r1529022424',\n", "#  'https://github.com/augmentcode/augment/pull/5233#discussion_r1528845045',\n", "#  'https://github.com/augmentcode/augment/pull/5220#discussion_r1527602548',\n", "#  'https://github.com/augmentcode/augment/pull/5218#discussion_r1527594584',\n", "#  'https://github.com/augmentcode/augment/pull/5213#discussion_r1527406784',\n", "#  'https://github.com/augmentcode/augment/pull/5213#discussion_r1527409889']\n", "\n", "\n", "urls2 = ['https://github.com/augmentcode/augment/pull/5354#discussion_r1532757922',\n", " 'https://github.com/augmentcode/augment/pull/5354#discussion_r1532764775',\n", " 'https://github.com/augmentcode/augment/pull/5353#discussion_r1532882797',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532576434',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532578895',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532579607',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532750377',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532821790',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532863639',\n", " 'https://github.com/augmentcode/augment/pull/5343#discussion_r1532534320',\n", " 'https://github.com/augmentcode/augment/pull/5342#discussion_r1532496218',\n", " 'https://github.com/augmentcode/augment/pull/5338#discussion_r1532076166',\n", " 'https://github.com/augmentcode/augment/pull/5338#discussion_r1532540910',\n", " 'https://github.com/augmentcode/augment/pull/5335#discussion_r1531539558',\n", " 'https://github.com/augmentcode/augment/pull/5330#discussion_r1531303260',\n", " 'https://github.com/augmentcode/augment/pull/5326#discussion_r1531265153',\n", " 'https://github.com/augmentcode/augment/pull/5316#discussion_r1531228763',\n", " 'https://github.com/augmentcode/augment/pull/5289#discussion_r1530736835',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530533750',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530534732',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530540240',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530704842',\n", " 'https://github.com/augmentcode/augment/pull/5275#discussion_r1530718258',\n", " 'https://github.com/augmentcode/augment/pull/5272#discussion_r1531120872',\n", " 'https://github.com/augmentcode/augment/pull/5271#discussion_r1529587988',\n", " 'https://github.com/augmentcode/augment/pull/5271#discussion_r1529589124',\n", " 'https://github.com/augmentcode/augment/pull/5265#discussion_r1531126137',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529210048',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529210320',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529211511',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529212059',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529213120',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214083',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214332',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214594',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529215103',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529349702',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529349821',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529364380',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529366270',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529366936',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532816796',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532818246',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532820210',\n", " 'https://github.com/augmentcode/augment/pull/5254#discussion_r1529540160',\n", " 'https://github.com/augmentcode/augment/pull/5247#discussion_r1528954795',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528872880',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528876913',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528878576',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528885940',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528890317',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528913843',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528916308',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529007532',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529015251',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529018446',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529019532',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529020183',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529021131',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529022424',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529023162',\n", " 'https://github.com/augmentcode/augment/pull/5233#discussion_r1528845045',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527624096',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527630059',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527630244',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1528694853',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1528705577']\n", "\n", "\n", "MAIN_HTML = \"\"\n", "for url in tqdm(urls3):\n", "    try:\n", "        output = process_pr(url, system, prompt_extractor)\n", "    except ExceedContextLength as e:\n", "        print(f\"Failed to process {url}: {e}\")\n", "        continue\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<a href=\\\"{url}\\\">Comment link</a>\"\n", "    cur_diff = get_diff_html(\n", "        output[\"system_input\"].selected_code,\n", "        output[\"model_output\"].generated_text,\n", "        output[\"system_input\"].instruction,\n", "        \"Selected code <-> Generated code\"\n", "    )\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "# with open('test_augment_prs_mar18_joined2500_v1_code_edits_droid.html', 'w') as f:\n", "with open('test_augment_prs_mar20_v9.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["urls3 = ['https://github.com/augmentcode/augment/pull/5354#discussion_r1532757922',\n", " 'https://github.com/augmentcode/augment/pull/5354#discussion_r1532764775',\n", " 'https://github.com/augmentcode/augment/pull/5353#discussion_r1532882797',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532576434',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532578895',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532579607',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532750377',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532821790',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532863639',\n", " 'https://github.com/augmentcode/augment/pull/5343#discussion_r1532534320',\n", " 'https://github.com/augmentcode/augment/pull/5342#discussion_r1532496218',\n", " 'https://github.com/augmentcode/augment/pull/5338#discussion_r1532076166',\n", " 'https://github.com/augmentcode/augment/pull/5338#discussion_r1532540910',\n", " 'https://github.com/augmentcode/augment/pull/5335#discussion_r1531539558',\n", " 'https://github.com/augmentcode/augment/pull/5330#discussion_r1531303260',\n", " 'https://github.com/augmentcode/augment/pull/5326#discussion_r1531265153',\n", " 'https://github.com/augmentcode/augment/pull/5316#discussion_r1531228763',\n", " 'https://github.com/augmentcode/augment/pull/5289#discussion_r1530736835',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530533750',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530534732',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530540240',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530704842',\n", " 'https://github.com/augmentcode/augment/pull/5275#discussion_r1530718258',\n", " 'https://github.com/augmentcode/augment/pull/5272#discussion_r1531120872',\n", " 'https://github.com/augmentcode/augment/pull/5271#discussion_r1529587988',\n", " 'https://github.com/augmentcode/augment/pull/5271#discussion_r1529589124',\n", " 'https://github.com/augmentcode/augment/pull/5265#discussion_r1531126137',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529210048',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529210320',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529211511',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529212059',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529213120',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214083',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214332',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214594',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529215103',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529349702',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529349821',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529364380',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529366270',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529366936',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532816796',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532818246',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532820210',\n", " 'https://github.com/augmentcode/augment/pull/5254#discussion_r1529540160',\n", " 'https://github.com/augmentcode/augment/pull/5247#discussion_r1528954795',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528872880',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528876913',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528878576',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528885940',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528890317',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528913843',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528916308',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529007532',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529015251',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529018446',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529019532',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529020183',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529021131',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529022424',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529023162',\n", " 'https://github.com/augmentcode/augment/pull/5233#discussion_r1528845045',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527624096',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527630059',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527630244',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1528694853',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1528705577',\n", " 'https://github.com/augmentcode/augment/pull/5220#discussion_r1527602548',\n", " 'https://github.com/augmentcode/augment/pull/5219#discussion_r1527595236',\n", " 'https://github.com/augmentcode/augment/pull/5218#discussion_r1527594584',\n", " 'https://github.com/augmentcode/augment/pull/5213#discussion_r1527406784',\n", " 'https://github.com/augmentcode/augment/pull/5213#discussion_r1527409889',\n", " 'https://github.com/augmentcode/augment/pull/5207#discussion_r1527263235',\n", " 'https://github.com/augmentcode/augment/pull/5200#discussion_r1528922499',\n", " 'https://github.com/augmentcode/augment/pull/5198#discussion_r1527008717',\n", " 'https://github.com/augmentcode/augment/pull/5198#discussion_r1527008990',\n", " 'https://github.com/augmentcode/augment/pull/5198#discussion_r1527009121',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1526919506',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1526939425',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1526970788',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1526971286',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1526971612',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1528923124',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1528923744',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1528925339',\n", " 'https://github.com/augmentcode/augment/pull/5189#discussion_r1528939306',\n", " 'https://github.com/augmentcode/augment/pull/5180#discussion_r1529055183',\n", " 'https://github.com/augmentcode/augment/pull/5180#discussion_r1529076886',\n", " 'https://github.com/augmentcode/augment/pull/5178#discussion_r1526795463',\n", " 'https://github.com/augmentcode/augment/pull/5177#discussion_r1530695786',\n", " 'https://github.com/augmentcode/augment/pull/5177#discussion_r1530935739',\n", " 'https://github.com/augmentcode/augment/pull/5177#discussion_r1530947686',\n", " 'https://github.com/augmentcode/augment/pull/5177#discussion_r1530970897',\n", " 'https://github.com/augmentcode/augment/pull/5176#discussion_r1532987395',\n", " 'https://github.com/augmentcode/augment/pull/5173#discussion_r1527703254',\n", " 'https://github.com/augmentcode/augment/pull/5173#discussion_r1529018328',\n", " 'https://github.com/augmentcode/augment/pull/5163#discussion_r1526640192',\n", " 'https://github.com/augmentcode/augment/pull/5162#discussion_r1526802436',\n", " 'https://github.com/augmentcode/augment/pull/5162#discussion_r1526803265',\n", " 'https://github.com/augmentcode/augment/pull/5154#discussion_r1526818414',\n", " 'https://github.com/augmentcode/augment/pull/5154#discussion_r1526819871',\n", " 'https://github.com/augmentcode/augment/pull/5154#discussion_r1526820994',\n", " 'https://github.com/augmentcode/augment/pull/5146#discussion_r1525751351',\n", " 'https://github.com/augmentcode/augment/pull/5146#discussion_r1525752581',\n", " 'https://github.com/augmentcode/augment/pull/5145#discussion_r1525683852',\n", " 'https://github.com/augmentcode/augment/pull/5145#discussion_r1525686046',\n", " 'https://github.com/augmentcode/augment/pull/5143#discussion_r1525611341',\n", " 'https://github.com/augmentcode/augment/pull/5131#discussion_r1529528102',\n", " 'https://github.com/augmentcode/augment/pull/5131#discussion_r1529546829',\n", " 'https://github.com/augmentcode/augment/pull/5130#discussion_r1525585648',\n", " 'https://github.com/augmentcode/augment/pull/5118#discussion_r1525473988',\n", " 'https://github.com/augmentcode/augment/pull/5118#discussion_r1526429586',\n", " 'https://github.com/augmentcode/augment/pull/5118#discussion_r1526435027',\n", " 'https://github.com/augmentcode/augment/pull/5118#discussion_r1526616964',\n", " 'https://github.com/augmentcode/augment/pull/5118#discussion_r1526621022',\n", " 'https://github.com/augmentcode/augment/pull/5117#discussion_r1525614693',\n", " 'https://github.com/augmentcode/augment/pull/5117#discussion_r1525615193',\n", " 'https://github.com/augmentcode/augment/pull/5117#discussion_r1526920520',\n", " 'https://github.com/augmentcode/augment/pull/5117#discussion_r1526936051',\n", " 'https://github.com/augmentcode/augment/pull/5117#discussion_r1528855688',\n", " 'https://github.com/augmentcode/augment/pull/5117#discussion_r1528858293',\n", " 'https://github.com/augmentcode/augment/pull/5117#discussion_r1528873911',\n", " 'https://github.com/augmentcode/augment/pull/5116#discussion_r1525466994',\n", " 'https://github.com/augmentcode/augment/pull/5115#discussion_r1525473983',\n", " 'https://github.com/augmentcode/augment/pull/5111#discussion_r1525351479',\n", " 'https://github.com/augmentcode/augment/pull/5105#discussion_r1525285930',\n", " 'https://github.com/augmentcode/augment/pull/5104#discussion_r1525269150',\n", " 'https://github.com/augmentcode/augment/pull/5100#discussion_r1525136506',\n", " 'https://github.com/augmentcode/augment/pull/5097#discussion_r1525594057',\n", " 'https://github.com/augmentcode/augment/pull/5097#discussion_r1525600296',\n", " 'https://github.com/augmentcode/augment/pull/5095#discussion_r1525484820',\n", " 'https://github.com/augmentcode/augment/pull/5088#discussion_r1525155822',\n", " 'https://github.com/augmentcode/augment/pull/5084#discussion_r1524037078',\n", " 'https://github.com/augmentcode/augment/pull/5084#discussion_r1524053885',\n", " 'https://github.com/augmentcode/augment/pull/5084#discussion_r1524064872',\n", " 'https://github.com/augmentcode/augment/pull/5084#discussion_r1524070485',\n", " 'https://github.com/augmentcode/augment/pull/5084#discussion_r1524075336',\n", " 'https://github.com/augmentcode/augment/pull/5084#discussion_r1524100789',\n", " 'https://github.com/augmentcode/augment/pull/5082#discussion_r1524113848',\n", " 'https://github.com/augmentcode/augment/pull/5080#discussion_r1529101844',\n", " 'https://github.com/augmentcode/augment/pull/5080#discussion_r1529103684',\n", " 'https://github.com/augmentcode/augment/pull/5080#discussion_r1529104845',\n", " 'https://github.com/augmentcode/augment/pull/5080#discussion_r1529105894',\n", " 'https://github.com/augmentcode/augment/pull/5080#discussion_r1529111621',\n", " 'https://github.com/augmentcode/augment/pull/5078#discussion_r1529050447',\n", " 'https://github.com/augmentcode/augment/pull/5078#discussion_r1529054236',\n", " 'https://github.com/augmentcode/augment/pull/5078#discussion_r1529057383',\n", " 'https://github.com/augmentcode/augment/pull/5075#discussion_r1525133659',\n", " 'https://github.com/augmentcode/augment/pull/5075#discussion_r1525137345',\n", " 'https://github.com/augmentcode/augment/pull/5074#discussion_r1525125713',\n", " 'https://github.com/augmentcode/augment/pull/5070#discussion_r1523887861',\n", " 'https://github.com/augmentcode/augment/pull/5069#discussion_r1523861075',\n", " 'https://github.com/augmentcode/augment/pull/5069#discussion_r1523881517',\n", " 'https://github.com/augmentcode/augment/pull/5069#discussion_r1523883194',\n", " 'https://github.com/augmentcode/augment/pull/5069#discussion_r1523883905',\n", " 'https://github.com/augmentcode/augment/pull/5069#discussion_r1523884862',\n", " 'https://github.com/augmentcode/augment/pull/5069#discussion_r1523885388',\n", " 'https://github.com/augmentcode/augment/pull/5062#discussion_r1523538890',\n", " 'https://github.com/augmentcode/augment/pull/5062#discussion_r1523540312',\n", " 'https://github.com/augmentcode/augment/pull/5062#discussion_r1523541303',\n", " 'https://github.com/augmentcode/augment/pull/5061#discussion_r1523567000',\n", " 'https://github.com/augmentcode/augment/pull/5054#discussion_r1522349749',\n", " 'https://github.com/augmentcode/augment/pull/5053#discussion_r1523602311',\n", " 'https://github.com/augmentcode/augment/pull/5053#discussion_r1523662413',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1522337439',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529403320',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529403962',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529405137',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529409603',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529414149',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529415957',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529425816',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529433078',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529448783',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529454322',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529470407',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529471930',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529482966',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529514024',\n", " 'https://github.com/augmentcode/augment/pull/5048#discussion_r1529517181',\n", " 'https://github.com/augmentcode/augment/pull/5044#discussion_r1523807035',\n", " 'https://github.com/augmentcode/augment/pull/5044#discussion_r1524042978',\n", " 'https://github.com/augmentcode/augment/pull/5043#discussion_r1523624457',\n", " 'https://github.com/augmentcode/augment/pull/5040#discussion_r1522181165',\n", " 'https://github.com/augmentcode/augment/pull/5036#discussion_r1522163710',\n", " 'https://github.com/augmentcode/augment/pull/5036#discussion_r1523700713',\n", " 'https://github.com/augmentcode/augment/pull/5024#discussion_r1522135190',\n", " 'https://github.com/augmentcode/augment/pull/5022#discussion_r1522033455',\n", " 'https://github.com/augmentcode/augment/pull/5019#discussion_r1521942116',\n", " 'https://github.com/augmentcode/augment/pull/5019#discussion_r1521942558',\n", " 'https://github.com/augmentcode/augment/pull/5018#discussion_r1522031244',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1520746162',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1520747344',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1520749866',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1521837659',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1521840476',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1521849953',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1521850484',\n", " 'https://github.com/augmentcode/augment/pull/5010#discussion_r1521851076',\n", " 'https://github.com/augmentcode/augment/pull/5007#discussion_r1523629267',\n", " 'https://github.com/augmentcode/augment/pull/5003#discussion_r1523822592',\n", " 'https://github.com/augmentcode/augment/pull/5002#discussion_r1523653335']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
"""Filtering script for synthetic data.

Script sorts input samples into 3 directories:
- good: samples that passed filtering
- bad: samples that didn't pass filtering
- failed: samples that failed to be filtered
"""

import argparse
import json
import re
from dataclasses import asdict
from multiprocessing import Pool
from pathlib import Path

from tqdm import tqdm

from research.core.edit_prompt_input import ResearchEditPromptInput
from research.data.synthetic_code_edit.api_lib import GptWrapper
from research.data.synthetic_code_edit.util_lib import get_unified_diff, render_list


def read_cr_data(path):
    cr_data = []
    with path.open() as f:
        for line in tqdm(f, desc="Reading data"):
            cr_data.append(json.loads(line))
    return cr_data


def parse_all_diff_numbers(diff_hunk: str):
    match = re.search(r"@@ -(\d+),(\d+) \+(\d+),(\d+) @@", diff_hunk)
    if match:
        return (
            int(match.group(1)) - 1,
            int(match.group(2)),
            int(match.group(3)) - 1,
            int(match.group(4)),
        )
    assert False


def get_lines(code: str, start: int, stop: int):
    return "".join(code.splitlines(True)[start:stop])


def hunk_to_code(hunk, ignore_symbol):
    result = []
    for i, line in enumerate(hunk.splitlines(True)):
        if i == 0:  # For hunk header
            continue
        if line.startswith(ignore_symbol):
            continue
        line = line[1:]
        result.append(line)

    return "".join(result)


def find_matching_files(S: str, D: Path) -> list:
    matching_files = []

    if not D.is_dir():
        raise ValueError(f"The path {D} is not a directory.")

    for file_path in tqdm(D.rglob("*"), desc="Searching for matching files"):
        if file_path.is_file():
            try:
                with open(file_path, "r", encoding="utf-8") as file:  # noqa: PL123
                    content = file.read()
                    if content == S:
                        matching_files.append(file_path)
            except Exception:  # pylint: disable=W0718
                pass

    return matching_files


def get_commented_line_index(old_hunk, hunk):
    number_of_added_lines = len(
        list(filter(lambda line: not line.startswith("-"), old_hunk.splitlines()))
    )
    _, _, c, _ = parse_all_diff_numbers(old_hunk)
    a, _, _, _ = parse_all_diff_numbers(hunk)
    return (
        c - a + number_of_added_lines - 2
    )  # -2 to accounts for diff header and the selected line itself


def convert_doc(doc):
    return {
        "hexsha": doc["id"],
        "content": doc["text"],
        "max_stars_repo_path": doc["path"],
    }


def convert_sample(sample):
    return {
        "instruction": sample["instruction"],
        "path": sample["path"],
        "prefix": sample["prefix"],
        "suffix": sample["suffix"],
        "old_middle": sample["selected_code"],
        "new_middle": sample["updated_code"],
        "repo_url": sample["extra"]["repo_url"],
        "full_sample": sample,
    }


def process_cr_sample(cr_sample):
    # Convert cr sample to harness sample
    old_start, old_len, _, _ = parse_all_diff_numbers(cr_sample["hunk"])

    prefix = get_lines(cr_sample["oldf"], 0, old_start)
    suffix = get_lines(cr_sample["oldf"], old_start + old_len, int(1e9))

    selected_code = hunk_to_code(cr_sample["hunk"], ignore_symbol="+")
    updated_code = hunk_to_code(cr_sample["hunk"], ignore_symbol="-")

    commented_line = get_commented_line_index(
        cr_sample["old_hunk"],
        cr_sample["hunk"],
    )
    if commented_line < 0 or commented_line >= len(selected_code.splitlines(True)):
        print("Commented line is outside selected_code.")
        return None

    code_edit_sample = ResearchEditPromptInput(
        path="",
        prefix=prefix,
        selected_code=selected_code,
        suffix=suffix,
        instruction=cr_sample["comment"],
        # prefix_begin=0,
        # suffix_end=len(cr_sample["oldf"]),
        retrieved_chunks=[],
        updated_code=updated_code,
        extra={
            "commented_line": commented_line,
            "pr_url": f"https://github.com/{cr_sample['repo']}/pull/{cr_sample['ghid']}/commits/{cr_sample['ids'][1]}",
        },
    )

    return code_edit_sample


def mark_lines(code: str, line_range: list[int]) -> str:
    result = []
    for i, line in enumerate(code.splitlines(True)):
        if line_range[0] <= i <= line_range[1]:
            result.append(f"|>{line}")
        else:
            result.append(line)
    return "".join(result)


FILTERING_PROMPT = """Here is the original source code:
```
{selected_code}
```

Here is updated code obtained by addressing PR comment ('{instruction}') left on a line marked with |> in the original source code:
```
{updated_code}
```

And here is the diff between original source code and updated code:
```
{diff}
```

Please evaluate whether PR comment and updated code meet these criterias:
{criterias}

If PR comment or the updated code doesn't meet at least one of these criterias, mark this evaluation as failed.

Follow this steps to complete this task:
1. Analyze the original code, PR comment, updated code, diff and criterias.
2. Describe your thinking process on how to complete the evaluation.
3. Explicitly write whether evaluation passed or not.
"""


FILTERING_EXTRACT_PROMPT = """Return results of evaluation as a JSON with 2 keys:
- success
- feedback

If evaluation was successful, then success must be True and feedback empty.
If evaluation failed, then success must be False and feedback should contain why it didn't pass evaluation.
"""

FILTERING_CRITERIAS = [
    "PR comment must suggest (or imply) the modification made to the code",
    "Updated code must contain all modifications suggested by the PR comment",
    "Updated code must NOT contain any modifications not suggested by the PR comment",
]

FilteringArgType = tuple[dict, GptWrapper, Path, str]


def filter_sample(filter_args: FilteringArgType):
    cr_sample, gpt, result_dir, sample_idx = filter_args

    sample = process_cr_sample(cr_sample)
    if sample is None:
        return

    for _dir in ["good", "bad", "failed"]:
        if (result_dir / _dir / f"{sample_idx}.json").exists():
            return

    diff = get_unified_diff(sample.selected_code, sample.updated_code)

    marked_code = mark_lines(
        sample.selected_code,
        [sample.extra["commented_line"], sample.extra["commented_line"]],
    )

    prompt = FILTERING_PROMPT.format(
        selected_code=marked_code,
        updated_code=sample.updated_code,
        instruction=sample.instruction,
        criterias=render_list(FILTERING_CRITERIAS),
        diff=diff,
    )

    try:
        messages = [{"role": "user", "content": prompt}]
        gpt4_response = gpt(messages, model="gpt-4-1106-preview", temperature=0)

        messages.append({"role": "assistant", "content": gpt4_response})
        messages.append({"role": "user", "content": FILTERING_EXTRACT_PROMPT})

        filtering_result = gpt(
            messages, model="gpt-3.5-turbo-1106", use_json=True, temperature=0
        )
    except KeyboardInterrupt:  # pylint: disable=W0706
        raise
    except Exception as e:  # pylint: disable=W0718
        print(f"Sample {sample_idx} failed with exception: {e}")
        filtering_result = dict()

    if set(filtering_result.keys()) != {"success", "feedback"}:
        target_dir = "failed"
    elif filtering_result["success"] is True:
        target_dir = "good"
    elif filtering_result["success"] is False:
        target_dir = "bad"
    else:
        target_dir = "failed"

    # sample.extra["filtering_result"] = filtering_result
    # sample.extra["cr_sample"] = cr_sample

    cr_sample["filtering_result"] = filtering_result
    cr_sample["ce_sample"] = asdict(sample)

    with (result_dir / target_dir / f"{sample_idx}.json").open("w") as f:
        json.dump(cr_sample, f, indent=2)


def main(
    input_json: Path,
    output_dir: Path,
    num_processes: int = 1,
    num_samples: int = int(1e12),
):
    data_samples = read_cr_data(input_json)
    print(f"Loaded {len(data_samples)} samples.")

    output_dir.mkdir(exist_ok=True)
    for dir_name in ["good", "bad", "failed"]:
        (output_dir / dir_name).mkdir(exist_ok=True)

    gpt = GptWrapper()
    tasks = []
    for i, sample in enumerate(data_samples[:num_samples]):
        tasks.append((sample, gpt, output_dir, i))

    if num_processes <= 1:
        for task in tqdm(tasks):
            filter_sample(task)
    else:
        with Pool(num_processes) as pool:
            _ = list(tqdm(pool.imap(filter_sample, tasks), total=len(tasks)))

    print(f"GPT usage:\n{gpt.get_stats()}")


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for automatically filtering synthetic data.",
    )
    parser.add_argument(
        "--input_json",
        "-i",
        type=Path,
        required=True,
        help="Path to input json with CR data.",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory to store results",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        default=int(1e12),
        help="Number of samples to filter",
    )
    args = parser.parse_args()

    return args


if __name__ == "__main__":
    cli_args = parse_args()
    main(
        cli_args.input_json,
        cli_args.output_dir,
        cli_args.num_processes,
        cli_args.num_samples,
    )

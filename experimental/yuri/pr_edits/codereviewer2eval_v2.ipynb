{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "import subprocess\n", "import zipfile\n", "import os\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from research.retrieval.utils import convert_repository_to_documents\n", "from dataclasses import asdict\n", "from experimental.yuri.pr_edits.codereviewer2train_v2 import modify_boundaries\n", "\n", "\n", "CODEREVIEWER_DATA_PATH = Path(\"/home/<USER>/data/code_reviewer/Code_Refinement/ref-valid.jsonl\")\n", "OUT_DIR = Path(\"/home/<USER>/tmp/pr_edits_eval_145_10aligned\")\n", "IS_FILTER_BY_EVAL_SAMPLES = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_SAMPLES = [\n", "\"AMReX-Codes__amrex___87fec33e0b75bd3b0c71f72b3e94e4ff06eb2741___13183\",\n", "\"AgileVentures__WebsiteOne___c237c4f323aa53b94be3caa7f23d3112f187f41b___21187\",\n", "\"AntennaPod__AntennaPod___01bfd6c4f66f4517201fe3a81cad97fabdeea837___18515\",\n", "\"AntennaPod__AntennaPod___62727d071cbb2c0f342607057090dc061dc3b165___19760\",\n", "\"AntennaPod__AntennaPod___e8be5cb8ec18d49c66abd6898a6a55d9f246c7e4___15196\",\n", "\"Azure__WALinuxAgent___5e90929ae911ba937f96e60ff00b90936db41afa___10612\",\n", "\"Azure__WALinuxAgent___f2629f5c8265a7496ba7fb704385e14867647d31___16545\",\n", "\"Azure__azure-xplat-cli___589c20467d7436cb4a2dc0961b1a1040f3c748fc___25441\",\n", "\"Blazemeter__taurus___8dfb7abe1f74c4e248f4844b1922a2874ac1b33b___14488\",\n", "\"Bytom__bytom___0055f1977d40b36d0126a9f125a55f7d245829cb___16921\",\n", "\"CachetHQ__Cachet___36b2b9d7c9fdd4f457b8586f294a012b1101b45d___17016\",\n", "\"Cockatrice__Cockatrice___41478800efbc4d74ecb3024e52dba1c9d0cacb61___9231\",\n", "\"Cockatrice__Cockatrice___519ed150bc73bf74490e082cf653be7bd1b97566___13072\",\n", "\"Cockatrice__Cockatrice___8ca0b96d1093c20750875cb21228853667759acb___9249\",\n", "\"EQEmu__Server___a85a674467c7ee41d952057ccb289983a426b0d1___10336\",\n", "\"FISCO-BCOS__FISCO-BCOS___90bf1841bfee53361d433fae3d07d3ab8d8d6c30___18966\",\n", "\"HerculesWS__Hercules___42ed96ae54a02a708f971c59380d3914cfe9f568___12087\",\n", "\"HubSpot__Singularity___1bc471d4741b1c7b2345132ae23a1ea60ad722e0___17786\",\n", "\"HubSpot__Singularity___7c5669c5e709c15c4db40d08b6ef63a9f56127c1___16595\",\n", "\"HubSpot__Singularity___7c7e4850ff4f048cc998a6060a0a0a8a71715dfa___17264\",\n", "\"HubSpot__Singularity___8c71d42009c93270be0c65b0daf465db460b74f6___18742\",\n", "\"HubSpot__Singularity___ac94d3c98c2e7bf12636ec36530fb0b0593217e4___18557\",\n", "\"HubSpot__Singularity___ee6abc96f95f50afbaefec7401e7410cbbe933a1___15494\",\n", "\"HypothesisWorks__hypothesis___4db06ef16613e73e864520b378c88ac91a071e4a___12090\",\n", "\"HypothesisWorks__hypothesis___5c95616135159f00273624f8c15f2bd6a378a48b___19377\",\n", "\"<PERSON><PERSON><PERSON><PERSON><PERSON>__<PERSON><PERSON><PERSON><PERSON><PERSON>___4b2bc69061a0b197182aa9a64283d9f20b61ae98___20072\",\n", "\"MDAnalysis__mdanalysis___387b8870b4c249955b2fe31096fae695d3a919e2___28578\",\n", "\"MDAnalysis__mdanalysis___44bf4bd2743ecc4f94a700ac59b114b2dbdfdda8___29232\",\n", "\"NLog__NLog___374cef13f8180c7c318363fa3b9217931e914d61___11220\",\n", "\"NLog__NLog___aaf66b8f64158e1ef2f6114f398db891a595efc3___18124\",\n", "\"NREL__OpenStudio___1ab869a3d30ae64f82b05dffcd3b3938a775c323___27680\",\n", "\"NVIDIA__DALI___ad0006f1230db7968e854a3b5b7385dea71cf62f___47432\",\n", "\"OpenEMS__openems___038e0517c689e3376b0a2226265b2d86922f8527___10406\",\n", "\"PegaSysEng__pantheon___21f567a43c43965601068c9bdb672de7ee8ebe2d___19238\",\n", "\"PegaSysEng__pantheon___84ed98587228ccfc3cfa8d3cc0688683a7ab1977___19244\",\n", "\"PokemonGoF__PokemonGo-Bot___5e3eeb59ea2636a539cb91246133379c6baa41ed___26036\",\n", "\"PokemonGoF__PokemonGo-<PERSON>t___abc8949ce0f2d79aa51bfead6b2b16e8bf24ac6b___22015\",\n", "\"SeleniumHQ__selenium___9c741572becc0aade48b0ad75e593bf5dc89cb3a___11857\",\n", "\"TheAlgorithms__C-Plus-Plus___0820bf240673c2cdfc0b62e918058a5f76e68489___14085\",\n", "\"WordPress__wordpress-develop___495f8a76806d59ac43c098574cc739c45538f43b___19777\",\n", "\"Workiva__frugal___97191b3f2da35c3edc4043b413a79bbb2f1265c2___15371\",\n", "\"algorand__go-algorand___25f25f75fa37c186cfc2815c1b39a3e708712e1c___37477\",\n", "\"algorand__go-algorand___2b3cf2f8fb86ebe17f0b0114d86ba4c3792fe63b___36991\",\n", "\"alibaba-fusion__next___9960b1cfdf71b29b2db56aad18bf5c923b54373d___11208\",\n", "\"ansible-community__molecule___1a0e089e4bfbb8f44ef158d5f5a2170063eb3170___5720\",\n", "\"apache__accumulo___393f4f57eecea9828f8d29eb41870c274a227fd9___21021\",\n", "\"apache__accumulo___cc18064d652200b14f3e7dc30303231caeabe3bb___16607\",\n", "\"apache__accumulo___f290db3a4d2c37e20100ce7dbdf974366315ba46___12515\",\n", "\"apache__calcite___d9902b2040370ce1ec206af93586872677e06505___21990\",\n", "\"apache__camel-k___78e62f4742bc1347218496a3be09348c979b7608___8500\",\n", "\"apache__iceberg___6e1af4eacfa6e7e620cf4af9227c78b8f4a84f5f___15951\",\n", "\"apache__iceberg___cf94fbf615bd16abbf8e5c78eec807d954015c26___16060\",\n", "\"apostrophecms__apostrophe___8a650d43bb22272c3d90cad1e0165394a116cdcf___13310\",\n", "\"apostrophecms__apostrophe___8dac8fc3f2a995dc17dbe875a29cde7cc1ca6198___15240\",\n", "\"assimp__assimp___4b15dfd9af414ccf7a0a63a6111417ab205a182f___11767\",\n", "\"authelia__authelia___f68cc535aeab475c384c30f368d34b41d56f222e___9035\",\n", "\"aws-amplify__aws-sdk-android___bc62c13393fa60084449833d264ab08075cfc91c___7814\",\n", "\"aws__aws-sdk-java-v2___2388e64a0b90efaa74ed7282057702c9940e4794___19865\",\n", "\"aws__copilot-cli___17c382df47938520994aa4b7cff3ce055869865e___14248\",\n", "\"aws__copilot-cli___5a1f085d1b0dc66dba32eed0227ec57912a16b74___15144\",\n", "\"aws__s2n-tls___669cc4ddf0426551886a4c477f414f6bb4425616___15436\",\n", "\"aws__s2n-tls___949f908311d367c6fc0a3f22f857fc76bf872769___22797\",\n", "\"aws__s2n-tls___c515fb6874bb1feb2c6181d536a06a14a3dc1e51___16310\",\n", "\"awsdocs__aws-doc-sdk-examples___8e882de9043b26bdc3c6807a605ddb76f36691c5___16774\",\n", "\"beego__beego___6e57b2d8d666681a1d161d8899e3cc11d9bc3328___11055\",\n", "\"bigchaindb__bigchaindb___39e260f88930b2f95a07f57f7e35314167b12ba9___9130\",\n", "\"bigchaindb__bigchaindb___6f5eef97a7e000502434522a9401b8b39b4c583b___14139\",\n", "\"bigchaindb__bigchaindb___d5a0c87e4983de33512a9b1880fc0ff347336a23___16412\",\n", "\"biopython__biopython___49ef5017d5768760445d7a1bf6157e612bb0f660___18726\",\n", "\"biopython__biopython___49ef5017d5768760445d7a1bf6157e612bb0f660___18732\",\n", "\"biopython__biopython___60a961d69fbc4008bcdbdfb3e984f79343523259___19548\",\n", "\"biopython__biopython___7524f215dbe7bd690ab8930653214cc0d511469f___20301\",\n", "\"biopython__biopython___8e08f399f4435f3e6673345a098f339a56f9c960___22858\",\n", "\"biopython__biopython___c084193432e758ead297cbdea73f2978fc31f2ae___20668\",\n", "\"btcpayserver__btcpayserver___a87c2a3374fec8f9c97d17f3472e7d03f76242f9___8860\",\n", "\"caddyserver__caddy___19a85d08c61726f987db6682c42e795ee3716847___8025\",\n", "\"caddyserver__caddy___918c230273b05231d78dbb3927568646f84a2c43___10859\",\n", "\"celery__celery___d61caa63082f651d166c5c179fdc5eece74a4f0a___15953\",\n", "\"chef__supermarket___07e81027645229483a390364582acfdf2159b087___9011\",\n", "\"commons-app__apps-android-commons___8d8002fcf703766f7c9c45ddab64a9e817c2faab___16233\",\n", "\"contao__contao___2aba23ff7ddd8bcf63befec063950586aa38b5d1___16985\",\n", "\"convox__rack___0d77b0e831d61d399195237e4919361b5ae82503___14278\",\n", "\"convox__rack___aad2602772241ea92322646516631dd6c73d27ca___15866\",\n", "\"dmlc__dgl___509074647ec15f998bf07d4136a883e2e5970069___39175\",\n", "\"dmlc__dgl___569d31942014d161fc2af033be55aea500ee3319___38192\",\n", "\"dmlc__dgl___8090bd6ea219c5eb514545e86a2a277851bdd69c___42373\",\n", "\"dmlc__dgl___ee1a391754904b1ce59dbb631860ab9880bf7d23___38122\",\n", "\"dotnet__corefxlab___f2da3baee55e108680874391a41b34df213f8502___14580\",\n", "\"embarklabs__embark___047286b46d9d416fd78d599af4a80786489375c9___9580\",\n", "\"fastai__fastai___4c0b670450da141ce17a0be9523ca916788afa59___10251\",\n", "\"fastai__fastai___5faa1d09a2c23a525ac9f9787f317ff7baf8de5f___7940\",\n", "\"forseti-security__forseti-security___4a94ae49e6460cdc5d683c4ebb405ece65aa6d94___25673\",\n", "\"forseti-security__forseti-security___9d4c4892e4904d8fdcfb0b7cb5c9a44036fa6b59___30106\",\n", "\"forseti-security__forseti-security___b06454b44e1b98a01a9c0897b4aa3fab278c25fc___26516\",\n", "\"forseti-security__forseti-security___c2390661eced7fbc64dd41b8eb196f767bedd02f___25345\",\n", "\"fossasia__susi.ai___a54d02c89d6f1998e8ddae9421ae131e5dc7947a___6756\",\n", "\"google__clusterfuzz___30ae347aa950391deeb155d931e31b5b552d3179___16134\",\n", "\"google__clusterfuzz___af8d3297d707f87f05ba3912967694516e517262___14076\",\n", "\"google__clusterfuzz___c2a0e7147b5b27cf3a7a60cea49c121b1eae624e___9721\",\n", "\"google__go-cloud___1c23c80e8b51652695abdbf4ce5a802ec96b8643___10872\",\n", "\"google__go-cloud___896c43fe1cee533417b9ac6f428b049c1019b937___16640\",\n", "\"google__go-cloud___a30ad19835c4bc1c9e3e95d6fa4f135026a57d91___17594\",\n", "\"googleapis__google-cloud-go___1ae2a99e9e2cea536ca1dd8ec3a2985ca8862288___16506\",\n", "\"googleapis__google-cloud-go___1edfd6fb265d1c5ab6e4ed5b155abdcf707874f1___10969\",\n", "\"googleapis__google-cloud-go___51ec8fbca279cc856bae0e581ae259ee69814c12___15086\",\n", "\"googleapis__google-cloud-go___7191acffedf9c0a27523b77c32d9c9759117c927___16019\",\n", "\"goreleaser__goreleaser___70e042e973563e662efee305fb9ea72d0660bf25___6630\",\n", "\"graphite-project__graphite-web___22a971f30fb4d5b32a359f7aa5e1b2eaec586e14___12541\",\n", "\"graphite-project__graphite-web___bd55fb61f2dec8672013f7cdb384b8bbf30f2622___13034\",\n", "\"h2database__h2database___bb06b10c0bc04fe3345c7a7060941ea2c84a17c3___8855\",\n", "\"h2database__h2database___ee65a1bb70fed29522d88080f377158686ed8625___9507\",\n", "\"hashicorp__boundary___f3a4ff707a3a6143a152275e60fb782fff8edebf___15458\",\n", "\"hashicorp__boundary___f6bef95fa219304db4c24ec0dd7efae2f57d2b45___15424\",\n", "\"hazelcast__hazelcast-jet___33a105d239fb4ac84705f8e2d0941bf92aa309ec___18424\",\n", "\"holoviz__holoviews___4aa2684a38470f77a0e9cbdd62698aab61fa751e___21348\",\n", "\"holoviz__holoviews___fed4877723f94bccb9db8a1383e2ef2280951454___15456\",\n", "\"hyperledger-archives__iroha___2f0e563e8c1989706634711f03324effad9d10c4___31782\",\n", "\"hyperledger-archives__iroha___507902bbeb26b7b57823a7b6748e27ed32ddeb57___34011\",\n", "\"hyperledger-archives__iroha___ceb39de15111c61f6936f63d75eedb59d2bbd6cd___34190\",\n", "\"ihhub__fheroes2___566643a875347b16266cfec295fddb09ab5c3256___17371\",\n", "\"ihhub__fheroes2___f44e6b8b93e92130f8d8d68d7b706d0a8f76e8e7___16399\",\n", "\"jetstack__cert-manager___455f6ebab43027ccad757cf7f3e6bfba4e21b709___17068\",\n", "\"jupyter__notebook___33bd6d4d1e7da0baca3bfc46a93d40d66864c416___11003\",\n", "\"jupyter__notebook___b56e3b16df1a70adb77d58f96250e3ca08faa9b2___15517\",\n", "\"k9mail__k-9___7d0c49106418ab680b7779cf1c2e9d9025a027ed___18219\",\n", "\"kframework__k___e56e6417b1f7d05cd3b433eb2eb67ed892d4f384___15048\",\n", "\"kiali__kiali___b8d7e66197a8ad24dcdee51e3cbcc507740bbce6___14972\",\n", "\"kubeedge__kubeedge___9a0e625be6fecd2ac3877adac1527f49548a1dae___12834\",\n", "\"lammps__lammps___2b5af8894ef0fecd9d550da94845595e6735f9f2___30490\",\n", "\"localstack__localstack___791386d4cf6c7871990f4a008a84b4658e890d36___12117\",\n", "\"log2timeline__plaso___ff8f63da5e4e7320474c217ffb2d6bfff2d3376f___11912\",\n", "\"microsoft__LightGBM___e3ac1e6d1888db7cb6af7fade7028409d9f3ff03___17993\",\n", "\"microsoft__fhir-server___b295bc82e7b9308ecf3d8948ce5ca7dc10392abb___12305\",\n", "\"mongodb__mongoid___951c52d5425ef7a9240e7139945ae008bcdd518b___12801\",\n", "\"mozilla__pontoon___f43447c73bb110689b4d90adaa756a7ed952e0a3___15483\",\n", "\"netblue30__firejail___089919539b656a0a802f8cd2098f1ce33f3911fb___9910\",\n", "\"networkx__networkx___16f65db852543448fd7a68ff25c2218369ea3246___13251\",\n", "\"networkx__networkx___f5bc8a34cbb3156de36120cea9537e41df440927___14800\",\n", "\"oam-dev__kubevela___6172739792f75e77dd82e79bd4ba0c66df3dacd9___11830\",\n", "\"onflow__flow-go___76bfa82d7911307b745c71dc536e53675dc92a2a___41178\",\n", "\"onflow__flow-go___9e551ac95543808f686d5a1b0d43a4adb4d5a1d9___39694\",\n", "\"onflow__flow-go___c2ae06eb69d4b2790e4753c85706d6ceed23b42d___35992\",\n", "\"open-mmlab__mmdetection___2efbfbb53424168376e5ea146a86df5f3a4ddc70___17480\",\n", "\"open-mmlab__mmdetection___338de940234d3ae2c95739d94cdfd07c64b2a3b2___20725\",\n", "\"open-mmlab__mmdetection___44b22e98e8d3223e3e9d2ed4a22269291e68bd0c___25631\",\n", "]\n", "\n", "len(EVAL_SAMPLES)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def parse_all_diff_numbers(diff_hunk: str):\n", "    match = re.search(r'@@ -(\\d+),(\\d+) \\+(\\d+),(\\d+) @@', diff_hunk)\n", "    if match:\n", "        return (int(match.group(1)) - 1, int(match.group(2)),\n", "                int(match.group(3)) - 1, int(match.group(4)))\n", "    assert False\n", "\n", "\n", "def get_lines(code: str, start: int, stop: int):\n", "    return \"\".join(code.splitlines(True)[start:stop])\n", "\n", "\n", "def hunk_to_code(hunk, ignore_symbol):\n", "    result = []\n", "    for i, line in enumerate(hunk.splitlines(True)):\n", "        if i == 0: # For hunk header\n", "            continue\n", "        if line.startswith(ignore_symbol):\n", "            continue\n", "        line = line[1:]\n", "        result.append(line)\n", "\n", "    return \"\".join(result)\n", "\n", "\n", "REPOS_CACHE = Path(\"/mnt/efs/augment/user/yuri/data/code_reviewer_feb_22_repos_cache\")\n", "def checkout_repo(repo, commit_hash):\n", "    url = f\"https://github.com/{repo}/archive/{commit_hash}.zip\"\n", "    repo_id = f\"{repo.replace('/', '__')}___{commit_hash}\"\n", "\n", "    download_dir = REPOS_CACHE / repo_id\n", "    if not download_dir.exists():\n", "        download_dir.mkdir(exist_ok=True)\n", "        zip_path = download_dir / \"downloaded.zip\"\n", "        subprocess.run([\"curl\", \"-L\", url, \"-o\", zip_path], check=True)\n", "        with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "            zip_ref.extractall(download_dir)\n", "        os.remove(zip_path)\n", "\n", "    repo_dir = [*download_dir.iterdir()]\n", "    assert len(repo_dir) == 1\n", "    repo_dir = repo_dir[0]\n", "\n", "    return repo_dir, url\n", "\n", "\n", "def find_matching_files(S: str, D: Path) -> list:\n", "    matching_files = []\n", "    \n", "    if not D.is_dir():\n", "        raise ValueError(f\"The path {D} is not a directory.\")\n", "    \n", "    for file_path in tqdm(D.rglob('*'), desc=\"Searching for matching files\"):\n", "        if file_path.is_file():\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as file:\n", "                    content = file.read()\n", "                    if content == S:\n", "                        matching_files.append(file_path)\n", "            except Exception as e:\n", "                pass\n", "    \n", "    return matching_files\n", "    \n", "def get_commented_line_index(old_hunk, hunk):\n", "    number_of_added_lines = len(list(filter(\n", "        lambda l: not l.startswith(\"-\"),\n", "        old_hunk.splitlines())\n", "    ))\n", "    _, _, c, _ = parse_all_diff_numbers(old_hunk)\n", "    a, _, _, _ = parse_all_diff_numbers(hunk)\n", "    return c - a + number_of_added_lines - 2  # -2 to accounts for diff header and the selected line itself"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cr_data = []\n", "with CODEREVIEWER_DATA_PATH.open() as f:\n", "    for line in tqdm(f):\n", "        cr_data.append(json.loads(line))\n", "\n", "cr_data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in tqdm(range(len(cr_data)), \"Main processing\"):\n", "    cr_sample = cr_data[i]\n", "\n", "    #### Optional filtering\n", "    repo_name = cr_sample['repo']\n", "    commit_hash = cr_sample['ids'][1]\n", "    repo_id = f\"{repo_name.replace('/', '__')}___{commit_hash}\"\n", "    sample_id = f\"{repo_id}___{cr_sample['ids'][0]}\"  # Not sure what ['ids'][0] denotes, but it is unique across dataset\n", "\n", "    if IS_FILTER_BY_EVAL_SAMPLES and sample_id not in EVAL_SAMPLES:\n", "        continue\n", "\n", "    #### Convert cr sample to harness sample\n", "    old_start, old_len, new_start, new_len = parse_all_diff_numbers(cr_sample['hunk'])\n", "\n", "    prefix = get_lines(cr_sample['oldf'], 0, old_start)\n", "    suffix = get_lines(cr_sample['oldf'], old_start+old_len, int(1e9))\n", "\n", "    selected_code = hunk_to_code(cr_sample['hunk'], ignore_symbol=\"+\")\n", "    updated_code = hunk_to_code(cr_sample['hunk'], ignore_symbol=\"-\")\n", "\n", "    repo_dir, repo_url = checkout_repo(repo_name, commit_hash)\n", "\n", "    possible_file_paths = find_matching_files(cr_sample['oldf'], repo_dir)\n", "    if len(possible_file_paths) != 1:\n", "        print(f\"len(possible_file_paths) != 1 {i}\")\n", "        continue\n", "    file_path = possible_file_paths[0].relative_to(repo_dir)\n", "\n", "    docs = convert_repository_to_documents(str(repo_dir))\n", "\n", "    commented_line = get_commented_line_index(\n", "        cr_sample['old_hunk'],\n", "        cr_sample['hunk'],\n", "    )\n", "    if commented_line < 0 or commented_line >= len(selected_code.splitlines(True)):\n", "        print(f\"Commented line is outside selected_code.\")\n", "        continue\n", "\n", "    try:\n", "        prefix, suffix, selected_code, updated_code, commented_line = modify_boundaries(\n", "            prefix, suffix, selected_code, updated_code, commented_line\n", "        )\n", "    except AssertionError as e:\n", "        print(\"#\" * 20 + f\"Assertion error while modifying boundaries: {e}\")\n", "        continue\n", "\n", "    code_edit_sample = ResearchEditPromptInput(\n", "        path=str(file_path),\n", "        prefix=prefix,\n", "        selected_code=selected_code,\n", "        suffix=suffix,\n", "        instruction=cr_sample['comment'],\n", "        # prefix_begin=0,\n", "        # suffix_end=len(cr_sample[\"oldf\"]),\n", "        retrieved_chunks=[],\n", "        updated_code=updated_code,\n", "        extra={\n", "            \"repo_url\": repo_url,\n", "            \"commented_line\": commented_line,\n", "            \"pr_url\": f\"https://github.com/{cr_sample['repo']}/pull/{cr_sample['ghid']}/commits/{cr_sample['ids'][1]}\"\n", "        },\n", "    )\n", "    docs_as_dict = {\n", "        \"repo_url\": repo_url,\n", "        \"docs\": [asdict(doc) for doc in docs]\n", "    }\n", "\n", "    #### Save it\n", "    examples_dir = OUT_DIR / \"examples\"\n", "    repos_dir = OUT_DIR / \"repos\"\n", "\n", "    examples_dir.mkdir(exist_ok=True)\n", "    repos_dir.mkdir(exist_ok=True)\n", "\n", "    repo_path = repos_dir / f\"{repo_id}.json\"\n", "    sample_path = examples_dir / f\"{sample_id}.json\"\n", "\n", "    assert not sample_path.exists()\n", "    with sample_path.open(\"w\") as f:\n", "        json.dump(asdict(code_edit_sample), f, indent=2)\n", "\n", "    if repo_path.exists():\n", "        print(f\"Reusing {repo_id}\")\n", "    else:\n", "        with repo_path.open(\"w\") as f:\n", "            json.dump(docs_as_dict, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cr_sample['oldf']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_dir"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hash_to_ghid = dict()\n", "\n", "for _c in cr_data:\n", "    if _c['ids'][1] not in hash_to_ghid:\n", "        hash_to_ghid[_c['ids'][1]] = set([_c['ids'][0]])\n", "    else:\n", "        assert _c['ids'][0] not in hash_to_ghid[_c['ids'][1]]\n", "        hash_to_ghid[_c['ids'][1]].add(_c['ids'][0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hash_to_ghid[_c['ids'][1]]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_c['ghid']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
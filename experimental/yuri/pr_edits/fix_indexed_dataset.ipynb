{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import random\n", "import torch\n", "import numpy as np\n", "\n", "from pathlib import Path\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "\n", "# To initialise formatters registry\n", "from research.eval.harness.factories import create_retriever\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RESULT_DIR = Path(\"/mnt/efs/augment/user/yuri/data/mar_7_75k_v4\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = MMapIndexedDataset(str(RESULT_DIR / \"train\")) + MMapIndexedDataset(str(RESULT_DIR / \"valid\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(dataset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_substring(S, A, B):\n", "    # Find the first occurrence of A in S\n", "    start_index = S.find(A)\n", "    if start_index == -1:\n", "        # A is not found\n", "        assert False\n", "        return \"\"\n", "    \n", "    # Adjust start_index to the end of A to find the substring after A\n", "    start_index += len(A)\n", "    \n", "    # Find the first occurrence of B in S after A\n", "    end_index = S.find(B, start_index)\n", "    if end_index == -1:\n", "        # B is not found after A\n", "        assert False\n", "        return \"\"\n", "    \n", "    # Extract the substring between A and B\n", "    return S[start_index:end_index]\n", "\n", "    \n", "def split_sample(sample_s: str):\n", "    instruction = find_substring(sample_s, \"Instruction: Fix PR comment: \", \"\\nPrefix:\\n```\")\n", "    try:\n", "        instruction2 = find_substring(sample_s, \"Instruction: Fix PR comment: \", \"\\nSee (\")\n", "    except AssertionError:\n", "        instruction2 = instruction\n", "    if len(instruction2) < len(instruction):\n", "        instruction = instruction2\n", "    selected_code = find_substring(sample_s, \"Selected Code:\\n```\\n\", \"```\\n\\nUpdated Code:\\n```\")\n", "    updated_code = find_substring(sample_s, \"Updated Code:\\n```\\n\", \"\\n```\\n<|EOT|><|EOT|>\")\n", "    return {\"instruction\": instruction, \"selected_code\": selected_code, \"updated_code\": updated_code}\n", "                \n", "def split_raw(sample, tokenizer):\n", "    sample = list(map(abs, sample))\n", "    sample_s = tokenizer.detokenize(sample)\n", "    return split_sample(sample_s)\n", "\n", "\n", "def save_dataset(train_dataset, eval_dataset, output_path):\n", "    for f_name, dataset in [(\"train\", train_dataset), (\"valid\", eval_dataset)]:\n", "        cur_output_path = output_path / f_name\n", "        builder = MMapIndexedDatasetBuilder(\n", "            cur_output_path.with_suffix(\".bin\"), dtype=np.int32\n", "        )\n", "        for sample in tqdm(dataset):\n", "            if len(sample) == 1:\n", "                continue\n", "            builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "            builder.end_document()\n", "        builder.finalize(cur_output_path.with_suffix(\".idx\"))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TOKEN_APPORTIONMENT = {\n", "    \"path_len\": 256,\n", "    \"instruction_len\": 512,\n", "    \"prefix_len\": 1536,\n", "    \"selected_code_len\": 4096,\n", "    \"suffix_len\": 1024,\n", "    \"max_prompt_tokens\": 16384 - 4096,  # 4096 represents the max output tokens\n", "}\n", "\n", "prompt_formatter = get_prompt_formatter(\"droid\", **TOKEN_APPORTIONMENT)\n", "tokenizer = prompt_formatter.tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = list(map(abs, dataset[2]))\n", "print(tokenizer.detok<PERSON>ze(sample))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["split_sample(tokenizer.detokenize(sample))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "\n", "instr2samples = defaultdict(list)\n", "for sample in tqdm(dataset):\n", "    sample_s = tokenizer.detokenize(list(map(abs, sample)))\n", "    instr2samples[split_sample(sample_s)[\"instruction\"]].append(sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_samples = []\n", "first_stage_bad_samples = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for f_path in tqdm(Path(\"/mnt/efs/augment/user/yuri/data/cr_75k_filter_mar17_v1/good\").glob(\"*.json\")):\n", "    with f_path.open() as f:\n", "        sample = json.load(f)\n", "    \n", "    possible_samples = instr2samples[sample[\"comment\"]]\n", "    \n", "    if len(possible_samples) == 0:\n", "        first_stage_bad_samples.append(sample)\n", "        continue\n", "\n", "    possible_samples = [*filter(\n", "        lambda s: split_raw(s, tokenizer)[\"updated_code\"] == sample[\"ce_sample\"][\"updated_code\"], possible_samples)]\n", "    # possible_samples = [*filter(\n", "    #     lambda s: split_raw(s, tokenizer)[\"selected_code\"] in sample[\"ce_sample\"][\"selected_code\"], possible_samples)]\n", "    if len(possible_samples) == 0:\n", "        print(\"Skipping second stage.\")\n", "        continue\n", "    assert len(possible_samples) == 1\n", "\n", "\n", "    found_sample = possible_samples[0]\n", "    good_samples.append(found_sample)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(good_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(first_stage_bad_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["first_stage_bad_samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# len(list(filter(lambda k: \"I think it's easier to read this way: \" in k, instr2samples.keys())))\n", "# list(filter(lambda k: \"I think we still want this check so that\" in k, instr2samples.keys()))\n", "\n", "# instr2samples[\"I think we still want this check so that disabled projects don't run old tasks wouldn't we?\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["good_samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["random.shuffle(good_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_o =  Path(\"/mnt/efs/augment/user/yuri/data/cr_75k_filter_mar17_v1_first40k\")\n", "_o.mkdir(exist_ok=False, parents=False)\n", "save_dataset(good_samples[:-750], good_samples[-750:], _o)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import numpy as np\n", "import torch\n", "\n", "from pathlib import Path\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_all_samples(path: Path):\n", "    dataset = MMapIndexedDataset(str(path))\n", "    all_samples = []\n", "    for i in tqdm(range(len(dataset))):\n", "        all_samples.append(dataset[i].copy())\n", "    return all_samples\n", "\n", "def save_dataset(samples, output_path):\n", "    random.shuffle(samples)\n", "    if not output_path.parent.exists():\n", "        output_path.parent.mkdir()\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sample in tqdm(samples):\n", "        builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["new_training_data_path = Path(\"/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8/train\")\n", "new_validation_data_path = Path(\"/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8/valid\")\n", "\n", "new_training_samples = get_all_samples(new_training_data_path)\n", "new_validation_samples = get_all_samples(new_validation_data_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TRAIN_SPLIT = 4000\n", "VALID_SPLIT = 400"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s1_training_samples = get_all_samples(Path(\"/home/<USER>/tmp/droid-repo-47/train\"))\n", "s1_validation_samples = get_all_samples(Path(\"/home/<USER>/tmp/droid-repo-47/validation\"))\n", "\n", "s2_training_samples = get_all_samples(Path(\"/home/<USER>/tmp/droid-repo-48/train\"))\n", "s2_validation_samples = get_all_samples(Path(\"/home/<USER>/tmp/droid-repo-48/validation\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output_path1 = Path(\"/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8_joined_stage1\")\n", "output_path2 = Path(\"/home/<USER>/tmp/test_feb_27_cr_stage3_10k_v8_joined_stage2\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_dataset(s1_training_samples + new_training_samples[:TRAIN_SPLIT], output_path1 / \"train\")\n", "save_dataset(s1_validation_samples + new_validation_samples[:VALID_SPLIT], output_path1 / \"valid\")\n", "\n", "save_dataset(s2_training_samples + new_training_samples[TRAIN_SPLIT:], output_path2 / \"train\")\n", "save_dataset(s2_validation_samples + new_validation_samples[VALID_SPLIT:], output_path2 / \"valid\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "\n", "from pathlib import Path\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from research.core import utils_for_dataclass, utils_for_file\n", "\n", "DATA_PATH = Path(\"/tmp/f5/bad\")\n", "REQUEST_INSIGHT_URL = \"https://support.dev-yuri.t.us-central1.dev.augmentcode.com/request\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_diff_html(input_code, output_code, comment, another_header=None):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><strong>{comment}</strong></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Comment: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    if another_header is not None:\n", "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n", "    return html\n", "\n", "def mark_lines(code, line_range):\n", "    result = []\n", "    for i, line in enumerate(code.splitlines(True)):\n", "        if line_range[0] <= i < line_range[1]:\n", "            result.append(f\"|>{line}\")\n", "        else:\n", "            result.append(f\"  {line}\")\n", "    return \"\".join(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize filtering result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_files = sorted(list(DATA_PATH.glob(\"*.json\")))\n", "len(all_files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = {}\n", "\n", "for ex_file in all_files:\n", "    ex = utils_for_file.read_json(ex_file)\n", "\n", "    ex[\"selected_code\"] = ex[\"old_middle\"]\n", "    ex[\"updated_code\"] = ex[\"new_middle\"]\n", "    ex[\"prefix_begin\"] = 0\n", "    ex[\"suffix_end\"] = len(ex[\"prefix\"] + ex[\"old_middle\"] + ex[\"suffix\"])\n", "    ex[\"retrieved_chunks\"] = []\n", "\n", "    sample = utils_for_dataclass.create_from_dict(ResearchEditPromptInput, ex)\n", "    sample.extra[\"feedback\"] = ex[\"filtering_result\"][\"feedback\"]\n", "\n", "    assert ex_file.stem not in samples\n", "    samples[ex_file.stem] = sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "for sample_id, sample in samples.items():\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {sample_id}</h2><hr>\"\n", "\n", "    cur_diff = get_diff_html(\n", "        sample.selected_code,\n", "        sample.updated_code,\n", "        sample.instruction,\n", "        f'Feedback: {sample.extra[\"feedback\"]}',\n", "    )\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_mar_3_filter_v3.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize the code edit samples."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_files = sorted(list(DATA_PATH.glob(\"*.json\")))\n", "len(all_files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = {}\n", "\n", "for ex_file in all_files:\n", "    ex = utils_for_file.read_json(ex_file)\n", "    sample = utils_for_dataclass.create_from_dict(ResearchEditPromptInput, ex)\n", "    assert ex_file.stem not in samples\n", "    samples[ex_file.stem] = sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "for sample_id, sample in samples.items():\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {sample_id}</h2><hr>\"\n", "    MAIN_HTML += f\"<a href=\\\"{sample.extra['pr_url']}\\\">PR link</a>\"\n", "    MAIN_HTML += f'<input type=\"checkbox\" id=\"{sample_id}\" name=\"{sample_id}\">'\n", "\n", "    commented_line_range = [\n", "        sample.extra[\"commented_line\"],\n", "        sample.extra[\"commented_line\"] + 1\n", "    ]\n", "    cur_diff = get_diff_html(\n", "        mark_lines(sample.selected_code, commented_line_range),\n", "        mark_lines(sample.updated_code, [-10, -10]),\n", "        sample.instruction\n", "    )\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_mar_3_filter_v1.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Filtered samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["FILTERED_SAMPLES = [\n", "\"PegaSysEng__pantheon___6b1efbd8d69b77126e44494c26920619dd7d7f32___17750\",\n", "\"btcpayserver__btcpayserver___a87c2a3374fec8f9c97d17f3472e7d03f76242f9___8860\",\n", "]\n", "\n", "filtered_samples = {k: v for k, v in samples.items() if k in FILTERED_SAMPLES}\n", "filtered_samples"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Eval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_RESULT_DIR_PATH = Path(\"/mnt/efs/augment/eval/jobs/GPW9c5ME/yuri-a100__RemoteEditSystem_wm5x/cache/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_result_files = sorted(list(EVAL_RESULT_DIR_PATH.glob(\"*.json\")))\n", "len(all_result_files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = {}\n", "\n", "for res_file in all_result_files:\n", "    result = utils_for_file.read_json(res_file)\n", "    result_id = f\"{result['repo_url']}_{result['path']}\"\n", "\n", "    # Because we have two samples with the same repo and commit. And here we don't have another id to filter it\n", "    if result_id == \"https://github.com/biopython/biopython/archive/49ef5017d5768760445d7a1bf6157e612bb0f660.zip_Bio/Seq.py\":\n", "        continue\n", "    assert result_id not in results\n", "    results[result_id] = result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EVAL_DATA_PATH = Path(\"/home/<USER>/tmp/pr_edits_eval_145/examples\")\n", "all_files = sorted(list(EVAL_DATA_PATH.glob(\"*.json\")))\n", "len(all_files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["samples = {}\n", "\n", "for ex_file in all_files:\n", "    ex = utils_for_file.read_json(ex_file)\n", "    sample = utils_for_dataclass.create_from_dict(ResearchEditPromptInput, ex)\n", "    assert ex_file.stem not in samples\n", "    samples[ex_file.stem] = sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "for sample_id, sample in samples.items():\n", "    result_id = f\"{sample.extra['repo_url']}_{sample.path}\"\n", "    if result_id not in results:\n", "        print(f\"Didnt find {result_id} in results\")\n", "        continue\n", "    cur_result = results[result_id]\n", "\n", "    is_exact_match = cur_result[\"generated_text\"] == sample.updated_code\n", "\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {sample_id} (Exact match: {is_exact_match})</h2><hr>\"\n", "    MAIN_HTML += f\"<a href=\\\"{sample.extra['pr_url']}\\\">PR link</a>\"\n", "    MAIN_HTML += \"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\"\n", "    MAIN_HTML += f\"<a href=\\\"{REQUEST_INSIGHT_URL}/{cur_result['request_id']}\\\">RI link</a>\"\n", "    # MAIN_HTML += f'<input type=\"checkbox\" id=\"{sample_id}\" name=\"{sample_id}\">'\n", "\n", "\n", "    commented_line_range = [\n", "        sample.extra[\"commented_line\"],\n", "        sample.extra[\"commented_line\"] + 1\n", "    ]\n", "    input_gt_diff = get_diff_html(\n", "        mark_lines(sample.selected_code, commented_line_range),\n", "        mark_lines(sample.updated_code, [-10, -10]),\n", "        sample.instruction,\n", "        \"Selected code <-> Ground truth\"\n", "    )\n", "    input_generated_diff = get_diff_html(\n", "        mark_lines(sample.selected_code, commented_line_range),\n", "        mark_lines(cur_result[\"generated_text\"], [-10, -10]),\n", "        sample.instruction,\n", "        \"Selected code <-> Generated code\"\n", "    )\n", "    generated_output_diff = get_diff_html(\n", "        mark_lines(cur_result[\"generated_text\"], [-10, -10]),\n", "        mark_lines(sample.updated_code, [-10, -10]),\n", "        sample.instruction,\n", "        \"Generated code <-> Ground truth\"\n", "    )\n", "    MAIN_HTML += f\"{input_gt_diff}<hr>\"\n", "    MAIN_HTML += f\"{input_generated_diff}<hr>\"\n", "    MAIN_HTML += f\"{generated_output_diff}<hr>\"\n", "    # break\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_feb_22_eval_145.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result[\"generated_text\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
"""Data preparation for PR edits."""

import argparse
import json
from collections import defaultdict
from functools import partial
from pathlib import Path
from random import random

import numpy as np
import pandas as pd
import pyspark.sql
import torch
from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder
from pyspark.sql import SparkSession
from pyspark.sql import functions as F
from tqdm import tqdm

from base.prompt_format_completion.prompt_formatter import TokenList
from base.prompt_format_edit.prompt_formatter import ExceedContextLength
from research.core.utils_for_log import create_logger
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet

LOGGER = create_logger(__file__)


def create_spark(job_name: str, max_workers: int) -> SparkSession:
    return k8s_session(
        name=job_name,
        gpu_type=["A40", "Quadro_RTX_5000", "RTX_A5000", "RTX_A6000"],
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        max_workers=max_workers,
    )


def join_samples_with_repos(
    spark: SparkSession, synthetic_samples_path: Path, raw_repos_uri: str
) -> pyspark.sql.DataFrame:
    with synthetic_samples_path.open() as f:
        synthetic_samples = json.load(f)
    LOGGER.info(f"Loaded {len(synthetic_samples)} synthetic samples.")

    repos_df = spark.read.parquet(raw_repos_uri)

    samples_by_repo = defaultdict(list)
    for sample in synthetic_samples:
        repo_url = sample["repo_url"]
        samples_by_repo[repo_url].append(json.dumps(sample))

    samples_by_repo_df = pd.DataFrame(
        [
            {"repo_url": repo_url, "samples": samples}
            for repo_url, samples in samples_by_repo.items()
        ]
    )
    samples_by_repo_df = spark.createDataFrame(samples_by_repo_df)

    samples_join_w_repos_df = samples_by_repo_df.join(
        repos_df, F.col("repo_url") == F.col("repo_uuid"), "inner"
    )

    return samples_join_w_repos_df


def are_strings_almost_the_same(s1, s2):
    """Checks whether two strings are the same except for symbols mentioned in `skip_symbols`."""
    skip_symbols = "\n"
    i = j = 0
    while i < len(s1) and j < len(s2):
        if s1[i] == s2[j]:
            i += 1
            j += 1
            continue
        if s1[i] in skip_symbols:
            i += 1
            continue
        if s2[j] in skip_symbols:
            j += 1
            continue
        return False
    return len(s1[i:].rstrip(skip_symbols)) == len(s2[j:].rstrip(skip_symbols)) == 0


def mark_lines(code, line_range):
    result = []
    for i, line in enumerate(code.splitlines(True)):
        if line_range[0] <= i < line_range[1]:
            result.append(f"|>{line}")
        else:
            result.append(line)
    return "".join(result)


RETRIEVER = None


def tokenize(
    samples_join_w_repos_df,
    token_apportionment,
    retriever_config,
    seq_length,
    retrieval_dropout,
    pr_edits,
) -> list[TokenList]:
    # This is needed because codereviewer2train_v2.py:process_cr_sample_batch might return empty samples
    if samples_join_w_repos_df["samples"][0] == "":
        print("Skipping empty sample (samples_join_w_repos_df['samples'][0] == \"\" )")
        return []

    from research.core.abstract_prompt_formatter import get_prompt_formatter
    from research.core.model_input import ModelInput
    from research.eval.harness.factories import create_retriever
    from research.retrieval.types import Document

    prompt_formatter = get_prompt_formatter("droid", **token_apportionment)
    tokenizer = prompt_formatter.tokenizer

    # It's just a hack to turn off retrieval
    if retrieval_dropout < 1:
        global RETRIEVER  # pylint: disable=global-statement
        if RETRIEVER is None:
            RETRIEVER = create_retriever(retriever_config)
            RETRIEVER.load()

    if pr_edits:
        file_list = json.loads(samples_join_w_repos_df["file_list"])
    else:
        file_list = samples_join_w_repos_df["file_list"]

    docs = [
        Document(
            id=file["hexsha"], text=file["content"], path=file["max_stars_repo_path"]
        )
        for file in file_list
    ]

    if retrieval_dropout < 1:
        RETRIEVER.add_docs(docs)

    tokenized_prompts = []
    for sample in samples_join_w_repos_df["samples"]:
        sample = json.loads(sample)
        modified_files = list(
            filter(
                lambda f: f["max_stars_repo_path"] == sample["path"],  # pylint: disable=cell-var-from-loop
                file_list,
            )
        )
        if len(modified_files) != 1:
            print(
                f"Skipping sample (len(modified_files) == {len(modified_files)}) with path={sample['path']}."
            )
            continue
        modified_file = modified_files[0]

        if pr_edits:
            check_middle = sample["old_middle"]
        else:
            check_middle = sample["new_middle"]
        if not are_strings_almost_the_same(
            sample["prefix"] + check_middle + sample["suffix"],
            modified_file["content"],
        ):
            print(f"Skipping sample with path={sample['path']}.")
            continue

        if random() < retrieval_dropout:
            retrieved_chunks = []
        else:
            retrieved_chunks, _ = RETRIEVER.query(
                model_input=ModelInput(
                    prefix=sample["prefix"],
                    suffix=sample["suffix"],
                    path=sample["path"],
                ),
                top_k=128,
            )

        if pr_edits:
            commented_line = sample["full_sample"]["extra"]["commented_line"]
            selected_code = sample["old_middle"]
            selected_code = mark_lines(
                selected_code,
                [commented_line, commented_line + 1],
            )
            instruction = f"Fix PR comment: {sample['instruction']}"
        else:
            selected_code = sample["old_middle"]
            instruction = sample["instruction"]

        model_input = ModelInput(
            path=sample["path"],
            prefix=sample["prefix"],
            suffix=sample["suffix"],
            retrieved_chunks=retrieved_chunks,
            extra={
                "instruction": instruction,
                "selected_code": selected_code,
                "prefix_begin": 0,
                "suffix_end": len(sample["prefix"] + selected_code + sample["suffix"]),
            },
        )

        try:
            tokenized_input, _ = prompt_formatter.prepare_prompt(model_input)
        except ExceedContextLength:
            print(
                f"Skipping sample (ExceedContextLength in prepare_prompt) with path={sample['path']}."
            )
            continue

        tokenized_output = tokenizer.tokenize(sample["new_middle"] + "\n```\n") + [
            tokenizer.eod_id
        ]

        complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output
        if len(complete_prompt) > seq_length:
            print(
                f"Skipping sample (len(complete_prompt) > seq_length) with path={sample['path']}."
            )
            continue
        complete_prompt += [-1 * tokenizer.eod_id] * (
            seq_length - len(complete_prompt) + 1
        )  # +1 to make total prompt of length SEQUENCE_LENGTH + 1

        tokenized_prompts.append(complete_prompt)

    return tokenized_prompts


def tokenize_batch(
    batch: pd.DataFrame,
    token_apportionment,
    retriever_config,
    seq_length,
    retrieval_dropout,
    pr_edits,
    progress_file: Path,
) -> pd.DataFrame:
    tokenized_prompts: list[TokenList] = []
    for i in range(batch.shape[0]):
        cur_result = tokenize(
            batch.iloc[i],
            token_apportionment,
            retriever_config,
            seq_length,
            retrieval_dropout,
            pr_edits,
        )
        with progress_file.open("a") as f:
            f.write(f"{len(cur_result)}\n")
        tokenized_prompts.extend(cur_result)

    # Otherwise we get a schema mismatch. We filter out such samples later in `save_dataset`.
    if len(tokenized_prompts) == 0:
        tokenized_prompts = [[-1]]

    return pd.DataFrame(
        {
            "prompt_tokens": tokenized_prompts,
        },
    )


def save_dataset(train_dataset, eval_dataset, output_path):
    for f_name, dataset in [("train", train_dataset), ("valid", eval_dataset)]:
        cur_output_path = output_path / f_name
        builder = MMapIndexedDatasetBuilder(
            cur_output_path.with_suffix(".bin"), dtype=np.int32
        )
        for sample in tqdm(dataset):
            if len(sample) == 1:
                LOGGER.info("Skipping [-1] sample.")
                continue
            builder.add_item(sample)
            builder.end_document()
        builder.finalize(cur_output_path.with_suffix(".idx"))


def main(args):
    LOGGER.info(
        f"Creating spark session with name={args.job_name} and max_workers={args.max_workers}..."
    )
    spark = create_spark(args.job_name, args.max_workers)
    LOGGER.info("Spark session created.")

    if args.pr_edits:
        assert args.pr_inputs_uri is not None
        samples_join_w_repos_uri = args.pr_inputs_uri
    else:
        LOGGER.info("Joining synthetic samples with repos...")
        samples_join_w_repos_df = join_samples_with_repos(
            spark,
            args.synthetic_json_path,
            args.raw_repos_uri,
        )
        samples_join_w_repos_df.repartition(args.max_workers * 4).write.parquet(
            f"{args.tmp_bucket_uri}/samples_join_w_repos"
        )
        samples_join_w_repos_uri = f"{args.tmp_bucket_uri}/samples_join_w_repos"
        LOGGER.info("Synthetic samples joined with repos.")

    retriever_config = {
        "scorer": {
            "name": "ethanol",
            "checkpoint_path": "ethanol/ethanol6-16.1",
        },
        "chunker": {
            "name": "line_level",
            "max_lines_per_chunk": 30,
        },
        "query_formatter": {
            "name": "ethanol6_query",
            "add_path": True,
            "add_suffix": True,
            "max_tokens": 1023,
            "prefix_ratio": 0.9,
        },
        "document_formatter": {
            "name": "ethanol6_document",
            "add_path": True,
            "max_tokens": 999,
        },
    }
    token_apportionment = {
        "path_len": 256,
        "instruction_len": 512,
        "prefix_len": 1536,
        "selected_code_len": 4096,
        "suffix_len": 1024,
        "max_prompt_tokens": 16384 - 4096,  # 4096 represents the max output tokens
    }

    LOGGER.info("Start tokenizing...")
    spark_result = map_parquet.apply_pandas(
        spark,
        partial(
            tokenize_batch,
            token_apportionment=token_apportionment,
            retriever_config=retriever_config,
            seq_length=args.sequence_length,
            retrieval_dropout=args.retrieval_dropout,
            pr_edits=args.pr_edits,
            progress_file=args.progress_bar_file,
        ),
        input_path=samples_join_w_repos_uri,
        output_path=f"{args.tmp_bucket_uri}/tokenized_samples",
        output_column="prompt_tokens",
        drop_original_columns=True,
        timeout=int(1e6),
        batch_size=4,
    )
    args.output_dir.mkdir(exist_ok=True)
    spark_result["task_info"].to_csv(f"{args.output_dir}/spark_logs.csv")
    LOGGER.info(
        f"Tokenization finished. Saving spark logs {args.output_dir}/spark_logs.csv."
    )

    LOGGER.info("Saving final dataset...")
    tokenized_samples = (
        spark.read.parquet(f"{args.tmp_bucket_uri}/tokenized_samples")
        .toPandas()["prompt_tokens"]
        .tolist()
    )
    args.output_dir.mkdir(exist_ok=True)
    num_train_samples = int(len(tokenized_samples) * args.train_fraction)
    save_dataset(
        tokenized_samples[:num_train_samples],
        tokenized_samples[num_train_samples:],
        args.output_dir,
    )
    LOGGER.info("Final dataset successfully saved.")


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument(
        "--job_name",
        "-jn",
        type=str,
        help="Spark job name.",
        required=True,
    )
    parser.add_argument(
        "--max_workers",
        "-mw",
        type=int,
        help="Maximum number of spark workers.",
        default=32,
    )
    parser.add_argument(
        "--sequence_length",
        "-sl",
        type=int,
        help="Maximum number of spark workers.",
        default=16384,
    )
    parser.add_argument(
        "--synthetic_json_path",
        "-sj",
        type=Path,
        help="Path to the json file containing synthetic data.",
    )
    parser.add_argument(
        "--raw_repos_uri",
        "-rr",
        type=str,
        help="URI to the parquet file(s) containing raw repos.",
    )
    parser.add_argument(
        "--tmp_bucket_uri",
        "-tb",
        type=str,
        help="URI to the temporary 'folder' in the S3 bucket.",
        required=True,
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        help="Path to the the folder where to store training data.",
        required=True,
    )
    parser.add_argument(
        "--train_fraction",
        "-tf",
        type=float,
        help="Fraction of data to use in training. Opposed to validation.",
        default=0.9,
    )
    parser.add_argument(
        "--retrieval_dropout",
        "-rd",
        type=float,
        help="Fraction of data for which we don't use the retrieved chunks.",
        default=0.25,
    )
    parser.add_argument(
        "--pr_edits",
        "-pr",
        action="store_true",
        help="Indicates that we processing PR edits data.",
    )
    parser.add_argument(
        "--pr_inputs_uri",
        "-pri",
        type=str,
        help="URI to the parquet file(s) that contains prepared PR edits data.",
        default=None,
    )
    parser.add_argument(
        "--progress_bar_file",
        "-pf",
        type=Path,
        required=True,
        help="File into which Spark worker logs the progress.",
    )

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    _args = parse_args()
    main(_args)

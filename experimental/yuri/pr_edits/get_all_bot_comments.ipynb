{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "from tqdm import tqdm\n", "from collections import defaultdict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GITHUB_TOKEN = \"????\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_last_n_prs(repo_owner, repo_name, access_token, n):\n", "    prs = []\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls\"\n", "    headers = {\"Authorization\": f\"token {access_token}\"}\n", "    \n", "    items_per_page = min(n, 100)\n", "    params = {\"state\": \"all\", \"per_page\": items_per_page}\n", "\n", "    while True:\n", "        response = requests.get(url, params=params, headers=headers)\n", "        if response.status_code == 200:\n", "            print(\"Successful request\")\n", "            batch = response.json()\n", "            prs.extend(batch)\n", "            # If the length of the batch is less than items_per_page, we've reached the last page\n", "            if len(batch) < items_per_page or len(prs) >= n:\n", "                break\n", "            # Prepare for the next page\n", "            params[\"page\"] = params.get(\"page\", 1) + 1\n", "        else:\n", "            raise Exception(f\"Failed to fetch PRs: {response.status_code} - {response.text}\")\n", "\n", "        # Ensure we don't fetch more than needed\n", "        if len(prs) >= n:\n", "            prs = prs[:n]\n", "            break\n", "\n", "    return prs\n", "    \n", "\n", "def get_pr_comments(repo_owner, repo_name, pr_number, access_token):\n", "    url = url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/{pr_number}/comments\"\n", "    \n", "    headers = {'Accept': 'application/vnd.github.v3+json'}\n", "    headers[\"Authorization\"] = f\"token {access_token}\"\n", "    \n", "    response = requests.get(url, headers=headers)\n", "    \n", "    if response.status_code == 200:\n", "        comments = response.json()\n", "        return comments\n", "    else:\n", "        raise Exception(f\"Failed to fetch PR comments: {response.status_code} - {response.text}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prs = get_last_n_prs(\"augmentcode\", \"augment\", GITHUB_TOKEN, 300)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[*map(lambda pr: pr[\"number\"], prs)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_comments = []\n", "user_to_comments = defaultdict(list)\n", "\n", "for pr in tqdm(prs):\n", "    comments = get_pr_comments(\"augmentcode\", \"augment\", pr[\"number\"], GITHUB_TOKEN)\n", "    all_comments.extend(comments)\n", "    user_to_comments[pr[\"user\"][\"login\"]].extend(comments)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(all_comments)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_comments[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bot_comments = [comment for comment in all_comments if comment[\"user\"][\"login\"] == 'augment-eng[bot]']\n", "len(bot_comments)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bot_comments_sorted = sorted(bot_comments, key=lambda c: c[\"created_at\"], reverse=True)\n", "for c in bot_comments_sorted:\n", "    print(f\"{c['created_at']} - {c['html_url']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for user in user_to_comments:\n", "    user_bot_comments = [comment for comment in user_to_comments[user] if comment[\"user\"][\"login\"] == 'augment-eng[bot]']\n", "    print(f\"{user}: {len(user_bot_comments)}/{len(user_to_comments[user])}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_bot_comments = [comment for comment in user_to_comments[\"dmeister\"] if comment[\"user\"][\"login\"] == 'augment-eng[bot]']\n", "len(user_bot_comments)\n", "\n", "user_bot_comments_sorted = sorted(user_bot_comments, key=lambda c: c[\"created_at\"], reverse=True)\n", "for c in user_bot_comments_sorted:\n", "    print(f\"{c['created_at']}\\t{c['html_url']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
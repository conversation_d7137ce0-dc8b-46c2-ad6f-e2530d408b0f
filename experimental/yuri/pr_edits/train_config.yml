determined:
  name: "yuri_fix_imports_mar30_v13"
  description: null
  workspace: Dev
  project: yuri

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/deepseek_base_33b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/yuri/experiments
  learning_rate: 5e-6
  max_iters: 0
  max_epochs: 1
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  batch_size: 1
  gradient_accumulation_steps: 1
  eval_interval: 100
  block_size: 16384
  # hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct
  # checkpoint_dir: /mnt/efs/augment/user/igor/nlp/logs/droid-142/checkpoint_llama_iteration_839
  checkpoint_dir: /mnt/efs/augment/user/igor/nlp/logs/droid-173/checkpoint_llama_iteration_1269
  train_data_path: /mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k_filtered_tokenized/train
  eval_data_path: /mnt/efs/augment/user/yuri/data/fix_imports_mar30_v13_1k_filtered_tokenized/valid

  wandb_log: True
  wandb_project: yuri
  run_name: yuri_fix_imports_mar30_v13

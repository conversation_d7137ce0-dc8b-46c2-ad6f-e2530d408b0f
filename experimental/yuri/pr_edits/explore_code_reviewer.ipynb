{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/home/<USER>/data/code_reviewer/Code_Refinement/ref-train.jsonl\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["13it [00:00, 9110.43it/s]\n"]}], "source": ["with DATA_PATH.open() as file:\n", "    i = 0\n", "    for line in tqdm(file):\n", "        \n", "        data = json.loads(line)\n", "        if i == 13:\n", "            break\n", "        i += 1\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Can it be final?\n"]}], "source": ["print(data['comment'])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@@ -74,7 +74,7 @@ public class CatalogContext {\n", "     private final byte[] deploymentBytes;\n", "     public final byte[] deploymentHash;\n", "     public final UUID deploymentHashForConfig;\n", "-    public long m_genId; // export generation id\n", "+    public final long m_genId; // export generation id\n", "     public final JdbcDatabaseMetaDataGenerator m_jdbc;\n", "     // Default procs are loaded on the fly\n", "     // The DPM knows which default procs COULD EXIST\n", "\n"]}], "source": ["print(data['hunk'])"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'old_hunk': '@@ -77,8 +74,7 @@ public ProcedurePartitionInfo(VoltType type, int index) {\\n     private final byte[] deploymentBytes;\\n     public final byte[] deploymentHash;\\n     public final UUID deploymentHashForConfig;\\n-    public final long m_transactionId;\\n-    public long m_uniqueId;\\n+    public long m_genId; // export generation id',\n", " 'oldf': '/* This file is part of VoltDB.\\n * Copyright (C) 2008-2017 VoltDB Inc.\\n *\\n * This program is free software: you can redistribute it and/or modify\\n * it under the terms of the GNU Affero General Public License as\\n * published by the Free Software Foundation, either version 3 of the\\n * License, or (at your option) any later version.\\n *\\n * This program is distributed in the hope that it will be useful,\\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\\n * GNU Affero General Public License for more details.\\n *\\n * You should have received a copy of the GNU Affero General Public License\\n * along with VoltDB.  If not, see <http://www.gnu.org/licenses/>.\\n */\\n\\npackage org.voltdb;\\n\\nimport java.io.ByteArrayInputStream;\\nimport java.io.File;\\nimport java.io.IOException;\\nimport java.util.Map;\\nimport java.util.SortedMap;\\nimport java.util.TreeMap;\\nimport java.util.UUID;\\n\\nimport org.apache.zookeeper_voltpatches.KeeperException;\\nimport org.json_voltpatches.JSONException;\\nimport org.voltcore.logging.VoltLogger;\\nimport org.voltcore.messaging.HostMessenger;\\nimport org.voltdb.catalog.Catalog;\\nimport org.voltdb.catalog.CatalogMap;\\nimport org.voltdb.catalog.Cluster;\\nimport org.voltdb.catalog.Database;\\nimport org.voltdb.catalog.Deployment;\\nimport org.voltdb.catalog.Procedure;\\nimport org.voltdb.catalog.SnapshotSchedule;\\nimport org.voltdb.catalog.Table;\\nimport org.voltdb.compiler.PlannerTool;\\nimport org.voltdb.compiler.deploymentfile.DeploymentType;\\nimport org.voltdb.settings.ClusterSettings;\\nimport org.voltdb.settings.DbSettings;\\nimport org.voltdb.settings.NodeSettings;\\nimport org.voltdb.utils.CatalogUtil;\\nimport org.voltdb.utils.Encoder;\\nimport org.voltdb.utils.InMemoryJarfile;\\nimport org.voltdb.utils.VoltFile;\\n\\npublic class CatalogContext {\\n    private static final VoltLogger hostLog = new VoltLogger(\"HOST\");\\n\\n    public static final class ProcedurePartitionInfo {\\n        VoltType type;\\n        int index;\\n        public ProcedurePartitionInfo(VoltType type, int index) {\\n            this.type = type;\\n            this.index = index;\\n        }\\n    }\\n\\n    // THE CATALOG!\\n    public final Catalog catalog;\\n\\n    // PUBLIC IMMUTABLE CACHED INFORMATION\\n    public final Cluster cluster;\\n    public final Database database;\\n    public final CatalogMap<Procedure> procedures;\\n    public final CatalogMap<Table> tables;\\n    public final AuthSystem authSystem;\\n    public final int catalogVersion;\\n    private final byte[] catalogHash;\\n    private final long catalogCRC;\\n    private final byte[] deploymentBytes;\\n    public final byte[] deploymentHash;\\n    public final UUID deploymentHashForConfig;\\n    public long m_genId; // export generation id\\n    public final JdbcDatabaseMetaDataGenerator m_jdbc;\\n    // Default procs are loaded on the fly\\n    // The DPM knows which default procs COULD EXIST\\n    //  and also how to get SQL for them.\\n    public final DefaultProcedureManager m_defaultProcs;\\n    public final HostMessenger m_messenger;\\n\\n    /*\\n     * Planner associated with this catalog version.\\n     * Not thread-safe, should only be accessed by AsyncCompilerAgent\\n     */\\n    public final PlannerTool m_ptool;\\n\\n    // PRIVATE\\n    private final InMemoryJarfile m_jarfile;\\n\\n    // Some people may be interested in the JAXB rather than the raw deployment bytes.\\n    private DeploymentType m_memoizedDeployment;\\n\\n    // database settings. contains both cluster and path settings\\n    private final DbSettings m_dbSettings;\\n    /**\\n     * Constructor especially used during @CatalogContext update when @param hasSchemaChange is false.\\n     * When @param hasSchemaChange is true, @param defaultProcManager and @param plannerTool will be created as new.\\n     * Otherwise, it will try to use the ones passed in to save CPU cycles for performance reason.\\n     * @param genId\\n     * @param catalog\\n     * @param settings\\n     * @param catalogBytes\\n     * @param catalogBytesHash\\n     * @param deploymentBytes\\n     * @param version\\n     * @param messenger\\n     * @param hasSchemaChange\\n     * @param defaultProcManager\\n     * @param plannerTool\\n     */\\n    public CatalogContext(\\n            long genId,\\n            Catalog catalog,\\n            DbSettings settings,\\n            byte[] catalogBytes,\\n            byte[] catalogBytesHash,\\n            byte[] deploymentBytes,\\n            int version,\\n            HostMessenger messenger,\\n            boolean hasSchemaChange,\\n            DefaultProcedureManager defaultProcManager,\\n            PlannerTool plannerTool)\\n    {\\n        m_genId = genId;\\n        // check the heck out of the given params in this immutable class\\n        if (catalog == null) {\\n            throw new IllegalArgumentException(\"Can\\'t create CatalogContext with null catalog.\");\\n        }\\n\\n        if (deploymentBytes == null) {\\n            throw new IllegalArgumentException(\"Can\\'t create CatalogContext with null deployment bytes.\");\\n        }\\n\\n        if (catalogBytes != null) {\\n            try {\\n                m_jarfile = new InMemoryJarfile(catalogBytes);\\n                catalogCRC = m_jarfile.getCRC();\\n            }\\n            catch (Exception e) {\\n                throw new RuntimeException(e);\\n            }\\n\\n            if (catalogBytesHash != null) {\\n                // This is expensive to compute so if it was passed in to us, use it.\\n                this.catalogHash = catalogBytesHash;\\n            }\\n            else {\\n                this.catalogHash = m_jarfile.getSha1Hash();\\n            }\\n        }\\n        else {\\n            throw new IllegalArgumentException(\"Can\\'t create CatalogContext with null catalog bytes.\");\\n        }\\n\\n        if (settings == null) {\\n            throw new IllegalArgumentException(\"Cant\\'t create CatalogContent with null cluster settings\");\\n        }\\n\\n        this.catalog = catalog;\\n        cluster = catalog.getClusters().get(\"cluster\");\\n        database = cluster.getDatabases().get(\"database\");\\n        procedures = database.getProcedures();\\n        tables = database.getTables();\\n        authSystem = new AuthSystem(database, cluster.getSecurityenabled());\\n\\n        this.m_dbSettings = settings;\\n\\n        this.deploymentBytes = deploymentBytes;\\n        this.deploymentHash = CatalogUtil.makeDeploymentHash(deploymentBytes);\\n        this.deploymentHashForConfig = CatalogUtil.makeDeploymentHashForConfig(deploymentBytes);\\n        m_memoizedDeployment = null;\\n\\n\\n        // If there is no schema change, default procedures will not be changed.\\n        // Also, the planner tool can be almost reused except updating the catalog hash string.\\n        // When there is schema change, we just reload every default procedure and create new planner tool\\n        // by applying the existing schema, which are costly in the UAC MP blocking path.\\n        if (hasSchemaChange) {\\n            m_defaultProcs = new DefaultProcedureManager(database);\\n            m_ptool = new PlannerTool(database, catalogHash);\\n        } else {\\n            m_defaultProcs = defaultProcManager;\\n            m_ptool = plannerTool.updateWhenNoSchemaChange(database, catalogBytesHash);;\\n        }\\n\\n        m_jdbc = new JdbcDatabaseMetaDataGenerator(catalog, m_defaultProcs, m_jarfile);\\n\\n        catalogVersion = version;\\n        m_messenger = messenger;\\n\\n        if (procedures != null) {\\n            for (Procedure proc : procedures) {\\n                if (proc.getSinglepartition()) {\\n                    ProcedurePartitionInfo ppi = new ProcedurePartitionInfo(VoltType.get((byte)proc.getPartitioncolumn().getType()), proc.getPartitionparameter());\\n                    proc.setAttachment(ppi);\\n                }\\n            }\\n        }\\n    }\\n\\n    /**\\n     * Constructor of @CatalogConext used when creating brand-new instances.\\n     * @param genId\\n     * @param catalog\\n     * @param settings\\n     * @param catalogBytes\\n     * @param catalogBytesHash\\n     * @param deploymentBytes\\n     * @param version\\n     * @param messenger\\n     */\\n    public CatalogContext(\\n            long genId,\\n            Catalog catalog,\\n            DbSettings settings,\\n            byte[] catalogBytes,\\n            byte[] catalogBytesHash,\\n            byte[] deploymentBytes,\\n            int version,\\n            HostMessenger messenger)\\n    {\\n        this(genId, catalog, settings, catalogBytes, catalogBytesHash, deploymentBytes,\\n                version, messenger, true, null, null);\\n    }\\n\\n    public Cluster getCluster() {\\n        return cluster;\\n    }\\n\\n    public ClusterSettings getClusterSettings() {\\n        return m_dbSettings.getCluster();\\n    }\\n\\n    public NodeSettings getNodeSettings() {\\n        return m_dbSettings.getNodeSetting();\\n    }\\n\\n    public CatalogContext update(\\n            long genId,\\n            byte[] catalogBytes,\\n            byte[] catalogBytesHash,\\n            String diffCommands,\\n            boolean incrementVersion,\\n            byte[] deploymentBytes,\\n            HostMessenger messenger,\\n            boolean hasSchemaChange)\\n    {\\n        Catalog newCatalog = catalog.deepCopy();\\n        newCatalog.execute(diffCommands);\\n        int incValue = incrementVersion ? 1 : 0;\\n        // If there\\'s no new catalog bytes, preserve the old one rather than\\n        // bashing it\\n        byte[] bytes = catalogBytes;\\n        if (bytes == null) {\\n            try {\\n                bytes = this.getCatalogJarBytes();\\n            } catch (IOException e) {\\n                // Failure is not an option\\n                hostLog.fatal(e.getMessage());\\n            }\\n        }\\n        // Ditto for the deploymentBytes\\n        byte[] depbytes = deploymentBytes;\\n        if (depbytes == null) {\\n            depbytes = this.deploymentBytes;\\n        }\\n        CatalogContext retval =\\n            new CatalogContext(\\n                    genId,\\n                    newCatalog,\\n                    this.m_dbSettings,\\n                    bytes,\\n                    catalogBytesHash,\\n                    depbytes,\\n                    catalogVersion + incValue,\\n                    messenger,\\n                    hasSchemaChange,\\n                    m_defaultProcs,\\n                    m_ptool);\\n        return retval;\\n    }\\n\\n    /**\\n     * Get a file/entry (as bytes) given a key/path in the source jar.\\n     *\\n     * @param key In-jar path to file.\\n     * @return byte[] or null if the file doesn\\'t exist.\\n     */\\n    public byte[] getFileInJar(String key) {\\n        return m_jarfile.get(key);\\n    }\\n\\n    public enum CatalogJarWriteMode {\\n        START_OR_RESTART,\\n        CATALOG_UPDATE,\\n        RECOVER\\n    }\\n\\n    /**\\n     * Write, replace or update the catalog jar based on different cases. This function\\n     * assumes any IOException should lead to fatal crash.\\n     * @param path\\n     * @param name\\n     * @throws IOException\\n     */\\n    public Runnable writeCatalogJarToFile(String path, String name, CatalogJarWriteMode mode) throws IOException\\n    {\\n        File catalogFile = new VoltFile(path, name);\\n        File catalogTmpFile = new VoltFile(path, name + \".tmp\");\\n\\n        if (mode == CatalogJarWriteMode.CATALOG_UPDATE) {\\n            // This means a @UpdateCore case, the asynchronous writing of\\n            // jar file has finished, rename the jar file\\n            catalogFile.delete();\\n            catalogTmpFile.renameTo(catalogFile);\\n            return null;\\n        }\\n\\n        if (mode == CatalogJarWriteMode.START_OR_RESTART) {\\n            // This happens in the beginning of ,\\n            // when the catalog jar does not yet exist. Though the contents\\n            // written might be a default one and could be overwritten later\\n            // by @UAC, @UpdateClasses, etc.\\n            return m_jarfile.writeToFile(catalogFile);\\n        }\\n\\n        if (mode == CatalogJarWriteMode.RECOVER) {\\n            // we must overwrite the file (the file may have been changed)\\n            catalogFile.delete();\\n            if (catalogTmpFile.exists()) {\\n                // If somehow the catalog temp jar is not cleaned up, then delete it\\n                catalogTmpFile.delete();\\n            }\\n\\n            return m_jarfile.writeToFile(catalogFile);\\n        }\\n\\n        VoltDB.crashLocalVoltDB(\"Unsupported mode to write catalog jar\", true, null);\\n        return null;\\n    }\\n\\n    /**\\n     * Get the raw bytes of a catalog file for shipping around.\\n     */\\n    public byte[] getCatalogJarBytes() throws IOException {\\n        if (m_jarfile == null) {\\n            return null;\\n        }\\n        return m_jarfile.getFullJarBytes();\\n    }\\n\\n    /**\\n     * Get the JAXB XML Deployment object, which is memoized\\n     */\\n    public DeploymentType getDeployment()\\n    {\\n        if (m_memoizedDeployment == null) {\\n            m_memoizedDeployment = CatalogUtil.getDeployment(new ByteArrayInputStream(deploymentBytes));\\n            // This should NEVER happen\\n            if (m_memoizedDeployment == null) {\\n                VoltDB.crashLocalVoltDB(\"The internal deployment bytes are invalid.  This should never occur; please contact VoltDB support with your logfiles.\");\\n            }\\n        }\\n        return m_memoizedDeployment;\\n    }\\n\\n    /**\\n     * Get the XML Deployment bytes\\n     */\\n    public byte[] getDeploymentBytes()\\n    {\\n        return deploymentBytes;\\n    }\\n\\n    /**\\n     * Given a class name in the catalog jar, loads it from the jar, even if the\\n     * jar is served from an URL and isn\\'t in the classpath.\\n     *\\n     * @param procedureClassName The name of the class to load.\\n     * @return A java Class variable associated with the class.\\n     * @throws ClassNotFoundException if the class is not in the jar file.\\n     */\\n    public Class<?> classForProcedure(String procedureClassName) throws ClassNotFoundException {\\n        return classForProcedure(procedureClassName, m_jarfile.getLoader());\\n    }\\n\\n    public static Class<?> classForProcedure(String procedureClassName, ClassLoader loader)\\n            throws ClassNotFoundException {\\n        // this is a safety mechanism to prevent catalog classes overriding VoltDB stuff\\n        if (procedureClassName.startsWith(\"org.voltdb.\")) {\\n            return Class.forName(procedureClassName);\\n        }\\n\\n        // look in the catalog for the file\\n        return Class.forName(procedureClassName, true, loader);\\n    }\\n\\n    // Generate helpful status messages based on configuration present in the\\n    // catalog.  Used to generated these messages at startup and after an\\n    // @UpdateApplicationCatalog\\n    SortedMap<String, String> getDebuggingInfoFromCatalog(boolean verbose)\\n    {\\n        SortedMap<String, String> logLines = new TreeMap<>();\\n\\n        // topology\\n        Deployment deployment = cluster.getDeployment().iterator().next();\\n        int hostCount = m_dbSettings.getCluster().hostcount();\\n        if (verbose) {\\n            Map<Integer, Integer> sphMap;\\n            try {\\n                sphMap = m_messenger.getSitesPerHostMapFromZK();\\n            } catch (KeeperException | InterruptedException | JSONException e) {\\n                hostLog.warn(\"Failed to get sitesperhost information from Zookeeper\", e);\\n                sphMap = null;\\n            }\\n            int kFactor = deployment.getKfactor();\\n            if (sphMap == null) {\\n                logLines.put(\"deployment1\",\\n                        String.format(\"Cluster has %d hosts with leader hostname: \\\\\"%s\\\\\". [unknown] local sites count. K = %d.\",\\n                                hostCount, VoltDB.instance().getConfig().m_leader, kFactor));\\n                logLines.put(\"deployment2\", \"Unable to retrieve partition information from the cluster.\");\\n            } else {\\n                int localSitesCount = sphMap.get(m_messenger.getHostId());\\n                logLines.put(\"deployment1\",\\n                        String.format(\"Cluster has %d hosts with leader hostname: \\\\\"%s\\\\\". %d local sites count. K = %d.\",\\n                                hostCount, VoltDB.instance().getConfig().m_leader, localSitesCount, kFactor));\\n\\n                int totalSitesCount = 0;\\n                for (Map.Entry<Integer, Integer> e : sphMap.entrySet()) {\\n                    totalSitesCount += e.getValue();\\n                }\\n                int replicas = kFactor + 1;\\n                int partitionCount = totalSitesCount / replicas;\\n                logLines.put(\"deployment2\",\\n                        String.format(\"The entire cluster has %d %s of%s %d logical partition%s.\",\\n                                replicas,\\n                                replicas > 1 ? \"copies\" : \"copy\",\\n                                        partitionCount > 1 ? \" each of the\" : \"\",\\n                                                partitionCount,\\n                                                partitionCount > 1 ? \"s\" : \"\"));\\n            }\\n        }\\n\\n        // voltdb root\\n        logLines.put(\"voltdbroot\", \"Using \\\\\"\" + VoltDB.instance().getVoltDBRootPath() + \"\\\\\" for voltdbroot directory.\");\\n\\n        // partition detection\\n        if (cluster.getNetworkpartition()) {\\n            logLines.put(\"partition-detection\", \"Detection of network partitions in the cluster is enabled.\");\\n        }\\n        else {\\n            logLines.put(\"partition-detection\", \"Detection of network partitions in the cluster is not enabled.\");\\n        }\\n\\n        // security info\\n        if (cluster.getSecurityenabled()) {\\n            logLines.put(\"sec-enabled\", \"Client authentication is enabled.\");\\n        }\\n        else {\\n            logLines.put(\"sec-enabled\", \"Client authentication is not enabled. Anonymous clients accepted.\");\\n        }\\n\\n        // auto snapshot info\\n        SnapshotSchedule ssched = database.getSnapshotschedule().get(\"default\");\\n        if (ssched == null || !ssched.getEnabled()) {\\n            logLines.put(\"snapshot-schedule1\", \"No schedule set for automated snapshots.\");\\n        }\\n        else {\\n            final String frequencyUnitString = ssched.getFrequencyunit().toLowerCase();\\n            final char frequencyUnit = frequencyUnitString.charAt(0);\\n            String msg = \"[unknown frequency]\";\\n            switch (frequencyUnit) {\\n            case \\'s\\':\\n                msg = String.valueOf(ssched.getFrequencyvalue()) + \" seconds\";\\n                break;\\n            case \\'m\\':\\n                msg = String.valueOf(ssched.getFrequencyvalue()) + \" minutes\";\\n                break;\\n            case \\'h\\':\\n                msg = String.valueOf(ssched.getFrequencyvalue()) + \" hours\";\\n                break;\\n            }\\n            logLines.put(\"snapshot-schedule1\", \"Automatic snapshots enabled, saved to \" + VoltDB.instance().getSnapshotPath() +\\n                         \" and named with prefix \\'\" + ssched.getPrefix() + \"\\'.\");\\n            logLines.put(\"snapshot-schedule2\", \"Database will retain a history of \" + ssched.getRetain() +\\n                         \" snapshots, generated every \" + msg + \".\");\\n        }\\n\\n        return logLines;\\n    }\\n\\n    public long getCatalogCRC() {\\n        return catalogCRC;\\n    }\\n\\n    public byte[] getCatalogHash()\\n    {\\n        return catalogHash;\\n    }\\n\\n    public String getCatalogLogString() {\\n        return String.format(\"Catalog: catalog hash %s, deployment hash %s\",\\n                                Encoder.hexEncode(catalogHash).substring(0, 10),\\n                                Encoder.hexEncode(deploymentHash).substring(0, 10));\\n    }\\n\\n    public InMemoryJarfile getCatalogJar() {\\n        return m_jarfile;\\n    }\\n}\\n',\n", " 'hunk': '@@ -74,7 +74,7 @@ public class CatalogContext {\\n     private final byte[] deploymentBytes;\\n     public final byte[] deploymentHash;\\n     public final UUID deploymentHashForConfig;\\n-    public long m_genId; // export generation id\\n+    public final long m_genId; // export generation id\\n     public final JdbcDatabaseMetaDataGenerator m_jdbc;\\n     // Default procs are loaded on the fly\\n     // The DPM knows which default procs COULD EXIST\\n',\n", " 'comment': 'Can it be final?',\n", " 'ids': [81094,\n", "  '7d3d9afb460d277f419ec81e3fd3ad59f1f724c6',\n", "  '08f78fc892886e02f8498ae6e47831862c351526'],\n", " 'repo': 'VoltDB/voltdb',\n", " 'ghid': 4723,\n", " 'old': '     private final byte[] deploymentBytes;\\n     public final byte[] deploymentHash;\\n     public final UUID deploymentHashForConfig;\\n-    public long m_genId; // export generation id\\n     public final JdbcDatabaseMetaDataGenerator m_jdbc;\\n     // Default procs are loaded on the fly\\n     // The DPM knows which default procs COULD EXIST',\n", " 'new': '     private final byte[] deploymentBytes;\\n     public final byte[] deploymentHash;\\n     public final UUID deploymentHashForConfig;\\n+    public final long m_genId; // export generation id\\n     public final JdbcDatabaseMetaDataGenerator m_jdbc;\\n     // Default procs are loaded on the fly\\n     // The DPM knows which default procs COULD EXIST',\n", " 'lang': 'java'}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@@ -74,7 +74,7 @@ public class CatalogContext {\n", "     private final byte[] deploymentBytes;\n", "     public final byte[] deploymentHash;\n", "     public final UUID deploymentHashForConfig;\n", "-    public long m_genId; // export generation id\n", "+    public final long m_genId; // export generation id\n", "     public final JdbcDatabaseMetaDataGenerator m_jdbc;\n", "     // Default procs are loaded on the fly\n", "     // The DPM knows which default procs COULD EXIST\n", "\n"]}], "source": ["print(data['hunk'])"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@@ -77,8 +74,7 @@ public ProcedurePartitionInfo(VoltType type, int index) {\n", "     private final byte[] deploymentBytes;\n", "     public final byte[] deploymentHash;\n", "     public final UUID deploymentHashForConfig;\n", "-    public final long m_transactionId;\n", "-    public long m_uniqueId;\n", "+    public long m_genId; // export generation id\n"]}], "source": ["print(data['old_hunk'])"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     private final byte[] deploymentBytes;\n", "     public final byte[] deploymentHash;\n", "     public final UUID deploymentHashForConfig;\n", "-    public long m_genId; // export generation id\n", "     public final JdbcDatabaseMetaDataGenerator m_jdbc;\n", "     // Default procs are loaded on the fly\n", "     // The DPM knows which default procs COULD EXIST\n"]}], "source": ["print(data['old'])"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     private final byte[] deploymentBytes;\n", "     public final byte[] deploymentHash;\n", "     public final UUID deploymentHashForConfig;\n", "+    public final long m_genId; // export generation id\n", "     public final JdbcDatabaseMetaDataGenerator m_jdbc;\n", "     // Default procs are loaded on the fly\n", "     // The DPM knows which default procs COULD EXIST\n"]}], "source": ["print(data['new'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["def get_diff_html(result, name):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "    selected_code = result[\"selected_code\"]\n", "    updated_code = result[\"updated_code\"]\n", "\n", "    while selected_code.startswith('\\n'):\n", "        selected_code = selected_code[1:]\n", "\n", "    while updated_code.startswith('\\n'):\n", "        updated_code = updated_code[1:]\n", "\n", "    diff_html = diff_obj.make_file(\n", "        selected_code.splitlines(),\n", "        updated_code.splitlines()\n", "    )\n", "\n", "    instructions_html = f\"<li><strong>{result['instruction']}</strong></li>\"\n", "    # other_instructions = [\"AAA\", \"BBB\"]\n", "    other_instructions = []\n", "    for inst in other_instructions:\n", "        instructions_html += f\"<li>{inst}</li>\"\n", "    instructions_html = \"<ul class=\\\"instructions\\\">\" + instructions_html + \"</ul>\"\n", "\n", "    html = f\"\"\"\n", "\n", "    <h4>Instructions:</h4>\n", "    <h4>{instructions_html}</h4>\n", "    \n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    \n", "    return html"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = f\"\"\"\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["501it [00:00, 12368.50it/s]\n"]}], "source": ["COMPLETE_HTML = \"\"\n", "\n", "with DATA_PATH.open() as file:\n", "    i = 0\n", "    for line in tqdm(file):\n", "        \n", "        data = json.loads(line)\n", "        COMPLETE_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "        COMPLETE_HTML += f\"<li><strong>{i}  {data['comment']}</strong></li>\"\n", "        COMPLETE_HTML += f'<pre>{data[\"hunk\"]}</pre>'\n", "\n", "        if i > 500:\n", "            break\n", "        i += 1\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }\n", "        .wide-line {\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }\n", "        .instructions li {\n", "           color: gray; /* This makes all list items gray */\n", "        }\n", "\n", "        .instructions li:first-child {\n", "            color: black; /* This changes the color of the first item to black */\n", "        }\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "<hr class=\"wide-line\"><li><strong>0  Maybe assert false</strong></li><pre>@@ -66,6 +66,9 @@ bool TransformationAddGlobalVariable::IsApplicable(\n", "   if (message_.initializer_id()) {\n", "     // An initializer is not allowed if the storage class is Workgroup.\n", "     if (storage_class == SpvStorageClassWorkgroup) {\n", "+      assert(false &&\n", "+             \"By construction this transformation should not have an \"\n", "+             \"initializer when Workgroup storage class is used.\");\n", "       return false;\n", "     }\n", "     // The initializer id must be the id of a constant.  Check this with the\n", "</pre><hr class=\"wide-line\"><li><strong>1  Would it be better if add annotations to the parameters?</strong></li><pre>@@ -16,7 +16,7 @@ public class OnThisDayActivity extends SingleFragmentActivity<OnThisDayFragment>\n", "     public static final String YEAR = \"year\";\n", "     public static final String WIKISITE = \"wikisite\";\n", " \n", "-    public static Intent newIntent(@NonNull Context context, int age, WikiSite wikiSite, InvokeSource invokeSource, int year) {\n", "+    public static Intent newIntent(@NonNull Context context, int age, @NonNull WikiSite wikiSite, @NonNull InvokeSource invokeSource, int year) {\n", "         return new Intent(context, OnThisDayActivity.class)\n", "                 .putExtra(AGE, age)\n", "                 .putExtra(WIKISITE, wikiSite)\n", "</pre><hr class=\"wide-line\"><li><strong>2  ```suggestion return category.url if category ```</strong></li><pre>@@ -80,7 +80,7 @@ class Permalink < ActiveRecord::Base\n", "     return external_url if external_url\n", "     return \"#{Discourse::base_uri}#{post.url}\" if post\n", "     return topic.relative_url if topic\n", "-    return \"#{category.url}\" if category\n", "+    return category.url if category\n", "     return tag.full_url if tag\n", "     nil\n", "   end\n", "</pre><hr class=\"wide-line\"><li><strong>3  We don't need to check not nil before try to assert to bool.</strong></li><pre>@@ -253,10 +253,8 @@ func (c *twoPhaseCommitter) prewriteSingleBatch(bo *Backoffer, batch batchKeys)\n", " \n", " \tskipCheck := false\n", " \toptSkipCheck := c.txn.us.GetOption(kv.SkipCheckForWrite)\n", "-\tif optSkipCheck != nil {\n", "-\t\tif skip, ok := optSkipCheck.(bool); ok && skip {\n", "-\t\t\tskip<PERSON><PERSON><PERSON> = true\n", "-\t\t}\n", "+\tif skip, ok := optSkipCheck.(bool); ok && skip {\n", "+\t\tskipCheck = true\n", " \t}\n", " \treq := &pb.Request{\n", " \t\tType: pb.MessageType_CmdPrewrite,\n", "</pre><hr class=\"wide-line\"><li><strong>4  per current error handling best practices, its fine to continue for other resources in this action, but we want to stop policy exec by raising at the end of processing. ```python # pseudo error = None if f.exception() error = f.exception() if error: raise error ```</strong></li><pre>@@ -1718,6 +1718,7 @@ class Update(Action):\n", " \n", "         with self.executor_factory(max_workers=2) as w:\n", "             futures = {}\n", "+            error = None\n", "             for a in asgs:\n", "                 futures[w.submit(self.process_asg, client, a, settings)] = a\n", "             for f in as_completed(futures):\n", "</pre><hr class=\"wide-line\"><li><strong>5  This is no longer just an \"operator\" config map</strong></li><pre>@@ -2537,11 +2537,10 @@ func operatorPod(podName, appName, operatorServiceIP, agentPath, operatorImagePa\n", " \n", " // operatorConfigMap returns a *core.ConfigMap for the operator pod\n", " // of the specified application, with the specified configuration.\n", "-func operatorConfigMap(appName, cmName string, labels map[string]string, config *caas.OperatorConfig) *core.ConfigMap {\n", "+func operatorConfigMap(appName, name string, labels map[string]string, config *caas.OperatorConfig) *core.ConfigMap {\n", " \treturn &core.ConfigMap{\n", " \t\tObjectMeta: v1.ObjectMeta{\n", "-\t\t\tName: cm<PERSON><PERSON>,\n", "-\t\t\t// TODO: properly labling operator resources could ensure all resources get deleted when application is removed.\n", "+\t\t\tName:   name,\n", " \t\t\tLabels: labels,\n", " \t\t},\n", " \t\tData: map[string]string{\n", "</pre><hr class=\"wide-line\"><li><strong>6  `render html: \"<h1>bla</h1>\"` might work too ...</strong></li><pre>@@ -78,11 +78,6 @@ class Admin::SecretsController < ApplicationController\n", "     render :edit\n", "   end\n", " \n", "-  def failure(message)\n", "-    flash[:error] = message\n", "-    render :fail\n", "-  end\n", "-\n", "   def find_secret\n", "     @secret = SecretStorage.read(key, include_value: true)\n", "   end\n", "</pre><hr class=\"wide-line\"><li><strong>7  This can remain just `%s` because there is only one placeholder.</strong></li><pre>@@ -2008,7 +2008,7 @@ class AMP_Invalid_URL_Post_Type {\n", " \t\t}\n", " \n", " \t\t/* translators: %s is the name of the page with the the validation error(s) */\n", "-\t\treturn esc_html( sprintf( __( 'Errors for: %1$s', 'amp' ), $name ) );\n", "+\t\treturn esc_html( sprintf( __( 'Errors for: %s', 'amp' ), $name ) );\n", " \t}\n", " \n", " \t/**\n", "</pre><hr class=\"wide-line\"><li><strong>8  Did you mean to remove this and the related code?</strong></li><pre>@@ -144,6 +144,7 @@\n", "   angular.module('plotly', [])\n", "     .constant('ColorPalette', ColorPalette)\n", "     .directive('plotlyChart', function () {\n", "+      var bottomMargin = 50;\n", "       return {\n", "         restrict: 'E',\n", "         template: '<div></div>',\n", "</pre><hr class=\"wide-line\"><li><strong>9  if needToPrintMessage is true , I think it is better to add some message which mean to end a route</strong></li><pre>@@ -35,6 +35,11 @@ public class TailStateRouter<T> implements StateRouter<T> {\n", " \n", "     }\n", " \n", "+    @Override\n", "+    public void setNextRouter(StateRouter<T> nextRouter) {\n", "+\n", "+    }\n", "+\n", "     @Override\n", "     public URL getUrl() {\n", "         return null;\n", "</pre><hr class=\"wide-line\"><li><strong>10  ```suggestion // over multiple attempts, top scorers should be picked on high positions more often. ``` maybe change high scorers to top scorers</strong></li><pre>@@ -886,7 +886,7 @@ func TestBlocksFetcher_filterScoredPeers(t *testing.T) {\n", " \t\t\t}\n", " \t\t\t// Since peer selection is probabilistic (weighted, with high scorers having higher\n", " \t\t\t// chance of being selected), we need multiple rounds of filtering to test the order:\n", "-\t\t\t// over multiple attempts, high scorers should be picked on high posstions more often.\n", "+\t\t\t// over multiple attempts, top scorers should be picked on high positions more often.\n", " \t\t\tpeerStats := make(map[peer.ID]int, len(tt.want))\n", " \t\t\tvar filteredPIDs []peer.ID\n", " \t\t\tvar err error\n", "</pre><hr class=\"wide-line\"><li><strong>11  Without this code, how will the spec options on line 54 be validated?</strong></li><pre>@@ -38,6 +38,14 @@ def _validate(obj, items, loader, MISSING, INVALID):\n", "                 # initialize as empty builtin type\n", "                 obj[v.key] = v.type()\n", " \n", "+        if v.children and obj[v.key]:\n", "+            # handle recursive elements which may be passed as strings\n", "+            if v.children == 'self':\n", "+                children = items\n", "+            else:\n", "+                children = v.children\n", "+            _validate(obj[v.key], children, loader, MISSING, INVALID)\n", "+\n", " \n", " def spec_validator(spec, loader):\n", "     if not isinstance(spec, dict):\n", "</pre><hr class=\"wide-line\"><li><strong>12  This looks like a good way of doing it to make the code compile on Java versions prior to 8. Here is a suggestion to give it more speaking error messages: When you find that the `writeReplace` method returns a `SerializedLambda`, try to look up that class. If the lookup fails, throw an error saying something like \"User code tries to use lambdas, but framework is running with a Java version < 8\" or so.</strong></li><pre>@@ -97,6 +97,14 @@ public class FunctionUtils {\n", " \n", " \t\t\t\t\t// check if class is a lambda function\n", " \t\t\t\t\tif (serialVersion.getClass().getName().equals(\"java.lang.invoke.SerializedLambda\")) {\n", "+\t\t\t\t\t\t\n", "+\t\t\t\t\t\t// check if SerializedLambda class is present\n", "+\t\t\t\t\t\ttry {\n", "+\t\t\t\t\t\t\tClass.forName(\"java.lang.invoke.SerializedLambda\");\n", "+\t\t\t\t\t\t}\n", "+\t\t\t\t\t\tcatch (Throwable t) {\n", "+\t\t\t\t\t\t\tthrow new UnsupportedOperationException(\"User code tries to use lambdas, but framework is running with a Java version < 8\");\n", "+\t\t\t\t\t\t}\n", " \t\t\t\t\t\tserializedLambda = serialVersion;\n", " \t\t\t\t\t\tbreak;\n", " \t\t\t\t\t}\n", "</pre><hr class=\"wide-line\"><li><strong>13  Can it be final?</strong></li><pre>@@ -74,7 +74,7 @@ public class CatalogContext {\n", "     private final byte[] deploymentBytes;\n", "     public final byte[] deploymentHash;\n", "     public final UUID deploymentHashForConfig;\n", "-    public long m_genId; // export generation id\n", "+    public final long m_genId; // export generation id\n", "     public final JdbcDatabaseMetaDataGenerator m_jdbc;\n", "     // Default procs are loaded on the fly\n", "     // The DPM knows which default procs COULD EXIST\n", "</pre><hr class=\"wide-line\"><li><strong>14  If this is a prescribed Django fix for this, perhaps link to the permalink in docs here to explain what this is for.</strong></li><pre>@@ -29,6 +29,13 @@ from builtins import range\n", " \n", " \n", " def get_fields_with_model(cls):\n", "+    \"\"\"\n", "+    As of Django 1.10 Model._meta.get_fields_with_model() is deprecated.\n", "+    It was used in VersionSlugField.get_queryset(), but is now replaced\n", "+    with local function get_fields_with_model() as prescribed by the Django\n", "+    docs.\n", "+    https://docs.djangoproject.com/en/1.11/ref/models/meta/#migrating-from-the-old-api\n", "+    \"\"\"\n", "     return [\n", "         (f, f.model if f.model != cls else None)\n", "         for f in cls._meta.get_fields()\n", "</pre><hr class=\"wide-line\"><li><strong>15  shall we use \"log\" instead of \"LOG\" private static final Logger log = LoggerFactory.getLogger(MimeUtil.class);</strong></li><pre>@@ -288,11 +288,11 @@ public class MimeUtil {\n", "         if (isNotNullAndEmpty(returnValue)) {\n", "             return returnValue;\n", "         } else {\n", "-            String filePath = getFilePathFromFileHandler(entity);\n", "+            String filePath = getFilePathFromFileStruct(entity);\n", "             try {\n", "                 return filePath.isEmpty() ? null : new String(readFromFile(filePath), UTF_8);\n", "             } catch (UnsupportedEncodingException e) {\n", "-                LOG.error(\"Error occurred while extracting text payload from entity\", e.getMessage());\n", "+                log.error(\"Error occurred while extracting text payload from entity\", e.getMessage());\n", "             }\n", "         }\n", "         return null;\n", "</pre><hr class=\"wide-line\"><li><strong>16  Looked at it briefly, so please make sure, but it seems to me it's a good chance to move the above check to `isValidLocationPriority` also.</strong></li><pre>@@ -454,10 +454,6 @@ class LocationService implements LocationServiceInterface\n", "      */\n", "     public function updateLocation(APILocation $location, LocationUpdateStruct $locationUpdateStruct)\n", "     {\n", "-        if ($locationUpdateStruct->priority !== null && !is_int($locationUpdateStruct->priority)) {\n", "-            throw new InvalidArgumentValue('priority', $locationUpdateStruct->priority, 'LocationUpdateStruct');\n", "-        }\n", "-\n", "         if (!$this->domainMapper->isValidLocationPriority($locationUpdateStruct->priority)) {\n", "             throw new InvalidArgumentValue('priority', $locationUpdateStruct->priority, 'LocationUpdateStruct');\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>17  ```suggestion * @property {SplitChunksSizes} maxAsyncSize * @property {SplitChunksSizes} maxInitialSize ```</strong></li><pre>@@ -853,7 +853,8 @@ module.exports = class SplitChunksPlugin {\n", " \t\t\t\t\t/**\n", " \t\t\t\t\t * @typedef {Object} MaxSizeQueueItem\n", " \t\t\t\t\t * @property {SplitChunksSizes} minSize\n", "-\t\t\t\t\t * @property {SplitChunksSizes} maxSize\n", "+\t\t\t\t\t * @property {SplitChunksSizes} maxAsyncSize\n", "+\t\t\t\t\t * @property {SplitChunksSizes} maxInitialSize\n", " \t\t\t\t\t * @property {string} automaticNameDelimiter\n", " \t\t\t\t\t * @property {string[]} keys\n", " \t\t\t\t\t */\n", "</pre><hr class=\"wide-line\"><li><strong>18  Considering this as s test setup block should we use the existing feconf constant for this value (featured)?</strong></li><pre>@@ -30,6 +30,7 @@ from core.domain import prod_validation_jobs_one_off\n", " from core.domain import user_services\n", " from core.platform import models\n", " from core.tests import test_utils\n", "+import feconf\n", " \n", " (activity_models,) = models.Registry.import_models([models.NAMES.activity])\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>19  Are these debug statements...?</strong></li><pre>@@ -813,7 +813,6 @@ func (p *pg) UpdateRole(ctx context.Context, role *v2.Role, checkProjects bool)\n", " \tif err != nil {\n", " \t\treturn nil, err\n", " \t}\n", "-\tp.logger.Warnf(\"projects filter: %s\", projectsFilter)\n", " \n", " \ttx, err := p.db.BeginTx(ctx, nil /* use driver default */)\n", " \tif err != nil {\n", "</pre><hr class=\"wide-line\"><li><strong>20  Nit: Could be ``` private static void TryRunSetupSpinWaitContinuation(TdsParserStateObject stateObj) => SpinWait.SpinUntil(() => !stateObj._attentionSending); ```</strong></li><pre>@@ -2160,10 +2160,7 @@ namespace System.Data.SqlClient\n", "         }\n", " \n", "         // This is in its own method to avoid always allocating the lambda in TryRun \n", "-        private static void TryRunSetupSpinWaitContinuation(TdsParserStateObject stateObj)\n", "-        {\n", "-            SpinWait.SpinUntil(() => !stateObj._attentionSending);\n", "-        }\n", "+        private static void TryRunSetupSpinWaitContinuation(TdsParserStateObject stateObj) => SpinWait.SpinUntil(() => !stateObj._attentionSending);\n", " \n", "         private bool TryProcessEnvChange(int tokenLength, TdsParserStateObject stateObj, out SqlEnvChange[] sqlEnvChange)\n", "         {\n", "</pre><hr class=\"wide-line\"><li><strong>21  I think this returns `-> \"ReportType\"`?</strong></li><pre>@@ -220,8 +220,8 @@ class ReportType(Enum):\n", " \n", "     _report_name: str\n", " \n", "-    def __new__(cls, value: str, report_name: Optional[str] = None):\n", "-        member = object.__new__(cls)\n", "+    def __new__(cls, value: str, report_name: Optional[str] = None) -> \"ReportType\":\n", "+        member: \"ReportType\" = object.__new__(cls)\n", "         member._value_ = value\n", "         member._report_name = report_name if report_name is not None else value\n", "         return member\n", "</pre><hr class=\"wide-line\"><li><strong>22  Why do we need to use 'abs' here? If an underflow is possible then we are going to set a meaningless value</strong></li><pre>@@ -528,7 +528,7 @@ public class Consumer {\n", "                 cursorBitSet.and(givenBitSet);\n", "                 givenBitSet.recycle();\n", "                 int currentCardinality = cursorBitSet.cardinality();\n", "-                ackedCount = Math.abs(currentCardinality - lastCardinality);\n", "+                ackedCount = lastCardinality - currentCardinality;\n", "                 cursorBitSet.recycle();\n", "             } else {\n", "                 ackedCount = batchSize - BitSet.valueOf(ackSets).cardinality();\n", "</pre><hr class=\"wide-line\"><li><strong>23  nit: seems like continue skips just a single statement here so 'else' may be preferable</strong></li><pre>@@ -536,9 +536,11 @@ namespace Microsoft.CodeAnalysis.CSharp\n", "                     {\n", "                         Debug.Assert(recursive.HasAnyErrors);\n", "                         tests.Add(new Tests.One(new BoundDagTypeTest(recursive.Syntax, ErrorType(), input, hasErrors: true)));\n", "-                        continue;\n", "                     }\n", "-                    tests.Add(MakeTestsAndBindings(currentInput, pattern, bindings));\n", "+                    else\n", "+                    {\n", "+                        tests.Add(MakeTestsAndBindings(currentInput, pattern, bindings));\n", "+                    }\n", "                 }\n", "             }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>24  ```suggestion // Routing exceptions MissingRouteException::class => 404, ```</strong></li><pre>@@ -113,6 +113,7 @@ class ExceptionRenderer implements ExceptionRendererInterface\n", "         RecordNotFoundException::class => 404,\n", "         // Http exceptions\n", "         MissingControllerException::class => 404,\n", "+        // Routing exceptions\n", "         MissingRouteException::class => 404,\n", "     ];\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>25  No, we should also check if we are valid of `f2` and use that</strong></li><pre>@@ -81,6 +81,10 @@ namespace Voron.Impl.FileHeaders\n", "                 var hash = CalculateFileHeaderHash(f1);\n", "                 if (f1->Hash != hash)\n", "                     throw new InvalidDataException($\"Invalid hash for FileHeader with TransactionId {f1->TransactionId}, possible corruption. Expected hash to be {f1->Hash} but was {hash}\");\n", "+\n", "+                hash = CalculateFileHeaderHash(f2);\n", "+                if (f2->Hash != hash)\n", "+                    throw new InvalidDataException($\"Invalid hash for FileHeader with TransactionId {f2->TransactionId}, possible corruption. Expected hash to be {f2->Hash} but was {hash}\");\n", "                 \n", "                 if (f1->Version != Constants.CurrentVersion)\n", "                     throw new InvalidDataException($\"The db file is for version {f1->Version}, which is not compatible with the current version {Constants.CurrentVersion}\");\n", "</pre><hr class=\"wide-line\"><li><strong>26  can you move the declaration outside of the loop?</strong></li><pre>@@ -107,8 +107,8 @@ func createSCQuery(request *VMValueRequest) (*process.SCQuery, error) {\n", " \t}\n", " \n", " \targuments := make([][]byte, len(request.Args))\n", "+\tvar argBytes []byte\n", " \tfor i, arg := range request.Args {\n", "-\t\tvar argBytes []byte\n", " \t\targBytes, err = hex.DecodeString(arg)\n", " \t\tif err != nil {\n", " \t\t\treturn nil, fmt.<PERSON><PERSON>(\"'%s' is not a valid hex string: %s\", arg, err.<PERSON><PERSON>r())\n", "</pre><hr class=\"wide-line\"><li><strong>27  Why was this changed? This will certainly introduce a bug somewhere if we accept this. We should be passing along the flags since they are commonly used to flag behaviors during the onChange event loop.</strong></li><pre>@@ -192,7 +192,7 @@ export default class RadioComponent extends Field {\n", "       && this.previousValue === this.currentValue;\n", "     if (shouldResetValue) {\n", "       this.resetValue();\n", "-      this.trigger<PERSON><PERSON>e();\n", "+      this.triggerChange(flags);\n", "     }\n", "     this.previousValue = this.dataValue;\n", "     return changed;\n", "</pre><hr class=\"wide-line\"><li><strong>28  @NielsCharlier is this a resource leak (if print throws)? Does it need a try-with-resource block?</strong></li><pre>@@ -134,9 +134,9 @@ public class SpatialFile extends FileData {\n", "             }\n", "             if (epsgCrs != null) {\n", "                 String epsgWKT = epsgCrs.toWKT();\n", "-                final PrintStream printStream = new PrintStream(getPrjFile().out());\n", "-                printStream.print(epsgWKT);\n", "-                printStream.close();\n", "+                try (PrintStream printStream = new PrintStream(getPrjFile().out())) {\n", "+                    printStream.print(epsgWKT);\n", "+                }\n", "             }\n", "         }\n", "         catch (FactoryException e) {\n", "</pre><hr class=\"wide-line\"><li><strong>29  this one seems to Loose Regex, any better more accurate regex?</strong></li><pre>@@ -1022,7 +1022,7 @@ var guestIssues = []match{\n", " \t\t\tAdvice:   \"Your minikube certs likely expired, as a workaround, clear your minikube home dir `minikube delete --all --purge`\",\n", " \t\t\tIssues:   []int{10948},\n", " \t\t},\n", "-\t\tRegexp: re(`controlPlane never updated to`),\n", "+\t\tRegexp: re(`wait 6m0s for node: wait for healthy API server: controlPlane never updated to`),\n", " \t},\n", " \t{\n", " \t\tKind: Kind{\n", "</pre><hr class=\"wide-line\"><li><strong>30  What happens if `minEndTime < now()`? I think we'd want the thread to wake up immediately and begin processing timed out futures, is this the case?</strong></li><pre>@@ -461,7 +461,6 @@ void ProcessGroupAgent::pollTimedOutRPCs() {\n", "     if (!rpcRunning_.load()) {\n", "       return;\n", "     }\n", "-    std::chrono::milliseconds sleepTime;\n", "     // Estimate amount of time the first future will time out in, and sleep\n", "     // for that long.\n", "     // if there are no futures or the first future's RPC timeout is set to 0\n", "</pre><hr class=\"wide-line\"><li><strong>31  Missing size_t cast after (int)</strong></li><pre>@@ -65,7 +65,7 @@ static int r2k__close(RIODesc *fd) {\n", " \t}\n", " #elif defined (__linux__) && !defined (__GNU__)\n", " \tif (fd) {\n", "-\t\tclose ((int)fd->data);\n", "+\t\tclose ((int)(size_t)fd->data);\n", " \t}\n", " #else\n", " \teprintf (\"TODO: r2k not implemented for this plataform.\\n\");\n", "</pre><hr class=\"wide-line\"><li><strong>32  So globRoot is guaranteed to not be null as `ErrorUtilities.VerifyThrowArgumentNull` is called just before this. The change isn't necessary and adds an extra stack frame to what can stay as a simple string check. If anything this should (and all other uses of `X == string.Empty`) be replaced with `globRoot.Length == 0`</strong></li><pre>@@ -171,7 +171,7 @@ namespace Microsoft.Build.Globbing\n", "             ErrorUtilities.VerifyThrowArgumentNull(fileSpec, nameof(fileSpec));\n", "             ErrorUtilities.VerifyThrowArgumentInvalidPath(globRoot, nameof(globRoot));\n", " \n", "-            if (string.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(globRoot))\n", "+            if (globRoot.Length == 0)\n", "             {\n", "                 globRoot = Directory.GetCurrentDirectory();\n", "             }\n", "</pre><hr class=\"wide-line\"><li><strong>33  You can also consider something like ``` private static final NON_GROUP_COMPARATOR = Comparator.comparing(Endpoint::host).thenComparing(e -> e.ipAddr, IP_ADDR_COMPARATOR).thenComparing(e -> e.port); return NON_GROUP_COMPARATOR.compare(this, that); ```</strong></li><pre>@@ -446,11 +446,7 @@ public final class Endpoint implements Comparable<Endpoint> {\n", "             if (that.isGroup()) {\n", "                 return 1;\n", "             } else {\n", "-                return ComparisonChain.start()\n", "-                                      .compare(host(), that.host())\n", "-                                      .compare(ipAddr, that.ipAddr, IP_ADDR_COMPARATOR)\n", "-                                      .compare(port, that.port)\n", "-                                      .result();\n", "+                return NON_GROUP_COMPARATOR.compare(this, that);\n", "             }\n", "         }\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>34  In the case of version probing, we send an old assignment back. If we encode the new `tasksByHostState` instead of `partitionsByHostState` wouldn't the instances that are not upgrade yet crash? Or course, to be future prove, eg, we bump the version from 5 to 6, it might be ok for a 5 to 6 upgrade to send the new encoding. We need to make the cut-off point base on the smallest version we received.</strong></li><pre>@@ -678,7 +678,7 @@ public class StreamsPartitionAssignor implements PartitionAssignor, Configurable\n", " \n", "         final Map<String, Assignment> assignment;\n", "         if (versionProbing) {\n", "-            assignment = versionProbingAssignment(clientMetadataMap, partitionsForTask, tasksByHostState, futureConsumers, minReceivedMetadataVersion);\n", "+            assignment = versionProbingAssignment(clientMetadataMap, partitionsForTask, partitionsByHostState, futureConsumers, minReceivedMetadataVersion);\n", "         } else {\n", "             assignment = computeNewAssignment(clientMetadataMap, partitionsForTask, tasksByHostState, minReceivedMetadataVersion);\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>35  Don't abbreviate variable names. The names should be concise but clear, otherwise it makes the code hard to read. In this specific case, I would remove these intermediate variables altogether: ``` java DOUBLE.writeDouble(out, Math.exp(state.getDouble() / count)); ```</strong></li><pre>@@ -60,10 +60,7 @@ public final class GeometricAverageAggregations\n", "             out.append<PERSON>ull();\n", "         }\n", "         else {\n", "-            double value = state.getDouble();\n", "-            double lngavg = value / count;\n", "-            double gavg = Math.exp(lngavg);\n", "-            DOUBLE.writeDouble(out, gavg);\n", "+            DOUBLE.writeDouble(out, Math.exp(state.getDouble() / count));\n", "         }\n", "     }\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>36  Why was this change made?</strong></li><pre>@@ -49,8 +49,5 @@ func (mr *mergeResolver) Iterate(ctx context.Context, cb func(File) error, stopB\n", " \tif err := mr1.WriteTo(w); err != nil {\n", " \t\treturn err\n", " \t}\n", "-\tif err := w.Close(); err != nil {\n", "-\t\treturn err\n", "-\t}\n", "-\treturn nil\n", "+\treturn w.<PERSON>()\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>37  why is the modifier \"package\", better to change to private</strong></li><pre>@@ -68,7 +68,7 @@ public final class CarbonProperties {\n", "   /**\n", "    * Boolean type properties default value\n", "    */\n", "-  Map<String, String> booleanProperties = new ConcurrentHashMap<>();\n", "+  private Map<String, String> booleanProperties = new ConcurrentHashMap<>();\n", " \n", "   /**\n", "    * Private constructor this will call load properties method to load all the\n", "</pre><hr class=\"wide-line\"><li><strong>38  `__call__` shouldn't change the state of the object (unless when caching, for instance). In this case, imagine you have ``` fss = SelectBestFeatures(k=0.5) data1 = <some data set with 10 attributes> data2 = <some data set with 100 attributes> fss(data1) # this sets fss.k to 5! fss(data2) # and so this selects just 5 instead of 50 attributes ``` One option is to introduce ``` effective_k = min(n_attrs - idx_attr + 1, n_attrs) else: effective_k = self.k ``` and replace further occurrences of `self.k` in this method with `effective_k`.</strong></li><pre>@@ -55,7 +55,9 @@ class SelectBestFeatures(Reprable):\n", "         if isinstance(self.k, float):\n", "             idx_attr = np.ceil(self.k * n_attrs).astype(int)\n", "             # edge case: 0th percentile would result in selection of `(n_attrs + 1)` attrs\n", "-            self.k = min(n_attrs - idx_attr + 1, n_attrs)\n", "+            effective_k = min(n_attrs - idx_attr + 1, n_attrs)\n", "+        else:\n", "+            effective_k = self.k\n", " \n", "         method = self.method\n", "         # select default method according to the provided data\n", "</pre><hr class=\"wide-line\"><li><strong>39  We can remove the SERVER_CONNECTOR_TEST_PORT and move it to the Listener</strong></li><pre>@@ -30,6 +30,4 @@ public class TestConstant {\n", "     //HTTP2 related Constants\n", "     public static final long HTTP2_RESPONSE_TIME_OUT = 10;\n", "     public static final TimeUnit HTTP2_RESPONSE_TIME_UNIT = TimeUnit.SECONDS;\n", "-    //Default HTTP2 port of the server\n", "-    public static final int SERVER_CONNECTOR_TEST_PORT = 9092;\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>40  This annotation seems unnecessary (partial types do their thing here).</strong></li><pre>@@ -1456,7 +1456,7 @@ class TypeChecker(NodeVisitor[None], CheckerPluginInterface):\n", "         # lvalue had a type defined; this is handled by other\n", "         # parts, and all we have to worry about in that case is\n", "         # that lvalue is compatible with the base class.\n", "-        compare_node = None  # type: Optional[Node]\n", "+        compare_node = None\n", "         if lvalue_type:\n", "             compare_type = lvalue_type\n", "             compare_node = lvalue.node\n", "</pre><hr class=\"wide-line\"><li><strong>41  baout -> about</strong></li><pre>@@ -69,7 +69,7 @@ var CardsView = (function() {\n", " \n", "       // I assume that object properties are enumerated in\n", "       // the same order they were defined.\n", "-      // There is nothing baout that in spec, but I've never\n", "+      // There is nothing about that in spec, but I've never\n", "       // seen any unexpected behavior.\n", "       sortable.forEach(function(element) {\n", "         runningApps[element.origin] = element.app;\n", "</pre><hr class=\"wide-line\"><li><strong>42  Might be nicer if there was a \"DoFnSignatures.getSignature(...).isStateful()\" or something to easily categorize these?</strong></li><pre>@@ -961,8 +961,6 @@ public class DataflowPipelineTranslator {\n", "           private <InputT, OutputT> void translateMultiHelper(\n", "               ParDo.BoundMulti<InputT, OutputT> transform,\n", "               TranslationContext context) {\n", "-            rejectStatefulDoFn(transform.getNewFn());\n", "-\n", "             context.addStep(transform, \"ParallelDo\");\n", "             translateInputs(context.getInput(transform), transform.getSideInputs(), context);\n", "             BiMap<Long, TupleTag<?>> outputMap =\n", "</pre><hr class=\"wide-line\"><li><strong>43  site may not be always initiated when this is called. So important to check `if getattr(frappe.local, \"site\", None):`</strong></li><pre>@@ -213,9 +213,11 @@ def set_filters(jenv):\n", " \n", " def get_jenv_customization(customizable_type):\n", " \timport frappe\n", "-\tfor app in frappe.get_installed_apps():\n", "-\t\tfor jenv_customizable, jenv_customizable_definition in frappe.get_hooks(app_name=app).get(\"jenv\", {}).items():\n", "-\t\t\tif customizable_type == jenv_customizable:\n", "-\t\t\t\tfor data in jenv_customizable_definition:\n", "-\t\t\t\t\tsplit_data = data.split(\":\")\n", "-\t\t\t\t\tyield split_data[0], split_data[1]\n", "+\n", "+\tif getattr(frappe.local, \"site\", None):\n", "+\t\tfor app in frappe.get_installed_apps():\n", "+\t\t\tfor jenv_customizable, jenv_customizable_definition in frappe.get_hooks(app_name=app).get(\"jenv\", {}).items():\n", "+\t\t\t\tif customizable_type == jenv_customizable:\n", "+\t\t\t\t\tfor data in jenv_customizable_definition:\n", "+\t\t\t\t\t\tsplit_data = data.split(\":\")\n", "+\t\t\t\t\t\tyield split_data[0], split_data[1]\n", "</pre><hr class=\"wide-line\"><li><strong>44  add `job.FinishTableJob(model.JobStateRollbackDone, model.StatePublic, ver, tblInfo)` ?</strong></li><pre>@@ -89,8 +89,7 @@ func rollbackDropTable(t *meta.Meta, job *model.Job, schemaID int64, tblInfo *mo\n", " \tif err != nil {\n", " \t\treturn ver, errors.<PERSON>(err)\n", " \t}\n", "-\tjob.State = model.JobStateRollbackDone\n", "-\tjob.SchemaState = tblInfo.State\n", "+\tjob.FinishTableJob(model.JobStateRollbackDone, model.StatePublic, ver, tblInfo)\n", " \treturn ver, nil\n", " }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>45  Let's not add this field for datasets which do not have release note yet.</strong></li><pre>@@ -93,9 +93,6 @@ class MNIST(tfds.core.GeneratorBasedBuilder):\n", " \n", "   VERSION = tfds.core.Version(\"3.0.1\")\n", " \n", "-  #Add future release notes for all dataset versions here\n", "-  RELEASE_NOTES = {}\n", "-  \n", "   def _info(self):\n", "     return tfds.core.DatasetInfo(\n", "         builder=self,\n", "</pre><hr class=\"wide-line\"><li><strong>46  In my understanding, #2990 is happened with `linkcheck_anchors` option. But it seems `test-linkcheck` project does not define it. Does this really test the problem? If you remove the `decode_unicode` parameter, the problem will be reproduced by this? Could you check that please?</strong></li><pre>@@ -1,3 +1,4 @@\n", " master_doc = 'links'\n", " source_suffix = '.txt'\n", " exclude_patterns = ['_build']\n", "+linkcheck_anchors = True\n", "</pre><hr class=\"wide-line\"><li><strong>47  Move constants at the top of the class, where they should be</strong></li><pre>@@ -41,11 +41,12 @@ import net.runelite.client.ui.overlay.*;\n", " @Singleton\n", " public class PrayerBarOverlay extends Overlay\n", " {\n", "-\tprivate final Client client;\n", "-\tprivate final PrayerBarConfig config;\n", " \tprivate static final Color BAR_FILL_COLOR = Color.cyan;\n", " \tprivate static final Color BAR_BG_COLOR = Color.white;\n", " \tprivate static final Dimension PRAYER_BAR_SIZE = new Dimension(30, 5); // 30x5 is the size of health bars\n", "+\n", "+\tprivate final Client client;\n", "+\tprivate final PrayerBarConfig config;\n", " \tprivate Player localPlayer;\n", " \tprivate boolean showPrayerBar = true;\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>48  What if we modified information from the an existing uuid (like surname)? Or this PR is just about adding/removing new elements and we will do that in a follow up?</strong></li><pre>@@ -238,7 +238,13 @@ contacts.List = (function() {\n", "       if (alreadyRendered) {\n", "         // If already rendered, don't do anything unless one has been removed\n", "         // We check that comparing contact.id\n", "-        if (nodes[index] && nodes[index].dataset['uuid'] != contact.id) {\n", "+        var currentNode = nodes[index];\n", "+        var itemBody = currentNode.querySelector('[data-search]');\n", "+        var searchable = itemBody.dataset['search'];\n", "+        var newItemBody = newContact.querySelector('[data-search]');\n", "+        var newSearchable = newItemBody.dataset['search'];\n", "+        var hasChanged = searchable != newSearchable;\n", "+        if (currentNode.dataset['uuid'] != contact.id || hasChanged) {\n", "           resetGroup(list<PERSON><PERSON><PERSON>, counter[group]);\n", "           listContainer.appendChild(newContact);\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>49  Just occurred to me: there should be another variant of this test where the user chooses <PERSON> in the confirmation dialog, but then Cancels the Save dialog (the testWindow.brackets.fs.showSaveDialog() dialog).</strong></li><pre>@@ -151,15 +151,7 @@ define(function (require, exports, module) {\n", "                 });\n", " \n", "                 runs(function () {\n", "-                    var noLongerUntitledDocument = DocumentManager.getCurrentDocument();\n", "-\n", "-                    expect(noLongerUntitledDocument.isDirty).toBe(false);\n", "-                    expect(noLongerUntitledDocument.isUntitled()).toBe(false);\n", "-                    expect(noLongerUntitledDocument.file.fullPath).toEqual(newFilePath);\n", "-                    expect(DocumentManager.findInWorkingSet(newFilePath)).toBeGreaterThan(-1);\n", "-\n", "-                    promise = SpecRunnerUtils.deletePath(newFilePath);\n", "-                    waitsForDone(promise, \"Remove the testfile\");\n", "+                    expect(DocumentManager.getWorkingSet().length).toEqual(0);\n", "                 });\n", "             });\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>50  @nadeeshaan can you please verify this change again? Looks incorrect.</strong></li><pre>@@ -143,7 +143,7 @@ class BallerinaTextDocumentService implements TextDocumentService {\n", "                     position.getPosition());\n", "             try {\n", "                 return LangExtensionDelegator.instance()\n", "-                        .completion(position, context, this.serverContext, null);\n", "+                        .completion(position, context, this.serverContext, cancelChecker);\n", "             } catch (CancellationException ignore) {\n", "                 // Ignore the cancellation exception\n", "             } catch (Throwable e) {\n", "</pre><hr class=\"wide-line\"><li><strong>51  `DefaultStoreTest` was failing because it clears the MDS before every run, so the _default_ namespace gets deleted, hence deploy returns `null`. Fixed that temporarily with a TODO.</strong></li><pre>@@ -294,14 +294,13 @@ public class TestBase {\n", "         }\n", "       }\n", "     );\n", "+\n", "     txService = injector.getInstance(TransactionManager.class);\n", "     txService.startAndWait();\n", "     dsOpService = injector.getInstance(DatasetOpExecutor.class);\n", "     dsOpService.startAndWait();\n", "     datasetService = injector.getInstance(DatasetService.class);\n", "     datasetService.startAndWait();\n", "-    namespaceService = injector.getInstance(NamespaceService.class);\n", "-    namespaceService.startAndWait();\n", "     metricsQueryService = injector.getInstance(MetricsQueryService.class);\n", "     metricsQueryService.startAndWait();\n", "     metricsCollectionService = injector.getInstance(MetricsCollectionService.class);\n", "</pre><hr class=\"wide-line\"><li><strong>52  We don't support partitions for views. So let's remove the `expectView` parameter.</strong></li><pre>@@ -1851,15 +1851,11 @@ public class HiveParserDDLSemanticAnalyzer {\n", "             }\n", "         }\n", " \n", "-        validateAlterTableType(tab, AlterTableDesc.AlterTableTypes.DROPPARTITION, expectView);\n", "+        validateAlterTableType(tab, AlterTableDesc.AlterTableTypes.DROPPARTITION);\n", " \n", "         ObjectIdentifier tableIdentifier =\n", "                 catalogManager.qualifyIdentifier(\n", "                         UnresolvedIdentifier.of(qualified[0], qualified[1]));\n", "-        CatalogBaseTable catalogBaseTable = getCatalogBaseTable(tableIdentifier);\n", "-        if (catalogBaseTable instanceof CatalogView) {\n", "-            throw new ValidationException(\"DROP PARTITION for a view is not supported\");\n", "-        }\n", "         List<CatalogPartitionSpec> specs =\n", "                 partSpecs.stream().map(CatalogPartitionSpec::new).collect(Collectors.toList());\n", "         return new DropPartitionsOperation(tableIdentifier, ifExists, specs);\n", "</pre><hr class=\"wide-line\"><li><strong>53  We can throw ErrorValue? It was recommended to be used if I am not mistaken.</strong></li><pre>@@ -77,7 +77,7 @@ public class StartServiceSkeleton extends BlockingNativeCallableUnit {\n", " \n", "         try {\n", "             // TODO: find how to give the service name in to service generation function.\n", "-            generator.generateService(userDir, openApiFilePath, \"\", rootDir.toString());\n", "+            generator.generateService(userDir, openApiFilePath, \"\", \"\", rootDir.toString());\n", "         } catch (IOException | BallerinaOpenApiException e) {\n", "             throw new BallerinaException(String.format(\"Service skeleton creation failed. Failed to generate the \"\n", "                     + \"service from the [OpenApi file] %s [cause] %s\", openApiFilePath, e.getMessage()), e);\n", "</pre><hr class=\"wide-line\"><li><strong>54  The second condition here is checking \"dev mode\". We had problems with strange settings like this before. Maybe just rely on having EnableDiagnostic disabled? You might get some bad diagnostics from external devs but those would be easy to filter out. Or at least move this check to a `isNonOfficalBuild()` function or something similar.</strong></li><pre>@@ -78,7 +78,7 @@ func main() {\n", " func diagnosticsJob() {\n", " \tgo func() {\n", " \t\tfor {\n", "-\t\t\tif utils.Cfg.PrivacySettings.EnableDiagnostic && model.BuildNumber != \"_BUILD_NUMBER_\" {\n", "+\t\t\tif utils.Cfg.PrivacySettings.EnableDiagnostic && !model.IsOfficalBuild() {\n", " \t\t\t\tif result := <-api.Srv.Store.System().Get(); result.Err == nil {\n", " \t\t\t\t\tprops := result.Data.(model.StringMap)\n", " \t\t\t\t\tlastTime, _ := strconv.ParseInt(props[\"LastDiagnosticTime\"], 10, 0)\n", "</pre><hr class=\"wide-line\"><li><strong>55  Might need a tweak, something like `An application/json parameter.`?</strong></li><pre>@@ -59,7 +59,7 @@ public class NameValuePair {\n", "      */\n", "     public static final int TYPE_MULTIPART_DATA_FILE_CONTENTTYPE = 36;\n", "     /**\n", "-     * The content-type portion of a application/json file parameter\n", "+     * The application/json content-type of a web application\n", "      * \n", "      * @since TODO add version\n", "      */   \n", "</pre><hr class=\"wide-line\"><li><strong>56  Hm -- maybe say contributors_summary.keys() instead? Seems more specific. Nice simplification, btw!</strong></li><pre>@@ -1003,7 +1003,7 @@ def compute_summary_of_collection(collection, contributor_id_to_add):\n", "     elif contributor_id_to_add not in constants.SYSTEM_USER_IDS:\n", "         contributors_summary[contributor_id_to_add] = (\n", "             contributors_summary.get(contributor_id_to_add, 0) + 1)\n", "-    contributor_ids = list(contributors_summary)\n", "+    contributor_ids = list(contributors_summary.keys())\n", " \n", "     collection_model_last_updated = collection.last_updated\n", "     collection_model_created_on = collection.created_on\n", "</pre><hr class=\"wide-line\"><li><strong>57  why are you ignoring all but the first?</strong></li><pre>@@ -3,6 +3,8 @@ import ipaddress\n", " # Local imports\n", " from CommonServerPython import *\n", " \n", "+# The following script check if given IPv4 CIDR availble addresses is lower from a given number.\n", "+\n", " \n", " def cidr_network_addresses_lower_from_const(ip_cidr: str, max_num_addresses: str) -> bool:\n", "     \"\"\" Decide if num_adddresses const is lower than availble addresses in IPv4 or IPv6 cidr\n", "</pre><hr class=\"wide-line\"><li><strong>58  [nit] Not sure if you need the `<>` here.</strong></li><pre>@@ -140,9 +140,8 @@ const EditGasItem = ({ priorityLevel }) => {\n", "       <span\n", "         className={`edit-gas-item__time-estimate edit-gas-item__time-estimate-${priorityLevel}`}\n", "       >\n", "-        {editGasMode !== EDIT_GAS_MODES.SWAPS && (\n", "-          <>{minWaitTime ? toHumanReadableTime(t, minWaitTime) : '--'}</>\n", "-        )}\n", "+        {editGasMode !== EDIT_GAS_MODES.SWAPS &&\n", "+          (minWaitTime ? toHumanReadableTime(t, minWaitTime) : '--')}\n", "       </span>\n", "       <span\n", "         className={`edit-gas-item__fee-estimate edit-gas-item__fee-estimate-${priorityLevel}`}\n", "</pre><hr class=\"wide-line\"><li><strong>59  \"alpha\" needed ?</strong></li><pre>@@ -174,7 +174,7 @@ $(document).ready(function() {\n", " $now=dol_now();\n", " \n", " $search_ref = GETPOST(\"search_ref\");\n", "-$search_refcustomer=GETPOST('search_refcustomer','alpha');\n", "+$search_refcustomer=GETPOST('search_refcustomer');\n", " $search_societe = GETPOST(\"search_societe\");\n", " $search_montant_ht = GETPOST(\"search_montant_ht\");\n", " $search_montant_ttc = GETPOST(\"search_montant_ttc\");\n", "</pre><hr class=\"wide-line\"><li><strong>60  What do you mean by \"test escape\"?</strong></li><pre>@@ -1645,7 +1645,6 @@ func (s *testSuite) TestLoadDataIgnoreLines(c *C) {\n", " \t<PERSON><PERSON>(ok, IsTrue)\n", " \tdefer ctx.<PERSON><PERSON><PERSON><PERSON>(executor.LoadData<PERSON>, nil)\n", " \t<PERSON><PERSON>(ld, NotNil)\n", "-\t// test escape\n", " \ttests := []testCase{\n", " \t\t{nil, []byte(\"1\\tline1\\n2\\tline2\\n\"), []string{\"2|line2\"}, nil},\n", " \t\t{nil, []byte(\"1\\tline1\\n2\\tline2\\n3\\tline3\\n\"), []string{\"2|line2\", \"3|line3\"}, nil},\n", "</pre><hr class=\"wide-line\"><li><strong>61  Is `file` a better name?</strong></li><pre>@@ -244,12 +244,12 @@ public class CPlatformWindow extends CFRetainedResource implements PlatformWindo\n", "             c.execute(ptr -> nativeRevalidateNSWindowShadow(ptr));\n", "         }},\n", "         new Property<CPlatformWindow>(WINDOW_DOCUMENT_FILE) { public void applyProperty(final CPlatformWindow c, final Object value) {\n", "-            if (!(value instanceof java.io.File f)) {\n", "+            if (!(value instanceof java.io.File file)) {\n", "                 c.execute(ptr->nativeSetNSWindowRepresentedFilename(ptr, null));\n", "                 return;\n", "             }\n", " \n", "-            final String filename = f.getAbsolutePath();\n", "+            final String filename = file.getAbsolutePath();\n", "             c.execute(ptr->nativeSetNSWindowRepresentedFilename(ptr, filename));\n", "         }},\n", "         new Property<CPlatformWindow>(WINDOW_FULL_CONTENT) {\n", "</pre><hr class=\"wide-line\"><li><strong>62  Please change this to ```python print(\"*. Displaying log messages only from the \") ```</strong></li><pre>@@ -120,7 +120,7 @@ if args.debug:\n", "     print(\"0x. Switching debug mode on\")\n", "     os.environ[\"SPYDER_DEBUG\"] = \"3\"\n", "     if len(args.filter_log) > 0:\n", "-        print(\"0x-1. Displaying log messages only from the \"\n", "+        print(\"*. Displaying log messages only from the \"\n", "               \"following modules: {0}\".format(', '.join(args.filter_log)))\n", "     os.environ[\"SPYDER_FILTER_LOG\"] = ' '.join(args.filter_log)\n", "     # this way of interaction suxx, because there is no feedback\n", "</pre><hr class=\"wide-line\"><li><strong>63  8? other have 9</strong></li><pre>@@ -593,7 +593,7 @@ namespace Tizen.Network.Bluetooth\n", "         /// The ServiceChanged event is raised when the service is changed from the remote device(GATT server).\n", "         /// </summary>\n", "         /// <feature>http://tizen.org/feature/network.bluetooth.le.gatt.client</feature>\n", "-        /// <since_tizen> 8 </since_tizen>\n", "+        /// <since_tizen> 9 </since_tizen>\n", "         public event EventHandler<ServiceChangedEventArgs> ServiceChanged;\n", " \n", "         internal bool Isvalid()\n", "</pre><hr class=\"wide-line\"><li><strong>64  Can we modify the message of the exception to be more specific? That would help a lot in debugging</strong></li><pre>@@ -417,8 +417,9 @@ public class SinkConfigUtils {\n", "                     throw new IllegalArgumentException(\"Input Topics cannot be altered\");\n", "                 }\n", "                 if (consumerConfig.isRegexPattern() != existingConfig.getInputSpecs().get(topicName).isRegexPattern()) {\n", "-                    throw new IllegalArgumentException(\"Input Specs mismatch\");\n", "+                    throw new IllegalArgumentException(\"isRegexPattern for input topic \" + topicName + \" cannot be altered\");\n", "                 }\n", "+                mergedConfig.getInputSpecs().put(topicName, consumerConfig);\n", "             });\n", "         }\n", "         if (newConfig.getProcessingGuarantees() != null && !newConfig.getProcessingGuarantees().equals(existingConfig.getProcessingGuarantees())) {\n", "</pre><hr class=\"wide-line\"><li><strong>65  this code should be removed, this file doesn't need to be touched.</strong></li><pre>@@ -244,12 +244,6 @@ func (h *handler) assertAdminOnly() {\n", " \t}\n", " }\n", " \n", "-func (h *handler) assertNotCors() {\n", "-\tif h.response.Header().Get(\"Access-Control-Allow-Origin\") != \"\" {\n", "-\t\t\n", "-\t}\n", "-}\n", "-\n", " func (h *handler) PathVar(name string) string {\n", " \tv := mux.Vars(h.rq)[name]\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>66  Thanks for the patch! In the stdlib we try to follow the guidelines laid out in PEP 257. Mainly, the first line of a docstring should be a very short description that stands alone, and more information follows after a blank line (this is very similar to the recommendations for git commit messages). Taking inspiration from the rst docs, here the first line could be something like `PurePath subclass for non-Windows systems.`, followed by a blank line then your original text.</strong></li><pre>@@ -939,7 +939,9 @@ os.PathLike.register(PurePath)\n", " \n", " \n", " class PurePosixPath(PurePath):\n", "-    \"\"\"On a POSIX system, instantiating a PurePath should return this object.\n", "+    \"\"\"PurePath subclass for non-Windows systems.\n", "+\n", "+    On a POSIX system, instantiating a PurePath should return this object.\n", "     However, you can also instantiate it directly on any system.\n", "     \"\"\"\n", "     _flavour = _posix_flavour\n", "</pre><hr class=\"wide-line\"><li><strong>67  We should still use the canonical URL.</strong></li><pre>@@ -1,7 +1,7 @@\n", " class Leptonica < Formula\n", "   desc \"Image processing and image analysis library\"\n", "   homepage \"http://www.leptonica.org/\"\n", "-  url \"https://github.com/DanBloomberg/leptonica/releases/download/1.74.4/leptonica-1.74.4.tar.gz\"\n", "+  url \"http://www.leptonica.org/source/leptonica-1.74.4.tar.gz\"\n", "   sha256 \"29c35426a416bf454413c6fec24c24a0b633e26144a17e98351b6dffaa4a833b\"\n", " \n", "   bottle do\n", "</pre><hr class=\"wide-line\"><li><strong>68  [optional] `op->getResultTypes()[0]` is cleaner to me</strong></li><pre>@@ -552,7 +552,7 @@ struct FlattenMemRefSubspanPass\n", "     target.addDynamicallyLegalOp<IREE::HAL::InterfaceBindingSubspanOp,\n", "                                  memref::AllocaOp, memref::AllocOp,\n", "                                  memref::GetGlobalOp>([](Operation *op) {\n", "-      return isRankZeroOrOneMemRef(op->getResult(0).getType());\n", "+      return isRankZeroOrOneMemRef(op->getResultTypes().front());\n", "     });\n", "     target.addDynamicallyLegalOp<memref::GlobalOp>(\n", "         [](memref::GlobalOp op) { return isRankZeroOrOneMemRef(op.type()); });\n", "</pre><hr class=\"wide-line\"><li><strong>69  nit: GoDocs should be complete sentences starting with the name of the object they describe.</strong></li><pre>@@ -25,7 +25,7 @@ import (\n", " \t\"github.com/apache/trafficcontrol/traffic_ops/traffic_ops_golang/dbhelpers\"\n", " )\n", " \n", "-//we need a type alias to define functions on\n", "+// TOCDNConf used as a type alias to define functions on to satisfy shared API REST interfaces.\n", " type TOCDNConf struct {\n", " \tapi.APIInfoImpl `json:\"-\"`\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>70  remove redundant space</strong></li><pre>@@ -4,8 +4,6 @@ import json\n", " import string\n", " import random\n", " import argparse\n", "-from subprocess import check_output\n", "-\n", " import requests\n", " \n", " import demisto\n", "</pre><hr class=\"wide-line\"><li><strong>71  i wonder if this means IsYAMLManifest should be a method whether than a field now, since it's now equivalent to len(ResourceInfo) > 0</strong></li><pre>@@ -485,7 +485,7 @@ func StateToView(s EngineState) view.View {\n", " \t\t\t\t},\n", " \t\t\t},\n", " \t\t\tLastDeployTime: s.GlobalYAMLState.LastSuccessfulApplyTime,\n", "-\t\t\tResourceInfo: view.YamlResourceInfo{\n", "+\t\t\tResourceInfo: view.YAMLResourceInfo{\n", " \t\t\t\tK8sResources: s.GlobalYAML.Resources(),\n", " \t\t\t},\n", " \t\t\tIsYAMLManifest: true,\n", "</pre><hr class=\"wide-line\"><li><strong>72  Please remove these warnings. They are not part of linters that the project uses.</strong></li><pre>@@ -30,7 +30,6 @@ PLATFORM_SCHEMA = PLATFORM_SCHEMA.extend({\n", " \n", " \n", " # noinspection PyUnusedLocal\n", "-# noinspection PyUnresolvedLocal\n", " def setup_platform(hass, config, add_devices, discovery_info=None):\n", "     \"\"\"Set up the component.\"\"\"\n", "     # Get options\n", "</pre><hr class=\"wide-line\"><li><strong>73  I'm not sure if suppressing the new IllegalStateException and IOException from getVaadinHomeDirectory is a good idea here. (probably just a left over from before)</strong></li><pre>@@ -495,6 +495,10 @@ public class FrontendUtils {\n", "                         .orElseGet(() -> frontendToolsLocator.tryLocateTool(cmd)\n", "                                 .or<PERSON><PERSON><PERSON>(null));\n", "             }\n", "+        } catch (FileNotFoundException exception) {\n", "+            Throwable cause = exception.getCause();\n", "+            assert cause != null;\n", "+            throw new IllegalStateException(cause);\n", "         } catch (Exception e) { // NOSONAR\n", "             // There are IOException coming from process fork\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>74  Error return value of `data.Read` is not checked</strong></li><pre>@@ -217,7 +217,10 @@ func (p1p *Phase1Packet) DeserializeWithoutHeader(data io.Reader, header *Packet\n", " \t\tif err != nil {\n", " \t\t\treturn errors.Wrap(err, \"[ Phase1Packet.DeserializeWithoutHeader ] Can't read Section 2\")\n", " \t\t}\n", "-\t\tdata.Read(read)\n", "+\t\t_, err = data.Read(read)\n", "+\t\tif err != nil {\n", "+\t\t\treturn errors.Wrap(err, \"failed to read raw data\")\n", "+\t\t}\n", " \t}\n", " \n", " \tp1p.Signature = make([]byte, SignatureLength)\n", "</pre><hr class=\"wide-line\"><li><strong>75  We might need to make this property public, otherwise the component users can't set initial state.</strong></li><pre>@@ -44,7 +44,8 @@ const metadata = {\n", " \t\t *\n", " \t\t * @type {boolean}\n", " \t\t * @defaultvalue false\n", "-\t\t * @private\n", "+\t\t * @since 1.0.0-rc.13\n", "+\t\t * @public\n", " \t\t */\n", " \t\tselected: {\n", " \t\t\ttype: Boolean,\n", "</pre><hr class=\"wide-line\"><li><strong>76  isn't it possible that `metadata` isn't implemented, on a sufficiently old RubyGems?</strong></li><pre>@@ -92,9 +92,12 @@ module Bundler\n", " \n", "     def rubygem_push(path)\n", "       if Pathname.new(\"~/.gem/credentials\").expand_path.exist?\n", "+        allowed_push_host = nil\n", "         gem_command = \"gem push '#{path}'\"\n", "-        allowed_push_host = @gemspec.metadata[\"allowed_push_host\"]\n", "-        gem_command << \" --host #{allowed_push_host}\" if allowed_push_host\n", "+        if spec.respond_to?(:metadata)\n", "+          allowed_push_host = @gemspec.metadata[\"allowed_push_host\"]\n", "+          gem_command << \" --host #{allowed_push_host}\" if allowed_push_host\n", "+        end\n", "         sh(gem_command)\n", "         Bundler.ui.confirm \"Pushed #{name} #{version} to #{allowed_push_host ? allowed_push_host : \"rubygems.org.\"}\"\n", "       else\n", "</pre><hr class=\"wide-line\"><li><strong>77  I see that there is both an `anon_id` and an `anon_ids` here. Do we need both?</strong></li><pre>@@ -1225,10 +1225,10 @@ HTML;\n", "         return $return . $this->core->getOutput()->renderTwigTemplate(\"grading/electronic/RubricPanel.twig\", [\n", "                 \"showNewInterface\" => $showNewInterface,\n", "                 \"gradeable\" => $gradeable,\n", "-                \"anon_ids\" => $anon_ids,\n", "+                \"student_anon_ids\" => $student_anon_ids,\n", "+                \"anon_id\" => $graded_gradeable->getSubmitter()->getAnonId(),\n", "                 \"gradeable_id\" => $gradeable->getId(),\n", "                 \"is_ta_grading\" => $gradeable->isTaGrading(),\n", "-                \"anon_id\" => $graded_gradeable->getSubmitter()->getAnonId(),\n", "                 \"show_verify_all\" => $show_verify_all,\n", "                 \"can_verify\" => $can_verify,\n", "                 \"grading_disabled\" => $grading_disabled,\n", "</pre><hr class=\"wide-line\"><li><strong>78  ```suggestion \"Consider annotating one of the parameters with '@Payload'.\"); ```</strong></li><pre>@@ -1329,7 +1329,7 @@ public class MessagingMethodInvokerHelper extends AbstractExpressionEvaluator im\n", " \t\t\tif (this.targetParameterTypeDescriptor != null) {\n", " \t\t\t\tthrow new IneligibleMethodException(\"Found more than one parameter type candidate: [\" +\n", " \t\t\t\t\t\tthis.targetParameterTypeDescriptor + \"] and [\" + targetParameterType + \"].\\n\" +\n", "-\t\t\t\t\t\t\"Consider to mark one of the parameter with '@Payload' annotation.\");\n", "+\t\t\t\t\t\t\"Consider annotating one of the parameters with '@Payload'.\");\n", " \t\t\t}\n", " \t\t\tthis.targetParameterTypeDescriptor = targetParameterType;\n", " \t\t\tif (Message.class.isAssignableFrom(targetParameterType.getObjectType())) {\n", "</pre><hr class=\"wide-line\"><li><strong>79  Sorry just noticed this. Can you replace it with a log statement?</strong></li><pre>@@ -823,7 +823,6 @@ public class Snippets {\n", "                       new DoFn<String, String>() {\n", "                         @ProcessElement\n", "                         public void process(@Element String src, OutputReceiver<String> o) {\n", "-                          System.out.println(src);\n", "                           o.output(src);\n", "                         }\n", "                       }))\n", "</pre><hr class=\"wide-line\"><li><strong>80  i think you're going to need to sync and rewrite this bit, because this rendering pipeline has changed a lot</strong></li><pre>@@ -603,10 +603,12 @@ func (s *LogStore) logHelper(spansToLog map[SpanID]*Span, showManifestPrefix boo\n", " \n", " \t\tsb.WriteString(string(segment.Text))\n", " \t\tisFirstLine = false\n", "+\t\tprogressID = segment.Fields[logger.FieldNameProgressID]\n", "+\t\tprogressMustPrint = segment.Fields[logger.FieldNameProgressMustPrint] == \"1\"\n", " \n", " \t\t// If this segment is not complete, run ahead and try to complete it.\n", " \t\tif segment.IsComplete() {\n", "-\t\t\tlastLineCompleted = true\n", "+\t\t\tmaybePushLine()\n", " \t\t\tcontinue\n", " \t\t}\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>81  Hmm... these are actually URLs, so we should actually filter these as absolute URLs (not just strings). The current filter would fail if we had the following, which are equivalent: ```HTML <li data-tests=\" payment-request-show-method.https.html, ./payment-request-show-method.https.html, http://....web platform test url.../payment-request-show-method.https.html\"> ``` So, really, what would be good would be on `testURLs` itself (which holds absolute URLs after all the map()s happen). Also, it would be good to warn the the Editor of duplicate tests, so they can fix that manually too. The filter you wrote is good for finding the duplicates, which could then be displayed in a warning. Then, to get clean list, you can use this nice trick (convert array to set, which removes the duplicates.. then be nice to sort the clean ones alphabetically, because why not  ): ```JS const duplicates = testURLs.filter(/* modified filter from above */); const cleanList = [...new Set(testURLs)].sort(); if (duplicates.length) publish warning... ```</strong></li><pre>@@ -88,7 +88,6 @@ export function run(conf) {\n", "       const renderer = hyperHTML.bind(details);\n", "       const testURLs = elem.dataset.tests\n", "         .split(/,/gm)\n", "-        .filter((links, i, self) => self.indexOf(links) === i)\n", "         .map(url => url.trim())\n", "         .map(url => {\n", "           let href = \"\";\n", "</pre><hr class=\"wide-line\"><li><strong>82  Consider a shallow clone here or downloading a tarball.</strong></li><pre>@@ -12,7 +12,7 @@\n", " # See the License for the specific language governing permissions and\n", " # limitations under the License.\n", " \n", "-\"\"\"Module containing openblas installation and cleanup functions.\"\"\"\n", "+\"\"\"Module containing OpenBLAS installation and cleanup functions.\"\"\"\n", " \n", " OPENBLAS_DIR = 'pkb/OpenBLAS'\n", " GIT_REPO = 'git://github.com/xianyi/OpenBLAS'\n", "</pre><hr class=\"wide-line\"><li><strong>83  1. statics method  removeWithReplies()  * _id ID replyTo ID1 2. async/await </strong></li><pre>@@ -78,9 +78,11 @@ module.exports = function(crowi) {\n", "     }));\n", "   };\n", " \n", "-  commentSchema.statics.removeRepliesByCommentId = function(commentId) {\n", "+  commentSchema.methods.removeWithReplies = function(commentId) {\n", "+    const Comment = crowi.model('Comment');\n", "+\n", "     return new Promise(((resolve, reject) => {\n", "-      this.remove({ replyTo: commentId }, (err, done) => {\n", "+      Comment.remove({ $or: [{ replyTo: commentId }, { _id: commentId }] }, (err, done) => {\n", "         if (err) {\n", "           return reject(err);\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>84  This cast can be removed.</strong></li><pre>@@ -382,7 +382,7 @@ class SemanticAnalyzer(NodeVisitor):\n", "             node = self.lookup_qualified(name, type)\n", "             if node and node.kind == UNBOUND_TVAR:\n", "                 assert isinstance(node.node, TypeVarExpr)\n", "-                result.append((name, cast(TypeVarExpr, node.node)))\n", "+                result.append((name, node.node))\n", "             for arg in type.args:\n", "                 result.extend(self.find_type_variables_in_type(arg))\n", "         elif isinstance(type, TypeList):\n", "</pre><hr class=\"wide-line\"><li><strong>85  I don't think you need the changes to this file?</strong></li><pre>@@ -1,4 +1,3 @@\n", "-require 'fog'\n", " def test\n", "  connection = Fog::Compute.new({ :provider => \"Google\" })\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>86  Do we still need to place a writeLock around this section? What write operation are we performing?</strong></li><pre>@@ -277,7 +277,7 @@ public abstract class AbstractProgramRuntimeService extends AbstractIdleService\n", "   }\n", " \n", "   protected void updateRuntimeInfo(ProgramType type, RunId runId, RuntimeInfo runtimeInfo) {\n", "-    Lock lock = runtimeInfosLock.writeLock();\n", "+    Lock lock = runtimeInfosLock.readLock();\n", "     lock.lock();\n", "     try {\n", "       if (!runtimeInfos.contains(type, runId)) {\n", "</pre><hr class=\"wide-line\"><li><strong>87  Cast to `object` in null check?</strong></li><pre>@@ -1381,7 +1381,7 @@ namespace Microsoft.CodeAnalysis.CSharp\n", "                 TypeSymbol baseType = null;\n", " \n", "                 // For a script class or a submission class base should have no members.\n", "-                if (containingType != null && containingType.Kind == SymbolKind.NamedType && ((NamedTypeSymbol)containingType).IsScriptClass)\n", "+                if ((object)containingType != null && containingType.Kind == SymbolKind.NamedType && ((NamedTypeSymbol)containingType).IsScriptClass)\n", "                 {\n", "                     return ImmutableArray<Symbol>.Empty;\n", "                 }\n", "</pre><hr class=\"wide-line\"><li><strong>88  You don't need `$scope` here. Just put a method on `this` and in the template, use `$ctrl.verifyEmail`.</strong></li><pre>@@ -46,12 +46,6 @@ const EmptyStateComponent = {\n", "         },\n", "       });\n", "     };\n", "-\n", "-    $scope.verifyEmail = () => {\n", "-      $http.get('/send_verification').success((data) => {\n", "-        toastr.success(data);\n", "-      });\n", "-    };\n", "   },\n", " };\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>89  Isn't it more idiomatic to do `cancellationToken.ThrowIfCancellationRequested()`?</strong></li><pre>@@ -469,10 +469,7 @@ namespace Microsoft.AspNetCore.Mvc.Formatters\n", "                 // MvcOptions.MaxIAsyncEnumerableBufferLimit is 8192. Pick some value larger than that.\n", "                 foreach (var i in Enumerable.Range(0, 9000))\n", "                 {\n", "-                    if (cancellationToken.IsCancellationRequested)\n", "-                    {\n", "-                        yield break;\n", "-                    }\n", "+                    cancellationToken.ThrowIfCancellationRequested();\n", "                     iterated = true;\n", "                     yield return i;\n", "                 }\n", "</pre><hr class=\"wide-line\"><li><strong>90  I think we don't need to care some many cases. 1. if data-dir exists, let etcd handle that; 2. else if it join itself, create a new cluster for it; 3. else connect to the joined cluster and do `MemberAdd`, if it cannot connect to the cluster or the peer urls already exist (etcd will detect that), just let it failed.</strong></li><pre>@@ -94,7 +94,7 @@ func memberList(client *clientv3.Client) (*clientv3.MemberListResponse, error) {\n", " //      join does: return \"\" (etcd will connect to other peers and will find\n", " //                 itself has been removed)\n", " //\n", "-//  7. a deleted pd joins to previous cluster\n", "+//  7. a deleted pd joins to previous cluster.\n", " //      join does: return \"\" (as etcd will read data dir and find itself has\n", " //                 been removed, so an empty string is fine.)\n", " func (cfg *Config) prepareJoinCluster() (string, string, error) {\n", "</pre><hr class=\"wide-line\"><li><strong>91  sholdn't we start with a filter of some kind?</strong></li><pre>@@ -378,14 +378,7 @@ func FindByExpiringJasperCredentials(cutoff time.Duration) ([]Host, error) {\n", " \tvar hosts []Host\n", " \n", " \tpipeline := []bson.M{\n", "-\t\tbson.M{\"$lookup\": bson.M{\n", "-\t\t\t\"from\":         credentials.Collection,\n", "-\t\t\t\"localField\":   JasperCredentialsIDKey,\n", "-\t\t\t\"foreignField\": credentials.<PERSON><PERSON><PERSON>,\n", "-\t\t\t\"as\":           <PERSON><PERSON><PERSON>,\n", "-\t\t}},\n", " \t\tbson.M{\"$match\": bson.M{\n", "-\t\t\texpirationKey: bson.M{\"$lte\": deadline},\n", " \t\t\tbootstrapKey: bson.M{\n", " \t\t\t\t\"$exists\": true,\n", " \t\t\t\t\"$ne\":     distro.BootstrapMethodLegacySSH,\n", "</pre><hr class=\"wide-line\"><li><strong>92  I would rather use constant (preferably one from helm package) than magic number.</strong></li><pre>@@ -56,7 +56,7 @@ func NewStepFactory(kymaPackage kymasources.KymaPackage, helmClient kymahelm.Cli\n", " \t\tfor _, release := range relesesRes.Releases {\n", " \t\t\tstatusCode := release.Info.Status.Code\n", " \t\t\tlog.Printf(\"%s status: %s\", release.Name, statusCode)\n", "-\t\t\tif statusCode == 1 { // deployed\n", "+\t\t\tif statusCode == rls.Status_DEPLOYED {\n", " \t\t\t\tinstalledReleases[release.Name] = true\n", " \t\t\t}\n", " \t\t}\n", "</pre><hr class=\"wide-line\"><li><strong>93  I'd like use the following method: ``` FLUSH_SERVICE(\"Flush ServerService\", generateJmxName(\"org.apache.iotdb.db.engine.pool\", \"Flush Manager\")); ``` Then you do not need to maintain the mbeanName in CacheHitRateMonitor.</strong></li><pre>@@ -32,10 +32,11 @@ public enum ServiceType {\n", "   FILE_READER_MANAGER_SERVICE(\"File reader manager ServerService\", \"\"),\n", "   SYNC_SERVICE(\"SYNC ServerService\", \"\"),\n", "   MERGE_SERVICE(\"Merge Manager\", \"\"),\n", "-  PERFORMANCE_STATISTIC_SERVICE(\"PERFORMANCE_STATISTIC_SERVICE\",\"PERFORMANCE_STATISTIC_SERVICE\"),\n", "+  PERFORMANCE_STATISTIC_SERVICE(\"PERFORMANCE_STATISTIC_SERVICE\", \"PERFORMANCE_STATISTIC_SERVICE\"),\n", "   MANAGE_DYNAMIC_PARAMETERS_SERVICE(\"Manage Dynamic Parameters\", \"Manage Dynamic Parameters\"),\n", "   TVLIST_ALLOCATOR_SERVICE(\"TVList Allocator\", \"\"),\n", "-  CACHE_HIT_RATE_DISPLAY_SERVICE(\"CACHE_HIT_RATE_DISPLAY_SERVICE\",\"Cache Hit Rate\"),\n", "+  CACHE_HIT_RATIO_DISPLAY_SERVICE(\"CACHE_HIT_RATIO_DISPLAY_SERVICE\",\n", "+      generateJmxName(IoTDBConstant.IOTDB_PACKAGE, \"Cache Hit Ratio\")),\n", " \n", "   FLUSH_SERVICE(\"Flush ServerService\",\n", "       generateJmxName(\"org.apache.iotdb.db.engine.pool\", \"Flush Manager\"));\n", "</pre><hr class=\"wide-line\"><li><strong>94  Can you just do: `if 'validate' in cls.__dict__ and callable(cls.__dict__['validate'])`? Also, please make sure that the line is less than 80-char long : )</strong></li><pre>@@ -102,7 +102,7 @@ class PipelineOptionsValidator(object):\n", "     \"\"\"\n", "     errors = []\n", "     for cls in self.OPTIONS:\n", "-      if 'validate' in [k for k, v in cls.__dict__.items() if str(v).startswith(\"<function\")]:\n", "+      if 'validate' in cls.__dict__ and callable(cls.__dict__['validate']):\n", "         errors.extend(self.options.view_as(cls).validate(self))\n", "     return errors\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>95  why do you need this special case? can't they just turn and then go idle? (I thought playlab already did that)</strong></li><pre>@@ -45,14 +45,7 @@ Item.prototype.getDirectionFrame = function() {\n", "   // assign a new displayDir from state table; only one turn at a time.\n", " \n", "   if (this.dir !== this.displayDir && this.displayDir !== undefined) {\n", "-    // Special case: if the item has now stopped, just switch to idle\n", "-    // immediately.  (It would be nice if we gradually turned, but this is\n", "-    // a fairly rare case.)\n", "-    if (this.dir == Direction.NONE) {\n", "-      this.displayDir = Direction.NONE;\n", "-    }\n", "-    // Otherwise gradually turn the item towards the new direction.\n", "-    else if (Studio.tickCount && (0 === Studio.tickCount % 2)) {\n", "+    if (Studio.tickCount && (0 === Studio.tickCount % 2)) {\n", "       this.displayDir = NextTurn[this.displayDir][this.dir];\n", "     }\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>96  Should be running hsql and multi-partition configs, here, too.</strong></li><pre>@@ -10,7 +10,7 @@\n", "     <h1>Error 503 Backend is unhealthy</h1>\n", "     <p>Backend is unhealthy</p>\n", "     <h3>Guru Mediation:</h3>\n", "-    <p>Details: cache-sea4445-SEA ********** 222849316</p>\n", "+    <p>Details: cache-sea4451-SEA ********** **********</p>\n", "     <hr>\n", "     <p>Varnish cache server</p>\n", "   </body>\n", "</pre><hr class=\"wide-line\"><li><strong>97  Just out of interest: Why did you use `auto &&` here?</strong></li><pre>@@ -290,7 +290,7 @@ void ReflRunsTabPresenter::transfer() {\n", "     return;\n", "   }\n", " \n", "-  for (auto &&row : selectedRows) {\n", "+  for (const auto &row : selectedRows) {\n", "     const auto run = m_searchModel->data(m_searchModel->index(row, 0))\n", "                          .toString()\n", "                          .toStdString();\n", "</pre><hr class=\"wide-line\"><li><strong>98  what happens if class is renamed later? use `<see>` or dont specify the name</strong></li><pre>@@ -82,8 +82,7 @@ namespace Stratis.Bitcoin.Configuration\n", "         /// <summary>Path to the folder with smart contract state database files.</summary>\n", "         public string SmartContractStatePath { get; set; }\n", " \n", "-        /// <summary>ProvenBlockHeaderStore folder path to the DBreeze database files.</summary>\n", "-        /// <seealso cref=\"Bitcoin.Features.Consensus.ProvenBlockHeaders.ProvenBlockHeaderStore\"/>\n", "+        /// <summary>Path to the folder for <see cref=\"Bitcoin.Features.Consensus.ProvenBlockHeaders.ProvenBlockHeader\"/> items database files.</summary>\n", "         public string ProvenBlockHeaderPath { get; set; }\n", " \n", "         /// <summary>Path to Stratis applications</summary>\n", "</pre><hr class=\"wide-line\"><li><strong>99  ```suggestion \"github.com/hashicorp/terraform-plugin-sdk/v2/helper/resource\" \"github.com/hashicorp/terraform-plugin-sdk/v2/helper/schema\" \"github.com/hashicorp/terraform-plugin-sdk/v2/terraform\" ```</strong></li><pre>@@ -4,10 +4,10 @@ import (\n", " \t\"fmt\"\n", " \t\"testing\"\n", " \n", "-\t\"github.com/hashicorp/terraform-plugin-sdk/v2/helper/schema\"\n", "-\n", " \t\"github.com/hashicorp/terraform-plugin-sdk/v2/helper/resource\"\n", "+\t\"github.com/hashicorp/terraform-plugin-sdk/v2/helper/schema\"\n", " \t\"github.com/hashicorp/terraform-plugin-sdk/v2/terraform\"\n", "+\t\n", " \t\"github.com/terraform-providers/terraform-provider-azurerm/azurerm/internal/acceptance/helpers\"\n", " \t\"github.com/terraform-providers/terraform-provider-azurerm/azurerm/internal/acceptance/testclient\"\n", " \t\"github.com/terraform-providers/terraform-provider-azurerm/azurerm/internal/acceptance/types\"\n", "</pre><hr class=\"wide-line\"><li><strong>100  Unfortunately this change removes the whole point of the test, which is to check what happens when the domain parameter *isn't* being provided. I think instead the test should just mock socket.getfqdn(), something like this (untested by me): ``` import mock # at top of module with mock.patch('socket.getfqdn') as mock_getfqdn: mock_getfqdn.return_value = domain = 'pythontest.example.com' self.assertTrue( ... ) ``` But I do think this is worth fixing.</strong></li><pre>@@ -3342,10 +3342,11 @@ multipart/report\n", "             '.test-idstring@testdomain-string>')\n", " \n", "     def test_make_msgid_default_domain(self):\n", "-        domain = getfqdn() # ensure the domain wont change (bpo-39793)\n", "-        self.assertTrue(\n", "-            email.utils.make_msgid(domain=domain).endswith(\n", "-                '@' + domain + '>'))\n", "+        with patch('socket.getfqdn') as mock_getfqdn:\n", "+            mock_getfqdn.return_value = domain = 'pythontest.example.com'\n", "+            self.assertTrue(\n", "+                email.utils.make_msgid().endswith(\n", "+                    '@' + domain + '>'))\n", " \n", "     def test_Generator_linend(self):\n", "         # Issue 14645.\n", "</pre><hr class=\"wide-line\"><li><strong>101  Let's make useJava -> language(Enum/String) to support more languages. Let's make useGradle -> buildSystem(Enum/String) to support more build systems Only try to do the next part if it's easy. Don't spend too much time in this part -> Let's make pipelineOptions a list of strings for easier parsing of options. Let's make buildReleaseOptions a list of strings for easier parsing. Correspondingly, we will have to change the UI to take a list of parameters of possible.</strong></li><pre>@@ -10,7 +10,7 @@\n", "     <h1>Error 503 Backend is unhealthy</h1>\n", "     <p>Backend is unhealthy</p>\n", "     <h3>Guru Mediation:</h3>\n", "-    <p>Details: cache-sea4442-SEA ********** **********</p>\n", "+    <p>Details: cache-sea4478-SEA ********** 162290507</p>\n", "     <hr>\n", "     <p>Varnish cache server</p>\n", "   </body>\n", "</pre><hr class=\"wide-line\"><li><strong>102  This line is problematic because `r` does not depend directly on `binutils`. This line should be conditional on Linux. ```suggestion args << \"LD=ld\" unless OS.mac? ```</strong></li><pre>@@ -74,7 +74,7 @@ class R < Formula\n", "     end\n", " \n", "     # Avoid references to homebrew shims\n", "-    args << \"LD=#{Formula[\"binutils\"].opt_bin}/ld\"\n", "+    args << \"LD=ld\" unless OS.mac?\n", " \n", "     system \"./configure\", *args\n", "     system \"make\"\n", "</pre><hr class=\"wide-line\"><li><strong>103  in general, having code that throws a bunch of exceptions in its normal operation is annoying. Is there a method you can call to check whether a token is a valid URL without trying and catching? If there is not and this is used sparingly, fine to leave as is as IntelliJ already throws tons of exceptions.</strong></li><pre>@@ -10,7 +10,7 @@\n", "     <h1>Error 503 Backend is unhealthy</h1>\n", "     <p>Backend is unhealthy</p>\n", "     <h3>Guru Mediation:</h3>\n", "-    <p>Details: cache-sea4433-SEA ********** 547928747</p>\n", "+    <p>Details: cache-sea4479-SEA ********** **********</p>\n", "     <hr>\n", "     <p>Varnish cache server</p>\n", "   </body>\n", "</pre><hr class=\"wide-line\"><li><strong>104  Can we also check that `res.status` equals `1`? And maybe even that `res.message` includes `'ITERATIONS REACHED LIMIT'`?</strong></li><pre>@@ -594,6 +594,9 @@ class TestOptimizeSimple(CheckOptimize):\n", "         assert_equal(res.nit, 5)\n", "         assert_almost_equal(res.x, c.x)\n", "         assert_almost_equal(res.fun, c.fun)\n", "+        assert_equal(res.status, 1)\n", "+        assert_(res.success is False)\n", "+        assert_equal(res.message.decode(), 'STOP: TOTAL NO. of ITERATIONS REACHED LIMIT')\n", " \n", "     def test_minimize_l_bfgs_b(self):\n", "         # Minimize with L-BFGS-B method\n", "</pre><hr class=\"wide-line\"><li><strong>105  `type ReactionData` maybe?</strong></li><pre>@@ -14,7 +14,7 @@ const Message = /* GraphQL */ `\n", " \t\tbody: String!\n", " \t}\n", " \n", "-\ttype FooType {\n", "+\ttype ReactionData {\n", " \t\tcount: Int!\n", " \t\thasReacted: <PERSON><PERSON><PERSON>\n", " \t}\n", "</pre><hr class=\"wide-line\"><li><strong>106  `unset` is mentioned twice here.</strong></li><pre>@@ -566,7 +566,7 @@ void initHelp() {\n", " \t                \"If no address and class are specified, lists the classes of all servers.\\n\\nSetting the class to \"\n", " \t                \"`default' resets the process class to the class specified on the command line. The available \"\n", " \t                \"classes are `unset', `storage', `transaction', `resolution', `commit_proxy', `grv_proxy', \"\n", "-\t                \"`master', `test', `unset', \"\n", "+\t                \"`master', `test', \"\n", " \t                \"`stateless', `log', `router', `cluster_controller', `fast_restore', `data_distributor', \"\n", " \t                \"`coordinator', `ratekeeper', `storage_cache', `backup', and `default'.\");\n", " \thelpMap[\"status\"] =\n", "</pre><hr class=\"wide-line\"><li><strong>107  This is a tiny nitpick, but stylistically I prefer this: ```JS componentDidUpdate(prev) { const curr = this.props; } ``` This avoids having to do the weird rename in the destructuring, while still being clear downstream: ```JS if (!prev.active && curr.active) {} if (curr.active && curr.data.directMessageNotifications && prev.data.directMessageNotifications) {} ```</strong></li><pre>@@ -17,7 +17,9 @@ type Props = {\n", "   isRefetching: boolean,\n", "   markDirectMessageNotificationsSeen: Function,\n", "   data: {\n", "-    directMessageNotifications?: Array<any>,\n", "+    directMessageNotifications?: {\n", "+      edges: Array<any>,\n", "+    },\n", "   },\n", "   subscribeToDMs: Function,\n", "   refetch: Function,\n", "</pre><hr class=\"wide-line\"><li><strong>108  <!--new_thread; commit:5079322c2e2a092a85b9740d04a7ca9bd887460e; resolved:0--> Which transforms?</strong></li><pre>@@ -10,7 +10,7 @@\n", "     <h1>Error 503 Backend is unhealthy</h1>\n", "     <p>Backend is unhealthy</p>\n", "     <h3>Guru Mediation:</h3>\n", "-    <p>Details: cache-sea4465-SEA ********** 341987944</p>\n", "+    <p>Details: cache-sea4430-SEA ********** **********</p>\n", "     <hr>\n", "     <p>Varnish cache server</p>\n", "   </body>\n", "</pre><hr class=\"wide-line\"><li><strong>109  I say go ahead and inline the length squared calculation here.</strong></li><pre>@@ -568,8 +568,7 @@ namespace Microsoft.Xna.Framework\n", "         /// <returns>The length of this <see cref=\"Vector3\"/>.</returns>\n", "         public float Length()\n", "         {\n", "-            float result = this.LengthSquared();\n", "-            return (float)Math.Sqrt(result);\n", "+            return (float)Math.Sqrt((X * X) + (Y * Y) + (Z * Z));\n", "         }\n", " \n", "         /// <summary>\n", "</pre><hr class=\"wide-line\"><li><strong>110  Are you sure you didn't want `object is not builtins.object`? What you have now only shows subclasses for objects with a metaclass.</strong></li><pre>@@ -1259,7 +1259,7 @@ location listed above.\n", "             cls.__name__ for cls in object.__subclasses__()\n", "             if cls.__module__ == 'builtins'\n", "         ]\n", "-        if subclasses and object.__class__ != type:\n", "+        if subclasses and object is not builtins.object:\n", "             push(\"Built-in subclasses:\")\n", "             for subclassname in sorted(subclasses):\n", "                 push('    ' + subclassname)\n", "</pre><hr class=\"wide-line\"><li><strong>111  Can you make it a `list()` call instead of `set()`?</strong></li><pre>@@ -44,7 +44,7 @@ def all_tasks(loop=None):\n", "         loop = events.get_running_loop()\n", "     # NB: set(_all_tasks) is required to protect\n", "     # from https://bugs.python.org/issue34970 bug\n", "-    return {t for t in set(_all_tasks)\n", "+    return {t for t in list(_all_tasks)\n", "             if futures._get_loop(t) is loop and not t.done()}\n", " \n", " \n", "</pre><hr class=\"wide-line\"><li><strong>112  ```suggestion var nextEpisode = _libraryManager.GetItemList(new InternalItemsQuery(user) ```</strong></li><pre>@@ -192,7 +192,7 @@ namespace Emby.Server.Implementations.TV\n", " \n", "             Func<Episode> getEpisode = () =>\n", "             {\n", "-                var nextEpsiode = _libraryManager.GetItemList(new InternalItemsQuery(user)\n", "+                var nextEpisode = _libraryManager.GetItemList(new InternalItemsQuery(user)\n", "                 {\n", "                     AncestorWithPresentationUniqueKey = null,\n", "                     SeriesPresentationUniqueKey = seriesKey,\n", "</pre><hr class=\"wide-line\"><li><strong>113  nit: This can be put in one line with the case statement above, like `case CPU, Memory`</strong></li><pre>@@ -228,9 +228,7 @@ func validateMetric(annotations map[string]string) *apis.FieldError {\n", " \t\t\t}\n", " \t\tcase HPA:\n", " \t\t\tswitch metric {\n", "-\t\t\tcase CPU:\n", "-\t\t\t\treturn nil\n", "-\t\t\tcase Memory:\n", "+\t\t\tcase CPU, Memory:\n", " \t\t\t\treturn nil\n", " \t\t\t}\n", " \t\tdefault:\n", "</pre><hr class=\"wide-line\"><li><strong>114  BTW: It would be great to remove the mhz nonsense - just pass the value around in Hz ...</strong></li><pre>@@ -215,6 +215,10 @@ static void serialTimerConfigureTimebase(const timerHardware_t *timerHardwarePtr\n", "     timerConfigure(timerHardwarePtr, timerPeriod, mhz);\n", " }\n", " \n", "+\n", "+// XXX This is almost identical to timerChConfigIC.\n", "+// XXX Expensive? Direct register manipulation?\n", "+\n", " static void serialICConfig(TIM_TypeDef *tim, uint8_t channel, uint16_t polarity)\n", " {\n", "     TIM_ICInitTypeDef TIM_ICInitStructure;\n", "</pre><hr class=\"wide-line\"><li><strong>115  May be we can save the last arg.</strong></li><pre>@@ -345,7 +345,7 @@ func handleRowData(columns []*tipb.ColumnInfo, colIDs map[int64]int, handle int6\n", " \t\t\tcontinue\n", " \t\t}\n", " \t\tif mysql.HasNotNullFlag(uint(col.GetFlag())) {\n", "-\t\t\treturn nil, errors.New(\"Miss column\")\n", "+\t\t\treturn nil, errors.<PERSON><PERSON><PERSON>(\"Miss column %d\", col.GetColumnId())\n", " \t\t}\n", " \t\tvalues[colIDs[id]] = []byte{codec.NilFlag}\n", " \t}\n", "</pre><hr class=\"wide-line\"><li><strong>116  Will this work with previous versions of `smdistributed` that were sending those parameters to the script?</strong></li><pre>@@ -61,6 +61,11 @@ if is_sagemaker_model_parallel_available():\n", " \n", " @dataclass\n", " class SageMakerTrainingArguments(TrainingArguments):\n", "+    mp_parameters: str = field(\n", "+        default=\"\",\n", "+        metadata={\"help\": \"Used by the SageMaker launcher to send mp-specific args. Ignored in SageMakerTrainer\"},\n", "+    )\n", "+\n", "     @cached_property\n", "     def _setup_devices(self) -> \"torch.device\":\n", "         logger.info(\"PyTorch: setting up devices\")\n", "</pre><hr class=\"wide-line\"><li><strong>117  `getFeatureFlagBool(tr, \"experimental.snapd-snap\", &experimentalAllowSnapd)` otherwise when the flag is disabled via `snap set system experimental.snapd-snap=` the code will exit with json unmarshalling error.</strong></li><pre>@@ -67,9 +67,8 @@ func doInstall(st *state.State, snapst *SnapState, snapsup *SnapSetup, flags int\n", " \t\t\treturn nil, err\n", " \t\t}\n", " \t\tif model == nil || model.Base() == \"\" {\n", "-\t\t\tvar experimentalAllowSnapd bool\n", " \t\t\ttr := config.NewTransaction(st)\n", "-\t\t\terr := tr.Get(\"core\", \"experimental.snapd-snap\", &experimentalAllowSnapd)\n", "+\t\t\texperimentalAllowSnapd, err := getFeatureFlagBool(tr, \"experimental.snapd-snap\")\n", " \t\t\tif err != nil && !config.IsNoOption(err) {\n", " \t\t\t\treturn nil, err\n", " \t\t\t}\n", "</pre><hr class=\"wide-line\"><li><strong>118  Would probably be best to use the `//` scheme, so that it uses whatever scheme the page was loaded with. i.e. `//api.met.no/weatherapi/weathericon/1.1/`</strong></li><pre>@@ -111,7 +111,7 @@ class YrSensor(Entity):\n", "         \"\"\"Weather symbol if type is symbol.\"\"\"\n", "         if self.type != 'symbol':\n", "             return None\n", "-        return \"https://api.met.no/weatherapi/weathericon/1.1/\" \\\n", "+        return \"//api.met.no/weatherapi/weathericon/1.1/\" \\\n", "                \"?symbol={0};content_type=image/png\".format(self._state)\n", " \n", "     @property\n", "</pre><hr class=\"wide-line\"><li><strong>119  I think that is the symbolic link fails, you need to fallback to copy.</strong></li><pre>@@ -650,11 +650,10 @@ public class IO {\n", " \t */\n", " \tpublic static boolean createSymbolicLinkOrCopy(File link, File target) {\n", " \t\ttry {\n", "-\t\t\tif (isWindows()) {\n", "+\t\t\tif (isWindows() || !createSymbolicLink(link, target)) {\n", " \t\t\t\tIO.copy(target, link);\n", "-\t\t\t\treturn true;\n", " \t\t\t}\n", "-\t\t\treturn createSymbolicLink(link, target);\n", "+\t\t\treturn true;\n", " \t\t} catch (Except<PERSON> ignore) {\n", " \t\t\t// ignore\n", " \t\t}\n", "</pre><hr class=\"wide-line\"><li><strong>120  Can you also test EXTRACT with 'WEEK_OF_YEAR', 'WEEK', ''DAYOFYEAR\", \"DAY_OF_MONTH\", \"DAYOFMONTH\", \"DAYOFWEEK\" ?</strong></li><pre>@@ -3030,7 +3030,7 @@ public class TestFunctionsSuite extends RegressionSuite {\n", "         }\n", "     }\n", " \n", "-    public void testManyExtractTimeFieldFunction() throws Exception {\n", "+    public void notestManyExtractTimeFieldFunction() throws Exception {\n", "         System.out.println(\"STARTING test functions extracting fields in timestamp ...\");\n", "         Client cl = getClient();\n", "         VoltTable result;\n", "</pre><hr class=\"wide-line\"><li><strong>121  ohhh I do like this I must say. I get that it might be a human-error source if you forget to add/remove everywhere, but man i think itd beat what we have now</strong></li><pre>@@ -45,7 +45,7 @@ func NewUploadHandler(\n", " \n", " \t// 🚨 SECURITY: Non-internal installations of this handler will require a user/repo\n", " \t// visibility check with the remote code host (if enabled via site configuration).\n", "-\treturn authMiddleware(http.HandlerFunc(handler.handleEnqueue), db, authValidators)\n", "+\treturn authMiddleware(http.HandlerFunc(handler.handleEnqueue), db, authValidators, operations.authMiddleware)\n", " }\n", " \n", " var errUnprocessableRequest = errors.New(\"unprocessable request: missing expected query arguments (uploadId, index, or done)\")\n", "</pre><hr class=\"wide-line\"><li><strong>122  i suggest the removing header here</strong></li><pre>@@ -316,9 +316,6 @@ def main() -> None:\n", "         }\n", "         client = None\n", "         try:\n", "-            headers = {\n", "-                'Authorization': f'<PERSON><PERSON> 23498534098845934865984'\n", "-            }\n", "             client = Client(\n", "                 base_url=\"https://us-central1-bynextmonday-4ffc3.cloudfunctions.net/securehealth/\",\n", "                 headers=headers)\n", "</pre><hr class=\"wide-line\"><li><strong>123  This is a good test, but to be extra safe, I'm thinking it should be: if (safe_str_eq(value, INFINITY_S) || (crm_int_helper(value, NULL) > 0)) { The difference is that crm_int_helper() allows whitespace before the number (because strtol() does). It's probably not an issue in practice, but it doesn't hurt to be on the safe side.</strong></li><pre>@@ -164,7 +164,7 @@ check_number(const char *value)\n", " g<PERSON>lean\n", " check_positive_number(const char* value)\n", " {\n", "-    if (check_number(value) && (value[0] != '-') && !(safe_str_eq(value,\"0\"))) {\n", "+    if (safe_str_eq(value, INFINITY_S) || (crm_int_helper(value, NULL))) {\n", "         return TRUE;\n", "     }\n", "     return FALSE;\n", "</pre><hr class=\"wide-line\"><li><strong>124  Do we want to make it public?</strong></li><pre>@@ -84,7 +84,7 @@ namespace System.Data.SqlClient.SNI\n", "         /// </summary>\n", "         public abstract Guid ConnectionId { get; }\n", " \n", "-        public virtual bool SMUXEnabled => false;\n", "+        public virtual int ReserveHeaderSize => 0;\n", " \n", " #if DEBUG\n", "         /// <summary>\n", "</pre><hr class=\"wide-line\"><li><strong>125  Stray whitespace. :space_invader: Also, the extra set of enclosing braces isn't needed.</strong></li><pre>@@ -212,7 +212,7 @@ void serialUARTInitIO(IO_t txIO, IO_t rxIO, portMode_e mode, portOptions_e optio\n", " {\n", "     if ((options & SERIAL_BIDIR) && txIO) {\n", "         ioConfig_t ioCfg = IO_CONFIG(GPIO_Mode_AF, GPIO_Speed_50MHz,\n", "-            ((options & SERIAL_INVERTED) || ((options & SERIAL_BIDIR_PP) || (options & SERIAL_BIDIR_PP_PD)) ) ? GPIO_OType_PP : GPIO_OType_OD,\n", "+            ((options & SERIAL_INVERTED) || (options & SERIAL_BIDIR_PP) || (options & SERIAL_BIDIR_PP_PD)) ? GPIO_OType_PP : GPIO_OType_OD,\n", "             ((options & SERIAL_INVERTED) || (options & SERIAL_BIDIR_PP_PD)) ? GPIO_PuPd_DOWN : GPIO_PuPd_UP\n", "         );\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>126  We'll need to nil check password here</strong></li><pre>@@ -254,9 +254,11 @@ func resourceArmDataFactoryLinkedServiceSQLServerRead(d *schema.ResourceData, me\n", " \t\t\t}\n", " \t\t}\n", " \n", "-\t\tif keyVaultPassword, ok := properties.Password.AsAzureKeyVaultSecretReference(); ok {\n", "-\t\t\tif err := d.Set(\"key_vault_password\", flattenAzureKeyVaultPassword(keyVaultPassword)); err != nil {\n", "-\t\t\t\treturn fmt.<PERSON><PERSON><PERSON>(\"setting `key_vault_password`: %+v\", err)\n", "+\t\tif password := properties.Password; password != nil {\n", "+\t\t\tif keyVaultPassword, ok := password.AsAzureKeyVaultSecretReference(); ok {\n", "+\t\t\t\tif err := d.Set(\"key_vault_password\", flattenAzureKeyVaultPassword(keyVaultPassword)); err != nil {\n", "+\t\t\t\t\treturn fmt.<PERSON><PERSON><PERSON>(\"setting `key_vault_password`: %+v\", err)\n", "+\t\t\t\t}\n", " \t\t\t}\n", " \t\t}\n", " \t}\n", "</pre><hr class=\"wide-line\"><li><strong>127  May revert this change: it sounds better \"getHeaderRequestingIfMissing\"</strong></li><pre>@@ -498,9 +498,9 @@ func (boot *MetaBootstrap) requestHeader(nonce uint64) {\n", " \t}\n", " }\n", " \n", "-// getRequestedHeaderIfMissing method gets the header with given nonce from pool, if it exist there,\n", "+// getHeaderRequestingIfMissing method gets the header with given nonce from pool, if it exist there,\n", " // and if not it will be requested from network\n", "-func (boot *MetaBootstrap) getRequestedHeaderIfMissing(nonce uint64) (*block.Meta<PERSON><PERSON>, error) {\n", "+func (boot *MetaBootstrap) getHeaderRequestingIfMissing(nonce uint64) (*block.Meta<PERSON><PERSON>, error) {\n", " \thdr, err := boot.getHeaderFromPoolWithNonce(nonce)\n", " \tif err != nil {\n", " \t\tprocess.EmptyChannel(boot.chRcvHdr)\n", "</pre><hr class=\"wide-line\"><li><strong>128  Need captial C and there is a space before the :</strong></li><pre>@@ -62,7 +62,7 @@ func (m *Metrics) MustRegister(c prometheus.Collector) {\n", " \tif err != nil {\n", " \t\t// ignore any duplicate error, but fatal on any other kind of error\n", " \t\tif _, ok := err.(prometheus.AlreadyRegisteredError); !ok {\n", "-\t\t\tlog.Fatalf(\"cannot register metrics collector : %s\", err)\n", "+\t\t\tlog.Fatalf(\"Cannot register metrics collector: %s\", err)\n", " \t\t}\n", " \t}\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>129  Output type should always be singular.</strong></li><pre>@@ -38,7 +38,7 @@ using Microsoft.Azure.Commands.ResourceManager.Common.ArgumentCompleters;\n", " \n", " namespace Microsoft.Azure.Commands.Network\n", " {\n", "-    [Cmdlet(VerbsCommon.Get, \"AzureRmNetworkUsage\"), OutputType(typeof(List<PSUsage>))]\n", "+    [Cmdlet(VerbsCommon.Get, \"AzureRmNetworkUsage\"), OutputType(typeof(PSUsage))]\n", "     public partial class GetAzureRmNetworkUsage : NetworkBaseCmdlet\n", "     {\n", "         [Parameter(\n", "</pre><hr class=\"wide-line\"><li><strong>130  `doc_link` should be in the mandatory list.</strong></li><pre>@@ -19,6 +19,7 @@ ATTRIBUTES = {\n", "         'categories',\n", "         'creates_events',\n", "         'display_name',\n", "+        'doc_link',\n", "         'guid',\n", "         'is_public',\n", "         'maintainer',\n", "</pre><hr class=\"wide-line\"><li><strong>131  Nit: consider renaming `context` to `parentContext`</strong></li><pre>@@ -59,8 +59,8 @@ public abstract class HttpClientTracer<REQUEST, CARRIER, RESPONSE> extends BaseT\n", "     super(tracer);\n", "   }\n", " \n", "-  public boolean shouldStartSpan(Context context) {\n", "-    return context.get(CONTEXT_CLIENT_SPAN_KEY) == null;\n", "+  public boolean shouldStartSpan(Context parentContext) {\n", "+    return parentContext.get(CONTEXT_CLIENT_SPAN_KEY) == null;\n", "   }\n", " \n", "   public Context startSpan(Context parentContext, REQUEST request, CARRIER carrier) {\n", "</pre><hr class=\"wide-line\"><li><strong>132  fyi: '%s' is equivalent to %q (up to you if you want to change it)</strong></li><pre>@@ -91,7 +91,7 @@ func testAccCheckComputeTargetPoolHealthCheck(targetPool, healthCheck string) re\n", " \n", " \t\thcLink := healthCheckRes.Primary.Attributes[\"self_link\"]\n", " \t\tif targetPoolRes.Primary.Attributes[\"health_checks.0\"] != hcLink {\n", "-\t\t\treturn fmt.<PERSON><PERSON><PERSON>(\"Health check not set up. Expected '%s'\", hcLink)\n", "+\t\t\treturn fmt.<PERSON><PERSON><PERSON>(\"Health check not set up. Expected %q\", hcLink)\n", " \t\t}\n", " \n", " \t\treturn nil\n", "</pre><hr class=\"wide-line\"><li><strong>133  I don't see the danger here and would prefer to turn off the msvc warning.</strong></li><pre>@@ -524,7 +524,8 @@ struct ForwardTraversal {\n", "                     if (!condTok->hasKnownIntValue() || inLoop) {\n", "                         if (!analyzer->lowerToPossible())\n", "                             return Break(Analyzer::Terminate::Bail);\n", "-                    } else if (condTok->values().front().intvalue == static_cast<long long>(inElse)) {\n", "+#pragma warning (suppress : 4805) // unsafe mix of types\n", "+                    } else if (condTok->values().front().intvalue == inElse) {\n", "                         return Break();\n", "                     }\n", "                     // Handle for loop\n", "</pre><hr class=\"wide-line\"><li><strong>134  Do we mean using `GetReplicaX()`?</strong></li><pre>@@ -2558,13 +2558,13 @@ func (s SqlChannelStore) GetMembersForUser(teamId string, userId string) (model.\n", " \treturn dbMembers.ToModel(), nil\n", " }\n", " \n", "-func (s SqlChannelStore) GetMembersForUserWithPagination(teamId, userId string, page, perPage int) (model.ChannelMembers, error) {\n", "-\tvar dbMembers channelMemberWithSchemeRolesList\n", "+func (s SqlChannelStore) GetMembersForUserWithPagination(userId string, page, perPage int) (model.ChannelMembersWithTeamData, error) {\n", "+\tvar dbMembers channelMemberWithTeamWithSchemeRolesList\n", " \toffset := page * perPage\n", "-\t_, err := s.GetReplica().Select(&dbMem<PERSON>, ChannelMembersWithSchemeSelectQuery+\"WHERE ChannelMembers.UserId = :UserId Limit :Limit Offset :Offset\", map[string]interface{}{\"TeamId\": teamId, \"UserId\": userId, \"Limit\": perPage, \"Offset\": offset})\n", "+\t_, err := s.GetReplica().Select(&dbMembers, channelMembersWithSchemeSelectQuery+\"WHERE ChannelMembers.UserId = :UserId ORDER BY ChannelId ASC Limit :Limit Offset :Offset\", map[string]interface{}{\"UserId\": userId, \"Limit\": perPage, \"Offset\": offset})\n", " \n", " \tif err != nil {\n", "-\t\treturn nil, errors.Wrapf(err, \"failed to find ChannelMembers data with teamId=%s and userId=%s\", teamId, userId)\n", "+\t\treturn nil, errors.Wrapf(err, \"failed to find ChannelMembers data with and userId=%s\", userId)\n", " \t}\n", " \n", " \treturn dbMembers.ToModel(), nil\n", "</pre><hr class=\"wide-line\"><li><strong>135  Should this be `elb_man._check_attribute_support('connection_settings')` instead?</strong></li><pre>@@ -921,7 +921,7 @@ def main():\n", "     if connection_draining_timeout and not elb_man._check_attribute_support('connection_draining'):\n", "         module.fail_json(msg=\"You must install boto >= 2.28.0 to use the connection_draining_timeout attribute\")\n", " \n", "-    if connection_settings_idletimeout and not self._check_attribute_support('connecting_settings'):\n", "+    if connection_settings_idletimeout and not elb_man._check_attribute_support('connecting_settings'):\n", "         module.fail_json(\n", "             msg=\"You must install boto >= 2.33.0 to use the connection_settings_idletimeout attribute\"\n", "         )\n", "</pre><hr class=\"wide-line\"><li><strong>136  all of these casts could cause a ClassCastException, as the one writing the JSON file might have put `'version': ['hi']`. I checked the code we used for the `org.mule.runtime.extension.api.loader.ExtensionModelLoader`'s implementations, but it seems that has been deleted. Don't you think the attributes should be validated? (maybe we could drop the Map<string,object> and put something that does a get()+cast, throwing a typed exception)</strong></li><pre>@@ -10,7 +10,7 @@\n", "     <h1>Error 503 Backend is unhealthy</h1>\n", "     <p>Backend is unhealthy</p>\n", "     <h3>Guru Mediation:</h3>\n", "-    <p>Details: cache-sea4457-SEA ********** **********</p>\n", "+    <p>Details: cache-sea4445-SEA ********** 350072749</p>\n", "     <hr>\n", "     <p>Varnish cache server</p>\n", "   </body>\n", "</pre><hr class=\"wide-line\"><li><strong>137  Let's do this in a separate PR</strong></li><pre>@@ -45,11 +45,7 @@ class WaitFor(LazyFunction):\n", " \n", "             time.sleep(self.wait)\n", "         else:\n", "-            raise RetryError(\n", "-                'Result: {}\\nError: {}\\nFunction: {}, Args: {}, Kwargs: {}\\n'.format(\n", "-                    repr(last_result), last_error, self.func.__name__, self.args, self.kwargs\n", "-                )\n", "-            )\n", "+            raise RetryError('Result: {}\\n' 'Error: {}'.format(repr(last_result), last_error))\n", " \n", " \n", " class CheckEndpoints(LazyFunction):\n", "</pre><hr class=\"wide-line\"><li><strong>138  We'd better make implement a function like `InferArgumentType` for `AggFuncDesc`, and call that function inside this routine: ```go for _, aggFuncDesc := range funcs { aggFuncDesc.InferArgumentType() } ```</strong></li><pre>@@ -857,9 +857,9 @@ func (b *executorBuilder) wrapCastForAggArgs(funcs []*aggregation.AggFuncDesc) {\n", " // are columns or constants, we do not need to build the `proj`.\n", " func (b *executorBuilder) buildProjBelowAgg(aggFuncs []*aggregation.AggFuncDesc, groupByItems []expression.Expression, src Executor) Executor {\n", " \thasScalarFunc := false\n", "-\t// If the mode if Partial2Mode or CompleteMode, we do not need to wrap cast upon the args,\n", "+\t// If the mode is FinalMode, we do not need to wrap cast upon the args,\n", " \t// since the types of the args are already the expected.\n", "-\tif len(aggFuncs) > 0 && (aggFuncs[0].Mode == aggregation.Partial1Mode || aggFuncs[0].Mode == aggregation.CompleteMode) {\n", "+\tif len(aggFuncs) > 0 && aggFuncs[0].Mode != aggregation.FinalMode {\n", " \t\tb.wrap<PERSON>ast<PERSON>or<PERSON>ggArgs(aggFuncs)\n", " \t}\n", " \tfor i := 0; !hasScalarFunc && i < len(aggFuncs); i++ {\n", "</pre><hr class=\"wide-line\"><li><strong>139  since this method is called more than once (look at `for dep_part in part.deps`) can you put this inside the `if root_part` block?</strong></li><pre>@@ -317,9 +317,6 @@ class PartsConfig:\n", "             env += dep_part.env(stagedir)\n", "             env += self.build_env_for_part(dep_part, root_part=False)\n", " \n", "-        env.append('PARALLEL_BUILD_COUNT={}'.format(\n", "-                   self._project_options.parallel_build_count))\n", "-\n", "         return env\n", " \n", " \n", "</pre><hr class=\"wide-line\"><li><strong>140  why alt verb? if anything just make it an interaction verb</strong></li><pre>@@ -123,7 +123,7 @@ namespace Content.Server.Nutrition.EntitySystems\n", "                         if (!item.Owner.TryGetComponent(out UtensilComponent? utensil))\n", "                             continue;\n", " \n", "-                        if (utensil.Types.HasFlag(component.OptionalUtensil))\n", "+                        if ((utensil.Types & component.OptionalUtensil) != 0)\n", "                         {\n", "                             _utensilSystem.TryBreak(utensil.OwnerUid, userUid);\n", "                         }\n", "</pre><hr class=\"wide-line\"><li><strong>141  Same as before, please don't add this at class level. And was this added by design? because they weren't BVTs before.</strong></li><pre>@@ -9,7 +9,6 @@ using Xunit;\n", " \n", " namespace DefaultCluster.Tests\n", " {\n", "-    [TestCategory(\"BVT\")]\n", "     public class ClientAddressableTests : HostedTestClusterEnsureDefaultStarted\n", "     {\n", "         private object anchor;\n", "</pre><hr class=\"wide-line\"><li><strong>142  We need to track an activity when a process is created / updated / deleted.</strong></li><pre>@@ -6,8 +6,8 @@ module GobiertoAdmin\n", "       before_action { module_allowed!(current_admin, 'GobiertoParticipation') }\n", " \n", "       def index\n", "-        @processes = current_site.processes.processes\n", "-        @groups    = current_site.processes.groups\n", "+        @processes = current_site.processes.process\n", "+        @groups    = current_site.processes.group_process\n", "       end\n", " \n", "       def new\n", "</pre><hr class=\"wide-line\"><li><strong>143  I believe the convention for errors is to have the prefix `Err` or `Error`. I understand we didn't have that before with the private variable.</strong></li><pre>@@ -14,7 +14,9 @@ import (\n", " )\n", " \n", " var (\n", "-\tBridgeResultMustBeJSONObjectError = errors.New(\"Bridge result must be a valid JSON object\")\n", "+\t// ErrBridgeResultMustBeJSONObject is returned when a Bridge POSTs a non JSON\n", "+\t// object to chainlink\n", "+\tErrBridgeResultMustBeJSONObject = errors.New(\"Bridge result must be a valid JSON object\")\n", " )\n", " \n", " // Bridge adapter is responsible for connecting the task pipeline to external\n", "</pre><hr class=\"wide-line\"><li><strong>144  s/we first see this peer/this is the first time this peer has been seen/g</strong></li><pre>@@ -66,11 +66,12 @@ func (t *transporter) SendAppendEntriesRequest(server *raft.Server, peer *raft.P\n", " \n", " \tdebugf(\"Send LogEntries to %s \", u)\n", " \n", "-\tthisPeerStats, ok := r.peersStats[peer.Name]\n", "+\tthisFollowerStats, ok := r.followersStats.Followers[peer.Name]\n", " \n", "-\tif !ok { // we first see this peer\n", "-\t\tthisPeerStats = &raftPeerStats{MinLatency: 1 << 63}\n", "-\t\tr.peersStats[peer.Name] = thisPeerStats\n", "+\tif !ok { //this is the first time this follower has been seen\n", "+\t\tthisFollowerStats = &raftFollowerStats{}\n", "+\t\tthisFollowerStats.Latency.Minimum = 1 << 63\n", "+\t\tr.followersStats.Followers[peer.Name] = thisFollowerStats\n", " \t}\n", " \n", " \tstart := time.Now()\n", "</pre><hr class=\"wide-line\"><li><strong>145  `iter()` is not needed. And why not return just 6 in `__len__()`?</strong></li><pre>@@ -813,7 +813,7 @@ class uname_result(\n", "         return result\n", " \n", "     def __getitem__(self, key):\n", "-        return tuple(iter(self))[key]\n", "+        return tuple(self)[key]\n", " \n", "     def __len__(self):\n", "         return len(tuple(iter(self)))\n", "</pre><hr class=\"wide-line\"><li><strong>146  this is just a circular import that's grabbing the locally-defined function. From osf_tests.utils.py: ``` from .factories import ( get_default_metaschema, RegistrationProviderFactory, DraftRegistrationFactory, ) ```</strong></li><pre>@@ -533,8 +533,6 @@ class DraftRegistrationFactory(DjangoModelFactory):\n", " \n", "     @classmethod\n", "     def _create(cls, *args, **kwargs):\n", "-        from osf_tests.utils import get_default_metaschema\n", "-\n", "         title = kwargs.pop('title', None)\n", "         initiator = kwargs.get('initiator', None)\n", "         description = kwargs.pop('description', None)\n", "</pre><hr class=\"wide-line\"><li><strong>147  ```suggestion // keybindings on the keyUp, then we'll still send the keydown to the ```</strong></li><pre>@@ -773,7 +773,7 @@ namespace winrt::Microsoft::Terminal::TerminalControl::implementation\n", "         //\n", "         // GH#4999: Only process keybindings on the keydown. If we don't check\n", "         // this at all, we'll process the keybinding twice. If we only process\n", "-        // keybindings on the keyUp, then we'll still sand the keydown to the\n", "+        // keybindings on the keyUp, then we'll still send the keydown to the\n", "         // connected terminal application, and something like ctrl+shift+T will\n", "         // emit a ^T to the pipe.\n", "         if (!modifiers.IsAltGrPressed() && keyDown)\n", "</pre><hr class=\"wide-line\"><li><strong>148  This label is a little inconsistent with the filters in the sidebar (which call them \"Bookmarker's Tags\"). I'm not sure which name is preferred, though.</strong></li><pre>@@ -31,14 +31,14 @@\n", "       <%= f.text_field :bookmark_notes %>\n", "     </dd>\n", "     <dt>\n", "-      <%= f.label :bookmarkable_tag, ts(\"Bookmarked Item's Tags\") %>\n", "+      <%= f.label :bookmarkable_tag, ts(\"Bookmarked item's tags\") %>\n", "       <%= link_to_help \"bookmark-search-tag-help\" %>\n", "     </dt>\n", "     <dd>\n", "       <%= f.text_field :bookmarkable_tag %>\n", "     </dd>\n", "     <dt>\n", "-      <%= f.label :bookmark_tag, ts(\"Bookmark's Tags\") %>\n", "+      <%= f.label :bookmark_tag, ts(\"Bookmarker's tags\") %>\n", "       <%= link_to_help \"bookmark-search-tag-help\" %>\n", "     </dt>\n", "     <dd>\n", "</pre><hr class=\"wide-line\"><li><strong>149  Or `ClassCastException` I suppose, theoretically. Maybe just catch `Exception` already.</strong></li><pre>@@ -195,7 +195,7 @@ public final class JDK extends ToolInstallation implements NodeSpecific<JDK>, En\n", "                 return Collections.singletonList(constructor.newInstance(null, false));\n", "             } catch (ClassNotFoundException e) {\n", "                 return Collections.emptyList();\n", "-            } catch (IllegalAccessException | InstantiationException | InvocationTargetException | NoSuchMethodException e) {\n", "+            } catch (Exception e) {\n", "                 LOGGER.log(Level.WARNING, \"Unable to get default installer\", e);\n", "                 return Collections.emptyList();\n", "             }\n", "</pre><hr class=\"wide-line\"><li><strong>150  if we add abstract method we break the backward compatibility (because every child of this class must implement this method). How about implementing it for now with some \"not implemented\" exception?</strong></li><pre>@@ -45,13 +45,6 @@ public abstract class JdbcDatabaseContainer<SELF extends JdbcDatabaseContainer<S\n", "      */\n", "     public abstract String getJdbcUrl();\n", " \n", "-    /**\n", "-     * @return the database name\n", "-     */\n", "-    public String getDatabaseName() {\n", "-        throw new UnsupportedOperationException();\n", "-    }\n", "-\n", "     /**\n", "      * @return the standard database username that should be used for connections\n", "      */\n", "</pre><hr class=\"wide-line\"><li><strong>151  maybe it's better to use \"DefTiDBMaxChunkSize\" to replace the magic number \"32\"?</strong></li><pre>@@ -677,8 +677,8 @@ func upgradeToVer24(s Session) {\n", " \n", " // upgradeToVer25 updates tidb_max_chunk_size to new low bound value 32 if previous value is small than 32.\n", " func upgradeToVer25(s Session) {\n", "-\tsql := fmt.Sprintf(\"UPDATE HIGH_PRIORITY %s.%s SET VARIABLE_VALUE = '32' WHERE VARIABLE_NAME = '%s' AND VARIABLE_VALUE < 32\",\n", "-\t\tmysql.SystemDB, mysql.GlobalVariablesTable, variable.TiDBMaxChunkSize)\n", "+\tsql := fmt.Sprintf(\"UPDATE HIGH_PRIORITY %[1]s.%[2]s SET VARIABLE_VALUE = '%[4]d' WHERE VARIABLE_NAME = '%[3]s' AND VARIABLE_VALUE < %[4]d\",\n", "+\t\tmysql.SystemDB, mysql.GlobalVariablesTable, variable.TiDBMaxChunkSize, variable.DefInitChunkSize)\n", " \tmustExecute(s, sql)\n", " }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>152  You can remove this line. Manpages are compressed automatically when building the package.</strong></li><pre>@@ -25,6 +25,5 @@ class Testdisk < Package\n", " \n", "   def self.install\n", "     system 'make', \"DESTDIR=#{CREW_DEST_DIR}\", 'install'\n", "-    system \"compressdoc --gzip -9 #{CREW_DEST_PREFIX}/share/man/man8\"\n", "   end\n", " end\n", "</pre><hr class=\"wide-line\"><li><strong>153  Read system property statically to constant.</strong></li><pre>@@ -775,8 +775,4 @@ public class DefaultHttpRequester extends AbstractNonBlockingMessageProcessor im\n", "         return securityFieldDebugInfo;\n", "     }\n", " \n", "-    private void setRetryAttempts ()\n", "-    {\n", "-        RETRY_ATTEMPTS = getInteger(RETRY_ATTEMPTS_PROPERTY, DEFAULT_RETRY_ATTEMPTS);\n", "-    }\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>154  You can use `Date.now()` here too (and below)</strong></li><pre>@@ -65,14 +65,14 @@ define(function (require, exports, module) {\n", "             });\n", " \n", "             it(\"should send data to server\", function () {\n", "-                PreferencesManager.setViewState(\"lastTimeSentHealthData\", (new Date()).getTime() - ONE_DAY);\n", "+                PreferencesManager.setViewState(\"lastTimeSentHealthData\", Date.now() - ONE_DAY);\n", "                 PreferencesManager.setViewState(\"healthDataNotificationShown\", true);\n", "                 var promise = HealthDataManager.checkHealthDataSend();\n", "                 waitsForDone(promise, \"Send Data to Server\", 4000);\n", "             });\n", " \n", "             it(\"should not send data to server\", function () {\n", "-                PreferencesManager.setViewState(\"lastTimeSentHealthData\", (new Date()).getTime() - ONE_DAY);\n", "+                PreferencesManager.setViewState(\"lastTimeSentHealthData\", Date.now() - ONE_DAY);\n", "                 prefs.set(\"healthDataTracking\", false);\n", "                 var promise = HealthDataManager.checkHealthDataSend();\n", "                 waitsForFail(promise, \"Send Data to Server\", 4000);\n", "</pre><hr class=\"wide-line\"><li><strong>155  same ```suggestion Span errorSpan = NettyHttpClientTracer.TRACER.startSpan(\"CONNECT\", Kind.CLIENT); ```</strong></li><pre>@@ -104,7 +104,7 @@ public class ChannelFutureListenerInstrumentation extends Instrumenter.Default {\n", "         return null;\n", "       }\n", "       Scope parentScope = currentContextWith(parentSpan);\n", "-      Span errorSpan = NettyHttpClientTracer.TRACER.startSpan(\"CONNECT\", Kind.INTERNAL);\n", "+      Span errorSpan = NettyHttpClientTracer.TRACER.startSpan(\"CONNECT\", Kind.CLIENT);\n", "       NettyHttpClientTracer.TRACER.endExceptionally(errorSpan, cause);\n", "       return parentScope;\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>156  Why do we need an `io.Reader` here? We should rather not break the existing API(s) and require an `io.Reader` just to get an address. If it's a must, then the `io.Reader` should already exist or be set on the context.</strong></li><pre>@@ -63,7 +63,7 @@ func NewCLIContextWithFrom(from string, input io.Reader) CLIContext {\n", " \tvar rpc rpcclient.Client\n", " \n", " \tgenOnly := viper.GetBool(flags.FlagGenerateOnly)\n", "-\tfrom<PERSON><PERSON><PERSON>, from<PERSON><PERSON>, err := GetFromFields(from, genOnly, viper.GetBool((flags.FlagSecretStore)), input)\n", "+\tfrom<PERSON><PERSON><PERSON>, from<PERSON><PERSON>, err := GetFromFields(from, genOnly, input)\n", " \tif err != nil {\n", " \t\tfmt.Printf(\"failed to get from fields: %v\", err)\n", " \t\tos.Exit(1)\n", "</pre><hr class=\"wide-line\"><li><strong>157  The conditional on `defined(__linux__)` got lost here; please leave it in.</strong></li><pre>@@ -10,7 +10,7 @@\n", " #include \"Util.h\"\n", " #include \"WasmExecutor.h\"\n", " \n", "-#if defined(__powerpc__)\n", "+#if defined(__powerpc__) && (defined(__FreeBSD__) || defined(__linux__))\n", " #if defined(__FreeBSD__)\n", " #include <machine/cpu.h>\n", " #include <sys/elf_common.h>\n", "</pre><hr class=\"wide-line\"><li><strong>158  We don't need this. With python args you can use a substring of the arg and it will work. For example you can say `paasta restart --serv foo` Just make a \"--clusters\" arg and it will automatically be reference-able as -- cluster.</strong></li><pre>@@ -62,12 +62,6 @@ def add_subparser(subparsers):\n", "             help=\"A comma-separated list of clusters to view. Defaults to view all clusters.\\n\"\n", "             \"For example: --clusters norcal-prod,nova-prod\"\n", "         ).completer = lazy_choices_completer(list_clusters)\n", "-        status_parser.add_argument(\n", "-            '--cluster',\n", "-            choices=list_clusters(),\n", "-            help=\"A single cluster to view.\\n\"\n", "-            \" Deprecated! For example: --cluster norcal-prod.\"\n", "-        ).completer = lazy_choices_completer(list_clusters)\n", " \n", "         status_parser.add_argument(\n", "             '-d', '--soa-dir',\n", "</pre><hr class=\"wide-line\"><li><strong>159  ClassType, not StrongTypePtr. Call the function object_loader.</strong></li><pre>@@ -116,7 +116,7 @@ IValue ScriptModuleDeserializer::readArchive(const std::string& archive_name) {\n", "   // Decouple how to get obj from type. In this file it's dependent on\n", "   // Method.run() and graph executor, etc.\n", "   // For bytecode import we need to decouple these dependencies.\n", "-  auto attr_retriever = [&](at::StrongTypePtr type, IValue input) {\n", "+  auto obj_loader = [&](at::StrongTypePtr type, IValue input) {\n", "     auto cls = type.type_->expect<at::ClassType>();\n", "     size_t n = cls->numAttributes();\n", "     if (checkHasValidSetGetState(type.type_)) {\n", "</pre><hr class=\"wide-line\"><li><strong>160  This doesn't seem to be used anywhere.</strong></li><pre>@@ -1,4 +0,0 @@\n", "-SNAPSHOT_PAGE = {\n", "-    'dashboard': True,\n", "-    'project': True\n", "-}\n", "\\ No newline at end of file\n", "</pre><hr class=\"wide-line\"><li><strong>161  Might be worth logging the exception?</strong></li><pre>@@ -918,7 +918,7 @@ def _namespaced_get_classic_service_information_for_nerve(name, namespace, soa_d\n", "     try:\n", "         with open(_get_classic_service_puppet_file(name)) as extras:\n", "             nerve_dict.update(json.load(extras))\n", "-    except (<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>):\n", "+    except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r):\n", "         pass\n", "     return (nerve_name, nerve_dict)\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>162  Is this `false` because `getCurrentGroup` will not work until joining?</strong></li><pre>@@ -720,7 +720,7 @@ public class RaftJournalSystem extends AbstractJournalSystem {\n", "             .setHost(localAddress.getHostString())\n", "             .setRpcPort(localAddress.getPort()))\n", "         .build();\n", "-    RaftClient client = createClient(false);\n", "+    RaftClient client = createClient();\n", "     client.sendReadOnlyAsync(Message.valueOf(\n", "         UnsafeByteOperations.unsafeWrap(\n", "             JournalQueryRequest\n", "</pre><hr class=\"wide-line\"><li><strong>163  This name is confusing. It should be something like `override_baseurl`</strong></li><pre>@@ -22,7 +22,7 @@ DOMAIN = 'doorbird'\n", " API_URL = '/api/{}'.format(DOMAIN)\n", " \n", " CONF_DOORBELL_EVENTS = 'doorbell_events'\n", "-CONF_CUSTOM_URL = 'push_to_host'\n", "+CONF_CUSTOM_URL = 'hass_url_override'\n", " \n", " CONFIG_SCHEMA = vol.Schema({\n", "     DOMAIN: vol.Schema({\n", "</pre><hr class=\"wide-line\"><li><strong>164  Let's add `validateRunsInMainThread` as a first statement. That way we enforce that this method really runs in the main thread context.</strong></li><pre>@@ -1517,6 +1517,7 @@ public class TaskExecutor extends RpcEndpoint implements TaskExecutorGateway {\n", " \n", " \t\t@Override\n", " \t\tpublic CompletableFuture<AccumulatorReport> retrievePayload(ResourceID resourceID) {\n", "+\t\t\tvalidateRunsInMainThread();\n", " \t\t\tJobManagerConnection jobManagerConnection = jobManagerConnections.get(resourceID);\n", " \t\t\tif (jobManagerConnection != null) {\n", " \t\t\t\tJobID jobId = jobManagerConnection.getJobID();\n", "</pre><hr class=\"wide-line\"><li><strong>165  I guess it's this offset that should be proportional to the hairpin length somehow. Make sense ?</strong></li><pre>@@ -121,6 +121,8 @@ void HairpinSegment::updateGrips(int* grips, QRectF* grip) const\n", "       doRotation.rotateRadians( asin(y/len) );\n", "       qreal lineApertureX;\n", "       qreal offsetX = 10;                               // Horizontal offset for x Grip\n", "+      if(len < offsetX * 3 )                            // For small hairpin, offset = 30% of len\n", "+          offsetX = len/3;                              // else offset is fixed to 10\n", " \n", "       if( hairpin()->hairpinType() == 0 )\n", "             lineApertureX = len - offsetX;              // End of CRESCENDO - Offset\n", "</pre><hr class=\"wide-line\"><li><strong>166  Maybe use the empty string as the default ID here?</strong></li><pre>@@ -24,7 +24,8 @@ import ujson as json\n", " \n", " # The category ID for the \"default\" category. We don't store as null in the\n", " # database to avoid the fun of null != null\n", "-_DEFAULT_CATEGORY_ID = \"default\"\n", "+_DEFAULT_CATEGORY_ID = \"\"\n", "+_DEFAULT_ROLE_ID = \"\"\n", " \n", " \n", " class GroupServerStore(SQLBaseStore):\n", "</pre><hr class=\"wide-line\"><li><strong>167  ``` go return c.convertToMap(payload) ``` You have function for converting value to map (you used it in previous lines of code) :)</strong></li><pre>@@ -104,8 +104,8 @@ func (c *eventActivationConverter) getPayload(in interface{}) map[string]interfa\n", " \t\treturn nil\n", " \t}\n", " \n", "-\tresult, ok := payload.(map[string]interface{})\n", "-\tif !ok {\n", "+\tresult, exists := c.convertToMap(payload)\n", "+\tif !exists {\n", " \t\treturn nil\n", " \t}\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>168  Start with uppercase \"O\".</strong></li><pre>@@ -268,7 +268,7 @@ public class SmsController\n", "             }\n", " \n", "             // current user does not belong to this number\n", "-            throw new WebMessageException( WebMessageUtils.conflict( \"originator's number does not match user's phone number\" ) );\n", "+            throw new WebMessageException( WebMessageUtils.conflict( \"Originator's number does not match user's Phone number\" ) );\n", "         }\n", " \n", "         return users.iterator().next();\n", "</pre><hr class=\"wide-line\"><li><strong>169  I would suggest to cache ReverseProxy objects here.</strong></li><pre>@@ -31,6 +31,9 @@ type proxyHandler struct {\n", " \teventServiceHost       string\n", " \tappRegistryPathPrefix  string\n", " \tappRegistryHost        string\n", "+\n", "+\teventsProxy      *httputil.ReverseProxy\n", "+\tappRegistryProxy *httputil.ReverseProxy\n", " }\n", " \n", " func NewProxyHandler(group, tenant, eventServicePathPrefix, eventServiceHost, appRegistryPathPrefix, appRegistryHost string) *proxyHandler {\n", "</pre><hr class=\"wide-line\"><li><strong>170  nit: no need to include the *Agent def for new algorithms, since we're renaming them all to *Trainer.</strong></li><pre>@@ -9,9 +9,8 @@ from ray.rllib.utils import renamed_class\n", " \n", " ApexDDPGAgent = renamed_class(ApexDDPGTrainer)\n", " DDPGAgent = renamed_class(DDPGTrainer)\n", "-TD3Agent = renamed_class(TD3Trainer)\n", " \n", " __all__ = [\n", "-    \"DDPGAgent\", \"ApexDDPGAgent\", \"TD3Agent\", \"DDPGTrainer\", \"ApexDDPGTrainer\",\n", "+    \"DDPGAgent\", \"ApexDDPGAgent\", \"DDPGTrainer\", \"ApexDDPGTrainer\",\n", "     \"TD3Trainer\", \"DEFAULT_CONFIG\"\n", " ]\n", "</pre><hr class=\"wide-line\"><li><strong>171  Again, I don't think we need this duplication</strong></li><pre>@@ -39,13 +39,12 @@ func (s installStep) Run() error {\n", " \n", " \tif installErr != nil {\n", " \t\tinstallErrMsg := fmt.Sprintf(\"Helm install error: %s\", installErr.Error())\n", "-\t\terrorMsg := installErrMsg\n", " \n", " \t\tisDeletable, err := s.helmClient.IsReleaseDeletable(s.component.GetReleaseName())\n", " \t\tif err != nil {\n", "-\t\t\terrMsg := fmt.Sprintf(\"Checking status of %s failed with an error: %s\", s.component.GetReleaseName(), err.<PERSON>())\n", "-\t\t\tlog.Println(errMsg)\n", "-\t\t\treturn errors.New(fmt.Sprintf(\"%s \\n %s \\n\", installErrMsg, errMsg))\n", "+\t\t\tstatusErrMsg = fmt.Sprintf(\"Checking status of %s failed with an error: %s\", s.component.GetReleaseName(), err.<PERSON><PERSON><PERSON>())\n", "+\t\t\tlog.Println(statusErrMsg)\n", "+\t\t\treturn errors.New(fmt.Sprintf(\"%s \\n %s \\n\", installErrMsg, statusErrorMsg))\n", " \t\t}\n", " \n", " \t\tif isDeletable {\n", "</pre><hr class=\"wide-line\"><li><strong>172  fyi, i have heard there are slight numerical differences between the old and new adam optimizers. something to be aware of as you compare convergence etc.</strong></li><pre>@@ -116,6 +116,7 @@ def get_optimizer(params):\n", "       beta_2=params[\"beta2\"],\n", "       epsilon=params[\"epsilon\"])\n", "   if params[\"use_tpu\"]:\n", "+    # TODO: remove this contrib import\n", "     optimizer = tf.contrib.tpu.CrossShardOptimizer(optimizer)\n", " \n", "   return optimizer\n", "</pre><hr class=\"wide-line\"><li><strong>173  This needs a check for the `RUNELITE` Menu Action type, so that it won't trigger while specifying Shift Click options ```suggestion if (event.getWidgetId() != WidgetInfo.INVENTORY.getId() || event.getMenuAction() == MenuAction.RUNELITE) ```</strong></li><pre>@@ -205,7 +205,7 @@ public class MiningPlugin extends Plugin\n", " \t@Subscribe\n", " \tpublic void onMenuOptionClicked(MenuOptionClicked event)\n", " \t{\n", "-\t\tif (event.getWidgetId() != WidgetInfo.INVENTORY.getId())\n", "+\t\tif (event.getWidgetId() != WidgetInfo.INVENTORY.getId() || event.getMenuAction() == MenuAction.RUNELITE)\n", " \t\t{\n", " \t\t\treturn;\n", " \t\t}\n", "</pre><hr class=\"wide-line\"><li><strong>174  Playthroughs are ...</strong></li><pre>@@ -1138,7 +1138,7 @@ class PlaythroughModel(base_models.BaseModel):\n", " \n", "     @staticmethod\n", "     def get_deletion_policy():\n", "-        \"\"\"Playthrough is not related to users.\"\"\"\n", "+        \"\"\"Playthroughs are not related to users.\"\"\"\n", "         return base_models.DELETION_POLICY.NOT_APPLICABLE\n", " \n", "     @classmethod\n", "</pre><hr class=\"wide-line\"><li><strong>175  Can we check all the attributes exported here?</strong></li><pre>@@ -467,7 +467,7 @@ func testAccDataSourceKubernetesCluster_addOnProfileIngressApplicationGateway(t\n", " \t\t\t\tcheck.That(data.ResourceName).Key(\"addon_profile.#\").HasValue(\"1\"),\n", " \t\t\t\tcheck.That(data.ResourceName).Key(\"addon_profile.0.ingress_application_gateway.#\").HasValue(\"1\"),\n", " \t\t\t\tcheck.That(data.ResourceName).Key(\"addon_profile.0.ingress_application_gateway.0.enabled\").HasValue(\"true\"),\n", "-\t\t\t\tcheck.That(data.ResourceName).Key(\"addon_profile.0.ingress_application_gateway.0.effective_application_gateway_id\").Exists(),\n", "+\t\t\t\tcheck.That(data.ResourceName).Key(\"addon_profile.0.ingress_application_gateway.0.effective_gateway_id\").Exists(),\n", " \t\t\t),\n", " \t\t},\n", " \t})\n", "</pre><hr class=\"wide-line\"><li><strong>176  >_lazyGuardedSnapshotManager [](start = 20, length = 27) Consider asserting that it is null #Closed</strong></li><pre>@@ -1434,7 +1434,8 @@ namespace Microsoft.CodeAnalysis.CSharp\n", "                 Debug.Assert(manager == null || !IsSpeculativeSemanticModel);\n", "                 if (!IsSpeculativeSemanticModel)\n", "                 {\n", "-                    _lazyGuardedSnapshotManager = manager;\n", "+                    Debug.Assert(_lazySnapshotManager is null);\n", "+                    _lazySnapshotManager = manager;\n", "                 }\n", "             }\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>177  This stores a pointer to a temporary object. This pointer becomes invalid right after this statement. I suggest you initialise it with a `nullptr` and check for that.</strong></li><pre>@@ -4887,7 +4887,7 @@ Character::comfort_response_t Character::base_comfort_value( const tripoint &p )\n", "     int comfort = 0;\n", " \n", "     comfort_response_t comfort_response;\n", "-    comfort_response.aid = &item( \"null\" );\n", "+\n", "     bool plantsleep = has_trait( trait_CHLOROMORPH );\n", "     bool fungaloid_cosplay = has_trait( trait_M_SKIN3 );\n", "     bool websleep = has_trait( trait_WEB_WALKER );\n", "</pre><hr class=\"wide-line\"><li><strong>178  The struct itself seems just like `ProcessTableInfo`. Maybe it could have a better name than \"orphan\"?</strong></li><pre>@@ -53,11 +53,6 @@ type ChangeFeedInfoRWriter interface {\n", " \tWrite(ctx context.Context, infos map[model.ChangeFeedID]*model.ChangeFeedInfo) error\n", " }\n", " \n", "-type orphanTable struct {\n", "-\tschema.TableName\n", "-\tstartTs uint64\n", "-}\n", "-\n", " type ChangeFeedInfo struct {\n", " \tID     string\n", " \tdetail *model.ChangeFeedDetail\n", "</pre><hr class=\"wide-line\"><li><strong>179  `this` should be `parser` here</strong></li><pre>@@ -26,7 +26,7 @@ const REPLACEMENT_TYPES = {\n", " \t__webpack_nonce__: \"string\" // eslint-disable-line camelcase\n", " };\n", " \n", "-let IGNORES = [];\n", "+const IGNORES = [];\n", " \n", " class APIPlugin {\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>180  any attribute validation that can be added to these assertions?</strong></li><pre>@@ -37,7 +37,9 @@ class TestCustomResource(KubeTest):\n", "         )\n", " \n", "         resources = policy.run()\n", "-        self.assertTrue(resources)\n", "+        self.assertTrue(len(resources), 1)\n", "+        self.assertEqual(resources[0]['apiVersion'], 'stable.example.com/v1')\n", "+        self.assertEqual(resources[0]['kind'], 'CronTabCluster')\n", " \n", "     def test_custom_namespaced_resource_query(self):\n", "         factory = self.replay_flight_data()\n", "</pre><hr class=\"wide-line\"><li><strong>181  This is a test case. Lets throw an error instead of logging this out.</strong></li><pre>@@ -65,8 +65,12 @@ public class ExamplesOrganizationClonerTests {\n", " \n", "         Organization newOrganization = new Organization();\n", "         newOrganization.setName(\"Template Organization\");\n", "-        final Mono<Organization> requiredDataMono = organizationService.create(newOrganization)\n", "+        final Mono<Organization> organizationMono = organizationService.create(newOrganization)\n", "             .flatMap(organization -> {\n", "+                if (organization.getId() == null) {\n", "+                    return Mono.error(new RuntimeException(\"Created templates organization doesn't have an ID.\"));\n", "+                }\n", "+\n", "                 Application app1 = new Application();\n", "                 app1.setName(\"1 - public app\");\n", "                 app1.setOrganizationId(organization.getId());\n", "</pre><hr class=\"wide-line\"><li><strong>182  I'd rather call this Activated to be consistent with Builds/Tasks</strong></li><pre>@@ -37,7 +37,7 @@ type Version struct {\n", " \tBranch              string               `bson:\"branch_name\" json:\"branch_name,omitempty\"`\n", " \tBuildVariants       []VersionBuildStatus `bson:\"build_variants_status,omitempty\" json:\"build_variants_status,omitempty\"`\n", " \tPeriodicBuildID     string               `bson:\"periodic_build_id,omitempty\" json:\"periodic_build_id,omitempty\"`\n", "-\tIsActive            bool                 `bson:\"is_active\" json:\"is_active,omitempty\"`\n", "+\tActivated           bool                 `bson:\"activated\" json:\"activated,omitempty\"`\n", " \n", " \t// GitTags stores tags that were pushed to this version, while TriggeredByGitTag is for versions created by tags\n", " \tGitTags           []GitTag `bson:\"git_tags,omitempty\" json:\"git_tags,omitempty\"`\n", "</pre><hr class=\"wide-line\"><li><strong>183  Should create a new test case here: ```c++ TEST_F(FileTest, MatrixTypesMajornessZpr) { runFileTest(\"type.matrix.majorness.zpr.hlsl\"); } ```</strong></li><pre>@@ -45,9 +45,11 @@ TEST_F(WholeFileTest, EmptyStructInterfaceVS) {\n", " TEST_F(FileTest, ScalarTypes) { runFileTest(\"type.scalar.hlsl\"); }\n", " TEST_F(FileTest, VectorTypes) { runFileTest(\"type.vector.hlsl\"); }\n", " TEST_F(FileTest, MatrixTypes) { runFileTest(\"type.matrix.hlsl\"); }\n", "+TEST_F(FileTest, MatrixTypesMajornessZpr) { \n", "+  runFileTest(\"type.matrix.majorness.zpr.hlsl\");\n", "+}\n", " TEST_F(FileTest, MatrixTypesMajorness) {\n", "   runFileTest(\"type.matrix.majorness.hlsl\", FileTest::Expect::Warning);\n", "-  runFileTest(\"type.matrix.default_majorness.hlsl\");\n", " }\n", " TEST_F(FileTest, StructTypes) { runFileTest(\"type.struct.hlsl\"); }\n", " TEST_F(FileTest, ClassTypes) { runFileTest(\"type.class.hlsl\"); }\n", "</pre><hr class=\"wide-line\"><li><strong>184  ```suggestion f.StringSliceVarP(&opts.TargetImages, \"build-image\", \"b\", nil, \"Only build artifacts with image names that contain the given substring. Default is to build sources for all artifacts\") ``` nit: I know that you took the same text as the existing `build-image` flag on `build`. But the grammar seems off.</strong></li><pre>@@ -37,9 +37,6 @@ func NewCmdRun() *cobra.Command {\n", " \t\tWithExample(\"Build, test, deploy and tail the logs\", \"run --tail\").\n", " \t\tWithExample(\"Run with a given profile\", \"run -p <profile>\").\n", " \t\tWithCommonFlags().\n", "-\t\tWithFlags(func(f *pflag.FlagSet) {\n", "-\t\t\tf.String<PERSON>liceVarP(&opts.TargetImages, \"build-image\", \"b\", nil, \"Choose which artifacts to build. Artifacts with image names that contain the expression will be built only. Default is to build sources for all artifacts\")\n", "-\t\t}).\n", " \t\tWithHouseKeepingMessages().\n", " \t\tNoArgs(doRun)\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>185  Can this be 200ms? 2s is a long time</strong></li><pre>@@ -143,7 +143,6 @@ func TestStaticPeering_PeersAreAdded(t *testing.T) {\n", " \ts.dv5Listener = &mockListener{}\n", " \tdefer s.<PERSON>()\n", " \n", "-\ttime.Sleep(2 * time.Second)\n", " \tpeers := s.host.Network().Peers()\n", " \tif len(peers) != 5 {\n", " \t\t<PERSON><PERSON>(\"Not all peers added to peerstore, wanted %d but got %d\", 5, len(peers))\n", "</pre><hr class=\"wide-line\"><li><strong>186  why you removed this?</strong></li><pre>@@ -118,6 +118,12 @@ ngraph::pass::ConvStridesPropagation::ConvStridesPropagation() {\n", "             auto conv_input = conv->input(0);\n", "             insert_strides_prop(conv_input, conv_strides);\n", "         } else {\n", "+            // Retain original padding\n", "+            // Make sure that setting strides does not change padding in cases when auto_pad is not EXPLICIT.\n", "+            // When padding type is not EXPLICIT, strides make a role to paddings calculation.\n", "+            // Change in padding, results in change in image position that filter is applied,\n", "+            // so we may end up with unwanted results after that.\n", "+            conv->set_auto_pad(op::PadType::EXPLICIT);\n", "             conv->set_strides(conv_strides);\n", "         }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>187  Does this break previous clients somehow? Can we just replace those or we need to have a version where we report both? @mattmoor, what's your take?</strong></li><pre>@@ -68,8 +68,8 @@ func extractData(body io.Reader) (*Stat, error) {\n", " \tfor m, pv := range map[string]*float64{\n", " \t\t\"queue_average_concurrent_requests\":         &stat.AverageConcurrentRequests,\n", " \t\t\"queue_average_proxied_concurrent_requests\": &stat.AverageProxiedConcurrentRequests,\n", "-\t\t\"queue_requests_per_second\":                 &stat.RequestCount,\n", "-\t\t\"queue_proxied_requests_per_second\":         &stat.ProxiedRequestCount,\n", "+\t\t\"queue_operations_per_second\":               &stat.RequestCount,\n", "+\t\t\"queue_proxied_operations_per_second\":       &stat.ProxiedRequestCount,\n", " \t} {\n", " \t\tpm := prometheusMetric(metricFamilies, m)\n", " \t\tif pm == nil {\n", "</pre><hr class=\"wide-line\"><li><strong>188  maybe set `\"/validatorprivatekey\"` to be a global variable?</strong></li><pre>@@ -40,7 +40,7 @@ type Config struct {\n", " // registry.\n", " func NewValidatorService(ctx context.Context, cfg *Config) (*ValidatorService, error) {\n", " \tctx, cancel := context.WithCancel(ctx)\n", "-\tvalidatorKeyFile := cfg.KeystorePath + \"/validatorprivatekey\"\n", "+\tvalidatorKeyFile := cfg.KeystorePath + params.BeaconConfig().ValidatorPrivkeyFileName\n", " \tks := keystore.NewKeystore(cfg.KeystorePath)\n", " \tkey, err := ks.<PERSON>(validator<PERSON>eyFile, cfg.Password)\n", " \tif err != nil {\n", "</pre><hr class=\"wide-line\"><li><strong>189  why public in a test case... in either case I would expect the one at NoListenerRequestHandler to be public or protected and this one to don't exist</strong></li><pre>@@ -38,7 +38,6 @@ public class HttpListenerConfigFunctionalTestCase extends FunctionalTestCase\n", "             \"([01]?\\\\d\\\\d?|2[0-4]\\\\d|25[0-5])\\\\.\" +\n", "             \"([01]?\\\\d\\\\d?|2[0-4]\\\\d|25[0-5])$\");\n", "     private static final int TIMEOUT = 1000;\n", "-    public static final String RESOURCE_NOT_FOUND = \"Resource not found.\";\n", " \n", "     @Rule\n", "     public DynamicPort fullConfigPort = new DynamicPort(\"fullConfigPort\");\n", "</pre><hr class=\"wide-line\"><li><strong>190  `adduser` is a bit disappointing here. Thanks for fixing it.</strong></li><pre>@@ -51,7 +51,7 @@ func AddExtraSudoUser(name string, sshKeys []string, gecos string) error {\n", " \t\treturn fmt.Errorf(\"adduser failed with %s: %s\", err, output)\n", " \t}\n", " \n", "-\tcmd = exec.Command(\"adduser\", name, \"sudo\")\n", "+\tcmd = exec.Command(\"adduser\", \"--extrausers\", name, \"sudo\")\n", " \tif output, err := cmd.CombinedOutput(); err != nil {\n", " \t\treturn fmt.Errorf(\"adding user to sudo group failed with %s: %s\", err, output)\n", " \t}\n", "</pre><hr class=\"wide-line\"><li><strong>191  I think an IndexOutOfBoundsException would be better here</strong></li><pre>@@ -163,7 +163,7 @@ public abstract class EntityEquipmentInvWrapper implements IItemHandlerModifiabl\n", "     protected EntityEquipmentSlot validateSlotIndex(final int slot)\n", "     {\n", "         if (slot < 0 || slot >= slots.size())\n", "-            throw new RuntimeException(\"Slot \" + slot + \" not in valid range - [0,\" + slots.size() + \")\");\n", "+            throw new IllegalArgumentException(\"Slot \" + slot + \" not in valid range - [0,\" + slots.size() + \")\");\n", " \n", "         return slots.get(slot);\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>192  Should it be set as a member variable of `HashJoinExec`?</strong></li><pre>@@ -743,7 +743,7 @@ func (e *HashJoinExec) doBuild(workerID int, finishedCh chan error) {\n", " // key of hash table: hash value of key columns\n", " // value of hash table: RowPtr of the corresponded row\n", " func (e *HashJoinExec) buildGlobalHashTable() error {\n", "-\te.globalHashTable = mvmap.NewMVMap()\n", "+\te.hashTables[0] = mvmap.NewMVMap()\n", " \te.innerKeyColIdx = make([]int, len(e.innerKeys))\n", " \tfor i := range e.innerKeys {\n", " \t\te.innerKeyColIdx[i] = e.innerKeys[i].Index\n", "</pre><hr class=\"wide-line\"><li><strong>193  Would `Vector3d(m_PosX, m_PosY, m_PosZ)` work instead?</strong></li><pre>@@ -75,7 +75,7 @@ void cDropSpenserEntity::DropSpense(cChunk & a_Chunk)\n", " \tif (SlotsCnt == 0)\n", " \t{\n", " \t\t// Nothing in the dropspenser, play the click sound\n", "-\t\tm_World->BroadcastSoundEffect(\"block.dispenser.fail\", {static_cast<double>(m_PosX), static_cast<double>(m_PosY), static_cast<double>(m_PosZ)}, 1.0f, 1.2f);\n", "+\t\tm_World->BroadcastSoundEffect(\"block.dispenser.fail\", Vector3d(m_PosX, m_PosY, m_PosZ), 1.0f, 1.2f);\n", " \t\treturn;\n", " \t}\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>194  error should be more readable: tm -> tabletmanager?</strong></li><pre>@@ -30,7 +30,7 @@ func init() {\n", " \tservenv.OnR<PERSON>(func() {\n", " \t\thttp.Handle(\"/healthz\", http.HandlerFunc(func(rw http.ResponseWriter, r *http.Request) {\n", " \t\t\tif _, err := tm.Healthy(); err != nil {\n", "-\t\t\t\thttp.Error(rw, fmt.Sprintf(\"500 internal server error: tm not healthy: %v\", err), http.StatusInternalServerError)\n", "+\t\t\t\thttp.Error(rw, fmt.Sprintf(\"500 internal server error: tablet manager not healthy: %v\", err), http.StatusInternalServerError)\n", " \t\t\t\treturn\n", " \t\t\t}\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>195  Nit: Break after the parenthesis '('. This should fix the lint failure.</strong></li><pre>@@ -1274,8 +1274,9 @@ class State(object):\n", "             interaction.hints, interaction.solution)\n", "         self.classifier_model_id = classifier_model_id\n", " \n", "-    def validate(self, exp_param_specs_dict, allow_null_interaction,\n", "-                 is_question=False):\n", "+    def validate(\n", "+            self, exp_param_specs_dict, allow_null_interaction,\n", "+            is_question=False):\n", "         \"\"\"Validates various properties of the State.\n", " \n", "         Args:\n", "</pre><hr class=\"wide-line\"><li><strong>196  Why not just \"syscall\" ? We use it as syscall everywhere else IIRC so unless there is a deeper reason I think I would prefer that we just use it here too.</strong></li><pre>@@ -26,13 +26,14 @@ import (\n", " \t\"net/http\"\n", " \t\"os\"\n", " \t\"sync\"\n", "-\tsys \"syscall\"\n", "+\t\"syscall\"\n", " \t\"time\"\n", " \n", " \t\"github.com/gorilla/mux\"\n", " \t\"gopkg.in/tomb.v2\"\n", " \n", " \t\"github.com/snapcore/snapd/dirs\"\n", "+\t\"github.com/snapcore/snapd/logger\"\n", " \t\"github.com/snapcore/snapd/netutil\"\n", " \t\"github.com/snapcore/snapd/systemd\"\n", " )\n", "</pre><hr class=\"wide-line\"><li><strong>197  Any idea how risky it is to leave these around indefinitely?</strong></li><pre>@@ -451,6 +451,10 @@ func (txmp *TxMempool) Update(\n", " \t// TODO: Purge transactions from height and timestamp indexes that have\n", " \t// exceeded their TTL.\n", " \n", "+\t// purge all transactions that have expired their height-based TTL\n", "+\n", "+\t// purge all transactions that have expired their time-based TTL\n", "+\n", " \t// If there any uncommitted transactions left in the mempool, we either\n", " \t// initiate re-CheckTx per remaining transaction or notify that remaining\n", " \t// transactions are left.\n", "</pre><hr class=\"wide-line\"><li><strong>198  Use `!==` please :)</strong></li><pre>@@ -191,7 +191,7 @@ FileActions.register('all', 'Rename', OC.PERMISSION_UPDATE, function () {\n", " \n", " FileActions.register('dir', 'Open', OC.PERMISSION_READ, '', function (filename) {\n", " \tvar dir = $('#dir').val()\n", "-\tif (dir != '/') {\n", "+\tif (dir !== '/') {\n", " \t\tdir = dir + '/';\n", " \t}\n", " \twindow.location = OC.linkTo('files', 'index.php') + '?dir=' + encodeURIComponent(dir + filename);\n", "</pre><hr class=\"wide-line\"><li><strong>199  Why callback and promise? Can we keep it with promise only?</strong></li><pre>@@ -143,7 +143,6 @@ export function settings() {\n", " \t};\n", " }\n", " \n", "-export async function getExtraConfigInfo(room, callback) {\n", "-\tconst extraConfig = await callbacks.run('livechat.onLoadConfigApi', room);\n", "-\tcallback && callback(null, extraConfig);\n", "+export async function getExtraConfigInfo(room) {\n", "+\treturn callbacks.run('livechat.onLoadConfigApi', room);\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>200  Should this live in the versioner configuration?</strong></li><pre>@@ -42,8 +42,7 @@ type FolderConfiguration struct {\n", " \tAutoNormalize           bool                        `xml:\"autoNormalize,attr\" json:\"autoNormalize\" default:\"true\"`\n", " \tMinDiskFree             Size                        `xml:\"minDiskFree\" json:\"minDiskFree\" default:\"1%\"`\n", " \tVersioning              VersioningConfiguration     `xml:\"versioning\" json:\"versioning\"`\n", "-\tVersionCleanupIntervalS int                         `xml:\"versionCleanupIntervalS\" json:\"versionCleanupIntervalS\" default:\"3600\"` // Follows same min/max as scan interval\n", "-\tCopiers                 int                         `xml:\"copiers\" json:\"copiers\"`                                                // This defines how many files are handled concurrently.\n", "+\tCopiers                 int                         `xml:\"copiers\" json:\"copiers\"` // This defines how many files are handled concurrently.\n", " \tPullerMaxPendingKiB     int                         `xml:\"pullerMaxPendingKiB\" json:\"pullerMaxPendingKiB\"`\n", " \tHashers                 int                         `xml:\"hashers\" json:\"hashers\"` // Less than one sets the value to the number of cores. These are CPU bound due to hashing.\n", " \tOrder                   PullOrder                   `xml:\"order\" json:\"order\"`\n", "</pre><hr class=\"wide-line\"><li><strong>201  ```suggestion - All tasks must be typeset (see typeset_dask_graph()) ``` I have no idea if \"typeset\" is correct here, but I have a feeling that \"typesetted\" is not a word.</strong></li><pre>@@ -346,7 +346,7 @@ class Layer(collections.abc.Mapping):\n", "           - Serializable by msgpack (notice, msgpack converts lists to tuples)\n", "           - All remote data must be unpacked (see unpack_remotedata())\n", "           - All keys must be converted to strings now or when unpacking\n", "-          - All tasks must be typesetted (see typeset_dask_graph())\n", "+          - All tasks must be typeset (see typeset_dask_graph())\n", " \n", "         The default implementation materialize the layer thus layers such as Blockwise\n", "         and <PERSON>ffle<PERSON><PERSON><PERSON> should implement a specialized pack and unpack function in\n", "</pre><hr class=\"wide-line\"><li><strong>202  State when this happens (see other examples in codebase).</strong></li><pre>@@ -66,9 +66,10 @@ def try_upgrading_draft_to_exp_version(\n", "                 commit.commit_cmds[0]['cmd'] ==\n", "                 exp_domain.CMD_MIGRATE_STATES_SCHEMA_TO_LATEST_VERSION):\n", " \n", "-            conversion_fn_name = '_convert_v%s_dict_to_v%s_dict' % (\n", "-                (current_draft_version + upgrade_times),\n", "-                (current_draft_version + upgrade_times + 1))\n", "+\n", "+            conversion_fn_name = '_convert_states_v%s_dict_to_v%s_dict' % (\n", "+                commit.commit_cmds[0]['from_version'],\n", "+                commit.commit_cmds[0]['to_version'])\n", "             if not hasattr(DraftUpgradeUtil, conversion_fn_name):\n", "                 logging.warning('%s is not implemented' % conversion_fn_name)\n", "                 return\n", "</pre><hr class=\"wide-line\"><li><strong>203  <PERSON><PERSON> confused why we encode as 'utf-8' here, and avoid it above. Could we move the handling of 'utf-8' inside of the `FileCache` class instead?</strong></li><pre>@@ -1716,8 +1716,7 @@ class LintChecksManager(object):\n", "             docstring_checker = docstrings_checker.ASTDocStringChecker()\n", "             for filepath in files_to_check:\n", "                 ast_file = ast.walk(\n", "-                    ast.parse(FileCache.read(filepath).encode('utf-8',\n", "-                                                              'ignore')))\n", "+                    ast.parse(FileCache.read(filepath).encode('utf-8')))\n", "                 func_defs = [n for n in ast_file if isinstance(\n", "                     n, ast.FunctionDef)]\n", "                 for func in func_defs:\n", "</pre><hr class=\"wide-line\"><li><strong>204   `gcsReferenceFileWithRelayMode`, `s3ReferenceFileWithRelayMode`  file-uploader  delivery  </strong></li><pre>@@ -46,14 +46,14 @@ export default class AdminAppContainer extends Container {\n", "       envGcsBucket: '',\n", "       gcsUploadNamespace: '',\n", "       envGcsUploadNamespace: '',\n", "-      gcsIsEnabledInternalStreamSystem: false,\n", "+      gcsReferenceFileWithRelayMode: false,\n", " \n", "       s3Region: '',\n", "       s3CustomEndpoint: '',\n", "       s3Bucket: '',\n", "       s3AccessKeyId: '',\n", "       s3SecretAccessKey: '',\n", "-      s3IsEnabledInternalStreamSystem: false,\n", "+      s3ReferenceFileWithRelayMode: false,\n", " \n", "       isEnabledPlugins: true,\n", "     };\n", "</pre><hr class=\"wide-line\"><li><strong>205  Hi @ad<PERSON><PERSON><PERSON><PERSON> would this give the same result hence making the `isEmpty()` redundant? Let me know your thoughts. Thanks   ```suggestion <PERSON><PERSON>an isPreviousPasswordBlank = previousPassword.trim().isEmpty(); ```</strong></li><pre>@@ -749,7 +749,7 @@ public class EditPostSettingsFragment extends Fragment {\n", "         String trimmedPassword = password.trim();\n", "         Boolean isNewPasswordBlank = trimmedPassword.isEmpty();\n", "         String previousPassword = editPostRepository.getPassword();\n", "-        Boolean isPreviousPasswordBlank = previousPassword.isEmpty() || previousPassword.trim().isEmpty();\n", "+        Boolean isPreviousPasswordBlank = previousPassword.trim().isEmpty();\n", " \n", "         // Nothing to save\n", "         if (isNewPasswordBlank && isPreviousPasswordBlank) return;\n", "</pre><hr class=\"wide-line\"><li><strong>206  If you have some time, it will be great to have two tests, one for healthy and the other one for not healthy. In this way the NC will be tested.</strong></li><pre>@@ -1242,7 +1242,7 @@ extern \"C\" {\n", "     bool pidOsdAntiGravityActive(void) { return false; }\n", "     bool failsafeIsActive(void) { return false; }\n", "     bool gpsRescueIsConfigured(void) { return false; }\n", "-    bool gpsIsHealthy(void) { return true; }\n", "+    bool gpsIsHealthy(void) { return simulationGpsHealthy; }\n", "     int8_t calculateThrottlePercent(void) { return 0; }\n", "     uint32_t persistentObjectRead(persistentObjectId_e) { return 0; }\n", "     void persistentObjectWrite(persistentObjectId_e, uint32_t) {}\n", "</pre><hr class=\"wide-line\"><li><strong>207  I don't understand why this is needed. Why not have the test just pass in the ProcessStartInfo configured the way it wants it? That's what we do in other tests, e.g. instead of doing: ```C# new RemoteInvokeOptions { CollectConsoleOutput = true } ``` do: ```C# new RemoteInvokeOptions { new ProcessStartInfo { RedirectStandardOutput = true, RedirectStandardInput = true } } ```</strong></li><pre>@@ -69,12 +69,6 @@ namespace System.Diagnostics\n", "                 psi.Arguments = testConsoleAppArgs;\n", "             }\n", " \n", "-            if (options.CollectConsoleOutput)\n", "-            {\n", "-                psi.RedirectStandardOutput = true;\n", "-                psi.RedirectStandardError = true;\n", "-            }\n", "-\n", "             // Return the handle to the process, which may or not be started\n", "             return new RemoteInvokeHandle(options.Start ?\n", "                 Process.Start(psi) :\n", "</pre><hr class=\"wide-line\"><li><strong>208  ```suggestion // Nothing found ```</strong></li><pre>@@ -54,7 +54,7 @@ namespace Orleans.Runtime.GrainDirectory\n", " \n", "             var entry = await GetGrainDirectory(grainId.Type).Lookup(grainId);\n", " \n", "-            // Nothing foundD\n", "+            // Nothing found\n", "             if (entry is null)\n", "             {\n", "                 return null;\n", "</pre><hr class=\"wide-line\"><li><strong>209  I believe this should already be mixed in by HttpClient so can probably be removed?</strong></li><pre>@@ -73,14 +73,16 @@ class Metasploit3 < Msf::Auxiliary\n", "       'DefaultAction' => 'OWA_2010'\n", "     )\n", " \n", "+    'DefaultOptions' => { 'SSL' => true }\n", "+\n", "     register_options(\n", "       [\n", "         OptInt.new('RPORT', [ true, \"The target port\", 443]),\n", "         OptAddress.new('RHOST', [ true, \"The target address\", true]),\n", "         OptBool.new('ENUM_DOMAIN', [ true, \"Automatically enumerate AD domain using NTLM authentication\", false]),\n", "-        OptBool.new('SSL', [ true, \"Negotiate SSL for outgoing connections\", true])\n", "       ], self.class)\n", " \n", "+\n", "     register_advanced_options(\n", "       [\n", "         OptString.new('AD_DOMAIN', [ false, \"Optional AD domain to prepend to usernames\", ''])\n", "</pre><hr class=\"wide-line\"><li><strong>210  The S3 key needs to be deterministically generated per example, here because `split_subsets` is generated from a dict, I do not thing the order is constant and deterministically generated. Additionally there is a two level loop, so we need to take special attention to the key. My suggestion is to have each subgenerator (`_parse_tvs`, ...) yield an id key and then combine with the split_subsets key: ``` for ss_name in split_subsets: ... for sub_key, ex in sub_generator(*files): ... key = '{}/{}'.format(ss_name, sub_key) yield key, ex ``` Then you need to be careful that each sub-generator generate a deterministic key.</strong></li><pre>@@ -731,7 +731,6 @@ class WmtTranslate(tfds.core.GeneratorBasedBuilder):\n", "         extract_dirs = extract_dirs * len(rel_paths)\n", "       return [os.path.join(ex_dir, rel_path) if rel_path else ex_dir\n", "               for ex_dir, rel_path in zip(extract_dirs, rel_paths)]\n", "-    idx = 0\n", "     for ss_name in split_subsets:\n", "       logging.info(\"Generating examples from: %s\", ss_name)\n", "       ds = DATASET_MAP[ss_name]\n", "</pre><hr class=\"wide-line\"><li><strong>211  as @autophagy has pointed out, do we want to return a set here instead, to be explicit about the no-duplicate property of the returned value?</strong></li><pre>@@ -22,8 +22,8 @@ import org.apache.flink.api.common.JobID;\n", " import org.apache.flink.runtime.jobmaster.JobResult;\n", " \n", " import java.io.IOException;\n", "-import java.util.Collection;\n", " import java.util.NoSuchElementException;\n", "+import java.util.Set;\n", " \n", " /**\n", "  * A persistent storage mechanism for the results of successfully and unsuccessfully completed jobs.\n", "</pre><hr class=\"wide-line\"><li><strong>212  you don't really need to init with an empty object here, it'll get overridden in try-catch anyway</strong></li><pre>@@ -195,14 +195,13 @@ maxerr: 50, node: true */\n", "      * @returns {Number} the file size in bytes\n", "      */\n", "     function getFilesizeInBytes(fileName) {\n", "-        var stats = {};\n", "         try {\n", "-            stats = fs.statSync(fileName);\n", "+            var stats = fs.statSync(fileName);\n", "+            return stats.size || 0;\n", "         } catch (ex) {\n", "             console.log(ex);\n", "             return 0;\n", "         }\n", "-        return stats.size || 0;\n", "     }\n", " \n", "     /**\n", "</pre><hr class=\"wide-line\"><li><strong>213  What's the significance of not allowing LogID to be an empty string?</strong></li><pre>@@ -33,10 +33,11 @@ type APITest struct {\n", " // TestLogs is a struct for storing the information about logs that will be\n", " // written out as part of an APITest.\n", " type TestLogs struct {\n", "-\tURL     *string `json:\"url\"`\n", "-\tURLRaw  *string `json:\"url_raw\"`\n", "-\tLineNum int     `json:\"line_num\"`\n", "-\tLogId   *string `json:\"log_id,omitempty\"`\n", "+\tURL        *string `json:\"url\"`\n", "+\tURLRaw     *string `json:\"url_raw\"`\n", "+\tURLLobster *string `json:\"url_lobster\"`\n", "+\tLineNum    int     `json:\"line_num\"`\n", "+\tLogId      *string `json:\"log_id,omitempty\"`\n", " }\n", " \n", " func (at *APITest) BuildFromService(st interface{}) error {\n", "</pre><hr class=\"wide-line\"><li><strong>214  Why removing 'joinfiles' ?</strong></li><pre>@@ -540,7 +540,7 @@ print \"</tr>\";\n", " \n", " // Show fields for topic, join files and body\n", " $fieldsforcontent = array('topic', 'joinfiles', 'content');\n", "-if (!empty($conf->global->MAIN_EMAIL_TEMPLATES_FOR_OBJECT_LINES)) { $fieldsforcontent = array('topic', 'content', 'content_lines'); }\n", "+if (!empty($conf->global->MAIN_EMAIL_TEMPLATES_FOR_OBJECT_LINES)) { $fieldsforcontent = array('topic', 'content', 'content_lines', 'joinfiles' ); }\n", " foreach ($fieldsforcontent as $tmpfieldlist)\n", " {\n", " \tprint '<tr class=\"impair nodrag nodrop nohover\"><td colspan=\"6\" class=\"nobottom\">';\n", "</pre><hr class=\"wide-line\"><li><strong>215  need space at the end</strong></li><pre>@@ -12,7 +12,7 @@ class AclResourcesHgRelationsRepository extends ServiceEntityRepository implemen\n", "      */\n", "     public function refresh(): void\n", "     {\n", "-        $sql = \"DELETE FROM acl_resources_hg_relations\"\n", "+        $sql = \"DELETE FROM acl_resources_hg_relations \"\n", "             . \"WHERE hg_hg_id NOT IN (SELECT t2.hg_id FROM hostgroup AS t2)\";\n", " \n", "         $stmt = $this->db->prepare($sql);\n", "</pre><hr class=\"wide-line\"><li><strong>216  macOS already includes curl. Doesn't it work with the system curl anymore?</strong></li><pre>@@ -18,7 +18,6 @@ class Siril < Formula\n", "   depends_on \"pkg-config\" => :build\n", "   depends_on \"adwaita-icon-theme\"\n", "   depends_on \"cfitsio\"\n", "-  depends_on \"curl\"\n", "   depends_on \"ffms2\"\n", "   depends_on \"fftw\"\n", "   depends_on \"gcc\" # for OpenMP\n", "</pre><hr class=\"wide-line\"><li><strong>217  This can be private, right?</strong></li><pre>@@ -278,7 +278,7 @@ public class AnalysisResultDAO extends BlackboardArtifactDAO {\n", "         return analysisResultCache.get(searchParams, () -> fetchAnalysisResultsForTable(searchParams));\n", "     }\n", " \n", "-    public boolean isAnalysisResultsInvalidating(AnalysisResultSearchParam key, DAOEvent eventData) {\n", "+    private boolean isAnalysisResultsInvalidating(AnalysisResultSearchParam key, DAOEvent eventData) {\n", "         if (!(eventData instanceof AnalysisResultEvent)) {\n", "             return false;\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>218  Considering that we might want to support unicode overmap note symbols, directly indexing the id might not be the best idea. How about extracting it using some separators, or better still use one of `subcategory` or `subtile` to pass the symbol?</strong></li><pre>@@ -1678,15 +1678,12 @@ bool cata_tiles::find_overlay_looks_like( const bool male, const std::string &ov\n", "     }\n", " \n", "     for( int cnt = 0; cnt < 10 && !looks_like.empty(); cnt++ ) {\n", "-        draw_id.clear();\n", "-        str_append( draw_id,\n", "-                    ( male ? \"overlay_male_\" : \"overlay_female_\" ), over_type, looks_like );\n", "+        draw_id = ( male ? \"overlay_male_\" : \"overlay_female_\" ) + over_type + looks_like;\n", "         if( tileset_ptr->find_tile_type( draw_id ) ) {\n", "             exists = true;\n", "             break;\n", "         }\n", "-        draw_id.clear();\n", "-        str_append( draw_id, \"overlay_\", over_type, looks_like );\n", "+        draw_id = \"overlay_\" + over_type + looks_like;\n", "         if( tileset_ptr->find_tile_type( draw_id ) ) {\n", "             exists = true;\n", "             break;\n", "</pre><hr class=\"wide-line\"><li><strong>219  WordPress can decide whether to load the RTL version or not. ```suggestion ```</strong></li><pre>@@ -100,7 +100,6 @@ final class AMP_Setup_Wizard_Submenu_Page {\n", " \t\t$asset        = require $asset_file;\n", " \t\t$dependencies = $asset['dependencies'];\n", " \t\t$version      = $asset['version'];\n", "-\t\t$rtl          = is_rtl() ? '-rtl' : '';\n", " \n", " \t\twp_enqueue_script(\n", " \t\t\tself::JS_HANDLE,\n", "</pre><hr class=\"wide-line\"><li><strong>220  What is the `goto` getting us here now?</strong></li><pre>@@ -72,9 +72,7 @@ namespace System.Text.Json\n", "                     goto Return;\n", "                 }\n", " \n", "-                idx = encoder == null ?\n", "-                    JavaScriptEncoder.Default.FindFirstCharacterToEncode(ptr, value.Length) :\n", "-                    encoder.FindFirstCharacterToEncode(ptr, value.Length);\n", "+                idx = (encoder ?? JavaScriptEncoder.Default).FindFirstCharacterToEncode(ptr, value.Length);\n", " \n", "             Return:\n", "                 return idx;\n", "</pre><hr class=\"wide-line\"><li><strong>221  Zero-initialized by default as well. Making these variables function-local static has no benefit though, therefore I suggest moving them back to global scope.</strong></li><pre>@@ -123,10 +123,10 @@ const gyroFftData_t *gyroFftData(int axis)\n", " void gyroDataAnalyse(const gyroDev_t *gyroDev, biquadFilter_t *notchFilterDyn)\n", " {\n", "     // accumulator for oversampled data => no aliasing and less noise\n", "-    static FAST_RAM float fftAcc[XYZ_AXIS_COUNT] = {0, 0, 0};\n", "-    static FAST_RAM uint32_t fftAccCount = 0;\n", "+    static FAST_RAM float fftAcc[XYZ_AXIS_COUNT];\n", "+    static FAST_RAM uint32_t fftAccCount;\n", " \n", "-    static FAST_RAM uint32_t gyroDataAnalyseUpdateTicks = 0;\n", "+    static FAST_RAM uint32_t gyroDataAnalyseUpdateTicks;\n", " \n", "     // if gyro sampling is > 1kHz, accumulate multiple samples\n", "     for (int axis = 0; axis < XYZ_AXIS_COUNT; axis++) {\n", "</pre><hr class=\"wide-line\"><li><strong>222  shouldn't the `eventSerializer` be checked for not null here after this change?</strong></li><pre>@@ -129,7 +129,7 @@ public class NFA<T> implements Serializable {\n", " \tpublic NFA(final TypeSerializer<T> eventSerializer,\n", " \t\t\tfinal long windowTime,\n", " \t\t\tfinal boolean handleTimeout) {\n", "-\t\tthis.eventSerializer = eventSerializer;\n", "+\t\tthis.eventSerializer = checkNotNull(eventSerializer);\n", " \t\tthis.windowTime = windowTime;\n", " \t\tthis.handleTimeout = handleTimeout;\n", " \t\tthis.eventSharedBuffer = new SharedBuffer<>();\n", "</pre><hr class=\"wide-line\"><li><strong>223  We should add `exceptionally()` section to handle the `CompletableFuture` failure and do negative-ack again on the message.</strong></li><pre>@@ -624,8 +624,12 @@ public class ConsumerImpl<T> extends ConsumerBase<T> implements ConnectionHandle\n", "                     if (message.hasKey()) {\n", "                         typedMessageBuilderNew.key(message.getKey());\n", "                     }\n", "-                    return typedMessageBuilderNew.sendAsync().thenAccept(__ ->\n", "-                            doAcknowledge(finalMessageId, ackType, properties, null));\n", "+                    typedMessageBuilderNew.sendAsync()\n", "+                            .thenAccept(__ -> doAcknowledge(finalMessageId, ackType, properties, null).thenAccept(v -> result.complete(null)))\n", "+                            .exceptionally(ex -> {\n", "+                                result.completeExceptionally(ex);\n", "+                                return null;\n", "+                            });\n", "                 }\n", "             } catch (Exception e) {\n", "                 log.error(\"Send to retry letter topic exception with topic: {}, messageId: {}\", retryLetterProducer.getTopic(), messageId, e);\n", "</pre><hr class=\"wide-line\"><li><strong>224  Possible suggestion: merge the code in this function into `_RunMultiStreamProcesses`, adding a few more configuration parameters to that function (you may need to make an object holding the configuration just to make it all readable). That should simplify the read part of `MultiStreamRWBenchmark` as well as the write part. What do you think?</strong></li><pre>@@ -802,6 +802,7 @@ def _RunMultiStreamProcesses(vms, command_builder, cmd_args,\n", "      until they complete.\n", " \n", "   Args:\n", "+    vms: the VMs to run the benchmark on.\n", "     command_builder: an APIScriptCommandBuilder.\n", "     cmd_args: arguments for the command_builder.\n", "     streams_per_vm: number of threads per vm.\n", "</pre><hr class=\"wide-line\"><li><strong>225  Not that it matters and it is perhaps good to protect against possible future changes, but isn't this always going to be true?</strong></li><pre>@@ -97,6 +97,7 @@ namespace System.Net.WebSockets\n", "                 // Try to use a shared handler rather than creating a new one just for this request, if\n", "                 // the options are compatible.\n", "                 if (options.Credentials == null &&\n", "+                    !options.UseDefaultCredentials &&\n", "                     options.Proxy == null &&\n", "                     options.Cookies == null &&\n", "                     options.RemoteCertificateValidationCallback == null &&\n", "</pre><hr class=\"wide-line\"><li><strong>226  spaces in `q + 8`</strong></li><pre>@@ -864,8 +864,8 @@ rep:\n", " \t\t\t\t*q = 0;\n", " \t\t\t\titem = r_flag_get (core->flags, p);\n", " \t\t\t\tif (item) {\n", "-\t\t\t\t\tif (!strncmp (q+1, \"base64:\", 7)) {\n", "-\t\t\t\t\t\tdec = (char *) r_base64_decode_dyn (q+8, -1);\n", "+\t\t\t\t\tif (!strncmp (q + 1, \"base64:\", 7)) {\n", "+\t\t\t\t\t\tdec = (char *) r_base64_decode_dyn (q + 8, -1);\n", " \t\t\t\t\t\tif (dec) {\n", " \t\t\t\t\t\t\tr_flag_item_set_comment (item, dec);\n", " \t\t\t\t\t\t\tfree (dec);\n", "</pre><hr class=\"wide-line\"><li><strong>227  `$contents` is unset if `$has_last_build_output` is false.</strong></li><pre>@@ -10,6 +10,7 @@ class GradeableView extends AbstractView {\n", "         $semester = $this->core->getConfig()->getSemester();\n", "         $course = $this->core->getConfig()->getCourse();\n", "         $build_script_output_file = \"/var/local/submitty/courses/\" . $semester . \"/\" . $course . \"/build_script_output.txt\";\n", "+        $contents = \"\";\n", " \n", "         $has_last_build_output = file_exists($build_script_output_file);\n", "         if ($has_last_build_output) {\n", "</pre><hr class=\"wide-line\"><li><strong>228  we should also deprecate the old `address_spaces` field, and add that to the deprecations page</strong></li><pre>@@ -24,6 +24,7 @@ func dataSourceArmVirtualNetwork() *schema.Resource {\n", " \t\t\t\"address_spaces\": {\n", " \t\t\t\tType:     schema.TypeList,\n", " \t\t\t\tComputed: true,\n", "+\t\t\t\t\tDeprecated: \"This resource has been deprecated in favour of `address_space` to be more consistent with the `azurerm_virtual_network` resource\",\n", " \t\t\t\tElem: &schema.Schema{\n", " \t\t\t\t\tType: schema.TypeString,\n", " \t\t\t\t},\n", "</pre><hr class=\"wide-line\"><li><strong>229  pretty expensive to create new collectr on every call...</strong></li><pre>@@ -410,8 +410,7 @@ public final class StreamHandler extends AuthenticatedHttpHandler {\n", "       @Override\n", "       public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {\n", "         if (!executor.isShutdown()) {\n", "-          metricsCollector.childCollector(Constants.Metrics.Tag.NAMESPACE, Constants.SYSTEM_NAMESPACE)\n", "-            .increment(\"collect.async.reject\", 1);\n", "+          streamHandlerMetricsCollector.increment(\"collect.async.reject\", 1);\n", "           r.run();\n", "         }\n", "       }\n", "</pre><hr class=\"wide-line\"><li><strong>230  Instead of blocking, give 503?</strong></li><pre>@@ -351,10 +351,6 @@ static bool rest_tx(HTTPRequest* req, const std::string& strURIPart)\n", "     if (!ParseHashStr(hashStr, hash))\n", "         return RESTERR(req, HTTP_BAD_REQUEST, \"Invalid hash: \" + hashStr);\n", " \n", "-    if (g_txindex) {\n", "-        g_txindex->BlockUntilSyncedToCurrentChain();\n", "-    }\n", "-\n", "     CTransactionRef tx;\n", "     uint256 hashBlock = uint256();\n", "     if (!GetTransaction(hash, tx, Params().GetConsensus(), hashBlock, true))\n", "</pre><hr class=\"wide-line\"><li><strong>231  rather than returning nil, can we return an empty list here, which should set this to an empty value: ```suggestion return []interface{}{} ```</strong></li><pre>@@ -30,7 +30,7 @@ func FlattenBatchAccountKeyvaultReference(keyVaultReference *batch.KeyVaultRefer\n", " \tresult := make(map[string]interface{})\n", " \n", " \tif keyVaultReference == nil {\n", "-\t\treturn nil\n", "+\t\treturn []interface{}{}\n", " \t}\n", " \n", " \tif keyVaultReference.ID != nil {\n", "</pre><hr class=\"wide-line\"><li><strong>232  We should be very careful about logging in hot portions of the core runtime, since it adds a synchronization point between threads which can quickly become contended. It's not clear to me whether the counters we have here are valuable enough to justify that overhead-they seem too low level to tell us very much.</strong></li><pre>@@ -716,8 +716,6 @@ struct InterpreterStateImpl : c10::intrusive_ptr_target {\n", "           // std::cout << \"pop reg[\" << reg << \"];\\n\" << registers[reg] << \"\\n\";\n", "         }\n", "         pc = new_pc;\n", "-        logging::getLogger()->addStatValue(\n", "-            logging::runtime_counters::EXECUTED_OPERATORS, 1.0);\n", "       } catch (Suspend& e) {\n", "         // wait() expects a single input\n", "         AT_ASSERT(inst.inputs.values.size == 1);\n", "</pre><hr class=\"wide-line\"><li><strong>233  doc string please. It's now harder to see by inspection what it returns.</strong></li><pre>@@ -71,6 +71,15 @@ class StateStore(SQLBaseStore):\n", " \n", "     @cached(max_entries=100000, iterable=True)\n", "     def get_current_state_ids(self, room_id):\n", "+        \"\"\"Get the current state event ids for a room based on the\n", "+        current_state_events table.\n", "+\n", "+        Args:\n", "+            room_id (str)\n", "+\n", "+        Returns:\n", "+            deferred: dict of (type, state_key) -> event_id\n", "+        \"\"\"\n", "         def _get_current_state_ids_txn(txn):\n", "             txn.execute(\n", "                 \"\"\"SELECT type, state_key, event_id FROM current_state_events\n", "</pre><hr class=\"wide-line\"><li><strong>234  Did you mean to leave `checkSanity()` in the code?</strong></li><pre>@@ -53,7 +53,7 @@ public class Tomcat8SessionsDUnitTest extends TestSessionsTomcat8Base {\n", " \n", "     sessionManager.getTheContext().setSessionTimeout(30);\n", "     region.clear();\n", "-    checkSanity();\n", "+    basicConnectivityCheck();\n", "   }\n", " \n", "   @After\n", "</pre><hr class=\"wide-line\"><li><strong>235  do we need the `avatar_url` ?</strong></li><pre>@@ -115,8 +115,7 @@ $(document).ready(function() {\n", "     bounty_reserved_for = {\n", "       username: data.text,\n", "       creation_date: new Date(),\n", "-      email: data.email,\n", "-      avatar_url: ''\n", "+      email: data.email\n", "     };\n", "   });\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>236  I don't think there's any need to do this, the original `label_namespace` is still in scope here?</strong></li><pre>@@ -73,7 +73,7 @@ class BasicClassifier(Model):\n", "         if num_labels:\n", "             self._num_labels = num_labels\n", "         else:\n", "-            self._num_labels = vocab.get_vocab_size(namespace=self._label_namespace)\n", "+            self._num_labels = vocab.get_vocab_size(namespace=label_namespace)\n", "         self._classification_layer = torch.nn.Linear(self._classifier_input_dim, self._num_labels)\n", "         self._accuracy = CategoricalAccuracy()\n", "         self._loss = torch.nn.CrossEntropyLoss()\n", "</pre><hr class=\"wide-line\"><li><strong>237  Mark this code with a `TODO: <description>` so we remember to remove them in the future once we upgrade to Java 10, @flyingsilverfin</strong></li><pre>@@ -110,6 +110,7 @@ public class QueryExecutor {\n", " \n", "             if (!infer) {\n", " \n", "+                // TODO: this is automatically fixed in Java 10 or OpenJDK 8u222, remove workaround if these conditions met\n", "                 // workaround to deal with non-lazy Java 8 flatMap() functions\n", "                 io.vavr.collection.Stream<Conjunction<Statement>> conjunctions =\n", "                         io.vavr.collection.Stream.ofAll(matchClause.getPatterns().getDisjunctiveNormalForm().getPatterns().stream());\n", "</pre><hr class=\"wide-line\"><li><strong>238  IIRC, all GitHub labels must have a color value (the system automatically assigns one if the user does not specify) so I think we can make the schema a bit more restrictive. Let me know if I've got those constraints wrong though   ```suggestion color: Joi.string().hex().required(), ```</strong></li><pre>@@ -5,7 +5,7 @@ const { GithubAuthV3Service } = require('./github-auth-service')\n", " const { documentation, errorMessagesFor } = require('./github-helpers')\n", " \n", " const schema = Joi.object({\n", "-  color: Joi.string(),\n", "+  color: Joi.string().hex().required(),\n", " }).required()\n", " \n", " module.exports = class GithubLabels extends GithubAuthV3Service {\n", "</pre><hr class=\"wide-line\"><li><strong>239  Run mscore -d Ctrl + T, enter text right click -> text properties -> Unstyled -> Ok Double click the text -> change alignement to bottom The red bbox is not around the text, and will make it hard to select it. Still the bbox numbers looks good...</strong></li><pre>@@ -265,6 +265,7 @@ void Text::layout1()\n", "                   o.rx() -= size.width();\n", "             else if (align() & ALIGN_HCENTER)\n", "                   o.rx() -= (size.width() * .5);\n", "+            setPos(textStyle().offset(spatium()));\n", "             bbox().setRect(o.x(), o.y(), size.width(), size.height());\n", "             _doc->setModified(false);\n", "             }\n", "</pre><hr class=\"wide-line\"><li><strong>240  Can you convert all of these snmp_gets into snmp_multi_get_oid please. Saves on SNMP queries. Also, don't prepend Teleste to the hardware, it's supposed to just describe the model / hardware.</strong></li><pre>@@ -1,4 +1,5 @@\n", " <?php\n", "-$version = preg_replace('/[\\r\\n\\\"]+/', ' ', snmp_get($device, \"swVersion.0\", \"-OQv\", \"TELESTE-LUMINATO-MIB\"));\n", "-$hardware = \"Teleste \" . preg_replace('/[\\r\\n\\\"]+/', ' ', snmp_get($device, \"deviceName.0\", \"-OQv\", \"TELESTE-LUMINATO-MIB\"));\n", "-$serial = preg_replace('/[\\r\\n\\\"]+/', ' ', snmp_get($device, \"hwSerialNumber.0\", \"-OQv\", \"TELESTE-LUMINATO-MIB\"));\n", "+$luminato_tmp = snmp_get_multi_oid($device, 'deviceName.0 hwSerialNumber.0 swVersion.0', '-OUQs', 'TELESTE-LUMINATO-MIB');\n", "+$hardware = $luminato_tmp['deviceName.0'];\n", "+$serial   = $luminato_tmp['hwSerialNumber.0'];\n", "+$version  = $luminato_tmp['swVersion.0'];\n", "</pre><hr class=\"wide-line\"><li><strong>241  Is this taken from the WalletSyncManager?</strong></li><pre>@@ -234,13 +234,13 @@ namespace Stratis.Bitcoin.Features.LightWallet\n", "                         token.ThrowIfCancellationRequested();\n", " \n", "                         next = newTip.GetAncestor(next.Height + 1);\n", "-                        Block nextblock = null;\n", "+                        ChainedHeaderBlock nextblock = null;\n", "                         int index = 0;\n", "                         while (true)\n", "                         {\n", "                             token.ThrowIfCancellationRequested();\n", " \n", "-                            nextblock = this.blockStore.GetBlockAsync(next.HashBlock).GetAwaiter().GetResult();\n", "+                            nextblock = this.consensusManager.GetBlockDataAsync(next.HashBlock).GetAwaiter().GetResult();\n", "                             if (nextblock == null)\n", "                             {\n", "                                 // The idea in this abandoning of the loop is to release consensus to push the block.\n", "</pre><hr class=\"wide-line\"><li><strong>242  This function doesn't work for variable charge items, such as water purifier: - `ammo_required` can't be relied on to provide the number of charges required, since it only depends on the tool itself - Fails to return non-consumed charges.</strong></li><pre>@@ -149,13 +149,12 @@ interact_results interact_with_vehicle( vehicle *veh, const tripoint &pos,\n", " \n", "     auto veh_tool = [&]( const itype_id & obj ) {\n", "         item pseudo( obj );\n", "-        itype_id ammo = pseudo.ammo_default();\n", "-        if( veh->fuel_left( ammo ) < pseudo.ammo_required() ) {\n", "+        if( veh->fuel_left( \"battery\" ) < pseudo.ammo_required() ) {\n", "             return false;\n", "         }\n", "-        pseudo.ammo_set( ammo, veh->drain( ammo, pseudo.ammo_required() ) );\n", "+        pseudo.ammo_set( \"battery\", veh->discharge_battery( pseudo.ammo_required() ) );\n", "         g->u.invoke_item( &pseudo );\n", "-        pseudo.ammo_consume( pseudo.ammo_required(), g->u.pos() );\n", "+        veh->charge_battery( pseudo.ammo_remaining() );\n", "         return true;\n", "     };\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>243  autograd Function is very inprecise here -- it IS the apply method of an autograd Function   it's not the `.apply` visible from Python</strong></li><pre>@@ -125,7 +125,8 @@ auto PyFunction::legacy_apply(const variable_list& inputs) -> variable_list {\n", " }\n", " \n", " // NOTE: this function is written in a way that assumes it's only called for backward;\n", "-// it's used by engine.cpp (NB: this isn't the apply method on autograd Function)\n", "+// it's used by engine.cpp.  This is responsible for forwarding a call from\n", "+// C++'s Function::apply to a Python method \"apply\".\n", " auto PyFunction::apply(const variable_list& inputs) -> variable_list {\n", "   AutoGIL gil;\n", "   AutoGPU _gpu_guard(-1);\n", "</pre><hr class=\"wide-line\"><li><strong>244  More curiosity than objection... can this test be changed to do a full equality test on the error instead of just the prefix match?</strong></li><pre>@@ -387,8 +387,10 @@ func TestQueryDeadline(t *testing.T) {\n", " \n", " \t// First run a query that is killed by the slow query killer after 2s\n", " \t_, err = conn.ExecuteFetch(\"select sleep(5) from dual\", 1000, false)\n", "-\twantErr := \"EOF (errno 2013) (sqlstate HY000)\"\n", "-\tif err == nil || !strings.HasPrefix(err.<PERSON><PERSON><PERSON>(), wantErr) {\n", "+\twantErr := \"EOF (errno 2013) (sqlstate HY000) (CallerID: userData1): Sql: \\\"select sleep(:vtp1) from dual\\\", \" +\n", "+\t\t\"BindVars: {vtp1: \\\"type:INT64 value:\\\\\\\"5\\\\\\\" \\\"#maxLimit: \\\"type:INT64 value:\\\\\\\"10001\\\\\\\" \\\"} \" +\n", "+\t\t\"(er<PERSON> 2013) (sqlstate HY000) during query: select sleep(5) from dual\"\n", "+\tif err == nil || err.Error() != wantErr {\n", " \t\t<PERSON><PERSON>(\"error want '%v', got '%v'\", wantErr, err)\n", " \t}\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>245  we could keep the old `CreateOptimizer` function with a deprecation warning for backward compatibility reasons</strong></li><pre>@@ -23,6 +23,10 @@ from . import analyzer_factory\n", " from . import communicator_factory\n", " from . import algorithm_factory\n", " \n", "+## Purely for backward compatibility, should be removed soon.\n", "+def CreateOptimizer(optimization_settings,model,external_analyzer=EmptyAnalyzer()):\n", "+    return Optimizer(model, optimization_settings, external_analyzer)\n", "+\n", " def Create( model, optimization_settings, external_analyzer=EmptyAnalyzer()):\n", "     return Optimizer(model, optimization_settings, external_analyzer)\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>246  should probably be an io.Reader?</strong></li><pre>@@ -11,7 +11,7 @@ import (\n", " \n", " type kubectlRunner interface {\n", " \texec(ctx context.Context, argv []string) (stdout string, stderr string, err error)\n", "-\texecWithStdin(ctx context.Context, argv []string, stdin *bytes.Reader) (stdout string, stderr string, err error)\n", "+\texecWithStdin(ctx context.Context, argv []string, stdin io.Reader) (stdout string, stderr string, err error)\n", " }\n", " \n", " type realKubectlRunner struct{}\n", "</pre><hr class=\"wide-line\"><li><strong>247  why not just `ID` ?</strong></li><pre>@@ -28,8 +28,8 @@ import (\n", " const (\n", " \t// EventType is event type/kind\n", " \tEventType = \"event\"\n", "-\t// EventUID is a unique event identifier\n", "-\tEventUID = \"uid\"\n", "+\t// EventID is a unique event identifier\n", "+\tEventID = \"uid\"\n", " \t// EventTime is event time\n", " \tEventTime = \"time\"\n", " \t// EventLogin is OS login\n", "</pre><hr class=\"wide-line\"><li><strong>248  This will show a warning in the Datadog UI as well - might be noisy. Is this intended, or is a warning in the Agent logs enough? Also, should we add a note in the warning about whether this is a problem? (I understand not so much unless it happens very frequently) ```suggestion self.warning(\"Unicode error while getting INNODB status (if this warning is infrequent, metric collection won't be impacted): %s\", e) ```</strong></li><pre>@@ -986,7 +986,11 @@ class MySql(AgentCheck):\n", "             )\n", "             return {}\n", "         except (UnicodeDecodeError, UnicodeEncodeError) as e:\n", "-            self.warning(\"Unicode error while getting INNODB status: %s\", e)\n", "+            self.log.warning(\n", "+                \"Unicode error while getting INNODB status \"\n", "+                \"(if this warning is infrequent, metric collection won't be impacted): %s\",\n", "+                str(e),\n", "+            )\n", "             return {}\n", " \n", "         if cursor.rowcount < 1:\n", "</pre><hr class=\"wide-line\"><li><strong>249  Grammar fix: 'don't need to be hashable'</strong></li><pre>@@ -488,7 +488,7 @@ def lru_cache(maxsize=128, typed=False, key=None):\n", " \n", "     If *key* is a callable, it will be called with the given arguments of\n", "     function. It is expected to return a hashable object. If *key* is\n", "-    provided, arguments of the function doesn't have to be hashable.\n", "+    provided, arguments of the function don't need to be hashable.\n", " \n", "     Arguments to the cached function must be hashable.\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>250  Can you add a help tip that explains how to make sure your script has a course version?</strong></li><pre>@@ -84,7 +84,9 @@ export default class ResourcesEditor extends Component {\n", "     if (useMigratedResources && !this.props.courseVersionId) {\n", "       return (\n", "         <strong>\n", "-          Cannot add resources to migrated script without course version.\n", "+          Cannot add resources to migrated script without course version. A\n", "+          script must belong to a course or have 'Is a Standalone Course'\n", "+          checked to have a course version.\n", "         </strong>\n", "       );\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>251  Yes, this should be configurable by the validator.</strong></li><pre>@@ -14,6 +14,7 @@ import (\n", " \n", " var (\n", " \t// TODO: Allow this to be configurable in the same way as minimum fees.\n", "+\t// ref: https://github.com/cosmos/cosmos-sdk/issues/3101\n", " \tgasPerUnitCost uint64 = 10000 // how much gas = 1 atom\n", " )\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>252  It not looks like caching is tested here, as you're not checking that `save` method was executed for cache mock. Please update test</strong></li><pre>@@ -63,6 +63,7 @@ class ProductMetadataTest extends \\PHPUnit\\Framework\\TestCase\n", "         $expectedVersion = '1.2.3';\n", "         $this->composerInformationMock->expects($this->never())->method('getSystemPackages');\n", "         $this->cacheMock->expects($this->once())->method('load')->willReturn($expectedVersion);\n", "+        $this->cacheMock->expects($this->never())->method('save');\n", "         $productVersion = $this->productMetadata->getVersion();\n", "         $this->assertEquals($expectedVersion, $productVersion);\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>253  ```suggestion if (curState is not AlertsComponentState state) ```</strong></li><pre>@@ -15,8 +15,6 @@ namespace Content.Shared.GameObjects.Components.Mobs\n", "     /// </summary>\n", "     public abstract class SharedAlertsComponent : Component\n", "     {\n", "-        private static readonly AlertState[] NoAlerts = new AlertState[0];\n", "-\n", "         [Dependency]\n", "         protected readonly AlertManager AlertManager = default!;\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>254  If you change the test to `FALSE` than you can have a simple way to ignore transparancy. ``` if($this->wm_x_transp !== FALSE) imagecolortransparent($wm_img, imagecolorat($wm_img, $this->wm_x_transp, $this->wm_y_transp)); ```</strong></li><pre>@@ -1193,8 +1193,13 @@ class CI_Image_lib {\n", " \t\t}\n", " \t\telse\n", " \t\t{\n", "-\t\t\t// set our RGB value from above to be transparent and merge the images with the specified opacity\n", "-\t\t\t$this->wm_x_transp && $this->wm_y_transp && imagecolortransparent($wm_img, imagecolorat($wm_img, $this->wm_x_transp, $this->wm_y_transp));\n", "+\t\t\tif ($this->wm_x_transp !== FALSE && $this->wm_y_transp !== FALSE)\n", "+\t\t\t{\n", "+\t\t\t\t// Set our RGB value from above to be transparent.\n", "+\t\t\t\timagecolortransparent($wm_img, imagecolorat($wm_img, $this->wm_x_transp, $this->wm_y_transp));\n", "+\t\t\t}\n", "+\n", "+\t\t\t// Merge the images with the specified opacity.\n", " \t\t\timagecopymerge($src_img, $wm_img, $x_axis, $y_axis, 0, 0, $wm_width, $wm_height, $this->wm_opacity);\n", " \t\t}\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>255  What role is the comma playing here? What does this actually end up asserting?</strong></li><pre>@@ -701,7 +701,7 @@ def _parse_datatype_json_string(json_string):\n", "     ...     assert datatype == pickled\n", "     ...     scala_datatype = sqlContext._ssql_ctx.parseDataType(datatype.json())\n", "     ...     python_datatype = _parse_datatype_json_string(scala_datatype.json())\n", "-    ...     assert datatype == python_datatype, str(datatype) + str(python_datatype)\n", "+    ...     assert datatype == python_datatype\n", "     >>> for cls in _all_atomic_types.values():\n", "     ...     check_datatype(cls())\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>256  Why not leave it static?</strong></li><pre>@@ -152,11 +152,6 @@ namespace Microsoft.Xna.Framework.Graphics\n", " \n", " #else\n", " \t\t\tif (_effect == null) {\n", "-                if (spriteEffect == null)\n", "-                {\n", "-                    // Use a custom SpriteEffect so we can control the transformation matrix\n", "-                    spriteEffect = new Effect(this.graphicsDevice, Effect.LoadEffectResource(\"SpriteEffect\"));\n", "-                }\n", " \t\t\t\tViewport vp = graphicsDevice.Viewport;\n", " \t\t\t\tMatrix projection = Matrix.CreateOrthographicOffCenter(0, vp.Width, vp.Height, 0, 0, 1);\n", " \t\t\t\tMatrix halfPixelOffset = Matrix.CreateTranslation(-0.5f, -0.5f, 0);\n", "</pre><hr class=\"wide-line\"><li><strong>257  You may just remove this method.</strong></li><pre>@@ -220,13 +220,4 @@ public class TsFileResource {\n", "   public TsFileProcessor getUnsealedFileProcessor() {\n", "     return processor;\n", "   }\n", "-\n", "-  @Deprecated\n", "-  public void updateTime(String deviceId, long time) {\n", "-    startTimeMap.putIfAbsent(deviceId, time);\n", "-    Long endTime = endTimeMap.get(deviceId);\n", "-    if (endTime == null || endTime < time) {\n", "-      endTimeMap.put(deviceId, time);\n", "-    }\n", "-  }\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>258  this null check is no longer needed</strong></li><pre>@@ -89,6 +89,7 @@ public class ExecutionHandlerContext extends ChannelInboundHandlerAdapter {\n", "       Runnable shutdownInvoker,\n", "       RedisStats redisStats,\n", "       ExecutorService backgroundExecutor,\n", "+      EventLoopGroup subscriberGroup,\n", "       byte[] password) {\n", "     this.channel = channel;\n", "     this.regionProvider = regionProvider;\n", "</pre><hr class=\"wide-line\"><li><strong>259  we had code about this is in snap.WrapperPath (which in this world goes away? ) , anyway snap seems a good place to put this in</strong></li><pre>@@ -65,9 +65,7 @@ func (x *cmdRun) Execute(args []string) error {\n", " \treturn snapRun(x.<PERSON>si<PERSON>.SnapApp, x.Command, args)\n", " }\n", " \n", "-var GetSnapInfo = getSnapInfoImpl\n", "-\n", "-func getSnapInfoImpl(snapName string) (*snap.Info, error) {\n", "+func getSnapInfo(snapName string) (*snap.Info, error) {\n", " \t// we need to get the revision here because once we are inside\n", " \t// the confinement its not available anymore\n", " \tsnaps, err := Client().ListSnaps([]string{snapName})\n", "</pre><hr class=\"wide-line\"><li><strong>260  is_multidiscrete is more clear I think</strong></li><pre>@@ -109,7 +109,7 @@ class VTracePolicyGraph(LearningRateSchedule, TFPolicyGraph):\n", "             \"Must use `truncate_episodes` batch mode with V-trace.\"\n", "         self.config = config\n", "         self.sess = tf.get_default_session()\n", "-        self._is_discrete = False\n", "+        self._is_multidiscrete = False\n", "         self.grads = None\n", " \n", "         output_hidden_shape = None\n", "</pre><hr class=\"wide-line\"><li><strong>261  This set of specs is independent of providers and tying these host/vm to redhat/openstack defeats the purpose. can you keep the generic types and just mock them to return true for support check?</strong></li><pre>@@ -4,10 +4,10 @@ describe ConversionHost do\n", "   let(:apst) { FactoryBot.create(:service_template_ansible_playbook) }\n", " \n", "   context \"provider independent methods\" do\n", "-    let(:host) { FactoryBot.create(:host_redhat) }\n", "-    let(:vm) { FactoryBot.create(:vm_openstack) }\n", "-    let(:conversion_host_1) { FactoryBot.create(:conversion_host, :resource => host) }\n", "-    let(:conversion_host_2) { FactoryBot.create(:conversion_host, :resource => vm) }\n", "+    let(:host) { FactoryBot.create(:host) }\n", "+    let(:vm) { FactoryBot.create(:vm) }\n", "+    let(:conversion_host_1) { FactoryBot.create(:conversion_host, :skip_validate, :resource => host) }\n", "+    let(:conversion_host_2) { FactoryBot.create(:conversion_host, :skip_validate, :resource => vm) }\n", "     let(:task_1) { FactoryBot.create(:service_template_transformation_plan_task, :state => 'active', :conversion_host => conversion_host_1) }\n", "     let(:task_2) { FactoryBot.create(:service_template_transformation_plan_task, :conversion_host => conversion_host_1) }\n", "     let(:task_3) { FactoryBot.create(:service_template_transformation_plan_task, :state => 'active', :conversion_host => conversion_host_2) }\n", "</pre><hr class=\"wide-line\"><li><strong>262  125 or 100? Consider adding a TODO for tuning the delay.</strong></li><pre>@@ -171,7 +171,11 @@ public class FlutterReloadManager {\n", "       return;\n", "     }\n", " \n", "-    // Add an arbitrary 125ms delay to allow analysis to catch up.\n", "+    // Add an arbitrary 125ms delay to allow analysis to catch up. This delay gives the analysis server a\n", "+    // small pause to return error results in the (relatively infrequent) case where the user makes a bad\n", "+    // edit and immediately hits save.\n", "+    final int reloadDelayMs = 125;\n", "+\n", "     handleingSave = true;\n", " \n", "     JobScheduler.getScheduler().schedule(() -> {\n", "</pre><hr class=\"wide-line\"><li><strong>263  can `error_code` ever be something else? Should we throw if we get an error_code that is not one of these two?</strong></li><pre>@@ -131,14 +131,13 @@ std::pair<int, PortType> listen(PortType port) {\n", "   return {socket, getSocketPort(socket)};\n", " }\n", " \n", "-template <typename T>\n", " void handleConnectException(\n", "     struct ::addrinfo** nextAddr,\n", "     int error_code,\n", "     bool* anyRefused,\n", "     bool* anyReset,\n", "     bool wait,\n", "-    T start,\n", "+    std::chrono::time_point<std::chrono::high_resolution_clock> start,\n", "     std::shared_ptr<struct ::addrinfo> addresses,\n", "     std::chrono::milliseconds timeout) {\n", "   // ECONNREFUSED happens if the server is not yet listening.\n", "</pre><hr class=\"wide-line\"><li><strong>264  Why are we performing two ```checkComponentValidity``` checks now?</strong></li><pre>@@ -2280,8 +2280,10 @@ export default class Component extends Element {\n", "     this.calculateComponentValue(data, flags, row);\n", "     this.checkComponentConditions(data, flags, row);\n", "     const shouldCheckValidity = !this.builderMode && !this.options.preview && this.defaultValue;\n", "-    const isValid =  shouldCheckValidity ? this.checkComponentValidity(data) : true;\n", "-    return flags.noValidate ? true : this.checkComponentValidity(data, !isValid, row);\n", "+    if (shouldCheckValidity && !flags.noValidate) {\n", "+      return this.checkComponentValidity(data, true, row);\n", "+    }\n", "+    return flags.noValidate ? true : this.checkComponentValidity(data, false, row);\n", "   }\n", " \n", "   get validationValue() {\n", "</pre><hr class=\"wide-line\"><li><strong>265  remove the default here. add the default in `lib/WebpackOptionsDefaulter.js`</strong></li><pre>@@ -29,7 +29,7 @@ class JsonpMainTemplatePlugin {\n", " \t\t\tconst chunkMaps = chunk.getChunkMaps();\n", " \t\t\tconst crossOriginLoading = this.outputOptions.crossOriginLoading;\n", " \t\t\tconst chunkLoadTimeout = this.outputOptions.chunkLoadTimeout;\n", "-\t\t\tconst jsonpScriptType = this.outputOptions.jsonpScriptType || \"text/javascript\";\n", "+\t\t\tconst jsonpScriptType = this.outputOptions.jsonpScriptType;\n", " \t\t\tconst scriptSrcPath = this.applyPluginsWaterfall(\"asset-path\", JSON.stringify(chunkFilename), {\n", " \t\t\t\thash: `\" + ${this.renderCurrentHashCode(hash)} + \"`,\n", " \t\t\t\thashWithLength: length => `\" + ${this.renderCurrentHashCode(hash, length)} + \"`,\n", "</pre><hr class=\"wide-line\"><li><strong>266  hmm, MDNS_RR_CACHE_FLUSH is a bit of a misnomer now. We should either rename that to maybe MDNS_RR_CACHE_FLUSH_OR_QU or so?</strong></li><pre>@@ -1653,11 +1653,11 @@ int dns_packet_read_key(\n", "                 return r;\n", " \n", "         if (p->protocol == DNS_PROTOCOL_MDNS) {\n", "-                /* See RFC6762, sections 5.4 and 10.2 */\n", "+                /* See RFC6762, Section 10.2 */\n", " \n", "-                if (type != DNS_TYPE_OPT && (class & MDNS_RR_CACHE_FLUSH_OR_QU)) {\n", "-                        class &= ~MDNS_RR_CACHE_FLUSH_OR_QU;\n", "-                        cache_flush_or_qu = true;\n", "+                if (type != DNS_TYPE_OPT && (class & MDNS_RR_CACHE_FLUSH)) {\n", "+                        class &= ~MDNS_RR_CACHE_FLUSH;\n", "+                        cache_flush = true;\n", "                 }\n", "         }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>267  Is 1 MB useful in any way?</strong></li><pre>@@ -12,7 +12,7 @@ import (\n", " // TODO: parametrize\n", " const fsType = \"ext4\"\n", " \n", "-const defaultSize uint64 = 1000000 // 1MB, applies if storage size is not specified\n", "+const defaultSize uint64 = 1000000000 // 1GB, applies if storage size is not specified\n", " \n", " // Mount mounts a formated LVM logical volume according to the given params\n", " func (d *Driver) Mount(params protocol.MountRequest) flex.Response {\n", "</pre><hr class=\"wide-line\"><li><strong>268  Since `all_tensor_names` is added, the minimal set of changes to preserve backward compatibility is to add a default for only `all_tensor_names`. Can we do that instead?</strong></li><pre>@@ -29,7 +29,7 @@ from tensorflow.python.platform import flags\n", " FLAGS = None\n", " \n", " \n", "-def print_tensors_in_checkpoint_file(file_name, tensor_name=\"\", all_tensors=False,\n", "+def print_tensors_in_checkpoint_file(file_name, tensor_name, all_tensors,\n", "                                      all_tensor_names=False):\n", "   \"\"\"Prints tensors in a checkpoint file.\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>269  Security protocol should be an enum, so we could fail if it is set to incorrect value.</strong></li><pre>@@ -44,13 +44,7 @@ public class KafkaConfig\n", "     private File tableDescriptionDir = new File(\"etc/kafka/\");\n", "     private boolean hideInternalColumns = true;\n", "     private int messagesPerSplit = 100_000;\n", "-    private String securityProtocol;\n", "-    private String sslTruststoreLocation;\n", "-    private String sslTruststorePassword;\n", "-    private String sslKeystoreLocation;\n", "-    private String sslKeystorePassword;\n", "-    private String sslEndpointIdentificationAlgorithm;\n", "-    private String sslKeyPassword;\n", "+    private KafkaSecurityConfig securityConfig = new KafkaSecurityConfig();\n", " \n", "     @Size(min = 1)\n", "     public Set<HostAddress> getNodes()\n", "</pre><hr class=\"wide-line\"><li><strong>270  I think we need a different API, this is currently an override operation, not a union</strong></li><pre>@@ -555,7 +555,7 @@ public class TieredBlockStore implements BlockStore {\n", " \n", "       // Check if block is pinned on commit\n", "       if (isPinned) {\n", "-        updatePinnedInodes(Collections.singleton(BlockId.getFileId(blockId)));\n", "+        addToPinnedInodes(BlockId.getFileId(blockId));\n", "       }\n", " \n", "       return loc;\n", "</pre><hr class=\"wide-line\"><li><strong>271  ```suggestion if controller.PodWebhookEnabled { go wait.Forever(func() { periodicityController.Run(ctx.Done()) }, waitDuration) } ``` - this controller is needed when webhook is used, so we can skip it entirely if webhook is not enabled - how to update statefulsets is the implementation details of the controller</strong></li><pre>@@ -221,7 +221,9 @@ func main() {\n", " \t\tgo wait.Forever(func() { bsController.Run(workers, ctx.Done()) }, waitDuration)\n", " \t\tgo wait.Forever(func() { tidbInitController.Run(workers, ctx.Done()) }, waitDuration)\n", " \t\tgo wait.Forever(func() { tidbMonitorController.Run(workers, ctx.Done()) }, waitDuration)\n", "-\t\tgo wait.Forever(func() { periodicityController.Run() }, periodicityDuration)\n", "+\t\tif controller.PodWebhookEnabled {\n", "+\t\t\tgo wait.Forever(func() { periodicityController.Run(ctx.Done()) }, waitDuration)\n", "+\t\t}\n", " \t\tif features.DefaultFeatureGate.Enabled(features.AutoScaling) {\n", " \t\t\tgo wait.Forever(func() { autoScalerController.Run(workers, ctx.Done()) }, waitDuration)\n", " \t\t}\n", "</pre><hr class=\"wide-line\"><li><strong>272  Move it into the loader function and use `static const AString` as the datatype.</strong></li><pre>@@ -26,7 +26,6 @@ extern \"C\"\n", " #undef TOLUA_TEMPLATE_BIND\n", " #include \"tolua++/include/tolua++.h\"\n", " \n", "-const std::string LUA_FILE_SUFFIX = \".lua\";\n", " \n", " \n", " \n", "</pre><hr class=\"wide-line\"><li><strong>273  prefer to use a more clear variable name, rw++ looks strange.</strong></li><pre>@@ -122,9 +122,7 @@ func newHotScheduler(opController *schedule.OperatorController, conf *hotRegionS\n", " \t\tregionPendings: make(map[uint64][2]*operator.Operator),\n", " \t\tconf:           conf,\n", " \t}\n", "-\tfor rw := rwType(0); rw < rwTypeLen; rw++ {\n", "-\t\tret.pendings[rw] = map[*pendingInfluence]struct{}{}\n", "-\t}\n", "+\tret.pendings = map[*pendingInfluence]struct{}{}\n", " \tfor ty := resourceType(0); ty < resourceTypeLen; ty++ {\n", " \t\tret.stLoadInfos[ty] = map[uint64]*storeLoadDetail{}\n", " \t}\n", "</pre><hr class=\"wide-line\"><li><strong>274  Not sure if that is a typo here but it is encoded as sfixed64</strong></li><pre>@@ -40,8 +40,8 @@ func CanonicalizePartSetHeader(psh tmproto.PartSetHeader) tmproto.CanonicalPartS\n", " func CanonicalizeProposal(chainID string, proposal *tmproto.Proposal) tmproto.CanonicalProposal {\n", " \treturn tmproto.CanonicalProposal{\n", " \t\tType:      tmproto.ProposalType,\n", "-\t\tHeight:    proposal.Height,       // encoded as sfixedsize64\n", "-\t\tRound:     int64(proposal.Round), // encoded as sfixedsize64\n", "+\t\tHeight:    proposal.Height,       // encoded as sfixed64\n", "+\t\tRound:     int64(proposal.Round), // encoded as sfixed64\n", " \t\tPOLRound:  int64(proposal.PolRound),\n", " \t\tBlockID:   CanonicalizeBlockID(proposal.BlockID),\n", " \t\tTimestamp: proposal.Timestamp,\n", "</pre><hr class=\"wide-line\"><li><strong>275  If `newValueIsNull` below is all `true`s, you could also return all-nulls RLE this applies to copyRegion methods in all block implementations and certainly adds some small overhead, not sure we would want that. @dain ?</strong></li><pre>@@ -234,9 +234,6 @@ public class ByteArrayBlockBuilder\n", "     {\n", "         checkValidRegion(getPositionCount(), positionOffset, length);\n", " \n", "-        if (!hasNonNullValue) {\n", "-            return new RunLengthEncodedBlock(NULL_VALUE_BLOCK, positionCount);\n", "-        }\n", "         boolean[] newValueIsNull = Arrays.copyOfRange(valueIsNull, positionOffset, positionOffset + length);\n", "         byte[] newValues = Arrays.copyOfRange(values, positionOffset, positionOffset + length);\n", "         return new ByteArrayBlock(length, newValueIsNull, newValues);\n", "</pre><hr class=\"wide-line\"><li><strong>276  can you not just `return GoForwardButton().IsChecked().GetBoolean()` just like `_CaseSensitive` below?</strong></li><pre>@@ -38,14 +38,7 @@ namespace winrt::Microsoft::Terminal::TerminalControl::implementation\n", "     //         states of the two direction buttons\n", "     bool SearchBoxControl::_GoForward()\n", "     {\n", "-        if (GoForward<PERSON>on().IsChecked().GetBoolean())\n", "-        {\n", "-            return true;\n", "-        }\n", "-        else\n", "-        {\n", "-            return false;\n", "-        }\n", "+        return GoForwardButton().IsChecked().GetBoolean();\n", "     }\n", " \n", "     // Method Description:\n", "</pre><hr class=\"wide-line\"><li><strong>277  Should we use fmod or `%` here?</strong></li><pre>@@ -85,7 +85,7 @@ void fmod_kernel(TensorIterator& iter) {\n", "   if (isIntegralType(iter.dtype(), false)) {\n", "     AT_DISPATCH_INTEGRAL_TYPES(iter.dtype(), \"fmod_cpu\", [&]() {\n", "         cpu_kernel(iter, [](scalar_t a, scalar_t b) -> scalar_t {\n", "-            return std::fmod(a, b);\n", "+            return a % b;\n", "         });\n", "     });\n", "   } else {\n", "</pre><hr class=\"wide-line\"><li><strong>278  Format this line correctly and check spelling.</strong></li><pre>@@ -355,8 +355,9 @@ public class MenuEntrySwapperPlugin extends Plugin\n", " \t\t\t{\n", " \t\t\t\tswap(\"exchange\", option, target, true);\n", " \t\t\t}\n", "-\t\t\tif(config.swapDarkMage()){\n", "-\t\t\t\tswap(\"repairs\",option,targer,true);\n", "+\t\t\tif (config.swapDarkMage())\n", "+\t\t\t{\n", "+\t\t\t\tswap(\"repairs\",option,target,true);\n", " \t\t\t}\n", " \n", " \t\t\t// make sure assignment swap is higher priority than trade swap for slayer masters\n", "</pre><hr class=\"wide-line\"><li><strong>279  this loop could be replaced with `std::accumulate`</strong></li><pre>@@ -61,10 +61,7 @@ JitConstants PoolingKernelBase::GetJitConstants(const pooling_params& pp, Poolin\n", "     });\n", " \n", "     if (pp.maxPoolOpset8Features) {\n", "-        mem_consts.AddConstants({\n", "-            MakeJitConstant(\"DILATION\", pp.poolDilation),\n", "-            MakeJitConstant(\"AXIS\", pp.poolAxis)\n", "-        });\n", "+        mem_consts.AddConstants({MakeJitConstant(\"DILATION\", pp.poolDilation)});\n", " \n", "         if (pp.poolAxis != 0) {\n", "             size_t indices_upper_bound = 1;\n", "</pre><hr class=\"wide-line\"><li><strong>280  could use `requester_can_do_action` here.</strong></li><pre>@@ -378,7 +378,7 @@ class AuthHandler(BaseHandler):\n", "         except LoginError:\n", "             # Update the ratelimiter to say we failed (`can_do_action` doesn't raise).\n", "             await self._failed_uia_attempts_ratelimiter.can_do_action(\n", "-                requester, requester_user_id\n", "+                requester,\n", "             )\n", "             raise\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>281  Can you, for consistency with the rest of the lib, change the `!!animated` to `<PERSON>olean(animated)`?</strong></li><pre>@@ -79,7 +79,7 @@ class Util {\n", "     if (text.includes('%')) text = decodeURIComponent(text);\n", "     if (text.includes(':')) {\n", "       const [, animated, name, id] = text.match(/(?:<(a)?:)?([\\w_]{2,32}):(\\d+)>?/);\n", "-      return { animated: !!animated, name, id };\n", "+      return { animated: <PERSON><PERSON><PERSON>(animated), name, id };\n", "     } else {\n", "       return {\n", "         animated: false,\n", "</pre><hr class=\"wide-line\"><li><strong>282  fwiw, i usually do this as ``` if ctx.Err() != nil { // context finished } ```</strong></li><pre>@@ -188,11 +188,9 @@ func (e *subscriberEntry) notify(ctx context.Context, store *Store) {\n", " \t\treturn\n", " \t}\n", " \n", "-\tselect {\n", "-\tcase <-ctx.Done():\n", "-\t\t// don't keep retrying after context is done\n", "+\tif ctx.Err() != nil {\n", "+\t\t// context finished\n", " \t\treturn\n", "-\tdefault:\n", " \t}\n", " \n", " \t// Backoff on error\n", "</pre><hr class=\"wide-line\"><li><strong>283  Why this change?</strong></li><pre>@@ -114,7 +114,7 @@ namespace MediaBrowser.Providers.MediaInfo\n", "                 ImageType.Primary => _primaryImageFileNames,\n", "                 ImageType.Backdrop => _backdropImageFileNames,\n", "                 ImageType.Logo => _logoImageFileNames,\n", "-                _ => throw new ArgumentException(\"Unexpected image type: \" + type)\n", "+                _ => Array.Empty<string>()\n", "             };\n", " \n", "             // Try attachments first\n", "</pre><hr class=\"wide-line\"><li><strong>284  This `elif` can be added as `or` clause to the previous one.</strong></li><pre>@@ -350,9 +350,7 @@ def _minimize_lbfgsb(fun, x0, args=(), jac=None, bounds=None,\n", "     task_str = task.tostring().strip(b'\\x00').strip()\n", "     if task_str.startswith(b'CONV'):\n", "         warnflag = 0\n", "-    elif n_function_evals[0] > maxfun:\n", "-        warnflag = 1\n", "-    elif n_iterations >= maxiter:\n", "+    elif n_function_evals[0] > maxfun or n_iterations >= maxiter:\n", "         warnflag = 1\n", "     else:\n", "         warnflag = 2\n", "</pre><hr class=\"wide-line\"><li><strong>285  Any thoughts on moving this to a `utils` or similar module?</strong></li><pre>@@ -787,13 +787,6 @@ def _isin_kernel(element, test_elements, assume_unique=False):\n", "     return values.reshape(element.shape + (1,) * test_elements.ndim)\n", " \n", " \n", "-def safe_wraps(wrapped, assigned=functools.WRAPPER_ASSIGNMENTS):\n", "-    if all(hasattr(wrapped, attr) for attr in assigned):\n", "-        return wraps(wrapped, assigned=assigned)\n", "-    else:\n", "-        return lambda x: x\n", "-\n", "-\n", " @safe_wraps(getattr(np, 'isin', None))\n", " def isin(element, test_elements, assume_unique=False, invert=False):\n", "     element = asarray(element)\n", "</pre><hr class=\"wide-line\"><li><strong>286  isn't this 1 microsecond? Are you missing time.Millisecond or something?</strong></li><pre>@@ -38,7 +38,13 @@ import (\n", " \t\"github.com/coreos/etcd/store\"\n", " )\n", " \n", "-const extraTimeout = 1000\n", "+// TODO(yichengq): constant extraTimeout is a hack.\n", "+// Current problem is that there is big lag between join command\n", "+// execution and join success.\n", "+// Fix it later. It should be removed when proper method is found and\n", "+// enough tests are provided. It is expected to be calculated from\n", "+// heartbeatInterval and electionTimeout only.\n", "+const extraTimeout = time.Duration(1000) * time.Millisecond\n", " \n", " type Etcd struct {\n", " \tConfig       *config.Config     // etcd config\n", "</pre><hr class=\"wide-line\"><li><strong>287  should this be an or rather than an and?</strong></li><pre>@@ -862,8 +862,8 @@ func (p *Project) BuildProjectTVPairsWithAlias(alias string) ([]TVPair, error) {\n", " \t\tfor _, variant := range p.BuildVariants {\n", " \t\t\tif variantRegex.MatchString(variant.Name) {\n", " \t\t\t\tfor _, task := range p.Tasks {\n", "-\t\t\t\t\tif taskRegex.MatchString(task.Name) &&\n", "-\t\t\t\t\t\t(len(v.Tags) == 0 || len(util.StringSliceIntersection(task.Tags, v.Tags)) > 0) &&\n", "+\t\t\t\t\tif ((v.Task != \"\" && taskRegex.MatchString(task.Name)) ||\n", "+\t\t\t\t\t\t(len(v.Tags) > 0 && len(util.StringSliceIntersection(task.Tags, v.Tags)) > 0)) &&\n", " \t\t\t\t\t\t(p.<PERSON><PERSON>(task.Name, variant.Name) != nil) {\n", " \t\t\t\t\t\tpairs = append(pairs, TVPair{variant.Name, task.Name})\n", " \t\t\t\t\t}\n", "</pre><hr class=\"wide-line\"><li><strong>288  Can we add the StringUtils class a normalize function that does `.replace(' ', '-').lower()`</strong></li><pre>@@ -113,6 +113,12 @@ class AzureFunctionMode(ServerlessExecutionMode):\n", "         data = self.policy.data\n", " \n", "         updated_parameters = {\n", "+            'name': (data['mode']['provision-options']['servicePlanName'] +\n", "+                     '-' +\n", "+                     data['name']).replace(' ', '-').lower(),\n", "+\n", "+            'storageName': (data['mode']['provision-options']['servicePlanName']\n", "+                            ).replace('-', '').lower(),\n", "             'dockerVersion': CONST_DOCKER_VERSION,\n", "             'functionsExtVersion': CONST_FUNCTIONS_EXT_VERSION\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>289  Interface methods are implicitly public; can remove `public` qualifier.</strong></li><pre>@@ -6,8 +6,8 @@ package com.yahoo.vespa.clustercontroller.core;\n", "  */\n", " public interface AggregatedClusterStats {\n", " \n", "-    public boolean hasUpdatesFromAllDistributors();\n", "+    boolean hasUpdatesFromAllDistributors();\n", " \n", "-    public ContentClusterStats getStats();\n", "+    ContentClusterStats getStats();\n", " \n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>290  Please don't cast, but use abs to support complex matrices. gen_linalg_ops.svd returns singular values with the same type as the input. (We should perhaps change that.)</strong></li><pre>@@ -558,9 +558,7 @@ def norm(tensor,\n", "         permed = array_ops.transpose(tensor, perm=perm_before)\n", "         matrix_2_norm = array_ops.expand_dims(\n", "             math_ops.reduce_max(\n", "-                math_ops.cast(\n", "-                    gen_linalg_ops.svd(permed, compute_uv=False)[0],\n", "-                    dtype=dtypes.float32),\n", "+                math_ops.abs(gen_linalg_ops.svd(permed, compute_uv=False)[0]),\n", "                 axis=-1,\n", "                 keepdims=True),\n", "             axis=-1)\n", "</pre><hr class=\"wide-line\"><li><strong>291  I get what this method is doing and why we need it, but the godoc is hard to grok. WDYT of the following: ```go // WrapSDKContext returns a stdlib context.Context with the provided sdk.Context's internal // context as a value. It is useful for passing an sdk.Context through methods that take a // stdlib context.Context parameter such as generated gRPC methods. To get the original // sdk.Context back, call UnwrapSDKContext. ```</strong></li><pre>@@ -230,10 +230,10 @@ type sdkContextKeyType string\n", " \n", " const sdkContextKey sdkContextKeyType = \"sdk-context\"\n", " \n", "-// WrapSDKContext attaches a Context to that Context's context.Context member\n", "-// and returns that context. It is useful for passing a Context through methods\n", "-// that take a generic context.Context parameter such as generated gRPC\n", "-// methods\n", "+// WrapSDKContext returns a stdlib context.Context with the provided sdk.Context's internal\n", "+// context as a value. It is useful for passing an sdk.Context  through methods that take a\n", "+// stdlib context.Context parameter such as generated gRPC methods. To get the original\n", "+// sdk.Context back, call UnwrapSDKContext.\n", " func WrapSDKContext(ctx Context) context.Context {\n", " \treturn context.WithValue(ctx.ctx, sdkContextKey, ctx)\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>292  No domain sockets in `/etc/` please!</strong></li><pre>@@ -54,7 +54,7 @@ CLI_FLAG(bool, disable_extensions, false, \"Disable extension API\");\n", " \n", " CLI_FLAG(string,\n", "          extensions_socket,\n", "-         OSQUERY_HOME \"/osquery.em\",\n", "+         OSQUERY_DB_HOME \"/osquery.em\",\n", "          \"Path to the extensions UNIX domain socket\")\n", " \n", " CLI_FLAG(string,\n", "</pre><hr class=\"wide-line\"><li><strong>293  why is this required?</strong></li><pre>@@ -136,8 +136,6 @@ namespace osu.Framework.Graphics.Containers\n", " \n", "         protected override Container<T> Content => content;\n", " \n", "-        protected override bool ConfinePositionalInput => true;\n", "-\n", "         /// <summary>\n", "         /// Whether we are currently scrolled as far as possible into the scroll direction.\n", "         /// </summary>\n", "</pre><hr class=\"wide-line\"><li><strong>294  Use indentation to improve readability</strong></li><pre>@@ -384,7 +384,7 @@ public class RealmProxyClassGenerator {\n", " \n", "         // verify number of columns\n", "         writer.beginControlFlow(\"if (table.getColumnCount() != \" + metadata.getFields().size() + \")\");\n", "-        writer.emitStatement(\"throw new IllegalStateException(\\\"Column count does not match\\\")\");\n", "+        writer.emitStatement(\"throw new RealmMigrationNeededException(transaction.getPath(), \\\"Field count does not match\\\")\");\n", "         writer.endControl<PERSON>low();\n", " \n", "         // create type dictionary for lookup\n", "</pre><hr class=\"wide-line\"><li><strong>295  hey this seem to be a hack. is there a cleaner way to do this?</strong></li><pre>@@ -34,6 +34,12 @@ $(function() {\n", "             $('#profilePicEditRotateRight').click(function() {\n", "                  picture.guillotine('rotateRight');\n", "             });\n", "+\n", "+            // Panning handlers based on approach outlined here\n", "+            // https://github.com/matiasgagliano/guillotine/issues/6#issuecomment-53178560\n", "+            //\n", "+            // It utilizes an internal method from the library (_offset)\n", "+            // to update the (top, left) offset values for the image.\n", "             $('#profilePicEditPanUp').click(function() {\n", "                 var data = picture.guillotine('getData');\n", "                 picture.guillotine('instance')._offset(data.x / data.w, (data.y - 10) / data.h);\n", "</pre><hr class=\"wide-line\"><li><strong>296  This is not right either.</strong></li><pre>@@ -138,7 +138,7 @@ public enum Errors {\n", "             new UnsupportedSaslMechanismException(\"The broker does not support the requested SASL mechanism.\")),\n", "     ILLEGAL_SASL_STATE(34,\n", "             new IllegalSaslStateException(\"Request is not valid given the current SASL state.\")),\n", "-    UNSUPPORTED_VERSION(33,\n", "+    UNSUPPORTED_VERSION(35,\n", "             new UnsupportedVersionException(\"The version of API is not supported.\"));\n", " \n", "     private static final Logger log = LoggerFactory.getLogger(Errors.class);\n", "</pre><hr class=\"wide-line\"><li><strong>297  Would invert the conditional and place this within. Later you might want to perform more tasks in this callback and that would cut down the future delta.</strong></li><pre>@@ -2150,11 +2150,9 @@ void item::on_pickup( Character &p )\n", " \n", " void item::on_contents_changed()\n", " {\n", "-    if( !is_non_resealable_container() ) {\n", "-        return;\n", "+    if( is_non_resealable_container() ) {\n", "+        convert( type->container->unseals_into );\n", "     }\n", "-\n", "-    convert( type->container->unseals_into );\n", " }\n", " \n", " std::string item::tname( unsigned int quantity, bool with_prefix ) const\n", "</pre><hr class=\"wide-line\"><li><strong>298  ```suggestion deprecationWarning('NumberHelper::defaultCurrency() is deprecated. Use setDefaultCurrency() and getDefaultCurrency() instead.'); ```</strong></li><pre>@@ -226,7 +226,7 @@ class NumberHelper extends Helper\n", "      */\n", "     public function defaultCurrency($currency): ?string\n", "     {\n", "-        deprecationWarning('NumberHelper::defaultCurreny() is deprecated. Use setDefaultCurreny() and getDefaultCurrent() instead.');\n", "+        deprecationWarning('NumberHelper::defaultCurrency() is deprecated. Use setDefaultCurrency() and getDefaultCurrency() instead.');\n", " \n", "         return $this->_engine->defaultCurrency($currency);\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>299  nit: please use camel case for variables names which exist only in JS: `displayName`</strong></li><pre>@@ -709,7 +709,7 @@ export const progressionsFromLevels = levels => {\n", "   let currentProgression = {\n", "     start: 0,\n", "     name: levels[0].progression || levels[0].name,\n", "-    display_name: levels[0].progressionDisplayName || levels[0].name,\n", "+    displayName: levels[0].progressionDisplayName || levels[0].name,\n", "     levels: [levels[0]]\n", "   };\n", "   levels.slice(1).forEach((level, index) => {\n", "</pre><hr class=\"wide-line\"><li><strong>300  It seems, if `key.GetSubKeyNames()` is empty, `Last()` would throw exception. Would it be a concern?</strong></li><pre>@@ -96,7 +96,7 @@ namespace Microsoft.WindowsAzure.Commands.Utilities.CloudService.AzureTools\n", "                     }\n", " \n", "                     // select the latest version of SDK installed on the machine.\n", "-                    version = key.GetSubKeyNames().Last();\n", "+                    version = key.GetSubKeyNames().LastOrDefault();\n", " \n", "                     if (string.IsNullOrWhiteSpace(version) && key.GetSubKeyNames().Length == 0)\n", "                     {\n", "</pre><hr class=\"wide-line\"><li><strong>301  Can you just do `super().from_params(...)` here?</strong></li><pre>@@ -97,9 +97,9 @@ class BasicTextFieldEmbedder(TextFieldEmbedder):\n", "         # This updated implementation delegates to the automatic \"from_params\" method when it can,\n", "         # but (for now) it continues to support the old way with a `DeprecationWarning`.\n", "         if 'token_embedders' in params:\n", "-            # We use `<PERSON><PERSON><PERSON><PERSON><PERSON>dder` as the first argument to super() so that we get\n", "-            # Registrable.from_params.\n", "-            return super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, cls).from_params(params=params, vocab=vocab)\n", "+            # Have to use __func__ to unbind the classmethod from `FromParams`\n", "+            # so that we can pass in this class.\n", "+            return FromParams.from_params.__func__(cls, params=params, vocab=vocab)\n", " \n", "         # Warn that the original behavior is deprecated\n", "         warnings.warn(DeprecationWarning(\"the token embedders for BasicTextFieldEmbedder should now \"\n", "</pre><hr class=\"wide-line\"><li><strong>302  This kind of indirection is very confusing for both users and developers. However, if we really have to hide cassandra from the config file I suppose it's unavoidable.</strong></li><pre>@@ -148,9 +148,16 @@ final public class TxFactoryJanus extends TxFactoryAbstract<GraknTxJanus, JanusG\n", "                 value = storageBackendMapper.get(value);\n", "             }\n", " \n", "+            //Inject properties into other default properties\n", "+            if(overrideMap.contains<PERSON><PERSON>(key)){\n", "+                builder.set(overrideMap.get(key), value);\n", "+            }\n", "+\n", "             builder.set(key.toString(), value);\n", "         });\n", " \n", "+\n", "+\n", "         LOG.debug(\"Opening graph on {}\", address);\n", "         return builder.open();\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>303  I'm not sure this is really worth logging.</strong></li><pre>@@ -103,14 +103,12 @@ func HostKey(id string) datastore.Key {\n", " \n", " func convert(results datastore.Results) ([]Host, error) {\n", " \thosts := make([]Host, results.Len())\n", "-\tplog.WithField(\"results\", results).Debug(\"convert results\", results)\n", " \tfor idx := range hosts {\n", " \t\tvar host Host\n", " \t\terr := results.Get(idx, &host)\n", " \t\tif err != nil {\n", " \t\t\treturn nil, err\n", " \t\t}\n", "-\t\tplog.<PERSON><PERSON><PERSON>(\"host\", host).Debug(\"Adding host\")\n", " \t\thosts[idx] = host\n", " \t}\n", " \treturn hosts, nil\n", "</pre><hr class=\"wide-line\"><li><strong>304  Is there any point in this check (is the following check a superset of this one)?</strong></li><pre>@@ -54,9 +54,6 @@ public class BlockInfoEntry extends JournalEntry {\n", " \n", "   @Override\n", "   public boolean equals(Object o) {\n", "-    if (this == o) {\n", "-      return true;\n", "-    }\n", "     if (o == null || getClass() != o.getClass()) {\n", "       return false;\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>305  is this the same as the count variable below ?</strong></li><pre>@@ -125,12 +125,13 @@ void PlayerSocial::SendSocialList(Player* player, uint32 flags)\n", " {\n", "     ASSERT(player);\n", " \n", "-    uint32 size = GetNumberOfSocialsWithFlag(SocialFlag(flags));\n", "-\n", "     uint32 count = 0;\n", "-    WorldPacket data(SMSG_CONTACT_LIST, (4 + 4 + size * 25)); // just can guess size\n", "+\n", "+    WorldPacket data(SMSG_CONTACT_LIST, (4 + 4 + _playerSocialMap.size() * 25)); // just can guess size\n", "     data << uint32(flags);                                    // 0x1 = Friendlist update. 0x2 = Ignorelist update. 0x4 = Mutelist update.\n", "-    data << uint32(size);                                     // friends count\n", "+\n", "+    size_t wpos = data.wpos();\n", "+    data << uint32(0);                                        // placeholder for contacts count\n", " \n", "     for (PlayerSocialMap::value_type& v : _playerSocialMap)\n", "     {\n", "</pre><hr class=\"wide-line\"><li><strong>306  Why is fetchColumn() returning an array? Shouldn't it return the scalar value in the column?</strong></li><pre>@@ -4712,7 +4712,15 @@ class QueryTest extends TestCase\n", "             ->limit(1);\n", "         $statement = $query->execute();\n", "         $results = $statement->fetchColumn(0);\n", "-        $this->assertSame('2', $results[0]);\n", "+        $this->assertSame('2', $results);\n", "+\n", "+        $statement = $query->execute();\n", "+        $results = $statement->fetchColumn(1);\n", "+        $this->assertSame('2', $results);\n", "+\n", "+        $statement = $query->execute();\n", "+        $results = $statement->fetchColumn(2);\n", "+        $this->assertSame('0', $results);\n", "     }\n", " \n", "     /**\n", "</pre><hr class=\"wide-line\"><li><strong>307  Since we'll have the `hardware_keyboard` metadata I don't think it's necessary to add properties to these events.</strong></li><pre>@@ -262,15 +262,9 @@ public class PostPreviewActivity extends AppCompatActivity {\n", "     private void publishPost() {\n", "         if (!isFinishing() && NetworkUtils.checkConnection(this)) {\n", "             if (!mPost.isLocalDraft()) {\n", "-                Map<String, Object> properties = new HashMap<>();\n", "-\n", "-                Configuration configuration = WordPress.getContext().getResources().getConfiguration();\n", "-                properties.put(\"hardware_keyboard\", configuration.keyboard != Configuration.KEYBOARD_NOKEYS);\n", "-\n", "                 AnalyticsUtils.trackWithBlogDetails(\n", "                         AnalyticsTracker.Stat.EDITOR_UPDATED_POST,\n", "-                        WordPress.getBlog(mPost.getLocalTableBlogId()),\n", "-                        properties\n", "+                        WordPress.getBlog(mPost.getLocalTableBlogId())\n", "                 );\n", "             }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>308  We'll need an overloaded `count` that has a `StateStoreSupplier<KeyValueStore>` param</strong></li><pre>@@ -147,4 +147,17 @@ public interface KGroupedTable<K, V> {\n", "      */\n", "     KTable<K, Long> count(String storeName);\n", " \n", "+    /**\n", "+     * Count number of records of this stream by the selected key into a new instance of {@link KTable}.\n", "+     * The resulting {@link KTable} will be materialized in a local state\n", "+     * store given by state store supplier. Also a changelog topic named \"${applicationId}-${storeName}-changelog\"\n", "+     * will be automatically created in Kafka for failure recovery, where \"applicationID\"\n", "+     * is specified by the user in {@link org.apache.kafka.streams.StreamsConfig}.\n", "+     *\n", "+     * @param storeSupplier user defined state store supplier {@link StateStoreSupplier}\n", "+     * @return a {@link KTable} with same key and {@link Long} value type as this {@link KGroupedTable},\n", "+     * containing the number of values for each key\n", "+     */\n", "+    KTable<K, Long> count(final StateStoreSupplier<KeyValueStore> storeSupplier);\n", "+\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>309  Should this be private? `_Batch..`</strong></li><pre>@@ -2,7 +2,7 @@ import ray\n", " \n", " \n", " @ray.remote(num_cpus=0)\n", "-class BatchLogsReporter:\n", "+class _BatchLogsReporter:\n", "     def __init__(self):\n", "         # we need the new_data field to allow sending back None as the legs\n", "         self._logs = {\"new_data\": False, \"data\": None}\n", "</pre><hr class=\"wide-line\"><li><strong>310  `titleized_slug` would be better here, I think. Methods that don't take args should describe a condition (e.g. \"titleized\") whereas methods that do take arguments should describe an action (e.g. \"titleize\").</strong></li><pre>@@ -109,11 +109,11 @@ module Jekyll\n", "     #\n", "     # Returns the post title\n", "     def title\n", "-      self.data.fetch(\"title\", self.titleize_slug)\n", "+      self.data.fetch(\"title\", self.titleized_slug)\n", "     end\n", " \n", "     # Turns the post slug into a suitable title\n", "-    def titleize_slug\n", "+    def titleized_slug\n", "       self.slug.split('-').select {|w| w.capitalize! || w }.join(' ')\n", "     end\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>311  Seems a touch odd that the dialog gets created with a layout, but only uses the truncate field of the layout.</strong></li><pre>@@ -285,7 +285,7 @@ void Dialog::Init(const string &message, const Font::Layout &layout, bool canCan\n", " \ttext.SetAlignment(WrappedText::JUSTIFIED);\n", " \ttext.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(WIDTH - 20);\n", " \ttext.SetFont(FontSet::Get(14));\n", "-\ttext.SetTruncate(layout.truncate);\n", "+\ttext.SetTruncate(truncate);\n", " \t\n", " \ttext.Wrap(message);\n", " \t\n", "</pre><hr class=\"wide-line\"><li><strong>312  minor critique, since caching is introduced, bring the if statement for it closer so it's less of a jump? I am sure they are both near jumps, but perhaps it can be optimized for jump prediction?</strong></li><pre>@@ -1387,12 +1387,12 @@ void overmap::draw(WINDOW *w, WINDOW *wbar, const tripoint &center,\n", " \n", "     // A small LRU cache: most oter_id's occur in clumps like forests of swamps.\n", "     // This cache helps avoid much more costly lookups in the full hashmap.\n", "-    constexpr size_t cache_size = 8;\n", "+    constexpr size_t cache_size = 8; // used below to calculate the next index\n", "     std::array<std::pair<oter_id, oter_t const*>, cache_size> cache {};\n", "     size_t cache_next = 0;\n", " \n", "-    auto const offset_x = cursx - (om_map_width  / 2);\n", "-    auto const offset_y = cursy - (om_map_height / 2);\n", "+    int const offset_x = cursx - (om_map_width  / 2);\n", "+    int const offset_y = cursy - (om_map_height / 2);\n", " \n", "     for (int i = 0; i < om_map_width; ++i) {\n", "         for (int j = 0; j < om_map_height; ++j) {\n", "</pre><hr class=\"wide-line\"><li><strong>313  (minor) I think we can omit the `!appOptions.channel` because the function would have exited at 289 if appOptions.channel were present. Up to you.</strong></li><pre>@@ -289,9 +289,11 @@ function loadAppAsync(appOptions) {\n", "     return loadProjectAndCheckAbuse(appOptions);\n", "   }\n", " \n", "-  // shouldLoadChannel will be true for channel backed levels on publicly-cached pages\n", "-  // We will need to load the channel client-side from api/user_progress\n", "-  const shouldLoadChannel = appOptions.shouldLoadChannel && !appOptions.channel;\n", "+  // If the level requires a channel but no channel was passed from the server through app_options,\n", "+  // that indicates that the level was cached and the channel id needs to be loaded client-side\n", "+  // through the user_progress request\n", "+  const shouldGetChannelId =\n", "+    appOptions.levelRequiresChannel && !appOptions.channel;\n", " \n", "   return new Promise((resolve, reject) => {\n", "     if (appOptions.publicCaching) {\n", "</pre><hr class=\"wide-line\"><li><strong>314  Do we have testing that validates this is correct?</strong></li><pre>@@ -119,9 +119,9 @@ func (l *labelPairs) Reset() {}\n", " func (l *labelPairs) String() string {\n", " \tvar a []string\n", " \tfor _, lbl := range l.Label {\n", "-\t\ta = append(a, lbl.String())\n", "+\t\ta = append(a, fmt.Sprintf(\"label:<%s> \", lbl.String()))\n", " \t}\n", "-\treturn strings.Join(a, \"\\n\")\n", "+\treturn strings.Join(a, \"\")\n", " }\n", " \n", " func (*labelPairs) ProtoMessage() {}\n", "</pre><hr class=\"wide-line\"><li><strong>315  Feel free to add a static import for `ofMillis` and `STREAMS_TIME` to shorten this line.</strong></li><pre>@@ -64,7 +64,7 @@ public final class WordCountProcessorDemo {\n", "                 @SuppressWarnings(\"unchecked\")\n", "                 public void init(final ProcessorContext context) {\n", "                     this.context = context;\n", "-                    this.context.schedule(Duration.of<PERSON>illis(1000), PunctuationType.STREAM_TIME, timestamp -> {\n", "+                    this.context.schedule(Duration.ofSeconds(1), PunctuationType.STREAM_TIME, timestamp -> {\n", "                         try (final KeyValueIterator<String, Integer> iter = kvStore.all()) {\n", "                             System.out.println(\"----------- \" + timestamp + \" ----------- \");\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>316  This does not ensure that **parent containers** have been updated before us, thus operations involving access parent properties (e.g. <see cref=\"DrawInfo\"/>) should not be executed in an overridden implementation.</strong></li><pre>@@ -1363,8 +1363,8 @@ namespace osu.Framework.Graphics\n", "         /// <summary>\n", "         /// Invalidates draw matrix and autosize caches.\n", "         /// <para>\n", "-        /// This does not ensure that the containing containers have been updated before us, thus updates involving\n", "-        /// parent states (e.g. DrawInfo) are NOT safe to run inside <see cref=\"Invalidate\"/>.\n", "+        /// This does not ensure that the parent containers have been updated before us, thus operations involving\n", "+        /// parent states (e.g. <see cref=\"DrawInfo\"/>) should not be executed in an overriden implementation.\n", "         /// </para>\n", "         /// </summary>\n", "         /// <returns>If the invalidate was actually necessary.</returns>\n", "</pre><hr class=\"wide-line\"><li><strong>317  I believe it would be more clear, if you import names, and then use names.BEAM_PACKAGE_NAME etc. in the code. What do you think?</strong></li><pre>@@ -51,7 +51,7 @@ from apache_beam.options.pipeline_options import WorkerOptions\n", " from apache_beam.runners.dataflow.internal import names\n", " from apache_beam.runners.dataflow.internal.clients import dataflow\n", " from apache_beam.runners.dataflow.internal.names import PropertyNames\n", "-from apache_beam.runners.internal.names import BEAM_PACKAGE_NAME, BEAM_SDK_NAME\n", "+from apache_beam.runners.internal import names as shared_names\n", " from apache_beam.runners.portability.stager import Stager\n", " from apache_beam.transforms import cy_combiners\n", " from apache_beam.transforms import DataflowDistributionCounter\n", "</pre><hr class=\"wide-line\"><li><strong>318  I think you can be more specific here and use `.rc-tags__tag-icon`. There's a lot of `rc-icon` elements on this template.</strong></li><pre>@@ -146,7 +146,7 @@ Template.createChannel.events({\n", " \t\tconst {username} = Blaze.getData(target);\n", " \t\tt.selectedUsers.set(t.selectedUsers.get().filter(user => user.username !== username));\n", " \t},\n", "-\t'click .rc-icon'(e, t) {\n", "+\t'click .rc-tags__tag-icon'(e, t) {\n", " \t\tconst {username} = Blaze.getData(t.find('.rc-tags__tag-text'));\n", " \t\tt.selectedUsers.set(t.selectedUsers.get().filter(user => user.username !== username));\n", " \t},\n", "</pre><hr class=\"wide-line\"><li><strong>319  Does `list-revisions` need to be handled here as well?</strong></li><pre>@@ -361,7 +361,11 @@ def _run_store_command(args):  # noqa: C901\n", "     elif args['status']:\n", "         snapcraft.status(\n", "             args['<snap-name>'], args['--series'], args['--arch'])\n", "-    el<PERSON> a<PERSON>['revisions']:\n", "+    elif args['history']:\n", "+        deprecations.handle_deprecation_notice('history')\n", "+        snapcraft.revisions(\n", "+            args['<snap-name>'], args['--series'], args['--arch'])\n", "+    elif args['revisions'] or args['list-revisions']:\n", "         snapcraft.revisions(\n", "             args['<snap-name>'], args['--series'], args['--arch'])\n", "     <PERSON><PERSON> a<PERSON>['close']:\n", "</pre><hr class=\"wide-line\"><li><strong>320  can better variable naming be used than `e` ? I know its from the spec but I have no idea what e is referring to</strong></li><pre>@@ -721,9 +721,10 @@ func ProcessPenaltiesAndExits(state *pb.BeaconState) *pb.BeaconState {\n", " \t\tpenalized := validator.PenalizedSlot/config.EpochLength +\n", " \t\t\tconfig.LatestPenalizedExitLength/2\n", " \t\tif state.Slot/config.EpochLength == penalized {\n", "-\t\t\te := (state.Slot / config.EpochLength) % config.LatestPenalizedExitLength\n", "-\t\t\ttotalAtStart := state.LatestPenalizedExitBalances[(e+1)%config.LatestPenalizedExitLength]\n", "-\t\t\ttotalAtEnd := state.LatestPenalizedExitBalances[e]\n", "+\t\t\tpenalizedEpoch := (state.Slot / config.EpochLength) % config.LatestPenalizedExitLength\n", "+\t\t\tpenalizedEpochStart := (penalizedEpoch + 1) % config.LatestPenalizedExitLength\n", "+\t\t\ttotalAtStart := state.LatestPenalizedExitBalances[penalizedEpochStart]\n", "+\t\t\ttotalAtEnd := state.LatestPenalizedExitBalances[penalizedEpoch]\n", " \t\t\ttotalPenalties := totalAtStart - totalAtEnd\n", " \n", " \t\t\tpenaltyMultiplier := totalPenalties * 3\n", "</pre><hr class=\"wide-line\"><li><strong>321  adding some assertions? E.g. the format and element number in the `result_df` is as expected.</strong></li><pre>@@ -53,10 +53,10 @@ class TestEstimatorForOpenVINO(TestCase):\n", "     def test_openvino_predict_ndarray(self):\n", "         input_data = np.random.random([20, 4, 3, 224, 224])\n", "         result = self.est.predict(input_data)\n", "-        print(result)\n", "+        assert isinstance(result, np.ndarray)\n", "+        assert result.shape[0] == 20\n", " \n", "     def test_openvino_predict_xshards(self):\n", "-        # xshards\n", "         input_data_list = [np.random.random([1, 4, 3, 224, 224]),\n", "                            np.random.random([2, 4, 3, 224, 224])]\n", "         sc = init_nncontext()\n", "</pre><hr class=\"wide-line\"><li><strong>322  This reads to me as equivalent to: ```python part_tasks = dsk.keys() ```</strong></li><pre>@@ -221,6 +221,7 @@ def to_orc(\n", "                 filename,\n", "             ],\n", "         )\n", "+    part_tasks = list(dsk.keys())\n", "     dsk[(final_name, 0)] = (lambda x: None, part_tasks)\n", "     graph = HighLevelGraph.from_collections((final_name, 0), dsk, dependencies=[df])\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>323  I think these should be the RunningTask* equivalents of these Last* fields.</strong></li><pre>@@ -81,7 +81,7 @@ func groupByTaskGroup(runningHosts []host.Host, taskQueue []model.TaskQueueItem)\n", " \tfor _, h := range runningHosts {\n", " \t\tgroupString := \"\"\n", " \t\tif h.RunningTask != \"\" && h.RunningTaskGroup != \"\" {\n", "-\t\t\tgroupString = makeTaskGroupString(h<PERSON>TaskGroup, h<PERSON>, h<PERSON>, h.<PERSON>)\n", "+\t\t\tgroupString = makeTaskGroupString(h.RunningTaskGroup, h.<PERSON>, h.<PERSON>, h.<PERSON>TaskVersion)\n", " \t\t}\n", " \t\tif data, exists := tgs[groupString]; exists {\n", " \t\t\tdata.hosts = append(data.hosts, h)\n", "</pre><hr class=\"wide-line\"><li><strong>324  Use `private` explicitly for private members. The name of private field starts with \"_\".</strong></li><pre>@@ -174,13 +174,13 @@ namespace SerializationTestTypes\n", "     public class DefaultCollections\n", "     {\n", "         [DataMember]\n", "-        ArrayList arrayList = new ArrayList() { new Person() };\n", "+        private ArrayList _arrayList = new ArrayList() { new Person() };\n", "         [DataMember]\n", "-        Dictionary<int, object> dictionary = new Dictionary<int, object>() { { 001, new CharClass() } };\n", "+        private Dictionary<int, object> _dictionary = new Dictionary<int, object>() { { 001, new CharClass() } };\n", "         [DataMember]\n", "-        Hashtable hashtable = new Hashtable() { { \"one\", new Version1() } };\n", "+        private Hashtable _hashtable = new Hashtable() { { \"one\", new Version1() } };\n", "         [DataMember]\n", "-        object[] singleDimArray = new object[] { new Employee() };\n", "+        private object[] _singleDimArray = new object[] { new Employee() };\n", "     }\n", " \n", "     [DataContract(Name = \"Car\", Namespace = \"TestingVersionTolerance\")]\n", "</pre><hr class=\"wide-line\"><li><strong>325  Recommend changing \"conditional branch\" to \"conditional branch or switch\"</strong></li><pre>@@ -263,8 +263,8 @@ void AggressiveDCEPass::AddBreaksAndContinuesToWorklist(\n", "                                           this](ir::Instruction* user) {\n", "     SpvOp op = user->opcode();\n", "     if (op == SpvOpBranchConditional || op == SpvOpSwitch) {\n", "-      // A conditional branch can only be a continue if it does not have a merge\n", "-      // instruction or its merge block is not the continue block.\n", "+      // A conditional branch or switch can only be a continue if it does not\n", "+      // have a merge instruction or its merge block is not the continue block.\n", "       ir::Instruction* hdrMerge = branch2merge_[user];\n", "       if (hdrMerge != nullptr && hdrMerge->opcode() == SpvOpSelectionMerge) {\n", "         uint32_t hdrMergeId =\n", "</pre><hr class=\"wide-line\"><li><strong>326  ```suggestion .put(\"airbyte/source-pipedrive\", new PipeDriveOAuthFlow(configRepository, httpClient)) .put(\"airbyte/source-quickbooks\", new QuickbooksOAuthFlow(configRepository, httpClient)) ```</strong></li><pre>@@ -46,8 +46,8 @@ public class OAuthImplementationFactory {\n", "         .put(\"airbyte/source-hubspot\", new HubspotOAuthFlow(configRepository, httpClient))\n", "         .put(\"airbyte/source-intercom\", new IntercomOAuthFlow(configRepository, httpClient))\n", "         .put(\"airbyte/source-instagram\", new InstagramOAuthFlow(configRepository, httpClient))\n", "-        .put(\"airbyte/source-quickbooks\", new QuickbooksOAuthFlow(configRepository, httpClient))\n", "         .put(\"airbyte/source-pipedrive\", new PipeDriveOAuthFlow(configRepository, httpClient))\n", "+        .put(\"airbyte/source-quickbooks\", new QuickbooksOAuthFlow(configRepository, httpClient))\n", "         .put(\"airbyte/source-salesforce\", new SalesforceOAuthFlow(configRepository, httpClient))\n", "         .put(\"airbyte/source-slack\", new SlackOAuthFlow(configRepository, httpClient))\n", "         .put(\"airbyte/source-snapchat-marketing\", new SnapchatMarketingOAuthFlow(configRepository, httpClient))\n", "</pre><hr class=\"wide-line\"><li><strong>327  If the value is a `datetime` object, then it should not be passed to `parse_date_format`. Should be handled in `parse_value` itself.</strong></li><pre>@@ -362,10 +362,7 @@ class Importer:\n", " \t\treturn value\n", " \n", " \tdef parse_date_format(self, value, df):\n", "-\t\tdate_format = self.get_date_format_for_df(df)\n", "-\t\tif not date_format:\n", "-\t\t\tdate_format = '%Y-%m-%d %H:%M:%S'\n", "-\n", "+\t\tdate_format = self.get_date_format_for_df(df) or '%Y-%m-%d %H:%M:%S'\n", " \t\ttry:\n", " \t\t\treturn datetime.strptime(value, date_format)\n", " \t\texcept:\n", "</pre><hr class=\"wide-line\"><li><strong>328  I don't see any code change here to *populate* the collection you're adding. Per the pattern, you're expected to create a new factory, passing the collection in as an argument. That would cause all JoinableTask's spawned by this JTF to automatically be added to the collection. Otherwise (as you have it now), any async task will have to be manually added to the collection, which might be easy to forget. But it gives you more control over which tasks will block, which maybe you need. But shouldn't *all* tasks block shutdown?</strong></li><pre>@@ -33,7 +33,7 @@ namespace Microsoft.CodeAnalysis.Editor.Shared.Utilities\n", "             HasMainThread = joinableTaskContext.MainThread.IsAlive;\n", "             JoinableTaskContext = joinableTaskContext;\n", "             JoinableTaskFactory = joinableTaskContext.Factory;\n", "-            JoinableTaskCollection = new JoinableTaskCollection(JoinableTaskContext);\n", "+            ShutdownBlockingTasks = new JoinableTaskCollection(JoinableTaskContext);\n", "         }\n", " \n", "         /// <inheritdoc/>\n", "</pre><hr class=\"wide-line\"><li><strong>329  Could you call `m.markRegistered()` here?</strong></li><pre>@@ -92,8 +92,7 @@ func (m *DeviceManager) confirmRegistered() error {\n", " \t}\n", " \n", " \tif device.Serial != \"\" {\n", "-\t\tm.registered = true\n", "-\t\tclose(m.reg)\n", "+\t\tm.markRegistered()\n", " \t}\n", " \treturn nil\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>330  I find this slightly clearer, but not a lot: ``` maxval = total_length if total_length else UnknownLength if is_dumb_terminal(): widgets = [message] elif maxval == UnknownLength: widgets = [message, AnimatedMarker()] else: widgets = [message, Bar(marker='=', left='[', right=']'), ' ', Percentage()] ```</strong></li><pre>@@ -37,7 +37,7 @@ def download_requests_stream(request_stream, destination, message=None):\n", "         total_length = int(request_stream.headers.get('Content-Length', '0'))\n", " \n", "     if total_length and is_dumb_terminal():\n", "-        widgets = [message]\n", "+        widgets = [message, ' ', Percentage()]\n", "         maxval = total_length\n", "     elif total_length and not is_dumb_terminal():\n", "         widgets = [message,\n", "</pre><hr class=\"wide-line\"><li><strong>331  Should this explain why we set it this way? I think anyone would probably switch it back if no explanation is given</strong></li><pre>@@ -8,6 +8,9 @@\n", " \n", "         public bool SupportsCrossQueueTransactions => true;\n", " \n", "+        // Disable native pub-sub for tests that require message-driven pub-sub. The tests in the \"Core\"\n", "+        // folder are not shipped to downstreams and therefore are only executed on this test project and\n", "+        // \"NServiceBus.Learning.AcceptanceTests\" (which is running the tests using native pub-sub).\n", "         public bool SupportsNativePubSub => false;\n", " \n", "         public bool SupportsDelayedDelivery => false;\n", "</pre><hr class=\"wide-line\"><li><strong>332  We probably want this on the \"derived\" implementations?</strong></li><pre>@@ -82,7 +82,3 @@ func (c *commonClient) Invitations() chan protocol.SessionInvitation {\n", " \tdefer c.mut.<PERSON><PERSON><PERSON><PERSON>()\n", " \treturn c.invitations\n", " }\n", "-\n", "-func (c *commonClient) String() string {\n", "-\treturn fmt.Sprintf(\"commonClient/@%p\", c)\n", "-}\n", "</pre><hr class=\"wide-line\"><li><strong>333  The is not right. Should be a parametrized type of List, created through TypeToken</strong></li><pre>@@ -141,7 +141,7 @@ public class DatasetInstanceHandler extends AbstractHttpHandler {\n", "           }\n", "         }\n", "         responder.sendJson(HttpResponseStatus.OK, joinBuilder.build(),\n", "-                           ImmutableList.class, GSON);\n", "+                           new TypeToken<ImmutableList<?>>() { }.getType(), GSON);\n", "         return;\n", "       } catch (Throwable t) {\n", "         LOG.error(\"<PERSON>aught exception while listing explorable datasets\", t);\n", "</pre><hr class=\"wide-line\"><li><strong>334  This doesn't need to be a `System.getProperty`. This test has no real reliance on Jetty. Instead I would suggest: ```java private final static int PORT = 8080; ``` Although it is arguable that you even need it as a global constant.</strong></li><pre>@@ -13,8 +13,6 @@ import static org.junit.Assert.fail;\n", " \n", " public class XmldbURITest {\n", " \n", "-    private final static String jettyPort = System.getProperty(\"jetty.port\", \"8080\");\n", "-\n", "     @Test\n", "     public void xmldbURIConstructors() throws URISyntaxException {\n", "         XmldbURI.xmldbUriFor(\".\");\n", "</pre><hr class=\"wide-line\"><li><strong>335  Is it possible to test that this fails because the limit is not greater than one?</strong></li><pre>@@ -7,9 +7,13 @@\n", " package org.mule.test.module.extension.source;\n", " \n", " import static java.util.Arrays.asList;\n", "+import static org.mockito.ArgumentMatchers.startsWith;\n", "+import static org.mule.functional.junit4.matchers.ThrowableCauseMatcher.hasCause;\n", "+import static org.mule.functional.junit4.matchers.ThrowableMessageMatcher.hasMessage;\n", " import static org.mule.runtime.api.util.MuleSystemProperties.ENABLE_SDK_POLLING_SOURCE_LIMIT;\n", " \n", " import org.junit.ClassRule;\n", "+import org.junit.rules.ExpectedException;\n", " import org.junit.runners.Parameterized;\n", " \n", " import org.mule.tck.junit4.rule.SystemProperty;\n", "</pre><hr class=\"wide-line\"><li><strong>336  Use `ROUNDN` in exception message.</strong></li><pre>@@ -860,7 +860,7 @@ public final class MathFunctions\n", "                 @LiteralParameter(\"p\") long numPrecision,\n", "                 @LiteralParameter(\"s\") long numScale,\n", "                 @SqlType(\"decimal(p, s)\") long num,\n", "-                @SqlType(StandardTypes.INTEGER) long decimals)\n", "+                @SqlType(StandardTypes.BIGINT) long decimals)\n", "         {\n", "             if (num == 0 || numPrecision - numScale + decimals <= 0) {\n", "                 return 0;\n", "</pre><hr class=\"wide-line\"><li><strong>337  It would help when debugging with the logs if we listed both the IngestJob ID and the IngestJobPipeline ID. This applies throughout the package.</strong></li><pre>@@ -108,9 +108,9 @@ final class DataSourceIngestPipeline {\n", "                     this.ingestJobPipeline.updateDataSourceIngestProgressBarDisplayName(displayName);\n", "                     this.ingestJobPipeline.switchDataSourceIngestProgressBarToIndeterminate();\n", "                     DataSourceIngestPipeline.ingestManager.setIngestTaskProgress(task, module.getDisplayName());\n", "-                    logger.log(Level.INFO, \"{0} analysis of {1} (jobId={2}) starting\", new Object[]{module.getDisplayName(), this.ingestJobPipeline.getDataSource().getName(), this.ingestJobPipeline.getId()}); //NON-NLS\n", "+                    logger.log(Level.INFO, \"{0} analysis of {1} (pipeline={2}) starting\", new Object[]{module.getDisplayName(), ingestJobPipeline.getDataSource().getName(), ingestJobPipeline.getId()}); //NON-NLS\n", "                     module.process(dataSource, new DataSourceIngestModuleProgress(this.ingestJobPipeline));\n", "-                    logger.log(Level.INFO, \"{0} analysis of {1} (jobId={2}) finished\", new Object[]{module.getDisplayName(), this.ingestJobPipeline.getDataSource().getName(), this.ingestJobPipeline.getId()}); //NON-NLS\n", "+                    logger.log(Level.INFO, \"{0} analysis of {1} (pipeline={2}) finished\", new Object[]{module.getDisplayName(), ingestJobPipeline.getDataSource().getName(), ingestJobPipeline.getId()}); //NON-NLS\n", "                 } catch (Throwable ex) { // Catch-all exception firewall\n", "                     errors.add(new IngestModuleError(module.getDisplayName(), ex));\n", "                 }\n", "</pre><hr class=\"wide-line\"><li><strong>338  Can we change the method name to getIndexExprs</strong></li><pre>@@ -729,7 +729,7 @@ public class BLangExecutor implements NodeExecutor {\n", "             throw new BallerinaException(\"variable '\" + arrayVarRefExpr.getVarName() + \"' is null\");\n", "         }\n", " \n", "-        Expression[] indexExpr = arrayMapAccessExpr.getIndexExpr();\n", "+        Expression[] indexExpr = arrayMapAccessExpr.getIndexExprs();\n", " \n", "         // Check whether this collection access expression is in the left hand of an assignment expression\n", "         // If yes skip setting the value;\n", "</pre><hr class=\"wide-line\"><li><strong>339  You can remove `Hidden: false`, since this is the default.</strong></li><pre>@@ -794,13 +794,13 @@ var defaultSysVars = []*SysVar{\n", " \t{Scope: ScopeGlobal, Name: InitConnect, Value: \"\"},\n", " \n", " \t/* TiDB specific variables */\n", "-\t{Scope: ScopeGlobal, Name: TiDBTSOClientBatchMaxWaitTime, Value: strconv.Itoa(DefTiDBTSOClientBatchMaxWaitTime), Hidden: false, Type: TypeInt, MinValue: 0, MaxValue: 10, GetGlobal: func(sv *SessionVars) (string, error) {\n", "+\t{Scope: ScopeGlobal, Name: TiDBTSOClientBatchMaxWaitTime, Value: strconv.Itoa(DefTiDBTSOClientBatchMaxWaitTime), Type: TypeInt, MinValue: 0, MaxValue: 10, GetGlobal: func(sv *SessionVars) (string, error) {\n", " \t\treturn strconv.Itoa(int(MaxTSOBatchWaitInterval.Load())), nil\n", " \t}, SetGlobal: func(s *SessionVars, val string) error {\n", " \t\tMaxTSOBatchWaitInterval.Store(tidbOptInt64(val, DefTiDBTSOClientBatchMaxWaitTime))\n", " \t\treturn nil\n", " \t}},\n", "-\t{Scope: ScopeGlobal, Name: TiDBTSOEnableFollowerProxy, Value: BoolToOnOff(DefTiDBTSOEnableFollowerProxy), Hidden: false, Type: TypeBool, GetGlobal: func(sv *SessionVars) (string, error) {\n", "+\t{Scope: ScopeGlobal, Name: TiDBTSOEnableFollowerProxy, Value: BoolToOnOff(DefTiDBTSOEnableFollowerProxy), Type: TypeBool, GetGlobal: func(sv *SessionVars) (string, error) {\n", " \t\treturn BoolToOnOff(EnableTSOFollowerProxy.Load()), nil\n", " \t}, SetGlobal: func(s *SessionVars, val string) error {\n", " \t\tEnableTSOFollowerProxy.Store(TiDBOptOn(val))\n", "</pre><hr class=\"wide-line\"><li><strong>340  do we need this? as you have the check further down that compares sizes?</strong></li><pre>@@ -99,12 +99,6 @@ export default class Filter\n", "          * @member {boolean}\n", "          */\n", "         this.autoFit = true;\n", "-\n", "-        /**\n", "-         * If enabled, filterArea for element will be same size of the screen. PixiJS v3 mode.\n", "-         * @member {boolean}\n", "-         */\n", "-        this.fullScreen = false;\n", "     }\n", " \n", "     /**\n", "</pre><hr class=\"wide-line\"><li><strong>341  >who can logged in Should be \"log in,\" I think.</strong></li><pre>@@ -21,7 +21,7 @@ FactoryBot.define do\n", "     password_confirmation(&:password)\n", "     email { generate(:email) }\n", " \n", "-    # By default, create activated users who can logged in, since we use\n", "+    # By default, create activated users who can log in, since we use\n", "     # devise :confirmable.\n", "     confirmed_at { Faker::Time.backward }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>342  You can use infoschema.ErrTooLongIdent.Gen(format, msg).</strong></li><pre>@@ -295,7 +295,7 @@ func (d *ddl) CreateSchema(ctx context.Context, schema model.CIStr, charsetInfo\n", " \t}\n", " \n", " \tif is.SchemaNameTooLong(schema) {\n", "-\t\treturn errors.Trace(infoschema.ErrTooLongIdent)\n", "+\t\treturn errors.Trace(infoschema.ErrTooLongIdent.Gen(\"too long schema %s\", schema.L))\n", " \t}\n", " \n", " \tschemaID, err := d.genGlobalID()\n", "</pre><hr class=\"wide-line\"><li><strong>343  We don't need to know which node is selected ;). That's only for analysis forms.</strong></li><pre>@@ -46,8 +46,7 @@ module.exports = cdb.core.View.extend({\n", "       label: _t('editor.layers.menu-tab-pane-labels.popup'),\n", "       createContentView: function () {\n", "         return new PopupView({\n", "-          layerDefinitionModel: self.layerDefinitionModel,\n", "-          selectedNodeId: self.options.selectedNodeId\n", "+          layerDefinitionModel: self.layerDefinitionModel\n", "         });\n", "       }\n", "     }, {\n", "</pre><hr class=\"wide-line\"><li><strong>344  I don't know enough about SSH keys to see the specifics but I feel like we'd be better to parse the SSH key using the crypto library rather than checking the string length?</strong></li><pre>@@ -113,13 +113,9 @@ func parseUsernameFromAuthorizedKeysPath(input string) *string {\n", " \treturn nil\n", " }\n", " \n", "-// TODO - leverage \"golang.org/x/crypto/ssh\" for key validation?\n", "-// ValidateSSHKey performs some basic validation on supplied SSH Keys - Signature and basic key length are evaluated\n", "+// ValidateSSHKey performs some basic validation on supplied SSH Keys - Encoded Signature and Key Size are evaluated\n", "+// Will require rework if/when other Key Types are supported\n", " func ValidateSSHKey(i interface{}, k string) (warnings []string, errors []error) {\n", "-\tpermittedKeyTypes := []string{\n", "-\t\t\"ssh-rsa\",\n", "-\t}\n", "-\n", " \tv, ok := i.(string)\n", " \tif !ok {\n", " \t\treturn nil, []error{fmt.<PERSON><PERSON><PERSON>(\"expected type of %q to be string\", k)}\n", "</pre><hr class=\"wide-line\"><li><strong>345  We shouldnt use !empty() with methods: ``` || $this->request->data() ```</strong></li><pre>@@ -93,7 +93,7 @@ class CsrfComponent extends Component\n", "         if ($request->is('get') && $cookieData === null) {\n", "             $this->_setCookie($request, $response);\n", "         }\n", "-        if ($request->is(['put', 'post', 'delete', 'patch']) || !empty($request->data())) {\n", "+        if ($request->is(['put', 'post', 'delete', 'patch']) || $request->data()) {\n", "             $this->_validateToken($request);\n", "             unset($request->data[$this->_config['field']]);\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>346  `arugments` is a misspelling of `arguments` (from `misspell`) ```suggestion ```</strong></li><pre>@@ -33,7 +33,7 @@ type MsgSubmitProposalI interface {\n", " }\n", " \n", " // NewMsgSubmitProposalI constructs a MsgSubmitProposalI instance based on the Codec instance passed in, populated with\n", "-// the provided content, initialDeposit and proposer arugments\n", "+// the provided content, initialDeposit and proposer arguments\n", " func NewMsgSubmitProposalI(cdc Codec, content Content, initialDeposit sdk.Coins, proposer sdk.AccAddress) (MsgSubmitProposalI, error) {\n", " \tmsg := cdc.NewMsgSubmitProposalI()\n", " \terr := msg.Set<PERSON>ontent(content)\n", "</pre><hr class=\"wide-line\"><li><strong>347  I would rather you add the gpu_contiguous import to the ..basic_ops line below. If you really want to import basic_op do it with a relative import `from .. import basic_ops`.</strong></li><pre>@@ -7,7 +7,7 @@ from theano.tests.unittest_tools import SkipTest\n", " from theano.tensor.tests import test_basic\n", " \n", " import theano.sandbox.gpuarray\n", "-from theano.sandbox.gpuarray import basic_ops\n", "+from .. import basic_ops\n", " from ..type import GpuArrayType, gpuarray_shared_constructor\n", " from ..basic_ops import (GpuAlloc, GpuReshape, gpu_alloc,\n", "                          gpu_from_host, host_from_gpu)\n", "</pre><hr class=\"wide-line\"><li><strong>348  Move that on same line and add a space after `if` please.</strong></li><pre>@@ -42,8 +42,7 @@ class ArchiveDownloaderTest extends \\PHPUnit_Framework_TestCase\n", "         $expected = 'https://github.com/composer/composer/zipball/master';\n", "         $url = $method->invoke($downloader, $expected);\n", " \n", "-        if(extension_loaded('openssl'))\n", "-        {\n", "+        if (extension_loaded('openssl')) {\n", "             $this->assertEquals($expected, $url);\n", "         } else {\n", "             $this->assertEquals('http://nodeload.github.com/composer/composer/zipball/master', $url);\n", "</pre><hr class=\"wide-line\"><li><strong>349  I don't think this is the maximum. It is a vector of lengths</strong></li><pre>@@ -119,7 +119,7 @@ class Decoder(object):\n", "         steps.\n", "       final_state: RNNCell final state (possibly nested tuple of) tensor[s] for\n", "         last time step.\n", "-      sequence_lengths: <PERSON><PERSON>r `int32` tensor. Maximum sequence length.\n", "+      sequence_lengths: `int32` tensor containing lengths of each sequences.\n", " \n", "     Returns:\n", "       `(final_outputs, final_state)`: `final_outputs` is an object containing\n", "</pre><hr class=\"wide-line\"><li><strong>350  Why pass _begin_ and _wg_ as parameters? `go func() {}` won't work?</strong></li><pre>@@ -182,14 +182,14 @@ func (s *testClientSuite) TestTSORace(c *C) {\n", " \tcount := 10\n", " \twg.Add(count)\n", " \tfor i := 0; i < count; i++ {\n", "-\t\tgo func(begin chan struct{}, wg *sync.WaitGroup) {\n", "+\t\tgo func() {\n", " \t\t\t<-begin\n", " \t\t\tfor i := 0; i < 100; i++ {\n", " \t\t\t\t_, _, err := s.client.GetTS(context.Background())\n", " \t\t\t\t<PERSON><PERSON>(err, <PERSON><PERSON><PERSON>)\n", " \t\t\t}\n", " \t\t\twg.<PERSON>()\n", "-\t\t}(begin, &wg)\n", "+\t\t}()\n", " \t}\n", " \tclose(begin)\n", " \twg.<PERSON>()\n", "</pre><hr class=\"wide-line\"><li><strong>351  seems hasSameColumn only check column number length, how about rename to `hasSameColumnLen`?</strong></li><pre>@@ -498,7 +498,7 @@ func CheckAllOneColumns(args ...expression.Expression) error {\n", " \treturn nil\n", " }\n", " \n", "-func columnNumber(e expression.Expression) int {\n", "+func columnCount(e expression.Expression) int {\n", " \tv, ok := e.(*Row)\n", " \tif ok {\n", " \t\t// TODO: add check, row constructor must have >= 2 columns\n", "</pre><hr class=\"wide-line\"><li><strong>352  No needs to change that here. `context.Background()` is always passed into.</strong></li><pre>@@ -205,7 +205,7 @@ func (n *ServiceNetwork) HandlePulse(ctx context.Context, pulse core.Pulse) {\n", " \n", " \ttraceID := \"pulse_\" + strconv.FormatUint(uint64(pulse.PulseNumber), 10)\n", " \n", "-\tctx, logger := inslogger.WithTraceField(context.Background(), traceID)\n", "+\tctx, logger := inslogger.WithTraceField(ctx, traceID)\n", " \tlogger.Infof(\"Got new pulse number: %d\", pulse.PulseNumber)\n", " \tif n.<PERSON> == nil {\n", " \t\tlogger.Error(\"PulseManager is not initialized\")\n", "</pre><hr class=\"wide-line\"><li><strong>353  This is again wrong. Pending orders can be updated. Users can pay and complete them.</strong></li><pre>@@ -287,12 +287,10 @@ class OrderDetail(ResourceDetail):\n", "                                                      \"You cannot update the status of a cancelled order\")\n", " \n", "         elif current_user.id == order.user_id:\n", "-            print(order.status)\n", "-            if order.status != 'initializing':\n", "+            if order.status != 'initializing' and order.status != 'pending':\n", "                 raise ForbiddenException({'pointer': ''},\n", "-                                         \"You cannot update a non-initialized order\")\n", "+                                         \"You cannot update a non-initialized or non-pending order\")\n", "             else:\n", "-                print('Hey----------------')\n", "                 for element in data:\n", "                     if element == 'is_billing_enabled' and order.status == 'completed' and data[element]\\\n", "                             and data[element] != getattr(order, element, None):\n", "</pre><hr class=\"wide-line\"><li><strong>354  TODO(github issue) vs TODO(username) ?</strong></li><pre>@@ -122,7 +122,7 @@ func (d *Driver) GetURL() (string, error) {\n", " \n", " // GetState returns the state that the host is in (running, stopped, etc)\n", " func (d *Driver) GetState() (state.State, error) {\n", "-\tif err := runningKubelet(d.exec); err != nil {\n", "+\tif err := checkKubelet(d.exec); err != nil {\n", " \t\tglog.Infof(\"kubelet not running: %v\", err)\n", " \t\treturn state.Stopped, nil\n", " \t}\n", "</pre><hr class=\"wide-line\"><li><strong>355  break in default case doesn't matter, so no need. Why have we done this change?</strong></li><pre>@@ -47,7 +47,6 @@ module.exports = CoreView.extend({\n", "         break;\n", "       default:\n", "         this._renderMosaic();\n", "-        break;\n", "     }\n", " \n", "     return this;\n", "</pre><hr class=\"wide-line\"><li><strong>356  Only \"if (! message.cameraId)\" does not resolve this.</strong></li><pre>@@ -34,7 +34,7 @@ var _onMessage = function (_message) {\n", "   let iceQueues = {};\n", "   let iceQueue;\n", " \n", "-  if (typeof message.cameraId === 'undefined' || !message.cameraId) {\n", "+  if (!message.cameraId) {\n", "     console.log(\"  [VideoManager] Undefined message.cameraId for session \", sessionId);\n", "     return;\n", "   }\n", "</pre><hr class=\"wide-line\"><li><strong>357  Could also add the exception name in the doc, like <PERSON> throw an **{@link OnErrorNotImplementedException}** ...</strong></li><pre>@@ -32,7 +32,7 @@ public final class Subscribers {\n", " \n", "     /**\n", "      * Returns an inert {@link Subscriber} that does nothing in response to the emissions or notifications \n", "-     * from any {@code Observable} it subscribes to.  Will throw an exception if {@link Subscriber#onError onError} \n", "+     * from any {@code Observable} it subscribes to.  Will throw an {@link OnErrorNotImplementedException} if {@link Subscriber#onError onError} \n", "      * method is called\n", "      *\n", "      * @return an inert {@code Observer}\n", "</pre><hr class=\"wide-line\"><li><strong>358  For IndexCount, we do not have this vector. Declaring this just for code readability.</strong></li><pre>@@ -165,10 +165,9 @@ public class IndexCountPlanNode extends AbstractScanPlanNode {\n", "             assert(m_searchkeyExpressions.size() > 0);\n", "             nullExprIndex = m_searchkeyExpressions.size() - 1;\n", "         }\n", "-        List<Boolean> noIgnoreNullCandidateFlagVector = null;\n", "         m_skip_null_predicate = IndexScanPlanNode.buildSkipNullPredicate(\n", "                 nullExprIndex, m_catalogIndex, m_tableScan,\n", "-                m_searchkeyExpressions, noIgnoreNullCandidateFlagVector);\n", "+                m_searchkeyExpressions, m_ignoreNullCandidate);\n", "         if (m_skip_null_predicate != null) {\n", "             m_skip_null_predicate.resolveForTable((Table)m_catalogIndex.getParent());\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>359  Is't this the same as ```throw new Error(`${plugin} is already installed`)```</strong></li><pre>@@ -91,6 +91,13 @@ function getPackageName(plugin) {\n", " function install(plugin, locally) {\n", "   const array = locally ? getLocalPlugins() : getPlugins();\n", "   return existsOnNpm(plugin)\n", "+    .catch(err => {\n", "+      const {statusCode} = err;\n", "+      if (statusCode && (statusCode === 404 || statusCode === 200)) {\n", "+        return Promise.reject(`${plugin} not found on npm`);\n", "+      }\n", "+      return Promise.reject(`${err.message}\\nPlugin check failed. Check your internet connection or retry later.`);\n", "+    })\n", "     .then(() => {\n", "       if (isInstalled(plugin, locally)) {\n", "         return Promise.reject(`${plugin} is already installed`);\n", "</pre><hr class=\"wide-line\"><li><strong>360  Seems like this should be an `Option<int>`</strong></li><pre>@@ -65,7 +65,7 @@ namespace Relayer\n", " \n", "         public bool ReceiveOnly { get; }\n", " \n", "-        public Option<string> UniqueResultsExpected { get; }\n", "+        public Option<int> UniqueResultsExpected { get; }\n", " \n", "         public override string ToString()\n", "         {\n", "</pre><hr class=\"wide-line\"><li><strong>361  Why did you abstract this out?</strong></li><pre>@@ -3800,9 +3800,6 @@ function printBindExpressionCallee(path, options, print) {\n", "   return concat([\"::\", path.call(print, \"callee\")]);\n", " }\n", " \n", "-function printNonNullExpression() {\n", "-  return \"!\";\n", "-}\n", " // We detect calls on member expressions specially to format a\n", " // common pattern better. The pattern we are looking for is this:\n", " //\n", "</pre><hr class=\"wide-line\"><li><strong>362  To note, was present prior to this PR, but if `$handle` should be an array and we need to check it its empty, `count` function is a better way to do this. PHP `empty` function has a lot of issues, and i avoid it. ```suggestion $handle = $this->handleTask($task); if (is_array($handle) && count($handle)) { ```</strong></li><pre>@@ -313,7 +313,8 @@ class Request extends AbstractRequest\n", "        //For the moment it's the Agent who informs us about the active tasks\n", "         if (property_exists($this->inventory->getRawData(), 'enabled-tasks')) {\n", "             foreach ($this->inventory->getRawData()->{'enabled-tasks'} as $task) {\n", "-                if ((!empty($handle = $this->handleTask($task)))) {\n", "+                $handle = $this->handleTask($task);\n", "+                if (is_array($handle) && count($handle)) {\n", "                    // Insert related task information under tasks list property\n", "                     $response['tasks'][$task] = $handle;\n", "                 } else {\n", "</pre><hr class=\"wide-line\"><li><strong>363  ```suggestion $allowed_video_mime_types = apply_filters( 'amp_story_allowed_video_types', [ 'video/mp4' ] ); ``` Let's make it clear that this is for stories only.</strong></li><pre>@@ -942,11 +942,11 @@ class AMP_Story_Post_Type {\n", " \t\t * This can be used to add additionally supported formats, for example by plugins\n", " \t\t * that do video transcoding.\n", " \t\t *\n", "-\t\t * @since 1.2\n", "+\t\t * @since 1.3\n", " \t\t *\n", "-\t\t * @param array Supported video mime types.\n", "+\t\t * @param array Allowed video mime types.\n", " \t\t */\n", "-\t\t$allowed_video_mime_types = apply_filters( 'amp_supported_video_types', [ 'video/mp4' ] );\n", "+\t\t$allowed_video_mime_types = apply_filters( 'amp_story_allowed_video_types', [ 'video/mp4' ] );\n", " \n", " \t\t// If `$allowed_video_mime_types` doesn't have valid data or is empty add default supported type.\n", " \t\tif ( ! is_array( $allowed_video_mime_types ) || empty( $allowed_video_mime_types ) ) {\n", "</pre><hr class=\"wide-line\"><li><strong>364  the resource name needs to have an RG on it... ```suggestion name = \"acctestRG-DigitalTwins-%d\" ```</strong></li><pre>@@ -163,7 +163,7 @@ provider \"azurerm\" {\n", " }\n", " \n", " resource \"azurerm_resource_group\" \"test\" {\n", "-  name     = \"acctest-DigitalTwins-%d\"\n", "+  name     = \"acctestRG-DigitalTwins-%d\"\n", "   location = \"%s\"\n", " }\n", " `, data.RandomInteger, data.Locations.Primary)\n", "</pre><hr class=\"wide-line\"><li><strong>365  We'll want to `addOptionalFieldsToSchema` for project as well, so it can be configured by the user.</strong></li><pre>@@ -10,6 +10,7 @@ func dataSourceGoogleCloudRunService() *schema.Resource {\n", " \n", " \tdsSchema := datasourceSchemaFromResourceSchema(resourceCloudRunService().Schema)\n", " \taddRequiredFieldsToSchema(dsSchema, \"name\", \"location\")\n", "+\taddOptionalFieldsToSchema(dsSchema, \"project\")\n", " \n", " \treturn &schema.Resource{\n", " \t\tRead:   dataSourceGoogleCloudRunServiceRead,\n", "</pre><hr class=\"wide-line\"><li><strong>366  you can use `assertRaises` to test the exception case.</strong></li><pre>@@ -1068,13 +1068,12 @@ class SQLTests(ReusedPySparkTestCase):\n", " \n", "     # regression test for SPARK-10417\n", "     def test_column_iterator(self):\n", "-        # Catch exception raised during improper construction\n", "-        try:\n", "+\n", "+        def foo():\n", "             for x in self.df.key:\n", "                 break\n", "-            self.assertEqual(0, 1)\n", "-        except TypeError:\n", "-            self.assertEqual(1, 1)\n", "+\n", "+        self.assertRaises(TypeError, foo)\n", " \n", " \n", " class HiveContextSQLTests(ReusedPySparkTestCase):\n", "</pre><hr class=\"wide-line\"><li><strong>367  We should break the loop after we have extra internal handle.</strong></li><pre>@@ -1194,6 +1194,7 @@ func (e *InsertValues) getColumns(tableCols []*table.Column) ([]*table.Column, e\n", " \tfor _, col := range cols {\n", " \t\tif col.Name.L == model.ExtraHandleName.L {\n", " \t\t\te.hasExtraHandle = true\n", "+\t\t\tbreak\n", " \t\t}\n", " \t}\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>368  Rather than keeping this as a private function on EventData that we then have to pylint:disable, we could move it out into a separate util function. ```python def get_last_enqueued_event_properties(event_data): \"\"\"Extracts the last enqueued event in from the received event delivery annotations. :rtype: Dict[str, Any] \"\"\" if event_data.message.delivery_annotations: sequence_number = event_data.message.delivery_annotations.get(PROP_LAST_ENQUEUED_SEQUENCE_NUMBER, None) enqueued_time_stamp = event_data.message.delivery_annotations.get(PROP_LAST_ENQUEUED_TIME_UTC, None) if enqueued_time_stamp: enqueued_time_stamp = utc_from_timestamp(float(enqueued_time_stamp)/1000) retrieval_time_stamp = event_data.message.delivery_annotations.get(PROP_RUNTIME_INFO_RETRIEVAL_TIME_UTC, None) if retrieval_time_stamp: retrieval_time_stamp = utc_from_timestamp(float(retrieval_time_stamp)/1000) offset_bytes = event_data.message.delivery_annotations.get(PROP_LAST_ENQUEUED_OFFSET, None) offset = offset_bytes.decode('UTF-8') if offset_bytes else None return { \"sequence_number\": sequence_number, \"offset\": offset, \"enqueued_time\": enqueued_time_stamp, \"retrieval_time\": retrieval_time_stamp } return None ```</strong></li><pre>@@ -40,6 +40,7 @@ class PartitionContext(object):\n", "         \"\"\"\n", "         if self._last_received_event:\n", "             return self._last_received_event._get_last_enqueued_event_properties()  # pylint: disable=protected-access\n", "+        return None\n", " \n", "     def update_checkpoint(self, event):\n", "         \"\"\"\n", "</pre><hr class=\"wide-line\"><li><strong>369  What happens if `GOOGLE_CLOUD_PROJECT` is unset? Might be worth making it a class variable and using a `@Before` to verify it is set correctly, else outputting a reasonable error message.</strong></li><pre>@@ -16,11 +16,15 @@\n", " \n", " package com.example.trace;\n", " \n", "+import com.google.common.base.Strings;\n", "+\n", " import io.opencensus.exporter.trace.stackdriver.StackdriverTraceExporter;\n", " \n", " import java.io.IOException;\n", " \n", " import org.junit.After;\n", "+import org.junit.Assert;\n", "+import org.junit.BeforeClass;\n", " import org.junit.Test;\n", " import org.junit.runner.RunWith;\n", " import org.junit.runners.JUnit4;\n", "</pre><hr class=\"wide-line\"><li><strong>370  This check was already done on line 463. Is it necessary once more?</strong></li><pre>@@ -474,7 +474,6 @@ class PairedRoutingTest extends DependencyInjectedTestCase {\n", " \t\t\t$this->assertFalse( has_filter( 'get_pagenum_link', [ $this->instance, 'filter_get_pagenum_link' ] ) );\n", " \t\t}\n", " \t\t$this->assertFalse( has_action( 'wp_head', 'amp_add_amphtml_link' ) );\n", "-\t\t$this->assertEquals( $using_path_suffix, $this->instance->is_using_path_suffix() );\n", " \t}\n", " \n", " \t/** @covers ::add_paired_request_hooks() */\n", "</pre><hr class=\"wide-line\"><li><strong>371  ```suggestion * @returns {?MessageAttachment} The value of the option, or null if not set and not required. ```</strong></li><pre>@@ -220,7 +220,7 @@ class CommandInteractionOptionResolver {\n", "    * Gets an attachment option.\n", "    * @param {string} name The name of the option.\n", "    * @param {boolean} [required=false] Whether to throw an error if the option is not found.\n", "-   * @returns {?(MessageAttachment)} The value of the option, or null if not set and not required.\n", "+   * @returns {?MessageAttachment} The value of the option, or null if not set and not required.\n", "    */\n", "   getAttachment(name, required = false) {\n", "     const option = this._getTypedOption(name, 'ATTACHMENT', ['attachment'], required);\n", "</pre><hr class=\"wide-line\"><li><strong>372  ```suggestion self.port = port or frappe.conf.db_port or '' ```</strong></li><pre>@@ -49,7 +49,7 @@ class Database(object):\n", " \tdef __init__(self, host=None, user=None, password=None, ac_name=None, use_default=0, port=None):\n", " \t\tself.setup_type_map()\n", " \t\tself.host = host or frappe.conf.db_host or 'localhost'\n", "-\t\tself.port = str(port or frappe.conf.db_port or '')\n", "+\t\tself.port = port or frappe.conf.db_port or ''\n", " \t\tself.user = user or frappe.conf.db_name\n", " \t\tself.db_name = frappe.conf.db_name\n", " \t\tself._conn = None\n", "</pre><hr class=\"wide-line\"><li><strong>373  I think to make it a bit faster it might be worth to use `.lean()` here</strong></li><pre>@@ -61,6 +61,7 @@ export async function listConversations (user) {\n", " \n", "   const users = await User.find({_id: {$in: conversationList}})\n", "     .select('_id profile.name auth.local.username')\n", "+    .lean()\n", "     .exec();\n", " \n", "   const conversations = users.map(u => ({\n", "</pre><hr class=\"wide-line\"><li><strong>374  This should be plural as well</strong></li><pre>@@ -130,7 +130,7 @@ public class <%= entityClass %>DTO implements Serializable {\n", "         ownerSide = relationships[idx].ownerSide;\n", "         if (relationshipType == 'many-to-many' && ownerSide == true) { _%>\n", " \n", "-    public Set<<%= otherEntityNameCapitalized %>DTO> get<%= relationshipNameCapitalized %>() {\n", "+    public Set<<%= otherEntityNameCapitalized %>DTO> get<%= relationshipNameCapitalizedPlural %>() {\n", "         return <%= relationshipFieldNamePlural %>;\n", "     }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>375  I think we still want this check so that disabled projects don't run old tasks wouldn't we?</strong></li><pre>@@ -68,6 +68,9 @@ func CollectRevisionsForProject(ctx context.Context, conf *evergreen.Settings, p\n", " }\n", " \n", " func ActivateBuildsForProject(project model.ProjectRef) error {\n", "+\tif !project.IsEnabled() {\n", "+\t\treturn errors.Errorf(\"project disabled: %s\", project.Id)\n", "+\t}\n", " \tif err := model.DoProjectActivation(project.Id); err != nil {\n", " \t\tgrip.Warning(message.WrapError(err, message.Fields{\n", " \t\t\t\"message\": \"problem activating recent commit for project\",\n", "</pre><hr class=\"wide-line\"><li><strong>376  You can check for correct type assertion here, so if will be the case in some strange circumstances you will avoid passing nil to line 46 in estimateTxSize method and also to return nil, true in line 49 tx, ok := item.(*WrappedTransaction) if !ok { return nil, false }</strong></li><pre>@@ -39,11 +39,14 @@ func (txMap *txByHashMap) removeTx(txHash string) (*WrappedTransaction, bool) {\n", " \t\treturn nil, false\n", " \t}\n", " \n", "-\ttx := item.(*WrappedTransaction)\n", "+\ttx, ok := item.(*WrappedTransaction)\n", "+\tif !ok {\n", "+\t\treturn nil, false\n", "+\t}\n", " \n", " \tif removed {\n", "-\ttxMap.counter.Decrement()\n", "-\ttxMap.numBytes.Subtract(int64(estimateTxSize(tx)))\n", "+\t\ttxMap.counter.Decrement()\n", "+\t\ttxMap.numBytes.Subtract(int64(estimateTxSize(tx)))\n", " \t}\n", " \n", " \treturn tx, true\n", "</pre><hr class=\"wide-line\"><li><strong>377  Drop this for now I think its not used</strong></li><pre>@@ -28,7 +28,7 @@ namespace Stratis.Bitcoin.Features.Consensus\n", "         /// Retrieves checkpoint for a block at given height.\n", "         /// </summary>\n", "         /// <param name=\"height\">Height of the block.</param>\n", "-        /// <returns>Checkpoint information or null if a checkpoint does not exist for given <paramref name=\"height\"/>.</returns>\n", "+        /// <returns>Checkpoint information or <c>null</c> if a checkpoint does not exist for given <paramref name=\"height\"/>.</returns>\n", "         CheckpointInfo GetCheckpoint(int height);\n", "     }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>378  do we need this line?</strong></li><pre>@@ -17,6 +17,7 @@ import com.google.common.io.Files;\n", " import org.apache.hadoop.fs.Path;\n", " import org.testng.annotations.Test;\n", " \n", "+import java.io.File;\n", " import java.util.UUID;\n", " \n", " import static com.facebook.presto.hive.HiveTestUtils.SESSION;\n", "</pre><hr class=\"wide-line\"><li><strong>379  Rather than throwing `ClassCastException`, I would have an explicit check and throw better exception if the context doesn't support transaction (maybe a `Precondition.checkState`).</strong></li><pre>@@ -59,7 +59,9 @@ public abstract class AbstractHttpHandlerDelegator<T extends HttpServiceHandler>\n", "   }\n", " \n", "   protected final TransactionContext getTransactionContext() {\n", "-    return ((DefaultHttpServiceContext) context.getServiceContext()).getTransactionContext();\n", "+    Preconditions.checkState(context.getServiceContext() instanceof BasicHttpServiceContext,\n", "+                             \"This instance of HttpServiceContext does not support transactions.\");\n", "+    return ((BasicHttpServiceContext) context.getServiceContext()).getTransactionContext();\n", "   }\n", " \n", "   protected final HttpServiceRequest wrapRequest(HttpRequest request) {\n", "</pre><hr class=\"wide-line\"><li><strong>380  Let's merge the two conditions and also, we can merge the particular with the condition at line 133.</strong></li><pre>@@ -130,15 +130,10 @@ public class RenameUtil {\n", "         NonTerminalNode nodeAtCursor = CommonUtil.findNode(cursorPosRange, document.get().syntaxTree());\n", " \n", "         // For clients that doesn't support prepare rename, we do this check here as well\n", "-        if (onImportDeclarationNode(context, nodeAtCursor)) {\n", "+        if (onImportDeclarationNode(context, nodeAtCursor) \n", "+                || (nodeAtCursor.kind() == SyntaxKind.SIMPLE_NAME_REFERENCE && isSelfClassSymbol(context))) {\n", "             return Collections.emptyMap();\n", "         }\n", "-        \n", "-        if (nodeAtCursor.kind() == SyntaxKind.SIMPLE_NAME_REFERENCE) {\n", "-            if (isSelfClassSymbol(context)) {\n", "-                return Collections.emptyMap();\n", "-            }\n", "-        }\n", " \n", "         if (QNameReferenceUtil.onModulePrefix(context, nodeAtCursor)) {\n", "             return handleQNameReferenceRename(context, document.get(), nodeAtCursor);\n", "</pre><hr class=\"wide-line\"><li><strong>381  I don't find this easier to read/understand, can you undo this bit?</strong></li><pre>@@ -40,7 +40,11 @@ const EngineInstance = EmberObject.extend(RegistryProxyMixin, ContainerProxyMixi\n", " \n", "     guidFor(this);\n", " \n", "-    let base = this.base || (this.base = this.application);\n", "+    let base = this.base;\n", "+\n", "+    if (!base) {\n", "+      this.base = base = this.application;\n", "+    }\n", " \n", "     // Create a per-instance registry that will use the application's registry\n", "     // as a fallback for resolving registrations.\n", "</pre><hr class=\"wide-line\"><li><strong>382  Should this be annotated with `@CheckReturnValue` ?</strong></li><pre>@@ -16,7 +16,7 @@ package io.reactivex.subjects;\n", " import java.util.concurrent.atomic.*;\n", " \n", " import io.reactivex.*;\n", "-import io.reactivex.annotations.Experimental;\n", "+import io.reactivex.annotations.*;\n", " import io.reactivex.disposables.Disposable;\n", " import io.reactivex.plugins.RxJavaPlugins;\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>383  This cleanup code can be removed if you do an atomic commit instead of StartCommit and FinishCommit. all you need to do is call `pc.NewPutFileClient()` and you'll get back a client that you can do `PutFile*`s operations on like normal and call `Close()` on it when you're done. If you crash midway through the operation everything will be rolled back.</strong></li><pre>@@ -25,7 +25,7 @@ const defaultSwitchInterval = 60000 // ms\n", " \n", " var logger log.Logger\n", " \n", "-func writeFiles(pc *client.APIClient, opts *options, buffer []amqp.Delivery, commits chan<- string) error {\n", "+func writeFiles(pc *client.APIClient, opts *options, buffer []amqp.Delivery) error {\n", " \n", " \tlog.Print(\"writing buffer...\")\n", " \tlog.Printf(\"buffer_size %d\", len(buffer))\n", "</pre><hr class=\"wide-line\"><li><strong>384  is it used anywhere?</strong></li><pre>@@ -38,7 +38,7 @@ get_assets_to_accept = partial(\n", "     get_assets, status=ACCEPTANCE_BACK_OFFICE_ACCEPT_STATUS\n", " )\n", " get_assets_to_accept_loan = partial(\n", "-    get_assets,status=ACCEPTANCE_BACK_OFFICE_ACCEPT_LOAN_STATUS\n", "+    get_assets, status=ACCEPTANCE_BACK_OFFICE_ACCEPT_LOAN_STATUS\n", " )\n", " \n", " \n", "</pre><hr class=\"wide-line\"><li><strong>385  Consider removing `requiresDataSchema()` because it is always true here? I know this is the general logic but in this specific provider this call is not necessary.</strong></li><pre>@@ -121,12 +121,12 @@ public class PubsubSchemaCapableIOProvider implements SchemaIOProvider {\n", "   }\n", " \n", "   @Override\n", "-  public Boolean requiresDataSchema() {\n", "+  public boolean requiresDataSchema() {\n", "     return true;\n", "   }\n", " \n", "   private void validateDataSchema(Schema schema) {\n", "-    if (requiresDataSchema() && schema == null) {\n", "+    if (schema == null) {\n", "       throw new InvalidSchemaException(\n", "           \"Unsupported schema specified for Pubsub source in CREATE TABLE.\"\n", "               + \"CREATE TABLE for Pubsub topic must not be null\");\n", "</pre><hr class=\"wide-line\"><li><strong>386  Will this bring breaking change? Should we print a warning and use `--gcs-server-port` with higher priority?</strong></li><pre>@@ -465,10 +465,10 @@ def start(node_ip_address, address, port, redis_password, redis_shard_ports,\n", "           no_monitor, tracing_startup_hook, ray_debugger_external):\n", "     \"\"\"Start Ray processes manually on the local machine.\"\"\"\n", "     if use_gcs_for_bootstrap() and gcs_server_port is not None:\n", "-        cli_logger.abort(\"`{}` is deprecated. Specify {} instead.\",\n", "-                         cf.bold(\"--gcs-server-port\"), cf.bold(\"--port\"))\n", "-        raise ValueError(\n", "-            \"`--gcs-server-port` is deprecated. Specify `--port` instead.\")\n", "+        cli_logger.error(\n", "+            \"`{}` is deprecated and ignored. Use {} to specify \"\n", "+            \"GCS server port on head node.\", cf.bold(\"--gcs-server-port\"),\n", "+            cf.bold(\"--port\"))\n", " \n", "     # Convert hostnames to numerical IP address.\n", "     if node_ip_address is not None:\n", "</pre><hr class=\"wide-line\"><li><strong>387  Can you use the full strings, here please? This causes problems in the future when searching whether a string is used in the code, thanks.</strong></li><pre>@@ -56,10 +56,8 @@ export default class AbstractMuteEveryonesVideoDialog<P: Props>\n", " \n", "         this.state = {\n", "             moderationEnabled: props.isVideoModerationEnabled,\n", "-            content: props.content || props.t(\n", "-                `dialog.muteEveryonesVideoDialog${props.isVideoModerationEnabled\n", "-                    ? 'ModerationOn'\n", "-                    : ''}`\n", "+            content: props.content || props.t(props.isVideoModerationEnabled\n", "+                ? 'dialog.muteEveryonesVideoDialogModerationOn' : 'dialog.muteEveryonesVideoDialog'\n", "             )\n", "         };\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>388  So, is it now true that all other apps, except Fish AND Weblab AND AiLab, call studioApp.init()?</strong></li><pre>@@ -54,7 +54,7 @@ Ailab.prototype.init = function(config) {\n", "   config.pinWorkspaceToBottom = true;\n", " \n", "   const onMount = () => {\n", "-    // NOTE: Other apps call studioApp.init(), except WebLab. Ailab is imitating WebLab.\n", "+    // NOTE: Most other apps call studioApp.init().  Like WebLab and Fish, we don't.\n", "     this.studioApp_.setConfigValues_(config);\n", " \n", "     // NOTE: if we called studioApp_.init(), the code here would be executed\n", "</pre><hr class=\"wide-line\"><li><strong>389  Should have a clear method to set index with time bucket suffix. Hard to read.</strong></li><pre>@@ -68,11 +68,7 @@ public class IndicatorEsDAO extends EsDAO implements IIndicatorDAO<IndexRequest,\n", "             }\n", "         }\n", "         builder.endObject();\n", "-        ElasticSearchClient client = getClient();\n", "-        if (client.getCreateByDayIndexes().contains(modelName)) {\n", "-            modelName = modelName + Const.ID_SPLIT + (indicator.getTimeBucket() + \"\").substring(0, 8);\n", "-        }\n", "-        return client.prepareInsert(modelName, indicator.id(), builder);\n", "+        return getClient().prepareInsert(modelName, indicator.id(), builder, indicator.getTimeBucket());\n", "     }\n", " \n", "     @Override public UpdateRequest prepareBatchUpdate(String modelName, Indicator indicator) throws IOException {\n", "</pre><hr class=\"wide-line\"><li><strong>390  is the coin denom also changing ?</strong></li><pre>@@ -956,7 +956,7 @@ func TestBondUnbondRedelegateSlashTwice(t *testing.T) {\n", " \tgot = handleMsgCreateValidator(ctx, msgCreateValidator, keeper)\n", " \trequire.True(t, got.IsOK(), \"expected no error on runMsgCreateValidator\")\n", " \n", "-\t// delegate 10 staking\n", "+\t// delegate 10 stake\n", " \tmsgDelegate := NewTestMsgDelegate(del, valA, 10)\n", " \tgot = handleMsgDelegate(ctx, msgDelegate, keeper)\n", " \trequire.True(t, got.IsOK(), \"expected no error on runMsgDelegate\")\n", "</pre><hr class=\"wide-line\"><li><strong>391  I know the conditions are part of the original code, but I don't understand how this adds dx.break for the nested loop case if your explanation is correct.</strong></li><pre>@@ -1207,8 +1207,8 @@ void CodeGenFunction::EmitBreakStmt(const BreakStmt &S) {\n", "       lastContinueBlock != BreakContinueStack.end()[-2].ContinueBlock.getBlock())) {\n", "     // We execute this if\n", "     // - we are in an unnested loop, or\n", "-    // - we are in a nested loop but the continue block of the enclosing loop is different from the current continue block.\n", "-    // TODO: Is the second conditional even possible in a language without goto or \"continue N\"?\n", "+    // - we are in a nested control construct but the continue block of the enclosing loop is different from the current continue block.\n", "+    // The second condition can happen for switch statements inside loops, which share the same continue block.\n", "     llvm::BasicBlock *lastBreakBlock = BreakContinueStack.back().BreakBlock.getBlock();\n", "     llvm::BranchInst *waveBr = CGM.getHLSLRuntime().EmitHLSLCondBreak(*this, CurFn, lastBreakBlock, lastContinueBlock);\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>392  How about renaming it to `redirect`. `retry` sounds like doing the same thing. ```suggestion static RetryDecision redirect(Backoff backoff, RequestHeaders headers) { ```</strong></li><pre>@@ -39,11 +39,7 @@ public final class RetryDecision {\n", "         if (backoff == Backoff.ofDefault()) {\n", "             return DEFAULT;\n", "         }\n", "-        return new RetryDecision(requireNon<PERSON>ull(backoff, \"backoff\"), null);\n", "-    }\n", "-\n", "-    static RetryDecision retry(Backoff backoff, RequestHeaders headers) {\n", "-        return new RetryDecision(requireNonNull(backoff, \"backoff\"), requireNonNull(headers, \"headers\"));\n", "+        return new RetryDecision(requireNonNull(backoff, \"backoff\"));\n", "     }\n", " \n", "     /**\n", "</pre><hr class=\"wide-line\"><li><strong>393  Can this be deleted instead?</strong></li><pre>@@ -31,9 +31,3 @@ type DSServer struct {\n", " \tName    string\n", " \tDSFQDNs []string // TOOD determine if neccessary, how to index\n", " }\n", "-\n", "-// type DSServer struct {\n", "-// \tDirectMatches                      map[string]tc.DeliveryServiceName\n", "-// \tDotStartSlashDotFooSlashDotDotStar map[string]tc.DeliveryServiceName\n", "-// \tRegexMatch                         map[*regexp.Regexp]tc.DeliveryServiceName\n", "-// }\n", "</pre><hr class=\"wide-line\"><li><strong>394  I dont think this is necessary here. We never explicitly set any authorities for reading Program and OrganisationUnit. But lets wait and see what others have to say.</strong></li><pre>@@ -130,13 +130,6 @@ public class ProgramController\n", "             throw new IllegalArgumentException( \"At least one program uid must be specified\" );\n", "         }\n", " \n", "-        if ( !aclService.canRead( currentUser, getEntityClass() )\n", "-            || !aclService.canRead( currentUser, OrganisationUnit.class ) )\n", "-        {\n", "-            throw new ReadAccessDeniedException(\n", "-                \"You don't have the proper permissions to read objects of this type.\" );\n", "-        }\n", "-\n", "         return programService.getProgramOrganisationUnitsAssociations( programUids );\n", " \n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>395  What do you think about extracting 2 local functions or methods and call them them here. Something similar with: ``` IEnumerable<(SyntaxNode node, string name)> GetCsharpNodes() => snippet.GetNodes<CSharpSyntax.InvocationExpressionSyntax>().Select(n => ((SyntaxNode)n, n.Expression.GetIdentifier()?.Identifier.ValueText)); IEnumerable<(SyntaxNode node, string name)> GetVbNodes() => snippet.GetNodes<VBSyntax.InvocationExpressionSyntax>().Select(n => ((SyntaxNode)n, n.Expression.GetIdentifier()?.Identifier.ValueText)); ``` and then: ``` var identifierPairs = snippet.IsCSharp() ? GetCsharpNodes() : GetVbNodes(); ``` I would also rename `invocation_identifierPairs` to `identifierPairs` or something similar.</strong></li><pre>@@ -299,13 +299,9 @@ End Namespace\n", "         {\n", "             var nameParts = typeAndMethodName.Split('.');\n", " \n", "-            var invocation_identifierPairs = snippet.IsCSharp()\n", "-                ? (IEnumerable<(SyntaxNode node, string name)>)snippet.GetNodes<CSharpSyntax.InvocationExpressionSyntax>()\n", "-                    .Select(n => ((SyntaxNode)n, n.Expression.GetIdentifier()?.Identifier.ValueText))\n", "-                : (IEnumerable<(SyntaxNode node, string name)>)snippet.GetNodes<VBSyntax.InvocationExpressionSyntax>()\n", "-                    .Select(n => ((SyntaxNode)n, VisualBasicSyntaxHelper.GetIdentifier(n.Expression)?.Identifier.ValueText));\n", "+            var identifierPairs = snippet.IsCSharp() ? GetCSharpNodes() : GetVbNodes();\n", " \n", "-            foreach (var (invocation, methodName) in invocation_identifierPairs)\n", "+            foreach (var (invocation, methodName) in identifierPairs)\n", "             {\n", "                 var symbol = snippet.GetSymbol<IMethodSymbol>(invocation);\n", "                 if (symbol.Name == nameParts[1] &&\n", "</pre><hr class=\"wide-line\"><li><strong>396  Why did you switch to `std::map::at`? It's a relatively expensive check that in this case serves no purpose.</strong></li><pre>@@ -81,7 +81,7 @@ std::string QtReflEventView::getTimeSlicingValues() const {\n", " */\n", " std::string QtReflEventView::getTimeSlicingType() const {\n", " \n", "-  return m_sliceTypeStrMap.at(m_sliceType);\n", "+  return m_sliceTypeMap.at(m_sliceType);\n", " }\n", " \n", " /** Enable slicing option entries for checked button and disable all others.\n", "</pre><hr class=\"wide-line\"><li><strong>397  What's with all the nulls?</strong></li><pre>@@ -21,7 +21,6 @@ package ai.grakn.graql.internal.query;\n", " \n", " import ai.grakn.GraknTx;\n", " import ai.grakn.graql.Printer;\n", "-import ai.grakn.graql.Query;\n", " import ai.grakn.graql.UndefineQuery;\n", " import ai.grakn.graql.admin.VarPatternAdmin;\n", " import com.google.auto.value.AutoValue;\n", "</pre><hr class=\"wide-line\"><li><strong>398  Maybe only do this conditionally if `ABSPATH . 'wp-content'` is actually different from `WP_CONTENT_DIR`?</strong></li><pre>@@ -1138,7 +1138,9 @@ class AMP_Style_Sanitizer_Test extends WP_UnitTestCase {\n", " \tpublic function get_stylesheet_urls() {\n", " \n", " \t\t// Make sure core-bundled themes are registered.\n", "-\t\tregister_theme_directory( ABSPATH . 'wp-content/themes' );\n", "+\t\tif ( WP_CONTENT_DIR !== ABSPATH . 'wp-content/themes' ) {\n", "+\t\t\tregister_theme_directory( ABSPATH . 'wp-content/themes' );\n", "+\t\t}\n", " \n", " \t\t$theme = new WP_Theme( 'twentyseventeen', ABSPATH . 'wp-content/themes' );\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>399  Maybe use `callbacks=[m]` here?</strong></li><pre>@@ -128,7 +128,7 @@ class LruCacheCallbacksTestCase(unittest.TestCase):\n", "         m = Mock()\n", "         cache = LruCache(1)\n", " \n", "-        cache.set(\"key\", \"value\", [m])\n", "+        cache.set(\"key\", \"value\", callbacks=[m])\n", "         self.assertFalse(m.called)\n", " \n", "         cache.set(\"key\", \"value\")\n", "</pre><hr class=\"wide-line\"><li><strong>400  It should be case insensitive for just a-z, based on that RFC. @tarekgh is there a built in way to be ASCII case insensitive, sensitive otherwise? If not you can trivially do it (in almost one line) of course.</strong></li><pre>@@ -106,6 +106,8 @@ namespace System.Net.NameResolution.Tests\n", "         public void DnsObsoleteGetHostByName_EmptyString_ReturnsHostName()\n", "         {\n", "             IPHostEntry entry = Dns.GetHostByName(\"\");\n", "+\n", "+            // DNS labels should be compared as case insensitive for ASCII characters. See RFC 4343.\n", "             Assert.Contains(Dns.GetHostName(), entry.Host<PERSON>ame, StringComparison.OrdinalIgnoreCase);\n", "         }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>401  I wonder what happens if a column of the schema is used as a partitioning key. I assume you will see that column twice here? Perhaps you should filter such duplicates?</strong></li><pre>@@ -492,8 +492,10 @@ public abstract class BaseHiveExploreService extends AbstractIdleService impleme\n", "       }\n", " \n", "       ImmutableList.Builder<TableInfo.ColumnInfo> schemaBuilder = ImmutableList.builder();\n", "+      Set<String> fieldNames = Sets.newHashSet();\n", "       for (FieldSchema column : tableFields) {\n", "         schemaBuilder.add(new TableInfo.ColumnInfo(column.getName(), column.getType(), column.getComment()));\n", "+        fieldNames.add(column.getName());\n", "       }\n", " \n", "       ImmutableList.Builder<TableInfo.ColumnInfo> partitionKeysBuilder = ImmutableList.builder();\n", "</pre><hr class=\"wide-line\"><li><strong>402  I think this is fine for now however, we should add a backlog task to process this on a low priority timer instead of in the critical path of the writing process. I don't think it will do work often enough to warrant a change prior to the initial release.</strong></li><pre>@@ -31,7 +31,7 @@ namespace eosio::trace_api {\n", "    void store_provider::append_lib(uint32_t lib) {\n", "       fc::cfile index;\n", "       const uint32_t slice_number = _slice_directory.slice_number(lib);\n", "-      _slice_directory.find_or_create_index_slice(slice_number, true, index);\n", "+      _slice_directory.find_or_create_index_slice(slice_number, open_state::write, index);\n", "       auto le = metadata_log_entry { lib_entry_v0 { .lib = lib }};\n", "       append_store(le, index);\n", "       _slice_directory.cleanup_old_slices(lib);\n", "</pre><hr class=\"wide-line\"><li><strong>403  Why set this to ture?</strong></li><pre>@@ -1074,7 +1074,7 @@ func (p *Analyze) convert2PhysicalPlan(prop *requiredProperty) (*physicalPlanInf\n", " \t\t\tTable:               tblInfo,\n", " \t\t\tColumns:             columns,\n", " \t\t\tTableAsName:         &p.Table.Name,\n", "-\t\t\tOutOfOrder:          true,\n", "+\t\t\tOutOfOrder:          false,\n", " \t\t\tDBName:              &p.Table.DBInfo.Name,\n", " \t\t\tphysicalTableSource: physicalTableSource{client: p.ctx.GetClient()},\n", " \t\t\tDoubleRead:          false,\n", "</pre><hr class=\"wide-line\"><li><strong>404  What's the reason that this is marked `thread_local`? Will multiple threads try to record debug info at the same time?</strong></li><pre>@@ -12,7 +12,6 @@ std::atomic<DebugHandleType> BackendDebugHandleManager::unique_debug_handle_{0};\n", " \n", " int64_t BackendDebugHandleManager::getNextDebugHandleForInlinedCallStackPtr(\n", "     const Node* node) {\n", "-  const SourceRange& range = node->sourceRange();\n", "   InlinedCallStackPtr cs_ptr;\n", "   if (node->callstack().has_value()) {\n", "     cs_ptr = node->callstack().value();\n", "</pre><hr class=\"wide-line\"><li><strong>405  Why are you making this simplification? This will limit the PNG usage.</strong></li><pre>@@ -719,7 +719,7 @@ void GraphicsWidget::drawWidget() {\n", " \t\tconst int x = _x + (_w - _gfx.w) / 2;\n", " \t\tconst int y = _y + (_h - _gfx.h) / 2;\n", " \n", "-\t\tg_gui.theme()->drawSurface(Common::Rect(x, y, x + _gfx.w, y + _gfx.h), _gfx, _transparency);\n", "+\t\tg_gui.theme()->drawSurfaceClip(Common::Rect(x, y, x + _gfx.w,  y + _gfx.h), getBossClipRect(), _gfx, _state, _alpha, _transparency);\n", " \t}\n", " }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>406  this is usually a sign that some abstraction is being broken and that code is in the wrong place. If somebody gets the twill controller here and messes with it, it will cause this class to malfunction.</strong></li><pre>@@ -119,7 +119,7 @@ public abstract class AbstractTwillProgramController extends AbstractProgramCont\n", "     Futures.getUnchecked(twillController.terminate());\n", "   }\n", " \n", "-  public final TwillController getTwillController() {\n", "+  protected final TwillController getTwillController() {\n", "     return twillController;\n", "   }\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>407  Also add a method from read from `InputStream`</strong></li><pre>@@ -39,4 +39,12 @@ public interface SchemaReader<T> {\n", "      * @return the serialized object\n", "      */\n", "     T read(byte[] bytes, int offset, int length);\n", "+\n", "+    /**\n", "+     * serialize bytes convert pojo\n", "+     *\n", "+     * @param inputStream the stream of message\n", "+     * @return the serialized object\n", "+     */\n", "+    T read(InputStream inputStream);\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>408  I think extend should call attach_feature instead of ignoring the feature.</strong></li><pre>@@ -434,6 +434,7 @@ class FunctionGraph(utils.object2):\n", "     def extend(self, feature):\n", "         warnings.warn(\"FunctionGraph.extend is deprecatd. It has been \"\n", "                 \"renamed to FunctionGraph.attach_feature\")\n", "+        return self.attach_feature(feature)\n", " \n", "     def attach_feature(self, feature):\n", "         \"\"\"\n", "</pre><hr class=\"wide-line\"><li><strong>409  Could you make the indentation here more obvious? Also, `const`! Like auto const iter = std::find_if( targets.begin(), targets.end(), [argv, i] (const std::string &targ) { return targ == argv[i]; });</strong></li><pre>@@ -489,10 +489,10 @@ const char *core_options::find_within_command_line(int argc, char **argv, const\n", " \t// find each of the targets in the argv array\n", " \tfor (int i = 1; i < argc - 1; i++)\n", " \t{\n", "-\t\tauto iter = std::find_if(targets.begin(), targets.end(), [argv, i](const std::string &targ)\n", "-\t\t{\n", "-\t\t\treturn targ == argv[i];\n", "-\t\t});\n", "+\t\tauto const iter = std::find_if(\n", "+\t\t\ttargets.begin(),\n", "+\t\t\ttargets.end(),\n", "+\t\t\t[argv, i](const std::string &targ) { return targ == argv[i]; });\n", " \t\tif (iter != targets.end())\n", " \t\t\treturn argv[i + 1];\n", " \t}\n", "</pre><hr class=\"wide-line\"><li><strong>410  should this be public?</strong></li><pre>@@ -10,7 +10,7 @@\n", "     <h1>Error 503 Backend is unhealthy</h1>\n", "     <p>Backend is unhealthy</p>\n", "     <h3>Guru Mediation:</h3>\n", "-    <p>Details: cache-sea4448-SEA ********** **********</p>\n", "+    <p>Details: cache-sea4468-SEA ********** **********</p>\n", "     <hr>\n", "     <p>Varnish cache server</p>\n", "   </body>\n", "</pre><hr class=\"wide-line\"><li><strong>411  BUG: It should be `!=`</strong></li><pre>@@ -138,7 +138,7 @@ namespace Microsoft.CodeAnalysis.Shared.Utilities\n", " \n", "         public static string GenerateUniqueName(string baseName, string extension, Func<string, bool> canUse)\n", "         {\n", "-            if (!string.IsNullOrEmpty(extension) && extension[0] == '.')\n", "+            if (!string.IsNullOrEmpty(extension) && extension[0] != '.')\n", "             {\n", "                 extension = \".\" + extension;\n", "             }\n", "</pre><hr class=\"wide-line\"><li><strong>412  Consider using `<see cref=\"xyz\">` when you talk about WalletTip and WalletManager. Also below and also for BlockStoreCache (also not sure why you have lowercase everywhere) and WalletSyncManager as well.</strong></li><pre>@@ -90,7 +90,8 @@ namespace Stratis.Bitcoin.Features.Wallet.Tests\n", "         }\n", " \n", "         /// <summary>\n", "-        /// When processing a new block that has a previous hash that is the same as the wallettip pass it directly to the walletmanager and set it as the new tip.\n", "+        /// When processing a new <see cref=\"Block\"/> that has a previous hash that is the same as the <see cref=\"WalletSyncManager.WalletTip\"/> pass it directly to the <see cref=\"WalletManager\"/> \n", "+        /// and set it as the new WalletTip.\n", "         /// </summary>\n", "         [Fact]\n", "         public void ProcessBlock_NewBlock_PreviousHashSameAsWalletTip_PassesBlockToManagerWithoutReorg()\n", "</pre><hr class=\"wide-line\"><li><strong>413  Creates new instance of mutation builder by wrapping existing existing set of row mutations. The builder will be owned by this RowMutation and should not be used by the caller after this call. This functionality is intended for advanced usage.</strong></li><pre>@@ -54,7 +54,9 @@ public final class RowMutation implements MutationApi<RowMutation>, Serializable\n", "   }\n", " \n", "   /**\n", "-   * Creates new instance of mutation builder by wrapping existing mutation builder.\n", "+   * Creates new instance of mutation builder by wrapping existing existing set of row mutations.\n", "+   * The builder will be owned by this RowMutation and should not be used by the caller after this call.\n", "+   * This functionality is intended for advanced usage.\n", "    *\n", "    * <p>Sample code:\n", "    *\n", "</pre><hr class=\"wide-line\"><li><strong>414  ```suggestion if bracket in bracket_pairs: ``` This way if we add new bracket types (<, >) the program auto adapts.</strong></li><pre>@@ -13,7 +13,7 @@ def balanced_parentheses(parentheses: str) -> bool:\n", "     stack = Stack()\n", "     bracket_pairs = {\"(\": \")\", \"[\": \"]\", \"{\": \"}\"}\n", "     for bracket in parentheses:\n", "-        if bracket in (\"(\", \"[\", \"{\"):\n", "+        if bracket in bracket_pairs:\n", "             stack.push(bracket)\n", "         elif bracket in (\")\", \"]\", \"}\"):\n", "             if stack.is_empty() or bracket_pairs[stack.pop()] != bracket:\n", "</pre><hr class=\"wide-line\"><li><strong>415  A huge complication. Better to find other way to fix.</strong></li><pre>@@ -34,10 +34,8 @@ ConvertingBlockInputStream::ConvertingBlockInputStream(\n", "     const Context & context_,\n", "     const BlockInputStreamPtr & input,\n", "     const Block & result_header,\n", "-    MatchColumnsMode mode,\n", "-    bool allow_different_constant_values_)\n", "+    MatchColumnsMode mode)\n", "     : context(context_), header(result_header), conversion(header.columns())\n", "-    , allow_different_constant_values(allow_different_constant_values_)\n", " {\n", "     children.emplace_back(input);\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>416  S not TS</strong></li><pre>@@ -21,10 +21,10 @@ namespace Orchard.Flows.Settings\n", "             IStringLocalizer<BagPartSettingsDisplayDriver> localizer)\n", "         {\n", "             _contentDefinitionManager = contentDefinitionManager;\n", "-            TS = localizer;\n", "+            S = localizer;\n", "         }\n", " \n", "-        public IStringLocalizer TS { get; set; }\n", "+        public IStringLocalizer S { get; set; }\n", " \n", "         public override IDisplayResult Edit(ContentTypePartDefinition contentTypePartDefinition, IUpdateModel updater)\n", "         {\n", "</pre><hr class=\"wide-line\"><li><strong>417  Why do you removed the if \"auto\" check? I case one uses hdf5 the primal solution is written twice to the adjoint model part. Or I am wrong?</strong></li><pre>@@ -374,10 +374,11 @@ class AdjointResponseFunction(ResponseFunctionBase):\n", "             adjoint_node.Z = primal_node.Z\n", " \n", "         # Put primal solution on adjoint model - for \"auto\" setting, else it has to be done by the user e.g. using hdf5 process\n", "-        Logger.PrintInfo(\"> Transfer primal state to adjoint model part.\")\n", "-        variable_utils = KratosMultiphysics.VariableUtils()\n", "-        for variable in self.primal_state_variables:\n", "-            variable_utils.CopyModelPartNodalVar(variable, self.primal_model_part, self.adjoint_model_part, 0)\n", "+        if self.response_settings[\"adjoint_settings\"].GetString() == \"auto\":\n", "+            Logger.PrintInfo(\"> Transfer primal state to adjoint model part.\")\n", "+            variable_utils = KratosMultiphysics.VariableUtils()\n", "+            for variable in self.primal_state_variables:\n", "+                variable_utils.CopyModelPartNodalVar(variable, self.primal_model_part, self.adjoint_model_part, 0)\n", " \n", " \n", "     def _GetAdjointParameters(self):\n", "</pre><hr class=\"wide-line\"><li><strong>418  don't need modify this line and Line 27</strong></li><pre>@@ -24,11 +24,11 @@ import (\n", " \t\"sync/atomic\"\n", " \t\"time\"\n", " \n", "-\t\"github.com/opentracing/opentracing-go\"\n", "+\topentracing \"github.com/opentracing/opentracing-go\"\n", " \t\"github.com/pingcap/errors\"\n", " \t\"github.com/pingcap/parser/mysql\"\n", " \t\"github.com/pingcap/parser/terror\"\n", "-\t\"github.com/pingcap/pd/client\"\n", "+\tpd \"github.com/pingcap/pd/client\"\n", " \tpumpcli \"github.com/pingcap/tidb-tools/tidb-binlog/pump_client\"\n", " \t\"github.com/pingcap/tidb/config\"\n", " \t\"github.com/pingcap/tidb/ddl\"\n", "</pre><hr class=\"wide-line\"><li><strong>419  In case you forget... make sure source is closed :P</strong></li><pre>@@ -99,11 +99,6 @@ public class DatabaseStorageManager\n", "     private FilesAndRowCount stagingImport(Operator source, String databaseName, String tableName, long shardId)\n", "             throws IOException\n", "     {\n", "-\n", "-        // todo assure source is closed\n", "-        // todo assure source is closed\n", "-        // todo assure source is closed\n", "-        // todo assure source is closed\n", "         ImmutableList.Builder<File> outputFilesBuilder = ImmutableList.builder();\n", "         ImmutableList.Builder<BlocksFileWriter> writersBuilder = ImmutableList.builder();\n", "         for (int channel = 0; channel < source.getChannelCount(); channel++) {\n", "</pre><hr class=\"wide-line\"><li><strong>420  The idea is to not mutate the map after assignment, so the previous code was fine.</strong></li><pre>@@ -50,9 +50,10 @@ public enum ApiKeys {\n", "         int maxKey = -1;\n", "         for (ApiKeys key : ApiKeys.values())\n", "             maxKey = Math.max(maxKey, key.id);\n", "-        ID_TO_TYPE = new ApiKeys[maxKey + 1];\n", "+        ApiKeys[] idToType = new ApiKeys[maxKey + 1];\n", "         for (ApiKeys key : ApiKeys.values())\n", "-            ID_TO_TYPE[key.id] = key;\n", "+            idToType[key.id] = key;\n", "+        ID_TO_TYPE = idToType;\n", "         MAX_API_KEY = maxKey;\n", "     }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>421  >if (sourceType?.IsTupleType == true [](start = 16, length = 35) Suggestion: `if (sourceTuple.Type is NamedTypeSymbol { IsTupleType: true } sourceType)` #Resolved</strong></li><pre>@@ -437,7 +437,7 @@ namespace Microsoft.CodeAnalysis.CSharp\n", " \n", "                 var sourceType = sourceTuple.Type as NamedTypeSymbol;\n", " \n", "-                if (sourceType?.IsTupleType == true)\n", "+                if (sourceTuple.Type is { IsTupleType: true })\n", "                 {\n", "                     targetType = targetType.WithTupleDataFrom(sourceType);\n", "                 }\n", "</pre><hr class=\"wide-line\"><li><strong>422  How could `dispose<PERSON>eth<PERSON>` be non-null here?</strong></li><pre>@@ -81,7 +81,7 @@ namespace Microsoft.CodeAnalysis.CSharp\n", " \n", "                 TypeSymbol expressionType = expressionOpt.Type;\n", "                 \n", "-                if (!iDisposableConversion.IsImplicit && disposeMethod is null)\n", "+                if (!iDisposableConversion.IsImplicit)\n", "                 {\n", "                     if (!(expressionType is null))\n", "                     {\n", "</pre><hr class=\"wide-line\"><li><strong>423  Change is unnecessary.</strong></li><pre>@@ -604,8 +604,9 @@ namespace Microsoft.CodeAnalysis.CSharp\n", " \n", "         private BoundStatement BindDeclarationStatementParts(LocalDeclarationStatementSyntax node, DiagnosticBag diagnostics)\n", "         {\n", "-            bool isConst = node.IsConst;\n", "             var typeSyntax = node.Declaration.Type.SkipRef(out _);\n", "+            bool isConst = node.IsConst;\n", "+\n", "             bool isVar;\n", "             AliasSymbol alias;\n", "             TypeSymbol declType = BindVariableType(node.Declaration, diagnostics, typeSyntax, ref isConst, isVar: out isVar, alias: out alias);\n", "</pre><hr class=\"wide-line\"><li><strong>424  ```suggestion for (const auto &specSection : specSections) { ```</strong></li><pre>@@ -6212,7 +6212,7 @@ void Graph::loadFromProject(const std::string &lines, ApplicationWindow *app,\n", "     }\n", " \n", "     std::vector<std::string> specSections = tsv.sections(\"spectrogram\");\n", "-    for (auto &specSection : specSections) {\n", "+    for (const auto &specSection : specSections) {\n", "       MantidQt::API::TSVSerialiser specTSV(specSection);\n", "       Spectrogram *s = nullptr;\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>425  The first line is not neccessary. You can just do `assembler_suffixes: <PERSON><PERSON>[str, ...] = ('s', 'S')`</strong></li><pre>@@ -74,8 +74,7 @@ c_suffixes = lang_suffixes['c'] + ('h',)  # type: <PERSON><PERSON>[str, ...]\n", " # C ABI; these can generally be used interchangeably\n", " clib_langs = ('objcpp', 'cpp', 'objc', 'c', 'fortran',)  # type: <PERSON><PERSON>[str, ...]\n", " # List of assembler suffixes that can be linked with C code directly by the linker\n", "-assembler_suffixes = tuple() # type: <PERSON><PERSON>[str, ...]\n", "-assembler_suffixes += ('s', 'S')\n", "+assembler_suffixes: <PERSON><PERSON>[str, ...] = ('s', 'S')\n", " # List of languages that can be linked with C code directly by the linker\n", " # used in build.py:process_compilers() and build.py:get_dynamic_linker()\n", " clink_langs = ('d', 'cuda') + clib_langs  # type: <PERSON><PERSON>[str, ...]\n", "</pre><hr class=\"wide-line\"><li><strong>426  Was this line added intentionally? It seems incorrect, `mod_opt` is advanced options here.</strong></li><pre>@@ -3551,7 +3551,6 @@ class Core\n", " \n", "   def show_advanced_options(mod) # :nodoc:\n", "     mod_opt = Serializer::ReadableText.dump_advanced_options(mod, '   ')\n", "-    print(\"\\nModule options (#{mod.fullname}):\\n\\n#{mod_opt}\\n\") if (mod_opt and mod_opt.length > 0)\n", "     print(\"\\nModule advanced options (#{mod.fullname}):\\n\\n#{mod_opt}\\n\") if (mod_opt and mod_opt.length > 0)\n", " \n", "     # If it's an exploit and a payload is defined, create it and\n", "</pre><hr class=\"wide-line\"><li><strong>427  Is this where we could do a bit of additional validation on the charm file, e.g., ensure `ReadCharmArchive()` returns success?</strong></li><pre>@@ -362,6 +362,13 @@ func (c *bootstrapCommand) Init(args []string) (err error) {\n", " \t\tif err != nil {\n", " \t\t\treturn errors.<PERSON><PERSON><PERSON><PERSON>(err, \"problem with --controller-charm\")\n", " \t\t}\n", "+\t\tch, err := charm.<PERSON><PERSON>harm(c.ControllerCharmPath)\n", "+\t\tif err != nil {\n", "+\t\t\treturn errors.<PERSON><PERSON><PERSON>(\"--controller-charm %q is not a valid charm\", c.<PERSON><PERSON><PERSON><PERSON>)\n", "+\t\t}\n", "+\t\tif ch.<PERSON><PERSON>().Name != bootstrap.ControllerCharmName {\n", "+\t\t\treturn errors.<PERSON>rrorf(\"--controller-charm %q is not a %q charm\", c<PERSON><PERSON>, bootstrap.ControllerCharmName)\n", "+\t\t}\n", " \t}\n", " \n", " \tif c.showClouds && c.showRegionsForCloud != \"\" {\n", "</pre><hr class=\"wide-line\"><li><strong>428  Can you insert your test suite after `HealthCheckSuite` (alphabetical order)?</strong></li><pre>@@ -60,7 +60,6 @@ func init() {\n", " \t\tcheck.Suite(&TimeoutSuite{})\n", " \t\tcheck.Suite(&TracingSuite{})\n", " \t\tcheck.Suite(&WebsocketSuite{})\n", "-\t\tcheck.Suite(&HostResolverSuite{})\n", " \t}\n", " \tif *host {\n", " \t\t// tests launched from the host\n", "</pre><hr class=\"wide-line\"><li><strong>429  You should let these \"expected\" exceptions bubble up. They'll be caught by the agent and will display in the \"datadog-agent info\" command (I'll add this to the docs today). Same with the couldn't connect exceptions, etc.</strong></li><pre>@@ -39,8 +39,7 @@ class KyotoTycoonCheck(AgentCheck):\n", "     def check(self, instance):\n", "         url = instance.get('report_url')\n", "         if not url:\n", "-            self.log.exception('Invalid Kyoto Tycoon report url %r', url)\n", "-            return\n", "+            raise Exception('Invalid Kyoto Tycoon report url %r' % url)\n", " \n", "         tags = instance.get('tags', {})\n", "         name = instance.get('name')\n", "</pre><hr class=\"wide-line\"><li><strong>430  The table ID here will be only used by the cached HMS client, right? If it is used by HiveMetastoreClient, will we throw an error if the ID from getTable different from the one is passed-in?</strong></li><pre>@@ -2227,15 +2227,7 @@ public class HiveMetaStoreClient implements IMetaStoreClient, AutoCloseable {\n", "   @Override\n", "   public GetPartitionsByNamesResult getPartitionsByNames(GetPartitionsByNamesRequest req)\n", "           throws NoSuchObjectException, MetaException, TException {\n", "-    checkDbAndTableFilters(req.getCatName(), req.getDb_name(), req.getTbl_name());\n", "-    req.setDb_name(prependCatalogToDbName(req.getCatName(), req.getDb_name(), conf));\n", "-    if (req.getValidWriteIdList() == null) {\n", "-      req.setValidWriteIdList(getValidWriteIdList(prependCatalogToDbName(req.getCatName(), req.getDb_name(),\n", "-              conf), req.getTbl_name()));\n", "-    }\n", "-    if (req.getId() <= 0) {\n", "-      req.setId(getTable(prependCatalogToDbName(req.getCatName(), req.getDb_name(), conf), req.getTbl_name()).getId());\n", "-    }\n", "+    checkDbAndTableFilters(getDefaultCatalog(conf), req.getDb_name(), req.getTbl_name());\n", "     if (processorCapabilities != null)\n", "       req.setProcessorCapabilities(new ArrayList<>(Arrays.asList(processorCapabilities)));\n", "     if (processorIdentifier != null)\n", "</pre><hr class=\"wide-line\"><li><strong>431  This could probably be changed to more specific `\\Cake\\Database\\Log\\LoggingStatement`</strong></li><pre>@@ -556,7 +556,7 @@ class Connection {\n", "  * for the passed original statement instance.\n", "  *\n", "  * @param \\Cake\\Database\\StatementInterface $statement the instance to be decorated\n", "- * @return \\Cake\\Database\\StatementInterface\n", "+ * @return \\Cake\\Database\\Log\\LoggingStatement\n", "  */\n", " \tprotected function _newLogger(StatementInterface $statement) {\n", " \t\t$log = new LoggingStatement($statement, $this->driver());\n", "</pre><hr class=\"wide-line\"><li><strong>432  Minor condensation: I would use `data = list(reversed(data))`</strong></li><pre>@@ -1038,5 +1038,4 @@ for (name, data) in (('magma', _magma_data),\n", "     cmaps[name] = ListedColormap(data, name=name)\n", "     # generate reversed colormap\n", "     name = name + '_r'\n", "-    data = [rgb for rgb in reversed(data)]\n", "-    cmaps[name] = ListedColormap(data, name=name)\n", "+    cmaps[name] = ListedColormap(list(reversed(data)), name=name)\n", "</pre><hr class=\"wide-line\"><li><strong>433  Authentication should be in a separate object (approach taken by the newer connectors - e.g. ByBit). Also, the auth responsibility should be pushed in the `WebAssistants` by inheriting from `AuthBase`. An example of that is CoinbasePro</strong></li><pre>@@ -1056,9 +1056,7 @@ class BinancePerpetualDerivative(ExchangeBase, PerpetualTrading):\n", "                     params[\"recvWindow\"] = f\"{20000}\"\n", "                 query = urlencode(sorted(params.items()))\n", "                 if is_signed:\n", "-                    secret = bytes(self._api_secret.encode(\"utf-8\"))\n", "-                    signature = hmac.new(secret, query.encode(\"utf-8\"), hashlib.sha256).hexdigest()\n", "-                    query += f\"&signature={signature}\"\n", "+                    query = self._auth.extend_query_with_authentication_info(query=query)\n", " \n", "                 url = utils.rest_url(path, self._domain, api_version)\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>434  Nitpick: I usually see this type of variable assigned to the variable `T`</strong></li><pre>@@ -24,8 +24,6 @@ from .utils import OperatorMethodMixin, apply, ensure_dict, funcname, methodcall\n", " \n", " __all__ = [\"Delayed\", \"delayed\"]\n", " \n", "-D = TypeVar(\"D\")\n", "-\n", " \n", " def unzip(ls, nout):\n", "     \"\"\"Unzip a list of lists into ``nout`` outputs.\"\"\"\n", "</pre><hr class=\"wide-line\"><li><strong>435  `equal_to` allows you to specify an optional `equals_fn` which you can set to something like `sess.run(tf.reduce_all(tf.equal(expected_tensor, actual_tensor)))`.</strong></li><pre>@@ -33,10 +33,9 @@ class BeamPipelineTest(tf.test.TestCase):\n", "     with tempfile.TemporaryDirectory() as temp_dir:\n", "       create_complex_graph.save_examples_as_graphdefs(temp_dir)\n", " \n", "-      # Root graph is the graph to compute. Since it's unique, it's both the\n", "-      # graph name extended and the graph name.\n", "+      # \"main\" is both a graph name and a remote op name.\n", "       root_graph = 'main'\n", "-      parent_graph_to_remote_graph_input_name_mapping = {\n", "+      graph_to_remote_op_input_name_mapping = {\n", "           'main': {'remote_op_a': {'ids_a': 'ids1'},\n", "                    'remote_op_b': {'ids_b1': 'ids1',\n", "                                    'ids_b2': 'ids2'},\n", "</pre><hr class=\"wide-line\"><li><strong>436  Why use local variable once?</strong></li><pre>@@ -62,8 +62,7 @@ class HelperCollection {\n", "  */\n", " \tpublic function __construct(View $view) {\n", " \t\t$this->_View = $view;\n", "-\t\t$eventManager = $view->getEventManager();\n", "-\t\t$this->_eventManager = $eventManager;\n", "+\t\t$this->_eventManager = $view->getEventManager();\n", " \t}\n", " \n", " /**\n", "</pre><hr class=\"wide-line\"><li><strong>437  nit: it was just assigned to this on the line above. You can change the line above to just be `char value;`</strong></li><pre>@@ -665,7 +665,6 @@ namespace System.Tests\n", "                 string s = \"Turkish I \\u0131s TROUBL\\u0130NG!\";\n", "                 char value = '\\u0130';\n", " \n", "-                value = '\\u0130';\n", "                 Assert.Equal(19, s.IndexOf(value));\n", "                 Assert.Equal(19, s.IndexOf(value, StringComparison.CurrentCulture));\n", "                 Assert.Equal(19, s.IndexOf(value, StringComparison.CurrentCultureIgnoreCase));\n", "</pre><hr class=\"wide-line\"><li><strong>438  Can you please clarify why we need separate kernel code for channels last bfloat16 but can go with same kernel in contiguous</strong></li><pre>@@ -182,7 +182,7 @@ void cpu_adaptive_avg_pool_channels_last<BFloat16>(\n", "     std::unique_ptr<float []> sum_arr(new float[channels]);\n", "     float* sum = sum_arr.get();\n", " \n", "-    for (int64_t i = begin; i < end; i++) {\n", "+    for (const auto i : c10::irange(begin, end)) {\n", "       int64_t ih0 = start_index(oh, output_height, input_height);\n", "       int64_t ih1 = end_index(oh, output_height, input_height);\n", "       int64_t kh = ih1 - ih0;\n", "</pre><hr class=\"wide-line\"><li><strong>439  I've had a quick general look at the MIB for this and it looks like this could be moved to yaml. Have you tried that?</strong></li><pre>@@ -44,7 +44,7 @@ foreach (explode(\"\\n\", $oids) as $data) {\n", "         $high_warn = snmp_get($device, 'analogAlarmHI.13' . $num_oid, '-Ovq', 'NSCRTV-HFCEMS-PROPERTY-MIB') / 1000;\n", "         $high_limit = snmp_get($device, 'analogAlarmHIHI.13' . $num_oid, '-Ovq', 'NSCRTV-HFCEMS-PROPERTY-MIB') / 1000;\n", "         $sensor_index = str_replace(' ', '', $descr);\n", "-        discover_sensor($valid['sensor'], 'current', $device, $num_oid, $sensor_index, 'gw-eydfa', $descr, $divisor, '1', $low_limit, $low_warn, $high_warn, $high_limit, $value);\n", "+        discover_sensor($valid['sensor'], 'current', $device, $num_oid, $sensor_index, 'gw-eydfa', $descr, $divisor, 1, $low_limit, $low_warn, $high_warn, $high_limit, $value);\n", "     }\n", "     if ($split_oid[0] == \"oaPumpTEC\" && $index = 1) { // Current - A\n", "         $divisor = 100;\n", "</pre><hr class=\"wide-line\"><li><strong>440  I think it would be nice to make the distinction between providing `BlobId` and `BlobInfo` suggesting that the latter is used to override the source information (such as content-type, content-language and metadata) and in that case content-type is a required field.</strong></li><pre>@@ -551,7 +551,7 @@ public interface Storage extends Service<StorageOptions> {\n", "       }\n", " \n", "       /**\n", "-       * Sets the copy target.\n", "+       * Sets the copy target. Target blob information is copied from source.\n", "        *\n", "        * @return the builder.\n", "        */\n", "</pre><hr class=\"wide-line\"><li><strong>441  Can you explicitly call out and mention that `--` is required before passing any flags into kubectl?</strong></li><pre>@@ -38,7 +38,8 @@ import (\n", " var kubectlCmd = &cobra.Command{\n", " \tUse:   \"kubectl\",\n", " \tShort: \"Run kubectl\",\n", "-\tLong: `Run the kubernetes client, download it if necessary.\n", "+\tLong: `Run the kubernetes client, download it if necessary. Remember -- after kubectl!\n", "+\n", " Examples:\n", " minikube kubectl -- --help\n", " minikube kubectl -- get pods --namespace kube-system`,\n", "</pre><hr class=\"wide-line\"><li><strong>442  What if `prevNode` is undefined?</strong></li><pre>@@ -908,7 +908,7 @@ function genericPrint(path, options, print) {\n", "       return concat([\n", "         node.value,\n", "         // Don't add spaces on escaped colon `:`, e.g: grid-template-rows: [row-1-00\\:00] auto;\n", "-        prevNode.value[prevNode.value.length - 1] === \"\\\\\" ||\n", "+        (prevNode && prevNode.value[prevNode.value.length - 1] === \"\\\\\") ||\n", "         // Don't add spaces on `:` in `url` function (i.e. `url(fbglyph: cross-outline, fig-white)`)\n", "         insideValueFunctionNode(path, \"url\")\n", "           ? \"\"\n", "</pre><hr class=\"wide-line\"><li><strong>443  Is this modified by mistake?</strong></li><pre>@@ -215,7 +215,8 @@ func (b *builtinIfNull{{ .TypeName }}Sig) vecEval{{ .TypeName }}(input *chunk.Ch\n", " \t\t\tresult.<PERSON><PERSON><PERSON><PERSON><PERSON>()\n", " \t\t}\n", " \t}\n", "-\t{{ end }}return nil\n", "+\t{{ end -}}\n", "+\treturn nil\n", " }\n", " \n", " func (b *builtinIfNull{{ .TypeName }}Sig) vectorized() bool {\n", "</pre><hr class=\"wide-line\"><li><strong>444  I think we've been explicitly putting `None` as a return type otherwise mypy doesn't check something correctly? I don't remember the exact failure mode though.</strong></li><pre>@@ -823,7 +823,7 @@ class OidcHandler(BaseHandler):\n", "         token: <PERSON><PERSON>,\n", "         request: SynapseRequest,\n", "         client_redirect_url: str,\n", "-    ):\n", "+    ) -> None:\n", "         \"\"\"Given a UserInfo response, complete the login flow\n", " \n", "         UserInfo should have a claim that uniquely identifies users. This claim\n", "</pre><hr class=\"wide-line\"><li><strong>445  Should `pytest` be removed here as well? ```suggestion 'will run in its own invocation, which will be slower, but isolates ' ```</strong></li><pre>@@ -409,7 +409,7 @@ class PartitionedTestRunnerTaskMixin(TestRunnerTaskMixin, Task):\n", " \n", "     register('--fast', type=bool, default=True, fingerprint=True,\n", "              help='Run all tests in a single invocation. If turned off, each test target '\n", "-                  'will run in its own pytest invocation, which will be slower, but isolates '\n", "+                'will run in its own invocation, which will be slower, but isolates '\n", "                   'tests from process-wide state created by tests in other targets.')\n", "     register('--chroot', advanced=True, fingerprint=True, type=bool, default=False,\n", "              help='Run tests in a chroot. Any loose files tests depend on via `{}` dependencies '\n", "</pre><hr class=\"wide-line\"><li><strong>446  I don't see a case that this method ever returns an error, and there are no checks for errors when we call `startRPC()`, so seems like this should be a void return value.</strong></li><pre>@@ -182,7 +182,7 @@ func (d *daemon) stopISVCS() {\n", " \tglog.Infof(\"isvcs shut down\")\n", " }\n", " \n", "-func (d *daemon) startRPC() error {\n", "+func (d *daemon) startRPC() {\n", " \tif options.DebugPort > 0 {\n", " \t\tgo func() {\n", " \t\t\tif err := http.ListenAndServe(fmt.Sprintf(\"127.0.0.1:%d\", options.DebugPort), nil); err != nil {\n", "</pre><hr class=\"wide-line\"><li><strong>447  'id' is probably also an abbreviation of 'identifier', so this can be updated to: ```suggestion throw new FormatException(\"URI \\\"\" + uriValue + \"\\\" is not a GUID entity ID.\"); ```</strong></li><pre>@@ -34,7 +34,7 @@ namespace Umbraco.Core\n", "         {\n", "             Guid guid;\n", "             if (Guid.TryParse(uriValue.AbsolutePath.TrimStart('/'), out guid) == false)\n", "-                throw new FormatException(\"URI \\\"\" + uriValue + \"\\\" is not a GUID entity id.\");\n", "+                throw new FormatException(\"URI \\\"\" + uriValue + \"\\\" is not a GUID entity ID.\");\n", " \n", "             Guid = guid;\n", "         }\n", "</pre><hr class=\"wide-line\"><li><strong>448  If I'm understanding this correctly, this static block will be executed the first time the AbstractConnectionFactory class is referenced. If there are two applications, each one having their own drivers, then the problem still exists as the initialization will occur during the first app deployment, but not in the second.</strong></li><pre>@@ -18,15 +18,9 @@ import javax.sql.DataSource;\n", " public abstract class AbstractConnectionFactory implements ConnectionFactory\n", " {\n", " \n", "-    /**\n", "-     * Ensures DriverManager classloading takes place before any connection creation.\n", "-     * It prevents a JDK deadlock that only occurs when two JDBC Connections of different DB vendors\n", "-     * are created concurrently and the {@link DriverManager} hasn't been loaded yet.\n", "-     * For more information, see MULE-14605.\n", "-     */\n", "-    static\n", "+    protected AbstractConnectionFactory()\n", "     {\n", "-        DriverManager.getLoginTimeout();\n", "+        initializeDriverMamager();\n", "     }\n", " \n", "     @Override\n", "</pre><hr class=\"wide-line\"><li><strong>449  We created `new_function_name` via a call to `unique_name` above, so shouldn't this be unnecessary? EDIT: hm, I guess `c_print_name` could transmute it back into a non-unique name via $->_ transforms? Ugh.</strong></li><pre>@@ -300,10 +300,11 @@ struct LowerParallelTasks : public IRMutator {\n", "                 std::vector<Expr> args(4);\n", "                 // Codegen will add user_context for us\n", " \n", "-                // Prefix the function name with \"::\" as we would in C to make\n", "+                // Prefix the function name with \"::\" as we would in C++ to make\n", "                 // it clear we're talking about something in global scope in\n", "                 // case some joker names an intermediate Func or Var the same\n", "-                // name as the pipeline.\n", "+                // name as the pipeline. This prefix works transparently in the\n", "+                // C++ backend.\n", "                 args[0] = Variable::make(Handle(), \"::\" + new_function_name);\n", "                 args[1] = t.min;\n", "                 args[2] = t.extent;\n", "</pre><hr class=\"wide-line\"><li><strong>450  I think strictly speaking the heartbeat timeout doesn't matter here because we are not starting the JobMaster (only invoking the constructor).</strong></li><pre>@@ -708,8 +708,7 @@ public class JobMasterTest extends TestLogger {\n", " \t\t\tconfiguration,\n", " \t\t\tjobGraph,\n", " \t\t\thaServices,\n", "-\t\t\tnew TestingJobManagerSharedServicesBuilder().build(),\n", "-\t\t\theartbeatServices);\n", "+\t\t\tnew TestingJobManagerSharedServicesBuilder().build());\n", " \n", " \t\ttry {\n", " \t\t\t// starting the JobMaster should have read the savepoint\n", "</pre><hr class=\"wide-line\"><li><strong>451  I think we can use `TestForAllClients` here. The system admin and local mode clients should behave the same. We can then drop the check that's on line 1914</strong></li><pre>@@ -1860,7 +1860,7 @@ func TestGetPost(t *testing.T) {\n", " \tClient := th.Client\n", " \n", " \tvar privatePost *model.Post\n", "-\tth.TestForClientAndLocal(t, func(t *testing.T, c *model.Client4) {\n", "+\tth.TestForAllClients(t, func(t *testing.T, c *model.Client4) {\n", " \t\tt.<PERSON>()\n", " \n", " \t\tpost, resp := c.<PERSON>(th.BasicPost.Id, \"\")\n", "</pre><hr class=\"wide-line\"><li><strong>452  Why isn't the unity8-session snap using `/run/user/[0-9]*/mir_socket`? What this rule seems to be saying is that the location of the mir socket will vary based on the slot implementation, which is a tight coupling between a specific slot implementation and the snap and the dependency required to make this tight coupling work cannot be expressed in snap/snapcraft.yaml. More concretely, this means that the slot's socket is in /run/user/1000/snap.unity8-session/mir_socket and a unity8 plugging app would need to look for it there. An alternate implementation of unity8 would be at /run/user/1000/snap.unity8-session-somethingelse/mir_socket and all snaps that use 'plugs: [ unity8 ]' will not be able to find it. The purpose of interfaces is that they are a contract between the slot side and the plug side such that a plug doesn't have to care what slot snap it is connecting to and vice versa.</strong></li><pre>@@ -74,7 +74,6 @@ var mirConnectedPlugAppArmor = []byte(`\n", " unix (receive, send) type=seqpacket addr=none peer=(label=###SLOT_SECURITY_TAGS###),\n", " /run/mir_socket rw,\n", " /run/user/[0-9]*/mir_socket rw,\n", "-/run/user/[0-9]*/snap.###SLOT_NAME###/mir_socket rw,\n", " `)\n", " \n", " var mirConnectedPlugSecComp = []byte(`\n", "</pre><hr class=\"wide-line\"><li><strong>453  Can we pass a parameter into `RowWithCols` to differentiate the behavior here and avoid duplicate code? or can we set `PresumeKeyNotExists` and `PresumeKeyNotExistsError` options accordingly to get the behavior we want, i.e, just check transaction buffer?</strong></li><pre>@@ -287,15 +287,9 @@ func (us *UnionScanExec) rowWithColsInTxn(t table.Table, h int64, cols []*table.\n", " \treturn v, nil\n", " }\n", " \n", "-func (us *UnionScanExec) buildAndSortAddedRows() error {\n", "+func (us *UnionScanExec) buildAndSortAddedRows(t table.Table) error {\n", " \tus.addedRows = make([][]types.Datum, 0, len(us.dirty.addedRows))\n", " \tmutableRow := chunk.MutRowFromTypes(us.retTypes())\n", "-\tt, found := GetInfoSchema(us.ctx).TableByID(us.dirty.tid)\n", "-\tif !found {\n", "-\t\t// t is got from a snapshot InfoSchema, so it should be found, this branch should not happen.\n", "-\t\treturn errors.<PERSON>rrorf(\"table not found (tid: %d, schema version: %d)\",\n", "-\t\t\tus.dirty.tid, GetInfoSchema(us.ctx).SchemaMetaVersion())\n", "-\t}\n", " \tcols := t.W<PERSON>ols()\n", " \tfor h := range us.dirty.addedRows {\n", " \t\tnewData := make([]types.Datum, 0, us.schema.Len())\n", "</pre><hr class=\"wide-line\"><li><strong>454  in that case we should remove it from r0 as well?</strong></li><pre>@@ -529,7 +529,7 @@ class ThreepidRestServlet(RestServlet):\n", " \n", " \n", " class ThreepidUnbindRestServlet(RestServlet):\n", "-    PATTERNS = client_patterns(\"/account/3pid/unbind$\", unstable=True)\n", "+    PATTERNS = client_patterns(\"/account/3pid/unbind$\", releases=(), unstable=True)\n", " \n", "     def __init__(self, hs):\n", "         super(ThreepidUnbindRestServlet, self).__init__()\n", "</pre><hr class=\"wide-line\"><li><strong>455  what about a test for the fetch-incidents?</strong></li><pre>@@ -2,7 +2,7 @@ from HelloWorld import Client, say_hello_command, say_hello_over_http_command\n", " \n", " \n", " def test_say_hello():\n", "-    client = Client(url=\"https://test.com\", verify=False, username=\"test\", password=\"test\")\n", "+    client = Client(base_url=\"https://test.com\", verify=False, auth=(\"test\", \"test\"))\n", "     args = {\n", "         \"name\": \"<PERSON><PERSON>\"\n", "     }\n", "</pre><hr class=\"wide-line\"><li><strong>456  Since 5.0 is leaving the station, we probably need a new `DeliveryServiceNullableV31` struct and all the other changes that come with that, since this will likely be 3.1. Right @ocket8888? This shouldn't be backported.</strong></li><pre>@@ -182,6 +182,11 @@ type DeliveryServiceNullableV30 struct {\n", " \tMaxRequestHeaderSize *int    `json:\"maxRequestHeaderSize\" db:\"max_request_header_size\"`\n", " }\n", " \n", "+type DeliveryServiceNullableV31 struct {\n", "+\tDeliveryServiceNullableV30\n", "+\tMaxRequestHeaderSize *int `json:\"maxRequestHeaderSize\" db:\"max_request_header_size\"`\n", "+}\n", "+\n", " // Deprecated: Use versioned structures only from now on.\n", " type DeliveryServiceNullable DeliveryServiceNullableV15\n", " type DeliveryServiceNullableV15 struct {\n", "</pre><hr class=\"wide-line\"><li><strong>457  Here Input(0) is only removed in the case that it does not have any use. Do you know cases that Input(0) does have uses later in the graph?</strong></li><pre>@@ -48,8 +48,6 @@ std::shared_ptr<torch::jit::Graph> PrepareForStaticRuntime(\n", "     }\n", "   }\n", " \n", "-  FuseTensorExprs(g);\n", "-\n", "   return g;\n", " }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>458  Do we need to set this in this test?</strong></li><pre>@@ -89,7 +89,6 @@ public class TestOMDbCheckpointServlet {\n", "     scmId = UUID.randomUUID().toString();\n", "     omId = UUID.randomUUID().toString();\n", "     conf.setBoolean(OZONE_ACL_ENABLED, true);\n", "-    conf.setTimeDuration(OZONE_OPEN_KEY_EXPIRE_THRESHOLD, 2, TimeUnit.SECONDS);\n", "     cluster = MiniOzoneCluster.newBuilder(conf)\n", "         .setClusterId(clusterId)\n", "         .setScmId(scmId)\n", "</pre><hr class=\"wide-line\"><li><strong>459  ```suggestion public static function sanitizeDbFormat(string $value) ```</strong></li><pre>@@ -74,7 +74,7 @@ class Time\n", " \t *\n", " \t * @return string\n", " \t */\n", "-\tpublic static function getTimeByDBFormat(string $time)\n", "+\tpublic static function sanitizeDbFormat(string $time)\n", " \t{\n", " \t\tif ($time) {\n", " \t\t\t$timeDetails = array_pad(explode(' ', $time), 2, '');\n", "</pre><hr class=\"wide-line\"><li><strong>460  ```suggestion /// Gets a value that determines where the contents are buffered on disk. ```</strong></li><pre>@@ -172,7 +172,7 @@ namespace Microsoft.AspNetCore.WebUtilities\n", "         }\n", " \n", "         /// <summary>\n", "-        /// Gets a value that determines if the contents are buffered entirely in memory.\n", "+        /// Gets a value that determines where the contents are buffered on disk.\n", "         /// </summary>\n", "         public string? TempFileName\n", "         {\n", "</pre><hr class=\"wide-line\"><li><strong>461  ogf => of.</strong></li><pre>@@ -175,7 +175,7 @@ public interface KStream<K, V> {\n", "     /**\n", "      * Combines values of this stream with KTable using Left Join.\n", "      *\n", "-     * @param ktable the instance ogf KTable joined with this stream\n", "+     * @param ktable the instance of KTable joined with this stream\n", "      * @param joiner ValueJ<PERSON>ner\n", "      * @param <V1>   the value type of the other stream\n", "      * @param <V2>   the value type of the new stream\n", "</pre><hr class=\"wide-line\"><li><strong>462  rename the field to 'allocator' is better.</strong></li><pre>@@ -58,7 +58,7 @@ type planBuilder struct {\n", " \thasAgg       bool\n", " \tsb           SubQueryBuilder\n", " \tobj          interface{}\n", "-\tallocer      *idAllocer\n", "+\tallocator    *idAllocator\n", " \tctx          context.Context\n", " \tis           infoschema.InfoSchema\n", " \touterSchemas []expression.Schema\n", "</pre><hr class=\"wide-line\"><li><strong>463  name?.length > 15 to prevent an unlikely (but potential) crash</strong></li><pre>@@ -40,7 +40,7 @@ export default function TopMoversSection() {\n", "       symbol,\n", "       // We’re truncating the coin name manually so the width of the text can be measured accurately\n", "       truncatedName: `${\n", "-        name.length > 15 ? name.substring(0, 15).trim() + '...' : name\n", "+        name?.length > 15 ? name.substring(0, 15).trim() + '...' : name\n", "       }`,\n", "     }),\n", "     [handlePress, nativeCurrencySymbol]\n", "</pre><hr class=\"wide-line\"><li><strong>464  This shouldn't be here, I think?</strong></li><pre>@@ -28,7 +28,7 @@ type resourceBoundary struct {\n", " \n", " var (\n", " \tqueueContainerRequestCPU    = resourceBoundary{min: resource.MustParse(\"25m\"), max: resource.MustParse(\"100m\")}\n", "-\tqueueContainerLimitCPU      = resourceBoundary{min: resource.MustParse(\"40m\"), max: resource.MustParse(\"800m\")}\n", "+\tqueueContainerLimitCPU      = resourceBoundary{min: resource.MustParse(\"40m\"), max: resource.MustParse(\"500m\")}\n", " \tqueueContainerRequestMemory = resourceBoundary{min: resource.MustParse(\"50Mi\"), max: resource.MustParse(\"200Mi\")}\n", " \tqueueContainerLimitMemory   = resourceBoundary{min: resource.MustParse(\"200Mi\"), max: resource.MustParse(\"500Mi\")}\n", " )\n", "</pre><hr class=\"wide-line\"><li><strong>465  use @callback or it break our event loop</strong></li><pre>@@ -222,6 +222,7 @@ class MqttCover(CoverDevice):\n", " \n", "             self.async_schedule_update_ha_state()\n", " \n", "+        @callback\n", "         def availability_message_received(topic, payload, qos):\n", "             \"\"\"Handle new MQTT availability messages.\"\"\"\n", "             if payload == self._payload_available:\n", "</pre><hr class=\"wide-line\"><li><strong>466  It appears that the issue was that key was never created. Rather than a round-trip through list_keys and using repr, consider the following implementation: ```suggestion key = ExpectationSuiteIdentifier(expectation_suite_name) ``` Is that addressing the same need?</strong></li><pre>@@ -941,12 +941,15 @@ class BaseDataContext(object):\n", "         Returns:\n", "             True for Success and False for Failure.\n", "         \"\"\"\n", "+        \"\"\"\n", "         key = None\n", "         keys = self.stores[self.expectations_store_name].list_keys()\n", "         for item in keys:\n", "             sval = repr(item)\n", "             if expectation_suite_name.expectation_suite_name in sval:\n", "                 key=item\n", "+        \"\"\"\n", "+        key = ExpectationSuiteIdentifier(expectation_suite_name)\n", "         if not self._stores[self.expectations_store_name].has_key(key):\n", "             raise ge_exceptions.DataContextError(\n", "                 \"expectation_suite with name {} does not exist.\"\n", "</pre><hr class=\"wide-line\"><li><strong>467  IllegalArgumentException would be more informative here.</strong></li><pre>@@ -203,7 +203,7 @@ public final class ClientImpl implements Client, ReplicaProcCaller {\n", "     public final ClientResponse callProcedure(String procName, Object... parameters)\n", "         throws IOException, NoConnectionsException, ProcCallException\n", "     {\n", "-        return callProcedureWithClientTimeout(BatchTimeoutType.NO_TIMEOUT, procName,\n", "+        return callProcedureWithClientTimeout(BatchTimeoutOverrideType.NO_TIMEOUT, procName,\n", "                 Distributer.USE_DEFAULT_CLIENT_TIMEOUT, TimeUnit.SECONDS, parameters);\n", "     }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>468  Isn't it the opposite? (same after)</strong></li><pre>@@ -57,7 +57,7 @@ BOOST_AUTO_TEST_CASE(project_in_direction_test) {\n", "     std::vector<navitia::type::GeographicalCoord> center;\n", "     auto coord_Paris = navitia::type::GeographicalCoord(2.3522219000000177,48.856614);\n", "     auto coord_North = navitia::type::GeographicalCoord(0,90);\n", "-    auto coord_Equator = navitia::type::GeographicalCoord(0,179,9999);\n", "+    auto coord_Equator = navitia::type::GeographicalCoord(180,0);\n", "     center.push_back(coord_Paris);\n", "     center.push_back(coord_North);\n", "     center.push_back(coord_Equator);\n", "</pre><hr class=\"wide-line\"><li><strong>469  don't need the revision</strong></li><pre>@@ -5,7 +5,6 @@ class Openssh < Formula\n", "   mirror \"https://ftp.openbsd.org/pub/OpenBSD/OpenSSH/portable/openssh-7.6p1.tar.gz\"\n", "   version \"7.6p1\"\n", "   sha256 \"a323caeeddfe145baaa0db16e98d784b1fbc7dd436a6bf1f479dfd5cd1d21723\"\n", "-  revision 1\n", " \n", "   bottle do\n", "     sha256 \"a7998e2c51b48845f74bfc925cb00b54778a0ccaa9d02ae40dbc98e4ba1f7963\" => :high_sierra\n", "</pre><hr class=\"wide-line\"><li><strong>470  You can probably leave this out and have a pointer to the recorder only in the clusterController.</strong></li><pre>@@ -116,8 +116,6 @@ func newReconciler(mgr manager.Manager, ctx *clusterd.Context, clusterController\n", " \t\tpanic(err)\n", " \t}\n", " \n", "-\tclusterController.recorder = k8sutil.NewEventReporter(mgr.GetEventRecorderFor(\"ClusterController\"))\n", "-\n", " \treturn &ReconcileCephCluster{\n", " \t\tclient:            mgr.GetClient(),\n", " \t\tscheme:            mgrScheme,\n", "</pre><hr class=\"wide-line\"><li><strong>471  What does this have to do with your patch?</strong></li><pre>@@ -349,9 +349,9 @@\n", "       // Create a new blob:// url for this blob\n", "       this._blobURL = URL.createObjectURL(blob);\n", " \n", "-      document.getElementById('screen').style.backgroundImage =\n", "-        'linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)),' +\n", "-        'url(' + this._blobURL + ')';\n", "+    //  document.getElementById('screen').style.backgroundImage =\n", "+    //    'linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)),' +\n", "+    //    'url(' + this._blobURL + ')';\n", " \n", "       // And tell the system about it.\n", "       var evt = new CustomEvent('wallpaperchange', {\n", "</pre><hr class=\"wide-line\"><li><strong>472  New function name feels oververbose. Keeping the old one but including the new logic would seem alright.</strong></li><pre>@@ -223,7 +223,7 @@ func setSlotsFromSnapYaml(y snapYaml, snap *Info) error {\n", " \treturn nil\n", " }\n", " \n", "-func setAppsAndAliasesFromSnapYaml(y snapYaml, snap *Info) error {\n", "+func setAppsFromSnapYaml(y snapYaml, snap *Info) error {\n", " \tfor appName, yApp := range y.Apps {\n", " \t\t// Collect all apps\n", " \t\tapp := &AppInfo{\n", "</pre><hr class=\"wide-line\"><li><strong>473  I'd remove this `xrange` too, while you're cleaning up things; there's only one of them.</strong></li><pre>@@ -1,8 +1,6 @@\n", " from __future__ import (absolute_import, division, print_function,\n", "                         unicode_literals)\n", " \n", "-from six.moves import xrange\n", "-\n", " from numpy.testing import assert_equal\n", " from matplotlib import rcParams\n", " from matplotlib.testing.decorators import image_comparison, cleanup\n", "</pre><hr class=\"wide-line\"><li><strong>474  Not sure if a nullptr is a sane default. Would probably just make `abstract`.</strong></li><pre>@@ -30,10 +30,10 @@ namespace osu.Framework.Platform\n", "         public abstract bool Exists { get; protected set; }\n", "         public abstract Display CurrentDisplay { get; set; }\n", "         public abstract DisplayMode CurrentDisplayMode { get; set; }\n", "+        public abstract IntPtr WindowHandle { get; }\n", " \n", "         public virtual IEnumerable<Display> Displays => Enumerable.Empty<Display>();\n", "         public virtual Display PrimaryDisplay => Displays.First();\n", "-        public virtual IntPtr WindowHandle { get; } = IntPtr.Zero;\n", " \n", "         #endregion\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>475  if outer plan contains no motion, should we set prefetch_joinqual to false?</strong></li><pre>@@ -1005,6 +1005,12 @@ create_join_plan(PlannerInfo *root, JoinPath *best_path)\n", " \t\t((Join *) plan)->prefetch_joinqual = false;\n", " \t}\n", " \n", "+\t/*\n", "+\t * We may set prefetch_joinqual to true if there is\n", "+\t * potential risk when create_xxxjoin_plan. Here, we\n", "+\t * have all the information at hand, this is the final\n", "+\t * logic to set prefetch_joinqual.\n", "+\t */\n", " \tif (((Join *) plan)->prefetch_joinqual)\n", " \t{\n", " \t\tList *joinqual = ((Join *) plan)->joinqual;\n", "</pre><hr class=\"wide-line\"><li><strong>476  Shouldn't we implement the same for tuples too? The following fails with an index out of range? ```ballerina public function main() { [boolean, int[]...] x = [true]; x[1][1] = 1; } ``` The following also ```ballerina public function main() { [boolean, [float, int]...] x = [true]; x[1][1] = 1; } ```</strong></li><pre>@@ -165,6 +165,10 @@ public class TupleValueImpl extends AbstractArrayValue {\n", " \n", "     @Override\n", "     public Object fillAndGetRefValue(long index) {\n", "+        // Need do a filling-read if index >= size\n", "+        if (index >= this.size && this.hasRestElement) {\n", "+            add(index, (Object) this.tupleType.getRestType().getZeroValue());\n", "+        }\n", "         return get(index);\n", "     }\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>477  evaluate_generator() is missing the class_weight param, which cause test failure. Please check the test log.</strong></li><pre>@@ -1441,7 +1441,6 @@ class Model(network.Network, version_utils.ModelVersionSelector):\n", "         workers=workers,\n", "         use_multiprocessing=use_multiprocessing,\n", "         verbose=verbose,\n", "-        class_weight=class_weight,\n", "         callbacks=callbacks)\n", " \n", "   @deprecation.deprecated(\n", "</pre><hr class=\"wide-line\"><li><strong>478  I think `ConcurrentHashMap` is better?</strong></li><pre>@@ -10,7 +10,7 @@\n", "     <h1>Error 503 Backend is unhealthy</h1>\n", "     <p>Backend is unhealthy</p>\n", "     <h3>Guru Mediation:</h3>\n", "-    <p>Details: cache-sea4452-SEA ********** **********</p>\n", "+    <p>Details: cache-sea4444-SEA ********** **********</p>\n", "     <hr>\n", "     <p>Varnish cache server</p>\n", "   </body>\n", "</pre><hr class=\"wide-line\"><li><strong>479  This flag is only meaningful on Linux. I would add a note to that effect in the help string.</strong></li><pre>@@ -782,7 +782,7 @@ class Console::CommandDispatcher::Core\n", "   end\n", " \n", "   @@migrate_opts = Rex::Parser::Arguments.new(\n", "-    '-p'  => [true,  'Writable path (eg. /tmp).'],\n", "+    '-p'  => [true,  'Writable path - Linux only (eg. /tmp).'],\n", "     '-t'  => [true,  'The number of seconds to wait for migration to finish (default: 60).'],\n", "     '-h'  => [false, 'Help menu.']\n", "   )\n", "</pre><hr class=\"wide-line\"><li><strong>480  This will make tracking these spans impossible in the full context of the operation. Please use a parent context of some kind.</strong></li><pre>@@ -17,12 +17,12 @@ import (\n", " // newly aggregated attestations in the pool.\n", " // It tracks the unaggregated attestations that weren't able to aggregate to prevent\n", " // the deletion of unaggregated attestations in the pool.\n", "-func (c *AttCaches) AggregateUnaggregatedAttestations() error {\n", "+func (c *AttCaches) AggregateUnaggregatedAttestations(ctx context.Context) error {\n", " \tunaggregatedAtts, err := c.UnaggregatedAttestations()\n", " \tif err != nil {\n", " \t\treturn err\n", " \t}\n", "-\treturn c.aggregateUnaggregatedAttestations(context.Background(), unaggregatedAtts)\n", "+\treturn c.aggregateUnaggregatedAttestations(ctx, unaggregatedAtts)\n", " }\n", " \n", " // AggregateUnaggregatedAttestationsBySlotIndex aggregates the unaggregated attestations and saves\n", "</pre><hr class=\"wide-line\"><li><strong>481  same in the line above to this one... you miss changing the loop</strong></li><pre>@@ -92,7 +92,7 @@ class TestMetisSubModelPartList(KratosUnittest.TestCase):\n", "                    [\"Main.submodelpart_liquid.mainPart\" , [85, 193, 118]],\n", "                    [\"Main.submodelpart_solid\" , [280,810,552]]]\n", "         ReadModelPart(self.file_name, model_part, settings)\n", "-        for submodel_part_name in results:\n", "+        for i_result in results:\n", "             submodel_part_name = i_result[0]\n", "             submodel_part = current_model[submodel_part_name]\n", "             local_number_nodes = submodel_part.GetCommunicator().LocalMesh().NumberOfNodes()\n", "</pre><hr class=\"wide-line\"><li><strong>482  Technically, these are also anon vars, right? Shall we move this and other name generation logic to `BLangAnonymousModelHelper`?</strong></li><pre>@@ -1369,7 +1369,7 @@ public class Desugar extends BLangNodeVisitor {\n", "     @Override\n", "     public void visit(BLangRecordVariable varNode) {\n", "         final BLangBlockStmt blockStmt = ASTBuilderUtil.createBlockStmt(varNode.pos);\n", "-        String name = String.format(\"$map$_%d$\", recordVarCount++);\n", "+        String name = anonModelHelper.getNextRecordVarKey(env.enclPkg.packageID);\n", "         final BLangSimpleVariable mapVariable =\n", "                 ASTBuilderUtil.createVariable(varNode.pos, name, symTable.mapAllType, null,\n", "                                               new BVarSymbol(0, names.fromString(name), this.env.scope.owner.pkgID,\n", "</pre><hr class=\"wide-line\"><li><strong>483  Move `require` to the top of the file: `const { exec } = require('child_process')`</strong></li><pre>@@ -1,6 +1,7 @@\n", " \"use strict\";\n", " \n", " const fs = require(\"fs\");\n", "+const { exec } = require(\"child_process\");\n", " const path = require(\"path\");\n", " const root = process.cwd();\n", " const wpfolder = path.resolve(root, \"node_modules/webpack/\");\n", "</pre><hr class=\"wide-line\"><li><strong>484  Are these values specific to the Java implementation, or should these be factored out to somewhere else ?</strong></li><pre>@@ -27,16 +27,12 @@ public final class SmithWatermanJavaAligner implements SmithWatermanAligner {\n", "     private static final SmithWatermanJavaAligner ALIGNER = new SmithWatermanJavaAligner();\n", " \n", "     /**\n", "-     * return the stateless singleton instance of SmithWatermanAligner\n", "+     * return the stateless singleton instance of SmithWatermanJavaAligner\n", "      */\n", "     public static SmithWatermanJavaAligner getInstance() {\n", "         return ALIGNER;\n", "     }\n", " \n", "-    // match=1, mismatch = -1/3, gap=-(1+k/3)\n", "-    public static final SWParameters ORIGINAL_DEFAULT = new SWParameters(3, -1, -4, -3);\n", "-    public static final SWParameters STANDARD_NGS = new SWParameters(25, -50, -110, -6);\n", "-\n", "     /**\n", "      * The state of a trace step through the matrix\n", "      */\n", "</pre><hr class=\"wide-line\"><li><strong>485  typo in `across-domain` phrase.</strong></li><pre>@@ -339,7 +339,7 @@ $(document).ready(function () {\n", "         });\n", "     });\n", " \n", "-    asyncTest('B237672 - TesCafe should not throw an exception \"Access is denied\" on accessing to a content of the across-domain iframe', function () {\n", "+    asyncTest('B237672 - TesCafe should not throw an exception \"Access is denied\" on accessing to a content of the cross-domain iframe', function () {\n", "         let result = false;\n", " \n", "         const $iframe = $('<iframe></iframe>')\n", "</pre><hr class=\"wide-line\"><li><strong>486  Why is this `Optional`?</strong></li><pre>@@ -27,16 +27,11 @@ class TrainingCallback(metaclass=abc.ABCMeta):\n", "         \"\"\"\n", "         pass\n", " \n", "-    def finish_training(self,\n", "-                        error: bool = False,\n", "-                        run_dir: Optional[str] = None,\n", "-                        **info):\n", "+    def finish_training(self, error: bool = False, **info):\n", "         \"\"\"Called once after training is over.\n", " \n", "         Args:\n", "             error (bool): If True, there was an exception during training.\n", "-            run_dir (Optional[str]): The path to the directory for this\n", "-                training run.\n", "             **info: kwargs dict for forward compatibility.\n", "         \"\"\"\n", "         pass\n", "</pre><hr class=\"wide-line\"><li><strong>487  What does this command do?</strong></li><pre>@@ -69,6 +69,7 @@ class Client(BaseClient):\n", "         if count_params > 0:\n", "             for key, value in kwargs.items():\n", "                 key = key.replace('_', '.')\n", "+                key = key.replace(\"sortBy_\", \"sortBy[0]_\")\n", "                 params[key] = value\n", " \n", "         response = self._http_request(\"GET\", url_suffix=\"/api/v1/secrets\", params=params).get(\"records\")\n", "</pre><hr class=\"wide-line\"><li><strong>488  We should use `WRITE_EXECUTOR` since this is public. If we introduce this, we should probably also redirect `executeTransactionAsync` to use it.</strong></li><pre>@@ -80,7 +80,7 @@ abstract class BaseRealm implements Closeable {\n", "      * Thread pool executor used for write operations - only one thread is needed as writes cannot\n", "      * be parallelized.\n", "      */\n", "-    public static final RealmThreadPoolExecutor writeExecutor = RealmThreadPoolExecutor.newSingleThreadExecutor();\n", "+    public static final RealmThreadPoolExecutor WRITE_EXECUTOR = RealmThreadPoolExecutor.newSingleThreadExecutor();\n", " \n", "     final boolean frozen; // Cache the value in Java, since it is accessed frequently and doesn't change.\n", "     final long threadId;\n", "</pre><hr class=\"wide-line\"><li><strong>489  ```suggestion inputs: list of two int64 tensors with shape [batch_size, length]: (input_ids, position_ids) ```</strong></li><pre>@@ -155,7 +155,7 @@ class TFMPNetEmbeddings(tf.keras.layers.Layer):\n", "         Get token embeddings of inputs\n", " \n", "         Args:\n", "-            inputs: list of three int64 tensors with shape [batch_size, length]: (input_ids, position_ids)\n", "+            inputs: list of two int64 tensors with shape [batch_size, length]: (input_ids, position_ids)\n", "             mode: string, a valid value is one of \"embedding\" and \"linear\"\n", " \n", "         Returns:\n", "</pre><hr class=\"wide-line\"><li><strong>490  This change is not needed, we moved from `html` to directories for components.</strong></li><pre>@@ -4,7 +4,7 @@ homeassistant.components.rfxtrx\n", " Provides support for RFXtrx components.\n", " \n", " For more details about this component, please refer to the documentation at\n", "-https://home-assistant.io/components/rfxtrx.html\n", "+https://home-assistant.io/components/rfxtrx/\n", " \"\"\"\n", " import logging\n", " from homeassistant.util import slugify\n", "</pre><hr class=\"wide-line\"><li><strong>491  ```suggestion // Auth == nil means we don't have a credential, but the user is site-admin, so we want to fall back to the global token and continue. if auth != nil { ```</strong></li><pre>@@ -240,6 +240,9 @@ func (e *executor) buildChangesetSource(repo *repos.Repo, extSvc *repos.External\n", " \tsrc := sources[0]\n", " \n", " \tif auth != nil {\n", "+\t\t// If auth == nil that means the user that applied that last\n", "+\t\t// campaign/changeset spec is a site-admin and we can fall back to the\n", "+\t\t// global credentials stored in extSvc.\n", " \t\tucs, ok := src.(repos.UserSource)\n", " \t\tif !ok {\n", " \t\t\treturn nil, errors.Errorf(\"using user credentials on code host of repo %q is not implemented\", repo.Name)\n", "</pre><hr class=\"wide-line\"><li><strong>492  urg, this is pulling in Powershell options: ``` Module options (post/multi/manage/shell_to_meterpreter): Name Current Setting Required Description ---- --------------- -------- ----------- HAN<PERSON><PERSON>R true yes Start an Exploit Multi Handler to receive the connection LHOST no IP of host that will receive the connection from the payload. LPORT 4433 no Port for Payload to connect to. PERSIST false yes Run the payload in a loop PSH_OLD_METHOD false yes Use powershell 1.0 RUN_WOW64 false yes Execute powershell in 32bit compatibility mode, payloads need native arch SESSION yes The session to run this module on. ``` Not a blocker but a bit confusing if your upgrading a linux shell</strong></li><pre>@@ -165,6 +165,7 @@ class Metasploit3 < Msf::Post\n", "       # Run the commands one at a time\n", "       #\n", "       sent = 0\n", "+      aborted = false\n", "       cmds.each { |cmd|\n", "         ret = session.shell_command_token(cmd)\n", "         if (not ret)\n", "</pre><hr class=\"wide-line\"><li><strong>493  How is this necessary? There's already: ``` DXASSERT(Call->getNumArgOperands() == 1, ... ``` and ``` Value *Arg = Call->getArgOperand(0); ``` So how could A be different unless there's a fundamental problem with operand iteration or something?</strong></li><pre>@@ -690,9 +690,6 @@ void HLMatrixLowerPass::lowerPreciseCall(CallInst *Call, IRBuilder<> Builder) {\n", "   DXASSERT(Call->getNumArgOperands() == 1, \"Only one arg expected for precise matrix call\");\n", "   Value *Arg = Call->getArgOperand(0);\n", "   Value *LoweredArg = getLoweredByValOperand(Arg, Builder);\n", "-  for (Value *A : Call->arg_operands()) {\n", "-    DXASSERT(A == Arg, \"oops\");\n", "-  }\n", "   HLModule::MarkPreciseAttributeOnValWithFunctionCall(LoweredArg, Builder, *m_pModule);\n", "   addToDeadInsts(Call);\n", " }\n", "</pre><hr class=\"wide-line\"><li><strong>494  `gcs_location` is not a pipeline option. It's a constructor argument. Can you error out as such?</strong></li><pre>@@ -1702,9 +1702,9 @@ class ReadFromBigQuery(PTransform):\n", "       logging.debug(\"gcs_location is empty, using temp_location instead\")\n", "     else:\n", "       raise ValueError(\n", "-          '{} requires a GCS location to be provided. Neither option'\n", "-          '--gcs_location nor the fallback --temp_location is set.'.format(\n", "-              self.__class__.__name__))\n", "+          '{} requires a GCS location to be provided. Neither gcs_location in'\n", "+          ' the constructor nor the fallback option --temp_location is set.'.\n", "+          format(self.__class__.__name__))\n", "     if self.validate:\n", "       self._validate_gcs_location(gcs_base)\n", " \n", "</pre><hr class=\"wide-line\"><li><strong>495  since we are using latest javascript we can start using self.blackListPeers.includes(ip);</strong></li><pre>@@ -246,7 +246,7 @@ __private.getMatched = function(test, peers) {\n", "  * @todo Add description for the params and the return value\n", "  */\n", " __private.isBlacklisted = function(ip) {\n", "-\treturn self.blackListPeers.indexOf(ip) > -1;\n", "+\treturn self.blackListedPeers.indexOf(ip) > -1;\n", " };\n", " \n", " /**\n", "</pre><hr class=\"wide-line\"><li><strong>496  Here you can just `return a.Srv.Store.Team().GetMember(teamId, userId)`</strong></li><pre>@@ -684,11 +684,7 @@ func (a *App) GetTeamsForUser(userId string) ([]*model.Team, *model.AppError) {\n", " }\n", " \n", " func (a *App) GetTeamMember(teamId, userId string) (*model.TeamMember, *model.AppError) {\n", "-\tresult, err := a.Srv.Store.Team().GetMember(teamId, userId)\n", "-\tif err != nil {\n", "-\t\treturn nil, err\n", "-\t}\n", "-\treturn result, nil\n", "+\treturn a.Srv.Store.Team().GetMember(teamId, userId)\n", " }\n", " \n", " func (a *App) GetTeamMembersForUser(userId string) ([]*model.TeamMember, *model.AppError) {\n", "</pre><hr class=\"wide-line\"><li><strong>497  nit: \"canceled\" throughout the file because we're using it everywhere. (<PERSON> agreed on that spelling as well.)</strong></li><pre>@@ -438,8 +438,8 @@ func TestHealthCheckTimeout(t *testing.T) {\n", " \t\tt<PERSON>(`<-l.output: %+v; want not serving`, res)\n", " \t}\n", " \n", "-\tif !fc.isCancelled() {\n", "-\t\tt<PERSON>(\"StreamHealth should be cancelled after timeout, but is not\")\n", "+\tif !fc.isCanceled() {\n", "+\t\tt.<PERSON>(\"StreamHealth should be canceled after timeout, but is not\")\n", " \t}\n", " \n", " \t// send a healthcheck response, it should be serving again\n", "</pre><hr class=\"wide-line\"><li><strong>498  Please just import the framework.ops, and use the function like ops.convert_to_tensor. This will avoid the confusion for whether this locally defined function.</strong></li><pre>@@ -22,7 +22,7 @@ from __future__ import print_function\n", " import copy\n", " \n", " from tensorflow.python.framework import tensor_shape\n", "-from tensorflow.python.framework.ops import convert_to_tensor\n", "+from tensorflow.python.framework import ops\n", " from tensorflow.python.keras import backend as K\n", " from tensorflow.python.keras.engine.base_layer import Layer\n", " from tensorflow.python.keras.engine.input_spec import InputSpec\n", "</pre><hr class=\"wide-line\"><li><strong>499  could replace by `CollectionUtils.isNotEmpty`</strong></li><pre>@@ -85,7 +85,7 @@ public class CancelAlterTableStmt extends CancelStmt {\n", "         StringBuilder stringBuilder = new StringBuilder();\n", "         stringBuilder.append(\"CANCEL ALTER \" + this.alterType);\n", "         stringBuilder.append(\" FROM \" + dbTableName.toSql());\n", "-        if (alterJobIdList != null && alterJobIdList.size() > 0) {\n", "+        if (!CollectionUtils.isEmpty(alterJobIdList)) {\n", "             stringBuilder.append(\" (\")\n", "             .append(String.join(\",\",alterJobIdList.stream().map(String::valueOf).collect(Collectors.toList())));\n", "             stringBuilder.append(\")\");\n", "</pre><hr class=\"wide-line\"><li><strong>500  Let's not add the linkToMessage to the constructor for now, it should just be populated when we deserialize the payload.</strong></li><pre>@@ -61,8 +61,7 @@ namespace Microsoft.Bot.Schema.Teams\n", "         /// <param name=\"mentions\">List of entities mentioned in the\n", "         /// message.</param>\n", "         /// <param name=\"reactions\">Reactions for the message.</param>\n", "-        /// <param name=\"linkToMessage\">Link back to the message.</param>\n", "-        public MessageActionsPayload(string id = default(string), string replyToId = default(string), string messageType = default(string), string createdDateTime = default(string), string lastModifiedDateTime = default(string), bool? deleted = default(bool?), string subject = default(string), string summary = default(string), string importance = default(string), string locale = default(string), MessageActionsPayloadFrom from = default(MessageActionsPayloadFrom), MessageActionsPayloadBody body = default(MessageActionsPayloadBody), string attachmentLayout = default(string), IList<MessageActionsPayloadAttachment> attachments = default(IList<MessageActionsPayloadAttachment>), IList<MessageActionsPayloadMention> mentions = default(IList<MessageActionsPayloadMention>), IList<MessageActionsPayloadReaction> reactions = default(IList<MessageActionsPayloadReaction>), Uri linkToMessage = default(Uri))\n", "+        public MessageActionsPayload(string id = default(string), string replyToId = default(string), string messageType = default(string), string createdDateTime = default(string), string lastModifiedDateTime = default(string), bool? deleted = default(bool?), string subject = default(string), string summary = default(string), string importance = default(string), string locale = default(string), MessageActionsPayloadFrom from = default(MessageActionsPayloadFrom), MessageActionsPayloadBody body = default(MessageActionsPayloadBody), string attachmentLayout = default(string), IList<MessageActionsPayloadAttachment> attachments = default(IList<MessageActionsPayloadAttachment>), IList<MessageActionsPayloadMention> mentions = default(IList<MessageActionsPayloadMention>), IList<MessageActionsPayloadReaction> reactions = default(IList<MessageActionsPayloadReaction>))\n", "         {\n", "             Id = id;\n", "             ReplyToId = replyToId;\n", "</pre><hr class=\"wide-line\"><li><strong>501  Can this small block of code be removed now that it's unused (the 2 lines below here)?</strong></li><pre>@@ -227,13 +227,12 @@ STATIC void set_freq(machine_pwm_obj_t *self, unsigned int freq, ledc_timer_conf\n", "             i = LEDC_REF_CLK_HZ; // 1 MHz\n", "         }\n", " \n", "-        #if 0\n", "+        #if ESP_IDF_VERSION < ESP_IDF_VERSION_VAL(5, 0, 0)\n", "         // original code\n", "         i /= freq;\n", "         #else\n", "         // See https://github.com/espressif/esp-idf/issues/7722\n", "-        unsigned int divider = i / freq; // truncated\n", "-        // int divider = (i + freq / 2) / freq; // rounded\n", "+        int divider = (i + freq / 2) / freq; // rounded\n", "         if (divider == 0) {\n", "             divider = 1;\n", "         }\n", "</pre>\n", "</body>\n", "</html>\n", "\n"]}], "source": ["RESULTING_HTML = HTML_START + COMPLETE_HTML + HTML_END\n", "print(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["with open('./code_reviewer_feb_12_train_v1.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": 316, "metadata": {}, "outputs": [{"data": {"text/plain": ["'@@ -220,8 +220,8 @@ class ReportType(Enum):\\n \\n     _report_name: str\\n \\n-    def __new__(cls, value: str, report_name: Optional[str] = None):\\n-        member = object.__new__(cls)\\n+    def __new__(cls, value: str, report_name: Optional[str] = None) -> \"ReportType\":\\n+        member: \"ReportType\" = object.__new__(cls)\\n         member._value_ = value\\n         member._report_name = report_name if report_name is not None else value\\n         return member\\n'"]}, "execution_count": 316, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"hunk\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Rag\n", "# 0\n", "# 34\n", "\n", "\n", "\n", "\n", "# # No-Rag\n", "# 1\n", "# 2\n", "# 3\n", "# 8\n", "# 9\n", "# 11\n", "# 14\n", "# 15\n", "# 18\n", "# 19\n", "# 22\n", "# 24\n", "# 26\n", "# 31\n", "# 33\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Rag\n", "RAG_IDS = [\n", "0,\n", "12,\n", "27,\n", "29,\n", "74,\n", "92,\n", "109,\n", "117,\n", "118,\n", "119,\n", "126,\n", "129,\n", "157,\n", "180,\n", "188,\n", "202,\n", "229,\n", "233,\n", "252,\n", "264,\n", "276,\n", "296,\n", "300,\n", "317,\n", "320,\n", "321,\n", "323,\n", "333,\n", "342,\n", "349,\n", "357,\n", "374,\n", "405,\n", "406,\n", "408,\n", "409,\n", "427,\n", "]\n", "\n", "\n", "\n", "\n", "# No-Rag\n", "NON_RAG_IDS = [\n", "1,\n", "2,\n", "3,\n", "7,\n", "10,\n", "13,\n", "15,\n", "17,\n", "19,\n", "20,\n", "21,\n", "22,\n", "23,\n", "24,\n", "25,\n", "26,\n", "28,\n", "31,\n", "32,\n", "35,\n", "40,\n", "56,\n", "84,\n", "86,\n", "89,\n", "106,\n", "113,\n", "125,\n", "128,\n", "130,\n", "132,\n", "135,\n", "148,\n", "149,\n", "152,\n", "154,\n", "193,\n", "198,\n", "199,\n", "204,\n", "212,\n", "216,\n", "217,\n", "219,\n", "222,\n", "225,\n", "231,\n", "267,\n", "277,\n", "281,\n", "284,\n", "303,\n", "309,\n", "324,\n", "345,\n", "348,\n", "350,\n", "355,\n", "366,\n", "367,\n", "399,\n", "411,\n", "414,\n", "416,\n", "419,\n", "429,\n", "437,\n", "443,\n", "444,\n", "446,\n", "479,\n", "]"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
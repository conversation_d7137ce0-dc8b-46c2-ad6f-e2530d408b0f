"""Utils for PR edits."""

import difflib
import random


def get_pr_edits_model_input(sample, one_line_prob):
    # This is needed because these lines will anyway be considered a part of middle_code
    # when we compute `whole_file_text`. So we need to handle line indices correctly.
    if not sample["prefix"].endswith("\n"):
        num_lines_prefix = get_n_lines(sample["prefix"])
        last_line_prefix = get_lines_range(
            sample["prefix"], num_lines_prefix - 1, num_lines_prefix
        )
        sample["old_middle"] = last_line_prefix + sample["old_middle"]
        sample["new_middle"] = last_line_prefix + sample["new_middle"]
        sample["prefix"] = get_lines_range(sample["prefix"], 0, num_lines_prefix - 1)
    if not sample["old_middle"].endswith("\n"):
        first_line_suffix = get_lines_range(sample["old_middle"], 0, 1)
        sample["old_middle"] = sample["old_middle"] + first_line_suffix
        sample["new_middle"] = sample["new_middle"] + first_line_suffix
        sample["suffix"] = get_lines_range(sample["suffix"], 1, int(1e9))

    whole_file_text = sample["prefix"] + sample["old_middle"] + sample["suffix"]
    try:
        commented_range = get_commented_range(sample, one_line_prob)
    except AssertionError:
        return None
    whole_file_text = label_commented_lines(whole_file_text, commented_range)

    new_selected_range = expand_range(
        commented_range, 60, len(whole_file_text.splitlines(True))
    )

    # Check whether the new selected range is covering the original selected range
    original_selected_range = [
        get_n_lines(sample["prefix"]),
        get_n_lines(sample["prefix"]) + get_n_lines(sample["old_middle"]),
    ]
    if not (
        new_selected_range[0] <= original_selected_range[0]
        and new_selected_range[1] >= original_selected_range[1]
    ):
        return None

    # Get the new input sample
    new_prefix = get_lines_range(whole_file_text, 0, new_selected_range[0])
    new_selected_code = get_lines_range(
        whole_file_text, new_selected_range[0], new_selected_range[1]
    )
    new_suffix = get_lines_range(whole_file_text, new_selected_range[1], int(1e9))

    # Change the updated code accordingly
    addition_from_prefix = get_lines_range(
        whole_file_text, new_selected_range[0], original_selected_range[0]
    )
    addition_from_suffix = get_lines_range(
        whole_file_text, original_selected_range[1], new_selected_range[1]
    )
    new_updated_code = (
        addition_from_prefix + sample["new_middle"] + addition_from_suffix
    )

    sample["prefix"] = new_prefix
    sample["suffix"] = new_suffix
    sample["new_middle"] = new_updated_code
    sample["old_middle"] = new_selected_code

    return sample


def get_affected_lines(s1, s2):
    lines1 = s1.splitlines()
    lines2 = s2.splitlines()

    matcher = difflib.SequenceMatcher(None, lines1, lines2)
    opcodes = matcher.get_opcodes()

    affected_line_indices = set()
    for opcode in opcodes:
        tag, i1, i2, _, _ = opcode
        if tag == "equal":
            continue
        elif tag in {"replace", "delete"}:
            affected_line_indices.update(range(i1, i2))
        elif tag == "insert":
            affected_line_indices.add(min(i1, len(lines1) - 1))
        else:
            raise ValueError(f"Unknown tag: {tag}")

    return list(affected_line_indices)


def expand_range(range_, size, upper_limit):
    """Expand `range_` symmetrically until it covers `size` lines."""
    range_ = range_.copy()
    available_size = size - (range_[1] - range_[0])
    while available_size > 0:
        if range_[0] > 0:
            range_[0] -= 1
            available_size -= 1
        if available_size == 0:
            break
        if range_[1] < upper_limit:
            range_[1] += 1
            available_size -= 1
        if range_[0] < 0 or range_[1] > upper_limit:
            assert False, "Shouldn't happen"
        if range_[0] == 0 and range_[1] == upper_limit:
            break

    return range_


def get_commented_range(sample, one_line_prob):
    affected_lines = get_affected_lines(sample["old_middle"], sample["new_middle"])
    assert len(affected_lines) > 0

    # Make line indices relative to the beginning of file
    affected_lines = [
        i + len(sample["prefix"].splitlines(True)) for i in affected_lines
    ]

    if len(affected_lines) == 1 or random.random() < one_line_prob:
        start_idx = random.sample(affected_lines, 1)[0]
        commented_range = [start_idx, start_idx + 1]
    else:
        commented_range = list(sorted(random.sample(affected_lines, 2)))

    return commented_range


def label_commented_lines(code, commented_range):
    code_labelled = []
    for idx, line in enumerate(code.splitlines(True)):
        if commented_range[0] <= idx < commented_range[1]:
            code_labelled.append(f"|>{line}")
        else:
            code_labelled.append(line)
    old_middle_marked = "".join(code_labelled)

    return old_middle_marked


def get_n_lines(s):
    return len(s.splitlines(True))


def get_lines_range(s, start, end):
    lines = s.splitlines(True)[start:end]
    return "".join(lines)

{"cells": [{"cell_type": "code", "execution_count": 127, "metadata": {}, "outputs": [], "source": ["import json\n", "import base64\n", "import requests\n", "import difflib\n", "import time\n", "import random\n", "import re\n", "\n", "from pathlib import Path\n", "\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 137, "metadata": {}, "outputs": [], "source": ["\n", "import subprocess\n", "import tempfile\n", "\n", "GITHUB_TOKEN = \"****************************************\"\n", "\n", "def get_file_at_commit(owner, repo, path, commit_sha):\n", "    url = f\"https://api.github.com/repos/{owner}/{repo}/contents/{path}\"\n", "    headers = {\n", "        'Accept': 'application/vnd.github.v3+json',\n", "        \"Authorization\": f\"token {GITHUB_TOKEN}\"    \n", "    }\n", "    params = {'ref': commit_sha}\n", "\n", "    response = requests.get(url, headers=headers, params=params)\n", "    response.raise_for_status()\n", "    data = response.json()\n", "\n", "    content = base64.b64decode(data['content']).decode('utf-8')\n", "    \n", "    return content\n", "\n", "def get_comment(owner, repo, comment_id):\n", "    url = f\"https://api.github.com/repos/{owner}/{repo}/pulls/comments/{comment_id}\"\n", "    headers = {\n", "        'Accept': 'application/vnd.github.v3+json',\n", "        \"Authorization\": f\"token {GITHUB_TOKEN}\"    \n", "    }\n", "\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()\n", "    data = response.json()\n", "\n", "    return data\n", "\n", "def get_git_diff(code_a: str, code_b: str) -> str:\n", "    with tempfile.NamedTemporaryFile(mode='w') as file_a, tempfile.NamedTemporaryFile(mode='w') as file_b:\n", "        file_a.write(code_a)\n", "        file_a.flush()\n", "        file_b.write(code_b)\n", "        file_b.flush()\n", "\n", "        result = subprocess.run(['git', 'diff', '--unified=3', '--no-index', file_a.name, file_b.name], capture_output=True, text=True)\n", "        return result.stdout\n", "\n", "def parse_diff_into_hunks(diff: str) -> list[str]:\n", "    all_hunks = []\n", "    current_hunk = []\n", "    for line in diff.splitlines(True):\n", "        if line.startswith(\"@@\"):\n", "            if len(current_hunk) > 0:\n", "                all_hunks.append(\"\".join(current_hunk))\n", "                current_hunk = []\n", "            indxs_line = line.split(\"@@\")[1]\n", "            cur_line = f\"@@{indxs_line}@@\\n\"\n", "            current_hunk.append(cur_line)\n", "        else:\n", "            current_hunk.append(line)\n", "    if len(current_hunk) > 0:\n", "        all_hunks.append(\"\".join(current_hunk))\n", "    return all_hunks\n", "\n", "\n", "def simplify_hunk_header(hunk):\n", "    all_lines = hunk.splitlines(True)\n", "    first_line = all_lines[0]\n", "    assert first_line.startswith(\"@@\")\n", "    indxs_line = first_line.split(\"@@\")[1]\n", "    first_line = f\"@@{indxs_line}@@\\n\"\n", "    \n", "    return \"\".join([first_line, *all_lines[1:]])\n", "\n", "\n", "def get_num_lines(s):\n", "    return len(s.splitlines(True))\n", "\n", "def get_first_n_lines(s, n):\n", "    return \"\".join(s.splitlines(True)[:n])\n", "\n", "def hunk_to_code(hunk, ignore_symbol):\n", "    result = []\n", "    for i, line in enumerate(hunk.splitlines(True)):\n", "        if i == 0: # For hunk header\n", "            continue\n", "        if line.startswith(ignore_symbol):\n", "            continue\n", "        line = line[1:]\n", "        result.append(line)\n", "\n", "    return \"\".join(result)\n", "\n", "\n", "def parse_all_diff_numbers(diff_hunk: str):\n", "    match = re.search(r'@@ -(\\d+),(\\d+) \\+(\\d+),(\\d+) @@', diff_hunk)\n", "    if match:\n", "        return (int(match.group(1)), int(match.group(2)),\n", "                int(match.group(3)), int(match.group(4)))\n", "    assert False\n", "\n", "\n", "def mark_lines(code: str, line_range: list[int]) -> str:\n", "    result = []\n", "    for i, line in enumerate(code.splitlines(True)):\n", "        if line_range[0] <= i <= line_range[1]:\n", "            result.append(f\"|>{line}\")\n", "        else:\n", "            result.append(line)\n", "    return \"\".join(result)\n", "\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explore data a bit"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/home/<USER>/repos/augment/experimental/yuri/pr_edits/bq-results-export-pr-comments-mar12-20k.json\")"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["20000it [00:02, 8068.71it/s]\n"]}, {"data": {"text/plain": ["20000"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["data = []\n", "with DATA_PATH.open() as f:\n", "    for line in tqdm(f):\n", "        cur_sample = json.loads(line)\n", "        data.append(cur_sample)\n", "\n", "len(data)"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [], "source": ["random.seed(42)\n", "random.shuffle(data)"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [], "source": ["def get_full_diff_samples(sample):\n", "    whole_samples = []\n", "\n", "    comments = [*filter(lambda c: \"in_reply_to_id\" not in c, sample[\"comments_array\"])]\n", "    comments = comments[:10] # To not get too much comments from a single PR\n", "    for comment in tqdm(comments, desc=\"Iterating over comments\"):\n", "        new_sample = {\n", "            \"comment\": comment,\n", "            \"comment_url\": f\"{sample['url']}/files#r{comment['id']}\"\n", "        }\n", "\n", "        try:\n", "            comment_full_info = get_comment(sample[\"base\"][\"repo_owner\"], sample[\"base\"][\"repo_name\"], comment[\"id\"])\n", "            original_content = get_file_at_commit(sample[\"base\"][\"repo_owner\"], sample[\"base\"][\"repo_name\"], comment[\"path\"], comment[\"base_sha\"])\n", "            updated_content = get_file_at_commit(sample[\"head\"][\"repo_owner\"], sample[\"head\"][\"repo_name\"], comment[\"path\"], comment[\"original_commit_id\"])\n", "        except Exception as e:\n", "            print(f\"Error getting file for comment {comment['id']}: {e}\")\n", "            continue\n", "\n", "        diff = get_git_diff(original_content, updated_content)\n", "        hunks = parse_diff_into_hunks(diff)\n", "\n", "        comment_diff_hunk = simplify_hunk_header(comment[\"diff_hunk\"]).rstrip()\n", "\n", "        new_sample[\"all_considered_hunks\"] = hunks\n", "        new_sample[\"comment_full_info\"] = comment_full_info\n", "        \n", "        for hunk in hunks:\n", "            whole_diff_hunk_prefix = get_first_n_lines(hunk, get_num_lines(comment_diff_hunk)).rstrip()\n", "            if whole_diff_hunk_prefix == comment_diff_hunk:\n", "                new_sample[\"full_diff_hunk\"] = hunk\n", "                break\n", "        \n", "        if \"full_diff_hunk\" not in new_sample:\n", "            print(f\"Cannot find hunk for comment {comment['id']}\")\n", "            continue\n", "\n", "        whole_samples.append(new_sample)\n", "\n", "    return whole_samples\n", "\n"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Iterating over comments: 100%|██████████| 1/1 [00:02<00:00,  2.94s/it]\n", "Iterating over comments: 100%|██████████| 1/1 [00:02<00:00,  2.80s/it]\n", "Iterating over comments: 100%|██████████| 1/1 [00:02<00:00,  2.89s/it]\n", "Iterating over comments: 100%|██████████| 1/1 [00:02<00:00,  2.67s/it]\n", " 40%|████      | 4/10 [00:11<00:16,  2.80s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Cannot find hunk for comment 484756754\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Cannot find hunk for comment 484756226\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Error getting file for comment 484753354: 404 Client Error: Not Found for url: https://api.github.com/repos/raiden-network/raiden/contents/raiden/network/transport/matrix/rtc/aio_queue.py?ref=c3be404f5e68baa648c92067d75b79736fb755f6\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Error getting file for comment 484754033: 404 Client Error: Not Found for url: https://api.github.com/repos/raiden-network/raiden/contents/raiden/network/transport/matrix/rtc/aio_queue.py?ref=c3be404f5e68baa648c92067d75b79736fb755f6\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Cannot find hunk for comment 484752734\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Cannot find hunk for comment 484756841\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Error getting file for comment 484754167: 404 Client Error: Not Found for url: https://api.github.com/repos/raiden-network/raiden/contents/raiden/network/transport/matrix/rtc/aio_queue.py?ref=c3be404f5e68baa648c92067d75b79736fb755f6\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Error getting file for comment 484755152: 404 Client Error: Not Found for url: https://api.github.com/repos/raiden-network/raiden/contents/raiden/network/transport/matrix/rtc/web_rtc.py?ref=c3be404f5e68baa648c92067d75b79736fb755f6\n"]}, {"name": "stderr", "output_type": "stream", "text": []}, {"name": "stdout", "output_type": "stream", "text": ["Error getting file for comment 488022776: 404 Client Error: Not Found for url: https://api.github.com/repos/raiden-network/raiden/contents/raiden/network/transport/matrix/rtc/web_rtc.py?ref=eb943a08308ff494e7a141e6b0daceeb578080ab\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Iterating over comments: 100%|██████████| 10/10 [00:19<00:00,  1.92s/it]\n", "Iterating over comments: 100%|██████████| 1/1 [00:02<00:00,  2.44s/it]\n", " 60%|██████    | 6/10 [00:32<00:26,  6.59s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Error getting file for comment 495486450: 404 Client Error: Not Found for url: https://api.github.com/repos/bogdanvlviv/rails/contents/activesupport/lib/active_support/core_ext/hash/slice.rb?ref=08c20bec942b321497ab7f4a0162c57a039b7ae6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Iterating over comments: 100%|██████████| 2/2 [00:03<00:00,  1.90s/it]\n", " 70%|███████   | 7/10 [00:36<00:17,  5.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Error getting file for comment 495486473: 404 Client Error: Not Found for url: https://api.github.com/repos/bogdanvlviv/rails/contents/activesupport/lib/active_support/core_ext/hash/slice.rb?ref=08c20bec942b321497ab7f4a0162c57a039b7ae6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Iterating over comments: 100%|██████████| 1/1 [00:02<00:00,  2.44s/it]\n", "Iterating over comments: 0it [00:00, ?it/s]it]\n", "Iterating over comments: 100%|██████████| 3/3 [00:06<00:00,  2.06s/it]\n", "100%|██████████| 10/10 [00:45<00:00,  4.54s/it]\n"]}, {"data": {"text/plain": ["10"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["final_samples = []\n", "for i in tqdm(range(10)):\n", "    try:\n", "        out = get_full_diff_samples(data[i])\n", "    except Exception as e:\n", "        print(f\"Error with sample {i}: {e}\")\n", "        continue\n", "    final_samples.append(out)\n", "\n", "len(final_samples)"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 1, 1, 1, 1, 1, 0, 1, 0, 3]"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["[*map(len, final_samples)]"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'comment': {'id': '570399000',\n", "  'path': 'src/gui/transferlistsortmodel.cpp',\n", "  'diff_hunk': '@@ -37,6 +39,21 @@\\n #include \"base/utils/string.h\"\\n #include \"transferlistmodel.h\"\\n \\n+namespace\\n+{\\n+    template <typename T>\\n+    bool customLessThan(const T &left, const T &right)\\n+    {\\n+        static_assert(std::is_same_v<T, int> || std::is_same_v<T, qlonglong> || std::is_same_v<T, qreal>);',\n", "  'body': 'Maybe `std::is_arithmetic_v<T>`?',\n", "  'commit_id': 'cc090b2ff46ae67b5c9512340160ef2417b220c0',\n", "  'original_commit_id': 'cc090b2ff46ae67b5c9512340160ef2417b220c0',\n", "  'base_sha': 'e53634ecef406bca0ec767771df09b80af1d0297',\n", "  'head_sha': 'cc090b2ff46ae67b5c9512340160ef2417b220c0',\n", "  'user': {'login': 'glassez',\n", "   'id': '5063477',\n", "   'type': 'User',\n", "   'site_admin': 'false'},\n", "  'updated_at': '2021-02-04T17:20:42Z'},\n", " 'comment_url': 'https://github.com/qbittorrent/qBittorrent/pull/14335/files#r570399000',\n", " 'all_considered_hunks': ['diff --git a/tmp/tmpakpzfsqy b/tmp/tmpbzltaasa\\nindex e854878a8..f83811c7e 100644\\n--- a/tmp/tmpakpzfsqy\\n+++ b/tmp/tmpbzltaasa\\n',\n", "  '@@ -28,6 +28,8 @@\\n \\n #include \"transferlistsortmodel.h\"\\n \\n+#include <type_traits>\\n+\\n #include <QDateTime>\\n \\n #include \"base/bittorrent/infohash.h\"\\n',\n", "  '@@ -37,6 +39,21 @@\\n #include \"base/utils/string.h\"\\n #include \"transferlistmodel.h\"\\n \\n+namespace\\n+{\\n+    template <typename T>\\n+    bool customLessThan(const T &left, const T &right)\\n+    {\\n+        static_assert(std::is_same_v<T, int> || std::is_same_v<T, qlonglong> || std::is_same_v<T, qreal>);\\n+\\n+        if ((left >= 0) && (right >= 0))\\n+            return left < right;\\n+        if (left >= 0)\\n+            return true;\\n+        return false;\\n+    }\\n+}\\n+\\n TransferListSortModel::TransferListSortModel(QObject *parent)\\n     : QSortFilterProxyModel {parent}\\n {\\n',\n", "  '@@ -86,27 +103,9 @@\\n }\\n \\n bool TransferListSortModel::lessThan(const QModelIndex &left, const QModelIndex &right) const\\n-{\\n-    return lessThan_impl(left, right);\\n-}\\n-\\n-bool TransferListSortModel::lessThan_impl(const QModelIndex &left, const QModelIndex &right) const\\n {\\n     Q_ASSERT(left.column() == right.column());\\n \\n-    const auto invokeLessThanForColumn = [this, &left, &right](const int column) -> bool\\n-    {\\n-        return lessThan_impl(left.sibling(left.row(), column), right.sibling(right.row(), column));\\n-    };\\n-\\n-    const auto hashLessThan = [this, &left, &right]() -> bool\\n-    {\\n-        const TransferListModel *model = qobject_cast<TransferListModel *>(sourceModel());\\n-        const BitTorrent::InfoHash hashL = model->torrentHandle(left)->hash();\\n-        const BitTorrent::InfoHash hashR = model->torrentHandle(right)->hash();\\n-        return hashL < hashR;\\n-    };\\n-\\n     const int sortColumn = left.column();\\n     const QVariant leftValue = left.data(TransferListModel::UnderlyingDataRole);\\n     const QVariant rightValue = right.data(TransferListModel::UnderlyingDataRole);\\n',\n", "  '@@ -114,63 +113,48 @@\\n     switch (sortColumn)\\n     {\\n     case TransferListModel::TR_CATEGORY:\\n-    case TransferListModel::TR_TAGS:\\n     case TransferListModel::TR_NAME:\\n-        if (!leftValue.isValid() || !rightValue.isValid() || (leftValue == rightValue))\\n-            return invokeLessThanForColumn(TransferListModel::TR_QUEUE_POSITION);\\n-        return (Utils::String::naturalCompare(leftValue.toString(), rightValue.toString(), Qt::CaseInsensitive) < 0);\\n+    case TransferListModel::TR_SAVE_PATH:\\n+    case TransferListModel::TR_TAGS:\\n+    case TransferListModel::TR_TRACKER:\\n+        return Utils::String::naturalCompare(leftValue.toString(), rightValue.toString(), Qt::CaseInsensitive) < 0;\\n+\\n+    case TransferListModel::TR_AMOUNT_DOWNLOADED:\\n+    case TransferListModel::TR_AMOUNT_DOWNLOADED_SESSION:\\n+    case TransferListModel::TR_AMOUNT_LEFT:\\n+    case TransferListModel::TR_AMOUNT_UPLOADED:\\n+    case TransferListModel::TR_AMOUNT_UPLOADED_SESSION:\\n+    case TransferListModel::TR_COMPLETED:\\n+    case TransferListModel::TR_ETA:\\n+    case TransferListModel::TR_LAST_ACTIVITY:\\n+    case TransferListModel::TR_SIZE:\\n+    case TransferListModel::TR_TIME_ELAPSED:\\n+    case TransferListModel::TR_TOTAL_SIZE:\\n+        return customLessThan(leftValue.toLongLong(), rightValue.toLongLong());\\n+\\n+    case TransferListModel::TR_AVAILABILITY:\\n+    case TransferListModel::TR_PROGRESS:\\n+    case TransferListModel::TR_RATIO:\\n+    case TransferListModel::TR_RATIO_LIMIT:\\n+        return customLessThan(leftValue.toReal(), rightValue.toReal());\\n \\n     case TransferListModel::TR_STATUS:\\n-        {\\n-            const auto stateL = leftValue.value<BitTorrent::TorrentState>();\\n-            const auto stateR = rightValue.value<BitTorrent::TorrentState>();\\n-\\n-            if (stateL != stateR)\\n-                return stateL < stateR;\\n-            return invokeLessThanForColumn(TransferListModel::TR_QUEUE_POSITION);\\n-        }\\n+        return leftValue.value<BitTorrent::TorrentState>() < rightValue.value<BitTorrent::TorrentState>();\\n \\n     case TransferListModel::TR_ADD_DATE:\\n     case TransferListModel::TR_SEED_DATE:\\n     case TransferListModel::TR_SEEN_COMPLETE_DATE:\\n-        {\\n-            const auto dateL = leftValue.toDateTime();\\n-            const auto dateR = rightValue.toDateTime();\\n-\\n-            if (dateL.isValid() && dateR.isValid())\\n-            {\\n-                if (dateL != dateR)\\n-                    return dateL < dateR;\\n-            }\\n-            else if (dateL.isValid())\\n-            {\\n-                return true;\\n-            }\\n-            else if (dateR.isValid())\\n-            {\\n-                return false;\\n-            }\\n-\\n-            return hashLessThan();\\n-        }\\n+        return leftValue.toDateTime() < rightValue.toDateTime();\\n \\n+    case TransferListModel::TR_DLLIMIT:\\n+    case TransferListModel::TR_DLSPEED:\\n     case TransferListModel::TR_QUEUE_POSITION:\\n-        {\\n-            const auto positionL = leftValue.toInt();\\n-            const auto positionR = rightValue.toInt();\\n+    case TransferListModel::TR_UPLIMIT:\\n+    case TransferListModel::TR_UPSPEED:\\n+        return customLessThan(leftValue.toInt(), rightValue.toInt());\\n \\n-            if ((positionL > 0) || (positionR > 0))\\n-            {\\n-                if ((positionL > 0) && (positionR > 0))\\n-                    return positionL < positionR;\\n-                return positionL != 0;\\n-            }\\n-\\n-            return invokeLessThanForColumn(TransferListModel::TR_SEED_DATE);\\n-        }\\n-\\n-    case TransferListModel::TR_SEEDS:\\n     case TransferListModel::TR_PEERS:\\n+    case TransferListModel::TR_SEEDS:\\n         {\\n             // Active peers/seeds take precedence over total peers/seeds\\n             const auto activeL = leftValue.toInt();\\n',\n", "  '@@ -180,85 +164,16 @@\\n \\n             const auto totalL = left.data(TransferListModel::AdditionalUnderlyingDataRole).toInt();\\n             const auto totalR = right.data(TransferListModel::AdditionalUnderlyingDataRole).toInt();\\n-            if (totalL != totalR)\\n-                return totalL < totalR;\\n-\\n-            return invokeLessThanForColumn(TransferListModel::TR_QUEUE_POSITION);\\n+            return totalL < totalR;\\n         }\\n \\n-    case TransferListModel::TR_ETA:\\n-        {\\n-            // Sorting rules prioritized.\\n-            // 1. Active torrents at the top\\n-            // 2. Seeding torrents at the bottom\\n-            // 3. Torrents with invalid ETAs at the bottom\\n-\\n-            const TransferListModel *model = qobject_cast<TransferListModel *>(sourceModel());\\n-\\n-            // From QSortFilterProxyModel::lessThan() documentation:\\n-            //   \"Note: The indices passed in correspond to the source model\"\\n-            const bool isActiveL = TorrentFilter::ActiveTorrent.match(model->torrentHandle(left));\\n-            const bool isActiveR = TorrentFilter::ActiveTorrent.match(model->torrentHandle(right));\\n-            if (isActiveL != isActiveR)\\n-                return isActiveL;\\n-\\n-            const auto queuePosL = left.sibling(left.row(), TransferListModel::TR_QUEUE_POSITION)\\n-                    .data(TransferListModel::UnderlyingDataRole).toInt();\\n-            const auto queuePosR = right.sibling(right.row(), TransferListModel::TR_QUEUE_POSITION)\\n-                    .data(TransferListModel::UnderlyingDataRole).toInt();\\n-            const bool isSeedingL = (queuePosL < 0);\\n-            const bool isSeedingR = (queuePosR < 0);\\n-            if (isSeedingL != isSeedingR)\\n-            {\\n-                const bool isAscendingOrder = (sortOrder() == Qt::AscendingOrder);\\n-                if (isSeedingL)\\n-                    return !isAscendingOrder;\\n-\\n-                return isAscendingOrder;\\n-            }\\n-\\n-            const auto etaL = leftValue.toLongLong();\\n-            const auto etaR = rightValue.toLongLong();\\n-            const bool isInvalidL = ((etaL < 0) || (etaL >= MAX_ETA));\\n-            const bool isInvalidR = ((etaR < 0) || (etaR >= MAX_ETA));\\n-            if (isInvalidL && isInvalidR)\\n-            {\\n-                if (isSeedingL)  // Both seeding\\n-                    return invokeLessThanForColumn(TransferListModel::TR_SEED_DATE);\\n-\\n-                return (queuePosL < queuePosR);\\n-            }\\n-\\n-            if (!isInvalidL && !isInvalidR)\\n-                return (etaL < etaR);\\n-\\n-            return !isInvalidL;\\n-        }\\n-\\n-    case TransferListModel::TR_LAST_ACTIVITY:\\n-        {\\n-            const auto lastActivityL = leftValue.toLongLong();\\n-            const auto lastActivityR = rightValue.toLongLong();\\n-\\n-            if (lastActivityL < 0) return false;\\n-            if (lastActivityR < 0) return true;\\n-            return lastActivityL < lastActivityR;\\n-        }\\n-\\n-    case TransferListModel::TR_RATIO_LIMIT:\\n-        {\\n-            const auto ratioL = leftValue.toReal();\\n-            const auto ratioR = rightValue.toReal();\\n-\\n-            if (ratioL < 0) return false;\\n-            if (ratioR < 0) return true;\\n-            return ratioL < ratioR;\\n-        }\\n+    default:\\n+        Q_ASSERT_X(false, Q_FUNC_INFO, \"Missing comparsion case\");\\n+        break;\\n     }\\n \\n-    return (leftValue != rightValue)\\n-        ? QSortFilterProxyModel::lessThan(left, right)\\n-        : invokeLessThanForColumn(TransferListModel::TR_QUEUE_POSITION);\\n+    // return `false` by default since `left` is not *strictly* less than `right`\\n+    return false;\\n }\\n \\n bool TransferListSortModel::filterAcceptsRow(const int sourceRow, const QModelIndex &sourceParent) const\\n'],\n", " 'comment_full_info': {'url': 'https://api.github.com/repos/qbittorrent/qBittorrent/pulls/comments/570399000',\n", "  'pull_request_review_id': 583626267,\n", "  'id': 570399000,\n", "  'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU3MDM5OTAwMA==',\n", "  'diff_hunk': '@@ -37,6 +39,21 @@\\n #include \"base/utils/string.h\"\\n #include \"transferlistmodel.h\"\\n \\n+namespace\\n+{\\n+    template <typename T>\\n+    bool customLessThan(const T &left, const T &right)\\n+    {\\n+        static_assert(std::is_same_v<T, int> || std::is_same_v<T, qlonglong> || std::is_same_v<T, qreal>);',\n", "  'path': 'src/gui/transferlistsortmodel.cpp',\n", "  'commit_id': 'ab0e1ec6e837c3c4be76d42211e1893bbcfba1e9',\n", "  'original_commit_id': 'cc090b2ff46ae67b5c9512340160ef2417b220c0',\n", "  'user': {'login': 'glassez',\n", "   'id': 5063477,\n", "   'node_id': 'MDQ6VXNlcjUwNjM0Nzc=',\n", "   'avatar_url': 'https://avatars.githubusercontent.com/u/5063477?v=4',\n", "   'gravatar_id': '',\n", "   'url': 'https://api.github.com/users/glassez',\n", "   'html_url': 'https://github.com/glassez',\n", "   'followers_url': 'https://api.github.com/users/glassez/followers',\n", "   'following_url': 'https://api.github.com/users/glassez/following{/other_user}',\n", "   'gists_url': 'https://api.github.com/users/glassez/gists{/gist_id}',\n", "   'starred_url': 'https://api.github.com/users/glassez/starred{/owner}{/repo}',\n", "   'subscriptions_url': 'https://api.github.com/users/glassez/subscriptions',\n", "   'organizations_url': 'https://api.github.com/users/glassez/orgs',\n", "   'repos_url': 'https://api.github.com/users/glassez/repos',\n", "   'events_url': 'https://api.github.com/users/glassez/events{/privacy}',\n", "   'received_events_url': 'https://api.github.com/users/glassez/received_events',\n", "   'type': 'User',\n", "   'site_admin': <PERSON><PERSON><PERSON>},\n", "  'body': 'Maybe `std::is_arithmetic_v<T>`?',\n", "  'created_at': '2021-02-04T17:14:03Z',\n", "  'updated_at': '2021-02-05T02:01:40Z',\n", "  'html_url': 'https://github.com/qbittorrent/qBittorrent/pull/14335#discussion_r570399000',\n", "  'pull_request_url': 'https://api.github.com/repos/qbittorrent/qBittorrent/pulls/14335',\n", "  'author_association': 'MEMBER',\n", "  '_links': {'self': {'href': 'https://api.github.com/repos/qbittorrent/qBittorrent/pulls/comments/570399000'},\n", "   'html': {'href': 'https://github.com/qbittorrent/qBittorrent/pull/14335#discussion_r570399000'},\n", "   'pull_request': {'href': 'https://api.github.com/repos/qbittorrent/qBittorrent/pulls/14335'}},\n", "  'reactions': {'url': 'https://api.github.com/repos/qbittorrent/qBittorrent/pulls/comments/570399000/reactions',\n", "   'total_count': 1,\n", "   '+1': 1,\n", "   '-1': 0,\n", "   'laugh': 0,\n", "   'hooray': 0,\n", "   'confused': 0,\n", "   'heart': 0,\n", "   'rocket': 0,\n", "   'eyes': 0},\n", "  'start_line': None,\n", "  'original_start_line': None,\n", "  'start_side': None,\n", "  'line': None,\n", "  'original_line': 47,\n", "  'side': 'RIGHT',\n", "  'original_position': 18,\n", "  'position': None,\n", "  'subject_type': 'line'},\n", " 'full_diff_hunk': '@@ -37,6 +39,21 @@\\n #include \"base/utils/string.h\"\\n #include \"transferlistmodel.h\"\\n \\n+namespace\\n+{\\n+    template <typename T>\\n+    bool customLessThan(const T &left, const T &right)\\n+    {\\n+        static_assert(std::is_same_v<T, int> || std::is_same_v<T, qlonglong> || std::is_same_v<T, qreal>);\\n+\\n+        if ((left >= 0) && (right >= 0))\\n+            return left < right;\\n+        if (left >= 0)\\n+            return true;\\n+        return false;\\n+    }\\n+}\\n+\\n TransferListSortModel::TransferListSortModel(QObject *parent)\\n     : QSortFilterProxyModel {parent}\\n {\\n'}"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["final_samples[0][0]"]}, {"cell_type": "code", "execution_count": 104, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "for f_sample_arr in final_samples:\n", "    for f_sample in f_sample_arr:\n", "        MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "        MAIN_HTML += f\"<a href=\\\"{f_sample['comment_url']}\\\">Comment link</a>\"\n", "        MAIN_HTML += f\"<h4>Comment:</h4> <pre>{f_sample['comment']['body']}</pre>\"\n", "        MAIN_HTML += f\"<h4>Diff hunk:</h4> <pre>{f_sample['comment']['diff_hunk']}</pre>\"\n", "        MAIN_HTML += f\"<h4>Real body:</h4> <pre>{f_sample['full_diff_hunk']}</pre>\"\n", "\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_reconstructed_comments_mar12_v5.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/tmp/tmp_reconstructed_diffs_mar12_v1.json\", \"w\") as f:\n", "    json.dump(final_samples, f)"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'comment': {'id': '484642066',\n", "  'path': 'src/crimson/osd/pg.cc',\n", "  'diff_hunk': '@@ -931,12 +935,18 @@ seastar::future<> PG::handle_rep_op(Ref<MOSDRepOp> req)\\n void PG::handle_rep_op_reply(crimson::net::Connection* conn,\\n \\t\\t\\t     const MOSDRepOpReply& m)\\n {\\n-  if (is_valid_rep_op_reply(m)) {\\n+  if (!can_discard_replica_op(m)) {',\n", "  'body': 'could you please extract the rename change into a separate commit ?',\n", "  'commit_id': 'ee861c113fa2af8ace528994c77b1f5f60150f89',\n", "  'original_commit_id': 'ee861c113fa2af8ace528994c77b1f5f60150f89',\n", "  'base_sha': '3627cd1e5cbc43630daa9774ed7159cdf4dbecf8',\n", "  'head_sha': 'ee861c113fa2af8ace528994c77b1f5f60150f89',\n", "  'user': {'login': 'tchaikov',\n", "   'id': '59071',\n", "   'type': 'User',\n", "   'site_admin': 'false'},\n", "  'updated_at': '2020-09-08T04:23:08Z'},\n", " 'comment_url': 'https://github.com/ceph/ceph/pull/37003/files#r484642066',\n", " 'all_considered_hunks': ['diff --git a/tmp/tmpsf05g5ak b/tmp/tmplsg012sw\\nindex a2a4f528b..119b75fdd 100644\\n--- a/tmp/tmpsf05g5ak\\n+++ b/tmp/tmplsg012sw\\n',\n", "  '@@ -908,6 +908,10 @@\\n \\tcrimson::common::system_shutdown_exception());\\n   }\\n \\n+  if (can_discard_replica_op(*req)) {\\n+    return seastar::now();\\n+  }\\n+\\n   ceph::os::Transaction txn;\\n   auto encoded_txn = req->get_data().cbegin();\\n   decode(txn, encoded_txn);\\n',\n", "  '@@ -931,12 +935,18 @@\\n void PG::handle_rep_op_reply(crimson::net::Connection* conn,\\n \\t\\t\\t     const MOSDRepOpReply& m)\\n {\\n-  if (is_valid_rep_op_reply(m)) {\\n+  if (!can_discard_replica_op(m)) {\\n     backend->got_rep_op_reply(m);\\n   }\\n }\\n \\n-bool PG::is_valid_rep_op_reply(const MOSDRepOpReply& reply) const\\n+bool PG::can_discard_op(const MOSDOp& m) const {\\n+  return __builtin_expect(m.get_map_epoch()\\n+      < peering_state.get_info().history.same_primary_since, false);\\n+}\\n+\\n+template <typename MsgType>\\n+bool PG::can_discard_replica_op(const MsgType& m) const\\n {\\n   // if a repop is replied after a replica goes down in a new osdmap, and\\n   // before the pg advances to this new osdmap, the repop replies before this\\n',\n", "  '@@ -945,27 +955,27 @@\\n   // resets the messenger sesssion when the replica reconnects. to avoid the\\n   // out-of-order replies, the messages from that replica should be discarded.\\n   const auto osdmap = peering_state.get_osdmap();\\n-  const int from_osd = reply.get_source().num();\\n+  const int from_osd = m.get_source().num();\\n   if (osdmap->is_down(from_osd)) {\\n-    return false;\\n+    return true;\\n   }\\n   // Mostly, this overlaps with the old_peering_msg\\n   // condition.  An important exception is pushes\\n   // sent by replicas not in the acting set, since\\n   // if such a replica goes down it does not cause\\n   // a new interval.\\n-  if (osdmap->get_down_at(from_osd) >= reply.map_epoch) {\\n-    return false;\\n+  if (osdmap->get_down_at(from_osd) >= m.map_epoch) {\\n+    return true;\\n   }\\n   // same pg?\\n   //  if pg changes *at all*, we reset and repeer!\\n   if (epoch_t lpr = peering_state.get_last_peering_reset();\\n-      lpr > reply.map_epoch) {\\n+      lpr > m.map_epoch) {\\n     logger().debug(\"{}: pg changed {} after {}, dropping\",\\n-                   __func__, get_info().history, reply.map_epoch);\\n-    return false;\\n+                   __func__, get_info().history, m.map_epoch);\\n+    return true;\\n   }\\n-  return true;\\n+  return false;\\n }\\n \\n seastar::future<> PG::stop()\\n'],\n", " 'comment_full_info': {'url': 'https://api.github.com/repos/ceph/ceph/pulls/comments/484642066',\n", "  'pull_request_review_id': 483785678,\n", "  'id': 484642066,\n", "  'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDQ4NDY0MjA2Ng==',\n", "  'diff_hunk': '@@ -931,12 +935,18 @@ seastar::future<> PG::handle_rep_op(Ref<MOSDRepOp> req)\\n void PG::handle_rep_op_reply(crimson::net::Connection* conn,\\n \\t\\t\\t     const MOSDRepOpReply& m)\\n {\\n-  if (is_valid_rep_op_reply(m)) {\\n+  if (!can_discard_replica_op(m)) {',\n", "  'path': 'src/crimson/osd/pg.cc',\n", "  'commit_id': '2c17bc1933d3e06a4d966e6a074d66213d3c61ed',\n", "  'original_commit_id': 'ee861c113fa2af8ace528994c77b1f5f60150f89',\n", "  'user': {'login': 'tchaikov',\n", "   'id': 59071,\n", "   'node_id': 'MDQ6VXNlcjU5MDcx',\n", "   'avatar_url': 'https://avatars.githubusercontent.com/u/59071?v=4',\n", "   'gravatar_id': '',\n", "   'url': 'https://api.github.com/users/tchaikov',\n", "   'html_url': 'https://github.com/tchaikov',\n", "   'followers_url': 'https://api.github.com/users/tchaikov/followers',\n", "   'following_url': 'https://api.github.com/users/tchaikov/following{/other_user}',\n", "   'gists_url': 'https://api.github.com/users/tchaikov/gists{/gist_id}',\n", "   'starred_url': 'https://api.github.com/users/tchaikov/starred{/owner}{/repo}',\n", "   'subscriptions_url': 'https://api.github.com/users/tchaikov/subscriptions',\n", "   'organizations_url': 'https://api.github.com/users/tchaikov/orgs',\n", "   'repos_url': 'https://api.github.com/users/tchaikov/repos',\n", "   'events_url': 'https://api.github.com/users/tchaikov/events{/privacy}',\n", "   'received_events_url': 'https://api.github.com/users/tchaikov/received_events',\n", "   'type': 'User',\n", "   'site_admin': <PERSON><PERSON><PERSON>},\n", "  'body': 'could you please extract the rename change into a separate commit ?',\n", "  'created_at': '2020-09-08T04:23:08Z',\n", "  'updated_at': '2020-09-08T04:38:20Z',\n", "  'html_url': 'https://github.com/ceph/ceph/pull/37003#discussion_r484642066',\n", "  'pull_request_url': 'https://api.github.com/repos/ceph/ceph/pulls/37003',\n", "  'author_association': 'CONTRIBUTOR',\n", "  '_links': {'self': {'href': 'https://api.github.com/repos/ceph/ceph/pulls/comments/484642066'},\n", "   'html': {'href': 'https://github.com/ceph/ceph/pull/37003#discussion_r484642066'},\n", "   'pull_request': {'href': 'https://api.github.com/repos/ceph/ceph/pulls/37003'}},\n", "  'reactions': {'url': 'https://api.github.com/repos/ceph/ceph/pulls/comments/484642066/reactions',\n", "   'total_count': 0,\n", "   '+1': 0,\n", "   '-1': 0,\n", "   'laugh': 0,\n", "   'hooray': 0,\n", "   'confused': 0,\n", "   'heart': 0,\n", "   'rocket': 0,\n", "   'eyes': 0},\n", "  'start_line': None,\n", "  'original_start_line': None,\n", "  'start_side': None,\n", "  'line': 938,\n", "  'original_line': 938,\n", "  'side': 'RIGHT',\n", "  'original_position': 16,\n", "  'position': 16,\n", "  'subject_type': 'line'},\n", " 'full_diff_hunk': '@@ -931,12 +935,18 @@\\n void PG::handle_rep_op_reply(crimson::net::Connection* conn,\\n \\t\\t\\t     const MOSDRepOpReply& m)\\n {\\n-  if (is_valid_rep_op_reply(m)) {\\n+  if (!can_discard_replica_op(m)) {\\n     backend->got_rep_op_reply(m);\\n   }\\n }\\n \\n-bool PG::is_valid_rep_op_reply(const MOSDRepOpReply& reply) const\\n+bool PG::can_discard_op(const MOSDOp& m) const {\\n+  return __builtin_expect(m.get_map_epoch()\\n+      < peering_state.get_info().history.same_primary_since, false);\\n+}\\n+\\n+template <typename MsgType>\\n+bool PG::can_discard_replica_op(const MsgType& m) const\\n {\\n   // if a repop is replied after a replica goes down in a new osdmap, and\\n   // before the pg advances to this new osdmap, the repop replies before this\\n'}"]}, "execution_count": 114, "metadata": {}, "output_type": "execute_result"}], "source": ["sample = final_samples[1][0]\n", "sample"]}, {"cell_type": "code", "execution_count": 143, "metadata": {}, "outputs": [], "source": ["if sample[\"comment_full_info\"][\"start_side\"] == \"LEFT\":\n", "    pass # cont\n", "if sample[\"comment_full_info\"][\"side\"] == \"LEFT\":\n", "    pass # cont\n", "\n", "cur_code = hunk_to_code(sample[\"full_diff_hunk\"], \"-\")\n", "\n", "last_commented_line = sample[\"comment_full_info\"][\"line\"]\n", "first_commented_line = sample[\"comment_full_info\"][\"start_line\"]\n", "if first_commented_line is None:\n", "    first_commented_line = last_commented_line\n", "    \n", "_, _, first_plus_line, _ = parse_all_diff_numbers(sample[\"full_diff_hunk\"])\n", "\n", "first_commented_line -= first_plus_line\n", "last_commented_line -= first_plus_line\n", "\n", "marked_code = mark_lines(cur_code, [first_commented_line, last_commented_line])"]}, {"cell_type": "code", "execution_count": 141, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["void PG::handle_rep_op_reply(crimson::net::Connection* conn,\n", "\t\t\t     const MOSDRepOpReply& m)\n", "{\n", "|>  if (!can_discard_replica_op(m)) {\n", "    backend->got_rep_op_reply(m);\n", "  }\n", "}\n", "\n", "bool PG::can_discard_op(const MOSDOp& m) const {\n", "  return __builtin_expect(m.get_map_epoch()\n", "      < peering_state.get_info().history.same_primary_since, false);\n", "}\n", "\n", "template <typename MsgType>\n", "bool PG::can_discard_replica_op(const MsgType& m) const\n", "{\n", "  // if a repop is replied after a replica goes down in a new osdmap, and\n", "  // before the pg advances to this new osdmap, the repop replies before this\n", "\n"]}], "source": ["print(marked_code)"]}, {"cell_type": "code", "execution_count": 142, "metadata": {}, "outputs": [{"data": {"text/plain": ["'could you please extract the rename change into a separate commit ?'"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "source": ["sample[\"comment\"][\"body\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
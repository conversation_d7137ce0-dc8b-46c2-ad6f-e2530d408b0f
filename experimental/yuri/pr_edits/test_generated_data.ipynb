{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "from pathlib import Path\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "from research.data.spark.utils import k8s_session\n", "\n", "# To initialise formatters registry\n", "from research.eval.harness.factories import create_retriever\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RESULT_DIR = Path(\"/mnt/efs/augment/user/yuri/data/cr_47k_mar20_v2\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark_logs_df = pd.read_csv(RESULT_DIR / \"spark_logs.csv\", index_col=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["number_of_specific_samples = 0\n", "\n", "found = []\n", "\n", "for _stdout in spark_logs_df.stdout:\n", "    # if _stdout.count(\"Skipping\") > 0:\n", "    #     print(_stdout)\n", "    number_of_specific_samples += _stdout.count(\"Assertion\")\n", "    if _stdout.count(\"Assertion\") > 0:\n", "        found.append(_stdout)\n", "\n", "\n", "print(number_of_specific_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(found[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(_stdout)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = MMapIndexedDataset(str(RESULT_DIR / \"valid\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(dataset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TOKEN_APPORTIONMENT = {\n", "    \"path_len\": 256,\n", "    \"instruction_len\": 512,\n", "    \"prefix_len\": 1536,\n", "    \"selected_code_len\": 4096,\n", "    \"suffix_len\": 1024,\n", "    \"max_prompt_tokens\": 16384 - 4096,  # 4096 represents the max output tokens\n", "}\n", "\n", "prompt_formatter = get_prompt_formatter(\"droid\", **TOKEN_APPORTIONMENT)\n", "tokenizer = prompt_formatter.tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = list(map(abs, dataset[0]))\n", "print(tokenizer.detok<PERSON>ze(sample))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = k8s_session(\n", "        max_workers=4,\n", "        name=\"yuri-notebook\",\n", "        conf={\n", "            \"spark.executor.pyspark.memory\": \"1000G\",\n", "        },\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_df = spark.read.parquet(\"s3a://yuri-dev-bucket/tmp/test_cr_mar20_47k_s2_v4\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_df.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = _df.to<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
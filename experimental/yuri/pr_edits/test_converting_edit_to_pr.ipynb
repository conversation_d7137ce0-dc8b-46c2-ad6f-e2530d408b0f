{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "\n", "from pathlib import Path\n", "from experimental.yuri.pr_edits.pr_edits_utils import get_pr_edits_model_input\n", "from research.core.model_input import ModelInput\n", "from tqdm import tqdm\n", "\n", "\n", "DATA_PATH = Path(\"/home/<USER>/tmp/prompts.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sample_to_str(sample):\n", "    s = []\n", "    s.append(f\"Instruction: {sample['instruction']}\\n\\n\")\n", "    s.append(sample[\"prefix\"])\n", "    s.append(\"<\" * 80)\n", "    s.append(sample[\"old_middle\"])\n", "    s.append(\"=\" * 80)\n", "    s.append(sample[\"new_middle\"])\n", "    s.append(\">\" * 80)\n", "    s.append(sample[\"suffix\"])\n", "\n", "    return \"\\n\".join(s)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with DATA_PATH.open() as f:\n", "    data = json.load(f)\n", "\n", "random.seed(42)\n", "random.shuffle(data)\n", "\n", "print(len(data))\n", "data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in tqdm(range(50)):\n", "    sample = data[i]\n", "    with open(\"./remove_me_original.txt\", \"a\") as f:\n", "        f.write(\"@\" * 200)\n", "        f.write(f\"\\n{i}\")\n", "        f.write(sample_to_str(sample))\n", "\n", "    pr_sample = get_pr_edits_model_input(sample.copy(), 0.75)\n", "    with open(\"./remove_me_pr.txt\", \"a\") as f:\n", "        f.write(\"@\" * 200)\n", "        f.write(f\"\\n{i}\")\n", "        if pr_sample is not None:\n", "            f.write(sample_to_str(pr_sample))\n", "        else:\n", "            f.write(\"Skipped.\")\n", "\n", "            "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_good = 0\n", "num_bad = 0\n", "for i in tqdm(range(len(data))):\n", "    pr_sample = get_pr_edits_model_input(data[i].copy(), 0.75)\n", "    if pr_sample is not None:\n", "        num_good += 1\n", "    else:\n", "        num_bad += 1\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_good, num_bad"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_good, num_bad"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = data[0]\n", "pr_sample = get_pr_edits_model_input(sample.copy(), 0)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
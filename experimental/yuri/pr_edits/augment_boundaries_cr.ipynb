{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from experimental.yuri.pr_edits.codereviewer2train_v2 import read_cr_data, process_cr_sample, modify_boundaries\n", "from pathlib import Path\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_first_different_line_idx(s1, s2, rev=False):\n", "    lines1 = [*s1.splitlines(True)]\n", "    lines2 = [*s2.splitlines(True)]\n", "\n", "    if rev:\n", "        lines1 = [*reversed(lines1)]\n", "        lines2 = [*reversed(lines2)]\n", "\n", "    i = 0\n", "    while i < len(lines1) and i < len(lines2) and lines1[i] == lines2[i]:\n", "        i += 1\n", "    return i\n", "\n", "def get_difference_boundaries(s1, s2):\n", "    first = get_first_different_line_idx(s1, s2)\n", "    last = get_first_different_line_idx(s1, s2)\n", "\n", "    return first, last\n", "\n", "def first_n(arr, n):\n", "    if n <= 0:\n", "        return []\n", "    return arr[:n]\n", "\n", "def last_n(arr, n):\n", "    if n <= 0:\n", "        return []\n", "    return arr[-n:]\n", "\n", "\n", "def modify_boundaries(prefix, suffix, old_middle, new_middle, commented_line):\n", "    MARGIN = 10\n", "\n", "    prefix_lines = prefix.splitlines(True)\n", "    suffix_lines = suffix.splitlines(True)\n", "    old_middle_lines = old_middle.splitlines(True)\n", "    new_middle_lines = new_middle.splitlines(True)\n", "\n", "    diffb1, diffb2 = get_difference_boundaries(old_middle, new_middle)\n", "    assert diffb1 == diffb2 == 3\n", "\n", "    borrow_from_prefix = MARGIN - commented_line  # if you have line N commented, then there is N lines already in old_middle BEFORE commented line\n", "    borrow_from_suffix = MARGIN - (len(old_middle_lines) - commented_line - 1)  # If yoi have M lines total and line N is commented, then there is M-N-1 lines already after commented line\n", "\n", "    assert borrow_from_prefix >= 0 and borrow_from_suffix >= 0\n", "\n", "    old_middle_lines_v2 = last_n(prefix_lines, borrow_from_prefix) + old_middle_lines + first_n(suffix_lines, borrow_from_suffix)\n", "    new_middle_lines_v2 = last_n(prefix_lines, borrow_from_prefix) + new_middle_lines + first_n(suffix_lines, borrow_from_suffix)\n", "    prefix_lines_v2 = first_n(prefix_lines, len(prefix_lines) - borrow_from_prefix)\n", "    suffix_lines_v2 = last_n(suffix_lines, len(suffix_lines) - borrow_from_suffix)\n", "    commented_line_v2 = commented_line + borrow_from_prefix\n", "\n", "    old_middle_v2 = \"\".join(old_middle_lines_v2)\n", "    new_middle_v2 = \"\".join(new_middle_lines_v2)\n", "    prefix_v2 = \"\".join(prefix_lines_v2)\n", "    suffix_v2 = \"\".join(suffix_lines_v2)\n", "    \n", "    assert (prefix + old_middle + suffix) == (prefix_v2 + old_middle_v2 + suffix_v2)\n", "    assert (prefix + new_middle + suffix) == (prefix_v2 + new_middle_v2 + suffix_v2)\n", "    assert old_middle_lines[commented_line] == old_middle_lines_v2[commented_line_v2]\n", "\n", "    return (prefix_v2, suffix_v2, old_middle_v2, new_middle_v2, commented_line_v2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import json\n", "# import os\n", "\n", "# def load_json_files(directory):\n", "#     json_files = [f for f in os.listdir(directory) if f.endswith('.json')]\n", "#     data = []\n", "#     for file in tqdm(json_files):\n", "#         with open(os.path.join(directory, file), 'r') as f:\n", "#             data.append(json.load(f))\n", "#     return data\n", "\n", "# def save_as_jsonl(data, output_file):\n", "#     with open(output_file, 'w') as f:\n", "#         for item in tqdm(data):\n", "#             f.write(json.dumps(item) + '\\n')\n", "\n", "# # Load JSON files from a specific directory\n", "# directory = '/mnt/efs/augment/user/yuri/data/cr_75k_filter_mar17_v1/good/'\n", "# data = load_json_files(directory)\n", "\n", "# # Save the data as a single JSONL file\n", "# output_file = '/mnt/efs/augment/user/yuri/data/cr_75k_filter_mar17_v1/good36k_mar19.jsonl'\n", "# save_as_jsonl(data, output_file)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# data = read_cr_data(Path(\"/home/<USER>/data/code_reviewer/Code_Refinement/ref-train.jsonl\"))\n", "data = read_cr_data(Path(\"/mnt/efs/augment/user/yuri/data/cr_75k_filter_mar20_v2/good47k_mar20.jsonl\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_samples = [*filter(lambda s: s['repo'] == \"hashicorp/terraform-provider-azurerm\", tqdm(data))]\n", "len(_samples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for sample in tqdm(_samples):\n", "    pc = process_cr_sample(sample)\n", "    s2 = json.loads(pc[\"samples\"][0])\n", "\n", "    prefix = s2[\"prefix\"]\n", "    suffix = s2[\"suffix\"]\n", "\n", "    old_middle = s2[\"old_middle\"]\n", "    new_middle = s2[\"new_middle\"]\n", "\n", "    commented_line = s2[\"full_sample\"][\"extra\"][\"commented_line\"]\n", "\n", "    prefix_v2, suffix_v2, old_middle_v2, new_middle_v2, commented_line_v2 = modify_boundaries(prefix, suffix, old_middle, new_middle, commented_line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = process_cr_sample(data[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s2 = json.loads(sample[\"samples\"][0])\n", "s2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MARGIN = 10\n", "\n", "prefix = s2[\"prefix\"]\n", "suffix = s2[\"suffix\"]\n", "\n", "old_middle = s2[\"old_middle\"]\n", "new_middle = s2[\"new_middle\"]\n", "\n", "commented_line = s2[\"full_sample\"][\"extra\"][\"commented_line\"]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prefix_v2, suffix_v2, old_middle_v2, new_middle_v2, commented_line_v2 = modify_boundaries(prefix, suffix, old_middle, new_middle, commented_line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["commented_line_v2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(old_middle_v2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(new_middle_v2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
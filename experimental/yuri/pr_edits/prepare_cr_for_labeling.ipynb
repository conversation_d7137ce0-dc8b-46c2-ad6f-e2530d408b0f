{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["from codereviewer2train_v2 import read_cr_data, parse_all_diff_numbers, get_lines, hunk_to_code, get_commented_line_index\n", "\n", "from pathlib import Path\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "import difflib\n", "import random\n", "import json\n", "from collections import Counter "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def process_cr_sample_wo_repo(cr_sample):\n", "    old_start, old_len, _, _ = parse_all_diff_numbers(cr_sample['hunk'])\n", "\n", "    prefix = get_lines(cr_sample['oldf'], 0, old_start)\n", "    suffix = get_lines(cr_sample['oldf'], old_start + old_len, int(1e9))\n", "\n", "    selected_code = hunk_to_code(cr_sample['hunk'], ignore_symbol=\"+\")\n", "    updated_code = hunk_to_code(cr_sample['hunk'], ignore_symbol=\"-\")\n", "\n", "    commented_line = get_commented_line_index(\n", "        cr_sample['old_hunk'],\n", "        cr_sample['hunk'],\n", "    )\n", "\n", "    if commented_line < 0 or commented_line >= len(selected_code.splitlines(True)):\n", "        print(f\"Commented line is outside selected_code.\")\n", "        return None\n", "    \n", "    if \"```suggestion\" in cr_sample['comment']:\n", "        print(f\"Contains suggestion. Skipping.\")\n", "        return None\n", "    \n", "    # code_edit_sample = ResearchEditPromptInput(\n", "    #     path=\"[Skipped...]\",\n", "    #     prefix=prefix,\n", "    #     selected_code=selected_code,\n", "    #     suffix=suffix,\n", "    #     instruction=cr_sample['comment'],\n", "    #     prefix_begin=0,\n", "    #     suffix_end=len(cr_sample[\"oldf\"]),\n", "    #     retrieved_chunks=[],\n", "    #     updated_code=updated_code,\n", "    #     extra={\n", "    #         \"commented_line\": commented_line,\n", "    #         \"pr_url\": f\"https://github.com/{cr_sample['repo']}/pull/{cr_sample['ghid']}/commits/{cr_sample['ids'][1]}\"\n", "    #     },\n", "    # )\n", "    code_edit_sample = {\n", "        \"selected_code\": selected_code,\n", "        \"updated_code\": updated_code,\n", "        \"commented_line\": commented_line,\n", "        \"comment\": cr_sample['comment'],\n", "        \"lang\": cr_sample[\"lang\"]\n", "    }\n", "\n", "    return code_edit_sample\n", "\n", "\n", "def get_diff_html(input_code, output_code, comment, another_header=None):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><strong>{comment}</strong></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Comment: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    if another_header is not None:\n", "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n", "    return html\n", "\n", "def mark_lines(code, line_range):\n", "    result = []\n", "    for i, line in enumerate(code.splitlines(True)):\n", "        if line_range[0] <= i < line_range[1]:\n", "            result.append(f\"|>{line}\")\n", "        else:\n", "            result.append(f\"  {line}\")\n", "    return \"\".join(result)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Reading data: 150406it [00:15, 9925.89it/s] \n"]}], "source": ["data = read_cr_data(Path(\"/home/<USER>/data/code_reviewer/Code_Refinement/ref-train.jsonl\"))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["random.seed(42)\n", "random.shuffle(data)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'old_hunk': '@@ -260,7 +263,7 @@ void shutdown() {\\n                 messages.m_messages.release();\\n             }\\n \\n-            if (m_inflightMessages != null) {\\n+            if (m_inflightMessages != null && m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0) {',\n", " 'oldf': '/* This file is part of VoltDB.\\n * Copyright (C) 2008-2019 VoltDB Inc.\\n *\\n * This program is free software: you can redistribute it and/or modify\\n * it under the terms of the GNU Affero General Public License as\\n * published by the Free Software Foundation, either version 3 of the\\n * License, or (at your option) any later version.\\n *\\n * This program is distributed in the hope that it will be useful,\\n * but WITHOUT ANY WARRANTY; without even the implied warranty of\\n * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\\n * GNU Affero General Public License for more details.\\n *\\n * You should have received a copy of the GNU Affero General Public License\\n * along with VoltDB.  If not, see <http://www.gnu.org/licenses/>.\\n */\\n\\npackage org.voltcore.network;\\n\\nimport java.io.IOException;\\nimport java.nio.ByteBuffer;\\nimport java.nio.channels.CancelledKeyException;\\nimport java.nio.channels.GatheringByteChannel;\\nimport java.util.Deque;\\nimport java.util.concurrent.ConcurrentLinkedDeque;\\nimport java.util.concurrent.ExecutionException;\\nimport java.util.concurrent.TimeUnit;\\n\\nimport javax.net.ssl.SSLEngine;\\n\\nimport org.voltcore.logging.VoltLogger;\\nimport org.voltcore.utils.CoreUtils;\\nimport org.voltcore.utils.DeferredSerialization;\\nimport org.voltcore.utils.FlexibleSemaphore;\\nimport org.voltcore.utils.Pair;\\nimport org.voltcore.utils.ssl.SSLBufferEncrypter;\\n\\nimport com.google_voltpatches.common.util.concurrent.ListenableFuture;\\n\\nimport io.netty.buffer.ByteBuf;\\n\\npublic class TLSEncryptionAdapter {\\n    private static final VoltLogger s_networkLog = new VoltLogger(\"NETWORK\");\\n\\n    private final ConcurrentLinkedDeque<ExecutionException> m_exceptions = new ConcurrentLinkedDeque<>();\\n    // Input frames encrypted as they came in\\n    private final ConcurrentLinkedDeque<EncryptedMessages> m_encryptedQueue = new ConcurrentLinkedDeque<>();\\n\\n    // Encrypted data which is in the process of being written out\\n    private EncryptedMessages m_inflightMessages;\\n\\n    private final FlexibleSemaphore m_inFlight = new FlexibleSemaphore(1);\\n\\n    private final Connection m_connection;\\n    private final CipherExecutor m_ce;\\n    private final SSLEngine m_sslEngine;\\n    private final SSLBufferEncrypter m_encrypter;\\n    private final EncryptionGateway m_ecryptgw = new EncryptionGateway();\\n    private volatile boolean m_isShutdown;\\n\\n    public TLSEncryptionAdapter(Connection connection,\\n                                SSLEngine engine,\\n                                CipherExecutor cipherExecutor) {\\n        m_connection = connection;\\n        m_sslEngine = engine;\\n        m_ce = cipherExecutor;\\n        m_encrypter = new SSLBufferEncrypter(engine);\\n    }\\n\\n    /**\\n     * this values may change if a TLS session renegotiates its cipher suite\\n     */\\n    public int applicationBufferSize() {\\n        return m_sslEngine.getSession().getApplicationBufferSize();\\n    }\\n\\n    /**\\n     * this values may change if a TLS session renegotiates its cipher suite\\n     */\\n    public int packetBufferSize() {\\n        return m_sslEngine.getSession().getPacketBufferSize();\\n    }\\n\\n    public Pair<Integer, Integer> encryptBuffers(Deque<DeferredSerialization> buffersToEncrypt, int frameMax) throws IOException {\\n        ByteBuf accum = m_ce.allocator().buffer(frameMax).clear();\\n\\n        int processedWrites = 0;\\n        DeferredSerialization ds = null;\\n        int bytesQueued = 0;\\n        int frameMsgs = 0;\\n        while ((ds = buffersToEncrypt.poll()) != null) {\\n            ++processedWrites;\\n            final int serializedSize = ds.getSerializedSize();\\n            if (serializedSize == DeferredSerialization.EMPTY_MESSAGE_LENGTH) {\\n                continue;\\n            }\\n            // pack as messages you can inside a TLS frame before you send it to\\n            // the encryption gateway\\n            if (serializedSize > frameMax) {\\n                // frames may contain only one or more whole messages, or only\\n                // partial parts of one message. a message may not contain whole\\n                // messages and an incomplete partial fragment of one\\n                if (accum.writerIndex() > 0) {\\n                    m_ecryptgw.offer(new SerializedMessages(accum, frameMsgs));\\n                    frameMsgs = 0;\\n                    bytesQueued += accum.writerIndex();\\n                    accum = m_ce.allocator().buffer(frameMax).clear();\\n                }\\n                ByteBuf big = m_ce.allocator().buffer(serializedSize).writerIndex(serializedSize);\\n                ByteBuffer jbb = big.nioBuffer();\\n                ds.serialize(jbb);\\n                NIOWriteStreamBase.checkSloppySerialization(jbb, ds);\\n                bytesQueued += big.writerIndex();\\n                m_ecryptgw.offer(new SerializedMessages(big, 1));\\n                frameMsgs = 0;\\n                continue;\\n            } else if (accum.writerIndex() + serializedSize > frameMax) {\\n                m_ecryptgw.offer(new SerializedMessages(accum, frameMsgs));\\n                frameMsgs = 0;\\n                bytesQueued += accum.writerIndex();\\n                accum = m_ce.allocator().buffer(frameMax).clear();\\n            }\\n            ByteBuf packet = accum.slice(accum.writerIndex(), serializedSize);\\n            ByteBuffer jbb = packet.nioBuffer();\\n            ds.serialize(jbb);\\n            NIOWriteStreamBase.checkSloppySerialization(jbb, ds);\\n            accum.writerIndex(accum.writerIndex()+serializedSize);\\n            ++frameMsgs;\\n        }\\n        if (accum.writerIndex() > 0) {\\n            m_ecryptgw.offer(new SerializedMessages(accum, frameMsgs));\\n            bytesQueued += accum.writerIndex();\\n        } else {\\n            accum.release();\\n        }\\n\\n        return new Pair<Integer, Integer>(processedWrites, bytesQueued);\\n    }\\n\\n    void waitForPendingEncrypts() throws IOException {\\n        boolean acquired;\\n\\n        do {\\n            int waitFor = 1 - m_inFlight.availablePermits();\\n            acquired = waitFor == 0;\\n            for (int i = 0; i < waitFor && !acquired; ++i) {\\n                checkForGatewayExceptions();\\n                try {\\n                    acquired = m_inFlight.tryAcquire(1, TimeUnit.SECONDS);\\n                    if (acquired) {\\n                        m_inFlight.release();\\n                    }\\n                } catch (InterruptedException e) {\\n                    throw new IOException(\"interrupted while waiting for pending encrypts\", e);\\n                }\\n            }\\n        } while (!acquired);\\n    }\\n\\n    boolean hasOutstandingData() {\\n        return !(m_inflightMessages == null && m_encryptedQueue.isEmpty());\\n    }\\n\\n    static final class EncryptLedger {\\n        final int encryptedBytesDelta;\\n        final long bytesWritten;\\n        final int messagesWritten;\\n\\n        public EncryptLedger(int delta, long bytesWritten, int messagesWritten) {\\n            this.encryptedBytesDelta = delta;\\n            this.bytesWritten = bytesWritten;\\n            this.messagesWritten = messagesWritten;\\n        }\\n    }\\n\\n    public EncryptLedger drainEncryptedMessages(final GatheringByteChannel channel) throws IOException {\\n        checkForGatewayExceptions();\\n\\n        int delta = 0;\\n        int bytesWritten = 0;\\n        int messagesWritten = 0;\\n\\n        while (true) {\\n            if (m_inflightMessages == null) {\\n                m_inflightMessages = m_encryptedQueue.poll();\\n                if (m_inflightMessages == null) {\\n                    break;\\n                }\\n                delta += m_inflightMessages.m_delta;\\n            }\\n\\n            bytesWritten += m_inflightMessages.write(channel);\\n            if (m_inflightMessages.m_messages.isReadable()) {\\n                break;\\n            }\\n\\n            messagesWritten += m_inflightMessages.m_count;\\n            m_inflightMessages.m_messages.release();\\n            m_inflightMessages = null;\\n        }\\n\\n        return new EncryptLedger(delta, bytesWritten, messagesWritten);\\n    }\\n\\n    // Called from synchronized block only\\n    // (Except from dumpState, which doesn\\'t appear to be used).\\n    public boolean isEmpty() {\\n        return m_ecryptgw.isEmpty() && m_encryptedQueue.isEmpty() && m_inflightMessages == null;\\n    }\\n\\n    public void checkForGatewayExceptions() throws IOException {\\n        ExecutionException ee = m_exceptions.poll();\\n        if (ee != null) {\\n            IOException ioe = TLSException.ioCause(ee.getCause());\\n            if (ioe == null) {\\n                ioe = new IOException(\"encrypt task failed\", ee.getCause());\\n            }\\n            throw ioe;\\n        }\\n    }\\n\\n    String dumpState() {\\n        return new StringBuilder(256).append(\"TLSEncryptionAdapter[\")\\n                .append(\"isEmpty()=\").append(isEmpty())\\n                .append(\", exceptions.isEmpty()=\").append(m_exceptions.isEmpty())\\n                .append(\", encryptedFrames.isEmpty()=\").append(m_encryptedQueue.isEmpty())\\n                .append(\", m_inflightMessages.readableBytes()=\")\\n                .append(m_inflightMessages == null ? 0 : m_inflightMessages.m_messages.readableBytes())\\n                .append(\", gateway=\").append(m_ecryptgw.dumpState())\\n                .append(\", inFlight=\").append(m_inFlight.availablePermits())\\n                .append(\"]\").toString();\\n    }\\n\\n    // Called from synchronized block only\\n    public int getOutstandingMessageCount() {\\n        return m_encryptedQueue.size()\\n                + (m_inflightMessages == null ? 0 : m_inflightMessages.m_count);\\n    }\\n\\n    // Called from synchronized block only\\n    void shutdown() {\\n        if (m_isShutdown) { // make sure we only shutdown once.\\n            return;\\n        }\\n        m_isShutdown = true;\\n        try {\\n            int waitFor = 1 - Math.min(m_inFlight.availablePermits(), -4);\\n            for (int i = 0; i < waitFor; ++i) {\\n                try {\\n                    if (m_inFlight.tryAcquire(1, TimeUnit.SECONDS)) {\\n                        m_inFlight.release();\\n                        break;\\n                    }\\n                } catch (InterruptedException e) {\\n                    break;\\n                }\\n            }\\n\\n            m_ecryptgw.die();\\n\\n            EncryptedMessages messages = null;\\n            while ((messages = m_encryptedQueue.poll()) != null) {\\n                messages.m_messages.release();\\n            }\\n\\n            if (m_inflightMessages != null && m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0) {\\n                m_inflightMessages.m_messages.release();\\n            }\\n        } finally {\\n            m_inFlight.drainPermits();\\n            m_inFlight.release();\\n        }\\n    }\\n\\n    /**\\n     * Construct used to serialize all the encryption tasks for this stream.\\n     * it takes an encryption request offer, divides it into chunks that\\n     * can be handled wholly by SSLEngine wrap, and queues all the\\n     * encrypted frames to the m_encrypted queue. All faults are queued\\n     * to the m_exceptions queue\\n     */\\n    class EncryptionGateway implements Runnable {\\n        private final ConcurrentLinkedDeque<SerializedMessages> m_q = new ConcurrentLinkedDeque<>();\\n\\n        synchronized void offer(SerializedMessages frame) {\\n            final boolean wasEmpty = m_q.isEmpty();\\n\\n            m_q.add(frame);\\n            m_inFlight.reducePermits(1);\\n\\n            if (wasEmpty) {\\n                submitSelf();\\n            }\\n        }\\n\\n        synchronized int die() {\\n            int toUnqueue = 0;\\n            SerializedMessages ef = null;\\n            while ((ef = m_q.poll()) != null) {\\n                toUnqueue += ef.m_messages.readableBytes();\\n            }\\n            return toUnqueue;\\n        }\\n\\n        String dumpState() {\\n            return new StringBuilder(256).append(\"EncryptionGateway[\")\\n                    .append(\"q.isEmpty()=\").append(m_q.isEmpty())\\n                    .append(\"]\").toString();\\n        }\\n\\n        @Override\\n        public void run() {\\n            SerializedMessages messages = m_q.peek();\\n            if (messages == null) {\\n                return;\\n            }\\n\\n            try {\\n                int clearTextSize = messages.m_messages.readableBytes();\\n                ByteBuf encr;\\n                try {\\n                    encr = m_encrypter.tlswrap(messages.m_messages, m_ce.allocator());\\n                } catch (TLSException e) {\\n                    m_exceptions.offer(new ExecutionException(\"failed to encrypt frame\", e));\\n                    m_connection.enableWriteSelection();\\n                    return;\\n                }\\n\\n                if (m_isShutdown) {\\n                    encr.release();\\n                    return;\\n                }\\n\\n                m_encryptedQueue.offer(new EncryptedMessages(encr, messages.m_count, clearTextSize));\\n\\n                /*\\n                 * All interactions with write stream must be protected with a lock to ensure that interests ops are\\n                 * consistent with the state of writes queued to the stream. This prevent lost queued writes where the\\n                 * write is queued but the write interest op is not set.\\n                 */\\n                try {\\n                    m_connection.enableWriteSelection();\\n                } catch (CancelledKeyException e) {\\n                    // If the connection gets closed for some reason we will get this error.\\n                    // OK to ignore and return immediately\\n                    s_networkLog.debug(\"CancelledKeyException while trying to enable write\", e);\\n                    return;\\n                }\\n            } finally {\\n                messages.m_messages.release();\\n                m_inFlight.release();\\n            }\\n\\n            synchronized(this) {\\n                m_q.poll();\\n                if (m_q.peek() != null && !m_isShutdown) {\\n                    submitSelf();\\n                }\\n            }\\n        }\\n\\n        boolean isEmpty() {\\n            return m_q.isEmpty();\\n        }\\n\\n        void submitSelf() {\\n            ListenableFuture<?> fut = m_ce.submit(this);\\n            fut.addListener(new ExceptionListener(fut), CoreUtils.LISTENINGSAMETHREADEXECUTOR);\\n        }\\n    }\\n\\n    class ExceptionListener implements Runnable {\\n        private final ListenableFuture<?> m_fut;\\n        private ExceptionListener(ListenableFuture<?> fut) {\\n            m_fut = fut;\\n        }\\n        @Override\\n        public void run() {\\n            if (!m_isShutdown) {\\n                return;\\n            }\\n            try {\\n                m_fut.get();\\n            } catch (InterruptedException notPossible) {\\n            } catch (ExecutionException e) {\\n                s_networkLog.error(\"unexpect fault occurred in encrypt task\", e.getCause());\\n                m_exceptions.offer(e);\\n            }\\n        }\\n    }\\n\\n    /**\\n     * Simple class to hold the plain text bytes of 1 one or more messages\\n     */\\n    private static class SerializedMessages {\\n        // Plain text data of messages\\n        final ByteBuf m_messages;\\n        // Number of individual messages in m_messages\\n        final int m_count;\\n\\n        SerializedMessages(ByteBuf messages, int count) {\\n            super();\\n            m_messages = messages;\\n            m_count = count;\\n        }\\n    }\\n\\n    /**\\n     * Simple class to hold a fully encrypted messages\\n     */\\n    private static final class EncryptedMessages extends SerializedMessages {\\n        // Difference in size of encrypted data to clear text data\\n        final int m_delta;\\n\\n        EncryptedMessages(ByteBuf messages, int count, int clearTextSize) {\\n            super(messages, count);\\n            m_delta = m_messages.readableBytes() - clearTextSize;\\n        }\\n\\n        /**\\n         * Write the contents of these messages to {@code channel}\\n         *\\n         * @param channel {@link GatheringByteChannel} to write to\\n         * @return Number of bytes written\\n         * @throws IOException If an error occurs\\n         * @see GatheringByteChannel#write(ByteBuffer[])\\n         */\\n        long write(GatheringByteChannel channel) throws IOException {\\n            return m_messages.readBytes(channel, m_messages.readableBytes());\\n        }\\n    }\\n}\\n',\n", " 'hunk': '@@ -263,7 +263,8 @@ public class TLSEncryptionAdapter {\\n                 messages.m_messages.release();\\n             }\\n \\n-            if (m_inflightMessages != null && m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0) {\\n+            if (m_inflightMessages != null) {\\n+                assert (m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0);\\n                 m_inflightMessages.m_messages.release();\\n             }\\n         } finally {\\n',\n", " 'comment': 'm_messages cannot be null so that check is not necessary. Also I would say it is a bug if m_inflightMessages != null && m_inflightMessages.m_messages.refCnt() == 0. Upon release of the messages m_inflightMessages must be set to null.',\n", " 'ids': [85823,\n", "  '977288c9549830985f3052b7249c2630f6fcbade',\n", "  '29ad8b0a77655f647e4f0ba9e25925ad4b8cb08c'],\n", " 'repo': 'VoltDB/voltdb',\n", " 'ghid': 6371,\n", " 'old': '                 messages.m_messages.release();\\n             }\\n-            if (m_inflightMessages != null && m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0) {\\n                 m_inflightMessages.m_messages.release();\\n             }\\n         } finally {',\n", " 'new': '                 messages.m_messages.release();\\n             }\\n+            if (m_inflightMessages != null) {\\n+                assert (m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0);\\n                 m_inflightMessages.m_messages.release();\\n             }\\n         } finally {',\n", " 'lang': 'java'}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["data[1]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Commented line is outside selected_code.\n", "Contains suggestion. Skipping.\n"]}], "source": ["edit_samples = []\n", "for cr_sample in data:\n", "    sample = process_cr_sample_wo_repo(cr_sample)\n", "    if sample is None:\n", "        continue\n", "    edit_samples.append(sample)\n", "    if len(edit_samples) == 200:\n", "        break"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'selected_code': '                messages.m_messages.release();\\n            }\\n\\n            if (m_inflightMessages != null && m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0) {\\n                m_inflightMessages.m_messages.release();\\n            }\\n        } finally {\\n',\n", " 'updated_code': '                messages.m_messages.release();\\n            }\\n\\n            if (m_inflightMessages != null) {\\n                assert (m_inflightMessages.m_messages != null && m_inflightMessages.m_messages.refCnt() > 0);\\n                m_inflightMessages.m_messages.release();\\n            }\\n        } finally {\\n',\n", " 'commented_line': 3,\n", " 'comment': 'm_messages cannot be null so that check is not necessary. Also I would say it is a bug if m_inflightMessages != null && m_inflightMessages.m_messages.refCnt() == 0. Upon release of the messages m_inflightMessages must be set to null.',\n", " 'lang': 'java'}"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["edit_samples[0]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["Counter({'java': 50,\n", "         'go': 42,\n", "         'py': 39,\n", "         'js': 17,\n", "         '.cs': 16,\n", "         'cpp': 15,\n", "         'rb': 13,\n", "         'php': 7,\n", "         'c': 1})"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["Counter([sample['lang'] for sample in edit_samples])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["for i, sample in enumerate(edit_samples):\n", "    with open(f\"/mnt/efs/augment/user/yuri/data/200_pr_data_samples/{i}.json\", \"w\") as f:\n", "        json.dump(sample, f, indent=2)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["edit_samples_read = []\n", "for i in range(200):\n", "    with open(f\"/mnt/efs/augment/user/yuri/data/200_pr_data_samples/{i}.json\", \"r\") as f:\n", "        edit_samples_read.append(json.load(f))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "for sample_id, sample in enumerate(edit_samples_read):\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {sample_id}</h2><hr>\"\n", "\n", "    commented_line_range = [\n", "        sample[\"commented_line\"],\n", "        sample[\"commented_line\"] + 1,\n", "    ]\n", "\n", "    cur_diff = get_diff_html(\n", "        mark_lines(sample['selected_code'], commented_line_range),\n", "        mark_lines(sample['updated_code'], [-10, -10]),\n", "        sample['comment'],\n", "    )\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_to_label_mar11.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
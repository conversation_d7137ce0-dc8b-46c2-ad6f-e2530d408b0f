"""Converting codereviewer data to training format."""

import argparse
import json
import os
import re
import shutil
import subprocess
from dataclasses import asdict
from datetime import datetime
from functools import partial
from pathlib import Path

import pandas as pd
from tqdm import tqdm

from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.utils_for_log import create_logger
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.utils import convert_repository_to_documents

LOGGER = create_logger(__file__)
S3_REPO_CACHE = "s3://yuri-dev-bucket/tmp/repo_cache"
S3_CFG = """[default]
access_key = ??
secret_key = ??
# The region for the host_bucket and host_base must be the same.
host_base = object.las1.coreweave.com
host_bucket = %(bucket)s.object.las1.coreweave.com
check_ssl_certificate = True
check_ssl_hostname = True
"""


def is_s3_path_exists(path, only_s3a=True):
    if only_s3a:
        assert path.startswith("s3a://")
        _path = f"s3{path[3:]}"
    else:
        assert path.startswith("s3a://") or path.startswith("s3://")
        if path.startswith("s3a://"):
            _path = f"s3{path[3:]}"
        else:
            _path = path
    cmd = ["s3cmd", "ls", _path]
    result = subprocess.run(
        cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
    )
    return len(result.stdout) > 0


def get_spark(max_workers):
    spark = k8s_session(
        name="yuri-init",
        conf={
            "spark.executor.pyspark.memory": "1000G",
            "spark.executor.memory": "30G",
            "spark.sql.parquet.columnarReaderBatchSize": "256",
            "spark.task.cpus": "5",
        },
        max_workers=max_workers,
    )
    return spark


def read_cr_data(path):
    cr_data = []
    with path.open() as f:
        for line in tqdm(f, desc="Reading data"):
            cr_data.append(json.loads(line))
    return cr_data


def parse_all_diff_numbers(diff_hunk: str):
    match = re.search(r"@@ -(\d+),(\d+) \+(\d+),(\d+) @@", diff_hunk)
    if match:
        return (
            int(match.group(1)) - 1,
            int(match.group(2)),
            int(match.group(3)) - 1,
            int(match.group(4)),
        )
    raise AssertionError()


def get_lines(code: str, start: int, stop: int):
    return "".join(code.splitlines(True)[start:stop])


def hunk_to_code(hunk, ignore_symbol):
    result = []
    for i, line in enumerate(hunk.splitlines(True)):
        if i == 0:  # For hunk header
            continue
        if line.startswith(ignore_symbol):
            continue
        line = line[1:]
        result.append(line)

    return "".join(result)


def delete_file(path: str):
    try:
        os.remove(path)  # noqa: PL107
    except FileNotFoundError:
        pass


def checkout_repo(repo, commit_hash):
    url = f"https://github.com/{repo}.git"

    repo_id = f"{repo.replace('/', '__')}___{commit_hash}"
    clone_dir = Path("/tmp") / repo_id

    if clone_dir.exists():
        shutil.rmtree(clone_dir)
    clone_dir.mkdir(parents=True, exist_ok=True)

    check_file_path = f"{S3_REPO_CACHE}/{repo_id}_FULLY_CLONED_MARKER"
    tar_name = f"{repo_id}.tar"
    if is_s3_path_exists(check_file_path, only_s3a=False):
        print(f"FOUND CLONED: {repo_id}. Using it.")
        delete_file(f"/tmp/{tar_name}")
        subprocess.run(
            f"s3cmd get {S3_REPO_CACHE}/{tar_name} /tmp/{tar_name}",
            shell=True,
            check=True,
        )
        subprocess.run(
            f"tar -xf /tmp/{tar_name} -C {clone_dir}", shell=True, check=True
        )
    else:
        print(f"DIDN'T FIND CLONED: {repo_id}. Cloning it.")

        # Clone the repository with minimal history and checkout the specific commit
        subprocess.run(
            ["git", "clone", "--depth", "1", url, clone_dir],
            check=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )
        subprocess.run(
            ["git", "-C", clone_dir, "fetch", "--depth", "1", "origin", commit_hash],
            check=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )
        subprocess.run(
            ["git", "-C", clone_dir, "checkout", commit_hash],
            check=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )

        subprocess.run(
            f"tar -cf /tmp/{tar_name} -C {clone_dir} .", shell=True, check=True
        )
        subprocess.run(
            f"s3cmd put /tmp/{tar_name} {S3_REPO_CACHE}/{tar_name}",
            shell=True,
            check=True,
        )
        subprocess.run(
            f"touch emptyfile && s3cmd put emptyfile {check_file_path}",
            shell=True,
            check=True,
        )

    return clone_dir, url


def find_matching_files(S: str, D: Path) -> list:
    matching_files = []

    if not D.is_dir():
        raise ValueError(f"The path {D} is not a directory.")

    for file_path in tqdm(D.rglob("*"), desc="Searching for matching files"):
        if file_path.is_file():
            try:
                with open(file_path, "r", encoding="utf-8") as file:  # noqa: PL123
                    content = file.read()
                    if content == S:
                        matching_files.append(file_path)
            except Exception:  # pylint: disable=W0718
                pass

    return matching_files


def get_commented_line_index(old_hunk, hunk):
    number_of_added_lines = len(
        list(filter(lambda line: not line.startswith("-"), old_hunk.splitlines()))
    )
    _, _, c, _ = parse_all_diff_numbers(old_hunk)
    a, _, _, _ = parse_all_diff_numbers(hunk)
    return (
        c - a + number_of_added_lines - 2
    )  # -2 to accounts for diff header and the selected line itself


def convert_doc(doc):
    return {
        "hexsha": doc["id"],
        "content": doc["text"],
        "max_stars_repo_path": doc["path"],
    }


def convert_sample(sample):
    return {
        "instruction": sample["instruction"],
        "path": sample["path"],
        "prefix": sample["prefix"],
        "suffix": sample["suffix"],
        "old_middle": sample["selected_code"],
        "new_middle": sample["updated_code"],
        "repo_url": sample["extra"]["repo_url"],
        "full_sample": sample,
    }


def get_first_different_line_idx(s1, s2, rev=False):
    lines1 = [*s1.splitlines(True)]
    lines2 = [*s2.splitlines(True)]

    if rev:
        lines1 = [*reversed(lines1)]
        lines2 = [*reversed(lines2)]

    i = 0
    while i < len(lines1) and i < len(lines2) and lines1[i] == lines2[i]:
        i += 1
    return i


def get_difference_boundaries(s1, s2):
    first = get_first_different_line_idx(s1, s2)
    last = get_first_different_line_idx(s1, s2)

    return first, last


def first_n(arr, n):
    if n <= 0:
        return []
    return arr[:n]


def last_n(arr, n):
    if n <= 0:
        return []
    return arr[-n:]


def modify_boundaries(prefix, suffix, old_middle, new_middle, commented_line):
    MARGIN = 10

    prefix_lines = prefix.splitlines(True)
    suffix_lines = suffix.splitlines(True)
    old_middle_lines = old_middle.splitlines(True)
    new_middle_lines = new_middle.splitlines(True)

    borrow_from_prefix = (
        MARGIN - commented_line
    )  # if you have line N commented, then there is N lines already in old_middle BEFORE commented line
    borrow_from_suffix = (
        MARGIN - (len(old_middle_lines) - commented_line - 1)
    )  # If yoi have M lines total and line N is commented, then there is M-N-1 lines already after commented line

    assert borrow_from_prefix >= 0 and borrow_from_suffix >= 0, (
        borrow_from_prefix,
        borrow_from_suffix,
    )

    old_middle_lines_v2 = (
        last_n(prefix_lines, borrow_from_prefix)
        + old_middle_lines
        + first_n(suffix_lines, borrow_from_suffix)
    )
    new_middle_lines_v2 = (
        last_n(prefix_lines, borrow_from_prefix)
        + new_middle_lines
        + first_n(suffix_lines, borrow_from_suffix)
    )
    prefix_lines_v2 = first_n(prefix_lines, len(prefix_lines) - borrow_from_prefix)
    suffix_lines_v2 = last_n(suffix_lines, len(suffix_lines) - borrow_from_suffix)
    commented_line_v2 = commented_line + min(borrow_from_prefix, len(prefix_lines))

    old_middle_v2 = "".join(old_middle_lines_v2)
    new_middle_v2 = "".join(new_middle_lines_v2)
    prefix_v2 = "".join(prefix_lines_v2)
    suffix_v2 = "".join(suffix_lines_v2)

    assert (prefix + old_middle + suffix) == (prefix_v2 + old_middle_v2 + suffix_v2)
    assert (prefix + new_middle + suffix) == (prefix_v2 + new_middle_v2 + suffix_v2)
    assert old_middle_lines[commented_line] == old_middle_lines_v2[commented_line_v2]

    return (prefix_v2, suffix_v2, old_middle_v2, new_middle_v2, commented_line_v2)


def process_cr_sample(cr_sample):
    repo_name = cr_sample["repo"]
    commit_hash = cr_sample["ids"][1]

    # Convert cr sample to harness sample
    old_start, old_len, _, _ = parse_all_diff_numbers(cr_sample["hunk"])

    prefix = get_lines(cr_sample["oldf"], 0, old_start)
    suffix = get_lines(cr_sample["oldf"], old_start + old_len, int(1e9))

    selected_code = hunk_to_code(cr_sample["hunk"], ignore_symbol="+")
    updated_code = hunk_to_code(cr_sample["hunk"], ignore_symbol="-")

    commented_line = get_commented_line_index(
        cr_sample["old_hunk"],
        cr_sample["hunk"],
    )

    try:
        prefix, suffix, selected_code, updated_code, commented_line = modify_boundaries(
            prefix, suffix, selected_code, updated_code, commented_line
        )
    except AssertionError as e:
        print(f"Assertion error while modifying boundaries: {e}")
        return None

    if commented_line < 0 or commented_line >= len(selected_code.splitlines(True)):
        print("Commented line is outside selected_code.")
        return None

    if cr_sample["old_hunk"].splitlines()[-1].startswith("-"):
        print("Comment was attached to the line that was deleted.")
        return None

    try:
        repo_dir, repo_url = checkout_repo(repo_name, commit_hash)
    except Exception:  # pylint: disable=W0718
        print("Failed to checkout repo")
        return None

    possible_file_paths = find_matching_files(cr_sample["oldf"], repo_dir)
    if len(possible_file_paths) != 1:
        print(f"len(possible_file_paths) != 1, {len(possible_file_paths)}")
        shutil.rmtree(repo_dir)
        return None

    file_path = possible_file_paths[0].relative_to(repo_dir)

    docs = convert_repository_to_documents(str(repo_dir))

    code_edit_sample = ResearchEditPromptInput(
        path=str(file_path),
        prefix=prefix,
        selected_code=selected_code,
        suffix=suffix,
        instruction=cr_sample["comment"],
        # prefix_begin=0,
        # suffix_end=len(cr_sample["oldf"]),
        retrieved_chunks=[],
        updated_code=updated_code,
        extra={
            "repo_url": repo_url,
            "commented_line": commented_line,
            "pr_url": f"https://github.com/{cr_sample['repo']}/pull/{cr_sample['ghid']}/commits/{cr_sample['ids'][1]}",
        },
    )
    docs_as_dict = {"repo_url": repo_url, "docs": [asdict(doc) for doc in docs]}

    shutil.rmtree(repo_dir)

    final_sample = json.dumps(convert_sample(asdict(code_edit_sample)))
    final_file_list = json.dumps(list(map(convert_doc, docs_as_dict["docs"])))
    final_record = {
        "repo_url": repo_url,
        "samples": [
            final_sample,
        ],
        "file_list": final_file_list,
    }

    return final_record


def process_cr_sample_batch(batch: pd.DataFrame, progress_file: Path):
    try:
        subprocess.run("s3cmd --help", shell=True, check=True)
    except Exception:  # pylint: disable=W0718
        subprocess.run("sudo apt install s3cmd -y", shell=True, check=True)
        with open("/home/<USER>/.s3cfg", "w") as f:  # noqa: PL123
            f.write(S3_CFG)

    records: list[dict] = []
    for i in range(batch.shape[0]):
        cur_record = process_cr_sample(batch.iloc[i])
        with progress_file.open("a") as f:
            if cur_record is not None:
                f.write("+\n")
                records.append(cur_record)
            else:
                f.write("-\n")

    # To not break the dataframe schema.
    if len(records) == 0:
        records.append(
            {
                "repo_url": "",
                "samples": [""],
                "file_list": "",
            }
        )

    return pd.DataFrame(records)


def main(args):
    assert not is_s3_path_exists(args.stage1_output_path)
    assert not is_s3_path_exists(args.stage2_output_path)

    LOGGER.info(f"Creating spark session with {args.max_workers} workers...")
    spark = get_spark(args.max_workers)

    # Stage 1 : storing initial data in parquet format.
    cr_data = read_cr_data(args.input_path)
    cr_data_df = pd.DataFrame(cr_data)

    LOGGER.info("Storing data...")
    # TODO(yuri): make this numbers customizable.
    spark.createDataFrame(cr_data_df).repartition(2500).write.parquet(
        args.stage1_output_path
    )

    # Stage 2 : generating augmented data.
    LOGGER.info("Processing data...")
    spark_result = map_parquet.apply_pandas(
        spark,
        partial(
            process_cr_sample_batch,
            progress_file=args.progress_bar_file,
        ),
        input_path=args.stage1_output_path,
        output_path=args.stage2_output_path,
        drop_original_columns=True,
        timeout=int(1e6),
        batch_size=4,
        ignore_error=True,
    )
    timestamp = datetime.now().strftime("%Y-%m-%d-%H:%M:%S")
    logs_path = f"/tmp/spark_logs_{timestamp}.csv"
    spark_result["task_info"].to_csv(logs_path)
    LOGGER.info(f"Logs are stored at {logs_path}")


def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    parser.add_argument(
        "--input_path",
        "-i",
        type=Path,
        required=True,
        help="Local path to jsonl file with the input data.",
    )
    parser.add_argument(
        "--stage1_output_path",
        "-s1",
        type=str,
        required=True,
        help="S3 path to the output of 1st stage.",
    )
    parser.add_argument(
        "--stage2_output_path",
        "-s2",
        type=str,
        required=True,
        help="S3 path to the output of 2nd stage.",
    )
    parser.add_argument(
        "--max_workers",
        "-mw",
        type=int,
        help="Maximum number of spark workers.",
        default=32,
    )
    parser.add_argument(
        "--progress_bar_file",
        "-pf",
        type=Path,
        required=True,
        help="File into which Spark worker logs the progress.",
    )

    args = parser.parse_args()
    return args


if __name__ == "__main__":
    _args = parse_args()
    main(_args)

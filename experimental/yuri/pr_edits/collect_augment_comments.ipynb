{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import re\n", "\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_last_n_prs(repo_owner, repo_name, access_token, n):\n", "    prs = []\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls\"\n", "    headers = {\"Authorization\": f\"token {access_token}\"}\n", "    \n", "    items_per_page = min(n, 100)\n", "    params = {\"state\": \"closed\", \"per_page\": items_per_page}\n", "\n", "    while True:\n", "        response = requests.get(url, params=params, headers=headers)\n", "        if response.status_code == 200:\n", "            batch = response.json()\n", "            prs.extend(batch)\n", "            # If the length of the batch is less than items_per_page, we've reached the last page\n", "            if len(batch) < items_per_page or len(prs) >= n:\n", "                break\n", "            # Prepare for the next page\n", "            params[\"page\"] = params.get(\"page\", 1) + 1\n", "        else:\n", "            raise Exception(f\"Failed to fetch PRs: {response.status_code} - {response.text}\")\n", "\n", "        # Ensure we don't fetch more than needed\n", "        if len(prs) >= n:\n", "            prs = prs[:n]\n", "            break\n", "\n", "    return prs\n", "    \n", "\n", "def get_pr_comments(repo_owner, repo_name, pr_number, access_token):\n", "    url = url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls/{pr_number}/comments\"\n", "    \n", "    headers = {'Accept': 'application/vnd.github.v3+json'}\n", "    headers[\"Authorization\"] = f\"token {access_token}\"\n", "    \n", "    response = requests.get(url, headers=headers)\n", "    \n", "    if response.status_code == 200:\n", "        comments = response.json()\n", "        return comments\n", "    else:\n", "        raise Exception(f\"Failed to fetch PR comments: {response.status_code} - {response.text}\")\n", "    \n", "\n", "def is_comment_ok(comment, pr):\n", "    if comment[\"user\"][\"login\"] == pr[\"user\"][\"login\"]:\n", "        print(1)\n", "        return False\n", "    \n", "    if comment.get(\"diff_hunk\") is None:\n", "        print(2)\n", "        return False\n", "    \n", "    if comment.get(\"in_reply_to_id\") is not None:\n", "        print(3)\n", "        return False\n", "    \n", "    if comment.get(\"original_start_line\") is not None:\n", "        print(4)\n", "        start_line = comment.get(\"original_start_line\")\n", "        end_line = comment.get(\"original_line\")\n", "        if end_line is None:\n", "            print(5)\n", "            return False  # I hope this doesn't happen\n", "        if end_line - start_line > 10:\n", "            print(6)\n", "            return False\n", "        \n", "    if comment.get(\"start_line\") is not None:\n", "        print(7)\n", "        start_line = comment.get(\"start_line\")\n", "        end_line = comment.get(\"line\")\n", "        if end_line is None:\n", "            print(8)\n", "            return False  # I hope this doesn't happen\n", "        if end_line - start_line > 10:\n", "            print(9)\n", "            return False\n", "    \n", "    return True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prs = get_last_n_prs(\"augmentcode\", \"augment\", \"??\", 500)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(prs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pr = prs[3]\n", "pr[\"number\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["comments = get_pr_comments(\"augmentcode\", \"augment\", 5348, \"??\")\n", "comments = [comment for comment in comments if is_comment_ok(comment, pr)]\n", "\n", "len(comments)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["comments[4][\"body\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["comments[4]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["h = \"@@ -11,7 +11,16 @@ pub struct GcpConfig {\\n     pub topic_prefix: String,\\n     pub subscription_prefix: String,\\n \\n+    // the ack deadline for the result subscription (in seconds)\\n+    // a blob notification to a transformation key that isn't acked via an upload_transformed_content call\\n+    // is retried\\n     pub notification_ack_deadline: u32,\\n+\\n+    pub notification_min_retry_backoff_seconds: Option<i64>,\"\n", "print(h)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_comments = []\n", "\n", "all_prs = get_last_n_prs(\"augmentcode\", \"augment\", \"??\", 300)\n", "for pr in tqdm(all_prs):\n", "    comments = get_pr_comments(\"augmentcode\", \"augment\", pr[\"number\"], \"??\")\n", "    comments = [comment for comment in comments if is_comment_ok(comment, pr)]\n", "    print(f\"{pr['number']} - {len(comments)}\")\n", "    final_comments.extend(comments)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list_of_urls = [*map(lambda c: c[\"html_url\"], final_comments)]\n", "\n", "list_of_urls"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(list_of_urls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["['https://github.com/augmentcode/augment/pull/5354#discussion_r1532757922',\n", " 'https://github.com/augmentcode/augment/pull/5354#discussion_r1532764775',\n", " 'https://github.com/augmentcode/augment/pull/5353#discussion_r1532882797',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532576434',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532578895',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532579607',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532750377',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532821790',\n", " 'https://github.com/augmentcode/augment/pull/5348#discussion_r1532863639',\n", " 'https://github.com/augmentcode/augment/pull/5343#discussion_r1532534320',\n", " 'https://github.com/augmentcode/augment/pull/5342#discussion_r1532496218',\n", " 'https://github.com/augmentcode/augment/pull/5338#discussion_r1532076166',\n", " 'https://github.com/augmentcode/augment/pull/5338#discussion_r1532540910',\n", " 'https://github.com/augmentcode/augment/pull/5335#discussion_r1531539558',\n", " 'https://github.com/augmentcode/augment/pull/5330#discussion_r1531303260',\n", " 'https://github.com/augmentcode/augment/pull/5326#discussion_r1531265153',\n", " 'https://github.com/augmentcode/augment/pull/5316#discussion_r1531228763',\n", " 'https://github.com/augmentcode/augment/pull/5289#discussion_r1530736835',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530533750',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530534732',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530540240',\n", " 'https://github.com/augmentcode/augment/pull/5284#discussion_r1530704842',\n", " 'https://github.com/augmentcode/augment/pull/5275#discussion_r1530718258',\n", " 'https://github.com/augmentcode/augment/pull/5272#discussion_r1531120872',\n", " 'https://github.com/augmentcode/augment/pull/5271#discussion_r1529587988',\n", " 'https://github.com/augmentcode/augment/pull/5271#discussion_r1529589124',\n", " 'https://github.com/augmentcode/augment/pull/5265#discussion_r1531126137',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529210048',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529210320',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529211511',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529212059',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529213120',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214083',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214332',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529214594',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529215103',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529349702',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529349821',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529364380',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529366270',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1529366936',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532816796',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532818246',\n", " 'https://github.com/augmentcode/augment/pull/5255#discussion_r1532820210',\n", " 'https://github.com/augmentcode/augment/pull/5254#discussion_r1529540160',\n", " 'https://github.com/augmentcode/augment/pull/5247#discussion_r1528954795',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528872880',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528876913',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528878576',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528885940',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528890317',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528913843',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1528916308',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529007532',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529015251',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529018446',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529019532',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529020183',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529021131',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529022424',\n", " 'https://github.com/augmentcode/augment/pull/5242#discussion_r1529023162',\n", " 'https://github.com/augmentcode/augment/pull/5233#discussion_r1528845045',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527624096',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527630059',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1527630244',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1528694853',\n", " 'https://github.com/augmentcode/augment/pull/5230#discussion_r1528705577']"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
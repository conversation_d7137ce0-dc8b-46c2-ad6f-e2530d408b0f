{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "import zipfile\n", "import tempfile\n", "import os\n", "import subprocess\n", "import re\n", "import shutil\n", "import difflib\n", "import time\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from research.retrieval.utils import convert_repository_to_documents\n", "from research.core.edit_prompt_input import ResearchPREditPromptInput\n", "from base.ranges.range_types import LineRange\n", "from dataclasses import asdict"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["STORE_ALL_REPOS_DIR = Path(\"/mnt/efs/augment/user/yuri/data/code_reviewer_feb_12\")\n", "\n", "def download_and_unpack_zip(url, target_dir: Path):\n", "    if not target_dir.exists():\n", "        target_dir.mkdir(exist_ok=True)\n", "        zip_path = target_dir / 'downloaded.zip'\n", "        \n", "        subprocess.run(['curl', '-L', url, '-o', zip_path], check=True)\n", "        \n", "        with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "            zip_ref.extractall(target_dir)\n", "        os.remove(zip_path)\n", "\n", "    temp_dir_content = [*target_dir.iterdir()]\n", "    assert len(temp_dir_content) == 1\n", "    repo_path = temp_dir_content[0]\n", "\n", "    return repo_path\n", "\n", "def get_repo_zip_url(cr_sample):\n", "    return f\"https://github.com/{cr_sample['repo']}/archive/{cr_sample['ids'][1]}.zip\"\n", "\n", "\n", "def find_matching_files(S: str, D: Path) -> list:\n", "    matching_files = []\n", "    \n", "    if not D.is_dir():\n", "        raise ValueError(f\"The path {D} is not a directory.\")\n", "    \n", "    for file_path in D.rglob('*'):\n", "        if file_path.is_file():\n", "            try:\n", "                with open(file_path, 'r', encoding='utf-8') as file:\n", "                    content = file.read()\n", "                    if content == S:\n", "                        matching_files.append(file_path)\n", "            except Exception as e:\n", "                # print(f\"Could not read file {file_path}: {e}\")\n", "                pass\n", "    \n", "    return matching_files\n", "\n", "def parse_all_diff_numbers(diff_hunk: str):\n", "    match = re.search(r'@@ -(\\d+),(\\d+) \\+(\\d+),(\\d+) @@', diff_hunk)\n", "    if match:\n", "        return (int(match.group(1)) - 1, int(match.group(2)),\n", "                int(match.group(3)) - 1, int(match.group(4)))\n", "    assert False\n", "\n", "def get_lines(code: str, start: int, stop: int):\n", "    return \"\".join(code.splitlines(True)[start:stop])\n", "\n", "def hunk_to_suggestion(hunk):\n", "    result = []\n", "    for i, line in enumerate(hunk.splitlines(True)):\n", "        if i == 0: # For hunk header\n", "            continue\n", "        if line.startswith(\"-\"):\n", "            continue\n", "        line = line[1:]\n", "        result.append(line)\n", "\n", "    return \"\".join(result)\n", "\n", "def print_w_lines(s, start=None, end=None):\n", "    for i, line in enumerate(s.splitlines()):\n", "        if start is not None and i < start:\n", "            continue\n", "        if end is not None and i > end:\n", "            continue\n", "        print(f\"{i + 1} {line}\")\n", "\n", "def get_commit_urls(cr_sample):\n", "    first = f\"https://github.com/{cr_sample['repo']}/pull/{cr_sample['ghid']}/commits/{cr_sample['ids'][1]}\"\n", "    second = f\"https://github.com/{cr_sample['repo']}/pull/{cr_sample['ghid']}/commits/{cr_sample['ids'][2]}\"\n", "\n", "    return first, second\n", "\n", "def check_sample(harness_sample, cr_sample):\n", "    # Just to check samples\n", "    # There seems to appear one additional new line after the inserted suggestion. Maybe git hunk always contains one additional new line at the end?\n", "\n", "    original_file_content = harness_sample.prefix + harness_sample.selected_code + harness_sample.suffix\n", "    assert original_file_content == cr_sample['oldf']\n", "\n", "    updated_file_content = get_lines(original_file_content, 0, harness_sample.range_to_replace.start) +\\\n", "                        harness_sample.generated_suggestion +\\\n", "                        get_lines(original_file_content, harness_sample.range_to_replace.stop, int(1e9))\n", "\n", "    first_url, second_url = get_commit_urls(cr_sample)\n", "    print(f\"Filepath: {harness_sample.path}\")\n", "    print(f\"Before URL: {first_url}. \")\n", "    print(f\"Comment {harness_sample.instruction}\\nshould on the line {len(harness_sample.prefix.splitlines()) + 1}\\n with content: {harness_sample.selected_code}\")\n", "\n", "    print(\"#\" * 20)\n", "\n", "    print(f\"After URL: {second_url}. \")\n", "    print(f\"Please check file content around {harness_sample.range_to_replace}\")\n", "    print_w_lines(updated_file_content, harness_sample.range_to_replace.start - 5, harness_sample.range_to_replace.stop + 5)\n", "\n", "def convert_to_harness_sample(cr_sample):\n", "    download_dir = STORE_ALL_REPOS_DIR / (str(cr_sample['repo'].replace(\"/\", \"___\")) + \"___\" + cr_sample['ids'][1])\n", "    zip_url = get_repo_zip_url(cr_sample)\n", "    repo_path = download_and_unpack_zip(zip_url, download_dir)\n", "\n", "    possible_file_paths = find_matching_files(cr_sample['oldf'], repo_path)\n", "    assert len(possible_file_paths) == 1\n", "    file_path = possible_file_paths[0].relative_to(repo_path)\n", "\n", "    a, b, _, _ = parse_all_diff_numbers(cr_sample[\"hunk\"])\n", "    range_to_replace = (a, a+b)\n", "\n", "    _, _, c, _ = parse_all_diff_numbers(cr_sample[\"old_hunk\"])\n", "    # Since we don't pass diff there is no way model will see the removed line.\n", "    # So for cases where comment attached to a removed line, we have no chance to get real selected line. And it will be just somewhere around\n", "    # But for comments attached to a new line, it will be set correctly.\n", "    number_of_added_lines = len(list(filter(lambda l: not l.startswith(\"-\"), cr_sample[\"old_hunk\"].splitlines())))\n", "    selected_line_index = c + number_of_added_lines - 2 # -2 to accounts for diff header and the selected line itself\n", "\n", "    harness_sample = ResearchPREditPromptInput(\n", "        path=str(file_path),\n", "        prefix=get_lines(cr_sample['oldf'], 0, selected_line_index),\n", "        selected_code=get_lines(cr_sample['oldf'], selected_line_index, selected_line_index + 1),\n", "        suffix=get_lines(cr_sample['oldf'], selected_line_index + 1, int(1e9)),\n", "        instruction=cr_sample['comment'],\n", "        prefix_begin=0,\n", "        suffix_end=len(cr_sample['oldf']),\n", "        retrieved_chunks=[],\n", "        generated_suggestion=hunk_to_suggestion(cr_sample['hunk']),\n", "        range_to_replace=LineRange(range_to_replace[0], range_to_replace[1])\n", "    )\n", "\n", "    repo_documents = convert_repository_to_documents(str(repo_path))\n", "\n", "    return harness_sample, repo_documents"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/home/<USER>/data/code_reviewer/Code_Refinement/ref-train.jsonl\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["150406it [00:15, 9683.40it/s] \n"]}], "source": ["cr_data = []\n", "with DATA_PATH.open() as f:\n", "    for line in tqdm(f):\n", "        cr_data.append(json.loads(line))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["OUT_DIR = Path(\"/home/<USER>/tmp/pr_edits_eval_data/likely_rag\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/37 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1215 total docs, skipping 89 of them (7%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  3%|▎         | 1/37 [00:02<01:25,  2.39s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 3046 total docs, skipping 779 of them (25%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  5%|▌         | 2/37 [00:05<01:40,  2.88s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 5072 total docs, skipping 1066 of them (21%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  8%|▊         | 3/37 [00:12<02:37,  4.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1803 total docs, skipping 450 of them (24%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 11%|█         | 4/37 [00:15<02:09,  3.92s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 596 total docs, skipping 126 of them (21%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 14%|█▎        | 5/37 [00:17<01:45,  3.30s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 4227 total docs, skipping 1100 of them (26%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 19%|█▉        | 7/37 [00:22<01:22,  2.76s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON>ple 6 109 failed\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 4223k    0 4223k    0     0  3058k      0 --:--:--  0:00:01 --:--:-- 4963k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 3147 total docs, skipping 916 of them (29%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 22%|██▏       | 8/37 [00:28<01:44,  3.59s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 3323k    0 3323k    0     0  2467k      0 --:--:--  0:00:01 --:--:-- 14.6M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 630 total docs, skipping 102 of them (16%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 24%|██▍       | 9/37 [00:31<01:43,  3.68s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 92.7M    0 92.7M    0     0  17.0M      0 --:--:--  0:00:05 --:--:-- 22.1M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 4758 total docs, skipping 1825 of them (38%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 27%|██▋       | 10/37 [00:42<02:34,  5.74s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 22.1M    0 22.1M    0     0  6259k      0 --:--:--  0:00:03 --:--:-- 8461k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 10773 total docs, skipping 1546 of them (14%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 30%|██▉       | 11/37 [00:52<03:07,  7.22s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100  114M    0  114M    0     0  6678k      0 --:--:--  0:00:17 --:--:-- 13.6M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 22250 total docs, skipping 3113 of them (13%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 12/37 [01:27<06:29, 15.57s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 30.1M    0 30.1M    0     0  12.8M      0 --:--:--  0:00:02 --:--:-- 17.5M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1980 total docs, skipping 250 of them (12%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 35%|███▌      | 13/37 [01:32<04:59, 12.49s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 16.7M    0 16.7M    0     0  8603k      0 --:--:--  0:00:01 --:--:-- 20.9M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 6082 total docs, skipping 1028 of them (16%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 38%|███▊      | 14/37 [01:40<04:11, 10.93s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100  575k    0  575k    0     0   487k      0 --:--:--  0:00:01 --:--:-- 2221k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 478 total docs, skipping 89 of them (18%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 41%|████      | 15/37 [01:43<03:12,  8.75s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 19.1M    0 19.1M    0     0  9843k      0 --:--:--  0:00:01 --:--:-- 24.4M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 2881 total docs, skipping 539 of them (18%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 43%|████▎     | 16/37 [01:49<02:42,  7.75s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 39.5M    0 39.5M    0     0  8967k      0 --:--:--  0:00:04 --:--:-- 10.6M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 10464 total docs, skipping 2918 of them (27%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 46%|████▌     | 17/37 [02:01<02:58,  8.94s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 1266k    0 1266k    0     0  1036k      0 --:--:--  0:00:01 --:--:-- 8017k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 777 total docs, skipping 113 of them (14%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 49%|████▊     | 18/37 [02:04<02:20,  7.40s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 76.2M    0 76.2M    0     0  11.0M      0 --:--:--  0:00:06 --:--:-- 13.9M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 54182 total docs, skipping 18147 of them (33%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 51%|█████▏    | 19/37 [02:39<04:39, 15.51s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 34.0M    0 34.0M    0     0  3069k      0 --:--:--  0:00:11 --:--:-- 3302k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 4623 total docs, skipping 1027 of them (22%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 54%|█████▍    | 20/37 [02:57<04:38, 16.40s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 8517k    0 8517k    0     0  4477k      0 --:--:--  0:00:01 --:--:-- 5716k\n", " 57%|█████▋    | 21/37 [03:00<03:16, 12.28s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Sample 20 276 failed\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 3328k    0 3328k    0     0  2570k      0 --:--:--  0:00:01 --:--:-- 5739k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1764 total docs, skipping 338 of them (19%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 59%|█████▉    | 22/37 [03:04<02:28,  9.93s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 22.8M    0 22.8M    0     0  7231k      0 --:--:--  0:00:03 --:--:-- 10.8M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 6038 total docs, skipping 1051 of them (17%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 62%|██████▏   | 23/37 [03:13<02:13,  9.55s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 33.8M    0 33.8M    0     0  10.9M      0 --:--:--  0:00:03 --:--:-- 13.6M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 6966 total docs, skipping 1933 of them (27%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 65%|██████▍   | 24/37 [03:21<01:59,  9.19s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100  580k    0  580k    0     0   472k      0 --:--:--  0:00:01 --:--:--  472k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 480 total docs, skipping 94 of them (19%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 68%|██████▊   | 25/37 [03:25<01:30,  7.55s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 44.4M    0 44.4M    0     0  11.7M      0 --:--:--  0:00:03 --:--:-- 13.7M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 2635 total docs, skipping 816 of them (30%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 70%|███████   | 26/37 [03:33<01:23,  7.55s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 46.6M    0 46.6M    0     0  7488k      0 --:--:--  0:00:06 --:--:-- 8833k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 19343 total docs, skipping 3482 of them (18%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 73%|███████▎  | 27/37 [03:51<01:46, 10.69s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 30.7M    0 30.7M    0     0  8070k      0 --:--:--  0:00:03 --:--:-- 10.5M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 8454 total docs, skipping 2642 of them (31%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 76%|███████▌  | 28/37 [04:01<01:36, 10.69s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 2787k    0 2787k    0     0  2074k      0 --:--:--  0:00:01 --:--:-- 26.6M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1016 total docs, skipping 191 of them (18%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 78%|███████▊  | 29/37 [04:06<01:09,  8.71s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 40.7M    0 40.7M    0     0  8102k      0 --:--:--  0:00:05 --:--:-- 10.2M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 15273 total docs, skipping 1727 of them (11%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 81%|████████  | 30/37 [04:20<01:13, 10.53s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 1065k    0 1065k    0     0   849k      0 --:--:--  0:00:01 --:--:--  849k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 543 total docs, skipping 51 of them (9%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 84%|████████▍ | 31/37 [04:24<00:51,  8.53s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 1216k    0 1216k    0     0   988k      0 --:--:--  0:00:01 --:--:-- 1733k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1395 total docs, skipping 286 of them (20%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 86%|████████▋ | 32/37 [04:28<00:36,  7.23s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 37.7M    0 37.7M    0     0  8319k      0 --:--:--  0:00:04 --:--:-- 10.4M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 9051 total docs, skipping 912 of them (10%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 89%|████████▉ | 33/37 [04:39<00:33,  8.37s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 29.5M    0 29.5M    0     0   9.7M      0 --:--:--  0:00:03 --:--:-- 16.3M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 9279 total docs, skipping 2999 of them (32%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 92%|█████████▏| 34/37 [04:50<00:27,  9.11s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 3915k    0 3915k    0     0  2792k      0 --:--:--  0:00:01 --:--:-- 4232k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 591 total docs, skipping 106 of them (17%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 95%|█████████▍| 35/37 [04:54<00:15,  7.58s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100  130M    0  130M    0     0  7663k      0 --:--:--  0:00:17 --:--:-- 8515k\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 27559 total docs, skipping 4095 of them (14%)\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 97%|█████████▋| 36/37 [05:28<00:15, 15.58s/it]  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0\n", "100 13.0M    0 13.0M    0     0  5742k      0 --:--:--  0:00:02 --:--:-- 11.1M\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 8503 total docs, skipping 1323 of them (15%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 37/37 [05:40<00:00,  9.21s/it]\n"]}], "source": ["all_harness_samples = []\n", "all_metas = []\n", "\n", "for i, sample_index in tqdm(list(enumerate(RAG_IDS))):\n", "    cr_sample = cr_data[sample_index]\n", "    try:\n", "        harness_sample, repo_documents = convert_to_harness_sample(cr_sample)\n", "    except:\n", "        print(f\"Sample {i} {sample_index} failed\")\n", "        continue\n", "    # check_sample(harness_sample, cr_sample)\n", "    # if input() == \"b\":\n", "    #     break\n", "\n", "    repo_name = cr_sample[\"repo\"].replace(\"/\", \"___\")\n", "    sample_id = f\"{repo_name}___{cr_sample['ghid']}\"\n", "    sample_path = OUT_DIR / f\"sample_{sample_id}.json\"\n", "    meta_path = OUT_DIR / f\"meta_{sample_id}.json\"\n", "    retrieval_db_path = OUT_DIR / f\"retrieval_db_{sample_id}.jsonl\"\n", "\n", "    meta = {\n", "        \"category\": \"likely_rag\",\n", "        \"input_commit_url\": get_commit_urls(cr_sample)[0]\n", "    }\n", "\n", "    all_harness_samples.append(harness_sample)\n", "    all_metas.append(meta)\n", "\n", "    with sample_path.open(\"w\") as f:\n", "        json.dump(asdict(harness_sample), f, indent=2)\n", "\n", "    with meta_path.open(\"w\") as f:\n", "        json.dump(meta, f, indent=2)\n", "\n", "    with retrieval_db_path.open(\"w\") as f:\n", "        for doc in repo_documents:\n", "            f.write(json.dumps(asdict(doc)))\n", "            f.write(\"\\n\")\n", "\n", "    time.sleep(2)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For sample 1, selected line lies outside modified area \n", "For sample 14, selected line lies outside modified area \n", "For sample 23, selected line lies outside modified area \n"]}], "source": ["def get_diff_html(input_code, output_code, comment):\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "\n", "    diff_html = diff_obj.make_file(\n", "        input_code.splitlines(),\n", "        output_code.splitlines()\n", "    )\n", "\n", "    comment_html = f\"<li><strong>{comment}</strong></li>\"\n", "\n", "    html = f\"\"\"\n", "    <h4>Comment: {comment_html}</h4>\n", "    <div id=\"code-diff\">{diff_html}</div>\n", "\"\"\"\n", "    return html\n", "\n", "\n", "MAIN_HTML = \"\"\n", "for i, (harness_sample, meta) in enumerate(zip(all_harness_samples, all_metas)):\n", "    original_file_content = harness_sample.prefix + harness_sample.selected_code + harness_sample.suffix\n", "\n", "    input_code = get_lines(original_file_content, max(harness_sample.range_to_replace.start - 5, 0),  harness_sample.range_to_replace.stop + 5)\n", "    start_line = max(harness_sample.range_to_replace.start - 5, 0)\n", "    cur_selected_line_index = len(harness_sample.prefix.splitlines()) - start_line\n", "    input_lines = list(map(lambda l: f\"  {l}\", input_code.splitlines(True)))\n", "\n", "    if cur_selected_line_index < 0 or cur_selected_line_index >= len(input_lines):\n", "        print(f\"For sample {i}, selected line lies outside modified area \")\n", "        continue\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<h2>Code sample {i}</h2><hr>\"\n", "    # MAIN_HTML += f\"<h5>PR link: {meta['input_commit_url']}</h2><hr>\"\n", "    MAIN_HTML += f\"<a href=\\\"{meta['input_commit_url']}\\\">PR link</a>\"\n", "\n", "    input_lines[cur_selected_line_index] = f\"|>{input_lines[cur_selected_line_index][2:]}\"\n", "    marked_input_code = \"\".join(input_lines)\n", "\n", "    output_code = get_lines(original_file_content, max(harness_sample.range_to_replace.start - 5, 0), harness_sample.range_to_replace.start) +\\\n", "                harness_sample.generated_suggestion +\\\n", "                get_lines(original_file_content, harness_sample.range_to_replace.stop, harness_sample.range_to_replace.stop + 5)\n", "    output_lines = list(map(lambda l: f\"  {l}\", output_code.splitlines(True)))\n", "    marked_output_code = \"\".join(output_lines)\n", "\n", "    cur_diff = get_diff_html(marked_input_code, marked_output_code, harness_sample.instruction)\n", "    MAIN_HTML += f\"{cur_diff}<hr>\"\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "\n", "with open('./test_feb_12_likely_rag.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Rag\n", "RAG_IDS = [\n", "0,\n", "12,\n", "27,\n", "29,\n", "74,\n", "92,\n", "109,\n", "117,\n", "118,\n", "119,\n", "126,\n", "129,\n", "157,\n", "180,\n", "188,\n", "202,\n", "229,\n", "233,\n", "252,\n", "264,\n", "276,\n", "296,\n", "300,\n", "317,\n", "320,\n", "321,\n", "323,\n", "333,\n", "342,\n", "349,\n", "357,\n", "374,\n", "405,\n", "406,\n", "408,\n", "409,\n", "427,\n", "]\n", "\n", "# No-Rag\n", "NON_RAG_IDS = [\n", "1,\n", "2,\n", "3,\n", "7,\n", "10,\n", "13,\n", "15,\n", "17,\n", "19,\n", "20,\n", "21,\n", "22,\n", "23,\n", "24,\n", "25,\n", "26,\n", "28,\n", "31,\n", "32,\n", "35,\n", "40,\n", "56,\n", "84,\n", "86,\n", "89,\n", "106,\n", "113,\n", "125,\n", "128,\n", "130,\n", "132,\n", "135,\n", "148,\n", "149,\n", "152,\n", "154,\n", "193,\n", "198,\n", "199,\n", "204,\n", "212,\n", "216,\n", "217,\n", "219,\n", "222,\n", "225,\n", "231,\n", "267,\n", "277,\n", "281,\n", "284,\n", "303,\n", "309,\n", "324,\n", "345,\n", "348,\n", "350,\n", "355,\n", "366,\n", "367,\n", "399,\n", "411,\n", "414,\n", "416,\n", "419,\n", "429,\n", "437,\n", "443,\n", "444,\n", "446,\n", "479,\n", "]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = f\"\"\"\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
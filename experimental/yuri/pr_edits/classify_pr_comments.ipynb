{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "import re\n", "import subprocess\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/home/<USER>/tmp/test_mar12_v3\")"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["PROMPT = \"\"\"\n", "Here is code hunk from a GitHub PR:\n", "```\n", "{code_chunk}\n", "```\n", "\n", "And here is a comment that was left by a reviewer:\n", "```\n", "{comment_body}\n", "```\n", "Comment was attached to the lines marked with '|>'\n", "\n", "Your task is to decide whether this comment asks for any change to the provided code hunk.\n", "Be careful, because comment might ask for a change implicitely and it still countes as an ask for change.\n", "If the comment asks for change which doesn't affect the provided code hunk, then you should return false.\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze the provided code chunk and comment.\n", "2. Describe your thinking process on how to complete this task.\n", "3. Explicitly write YES (if comment asks for change) or NO (if comment doesn't ask for change).\n", "\"\"\"\n", "\n", "\n", "EXTRACT_PROMPT = \"\"\"Return the result in a JSON with a single key:\n", "- result\n", "\n", "result should be true if comment asks for change, false otherwise.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def read_data(input_dir: Path):\n", "    data = []\n", "    for file in input_dir.glob(\"*.json\"):\n", "        with file.open() as f:\n", "            data.append((json.load(f), file.name))\n", "    return data\n", "\n", "\n", "def hunk_to_code(hunk, ignore_symbol):\n", "    result = []\n", "    for i, line in enumerate(hunk.splitlines(True)):\n", "        if i == 0: # For hunk header\n", "            continue\n", "        if line.startswith(ignore_symbol):\n", "            continue\n", "        line = line[1:]\n", "        result.append(line)\n", "\n", "    return \"\".join(result)\n", "\n", "def parse_all_diff_numbers(diff_hunk: str):\n", "    match = re.search(r'@@ -(\\d+),(\\d+) \\+(\\d+),(\\d+) @@', diff_hunk)\n", "    if match:\n", "        return (int(match.group(1)), int(match.group(2)),\n", "                int(match.group(3)), int(match.group(4)))\n", "    assert False\n", "\n", "\n", "def mark_lines(code: str, line_range: list[int]) -> str:\n", "    result = []\n", "    for i, line in enumerate(code.splitlines(True)):\n", "        if line_range[0] <= i <= line_range[1]:\n", "            result.append(f\"|>{line}\")\n", "        else:\n", "            result.append(line)\n", "    return \"\".join(result)\n", "\n", "def get_marked_code(sample):\n", "    if sample[\"comment_full_info\"][\"start_side\"] == \"LEFT\":\n", "        return None\n", "    if sample[\"comment_full_info\"][\"side\"] == \"LEFT\":\n", "        return None\n", "\n", "    cur_code = hunk_to_code(sample[\"full_diff_hunk\"], \"-\")\n", "\n", "    last_commented_line = sample[\"comment_full_info\"][\"original_line\"]\n", "    first_commented_line = sample[\"comment_full_info\"][\"original_start_line\"]\n", "    if first_commented_line is None:\n", "        first_commented_line = last_commented_line\n", "        \n", "    _, _, first_plus_line, _ = parse_all_diff_numbers(sample[\"full_diff_hunk\"])\n", "\n", "    first_commented_line -= first_plus_line\n", "    last_commented_line -= first_plus_line\n", "\n", "    marked_code = mark_lines(cur_code, [first_commented_line, last_commented_line])\n", "\n", "    return marked_code\n", "\n", "\n", "def classify_sample(comment_sample, gpt):\n", "    marked_code = get_marked_code(comment_sample)\n", "\n", "    prompt = PROMPT.format(code_chunk=marked_code, comment_body=comment_sample[\"comment\"][\"body\"])\n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "\n", "    messages.append({\"role\": \"assistant\", \"content\": gpt4_response})\n", "    messages.append({\"role\": \"user\", \"content\": EXTRACT_PROMPT})\n", "    extracted_response = gpt(messages, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0)\n", "\n", "    extracted_response[\"main_prompt\"] = prompt\n", "\n", "    return extracted_response"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["data = read_data(DATA_PATH)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["PosixPath('/home/<USER>/tmp/test_mar12_v3')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["DATA_PATH"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(data)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'comment': {'id': '*********',\n", "   'path': 'sdk/storage/azure-storage-blob-changefeed/azure/storage/blob/changefeed/_change_feed_client.py',\n", "   'diff_hunk': '@@ -24,9 +24,11 @@ class ChangeFeedClient(object):  # pylint: disable=too-many-public-methods\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.',\n", "   'body': 'Shouldn\\'t this say \"a ValueError error will be raised\" rather than \"...will take priority\"? ',\n", "   'commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'original_commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'base_sha': '80c3919700ada1d63998bbe446ea80a0f5eda026',\n", "   'head_sha': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'user': {'login': 'annatisch',\n", "    'id': '8689453',\n", "    'type': 'User',\n", "    'site_admin': 'false'},\n", "   'updated_at': '2021-01-12T20:46:15Z'},\n", "  'comment_url': 'https://github.com/Azure/azure-sdk-for-python/pull/16043/files#r*********',\n", "  'all_considered_hunks': ['diff --git a/tmp/tmp0z3ydo1b b/tmp/tmpqs_5ffko\\nindex *********..2152b2050 100644\\n--- a/tmp/tmp0z3ydo1b\\n+++ b/tmp/tmpqs_5ffko\\n',\n", "   '@@ -24,9 +24,11 @@\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.\\n     :keyword str secondary_hostname:\\n         The hostname of the secondary endpoint.\\n \\n',\n", "   '@@ -60,7 +62,8 @@\\n         :param credential:\\n             The credentials with which to authenticate. This is optional if the\\n             account URL already has a SAS token, or the connection string already has shared\\n-            access key values. The value can be a SAS token string, an account shared access\\n+            access key values. The value can be a SAS token string,\\n+            an instance of a AzureSasCredential from azure.core.credentials, an account shared access\\n             key, or an instance of a TokenCredentials class from azure.identity.\\n             Credentials provided here will take precedence over those in the connection string.\\n         :returns: A change feed client.\\n'],\n", "  'comment_full_info': {'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********',\n", "   'pull_request_review_id': *********,\n", "   'id': *********,\n", "   'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1NjA4MDY5OA==',\n", "   'diff_hunk': '@@ -24,9 +24,11 @@ class ChangeFeedClient(object):  # pylint: disable=too-many-public-methods\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.',\n", "   'path': 'sdk/storage/azure-storage-blob-changefeed/azure/storage/blob/changefeed/_change_feed_client.py',\n", "   'commit_id': '5145fdc1712f9fab985b7823da3667de909c7ff1',\n", "   'original_commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'user': {'login': 'annatisch',\n", "    'id': 8689453,\n", "    'node_id': 'MDQ6VXNlcjg2ODk0NTM=',\n", "    'avatar_url': 'https://avatars.githubusercontent.com/u/8689453?v=4',\n", "    'gravatar_id': '',\n", "    'url': 'https://api.github.com/users/annatisch',\n", "    'html_url': 'https://github.com/annatisch',\n", "    'followers_url': 'https://api.github.com/users/annatisch/followers',\n", "    'following_url': 'https://api.github.com/users/annatisch/following{/other_user}',\n", "    'gists_url': 'https://api.github.com/users/annatisch/gists{/gist_id}',\n", "    'starred_url': 'https://api.github.com/users/annatisch/starred{/owner}{/repo}',\n", "    'subscriptions_url': 'https://api.github.com/users/annatisch/subscriptions',\n", "    'organizations_url': 'https://api.github.com/users/annatisch/orgs',\n", "    'repos_url': 'https://api.github.com/users/annatisch/repos',\n", "    'events_url': 'https://api.github.com/users/annatisch/events{/privacy}',\n", "    'received_events_url': 'https://api.github.com/users/annatisch/received_events',\n", "    'type': 'User',\n", "    'site_admin': <PERSON><PERSON><PERSON>},\n", "   'body': 'Shouldn\\'t this say \"a ValueError error will be raised\" rather than \"...will take priority\"? ',\n", "   'created_at': '2021-01-12T20:46:15Z',\n", "   'updated_at': '2021-01-12T20:56:32Z',\n", "   'html_url': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********',\n", "   'pull_request_url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043',\n", "   'author_association': 'MEMBER',\n", "   '_links': {'self': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********'},\n", "    'html': {'href': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********'},\n", "    'pull_request': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043'}},\n", "   'reactions': {'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********/reactions',\n", "    'total_count': 0,\n", "    '+1': 0,\n", "    '-1': 0,\n", "    'laugh': 0,\n", "    'hooray': 0,\n", "    'confused': 0,\n", "    'heart': 0,\n", "    'rocket': 0,\n", "    'eyes': 0},\n", "   'start_line': None,\n", "   'original_start_line': None,\n", "   'start_side': None,\n", "   'line': None,\n", "   'original_line': 31,\n", "   'side': 'RIGHT',\n", "   'original_position': 10,\n", "   'position': None,\n", "   'subject_type': 'line'},\n", "  'full_diff_hunk': '@@ -24,9 +24,11 @@\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.\\n     :keyword str secondary_hostname:\\n         The hostname of the secondary endpoint.\\n \\n'},\n", " {'comment': {'id': '*********',\n", "   'path': 'sdk/storage/azure-storage-blob-changefeed/azure/storage/blob/changefeed/_change_feed_client.py',\n", "   'diff_hunk': '@@ -24,9 +24,11 @@ class ChangeFeedClient(object):  # pylint: disable=too-many-public-methods\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.',\n", "   'body': 'Shouldn\\'t this say \"a ValueError error will be raised\" rather than \"...will take priority\"? ',\n", "   'commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'original_commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'base_sha': '80c3919700ada1d63998bbe446ea80a0f5eda026',\n", "   'head_sha': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'user': {'login': 'annatisch',\n", "    'id': '8689453',\n", "    'type': 'User',\n", "    'site_admin': 'false'},\n", "   'updated_at': '2021-01-12T20:46:15Z'},\n", "  'comment_url': 'https://github.com/Azure/azure-sdk-for-python/pull/16043/files#r*********',\n", "  'all_considered_hunks': ['diff --git a/tmp/tmpycjdo3lp b/tmp/tmp1h093a3i\\nindex *********..2152b2050 100644\\n--- a/tmp/tmpycjdo3lp\\n+++ b/tmp/tmp1h093a3i\\n',\n", "   '@@ -24,9 +24,11 @@\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.\\n     :keyword str secondary_hostname:\\n         The hostname of the secondary endpoint.\\n \\n',\n", "   '@@ -60,7 +62,8 @@\\n         :param credential:\\n             The credentials with which to authenticate. This is optional if the\\n             account URL already has a SAS token, or the connection string already has shared\\n-            access key values. The value can be a SAS token string, an account shared access\\n+            access key values. The value can be a SAS token string,\\n+            an instance of a AzureSasCredential from azure.core.credentials, an account shared access\\n             key, or an instance of a TokenCredentials class from azure.identity.\\n             Credentials provided here will take precedence over those in the connection string.\\n         :returns: A change feed client.\\n'],\n", "  'comment_full_info': {'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********',\n", "   'pull_request_review_id': *********,\n", "   'id': *********,\n", "   'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1NjA4MDY5OA==',\n", "   'diff_hunk': '@@ -24,9 +24,11 @@ class ChangeFeedClient(object):  # pylint: disable=too-many-public-methods\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.',\n", "   'path': 'sdk/storage/azure-storage-blob-changefeed/azure/storage/blob/changefeed/_change_feed_client.py',\n", "   'commit_id': '5145fdc1712f9fab985b7823da3667de909c7ff1',\n", "   'original_commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "   'user': {'login': 'annatisch',\n", "    'id': 8689453,\n", "    'node_id': 'MDQ6VXNlcjg2ODk0NTM=',\n", "    'avatar_url': 'https://avatars.githubusercontent.com/u/8689453?v=4',\n", "    'gravatar_id': '',\n", "    'url': 'https://api.github.com/users/annatisch',\n", "    'html_url': 'https://github.com/annatisch',\n", "    'followers_url': 'https://api.github.com/users/annatisch/followers',\n", "    'following_url': 'https://api.github.com/users/annatisch/following{/other_user}',\n", "    'gists_url': 'https://api.github.com/users/annatisch/gists{/gist_id}',\n", "    'starred_url': 'https://api.github.com/users/annatisch/starred{/owner}{/repo}',\n", "    'subscriptions_url': 'https://api.github.com/users/annatisch/subscriptions',\n", "    'organizations_url': 'https://api.github.com/users/annatisch/orgs',\n", "    'repos_url': 'https://api.github.com/users/annatisch/repos',\n", "    'events_url': 'https://api.github.com/users/annatisch/events{/privacy}',\n", "    'received_events_url': 'https://api.github.com/users/annatisch/received_events',\n", "    'type': 'User',\n", "    'site_admin': <PERSON><PERSON><PERSON>},\n", "   'body': 'Shouldn\\'t this say \"a ValueError error will be raised\" rather than \"...will take priority\"? ',\n", "   'created_at': '2021-01-12T20:46:15Z',\n", "   'updated_at': '2021-01-12T20:56:32Z',\n", "   'html_url': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********',\n", "   'pull_request_url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043',\n", "   'author_association': 'MEMBER',\n", "   '_links': {'self': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********'},\n", "    'html': {'href': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********'},\n", "    'pull_request': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043'}},\n", "   'reactions': {'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********/reactions',\n", "    'total_count': 0,\n", "    '+1': 0,\n", "    '-1': 0,\n", "    'laugh': 0,\n", "    'hooray': 0,\n", "    'confused': 0,\n", "    'heart': 0,\n", "    'rocket': 0,\n", "    'eyes': 0},\n", "   'start_line': None,\n", "   'original_start_line': None,\n", "   'start_side': None,\n", "   'line': None,\n", "   'original_line': 31,\n", "   'side': 'RIGHT',\n", "   'original_position': 10,\n", "   'position': None,\n", "   'subject_type': 'line'},\n", "  'full_diff_hunk': '@@ -24,9 +24,11 @@\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.\\n     :keyword str secondary_hostname:\\n         The hostname of the secondary endpoint.\\n \\n'}]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data[0][0]"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'comment': {'id': '*********',\n", "  'path': 'shardingsphere-features/shardingsphere-readwrite-splitting/shardingsphere-readwrite-splitting-distsql/shardingsphere-readwrite-splitting-distsql-handler/src/main/java/org/apache/shardingsphere/readwritesplitting/distsql/handler/query/ReadwriteSplittingRuleQueryResultSet.java',\n", "  'diff_hunk': '@@ -64,9 +64,9 @@ public boolean next() {\\n     @Override\\n     public Collection<Object> getRowData() {\\n         ReadwriteSplittingDataSourceRuleConfiguration ruleConfig = data.next();\\n+        ShardingSphereAlgorithmConfiguration configuration = loadBalancers.get(ruleConfig.getLoadBalancerName());\\n         return Arrays.asList(ruleConfig.getName(), ruleConfig.getAutoAwareDataSourceName(), ruleConfig.getWriteDataSourceName(), Joiner.on(\",\").join(ruleConfig.getReadDataSourceNames()),\\n-                null == loadBalancers.get(ruleConfig.getLoadBalancerName()) ? null : loadBalancers.get(ruleConfig.getLoadBalancerName()).getType(),\\n-                PropertiesConverter.convert(loadBalancers.get(ruleConfig.getLoadBalancerName()).getProps()));\\n+                null == configuration ? null : configuration.getType(), null == configuration ? null : PropertiesConverter.convert(configuration.getProps()));',\n", "  'body': 'more than 1 conditional operator in a clause is not a good practice .',\n", "  'commit_id': '445987633ca584ba253530ee9124eeb98154a121',\n", "  'original_commit_id': '445987633ca584ba253530ee9124eeb98154a121',\n", "  'base_sha': 'd39fd2c4d1f21363c13c791dcb4df48808029da0',\n", "  'head_sha': '445987633ca584ba253530ee9124eeb98154a121',\n", "  'user': {'login': 'taojintianxia',\n", "   'id': '4112856',\n", "   'type': 'User',\n", "   'site_admin': 'false'},\n", "  'updated_at': '2021-08-17T03:12:02Z'},\n", " 'comment_url': 'https://github.com/apache/shardingsphere/pull/11853/files#r*********',\n", " 'all_considered_hunks': ['diff --git a/tmp/tmpcclftq5w b/tmp/tmpg05a2if1\\nindex b73dbff4e..9f770e1f7 100644\\n--- a/tmp/tmpcclftq5w\\n+++ b/tmp/tmpg05a2if1\\n',\n", "  '@@ -19,9 +19,9 @@\\n \\n import com.google.common.base.Joiner;\\n import org.apache.shardingsphere.infra.config.algorithm.ShardingSphereAlgorithmConfiguration;\\n+import org.apache.shardingsphere.infra.distsql.query.DistSQLResultSet;\\n import org.apache.shardingsphere.infra.metadata.ShardingSphereMetaData;\\n import org.apache.shardingsphere.infra.properties.PropertiesConverter;\\n-import org.apache.shardingsphere.infra.distsql.query.DistSQLResultSet;\\n import org.apache.shardingsphere.readwritesplitting.api.ReadwriteSplittingRuleConfiguration;\\n import org.apache.shardingsphere.readwritesplitting.api.rule.ReadwriteSplittingDataSourceRuleConfiguration;\\n import org.apache.shardingsphere.readwritesplitting.distsql.parser.statement.ShowReadwriteSplittingRulesStatement;\\n',\n", "  '@@ -64,9 +64,9 @@\\n     @Override\\n     public Collection<Object> getRowData() {\\n         ReadwriteSplittingDataSourceRuleConfiguration ruleConfig = data.next();\\n+        ShardingSphereAlgorithmConfiguration configuration = loadBalancers.get(ruleConfig.getLoadBalancerName());\\n         return Arrays.asList(ruleConfig.getName(), ruleConfig.getAutoAwareDataSourceName(), ruleConfig.getWriteDataSourceName(), Joiner.on(\",\").join(ruleConfig.getReadDataSourceNames()),\\n-                null == loadBalancers.get(ruleConfig.getLoadBalancerName()) ? null : loadBalancers.get(ruleConfig.getLoadBalancerName()).getType(),\\n-                PropertiesConverter.convert(loadBalancers.get(ruleConfig.getLoadBalancerName()).getProps()));\\n+                null == configuration ? null : configuration.getType(), null == configuration ? null : PropertiesConverter.convert(configuration.getProps()));\\n     }\\n     \\n     @Override\\n'],\n", " 'comment_full_info': {'url': 'https://api.github.com/repos/apache/shardingsphere/pulls/comments/*********',\n", "  'pull_request_review_id': 731296326,\n", "  'id': *********,\n", "  'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDY5MDAwMTg2OA==',\n", "  'diff_hunk': '@@ -64,9 +64,9 @@ public boolean next() {\\n     @Override\\n     public Collection<Object> getRowData() {\\n         ReadwriteSplittingDataSourceRuleConfiguration ruleConfig = data.next();\\n+        ShardingSphereAlgorithmConfiguration configuration = loadBalancers.get(ruleConfig.getLoadBalancerName());\\n         return Arrays.asList(ruleConfig.getName(), ruleConfig.getAutoAwareDataSourceName(), ruleConfig.getWriteDataSourceName(), Joiner.on(\",\").join(ruleConfig.getReadDataSourceNames()),\\n-                null == loadBalancers.get(ruleConfig.getLoadBalancerName()) ? null : loadBalancers.get(ruleConfig.getLoadBalancerName()).getType(),\\n-                PropertiesConverter.convert(loadBalancers.get(ruleConfig.getLoadBalancerName()).getProps()));\\n+                null == configuration ? null : configuration.getType(), null == configuration ? null : PropertiesConverter.convert(configuration.getProps()));',\n", "  'path': 'shardingsphere-features/shardingsphere-readwrite-splitting/shardingsphere-readwrite-splitting-distsql/shardingsphere-readwrite-splitting-distsql-handler/src/main/java/org/apache/shardingsphere/readwritesplitting/distsql/handler/query/ReadwriteSplittingRuleQueryResultSet.java',\n", "  'commit_id': '2b717f0d148c5877b6b98e25bb6d6f683fdbc68e',\n", "  'original_commit_id': '445987633ca584ba253530ee9124eeb98154a121',\n", "  'user': {'login': 'taojintianxia',\n", "   'id': 4112856,\n", "   'node_id': 'MDQ6VXNlcjQxMTI4NTY=',\n", "   'avatar_url': 'https://avatars.githubusercontent.com/u/4112856?v=4',\n", "   'gravatar_id': '',\n", "   'url': 'https://api.github.com/users/taojintianxia',\n", "   'html_url': 'https://github.com/taojintianxia',\n", "   'followers_url': 'https://api.github.com/users/taojintianxia/followers',\n", "   'following_url': 'https://api.github.com/users/taojintianxia/following{/other_user}',\n", "   'gists_url': 'https://api.github.com/users/taojintianxia/gists{/gist_id}',\n", "   'starred_url': 'https://api.github.com/users/taojintianxia/starred{/owner}{/repo}',\n", "   'subscriptions_url': 'https://api.github.com/users/taojintianxia/subscriptions',\n", "   'organizations_url': 'https://api.github.com/users/taojintianxia/orgs',\n", "   'repos_url': 'https://api.github.com/users/taojintianxia/repos',\n", "   'events_url': 'https://api.github.com/users/taojintianxia/events{/privacy}',\n", "   'received_events_url': 'https://api.github.com/users/taojintianxia/received_events',\n", "   'type': 'User',\n", "   'site_admin': <PERSON><PERSON><PERSON>},\n", "  'body': 'more than 1 conditional operator in a clause is not a good practice .',\n", "  'created_at': '2021-08-17T03:12:02Z',\n", "  'updated_at': '2021-08-17T03:12:02Z',\n", "  'html_url': 'https://github.com/apache/shardingsphere/pull/11853#discussion_r*********',\n", "  'pull_request_url': 'https://api.github.com/repos/apache/shardingsphere/pulls/11853',\n", "  'author_association': 'CONTRIBUTOR',\n", "  '_links': {'self': {'href': 'https://api.github.com/repos/apache/shardingsphere/pulls/comments/*********'},\n", "   'html': {'href': 'https://github.com/apache/shardingsphere/pull/11853#discussion_r*********'},\n", "   'pull_request': {'href': 'https://api.github.com/repos/apache/shardingsphere/pulls/11853'}},\n", "  'reactions': {'url': 'https://api.github.com/repos/apache/shardingsphere/pulls/comments/*********/reactions',\n", "   'total_count': 0,\n", "   '+1': 0,\n", "   '-1': 0,\n", "   'laugh': 0,\n", "   'hooray': 0,\n", "   'confused': 0,\n", "   'heart': 0,\n", "   'rocket': 0,\n", "   'eyes': 0},\n", "  'start_line': None,\n", "  'original_start_line': None,\n", "  'start_side': None,\n", "  'line': None,\n", "  'original_line': 69,\n", "  'side': 'RIGHT',\n", "  'original_position': 19,\n", "  'position': None,\n", "  'subject_type': 'line'},\n", " 'full_diff_hunk': '@@ -64,9 +64,9 @@\\n     @Override\\n     public Collection<Object> getRowData() {\\n         ReadwriteSplittingDataSourceRuleConfiguration ruleConfig = data.next();\\n+        ShardingSphereAlgorithmConfiguration configuration = loadBalancers.get(ruleConfig.getLoadBalancerName());\\n         return Arrays.asList(ruleConfig.getName(), ruleConfig.getAutoAwareDataSourceName(), ruleConfig.getWriteDataSourceName(), Joiner.on(\",\").join(ruleConfig.getReadDataSourceNames()),\\n-                null == loadBalancers.get(ruleConfig.getLoadBalancerName()) ? null : loadBalancers.get(ruleConfig.getLoadBalancerName()).getType(),\\n-                PropertiesConverter.convert(loadBalancers.get(ruleConfig.getLoadBalancerName()).getProps()));\\n+                null == configuration ? null : configuration.getType(), null == configuration ? null : PropertiesConverter.convert(configuration.getProps()));\\n     }\\n     \\n     @Override\\n'}"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["comments, filename = data[3]\n", "comment = comments[0]\n", "comment"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/plain": ["'more than 1 conditional operator in a clause is not a good practice .'"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["comment[\"comment\"][\"body\"]"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@@ -64,9 +64,9 @@\n", "     @Override\n", "     public Collection<Object> getRowData() {\n", "         ReadwriteSplittingDataSourceRuleConfiguration ruleConfig = data.next();\n", "+        ShardingSphereAlgorithmConfiguration configuration = loadBalancers.get(ruleConfig.getLoadBalancerName());\n", "         return Arrays.asList(ruleConfig.getName(), ruleConfig.getAutoAwareDataSourceName(), ruleConfig.getWriteDataSourceName(), Joiner.on(\",\").join(ruleConfig.getReadDataSourceNames()),\n", "-                null == loadBalancers.get(ruleConfig.getLoadBalancerName()) ? null : loadBalancers.get(ruleConfig.getLoadBalancerName()).getType(),\n", "-                PropertiesConverter.convert(loadBalancers.get(ruleConfig.getLoadBalancerName()).getProps()));\n", "+                null == configuration ? null : configuration.getType(), null == configuration ? null : PropertiesConverter.convert(configuration.getProps()));\n", "     }\n", "     \n", "     @Override\n", "\n"]}], "source": ["print(comment[\"full_diff_hunk\"])"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "\n", "\n", "gpt = GptWrapper()"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["_a = classify_sample(comment, gpt)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Here is code hunk from a GitHub PR:\n", "```\n", "    @Override\n", "    public Collection<Object> getRowData() {\n", "        ReadwriteSplittingDataSourceRuleConfiguration ruleConfig = data.next();\n", "        ShardingSphereAlgorithmConfiguration configuration = loadBalancers.get(ruleConfig.getLoadBalancerName());\n", "        return Arrays.asList(ruleConfig.getName(), ruleConfig.getAutoAwareDataSourceName(), ruleConfig.getWriteDataSourceName(), Joiner.on(\",\").join(ruleConfig.getReadDataSourceNames()),\n", "|>                null == configuration ? null : configuration.getType(), null == configuration ? null : PropertiesConverter.convert(configuration.getProps()));\n", "    }\n", "    \n", "    @Override\n", "\n", "```\n", "\n", "And here is a comment that was left by a reviewer:\n", "```\n", "more than 1 conditional operator in a clause is not a good practice .\n", "```\n", "Comment was attached to the lines marked with '|>'\n", "\n", "Your task is to decide whether this comment asks for any change to the provided code hunk.\n", "Be careful, because comment might ask for a change implicitely and it still countes as an ask for change.\n", "If the comment asks for change which doesn't affect the provided code hunk, then you should return false.\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze the provided code chunk and comment.\n", "2. Describe your thinking process on how to complete this task.\n", "3. Explicitly write YES (if comment asks for change) or NO (if comment doesn't ask for change).\n", "\n"]}], "source": ["print(_a[\"main_prompt\"])"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["_a[\"result\"]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["_c = get_marked_code(comment)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["        The URI to the storage account.\n", "    :param credential:\n", "        The credentials with which to authenticate. This is optional if the\n", "        account URL already has a SAS token. The value can be a SAS token string,\n", "        an instance of a AzureSasCredential from azure.core.credentials, an account\n", "        shared access key, or an instance of a TokenCredentials class from azure.identity.\n", "        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\n", "|>        if the URL already has a SAS token, specifying an explicit credential will take priority.\n", "    :keyword str secondary_hostname:\n", "        The hostname of the secondary endpoint.\n", "\n", "\n"]}], "source": ["print(_c)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********',\n", " 'pull_request_review_id': *********,\n", " 'id': *********,\n", " 'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1NjA4MDY5OA==',\n", " 'diff_hunk': '@@ -24,9 +24,11 @@ class ChangeFeedClient(object):  # pylint: disable=too-many-public-methods\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.',\n", " 'path': 'sdk/storage/azure-storage-blob-changefeed/azure/storage/blob/changefeed/_change_feed_client.py',\n", " 'commit_id': '5145fdc1712f9fab985b7823da3667de909c7ff1',\n", " 'original_commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", " 'user': {'login': 'annatisch',\n", "  'id': 8689453,\n", "  'node_id': 'MDQ6VXNlcjg2ODk0NTM=',\n", "  'avatar_url': 'https://avatars.githubusercontent.com/u/8689453?v=4',\n", "  'gravatar_id': '',\n", "  'url': 'https://api.github.com/users/annatisch',\n", "  'html_url': 'https://github.com/annatisch',\n", "  'followers_url': 'https://api.github.com/users/annatisch/followers',\n", "  'following_url': 'https://api.github.com/users/annatisch/following{/other_user}',\n", "  'gists_url': 'https://api.github.com/users/annatisch/gists{/gist_id}',\n", "  'starred_url': 'https://api.github.com/users/annatisch/starred{/owner}{/repo}',\n", "  'subscriptions_url': 'https://api.github.com/users/annatisch/subscriptions',\n", "  'organizations_url': 'https://api.github.com/users/annatisch/orgs',\n", "  'repos_url': 'https://api.github.com/users/annatisch/repos',\n", "  'events_url': 'https://api.github.com/users/annatisch/events{/privacy}',\n", "  'received_events_url': 'https://api.github.com/users/annatisch/received_events',\n", "  'type': 'User',\n", "  'site_admin': <PERSON><PERSON><PERSON>},\n", " 'body': 'Shouldn\\'t this say \"a ValueError error will be raised\" rather than \"...will take priority\"? ',\n", " 'created_at': '2021-01-12T20:46:15Z',\n", " 'updated_at': '2021-01-12T20:56:32Z',\n", " 'html_url': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********',\n", " 'pull_request_url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043',\n", " 'author_association': 'MEMBER',\n", " '_links': {'self': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********'},\n", "  'html': {'href': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********'},\n", "  'pull_request': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043'}},\n", " 'reactions': {'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********/reactions',\n", "  'total_count': 0,\n", "  '+1': 0,\n", "  '-1': 0,\n", "  'laugh': 0,\n", "  'hooray': 0,\n", "  'confused': 0,\n", "  'heart': 0,\n", "  'rocket': 0,\n", "  'eyes': 0},\n", " 'start_line': None,\n", " 'original_start_line': None,\n", " 'start_side': None,\n", " 'line': None,\n", " 'original_line': 31,\n", " 'side': 'RIGHT',\n", " 'original_position': 10,\n", " 'position': None,\n", " 'subject_type': 'line'}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["comment[\"comment_full_info\"]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'comment': {'id': '*********',\n", "  'path': 'sdk/storage/azure-storage-blob-changefeed/azure/storage/blob/changefeed/_change_feed_client.py',\n", "  'diff_hunk': '@@ -24,9 +24,11 @@ class ChangeFeedClient(object):  # pylint: disable=too-many-public-methods\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.',\n", "  'body': 'Shouldn\\'t this say \"a ValueError error will be raised\" rather than \"...will take priority\"? ',\n", "  'commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "  'original_commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "  'base_sha': '80c3919700ada1d63998bbe446ea80a0f5eda026',\n", "  'head_sha': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "  'user': {'login': 'annatisch',\n", "   'id': '8689453',\n", "   'type': 'User',\n", "   'site_admin': 'false'},\n", "  'updated_at': '2021-01-12T20:46:15Z'},\n", " 'comment_url': 'https://github.com/Azure/azure-sdk-for-python/pull/16043/files#r*********',\n", " 'all_considered_hunks': ['diff --git a/tmp/tmp0z3ydo1b b/tmp/tmpqs_5ffko\\nindex *********..2152b2050 100644\\n--- a/tmp/tmp0z3ydo1b\\n+++ b/tmp/tmpqs_5ffko\\n',\n", "  '@@ -24,9 +24,11 @@\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.\\n     :keyword str secondary_hostname:\\n         The hostname of the secondary endpoint.\\n \\n',\n", "  '@@ -60,7 +62,8 @@\\n         :param credential:\\n             The credentials with which to authenticate. This is optional if the\\n             account URL already has a SAS token, or the connection string already has shared\\n-            access key values. The value can be a SAS token string, an account shared access\\n+            access key values. The value can be a SAS token string,\\n+            an instance of a AzureSasCredential from azure.core.credentials, an account shared access\\n             key, or an instance of a TokenCredentials class from azure.identity.\\n             Credentials provided here will take precedence over those in the connection string.\\n         :returns: A change feed client.\\n'],\n", " 'comment_full_info': {'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********',\n", "  'pull_request_review_id': *********,\n", "  'id': *********,\n", "  'node_id': 'MDI0OlB1bGxSZXF1ZXN0UmV2aWV3Q29tbWVudDU1NjA4MDY5OA==',\n", "  'diff_hunk': '@@ -24,9 +24,11 @@ class ChangeFeedClient(object):  # pylint: disable=too-many-public-methods\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.',\n", "  'path': 'sdk/storage/azure-storage-blob-changefeed/azure/storage/blob/changefeed/_change_feed_client.py',\n", "  'commit_id': '5145fdc1712f9fab985b7823da3667de909c7ff1',\n", "  'original_commit_id': '7766cfe74c448f89288d22d0b95393e9dff979cc',\n", "  'user': {'login': 'annatisch',\n", "   'id': 8689453,\n", "   'node_id': 'MDQ6VXNlcjg2ODk0NTM=',\n", "   'avatar_url': 'https://avatars.githubusercontent.com/u/8689453?v=4',\n", "   'gravatar_id': '',\n", "   'url': 'https://api.github.com/users/annatisch',\n", "   'html_url': 'https://github.com/annatisch',\n", "   'followers_url': 'https://api.github.com/users/annatisch/followers',\n", "   'following_url': 'https://api.github.com/users/annatisch/following{/other_user}',\n", "   'gists_url': 'https://api.github.com/users/annatisch/gists{/gist_id}',\n", "   'starred_url': 'https://api.github.com/users/annatisch/starred{/owner}{/repo}',\n", "   'subscriptions_url': 'https://api.github.com/users/annatisch/subscriptions',\n", "   'organizations_url': 'https://api.github.com/users/annatisch/orgs',\n", "   'repos_url': 'https://api.github.com/users/annatisch/repos',\n", "   'events_url': 'https://api.github.com/users/annatisch/events{/privacy}',\n", "   'received_events_url': 'https://api.github.com/users/annatisch/received_events',\n", "   'type': 'User',\n", "   'site_admin': <PERSON><PERSON><PERSON>},\n", "  'body': 'Shouldn\\'t this say \"a ValueError error will be raised\" rather than \"...will take priority\"? ',\n", "  'created_at': '2021-01-12T20:46:15Z',\n", "  'updated_at': '2021-01-12T20:56:32Z',\n", "  'html_url': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********',\n", "  'pull_request_url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043',\n", "  'author_association': 'MEMBER',\n", "  '_links': {'self': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********'},\n", "   'html': {'href': 'https://github.com/Azure/azure-sdk-for-python/pull/16043#discussion_r*********'},\n", "   'pull_request': {'href': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/16043'}},\n", "  'reactions': {'url': 'https://api.github.com/repos/Azure/azure-sdk-for-python/pulls/comments/*********/reactions',\n", "   'total_count': 0,\n", "   '+1': 0,\n", "   '-1': 0,\n", "   'laugh': 0,\n", "   'hooray': 0,\n", "   'confused': 0,\n", "   'heart': 0,\n", "   'rocket': 0,\n", "   'eyes': 0},\n", "  'start_line': None,\n", "  'original_start_line': None,\n", "  'start_side': None,\n", "  'line': None,\n", "  'original_line': 31,\n", "  'side': 'RIGHT',\n", "  'original_position': 10,\n", "  'position': None,\n", "  'subject_type': 'line'},\n", " 'full_diff_hunk': '@@ -24,9 +24,11 @@\\n         The URI to the storage account.\\n     :param credential:\\n         The credentials with which to authenticate. This is optional if the\\n-        account URL already has a SAS token. The value can be a SAS token string, an account\\n+        account URL already has a SAS token. The value can be a SAS token string,\\n+        an instance of a AzureSasCredential from azure.core.credentials, an account\\n         shared access key, or an instance of a TokenCredentials class from azure.identity.\\n-        If the URL already has a SAS token, specifying an explicit credential will take priority.\\n+        If AzureSasCredential is used, the URI must not contain a SAS token otherwise\\n+        if the URL already has a SAS token, specifying an explicit credential will take priority.\\n     :keyword str secondary_hostname:\\n         The hostname of the secondary endpoint.\\n \\n'}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["comment"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": []}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["40\n"]}], "source": ["GEN_DIR = Path(\"/home/<USER>/tmp/test_mar12_v19_classified\")\n", "loaded_samples = []\n", "for f_name in (GEN_DIR / \"bad\").glob(\"*.json\"):\n", "    with f_name.open() as f:\n", "        loaded_samples.append(json.load(f))\n", "print(len(loaded_samples))\n", "\n", "\n", "MAIN_HTML = \"\"\n", "for sample in loaded_samples:\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<a href=\\\"{sample['comment_url']}\\\">Comment URL</a>\"\n", "    MAIN_HTML += f\"<h4>Full prompt</h4> <pre>{sample['classification_prompt']}</pre>\"\n", "\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_comms_classification_mar12_v19_bad.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
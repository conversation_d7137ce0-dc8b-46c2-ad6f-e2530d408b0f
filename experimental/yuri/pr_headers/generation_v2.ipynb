{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import matplotlib.pyplot as plt\n", "import random\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"] = \"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explore data a bit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/home/<USER>/repos/augment/experimental/yuri/pr_headers/bq-pr-data-20k-mar11-init.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = []\n", "with DATA_PATH.open() as f:\n", "    for line in tqdm(f):\n", "        cur_sample = json.loads(line)\n", "        if 'body' not in cur_sample:\n", "            continue\n", "        if 'title' not in cur_sample:\n", "            continue\n", "        if 'pr_diff' not in cur_sample:\n", "            continue\n", "        if len(cur_sample['pr_diff'].splitlines()) > 1000:\n", "            continue\n", "        if len(cur_sample['pr_diff'].splitlines()) < 3:\n", "            continue\n", "        data.append(cur_sample)\n", "\n", "len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["random.shuffle(data)\n", "with open(\"/home/<USER>/repos/augment/experimental/yuri/pr_headers/bq-pr-data-20k-mar11-filtered-shuffled.json\", \"w\") as f:\n", "    for sample in tqdm(data):\n", "        f.write(json.dumps(sample) + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_lines = []\n", "for s in tqdm(data):\n", "    num_lines.append(len(s['pr_diff'].splitlines()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sum(map(lambda x: x <= 0, num_lines))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["random.shuffle(num_lines)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.hist(num_lines[:1000], bins=100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len([*filter(lambda s: len(s['pr_diff'].splitlines()) == 0, data)])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len([*filter(lambda s: len(s['pr_diff'].splitlines()) == 1, data)])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print([*filter(lambda s: len(s['pr_diff'].splitlines()) == 7, data)][0]['pr_diff'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HTML_START = f\"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "<head>\n", "    <title>Code Visualization</title>\n", "    <style>\n", "        pre {{\n", "            background-color: #f4f4f4;\n", "            border: 1px solid #ddd;\n", "            border-left: 3px solid #f36d33;\n", "            color: #666;\n", "            page-break-inside: avoid;\n", "            font-family: monospace;\n", "            font-size: 15px;\n", "            line-height: 1.6;\n", "            margin-bottom: 1.6em;\n", "            max-width: 100%;\n", "            overflow: auto;\n", "            padding: 1em 1.5em;\n", "            display: block;\n", "            word-wrap: break-word;\n", "        }}\n", "        .wide-line {{\n", "            width: 100%; \n", "            margin-left: auto;\n", "            margin-right: auto;\n", "            height: 20px;\n", "            background-color: black;\n", "        }}\n", "        .instructions li {{\n", "           color: gray; /* This makes all list items gray */\n", "        }}\n", "\n", "        .instructions li:first-child {{\n", "            color: black; /* This changes the color of the first item to black */\n", "        }}\n", "\n", "    </style>\n", "</head>\n", "<body>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => checkbox.id);\n", "  const listElement = document.getElementById('checkedList');\n", "  \n", "  // Create a string or list items from the checkedIds\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join(', ') : 'No checkboxes checked';\n", "  \n", "  // Update the div's content\n", "  listElement.textContent = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\"\n", "\n", "HTML_END = \"\"\"\n", "<div id=\"checkedList\"></div>\n", "\n", "<script>\n", "function updateCheckedList() {\n", "  const checkboxes = document.querySelectorAll('input[type=\"checkbox\"]:checked');\n", "  const checkedIds = Array.from(checkboxes).map(checkbox => `\"${checkbox.id}\",`);\n", "  \n", "  // Update to display each entry on its own line, enclosed in quotation marks\n", "  const listElement = document.getElementById('checkedList');\n", "  const listContent = checkedIds.length > 0 ? checkedIds.join('<br>') : 'No checkboxes checked';\n", "  \n", "  // Use innerHTML since we're including HTML tags (e.g., <br>)\n", "  listElement.innerHTML = listContent;\n", "}\n", "\n", "// Initial update in case any are checked by default\n", "updateCheckedList();\n", "\n", "// Add event listener to checkboxes\n", "document.querySelectorAll('input[type=\"checkbox\"]').forEach(checkbox => {\n", "  checkbox.addEventListener('change', updateCheckedList);\n", "});\n", "</script>\n", "</body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PROMPT_W_JUST_DIFF = \"\"\"\n", "Here is a diff from a GitHub PR:\n", "```\n", "{pr_diff}\n", "```\n", "\n", "Here are the PR details written so far:\n", "1. Title:\n", "```\n", "{cur_title}\n", "```\n", "2. Body:\n", "```\n", "{cur_body}\n", "```\n", "\n", "Please, refine Title and Body to be more clear and descriptive.\n", "Refined Title and Body should not contain any links or usernames, except if they directly mentioned in code.\n", "Body should be structured as such:\n", "- Some describing text\n", "- List of key changes\n", "\n", "Follow these steps to complete this task:\n", "1. Analyze the diff and current PR details.\n", "2. Describe your thinking process on how to generate a refined PR description.\n", "3. Explicitly write refined Title and Body.\n", "\"\"\"\n", "\n", "EXTRACT_PROMPT = \"\"\"Return the refined PR description as a JSON with 2 keys:\n", "- title\n", "- body\n", "\n", "Don't miss new lines if they are present\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_description_v1(sample, gpt):\n", "    prompt = PROMPT_W_JUST_DIFF.format(\n", "        pr_diff=sample[\"pr_diff\"],\n", "        cur_title=sample[\"title\"],\n", "        cur_body=sample['body'],\n", "    )\n", "    # prompt = PROMPT_W_JUST_DIFF.format(\n", "    #     pr_diff=sample[\"pr_diff\"],\n", "    #     cur_title=\"\",\n", "    #     cur_body=\"\",\n", "    # )\n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    gpt4_response = gpt(messages, model=\"gpt-4-1106-preview\", temperature=0)\n", "\n", "    messages.append({\"role\": \"assistant\", \"content\": gpt4_response})\n", "    messages.append({\"role\": \"user\", \"content\": EXTRACT_PROMPT})\n", "    extracted_response = gpt(messages, model=\"gpt-3.5-turbo-1106\", use_json=True, temperature=0)\n", "\n", "    return extracted_response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt = GptWrapper()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_samples = []\n", "\n", "for i in tqdm(range(0, 10)):\n", "    sample = data[i]\n", "    try:\n", "        cur_result = generate_description_v1(sample, gpt)\n", "    except Exception as e:\n", "        print(f\"Failed to generate description for {i}-th sample: {e}\")\n", "        continue\n", "    final_samples.append([sample, cur_result])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GEN_DIR = Path(\"/home/<USER>/tmp/test_pr_header_outputs_mar11_v7\")\n", "loaded_samples = []\n", "for f_name in (GEN_DIR / \"good\").glob(\"*.json\"):\n", "    with f_name.open() as f:\n", "        loaded_samples.append(json.load(f))\n", "print(len(loaded_samples))\n", "\n", "MAIN_HTML = \"\"\n", "for sample in loaded_samples:\n", "    pr_url = f\"https://github.com/{sample['base']['repo_full_name']}/pull/{sample['number']}\"\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<a href=\\\"{pr_url}\\\">PR link</a>\"\n", "    MAIN_HTML += f\"<h4>Real title:</h4> <pre>{sample['title']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Generated title:</h4> <pre>{sample['gen_title']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Real body:</h4> <pre>{sample['body']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Generated body:</h4> <pre>{sample['gen_body']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Diff:</h4> <pre>{sample['pr_diff']}</pre>\"\n", "\n", "\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_pr_header_mar11_v13.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["loaded_samples[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MAIN_HTML = \"\"\n", "# for sample, generated_text in final_samples:\n", "for sample, generated_text in loaded_samples:\n", "    pr_url = f\"github.com/{sample['base']['repo_full_name']}/pull/{sample['number']}\"\n", "    MAIN_HTML += \"<hr class=\\\"wide-line\\\">\"\n", "    MAIN_HTML += f\"<a href=\\\"{pr_url}\\\">PR link</a>\"\n", "    MAIN_HTML += f\"<h4>Real title:</h4> <pre>{sample['title']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Generated title:</h4> <pre>{generated_text['title']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Real body:</h4> <pre>{sample['body']}</pre>\"\n", "    MAIN_HTML += f\"<h4>Generated body:</h4> <pre>{generated_text['body']}</pre>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "RESULTING_HTML = HTML_START + MAIN_HTML + HTML_END\n", "with open('./test_pr_header_mar11_v9.html', 'w') as f:\n", "    f.write(RESULTING_HTML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
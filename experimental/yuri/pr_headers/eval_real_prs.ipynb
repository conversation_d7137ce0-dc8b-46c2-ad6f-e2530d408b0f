{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "from tqdm import tqdm\n", "\n", "from research.eval.harness.systems.all_systems import DroidCodeEditSystem\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from base.prompt_format_edit.prompt_formatter import ExceedContextLength"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GITHUB_TOKEN = \"???\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_last_n_prs(repo_owner, repo_name, access_token, n):\n", "    prs = []\n", "    url = f\"https://api.github.com/repos/{repo_owner}/{repo_name}/pulls\"\n", "    headers = {\"Authorization\": f\"token {access_token}\"}\n", "    \n", "    items_per_page = min(n, 100)\n", "    params = {\"state\": \"closed\", \"per_page\": items_per_page}\n", "\n", "    while True:\n", "        response = requests.get(url, params=params, headers=headers)\n", "        if response.status_code == 200:\n", "            print(\"Successful request\")\n", "            batch = response.json()\n", "            prs.extend(batch)\n", "            # If the length of the batch is less than items_per_page, we've reached the last page\n", "            if len(batch) < items_per_page or len(prs) >= n:\n", "                break\n", "            # Prepare for the next page\n", "            params[\"page\"] = params.get(\"page\", 1) + 1\n", "        else:\n", "            raise Exception(f\"Failed to fetch PRs: {response.status_code} - {response.text}\")\n", "\n", "        # Ensure we don't fetch more than needed\n", "        if len(prs) >= n:\n", "            prs = prs[:n]\n", "            break\n", "\n", "    return prs\n", "\n", "def get_pr_diff(pr_number):\n", "    url = f\"https://api.github.com/repos/augmentcode/augment/pulls/{pr_number}\"\n", "    \n", "    headers = {\n", "        \"Authorization\": f\"token {GITHUB_TOKEN}\",\n", "        \"Accept\": \"application/vnd.github.v3.diff\"\n", "    }\n", "    response = requests.get(url, headers=headers)\n", "    \n", "    if response.status_code == 200:\n", "        return response.text\n", "    else:\n", "        assert False, f\"Failed to fetch PR diff: {response.status_code} - {response.reason}\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prs = get_last_n_prs(\"augmentcode\", \"augment\", GITHUB_TOKEN, 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token_apportionment = {\n", "    \"path_len\": 0,\n", "    \"instruction_len\": 32,\n", "    \"prefix_len\": 0,\n", "    \"selected_code_len\": 12000,\n", "    \"suffix_len\": 0,\n", "    \"max_prompt_tokens\": 16384 - 4096,  # 4096 represents the max output tokens\n", "}\n", "\n", "config = {\n", "  \"name\": \"droid_code_edit\",\n", "  \"model\": {\n", "    \"name\": \"fastforward_droid\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/user/yuri/experiments/yuri_pr_title_2k_mar16_v1/ffw\",\n", "    \"override_prompt_formatter\": {\n", "        \"name\": \"droid\",\n", "        **token_apportionment\n", "    }\n", "  },\n", "  \"generation_options\": {\n", "    \"temperature\": 0,\n", "    \"top_k\": 0,\n", "    \"top_p\": 0,\n", "    \"max_generated_tokens\": 1024\n", "  }\n", "}\n", "system = DroidCodeEditSystem.from_yaml_config(config)\n", "\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pr = prs[0]\n", "\n", "diff_content = get_pr_diff(pr[\"number\"])\n", "\n", "system_input = ResearchEditPromptInput(\n", "    path=\"\",\n", "    prefix=\"\",\n", "    suffix=\"\",\n", "    selected_code=diff_content,\n", "    instruction=\"Summarize the PR\",\n", "    retrieved_chunks=[],\n", ")\n", "\n", "model_output = system.generate(system_input)\n", "\n", "print(\"=\" * 50)\n", "print(f\"URL: {pr['html_url']}\")\n", "print(f\"Real: {pr['title']}\")\n", "print(f\"Generated: {model_output.generated_text}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = []\n", "\n", "for pr in tqdm(prs):\n", "    diff_content = get_pr_diff(pr[\"number\"])\n", "\n", "    system_input = ResearchEditPromptInput(\n", "        path=\"\",\n", "        prefix=\"\",\n", "        suffix=\"\",\n", "        selected_code=diff_content,\n", "        instruction=\"Summarize the PR\",\n", "        retrieved_chunks=[],\n", "    )\n", "\n", "    try:\n", "        model_output = system.generate(system_input)\n", "    except Exception as e:\n", "        print(f\"Failed to process {pr['html_url']}: {e}\")\n", "        continue\n", "\n", "    results.append((pr, model_output))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for (_pr, _model_output) in results:\n", "    print(\"=\" * 50)\n", "    print(f\"URL: {_pr['html_url']}\")\n", "    print(f\"Real: {_pr['title']}\")\n", "    print(f\"Generated: {_model_output.generated_text}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
import argparse
import json
from multiprocessing import Pool

from pathlib import Path
from tqdm import tqdm

from research.data.synthetic_code_edit.api_lib import GptWrapper


PROMPT_W_JUST_DIFF = """
Here is a diff from a GitHub PR:
```
{pr_diff}
```

Here are the PR details written so far:
1. Title:
```
{cur_title}
```
2. Body:
```
{cur_body}
```

Please, refine Title and Body to be more clear and descriptive.
Refined Title and Body should not contain any links or usernames, except if they directly mentioned in code.
Body should be structured as such:
- Some describing text
- List of key changes

Follow these steps to complete this task:
1. Analyze the diff and current PR details.
2. Describe your thinking process on how to generate a refined PR description.
3. Explicitly write refined Title and Body.
"""

EXTRACT_PROMPT = """Return the refined PR description as a JSON with 2 keys:
- title
- body

Don't miss new lines if they are present
"""


def generate_description_v1(sample, gpt):
    prompt = PROMPT_W_JUST_DIFF.format(
        pr_diff=sample["pr_diff"],
        cur_title=sample["title"],
        cur_body=sample['body'],
    )

    messages = [{"role": "user", "content": prompt}]
    gpt4_response = gpt(messages, model="gpt-4-1106-preview", temperature=0)

    messages.append({"role": "assistant", "content": gpt4_response})
    messages.append({"role": "user", "content": EXTRACT_PROMPT})
    extracted_response = gpt(messages, model="gpt-3.5-turbo-1106", use_json=True, temperature=0)

    return extracted_response


def read_data(input_json):
    data = []
    with input_json.open() as f:
        for line in tqdm(f):
            cur_sample = json.loads(line)
            if 'body' not in cur_sample:
                continue
            if 'title' not in cur_sample:
                continue
            if 'pr_diff' not in cur_sample:
                continue
            if len(cur_sample['pr_diff'].splitlines()) > 1000:
                continue
            if len(cur_sample['pr_diff'].splitlines()) < 3:
                continue
            data.append(cur_sample)

    return data


def generate_sample(args):
    sample, gpt, result_dir, sample_idx = args

    for _dir in ["good", "failed"]:
        if (result_dir / _dir / f"{sample_idx}.json").exists():
            return

    try:
        cur_result = generate_description_v1(sample, gpt)
    except Exception as e:
        print(f"Failed to generate description for {sample_idx}-th sample: {e}")
        with (result_dir / "failed" / f"{sample_idx}.json").open("w") as f:
            json.dump(sample, f, indent=2)
        return

    sample["gen_title"] = cur_result["title"]
    sample["gen_body"] = cur_result["body"]
    with (result_dir / "good" / f"{sample_idx}.json").open("w") as f:
        json.dump(sample, f, indent=2)



def main(
    input_json,
    output_dir,
    num_processes,
    num_samples
):
    output_dir.mkdir(exist_ok=True, parents=False)
    (output_dir / "good").mkdir(exist_ok=True, parents=False)
    (output_dir / "failed").mkdir(exist_ok=True, parents=False)

    data = read_data(input_json)
    print(f"Read {len(data)} samples.")

    gpt = GptWrapper()
    tasks = []
    for i, sample in enumerate(data[:num_samples]):
        tasks.append([sample, gpt, output_dir, i])

    if num_processes <= 1:
        for task in tqdm(tasks):
            generate_sample(task)
    else:
        with Pool(num_processes) as pool:
            _ = list(tqdm(pool.imap(generate_sample, tasks), total=len(tasks)))

    print(f"GPT usage:\n{gpt.get_stats()}")



def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description="Script for generating pr headers synthetic data.",
    )
    parser.add_argument(
        "--input_json",
        "-i",
        type=Path,
        required=True,
        help="Input json file with data from BigQuery",
    )
    parser.add_argument(
        "--output_dir",
        "-o",
        type=Path,
        required=True,
        help="Output directory for saving the data",
    )
    parser.add_argument(
        "--num_processes",
        "-np",
        type=int,
        default=1,
        help="Number of parallel processes",
    )
    parser.add_argument(
        "--num_samples",
        "-n",
        type=int,
        required=True,
        help="Number of samples to generate",
    )
    args = parser.parse_args()

    return args


if __name__ == "__main__":
    xargs = parse_args()
    main(
        input_json=xargs.input_json,
        output_dir=xargs.output_dir,
        num_processes=xargs.num_processes,
        num_samples=xargs.num_samples,
    )

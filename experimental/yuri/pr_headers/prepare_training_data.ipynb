{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"/home/<USER>/repos/augment\")\n", "sys.path.append(\"/home/<USER>/repos/augment/research/gpt-neox\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from research.core.abstract_prompt_formatter import get_prompt_formatter\n", "\n", "from research.core.model_input import ModelInput\n", "from research.eval.harness.factories import create_retriever\n", "from research.retrieval.types import Document\n", "\n", "\n", "import numpy as np\n", "import torch\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DATA_PATH = Path(\"/mnt/efs/augment/user/yuri/data/pr_headers_data_mar11_v1/good\")\n", "SEQ_LENGTH = 16384"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_dataset(train_dataset, eval_dataset, output_path):\n", "    for f_name, dataset in [(\"train\", train_dataset), (\"valid\", eval_dataset)]:\n", "        cur_output_path = output_path / f_name\n", "        builder = MMapIndexedDatasetBuilder(\n", "            cur_output_path.with_suffix(\".bin\"), dtype=np.int32\n", "        )\n", "        for sample in tqdm(dataset):\n", "            if len(sample) == 1:\n", "                continue\n", "            builder.add_item(torch.tensor(sample, dtype=torch.int32))\n", "            builder.end_document()\n", "        builder.finalize(cur_output_path.with_suffix(\".idx\"))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = []\n", "for f_name in tqdm(DATA_PATH.glob(\"*.json\")):\n", "    with f_name.open() as f:\n", "        data.append(json.load(f))\n", "\n", "len(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = data[5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sample[\"gen_title\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token_apportionment = {\n", "    \"path_len\": 0,\n", "    \"instruction_len\": 32,\n", "    \"prefix_len\": 0,\n", "    \"selected_code_len\": 12000,\n", "    \"suffix_len\": 0,\n", "    \"max_prompt_tokens\": 16384 - 4096,  # 4096 represents the max output tokens\n", "}\n", "\n", "prompt_formatter = get_prompt_formatter(\"droid\", **token_apportionment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenized_samples = []\n", "\n", "for sample in tqdm(data):\n", "    model_input = ModelInput(\n", "        path=\"\",\n", "        prefix=\"\",\n", "        suffix=\"\",\n", "        retrieved_chunks=[],\n", "        extra={\n", "            \"instruction\": \"Summarize the PR\",\n", "            \"selected_code\": sample[\"pr_diff\"],\n", "            \"prefix_begin\": 0,\n", "            \"suffix_end\": len(sample[\"pr_diff\"]),\n", "        }\n", "    )\n", "\n", "    try:\n", "        tokenized_input, _ = prompt_formatter.prepare_prompt(model_input)\n", "    except Exception as e:\n", "        print(f\"Sample preparation failed: {e}\")\n", "        continue\n", "\n", "    tokenized_output = prompt_formatter.tokenizer.tokenize(sample[\"gen_title\"]  + \"\\n```\\n\") + [prompt_formatter.tokenizer.eod_id]\n", "\n", "    complete_prompt = [-1 * t for t in tokenized_input] + tokenized_output\n", "\n", "    if len(complete_prompt) > SEQ_LENGTH:\n", "        print(f\"Sample too long: {len(complete_prompt)} > {SEQ_LENGTH}\")\n", "        continue\n", "    else:\n", "        complete_prompt += [-1 * prompt_formatter.tokenizer.eod_id] * (\n", "            SEQ_LENGTH - len(complete_prompt) + 1\n", "        )  # +1 to make total prompt of length SEQUENCE_LENGTH + 1  \n", "\n", "    tokenized_samples.append(complete_prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(DATA_PATH / \"tokenized_samples\").mkdir(exist_ok=True)\n", "save_dataset(tokenized_samples[:-100], tokenized_samples[-100:], DATA_PATH / \"tokenized_samples\")\n", "(DATA_PATH / \"tokenized_samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
import asyncio
from dataclasses import asdict
import json
import os
from typing import Annotated
import aiofiles
from anthropic import AsyncAnthropicVertex
from fastapi import FastAPI, Header
from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File
from base.prompt_format_autofix.check_command_prompt_formatter import (
    AutofixCheckCommandInput,
)
from base.prompt_format_autofix.common import PromptChunkWithLines
from base.prompt_format_autofix.contains_errors_prompt_formatter import (
    AutofixContainErrorsInput,
)
from base.prompt_format_autofix.create_fix_plan_prompt_formatter import (
    ApplyFileFixInput,
    AutofixFileFix,
    AutofixCreateFixPlanInput,
)
from research.autofix.autofix_manager import AutofixManager
from research.core.types import Chunk, Document
# remote: fastapi run experimental/lior/improve_fix/autofix_server.py --port 8080 --reload


app = FastAPI()

client = AsyncAnthropicVertex(
    project_id="augment-387916", region="us-east5", timeout=60, max_retries=3
)
manager = AutofixManager(client)


async def save_to_file(
    request,
    response,
    request_type: str,
    request_id: str | None = None,
    request_session_id: str | None = None,
):
    if request_id is None:
        request_id = "unknown"
    if request_session_id is None:
        request_session_id = "unknown"

    path: str = os.path.join(
        "/tmp", "autofix_server_logs", request_session_id, request_type
    )
    await asyncio.to_thread(os.makedirs, path, exist_ok=True)
    file_name = os.path.join(path, f"{request_id}.json")
    data = json.dumps(
        {
            "request": asdict(request),
            "response": asdict(response),
        },
        indent=2,
    )

    async with aiofiles.open(file_name, "w") as f:
        await f.write(data)
    print(f"Saved {request_type} to file {file_name} ({len(data)} bytes)")


@app.post("/check_command")
async def check_command(
    data: dict,
    x_request_id: Annotated[str | None, Header()] = None,
    x_request_session_id: Annotated[str | None, Header()] = None,
):
    request = AutofixCheckCommandInput(**data)
    response = await manager.check_command(request)
    asyncio.create_task(
        save_to_file(
            request, response, "check_command", x_request_id, x_request_session_id
        )
    )
    return response


@app.post("/contain_errors")
async def contain_errors(
    data: dict,
    x_request_id: Annotated[str | None, Header()] = None,
    x_request_session_id: Annotated[str | None, Header()] = None,
):
    request = AutofixContainErrorsInput(**data)
    response = await manager.contain_errors(request)
    asyncio.create_task(
        save_to_file(
            request, response, "contain_errors", x_request_id, x_request_session_id
        )
    )
    return response


@app.post("/create_fix_plan")
async def create_fix_plan(
    data: dict,
    x_request_id: Annotated[str | None, Header()] = None,
    x_request_session_id: Annotated[str | None, Header()] = None,
):
    request = AutofixCreateFixPlanInput(
        command=data["command"],
        command_output=data["command_output"],
        edit_locations=[
            PromptChunkWithLines(
                text=loc["text"],
                path=loc["path"],
                line_offset=loc["line_offset"],
                length_in_lines=loc["length_in_lines"],
            )
            for loc in data["edit_locations"]
        ],
        breaking_change=[
            Modified[File](
                before=File(
                    path=change["before"]["path"],
                    contents=change["before"]["contents"],
                ),
                after=File(
                    path=change["after"]["path"],
                    contents=change["after"]["contents"],
                ),
            )
            for change in data["breaking_change"]
        ],
    )
    response = await manager.create_fix_plan(request)
    asyncio.create_task(
        save_to_file(
            request, response, "create_fix_plan", x_request_id, x_request_session_id
        )
    )
    return response


@app.post("/apply_file_fix")
async def apply_file_fix(
    data: dict,
    x_request_id: Annotated[str | None, Header()] = None,
    x_request_session_id: Annotated[str | None, Header()] = None,
):
    request = ApplyFileFixInput(
        file_fix=AutofixFileFix(**data["file_fix"]),
        source_content=data["source_content"],
    )
    response = await manager.apply_file_fix(request)
    asyncio.create_task(
        save_to_file(
            request, response, "apply_file_fix", x_request_id, x_request_session_id
        )
    )
    return response

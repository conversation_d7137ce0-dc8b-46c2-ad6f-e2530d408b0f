"""
Knowledge Generator for Container Setup Agent.

This module analyzes a codebase to generate a comprehensive knowledge document
about the languages, tools, and best practices used in the project.
"""

import os
import sys
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any, Optional, Callable

from termcolor import colored
from tqdm import tqdm  # type: ignore

from base.augment_client.client import AugmentClient
from experimental.lior.python_interpreter_agent.builtins import (
    run_command,
    truncate_middle,
)
from experimental.guy.agent_qa.builtin_tools import (
    BackendCodebaseRetrievalTool,
    ReadFileTool,
    CompleteTool,
)
from experimental.guy.agent_qa.tools.bash_tool import BashTool
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_staging_api_proxy_url,
)
from research.llm_apis.llm_client import (
    GeneralContentBlock,
    get_client,
    <PERSON><PERSON>rompt,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>lFormattedResult,
)
from research.agents.tools import ToolCallLogger

# Constants
MAX_CHAR_OUTPUT = 10000
NUM_WORKERS = 16
TOP_K_LANGUAGES = 20


class ExpertAgent:
    """Agent that analyzes code at a specific git reference."""

    def __init__(self, verbose: bool = False):
        """Initialize the agent with a git reference.

        Args:
            verbose: Whether to print verbose output during execution.
        """
        self.temp_dir = Path.cwd()
        self.verbose = verbose

        # Setup workspace manager and tools
        self.tool_logger = ToolCallLogger()
        token = Path.home().joinpath(".augment/token").read_text().strip()
        api_proxy_url = get_staging_api_proxy_url()
        self.augment_client = AugmentPrototypingClient(api_proxy_url, auth_token=token)
        self.workspace_manager = WorkspaceManagerImpl(
            augment_client=self.augment_client,
            root=self.temp_dir,
        )

        self._max_char_output = MAX_CHAR_OUTPUT

        # Initialize tools
        augment_public_api_client = AugmentClient(api_proxy_url, token=token)
        self.codebase_tool = BackendCodebaseRetrievalTool(
            tool_call_logger=self.tool_logger,
            client=augment_public_api_client,
            workspace_manager=self.workspace_manager,
        )

        self.read_file_tool = ReadFileTool(
            tool_call_logger=self.tool_logger,
            root=self.temp_dir,
            char_budget=self._max_char_output,
        )
        self.complete_tool = CompleteTool(
            tool_call_logger=self.tool_logger,
        )
        self.bash_tool = BashTool(
            tool_call_logger=self.tool_logger,
            workspace_root=self.temp_dir,
            require_confirmation=False,
        )

        # Initialize LLM client
        self.client = get_client(
            "anthropic-direct", model_name="claude-3-5-sonnet-20241022", max_retries=50
        )

    def answer_question(self, question: str) -> str:
        """Answer a question about the codebase.

        Args:
            question: The question to answer

        Returns:
            The expert's answer as a string
        """
        # Add user question to history
        self.messages: List[List[GeneralContentBlock]] = [[TextPrompt(text=question)]]

        # Define available tools
        tools = [
            self.read_file_tool.get_tool_param(),
            self.codebase_tool.get_tool_param(),
            self.complete_tool.get_tool_param(),
            self.bash_tool.get_tool_param(),
        ]

        while True:
            response, _ = self.client.generate(
                messages=self.messages,
                max_tokens=8192,
                system_prompt=f"""
                You are an expert software engineer answering questions about the codebase.
                Carefully follow the user's instructions.
                Think step-by-step, and use the available tools to gather information.

                Current working directory is '{self.temp_dir.absolute()}'.
                Operating system is '{os.name}'.
                Always use relative paths when referring to files, and prepend them with './'.
                Use the {self.complete_tool.name} tool to provide your final answer.
                """,
                tools=tools,
            )

            response_messages = []
            for message in response:
                if isinstance(message, TextResult):
                    if self.verbose:
                        print(colored(message.text, "green"))
                    response_messages.append(message)
                elif isinstance(message, ToolCall):
                    if self.verbose:
                        print(colored(f"Tool call: {message.tool_name}", "blue"))
                    if message.tool_name == self.read_file_tool.name:
                        response_messages.append(message)
                        self.messages.append(response_messages)
                        result = self.read_file_tool.run_impl(message.tool_input)
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=truncate_middle(
                                result.tool_output, self._max_char_output
                            ),
                        )
                        self.messages.append([formatted_result])
                        response_messages = []
                        continue

                    elif message.tool_name == self.codebase_tool.name:
                        response_messages.append(message)
                        self.messages.append(response_messages)
                        result = self.codebase_tool.run_impl(message.tool_input)
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=truncate_middle(
                                result.tool_output, self._max_char_output
                            ),
                        )
                        self.messages.append([formatted_result])
                        response_messages = []
                        continue

                    elif message.tool_name == self.bash_tool.name:
                        response_messages.append(message)
                        self.messages.append(response_messages)
                        result = self.bash_tool.run_impl(message.tool_input)
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=truncate_middle(
                                result.tool_output, self._max_char_output
                            ),
                        )
                        if self.verbose:
                            print(colored(formatted_result.tool_output, "yellow"))
                        self.messages.append([formatted_result])
                        response_messages = []
                        continue

                    elif message.tool_name == self.complete_tool.name:
                        return message.tool_input["answer"]

        raise ValueError("Should not reach here")


# Configuration for different categories
def get_general_categories() -> Dict[str, Dict[str, str]]:
    """Return configuration for general project categories."""
    return {
        "source control platforms and tools": {
            "question_template": "Can you please list all {category} used in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: git,svn.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to interact with the source control platform?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to interact with the source control platform, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
        },
        "cloud providers": {
            "question_template": "Can you please list all {category} used in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: aws,azure.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to interact with the cloud provider?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to interact with the cloud provider, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
        },
        "databases and data stores": {
            "question_template": "Can you please list all {category} used in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: mysql,postgresql.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to interact with the database?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to interact with the database, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
        },
        "CI/CD platforms": {
            "question_template": "Can you please list all {category} used in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: github,gitlab.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to interact with the CI/CD tool?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to interact with the CI/CD tool, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
        },
        "architecture and design patterns": {
            "question_template": "Can you please list all {category} used in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that are used in the project. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: monorepo,microservices.",
            "tool_question_template": "Can you please explain how {tool} is applied to the project?",
            "tool_context_template": "Search the codebase to deeply understand how {tool} is applied to the project.",
        },
        "monitoring and logging tools": {
            "question_template": "Can you please list all {category} used in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: prometheus,grafana.",
            "tool_question_template": "Can you please explain how {tool} is used for monitoring and logging tool?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used for monitoring and logging tool, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
        },
    }


def get_language_categories() -> Dict[str, Dict[str, Any]]:
    """Return configuration for language-specific categories."""
    return {
        "best practices": {
            "question_template": "Can you please summarize in detail the {category} for '{lang}' programming language in the project?",
            "context_template": "Search the codebase to find {category} for '{lang}', potentially in README.md, documentation, shell scripts, or CI scripts. Focus on examples that align with the project's best practices.",
            "tool_response": False,
        },
        "package managers": {
            "question_template": "Can you please list all {category} used for {lang} in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report the {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: pip,npm.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to install dependencies?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to install dependencies, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
            "tool_response": True,
        },
        "testing frameworks": {
            "question_template": "Can you please list all {category} used for {lang} in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: pytest,unittest.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to run tests?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to run tests, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
            "tool_response": True,
        },
        "linters": {
            "question_template": "Can you please list all {category} used for {lang} in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: pylint,flake8.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to run linter?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to lint code, potentially in README.md, documentation, shell scripts, or CI scripts. Focus on examples that align with the project's best practices.",
            "tool_response": True,
        },
        "build systems": {
            "question_template": "Can you please list all {category} used for {lang} in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: make,cmake.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to build the project?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to compile, run tests, and lint code, potentially in README.md, documentation, shell scripts, or CI scripts. Focus on examples that align with the project's best practices.",
            "tool_response": True,
        },
        "environment managers": {
            "question_template": "Can you please list all {category} used for {lang} in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: virtualenv,conda.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to create a environment?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to create and activate a environment, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
            "tool_response": True,
        },
        "dependency declarations": {
            "question_template": "Can you please list all {category} used for {lang} in the project? If there are none, please respond with 'NONE'.",
            "context_template": "Only report {category} that I have installed on my local machine. Respond with a comma separated list, with no prefix or suffix and no spaces. For example: requirements.txt,package.json.",
            "tool_question_template": "Can you please explain how to use {tool} from terminal to install dependencies?",
            "tool_context_template": "Search the codebase to find examples of how {tool} is used to install dependencies, potentially in README.md, documentation, shell scripts, or CI scripts. Include any flags or arguments that are used. Focus on examples that align with the project's best practices.",
            "tool_response": True,
        },
    }


class KnowledgeGenerator:
    """Generates knowledge about a codebase by analyzing its structure and tools."""

    def __init__(self, top_k: int = TOP_K_LANGUAGES, num_workers: int = NUM_WORKERS):
        """Initialize the knowledge generator.

        Args:
            top_k: Number of top languages to analyze
            num_workers: Number of parallel workers for processing
        """
        self.top_k = top_k
        self.num_workers = num_workers
        self.start_time = time.time()

        # Initialize data structures
        self.tools_map = defaultdict(dict)
        self.answers_map = defaultdict(dict)
        self.tools_usage_examples = defaultdict(lambda: defaultdict(dict))

        # Get configurations
        self.general_categories = get_general_categories()
        self.categories = get_language_categories()

    def ask_expert(self, question: str, context: str) -> str:
        """Ask an expert specific question about codebase.

        Args:
            question (str): Concise one sentence question to ask.
            context (str): All relevant external non-code information

        Returns (str):
            The expert's answer as a string
        """
        expert = ExpertAgent()
        return expert.answer_question(
            f"Question: {question}\n\nRelevant context: {context}"
        )

    def detect_languages(self) -> List[str]:
        """Detect programming languages used in the project.

        Returns:
            List of programming languages
        """
        command = f"""find . -type f -name "*.*" | sed 's/.*\\.//' | tr '[:upper:]' '[:lower:]' | sort | grep -E '^(ad|adown|argdown|argdn|bicep|c|cpp|cc|cp|cxx|h|hpp|hxx|cs|ex|elm|erb|rhtml|gd|godot|tres|tscn|go|haml|hs|hx|html|htm|java|js|jsx|kt|ml|mli|mll|mly|php|py|r|rb|rs|res|resi|sass|scala|styl|swift|tf|tfvars|ts|tsx|vue|vala)$' | uniq -c | sort -nr | head -n {self.top_k}"""
        output = run_command(command)

        list_of_languages = self.ask_expert(
            "Can you please list all programming languages used in the project?",
            context=f"""
        Here is the list of the {self.top_k} most common file extensions in the project:
        {output}
        Exclude any languages that are declerative such as JSON, YAML, ProtoBuf, etc.
        Exclude any languages that do not have significant number of files in the project.
        Respond with a comma separated list, with no prefix or suffix and no spaces.
        Combine family of languages into a single language. For example, if you see both 'javascript' and 'typescript', combine them into 'js/ts'.
        For example: python,java,javascript.
        """,
        )
        return list_of_languages.split(",")

    def process_category(
        self, lang: str, category: str, categories: dict
    ) -> tuple[str, str, str, list[str]]:
        """Process a specific language category.

        Args:
            lang: Programming language
            category: Category name
            categories: Category configuration

        Returns:
            Tuple of (language, category, answer, tools)
        """
        question = categories[category]["question_template"].format(
            lang=lang, category=category
        )
        context = categories[category]["context_template"].format(
            lang=lang, category=category
        )
        answer = self.ask_expert(question, context)
        tools = (
            answer.split(",")
            if "NONE" not in answer and categories[category]["tool_response"]
            else []
        )
        return lang, category, answer, tools

    def process_general_category(
        self, category: str, category_config: dict
    ) -> tuple[str, str, set[str]]:
        """Process a general category.

        Args:
            category: Category name
            category_config: Category configuration

        Returns:
            Tuple of (category, answer, tools)
        """
        question = category_config["question_template"].format(category=category)
        context = category_config["context_template"].format(category=category)
        answer = self.ask_expert(question, context)
        tools = set(answer.split(",")) if "NONE" not in answer else set()
        return category, answer, tools

    def process_tool(
        self, args: tuple[str, str, str, dict]
    ) -> tuple[str, str, str, str]:
        """Process a specific tool.

        Args:
            args: Tuple of (language, category, tool, category_config)

        Returns:
            Tuple of (language, category, tool, tool_answer)
        """
        lang, category, tool, category_config = args
        tool_question = category_config["tool_question_template"].format(
            lang=lang, tool=tool
        )
        tool_context = category_config["tool_context_template"].format(
            lang=lang, tool=tool
        )
        tool_answer = self.ask_expert(tool_question, tool_context)
        return lang, category, tool, tool_answer

    def analyze_languages(self, langs: List[str]) -> None:
        """Analyze languages and their categories.

        Args:
            langs: List of programming languages
        """
        total_steps = len(langs) * len(self.categories)

        # Find the tools
        with tqdm(total=total_steps, desc="🔍 Analyzing project") as pbar:
            with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                futures = []
                for lang in langs:
                    for category in self.categories:
                        futures.append(
                            executor.submit(
                                self.process_category, lang, category, self.categories
                            )
                        )

                for future in as_completed(futures):
                    lang, category, answer, tools = future.result()
                    pbar.set_description(
                        f"🔍 Analyzing project - {colored(lang, 'green')}/{colored(category, 'blue')}"
                    )
                    self.answers_map[lang][category] = answer
                    if tools:  # Only add to tools_map if we got valid tools
                        self.tools_map[lang][category] = set(tools)
                    pbar.update(1)

    def analyze_general_categories(self) -> None:
        """Analyze general project categories."""
        # General categories tools
        with tqdm(
            total=len(self.general_categories), desc="🔍 Analyzing project"
        ) as pbar:
            with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                futures = []
                for category in self.general_categories:
                    futures.append(
                        executor.submit(
                            self.process_general_category,
                            category,
                            self.general_categories[category],
                        )
                    )

                for future in as_completed(futures):
                    category, answer, tools = future.result()
                    pbar.set_description(
                        f"🔍 Analyzing project - {colored(category, 'blue')}"
                    )
                    self.answers_map["general"][category] = answer
                    if tools:  # Only add to tools_map if we got valid tools
                        self.tools_map["general"][category] = tools
                    pbar.update(1)

    def analyze_tools(self) -> None:
        """Analyze tools and their usage examples."""
        # Calculate total number of tools across all languages and categories
        total_tools = sum(
            len(tools)
            for lang_tools in self.tools_map.values()
            for tools in lang_tools.values()
        )

        # Find the usage examples
        with tqdm(total=total_tools, desc="🔍 Analyzing project") as pbar:
            with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                futures = []
                for lang, cat in self.tools_map.items():
                    for category, tools in cat.items():
                        for tool in tools:
                            config = (
                                self.categories[category]
                                if category in self.categories
                                else self.general_categories[category]
                            )
                            futures.append(
                                executor.submit(
                                    self.process_tool, (lang, category, tool, config)
                                )
                            )

                for future in as_completed(futures):
                    lang, category, tool, tool_answer = future.result()
                    pbar.set_description(
                        f"🔍 Analyzing project - {colored(lang, 'green')}/{colored(category, 'blue')}/{colored(tool, 'magenta')}"
                    )
                    self.tools_usage_examples[lang][category][tool] = tool_answer
                    pbar.update(1)

    def generate_document(self, langs: List[str]) -> str:
        """Generate the knowledge document.

        Args:
            langs: List of programming languages

        Returns:
            The generated document as a string
        """
        document = "# Project Analysis Report"

        for lang in langs:
            document += f"\n\n## {lang}\n"
            document += f"\n### {lang} best practices:\n{self.answers_map[lang]['best practices']}\n"
            for category in self.categories:
                if category not in self.tools_map[lang]:
                    continue
                document += f"\n### {category}\n"
                for tool in self.tools_map[lang][category]:
                    document += f"\n#### {tool}\n"
                    document += f"{self.tools_usage_examples[lang][category][tool]}\n"

        return document

    def run(self) -> None:
        """Run the knowledge generation process."""
        # Detect languages
        langs = self.detect_languages()

        print(colored("\n📚 Project Languages:", "green"))
        for i, lang in enumerate(langs, 1):
            print(colored(f"  {i}. {lang}", "green"))
        print()

        # Analyze languages and categories
        self.analyze_languages(langs)

        # Analyze general categories
        self.analyze_general_categories()

        # Analyze tools
        self.analyze_tools()

        # Generate document
        document = self.generate_document(langs)

        # Save document
        with open("knowledge.md", "w") as f:
            f.write(document)
            print(f"Knowledge doc saved to {f.name}")
            print(f"{len(document.splitlines())} lines in knowledge doc.")
            print(f"~{len(document) // 3.5} tokens in knowledge doc.")
            print(f"Total time: {time.time() - self.start_time:.2f} seconds.")
            print("Done!")


def main():
    """Main entry point for the knowledge generator."""
    generator = KnowledgeGenerator()
    generator.run()


if __name__ == "__main__":
    main()

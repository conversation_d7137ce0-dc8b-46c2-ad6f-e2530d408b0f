Your goal is to successfully run test commands from my repository inside the container.

I have two files:
 * Dockerfile - read-only image build file.
 * setup.sh - editable script that runs a sequence of commands to set up the container before running the test command.
 * The repository is mounted into the container at '/home/<USER>/repo/'

Here is the command you should run to build the image:
docker build -f experimental/lior/container_setup_agent/Dockerfile -t container-setup-agent .

Here is the command you should run to run the container:
docker run -v $(pwd)/experimental/lior/container_setup_agent/setup.sh:/home/<USER>/repo/experimental/lior/container_setup_agent/setup.sh --rm -it container-setup-agent <test command>

### Environment build steps:
 * Search for all testing frameworks in the repo.
 * Check for build systems in the repo.
 * Check for virtual environment managers in the repo.
 * Search for all dependency declarations in the repo (such as requirements.txt, package.json, etc.).
 * Add dependencies installation commands to setup.sh.
 * Create a candidate test command to run based on repo.
 * Run the container with a test command.
 * If it fails, analyze the error and add any missing setup commands to setup.sh.
 * Repeat until the test command runs successfully.

### Key guidelines:
 * Never edit any file except setup.sh
 * Never use git commands
 * Always prefer to install packages from file declarations (such as requirements.txt) instead of installing them individually.

"""Create and build a Dockerfile for running test commands based on project knowledge."""

import os
import sys
import logging
import tempfile
import subprocess
from pathlib import Path
from typing import Optional, Tuple
from .knowledge_generator import ExpertAgent

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def build_docker_image(
    dockerfile_path: Path, image_name: str, max_retries: int = 3
) -> Tuple[bool, str]:
    """Build a Docker image from a Dockerfile.

    Args:
        dockerfile_path: Path to the Dockerfile
        image_name: Name to give the built image
        max_retries: Maximum number of build attempts

    Returns:
        Tuple of (success, output)
    """
    for attempt in range(max_retries):
        try:
            logger.info(f"Building Docker image (attempt {attempt+1}/{max_retries})...")
            result = subprocess.run(
                ["docker", "build", "-t", image_name, "-f", str(dockerfile_path), "."],
                check=True,
                capture_output=True,
                text=True,
            )
            logger.info(f"Successfully built Docker image: {image_name}")
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            logger.error(f"Build attempt {attempt+1}/{max_retries} failed")
            logger.error(f"Stdout: {e.stdout}")
            logger.error(f"Stderr: {e.stderr}")
            if attempt == max_retries - 1:
                return False, e.stderr

    return False, "Maximum retries exceeded"


def run_docker_container(image_name: str, test_command: str) -> Tuple[bool, int, str]:
    """Run a command in a Docker container.

    Args:
        image_name: Name of the Docker image to run
        test_command: Command to execute in the container

    Returns:
        Tuple of (success, exit_code, output)
    """
    try:
        logger.info(f"Running test command in Docker container: {test_command}")
        result = subprocess.run(
            ["docker", "run", "--rm", "-it", image_name, "bash", "-c", test_command],
            check=False,  # Don't raise exception on non-zero exit
            capture_output=True,
            text=True,
        )
        success = result.returncode == 0
        if success:
            logger.info("Test command executed successfully")
        else:
            logger.error(f"Test command failed with exit code: {result.returncode}")
            logger.error(f"Stderr: {result.stderr}")

        return success, result.returncode, result.stdout + result.stderr
    except Exception as e:
        logger.error(f"Error running Docker container: {e}")
        return False, -1, str(e)


def create_dockerfile(
    test_command: str, knowledge_path: Optional[Path] = None, verbose: bool = True
) -> Tuple[bool, str, Optional[Path]]:
    """Create a Dockerfile for running the test command.

    Args:
        test_command: The test command to run in the container
        knowledge_path: Path to the knowledge.md file
        verbose: Whether to enable verbose mode in the ExpertAgent

    Returns:
        Tuple of (success, image_name, dockerfile_path)
    """
    try:
        # Use default path if none provided
        if knowledge_path is None:
            knowledge_path = Path("/home/<USER>/augment/knowledge.md")

        # Ensure the knowledge file exists
        if not knowledge_path.exists():
            raise FileNotFoundError(f"Knowledge file not found at {knowledge_path}")

        # Read the knowledge file
        logger.info(f"Reading knowledge file from {knowledge_path}")
        with open(knowledge_path) as f:
            knowledge = f.read()

        # Create a temporary directory for the Dockerfile
        temp_dir = tempfile.mkdtemp()
        dockerfile_path = Path(temp_dir) / "Dockerfile"

        logger.info(f"Creating Dockerfile at {dockerfile_path}")

        prompt = f"""
        Below is a complete documentation of the codebase.
        {knowledge}

        Task: Create and build a Dockerfile that sets up an environment to run the following test command: `{test_command}`.
        Carefully review the documentation to understand the project's dependencies, tools, and environment setup required to run the test command.

        PROCESS:
        1. According to the documentation, create a new Dockerfile at '{dockerfile_path}':
           - Find the appropriate base image for the project
           - Install all necessary dependencies defined in the documentation
           - Install all necessary tools defined in the documentation
           - Copy the entire codebase into the container at /home/<USER>/repo/
           - Set up the environment as defined in the documentation
           - Use multi-stage builds where appropriate to reduce image size
           - Combine RUN commands to reduce layer count
           - Clean up package manager caches after installation

        2. Build the Dockerfile by running the following command:
           docker build -t <image_name> {dockerfile_path}

        3. Run the Docker image by running the following command:
           docker run --rm -it <image_name> bash -c "{test_command}"

        4. After successfully validating that the test command runs without errors in the Docker container, use the complete tool to submit your final answer.

        5. Iterate on steps 1-4 until the test command runs successfully.

        IMPORTANT: NEVER SUBMIT AN ANSWER UNTIL THE TEST COMMAND RUNS SUCCESSFULLY IN THE DOCKER CONTAINER.

        Your answer must contain the name of the built Docker image, without any prefix or suffix.
        """

        # Initialize the expert agent and get the answer
        logger.info("Generating Dockerfile and building image")
        expert = ExpertAgent(verbose=verbose)
        image_name = expert.answer_question(prompt)

        # Check if the Dockerfile was created
        if not dockerfile_path.exists():
            logger.error(f"Dockerfile was not created at {dockerfile_path}")
            return False, "", None

        # Display the Dockerfile content
        with open(dockerfile_path, "r") as f:
            dockerfile_content = f.read()
            logger.info(f"Generated Dockerfile:\n{dockerfile_content}")

        logger.info(f"Docker image name: {image_name}")
        return True, image_name, dockerfile_path

    except FileNotFoundError as e:
        logger.error(f"File error: {e}")
        return False, "", None
    except Exception as e:
        logger.error(f"Error creating Dockerfile: {e}")
        return False, "", None


def main() -> int:
    """Main entry point for the script.

    Returns:
        Exit code (0 for success, non-zero for failure)
    """
    try:
        # Get test command from command line argument
        if len(sys.argv) < 2:
            logger.error("Missing test command argument")
            print("Usage: python create_docker.py <test_command>")
            return 1

        test_command = sys.argv[1]
        logger.info(f"Creating Docker container for test command: {test_command}")

        # Create the Dockerfile
        success, image_name, dockerfile_path = create_dockerfile(test_command)
        if not success or not dockerfile_path:
            logger.error("Failed to create Dockerfile")
            return 1

        # Build the Docker image
        build_success, build_output = build_docker_image(dockerfile_path, image_name)
        if not build_success:
            logger.error("Failed to build Docker image")
            return 1

        # Run the test command in the container
        run_success, exit_code, run_output = run_docker_container(
            image_name, test_command
        )
        if not run_success:
            logger.error("Test command failed in Docker container")
            return exit_code

        logger.info("Success! Test command executed successfully in Docker container")
        return 0

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

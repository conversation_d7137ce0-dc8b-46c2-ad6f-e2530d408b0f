"""Logging configuration for the Python interpreter agent."""

import logging
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional

# Global logger instance
logger = logging.getLogger("python_interpreter")


def setup_logging(debug: bool = False, log_file: Optional[str] = None) -> None:
    """Configure logging with optional debug mode and file output.

    Args:
        debug: If True, set log level to DEBUG
        log_file: Optional file path to write logs to
    """
    # Clear any existing handlers
    logger.handlers.clear()

    # Set log level based on debug flag
    log_level = logging.DEBUG if debug else logging.INFO
    logger.setLevel(log_level)

    # Console handler with rich formatting - disable markup to avoid escaping issues
    console_handler = RichHandler(rich_tracebacks=True, markup=False, show_time=False)
    console_handler.setLevel(log_level)
    logger.addHandler(console_handler)

    # File handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(log_level)
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # Prevent propagation to root logger
    logger.propagate = False

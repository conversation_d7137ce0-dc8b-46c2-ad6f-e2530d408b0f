"""Tools for the Python interpreter agent."""

from pathlib import Path
from typing import Dict, Any, Optional, List
from research.agents.tools import (
    <PERSON><PERSON><PERSON>,
    ToolCallLogger,
    ToolImplOutput,
    DialogMessages,
)
from .interpreter import PythonInterpreter
from .logger import logger
from .builtins import truncate_middle


class PythonInterpreterTool(LLMTool):
    """Tool that provides Python interpreter functionality."""

    name = "python_interpreter"
    description = f"""Execute Python code and maintain state between executions.

 * Built-in functions available:
```
def ask_expert(question: str, context: str, ref: Optional[str] = None) -> str:
    \"\"\"
    Ask an expert specific question about codebase given context.

    Args:
        question (str): Concise one sentence question to ask.
        context (str): All relevant external non-code information (include user messages, git diff, PR title, PR body, comments, commit messages, etc.)
        ref (Optional[str]): Git reference (commit hash or branch name) to analyze, defaults to current HEAD.

    Returns (str):
        The expert's answer as a string
    \"\"\"

def request_edit(edit_description: str, context: str) -> str:
    \"\"\"
    Request an edit to the codebase given context.

    Args:
        edit_description (str): Concise one sentence description of the edit to make.
        context (str): All relevant external non-code information (include user messages, git diff, PR title, PR body, comments, commit messages, etc.)

    Returns (str):
        The expert's answer as a string
    \"\"\"

def run_command(cmd: str) -> str:
    \"\"\"
    Run a shell command and return its output.

    Args:
        cmd (str): The command to run

    Returns (str):
        The command's output (stdout and stderr combined) as a string
    \"\"\"

def find_symbol_references(symbol: str, search_path: Optional[Path] = None) -> List[SymbolReference]:
    \"\"\"
    Find all references to a symbol across files.

    Args:
        symbol: The symbol name to search for
        search_path: Optional[Path] = None # Optional path to search for files, defaults to current working directory

    Returns:
        List of SymbolReference objects containing reference locations

    class SymbolReference:
        file_path: Path
        line: int
        column: int
        symbol: str
        context: str  # The line of code containing the reference
    "\"\"
```

 * The expert can only analyze/edit one file/problem at a time, use python loop to analyze/edit a batch of files/problems.
 * Provide as much context as possible when asking the expert a question.
 * When asking a yes/no question, the expert will always include 'ANSWER_YES' if the answer is *yes*.
 * Make sure to provide the ref parameter if you want to analyze a specific commit or branch.
 * The 'ref' value for a specific pull request is 'pull/{{pull_request_number}}/head'.
 * Use find_symbol_references() or run_command('grep ...') for fast pattern searching.
 * Avoid printing large amounts of text.
 * Never write to files, instead use variables to store results.
 * Current working directory is '{Path.cwd().absolute()}'.
"""
    input_schema = {
        "type": "object",
        "properties": {
            "code": {
                "type": "string",
                "description": "Python code to execute. Has access to built-in functions ask_expert() and run_command()",
            }
        },
        "required": ["code"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, interpreter: PythonInterpreter
    ):
        """Initialize the tool with empty state."""
        super().__init__(tool_call_logger)
        self.interpreter = interpreter

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Execute Python code and return output with updated state."""
        code = tool_input["code"]
        logger.debug(f"Running code:\n{code}")

        output, error = self.interpreter.execute(code)
        if error:
            if output:
                output += "\n\n"
            output += f"Error: {error}"
            logger.error(f"Execution error: {error}")
            return ToolImplOutput(
                tool_output=truncate_middle(output),
                tool_result_message=f"Failed to execute Python code: {error}",
            )

        logger.debug(f"Execution successful, output: {output}")
        return ToolImplOutput(
            tool_output=truncate_middle(output),
            tool_result_message=f"Executed Python code: {code[:50]}...",
        )

    def get_tool_start_message(self, tool_input: Dict[str, Any]) -> str:
        """Get message indicating tool execution is starting."""
        return "Executing Python code..."


class CompleteTool(LLMTool):
    """Tool that reports the final answer by executing Python code.

    The code should use print statements to output the final answer.
    All output from print statements will be considered part of the answer.
    """

    name = "complete"
    description = "Report the final answer by executing Python code."
    input_schema = {
        "type": "object",
        "properties": {
            "code": {
                "type": "string",
                "description": "Python code that prints the final answer.",
            }
        },
        "required": ["code"],
    }

    def __init__(
        self, tool_call_logger: ToolCallLogger, interpreter: PythonInterpreter
    ):
        """Initialize the tool with the Python interpreter."""
        super().__init__(tool_call_logger)
        self.interpreter = interpreter

    def run_impl(
        self,
        tool_input: Dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        """Execute code that prints the final answer."""
        code = tool_input["code"]
        logger.debug(f"Executing answer code: {code}")

        output, error = self.interpreter.execute(code)

        if error:
            logger.error(f"Error executing answer code: {error}")
            return ToolImplOutput(
                tool_output=f"Error: Failed to execute answer code - {error}",
                tool_result_message="Failed to report answer",
            )

        return ToolImplOutput(
            tool_output=output,
            tool_result_message="Reported final answer",
        )

    def get_tool_start_message(self, tool_input: Dict[str, Any]) -> str:
        """Get message indicating tool execution is starting."""
        return "Reporting final answer..."

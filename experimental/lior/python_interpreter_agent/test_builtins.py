"""Tests for built-in functions."""

import pytest
import subprocess
from unittest.mock import patch
from pathlib import Path
import tempfile
from base.static_analysis.common import LanguageID
from research.agents.tools import ToolCall
from . import builtins as builtins_module
from .builtins import (
    ExpertAgent,
    ask_expert,
    get_expert,
    run_command,
    find_symbol_references,
    request_edit,
    ContextRequestException,
)


@pytest.fixture
def git_repo():
    """Create a temporary git repository for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Initialize git repo with main branch
        subprocess.run(["git", "init", "-b", "main"], cwd=temp_path, check=True)
        subprocess.run(
            ["git", "config", "user.name", "Test User"], cwd=temp_path, check=True
        )
        subprocess.run(
            ["git", "config", "user.email", "<EMAIL>"],
            cwd=temp_path,
            check=True,
        )

        # Create some initial files
        (temp_path / "README.md").write_text("# Test Repository")
        (temp_path / "main.py").write_text("def main():\n    return 42\n")

        # Commit files
        subprocess.run(["git", "add", "."], cwd=temp_path, check=True)
        subprocess.run(
            ["git", "commit", "-m", "Initial commit"], cwd=temp_path, check=True
        )

        yield temp_path


def test_run_command_success():
    """Test that run_command successfully executes a command and returns output."""
    result = run_command("echo 'test output'")
    assert "test output" in result
    assert result.strip() == "test output"


def test_run_command_failure():
    """Test that run_command raises exception for command failures."""
    with pytest.raises(Exception):
        run_command("nonexistent_command")


def test_run_command_with_error_code():
    """Test that run_command raises exception for non-zero exit codes."""
    with pytest.raises(Exception):
        run_command("bash -c 'exit 42'")


def test_run_command_detects_yes_no_prompt():
    """Test that run_command detects and raises exception for yes/no prompts."""
    script = r"""
import sys
print("Proceed? [y/n]: ", end="", flush=True)
sys.stdout.flush()
while True:
    pass
"""
    cmd = f"python3 -c '{script}'"
    with pytest.raises(Exception) as exc_info:
        run_command(cmd)
    assert "waiting for user input" in str(exc_info.value)


def test_run_command_detects_password_prompt():
    """Test that run_command detects and raises exception for password prompts."""
    script = r"""
import sys
print("Password: ", end="", flush=True)
sys.stdout.flush()
while True:
    pass
"""
    cmd = f"python3 -c '{script}'"
    with pytest.raises(Exception) as exc_info:
        run_command(cmd)
    assert "waiting for user input" in str(exc_info.value)


def test_run_command_detects_sudo_prompt():
    """Test that run_command detects and raises exception for sudo prompts."""
    script = r"""
import sys
print("[sudo] password for user: ", end="", flush=True)
sys.stdout.flush()
while True:
    pass
"""
    cmd = f"python3 -c '{script}'"
    with pytest.raises(Exception) as exc_info:
        run_command(cmd)
    assert "waiting for user input" in str(exc_info.value)


def test_run_command_detects_git_log_prompt(git_repo):
    """Test that run_command detects and raises exception for git log interactive prompt."""
    # Create a minimal git repo with just one commit
    test_file = git_repo / "test.txt"
    test_file.write_text("test content")
    subprocess.run(["git", "add", "test.txt"], cwd=git_repo, check=True)
    subprocess.run(["git", "commit", "-m", "test commit"], cwd=git_repo, check=True)

    # Add debug print to see what's being sent to _looks_like_interactive_prompt
    original_func = builtins_module._looks_like_interactive_prompt

    def debug_wrapper(command: str, output: str) -> bool:
        print(f"\nDEBUG: Command being checked: '{command}'")
        print(f"DEBUG: Output being checked (length: {len(output)}):")
        print("---BEGIN OUTPUT---")
        print(output)
        print("---END OUTPUT---")
        result = original_func(command, output)
        print(f"DEBUG: LLM returned: {result}")
        return result

    with patch(
        "experimental.lior.python_interpreter_agent.builtins._looks_like_interactive_prompt",
        side_effect=debug_wrapper,
    ):
        with pytest.raises(Exception) as exc_info:
            # Use git log without flags to get interactive pager
            run_command("git log")
        assert "waiting for user input" in str(exc_info.value)


def test_expert_agent_initialization(git_repo):
    """Test that ExpertAgent initializes correctly."""
    expert = ExpertAgent(ref="main", workspace_root=git_repo)
    assert expert.temp_dir.exists()
    assert expert.temp_dir.is_dir()
    assert (expert.temp_dir / ".git").exists()


def test_ask_expert_with_ref(git_repo):
    """Test ask_expert function with explicit ref."""
    response = ask_expert(
        question="What is the purpose of the main function?",
        context="Looking at the main function implementation",
        ref="main",
        workspace_root=git_repo,
    )
    assert isinstance(response, str)
    assert len(response) > 0
    assert "main" in response


def test_ask_expert_without_ref(git_repo):
    """Test ask_expert function without ref (uses current HEAD)."""
    response = ask_expert(
        question="What is the purpose of the main function?",
        context="Looking at the main function implementation",
        workspace_root=git_repo,
    )
    assert isinstance(response, str)
    assert len(response) > 0
    assert "main" in response  # Response should mention main function


def test_expert_caching_with_same_ref(git_repo):
    """Test that ExpertAgent instances are cached when using same ref."""
    expert1 = get_expert("main", workspace_root=git_repo)
    expert2 = get_expert("main", workspace_root=git_repo)
    assert expert1 is expert2
    assert expert1.temp_dir.exists()
    assert expert2.temp_dir.exists()


def test_expert_caching_with_different_refs(git_repo):
    """Test that ExpertAgent instances are separate for different refs."""
    # Create a new branch with different content
    subprocess.run(["git", "checkout", "-b", "feature"], cwd=git_repo, check=True)
    (git_repo / "main.py").write_text("def main():\n    return 43\n")
    subprocess.run(["git", "commit", "-am", "Update main"], cwd=git_repo, check=True)
    subprocess.run(["git", "checkout", "main"], cwd=git_repo, check=True)

    expert1 = get_expert("main", workspace_root=git_repo)
    expert2 = get_expert("feature", workspace_root=git_repo)
    assert expert1 is not expert2
    assert expert1.temp_dir.exists()
    assert expert2.temp_dir.exists()


def test_expert_agent_edit_tool(git_repo):
    """Test that ExpertAgent can successfully edit files."""
    # Create a test file
    test_file = git_repo / "test_hello.py"
    test_file.write_text("def hello():\n    return 'world'\n")
    subprocess.run(["git", "add", "test_hello.py"], cwd=git_repo, check=True)
    subprocess.run(
        ["git", "commit", "-m", "Add hello function"], cwd=git_repo, check=True
    )

    expert = ExpertAgent(workspace_root=git_repo)

    # Use the edit tool through answer_question
    question = """Please edit ./test_hello.py to change the return value in the hello() function from 'world' to 'hello world'. The function should look like:
    def hello():
        return 'hello world'
    """
    response = expert.answer_question(question)

    # Verify the edit was successful
    assert test_file.exists()
    content = test_file.read_text()
    assert "return 'hello world'" in content
    assert response and isinstance(response, str)


def test_find_symbol_references():
    """Test finding symbol references in Python files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create test files
        test_files = {
            "test1.py": """
def hello():
    print("Hello")

def test():
    hello()
""",
            "test2.py": """
from test1 import hello

def main():
    hello()
""",
        }

        for filename, content in test_files.items():
            file_path = temp_path / filename
            file_path.write_text(content)

        # Find references to 'hello'
        refs = find_symbol_references("hello", search_path=temp_path)

        # Verify references found
        assert len(refs) == 4  # Should find 4 references to 'hello' (including import)

        # Verify reference details
        ref_locations = [(ref.file_path.name, ref.symbol) for ref in refs]
        expected = [
            ("test1.py", "hello"),  # Function definition
            ("test1.py", "hello"),  # Function call
            ("test2.py", "hello"),  # Import statement
            ("test2.py", "hello"),  # Function call
        ]
        assert sorted(ref_locations) == sorted(expected)

        # Verify line numbers are 1-based
        assert all(ref.line > 0 for ref in refs)
        assert all(ref.column > 0 for ref in refs)

        # Verify context is captured
        assert any("def hello" in ref.context for ref in refs)
        assert any("hello()" in ref.context for ref in refs)
        assert any("import hello" in ref.context for ref in refs)


def test_find_symbol_references_multi_language():
    """Test finding symbol references across multiple languages."""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create test files in different languages
        test_files = {
            "main.py": """
def calculate():
    return sum([1, 2, 3])
""",
            "script.js": """
function calculate() {
    return [1, 2, 3].reduce((a, b) => a + b, 0);
}
""",
            "program.java": """
public class Program {
    public int calculate() {
        return IntStream.of(1, 2, 3).sum();
    }
}
""",
        }

        for filename, content in test_files.items():
            file_path = temp_path / filename
            file_path.write_text(content)

        # Find references to 'calculate' across all languages in a single call
        refs = find_symbol_references("calculate", search_path=temp_path)

        # Should find one reference in each file
        assert len(refs) == 3, f"Expected 3 references, got {len(refs)}: {refs}"

        # Verify we found references in each language
        file_extensions = {ref.file_path.suffix for ref in refs}
        assert file_extensions == {".py", ".js", ".java"}

        # Verify each reference is a function definition
        for ref in refs:
            if ref.file_path.suffix == ".py":
                assert "def calculate" in ref.context
            elif ref.file_path.suffix == ".js":
                assert "function calculate" in ref.context
            elif ref.file_path.suffix == ".java":
                assert "calculate()" in ref.context


def test_request_edit(git_repo):
    """Test request_edit function."""
    # Create a test file
    test_file = git_repo / "test_hello.py"
    test_file.write_text("def hello():\n    pass\n")
    subprocess.run(["git", "add", "test_hello.py"], cwd=git_repo, check=True)
    subprocess.run(
        ["git", "commit", "-m", "Add hello function"], cwd=git_repo, check=True
    )

    response = request_edit(
        edit_description="Add a docstring to the hello function in ./test_hello.py",
        context="""
        The hello function is a simple greeting function that should return a greeting.
        It needs a proper docstring explaining that it's a function that returns a greeting.
        """,
        workspace_root=git_repo,
    )
    assert isinstance(response, str)
    assert len(response) > 0

    # Verify the edit was made
    content = test_file.read_text()
    assert '"""' in content or "'''" in content  # Check for docstring


def test_expert_agent_cleanup(git_repo):
    """Test that ExpertAgent cleans up temp directories."""
    expert = ExpertAgent(ref="main", workspace_root=git_repo)
    temp_dir = expert.temp_dir
    assert temp_dir.exists()
    del expert
    assert not temp_dir.exists()


def test_expert_agent_context_request(git_repo):
    """Test that ExpertAgent can request additional context."""
    expert = ExpertAgent(workspace_root=git_repo)

    # Mock the LLM client to return a request_context tool call
    with patch.object(expert, "client") as mock_client:
        mock_client.generate.return_value = (
            [
                ToolCall(
                    tool_name="request_context",
                    tool_call_id="1",
                    tool_input={
                        "context_request": "Need more details about the function"
                    },
                )
            ],
            None,
        )

        with pytest.raises(ContextRequestException) as exc_info:
            expert.answer_question(
                "What is the implementation of nonexistent_function?"
            )
        assert "need more details" in str(exc_info.value).lower()


def test_expert_agent_tools_initialization(git_repo):
    """Test that ExpertAgent initializes all required tools."""
    expert = ExpertAgent(workspace_root=git_repo)
    assert expert.codebase_tool is not None
    assert expert.read_file_tool is not None
    assert expert.edit_file_tool is not None
    assert expert.complete_tool is not None
    assert expert.tool_logger is not None


def test_expert_agent_answer_question_with_tools(git_repo):
    """Test that ExpertAgent can use tools to answer questions."""
    expert = ExpertAgent(workspace_root=git_repo)

    # Create a test file
    test_file = git_repo / "test_function.py"
    test_file.write_text("def test_function():\n    return 42\n")
    subprocess.run(["git", "add", "test_function.py"], cwd=git_repo, check=True)
    subprocess.run(
        ["git", "commit", "-m", "Add test function"], cwd=git_repo, check=True
    )

    response = expert.answer_question(
        "What does the function in ./test_function.py return?"
    )
    assert isinstance(response, str)
    assert "42" in response


def test_expert_agent_invalid_ref(git_repo):
    """Test that ExpertAgent handles invalid git refs gracefully."""
    with pytest.raises(Exception) as exc_info:
        ExpertAgent(ref="nonexistent_branch_12345", workspace_root=git_repo)
    assert "couldn't find remote ref" in str(exc_info.value)


def test_ask_expert_yes_no_question(git_repo):
    """Test that ask_expert includes ANSWER_YES for yes/no questions with positive answers."""
    # Create a test file with a clear yes/no testable property
    test_file = git_repo / "test_function.py"
    test_file.write_text("""
def add_numbers(a: int, b: int) -> int:
    \"\"\"Add two numbers and return the result.\"\"\"
    return a + b
""")
    subprocess.run(["git", "add", "test_function.py"], cwd=git_repo, check=True)
    subprocess.run(
        ["git", "commit", "-m", "Add function with type hints"],
        cwd=git_repo,
        check=True,
    )

    # Ask a yes/no question that should get a positive answer
    response = ask_expert(
        question="Does the add_numbers function have type hints?",
        context="Looking at the type hints in the function definition",
        workspace_root=git_repo,
    )

    assert "ANSWER_YES" in response

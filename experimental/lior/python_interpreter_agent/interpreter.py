"""Module for Python code execution with state management."""

import sys
import signal
from io import <PERSON><PERSON>
from typing import <PERSON><PERSON>, Dict, Any
from experimental.lior.python_interpreter_agent.state import InterpreterState
from .builtins import run_command, ask_expert, request_edit, find_symbol_references
from .logger import logger


class TimeoutError(Exception):
    """Raised when code execution times out."""

    pass


class PythonInterpreter:
    """Executes Python code while maintaining state."""

    DEFAULT_TIMEOUT = 30 * 60  # 30 minutes in seconds

    def __init__(self):
        """Initialize interpreter with empty state."""
        self.state = InterpreterState(locals={})
        self.globals = {
            "__builtins__": __builtins__,
            "run_command": run_command,
            "ask_expert": ask_expert,
            "request_edit": request_edit,
            "find_symbol_references": find_symbol_references,
        }
        logger.debug("Initialized PythonInterpreter")

    def _timeout_handler(self, signum, frame):
        raise TimeoutError("Code execution timed out")

    def execute(self, code: str, timeout: int = DEFAULT_TIMEOUT) -> Tuple[str, str]:
        """
        Execute Python code and return output and errors.

        Args:
            code: Python code to execute
            timeout: Maximum execution time in seconds (default: 30 minutes)

        Returns:
            Tuple of (output including both stdout and stderr, error message if any)
        """
        # Capture output using a single StringIO for both stdout and stderr
        output_capture = StringIO()
        sys_stdout = sys.stdout
        sys_stderr = sys.stderr

        # Set up timeout handler
        old_handler = signal.signal(signal.SIGALRM, self._timeout_handler)
        signal.alarm(timeout)

        try:
            # Redirect both stdout and stderr to the same StringIO
            sys.stdout = output_capture
            sys.stderr = output_capture

            logger.debug(f"Executing code:\n```python\n{code}\n```")

            # Execute the code in the current state
            exec(code, self.globals, self.globals)  # Use globals for both namespaces

            output = output_capture.getvalue()
            logger.debug(f"Execution output: {output}")
            return output, ""

        except TimeoutError as e:
            error_msg = str(e)
            logger.error(
                f"Execution timeout after {timeout} seconds: {error_msg}, please optimize the code or break the task into smaller ones and run them separately."
            )
            return output_capture.getvalue(), error_msg

        except Exception as e:
            error_msg = str(e)
            logger.error(f"Execution error: {error_msg}")
            return output_capture.getvalue(), error_msg

        finally:
            signal.alarm(0)  # Disable alarm
            signal.signal(signal.SIGALRM, old_handler)  # Restore old handler
            sys.stdout = sys_stdout
            sys.stderr = sys_stderr

    def get_state(self) -> Dict[str, Any]:
        """Get current interpreter state."""
        state = {
            k: v
            for k, v in self.globals.items()
            if k
            not in [
                "__builtins__",
                "run_command",
                "ask_expert",
                "request_edit",
                "find_symbol_references",
            ]
        }
        logger.debug(f"Current state: {state}")
        return state

    def set_state(self, state: Dict[str, Any]) -> None:
        """Set interpreter state."""
        logger.debug(f"Setting state: {state}")
        self.globals.update(state)

    def clear_state(self) -> None:
        """Clear interpreter state."""
        logger.debug("Clearing interpreter state")
        self.globals = {
            "__builtins__": __builtins__,
            "run_command": run_command,
            "ask_expert": ask_expert,
            "request_edit": request_edit,
            "find_symbol_references": find_symbol_references,
        }

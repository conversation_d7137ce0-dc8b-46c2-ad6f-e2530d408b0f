"""Built-in functions for the Python interpreter."""

import os
import subprocess
import tempfile
from pathlib import Path
import time  # Add time import
from typing import Dict, Optional, List, cast, Union
from dataclasses import dataclass
from research.llm_apis.llm_client import (
    GeneralContentBlock,
    get_client,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
)
from research.agents.tools import ToolCallLogger
from experimental.guy.agent_qa.builtin_tools import (
    ReadFileTool,
    BackendCodebaseRetrievalTool,
    CompleteTool,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_staging_api_proxy_url,
)
from experimental.guy.agent_qa.file_edit.str_replace_editor_tool import (
    StrReplaceEditorTool,
)
from base.augment_client.client import AugmentClient
from base.static_analysis.parsing import (
    GlobalTsParser,
    TsParsedFile,
    LanguageID,
    get_nodes_of_types,
)


def truncate_middle(s: str, max_len: int = 50000) -> str:
    if len(s) > max_len:
        half = max_len // 2
        return (
            s[:half] + f"... [output truncated to {max_len} characters] ..." + s[-half:]
        )
    return s


class ContextRequestException(Exception):
    """Raised when more context is needed to execute a command."""

    pass


def _looks_like_interactive_prompt(command: str, output: str) -> bool:
    """Check if the command appears to be waiting for user input.

    Args:
        command: Command to run
        output: Command output so far

    Returns:
        True if command appears to be waiting for user input
    """
    # For git log, check if output ends with a colon
    if command == "git log" and output.rstrip().endswith(":"):
        return True

    # For other commands, check with LLM but limit output size
    client = get_client(
        "anthropic-direct", model_name="claude-3-5-sonnet-20241022", max_retries=50
    )

    prompt = f"""
    Command: '{command}'
    Output so far: '{output}'
    Is the command waiting for user input? Respond 'ANSWER_YES' if the command is waiting for user input.
    """
    response, _ = client.generate(
        messages=[[TextPrompt(text=prompt)]],
        max_tokens=1024,
        system_prompt="",
        temperature=0.0,
    )
    assert isinstance(response[0], TextResult)
    return "ANSWER_YES" in response[0].text


def run_command(cmd: str) -> str:
    """
    Run a shell command and return its output.

    Args:
        cmd (str): The command to run

    Returns (str):
        The command's output (stdout and stderr combined) as a string

    Raises:
        subprocess.TimeoutExpired: If command execution times out
        Exception: For other execution errors, non-zero exit codes, or if command appears to be waiting for input
    """
    try:
        process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # Redirect stderr to stdout
            text=True,
            bufsize=1,  # Line buffered
        )

        # Set stdout to non-blocking mode
        assert process.stdout is not None
        os.set_blocking(process.stdout.fileno(), False)

        output = []
        last_check_time = time.time()
        start_time = time.time()
        check_interval = 5.0  # Start with 5 seconds

        while True:
            line = process.stdout.readline()
            if not line and process.poll() is not None:
                break

            output.append(line)
            current_output = "".join(output)

            # Check for interactive prompts with exponential backoff
            current_time = time.time()
            if current_time - start_time >= 300:
                process.kill()
                raise Exception(
                    f"Command timed out after 300 seconds: {cmd}, please optimize the command or break the task into smaller ones and run them separately."
                )
            if current_time - last_check_time >= check_interval:
                print(
                    f"Checking if command is waiting for input after {check_interval} seconds"
                )
                if _looks_like_interactive_prompt(cmd, truncate_middle(current_output)):
                    process.kill()
                    raise Exception(
                        f"Command appears to be waiting for user input: {cmd}\nOutput so far: {current_output}"
                    )
                last_check_time = current_time
                check_interval = min(
                    check_interval * 2, 60
                )  # Double interval up to max 60 seconds

        final_output = "".join(output)

        if process.returncode != 0:
            raise Exception(
                f"Command failed with exit code {process.returncode}:\n{final_output}"
            )

        return truncate_middle(final_output)

    except Exception:
        raise  # Re-raise any other exception


@dataclass
class SymbolReference:
    """A reference to a symbol in code."""

    file_path: Path
    line: int
    column: int
    symbol: str
    context: str  # The line of code containing the reference


def find_symbol_references(
    symbol: str, search_path: Optional[Path] = None
) -> List[SymbolReference]:
    """Find all references to a symbol across files.

    Args:
        symbol: The symbol name to search for
        search_path: Optional path to search for files, defaults to current working directory

    Returns:
        List of SymbolReference objects containing reference locations

    class SymbolReference:
        file_path: Path
        line: int
        column: int
        symbol: str
        context: str  # The line of code containing the reference
    """
    references = []
    if search_path is None:
        search_path = Path.cwd()

    # Common programming language extensions
    patterns = [
        "**/*.py",  # Python
        "**/*.js",
        "**/*.jsx",  # JavaScript
        "**/*.ts",
        "**/*.tsx",  # TypeScript
        "**/*.java",  # Java
        "**/*.cpp",
        "**/*.hpp",
        "**/*.cc",  # C++
        "**/*.rs",  # Rust
        "**/*.go",  # Go
        "**/*.cs",  # C#
        "**/*.php",  # PHP
        "**/*.html",  # HTML
        "**/*.dart",  # Dart
        "**/*.css",  # CSS
        "**/*.sh",  # Bash
        "**/*.scala",  # Scala
        "**/*.rb",  # Ruby
        "**/*.lua",  # Lua
        "**/*.sql",  # SQL
        "**/*.kt",  # Kotlin
        "**/*.md",  # Markdown
    ]
    files: List[Path] = []
    for pattern in patterns:
        files.extend(search_path.glob(pattern))

    # Exclude big files
    files = [f for f in files if f.is_file() and f.stat().st_size < 1000000]

    # Fast pass
    def check_symbol_in_file(f: Path, symbol: str) -> bool:
        try:
            return symbol in f.read_text()
        except UnicodeDecodeError:
            return False

    files = [f for f in files if check_symbol_in_file(f, symbol)]

    for file_path in files:
        try:
            # Determine language from file extension
            ext = file_path.suffix.lstrip(".")
            # Map file extensions to tree-sitter language IDs
            lang_map: Dict[str, LanguageID] = {
                # Python
                "py": cast(LanguageID, "python"),
                # JavaScript/TypeScript
                "js": cast(LanguageID, "javascript"),
                "jsx": cast(LanguageID, "javascript"),
                "ts": cast(LanguageID, "typescript"),
                "tsx": cast(LanguageID, "typescript"),
                # JVM languages
                "java": cast(LanguageID, "java"),
                "kt": cast(LanguageID, "kotlin"),
                "scala": cast(LanguageID, "scala"),
                # Web
                "html": cast(LanguageID, "html"),
                "css": cast(LanguageID, "css"),
                # C-family
                "cpp": cast(LanguageID, "cpp"),
                "hpp": cast(LanguageID, "cpp"),
                "cc": cast(LanguageID, "cpp"),
                "cs": cast(LanguageID, "c_sharp"),
                # Other languages
                "rs": cast(LanguageID, "rust"),
                "go": cast(LanguageID, "go"),
                "rb": cast(LanguageID, "ruby"),
                "php": cast(LanguageID, "php"),
                "dart": cast(LanguageID, "dart"),
                "lua": cast(LanguageID, "lua"),
                "sql": cast(LanguageID, "sql"),
                "sh": cast(LanguageID, "bash"),
                "md": cast(LanguageID, "markdown"),
            }

            lang = lang_map.get(ext)
            if not lang:
                continue

            # Parse file with tree-sitter
            code = file_path.read_text()
            parsed_file = TsParsedFile.parse(
                file_path, lang, code, parser=GlobalTsParser
            )

            # Find all identifier nodes
            identifier_types = ["identifier"]
            if lang == "python":
                identifier_types.extend(["function_definition", "call"])
            elif lang == "javascript":
                identifier_types.extend(["function_declaration", "call_expression"])
            elif lang == "java":
                identifier_types.extend(["method_declaration", "method_invocation"])

            identifier_nodes = get_nodes_of_types(
                parsed_file.ts_tree.root_node, identifier_types
            )

            # Check each identifier for matches
            for node in identifier_nodes:
                node_text = node.text.decode()
                if node_text == symbol:
                    # Get line and column
                    start_point = node.start_point

                    # Get context (the full line containing the reference)
                    lines = code.splitlines()
                    context = (
                        lines[start_point[0]] if start_point[0] < len(lines) else ""
                    )

                    ref = SymbolReference(
                        file_path=file_path,
                        line=start_point[0] + 1,  # Convert to 1-based line numbers
                        column=start_point[1] + 1,  # Convert to 1-based column numbers
                        symbol=symbol,
                        context=context,
                    )
                    references.append(ref)

        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue

    return references


class ExpertAgent:
    """Agent that analyzes code at a specific git reference."""

    def __init__(self, ref: str | None = None, workspace_root: Optional[Path] = None):
        """Initialize the agent with a git reference.

        Args:
            ref (Optional[str]): Git reference (commit hash or branch name) to analyze, defaults to current HEAD.
            workspace_root (Optional[Path]): Root directory of the workspace, defaults to current working directory.
        """
        self.ref = ref
        if ref is not None:
            # For specific refs, create a fresh temp directory
            self.temp_dir = Path(tempfile.mkdtemp())

            # Get repository root
            repo_root = workspace_root or Path(
                run_command("git rev-parse --show-toplevel").strip()
            )

            # Initialize empty repo
            run_command(f"git init -b main {str(self.temp_dir)}")

            # Check if remote exists and get its URL, otherwise use local path
            has_remote = run_command(f"git -C {repo_root} remote").strip() != ""
            if has_remote:
                remote_url = run_command(
                    f"git -C {repo_root} remote get-url origin"
                ).strip()
                run_command(
                    f"git -C {str(self.temp_dir)} remote add origin {remote_url}"
                )
            else:
                # For local repos without remotes (like in tests)
                run_command(
                    f"git -C {str(self.temp_dir)} remote add origin {repo_root}"
                )

            # Fetch and checkout the ref
            run_command(f"git -C {str(self.temp_dir)} fetch origin {ref}")
            run_command(f"git -C {str(self.temp_dir)} checkout FETCH_HEAD")
        else:
            # When no ref specified, work directly in the workspace
            self.temp_dir = workspace_root or Path.cwd()

        # Setup workspace manager and tools
        self.tool_logger = ToolCallLogger()
        token = Path.home().joinpath(".augment/token").read_text().strip()
        api_proxy_url = get_staging_api_proxy_url()
        self.augment_client = AugmentPrototypingClient(api_proxy_url, auth_token=token)
        self.workspace_manager = WorkspaceManagerImpl(
            augment_client=self.augment_client,
            root=self.temp_dir,
        )

        # Initialize tools
        augment_public_api_client = AugmentClient(api_proxy_url, token=token)
        self.codebase_tool = BackendCodebaseRetrievalTool(
            tool_call_logger=self.tool_logger,
            client=augment_public_api_client,
            workspace_manager=self.workspace_manager,
        )

        self.read_file_tool = ReadFileTool(
            tool_call_logger=self.tool_logger,
            root=self.temp_dir,
        )
        self.edit_file_tool = StrReplaceEditorTool(
            tool_call_logger=self.tool_logger,
            workspace_manager=self.workspace_manager,
        )
        self.complete_tool = CompleteTool(
            tool_call_logger=self.tool_logger,
        )

        # Initialize LLM client
        self.client = get_client(
            "anthropic-direct", model_name="claude-3-5-sonnet-20241022", max_retries=50
        )

    def __del__(self):
        """Cleanup temp directory when object is destroyed."""
        if self.ref is not None:
            run_command(f"rm -rf {str(self.temp_dir)}")

    def answer_question(self, question: str) -> str:
        """Answer a question about the codebase.

        Args:
            question: The question to answer

        Returns:
            The answer from the expert
        """
        # Add user question to history
        self.messages: List[List[GeneralContentBlock]] = [[TextPrompt(text=question)]]

        # Define available tools
        tools = [
            ToolParam(
                name="request_context",
                description="Request additional context from the user after you exhuasted all available information.",
                input_schema={
                    "type": "object",
                    "properties": {
                        "context_request": {
                            "type": "string",
                            "description": "Detailed and technical request for additional context or information.",
                        },
                    },
                    "required": ["context_request"],
                },
            ),
            self.read_file_tool.get_tool_param(),
            self.codebase_tool.get_tool_param(),
            self.edit_file_tool.get_tool_param(),
            self.complete_tool.get_tool_param(),
        ]

        while True:
            response, _ = self.client.generate(
                messages=self.messages,
                max_tokens=8192,
                system_prompt=f"""
                You are an expert software engineer, working with a user to help them with a task.
                Think step-by-step, and use the available tools to gather information.

                You will be provided with two primary tasks:
                1. Answer questions about the codebase
                2. Make edits to the codebase

                Current working directory is '{self.temp_dir.absolute()}'.
                Always use relative paths when referring to files, and prepend them with './'.
                Always include 'ANSWER_YES' in your response if the answer to a yes/no question is *yes*.
                """,
                tools=tools,
            )

            response_messages = []
            for message in response:
                if isinstance(message, TextResult):
                    response_messages.append(message)
                elif isinstance(message, ToolCall):
                    if message.tool_name == "request_context":
                        context_request = message.tool_input.get(
                            "context_request", "Need more information to proceed."
                        )
                        formatted_request = f"I need more context to help you effectively:\n\n{context_request}"
                        raise ContextRequestException(formatted_request)

                    elif message.tool_name == self.read_file_tool.name:
                        response_messages.append(message)
                        self.messages.append(response_messages)
                        result = self.read_file_tool.run_impl(message.tool_input)
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=result.tool_output,
                        )
                        self.messages.append([formatted_result])
                        response_messages = []
                        continue

                    elif message.tool_name == self.codebase_tool.name:
                        response_messages.append(message)
                        self.messages.append(response_messages)
                        result = self.codebase_tool.run_impl(message.tool_input)
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=result.tool_output,
                        )
                        self.messages.append([formatted_result])
                        response_messages = []
                        continue

                    elif message.tool_name == self.edit_file_tool.name:
                        response_messages.append(message)
                        self.messages.append(response_messages)
                        result = self.edit_file_tool.run_impl(message.tool_input)
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=result.tool_output,
                        )
                        self.messages.append([formatted_result])
                        response_messages = []
                        continue

                    elif message.tool_name == self.complete_tool.name:
                        return message.tool_input["answer"]

        raise ValueError("Should not reach here")


# Cache of ExpertAgent instances per ref
_expert_cache: Dict[str, ExpertAgent] = {}


def get_expert(
    ref: str | None = None, workspace_root: Optional[Path] = None
) -> ExpertAgent:
    """Get or create an ExpertAgent for the given ref.

    Args:
        ref: Git reference to analyze
        workspace_root: Root directory of the workspace

    Returns:
        ExpertAgent instance for the ref (cached until process end)
    """
    key = f"{ref or 'current'}:{workspace_root or 'default'}"
    if key not in _expert_cache:
        _expert_cache[key] = ExpertAgent(ref=ref, workspace_root=workspace_root)
    return _expert_cache[key]


def ask_expert(
    question: str,
    context: str,
    ref: Optional[str] = None,
    workspace_root: Optional[Path] = None,
) -> str:
    """
    Ask an expert specific question about codebase.

    Args:
        question (str): Concise one sentence question to ask.
        context (str): Required relevant external non-code information (include user messages, git diff, PR title, PR body, comments, commit messages, etc.)
        ref (Optional[str]): Git reference (commit hash or branch name) to analyze, defaults to current HEAD.
        workspace_root (Optional[Path]): Root directory of the workspace, defaults to current working directory.

    Returns (str):
        The expert's answer as a string
    """
    expert = get_expert(ref, workspace_root)
    return expert.answer_question(
        f"Question: {question}\n\nRelevant context: {context}"
    )


def request_edit(
    edit_description: str, context: str, workspace_root: Optional[Path] = None
) -> str:
    """
    Request an edit to the codebase.

    Args:
        edit_description (str): Concise one sentence description of the edit to make.
        context (str): All relevant external non-code information (include user messages, git diff, PR title, PR body, comments, commit messages, etc.)
        workspace_root (Optional[Path]): Root directory of the workspace, defaults to current working directory.

    Returns (str):
        The expert's answer as a string
    """
    expert = get_expert(None, workspace_root)
    return expert.answer_question(
        f"Edit description: {edit_description}\n\nRelevant context: {context}"
    )

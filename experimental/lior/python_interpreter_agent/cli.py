"""Command line interface for the Python interpreter agent."""

import argparse
import sys
from typing import Op<PERSON>
from rich.console import Console
from rich.prompt import Prompt
from rich.syntax import Syntax
from rich.panel import Panel
from .agent import PythonInterpreterAgent
from .logger import setup_logging, logger

console = Console()


def create_parser() -> argparse.ArgumentParser:
    """Create argument parser for the CLI."""
    parser = argparse.ArgumentParser(
        description="Python Interpreter Agent - Interactive Python with LLM assistance"
    )
    parser.add_argument(
        "--model",
        default="claude-3-5-sonnet-20240620",
        help="Model name to use (default: %(default)s)",
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug output")
    parser.add_argument("--execute", "-e", help="Execute a single command and exit")
    parser.add_argument("--log-file", help="Write logs to specified file")
    return parser


def display_code_output(output: str, error: Optional[str] = None) -> None:
    """Display code execution output using rich formatting."""
    if error:
        logger.error(error)
        console.print(Panel(error, title="Error", style="red"))
    else:
        # Try to detect if output contains Python code
        if any(keyword in output for keyword in ["def ", "class ", "import ", "    "]):
            syntax = Syntax(output, "python", theme="monokai")
            console.print(syntax)
            logger.debug(f"Code output:\n{output}")
        else:
            console.print(output)
            logger.info(f"Output: {output}")


def main() -> None:
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()

    # Set up logging based on arguments
    setup_logging(debug=args.debug, log_file=args.log_file)

    agent = PythonInterpreterAgent(model_name=args.model)
    logger.debug(f"Initialized agent with model: {args.model}")

    if args.execute:
        logger.info(f"Executing command: {args.execute}")
        agent.process_input(args.execute)
        return

    logger.info("Starting interactive session")
    console.print("[bold green]Python Interpreter Agent[/bold green]")
    console.print("Enter your requests (Ctrl+C to exit)")

    try:
        while True:
            try:
                user_input = Prompt.ask("\n[bold blue]>[/bold blue]")
                if not user_input.strip():
                    continue

                logger.debug(f"Processing input: {user_input}")
                agent.process_input(user_input)

            except Exception as e:
                if args.debug:
                    logger.exception("Error processing input")
                    console.print_exception()
                else:
                    logger.error(f"Error: {str(e)}")
                    console.print(f"[red]Error:[/red] {str(e)}")

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, exiting")
        console.print("\n[yellow]Exiting...[/yellow]")
        sys.exit(0)


if __name__ == "__main__":
    main()

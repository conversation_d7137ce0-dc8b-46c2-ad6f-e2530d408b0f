"""Tests for the CLI module."""

import pytest
from experimental.lior.python_interpreter_agent.cli import create_parser


def test_parser_defaults():
    """Test argument parser defaults."""
    parser = create_parser()
    args = parser.parse_args([])
    assert args.model == "claude-3-5-sonnet-20240620"
    assert not args.debug
    assert args.execute is None


def test_parser_model_override():
    """Test model name override."""
    parser = create_parser()
    args = parser.parse_args(["--model", "test-model"])
    assert args.model == "test-model"


def test_parser_debug_flag():
    """Test debug flag."""
    parser = create_parser()
    args = parser.parse_args(["--debug"])
    assert args.debug


def test_parser_execute_option():
    """Test execute option."""
    parser = create_parser()
    args = parser.parse_args(["-e", "print('test')"])
    assert args.execute == "print('test')"

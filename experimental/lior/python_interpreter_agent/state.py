"""Module for managing Python interpreter state."""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class InterpreterState:
    """Represents the state of the Python interpreter."""

    locals: Dict[str, Any]

    def __str__(self) -> str:
        """Convert state to string representation."""
        return "\n".join(f"{k} = {repr(v)}" for k, v in sorted(self.locals.items()))

    def get_value(self, name: str) -> Any:
        """Get value from state by name."""
        return self.locals.get(name)

    def set_value(self, name: str, value: Any) -> None:
        """Set value in state."""
        self.locals[name] = value

    def clear(self) -> None:
        """Clear all state."""
        self.locals.clear()

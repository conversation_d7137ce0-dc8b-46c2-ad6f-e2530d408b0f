"""Tests for Python interpreter agent."""

from unittest.mock import Mock, patch
import pytest
from .agent import PythonInterpreterAgent
from research.llm_apis.llm_client import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>all, ToolFormattedResult


@pytest.fixture
def mock_llm():
    """Mock LLM client."""
    with patch(
        "experimental.lior.python_interpreter_agent.agent.get_client"
    ) as mock_get_client:
        mock_client = Mock()
        mock_get_client.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_workspace():
    """Mock workspace manager and dependencies."""
    with patch(
        "experimental.lior.python_interpreter_agent.builtins.WorkspaceManagerImpl"
    ) as mock_workspace, patch(
        "experimental.lior.python_interpreter_agent.builtins.AugmentPrototypingClient"
    ) as mock_client, patch(
        "experimental.lior.python_interpreter_agent.builtins.get_client"
    ) as mock_llm, patch(
        "experimental.lior.python_interpreter_agent.builtins.get_staging_api_proxy_url"
    ) as mock_url:
        # Setup mock URL
        mock_url.return_value = "http://mock-url"

        # Setup mock LLM response
        mock_llm_instance = Mock()
        mock_llm_instance.generate.return_value = ([Mock(text="Test response")], None)
        mock_llm.return_value = mock_llm_instance

        yield {
            "workspace": mock_workspace,
            "client": mock_client,
            "llm": mock_llm_instance,
        }


def test_agent_ask_expert(mock_llm, mock_workspace):
    """Test that agent properly handles ask_expert calls."""
    agent = PythonInterpreterAgent()

    # Setup mock LLM response that uses ask_expert
    mock_llm.generate.return_value = (
        [
            TextResult(text="Let me ask an expert about that."),
            ToolCall(
                tool_name="python_interpreter",
                tool_call_id="1",
                tool_input={
                    "code": 'response = ask_expert("What is this?", ref="main")'
                },
            ),
            ToolFormattedResult(
                tool_call_id="1",
                tool_name="python_interpreter",
                tool_output="Expert says: This is a test",
            ),
            ToolCall(
                tool_name="report_answer",
                tool_call_id="2",
                tool_input={"code": 'print("The expert explained: This is a test")'},
            ),
        ],
        None,
    )

    # Process input that requires expert knowledge
    result: str = agent.process_input("What is this code about?")

    # Verify expert was consulted
    assert "expert" in result.lower()
    assert "test" in result.lower()


def test_agent_caches_expert(mock_llm, mock_workspace):
    """Test that agent reuses ExpertAgent instances."""
    agent = PythonInterpreterAgent()

    # Setup mock LLM responses that use ask_expert twice with same ref
    mock_llm.generate.side_effect = [
        (
            [
                ToolCall(
                    tool_name="python_interpreter",
                    tool_call_id="1",
                    tool_input={"code": 'response1 = ask_expert("Q1?", ref="main")'},
                ),
                ToolCall(
                    tool_name="report_answer",
                    tool_call_id="2",
                    tool_input={"code": "print(response1)"},
                ),
            ],
            None,
        ),
        (
            [
                ToolCall(
                    tool_name="python_interpreter",
                    tool_call_id="3",
                    tool_input={"code": 'response2 = ask_expert("Q2?", ref="main")'},
                ),
                ToolCall(
                    tool_name="report_answer",
                    tool_call_id="4",
                    tool_input={"code": "print(response2)"},
                ),
            ],
            None,
        ),
    ]

    # Make two requests that consult expert
    agent.process_input("First question")
    agent.process_input("Second question")

    # Verify ExpertAgent was created only once
    mock_workspace["workspace"].assert_called_once()

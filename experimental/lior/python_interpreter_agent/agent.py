"""Agent that maintains a Python interpreter state and interacts with LLM."""

from typing import Union, cast, List

from research.llm_apis.llm_client import (
    Text<PERSON>rompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolParam,
    get_client,
)
from research.agents.tools import Too<PERSON><PERSON><PERSON><PERSON>og<PERSON>
from .interpreter import PythonInterpreter
from .tools import PythonInterpreterTool, CompleteTool
from .logger import logger


class PythonInterpreterAgent:
    """Agent that maintains a Python interpreter state and interacts with LLM."""

    def __init__(self, model_name: str = "claude-3-5-sonnet-20241022"):
        """Initialize the agent with an LLM client and tools."""
        self.client = get_client(
            "anthropic-direct", model_name=model_name, max_retries=50
        )
        self.tool_logger = ToolCallLogger()
        self.interpreter = PythonInterpreter()
        self.interpreter_tool = PythonInterpreterTool(
            self.tool_logger, self.interpreter
        )
        self.complete_tool = CompleteTool(self.tool_logger, self.interpreter)
        self.messages: List[
            List[Union[TextPrompt, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>]]
        ] = []
        logger.debug(f"Initialized agent with model: {model_name}")

    def process_input(self, user_input: str) -> str:
        """Process user input through the agent loop."""
        logger.debug("Processing user input: %s", user_input)

        # Add user message to history
        self.messages.append([TextPrompt(text=user_input)])

        # Define available tools using ToolParam objects
        tools = [
            ToolParam(
                name=self.interpreter_tool.name,
                description=self.interpreter_tool.description,
                input_schema=self.interpreter_tool.input_schema,
            ),
            ToolParam(
                name=self.complete_tool.name,
                description=self.complete_tool.description,
                input_schema=self.complete_tool.input_schema,
            ),
        ]

        while True:
            logger.debug("Generating response from LLM")
            response, _ = self.client.generate(
                messages=self.messages,
                max_tokens=8192,
                tools=tools,
                system_prompt="""
You are a software engineer that solves complex tasks using a Python interpreter that preserves state between calls.

You must think step-by-step and follow these steps for each user message:
Step 0: Orientation - Collect the necessary information about the codebase. If necessary, ask the expert for a high-level orientation about the codebase.
Step 1: Plan - create a high-level plan, unless the user provided a complete plan.
Step 2: Execute - Use the python_interpreter tool to iteratively execute the plan.
Step 3: Answer - Use the complete tool to provide the final answer to the user and stop the conversation.

### Key objectives:
 * Efficiency - Complete the task in the least amount of time.
 * Completeness - Complete the task in its entirety.
 * Accuracy - Maximize accuracy by asking the expert for help.
 * Validation - Validate the results of each step before moving to the next step.

### Optimizing execution:
 * Iterative execution - Break down the plan into the smallest possible Python code blocks and use function calls to execute them.
 * Pattern matching - Use 'grep' and find references to symbols to quickly find patterns in the codebase.
 * Deep Analysis - Always use the expert for deep analysis after performing shallow analysis yourself.
 * Risks - Consider large files, binary files, and massive search results as costly operations that should be avoided if possible.

IMPORTANT: Read the interpreter tool description carefully and use the built-in functions as described.
""",
                tool_choice={"type": "auto"},
            )

            response_messages = []
            for message in response:
                if isinstance(message, TextResult):
                    logger.info("LLM: %s", message.text)
                    response_messages.append(
                        cast(
                            Union[
                                TextPrompt, ToolFormattedResult, TextResult, ToolCall
                            ],
                            message,
                        )
                    )
                elif isinstance(message, ToolCall):
                    logger.debug("Tool call: %s", message.tool_name)
                    if message.tool_name == self.complete_tool.name:
                        response_messages.append(
                            cast(
                                Union[
                                    TextPrompt,
                                    ToolFormattedResult,
                                    TextResult,
                                    ToolCall,
                                ],
                                message,
                            )
                        )
                        self.messages.append(response_messages)

                        result = self.complete_tool.run_impl(message.tool_input)
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=result.tool_output,
                        )
                        self.messages.append([formatted_result])
                        logger.info(
                            "Task complete, final answer: %s", result.tool_output
                        )
                        return result.tool_output
                    elif message.tool_name == self.interpreter_tool.name:
                        response_messages.append(
                            cast(
                                Union[
                                    TextPrompt,
                                    ToolFormattedResult,
                                    TextResult,
                                    ToolCall,
                                ],
                                message,
                            )
                        )
                        self.messages.append(response_messages)
                        logger.info(
                            "Interpreter:\n```python\n%s\n```",
                            message.tool_input["code"],
                        )
                        result = self.interpreter_tool.run_impl(message.tool_input)
                        logger.info(
                            "Interpreter output:\n```\n%s\n```", result.tool_output
                        )
                        formatted_result = ToolFormattedResult(
                            tool_call_id=message.tool_call_id,
                            tool_name=message.tool_name,
                            tool_output=result.tool_output,
                        )
                        self.messages.append([formatted_result])
                        response_messages = []
                        continue

        raise ValueError("Should not reach here")

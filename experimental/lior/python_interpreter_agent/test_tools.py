"""Tests for the Python interpreter tools."""

import pytest
from research.agents.tools import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from experimental.lior.python_interpreter_agent.tools import (
    PythonInterpreterTool,
    ReportAnswerTool,
)
from experimental.lior.python_interpreter_agent.interpreter import PythonInterpreter


@pytest.fixture
def interpreter():
    """Create a PythonInterpreter instance for testing."""
    return PythonInterpreter()


@pytest.fixture
def tool(interpreter):
    """Create a PythonInterpreterTool instance for testing."""
    logger = ToolCallLogger()
    return PythonInterpreterTool(logger, interpreter)


@pytest.fixture
def report_tool(interpreter):
    """Create a ReportAnswerTool instance for testing."""
    logger = ToolCallLogger()
    return ReportAnswerTool(logger, interpreter)


def test_tool_execution_success(tool):
    """Test successful code execution."""
    result = tool.run_impl({"code": "x = 42\nprint(x)"})
    assert "42" in result.tool_output
    assert "Executed Python code" in result.tool_result_message
    assert "Error" not in result.tool_output


def test_tool_execution_error(tool):
    """Test error handling in code execution."""
    result = tool.run_impl({"code": "1/0"})
    assert "Error" in result.tool_output
    assert "division by zero" in result.tool_output


def test_tool_state_persistence(tool):
    """Test that state persists between executions."""
    tool.run_impl({"code": "x = 42"})
    result = tool.run_impl({"code": "print(x)"})
    assert "42" in result.tool_output


def test_tool_start_message(tool):
    """Test tool start message."""
    msg = tool.get_tool_start_message({"code": "print('test')"})
    assert "Executing Python code" in msg


def test_report_answer_basic(report_tool):
    """Test basic answer reporting."""
    result = report_tool.run_impl({"code": 'print("The result is 42")'})

    assert result.tool_output.strip() == "The result is 42"
    assert result.tool_result_message == "Reported final answer"


def test_report_answer_multiline(report_tool):
    """Test reporting a multiline answer."""
    code = """
print("Line 1")
print("Line 2")
print("Line 3")
"""
    result = report_tool.run_impl({"code": code})

    assert "Line 1" in result.tool_output
    assert "Line 2" in result.tool_output
    assert "Line 3" in result.tool_output


def test_report_answer_with_computation(report_tool):
    """Test reporting an answer that requires computation."""
    code = """
x = 20
y = 22
result = x + y
print(f"The sum is {result}")
"""
    result = report_tool.run_impl({"code": code})
    assert result.tool_output.strip() == "The sum is 42"


def test_report_answer_start_message(report_tool):
    """Test the start message for answer reporting."""
    msg = report_tool.get_tool_start_message({"code": "print('test')"})
    assert msg == "Reporting final answer..."

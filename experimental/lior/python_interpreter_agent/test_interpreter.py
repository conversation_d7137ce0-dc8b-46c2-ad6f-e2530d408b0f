"""Tests for the Python interpreter."""

import pytest
from experimental.lior.python_interpreter_agent.interpreter import PythonInterpreter


def test_interpreter_init():
    """Test interpreter initialization."""
    interpreter = PythonInterpreter()
    assert interpreter.get_state() == {}


def test_simple_execution():
    """Test basic code execution."""
    interpreter = PythonInterpreter()
    output, error = interpreter.execute('print("hello")')
    assert output.strip() == "hello"
    assert error == ""


def test_state_persistence():
    """Test that state persists between executions."""
    interpreter = PythonInterpreter()
    interpreter.execute("x = 42")
    output, error = interpreter.execute("print(x)")
    assert output.strip() == "42"
    assert error == ""


def test_execution_error():
    """Test handling of execution errors."""
    interpreter = PythonInterpreter()
    output, error = interpreter.execute("1/0")
    assert "division by zero" in error


def test_state_management():
    """Test state management functions."""
    interpreter = PythonInterpreter()
    interpreter.set_state({"x": 1, "y": 2})
    assert interpreter.get_state() == {"x": 1, "y": 2}
    interpreter.clear_state()
    assert interpreter.get_state() == {}


def test_complex_execution():
    """Test more complex code execution."""
    interpreter = PythonInterpreter()
    # Execute function definition first
    code1 = """def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n-1)"""
    output1, error1 = interpreter.execute(code1)
    assert error1 == ""

    # Then execute function call
    code2 = """result = factorial(5)
print(result)"""
    output2, error2 = interpreter.execute(code2)
    print("Output:", repr(output2))  # Debug print
    print("Error:", repr(error2))  # Debug print
    assert output2.strip() == "120"


def test_stdout_stderr_combination():
    """Test that stdout and stderr are combined in output."""
    interpreter = PythonInterpreter()
    code = """
import sys
print("stdout")
print("stderr", file=sys.stderr)
"""
    output, error = interpreter.execute(code)
    assert "stdout" in output
    assert "stderr" in output
    assert error == ""


def test_default_timeout():
    """Test that default timeout (5 min) works."""
    interpreter = PythonInterpreter()
    code = """
import time
time.sleep(0.1)  # Should complete well within default timeout
print("Done")
"""
    output, error = interpreter.execute(code)
    assert "Done" in output
    assert error == ""


def test_state_isolation():
    """Test that different interpreter instances are isolated."""
    interpreter1 = PythonInterpreter()
    interpreter2 = PythonInterpreter()

    # Set variable in first interpreter
    interpreter1.execute("x = 42")

    # Try to access it in second interpreter
    output, error = interpreter2.execute("print(x)")
    assert "is not defined" in error

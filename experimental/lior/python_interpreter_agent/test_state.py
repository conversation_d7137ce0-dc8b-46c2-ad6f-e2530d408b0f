"""Tests for the Python interpreter agent."""

import pytest
from experimental.lior.python_interpreter_agent.state import InterpreterState


def test_interpreter_state_init():
    """Test InterpreterState initialization."""
    state = InterpreterState(locals={})
    assert state.locals == {}


def test_interpreter_state_str():
    """Test InterpreterState string representation."""
    state = InterpreterState(locals={"x": 1, "y": "test"})
    state_str = str(state)
    assert "x = 1" in state_str
    assert "y = 'test'" in state_str


def test_interpreter_state_get_value():
    """Test getting values from state."""
    state = InterpreterState(locals={"x": 1})
    assert state.get_value("x") == 1
    assert state.get_value("nonexistent") is None


def test_interpreter_state_set_value():
    """Test setting values in state."""
    state = InterpreterState(locals={})
    state.set_value("x", 1)
    assert state.locals["x"] == 1
    state.set_value("x", 2)
    assert state.locals["x"] == 2


def test_interpreter_state_clear():
    """Test clearing state."""
    state = InterpreterState(locals={"x": 1, "y": 2})
    state.clear()
    assert state.locals == {}

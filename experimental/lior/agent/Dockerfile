# Use Python 3.11 as the base image
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=America/Los_Angeles

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    git \
    openssh-client \
    libtinfo5 \
    patchelf \
    jq \
    sudo \
    gcc \
    g++ \
    software-properties-common \
    tzdata \
    && add-apt-repository ppa:deadsnakes/ppa -y \
    && apt-get update \
    && apt-get install -y \
        python3.11 \
        python3.11-dev \
        python3.11-venv \
    && curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py \
    && python3.11 get-pip.py \
    && rm get-pip.py \
    && python3.11 -m pip install --no-cache-dir \
        black \
        ruff==0.6.1 \
        pyright==1.1.353 \
        pybind11 \
        pre-commit \
        keyrings.google-artifactregistry-auth \
        keyring \
        pylint==2.17.0 \
    && ln -sf /usr/bin/python3.11 /usr/bin/python3 \
    && ln -sf /usr/bin/python3 /usr/bin/python \
    && rm -rf /var/lib/apt/lists/*

# Install GitHub CLI
RUN curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg \
    && chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
    && apt-get update \
    && apt-get install -y gh \
    && rm -rf /var/lib/apt/lists/*

# Install Google Cloud SDK
RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee /etc/apt/sources.list.d/google-cloud-sdk.list && \
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor > /usr/share/keyrings/cloud.google.gpg && \
    apt-get update && \
    apt-get install -y google-cloud-sdk google-cloud-sdk-gke-gcloud-auth-plugin && \
    rm -rf /var/lib/apt/lists/*

# Install Node and pnpm for extension development
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g pnpm@latest-9

# Install Bazelisk (which will manage Bazel versions automatically)
RUN wget https://github.com/bazelbuild/bazelisk/releases/download/v1.25.0/bazelisk-linux-amd64 && \
    echo "fd8fdff418a1758887520fa42da7e6ae39aefc788cf5e7f7bb8db6934d279fc4 bazelisk-linux-amd64" | sha256sum -c && \
    chmod +x bazelisk-linux-amd64 && \
    mv bazelisk-linux-amd64 /usr/local/bin/bazel

# Configure gcloud auth plugin for GKE
ENV USE_GKE_GCLOUD_AUTH_PLUGIN=True

# Configure gcloud docker auth
RUN gcloud auth configure-docker

# Create and setup augment user
RUN useradd --create-home --uid 1000 --shell /bin/bash augment && \
    usermod -aG sudo augment && \
    echo "augment ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# Set environment variables for the augment user
ENV PATH="/home/<USER>/.local/bin:${PATH}"
ENV PYTHONPATH="/home/<USER>/.local/lib/python3.11/site-packages:${PYTHONPATH}"

# Switch to augment user
USER augment
WORKDIR /home/<USER>

# Set up SSH directory with correct permissions
RUN mkdir -p /home/<USER>/.ssh && chmod 700 /home/<USER>/.ssh

# Copy SSH private key dynamically based on the provided filename
ARG SSH_PRIVATE_KEY_FILE
ARG SSH_PRIVATE_KEY_CONTENT
RUN echo "$SSH_PRIVATE_KEY_CONTENT" > /home/<USER>/.ssh/$SSH_PRIVATE_KEY_FILE && chmod 600 /home/<USER>/.ssh/$SSH_PRIVATE_KEY_FILE

# Configure SSH to use the correct key file with proper permissions
RUN echo "IdentityFile /home/<USER>/.ssh/$SSH_PRIVATE_KEY_FILE" >> /home/<USER>/.ssh/config && \
    chmod 600 /home/<USER>/.ssh/config

# Add GitHub to known hosts
RUN ssh-keyscan github.com >> /home/<USER>/.ssh/known_hosts

# Set up kubectl config
RUN mkdir -p /home/<USER>/.kube
ARG KUBE_CONFIG_CONTENT
RUN echo "$KUBE_CONFIG_CONTENT" > /home/<USER>/.kube/config && chmod 600 /home/<USER>/.kube/config

# Set up augment token
RUN mkdir -p /home/<USER>/.augment
ARG AUGMENT_TOKEN_CONTENT
RUN echo -n "$AUGMENT_TOKEN_CONTENT" > /home/<USER>/.augment/token && chmod 600 /home/<USER>/.augment/token

# Set up Slack token
RUN mkdir -p /home/<USER>/.augment/agent
ARG SLACK_BOT_TOKEN_CONTENT
RUN echo -n "$SLACK_BOT_TOKEN_CONTENT" > /home/<USER>/.augment/agent/slack_bot_token && chmod 600 /home/<USER>/.augment/agent/slack_bot_token

# Clone your private GitHub repository
WORKDIR /home/<USER>/app
RUN <NAME_EMAIL>:augmentcode/augment.git .

# Run research-init.sh
RUN /home/<USER>/app/research/research-init.sh --cpu --reqs-only

# Config git
RUN git config --global user.email "<EMAIL>" && git config --global user.name "Agent"

# Install dependencies
RUN pnpm install

# Generate proto typestubs and install base
RUN bazel run //tools/generate_proto_typestubs

# Install base
RUN bazel run //base:install

# Set working directory
WORKDIR /home/<USER>/app

CMD ["bash"]

"""Agent server implementation.

Example usage:
    # Run the server
    uvicorn experimental.lior.agent.agent_server:app --host 0.0.0.0 --port 8000

    # Example API calls:
    # Dispatch a task:
    curl -X POST http://localhost:8000/dispatch -H "Content-Type: application/json" -d '{"command": "sleep 10 && echo hello world"}'

    # List tasks:
    curl http://localhost:8000/list

    # Fetch task status:
    curl http://localhost:8000/fetch/task-id-here

    # Cancel task:
    curl -X POST http://localhost:8000/cancel/task-id-here

    # Stream task output:
    curl http://localhost:8000/stream/task-id-here
"""

import asyncio
import uuid
from typing import (
    Any,
    Dict,
    List,
    Optional,
    cast,
    AsyncIterator,
    TypedDict,
    AsyncGenerator,
)
from pathlib import Path
from fastapi import FastAPI, HTTPException, Response
from fastapi.responses import StreamingResponse, HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, ConfigDict
import time
import os
import glob
import subprocess
import re
from datetime import datetime, timedelta
from enum import Enum
import yaml
import shlex
from experimental.lior.agent.task_executor import (
    TaskExecutor,
    DockerTaskExecutor,
    LocalTaskExecutor,
)
from experimental.lior.agent.task_types import TaskResult
from contextlib import asynccontextmanager
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.prompt_format.common import Exchange, RequestMessage
from base.third_party_clients.third_party_model_client import ThirdPartyModelResponse
from research.environments import get_eng_secret


# Add these at the module level
def get_claude_client() -> AnthropicDirectClient:
    """Get configured Claude client."""
    return AnthropicDirectClient(
        api_key=get_eng_secret("seal-research-anthropic-key"),
        model_name="claude-3-sonnet-20240229",  # Updated to correct model name
        temperature=0,  # Use 0 for consistent, deterministic responses
        max_output_tokens=1000,
    )


async def async_generator_from_sync(gen):
    """Convert a sync generator to an async generator."""
    for item in gen:
        yield item


class LogSummaryRequest(BaseModel):
    logs: str
    previous_steps: Optional[List[str]] = None


class LogSummaryResponse(BaseModel):
    steps: List[str]


class ScheduleDict(TypedDict):
    """Type definition for schedule dictionary."""

    instruction: str
    schedule_type: str
    time: str
    day_of_week: Optional[int]
    next_run: datetime
    pickle_log_dir: str


class ServerConfig(BaseModel):
    """Server configuration."""

    DEFAULT_TIMEOUT: int = 60 * 60  # 1 hour default timeout
    MAX_CONCURRENT_TASKS: int = 10
    task_executor: Optional[TaskExecutor] = None
    test_mode: bool = False  # Added test mode flag

    model_config = ConfigDict(arbitrary_types_allowed=True)


class DispatchRequest(BaseModel):
    """Request model for dispatch endpoint."""

    command: str  # The bash command to run
    timeout: Optional[int] = None  # Optional per-task timeout
    branch: Optional[str] = None  # Optional branch to checkout


class TaskResponse(BaseModel):
    """Response model for task status."""

    task_id: str
    status: str
    command: str
    output: Optional[str] = None
    error: Optional[str] = None
    returncode: Optional[int] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    duration: Optional[float] = None
    branch: Optional[str] = None

    @classmethod
    def from_task_result(cls, task_id: str, task: TaskResult) -> "TaskResponse":
        """Create TaskResponse from TaskResult."""
        return cls(
            task_id=task_id,
            status=task.get("status", "unknown"),
            command=task.get("command", ""),
            output=task.get("output"),
            error=task.get("error"),
            returncode=task.get("returncode"),
            start_time=task.get("start_time"),
            end_time=task.get("end_time"),
            duration=task.get("duration"),
            branch=task.get("branch"),
        )


class TaskList(BaseModel):
    """Response model for list endpoint."""

    tasks: List[TaskResponse]


class AgentRequest(BaseModel):
    """Request model for agent endpoint."""

    instruction: str  # The instruction for the agent
    pickle_log_dir: str = "/tmp/agent_logs/"  # Directory for pickle logs
    branch: Optional[str] = None  # Optional branch to checkout
    timeout: Optional[int] = None  # Optional per-task timeout


class ScheduleType(str, Enum):
    HOURLY = "hourly"  # Add hourly type
    DAILY = "daily"
    WEEKLY = "weekly"


class ScheduleRequest(BaseModel):
    instruction: str
    schedule_type: ScheduleType
    time: str  # Format: "HH:MM"
    day_of_week: Optional[int] = None  # 0-6, where 0 is Monday (only for weekly)
    pickle_log_dir: str = "/tmp/agent_logs/"


class ScheduleResponse(BaseModel):
    schedule_id: str
    instruction: str
    schedule_type: ScheduleType
    time: str
    day_of_week: Optional[int]
    next_run: datetime


class TaskStreamResponse(BaseModel):
    """Response model for streaming updates."""

    task_id: str
    event: str  # 'output', 'status', or 'complete'
    data: Dict[str, Any]
    timestamp: float

    def to_sse(self) -> str:
        """Convert to SSE format efficiently."""
        return f"data: {self.model_dump_json()}\n\n"


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator[None]:
    """Configure task executor and start scheduled tasks."""
    # Configure task executor based on mode
    if not config.task_executor:
        if config.test_mode:
            config.task_executor = LocalTaskExecutor()
        else:
            config.task_executor = DockerTaskExecutor()

    task = asyncio.create_task(run_scheduled_tasks())
    yield
    task.cancel()
    try:
        await task
    except asyncio.CancelledError:
        pass


app = FastAPI(lifespan=lifespan)
# Mount static files BEFORE the root route
static_path = Path(__file__).parent / "static"
app.mount("/static", StaticFiles(directory=str(static_path)), name="static")


config = ServerConfig()

# Store tasks and their status
tasks: Dict[str, TaskResult] = {}

# Store schedules
schedules: Dict[str, ScheduleDict] = {}

# Store tasks and their output queues
task_queues: Dict[str, asyncio.Queue[TaskStreamResponse]] = {}


async def run_task(
    task_id: str,
    command: str,
    timeout: Optional[int] = None,
    branch: Optional[str] = None,
) -> None:
    """Run a task using the configured executor."""
    print(f"[DEBUG] Starting task {task_id}: {command}")
    try:
        if not config.task_executor:
            raise RuntimeError("Task executor not configured")

        start_time = time.time()
        tasks[task_id]["start_time"] = start_time
        tasks[task_id]["status"] = "running"
        tasks[task_id]["output"] = ""  # Initialize empty output
        print(f"[DEBUG] Task {task_id} status set to running")

        # Create queue for this task
        queue = asyncio.Queue[TaskStreamResponse]()
        task_queues[task_id] = queue

        # Stream initial status
        initial_status = TaskStreamResponse(
            task_id=task_id,
            event="status",
            data={"status": "running", "start_time": start_time},
            timestamp=time.time(),
        )
        print(f"[DEBUG] Sending initial status for task {task_id}")
        await queue.put(initial_status)

        # Execute the command and stream output
        print(f"[DEBUG] Starting command execution for task {task_id}")
        output_buffer = []
        current_line = []

        if config.task_executor:
            async for chunk in config.task_executor.stream(
                task_id, command, timeout, branch
            ):
                # Process the chunk character by character
                for char in chunk:
                    current_line.append(char)
                    if char == "\n":
                        # We have a complete line
                        line = "".join(current_line)
                        output_buffer.append(line)
                        current_line = []

                        # Update task output
                        tasks[task_id]["output"] = "".join(output_buffer)

                        # Stream to clients
                        await queue.put(
                            TaskStreamResponse(
                                task_id=task_id,
                                event="output",
                                data={"output": line},
                                timestamp=time.time(),
                            )
                        )

        # Handle any remaining partial line
        if current_line:
            line = "".join(current_line)
            output_buffer.append(line)
            tasks[task_id]["output"] = "".join(output_buffer)
            await queue.put(
                TaskStreamResponse(
                    task_id=task_id,
                    event="output",
                    data={"output": line},
                    timestamp=time.time(),
                )
            )

        # Get final status
        end_time = time.time()
        start_time_val = tasks[task_id].get("start_time", start_time)
        duration = end_time - float(
            start_time_val if start_time_val is not None else start_time
        )

        # Get result from task executor
        result = None
        if config.task_executor:
            result = await config.task_executor.get_result(task_id)
        returncode = result.get("returncode", 0) if result is not None else 0
        error = result.get("error") if result is not None else None

        # Determine final status
        final_status = {
            "status": "completed" if returncode == 0 else "failed",
            "returncode": returncode,  # Preserve actual return code
            "end_time": end_time,
            "duration": duration,
            "error": error,
            "output": "".join(output_buffer),
        }

        print(f"[DEBUG] Process completed with return code: {returncode}")
        print(f"[DEBUG] Final output: {''.join(output_buffer)}")
        print(
            f"[DEBUG] Task {task_id} status change: running -> {final_status['status']}"
        )
        tasks[task_id].update(cast(TaskResult, final_status))

        # Stream completion status
        await queue.put(
            TaskStreamResponse(
                task_id=task_id,
                event="complete",
                data=final_status,
                timestamp=time.time(),
            )
        )

    except asyncio.TimeoutError:
        print(f"[DEBUG] Task {task_id} timed out")
        end_time = time.time()
        start_time_val = tasks[task_id].get("start_time")
        duration = end_time - float(
            start_time_val if start_time_val is not None else end_time
        )

        # Get result from task executor to get the actual return code
        try:
            if config.task_executor:
                result = await config.task_executor.get_result(task_id)
                returncode = (
                    result.get("returncode", 124) if result is not None else 124
                )
            else:
                returncode = 124
        except Exception:
            returncode = (
                124  # Standard timeout return code if we can't get the actual code
            )

        timeout_status = {
            "status": "failed",
            "error": f"Command timed out after {timeout} seconds",
            "returncode": returncode,  # Use actual return code from executor
            "end_time": end_time,
            "duration": duration,
            "output": tasks[task_id].get("output", ""),  # Include any output so far
        }
        print(f"[DEBUG] Task {task_id} status change: running -> failed")
        tasks[task_id].update(cast(TaskResult, timeout_status))

        if task_id in task_queues:
            await task_queues[task_id].put(
                TaskStreamResponse(
                    task_id=task_id,
                    event="complete",
                    data=timeout_status,
                    timestamp=time.time(),
                )
            )

    except Exception as e:
        print(f"[DEBUG] Error in task {task_id}: {str(e)}")
        end_time = time.time()
        start_time_val = tasks[task_id].get("start_time")
        duration = end_time - float(
            start_time_val if start_time_val is not None else end_time
        )

        # Try to get actual return code from executor
        try:
            if config.task_executor:
                result = await config.task_executor.get_result(task_id)
                returncode = result.get("returncode", 1) if result is not None else 1
            else:
                returncode = 1
        except Exception:
            returncode = 1  # Standard error return code if we can't get the actual code

        error_status = {
            "status": "failed",
            "error": str(e),
            "returncode": returncode,  # Use actual return code from executor
            "end_time": end_time,
            "duration": duration,
            "output": tasks[task_id].get("output", ""),  # Include any output so far
        }
        print(f"[DEBUG] Task {task_id} status change: running -> failed")
        tasks[task_id].update(cast(TaskResult, error_status))

        if task_id in task_queues:
            await task_queues[task_id].put(
                TaskStreamResponse(
                    task_id=task_id,
                    event="complete",
                    data=error_status,
                    timestamp=time.time(),
                )
            )

    finally:
        print(f"[DEBUG] Task {task_id} cleanup")
        if config.task_executor:
            try:
                await config.task_executor.cancel(task_id)
            except Exception:
                pass

        # Keep queue around briefly to allow late clients to get final status
        await asyncio.sleep(5)
        task_queues.pop(task_id, None)


@app.post("/dispatch", response_model=TaskResponse)
async def dispatch_endpoint(request: DispatchRequest) -> TaskResponse:
    """Dispatch a new task."""
    if not config.task_executor:
        raise HTTPException(status_code=500, detail="Task executor not configured")

    running_tasks = sum(1 for task in tasks.values() if task.get("status") == "running")
    if running_tasks >= config.MAX_CONCURRENT_TASKS:
        raise HTTPException(
            status_code=429,
            detail=f"Maximum concurrent tasks limit ({config.MAX_CONCURRENT_TASKS}) reached",
        )

    task_id = str(uuid.uuid4())

    tasks[task_id] = TaskResult(
        command=request.command,
        status="pending",
        output=None,
        error=None,
        branch=request.branch,
        returncode=None,
        start_time=None,
        end_time=None,
        duration=None,
    )

    # Start task in background
    asyncio.create_task(
        run_task(task_id, request.command, request.timeout, request.branch)
    )

    return TaskResponse.from_task_result(task_id, tasks[task_id])


@app.get("/list", response_model=Dict[str, Any])
async def list_tasks() -> Dict[str, Any]:
    """List all tasks and schedules."""
    return {
        "tasks": [
            TaskResponse.from_task_result(task_id, task)
            for task_id, task in tasks.items()
        ],
        "schedules": [
            ScheduleResponse(
                schedule_id=schedule_id,
                instruction=schedule["instruction"],
                schedule_type=cast(ScheduleType, schedule["schedule_type"]),
                time=schedule["time"],
                day_of_week=schedule["day_of_week"],
                next_run=schedule["next_run"],
            )
            for schedule_id, schedule in schedules.items()
        ],
    }


@app.get("/fetch/{task_id}", response_model=TaskResponse)
async def fetch(task_id: str) -> TaskResponse:
    """Fetch the status and result of a task."""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    return TaskResponse.from_task_result(task_id, tasks[task_id])


@app.post("/cancel/{task_id}", response_model=TaskResponse)
async def cancel(task_id: str) -> TaskResponse:
    """Cancel a running task."""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    if not config.task_executor:
        raise HTTPException(status_code=500, detail="Task executor not configured")

    task = tasks[task_id]

    if task.get("status") == "running":
        try:
            await config.task_executor.cancel(task_id)

            # Update timing on manual cancellation
            end_time = time.time()
            start_time_val = task.get("start_time")
            if start_time_val is not None:
                duration = end_time - start_time_val
            else:
                duration = None

            task.update(
                cast(
                    TaskResult,
                    {
                        "status": "cancelled",
                        "end_time": end_time,
                        "duration": duration,
                        "returncode": -1,
                    },
                )
            )

        except Exception as e:
            task["error"] = str(e)

    return TaskResponse.from_task_result(task_id, task)


def find_first_private_key() -> Optional[str]:
    """Find the first available SSH private key."""
    # Expand ~/.ssh to full path
    ssh_dir = os.path.expanduser("~/.ssh")
    # Get all files starting with id_
    key_pattern = os.path.join(ssh_dir, "id_*")
    # Filter out .pub files
    private_keys = [k for k in glob.glob(key_pattern) if not k.endswith(".pub")]
    # Return first one if any exist
    return private_keys[0] if private_keys else None


@app.post("/agent", response_model=TaskResponse)
async def run_agent(request: AgentRequest) -> TaskResponse:
    """Run the interactive agent with the given instruction."""
    if config.test_mode:
        # In test mode, use the instruction directly as the command
        command = request.instruction
    else:
        # Normal mode - use the agent script
        command = (
            f"./experimental/guy/agent_qa/interactive_agent.sh "
            f"--pickle-log-file {request.pickle_log_dir} "
            f"--auth-token-file=/home/<USER>/.augment/token "  # Updated path for augment user
            f"-i {shlex.quote(request.instruction)} "  # Pass the instruction
            "--approve-command-execution "
            "--no-integration-warnings "
            "--enable-slack-notifications=<EMAIL> "
            "--remove-tool=clarify "
            "--remove-tool=kill_process "
            "--verbose-llm-calls "
            "-q"
        )

    # Create dispatch request
    dispatch_request = DispatchRequest(
        command=command, branch=request.branch, timeout=request.timeout
    )

    # Use dispatch endpoint
    return await dispatch_endpoint(dispatch_request)


@app.post("/schedule", response_model=ScheduleResponse)
async def schedule_agent(request: ScheduleRequest) -> ScheduleResponse:
    """Schedule an agent task."""
    try:
        schedule_id = str(uuid.uuid4())
        now = datetime.now()
        time_obj = datetime.strptime(request.time, "%H:%M").time()
        base_time = datetime.combine(now.date(), time_obj)

        if request.schedule_type == ScheduleType.HOURLY:
            # If base time passed, start from next hour maintaining same minutes
            if base_time <= now:
                next_run = now.replace(minute=time_obj.minute, second=0, microsecond=0)
                if next_run <= now:
                    next_run += timedelta(hours=1)
            else:
                next_run = base_time
        elif request.schedule_type == ScheduleType.DAILY:
            next_run = base_time
            if next_run <= now:
                next_run += timedelta(days=1)
        elif request.schedule_type == ScheduleType.WEEKLY:
            if request.day_of_week is None:
                raise ValueError("day_of_week is required for weekly schedules")
            days_ahead = request.day_of_week - now.weekday()
            if days_ahead <= 0:
                days_ahead += 7
            next_run = datetime.combine(
                now.date() + timedelta(days=days_ahead), time_obj
            )
        else:
            raise ValueError("Invalid schedule_type")

        schedules[schedule_id] = ScheduleDict(
            instruction=request.instruction,
            schedule_type=request.schedule_type,
            time=request.time,
            day_of_week=request.day_of_week,
            next_run=next_run,
            pickle_log_dir=request.pickle_log_dir,
        )

        return ScheduleResponse(
            schedule_id=schedule_id,
            instruction=schedules[schedule_id]["instruction"],
            schedule_type=cast(ScheduleType, schedules[schedule_id]["schedule_type"]),
            time=schedules[schedule_id]["time"],
            day_of_week=schedules[schedule_id]["day_of_week"],
            next_run=schedules[schedule_id]["next_run"],
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        )


async def run_scheduled_tasks() -> None:
    """Run scheduled tasks at their scheduled times."""
    while True:
        now = datetime.now()
        for schedule_id, schedule in schedules.items():
            if schedule["next_run"] <= now:
                # Run the scheduled task
                request = AgentRequest(
                    instruction=schedule["instruction"],
                    pickle_log_dir=schedule["pickle_log_dir"],
                )
                await run_agent(request)

                # Update next run time
                if schedule["schedule_type"] == ScheduleType.HOURLY:
                    schedule["next_run"] += timedelta(hours=1)
                elif schedule["schedule_type"] == ScheduleType.DAILY:
                    schedule["next_run"] += timedelta(days=1)
                else:  # WEEKLY
                    schedule["next_run"] += timedelta(days=7)

        await asyncio.sleep(60)  # Check every minute


async def stream_task_events(task_id: str) -> AsyncGenerator[str, None]:
    """Stream task events as SSE."""
    if task_id not in tasks:
        error_event = TaskStreamResponse(
            task_id=task_id,
            event="error",
            data={"error": "Task not found"},
            timestamp=time.time(),
        )
        yield error_event.to_sse()
        return

    # Create queue if doesn't exist (for already running tasks)
    if task_id not in task_queues:
        task_queues[task_id] = asyncio.Queue[TaskStreamResponse]()

    queue = task_queues[task_id]

    try:
        # Send initial state
        task = tasks[task_id]
        initial_event = TaskStreamResponse(
            task_id=task_id,
            event="status",
            data={
                "status": task.get("status", "unknown"),
                "start_time": task.get("start_time"),
                "output": task.get("output", ""),
            },
            timestamp=time.time(),
        )
        yield initial_event.to_sse()

        if task.get("status") not in ["pending", "running"]:
            # Task already completed, send final state
            final_event = TaskStreamResponse(
                task_id=task_id,
                event="complete",
                data={
                    "status": task.get("status", "unknown"),
                    "returncode": task.get("returncode"),
                    "error": task.get("error"),
                    "end_time": task.get("end_time"),
                    "duration": task.get("duration"),
                },
                timestamp=time.time(),
            )
            yield final_event.to_sse()
            return

        while True:
            try:
                # Wait for new events with timeout
                event = await asyncio.wait_for(queue.get(), timeout=1.0)
                yield event.to_sse()

                # Stop if task completed
                if event.event == "complete":
                    break

            except asyncio.TimeoutError:
                # Send keepalive comment
                yield ": keepalive\n\n"

                # Check if task still exists
                if task_id not in tasks:
                    error_event = TaskStreamResponse(
                        task_id=task_id,
                        event="error",
                        data={"error": "Task no longer exists"},
                        timestamp=time.time(),
                    )
                    yield error_event.to_sse()
                    break

                # Check if task completed while we were waiting
                task = tasks[task_id]
                if task.get("status") not in ["pending", "running"]:
                    final_event = TaskStreamResponse(
                        task_id=task_id,
                        event="complete",
                        data={
                            "status": task.get("status", "unknown"),
                            "returncode": task.get("returncode"),
                            "error": task.get("error"),
                            "end_time": task.get("end_time"),
                            "duration": task.get("duration"),
                        },
                        timestamp=time.time(),
                    )
                    yield final_event.to_sse()
                    break

    except asyncio.CancelledError:
        # Client disconnected
        pass
    except Exception as e:
        # Send error event
        error_event = TaskStreamResponse(
            task_id=task_id,
            event="error",
            data={"error": str(e)},
            timestamp=time.time(),
        )
        yield error_event.to_sse()


@app.get("/stream/{task_id}")
async def stream(task_id: str):
    """Stream task output and status updates using Server-Sent Events."""
    return StreamingResponse(
        stream_task_events(task_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )


@app.get("/", response_class=HTMLResponse)
async def get_client():
    """Serve the web client HTML."""
    html_path = static_path / "web_client.html"
    if not html_path.exists():
        raise HTTPException(status_code=404, detail="Web client HTML not found")
    return HTMLResponse(content=html_path.read_text(), status_code=200)


@app.get("/git-branches")
async def list_branches():
    """Get list of remote git branches."""
    try:
        cmd = ["git", "branch", "-r", "--format=%(refname:short)"]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)

        branches = []
        for branch in result.stdout.strip().split("\n"):
            if not branch or "HEAD" in branch:
                continue
            # Remove 'origin/' prefix if present
            if branch.startswith("origin/"):
                branch = branch[7:]
            branches.append(branch)

        return sorted(branches)
    except subprocess.CalledProcessError as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to list branches: {e.stderr}"
        )


@app.post("/summarize-logs", response_model=LogSummaryResponse)
async def summarize_logs(request: LogSummaryRequest) -> LogSummaryResponse:
    """Summarize logs into clear, numbered steps using Claude, maintaining consistency with previous steps."""
    try:
        # Get Claude client
        claude_client = get_claude_client()

        # Construct prompt with previous steps if they exist
        previous_steps_text = ""
        if request.previous_steps:
            previous_steps_text = "Previous steps:\n" + "\n".join(
                f"{i+1}. {step}" for i, step in enumerate(request.previous_steps)
            )

        prompt = f"""
I have logs of an AI agent actions, including some tool outputs.
Here are the previous steps:
{previous_steps_text}

Here are the logs to summarize:
{request.logs}

Rules for summarization:
1. Each step should be concise and clear
2. Focus on actual operations and their results, ignore debug messages
3. Avoid repeating information that was already mentioned in previous steps
4. Combine similar actions into a single step (e.g. multiple greetings should be one step)
5. For authentication/token steps, only mention it once
6. For repeated actions like workspace syncs, only mention significant ones
7. Remove redundant or overly detailed steps
8. Return the full list of steps, including the previous ones, in the same format as before.

Format the response as a list of steps, one per line, starting with the step number followed by a period and space.
Example format:
1. First action taken
2. Second action taken

Important: Return ONLY the numbered steps, no additional text or explanations."""

        # Get response from Claude
        response_text = ""
        response_generator = claude_client.generate_response_stream(
            cur_message=prompt,
            system_prompt="You are a helpful assistant that summarizes logs into clear, numbered steps.",
        )
        async for response in async_generator_from_sync(response_generator):
            if isinstance(response, ThirdPartyModelResponse):
                response_text += response.text

        # Parse Claude's output into steps, removing any numbering
        new_steps = []
        for line in response_text.split("\n"):
            line = line.strip()
            if line and line[0].isdigit():
                # Remove the numbering from the start
                step_text = re.sub(r"^\d+\.\s*", "", line).strip()
                if step_text:
                    new_steps.append(step_text)

        if not new_steps and not request.previous_steps:
            raise HTTPException(
                status_code=500,
                detail="Failed to generate steps from Claude's response",
            )

        # Return only the new steps without previous ones
        return LogSummaryResponse(steps=new_steps)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error summarizing logs: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    # build the docker
    private_key_path = find_first_private_key()
    if not private_key_path:
        raise RuntimeError("No SSH private key found in ~/.ssh/")

    gh_token = yaml.safe_load(open(os.path.expanduser("~/.config/gh/hosts.yml"), "r"))[
        "github.com"
    ]["oauth_token"]
    if not gh_token:
        raise RuntimeError("No GH token found in ~/.config/gh/hosts.yml")
    print(f"Using GH token: {'*' * len(gh_token)}")
    print(f"Using SSH private key: {private_key_path}")
    docker_build_cmd = [
        "docker",
        "build",
        "--build-arg",
        f"SSH_PRIVATE_KEY_FILE={os.path.basename(private_key_path)}",
        "--build-arg",
        f"SSH_PRIVATE_KEY_CONTENT={open(private_key_path, 'r').read()}",
        "--build-arg",
        f"KUBE_CONFIG_CONTENT={open(os.path.expanduser('~/.kube/config'), 'r').read()}",
        "--build-arg",
        f"AUGMENT_TOKEN_CONTENT={open(os.path.expanduser('~/.augment/token'), 'r').read()}",
        "--build-arg",
        f"SLACK_BOT_TOKEN_CONTENT={open(os.path.expanduser('~/.augment/agent/slack_bot_token'), 'r').read()}",
        "-t",
        "agent-dev",
        "experimental/lior/agent/",
    ]
    print(f"Running: {' '.join(docker_build_cmd)}")
    subprocess.run(docker_build_cmd, check=True)
    print("Docker build complete")

    # Run the server
    uvicorn.run(app, host="0.0.0.0", port=8000)

#!/usr/bin/env python3
"""Client for the agent server."""

import argparse
import json
import os
import sys
import time
from datetime import datetime
from functools import wraps, partial
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, Optional, Union
import tempfile
import aiohttp
import asyncio
import requests
from humanfriendly import format_timespan
from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from rich.markdown import Markdown
import signal

# Default configuration
DEFAULT_SERVER_URL = "http://localhost:8000"
TASK_ID_PREFIX_LENGTH = 8


class AgentClientError(Exception):
    """Base exception for agent client errors."""


def handle_api_errors(func):
    """Decorator to handle API errors consistently."""

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except requests.HTTPError as e:
            error_message = "Unknown error"
            if e.response is not None:
                if e.response.status_code == 400:
                    error_message = e.response.json().get("detail", "Bad request")
                elif e.response.status_code == 404:
                    error_message = "Resource not found"
                elif e.response.status_code == 500:
                    error_message = "An unexpected server error occurred"
                else:
                    error_message = str(e)
            self.console.print(f"[red]Error: {error_message}[/red]")
            raise
        except requests.RequestException as e:
            self.console.print(
                f"[red]Error: Failed to connect to the server. {str(e)}[/red]"
            )
            raise

    return wrapper


class AgentClient:
    """Client for interacting with the agent server."""

    def __init__(self, base_url: str):
        """Initialize the client.

        Args:
            base_url: Base URL of the agent server
        """
        self.base_url = base_url.rstrip("/")
        self.console = Console()
        self.owned_task_id: Optional[str] = None
        # Add signal handling state
        self._shutdown = False
        self._exit_code = 0  # Add exit code tracking
        self._shutdown_event = None  # Will be set in _monitor_task

    def _setup_signal_handlers(self, task_id: str, live: Live, terminal_height: int):
        """Setup signal handlers for graceful shutdown.

        Args:
            task_id: Task ID being monitored
            live: Live display object
            terminal_height: Terminal height
        """

        def signal_handler(signum, frame):
            if self._shutdown:  # Prevent multiple signal handling
                return
            self._shutdown = True

            # Set the event instead of raising an exception
            if self._shutdown_event:
                self._shutdown_event.set()

        # Setup handlers for both SIGINT and SIGTERM
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def _render_output(self, text: str) -> Union[str, Markdown]:
        """Render output text, detecting if it's markdown.

        Args:
            text: Text to render

        Returns:
            Either plain text or Markdown object
        """
        # Check if text looks like markdown
        if any(marker in text for marker in ["#", "`", "*", "_"]):
            return Markdown(text)
        return text

    @handle_api_errors
    def fetch(self, task_id: str) -> dict:
        """Fetch task status and output.

        Args:
            task_id: Task ID to fetch

        Returns:
            Task information dictionary
        """
        response = requests.get(f"{self.base_url}/fetch/{task_id}")
        response.raise_for_status()
        return response.json()

    @handle_api_errors
    async def cancel(self, task_id: str) -> dict[str, Any]:
        """Cancel a task.

        Args:
            task_id: Task ID to cancel

        Returns:
            Response data
        """
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{self.base_url}/cancel/{task_id}") as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise RuntimeError(f"Failed to cancel task: {error_text}")
                return await response.json()

    @handle_api_errors
    def list_tasks(self) -> dict:
        """List all tasks and schedules.

        Returns:
            Dictionary containing lists of tasks and schedules
        """
        response = requests.get(f"{self.base_url}/list")
        response.raise_for_status()
        return response.json()

    def _create_schedule_table(self, schedules: list[dict]) -> Table:
        """Create a rich table for displaying schedules.

        Args:
            schedules: List of schedule dictionaries

        Returns:
            Rich Table object
        """
        table = Table(title="Scheduled Tasks", expand=True)
        table.add_column("Schedule ID", style="cyan", no_wrap=True)
        table.add_column("Instruction", style="green", ratio=3, no_wrap=True)
        table.add_column("Schedule Type", style="magenta", no_wrap=True)
        table.add_column("Time", style="yellow", no_wrap=True)
        table.add_column("Day of Week", style="blue", no_wrap=True)
        table.add_column("Next Run", style="red", no_wrap=True)
        table.add_column("Branch", style="cyan", no_wrap=True)

        for schedule in schedules:
            instruction = schedule["instruction"].split("\n")[0]
            table.add_row(
                schedule["schedule_id"][:TASK_ID_PREFIX_LENGTH],
                instruction,
                schedule["schedule_type"],
                schedule["time"],
                str(schedule["day_of_week"])
                if schedule["day_of_week"] is not None
                else "N/A",
                schedule["next_run"],
                schedule.get("branch", "default"),
            )

        return table

    def _create_tasks_table(self, tasks: list[dict]) -> Table:
        """Create a rich table for displaying tasks.

        Args:
            tasks: List of task dictionaries

        Returns:
            Rich Table object
        """
        table = Table(title="Agent Tasks", expand=True)
        table.add_column("Task ID", style="cyan", no_wrap=True)
        table.add_column("Status", style="magenta", no_wrap=True)
        table.add_column("Command", style="green", ratio=3, no_wrap=True)
        table.add_column("Duration", style="blue", no_wrap=True)
        table.add_column("Return Code", style="yellow", no_wrap=True)
        table.add_column("Branch", style="red", no_wrap=True)

        for task in tasks:
            duration_str = self._format_task_duration(task)
            table.add_row(
                task["task_id"][:TASK_ID_PREFIX_LENGTH],
                task["status"],
                task["command"].split("\n")[0],
                duration_str,
                str(task.get("returncode", "N/A")),
                task.get("branch", "default"),
            )

        return table

    def _format_task_duration(self, task: dict) -> str:
        """Format task duration for display.

        Args:
            task: Task dictionary

        Returns:
            Formatted duration string
        """
        if task["status"] == "running" and task.get("start_time"):
            return format_timespan(time.time() - task["start_time"])
        elif task.get("duration") is not None:
            return format_timespan(task["duration"])
        return "N/A"

    def print_schedules(self) -> None:
        """Print a pretty table of all scheduled tasks."""
        data = self.list_tasks()
        schedules = data.get("schedules", [])
        table = self._create_schedule_table(schedules)
        self.console.print(table)

    def print_task_list(self) -> None:
        """Print a pretty table of all tasks."""
        data = self.list_tasks()
        tasks = data.get("tasks", [])
        table = self._create_tasks_table(tasks)
        self.console.print(table)

    @handle_api_errors
    def run_agent(
        self,
        instruction: str,
        branch: Optional[str] = None,
        timeout: Optional[int] = None,
    ) -> str:
        """Run the agent with the given instruction.

        Args:
            instruction: Instruction for the agent
            branch: Optional git branch to checkout
            timeout: Optional timeout in seconds

        Returns:
            Task ID
        """
        data = {"instruction": instruction, "timeout": timeout}
        if branch:
            data["branch"] = branch
        response = requests.post(f"{self.base_url}/agent", json=data)
        response.raise_for_status()
        return response.json()["task_id"]

    def run_agent_and_monitor(
        self,
        command: str,
        branch: Optional[str] = None,
        timeout: Optional[int] = None,
        output_dir: Optional[Path] = None,
    ) -> None:
        """Run an agent command and monitor its output.

        Args:
            command: Command to run
            branch: Optional git branch to checkout
            timeout: Optional timeout in seconds
            output_dir: Optional directory to save output logs
        """
        task_id = self.run_agent(command, branch, timeout)
        self.owned_task_id = task_id  # Mark this task as owned by us
        exit_code = asyncio.run(self._monitor_task(task_id, output_dir))
        sys.exit(exit_code)

    @handle_api_errors
    def schedule_task(
        self,
        instruction: str,
        schedule_type: str,
        time: str,
        day_of_week: Optional[int] = None,
    ) -> str:
        """Schedule a task to run periodically.

        Args:
            instruction: Instruction for the agent
            schedule_type: Type of schedule ('daily' or 'weekly')
            time: Time to run the task (format: "HH:MM")
            day_of_week: Day of the week for weekly tasks (0-6, where 0 is Monday)

        Returns:
            Schedule ID
        """
        data: dict[str, Any] = {
            "instruction": instruction,
            "schedule_type": schedule_type,
            "time": time,
        }
        if day_of_week is not None:
            data["day_of_week"] = day_of_week

        response = requests.post(f"{self.base_url}/schedule", json=data)
        response.raise_for_status()
        return response.json()["schedule_id"]

    @handle_api_errors
    def attach_to_task(self, partial_task_id: str) -> str:
        """Attach to a task given its first 8 digits of the task ID.

        Args:
            partial_task_id: First 8 digits of the task ID

        Returns:
            Full task ID

        Raises:
            ValueError: If no matching task is found or multiple tasks match
        """
        response = requests.get(f"{self.base_url}/list")
        response.raise_for_status()
        tasks = response.json().get("tasks", [])

        matching_tasks = [
            task for task in tasks if task["task_id"].startswith(partial_task_id)
        ]

        if not matching_tasks:
            raise ValueError(f"No task found with ID starting with {partial_task_id}")
        elif len(matching_tasks) > 1:
            raise ValueError(
                f"Multiple tasks found with ID starting with {partial_task_id}"
            )

        return matching_tasks[0]["task_id"]

    async def stream_task_events(
        self, task_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream task events using SSE.

        Args:
            task_id: Task ID to stream

        Yields:
            Task event dictionaries
        """
        while True:  # Add reconnection loop
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.base_url}/stream/{task_id}"
                    ) as response:
                        if response.status != 200:
                            error_text = await response.text()
                            raise RuntimeError(f"Failed to stream task: {error_text}")

                        buffer = ""
                        async for chunk in response.content:
                            buffer += chunk.decode()

                            while "\n\n" in buffer:
                                message, buffer = buffer.split("\n\n", 1)
                                if message.startswith(":"):  # Skip comments/keepalive
                                    continue

                                for line in message.split("\n"):
                                    if line.startswith("data: "):
                                        try:
                                            event_data = json.loads(line[6:])
                                            yield event_data
                                        except json.JSONDecodeError:
                                            self.console.print(
                                                "[red]Error parsing event data[/red]"
                                            )
            except (aiohttp.ClientError, asyncio.CancelledError) as e:
                if isinstance(e, asyncio.CancelledError):
                    raise
                # On connection error, wait briefly and retry
                await asyncio.sleep(1)
                continue

    def _get_terminal_dimensions(self) -> tuple[int, int]:
        """Get terminal dimensions.

        Returns:
            Tuple of (height, content_height) where content_height is adjusted for borders
        """
        terminal_height = os.get_terminal_size().lines
        content_height = terminal_height - 4  # Account for panel borders and status
        return terminal_height, content_height

    def _format_status_line(
        self, status: str, start_time: Optional[float], returncode: Optional[int]
    ) -> str:
        """Format the status line for display.

        Args:
            status: Current task status
            start_time: Task start time
            returncode: Task return code

        Returns:
            Formatted status line
        """
        status_parts = [f"Status: {status}"]
        if start_time:
            duration_str = format_timespan(time.time() - start_time)
            status_parts.append(f"Duration: {duration_str}")
        if returncode is not None:
            status_parts.append(f"Return Code: {returncode}")
        return " | ".join(status_parts)

    def _get_border_style(self, status: str) -> str:
        """Get the border style based on task status.

        Args:
            status: Task status

        Returns:
            Border style name
        """
        return {
            "completed": "green",
            "failed": "red",
            "timeout": "red",
        }.get(status, "yellow")

    async def _monitor_task(
        self, task_id: str, output_dir: Optional[Path] = None
    ) -> int:
        """Monitor a task's output using SSE.

        Args:
            task_id: Task ID to monitor
            output_dir: Optional directory to save output logs (defaults to system temp directory)

        Returns:
            Exit code to use when terminating the program
        """
        terminal_height, content_height = self._get_terminal_dimensions()
        live = Live(
            Panel("Starting task..."),
            refresh_per_second=10,
            auto_refresh=True,
            console=Console(height=terminal_height, force_terminal=True),
        )

        # Create shutdown event
        self._shutdown_event = asyncio.Event()
        self._setup_signal_handlers(task_id, live, terminal_height)

        task_status = "pending"
        task_output = ""
        task_error = None
        start_time = None
        returncode = None

        # Create output file
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"task_{task_id[:TASK_ID_PREFIX_LENGTH]}_{timestamp}.log"

        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        else:
            output_dir = Path(tempfile.gettempdir()) / "agent_log_files"
            output_dir.mkdir(parents=True, exist_ok=True)

        output_file = output_dir / filename

        try:
            with live:
                stream_task = self.stream_task_events(task_id)
                while True:
                    # Create tasks for both streaming and shutdown detection
                    stream_future = asyncio.create_task(anext(stream_task))
                    shutdown_future = asyncio.create_task(self._shutdown_event.wait())

                    done, pending = await asyncio.wait(
                        [stream_future, shutdown_future],
                        return_when=asyncio.FIRST_COMPLETED,
                    )

                    # Cancel pending tasks
                    for task in pending:
                        task.cancel()

                    # Handle shutdown if triggered
                    if shutdown_future in done:
                        # Check if we created this task or are just monitoring it
                        should_cancel = task_id == self.owned_task_id

                        if should_cancel:
                            live.update(
                                Panel(
                                    "[yellow]Cancelling task...[/yellow]",
                                    border_style="yellow",
                                    height=terminal_height,
                                )
                            )
                            await self.cancel(task_id)
                            self._exit_code = 1
                        else:
                            live.update(
                                Panel(
                                    "[yellow]Detaching from task...[/yellow]",
                                    border_style="yellow",
                                    height=terminal_height,
                                )
                            )
                            self._exit_code = 0
                        return self._exit_code

                    # Handle stream event
                    try:
                        event = await stream_future
                    except StopAsyncIteration:
                        break

                    # Process event data
                    if event["event"] == "output":
                        task_output += event["data"].get("output", "")
                        with open(output_file, "a") as f:
                            f.write(event["data"].get("output", ""))
                    elif event["event"] == "status":
                        task_status = event["data"].get("status", task_status)
                        if start_time is None:
                            start_time = event["data"].get("start_time")
                    elif event["event"] == "error":
                        task_error = event["data"].get("error")
                    elif event["event"] == "complete":
                        task_status = event["data"].get("status", task_status)
                        returncode = event["data"].get("returncode", returncode)
                        break

                    # Update display
                    status_line = self._format_status_line(
                        task_status, start_time, returncode
                    )
                    border_style = self._get_border_style(task_status)

                    output_lines = task_output.splitlines()[-content_height:]
                    visible_output = "\n".join(output_lines)

                    if task_error:
                        visible_output = f"{visible_output}\n[red]{task_error}[/red]"
                        with open(output_file, "a") as f:
                            f.write(f"\nERROR: {task_error}\n")

                    panel = Panel(
                        f"{visible_output}\n\n[blue]Output is being saved to: {output_file}[/blue]",
                        title=f"Task {task_id[:TASK_ID_PREFIX_LENGTH]} - {status_line}",
                        border_style=border_style,
                        height=terminal_height,
                    )
                    live.update(panel)

        except Exception as e:
            with open(output_file, "a") as f:
                f.write(f"\nTask monitoring error: {str(e)}\n")
            return 1

        finally:
            # Write final status to file
            with open(output_file, "a") as f:
                f.write(f"\nFinal status: {task_status}")
                if returncode is not None:
                    f.write(f"\nReturn code: {returncode}")

            # Get final task status if needed
            if returncode is None:
                try:
                    task = self.fetch(task_id)
                    returncode = task.get("returncode", 1)
                    with open(output_file, "a") as f:
                        f.write(f"\nFinal return code: {returncode}")
                except Exception:
                    returncode = 1

            # Print final message about log file
            self.console.print(
                f"\n[green]Complete task log saved to: {output_file}[/green]"
            )
            return returncode if returncode is not None else 1

    async def _handle_monitoring_error(
        self,
        error: Union[aiohttp.ClientError, asyncio.CancelledError],
        task_id: str,
        live: Live,
        terminal_height: int,
    ) -> None:
        """Handle errors during task monitoring.

        Args:
            error: The error that occurred
            task_id: Task ID being monitored
            live: Live display object
            terminal_height: Terminal height
        """
        if isinstance(error, asyncio.CancelledError):
            # Check if we own this task
            if task_id == self.owned_task_id:
                live.update(
                    Panel(
                        "[yellow]Cancelling task...[/yellow]",
                        border_style="yellow",
                        height=terminal_height,
                    )
                )
                self.cancel(task_id)
                self._exit_code = 1
            else:
                live.update(
                    Panel(
                        "[yellow]Detaching from task...[/yellow]",
                        border_style="yellow",
                        height=terminal_height,
                    )
                )
                self._exit_code = 0
            raise  # Re-raise the CancelledError
        else:
            error_panel = Panel(
                f"Error: Failed to stream task: {str(error)}",
                border_style="red",
                height=terminal_height,
            )
            live.update(error_panel)
            raise


def main() -> None:
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Agent client")
    parser.add_argument("--server", default=DEFAULT_SERVER_URL, help="Agent server URL")
    parser.add_argument("--list", action="store_true", help="List all tasks")
    parser.add_argument(
        "--list-schedules", action="store_true", help="List all scheduled tasks"
    )
    parser.add_argument(
        "--timeout", "-t", type=int, default=None, help="Timeout in seconds"
    )
    parser.add_argument("--schedule", action="store_true", help="Schedule a task")
    parser.add_argument(
        "--schedule-type",
        choices=["hourly", "daily", "weekly"],
        help="Type of schedule",
    )
    parser.add_argument("--time", help="Time to run the scheduled task (format: HH:MM)")
    parser.add_argument(
        "--day-of-week",
        type=int,
        choices=range(7),
        help="Day of the week for weekly tasks (0-6, where 0 is Monday)",
    )
    parser.add_argument(
        "--attach", "-a", help="Attach to a task by its first 8 digits of the task ID"
    )
    parser.add_argument(
        "--branch", "-b", help="Git branch to checkout before running the command"
    )
    parser.add_argument(
        "--output-dir",
        help="Directory to save task output logs (defaults to system temp directory)",
    )
    parser.add_argument(
        "command",
        nargs="?",
        help="Instruction for agent if not scheduling, or instruction for scheduled task if --schedule is used",
    )

    args = parser.parse_args()
    client = AgentClient(args.server)

    try:
        if args.attach:
            try:
                full_task_id = client.attach_to_task(args.attach)
                asyncio.run(
                    client._monitor_task(full_task_id, output_dir=args.output_dir)
                )
            except ValueError as e:
                print(f"Error: {str(e)}")
                sys.exit(1)
        elif args.list:
            client.print_task_list()
        elif args.list_schedules:
            client.print_schedules()
        elif args.schedule:
            if not args.command or not args.schedule_type or not args.time:
                parser.error(
                    "--schedule requires --schedule-type, --time, and a command (instruction)"
                )

            try:
                datetime.strptime(args.time, "%H:%M")
            except ValueError as e:
                parser.error(f"Invalid time format: {str(e)}. Please use HH:MM format")

            if args.schedule_type == "weekly" and args.day_of_week is None:
                parser.error("Weekly schedules require --day-of-week")

            schedule_id = client.schedule_task(
                args.command, args.schedule_type, args.time, args.day_of_week
            )
            print(f"Task scheduled successfully. Schedule ID: {schedule_id}")
        elif args.command:
            client.run_agent_and_monitor(
                args.command, args.branch, args.timeout, output_dir=args.output_dir
            )
        else:
            parser.print_help()
    except (requests.HTTPError, requests.RequestException):
        sys.exit(1)  # Exit with an error code


if __name__ == "__main__":
    main()

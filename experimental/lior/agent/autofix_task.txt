Task:
Automatically detect a failing PR, fix the errors locally, and create a draft PR with the fix.

Steps:
1. Run `bash experimental/lior/agent/scripts/find_failing_pr.sh` to find a PR with failing tests.
2. If all PRs passed tests, exit.
3. Read the log file to understand the failure, but do not print it out (it's too long). If the log file is empty, exit.
4. Checkout the PR locally using `gh pr checkout <pr_number>`.
5. Review the changes in the original PR using `gh pr diff <pr_number>`.
6. Reproduce the errors locally, fix them, and verify that the errors are fixed.
7. If you are unable to fix the errors, exit.
8. Only proceed to the next step when you are confident that the fix is correct.
9. Create a new branch using the format: autofix-<original_branch>-$(openssl rand -hex 4).
10. Commit the changes to the new branch with a descriptive commit message explaining the fix.
11. Create a draft PR from the new branch to the original branch. Set the title to "[autofix]: <original_pr_title>". Write a detailed PR description that includes:
    - A detailed description of the errors
    - A step-by-step description of the fix
    - The statement: "This PR was created automatically by the autofix agent."
    - A link to the original PR
    - Add the person who created the original PR as the reviewer
    - Example command: `gh pr create --draft --base <original_branch> --head <new_branch> --title "[autofix]: <original_pr_title>" --body "This PR was created automatically by the autofix agent. Fixes the CI errors in the original PR: <link_to_original_pr>" --reviewer <original_pr_author>`
12. Run `bash experimental/lior/agent/scripts/wait_for_pr_logs.sh <pr_number>` to get the updated CI logs.
13. Validate that the updated CI logs contain no errors. You must wait for the command to complete.
    - If the issue is resolved, add a comment on the original PR with a short description of the fix and a link to the autofix PR.
    - If the issue is not resolved, try to fix the errors again with a new commit and repeat the process.

Guidelines:
- No access to augi CLI or Graphite (gt) tools
- Use Git CLI for any Git operations if needed
- Use GitHub CLI (gh) for any GitHub operations
- Do not commit or push directly to the original branch
- Do not use force push
- It is not enough that the tests are passing locally, you must validate that the CI issue was resolved, wait as long as needed.
- Only create draft PRs
- Make minimal changes to fix the errors
- You are not allowed to revert changes to fix errors
- Do not comment on the original PR unless you have validated that the CI issue was resolved using experimental/lior/agent/scripts/wait_for_pr_logs.sh.
- Step 12 is critical, do not skip it, create as many commits as needed to validate that all CI errors were resolved.
- My contact details:
  - Email: <EMAIL>
  - GitHub Username: liornm

"""Tests for agent client."""

import pytest
import requests
from unittest.mock import Mock, patch
from rich.markdown import Markdown

from experimental.lior.agent.client import Agent<PERSON>lient
from experimental.lior.agent.task_types import TaskResult


@pytest.fixture
def client():
    """Create a test client."""
    return AgentClient("http://test-server:8000")


@pytest.fixture
def mock_response():
    """Create a mock response."""
    response = Mock(spec=requests.Response)
    response.raise_for_status = Mock()
    return response


def test_fetch(client, mock_response):
    """Test fetching task status."""
    task_result = TaskResult(
        task_id="test-task",
        status="completed",
        command="echo test",
        output="test output",
        error=None,
        returncode=0,
        start_time=1000.0,
        end_time=1001.0,
        duration=1.0,
        branch=None,
    )
    mock_response.json.return_value = task_result

    with patch("requests.get", return_value=mock_response) as mock_get:
        result = client.fetch("test-task")
        mock_get.assert_called_once_with("http://test-server:8000/fetch/test-task")
        assert result == task_result


def test_cancel(client, mock_response):
    """Test cancelling a task."""
    task_result = TaskResult(
        task_id="test-task",
        status="cancelled",
        command="echo test",
        output="test output",
        error=None,
        returncode=-1,
        start_time=1000.0,
        end_time=1001.0,
        duration=1.0,
        branch=None,
    )
    mock_response.json.return_value = task_result

    with patch("requests.post", return_value=mock_response) as mock_post:
        result = client.cancel("test-task")
        mock_post.assert_called_once_with("http://test-server:8000/cancel/test-task")
        assert result == task_result


def test_list_tasks(client, mock_response):
    """Test listing tasks."""
    tasks = {
        "tasks": [
            TaskResult(
                task_id="task1",
                status="completed",
                command="echo test1",
                output="test1 output",
                error=None,
                returncode=0,
                start_time=1000.0,
                end_time=1001.0,
                duration=1.0,
                branch=None,
            ),
            TaskResult(
                task_id="task2",
                status="running",
                command="echo test2",
                output="test2 output",
                error=None,
                returncode=None,
                start_time=1002.0,
                end_time=None,
                duration=None,
                branch="test-branch",
            ),
        ],
        "schedules": [],
    }
    mock_response.json.return_value = tasks

    with patch("requests.get", return_value=mock_response) as mock_get:
        result = client.list_tasks()
        mock_get.assert_called_once_with("http://test-server:8000/list")
        assert result == tasks


def test_run_agent(client, mock_response):
    """Test running an agent task."""
    task_result = TaskResult(
        task_id="new-task",
        status="pending",
        command="agent command",
        output=None,
        error=None,
        returncode=None,
        start_time=None,
        end_time=None,
        duration=None,
        branch=None,
    )
    mock_response.json.return_value = task_result

    with patch("requests.post", return_value=mock_response) as mock_post:
        task_id = client.run_agent("test instruction")
        mock_post.assert_called_once_with(
            "http://test-server:8000/agent",
            json={"instruction": "test instruction", "timeout": None},
        )
        assert task_id == "new-task"


def test_run_agent_with_branch(client, mock_response):
    """Test running an agent task with a branch specified."""
    task_result = TaskResult(
        task_id="new-task",
        status="pending",
        command="agent command",
        output=None,
        error=None,
        returncode=None,
        start_time=None,
        end_time=None,
        duration=None,
        branch="test-branch",
    )
    mock_response.json.return_value = task_result

    with patch("requests.post", return_value=mock_response) as mock_post:
        task_id = client.run_agent("test instruction", branch="test-branch")
        mock_post.assert_called_once_with(
            "http://test-server:8000/agent",
            json={
                "instruction": "test instruction",
                "timeout": None,
                "branch": "test-branch",
            },
        )
        assert task_id == "new-task"


def test_attach_to_task(client, mock_response):
    """Test attaching to a task by partial ID."""
    tasks = {
        "tasks": [
            TaskResult(
                task_id="abcd1234-full-id",
                status="running",
                command="echo test",
                output="test output",
                error=None,
                returncode=None,
                start_time=1000.0,
                end_time=None,
                duration=None,
                branch=None,
            )
        ],
        "schedules": [],
    }
    mock_response.json.return_value = tasks

    with patch("requests.get", return_value=mock_response) as mock_get:
        full_id = client.attach_to_task("abcd1234")
        mock_get.assert_called_once_with("http://test-server:8000/list")
        assert full_id == "abcd1234-full-id"


def test_attach_to_task_not_found(client, mock_response):
    """Test attaching to a non-existent task."""
    mock_response.json.return_value = {"tasks": [], "schedules": []}

    with patch("requests.get", return_value=mock_response):
        with pytest.raises(
            ValueError, match="No task found with ID starting with xyz123"
        ):
            client.attach_to_task("xyz123")


def test_attach_to_task_multiple_matches(client, mock_response):
    """Test attaching to a task with ambiguous ID."""
    tasks = {
        "tasks": [
            TaskResult(
                task_id="abcd1234-first",
                status="running",
                command="echo test1",
                output=None,
                error=None,
                returncode=None,
                start_time=None,
                end_time=None,
                duration=None,
                branch=None,
            ),
            TaskResult(
                task_id="abcd1234-second",
                status="running",
                command="echo test2",
                output=None,
                error=None,
                returncode=None,
                start_time=None,
                end_time=None,
                duration=None,
                branch=None,
            ),
        ],
        "schedules": [],
    }
    mock_response.json.return_value = tasks

    with patch("requests.get", return_value=mock_response):
        with pytest.raises(
            ValueError, match="Multiple tasks found with ID starting with abcd1234"
        ):
            client.attach_to_task("abcd1234")


def test_render_output_plain(client):
    """Test rendering plain text output."""
    text = "Hello, world!"
    result = client._render_output(text)
    assert result == text


def test_render_output_markdown(client):
    """Test rendering markdown output."""
    text = "# Hello\n```python\nprint('world')\n```"
    result = client._render_output(text)
    assert isinstance(result, Markdown)


def test_handle_api_errors(client, mock_response):
    """Test error handling decorator."""
    mock_response.status_code = 500
    mock_response.json.return_value = {"detail": "Internal server error"}
    error = requests.HTTPError(response=mock_response)

    with patch("requests.get", side_effect=error):
        with pytest.raises(requests.HTTPError):
            client.fetch("test-task")


def test_handle_connection_error(client):
    """Test handling connection errors."""
    with patch(
        "requests.get", side_effect=requests.ConnectionError("Connection failed")
    ):
        with pytest.raises(requests.ConnectionError):
            client.fetch("test-task")


Task:
Review all PRs opened by a specific user in the past week, identify unhandled edge cases, and prove them using unit tests before commenting.

Steps:
1. Find PRs to review using:
   ```bash
   gh pr list -A bddavis --json number,title,createdAt,isDraft,isCrossRepository \
     --jq '.[] | select(.isDraft == false and .isCrossRepository == false and (now - fromdate(.createdAt) | . < 604800)) | .number'
   ```
2. For each PR:
   a. Checkout the PR locally using `gh pr checkout <pr_number>`
   b. Review the change in the PR using `gh pr diff <pr_number>`
   c. Analyze the code for potential unhandled edge cases, such as:
      - Empty or null inputs
      - Maximum/minimum values
      - Malformed data structures
      - Unexpected API responses
      - Resource exhaustion scenarios
      - Concurrent access patterns
      - Unicode and special characters
      - Boundary conditions
   d. When a potential edge case is identified:
      1. Create a unit test that demonstrates the unhandled case
      2. Run the test using the appropriate test command.
         - For Python: `pytest <test_file>`
      3. If the test fails (proving the unhandled edge case):
         - Create a detailed comment with:
           ```
           ### Unhandled Edge Case Report
           **Scenario:**
           [Description of the edge case]

           **Current Behavior:**
           [What happens in this case]

           **Proof:**
           [The unit test that demonstrates the issue]

           **Expected Behavior:**
           [How this edge case should be handled]

           **Suggestion:**
           [Proposed implementation for handling this case]
           ```
         - Submit the review:
           ```bash
           gh pr review <pr_number> --body <review_comment>
           ```
      4. If the test passes (edge case is handled), continue analysis
   e. If no unhandled edge cases are found after thorough analysis, move to the next PR

Guidelines:
- Focus only on edge cases that can occur in real-world scenarios
- Every reported edge case must be proven with a failing test
- Skip theoretical edge cases that are unlikely in practice
- Do not comment without test evidence
- Consider the context and intended use of the code

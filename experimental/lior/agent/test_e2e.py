"""End-to-end tests for agent client and server."""

import asyncio
import shlex
import pytest
import time
import json
from typing import Generator, Set
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
import tempfile
from pathlib import Path

from experimental.lior.agent.agent_server import app, config
from experimental.lior.agent.client import Agent<PERSON><PERSON>
from experimental.lior.agent.task_executor import LocalTaskExecutor, DockerTaskExecutor
from experimental.lior.agent.test_task_executor import is_docker_available


class MockSSEResponse:
    """Mock SSE response for testing."""

    def __init__(self, test_client: TestClient, task_id: str):
        self.test_client = test_client
        self.task_id = task_id
        self.status = 200
        self._closed = False
        self._queue: asyncio.Queue[bytes] = asyncio.Queue()
        self._task = None
        self.content = self  # Make content property point to self for async iteration

    async def _monitor_task(self):
        """Monitor task and generate SSE events."""
        last_output_len = 0

        while not self._closed:
            # Get current task state
            response = self.test_client.get(f"/fetch/{self.task_id}")
            task = response.json()

            # Stream any new output
            if task.get("output"):
                current_output = task["output"]
                if len(current_output) > last_output_len:
                    new_output = current_output[last_output_len:]
                    last_output_len = len(current_output)

                    event_data = {
                        "event": "output",
                        "data": {"output": new_output},
                        "timestamp": time.time(),
                    }
                    await self._queue.put(
                        f"data: {json.dumps(event_data)}\n\n".encode()
                    )

            # Check if task completed
            if task["status"] not in ["pending", "running"]:
                event_data = {
                    "event": "complete",
                    "data": task,
                    "timestamp": time.time(),
                }
                await self._queue.put(f"data: {json.dumps(event_data)}\n\n".encode())
                break

            await asyncio.sleep(0.001)  # Tight polling for testing

    async def start(self):
        """Start monitoring task."""
        self._task = asyncio.create_task(self._monitor_task())

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def close(self):
        """Close the response."""
        self._closed = True
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass

    def __aiter__(self):
        return self

    async def __anext__(self):
        if self._closed:
            raise StopAsyncIteration
        try:
            return await self._queue.get()
        except asyncio.CancelledError:
            raise StopAsyncIteration


class MockClientSession:
    """Mock aiohttp ClientSession."""

    def __init__(self, test_client: TestClient):
        self.test_client = test_client

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    async def get(self, url: str, **kwargs) -> MockSSEResponse:
        """Async get method that returns a MockSSEResponse."""
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        task_id = url.split("/")[-1]
        response = MockSSEResponse(self.test_client, task_id)
        await response.start()
        return response


@pytest.fixture
def test_app() -> FastAPI:
    """Create test FastAPI app."""
    # Configure server to use LocalTaskExecutor
    config.task_executor = LocalTaskExecutor()
    config.test_mode = True
    return app


@pytest.fixture
def test_client(test_app: FastAPI) -> Generator[TestClient, None, None]:
    """Create test client."""
    with TestClient(test_app) as client:
        yield client


@pytest.fixture
def agent_client(test_client: TestClient) -> AgentClient:
    """Create agent client."""
    # Get the base URL from the test client
    base_url = "http://testserver"  # Use testserver for FastAPI test client
    return AgentClient(base_url)


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


def test_streaming_output(
    test_client: TestClient,
    agent_client: AgentClient,
    monkeypatch: pytest.MonkeyPatch,
    temp_dir: Path,
):
    """Test that client can properly display streaming output from a long-running task."""
    # Create a script that outputs numbers with delays
    script = """
import time
for i in range(5):
    print(f"Line {i}")
    time.sleep(0.1)  # Shorter delay for faster tests
"""

    # Create a file with the script in the temp directory
    script_path = temp_dir / "test_script.py"
    script_path.write_text(script)

    # Mock the client's requests to use test_client directly
    def mock_post(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        if url == "/agent":
            # Convert agent request to direct command
            instruction = kwargs["json"]["instruction"]
            timeout = kwargs["json"].get("timeout")
            return test_client.post(
                "/dispatch", json={"command": instruction, "timeout": timeout}
            )
        return test_client.post(url, **kwargs)

    def mock_get(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        return test_client.get(url, **kwargs)

    monkeypatch.setattr("requests.post", mock_post)
    monkeypatch.setattr("requests.get", mock_get)

    # Run the script
    task_id = agent_client.run_agent(f"python {script_path}")

    # Wait for task to complete
    max_wait = 10  # seconds
    start_time = time.time()
    while time.time() - start_time < max_wait:
        task = agent_client.fetch(task_id)
        if task["status"] not in ["pending", "running"]:
            break
        time.sleep(0.1)
    else:
        pytest.fail("Task did not complete in time")

    # Verify output
    assert task["status"] == "completed"
    assert task["returncode"] == 0

    output = task["output"]
    for i in range(5):
        assert f"Line {i}" in output


@pytest.mark.asyncio
async def test_streaming_output_async(
    test_client: TestClient,
    agent_client: AgentClient,
    monkeypatch: pytest.MonkeyPatch,
    temp_dir: Path,
):
    """Test streaming output with async verification."""
    # Create a script that outputs numbers with delays
    script = """
import time
for i in range(5):
    print(f"Line {i}")
    time.sleep(0.1)  # Shorter delay for faster tests
"""

    # Create a file with the script in the temp directory
    script_path = temp_dir / "test_script_async.py"
    script_path.write_text(script)

    # Mock the client's requests to use test_client directly
    def mock_post(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        if url == "/agent":
            # Convert agent request to direct command
            instruction = kwargs["json"]["instruction"]
            timeout = kwargs["json"].get("timeout")
            return test_client.post(
                "/dispatch", json={"command": instruction, "timeout": timeout}
            )
        return test_client.post(url, **kwargs)

    def mock_get(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        return test_client.get(url, **kwargs)

    monkeypatch.setattr("requests.post", mock_post)
    monkeypatch.setattr("requests.get", mock_get)

    # Start task
    task_id = agent_client.run_agent(f"python {script_path}")

    # Monitor task output with timeouts
    start_time = time.time()
    timeout = 10  # 10 seconds total timeout
    seen_lines: Set[str] = set()
    expected_lines = {f"Line {i}" for i in range(5)}

    while time.time() - start_time < timeout and seen_lines != expected_lines:
        task = agent_client.fetch(task_id)
        if task.get("output"):
            for line in task["output"].splitlines():
                seen_lines.add(line.strip())
        await asyncio.sleep(0.1)  # Small delay between checks

    assert seen_lines == expected_lines, f"Missing lines: {expected_lines - seen_lines}"

    # Verify final task state
    final_task = agent_client.fetch(task_id)
    assert final_task["status"] == "completed"
    assert final_task["returncode"] == 0


@pytest.mark.asyncio
async def test_immediate_streaming_output(
    test_client: TestClient,
    agent_client: AgentClient,
    monkeypatch: pytest.MonkeyPatch,
    temp_dir: Path,
):
    """Test that output is streamed in real-time using SSE."""
    # Create a script that outputs numbers with timestamps
    script = """
import sys
import time

# Output initial timestamp for synchronization
print(f"START {time.time()}")
sys.stdout.flush()

# Output 10 lines with timestamps, one every 0.1 seconds
for i in range(10):
    time.sleep(0.1)  # Fixed delay between lines
    print(f"Line {i} {time.time()}")
    sys.stdout.flush()  # Force flush to get accurate timestamps
"""

    # Create a file with the script in the temp directory
    script_path = temp_dir / "test_script_timing.py"
    script_path.write_text(script)

    # Mock the client's requests to use test_client directly
    def mock_post(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        if url == "/agent":
            instruction = kwargs["json"]["instruction"]
            timeout = kwargs["json"].get("timeout")
            return test_client.post(
                "/dispatch", json={"command": instruction, "timeout": timeout}
            )
        return test_client.post(url, **kwargs)

    def mock_get(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        return test_client.get(url, **kwargs)

    monkeypatch.setattr("requests.post", mock_post)
    monkeypatch.setattr("requests.get", mock_get)

    # Run the script
    task_id = agent_client.run_agent(f"python {script_path}")

    # Track when we see each line
    line_timestamps = {}
    delays = []

    # Use test client to stream SSE events directly
    with test_client.stream("GET", f"/stream/{task_id}") as response:
        buffer = ""
        for chunk in response.iter_bytes():
            buffer += chunk.decode()

            # Process complete SSE messages
            while "\n\n" in buffer:
                message, buffer = buffer.split("\n\n", 1)

                # Skip comments/keepalive
                if message.startswith(":"):
                    continue

                # Parse SSE data
                for line in message.split("\n"):
                    if line.startswith("data: "):
                        try:
                            event = json.loads(line[6:])  # Skip "data: " prefix
                            if event["event"] == "output":
                                output = event["data"]["output"]
                                received_time = time.time()

                                # Process each line in the output
                                for output_line in output.splitlines():
                                    if output_line.startswith("START "):
                                        # Skip the START line
                                        continue
                                    elif output_line.startswith("Line "):
                                        # Extract line number and its timestamp from the script
                                        parts = output_line.split()
                                        line_num = int(parts[1])
                                        script_timestamp = float(parts[2])

                                        # Record when we first see this line
                                        if line_num not in line_timestamps:
                                            delay = received_time - script_timestamp
                                            delays.append(delay)
                                            line_timestamps[line_num] = {
                                                "script_time": script_timestamp,
                                                "received_time": received_time,
                                                "delay": delay,
                                            }

                            elif event["event"] == "complete":
                                # Task completed
                                break
                        except json.JSONDecodeError:
                            print("Failed to parse event data: " + line[6:])

    # Verify all lines were received
    assert len(line_timestamps) == 10, f"Expected 10 lines, got {len(line_timestamps)}"

    # Calculate and verify delays
    for i in range(10):
        assert i in line_timestamps, f"Missing line {i}"
        delay = line_timestamps[i]["delay"]

        # Allow larger delays in test environment (up to 1s)
        # In production with uvicorn, delays would be much smaller
        assert delay < 1.0, f"Line {i} had too much delay: {delay:.3f}s"

        if i > 0:
            # Verify that lines are received roughly 0.1s apart (±50ms)
            # More lenient in test environment
            time_between_lines = (
                line_timestamps[i]["script_time"]
                - line_timestamps[i - 1]["script_time"]
            )
            assert (
                0.05 < time_between_lines < 0.15
            ), f"Unexpected time between lines {i-1} and {i}: {time_between_lines:.3f}s"

    # Calculate and print statistics
    avg_delay = sum(delays) / len(delays)
    max_delay = max(delays)
    print("\nSSE Streaming delay statistics:")
    print(f"Average delay: {avg_delay:.3f}s")
    print(f"Maximum delay: {max_delay:.3f}s")
    print(
        "Note: Delays in test environment are higher than in production due to test client overhead"
    )


def test_streaming_error_output(
    test_client: TestClient,
    agent_client: AgentClient,
    monkeypatch: pytest.MonkeyPatch,
    temp_dir: Path,
):
    """Test streaming output when command produces errors."""
    # Create a script that outputs to both stdout and stderr
    script = """
import sys
import time

print("Starting...")
time.sleep(0.1)
print("Error message", file=sys.stderr)
time.sleep(0.1)
print("Finishing...")
sys.exit(1)
"""

    # Create a file with the script in the temp directory
    script_path = temp_dir / "test_script_error.py"
    script_path.write_text(script)

    # Mock the client's requests to use test_client directly
    def mock_post(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        if url == "/agent":
            # Convert agent request to direct command
            instruction = kwargs["json"]["instruction"]
            timeout = kwargs["json"].get("timeout")
            return test_client.post(
                "/dispatch", json={"command": instruction, "timeout": timeout}
            )
        return test_client.post(url, **kwargs)

    def mock_get(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        return test_client.get(url, **kwargs)

    monkeypatch.setattr("requests.post", mock_post)
    monkeypatch.setattr("requests.get", mock_get)

    # Run the script
    task_id = agent_client.run_agent(f"python {script_path}")

    # Wait for task to complete
    max_wait = 10  # seconds
    start_time = time.time()
    while time.time() - start_time < max_wait:
        task = agent_client.fetch(task_id)
        if task["status"] not in ["pending", "running"]:
            break
        time.sleep(0.1)
    else:
        pytest.fail("Task did not complete in time")

    # Verify final state
    assert task["status"] == "failed"
    assert task["returncode"] == 1  # Actual exit code from sys.exit(1)
    assert "Starting..." in task["output"]
    assert "Error message" in task["output"]
    assert "Finishing..." in task["output"]


def test_streaming_timeout(
    test_client: TestClient,
    agent_client: AgentClient,
    monkeypatch: pytest.MonkeyPatch,
    temp_dir: Path,
):
    """Test streaming output with timeout."""
    # Create a script that runs longer than timeout
    script = """
import time
import sys
print("Starting long task...")
sys.stdout.flush()  # Ensure output is flushed
time.sleep(0.2)  # Short delay to ensure output is captured
time.sleep(10)  # Should timeout before this
print("This should not be printed")
"""

    # Create a file with the script in the temp directory
    script_path = temp_dir / "test_script_timeout.py"
    script_path.write_text(script)

    # Mock the client's requests to use test_client directly
    def mock_post(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        if url == "/agent":
            # Convert agent request to direct command
            instruction = kwargs["json"]["instruction"]
            timeout = kwargs["json"].get("timeout")
            return test_client.post(
                "/dispatch", json={"command": instruction, "timeout": timeout}
            )
        return test_client.post(url, **kwargs)

    def mock_get(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        return test_client.get(url, **kwargs)

    monkeypatch.setattr("requests.post", mock_post)
    monkeypatch.setattr("requests.get", mock_get)
    monkeypatch.setattr("aiohttp.ClientSession", lambda: MockClientSession(test_client))

    # Run with short timeout
    task_id = agent_client.run_agent(
        f"python {script_path}", timeout=2
    )  # Slightly longer timeout

    # Use SSE to monitor task
    task_status = None
    task_returncode = None
    task_output = ""
    task_error = None

    # Stream events until task completes
    with test_client.stream("GET", f"/stream/{task_id}") as response:
        buffer = ""
        for chunk in response.iter_bytes():
            buffer += chunk.decode()

            # Process complete SSE messages
            while "\n\n" in buffer:
                message, buffer = buffer.split("\n\n", 1)

                # Skip comments/keepalive
                if message.startswith(":"):
                    continue

                # Parse SSE data
                for line in message.split("\n"):
                    if line.startswith("data: "):
                        try:
                            event = json.loads(line[6:])  # Skip "data: " prefix
                            if event["event"] == "output":
                                task_output += event["data"]["output"]
                            elif event["event"] == "complete":
                                task_status = event["data"]["status"]
                                task_returncode = event["data"]["returncode"]
                                if "error" in event["data"]:
                                    task_error = event["data"]["error"]
                                break
                        except json.JSONDecodeError:
                            continue

                if task_status is not None:
                    break

            if task_status is not None:
                break

    # Verify timeout state
    assert task_status == "failed"
    assert task_returncode == 124  # Standard timeout command exit code
    assert "Starting long task..." in task_output
    assert "This should not be printed" not in task_output
    assert task_error is not None and "timed out" in task_error.lower()


@pytest.mark.asyncio
async def test_docker_streaming_output(
    test_client: TestClient,
    agent_client: AgentClient,
    monkeypatch: pytest.MonkeyPatch,
    temp_dir: Path,
):
    """Test that client can properly display streaming output from a Docker-based task."""
    # Skip if Docker is not available
    if not is_docker_available():
        pytest.skip("Docker is not available")

    # Create a script that outputs numbers with delays
    script = """import time
for i in range(5):
    print(f"Docker Line {i}")
    time.sleep(0.1)  # Shorter delay for faster tests"""

    # Configure server to use DockerTaskExecutor
    config.task_executor = DockerTaskExecutor(docker_image="python:3.9-slim")
    config.test_mode = True

    # Mock the client's requests to use test_client directly
    def mock_post(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        if url == "/agent":
            # Convert agent request to direct command
            instruction = kwargs["json"]["instruction"]
            timeout = kwargs["json"].get("timeout")
            return test_client.post(
                "/dispatch", json={"command": instruction, "timeout": timeout}
            )
        return test_client.post(url, **kwargs)

    def mock_get(*args, **kwargs):
        url = args[0]
        if url.startswith("http://testserver"):
            url = url.replace("http://testserver", "")
        return test_client.get(url, **kwargs)

    monkeypatch.setattr("requests.post", mock_post)
    monkeypatch.setattr("requests.get", mock_get)

    try:
        # Run the script directly using python -c
        task_id = agent_client.run_agent(f"python -c {shlex.quote(script)}")

        # Wait for task to complete
        max_wait = 20  # seconds (longer timeout for Docker)
        start_time = time.time()
        while time.time() - start_time < max_wait:
            task = agent_client.fetch(task_id)
            if task["status"] not in ["pending", "running"]:
                break
            await asyncio.sleep(0.1)
        else:
            pytest.fail("Task did not complete in time")

        # Verify output
        assert task["status"] == "completed"
        assert task["returncode"] == 0

        output = task["output"]
        for i in range(5):
            assert f"Docker Line {i}" in output

    finally:
        # Clean up Docker resources
        if isinstance(config.task_executor, DockerTaskExecutor):
            for container_id in config.task_executor.containers:
                try:
                    await config.task_executor.cancel(container_id)
                except Exception:
                    pass  # Best effort cleanup

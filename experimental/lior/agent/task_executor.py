"""Task executor interface and implementations."""

from abc import ABC, abstractmethod
import asyncio
import os
from typing import Dict, Optional, Set, List, AsyncGenerator, Any
import time


class CommandFailedError(Exception):
    """Exception raised when a command fails with a specific return code."""

    def __init__(self, returncode: int, message: str):
        self.returncode = returncode
        super().__init__(message)


class CommandTimeoutError(Exception):
    """Exception raised when a command times out."""

    def __init__(self, timeout: int):
        self.timeout = timeout
        super().__init__(f"Command timed out after {timeout} seconds")


def create_task_result(
    status: str,
    command: str,
    output: str = "",
    error: Optional[str] = None,
    returncode: Optional[int] = None,
    start_time: Optional[float] = None,
    end_time: Optional[float] = None,
    branch: Optional[str] = None,
) -> Dict[str, Any]:
    """Create a TaskResult with proper initialization."""
    now = time.time()
    start_time = start_time or now
    end_time = end_time or now
    duration = end_time - start_time

    return {
        "status": status,
        "command": command,
        "output": output,
        "error": error,
        "returncode": returncode,
        "start_time": start_time,
        "end_time": end_time,
        "duration": duration,
        "branch": branch,
    }


def get_common_env_vars() -> Dict[str, str]:
    """Get common environment variables for task execution."""
    env = os.environ.copy()
    env["PYTHONUNBUFFERED"] = "1"  # Force Python to be unbuffered
    env["PYTHONFAULTHANDLER"] = "1"  # Better error reporting
    return env


class TaskExecutor(ABC):
    """Interface for task execution."""

    @abstractmethod
    async def stream(
        self,
        task_id: str,
        command: str,
        timeout: Optional[int] = None,
        branch: Optional[str] = None,
    ) -> AsyncGenerator[str, None]:
        """Stream task output in real-time.

        Args:
            task_id: Unique task identifier
            command: Command to execute
            timeout: Optional timeout in seconds
            branch: Optional git branch to use

        Yields:
            Output chunks as they become available
        """
        yield ""  # Abstract method placeholder

    @abstractmethod
    async def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current result for a task.

        Args:
            task_id: Task ID to get result for

        Returns:
            Current TaskResult if available, None otherwise
        """
        pass

    async def execute(
        self,
        task_id: str,
        command: str,
        timeout: Optional[int] = None,
        branch: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Execute a task.

        Args:
            task_id: Unique task identifier
            command: Command to execute
            timeout: Optional timeout in seconds
            branch: Optional git branch to use

        Returns:
            TaskResult containing execution status and details
        """
        # Collect output using stream()
        start_time = time.time()
        output: List[str] = []
        try:
            # Use async for to iterate over the AsyncGenerator
            async for chunk in self.stream(task_id, command, timeout, branch):
                output.append(chunk)

            # Get final result from executor
            result = await self.get_result(task_id)
            if result is not None:
                return result

            # If no result available but stream completed, assume success
            return create_task_result(
                status="completed",
                command=command,
                output="".join(output),
                returncode=0,
                start_time=start_time,
                branch=branch,
            )

        except CommandTimeoutError as e:
            return create_task_result(
                status="failed",
                command=command,
                output="".join(output),
                error=str(e),
                returncode=124,  # Standard timeout return code
                start_time=start_time,
                branch=branch,
            )

        except CommandFailedError as e:
            return create_task_result(
                status="failed",
                command=command,
                output="".join(output),
                error=str(e),
                returncode=e.returncode,
                start_time=start_time,
                branch=branch,
            )

        except Exception as e:
            # Determine appropriate return code
            if isinstance(e, FileNotFoundError):
                returncode = 127  # Command not found
            elif isinstance(e, PermissionError):
                returncode = 126  # Command not executable
            else:
                returncode = 1  # General error

            return create_task_result(
                status="failed",
                command=command,
                output="".join(output),
                error=str(e),
                returncode=returncode,
                start_time=start_time,
                branch=branch,
            )

    @abstractmethod
    async def cancel(self, task_id: str) -> None:
        """Cancel a running task.

        Args:
            task_id: Task ID to cancel
        """
        pass


class DockerTaskExecutor(TaskExecutor):
    """Task executor that runs commands in Docker containers."""

    def __init__(self, docker_image: str = "agent-dev") -> None:
        self.docker_image: str = docker_image
        self.containers: Set[str] = set()
        self._results: Dict[str, Dict[str, Any]] = {}

    async def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current result for a task."""
        return self._results.get(task_id)

    async def stream(
        self,
        task_id: str,
        command: str,
        timeout: Optional[int] = None,
        branch: Optional[str] = None,
    ) -> AsyncGenerator[str, None]:
        """Stream output from a Docker container."""
        container_name: str = f"agent-{task_id}"
        self.containers.add(container_name)
        start_time = time.time()

        try:
            # Set environment variables
            env = get_common_env_vars()

            # Debug logging
            print(f"[DEBUG] Starting task {task_id}: {command}")
            print(f"[DEBUG] Working directory: {os.getcwd()}")
            print(f"[DEBUG] Environment: {env}")

            # Create container with environment variables
            docker_args = [
                "docker",
                "run",
                "--rm",
                "--name",
                container_name,
                "-v",
                "/mnt/efs:/mnt/efs",
                "-v",
                f"{os.path.expanduser('~/.augment')}:/home/<USER>/.augment",
                "-v",
                f"{os.path.expanduser('~/.kube')}:/home/<USER>/.kube",
                "-v",
                f"{os.path.expanduser('~/.config/gh')}:/home/<USER>/.config/gh",
                "--mount",
                f"type=bind,source={os.path.expanduser('~/.config/gcloud')},destination=/home/<USER>/.config/gcloud",
            ]

            # Add environment variables
            for key, value in env.items():
                docker_args.extend(["-e", f"{key}={value}"])

            docker_args.extend(
                [
                    self.docker_image,
                ]
            )

            process: asyncio.subprocess.Process = await asyncio.create_subprocess_exec(
                *docker_args,
                "bash",
                "-c",
                f"""set -e
git fetch --all > /dev/null 2>&1 || true
git reset --hard > /dev/null 2>&1 || true
{f'''
if git rev-parse --verify {branch} > /dev/null 2>&1; then
    git checkout {branch} > /dev/null 2>&1 || true
elif git rev-parse --verify origin/{branch} > /dev/null 2>&1; then
    git checkout -b {branch} origin/{branch} > /dev/null 2>&1 || true
else
    echo "Branch {branch} not found" >&2
    exit 1
fi
git pull origin {branch} > /dev/null 2>&1 || true
''' if branch else 'git pull > /dev/null 2>&1 || true'}
bash research/research-init.sh --cpu --reqs-only > /dev/null 2>&1 || true
bazel run //tools/generate_proto_typestubs > /dev/null 2>&1 || true
bazel run //base:install > /dev/null 2>&1 || true
pnpm install > /dev/null 2>&1 || true
exec {command}""",  # Use exec to replace shell with the command
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
            )
            print("[DEBUG] Process created")

            output_buffer = []
            try:
                async with asyncio.timeout(timeout or 3600):
                    if process.stdout is not None:
                        while True:
                            line = await process.stdout.readline()
                            if not line:  # EOF
                                break
                            decoded_line = line.decode()
                            output_buffer.append(decoded_line)
                            yield decoded_line

                    returncode = await process.wait()
                    print("[DEBUG] Process completed")
                    output = "".join(output_buffer)

                    if returncode != 0:
                        error_msg = f"Command failed with exit code {returncode}"
                        self._results[task_id] = create_task_result(
                            status="failed",
                            command=command,
                            output=output,
                            error=error_msg,
                            returncode=returncode,
                            start_time=start_time,
                            branch=branch,
                        )
                        raise CommandFailedError(returncode, error_msg)

                    self._results[task_id] = create_task_result(
                        status="completed",
                        command=command,
                        output=output,
                        returncode=0,
                        start_time=start_time,
                        branch=branch,
                    )

            except asyncio.TimeoutError:
                await self.cancel(task_id)
                output = "".join(output_buffer)
                error_msg = f"Command timed out after {timeout or 3600} seconds"
                self._results[task_id] = create_task_result(
                    status="failed",
                    command=command,
                    output=output,
                    error=error_msg,
                    returncode=124,  # Standard timeout return code
                    start_time=start_time,
                    branch=branch,
                )
                raise CommandTimeoutError(timeout or 3600)

        finally:
            self.containers.remove(container_name)

    async def cancel(self, task_id: str) -> None:
        """Cancel a running Docker container."""
        container_name: str = f"agent-{task_id}"
        if container_name in self.containers:
            try:
                process: asyncio.subprocess.Process = (
                    await asyncio.create_subprocess_exec(
                        "docker",
                        "kill",
                        container_name,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.STDOUT,
                    )
                )
                await process.communicate()
            except Exception:
                pass  # Ignore errors during cancellation


class LocalTaskExecutor(TaskExecutor):
    """Task executor that runs commands locally for testing."""

    def __init__(self) -> None:
        self.processes: Dict[str, asyncio.subprocess.Process] = {}
        self._output_buffers: Dict[str, List[str]] = {}
        self._results: Dict[str, Dict[str, Any]] = {}

    async def get_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current result for a task."""
        return self._results.get(task_id)

    def _update_task_status(
        self,
        task_id: str,
        status: str,
        command: str,
        output: str = "",
        error: Optional[str] = None,
        returncode: Optional[int] = None,
        start_time: Optional[float] = None,
        branch: Optional[str] = None,
    ) -> None:
        """Update task status with debug logging."""
        current = self._results.get(task_id)
        if current:
            print(
                f"[DEBUG] Task {task_id} status change: {current['status']} -> {status}"
            )
        else:
            print(f"[DEBUG] Task {task_id} initial status: {status}")

        self._results[task_id] = create_task_result(
            status=status,
            command=command,
            output=output,
            error=error,
            returncode=returncode,
            start_time=start_time,
            branch=branch,
        )

    async def stream(
        self,
        task_id: str,
        command: str,
        timeout: Optional[int] = None,
        branch: Optional[str] = None,
    ) -> AsyncGenerator[str, None]:
        """Stream output from a local process."""
        start_time = time.time()
        try:
            # Set environment variables for unbuffered output
            env = get_common_env_vars()

            # Debug logging
            print(f"[DEBUG] Starting task {task_id}: {command}")
            print(f"[DEBUG] Working directory: {os.getcwd()}")
            print(f"[DEBUG] Environment: {env}")

            # Initialize output buffer
            self._output_buffers[task_id] = []

            # Set initial task status
            self._update_task_status(
                task_id,
                status="running",
                command=command,
                start_time=start_time,
                branch=branch,
            )

            # Wrap command with timeout if specified
            if timeout:
                # Use GNU timeout command with proper signal handling
                wrapped_command = f"timeout {timeout} {command}"
            else:
                wrapped_command = command

            # Start process with shell=True for shell commands
            process: asyncio.subprocess.Process = await asyncio.create_subprocess_shell(
                wrapped_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
                env=env,
                shell=True,  # Always use shell=True for consistent behavior
            )

            self.processes[task_id] = process

            try:
                # Read output line by line for efficient streaming
                if process.stdout is not None:
                    while True:
                        line = await process.stdout.readline()
                        if not line:  # EOF
                            break
                        decoded_line = line.decode()
                        self._output_buffers[task_id].append(decoded_line)
                        yield decoded_line

                # Wait for process to complete
                returncode = await process.wait()
                output = "".join(self._output_buffers[task_id])

                # Debug logging
                print(f"[DEBUG] Process completed with return code: {returncode}")
                print(f"[DEBUG] Final output: {output}")

                # Handle timeout return code (124) specially
                if returncode == 124:
                    error_msg = f"Command timed out after {timeout} seconds"
                    self._update_task_status(
                        task_id,
                        status="failed",
                        command=command,
                        output=output,
                        error=error_msg,
                        returncode=124,
                        start_time=start_time,
                        branch=branch,
                    )
                    raise CommandTimeoutError(timeout or 3600)
                elif returncode != 0:
                    error_msg = f"Command failed with exit code {returncode}"
                    self._update_task_status(
                        task_id,
                        status="failed",
                        command=command,
                        output=output,
                        error=error_msg,
                        returncode=returncode,
                        start_time=start_time,
                        branch=branch,
                    )
                    raise CommandFailedError(returncode, error_msg)

                # Update result with success
                self._update_task_status(
                    task_id,
                    status="completed",
                    command=command,
                    output=output,
                    returncode=0,
                    start_time=start_time,
                    branch=branch,
                )

            except asyncio.CancelledError:
                # Handle task cancellation
                await self.cancel(task_id)
                output = "".join(self._output_buffers[task_id])
                error_msg = "Task cancelled"
                self._update_task_status(
                    task_id,
                    status="failed",
                    command=command,
                    output=output,
                    error=error_msg,
                    returncode=130,  # Standard cancellation return code
                    start_time=start_time,
                    branch=branch,
                )
                raise

        except Exception as e:
            # Debug logging
            print(f"[DEBUG] Error in task {task_id}: {str(e)}")

            # Handle any other errors
            output = "".join(self._output_buffers.get(task_id, []))
            error_msg = str(e)

            # Determine appropriate return code
            if isinstance(e, FileNotFoundError):
                returncode = 127  # Command not found
            elif isinstance(e, PermissionError):
                returncode = 126  # Command not executable
            elif isinstance(e, CommandTimeoutError):
                returncode = 124  # Standard timeout return code
            elif isinstance(e, CommandFailedError):
                returncode = e.returncode  # Use actual return code
            else:
                returncode = 1  # General error

            self._update_task_status(
                task_id,
                status="failed",
                command=command,
                output=output,
                error=error_msg,
                returncode=returncode,
                start_time=start_time,
                branch=branch,
            )
            raise

        finally:
            # Debug logging
            print(f"[DEBUG] Task {task_id} cleanup")

            # Clean up process
            if task_id in self.processes:
                try:
                    await self.cancel(task_id)
                except Exception:
                    pass
                self.processes.pop(task_id, None)
                self._output_buffers.pop(task_id, None)

    async def cancel(self, task_id: str) -> None:
        """Cancel a running local process."""
        if task_id in self.processes:
            process = self.processes[task_id]
            try:
                # Try graceful termination first
                process.terminate()
                try:
                    # Wait for process to terminate
                    await asyncio.wait_for(process.wait(), timeout=2.0)
                except asyncio.TimeoutError:
                    # If termination doesn't work, force kill
                    process.kill()
                    # Wait for the kill to take effect
                    await asyncio.wait_for(process.wait(), timeout=1.0)
            except ProcessLookupError:
                pass  # Process already terminated
            except Exception as e:
                print(f"[DEBUG] Error canceling task {task_id}: {str(e)}")

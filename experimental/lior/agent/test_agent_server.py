"""Tests for agent server."""

import pytest
import time
import asyncio
import json
from typing import Callable, Generator, Dict, Any, cast, List
from fastapi.testclient import TestClient
from experimental.lior.agent.agent_server import (
    app,
    config,
    DispatchRequest,
    TaskStreamResponse,
)
from experimental.lior.agent.task_executor import LocalTaskExecutor
from experimental.lior.agent.task_types import TaskResult


@pytest.fixture(scope="module")
def client() -> Generator[TestClient, None, None]:
    """Create a test client using LocalTaskExecutor."""
    # Use LocalTaskExecutor for testing
    config.task_executor = LocalTaskExecutor()
    with TestClient(app) as client:
        yield client


@pytest.fixture
def wait_for_task() -> Callable[[TestClient, str], TaskResult]:
    """Helper to wait for task completion."""

    def _wait_for_task(client: TestClient, task_id: str) -> TaskResult:
        start_time = time.time()
        while True:
            response = client.get(f"/fetch/{task_id}")
            if response.status_code != 200:
                raise RuntimeError(f"Failed to fetch task: {response.text}")

            data = cast(TaskResult, response.json())
            status = data.get("status")
            if status is not None and status not in ["pending", "running"]:
                return data

            if time.time() - start_time > 5:  # Fixed 5 second timeout
                raise TimeoutError(f"Task {task_id} did not complete in 5 seconds")

            # Use sleep instead of asyncio.sleep since we're in sync context
            time.sleep(0.1)

    return _wait_for_task


def test_dispatch_task(
    client: TestClient, wait_for_task: Callable[[TestClient, str], TaskResult]
) -> None:
    """Test dispatching a simple task."""
    response = client.post(
        "/dispatch", json=DispatchRequest(command="echo hello").model_dump()
    )
    assert response.status_code == 200, f"Failed to dispatch task: {response.text}"
    data = cast(Dict[str, Any], response.json())
    task_id = data.get("task_id")
    assert task_id is not None, "Response missing task_id"
    assert data.get("status") == "pending"

    try:
        result = wait_for_task(client, task_id)
        status = result.get("status")
        assert status == "completed"
        output = result.get("output")
        assert output is not None and "hello" in output
    except TimeoutError:
        pytest.fail("Task timed out")


def test_list_tasks(
    client: TestClient, wait_for_task: Callable[[TestClient, str], TaskResult]
) -> None:
    """Test listing tasks."""
    # First dispatch a task
    response = client.post(
        "/dispatch", json=DispatchRequest(command="echo hello").model_dump()
    )
    assert response.status_code == 200, f"Failed to dispatch task: {response.text}"
    data = cast(Dict[str, Any], response.json())
    task_id = data.get("task_id")
    assert task_id is not None, "Response missing task_id"

    try:
        # Wait for task completion
        wait_for_task(client, task_id)

        # Then list tasks
        response = client.get("/list")
        assert response.status_code == 200
        list_data = cast(Dict[str, Any], response.json())
        tasks = list_data.get("tasks", [])
        matching_tasks = [t for t in tasks if t.get("task_id") == task_id]
        assert len(matching_tasks) == 1, f"Task {task_id} not found in task list"
        task = cast(TaskResult, matching_tasks[0])
        assert task.get("status") == "completed"
        output = task.get("output")
        assert output is not None and "hello" in output
    except TimeoutError:
        pytest.fail("Task timed out")


def test_fetch_task(
    client: TestClient, wait_for_task: Callable[[TestClient, str], TaskResult]
) -> None:
    """Test fetching a specific task."""
    # First dispatch a task
    response = client.post(
        "/dispatch", json=DispatchRequest(command="echo hello").model_dump()
    )
    assert response.status_code == 200, f"Failed to dispatch task: {response.text}"
    data = cast(Dict[str, Any], response.json())
    task_id = data.get("task_id")
    assert task_id is not None, "Response missing task_id"

    try:
        result = wait_for_task(client, task_id)
        assert result.get("status") == "completed"
        output = result.get("output")
        assert output is not None and "hello" in output
    except TimeoutError:
        pytest.fail("Task timed out")


def test_cancel_task(client: TestClient) -> None:
    """Test cancelling a task."""
    # First dispatch a long-running task
    response = client.post(
        "/dispatch", json=DispatchRequest(command="sleep 10").model_dump()
    )
    assert response.status_code == 200, f"Failed to dispatch task: {response.text}"
    data = cast(Dict[str, Any], response.json())
    task_id = data.get("task_id")
    assert task_id is not None, "Response missing task_id"

    # Give it a moment to start
    time.sleep(0.1)

    # Then cancel it
    response = client.post(f"/cancel/{task_id}")
    assert response.status_code == 200, f"Failed to cancel task: {response.text}"
    cancel_data = cast(Dict[str, Any], response.json())
    assert cancel_data.get("task_id") == task_id
    status = cancel_data.get("status")
    assert status is not None and status in ["cancelled", "failed"]


def test_max_concurrent_tasks(client: TestClient) -> None:
    """Test maximum concurrent tasks limit."""
    # Dispatch max_concurrent_tasks + 1 tasks
    tasks = []
    for i in range(config.MAX_CONCURRENT_TASKS + 1):
        response = client.post(
            "/dispatch",
            json=DispatchRequest(
                command=f"sleep {5+i}"
            ).model_dump(),  # Different sleep times to avoid race conditions
        )
        if i < config.MAX_CONCURRENT_TASKS:
            assert (
                response.status_code == 200
            ), f"Failed to dispatch task {i}: {response.text}"
            data = cast(Dict[str, Any], response.json())
            task_id = data.get("task_id")
            assert task_id is not None, f"Response missing task_id for task {i}"
            tasks.append(task_id)
        else:
            assert response.status_code == 429, "Expected too many requests error"

    # Cancel all tasks
    for task_id in tasks:
        response = client.post(f"/cancel/{task_id}")
        assert (
            response.status_code == 200
        ), f"Failed to cancel task {task_id}: {response.text}"


def test_agent_endpoint(
    client: TestClient, wait_for_task: Callable[[TestClient, str], TaskResult]
) -> None:
    """Test the agent endpoint with a simple command."""
    # Use a simple command that mimics agent behavior
    response = client.post(
        "/dispatch",  # Use dispatch instead of agent for testing
        json=DispatchRequest(
            command="echo 'test instruction'"  # Simple command that should work
        ).model_dump(),
    )
    assert response.status_code == 200, f"Failed to start task: {response.text}"
    data = cast(Dict[str, Any], response.json())
    task_id = data.get("task_id")
    assert task_id is not None, "Response missing task_id"
    assert data.get("status") == "pending"

    try:
        result = wait_for_task(client, task_id)
        status = result.get("status")
        assert (
            status == "completed"
        ), f"Task failed: {result.get('error', 'Unknown error')}"
        output = result.get("output")
        assert (
            output is not None and "test instruction" in output
        ), "Expected output not found"
    except TimeoutError as e:
        # Get final task status for better error reporting
        response = client.get(f"/fetch/{task_id}")
        if response.status_code == 200:
            task_data = cast(TaskResult, response.json())
            pytest.fail(
                f"Task timed out. Final status: {task_data.get('status')}, Error: {task_data.get('error')}"
            )
        else:
            pytest.fail(f"Task timed out and failed to fetch final status: {str(e)}")


def parse_sse_event(event_data: str) -> List[TaskStreamResponse]:
    """Parse SSE event data into TaskStreamResponse objects."""
    events = []
    for line in event_data.strip().split("\n\n"):
        if line.startswith("data: "):
            try:
                data = json.loads(line[6:])  # Skip "data: " prefix
                events.append(TaskStreamResponse(**data))
            except json.JSONDecodeError:
                continue  # Skip invalid JSON
    return events


async def collect_stream_events(
    client: TestClient,
    task_id: str,
    timeout: float = 5.0,
    check_completion: bool = True,
) -> List[TaskStreamResponse]:
    """Collect all events from a stream until completion or timeout."""
    events = []
    start_time = time.time()
    completed = False

    # Create a single stream connection and keep it open
    with client.stream("GET", f"/stream/{task_id}") as stream:
        while not completed and time.time() - start_time < timeout:
            try:
                # Read lines with a timeout
                for line in stream.iter_lines():
                    if line:
                        new_events = parse_sse_event(line + "\n\n")
                        events.extend(new_events)

                        if check_completion and any(
                            e.event == "complete" for e in new_events
                        ):
                            completed = True
                            break

                        if any(e.event == "error" for e in new_events):
                            # For error events, return immediately
                            return events

                    # Check timeout after each line
                    if time.time() - start_time >= timeout:
                        break

                if completed:
                    break

            except Exception:
                # If we get an error reading the stream, wait a bit and continue
                await asyncio.sleep(0.1)
                continue

    return events


@pytest.mark.asyncio
async def test_concurrent_streaming(client: TestClient) -> None:
    """Test concurrent streaming of multiple tasks."""
    # Start multiple tasks that produce output over time
    task_ids = []
    for i in range(3):  # Test with 3 concurrent tasks
        response = client.post(
            "/dispatch",
            json=DispatchRequest(
                command=f"bash -c 'for n in 1 2 3; do echo \"Task {i} output $n\"; sleep 0.1; done'"
            ).model_dump(),
        )
        assert response.status_code == 200
        task_id = response.json()["task_id"]
        task_ids.append(task_id)

    # Collect events from all tasks concurrently
    tasks = [collect_stream_events(client, task_id) for task_id in task_ids]
    task_events = await asyncio.gather(*tasks)

    # Verify each task's events
    for i, events in enumerate(task_events):
        # Should have at least initial status, some output events, and completion
        assert len(events) >= 5, f"Task {task_ids[i]} should have multiple events"

        # Verify event sequence
        status_events = [e for e in events if e.event == "status"]
        output_events = [e for e in events if e.event == "output"]
        complete_events = [e for e in events if e.event == "complete"]

        assert len(status_events) >= 1, f"Task {task_ids[i]} should have initial status"
        assert len(output_events) >= 3, f"Task {task_ids[i]} should have output events"
        assert (
            len(complete_events) == 1
        ), f"Task {task_ids[i]} should have one completion event"

        # Verify completion status
        assert (
            complete_events[0].data["status"] == "completed"
        ), f"Task {task_ids[i]} should complete successfully"

        # Verify output content
        output_text = "".join(e.data.get("output", "") for e in output_events)
        expected_outputs = [f"Task {i} output {n}" for n in [1, 2, 3]]
        for expected in expected_outputs:
            assert (
                expected in output_text
            ), f"Expected output '{expected}' not found in task {task_ids[i]}"


@pytest.mark.asyncio
async def test_stream_error_handling(client: TestClient) -> None:
    """Test error handling in streaming endpoint."""
    # Test non-existent task
    events = await collect_stream_events(
        client, "nonexistent-task", timeout=1.0, check_completion=False
    )
    assert len(events) == 1, "Should get exactly one error event"
    assert events[0].event == "error"
    assert "Task not found" in events[0].data["error"]

    # Test task that fails
    response = client.post(
        "/dispatch",
        json=DispatchRequest(command="nonexistent-command").model_dump(),
    )
    assert response.status_code == 200
    task_id = response.json()["task_id"]

    events = await collect_stream_events(client, task_id)

    # Verify error handling
    status_events = [e for e in events if e.event == "status"]
    complete_events = [e for e in events if e.event == "complete"]

    assert len(status_events) >= 1, "Should have initial status"
    assert len(complete_events) == 1, "Should have completion event"
    assert complete_events[0].data["status"] == "failed", "Task should fail"
    assert complete_events[0].data["error"] is not None, "Should have error message"


@pytest.mark.asyncio
async def test_stream_concurrent_clients(client: TestClient) -> None:
    """Test multiple clients streaming the same task."""
    # Start a task that produces output over time
    response = client.post(
        "/dispatch",
        json=DispatchRequest(
            command="bash -c 'for n in 1 2 3; do echo \"Output $n\"; sleep 0.1; done'"
        ).model_dump(),
    )
    assert response.status_code == 200
    task_id = response.json()["task_id"]

    # Wait a tiny bit to ensure task is started
    await asyncio.sleep(0.1)

    # Collect events from multiple clients concurrently
    tasks = [collect_stream_events(client, task_id) for _ in range(3)]
    client_events = await asyncio.gather(*tasks)

    # Verify all clients received the completion event with the same final state
    completion_events = []
    for i, events in enumerate(client_events):
        complete_events = [e for e in events if e.event == "complete"]
        assert (
            len(complete_events) == 1
        ), f"Client {i} should have exactly one completion event"
        completion_events.append(complete_events[0])

    # All completion events should have the same status, returncode, and output
    for i in range(1, len(completion_events)):
        assert (
            completion_events[0].data["status"] == completion_events[i].data["status"]
        ), f"Client {i} has different completion status"
        assert (
            completion_events[0].data["returncode"]
            == completion_events[i].data["returncode"]
        ), f"Client {i} has different return code"
        assert (
            completion_events[0].data["status"] == "completed"
        ), "Task should complete successfully"
        assert completion_events[0].data["returncode"] == 0, "Task should return zero"

    # Verify final output in completion event contains all expected content
    final_output = completion_events[0].data.get("output", "")
    expected_outputs = [f"Output {n}" for n in [1, 2, 3]]
    for expected in expected_outputs:
        assert (
            expected in final_output
        ), f"Expected output '{expected}' not found in final output"

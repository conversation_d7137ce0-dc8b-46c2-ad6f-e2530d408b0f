task: Find bugs in recent landing PRs.
steps:
  - For each of the recent 5 merged PRs, analyze the diff for potential bugs, such as:
    - Empty or null inputs
    - Maximum/minimum values
    - Malformed data structures
    - Unexpected API responses
    - Resource exhaustion scenarios
    - Concurrent access patterns
    - Unicode and special characters
    - Boundary conditions
  - In your final answer write a summary of each PR, including the list of issues found together with the code snippets that cause them surrounded by triple backticks.

Guidelines:
- Use GH cli to get the list of recent PRs and the PR diffs with `gh pr list -s merged -L 5 --json number,title,files` and `gh pr diff <pr_number>`
- For each potential bug get more context to understand the codebase and the changes made.
- Never switch branches or commit changes.
- When flagging issues make sure to conduct thorough review and only report real bugs.


#!/bin/bash
set -e

# Define allowed authors
allowed_authors=("liornm" "urikz" "guygurari" "Mr<PERSON><PERSON><PERSON><PERSON>" "diehuxx" "augmentmoogi" "jgalenson" "ranhalprin" "gauntface" "igor0" "c-flaherty" "<PERSON><PERSON><PERSON>" "mtpauly" "bddavis" "cmsflash" "Jareddvw" "devangjhabakh")

# Loop through open PRs
for pr_info in $(gh pr list -L 100 --base main --json number,author,updatedAt,isDraft,isCrossRepository --jq ".[] | select(.isDraft == false and .isCrossRepository == false and .updatedAt > \"$(date -d '7 days ago' -u +%Y-%m-%dT%H:%M:%SZ)\") | \"\(.number):\(.author.login)\""); do
    pr=$(echo $pr_info | cut -d':' -f1)
    author=$(echo $pr_info | cut -d':' -f2)

    # Check if author is in allowed list
    author_allowed=false
    for allowed_author in "${allowed_authors[@]}"; do
        if [ "$author" = "$allowed_author" ]; then
            author_allowed=true
            break
        fi
    done

    if [ "$author_allowed" = false ]; then
        echo "Skipping PR #$pr from author $author (not in allowed list)"
        continue
    fi

    # Get check runs status for the PR
    checks=$(gh pr view $pr --json statusCheckRollup --jq '.statusCheckRollup[] | select(.conclusion=="FAILURE") | .name')

    if [ -n "$checks" ]; then
        # Check if there are any autofix branches for this PR
        pr_branch=$(gh pr view $pr --json headRefName --jq '.headRefName')
        branches=$(gh pr list --base "$pr_branch" --json headRefName --jq '.[].headRefName | select(startswith("autofix-"))')
        if [ -n "$branches" ]; then
            echo "Skipping PR #$pr as it has autofix branches: [$branches]"
            continue
        fi

        echo "Found failing checks in PR #$pr by $author: ['$checks']"
        echo "Attempting to fetch logs..."

        while IFS= read -r check; do
            echo "Fetching logs for check: '$check'"
            details_url=$(gh pr view $pr --json statusCheckRollup --jq ".statusCheckRollup[] | select(.name==\"$check\" and .conclusion==\"FAILURE\") | .detailsUrl")

            if [ -z "$details_url" ]; then
                echo "Error: Details URL is empty"
                continue
            fi

            run_id=$(echo "$details_url" | grep -oP '(?<=runs/)\d+' || echo "")

            if [ -z "$run_id" ]; then
                echo "Error: Could not extract run ID from details URL"
                continue
            fi
            log_file="/tmp/$(echo "$pr-$check" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-zA-Z0-9._-]/_/g').log"
            if gh run view $run_id --log > "$log_file" 2>/dev/null; then
                if [ -s "$log_file" ]; then
                    echo "Successfully found logs for PR #$pr by $author, check: '$check', saved to $log_file"
                    exit 0
                else
                    echo "Log is empty for check: '$check'"
                fi
            else
                echo "Failed to fetch logs for check: '$check' (Exit code: $?)"
            fi
        done <<< "$checks"
    fi
done

echo "No PRs with accessible failing logs found"

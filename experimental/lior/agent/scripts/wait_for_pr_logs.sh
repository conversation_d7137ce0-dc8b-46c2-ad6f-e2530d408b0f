#!/bin/bash
set -e

if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <pr_number>"
    exit 1
fi

pr="$1"
interval=10  # Seconds to wait between checks

echo "Monitoring PR #$pr for logs from first failed check"

# Loop until all checks are completed
pending_checks=$(gh pr view "$pr" --json statusCheckRollup --jq '.statusCheckRollup[] | select(.status=="IN_PROGRESS" or .state=="PENDING") | .name')
if [ -n "$pending_checks" ]; then
    echo "Waiting for checks to complete:"
    echo "$pending_checks" | while read -r check; do
        echo "  - $check"
    done
    while true; do

        if [ -z "$pending_checks" ]; then
            break
        fi
        pending_checks=$(gh pr view "$pr" --json statusCheckRollup --jq '.statusCheckRollup[] | select(.status=="IN_PROGRESS" or .state=="PENDING") | .name')
        sleep "$interval"
    done
fi

failed_checks=$(gh pr view $pr --json statusCheckRollup --jq '.statusCheckRollup[] | select(.conclusion=="FAILURE") | .name')

if [ -n "$failed_checks" ]; then
    echo "Failed checks found: [$failed_checks]"
    echo "Attempting to fetch logs..."
    while IFS= read -r check; do
        echo "Fetching logs for check: '$check'"
        details_url=$(gh pr view $pr --json statusCheckRollup --jq ".statusCheckRollup[] | select(.name==\"$check\" and .conclusion==\"FAILURE\") | .detailsUrl")
        if [ -z "$details_url" ]; then
            echo "Error: Details URL is empty"
            continue
        fi
        run_id=$(echo "$details_url" | grep -oP '(?<=runs/)\d+' || echo "")
        if [ -z "$run_id" ]; then
            echo "Error: Could not extract run ID from details URL"
            continue
        fi
        log_file="/tmp/$(echo "$pr-$check" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-zA-Z0-9._-]/_/g').log"
        if gh run view $run_id --log > "$log_file" 2>/dev/null; then
            if [ -s "$log_file" ]; then
                echo "Successfully found logs for PR #$pr, check: '$check', saved to $log_file"
            else
                echo "Log is empty for check: '$check'"
            fi
        else
            echo "Failed to fetch logs for check: '$check' (Exit code: $?)"
        fi
    done <<< "$failed_checks"
else
    echo "No failed checks found for PR #$pr"
fi

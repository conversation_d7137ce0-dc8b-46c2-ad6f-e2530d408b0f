let activeAgentId = null;
const agents = new Map();

class Agent {
    constructor(id, name) {
        this.id = id;
        this.name = name || `Agent ${id}`;
        this.status = 'idle';
        this.outputTerminal = null;
        this.debugTerminal = null;
        this.eventSource = null;
        this.instruction = '';
        this.isStarted = false;
        this.branch = '';
        this.taskId = null;
    }
}

document.addEventListener('DOMContentLoaded', () => {
    initializeBranchSelector();
    createNewAgent();
    setupInstructionShortcut();
});

function initializeBranchSelector() {
    const container = document.querySelector('.branch-selection');
    if (container) {
        window.mainBranchSelector = new BranchSelector(container, 'main');
        fetch('/git-branches')
            .then(response => response.json())
            .then(branches => window.mainBranchSelector.setBranches(branches))
            .catch(error => console.error('Error fetching branches:', error));
    }
}

function setupInstructionShortcut() {
    document.getElementById('instruction').addEventListener('keydown', async (e) => {
        if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
            e.preventDefault();
            const instruction = e.target.value.trim();
            if (instruction) {
                const currentAgentId = activeAgentId;
                await createAgent(instruction);
                if (agents.get(currentAgentId)?.isStarted) {
                    await createNewAgent();
                }
            }
        }
    });
}

async function createNewAgent() {
    const id = Date.now().toString();
    const agent = new Agent(id);
    agents.set(id, agent);
    updateAgentsGrid();
    createTerminalForAgent(agent);
    switchToAgent(id);
    document.getElementById("instruction").value = "";
    window.mainBranchSelector?.clear();
    document.getElementById("instruction").focus();
}

async function createAgent(instruction) {
    if (!activeAgentId || !agents.has(activeAgentId)) {
        appendToTerminal("No active agent selected", "red", true);
        return;
    }
    const agent = agents.get(activeAgentId);
    if (agent.isStarted) {
        appendToTerminal("Agent already started", "red", true);
        return;
    }
    if (!instruction.trim()) {
        appendToTerminal("Error: Instruction cannot be empty", "red", true);
        return;
    }
    agent.instruction = instruction;
    agent.branch = window.mainBranchSelector?.getBranch() || '';
    agent.isStarted = true;
    agent.status = 'pending';
    updateAgentsGrid();
    document.getElementById('createAgent').style.display = 'none';
    agent.outputTerminal.innerHTML = "";
    agent.debugTerminal.innerHTML = "";
    appendToTerminal("=== Agent Creation Debug Info ===", "yellow", true);
    appendToTerminal(`Instruction: ${instruction}`, "yellow", true);
    appendToTerminal(`Branch: ${agent.branch}`, "yellow", true);
    appendToTerminal("------------------------", "yellow", true);

    const requestBody = { instruction, pickle_log_dir: "/tmp/agent_logs/" };
    if (agent.branch) requestBody.branch = agent.branch;

    try {
        const response = await fetch('/agent', {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        agent.taskId = data.task_id;
        updateAgentsGrid();
        monitorTask(agent.taskId);
    } catch (error) {
        agent.status = 'failed';
        updateAgentsGrid();
        appendToTerminal(`Error: ${error.toString()}`, "red", true);
    }
}

function monitorTask(taskId) {
    const agent = Array.from(agents.values()).find(a => a.taskId === taskId);
    if (!agent) {
        console.error(`No agent found for task ${taskId}`);
        return;
    }
    if (agent.eventSource) agent.eventSource.close();
    agent.eventSource = new EventSource(`/stream/${taskId}`);
    agent.eventSource.onmessage = event => handleTaskEvent(JSON.parse(event.data), agent);
    agent.eventSource.onerror = () => {
        appendToTerminal('EventSource error', 'red', true, agent);
        agent.eventSource.close();
    };
}

function handleTaskEvent(event, agent) {
    if (!event || !event.event) return;
    switch (event.event) {
        case 'status':
            updateAgentStatus(event.data?.status, agent);
            break;
        case 'output':
            appendOutput(event.data?.output, agent);
            break;
        case 'error':
            updateAgentStatus(event.data?.status, agent);
            appendToTerminal(event.data?.error, 'red', true, agent);
            break;
        case 'complete':
            updateAgentStatus(event.data?.status, agent);
            if (event.data?.error) appendToTerminal(event.data.error, 'red', true, agent);
            agent.eventSource?.close();
            break;
    }
}

function updateAgentStatus(status, agent) {
    if (status !== agent.status) {  // Only update and log if status actually changed
        agent.status = status;
        updateAgentsGrid();
        appendToTerminal(`Status: ${status}`, statusColors[status] || 'yellow', true, agent);
    }
}

function appendOutput(output, agent) {
    if (output) {
        output = typeof output === 'string' ? output : JSON.stringify(output);
        output = output.replace(/^\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\] /g, '');
        appendToTerminal(output, null, false, agent);
    }
}

function appendToTerminal(text, color = null, isDebug = false, targetAgent = null) {
    const line = document.createElement('div');
    line.className = 'terminal-line';
    line.innerHTML = typeof text === 'object' ? JSON.stringify(text, null, 2) : String(text);
    if (color) line.style.color = color;
    const agent = targetAgent || (activeAgentId && agents.get(activeAgentId));
    if (agent) {
        const terminal = isDebug ? agent.debugTerminal : agent.outputTerminal;
        if (terminal) {
            terminal.appendChild(line);
            terminal.scrollTop = terminal.scrollHeight;
        }
    }
}

const statusColors = {
    'completed': 'green',
    'failed': 'red',
    'cancelled': 'orange',
    'pending': 'yellow',
    'running': 'yellow'
};

async function listTasks() {
    const taskList = document.getElementById('taskList');
    taskList.innerHTML = '<p>Loading tasks...</p>';
    try {
        const response = await fetch('/list');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const { tasks = [] } = await response.json();
        if (tasks.length === 0) {
            taskList.innerHTML = '<p>No tasks found.</p>';
            return;
        }
        const taskTable = createTaskTable(tasks);
        taskList.innerHTML = '';
        taskList.appendChild(taskTable);
    } catch (error) {
        console.error('Error listing tasks:', error);
        taskList.innerHTML = `<p class="status-red">Error loading tasks: ${error.message}</p>`;
    }
}

function createTaskTable(tasks) {
    const table = document.createElement('table');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';
    table.innerHTML = `
        <tr style="background-color: #f2f2f2;">
            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Task ID</th>
            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Status</th>
            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Command</th>
            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Actions</th>
        </tr>
    `;
    tasks.forEach(task => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="padding: 8px; border: 1px solid #ddd;">${task.task_id || 'N/A'}</td>
            <td style="padding: 8px; border: 1px solid #ddd;">${task.status || 'N/A'}</td>
            <td style="padding: 8px; border: 1px solid #ddd;">${task.command || 'N/A'}</td>
            <td style="padding: 8px; border: 1px solid #ddd;">
                <button onclick="monitorTask('${task.task_id}')">Monitor</button>
            </td>
        `;
        table.appendChild(row);
    });
    return table;
}

function switchTab(tabName) {
    document.querySelectorAll('.tab-button').forEach(button => button.classList.remove('active'));
    document.querySelector(`.tab-button[onclick="switchTab('${tabName}')"]`).classList.add('active');
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

function updateAgentsGrid() {
    const grid = document.getElementById('agents-grid');
    grid.innerHTML = '';
    agents.forEach((agent, id) => {
        const card = document.createElement('div');
        card.className = `agent-card${id === activeAgentId ? ' active' : ''}`;
        card.dataset.agentId = id;
        card.onclick = () => switchToAgent(id);
        card.innerHTML = `
            <div class="agent-header">
                <div class="agent-header-left">
                    <span class="status-indicator status-${agent.status}"></span>
                    <span>${agent.name}</span>
                </div>
                <button class="agent-button" style="display: block;">Cancel</button>
            </div>
            ${agent.branch ? `<div class="branch-badge" title="${agent.branch}">${agent.branch}</div>` : ''}
            ${agent.isStarted && agent.instruction ? `<div class="agent-instruction" title="${agent.instruction}">${agent.instruction}</div>` : ''}
        `;
        cancelButton = card.querySelector('.agent-button');
        if (agent.status === 'pending' || agent.status === 'idle') {
            cancelButton.innerHTML = 'Run';
            cancelButton.onclick = async () => {
                if (!agent.instruction) return;
                await createAgent(agent.instruction, agent.branch, id);
                switchToAgent(id);
                await createNewAgent();
            };
        } else if (agent.status === 'running') {
            cancelButton.innerHTML = 'Cancel';
            cancelButton.onclick = () => cancelAgent(id);
        } else {
            cancelButton.innerHTML = 'Rerun';
            cancelButton.onclick = () => startAgent(id);
            cancelButton.onclick = async () => {
                agent.isStarted = false;
                await createAgent(agent.instruction, agent.branch, id);
                switchToAgent(id);
            };
        }

        grid.appendChild(card);
    });
}

function createTerminalForAgent(agent) {
    const terminalsContent = document.getElementById('terminals-content');
    const wrapper = document.createElement('div');
    wrapper.className = `terminal-wrapper ${agent.id === activeAgentId ? 'active' : ''}`;
    wrapper.id = `terminal-wrapper-${agent.id}`;
    wrapper.dataset.agentId = agent.id;
    wrapper.innerHTML = `
        <div class="terminal-tabs">
            <div class="terminal-tab active" onclick="switchTerminalTab('${agent.id}', 'output')">Output</div>
            <div class="terminal-tab" onclick="switchTerminalTab('${agent.id}', 'debug')">Debug Info</div>
        </div>
        <div class="terminals-container">
            <div class="terminal-content active" id="output-terminal-${agent.id}"></div>
            <div class="terminal-content" id="debug-terminal-${agent.id}"></div>
        </div>
    `;
    terminalsContent.appendChild(wrapper);
    agent.outputTerminal = document.getElementById(`output-terminal-${agent.id}`);
    agent.debugTerminal = document.getElementById(`debug-terminal-${agent.id}`);
}

function switchTerminalTab(agentId, tabType) {
    const wrapper = document.getElementById(`terminal-wrapper-${agentId}`);
    if (!wrapper) return;
    wrapper.querySelectorAll('.terminal-tab').forEach(tab => tab.classList.remove('active'));
    wrapper.querySelector(`.terminal-tab:nth-child(${tabType === 'output' ? 1 : 2})`).classList.add('active');
    wrapper.querySelectorAll('.terminal-content').forEach(terminal => terminal.classList.remove('active'));
    document.getElementById(`${tabType}-terminal-${agentId}`)?.classList.add('active');
}

function switchToAgent(id) {
    if (!agents.has(id)) return;
    activeAgentId = id;
    const agent = agents.get(id);
    document.querySelectorAll('.agent-card').forEach(card => card.classList.toggle('active', card.dataset.agentId === id));

    // Instead of hiding, disable the create agent section elements
    const createAgentSection = document.getElementById('createAgent');
    createAgentSection.style.display = 'block'; // Always show

    // Disable/enable the branch selector and instruction input based on agent state
    const branchSelector = window.mainBranchSelector?.input;
    const instructionInput = document.getElementById("instruction");

    if (branchSelector) {
        branchSelector.disabled = agent.isStarted;
    }
    if (instructionInput) {
        instructionInput.disabled = agent.isStarted;
    }
    branchSelector.value = agent.branch;
    instructionInput.value = agent.instruction;

    document.querySelectorAll('.terminal-wrapper').forEach(wrapper => wrapper.classList.toggle('active', wrapper.dataset.agentId === id));

    // Only focus the instruction if it's not disabled
    if (!agent.isStarted) {
        instructionInput.focus();
    }
}

async function cancelAgent(agentId) {
    try {
        const agent = agents.get(agentId);
        if (!agent || !agent.taskId) return;
        const response = await fetch(`/cancel/${agent.taskId}`, { method: 'POST' });
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        agent.status = 'cancelled';
        updateAgentsGrid();
        if (agent.debugTerminal) {
            agent.debugTerminal.innerHTML += '<div class="terminal-line">Agent cancelled by user</div>';
        }
    } catch (error) {
        console.error('Error cancelling agent:', error);
        const agent = agents.get(agentId);
        if (agent && agent.debugTerminal) {
            agent.debugTerminal.innerHTML += `<div class="terminal-line error">Error cancelling agent: ${error.message}</div>`;
        }
    }
}

class BranchSelector {
    constructor(container, agentId) {
        this.container = container;
        this.agentId = agentId;
        this.branches = [];
        this.selectedIndex = -1;
        this.setupUI();
        this.setupEventListeners();
    }

    setupUI() {
        this.input = document.createElement('input');
        this.input.type = 'text';
        this.input.className = 'branch-search';
        this.input.placeholder = 'Search branches...';
        this.suggestions = document.createElement('div');
        this.suggestions.className = 'branch-suggestion';
        this.hiddenInput = document.createElement('input');
        this.hiddenInput.type = 'hidden';
        this.hiddenInput.name = 'branch';
        this.hiddenInput.className = 'branch-input';
        this.container.innerHTML = '';
        this.container.appendChild(this.input);
        this.container.appendChild(this.suggestions);
        this.container.appendChild(this.hiddenInput);
    }

    setupEventListeners() {
        this.input.addEventListener('focus', () => this.showSuggestions());
        this.input.addEventListener('input', () => this.showSuggestions());
        document.addEventListener('click', (e) => {
            if (!this.container.contains(e.target)) this.hideSuggestions();
        });
        this.input.addEventListener('keydown', this.handleKeydown.bind(this));
    }

    handleKeydown(e) {
        const suggestions = this.suggestions.querySelectorAll('.branch-suggestion');
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, suggestions.length - 1);
                this.updateSelectedSuggestion();
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelectedSuggestion();
                break;
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && suggestions[this.selectedIndex]) {
                    this.selectBranch(suggestions[this.selectedIndex].textContent);
                }
                break;
            case 'Escape':
                e.preventDefault();
                this.hideSuggestions();
                break;
        }
    }

    showSuggestions() {
        const searchTerm = this.input.value.toLowerCase();
        const filteredBranches = this.branches.filter(branch => !searchTerm || branch.toLowerCase().includes(searchTerm));
        this.suggestions.innerHTML = '';
        filteredBranches.forEach(branch => {
            const div = document.createElement('div');
            div.className = 'branch-suggestion';
            div.textContent = branch;
            div.addEventListener('click', () => this.selectBranch(branch));
            div.addEventListener('mouseenter', () => {
                this.suggestions.querySelectorAll('.branch-suggestion').forEach(s => s.classList.remove('selected'));
                div.classList.add('selected');
            });
            this.suggestions.appendChild(div);
        });
        this.suggestions.style.display = filteredBranches.length > 0 ? 'block' : 'none';
    }

    hideSuggestions() {
        this.suggestions.style.display = 'none';
        this.selectedIndex = -1;
    }

    updateSelectedSuggestion() {
        const suggestions = this.suggestions.querySelectorAll('.branch-suggestion');
        suggestions.forEach((s, i) => {
            s.classList.toggle('selected', i === this.selectedIndex);
            if (i === this.selectedIndex) s.scrollIntoView({ block: 'nearest' });
        });
    }

    selectBranch(branch) {
        const cleanBranch = branch.startsWith('origin/') ? branch.substring(7) : branch;
        this.input.value = cleanBranch;
        this.hiddenInput.value = cleanBranch;
        this.hideSuggestions();
        this.hiddenInput.dispatchEvent(new Event('change'));
    }

    setBranches(branches) {
        this.branches = branches.map(branch => branch.startsWith('origin/') ? branch.substring(7) : branch);
    }

    getBranch() {
        return this.hiddenInput.value;
    }

    clear() {
        this.input.value = "";
        this.hiddenInput.value = "";
        this.suggestions.style.display = 'none';
        this.selectedIndex = -1;
    }
}

window.branchSelectors = new Map();
window.availableBranches = [];

function createBranchSelector(agentId) {
    const container = document.querySelector(`#agent_${agentId} .branch-selection`);
    if (container) {
        const selector = new BranchSelector(container, agentId);
        if (window.availableBranches.length > 0) {
            selector.setBranches(window.availableBranches);
        }
        window.branchSelectors.set(agentId, selector);
        return selector;
    }
    return null;
}

function getBranchForAgent(agentId) {
    return window.branchSelectors.get(agentId)?.getBranch() || '';
}

function initializeBranches(branches) {
    window.availableBranches = branches;
    window.branchSelectors.forEach(selector => selector.setBranches(branches));
}

function removeAgent(agentId) {
    window.branchSelectors.delete(agentId);
    document.querySelector(`#agent_${agentId}`)?.remove();
}

fetch('/git-branches')
    .then(response => response.json())
    .then(branches => initializeBranches(branches))
    .catch(error => console.error('Error fetching branches:', error));

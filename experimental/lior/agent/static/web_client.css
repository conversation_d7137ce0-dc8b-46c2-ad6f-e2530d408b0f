body {
    font-family: monospace;
    margin: 0;
    padding: 0;
    display: flex;
    height: 100vh;
    overflow: hidden;
}
/* Left side panel styles */
.agents-panel {
    width: 300px;
    height: 100vh;
    overflow-y: auto;
    background: #f5f5f5;
    border-right: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    flex-shrink: 0; /* Prevent panel from shrinking */
    margin: 20px;
    padding: 15px;
    border-radius: 5px;
}
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
}
.workspace-container {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    background: white;
    min-height: 0;
    overflow: hidden; /* Add this to contain children */
}
#createAgent {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    gap: 15px;
}
.instruction-input {
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.instruction-input textarea {
    width: 100%;
    height: 100px;
    resize: vertical;
    font-family: monospace;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}
.terminals-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 20px;
    overflow: hidden; /* Add this to contain children */
}
#agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, 250px); /* Match card width */
    gap: 16px;
    padding: 15px;
}
.agent-card {
    background: white;
    padding: 8px;
    margin: 8px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
    cursor: pointer;
    width: 250px; /* Fixed width */
    box-sizing: border-box; /* Include padding in width */
    display: flex;
    flex-direction: column;
    position: relative; /* For positioning */
    border: 2px solid transparent;  /* Maintain consistent border width */
    transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
}
.agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
}

.agent-card.selected {
    border-color: #007bff;  /* Highlight color */
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.16), 0 2px 5px rgba(0, 0, 0, 0.13);
}

.agent-card.active {
    border-color: #007bff;  /* Use same highlight color for active state */
}

.agent-card.selected:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.16), 0 2px 5px rgba(0, 0, 0, 0.13);
}
button {
    padding: 8px 16px;
    margin-right: 10px;
}
input, select, textarea {
    padding: 8px;
    margin-right: 10px;
    margin-bottom: 10px;
}
.task-list {
    margin-top: 20px;
}
.hidden {
    display: none;
}
textarea {
    width: 100%;
    height: 100px;
    font-family: monospace;
}
.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    box-sizing: border-box;
}
label {
    font-weight: bold;
}

.create-agent-btn {
    padding: 8px 15px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
#agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}
.agent-start-button {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
}
.terminals-container {
    margin: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.terminals-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
}
.terminal-wrapper {
    display: none;
    height: 100%;
    flex: 1;
    flex-direction: column;
    min-height: 0;
}
.terminal-wrapper.active {
    display: flex;
}
.terminal-content {
    display: none;
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    background: #1e1e1e;
    color: #fff;
    font-family: monospace;
    min-height: 0;
}
.terminal-content.active {
    display: block;
    height: 100%;
}
.terminal-tabs {
    display: flex;
    background: #f5f5f5;
    padding: 10px 10px 0 10px;
    border-bottom: 1px solid #ddd;
}
.terminal-tab {
    padding: 8px 16px;
    margin-right: 8px;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    background: #e0e0e0;
    cursor: pointer;
}
.terminal-tab.active {
    background: #fff;
    position: relative;
}
.terminal-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: #fff;
}
.status-active {
    background-color: #4CAF50;
}
.status-inactive {
    background-color: #9e9e9e;
}
.agent-button {
    margin-left: auto;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    color: #333;
    cursor: pointer;
    font-size: 11px;
    display: none;
}

.agent-button:hover {
    background: #f5f5f5;
}

.terminal-line.error {
    color: #dc3545;
}

.status-container {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-right: 12px;
}

.status-indicator.running {
    background-color: #28a745;
}

.status-indicator.error {
    background-color: #dc3545;
}

.status-indicator.success {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.inactive {
    background-color: #8c8c8c;
}

/* Add styles for branch selector */
.branch-selector {
    margin: 10px 0;
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    font-family: monospace;
}

.branch-search {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: monospace;
    font-size: 12px;
}

.branch-badge, .agent-instruction {
    display: block;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 11px;
    font-family: monospace;
    color: #666;
    margin: 8px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    box-sizing: border-box;
    position: static; /* Prevent any positioning effects */
}

.agent-header {
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.agent-header-left {
    display: flex;
    align-items: center;
    gap: 8px;
}


.branch-selector select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    font-family: monospace;
}
/* Branch selection styles */
.branch-selection {
    position: relative;
    width: 100%;
    z-index: 1000;
}

/* Status indicator styles */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-text {
    font-size: 12px;
    font-weight: 500;
}

/* Status indicator colors */
.status-indicator.status-pending {
    background-color: #722ed1;
}

.status-indicator.status-running {
    background-color: #1890ff;
}

.status-indicator.status-completed {
    background-color: #52c41a;
}

.status-indicator.status-failed {
    background-color: #f5222d;
}

.status-indicator.status-cancelled {
    background-color: #faad14;
}

.status-indicator.status-unknown {
    background-color: #8c8c8c;
}
.status-indicator.status-idle {
    background-color: #8c8c8c;
}

.agent-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.agent-name {
    font-weight: 500;
    margin-right: auto;
}

/* Branch selection styles */

.branch-suggestion {
    padding: 4px 4px;
    cursor: pointer;
    font-family: monospace;
    font-size: 12px;
    border-bottom: 1px solid #eee;
    max-height: 300px;
    overflow-y: auto;
    display: block;
}

.branch-suggestion:last-child {
    border-bottom: none;
}

.branch-suggestion.selected {
    background: #dfdfdf;
}

.instruction-container {
    margin-top: 4px;
}

input:disabled,
textarea:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    opacity: 0.7;
}

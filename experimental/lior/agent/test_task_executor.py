"""Tests for task executor implementations."""

import asyncio
from typing import List, Dict, Any, cast
import pytest
import subprocess
from experimental.lior.agent.task_executor import LocalTaskExecutor, DockerTaskExecutor
from experimental.lior.agent.task_types import TaskResult


def is_docker_available() -> bool:
    """Check if Dock<PERSON> is available and working."""
    try:
        # First check if Docker daemon is running
        subprocess.run(["docker", "info"], capture_output=True, check=True, timeout=5)
        return True
    except (
        subprocess.CalledProcessError,
        FileNotFoundError,
        subprocess.TimeoutExpired,
    ):
        return False


@pytest.mark.asyncio
async def test_local_executor_success() -> None:
    """Test successful command execution with LocalTaskExecutor."""
    executor = LocalTaskExecutor()

    result_dict = await executor.execute("task1", "echo hello")
    result = cast(TaskResult, result_dict)
    assert result["status"] == "completed"
    output = result["output"]
    assert output is not None and "hello" in output
    assert result["error"] is None
    assert result["returncode"] == 0


@pytest.mark.asyncio
async def test_local_executor_output() -> None:
    """Test command output capture with LocalTaskExecutor."""
    executor = LocalTaskExecutor()

    result_dict = await executor.execute("task1", "echo 'line1\nline2'")
    result = cast(TaskResult, result_dict)
    assert result["status"] == "completed"
    output = result["output"]
    assert output is not None and "line1\nline2" in output
    assert result["error"] is None
    assert result["returncode"] == 0


@pytest.mark.asyncio
async def test_local_executor_failure() -> None:
    """Test failed command execution with LocalTaskExecutor."""
    executor = LocalTaskExecutor()

    result_dict = await executor.execute("task1", "nonexistent_command")
    result = cast(TaskResult, result_dict)
    assert result["status"] == "failed"
    assert result["error"] is not None
    assert result["returncode"] == 127  # Shell's "command not found" code


@pytest.mark.asyncio
async def test_local_executor_failure_with_output() -> None:
    """Test that output is captured even when command fails."""
    executor = LocalTaskExecutor()

    result_dict = await executor.execute(
        "task1", "bash -c 'echo error message >&2; exit 1'"
    )
    result = cast(TaskResult, result_dict)
    assert result["status"] == "failed"
    assert result["error"] is not None
    output = result["output"]
    assert output is not None and "error message" in output
    assert result["returncode"] == 1


@pytest.mark.asyncio
async def test_local_executor_timeout() -> None:
    """Test command timeout with LocalTaskExecutor."""
    executor = LocalTaskExecutor()

    result_dict = await executor.execute("task1", "sleep 10", timeout=1)
    result = cast(TaskResult, result_dict)
    assert result["status"] == "failed"
    error = result["error"]
    assert error is not None and "timed out" in error
    assert result["returncode"] == 124  # timeout command's exit code


@pytest.mark.asyncio
async def test_local_executor_cancel() -> None:
    """Test cancelling a task with LocalTaskExecutor."""
    executor = LocalTaskExecutor()

    # Start a long-running task
    task = asyncio.create_task(executor.execute("task1", "sleep 10"))

    # Give it a moment to start
    await asyncio.sleep(0.1)

    # Cancel it
    await executor.cancel("task1")

    result_dict = await task
    result = cast(TaskResult, result_dict)
    assert result["status"] == "failed"
    assert result["returncode"] == -15  # SIGTERM


@pytest.mark.asyncio
async def test_local_executor_multiple_tasks() -> None:
    """Test running multiple tasks concurrently with LocalTaskExecutor."""
    executor = LocalTaskExecutor()

    # Start multiple tasks
    tasks: List[asyncio.Task[Dict[str, Any]]] = [
        asyncio.create_task(executor.execute(f"task{i}", f"echo Task {i}"))
        for i in range(3)
    ]

    # Wait for all tasks
    results_dict = await asyncio.gather(*tasks)
    results = [cast(TaskResult, r) for r in results_dict]

    # Check results
    for i, result in enumerate(results):
        assert result["status"] == "completed"
        output = result["output"]
        assert output is not None and f"Task {i}" in output
        assert result["error"] is None
        assert result["returncode"] == 0


@pytest.mark.asyncio
async def test_docker_executor_success() -> None:
    """Test successful command execution with DockerTaskExecutor."""
    if not is_docker_available():
        pytest.skip("Docker is not available")

    executor = DockerTaskExecutor()
    try:
        result_dict = await executor.execute("task1", "echo hello")
        result = cast(TaskResult, result_dict)
        assert result["status"] == "completed"
        output = result["output"]
        assert output is not None and "hello" in output
        assert result["error"] is None
        assert result["returncode"] == 0
    except Exception as e:
        pytest.skip(f"Docker test failed: {str(e)}")


@pytest.mark.asyncio
async def test_docker_executor_failure() -> None:
    """Test failed command execution with DockerTaskExecutor."""
    if not is_docker_available():
        pytest.skip("Docker is not available")

    executor = DockerTaskExecutor()
    try:
        result_dict = await executor.execute("task1", "nonexistent_command")
        result = cast(TaskResult, result_dict)
        assert result["status"] == "failed"
        assert result["error"] is not None
        assert result["returncode"] == 127  # Shell's "command not found" code
    except Exception as e:
        pytest.skip(f"Docker test failed: {str(e)}")


@pytest.mark.asyncio
async def test_docker_executor_failure_with_output() -> None:
    """Test that output is captured even when command fails in Docker."""
    if not is_docker_available():
        pytest.skip("Docker is not available")

    executor = DockerTaskExecutor()
    try:
        result_dict = await executor.execute(
            "task1", "bash -c 'echo error message >&2; exit 1'"
        )
        result = cast(TaskResult, result_dict)
        assert result["status"] == "failed"
        assert result["error"] is not None
        output = result["output"]
        assert output is not None and "error message" in output
        assert result["returncode"] == 1
    except Exception as e:
        pytest.skip(f"Docker test failed: {str(e)}")


@pytest.mark.asyncio
async def test_docker_executor_cancel() -> None:
    """Test cancelling a task with DockerTaskExecutor."""
    if not is_docker_available():
        pytest.skip("Docker is not available")

    executor = DockerTaskExecutor()
    try:
        # Start a long-running task
        task = asyncio.create_task(executor.execute("task1", "sleep 10"))

        # Give it a moment to start
        await asyncio.sleep(0.1)

        # Cancel it
        await executor.cancel("task1")

        result_dict = await task
        result = cast(TaskResult, result_dict)
        assert result["status"] == "failed"
        assert result["returncode"] == -15  # SIGTERM
    except Exception as e:
        pytest.skip(f"Docker test failed: {str(e)}")

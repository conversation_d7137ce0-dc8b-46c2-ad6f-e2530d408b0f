"""
Command Finder Module

This module handles finding and validating test commands for the setup script generator.
"""

import sys
from pathlib import Path
from typing import Tuple, Optional

from prompt_toolkit import prompt
from prompt_toolkit.completion import WordCompleter
from experimental.lior.python_interpreter_agent.builtins import truncate_middle

# Import shared utilities
from modules.console_utils import console, Confirm
from modules.process_utils import stream_process_output


class CommandFinder:
    """Finds and validates test commands for the project."""

    def __init__(self, agent, workspace_root: Path, verbose: bool = False):
        """Initialize the test command finder.

        Args:
            agent: Agent for LLM interactions
            workspace_root: Root directory of the workspace
            verbose: Whether to print verbose output
        """
        self.agent = agent
        self.workspace_root = workspace_root
        self.verbose = verbose
        self.test_command: str = ""

    def _get_suggested_command(self, instruction: Optional[str]) -> str:
        """Get a suggested test command from the agent.

        Args:
            instruction: Optional instruction for the agent

        Returns:
            The suggested test command
        """
        with console.status(
            "[bold green]Analyzing codebase to find test command...[/bold green]"
        ):
            if instruction is None:
                prompt_text = """
                Suggest a command to run unit tests for this project.

                Look for:
                1. Test frameworks used in the project (pytest, jest, etc.)
                2. Test directories or files
                3. Build systems or package managers (npm, pip, etc.)
                4. CI/CD configuration that might contain test commands

                Your response should contain the suggested test command wrapped in <agent_response></agent_response> tags.
                For example: <agent_response>pytest tests/</agent_response>
                """
                initialize_dialog = True
            else:
                instruction = instruction or "Suggest a better test command."
                prompt_text = f"{instruction}\n\nYour response should contain the suggested test command wrapped in <agent_response></agent_response> tags."
                initialize_dialog = False

            # Generate the test command using the LLM
            return self.agent.generate_content(prompt_text, initialize_dialog)

    def _execute_command(self, command: str) -> Tuple[str, int]:
        """Execute a command in a shell and capture its output.

        Args:
            command: The command to run

        Returns:
            Tuple of (output, exit_code)
            If the process is interrupted by Ctrl+C, exit_code will be -3
        """
        # Use the stream_process_output function to run the command with live output
        console.print(f"\n[bold green]Running '{command}'...[/bold green]")
        command_elipsized = f"{command[:17]}..." if len(command) > 20 else command
        # Stream the command output with a 10-minute timeout
        output, exit_code = stream_process_output(
            [command],  # Pass as a list for the shell=True case
            timeout=600,  # 10 minute timeout
            title=f"Test Command Execution ({command_elipsized})",
            shell=True,
            cwd=str(self.workspace_root),  # Specify the working directory
        )

        return output, exit_code

    def _display_command_output(self, output: str) -> None:
        """Display the command output in a panel.

        This method is kept for compatibility but doesn't display anything
        since the output is already displayed in real-time by stream_process_output.

        Args:
            output: The command output to display
        """
        # Output is already displayed in real-time, so we don't need to display it again
        pass

    def _get_user_choice(self, suggested_command: str) -> str:
        """Get the user's choice for a suggested command.

        Args:
            suggested_command: The suggested command

        Returns:
            The user's choice ('a', 'r', 'q', or other)
        """
        console.print(
            f"\nSuggested test command: [bold cyan]{suggested_command}[/bold cyan]"
        )

        # Ask for user approval
        choices = ["approve", "refine", "quit"]
        completer = WordCompleter(choices)
        choice = prompt(
            "Do you want to approve this command, refine it, or quit? [approve/refine/quit]: ",
            completer=completer,
        ).lower()

        return choice

    def _handle_approve_choice(
        self, suggested_command: str
    ) -> Tuple[bool, Optional[str]]:
        """Handle the 'approve' choice.

        Args:
            suggested_command: The suggested command

        Returns:
            Tuple of (done, instruction)
            If done is True, we're finished finding a command
            Otherwise, instruction contains feedback for the next iteration
        """
        self.test_command = suggested_command

        # Run the test command locally to verify it works
        console.print("\n[bold]Verifying test command on local machine...[/bold]")

        # Run the command and get output and success status
        output, exit_code = self._execute_command(self.test_command)

        # Check if the command was interrupted by Ctrl+C
        is_interrupted = exit_code == -3

        # Handle the output based on exit code
        if is_interrupted:
            console.print(
                "[bold yellow]Command was interrupted by Ctrl+C.[/bold yellow]"
            )
            if Confirm.ask("Do you want to try a different command?"):
                # Get feedback instruction for the next iteration
                instruction = self._get_feedback_instruction(
                    self.test_command, output, interrupted=True
                )
                return False, instruction
            else:
                # User wants to keep this command despite interruption
                return True, None

        # For non-interrupted commands, ask for confirmation
        if Confirm.ask(f"Does the test command output look right ({exit_code=})?"):
            return True, None
        else:
            console.print("[bold yellow]Let's try a different command.[/bold yellow]")

            # Command ran but user wants to try another one
            is_timeout = exit_code == -1 or "timed out" in output
            is_error = not is_timeout and not is_interrupted and exit_code != 0

            # Get feedback instruction for the next iteration
            instruction = self._get_feedback_instruction(
                self.test_command, output, error=is_error, timeout=is_timeout
            )
            return False, instruction

    def _handle_refine_choice(self) -> str:
        """Handle the 'refine' choice.

        Returns:
            The user's instruction for refining the command
        """
        return prompt(
            "Would you like to provide feedback for improving the command? [Enter to skip]\n> "
        )

    def _get_feedback_instruction(
        self,
        command: str,
        output: str,
        error: bool = False,
        timeout: bool = False,
        interrupted: bool = False,
    ) -> str:
        """Get feedback instruction from the user for improving the command."""
        # Get user feedback with simple prompt
        user_instruction = prompt(
            "What would you like to provide feedback to improve the command? [Enter to skip]\n> "
        )

        # Default instruction if empty
        if not user_instruction.strip():
            user_instruction = "Suggest a better command."

        # Always truncate output
        output = truncate_middle(output, 5000)

        # Return simplified instruction
        return f"Previous command: '{command}'\nOutput:\n{output}\n\n{user_instruction}"

    def find_test_command(self) -> str:
        """Find a suitable test command for the project.

        Returns:
            The selected test command
        """
        console.print("\n[bold]Step 1: Finding a test command[/bold]")

        instruction = None
        while True:
            # Get suggested command from the agent
            suggested_command = self._get_suggested_command(instruction)

            # Get the user's choice
            while True:
                choice = self._get_user_choice(suggested_command)

                if choice.startswith("a"):  # approve
                    done, instruction = self._handle_approve_choice(suggested_command)
                    if done:
                        console.print(
                            f"[bold green]Test command approved '{self.test_command}'![/bold green]"
                        )
                        return self.test_command
                    break  # Break the inner loop to get a new suggestion
                elif choice.startswith("r"):  # refine
                    instruction = self._handle_refine_choice()
                    break  # Break the inner loop to get a new suggestion
                elif choice.startswith("q"):  # quit
                    console.print("[bold red]Exiting...[/bold red]")
                    sys.exit(0)
                else:
                    console.print(
                        "[bold yellow]Invalid choice. Please enter 'approve', 'refine', or 'quit'.[/bold yellow]"
                    )
                    # Continue the inner loop to ask for input again

        # This line should not be reached, but included as a fallback
        return self.test_command

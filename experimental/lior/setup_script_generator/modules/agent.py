"""
Agent Module

This module handles interactions with the LLM for the setup script generator.
"""

import os
import re
from pathlib import Path
from typing import List, Tu<PERSON>, Dict

from research.agents.tools import ToolCallLogger
from experimental.guy.agent_qa.builtin_tools import (
    ReadFileTool,
    BackendCodebaseRetrievalTool,
)
from base.augment_client.client import AugmentClient
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_staging_api_proxy_url,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.lior.python_interpreter_agent.builtins import truncate_middle

from research.llm_apis.llm_client import (
    get_client,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
)
from research.agents.tools import DialogMessages, ToolCallParameters

# Import shared utilities
from modules.console_utils import console


class Agent:
    """Handles interactions with the LLM."""

    def __init__(
        self, workspace_root: Path, max_char_output: int = 10000, verbose: bool = False
    ):
        """Initialize the agent.

        Args:
            workspace_root: Root directory of the workspace
            max_char_output: Maximum character output for tool results
            verbose: Whether to print verbose output
        """
        self.temp_dir = workspace_root
        self._max_char_output = max_char_output
        self.verbose = verbose

        # Initialize tools
        self.tool_logger = ToolCallLogger()

        # Set up Augment clients
        token = Path.home().joinpath(".augment/token").read_text().strip()
        api_proxy_url = get_staging_api_proxy_url()
        self.augment_client = AugmentPrototypingClient(api_proxy_url, auth_token=token)

        # Initialize codebase tool
        augment_public_api_client = AugmentClient(api_proxy_url, token=token)

        # Create workspace manager implementation
        self.workspace_manager_impl = WorkspaceManagerImpl(
            augment_client=self.augment_client,
            root=workspace_root,
        )

        # Initialize codebase tool with workspace manager
        self.codebase_tool = BackendCodebaseRetrievalTool(
            tool_call_logger=self.tool_logger,
            client=augment_public_api_client,
            workspace_manager=self.workspace_manager_impl,
        )

        # Initialize read file tool
        self.read_file_tool = ReadFileTool(
            tool_call_logger=self.tool_logger,
            root=workspace_root,
            char_budget=max_char_output,
        )

        # Set up tools list
        self.tools = [self.codebase_tool, self.read_file_tool]

        # Create tools dictionary
        self.tools_dict = {
            self.codebase_tool.name: self.codebase_tool,
            self.read_file_tool.name: self.read_file_tool,
        }

        # Initialize LLM client
        self.client = get_client(
            "anthropic-direct", model_name="claude-3-5-sonnet-20241022", max_retries=50
        )

        # Initialize dialog for conversation history
        self.dialog = DialogMessages()
        self.system_prompt = "You are helping find a suitable test command for this project. Suggest commands that will run the tests effectively."

    def _initialize_dialog(self):
        """Initialize a new dialog history."""
        self.dialog.clear()

    def _add_user_message(self, message: str):
        """Add a user message to the dialog.

        Args:
            message: The message content
        """
        # Ensure message is not empty or just whitespace
        if not message.strip():
            message = "Please suggest a test command."

        # Check if the last message group is empty and remove it if so
        if (
            hasattr(self.dialog, "_message_lists")
            and self.dialog._message_lists
            and not self.dialog._message_lists[-1]
        ):
            self.dialog._message_lists.pop()

        # Check if it's the user's turn before adding a user message
        if not self.dialog.is_user_turn():
            # If it's not the user's turn, we need to add an empty assistant response first
            self.dialog.add_model_response([])

        # Now it should be the user's turn
        self.dialog.add_user_prompt(message)

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the LLM.

        Returns:
            The system prompt string
        """
        return f"""
        You are an expert software engineer helping with code and script generation.
        Carefully analyze the codebase to find the appropriate information.
        Think step-by-step, and use the available tools to gather information.

        Current working directory is '{self.temp_dir.absolute()}'.
        Operating system is '{os.name}'.
        Always use relative paths when referring to files, and prepend them with './'.

        Provide your final answer as a direct response.
        Do not use any tools for your final answer - just provide the content directly in the specified format.
        Always wrap your final answer in <agent_response></agent_response> tags.
        """

    def _print_dialog_history(self) -> None:
        """Print the dialog history for debugging purposes."""
        if not self.verbose:
            return

        console.print("[bold blue]Dialog history (before generation):[/bold blue]")
        for i, msg_list in enumerate(self.dialog.get_messages_for_llm_client()):
            console.print(f"[bold blue]Message group {i}:[/bold blue]")
            for msg in msg_list:
                if isinstance(msg, TextPrompt):
                    console.print(f"[cyan]User: {msg.text}[/cyan]")
                elif isinstance(msg, TextResult):
                    console.print(f"[green]Assistant: {msg.text}[/green]")
                elif isinstance(msg, ToolCall):
                    console.print(f"[yellow]Tool call: {msg.tool_name}[/yellow]")
                elif isinstance(msg, ToolFormattedResult):
                    console.print(f"[magenta]Tool result: {msg.tool_output}[/magenta]")

    def _handle_tool_call(
        self, message: ToolCall, response_messages: List, tools_dict: Dict
    ) -> List:
        """Handle a tool call from the LLM.

        Args:
            message: The tool call message
            response_messages: Current list of response messages
            tools_dict: Dictionary of tools by name

        Returns:
            Updated list of response messages (usually empty after tool call)
        """
        if self.verbose:
            console.print(f"[blue]Tool call: {message.tool_name}[/blue]")

        # Add the model response with the tool call
        response_messages.append(message)
        self.dialog.add_model_response(response_messages)

        # Determine which tool to use and execute it
        if message.tool_name in tools_dict:
            tool = tools_dict[message.tool_name]
        else:
            # Unknown tool - skip
            return response_messages

        # Execute the tool
        result = tool.run_impl(message.tool_input)

        # Add the tool result to the dialog
        self.dialog.add_tool_call_result(
            ToolCallParameters(
                tool_call_id=message.tool_call_id,
                tool_name=message.tool_name,
                tool_input=message.tool_input,
            ),
            truncate_middle(result.tool_output, self._max_char_output),
        )

        # Reset response_messages for the next turn
        return []

    def _extract_answer_from_response(self, response_messages: List) -> str:
        """Extract the answer from the response messages.

        Args:
            response_messages: List of response messages from the LLM

        Returns:
            The extracted answer string

        Raises:
            ValueError: If no answer could be extracted
        """
        # Add the final response to the dialog
        self.dialog.add_model_response(response_messages)

        # Extract the answer from the text response
        answer = ""
        for msg in response_messages:
            if isinstance(msg, TextResult):
                # Look for output tags in the response
                output_match = re.search(
                    r"<agent_response>(.*?)</agent_response>", msg.text, re.DOTALL
                )

                if output_match:
                    # Extract the content from the tags
                    answer = output_match.group(1).strip()
                    break

        # Check if we found content
        if not answer:
            if self.verbose:
                console.print(
                    "[bold red]Error: No content found in response[/bold red]"
                )
                console.print(
                    f"[bold yellow]Response text: {''.join(msg.text for msg in response_messages if isinstance(msg, TextResult))}[/bold yellow]"
                )
            # Raise an error to prevent returning empty content
            raise ValueError("No <agent_response> tags found in model response")

        if self.verbose:
            console.print(f"[bold green]Generated content: {answer}[/bold green]")

        return answer

    def _clean_empty_message_groups(self) -> None:
        """Clean up empty message groups in the dialog."""
        if (
            hasattr(self.dialog, "_message_lists")
            and self.dialog._message_lists
            and not self.dialog._message_lists[-1]
        ):
            self.dialog._message_lists.pop()

    def _setup_content_generation(
        self, prompt: str, initialize_dialog: bool, tools_dict: Dict
    ) -> Tuple[List, str]:
        """Set up the content generation process.

        Args:
            prompt: The prompt to send to the model
            initialize_dialog: Whether to reset the dialog history
            tools_dict: Dictionary of tools by name

        Returns:
            Tuple of (tools, system_prompt)
        """
        # Initialize dialog if requested
        if initialize_dialog:
            self._initialize_dialog()

        # Add the user prompt
        self.dialog.add_user_prompt(prompt)

        # Print dialog history for debugging
        self._print_dialog_history()

        # Set up tools and system prompt
        tools = [tool.get_tool_param() for tool in tools_dict.values()]
        system_prompt = self._get_system_prompt()

        return tools, system_prompt

    def _process_llm_response(self, response, tools_dict: Dict) -> List:
        """Process the response from the LLM.

        Args:
            response: The response from the LLM
            tools_dict: Dictionary of tools by name

        Returns:
            List of response messages
        """
        response_messages = []
        for message in response:
            if isinstance(message, TextResult):
                if self.verbose:
                    console.print(f"[green]{message.text}[/green]")
                response_messages.append(message)
            elif isinstance(message, ToolCall):
                # Handle tool calls
                response_messages = self._handle_tool_call(
                    message, response_messages, tools_dict
                )
                continue

        return response_messages

    def _check_for_final_answer(self, response_messages: List) -> Tuple[bool, bool]:
        """Check if we have a final answer.

        Args:
            response_messages: List of response messages

        Returns:
            Tuple of (has_text_response, has_tool_calls)
        """
        has_text_response = any(
            isinstance(msg, TextResult) for msg in response_messages
        )
        has_tool_calls = any(isinstance(msg, ToolCall) for msg in response_messages)

        return has_text_response, has_tool_calls

    def generate_content(self, prompt: str, initialize_dialog: bool = True) -> str:
        """Generate content based on the provided prompt.

        Args:
            prompt: The prompt to send to the model
            initialize_dialog: Whether to reset the dialog history (default: True)

        Returns:
            The generated content
        """
        # Set up the content generation process
        tools, system_prompt = self._setup_content_generation(
            prompt, initialize_dialog, self.tools_dict
        )

        while True:
            # Generate a response from the LLM
            response, _ = self.client.generate(
                messages=self.dialog.get_messages_for_llm_client(),
                max_tokens=8192,
                system_prompt=system_prompt,
                tools=tools,
            )

            # Process the response
            response_messages = self._process_llm_response(response, self.tools_dict)

            # Check if we have a final answer
            has_text_response, has_tool_calls = self._check_for_final_answer(
                response_messages
            )

            if has_text_response and not has_tool_calls:
                # We have a final answer - extract and return it
                return self._extract_answer_from_response(response_messages)

            # Only add non-empty response messages if we're continuing
            if response_messages:
                self.dialog.add_model_response(response_messages)

            # Clean up empty message groups
            self._clean_empty_message_groups()

        raise ValueError(
            "Failed to get content. The model did not provide a final answer."
        )

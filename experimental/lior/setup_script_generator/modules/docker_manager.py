"""
Docker Manager Module

This module handles Docker operations for the setup script generator.
"""

import os
import tempfile
import subprocess
from pathlib import Path
from typing import Tuple, Optional, List

from modules.process_utils import stream_process_output


class DockerManager:
    """Manages Docker operations for running setup scripts."""

    def __init__(self, verbose: bool = False):
        """Initialize the Docker manager.

        Args:
            verbose: Whether to print verbose output
        """
        self.verbose = verbose

    def _copy_workspace_files(
        self,
        source: Path,
        destination: Path,
        exclude_patterns: Optional[List[str]] = None,
    ) -> None:
        """Copy files from source to destination.

        Args:
            source: Source directory
            destination: Destination directory
            exclude_patterns: Patterns to exclude (default: [".git"])
        """
        if exclude_patterns is None:
            exclude_patterns = [".git"]

        if self.verbose:
            print(f"Copying files from {source} to {destination}")

        # Build rsync command with exclusions
        rsync_cmd = ["rsync", "-a"]  # archive mode (preserves permissions, etc.)

        for pattern in exclude_patterns:
            rsync_cmd.append(f"--exclude={pattern}")

        rsync_cmd.extend(
            [
                f"{source.absolute()}/",  # source with trailing slash to copy contents
                f"{destination.absolute()}/",  # destination
            ]
        )

        # Execute rsync command
        subprocess.run(rsync_cmd, check=True, capture_output=True)

        if self.verbose:
            print("File copy complete")

    def build_image(
        self,
        image_name: str = "setup-script-generator-image",
    ) -> Tuple[str, str]:
        """Build a Docker image.

        Args:
            image_name: Name for the Docker image

        Returns:
            Tuple of (image_name, error_message)
            If successful, error_message will be empty
        """
        # Define the Dockerfile content
        dockerfile_content = """FROM ubuntu:latest

# Install sudo
RUN apt-get update && apt-get install -y sudo

# Configure sudo to not require a password
RUN echo "ALL ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/nopasswd && \\
    chmod 440 /etc/sudoers.d/nopasswd

WORKDIR /workspace
"""

        # Create a temporary file for the Dockerfile
        with tempfile.NamedTemporaryFile(mode="w", suffix=".dockerfile") as dockerfile:
            # Write the Dockerfile content
            dockerfile.write(dockerfile_content)
            dockerfile.flush()

            build_cmd = [
                "docker",
                "build",
                "-t",
                image_name,
                "-f",
                dockerfile.name,
                ".",
            ]

            try:
                if self.verbose:
                    print(f"Building Docker image: {image_name}")

                subprocess.run(build_cmd, check=True, capture_output=not self.verbose)

                if self.verbose:
                    print("Docker image build complete")

                return image_name, ""
            except subprocess.CalledProcessError as e:
                error_msg = f"ERROR: Failed to build Docker image: {str(e)}\nStdout: {e.stdout}\nStderr: {e.stderr}"
                return "", error_msg
            except Exception as e:
                error_msg = f"ERROR: Failed to build Docker image: {str(e)}"
                return "", error_msg

    def run_container(
        self,
        image_name: str,
        script_content: str,
        workspace_path: Path,
        test_command: str = "true",  # Default to a no-op command
        timeout: int = 600,
    ) -> str:
        """Run a Docker container with the setup script and optional test command.

        Args:
            image_name: Name of the Docker image
            script_content: Content of the script to run
            workspace_path: Path to the workspace directory
            test_command: Optional test command to run after the setup script
            timeout: Timeout in seconds (default: 600)

        Returns:
            Output from the script execution
        """
        # Use context managers for temporary resources
        with tempfile.NamedTemporaryFile(mode="w", suffix=".sh") as temp_script_file:
            # Write script content to the temporary file
            temp_script_file.write(script_content)
            temp_script_file.flush()
            # Make the script executable
            os.chmod(temp_script_file.name, 0o755)

            # Create a temporary workspace directory
            with tempfile.TemporaryDirectory(
                prefix="setup_script_workspace_"
            ) as temp_workspace_dir:
                temp_workspace_path = Path(temp_workspace_dir)

                # Copy files from source workspace to temporary directory
                self._copy_workspace_files(workspace_path, temp_workspace_path)

                # Execute the command and capture output
                uid = os.getuid()
                gid = os.getgid()

                # Prepare the Docker command
                # Always run the setup script followed by the test command
                # If test_command is the default "true", it will be a no-op
                docker_cmd = [
                    "docker",
                    "run",
                    "--rm",
                    "-v",
                    f"{temp_script_file.name}:/setup.sh:ro",
                    "-v",
                    f"{temp_workspace_path}:/workspace:rw",
                    "-w",
                    "/workspace",
                    "--user",
                    f"{uid}:{gid}",
                    image_name,
                    "bash",
                    "-c",
                    f"source /setup.sh && {test_command}",
                ]

                # Always use streaming output for better user experience
                output, exit_code = stream_process_output(
                    docker_cmd, timeout, title="Setup Script Execution"
                )
                return output

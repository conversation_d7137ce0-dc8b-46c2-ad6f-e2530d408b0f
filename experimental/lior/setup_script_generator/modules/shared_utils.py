"""
Shared Utilities Module

This module provides shared utilities for the setup script generator.
"""

from rich.console import Console
from prompt_toolkit import prompt

# Initialize console (single instance to be used across modules)
console = Console()


class UserInteraction:
    """Utilities for user interaction."""

    @staticmethod
    def confirm(question):
        """Ask a yes/no question and return the answer.

        Args:
            question: The question to ask

        Returns:
            True if the answer is yes, False otherwise
        """
        response = prompt(f"{question} [y/n] ").lower().strip()
        return response.startswith("y")

"""
Script Generator Module

This module handles generating and refining setup scripts for the setup script generator.
"""

import sys
from typing import Optional

from rich.syntax import Syntax
from prompt_toolkit import prompt
from prompt_toolkit.completion import WordCompleter
from experimental.lior.python_interpreter_agent.builtins import truncate_middle

# Import shared utilities
from modules.console_utils import console


class ScriptGenerator:
    """Generates and refines setup scripts."""

    def __init__(self, agent, docker_manager, verbose: bool = False):
        """Initialize the script generator.

        Args:
            agent: Agent for LLM interactions
            docker_manager: Docker manager
            verbose: Whether to print verbose output
        """
        self.agent = agent
        self.docker_manager = docker_manager
        self.verbose = verbose
        self.setup_script: str = ""

    def _generate_script_iteration(
        self, test_command: str, feedback: Optional[str], iteration: int
    ) -> str:
        """Generate a setup script iteration.

        Args:
            test_command: The test command to use
            feedback: Optional feedback from previous iterations
            iteration: Current iteration number

        Returns:
            The generated setup script
        """
        with console.status(
            f"[bold green]Generating setup script (iteration {iteration})...[/bold green]"
        ):
            # Prepare the prompt for the LLM
            if feedback:
                prompt = f"{feedback}\n\nYour answer should be wrapped in <agent_response></agent_response> tags."
            else:
                prompt = f"""
                Please generate a setup.sh script that will install all dependencies needed to run this test command: `{test_command}`

                Your setup script should:
                1. Install all necessary system packages using apt-get
                2. Install all language-specific dependencies (pip, npm, etc.)
                3. Set up any required environment variables
                4. Create any necessary directories or files
                5. The script is run from the root of the repository
                6. The script is run as a non-root user, so use sudo when needed
                7. IMPORTANT: DO NOT include the test command in the script - it will be run separately after setup as follows: `source setup.sh && {test_command}`

                Make sure to handle errors properly in the script with set -e and proper error messages.
                The script should focus ONLY on setting up the environment, not running tests.

                Your answer should be wrapped in <agent_response></agent_response> tags.
                For example:
                <agent_response>
                sudo apt-get update
                sudo apt-get install -y python3-pip
                ...</agent_response>
                """

            # Generate the setup script using the LLM
            # Initialize dialog only for the first iteration (when feedback is None)
            initialize_dialog = feedback is None
            return self.agent.generate_content(
                prompt, initialize_dialog=initialize_dialog
            )

    def _get_script_feedback(self, output: str) -> str:
        """Get feedback from the user for improving the script.

        Args:
            output: The output from running the script

        Returns:
            Formatted feedback string for the agent
        """
        user_feedback = prompt(
            "Would you like to provide feedback for improving the script? [Enter to skip]\n> "
        )
        if not user_feedback.strip():
            user_feedback = "Suggest a better script."

        truncated_output = truncate_middle(output, 5000)
        return f"Previous script execution output:\n\n{truncated_output}\n\nUser feedback: {user_feedback}"

    def generate_setup_script(self, test_command: str) -> str:
        """Generate and refine the setup script.

        Args:
            test_command: The test command to use

        Returns:
            The generated setup script
        """
        console.print("\n[bold]Step 2: Generating setup script[/bold]")

        # Reset the agent's dialog history before starting script generation
        self.agent._initialize_dialog()

        feedback = None
        iteration = 1

        while True:
            # Generate the setup script
            self.setup_script = self._generate_script_iteration(
                test_command, feedback, iteration
            )

            # Display the generated script
            console.print("\nGenerated setup script:")
            console.print(
                Syntax(self.setup_script, "bash", theme="monokai", line_numbers=True)
            )

            # Build the Docker image
            image_name, error = self.docker_manager.build_image()
            if error:
                console.print(
                    f"[bold red]Error building Docker image: {error}[/bold red]"
                )
                return self.setup_script

            # Get the workspace root from the agent
            workspace_root = self.agent.temp_dir

            # Add spacing
            console.print("\n")

            output = self.docker_manager.run_container(
                image_name=image_name,
                script_content=self.setup_script,
                workspace_path=workspace_root,
                test_command=test_command,
            )

            # No need to display the output again as it's already streamed

            # Ask for user approval
            choices = ["approve", "refine", "quit"]
            completer = WordCompleter(choices)

            while True:
                choice = prompt(
                    "Do you want to approve this script, refine it, or quit? [approve/refine/quit]: ",
                    completer=completer,
                ).lower()

                if choice.startswith("a"):  # approve
                    return self.setup_script  # Return the approved script
                elif choice.startswith("r"):  # refine
                    feedback = self._get_script_feedback(output)
                    iteration += 1
                    break  # Break the inner loop to generate a new script
                elif choice.startswith("q"):  # quit
                    console.print("[bold red]Exiting...[/bold red]")
                    sys.exit(0)
                else:
                    console.print(
                        "[bold yellow]Invalid choice. Please enter 'approve', 'refine', or 'quit'.[/bold yellow]"
                    )
                    # Continue the inner loop to ask for input again

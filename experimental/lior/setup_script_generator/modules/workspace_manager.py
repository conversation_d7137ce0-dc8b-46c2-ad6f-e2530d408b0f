"""
Workspace Manager Module

This module handles workspace setup and management for the setup script generator.
"""

import tempfile
import subprocess
from pathlib import Path
from typing import Optional, List


class WorkspaceManager:
    """Manages workspace setup and file operations."""

    def __init__(self, workspace_root: Optional[Path] = None, verbose: bool = False):
        """Initialize the workspace manager.

        Args:
            workspace_root: Root directory of the workspace
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.workspace_root = workspace_root or Path.cwd()

    def setup_workspace(self, prefix: str = "setup_script_workspace_") -> Path:
        """Set up a temporary workspace directory.

        Args:
            prefix: Prefix for the temporary directory name

        Returns:
            Path to the temporary workspace directory
        """
        temp_dir = Path(tempfile.mkdtemp(prefix=prefix))

        if self.verbose:
            print(f"Created temporary workspace directory: {temp_dir}")

        return temp_dir

    def copy_files(
        self,
        source: Path,
        destination: Path,
        exclude_patterns: Optional[List[str]] = None,
    ) -> None:
        """Copy files from source to destination.

        Args:
            source: Source directory
            destination: Destination directory
            exclude_patterns: Patterns to exclude (default: [".git"])
        """
        if exclude_patterns is None:
            exclude_patterns = [".git"]

        if self.verbose:
            print(f"Copying files from {source} to {destination}")

        # Build rsync command with exclusions
        rsync_cmd = ["rsync", "-a"]  # archive mode (preserves permissions, etc.)

        for pattern in exclude_patterns:
            rsync_cmd.append(f"--exclude={pattern}")

        rsync_cmd.extend(
            [
                f"{source.absolute()}/",  # source with trailing slash to copy contents
                f"{destination.absolute()}/",  # destination
            ]
        )

        # Execute rsync command
        subprocess.run(rsync_cmd, check=True, capture_output=True)

        if self.verbose:
            print("File copy complete")

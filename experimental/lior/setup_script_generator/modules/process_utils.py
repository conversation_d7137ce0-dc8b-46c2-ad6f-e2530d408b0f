"""
Process Utilities Module

This module provides utilities for running processes with streaming output.
"""

import subprocess
import time
import select
import signal
from typing import List, Tuple, Optional, Callable

from modules.console_utils import create_live_output_display


def start_process(
    cmd: List[str], shell: bool = False, cwd: Optional[str] = None
) -> Tuple[Optional[subprocess.Popen], List[str]]:
    """Start a subprocess with the given command.

    Args:
        cmd: Command to run
        shell: Whether to run the command in a shell
        cwd: Working directory for the command

    Returns:
        Tuple of (process, initial_output_lines)
        If process is None, initial_output_lines will contain an error message
    """
    output_lines = []

    try:
        if shell:
            # If shell is True, cmd should be a string
            cmd_str = (
                cmd[0] if isinstance(cmd, list) and len(cmd) == 1 else " ".join(cmd)
            )
            process = subprocess.Popen(
                cmd_str,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout
                text=True,
                bufsize=1,  # Line buffered
                universal_newlines=True,
                shell=True,
                cwd=cwd,
            )
        else:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout
                text=True,
                bufsize=1,  # Line buffered
                universal_newlines=True,
                cwd=cwd,
            )
        return process, output_lines
    except Exception as e:
        error_msg = f"ERROR: Failed to start process: {str(e)}"
        output_lines.append(error_msg)
        return None, output_lines


def read_process_output(
    process: subprocess.Popen, output_lines: List[str], update_content: Callable
) -> float:
    """Read available output from the process.

    Args:
        process: Process to read from
        output_lines: List to append output lines to
        update_content: Function to update the display

    Returns:
        Timestamp of when output was last read (for timeout calculation)
    """
    if process.stdout is None:
        output_lines.append("ERROR: Failed to capture process output")
        update_content(output_lines)
        return time.time()

    try:
        # Use select to check if there's data to read (non-blocking)
        readable, _, _ = select.select([process.stdout], [], [], 0.1)

        if readable:
            # There's data to read
            line = process.stdout.readline()
            if line:  # Only process non-empty lines
                output_lines.append(line.rstrip())
                update_content(output_lines)
                return time.time()  # Reset timeout when we get output
    except (IOError, OSError) as e:
        # Handle case where the pipe is broken or closed during read
        output_lines.append(f"WARNING: Error reading process output: {str(e)}")
        update_content(output_lines)
        return time.time()

    return 0  # No output was read, don't reset timeout


def handle_process_timeout(
    process: subprocess.Popen,
    timeout: int,
    output_lines: List[str],
    update_content: Callable,
):
    """Handle process timeout by terminating the process.

    Args:
        process: Process to terminate
        timeout: Timeout in seconds
        output_lines: List to append error message to
        update_content: Function to update the display
    """
    process.terminate()
    time.sleep(0.5)
    if process.poll() is None:
        process.kill()

    output_lines.append(f"ERROR: Process execution timed out after {timeout} seconds.")
    update_content(output_lines)


def read_remaining_output(
    process: subprocess.Popen, output_lines: List[str], update_content: Callable
):
    """Read any remaining output from the process after it has completed.

    Args:
        process: Process to read from
        output_lines: List to append output lines to
        update_content: Function to update the display
    """
    if process.stdout is None:
        return

    try:
        # Try to read any remaining output
        while True:
            try:
                readable, _, _ = select.select([process.stdout], [], [], 0.1)
                if not readable:
                    break

                line = process.stdout.readline()
                if not line:  # End of output
                    break

                output_lines.append(line.rstrip())
                update_content(output_lines)
            except (IOError, OSError):
                # Handle case where the pipe is broken or closed during read
                break
    except Exception as e:
        # Catch any other exceptions to ensure we don't crash
        output_lines.append(f"WARNING: Error reading remaining output: {str(e)}")
        update_content(output_lines)


def handle_process_interrupt(
    process: subprocess.Popen,
    output_lines: List[str],
    update_content: Callable,
):
    """Handle process interruption (Ctrl+C) by terminating the process.

    Args:
        process: Process to terminate
        output_lines: List to append error message to
        update_content: Function to update the display
    """
    # First read any remaining output before terminating
    read_remaining_output(process, output_lines, update_content)

    # Now terminate the process
    process.terminate()
    time.sleep(0.5)
    if process.poll() is None:
        process.kill()

    output_lines.append("Process was interrupted by user (Ctrl+C).")
    update_content(output_lines)


def stream_process_output(
    cmd: List[str],
    timeout: int = 600,
    title: str = "Process Execution",
    shell: bool = False,
    cwd: Optional[str] = None,
) -> Tuple[str, int]:
    """Stream process output in a fixed-height container.

    Args:
        cmd: Command to run
        timeout: Timeout in seconds (default: 600)
        title: Title for the output panel (default: "Process Execution")
        shell: Whether to run the command in a shell (default: False)
        cwd: Working directory for the command (default: None)

    Returns:
        Tuple of (complete_output, exit_code)
        If the process times out, exit_code will be -1
        If the process fails to start, exit_code will be -2
        If the process is interrupted by Ctrl+C, exit_code will be -3
    """
    # Create a live display for the output
    live, update_content = create_live_output_display(title)

    # Start the process
    process, output_lines = start_process(cmd, shell, cwd)

    # If process failed to start, return the error message and a special exit code
    if process is None:
        return "\n".join(output_lines), -2

    # Initialize exit_code to a default value
    exit_code = 0
    timed_out = False
    interrupted = False

    # Set up signal handler for Ctrl+C
    original_sigint_handler = signal.getsignal(signal.SIGINT)

    def sigint_handler(sig, frame):
        nonlocal interrupted, exit_code
        interrupted = True
        exit_code = -3  # Special exit code for interruption
        # Don't call the original handler yet - we'll restore it after cleanup

    # Install our custom handler
    signal.signal(signal.SIGINT, sigint_handler)

    try:
        # Start the live display and process the output
        with live:
            start_time = time.time()

            # Process is running, read output until it completes, times out, or is interrupted
            while (
                process.poll() is None and not interrupted
            ):  # Process is still running
                # Check for timeout
                if time.time() - start_time > timeout:
                    handle_process_timeout(
                        process, timeout, output_lines, update_content
                    )
                    timed_out = True
                    exit_code = -1  # Special exit code for timeout
                    break

                # Read available output
                new_output_time = read_process_output(
                    process, output_lines, update_content
                )
                if new_output_time > 0:
                    start_time = new_output_time  # Reset timeout when we get output

            # Handle interruption if it occurred
            if interrupted:
                handle_process_interrupt(process, output_lines, update_content)
            # If the process completed normally (not timed out or interrupted), get its exit code
            elif not timed_out:
                exit_code = process.poll() or 0  # Use 0 if poll() returns None
                # Process any remaining output after normal completion
                read_remaining_output(process, output_lines, update_content)

            # Check the return code and add a message if non-zero
            if exit_code > 0 and not timed_out and not interrupted:
                output_lines.append(f"Process exited with code {exit_code}")
                update_content(output_lines)
    finally:
        # Restore the original signal handler
        signal.signal(signal.SIGINT, original_sigint_handler)

    # Return the complete output and exit code
    return "\n".join(output_lines), exit_code

#!/usr/bin/env python3
"""
Tests for setup_script_generator_modular.py

This file contains unit tests for the modular setup script generator components.
"""

import os
import tempfile
import subprocess
import time
from pathlib import Path
import pytest

from setup_script_generator import (
    SetupScriptGenerator,
)
from experimental.lior.python_interpreter_agent.builtins import truncate_middle
from modules.docker_manager import DockerManager


class TestDockerManagerRunEnv:
    """Tests for the DockerManager run_container method."""

    def test_run_env_success(self):
        """Test successful execution of a setup script in Docker."""
        # Create a simple test script that should succeed
        test_script = """#!/bin/bash
echo "Hello, world!"
exit 0
"""
        docker_manager = DockerManager(verbose=False)

        # Create temporary directory for workspace
        with tempfile.TemporaryDirectory(prefix="setup_script_workspace_") as _:
            # Build the Docker image
            image_name, error = docker_manager.build_image()
            assert not error, f"Docker image build failed: {error}"

            # Run the Docker container
            output = docker_manager.run_container(
                image_name=image_name,
                script_content=test_script,
                workspace_path=Path(_),
            )

            # Check that the output contains the expected text
            assert "Hello, world!" in output

    def test_run_env_failure(self):
        """Test execution of a failing setup script in Docker."""
        # Create a test script that should fail
        test_script = """#!/bin/bash
echo "This script will fail"
exit 1
"""
        docker_manager = DockerManager(verbose=False)

        # Create temporary directory for workspace
        with tempfile.TemporaryDirectory(prefix="setup_script_workspace_") as _:
            # Build the Docker image
            image_name, error = docker_manager.build_image()
            assert not error, f"Docker image build failed: {error}"

            # Run the Docker container
            output = docker_manager.run_container(
                image_name=image_name,
                script_content=test_script,
                workspace_path=Path(_),
            )

            # Check that the output contains the expected text
            assert "This script will fail" in output

    def test_run_env_timeout(self):
        """Test timeout handling in run_env with a short timeout."""
        # Create a test script that should timeout
        test_script = """#!/bin/bash
echo "Starting infinite loop"
while true; do
    sleep 1
done
"""
        docker_manager = DockerManager(verbose=False)

        # Create temporary directory for workspace
        with tempfile.TemporaryDirectory(prefix="setup_script_workspace_") as _:
            # Build the Docker image
            image_name, error = docker_manager.build_image()
            assert not error, f"Docker image build failed: {error}"

            # Run the Docker container with a short timeout
            output = docker_manager.run_container(
                image_name=image_name,
                script_content=test_script,
                workspace_path=Path(_),
                timeout=2,
            )

            # Check that the output indicates a timeout
            assert "ERROR: Process execution timed out after 2 seconds." in output

    def test_run_env_workspace_mapping(self):
        """Test that files are correctly mapped from local machine to /workspace in Docker."""
        # Create a temporary directory with some test files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create a unique marker file
            marker_filename = f"marker_{time.time()}.txt"
            marker_content = f"test_content_{time.time()}"
            (temp_path / marker_filename).write_text(marker_content)

            # Create a test directory structure
            test_dir = temp_path / "test_dir"
            test_dir.mkdir()
            (test_dir / "test_file.txt").write_text("test file content")

            # Create a script that checks the current working directory and lists files in /workspace
            test_script = f"""#!/bin/bash
# Check current working directory
echo "Current working directory: $(pwd)"

# Verify that the current directory is /workspace
if [ "$(pwd)" != "/workspace" ]; then
    echo "ERROR: Current directory is not /workspace"
    exit 1
fi

# Check that we can access files relative to the current directory
echo "Listing files in current directory:"
ls -la .

# Check that we can access the marker file using a relative path
if [ -f "./{marker_filename}" ]; then
    echo "Marker file found with relative path"
    echo "Content: $(cat ./{marker_filename})"
else
    echo "ERROR: Marker file not found with relative path"
    exit 1
fi

echo "Listing files in /workspace:"
ls -la /workspace
echo "\nListing files in /workspace/test_dir:"
ls -la /workspace/test_dir

if [ -f "/workspace/{marker_filename}" ]; then
    echo "\nMarker file found with absolute path"
    echo "Content: $(cat /workspace/{marker_filename})"
else
    echo "\nERROR: Marker file not found with absolute path"
    exit 1
fi

if [ -d "/workspace/test_dir" ]; then
    echo "\nTest directory found"
else
    echo "\nERROR: Test directory not found"
    exit 1
fi

if [ -f "/workspace/test_dir/test_file.txt" ]; then
    echo "Test file found"
    echo "Content: $(cat /workspace/test_dir/test_file.txt)"
else
    echo "ERROR: Test file not found"
    exit 1
fi
"""
            docker_manager = DockerManager(verbose=False)

            # Build the Docker image
            image_name, error = docker_manager.build_image()
            assert not error, f"Docker image build failed: {error}"

            # Run the Docker container
            output = docker_manager.run_container(
                image_name=image_name,
                script_content=test_script,
                workspace_path=temp_path,
            )

            # Check that the output contains evidence of correct mapping and working directory
            assert (
                "Current working directory: /workspace" in output
            ), "Working directory is not /workspace"
            assert "Listing files in current directory:" in output
            assert (
                "Marker file found with relative path" in output
            ), "Could not access marker file with relative path"
            assert "Listing files in /workspace:" in output
            assert "Listing files in /workspace/test_dir:" in output
            assert "Marker file found with absolute path" in output
            assert f"Content: {marker_content}" in output
            assert "Test directory found" in output
            assert "Test file found" in output
            assert "Content: test file content" in output

    def test_workspace_is_writable(self):
        """Test that the /workspace directory in Docker is writable."""
        # Create a temporary directory with some test files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create a test file to verify it exists
            test_file = "initial_file.txt"
            (temp_path / test_file).write_text("initial content")

            # Create a script that attempts to write files in the workspace
            test_script = f"""#!/bin/bash
# Try to create a new file
echo "Creating new file in workspace..."
echo "new file content" > /workspace/new_file.txt

# Check if the file was created
if [ -f "/workspace/new_file.txt" ]; then
    echo "Successfully created new file"
    echo "Content: $(cat /workspace/new_file.txt)"
else
    echo "ERROR: Failed to create new file"
    exit 1
fi

# Try to modify an existing file
echo "Modifying existing file..."
echo "modified content" > /workspace/{test_file}

# Check if the file was modified
if grep -q "modified content" "/workspace/{test_file}"; then
    echo "Successfully modified existing file"
    echo "New content: $(cat /workspace/{test_file})"
else
    echo "ERROR: Failed to modify existing file"
    exit 1
fi

# Create a new directory
echo "Creating new directory..."
mkdir -p /workspace/new_directory

# Check if the directory was created
if [ -d "/workspace/new_directory" ]; then
    echo "Successfully created new directory"
else
    echo "ERROR: Failed to create new directory"
    exit 1
fi

# Create a file in the new directory
echo "Creating file in new directory..."
echo "nested file content" > /workspace/new_directory/nested_file.txt

# Check if the nested file was created
if [ -f "/workspace/new_directory/nested_file.txt" ]; then
    echo "Successfully created nested file"
    echo "Content: $(cat /workspace/new_directory/nested_file.txt)"
else
    echo "ERROR: Failed to create nested file"
    exit 1
fi

echo "All write operations successful"
"""
            docker_manager = DockerManager(verbose=False)

            # Build the Docker image
            image_name, error = docker_manager.build_image()
            assert not error, f"Docker image build failed: {error}"

            # Run the Docker container
            output = docker_manager.run_container(
                image_name=image_name,
                script_content=test_script,
                workspace_path=temp_path,
            )

            # Check that the output indicates successful write operations
            assert "Creating new file in workspace..." in output
            assert "Successfully created new file" in output
            assert "Content: new file content" in output
            assert "Modifying existing file..." in output
            assert "Successfully modified existing file" in output
            assert "New content: modified content" in output
            assert "Creating new directory..." in output
            assert "Successfully created new directory" in output
            assert "Creating file in new directory..." in output
            assert "Successfully created nested file" in output
            assert "Content: nested file content" in output
            assert "All write operations successful" in output

            # Verify that the original file on the local machine was NOT modified
            assert (
                temp_path / test_file
            ).read_text() == "initial content", "Local file was modified"

            # Verify that the new files were NOT created on the local machine
            assert not (
                temp_path / "new_file.txt"
            ).exists(), "New file was created on local machine"
            assert not (
                temp_path / "new_directory"
            ).exists(), "New directory was created on local machine"

    def test_workspace_changes_isolation(self):
        """Test that changes to files in Docker don't affect the original directory."""
        # Create a temporary directory with some test files
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create a set of test files with known content
            files_to_test = {
                "file1.txt": "original content 1",
                "file2.txt": "original content 2",
                "file3.txt": "original content 3",
            }

            # Create the files
            for filename, content in files_to_test.items():
                (temp_path / filename).write_text(content)

            # Create a subdirectory with a file
            subdir = temp_path / "subdir"
            subdir.mkdir()
            (subdir / "subfile.txt").write_text("original subfile content")

            # Create a script that makes various changes to the workspace
            test_script = """#!/bin/bash
# Modify existing files
echo "modified content 1" > /workspace/file1.txt
echo "appended line" >> /workspace/file2.txt
rm /workspace/file3.txt

# Create new files
echo "new file content" > /workspace/new_file.txt

# Modify subdirectory
echo "modified subfile" > /workspace/subdir/subfile.txt
echo "new subfile" > /workspace/subdir/new_subfile.txt

# Create new subdirectory
mkdir -p /workspace/new_subdir
echo "nested new content" > /workspace/new_subdir/nested.txt

echo "All modifications completed"
"""
            docker_manager = DockerManager(verbose=False)

            # Build the Docker image
            image_name, error = docker_manager.build_image()
            assert not error, f"Docker image build failed: {error}"

            # Run the Docker container
            output = docker_manager.run_container(
                image_name=image_name,
                script_content=test_script,
                workspace_path=temp_path,
            )

            # Verify the script completed
            assert "All modifications completed" in output

            # Verify that none of the files on the local machine were modified
            for filename, original_content in files_to_test.items():
                file_path = temp_path / filename
                if filename == "file3.txt":
                    # This file was deleted in Docker, should still exist locally
                    assert (
                        file_path.exists()
                    ), f"File {filename} was deleted on local machine"
                    assert (
                        file_path.read_text() == original_content
                    ), f"File {filename} was modified on local machine"
                else:
                    # These files were modified in Docker, should be unchanged locally
                    assert (
                        file_path.read_text() == original_content
                    ), f"File {filename} was modified on local machine"

            # Verify that new files were not created on the local machine
            assert not (
                temp_path / "new_file.txt"
            ).exists(), "New file was created on local machine"
            assert not (
                temp_path / "new_subdir"
            ).exists(), "New directory was created on local machine"

            # Verify that the subdirectory file was not modified
            assert (
                (subdir / "subfile.txt").read_text() == "original subfile content"
            ), "Subfile was modified on local machine"
            assert not (
                subdir / "new_subfile.txt"
            ).exists(), "New subfile was created on local machine"


class TestTruncateMiddle:
    """Tests for the truncate_middle utility function."""

    def test_truncate_middle_short_string(self):
        """Test truncate_middle with a string shorter than max_length."""
        text = "Short string"
        max_length = 20
        result = truncate_middle(text, max_length)
        assert result == text

    def test_truncate_middle_long_string(self):
        """Test truncate_middle with a string longer than max_length."""
        text = "This is a very long string that should be truncated in the middle"
        max_length = 20
        result = truncate_middle(text, max_length)
        # The actual implementation adds a message about truncation which makes the result longer than max_length
        assert "output truncated to 20 characters" in result
        assert result.startswith("This")
        assert result.endswith("middle")
        assert "..." in result


class TestScriptGenerator:
    """Tests for script generation functionality."""

    def test_write_script_to_file(self):
        """Test writing a script to a file."""
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            output_path = temp_path / "test_setup.sh"

            # Create a test script
            test_script = """#!/bin/bash
echo "Test script"
"""
            # Write the script to file
            with open(output_path, "w") as f:
                f.write(test_script)

            # Make the script executable
            os.chmod(output_path, 0o755)

            # Check that the file exists and has the correct content
            assert output_path.exists()
            assert output_path.read_text() == test_script

            # Check that the file is executable
            assert os.access(output_path, os.X_OK)


class TestDockerAvailability:
    """Tests for Docker availability."""

    def test_docker_available(self):
        """Test that Docker is available on the system."""
        try:
            result = subprocess.run(
                ["docker", "--version"], capture_output=True, text=True, timeout=5
            )
            assert result.returncode == 0
            assert "Docker version" in result.stdout
        except (subprocess.SubprocessError, FileNotFoundError):
            pytest.skip("Docker is not available on this system")


class TestSetupScriptGeneratorComponents:
    """Tests for SetupScriptGenerator components."""

    def test_initialization(self):
        """Test initialization of SetupScriptGenerator."""
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock the token file
            token_dir = Path(temp_dir) / ".augment"
            token_dir.mkdir()
            token_file = token_dir / "token"
            token_file.write_text("test_token")

            # Set HOME environment variable to our temp directory
            old_home = os.environ.get("HOME")
            try:
                os.environ["HOME"] = str(temp_dir)

                # Create the generator with a real workspace path
                workspace_root = Path(temp_dir)
                # Use the SetupScriptGenerator for this test
                generator = SetupScriptGenerator(
                    workspace_root=workspace_root, verbose=True
                )

                # Verify initialization
                assert generator.verbose is True
                assert generator.workspace_root == workspace_root
                assert generator.test_command is None
                assert generator.setup_script is None
                assert generator.agent is not None
                assert generator.docker_manager is not None
                assert generator.command_finder is not None
                assert generator.script_generator is not None
            finally:
                # Restore HOME environment variable
                if old_home:
                    os.environ["HOME"] = old_home


class TestDockerIsolation:
    """Tests for Docker isolation functionality."""

    def test_project_files_in_container(self):
        """Test that project files are available in the Docker container and cleaned up after."""
        # Skip if Docker is not available
        try:
            result = subprocess.run(
                ["docker", "--version"], capture_output=True, text=True, timeout=5
            )
            if result.returncode != 0:
                pytest.skip("Docker is not available on this system")
        except (subprocess.SubprocessError, FileNotFoundError):
            pytest.skip("Docker is not available on this system")

        # Create a temporary project directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create a simple project structure
            (temp_path / "src").mkdir()
            (temp_path / "src" / "main.py").write_text("print('Hello, world!')")

            (temp_path / "tests").mkdir()
            (temp_path / "tests" / "test_main.py").write_text("""
def test_example():
    assert True
""")

            # Create a special marker file to verify in the container
            marker_content = f"test_marker_{time.time()}"
            (temp_path / "marker.txt").write_text(marker_content)

            # Create a test script that verifies files exist in the container
            test_script = f"""#!/bin/bash
set -e
echo "Checking project files in container..."
if [ -f "/workspace/src/main.py" ]; then
    echo "Found main.py"
else
    echo "ERROR: main.py not found"
    exit 1
fi

if [ -f "/workspace/tests/test_main.py" ]; then
    echo "Found test_main.py"
else
    echo "ERROR: test_main.py not found"
    exit 1
fi

if [ -f "/workspace/marker.txt" ]; then
    echo "Found marker.txt"
    CONTENT=$(cat /workspace/marker.txt)
    echo "Marker content: $CONTENT"
    if [ "$CONTENT" != "{marker_content}" ]; then
        echo "ERROR: Marker content doesn't match"
        exit 1
    fi
else
    echo "ERROR: marker.txt not found"
    exit 1
fi

echo "All project files verified successfully"
"""
            docker_manager = DockerManager(verbose=False)

            # Build the Docker image
            image_name, error = docker_manager.build_image()
            assert not error, f"Docker image build failed: {error}"

            # Run the Docker container
            output = docker_manager.run_container(
                image_name=image_name,
                script_content=test_script,
                workspace_path=temp_path,
            )

            # Check that the output contains the expected messages
            assert "Found main.py" in output
            assert "Found test_main.py" in output
            assert "Found marker.txt" in output
            assert f"Marker content: {marker_content}" in output
            assert "All project files verified successfully" in output


if __name__ == "__main__":
    # Run the tests in this file
    pytest.main(["-v", __file__])

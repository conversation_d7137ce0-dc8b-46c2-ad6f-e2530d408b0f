#!/usr/bin/env python3
"""
Setup Script Generator

This tool generates a setup script for a project based on its test command.
It uses an LLM to analyze the codebase and suggest a test command, then generates
a setup script that installs all dependencies needed to run the tests.
"""

import os
import sys
import argparse
import tempfile
import subprocess
import contextlib
from contextlib import nullcontext
from pathlib import Path
from typing import Optional, Generator

from modules.docker_manager import DockerManager
from modules.agent import Agent
from modules.command_finder import CommandFinder
from modules.script_generator import ScriptGenerator
from modules.console_utils import (
    console,
    print_welcome_message,
    print_file_saved_message,
    display_script,
    get_script_path,
)

# Constants
MAX_CHAR_OUTPUT = 100000


@contextlib.contextmanager
def demo_environment() -> Generator[Path, None, None]:
    """Context manager that sets up a demo environment by cloning a sample Python project.

    The demo environment is a temporary directory with a cloned repository.
    The directory is automatically cleaned up when the context manager exits,
    without prompting the user.

    Yields:
        Path to the temporary directory containing the demo project
    """
    console.print(
        "[bold yellow]Running in demo mode with a sample Python project[/bold yellow]"
    )
    console.print("[bold]Setting up demo environment...[/bold]")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Clone the demo repository
        repo_url = (
            "https://github.com/ericsalesdeandrade/pytest-github-actions-example.git"
        )
        subprocess.run(
            ["git", "clone", repo_url, temp_dir],
            check=True,
            capture_output=True,
            text=True,
        )
        console.print(
            f"[bold green]Successfully cloned demo repository: {repo_url}[/bold green]"
        )
        yield Path(temp_dir)


class SetupScriptGenerator:
    """Generates setup scripts for projects."""

    def __init__(self, workspace_root: Optional[Path] = None, verbose: bool = False):
        """Initialize the setup script generator.

        Args:
            workspace_root: Root directory of the workspace
            verbose: Whether to print verbose output
        """
        self.verbose = verbose

        # Set workspace root
        if workspace_root:
            self.workspace_root = workspace_root
        else:
            self.workspace_root = Path.cwd()

        # Initialize agent
        self.agent = Agent(
            workspace_root=self.workspace_root,
            max_char_output=MAX_CHAR_OUTPUT,
            verbose=verbose,
        )

        # Initialize Docker manager
        self.docker_manager = DockerManager(verbose)

        # Initialize command finder
        self.command_finder = CommandFinder(
            agent=self.agent, workspace_root=self.workspace_root, verbose=verbose
        )

        # Initialize script generator
        self.script_generator = ScriptGenerator(
            agent=self.agent, docker_manager=self.docker_manager, verbose=verbose
        )

        # Initialize state
        self.test_command = None
        self.setup_script = None

    def execute_script_in_docker(self, script_path: Path) -> int:
        """Execute an existing script in a Docker container.

        Args:
            script_path: Path to the script file to execute
            test_command: Test command to run after the setup script

        Returns:
            Exit code (0 for success, non-zero for failure)
        """
        if not script_path.exists():
            console.print(
                f"[bold red]Error: Script file not found: {script_path}[/bold red]"
            )
            return 1

        # Read the script content
        script_content = script_path.read_text()

        # Build Docker image
        console.print(f"[bold]Building Docker image to run {script_path}...[/bold]")
        image_name, error = self.docker_manager.build_image()
        if error:
            console.print(f"[bold red]Error building Docker image: {error}[/bold red]")
            return 1

        # Run the script in Docker with streaming output
        console.print("[bold green]Running script in Docker container...[/bold green]")
        self.docker_manager.run_container(
            image_name=image_name,
            script_content=script_content,
            workspace_path=self.workspace_root,
        )

        return 0

    def generate_setup_script(self) -> int:
        """Generate a setup script for the project.

        Returns:
            Exit code (0 for success, non-zero for failure)
        """
        # Print welcome message
        print_welcome_message()

        # Find a test command
        self.test_command = self.command_finder.find_test_command()

        # Generate a setup script
        self.setup_script = self.script_generator.generate_setup_script(
            self.test_command
        )

        # Save the setup script
        script_path = get_script_path()
        with open(script_path, "w") as f:
            f.write(self.setup_script)

        # Make the script executable
        os.chmod(script_path, 0o755)

        # Display the final script
        display_script(self.setup_script, script_path)

        # Print success message
        print_file_saved_message(script_path)

        return 0

    def run(self, run_script_path=None) -> int:
        """Run the setup script generator.

        Args:
            run_script_path: Path to an existing script to run in Docker (optional)

        Returns:
            Exit code (0 for success, non-zero for failure)
        """
        if run_script_path:
            return self.execute_script_in_docker(run_script_path)
        else:
            return self.generate_setup_script()


def main():
    """Main entry point for the setup script generator."""
    parser = argparse.ArgumentParser(
        description="Generate a setup script for a project."
    )
    parser.add_argument(
        "--workspace",
        "-w",
        type=Path,
        help="Path to the workspace directory (default: current directory)",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Print verbose output"
    )
    parser.add_argument(
        "--run-script",
        "-r",
        type=Path,
        help="Run an existing setup script in Docker instead of generating one",
    )
    parser.add_argument(
        "--demo",
        "-d",
        action="store_true",
        help="Use a demo Python project instead of the current directory",
    )

    args = parser.parse_args()

    # Convert workspace path to Path object if provided
    workspace_path = (
        args.workspace if args.workspace and args.workspace.exists() else None
    )

    # Choose the appropriate context manager based on the demo flag
    # If demo is True, use demo_environment(), otherwise use nullcontext
    ctx_manager = demo_environment() if args.demo else nullcontext(workspace_path)

    # Use the context manager to get the workspace path
    with ctx_manager as workspace_root:
        # Create and run the setup script generator
        generator = SetupScriptGenerator(
            workspace_root=workspace_root, verbose=args.verbose
        )
        return generator.run(run_script_path=args.run_script)


if __name__ == "__main__":
    sys.exit(main())

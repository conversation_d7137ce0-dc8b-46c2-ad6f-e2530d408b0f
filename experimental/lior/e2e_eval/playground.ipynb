{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from eval import ActEvaluator\n", "from collections import defaultdict\n", "import json\n", "import os\n", "\n", "act_path: str = os.path.expanduser(\"~/git/act/dist/local/act\")\n", "\n", "repos = [\n", "    {\n", "        \"sha\": \"83c6fcfac017fa4545cdaad5df58b60f38031755\",\n", "        \"fix_sha\": \"104834e985471c6791b6c50a4b15f8380d0c68e7\",\n", "        \"repo_id\": \"likec4/likec4\",\n", "        \"run_name\": \"check-packages\",\n", "    },\n", "    {\n", "        \"sha\": \"1ac396647d2c675378cfe20c03d1b62b3fb3b3d6\",\n", "        \"fix_sha\": \"a7edc54d4f1421671136c6945fca964a3344c196\",\n", "        \"repo_id\": \"bcgov/des-notifybc\",\n", "        \"run_name\": \"install-build-lint-and-test\",\n", "    },\n", "    {\n", "        \"sha\": \"a59a9d027a6e46c637b26898994411dd8443e705\",\n", "        \"fix_sha\": \"d8073131e0044a0e49deba9f3692c821fc531ca2\",\n", "        \"repo_id\": \"demokratie-live/democracy-development\",\n", "        \"run_name\": \"build-and-push / build-and-push (bundestag.io-admin)\",\n", "    },\n", "    {\n", "        \"sha\": \"4b0723a2f77cbb385b38dcd5e453c39ebf7a54e7\",\n", "        \"fix_sha\": \"ece918f7aff412ea68a85cdb20b79518ec0d8834\",\n", "        \"repo_id\": \"knative-extensions/eventing-natss\",\n", "        \"run_name\": \"style / Golang / Lint\",\n", "    },\n", "    {\n", "        \"sha\": \"2f5a84e008808abdd714b1dbe3d358550d587bd3\",\n", "        \"fix_sha\": \"4445d7bb52f39056c1710125d973648fbec148f1\",\n", "        \"repo_id\": \"manytask/manytask\",\n", "        \"run_name\": \"lint-python\",\n", "    },\n", "    {\n", "        \"sha\": \"4e2661972096bd2b12209b606465243c8e2f33e3\",\n", "        \"fix_sha\": \"94583a6d7ba034321a6001bd23358208ca2c350f\",\n", "        \"repo_id\": \"IBM/oper8\",\n", "        \"run_name\": \"build (3.10, py310)\",\n", "    },\n", "    {\n", "        \"sha\": \"70603074e3ee576dcd93ed45193cd155214b52b2\",\n", "        \"fix_sha\": \"3278f63991e208e2bc885555c1962f2530f87a82\",\n", "        \"repo_id\": \"TrustedComputingGroup/pandoc\",\n", "        \"run_name\": \"render-samples / Render\",\n", "    },\n", "    {\n", "        \"sha\": \"cca8b80fe1d45c253c23086b4514606f9b8de80d\",\n", "        \"fix_sha\": \"4f5256455294304b37cae4b96d3a97e13c50700d\",\n", "        \"repo_id\": \"COS301-SE-2024/MovieHub\",\n", "        \"run_name\": \"build\",\n", "    },\n", "    {\n", "        \"sha\": \"79300eea6427d14eb77bce4e92a5a1c577273199\",\n", "        \"fix_sha\": \"046cc1ad0d99628fa7902b62aed281e0270a26ca\",\n", "        \"repo_id\": \"aquanauts/alogamous\",\n", "        \"run_name\": \"Python 3.11 on Linux\",\n", "    },\n", "    {\n", "        \"sha\": \"b960396348672215322a973de8042ab00bb4498a\",\n", "        \"fix_sha\": \"eac4a6d3c8e4c813dedd4b63188aa8c8bb745f9b\",\n", "        \"repo_id\": \"netbox-community/netbox-chart\",\n", "        \"run_name\": \"Test / Run chart-testing (lint-and-install)\",\n", "    },\n", "    {\n", "        \"sha\": \"eafeef8173fbc75fb3641b8416433497452c839d\",\n", "        \"fix_sha\": \"c68bfab11c880a059b3c6c1effe3f1e6f3f4c735\",\n", "        \"repo_id\": \"Myriad-Dreamin/typst.ts\",\n", "        \"run_name\": \"ci\",\n", "    },\n", "    {\n", "        \"sha\": \"516557be737bf0df56d57c17d4b7642be28e7b3e\",\n", "        \"fix_sha\": \"53f2f684f922dad79bb547295899f12e08b480fe\",\n", "        \"repo_id\": \"apolloconfig/apollo\",\n", "        \"run_name\": \"CLAssistant\",\n", "    },\n", "    {\n", "        \"sha\": \"a2a9ff7ef3c66ccfe8e8027469b32e0e752f82b3\",\n", "        \"fix_sha\": \"605a7ef3c742b0813a7f34cc1f58b81e59f6afb9\",\n", "        \"repo_id\": \"acidjunk/shop-backend\",\n", "        \"run_name\": \"Unit tests (3.11)\",\n", "    },\n", "    {\n", "        \"sha\": \"2c66e14f0eadcf3a67aca87176a4720b3a90cb7b\",\n", "        \"fix_sha\": \"867914e6d65af3308449d097adb35a4578a2c23b\",\n", "        \"repo_id\": \"stryker-mutator/mutation-testing-elements\",\n", "        \"run_name\": \"build_and_test (18.x, ubuntu-latest)\",\n", "    },\n", "    {\n", "        \"sha\": \"0dc87abfb7a8842c5a7db282eca175ff69cdbe03\",\n", "        \"fix_sha\": \"bf0975fb19a894e9b6738eb1ad33b09e2f6a023e\",\n", "        \"repo_id\": \"pulumi/pulumi-kubernetes-operator\",\n", "        \"run_name\": \"Integration Testing\",\n", "    },\n", "    {\n", "        \"sha\": \"1265dfeda7fe723ec7b5da497852111376ad033b\",\n", "        \"fix_sha\": \"ee4a4dac324f1bb51fb8bcbee2fe45c00dc50379\",\n", "        \"repo_id\": \"Tencent/tdesign-react\",\n", "        \"run_name\": \"call-test-build / test\",\n", "    },\n", "    {\n", "        \"sha\": \"67d795e36f765d362c53e9dc0f160a62101fd1b3\",\n", "        \"fix_sha\": \"335e62d69a26d93b83cb644b12e0293c1435d0c0\",\n", "        \"repo_id\": \"ChristienGuy/bird\",\n", "        \"run_name\": \"<PERSON><PERSON>\",\n", "    },\n", "    {\n", "        \"sha\": \"019b6ede16b7df7a7beacbe549946497bea8908c\",\n", "        \"fix_sha\": \"e2d578e676f91e66419c6483c24e32235b3a395c\",\n", "        \"repo_id\": \"DiamondLightSource/dodal\",\n", "        \"run_name\": \"lint / run\",\n", "    },\n", "    {\n", "        \"sha\": \"50f32c8c7e2a4f3dc41e02cb56549dd6c995fcad\",\n", "        \"fix_sha\": \"3149adc450d02d1869a29f2c2a0c6b7d0f72edda\",\n", "        \"repo_id\": \"NHSDigital/connecting-party-manager\",\n", "        \"run_name\": \"terraform-base-build\",\n", "    },\n", "    {\n", "        \"sha\": \"0e3bc031d096e2a3d49644c274173c9528a8fe66\",\n", "        \"fix_sha\": \"88df9939648ee7d333112e64339a3af1fbca782d\",\n", "        \"repo_id\": \"nanofuzz/nanofuzz\",\n", "        \"run_name\": \"buildandtest\",\n", "    },\n", "    {\n", "        \"sha\": \"b8f3a3a0a4896136a3aba56136520c4c409d58c2\",\n", "        \"fix_sha\": \"7608a03e234211e31584a78d785b4c0ae5aa6aee\",\n", "        \"repo_id\": \"lambdaclass/cairo-vm\",\n", "        \"run_name\": \"changelog\",\n", "    },\n", "    {\n", "        \"sha\": \"e33d250f0145501975cf78647024329ccc785086\",\n", "        \"fix_sha\": \"57951755b5875f0461468767adec5ca91b797264\",\n", "        \"repo_id\": \"L-OCAT/Server\",\n", "        \"run_name\": \"locat-CI\",\n", "    },\n", "    {\n", "        \"sha\": \"04912c9243b3c29fa5c6d95fa632f5003f11783b\",\n", "        \"fix_sha\": \"19ab4be1b0bbe726b16045a799cf31a55e2cbf31\",\n", "        \"repo_id\": \"factoriolab/factoriolab\",\n", "        \"run_name\": \"tests\",\n", "    },\n", "    {\n", "        \"sha\": \"60f7eb67a072cad30fe2d5ebe0b127afecc5a87c\",\n", "        \"fix_sha\": \"416cabb95eb393512e0f722e8ec7491a73c920d6\",\n", "        \"repo_id\": \"Dugout-Developers/CatchMate-Android\",\n", "        \"run_name\": \"ktlint\",\n", "    },\n", "    {\n", "        \"sha\": \"1d59552d1c1b2d4b3cbcd7e6a01c0b4a3b839286\",\n", "        \"fix_sha\": \"719db05bd89cd7c37a20a16177945109757e40fa\",\n", "        \"repo_id\": \"DataBiosphere/terra-scientific-pipelines-service\",\n", "        \"run_name\": \"run-e2e-test-job / run-e2e-test-job\",\n", "    },\n", "    {\n", "        \"sha\": \"555a7ab88d5dfb84e3ac902e47186ca8897f2302\",\n", "        \"fix_sha\": \"ac9b9dcd044b3e3ba418d92423069d4130d55530\",\n", "        \"repo_id\": \"SJSU-CS-systems-group/DDD\",\n", "        \"run_name\": \"compile\",\n", "    },\n", "    {\n", "        \"sha\": \"b8e77aae8057a3ef90ec6ebe9ec4a9939a6d4bb2\",\n", "        \"fix_sha\": \"40bb75ea77c24b320f9d5898727945da4b3adee3\",\n", "        \"repo_id\": \"deNBI/cloud-portal-webapp\",\n", "        \"run_name\": \"build-test\",\n", "    },\n", "    {\n", "        \"sha\": \"d56f13a671fe7253788d7129e925704701abf004\",\n", "        \"fix_sha\": \"27523c8989cbd692408760cce7d548c3db38bdad\",\n", "        \"repo_id\": \"plone/volto\",\n", "        \"run_name\": \"Core Blocks - Listing (20.x)\",\n", "    },\n", "    {\n", "        \"sha\": \"7a0f4ac25b264f19cd111b9517d33ad17b4e6a47\",\n", "        \"fix_sha\": \"590fd3ad453b56b040d27ad092176e409f894b7a\",\n", "        \"repo_id\": \"InseeFrLab/onyxia-api\",\n", "        \"run_name\": \"build\",\n", "    },\n", "    {\n", "        \"sha\": \"e158844e82160447a4439c48e9cae18eb3e3c22f\",\n", "        \"fix_sha\": \"d0d628abee9dcffbffefa5f4077486910ad451f8\",\n", "        \"repo_id\": \"NVIDIA/mig-parted\",\n", "        \"run_name\": \"check\",\n", "    },\n", "    {\n", "        \"sha\": \"fdf015daccc43a636b15e5a5586d011efd1d7eaa\",\n", "        \"fix_sha\": \"4783ccb505b85f86372a726cc25eb3fa836286be\",\n", "        \"repo_id\": \"cesarParra/apexdocs\",\n", "        \"run_name\": \"build\",\n", "    },\n", "    {\n", "        \"sha\": \"ee903b78dcbb0e08dab4cc80646cfa5705f1811d\",\n", "        \"fix_sha\": \"5dd3acbf4827adf288e35144ec83b86603ac09c0\",\n", "        \"repo_id\": \"CSCI-GA-2820-SU24-001/recommendations\",\n", "        \"run_name\": \"build\",\n", "    },\n", "    {\n", "        \"sha\": \"61bfaf8fe11eba06846678d12cdf146bfd5720f4\",\n", "        \"fix_sha\": \"50d6dddf2a2d5ecbddbc7f9b4f4d1f7e2c2ac83a\",\n", "        \"repo_id\": \"dnd-side-project/dnd-10th-5-frontend\",\n", "        \"run_name\": \"check-quality (build)\",\n", "    },\n", "    {\n", "        \"sha\": \"a33efd125226ddad40a9932d039a10f6d8bfaff1\",\n", "        \"fix_sha\": \"4debb1b26976676ffed25addee2e393a92254f71\",\n", "        \"repo_id\": \"dsv-rp/DDS\",\n", "        \"run_name\": \"visual-regression-test (wc, development)\",\n", "    },\n", "    {\n", "        \"sha\": \"b87571e0411815c806cfacbe43b6e998b10ee82c\",\n", "        \"fix_sha\": \"506424e6d3810c260b8b77c99d7b58cfc4e5f1de\",\n", "        \"repo_id\": \"Scrump31/scottiecrump.com\",\n", "        \"run_name\": \"e2e\",\n", "    },\n", "    {\n", "        \"sha\": \"87bb1ac699b81b225e9f3f16c62b3b4193049391\",\n", "        \"fix_sha\": \"f60759591eb5b602482d639aa43471d3193a9984\",\n", "        \"repo_id\": \"editor-js/utils\",\n", "        \"run_name\": \"Build editorjs/utils\",\n", "    },\n", "    {\n", "        \"sha\": \"7c85ba1577cb094740de8453c64c09370fa25d6c\",\n", "        \"fix_sha\": \"1ab56bfc5896527d2d9a8cff8484d2a25fbbd90c\",\n", "        \"repo_id\": \"Chia-Network/chia_rs\",\n", "        \"run_name\": \"Check chia_rs.pyi\",\n", "    },\n", "    {\n", "        \"sha\": \"72eedfb58e7db1c1444564e99b10c64c6fa81a5a\",\n", "        \"fix_sha\": \"c6f677ba53001102a97b6bf2be33b8eb8cb832e3\",\n", "        \"repo_id\": \"onecx/onecx-announcement-ui\",\n", "        \"run_name\": \"pr / angular / npm build and test\",\n", "    },\n", "    {\n", "        \"sha\": \"86b5edff84bc4e940bff13dc86618df83d517e9e\",\n", "        \"fix_sha\": \"28b88f8c4cced69c1a6d1fccb72a19adba657da6\",\n", "        \"repo_id\": \"bramstroker/homeassistant-powercalc\",\n", "        \"run_name\": \"my<PERSON>\",\n", "    },\n", "    {\n", "        \"sha\": \"578ca27fa64932c188ac394e4736f31254bae64c\",\n", "        \"fix_sha\": \"671672978b8766c20805de9a1d3d452978bf4d38\",\n", "        \"repo_id\": \"Team-Ampersand/Dotori-server-V2\",\n", "        \"run_name\": \"build\",\n", "    },\n", "    {\n", "        \"sha\": \"48f5e32e2522be641da31f91a2b88a0689c42961\",\n", "        \"fix_sha\": \"6692ff36300f724607c9791e625328ae639a35a0\",\n", "        \"repo_id\": \"otuskotlin/202405-ok-marketplace\",\n", "        \"run_name\": \"build\",\n", "    },\n", "    {\n", "        \"sha\": \"180ff644b7096ba477839663c96c5ca7311b2e11\",\n", "        \"fix_sha\": \"3f354bae53bc8245791cf9f186e458a600d9f26f\",\n", "        \"repo_id\": \"ykakarap/cluster-api\",\n", "        \"run_name\": \"lint (hack/tools)\",\n", "    },\n", "    {\n", "        \"sha\": \"8c38852c95f888b4f8f0071f5b87663c86d1fa11\",\n", "        \"fix_sha\": \"f27f8206c9d5579c87fad372b5aa1a22122d1ba3\",\n", "        \"repo_id\": \"chanzuckerberg/single-cell-data-portal\",\n", "        \"run_name\": \"rdev-tests / e2e-logged-in-test\",\n", "    },\n", "    {\n", "        \"sha\": \"3a66f03611580e26394561642c56ff1542313b86\",\n", "        \"fix_sha\": \"aa0c909c9e6adb50409fc153edb2232eec1dc8ad\",\n", "        \"repo_id\": \"MIERUNE/plateau-gis-converter\",\n", "        \"run_name\": \"test\",\n", "    },\n", "    {\n", "        \"sha\": \"da84a7f216f084aa77fe89e06a715b537b7ca23a\",\n", "        \"fix_sha\": \"22c606ea9fb5a6ad0557b5340a07fd900a0ff7f9\",\n", "        \"repo_id\": \"astriaorg/astria\",\n", "        \"run_name\": \"rust\",\n", "    },\n", "    {\n", "        \"sha\": \"1761b43fe59c7ad0f10047e4291cbd3a74cce422\",\n", "        \"fix_sha\": \"f7fe0037de61834004c82222b830394ed6b5f0fe\",\n", "        \"repo_id\": \"Iterable/iterable-web-sdk\",\n", "        \"run_name\": \"build\",\n", "    },\n", "    {\n", "        \"sha\": \"0836e7e64be136c8be425b2f5e064ff27250de90\",\n", "        \"fix_sha\": \"f87fa02b958934be55fac5241d284509fae192c1\",\n", "        \"repo_id\": \"scaleway/ultraviolet\",\n", "        \"run_name\": \"format\",\n", "    },\n", "    {\n", "        \"sha\": \"9c9371328b34054c936cd7edb18da292fc5406e6\",\n", "        \"fix_sha\": \"c561045a7d9997e379f5447bcea4213ec37da654\",\n", "        \"repo_id\": \"ergebnis/phpunit-slow-test-detector\",\n", "        \"run_name\": \"Tests (7.5.0, 7.4, highest)\",\n", "    },\n", "    {\n", "        \"sha\": \"439bcbc0d721025d37aaf37f9474a3124ef6bdb2\",\n", "        \"fix_sha\": \"18853cf3477d6a23d605547de3fd4fa38cc015b4\",\n", "        \"repo_id\": \"NonlinearOscillations/HarmonicBalance.jl\",\n", "        \"run_name\": \"Julia 1.10 - ubuntu-latest - x64\",\n", "    },\n", "    {\n", "        \"sha\": \"a2d7c67c5237241761356182eed0a160e8c0ee36\",\n", "        \"fix_sha\": \"bd2c87b6641d7f5b4dceb7babbb056f4589a9dda\",\n", "        \"repo_id\": \"navikt/aksel\",\n", "        \"run_name\": \"playwright\",\n", "    },\n", "]\n", "\n", "stats = defaultdict(int)\n", "\n", "act_eval = ActEvaluator(act_path)\n", "for i, x in enumerate(repos):\n", "    patch_url = (\n", "        f\"https://github.com/{x['repo_id']}/compare/{x['sha']}...{x['fix_sha']}.patch\"\n", "    )\n", "    fix_patch = requests.get(patch_url).text\n", "    outcome = act_eval.test_repo(x[\"repo_id\"], x[\"sha\"], fix_patch, x[\"run_name\"])\n", "\n", "    stats[str(outcome)] += 1\n", "    print(f\"Stats: {json.dumps(stats, indent=2)}\")\n", "    print(f\"[{i+1} / {len(repos)}] {x['repo_id']} {x['run_name']} Outcome: {outcome}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}
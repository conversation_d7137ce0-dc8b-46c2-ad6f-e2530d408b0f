from pathlib import Path
import tempfile
import subprocess

from base.diff_utils.changes import Added, Deleted, Modified
from base.diff_utils.diff_utils import File
from research.autofix.autofix_eval_static import (
    generate_fix_suggestion,
    repo_change_to_docs,
)
from research.autofix.autofix_client import AutofixClient
from research.autofix.autofix_manager import AutofixManager
from research.autofix.retrieval import (
    AutofixPredictEditLocationsInput,
    AutofixContextRetrieveInput,
    AutofixContextOutput,
)
from research.core.diff_utils import Repository
from research.utils.repo_change_utils import (
    FileTuple,
    PatchSet,
    RepoChange,
    patchset_from_repo_change,
    pyr_map,
)


def run_command(command: str, cwd: str) -> tuple[str, int]:
    result = subprocess.run(
        command,
        shell=True,
        cwd=cwd,
        stderr=subprocess.STDOUT,
        stdout=subprocess.PIPE,
        check=False,
    )
    return result.stdout.decode("utf-8"), result.returncode


def get_merge_base(cwd: str) -> str:
    result = run_command("git merge-base HEAD origin/main", cwd)[0]
    return result.strip()


def calc_breaking_diff(merge_base: str, cwd: str) -> str:
    return run_command(f"git diff {merge_base} --no-prefix", cwd)[0]


def get_repository_by_commit(cwd: str, commit: str) -> Repository:
    with tempfile.TemporaryDirectory() as tmpdir:
        subprocess.run(["git", "clone", f"file://{cwd}", "."], check=True, cwd=tmpdir)
        subprocess.run(["git", "reset", "--hard"], check=True, cwd=tmpdir)
        subprocess.run(["git", "reset", "--hard", commit], check=True, cwd=tmpdir)
        return Repository.load(Path(tmpdir))


def calc_repo_change(
    breaking_diff: str,
    before: Repository,
    after: Repository,
) -> RepoChange:
    repo_files = pyr_map({Path(file.path): file.contents for file in before.files})
    after_files = pyr_map({Path(file.path): file.contents for file in after.files})

    patchset = PatchSet(breaking_diff)
    # break_change = repo_change_from_repositories(before, after)
    changed_files = []
    for file in patchset:
        if file.is_rename:
            changed_files.append(
                Deleted[FileTuple](
                    before=FileTuple(
                        path=Path(file.path), code=repo_files[Path(file.source_file)]
                    ),
                )
            )
            changed_files.append(
                Added[FileTuple](
                    after=FileTuple(
                        path=Path(file.path), code=after_files[Path(file.target_file)]
                    ),
                )
            )
        elif file.is_modified_file:
            changed_files.append(
                Modified[FileTuple](
                    before=FileTuple(
                        path=Path(file.path), code=repo_files[Path(file.path)]
                    ),
                    after=FileTuple(
                        path=Path(file.path), code=after_files[Path(file.path)]
                    ),
                )
            )
        elif file.is_added_file:
            changed_files.append(
                Added[FileTuple](
                    after=FileTuple(
                        path=Path(file.path), code=after_files[Path(file.path)]
                    ),
                )
            )
        elif file.is_removed_file:
            changed_files.append(
                Deleted[FileTuple](
                    before=FileTuple(
                        path=Path(file.path), code=repo_files[Path(file.path)]
                    ),
                )
            )
    return RepoChange.from_before_and_changes(repo_files, changed_files)


def apply_suggestion(suggested_change: RepoChange, cwd: str):
    for file in suggested_change.changed_files:
        if isinstance(file, Modified):
            abs_path = Path(cwd) / file.after.path
            with open(abs_path, "w") as f:
                f.write(file.after.code)


async def run_local(command: str, cwd: str, client: AutofixManager):
    print(f"Running {command} in {cwd}")
    command_output, returncode = run_command(command, cwd)
    print(command_output)
    if returncode == 0:
        print("Command succeeded, no need to fix")
        return

    print("Calculating breaking change")
    merge_base = get_merge_base(cwd)
    breaking_diff = calc_breaking_diff(merge_base, cwd)
    before = get_repository_by_commit(cwd, merge_base)
    after = Repository.load(Path(cwd))
    breaking_change = calc_repo_change(breaking_diff, before, after)

    docs = repo_change_to_docs(breaking_change)
    repo_doc_ids = [doc.id for doc in docs]
    recent_changes = [
        Modified(
            File(path=str(file.before.path), contents=file.before.code),
            File(path=str(file.after.path), contents=file.after.code),
        )
        for file in breaking_change.changed_files
        if isinstance(file, Modified)
    ]

    print("predicting edit locations")
    edit_locations, _ = await client.predict_edit_locations(
        AutofixPredictEditLocationsInput(
            command_output=command_output,
            recent_changes=recent_changes,
            doc_ids=repo_doc_ids,
            new_docs=docs,
            top_k=256,
        )
    )
    print("retrieving chunks for root cause")
    retrival_output: AutofixContextOutput = await client.context_retrieve(
        AutofixContextRetrieveInput(
            command_output=command_output,
            doc_ids=repo_doc_ids,
            new_docs=docs,
        )
    )

    print("generating fix suggestion")
    suggested_change, artifacts = await generate_fix_suggestion(
        client,
        edit_locations,
        retrival_output.retrieved_chunks,
        breaking_change,
        command_output,
        command,
    )
    suggested_diff = str(
        patchset_from_repo_change(suggested_change, num_context_lines=3)
    )
    print(suggested_diff)

    print("applying suggestion")
    apply_suggestion(suggested_change, cwd)

    print(f"Running {command} in {cwd} after suggestion")
    command_output_after, returncode_after = run_command(command, cwd)
    print(command_output_after)

    return {
        "cwd": cwd,
        "command": command,
        "command_output": command_output,
        "returncode": returncode,
        "command_output_after": command_output_after,
        "returncode_after": returncode_after,
        "merge_base": merge_base,
        "breaking_diff": breaking_diff,
        "breaking_change": breaking_change,
        "suggested_change": suggested_change,
        "suggested_diff": suggested_diff,
        "edit_locations": edit_locations,
        "retrived_chunks": retrival_output.retrieved_chunks,
        "retrived_query": retrival_output.generated_query,
        **artifacts,
    }

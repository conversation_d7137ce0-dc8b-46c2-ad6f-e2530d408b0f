from dataclasses import dataclass
from enum import Enum
import subprocess
import tempfile


class Outcome(Enum):
    JOB_ID_NOT_FOUND = 0
    GREEN_FAILED = 1
    GREEN_TIMEOUT = 2
    RED_TIMEOUT = 3
    RED_PASSED = 4
    SUCCESS = 5


class ResultType(Enum):
    SUCCESS = 0
    FAILURE = 1
    TIMEOUT = 2
    JOB_ID_NOT_FOUND = 3


@dataclass
class ExecutionResult:
    stdout: str
    stderr: str
    returncode: int
    result_type: ResultType


class ActEvaluator:
    def __init__(self, act_path: str):
        self.act_path = act_path

    def run_without_checkout(
        self,
        code_directory: str,
        checkrun_name: str,
        timeout: int = 30,
        pipe_to_stdout: bool = False,
    ) -> ExecutionResult:
        try:
            job_id = self._run_name_2_job_id(code_directory, checkrun_name)
            if job_id is None:
                return ExecutionResult(
                    stdout="",
                    stderr="",
                    returncode=1,
                    result_type=ResultType.JOB_ID_NOT_FOUND,
                )
            output = subprocess.run(
                [
                    self.act_path,
                    "push",
                    "--container-architecture",
                    "linux/amd64",
                    "-j",
                    job_id,
                    "--rm",
                ],
                check=True,
                cwd=code_directory,
                timeout=timeout,
                capture_output=not pipe_to_stdout,
                text=True,
            )
            return ExecutionResult(
                stdout=output.stdout,
                stderr=output.stderr,
                returncode=output.returncode,
                result_type=ResultType.SUCCESS,
            )
        except subprocess.TimeoutExpired:
            return ExecutionResult(
                stdout="", stderr="", returncode=1, result_type=ResultType.TIMEOUT
            )
        except subprocess.CalledProcessError as e:
            return ExecutionResult(
                stdout=e.stdout,
                stderr=e.stderr,
                returncode=e.returncode,
                result_type=ResultType.FAILURE,
            )

    def _run_name_2_job_id(self, cwd: str, checkrun_name: str) -> str | None:
        act_output = subprocess.run(
            [
                self.act_path,
                "-l",
                "--container-architecture",
                "linux/amd64",
            ],
            check=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=5,
        ).stdout

        # Parse the output of act -l, line by line
        for line in act_output.splitlines():
            cols = line.split()
            if len(cols) < 3:
                continue
            if checkrun_name.startswith(cols[2]):
                return cols[1]
        return None

    def _checkout(self, cwd: str, repo_name: str, commit_hash: str, patch: str | None):
        subprocess.run(["git", "init", cwd], check=True, cwd=cwd)
        url = f"https://github.com/{repo_name}.git"
        subprocess.run(["git", "remote", "add", "origin", url], check=True, cwd=cwd)
        subprocess.run(
            ["git", "fetch", "--quiet", "--depth", "1", "origin", commit_hash],
            check=True,
            cwd=cwd,
        )
        subprocess.run(
            ["git", "-c", "advice.detachedHead=false", "checkout", "FETCH_HEAD"],
            check=True,
            cwd=cwd,
        )
        subprocess.run(["git", "checkout", "-b", "test"], check=True, cwd=cwd)
        if patch is not None:
            with tempfile.NamedTemporaryFile(mode="w") as f:
                f.write(patch)
                f.flush()
                subprocess.run(["git", "apply", f.name], check=True, cwd=cwd)

    def run(
        self,
        repo_name: str,
        commit_hash: str,
        patch: str | None,
        checkrun_name: str,
        pipe_to_stdout: bool = False,
        timeout: int = 30,
    ) -> ExecutionResult:
        with tempfile.TemporaryDirectory() as cwd:
            self._checkout(cwd, repo_name, commit_hash, patch)
            return self.run_without_checkout(
                cwd, checkrun_name, timeout, pipe_to_stdout
            )

    def test_repo(
        self,
        repo_name: str,
        sha: str,
        patch: str,
        checkrun_name: str,
        pipe_to_stdout: bool = False,
        timeout: int = 30,
    ) -> Outcome:
        assert patch is not None, "Fix patch must be provided"

        result = self.run(repo_name, sha, patch, checkrun_name, pipe_to_stdout, timeout)
        if result.result_type == ResultType.TIMEOUT:
            return Outcome.GREEN_TIMEOUT
        elif result.result_type == ResultType.JOB_ID_NOT_FOUND:
            return Outcome.JOB_ID_NOT_FOUND
        elif result.result_type == ResultType.FAILURE:
            return Outcome.GREEN_FAILED

        result = self.run(repo_name, sha, None, checkrun_name, pipe_to_stdout, timeout)
        if result.result_type == ResultType.TIMEOUT:
            return Outcome.RED_TIMEOUT
        elif result.result_type == ResultType.SUCCESS:
            return Outcome.RED_PASSED

        return Outcome.SUCCESS

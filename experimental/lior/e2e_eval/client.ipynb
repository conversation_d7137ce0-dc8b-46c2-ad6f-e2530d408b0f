{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm.asyncio import tqdm_asyncio\n", "from research.autofix.filter_files import neural_fix_file_selection\n", "from research.autofix.autofix_eval_static import (\n", "    load_data,\n", "    convert_problems_to_df,\n", "    filter_data,\n", "    DEFAULT_PATH,\n", ")\n", "import pickle\n", "\n", "df = load_data(DEFAULT_PATH, 10)\n", "df = convert_problems_to_df(df)\n", "df = filter_data(df, unique_repo=True)\n", "df[\"fixing_change\"] = await tqdm_asyncio.gather(\n", "    *[\n", "        neural_fix_file_selection(sample[\"log\"], sample[\"fixing_change\"])\n", "        for _, sample in df.iterrows()\n", "    ],\n", "    desc=\"Neural fix change file filtering\",\n", ")\n", "\n", "pickle.dump(df, open(\"/tmp/e2e_eval.pkl\", \"wb\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic import AsyncAnthropic\n", "from research.autofix.autofix_eval_static import repo_change_to_docs, run_all\n", "import os\n", "import pickle\n", "\n", "# from experimental.lior.improve_fix.autofix_client import AutofixClient\n", "from research.autofix.autofix_manager import AutofixManager\n", "\n", "os.environ[\"ANTHROPIC_API_KEY\"] = (\n", "    open(os.path.expanduser(\"~/.anthropic\")).read().strip()\n", ")\n", "os.environ[\"AUGMENT_TOKEN\"] = open(os.path.expanduser(\"~/.augment\")).read().strip()\n", "\n", "df = pickle.load(open(\"/tmp/e2e_eval.pkl\", \"rb\"))\n", "client = AutofixManager(AsyncAnthropic(), verbose=False)\n", "# client = AutofixClient(\"http://127.0.0.1:5000\")\n", "\n", "docs = df[\"breaking_change\"].apply(repo_change_to_docs).explode().to_list()\n", "client.localization_model.add_docs(docs)\n", "client.rca_retriever.add_docs(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["artifacts = await run_all(\n", "    client,\n", "    df,\n", "    use_gold_locations=False,\n", "    context_retrieval=True,\n", "    top_k_edit_locations=32,\n", ")\n", "exact_match_score = artifacts[\"exact_match_score\"].mean()\n", "edit_distance_score = artifacts[\"edit_distance_score\"].mean()\n", "print(f\"{exact_match_score=:.3f} {edit_distance_score=:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import pandas as pd\n", "\n", "artifacts = pd.concat(\n", "    [\n", "        pd.read_parquet(f)\n", "        for f in glob.glob(\"/mnt/efs/augment/user/lior/artifacts_v2/*.parquet\")\n", "    ]\n", ")\n", "\n", "print(f\"Got {len(artifacts)} artifacts\")\n", "\n", "artifacts[\"exact_match_score\"].mean(), artifacts[\"edit_distance_score\"].mean()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "\n", "def hist_file_counts(artifacts, nbins: int = 10):\n", "    plt.figure(figsize=(12, 6))\n", "    x, y, z, w = (\n", "        artifacts[\"breaking_diff_files\"].str.len(),\n", "        artifacts[\"fixing_diff_files\"].str.len(),\n", "        artifacts[\"suggested_diff_files\"].str.len(),\n", "        artifacts[\"fix_plan\"].apply(lambda x: len(x[\"fix_plan\"][\"changes\"])),\n", "    )\n", "    bins: list[float] = list(range(nbins + 1))\n", "    plt.hist(\n", "        [x, y, z, w],\n", "        label=[\n", "            \"Breaking Diff Files\",\n", "            \"Fixing Diff Files\",\n", "            \"Suggested Diff Files\",\n", "            \"Fix Plan Changes\",\n", "        ],\n", "        bins=bins,\n", "        alpha=0.6,\n", "        edgecolor=\"black\",\n", "        color=[\"red\", \"green\", \"purple\", \"blue\"],\n", "        density=True,\n", "    )\n", "\n", "    plt.title(\"Distribution of Diff File Counts\", fontsize=16)\n", "    plt.xticks(np.arange(0.5, nbins + 0.5, 1), [str(i) for i in range(nbins)])\n", "    plt.xlabel(\"Number of Files\", fontsize=12)\n", "    plt.ylabel(\"Frequency\", fontsize=12)\n", "    plt.legend(fontsize=10)\n", "\n", "    # Add grid and set y-axis intervals\n", "    plt.grid(True, linestyle=\"--\", alpha=0.7)\n", "    plt.yticks(\n", "        np.arange(0, 1.05, 0.05)\n", "    )  # Set y-axis ticks from 0 to 1 with 0.05 intervals\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "\n", "hist_file_counts(artifacts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_exact_match(artifacts):\n", "    def calc_exact_match(sample) -> int:\n", "        suggested_paths = set(\n", "            [file[\"after\"][\"path\"] for file in sample[\"suggested_files\"]]\n", "        )\n", "        target_paths = set([file[\"after\"][\"path\"] for file in sample[\"target_files\"]])\n", "        both = suggested_paths.intersection(target_paths)\n", "        if len(both) == 0:\n", "            return 2\n", "        same_count: int = 0\n", "\n", "        def get_file_by_path(files, path):\n", "            for file in files:\n", "                if file[\"after\"][\"path\"] == path:\n", "                    return file[\"after\"][\"contents\"]\n", "            return None\n", "\n", "        for path in both:\n", "            if get_file_by_path(sample[\"suggested_files\"], path) == get_file_by_path(\n", "                sample[\"target_files\"], path\n", "            ):\n", "                same_count += 1\n", "        if same_count == len(suggested_paths):\n", "            return 0\n", "        if same_count > 0:\n", "            return 1\n", "        return 2\n", "\n", "    # Get the counts for each category\n", "    counts = artifacts.apply(calc_exact_match, axis=1).value_counts().sort_index()\n", "\n", "    # Define custom colors and labels\n", "    labels = [\"Exact Match\", \"Partial Match\", \"No Match\"]\n", "    plt.pie(\n", "        counts,\n", "        colors=[\"#2ecc71\", \"#f1c40f\", \"#e74c3c\"],\n", "        labels=[f\"{labels[i]}\\n({count} samples)\" for i, count in enumerate(counts)],\n", "        autopct=\"%1.1f%%\",\n", "        startangle=90,\n", "    )\n", "\n", "    # Add legend\n", "    plt.legend(\n", "        [f\"{labels[i]}\" for i in range(len(labels))],\n", "        title=\"Match Categories\",\n", "        loc=\"center left\",\n", "        bbox_to_anchor=(1, 0, 0.5, 1),\n", "    )\n", "\n", "    plt.title(\"Distribution of Code Match Results\")\n", "    plt.show()\n", "\n", "\n", "plot_exact_match(artifacts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["artifacts[artifacts[\"exact_match_score\"] == 1][\"suggested_diff_len\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display\n", "\n", "artifacts[\"suggested_diff_len\"] = artifacts[\"suggested_diff\"].apply(\n", "    lambda x: len(x.splitlines(keepends=True))\n", ")\n", "artifacts.sort_values(by=[\"suggested_diff_len\"], ascending=[False], inplace=True)\n", "\n", "sample = artifacts[\n", "    (artifacts[\"exact_match_score\"] == 1) & (artifacts[\"log\"].str.contains(\"pytest\"))\n", "    # (artifacts[\"suggested_diff_files\"].str.len() == 0)\n", "].iloc[7]\n", "\n", "display(\n", "    Markdown(\n", "        f'#### {sample[\"repo_id\"]} {sample[\"sha\"][:8]} {sample[\"check_run_name\"]} ([pull request](https://github.com/search?q=repo%3A{sample[\"repo_id\"]}+{sample[\"sha\"]}&type=pullrequests)) - {sample[\"files_in_repo\"]} files in repo'\n", "    )\n", ")\n", "display(\n", "    Markdown(\n", "        f'breaking change files: {\", \".join([f\"`{f}`\" for f in sample[\"breaking_diff_files\"]])}'\n", "    )\n", ")\n", "display(Markdown(f'#### {sample[\"root_cause\"][\"critical_thinking\"][\"text\"]}'))\n", "display(\n", "    Markdown(\n", "        f'#### {sample[\"root_cause\"][\"root_cause\"][\"root_cause_desc\"]}\\n'\n", "        + \"\\n\\n\".join(\n", "            [\n", "                f\"`{c['path']}` - `{c['change_desc']}`\"\n", "                for c in sample[\"root_cause\"][\"root_cause\"][\"changes\"]\n", "            ]\n", "        )\n", "    )\n", ")\n", "display(\n", "    Markdown(\n", "        f'#### suggested fix plan\\n```{sample[\"fix_plan\"][\"fix_plan\"][\"fix_desc\"]}```'\n", "    )\n", ")\n", "for change in sample[\"fix_plan\"][\"fix_plan\"][\"changes\"]:\n", "    display(\n", "        Markdown(\n", "            f' - `{change[\"path\"]}` - `{change[\"change_desc\"]}`\\n```\\n{change[\"code_block\"]}\\n```'\n", "        )\n", "    )\n", "max_lines = 300\n", "display(\n", "    Markdown(\n", "        f'#### suggested fix:\\n```diff\\n{\"\".join(sample[\"suggested_diff\"].splitlines(keepends=True)[:max_lines])}\\n```'\n", "    )\n", ")\n", "display(\n", "    Markdown(\n", "        f'#### user fix:\\n```diff\\n{\"\".join(sample[\"fixing_diff\"].splitlines(keepends=True)[:max_lines])}\\n```'\n", "    )\n", ")\n", "display(\n", "    Markdown(\n", "        f'#### breaking change:\\n```diff\\n{\"\".join(sample[\"breaking_diff\"].splitlines(keepends=True)[:max_lines])}\\n```'\n", "    )\n", ")\n", "display(\n", "    Markdown(\n", "        f'#### log:\\n```bash\\n{\"\".join(sample[\"log\"].splitlines(keepends=True)[-max_lines:])}\\n```'\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "\n", "def sample_to_smart_paste(sample):\n", "    before = pd.DataFrame(\n", "        list(map(lambda x: x[\"before\"], sample[\"target_files\"]))\n", "    ).rename(columns={\"contents\": \"original\"})\n", "    after = pd.DataFrame(\n", "        list(map(lambda x: x[\"after\"], sample[\"target_files\"]))\n", "    ).rename(columns={\"contents\": \"target\"})\n", "    plan = pd.DataFrame(sample[\"fix_plan\"][\"changes\"])\n", "    suggested = pd.DataFrame(\n", "        list(map(lambda x: x[\"after\"], sample[\"target_files\"])),\n", "        columns=[\"path\", \"suggested\"],\n", "    ).fillna(before.rename(columns={\"original\": \"suggested\"}))\n", "    return (\n", "        before.merge(on=\"path\", right=after, how=\"left\")\n", "        .merge(on=\"path\", right=plan, how=\"left\")\n", "        .merge(on=\"path\", right=suggested, how=\"left\")\n", "    )\n", "\n", "\n", "sample_to_smart_paste(sample).to_parquet(\"sample.parquet\", compression=\"snappy\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
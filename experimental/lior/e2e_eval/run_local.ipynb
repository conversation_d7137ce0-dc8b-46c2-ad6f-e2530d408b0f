{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["from anthropic import AsyncAnthropic\n", "import os\n", "\n", "# from experimental.lior.improve_fix.autofix_client import AutofixClient\n", "from experimental.lior.improve_fix.autofix_manager import AutofixManager\n", "\n", "os.environ[\"ANTHROPIC_API_KEY\"] = (\n", "    open(os.path.expanduser(\"~/.anthropic\")).read().strip()\n", ")\n", "os.environ[\"AUGMENT_TOKEN\"] = open(os.path.expanduser(\"~/.augment\")).read().strip()\n", "\n", "client = AutofixManager(AsyncAnthropic(), verbose=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["from autofix_run_local import run_local\n", "\n", "artifacts = await run_local(\n", "    \"bazel test //base/prompt_format_autofix:create_fix_plan_prompt_formatter_test\",\n", "    \"/home/<USER>/augment\",\n", "    client,\n", ")\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}
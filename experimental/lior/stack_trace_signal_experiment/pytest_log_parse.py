import re


def get_summary(log: str) -> dict[str, int]:
    pattern = r"^.*\s=+\s?(\d+\s\w+,?\s)?(\d+\s\w+,?\s)?(\d+\s\w+,?\s)?(\d+\s\w+,?\s)(\d+\s\w+,?\s)?(\d+\s\w+,?\s)?.*?\s=+$"
    ret = re.findall(pattern, log, re.MULTILINE)
    if ret:
        ret = ret[0]
        # remove empty strings
        ret = list(filter(None, ret))
        ret = [x.strip() for x in ret]
        ret = [x[:-1] if x.endswith(",") else x for x in ret]
        ret = [x.split(" ") for x in ret]
        return {x[1]: int(x[0]) for x in ret}
    return None


def get_failures(log: str) -> list[str]:
    pattern = r"^.*[FAILED,ERROR]\s+(.*::\w+.*)\s+-\s+.*$"
    ret = re.findall(pattern, log, re.MULTILINE)
    if ret:
        return ret
    return None


def get_pandas_tag(log: str) -> str:
    pattern = r"^.*Successfully installed pandas-(.*?)\+.*$"
    ret = re.findall(pattern, log, re.MULTILINE)
    if ret:
        return ret[0]
    return None

import os
from context import CodeHunk
from abc import ABC, abstractmethod


def format_code_snippet(hunk: CodeHunk) -> str:
    hunk_code = hunk.code
    lined_code = os.linesep.join(
        f"{hunk.start_line + i}: {line}" for i, line in enumerate(hunk_code.split("\n"))
    )
    formated_snippet = (
        f"label - {hunk.label}"
        + "\n"
        + f"file_path - {hunk.path}"
        + "\n```\n"
        + lined_code
        + "\n```\n"
    )
    return formated_snippet


class AbstractPromptEng(ABC):
    @abstractmethod
    def system_msg(self, ctx_list: list[CodeHunk]) -> str:
        """
        The system message is the first message in the conversation.

        Args:
            ctx_list: list of CodeHunks to be provided to the LLM
        """
        pass

    @abstractmethod
    def user_msg(self, stack_trace_format: str, git_diff_format: str) -> str:
        """
        The user message is the second message in the conversation.

        Args:
            stack_trace_format: formatted stack traces
            git_diff_format: formatted git diff
        """
        pass

    @abstractmethod
    def user_msg_retry(self) -> str:
        """
        The user message is used to ensure the patch is valid.
        """
        pass


# LLAMA


class LlamaPromptEng(AbstractPromptEng):
    def system_msg(self, ctx_list: list[CodeHunk]) -> str:
        return f"""
You are a helpful developer investigating and fixing test failures in the code.
Provided code snippets:
{
            os.linesep.join(format_code_snippet(hunk) for hunk in ctx_list)
        }
"""

    def user_msg(self, stack_trace_format: str, git_diff_format: str) -> str:
        return f"""
I have some test failures.

Stack traces:
```
{stack_trace_format}
```

Code Changes:
```
{git_diff_format}
```

Explain why the test fails and how to fix it.
"""

    def user_msg_retry(self) -> str:
        return """
Great! Now rewrite the provided code snippets to fix the broken tests.
As a reminder, the snippet format is as follows:
label - <label>
file_path - <path>
```
<line_numbered_code>
```
"""


class DeepSeekPromptEng(AbstractPromptEng):
    def system_msg(self, ctx_list: list[CodeHunk]) -> str:
        return f"""
You are a helpful developer investigating and fixing test failures in the code.
Here is a list of code snippets:
{
            os.linesep.join(format_code_snippet(hunk) for hunk in ctx_list)
        }
"""

    def user_msg(self, stack_trace_format: str, git_diff_format: str) -> str:
        return f"""
I have some test failures I need help fixing.

The stack traces for the failing tests:
```
{stack_trace_format}
```

The git diff leading to the failure:
```
{git_diff_format}
```

1. Explain the why the test fails.
2. Explain how to fix the failure by modifying the provided snippets.
3. Revise the provided snippets to fix the failure.
"""

    def user_msg_retry(self) -> str:
        return """
Great! Now rewrite the provided code snippets based on your explanation.
Maintain original snippet format, with label, path and line numbers.
As a reminder, the code snippet format is as follows:
label - <label>
file_path - <path>
```
<new_code>
```
"""


class AnthropicPromptEng(AbstractPromptEng):
    def system_msg(self, ctx_list: list[CodeHunk]) -> str:
        return f"""
You are a helpful developer investigating and fixing test failures in the code.
Here is a list of code snippets:
{
            os.linesep.join(format_code_snippet(hunk) for hunk in ctx_list)
        }
"""

    def user_msg(self, stack_trace_format: str, git_diff_format: str) -> str:
        return f"""
I have some test failures I need help fixing.

The stack traces for the failing tests:
```
{stack_trace_format}
```

The git diff leading to the failure:
```
{git_diff_format}
```

1. Explain the why the test fails.
2. Explain how to fix the failure by modifying the provided snippets.
3. Revise the provided snippets to fix the failure.
"""

    def user_msg_retry(self) -> str:
        return """
Great! Now rewrite the provided code snippets based on your explanation.
Maintain original snippet format, with label, path and line numbers.
As a reminder, the code snippet format is as follows:
label - <label>
file_path - <path>
```
<new_code>
```
"""


def model_to_prompt_eng(model: str) -> AbstractPromptEng:
    if model == "llama":
        return LlamaPromptEng()
    elif model == "deepseek":
        return DeepSeekPromptEng()
    elif model == "anthropic":
        return AnthropicPromptEng()
    else:
        raise ValueError(f"Unknown model {model}")

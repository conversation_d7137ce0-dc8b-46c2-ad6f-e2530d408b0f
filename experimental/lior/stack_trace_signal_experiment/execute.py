import os
import subprocess
import pandas as pd
import xml.etree.ElementTree as ET
import shlex


def parse_junit_xml(xml_string: str) -> pd.DataFrame:
    tree = ET.ElementTree(ET.fromstring(xml_string))
    root = tree.getroot()
    testcases = []
    for testcase in root.findall(".//testcase"):
        failure_msg = None
        if testcase.find("failure") is not None:
            failure_msg = testcase.find("failure").text
        elif testcase.find("error") is not None:
            if testcase.find("error").attrib["message"] == "collection failure":
                raise Exception("Collection failure")
            failure_msg = testcase.find("error").text
        testcases.append(
            {
                "name": testcase.attrib["name"],
                "classname": testcase.attrib["classname"],
                "time": testcase.attrib["time"],
                "failure": failure_msg,
            }
        )
    return pd.DataFrame(testcases)


def build_docker_image(
    repo_url: str, pr_number: int, commit_hash: str, tag_ver: str, quiet: bool = False
):
    temp_dir = os.path.join("/tmp", "test_docker", str(pr_number))
    docker_file_path = os.path.join(temp_dir, "Dockerfile")
    docker_file = f"""
    FROM python:3.10.8

    # Install necessary packages
    RUN apt-get update && apt-get install libgl1 -y

    # Configure the working directory
    WORKDIR /home/<USER>

    # Checkout the specific commit
    RUN git init /home/<USER>
    RUN cd /home/<USER>
    RUN git remote add origin {repo_url}
    RUN git fetch --quiet --depth 1 origin {commit_hash}
    RUN git -c advice.detachedHead=false checkout FETCH_HEAD

    # Tag the commit
    RUN git config --global user.email "<EMAIL>"
    RUN git tag -a v{tag_ver} {commit_hash} -m "Tagging {tag_ver}"

    # Set environment variables
    ENV MAMBA_ROOT_PREFIX=/home/<USER>/root_micromamba
    ENV HOME=/home/<USER>

    # Download and extract micromamba
    RUN curl -Ls https://micro.mamba.pm/api/micromamba/linux-64/latest | tar -xvj --strip-components=1 bin/micromamba

    # Remove locks from conda
    RUN echo "use_lockfiles: false" >> ci/.condarc

    # Create conda environment
    RUN ./micromamba create -n test --file environment.yml --rc-file ci/.condarc

    # Activate the environment and install the package
    RUN ./micromamba run -n test python -m pip install -ve . --no-build-isolation --config-settings=editable-verbose=true

    # Set entrypoint for running tests
    ENTRYPOINT ["./micromamba", "run", "-n", "test"]
    """  # noqa
    os.makedirs(temp_dir, exist_ok=True)
    with open(docker_file_path, "w") as f:
        f.write(docker_file)
    subprocess.run(
        [
            "docker",
            "build",
            "--platform",
            "linux/amd64",
            temp_dir,
            "-t",
            f"test_docker:{pr_number}",
        ]
        + (["-q"] if quiet else []),
        check=True,
    )


def remove_binary_files(patch: str) -> str:
    return "\n".join(
        line for line in patch.split("\n") if not line.startswith("Binary files")
    )


def run_tests(
    pr_number: int,
    failed_tests: list[str],
    patch_seq: list[str] = [],
    quiet: bool = False,
) -> str:
    temp_dir = os.path.join("/tmp", "test_docker", str(pr_number))
    script_path = os.path.join(temp_dir, "run_tests.sh")
    test_list = " ".join(shlex.quote(test) for test in failed_tests)

    script = f"""
    #!/bin/bash
    git reset --hard
    {os.linesep.join(f"patch -p1 < /tmp/patch_{i}.diff" for i in range(len(patch_seq)))}
    ./micromamba update --all -y -q
    rm -f /tmp/result.xml
    pytest -n auto -m "not single_cpu" {test_list} --junitxml=/tmp/result.xml
    sync
    """
    os.makedirs(temp_dir, exist_ok=True)
    with open(script_path, "w") as f:
        f.write(script)
    for i, patch in enumerate(patch_seq):
        patch_path = os.path.join(temp_dir, f"patch_{i}.diff")
        with open(patch_path, "w") as f:
            f.write(remove_binary_files(patch))
    stdout, _ = subprocess.Popen(
        [
            "docker",
            "run",
            "--rm",
            "-v",
            f"{temp_dir}:/tmp",
            "--platform",
            "linux/amd64",
            f"test_docker:{pr_number}",
            "bash",
            "/tmp/run_tests.sh",
        ],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
    ).communicate()
    if not quiet:
        print(stdout.decode())
    with open(os.path.join(temp_dir, "result.xml")) as f:
        return f.read()

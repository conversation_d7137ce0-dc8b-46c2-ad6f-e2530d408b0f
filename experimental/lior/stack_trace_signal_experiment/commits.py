from dataclasses import dataclass
import pandas as pd
from asyncio import sleep, CancelledError
from tqdm.asyncio import tqdm
import click

from aiohttp import ClientSession


@dataclass
class Commit:
    pr_number: int
    branch: str
    base: str
    merged_at: str
    title: str
    sha: str
    commit_date: str
    message: str
    parents: list[str]


PAGINATED_QUERY = """
query($endCursor: String, $owner: String!, $name: String!) {
  repository(owner: $owner, name: $name) {
    pullRequests(states: MERGED, first: 100, orderBy: {field: CREATED_AT, direction: DESC}, after: $endCursor) {
      totalCount
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          number
          headRefName
          baseRefName
          mergedAt
          title
          commits(last: 100) {
            edges {
              node {
                commit {
                  oid
                  committedDate
                  message
                  parents (last: 10) {
                    edges {
                      node {
                        oid
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
"""


async def fetch_data(session, query, end_cursor, owner, name):
    while True:
        async with session.post(
            "/graphql",
            json={
                "query": query,
                "variables": {
                    "endCursor": end_cursor,
                    "owner": owner,
                    "name": name,
                },
            },
            timeout=15,
        ) as response:
            if response.status == 200:
                ret = await response.json()
                if "errors" in ret:
                    error = ret["errors"][0]
                    if "type" in error and error["type"] == "RATE_LIMITED":
                        rate_limit = response.headers.get("X-RateLimit-Limit")
                        rate_limit_remaining = response.headers.get(
                            "X-RateLimit-Remaining"
                        )
                        reset_time = response.headers.get("X-RateLimit-Reset")
                        utc_now = pd.Timestamp.utcnow()
                        utc_time = pd.to_datetime(int(reset_time), unit="s", utc=True)
                        time_diff = utc_time - utc_now
                        click.echo(
                            f"\nRate limited ({rate_limit_remaining}/{rate_limit}),"
                            f" retrying in {time_diff} second..."
                        )
                        await sleep(time_diff.total_seconds() + 1)
                        continue
                    click.echo(f"Error in response: {ret['errors']}")
                    raise Exception("Error in response")
                return ret
            else:
                text = await response.text()
                click.echo(f"Error: {text}")
                click.echo("Retrying in 1 second...")
                await sleep(1)


async def get_commits(session: ClientSession, full_name: str) -> pd.DataFrame:
    query = PAGINATED_QUERY
    end_cursor = None
    has_next_page = True
    commits = []
    owner, name = full_name.split("/")

    try:
        with tqdm(desc="Fetching commits") as pbar:
            while has_next_page:
                data = await fetch_data(session, query, end_cursor, owner, name)
                pull_requests = data["data"]["repository"]["pullRequests"]
                if pbar.total is None:
                    pbar.total = pull_requests["totalCount"]
                for pr in pull_requests["edges"]:
                    pr_node = pr["node"]
                    for commit_node in pr_node["commits"]["edges"]:
                        commit = Commit(
                            pr_number=pr_node["number"],
                            branch=pr_node["headRefName"],
                            base=pr_node["baseRefName"],
                            merged_at=pr_node["mergedAt"],
                            title=pr_node["title"],
                            sha=commit_node["node"]["commit"]["oid"],
                            commit_date=commit_node["node"]["commit"]["committedDate"],
                            message=commit_node["node"]["commit"]["message"],
                            parents=[
                                x["node"]["oid"]
                                for x in commit_node["node"]["commit"]["parents"][
                                    "edges"
                                ]
                            ],
                        )
                        commits.append(commit)

                page_info = data["data"]["repository"]["pullRequests"]["pageInfo"]
                end_cursor = page_info["endCursor"]
                has_next_page = page_info["hasNextPage"]
                pbar.update(len(pull_requests["edges"]))
    except (KeyboardInterrupt, CancelledError):
        click.echo(f"Stopping, total commits fetched: {len(commits)}")

    commits_df = pd.DataFrame(commits)
    if commits_df.empty:
        return commits_df
    commits_df["pr_url"] = commits_df["pr_number"].map(
        lambda x: f"https://github.com/{full_name}/pull/{x}"
    )
    commits_df["https_git_url"] = f"https://github.com/{full_name}.git"
    commits_df.sort_values(["pr_number", "commit_date"], inplace=True, ascending=True)
    commits_df["first_sha"] = commits_df.groupby("pr_number").transform("first")[
        "parents"
    ]
    commits_df["first_sha"] = commits_df["first_sha"].map(lambda x: x[0])
    commits_df["last_sha"] = commits_df.groupby("pr_number").transform("last")["sha"]
    commits_df["full_name"] = full_name
    return commits_df

from dataclasses import dataclass
import hashlib
import os
from aiohttp import Client<PERSON>ession


@dataclass
class ContextRequest:
    path: str
    start_line: int
    length: int


@dataclass
class CodeHunk:
    label: str
    path: str
    start_line: int
    code: str


async def get_context(
    full_name, commit_hash, ctx_reqs: list[ContextRequest]
) -> list[CodeHunk]:
    code_hunks = []
    async with ClientSession() as session:
        for ctx_req in ctx_reqs:
            async with session.get(
                f"https://raw.githubusercontent.com/{full_name}/{commit_hash}/{ctx_req.path}"
            ) as response:
                source = await response.text()
                code_hunks.append(
                    CodeHunk(
                        label=hashlib.md5(
                            f"{ctx_req.path}_{ctx_req.start_line}".encode()
                        ).hexdigest(),
                        path=ctx_req.path,
                        start_line=ctx_req.start_line,
                        code=os.linesep.join(
                            # Note that start_line is 1-indexed
                            source.split("\n")[
                                ctx_req.start_line - 1 : ctx_req.start_line
                                + ctx_req.length
                                - 1
                            ]
                        ),
                    )
                )
    return code_hunks

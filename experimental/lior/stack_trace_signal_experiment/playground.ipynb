{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LLAMA 3 70B - 5 / 12 (38.46%)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from context import CodeHunk, get_context, ContextRequest\n", "import json\n", "\n", "from execute import build_docker_image, parse_junit_xml, run_tests\n", "from llm import MAX_STACK_TRACES, generate_patch\n", "\n", "merged_df = pd.read_parquet(\"/tmp/artifacts/cGFuZGFzLWRldi9wYW5kYXM=/VW5pdCBUZXN0cw==/merged.parquet\")\n", "\n", "# Keep only rows with context\n", "with open(\"./context_reqs.json\") as f:\n", "    json_reqs = json.load(f)\n", "\n", "prs = [req[\"pr_number\"] for req in json_reqs]\n", "print(f\"Found {len(prs)} PRs with context requests\")\n", "conversations = []\n", "\n", "for pr in prs:\n", "    row = merged_df[merged_df[\"pr_number\"] == pr].iloc[0]\n", "    print(f\"Pull request: {row['pr_number']} ({row['pr_url']}), breaking commit: {row['sha'][:7]}\")\n", "    \n", "    # Get context\n", "    print(\"Getting context...\")\n", "    pr_reqs = next(req for req in json_reqs if req[\"pr_number\"] == row[\"pr_number\"])\n", "    reqs = [ContextRequest(path=req[\"path\"], start_line=req[\"start_line\"], length=req[\"length\"]) for req in pr_reqs[\"context\"]]\n", "    ctx_list: list[CodeHunk] = await get_context(\"pandas-dev/pandas\", row[\"sha\"], ctx_reqs=reqs)\n", "    test_list = list(row[\"failures\"])[:MAX_STACK_TRACES]\n", "\n", "    # Build image\n", "    print(\"Building image...\")\n", "    build_docker_image(\n", "        row[\"https_git_url\"], row[\"pr_number\"], row[\"first_sha\"], row[\"pandas_tag\"], quiet=True\n", "    )\n", "\n", "    # Run tests on breaking change\n", "    print(\"Running tests on breaking change...\")\n", "    xml_result = run_tests(row[\"pr_number\"], \n", "                           test_list, \n", "                           patch_seq=[row[\"breaking_change\"]], \n", "                           quiet=True,\n", "    )\n", "    df = parse_junit_xml(xml_result)\n", "    for r in df.sort_values(by=\"name\").itertuples():\n", "        print(f\"[{'PASS' if r.failure is None else 'FAILURE'}] {r.name}\")\n", "    print(\n", "        \"====== \" + \n", "        \", \".join([f\"{key}: {val}\" for key, val in row[\"summary\"].items()]) + \n", "        \" ======\"\n", "    )\n", "\n", "    # Get LLM patch\n", "    print(\"Generating LLM patch...\")\n", "    try:\n", "        llm_patch, conversation = generate_patch(\n", "            ctx_list=ctx_list, \n", "            stack_traces=df[\"failure\"].to_list(), \n", "            git_diff=row[\"breaking_change\"],\n", "            model=\"llama\",\n", "        )\n", "        conversations.append(conversation)\n", "    except Exception as e:\n", "        print(f\"Failed to generate LLM patch: {e}\\n\\n\")\n", "        continue\n", "    \n", "    # Run tests on breaking change with LLM patch\n", "    print(\"Running tests on breaking change with LLM patch...\")\n", "    try:\n", "        xml_result = run_tests(row[\"pr_number\"], \n", "                            test_list, \n", "                            patch_seq=[row[\"breaking_change\"], llm_patch], \n", "                            quiet=True,\n", "        )\n", "        df = parse_junit_xml(xml_result)\n", "    except Exception as e:\n", "        print(f\"Failed to run tests: {e}\\n\\n\")\n", "        continue\n", "    for r in df.sort_values(by=\"name\").itertuples():\n", "        print(f\"[{'PASS 🎉' if r.failure is None else 'FAILURE 🤦🏻‍♂️'}] {r.name}\")\n", "    print(\"\\n\")\n", "pd.DataFrame(conversations).to_csv(\"llama_conversations.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON> 3.5 - 10/13 (76.92%)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from context import CodeHunk, get_context, ContextRequest\n", "import json\n", "\n", "from execute import build_docker_image, parse_junit_xml, run_tests\n", "from llm import MAX_STACK_TRACES, generate_patch\n", "\n", "merged_df = pd.read_parquet(\"/tmp/artifacts/cGFuZGFzLWRldi9wYW5kYXM=/VW5pdCBUZXN0cw==/merged.parquet\")\n", "\n", "# Keep only rows with context\n", "with open(\"./context_reqs.json\") as f:\n", "    json_reqs = json.load(f)\n", "\n", "prs = [req[\"pr_number\"] for req in json_reqs]\n", "print(f\"Found {len(prs)} PRs with context requests\")\n", "conversations = []\n", "\n", "for pr in prs:\n", "    row = merged_df[merged_df[\"pr_number\"] == pr].iloc[0]\n", "    print(f\"Pull request: {row['pr_number']} ({row['pr_url']}), breaking commit: {row['sha'][:7]}\")\n", "    \n", "    # Get context\n", "    print(\"Getting context...\")\n", "    pr_reqs = next(req for req in json_reqs if req[\"pr_number\"] == row[\"pr_number\"])\n", "    reqs = [ContextRequest(path=req[\"path\"], start_line=req[\"start_line\"], length=req[\"length\"]) for req in pr_reqs[\"context\"]]\n", "    ctx_list: list[CodeHunk] = await get_context(\"pandas-dev/pandas\", row[\"sha\"], ctx_reqs=reqs)\n", "    test_list = list(row[\"failures\"])[:MAX_STACK_TRACES]\n", "\n", "    # Build image\n", "    print(\"Building image...\")\n", "    build_docker_image(\n", "        row[\"https_git_url\"], row[\"pr_number\"], row[\"first_sha\"], row[\"pandas_tag\"], quiet=True\n", "    )\n", "\n", "    # Run tests on breaking change\n", "    print(\"Running tests on breaking change...\")\n", "    xml_result = run_tests(row[\"pr_number\"], \n", "                           test_list, \n", "                           patch_seq=[row[\"breaking_change\"]], \n", "                           quiet=True,\n", "    )\n", "    df = parse_junit_xml(xml_result)\n", "    for r in df.sort_values(by=\"name\").itertuples():\n", "        print(f\"[{'PASS' if r.failure is None else 'FAILURE'}] {r.name}\")\n", "    print(\n", "        \"====== \" + \n", "        \", \".join([f\"{key}: {val}\" for key, val in row[\"summary\"].items()]) + \n", "        \" ======\"\n", "    )\n", "\n", "    # Get LLM patch\n", "    print(\"Generating LLM patch...\")\n", "    try:\n", "        llm_patch, conversation = generate_patch(\n", "            ctx_list=ctx_list, \n", "            stack_traces=df[\"failure\"].to_list(), \n", "            git_diff=row[\"breaking_change\"],\n", "            model=\"anthropic\",\n", "        )\n", "        conversations.append(conversation)\n", "    except Exception as e:\n", "        print(f\"Failed to generate LLM patch: {e}\\n\\n\")\n", "        continue\n", "    \n", "    # Run tests on breaking change with LLM patch\n", "    print(\"Running tests on breaking change with LLM patch...\")\n", "    try:\n", "        xml_result = run_tests(row[\"pr_number\"], \n", "                            test_list, \n", "                            patch_seq=[row[\"breaking_change\"], llm_patch], \n", "                            quiet=True,\n", "        )\n", "        df = parse_junit_xml(xml_result)\n", "    except Exception as e:\n", "        print(f\"Failed to run tests: {e}\\n\\n\")\n", "        continue\n", "    for r in df.sort_values(by=\"name\").itertuples():\n", "        print(f\"[{'PASS 🎉' if r.failure is None else 'FAILURE 🤦🏻‍♂️'}] {r.name}\")\n", "    print(\"\\n\")\n", "pd.DataFrame(conversations).to_csv(\"anthropic_conversations.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DeepSeek v2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from context import CodeHunk, get_context, ContextRequest\n", "import json\n", "\n", "from execute import build_docker_image, parse_junit_xml, run_tests\n", "from llm import MAX_STACK_TRACES, generate_patch\n", "\n", "merged_df = pd.read_parquet(\"/tmp/artifacts/cGFuZGFzLWRldi9wYW5kYXM=/VW5pdCBUZXN0cw==/merged.parquet\")\n", "\n", "# Keep only rows with context\n", "with open(\"./context_reqs.json\") as f:\n", "    json_reqs = json.load(f)\n", "\n", "prs = [req[\"pr_number\"] for req in json_reqs]\n", "print(f\"Found {len(prs)} PRs with context requests\")\n", "conversations = []\n", "\n", "for pr in prs:\n", "    row = merged_df[merged_df[\"pr_number\"] == pr].iloc[0]\n", "    print(f\"Pull request: {row['pr_number']} ({row['pr_url']}), breaking commit: {row['sha'][:7]}\")\n", "    \n", "    # Get context\n", "    print(\"Getting context...\")\n", "    pr_reqs = next(req for req in json_reqs if req[\"pr_number\"] == row[\"pr_number\"])\n", "    reqs = [ContextRequest(path=req[\"path\"], start_line=req[\"start_line\"], length=req[\"length\"]) for req in pr_reqs[\"context\"]]\n", "    ctx_list: list[CodeHunk] = await get_context(\"pandas-dev/pandas\", row[\"sha\"], ctx_reqs=reqs)\n", "    test_list = list(row[\"failures\"])[:MAX_STACK_TRACES]\n", "\n", "    # Build image\n", "    print(\"Building image...\")\n", "    build_docker_image(\n", "        row[\"https_git_url\"], row[\"pr_number\"], row[\"first_sha\"], row[\"pandas_tag\"], quiet=True\n", "    )\n", "\n", "    # Run tests on breaking change\n", "    print(\"Running tests on breaking change...\")\n", "    xml_result = run_tests(row[\"pr_number\"], \n", "                           test_list, \n", "                           patch_seq=[row[\"breaking_change\"]], \n", "                           quiet=True,\n", "    )\n", "    df = parse_junit_xml(xml_result)\n", "    for r in df.sort_values(by=\"name\").itertuples():\n", "        print(f\"[{'PASS' if r.failure is None else 'FAILURE'}] {r.name}\")\n", "    print(\n", "        \"====== \" + \n", "        \", \".join([f\"{key}: {val}\" for key, val in row[\"summary\"].items()]) + \n", "        \" ======\"\n", "    )\n", "\n", "    # Get LLM patch\n", "    print(\"Generating LLM patch...\")\n", "    try:\n", "        llm_patch, conversation = generate_patch(\n", "            ctx_list=ctx_list, \n", "            stack_traces=df[\"failure\"].to_list(), \n", "            git_diff=row[\"breaking_change\"],\n", "            model=\"deepseek\",\n", "        )\n", "        conversations.append(conversation)\n", "    except Exception as e:\n", "        print(f\"Failed to generate LLM patch: {e}\\n\\n\")\n", "        continue\n", "    \n", "    # Run tests on breaking change with LLM patch\n", "    print(\"Running tests on breaking change with LLM patch...\")\n", "    try:\n", "        xml_result = run_tests(row[\"pr_number\"], \n", "                            test_list, \n", "                            patch_seq=[row[\"breaking_change\"], llm_patch], \n", "                            quiet=True,\n", "        )\n", "        df = parse_junit_xml(xml_result)\n", "    except Exception as e:\n", "        print(f\"Failed to run tests: {e}\\n\\n\")\n", "        continue\n", "    for r in df.sort_values(by=\"name\").itertuples():\n", "        print(f\"[{'PASS 🎉' if r.failure is None else 'FAILURE 🤦🏻‍♂️'}] {r.name}\")\n", "    print(\"\\n\")\n", "pd.DataFrame(conversations).to_csv(\"deepseek_conversations.csv\", index=False)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 0}
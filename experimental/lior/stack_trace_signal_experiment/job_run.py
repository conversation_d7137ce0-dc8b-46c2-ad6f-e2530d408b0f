from dataclasses import dataclass
from aiohttp import ClientSession
import pandas as pd
from asyncio import sleep, CancelledError
from tqdm.asyncio import tqdm
import click


@dataclass
class JobRun:
    run_id: int
    run_name: str
    run_status: str
    event: str
    job_id: int
    job_name: str
    conclusion: str
    started_at: str
    completed_at: str
    sha: str


PAGINATED_QUERY: str = """
query($endCursor: String, $workflowNode: ID!) {
  node (id: $workflowNode) {
    ... on Workflow {
      runs (first: 100, orderBy: {field: CREATED_AT, direction: DESC}, after: $endCursor) {
        totalCount
        pageInfo {
          endCursor
          hasNextPage
        }
        edges {
          node {
            event
            file {
              path
            }
            checkSuite {
              status
              commit {
                oid
              }
              workflowRun {
                runNumber
              }
              checkRuns (last: 100) {
                edges {
                  node {
                    name
                    conclusion
                    databaseId
                    startedAt
                    completedAt
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
"""


async def fetch_data(session, query, end_cursor, workflow_node):
    while True:
        async with session.post(
            "/graphql",
            json={
                "query": query,
                "variables": {"endCursor": end_cursor, "workflowNode": workflow_node},
            },
            timeout=15,
        ) as response:
            if response.status == 200:
                ret = await response.json()
                if "errors" in ret:
                    error = ret["errors"][0]
                    if "type" in error and error["type"] == "RATE_LIMITED":
                        rate_limit = response.headers.get("X-RateLimit-Limit")
                        rate_limit_remaining = response.headers.get(
                            "X-RateLimit-Remaining"
                        )
                        reset_time = response.headers.get("X-RateLimit-Reset")
                        utc_now = pd.Timestamp.utcnow()
                        utc_time = pd.to_datetime(reset_time, unit="s", utc=True)
                        time_diff = utc_time - utc_now
                        click.echo(
                            f"Rate limited ({rate_limit_remaining}/{rate_limit}),"
                            f" retrying in {time_diff} second..."
                        )
                        await sleep(time_diff.total_seconds() + 1)
                        continue
                    click.echo(f"Error in response: {ret['errors']}")
                    raise Exception("Error in response")
                return ret
            else:
                text = await response.text()
                click.echo(f"Error: {text}")
                click.echo("Retrying in 1 second...")
                await sleep(1)


async def get_job_runs(
    session: ClientSession, workflow_node: str, full_name: str
) -> pd.DataFrame:
    query = PAGINATED_QUERY
    end_cursor = None
    has_next_page = True
    job_runs = []

    try:
        with tqdm(desc="Fetching workflow runs") as pbar:
            while has_next_page:
                data = await fetch_data(session, query, end_cursor, workflow_node)
                runs = data["data"]["node"]["runs"]
                if pbar.total is None:
                    pbar.total = runs["totalCount"]
                for run in runs["edges"]:
                    run_node = run["node"]
                    check_suite = run_node["checkSuite"]
                    for job in check_suite["checkRuns"]["edges"]:
                        job_node = job["node"]
                        job_runs.append(
                            JobRun(
                                run_id=check_suite["workflowRun"]["runNumber"],
                                run_name=run_node["file"]["path"],
                                run_status=check_suite["status"],
                                event=run_node["event"],
                                job_id=job_node["databaseId"],
                                job_name=job_node["name"],
                                conclusion=job_node["conclusion"],
                                started_at=job_node["startedAt"],
                                completed_at=job_node["completedAt"],
                                sha=check_suite["commit"]["oid"],
                            )
                        )

                page_info = data["data"]["node"]["runs"]["pageInfo"]
                end_cursor = page_info["endCursor"]
                has_next_page = page_info["hasNextPage"]
                pbar.update(len(runs["edges"]))
    except (KeyboardInterrupt, CancelledError):
        click.echo(f"Stopping, total job runs fetched: {len(job_runs)}")

    jobs_df = pd.DataFrame(job_runs)
    if jobs_df.empty:
        return jobs_df
    jobs_df["log_url"] = jobs_df["job_id"].map(
        lambda x: f"https://api.github.com/repos/{full_name}/actions/jobs/{x}/logs"
    )
    jobs_df["job_url"] = jobs_df.apply(
        lambda x: f"https://github.com/{full_name}/actions/runs/{x['run_id']}/job/{x['job_id']}",
        axis=1,
    )
    jobs_df["is_failed"] = jobs_df["conclusion"] == "FAILURE"
    jobs_df["run_failure"] = jobs_df.groupby("run_id")["is_failed"].transform(max)
    return jobs_df

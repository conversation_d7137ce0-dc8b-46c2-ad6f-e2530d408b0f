import anthropic
import os
from collections import defaultdict
import re
from dataclasses import dataclass
import replicate
from openai import OpenAI
import replicate.client
from prompt_eng import model_to_prompt_eng
from context import CodeHunk

with open(os.path.join(os.path.expanduser("~"), ".anthropic"), "r") as f:
    os.environ["ANTHROPIC_API_KEY"] = f.read().strip()

with open(os.path.join(os.path.expanduser("~"), ".replicate"), "r") as f:
    os.environ["REPLICATE_API_TOKEN"] = f.read().strip()

with open(os.path.join(os.path.expanduser("~"), ".deepseek"), "r") as f:
    os.environ["DEEPSEEK_API_KEY"] = f.read().strip()

anthropic_client = anthropic.Anthropic()
replicate_client = replicate.Client(api_token=os.environ["REPLICATE_API_TOKEN"])
deepseek_client = OpenAI(
    api_key=os.environ["DEEPSEEK_API_KEY"], base_url="https://api.deepseek.com"
)


MAX_STACK_TRACE_LENGTH: int = 50
MAX_STACK_TRACES: int = 5
MAX_GIT_DIFF: int = 200


@dataclass
class ChangeHunk:
    label: str
    path: str
    start_line: int
    source_code: str
    target_code: str


@dataclass
class Conversation:
    user: str
    system: str
    explanation: str
    response: str


def format_response(resp: str) -> str:
    return f"-----------------------------------\n{resp}\n-----------------------------------\n"


def parse_response(resp: str, ctx_list: list[CodeHunk]) -> dict:
    regex_pattern = r"label\s*-\s*\"?([a-f0-9]+)\"?\nfile_path\s*-\s*\"?(.+?)\"?\n```\w*\n(.*?)\n```\n?"
    parsed = re.findall(regex_pattern, resp, flags=re.DOTALL | re.MULTILINE)
    if len(parsed) == 0:
        raise ValueError("Could not parse the response\n" + format_response(resp))
    new_hunks = []
    for label, path, code in parsed:
        try:
            old_hunk = next(hunk for hunk in ctx_list if hunk.label == label)
        except StopIteration:
            print(
                f"Could not find hunk with label {label} in the list\n"
                + format_response(resp)
            )
            raise
        try:
            assert (
                old_hunk.path == path
            ), f"Sanity check failed, paths do not match, {old_hunk.path} != {path}"
        except AssertionError as e:
            print(
                f"Sanity check failed, paths do not match, {old_hunk.path} != {path}\n"
                + format_response(resp)
            )
            raise e
        try:
            code = os.linesep.join(
                [line[line.index(":") + 2 :] for line in code.split("\n")]
            )
        except ValueError:
            raise ValueError(
                "Some lines of code does not contain numbering\n"
                + format_response(resp)
            )
        new_hunks.append(
            ChangeHunk(
                path=old_hunk.path,
                start_line=old_hunk.start_line,
                source_code=old_hunk.code,
                target_code=code,
                label=label,
            )
        )
    return new_hunks


def change_to_patch(hunks: list[ChangeHunk]) -> str:
    out_list = []
    sorted_hunks = sorted(hunks, key=lambda x: (x.path, x.start_line))
    offsets = defaultdict(int)
    for hunk in sorted_hunks:
        source_lines = hunk.source_code.split("\n")
        target_lines = hunk.target_code.split("\n")

        offset = offsets[hunk.path]
        offsets[hunk.path] += len(target_lines) - len(source_lines)
        lines = []

        lines.append(f"diff --git a/{hunk.path} b/{hunk.path}")
        lines.append(
            f"@@ -{hunk.start_line},{len(source_lines)} +{hunk.start_line + offset},{len(target_lines)} @@"
        )
        lines.extend([f"-{line}" for line in source_lines])
        lines.extend([f"+{line}" for line in target_lines])
        out_list.extend(lines)
    return "\n".join(out_list) + "\n"


def encode_messages(messages: list[str], system_message: str) -> str:
    encoded = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_message}<|eot_id|>"
    for message in messages:
        encoded += f"<|start_header_id|>{message['role']}<|end_header_id|>\n\n{message['content'][0]['text']}<|eot_id|>"
    encoded += "<|start_header_id|>assistant<|end_header_id|>\n\n"
    return encoded


def llama_instruct(messages: list[str], system_message: str) -> str:
    resp = ""
    replicate_client
    for event in replicate_client.stream(
        "meta/meta-llama-3-70b-instruct",
        input={
            "prompt": encode_messages(messages, system_message),
            "temperature": 0,
            "seed": 42,
            "max_new_tokens": 4096,
            "prompt_template": r"{prompt}",
            "stop_sequences": "<|end_of_text|>,<|eot_id|>",
        },
    ):
        resp += str(event)
    return resp


def generate_patch(
    ctx_list: list[CodeHunk],
    stack_traces: list[str],
    git_diff: str,
    print_response: bool = False,
    model: str = "anthropic",  # [anthropic, deepseek, llama]
) -> tuple[str, Conversation]:
    assert model in ["anthropic", "deepseek", "llama"]

    prompt_eng = model_to_prompt_eng(model)
    system_msg = prompt_eng.system_msg(ctx_list)

    # Keep the last MAX_STACK_TRACE_LENGTH lines of each stack trace
    stack_trace_format = os.linesep.join(
        [
            os.linesep.join(
                trace.split("\n")
                if len(trace.split("\n")) <= MAX_STACK_TRACE_LENGTH
                else ["..."] + trace.split("\n")[-MAX_STACK_TRACE_LENGTH:]
            )
            for trace in stack_traces[:MAX_STACK_TRACES]
        ]
    )
    num_traces = len(stack_traces)
    if num_traces > MAX_STACK_TRACES:
        print(
            "Warning: Truncated stack traces to the "
            f"first {MAX_STACK_TRACES}/{num_traces} traces."
        )
    for i, trace in enumerate(stack_traces):
        num_lines = len(trace.split("\n"))
        if num_lines > MAX_STACK_TRACE_LENGTH:
            print(
                f"Warning: Truncated stack trace {i} to the "
                f"last {MAX_STACK_TRACE_LENGTH}/{num_lines} lines."
            )

    # Keep the first MAX_GIT_DIFF lines of the git diff
    diff_lines = git_diff.split("\n")
    num_lines = len(diff_lines)
    git_diff_format = git_diff
    if num_lines > MAX_GIT_DIFF:
        git_diff_format = os.linesep.join(diff_lines[:MAX_GIT_DIFF] + ["..."])
        print(
            "Warning: Truncated git diff to the first "
            f"{MAX_GIT_DIFF}/{num_lines} lines."
        )

    user_msg = prompt_eng.user_msg(stack_trace_format, git_diff_format)

    # First query to get the initial response
    messages = [{"role": "user", "content": [{"type": "text", "text": user_msg}]}]
    if model == "llama":
        explanation = llama_instruct(messages, system_msg)
    elif model == "anthropic":
        message = anthropic_client.messages.create(
            model="claude-3-5-sonnet-20240620",
            max_tokens=4096,
            temperature=0,
            system=system_msg,
            messages=messages,
        )
        explanation = message.content[0].text
    elif model == "deepseek":
        response = deepseek_client.chat.completions.create(
            model="deepseek-coder",
            messages=[
                {"role": "system", "content": system_msg},
                {"role": "user", "content": user_msg},
            ],
            temperature=0,
            stream=False,
        )
        explanation = response.choices[0].message.content

    if print_response:
        print("LLM response:")
        print(format_response(explanation))

    # Second query to get the final response
    messages.append(
        {
            "role": "assistant",
            "content": [{"type": "text", "text": explanation}],
        }
    )
    messages.append(
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": prompt_eng.user_msg_retry(),
                }
            ],
        }
    )
    if model == "llama":
        resp = llama_instruct(messages, system_msg)
    elif model == "anthropic":
        message = anthropic_client.messages.create(
            model="claude-3-5-sonnet-20240620",
            max_tokens=4096,
            temperature=0,
            system=system_msg,
            messages=messages,
        )
        resp = message.content[0].text
    elif model == "deepseek":
        openai_messages = [{"role": "system", "content": system_msg}]
        openai_messages.extend(
            [
                {"role": msg["role"], "content": msg["content"][0]["text"]}
                for msg in messages
            ]
        )
        response = deepseek_client.chat.completions.create(
            model="deepseek-coder",
            messages=openai_messages,
            temperature=0,
            stream=False,
        )
        resp = response.choices[0].message.content

    parsed_resp = parse_response(resp, ctx_list)
    llm_patch = change_to_patch(parsed_resp)
    return llm_patch, Conversation(
        user=user_msg, system=system_msg, explanation=explanation, response=resp
    )

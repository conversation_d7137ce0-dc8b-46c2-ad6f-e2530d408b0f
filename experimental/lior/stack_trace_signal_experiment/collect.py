import base64
import os
import click
import anyio
from aiohttp import ClientSession
import pandas as pd
from prompt_toolkit.shortcuts import radiolist_dialog
from job_run import get_job_runs
from commits import get_commits
from tqdm.asyncio import tqdm
from github.Workflow import Workflow
from pytest_log_parse import get_failures, get_pandas_tag, get_summary

with open(os.path.join(os.path.expanduser("~"), ".github_api"), "r") as f:
    TOKEN: str = f.read().strip()

GITHUB_API_URL = "https://api.github.com"

ARTIFACTS_DIR = "/tmp/artifacts"


async def fetch_workflows(session: ClientSession, repo_full_name: str) -> dict:
    async with session.get(f"/repos/{repo_full_name}/actions/workflows") as response:
        return await response.json()


async def fetch_repo(session: ClientSession, repo_full_name: str) -> dict:
    async with session.get(f"/repos/{repo_full_name}") as response:
        return await response.json()


async def select_workflow_interactively(workflows: list[Workflow]) -> tuple[str, str]:
    choices = [(workflow["node_id"], workflow["name"]) for workflow in workflows]
    id_to_name = {workflow["node_id"]: workflow["name"] for workflow in workflows}

    app = radiolist_dialog(
        title="Select a workflow:",
        text="Use arrow keys to navigate and Space to select:",
        values=choices,
    )
    selected_workflow = await app.run_async()
    return id_to_name[selected_workflow], selected_workflow


async def select_job_interactively(
    jobs_names: list[str], job_display_names: list[str]
) -> str:
    app = radiolist_dialog(
        title="Select a Job name:",
        text="Use arrow keys to navigate and Space to select:",
        values=list(zip(jobs_names, job_display_names)),
    )
    return await app.run_async()


def jobs_stats(jobs_df: pd.DataFrame):
    # assert jobs_df.groupby("run_id")["sha"].nunique().max() == 1, "Multiple commits per run"
    # assert jobs_df.groupby("sha")["run_id"].nunique().max() == 1, "Multiple runs per commit"

    n_commits = jobs_df["sha"].nunique()
    print(f"Number of commits: {n_commits}")

    events_stats = jobs_df.groupby("run_id")["event"].max().value_counts(normalize=True)
    events_stats_str = [f"'{k}': {v:.2%}" for k, v in events_stats.items()]
    print(f"run events - {', '.join(events_stats_str)}")

    run_fail_rate = jobs_df.groupby("run_id")["run_failure"].max().mean()
    print(f"Run failure rate: {run_fail_rate:.2%}")

    job_fail_rate = (
        jobs_df.groupby("job_name")["is_failed"]
        .agg(["sum", "count", "mean"])
        .sort_values("mean", ascending=False)
    )
    enough_runs = job_fail_rate["count"] > (
        job_fail_rate["count"].mean() - 1 * job_fail_rate["count"].std()
    )
    print(job_fail_rate[enough_runs].head(10).to_markdown())


def commits_stats(commits_df: pd.DataFrame):
    num_prs = commits_df["pr_number"].nunique()
    print(f"Number of PRs: {num_prs}")
    num_commits = commits_df["sha"].nunique()
    print(f"Number of commits: {num_commits}")

    invalid_pr = (
        (commits_df["branch"] == "main")
        | (commits_df["branch"] == "master")
        | (commits_df["branch"] == commits_df["base"])
    )
    print(
        f"Filtering {invalid_pr.sum()} commits ({invalid_pr.mean():.2%})"
        " from invalid PRs from main/master"
    )
    commits_df = commits_df[~invalid_pr]
    # assert commits_df.groupby("sha")["pr_number"].nunique().max() == 1, "Multiple prs per commit"

    num_commits_per_pr = commits_df.groupby("pr_number")["sha"].nunique()
    print(
        f"Number of commits per PR p50={int(num_commits_per_pr.median())}, mean={num_commits_per_pr.mean():.2f}"
    )


async def fetch_and_cache(
    func: callable, path: str, invalidate_cache: bool
) -> pd.DataFrame:
    base_dir = os.path.dirname(path)
    os.makedirs(base_dir, exist_ok=True)
    if os.path.exists(path) and not invalidate_cache:
        click.echo(f"Loading {path} from cache")
        return pd.read_parquet(path)
    else:
        data: pd.DataFrame = await func()
        data.to_parquet(path)
        return data


async def fetch_commit_data(
    merged_df: pd.DataFrame, full_name: str, token: str
) -> pd.DataFrame:
    # Fetch data for each commit
    async with ClientSession(
        headers={"Authorization": f"token {token}"},
    ) as commit_session:
        for row in tqdm(merged_df.itertuples(), total=len(merged_df)):
            async with commit_session.get(row.log_url) as response:
                merged_df.at[row.Index, "raw_log"] = await response.text()
            break_patch_url = f"https://github.com/{full_name}/compare/{row.first_sha}...{row.sha}.diff"
            fix_patch_url = f"https://github.com/{full_name}/compare/{row.sha}...{row.last_sha}.diff"

            async with commit_session.get(break_patch_url) as response:
                merged_df.at[row.Index, "breaking_change"] = await response.text()
                assert (
                    merged_df.at[row.Index, "breaking_change"] != ""
                ), f"No breaking change found ({break_patch_url}), pr_url = {row.pr_url}"
            async with commit_session.get(fix_patch_url) as response:
                merged_df.at[row.Index, "fix_change"] = await response.text()
                assert (
                    merged_df.at[row.Index, "fix_change"] != ""
                ), f"No fix change found ({fix_patch_url}), pr_url = {row.pr_url}"
    return merged_df


async def process_merged_df(merged_df: pd.DataFrame) -> pd.DataFrame:
    # Remove commits without a run
    commit_na_rate = merged_df.drop_duplicates("sha")["run_id"].isna().mean()
    click.echo(f"Commit NA rate: {commit_na_rate:.2%}")
    merged_df.dropna(inplace=True)

    # Remove re-runs
    merged_df.sort_values(
        ["sha", "job_name", "is_failed"], ascending=True, inplace=True
    )
    click.echo(
        f"Removing re-runs: {merged_df.duplicated(['sha', 'job_name']).mean():.2%}"
    )
    merged_df.drop_duplicates(["sha", "job_name"], keep="first", inplace=True)

    # Select job with the most failed commits
    job_names = sorted(merged_df["job_name"].unique().tolist())
    job_run_counts = merged_df.groupby("job_name")["sha"].nunique()
    job_fail_counts = merged_df.groupby("job_name")["is_failed"].sum().astype(int)
    job_display_names = [
        f"{job_name} ({job_fail_counts[job_name]}/{job_run_counts[job_name]})"
        for job_name in job_names
    ]

    job_name = await select_job_interactively(job_names, job_display_names)
    merged_df = merged_df[merged_df["job_name"] == job_name]
    num_commits = merged_df["sha"].nunique()
    num_prs = merged_df["pr_number"].nunique()
    click.echo(f"Focusing on: '{job_name}' ({num_commits} commits, {num_prs} PRs)")

    # Remove PRs where last commit failed
    last_commit_failed = merged_df[
        (merged_df["sha"] == merged_df["last_sha"]) & merged_df["is_failed"]
    ]["pr_number"].unique()
    num_prs = merged_df["pr_number"].nunique()
    click.echo(
        f"Removing {len(last_commit_failed)} PRs where last commit failed ({len(last_commit_failed)/num_prs:.2%})"
    )
    merged_df = merged_df[~merged_df["pr_number"].isin(last_commit_failed)]

    # Keep only failed commits
    failed_commits_rate = merged_df["is_failed"].mean()
    merged_df = merged_df[merged_df["is_failed"]]
    num_commits = merged_df["sha"].nunique()
    num_prs = merged_df["pr_number"].nunique()
    click.echo(
        f"Keeping only failed commits: {num_commits} commits, {num_prs} PRs ({failed_commits_rate:.2%})"
    )

    # One commit per PR, keep the first one
    click.echo(f"Keeping one commit per PR, removing {num_commits - num_prs} commits")
    merged_df.sort_values("commit_date", inplace=True, ascending=True)
    merged_df = merged_df.drop_duplicates("pr_number", keep="first")
    return merged_df


def process_log(merged_df: pd.DataFrame) -> pd.DataFrame:
    merged_df["summary"] = merged_df["raw_log"].map(get_summary)
    if merged_df["summary"].isna().any():
        for _, row in merged_df[merged_df["summary"].isna()].iterrows():
            click.echo(f"Failed to parse summary for {row.log_url}")
        merged_df.dropna(subset=["summary"], inplace=True)
    merged_df["failures"] = merged_df["raw_log"].map(get_failures)
    if merged_df["failures"].isna().any():
        for _, row in merged_df[merged_df["failures"].isna()].iterrows():
            click.echo(f"Failed to parse failures for {row.log_url}")
        merged_df.dropna(subset=["failures"], inplace=True)
    merged_df["pandas_tag"] = merged_df["raw_log"].map(get_pandas_tag)
    if merged_df["pandas_tag"].isna().any():
        for _, row in merged_df[merged_df["pandas_tag"].isna()].iterrows():
            click.echo(f"Failed to parse pandas tag for {row.log_url}")
        merged_df.dropna(subset=["pandas_tag"], inplace=True)
    return merged_df


async def fetch_merged_data(
    commits_df: pd.DataFrame, jobs_df: pd.DataFrame, full_name: str, token: str
) -> pd.DataFrame:
    merged_df = pd.merge(commits_df, jobs_df, on="sha", how="left")
    merged_df = await process_merged_df(merged_df)
    merged_df = await fetch_commit_data(merged_df, full_name, token)
    merged_df = process_log(merged_df)
    return merged_df


async def init_github(
    full_name: str, token: str, artifacts_dir: str, invalidate_cache: bool
):
    async with ClientSession(
        base_url=GITHUB_API_URL,
        headers={
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
        },
    ) as session:
        data = await fetch_workflows(session, full_name)
        workflows = data.get("workflows", [])
        workflow, workflow_id = await select_workflow_interactively(workflows)
        click.echo(f"Selected workflow: {workflow}")
        data = await fetch_repo(session, full_name)

        encoded_workflow = base64.b64encode(workflow.encode()).decode()
        encoded_name = base64.b64encode(full_name.encode()).decode()
        jobs_path: str = os.path.join(
            artifacts_dir, encoded_name, encoded_workflow, "jobs.parquet"
        )
        commits_path: str = os.path.join(
            artifacts_dir, encoded_name, encoded_workflow, "commits.parquet"
        )
        merged_path: str = os.path.join(
            artifacts_dir, encoded_name, encoded_workflow, "merged.parquet"
        )
        commits_df = await fetch_and_cache(
            lambda: get_commits(session, full_name),
            commits_path,
            invalidate_cache,
        )
        commits_stats(commits_df)
        jobs_df = await fetch_and_cache(
            lambda: get_job_runs(session, workflow_id, full_name),
            jobs_path,
            invalidate_cache,
        )
        jobs_stats(jobs_df)
        merged_df = await fetch_and_cache(  # noqa
            lambda: fetch_merged_data(commits_df, jobs_df, full_name, token),
            merged_path,
            invalidate_cache,
        )
        click.echo(f"Saving merged data to {merged_path}")


@click.command()
@click.option(
    "--full_name",
    type=str,
    help="The full name of the repository",
    required=True,
    default="pandas-dev/pandas",
)
@click.option(
    "--token",
    envvar="GITHUB_TOKEN",
    help="GitHub token (can also be set via environment variable GITHUB_TOKEN)",
    required=True,
    default=TOKEN,
)
@click.option(
    "--artifacts_dir",
    type=str,
    help="The directory to store artifacts",
    default=ARTIFACTS_DIR,
)
@click.option(
    "--invalidate_cache",
    is_flag=True,
    help="Invalidate cache and re-fetch data",
)
def main(full_name, token, artifacts_dir, invalidate_cache):
    anyio.run(init_github, full_name, token, artifacts_dir, invalidate_cache)


if __name__ == "__main__":
    main()

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e400fbc919e44e708b4cc55c757d4aa5", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch\n", "from transformers import AutoModelForCausalLM, AutoTokenizer\n", "\n", "torch.cuda.empty_cache()\n", "\n", "# Set random seed for reproducibility\n", "torch.random.manual_seed(0)\n", "\n", "# Load model and tokenizer\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    \"microsoft/Phi-3.5-mini-instruct\",\n", "    device_map=\"cuda\",\n", "    torch_dtype=\"auto\",\n", "    trust_remote_code=True,\n", "    attn_implementation=\"flash_attention_2\",\n", ")\n", "tokenizer = AutoTokenizer.from_pretrained(\"microsoft/Phi-3.5-mini-instruct\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "\n", "LOGS_DIR = \"/mnt/efs/spark-data/shared/pr_v2/failed_logs\"\n", "\n", "\n", "def sample_files(max_files: int = 20):\n", "    return (\n", "        pd.Series(os.listdir(LOGS_DIR)).sample(n=max_files, random_state=42).to_list()\n", "    )\n", "\n", "\n", "def load_data(files: list[str]):\n", "    sample_raw = pd.concat([pd.read_parquet(os.path.join(LOGS_DIR, f)) for f in files])\n", "\n", "    # Remove builds from the same PR (duplications)\n", "    sample_full = sample_raw.drop_duplicates(\n", "        [\"owner\", \"name\", \"pr_number\", \"check_run_name\"]\n", "    )\n", "\n", "    # Remove checks without logs\n", "    sample_full = sample_full[sample_full[\"log\"].str.len() > 0].reset_index()\n", "\n", "    print(f\"Got {sample_full.shape[0]} samples\")\n", "\n", "    # Remove timestamps as they are very token expensive\n", "    datetime_regex = r\"\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d+Z\"\n", "    sample_full[\"log\"] = (\n", "        sample_full[\"log\"].str.replace(datetime_regex, \"\", regex=True).str.strip()\n", "    )\n", "\n", "    # Remove all non ascii characters\n", "    sample_full[\"log\"] = (\n", "        sample_full[\"log\"].str.encode(\"ascii\", \"ignore\").str.decode(\"ascii\")\n", "    )\n", "\n", "    # Remove ANSI escape sequences\n", "    ansi_escape_regex = r\"\\x1B[@-_][0-?]*[ -/]*[@-~]\"\n", "    sample_full[\"log\"] = sample_full[\"log\"].str.replace(\n", "        ansi_escape_regex, \"\", regex=True\n", "    )\n", "\n", "    return sample_full\n", "\n", "\n", "def apply_manual_annotation(df: pd.DataFrame):\n", "    # Pytest\n", "    pydpf_core_filter = {\n", "        \"owner\": \"ansys\",\n", "        \"name\": \"pydpf-core\",\n", "        \"check_run_name\": \"tests / Tests (3.9, ubuntu-latest)\",\n", "    }\n", "\n", "    # vitest\n", "    appui_filter = {\n", "        \"owner\": \"itwin\",\n", "        \"name\": \"appui\",\n", "        \"check_run_name\": \"test (macos-latest)\",\n", "    }\n", "\n", "    # go test\n", "    bincapz_filter = {\n", "        \"owner\": \"chainguard-dev\",\n", "        \"name\": \"bincapz\",\n", "        \"check_run_name\": \"test\",\n", "    }\n", "\n", "    # sphinx-build (documentation compiler)\n", "    itr_filter = {\n", "        \"owner\": \"os-climate\",\n", "        \"name\": \"itr\",\n", "        \"check_run_name\": \"Rebuild documentation\",\n", "    }\n", "\n", "    # pytest\n", "    r2r_filter = {\"owner\": \"sciphi-ai\", \"name\": \"r2r\", \"check_run_name\": \"pytest\"}\n", "\n", "    def filter_repo(df, key_dict):\n", "        return (df[\"owner\"] == key_dict[\"owner\"]) & (df[\"name\"] == key_dict[\"name\"])\n", "\n", "    def filter_check(df, key_dict):\n", "        return filter_repo(df, key_dict) & (\n", "            df[\"check_run_name\"] == key_dict[\"check_run_name\"]\n", "        )\n", "\n", "    evalset = df[\n", "        filter_check(df, pydpf_core_filter)\n", "        | filter_check(df, appui_filter)\n", "        | filter_check(df, bincapz_filter)\n", "        | filter_check(df, itr_filter)\n", "        | filter_check(df, r2r_filter)\n", "    ].reset_index()\n", "\n", "    evalset[\"is_pytest\"] = filter_check(evalset, pydpf_core_filter) | filter_check(\n", "        evalset, r2r_filter\n", "    )\n", "    print(f\"Got {evalset['is_pytest'].mean():.2%} of eval samples are pytest\")\n", "    evalset[\"is_go_test\"] = filter_check(evalset, bincapz_filter)\n", "    print(f\"Got {evalset['is_go_test'].mean():.2%} of eval samples are go test\")\n", "    evalset[\"is_test\"] = (\n", "        filter_check(evalset, pydpf_core_filter)\n", "        | filter_check(evalset, pydpf_core_filter)\n", "        | filter_check(evalset, bincapz_filter)\n", "        | filter_check(evalset, r2r_filter)\n", "    )\n", "    print(f\"Got {evalset['is_test'].mean():.2%} of eval samples are testing framework\")\n", "\n", "    print(f\"Got {evalset.shape[0]} eval samples\")\n", "    return evalset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Got 8256 samples\n", "Got 680 samples\n", "Got 61.29% of eval samples are pytest\n", "Got 13.71% of eval samples are go test\n", "Got 75.00% of eval samples are testing framework\n", "Got 124 eval samples\n"]}], "source": ["files = sample_files(max_files=20)\n", "eval_files = [\"part-00000.parquet\", \"part-00001.parquet\", \"part-00002.parquet\"]\n", "sample_full = load_data(files)\n", "evalset = apply_manual_annotation(load_data(eval_files))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from tqdm.notebook import tqdm\n", "import numpy as np\n", "\n", "prefix_text = \"\"\"<|system|>You are an expert AI software engineer, capable of responding with only 'Yes' or 'No'<|end|><|user|>\n", "```\n", "\"\"\"\n", "suffix_text = \"\"\"\n", "```\n", "Does this log excerpt contains output of {type_class} command?<|end|><|assistant|>\"\"\"\n", "\n", "\n", "def _predict(log, type_class, max_log_tokens=5000):\n", "    # Tokenization\n", "    prefix = tokenizer(prefix_text, return_tensors=\"pt\").input_ids\n", "    suffix = tokenizer(\n", "        suffix_text.format(type_class=type_class), return_tensors=\"pt\"\n", "    ).input_ids\n", "\n", "    log_tokens = tokenizer(log, return_tensors=\"pt\", truncation=False).input_ids[\n", "        :, -max_log_tokens:\n", "    ]\n", "    truncated_logs = tokenizer.decode(log_tokens[0], skip_special_tokens=True)\n", "    lines_after_truncation = len(truncated_logs.split(\"\\n\"))\n", "\n", "    input_ids = torch.cat([prefix, log_tokens, suffix], dim=1)\n", "    with torch.no_grad():\n", "        logits = model(input_ids.cuda()).logits[:, -1, :].cpu()\n", "\n", "    probs = torch.softmax(logits, dim=-1)\n", "\n", "    # Extract probabilities for 'Yes' and 'No'\n", "    true_prob = probs[0, tokenizer.encode(\"Yes\")[0]].item()\n", "    false_prob = probs[0, tokenizer.encode(\"No\")[0]].item()\n", "    pos = true_prob / (true_prob + false_prob)\n", "\n", "    return pos, lines_after_truncation\n", "\n", "\n", "def predict(logs: pd.Series, type_class, max_log_tokens=10000):\n", "    proba, lines_after_truncation = zip(\n", "        *[_predict(log, type_class, max_log_tokens) for log in tqdm(logs)]\n", "    )\n", "\n", "    return pd.Series(proba, dtype=np.float32), pd.Series(\n", "        lines_after_truncation, dtype=np.int32\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from matplotlib import pyplot as plt\n", "from sklearn.metrics import roc_curve, accuracy_score, recall_score\n", "from sklearn.calibration import calibration_curve\n", "\n", "\n", "# Helper function to plot histograms\n", "def plot_histogram(data, title, bins=20):\n", "    data.plot.hist(bins=bins, title=title)\n", "    plt.show()\n", "\n", "\n", "# Helper function to filter dataset by minimum number of lines\n", "def filter_by_lines(data, min_lines):\n", "    num_filter = data[\n", "        (data[\"lines_after_truncation\"] < min_lines)\n", "        | (data[\"lines_after_truncation\"] == data[\"lines\"])\n", "    ].shape[0]\n", "    if num_filter > 0:\n", "        print(\n", "            f\"Filtered {num_filter}/{data.shape[0]} ({num_filter / data.shape[0]:.2%}) logs with less than {min_lines} lines\"\n", "        )\n", "    filtered_data = data[data[\"lines_after_truncation\"] >= min_lines]\n", "    return filtered_data\n", "\n", "\n", "# Helper function to assert data validity\n", "def assert_validity(data, y_col, min_lines):\n", "    assert data.shape[0] > 0, f\"No logs with more than {min_lines} lines\"\n", "    if y_col:\n", "        assert data[data[y_col]].shape[0] > 0, f\"No logs with {y_col} = True\"\n", "        assert data[~data[y_col]].shape[0] > 0, f\"No logs with {y_col} = False\"\n", "\n", "\n", "# Plot the ROC curve and find the best threshold\n", "def plot_roc_curve(proba, y):\n", "    fpr, tpr, thresholds = roc_curve(y, proba)\n", "    distances = np.sqrt((1 - tpr) ** 2 + fpr**2)\n", "    best_index = np.argmin(distances)\n", "    best_threshold = thresholds[best_index]\n", "\n", "    plt.figure(figsize=(5, 5))\n", "    plt.plot(fpr, tpr, label=\"ROC curve\")\n", "    plt.scatter(\n", "        fpr[best_index],\n", "        tpr[best_index],\n", "        color=\"red\",\n", "        label=f\"Best Threshold = {best_threshold:.3f}\",\n", "        zorder=5,\n", "    )\n", "    plt.xlabel(\"False Positive Rate\")\n", "    plt.ylabel(\"True Positive Rate\")\n", "    plt.title(\"ROC Curve\")\n", "    plt.show()\n", "\n", "    return best_threshold\n", "\n", "\n", "# Plot reliability curve\n", "def plot_reliability_curve(proba, y, n_bins=10):\n", "    fraction_of_positives, mean_predicted_value = calibration_curve(\n", "        y, proba, n_bins=n_bins\n", "    )\n", "    plt.figure(figsize=(5, 5))\n", "    plt.plot(\n", "        mean_predicted_value, fraction_of_positives, \"s-\", label=\"Reliability Curve\"\n", "    )\n", "    plt.plot([0, 1], [0, 1], color=\"gray\", linestyle=\"--\", label=\"Perfectly Calibrated\")\n", "    plt.xlabel(\"Mean Predicted Probability\")\n", "    plt.ylabel(\"Fraction of Positives\")\n", "    plt.title(\"Reliability Curve\")\n", "    plt.show()\n", "\n", "\n", "# Classify a sample and print the fraction of logs above threshold\n", "def classify(df, type_class, min_lines=100, sample_size=None, max_log_tokens=10000):\n", "    if sample_size is not None:\n", "        df = df.sample(sample_size, random_state=42).reset_index()\n", "    else:\n", "        df = df.copy()\n", "\n", "    df[\"lines\"] = df[\"log\"].str.split(\"\\n\").str.len()\n", "    df[\"proba\"], df[\"lines_after_truncation\"] = predict(\n", "        df[\"log\"], type_class, max_log_tokens\n", "    )\n", "\n", "    sample_filtered = filter_by_lines(df, min_lines)\n", "\n", "    plot_histogram(\n", "        sample_filtered[\"proba\"], f\"Probability of log containing {type_class} output\"\n", "    )\n", "    plot_histogram(\n", "        sample_filtered[\"lines_after_truncation\"],\n", "        \"Number of log lines after truncation\",\n", "    )\n", "\n", "    return sample_filtered\n", "\n", "\n", "# Evaluate classifier using classify_sample and return the best threshold\n", "def eval_classifier(evalset, type_class, y_col, min_lines=100, max_log_tokens=10000):\n", "    evalset_filtered = classify(\n", "        evalset, type_class, min_lines=min_lines, max_log_tokens=max_log_tokens\n", "    )\n", "    assert_validity(evalset_filtered, y_col=y_col, min_lines=min_lines)\n", "\n", "    t = plot_roc_curve(evalset_filtered[\"proba\"], evalset_filtered[y_col])\n", "    plot_reliability_curve(evalset_filtered[\"proba\"], evalset_filtered[y_col])\n", "\n", "    y = evalset_filtered[y_col]\n", "    y_pred = evalset_filtered[\"proba\"] > 0.5\n", "    y_pred_opt = evalset_filtered[\"proba\"] > t\n", "\n", "    print(f\"Accuracy (threshold=0.5): {accuracy_score(y, y_pred):.2%}\")\n", "    print(f\"Recall (threshold=0.5): {recall_score(y, y_pred):.2%}\")\n", "    print(f\"Accuracy (threshold={t:.6f}): {accuracy_score(y, y_pred_opt):.2%}\")\n", "    print(f\"Recall (threshold={t:.6f}): {recall_score(y, y_pred_opt):.2%}\")\n", "    return t\n", "\n", "\n", "def extract(log, type_class, chunk_size=30, threshold=0.5, max_log_tokens=5000):\n", "    log_lines = log.split(\"\\n\")\n", "    chunks = pd.Series(\n", "        [\n", "            \"\\n\".join(log_lines[i : i + chunk_size])\n", "            for i in range(0, len(log_lines), chunk_size)\n", "        ]\n", "    )\n", "    proba, _ = predict(chunks, type_class, max_log_tokens=max_log_tokens)\n", "\n", "    # Extract longest contiguous sequence of chunks above threshold\n", "    mask = proba.to_numpy() > threshold\n", "    # first_true, last_true = np.argmax(mask), len(mask) - np.argmax(mask[::-1]) - 1\n", "    # mask = np.zeros_like(mask, dtype=bool)\n", "    # mask[first_true: last_true + 1] = True\n", "\n", "    return \"\\n\".join(chunks[mask].to_list())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e69d57b15a1f4f46b27481367c5412c2", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/124 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (248715 > 131072). Running this sequence through the model will result in indexing errors\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Filtered 5/124 (4.03%) logs with less than 100 lines\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAcoAAAHWCAYAAAD3iMk8AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/TGe4hAAAACXBIWXMAAA9hAAAPYQGoP6dpAABhcklEQVR4nO3deVxU5f4H8M8MMDMssiigiCiIu5hrmltulJr7iivobbmWVlev3bQy9bbovZXZr0zLUnAL1MxMzQ1F09xyy11UVFzYXNhhYOb5/cHlJLLI4MycWT7v14vXy3nmzJnvHJAPz3Oe8xyFEEKAiIiIyqSUuwAiIiJLxqAkIiKqAIOSiIioAgxKIiKiCjAoiYiIKsCgJCIiqgCDkoiIqAIMSiIiogowKImIiCrAoCR6QnFxcVAoFIiLi5PaJkyYgMDAwCrtT6FQYMqUKY/dLjIyEgqFAteuXZPaunfvju7du0uPr127BoVCgcjIyCrVQkQMSrIzxeFS/OXo6Ah/f39MmDABt27dkrs8s9i6dSvmzJljkn3rdDosX74c3bt3R/Xq1aFWqxEYGIiJEyfijz/+MMl7Epmao9wFEMnh3//+N4KCgpCXl4dDhw4hMjIS+/fvx5kzZ6DRaJ54/0uXLoVerzdCpeUbP348Ro0aBbVaXe429erVQ25uLpycnKS2rVu3YtGiRUYPy9zcXAwdOhTbtm3Ds88+i3feeQfVq1fHtWvXsHbtWkRFReHGjRuoU6eOUd+XyNQYlGSX+vbti3bt2gEAXnrpJXh7e+M///kPNm3ahJEjRz7x/h8OJlNxcHCAg4NDhdsoFAqjBH9lvPXWW9i2bRs+//xz/OMf/yjx3OzZs/H5558b5X30ej20Wq3ZPhcRh16JAHTt2hUAcOXKlRLtFy5cwPDhw1G9enVoNBq0a9cOmzZteuz+yjpH+emnn6JTp06oUaMGnJ2d0bZtW6xfv77cfaxevRqNGzeGRqNB27ZtsW/fvhLPl3WO8lGPnqOcMGECFi1aBAAlhqCFEAgMDMSgQYNK7SMvLw8eHh74+9//Xu773Lx5E9988w2ee+65UiEJFIX69OnTpd5keedw58yZA4VCUaKt+Jzt6tWr0bx5c6jVavzyyy+oXr06Jk6cWGofGRkZ0Gg0mD59utSWn5+P2bNno0GDBlCr1QgICMC//vUv5Ofnl/uZiIqxR0kESGHj5eUltZ09exadO3eGv78/ZsyYAVdXV6xduxaDBw/Gjz/+iCFDhhj0Hl988QUGDhyIsWPHQqvVIjo6GiNGjMDmzZvRr1+/Etvu3bsXMTExeOONN6BWq/H111+jT58+OHLkCEJCQqr8Of/+97/j9u3b2LlzJ1auXCm1KxQKjBs3Dv/9739x7949VK9eXXrul19+QUZGBsaNG1fufn/99VcUFhZi/PjxVa6tIrt378batWsxZcoUeHt7o2HDhhgyZAg2bNiAb775BiqVStp248aNyM/Px6hRowAU9UAHDhyI/fv345VXXkHTpk1x+vRpfP7557h06RI2btxokprJhggiO7J8+XIBQOzatUukpqaKxMREsX79euHj4yPUarVITEyUtu3Vq5do0aKFyMvLk9r0er3o1KmTaNiwodS2Z88eAUDs2bNHaouIiBD16tUr8d45OTklHmu1WhESEiJ69uxZoh2AACD++OMPqe369etCo9GIIUOGlPosCQkJUlu3bt1Et27dpMcJCQkCgFi+fLnUNnnyZFHWf/2LFy8KAGLx4sUl2gcOHCgCAwOFXq8v9ZpiU6dOFQDEiRMnyt3mYWUdHyGEmD17dqnaAAilUinOnj1bon379u0CgPjll19KtL/wwguifv360uOVK1cKpVIpfvvttxLbLVmyRAAQBw4cqFTNZL849Ep2KTQ0FD4+PggICMDw4cPh6uqKTZs2SUOD9+7dw+7duzFy5EhkZmYiLS0NaWlpuHv3Lnr37o34+HiDZ8k6OztL/75//z7S09PRtWtXHD9+vNS2HTt2RNu2baXHdevWxaBBg7B9+3bodLoqfuqKNWrUCB06dMDq1aultnv37uHXX3/F2LFjSw2JPiwjIwMAUK1aNZPU1q1bNzRr1qxEW8+ePeHt7Y2YmBip7f79+9i5cyfCwsKktnXr1qFp06Zo0qSJ9H1MS0tDz549AQB79uwxSc1kOzj0SnZp0aJFaNSoEdLT07Fs2TLs27evxOzRy5cvQwiBWbNmYdasWWXuIyUlBf7+/pV+z82bN+PDDz/EyZMnS5wbKyuAGjZsWKqtUaNGyMnJQWpqKmrVqlXp9zVEeHg4pkyZguvXr6NevXpYt24dCgoKHjuk6u7uDgDIzMw0SV1BQUGl2hwdHTFs2DCsWbMG+fn5UKvV2LBhAwoKCkoEZXx8PM6fPw8fH58y952SkmKSmsl2MCjJLrVv316a9Tp48GB06dIFY8aMwcWLF+Hm5iZd2jF9+nT07t27zH00aNCg0u/322+/YeDAgXj22Wfx9ddfw8/PD05OTli+fDnWrFnz5B/ISEaNGoWpU6di9erVeOedd7Bq1Sq0a9cOjRs3rvB1TZo0AQCcPn0arVq1euz7lNc7La+3/HBv/NF6v/nmG/z6668YPHgw1q5diyZNmqBly5bSNnq9Hi1atMCCBQvK3EdAQMBj6yX7xqAku+fg4IB58+ahR48e+OqrrzBjxgzUr18fQNFlHqGhoU/8Hj/++CM0Gg22b99eoue6fPnyMrePj48v1Xbp0iW4uLiU2zOqrIqGUKtXr45+/fph9erVGDt2LA4cOICFCxc+dp99+/aFg4MDVq1aVakJPV5eXnjw4EGp9uvXrz/2tQ979tln4efnh5iYGHTp0gW7d+/Gu+++W2Kb4OBgnDp1Cr169arwsxOVh+coiVC09Fv79u2xcOFC5OXlwdfXF927d8c333yDO3fulNo+NTXVoP07ODhAoVCU6DFdu3at3BmXBw8eLHHuMjExET///DOef/75x147+Tiurq4AUGZQAUULGZw7dw5vvfUWHBwcpNmjFQkICMDLL7+MHTt24Msvvyz1vF6vx2effYabN28CKAqv9PR0/Pnnn9I2d+7cwU8//WTQZ1EqlRg+fDh++eUXrFy5EoWFhSWGXQFg5MiRuHXrFpYuXVrq9bm5ucjOzjboPcn+sEdJ9D9vvfUWRowYgcjISEyaNAmLFi1Cly5d0KJFC7z88suoX78+kpOTcfDgQdy8eROnTp2q9L779euHBQsWoE+fPhgzZgxSUlKwaNEiNGjQoERYFAsJCUHv3r1LXB4CAHPnzn3iz1k8SeiNN95A7969S4Vhv379UKNGDaxbtw59+/aFr69vpfb72Wef4cqVK3jjjTewYcMG9O/fH15eXrhx4wbWrVuHCxcuSO8zatQovP322xgyZAjeeOMN5OTkYPHixWjUqFGZk5sqEhYWhi+//BKzZ89GixYt0LRp0xLPjx8/HmvXrsWkSZOwZ88edO7cGTqdDhcuXMDatWuxfft2aRieqExyT7slMqfiSyqOHj1a6jmdTieCg4NFcHCwKCwsFEIIceXKFREeHi5q1aolnJychL+/v+jfv79Yv3699LrKXh7y/fffi4YNGwq1Wi2aNGkili9fXu7lEJMnTxarVq2Stm/dunWJ/T/8WQy9PKSwsFC8/vrrwsfHRygUijIvFXnttdcEALFmzZpyjmTZCgsLxXfffSe6du0qPDw8hJOTk6hXr56YOHFiqUtHduzYIUJCQoRKpRKNGzcWq1atqvB4lEev14uAgAABQHz44YdlbqPVasV//vMf0bx5c6FWq4WXl5do27atmDt3rkhPTzfoM5L9UQghhEwZTUQWaurUqfj++++RlJQEFxcXucshkhXPURJRCXl5eVi1ahWGDRvGkCQCz1ES0f+kpKRg165dWL9+Pe7evYs333xT7pKILAKDkogAAOfOncPYsWPh6+uL//u//6vU9ZBE9oDnKImIiCrAc5REREQVYFASERFVwO7OUer1ety+fRvVqlXjclZERHZMCIHMzEzUrl0bSmX5/Ua7C8rbt29zEWQiIpIkJiZKt9gri90FZfH98hITE6VbAxERkf3JyMhAQEDAY++jandBWTzc6u7uzqAkIqLHnobjZB4iIqIKMCiJiIgqwKAkIiKqAIOSiIioAgxKIiKiCjAoiYiIKsCgJCIiqgCDkoiIqAIMSiIiogrY3co89ujWg1zcz9aW+7yXqwr+ns5mrMhwlvoZLLWusrDWJ2epdT3KWuosj6XVL2tQ7tu3D5988gmOHTuGO3fu4KeffsLgwYMrfE1cXBymTZuGs2fPIiAgAO+99x4mTJhglnqt0a0Huej5aRzyC/XlbqN2VGL39O4W+x/HUj+DpdZVFtZqu3U9ylrqLI8l1i/r0Gt2djZatmyJRYsWVWr7hIQE9OvXDz169MDJkyfxj3/8Ay+99BK2b99u4kqt1/1sbYU/cACQX6iv8K83uVnqZ7DUusrCWp+cpdb1KGupszyWWL+sPcq+ffuib9++ld5+yZIlCAoKwmeffQYAaNq0Kfbv34/PP/8cvXv3NlWZRERkx6zqHOXBgwcRGhpaoq137974xz/+Ue5r8vPzkZ+fLz3OyMgwVXlWbcqa49A4OchdRpnyCnSV2s7cn8FS6yoLa31yllrXo6ylzvJUtn5zsqqgTEpKQs2aNUu01axZExkZGcjNzYWzc+nx6nnz5mHu3LnmKtFqXbubI3cJT8xSP4Ol1lUW1vrkLLWuR1lLnZbAqoKyKmbOnIlp06ZJj4tv1EklfTAoBEHernKXUaaEtGzM+vnMY7cz92ew1LrKwlqfnKXW9ShrqbM85dcv4KbQIkuozV6TVQVlrVq1kJycXKItOTkZ7u7uZfYmAUCtVkOtNv+BtTat63oixN9D7jLK5OniVKntzP0ZLLWusrDWJ2epdT3KWuosT9n1C3Ryuo5Ah/vYnt8Id4V5A96qFhzo2LEjYmNjS7Tt3LkTHTt2lKkiIiIyLYHOTtfR2DENTtDBXZn/+JcYmaxBmZWVhZMnT+LkyZMAii7/OHnyJG7cuAGgaNg0PDxc2n7SpEm4evUq/vWvf+HChQv4+uuvsXbtWkydOlWO8q2Cl6sKaseKv81qRyW8XFVmqshwlvoZLLWusrDWJ2epdT3KWuosT8n6i0KykWMa9AL4rSAICbrqZq9fIYQQZnu3R8TFxaFHjx6l2iMiIhAZGYkJEybg2rVriIuLK/GaqVOn4ty5c6hTpw5mzZpl0IIDGRkZ8PDwQHp6Otzd3Y3wKSxf8SoXi+MuY8vpJAxt44+/dQ6Snrf0VToAy1upo5il1lUW1vrkLLWuR1lLneW59SAX9zLzcOy3Xbgefw5QKNC+ex8EBDcGYLz6K5sHsgalHOwxKIuN/vYQDl69i09HtMTwtnXkLoeIqEx6vR6bNm3CqVOnoFAoMHToUISEhBj9fSqbB1Y1mYeezOXULABAQ183mSshIiqfXq9HZmYmFAoFhg0bhubNm8taD4PSTjzI0SI1s+gkeDCDkogsmKOjI0aNGoWbN28iKCjo8S8wMaua9UpVdzmlqDdZ20MDNzX/PiIiy6LX63HmzBkUnw10cnKyiJAE2KO0G8VByd4kEVkavV6Pn376CWfOnEFKSgp69uwpd0klMCjtRHxK8fnJajJXQkT0F71ejw0bNuDs2bNQKpWoXbu23CWVwqFXO1Hco2xYkz1KIrIMj4bkiBEj0KRJE7nLKoU9SjtRHJQNOPRKRBZAp9Nhw4YNOHfuHJRKJUaOHInGjRvLXVaZGJR2IDu/ELce5AIAGvgwKIlIXkIIKSQdHBwwcuRINGrUSO6yysWhVztw5X/XT3q7qSx22Soish8KhQLBwcFwdHS0+JAE2KO0C/HJHHYlIsvSpk0bNGjQwCpWSGOP0g4Ur8jDoCQiueh0Omzfvh3Z2dlSmzWEJMCgtAvFPUpeGkJEctDpdFi3bh0OHTqENWvWwNqWGOfQqx24wjVeiUgmhYWFWLduHS5dugRHR0f07NkTCoVC7rIMwqC0cXkFOly/WzTUwaFXIjKnwsJCrF27FvHx8dL6rcHBwXKXZTAGpY1LSMuGXgDuGkf4VFPLXQ4R2YlHQ3L06NGoX7++3GVVCYPSxj280IC1DXcQkfX69ddfpZAcM2aMxSxwXhUMShvHNV6JSA5du3ZFYmIi+vbta9UhCTAobd4VrvFKRGYihJBGrjw9PTFp0iQoldZ/cYX1fwKqUHxKJgDeXouITKugoAA//PADzp8/L7XZQkgC7FHatEKdHglpRTNeeWkIEZlKQUEBoqOjcfXqVSQmJiIoKAgajUbusoyGQWnDrt/LQYFOwNnJAbU9nOUuh4hsUHFPMiEhAU5OThg1apRNhSTAoLRpD6/xqlRyxisRGZdWq8UPP/yAa9euQaVSYezYsahbt67cZRmdbQwgU5m4Ig8RmYq9hCTAHqVNi0/mRB4iMo1jx45JITlu3DgEBATIXZLJMCht2F/XUDIoici4nnnmGaSnp6N58+Y2HZIAg9Jm6fVCGnrlGq9EZAxarRYODg5wcHCAQqFAnz595C7JLHiO0kbdepCLvAI9VA5K1K3uInc5RGTl8vPzsXr1amzYsAE6nU7ucsyKPUobVbzGa5C3Kxwd+PcQEVVdcUgmJiZCrVbj/v378Pb2lrsss2FQ2qjiFXkacOk6InoCD4ekRqPB+PHj7SokAQalzbrMiTxE9ITy8vKwevVq3Lx5UwrJ2rVry12W2TEobVR8CifyEFHV5eXlYdWqVbh16xY0Gg3Cw8Ph5+cnd1myYFDaICEELifz9lpEVHWpqalITk6Gs7Mzxo8fb7chCTAobVJKZj4y8wuhVACB3pzxSkSGCwgIwOjRo+Hi4oJatWrJXY6sGJQ2qHiN18AarlA7OshcDRFZi9zcXGRnZ0uTderXry9zRZaB1w3YoMvFM155fpKIKik3NxcrV65EZGQkUlNT5S7HojAobRAn8hCRIXJzc7FixQrcuXMHQggIIeQuyaJw6NUGSWu88hpKInqMnJwcrFy5EklJSXB1dUV4eDh8fX3lLsuiMCht0JXiHqUPZ7wSUfkeDcmIiAj4+PjIXZbFYVDamHvZWtzN1gIAgn1dZa6GiCxVTk4OVqxYgeTkZIbkYzAobUzxijx1vJzhouK3l4jKplQq4ejoCDc3N0RERNjdsnSG4G9SGxPPGa9EVAkajQbjxo1DdnY2atSoIXc5Fo2zXm1MfDLXeCWismVnZ+PEiRPSY41Gw5CsBPYobQxv1kxEZcnKysKKFSuQmpoKnU6Hdu3ayV2S1WBQ2pjiHmUDrvFKRP+TlZWFqKgopKWloVq1aggKCpK7JKvCoLQhmXkFSMrIA8AeJREVyczMxIoVK6SQjIiI4HCrgRiUNqR4xqtvNTU8nJ1kroaI5JaZmYmoqCjcvXsX7u7uiIiIQPXq1eUuy+pwMo8N4Yo8RFSsoKCAIWkkDEob8teKPAxKInvn5OSE1q1bw8PDAxMmTGBIPgEOvdoQaTH0mpzIQ0RA586d0bZtW2g0GrlLsWrsUdqQy+xREtm1jIwMbNiwAfn5+VIbQ/LJsUdpI/IKdEi8nwOA5yiJ7FF6ejqioqJw//596PV6DB8+XO6SbAZ7lDbiSmoWhAC8XJxQw1UldzlEZEYPh6SnpydCQ0PlLsmmsEdpIy4/dLNmhUIhczVEZC4PHjxAVFQUHjx4AC8vL0RERMDDw0PusmwKe5Q2givyENkfhqR5sEdpIx7uURKR7RNC4Mcff5RCcsKECXB3d5e7LJvEHqWNKL69Fu8aQmQfFAoFBg4ciLp16zIkTYw9ShugLdTj+l3OeCWyB3q9HkplUR/Hx8cHEyZM4LwEE2OP0gZcv5uNQr2Am9oRtdx5zRSRrbp//z6+/vprJCQkSG0MSdNjUNqA4hV5gjnjlchm3bt3D5GRkbh79y527twJIYTcJdkN2YNy0aJFCAwMhEajQYcOHXDkyJEKt1+4cCEaN24MZ2dnBAQEYOrUqcjLyzNTtZaJK/IQ2bZ79+4hKioKGRkZ8Pb2xpgxY/hHsRnJGpQxMTGYNm0aZs+ejePHj6Nly5bo3bs3UlJSytx+zZo1mDFjBmbPno3z58/j+++/R0xMDN555x0zV25ZeNcQItt19+5dREZGSiEZEREBNzf+XzcnWYNywYIFePnllzFx4kQ0a9YMS5YsgYuLC5YtW1bm9r///js6d+6MMWPGIDAwEM8//zxGjx792F6orWOPksg23b17F1FRUcjMzISPjw9DUiayBaVWq8WxY8dKLLWkVCoRGhqKgwcPlvmaTp064dixY1IwXr16FVu3bsULL7xQ7vvk5+cjIyOjxJct0ekFrqSyR0lkiw4dOsSQtACyXR6SlpYGnU6HmjVrlmivWbMmLly4UOZrxowZg7S0NHTp0gVCCBQWFmLSpEkVDr3OmzcPc+fONWrtliTxXg60hXqoHZWo4+UidzlEZER9+vSBSqVCp06d4OrqKnc5dkv2yTyGiIuLw8cff4yvv/4ax48fx4YNG7BlyxZ88MEH5b5m5syZSE9Pl74SExPNWLHpFQ+71vdxg4OSJ/eJrF1GRoY0o9XBwQHPPfccQ1JmsvUovb294eDggOTk5BLtycnJqFWrVpmvmTVrFsaPH4+XXnoJANCiRQtkZ2fjlVdewbvvvitdhPswtVoNtVpt/A9gIaSJPFyRh8jqpaamIioqCo0bN0b//v05s9VCyNajVKlUaNu2LWJjY6U2vV6P2NhYdOzYsczX5OTklApDBwcHALDba4q4xiuRbSgOyezsbNy6dQtarVbukuh/ZF3Cbtq0aYiIiEC7du3Qvn17LFy4ENnZ2Zg4cSIAIDw8HP7+/pg3bx4AYMCAAViwYAFat26NDh064PLly5g1axYGDBggBaa9ucw1XomsXkpKClasWIHs7GzUqlUL48ePt+mRMGsja1CGhYUhNTUV77//PpKSktCqVSts27ZNmuBz48aNEj3I9957DwqFAu+99x5u3boFHx8fDBgwAB999JFcH0FWQgipR8kZr0TWKSUlBVFRUcjJyUGtWrUQHh4OZ2dnucuihyiEnY1ZZmRkwMPDA+np6Va/2v7tB7noNH83HJUKnP+gD5wcrGpuFpHdS05OxooVK5CTkwM/Pz+MHz+eIWlGlc0D/ma1YsUTeQK9XRmSRFbo/v37yMvLY0haON5my4pxRR4i69akSROMGTMGtWvXZkhaMAalFZMm8vD8JJHVSEpKgkajgaenJwAgODhY3oLosTheZ8V4aQiRdblz5w6ioqIQFRWF9PR0ucuhSmJQWikhBC4lMyiJrMXt27exYsUK5OXlwc3NjZd/WBEOvVqptCwt0nMLoFAAwTxHSWTRbt++jZUrVyIvLw916tTBuHHjGJRWhEFppYqHXQO8XKBxss/FFoiswa1bt7Bq1Srk5eUhICAAY8eOZUhaGQalleKKPESW786dO1i5ciXy8/MZklaMQWmlOJGHyPK5u7vD3d0dzs7OGDNmDEPSSjEorVQ8g5LI4rm6uiIiIgJOTk5QqVRyl0NVxFmvVkq6vVbNajJXQkQPS0xMxMmTJ6XHrq6uDEkrxx6lFUrPKUBqZj4AINiHN3QlshSJiYlYtWoVtFotXFxc0KhRI7lLIiNgUFqhy6lFE3n8PDSopnGSuRoiAorudrR69WpotVoEBgYiMDBQ7pLISBiUVogTeYgsy8MhGRQUhNGjR8PJiX/E2goGpRWK54o8RBbj+vXrWL16NQoKChiSNopBaYWkiTy+nMhDJKf79+9LIVm/fn2MGjWKIWmDGJRWiEOvRJbB09MTTz/9NJKTkxEWFsaQtFEMSiuTnV+IWw9yAXBVHiK5KRQKhIaGQq/Xw8GBS0naKl5HaWWupmYDAGq4quDlymuziMwtISEB0dHRKCgoAFAUlgxJ28agtDLx/1vjlcOuROZ39epVrFmzBhcvXsSBAwfkLofMhEFpZXh+kkgeV69exQ8//IDCwkI0bNgQXbp0kbskMhOeo7Qyf814ZVASmcuVK1cQHR2NwsJCNGrUCCNGjICjI3992gt+p63MZa7xSmRWly9fRnR0NHQ6HUPSTvG7bUXyC3W4frdoMg+HXolMr6CgABs3boROp0Pjxo0xYsQITtyxQwxKK5KQlg29AKppHOFbjfe1IzI1JycnjB49GkePHsWAAQMYknaKQWlFHp7Io1AoZK6GyHZptVrp1lj+/v7w9/eXuSKSE2e9WpHiNV45kYfIdC5duoT/+7//w61bt+QuhSwEg9KKXOYar0QmdfHiRcTExCA7Oxt//PGH3OWQhWBQWhFeQ0lkOhcvXsTatWuh1+vRrFkz9O/fX+6SyELwHKWVKNTpcTWNQUlkChcuXMC6deug1+vRvHlzDB06FEol+xFUhEFpJW7cy0GBTsDZyQH+ns5yl0NkM86fP4/169dDr9cjJCQEQ4YMYUhSCQb/NGzbtg379++XHi9atAitWrXCmDFjcP/+faMWR38pXpEn2NcVSiVnvBIZgxACJ06cYEhShQz+iXjrrbeQkZEBADh9+jT++c9/4oUXXkBCQgKmTZtm9AKpCCfyEBmfQqHAiBEjEBoaypCkchk89JqQkIBmzZoBAH788Uf0798fH3/8MY4fP44XXnjB6AVSEU7kITKe5ORk+Pr6QqFQwMnJCZ07d5a7JLJgBv/5pFKpkJOTAwDYtWsXnn/+eQBA9erVpZ4mGR9vr0VkHGfPnsU333yD2NhYCCHkLoesgME9yi5dumDatGno3Lkzjhw5gpiYGABFF+nWqVPH6AUSoNcLXEnhGq9ET+rMmTPYsGEDhBDIysqSuxyyEgb3KL/66is4Ojpi/fr1WLx4sbS006+//oo+ffoYvUACbj3IRW6BDk4OCtSr7iJ3OURW6fTp01JItmrVCgMHDuRSkFQpBvco69ati82bN5dq//zzz41SEJVWfH4yyNsVjg6cbEBkqNOnT+Onn35iSFKVVOm37pUrV/Dee+9h9OjRSElJAVDUozx79qxRi6MinPFKVHV//vmnFJKtW7dmSJLBDA7KvXv3okWLFjh8+DA2bNggjfOfOnUKs2fPNnqBxIk8RE9Cp9NJITlgwACGJBnM4KHXGTNm4MMPP8S0adNQrdpfPZyePXviq6++MmpxVISXhhBVXevWrVG9enXUrVuXIUlVYnCP8vTp0xgyZEipdl9fX6SlpRmlKPqLEEJaladhTQYlUWWcO3cO2dnZ0uN69eoxJKnKDA5KT09P3Llzp1T7iRMneHNTE0jNzEdmXiGUiqLJPERUsRMnTmDdunWIiopCXl6e3OWQDTA4KEeNGoW3334bSUlJUCgU0Ov1OHDgAKZPn47w8HBT1GjXinuT9Wq4Qu3oIHM1RJbt+PHj2LRpEwAgMDAQarVa5orIFhgclB9//DGaNGmCgIAAZGVloVmzZnj22WfRqVMnvPfee6ao0a7FJ3MiD1FlHDt2DL/88gsAoH379ujbty+HW8koDJ7Mo1KpsHTpUsyaNQtnzpxBVlYWWrdujYYNG5qiPrt3OZUTeYge59ixY9L13R06dEDv3r0ZkmQ0Bgfl/v370aVLF9StWxd169Y1RU30kPjk4msoGZREZfnzzz8ZkmRSBg+99uzZE0FBQXjnnXdw7tw5U9RED7nCHiVRherWrQsPDw8888wzDEkyCYOD8vbt2/jnP/+JvXv3IiQkBK1atcInn3yCmzdvmqI+u3YvW4u0LC0AINiHQUlUFk9PT7zyyit4/vnnGZJkEgYHpbe3N6ZMmYIDBw7gypUrGDFiBKKiohAYGIiePXuaoka7VbzQgL+nM1zVBo+SE9msI0eO4Pz589JjFxcXhiSZzBOtsB0UFIQZM2Zg/vz5aNGiBfbu3WusughckYeoLIcPH8avv/6K9evXS2tNE5lSlYPywIEDeO211+Dn54cxY8YgJCQEW7ZsMWZtdq94jVdO5CEqcujQIWzbtg0A0LFjR/j4+MhcEdkDg8fzZs6ciejoaNy+fRvPPfccvvjiCwwaNAguLrxPorGxR0n0l4MHD2LHjh0Aim4g37NnTw63klkYHJT79u3DW2+9hZEjR8Lb29sUNdH/XOYar0QASoZk165d0aNHD4YkmY3BQXngwAFT1EGPyMwrwJ30onUqG/jwPpRkv65cuSKF5LPPPovu3bszJMmsKhWUmzZtQt++feHk5CSto1iegQMHGqUwe3cltejOBz7V1PBwcZK5GiL51K9fH61bt4a7uzu6d+8udzlkhyoVlIMHD0ZSUhJ8fX0xePDgcrdTKBTQ6XTGqs2uFa/xyok8ZK+EEFAoFFAoFLzhMsmqUkGp1+vL/DeZDtd4JXv222+/ISkpCUOHDoWDgwNDkmRl8OUhK1asQH5+fql2rVaLFStWGKUoAi5zjVeyU7/99ht2796Nc+fO4eLFi3KXQ2R4UE6cOBHp6eml2jMzMzFx4kSDC1i0aBECAwOh0WjQoUMHHDlypMLtHzx4gMmTJ8PPzw9qtRqNGjXC1q1bDX5fS1d8H8pgBiXZkX379mH37t0AgB49eqBZs2YyV0RUhVmvxecNHnXz5k14eHgYtK+YmBhMmzYNS5YsQYcOHbBw4UL07t0bFy9ehK+vb6nttVotnnvuOfj6+mL9+vXw9/fH9evX4enpaejHsGh5BTok3s8BADT05YxXsg979+5FXFwcgKKbL3Tt2lXegoj+p9JB2bp1a+nEeq9eveDo+NdLdTodEhIS0KdPH4PefMGCBXj55ZelnuiSJUuwZcsWLFu2DDNmzCi1/bJly3Dv3j38/vvvcHIqmgkaGBho0HtagyupWRAC8HRxgrebSu5yiEwuLi5OWgKzV69e6NKli8wVEf2l0kFZPNv15MmT6N27N9zc/hoSVKlUCAwMxLBhwyr9xlqtFseOHcPMmTOlNqVSidDQUBw8eLDM12zatAkdO3bE5MmT8fPPP8PHxwdjxozB22+/DQcHhzJfk5+fX+KcakZGRqVrlIu0Io+PGycxkM27f/++dH02Q5IsUaWDcvbs2QCKenBhYWHQaDRP9MZpaWnQ6XSoWbNmifaaNWviwoULZb7m6tWr2L17N8aOHYutW7fi8uXLeO2111BQUCDV96h58+Zh7ty5T1SruXFFHrInXl5eGD16NJKTk9GxY0e5yyEqxeDJPBEREU8cklWl1+vh6+uLb7/9Fm3btkVYWBjeffddLFmypNzXzJw5E+np6dJXYmKiGSuumuKg5D0oyVYJIZCdnS09rl+/PkOSLFalepTVq1fHpUuX4O3tDS8vrwqHA+/du1epN/b29oaDgwOSk5NLtCcnJ6NWrVplvsbPzw9OTk4lhlmbNm2KpKQkaLVaqFSlz+ep1Wqo1epK1WQp4qUeJSfykO0RQmDPnj04fvw4IiIieAcQsniVCsrPP/8c1apVk/5tjPNmKpUKbdu2RWxsrHT+U6/XIzY2FlOmTCnzNZ07d8aaNWug1+uhVBZ1hi9dugQ/P78yQ9IaFej0uJZW9Jc2r6EkWyOEwO7du7F//34AwPXr1xmUZPEqFZQRERHSvydMmGC0N582bRoiIiLQrl07tG/fHgsXLkR2drY0CzY8PBz+/v6YN28eAODVV1/FV199hTfffBOvv/464uPj8fHHH+ONN94wWk1yu343G4V6AVeVA/w85BniJjIFIQRiY2OliTt9+vRBu3btZK6K6PEMvo7y+PHjcHJyQosWLQAAP//8M5YvX45mzZphzpw5BvXswsLCkJqaivfffx9JSUlo1aoVtm3bJk3wuXHjhtRzBICAgABs374dU6dOxVNPPQV/f3+8+eabePvttw39GBYrPvmvpes445VshRACu3btwu+//w6gKCQ7dOggc1VElaMQQghDXvD0009jxowZGDZsGK5evYpmzZph6NChOHr0KPr164eFCxeaqFTjyMjIgIeHB9LT0+Hu7i53OaV8GRuPz3ZewtA2/lgwspXc5RA9MSEEdu7cKV321bdvX7Rv317mqogqnwcGz3q9dOkSWrVqBQBYt24dunXrhjVr1iAyMhI//vhjlQumItJEHq7IQzaisLAQN27cAAC88MILDEmyOlVawq74DiK7du1C//79ARQNi6alpRm3OjtUHJS8awjZCicnJ4wbNw5XrlxB8+bN5S6HyGAG9yjbtWuHDz/8ECtXrsTevXvRr18/AEBCQkKpxQPIMDq9wNVU3jWErJ8QAgkJCdJjjUbDkCSrZXBQLly4EMePH8eUKVPw7rvvokGDBgCA9evXo1OnTkYv0J7cvJ+D/EI9VI5KBFR3kbscoioRQmDbtm1YsWKFNHmHyJoZPPT61FNP4fTp06XaP/nkk3LXW6XKKV6Rp763KxyUnPFK1kcIgV9//RVHjx4FANlW8SIyJoODstixY8dw/vx5AECzZs3Qpk0boxVlr7giD1kzIQS2bt2KP/74AwAwcOBAtG7dWuaqiJ6cwUGZkpKCsLAw7N27V7oP5IMHD9CjRw9ER0dzlY0nIF1DyTVeyco8GpKDBg2SZscTWTuDz1G+/vrryMrKwtmzZ3Hv3j3cu3cPZ86cQUZGhk2tkCOHy6m8awhZHyEEtmzZwpAkm2Vwj3Lbtm3YtWsXmjZtKrU1a9YMixYtwvPPP2/U4uyJEAJXUjjjlayPQqFA9erVoVAoMGjQILRs2VLukoiMyuCg1Ov1cHJyKtXu5OQkXV9JhkvKyENWfiEclArUq+EqdzlEBunUqRMaNmzIUy9kkwweeu3ZsyfefPNN3L59W2q7desWpk6dil69ehm1OHtSfH4ysIYLVI4Gf1uIzEoIgf379yMvL09qY0iSrTL4N/JXX32FjIwMBAYGIjg4GMHBwQgKCkJGRga+/PJLU9RoFy5zRR6yEkIIbNq0CbGxsVizZg0MXC6ayOoYPPQaEBCA48ePIzY2Vro8pGnTpggNDTV6cfaEa7ySNdDr9fjll19w8uRJKBQKtG/fnne5IZtnUFDGxMRg06ZN0Gq16NWrF15//XVT1WV3LqdkAuCMV7Jcer0emzZtwqlTp6BQKDB06FCEhITIXRaRyVU6KBcvXozJkyejYcOGcHZ2xoYNG3DlyhV88sknpqzPLgghpB5lMK+hJAuk1+vx888/488//4RCocCwYcO4divZjUqfo/zqq68we/ZsXLx4ESdPnkRUVBS+/vprU9ZmN+5ma/EgpwAKBYOSLNOOHTukkBw+fDhDkuxKpYPy6tWriIiIkB6PGTMGhYWFuHPnjkkKsyfFE3nqeDnDWcX1csnytG3bFu7u7hg+fDiaNWsmdzlEZlXpodf8/Hy4uv51fZ9SqYRKpUJubq5JCrMnnMhDls7HxwdTpkwp8xpqIltn0GSeWbNmwcXlr9s/abVafPTRR/Dw8JDaFixYYLzq7MTl5KKJPLw0hCxF8ezWFi1aoH79+gDAkCS7VemgfPbZZ3Hx4sUSbZ06dcLVq1elx5wmXjXFa7wyKMkS6PV6bNiwAWfPnsX58+fxj3/8g7fLIrtW6aCMi4szYRn2rXhVHq7xSnLT6XTYsGEDzp07B6VSiSFDhjAkye5V+X6UZBzpuQVIycwHAAQzKElGOp0OP/74I86fPw8HBweMHDkSjRo1krssItkxKGVWPOO1lrsG7hqeAyJ56HQ6rF+/HhcuXGBIEj2CQSmz4hV5eH6S5HTkyBEpJMPCwtCwYUO5SyKyGAxKmXExdLIE7du3x507d9CiRQuGJNEjKrXgwNChQ5GRkQEAWLFiBfLz801alD2RrqHkGq9kZjqdTrrzh4ODA4YOHcqQJCpDpYJy8+bNyM7OBgBMnDgR6enpJi3Knkg9Si5dR2ZUWFiItWvXYvPmzbxNFtFjVGrotUmTJpg5cyZ69OgBIQTWrl0Ld3f3MrcNDw83aoG2LEdbiJv3i1Y2aliTq/KQeRQWFmLdunW4dOkSHB0d0aFDB/j6+spdFpHFqlRQLlmyBNOmTcOWLVugUCjw3nvvlbm4gEKhYFAa4EpKUS+9uqsK1V1VMldD9qC4JxkfHw9HR0eMHj2aIUn0GJUKyk6dOuHQoUMAitZ4vXTpEv9zGcHlVM54JfMpLCxETEwMLl++LIVk8fJ0RFQ+g2e9JiQkwMfHxxS12B2uyEPm8mhIjhkzBkFBQXKXRWQVDA7KevXq4cGDB/j+++9x/vx5AECzZs3w4osvllgcnR6Pl4aQudy6dQtXr16Fk5MTxowZg8DAQLlLIrIalb4fZbE//vgDwcHB+Pzzz3Hv3j3cu3cPn3/+OYKDg3H8+HFT1GizLvP2WmQm9erVw/DhwxmSRFVgcI9y6tSpGDhwIJYuXQpHx6KXFxYW4qWXXsI//vEP7Nu3z+hF2qL8Qh2u38sBwB4lmUZBQQFyc3OlGepNmzaVuSIi62RwUP7xxx8lQhIAHB0d8a9//Qvt2rUzanG27FpaDnR6gWpqR9R0V8tdDtmYgoIC/PDDD7h//z4iIiLg6ekpd0lEVsvgoVd3d3fcuHGjVHtiYiKqVeMQYmXF/2+N12BfN97Hk4yqOCQTEhKQk5ODzMxMuUsismoGB2VYWBhefPFFxMTEIDExEYmJiYiOjsZLL72E0aNHm6JGm/TX+UkOu5LxaLVarFmzBgkJCVCpVBg3bhwCAgLkLovIqhk89Prpp59KCwsUFhYCAJycnPDqq69i/vz5Ri/QVnGNVzK24pC8fv06Q5LIiAwOSpVKhS+++ALz5s3DlStXAADBwcFwcXExenG27AovDSEjejgk1Wo1xo0bhzp16shdFpFNqPJttlxcXNCiRQtj1mI3CnV6XE0tWr6Ol4aQMRQUFCAnJ4chSWQCvB+lDG7cy4FWp4fGSQl/T2e5yyEb4OrqioiICKSnp6N27dpyl0NkUwyezENPrngiT7CPG5RKznilqsnPz8eFCxekx66urgxJIhNgUMognjNe6Qnl5+dj1apViImJwalTp+Quh8imcehVBpzIQ08iLy8Pq1evxs2bN6HRaHiTAiITq1JQxsfHY8+ePUhJSYFery/x3Pvvv2+UwmxZvBSUnMhDhsnLy8OqVatw69YtaDQahIeHw8/PT+6yiGyawUG5dOlSvPrqq/D29katWrVKrCqjUCgYlI+h1wveNYSq5OGQdHZ2xvjx4xmSRGZgcFB++OGH+Oijj/D222+boh6bdzs9F7kFOjg5KFCvBq89pcopKCjAypUrcfv2bTg7OyM8PBy1atWSuywiu2DwZJ779+9jxIgRpqjFLhQPuwZ5u8LJgXOpqHIcHR0RFBTEkCSSgcG/qUeMGIEdO3aYoha7wIk8VBUKhQK9evXCq6++ypAkMjODh14bNGiAWbNm4dChQ2jRogWcnJxKPP/GG28YrThbFJ/MiTxUObm5udi7dy969eoFJycnKBQK3qGHSAYGB+W3334LNzc37N27F3v37i3xnEKhYFA+RvHttdijpIrk5ORg5cqVSEpKQk5ODoYOHSp3SUR2y+CgTEhIMEUddkEIwdtr0WPl5ORgxYoVSE5OhqurK7p06SJ3SUR27YkWHBBCAABvPFxJqZn5yMgrhFJRNJmH6FGPhmRERAQXFCCSWZWmXa5YsQItWrSAs7MznJ2d8dRTT2HlypXGrs3mFPcm61Z3gcbJQeZqyNJkZ2cjKiqKIUlkYQzuUS5YsACzZs3ClClT0LlzZwDA/v37MWnSJKSlpWHq1KlGL9JWcEUeKo8QAmvXrkVKSgrc3NwQEREBb29vucsiIlQhKL/88kssXrwY4eHhUtvAgQPRvHlzzJkzh0FZAa7IQ+VRKBR4/vnnsXHjRoSFhTEkiSyIwUF5584ddOrUqVR7p06dcOfOHaMUZauKZ7xyIg8VE0JI5/j9/f3x6quvQqnkQhRElsTg/5ENGjTA2rVrS7XHxMSgYcOGRinKVrFHSQ/LysrC8uXLcevWLamNIUlkeQzuUc6dOxdhYWHYt2+fdI7ywIEDiI2NLTNAqcj9bC3SsrQAgGAGpd3LyspCVFQU0tLSsGnTJkyaNImzx4kslMF/vg4bNgyHDx+Gt7c3Nm7ciI0bN8Lb2xtHjhzBkCFDqlTEokWLEBgYCI1Ggw4dOuDIkSOVel10dDQUCgUGDx5cpfc1p8upRb1Jf09nuKl5G1B7lpmZKYWku7s7wsLCGJJEFqxKv7Hbtm2LVatWGaWAmJgYTJs2DUuWLEGHDh2wcOFC9O7dGxcvXoSvr2+5r7t27RqmT5+Orl27GqUOUysedmVv0r4Vh+Tdu3fh7u6OiIgIVK9eXe6yiKgClepRZmRklPh3RV+GWrBgAV5++WVMnDgRzZo1w5IlS+Di4oJly5aV+xqdToexY8di7ty5qF+/vsHvKYfiNV45kcd+PRySHh4emDBhAkOSyApUqkfp5eWFO3fuwNfXF56enmUOExXP3tPpdJV+c61Wi2PHjmHmzJlSm1KpRGhoKA4ePFju6/7973/D19cXL774In777bcK3yM/Px/5+fnS46qEuTFwjVfau3evFJIRERHw8vKSuyQiqoRKBeXu3bulv3z37NljtDdPS0uDTqdDzZo1S7TXrFkTFy5cKPM1+/fvx/fff4+TJ09W6j3mzZuHuXPnPmmpT+wK13i1e71794ZOp0O3bt3g6ekpdzlEVEmVCspu3bpJ/w4KCkJAQECpXqUQAomJicat7hGZmZkYP348li5dWukLsmfOnIlp06ZJjzMyMhAQEGCqEsuUlV+I2+l5ANijtDd5eXlQq9VQKBRwcnLCoEGD5C6JiAxk8GSeoKAgaRj2Yffu3UNQUJBBQ6/e3t5wcHBAcnJyifbk5OQyb0575coVXLt2DQMGDJDa9Ho9gKI7wF+8eBHBwcElXqNWq6FWqytdkykU9ya93dTwdFHJWguZT3p6OqKiotCsWTP06tWLM1uJrJTBl4c8vJLIw7KysqDRaAzal0qlQtu2bREbGyu16fV6xMbGomPHjqW2b9KkCU6fPo2TJ09KXwMHDkSPHj1w8uRJs/cUKyuew652Jz09HZGRkbh//z7Onj2LvLw8uUsioiqqdI+yePhSoVBg1qxZcHFxkZ7T6XQ4fPgwWrVqZXAB06ZNQ0REBNq1a4f27dtj4cKFyM7OxsSJEwEA4eHh8Pf3x7x586DRaBASElLi9cXneh5ttyScyGNfHjx4gKioKDx48ABeXl6IiIiAs7Oz3GURURVVOihPnDgBoKhHefr0aahUfw0hqlQqtGzZEtOnTze4gLCwMKSmpuL9999HUlISWrVqhW3btkkTfG7cuGH1y3pJE3lqMiht3aMhOWHCBLi7u8tdFhE9AYUovvtyJU2cOBFffPGF1f7nz8jIgIeHB9LT0832Gbp9sgfX7+ZgzUsd0KkB7wphqx48eIDIyEikp6ejevXqiIiIsNr/J0T2oLJ5YHBXbeHChSgsLCzVfu/ePdmuUbRkeQU6JN7LAQA0YI/Spt28eZMhSWSDDA7KUaNGITo6ulT72rVrMWrUKKMUZUuupmZDLwAPZyf4uMk7+5ZMKyQkBMOGDeNwK5GNMTgoDx8+jB49epRq7969Ow4fPmyUomxJ8WLoDXzdeHmADbp//z6ysrKkxyEhIahWrZqMFRGRsRkclPn5+WUOvRYUFCA3N9coRdmSy8m8WbOtunfvHiIjI7FixQpkZ2fLXQ4RmYjBQdm+fXt8++23pdqXLFmCtm3bGqUoWxLPmzXbpLt37yIyMhIZGRkQQsDAOXFEZEUMXpnnww8/RGhoKE6dOoVevXoBAGJjY3H06FHs2LHD6AVau8sMSptz9+5dREVFITMzEz4+PggPD4ebG7+/RLbK4B5l586dcfDgQQQEBGDt2rX45Zdf0KBBA/z5559Wc29IcynQ6ZGQVjQk17Amz1vZgrS0NERGRjIkiexIlW7c3KpVK6xevdrYtdic63dzUKgXcFE5oLaHYcv7keVJS0tDVFQUsrKy4Ovri/DwcLi6uspdFhGZWJWCslheXh60Wm2JNk6L/8vlh5au44xX6+fo6AhHR0eGJJGdMTgoc3Jy8K9//Qtr167F3bt3Sz1vyN1DbF188v/OT/pwaM4WeHp6IiIiAk5OTgxJIjti8DnKt956C7t378bixYuhVqvx3XffYe7cuahduzZWrFhhihqtlnQNJVfksVqpqaklbiLu6enJkCSyMwb3KH/55ResWLEC3bt3x8SJE9G1a1c0aNAA9erVw+rVqzF27FhT1GmVinuUDX05kccapaSkICoqCnl5eRgzZkype50SkX0wuEd579491K9fH0DR+ch79+4BALp06YJ9+/YZtzorptMLXEnlpSHWqjgkc3Jy4Ovri9q1a8tdEhHJxOCgrF+/PhISEgAU3Uh57dq1AIp6msX3hiTg1v1c5BfqoXJUIsCL9yK0JsnJyVJI+vn5ITw8nPeTJLJjBgflxIkTcerUKQDAjBkzsGjRImg0GkydOhVvvfWW0Qu0VsU3a67v7QpHB+u+n6Y9eTQkx48fz5AksnMGn6OcOnWq9O/Q0FBcuHABx44dQ4MGDfDUU08ZtThrxhV5rE/xTZdzc3NRu3ZtjB8/HhoNr38lsncGdXUKCgrQq1cvxMfHS2316tXD0KFDGZKP4Bqv1sfDwwNNmjRhSBJRCQb1KJ2cnPDnn3+aqhabUtyj5IxX66FQKDBgwAAUFBRApVLJXQ4RWQiDT56NGzcO33//vSlqsRlCiL+CktdQWrQ7d+5g06ZN0kIZCoWCIUlEJRh8jrKwsBDLli3Drl270LZt21IXXy9YsMBoxVmrpIw8ZOUXwkGpQGANXpxuqW7fvo2VK1ciLy8P7u7u6N69u9wlEZEFMjgoz5w5gzZt2gAALl26VOI5rmdapLg3Wa+GC1SOnPFqiR4OyYCAAHTs2FHukojIQlU6KK9evYqgoCDs2bPHlPXYBK7xatlu3bqFlStXIj8/HwEBARg7dizUarXcZRGRhap0d6dhw4ZITU2VHoeFhSE5OdkkRVm74jVeeX7S8ty8eVMKybp16zIkieixKh2UQogSj7du3Yrs7GyjF2QLLnONV4tUUFCAmJgYhiQRGYQn0EzgMtd4tUhOTk4YMmQIGjRogLFjx3J2KxFVSqXPUSoUilKTdTh5p7S7Wfm4l62FQgEE8xylRdDr9VAqi/4mrF+/PoKCgvizS0SVVumgFEJgwoQJ0lBVXl4eJk2aVOrykA0bNhi3QitTvCKPv6cznFUOMldDN27cwM8//4xRo0bBx8cHAP/AIyLDVDooIyIiSjweN26c0YuxBX+tyMPepNxu3LiB1atXQ6vVYt++fRg2bJjcJRGRFap0UC5fvtyUddiMv1bk4UQeOV2/fh2rV69GQUEBgoKCMHDgQLlLIiIrZfCCA1Qx6a4hPD8pm4dDsn79+hg1ahScnJzkLouIrBSD0siK70PZgNdQyuLatWtYs2YNQ5KIjIZBaUQZeQVIzsgHwEtD5CCEwL59+1BQUIDg4GCEhYUxJInoiTEojah42LWmuxruGv6CNjeFQoGRI0fit99+Q48ePeDoyB9vInpyXHDAiIpX5GFv0rzS09Olf2s0Gjz33HMMSSIyGgalEUlrvHLpOrO5evUqvvrqK/z+++9yl0JENopBaUTxyf+byMMepVlcuXIFP/zwAwoLC3H9+nXo9Xq5SyIiG8TxKSMqXpWHQWl6ly9fRnR0NHQ6HRo1aoQRI0ZIy9QRERkTg9JIcrSFuPUgFwBX5TG1h0OycePGGDFiBBwcuFwgEZkGg9JIrqZmQwjAy8UJNdx46yZTiY+PR0xMDEOSiMyGQWkkf63xyok8pnT37l3odDo0adIEw4cPZ0gSkckxKI2EK/KYxzPPPANPT080bNiQIUlEZsHZD0YSn8w1Xk0lISEBeXl50uMmTZowJInIbBiURiJdQ8kepVFdvHgRq1atwqpVq5Cfny93OURkhzj0agTaQj2u380BwEtDjOnChQtYt24d9Ho9PD09uW4rEcmCQWkE1+5mQ6cXcFM7opa7Ru5ybML58+exfv166PV6hISEYMiQIbxOkohkwaA0gviH1nhVKBQyV2P9Hg7JFi1aYPDgwQxJIpINf/sYwWWuyGM0Fy5cYEgSkUVhj9IIii8N4Yo8T65GjRpwdnZGcHAwBg0axJAkItkxKI2APUrj8fHxwcsvv4xq1aoxJInIIvA30RMq1OlxNS0bAFflqaozZ87g6tWr0mMPDw+GJBFZDP42ekKJ93OhLdRD7aiEv5ez3OVYndOnT2PDhg344YcfkJKSInc5RESlMCifUPGwa7CPGxyUnPFqiNOnT+Onn36CEAItWrSAj4+P3CUREZXCc5RPSJrIwxV5DPLnn39i48aNEEKgdevWGDBgAC+tISKLxKB8Qpe5xqvBTp06hY0bNwIA2rRpg/79+zMkichiMSifENd4NUxCQoIUkm3btkW/fv0YkkRk0RiUT0CvF7w0xEB169ZFs2bN4OLighdeeIEhSUQWj0H5BO5k5CFHq4OjUoF6NVzlLscqODg4YNiwYVAoFAxJIrIKnPX6BOKTiybyBHm7wsmBh7I8x48fxy+//AIhBABAqVQyJInIarBHaaBbD3JxP1sLAPgtPhUA4FNNjTO30gEAXq4q+Hvyespix44dw+bNmwEAQUFBCAkJkbkiIiLDWEQ3aNGiRQgMDIRGo0GHDh1w5MiRcrddunQpunbtCi8vL3h5eSE0NLTC7Y3p1oNc9Pw0Dv2/3I/+X+7H9/uvAQB+v3JXauv5aRxuPcg1Sz2W7uGQbN++PZo3by5zRUREhpM9KGNiYjBt2jTMnj0bx48fR8uWLdG7d+9yV2mJi4vD6NGjsWfPHhw8eBABAQF4/vnncevWLZPXej9bi/xCfYXb5BfqpR6nPfvjjz+kkOzQoQP69OnD4VYiskqyB+WCBQvw8ssvY+LEiWjWrBmWLFkCFxcXLFu2rMztV69ejddeew2tWrVCkyZN8N1330Gv1yM2NtbMlVN5jh49ii1btgAAnnnmGfTu3ZshSURWS9ag1Gq1OHbsGEJDQ6U2pVKJ0NBQHDx4sFL7yMnJQUFBAapXr17m8/n5+cjIyCjxRabz4MEDbNu2DUBRSD7//PMMSSKyarIGZVpaGnQ6HWrWrFmivWbNmkhKSqrUPt5++23Url27RNg+bN68efDw8JC+AgICnrhuKp+npyeGDx+Ozp07MySJyCbIPvT6JObPn4/o6Gj89NNP0Gg0ZW4zc+ZMpKenS1+JiYlmrtI+aLV/nZdt2rQpQkNDGZJEZBNkvTzE29sbDg4OSE5OLtGenJyMWrVqVfjaTz/9FPPnz8euXbvw1FNPlbudWq2GWq02Sr1UtsOHD+PQoUOIiIiAp6en3OUQERmVrD1KlUqFtm3blpiIUzwxp2PHjuW+7r///S8++OADbNu2De3atTNHqVSOQ4cOYdu2bXjw4AHOnTsndzlEREYn+4ID06ZNQ0REBNq1a4f27dtj4cKFyM7OxsSJEwEA4eHh8Pf3x7x58wAA//nPf/D+++9jzZo1CAwMlM5lurm5wc3NtOutermqoHZUVniJiNpRCS9XlUnrsBQHDx7Ejh07AABdu3at8I8bIiJrJXtQhoWFITU1Fe+//z6SkpLQqlUrbNu2TZrgc+PGDSiVf3V8Fy9eDK1Wi+HDh5fYz+zZszFnzhyT1urv6Yzd07tXeJ2kvazM8/vvv2Pnzp0AikKyR48ePCdJRDZJIYoX4LQTGRkZ8PDwQHp6Otzd3eUuxyo9HJLPPvssunfvzpAkIqtT2TyQvUdJ1qWgoAAnT54EAHTr1g3du3eXtR4iIlNjUJJBnJycEBERgXPnzuHpp5+WuxwiIpOz6usoyXweXgDC1dWVIUlEdoNBSY+1b98+fPPNNzh+/LjcpRARmR2HXqlCe/fuRVxcHAAgOztb3mKIiGTAoKRyxcXFYe/evQCAXr16oUuXLjJXRERkfgxKKtPDIRkaGorOnTvLXBERkTwYlFSCEAJxcXHYt28fAIYkERGDkkrR64uW6HvuuefQqVMnmashIpIXg5JKUCgU6NmzJxo2bIi6devKXQ4Rkex4eQhBCIETJ06goKAAQFFYMiSJiIowKO2cEAKxsbHYtGkTYmJipGFXIiIqwqFXOyaEwK5du/D7778DABo1alTiTi1ERMSgtFtCCOzcuRMHDx4EAPTt2xft27eXuSoiIsvDoLRDQgjs2LEDhw4dAgC88MILXLuViKgcDEo7tGfPHikk+/Xrh3bt2slcERGR5eIJKTvUuHFjaDQa9O/fnyFJRPQYCiGEkLsIc6rsHa1tXU5ODlxcXOQug4hINpXNA/Yo7UDx7NZbt25JbQxJIqLKYVDaOCEEtm7digMHDmDVqlXIzc2VuyQiIqvCoLRhxSH5xx9/AACef/55ODs7y1wVEZF14axXGyWEwJYtW3Ds2DEAwKBBg9CqVSt5iyIiskIMShskhMDmzZtx/PhxAMDgwYPRsmVLmasiIrJODEobdOTIERw/fhwKhQKDBw/GU089JXdJRERWi0Fpg9q0aYPLly+jRYsWDEkioifEoLQRxZfDKhQKODk5YcyYMVAoFDJXRURk/Tjr1Qbo9Xps2rQJsbGxJQKTiIieHHuUVq44JE+dOgWFQoGQkBDUqlVL7rKIiGwGg9KK6fV6/Pzzz/jzzz+hUCgwbNgwhiQRkZExKK3UoyE5fPhwNGvWTO6yiIhsDoPSCun1emzcuBGnT5+GUqnEsGHDGJJERCbCoLRCN27ckEJy+PDhaNq0qdwlERHZLAalFQoMDMTAgQOh0WgYkkREJsagtBJ6vR55eXnS7bFat24tc0VERPaB11FaAZ1Ohx9//BGRkZHIysqSuxwiIrvCHqWFKw7J8+fPw8HBASkpKXBzc5O7LCIiu8EepQV7NCRHjhyJ+vXry10WEZFdYY/SQul0Oqxfvx4XLlyAg4MDwsLC0LBhQ7nLIiKyOwxKC6TT6bBu3TpcvHgRDg4OGDVqFBo0aCB3WUREdolDrxYoJycHycnJDEkiIgvAHqUFqlatGiIiInDv3j2ekyQikhl7lBaisLAQ169flx57enoyJImILACD0gIUFhZi7dq1WLFiBc6fPy93OURE9BAGpcwKCwsRExOD+Ph4KJVKaDQauUsiIqKHMChlVBySly9fhqOjI8aMGYOgoCC5yyIioodwMo9MCgoKEBMTgytXrsDJyQljxoxBYGCg3GUREdEjGJQyKCwsRHR0NK5evcqQJCKycAxKGTg4OMDT0xNOTk4YO3Ys6tWrJ3dJRERUDgalDBQKBfr374+OHTvC29tb7nKIiKgCnMxjJlqtFvv27YNOpwNQFJYMSSIiy8cepRlotVr88MMPuHbtGu7fv49BgwbJXRIREVUSg9LEtFot1qxZg+vXr0OlUqFNmzZyl0RERAZgUJqQVqvF6tWrcePGDajVaowbNw516tSRuywiIjIAg9JE8vPzsWbNGoYkEZGVY1CagBAC69atk0Jy/Pjx8Pf3l7ssIiKqAs56NQGFQoHOnTvDzc2NIUlEZOUUQgghdxHmlJGRAQ8PD6Snp8Pd3d2k71VQUAAnJyeTvgcREVVNZfOAPUojycvLQ3R0NFJTU6U2hiQRkfVjUBpBXl4eVq1ahYsXL2Lt2rXQ6/Vyl0REREZiEUG5aNEiBAYGQqPRoEOHDjhy5EiF269btw5NmjSBRqNBixYtsHXrVjNVWlpxSN66dQvOzs4YOnQolEqLOKxERGQEsv9Gj4mJwbRp0zB79mwcP34cLVu2RO/evZGSklLm9r///jtGjx6NF198ESdOnMDgwYMxePBgnDlzxsyVF4XkypUrpZAMDw+Hn5+f2esgIiLTkX0yT4cOHfD000/jq6++AgDo9XoEBATg9ddfx4wZM0ptHxYWhuzsbGzevFlqe+aZZ9CqVSssWbLkse9nrMk8ubm5WLVqFW7fvi2FZK1ataq8PyIiMi+rmMyj1Wpx7NgxhIaGSm1KpRKhoaE4ePBgma85ePBgie0BoHfv3uVun5+fj4yMjBJfxrBr1y7cvn0bLi4uiIiIYEgSEdkoWYMyLS0NOp0ONWvWLNFes2ZNJCUllfmapKQkg7afN28ePDw8pK+AgACj1P7cc8+hUaNGCA8PL1UPERHZDtnPUZrazJkzkZ6eLn0lJiYaZb8ajQajR49mSBIR2ThZl7Dz9vaGg4MDkpOTS7QnJyeXO5RZq1Ytg7ZXq9VQq9XGKZiIiOyOrD1KlUqFtm3bIjY2VmrT6/WIjY1Fx44dy3xNx44dS2wPADt37ix3eyIioich+6Lo06ZNQ0REBNq1a4f27dtj4cKFyM7OxsSJEwEA4eHh8Pf3x7x58wAAb775Jrp164bPPvsM/fr1Q3R0NP744w98++23cn4MIiKyUbIHZVhYGFJTU/H+++8jKSkJrVq1wrZt26Rzfzdu3ChxAX+nTp2wZs0avPfee3jnnXfQsGFDbNy4ESEhIXJ9BCIismGyX0dpbuZcFJ2IiCyXVVxHSUREZOkYlERERBVgUBIREVWAQUlERFQBBiUREVEFGJREREQVYFASERFVgEFJRERUAQYlERFRBRiUREREFZB9rVdzK16xLyMjQ+ZKiIhITsU58LiVXO0uKDMzMwEAAQEBMldCRESWIDMzEx4eHuU+b3eLouv1ety+fRvVqlWDQqGo8n4yMjIQEBCAxMRELq7+EB6X8vHYlI3HpXw8NmUz1nERQiAzMxO1a9cucZeqR9ldj1KpVKJOnTpG25+7uzt/gMvA41I+Hpuy8biUj8embMY4LhX1JItxMg8REVEFGJREREQVYFBWkVqtxuzZs6FWq+UuxaLwuJSPx6ZsPC7l47Epm7mPi91N5iEiIjIEe5REREQVYFASERFVgEFJRERUAQYlERFRBRiUFVi0aBECAwOh0WjQoUMHHDlypMLt161bhyZNmkCj0aBFixbYunWrmSo1L0OOy9KlS9G1a1d4eXnBy8sLoaGhjz2O1szQn5li0dHRUCgUGDx4sGkLlImhx+XBgweYPHky/Pz8oFar0ahRI/5/+p+FCxeicePGcHZ2RkBAAKZOnYq8vDwzVWse+/btw4ABA1C7dm0oFAps3Ljxsa+Ji4tDmzZtoFar0aBBA0RGRhqvIEFlio6OFiqVSixbtkycPXtWvPzyy8LT01MkJyeXuf2BAweEg4OD+O9//yvOnTsn3nvvPeHk5CROnz5t5spNy9DjMmbMGLFo0SJx4sQJcf78eTFhwgTh4eEhbt68aebKTc/QY1MsISFB+Pv7i65du4pBgwaZp1gzMvS45Ofni3bt2okXXnhB7N+/XyQkJIi4uDhx8uRJM1dueoYem9WrVwu1Wi1Wr14tEhISxPbt24Wfn5+YOnWqmSs3ra1bt4p3331XbNiwQQAQP/30U4XbX716Vbi4uIhp06aJc+fOiS+//FI4ODiIbdu2GaUeBmU52rdvLyZPniw91ul0onbt2mLevHllbj9y5EjRr1+/Em0dOnQQf//7301ap7kZelweVVhYKKpVqyaioqJMVaJsqnJsCgsLRadOncR3330nIiIibDIoDT0uixcvFvXr1xdardZcJcrG0GMzefJk0bNnzxJt06ZNE507dzZpnXKqTFD+61//Es2bNy/RFhYWJnr37m2UGjj0WgatVotjx44hNDRUalMqlQgNDcXBgwfLfM3BgwdLbA8AvXv3Lnd7a1SV4/KonJwcFBQUoHr16qYqUxZVPTb//ve/4evrixdffNEcZZpdVY7Lpk2b0LFjR0yePBk1a9ZESEgIPv74Y+h0OnOVbRZVOTadOnXCsWPHpOHZq1evYuvWrXjhhRfMUrOlMvXvX7tbFL0y0tLSoNPpULNmzRLtNWvWxIULF8p8TVJSUpnbJyUlmaxOc6vKcXnU22+/jdq1a5f6obZ2VTk2+/fvx/fff4+TJ0+aoUJ5VOW4XL16Fbt378bYsWOxdetWXL58Ga+99hoKCgowe/Zsc5RtFlU5NmPGjEFaWhq6dOkCIQQKCwsxadIkvPPOO+Yo2WKV9/s3IyMDubm5cHZ2fqL9s0dJZjN//nxER0fjp59+gkajkbscWWVmZmL8+PFYunQpvL295S7Houj1evj6+uLbb79F27ZtERYWhnfffRdLliyRuzTZxcXF4eOPP8bXX3+N48ePY8OGDdiyZQs++OADuUuzaexRlsHb2xsODg5ITk4u0Z6cnIxatWqV+ZpatWoZtL01qspxKfbpp59i/vz52LVrF5566ilTlikLQ4/NlStXcO3aNQwYMEBq0+v1AABHR0dcvHgRwcHBpi3aDKryM+Pn5wcnJyc4ODhIbU2bNkVSUhK0Wi1UKpVJazaXqhybWbNmYfz48XjppZcAAC1atEB2djZeeeUVvPvuuxXeU9GWlff7193d/Yl7kwB7lGVSqVRo27YtYmNjpTa9Xo/Y2Fh07NixzNd07NixxPYAsHPnznK3t0ZVOS4A8N///hcffPABtm3bhnbt2pmjVLMz9Ng0adIEp0+fxsmTJ6WvgQMHokePHjh58iQCAgLMWb7JVOVnpnPnzrh8+bL0hwMAXLp0CX5+fjYTkkDVjk1OTk6pMCz+g0LY8bLdJv/9a5QpQTYoOjpaqNVqERkZKc6dOydeeeUV4enpKZKSkoQQQowfP17MmDFD2v7AgQPC0dFRfPrpp+L8+fNi9uzZNnt5iCHHZf78+UKlUon169eLO3fuSF+ZmZlyfQSTMfTYPMpWZ70aelxu3LghqlWrJqZMmSIuXrwoNm/eLHx9fcWHH34o10cwGUOPzezZs0W1atXEDz/8IK5evSp27NghgoODxciRI+X6CCaRmZkpTpw4IU6cOCEAiAULFogTJ06I69evCyGEmDFjhhg/fry0ffHlIW+99ZY4f/68WLRoES8PMZcvv/xS1K1bV6hUKtG+fXtx6NAh6blu3bqJiIiIEtuvXbtWNGrUSKhUKtG8eXOxZcsWM1dsHoYcl3r16gkApb5mz55t/sLNwNCfmYfZalAKYfhx+f3330WHDh2EWq0W9evXFx999JEoLCw0c9XmYcixKSgoEHPmzBHBwcFCo9GIgIAA8dprr4n79++bv3AT2rNnT5m/N4qPRUREhOjWrVup17Rq1UqoVCpRv359sXz5cqPVw9tsERERVYDnKImIiCrAoCQiIqoAg5KIiKgCDEoiIqIKMCiJiIgqwKAkIiKqAIOSiIioAgxKIiKiCjAoiSxYZGQkPD09pcdz5sxBq1atZKlFoVBg48aNZn/fCRMmYPDgwU+0j2vXrkGhUFR4S7O4uDgoFAo8ePAAgGUde5IXg5IsxoQJE6BQKDBp0qRSz02ePBkKhQITJkwwf2GPiIyMhEKhgEKhgFKpRJ06dTBx4kSkpKSY/L2nT59eavHnipgz3Iq/fwqFAiqVCg0aNMC///1vFBYWmuX9n1SnTp1w584deHh4lPn8o8feGAFO1oFBSRYlICAA0dHRyM3Nldry8vKwZs0a1K1bV8bKSnJ3d8edO3dw8+ZNLF26FL/++ivGjx9f5rY6na7EnTCehJubG2rUqGGUfZlCnz59cOfOHcTHx+Of//wn5syZg08++aTMbbVarZmrq5hKpUKtWrWgUCjKfN7Sjz2ZDoOSLEqbNm0QEBCADRs2SG0bNmxA3bp10bp16xLb6vV6zJs3D0FBQXB2dkbLli2xfv166XmdTocXX3xRer5x48b44osvSuyjuFfw6aefws/PDzVq1MDkyZNRUFBQYZ0KhQK1atVC7dq10bdvX7zxxhvYtWsXcnNzpSG7TZs2oVmzZlCr1bhx4wby8/Mxffp0+Pv7w9XVFR06dEBcXFyJ/UZGRqJu3bpwcXHBkCFDcPfu3RLPlzX8t2zZMjRv3hxqtRp+fn6YMmUKACAwMBAAMGTIECgUCukxAPz8889o06YNNBoN6tevj7lz55bo+cXHx+PZZ5+FRqNBs2bNsHPnzgqPRzG1Wo1atWqhXr16ePXVVxEaGopNmzaVONYfffQRateujcaNGwMATp8+jZ49e8LZ2Rk1atTAK6+8gqysrFL7njt3Lnx8fODu7o5JkyaVCNpt27ahS5cu8PT0RI0aNdC/f39cuXKl1D4uXLiATp06QaPRICQkBHv37pWee3To9VEPH/s5c+YgKioKP//8s9SLjouLQ8+ePaXjXyw1NRUqlcqgkQCyLAxKsjh/+9vfsHz5cunxsmXLMHHixFLbzZs3DytWrMCSJUtw9uxZTJ06FePGjZN++en1etSpUwfr1q3DuXPn8P777+Odd97B2rVrS+xnz549uHLlCvbs2YOoqChERkYiMjLSoJqdnZ2h1+ulsMnJycF//vMffPfddzh79ix8fX0xZcoUHDx4ENHR0fjzzz8xYsQI9OnTB/Hx8QCAw4cP48UXX8SUKVNw8uRJ9OjRAx9++GGF77t48WJMnjwZr7zyCk6fPo1NmzahQYMGAICjR48CAJYvX447d+5Ij3/77TeEh4fjzTffxLlz5/DNN98gMjISH330kXTchg4dCpVKhcOHD2PJkiV4++23DToeDx+XhwMtNjYWFy9exM6dO7F582ZkZ2ejd+/e8PLywtGjR7Fu3Trs2rWrVNjExsbi/PnziIuLww8//IANGzZg7ty50vPZ2dmYNm0a/vjjD8TGxkKpVGLIkCGlevJvvfUW/vnPf+LEiRPo2LEjBgwYUOqPkcqYPn06Ro4cKfWg79y5g06dOuGll17CmjVrkJ+fL227atUq+Pv7o2fPnga/D1kIo92HhOgJFd9mKiUlRajVanHt2jVx7do1odFoRGpqqhg0aJB0m528vDzh4uIifv/99xL7ePHFF8Xo0aPLfY/JkyeLYcOGlXjPevXqlbiF04gRI0RYWFi5+1i+fLnw8PCQHl+6dEk0atRItGvXTnoegDh58qS0zfXr14WDg4O4detWiX316tVLzJw5UwghxOjRo8ULL7xQ4vmwsLAS7zV79mzRsmVL6XHt2rXFu+++W26tAMRPP/1U6j0//vjjEm0rV64Ufn5+Qgghtm/fLhwdHUvU+uuvv5a5r4c9fJswvV4vdu7cKdRqtZg+fbr0fM2aNUV+fr70mm+//VZ4eXmJrKwsqW3Lli1CqVRK92SMiIgQ1atXF9nZ2dI2ixcvFm5ubkKn05VZS2pqqgAg3Q82ISFBABDz58+XtikoKBB16tQR//nPf4QQf93aqfiWVY9+nx899mXdFi03N1d4eXmJmJgYqe2pp54Sc+bMKfe4keVzlDOkicri4+ODfv36ITIyEkII9OvXD97e3iW2uXz5MnJycvDcc8+VaNdqtSWGaBctWoRly5bhxo0byM3NhVarLTV02bx5c+ku8QDg5+eH06dPV1hjeno63NzcoNfrkZeXhy5duuC7776TnlepVHjqqaekx6dPn4ZOp0OjRo1K7Cc/P18673X+/HkMGTKkxPMdO3bEtm3byqwhJSUFt2/fRq9evSqs9VGnTp3CgQMHpB4kUDRMnZeXh5ycHJw/fx4BAQGoXbt2iToqY/PmzXBzc0NBQQH0ej3GjBmDOXPmSM+3aNECKpVKenz+/Hm0bNkSrq6uUlvnzp2h1+tx8eJF1KxZEwDQsmVLuLi4lKgnKysLiYmJqFevHuLj4/H+++/j8OHDSEtLk3qSN27cQEhISJmfw9HREe3atcP58+cr9dkqQ6PRYPz48Vi2bBlGjhyJ48eP48yZM9LwM1knBiVZpL/97W/S8NuiRYtKPV98DmvLli3w9/cv8ZxarQYAREdHY/r06fjss8/QsWNHVKtWDZ988gkOHz5cYnsnJ6cSjxUKxWMn31SrVg3Hjx+HUqmEn58fnJ2dSzzv7OxcYlJIVlYWHBwccOzYsRKhDBRNEqmKR9+zsrKysjB37lwMHTq01HMajaZK+yzWo0cPLF68GCqVCrVr14ajY8lfMQ8HojENGDAA9erVw9KlS1G7dm3o9XqEhITIMmHopZdeQqtWrXDz5k0sX74cPXv2RL169cxeBxkPg5IsUp8+faDVaqFQKNC7d+9Szz88SaZbt25l7uPAgQPo1KkTXnvtNamtrAkeVaFUKqVzgZXRunVr6HQ6pKSkoGvXrmVu07Rp01IhfujQoXL3Wa1aNQQGBiI2NhY9evQocxsnJyfodLoSbW3atMHFixfLrb9p06ZITEzEnTt34Ofn99g6Hubq6mrQcWnatCkiIyORnZ0theiBAwegVCqlyT5AUS84NzdX+uPg0KFDcHNzQ0BAAO7evYuLFy9i6dKl0rHdv39/me936NAhPPvsswCAwsJCHDt2rNT50MpSqVSlji1Q1Gtu164dli5dijVr1uCrr76q0v7JcjAoySI5ODhIQ2KP9sCAopCYPn06pk6dCr1ejy5duiA9PR0HDhyAu7s7IiIi0LBhQ6xYsQLbt29HUFAQVq5ciaNHjyIoKMjcHweNGjXC2LFjER4ejs8++wytW7dGamoqYmNj8dRTT6Ffv35444030LlzZ3z66acYNGgQtm/fXu6wa7E5c+Zg0qRJ8PX1Rd++fZGZmYkDBw7g9ddfBwApSDt37gy1Wg0vLy+8//776N+/P+rWrYvhw4dDqVTi1KlTOHPmDD788EOEhoaiUaNGiIiIwCeffIKMjAy8++67JjkuY8eOxezZsxEREYE5c+YgNTUVr7/+OsaPHy8NuwJFQ+ovvvgi3nvvPVy7dg2zZ8/GlClToFQq4eXlhRo1auDbb7+Fn58fbty4gRkzZpT5fosWLULDhg3RtGlTfP7557h//z7+9re/Van2wMBAbN++HRcvXkSNGjXg4eEhjU689NJLmDJlClxdXUsNp5P14axXslju7u5wd3cv9/kPPvgAs2bNwrx589C0aVP06dMHW7ZskYLw73//O4YOHYqwsDB06NABd+/eLdG7NLfly5cjPDwc//znP9G4cWMMHjwYR48ela4PfeaZZ7B06VJ88cUXaNmyJXbs2IH33nuvwn1GRERg4cKF+Prrr9G8eXP0799fmkULAJ999hl27tyJgIAA6dxt7969sXnzZuzYsQNPP/00nnnmGXz++efS8KBSqcRPP/2E3NxctG/fHi+99FKJ85nG5OLigu3bt+PevXt4+umnMXz4cPTq1atUL6xXr15o2LAhnn32WYSFhWHgwIHSuU+lUono6GgcO3YMISEhmDp1arnXbs6fPx/z589Hy5YtsX//fmzatKnU+e/Kevnll9G4cWO0a9cOPj4+OHDggPTc6NGj4ejoiNGjRz/xcDbJTyGEEHIXQURkS65du4bg4GAcPXoUbdq0kbscekIMSiIiIykoKMDdu3cxffp0JCQklOhlkvXi0CsRkZEcOHAAfn5+OHr0KJYsWSJ3OWQk7FESERFVgD1KIiKiCjAoiYiIKsCgJCIiqgCDkoiIqAIMSiIiogowKImIiCrAoCQiIqoAg5KIiKgC/w+VG8Z9gEz7VwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy (threshold=0.5): 81.45%\n", "Recall (threshold=0.5): 69.74%\n", "Accuracy (threshold=0.000075): 99.19%\n", "Recall (threshold=0.000075): 98.68%\n"]}], "source": ["t = eval_classifier(evalset, \"pytest\", \"is_pytest\", min_lines=100)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ad088fb294764ed6abc07520158c3548", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/100 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Filtered 24/100 (24.00%) logs with less than 100 lines\n"]}, {"data": {"image/png": "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*********//lCbNm1kjNGZM2f00EMP6Z///GdllPw/o6TfhVlZWTp16pR8fX3L/ZickYGlTZ06VUuXLtW7774rHx8fV5dzyTh+/Ljuvfdevfrqq6pZs6ary7mkFRQUKDQ0VK+88opiYmJ055136vHHH9dLL73k6tIuKZ9//rmeeeYZzZ07V5s2bdI777yjjz76SJMnT3Z1abhInJEpRs2aNeXu7q6MjAyH9oyMDIWHhxd7n/Dw8DL1x9+cmetCzz33nKZOnao1a9bo6quvrsgyLa+s87xr1y7t3btXXbt2tbcVFBRIkjw8PLRt2zbVr1+/You2IGeezxEREfL09JS7u7u9rWnTpkpPT9fp06fl5eVVoTVbkTPzPH78eN177726//77JUlXXXWVsrOzNWTIED3++ONyc+Pv+vJQ0u/CgICACjkbI3FGplheXl6KiYnR2rVr7W0FBQVau3atYmNji71PbGysQ39JWr16dYn98Tdn5lqSpk+frsmTJ2vlypW6/vrrK6NUSyvrPDdp0kQ//fSTtmzZYt9uv/12dejQQVu2bFFUVFRllm8ZzjyfW7durZ07d9qDoiRt375dERERhJgSODPPJ0+eLBJWCsOj4buTy41LfhdW2DJii1u6dKnx9vY2CxYsMFu3bjVDhgwxQUFBJj093RhjzL333mvGjRtn7//VV18ZDw8P89xzz5lff/3VTJgwgcuvS6mscz116lTj5eVlli9fbtLS0uzb8ePHXfUQLKGs83wurloqnbLO8/79+42/v78ZNmyY2bZtm/nwww9NaGiomTJliqsegiWUdZ4nTJhg/P39zZtvvml2795tPv30U1O/fn3Tp08fVz0ESzh+/LjZvHmz2bx5s5FkZsyYYTZv3mz27dtnjDFm3Lhx5t5777X3L7z8+tFHHzW//vqrmTNnDpdfu9KLL75o6tSpY7y8vMwNN9xgNmzYYN/Xvn17M2DAAIf+b7/9tmnUqJHx8vIyV155pfnoo48quWLrKstc161b10gqsk2YMKHyC7eYsj6nz0aQKb2yzvPXX39tWrRoYby9vc3ll19unn76aXPmzJlKrtp6yjLPeXl5ZuLEiaZ+/frGx8fHREVFmaFDh5q//vqr8gu3kHXr1hX7723h3A4YMMC0b9++yH2aN29uvLy8zOWXX25SUlIqtEabMZxTAwAA1sQaGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFkEGQAAYFn/Dwk5zltkUb+tAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Fraction of logs above threshold: 14.94%\n"]}], "source": ["sample = classify(sample_full, \"pytest\", min_lines=100, sample_size=100)\n", "sample_pos = sample[sample[\"proba\"] > t]\n", "frac = sample_pos.shape[0] / sample.shape[0]\n", "print(f\"Fraction of logs above threshold: {frac:.2%}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "798f598460944ffb8b68fe266417f369", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/51 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "63adb363b8dc413f9cb23df014e494c6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/68 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cdc6b791fa734c18b97d97d9dd81f9a4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/353 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "feddebef8b3542f289725cd31102bc35", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/47 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b74673e352fe471ca3168266226a50a1", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/65 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d5ec165542a1418699360402d9fbdd46", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/93 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ec926d3b5834e2e8ec5831a65685bad", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/82 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cb99a7a88c1249b5b329c772a1124aff", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/27 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "31da8b2184214039973af8a61bf78ac6", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/83 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "89d02545dece44b3a05098b38b8003aa", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/117 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["extracted = []\n", "\n", "for log in sample_pos[\"log\"][:10]:\n", "    extracted.append((extract(log, \"pytest\", threshold=t, chunk_size=30), log))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.27131566508125965"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["np.mean(\n", "    [\n", "        len(log_pytest.split(\"\\n\")) / len(log.split(\"\\n\"))\n", "        for log_pytest, log in extracted\n", "    ]\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
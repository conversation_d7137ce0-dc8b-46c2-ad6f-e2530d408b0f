{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["!pip install compress-pickle -q"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.utils import k8s_session\n", "from compress_pickle import loads as compressed_loads\n", "\n", "data_path = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v7/stage4_with_hydrated_commits\"\n", "\n", "\n", "def create_spark(max_workers: int = 5):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "            # add lots of memory\n", "            \"spark.executor.pyspark.memory\": \"1050G\",\n", "        },\n", "    )\n", "    return spark\n", "\n", "\n", "# import dataclass above\n", "\n", "spark = create_spark()\n", "\n", "spark_df = spark.read.parquet(data_path)\n", "\n", "# grab a sample to look at\n", "df = spark_df.sample(fraction=1.0, seed=42).limit(10).to<PERSON>andas()\n", "\n", "# iterate over over each row and unpickle it with compressed_loads(..., compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["problems = (\n", "    df[\"problems\"]\n", "    .apply(lambda x: compressed_loads(x, compression=\"gzip\"))\n", "    .explode()\n", "    .to_list()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.autofix.logs_extractor import (\n", "    LogExtractor,\n", ")\n", "\n", "log_extractor = LogExtractor()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log = log_extractor.extract(problems[0].logs[0], problems[0].breaking_change)\n", "print(log)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
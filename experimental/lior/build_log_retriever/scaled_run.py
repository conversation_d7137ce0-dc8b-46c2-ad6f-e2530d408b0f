from research.data.spark.utils import k8s_session
from compress_pickle import loads as compressed_loads
from functools import partial
from compress_pickle import dumps as compressed_dumps
from research.data.spark.pipelines.utils import map_parquet
import pandas as pd
from research.autofix.logs_extractor import (
    LogExtractor,
)
from unidiff.errors import UnidiffParseError

data_path = "/mnt/efs/spark-data/user/colin/bugfix_localization_model/v7/stage4_with_hydrated_commits"
output_path = (
    "/mnt/efs/spark-data/user/colin/bugfix_localization_model/v11/stage5_with_logs"
)


def create_spark(max_workers: int = 100):
    spark = k8s_session(
        max_workers=max_workers,
        # gpu_type=["A40", "RTX_A6000", "RTX_A5000"],
        gpu_type="H100_NVLINK_80GB",
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.task.maxFailures": "10",
            # add lots of memory
            "spark.executor.pyspark.memory": "1050G",
            "spark.task.cpus": "5",
        },
        ephemeral_storage_gb=128,
    )
    return spark


spark = create_spark()


def log_extractor_row(problems, log_extractor: LogExtractor):
    problems = compressed_loads(problems, compression="gzip")
    for problem in problems:
        try:
            problem.logs = [
                log_extractor.extract(
                    problem.logs[0], problem.breaking_change, batch_size=4
                )
            ]
        except UnidiffParseError:
            print(f"Error parsing diff for row: {problem.logs_sha}")

    return compressed_dumps(problems, compression="gzip")


def log_extractor_batch(batch):
    global cached_log_extractor
    cached_log_extractor = LogExtractor()
    batch["problems"] = batch.apply(
        lambda row: log_extractor_row(row["problems"], cached_log_extractor), axis=1
    )
    return batch


map_parquet.apply_pandas(
    spark,
    log_extractor_batch,
    input_path=data_path,
    output_path=output_path,
    task_info_location=output_path + ".logs",
    input_columns=["repo_id", "problems"],
    timeout=72000,  # 1 hour is more than enough for this stage.
    ignore_error=True,
    timing_run=False,
    allow_resume=True,
)

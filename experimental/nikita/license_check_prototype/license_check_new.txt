[
	{
		"project": "/home/<USER>/new_test_dir_1",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_2",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_7",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_13",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_22",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_26",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_29",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_33",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_35",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_14",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_53",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_57",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_34",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_56",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_51",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_21",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_23",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_3",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_40",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_46",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_58",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_63",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_38",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_50",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_0",
		"matches": [
			{
				"license": "deprecated_GPL-3.0+",
				"confidence": 0.99498117,
				"file": "LICENSE"
			},
			{
				"license": "GPL-3.0-only",
				"confidence": 0.99480194,
				"file": "LICENSE"
			},
			{
				"license": "GPL-3.0-or-later",
				"confidence": 0.99480194,
				"file": "LICENSE"
			},
			{
				"license": "deprecated_GPL-3.0",
				"confidence": 0.99480194,
				"file": "LICENSE"
			},
			{
				"license": "AGPL-3.0-only",
				"confidence": 0.86566895,
				"file": "LICENSE"
			},
			{
				"license": "AGPL-3.0-or-later",
				"confidence": 0.86566895,
				"file": "LICENSE"
			},
			{
				"license": "deprecated_AGPL-3.0",
				"confidence": 0.86566895,
				"file": "LICENSE"
			},
			{
				"license": "SSPL-1.0",
				"confidence": 0.7874001,
				"file": "LICENSE"
			},
			{
				"license": "LGPL-3.0-only",
				"confidence": 0.7779172,
				"file": "LICENSE"
			},
			{
				"license": "LGPL-3.0-or-later",
				"confidence": 0.7779172,
				"file": "LICENSE"
			},
			{
				"license": "deprecated_LGPL-3.0",
				"confidence": 0.7779172,
				"file": "LICENSE"
			},
			{
				"license": "deprecated_LGPL-3.0+",
				"confidence": 0.777738,
				"file": "LICENSE"
			}
		]
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_62",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_64",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_17",
		"matches": [
			{
				"license": "CC0-1.0",
				"confidence": 1,
				"file": "LICENSE"
			}
		]
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_25",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_54",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_19",
		"matches": [
			{
				"license": "Apache-2.0",
				"confidence": 0.9876943,
				"file": "LICENSE"
			},
			{
				"license": "ECL-2.0",
				"confidence": 0.90077823,
				"file": "LICENSE"
			},
			{
				"license": "SHL-0.51",
				"confidence": 0.81144345,
				"file": "LICENSE"
			},
			{
				"license": "SHL-0.5",
				"confidence": 0.8107932,
				"file": "LICENSE"
			}
		]
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_55",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_28",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_42",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_61",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_43",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_6",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_47",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_4",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_48",
		"matches": [
			{
				"license": "GPL-3.0-or-later",
				"confidence": 0.9935426,
				"file": "LICENSE.md"
			},
			{
				"license": "GPL-3.0-only",
				"confidence": 0.9935426,
				"file": "LICENSE.md"
			},
			{
				"license": "deprecated_GPL-3.0",
				"confidence": 0.9935426,
				"file": "LICENSE.md"
			},
			{
				"license": "deprecated_GPL-3.0+",
				"confidence": 0.9928251,
				"file": "LICENSE.md"
			},
			{
				"license": "AGPL-3.0-or-later",
				"confidence": 0.86665463,
				"file": "LICENSE.md"
			},
			{
				"license": "deprecated_AGPL-3.0",
				"confidence": 0.86665463,
				"file": "LICENSE.md"
			},
			{
				"license": "AGPL-3.0-only",
				"confidence": 0.86665463,
				"file": "LICENSE.md"
			},
			{
				"license": "SSPL-1.0",
				"confidence": 0.7879724,
				"file": "LICENSE.md"
			},
			{
				"license": "deprecated_LGPL-3.0",
				"confidence": 0.77650225,
				"file": "LICENSE.md"
			},
			{
				"license": "LGPL-3.0-only",
				"confidence": 0.77650225,
				"file": "LICENSE.md"
			},
			{
				"license": "LGPL-3.0-or-later",
				"confidence": 0.77650225,
				"file": "LICENSE.md"
			},
			{
				"license": "deprecated_LGPL-3.0+",
				"confidence": 0.77542603,
				"file": "LICENSE.md"
			}
		]
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_30",
		"matches": [
			{
				"license": "MIT",
				"confidence": 1,
				"file": "LICENSE"
			},
			{
				"license": "JSON",
				"confidence": 0.939759,
				"file": "LICENSE"
			},
			{
				"license": "MIT-0",
				"confidence": 0.84662575,
				"file": "LICENSE"
			},
			{
				"license": "MIT-feh",
				"confidence": 0.84146345,
				"file": "LICENSE"
			},
			{
				"license": "MIT-advertising",
				"confidence": 0.8109756,
				"file": "LICENSE"
			},
			{
				"license": "X11-distribute-modifications-variant",
				"confidence": 0.45454544,
				"file": "LICENSE"
			}
		]
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_5",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_32",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_59",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_60",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_31",
		"matches": [
			{
				"license": "MIT",
				"confidence": 1,
				"file": "LICENSE"
			},
			{
				"license": "JSON",
				"confidence": 0.939759,
				"file": "LICENSE"
			},
			{
				"license": "MIT-0",
				"confidence": 0.84662575,
				"file": "LICENSE"
			},
			{
				"license": "MIT-feh",
				"confidence": 0.84146345,
				"file": "LICENSE"
			},
			{
				"license": "MIT-advertising",
				"confidence": 0.8109756,
				"file": "LICENSE"
			},
			{
				"license": "X11-distribute-modifications-variant",
				"confidence": 0.45454544,
				"file": "LICENSE"
			}
		]
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_16",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_49",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_39",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_20",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_12",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_37",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_27",
		"matches": [
			{
				"license": "MIT",
				"confidence": 1,
				"file": "README.md"
			},
			{
				"license": "MIT-advertising",
				"confidence": 0.5,
				"file": "README.md"
			},
			{
				"license": "AML",
				"confidence": 0.5,
				"file": "README.md"
			},
			{
				"license": "MIT-0",
				"confidence": 0.5,
				"file": "README.md"
			},
			{
				"license": "MIT-enna",
				"confidence": 0.5,
				"file": "README.md"
			},
			{
				"license": "MIT-feh",
				"confidence": 0.5,
				"file": "README.md"
			},
			{
				"license": "MIT-CMU",
				"confidence": 0.5,
				"file": "README.md"
			},
			{
				"license": "MIT-open-group",
				"confidence": 0.33333334,
				"file": "README.md"
			},
			{
				"license": "MIT-Modern-Variant",
				"confidence": 0.33333334,
				"file": "README.md"
			}
		]
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_18",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_52",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_36",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_44",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_11",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_24",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_10",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_41",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_45",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_15",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_9",
		"error": "no license file was found"
	}
]
[
	{
		"project": "/home/<USER>/new_test_dir_8",
		"matches": [
			{
				"license": "Apache-2.0",
				"confidence": 0.987055,
				"file": "LICENSE"
			},
			{
				"license": "ECL-2.0",
				"confidence": 0.9001944,
				"file": "LICENSE"
			},
			{
				"license": "SHL-0.51",
				"confidence": 0.8109162,
				"file": "LICENSE"
			},
			{
				"license": "SHL-0.5",
				"confidence": 0.8102664,
				"file": "LICENSE"
			}
		]
	}
]

# License Check Prototype

This is a prototype for checking the license of a request.

It's not "production ready" but is a functional POC.

It currently operates on a json file containing a collection of events.
The example json file was created with this query:

// SELECT raw_json FROM `system-services-prod.prod_request_insight_full_export_dataset.request_event`
// WHERE tenant = "i0-vanguard0" AND event_type = "completion_host_request"
// AND request_id IN
//   (SELECT request_id FROM
//     (SELECT user_id, time, request_id
//     FROM (
//       SELECT user_id,
//             time,
//             request_id,
//             ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY time DESC) AS row_num
//       FROM `system-services-prod.us_prod_request_insight_analytics_dataset.customers_request_metadata`
//       WHERE tenant = "i0-vanguard0"
//     )
//     WHERE row_num = 1
//     LIMIT 100));

And then run like:
`go run nikita/license_check_prototype/main.go -file /home/<USER>/files/example_licsense_job.json`

An updated pipeline would want to access files from the GCS bucket instead.

The logic is then:
1. Extract the json
2. For each json request (parallelized):
    a. Extract the blob names from the json
    b. Filter for only blob names with some possible license file in their name
    c. Download those blobs (parallelized)
    d. Run the license check on a temporary directory containing the blobs
    e. Print the results (and save to a temp directory)

An updated pipeline would probably want to save individual blob results
to a database. Probably also want to not create an actual temporary file system.
Would want to save results to a database instead of just printing them. Etc.

But this is a prototype/hopefully enough to get a pipeline started.

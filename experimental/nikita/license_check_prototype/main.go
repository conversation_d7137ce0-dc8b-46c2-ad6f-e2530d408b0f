package main

import (
	"context"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"

	"github.com/rs/zerolog/log" //nolint:staticcheck
	"google.golang.org/api/iterator"

	"cloud.google.com/go/storage"
	"github.com/go-enry/go-license-detector/v4/licensedb"
)

var FILE_NAMES = []string{
	"LICENSE",
	"LICENSE.md",
	"LICENSE.txt",
	"LICENSE.rst",
	"license.txt",
	"license",
	"COPYING",
	"COPYING.md",
	"COPYING.txt",
	"COPYING.rst",
	"COPYRIGHT",
	"COPYRIGHT.md",
	"COPYRIGHT.txt",
	"COPYRIGHT.rst",
	"NOTICE",
	"README.md",
	"README",
	"README.rst",
	"README.txt",
	"README.markdown",
	"readme.md",
	"readme.txt",
	"readme",
	"readme.rst",
	"README.asciidoc",
	"README.adoc",
	"readme.asciidoc",
	"readme.adoc",
}

var vanguardBucketExportName = "augment-blob-exporter-i0-prod"

// cached with lazy init for performance. close need not be called on program exit
var client *storage.Client

func getGCSStorageClient() *storage.Client {
	if client == nil {
		ctx := context.Background()
		var err error
		client, err = storage.NewClient(ctx)
		if err != nil {
			log.Fatal().Msgf("Error creating GCS client: %v", err)
		}
	}
	return client
}

func base64ToHex(b64 string) (string, error) {
	// Decode the base64 string
	data, err := base64.StdEncoding.DecodeString(b64)
	if err != nil {
		return "", fmt.Errorf("error decoding base64: %v", err)
	}

	// Convert to hexadecimal
	hexStr := hex.EncodeToString(data)

	return hexStr, nil
}

func getBlobContents(blob_name string) []byte {
	ctx := context.Background()
	client := getGCSStorageClient()

	bkt := client.Bucket(vanguardBucketExportName)

	rc, err := bkt.Object("blobs/" + blob_name).NewReader(ctx)
	if err != nil {
		log.Error().Msgf("Error creating blob object reader: %v", err)
		return nil
	}
	defer rc.Close()
	body, err := io.ReadAll(rc)
	if err != nil {
		log.Error().Msgf("Error reading blob: %v", err)
		return nil
	}
	return body
}

func getBlobNamesFromCheckpoint(checkpoint_id string) []string {
	if checkpoint_id == "" {
		return []string{}
	}

	ctx := context.Background()
	client := getGCSStorageClient()

	bkt := client.Bucket(vanguardBucketExportName)

	rc, err := bkt.Object("checkpoints/" + checkpoint_id).NewReader(ctx)
	if err != nil {
		log.Error().Msgf("Error creating checkpoint object reader: %v", err)
		return []string{}
	}
	defer rc.Close()
	body, err := io.ReadAll(rc)
	if err != nil {
		log.Error().Msgf("Error reading checkpoint: %v", err)
		return []string{}
	}
	var blobs []string
	err = json.Unmarshal(body, &blobs)
	if err != nil {
		log.Error().Msgf("Error unmarshalling checkpoint: %v", err)
		return []string{}
	}
	return blobs
}

func getBlobPath(blob_name string) string {
	ctx := context.Background()
	client := getGCSStorageClient()

	bkt := client.Bucket(vanguardBucketExportName)
	query := &storage.Query{
		Prefix: "blobs/" + blob_name,
	}
	query.SetAttrSelection([]string{"Metadata", "Name"})

	it := bkt.Objects(ctx, query)
	path := ""
	for {
		attrs, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Error().Msgf("Error: %v", err)
			return ""
		}
		if attrs == nil {
			log.Error().Msgf("nil attrs for %s", blob_name)
			return ""
		}
		if attrs.Metadata == nil {
			log.Error().Msgf("nil metadata for %s", blob_name)
			return ""
		}
		if attrs.Name == "" {
			log.Error().Msgf("empty name for %s", blob_name)
			return ""
		}
		if attrs.Metadata["path"] == "" {
			log.Error().Msgf("empty path for %s", blob_name)
			return ""
		}
		path = attrs.Metadata["path"]
	}
	return path
}

func getAllBlobNamesInRequest(raw_json string, index int) ([]string, error) {
	// request is a list of other json objects
	var result map[string]interface{}
	err := json.Unmarshal([]byte(raw_json), &result)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling json: %v", err)
	}
	blobs := result["blobs"].(map[string]interface{})
	if blobs == nil {
		return nil, fmt.Errorf("error unmarshalling json: %v", err)
	}
	blobs_to_return := []string{}
	if blobs["added"] != nil {
		added := blobs["added"].([]interface{})

		for _, blob := range added {
			blob_name := blob.(string)

			hexStr, err := base64ToHex(blob_name)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
				return nil, err
			}
			blobs_to_return = append(blobs_to_return, hexStr)
		}
	}

	baseline_checkpoint_id := ""
	if blobs["baseline_checkpoint_id"] != nil {
		baseline_checkpoint_id = blobs["baseline_checkpoint_id"].(string)
	}

	more_blobs := getBlobNamesFromCheckpoint(baseline_checkpoint_id)
	blobs_to_return = append(blobs_to_return, more_blobs...)

	return blobs_to_return, nil
}

func filterBlobByLicensePath(blobsChan chan<- string, blobs []string) {
	for _, blob_name := range blobs {
		path := getBlobPath(blob_name)
		if path == "" {
			// Possible if the blob is not in the bucket (deleted, for instance)
			continue
		}
		for _, file_name := range FILE_NAMES {
			if strings.Contains(path, file_name) {
				blobsChan <- blob_name
			}
		}
	}
	close(blobsChan)
}

func RunLicenseFiltering(blobs []string, temp_dir string) {
	blobsChan := make(chan string)
	// stream back the blobs that match the license file names
	// operate in parallel on those (loop below)
	go filterBlobByLicensePath(blobsChan, blobs)

	os.Mkdir(temp_dir, 0o700)
	defer os.RemoveAll(temp_dir)

	var wg sync.WaitGroup

loop:
	for {
		select {
		case blob_name, ok := <-blobsChan:
			if !ok {
				break loop
			}
			wg.Add(1)
			go func() {
				defer wg.Done()
				path := getBlobPath(blob_name)
				if path == "" {
					log.Fatal().Msgf("empty path for %s\n", blob_name)
				}
				content := getBlobContents(blob_name)
				if content == nil {
					log.Error().Msgf("empty content for %s", blob_name)
					return
				}
				dir := filepath.Dir(temp_dir + "/" + path)
				err := os.MkdirAll(dir, 0o700)
				if err != nil {
					log.Fatal().Msgf("Error: %v\n", err)
				}
				err = os.WriteFile(temp_dir+"/"+path, content, 0o700)
				if err != nil {
					log.Fatal().Msgf("Error: %v\n", err)
				}
			}()
		}
	}

	wg.Wait()
	results := licensedb.Analyse(temp_dir)
	bytes, err := json.MarshalIndent(results, "", "\t")
	if err != nil {
		fmt.Printf("could not encode result to JSON: %v\n", err)
	}
	fmt.Println(string(bytes))
}

func main() {
	// This is a prototype for checking the license of a request.
	//
	// The prototype is a simple check that the request does not contain any GPL code.
	//
	// It is not intended to be used in production.

	// Open the file
	filePath := flag.String("file", "", "Path to the license job JSON file")
	flag.Parse()

	if *filePath == "" {
		log.Fatal().Msg("Please provide a file path using the -file flag")
	}

	fileContent, err := os.ReadFile(*filePath)
	if err != nil {
		log.Error().Msgf("Error opening file: %v", err)
		return
	}

	// extract the json
	var result1 []interface{}
	err = json.Unmarshal([]byte(string(fileContent)), &result1)
	if err != nil {
		log.Error().Msgf("Error unmarshalling json: %v", err)
		return
	}

	var wg sync.WaitGroup
	for i := 0; i < len(result1); i++ {
		raw_json := result1[i].(map[string]interface{})["raw_json"].(string)
		blobs, err := getAllBlobNamesInRequest(raw_json, i)
		if err != nil {
			log.Error().Msgf("Error: %v", err)
			continue
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			RunLicenseFiltering(blobs, "/home/<USER>/new_test_dir_"+strconv.Itoa(i))
			log.Info().Msgf("Done with %d", i)
		}()
	}
	wg.Wait()
}

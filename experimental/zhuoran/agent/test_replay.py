import difflib

from research.tools.chat_replay.replay_utils import decode_prompt, render_prompt

with open("/home/<USER>/t.md", "r") as f:
    original_string = f.read()
with open("/home/<USER>/t1.md", "r") as f:
    original_string1 = f.read()

prompt_output = decode_prompt(original_string)
prompt_output1 = decode_prompt(original_string1)

prompt_rendering = render_prompt(prompt_output)
prompt_rendering1 = render_prompt(prompt_output1)

request_node_count = 0
response_node_count = 0
for exchange in prompt_output.chat_history:
    if isinstance(exchange.request_message, list):
        request_node_count += len(exchange.request_message)
    if isinstance(exchange.response_text, list):
        response_node_count += len(exchange.response_text)
print(f"Request node count: {request_node_count}")
print(f"Response node count: {response_node_count}")

with open("/home/<USER>/a.md", "w") as f:
    to_write = original_string + "\n\n\n" + original_string1
    for line in to_write.splitlines():
        line = line.strip()
        if line:
            f.write(f"{line}\n")
with open("/home/<USER>/b.md", "w") as f:
    to_write = prompt_rendering + "\n\n\n" + prompt_rendering1
    for line in to_write.splitlines():
        line = line.strip()
        if line:
            f.write(f"{line}\n")

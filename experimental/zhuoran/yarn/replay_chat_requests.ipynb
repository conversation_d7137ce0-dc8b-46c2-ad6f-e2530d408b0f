{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay Chat requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REQUEST_ID = \"\"\"\n", "ed01a8cf-5cb4-4dba-91cf-4286f8c9a41d\n", "\"\"\".strip()\n", "REQUEST_ID"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import base64\n", "\n", "from google.cloud import storage, bigquery\n", "\n", "from base.datasets.tenants import DOGFOOD, DOGFOOD_SHARD\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache\n", "from base.prompt_format.common import Exchange\n", "from research.eval.harness.systems.remote_chat_system import RemoteChatSystem\n", "from research.core.chat_prompt_input import ResearchChatPromptInput\n", "from research.core.artifacts import collect_artifacts\n", "from research.core.types import Document"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def get_caches(tenant):\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)\n", "    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)\n", "    blob_cache_size_bytes = 2**30\n", "    blob_cache_num_threads = 32\n", "    blob_cache = GCSBlobCache(\n", "        blob_bucket,\n", "        tenant.blob_bucket_prefix,\n", "        max_size_bytes=blob_cache_size_bytes,\n", "        num_threads=blob_cache_num_threads,\n", "    )\n", "    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)\n", "    checkpoint_cache = GCSCheckpointCache(\n", "        checkpoint_bucket,\n", "        tenant.checkpoint_bucket_prefix,\n", "        blob_cache_size_bytes,\n", "        num_threads=blob_cache_num_threads,\n", "    )\n", "    return blob_cache, checkpoint_cache"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def parse_row(\n", "    row: bigquery.Row, blob_cache: GCSBlobCache, checkpoint_cache: GCSCheckpointCache\n", ") -> dict:\n", "    request_json = row[\"request_json\"]\n", "\n", "    prefix = request_json.get(\"prefix\", \"\")\n", "    suffix = request_json.get(\"suffix\", \"\")\n", "    path = request_json.get(\"path\", \"\")\n", "    selected_code = request_json.get(\"selected_code\", \"\")\n", "    prefix_begin = request_json.get(\"prefix_begin\", None)\n", "    suffix_end = request_json.get(\"suffix_end\", None)\n", "    blob_name = request_json.get(\"blob_name\", None)\n", "    blobs = request_json.get(\"blobs\", None)\n", "    user_guided_blobs = request_json.get(\"user_guided_blobs\", None)\n", "    context_code_exchange_request_id = request_json.get(\n", "        \"context_code_exchange_request_id\", None\n", "    )\n", "    if blobs is not None:\n", "        checkpoint_id = blobs.pop(\"baseline_checkpoint_id\", None)\n", "        if checkpoint_id is None:\n", "            checkpoint_blob_names = set()\n", "        else:\n", "            cached_checkpoint = checkpoint_cache.get([checkpoint_id])[0]\n", "            if cached_checkpoint is None:\n", "                checkpoint_blob_names = set()\n", "            else:\n", "                checkpoint_blob_names = set(\n", "                    checkpoint_cache.get([checkpoint_id])[0].blob_names\n", "                )\n", "        added_blob_names = set(\n", "            [base64.b64decode(blob).hex() for blob in blobs[\"added\"]]\n", "            if \"added\" in blobs\n", "            else []\n", "        )\n", "        deleted_blob_names = set(\n", "            [base64.b64decode(blob).hex() for blob in blobs[\"deleted\"]]\n", "            if \"deleted\" in blobs\n", "            else []\n", "        )\n", "        blob_names = sorted(\n", "            list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)\n", "        )\n", "        blobs = blob_cache.get(blob_names)\n", "\n", "        documents = [\n", "            Document(id=blob_name, text=blob.content, path=str(blob.path))\n", "            for blob_name, blob in zip(blob_names, blobs)\n", "            if blob is not None\n", "        ]\n", "\n", "    chat_history = request_json.get(\"chat_history\", [])\n", "\n", "    return_dict = {\n", "        \"selected_code\": selected_code,\n", "        \"message\": request_json[\"message\"],\n", "        \"prefix\": prefix,\n", "        \"suffix\": suffix,\n", "        \"path\": path,\n", "        \"blob_name\": blob_name,\n", "        \"prefix_begin\": prefix_begin,\n", "        \"suffix_end\": suffix_end,\n", "        \"blobs\": blobs,\n", "        \"documents\": documents,\n", "        \"blob_names\": blob_names,\n", "        \"chat_history\": chat_history,\n", "        \"user_guided_blobs\": user_guided_blobs,\n", "        \"context_code_exchange_request_id\": context_code_exchange_request_id,\n", "    }\n", "    return return_dict\n", "\n", "\n", "def download_request(request_id: str, tenant):\n", "    query = f\"\"\"\n", "        SELECT\n", "            *,\n", "        FROM `system-services-prod.staging_request_insight_full_export_dataset.chat_host_request`\n", "        WHERE request_id = '{request_id}'\n", "    \"\"\"\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)\n", "\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "    if not rows:\n", "        return None\n", "\n", "    assert len(rows) == 1\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    # make row json serializable\n", "    row = {\n", "        \"request_id\": row.request_id,\n", "        \"request_json\": row.raw_json[\"request\"],\n", "    }\n", "    return row\n", "\n", "\n", "def get_request_inputs(request_id: str, tenants_and_caches):\n", "    for tenant, blob_cache, checkpoint_cache in tenants_and_caches:\n", "        row = download_request(request_id, tenant)\n", "        if row:\n", "            return parse_row(row, blob_cache, checkpoint_cache), tenant\n", "    raise ValueError(f\"request {request_id} not found\")\n", "\n", "\n", "tenants_and_caches = []\n", "for tenant in [DOGFOOD, DOGFOOD_SHARD]:\n", "    blob_cache, checkpoint_cache = get_caches(tenant)\n", "    tenants_and_caches.append((tenant, blob_cache, checkpoint_cache))\n", "\n", "chat_sample_data, tenant = get_request_inputs(REQUEST_ID, tenants_and_caches)\n", "print(f\"Got data from {tenant.name}\")\n", "chat_history = [\n", "    Exchange(\n", "        request_message=chat_request[\"request_message\"],\n", "        response_text=chat_request[\"response_text\"],\n", "    )\n", "    for chat_request in chat_sample_data[\"chat_history\"]\n", "]\n", "current_blobs = [chat_sample_data[\"blob_name\"]] if chat_sample_data[\"blob_name\"] else []\n", "original_user_guided_blobs = chat_sample_data[\"user_guided_blobs\"] or []\n", "user_guided_blobs = list(set(current_blobs + original_user_guided_blobs))\n", "model_input = ResearchChatPromptInput(\n", "    path=chat_sample_data[\"path\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    message=chat_sample_data[\"message\"],\n", "    chat_history=chat_history,\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    retrieved_chunks=[],\n", "    doc_ids=chat_sample_data[\"blob_names\"],\n", "    user_guided_blobs=user_guided_blobs,\n", "    context_code_exchange_request_id=chat_sample_data[\n", "        \"context_code_exchange_request_id\"\n", "    ],\n", ")\n", "chat_sample_data[\"message\"]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def generate_chat_response(url, model_name):\n", "    remote_config = {\n", "        \"client\": {\"url\": url},\n", "        \"model_name\": model_name,\n", "    }\n", "    remote_chat = RemoteChatSystem.from_yaml_config(\n", "        remote_config,\n", "    )\n", "    remote_chat.load()\n", "    remote_chat.add_docs(chat_sample_data[\"documents\"])\n", "\n", "    with collect_artifacts() as collector_manager:\n", "        chat_response = remote_chat.generate(model_input)\n", "        artifacts = collector_manager.get_artifacts()\n", "    request_id = artifacts[0][\"request_id\"]\n", "    return chat_response, request_id"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["responses = {}\n", "request_ids = {}"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["DOGFOOD_SHARD_URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "DOGFOOD_SHARD_RI_LINK_TEMPLATE = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"dogfood_shard\"\n", "responses[label], request_ids[label] = generate_chat_response(DOGFOOD_SHARD_URL, \"\")\n", "print(DOGFOOD_SHARD_RI_LINK_TEMPLATE.format(request_id=request_ids[label]))\n", "print(responses[label])\n", "print()\n", "print(responses[label].generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["responses[\"dogfood_shard\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"v10\"\n", "responses[label], request_ids[label] = generate_chat_response(\n", "    DOGFOOD_SHARD_URL, \"binks-ug-chatanol1-18-reranker-chat\"\n", ")\n", "print(DOGFOOD_SHARD_RI_LINK_TEMPLATE.format(request_id=request_ids[label]))\n", "print(responses[label])\n", "print()\n", "print(responses[label].generated_text)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["DEV_URL = \"https://dev-zhuoran.us-central.api.augmentcode.com/\"\n", "DEV_RI_LINK_TEMPLATE = \"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{request_id}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"claude\"\n", "responses[label], request_ids[label] = generate_chat_response(DEV_URL, \"\")\n", "print(DEV_RI_LINK_TEMPLATE.format(request_id=request_ids[label]))\n", "print(responses[label])\n", "print()\n", "print(responses[label].generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "from typing import Sequence\n", "\n", "import numpy as np\n", "import pytest\n", "import torch\n", "\n", "from base.fastforward import positional_embeddings, positional_embeddings_test_utils"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["head_dim = 1024\n", "rotary_theta = 500_000\n", "max_position_embeddings = 131_072\n", "rotary_scaling_factor = 8.0\n", "\n", "config = positional_embeddings.RotaryConfig(\n", "    rotary_ratio=1.0,\n", "    rotary_theta=rotary_theta,\n", "    max_position_embeddings=max_position_embeddings,\n", "    ext_config=positional_embeddings.Llama3_1ExtensionConfig(\n", "        rotary_scaling_factor=rotary_scaling_factor,\n", "    ),\n", ")\n", "fused_rope = positional_embeddings.FusedRotaryEmbedding(\n", "    head_dim=head_dim,\n", "    max_seq_len=max_position_embeddings,\n", "    config=config,\n", ")\n", "\n", "freqs_cis_ref, outs_ref = positional_embeddings_test_utils.precompute_freqs_cis(\n", "    head_dim,\n", "    max_position_embeddings,\n", "    theta=rotary_theta,\n", "    use_scaled=True,\n", ")\n", "cos_ref = freqs_cis_ref.real\n", "sin_ref = freqs_cis_ref.imag\n", "\n", "cos_yarn = fused_rope.freqs_cos\n", "sin_yarn = fused_rope.freqs_sin\n", "outs_yarn = fused_rope.outs"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['dim', 'end', 'theta', 'use_scaled', 'device', 'prescale_freqs', 'pure_freqs', 't'])"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["outs_ref.keys()"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1024 1024\n", "131072 131072\n", "500000 500000\n", "True True\n", "None None\n"]}], "source": ["print(outs_ref[\"dim\"], outs_yarn[\"dim\"])\n", "print(outs_ref[\"end\"], outs_yarn[\"end\"])\n", "print(outs_ref[\"theta\"], outs_yarn[\"theta\"])\n", "print(outs_ref[\"use_scaled\"], outs_yarn[\"use_scaled\"])\n", "print(outs_ref[\"device\"], outs_yarn[\"device\"])"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(True)"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["(cos_yarn == cos_ref).all()"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([1.0000e+00, 9.9719e-01, 9.9439e-01, 9.9160e-01, 9.8882e-01, 9.8604e-01,\n", "         9.8328e-01, 9.8052e-01, 9.7776e-01, 9.7502e-01, 9.7228e-01, 9.6955e-01,\n", "         9.6683e-01, 9.6412e-01, 9.6141e-01, 9.5871e-01, 9.5602e-01, 9.5334e-01,\n", "         9.5066e-01, 9.4800e-01, 9.4534e-01, 9.4268e-01, 9.4004e-01, 9.3740e-01,\n", "         9.3477e-01, 9.3214e-01, 9.2953e-01, 9.2692e-01, 9.2432e-01, 9.2172e-01,\n", "         9.1913e-01, 9.1655e-01, 9.1398e-01, 9.1142e-01, 9.0886e-01, 9.0631e-01,\n", "         9.0376e-01, 9.0123e-01, 8.9870e-01, 8.9617e-01, 8.9366e-01, 8.9115e-01,\n", "         8.8865e-01, 8.8616e-01, 8.8367e-01, 8.8119e-01, 8.7871e-01, 8.7625e-01,\n", "         8.7379e-01, 8.7134e-01, 8.6889e-01, 8.6645e-01, 8.6402e-01, 8.6159e-01,\n", "         8.5918e-01, 8.5676e-01, 8.5436e-01, 8.5196e-01, 8.4957e-01, 8.4719e-01,\n", "         8.4481e-01, 8.4244e-01, 8.4007e-01, 8.3771e-01, 8.3536e-01, 8.3302e-01,\n", "         8.3068e-01, 8.2835e-01, 8.2602e-01, 8.2370e-01, 8.2139e-01, 8.1909e-01,\n", "         8.1679e-01, 8.1450e-01, 8.1221e-01, 8.0993e-01, 8.0766e-01, 8.0539e-01,\n", "         8.0313e-01, 8.0087e-01, 7.9863e-01, 7.9638e-01, 7.9415e-01, 7.9192e-01,\n", "         7.8970e-01, 7.8748e-01, 7.8527e-01, 7.8307e-01, 7.8087e-01, 7.7868e-01,\n", "         7.7649e-01, 7.7431e-01, 7.7214e-01, 7.6997e-01, 7.6781e-01, 7.6566e-01,\n", "         7.6351e-01, 7.6136e-01, 7.5923e-01, 7.5709e-01, 7.5497e-01, 7.5285e-01,\n", "         7.5074e-01, 7.4863e-01, 7.4653e-01, 7.4443e-01, 7.4234e-01, 7.4026e-01,\n", "         7.3818e-01, 7.3611e-01, 7.3404e-01, 7.3198e-01, 7.2993e-01, 7.2788e-01,\n", "         7.2584e-01, 7.2380e-01, 7.2177e-01, 7.1974e-01, 7.1772e-01, 7.1571e-01,\n", "         7.1370e-01, 7.1170e-01, 7.0970e-01, 7.0771e-01, 7.0572e-01, 7.0374e-01,\n", "         7.0176e-01, 6.9979e-01, 6.9783e-01, 6.9587e-01, 6.9392e-01, 6.9197e-01,\n", "         6.9003e-01, 6.8809e-01, 6.8616e-01, 6.8423e-01, 6.8231e-01, 6.8040e-01,\n", "         6.7849e-01, 6.7658e-01, 6.7469e-01, 6.7279e-01, 6.7090e-01, 6.6902e-01,\n", "         6.6714e-01, 6.6527e-01, 6.6340e-01, 6.6154e-01, 6.5968e-01, 6.5783e-01,\n", "         6.5599e-01, 6.5414e-01, 6.5231e-01, 6.5048e-01, 6.4865e-01, 6.4683e-01,\n", "         6.4502e-01, 6.4321e-01, 6.4140e-01, 6.3960e-01, 6.3780e-01, 6.3601e-01,\n", "         6.3423e-01, 6.3245e-01, 6.3067e-01, 6.2890e-01, 6.2714e-01, 6.2538e-01,\n", "         6.2362e-01, 6.2187e-01, 6.2013e-01, 6.1839e-01, 6.1665e-01, 6.1492e-01,\n", "         6.1319e-01, 6.1147e-01, 6.0976e-01, 6.0804e-01, 6.0634e-01, 6.0464e-01,\n", "         6.0294e-01, 6.0125e-01, 5.9956e-01, 5.9788e-01, 5.9620e-01, 5.9452e-01,\n", "         5.9286e-01, 5.9119e-01, 5.8953e-01, 5.8788e-01, 5.8623e-01, 5.8458e-01,\n", "         5.8294e-01, 5.8131e-01, 5.7967e-01, 5.7805e-01, 5.7642e-01, 5.7481e-01,\n", "         5.7319e-01, 5.7158e-01, 5.6998e-01, 5.6838e-01, 5.6678e-01, 5.6519e-01,\n", "         5.6361e-01, 5.6203e-01, 5.6045e-01, 5.5887e-01, 5.5731e-01, 5.5574e-01,\n", "         5.5418e-01, 5.5263e-01, 5.5108e-01, 5.4953e-01, 5.4799e-01, 5.4645e-01,\n", "         5.4491e-01, 5.4338e-01, 5.4186e-01, 5.4034e-01, 5.3882e-01, 5.3731e-01,\n", "         5.3580e-01, 5.3430e-01, 5.3280e-01, 5.3130e-01, 5.2981e-01, 5.2832e-01,\n", "         5.2684e-01, 5.2536e-01, 5.2389e-01, 5.2242e-01, 5.2095e-01, 5.1949e-01,\n", "         5.1803e-01, 5.1658e-01, 5.1513e-01, 5.1368e-01, 5.1224e-01, 5.1080e-01,\n", "         5.0937e-01, 5.0794e-01, 5.0651e-01, 5.0509e-01, 5.0367e-01, 5.0226e-01,\n", "         5.0085e-01, 4.9944e-01, 4.9804e-01, 4.9664e-01, 4.9525e-01, 4.9386e-01,\n", "         4.9247e-01, 4.9109e-01, 4.8971e-01, 4.8834e-01, 4.8697e-01, 4.8560e-01,\n", "         4.8424e-01, 4.8288e-01, 4.8152e-01, 4.8017e-01, 4.7882e-01, 4.7748e-01,\n", "         4.7614e-01, 4.7480e-01, 4.7347e-01, 4.7214e-01, 4.7082e-01, 4.6949e-01,\n", "         4.6818e-01, 4.6686e-01, 4.6555e-01, 4.6425e-01, 4.6294e-01, 4.6164e-01,\n", "         4.6035e-01, 4.5906e-01, 4.5777e-01, 4.5648e-01, 4.5520e-01, 4.5392e-01,\n", "         4.5265e-01, 4.5138e-01, 4.5011e-01, 4.4885e-01, 4.4759e-01, 4.4633e-01,\n", "         4.4508e-01, 4.4383e-01, 4.4258e-01, 4.4134e-01, 4.4010e-01, 4.3887e-01,\n", "         4.3764e-01, 4.3641e-01, 4.3518e-01, 4.3396e-01, 4.3274e-01, 4.3153e-01,\n", "         4.3032e-01, 4.2911e-01, 4.2791e-01, 4.2670e-01, 4.2551e-01, 4.2431e-01,\n", "         4.2312e-01, 4.2193e-01, 4.2075e-01, 4.1957e-01, 4.1839e-01, 4.1722e-01,\n", "         4.1605e-01, 4.1488e-01, 4.1371e-01, 4.1255e-01, 4.1139e-01, 4.1024e-01,\n", "         4.0909e-01, 4.0794e-01, 4.0679e-01, 4.0565e-01, 4.0451e-01, 4.0338e-01,\n", "         4.0225e-01, 4.0112e-01, 3.9999e-01, 3.9887e-01, 3.9775e-01, 3.9663e-01,\n", "         3.9552e-01, 3.9441e-01, 3.9330e-01, 3.9220e-01, 3.9110e-01, 3.9000e-01,\n", "         3.8891e-01, 3.8781e-01, 3.8673e-01, 3.8564e-01, 3.8456e-01, 3.8348e-01,\n", "         3.8240e-01, 3.8133e-01, 3.8026e-01, 3.7919e-01, 3.7813e-01, 3.7706e-01,\n", "         3.7601e-01, 3.7495e-01, 3.7390e-01, 3.7285e-01, 3.7180e-01, 3.7076e-01,\n", "         3.6972e-01, 3.6868e-01, 3.6765e-01, 3.6661e-01, 3.6558e-01, 3.6456e-01,\n", "         3.6354e-01, 3.6252e-01, 3.6150e-01, 3.6048e-01, 3.5947e-01, 3.5846e-01,\n", "         3.5746e-01, 3.5645e-01, 3.5545e-01, 3.5445e-01, 3.5346e-01, 3.5247e-01,\n", "         3.5148e-01, 3.5049e-01, 3.4951e-01, 3.4853e-01, 3.4755e-01, 3.4657e-01,\n", "         3.4560e-01, 3.4463e-01, 3.4366e-01, 3.4270e-01, 3.4174e-01, 3.4078e-01,\n", "         3.3982e-01, 3.3887e-01, 3.3792e-01, 3.3697e-01, 3.3602e-01, 3.3508e-01,\n", "         3.3414e-01, 3.3320e-01, 3.3226e-01, 3.3133e-01, 3.3040e-01, 3.2947e-01,\n", "         3.2855e-01, 3.2763e-01, 3.2671e-01, 3.2579e-01, 3.2488e-01, 3.2396e-01,\n", "         3.2306e-01, 3.2215e-01, 3.2124e-01, 3.2034e-01, 3.1944e-01, 3.1855e-01,\n", "         3.1765e-01, 3.1676e-01, 3.1587e-01, 3.1499e-01, 3.1410e-01, 3.1322e-01,\n", "         3.1234e-01, 3.1146e-01, 3.1059e-01, 3.0972e-01, 3.0885e-01, 3.0798e-01,\n", "         3.0712e-01, 3.0626e-01, 3.0540e-01, 3.0454e-01, 3.0368e-01, 3.0283e-01,\n", "         3.0198e-01, 3.0113e-01, 3.0029e-01, 2.9945e-01, 2.9861e-01, 2.9777e-01,\n", "         2.9693e-01, 2.9610e-01, 2.9527e-01, 2.9444e-01, 2.9361e-01, 2.9279e-01,\n", "         2.9197e-01, 2.9115e-01, 2.9033e-01, 2.8951e-01, 2.8870e-01, 2.8789e-01,\n", "         2.8708e-01, 2.8628e-01, 2.8547e-01, 2.8467e-01, 2.8387e-01, 2.8308e-01,\n", "         2.8228e-01, 2.8149e-01, 2.8070e-01, 2.7991e-01, 2.7913e-01, 2.7834e-01,\n", "         2.7756e-01, 2.7678e-01, 2.7601e-01, 2.7523e-01, 2.7446e-01, 2.7369e-01,\n", "         2.7292e-01, 2.7215e-01, 2.7139e-01, 2.7063e-01, 2.6987e-01, 2.6911e-01,\n", "         2.6836e-01, 2.6760e-01, 2.6685e-01, 2.6610e-01, 2.6536e-01, 2.6461e-01,\n", "         2.6387e-01, 2.6313e-01, 2.6239e-01, 2.6165e-01, 2.6092e-01, 2.6019e-01,\n", "         2.5946e-01, 2.5873e-01, 2.5800e-01, 2.5728e-01, 2.5655e-01, 2.5583e-01,\n", "         2.5512e-01, 2.5440e-01, 2.5369e-01, 2.5297e-01, 2.5226e-01, 2.5156e-01,\n", "         2.5085e-01, 2.5015e-01, 2.4944e-01, 2.4874e-01, 2.4805e-01, 2.4735e-01,\n", "         2.4665e-01, 2.4596e-01, 2.4527e-01, 2.4458e-01, 2.4390e-01, 2.4321e-01,\n", "         2.4253e-01, 2.4185e-01, 2.4117e-01, 2.4049e-01, 2.3982e-01, 2.3915e-01,\n", "         2.3847e-01, 2.3780e-01, 2.3714e-01, 2.3647e-01, 2.3581e-01, 2.3515e-01,\n", "         2.3449e-01, 2.3383e-01, 2.3317e-01, 2.3252e-01, 2.3186e-01, 2.3121e-01,\n", "         2.3056e-01, 2.2992e-01, 2.2927e-01, 2.2863e-01, 2.2799e-01, 2.2735e-01,\n", "         2.2671e-01, 2.2607e-01, 2.2544e-01, 2.2481e-01, 2.2417e-01, 2.2355e-01,\n", "         2.2292e-01, 2.2229e-01, 2.2167e-01, 2.2105e-01, 2.2043e-01, 2.1981e-01,\n", "         2.1919e-01, 2.1857e-01, 2.1796e-01, 2.1735e-01, 2.1674e-01, 2.1613e-01,\n", "         2.1552e-01, 2.1492e-01, 2.1432e-01, 2.1371e-01, 2.1311e-01, 2.1252e-01,\n", "         2.1192e-01, 2.1133e-01, 2.1073e-01, 2.1014e-01, 2.0955e-01, 2.0896e-01,\n", "         2.0838e-01, 2.0779e-01, 2.0721e-01, 2.0663e-01, 2.0605e-01, 2.0547e-01,\n", "         2.0489e-01, 2.0432e-01, 2.0374e-01, 2.0317e-01, 2.0260e-01, 2.0203e-01,\n", "         2.0146e-01, 2.0090e-01, 2.0034e-01, 1.9977e-01, 1.9921e-01, 1.9865e-01,\n", "         1.9810e-01, 1.9754e-01, 1.9699e-01, 1.9643e-01, 1.9588e-01, 1.9533e-01,\n", "         1.9478e-01, 1.9424e-01, 1.9369e-01, 1.9315e-01, 1.9261e-01, 1.9206e-01,\n", "         1.9153e-01, 1.9099e-01, 1.9045e-01, 1.8992e-01, 1.8938e-01, 1.8885e-01,\n", "         1.8832e-01, 1.8779e-01, 1.8727e-01, 1.8674e-01, 1.8622e-01, 1.8569e-01,\n", "         1.8517e-01, 1.8465e-01, 1.8414e-01, 1.8362e-01, 1.8310e-01, 1.8259e-01,\n", "         1.8208e-01, 1.8157e-01, 1.8106e-01, 1.8055e-01, 1.8004e-01, 1.7954e-01,\n", "         1.7903e-01, 1.7853e-01, 1.7803e-01, 1.7753e-01, 1.7703e-01, 1.7653e-01,\n", "         1.7604e-01, 1.7554e-01, 1.7505e-01, 1.7456e-01, 1.7407e-01, 1.7358e-01,\n", "         1.7309e-01, 1.7261e-01, 1.7212e-01, 1.7164e-01, 1.7116e-01, 1.7068e-01,\n", "         1.7020e-01, 1.6972e-01, 1.6924e-01, 1.6877e-01, 1.6830e-01, 1.6782e-01,\n", "         1.6735e-01, 1.6688e-01, 1.6641e-01, 1.6595e-01, 1.6548e-01, 1.6502e-01,\n", "         1.6455e-01, 1.6409e-01, 1.6363e-01, 1.6317e-01, 1.6271e-01, 1.6226e-01,\n", "         1.6180e-01, 1.6135e-01, 1.6090e-01, 1.6044e-01, 1.5999e-01, 1.5954e-01,\n", "         1.5910e-01, 1.5865e-01, 1.5820e-01, 1.5776e-01, 1.5732e-01, 1.5688e-01,\n", "         1.5644e-01, 1.5600e-01, 1.5556e-01, 1.5512e-01, 1.5469e-01, 1.5425e-01,\n", "         1.5382e-01, 1.5339e-01, 1.5296e-01, 1.5253e-01, 1.5210e-01, 1.5167e-01,\n", "         1.5125e-01, 1.5082e-01, 1.5040e-01, 1.4998e-01, 1.4956e-01, 1.4914e-01,\n", "         1.4872e-01, 1.4830e-01, 1.4788e-01, 1.4747e-01, 1.4706e-01, 1.4664e-01,\n", "         1.4623e-01, 1.4582e-01, 1.4541e-01, 1.4500e-01, 1.4460e-01, 1.4419e-01,\n", "         1.4379e-01, 1.4338e-01, 1.4298e-01, 1.4258e-01, 1.4218e-01, 1.4178e-01,\n", "         1.4138e-01, 1.4098e-01, 1.4059e-01, 1.4019e-01, 1.3980e-01, 1.3941e-01,\n", "         1.3902e-01, 1.3863e-01, 1.3824e-01, 1.3785e-01, 1.3746e-01, 1.3708e-01,\n", "         1.3669e-01, 1.3631e-01, 1.3593e-01, 1.3554e-01, 1.3516e-01, 1.3478e-01,\n", "         1.3441e-01, 1.3403e-01, 1.3365e-01, 1.3328e-01, 1.3290e-01, 1.3253e-01,\n", "         1.3216e-01, 1.3179e-01, 1.3142e-01, 1.3105e-01, 1.3068e-01, 1.3031e-01,\n", "         1.2995e-01, 1.2958e-01, 1.2922e-01, 1.2886e-01, 1.2850e-01, 1.2813e-01,\n", "         1.2777e-01, 1.2742e-01, 1.2706e-01, 1.2670e-01, 1.2635e-01, 1.2599e-01,\n", "         1.2564e-01, 1.2529e-01, 1.2493e-01, 1.2458e-01, 1.2423e-01, 1.2388e-01,\n", "         1.2354e-01, 1.2319e-01, 1.2284e-01, 1.2250e-01, 1.2216e-01, 1.2181e-01,\n", "         1.2147e-01, 1.2113e-01, 1.2079e-01, 1.2045e-01, 1.2011e-01, 1.1978e-01,\n", "         1.1944e-01, 1.1910e-01, 1.1877e-01, 1.1844e-01, 1.1810e-01, 1.1777e-01,\n", "         1.1744e-01, 1.1711e-01, 1.1678e-01, 1.1646e-01, 1.1613e-01, 1.1580e-01,\n", "         1.1548e-01, 1.1515e-01, 1.1483e-01, 1.1451e-01, 1.1419e-01, 1.1387e-01,\n", "         1.1355e-01, 1.1323e-01, 1.1291e-01, 1.1259e-01, 1.1228e-01, 1.1196e-01,\n", "         1.1165e-01, 1.1133e-01, 1.1102e-01, 1.1071e-01, 1.1040e-01, 1.1009e-01,\n", "         1.0978e-01, 1.0947e-01, 1.0917e-01, 1.0886e-01, 1.0855e-01, 1.0825e-01,\n", "         1.0795e-01, 1.0764e-01, 1.0734e-01, 1.0704e-01, 1.0674e-01, 1.0644e-01,\n", "         1.0614e-01, 1.0584e-01, 1.0554e-01, 1.0525e-01, 1.0495e-01, 1.0466e-01,\n", "         1.0436e-01, 1.0407e-01, 1.0378e-01, 1.0349e-01, 1.0320e-01, 1.0291e-01,\n", "         1.0262e-01, 1.0233e-01, 1.0204e-01, 1.0176e-01, 1.0147e-01, 1.0119e-01,\n", "         1.0090e-01, 1.0062e-01, 1.0034e-01, 1.0006e-01, 9.9775e-02, 9.9495e-02,\n", "         9.9216e-02, 9.8938e-02, 9.8660e-02, 9.8383e-02, 9.8107e-02, 9.7831e-02,\n", "         9.7557e-02, 9.7283e-02, 9.7010e-02, 9.6738e-02, 9.6466e-02, 9.6195e-02,\n", "         9.5925e-02, 9.5656e-02, 9.5388e-02, 9.5120e-02, 9.4853e-02, 9.4587e-02,\n", "         9.4321e-02, 9.4056e-02, 9.3792e-02, 9.3529e-02, 9.3267e-02, 9.3005e-02,\n", "         9.2744e-02, 9.2484e-02, 9.2224e-02, 9.1965e-02, 9.1707e-02, 9.1450e-02,\n", "         9.1193e-02, 9.0937e-02, 9.0682e-02, 9.0427e-02, 9.0173e-02, 8.9920e-02,\n", "         8.9668e-02, 8.9416e-02, 8.9165e-02, 8.8915e-02, 8.8665e-02, 8.8416e-02,\n", "         8.8168e-02, 8.7921e-02, 8.7674e-02, 8.7428e-02, 8.7183e-02, 8.6938e-02,\n", "         8.6694e-02, 8.6451e-02, 8.6208e-02, 8.5966e-02, 8.5725e-02, 8.5484e-02,\n", "         8.5244e-02, 8.5005e-02, 8.4766e-02, 8.4528e-02, 8.4291e-02, 8.4054e-02,\n", "         8.3818e-02, 8.3583e-02, 8.3349e-02, 8.3115e-02, 8.2881e-02, 8.2649e-02,\n", "         8.2417e-02, 8.2185e-02, 8.1955e-02, 8.1725e-02, 8.1495e-02, 8.1267e-02,\n", "         8.1039e-02, 8.0811e-02, 8.0584e-02, 8.0358e-02, 8.0132e-02, 7.9908e-02,\n", "         7.9683e-02, 7.9460e-02, 7.9237e-02, 7.9014e-02, 7.8792e-02, 7.8571e-02,\n", "         7.8351e-02, 7.8131e-02, 7.7911e-02, 7.7693e-02, 7.7475e-02, 7.7257e-02,\n", "         7.7040e-02, 7.6824e-02, 7.6609e-02, 7.6394e-02, 7.6179e-02, 7.5965e-02,\n", "         7.5752e-02, 7.5539e-02, 7.5327e-02, 7.5116e-02, 7.4905e-02, 7.4695e-02,\n", "         7.4485e-02, 7.4276e-02, 7.4068e-02, 7.3860e-02, 7.3652e-02, 7.3446e-02,\n", "         7.3240e-02, 7.3034e-02, 7.2829e-02, 7.2625e-02, 7.2421e-02, 7.2218e-02,\n", "         7.2015e-02, 7.1813e-02, 7.1611e-02, 7.1410e-02, 7.1210e-02, 7.1010e-02,\n", "         7.0811e-02, 7.0612e-02, 7.0414e-02, 7.0216e-02, 7.0019e-02, 6.9822e-02,\n", "         6.9626e-02, 6.9431e-02, 6.9236e-02, 6.9042e-02, 6.8848e-02, 6.8655e-02,\n", "         6.8462e-02, 6.8270e-02, 6.8078e-02, 6.7887e-02, 6.7697e-02, 6.7507e-02,\n", "         6.7317e-02, 6.7128e-02, 6.6940e-02, 6.6752e-02, 6.6564e-02, 6.6378e-02,\n", "         6.6191e-02, 6.6005e-02, 6.5820e-02, 6.5635e-02, 6.5451e-02, 6.5268e-02,\n", "         6.5084e-02, 6.4902e-02, 6.4719e-02, 6.4538e-02, 6.4357e-02, 6.4176e-02,\n", "         6.3996e-02, 6.3816e-02, 6.3637e-02, 6.3459e-02, 6.3280e-02, 6.3103e-02,\n", "         6.2926e-02, 6.2749e-02, 6.2573e-02, 6.2397e-02, 6.2222e-02, 6.2048e-02,\n", "         6.1873e-02, 6.1700e-02, 6.1527e-02, 6.1354e-02, 6.1182e-02, 6.1010e-02,\n", "         6.0839e-02, 6.0668e-02, 6.0498e-02, 6.0328e-02, 6.0158e-02, 5.9990e-02,\n", "         5.9821e-02, 5.9653e-02, 5.9486e-02, 5.9319e-02, 5.9152e-02, 5.8986e-02,\n", "         5.8821e-02, 5.8656e-02, 5.8491e-02, 5.8327e-02, 5.8163e-02, 5.8000e-02,\n", "         5.7837e-02, 5.7675e-02, 5.7513e-02, 5.7352e-02, 5.7191e-02, 5.7030e-02,\n", "         5.6870e-02, 5.6710e-02, 5.6551e-02, 5.6392e-02, 5.6234e-02, 5.6076e-02,\n", "         5.5919e-02, 5.5762e-02, 5.5605e-02, 5.5449e-02, 5.5294e-02, 5.5139e-02,\n", "         5.4984e-02, 5.4829e-02, 5.4676e-02, 5.4522e-02, 5.4369e-02, 5.4216e-02,\n", "         5.4064e-02, 5.3912e-02, 5.3761e-02, 5.3610e-02, 5.3460e-02, 5.3310e-02,\n", "         5.3160e-02, 5.3011e-02, 5.2862e-02, 5.2714e-02, 5.2566e-02, 5.2418e-02,\n", "         5.2271e-02, 5.2124e-02, 5.1978e-02, 5.1832e-02, 5.1687e-02, 5.1542e-02,\n", "         5.1397e-02, 5.1253e-02, 5.1109e-02, 5.0965e-02, 5.0822e-02, 5.0680e-02,\n", "         5.0537e-02, 5.0396e-02, 5.0254e-02, 5.0113e-02, 4.9972e-02, 4.9832e-02,\n", "         4.9692e-02, 4.9553e-02, 4.9414e-02, 4.9275e-02, 4.9137e-02, 4.8999e-02,\n", "         4.8861e-02, 4.8724e-02, 4.8587e-02, 4.8451e-02, 4.8315e-02, 4.8179e-02,\n", "         4.8044e-02, 4.7909e-02, 4.7775e-02, 4.7641e-02, 4.7507e-02, 4.7374e-02,\n", "         4.7241e-02, 4.7108e-02, 4.6976e-02, 4.6844e-02, 4.6713e-02, 4.6581e-02,\n", "         4.6451e-02, 4.6320e-02, 4.6190e-02, 4.6061e-02, 4.5931e-02, 4.5802e-02,\n", "         4.5674e-02, 4.5546e-02, 4.5418e-02, 4.5290e-02, 4.5163e-02, 4.5036e-02,\n", "         4.4910e-02, 4.4784e-02, 4.4658e-02, 4.4533e-02, 4.4408e-02, 4.4283e-02,\n", "         4.4159e-02, 4.4035e-02, 4.3911e-02, 4.3788e-02, 4.3665e-02, 4.3543e-02,\n", "         4.3421e-02, 4.3299e-02, 4.3177e-02, 4.3056e-02, 4.2935e-02, 4.2815e-02,\n", "         4.2694e-02, 4.2575e-02, 4.2455e-02, 4.2336e-02, 4.2217e-02, 4.2099e-02,\n", "         4.1980e-02, 4.1863e-02, 4.1745e-02, 4.1628e-02, 4.1511e-02, 4.1395e-02,\n", "         4.1278e-02, 4.1163e-02, 4.1047e-02, 4.0932e-02, 4.0817e-02, 4.0702e-02,\n", "         4.0588e-02, 4.0474e-02, 4.0361e-02, 4.0247e-02, 4.0134e-02, 4.0022e-02,\n", "         3.9909e-02, 3.9797e-02, 3.9686e-02, 3.9574e-02, 3.9463e-02, 3.9352e-02,\n", "         3.9242e-02, 3.9132e-02, 3.9022e-02, 3.8912e-02, 3.8803e-02, 3.8694e-02,\n", "         3.8586e-02, 3.8477e-02, 3.8369e-02, 3.8262e-02, 3.8154e-02, 3.8047e-02,\n", "         3.7940e-02, 3.7834e-02, 3.7728e-02, 3.7622e-02, 3.7516e-02, 3.7411e-02,\n", "         3.7306e-02, 3.7201e-02, 3.7097e-02, 3.6993e-02, 3.6889e-02, 3.6785e-02,\n", "         3.6682e-02, 3.6579e-02, 3.6476e-02, 3.6374e-02, 3.6272e-02, 3.6170e-02,\n", "         3.6069e-02, 3.5967e-02, 3.5866e-02, 3.5766e-02, 3.5665e-02, 3.5565e-02,\n", "         3.5465e-02, 3.5366e-02, 3.5267e-02, 3.5168e-02, 3.5069e-02, 3.4970e-02,\n", "         3.4872e-02, 3.4774e-02, 3.4677e-02, 3.4579e-02, 3.4482e-02, 3.4386e-02,\n", "         3.4289e-02, 3.4193e-02, 3.4097e-02, 3.4001e-02, 3.3906e-02, 3.3811e-02,\n", "         3.3716e-02, 3.3621e-02, 3.3527e-02, 3.3433e-02, 3.3339e-02, 3.3245e-02,\n", "         3.3152e-02, 3.3059e-02, 3.2966e-02, 3.2873e-02, 3.2781e-02, 3.2689e-02,\n", "         3.2597e-02, 3.2506e-02, 3.2415e-02, 3.2324e-02, 3.2233e-02, 3.2143e-02,\n", "         3.2052e-02, 3.1962e-02, 3.1873e-02, 3.1783e-02, 3.1694e-02, 3.1605e-02,\n", "         3.1516e-02, 3.1428e-02, 3.1340e-02, 3.1252e-02, 3.1164e-02, 3.1076e-02,\n", "         3.0989e-02, 3.0902e-02, 3.0816e-02, 3.0729e-02, 3.0643e-02, 3.0557e-02,\n", "         3.0471e-02, 3.0385e-02, 3.0300e-02, 3.0215e-02, 3.0130e-02, 3.0046e-02,\n", "         2.9961e-02, 2.9877e-02, 2.9793e-02, 2.9710e-02, 2.9626e-02, 2.9543e-02,\n", "         2.9460e-02, 2.9378e-02, 2.9295e-02, 2.9213e-02, 2.9131e-02, 2.9049e-02,\n", "         2.8968e-02, 2.8886e-02, 2.8805e-02, 2.8724e-02, 2.8644e-02, 2.8563e-02,\n", "         2.8483e-02, 2.8403e-02, 2.8324e-02, 2.8244e-02, 2.8165e-02, 2.8086e-02,\n", "         2.8007e-02, 2.7928e-02, 2.7850e-02, 2.7772e-02, 2.7694e-02, 2.7616e-02,\n", "         2.7539e-02, 2.7461e-02, 2.7384e-02, 2.7307e-02, 2.7231e-02, 2.7154e-02,\n", "         2.7078e-02, 2.7002e-02, 2.6926e-02, 2.6851e-02, 2.6775e-02, 2.6700e-02,\n", "         2.6625e-02, 2.6550e-02, 2.6476e-02, 2.6402e-02, 2.6328e-02, 2.6254e-02,\n", "         2.6180e-02, 2.6106e-02, 2.6033e-02, 2.5960e-02, 2.5887e-02, 2.5815e-02,\n", "         2.5742e-02, 2.5670e-02, 2.5598e-02, 2.5526e-02, 2.5454e-02, 2.5383e-02,\n", "         2.5312e-02, 2.5241e-02, 2.5170e-02, 2.5099e-02, 2.5029e-02, 2.4958e-02,\n", "         2.4888e-02, 2.4818e-02, 2.4749e-02, 2.4679e-02, 2.4610e-02, 2.4541e-02,\n", "         2.4472e-02, 2.4403e-02, 2.4335e-02, 2.4267e-02, 2.4199e-02, 2.4131e-02,\n", "         2.4063e-02, 2.3995e-02, 2.3928e-02, 2.3861e-02, 2.3794e-02, 2.3727e-02,\n", "         2.3660e-02, 2.3594e-02, 2.3528e-02, 2.3462e-02, 2.3396e-02, 2.3330e-02,\n", "         2.3265e-02, 2.3199e-02, 2.3134e-02, 2.3069e-02, 2.3005e-02, 2.2940e-02,\n", "         2.2876e-02, 2.2812e-02, 2.2747e-02, 2.2684e-02, 2.2620e-02, 2.2556e-02,\n", "         2.2493e-02, 2.2430e-02, 2.2367e-02, 2.2304e-02, 2.2242e-02, 2.2179e-02,\n", "         2.2117e-02, 2.2055e-02, 2.1993e-02, 2.1931e-02, 2.1870e-02, 2.1808e-02,\n", "         2.1747e-02, 2.1686e-02, 2.1625e-02, 2.1565e-02, 2.1504e-02, 2.1444e-02,\n", "         2.1383e-02, 2.1323e-02, 2.1264e-02, 2.1204e-02, 2.1144e-02, 2.1085e-02,\n", "         2.1026e-02, 2.0967e-02, 2.0908e-02, 2.0849e-02, 2.0791e-02, 2.0732e-02,\n", "         2.0674e-02, 2.0616e-02, 2.0558e-02, 2.0501e-02, 2.0443e-02, 2.0386e-02,\n", "         2.0329e-02, 2.0271e-02, 2.0215e-02, 2.0158e-02, 2.0101e-02, 2.0045e-02,\n", "         1.9989e-02, 1.9932e-02, 1.9876e-02, 1.9821e-02, 1.9765e-02, 1.9710e-02,\n", "         1.9654e-02, 1.9599e-02, 1.9544e-02, 1.9489e-02, 1.9435e-02, 1.9380e-02,\n", "         1.9326e-02, 1.9271e-02, 1.9217e-02, 1.9163e-02, 1.9110e-02, 1.9056e-02,\n", "         1.9002e-02, 1.8949e-02, 1.8896e-02, 1.8843e-02, 1.8790e-02, 1.8737e-02,\n", "         1.8685e-02, 1.8632e-02, 1.8580e-02, 1.8528e-02, 1.8476e-02, 1.8424e-02,\n", "         1.8372e-02, 1.8321e-02, 1.8269e-02, 1.8218e-02, 1.8167e-02, 1.8116e-02,\n", "         1.8065e-02, 1.8014e-02, 1.7964e-02, 1.7913e-02, 1.7863e-02, 1.7813e-02,\n", "         1.7763e-02, 1.7713e-02, 1.7663e-02, 1.7614e-02, 1.7564e-02, 1.7515e-02,\n", "         1.7466e-02, 1.7417e-02, 1.7368e-02, 1.7319e-02, 1.7270e-02, 1.7222e-02,\n", "         1.7174e-02, 1.7125e-02, 1.7077e-02, 1.7029e-02, 1.6982e-02, 1.6934e-02,\n", "         1.6886e-02, 1.6839e-02, 1.6792e-02, 1.6745e-02, 1.6698e-02, 1.6651e-02,\n", "         1.6604e-02, 1.6557e-02, 1.6511e-02, 1.6465e-02, 1.6418e-02, 1.6372e-02,\n", "         1.6326e-02, 1.6281e-02, 1.6235e-02, 1.6189e-02, 1.6144e-02, 1.6099e-02,\n", "         1.6053e-02, 1.6008e-02, 1.5963e-02, 1.5919e-02, 1.5874e-02, 1.5829e-02,\n", "         1.5785e-02, 1.5741e-02, 1.5696e-02, 1.5652e-02, 1.5608e-02, 1.5565e-02,\n", "         1.5521e-02, 1.5477e-02, 1.5434e-02, 1.5391e-02, 1.5347e-02, 1.5304e-02,\n", "         1.5261e-02, 1.5219e-02, 1.5176e-02, 1.5133e-02, 1.5091e-02, 1.5048e-02,\n", "         1.5006e-02, 1.4964e-02, 1.4922e-02, 1.4880e-02, 1.4838e-02, 1.4797e-02,\n", "         1.4755e-02, 1.4714e-02, 1.4672e-02, 1.4631e-02, 1.4590e-02, 1.4549e-02,\n", "         1.4508e-02, 1.4468e-02, 1.4427e-02, 1.4387e-02, 1.4346e-02, 1.4306e-02,\n", "         1.4266e-02, 1.4226e-02, 1.4186e-02, 1.4146e-02, 1.4106e-02, 1.4067e-02,\n", "         1.4027e-02, 1.3988e-02, 1.3949e-02, 1.3909e-02, 1.3870e-02, 1.3831e-02,\n", "         1.3793e-02, 1.3754e-02, 1.3715e-02, 1.3677e-02, 1.3638e-02, 1.3600e-02,\n", "         1.3562e-02, 1.3524e-02, 1.3486e-02, 1.3448e-02, 1.3410e-02, 1.3373e-02,\n", "         1.3335e-02, 1.3298e-02, 1.3260e-02, 1.3223e-02, 1.3186e-02, 1.3149e-02,\n", "         1.3112e-02, 1.3075e-02, 1.3039e-02, 1.3002e-02, 1.2966e-02, 1.2929e-02,\n", "         1.2893e-02, 1.2857e-02, 1.2821e-02, 1.2785e-02, 1.2749e-02, 1.2713e-02,\n", "         1.2677e-02, 1.2642e-02, 1.2606e-02, 1.2571e-02, 1.2536e-02, 1.2500e-02,\n", "         1.2465e-02, 1.2430e-02, 1.2395e-02, 1.2361e-02, 1.2326e-02, 1.2291e-02,\n", "         1.2257e-02, 1.2222e-02, 1.2188e-02, 1.2154e-02, 1.2120e-02, 1.2086e-02,\n", "         1.2052e-02, 1.2018e-02, 1.1984e-02, 1.1951e-02, 1.1917e-02, 1.1884e-02,\n", "         1.1850e-02, 1.1817e-02, 1.1784e-02, 1.1751e-02, 1.1718e-02, 1.1685e-02,\n", "         1.1652e-02, 1.1619e-02, 1.1587e-02, 1.1554e-02, 1.1522e-02, 1.1490e-02,\n", "         1.1457e-02, 1.1425e-02, 1.1393e-02, 1.1361e-02, 1.1329e-02, 1.1297e-02,\n", "         1.1266e-02, 1.1234e-02, 1.1203e-02, 1.1171e-02, 1.1140e-02, 1.1108e-02,\n", "         1.1077e-02, 1.1046e-02, 1.1015e-02, 1.0984e-02, 1.0953e-02, 1.0923e-02,\n", "         1.0892e-02, 1.0861e-02, 1.0831e-02, 1.0801e-02, 1.0770e-02, 1.0740e-02,\n", "         1.0710e-02, 1.0680e-02, 1.0650e-02, 1.0620e-02, 1.0590e-02, 1.0560e-02,\n", "         1.0531e-02, 1.0501e-02, 1.0472e-02, 1.0442e-02, 1.0413e-02, 1.0384e-02,\n", "         1.0355e-02, 1.0326e-02, 1.0297e-02, 1.0268e-02, 1.0239e-02, 1.0210e-02,\n", "         1.0182e-02, 1.0153e-02, 1.0124e-02, 1.0096e-02, 1.0068e-02, 1.0039e-02,\n", "         1.0011e-02, 9.9832e-03, 9.9551e-03, 9.9272e-03, 9.8993e-03, 9.8715e-03,\n", "         9.8438e-03, 9.8162e-03, 9.7886e-03, 9.7612e-03, 9.7338e-03, 9.7065e-03,\n", "         9.6792e-03, 9.6520e-03, 9.6249e-03, 9.5979e-03, 9.5710e-03, 9.5441e-03,\n", "         9.5173e-03, 9.4906e-03, 9.4640e-03, 9.4374e-03, 9.4109e-03, 9.3845e-03,\n", "         9.3582e-03, 9.3319e-03, 9.3057e-03, 9.2796e-03, 9.2536e-03, 9.2276e-03,\n", "         9.2017e-03, 9.1759e-03, 9.1501e-03, 9.1244e-03, 9.0988e-03, 9.0733e-03,\n", "         9.0478e-03, 9.0224e-03, 8.9971e-03, 8.9718e-03, 8.9466e-03, 8.9215e-03,\n", "         8.8965e-03, 8.8715e-03, 8.8466e-03, 8.8218e-03, 8.7970e-03, 8.7723e-03,\n", "         8.7477e-03, 8.7232e-03, 8.6987e-03, 8.6743e-03, 8.6499e-03, 8.6256e-03,\n", "         8.6014e-03, 8.5773e-03, 8.5532e-03, 8.5292e-03, 8.5053e-03, 8.4814e-03,\n", "         8.4576e-03, 8.4338e-03, 8.4102e-03, 8.3866e-03, 8.3630e-03, 8.3395e-03,\n", "         8.3161e-03, 8.2928e-03, 8.2695e-03, 8.2463e-03, 8.2232e-03, 8.2001e-03,\n", "         8.1771e-03, 8.1541e-03, 8.1312e-03, 8.1084e-03, 8.0856e-03, 8.0630e-03,\n", "         8.0403e-03, 8.0178e-03, 7.9952e-03, 7.9728e-03, 7.9504e-03, 7.9281e-03,\n", "         7.9059e-03, 7.8837e-03, 7.8615e-03, 7.8395e-03, 7.8175e-03, 7.7955e-03,\n", "         7.7737e-03, 7.7518e-03, 7.7301e-03, 7.7084e-03, 7.6867e-03, 7.6652e-03,\n", "         7.6436e-03, 7.6222e-03, 7.6008e-03, 7.5795e-03, 7.5582e-03, 7.5370e-03,\n", "         7.5158e-03, 7.4947e-03, 7.4737e-03, 7.4527e-03, 7.4318e-03, 7.4109e-03,\n", "         7.3901e-03, 7.3694e-03, 7.3487e-03, 7.3281e-03, 7.3075e-03, 7.2870e-03,\n", "         7.2665e-03, 7.2462e-03, 7.2258e-03, 7.2055e-03, 7.1853e-03, 7.1651e-03,\n", "         7.1450e-03, 7.1250e-03, 7.1050e-03, 7.0850e-03, 7.0651e-03, 7.0453e-03,\n", "         7.0255e-03, 7.0058e-03, 6.9862e-03, 6.9665e-03, 6.9470e-03, 6.9275e-03,\n", "         6.9080e-03, 6.8887e-03, 6.8693e-03, 6.8500e-03, 6.8308e-03, 6.8116e-03,\n", "         6.7925e-03, 6.7735e-03, 6.7544e-03, 6.7355e-03, 6.7166e-03, 6.6977e-03,\n", "         6.6789e-03, 6.6602e-03, 6.6415e-03, 6.6228e-03, 6.6043e-03, 6.5857e-03,\n", "         6.5672e-03, 6.5488e-03, 6.5304e-03, 6.5121e-03, 6.4938e-03, 6.4756e-03,\n", "         6.4574e-03, 6.4393e-03, 6.4212e-03, 6.4032e-03, 6.3852e-03, 6.3673e-03,\n", "         6.3494e-03, 6.3316e-03, 6.3138e-03, 6.2961e-03, 6.2784e-03, 6.2608e-03,\n", "         6.2432e-03, 6.2257e-03, 6.2082e-03, 6.1908e-03, 6.1734e-03, 6.1561e-03,\n", "         6.1388e-03, 6.1216e-03, 6.1044e-03, 6.0873e-03, 6.0702e-03, 6.0532e-03,\n", "         6.0362e-03, 6.0192e-03, 6.0023e-03, 5.9855e-03, 5.9687e-03, 5.9519e-03,\n", "         5.9352e-03, 5.9186e-03, 5.9020e-03, 5.8854e-03, 5.8689e-03, 5.8524e-03,\n", "         5.8360e-03, 5.8196e-03, 5.8033e-03, 5.7870e-03, 5.7707e-03, 5.7545e-03,\n", "         5.7384e-03, 5.7223e-03, 5.7062e-03, 5.6902e-03, 5.6742e-03, 5.6583e-03,\n", "         5.6424e-03, 5.6266e-03, 5.6108e-03, 5.5950e-03, 5.5793e-03, 5.5637e-03,\n", "         5.5481e-03, 5.5325e-03, 5.5170e-03, 5.5015e-03, 5.4860e-03, 5.4706e-03,\n", "         5.4553e-03, 5.4400e-03, 5.4247e-03, 5.4095e-03, 5.3943e-03, 5.3791e-03,\n", "         5.3640e-03, 5.3490e-03, 5.3340e-03, 5.3190e-03, 5.3041e-03, 5.2892e-03,\n", "         5.2743e-03, 5.2595e-03, 5.2448e-03, 5.2301e-03, 5.2154e-03, 5.2007e-03,\n", "         5.1861e-03, 5.1716e-03, 5.1571e-03, 5.1426e-03, 5.1282e-03, 5.1138e-03,\n", "         5.0994e-03, 5.0851e-03, 5.0708e-03, 5.0566e-03, 5.0424e-03, 5.0282e-03,\n", "         5.0141e-03, 5.0001e-03, 4.9860e-03, 4.9720e-03, 4.9581e-03, 4.9442e-03,\n", "         4.9303e-03, 4.9164e-03, 4.9026e-03, 4.8889e-03, 4.8752e-03, 4.8615e-03,\n", "         4.8478e-03, 4.8342e-03, 4.8206e-03, 4.8071e-03, 4.7936e-03, 4.7802e-03,\n", "         4.7668e-03, 4.7534e-03, 4.7400e-03, 4.7267e-03, 4.7135e-03, 4.7002e-03,\n", "         4.6870e-03, 4.6739e-03, 4.6608e-03, 4.6477e-03, 4.6346e-03, 4.6216e-03,\n", "         4.6087e-03, 4.5957e-03, 4.5828e-03, 4.5700e-03, 4.5571e-03, 4.5443e-03,\n", "         4.5316e-03, 4.5189e-03, 4.5062e-03, 4.4935e-03, 4.4809e-03, 4.4683e-03,\n", "         4.4558e-03, 4.4433e-03, 4.4308e-03, 4.4184e-03, 4.4060e-03, 4.3936e-03,\n", "         4.3813e-03, 4.3690e-03, 4.3567e-03, 4.3445e-03, 4.3323e-03, 4.3201e-03,\n", "         4.3080e-03, 4.2959e-03, 4.2839e-03, 4.2718e-03, 4.2599e-03, 4.2479e-03,\n", "         4.2360e-03, 4.2241e-03, 4.2122e-03, 4.2004e-03, 4.1886e-03, 4.1769e-03,\n", "         4.1651e-03, 4.1534e-03, 4.1418e-03, 4.1302e-03, 4.1186e-03, 4.1070e-03,\n", "         4.0955e-03, 4.0840e-03, 4.0725e-03, 4.0611e-03, 4.0497e-03, 4.0383e-03,\n", "         4.0270e-03, 4.0157e-03, 4.0044e-03, 3.9932e-03, 3.9820e-03, 3.9708e-03,\n", "         3.9596e-03, 3.9485e-03, 3.9374e-03, 3.9264e-03, 3.9154e-03, 3.9044e-03,\n", "         3.8934e-03, 3.8825e-03, 3.8716e-03, 3.8607e-03, 3.8499e-03, 3.8391e-03,\n", "         3.8283e-03, 3.8176e-03, 3.8069e-03, 3.7962e-03, 3.7855e-03, 3.7749e-03,\n", "         3.7643e-03, 3.7537e-03, 3.7432e-03, 3.7327e-03, 3.7222e-03, 3.7118e-03,\n", "         3.7013e-03, 3.6910e-03, 3.6806e-03, 3.6703e-03, 3.6600e-03, 3.6497e-03,\n", "         3.6394e-03, 3.6292e-03, 3.6190e-03, 3.6089e-03, 3.5988e-03, 3.5887e-03,\n", "         3.5786e-03, 3.5685e-03, 3.5585e-03, 3.5485e-03, 3.5386e-03, 3.5286e-03,\n", "         3.5187e-03, 3.5089e-03, 3.4990e-03, 3.4892e-03, 3.4794e-03, 3.4696e-03,\n", "         3.4599e-03, 3.4502e-03, 3.4405e-03, 3.4308e-03, 3.4212e-03, 3.4116e-03,\n", "         3.4020e-03, 3.3925e-03, 3.3830e-03, 3.3735e-03, 3.3640e-03, 3.3546e-03,\n", "         3.3451e-03, 3.3357e-03, 3.3264e-03, 3.3170e-03, 3.3077e-03, 3.2985e-03,\n", "         3.2892e-03, 3.2800e-03, 3.2708e-03, 3.2616e-03, 3.2524e-03, 3.2433e-03,\n", "         3.2342e-03, 3.2251e-03, 3.2161e-03, 3.2070e-03, 3.1980e-03, 3.1891e-03,\n", "         3.1801e-03, 3.1712e-03, 3.1623e-03, 3.1534e-03, 3.1446e-03, 3.1357e-03,\n", "         3.1269e-03, 3.1181e-03, 3.1094e-03, 3.1007e-03, 3.0920e-03, 3.0833e-03,\n", "         3.0746e-03, 3.0660e-03, 3.0574e-03, 3.0488e-03, 3.0403e-03, 3.0317e-03,\n", "         3.0232e-03, 3.0147e-03, 3.0063e-03, 2.9978e-03, 2.9894e-03, 2.9810e-03,\n", "         2.9727e-03, 2.9643e-03, 2.9560e-03, 2.9477e-03, 2.9394e-03, 2.9312e-03,\n", "         2.9229e-03, 2.9147e-03, 2.9066e-03, 2.8984e-03, 2.8903e-03, 2.8822e-03,\n", "         2.8741e-03, 2.8660e-03, 2.8580e-03, 2.8499e-03, 2.8419e-03, 2.8340e-03,\n", "         2.8260e-03, 2.8181e-03, 2.8102e-03, 2.8023e-03, 2.7944e-03, 2.7866e-03,\n", "         2.7787e-03, 2.7709e-03, 2.7632e-03, 2.7554e-03, 2.7477e-03, 2.7400e-03,\n", "         2.7323e-03, 2.7246e-03, 2.7170e-03, 2.7093e-03, 2.7017e-03, 2.6941e-03,\n", "         2.6866e-03, 2.6790e-03, 2.6715e-03, 2.6640e-03, 2.6565e-03, 2.6491e-03,\n", "         2.6416e-03, 2.6342e-03, 2.6268e-03, 2.6195e-03, 2.6121e-03, 2.6048e-03,\n", "         2.5975e-03, 2.5902e-03, 2.5829e-03, 2.5757e-03, 2.5684e-03, 2.5612e-03,\n", "         2.5540e-03, 2.5469e-03, 2.5397e-03, 2.5326e-03, 2.5255e-03, 2.5184e-03,\n", "         2.5113e-03, 2.5043e-03, 2.4972e-03, 2.4902e-03, 2.4832e-03, 2.4763e-03,\n", "         2.4693e-03, 2.4624e-03, 2.4555e-03, 2.4486e-03, 2.4417e-03, 2.4349e-03,\n", "         2.4280e-03, 2.4212e-03, 2.4144e-03, 2.4076e-03, 2.4009e-03, 2.3941e-03,\n", "         2.3874e-03, 2.3807e-03, 2.3740e-03, 2.3674e-03, 2.3607e-03, 2.3541e-03,\n", "         2.3475e-03, 2.3409e-03, 2.3343e-03, 2.3278e-03, 2.3213e-03, 2.3147e-03,\n", "         2.3082e-03, 2.3018e-03, 2.2953e-03, 2.2889e-03, 2.2824e-03, 2.2760e-03,\n", "         2.2696e-03, 2.2633e-03, 2.2569e-03, 2.2506e-03, 2.2443e-03, 2.2380e-03,\n", "         2.2317e-03, 2.2254e-03, 2.2192e-03, 2.2129e-03, 2.2067e-03, 2.2005e-03,\n", "         2.1944e-03, 2.1882e-03, 2.1821e-03, 2.1759e-03, 2.1698e-03, 2.1637e-03,\n", "         2.1577e-03, 2.1516e-03, 2.1456e-03, 2.1395e-03, 2.1335e-03, 2.1276e-03,\n", "         2.1216e-03, 2.1156e-03, 2.1097e-03, 2.1038e-03, 2.0979e-03, 2.0920e-03,\n", "         2.0861e-03, 2.0802e-03, 2.0744e-03, 2.0686e-03, 2.0628e-03, 2.0570e-03,\n", "         2.0512e-03, 2.0455e-03, 2.0397e-03, 2.0340e-03, 2.0283e-03, 2.0226e-03,\n", "         2.0169e-03, 2.0113e-03, 2.0056e-03, 2.0000e-03, 1.9944e-03, 1.9888e-03,\n", "         1.9832e-03, 1.9776e-03, 1.9721e-03, 1.9665e-03, 1.9610e-03, 1.9555e-03,\n", "         1.9500e-03, 1.9445e-03, 1.9391e-03, 1.9336e-03, 1.9282e-03, 1.9228e-03,\n", "         1.9174e-03, 1.9120e-03, 1.9067e-03, 1.9013e-03, 1.8960e-03, 1.8907e-03,\n", "         1.8853e-03, 1.8801e-03, 1.8748e-03, 1.8695e-03, 1.8643e-03, 1.8590e-03,\n", "         1.8538e-03, 1.8486e-03, 1.8434e-03, 1.8382e-03, 1.8331e-03, 1.8279e-03,\n", "         1.8228e-03, 1.8177e-03, 1.8126e-03, 1.8075e-03, 1.8024e-03, 1.7974e-03,\n", "         1.7923e-03, 1.7873e-03, 1.7823e-03, 1.7773e-03, 1.7723e-03, 1.7673e-03,\n", "         1.7624e-03, 1.7574e-03, 1.7525e-03, 1.7476e-03, 1.7427e-03, 1.7378e-03,\n", "         1.7329e-03, 1.7280e-03, 1.7232e-03, 1.7183e-03, 1.7135e-03, 1.7087e-03,\n", "         1.7039e-03, 1.6991e-03, 1.6944e-03, 1.6896e-03, 1.6849e-03, 1.6801e-03,\n", "         1.6754e-03, 1.6707e-03, 1.6660e-03, 1.6613e-03, 1.6567e-03, 1.6520e-03,\n", "         1.6474e-03, 1.6428e-03, 1.6382e-03, 1.6336e-03, 1.6290e-03, 1.6244e-03,\n", "         1.6198e-03, 1.6153e-03, 1.6108e-03, 1.6062e-03, 1.6017e-03, 1.5972e-03,\n", "         1.5928e-03, 1.5883e-03, 1.5838e-03, 1.5794e-03, 1.5749e-03, 1.5705e-03,\n", "         1.5661e-03, 1.5617e-03, 1.5573e-03, 1.5530e-03, 1.5486e-03, 1.5443e-03,\n", "         1.5399e-03, 1.5356e-03, 1.5313e-03, 1.5270e-03, 1.5227e-03, 1.5184e-03,\n", "         1.5142e-03, 1.5099e-03, 1.5057e-03, 1.5015e-03, 1.4972e-03, 1.4930e-03,\n", "         1.4889e-03, 1.4847e-03, 1.4805e-03, 1.4764e-03, 1.4722e-03, 1.4681e-03,\n", "         1.4640e-03, 1.4598e-03, 1.4557e-03, 1.4517e-03, 1.4476e-03, 1.4435e-03,\n", "         1.4395e-03, 1.4354e-03, 1.4314e-03, 1.4274e-03, 1.4234e-03, 1.4194e-03,\n", "         1.4154e-03, 1.4114e-03, 1.4075e-03, 1.4035e-03, 1.3996e-03, 1.3956e-03,\n", "         1.3917e-03, 1.3878e-03, 1.3839e-03, 1.3800e-03, 1.3762e-03, 1.3723e-03,\n", "         1.3685e-03, 1.3646e-03, 1.3608e-03, 1.3570e-03, 1.3532e-03, 1.3494e-03,\n", "         1.3456e-03, 1.3418e-03, 1.3380e-03, 1.3343e-03, 1.3305e-03, 1.3268e-03,\n", "         1.3231e-03, 1.3194e-03, 1.3157e-03, 1.3120e-03, 1.3083e-03, 1.3046e-03,\n", "         1.3009e-03, 1.2973e-03, 1.2936e-03, 1.2900e-03, 1.2864e-03, 1.2828e-03,\n", "         1.2792e-03, 1.2756e-03, 1.2720e-03, 1.2684e-03, 1.2649e-03, 1.2613e-03,\n", "         1.2578e-03, 1.2543e-03, 1.2507e-03, 1.2472e-03, 1.2437e-03, 1.2402e-03,\n", "         1.2368e-03, 1.2333e-03, 1.2298e-03, 1.2264e-03, 1.2229e-03, 1.2195e-03,\n", "         1.2161e-03, 1.2127e-03, 1.2093e-03, 1.2059e-03, 1.2025e-03, 1.1991e-03,\n", "         1.1957e-03, 1.1924e-03, 1.1890e-03, 1.1857e-03, 1.1824e-03, 1.1791e-03,\n", "         1.1757e-03, 1.1724e-03, 1.1692e-03, 1.1659e-03, 1.1626e-03, 1.1593e-03,\n", "         1.1561e-03, 1.1528e-03, 1.1496e-03, 1.1464e-03, 1.1432e-03, 1.1399e-03,\n", "         1.1367e-03, 1.1336e-03, 1.1304e-03, 1.1272e-03, 1.1240e-03, 1.1209e-03,\n", "         1.1177e-03, 1.1146e-03, 1.1115e-03, 1.1084e-03, 1.1052e-03, 1.1021e-03,\n", "         1.0990e-03, 1.0960e-03, 1.0929e-03, 1.0898e-03, 1.0868e-03, 1.0837e-03,\n", "         1.0807e-03, 1.0776e-03, 1.0746e-03, 1.0716e-03, 1.0686e-03, 1.0656e-03,\n", "         1.0626e-03, 1.0596e-03, 1.0566e-03, 1.0537e-03, 1.0507e-03, 1.0478e-03,\n", "         1.0448e-03, 1.0419e-03, 1.0390e-03, 1.0361e-03, 1.0331e-03, 1.0302e-03,\n", "         1.0274e-03, 1.0245e-03, 1.0216e-03, 1.0187e-03, 1.0159e-03, 1.0130e-03,\n", "         1.0102e-03, 1.0073e-03, 1.0045e-03, 1.0017e-03, 9.9888e-04, 9.9607e-04,\n", "         9.9328e-04, 9.9049e-04, 9.8771e-04, 9.8494e-04, 9.8217e-04, 9.7942e-04,\n", "         9.7667e-04, 9.7392e-04, 9.7119e-04, 9.6847e-04, 9.6575e-04, 9.6304e-04,\n", "         9.6033e-04, 9.5764e-04, 9.5495e-04, 9.5227e-04, 9.4960e-04, 9.4693e-04,\n", "         9.4427e-04, 9.4162e-04, 9.3898e-04, 9.3634e-04, 9.3372e-04, 9.3110e-04,\n", "         9.2848e-04, 9.2588e-04, 9.2328e-04, 9.2069e-04, 9.1810e-04, 9.1552e-04,\n", "         9.1295e-04, 9.1039e-04, 9.0784e-04, 9.0529e-04, 9.0275e-04, 9.0021e-04,\n", "         8.9769e-04, 8.9517e-04, 8.9265e-04, 8.9015e-04, 8.8765e-04, 8.8516e-04,\n", "         8.8267e-04, 8.8020e-04, 8.7773e-04, 8.7526e-04, 8.7281e-04, 8.7036e-04,\n", "         8.6791e-04, 8.6548e-04, 8.6305e-04, 8.6063e-04, 8.5821e-04, 8.5580e-04,\n", "         8.5340e-04, 8.5100e-04, 8.4862e-04, 8.4623e-04, 8.4386e-04, 8.4149e-04,\n", "         8.3913e-04, 8.3677e-04, 8.3442e-04, 8.3208e-04, 8.2975e-04, 8.2742e-04,\n", "         8.2509e-04, 8.2278e-04, 8.2047e-04, 8.1817e-04, 8.1587e-04, 8.1358e-04,\n", "         8.1130e-04, 8.0902e-04, 8.0675e-04, 8.0448e-04, 8.0223e-04, 7.9997e-04,\n", "         7.9773e-04, 7.9549e-04, 7.9326e-04, 7.9103e-04, 7.8881e-04, 7.8660e-04,\n", "         7.8439e-04, 7.8219e-04, 7.7999e-04, 7.7780e-04, 7.7562e-04, 7.7344e-04,\n", "         7.7127e-04, 7.6911e-04, 7.6695e-04, 7.6479e-04, 7.6265e-04, 7.6051e-04,\n", "         7.5837e-04, 7.5624e-04, 7.5412e-04, 7.5200e-04, 7.4989e-04, 7.4779e-04,\n", "         7.4569e-04, 7.4360e-04, 7.4151e-04, 7.3943e-04, 7.3735e-04, 7.3528e-04,\n", "         7.3322e-04, 7.3116e-04, 7.2911e-04, 7.2706e-04, 7.2502e-04, 7.2299e-04,\n", "         7.2096e-04, 7.1893e-04, 7.1692e-04, 7.1490e-04, 7.1290e-04, 7.1090e-04,\n", "         7.0890e-04, 7.0691e-04, 7.0493e-04, 7.0295e-04, 7.0098e-04, 6.9901e-04,\n", "         6.9705e-04, 6.9509e-04, 6.9314e-04, 6.9119e-04, 6.8925e-04, 6.8732e-04,\n", "         6.8539e-04, 6.8347e-04, 6.8155e-04, 6.7963e-04, 6.7773e-04, 6.7582e-04,\n", "         6.7393e-04, 6.7204e-04, 6.7015e-04, 6.6827e-04, 6.6639e-04, 6.6452e-04,\n", "         6.6266e-04, 6.6080e-04, 6.5894e-04, 6.5709e-04, 6.5525e-04, 6.5341e-04,\n", "         6.5158e-04, 6.4975e-04, 6.4792e-04, 6.4610e-04, 6.4429e-04, 6.4248e-04,\n", "         6.4068e-04, 6.3888e-04, 6.3709e-04, 6.3530e-04, 6.3352e-04, 6.3174e-04,\n", "         6.2996e-04, 6.2820e-04, 6.2643e-04, 6.2468e-04, 6.2292e-04, 6.2117e-04,\n", "         6.1943e-04, 6.1769e-04, 6.1596e-04, 6.1423e-04, 6.1250e-04, 6.1079e-04,\n", "         6.0907e-04, 6.0736e-04, 6.0566e-04, 6.0396e-04, 6.0226e-04, 6.0057e-04,\n", "         5.9889e-04, 5.9720e-04, 5.9553e-04, 5.9386e-04, 5.9219e-04, 5.9053e-04,\n", "         5.8887e-04, 5.8722e-04, 5.8557e-04, 5.8393e-04, 5.8229e-04, 5.8065e-04,\n", "         5.7902e-04, 5.7740e-04, 5.7578e-04, 5.7416e-04, 5.7255e-04, 5.7094e-04,\n", "         5.6934e-04, 5.6774e-04, 5.6615e-04, 5.6456e-04, 5.6297e-04, 5.6139e-04,\n", "         5.5982e-04, 5.5825e-04, 5.5668e-04, 5.5512e-04, 5.5356e-04, 5.5201e-04,\n", "         5.5046e-04, 5.4891e-04, 5.4737e-04, 5.4583e-04, 5.4430e-04, 5.4277e-04,\n", "         5.4125e-04, 5.3973e-04, 5.3822e-04, 5.3671e-04, 5.3520e-04, 5.3370e-04,\n", "         5.3220e-04, 5.3071e-04, 5.2922e-04, 5.2773e-04, 5.2625e-04, 5.2477e-04,\n", "         5.2330e-04, 5.2183e-04, 5.2037e-04, 5.1891e-04, 5.1745e-04, 5.1600e-04,\n", "         5.1455e-04, 5.1310e-04, 5.1166e-04, 5.1023e-04, 5.0880e-04, 5.0737e-04,\n", "         5.0594e-04, 5.0452e-04, 5.0311e-04, 5.0169e-04, 5.0029e-04, 4.9888e-04,\n", "         4.9748e-04, 4.9609e-04, 4.9469e-04, 4.9330e-04, 4.9192e-04, 4.9054e-04,\n", "         4.8916e-04, 4.8779e-04, 4.8642e-04, 4.8506e-04, 4.8369e-04, 4.8234e-04,\n", "         4.8098e-04, 4.7963e-04, 4.7829e-04, 4.7694e-04, 4.7560e-04, 4.7427e-04,\n", "         4.7294e-04, 4.7161e-04, 4.7029e-04, 4.6897e-04, 4.6765e-04, 4.6634e-04,\n", "         4.6503e-04, 4.6372e-04, 4.6242e-04, 4.6112e-04, 4.5983e-04, 4.5854e-04,\n", "         4.5725e-04, 4.5597e-04, 4.5469e-04, 4.5341e-04, 4.5214e-04, 4.5087e-04,\n", "         4.4961e-04, 4.4834e-04, 4.4709e-04, 4.4583e-04, 4.4458e-04, 4.4333e-04,\n", "         4.4209e-04, 4.4085e-04, 4.3961e-04, 4.3837e-04, 4.3714e-04, 4.3592e-04,\n", "         4.3469e-04, 4.3347e-04, 4.3226e-04, 4.3104e-04, 4.2983e-04, 4.2863e-04,\n", "         4.2742e-04, 4.2622e-04, 4.2503e-04, 4.2384e-04, 4.2265e-04, 4.2146e-04,\n", "         4.2028e-04, 4.1910e-04, 4.1792e-04, 4.1675e-04, 4.1558e-04, 4.1441e-04,\n", "         4.1325e-04, 4.1209e-04, 4.1093e-04, 4.0978e-04, 4.0863e-04, 4.0748e-04,\n", "         4.0634e-04, 4.0520e-04, 4.0406e-04, 4.0293e-04, 4.0179e-04, 4.0067e-04,\n", "         3.9954e-04, 3.9842e-04, 3.9730e-04, 3.9619e-04, 3.9508e-04, 3.9397e-04,\n", "         3.9286e-04, 3.9176e-04, 3.9066e-04, 3.8956e-04, 3.8847e-04, 3.8738e-04,\n", "         3.8629e-04, 3.8521e-04, 3.8413e-04, 3.8305e-04, 3.8197e-04, 3.8090e-04,\n", "         3.7983e-04, 3.7876e-04, 3.7770e-04, 3.7664e-04, 3.7558e-04, 3.7453e-04,\n", "         3.7348e-04, 3.7243e-04, 3.7138e-04, 3.7034e-04, 3.6930e-04, 3.6827e-04,\n", "         3.6723e-04, 3.6620e-04, 3.6517e-04, 3.6415e-04, 3.6313e-04, 3.6211e-04,\n", "         3.6109e-04, 3.6008e-04, 3.5907e-04, 3.5806e-04, 3.5705e-04, 3.5605e-04,\n", "         3.5505e-04, 3.5406e-04, 3.5306e-04, 3.5207e-04, 3.5108e-04, 3.5010e-04,\n", "         3.4912e-04, 3.4814e-04, 3.4716e-04, 3.4618e-04, 3.4521e-04, 3.4424e-04,\n", "         3.4328e-04, 3.4231e-04, 3.4135e-04, 3.4039e-04, 3.3944e-04, 3.3849e-04,\n", "         3.3754e-04, 3.3659e-04, 3.3564e-04, 3.3470e-04, 3.3376e-04, 3.3283e-04,\n", "         3.3189e-04, 3.3096e-04, 3.3003e-04, 3.2910e-04, 3.2818e-04, 3.2726e-04,\n", "         3.2634e-04, 3.2543e-04, 3.2451e-04, 3.2360e-04, 3.2269e-04, 3.2179e-04,\n", "         3.2088e-04, 3.1998e-04, 3.1908e-04, 3.1819e-04, 3.1730e-04, 3.1641e-04,\n", "         3.1552e-04, 3.1463e-04, 3.1375e-04, 3.1287e-04, 3.1199e-04, 3.1111e-04,\n", "         3.1024e-04, 3.0937e-04, 3.0850e-04, 3.0764e-04, 3.0677e-04, 3.0591e-04,\n", "         3.0505e-04, 3.0420e-04, 3.0334e-04, 3.0249e-04, 3.0164e-04, 3.0080e-04,\n", "         2.9995e-04, 2.9911e-04, 2.9827e-04, 2.9743e-04, 2.9660e-04, 2.9577e-04,\n", "         2.9494e-04, 2.9411e-04, 2.9328e-04, 2.9246e-04, 2.9164e-04, 2.9082e-04,\n", "         2.9000e-04, 2.8919e-04, 2.8838e-04, 2.8757e-04, 2.8676e-04, 2.8596e-04,\n", "         2.8515e-04, 2.8435e-04, 2.8355e-04, 2.8276e-04, 2.8197e-04, 2.8117e-04,\n", "         2.8038e-04, 2.7960e-04, 2.7881e-04, 2.7803e-04, 2.7725e-04, 2.7647e-04,\n", "         2.7570e-04, 2.7492e-04, 2.7415e-04, 2.7338e-04, 2.7261e-04, 2.7185e-04,\n", "         2.7109e-04, 2.7032e-04, 2.6957e-04, 2.6881e-04, 2.6805e-04, 2.6730e-04,\n", "         2.6655e-04, 2.6580e-04, 2.6506e-04, 2.6431e-04, 2.6357e-04, 2.6283e-04,\n", "         2.6209e-04, 2.6136e-04, 2.6062e-04, 2.5989e-04, 2.5916e-04, 2.5844e-04,\n", "         2.5771e-04, 2.5699e-04, 2.5627e-04, 2.5555e-04, 2.5483e-04, 2.5411e-04,\n", "         2.5340e-04, 2.5269e-04, 2.5198e-04, 2.5127e-04, 2.5057e-04, 2.4986e-04,\n", "         2.4916e-04, 2.4846e-04, 2.4777e-04, 2.4707e-04, 2.4638e-04, 2.4569e-04,\n", "         2.4500e-04, 2.4431e-04, 2.4362e-04, 2.4294e-04, 2.4226e-04, 2.4158e-04,\n", "         2.4090e-04, 2.4022e-04, 2.3955e-04, 2.3888e-04, 2.3821e-04, 2.3754e-04,\n", "         2.3687e-04, 2.3621e-04, 2.3554e-04, 2.3488e-04, 2.3422e-04, 2.3357e-04,\n", "         2.3291e-04, 2.3226e-04, 2.3160e-04, 2.3095e-04, 2.3031e-04, 2.2966e-04,\n", "         2.2901e-04, 2.2837e-04, 2.2773e-04, 2.2709e-04, 2.2645e-04, 2.2582e-04,\n", "         2.2518e-04, 2.2455e-04, 2.2392e-04, 2.2329e-04, 2.2267e-04, 2.2204e-04,\n", "         2.2142e-04, 2.2080e-04, 2.2018e-04, 2.1956e-04, 2.1894e-04, 2.1833e-04,\n", "         2.1772e-04, 2.1711e-04, 2.1650e-04, 2.1589e-04, 2.1528e-04, 2.1468e-04,\n", "         2.1408e-04, 2.1347e-04, 2.1288e-04, 2.1228e-04, 2.1168e-04, 2.1109e-04,\n", "         2.1050e-04, 2.0990e-04, 2.0932e-04, 2.0873e-04, 2.0814e-04, 2.0756e-04,\n", "         2.0698e-04, 2.0639e-04, 2.0581e-04, 2.0524e-04, 2.0466e-04, 2.0409e-04,\n", "         2.0351e-04, 2.0294e-04, 2.0237e-04, 2.0180e-04, 2.0124e-04, 2.0067e-04,\n", "         2.0011e-04, 1.9955e-04, 1.9899e-04, 1.9843e-04, 1.9787e-04, 1.9732e-04,\n", "         1.9676e-04, 1.9621e-04, 1.9566e-04, 1.9511e-04, 1.9456e-04, 1.9402e-04,\n", "         1.9347e-04, 1.9293e-04, 1.9239e-04, 1.9185e-04, 1.9131e-04, 1.9077e-04,\n", "         1.9024e-04, 1.8970e-04, 1.8917e-04, 1.8864e-04, 1.8811e-04, 1.8758e-04,\n", "         1.8706e-04, 1.8653e-04, 1.8601e-04, 1.8549e-04, 1.8497e-04, 1.8445e-04,\n", "         1.8393e-04, 1.8341e-04, 1.8290e-04, 1.8238e-04, 1.8187e-04, 1.8136e-04,\n", "         1.8085e-04, 1.8034e-04, 1.7984e-04, 1.7933e-04, 1.7883e-04, 1.7833e-04,\n", "         1.7783e-04, 1.7733e-04, 1.7683e-04, 1.7633e-04, 1.7584e-04, 1.7535e-04,\n", "         1.7485e-04, 1.7436e-04, 1.7387e-04, 1.7339e-04, 1.7290e-04, 1.7241e-04,\n", "         1.7193e-04, 1.7145e-04, 1.7097e-04, 1.7049e-04, 1.7001e-04, 1.6953e-04,\n", "         1.6905e-04, 1.6858e-04, 1.6811e-04, 1.6764e-04, 1.6716e-04, 1.6670e-04,\n", "         1.6623e-04, 1.6576e-04, 1.6530e-04, 1.6483e-04, 1.6437e-04, 1.6391e-04,\n", "         1.6345e-04, 1.6299e-04, 1.6253e-04, 1.6208e-04, 1.6162e-04, 1.6117e-04,\n", "         1.6071e-04, 1.6026e-04, 1.5981e-04, 1.5936e-04, 1.5892e-04, 1.5847e-04,\n", "         1.5803e-04, 1.5758e-04, 1.5714e-04, 1.5670e-04, 1.5626e-04, 1.5582e-04,\n", "         1.5538e-04, 1.5495e-04, 1.5451e-04, 1.5408e-04, 1.5365e-04, 1.5322e-04,\n", "         1.5279e-04, 1.5236e-04, 1.5193e-04, 1.5150e-04, 1.5108e-04, 1.5065e-04,\n", "         1.5023e-04, 1.4981e-04, 1.4939e-04, 1.4897e-04, 1.4855e-04, 1.4813e-04,\n", "         1.4772e-04, 1.4730e-04, 1.4689e-04, 1.4648e-04, 1.4607e-04, 1.4566e-04,\n", "         1.4525e-04, 1.4484e-04, 1.4443e-04, 1.4403e-04, 1.4362e-04, 1.4322e-04,\n", "         1.4282e-04, 1.4242e-04, 1.4202e-04, 1.4162e-04, 1.4122e-04, 1.4083e-04,\n", "         1.4043e-04, 1.4004e-04, 1.3964e-04, 1.3925e-04, 1.3886e-04, 1.3847e-04,\n", "         1.3808e-04, 1.3769e-04, 1.3731e-04, 1.3692e-04, 1.3654e-04, 1.3615e-04,\n", "         1.3577e-04, 1.3539e-04, 1.3501e-04, 1.3463e-04, 1.3425e-04, 1.3388e-04,\n", "         1.3350e-04, 1.3313e-04, 1.3275e-04, 1.3238e-04, 1.3201e-04, 1.3164e-04,\n", "         1.3127e-04, 1.3090e-04, 1.3053e-04, 1.3017e-04, 1.2980e-04, 1.2944e-04,\n", "         1.2907e-04, 1.2871e-04, 1.2835e-04, 1.2799e-04, 1.2763e-04, 1.2727e-04,\n", "         1.2692e-04, 1.2656e-04, 1.2620e-04, 1.2585e-04, 1.2550e-04, 1.2514e-04,\n", "         1.2479e-04, 1.2444e-04, 1.2409e-04, 1.2375e-04, 1.2340e-04, 1.2305e-04,\n", "         1.2271e-04, 1.2236e-04, 1.2202e-04, 1.2168e-04, 1.2133e-04, 1.2099e-04,\n", "         1.2065e-04, 1.2032e-04, 1.1998e-04, 1.1964e-04, 1.1931e-04, 1.1897e-04,\n", "         1.1864e-04, 1.1830e-04, 1.1797e-04, 1.1764e-04, 1.1731e-04, 1.1698e-04,\n", "         1.1665e-04, 1.1633e-04, 1.1600e-04, 1.1567e-04, 1.1535e-04, 1.1502e-04,\n", "         1.1470e-04, 1.1438e-04, 1.1406e-04, 1.1374e-04, 1.1342e-04, 1.1310e-04,\n", "         1.1278e-04, 1.1247e-04, 1.1215e-04, 1.1184e-04, 1.1152e-04, 1.1121e-04,\n", "         1.1090e-04, 1.1059e-04, 1.1028e-04, 1.0997e-04, 1.0966e-04, 1.0935e-04,\n", "         1.0904e-04, 1.0874e-04, 1.0843e-04, 1.0813e-04, 1.0782e-04, 1.0752e-04,\n", "         1.0722e-04, 1.0692e-04, 1.0662e-04, 1.0632e-04, 1.0602e-04, 1.0572e-04,\n", "         1.0543e-04, 1.0513e-04, 1.0484e-04, 1.0454e-04, 1.0425e-04, 1.0396e-04,\n", "         1.0366e-04, 1.0337e-04, 1.0308e-04, 1.0279e-04, 1.0250e-04, 1.0222e-04,\n", "         1.0193e-04, 1.0164e-04, 1.0136e-04, 1.0107e-04, 1.0079e-04, 1.0051e-04,\n", "         1.0023e-04, 9.9944e-05, 9.9663e-05, 9.9384e-05, 9.9105e-05, 9.8826e-05,\n", "         9.8549e-05, 9.8272e-05, 9.7997e-05, 9.7722e-05, 9.7447e-05, 9.7174e-05,\n", "         9.6901e-05, 9.6629e-05, 9.6358e-05, 9.6087e-05, 9.5818e-05, 9.5549e-05,\n", "         9.5280e-05, 9.5013e-05, 9.4746e-05, 9.4480e-05, 9.4215e-05, 9.3951e-05,\n", "         9.3687e-05, 9.3424e-05, 9.3162e-05, 9.2900e-05, 9.2640e-05, 9.2380e-05,\n", "         9.2120e-05, 9.1862e-05, 9.1604e-05, 9.1347e-05, 9.1090e-05, 9.0835e-05,\n", "         9.0580e-05, 9.0326e-05, 9.0072e-05, 8.9819e-05, 8.9567e-05, 8.9316e-05,\n", "         8.9065e-05, 8.8815e-05, 8.8566e-05, 8.8317e-05, 8.8069e-05, 8.7822e-05,\n", "         8.7576e-05, 8.7330e-05, 8.7085e-05, 8.6840e-05, 8.6596e-05, 8.6353e-05,\n", "         8.6111e-05, 8.5869e-05, 8.5628e-05, 8.5388e-05, 8.5148e-05, 8.4909e-05,\n", "         8.4671e-05, 8.4433e-05, 8.4196e-05, 8.3960e-05, 8.3724e-05, 8.3489e-05,\n", "         8.3255e-05, 8.3021e-05, 8.2788e-05, 8.2556e-05, 8.2324e-05, 8.2093e-05,\n", "         8.1863e-05, 8.1633e-05, 8.1404e-05, 8.1175e-05, 8.0947e-05, 8.0720e-05,\n", "         8.0494e-05, 8.0268e-05, 8.0042e-05, 7.9818e-05, 7.9594e-05, 7.9370e-05,\n", "         7.9148e-05, 7.8925e-05, 7.8704e-05, 7.8483e-05, 7.8263e-05, 7.8043e-05,\n", "         7.7824e-05, 7.7606e-05, 7.7388e-05, 7.7170e-05, 7.6954e-05, 7.6738e-05,\n", "         7.6522e-05, 7.6308e-05, 7.6094e-05, 7.5880e-05, 7.5667e-05, 7.5455e-05,\n", "         7.5243e-05, 7.5032e-05, 7.4821e-05, 7.4611e-05, 7.4402e-05, 7.4193e-05,\n", "         7.3984e-05, 7.3777e-05, 7.3570e-05, 7.3363e-05, 7.3157e-05, 7.2952e-05,\n", "         7.2747e-05, 7.2543e-05, 7.2339e-05, 7.2136e-05, 7.1934e-05, 7.1732e-05,\n", "         7.1531e-05, 7.1330e-05, 7.1130e-05, 7.0930e-05, 7.0731e-05, 7.0532e-05,\n", "         7.0334e-05, 7.0137e-05, 6.9940e-05, 6.9744e-05, 6.9548e-05, 6.9353e-05,\n", "         6.9158e-05, 6.8964e-05, 6.8771e-05, 6.8578e-05, 6.8385e-05, 6.8193e-05,\n", "         6.8002e-05, 6.7811e-05, 6.7620e-05, 6.7431e-05, 6.7241e-05, 6.7053e-05,\n", "         6.6864e-05, 6.6677e-05, 6.6490e-05, 6.6303e-05, 6.6117e-05, 6.5931e-05,\n", "         6.5746e-05, 6.5562e-05, 6.5378e-05, 6.5194e-05, 6.5011e-05, 6.4829e-05,\n", "         6.4647e-05, 6.4465e-05, 6.4284e-05, 6.4104e-05, 6.3924e-05, 6.3745e-05,\n", "         6.3566e-05, 6.3387e-05, 6.3209e-05, 6.3032e-05, 6.2855e-05, 6.2679e-05,\n", "         6.2503e-05, 6.2327e-05, 6.2152e-05, 6.1978e-05, 6.1804e-05, 6.1630e-05,\n", "         6.1457e-05, 6.1285e-05, 6.1113e-05, 6.0941e-05, 6.0770e-05, 6.0600e-05,\n", "         6.0430e-05, 6.0260e-05, 6.0091e-05, 5.9922e-05, 5.9754e-05, 5.9586e-05,\n", "         5.9419e-05, 5.9252e-05, 5.9086e-05, 5.8920e-05, 5.8755e-05, 5.8590e-05,\n", "         5.8425e-05, 5.8261e-05, 5.8098e-05, 5.7935e-05, 5.7772e-05, 5.7610e-05,\n", "         5.7448e-05, 5.7287e-05, 5.7126e-05, 5.6966e-05, 5.6806e-05, 5.6647e-05,\n", "         5.6488e-05, 5.6329e-05, 5.6171e-05, 5.6013e-05, 5.5856e-05, 5.5699e-05,\n", "         5.5543e-05, 5.5387e-05, 5.5232e-05, 5.5077e-05, 5.4922e-05, 5.4768e-05,\n", "         5.4614e-05, 5.4461e-05, 5.4308e-05, 5.4155e-05, 5.4003e-05, 5.3852e-05,\n", "         5.3701e-05, 5.3550e-05, 5.3400e-05, 5.3250e-05, 5.3100e-05, 5.2951e-05,\n", "         5.2803e-05, 5.2655e-05, 5.2507e-05, 5.2359e-05, 5.2212e-05, 5.2066e-05,\n", "         5.1920e-05, 5.1774e-05, 5.1629e-05, 5.1484e-05, 5.1339e-05, 5.1195e-05,\n", "         5.1051e-05, 5.0908e-05, 5.0765e-05, 5.0623e-05, 5.0481e-05, 5.0339e-05,\n", "         5.0198e-05, 5.0057e-05, 4.9916e-05, 4.9776e-05, 4.9636e-05, 4.9497e-05,\n", "         4.9358e-05, 4.9220e-05, 4.9082e-05, 4.8944e-05, 4.8806e-05, 4.8669e-05,\n", "         4.8533e-05, 4.8397e-05, 4.8261e-05, 4.8125e-05, 4.7990e-05, 4.7855e-05,\n", "         4.7721e-05, 4.7587e-05, 4.7454e-05, 4.7320e-05, 4.7188e-05, 4.7055e-05,\n", "         4.6923e-05, 4.6791e-05, 4.6660e-05, 4.6529e-05, 4.6398e-05, 4.6268e-05,\n", "         4.6138e-05, 4.6009e-05, 4.5880e-05, 4.5751e-05, 4.5623e-05, 4.5495e-05,\n", "         4.5367e-05, 4.5239e-05, 4.5112e-05, 4.4986e-05, 4.4860e-05, 4.4734e-05,\n", "         4.4608e-05, 4.4483e-05, 4.4358e-05, 4.4234e-05, 4.4109e-05, 4.3986e-05,\n", "         4.3862e-05, 4.3739e-05, 4.3616e-05, 4.3494e-05, 4.3372e-05, 4.3250e-05,\n", "         4.3129e-05, 4.3008e-05, 4.2887e-05, 4.2766e-05, 4.2646e-05, 4.2527e-05,\n", "         4.2407e-05, 4.2288e-05, 4.2170e-05, 4.2051e-05, 4.1933e-05, 4.1816e-05,\n", "         4.1698e-05, 4.1581e-05, 4.1464e-05, 4.1348e-05, 4.1232e-05, 4.1116e-05,\n", "         4.1001e-05, 4.0886e-05, 4.0771e-05, 4.0657e-05, 4.0542e-05, 4.0429e-05,\n", "         4.0315e-05, 4.0202e-05, 4.0089e-05, 3.9977e-05, 3.9864e-05, 3.9753e-05,\n", "         3.9641e-05, 3.9530e-05, 3.9419e-05, 3.9308e-05, 3.9198e-05, 3.9088e-05,\n", "         3.8978e-05, 3.8869e-05, 3.8760e-05, 3.8651e-05, 3.8542e-05, 3.8434e-05,\n", "         3.8326e-05, 3.8219e-05, 3.8111e-05, 3.8004e-05, 3.7898e-05, 3.7791e-05,\n", "         3.7685e-05, 3.7580e-05, 3.7474e-05, 3.7369e-05, 3.7264e-05, 3.7159e-05,\n", "         3.7055e-05, 3.6951e-05, 3.6847e-05, 3.6744e-05, 3.6641e-05, 3.6538e-05,\n", "         3.6435e-05, 3.6333e-05, 3.6231e-05, 3.6129e-05, 3.6028e-05, 3.5927e-05,\n", "         3.5826e-05, 3.5726e-05, 3.5625e-05, 3.5525e-05, 3.5426e-05, 3.5326e-05,\n", "         3.5227e-05, 3.5128e-05, 3.5029e-05, 3.4931e-05, 3.4833e-05, 3.4735e-05,\n", "         3.4638e-05, 3.4541e-05, 3.4444e-05, 3.4347e-05, 3.4251e-05, 3.4154e-05,\n", "         3.4059e-05, 3.3963e-05, 3.3868e-05, 3.3773e-05, 3.3678e-05, 3.3583e-05,\n", "         3.3489e-05, 3.3395e-05, 3.3301e-05, 3.3208e-05, 3.3115e-05, 3.3022e-05,\n", "         3.2929e-05, 3.2837e-05, 3.2744e-05, 3.2652e-05, 3.2561e-05, 3.2469e-05,\n", "         3.2378e-05, 3.2287e-05, 3.2197e-05, 3.2106e-05, 3.2016e-05, 3.1926e-05,\n", "         3.1837e-05, 3.1747e-05, 3.1658e-05, 3.1569e-05, 3.1481e-05, 3.1393e-05,\n", "         3.1304e-05, 3.1217e-05, 3.1129e-05, 3.1042e-05, 3.0954e-05, 3.0868e-05,\n", "         3.0781e-05, 3.0695e-05, 3.0608e-05, 3.0522e-05, 3.0437e-05, 3.0351e-05,\n", "         3.0266e-05, 3.0181e-05, 3.0096e-05, 3.0012e-05, 2.9928e-05, 2.9844e-05,\n", "         2.9760e-05, 2.9676e-05, 2.9593e-05, 2.9510e-05, 2.9427e-05, 2.9345e-05,\n", "         2.9262e-05, 2.9180e-05, 2.9098e-05, 2.9017e-05, 2.8935e-05, 2.8854e-05,\n", "         2.8773e-05, 2.8692e-05, 2.8612e-05, 2.8531e-05, 2.8451e-05, 2.8371e-05,\n", "         2.8292e-05, 2.8212e-05, 2.8133e-05, 2.8054e-05, 2.7975e-05, 2.7897e-05,\n", "         2.7819e-05, 2.7741e-05, 2.7663e-05, 2.7585e-05, 2.7508e-05, 2.7430e-05,\n", "         2.7353e-05, 2.7277e-05, 2.7200e-05, 2.7124e-05, 2.7048e-05, 2.6972e-05,\n", "         2.6896e-05, 2.6820e-05, 2.6745e-05, 2.6670e-05, 2.6595e-05, 2.6521e-05,\n", "         2.6446e-05, 2.6372e-05, 2.6298e-05, 2.6224e-05, 2.6151e-05, 2.6077e-05,\n", "         2.6004e-05, 2.5931e-05, 2.5858e-05, 2.5786e-05, 2.5713e-05, 2.5641e-05,\n", "         2.5569e-05, 2.5497e-05, 2.5426e-05, 2.5354e-05, 2.5283e-05, 2.5212e-05,\n", "         2.5141e-05, 2.5071e-05, 2.5001e-05, 2.4930e-05, 2.4860e-05, 2.4791e-05,\n", "         2.4721e-05, 2.4652e-05, 2.4582e-05, 2.4513e-05, 2.4445e-05, 2.4376e-05,\n", "         2.4308e-05, 2.4239e-05, 2.4171e-05, 2.4103e-05, 2.4036e-05, 2.3968e-05,\n", "         2.3901e-05, 2.3834e-05, 2.3767e-05, 2.3700e-05, 2.3634e-05, 2.3568e-05,\n", "         2.3501e-05, 2.3435e-05, 2.3370e-05, 2.3304e-05, 2.3239e-05, 2.3173e-05,\n", "         2.3108e-05, 2.3044e-05, 2.2979e-05, 2.2914e-05, 2.2850e-05, 2.2786e-05,\n", "         2.2722e-05, 2.2658e-05, 2.2595e-05, 2.2531e-05, 2.2468e-05, 2.2405e-05,\n", "         2.2342e-05, 2.2279e-05, 2.2217e-05, 2.2154e-05, 2.2092e-05, 2.2030e-05,\n", "         2.1968e-05, 2.1907e-05, 2.1845e-05, 2.1784e-05, 2.1723e-05, 2.1662e-05,\n", "         2.1601e-05, 2.1540e-05, 2.1480e-05, 2.1420e-05, 2.1359e-05, 2.1299e-05,\n", "         2.1240e-05, 2.1180e-05, 2.1121e-05, 2.1061e-05, 2.1002e-05, 2.0943e-05,\n", "         2.0885e-05, 2.0826e-05, 2.0767e-05, 2.0709e-05, 2.0651e-05, 2.0593e-05,\n", "         2.0535e-05, 2.0478e-05, 2.0420e-05, 2.0363e-05, 2.0306e-05, 2.0249e-05,\n", "         2.0192e-05, 2.0135e-05, 2.0079e-05, 2.0022e-05, 1.9966e-05, 1.9910e-05,\n", "         1.9854e-05, 1.9798e-05, 1.9743e-05, 1.9687e-05, 1.9632e-05, 1.9577e-05,\n", "         1.9522e-05, 1.9467e-05, 1.9413e-05, 1.9358e-05, 1.9304e-05, 1.9250e-05,\n", "         1.9196e-05, 1.9142e-05, 1.9088e-05, 1.9034e-05, 1.8981e-05, 1.8928e-05,\n", "         1.8875e-05, 1.8822e-05, 1.8769e-05, 1.8716e-05, 1.8664e-05, 1.8611e-05,\n", "         1.8559e-05, 1.8507e-05, 1.8455e-05, 1.8403e-05, 1.8352e-05, 1.8300e-05,\n", "         1.8249e-05, 1.8197e-05, 1.8146e-05, 1.8095e-05, 1.8045e-05, 1.7994e-05,\n", "         1.7943e-05, 1.7893e-05, 1.7843e-05, 1.7793e-05, 1.7743e-05, 1.7693e-05,\n", "         1.7643e-05, 1.7594e-05, 1.7544e-05, 1.7495e-05, 1.7446e-05, 1.7397e-05,\n", "         1.7348e-05, 1.7300e-05, 1.7251e-05, 1.7203e-05, 1.7154e-05, 1.7106e-05,\n", "         1.7058e-05, 1.7010e-05, 1.6963e-05, 1.6915e-05, 1.6868e-05, 1.6820e-05,\n", "         1.6773e-05, 1.6726e-05, 1.6679e-05, 1.6632e-05, 1.6585e-05, 1.6539e-05,\n", "         1.6492e-05, 1.6446e-05, 1.6400e-05, 1.6354e-05, 1.6308e-05, 1.6262e-05,\n", "         1.6217e-05, 1.6171e-05, 1.6126e-05, 1.6080e-05, 1.6035e-05, 1.5990e-05,\n", "         1.5945e-05, 1.5901e-05, 1.5856e-05, 1.5812e-05, 1.5767e-05, 1.5723e-05,\n", "         1.5679e-05, 1.5635e-05, 1.5591e-05, 1.5547e-05, 1.5503e-05, 1.5460e-05,\n", "         1.5417e-05, 1.5373e-05, 1.5330e-05, 1.5287e-05, 1.5244e-05, 1.5201e-05,\n", "         1.5159e-05, 1.5116e-05, 1.5074e-05, 1.5031e-05, 1.4989e-05, 1.4947e-05,\n", "         1.4905e-05, 1.4863e-05, 1.4822e-05, 1.4780e-05, 1.4739e-05, 1.4697e-05,\n", "         1.4656e-05, 1.4615e-05, 1.4574e-05, 1.4533e-05, 1.4492e-05, 1.4451e-05,\n", "         1.4411e-05, 1.4370e-05, 1.4330e-05, 1.4290e-05, 1.4250e-05, 1.4210e-05,\n", "         1.4170e-05, 1.4130e-05, 1.4090e-05, 1.4051e-05, 1.4011e-05, 1.3972e-05,\n", "         1.3933e-05, 1.3894e-05, 1.3855e-05, 1.3816e-05, 1.3777e-05, 1.3739e-05,\n", "         1.3700e-05, 1.3661e-05, 1.3623e-05, 1.3585e-05, 1.3547e-05, 1.3509e-05,\n", "         1.3471e-05, 1.3433e-05, 1.3395e-05, 1.3358e-05, 1.3320e-05, 1.3283e-05,\n", "         1.3246e-05, 1.3208e-05, 1.3171e-05, 1.3134e-05, 1.3097e-05, 1.3061e-05,\n", "         1.3024e-05, 1.2987e-05, 1.2951e-05, 1.2915e-05, 1.2878e-05, 1.2842e-05,\n", "         1.2806e-05, 1.2770e-05, 1.2734e-05, 1.2699e-05, 1.2663e-05, 1.2628e-05,\n", "         1.2592e-05, 1.2557e-05, 1.2521e-05, 1.2486e-05, 1.2451e-05, 1.2416e-05,\n", "         1.2382e-05, 1.2347e-05, 1.2312e-05, 1.2278e-05, 1.2243e-05, 1.2209e-05,\n", "         1.2174e-05, 1.2140e-05, 1.2106e-05, 1.2072e-05, 1.2038e-05, 1.2005e-05,\n", "         1.1971e-05, 1.1937e-05, 1.1904e-05, 1.1870e-05, 1.1837e-05, 1.1804e-05,\n", "         1.1771e-05, 1.1738e-05, 1.1705e-05, 1.1672e-05, 1.1639e-05, 1.1606e-05,\n", "         1.1574e-05, 1.1541e-05, 1.1509e-05, 1.1477e-05, 1.1444e-05, 1.1412e-05,\n", "         1.1380e-05, 1.1348e-05, 1.1316e-05, 1.1285e-05, 1.1253e-05, 1.1221e-05,\n", "         1.1190e-05, 1.1159e-05, 1.1127e-05, 1.1096e-05, 1.1065e-05, 1.1034e-05,\n", "         1.1003e-05, 1.0972e-05, 1.0941e-05, 1.0910e-05, 1.0880e-05, 1.0849e-05,\n", "         1.0819e-05, 1.0788e-05, 1.0758e-05, 1.0728e-05, 1.0698e-05, 1.0668e-05,\n", "         1.0638e-05, 1.0608e-05, 1.0578e-05, 1.0549e-05, 1.0519e-05, 1.0489e-05,\n", "         1.0460e-05, 1.0431e-05, 1.0401e-05, 1.0372e-05, 1.0343e-05, 1.0314e-05,\n", "         1.0285e-05, 1.0256e-05, 1.0227e-05, 1.0199e-05, 1.0170e-05, 1.0142e-05,\n", "         1.0113e-05, 1.0085e-05, 1.0056e-05, 1.0028e-05], device='cuda:0'),\n", " tensor([1.0000e+00, 9.9719e-01, 9.9439e-01, 9.9160e-01, 9.8882e-01, 9.8604e-01,\n", "         9.8328e-01, 9.8052e-01, 9.7776e-01, 9.7502e-01, 9.7228e-01, 9.6955e-01,\n", "         9.6683e-01, 9.6412e-01, 9.6141e-01, 9.5871e-01, 9.5602e-01, 9.5334e-01,\n", "         9.5066e-01, 9.4800e-01, 9.4534e-01, 9.4268e-01, 9.4004e-01, 9.3740e-01,\n", "         9.3477e-01, 9.3214e-01, 9.2953e-01, 9.2692e-01, 9.2432e-01, 9.2172e-01,\n", "         9.1913e-01, 9.1655e-01, 9.1398e-01, 9.1142e-01, 9.0886e-01, 9.0631e-01,\n", "         9.0376e-01, 9.0123e-01, 8.9870e-01, 8.9617e-01, 8.9366e-01, 8.9115e-01,\n", "         8.8865e-01, 8.8616e-01, 8.8367e-01, 8.8119e-01, 8.7871e-01, 8.7625e-01,\n", "         8.7379e-01, 8.7134e-01, 8.6889e-01, 8.6645e-01, 8.6402e-01, 8.6159e-01,\n", "         8.5918e-01, 8.5676e-01, 8.5436e-01, 8.5196e-01, 8.4957e-01, 8.4719e-01,\n", "         8.4481e-01, 8.4244e-01, 8.4007e-01, 8.3771e-01, 8.3536e-01, 8.3302e-01,\n", "         8.3068e-01, 8.2835e-01, 8.2602e-01, 8.2370e-01, 8.2139e-01, 8.1909e-01,\n", "         8.1679e-01, 8.1450e-01, 8.1221e-01, 8.0993e-01, 8.0766e-01, 8.0539e-01,\n", "         8.0313e-01, 8.0087e-01, 7.9863e-01, 7.9638e-01, 7.9415e-01, 7.9192e-01,\n", "         7.8970e-01, 7.8748e-01, 7.8527e-01, 7.8307e-01, 7.8087e-01, 7.7868e-01,\n", "         7.7649e-01, 7.7431e-01, 7.7214e-01, 7.6997e-01, 7.6781e-01, 7.6566e-01,\n", "         7.6351e-01, 7.6136e-01, 7.5923e-01, 7.5709e-01, 7.5497e-01, 7.5285e-01,\n", "         7.5074e-01, 7.4863e-01, 7.4653e-01, 7.4443e-01, 7.4234e-01, 7.4026e-01,\n", "         7.3818e-01, 7.3611e-01, 7.3404e-01, 7.3198e-01, 7.2993e-01, 7.2788e-01,\n", "         7.2584e-01, 7.2380e-01, 7.2177e-01, 7.1974e-01, 7.1772e-01, 7.1571e-01,\n", "         7.1370e-01, 7.1170e-01, 7.0970e-01, 7.0771e-01, 7.0572e-01, 7.0374e-01,\n", "         7.0176e-01, 6.9979e-01, 6.9783e-01, 6.9587e-01, 6.9392e-01, 6.9197e-01,\n", "         6.9003e-01, 6.8809e-01, 6.8616e-01, 6.8423e-01, 6.8231e-01, 6.8040e-01,\n", "         6.7849e-01, 6.7658e-01, 6.7469e-01, 6.7279e-01, 6.7090e-01, 6.6902e-01,\n", "         6.6714e-01, 6.6527e-01, 6.6340e-01, 6.6154e-01, 6.5968e-01, 6.5783e-01,\n", "         6.5599e-01, 6.5414e-01, 6.5231e-01, 6.5048e-01, 6.4865e-01, 6.4683e-01,\n", "         6.4502e-01, 6.4321e-01, 6.4140e-01, 6.3960e-01, 6.3780e-01, 6.3601e-01,\n", "         6.3423e-01, 6.3245e-01, 6.3067e-01, 6.2890e-01, 6.2714e-01, 6.2538e-01,\n", "         6.2362e-01, 6.2187e-01, 6.2013e-01, 6.1839e-01, 6.1665e-01, 6.1492e-01,\n", "         6.1319e-01, 6.1147e-01, 6.0976e-01, 6.0804e-01, 6.0634e-01, 6.0464e-01,\n", "         6.0294e-01, 6.0125e-01, 5.9956e-01, 5.9788e-01, 5.9620e-01, 5.9452e-01,\n", "         5.9286e-01, 5.9119e-01, 5.8953e-01, 5.8788e-01, 5.8623e-01, 5.8458e-01,\n", "         5.8294e-01, 5.8131e-01, 5.7967e-01, 5.7805e-01, 5.7642e-01, 5.7481e-01,\n", "         5.7319e-01, 5.7158e-01, 5.6998e-01, 5.6838e-01, 5.6678e-01, 5.6519e-01,\n", "         5.6361e-01, 5.6203e-01, 5.6045e-01, 5.5887e-01, 5.5731e-01, 5.5574e-01,\n", "         5.5418e-01, 5.5263e-01, 5.5108e-01, 5.4953e-01, 5.4799e-01, 5.4645e-01,\n", "         5.4491e-01, 5.4338e-01, 5.4186e-01, 5.4034e-01, 5.3882e-01, 5.3731e-01,\n", "         5.3580e-01, 5.3430e-01, 5.3280e-01, 5.3130e-01, 5.2981e-01, 5.2832e-01,\n", "         5.2684e-01, 5.2536e-01, 5.2389e-01, 5.2242e-01, 5.2095e-01, 5.1949e-01,\n", "         5.1803e-01, 5.1658e-01, 5.1513e-01, 5.1368e-01, 5.1224e-01, 5.1080e-01,\n", "         5.0937e-01, 5.0794e-01, 5.0651e-01, 5.0509e-01, 5.0367e-01, 5.0226e-01,\n", "         5.0085e-01, 4.9944e-01, 4.9804e-01, 4.9664e-01, 4.9525e-01, 4.9386e-01,\n", "         4.9247e-01, 4.9109e-01, 4.8971e-01, 4.8834e-01, 4.8697e-01, 4.8560e-01,\n", "         4.8424e-01, 4.8288e-01, 4.8152e-01, 4.8017e-01, 4.7882e-01, 4.7748e-01,\n", "         4.7614e-01, 4.7480e-01, 4.7347e-01, 4.7214e-01, 4.7082e-01, 4.6949e-01,\n", "         4.6818e-01, 4.6686e-01, 4.6555e-01, 4.6425e-01, 4.6294e-01, 4.6164e-01,\n", "         4.6035e-01, 4.5906e-01, 4.5777e-01, 4.5648e-01, 4.5520e-01, 4.5392e-01,\n", "         4.5265e-01, 4.5138e-01, 4.5011e-01, 4.4885e-01, 4.4759e-01, 4.4633e-01,\n", "         4.4508e-01, 4.4383e-01, 4.4258e-01, 4.4134e-01, 4.4010e-01, 4.3887e-01,\n", "         4.3764e-01, 4.3641e-01, 4.3518e-01, 4.3396e-01, 4.3274e-01, 4.3153e-01,\n", "         4.3032e-01, 4.2911e-01, 4.2791e-01, 4.2670e-01, 4.2551e-01, 4.2431e-01,\n", "         4.2312e-01, 4.2193e-01, 4.2075e-01, 4.1957e-01, 4.1839e-01, 4.1722e-01,\n", "         4.1605e-01, 4.1488e-01, 4.1371e-01, 4.1255e-01, 4.1139e-01, 4.1024e-01,\n", "         4.0909e-01, 4.0794e-01, 4.0679e-01, 4.0565e-01, 4.0451e-01, 4.0338e-01,\n", "         4.0225e-01, 4.0112e-01, 3.9999e-01, 3.9887e-01, 3.9775e-01, 3.9663e-01,\n", "         3.9552e-01, 3.9441e-01, 3.9330e-01, 3.9220e-01, 3.9110e-01, 3.9000e-01,\n", "         3.8891e-01, 3.8781e-01, 3.8673e-01, 3.8564e-01, 3.8456e-01, 3.8348e-01,\n", "         3.8240e-01, 3.8133e-01, 3.8026e-01, 3.7919e-01, 3.7813e-01, 3.7706e-01,\n", "         3.7601e-01, 3.7495e-01, 3.7390e-01, 3.7285e-01, 3.7180e-01, 3.7076e-01,\n", "         3.6972e-01, 3.6868e-01, 3.6765e-01, 3.6661e-01, 3.6558e-01, 3.6456e-01,\n", "         3.6354e-01, 3.6252e-01, 3.6150e-01, 3.6048e-01, 3.5947e-01, 3.5846e-01,\n", "         3.5746e-01, 3.5645e-01, 3.5545e-01, 3.5445e-01, 3.5346e-01, 3.5247e-01,\n", "         3.5148e-01, 3.5049e-01, 3.4951e-01, 3.4853e-01, 3.4755e-01, 3.4657e-01,\n", "         3.4560e-01, 3.4463e-01, 3.4366e-01, 3.4270e-01, 3.4174e-01, 3.4078e-01,\n", "         3.3982e-01, 3.3887e-01, 3.3792e-01, 3.3697e-01, 3.3602e-01, 3.3508e-01,\n", "         3.3414e-01, 3.3320e-01, 3.3226e-01, 3.3133e-01, 3.3040e-01, 3.2947e-01,\n", "         3.2855e-01, 3.2763e-01, 3.2671e-01, 3.2579e-01, 3.2488e-01, 3.2396e-01,\n", "         3.2306e-01, 3.2215e-01, 3.2124e-01, 3.2034e-01, 3.1944e-01, 3.1855e-01,\n", "         3.1765e-01, 3.1676e-01, 3.1587e-01, 3.1499e-01, 3.1410e-01, 3.1322e-01,\n", "         3.1234e-01, 3.1146e-01, 3.1059e-01, 3.0972e-01, 3.0885e-01, 3.0798e-01,\n", "         3.0712e-01, 3.0626e-01, 3.0540e-01, 3.0454e-01, 3.0368e-01, 3.0283e-01,\n", "         3.0198e-01, 3.0113e-01, 3.0029e-01, 2.9945e-01, 2.9861e-01, 2.9777e-01,\n", "         2.9693e-01, 2.9610e-01, 2.9527e-01, 2.9444e-01, 2.9361e-01, 2.9279e-01,\n", "         2.9197e-01, 2.9115e-01, 2.9033e-01, 2.8951e-01, 2.8870e-01, 2.8789e-01,\n", "         2.8708e-01, 2.8628e-01, 2.8547e-01, 2.8467e-01, 2.8387e-01, 2.8308e-01,\n", "         2.8228e-01, 2.8149e-01, 2.8070e-01, 2.7991e-01, 2.7913e-01, 2.7834e-01,\n", "         2.7756e-01, 2.7678e-01, 2.7601e-01, 2.7523e-01, 2.7446e-01, 2.7369e-01,\n", "         2.7292e-01, 2.7215e-01, 2.7139e-01, 2.7063e-01, 2.6987e-01, 2.6911e-01,\n", "         2.6836e-01, 2.6760e-01, 2.6685e-01, 2.6610e-01, 2.6536e-01, 2.6461e-01,\n", "         2.6387e-01, 2.6313e-01, 2.6239e-01, 2.6165e-01, 2.6092e-01, 2.6019e-01,\n", "         2.5946e-01, 2.5873e-01, 2.5800e-01, 2.5728e-01, 2.5655e-01, 2.5583e-01,\n", "         2.5512e-01, 2.5440e-01, 2.5369e-01, 2.5297e-01, 2.5226e-01, 2.5156e-01,\n", "         2.5085e-01, 2.5015e-01, 2.4944e-01, 2.4874e-01, 2.4805e-01, 2.4735e-01,\n", "         2.4665e-01, 2.4596e-01, 2.4527e-01, 2.4458e-01, 2.4390e-01, 2.4321e-01,\n", "         2.4253e-01, 2.4185e-01, 2.4117e-01, 2.4049e-01, 2.3982e-01, 2.3915e-01,\n", "         2.3847e-01, 2.3780e-01, 2.3714e-01, 2.3647e-01, 2.3581e-01, 2.3515e-01,\n", "         2.3449e-01, 2.3383e-01, 2.3317e-01, 2.3252e-01, 2.3186e-01, 2.3121e-01,\n", "         2.3056e-01, 2.2992e-01, 2.2927e-01, 2.2863e-01, 2.2799e-01, 2.2735e-01,\n", "         2.2671e-01, 2.2607e-01, 2.2544e-01, 2.2481e-01, 2.2417e-01, 2.2355e-01,\n", "         2.2292e-01, 2.2229e-01, 2.2167e-01, 2.2105e-01, 2.2043e-01, 2.1981e-01,\n", "         2.1919e-01, 2.1857e-01, 2.1796e-01, 2.1735e-01, 2.1674e-01, 2.1613e-01,\n", "         2.1552e-01, 2.1492e-01, 2.1432e-01, 2.1371e-01, 2.1311e-01, 2.1252e-01,\n", "         2.1192e-01, 2.1133e-01, 2.1073e-01, 2.1014e-01, 2.0955e-01, 2.0896e-01,\n", "         2.0838e-01, 2.0779e-01, 2.0721e-01, 2.0663e-01, 2.0605e-01, 2.0547e-01,\n", "         2.0489e-01, 2.0432e-01, 2.0374e-01, 2.0317e-01, 2.0260e-01, 2.0203e-01,\n", "         2.0146e-01, 2.0090e-01, 2.0034e-01, 1.9977e-01, 1.9921e-01, 1.9865e-01,\n", "         1.9810e-01, 1.9754e-01, 1.9699e-01, 1.9643e-01, 1.9588e-01, 1.9533e-01,\n", "         1.9478e-01, 1.9424e-01, 1.9369e-01, 1.9315e-01, 1.9261e-01, 1.9206e-01,\n", "         1.9153e-01, 1.9099e-01, 1.9045e-01, 1.8992e-01, 1.8938e-01, 1.8885e-01,\n", "         1.8832e-01, 1.8779e-01, 1.8727e-01, 1.8674e-01, 1.8622e-01, 1.8569e-01,\n", "         1.8517e-01, 1.8465e-01, 1.8414e-01, 1.8362e-01, 1.8310e-01, 1.8259e-01,\n", "         1.8208e-01, 1.8157e-01, 1.8106e-01, 1.8055e-01, 1.8004e-01, 1.7954e-01,\n", "         1.7903e-01, 1.7853e-01, 1.7803e-01, 1.7753e-01, 1.7703e-01, 1.7653e-01,\n", "         1.7604e-01, 1.7554e-01, 1.7505e-01, 1.7456e-01, 1.7407e-01, 1.7358e-01,\n", "         1.7309e-01, 1.7261e-01, 1.7212e-01, 1.7164e-01, 1.7116e-01, 1.7068e-01,\n", "         1.7020e-01, 1.6972e-01, 1.6924e-01, 1.6877e-01, 1.6830e-01, 1.6782e-01,\n", "         1.6735e-01, 1.6688e-01, 1.6641e-01, 1.6595e-01, 1.6548e-01, 1.6502e-01,\n", "         1.6455e-01, 1.6409e-01, 1.6363e-01, 1.6317e-01, 1.6271e-01, 1.6226e-01,\n", "         1.6180e-01, 1.6135e-01, 1.6090e-01, 1.6044e-01, 1.5999e-01, 1.5954e-01,\n", "         1.5910e-01, 1.5865e-01, 1.5820e-01, 1.5776e-01, 1.5732e-01, 1.5688e-01,\n", "         1.5644e-01, 1.5600e-01, 1.5556e-01, 1.5512e-01, 1.5469e-01, 1.5425e-01,\n", "         1.5382e-01, 1.5339e-01, 1.5296e-01, 1.5253e-01, 1.5210e-01, 1.5167e-01,\n", "         1.5125e-01, 1.5082e-01, 1.5040e-01, 1.4998e-01, 1.4956e-01, 1.4914e-01,\n", "         1.4872e-01, 1.4830e-01, 1.4788e-01, 1.4747e-01, 1.4706e-01, 1.4664e-01,\n", "         1.4623e-01, 1.4582e-01, 1.4541e-01, 1.4500e-01, 1.4460e-01, 1.4419e-01,\n", "         1.4379e-01, 1.4338e-01, 1.4298e-01, 1.4258e-01, 1.4218e-01, 1.4178e-01,\n", "         1.4138e-01, 1.4098e-01, 1.4059e-01, 1.4019e-01, 1.3980e-01, 1.3941e-01,\n", "         1.3902e-01, 1.3863e-01, 1.3824e-01, 1.3785e-01, 1.3746e-01, 1.3708e-01,\n", "         1.3669e-01, 1.3631e-01, 1.3593e-01, 1.3554e-01, 1.3516e-01, 1.3478e-01,\n", "         1.3441e-01, 1.3403e-01, 1.3365e-01, 1.3328e-01, 1.3290e-01, 1.3253e-01,\n", "         1.3216e-01, 1.3179e-01, 1.3142e-01, 1.3105e-01, 1.3068e-01, 1.3031e-01,\n", "         1.2995e-01, 1.2958e-01, 1.2922e-01, 1.2886e-01, 1.2850e-01, 1.2813e-01,\n", "         1.2777e-01, 1.2742e-01, 1.2706e-01, 1.2670e-01, 1.2635e-01, 1.2599e-01,\n", "         1.2564e-01, 1.2529e-01, 1.2493e-01, 1.2458e-01, 1.2423e-01, 1.2388e-01,\n", "         1.2354e-01, 1.2319e-01, 1.2284e-01, 1.2250e-01, 1.2216e-01, 1.2181e-01,\n", "         1.2147e-01, 1.2113e-01, 1.2079e-01, 1.2045e-01, 1.2011e-01, 1.1978e-01,\n", "         1.1944e-01, 1.1910e-01, 1.1877e-01, 1.1844e-01, 1.1810e-01, 1.1777e-01,\n", "         1.1744e-01, 1.1711e-01, 1.1678e-01, 1.1646e-01, 1.1613e-01, 1.1580e-01,\n", "         1.1548e-01, 1.1515e-01, 1.1483e-01, 1.1451e-01, 1.1419e-01, 1.1387e-01,\n", "         1.1355e-01, 1.1323e-01, 1.1291e-01, 1.1259e-01, 1.1228e-01, 1.1196e-01,\n", "         1.1165e-01, 1.1133e-01, 1.1102e-01, 1.1071e-01, 1.1040e-01, 1.1009e-01,\n", "         1.0978e-01, 1.0947e-01, 1.0917e-01, 1.0886e-01, 1.0855e-01, 1.0825e-01,\n", "         1.0795e-01, 1.0764e-01, 1.0734e-01, 1.0704e-01, 1.0674e-01, 1.0644e-01,\n", "         1.0614e-01, 1.0584e-01, 1.0554e-01, 1.0525e-01, 1.0495e-01, 1.0466e-01,\n", "         1.0436e-01, 1.0407e-01, 1.0378e-01, 1.0349e-01, 1.0320e-01, 1.0291e-01,\n", "         1.0262e-01, 1.0233e-01, 1.0204e-01, 1.0176e-01, 1.0147e-01, 1.0119e-01,\n", "         1.0090e-01, 1.0062e-01, 1.0034e-01, 1.0006e-01, 9.9775e-02, 9.9495e-02,\n", "         9.9216e-02, 9.8938e-02, 9.8660e-02, 9.8383e-02, 9.8107e-02, 9.7831e-02,\n", "         9.7557e-02, 9.7283e-02, 9.7010e-02, 9.6738e-02, 9.6466e-02, 9.6195e-02,\n", "         9.5925e-02, 9.5656e-02, 9.5388e-02, 9.5120e-02, 9.4853e-02, 9.4587e-02,\n", "         9.4321e-02, 9.4056e-02, 9.3792e-02, 9.3529e-02, 9.3267e-02, 9.3005e-02,\n", "         9.2744e-02, 9.2484e-02, 9.2224e-02, 9.1965e-02, 9.1707e-02, 9.1450e-02,\n", "         9.1193e-02, 9.0937e-02, 9.0682e-02, 9.0427e-02, 9.0173e-02, 8.9920e-02,\n", "         8.9668e-02, 8.9416e-02, 8.9165e-02, 8.8915e-02, 8.8665e-02, 8.8416e-02,\n", "         8.8168e-02, 8.7921e-02, 8.7674e-02, 8.7428e-02, 8.7183e-02, 8.6938e-02,\n", "         8.6694e-02, 8.6451e-02, 8.6208e-02, 8.5966e-02, 8.5725e-02, 8.5484e-02,\n", "         8.5244e-02, 8.5005e-02, 8.4766e-02, 8.4528e-02, 8.4291e-02, 8.4054e-02,\n", "         8.3818e-02, 8.3583e-02, 8.3349e-02, 8.3115e-02, 8.2881e-02, 8.2649e-02,\n", "         8.2417e-02, 8.2185e-02, 8.1955e-02, 8.1725e-02, 8.1495e-02, 8.1267e-02,\n", "         8.1039e-02, 8.0811e-02, 8.0584e-02, 8.0358e-02, 8.0132e-02, 7.9908e-02,\n", "         7.9683e-02, 7.9460e-02, 7.9237e-02, 7.9014e-02, 7.8792e-02, 7.8571e-02,\n", "         7.8351e-02, 7.8131e-02, 7.7911e-02, 7.7693e-02, 7.7475e-02, 7.7257e-02,\n", "         7.7040e-02, 7.6824e-02, 7.6609e-02, 7.6394e-02, 7.6179e-02, 7.5965e-02,\n", "         7.5752e-02, 7.5539e-02, 7.5327e-02, 7.5116e-02, 7.4905e-02, 7.4695e-02,\n", "         7.4485e-02, 7.4276e-02, 7.4068e-02, 7.3860e-02, 7.3652e-02, 7.3446e-02,\n", "         7.3240e-02, 7.3034e-02, 7.2829e-02, 7.2625e-02, 7.2421e-02, 7.2218e-02,\n", "         7.2015e-02, 7.1813e-02, 7.1611e-02, 7.1410e-02, 7.1210e-02, 7.1010e-02,\n", "         7.0811e-02, 7.0612e-02, 7.0414e-02, 7.0216e-02, 7.0019e-02, 6.9822e-02,\n", "         6.9626e-02, 6.9431e-02, 6.9236e-02, 6.9042e-02, 6.8848e-02, 6.8655e-02,\n", "         6.8462e-02, 6.8270e-02, 6.8078e-02, 6.7887e-02, 6.7697e-02, 6.7507e-02,\n", "         6.7317e-02, 6.7128e-02, 6.6940e-02, 6.6752e-02, 6.6564e-02, 6.6378e-02,\n", "         6.6191e-02, 6.6005e-02, 6.5820e-02, 6.5635e-02, 6.5451e-02, 6.5268e-02,\n", "         6.5084e-02, 6.4902e-02, 6.4719e-02, 6.4538e-02, 6.4357e-02, 6.4176e-02,\n", "         6.3996e-02, 6.3816e-02, 6.3637e-02, 6.3459e-02, 6.3280e-02, 6.3103e-02,\n", "         6.2926e-02, 6.2749e-02, 6.2573e-02, 6.2397e-02, 6.2222e-02, 6.2048e-02,\n", "         6.1873e-02, 6.1700e-02, 6.1527e-02, 6.1354e-02, 6.1182e-02, 6.1010e-02,\n", "         6.0839e-02, 6.0668e-02, 6.0498e-02, 6.0328e-02, 6.0158e-02, 5.9990e-02,\n", "         5.9821e-02, 5.9653e-02, 5.9486e-02, 5.9319e-02, 5.9152e-02, 5.8986e-02,\n", "         5.8821e-02, 5.8656e-02, 5.8491e-02, 5.8327e-02, 5.8163e-02, 5.8000e-02,\n", "         5.7837e-02, 5.7675e-02, 5.7513e-02, 5.7352e-02, 5.7191e-02, 5.7030e-02,\n", "         5.6870e-02, 5.6710e-02, 5.6551e-02, 5.6392e-02, 5.6234e-02, 5.6076e-02,\n", "         5.5919e-02, 5.5762e-02, 5.5605e-02, 5.5449e-02, 5.5294e-02, 5.5139e-02,\n", "         5.4984e-02, 5.4829e-02, 5.4676e-02, 5.4522e-02, 5.4369e-02, 5.4216e-02,\n", "         5.4064e-02, 5.3912e-02, 5.3761e-02, 5.3610e-02, 5.3460e-02, 5.3310e-02,\n", "         5.3160e-02, 5.3011e-02, 5.2862e-02, 5.2714e-02, 5.2566e-02, 5.2418e-02,\n", "         5.2271e-02, 5.2124e-02, 5.1978e-02, 5.1832e-02, 5.1687e-02, 5.1542e-02,\n", "         5.1397e-02, 5.1253e-02, 5.1109e-02, 5.0965e-02, 5.0822e-02, 5.0680e-02,\n", "         5.0537e-02, 5.0396e-02, 5.0254e-02, 5.0113e-02, 4.9972e-02, 4.9832e-02,\n", "         4.9692e-02, 4.9553e-02, 4.9414e-02, 4.9275e-02, 4.9137e-02, 4.8999e-02,\n", "         4.8861e-02, 4.8724e-02, 4.8587e-02, 4.8451e-02, 4.8315e-02, 4.8179e-02,\n", "         4.8044e-02, 4.7909e-02, 4.7775e-02, 4.7641e-02, 4.7507e-02, 4.7374e-02,\n", "         4.7241e-02, 4.7108e-02, 4.6976e-02, 4.6844e-02, 4.6713e-02, 4.6581e-02,\n", "         4.6451e-02, 4.6320e-02, 4.6190e-02, 4.6061e-02, 4.5931e-02, 4.5802e-02,\n", "         4.5674e-02, 4.5546e-02, 4.5418e-02, 4.5290e-02, 4.5163e-02, 4.5036e-02,\n", "         4.4910e-02, 4.4784e-02, 4.4658e-02, 4.4533e-02, 4.4408e-02, 4.4283e-02,\n", "         4.4159e-02, 4.4035e-02, 4.3911e-02, 4.3788e-02, 4.3665e-02, 4.3543e-02,\n", "         4.3421e-02, 4.3299e-02, 4.3177e-02, 4.3056e-02, 4.2935e-02, 4.2815e-02,\n", "         4.2694e-02, 4.2575e-02, 4.2455e-02, 4.2336e-02, 4.2217e-02, 4.2099e-02,\n", "         4.1980e-02, 4.1863e-02, 4.1745e-02, 4.1628e-02, 4.1511e-02, 4.1395e-02,\n", "         4.1278e-02, 4.1163e-02, 4.1047e-02, 4.0932e-02, 4.0817e-02, 4.0702e-02,\n", "         4.0588e-02, 4.0474e-02, 4.0361e-02, 4.0247e-02, 4.0134e-02, 4.0022e-02,\n", "         3.9909e-02, 3.9797e-02, 3.9686e-02, 3.9574e-02, 3.9463e-02, 3.9352e-02,\n", "         3.9242e-02, 3.9132e-02, 3.9022e-02, 3.8912e-02, 3.8803e-02, 3.8694e-02,\n", "         3.8586e-02, 3.8477e-02, 3.8369e-02, 3.8262e-02, 3.8154e-02, 3.8047e-02,\n", "         3.7940e-02, 3.7834e-02, 3.7728e-02, 3.7622e-02, 3.7516e-02, 3.7411e-02,\n", "         3.7306e-02, 3.7201e-02, 3.7097e-02, 3.6993e-02, 3.6889e-02, 3.6785e-02,\n", "         3.6682e-02, 3.6579e-02, 3.6476e-02, 3.6374e-02, 3.6272e-02, 3.6170e-02,\n", "         3.6069e-02, 3.5967e-02, 3.5866e-02, 3.5766e-02, 3.5665e-02, 3.5565e-02,\n", "         3.5465e-02, 3.5366e-02, 3.5267e-02, 3.5168e-02, 3.5069e-02, 3.4970e-02,\n", "         3.4872e-02, 3.4774e-02, 3.4677e-02, 3.4579e-02, 3.4482e-02, 3.4386e-02,\n", "         3.4289e-02, 3.4193e-02, 3.4097e-02, 3.4001e-02, 3.3906e-02, 3.3811e-02,\n", "         3.3716e-02, 3.3621e-02, 3.3527e-02, 3.3433e-02, 3.3339e-02, 3.3245e-02,\n", "         3.3152e-02, 3.3059e-02, 3.2966e-02, 3.2873e-02, 3.2781e-02, 3.2689e-02,\n", "         3.2597e-02, 3.2506e-02, 3.2415e-02, 3.2324e-02, 3.2233e-02, 3.2143e-02,\n", "         3.2052e-02, 3.1962e-02, 3.1873e-02, 3.1783e-02, 3.1694e-02, 3.1605e-02,\n", "         3.1516e-02, 3.1428e-02, 3.1340e-02, 3.1252e-02, 3.1164e-02, 3.1076e-02,\n", "         3.0989e-02, 3.0902e-02, 3.0816e-02, 3.0729e-02, 3.0643e-02, 3.0557e-02,\n", "         3.0471e-02, 3.0385e-02, 3.0300e-02, 3.0215e-02, 3.0130e-02, 3.0046e-02,\n", "         2.9961e-02, 2.9877e-02, 2.9793e-02, 2.9710e-02, 2.9626e-02, 2.9543e-02,\n", "         2.9460e-02, 2.9378e-02, 2.9295e-02, 2.9213e-02, 2.9131e-02, 2.9049e-02,\n", "         2.8968e-02, 2.8886e-02, 2.8805e-02, 2.8724e-02, 2.8644e-02, 2.8563e-02,\n", "         2.8483e-02, 2.8403e-02, 2.8324e-02, 2.8244e-02, 2.8165e-02, 2.8086e-02,\n", "         2.8007e-02, 2.7928e-02, 2.7850e-02, 2.7772e-02, 2.7694e-02, 2.7616e-02,\n", "         2.7539e-02, 2.7461e-02, 2.7384e-02, 2.7307e-02, 2.7231e-02, 2.7154e-02,\n", "         2.7078e-02, 2.7002e-02, 2.6926e-02, 2.6851e-02, 2.6775e-02, 2.6700e-02,\n", "         2.6625e-02, 2.6550e-02, 2.6476e-02, 2.6402e-02, 2.6328e-02, 2.6254e-02,\n", "         2.6180e-02, 2.6106e-02, 2.6033e-02, 2.5960e-02, 2.5887e-02, 2.5815e-02,\n", "         2.5742e-02, 2.5670e-02, 2.5598e-02, 2.5526e-02, 2.5454e-02, 2.5383e-02,\n", "         2.5312e-02, 2.5241e-02, 2.5170e-02, 2.5099e-02, 2.5029e-02, 2.4958e-02,\n", "         2.4888e-02, 2.4818e-02, 2.4749e-02, 2.4679e-02, 2.4610e-02, 2.4541e-02,\n", "         2.4472e-02, 2.4403e-02, 2.4335e-02, 2.4267e-02, 2.4199e-02, 2.4131e-02,\n", "         2.4063e-02, 2.3995e-02, 2.3928e-02, 2.3861e-02, 2.3794e-02, 2.3727e-02,\n", "         2.3660e-02, 2.3594e-02, 2.3528e-02, 2.3462e-02, 2.3396e-02, 2.3330e-02,\n", "         2.3265e-02, 2.3199e-02, 2.3134e-02, 2.3069e-02, 2.3005e-02, 2.2940e-02,\n", "         2.2876e-02, 2.2812e-02, 2.2747e-02, 2.2684e-02, 2.2620e-02, 2.2556e-02,\n", "         2.2493e-02, 2.2430e-02, 2.2367e-02, 2.2304e-02, 2.2242e-02, 2.2179e-02,\n", "         2.2117e-02, 2.2055e-02, 2.1993e-02, 2.1931e-02, 2.1870e-02, 2.1808e-02,\n", "         2.1747e-02, 2.1686e-02, 2.1625e-02, 2.1565e-02, 2.1504e-02, 2.1444e-02,\n", "         2.1383e-02, 2.1323e-02, 2.1264e-02, 2.1204e-02, 2.1144e-02, 2.1085e-02,\n", "         2.1026e-02, 2.0967e-02, 2.0908e-02, 2.0849e-02, 2.0791e-02, 2.0732e-02,\n", "         2.0674e-02, 2.0616e-02, 2.0558e-02, 2.0501e-02, 2.0443e-02, 2.0386e-02,\n", "         2.0329e-02, 2.0271e-02, 2.0215e-02, 2.0158e-02, 2.0101e-02, 2.0045e-02,\n", "         1.9989e-02, 1.9932e-02, 1.9876e-02, 1.9821e-02, 1.9765e-02, 1.9710e-02,\n", "         1.9654e-02, 1.9599e-02, 1.9544e-02, 1.9489e-02, 1.9435e-02, 1.9380e-02,\n", "         1.9326e-02, 1.9271e-02, 1.9217e-02, 1.9163e-02, 1.9110e-02, 1.9056e-02,\n", "         1.9002e-02, 1.8949e-02, 1.8896e-02, 1.8843e-02, 1.8790e-02, 1.8737e-02,\n", "         1.8685e-02, 1.8632e-02, 1.8580e-02, 1.8528e-02, 1.8476e-02, 1.8424e-02,\n", "         1.8372e-02, 1.8321e-02, 1.8269e-02, 1.8218e-02, 1.8167e-02, 1.8116e-02,\n", "         1.8065e-02, 1.8014e-02, 1.7964e-02, 1.7913e-02, 1.7863e-02, 1.7813e-02,\n", "         1.7763e-02, 1.7713e-02, 1.7663e-02, 1.7614e-02, 1.7564e-02, 1.7515e-02,\n", "         1.7466e-02, 1.7417e-02, 1.7368e-02, 1.7319e-02, 1.7270e-02, 1.7222e-02,\n", "         1.7174e-02, 1.7125e-02, 1.7077e-02, 1.7029e-02, 1.6982e-02, 1.6934e-02,\n", "         1.6886e-02, 1.6839e-02, 1.6792e-02, 1.6745e-02, 1.6698e-02, 1.6651e-02,\n", "         1.6604e-02, 1.6557e-02, 1.6511e-02, 1.6465e-02, 1.6418e-02, 1.6372e-02,\n", "         1.6326e-02, 1.6281e-02, 1.6235e-02, 1.6189e-02, 1.6144e-02, 1.6099e-02,\n", "         1.6053e-02, 1.6008e-02, 1.5963e-02, 1.5919e-02, 1.5874e-02, 1.5829e-02,\n", "         1.5785e-02, 1.5741e-02, 1.5696e-02, 1.5652e-02, 1.5608e-02, 1.5565e-02,\n", "         1.5521e-02, 1.5477e-02, 1.5434e-02, 1.5391e-02, 1.5347e-02, 1.5304e-02,\n", "         1.5261e-02, 1.5219e-02, 1.5176e-02, 1.5133e-02, 1.5091e-02, 1.5048e-02,\n", "         1.5006e-02, 1.4964e-02, 1.4922e-02, 1.4880e-02, 1.4838e-02, 1.4797e-02,\n", "         1.4755e-02, 1.4714e-02, 1.4672e-02, 1.4631e-02, 1.4590e-02, 1.4549e-02,\n", "         1.4508e-02, 1.4468e-02, 1.4427e-02, 1.4387e-02, 1.4346e-02, 1.4306e-02,\n", "         1.4266e-02, 1.4226e-02, 1.4186e-02, 1.4146e-02, 1.4106e-02, 1.4067e-02,\n", "         1.4027e-02, 1.3988e-02, 1.3949e-02, 1.3909e-02, 1.3870e-02, 1.3831e-02,\n", "         1.3793e-02, 1.3754e-02, 1.3715e-02, 1.3677e-02, 1.3638e-02, 1.3600e-02,\n", "         1.3562e-02, 1.3524e-02, 1.3486e-02, 1.3448e-02, 1.3410e-02, 1.3373e-02,\n", "         1.3335e-02, 1.3298e-02, 1.3260e-02, 1.3223e-02, 1.3186e-02, 1.3149e-02,\n", "         1.3112e-02, 1.3075e-02, 1.3039e-02, 1.3002e-02, 1.2966e-02, 1.2929e-02,\n", "         1.2893e-02, 1.2857e-02, 1.2821e-02, 1.2785e-02, 1.2749e-02, 1.2713e-02,\n", "         1.2677e-02, 1.2642e-02, 1.2606e-02, 1.2571e-02, 1.2536e-02, 1.2500e-02,\n", "         1.2465e-02, 1.2430e-02, 1.2395e-02, 1.2361e-02, 1.2326e-02, 1.2291e-02,\n", "         1.2257e-02, 1.2222e-02, 1.2188e-02, 1.2154e-02, 1.2120e-02, 1.2086e-02,\n", "         1.2052e-02, 1.2018e-02, 1.1984e-02, 1.1951e-02, 1.1917e-02, 1.1884e-02,\n", "         1.1850e-02, 1.1817e-02, 1.1784e-02, 1.1751e-02, 1.1718e-02, 1.1685e-02,\n", "         1.1652e-02, 1.1619e-02, 1.1587e-02, 1.1554e-02, 1.1522e-02, 1.1490e-02,\n", "         1.1457e-02, 1.1425e-02, 1.1393e-02, 1.1361e-02, 1.1329e-02, 1.1297e-02,\n", "         1.1266e-02, 1.1234e-02, 1.1203e-02, 1.1171e-02, 1.1140e-02, 1.1108e-02,\n", "         1.1077e-02, 1.1046e-02, 1.1015e-02, 1.0984e-02, 1.0953e-02, 1.0923e-02,\n", "         1.0892e-02, 1.0861e-02, 1.0831e-02, 1.0801e-02, 1.0770e-02, 1.0740e-02,\n", "         1.0710e-02, 1.0680e-02, 1.0650e-02, 1.0620e-02, 1.0590e-02, 1.0560e-02,\n", "         1.0531e-02, 1.0501e-02, 1.0472e-02, 1.0442e-02, 1.0413e-02, 1.0384e-02,\n", "         1.0355e-02, 1.0326e-02, 1.0297e-02, 1.0268e-02, 1.0239e-02, 1.0210e-02,\n", "         1.0182e-02, 1.0153e-02, 1.0124e-02, 1.0096e-02, 1.0068e-02, 1.0039e-02,\n", "         1.0011e-02, 9.9832e-03, 9.9551e-03, 9.9272e-03, 9.8993e-03, 9.8715e-03,\n", "         9.8438e-03, 9.8162e-03, 9.7886e-03, 9.7612e-03, 9.7338e-03, 9.7065e-03,\n", "         9.6792e-03, 9.6520e-03, 9.6249e-03, 9.5979e-03, 9.5710e-03, 9.5441e-03,\n", "         9.5173e-03, 9.4906e-03, 9.4640e-03, 9.4374e-03, 9.4109e-03, 9.3845e-03,\n", "         9.3582e-03, 9.3319e-03, 9.3057e-03, 9.2796e-03, 9.2536e-03, 9.2276e-03,\n", "         9.2017e-03, 9.1759e-03, 9.1501e-03, 9.1244e-03, 9.0988e-03, 9.0733e-03,\n", "         9.0478e-03, 9.0224e-03, 8.9971e-03, 8.9718e-03, 8.9466e-03, 8.9215e-03,\n", "         8.8965e-03, 8.8715e-03, 8.8466e-03, 8.8218e-03, 8.7970e-03, 8.7723e-03,\n", "         8.7477e-03, 8.7232e-03, 8.6987e-03, 8.6743e-03, 8.6499e-03, 8.6256e-03,\n", "         8.6014e-03, 8.5773e-03, 8.5532e-03, 8.5292e-03, 8.5053e-03, 8.4814e-03,\n", "         8.4576e-03, 8.4338e-03, 8.4102e-03, 8.3866e-03, 8.3630e-03, 8.3395e-03,\n", "         8.3161e-03, 8.2928e-03, 8.2695e-03, 8.2463e-03, 8.2232e-03, 8.2001e-03,\n", "         8.1771e-03, 8.1541e-03, 8.1312e-03, 8.1084e-03, 8.0856e-03, 8.0630e-03,\n", "         8.0403e-03, 8.0178e-03, 7.9952e-03, 7.9728e-03, 7.9504e-03, 7.9281e-03,\n", "         7.9059e-03, 7.8837e-03, 7.8615e-03, 7.8395e-03, 7.8175e-03, 7.7955e-03,\n", "         7.7737e-03, 7.7518e-03, 7.7301e-03, 7.7084e-03, 7.6867e-03, 7.6652e-03,\n", "         7.6436e-03, 7.6222e-03, 7.6008e-03, 7.5795e-03, 7.5582e-03, 7.5370e-03,\n", "         7.5158e-03, 7.4947e-03, 7.4737e-03, 7.4527e-03, 7.4318e-03, 7.4109e-03,\n", "         7.3901e-03, 7.3694e-03, 7.3487e-03, 7.3281e-03, 7.3075e-03, 7.2870e-03,\n", "         7.2665e-03, 7.2462e-03, 7.2258e-03, 7.2055e-03, 7.1853e-03, 7.1651e-03,\n", "         7.1450e-03, 7.1250e-03, 7.1050e-03, 7.0850e-03, 7.0651e-03, 7.0453e-03,\n", "         7.0255e-03, 7.0058e-03, 6.9862e-03, 6.9665e-03, 6.9470e-03, 6.9275e-03,\n", "         6.9080e-03, 6.8887e-03, 6.8693e-03, 6.8500e-03, 6.8308e-03, 6.8116e-03,\n", "         6.7925e-03, 6.7735e-03, 6.7544e-03, 6.7355e-03, 6.7166e-03, 6.6977e-03,\n", "         6.6789e-03, 6.6602e-03, 6.6415e-03, 6.6228e-03, 6.6043e-03, 6.5857e-03,\n", "         6.5672e-03, 6.5488e-03, 6.5304e-03, 6.5121e-03, 6.4938e-03, 6.4756e-03,\n", "         6.4574e-03, 6.4393e-03, 6.4212e-03, 6.4032e-03, 6.3852e-03, 6.3673e-03,\n", "         6.3494e-03, 6.3316e-03, 6.3138e-03, 6.2961e-03, 6.2784e-03, 6.2608e-03,\n", "         6.2432e-03, 6.2257e-03, 6.2082e-03, 6.1908e-03, 6.1734e-03, 6.1561e-03,\n", "         6.1388e-03, 6.1216e-03, 6.1044e-03, 6.0873e-03, 6.0702e-03, 6.0532e-03,\n", "         6.0362e-03, 6.0192e-03, 6.0023e-03, 5.9855e-03, 5.9687e-03, 5.9519e-03,\n", "         5.9352e-03, 5.9186e-03, 5.9020e-03, 5.8854e-03, 5.8689e-03, 5.8524e-03,\n", "         5.8360e-03, 5.8196e-03, 5.8033e-03, 5.7870e-03, 5.7707e-03, 5.7545e-03,\n", "         5.7384e-03, 5.7223e-03, 5.7062e-03, 5.6902e-03, 5.6742e-03, 5.6583e-03,\n", "         5.6424e-03, 5.6266e-03, 5.6108e-03, 5.5950e-03, 5.5793e-03, 5.5637e-03,\n", "         5.5481e-03, 5.5325e-03, 5.5170e-03, 5.5015e-03, 5.4860e-03, 5.4706e-03,\n", "         5.4553e-03, 5.4400e-03, 5.4247e-03, 5.4095e-03, 5.3943e-03, 5.3791e-03,\n", "         5.3640e-03, 5.3490e-03, 5.3340e-03, 5.3190e-03, 5.3041e-03, 5.2892e-03,\n", "         5.2743e-03, 5.2595e-03, 5.2448e-03, 5.2301e-03, 5.2154e-03, 5.2007e-03,\n", "         5.1861e-03, 5.1716e-03, 5.1571e-03, 5.1426e-03, 5.1282e-03, 5.1138e-03,\n", "         5.0994e-03, 5.0851e-03, 5.0708e-03, 5.0566e-03, 5.0424e-03, 5.0282e-03,\n", "         5.0141e-03, 5.0001e-03, 4.9860e-03, 4.9720e-03, 4.9581e-03, 4.9442e-03,\n", "         4.9303e-03, 4.9164e-03, 4.9026e-03, 4.8889e-03, 4.8752e-03, 4.8615e-03,\n", "         4.8478e-03, 4.8342e-03, 4.8206e-03, 4.8071e-03, 4.7936e-03, 4.7802e-03,\n", "         4.7668e-03, 4.7534e-03, 4.7400e-03, 4.7267e-03, 4.7135e-03, 4.7002e-03,\n", "         4.6870e-03, 4.6739e-03, 4.6608e-03, 4.6477e-03, 4.6346e-03, 4.6216e-03,\n", "         4.6087e-03, 4.5957e-03, 4.5828e-03, 4.5700e-03, 4.5571e-03, 4.5443e-03,\n", "         4.5316e-03, 4.5189e-03, 4.5062e-03, 4.4935e-03, 4.4809e-03, 4.4683e-03,\n", "         4.4558e-03, 4.4433e-03, 4.4308e-03, 4.4184e-03, 4.4060e-03, 4.3936e-03,\n", "         4.3813e-03, 4.3690e-03, 4.3567e-03, 4.3445e-03, 4.3323e-03, 4.3201e-03,\n", "         4.3080e-03, 4.2959e-03, 4.2839e-03, 4.2718e-03, 4.2599e-03, 4.2479e-03,\n", "         4.2360e-03, 4.2241e-03, 4.2122e-03, 4.2004e-03, 4.1886e-03, 4.1769e-03,\n", "         4.1651e-03, 4.1534e-03, 4.1418e-03, 4.1302e-03, 4.1186e-03, 4.1070e-03,\n", "         4.0955e-03, 4.0840e-03, 4.0725e-03, 4.0611e-03, 4.0497e-03, 4.0383e-03,\n", "         4.0270e-03, 4.0157e-03, 4.0044e-03, 3.9932e-03, 3.9820e-03, 3.9708e-03,\n", "         3.9596e-03, 3.9485e-03, 3.9374e-03, 3.9264e-03, 3.9154e-03, 3.9044e-03,\n", "         3.8934e-03, 3.8825e-03, 3.8716e-03, 3.8607e-03, 3.8499e-03, 3.8391e-03,\n", "         3.8283e-03, 3.8176e-03, 3.8069e-03, 3.7962e-03, 3.7855e-03, 3.7749e-03,\n", "         3.7643e-03, 3.7537e-03, 3.7432e-03, 3.7327e-03, 3.7222e-03, 3.7118e-03,\n", "         3.7013e-03, 3.6910e-03, 3.6806e-03, 3.6703e-03, 3.6600e-03, 3.6497e-03,\n", "         3.6394e-03, 3.6292e-03, 3.6190e-03, 3.6089e-03, 3.5988e-03, 3.5887e-03,\n", "         3.5786e-03, 3.5685e-03, 3.5585e-03, 3.5485e-03, 3.5386e-03, 3.5286e-03,\n", "         3.5187e-03, 3.5089e-03, 3.4990e-03, 3.4892e-03, 3.4794e-03, 3.4696e-03,\n", "         3.4599e-03, 3.4502e-03, 3.4405e-03, 3.4308e-03, 3.4212e-03, 3.4116e-03,\n", "         3.4020e-03, 3.3925e-03, 3.3830e-03, 3.3735e-03, 3.3640e-03, 3.3546e-03,\n", "         3.3451e-03, 3.3357e-03, 3.3264e-03, 3.3170e-03, 3.3077e-03, 3.2985e-03,\n", "         3.2892e-03, 3.2800e-03, 3.2708e-03, 3.2616e-03, 3.2524e-03, 3.2433e-03,\n", "         3.2342e-03, 3.2251e-03, 3.2161e-03, 3.2070e-03, 3.1980e-03, 3.1891e-03,\n", "         3.1801e-03, 3.1712e-03, 3.1623e-03, 3.1534e-03, 3.1446e-03, 3.1357e-03,\n", "         3.1269e-03, 3.1181e-03, 3.1094e-03, 3.1007e-03, 3.0920e-03, 3.0833e-03,\n", "         3.0746e-03, 3.0660e-03, 3.0574e-03, 3.0488e-03, 3.0403e-03, 3.0317e-03,\n", "         3.0232e-03, 3.0147e-03, 3.0063e-03, 2.9978e-03, 2.9894e-03, 2.9810e-03,\n", "         2.9727e-03, 2.9643e-03, 2.9560e-03, 2.9477e-03, 2.9394e-03, 2.9312e-03,\n", "         2.9229e-03, 2.9147e-03, 2.9066e-03, 2.8984e-03, 2.8903e-03, 2.8822e-03,\n", "         2.8741e-03, 2.8660e-03, 2.8580e-03, 2.8499e-03, 2.8419e-03, 2.8340e-03,\n", "         2.8260e-03, 2.8181e-03, 2.8102e-03, 2.8023e-03, 2.7944e-03, 2.7866e-03,\n", "         2.7787e-03, 2.7709e-03, 2.7632e-03, 2.7554e-03, 2.7477e-03, 2.7400e-03,\n", "         2.7323e-03, 2.7246e-03, 2.7170e-03, 2.7093e-03, 2.7017e-03, 2.6941e-03,\n", "         2.6866e-03, 2.6790e-03, 2.6715e-03, 2.6640e-03, 2.6565e-03, 2.6491e-03,\n", "         2.6416e-03, 2.6342e-03, 2.6268e-03, 2.6195e-03, 2.6121e-03, 2.6048e-03,\n", "         2.5975e-03, 2.5902e-03, 2.5829e-03, 2.5757e-03, 2.5684e-03, 2.5612e-03,\n", "         2.5540e-03, 2.5469e-03, 2.5397e-03, 2.5326e-03, 2.5255e-03, 2.5184e-03,\n", "         2.5113e-03, 2.5043e-03, 2.4972e-03, 2.4902e-03, 2.4832e-03, 2.4763e-03,\n", "         2.4693e-03, 2.4624e-03, 2.4555e-03, 2.4486e-03, 2.4417e-03, 2.4349e-03,\n", "         2.4280e-03, 2.4212e-03, 2.4144e-03, 2.4076e-03, 2.4009e-03, 2.3941e-03,\n", "         2.3874e-03, 2.3807e-03, 2.3740e-03, 2.3674e-03, 2.3607e-03, 2.3541e-03,\n", "         2.3475e-03, 2.3409e-03, 2.3343e-03, 2.3278e-03, 2.3213e-03, 2.3147e-03,\n", "         2.3082e-03, 2.3018e-03, 2.2953e-03, 2.2889e-03, 2.2824e-03, 2.2760e-03,\n", "         2.2696e-03, 2.2633e-03, 2.2569e-03, 2.2506e-03, 2.2443e-03, 2.2380e-03,\n", "         2.2317e-03, 2.2254e-03, 2.2192e-03, 2.2129e-03, 2.2067e-03, 2.2005e-03,\n", "         2.1944e-03, 2.1882e-03, 2.1821e-03, 2.1759e-03, 2.1698e-03, 2.1637e-03,\n", "         2.1577e-03, 2.1516e-03, 2.1456e-03, 2.1395e-03, 2.1335e-03, 2.1276e-03,\n", "         2.1216e-03, 2.1156e-03, 2.1097e-03, 2.1038e-03, 2.0979e-03, 2.0920e-03,\n", "         2.0861e-03, 2.0802e-03, 2.0744e-03, 2.0686e-03, 2.0628e-03, 2.0570e-03,\n", "         2.0512e-03, 2.0455e-03, 2.0397e-03, 2.0340e-03, 2.0283e-03, 2.0226e-03,\n", "         2.0169e-03, 2.0113e-03, 2.0056e-03, 2.0000e-03, 1.9944e-03, 1.9888e-03,\n", "         1.9832e-03, 1.9776e-03, 1.9721e-03, 1.9665e-03, 1.9610e-03, 1.9555e-03,\n", "         1.9500e-03, 1.9445e-03, 1.9391e-03, 1.9336e-03, 1.9282e-03, 1.9228e-03,\n", "         1.9174e-03, 1.9120e-03, 1.9067e-03, 1.9013e-03, 1.8960e-03, 1.8907e-03,\n", "         1.8853e-03, 1.8801e-03, 1.8748e-03, 1.8695e-03, 1.8643e-03, 1.8590e-03,\n", "         1.8538e-03, 1.8486e-03, 1.8434e-03, 1.8382e-03, 1.8331e-03, 1.8279e-03,\n", "         1.8228e-03, 1.8177e-03, 1.8126e-03, 1.8075e-03, 1.8024e-03, 1.7974e-03,\n", "         1.7923e-03, 1.7873e-03, 1.7823e-03, 1.7773e-03, 1.7723e-03, 1.7673e-03,\n", "         1.7624e-03, 1.7574e-03, 1.7525e-03, 1.7476e-03, 1.7427e-03, 1.7378e-03,\n", "         1.7329e-03, 1.7280e-03, 1.7232e-03, 1.7183e-03, 1.7135e-03, 1.7087e-03,\n", "         1.7039e-03, 1.6991e-03, 1.6944e-03, 1.6896e-03, 1.6849e-03, 1.6801e-03,\n", "         1.6754e-03, 1.6707e-03, 1.6660e-03, 1.6613e-03, 1.6567e-03, 1.6520e-03,\n", "         1.6474e-03, 1.6428e-03, 1.6382e-03, 1.6336e-03, 1.6290e-03, 1.6244e-03,\n", "         1.6198e-03, 1.6153e-03, 1.6108e-03, 1.6062e-03, 1.6017e-03, 1.5972e-03,\n", "         1.5928e-03, 1.5883e-03, 1.5838e-03, 1.5794e-03, 1.5749e-03, 1.5705e-03,\n", "         1.5661e-03, 1.5617e-03, 1.5573e-03, 1.5530e-03, 1.5486e-03, 1.5443e-03,\n", "         1.5399e-03, 1.5356e-03, 1.5313e-03, 1.5270e-03, 1.5227e-03, 1.5184e-03,\n", "         1.5142e-03, 1.5099e-03, 1.5057e-03, 1.5015e-03, 1.4972e-03, 1.4930e-03,\n", "         1.4889e-03, 1.4847e-03, 1.4805e-03, 1.4764e-03, 1.4722e-03, 1.4681e-03,\n", "         1.4640e-03, 1.4598e-03, 1.4557e-03, 1.4517e-03, 1.4476e-03, 1.4435e-03,\n", "         1.4395e-03, 1.4354e-03, 1.4314e-03, 1.4274e-03, 1.4234e-03, 1.4194e-03,\n", "         1.4154e-03, 1.4114e-03, 1.4075e-03, 1.4035e-03, 1.3996e-03, 1.3956e-03,\n", "         1.3917e-03, 1.3878e-03, 1.3839e-03, 1.3800e-03, 1.3762e-03, 1.3723e-03,\n", "         1.3685e-03, 1.3646e-03, 1.3608e-03, 1.3570e-03, 1.3532e-03, 1.3494e-03,\n", "         1.3456e-03, 1.3418e-03, 1.3380e-03, 1.3343e-03, 1.3305e-03, 1.3268e-03,\n", "         1.3231e-03, 1.3194e-03, 1.3157e-03, 1.3120e-03, 1.3083e-03, 1.3046e-03,\n", "         1.3009e-03, 1.2973e-03, 1.2936e-03, 1.2900e-03, 1.2864e-03, 1.2828e-03,\n", "         1.2792e-03, 1.2756e-03, 1.2720e-03, 1.2684e-03, 1.2649e-03, 1.2613e-03,\n", "         1.2578e-03, 1.2543e-03, 1.2507e-03, 1.2472e-03, 1.2437e-03, 1.2402e-03,\n", "         1.2368e-03, 1.2333e-03, 1.2298e-03, 1.2264e-03, 1.2229e-03, 1.2195e-03,\n", "         1.2161e-03, 1.2127e-03, 1.2093e-03, 1.2059e-03, 1.2025e-03, 1.1991e-03,\n", "         1.1957e-03, 1.1924e-03, 1.1890e-03, 1.1857e-03, 1.1824e-03, 1.1791e-03,\n", "         1.1757e-03, 1.1724e-03, 1.1692e-03, 1.1659e-03, 1.1626e-03, 1.1593e-03,\n", "         1.1561e-03, 1.1528e-03, 1.1496e-03, 1.1464e-03, 1.1432e-03, 1.1399e-03,\n", "         1.1367e-03, 1.1336e-03, 1.1304e-03, 1.1272e-03, 1.1240e-03, 1.1209e-03,\n", "         1.1177e-03, 1.1146e-03, 1.1115e-03, 1.1084e-03, 1.1052e-03, 1.1021e-03,\n", "         1.0990e-03, 1.0960e-03, 1.0929e-03, 1.0898e-03, 1.0868e-03, 1.0837e-03,\n", "         1.0807e-03, 1.0776e-03, 1.0746e-03, 1.0716e-03, 1.0686e-03, 1.0656e-03,\n", "         1.0626e-03, 1.0596e-03, 1.0566e-03, 1.0537e-03, 1.0507e-03, 1.0478e-03,\n", "         1.0448e-03, 1.0419e-03, 1.0390e-03, 1.0361e-03, 1.0331e-03, 1.0302e-03,\n", "         1.0274e-03, 1.0245e-03, 1.0216e-03, 1.0187e-03, 1.0159e-03, 1.0130e-03,\n", "         1.0102e-03, 1.0073e-03, 1.0045e-03, 1.0017e-03, 9.9888e-04, 9.9607e-04,\n", "         9.9328e-04, 9.9049e-04, 9.8771e-04, 9.8494e-04, 9.8217e-04, 9.7942e-04,\n", "         9.7667e-04, 9.7392e-04, 9.7119e-04, 9.6847e-04, 9.6575e-04, 9.6304e-04,\n", "         9.6033e-04, 9.5764e-04, 9.5495e-04, 9.5227e-04, 9.4960e-04, 9.4693e-04,\n", "         9.4427e-04, 9.4162e-04, 9.3898e-04, 9.3634e-04, 9.3372e-04, 9.3110e-04,\n", "         9.2848e-04, 9.2588e-04, 9.2328e-04, 9.2069e-04, 9.1810e-04, 9.1552e-04,\n", "         9.1295e-04, 9.1039e-04, 9.0784e-04, 9.0529e-04, 9.0275e-04, 9.0021e-04,\n", "         8.9769e-04, 8.9517e-04, 8.9265e-04, 8.9015e-04, 8.8765e-04, 8.8516e-04,\n", "         8.8267e-04, 8.8020e-04, 8.7773e-04, 8.7526e-04, 8.7281e-04, 8.7036e-04,\n", "         8.6791e-04, 8.6548e-04, 8.6305e-04, 8.6063e-04, 8.5821e-04, 8.5580e-04,\n", "         8.5340e-04, 8.5100e-04, 8.4862e-04, 8.4623e-04, 8.4386e-04, 8.4149e-04,\n", "         8.3913e-04, 8.3677e-04, 8.3442e-04, 8.3208e-04, 8.2975e-04, 8.2742e-04,\n", "         8.2509e-04, 8.2278e-04, 8.2047e-04, 8.1817e-04, 8.1587e-04, 8.1358e-04,\n", "         8.1130e-04, 8.0902e-04, 8.0675e-04, 8.0448e-04, 8.0223e-04, 7.9997e-04,\n", "         7.9773e-04, 7.9549e-04, 7.9326e-04, 7.9103e-04, 7.8881e-04, 7.8660e-04,\n", "         7.8439e-04, 7.8219e-04, 7.7999e-04, 7.7780e-04, 7.7562e-04, 7.7344e-04,\n", "         7.7127e-04, 7.6911e-04, 7.6695e-04, 7.6479e-04, 7.6265e-04, 7.6051e-04,\n", "         7.5837e-04, 7.5624e-04, 7.5412e-04, 7.5200e-04, 7.4989e-04, 7.4779e-04,\n", "         7.4569e-04, 7.4360e-04, 7.4151e-04, 7.3943e-04, 7.3735e-04, 7.3528e-04,\n", "         7.3322e-04, 7.3116e-04, 7.2911e-04, 7.2706e-04, 7.2502e-04, 7.2299e-04,\n", "         7.2096e-04, 7.1893e-04, 7.1692e-04, 7.1490e-04, 7.1290e-04, 7.1090e-04,\n", "         7.0890e-04, 7.0691e-04, 7.0493e-04, 7.0295e-04, 7.0098e-04, 6.9901e-04,\n", "         6.9705e-04, 6.9509e-04, 6.9314e-04, 6.9119e-04, 6.8925e-04, 6.8732e-04,\n", "         6.8539e-04, 6.8347e-04, 6.8155e-04, 6.7963e-04, 6.7773e-04, 6.7582e-04,\n", "         6.7393e-04, 6.7204e-04, 6.7015e-04, 6.6827e-04, 6.6639e-04, 6.6452e-04,\n", "         6.6266e-04, 6.6080e-04, 6.5894e-04, 6.5709e-04, 6.5525e-04, 6.5341e-04,\n", "         6.5158e-04, 6.4975e-04, 6.4792e-04, 6.4610e-04, 6.4429e-04, 6.4248e-04,\n", "         6.4068e-04, 6.3888e-04, 6.3709e-04, 6.3530e-04, 6.3352e-04, 6.3174e-04,\n", "         6.2996e-04, 6.2820e-04, 6.2643e-04, 6.2468e-04, 6.2292e-04, 6.2117e-04,\n", "         6.1943e-04, 6.1769e-04, 6.1596e-04, 6.1423e-04, 6.1250e-04, 6.1079e-04,\n", "         6.0907e-04, 6.0736e-04, 6.0566e-04, 6.0396e-04, 6.0226e-04, 6.0057e-04,\n", "         5.9889e-04, 5.9720e-04, 5.9553e-04, 5.9386e-04, 5.9219e-04, 5.9053e-04,\n", "         5.8887e-04, 5.8722e-04, 5.8557e-04, 5.8393e-04, 5.8229e-04, 5.8065e-04,\n", "         5.7902e-04, 5.7740e-04, 5.7578e-04, 5.7416e-04, 5.7255e-04, 5.7094e-04,\n", "         5.6934e-04, 5.6774e-04, 5.6615e-04, 5.6456e-04, 5.6297e-04, 5.6139e-04,\n", "         5.5982e-04, 5.5825e-04, 5.5668e-04, 5.5512e-04, 5.5356e-04, 5.5201e-04,\n", "         5.5046e-04, 5.4891e-04, 5.4737e-04, 5.4583e-04, 5.4430e-04, 5.4277e-04,\n", "         5.4125e-04, 5.3973e-04, 5.3822e-04, 5.3671e-04, 5.3520e-04, 5.3370e-04,\n", "         5.3220e-04, 5.3071e-04, 5.2922e-04, 5.2773e-04, 5.2625e-04, 5.2477e-04,\n", "         5.2330e-04, 5.2183e-04, 5.2037e-04, 5.1891e-04, 5.1745e-04, 5.1600e-04,\n", "         5.1455e-04, 5.1310e-04, 5.1166e-04, 5.1023e-04, 5.0880e-04, 5.0737e-04,\n", "         5.0594e-04, 5.0452e-04, 5.0311e-04, 5.0169e-04, 5.0029e-04, 4.9888e-04,\n", "         4.9748e-04, 4.9609e-04, 4.9469e-04, 4.9330e-04, 4.9192e-04, 4.9054e-04,\n", "         4.8916e-04, 4.8779e-04, 4.8642e-04, 4.8506e-04, 4.8369e-04, 4.8234e-04,\n", "         4.8098e-04, 4.7963e-04, 4.7829e-04, 4.7694e-04, 4.7560e-04, 4.7427e-04,\n", "         4.7294e-04, 4.7161e-04, 4.7029e-04, 4.6897e-04, 4.6765e-04, 4.6634e-04,\n", "         4.6503e-04, 4.6372e-04, 4.6242e-04, 4.6112e-04, 4.5983e-04, 4.5854e-04,\n", "         4.5725e-04, 4.5597e-04, 4.5469e-04, 4.5341e-04, 4.5214e-04, 4.5087e-04,\n", "         4.4961e-04, 4.4834e-04, 4.4709e-04, 4.4583e-04, 4.4458e-04, 4.4333e-04,\n", "         4.4209e-04, 4.4085e-04, 4.3961e-04, 4.3837e-04, 4.3714e-04, 4.3592e-04,\n", "         4.3469e-04, 4.3347e-04, 4.3226e-04, 4.3104e-04, 4.2983e-04, 4.2863e-04,\n", "         4.2742e-04, 4.2622e-04, 4.2503e-04, 4.2384e-04, 4.2265e-04, 4.2146e-04,\n", "         4.2028e-04, 4.1910e-04, 4.1792e-04, 4.1675e-04, 4.1558e-04, 4.1441e-04,\n", "         4.1325e-04, 4.1209e-04, 4.1093e-04, 4.0978e-04, 4.0863e-04, 4.0748e-04,\n", "         4.0634e-04, 4.0520e-04, 4.0406e-04, 4.0293e-04, 4.0179e-04, 4.0067e-04,\n", "         3.9954e-04, 3.9842e-04, 3.9730e-04, 3.9619e-04, 3.9508e-04, 3.9397e-04,\n", "         3.9286e-04, 3.9176e-04, 3.9066e-04, 3.8956e-04, 3.8847e-04, 3.8738e-04,\n", "         3.8629e-04, 3.8521e-04, 3.8413e-04, 3.8305e-04, 3.8197e-04, 3.8090e-04,\n", "         3.7983e-04, 3.7876e-04, 3.7770e-04, 3.7664e-04, 3.7558e-04, 3.7453e-04,\n", "         3.7348e-04, 3.7243e-04, 3.7138e-04, 3.7034e-04, 3.6930e-04, 3.6827e-04,\n", "         3.6723e-04, 3.6620e-04, 3.6517e-04, 3.6415e-04, 3.6313e-04, 3.6211e-04,\n", "         3.6109e-04, 3.6008e-04, 3.5907e-04, 3.5806e-04, 3.5705e-04, 3.5605e-04,\n", "         3.5505e-04, 3.5406e-04, 3.5306e-04, 3.5207e-04, 3.5108e-04, 3.5010e-04,\n", "         3.4912e-04, 3.4814e-04, 3.4716e-04, 3.4618e-04, 3.4521e-04, 3.4424e-04,\n", "         3.4328e-04, 3.4231e-04, 3.4135e-04, 3.4039e-04, 3.3944e-04, 3.3849e-04,\n", "         3.3754e-04, 3.3659e-04, 3.3564e-04, 3.3470e-04, 3.3376e-04, 3.3283e-04,\n", "         3.3189e-04, 3.3096e-04, 3.3003e-04, 3.2910e-04, 3.2818e-04, 3.2726e-04,\n", "         3.2634e-04, 3.2543e-04, 3.2451e-04, 3.2360e-04, 3.2269e-04, 3.2179e-04,\n", "         3.2088e-04, 3.1998e-04, 3.1908e-04, 3.1819e-04, 3.1730e-04, 3.1641e-04,\n", "         3.1552e-04, 3.1463e-04, 3.1375e-04, 3.1287e-04, 3.1199e-04, 3.1111e-04,\n", "         3.1024e-04, 3.0937e-04, 3.0850e-04, 3.0764e-04, 3.0677e-04, 3.0591e-04,\n", "         3.0505e-04, 3.0420e-04, 3.0334e-04, 3.0249e-04, 3.0164e-04, 3.0080e-04,\n", "         2.9995e-04, 2.9911e-04, 2.9827e-04, 2.9743e-04, 2.9660e-04, 2.9577e-04,\n", "         2.9494e-04, 2.9411e-04, 2.9328e-04, 2.9246e-04, 2.9164e-04, 2.9082e-04,\n", "         2.9000e-04, 2.8919e-04, 2.8838e-04, 2.8757e-04, 2.8676e-04, 2.8596e-04,\n", "         2.8515e-04, 2.8435e-04, 2.8355e-04, 2.8276e-04, 2.8197e-04, 2.8117e-04,\n", "         2.8038e-04, 2.7960e-04, 2.7881e-04, 2.7803e-04, 2.7725e-04, 2.7647e-04,\n", "         2.7570e-04, 2.7492e-04, 2.7415e-04, 2.7338e-04, 2.7261e-04, 2.7185e-04,\n", "         2.7109e-04, 2.7032e-04, 2.6957e-04, 2.6881e-04, 2.6805e-04, 2.6730e-04,\n", "         2.6655e-04, 2.6580e-04, 2.6506e-04, 2.6431e-04, 2.6357e-04, 2.6283e-04,\n", "         2.6209e-04, 2.6136e-04, 2.6062e-04, 2.5989e-04, 2.5916e-04, 2.5844e-04,\n", "         2.5771e-04, 2.5699e-04, 2.5627e-04, 2.5555e-04, 2.5483e-04, 2.5411e-04,\n", "         2.5340e-04, 2.5269e-04, 2.5198e-04, 2.5127e-04, 2.5057e-04, 2.4986e-04,\n", "         2.4916e-04, 2.4846e-04, 2.4777e-04, 2.4707e-04, 2.4638e-04, 2.4569e-04,\n", "         2.4500e-04, 2.4431e-04, 2.4362e-04, 2.4294e-04, 2.4226e-04, 2.4158e-04,\n", "         2.4090e-04, 2.4022e-04, 2.3955e-04, 2.3888e-04, 2.3821e-04, 2.3754e-04,\n", "         2.3687e-04, 2.3621e-04, 2.3554e-04, 2.3488e-04, 2.3422e-04, 2.3357e-04,\n", "         2.3291e-04, 2.3226e-04, 2.3160e-04, 2.3095e-04, 2.3031e-04, 2.2966e-04,\n", "         2.2901e-04, 2.2837e-04, 2.2773e-04, 2.2709e-04, 2.2645e-04, 2.2582e-04,\n", "         2.2518e-04, 2.2455e-04, 2.2392e-04, 2.2329e-04, 2.2267e-04, 2.2204e-04,\n", "         2.2142e-04, 2.2080e-04, 2.2018e-04, 2.1956e-04, 2.1894e-04, 2.1833e-04,\n", "         2.1772e-04, 2.1711e-04, 2.1650e-04, 2.1589e-04, 2.1528e-04, 2.1468e-04,\n", "         2.1408e-04, 2.1347e-04, 2.1288e-04, 2.1228e-04, 2.1168e-04, 2.1109e-04,\n", "         2.1050e-04, 2.0990e-04, 2.0932e-04, 2.0873e-04, 2.0814e-04, 2.0756e-04,\n", "         2.0698e-04, 2.0639e-04, 2.0581e-04, 2.0524e-04, 2.0466e-04, 2.0409e-04,\n", "         2.0351e-04, 2.0294e-04, 2.0237e-04, 2.0180e-04, 2.0124e-04, 2.0067e-04,\n", "         2.0011e-04, 1.9955e-04, 1.9899e-04, 1.9843e-04, 1.9787e-04, 1.9732e-04,\n", "         1.9676e-04, 1.9621e-04, 1.9566e-04, 1.9511e-04, 1.9456e-04, 1.9402e-04,\n", "         1.9347e-04, 1.9293e-04, 1.9239e-04, 1.9185e-04, 1.9131e-04, 1.9077e-04,\n", "         1.9024e-04, 1.8970e-04, 1.8917e-04, 1.8864e-04, 1.8811e-04, 1.8758e-04,\n", "         1.8706e-04, 1.8653e-04, 1.8601e-04, 1.8549e-04, 1.8497e-04, 1.8445e-04,\n", "         1.8393e-04, 1.8341e-04, 1.8290e-04, 1.8238e-04, 1.8187e-04, 1.8136e-04,\n", "         1.8085e-04, 1.8034e-04, 1.7984e-04, 1.7933e-04, 1.7883e-04, 1.7833e-04,\n", "         1.7783e-04, 1.7733e-04, 1.7683e-04, 1.7633e-04, 1.7584e-04, 1.7535e-04,\n", "         1.7485e-04, 1.7436e-04, 1.7387e-04, 1.7339e-04, 1.7290e-04, 1.7241e-04,\n", "         1.7193e-04, 1.7145e-04, 1.7097e-04, 1.7049e-04, 1.7001e-04, 1.6953e-04,\n", "         1.6905e-04, 1.6858e-04, 1.6811e-04, 1.6764e-04, 1.6716e-04, 1.6670e-04,\n", "         1.6623e-04, 1.6576e-04, 1.6530e-04, 1.6483e-04, 1.6437e-04, 1.6391e-04,\n", "         1.6345e-04, 1.6299e-04, 1.6253e-04, 1.6208e-04, 1.6162e-04, 1.6117e-04,\n", "         1.6071e-04, 1.6026e-04, 1.5981e-04, 1.5936e-04, 1.5892e-04, 1.5847e-04,\n", "         1.5803e-04, 1.5758e-04, 1.5714e-04, 1.5670e-04, 1.5626e-04, 1.5582e-04,\n", "         1.5538e-04, 1.5495e-04, 1.5451e-04, 1.5408e-04, 1.5365e-04, 1.5322e-04,\n", "         1.5279e-04, 1.5236e-04, 1.5193e-04, 1.5150e-04, 1.5108e-04, 1.5065e-04,\n", "         1.5023e-04, 1.4981e-04, 1.4939e-04, 1.4897e-04, 1.4855e-04, 1.4813e-04,\n", "         1.4772e-04, 1.4730e-04, 1.4689e-04, 1.4648e-04, 1.4607e-04, 1.4566e-04,\n", "         1.4525e-04, 1.4484e-04, 1.4443e-04, 1.4403e-04, 1.4362e-04, 1.4322e-04,\n", "         1.4282e-04, 1.4242e-04, 1.4202e-04, 1.4162e-04, 1.4122e-04, 1.4083e-04,\n", "         1.4043e-04, 1.4004e-04, 1.3964e-04, 1.3925e-04, 1.3886e-04, 1.3847e-04,\n", "         1.3808e-04, 1.3769e-04, 1.3731e-04, 1.3692e-04, 1.3654e-04, 1.3615e-04,\n", "         1.3577e-04, 1.3539e-04, 1.3501e-04, 1.3463e-04, 1.3425e-04, 1.3388e-04,\n", "         1.3350e-04, 1.3313e-04, 1.3275e-04, 1.3238e-04, 1.3201e-04, 1.3164e-04,\n", "         1.3127e-04, 1.3090e-04, 1.3053e-04, 1.3017e-04, 1.2980e-04, 1.2944e-04,\n", "         1.2907e-04, 1.2871e-04, 1.2835e-04, 1.2799e-04, 1.2763e-04, 1.2727e-04,\n", "         1.2692e-04, 1.2656e-04, 1.2620e-04, 1.2585e-04, 1.2550e-04, 1.2514e-04,\n", "         1.2479e-04, 1.2444e-04, 1.2409e-04, 1.2375e-04, 1.2340e-04, 1.2305e-04,\n", "         1.2271e-04, 1.2236e-04, 1.2202e-04, 1.2168e-04, 1.2133e-04, 1.2099e-04,\n", "         1.2065e-04, 1.2032e-04, 1.1998e-04, 1.1964e-04, 1.1931e-04, 1.1897e-04,\n", "         1.1864e-04, 1.1830e-04, 1.1797e-04, 1.1764e-04, 1.1731e-04, 1.1698e-04,\n", "         1.1665e-04, 1.1633e-04, 1.1600e-04, 1.1567e-04, 1.1535e-04, 1.1502e-04,\n", "         1.1470e-04, 1.1438e-04, 1.1406e-04, 1.1374e-04, 1.1342e-04, 1.1310e-04,\n", "         1.1278e-04, 1.1247e-04, 1.1215e-04, 1.1184e-04, 1.1152e-04, 1.1121e-04,\n", "         1.1090e-04, 1.1059e-04, 1.1028e-04, 1.0997e-04, 1.0966e-04, 1.0935e-04,\n", "         1.0904e-04, 1.0874e-04, 1.0843e-04, 1.0813e-04, 1.0782e-04, 1.0752e-04,\n", "         1.0722e-04, 1.0692e-04, 1.0662e-04, 1.0632e-04, 1.0602e-04, 1.0572e-04,\n", "         1.0543e-04, 1.0513e-04, 1.0484e-04, 1.0454e-04, 1.0425e-04, 1.0396e-04,\n", "         1.0366e-04, 1.0337e-04, 1.0308e-04, 1.0279e-04, 1.0250e-04, 1.0222e-04,\n", "         1.0193e-04, 1.0164e-04, 1.0136e-04, 1.0107e-04, 1.0079e-04, 1.0051e-04,\n", "         1.0023e-04, 9.9944e-05, 9.9663e-05, 9.9384e-05, 9.9105e-05, 9.8826e-05,\n", "         9.8549e-05, 9.8272e-05, 9.7997e-05, 9.7722e-05, 9.7447e-05, 9.7174e-05,\n", "         9.6901e-05, 9.6629e-05, 9.6358e-05, 9.6087e-05, 9.5818e-05, 9.5549e-05,\n", "         9.5280e-05, 9.5013e-05, 9.4746e-05, 9.4480e-05, 9.4215e-05, 9.3951e-05,\n", "         9.3687e-05, 9.3424e-05, 9.3162e-05, 9.2900e-05, 9.2640e-05, 9.2380e-05,\n", "         9.2120e-05, 9.1862e-05, 9.1604e-05, 9.1347e-05, 9.1090e-05, 9.0835e-05,\n", "         9.0580e-05, 9.0326e-05, 9.0072e-05, 8.9819e-05, 8.9567e-05, 8.9316e-05,\n", "         8.9065e-05, 8.8815e-05, 8.8566e-05, 8.8317e-05, 8.8069e-05, 8.7822e-05,\n", "         8.7576e-05, 8.7330e-05, 8.7085e-05, 8.6840e-05, 8.6596e-05, 8.6353e-05,\n", "         8.6111e-05, 8.5869e-05, 8.5628e-05, 8.5388e-05, 8.5148e-05, 8.4909e-05,\n", "         8.4671e-05, 8.4433e-05, 8.4196e-05, 8.3960e-05, 8.3724e-05, 8.3489e-05,\n", "         8.3255e-05, 8.3021e-05, 8.2788e-05, 8.2556e-05, 8.2324e-05, 8.2093e-05,\n", "         8.1863e-05, 8.1633e-05, 8.1404e-05, 8.1175e-05, 8.0947e-05, 8.0720e-05,\n", "         8.0494e-05, 8.0268e-05, 8.0042e-05, 7.9818e-05, 7.9594e-05, 7.9370e-05,\n", "         7.9148e-05, 7.8925e-05, 7.8704e-05, 7.8483e-05, 7.8263e-05, 7.8043e-05,\n", "         7.7824e-05, 7.7606e-05, 7.7388e-05, 7.7170e-05, 7.6954e-05, 7.6738e-05,\n", "         7.6522e-05, 7.6308e-05, 7.6094e-05, 7.5880e-05, 7.5667e-05, 7.5455e-05,\n", "         7.5243e-05, 7.5032e-05, 7.4821e-05, 7.4611e-05, 7.4402e-05, 7.4193e-05,\n", "         7.3984e-05, 7.3777e-05, 7.3570e-05, 7.3363e-05, 7.3157e-05, 7.2952e-05,\n", "         7.2747e-05, 7.2543e-05, 7.2339e-05, 7.2136e-05, 7.1934e-05, 7.1732e-05,\n", "         7.1531e-05, 7.1330e-05, 7.1130e-05, 7.0930e-05, 7.0731e-05, 7.0532e-05,\n", "         7.0334e-05, 7.0137e-05, 6.9940e-05, 6.9744e-05, 6.9548e-05, 6.9353e-05,\n", "         6.9158e-05, 6.8964e-05, 6.8771e-05, 6.8578e-05, 6.8385e-05, 6.8193e-05,\n", "         6.8002e-05, 6.7811e-05, 6.7620e-05, 6.7431e-05, 6.7241e-05, 6.7053e-05,\n", "         6.6864e-05, 6.6677e-05, 6.6490e-05, 6.6303e-05, 6.6117e-05, 6.5931e-05,\n", "         6.5746e-05, 6.5562e-05, 6.5378e-05, 6.5194e-05, 6.5011e-05, 6.4829e-05,\n", "         6.4647e-05, 6.4465e-05, 6.4284e-05, 6.4104e-05, 6.3924e-05, 6.3745e-05,\n", "         6.3566e-05, 6.3387e-05, 6.3209e-05, 6.3032e-05, 6.2855e-05, 6.2679e-05,\n", "         6.2503e-05, 6.2327e-05, 6.2152e-05, 6.1978e-05, 6.1804e-05, 6.1630e-05,\n", "         6.1457e-05, 6.1285e-05, 6.1113e-05, 6.0941e-05, 6.0770e-05, 6.0600e-05,\n", "         6.0430e-05, 6.0260e-05, 6.0091e-05, 5.9922e-05, 5.9754e-05, 5.9586e-05,\n", "         5.9419e-05, 5.9252e-05, 5.9086e-05, 5.8920e-05, 5.8755e-05, 5.8590e-05,\n", "         5.8425e-05, 5.8261e-05, 5.8098e-05, 5.7935e-05, 5.7772e-05, 5.7610e-05,\n", "         5.7448e-05, 5.7287e-05, 5.7126e-05, 5.6966e-05, 5.6806e-05, 5.6647e-05,\n", "         5.6488e-05, 5.6329e-05, 5.6171e-05, 5.6013e-05, 5.5856e-05, 5.5699e-05,\n", "         5.5543e-05, 5.5387e-05, 5.5232e-05, 5.5077e-05, 5.4922e-05, 5.4768e-05,\n", "         5.4614e-05, 5.4461e-05, 5.4308e-05, 5.4155e-05, 5.4003e-05, 5.3852e-05,\n", "         5.3701e-05, 5.3550e-05, 5.3400e-05, 5.3250e-05, 5.3100e-05, 5.2951e-05,\n", "         5.2803e-05, 5.2655e-05, 5.2507e-05, 5.2359e-05, 5.2212e-05, 5.2066e-05,\n", "         5.1920e-05, 5.1774e-05, 5.1629e-05, 5.1484e-05, 5.1339e-05, 5.1195e-05,\n", "         5.1051e-05, 5.0908e-05, 5.0765e-05, 5.0623e-05, 5.0481e-05, 5.0339e-05,\n", "         5.0198e-05, 5.0057e-05, 4.9916e-05, 4.9776e-05, 4.9636e-05, 4.9497e-05,\n", "         4.9358e-05, 4.9220e-05, 4.9082e-05, 4.8944e-05, 4.8806e-05, 4.8669e-05,\n", "         4.8533e-05, 4.8397e-05, 4.8261e-05, 4.8125e-05, 4.7990e-05, 4.7855e-05,\n", "         4.7721e-05, 4.7587e-05, 4.7454e-05, 4.7320e-05, 4.7188e-05, 4.7055e-05,\n", "         4.6923e-05, 4.6791e-05, 4.6660e-05, 4.6529e-05, 4.6398e-05, 4.6268e-05,\n", "         4.6138e-05, 4.6009e-05, 4.5880e-05, 4.5751e-05, 4.5623e-05, 4.5495e-05,\n", "         4.5367e-05, 4.5239e-05, 4.5112e-05, 4.4986e-05, 4.4860e-05, 4.4734e-05,\n", "         4.4608e-05, 4.4483e-05, 4.4358e-05, 4.4234e-05, 4.4109e-05, 4.3986e-05,\n", "         4.3862e-05, 4.3739e-05, 4.3616e-05, 4.3494e-05, 4.3372e-05, 4.3250e-05,\n", "         4.3129e-05, 4.3008e-05, 4.2887e-05, 4.2766e-05, 4.2646e-05, 4.2527e-05,\n", "         4.2407e-05, 4.2288e-05, 4.2170e-05, 4.2051e-05, 4.1933e-05, 4.1816e-05,\n", "         4.1698e-05, 4.1581e-05, 4.1464e-05, 4.1348e-05, 4.1232e-05, 4.1116e-05,\n", "         4.1001e-05, 4.0886e-05, 4.0771e-05, 4.0657e-05, 4.0542e-05, 4.0429e-05,\n", "         4.0315e-05, 4.0202e-05, 4.0089e-05, 3.9977e-05, 3.9864e-05, 3.9753e-05,\n", "         3.9641e-05, 3.9530e-05, 3.9419e-05, 3.9308e-05, 3.9198e-05, 3.9088e-05,\n", "         3.8978e-05, 3.8869e-05, 3.8760e-05, 3.8651e-05, 3.8542e-05, 3.8434e-05,\n", "         3.8326e-05, 3.8219e-05, 3.8111e-05, 3.8004e-05, 3.7898e-05, 3.7791e-05,\n", "         3.7685e-05, 3.7580e-05, 3.7474e-05, 3.7369e-05, 3.7264e-05, 3.7159e-05,\n", "         3.7055e-05, 3.6951e-05, 3.6847e-05, 3.6744e-05, 3.6641e-05, 3.6538e-05,\n", "         3.6435e-05, 3.6333e-05, 3.6231e-05, 3.6129e-05, 3.6028e-05, 3.5927e-05,\n", "         3.5826e-05, 3.5726e-05, 3.5625e-05, 3.5525e-05, 3.5426e-05, 3.5326e-05,\n", "         3.5227e-05, 3.5128e-05, 3.5029e-05, 3.4931e-05, 3.4833e-05, 3.4735e-05,\n", "         3.4638e-05, 3.4541e-05, 3.4444e-05, 3.4347e-05, 3.4251e-05, 3.4154e-05,\n", "         3.4059e-05, 3.3963e-05, 3.3868e-05, 3.3773e-05, 3.3678e-05, 3.3583e-05,\n", "         3.3489e-05, 3.3395e-05, 3.3301e-05, 3.3208e-05, 3.3115e-05, 3.3022e-05,\n", "         3.2929e-05, 3.2837e-05, 3.2744e-05, 3.2652e-05, 3.2561e-05, 3.2469e-05,\n", "         3.2378e-05, 3.2287e-05, 3.2197e-05, 3.2106e-05, 3.2016e-05, 3.1926e-05,\n", "         3.1837e-05, 3.1747e-05, 3.1658e-05, 3.1569e-05, 3.1481e-05, 3.1393e-05,\n", "         3.1304e-05, 3.1217e-05, 3.1129e-05, 3.1042e-05, 3.0954e-05, 3.0868e-05,\n", "         3.0781e-05, 3.0695e-05, 3.0608e-05, 3.0522e-05, 3.0437e-05, 3.0351e-05,\n", "         3.0266e-05, 3.0181e-05, 3.0096e-05, 3.0012e-05, 2.9928e-05, 2.9844e-05,\n", "         2.9760e-05, 2.9676e-05, 2.9593e-05, 2.9510e-05, 2.9427e-05, 2.9345e-05,\n", "         2.9262e-05, 2.9180e-05, 2.9098e-05, 2.9017e-05, 2.8935e-05, 2.8854e-05,\n", "         2.8773e-05, 2.8692e-05, 2.8612e-05, 2.8531e-05, 2.8451e-05, 2.8371e-05,\n", "         2.8292e-05, 2.8212e-05, 2.8133e-05, 2.8054e-05, 2.7975e-05, 2.7897e-05,\n", "         2.7819e-05, 2.7741e-05, 2.7663e-05, 2.7585e-05, 2.7508e-05, 2.7430e-05,\n", "         2.7353e-05, 2.7277e-05, 2.7200e-05, 2.7124e-05, 2.7048e-05, 2.6972e-05,\n", "         2.6896e-05, 2.6820e-05, 2.6745e-05, 2.6670e-05, 2.6595e-05, 2.6521e-05,\n", "         2.6446e-05, 2.6372e-05, 2.6298e-05, 2.6224e-05, 2.6151e-05, 2.6077e-05,\n", "         2.6004e-05, 2.5931e-05, 2.5858e-05, 2.5786e-05, 2.5713e-05, 2.5641e-05,\n", "         2.5569e-05, 2.5497e-05, 2.5426e-05, 2.5354e-05, 2.5283e-05, 2.5212e-05,\n", "         2.5141e-05, 2.5071e-05, 2.5001e-05, 2.4930e-05, 2.4860e-05, 2.4791e-05,\n", "         2.4721e-05, 2.4652e-05, 2.4582e-05, 2.4513e-05, 2.4445e-05, 2.4376e-05,\n", "         2.4308e-05, 2.4239e-05, 2.4171e-05, 2.4103e-05, 2.4036e-05, 2.3968e-05,\n", "         2.3901e-05, 2.3834e-05, 2.3767e-05, 2.3700e-05, 2.3634e-05, 2.3568e-05,\n", "         2.3501e-05, 2.3435e-05, 2.3370e-05, 2.3304e-05, 2.3239e-05, 2.3173e-05,\n", "         2.3108e-05, 2.3044e-05, 2.2979e-05, 2.2914e-05, 2.2850e-05, 2.2786e-05,\n", "         2.2722e-05, 2.2658e-05, 2.2595e-05, 2.2531e-05, 2.2468e-05, 2.2405e-05,\n", "         2.2342e-05, 2.2279e-05, 2.2217e-05, 2.2154e-05, 2.2092e-05, 2.2030e-05,\n", "         2.1968e-05, 2.1907e-05, 2.1845e-05, 2.1784e-05, 2.1723e-05, 2.1662e-05,\n", "         2.1601e-05, 2.1540e-05, 2.1480e-05, 2.1420e-05, 2.1359e-05, 2.1299e-05,\n", "         2.1240e-05, 2.1180e-05, 2.1121e-05, 2.1061e-05, 2.1002e-05, 2.0943e-05,\n", "         2.0885e-05, 2.0826e-05, 2.0767e-05, 2.0709e-05, 2.0651e-05, 2.0593e-05,\n", "         2.0535e-05, 2.0478e-05, 2.0420e-05, 2.0363e-05, 2.0306e-05, 2.0249e-05,\n", "         2.0192e-05, 2.0135e-05, 2.0079e-05, 2.0022e-05, 1.9966e-05, 1.9910e-05,\n", "         1.9854e-05, 1.9798e-05, 1.9743e-05, 1.9687e-05, 1.9632e-05, 1.9577e-05,\n", "         1.9522e-05, 1.9467e-05, 1.9413e-05, 1.9358e-05, 1.9304e-05, 1.9250e-05,\n", "         1.9196e-05, 1.9142e-05, 1.9088e-05, 1.9034e-05, 1.8981e-05, 1.8928e-05,\n", "         1.8875e-05, 1.8822e-05, 1.8769e-05, 1.8716e-05, 1.8664e-05, 1.8611e-05,\n", "         1.8559e-05, 1.8507e-05, 1.8455e-05, 1.8403e-05, 1.8352e-05, 1.8300e-05,\n", "         1.8249e-05, 1.8197e-05, 1.8146e-05, 1.8095e-05, 1.8045e-05, 1.7994e-05,\n", "         1.7943e-05, 1.7893e-05, 1.7843e-05, 1.7793e-05, 1.7743e-05, 1.7693e-05,\n", "         1.7643e-05, 1.7594e-05, 1.7544e-05, 1.7495e-05, 1.7446e-05, 1.7397e-05,\n", "         1.7348e-05, 1.7300e-05, 1.7251e-05, 1.7203e-05, 1.7154e-05, 1.7106e-05,\n", "         1.7058e-05, 1.7010e-05, 1.6963e-05, 1.6915e-05, 1.6868e-05, 1.6820e-05,\n", "         1.6773e-05, 1.6726e-05, 1.6679e-05, 1.6632e-05, 1.6585e-05, 1.6539e-05,\n", "         1.6492e-05, 1.6446e-05, 1.6400e-05, 1.6354e-05, 1.6308e-05, 1.6262e-05,\n", "         1.6217e-05, 1.6171e-05, 1.6126e-05, 1.6080e-05, 1.6035e-05, 1.5990e-05,\n", "         1.5945e-05, 1.5901e-05, 1.5856e-05, 1.5812e-05, 1.5767e-05, 1.5723e-05,\n", "         1.5679e-05, 1.5635e-05, 1.5591e-05, 1.5547e-05, 1.5503e-05, 1.5460e-05,\n", "         1.5417e-05, 1.5373e-05, 1.5330e-05, 1.5287e-05, 1.5244e-05, 1.5201e-05,\n", "         1.5159e-05, 1.5116e-05, 1.5074e-05, 1.5031e-05, 1.4989e-05, 1.4947e-05,\n", "         1.4905e-05, 1.4863e-05, 1.4822e-05, 1.4780e-05, 1.4739e-05, 1.4697e-05,\n", "         1.4656e-05, 1.4615e-05, 1.4574e-05, 1.4533e-05, 1.4492e-05, 1.4451e-05,\n", "         1.4411e-05, 1.4370e-05, 1.4330e-05, 1.4290e-05, 1.4250e-05, 1.4210e-05,\n", "         1.4170e-05, 1.4130e-05, 1.4090e-05, 1.4051e-05, 1.4011e-05, 1.3972e-05,\n", "         1.3933e-05, 1.3894e-05, 1.3855e-05, 1.3816e-05, 1.3777e-05, 1.3739e-05,\n", "         1.3700e-05, 1.3661e-05, 1.3623e-05, 1.3585e-05, 1.3547e-05, 1.3509e-05,\n", "         1.3471e-05, 1.3433e-05, 1.3395e-05, 1.3358e-05, 1.3320e-05, 1.3283e-05,\n", "         1.3246e-05, 1.3208e-05, 1.3171e-05, 1.3134e-05, 1.3097e-05, 1.3061e-05,\n", "         1.3024e-05, 1.2987e-05, 1.2951e-05, 1.2915e-05, 1.2878e-05, 1.2842e-05,\n", "         1.2806e-05, 1.2770e-05, 1.2734e-05, 1.2699e-05, 1.2663e-05, 1.2628e-05,\n", "         1.2592e-05, 1.2557e-05, 1.2521e-05, 1.2486e-05, 1.2451e-05, 1.2416e-05,\n", "         1.2382e-05, 1.2347e-05, 1.2312e-05, 1.2278e-05, 1.2243e-05, 1.2209e-05,\n", "         1.2174e-05, 1.2140e-05, 1.2106e-05, 1.2072e-05, 1.2038e-05, 1.2005e-05,\n", "         1.1971e-05, 1.1937e-05, 1.1904e-05, 1.1870e-05, 1.1837e-05, 1.1804e-05,\n", "         1.1771e-05, 1.1738e-05, 1.1705e-05, 1.1672e-05, 1.1639e-05, 1.1606e-05,\n", "         1.1574e-05, 1.1541e-05, 1.1509e-05, 1.1477e-05, 1.1444e-05, 1.1412e-05,\n", "         1.1380e-05, 1.1348e-05, 1.1316e-05, 1.1285e-05, 1.1253e-05, 1.1221e-05,\n", "         1.1190e-05, 1.1159e-05, 1.1127e-05, 1.1096e-05, 1.1065e-05, 1.1034e-05,\n", "         1.1003e-05, 1.0972e-05, 1.0941e-05, 1.0910e-05, 1.0880e-05, 1.0849e-05,\n", "         1.0819e-05, 1.0788e-05, 1.0758e-05, 1.0728e-05, 1.0698e-05, 1.0668e-05,\n", "         1.0638e-05, 1.0608e-05, 1.0578e-05, 1.0549e-05, 1.0519e-05, 1.0489e-05,\n", "         1.0460e-05, 1.0431e-05, 1.0401e-05, 1.0372e-05, 1.0343e-05, 1.0314e-05,\n", "         1.0285e-05, 1.0256e-05, 1.0227e-05, 1.0199e-05, 1.0170e-05, 1.0142e-05,\n", "         1.0113e-05, 1.0085e-05, 1.0056e-05, 1.0028e-05], device='cuda:0'),\n", " tensor([0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "         0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.],\n", "        device='cuda:0'))"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["(\n", "    outs[\"prescale_freqs\"],\n", "    yarn_outs[\"prescale_freqs\"],\n", "    outs[\"prescale_freqs\"] - yarn_outs[\"prescale_freqs\"],\n", ")"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1.2500e-01, 1.2465e-01, 1.2430e-01, 1.2395e-01, 1.2360e-01, 1.2326e-01,\n", "        1.2291e-01, 1.2256e-01, 1.2222e-01, 1.2188e-01, 1.2154e-01, 1.2119e-01,\n", "        1.2085e-01, 1.2051e-01, 1.2018e-01, 1.1984e-01, 1.1950e-01, 1.1917e-01,\n", "        1.1883e-01, 1.1850e-01, 1.1817e-01, 1.1784e-01, 1.1750e-01, 1.1717e-01,\n", "        1.1685e-01, 1.1652e-01, 1.1619e-01, 1.1586e-01, 1.1554e-01, 1.1522e-01,\n", "        1.1489e-01, 1.1457e-01, 1.1425e-01, 1.1393e-01, 1.1361e-01, 1.1329e-01,\n", "        1.1297e-01, 1.1265e-01, 1.1234e-01, 1.1202e-01, 1.1171e-01, 1.1139e-01,\n", "        1.1108e-01, 1.1077e-01, 1.1046e-01, 1.1015e-01, 1.0984e-01, 1.0953e-01,\n", "        1.0922e-01, 1.0892e-01, 1.0861e-01, 1.0831e-01, 1.0800e-01, 1.0770e-01,\n", "        1.0740e-01, 1.0710e-01, 1.0679e-01, 1.0650e-01, 1.0620e-01, 1.0590e-01,\n", "        1.0560e-01, 1.0530e-01, 1.0501e-01, 1.0471e-01, 1.0442e-01, 1.0413e-01,\n", "        1.0383e-01, 1.0354e-01, 1.0325e-01, 1.0296e-01, 1.0267e-01, 1.0239e-01,\n", "        1.0210e-01, 1.0181e-01, 1.0153e-01, 1.0124e-01, 1.0096e-01, 1.0067e-01,\n", "        1.0039e-01, 1.0011e-01, 9.9828e-02, 9.9548e-02, 9.9269e-02, 9.8990e-02,\n", "        9.8712e-02, 9.8435e-02, 9.8159e-02, 9.7883e-02, 9.7609e-02, 9.7335e-02,\n", "        9.7061e-02, 9.6789e-02, 9.6517e-02, 9.6246e-02, 9.5976e-02, 9.5707e-02,\n", "        9.5438e-02, 9.5170e-02, 9.4903e-02, 9.4637e-02, 9.4371e-02, 9.4106e-02,\n", "        9.3842e-02, 9.3579e-02, 9.3316e-02, 9.3054e-02, 9.2793e-02, 9.2533e-02,\n", "        9.2273e-02, 9.2014e-02, 9.1756e-02, 9.1498e-02, 9.1241e-02, 9.0985e-02,\n", "        9.0730e-02, 9.0475e-02, 9.0221e-02, 8.9968e-02, 8.9715e-02, 8.9464e-02,\n", "        8.9212e-02, 8.8962e-02, 8.8712e-02, 8.8463e-02, 8.8215e-02, 8.7967e-02,\n", "        8.7721e-02, 8.7474e-02, 8.7229e-02, 8.6984e-02, 8.6740e-02, 8.6496e-02,\n", "        8.6254e-02, 8.6011e-02, 8.5770e-02, 8.5529e-02, 8.5289e-02, 8.5050e-02,\n", "        8.4811e-02, 8.4573e-02, 8.4336e-02, 8.4099e-02, 8.3863e-02, 8.3628e-02,\n", "        8.3393e-02, 8.3159e-02, 8.2925e-02, 8.2693e-02, 8.2460e-02, 8.2229e-02,\n", "        8.1998e-02, 8.1768e-02, 8.1539e-02, 8.1310e-02, 8.1081e-02, 8.0854e-02,\n", "        8.0627e-02, 8.0401e-02, 8.0175e-02, 7.9950e-02, 7.9726e-02, 7.9502e-02,\n", "        7.9279e-02, 7.9056e-02, 7.8834e-02, 7.8613e-02, 7.8392e-02, 7.8172e-02,\n", "        7.7953e-02, 7.7734e-02, 7.7516e-02, 7.7298e-02, 7.7081e-02, 7.6865e-02,\n", "        7.6649e-02, 7.6434e-02, 7.6220e-02, 7.6006e-02, 7.5792e-02, 7.5580e-02,\n", "        7.5367e-02, 7.5156e-02, 7.4945e-02, 7.4735e-02, 7.4525e-02, 7.4316e-02,\n", "        7.4107e-02, 7.3899e-02, 7.3692e-02, 7.3485e-02, 7.3278e-02, 7.3073e-02,\n", "        7.2868e-02, 7.2663e-02, 7.2459e-02, 7.2256e-02, 7.2053e-02, 7.1851e-02,\n", "        7.1649e-02, 7.1448e-02, 7.1247e-02, 7.1047e-02, 7.0848e-02, 7.0649e-02,\n", "        7.0451e-02, 7.0253e-02, 7.0056e-02, 6.9859e-02, 6.9663e-02, 6.9468e-02,\n", "        6.9273e-02, 6.9078e-02, 6.8884e-02, 6.8691e-02, 6.8498e-02, 6.8306e-02,\n", "        6.8114e-02, 6.7923e-02, 6.7732e-02, 6.7542e-02, 6.7353e-02, 6.7164e-02,\n", "        6.6975e-02, 6.6787e-02, 6.6600e-02, 6.6413e-02, 6.6226e-02, 6.6041e-02,\n", "        6.5855e-02, 6.5670e-02, 6.5486e-02, 6.5302e-02, 6.5119e-02, 6.4936e-02,\n", "        6.4754e-02, 6.4572e-02, 6.4391e-02, 6.4210e-02, 6.4030e-02, 6.3850e-02,\n", "        6.3671e-02, 6.3492e-02, 6.3314e-02, 6.3136e-02, 6.2959e-02, 6.2782e-02,\n", "        6.2606e-02, 6.2430e-02, 6.2255e-02, 6.2080e-02, 6.1906e-02, 6.1732e-02,\n", "        6.1559e-02, 6.1386e-02, 6.1214e-02, 6.1042e-02, 6.0871e-02, 6.0700e-02,\n", "        6.0530e-02, 6.0360e-02, 6.0190e-02, 6.0021e-02, 5.9853e-02, 5.9685e-02,\n", "        5.9517e-02, 5.9350e-02, 5.9184e-02, 5.9018e-02, 5.8852e-02, 5.8687e-02,\n", "        5.8522e-02, 5.8358e-02, 5.8194e-02, 5.8031e-02, 5.7868e-02, 5.7705e-02,\n", "        5.7543e-02, 5.7382e-02, 5.7221e-02, 5.7060e-02, 5.6900e-02, 5.6740e-02,\n", "        5.6581e-02, 5.6422e-02, 5.6264e-02, 5.6106e-02, 5.5949e-02, 5.5792e-02,\n", "        5.5635e-02, 5.5479e-02, 5.5323e-02, 5.5168e-02, 5.5013e-02, 5.4859e-02,\n", "        5.4705e-02, 5.4551e-02, 5.4398e-02, 5.4245e-02, 5.4093e-02, 5.3941e-02,\n", "        5.3790e-02, 5.3639e-02, 5.3488e-02, 5.3338e-02, 5.3188e-02, 5.3039e-02,\n", "        5.2890e-02, 5.2742e-02, 5.2594e-02, 5.2446e-02, 5.2299e-02, 5.2152e-02,\n", "        5.2006e-02, 5.1860e-02, 5.1714e-02, 5.1569e-02, 5.1424e-02, 5.1280e-02,\n", "        5.1136e-02, 5.0992e-02, 5.0849e-02, 5.0707e-02, 5.0564e-02, 5.0422e-02,\n", "        5.0281e-02, 5.0140e-02, 4.9999e-02, 4.9859e-02, 4.9719e-02, 4.9579e-02,\n", "        4.9440e-02, 4.9301e-02, 4.9163e-02, 4.9025e-02, 4.8887e-02, 4.8750e-02,\n", "        4.8613e-02, 4.8477e-02, 4.8341e-02, 4.8205e-02, 4.8070e-02, 4.7935e-02,\n", "        4.7800e-02, 4.7666e-02, 4.7532e-02, 4.7399e-02, 4.7266e-02, 4.7133e-02,\n", "        4.7001e-02, 4.6869e-02, 4.6737e-02, 4.6606e-02, 4.6475e-02, 4.6345e-02,\n", "        4.6215e-02, 4.6085e-02, 4.5956e-02, 4.5827e-02, 4.5698e-02, 4.5570e-02,\n", "        4.5442e-02, 4.5314e-02, 4.5187e-02, 4.5060e-02, 4.4934e-02, 4.4808e-02,\n", "        4.4682e-02, 4.4557e-02, 4.4432e-02, 4.4307e-02, 4.4182e-02, 4.4058e-02,\n", "        4.3935e-02, 4.3811e-02, 4.3688e-02, 4.3566e-02, 4.3444e-02, 4.3322e-02,\n", "        4.3200e-02, 4.3079e-02, 4.2958e-02, 4.2837e-02, 4.2717e-02, 4.2597e-02,\n", "        4.2478e-02, 4.2358e-02, 4.2239e-02, 4.2121e-02, 4.2003e-02, 4.1885e-02,\n", "        4.1767e-02, 4.1650e-02, 4.1533e-02, 4.1417e-02, 4.1300e-02, 4.1184e-02,\n", "        4.1069e-02, 4.0953e-02, 4.0839e-02, 4.0724e-02, 4.0610e-02, 4.0496e-02,\n", "        4.0382e-02, 4.0269e-02, 4.0156e-02, 4.0043e-02, 3.9930e-02, 3.9818e-02,\n", "        3.9707e-02, 3.9595e-02, 3.9484e-02, 3.9373e-02, 3.9263e-02, 3.9153e-02,\n", "        3.9043e-02, 3.8933e-02, 3.8824e-02, 3.8715e-02, 3.8606e-02, 3.8498e-02,\n", "        3.8390e-02, 3.8282e-02, 3.8174e-02, 3.8067e-02, 3.7960e-02, 3.7854e-02,\n", "        3.7748e-02, 3.7642e-02, 3.7536e-02, 3.7431e-02, 3.7326e-02, 3.7221e-02,\n", "        3.7116e-02, 3.7012e-02, 3.6908e-02, 3.6805e-02, 3.6701e-02, 3.6598e-02,\n", "        3.6496e-02, 3.6393e-02, 3.6291e-02, 3.6189e-02, 3.6088e-02, 3.5986e-02,\n", "        3.5885e-02, 3.5785e-02, 3.5684e-02, 3.5584e-02, 3.5484e-02, 3.5385e-02,\n", "        3.5285e-02, 3.5186e-02, 3.5087e-02, 3.4989e-02, 3.4891e-02, 3.4793e-02,\n", "        3.4695e-02, 3.4598e-02, 3.4501e-02, 3.4404e-02, 3.4307e-02, 3.4211e-02,\n", "        3.4115e-02, 3.4019e-02, 3.3924e-02, 3.3829e-02, 3.3734e-02, 3.3639e-02,\n", "        3.3544e-02, 3.3450e-02, 3.3356e-02, 3.3263e-02, 3.3169e-02, 3.3076e-02,\n", "        3.2984e-02, 3.2891e-02, 3.2799e-02, 3.2707e-02, 3.2615e-02, 3.2523e-02,\n", "        3.2432e-02, 3.2341e-02, 3.2250e-02, 3.2160e-02, 3.2069e-02, 3.1979e-02,\n", "        3.1890e-02, 3.1800e-02, 3.1711e-02, 3.1622e-02, 3.1533e-02, 3.1445e-02,\n", "        3.1356e-02, 3.1268e-02, 3.1180e-02, 3.1093e-02, 3.1006e-02, 3.0919e-02,\n", "        3.0832e-02, 3.0745e-02, 3.0659e-02, 3.0573e-02, 3.0487e-02, 3.0402e-02,\n", "        3.0316e-02, 3.0231e-02, 3.0146e-02, 3.0062e-02, 2.9977e-02, 2.9893e-02,\n", "        2.9809e-02, 2.9726e-02, 2.9642e-02, 2.9559e-02, 2.9476e-02, 2.9393e-02,\n", "        2.9311e-02, 2.9228e-02, 2.9146e-02, 2.9065e-02, 2.8983e-02, 2.8902e-02,\n", "        2.8821e-02, 2.8740e-02, 2.8659e-02, 2.8579e-02, 2.8498e-02, 2.8418e-02,\n", "        2.8339e-02, 2.8259e-02, 2.8180e-02, 2.8101e-02, 2.8022e-02, 2.7943e-02,\n", "        2.7865e-02, 2.7786e-02, 2.7709e-02, 2.7631e-02, 2.7553e-02, 2.7476e-02,\n", "        2.7399e-02, 2.7322e-02, 2.7245e-02, 2.7169e-02, 2.7092e-02, 2.7016e-02,\n", "        2.6941e-02, 2.6865e-02, 2.6790e-02, 2.6714e-02, 2.6639e-02, 2.6565e-02,\n", "        2.6490e-02, 2.6416e-02, 2.6341e-02, 2.6268e-02, 2.6194e-02, 2.6120e-02,\n", "        2.6047e-02, 2.5974e-02, 2.5901e-02, 2.5828e-02, 2.5756e-02, 2.5683e-02,\n", "        2.5611e-02, 2.5540e-02, 2.5468e-02, 2.5396e-02, 2.5325e-02, 2.5254e-02,\n", "        2.5183e-02, 2.5112e-02, 2.5042e-02, 2.4972e-02, 2.4902e-02, 2.4832e-02,\n", "        2.4762e-02, 2.4692e-02, 2.4623e-02, 2.4554e-02, 2.4485e-02, 2.4416e-02,\n", "        2.4348e-02, 2.4280e-02, 2.4211e-02, 2.4143e-02, 2.4076e-02, 2.4008e-02,\n", "        2.3941e-02, 2.3873e-02, 2.3806e-02, 2.3740e-02, 2.3673e-02, 2.3607e-02,\n", "        2.3540e-02, 2.3474e-02, 2.3408e-02, 2.3343e-02, 2.3277e-02, 2.3212e-02,\n", "        2.3147e-02, 2.3082e-02, 2.3017e-02, 2.2952e-02, 2.2888e-02, 2.2824e-02,\n", "        2.2760e-02, 2.2696e-02, 2.2632e-02, 2.2568e-02, 2.2505e-02, 2.2442e-02,\n", "        2.2379e-02, 2.2316e-02, 2.2254e-02, 2.2191e-02, 2.2129e-02, 2.2067e-02,\n", "        2.2005e-02, 2.1943e-02, 2.1881e-02, 2.1820e-02, 2.1759e-02, 2.1698e-02,\n", "        2.1637e-02, 2.1576e-02, 2.1515e-02, 2.1455e-02, 2.1395e-02, 2.1335e-02,\n", "        2.1275e-02, 2.1215e-02, 2.1156e-02, 2.1096e-02, 2.1037e-02, 2.0978e-02,\n", "        2.0919e-02, 2.0860e-02, 2.0802e-02, 2.0743e-02, 2.0685e-02, 2.0627e-02,\n", "        2.0569e-02, 2.0512e-02, 2.0454e-02, 2.0397e-02, 2.0339e-02, 2.0282e-02,\n", "        2.0225e-02, 2.0169e-02, 2.0112e-02, 2.0055e-02, 1.9999e-02, 1.9943e-02,\n", "        1.9887e-02, 1.9831e-02, 1.9776e-02, 1.9720e-02, 1.9665e-02, 1.9610e-02,\n", "        1.9554e-02, 1.9500e-02, 1.9445e-02, 1.9390e-02, 1.9336e-02, 1.9282e-02,\n", "        1.9227e-02, 1.9173e-02, 1.9120e-02, 1.9066e-02, 1.9012e-02, 1.8959e-02,\n", "        1.8906e-02, 1.8853e-02, 1.8800e-02, 1.8747e-02, 1.8695e-02, 1.8642e-02,\n", "        1.8590e-02, 1.8538e-02, 1.8486e-02, 1.8434e-02, 1.8382e-02, 1.8330e-02,\n", "        1.8279e-02, 1.8228e-02, 1.8176e-02, 1.8125e-02, 1.8074e-02, 1.8024e-02,\n", "        1.7973e-02, 1.7923e-02, 1.7872e-02, 1.7822e-02, 1.7772e-02, 1.7722e-02,\n", "        1.7673e-02, 1.7623e-02, 1.7574e-02, 1.7524e-02, 1.7475e-02, 1.7426e-02,\n", "        1.7377e-02, 1.7328e-02, 1.7280e-02, 1.7231e-02, 1.7183e-02, 1.7135e-02,\n", "        1.7086e-02, 1.7039e-02, 1.6991e-02, 1.6943e-02, 1.6895e-02, 1.6848e-02,\n", "        1.6801e-02, 1.6754e-02, 1.6707e-02, 1.6660e-02, 1.6613e-02, 1.6566e-02,\n", "        1.6520e-02, 1.6473e-02, 1.6427e-02, 1.6381e-02, 1.6335e-02, 1.6289e-02,\n", "        1.6243e-02, 1.6198e-02, 1.6152e-02, 1.6107e-02, 1.6062e-02, 1.6017e-02,\n", "        1.5972e-02, 1.5927e-02, 1.5882e-02, 1.5838e-02, 1.5793e-02, 1.5749e-02,\n", "        1.5705e-02, 1.5661e-02, 1.5617e-02, 1.5573e-02, 1.5529e-02, 1.5486e-02,\n", "        1.5442e-02, 1.5399e-02, 1.5356e-02, 1.5312e-02, 1.5269e-02, 1.5227e-02,\n", "        1.5184e-02, 1.5141e-02, 1.5099e-02, 1.5056e-02, 1.5014e-02, 1.4972e-02,\n", "        1.4930e-02, 1.4888e-02, 1.4846e-02, 1.4805e-02, 1.4763e-02, 1.4722e-02,\n", "        1.4680e-02, 1.4639e-02, 1.4598e-02, 1.4557e-02, 1.4516e-02, 1.4475e-02,\n", "        1.4435e-02, 1.4394e-02, 1.4354e-02, 1.4314e-02, 1.4273e-02, 1.4233e-02,\n", "        1.4193e-02, 1.4154e-02, 1.4114e-02, 1.4074e-02, 1.4035e-02, 1.3995e-02,\n", "        1.3956e-02, 1.3917e-02, 1.3878e-02, 1.3839e-02, 1.3800e-02, 1.3761e-02,\n", "        1.3723e-02, 1.3684e-02, 1.3646e-02, 1.3607e-02, 1.3569e-02, 1.3531e-02,\n", "        1.3493e-02, 1.3455e-02, 1.3418e-02, 1.3380e-02, 1.3342e-02, 1.3305e-02,\n", "        1.3267e-02, 1.3230e-02, 1.3193e-02, 1.3156e-02, 1.3119e-02, 1.3082e-02,\n", "        1.3046e-02, 1.3009e-02, 1.2972e-02, 1.2936e-02, 1.2900e-02, 1.2864e-02,\n", "        1.2827e-02, 1.2791e-02, 1.2756e-02, 1.2720e-02, 1.2684e-02, 1.2648e-02,\n", "        1.2613e-02, 1.2578e-02, 1.2542e-02, 1.2507e-02, 1.2472e-02, 1.2437e-02,\n", "        1.2402e-02, 1.2367e-02, 1.2332e-02, 1.2298e-02, 1.2263e-02, 1.2229e-02,\n", "        1.2195e-02, 1.2160e-02, 1.2126e-02, 1.2092e-02, 1.2058e-02, 1.2024e-02,\n", "        1.1991e-02, 1.1957e-02, 1.1923e-02, 1.1890e-02, 1.1857e-02, 1.1823e-02,\n", "        1.1790e-02, 1.1757e-02, 1.1724e-02, 1.1691e-02, 1.1658e-02, 1.1626e-02,\n", "        1.1593e-02, 1.1560e-02, 1.1528e-02, 1.1496e-02, 1.1463e-02, 1.1431e-02,\n", "        1.1399e-02, 1.1367e-02, 1.1335e-02, 1.1303e-02, 1.1272e-02, 1.1240e-02,\n", "        1.1208e-02, 1.1177e-02, 1.1146e-02, 1.1114e-02, 1.1083e-02, 1.1052e-02,\n", "        1.1021e-02, 1.0990e-02, 1.0959e-02, 1.0928e-02, 1.0898e-02, 1.0867e-02,\n", "        1.0837e-02, 1.0806e-02, 1.0776e-02, 1.0746e-02, 1.0716e-02, 1.0685e-02,\n", "        1.0656e-02, 1.0626e-02, 1.0596e-02, 1.0566e-02, 1.0536e-02, 1.0507e-02,\n", "        1.0477e-02, 1.0448e-02, 1.0419e-02, 1.0389e-02, 1.0360e-02, 1.0331e-02,\n", "        1.0302e-02, 1.0273e-02, 1.0244e-02, 1.0216e-02, 1.0187e-02, 1.0158e-02,\n", "        1.0130e-02, 1.0101e-02, 1.0073e-02, 1.0045e-02, 1.0017e-02, 9.9884e-03,\n", "        9.9604e-03, 9.9325e-03, 9.9046e-03, 9.8768e-03, 9.8491e-03, 9.8214e-03,\n", "        9.7938e-03, 9.7663e-03, 9.7389e-03, 9.7116e-03, 9.6843e-03, 9.6572e-03,\n", "        9.6301e-03, 9.6030e-03, 9.5761e-03, 9.5492e-03, 9.5224e-03, 9.4957e-03,\n", "        9.4690e-03, 9.4424e-03, 9.4159e-03, 9.3895e-03, 9.3631e-03, 9.3369e-03,\n", "        9.3107e-03, 9.2845e-03, 9.2585e-03, 9.2325e-03, 9.2066e-03, 9.1807e-03,\n", "        9.1550e-03, 9.1293e-03, 9.1036e-03, 9.0781e-03, 9.0526e-03, 9.0272e-03,\n", "        9.0019e-03, 8.9766e-03, 8.9514e-03, 8.9263e-03, 8.9012e-03, 8.8762e-03,\n", "        8.8513e-03, 8.8265e-03, 8.8017e-03, 8.7770e-03, 8.7524e-03, 8.7278e-03,\n", "        8.7033e-03, 8.6789e-03, 8.6545e-03, 8.6302e-03, 8.6060e-03, 8.5818e-03,\n", "        8.5577e-03, 8.5337e-03, 8.5098e-03, 8.4859e-03, 8.4621e-03, 8.4383e-03,\n", "        8.4146e-03, 8.3910e-03, 8.3675e-03, 8.3440e-03, 8.3206e-03, 8.2972e-03,\n", "        8.2739e-03, 8.2507e-03, 8.2275e-03, 8.2044e-03, 8.1814e-03, 8.1584e-03,\n", "        8.1355e-03, 8.1127e-03, 8.0899e-03, 8.0672e-03, 8.0446e-03, 8.0220e-03,\n", "        7.9995e-03, 7.9770e-03, 7.9546e-03, 7.9323e-03, 7.9101e-03, 7.8879e-03,\n", "        7.8657e-03, 7.8436e-03, 7.8216e-03, 7.7997e-03, 7.7778e-03, 7.7559e-03,\n", "        7.7342e-03, 7.7125e-03, 7.6908e-03, 7.6692e-03, 7.6477e-03, 7.6262e-03,\n", "        7.6048e-03, 7.5835e-03, 7.5622e-03, 7.5410e-03, 7.5198e-03, 7.4987e-03,\n", "        7.4777e-03, 7.4567e-03, 7.4357e-03, 7.4149e-03, 7.3941e-03, 7.3733e-03,\n", "        7.3526e-03, 7.3320e-03, 7.3114e-03, 7.2909e-03, 7.2704e-03, 7.2500e-03,\n", "        7.2296e-03, 7.2094e-03, 7.1891e-03, 7.1689e-03, 7.1488e-03, 7.1288e-03,\n", "        7.1087e-03, 7.0888e-03, 7.0689e-03, 7.0491e-03, 7.0293e-03, 7.0095e-03,\n", "        6.9899e-03, 6.9702e-03, 6.9507e-03, 6.9312e-03, 6.9117e-03, 6.8923e-03,\n", "        6.8730e-03, 6.8537e-03, 6.8344e-03, 6.8153e-03, 6.7961e-03, 6.7771e-03,\n", "        6.7580e-03, 6.7391e-03, 6.7201e-03, 6.7013e-03, 6.6825e-03, 6.6637e-03,\n", "        6.6450e-03, 6.6264e-03, 6.6078e-03, 6.5892e-03, 6.5707e-03, 6.5523e-03,\n", "        6.5339e-03, 6.5155e-03, 6.4973e-03, 6.4790e-03, 6.4608e-03, 6.4427e-03,\n", "        6.4246e-03, 6.4066e-03, 6.3886e-03, 6.3707e-03, 6.3528e-03, 6.3350e-03,\n", "        6.3172e-03, 6.2994e-03, 6.2818e-03, 6.2641e-03, 6.2466e-03, 6.2290e-03,\n", "        6.2115e-03, 6.1941e-03, 6.1767e-03, 6.1594e-03, 6.1421e-03, 6.1249e-03,\n", "        6.1077e-03, 6.0905e-03, 6.0734e-03, 6.0564e-03, 6.0394e-03, 6.0224e-03,\n", "        6.0055e-03, 5.9887e-03, 5.9719e-03, 5.9551e-03, 5.9384e-03, 5.9217e-03,\n", "        5.9051e-03, 5.8885e-03, 5.8720e-03, 5.8555e-03, 5.8391e-03, 5.8227e-03,\n", "        5.8063e-03, 5.7900e-03, 5.7738e-03, 5.7576e-03, 5.7414e-03, 5.7253e-03,\n", "        5.7092e-03, 5.6932e-03, 5.6772e-03, 5.6613e-03, 5.6454e-03, 5.6296e-03,\n", "        5.6138e-03, 5.5980e-03, 5.5823e-03, 5.5666e-03, 5.5510e-03, 5.5354e-03,\n", "        5.5199e-03, 5.5044e-03, 5.4889e-03, 5.4735e-03, 5.4582e-03, 5.4428e-03,\n", "        5.4276e-03, 5.4123e-03, 5.3971e-03, 5.3820e-03, 5.3669e-03, 5.3518e-03,\n", "        5.3368e-03, 5.3218e-03, 5.3069e-03, 5.2920e-03, 5.2771e-03, 5.2623e-03,\n", "        5.2476e-03, 5.2328e-03, 5.2181e-03, 5.2035e-03, 5.1889e-03, 5.1743e-03,\n", "        5.1598e-03, 5.1453e-03, 5.1309e-03, 5.1165e-03, 5.1021e-03, 5.0878e-03,\n", "        5.0735e-03, 5.0593e-03, 5.0451e-03, 5.0309e-03, 5.0168e-03, 5.0027e-03,\n", "        4.9887e-03, 4.9747e-03, 4.9607e-03, 4.9468e-03, 4.9329e-03, 4.9190e-03,\n", "        4.9052e-03, 4.8915e-03, 4.8777e-03, 4.8640e-03, 4.8504e-03, 4.8368e-03,\n", "        4.8232e-03, 4.8097e-03, 4.7962e-03, 4.7827e-03, 4.7693e-03, 4.7559e-03,\n", "        4.7425e-03, 4.7292e-03, 4.7160e-03, 4.7027e-03, 4.6895e-03, 4.6764e-03,\n", "        4.6632e-03, 4.6501e-03, 4.6371e-03, 4.6241e-03, 4.6111e-03, 4.5982e-03,\n", "        4.5853e-03, 4.5724e-03, 4.5595e-03, 4.5467e-03, 4.5340e-03, 4.5213e-03,\n", "        4.5086e-03, 4.4959e-03, 4.4833e-03, 4.4707e-03, 4.4582e-03, 4.4457e-03,\n", "        4.4332e-03, 4.4207e-03, 4.4083e-03, 4.3959e-03, 4.3836e-03, 4.3713e-03,\n", "        4.3590e-03, 4.3468e-03, 4.3346e-03, 4.3224e-03, 4.3103e-03, 4.2982e-03,\n", "        4.2861e-03, 4.2741e-03, 4.2621e-03, 4.2501e-03, 4.2382e-03, 4.2263e-03,\n", "        4.2145e-03, 4.2026e-03, 4.1908e-03, 4.1791e-03, 4.1673e-03, 4.1556e-03,\n", "        4.1440e-03, 4.1324e-03, 4.1208e-03, 4.1092e-03, 4.0977e-03, 4.0861e-03,\n", "        4.0747e-03, 4.0632e-03, 4.0518e-03, 4.0405e-03, 4.0291e-03, 4.0178e-03,\n", "        4.0065e-03, 3.9953e-03, 3.9841e-03, 3.9729e-03, 3.9617e-03, 3.9506e-03,\n", "        3.9395e-03, 3.9285e-03, 3.9175e-03, 3.9065e-03, 3.8955e-03, 3.8846e-03,\n", "        3.8737e-03, 3.8628e-03, 3.8519e-03, 3.8411e-03, 3.8303e-03, 3.8196e-03,\n", "        3.8089e-03, 3.7982e-03, 3.7875e-03, 3.7769e-03, 3.7663e-03, 3.7557e-03,\n", "        3.7452e-03, 3.7347e-03, 3.7242e-03, 3.7137e-03, 3.7033e-03, 3.6929e-03,\n", "        3.6825e-03, 3.6722e-03, 3.6619e-03, 3.6516e-03, 3.6414e-03, 3.6312e-03,\n", "        3.6210e-03, 3.6108e-03, 3.6007e-03, 3.5906e-03, 3.5805e-03, 3.5704e-03,\n", "        3.5604e-03, 3.5504e-03, 3.5404e-03, 3.5305e-03, 3.5206e-03, 3.5107e-03,\n", "        3.5009e-03, 3.4910e-03, 3.4812e-03, 3.4715e-03, 3.4617e-03, 3.4520e-03,\n", "        3.4423e-03, 3.4327e-03, 3.4230e-03, 3.4134e-03, 3.4038e-03, 3.3943e-03,\n", "        3.3848e-03, 3.3753e-03, 3.3658e-03, 3.3563e-03, 3.3469e-03, 3.3375e-03,\n", "        3.3282e-03, 3.3188e-03, 3.3095e-03, 3.3002e-03, 3.2909e-03, 3.2817e-03,\n", "        3.2725e-03, 3.2633e-03, 3.2541e-03, 3.2450e-03, 3.2359e-03, 3.2268e-03,\n", "        3.2178e-03, 3.2087e-03, 3.1997e-03, 3.1907e-03, 3.1818e-03, 3.1729e-03,\n", "        3.1640e-03, 3.1551e-03, 3.1462e-03, 3.1374e-03, 3.1286e-03, 3.1198e-03,\n", "        3.1110e-03, 3.1023e-03, 3.0936e-03, 3.0849e-03, 3.0763e-03, 3.0676e-03,\n", "        3.0590e-03, 3.0504e-03, 3.0419e-03, 3.0333e-03, 3.0248e-03, 3.0163e-03,\n", "        3.0079e-03, 2.9994e-03, 2.9910e-03, 2.9826e-03, 2.9742e-03, 2.9659e-03,\n", "        2.9576e-03, 2.9493e-03, 2.9410e-03, 2.9327e-03, 2.9245e-03, 2.9163e-03,\n", "        2.9081e-03, 2.8999e-03, 2.8918e-03, 2.8837e-03, 2.8756e-03, 2.8675e-03,\n", "        2.8595e-03, 2.8514e-03, 2.8434e-03, 2.8355e-03, 2.8275e-03, 2.8196e-03,\n", "        2.8116e-03, 2.8038e-03, 2.7959e-03, 2.7880e-03, 2.7802e-03, 2.7724e-03,\n", "        2.7646e-03, 2.7569e-03, 2.7491e-03, 2.7414e-03, 2.7337e-03, 2.7260e-03,\n", "        2.7184e-03, 2.7108e-03, 2.7032e-03, 2.6956e-03, 2.6880e-03, 2.6805e-03,\n", "        2.6729e-03, 2.6654e-03, 2.6579e-03, 2.6505e-03, 2.6430e-03, 2.6356e-03,\n", "        2.6282e-03, 2.6209e-03, 2.6135e-03, 2.6062e-03, 2.5988e-03, 2.5916e-03,\n", "        2.5843e-03, 2.5770e-03, 2.5698e-03, 2.5626e-03, 2.5554e-03, 2.5482e-03,\n", "        2.5411e-03, 2.5339e-03, 2.5268e-03, 2.5197e-03, 2.5127e-03, 2.5056e-03,\n", "        2.4986e-03, 2.4916e-03, 2.4846e-03, 2.4776e-03, 2.4706e-03, 2.4637e-03,\n", "        2.4568e-03, 2.4499e-03, 2.4430e-03, 2.4362e-03, 2.4293e-03, 2.4225e-03,\n", "        2.4157e-03, 2.4089e-03, 2.4022e-03, 2.3954e-03, 2.3887e-03, 2.3820e-03,\n", "        2.3753e-03, 2.3686e-03, 2.3620e-03, 2.3554e-03, 2.3487e-03, 2.3422e-03,\n", "        2.3356e-03, 2.3290e-03, 2.3225e-03, 2.3160e-03, 2.3095e-03, 2.3030e-03,\n", "        2.2965e-03, 2.2901e-03, 2.2836e-03, 2.2772e-03, 2.2708e-03, 2.2645e-03,\n", "        2.2581e-03, 2.2518e-03, 2.2455e-03, 2.2392e-03, 2.2329e-03, 2.2266e-03,\n", "        2.2204e-03, 2.2141e-03, 2.2079e-03, 2.2017e-03, 2.1955e-03, 2.1894e-03,\n", "        2.1832e-03, 2.1771e-03, 2.1710e-03, 2.1649e-03, 2.1588e-03, 2.1528e-03,\n", "        2.1467e-03, 2.1407e-03, 2.1347e-03, 2.1287e-03, 2.1227e-03, 2.1168e-03,\n", "        2.1108e-03, 2.1049e-03, 2.0990e-03, 2.0931e-03, 2.0872e-03, 2.0814e-03,\n", "        2.0755e-03, 2.0697e-03, 2.0639e-03, 2.0581e-03, 2.0523e-03, 2.0465e-03,\n", "        2.0408e-03, 2.0351e-03, 2.0294e-03, 2.0237e-03, 2.0180e-03, 2.0123e-03,\n", "        2.0067e-03, 2.0010e-03, 1.9954e-03, 1.9898e-03, 1.9842e-03, 1.9787e-03,\n", "        1.9731e-03, 1.9676e-03, 1.9621e-03, 1.9565e-03, 1.9511e-03, 1.9456e-03,\n", "        1.9401e-03, 1.9347e-03, 1.9292e-03, 1.9238e-03, 1.9184e-03, 1.9130e-03,\n", "        1.9077e-03, 1.9023e-03, 1.8970e-03, 1.8917e-03, 1.8863e-03, 1.8810e-03,\n", "        1.8758e-03, 1.8705e-03, 1.8653e-03, 1.8600e-03, 1.8548e-03, 1.8496e-03,\n", "        1.8444e-03, 1.8392e-03, 1.8341e-03, 1.8289e-03, 1.8238e-03, 1.8187e-03,\n", "        1.8136e-03, 1.8085e-03, 1.8034e-03, 1.7983e-03, 1.7933e-03, 1.7882e-03,\n", "        1.7832e-03, 1.7782e-03, 1.7732e-03, 1.7683e-03, 1.7633e-03, 1.7583e-03,\n", "        1.7534e-03, 1.7485e-03, 1.7436e-03, 1.7387e-03, 1.7338e-03, 1.7289e-03,\n", "        1.7241e-03, 1.7192e-03, 1.7144e-03, 1.7096e-03, 1.7048e-03, 1.7000e-03,\n", "        1.6953e-03, 1.6905e-03, 1.6857e-03, 1.6810e-03, 1.6763e-03, 1.6716e-03,\n", "        1.6669e-03, 1.6622e-03, 1.6576e-03, 1.6529e-03, 1.6483e-03, 1.6436e-03,\n", "        1.6390e-03, 1.6344e-03, 1.6298e-03, 1.6253e-03, 1.6207e-03, 1.6162e-03,\n", "        1.6116e-03, 1.6071e-03, 1.6026e-03, 1.5981e-03, 1.5936e-03, 1.5891e-03,\n", "        1.5847e-03, 1.5802e-03, 1.5758e-03, 1.5714e-03, 1.5669e-03, 1.5625e-03,\n", "        1.5582e-03, 1.5538e-03, 1.5494e-03, 1.5451e-03, 1.5407e-03, 1.5364e-03,\n", "        1.5321e-03, 1.5278e-03, 1.5235e-03, 1.5192e-03, 1.5150e-03, 1.5107e-03,\n", "        1.5065e-03, 1.5023e-03, 1.4980e-03, 1.4938e-03, 1.4896e-03, 1.4855e-03,\n", "        1.4813e-03, 1.4771e-03, 1.4730e-03, 1.4689e-03, 1.4647e-03, 1.4606e-03,\n", "        1.4565e-03, 1.4524e-03, 1.4484e-03, 1.4443e-03, 1.4402e-03, 1.4362e-03,\n", "        1.4322e-03, 1.4281e-03, 1.4241e-03, 1.4201e-03, 1.4161e-03, 1.4122e-03,\n", "        1.4082e-03, 1.4043e-03, 1.4003e-03, 1.3964e-03, 1.3925e-03, 1.3886e-03,\n", "        1.3847e-03, 1.3808e-03, 1.3769e-03, 1.3730e-03, 1.3692e-03, 1.3653e-03,\n", "        1.3615e-03, 1.3577e-03, 1.3539e-03, 1.3501e-03, 1.3463e-03, 1.3425e-03,\n", "        1.3387e-03, 1.3350e-03, 1.3312e-03, 1.3275e-03, 1.3238e-03, 1.3201e-03,\n", "        1.3163e-03, 1.3127e-03, 1.3090e-03, 1.3053e-03, 1.3016e-03, 1.2980e-03,\n", "        1.2943e-03, 1.2907e-03, 1.2871e-03, 1.2835e-03, 1.2799e-03, 1.2763e-03,\n", "        1.2727e-03, 1.2691e-03, 1.2656e-03, 1.2620e-03, 1.2585e-03, 1.2549e-03,\n", "        1.2514e-03, 1.2479e-03, 1.2444e-03, 1.2409e-03, 1.2374e-03, 1.2339e-03,\n", "        1.2305e-03, 1.2270e-03, 1.2236e-03, 1.2201e-03, 1.2167e-03, 1.2133e-03,\n", "        1.2099e-03, 1.2065e-03, 1.2031e-03, 1.1997e-03, 1.1964e-03, 1.1930e-03,\n", "        1.1897e-03, 1.1863e-03, 1.1830e-03, 1.1797e-03, 1.1764e-03, 1.1731e-03,\n", "        1.1698e-03, 1.1665e-03, 1.1632e-03, 1.1600e-03, 1.1567e-03, 1.1534e-03,\n", "        1.1502e-03, 1.1470e-03, 1.1438e-03, 1.1406e-03, 1.1374e-03, 1.1342e-03,\n", "        1.1310e-03, 1.1278e-03, 1.1246e-03, 1.1215e-03, 1.1183e-03, 1.1152e-03,\n", "        1.1121e-03, 1.1089e-03, 1.1058e-03, 1.1027e-03, 1.0996e-03, 1.0965e-03,\n", "        1.0935e-03, 1.0904e-03, 1.0873e-03, 1.0843e-03, 1.0812e-03, 1.0782e-03,\n", "        1.0752e-03, 1.0722e-03, 1.0692e-03, 1.0661e-03, 1.0632e-03, 1.0602e-03,\n", "        1.0572e-03, 1.0542e-03, 1.0513e-03, 1.0483e-03, 1.0454e-03, 1.0424e-03,\n", "        1.0395e-03, 1.0366e-03, 1.0337e-03, 1.0308e-03, 1.0279e-03, 1.0250e-03,\n", "        1.0221e-03, 1.0193e-03, 1.0164e-03, 1.0136e-03, 1.0107e-03, 1.0079e-03,\n", "        1.0050e-03, 1.0022e-03, 9.9941e-04, 9.9660e-04, 9.9380e-04, 9.9101e-04,\n", "        9.8823e-04, 9.8546e-04, 9.8269e-04, 9.7993e-04, 9.7718e-04, 9.7444e-04,\n", "        9.7171e-04, 9.6898e-04, 9.6626e-04, 9.6355e-04, 9.6084e-04, 9.5815e-04,\n", "        9.5546e-04, 9.5277e-04, 9.5010e-04, 9.4743e-04, 9.4477e-04, 9.4212e-04,\n", "        9.3948e-04, 9.3684e-04, 9.3421e-04, 9.3159e-04, 9.2897e-04, 9.2637e-04,\n", "        9.2377e-04, 9.2117e-04, 9.1859e-04, 9.1601e-04, 9.1344e-04, 9.1088e-04,\n", "        9.0832e-04, 9.0577e-04, 9.0323e-04, 9.0069e-04, 8.9816e-04, 8.9564e-04,\n", "        8.9313e-04, 8.9062e-04, 8.8812e-04, 8.8563e-04, 8.8314e-04, 8.8066e-04,\n", "        8.7819e-04, 8.7573e-04, 8.7327e-04, 8.7082e-04, 8.6837e-04, 8.6594e-04,\n", "        8.6351e-04, 8.6108e-04, 8.5867e-04, 8.5626e-04, 8.5385e-04, 8.5146e-04,\n", "        8.4907e-04, 8.4668e-04, 8.4431e-04, 8.4194e-04, 8.3957e-04, 8.3722e-04,\n", "        8.3487e-04, 8.3252e-04, 8.3019e-04, 8.2786e-04, 8.2553e-04, 8.2322e-04,\n", "        8.2090e-04, 8.1860e-04, 8.1630e-04, 8.1401e-04, 8.1173e-04, 8.0945e-04,\n", "        8.0718e-04, 8.0491e-04, 8.0265e-04, 8.0040e-04, 7.9815e-04, 7.9591e-04,\n", "        7.9368e-04, 7.9145e-04, 7.8923e-04, 7.8701e-04, 7.8480e-04, 7.8260e-04,\n", "        7.8041e-04, 7.7821e-04, 7.7603e-04, 7.7385e-04, 7.7168e-04, 7.6951e-04,\n", "        7.6735e-04, 7.6520e-04, 7.6305e-04, 7.6091e-04, 7.5878e-04, 7.5665e-04,\n", "        7.5452e-04, 7.5240e-04, 7.5029e-04, 7.4819e-04, 7.4609e-04, 7.4399e-04,\n", "        7.4190e-04, 7.3982e-04, 7.3774e-04, 7.3567e-04, 7.3361e-04, 7.3155e-04,\n", "        7.2950e-04, 7.2745e-04, 7.2541e-04, 7.2337e-04, 7.2134e-04, 7.1932e-04,\n", "        7.1730e-04, 7.1528e-04, 7.1328e-04, 7.1127e-04, 7.0928e-04, 7.0729e-04,\n", "        7.0530e-04, 7.0332e-04, 7.0135e-04, 6.9938e-04, 6.9742e-04, 6.9546e-04,\n", "        6.9351e-04, 6.9156e-04, 6.8962e-04, 6.8768e-04, 6.8575e-04, 6.8383e-04,\n", "        6.8191e-04, 6.8000e-04, 6.7809e-04, 6.7618e-04, 6.7429e-04, 6.7239e-04,\n", "        6.7051e-04, 6.6862e-04, 6.6675e-04, 6.6488e-04, 6.6301e-04, 6.6115e-04,\n", "        6.5929e-04, 6.5744e-04, 6.5560e-04, 6.5376e-04, 6.5192e-04, 6.5009e-04,\n", "        6.4827e-04, 6.4645e-04, 6.4463e-04, 6.4282e-04, 6.4102e-04, 6.3922e-04,\n", "        6.3743e-04, 6.3564e-04, 6.3385e-04, 6.3207e-04, 6.3030e-04, 6.2853e-04,\n", "        6.2677e-04, 6.2501e-04, 6.2325e-04, 6.2150e-04, 6.1976e-04, 6.1802e-04,\n", "        6.1628e-04, 6.1455e-04, 6.1283e-04, 6.1111e-04, 6.0939e-04, 6.0768e-04,\n", "        6.0598e-04, 6.0428e-04, 6.0258e-04, 6.0089e-04, 5.9920e-04, 5.9752e-04,\n", "        5.9584e-04, 5.9417e-04, 5.9250e-04, 5.9084e-04, 5.8918e-04, 5.8753e-04,\n", "        5.8588e-04, 5.8424e-04, 5.8260e-04, 5.8096e-04, 5.7933e-04, 5.7770e-04,\n", "        5.7608e-04, 5.7446e-04, 5.7285e-04, 5.7124e-04, 5.6964e-04, 5.6804e-04,\n", "        5.6645e-04, 5.6486e-04, 5.6327e-04, 5.6169e-04, 5.6011e-04, 5.5854e-04,\n", "        5.5698e-04, 5.5541e-04, 5.5385e-04, 5.5230e-04, 5.5075e-04, 5.4920e-04,\n", "        5.4766e-04, 5.4612e-04, 5.4459e-04, 5.4306e-04, 5.4154e-04, 5.4002e-04,\n", "        5.3850e-04, 5.3699e-04, 5.3548e-04, 5.3398e-04, 5.3248e-04, 5.3099e-04,\n", "        5.2950e-04, 5.2801e-04, 5.2653e-04, 5.2505e-04, 5.2358e-04, 5.2211e-04,\n", "        5.2064e-04, 5.1918e-04, 5.1772e-04, 5.1627e-04, 5.1482e-04, 5.1338e-04,\n", "        5.1193e-04, 5.1050e-04, 5.0907e-04, 5.0764e-04, 5.0621e-04, 5.0479e-04,\n", "        5.0337e-04, 5.0196e-04, 5.0055e-04, 4.9915e-04, 4.9775e-04, 4.9635e-04,\n", "        4.9496e-04, 4.9357e-04, 4.9218e-04, 4.9080e-04, 4.8942e-04, 4.8805e-04,\n", "        4.8668e-04, 4.8531e-04, 4.8395e-04, 4.8259e-04, 4.8124e-04, 4.7989e-04,\n", "        4.7854e-04, 4.7720e-04, 4.7586e-04, 4.7452e-04, 4.7319e-04, 4.7186e-04,\n", "        4.7054e-04, 4.6922e-04, 4.6790e-04, 4.6659e-04, 4.6528e-04, 4.6397e-04,\n", "        4.6267e-04, 4.6137e-04, 4.6007e-04, 4.5878e-04, 4.5750e-04, 4.5621e-04,\n", "        4.5493e-04, 4.5365e-04, 4.5238e-04, 4.5111e-04, 4.4984e-04, 4.4858e-04,\n", "        4.4732e-04, 4.4607e-04, 4.4482e-04, 4.4357e-04, 4.4232e-04, 4.4108e-04,\n", "        4.3984e-04, 4.3861e-04, 4.3738e-04, 4.3615e-04, 4.3492e-04, 4.3370e-04,\n", "        4.3249e-04, 4.3127e-04, 4.3006e-04, 4.2885e-04, 4.2765e-04, 4.2645e-04,\n", "        4.2525e-04, 4.2406e-04, 4.2287e-04, 4.2168e-04, 4.2050e-04, 4.1932e-04,\n", "        4.1814e-04, 4.1697e-04, 4.1580e-04, 4.1463e-04, 4.1347e-04, 4.1231e-04,\n", "        4.1115e-04, 4.1000e-04, 4.0884e-04, 4.0770e-04, 4.0655e-04, 4.0541e-04,\n", "        4.0427e-04, 4.0314e-04, 4.0201e-04, 4.0088e-04, 3.9975e-04, 3.9863e-04,\n", "        3.9751e-04, 3.9640e-04, 3.9528e-04, 3.9418e-04, 3.9307e-04, 3.9197e-04,\n", "        3.9087e-04, 3.8977e-04, 3.8867e-04, 3.8758e-04, 3.8650e-04, 3.8541e-04,\n", "        3.8433e-04, 3.8325e-04, 3.8217e-04, 3.8110e-04, 3.8003e-04, 3.7897e-04,\n", "        3.7790e-04, 3.7684e-04, 3.7578e-04, 3.7473e-04, 3.7368e-04, 3.7263e-04,\n", "        3.7158e-04, 3.7054e-04, 3.6950e-04, 3.6846e-04, 3.6743e-04, 3.6640e-04,\n", "        3.6537e-04, 3.6434e-04, 3.6332e-04, 3.6230e-04, 3.6128e-04, 3.6027e-04,\n", "        3.5926e-04, 3.5825e-04, 3.5724e-04, 3.5624e-04, 3.5524e-04, 3.5424e-04,\n", "        3.5325e-04, 3.5226e-04, 3.5127e-04, 3.5028e-04, 3.4930e-04, 3.4832e-04,\n", "        3.4734e-04, 3.4637e-04, 3.4540e-04, 3.4443e-04, 3.4346e-04, 3.4249e-04,\n", "        3.4153e-04, 3.4057e-04, 3.3962e-04, 3.3867e-04, 3.3772e-04, 3.3677e-04,\n", "        3.3582e-04, 3.3488e-04, 3.3394e-04, 3.3300e-04, 3.3207e-04, 3.3114e-04,\n", "        3.3021e-04, 3.2928e-04, 3.2836e-04, 3.2743e-04, 3.2651e-04, 3.2560e-04,\n", "        3.2468e-04, 3.2377e-04, 3.2286e-04, 3.2196e-04, 3.2105e-04, 3.2015e-04,\n", "        3.1925e-04, 3.1836e-04, 3.1746e-04, 3.1657e-04, 3.1568e-04, 3.1480e-04,\n", "        3.1392e-04, 3.1303e-04, 3.1216e-04, 3.1128e-04, 3.1041e-04, 3.0953e-04,\n", "        3.0867e-04, 3.0780e-04, 3.0694e-04, 3.0607e-04, 3.0521e-04, 3.0436e-04,\n", "        3.0350e-04, 3.0265e-04, 3.0180e-04, 3.0096e-04, 3.0011e-04, 2.9927e-04,\n", "        2.9843e-04, 2.9759e-04, 2.9676e-04, 2.9592e-04, 2.9509e-04, 2.9426e-04,\n", "        2.9344e-04, 2.9261e-04, 2.9179e-04, 2.9097e-04, 2.9016e-04, 2.8934e-04,\n", "        2.8853e-04, 2.8772e-04, 2.8691e-04, 2.8611e-04, 2.8530e-04, 2.8450e-04,\n", "        2.8371e-04, 2.8291e-04, 2.8211e-04, 2.8132e-04, 2.8053e-04, 2.7975e-04,\n", "        2.7896e-04, 2.7818e-04, 2.7740e-04, 2.7662e-04, 2.7584e-04, 2.7507e-04,\n", "        2.7430e-04, 2.7353e-04, 2.7276e-04, 2.7199e-04, 2.7123e-04, 2.7047e-04,\n", "        2.6971e-04, 2.6895e-04, 2.6820e-04, 2.6744e-04, 2.6669e-04, 2.6594e-04,\n", "        2.6520e-04, 2.6445e-04, 2.6371e-04, 2.6297e-04, 2.6223e-04, 2.6150e-04,\n", "        2.6076e-04, 2.6003e-04, 2.5930e-04, 2.5857e-04, 2.5785e-04, 2.5712e-04,\n", "        2.5640e-04, 2.5568e-04, 2.5496e-04, 2.5425e-04, 2.5354e-04, 2.5282e-04,\n", "        2.5211e-04, 2.5141e-04, 2.5070e-04, 2.5000e-04, 2.4930e-04, 2.4860e-04,\n", "        2.4790e-04, 2.4720e-04, 2.4651e-04, 2.4582e-04, 2.4513e-04, 2.4444e-04,\n", "        2.4375e-04, 2.4307e-04, 2.4239e-04, 2.4171e-04, 2.4103e-04, 2.4035e-04,\n", "        2.3968e-04, 2.3900e-04, 2.3833e-04, 2.3766e-04, 2.3700e-04, 2.3633e-04,\n", "        2.3567e-04, 2.3501e-04, 2.3435e-04, 2.3369e-04, 2.3303e-04, 2.3238e-04,\n", "        2.3173e-04, 2.3108e-04, 2.3043e-04, 2.2978e-04, 2.2914e-04, 2.2849e-04,\n", "        2.2785e-04, 2.2721e-04, 2.2657e-04, 2.2594e-04, 2.2530e-04, 2.2467e-04,\n", "        2.2404e-04, 2.2341e-04, 2.2279e-04, 2.2216e-04, 2.2154e-04, 2.2091e-04,\n", "        2.2029e-04, 2.1968e-04, 2.1906e-04, 2.1844e-04, 2.1783e-04, 2.1722e-04,\n", "        2.1661e-04, 2.1600e-04, 2.1540e-04, 2.1479e-04, 2.1419e-04, 2.1359e-04,\n", "        2.1299e-04, 2.1239e-04, 2.1179e-04, 2.1120e-04, 2.1061e-04, 2.1002e-04,\n", "        2.0943e-04, 2.0884e-04, 2.0825e-04, 2.0767e-04, 2.0708e-04, 2.0650e-04,\n", "        2.0592e-04, 2.0535e-04, 2.0477e-04, 2.0419e-04, 2.0362e-04, 2.0305e-04,\n", "        2.0248e-04, 2.0191e-04, 2.0135e-04, 2.0078e-04, 2.0022e-04, 1.9965e-04,\n", "        1.9909e-04, 1.9854e-04, 1.9798e-04, 1.9742e-04, 1.9687e-04, 1.9632e-04,\n", "        1.9576e-04, 1.9522e-04, 1.9467e-04, 1.9412e-04, 1.9358e-04, 1.9303e-04,\n", "        1.9249e-04, 1.9195e-04, 1.9141e-04, 1.9087e-04, 1.9034e-04, 1.8980e-04,\n", "        1.8927e-04, 1.8874e-04, 1.8821e-04, 1.8768e-04, 1.8716e-04, 1.8663e-04,\n", "        1.8611e-04, 1.8558e-04, 1.8506e-04, 1.8454e-04, 1.8403e-04, 1.8351e-04,\n", "        1.8299e-04, 1.8248e-04, 1.8197e-04, 1.8146e-04, 1.8095e-04, 1.8044e-04,\n", "        1.7993e-04, 1.7943e-04, 1.7893e-04, 1.7842e-04, 1.7792e-04, 1.7742e-04,\n", "        1.7692e-04, 1.7643e-04, 1.7593e-04, 1.7544e-04, 1.7495e-04, 1.7446e-04,\n", "        1.7397e-04, 1.7348e-04, 1.7299e-04, 1.7251e-04, 1.7202e-04, 1.7154e-04,\n", "        1.7106e-04, 1.7058e-04, 1.7010e-04, 1.6962e-04, 1.6914e-04, 1.6867e-04,\n", "        1.6820e-04, 1.6772e-04, 1.6725e-04, 1.6678e-04, 1.6632e-04, 1.6585e-04,\n", "        1.6538e-04, 1.6492e-04, 1.6446e-04, 1.6399e-04, 1.6353e-04, 1.6308e-04,\n", "        1.6262e-04, 1.6216e-04, 1.6171e-04, 1.6125e-04, 1.6080e-04, 1.6035e-04,\n", "        1.5990e-04, 1.5945e-04, 1.5900e-04, 1.5856e-04, 1.5811e-04, 1.5767e-04,\n", "        1.5722e-04, 1.5678e-04, 1.5634e-04, 1.5590e-04, 1.5547e-04, 1.5503e-04,\n", "        1.5459e-04, 1.5416e-04, 1.5373e-04, 1.5330e-04, 1.5287e-04, 1.5244e-04,\n", "        1.5201e-04, 1.5158e-04, 1.5116e-04, 1.5073e-04, 1.5031e-04, 1.4989e-04,\n", "        1.4947e-04, 1.4905e-04, 1.4863e-04, 1.4821e-04, 1.4780e-04, 1.4738e-04,\n", "        1.4697e-04, 1.4656e-04, 1.4614e-04, 1.4573e-04, 1.4532e-04, 1.4492e-04,\n", "        1.4451e-04, 1.4410e-04, 1.4370e-04, 1.4330e-04, 1.4289e-04, 1.4249e-04,\n", "        1.4209e-04, 1.4169e-04, 1.4130e-04, 1.4090e-04, 1.4050e-04, 1.4011e-04,\n", "        1.3972e-04, 1.3933e-04, 1.3893e-04, 1.3854e-04, 1.3816e-04, 1.3777e-04,\n", "        1.3738e-04, 1.3700e-04, 1.3661e-04, 1.3623e-04, 1.3584e-04, 1.3546e-04,\n", "        1.3508e-04, 1.3470e-04, 1.3433e-04, 1.3395e-04, 1.3357e-04, 1.3320e-04,\n", "        1.3282e-04, 1.3245e-04, 1.3208e-04, 1.3171e-04, 1.3134e-04, 1.3097e-04,\n", "        1.3060e-04, 1.3024e-04, 1.2987e-04, 1.2951e-04, 1.2914e-04, 1.2878e-04,\n", "        1.2842e-04, 1.2806e-04, 1.2770e-04, 1.2734e-04, 1.2698e-04, 1.2663e-04,\n", "        1.2627e-04, 1.2592e-04, 1.2556e-04, 1.2521e-04, 1.2486e-04, 1.2451e-04,\n", "        1.2416e-04, 1.2381e-04, 1.2346e-04, 1.2312e-04, 1.2277e-04, 1.2243e-04,\n", "        1.2208e-04, 1.2174e-04, 1.2140e-04, 1.2106e-04, 1.2072e-04, 1.2038e-04,\n", "        1.2004e-04, 1.1970e-04, 1.1937e-04, 1.1903e-04, 1.1870e-04, 1.1837e-04,\n", "        1.1803e-04, 1.1770e-04, 1.1737e-04, 1.1704e-04, 1.1671e-04, 1.1639e-04,\n", "        1.1606e-04, 1.1573e-04, 1.1541e-04, 1.1509e-04, 1.1476e-04, 1.1444e-04,\n", "        1.1412e-04, 1.1380e-04, 1.1348e-04, 1.1316e-04, 1.1284e-04, 1.1253e-04,\n", "        1.1221e-04, 1.1190e-04, 1.1158e-04, 1.1127e-04, 1.1096e-04, 1.1064e-04,\n", "        1.1033e-04, 1.1002e-04, 1.0972e-04, 1.0941e-04, 1.0910e-04, 1.0879e-04,\n", "        1.0849e-04, 1.0818e-04, 1.0788e-04, 1.0758e-04, 1.0728e-04, 1.0698e-04,\n", "        1.0667e-04, 1.0638e-04, 1.0608e-04, 1.0578e-04, 1.0548e-04, 1.0519e-04,\n", "        1.0489e-04, 1.0460e-04, 1.0430e-04, 1.0401e-04, 1.0372e-04, 1.0343e-04,\n", "        1.0314e-04, 1.0285e-04, 1.0256e-04, 1.0227e-04, 1.0198e-04, 1.0170e-04,\n", "        1.0141e-04, 1.0113e-04, 1.0084e-04, 1.0056e-04, 1.0028e-04, 9.9997e-05,\n", "        9.9716e-05, 9.9436e-05, 9.9157e-05, 9.8879e-05, 9.8601e-05, 9.8325e-05,\n", "        9.8049e-05, 9.7773e-05, 9.7499e-05, 9.7225e-05, 9.6952e-05, 9.6680e-05,\n", "        9.6409e-05, 9.6138e-05, 9.5868e-05, 9.5599e-05, 9.5331e-05, 9.5063e-05,\n", "        9.4797e-05, 9.4531e-05, 9.4265e-05, 9.4001e-05, 9.3737e-05, 9.3474e-05,\n", "        9.3211e-05, 9.2950e-05, 9.2689e-05, 9.2429e-05, 9.2169e-05, 9.1910e-05,\n", "        9.1653e-05, 9.1395e-05, 9.1139e-05, 9.0883e-05, 9.0628e-05, 9.0373e-05,\n", "        9.0120e-05, 8.9867e-05, 8.9615e-05, 8.9363e-05, 8.9112e-05, 8.8862e-05,\n", "        8.8613e-05, 8.8364e-05, 8.8116e-05, 8.7869e-05, 8.7622e-05, 8.7376e-05,\n", "        8.7131e-05, 8.6886e-05, 8.6642e-05, 8.6399e-05, 8.6157e-05, 8.5915e-05,\n", "        8.5674e-05, 8.5433e-05, 8.5193e-05, 8.4954e-05, 8.4716e-05, 8.4478e-05,\n", "        8.4241e-05, 8.4005e-05, 8.3769e-05, 8.3534e-05, 8.3299e-05, 8.3065e-05,\n", "        8.2832e-05, 8.2600e-05, 8.2368e-05, 8.2137e-05, 8.1906e-05, 8.1676e-05,\n", "        8.1447e-05, 8.1218e-05, 8.0990e-05, 8.0763e-05, 8.0536e-05, 8.0310e-05,\n", "        8.0085e-05, 7.9860e-05, 7.9636e-05, 7.9412e-05, 7.9190e-05, 7.8967e-05,\n", "        7.8746e-05, 7.8525e-05, 7.8304e-05, 7.8084e-05, 7.7865e-05, 7.7647e-05,\n", "        7.7429e-05, 7.7211e-05, 7.6995e-05, 7.6779e-05, 7.6563e-05, 7.6348e-05,\n", "        7.6134e-05, 7.5920e-05, 7.5707e-05, 7.5495e-05, 7.5283e-05, 7.5071e-05,\n", "        7.4861e-05, 7.4651e-05, 7.4441e-05, 7.4232e-05, 7.4024e-05, 7.3816e-05,\n", "        7.3609e-05, 7.3402e-05, 7.3196e-05, 7.2991e-05, 7.2786e-05, 7.2582e-05,\n", "        7.2378e-05, 7.2175e-05, 7.1972e-05, 7.1770e-05, 7.1569e-05, 7.1368e-05,\n", "        7.1167e-05, 7.0968e-05, 7.0768e-05, 7.0570e-05, 7.0372e-05, 7.0174e-05,\n", "        6.9977e-05, 6.9781e-05, 6.9585e-05, 6.9390e-05, 6.9195e-05, 6.9001e-05,\n", "        6.8807e-05, 6.8614e-05, 6.8421e-05, 6.8229e-05, 6.8038e-05, 6.7847e-05,\n", "        6.7656e-05, 6.7466e-05, 6.7277e-05, 6.7088e-05, 6.6900e-05, 6.6712e-05,\n", "        6.6525e-05, 6.6338e-05, 6.6152e-05, 6.5966e-05, 6.5781e-05, 6.5597e-05,\n", "        6.5412e-05, 6.5229e-05, 6.5046e-05, 6.4863e-05, 6.4681e-05, 6.4500e-05,\n", "        6.4318e-05, 6.4138e-05, 6.3958e-05, 6.3778e-05, 6.3599e-05, 6.3421e-05,\n", "        6.3243e-05, 6.3065e-05, 6.2888e-05, 6.2712e-05, 6.2536e-05, 6.2360e-05,\n", "        6.2185e-05, 6.2011e-05, 6.1837e-05, 6.1663e-05, 6.1490e-05, 6.1317e-05,\n", "        6.1145e-05, 6.0974e-05, 6.0803e-05, 6.0632e-05, 6.0462e-05, 6.0292e-05,\n", "        6.0123e-05, 5.9954e-05, 5.9786e-05, 5.9618e-05, 5.9451e-05, 5.9284e-05,\n", "        5.9117e-05, 5.8951e-05, 5.8786e-05, 5.8621e-05, 5.8456e-05, 5.8292e-05,\n", "        5.8129e-05, 5.7966e-05, 5.7803e-05, 5.7641e-05, 5.7479e-05, 5.7317e-05,\n", "        5.7157e-05, 5.6996e-05, 5.6836e-05, 5.6677e-05, 5.6518e-05, 5.6359e-05,\n", "        5.6201e-05, 5.6043e-05, 5.5886e-05, 5.5729e-05, 5.5572e-05, 5.5416e-05,\n", "        5.5261e-05, 5.5106e-05, 5.4951e-05, 5.4797e-05, 5.4643e-05, 5.4490e-05,\n", "        5.4337e-05, 5.4184e-05, 5.4032e-05, 5.3880e-05, 5.3729e-05, 5.3578e-05,\n", "        5.3428e-05, 5.3278e-05, 5.3129e-05, 5.2979e-05, 5.2831e-05, 5.2682e-05,\n", "        5.2535e-05, 5.2387e-05, 5.2240e-05, 5.2093e-05, 5.1947e-05, 5.1801e-05,\n", "        5.1656e-05, 5.1511e-05, 5.1366e-05, 5.1222e-05, 5.1078e-05, 5.0935e-05,\n", "        5.0792e-05, 5.0650e-05, 5.0507e-05, 5.0366e-05, 5.0224e-05, 5.0083e-05,\n", "        4.9943e-05, 4.9803e-05, 4.9663e-05, 4.9523e-05, 4.9384e-05, 4.9246e-05,\n", "        4.9108e-05, 4.8970e-05, 4.8832e-05, 4.8695e-05, 4.8559e-05, 4.8422e-05,\n", "        4.8286e-05, 4.8151e-05, 4.8016e-05, 4.7881e-05, 4.7746e-05, 4.7612e-05,\n", "        4.7479e-05, 4.7346e-05, 4.7213e-05, 4.7080e-05, 4.6948e-05, 4.6816e-05,\n", "        4.6685e-05, 4.6554e-05, 4.6423e-05, 4.6293e-05, 4.6163e-05, 4.6033e-05,\n", "        4.5904e-05, 4.5775e-05, 4.5647e-05, 4.5519e-05, 4.5391e-05, 4.5263e-05,\n", "        4.5136e-05, 4.5010e-05, 4.4883e-05, 4.4757e-05, 4.4632e-05, 4.4507e-05,\n", "        4.4382e-05, 4.4257e-05, 4.4133e-05, 4.4009e-05, 4.3885e-05, 4.3762e-05,\n", "        4.3639e-05, 4.3517e-05, 4.3395e-05, 4.3273e-05, 4.3152e-05, 4.3030e-05,\n", "        4.2910e-05, 4.2789e-05, 4.2669e-05, 4.2549e-05, 4.2430e-05, 4.2311e-05,\n", "        4.2192e-05, 4.2074e-05, 4.1956e-05, 4.1838e-05, 4.1720e-05, 4.1603e-05,\n", "        4.1486e-05, 4.1370e-05, 4.1254e-05, 4.1138e-05, 4.1023e-05, 4.0907e-05,\n", "        4.0793e-05, 4.0678e-05, 4.0564e-05, 4.0450e-05, 4.0337e-05, 4.0223e-05,\n", "        4.0110e-05, 3.9998e-05, 3.9886e-05, 3.9774e-05, 3.9662e-05, 3.9551e-05,\n", "        3.9440e-05, 3.9329e-05, 3.9219e-05, 3.9109e-05, 3.8999e-05, 3.8889e-05,\n", "        3.8780e-05, 3.8671e-05, 3.8563e-05, 3.8454e-05, 3.8347e-05, 3.8239e-05,\n", "        3.8132e-05, 3.8025e-05, 3.7918e-05, 3.7811e-05, 3.7705e-05, 3.7599e-05,\n", "        3.7494e-05, 3.7389e-05, 3.7284e-05, 3.7179e-05, 3.7075e-05, 3.6971e-05,\n", "        3.6867e-05, 3.6763e-05, 3.6660e-05, 3.6557e-05, 3.6455e-05, 3.6352e-05,\n", "        3.6250e-05, 3.6149e-05, 3.6047e-05, 3.5946e-05, 3.5845e-05, 3.5744e-05,\n", "        3.5644e-05, 3.5544e-05, 3.5444e-05, 3.5345e-05, 3.5246e-05, 3.5147e-05,\n", "        3.5048e-05, 3.4950e-05, 3.4852e-05, 3.4754e-05, 3.4656e-05, 3.4559e-05,\n", "        3.4462e-05, 3.4365e-05, 3.4269e-05, 3.4173e-05, 3.4077e-05, 3.3981e-05,\n", "        3.3886e-05, 3.3791e-05, 3.3696e-05, 3.3601e-05, 3.3507e-05, 3.3413e-05,\n", "        3.3319e-05, 3.3225e-05, 3.3132e-05, 3.3039e-05, 3.2946e-05, 3.2854e-05,\n", "        3.2762e-05, 3.2670e-05, 3.2578e-05, 3.2487e-05, 3.2395e-05, 3.2305e-05,\n", "        3.2214e-05, 3.2123e-05, 3.2033e-05, 3.1943e-05, 3.1854e-05, 3.1764e-05,\n", "        3.1675e-05, 3.1586e-05, 3.1498e-05, 3.1409e-05, 3.1321e-05, 3.1233e-05,\n", "        3.1145e-05, 3.1058e-05, 3.0971e-05, 3.0884e-05, 3.0797e-05, 3.0711e-05,\n", "        3.0625e-05, 3.0539e-05, 3.0453e-05, 3.0367e-05, 3.0282e-05, 3.0197e-05,\n", "        3.0112e-05, 3.0028e-05, 2.9944e-05, 2.9860e-05, 2.9776e-05, 2.9692e-05,\n", "        2.9609e-05, 2.9526e-05, 2.9443e-05, 2.9360e-05, 2.9278e-05, 2.9196e-05,\n", "        2.9114e-05, 2.9032e-05, 2.8951e-05, 2.8869e-05, 2.8788e-05, 2.8707e-05,\n", "        2.8627e-05, 2.8546e-05, 2.8466e-05, 2.8386e-05, 2.8307e-05, 2.8227e-05,\n", "        2.8148e-05, 2.8069e-05, 2.7990e-05, 2.7912e-05, 2.7833e-05, 2.7755e-05,\n", "        2.7677e-05, 2.7600e-05, 2.7522e-05, 2.7445e-05, 2.7368e-05, 2.7291e-05,\n", "        2.7215e-05, 2.7138e-05, 2.7062e-05, 2.6986e-05, 2.6910e-05, 2.6835e-05,\n", "        2.6759e-05, 2.6684e-05, 2.6609e-05, 2.6535e-05, 2.6460e-05, 2.6386e-05,\n", "        2.6312e-05, 2.6238e-05, 2.6164e-05, 2.6091e-05, 2.6018e-05, 2.5945e-05,\n", "        2.5872e-05, 2.5799e-05, 2.5727e-05, 2.5655e-05, 2.5583e-05, 2.5511e-05,\n", "        2.5439e-05, 2.5368e-05, 2.5297e-05, 2.5226e-05, 2.5155e-05, 2.5084e-05,\n", "        2.5014e-05, 2.4944e-05, 2.4874e-05, 2.4804e-05, 2.4734e-05, 2.4665e-05,\n", "        2.4595e-05, 2.4526e-05, 2.4458e-05, 2.4389e-05, 2.4321e-05, 2.4252e-05,\n", "        2.4184e-05, 2.4116e-05, 2.4049e-05, 2.3981e-05, 2.3914e-05, 2.3847e-05,\n", "        2.3780e-05, 2.3713e-05, 2.3646e-05, 2.3580e-05, 2.3514e-05, 2.3448e-05,\n", "        2.3382e-05, 2.3316e-05, 2.3251e-05, 2.3186e-05, 2.3121e-05, 2.3056e-05,\n", "        2.2991e-05, 2.2926e-05, 2.2862e-05, 2.2798e-05, 2.2734e-05, 2.2670e-05,\n", "        2.2607e-05, 2.2543e-05, 2.2480e-05, 2.2417e-05, 2.2354e-05, 2.2291e-05,\n", "        2.2228e-05, 2.2166e-05, 2.2104e-05, 2.2042e-05, 2.1980e-05, 2.1918e-05,\n", "        2.1857e-05, 2.1795e-05, 2.1734e-05, 2.1673e-05, 2.1612e-05, 2.1552e-05,\n", "        2.1491e-05, 2.1431e-05, 2.1371e-05, 2.1311e-05, 2.1251e-05, 2.1191e-05,\n", "        2.1132e-05, 2.1073e-05, 2.1013e-05, 2.0954e-05, 2.0896e-05, 2.0837e-05,\n", "        2.0778e-05, 2.0720e-05, 2.0662e-05, 2.0604e-05, 2.0546e-05, 2.0488e-05,\n", "        2.0431e-05, 2.0374e-05, 2.0316e-05, 2.0259e-05, 2.0203e-05, 2.0146e-05,\n", "        2.0089e-05, 2.0033e-05, 1.9977e-05, 1.9921e-05, 1.9865e-05, 1.9809e-05,\n", "        1.9753e-05, 1.9698e-05, 1.9643e-05, 1.9587e-05, 1.9532e-05, 1.9478e-05,\n", "        1.9423e-05, 1.9368e-05, 1.9314e-05, 1.9260e-05, 1.9206e-05, 1.9152e-05,\n", "        1.9098e-05, 1.9045e-05, 1.8991e-05, 1.8938e-05, 1.8885e-05, 1.8832e-05,\n", "        1.8779e-05, 1.8726e-05, 1.8674e-05, 1.8621e-05, 1.8569e-05, 1.8517e-05,\n", "        1.8465e-05, 1.8413e-05, 1.8361e-05, 1.8310e-05, 1.8258e-05, 1.8207e-05,\n", "        1.8156e-05, 1.8105e-05, 1.8054e-05, 1.8004e-05, 1.7953e-05, 1.7903e-05,\n", "        1.7852e-05, 1.7802e-05, 1.7752e-05, 1.7702e-05, 1.7653e-05, 1.7603e-05,\n", "        1.7554e-05, 1.7505e-05, 1.7455e-05, 1.7406e-05, 1.7358e-05, 1.7309e-05,\n", "        1.7260e-05, 1.7212e-05, 1.7163e-05, 1.7115e-05, 1.7067e-05, 1.7019e-05,\n", "        1.6972e-05, 1.6924e-05, 1.6876e-05, 1.6829e-05, 1.6782e-05, 1.6735e-05,\n", "        1.6688e-05, 1.6641e-05, 1.6594e-05, 1.6548e-05, 1.6501e-05, 1.6455e-05,\n", "        1.6409e-05, 1.6363e-05, 1.6317e-05, 1.6271e-05, 1.6225e-05, 1.6180e-05,\n", "        1.6134e-05, 1.6089e-05, 1.6044e-05, 1.5999e-05, 1.5954e-05, 1.5909e-05,\n", "        1.5864e-05, 1.5820e-05, 1.5776e-05, 1.5731e-05, 1.5687e-05, 1.5643e-05,\n", "        1.5599e-05, 1.5555e-05, 1.5512e-05, 1.5468e-05, 1.5425e-05, 1.5381e-05,\n", "        1.5338e-05, 1.5295e-05, 1.5252e-05, 1.5210e-05, 1.5167e-05, 1.5124e-05,\n", "        1.5082e-05, 1.5039e-05, 1.4997e-05, 1.4955e-05, 1.4913e-05, 1.4871e-05,\n", "        1.4830e-05, 1.4788e-05, 1.4746e-05, 1.4705e-05, 1.4664e-05, 1.4623e-05,\n", "        1.4582e-05, 1.4541e-05, 1.4500e-05, 1.4459e-05, 1.4419e-05, 1.4378e-05,\n", "        1.4338e-05, 1.4297e-05, 1.4257e-05, 1.4217e-05, 1.4177e-05, 1.4138e-05,\n", "        1.4098e-05, 1.4058e-05, 1.4019e-05, 1.3980e-05, 1.3940e-05, 1.3901e-05,\n", "        1.3862e-05, 1.3823e-05, 1.3784e-05, 1.3746e-05, 1.3707e-05, 1.3669e-05,\n", "        1.3630e-05, 1.3592e-05, 1.3554e-05, 1.3516e-05, 1.3478e-05, 1.3440e-05,\n", "        1.3402e-05, 1.3365e-05, 1.3327e-05, 1.3290e-05, 1.3253e-05, 1.3215e-05,\n", "        1.3178e-05, 1.3141e-05, 1.3104e-05, 1.3068e-05, 1.3031e-05, 1.2994e-05,\n", "        1.2958e-05, 1.2922e-05, 1.2885e-05, 1.2849e-05, 1.2813e-05, 1.2777e-05,\n", "        1.2741e-05, 1.2705e-05, 1.2670e-05, 1.2634e-05, 1.2599e-05, 1.2563e-05,\n", "        1.2528e-05, 1.2493e-05, 1.2458e-05, 1.2423e-05, 1.2388e-05, 1.2353e-05,\n", "        1.2319e-05, 1.2284e-05, 1.2250e-05, 1.2215e-05, 1.2181e-05, 1.2147e-05,\n", "        1.2113e-05, 1.2079e-05, 1.2045e-05, 1.2011e-05, 1.1977e-05, 1.1944e-05,\n", "        1.1910e-05, 1.1877e-05, 1.1843e-05, 1.1810e-05, 1.1777e-05, 1.1744e-05,\n", "        1.1711e-05, 1.1678e-05, 1.1645e-05, 1.1613e-05, 1.1580e-05, 1.1547e-05,\n", "        1.1515e-05, 1.1483e-05, 1.1450e-05, 1.1418e-05, 1.1386e-05, 1.1354e-05,\n", "        1.1322e-05, 1.1291e-05, 1.1259e-05, 1.1227e-05, 1.1196e-05, 1.1164e-05,\n", "        1.1133e-05, 1.1102e-05, 1.1071e-05, 1.1040e-05, 1.1009e-05, 1.0978e-05,\n", "        1.0947e-05, 1.0916e-05, 1.0886e-05, 1.0855e-05, 1.0825e-05, 1.0794e-05,\n", "        1.0764e-05, 1.0734e-05, 1.0704e-05, 1.0673e-05, 1.0644e-05, 1.0614e-05,\n", "        1.0584e-05, 1.0554e-05, 1.0525e-05, 1.0495e-05, 1.0466e-05, 1.0436e-05,\n", "        1.0407e-05, 1.0378e-05, 1.0349e-05, 1.0319e-05, 1.0291e-05, 1.0262e-05,\n", "        1.0233e-05, 1.0204e-05, 1.0175e-05, 1.0147e-05, 1.0118e-05, 1.0090e-05,\n", "        1.0062e-05, 1.0033e-05, 1.0005e-05, 9.9772e-06, 9.9492e-06, 9.9213e-06,\n", "        9.8934e-06, 9.8657e-06, 9.8380e-06, 9.8104e-06, 9.7828e-06, 9.7554e-06,\n", "        9.7280e-06, 9.7007e-06, 9.6735e-06, 9.6463e-06, 9.6192e-06, 9.5922e-06,\n", "        9.5653e-06, 9.5385e-06, 9.5117e-06, 9.4850e-06, 9.4584e-06, 9.4318e-06,\n", "        9.4053e-06, 9.3789e-06, 9.3526e-06, 9.3264e-06, 9.3002e-06, 9.2741e-06,\n", "        9.2481e-06, 9.2221e-06, 9.1962e-06, 9.1704e-06, 9.1447e-06, 9.1190e-06,\n", "        9.0934e-06, 9.0679e-06, 9.0424e-06, 9.0170e-06, 8.9917e-06, 8.9665e-06,\n", "        8.9413e-06, 8.9162e-06, 8.8912e-06, 8.8663e-06, 8.8414e-06, 8.8166e-06,\n", "        8.7918e-06, 8.7671e-06, 8.7425e-06, 8.7180e-06, 8.6935e-06, 8.6691e-06,\n", "        8.6448e-06, 8.6205e-06, 8.5963e-06, 8.5722e-06, 8.5481e-06, 8.5241e-06,\n", "        8.5002e-06, 8.4763e-06, 8.4526e-06, 8.4288e-06, 8.4052e-06, 8.3816e-06,\n", "        8.3581e-06, 8.3346e-06, 8.3112e-06, 8.2879e-06, 8.2646e-06, 8.2414e-06,\n", "        8.2183e-06, 8.1952e-06, 8.1722e-06, 8.1493e-06, 8.1264e-06, 8.1036e-06,\n", "        8.0808e-06, 8.0582e-06, 8.0355e-06, 8.0130e-06, 7.9905e-06, 7.9681e-06,\n", "        7.9457e-06, 7.9234e-06, 7.9012e-06, 7.8790e-06, 7.8569e-06, 7.8348e-06,\n", "        7.8128e-06, 7.7909e-06, 7.7690e-06, 7.7472e-06, 7.7255e-06, 7.7038e-06,\n", "        7.6822e-06, 7.6606e-06, 7.6391e-06, 7.6177e-06, 7.5963e-06, 7.5750e-06,\n", "        7.5537e-06, 7.5325e-06, 7.5114e-06, 7.4903e-06, 7.4693e-06, 7.4483e-06,\n", "        7.4274e-06, 7.4065e-06, 7.3857e-06, 7.3650e-06, 7.3443e-06, 7.3237e-06,\n", "        7.3032e-06, 7.2827e-06, 7.2622e-06, 7.2418e-06, 7.2215e-06, 7.2013e-06,\n", "        7.1810e-06, 7.1609e-06, 7.1408e-06, 7.1207e-06, 7.1008e-06, 7.0808e-06,\n", "        7.0610e-06, 7.0411e-06, 7.0214e-06, 7.0017e-06, 6.9820e-06, 6.9624e-06,\n", "        6.9429e-06, 6.9234e-06, 6.9039e-06, 6.8846e-06, 6.8652e-06, 6.8460e-06,\n", "        6.8268e-06, 6.8076e-06, 6.7885e-06, 6.7694e-06, 6.7504e-06, 6.7315e-06,\n", "        6.7126e-06, 6.6938e-06, 6.6750e-06, 6.6562e-06, 6.6375e-06, 6.6189e-06,\n", "        6.6003e-06, 6.5818e-06, 6.5633e-06, 6.5449e-06, 6.5265e-06, 6.5082e-06,\n", "        6.4900e-06, 6.4717e-06, 6.4536e-06, 6.4355e-06, 6.4174e-06, 6.3994e-06,\n", "        6.3814e-06, 6.3635e-06, 6.3457e-06, 6.3278e-06, 6.3101e-06, 6.2924e-06,\n", "        6.2747e-06, 6.2571e-06, 6.2395e-06, 6.2220e-06, 6.2046e-06, 6.1871e-06,\n", "        6.1698e-06, 6.1525e-06, 6.1352e-06, 6.1180e-06, 6.1008e-06, 6.0837e-06,\n", "        6.0666e-06, 6.0496e-06, 6.0326e-06, 6.0157e-06, 5.9988e-06, 5.9819e-06,\n", "        5.9651e-06, 5.9484e-06, 5.9317e-06, 5.9151e-06, 5.8985e-06, 5.8819e-06,\n", "        5.8654e-06, 5.8489e-06, 5.8325e-06, 5.8161e-06, 5.7998e-06, 5.7835e-06,\n", "        5.7673e-06, 5.7511e-06, 5.7350e-06, 5.7189e-06, 5.7028e-06, 5.6868e-06,\n", "        5.6709e-06, 5.6549e-06, 5.6391e-06, 5.6232e-06, 5.6075e-06, 5.5917e-06,\n", "        5.5760e-06, 5.5604e-06, 5.5448e-06, 5.5292e-06, 5.5137e-06, 5.4982e-06,\n", "        5.4828e-06, 5.4674e-06, 5.4520e-06, 5.4367e-06, 5.4215e-06, 5.4063e-06,\n", "        5.3911e-06, 5.3759e-06, 5.3609e-06, 5.3458e-06, 5.3308e-06, 5.3158e-06,\n", "        5.3009e-06, 5.2860e-06, 5.2712e-06, 5.2564e-06, 5.2417e-06, 5.2269e-06,\n", "        5.2123e-06, 5.1976e-06, 5.1831e-06, 5.1685e-06, 5.1540e-06, 5.1395e-06,\n", "        5.1251e-06, 5.1107e-06, 5.0964e-06, 5.0821e-06, 5.0678e-06, 5.0536e-06,\n", "        5.0394e-06, 5.0253e-06, 5.0111e-06, 4.9971e-06, 4.9831e-06, 4.9691e-06,\n", "        4.9551e-06, 4.9412e-06, 4.9273e-06, 4.9135e-06, 4.8997e-06, 4.8860e-06,\n", "        4.8723e-06, 4.8586e-06, 4.8449e-06, 4.8313e-06, 4.8178e-06, 4.8043e-06,\n", "        4.7908e-06, 4.7773e-06, 4.7639e-06, 4.7506e-06, 4.7372e-06, 4.7239e-06,\n", "        4.7107e-06, 4.6974e-06, 4.6843e-06, 4.6711e-06, 4.6580e-06, 4.6449e-06,\n", "        4.6319e-06, 4.6189e-06, 4.6059e-06, 4.5930e-06, 4.5801e-06, 4.5672e-06,\n", "        4.5544e-06, 4.5416e-06, 4.5289e-06, 4.5162e-06, 4.5035e-06, 4.4909e-06,\n", "        4.4783e-06, 4.4657e-06, 4.4532e-06, 4.4407e-06, 4.4282e-06, 4.4158e-06,\n", "        4.4034e-06, 4.3910e-06, 4.3787e-06, 4.3664e-06, 4.3541e-06, 4.3419e-06,\n", "        4.3297e-06, 4.3176e-06, 4.3055e-06, 4.2934e-06, 4.2813e-06, 4.2693e-06,\n", "        4.2573e-06, 4.2454e-06, 4.2335e-06, 4.2216e-06, 4.2097e-06, 4.1979e-06,\n", "        4.1861e-06, 4.1744e-06, 4.1627e-06, 4.1510e-06, 4.1393e-06, 4.1277e-06,\n", "        4.1161e-06, 4.1046e-06, 4.0930e-06, 4.0816e-06, 4.0701e-06, 4.0587e-06,\n", "        4.0473e-06, 4.0359e-06, 4.0246e-06, 4.0133e-06, 4.0020e-06, 3.9908e-06,\n", "        3.9796e-06, 3.9684e-06, 3.9573e-06, 3.9462e-06, 3.9351e-06, 3.9241e-06,\n", "        3.9131e-06, 3.9021e-06, 3.8911e-06, 3.8802e-06, 3.8693e-06, 3.8584e-06,\n", "        3.8476e-06, 3.8368e-06, 3.8260e-06, 3.8153e-06, 3.8046e-06, 3.7939e-06,\n", "        3.7833e-06, 3.7726e-06, 3.7621e-06, 3.7515e-06, 3.7410e-06, 3.7305e-06,\n", "        3.7200e-06, 3.7096e-06, 3.6991e-06, 3.6888e-06, 3.6784e-06, 3.6681e-06,\n", "        3.6578e-06, 3.6475e-06, 3.6373e-06, 3.6271e-06, 3.6169e-06, 3.6067e-06,\n", "        3.5966e-06, 3.5865e-06, 3.5765e-06, 3.5664e-06, 3.5564e-06, 3.5464e-06,\n", "        3.5365e-06, 3.5265e-06, 3.5166e-06, 3.5068e-06, 3.4969e-06, 3.4871e-06,\n", "        3.4773e-06, 3.4676e-06, 3.4578e-06, 3.4481e-06, 3.4385e-06, 3.4288e-06,\n", "        3.4192e-06, 3.4096e-06, 3.4000e-06, 3.3905e-06, 3.3810e-06, 3.3715e-06,\n", "        3.3620e-06, 3.3526e-06, 3.3432e-06, 3.3338e-06, 3.3244e-06, 3.3151e-06,\n", "        3.3058e-06, 3.2965e-06, 3.2872e-06, 3.2780e-06, 3.2688e-06, 3.2596e-06,\n", "        3.2505e-06, 3.2414e-06, 3.2323e-06, 3.2232e-06, 3.2142e-06, 3.2051e-06,\n", "        3.1961e-06, 3.1872e-06, 3.1782e-06, 3.1693e-06, 3.1604e-06, 3.1515e-06,\n", "        3.1427e-06, 3.1339e-06, 3.1251e-06, 3.1163e-06, 3.1075e-06, 3.0988e-06,\n", "        3.0901e-06, 3.0815e-06, 3.0728e-06, 3.0642e-06, 3.0556e-06, 3.0470e-06,\n", "        3.0385e-06, 3.0299e-06, 3.0214e-06, 3.0129e-06, 3.0045e-06, 2.9960e-06,\n", "        2.9876e-06, 2.9793e-06, 2.9709e-06, 2.9626e-06, 2.9542e-06, 2.9459e-06,\n", "        2.9377e-06, 2.9294e-06, 2.9212e-06, 2.9130e-06, 2.9048e-06, 2.8967e-06,\n", "        2.8885e-06, 2.8804e-06, 2.8724e-06, 2.8643e-06, 2.8563e-06, 2.8482e-06,\n", "        2.8402e-06, 2.8323e-06, 2.8243e-06, 2.8164e-06, 2.8085e-06, 2.8006e-06,\n", "        2.7927e-06, 2.7849e-06, 2.7771e-06, 2.7693e-06, 2.7615e-06, 2.7538e-06,\n", "        2.7460e-06, 2.7383e-06, 2.7306e-06, 2.7230e-06, 2.7153e-06, 2.7077e-06,\n", "        2.7001e-06, 2.6925e-06, 2.6850e-06, 2.6774e-06, 2.6699e-06, 2.6624e-06,\n", "        2.6550e-06, 2.6475e-06, 2.6401e-06, 2.6327e-06, 2.6253e-06, 2.6179e-06,\n", "        2.6106e-06, 2.6032e-06, 2.5959e-06, 2.5886e-06, 2.5814e-06, 2.5741e-06,\n", "        2.5669e-06, 2.5597e-06, 2.5525e-06, 2.5454e-06, 2.5382e-06, 2.5311e-06,\n", "        2.5240e-06, 2.5169e-06, 2.5098e-06, 2.5028e-06, 2.4958e-06, 2.4888e-06,\n", "        2.4818e-06, 2.4748e-06, 2.4679e-06, 2.4609e-06, 2.4540e-06, 2.4471e-06,\n", "        2.4403e-06, 2.4334e-06, 2.4266e-06, 2.4198e-06, 2.4130e-06, 2.4062e-06,\n", "        2.3995e-06, 2.3927e-06, 2.3860e-06, 2.3793e-06, 2.3726e-06, 2.3660e-06,\n", "        2.3593e-06, 2.3527e-06, 2.3461e-06, 2.3395e-06, 2.3330e-06, 2.3264e-06,\n", "        2.3199e-06, 2.3134e-06, 2.3069e-06, 2.3004e-06, 2.2939e-06, 2.2875e-06,\n", "        2.2811e-06, 2.2747e-06, 2.2683e-06, 2.2619e-06, 2.2556e-06, 2.2492e-06,\n", "        2.2429e-06, 2.2366e-06, 2.2304e-06, 2.2241e-06, 2.2179e-06, 2.2116e-06,\n", "        2.2054e-06, 2.1992e-06, 2.1931e-06, 2.1869e-06, 2.1808e-06, 2.1746e-06,\n", "        2.1685e-06, 2.1625e-06, 2.1564e-06, 2.1503e-06, 2.1443e-06, 2.1383e-06,\n", "        2.1323e-06, 2.1263e-06, 2.1203e-06, 2.1144e-06, 2.1084e-06, 2.1025e-06,\n", "        2.0966e-06, 2.0907e-06, 2.0849e-06, 2.0790e-06, 2.0732e-06, 2.0674e-06,\n", "        2.0616e-06, 2.0558e-06, 2.0500e-06, 2.0442e-06, 2.0385e-06, 2.0328e-06,\n", "        2.0271e-06, 2.0214e-06, 2.0157e-06, 2.0101e-06, 2.0044e-06, 1.9988e-06,\n", "        1.9932e-06, 1.9876e-06, 1.9820e-06, 1.9764e-06, 1.9709e-06, 1.9654e-06,\n", "        1.9598e-06, 1.9543e-06, 1.9489e-06, 1.9434e-06, 1.9379e-06, 1.9325e-06,\n", "        1.9271e-06, 1.9217e-06, 1.9163e-06, 1.9109e-06, 1.9055e-06, 1.9002e-06,\n", "        1.8948e-06, 1.8895e-06, 1.8842e-06, 1.8789e-06, 1.8737e-06, 1.8684e-06,\n", "        1.8632e-06, 1.8579e-06, 1.8527e-06, 1.8475e-06, 1.8423e-06, 1.8372e-06,\n", "        1.8320e-06, 1.8269e-06, 1.8217e-06, 1.8166e-06, 1.8115e-06, 1.8064e-06,\n", "        1.8014e-06, 1.7963e-06, 1.7913e-06, 1.7862e-06, 1.7812e-06, 1.7762e-06,\n", "        1.7712e-06, 1.7663e-06, 1.7613e-06, 1.7564e-06, 1.7514e-06, 1.7465e-06,\n", "        1.7416e-06, 1.7367e-06, 1.7319e-06, 1.7270e-06, 1.7221e-06, 1.7173e-06,\n", "        1.7125e-06, 1.7077e-06, 1.7029e-06, 1.6981e-06, 1.6933e-06, 1.6886e-06,\n", "        1.6839e-06, 1.6791e-06, 1.6744e-06, 1.6697e-06, 1.6650e-06, 1.6604e-06,\n", "        1.6557e-06, 1.6510e-06, 1.6464e-06, 1.6418e-06, 1.6372e-06, 1.6326e-06,\n", "        1.6280e-06, 1.6234e-06, 1.6189e-06, 1.6143e-06, 1.6098e-06, 1.6053e-06,\n", "        1.6008e-06, 1.5963e-06, 1.5918e-06, 1.5873e-06, 1.5829e-06, 1.5784e-06,\n", "        1.5740e-06, 1.5696e-06, 1.5652e-06, 1.5608e-06, 1.5564e-06, 1.5520e-06,\n", "        1.5477e-06, 1.5433e-06, 1.5390e-06, 1.5347e-06, 1.5304e-06, 1.5261e-06,\n", "        1.5218e-06, 1.5175e-06, 1.5133e-06, 1.5090e-06, 1.5048e-06, 1.5006e-06,\n", "        1.4964e-06, 1.4922e-06, 1.4880e-06, 1.4838e-06, 1.4796e-06, 1.4755e-06,\n", "        1.4713e-06, 1.4672e-06, 1.4631e-06, 1.4590e-06, 1.4549e-06, 1.4508e-06,\n", "        1.4467e-06, 1.4427e-06, 1.4386e-06, 1.4346e-06, 1.4306e-06, 1.4265e-06,\n", "        1.4225e-06, 1.4185e-06, 1.4146e-06, 1.4106e-06, 1.4066e-06, 1.4027e-06,\n", "        1.3987e-06, 1.3948e-06, 1.3909e-06, 1.3870e-06, 1.3831e-06, 1.3792e-06,\n", "        1.3754e-06, 1.3715e-06, 1.3676e-06, 1.3638e-06, 1.3600e-06, 1.3562e-06,\n", "        1.3524e-06, 1.3486e-06, 1.3448e-06, 1.3410e-06, 1.3372e-06, 1.3335e-06,\n", "        1.3297e-06, 1.3260e-06, 1.3223e-06, 1.3186e-06, 1.3149e-06, 1.3112e-06,\n", "        1.3075e-06, 1.3038e-06, 1.3002e-06, 1.2965e-06, 1.2929e-06, 1.2893e-06,\n", "        1.2856e-06, 1.2820e-06, 1.2784e-06, 1.2748e-06, 1.2713e-06, 1.2677e-06,\n", "        1.2641e-06, 1.2606e-06, 1.2570e-06, 1.2535e-06], device='cuda:0')"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["yarn_outs[\"inter_freqs\"]"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00, 1.0000e+00,\n", "         1.0000e+00, 9.9915e-01, 9.9541e-01, 9.9168e-01, 9.8796e-01, 9.8425e-01,\n", "         9.8055e-01, 9.7686e-01, 9.7319e-01, 9.6952e-01, 9.6586e-01, 9.6222e-01,\n", "         9.5858e-01, 9.5495e-01, 9.5134e-01, 9.4773e-01, 9.4414e-01, 9.4055e-01,\n", "         9.3697e-01, 9.3341e-01, 9.2985e-01, 9.2631e-01, 9.2277e-01, 9.1925e-01,\n", "         9.1573e-01, 9.1223e-01, 9.0873e-01, 9.0524e-01, 9.0177e-01, 8.9830e-01,\n", "         8.9484e-01, 8.9140e-01, 8.8796e-01, 8.8453e-01, 8.8111e-01, 8.7770e-01,\n", "         8.7430e-01, 8.7091e-01, 8.6753e-01, 8.6416e-01, 8.6080e-01, 8.5745e-01,\n", "         8.5411e-01, 8.5078e-01, 8.4745e-01, 8.4414e-01, 8.4083e-01, 8.3754e-01,\n", "         8.3425e-01, 8.3097e-01, 8.2771e-01, 8.2445e-01, 8.2120e-01, 8.1796e-01,\n", "         8.1472e-01, 8.1150e-01, 8.0829e-01, 8.0508e-01, 8.0189e-01, 7.9870e-01,\n", "         7.9553e-01, 7.9236e-01, 7.8920e-01, 7.8605e-01, 7.8290e-01, 7.7977e-01,\n", "         7.7665e-01, 7.7353e-01, 7.7042e-01, 7.6733e-01, 7.6424e-01, 7.6116e-01,\n", "         7.5808e-01, 7.5502e-01, 7.5197e-01, 7.4892e-01, 7.4588e-01, 7.4285e-01,\n", "         7.3983e-01, 7.3682e-01, 7.3382e-01, 7.3082e-01, 7.2783e-01, 7.2486e-01,\n", "         7.2189e-01, 7.1892e-01, 7.1597e-01, 7.1303e-01, 7.1009e-01, 7.0716e-01,\n", "         7.0424e-01, 7.0133e-01, 6.9842e-01, 6.9553e-01, 6.9264e-01, 6.8976e-01,\n", "         6.8689e-01, 6.8402e-01, 6.8117e-01, 6.7832e-01, 6.7548e-01, 6.7265e-01,\n", "         6.6983e-01, 6.6701e-01, 6.6420e-01, 6.6140e-01, 6.5861e-01, 6.5583e-01,\n", "         6.5305e-01, 6.5028e-01, 6.4752e-01, 6.4477e-01, 6.4202e-01, 6.3928e-01,\n", "         6.3655e-01, 6.3383e-01, 6.3112e-01, 6.2841e-01, 6.2571e-01, 6.2302e-01,\n", "         6.2034e-01, 6.1766e-01, 6.1499e-01, 6.1233e-01, 6.0967e-01, 6.0703e-01,\n", "         6.0439e-01, 6.0175e-01, 5.9913e-01, 5.9651e-01, 5.9390e-01, 5.9130e-01,\n", "         5.8871e-01, 5.8612e-01, 5.8354e-01, 5.8096e-01, 5.7840e-01, 5.7584e-01,\n", "         5.7329e-01, 5.7074e-01, 5.6820e-01, 5.6567e-01, 5.6315e-01, 5.6063e-01,\n", "         5.5812e-01, 5.5562e-01, 5.5313e-01, 5.5064e-01, 5.4816e-01, 5.4568e-01,\n", "         5.4322e-01, 5.4076e-01, 5.3830e-01, 5.3586e-01, 5.3342e-01, 5.3098e-01,\n", "         5.2856e-01, 5.2614e-01, 5.2373e-01, 5.2132e-01, 5.1892e-01, 5.1653e-01,\n", "         5.1414e-01, 5.1176e-01, 5.0939e-01, 5.0703e-01, 5.0467e-01, 5.0232e-01,\n", "         4.9997e-01, 4.9763e-01, 4.9530e-01, 4.9297e-01, 4.9065e-01, 4.8834e-01,\n", "         4.8604e-01, 4.8374e-01, 4.8144e-01, 4.7916e-01, 4.7687e-01, 4.7460e-01,\n", "         4.7233e-01, 4.7007e-01, 4.6782e-01, 4.6557e-01, 4.6333e-01, 4.6109e-01,\n", "         4.5886e-01, 4.5664e-01, 4.5442e-01, 4.5221e-01, 4.5000e-01, 4.4780e-01,\n", "         4.4561e-01, 4.4343e-01, 4.4125e-01, 4.3907e-01, 4.3690e-01, 4.3474e-01,\n", "         4.3259e-01, 4.3044e-01, 4.2829e-01, 4.2615e-01, 4.2402e-01, 4.2190e-01,\n", "         4.1978e-01, 4.1766e-01, 4.1555e-01, 4.1345e-01, 4.1136e-01, 4.0927e-01,\n", "         4.0718e-01, 4.0510e-01, 4.0303e-01, 4.0096e-01, 3.9890e-01, 3.9685e-01,\n", "         3.9480e-01, 3.9275e-01, 3.9072e-01, 3.8868e-01, 3.8666e-01, 3.8464e-01,\n", "         3.8262e-01, 3.8061e-01, 3.7861e-01, 3.7661e-01, 3.7462e-01, 3.7263e-01,\n", "         3.7065e-01, 3.6867e-01, 3.6670e-01, 3.6474e-01, 3.6278e-01, 3.6082e-01,\n", "         3.5888e-01, 3.5693e-01, 3.5500e-01, 3.5306e-01, 3.5114e-01, 3.4922e-01,\n", "         3.4730e-01, 3.4539e-01, 3.4348e-01, 3.4158e-01, 3.3969e-01, 3.3780e-01,\n", "         3.3592e-01, 3.3404e-01, 3.3217e-01, 3.3030e-01, 3.2844e-01, 3.2658e-01,\n", "         3.2473e-01, 3.2288e-01, 3.2104e-01, 3.1920e-01, 3.1737e-01, 3.1554e-01,\n", "         3.1372e-01, 3.1190e-01, 3.1009e-01, 3.0829e-01, 3.0649e-01, 3.0469e-01,\n", "         3.0290e-01, 3.0111e-01, 2.9933e-01, 2.9756e-01, 2.9579e-01, 2.9402e-01,\n", "         2.9226e-01, 2.9050e-01, 2.8875e-01, 2.8701e-01, 2.8527e-01, 2.8353e-01,\n", "         2.8180e-01, 2.8007e-01, 2.7835e-01, 2.7663e-01, 2.7492e-01, 2.7321e-01,\n", "         2.7151e-01, 2.6981e-01, 2.6812e-01, 2.6643e-01, 2.6475e-01, 2.6307e-01,\n", "         2.6140e-01, 2.5973e-01, 2.5806e-01, 2.5640e-01, 2.5475e-01, 2.5310e-01,\n", "         2.5145e-01, 2.4981e-01, 2.4817e-01, 2.4654e-01, 2.4491e-01, 2.4329e-01,\n", "         2.4167e-01, 2.4006e-01, 2.3845e-01, 2.3684e-01, 2.3524e-01, 2.3365e-01,\n", "         2.3205e-01, 2.3047e-01, 2.2889e-01, 2.2731e-01, 2.2573e-01, 2.2416e-01,\n", "         2.2260e-01, 2.2104e-01, 2.1948e-01, 2.1793e-01, 2.1638e-01, 2.1484e-01,\n", "         2.1330e-01, 2.1177e-01, 2.1024e-01, 2.0871e-01, 2.0719e-01, 2.0567e-01,\n", "         2.0416e-01, 2.0265e-01, 2.0115e-01, 1.9965e-01, 1.9815e-01, 1.9666e-01,\n", "         1.9517e-01, 1.9369e-01, 1.9221e-01, 1.9073e-01, 1.8926e-01, 1.8780e-01,\n", "         1.8633e-01, 1.8488e-01, 1.8342e-01, 1.8197e-01, 1.8052e-01, 1.7908e-01,\n", "         1.7764e-01, 1.7621e-01, 1.7478e-01, 1.7335e-01, 1.7193e-01, 1.7051e-01,\n", "         1.6910e-01, 1.6769e-01, 1.6628e-01, 1.6488e-01, 1.6348e-01, 1.6209e-01,\n", "         1.6070e-01, 1.5931e-01, 1.5793e-01, 1.5655e-01, 1.5517e-01, 1.5380e-01,\n", "         1.5243e-01, 1.5107e-01, 1.4971e-01, 1.4836e-01, 1.4700e-01, 1.4566e-01,\n", "         1.4431e-01, 1.4297e-01, 1.4163e-01, 1.4030e-01, 1.3897e-01, 1.3764e-01,\n", "         1.3632e-01, 1.3500e-01, 1.3369e-01, 1.3238e-01, 1.3107e-01, 1.2977e-01,\n", "         1.2847e-01, 1.2717e-01, 1.2588e-01, 1.2459e-01, 1.2331e-01, 1.2202e-01,\n", "         1.2075e-01, 1.1947e-01, 1.1820e-01, 1.1693e-01, 1.1567e-01, 1.1441e-01,\n", "         1.1315e-01, 1.1190e-01, 1.1065e-01, 1.0940e-01, 1.0816e-01, 1.0692e-01,\n", "         1.0569e-01, 1.0445e-01, 1.0322e-01, 1.0200e-01, 1.0078e-01, 9.9559e-02,\n", "         9.8344e-02, 9.7132e-02, 9.5924e-02, 9.4719e-02, 9.3518e-02, 9.2320e-02,\n", "         9.1125e-02, 8.9933e-02, 8.8745e-02, 8.7561e-02, 8.6379e-02, 8.5201e-02,\n", "         8.4027e-02, 8.2855e-02, 8.1687e-02, 8.0522e-02, 7.9360e-02, 7.8202e-02,\n", "         7.7047e-02, 7.5895e-02, 7.4746e-02, 7.3601e-02, 7.2459e-02, 7.1320e-02,\n", "         7.0184e-02, 6.9051e-02, 6.7922e-02, 6.6796e-02, 6.5673e-02, 6.4553e-02,\n", "         6.3436e-02, 6.2322e-02, 6.1212e-02, 6.0104e-02, 5.9000e-02, 5.7899e-02,\n", "         5.6801e-02, 5.5706e-02, 5.4614e-02, 5.3525e-02, 5.2439e-02, 5.1356e-02,\n", "         5.0276e-02, 4.9200e-02, 4.8126e-02, 4.7055e-02, 4.5988e-02, 4.4923e-02,\n", "         4.3861e-02, 4.2802e-02, 4.1747e-02, 4.0694e-02, 3.9644e-02, 3.8597e-02,\n", "         3.7553e-02, 3.6512e-02, 3.5474e-02, 3.4439e-02, 3.3407e-02, 3.2377e-02,\n", "         3.1351e-02, 3.0327e-02, 2.9307e-02, 2.8289e-02, 2.7274e-02, 2.6261e-02,\n", "         2.5252e-02, 2.4246e-02, 2.3242e-02, 2.2241e-02, 2.1243e-02, 2.0248e-02,\n", "         1.9255e-02, 1.8266e-02, 1.7279e-02, 1.6295e-02, 1.5313e-02, 1.4335e-02,\n", "         1.3359e-02, 1.2386e-02, 1.1416e-02, 1.0448e-02, 9.4829e-03, 8.5208e-03,\n", "         7.5612e-03, 6.6044e-03, 5.6502e-03, 4.6988e-03, 3.7500e-03, 2.8039e-03,\n", "         1.8603e-03, 9.1950e-04, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00,\n", "         0.0000e+00, 0.0000e+00, 0.0000e+00, 0.0000e+00], device='cuda:0'),\n", " tensor([1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "         1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 0.9980, 0.9960,\n", "         0.9939, 0.9919, 0.9899, 0.9879, 0.9858, 0.9838, 0.9818, 0.9798, 0.9777,\n", "         0.9757, 0.9737, 0.9717, 0.9696, 0.9676, 0.9656, 0.9636, 0.9615, 0.9595,\n", "         0.9575, 0.9555, 0.9534, 0.9514, 0.9494, 0.9474, 0.9453, 0.9433, 0.9413,\n", "         0.9393, 0.9372, 0.9352, 0.9332, 0.9312, 0.9291, 0.9271, 0.9251, 0.9231,\n", "         0.9211, 0.9190, 0.9170, 0.9150, 0.9130, 0.9109, 0.9089, 0.9069, 0.9049,\n", "         0.9028, 0.9008, 0.8988, 0.8968, 0.8947, 0.8927, 0.8907, 0.8887, 0.8866,\n", "         0.8846, 0.8826, 0.8806, 0.8785, 0.8765, 0.8745, 0.8725, 0.8704, 0.8684,\n", "         0.8664, 0.8644, 0.8623, 0.8603, 0.8583, 0.8563, 0.8543, 0.8522, 0.8502,\n", "         0.8482, 0.8462, 0.8441, 0.8421, 0.8401, 0.8381, 0.8360, 0.8340, 0.8320,\n", "         0.8300, 0.8279, 0.8259, 0.8239, 0.8219, 0.8198, 0.8178, 0.8158, 0.8138,\n", "         0.8117, 0.8097, 0.8077, 0.8057, 0.8036, 0.8016, 0.7996, 0.7976, 0.7955,\n", "         0.7935, 0.7915, 0.7895, 0.7874, 0.7854, 0.7834, 0.7814, 0.7794, 0.7773,\n", "         0.7753, 0.7733, 0.7713, 0.7692, 0.7672, 0.7652, 0.7632, 0.7611, 0.7591,\n", "         0.7571, 0.7551, 0.7530, 0.7510, 0.7490, 0.7470, 0.7449, 0.7429, 0.7409,\n", "         0.7389, 0.7368, 0.7348, 0.7328, 0.7308, 0.7287, 0.7267, 0.7247, 0.7227,\n", "         0.7206, 0.7186, 0.7166, 0.7146, 0.7126, 0.7105, 0.7085, 0.7065, 0.7045,\n", "         0.7024, 0.7004, 0.6984, 0.6964, 0.6943, 0.6923, 0.6903, 0.6883, 0.6862,\n", "         0.6842, 0.6822, 0.6802, 0.6781, 0.6761, 0.6741, 0.6721, 0.6700, 0.6680,\n", "         0.6660, 0.6640, 0.6619, 0.6599, 0.6579, 0.6559, 0.6538, 0.6518, 0.6498,\n", "         0.6478, 0.6457, 0.6437, 0.6417, 0.6397, 0.6377, 0.6356, 0.6336, 0.6316,\n", "         0.6296, 0.6275, 0.6255, 0.6235, 0.6215, 0.6194, 0.6174, 0.6154, 0.6134,\n", "         0.6113, 0.6093, 0.6073, 0.6053, 0.6032, 0.6012, 0.5992, 0.5972, 0.5951,\n", "         0.5931, 0.5911, 0.5891, 0.5870, 0.5850, 0.5830, 0.5810, 0.5789, 0.5769,\n", "         0.5749, 0.5729, 0.5709, 0.5688, 0.5668, 0.5648, 0.5628, 0.5607, 0.5587,\n", "         0.5567, 0.5547, 0.5526, 0.5506, 0.5486, 0.5466, 0.5445, 0.5425, 0.5405,\n", "         0.5385, 0.5364, 0.5344, 0.5324, 0.5304, 0.5283, 0.5263, 0.5243, 0.5223,\n", "         0.5202, 0.5182, 0.5162, 0.5142, 0.5121, 0.5101, 0.5081, 0.5061, 0.5040,\n", "         0.5020, 0.5000, 0.4980, 0.4960, 0.4939, 0.4919, 0.4899, 0.4879, 0.4858,\n", "         0.4838, 0.4818, 0.4798, 0.4777, 0.4757, 0.4737, 0.4717, 0.4696, 0.4676,\n", "         0.4656, 0.4636, 0.4615, 0.4595, 0.4575, 0.4555, 0.4534, 0.4514, 0.4494,\n", "         0.4474, 0.4453, 0.4433, 0.4413, 0.4393, 0.4372, 0.4352, 0.4332, 0.4312,\n", "         0.4291, 0.4271, 0.4251, 0.4231, 0.4211, 0.4190, 0.4170, 0.4150, 0.4130,\n", "         0.4109, 0.4089, 0.4069, 0.4049, 0.4028, 0.4008, 0.3988, 0.3968, 0.3947,\n", "         0.3927, 0.3907, 0.3887, 0.3866, 0.3846, 0.3826, 0.3806, 0.3785, 0.3765,\n", "         0.3745, 0.3725, 0.3704, 0.3684, 0.3664, 0.3644, 0.3623, 0.3603, 0.3583,\n", "         0.3563, 0.3543, 0.3522, 0.3502, 0.3482, 0.3462, 0.3441, 0.3421, 0.3401,\n", "         0.3381, 0.3360, 0.3340, 0.3320, 0.3300, 0.3279, 0.3259, 0.3239, 0.3219,\n", "         0.3198, 0.3178, 0.3158, 0.3138, 0.3117, 0.3097, 0.3077, 0.3057, 0.3036,\n", "         0.3016, 0.2996, 0.2976, 0.2955, 0.2935, 0.2915, 0.2895, 0.2874, 0.2854,\n", "         0.2834, 0.2814, 0.2794, 0.2773, 0.2753, 0.2733, 0.2713, 0.2692, 0.2672,\n", "         0.2652, 0.2632, 0.2611, 0.2591, 0.2571, 0.2551, 0.2530, 0.2510, 0.2490,\n", "         0.2470, 0.2449, 0.2429, 0.2409, 0.2389, 0.2368, 0.2348, 0.2328, 0.2308,\n", "         0.2287, 0.2267, 0.2247, 0.2227, 0.2206, 0.2186, 0.2166, 0.2146, 0.2126,\n", "         0.2105, 0.2085, 0.2065, 0.2045, 0.2024, 0.2004, 0.1984, 0.1964, 0.1943,\n", "         0.1923, 0.1903, 0.1883, 0.1862, 0.1842, 0.1822, 0.1802, 0.1781, 0.1761,\n", "         0.1741, 0.1721, 0.1700, 0.1680, 0.1660, 0.1640, 0.1619, 0.1599, 0.1579,\n", "         0.1559, 0.1538, 0.1518, 0.1498, 0.1478, 0.1457, 0.1437, 0.1417, 0.1397,\n", "         0.1377, 0.1356, 0.1336, 0.1316, 0.1296, 0.1275, 0.1255, 0.1235, 0.1215,\n", "         0.1194, 0.1174, 0.1154, 0.1134, 0.1113, 0.1093, 0.1073, 0.1053, 0.1032,\n", "         0.1012, 0.0992, 0.0972, 0.0951, 0.0931, 0.0911, 0.0891, 0.0870, 0.0850,\n", "         0.0830, 0.0810, 0.0789, 0.0769, 0.0749, 0.0729, 0.0709, 0.0688, 0.0668,\n", "         0.0648, 0.0628, 0.0607, 0.0587, 0.0567, 0.0547, 0.0526, 0.0506, 0.0486,\n", "         0.0466, 0.0445, 0.0425, 0.0405, 0.0385, 0.0364, 0.0344, 0.0324, 0.0304,\n", "         0.0283, 0.0263, 0.0243, 0.0223, 0.0202, 0.0182, 0.0162, 0.0142, 0.0121,\n", "         0.0101, 0.0081, 0.0061, 0.0040, 0.0020, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000,\n", "         0.0000], device='cuda:0'),\n", " tensor([ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0012, -0.0005, -0.0022, -0.0039, -0.0056,\n", "         -0.0073, -0.0090, -0.0106, -0.0123, -0.0139, -0.0155, -0.0171, -0.0187,\n", "         -0.0203, -0.0219, -0.0235, -0.0250, -0.0266, -0.0281, -0.0297, -0.0312,\n", "         -0.0327, -0.0342, -0.0357, -0.0372, -0.0386, -0.0401, -0.0416, -0.0430,\n", "         -0.0444, -0.0459, -0.0473, -0.0487, -0.0501, -0.0514, -0.0528, -0.0542,\n", "         -0.0555, -0.0569, -0.0582, -0.0596, -0.0609, -0.0622, -0.0635, -0.0648,\n", "         -0.0661, -0.0673, -0.0686, -0.0698, -0.0711, -0.0723, -0.0735, -0.0748,\n", "         -0.0760, -0.0772, -0.0784, -0.0795, -0.0807, -0.0819, -0.0830, -0.0842,\n", "         -0.0853, -0.0864, -0.0875, -0.0886, -0.0897, -0.0908, -0.0919, -0.0930,\n", "         -0.0941, -0.0951, -0.0962, -0.0972, -0.0982, -0.0993, -0.1003, -0.1013,\n", "         -0.1023, -0.1033, -0.1042, -0.1052, -0.1062, -0.1071, -0.1081, -0.1090,\n", "         -0.1099, -0.1109, -0.1118, -0.1127, -0.1136, -0.1145, -0.1153, -0.1162,\n", "         -0.1171, -0.1179, -0.1188, -0.1196, -0.1205, -0.1213, -0.1221, -0.1229,\n", "         -0.1237, -0.1245, -0.1253, -0.1260, -0.1268, -0.1276, -0.1283, -0.1291,\n", "         -0.1298, -0.1305, -0.1313, -0.1320, -0.1327, -0.1334, -0.1341, -0.1347,\n", "         -0.1354, -0.1361, -0.1367, -0.1374, -0.1380, -0.1387, -0.1393, -0.1399,\n", "         -0.1406, -0.1412, -0.1418, -0.1424, -0.1429, -0.1435, -0.1441, -0.1447,\n", "         -0.1452, -0.1458, -0.1463, -0.1468, -0.1474, -0.1479, -0.1484, -0.1489,\n", "         -0.1494, -0.1499, -0.1504, -0.1509, -0.1513, -0.1518, -0.1522, -0.1527,\n", "         -0.1531, -0.1536, -0.1540, -0.1544, -0.1548, -0.1553, -0.1557, -0.1560,\n", "         -0.1564, -0.1568, -0.1572, -0.1576, -0.1579, -0.1583, -0.1586, -0.1590,\n", "         -0.1593, -0.1596, -0.1599, -0.1603, -0.1606, -0.1609, -0.1612, -0.1615,\n", "         -0.1617, -0.1620, -0.1623, -0.1625, -0.1628, -0.1631, -0.1633, -0.1635,\n", "         -0.1638, -0.1640, -0.1642, -0.1644, -0.1646, -0.1648, -0.1650, -0.1652,\n", "         -0.1654, -0.1656, -0.1657, -0.1659, -0.1660, -0.1662, -0.1663, -0.1665,\n", "         -0.1666, -0.1667, -0.1669, -0.1670, -0.1671, -0.1672, -0.1673, -0.1674,\n", "         -0.1674, -0.1675, -0.1676, -0.1677, -0.1677, -0.1678, -0.1678, -0.1679,\n", "         -0.1679, -0.1679, -0.1680, -0.1680, -0.1680, -0.1680, -0.1680, -0.1680,\n", "         -0.1680, -0.1680, -0.1680, -0.1679, -0.1679, -0.1679, -0.1678, -0.1678,\n", "         -0.1677, -0.1677, -0.1676, -0.1675, -0.1674, -0.1674, -0.1673, -0.1672,\n", "         -0.1671, -0.1670, -0.1669, -0.1668, -0.1666, -0.1665, -0.1664, -0.1662,\n", "         -0.1661, -0.1660, -0.1658, -0.1657, -0.1655, -0.1653, -0.1652, -0.1650,\n", "         -0.1648, -0.1646, -0.1644, -0.1642, -0.1640, -0.1638, -0.1636, -0.1634,\n", "         -0.1631, -0.1629, -0.1627, -0.1624, -0.1622, -0.1620, -0.1617, -0.1614,\n", "         -0.1612, -0.1609, -0.1606, -0.1604, -0.1601, -0.1598, -0.1595, -0.1592,\n", "         -0.1589, -0.1586, -0.1583, -0.1580, -0.1576, -0.1573, -0.1570, -0.1566,\n", "         -0.1563, -0.1560, -0.1556, -0.1553, -0.1549, -0.1545, -0.1542, -0.1538,\n", "         -0.1534, -0.1530, -0.1526, -0.1522, -0.1518, -0.1514, -0.1510, -0.1506,\n", "         -0.1502, -0.1498, -0.1494, -0.1489, -0.1485, -0.1481, -0.1476, -0.1472,\n", "         -0.1467, -0.1463, -0.1458, -0.1454, -0.1449, -0.1444, -0.1439, -0.1435,\n", "         -0.1430, -0.1425, -0.1420, -0.1415, -0.1410, -0.1405, -0.1400, -0.1395,\n", "         -0.1389, -0.1384, -0.1379, -0.1373, -0.1368, -0.1363, -0.1357, -0.1352,\n", "         -0.1346, -0.1341, -0.1335, -0.1329, -0.1324, -0.1318, -0.1312, -0.1306,\n", "         -0.1300, -0.1295, -0.1289, -0.1283, -0.1277, -0.1271, -0.1264, -0.1258,\n", "         -0.1252, -0.1246, -0.1240, -0.1233, -0.1227, -0.1221, -0.1214, -0.1208,\n", "         -0.1201, -0.1195, -0.1188, -0.1182, -0.1175, -0.1168, -0.1162, -0.1155,\n", "         -0.1148, -0.1141, -0.1134, -0.1127, -0.1120, -0.1113, -0.1106, -0.1099,\n", "         -0.1092, -0.1085, -0.1078, -0.1071, -0.1063, -0.1056, -0.1049, -0.1042,\n", "         -0.1034, -0.1027, -0.1019, -0.1012, -0.1004, -0.0997, -0.0989, -0.0981,\n", "         -0.0974, -0.0966, -0.0958, -0.0951, -0.0943, -0.0935, -0.0927, -0.0919,\n", "         -0.0911, -0.0903, -0.0895, -0.0887, -0.0879, -0.0871, -0.0863, -0.0854,\n", "         -0.0846, -0.0838, -0.0830, -0.0821, -0.0813, -0.0805, -0.0796, -0.0788,\n", "         -0.0779, -0.0771, -0.0762, -0.0753, -0.0745, -0.0736, -0.0728, -0.0719,\n", "         -0.0710, -0.0701, -0.0692, -0.0684, -0.0675, -0.0666, -0.0657, -0.0648,\n", "         -0.0639, -0.0630, -0.0621, -0.0612, -0.0602, -0.0593, -0.0584, -0.0575,\n", "         -0.0566, -0.0556, -0.0547, -0.0538, -0.0528, -0.0519, -0.0509, -0.0500,\n", "         -0.0490, -0.0481, -0.0471, -0.0462, -0.0452, -0.0442, -0.0433, -0.0423,\n", "         -0.0413, -0.0404, -0.0394, -0.0384, -0.0374, -0.0364, -0.0354, -0.0344,\n", "         -0.0334, -0.0324, -0.0314, -0.0304, -0.0294, -0.0284, -0.0274, -0.0264,\n", "         -0.0253, -0.0243, -0.0233, -0.0223, -0.0212, -0.0202, -0.0192, -0.0181,\n", "         -0.0171, -0.0160, -0.0150, -0.0139, -0.0129, -0.0118, -0.0108, -0.0097,\n", "         -0.0086, -0.0076, -0.0065, -0.0054, -0.0043, -0.0033, -0.0022, -0.0011,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000],\n", "        device='cuda:0'))"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["(\n", "    outs[\"pure_freqs\"],\n", "    yarn_outs[\"pure_freqs\"],\n", "    outs[\"pure_freqs\"] - yarn_outs[\"pure_freqs\"],\n", ")"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(outs[\"pure_freqs\"].cpu().numpy(), label=\"LLaMa 3.1 pure freqs\")\n", "plt.plot(yarn_outs[\"pure_freqs\"].cpu().numpy(), label=\"YaRN pure freqs\")\n", "# plt.yscale(\"log\")\n", "plt.xlim(2000, 2600)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(True, device='cuda:0')"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["(outs[\"t\"] == yarn_outs[\"t\"]).all()"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>.<PERSON><PERSON>([65536, 4096])\n", "tensor(2.0000, device='cuda:0')\n"]}], "source": ["cos_diff = cos_yarn - cos_ref\n", "print(cos_diff.size())\n", "print(cos_diff.abs().max())\n", "# print(cos_diff)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>.<PERSON><PERSON>([65536, 4096])\n", "tensor(2.0000, device='cuda:0')\n"]}], "source": ["cos_diff_cpu = cos_yarn - cos_ref_cpu.cuda()\n", "print(cos_diff_cpu.size())\n", "print(cos_diff_cpu.abs().max())\n", "# print(cos_diff_cpu)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>.<PERSON><PERSON>([65536, 4096])\n", "tensor(0.0156, device='cuda:0')\n"]}], "source": ["cpu_cuda_diff = cos_ref_cpu.cuda() - cos_ref\n", "print(cpu_cuda_diff.size())\n", "print(cpu_cuda_diff.abs().max())\n", "# print(cpu_cuda_diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
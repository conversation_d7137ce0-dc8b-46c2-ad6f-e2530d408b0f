import argparse
import subprocess
import tempfile

TEMPLATE = """
system:
    name: remote_chat
    {model_str}

    client:
        url: {url}

    retriever:
        disable_retrieval: {disable_retrieval}

task:
    name: humaneval_instruct
    variant: {variant}

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: instruct_humaneval_{short_name}
  project: zhuoran-eval
  workspace: Dev
"""


def run_eval(
    model_name,
    url,
    short_name,
    variant,
    retrieval,
    wait_for_completion,
    override_summary,
):
    model_str = f"model_name: {model_name}" if model_name else ""
    config_str = TEMPLATE.format(
        model_str=model_str,
        url=url,
        short_name=short_name,
        disable_retrieval=not retrieval,
        variant=variant,
    )

    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        f.write(config_str)
        f.flush()
        cmd = [
            "python",
            "research/eval/eval.py",
            f"--summary_path=/mnt/efs/augment/user/zhuoran/summaries/long_context/llama3.1/{short_name}.json",
            f"{f.name}",
        ]
        if wait_for_completion:
            cmd.append("--wait_for_completion")
        if override_summary:
            cmd.append("--override_summary")
        subprocess.run(" ".join(cmd), shell=True)
        print(" ".join(cmd))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-w", "--wait_for_completion", action="store_true", default=False
    )
    parser.add_argument(
        "-o",
        "--override_summary",
        action="store_true",
        default=False,
    )
    args = parser.parse_args()

    configs = [
        (
            "",
            "https://staging-shard-0.api.augmentcode.com",
            "dogfood",
        ),
        # (
        #     "binks-l3-70B-FP8-ug-chatanol1-16-3-chat",
        #     "https://dev-zhuoran.us-central.api.augmentcode.com",
        #     "8k_ug",
        # ),
        (
            "binks-l3-16k-cl1-18-dcl1-chat",
            "https://staging-shard-0.api.augmentcode.com",
            "16k_v10",
        ),
        (
            "binks-v11",
            "https://dev-zhuoran.us-central.api.augmentcode.com",
            "v10_31_newtok",
        ),
    ]
    variants = [
        "standard",
        "synthetic",
    ]
    for model_name, url, short_name in configs:
        for variant in variants:
            short_variant = "o" if variant == "standard" else "s"
            for repeat in range(3):
                run_eval(
                    model_name,
                    url,
                    f"{short_name}_{short_variant}_r{repeat}",
                    variant,
                    True,
                    args.wait_for_completion,
                    args.override_summary,
                )

import base64
import concurrent.futures
import json

from google.cloud import bigquery, storage

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache
from base.datasets.tenants import DOGFOOD
from base.prompt_format.common import Exchange
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.types import Document

tenant = DOGFOOD
gcp_creds, _ = get_gcp_creds(None)
storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
blob_cache_size_bytes = 2**30
blob_cache_num_threads = 32
blob_cache = GCSBlobCache(
    blob_bucket,
    tenant.blob_bucket_prefix,
    max_size_bytes=blob_cache_size_bytes,
    num_threads=blob_cache_num_threads,
)
checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
checkpoint_cache = GCSCheckpointCache(
    checkpoint_bucket,
    tenant.checkpoint_bucket_prefix,
    blob_cache_size_bytes,
    num_threads=blob_cache_num_threads,
)
bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)


def parse_row(row: bigquery.Row) -> dict:
    request_json = row["request_json"]

    prefix = request_json.get("prefix", "")
    suffix = request_json.get("suffix", "")
    selected_code = request_json.get("selected_code", "")
    prefix_begin = request_json.get("prefix_begin", None)
    suffix_end = request_json.get("suffix_end", None)
    blob_name = request_json.get("blob_name", None)
    blobs = request_json.get("blobs", None)
    user_guided_blobs = request_json.get("user_guided_blobs", None)
    if blobs is not None:
        checkpoint_id = blobs.pop("baseline_checkpoint_id", None)
        if checkpoint_id is None:
            checkpoint_blob_names = set()
        else:
            cached_checkpoint = checkpoint_cache.get([checkpoint_id])[0]
            if cached_checkpoint is None:
                checkpoint_blob_names = set()
            else:
                checkpoint_blob_names = set(
                    checkpoint_cache.get([checkpoint_id])[0].blob_names
                )
        added_blob_names = set(
            [base64.b64decode(blob).hex() for blob in blobs["added"]]
            if "added" in blobs
            else []
        )
        deleted_blob_names = set(
            [base64.b64decode(blob).hex() for blob in blobs["deleted"]]
            if "deleted" in blobs
            else []
        )
        blob_names = sorted(
            list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
        )
        blobs = blob_cache.get(blob_names)

        documents = [
            Document(id=blob_name, text=blob.content, path=str(blob.path))
            for blob_name, blob in zip(blob_names, blobs)
            if blob is not None
        ]

    chat_history = request_json.get("chat_history", [])

    return_dict = {
        "selected_code": selected_code,
        "message": request_json["message"],
        "prefix": prefix,
        "suffix": suffix,
        "path": request_json["path"],
        "blob_name": blob_name,
        "prefix_begin": prefix_begin,
        "suffix_end": suffix_end,
        "blobs": blobs,
        "documents": documents,
        "blob_names": blob_names,
        "chat_history": chat_history,
        "user_guided_blobs": user_guided_blobs,
    }

    return return_dict


def download_request(request_id: str):
    query = f"""SELECT
                *
            FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`
            WHERE request_id = '{request_id}' AND event_type = 'chat_host_request'"""

    tenant = DOGFOOD

    gcp_creds, _ = get_gcp_creds(None)

    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    print("sending request to big query")
    rows = bigquery_client.query_and_wait(query, page_size=1)
    rows = list(rows)
    assert len(rows) == 1
    row = rows[0]
    assert row.request_id == request_id

    # make row json serializable
    row = {
        "request_id": row.request_id,
        "request_json": row.raw_json["request"],
    }
    return row


def get_request_inputs(request_id: str):
    return parse_row(download_request(request_id))


ris = [
    "4883381f-94d4-4a71-8182-2bdec46cdc89",
    "5199b947-b45a-4e22-a087-e59a12beda91",
    "3bb42ee8-c40b-4c69-96cf-9bf23e8548fb",
    "f08c5194-6cb6-4480-87a1-b256ab2f52e2",
    "eaabc5c0-d3c2-4b6c-8f3e-9e11ca25c751",
    "f107a309-8539-44d7-a838-33bffee6f453",
    "4d2dded2-2b7e-4c1f-95b0-8a1af6038dd7",
    "88b45e38-2290-4ea3-a977-79ab621919dc",
    "d6591aea-4aaa-445d-994b-cb8584b2faa3",
    "c2f6f68d-6cc7-43bc-8bde-b859e3fb2a18",
    "68807d90-401b-4405-8fe9-3bc6c3d1d543",
    "84f1b2b8-cdd4-4f79-a97c-17ee2ac9e288",
    "09c3ebc3-f21a-4a99-9046-647f4b037716",
    "3be42ffe-15f8-4ed4-9343-1c57376692b9",
    "271d5ef8-8879-4cec-9ccb-9c13e3e8e77b",
    "bdaf4277-0388-4b90-9db0-0e597bff2ed3",
    "2ae20dcc-8e05-4123-99eb-1c1d35021ddd",
    "3f0d74fe-84c5-4d9f-8e4d-8fc8d4102fd5",
    "3be42ffe-15f8-4ed4-9343-1c57376692b9",
    "db2e00d7-2a07-4b78-83d5-9691234e621f",
    "d0eb5f89-dff3-409b-ac95-6d73ca6de261",
    "646abdee-0a49-4f58-b916-54a2e0373935",
    "3c3df37a-83d2-47bf-9d2f-c9414e34dda2",
    "b24ab870-2199-40e0-972f-30885884169d",
    "53e4785d-f73d-40a0-bfca-d41a9af51a28",
    "b76b591e-294a-4652-bd69-fcac8cca7ce7",
    "87aa48d3-e7de-481f-bf6a-4022b3edc7ef",
    "4e5a9077-3532-40a6-8f74-9296d99e354f",
    "55ce471b-c1b5-4b67-a993-ab3dc352b3fb",
    "02735c87-064c-4af5-9bb4-e2d3b0cb2228",
    "f0c1a9d5-35fd-4c19-98b7-24c94b5870c1",
    "a5612d97-f344-44c4-b3eb-9a1d8bcc0ef9",
    "5c2f0981-9088-458f-bd40-344f803c0ba4",
    "163cf668-abdf-485b-b78f-71e2f7667d72",
    "70bdbd7e-94dc-4e02-9f8a-ab7fa2227c36",
    "974831ff-997d-4de8-984b-94e6c6bd42dc",
    "87370b41-c997-4a42-aafe-3bdf86b2cf88",
    "e2757a6b-9bc8-4760-baf0-f6fff30fd9d0",
    "7fe3347a-ccdb-4df8-ac13-93d88926a6a8",
    "8436588d-6e98-4ae7-a448-84cb7644612a",
    "9ac85b86-0135-4965-bd62-4a9ff2fdfc17",
    "faa1dfaa-8961-462f-a253-30fbf420ffb0",
    "17276560-0a77-4acd-917f-740f4a4e1f30",
    "150adc5a-0366-46cf-9aba-ff721965e219",
    "72a02215-19a8-4480-9709-abc10ef2400f",
    "3ad3f548-ee20-4220-b4a6-4396bdfb69eb",
    "0c3ee384-9f1a-469d-89d7-605879e35f38",
]

ug_blobs = {}


def process_ri(ri):
    try:
        chat_sample_data = get_request_inputs(ri)
    except:  # noqa: E722
        ug_blobs[ri] = {
            "user_guided_blobs": [],
            "current_blobs": [],
            "original_user_guided_blobs": [],
            "selected_code": "",
        }
        print("!!!", ri, "failed")
        return
    chat_history = [
        Exchange(
            request_message=chat_request["request_message"],
            response_text=chat_request["response_text"],
        )
        for chat_request in chat_sample_data["chat_history"]
    ]
    current_blobs = (
        [chat_sample_data["blob_name"]] if chat_sample_data["blob_name"] else []
    )
    original_user_guided_blobs = chat_sample_data["user_guided_blobs"] or []
    user_guided_blobs = list(set(current_blobs + original_user_guided_blobs))
    model_input = ResearchChatPromptInput(
        path=chat_sample_data["path"],
        prefix=chat_sample_data["prefix"],
        selected_code=chat_sample_data["selected_code"],
        suffix=chat_sample_data["suffix"],
        message=chat_sample_data["message"],
        chat_history=chat_history,
        prefix_begin=chat_sample_data["prefix_begin"],
        suffix_end=chat_sample_data["suffix_end"],
        retrieved_chunks=[],
        doc_ids=chat_sample_data["blob_names"],
        user_guided_blobs=user_guided_blobs,
    )
    selected_code = chat_sample_data["selected_code"] or ""
    ug_blobs[ri] = {
        "user_guided_blobs": model_input.user_guided_blobs,
        "current_blobs": current_blobs,
        "original_user_guided_blobs": original_user_guided_blobs,
        "selected_code": selected_code,
    }
    print(
        "!!!",
        ri,
        len(model_input.user_guided_blobs),
        len(current_blobs),
        len(original_user_guided_blobs),
        len(selected_code),
    )


with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    futures = {executor.submit(process_ri, ri): ri for ri in ris}
    for future in concurrent.futures.as_completed(futures):
        ri = futures[future]
        try:
            future.result()
        except Exception as e:
            print(f"!!! {ri} failed: {e}")

print()

with open("ug_blobs.json", "w") as f:
    json.dump(ug_blobs, f)

with open("ug_blob_lengths.txt", "w") as f:
    for ri in ris:
        f.write(
            f"{ri} {len(ug_blobs[ri]['user_guided_blobs'])} {len(ug_blobs[ri]['current_blobs'])} {len(ug_blobs[ri]['original_user_guided_blobs'])}\n"
        )
        print(
            ri,
            len(ug_blobs[ri]["user_guided_blobs"]),
            len(ug_blobs[ri]["current_blobs"]),
            len(ug_blobs[ri]["original_user_guided_blobs"]),
            len(ug_blobs[ri]["selected_code"]),
        )

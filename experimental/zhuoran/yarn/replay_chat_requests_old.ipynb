{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay Chat requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "from google.cloud import bigquery\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.tenants import DOGFOOD\n", "from base.augment_client.client import BlobsJson\n", "\n", "\n", "def parse_row(row: bigquery.Row) -> dict:\n", "    request_json = row[\"request_json\"]\n", "\n", "    prefix = request_json.get(\"prefix\", \"\")\n", "    suffix = request_json.get(\"suffix\", \"\")\n", "    selected_code = request_json.get(\"selected_code\", \"\")\n", "    prefix_begin = request_json.get(\"prefix_begin\", None)\n", "    suffix_end = request_json.get(\"suffix_end\", None)\n", "    blob_name = request_json.get(\"blob_name\", None)\n", "    blobs = request_json.get(\"blobs\", None)\n", "    if blobs is not None:\n", "        print(blobs.keys())\n", "        blobs = BlobsJson(\n", "            checkpoint_id=blobs.pop(\"baseline_checkpoint_id\")\n", "            if \"baseline_checkpoint_id\" in blobs\n", "            else None,\n", "            added_blobs=[base64.b64decode(blob).hex() for blob in blobs[\"added\"]]\n", "            if \"added\" in blobs\n", "            else [],\n", "            deleted_blobs=[base64.b64decode(blob).hex() for blob in blobs[\"deleted\"]]\n", "            if \"deleted\" in blobs\n", "            else [],\n", "        )\n", "    user_guided_blobs = request_json.get(\"user_guided_blobs\", None)\n", "\n", "    chat_history = request_json.get(\"chat_history\", [])\n", "\n", "    return_dict = {\n", "        \"selected_code\": selected_code,\n", "        \"message\": request_json[\"message\"],\n", "        \"prefix\": prefix,\n", "        \"suffix\": suffix,\n", "        \"path\": request_json[\"path\"],\n", "        \"blob_name\": blob_name,\n", "        \"prefix_begin\": prefix_begin,\n", "        \"suffix_end\": suffix_end,\n", "        \"blobs\": blobs,\n", "        \"chat_history\": chat_history,\n", "        \"user_guided_blobs\": user_guided_blobs,\n", "        \"all_keys\": request_json.keys(),\n", "    }\n", "\n", "    return return_dict\n", "\n", "\n", "def download_request(request_id: str):\n", "    query = f\"\"\"SELECT\n", "                *\n", "            FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`\n", "            WHERE request_id = '{request_id}' AND event_type = 'chat_host_request'\"\"\"\n", "\n", "    tenant = DOGFOOD\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "\n", "    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)\n", "\n", "    print(\"sending request to big query\")\n", "    rows = bigquery_client.query_and_wait(query, page_size=1)\n", "    rows = list(rows)\n", "\n", "    assert len(rows) == 1, len(rows)\n", "    row = rows[0]\n", "    assert row.request_id == request_id\n", "\n", "    # make row json serializable\n", "    row = {\n", "        \"request_id\": row.request_id,\n", "        \"request_json\": row.raw_json[\"request\"],\n", "    }\n", "    return row\n", "\n", "\n", "def get_request_inputs(request_id: str):\n", "    return parse_row(download_request(request_id))\n", "\n", "\n", "chat_sample_data = get_request_inputs(\n", "    \"\"\"\n", "68807d90-401b-4405-8fe9-3bc6c3d1d543\n", "\"\"\".strip()\n", ")\n", "print(chat_sample_data[\"message\"])\n", "print(chat_sample_data[\"all_keys\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient, AugmentModelClient, ChatResponse\n", "\n", "API_TOKEN: str = os.environ.get(\"AUGMENT_TOKEN\", \"\")\n", "assert API_TOKEN\n", "\n", "client = AugmentClient(\n", "    url=\"https://dev-zhuoran.us-central.api.augmentcode.com\",\n", "    token=API_TOKEN,\n", ")\n", "\n", "\n", "def collect_streaming_response(stream):\n", "    # dict_keys(['text', 'unknown_blob_names', 'checkpoint_not_found', 'request_id'])\n", "    text = \"\"\n", "    unknown_blob_names = set()\n", "    checkpoint_not_found = False\n", "    request_ids = set()\n", "    for chunk_response in stream:\n", "        text += chunk_response.text\n", "        unknown_blob_names.update(chunk_response.unknown_blob_names)\n", "        checkpoint_not_found = (\n", "            checkpoint_not_found or chunk_response.checkpoint_not_found\n", "        )\n", "        request_ids.add(chunk_response.request_id)\n", "    assert len(request_ids) == 1\n", "    return ChatResponse(\n", "        request_id=list(request_ids)[0],\n", "        text=text,\n", "        unknown_blob_names=list(unknown_blob_names),\n", "        checkpoint_not_found=checkpoint_not_found,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dogfood_client = AugmentClient(\n", "    url=\"https://staging-shard-0.api.augmentcode.com/\",\n", "    token=API_TOKEN,\n", ")\n", "\n", "model_client_dogfood = AugmentModelClient(dogfood_client, \"\")\n", "chat_stream_dogfood = model_client_dogfood.chat_stream(\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    message=chat_sample_data[\"message\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    path=chat_sample_data[\"path\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    blob_name=chat_sample_data[\"blob_name\"],\n", "    blobs=chat_sample_data[\"blobs\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", "    user_guided_blobs=chat_sample_data[\"user_guided_blobs\"],\n", ")\n", "chat_response_dogfood = collect_streaming_response(chat_stream_dogfood)\n", "chat_nonstream_dogfood = model_client_dogfood.chat(\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    message=chat_sample_data[\"message\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    path=chat_sample_data[\"path\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    blob_name=chat_sample_data[\"blob_name\"],\n", "    blobs=chat_sample_data[\"blobs\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", "    user_guided_blobs=chat_sample_data[\"user_guided_blobs\"],\n", ")\n", "print(\n", "    f\"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{chat_response_dogfood.request_id}\"\n", ")\n", "chat_response_dogfood"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chat_nonstream_dogfood"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# model_client_8k = AugmentModelClient(client, \"binks-l3-70B-FP8-8k-noug-cl1-16-3-chat\")\n", "model_client_8k = AugmentModelClient(client, \"binks-l3-70B-FP8-ug-chatanol1-16-3-chat\")\n", "\n", "chat_stream_8k = model_client_8k.chat_stream(\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    message=chat_sample_data[\"message\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    path=chat_sample_data[\"path\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    blob_name=chat_sample_data[\"blob_name\"],\n", "    blobs=chat_sample_data[\"blobs\"],\n", "    user_guided_blobs=chat_sample_data[\"user_guided_blobs\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", ")\n", "chat_response_8k = collect_streaming_response(chat_stream_8k)\n", "print(\n", "    f\"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{chat_response_8k.request_id}\"\n", ")\n", "chat_response_8k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# model_client_16k = AugmentModelClient(client, \"binks-l3-70B-FP8-16k-noug-cl1-16-3-chat\")\n", "model_client_16k = AugmentModelClient(client, \"binks-l3-70B-FP8-16k-ug-cl1-16-3-chat\")\n", "\n", "chat_stream_16k = model_client_16k.chat_stream(\n", "    selected_code=chat_sample_data[\"selected_code\"],\n", "    message=chat_sample_data[\"message\"],\n", "    prefix=chat_sample_data[\"prefix\"],\n", "    suffix=chat_sample_data[\"suffix\"],\n", "    path=chat_sample_data[\"path\"],\n", "    prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "    suffix_end=chat_sample_data[\"suffix_end\"],\n", "    blob_name=chat_sample_data[\"blob_name\"],\n", "    blobs=chat_sample_data[\"blobs\"],\n", "    user_guided_blobs=chat_sample_data[\"user_guided_blobs\"],\n", "    chat_history=chat_sample_data[\"chat_history\"],\n", ")\n", "chat_response_16k = collect_streaming_response(chat_stream_16k)\n", "print(\n", "    f\"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{chat_response_16k.request_id}\"\n", ")\n", "chat_response_16k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chat_response_dogfood.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chat_response_8k.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chat_response_16k.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# request_ids = [\n", "#     \"024f06b5-e231-4805-815c-************\",\n", "#     \"df895be8-2d48-4106-8a6a-2c4a1bf7a418\",\n", "#     \"638bf0d2-47f8-4f01-b523-3bcce83ffa9f\",\n", "#     \"e0ddeb8a-02ec-4a2c-a8a8-3a11042259c0\",\n", "#     \"aa2953b1-4a42-42ad-bd1c-138bbe413b14\",\n", "#     \"718bb7b4-fd25-424c-970e-06256d25ec01\",\n", "#     \"b5142b80-fa7b-41e5-bdaf-87bb443cf861\",\n", "#     \"1a1e2eb9-4737-406a-a833-b9c34b8e3238\",\n", "#     \"a6482232-d1a2-464e-a1a7-0c85008b910f\",\n", "#     \"d0450344-a28f-4ab7-beae-8d1a2a4feed0\",\n", "#     \"f3077349-e63f-4016-8d0d-d5a6923a917b\",\n", "#     \"761d3c30-daaa-4a68-a258-1b4c7d68e204\",\n", "#     \"a4379f10-e329-453d-95ae-8c7339da2cd5\",\n", "#     \"8d725121-6db4-47b8-91ae-3058d5a021f7\",\n", "#     \"e36d3173-44fa-4574-b387-d9ceb6f41c43\",\n", "#     \"802921db-f63c-479c-a101-21ede8ce6f4a\",\n", "#     \"4f30db95-17a8-4a5c-98aa-05ef9d5682bb\",\n", "#     \"0d88fa32-b8c5-49f4-9c7f-405e98ae8971\",\n", "# ]\n", "\n", "# model_client_16k = AugmentModelClient(client, \"binks-l3-70B-FP8-16k-ug-cl1-16-3-chat\")\n", "\n", "# for request_id in request_ids:\n", "#     print(request_id)\n", "#     chat_sample_data = get_request_inputs(request_id)\n", "#     chat_stream_16k = model_client_16k.chat_stream(\n", "#         selected_code=chat_sample_data[\"selected_code\"],\n", "#         message=chat_sample_data[\"message\"],\n", "#         prefix=chat_sample_data[\"prefix\"],\n", "#         suffix=chat_sample_data[\"suffix\"],\n", "#         path=chat_sample_data[\"path\"],\n", "#         prefix_begin=chat_sample_data[\"prefix_begin\"],\n", "#         suffix_end=chat_sample_data[\"suffix_end\"],\n", "#         blob_name=chat_sample_data[\"blob_name\"],\n", "#         blobs=chat_sample_data[\"blobs\"],\n", "#         user_guided_blobs=chat_sample_data[\"user_guided_blobs\"],\n", "#         chat_history=chat_sample_data[\"chat_history\"],\n", "#     )\n", "#     print(chat_sample_data[\"message\"])\n", "#     chat_response_16k = collect_streaming_response(chat_stream_16k)\n", "#     print(\n", "#         f\"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{chat_response_16k.request_id}\"\n", "#     )\n", "#     print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
system:
    name: remote_chat
    # model_name: binks-llama3-70B-FP8-chatanol1-11-chat
    # model_name: binks-l3-70B-FP8-16k-noug-cl1-16-3-chat
    # model_name: binks-l3-70B-FP8-8k-noug-cl1-16-3-chat
    model_name: {model_name}

    client:
        # url: https://dev-zhuoran.us-central.api.augmentcode.com
        # url: https://dogfood.api.augmentcode.com
        url: {url}

task:
    name: humaneval_instruct
    # variant: synthetic
    variant: standard
    # limit: 40

podspec: gpu-small.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: instruct_humaneval_{short_name}
  project: zhuoran-eval
  workspace: Dev

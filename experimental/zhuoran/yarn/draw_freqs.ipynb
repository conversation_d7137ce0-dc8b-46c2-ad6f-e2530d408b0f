{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import importlib\n", "\n", "import torch\n", "\n", "from base.fastforward import positional_embeddings, positional_embeddings_test_utils\n", "\n", "importlib.reload(positional_embeddings)\n", "importlib.reload(positional_embeddings_test_utils)\n", "\n", "dim = 10_000\n", "max_seq_len = 128_000\n", "original_max_seq_len = 8_192\n", "base = 1e7\n", "yarn_extrapolation_factor = 1.0\n", "beta_fast = 32\n", "beta_slow = 1\n", "\n", "fused_rope = positional_embeddings.FusedRotaryEmbedding(\n", "    head_dim=dim,\n", "    max_seq_len=max_seq_len,\n", "    config=positional_embeddings.RotaryConfig(\n", "        rotary_ratio=1.0,\n", "        rotary_theta=base,\n", "        max_position_embeddings=max_seq_len,\n", "        ext_config=positional_embeddings.YaRNExtensionConfig(\n", "            unscaled_max_position_embeddings=original_max_seq_len,\n", "            rotary_scaling_factor=max_seq_len / original_max_seq_len,\n", "            beta_fast=beta_fast,\n", "            beta_slow=beta_slow,\n", "        ),\n", "        rotary_interleave=False,\n", "    ),\n", "    device=\"cuda\",\n", ")\n", "cos = fused_rope.freqs_cos\n", "sin = fused_rope.freqs_sin\n", "\n", "fused_rope_inv = positional_embeddings.FusedRotaryEmbedding(\n", "    head_dim=dim,\n", "    max_seq_len=max_seq_len,\n", "    config=positional_embeddings.RotaryConfig(\n", "        rotary_ratio=1.0,\n", "        rotary_theta=base,\n", "        max_position_embeddings=max_seq_len,\n", "        ext_config=positional_embeddings.YaRNExtensionConfig(\n", "            unscaled_max_position_embeddings=original_max_seq_len,\n", "            rotary_scaling_factor=original_max_seq_len / max_seq_len,\n", "            beta_fast=beta_fast,\n", "            beta_slow=beta_slow,\n", "        ),\n", "        rotary_interleave=False,\n", "    ),\n", "    device=\"cuda\",\n", ")\n", "cos = fused_rope.freqs_cos\n", "sin = fused_rope.freqs_sin\n", "\n", "fused_rope_1 = positional_embeddings.FusedRotaryEmbedding(\n", "    head_dim=dim,\n", "    max_seq_len=max_seq_len,\n", "    config=positional_embeddings.RotaryConfig(\n", "        rotary_ratio=1.0,\n", "        rotary_theta=base,\n", "        max_position_embeddings=max_seq_len,\n", "        ext_config=positional_embeddings.YaRNExtensionConfig(\n", "            unscaled_max_position_embeddings=original_max_seq_len,\n", "            rotary_scaling_factor=1.0,\n", "            beta_fast=beta_fast,\n", "            beta_slow=beta_slow,\n", "        ),\n", "        rotary_interleave=False,\n", "    ),\n", "    device=\"cuda\",\n", ")\n", "cos = fused_rope.freqs_cos\n", "sin = fused_rope.freqs_sin\n", "\n", "yarn_ref = positional_embeddings_test_utils.LlamaDynamicYaRNScaledRotaryEmbedding(\n", "    dim=dim,\n", "    max_position_embeddings=max_seq_len,\n", "    base=int(base),\n", "    original_max_position_embeddings=original_max_seq_len,\n", "    extrapolation_factor=yarn_extrapolation_factor,  # type: ignore\n", "    beta_fast=beta_fast,\n", "    beta_slow=beta_slow,\n", "    finetuned=True,\n", "    device=\"cuda\",\n", ")\n", "cos_ref = yarn_ref.cos_cached[0, 0, :, : dim // 2]\n", "sin_ref = yarn_ref.sin_cached[0, 0, :, : dim // 2]\n", "\n", "# part = positional_embeddings_test_utils.LlamaDynamicPartNTKScaledRotaryEmbedding(\n", "#     dim=dim,\n", "#     max_position_embeddings=max_seq_len,\n", "#     original_max_position_embeddings=original_max_seq_len,\n", "#     base=base,\n", "#     ntk_factor=1.0,\n", "#     extrapolation_factor=yarn_extrapolation_factor,\n", "#     finetuned=True,\n", "#     device=\"cuda\",\n", "# )\n", "# cos_ntk = part.cos_cached[0, 0, :, : dim // 2]\n", "# sin_ntk = part.sin_cached[0, 0, :, : dim // 2]\n", "\n", "# scaled = positional_embeddings_test_utils.LlamaDynamicScaledRotaryEmbedding(\n", "#     dim=dim, max_position_embeddings=original_max_seq_len, base=base, device=\"cuda\"\n", "# )\n", "# # cos_scaled = scaled.cos_cached[0, 0, :, : dim // 2]\n", "# # sin_scaled = scaled.sin_cached[0, 0, :, : dim // 2]\n", "\n", "# ntk = positional_embeddings_test_utils.LlamaDynamicScaledRotaryEmbedding(\n", "#     dim=dim,\n", "#     max_position_embeddings=original_max_seq_len,\n", "#     ntk=True,\n", "#     base=base,\n", "#     device=\"cuda\",\n", "# )\n", "# ntk(torch.tensor(0), seq_len=max_seq_len)\n", "# cos_ntk = ntk.cos_cached[0, 0, :, : dim // 2]\n", "# sin_ntk = ntk.sin_cached[0, 0, :, : dim // 2]\n", "\n", "# original = positional_embeddings_test_utils.LlamaDynamicScaledRotaryEmbedding(\n", "#     dim=dim, max_position_embeddings=original_max_seq_len, base=base, device=\"cuda\"\n", "# )\n", "# cos_original = original.cos_cached[0, 0, :, : dim // 2]\n", "# sin_original = original.sin_cached[0, 0, :, : dim // 2]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'FusedRotaryEmbedding' object has no attribute 'freqs'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmat<PERSON>lot<PERSON>b\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyplot\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mplt\u001b[39;00m\n\u001b[0;32m----> 4\u001b[0m freq_fused \u001b[38;5;241m=\u001b[39m \u001b[43mfused_rope\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfreqs\u001b[49m\u001b[38;5;241m.\u001b[39mcpu()\u001b[38;5;241m.\u001b[39mnumpy()\n\u001b[1;32m      5\u001b[0m y_fused \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mlog(\u001b[38;5;241m1\u001b[39m \u001b[38;5;241m/\u001b[39m freq_fused)\n\u001b[1;32m      7\u001b[0m freq_fused_inv \u001b[38;5;241m=\u001b[39m fused_rope_inv\u001b[38;5;241m.\u001b[39mfreqs\u001b[38;5;241m.\u001b[39mcpu()\u001b[38;5;241m.\u001b[39mnumpy()\n", "File \u001b[0;32m~/.local/lib/python3.11/site-packages/torch/nn/modules/module.py:1709\u001b[0m, in \u001b[0;36mModule.__getattr__\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   1707\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m modules:\n\u001b[1;32m   1708\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m modules[name]\n\u001b[0;32m-> 1709\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m object has no attribute \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: 'FusedRotaryEmbedding' object has no attribute 'freqs'"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "freq_fused = fused_rope.freqs.cpu().numpy()\n", "y_fused = np.log(1 / freq_fused)\n", "\n", "freq_fused_inv = fused_rope_inv.freqs.cpu().numpy()\n", "y_fused_inv = np.log(1 / freq_fused_inv)\n", "\n", "freq_fused_1 = fused_rope_1.freqs.cpu().numpy()\n", "y_fused_1 = np.log(1 / freq_fused_1)\n", "\n", "freq_ref = yarn_ref.inv_freq.cpu().numpy()\n", "y_ref = np.log(1 / freq_ref)\n", "\n", "# freq_part = part.inv_freq.cpu().numpy()\n", "# y_part = np.log(1 / freq_part)\n", "\n", "# freq_scaled = scaled.inv_freq.cpu().numpy() / max_seq_len * original_max_seq_len\n", "# y_scaled = np.log(1 / freq_scaled)\n", "\n", "# freq_ntk = ntk.inv_freq.cpu().numpy()\n", "# y_ntk = np.log(1 / freq_ntk)\n", "\n", "# freq_original = original.inv_freq.cpu().numpy()\n", "# y_original = np.log(1 / freq_original)\n", "\n", "plt.plot(y_fused, label=\"Fused\")\n", "plt.plot(y_fused_inv, label=\"Fused Inv\")\n", "plt.plot(y_fused_1, label=\"Fused 1\")\n", "# plt.plot(y_ref, label=\"Reference\")\n", "# plt.plot(y_part, label=\"Part\")\n", "# plt.plot(y_scaled, label=\"Scaled\")\n", "# plt.plot(y_ntk, label=\"NTK\")\n", "# plt.plot(y_original, label=\"Original\")\n", "plt.xlabel(\"Index\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.064, 0.064, 0.064, ..., 0.064, 0.064, 0.064])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["freq_scaled / freq_original"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([128000, 5000])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["freq = fused_rope.freqs\n", "freq.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rotation_count=32, dim=10000, base=10000000.0, max_position_embeddings=64000\n", "rotation_count * 2 * math.pi=201.06192982974676\n", "max_position_embeddings / (rotation_count * 2 * math.pi)=318.3098861837907\n", "rotation_count=1, dim=10000, base=10000000.0, max_position_embeddings=64000\n", "rotation_count * 2 * math.pi=6.283185307179586\n", "max_position_embeddings / (rotation_count * 2 * math.pi)=10185.916357881302\n", "1787 2863\n", "linear_func=tensor([-1.6608, -1.6599, -1.6589,  ...,  2.9833,  2.9842,  2.9851])\n", "ramp_func=tensor([0., 0., 0.,  ..., 1., 1., 1.])\n", "extrapolation_factor=0.0\n", "mask=tensor([0., 0., 0.,  ..., 0., 0., 0.], device='cuda:0')\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "\n", "from base.fastforward import positional_embeddings_backup, positional_embeddings\n", "\n", "rotary_ratio = 1.0\n", "rotary_theta = 1e7\n", "max_position_embeddings = 128_000\n", "position_interpolation_factor = 2.0\n", "rotary_interleave = False\n", "cast_sincos_as_input = False\n", "head_dim = 10_000\n", "\n", "config = positional_embeddings.RotaryConfig(\n", "    rotary_ratio=rotary_ratio,\n", "    rotary_theta=rotary_theta,\n", "    max_position_embeddings=max_position_embeddings,\n", "    original_max_position_embeddings=int(\n", "        max_position_embeddings / position_interpolation_factor\n", "    ),\n", "    position_interpolation_factor=position_interpolation_factor,\n", "    rotary_interleave=rotary_interleave,\n", "    cast_sincos_as_input=cast_sincos_as_input,\n", ")\n", "fused_rope = positional_embeddings.FusedRotaryEmbedding(\n", "    head_dim=head_dim,\n", "    config=config,\n", ")\n", "backup_config = positional_embeddings_backup.RotaryConfig(\n", "    rotary_ratio=rotary_ratio,\n", "    rotary_theta=rotary_theta,\n", "    max_position_embeddings=max_position_embeddings,\n", "    rotary_scaling_factor=position_interpolation_factor,\n", "    rotary_interleave=rotary_interleave,\n", "    cast_sincos_as_input=cast_sincos_as_input,\n", ")\n", "backup_rope = positional_embeddings_backup.FusedRotaryEmbedding(\n", "    head_dim=head_dim,\n", "    config=backup_config,\n", ")\n", "\n", "torch.allclose(fused_rope.final_freqs, backup_rope.final_freqs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fn = fused_rope.final_freqs.cpu().numpy()\n", "fo = backup_rope.final_freqs.cpu().numpy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_159767/4118419373.py:1: RuntimeWarning: invalid value encountered in divide\n", "  fn / fo\n"]}, {"data": {"text/plain": ["array([[nan, nan, nan, ..., nan, nan, nan],\n", "       [0.5, 0.5, 0.5, ..., 0.5, 0.5, 0.5],\n", "       [0.5, 0.5, 0.5, ..., 0.5, 0.5, 0.5],\n", "       ...,\n", "       [0.5, 0.5, 0.5, ..., 0.5, 0.5, 0.5],\n", "       [0.5, 0.5, 0.5, ..., 0.5, 0.5, 0.5],\n", "       [0.5, 0.5, 0.5, ..., 0.5, 0.5, 0.5]], dtype=float32)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["fn / fo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
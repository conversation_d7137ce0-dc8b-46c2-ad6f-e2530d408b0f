system:
  name: "chat_rag"
  model:
    name: "fastforward_llama3_instruct_70b_16k_fp8"
    sequence_length: 16384
  prompt_formatter:
    tokenizer_name: llama3_instruct
    prompt_formatter_name: binks_llama3_tokenized
    prefix_len: 1024
    suffix_len: 1024
    path_len: 256
    message_len: -1  # Not used by the binks_llama3 prompt formatter
    selected_code_len: -1  # Not used by the binks_llama3 prompt formatter
    chat_history_len: 3072
    retrieval_len_per_each_user_guided_file: 3072
    retrieval_len_for_user_guided: 8192
    retrieval_len: -1  # Fill the rest of the input prompt with retrievals
    max_prompt_len: 13312 # 16384 - 3072, the last 3072 reserved for output tokens
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 3072
  retriever:
    # scorer:
    #   name: dense_scorer_v2_fbwd
    #   checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid
    #   model_key: model.models.0
    scorer:
      name: dense_scorer_v2_fbwd
      checkpoint_path: /mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-16-3
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: base:chatanol6
      tokenizer_name: rogue
      max_tokens: 1024
    document_formatter:
      name: base:ethanol6-embedding-with-path-key
      tokenizer_name: rogue
      add_path: true
      max_tokens: 1024
  experimental:
    retriever_top_k: 32
  verbose: False

task:
  name: augment_qa
  dataset_path: /mnt/efs/augment/data/processed/augment_qa/v2
  html_report_output_dir: /mnt/efs/augment/public_html/augment_qa/v2

podspec: 2xH100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: augment_qa_v9_yarn_16k
  project: zhuoran-eval
  workspace: Dev

augment:
  gpu_count: 2

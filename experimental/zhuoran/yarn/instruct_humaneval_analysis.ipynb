{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "data_8k = []\n", "with open(\"8k_noret.jsonl\", \"r\") as f:\n", "    for line in f:\n", "        data = json.loads(line)\n", "        data_8k.append(data)\n", "\n", "data_16k = []\n", "with open(\"16k_noret.jsonl\", \"r\") as f:\n", "    for line in f:\n", "        data = json.loads(line)\n", "        data_16k.append(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datum = data_8k[0]\n", "print(datum.keys())\n", "print(datum[\"patch\"].keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff_pairs = []\n", "for datum_8k, datum_16k in zip(data_8k, data_16k):\n", "    if datum_8k[\"score\"][\"pass_at_1\"] != datum_16k[\"score\"][\"pass_at_1\"]:\n", "        diff_pairs.append((datum_8k, datum_16k))\n", "\n", "for datum_8k, datum_16k in diff_pairs:\n", "    print(datum_8k[\"score\"][\"pass_at_1\"], datum_16k[\"score\"][\"pass_at_1\"])\n", "print(len(diff_pairs))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import subprocess\n", "\n", "\n", "def execute_python_code(python_code: str) -> tuple[str, str]:\n", "    process = subprocess.Popen(\n", "        [\"python\", \"-c\", python_code], stdout=subprocess.PIPE, stderr=subprocess.PIPE\n", "    )\n", "\n", "    stdout, stderr = process.communicate()\n", "\n", "    return stdout.decode(\"utf-8\"), stderr.decode(\"utf-8\")\n", "\n", "\n", "execute_python_code(\"print('hello world')\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["datum_8k, datum_16k = diff_pairs[13]\n", "print(\"# Result\")\n", "print()\n", "print(datum_8k[\"patch\"][\"task_id\"])\n", "print(datum_8k[\"score\"][\"pass_at_1\"], datum_16k[\"score\"][\"pass_at_1\"])\n", "print()\n", "print(\"# Instruction\")\n", "print()\n", "print(datum_8k[\"patch\"][\"instruction\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(datum_8k[\"completion\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_max(words):\n", "    \"\"\"Write a function that accepts a list of strings.\n", "    The list contains different words. Return the word with maximum number\n", "    of unique characters. If multiple strings have maximum number of unique\n", "    characters, return the one which comes first in lexicographical order.\n", "\n", "    find_max([\"name\", \"of\", \"string\"]) == \"string\"\n", "    find_max([\"name\", \"enam\", \"game\"]) == \"enam\"\n", "    find_max([\"aaaaaaa\", \"bb\" ,\"cc\"]) == \"\"aaaaaaa\"\n", "    \"\"\"\n", "\n", "\n", "def find_max(words):\n", "    def unique_chars(word):\n", "        return len(set(word))\n", "\n", "    max_unique_chars = max(unique_chars(word) for word in words)\n", "    max_words = [word for word in words if unique_chars(word) == max_unique_chars]\n", "    return min(max_words, key=lambda x: (x.lower(), x))\n", "\n", "\n", "def check(candidate):\n", "    # Check some simple cases\n", "    assert candidate([\"name\", \"of\", \"string\"]) == \"string\", \"t1\"\n", "    assert candidate([\"name\", \"enam\", \"game\"]) == \"enam\", \"t2\"\n", "    assert candidate([\"aaaaaaa\", \"bb\", \"cc\"]) == \"aaaaaaa\", \"t3\"\n", "    assert candidate([\"abc\", \"cba\"]) == \"abc\", \"t4\"\n", "    assert candidate([\"play\", \"this\", \"game\", \"of\", \"footbott\"]) == \"footbott\", \"t5\"\n", "    assert candidate([\"we\", \"are\", \"gonna\", \"rock\"]) == \"gonna\", \"t6\"\n", "    assert candidate([\"we\", \"are\", \"a\", \"mad\", \"nation\"]) == \"nation\", \"t7\"\n", "    assert candidate([\"this\", \"is\", \"a\", \"prrk\"]) == \"this\", \"t8\"\n", "\n", "    # Check some edge cases that are easy to work out by hand.\n", "    assert candidate([\"b\"]) == \"b\", \"t9\"\n", "    assert candidate([\"play\", \"play\", \"play\"]) == \"play\", \"t10\"\n", "\n", "\n", "check(find_max)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(datum_16k[\"completion\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_max(words):\n", "    \"\"\"Write a function that accepts a list of strings.\n", "    The list contains different words. Return the word with maximum number\n", "    of unique characters. If multiple strings have maximum number of unique\n", "    characters, return the one which comes first in lexicographical order.\n", "\n", "    find_max([\"name\", \"of\", \"string\"]) == \"string\"\n", "    find_max([\"name\", \"enam\", \"game\"]) == \"enam\"\n", "    find_max([\"aaaaaaa\", \"bb\" ,\"cc\"]) == \"\"aaaaaaa\"\n", "    \"\"\"\n", "\n", "\n", "def find_max(words):\n", "    def count_unique_chars(word):\n", "        return len(set(word))\n", "\n", "    max_unique_chars = max(count_unique_chars(word) for word in words)\n", "    max_words = [word for word in words if count_unique_chars(word) == max_unique_chars]\n", "    return min(max_words, key=lambda x: (len(x), x))\n", "\n", "\n", "def check(candidate):\n", "    # Check some simple cases\n", "    assert candidate([\"name\", \"of\", \"string\"]) == \"string\", \"t1\"\n", "    assert candidate([\"name\", \"enam\", \"game\"]) == \"enam\", \"t2\"\n", "    assert candidate([\"aaaaaaa\", \"bb\", \"cc\"]) == \"aaaaaaa\", candidate(\n", "        [\"aaaaaaa\", \"bb\", \"cc\"]\n", "    )\n", "    assert candidate([\"abc\", \"cba\"]) == \"abc\", \"t4\"\n", "    assert candidate([\"play\", \"this\", \"game\", \"of\", \"footbott\"]) == \"footbott\", \"t5\"\n", "    assert candidate([\"we\", \"are\", \"gonna\", \"rock\"]) == \"gonna\", \"t6\"\n", "    assert candidate([\"we\", \"are\", \"a\", \"mad\", \"nation\"]) == \"nation\", \"t7\"\n", "    assert candidate([\"this\", \"is\", \"a\", \"prrk\"]) == \"this\", \"t8\"\n", "\n", "    # Check some edge cases that are easy to work out by hand.\n", "    assert candidate([\"b\"]) == \"b\", \"t9\"\n", "    assert candidate([\"play\", \"play\", \"play\"]) == \"play\", \"t10\"\n", "\n", "\n", "check(find_max)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
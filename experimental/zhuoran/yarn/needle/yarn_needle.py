import argparse
import datetime
import random
import re
import string

import numpy as np
from megatron.tokenizer import tokenizer as megatron_tokenizer

from research.core.data_paths import canonicalize_path
from research.models import fastforward_llama_models, meta_model, remote_models
from research.models.fastforward_llama_models import cached_attention, llama_model_specs

_ = (
    canonicalize_path,
    fastforward_llama_models,
    llama_model_specs,
    meta_model,
    remote_models,
    cached_attention,
)

TEMPLATE = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.<|eot_id|><|start_header_id|>user<|end_header_id|>

{user_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""


def get_needle_stack(
    tokenizer, target_token_count, key_type, key_width, value_type, value_width
):
    """Generates a needle stack."""
    secret_count = 60_000
    real_target_token_count = max(
        int(target_token_count * 0.93), target_token_count - 768
    )
    suffix_length = 5

    if key_type == "index":
        keys = list(range(secret_count))
    elif key_type == "alphabetic":
        keys = [
            "".join(random.choice(string.ascii_lowercase) for _ in range(key_width))
            for _ in range(secret_count)
        ]
    elif key_type == "digital":
        keys = [
            "".join(random.choice(string.digits) for _ in range(key_width))
            for _ in range(secret_count)
        ]
    if value_type == "alphabetic":
        secrets = [
            "".join(random.choice(string.ascii_lowercase) for _ in range(value_width))
            for _ in range(secret_count)
        ]
    elif value_type == "digital":
        secrets = [
            "".join(random.choice(string.digits) for _ in range(value_width))
            for _ in range(secret_count)
        ]
    full_string = ".\n".join(
        f"Secret {key}: {secret}" for key, secret in zip(keys, secrets)
    )
    full_tokens = tokenizer.tokenize(TEMPLATE.format(user_message=full_string))

    prefix = full_tokens[: real_target_token_count - suffix_length]
    line_break = tokenizer.tokenize(".\n")[0]
    last_line_break = prefix[::-1].index(line_break)
    second_last_line_break = prefix[::-1].index(line_break, last_line_break + 1)
    third_last_line_break = prefix[::-1].index(line_break, second_last_line_break + 1)
    if last_line_break == 0:
        last_line_break = second_last_line_break
        second_last_line_break = third_last_line_break
    prefix = prefix[:-last_line_break]

    suffix = full_tokens[-suffix_length:]
    tokens = prefix + suffix
    available_secret_count = len(
        re.findall(
            rf"Secret [a-z0-9]{{1,10}}: [a-z0-9]{{{value_width}}}.",
            tokenizer.detokenize(tokens),
        )
    )
    return tokens, available_secret_count, keys, secrets, suffix


def recall_secret(
    i, tokens, suffix, keys, model, tokenizer, value_type, value_width, raw_generate
):
    """Asks the model to recall a secret."""
    if value_type == "alphabetic":
        value_type = "letter"
    elif value_type == "digital":
        value_type = "digit"
    probe = tokenizer.tokenize(
        f"Now recall from this list of secrets, what is secret {keys[i]}?"
        f" Answer just the {value_width}-{value_type} secret and nothing more."
        f" Your answer must be only {value_width} long and all {value_type}s!"
    )
    probing_tokens = tokens[: -len(suffix)] + probe + tokens[-len(suffix) :]

    options = meta_model.GenerationOptions(
        max_generated_tokens=20, temperature=0.0, top_k=0, top_p=0
    )

    if raw_generate:
        output = model.raw_generate(probing_tokens, options)
    else:
        output = model.generate(
            meta_model.ModelInput(prefix=tokenizer.detokenize(probing_tokens)), options
        )
    return output


def get_indices(length, sample_count):
    """Selects first 5, last 5, and 10 evenly distributed indices in the middle."""
    all_ints = list(range(length))

    first_5 = all_ints[:5]
    last_5 = all_ints[-5:]
    mid = all_ints[5:-5][:: ((length - 10) // (sample_count - 10)) or 1][
        : sample_count - 10
    ]

    result = sorted(set(first_5 + mid + last_5))
    return result


def main(
    target_token_count,
    max_position_embeddings,
    original_max_position_embeddings,
    theta,
    pos_factor,
    uses_yarn,
    beta_fast,
    beta_slow,
    sample_count,
    model_type,
    raw_generate,
):
    """Main function."""
    start_time = datetime.datetime.now(datetime.UTC)
    original_max_position_embeddings = original_max_position_embeddings or int(
        max_position_embeddings / pos_factor
    )
    print(
        f"{max_position_embeddings=}, "
        f"{original_max_position_embeddings=}, "
        f"{pos_factor=}",
    )
    model_spec = llama_model_specs.LlamaModelSpec(
        name="llama3-70b",
        checkpoint_path=str(
            canonicalize_path("checkpoints/llama3/Meta-Llama-3-70B-Instruct-ff-fp8")
        ),
        emb_dim=8_192,
        num_layers=80,
        num_heads=8,
        num_queries_per_head=8,
        head_dim=128,
        attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,
        vocab_size=128_256,
        mlp_dim_divisible_by=2_048,  # How to get this reliably?
        ffn_dim_multiplier=1.3125,
        norm_eps=1e-05,
        # rotary_config=positional_embeddings.RotaryConfig(
        #     rotary_theta=theta,
        #     rotary_ratio=1.0,
        #     rotary_scaling_factor=pos_factor,
        #     uses_yarn=uses_yarn,
        #     beta_fast=beta_fast,
        #     beta_slow=beta_slow,
        #     max_position_embeddings=max_position_embeddings,
        #     unscaled_max_position_embeddings=original_max_position_embeddings,
        # ),
        rotary_theta=theta,
        rotary_pct=1.0,
        rotary_scaling_factor=pos_factor,
        rotary_extension_method="yarn" if uses_yarn else "deepseek_v1",
        beta_fast=beta_fast,
        beta_slow=beta_slow,
        max_position_embeddings=max_position_embeddings,
        unscaled_max_position_embeddings=original_max_position_embeddings,
    )
    try:
        sequence_length = target_token_count
        load_weights = True
        _ = load_weights
        if model_type == "debug":
            model = fastforward_llama_models.FastForwardLlama3Instruct70BModelFp8Debug(
                sequence_length=sequence_length,
                model_spec=model_spec,
                # load_weights=load_weights,
            )
        elif model_type == "prod":
            model = fastforward_llama_models.FastForwardLlama3Instruct70BModelFp8(
                sequence_length=sequence_length,
                # load_weights=load_weights,
            )
        elif model_type == "3.1":
            model = fastforward_llama_models.FastForwardLlama31Instruct70b16kModelFp8(
                sequence_length=sequence_length,
                # load_weights=load_weights,
            )
        elif model_type == "llama.cpp":
            model = remote_models.LLaMA3_LLaMACPP_Model(
                url="http://127.0.0.1:8080", seq_length=target_token_count
            )
        model.load()
        print(
            f"TIME: Loaded model at {datetime.datetime.now(datetime.UTC) - start_time}"
        )
        tokenizer = megatron_tokenizer.LLama3InstructTokenizer()
        for key_type, key_width, value_type, value_width in [
            ("index", None, "alphabetic", 5),
            ("alphabetic", 5, "alphabetic", 5),
            ("index", None, "digital", 7),
            ("digital", 7, "digital", 7),
        ]:
            tokens, available_secret_count, keys, secrets, suffix = get_needle_stack(
                tokenizer=tokenizer,
                target_token_count=target_token_count,
                key_type=key_type,
                key_width=key_width,
                value_type=value_type,
                value_width=value_width,
            )

            recalls = []
            print(f"{available_secret_count=}")
            indices = get_indices(available_secret_count, sample_count)
            for i in indices:
                recall = recall_secret(
                    i=i,
                    tokens=tokens,
                    suffix=suffix,
                    keys=keys,
                    model=model,
                    tokenizer=tokenizer,
                    value_type=value_type,
                    value_width=value_width,
                    raw_generate=raw_generate,
                )
                print(i, keys[i], recall, secrets[i])
                recalls.append(recall)
                break

            print(len(recalls), len(secrets), len(indices))
            print(
                f"kv_type={key_type[0]}{key_width or '0'}{value_type[0]}{value_width} "
                f"Accuracy: {(np.array(recalls) == np.array(secrets)[indices]).mean()}"
            )
            print(f"TIME: {datetime.datetime.now(datetime.UTC) - start_time}")
    except KeyboardInterrupt:
        print("!!!!Handling KeyboardInterrupt")
        model.unload()
        raise
    model.unload()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Main function.")
    parser.add_argument("--target_token_count", type=int, required=True)
    parser.add_argument("--max_position_embeddings", type=int, default=16_384)
    parser.add_argument("--original_max_position_embeddings", type=int, default=None)
    parser.add_argument("--theta", type=float, required=True)
    parser.add_argument("--pos_factor", type=float, required=True)
    parser.add_argument("--uses_yarn", action=argparse.BooleanOptionalAction)
    parser.add_argument("--beta_fast", type=int, required=True)
    parser.add_argument("--beta_slow", type=int, required=True)
    parser.add_argument("--sample_count", type=int, required=True)
    parser.add_argument("--model_type", type=str, required=True)
    parser.add_argument("--raw_generate", type=bool)
    args = parser.parse_args()
    main(
        target_token_count=args.target_token_count,
        max_position_embeddings=args.max_position_embeddings,
        original_max_position_embeddings=args.original_max_position_embeddings,
        theta=args.theta,
        pos_factor=args.pos_factor,
        uses_yarn=args.uses_yarn,
        beta_fast=args.beta_fast,
        beta_slow=args.beta_slow,
        sample_count=args.sample_count,
        model_type=args.model_type,
        raw_generate=args.raw_generate,
    )

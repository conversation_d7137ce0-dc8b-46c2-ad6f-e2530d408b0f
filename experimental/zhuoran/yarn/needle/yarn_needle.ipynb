{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import argparse\n", "import datetime\n", "import random\n", "import re\n", "import string\n", "\n", "import numpy as np\n", "from megatron.tokenizer import tokenizer as megatron_tokenizer\n", "\n", "from research.core.data_paths import canonicalize_path\n", "from research.models import fastforward_llama_models, meta_model, remote_models\n", "from research.models.fastforward_llama_models import cached_attention, llama_model_specs\n", "from research.core import model_input\n", "\n", "_ = (\n", "    canonicalize_path,\n", "    fastforward_llama_models,\n", "    llama_model_specs,\n", "    meta_model,\n", "    remote_models,\n", "    cached_attention,\n", ")\n", "\n", "TEMPLATE = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are Aug<PERSON>, an AI code assistant.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "{user_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "\"\"\"\n", "\n", "\n", "def get_needle_stack(\n", "    tokenizer, target_token_count, key_type, key_width, value_type, value_width\n", "):\n", "    \"\"\"Generates a needle stack.\"\"\"\n", "    secret_count = 60_000\n", "    real_target_token_count = max(\n", "        int(target_token_count * 0.93), target_token_count - 768\n", "    )\n", "    suffix_length = 5\n", "\n", "    if key_type == \"index\":\n", "        keys = list(range(secret_count))\n", "    elif key_type == \"alphabetic\":\n", "        keys = [\n", "            \"\".join(random.choice(string.ascii_lowercase) for _ in range(key_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    elif key_type == \"digital\":\n", "        keys = [\n", "            \"\".join(random.choice(string.digits) for _ in range(key_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    if value_type == \"alphabetic\":\n", "        secrets = [\n", "            \"\".join(random.choice(string.ascii_lowercase) for _ in range(value_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    elif value_type == \"digital\":\n", "        secrets = [\n", "            \"\".join(random.choice(string.digits) for _ in range(value_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    full_string = \".\\n\".join(\n", "        f\"Secret {key}: {secret}\" for key, secret in zip(keys, secrets)\n", "    )\n", "    full_tokens = tokenizer.tokenize(TEMPLATE.format(user_message=full_string))\n", "\n", "    prefix = full_tokens[: real_target_token_count - suffix_length]\n", "    line_break = tokenizer.tokenize(\".\\n\")[0]\n", "    last_line_break = prefix[::-1].index(line_break)\n", "    second_last_line_break = prefix[::-1].index(line_break, last_line_break + 1)\n", "    third_last_line_break = prefix[::-1].index(line_break, second_last_line_break + 1)\n", "    if last_line_break == 0:\n", "        last_line_break = second_last_line_break\n", "        second_last_line_break = third_last_line_break\n", "    prefix = prefix[:-last_line_break]\n", "\n", "    suffix = full_tokens[-suffix_length:]\n", "    tokens = prefix + suffix\n", "    available_secret_count = len(\n", "        re.findall(\n", "            rf\"Secret [a-z0-9]{{1,10}}: [a-z0-9]{{{value_width}}}.\",\n", "            tokenizer.de<PERSON><PERSON>ze(tokens),\n", "        )\n", "    )\n", "    return tokens, available_secret_count, keys, secrets, suffix\n", "\n", "\n", "def recall_secret(\n", "    i, tokens, suffix, keys, model, tokenizer, value_type, value_width, raw_generate\n", "):\n", "    \"\"\"Asks the model to recall a secret.\"\"\"\n", "    if value_type == \"alphabetic\":\n", "        value_type = \"letter\"\n", "    elif value_type == \"digital\":\n", "        value_type = \"digit\"\n", "    probe = tokenizer.tokenize(\n", "        f\"Now recall from this list of secrets, what is secret {keys[i]}?\"\n", "        f\" Answer just the {value_width}-{value_type} secret and nothing more.\"\n", "        f\" Your answer must be only {value_width} long and all {value_type}s!\"\n", "    )\n", "    probing_tokens = tokens[: -len(suffix)] + probe + tokens[-len(suffix) :]\n", "\n", "    options = meta_model.GenerationOptions(\n", "        max_generated_tokens=20, temperature=0.0, top_k=0, top_p=0\n", "    )\n", "\n", "    if raw_generate:\n", "        output = model.raw_generate(probing_tokens, options)\n", "    else:\n", "        output = model.generate(\n", "            meta_model.ModelInput(prefix=tokenizer.detokenize(probing_tokens)), options\n", "        )\n", "    return output\n", "\n", "\n", "def get_indices(length, sample_count):\n", "    \"\"\"Selects first 5, last 5, and 10 evenly distributed indices in the middle.\"\"\"\n", "    all_ints = list(range(length))\n", "\n", "    first_5 = all_ints[:5]\n", "    last_5 = all_ints[-5:]\n", "    mid = all_ints[5:-5][:: ((length - 10) // (sample_count - 10)) or 1][\n", "        : sample_count - 10\n", "    ]\n", "\n", "    result = sorted(set(first_5 + mid + last_5))\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sequence_length = 16_384\n", "unscaled_sequence_length = 8_192\n", "scaling_factor = 4.0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.fastforward import cached_attention\n", "from base.fastforward.llama import model_specs as llama_model_specs\n", "from research.core.data_paths import canonicalize_path\n", "from research.models import remote_models, fastforward_llama_models, meta_model\n", "\n", "# model = remote_models.OpenAI_Model(\n", "#     api_key=\"\",\n", "#     openai_model_name=\"gpt-3.5-turbo\",\n", "# )\n", "# model = remote_models.LLaMA3_LLaMACPP_Model(\n", "#     url=\"http://127.0.0.1:8080\", seq_length=sequence_length,\n", "# )\n", "model_spec = llama_model_specs.LlamaModelSpec(\n", "    name=\"llama3-70b\",\n", "    checkpoint_path=str(\n", "        canonicalize_path(\"checkpoints/llama3/Meta-Llama-3-70B-Instruct-ff-fp8\")\n", "    ),\n", "    emb_dim=8_192,\n", "    num_layers=80,\n", "    num_heads=8,\n", "    num_queries_per_head=8,\n", "    head_dim=128,\n", "    attn_split_head_mode=cached_attention.SplitHeadModes.KV_HEADS,\n", "    rotary_theta=500_000.0,\n", "    rotary_scaling_factor=scaling_factor,\n", "    vocab_size=128_256,\n", "    mlp_dim_divisible_by=2_048,  # How to get this reliably?\n", "    ffn_dim_multiplier=1.3125,\n", "    norm_eps=1e-05,\n", "    max_position_embeddings=sequence_length,\n", "    unscaled_max_position_embeddings=unscaled_sequence_length,\n", ")\n", "# model = fastforward_llama_models.FastForwardLlama3Instruct70BModelFp8Debug(\n", "#     sequence_length=sequence_length,\n", "#     load_weights=True,\n", "#     model_spec=model_spec,\n", "# )\n", "# model = fastforward_llama_models.FastForwardLlama3Instruct70BModelFp8(\n", "#     sequence_length=16_384,\n", "#     load_weights=True,\n", "# )\n", "model = fastforward_llama_models.FastForwardLlama31Instruct70b16kModelFp8()\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer import tokenizer as megatron_tokenizer\n", "\n", "tokenizer = megatron_tokenizer.LLama3InstructTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["key_type, key_width, value_type, value_width = [\n", "    (\"index\", None, \"alphabetic\", 5),\n", "    # (\"alphabetic\", 5, \"alphabetic\", 5),\n", "    # (\"index\", None, \"digital\", 7),\n", "    # (\"digital\", 7, \"digital\", 7),\n", "][0]\n", "tokens, available_secret_count, keys, secrets, suffix = get_needle_stack(\n", "    tokenizer=tokenizer,\n", "    target_token_count=sequence_length,\n", "    key_type=key_type,\n", "    key_width=key_width,\n", "    value_type=value_type,\n", "    value_width=value_width,\n", ")\n", "\n", "recalls = []\n", "print(f\"{available_secret_count=}\")\n", "indices = get_indices(available_secret_count, sample_count)\n", "for i in indices:\n", "    recall = recall_secret(\n", "        i=i,\n", "        tokens=tokens,\n", "        suffix=suffix,\n", "        keys=keys,\n", "        model=model,\n", "        tokenizer=tokenizer,\n", "        value_type=value_type,\n", "        value_width=value_width,\n", "        raw_generate=raw_generate,\n", "    )\n", "    print(i, keys[i], recall, secrets[i])\n", "    recalls.append(recall)\n", "    break\n", "\n", "print(len(recalls), len(secrets), len(indices))\n", "print(\n", "    f\"kv_type={key_type[0]}{key_width or '0'}{value_type[0]}{value_width} \"\n", "    f\"Accuracy: {(np.array(recalls) == np.array(secrets)[indices]).mean()}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from base.fastforward import positional_embeddings\n", "\n", "\n", "# rotary_config = positional_embeddings.RotaryConfig(\n", "#     rotary_theta=500_000.0,\n", "#     rotary_ratio=1.0,\n", "#     rotary_scaling_factor=4.0,\n", "#     max_position_embeddings=16_384,\n", "#     unscaled_max_position_embeddings=4_096,\n", "# )\n", "# model.reload_attention(seq_length=16_384, rotary_config=rotary_config)\n", "# rope = model.generation_attn_cache._mc_attn._rotary_by_device[0]\n", "# model.seq_length, rope.rotary_scaling_factor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quick test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    model.generate(\n", "        meta_model.ModelInput(\n", "            chat_input=model_input.ChatInput(\n", "                history=[], request=\"Write a quick-sort algorithm in Python.\"\n", "            )\n", "            # prefix=\"Write a quick-sort algorithm in Python.\"\n", "        ),\n", "        meta_model.GenerationOptions(\n", "            max_generated_tokens=1000, temperature=0.0, top_k=0, top_p=0\n", "        ),\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    model.generate(\n", "        meta_model.ModelInput(\n", "            chat_input=model_input.ChatInput(\n", "                history=[], request=\"How to serialize a Python dataclass?\"\n", "            )\n", "            # prefix=\"Write a quick-sort algorithm in Python.\"\n", "        ),\n", "        meta_model.GenerationOptions(\n", "            max_generated_tokens=1000, temperature=0.0, top_k=0, top_p=0\n", "        ),\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import re\n", "import string\n", "\n", "TEMPLATE = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are Aug<PERSON>, an AI code assistant.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "{user_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "\"\"\"\n", "\n", "\n", "def get_needle_stack(tokenizer, key_type, key_width, value_type, value_width):\n", "    \"\"\"Generates a needle stack.\"\"\"\n", "    secret_count = 60_000\n", "    rough_target_token_count = 16_384\n", "    target_token_count = max(\n", "        int(rough_target_token_count * 0.93), rough_target_token_count - 768\n", "    )\n", "    suffix_length = 5\n", "\n", "    if key_type == \"index\":\n", "        keys = list(range(secret_count))\n", "    elif key_type == \"alphabetic\":\n", "        keys = [\n", "            \"\".join(random.choice(string.ascii_lowercase) for _ in range(key_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    elif key_type == \"digital\":\n", "        keys = [\n", "            \"\".join(random.choice(string.digits) for _ in range(key_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    if value_type == \"alphabetic\":\n", "        secrets = [\n", "            \"\".join(random.choice(string.ascii_lowercase) for _ in range(value_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    elif value_type == \"digital\":\n", "        secrets = [\n", "            \"\".join(random.choice(string.digits) for _ in range(value_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    full_string = \".\\n\".join(\n", "        f\"Secret {key}: {secret}\" for key, secret in zip(keys, secrets)\n", "    )\n", "    full_tokens = tokenizer.tokenize(TEMPLATE.format(user_message=full_string))\n", "\n", "    prefix = full_tokens[: target_token_count - suffix_length]\n", "    line_break = tokenizer.tokenize(\".\\n\")[0]\n", "    last_line_break = prefix[::-1].index(line_break)\n", "    second_last_line_break = prefix[::-1].index(line_break, last_line_break + 1)\n", "    third_last_line_break = prefix[::-1].index(line_break, second_last_line_break + 1)\n", "    if last_line_break == 0:\n", "        last_line_break = second_last_line_break\n", "        second_last_line_break = third_last_line_break\n", "    prefix = prefix[:-last_line_break]\n", "\n", "    suffix = full_tokens[-suffix_length:]\n", "    tokens = prefix + suffix\n", "    available_secret_count = len(\n", "        re.findall(\n", "            rf\"Secret [a-z0-9]{{1,10}}: [a-z0-9]{{{value_width}}}.\",\n", "            tokenizer.de<PERSON><PERSON>ze(tokens),\n", "        )\n", "    )\n", "    return tokens, available_secret_count, keys, secrets, suffix\n", "\n", "\n", "key_type = \"alphabetic\"\n", "key_width = 5\n", "value_type = \"alphabetic\"\n", "value_width = 5\n", "tokens, available_secret_count, keys, secrets, suffix = get_needle_stack(\n", "    tokenizer, key_type, key_width, value_type, value_width\n", ")\n", "print(available_secret_count)\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import model_input\n", "\n", "probe = tokenizer.tokenize(\n", "    f\"Now recall from this list of secrets, what is secret {keys[200]}? Answer just the {value_width}-{value_type} secret and nothing more.\"\n", ")\n", "probing_tokens = tokens[: -len(suffix)] + probe + tokens[-len(suffix) :]\n", "\n", "options = meta_model.GenerationOptions(\n", "    max_generated_tokens=20, temperature=0.0, top_k=0, top_p=0\n", ")\n", "\n", "print(len(probing_tokens))\n", "# output = model.generate(\n", "#     meta_model.ModelInput(\n", "#         # chat_input=model_input.ChatInput(\n", "#         #     history=[], request=tokenizer.detokenize(probing_tokens)\n", "#         # ),\n", "#         prefix=tokenizer.detokenize(probing_tokens),\n", "#     ),\n", "#     options,\n", "# )\n", "output = model.raw_generate(probing_tokens, options)\n", "print(output)\n", "print(tokenizer.tokenize(output))\n", "print(secrets[199:202])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def recall_secret(i, value_type, value_width):\n", "    probe = tokenizer.tokenize(\n", "        f\"Now recall from this list of secrets, what is secret {keys[i]}?\"\n", "        f\" Answer just the {value_width}-{value_type} secret and nothing more.\"\n", "        f\" Only the {value_width} {value_type}s! No additional words!\"\n", "    )\n", "    probing_tokens = tokens[: -len(suffix)] + probe + tokens[-len(suffix) :]\n", "\n", "    options = meta_model.GenerationOptions(\n", "        max_generated_tokens=20, temperature=0.0, top_k=0, top_p=0\n", "    )\n", "\n", "    output = model.raw_generate(probing_tokens, options)\n", "    # output = model.generate(\n", "    #     meta_model.ModelInput(prefix=tokenizer.detokenize(probing_tokens)), options\n", "    # )\n", "    return output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "\n", "def get_indices(length):\n", "    # Generate all integers in the range [0, x)\n", "    all_ints = list(range(length))\n", "\n", "    # Select the first 5, last 5, and 10 evenly distributed in the middle\n", "    first_5 = all_ints[:5]\n", "    last_5 = all_ints[-5:]\n", "    mid_10 = all_ints[5:-5][:: ((length - 10) // 10) or 1]\n", "\n", "    # Combine and sort the selected integers\n", "    result = sorted(set(first_5 + mid_10 + last_5))\n", "\n", "    return result\n", "\n", "\n", "recalls = []\n", "indices = get_indices(available_secret_count)\n", "for i in indices:\n", "    recall = recall_secret(i, value_type, value_width)\n", "    print(i, keys[i], recall, secrets[i])\n", "    recalls.append(recall)\n", "\n", "(np.array(recalls) == np.array(secrets)[indices]).mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Backlog"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import torch\n", "\n", "# from base.fastforward import positional_embeddings, positional_embeddings_backup\n", "\n", "# deepseek_head_dim = 128\n", "# deepseek_params = {\n", "#     \"rotary_theta\": 100000.0,\n", "#     \"position_interpolation_factor\": 4.0,\n", "#     \"rotary_pct\": 1.0,\n", "#     \"max_position_embeddings\": 16384,\n", "#     \"original_max_position_embeddings\": 4096,\n", "# }\n", "# llama3_head_dim = 128\n", "# llama3_params_ref = {\n", "#     \"rotary_theta\": 500000.0,\n", "#     \"position_interpolation_factor\": 1.0,\n", "#     \"rotary_pct\": 1.0,\n", "#     \"max_position_embeddings\": 8192,\n", "#     \"original_max_position_embeddings\": 8192,\n", "# }\n", "# llama3_params = {\n", "#     \"rotary_theta\": 500000.0,\n", "#     \"position_interpolation_factor\": 1.0,\n", "#     \"rotary_pct\": 1.0,\n", "#     \"max_position_embeddings\": 8192,\n", "#     \"original_max_position_embeddings\": 8192,\n", "# }\n", "# head_dim = llama3_head_dim\n", "# params = llama3_params\n", "\n", "# config_new = positional_embeddings.RotaryConfig(\n", "#     rotary_ratio=params[\"rotary_pct\"],\n", "#     rotary_theta=8_000_000,\n", "#     position_interpolation_factor=params[\"position_interpolation_factor\"],\n", "#     yarn_extrapolation_factor=1.0,\n", "#     max_position_embeddings=params[\"max_position_embeddings\"],\n", "#     original_max_position_embeddings=params[\"original_max_position_embeddings\"],\n", "# )\n", "# rotary_by_device_new = [\n", "#     positional_embeddings.FusedRotaryEmbedding(\n", "#         head_dim=head_dim,\n", "#         config=config_new,\n", "#         device=torch.device(f\"cuda:{i}\"),\n", "#     )\n", "#     for i in range(torch.cuda.device_count())\n", "# ]\n", "\n", "# config_backup = positional_embeddings_backup.RotaryConfig(\n", "#     rotary_ratio=params[\"rotary_pct\"],\n", "#     rotary_theta=params[\"rotary_theta\"],\n", "#     rotary_scaling_factor=params[\"position_interpolation_factor\"],\n", "#     max_position_embeddings=params[\"max_position_embeddings\"],\n", "# )\n", "# rotary_by_device_backup = [\n", "#     positional_embeddings_backup.FusedRotaryEmbedding(\n", "#         head_dim=head_dim,\n", "#         config=config_backup,\n", "#         device=torch.device(f\"cuda:{i}\"),\n", "#     )\n", "#     for i in range(torch.cuda.device_count())\n", "# ]\n", "\n", "# rotary_by_device = rotary_by_device_backup\n", "\n", "# model.generation_attn_cache._mc_attn.reload_rotary(rotary_by_device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import random\n", "# import string\n", "# import re\n", "\n", "\n", "# def t():\n", "#     secret_width = 5\n", "#     secret_count = 16_384\n", "#     rough_target_token_count = 13_000\n", "#     target_token_count = min(\n", "#         int(rough_target_token_count * 0.93), rough_target_token_count - 100\n", "#     )\n", "#     # target_token_count = 1_000\n", "#     suffix_length = 5\n", "\n", "#     # keys = list(range(secret_count))\n", "#     keys = [\n", "#         \"\".join(random.choice(string.ascii_lowercase) for _ in range(secret_width))\n", "#         for _ in range(secret_count)\n", "#     ]\n", "#     secrets = [\n", "#         \"\".join(random.choice(string.ascii_lowercase) for _ in range(secret_width))\n", "#         for _ in range(secret_count)\n", "#     ]\n", "#     full_string = \".\\n\".join(\n", "#         f\"Secret {key}: {secret}\" for key, secret in zip(keys, secrets)\n", "#     )\n", "#     full_tokens = tokenizer.tokenize(TEMPLATE.format(user_message=full_string))\n", "\n", "#     prefix = full_tokens[: target_token_count - suffix_length]\n", "#     original_prefix = prefix\n", "#     line_break = tokenizer.tokenize(\".\\n\")[0]\n", "#     last_line_break = prefix[::-1].index(line_break)\n", "#     second_last_line_break = prefix[::-1].index(line_break, last_line_break + 1)\n", "#     third_last_line_break = prefix[::-1].index(line_break, second_last_line_break + 1)\n", "#     if last_line_break == 0:\n", "#         last_line_break = second_last_line_break\n", "#         second_last_line_break = third_last_line_break\n", "#     last_line = prefix[-second_last_line_break:-last_line_break]\n", "#     prefix = prefix[:-last_line_break]\n", "\n", "#     suffix = full_tokens[-suffix_length:]\n", "#     # tokens = prefix + tokenizer.tokenize(\"ignore\") * (target_token_count - len(prefix) - len(suffix)) + suffix\n", "#     tokens = prefix + suffix\n", "#     available_secret_count = len(\n", "#         re.findall(r\"Secret [a-z0-9]{1,5}: [a-z]{5}.\", tokenizer.detokenize(tokens))\n", "#     )\n", "\n", "#     print(len(tokens))\n", "#     print(available_secret_count)\n", "#     print(tokenizer.detokenize(last_line))\n", "#     print(tokenizer.detok<PERSON>ze(tokens))\n", "#     return tokens, {\n", "#         \"target_token_count\": target_token_count,\n", "#         \"keys\": keys,\n", "#         \"secrets\": secrets,\n", "#         \"full_string\": full_string,\n", "#         \"full_tokens\": full_tokens,\n", "#         \"original_prefix\": original_prefix,\n", "#         \"prefix\": prefix,\n", "#         \"last_line_break\": last_line_break,\n", "#         \"second_last_line_break\": second_last_line_break,\n", "#         \"last_line\": last_line,\n", "#         \"suffix\": suffix,\n", "#         \"suffix_length\": suffix_length,\n", "#         \"available_secret_count\": available_secret_count,\n", "#     }\n", "\n", "\n", "# found = False\n", "# for _ in range(100):\n", "#     tokens, d = t()\n", "#     if len(tokens) < 200:\n", "#         print(\"Found it!\")\n", "#         found = True\n", "#         break\n", "# print(found)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tokenizer.tokenize(\".\\n\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# d[\"original_prefix\"][::-1].index(tokenizer.tokenize(\".\\n\")[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from collections import defaultdict\n", "# import csv\n", "# import re\n", "# import sys\n", "\n", "\n", "# def parse_to_csv(input_str, parsed_data, metric_keys):\n", "#     # Split the input string into lines\n", "#     lines = input_str.strip().split(\"\\n\")\n", "\n", "#     # Define a regular expression to extract the required fields\n", "#     pattern = re.compile(r\"^\\./([^:]+):(\\d+):([^ ]+) Accuracy: ([\\d\\.nan]+)$\")\n", "\n", "#     # Parse each line using the regular expression\n", "#     for line in lines:\n", "#         match = pattern.match(line)\n", "#         if match:\n", "#             file_info = match.group(1)\n", "#             metric_key = match.group(3)\n", "#             accuracy = match.group(4)\n", "\n", "#             # Split file_info to extract individual parameters\n", "#             params = file_info.split(\"_\")\n", "#             param_dict = {}\n", "#             for param in params:\n", "#                 if param.endswith(\".txt\"):\n", "#                     param = param[:-4]\n", "#                 if param.startswith(\"tf\"):\n", "#                     param_dict[\"Theta factor\"] = param[2:]\n", "#                 elif param.startswith(\"t\"):\n", "#                     param_dict[\"Theta base\"] = param[1:]\n", "#                 elif param.startswith(\"pf\"):\n", "#                     param_dict[\"Position factor\"] = param[2:]\n", "#                 elif param.startswith(\"ys\"):\n", "#                     param_dict[\"YaRN strength\"] = param[2:]\n", "#                 elif param.startswith(\"bf\"):\n", "#                     param_dict[\"Beta fast\"] = param[2:]\n", "#                 elif param.startswith(\"bs\"):\n", "#                     param_dict[\"Beta slow\"] = param[2:]\n", "\n", "#             # Create a dictionary for the current line's data\n", "#             metric_keys.add(metric_key)\n", "#             data = {\n", "#                 **param_dict,\n", "#                 metric_key: accuracy,\n", "#             }\n", "#             parsed_data[file_info].update(data)\n", "#     return parsed_data\n", "\n", "\n", "# # Example usage\n", "# input_str = \\\\\"\"\"\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs2.txt:245:kv_type=i0a5 Accuracy: 0.55\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs2.txt:267:kv_type=a5a5 Accuracy: 0.7619047619047619\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs2.txt:268:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs2.txt:269:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys1.5_bf32_bs1.txt:246:kv_type=i0a5 Accuracy: 0.2857142857142857\n", "# ./t500000_tf2.0_pf1.0_ys1.5_bf32_bs1.txt:268:kv_type=a5a5 Accuracy: 0.6666666666666666\n", "# ./t500000_tf2.0_pf1.0_ys1.5_bf32_bs1.txt:269:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys1.5_bf32_bs1.txt:270:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf4.0_pf1.0_ys1.0_bf32_bs1.txt:246:kv_type=i0a5 Accuracy: 0.8095238095238095\n", "# ./t500000_tf4.0_pf1.0_ys1.0_bf32_bs1.txt:268:kv_type=a5a5 Accuracy: 0.8571428571428571\n", "# ./t500000_tf4.0_pf1.0_ys1.0_bf32_bs1.txt:269:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf4.0_pf1.0_ys1.0_bf32_bs1.txt:270:kv_type=d7d7 Accuracy: nan\n", "# ./t32000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.5238095238095238\n", "# ./t32000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:kv_type=a5a5 Accuracy: 0.8095238095238095\n", "# ./t32000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:261:kv_type=i0d7 Accuracy: nan\n", "# ./t32000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:262:kv_type=d7d7 Accuracy: nan\n", "# ./t1000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.0\n", "# ./t1000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:kv_type=a5a5 Accuracy: 0.0\n", "# ./t1000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:261:kv_type=i0d7 Accuracy: nan\n", "# ./t1000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:262:kv_type=d7d7 Accuracy: nan\n", "# ./t16000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.8571428571428571\n", "# ./t16000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:kv_type=a5a5 Accuracy: 0.7142857142857143\n", "# ./t16000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:261:kv_type=i0d7 Accuracy: nan\n", "# ./t16000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:262:kv_type=d7d7 Accuracy: nan\n", "# ./t4000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.8571428571428571\n", "# ./t4000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:259:kv_type=a5a5 Accuracy: 0.9\n", "# ./t4000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:kv_type=i0d7 Accuracy: nan\n", "# ./t4000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:261:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf1.0_pf2.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.047619047619047616\n", "# ./t500000_tf1.0_pf2.0_ys0.0_bf32_bs1.txt:262:kv_type=a5a5 Accuracy: 0.19047619047619047\n", "# ./t500000_tf1.0_pf2.0_ys0.0_bf32_bs1.txt:263:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf1.0_pf2.0_ys0.0_bf32_bs1.txt:264:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf0.5_pf1.0_ys1.0_bf32_bs1.txt:246:kv_type=i0a5 Accuracy: 0.0\n", "# ./t500000_tf0.5_pf1.0_ys1.0_bf32_bs1.txt:268:kv_type=a5a5 Accuracy: 0.0\n", "# ./t500000_tf0.5_pf1.0_ys1.0_bf32_bs1.txt:269:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf0.5_pf1.0_ys1.0_bf32_bs1.txt:270:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf16_bs1.txt:245:kv_type=i0a5 Accuracy: 0.35\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf16_bs1.txt:267:kv_type=a5a5 Accuracy: 0.5238095238095238\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf16_bs1.txt:268:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf16_bs1.txt:269:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf1.0_pf1.0_ys1.0_bf32_bs1.txt:666:kv_type=i0a5 Accuracy: 0.0\n", "# ./t500000_tf1.0_pf1.0_ys1.0_bf32_bs1.txt:973:kv_type=a5a5 Accuracy: 0.0\n", "# ./t500000_tf1.0_pf1.0_ys1.0_bf32_bs1.txt:974:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf1.0_pf1.0_ys1.0_bf32_bs1.txt:975:kv_type=d7d7 Accuracy: nan\n", "# ./t250000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.0\n", "# ./t250000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:kv_type=a5a5 Accuracy: 0.0\n", "# ./t250000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:261:kv_type=i0d7 Accuracy: nan\n", "# ./t250000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:262:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf32_bs1.txt:245:kv_type=i0a5 Accuracy: 0.55\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf32_bs1.txt:267:kv_type=a5a5 Accuracy: 0.7619047619047619\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf32_bs1.txt:268:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf32_bs1.txt:269:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf64_bs1.txt:246:kv_type=i0a5 Accuracy: 0.5238095238095238\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf64_bs1.txt:267:kv_type=a5a5 Accuracy: 0.6\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf64_bs1.txt:268:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.0_bf64_bs1.txt:269:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:245:kv_type=i0a5 Accuracy: 0.45\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:267:kv_type=a5a5 Accuracy: 0.8571428571428571\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:268:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:269:kv_type=d7d7 Accuracy: nan\n", "# ./t8000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.8571428571428571\n", "# ./t8000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:kv_type=a5a5 Accuracy: 0.9523809523809523\n", "# ./t8000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:261:kv_type=i0d7 Accuracy: nan\n", "# ./t8000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:262:kv_type=d7d7 Accuracy: nan\n", "# ./t2000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:kv_type=i0a5 Accuracy: 0.8095238095238095\n", "# ./t2000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:kv_type=a5a5 Accuracy: 1.0\n", "# ./t2000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:261:kv_type=i0d7 Accuracy: nan\n", "# ./t2000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:262:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.5_bf32_bs1.txt:246:kv_type=i0a5 Accuracy: 0.3333333333333333\n", "# ./t500000_tf2.0_pf1.0_ys0.5_bf32_bs1.txt:268:kv_type=a5a5 Accuracy: 0.7142857142857143\n", "# ./t500000_tf2.0_pf1.0_ys0.5_bf32_bs1.txt:269:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys0.5_bf32_bs1.txt:270:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf8.0_pf1.0_ys1.0_bf32_bs1.txt:246:kv_type=i0a5 Accuracy: 0.6666666666666666\n", "# ./t500000_tf8.0_pf1.0_ys1.0_bf32_bs1.txt:268:kv_type=a5a5 Accuracy: 0.8571428571428571\n", "# ./t500000_tf8.0_pf1.0_ys1.0_bf32_bs1.txt:269:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf8.0_pf1.0_ys1.0_bf32_bs1.txt:270:kv_type=d7d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys1.0_bf32_bs1.txt:246:kv_type=i0a5 Accuracy: 0.47619047619047616\n", "# ./t500000_tf2.0_pf1.0_ys1.0_bf32_bs1.txt:268:kv_type=a5a5 Accuracy: 0.8095238095238095\n", "# ./t500000_tf2.0_pf1.0_ys1.0_bf32_bs1.txt:269:kv_type=i0d7 Accuracy: nan\n", "# ./t500000_tf2.0_pf1.0_ys1.0_bf32_bs1.txt:270:kv_type=d7d7 Accuracy: nan\n", "# \\\\\"\"\"\n", "\n", "# input_str1 = \\\\\"\"\"\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs2.txt:246:numeric_key=True Accuracy: 0.9523809523809523\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs2.txt:268:numeric_key=False Accuracy: 0.9047619047619048\n", "# ./t500000_tf4.0_pf1.0_ys1.0_bf32_bs1.txt:246:numeric_key=True Accuracy: 0.8095238095238095\n", "# ./t500000_tf4.0_pf1.0_ys1.0_bf32_bs1.txt:268:numeric_key=False Accuracy: 0.9047619047619048\n", "# ./t32000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:numeric_key=True Accuracy: 0.6666666666666666\n", "# ./t32000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:numeric_key=False Accuracy: 0.9523809523809523\n", "# ./t1000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:numeric_key=True Accuracy: 0.6190476190476191\n", "# ./t1000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:numeric_key=False Accuracy: 0.6666666666666666\n", "# ./t16000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:numeric_key=True Accuracy: 0.8095238095238095\n", "# ./t16000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:numeric_key=False Accuracy: 0.9523809523809523\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf64_bs1.txt:246:numeric_key=True Accuracy: 0.9523809523809523\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf64_bs1.txt:267:numeric_key=False Accuracy: 1.0\n", "# ./t4000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:numeric_key=True Accuracy: 0.8571428571428571\n", "# ./t4000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:259:numeric_key=False Accuracy: 1.0\n", "# ./t500000_tf1.0_pf2.0_ys0.0_bf32_bs1.txt:238:numeric_key=True Accuracy: 0.09523809523809523\n", "# ./t500000_tf1.0_pf2.0_ys0.0_bf32_bs1.txt:260:numeric_key=False Accuracy: 0.2857142857142857\n", "# ./t500000_tf0.5_pf1.0_ys1.0_bf32_bs1.txt:304:numeric_key=True Accuracy: 0.0\n", "# ./t500000_tf0.5_pf1.0_ys1.0_bf32_bs1.txt:326:numeric_key=False Accuracy: 0.0\n", "# ./t500000_tf1.0_pf1.0_ys1.0_bf32_bs1.txt:666:numeric_key=True Accuracy: 0.0\n", "# ./t500000_tf1.0_pf1.0_ys1.0_bf32_bs1.txt:1108:numeric_key=False Accuracy: 0.0\n", "# ./t250000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:658:numeric_key=True Accuracy: 0.0\n", "# ./t250000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:733:numeric_key=False Accuracy: 0.0\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:246:numeric_key=True Accuracy: 0.8571428571428571\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:268:numeric_key=False Accuracy: 1.0\n", "# ./t8000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:numeric_key=True Accuracy: 0.7619047619047619\n", "# ./t8000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:numeric_key=False Accuracy: 0.9047619047619048\n", "# ./t2000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:238:numeric_key=True Accuracy: 0.8571428571428571\n", "# ./t2000000_tf1.0_pf1.0_ys0.0_bf32_bs1.txt:260:numeric_key=False Accuracy: 1.0\n", "# ./t500000_tf8.0_pf1.0_ys1.0_bf32_bs1.txt:245:numeric_key=True Accuracy: 0.65\n", "# ./t500000_tf8.0_pf1.0_ys1.0_bf32_bs1.txt:267:numeric_key=False Accuracy: 0.8095238095238095\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf16_bs1.txt:246:numeric_key=True Accuracy: 0.6666666666666666\n", "# ./t500000_tf1.0_pf1.0_ys0.0_bf16_bs1.txt:268:numeric_key=False Accuracy: 0.8571428571428571\n", "# ./t500000_tf1.0_pf1.0_ys1.5_bf32_bs1.txt:666:numeric_key=True Accuracy: 0.0\n", "# ./t500000_tf1.0_pf1.0_ys1.5_bf32_bs1.txt:1108:numeric_key=False Accuracy: 0.0\n", "# ./t500000_tf2.0_pf1.0_ys1.0_bf32_bs1.txt:246:numeric_key=True Accuracy: 0.8571428571428571\n", "# ./t500000_tf2.0_pf1.0_ys1.0_bf32_bs1.txt:268:numeric_key=False Accuracy: 0.9523809523809523\n", "# ./t500000_tf1.0_pf1.0_ys0.5_bf32_bs1.txt:666:numeric_key=True Accuracy: 0.0\n", "# ./t500000_tf1.0_pf1.0_ys0.5_bf32_bs1.txt:1108:numeric_key=False Accuracy: 0.0\n", "# \\\\\"\"\"\n", "\n", "# d = defaultdict(lambda: defaultdict(lambda: \"-\"))\n", "# m = set()\n", "# parse_to_csv(input_str, d, m)\n", "# parse_to_csv(input_str1, d, m)\n", "# d, m\n", "\n", "# columns = [\n", "#     \"Theta base\",\n", "#     \"Theta factor\",\n", "#     \"Position factor\",\n", "#     \"YaRN strength\",\n", "#     \"Beta fast\",\n", "#     \"Beta slow\",\n", "#     *m,\n", "# ]\n", "\n", "# # Write the parsed data to stdout\n", "# writer = csv.DictWriter(sys.stdout, fieldnames=columns)\n", "# writer.writeheader()\n", "# for row in d.values():\n", "#     writer.writerow(row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from megatron.tokenizer import tokenizer as megatron_tokenizer\n", "\n", "# tokenizer = megatron_tokenizer.LLama3InstructTokenizer()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
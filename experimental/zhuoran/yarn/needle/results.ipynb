{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["input_string = \"\"\"./s131072_pf1.0.txt:1277:kv_type=i0d7 Accuracy: 0.0\n", "./s131072_pf1.0.txt:1312:kv_type=d7d7 Accuracy: 0.0\n", "./s131072_pf1.0.txt:647:kv_type=i0a5 Accuracy: 0.0\n", "./s131072_pf1.0.txt:867:kv_type=a5a5 Accuracy: 0.0\n", "./s131072_pf16.0.txt:247:kv_type=i0a5 Accuracy: 0.7\n", "./s131072_pf16.0.txt:271:kv_type=a5a5 Accuracy: 0.8\n", "./s131072_pf16.0.txt:295:kv_type=i0d7 Accuracy: 0.7\n", "./s131072_pf16.0.txt:319:kv_type=d7d7 Accuracy: 0.8\n", "./s131072_pf2.0.txt:247:kv_type=i0a5 Accuracy: 0.4\n", "./s131072_pf2.0.txt:271:kv_type=a5a5 Accuracy: 0.7\n", "./s131072_pf2.0.txt:295:kv_type=i0d7 Accuracy: 0.5\n", "./s131072_pf2.0.txt:319:kv_type=d7d7 Accuracy: 0.55\n", "./s131072_pf32.0.txt:247:kv_type=i0a5 Accuracy: 0.6\n", "./s131072_pf32.0.txt:271:kv_type=a5a5 Accuracy: 0.85\n", "./s131072_pf32.0.txt:295:kv_type=i0d7 Accuracy: 0.6\n", "./s131072_pf32.0.txt:319:kv_type=d7d7 Accuracy: 0.65\n", "./s131072_pf4.0.txt:247:kv_type=i0a5 Accuracy: 0.85\n", "./s131072_pf4.0.txt:271:kv_type=a5a5 Accuracy: 1.0\n", "./s131072_pf4.0.txt:295:kv_type=i0d7 Accuracy: 0.85\n", "./s131072_pf4.0.txt:319:kv_type=d7d7 Accuracy: 1.0\n", "./s131072_pf64.0.txt:247:kv_type=i0a5 Accuracy: 0.15\n", "./s131072_pf64.0.txt:271:kv_type=a5a5 Accuracy: 0.05\n", "./s131072_pf64.0.txt:295:kv_type=i0d7 Accuracy: 0.2\n", "./s131072_pf64.0.txt:319:kv_type=d7d7 Accuracy: 0.3\n", "./s131072_pf8.0.txt:247:kv_type=i0a5 Accuracy: 0.85\n", "./s131072_pf8.0.txt:271:kv_type=a5a5 Accuracy: 0.95\n", "./s131072_pf8.0.txt:295:kv_type=i0d7 Accuracy: 0.7\n", "./s131072_pf8.0.txt:319:kv_type=d7d7 Accuracy: 0.95\n", "./s16384_pf1.0.txt:1267:kv_type=i0d7 Accuracy: 0.0\n", "./s16384_pf1.0.txt:1340:kv_type=d7d7 Accuracy: 0.0\n", "./s16384_pf1.0.txt:647:kv_type=i0a5 Accuracy: 0.0\n", "./s16384_pf1.0.txt:858:kv_type=a5a5 Accuracy: 0.0\n", "./s16384_pf16.0.txt:247:kv_type=i0a5 Accuracy: 0.75\n", "./s16384_pf16.0.txt:271:kv_type=a5a5 Accuracy: 0.9\n", "./s16384_pf16.0.txt:295:kv_type=i0d7 Accuracy: 0.65\n", "./s16384_pf16.0.txt:319:kv_type=d7d7 Accuracy: 0.75\n", "./s16384_pf2.0.txt:247:kv_type=i0a5 Accuracy: 0.5\n", "./s16384_pf2.0.txt:271:kv_type=a5a5 Accuracy: 0.65\n", "./s16384_pf2.0.txt:295:kv_type=i0d7 Accuracy: 0.45\n", "./s16384_pf2.0.txt:319:kv_type=d7d7 Accuracy: 0.7\n", "./s16384_pf32.0.txt:247:kv_type=i0a5 Accuracy: 0.55\n", "./s16384_pf32.0.txt:271:kv_type=a5a5 Accuracy: 0.6\n", "./s16384_pf32.0.txt:295:kv_type=i0d7 Accuracy: 0.65\n", "./s16384_pf32.0.txt:319:kv_type=d7d7 Accuracy: 0.4\n", "./s16384_pf4.0.txt:247:kv_type=i0a5 Accuracy: 0.9\n", "./s16384_pf4.0.txt:271:kv_type=a5a5 Accuracy: 0.95\n", "./s16384_pf4.0.txt:295:kv_type=i0d7 Accuracy: 0.85\n", "./s16384_pf4.0.txt:319:kv_type=d7d7 Accuracy: 0.95\n", "./s16384_pf64.0.txt:247:kv_type=i0a5 Accuracy: 0.05\n", "./s16384_pf64.0.txt:271:kv_type=a5a5 Accuracy: 0.1\n", "./s16384_pf64.0.txt:295:kv_type=i0d7 Accuracy: 0.25\n", "./s16384_pf64.0.txt:319:kv_type=d7d7 Accuracy: 0.2\n", "./s16384_pf8.0.txt:247:kv_type=i0a5 Accuracy: 0.85\n", "./s16384_pf8.0.txt:271:kv_type=a5a5 Accuracy: 1.0\n", "./s16384_pf8.0.txt:295:kv_type=i0d7 Accuracy: 0.85\n", "./s16384_pf8.0.txt:319:kv_type=d7d7 Accuracy: 0.95\n", "./s32768_pf1.0.txt:1201:kv_type=i0d7 Accuracy: 0.0\n", "./s32768_pf1.0.txt:1237:kv_type=d7d7 Accuracy: 0.0\n", "./s32768_pf1.0.txt:647:kv_type=i0a5 Accuracy: 0.0\n", "./s32768_pf1.0.txt:790:kv_type=a5a5 Accuracy: 0.0\n", "./s32768_pf16.0.txt:247:kv_type=i0a5 Accuracy: 0.8\n", "./s32768_pf16.0.txt:271:kv_type=a5a5 Accuracy: 0.9\n", "./s32768_pf16.0.txt:295:kv_type=i0d7 Accuracy: 0.75\n", "./s32768_pf16.0.txt:319:kv_type=d7d7 Accuracy: 0.85\n", "./s32768_pf2.0.txt:247:kv_type=i0a5 Accuracy: 0.4\n", "./s32768_pf2.0.txt:271:kv_type=a5a5 Accuracy: 0.6\n", "./s32768_pf2.0.txt:295:kv_type=i0d7 Accuracy: 0.35\n", "./s32768_pf2.0.txt:319:kv_type=d7d7 Accuracy: 0.65\n", "./s32768_pf32.0.txt:247:kv_type=i0a5 Accuracy: 0.7\n", "./s32768_pf32.0.txt:271:kv_type=a5a5 Accuracy: 0.75\n", "./s32768_pf32.0.txt:295:kv_type=i0d7 Accuracy: 0.55\n", "./s32768_pf32.0.txt:319:kv_type=d7d7 Accuracy: 0.6\n", "./s32768_pf4.0.txt:247:kv_type=i0a5 Accuracy: 0.95\n", "./s32768_pf4.0.txt:271:kv_type=a5a5 Accuracy: 1.0\n", "./s32768_pf4.0.txt:295:kv_type=i0d7 Accuracy: 0.75\n", "./s32768_pf4.0.txt:319:kv_type=d7d7 Accuracy: 0.95\n", "./s32768_pf64.0.txt:247:kv_type=i0a5 Accuracy: 0.25\n", "./s32768_pf64.0.txt:271:kv_type=a5a5 Accuracy: 0.0\n", "./s32768_pf64.0.txt:295:kv_type=i0d7 Accuracy: 0.25\n", "./s32768_pf64.0.txt:319:kv_type=d7d7 Accuracy: 0.2\n", "./s32768_pf8.0.txt:247:kv_type=i0a5 Accuracy: 0.85\n", "./s32768_pf8.0.txt:271:kv_type=a5a5 Accuracy: 0.95\n", "./s32768_pf8.0.txt:295:kv_type=i0d7 Accuracy: 0.65\n", "./s32768_pf8.0.txt:319:kv_type=d7d7 Accuracy: 1.0\n", "./s65536_pf1.0.txt:1184:kv_type=i0d7 Accuracy: 0.0\n", "./s65536_pf1.0.txt:1283:kv_type=d7d7 Accuracy: 0.0\n", "./s65536_pf1.0.txt:647:kv_type=i0a5 Accuracy: 0.0\n", "./s65536_pf1.0.txt:821:kv_type=a5a5 Accuracy: 0.0\n", "./s65536_pf16.0.txt:247:kv_type=i0a5 Accuracy: 0.8\n", "./s65536_pf16.0.txt:271:kv_type=a5a5 Accuracy: 0.95\n", "./s65536_pf16.0.txt:295:kv_type=i0d7 Accuracy: 0.7\n", "./s65536_pf16.0.txt:319:kv_type=d7d7 Accuracy: 0.8\n", "./s65536_pf2.0.txt:247:kv_type=i0a5 Accuracy: 0.45\n", "./s65536_pf2.0.txt:271:kv_type=a5a5 Accuracy: 0.55\n", "./s65536_pf2.0.txt:295:kv_type=i0d7 Accuracy: 0.35\n", "./s65536_pf2.0.txt:319:kv_type=d7d7 Accuracy: 0.55\n", "./s65536_pf32.0.txt:247:kv_type=i0a5 Accuracy: 0.6\n", "./s65536_pf32.0.txt:271:kv_type=a5a5 Accuracy: 0.6\n", "./s65536_pf32.0.txt:295:kv_type=i0d7 Accuracy: 0.5\n", "./s65536_pf32.0.txt:319:kv_type=d7d7 Accuracy: 0.4\n", "./s65536_pf4.0.txt:247:kv_type=i0a5 Accuracy: 0.9\n", "./s65536_pf4.0.txt:271:kv_type=a5a5 Accuracy: 0.95\n", "./s65536_pf4.0.txt:295:kv_type=i0d7 Accuracy: 0.9\n", "./s65536_pf4.0.txt:319:kv_type=d7d7 Accuracy: 1.0\n", "./s65536_pf64.0.txt:247:kv_type=i0a5 Accuracy: 0.2\n", "./s65536_pf64.0.txt:271:kv_type=a5a5 Accuracy: 0.0\n", "./s65536_pf64.0.txt:295:kv_type=i0d7 Accuracy: 0.25\n", "./s65536_pf64.0.txt:319:kv_type=d7d7 Accuracy: 0.3\n", "./s65536_pf8.0.txt:247:kv_type=i0a5 Accuracy: 0.85\n", "./s65536_pf8.0.txt:271:kv_type=a5a5 Accuracy: 1.0\n", "./s65536_pf8.0.txt:295:kv_type=i0d7 Accuracy: 0.8\n", "./s65536_pf8.0.txt:319:kv_type=d7d7 Accuracy: 0.85\"\"\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          s    pf  line kv_type   acc\n", "0    131072   1.0  1277    i0d7   0.0\n", "1    131072   1.0  1312    d7d7   0.0\n", "2    131072   1.0   647    i0a5   0.0\n", "3    131072   1.0   867    a5a5   0.0\n", "4    131072  16.0   247    i0a5   0.7\n", "..      ...   ...   ...     ...   ...\n", "107   65536  64.0   319    d7d7   0.3\n", "108   65536   8.0   247    i0a5  0.85\n", "109   65536   8.0   271    a5a5   1.0\n", "110   65536   8.0   295    i0d7   0.8\n", "111   65536   8.0   319    d7d7  0.85\n", "\n", "[112 rows x 5 columns]\n"]}], "source": ["import re\n", "import pandas as pd\n", "\n", "pattern = re.compile(\n", "    r\"./s(\\d+)_pf(\\d+\\.\\d+)\\.txt:(\\d+):kv_type=(\\w+) Accuracy: (\\d+\\.\\d+)\"\n", ")\n", "matches = [pattern.match(line).groups() for line in input_string.splitlines()]\n", "\n", "result = pd.DataFrame(matches, columns=[\"s\", \"pf\", \"line\", \"kv_type\", \"acc\"])\n", "\n", "print(result)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>s</th>\n", "      <th>pf</th>\n", "      <th>kv_type</th>\n", "      <th>acc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>131072</td>\n", "      <td>1.0</td>\n", "      <td>i0d7</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131072</td>\n", "      <td>1.0</td>\n", "      <td>d7d7</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>131072</td>\n", "      <td>1.0</td>\n", "      <td>i0a5</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>131072</td>\n", "      <td>1.0</td>\n", "      <td>a5a5</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>131072</td>\n", "      <td>16.0</td>\n", "      <td>i0a5</td>\n", "      <td>0.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>107</th>\n", "      <td>65536</td>\n", "      <td>64.0</td>\n", "      <td>d7d7</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>65536</td>\n", "      <td>8.0</td>\n", "      <td>i0a5</td>\n", "      <td>0.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109</th>\n", "      <td>65536</td>\n", "      <td>8.0</td>\n", "      <td>a5a5</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>65536</td>\n", "      <td>8.0</td>\n", "      <td>i0d7</td>\n", "      <td>0.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>65536</td>\n", "      <td>8.0</td>\n", "      <td>d7d7</td>\n", "      <td>0.85</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>112 rows × 4 columns</p>\n", "</div>"], "text/plain": ["          s    pf kv_type   acc\n", "0    131072   1.0    i0d7   0.0\n", "1    131072   1.0    d7d7   0.0\n", "2    131072   1.0    i0a5   0.0\n", "3    131072   1.0    a5a5   0.0\n", "4    131072  16.0    i0a5   0.7\n", "..      ...   ...     ...   ...\n", "107   65536  64.0    d7d7   0.3\n", "108   65536   8.0    i0a5  0.85\n", "109   65536   8.0    a5a5   1.0\n", "110   65536   8.0    i0d7   0.8\n", "111   65536   8.0    d7d7  0.85\n", "\n", "[112 rows x 4 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result = result[[\"s\", \"pf\", \"kv_type\", \"acc\"]]\n", "result"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>kv_type</th>\n", "      <th>s</th>\n", "      <th>pf</th>\n", "      <th>a5a5</th>\n", "      <th>d7d7</th>\n", "      <th>i0a5</th>\n", "      <th>i0d7</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>131072</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131072</td>\n", "      <td>16.0</td>\n", "      <td>0.80</td>\n", "      <td>0.80</td>\n", "      <td>0.70</td>\n", "      <td>0.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>131072</td>\n", "      <td>2.0</td>\n", "      <td>0.70</td>\n", "      <td>0.55</td>\n", "      <td>0.40</td>\n", "      <td>0.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>131072</td>\n", "      <td>32.0</td>\n", "      <td>0.85</td>\n", "      <td>0.65</td>\n", "      <td>0.60</td>\n", "      <td>0.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>131072</td>\n", "      <td>4.0</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>131072</td>\n", "      <td>64.0</td>\n", "      <td>0.05</td>\n", "      <td>0.30</td>\n", "      <td>0.15</td>\n", "      <td>0.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>131072</td>\n", "      <td>8.0</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.85</td>\n", "      <td>0.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>16384</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>16384</td>\n", "      <td>16.0</td>\n", "      <td>0.90</td>\n", "      <td>0.75</td>\n", "      <td>0.75</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>16384</td>\n", "      <td>2.0</td>\n", "      <td>0.65</td>\n", "      <td>0.70</td>\n", "      <td>0.50</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>16384</td>\n", "      <td>32.0</td>\n", "      <td>0.60</td>\n", "      <td>0.40</td>\n", "      <td>0.55</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>16384</td>\n", "      <td>4.0</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.90</td>\n", "      <td>0.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>16384</td>\n", "      <td>64.0</td>\n", "      <td>0.10</td>\n", "      <td>0.20</td>\n", "      <td>0.05</td>\n", "      <td>0.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>16384</td>\n", "      <td>8.0</td>\n", "      <td>1.00</td>\n", "      <td>0.95</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>32768</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>32768</td>\n", "      <td>16.0</td>\n", "      <td>0.90</td>\n", "      <td>0.85</td>\n", "      <td>0.80</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>32768</td>\n", "      <td>2.0</td>\n", "      <td>0.60</td>\n", "      <td>0.65</td>\n", "      <td>0.40</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>32768</td>\n", "      <td>32.0</td>\n", "      <td>0.75</td>\n", "      <td>0.60</td>\n", "      <td>0.70</td>\n", "      <td>0.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>32768</td>\n", "      <td>4.0</td>\n", "      <td>1.00</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>32768</td>\n", "      <td>64.0</td>\n", "      <td>0.00</td>\n", "      <td>0.20</td>\n", "      <td>0.25</td>\n", "      <td>0.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>32768</td>\n", "      <td>8.0</td>\n", "      <td>0.95</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>65536</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>65536</td>\n", "      <td>16.0</td>\n", "      <td>0.95</td>\n", "      <td>0.80</td>\n", "      <td>0.80</td>\n", "      <td>0.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>65536</td>\n", "      <td>2.0</td>\n", "      <td>0.55</td>\n", "      <td>0.55</td>\n", "      <td>0.45</td>\n", "      <td>0.35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>65536</td>\n", "      <td>32.0</td>\n", "      <td>0.60</td>\n", "      <td>0.40</td>\n", "      <td>0.60</td>\n", "      <td>0.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>65536</td>\n", "      <td>4.0</td>\n", "      <td>0.95</td>\n", "      <td>1.00</td>\n", "      <td>0.90</td>\n", "      <td>0.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>65536</td>\n", "      <td>64.0</td>\n", "      <td>0.00</td>\n", "      <td>0.30</td>\n", "      <td>0.20</td>\n", "      <td>0.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>65536</td>\n", "      <td>8.0</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "      <td>0.80</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["kv_type       s    pf  a5a5  d7d7  i0a5  i0d7\n", "0        131072   1.0  0.00  0.00  0.00  0.00\n", "1        131072  16.0  0.80  0.80  0.70  0.70\n", "2        131072   2.0  0.70  0.55  0.40  0.50\n", "3        131072  32.0  0.85  0.65  0.60  0.60\n", "4        131072   4.0  1.00  1.00  0.85  0.85\n", "5        131072  64.0  0.05  0.30  0.15  0.20\n", "6        131072   8.0  0.95  0.95  0.85  0.70\n", "7         16384   1.0  0.00  0.00  0.00  0.00\n", "8         16384  16.0  0.90  0.75  0.75  0.65\n", "9         16384   2.0  0.65  0.70  0.50  0.45\n", "10        16384  32.0  0.60  0.40  0.55  0.65\n", "11        16384   4.0  0.95  0.95  0.90  0.85\n", "12        16384  64.0  0.10  0.20  0.05  0.25\n", "13        16384   8.0  1.00  0.95  0.85  0.85\n", "14        32768   1.0  0.00  0.00  0.00  0.00\n", "15        32768  16.0  0.90  0.85  0.80  0.75\n", "16        32768   2.0  0.60  0.65  0.40  0.35\n", "17        32768  32.0  0.75  0.60  0.70  0.55\n", "18        32768   4.0  1.00  0.95  0.95  0.75\n", "19        32768  64.0  0.00  0.20  0.25  0.25\n", "20        32768   8.0  0.95  1.00  0.85  0.65\n", "21        65536   1.0  0.00  0.00  0.00  0.00\n", "22        65536  16.0  0.95  0.80  0.80  0.70\n", "23        65536   2.0  0.55  0.55  0.45  0.35\n", "24        65536  32.0  0.60  0.40  0.60  0.50\n", "25        65536   4.0  0.95  1.00  0.90  0.90\n", "26        65536  64.0  0.00  0.30  0.20  0.25\n", "27        65536   8.0  1.00  0.85  0.85  0.80"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["result[\"acc\"] = result[\"acc\"].astype(float)\n", "result_pivot = result.pivot_table(\n", "    index=[\"s\", \"pf\"], columns=\"kv_type\", values=\"acc\"\n", ").reset_index()\n", "result_pivot"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>kv_type</th>\n", "      <th>s</th>\n", "      <th>pf</th>\n", "      <th>a5a5</th>\n", "      <th>d7d7</th>\n", "      <th>i0a5</th>\n", "      <th>i0d7</th>\n", "      <th>mean</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>131072</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131072</td>\n", "      <td>16.0</td>\n", "      <td>0.80</td>\n", "      <td>0.80</td>\n", "      <td>0.70</td>\n", "      <td>0.70</td>\n", "      <td>0.7500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>131072</td>\n", "      <td>2.0</td>\n", "      <td>0.70</td>\n", "      <td>0.55</td>\n", "      <td>0.40</td>\n", "      <td>0.50</td>\n", "      <td>0.5375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>131072</td>\n", "      <td>32.0</td>\n", "      <td>0.85</td>\n", "      <td>0.65</td>\n", "      <td>0.60</td>\n", "      <td>0.60</td>\n", "      <td>0.6750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>131072</td>\n", "      <td>4.0</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "      <td>0.9250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>131072</td>\n", "      <td>64.0</td>\n", "      <td>0.05</td>\n", "      <td>0.30</td>\n", "      <td>0.15</td>\n", "      <td>0.20</td>\n", "      <td>0.1750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>131072</td>\n", "      <td>8.0</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.85</td>\n", "      <td>0.70</td>\n", "      <td>0.8625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>16384</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>16384</td>\n", "      <td>16.0</td>\n", "      <td>0.90</td>\n", "      <td>0.75</td>\n", "      <td>0.75</td>\n", "      <td>0.65</td>\n", "      <td>0.7625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>16384</td>\n", "      <td>2.0</td>\n", "      <td>0.65</td>\n", "      <td>0.70</td>\n", "      <td>0.50</td>\n", "      <td>0.45</td>\n", "      <td>0.5750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>16384</td>\n", "      <td>32.0</td>\n", "      <td>0.60</td>\n", "      <td>0.40</td>\n", "      <td>0.55</td>\n", "      <td>0.65</td>\n", "      <td>0.5500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>16384</td>\n", "      <td>4.0</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.90</td>\n", "      <td>0.85</td>\n", "      <td>0.9125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>16384</td>\n", "      <td>64.0</td>\n", "      <td>0.10</td>\n", "      <td>0.20</td>\n", "      <td>0.05</td>\n", "      <td>0.25</td>\n", "      <td>0.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>16384</td>\n", "      <td>8.0</td>\n", "      <td>1.00</td>\n", "      <td>0.95</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "      <td>0.9125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>32768</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>32768</td>\n", "      <td>16.0</td>\n", "      <td>0.90</td>\n", "      <td>0.85</td>\n", "      <td>0.80</td>\n", "      <td>0.75</td>\n", "      <td>0.8250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>32768</td>\n", "      <td>2.0</td>\n", "      <td>0.60</td>\n", "      <td>0.65</td>\n", "      <td>0.40</td>\n", "      <td>0.35</td>\n", "      <td>0.5000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>32768</td>\n", "      <td>32.0</td>\n", "      <td>0.75</td>\n", "      <td>0.60</td>\n", "      <td>0.70</td>\n", "      <td>0.55</td>\n", "      <td>0.6500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>32768</td>\n", "      <td>4.0</td>\n", "      <td>1.00</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.75</td>\n", "      <td>0.9125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>32768</td>\n", "      <td>64.0</td>\n", "      <td>0.00</td>\n", "      <td>0.20</td>\n", "      <td>0.25</td>\n", "      <td>0.25</td>\n", "      <td>0.1750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>32768</td>\n", "      <td>8.0</td>\n", "      <td>0.95</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.65</td>\n", "      <td>0.8625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>65536</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>65536</td>\n", "      <td>16.0</td>\n", "      <td>0.95</td>\n", "      <td>0.80</td>\n", "      <td>0.80</td>\n", "      <td>0.70</td>\n", "      <td>0.8125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>65536</td>\n", "      <td>2.0</td>\n", "      <td>0.55</td>\n", "      <td>0.55</td>\n", "      <td>0.45</td>\n", "      <td>0.35</td>\n", "      <td>0.4750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>65536</td>\n", "      <td>32.0</td>\n", "      <td>0.60</td>\n", "      <td>0.40</td>\n", "      <td>0.60</td>\n", "      <td>0.50</td>\n", "      <td>0.5250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>65536</td>\n", "      <td>4.0</td>\n", "      <td>0.95</td>\n", "      <td>1.00</td>\n", "      <td>0.90</td>\n", "      <td>0.90</td>\n", "      <td>0.9375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>65536</td>\n", "      <td>64.0</td>\n", "      <td>0.00</td>\n", "      <td>0.30</td>\n", "      <td>0.20</td>\n", "      <td>0.25</td>\n", "      <td>0.1875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>65536</td>\n", "      <td>8.0</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "      <td>0.80</td>\n", "      <td>0.8750</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["kv_type       s    pf  a5a5  d7d7  i0a5  i0d7    mean\n", "0        131072   1.0  0.00  0.00  0.00  0.00  0.0000\n", "1        131072  16.0  0.80  0.80  0.70  0.70  0.7500\n", "2        131072   2.0  0.70  0.55  0.40  0.50  0.5375\n", "3        131072  32.0  0.85  0.65  0.60  0.60  0.6750\n", "4        131072   4.0  1.00  1.00  0.85  0.85  0.9250\n", "5        131072  64.0  0.05  0.30  0.15  0.20  0.1750\n", "6        131072   8.0  0.95  0.95  0.85  0.70  0.8625\n", "7         16384   1.0  0.00  0.00  0.00  0.00  0.0000\n", "8         16384  16.0  0.90  0.75  0.75  0.65  0.7625\n", "9         16384   2.0  0.65  0.70  0.50  0.45  0.5750\n", "10        16384  32.0  0.60  0.40  0.55  0.65  0.5500\n", "11        16384   4.0  0.95  0.95  0.90  0.85  0.9125\n", "12        16384  64.0  0.10  0.20  0.05  0.25  0.1500\n", "13        16384   8.0  1.00  0.95  0.85  0.85  0.9125\n", "14        32768   1.0  0.00  0.00  0.00  0.00  0.0000\n", "15        32768  16.0  0.90  0.85  0.80  0.75  0.8250\n", "16        32768   2.0  0.60  0.65  0.40  0.35  0.5000\n", "17        32768  32.0  0.75  0.60  0.70  0.55  0.6500\n", "18        32768   4.0  1.00  0.95  0.95  0.75  0.9125\n", "19        32768  64.0  0.00  0.20  0.25  0.25  0.1750\n", "20        32768   8.0  0.95  1.00  0.85  0.65  0.8625\n", "21        65536   1.0  0.00  0.00  0.00  0.00  0.0000\n", "22        65536  16.0  0.95  0.80  0.80  0.70  0.8125\n", "23        65536   2.0  0.55  0.55  0.45  0.35  0.4750\n", "24        65536  32.0  0.60  0.40  0.60  0.50  0.5250\n", "25        65536   4.0  0.95  1.00  0.90  0.90  0.9375\n", "26        65536  64.0  0.00  0.30  0.20  0.25  0.1875\n", "27        65536   8.0  1.00  0.85  0.85  0.80  0.8750"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["result_pivot[\"mean\"] = result_pivot.iloc[:, 2:].mean(axis=1)\n", "result_pivot"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>kv_type</th>\n", "      <th>s</th>\n", "      <th>pf</th>\n", "      <th>a5a5</th>\n", "      <th>d7d7</th>\n", "      <th>i0a5</th>\n", "      <th>i0d7</th>\n", "      <th>mean</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>65536</td>\n", "      <td>4.0</td>\n", "      <td>0.95</td>\n", "      <td>1.00</td>\n", "      <td>0.90</td>\n", "      <td>0.90</td>\n", "      <td>0.9375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>131072</td>\n", "      <td>4.0</td>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "      <td>0.9250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>16384</td>\n", "      <td>8.0</td>\n", "      <td>1.00</td>\n", "      <td>0.95</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "      <td>0.9125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>16384</td>\n", "      <td>4.0</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.90</td>\n", "      <td>0.85</td>\n", "      <td>0.9125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>32768</td>\n", "      <td>4.0</td>\n", "      <td>1.00</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.75</td>\n", "      <td>0.9125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>65536</td>\n", "      <td>8.0</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.85</td>\n", "      <td>0.80</td>\n", "      <td>0.8750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>131072</td>\n", "      <td>8.0</td>\n", "      <td>0.95</td>\n", "      <td>0.95</td>\n", "      <td>0.85</td>\n", "      <td>0.70</td>\n", "      <td>0.8625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>32768</td>\n", "      <td>8.0</td>\n", "      <td>0.95</td>\n", "      <td>1.00</td>\n", "      <td>0.85</td>\n", "      <td>0.65</td>\n", "      <td>0.8625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>32768</td>\n", "      <td>16.0</td>\n", "      <td>0.90</td>\n", "      <td>0.85</td>\n", "      <td>0.80</td>\n", "      <td>0.75</td>\n", "      <td>0.8250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>65536</td>\n", "      <td>16.0</td>\n", "      <td>0.95</td>\n", "      <td>0.80</td>\n", "      <td>0.80</td>\n", "      <td>0.70</td>\n", "      <td>0.8125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>16384</td>\n", "      <td>16.0</td>\n", "      <td>0.90</td>\n", "      <td>0.75</td>\n", "      <td>0.75</td>\n", "      <td>0.65</td>\n", "      <td>0.7625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>131072</td>\n", "      <td>16.0</td>\n", "      <td>0.80</td>\n", "      <td>0.80</td>\n", "      <td>0.70</td>\n", "      <td>0.70</td>\n", "      <td>0.7500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>131072</td>\n", "      <td>32.0</td>\n", "      <td>0.85</td>\n", "      <td>0.65</td>\n", "      <td>0.60</td>\n", "      <td>0.60</td>\n", "      <td>0.6750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>32768</td>\n", "      <td>32.0</td>\n", "      <td>0.75</td>\n", "      <td>0.60</td>\n", "      <td>0.70</td>\n", "      <td>0.55</td>\n", "      <td>0.6500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>16384</td>\n", "      <td>2.0</td>\n", "      <td>0.65</td>\n", "      <td>0.70</td>\n", "      <td>0.50</td>\n", "      <td>0.45</td>\n", "      <td>0.5750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>16384</td>\n", "      <td>32.0</td>\n", "      <td>0.60</td>\n", "      <td>0.40</td>\n", "      <td>0.55</td>\n", "      <td>0.65</td>\n", "      <td>0.5500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>131072</td>\n", "      <td>2.0</td>\n", "      <td>0.70</td>\n", "      <td>0.55</td>\n", "      <td>0.40</td>\n", "      <td>0.50</td>\n", "      <td>0.5375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>65536</td>\n", "      <td>32.0</td>\n", "      <td>0.60</td>\n", "      <td>0.40</td>\n", "      <td>0.60</td>\n", "      <td>0.50</td>\n", "      <td>0.5250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>32768</td>\n", "      <td>2.0</td>\n", "      <td>0.60</td>\n", "      <td>0.65</td>\n", "      <td>0.40</td>\n", "      <td>0.35</td>\n", "      <td>0.5000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>65536</td>\n", "      <td>2.0</td>\n", "      <td>0.55</td>\n", "      <td>0.55</td>\n", "      <td>0.45</td>\n", "      <td>0.35</td>\n", "      <td>0.4750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>65536</td>\n", "      <td>64.0</td>\n", "      <td>0.00</td>\n", "      <td>0.30</td>\n", "      <td>0.20</td>\n", "      <td>0.25</td>\n", "      <td>0.1875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>32768</td>\n", "      <td>64.0</td>\n", "      <td>0.00</td>\n", "      <td>0.20</td>\n", "      <td>0.25</td>\n", "      <td>0.25</td>\n", "      <td>0.1750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>131072</td>\n", "      <td>64.0</td>\n", "      <td>0.05</td>\n", "      <td>0.30</td>\n", "      <td>0.15</td>\n", "      <td>0.20</td>\n", "      <td>0.1750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>16384</td>\n", "      <td>64.0</td>\n", "      <td>0.10</td>\n", "      <td>0.20</td>\n", "      <td>0.05</td>\n", "      <td>0.25</td>\n", "      <td>0.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>16384</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>131072</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>32768</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>65536</td>\n", "      <td>1.0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["kv_type       s    pf  a5a5  d7d7  i0a5  i0d7    mean\n", "25        65536   4.0  0.95  1.00  0.90  0.90  0.9375\n", "4        131072   4.0  1.00  1.00  0.85  0.85  0.9250\n", "13        16384   8.0  1.00  0.95  0.85  0.85  0.9125\n", "11        16384   4.0  0.95  0.95  0.90  0.85  0.9125\n", "18        32768   4.0  1.00  0.95  0.95  0.75  0.9125\n", "27        65536   8.0  1.00  0.85  0.85  0.80  0.8750\n", "6        131072   8.0  0.95  0.95  0.85  0.70  0.8625\n", "20        32768   8.0  0.95  1.00  0.85  0.65  0.8625\n", "15        32768  16.0  0.90  0.85  0.80  0.75  0.8250\n", "22        65536  16.0  0.95  0.80  0.80  0.70  0.8125\n", "8         16384  16.0  0.90  0.75  0.75  0.65  0.7625\n", "1        131072  16.0  0.80  0.80  0.70  0.70  0.7500\n", "3        131072  32.0  0.85  0.65  0.60  0.60  0.6750\n", "17        32768  32.0  0.75  0.60  0.70  0.55  0.6500\n", "9         16384   2.0  0.65  0.70  0.50  0.45  0.5750\n", "10        16384  32.0  0.60  0.40  0.55  0.65  0.5500\n", "2        131072   2.0  0.70  0.55  0.40  0.50  0.5375\n", "24        65536  32.0  0.60  0.40  0.60  0.50  0.5250\n", "16        32768   2.0  0.60  0.65  0.40  0.35  0.5000\n", "23        65536   2.0  0.55  0.55  0.45  0.35  0.4750\n", "26        65536  64.0  0.00  0.30  0.20  0.25  0.1875\n", "19        32768  64.0  0.00  0.20  0.25  0.25  0.1750\n", "5        131072  64.0  0.05  0.30  0.15  0.20  0.1750\n", "12        16384  64.0  0.10  0.20  0.05  0.25  0.1500\n", "7         16384   1.0  0.00  0.00  0.00  0.00  0.0000\n", "0        131072   1.0  0.00  0.00  0.00  0.00  0.0000\n", "14        32768   1.0  0.00  0.00  0.00  0.00  0.0000\n", "21        65536   1.0  0.00  0.00  0.00  0.00  0.0000"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["result_pivot = result_pivot.sort_values(by=\"mean\", ascending=False)\n", "result_pivot"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
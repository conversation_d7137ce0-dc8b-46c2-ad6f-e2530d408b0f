{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import argparse\n", "import datetime\n", "import random\n", "import re\n", "import string\n", "\n", "import numpy as np\n", "from megatron.tokenizer import tokenizer as megatron_tokenizer\n", "\n", "from research.core.data_paths import canonicalize_path\n", "from research.models import fastforward_llama_models, meta_model, remote_models\n", "from research.models.fastforward_llama_models import cached_attention, llama_model_specs\n", "\n", "_ = (\n", "    canonicalize_path,\n", "    fastforward_llama_models,\n", "    llama_model_specs,\n", "    meta_model,\n", "    remote_models,\n", "    cached_attention,\n", ")\n", "\n", "TEMPLATE = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are Aug<PERSON>, an AI code assistant.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "{user_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_needle_stack(\n", "    tokenizer, target_token_count, key_type, key_width, value_type, value_width\n", "):\n", "    \"\"\"Generates a needle stack.\"\"\"\n", "    secret_count = target_token_count // 5\n", "    real_target_token_count = max(\n", "        int(target_token_count * 0.93), target_token_count - 768\n", "    )\n", "    suffix_length = 5\n", "\n", "    if key_type == \"index\":\n", "        keys = list(range(secret_count))\n", "    elif key_type == \"alphabetic\":\n", "        keys = [\n", "            \"\".join(random.choice(string.ascii_lowercase) for _ in range(key_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    elif key_type == \"digital\":\n", "        keys = [\n", "            \"\".join(random.choice(string.digits) for _ in range(key_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    if value_type == \"alphabetic\":\n", "        secrets = [\n", "            \"\".join(random.choice(string.ascii_lowercase) for _ in range(value_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    elif value_type == \"digital\":\n", "        secrets = [\n", "            \"\".join(random.choice(string.digits) for _ in range(value_width))\n", "            for _ in range(secret_count)\n", "        ]\n", "    full_string = \".\\n\".join(\n", "        f\"Secret {key}: {secret}\" for key, secret in zip(keys, secrets)\n", "    )\n", "    full_tokens = tokenizer.tokenize(TEMPLATE.format(user_message=full_string))\n", "\n", "    prefix = full_tokens[: real_target_token_count - suffix_length]\n", "    line_break = tokenizer.tokenize(\".\\n\")[0]\n", "    last_line_break = prefix[::-1].index(line_break)\n", "    second_last_line_break = prefix[::-1].index(line_break, last_line_break + 1)\n", "    third_last_line_break = prefix[::-1].index(line_break, second_last_line_break + 1)\n", "    if last_line_break == 0:\n", "        last_line_break = second_last_line_break\n", "        second_last_line_break = third_last_line_break\n", "    prefix = prefix[:-last_line_break]\n", "\n", "    suffix = full_tokens[-suffix_length:]\n", "    tokens = prefix + suffix\n", "    available_secret_count = len(\n", "        re.findall(\n", "            rf\"Secret [a-z0-9]{{1,10}}: [a-z0-9]{{{value_width}}}.\",\n", "            tokenizer.de<PERSON><PERSON>ze(tokens),\n", "        )\n", "    )\n", "    return tokens, available_secret_count, keys, secrets, suffix\n", "\n", "\n", "def recall_secret(i, tokens, suffix, keys, model, tokenizer, value_type, value_width):\n", "    \"\"\"Asks the model to recall a secret.\"\"\"\n", "    if value_type == \"alphabetic\":\n", "        value_type = \"letter\"\n", "    elif value_type == \"digital\":\n", "        value_type = \"digit\"\n", "    probe = tokenizer.tokenize(\n", "        f\"Now recall from this list of secrets, what is secret {keys[i]}?\"\n", "        f\" Answer just the {value_width}-{value_type} secret and nothing more.\"\n", "        f\" Your answer must be only {value_width} long and all {value_type}s!\"\n", "    )\n", "    probing_tokens = tokens[: -len(suffix)] + probe + tokens[-len(suffix) :]\n", "\n", "    options = meta_model.GenerationOptions(\n", "        max_generated_tokens=20, temperature=0.0, top_k=0, top_p=0\n", "    )\n", "    output = model.raw_generate(probing_tokens, options)\n", "    return output\n", "\n", "\n", "def get_indices(length, sample_count):\n", "    \"\"\"Selects first 5, last 5, and 10 evenly distributed indices in the middle.\"\"\"\n", "    all_ints = list(range(length))\n", "\n", "    first_5 = all_ints[:5]\n", "    last_5 = all_ints[-5:]\n", "    mid = all_ints[5:-5][:: ((length - 10) // (sample_count - 10)) or 1][\n", "        : sample_count - 10\n", "    ]\n", "\n", "    result = sorted(set(first_5 + mid + last_5))\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sequence_length = 131_072\n", "sample_count = 20\n", "\n", "start_time = datetime.datetime.now(datetime.UTC)\n", "# load_weight_options = {\"load_weights\": True}\n", "load_weight_options = {}\n", "model = fastforward_llama_models.FastForwardLlama31Instruct70b16kModelFp8(\n", "    sequence_length=sequence_length,\n", "    **load_weight_options,\n", ")\n", "model.load()\n", "print(f\"TIME: Loaded model at {datetime.datetime.now(datetime.UTC) - start_time}\")\n", "tokenizer = megatron_tokenizer.LLama3InstructTokenizer()\n", "for key_type, key_width, value_type, value_width in [\n", "    (\"index\", None, \"alphabetic\", 5),\n", "    (\"alphabetic\", 5, \"alphabetic\", 5),\n", "    (\"index\", None, \"digital\", 7),\n", "    (\"digital\", 7, \"digital\", 7),\n", "]:\n", "    tokens, available_secret_count, keys, secrets, suffix = get_needle_stack(\n", "        tokenizer=tokenizer,\n", "        target_token_count=sequence_length,\n", "        key_type=key_type,\n", "        key_width=key_width,\n", "        value_type=value_type,\n", "        value_width=value_width,\n", "    )\n", "\n", "    recalls = []\n", "    print(f\"{available_secret_count=}\")\n", "    indices = get_indices(available_secret_count, sample_count)\n", "    for i in indices:\n", "        recall = recall_secret(\n", "            i=i,\n", "            tokens=tokens,\n", "            suffix=suffix,\n", "            keys=keys,\n", "            model=model,\n", "            tokenizer=tokenizer,\n", "            value_type=value_type,\n", "            value_width=value_width,\n", "        )\n", "        print(i, keys[i], recall, secrets[i])\n", "        recalls.append(recall)\n", "\n", "    print(len(recalls), len(secrets), len(indices))\n", "    print(\n", "        f\"kv_type={key_type[0]}{key_width or '0'}{value_type[0]}{value_width} \"\n", "        f\"Accuracy: {(np.array(recalls) == np.array(secrets)[indices]).mean()}\"\n", "    )\n", "    print(f\"TIME: {datetime.datetime.now(datetime.UTC) - start_time}\")\n", "model.unload()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
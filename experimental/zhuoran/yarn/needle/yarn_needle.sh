#!/bin/bash

TARGET_TOKEN_COUNT=16384
SAMPLE_COUNT=20

# rm -f ~/t.txt
# touch ~/t.txt
# time python3 c.py \
#     --target_token_count=${TARGET_TOKEN_COUNT} \
#     --theta=500000 \
#     --max_position_embeddings=16384 \
#     --original_max_position_embeddings=4096 \
#     --pos_factor=4.0 \
#     --no-uses_yarn \
#     --beta_fast=32 \
#     --beta_slow=1 \
#     --sample_count=${SAMPLE_COUNT} \
#     --model_type="prod" \
#     --raw_generate=true \
# | tee -a ~/t.txt

rm -f ~/t.txt
touch ~/t.txt
time python3 c.py \
    --target_token_count=16384 \
    --theta=500000 \
    --pos_factor=8.0 \
    --uses_yarn \
    --beta_fast=32 \
    --beta_slow=1 \
    --sample_count=${SAMPLE_COUNT} \
    --model_type="3.1" \
    --raw_generate=true \
| tee -a ~/t.txt

# for target_token_count in 32768 65536 131072
# do
#     for pos_factor in 4.0 8.0 16.0 32.0 64.0
#     do
#         echo "target_token_count: ${target_token_count}"
#         echo "pos_factor: ${pos_factor}"
#         time python3 c.py \
#             --target_token_count=${target_token_count} \
#             --max_position_embeddings=${target_token_count} \
#             --theta=500000 \
#             --pos_factor=${pos_factor} \
#             --uses_yarn \
#             --beta_fast=32 \
#             --beta_slow=1 \
#             --sample_count=${SAMPLE_COUNT} \
#             --model_type="debug" \
#             --raw_generate=true \
#         | tee results_d/s${target_token_count}_pf${pos_factor}.txt
#     done
# done

# for theta in 2000000 4000000 8000000
# do
#     echo "theta: ${theta}"
#     time python3 c.py \
#         --target_token_count=${TARGET_TOKEN_COUNT} \
#         --theta=${theta} \
#         --pos_factor=1.0 \
#         --no-uses_yarn \
#         --beta_fast=32 \
#         --beta_slow=1 \
#         --sample_count=${SAMPLE_COUNT} \
#         --model_type="debug" \
#         --raw_generate=true \
#     | tee results_d/t${theta}_tf1.0_pf1.0_ys0.0_bf32_bs1.txt
# done

# for beta_fast in 16 32 64
# do
#     echo "beta_fast: ${beta_fast}"
#     time python3 c.py \
#         --target_token_count=${TARGET_TOKEN_COUNT} \
#         --theta=500000 \
#         --pos_factor=2.0 \
#         --uses_yarn \
#         --beta_fast=${beta_fast} \
#         --beta_slow=1 \
#         --sample_count=${SAMPLE_COUNT} \
#         --model_type="debug" \
#         --raw_generate=true \
#     | tee results_d/t500000_tf2.0_pf1.0_ys0.0_bf${beta_fast}_bs1.txt
# done

# for beta_slow in 0.5 1 2
# do
#     echo "beta_slow: ${beta_slow}"
#     time python3 c.py \
#         --target_token_count=${TARGET_TOKEN_COUNT} \
#         --theta=500000 \
#         --pos_factor=2.0 \
#         --uses_yarn \
#         --beta_fast=32 \
#         --beta_slow=${beta_slow} \
#         --sample_count=${SAMPLE_COUNT} \
#         --model_type="debug" \
#         --raw_generate=true \
#     | tee results_d/t500000_tf1.0_pf1.0_ys0.0_bf32_bs${beta_slow}.txt
# done

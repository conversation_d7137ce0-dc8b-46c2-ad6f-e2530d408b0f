import json
from pathlib import Path


def fix_json_file(file_path):
    path = Path(file_path)

    # Read the file content
    with path.open("r", encoding="utf-16") as f:
        content = f.read()

    # Try to parse the JSON first
    try:
        data = json.loads(content)
        print(f"JSON file is already valid. {len(data)} items found.")
        return
    except json.JSONDecodeError:
        print("JSON is invalid. Attempting to fix...")

    # Remove all BOMs except at the beginning
    if content.startswith("\ufeff"):
        content = "\ufeff" + content[1:].replace("\ufeff", "")
    else:
        content = content.replace("\ufeff", "")

    content = content.rstrip()
    if content.endswith("},"):
        content = content[:-2]
        content += "}\n]"
    elif content.endswith("}"):
        content = content[:-1]
        content += "}\n]"

    # Try to parse the JSON
    try:
        data = json.loads(content)
        print(f"JSON file has been fixed. {len(data)} items found.")
    except json.JSONDecodeError as e:
        print(f"Failed to parse JSON: {e}")
        error_position = int(str(e).split("char ")[-1].split(")")[0])
        offending_char = content[error_position]
        print(
            f"Offending character: '{offending_char}', Unicode: U+{ord(offending_char):04X}"
        )
        return

    # Write back the cleaned and properly formatted content
    with path.open("w", encoding="utf-16") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    fix_json_file("/home/<USER>/zhuoran/commit_msgs_spark/commits_v5.json")

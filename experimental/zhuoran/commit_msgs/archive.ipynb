{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["total_file_count = 0\n", "total_no_type_file_count = 0\n", "total_commit_count = 0\n", "total_no_type_commit_count = 0\n", "no_type_rates = []\n", "for index, commit in enumerate(gh.commits):\n", "    file_count = 0\n", "    no_type_file_count = 0\n", "    for diff in commit[\"diffs\"].values():\n", "        if \"change_type\" not in diff:\n", "            no_type_file_count += 1\n", "        file_count += 1\n", "    if file_count == 0:\n", "        continue\n", "    total_commit_count += 1\n", "    total_no_type_commit_count += 1 if no_type_file_count > 0 else 0\n", "    no_type_rate = no_type_file_count / file_count\n", "    no_type_rates.append(no_type_rate)\n", "    total_file_count += file_count\n", "    total_no_type_file_count += no_type_file_count\n", "total_no_type_rate = total_no_type_file_count / total_file_count\n", "total_no_type_commit_rate = total_no_type_commit_count / total_commit_count\n", "print(f\"max_no_type_rate: {max(no_type_rates)}\")\n", "print(f\"min_no_type_rate: {min(no_type_rates)}\")\n", "print(f\"avg_no_type_rate: {sum(no_type_rates) / len(no_type_rates)}\")\n", "print(f\"total_no_type_rate: {total_no_type_rate}\")\n", "print(f\"total_no_type_commit_rate: {total_no_type_commit_rate}\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}
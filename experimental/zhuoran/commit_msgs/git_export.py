import json
import re
import sys
import time
from collections import defaultdict
from datetime import datetime

from git import Repo, exc
from tqdm import tqdm


def get_default_branch(repo):
    try:
        # Get the default branch name
        default_branch = repo.git.symbolic_ref("refs/remotes/origin/HEAD").split("/")[
            -1
        ]
    except exc.GitCommandError:
        # If the above fails, fall back to 'main' or 'master'
        if "main" in repo.heads:
            default_branch = "main"
        elif "master" in repo.heads:
            default_branch = "master"
        else:
            raise ValueError("Could not determine default branch")
    return default_branch


def count_commits(repo, branch=None, limit: int = 0) -> int:
    """Count total number of commits on the branch, up to limit if specified"""
    if not limit:
        return sum(1 for _ in repo.iter_commits(branch))
    return min(sum(1 for _ in repo.iter_commits(branch)), limit)


def get_commit_data(
    repo_path: str, start_commit: str | None, limit: int = 0, show_progress=True
):
    """
    Extract commits from main branch with their metadata and diff hunks.

    Args:
        repo_path (str): Path to the git repository
        start_commit (str | None): Commit hash to start from
        limit (int): Maximum number of commits to extract (0 for all commits)
        show_progress (bool): Whether to show progress bar

    Yields:
        dict: Commit data dictionary
    """
    repo = Repo(repo_path)

    # Count total commits for progress bar
    total_commits = count_commits(repo, limit=limit)
    commits = repo.iter_commits(get_default_branch(repo))

    progress_bar = None

    if show_progress:
        progress_bar = tqdm(total=total_commits, desc="Processing commits")

    started = False
    offset = 0
    try:
        for i, commit in enumerate(commits):
            if not started and (commit.hexsha == start_commit or not start_commit):
                offset = i
                started = True
            if not started:
                continue
            if limit > 0 and i - offset >= limit:
                break

            # Get the diff for this commit
            diff_data = defaultdict(dict)

            # Handle first commit case
            parent = commit.parents[0] if commit.parents else None

            if parent:
                git = commit.repo.git
                try:
                    for file_path in commit.stats.files:
                        diff_string = git.diff(
                            parent.hexsha, commit.hexsha, "--", file_path
                        )
                        hunks = re.split(r"(?=^@@)", diff_string, flags=re.MULTILINE)
                        diff_data[file_path] = {
                            "old_path": file_path,
                            "content": diff_string,
                            "header": hunks[0].strip(),
                            "insertions": commit.stats.files[file_path]["insertions"],
                            "deletions": commit.stats.files[file_path]["deletions"],
                            "lines": commit.stats.files[file_path]["lines"],
                            "hunks": [
                                {
                                    "insertions": sum(
                                        1
                                        for line in hunk.splitlines()
                                        if line.startswith("+")
                                    ),
                                    "deletions": sum(
                                        1
                                        for line in hunk.splitlines()
                                        if line.startswith("-")
                                    ),
                                    "lines": sum(
                                        1
                                        for line in hunk.splitlines()
                                        if line.startswith("+") or line.startswith("-")
                                    ),
                                    "content": hunk.strip(),
                                }
                                for hunk in hunks[1:]
                                if hunk.strip()
                            ],
                        }
                except Exception as e:
                    print(
                        f"Error processing diff for commit {commit.hexsha}: {str(e)}",
                        file=sys.stderr,
                    )

                try:
                    change_types_string = git.diff(
                        "--name-status", parent.hexsha, commit.hexsha
                    )
                    for change_type_string in change_types_string.splitlines():
                        change_type_columns = change_type_string.split("\t")
                        change_type = change_type_columns[0]
                        file_path = change_type_columns[-1]
                        old_path = (
                            change_type_columns[1]
                            if len(change_type_columns) == 2
                            else file_path
                        )
                        if len(change_type_columns) > 3:
                            print(
                                f"Warning: {len(change_type_columns)} columns in change type string: {change_type_string}"
                            )
                        if file_path in diff_data:
                            diff_data[file_path]["change_type"] = change_type
                            diff_data[file_path]["old_path"] = old_path
                        else:
                            print(f"{file_path} not in diff_data. Existing keys:")
                            for key in diff_data:
                                print(f"  {key}")
                except Exception as e:
                    print(
                        f"Error processing change types for commit {commit.hexsha}: {str(e)}",
                        file=sys.stderr,
                    )
                    if "too many values to unpack" in str(e):
                        string_splits = change_type_string.split("\t")
                        print(
                            f"{len(string_splits)=}",
                            file=sys.stderr,
                        )
                        for string_split in string_splits:
                            print(f"{string_split=}", file=sys.stderr)

            # Build commit metadata
            commit_info = {
                "current_hash": commit.hexsha,
                "parent_hashes": [p.hexsha for p in commit.parents],
                **commit.stats.total,
                "author": {"name": commit.author.name, "email": commit.author.email},
                "authored_date": datetime.fromtimestamp(
                    commit.authored_date
                ).isoformat(),
                "committer": {
                    "name": commit.committer.name,
                    "email": commit.committer.email,
                },
                "committed_date": datetime.fromtimestamp(
                    commit.committed_date
                ).isoformat(),
                "message": commit.message,
                "diffs": dict(diff_data),  # Make sure this line is present
            }

            yield commit_info

            if progress_bar:
                progress_bar.update(1)

    finally:
        if progress_bar:
            progress_bar.close()


def export_commits_to_json(
    repo_path: str,
    start_commit: str | None,
    output_file: str | None,
    limit: int = 0,
    batch_size: int = 500,
    enable_timer: bool = False,
):
    """
    Export commit data to a JSON file or stdout, appending batches of commits at a time.

    Args:
        repo_path: Path to the git repository
        output_file: Path to save the JSON output, or None for stdout
        start_commit: Commit hash to start from
        limit: Maximum number of commits to extract (0 for all commits)
        batch_size: Number of commits to process in each batch (default: 500)
        enable_timer: Whether to enable timing and index printing (default: False)
    """
    if output_file is not None:
        # Clear the file and write the opening bracket
        with open(output_file, "wb") as f:  # Open in binary write mode
            f.write("[\n".encode("utf-16", "replace"))
    else:
        print("[")

    commit_data_generator = get_commit_data(
        repo_path, start_commit=start_commit, limit=limit
    )
    batch = []
    total_commits = 0
    batch_start_time = time.time()
    start_time = time.time()

    for commit in commit_data_generator:
        batch.append(commit)
        total_commits += 1

        if len(batch) == batch_size:
            if enable_timer:
                batch_end_time = time.time()
                batch_time = batch_end_time - batch_start_time
                total_time = batch_end_time - start_time
                print(
                    f"Batch {total_commits - batch_size + 1} to {total_commits} processed in {batch_time:.2f} seconds ({total_time:.2f}s total)"
                )

            _write_batch(batch, output_file, total_commits)
            batch = []
            batch_start_time = time.time()

    # Write any remaining commits
    if batch:
        if enable_timer:
            batch_end_time = time.time()
            batch_time = batch_end_time - batch_start_time
            print(
                f"Final batch {total_commits - len(batch) + 1} to {total_commits} processed in {batch_time:.2f} seconds ({total_time:.2f}s total)"
            )

        _write_batch(batch, output_file, total_commits)

    # Add the final closing bracket
    if output_file is not None:
        with open(output_file, "ab") as f:  # Open in binary append mode
            f.write("\n]".encode("utf-16", "replace"))
    else:
        print("\n]")

    if enable_timer:
        print(f"Total commits processed: {total_commits}")
    print(f"Saved results to {output_file}")


def _write_batch(batch, output_file, total_commits):
    """Helper function to write a batch of commits to the file or stdout."""
    if output_file is None:
        for i, commit in enumerate(batch):
            json_str = json.dumps(commit, indent=2, ensure_ascii=False)
            if total_commits - len(batch) + i > 0:
                print(",")
            print(json_str)
    else:
        with open(output_file, "ab") as f:  # Open in binary append mode
            for i, commit in enumerate(batch):
                json_str = json.dumps(commit, indent=2, ensure_ascii=False)
                if total_commits - len(batch) + i > 0:
                    f.write(",\n".encode("utf-16", "replace"))
                f.write(json_str.encode("utf-16", "replace"))


if __name__ == "__main__":
    export_commits_to_json(
        repo_path="/home/<USER>/spark",
        start_commit=None,
        output_file="/home/<USER>/zhuoran/commit_msgs_spark/commits_v5.json",
        limit=0,
        batch_size=10,  # You can change this value as needed
        enable_timer=True,  # Enable timing and index printing
    )

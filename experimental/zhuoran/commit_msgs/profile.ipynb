{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from experimental.zhuoran.commit_msgs.git_history import MultiGitHistory\n", "import time\n", "\n", "\n", "class TokenCounterWrapper:\n", "    def __init__(self, inner_counter):\n", "        self.inner_counter = inner_counter\n", "        self.token_count = 0\n", "        self.char_count = 0\n", "        self.ratios = []\n", "\n", "    def count_tokens(self, prompt_chars: str) -> int:\n", "        token_count = self.inner_counter.count_tokens(prompt_chars)\n", "        self.token_count += token_count\n", "        self.char_count += len(prompt_chars)\n", "        if len(prompt_chars) > 0:\n", "            self.ratios.append(token_count / len(prompt_chars))\n", "        return token_count\n", "\n", "\n", "class RoughTokenCounter:\n", "    def __init__(self):\n", "        self.timings = {\n", "            \"Length getting\": 0.0,\n", "            \"Division\": 0.0,\n", "            \"Integer cast\": 0.0,\n", "        }\n", "\n", "    def count_tokens(self, prompt_chars: str) -> int:\n", "        start_time = time.time()\n", "        length = len(prompt_chars)\n", "        self.timings[\"Length getting\"] += time.time() - start_time\n", "        start_time = time.time()\n", "        result = length / 3\n", "        self.timings[\"Division\"] += time.time() - start_time\n", "        start_time = time.time()\n", "        result = int(result)\n", "        self.timings[\"Integer cast\"] += time.time() - start_time\n", "        return result\n", "\n", "\n", "claude_token_counter = ClaudeTokenCounter()\n", "wrapped_token_counter = TokenCounterWrapper(ClaudeTokenCounter())\n", "rough_token_counter = RoughTokenCounter()\n", "token_counter = rough_token_counter\n", "\n", "mgh = MultiGitHistory.from_jsons(\n", "    {\n", "        \"augment_recent\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/eval_set_v1_new_keys.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json\",\n", "            \"/home/<USER>/augment/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"augment\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json\",\n", "            \"/home/<USER>/augment/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"angular\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_angular/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_angular/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_angular/git_structure_v1.json\",\n", "            \"/home/<USER>/angular/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"pytorch\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_pytorch/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_pytorch/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_pytorch/git_structure_v1.json\",\n", "            \"/home/<USER>/pytorch/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"linux\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_linux/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_linux/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_linux/git_structure_v1.json\",\n", "            \"/home/<USER>/linux/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"wine\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_wine/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_wine/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_wine/git_structure_v1.json\",\n", "            \"/home/<USER>/wine/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"beauty_net\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_beauty_net/commits_v4.json\",\n", "            \"/mnt/efs/augment/user/zhu<PERSON>/commit_msgs_beauty_net/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_beauty_net/git_structure_v1.json\",\n", "            \"/home/<USER>/beauty-net/\",\n", "            \"utf-8\",\n", "        ),\n", "    },\n", "    token_counter.count_tokens,\n", ")\n", "mgh.get_lengths()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TRAILING_INSTRUCTIONS = \"\"\"Do not include boilerplate text like \"Here is the commit message:\" in your response..\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Angular\tLargest\t2\n", "# Angular\tPackages\t1\n", "# Angular\tPackages\t2\n", "# Angular\tPackages\t3\n", "# Angular\tPackages\t4\n", "# PyTorch\tDocs\t0\n", "# PyTorch\tTorch\t0\n", "# PyTorch\tTorch\t1\n", "# PyTorch\tTorch\t2\n", "# PyTorch\tTorch\t3\n", "# Linux\tLargest\t2\n", "# Linux\tKernel\t0\n", "# Linux\tMM\t0\n", "# Linux\tInit\t2\n", "# Wine\tServer\t1\n", "# Wine\tServer\t5\n", "# Wine\tServer\t7\n", "# BeautyNet\tBeauty\t0\n", "# Augment (recent)\tModels\t1\n", "# Augment (recent)\tExperimental\t0\n", "# Augment (recent)\tTools\t3\n", "# Augment (recent)\tSmallest\t3\n", "# Augment (recent)\tLargest\t4\n", "# Augment (recent)\tLargest\t0\n", "# Augment (recent)\tServices\t0\n", "core_eval_set = [\n", "    (\"angular\", \"largest_diff\", 2),\n", "    (\"angular\", \"packages\", 1),\n", "    (\"angular\", \"packages\", 2),\n", "    (\"angular\", \"packages\", 3),\n", "    (\"angular\", \"packages\", 4),\n", "    (\"pytorch\", \"docs\", 0),\n", "    (\"pytorch\", \"torch\", 0),\n", "    (\"pytorch\", \"torch\", 1),\n", "    (\"pytorch\", \"torch\", 2),\n", "    (\"pytorch\", \"torch\", 3),\n", "    (\"linux\", \"largest_diff\", 2),\n", "    (\"linux\", \"kernel\", 0),\n", "    (\"linux\", \"mm\", 0),\n", "    (\"linux\", \"init\", 2),\n", "    (\"wine\", \"server\", 1),\n", "    (\"wine\", \"server\", 5),\n", "    (\"wine\", \"server\", 7),\n", "    (\"beauty_net\", \"beauty\", 0),\n", "    (\"augment_recent\", \"models\", 1),\n", "    (\"augment_recent\", \"experimental\", 0),\n", "    (\"augment_recent\", \"tools\", 3),\n", "    (\"augment_recent\", \"smallest_diff\", 3),\n", "    (\"augment_recent\", \"largest_diff\", 4),\n", "    (\"augment_recent\", \"largest_diff\", 0),\n", "    (\"augment_recent\", \"services\", 0),\n", "]\n", "core_eval_set_commits = [\n", "    mgh.get_commit((example[0], mgh.get_eval_example(*example)[\"commit_index\"]))\n", "    for example in core_eval_set\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "\n", "def format_execution_stats(execution_times, lengths, threshold=-1.0):\n", "    if not lengths:\n", "        return\n", "    print(f\"{len(lengths)} examples\")\n", "\n", "    execution_stats = {\n", "        \"Length\": lengths,\n", "        **execution_times,\n", "    }\n", "    percentiles = {}\n", "    for k, v in execution_stats.items():\n", "        if not v or max(v) <= threshold:\n", "            continue\n", "        percentiles[k] = {\n", "            \"p50\": np.percentile(v, 50, method=\"lower\"),\n", "            \"p90\": np.percentile(v, 90, method=\"lower\"),\n", "            # \"p95\": np.percentile(v, 95, method=\"lower\"),\n", "            \"p99\": np.percentile(v, 99, method=\"lower\"),\n", "            \"max\": max(v),\n", "        }\n", "    df = pd.DataFrame(percentiles).T\n", "    df = df.map(lambda x: f\"{x:.2f}\")\n", "    df = df[[\"p50\", \"p90\", \"p99\", \"max\"]]\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "\n", "def get_execution_stats(commits: list[dict], print_progress=False):\n", "    execution_times = defaultdict(list)\n", "    lengths = []\n", "    metadata = None\n", "    for index, commit in enumerate(commits):\n", "        start_time = time.time()\n", "        prompt, metadata = mgh.get_prompt(\n", "            commit[\"current_hash\"],\n", "            summary=False,\n", "            latest_commit_count=32,\n", "            sort_paths_by_size=True,\n", "            count_diffs_by_hunk=False,\n", "            summary_v2_threshold=900,\n", "            trailing_instructions=TRAILING_INSTRUCTIONS,\n", "            message_soft_budget=1024 * 3,\n", "            example_type=\"similar\",\n", "            similar_search_range=1000,\n", "            ratio_threshold=0.7,\n", "            git_structure_depth_limit=2,\n", "            git_structure_count_type=\"file\",\n", "            descriptor_path_source=\"commit\",\n", "            relevant_message_limit=3,\n", "            read_from_disk=True,\n", "            diff_line_limit=5000,\n", "            token_budget=1024 * 12,\n", "        )\n", "        lengths.append(token_counter.count_tokens(prompt))\n", "        for k, v in metadata.items():\n", "            if k in {\"relevant_message_prompt\"} or k.endswith(\"token_count\"):\n", "                continue\n", "            execution_times[k].append(v)\n", "        execution_times[\"Outer\"].append(time.time() - start_time)\n", "        start_time = time.time()\n", "        if print_progress:\n", "            print(\n", "                f\"{index}, {commit['current_hash']}: {execution_times['Outer'][-1]:.2f}s\"\n", "            )\n", "    return execution_times, lengths, metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["execution_time_dicts = {}\n", "length_lists = {}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"single\"\n", "execution_time_dicts[label], length_lists[label], metadata = get_execution_stats(\n", "    [\n", "        # mgh.get_commit(\"4a728957135d5d0c84ab3610b9a4683231bd163a\"),\n", "        mgh.get_commit(\"be602cde657ee43d23adbf309be6d700d0106dc9\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"single\"\n", "format_execution_stats(execution_time_dicts[label], length_lists[label], 0.01)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["trial_count = 100\n", "random_commits = [mgh.get_random_commit() for _ in range(trial_count)]\n", "label = \"random\"\n", "execution_time_dicts[label], length_lists[label], _ = get_execution_stats(\n", "    random_commits, print_progress=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"random\"\n", "format_execution_stats(execution_time_dicts[label], length_lists[label], 0.01)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"core_eval_set\"\n", "execution_time_dicts[label], length_lists[label], _ = get_execution_stats(\n", "    core_eval_set_commits, print_progress=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"core_eval_set\"\n", "format_execution_stats(execution_time_dicts[label], length_lists[label], 0.01)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# trial_count = 1000\n", "# random_commits = [mgh.get_random_commit() for _ in range(trial_count)]\n", "# label = \"random_large\"\n", "# execution_time_dicts[label], length_lists[label], _ = get_execution_stats(\n", "#     random_commits, print_progress=True\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# label = \"random_large\"\n", "# format_execution_stats(execution_time_dicts[label], length_lists[label], 0.01)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# label = \"random_large\"\n", "# for execution_time in execution_time_dicts[label][\"Total\"]:\n", "#     if execution_time > 0.5:\n", "#         print(execution_time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Prompt formatter test"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get input"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt, metadata = mgh.get_prompt(\n", "    \"3bbd12d56090e37b1deea0f051564521a98d811e\",\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=False,\n", "    summary_v2_threshold=900,\n", "    trailing_instructions=TRAILING_INSTRUCTIONS,\n", "    message_soft_budget=1024 * 3,\n", "    example_type=\"similar\",\n", "    similar_search_range=1000,\n", "    ratio_threshold=0.7,\n", "    git_structure_depth_limit=2,\n", "    git_structure_count_type=\"file\",\n", "    descriptor_path_source=\"commit\",\n", "    relevant_message_limit=3,\n", "    read_from_disk=True,\n", "    diff_line_limit=5000,\n", "    token_budget=1024 * 12,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_name, index = mgh._get_index_from_handle(\n", "    \"3bbd12d56090e37b1deea0f051564521a98d811e\"\n", ")\n", "gh = mgh.get_git_history(\"3bbd12d56090e37b1deea0f051564521a98d811e\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert gh\n", "changed_file_stats, diff, relevant_commit_messages, example_commit_messages = (\n", "    gh.get_prompt_formatter_inputs(\n", "        \"3bbd12d56090e37b1deea0f051564521a98d811e\",\n", "        summary=False,\n", "        latest_commit_count=32,\n", "        sort_paths_by_size=True,\n", "        count_diffs_by_hunk=False,\n", "        summary_v2_threshold=900,\n", "        trailing_instructions=TRAILING_INSTRUCTIONS,\n", "        message_soft_budget=1024 * 3,\n", "        example_type=\"similar\",\n", "        similar_search_range=1000,\n", "        ratio_threshold=0.7,\n", "        git_structure_depth_limit=2,\n", "        git_structure_count_type=\"file\",\n", "        descriptor_path_source=\"commit\",\n", "        relevant_message_limit=3,\n", "        read_from_disk=True,\n", "        diff_line_limit=5000,\n", "        token_budget=1024 * 12,\n", "    )\n", ")\n", "print(changed_file_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for message in relevant_commit_messages:\n", "    print(message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for message in example_commit_messages:\n", "    print(message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_name, index = mgh._get_index_from_handle(\n", "    \"3bbd12d56090e37b1deea0f051564521a98d811e\"\n", ")\n", "gh = mgh.get_git_history(\"3bbd12d56090e37b1deea0f051564521a98d811e\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert gh\n", "prompt = gh.get_prompt_using_formatter(\n", "    \"3bbd12d56090e37b1deea0f051564521a98d811e\",\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=False,\n", "    summary_v2_threshold=900,\n", "    trailing_instructions=TRAILING_INSTRUCTIONS,\n", "    message_soft_budget=1024 * 3,\n", "    example_type=\"similar\",\n", "    similar_search_range=1000,\n", "    ratio_threshold=0.7,\n", "    git_structure_depth_limit=2,\n", "    git_structure_count_type=\"file\",\n", "    descriptor_path_source=\"commit\",\n", "    relevant_message_limit=3,\n", "    read_from_disk=True,\n", "    diff_line_limit=5000,\n", "    token_budget=1024 * 12,\n", ")\n", "# print(prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt.system_prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt.message)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
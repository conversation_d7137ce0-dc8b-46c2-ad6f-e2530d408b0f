{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import nbformat\n", "from nbconvert import PythonExporter\n", "\n", "\n", "def ipynb_to_py(ipynb_path, py_path=None):\n", "    # If no output path specified, replace .ipynb with .py\n", "    if py_path is None:\n", "        py_path = ipynb_path.replace(\".ipynb\", \".py\")\n", "\n", "    # Read the notebook\n", "    with open(ipynb_path) as f:\n", "        notebook = nbformat.read(f, as_version=4)\n", "\n", "    # Convert to python\n", "    exporter = PythonExporter()\n", "    python_code, _ = exporter.from_notebook_node(notebook)\n", "\n", "    # Write to file\n", "    with open(py_path, \"w\") as f:\n", "        f.write(python_code)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ipynb_to_py(\"/home/<USER>/augment/experimental/zhuoran/commit_msgs/exp4.ipynb\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
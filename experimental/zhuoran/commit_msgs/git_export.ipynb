{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from collections import defaultdict\n", "from datetime import datetime\n", "\n", "from git import Repo\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Export"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def count_commits(repo, branch=\"main\", limit: int = 0) -> int:\n", "    \"\"\"Count total number of commits on the branch, up to limit if specified\"\"\"\n", "    if not limit:\n", "        return sum(1 for _ in repo.iter_commits(branch))\n", "    return min(sum(1 for _ in repo.iter_commits(branch)), limit)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["10"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["repo = Repo(\"/home/<USER>/augment\")\n", "\n", "count_commits(repo, limit=10)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["commits = repo.iter_commits(\"main\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["<git.Commit \"030a70f1debc3e924de3437ffb03d96b6cefb4d5\">"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["commit = next(commits)\n", "commit"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["' 1671 files changed, 2905316 insertions(+)'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["sha = \"4a728957135d5d0c84ab3610b9a4683231bd163a\"\n", "change_stat_string = repo.git.execute(\n", "    f\"git diff {sha} {sha}~1 --shortstat\",\n", "    shell=True,\n", ")\n", "change_stat_string"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["['1671 files changed', '2905316 insertions(+)']"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["[string.strip() for string in change_stat_string.split(\",\")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Insertions: 2905316, Deletions: 0\n"]}], "source": ["import re\n", "\n", "\n", "def extract_insertion_deletion_counts(change_stat_string):\n", "    insertion_stat_pattern = re.compile(r\"(\\d+) insertion[s]?\\(\\+\\)\")\n", "    deletion_stat_pattern = re.compile(r\"(\\d+) deletion[s]?\\(-\\)\")\n", "    insertion_match = insertion_stat_pattern.search(change_stat_string)\n", "    deletion_match = deletion_stat_pattern.search(change_stat_string)\n", "    if insertion_match:\n", "        insertions = int(insertion_match.group(1))\n", "    else:\n", "        insertions = 0\n", "    if deletion_match:\n", "        deletions = int(deletion_match.group(1))\n", "    else:\n", "        deletions = 0\n", "    return insertions, deletions\n", "\n", "\n", "# Example usage\n", "insertions, deletions = extract_insertion_deletion_counts(change_stat_string)\n", "print(f\"Insertions: {insertions}, Deletions: {deletions}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dir(commit)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(commit)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diffs = commit.parents[0].diff(commit)\n", "diffs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff = diffs[0]\n", "diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(diff.b_blob.data_stream)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["commit.stats.total"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff_item = diff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(diff_item.a_blob.size if diff_item.a_blob else 0)\n", "print(diff_item.b_blob.size if diff_item.b_blob else 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diffs = commit.parents[0].diff(commit, create_patch=True, R=True, M=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff_items = diffs[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First, let's see what we can safely get from the diff_item\n", "print(\"A path:\", diff_item.a_path)\n", "print(\"B path:\", diff_item.b_path)\n", "print(\"Change type:\", diff_item.change_type)\n", "print(\"Renamed:\", diff_item.renamed)\n", "print(\"Raw hexsha of blobs:\")\n", "print(\"A blob:\", diff_item.a_blob.hexsha if diff_item.a_blob else None)\n", "print(\"B blob:\", diff_item.b_blob.hexsha if diff_item.b_blob else None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using git.Git() to run raw git commands\n", "git = commit.repo.git\n", "diff_output = git.diff(commit.parents[0].hexsha, commit.hexsha, \"--\", diff_item.b_path)\n", "print(diff_output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["git = commit.repo.git\n", "# Try different diff formats\n", "print(\"Default diff:\")\n", "diff_output = git.diff(commit.parents[0].hexsha, commit.hexsha, \"--\", diff_item.b_path)\n", "print(diff_output)\n", "\n", "print(\"\\nUnified diff:\")\n", "diff_output = git.diff(\n", "    \"-U3\", commit.parents[0].hexsha, commit.hexsha, \"--\", diff_item.b_path\n", ")\n", "print(diff_output)\n", "\n", "# Or try getting raw blob contents to compare\n", "old_content = git.show(f\"{diff_item.a_blob.hexsha}\")\n", "new_content = git.show(f\"{diff_item.b_blob.hexsha}\")\n", "print(\"\\nContents different:\", old_content != new_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try with different diff options\n", "diff = commit.parents[0].diff(\n", "    commit,\n", "    create_patch=True,  # Ensure patch creation\n", "    unified=3,  # Standard unified diff format\n", "    U=\"3\",  # Alternative way to specify unified diff lines\n", "    patch=True,  # Explicit patch request\n", "    output_type=\"patch\",  # Request patch output\n", ")\n", "\n", "# Check the diff_item attributes\n", "print(\"Has diff:\", bool(diff_item.diff))\n", "print(\"Has a_blob:\", bool(diff_item.a_blob))\n", "print(\"Has b_blob:\", bool(diff_item.b_blob))\n", "print(\"Change type:\", diff_item.change_type)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stream = diff.a_blob.data_stream\n", "stream.read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for d in diffs:\n", "    print(type(b_blob))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["commit_data = []\n", "progress_bar = None\n", "\n", "for i, commit in enumerate(commits):\n", "    if limit > 0 and i >= limit:\n", "        break\n", "\n", "    # Get the diff for this commit\n", "    diff_data = defaultdict(list)\n", "\n", "    # Handle first commit case\n", "    parent = commit.parents[0] if commit.parents else None\n", "\n", "    if parent:\n", "        try:\n", "            diffs = parent.diff(commit)\n", "\n", "            for diff in diffs:\n", "                file_path = diff.b_path or diff.a_path\n", "\n", "                # Get full diff\n", "                if diff.diff:\n", "                    try:\n", "                        diff_text = diff.diff.decode(\"utf-8\")\n", "                        diff_data[file_path] = [diff_text]\n", "                    except UnicodeDecodeError:\n", "                        # Skip binary files or files with encoding issues\n", "                        diff_data[file_path] = [\"Binary file or encoding error\"]\n", "        except Exception as e:\n", "            print(\n", "                f\"Error processing diff for commit {commit.hexsha}: {str(e)}\",\n", "                file=sys.stderr,\n", "            )\n", "\n", "    # Build commit metadata\n", "    commit_info = {\n", "        \"hash\": commit.hex<PERSON>,\n", "        \"author\": {\"name\": commit.author.name, \"email\": commit.author.email},\n", "        \"authored_date\": datetime.fromtimestamp(commit.authored_date).isoformat(),\n", "        \"committer\": {\n", "            \"name\": commit.committer.name,\n", "            \"email\": commit.committer.email,\n", "        },\n", "        \"committed_date\": datetime.fromtimestamp(commit.committed_date).isoformat(),\n", "        \"message\": commit.message,\n", "        \"diff\": dict(diff_data),  # Make sure this line is present\n", "    }\n", "\n", "    commit_data.append(commit_info)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "def split_diff_into_hunks(diff_string):\n", "    # Split the diff string into hunks\n", "    hunks = re.split(r\"(?=^@@)\", diff_string, flags=re.MULTILINE)\n", "    # Remove any empty hunks and ensure all hunks start with @@\n", "    return [hunk.strip() for hunk in hunks if hunk.strip()]\n", "\n", "\n", "def process_commit(commit):\n", "    commit[\"diff_hunks\"] = {}\n", "    for file_path, diffs in commit[\"diffs\"].items():\n", "        commit[\"diff_hunks\"][file_path] = [\n", "            split_diff_into_hunks(diff) for diff in diffs\n", "        ]\n", "    return commit\n", "\n", "\n", "# Read the JSON file\n", "with open(\"/home/<USER>/zhuoran/commit_msgs/10_commits.json\", \"r\") as f:\n", "    commits = json.load(f)\n", "\n", "# Process each commit\n", "processed_commits = [process_commit(commit) for commit in commits]\n", "\n", "# Print the first processed commit to stdout\n", "print(json.dumps(processed_commits[0], indent=4))\n", "\n", "print(\"Processing complete. First commit printed to stdout.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["token_counts = {}\n", "with open(\"/home/<USER>/zhuoran/commit_msgs/token_counts.json\") as file_:\n", "    for line in file_:\n", "        hash_, token_count = line.split()\n", "        token_counts[hash_] = int(token_count)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Filter out non-positive values and add a small constant\n", "filtered_token_counts = {k: v + 1 for k, v in token_counts.items() if v > 0}\n", "\n", "if not filtered_token_counts:\n", "    print(\"No positive values in the dataset. Unable to create histogram.\")\n", "else:\n", "    # Calculate logarithmic bins\n", "    min_value = min(filtered_token_counts.values())\n", "    max_value = max(filtered_token_counts.values())\n", "    bins = np.logspace(np.log10(min_value), np.log10(max_value), num=20)\n", "\n", "    # Create histogram\n", "    plt.figure(figsize=(10, 6))\n", "    plt.hist(list(filtered_token_counts.values()), bins=bins, edgecolor=\"black\")\n", "    plt.title(\"Distribution of Token Counts per Commit\")\n", "    plt.xlabel(\"Token Count\")\n", "    plt.ylabel(\"Frequency\")\n", "    plt.grid(True, alpha=0.3)\n", "    plt.xscale(\"log\")  # Set x-axis to logarithmic scale\n", "    plt.yscale(\"log\")  # Set y-axis to logarithmic scale\n", "\n", "    # Add mean line\n", "    mean_tokens = sum(filtered_token_counts.values()) / len(filtered_token_counts)\n", "    plt.axvline(\n", "        mean_tokens,\n", "        color=\"red\",\n", "        linestyle=\"dashed\",\n", "        linewidth=2,\n", "        label=f\"Mean: {mean_tokens:.2f}\",\n", "    )\n", "\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    # Print summary statistics\n", "    print(f\"Total commits: {len(filtered_token_counts)}\")\n", "    print(f\"Mean tokens per commit: {mean_tokens:.2f}\")\n", "    print(f\"Max tokens: {max(filtered_token_counts.values())}\")\n", "    print(f\"Min tokens: {min(filtered_token_counts.values())}\")\n", "\n", "    # Calculate and print percentiles\n", "    token_values = np.array(list(filtered_token_counts.values()))\n", "\n", "    percentiles = list(range(0, 100, 5)) + [\n", "        86,\n", "        87,\n", "        88,\n", "        89,\n", "        96,\n", "        97,\n", "        98,\n", "        98.5,\n", "        99,\n", "        99.5,\n", "        99.9,\n", "        100,\n", "    ]\n", "    for i in sorted(percentiles):\n", "        percentile = np.percentile(token_values, i)\n", "        print(f\"{i}th percentile: {percentile:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from scipy import stats\n", "\n", "# Assuming token_counts is your dictionary of token counts\n", "token_values = list(token_counts.values())\n", "\n", "# Calculate percentiles for specific token counts\n", "percentile_12k = stats.percentileofscore(token_values, 12000)\n", "percentile_24k = stats.percentileofscore(token_values, 24000)\n", "percentile_32k = stats.percentileofscore(token_values, 32000)\n", "\n", "print(f\"Percentile for 12K tokens: {percentile_12k:.2f}%\")\n", "print(f\"Percentile for 24K tokens: {percentile_24k:.2f}%\")\n", "print(f\"Percentile for 32K tokens: {percentile_32k:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# New"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from git import Repo\n", "\n", "repo = Repo(\"/home/<USER>/augment\")\n", "\n", "limit = 10\n", "commits = repo.iter_commits(\"main\")\n", "commit = next(commits)\n", "commit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_object_attributes(obj):\n", "    \"\"\"\n", "    Create a dictionary of an object's attributes and their values.\n", "\n", "    For callable attributes, the function attempts to call them and store the result.\n", "    For non-callable attributes, it stores the attribute value directly.\n", "    Only includes attributes that don't start with an underscore.\n", "\n", "    Args:\n", "        obj: The object to inspect\n", "\n", "    Returns:\n", "        dict: A dictionary of attribute names and their values/results\n", "    \"\"\"\n", "    obj_attrs = {}\n", "    for attr_name in dir(obj):\n", "        if not attr_name.startswith(\"_\"):\n", "            try:\n", "                attr = getattr(obj, attr_name)\n", "                if callable(attr):\n", "                    try:\n", "                        obj_attrs[f\"{attr_name}()\"] = attr()\n", "                    except Exception as e:\n", "                        obj_attrs[f\"{attr_name}()\"] = (\n", "                            f\"Error calling {attr_name}: {str(e)}\"\n", "                        )\n", "                else:\n", "                    obj_attrs[attr_name] = attr\n", "            except Exception as e:\n", "                obj_attrs[attr_name] = f\"Error getting {attr_name}: {str(e)}\"\n", "    return obj_attrs\n", "\n", "\n", "def get_object_attribute_types(obj):\n", "    obj_attrs = {}\n", "    for attr_name in dir(obj):\n", "        if not attr_name.startswith(\"_\"):\n", "            try:\n", "                attr = getattr(obj, attr_name)\n", "                if callable(attr):\n", "                    try:\n", "                        obj_attrs[f\"{attr_name}()\"] = type(attr())\n", "                    except Exception as e:\n", "                        obj_attrs[f\"{attr_name}()\"] = (\n", "                            f\"Error calling {attr_name}: {str(e)}\"\n", "                        )\n", "                else:\n", "                    obj_attrs[attr_name] = type(attr)\n", "            except Exception as e:\n", "                obj_attrs[attr_name] = f\"Error getting {attr_name}: {str(e)}\"\n", "    return obj_attrs\n", "\n", "\n", "# Example usage:\n", "commit_attrs = get_object_attributes(commit)\n", "commit_attrs_types = get_object_attribute_types(commit)\n", "\n", "from pprint import pprint\n", "\n", "pprint(commit_attrs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pprint(get_object_attributes(commit.stats))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["commit_attrs_types"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pprint(get_object_attributes(commit.diff()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diffs = commit.diff()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff = diffs[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diffs.change_type"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_object_attributes(diff)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# File stat size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "commits = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_v1.json\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["commit_stats = [\n", "    {\n", "        \"hash\": commit[\"current_hash\"],\n", "        \"diffs\": [\n", "            (path, diff[\"insertions\"], diff[\"deletions\"])\n", "            for path, diff in commit[\"diffs\"].items()\n", "        ],\n", "    }\n", "    for commit in commits\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["count = 1_000\n", "\n", "\n", "def print_size(data, count=None):\n", "    if isinstance(data, dict):\n", "        data = list(data.items())\n", "    count = count or len(data)\n", "    print(f\"{len(json.dumps(data[: int(count)]))*2:,} {min(len(data), int(count))}\")\n", "\n", "\n", "print_size(commit_stats, count)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from typing import Any, Dict, List, Union, Tuple\n", "\n", "\n", "def print_json_schema(obj: Union[Dict, List, Tuple], indent: int = 0) -> None:\n", "    \"\"\"\n", "    Print the schema of a nicely-structured JSON object.\n", "\n", "    Args:\n", "        obj (Union[Dict, <PERSON>, <PERSON><PERSON>]): The JSON object to analyze.\n", "        indent (int): The current indentation level (used for recursion).\n", "    \"\"\"\n", "    indent_str = \"  \" * indent\n", "\n", "    if isinstance(obj, dict):\n", "        print(f\"{indent_str}{{\")\n", "        for key, value in obj.items():\n", "            print(f'{indent_str}  \"{key}\": ', end=\"\")\n", "            if isinstance(value, (dict, list, tuple)):\n", "                print()\n", "                print_json_schema(value, indent + 1)\n", "            else:\n", "                print(f\"{type(value).__name__}\")\n", "        print(f\"{indent_str}}}\")\n", "\n", "    elif isinstance(obj, list):\n", "        print(f\"{indent_str}[\")\n", "        if obj:\n", "            print(f\"{indent_str}  \", end=\"\")\n", "            if isinstance(obj[0], (dict, list, tuple)):\n", "                print()\n", "                print_json_schema(obj[0], indent + 1)\n", "            else:\n", "                print(f\"{type(obj[0]).__name__}\")\n", "        print(f\"{indent_str}]\")\n", "\n", "    elif isinstance(obj, tuple):\n", "        print(f\"{indent_str}tuple(\", end=\"\")\n", "        if obj:\n", "            print(\", \".join(type(item).__name__ for item in obj), end=\"\")\n", "        print(\")\")\n", "\n", "    else:\n", "        print(f\"{type(obj).__name__}\")\n", "\n", "\n", "print_json_schema(commit_stats)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Tuple\n", "from pathlib import Path\n", "\n", "\n", "def transpose_json(data: List[Dict[str, Any]]) -> Dict[str, Any]:\n", "    result = {}\n", "\n", "    for commit in data:\n", "        commit_hash = commit[\"hash\"]\n", "        for diff in commit[\"diffs\"]:\n", "            path, insertions, deletions = diff\n", "\n", "            # Split the path into parts\n", "            parts = Path(path).parts\n", "\n", "            # Navigate through the nested dictionary\n", "            current = result\n", "            for part in parts[:-1]:  # All parts except the last (filename)\n", "                if part not in current:\n", "                    current[part] = {}\n", "                current = current[part]\n", "\n", "            # Add the file entry\n", "            filename = parts[-1]\n", "            if filename not in current:\n", "                current[filename] = {}\n", "            current[filename][commit_hash] = (insertions, deletions)\n", "\n", "    return result\n", "\n", "\n", "transposed = transpose_json(commit_stats)\n", "print_json_schema(transposed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print_size(transpose_json(commit_stats[:1000]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json.dumps(transposed)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Third-party trial load"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "commits = json.load(open(\"/home/<USER>/zhuoran/commit_msgs_angular/commits_v4_t.json\"))\n", "len(commits)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Review"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.git_history import GitHistory\n", "\n", "git_history = GitHistory.from_json(\n", "    \"/home/<USER>/zhuoran/commit_msgs_linux/commits_v5.json\",\n", "    encoding=\"utf-16\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i = -1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["i += 1\n", "print(i)\n", "print(git_history.get_commit_message(i))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
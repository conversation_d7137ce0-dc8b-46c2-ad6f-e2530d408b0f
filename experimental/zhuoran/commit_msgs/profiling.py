import time
from collections import defaultdict

import numpy as np
import pandas as pd

from experimental.zhuoran.commit_msgs.git_history import MultiGitHistory

print("Entering...")


class RoughTokenCounter:
    def __init__(self):
        self.timings = {
            "Length getting": 0.0,
            "Division": 0.0,
            "Integer cast": 0.0,
        }

    def count_tokens(self, prompt_chars: str) -> int:
        start_time = time.time()
        length = len(prompt_chars)
        self.timings["Length getting"] += time.time() - start_time
        start_time = time.time()
        result = length / 3
        self.timings["Division"] += time.time() - start_time
        start_time = time.time()
        result = int(result)
        self.timings["Integer cast"] += time.time() - start_time
        return result


token_counter = RoughTokenCounter()


print("Loading...")
mgh = MultiGitHistory.from_jsons(
    {
        "augment_recent": (
            "/home/<USER>/zhuoran/commit_msgs/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs/eval_set_v1_new_keys.json",
            "/home/<USER>/zhuoran/commit_msgs/git_structure.json",
            "/home/<USER>/augment/",
            "utf-8",
        ),
        "augment": (
            "/home/<USER>/zhuoran/commit_msgs/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/git_structure.json",
            "/home/<USER>/augment/",
            "utf-8",
        ),
        "angular": (
            "/home/<USER>/zhuoran/commit_msgs_angular/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs_angular/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_angular/git_structure.json",
            "/home/<USER>/angular/",
            "utf-8",
        ),
        "pytorch": (
            "/home/<USER>/zhuoran/commit_msgs_pytorch/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_pytorch/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_pytorch/git_structure.json",
            "/home/<USER>/pytorch/",
            "utf-16",
        ),
        "linux": (
            "/home/<USER>/zhuoran/commit_msgs_linux/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_linux/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_linux/git_structure.json",
            "/home/<USER>/linux/",
            "utf-16",
        ),
        "wine": (
            "/home/<USER>/zhuoran/commit_msgs_wine/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_wine/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_wine/git_structure.json",
            "/home/<USER>/wine/",
            "utf-16",
        ),
        "beauty_net": (
            "/home/<USER>/zhuoran/commit_msgs_beauty_net/commits_v4.json",
            "/mnt/efs/augment/user/zhuoran/commit_msgs_beauty_net/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_beauty_net/git_structure.json",
            "/home/<USER>/beauty-net/",
            "utf-8",
        ),
    },
    token_counter.count_tokens,
)

print(mgh.get_lengths())

# Angular	Largest	2
# Angular	Packages	1
# Angular	Packages	2
# Angular	Packages	3
# Angular	Packages	4
# PyTorch	Docs	0
# PyTorch	Torch	0
# PyTorch	Torch	1
# PyTorch	Torch	2
# PyTorch	Torch	3
# Linux	Largest	2
# Linux	Kernel	0
# Linux	MM	0
# Linux	Init	2
# Wine	Server	1
# Wine	Server	5
# Wine	Server	7
# BeautyNet	Beauty	0
# Augment (recent)	Models	1
# Augment (recent)	Experimental	0
# Augment (recent)	Tools	3
# Augment (recent)	Smallest	3
# Augment (recent)	Largest	4
# Augment (recent)	Largest	0
# Augment (recent)	Services	0
core_eval_set = [
    ("angular", "largest_diff", 2),
    ("angular", "packages", 1),
    ("angular", "packages", 2),
    ("angular", "packages", 3),
    ("angular", "packages", 4),
    ("pytorch", "docs", 0),
    ("pytorch", "torch", 0),
    ("pytorch", "torch", 1),
    ("pytorch", "torch", 2),
    ("pytorch", "torch", 3),
    ("linux", "largest_diff", 2),
    ("linux", "kernel", 0),
    ("linux", "mm", 0),
    ("linux", "init", 2),
    ("wine", "server", 1),
    ("wine", "server", 5),
    ("wine", "server", 7),
    ("beauty_net", "beauty", 0),
    ("augment_recent", "models", 1),
    ("augment_recent", "experimental", 0),
    ("augment_recent", "tools", 3),
    ("augment_recent", "smallest_diff", 3),
    ("augment_recent", "largest_diff", 4),
    ("augment_recent", "largest_diff", 0),
    ("augment_recent", "services", 0),
]
core_eval_set_commits = [
    mgh.get_commit((example[0], mgh.get_eval_example(*example)["commit_index"]))
    for example in core_eval_set
]


TRAILING_INSTRUCTIONS = """Do not include boilerplate text like "Here is the commit message:" in your response.."""

start_time = time.time()
execution_times = defaultdict(list)
lengths = []
trial_count = 100
random_commits = [mgh.get_random_commit() for _ in range(trial_count)]

print("Start")

# for commit in random_commits:
for commit in core_eval_set_commits:
    # for commit in [mgh.get_commit("3bbd12d56090e37b1deea0f051564521a98d811e")]:
    start_time = time.time()
    prompt, metadata = mgh.get_prompt(
        commit["current_hash"],
        summary=False,
        latest_commit_count=32,
        sort_paths_by_size=True,
        count_diffs_by_hunk=False,
        summary_v2_threshold=900,
        trailing_instructions=TRAILING_INSTRUCTIONS,
        message_soft_budget=1024 * 3,
        example_type="similar",
        similar_search_range=1000,
        line_ratio_threshold=0.7,
        relevant_message_limit=3,
        read_from_disk=True,
        token_budget=1024 * 12,
    )
    lengths.append(token_counter.count_tokens(prompt))
    for k, v in metadata.items():
        if k in {"relevant_message_token_count", "relevant_message_prompt"}:
            continue
        execution_times[k].append(v)
    execution_times["Outer"].append(time.time() - start_time)
    start_time = time.time()
    print(f"{commit['current_hash']}: {execution_times['Outer'][-1]:.2f}s")

print(f"{len(lengths)} examples")
print(f"{sum(lengths) / len(lengths)}/{max(lengths)} Prompt length")
averages = {}
maxs = {}
for k, v in execution_times.items():
    averages[k] = sum(v) / len(v)
    maxs[k] = max(v)
for k, v in execution_times.items():
    # print(f"{sum(v) / len(v):.2f}s/{max(v):.2f}s {k}")
    print(f"{averages[k]:.2f}s/{maxs[k]:.2f}s {k}")
print()
for k, v in execution_times.items():
    print(k)
for k, v in execution_times.items():
    print(f"{averages[k]:.2f}")
for k, v in execution_times.items():
    print(f"{maxs[k]:.2f}")


print(f"{len(lengths)} examples")
print(
    f"Prompt length: p50={np.percentile(lengths, 50):.2f}, p90={np.percentile(lengths, 90):.2f}, p99={np.percentile(lengths, 99):.2f}, max={max(lengths)}"
)

percentiles = {}
for k, v in execution_times.items():
    percentiles[k] = {
        "p50": np.percentile(v, 50),
        "p90": np.percentile(v, 90),
        "p95": np.percentile(v, 95),
        "p99": np.percentile(v, 99),
        "max": max(v),
    }

# Create a DataFrame for individual statistics
df = pd.DataFrame(percentiles).T
df = df.applymap(lambda x: f"{x:.2f}s")

# Reorder columns to match desired output order
df = df[["p50", "p90", "p95", "p99", "max"]]

# Display the formatted DataFrame
print("\nExecution Time Statistics (in seconds):")
print(df)

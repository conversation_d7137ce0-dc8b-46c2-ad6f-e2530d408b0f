import json
import os
import subprocess
import time
from copy import deepcopy
from dataclasses import dataclass, field

# Remove this line as PriorityQueue is no longer used
from typing import Dict, List, Optional

from git import Repo
from tqdm import tqdm

_ = tqdm


@dataclass
class GitNode:
    path: str
    count: int = 0
    ratio: float = 0.0
    children: Dict[str, "GitNode"] = field(default_factory=dict)

    def to_dict(self):
        return {
            "path": self.path,
            "count": self.count,
            "ratio": self.ratio,
            "children": {
                name: child.to_dict() for name, child in self.children.items()
            },
        }

    @classmethod
    def from_dict(cls, tree_dict: dict):
        path = tree_dict["path"]
        count = tree_dict["count"]
        ratio = tree_dict["ratio"]
        children = {
            name: cls.from_dict(child_dict)
            for name, child_dict in tree_dict["children"].items()
        }
        return cls(path, count, ratio, children)


class GitStructure:
    def __init__(self, root: GitNode):
        self.root = root

    @classmethod
    def from_json(cls, json_file: str):
        with open(json_file, "r", encoding="utf-8") as f:
            tree_dict = json.load(f)
        return cls(GitNode.from_dict(tree_dict))

    def reload_from_repo(self, repo_path: str):
        exporter = GitStructureExporter(repo_path)
        self.root = exporter.root

    def get_tree(
        self,
        node: Optional[GitNode] = None,
        max_level: int | None = None,
        ratio_threshold: float | None = None,
        node_limit: int | None = None,
        level: int = 0,
    ) -> dict:
        """Get the tree structure with statistics as a nested dictionary."""
        assert ratio_threshold is None or node_limit is None
        if max_level and level >= max_level:
            return {}
        if node is None:
            node = self.root

        if ratio_threshold is not None:
            result = {
                "name": os.path.basename(node.path),
                "ratio": node.ratio,
                "children": [],
            }
            # print(f"V0: Adding node {node.path} with ratio {node.ratio}")
            if ratio_threshold and node.ratio >= ratio_threshold:
                for child in sorted(node.children.values(), key=lambda x: x.path):
                    child_stats = self.get_tree(
                        node=child,
                        max_level=max_level,
                        ratio_threshold=ratio_threshold,
                        node_limit=node_limit,
                        level=level + 1,
                    )
                    if child_stats:
                        result["children"].append(child_stats)
        elif node_limit is not None:
            result = {}
        else:
            raise ValueError(
                "Either ratio_threshold or node_limit must be specified in V0"
            )
        return result

    def get_tree_as_list(
        self,
        max_level=None,
        ratio_threshold=None,
        reverse_sort=True,
    ) -> List[str]:
        """Get the tree structure as a flat list of nodes."""
        assert ratio_threshold is not None
        tree = self.get_tree(
            max_level=max_level,
            ratio_threshold=ratio_threshold,
        )

        flat_list = []

        def flatten_tree(node, path=""):
            current_path = os.path.join(path, node["name"])
            node_dict = {
                "path": current_path,
                "name": node["name"],
                "ratio": node["ratio"],
            }
            flat_list.append(node_dict)

            for child in node["children"]:
                flatten_tree(child, current_path)

        flatten_tree(tree)
        flat_list = [node["path"].strip().rstrip("/") for node in flat_list]
        flat_list.sort(reverse=reverse_sort)
        return flat_list

    def get_tree_as_list_v1(
        self,
        max_level: int | None = None,
        ratio_threshold: float | None = None,
        node_limit: int | None = None,
        node_ratio_threshold: float | None = None,
        add_parent_paths: bool = True,
        reverse_sort: bool = True,
    ) -> List[str]:
        """Get the tree structure as a flat list of nodes without using get_tree."""
        assert ratio_threshold is None or node_limit is None
        # print()

        if ratio_threshold is not None:
            results = []
            nodes_to_process = [(self.root, 0)]
            while nodes_to_process:
                node, level = nodes_to_process.pop()
                if max_level is not None and level >= max_level:
                    continue
                if node.ratio < ratio_threshold:
                    # print(f"V1: Adding leaf {node.path} with ratio {node.ratio}")
                    results.append(node.path)
                else:
                    # print(f"V1: Adding branch {node.path} with ratio {node.ratio}")
                    if add_parent_paths:
                        results.append(node.path)
                    nodes_to_process.extend(
                        [(child, level + 1) for child in node.children.values()]
                    )
            # print(f"V1: Found {len(results)} nodes with ratio >= {ratio_threshold}")
        elif node_limit is not None:
            results = []
            queue = [(self.root.ratio, 0, self.root.path, self.root)]

            while queue and len(results) + len(queue) < node_limit:
                queue.sort(reverse=True)
                remaining_ratio, level, path, node = queue.pop(0)
                node = deepcopy(node)

                if (
                    (max_level is not None and level >= max_level)
                    or not node.children
                    or (
                        node_ratio_threshold is not None
                        and max(node.children.values(), key=lambda x: x.ratio).ratio
                        < node_ratio_threshold
                    )
                ):
                    # print(
                    #     f"V1: Adding leaf {path} with {level=} and {remaining_ratio=:.2%}"
                    # )
                    results.append((path, remaining_ratio))
                else:
                    largest_child = max(node.children.values(), key=lambda x: x.ratio)
                    new_remaining_ratio = remaining_ratio - largest_child.ratio

                    node.children.pop(os.path.basename(largest_child.path))
                    queue.append((new_remaining_ratio, level, path, node))

                    queue.append(
                        (
                            largest_child.ratio,
                            level + 1,
                            largest_child.path,
                            largest_child,
                        )
                    )
                    queue.sort(reverse=True)
                # print(f"{len(results)}:", results)
                # print(f"PQ {len(queue)}:", [(f"{x[0]:.2%}", x[2]) for x in queue])

            # print("Processing remaining items in the queue")
            # Add remaining items from the queue
            while queue and len(results) < node_limit:
                remaining_ratio, level, path, _ = queue.pop(0)
                results.append((path, remaining_ratio))
                # print(f"{len(results)}:", results)
                # print(f"PQ {len(queue)}:", [(f"{x[0]:.2%}", x[2]) for x in queue])
            results.sort(reverse=reverse_sort)
            return results
        elif ratio_threshold is not None:
            results = []
            nodes_to_process = [(self.root, 0)]
            while nodes_to_process:
                node, level = nodes_to_process.pop()
                if max_level is not None and level >= max_level:
                    continue
                if node.ratio < ratio_threshold:
                    results.append(node.path)
                else:
                    if add_parent_paths:
                        results.append(node.path)
                    nodes_to_process.extend(
                        [(child, level + 1) for child in node.children.values()]
                    )
        else:
            raise ValueError(
                "Either ratio_threshold or node_limit must be specified in V1"
            )
        results.sort(reverse=reverse_sort)
        results = [path.strip().strip("/") for path in results]
        return results

    def get_tree_as_set(self, max_level=None, ratio_threshold=None) -> set:
        """Get the tree structure as a set of paths."""
        tree_list = self.get_tree_as_list(max_level, ratio_threshold)
        return set(tree_list)

    def print_tree(
        self,
        max_level=None,
        ratio_threshold=None,
    ):
        """Print the tree structure with statistics."""
        tree_stats = self.get_tree(
            max_level=max_level,
            ratio_threshold=ratio_threshold,
        )

        def print_node(node, level=0):
            indent = "    " * level
            print(f"{indent}{node['name']} ({node['ratio']:.2%}L)")
            for child in node["children"]:
                print_node(child, level + 1)

        print_node(tree_stats)


class GitStructureExporter:
    def __init__(
        self, repo_path: str, depth_limit: int | None = None, count_type: str = "line"
    ):
        self.repo_path = repo_path
        self.depth_limit = depth_limit
        self.count_type = count_type
        self.repo = Repo(self.repo_path)
        self.root = GitNode(path="")
        self.timings = {}
        print(f"Initializing GitTreeStats for repository: {self.repo_path}")
        self._build_tree(depth_limit)

    def _get_tracked_files(self) -> list:
        """Get list of tracked files in the repository."""
        print("Getting list of tracked files...")
        start_time = time.time()
        # api_tracked_files = [
        #     item.path  # type: ignore
        #     for item in self.repo.tree().traverse()
        #     if item.type == "blob"  # type: ignore
        # ]
        # self.api_tracked_files = api_tracked_files
        # tracked_files = api_tracked_files
        # self.timings["Get tracked files"] = time.time() - start_time
        # start_time = time.time()
        try:
            result = subprocess.run(
                ["git", "ls-tree", "-r", "--name-only", "HEAD"],
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                check=True,
            )
            tracked_files = result.stdout.splitlines()
        except subprocess.CalledProcessError as e:
            print(f"Error getting tracked files: {e}")
            tracked_files = []
        self.subprocess_tracked_files = tracked_files
        self.timings["Get tracked files (subprocess)"] = time.time() - start_time
        print(f"Found {len(tracked_files)} tracked files")
        return tracked_files

    def _count_lines(self, file_path: str) -> int:
        """Count lines in a file using GitPython."""
        try:
            blob = self.repo.head.commit.tree / file_path
            return len(blob.data_stream.read().decode("utf-8").splitlines())
        except (KeyError, UnicodeDecodeError):
            return 0

    def _add_file_to_tree(self, file_path: str, line_count: int | None = None):
        """Add a file to the tree structure and update counts."""
        parts = file_path.split(os.sep)
        current = self.root
        assert self.count_type in ["file", "line"]
        if self.count_type == "file":
            current.count += 1
        elif self.count_type == "line":
            assert line_count is not None
            current.count += line_count

        # Build path incrementally and update counts
        for part in parts[:-1]:  # Exclude the file name
            if part not in current.children:
                current.children[part] = GitNode(path=os.path.join(current.path, part))
            current = current.children[part]
            if self.count_type == "file":
                current.count += 1
            elif self.count_type == "line":
                assert line_count is not None
                current.count += line_count

    def _build_tree(self, depth_limit: int | None = None):
        """Build the entire tree structure and calculate ratios."""
        print("Building tree structure...")
        tracked_files = self._get_tracked_files()
        start_time = time.time()

        # for file_path in tqdm(tracked_files, desc="Processing files", unit="file"):
        for file_path in tracked_files:
            if depth_limit is not None and len(file_path.split(os.sep)) > depth_limit:
                continue
            line_count = (
                self._count_lines(file_path) if self.count_type == "line" else None
            )
            self._add_file_to_tree(file_path, line_count)
        self.timings["Add files to tree"] = time.time() - start_time
        start_time = time.time()

        print("Calculating ratios...")
        self._calculate_ratio()
        self.timings["Calculate ratios"] = time.time() - start_time
        start_time = time.time()

        print("Tree structure built successfully")

    def _calculate_ratio(self, node: GitNode | None = None):
        """Calculate file and line ratios for each node in the tree."""
        if node is None:
            node = self.root
            node.ratio = 1.0

        for child in node.children.values():
            child.ratio = child.count / self.root.count
            self._calculate_ratio(child)

    def get_untracked_files(self):
        """Get list of untracked files (optional helper method)."""
        print("Getting untracked files...")
        untracked_files = self.repo.untracked_files
        print(f"Found {len(untracked_files)} untracked files")
        return untracked_files

    def dump(self, output_file: str):
        """Export the tree structure to a JSON file."""
        tree_dict = self.root.to_dict()
        print(f"Exporting tree structure to {output_file}")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(tree_dict, f, indent=2)
        print(f"Tree structure exported successfully to {output_file}")


# Example usage
if __name__ == "__main__":
    print("Starting GitTreeStats")

    # Initialize with current directory
    exporter = GitStructureExporter("/home/<USER>/spark/", None, "line")

    # Print the tree
    print("Printing repository statistics:")
    git_structure = GitStructure(exporter.root)
    git_structure.print_tree(ratio_threshold=0.7)

    # Export the tree to a JSON file
    json_path = "/home/<USER>/zhuoran/commit_msgs_spark/git_structure_v1.json"
    exporter.dump(json_path)

    # git_structure = GitStructure.from_json(json_path)

    print("GitTreeStats completed")

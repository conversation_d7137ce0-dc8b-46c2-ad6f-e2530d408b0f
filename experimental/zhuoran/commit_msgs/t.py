from base.prompt_format_chat.lib.token_counter import RoughTokenCounter
from experimental.zhuoran.commit_msgs.git_history import MultiGitHistory

token_counter = RoughTokenCounter()
mgh = MultiGitHistory.from_jsons(
    {
        "augment_recent": (
            "/home/<USER>/zhuoran/commit_msgs/augment/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs/augment/eval_set_v1_new_keys.json",
            "/home/<USER>/zhuoran/commit_msgs/augment/git_structure_v1.json",
            "/home/<USER>/augment/",
            "utf-8",
        ),
        "augment": (
            "/home/<USER>/zhuoran/commit_msgs/augment/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs/augment/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/augment/git_structure_v1.json",
            "/home/<USER>/augment/",
            "utf-8",
        ),
        "angular": (
            "/home/<USER>/zhuoran/commit_msgs/angular/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs/angular/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/angular/git_structure_v1.json",
            "/home/<USER>/angular/",
            "utf-8",
        ),
        "pytorch": (
            "/home/<USER>/zhuoran/commit_msgs/pytorch/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/pytorch/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/pytorch/git_structure_v1.json",
            "/home/<USER>/pytorch/",
            "utf-16",
        ),
        "linux": (
            "/home/<USER>/zhuoran/commit_msgs/linux/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/linux/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/linux/git_structure_v1.json",
            "/home/<USER>/linux/",
            "utf-16",
        ),
        "wine": (
            "/home/<USER>/zhuoran/commit_msgs/wine/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/wine/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/wine/git_structure_v1.json",
            "/home/<USER>/wine/",
            "utf-16",
        ),
        "beauty_net": (
            "/home/<USER>/zhuoran/commit_msgs/beauty_net/commits_v4.json",
            "/mnt/efs/augment/user/zhuoran/commit_msgs/beauty_net/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/beauty_net/git_structure_v1.json",
            "/home/<USER>/beauty-net/",
            "utf-8",
        ),
        "spark": (
            "/home/<USER>/zhuoran/commit_msgs/spark/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/spark/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/spark/git_structure_v1.json",
            "/home/<USER>/spark/",
            "utf-16",
        ),
    },
    token_counter.count_tokens,
)
mgh.get_lengths()

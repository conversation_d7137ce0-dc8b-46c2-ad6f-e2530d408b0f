import chardet


def detect_file_encoding(path):
    """
    Detect the encoding of a file.

    Args:
    path (str): The path to the file.

    Returns:
    str: The detected encoding of the file.
    """
    with open(path, "rb") as f:
        raw_sample = f.read(1024 * 1024)  # Read 1MB
        detected = chardet.detect(raw_sample)
        encoding = detected["encoding"]
    print(f"Detected encoding: {encoding}")
    return encoding

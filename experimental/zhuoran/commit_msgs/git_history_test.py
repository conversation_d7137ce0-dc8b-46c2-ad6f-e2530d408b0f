import random
from unittest.mock import <PERSON>Mock

from experimental.zhuoran.commit_msgs.git_history import Git<PERSON>istory


def test_get_commit_diff_regression():
    # Create a mock GitHistory object with random commits
    commits = []
    for i in range(1000):
        file_count = random.randint(1, 100)
        insertions = [random.randint(1, 50) for _ in range(file_count)]
        deletions = [random.randint(1, 50) for _ in range(file_count)]
        diffs = {
            f"file_{j}": {
                "content": f"diff content for file {j} in commit {i}\n"
                * (insertions[j] + deletions[j]),
                "insertions": insertions[j],
                "deletions": deletions[j],
                "hunks": MagicMock(),
            }
            for j in range(file_count)
        }
        commits.append(
            {
                "current_hash": f"commit_{i}",
                "diffs": diffs,
            }
        )

    # Mock token counter function
    def mock_token_counter(text):
        return len(text.split())

    git_history = GitHistory(commits, token_counter=mock_token_counter)

    # Test with a very large token budget
    token_budget = 1_000_000

    for commit in commits:
        handle = commit["current_hash"]

        legacy_diff = git_history.get_commit_diff_legacy(handle, token_budget, True)
        new_diff = git_history.get_commit_diff(handle, token_budget)

        # Assert that both methods produce the same output
        assert legacy_diff == new_diff, f"Mismatch for commit {handle}"

        # Additional assertions to ensure the test is meaningful
        assert len(legacy_diff) > 0, f"Empty diff for commit {handle}"
        assert len(new_diff) > 0, f"Empty diff for commit {handle}"


if __name__ == "__main__":
    test_get_commit_diff_regression()

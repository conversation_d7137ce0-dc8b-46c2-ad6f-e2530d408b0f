import contextlib
import io
import json
import random
import re
import sys
import time
from collections import defaultdict

import git
import git.exc
from git import Repo

from base.prompt_format_chat.generate_commit_message_prompt_formatter import (
    GenerateCommitMessagePromptFormatter,
)
from base.prompt_format_chat.lib.token_counter import RoughTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChangedFileStats,
    ChatPromptInput,
    FileDiff,
    GenerateCommitMessageTokenApportionment,
    PerFileChangeStats,
    PerTypeChangedFileStats,
)
from experimental.zhuoran.commit_msgs.git_structure import (
    GitStructure,
    GitStructureExporter,
)
from experimental.zhuoran.commit_msgs.utils import detect_file_encoding


def extract_insertion_deletion_counts(change_stat_string):
    insertion_stat_pattern = re.compile(r"(\d+) insertion[s]?\(\+\)")
    deletion_stat_pattern = re.compile(r"(\d+) deletion[s]?\(-\)")
    insertion_match = insertion_stat_pattern.search(change_stat_string)
    deletion_match = deletion_stat_pattern.search(change_stat_string)
    if insertion_match:
        insertions = int(insertion_match.group(1))
    else:
        insertions = 0
    if deletion_match:
        deletions = int(deletion_match.group(1))
    else:
        deletions = 0
    return insertions, deletions


class GitHistory:
    INFINITE = int(1e100)

    def __init__(
        self,
        commits: list[dict],
        eval_set: dict[str, list] | None = None,
        git_structure: GitStructure | None = None,
        repo: Repo | None = None,
        token_counter=None,
        system_prompt="",
    ):
        self.commits = commits  # Schema like the example above
        self.eval_set = eval_set
        self.git_structure = git_structure
        self.repo = repo
        self.token_counter = token_counter or (lambda _: 0)
        self.system_prompt = system_prompt

    @classmethod
    def from_json(
        cls,
        path: str,
        eval_set_path: str | None = None,
        git_structure_path: str | None = None,
        repo_path: str | None = None,
        token_counter=None,
        system_prompt="",
        encoding=None,
    ):
        encoding = encoding or detect_file_encoding(path)
        with open(path, "r", encoding=encoding) as f:
            commits = json.load(f)
        eval_set = None
        if eval_set_path:
            with open(eval_set_path, "r") as f:
                eval_set = json.load(f)
        git_structure = None
        if git_structure_path:
            git_structure = GitStructure.from_json(git_structure_path)
        repo = None
        if repo_path:
            repo = Repo(repo_path)
        return cls(commits, eval_set, git_structure, repo, token_counter, system_prompt)

    def get_length(self):
        return len(self.commits)

    def get_eval_iter(self):
        assert self.eval_set is not None
        for reason, split in self.eval_set.items():
            for index, commit in enumerate(split):
                yield reason, index, commit

    def get_eval_example(self, reason, index):
        assert self.eval_set
        return self.eval_set[reason][index]

    def print_eval_set(self, indent=0):
        if not self.eval_set:
            return

        sorted_reasons = sorted(
            self.eval_set.items(),
            key=lambda x: (
                0 if x[0] == "largest_diff" else 1 if x[0] == "smallest_diff" else 2,
                x[0],
            ),
        )
        for reason, commits in sorted_reasons:
            print(f"{' ' * indent}{reason}: {len(commits)}")

    def get_commit_index(self, hash_: str):
        for i in range(len(self.commits) - 1, -1, -1):
            if self.commits[i]["current_hash"] == hash_:
                return i
        return -1

    def _get_index_from_handle(self, handle: int | str):
        if isinstance(handle, int):
            return handle
        return self.get_commit_index(handle)

    def truncate_to_budget(self, text: str, budget: int) -> str:
        """Truncate the text to fit within the given token budget using binary search."""
        if self.token_counter(text) <= budget:
            return text

        text = text[: budget * 4]  # Necessary to accelerate very long diffs

        left, right = 0, len(text)
        while left < right:
            mid = (left + right + 1) // 2
            if self.token_counter(text[:mid]) <= budget:
                left = mid
            else:
                right = mid - 1

        return text[:left]

    def get_commit(self, handle: int | str):
        index = self._get_index_from_handle(handle)
        return self.commits[index]

    def get_random_commit(self):
        return self.commits[random.randint(0, len(self.commits) - 1)]

    def get_recent_commits(self, handle: int | str, count: int, offset: int = 0):
        index = self._get_index_from_handle(handle)
        return self.commits[index + offset + 1 : index + offset + count + 1]

    def get_relevant_commits(self, handle: int | str, limit: int):
        index = self._get_index_from_handle(handle)
        reference_commit = self.commits[index]
        relevant_commits = []
        for i in range(index + 1, min(len(self.commits), index + limit + 1)):
            commit = self.commits[i]
            if reference_commit["author"]["email"] != commit["author"]["email"]:
                break
            relevant_commits.append(commit)
        return relevant_commits

    @staticmethod
    def _scalar_similarity(x, y):
        if x == y:
            return 1.0
        dissimilarity = abs(x - y) / max(x, y)
        return 1 - dissimilarity

    def get_commit_descriptor(
        self, handle: int | str, ratio_threshold: float, descriptor_paths: list[str]
    ):
        # print(f"Describing commit {handle}")
        index = self._get_index_from_handle(handle)
        commit = self.commits[index]
        timings = {}
        start_time = time.time()
        assert self.git_structure is not None
        timings["Repo structure"] = time.time() - start_time
        start_time = time.time()

        descriptor = {
            path: {
                "insertions": 0,
                "deletions": 0,
                "lines": 0,
            }
            for path in descriptor_paths + [""]
        }
        for path, diff in commit["diffs"].items():
            allocated = None
            for descriptor_path in descriptor_paths:
                if path.startswith(descriptor_path):
                    # assert (
                    #     not allocated
                    # ), f"Path {path} allocated to {allocated} and {descriptor_path}"
                    descriptor[descriptor_path]["insertions"] += diff["insertions"]
                    descriptor[descriptor_path]["deletions"] += diff["deletions"]
                    descriptor[descriptor_path]["lines"] += diff["lines"]
                    allocated = descriptor_path
                    break
            if not allocated:
                descriptor[""]["insertions"] += diff["insertions"]
                descriptor[""]["deletions"] += diff["deletions"]
                descriptor[""]["lines"] += diff["lines"]
        empty_paths = []
        for path in descriptor:
            if path == "":
                continue
            if descriptor[path]["lines"] == 0:
                # print(f"Empty path: {path}")
                empty_paths.append(path)
            else:
                pass
                # print(f"Non-empty path: {path}")
        for path in empty_paths:
            del descriptor[path]
        timings["Descriptor construction"] = time.time() - start_time
        # print()
        return descriptor, timings

    def compare_commits(
        self,
        target_descriptor: dict,
        reference_descriptor: dict,
    ):
        assert self.git_structure is not None

        similarity = 0
        for path in reference_descriptor.keys():
            similarity += self._scalar_similarity(
                target_descriptor[path]["insertions"]
                if path in target_descriptor
                else 0,
                reference_descriptor[path]["insertions"],
            )
            similarity += self._scalar_similarity(
                target_descriptor[path]["deletions"]
                if path in target_descriptor
                else 0,
                reference_descriptor[path]["deletions"],
            )
            similarity += self._scalar_similarity(
                target_descriptor[path]["lines"] if path in target_descriptor else 0,
                reference_descriptor[path]["lines"],
            )
        return similarity / len(reference_descriptor.keys())

    def get_descriptor_paths_from_commit(self, handle: int | str):
        index = self._get_index_from_handle(handle)
        return list(self.commits[index]["diffs"].keys())

    def get_similar_commits(
        self,
        handle: int | str,
        commit_count: int,
        search_range: int,
        ratio_threshold: float,
        git_structure_depth_limit: int | None = None,
        git_structure_count_type: str = "line",
        descriptor_path_source: str = "repo",
    ) -> tuple[list[dict], dict]:
        index = self._get_index_from_handle(handle)
        timings = {}
        start_time = time.time()

        start_index = index + 1
        end_index = min(len(self.commits), index + search_range + 1)

        similar_commit_indices = []

        if descriptor_path_source == "repo":
            with contextlib.redirect_stdout(io.StringIO()):
                exporter = GitStructureExporter(
                    self.repo.working_dir,  # type: ignore
                    git_structure_depth_limit,
                    git_structure_count_type,
                )
                timings.update(exporter.timings)
                self.git_structure = GitStructure(exporter.root)
            descripor_paths = self.git_structure.get_tree_as_list(
                ratio_threshold=ratio_threshold
            )
        elif descriptor_path_source == "commit":
            descripor_paths = self.get_descriptor_paths_from_commit(index)
        else:
            raise ValueError(
                f"Unknown descriptor path source: {descriptor_path_source}"
            )
        timings["Similar commits get structure"] = time.time() - start_time
        start_time = time.time()
        reference_descriptor, _ = self.get_commit_descriptor(
            index, ratio_threshold, descripor_paths
        )
        for i in range(start_index, end_index):
            target_descriptor, _ = self.get_commit_descriptor(
                i, ratio_threshold, descripor_paths
            )
            similarity = self.compare_commits(target_descriptor, reference_descriptor)
            similar_commit_indices.append((i, similarity))

        similar_commit_indices.sort(key=lambda x: x[1], reverse=True)
        similar_commits = [
            self.commits[i] for i, _ in similar_commit_indices[:commit_count]
        ]
        timings["Get similar commits"] = time.time() - start_time
        return similar_commits, timings

    def get_example_commits(
        self,
        handle: int | str,
        example_type: str,
        example_commit_count: int,
        relevant_commit_handles: set[str],
        similar_search_range: int,
        ratio_threshold: float,
        git_structure_depth_limit: int | None = None,
        git_structure_count_type: str = "line",
        descriptor_path_source: str = "repo",
    ) -> tuple[list[dict], dict]:
        timings = {}
        start_time = time.time()
        if example_type == "similar":
            example_commits, similar_timings = self.get_similar_commits(
                handle,
                example_commit_count + len(relevant_commit_handles),
                similar_search_range,
                ratio_threshold,
                git_structure_depth_limit,
                git_structure_count_type,
                descriptor_path_source,
            )
            example_commits = [
                commit
                for commit in example_commits
                if commit["current_hash"] not in relevant_commit_handles
            ]
            timings.update(similar_timings)
        elif example_type == "recent":
            example_commits = self.get_recent_commits(
                handle, example_commit_count, len(relevant_commit_handles)
            )
        else:
            raise ValueError(f"Invalid example type: {example_type}")
        timings["Get example commits"] = time.time() - start_time
        return example_commits, timings

    def get_commit_message(self, handle: int | str):
        index = self._get_index_from_handle(handle)
        return self.commits[index]["message"]

    def _summarize_change_type(
        self,
        changed_files_by_type: dict,
        change_type: str,
        imperative: str,
        template: str,
    ):
        front_file_quota = 5
        back_file_quota = 5
        template = "    " + template
        summary = ""
        if change_type in changed_files_by_type:
            summary += (
                f"{imperative} {len(changed_files_by_type[change_type])} files:\n"
            )
            if (
                len(changed_files_by_type[change_type])
                <= front_file_quota + back_file_quota
            ):
                for change in changed_files_by_type[change_type]:
                    summary += template.format(
                        change_type=change_type,
                        insertions=change["insertions"],
                        deletions=change["deletions"],
                        path=change["path"],
                        old_path=change["old_path"],
                    )
            else:
                for change in changed_files_by_type[change_type][:front_file_quota]:
                    summary += template.format(
                        change_type=change_type,
                        insertions=change["insertions"],
                        deletions=change["deletions"],
                        path=change["path"],
                        old_path=change["old_path"],
                    )
                summary += "    ...\n"
                for change in changed_files_by_type[change_type][-back_file_quota:]:
                    summary += template.format(
                        change_type=change_type,
                        insertions=change["insertions"],
                        deletions=change["deletions"],
                        path=change["path"],
                        old_path=change["old_path"],
                    )
        return summary

    def get_v2_summary(self, diffs: dict, summary_v2_threshold: int):
        change_size_by_type = defaultdict(int)
        changed_files_by_type = defaultdict(list)
        # print(len(diffs))
        for path, diff in diffs.items():
            if "change_type" in diff:
                raw_change_type = diff["change_type"]
            else:
                raw_change_type = "U"
            change_type = "R" if raw_change_type.startswith("R") else raw_change_type
            change_size_by_type[change_type] += diff["lines"]
            changed_files_by_type[change_type].append(
                {
                    "change_type": change_type,
                    "insertions": diff["insertions"],
                    "deletions": diff["deletions"],
                    "path": path,
                    "old_path": diff["old_path"],
                }
            )
            # print(f"{path}: {raw_change_type}")
        summary = ""
        if any(
            change_size > summary_v2_threshold
            for change_size in change_size_by_type.values()
        ):
            summary += "The commit contains the following changes:\n"
            summary += self._summarize_change_type(
                changed_files_by_type,
                "M",
                "Modified",
                "{change_type} +{insertions} -{deletions} {path}\n",
            )
            summary += self._summarize_change_type(
                changed_files_by_type,
                "A",
                "Added",
                "{change_type} +{insertions} -{deletions} {path}\n",
            )
            summary += self._summarize_change_type(
                changed_files_by_type,
                "D",
                "Deleted",
                "{change_type} +{insertions} -{deletions} {path}\n",
            )
            summary += self._summarize_change_type(
                changed_files_by_type,
                "R",
                "Renamed",
                "{change_type} +{insertions} -{deletions} {old_path} -> {path}\n",
            )
            summary += self._summarize_change_type(
                changed_files_by_type,
                "C",
                "Copied",
                "{change_type} +{insertions} -{deletions} {old_path} -> {path}\n",
            )
            summary += self._summarize_change_type(
                changed_files_by_type,
                "U",
                "Changed (unknown change type)",
                "{change_type} +{insertions} -{deletions} {path}\n",
            )
        summary += "\n\n"
        return summary

    def get_commit_diff_legacy(
        self, handle: int | str, token_budget: int, sort_paths: bool = False
    ):
        index = self._get_index_from_handle(handle)
        diffs = self.commits[index]["diffs"]

        if sort_paths:
            paths = sorted(diffs.keys())
        else:
            paths = diffs.keys()

        full_diff = ""
        for path in paths:
            diff = diffs[path]
            token_budget -= self.token_counter(diff["content"])
            if token_budget < 0:
                break
            full_diff += diff["content"]

        return full_diff

    def read_diff_from_disk(
        self, handle: int | str, line_limit: int | None = None
    ) -> tuple[dict, dict]:
        index = self._get_index_from_handle(handle)
        commit_hash = self.commits[index]["current_hash"]
        assert self.repo
        commit = self.repo.commit(commit_hash)
        diff_data = defaultdict(dict)
        timings = {}
        start_time = time.time()
        truncated_by_line_limit = False

        parent = commit.parents[0] if commit.parents else None
        if parent:
            git = commit.repo.git
            timings["Diff read getting git"] = time.time() - start_time
            start_time = time.time()
            files = commit.stats.files
            timings["Diff read getting files"] = time.time() - start_time
            start_time = time.time()
            timings["Diff read CLI diff"] = 0.0
            timings["Diff read splitting hunks"] = 0.0
            timings["Diff read getting stats"] = 0.0
            timings["Diff read processing"] = 0.0
            total_diff_string = ""
            try:
                for file_path in files:
                    start_time = time.time()
                    diff_string = git.diff(
                        parent.hexsha, commit.hexsha, "--", file_path
                    )
                    total_diff_string += diff_string
                    timings["Diff read CLI diff"] += time.time() - start_time
                    start_time = time.time()
                    if (
                        line_limit is not None
                        and total_diff_string.count("\n") >= line_limit
                    ):
                        truncated_by_line_limit = True
                        break
                    # hunks = re.split(r"(?=^@@)", diff_string, flags=re.MULTILINE)
                    # timings["Diff read splitting hunks"] += time.time() - start_time
                    # start_time = time.time()
                    stats = files[file_path]
                    timings["Diff read getting stats"] += time.time() - start_time
                    start_time = time.time()
                    diff_data[file_path] = {
                        "old_path": file_path,
                        "content": diff_string,
                        # "header": hunks[0].strip(),
                        "insertions": stats["insertions"],
                        "deletions": stats["deletions"],
                        "lines": stats["lines"],
                        # "hunks": [
                        #     {
                        #         "insertions": sum(
                        #             1
                        #             for line in hunk.splitlines()
                        #             if line.startswith("+")
                        #         ),
                        #         "deletions": sum(
                        #             1
                        #             for line in hunk.splitlines()
                        #             if line.startswith("-")
                        #         ),
                        #         "lines": sum(
                        #             1
                        #             for line in hunk.splitlines()
                        #             if line.startswith("+") or line.startswith("-")
                        #         ),
                        #         "content": hunk.strip(),
                        #     }
                        #     for hunk in hunks[1:]
                        #     if hunk.strip()
                        # ],
                    }
                    timings["Diff read processing"] += time.time() - start_time
                    start_time = time.time()
            except Exception as e:
                print(
                    f"Error processing diff for commit {commit.hexsha}: {str(e)}",
                    file=sys.stderr,
                )
                print(e, file=sys.stderr)

            change_types_string = ""
            try:
                change_types_string = git.diff(
                    "--name-status", parent.hexsha, commit.hexsha
                )
                timings["Diff read getting change types"] = time.time() - start_time
                for change_type_string in change_types_string.splitlines():
                    change_type_columns = change_type_string.split("\t")
                    change_type = change_type_columns[0]
                    file_path = change_type_columns[-1]
                    old_path = (
                        change_type_columns[1]
                        if len(change_type_columns) == 2
                        else file_path
                    )
                    if len(change_type_columns) > 3:
                        print(
                            f"Warning: {len(change_type_columns)} columns in change type string: {change_type_string}"
                        )
                    if file_path in diff_data:
                        diff_data[file_path]["change_type"] = change_type
                        diff_data[file_path]["old_path"] = old_path
                    else:
                        pass
                        # print(f"{file_path} not in diff_data. Existing keys:")
                        # for key in diff_data:
                        #     print(f"  {key}")
                timings["Diff read change type processing"] = time.time() - start_time
            except Exception as e:
                print(
                    f"Error processing change types for commit {commit.hexsha}: {str(e)}",
                    file=sys.stderr,
                )
                if "too many values to unpack" in str(e):
                    string_splits = change_type_string.split("\t")
                    print(
                        f"{len(string_splits)=}",
                        file=sys.stderr,
                    )
                    for string_split in string_splits:
                        print(f"{string_split=}", file=sys.stderr)
        if truncated_by_line_limit:
            timings["line_limited_diff_token_count"] = self.token_counter(
                total_diff_string
            )

        return diff_data, timings

    def _get_diffs(self, handle: int | str, read_from_disk=False, diff_line_limit=None):
        timings = {}
        start_time = time.time()
        commit_index = self._get_index_from_handle(handle)
        if read_from_disk:
            diffs, disk_timings = self.read_diff_from_disk(handle, diff_line_limit)
            # for key in diffs:
            #     diffs[key]["header"] = self.commits[commit_index]["diffs"][key][
            #         "header"
            #     ]
            #     diffs[key]["hunks"] = self.commits[commit_index]["diffs"][key]["hunks"]
            timings.update(disk_timings)
            timings["Original diffs"] = diffs
        else:
            diffs = self.commits[commit_index]["diffs"]
        timings["Get diffs"] = time.time() - start_time
        return diffs, timings

    def get_commit_diff(
        self,
        handle: int | str,
        token_budget: int,
        sort_paths_by_size=False,
        count_diffs_by_hunk=False,
        allow_truncated_last_file=False,
        summary_v2_threshold=None,
        read_from_disk=False,
        diff_line_limit=None,
    ) -> tuple[str, dict]:
        commit_index = self._get_index_from_handle(handle)
        timings = {}
        start_time = time.time()

        assert self.repo
        commit_hash = self.commits[commit_index]["current_hash"]
        git_commit = self.repo.commit(commit_hash)
        timings["Diff getting commit object"] = time.time() - start_time
        start_time = time.time()
        change_stat_string = self.repo.git.execute(
            f"git diff --shortstat {git_commit.hexsha} {git_commit.hexsha}~1",
            shell=True,
        )
        timings["Diff getting shortstat"] = time.time() - start_time
        start_time = time.time()
        total_insertion_count, total_deletion_count = extract_insertion_deletion_counts(
            change_stat_string
        )
        timings["Diff getting total insertion and deletion counts"] = (
            time.time() - start_time
        )
        start_time = time.time()
        total_changed_line_count = total_insertion_count + total_deletion_count
        timings["total_changed_line_count"] = total_changed_line_count
        # assert total_changed_line_count == git_commit.stats.total["lines"], (
        #     total_changed_line_count,
        #     git_commit.stats.total["lines"],
        # )
        if total_changed_line_count > diff_line_limit:
            summary = ""
            timings["Diff overhead before entering summary logic"] = (
                time.time() - start_time
            )
            start_time = time.time()
            # print(f"{total_changed_line_count=}, {summary_v2_threshold=}")
            if (
                summary_v2_threshold is not None
                and total_changed_line_count > summary_v2_threshold
            ):
                summary = "SUMMARY SHOULD BE HERE"
                pass
            timings["Diff summary"] = time.time() - start_time
            start_time = time.time()
            git = self.repo.git
            parent = git_commit.parents[0] if git_commit.parents else None
            timings["Diff getting git"] = time.time() - start_time
            start_time = time.time()
            if parent:
                diff_command = f"git diff {parent.hexsha} {commit_hash} | head -n {diff_line_limit}"
                diff_string = git.execute(diff_command, shell=True)
                full_diff = summary + "\n\n" + diff_string
            else:
                full_diff = summary
            timings["Diff getting diff"] = time.time() - start_time
            start_time = time.time()
            full_diff = self.truncate_to_budget(full_diff, token_budget)
            timings["Diff truncation"] = time.time() - start_time
            timings["Diff get_commit_diff"] = time.time() - start_time
            return full_diff, timings

        if read_from_disk:
            diffs, disk_timings = self.read_diff_from_disk(handle, diff_line_limit)
            # for key in diffs:
            #     diffs[key]["header"] = self.commits[commit_index]["diffs"][key][
            #         "header"
            #     ]
            #     diffs[key]["hunks"] = self.commits[commit_index]["diffs"][key]["hunks"]
            timings.update(disk_timings)
            timings["Original diffs"] = diffs
        else:
            diffs = self.commits[commit_index]["diffs"]
        timings["Diff read"] = time.time() - start_time
        start_time = time.time()

        full_diff = ""
        if summary_v2_threshold is not None:
            summary = self.get_v2_summary(diffs, summary_v2_threshold)
            token_budget -= self.token_counter(summary)
            timings["Diff summary"] = time.time() - start_time
            start_time = time.time()
            if token_budget < 0:
                return summary, timings
            full_diff += summary
            timings["Diff summary"] = time.time() - start_time
            start_time = time.time()

        paths_and_diffs = list(diffs.items())
        if sort_paths_by_size:
            if count_diffs_by_hunk:
                paths_and_diffs.sort(
                    key=lambda x: (
                        sum(
                            hunk["insertions"] + hunk["deletions"]
                            for hunk in x[1]["hunks"]
                        )
                    ),
                )
            else:
                paths_and_diffs.sort(
                    key=lambda x: x[1]["insertions"] + x[1]["deletions"]
                )
            timings["Diff path sorting"] = time.time() - start_time
            start_time = time.time()

        included_paths = set()
        failed_attempt_count = 0
        inner_start_time = time.time()
        inner_timings = {}
        inner_timings["Diff token count"] = 0.0
        inner_timings["Diff token recount"] = 0.0
        inner_timings["Diff path adding"] = 0.0
        for path, diff in paths_and_diffs:
            diff_content = diff["content"]
            diff_token_count = self.token_counter(diff_content)
            inner_timings["Diff token count"] += time.time() - inner_start_time
            inner_start_time = time.time()
            token_budget -= diff_token_count
            if token_budget < 0:
                token_budget += self.token_counter(diff["content"])
                inner_timings["Diff token recount"] += time.time() - inner_start_time
                inner_start_time = time.time()
                failed_attempt_count += 1
                if failed_attempt_count > 50:
                    break
                continue
            included_paths.add(path)
            inner_timings["Diff path adding"] += time.time() - inner_start_time
            inner_start_time = time.time()
        timings["Diff path inclusion"] = time.time() - start_time
        start_time = time.time()
        if timings["Diff path inclusion"] > 10:
            print(self.commits[commit_index]["current_hash"])

        for path in sorted(included_paths):
            diff = diffs[path]
            full_diff += diff["content"]
        timings["Diff adding"] = time.time() - start_time
        start_time = time.time()
        if allow_truncated_last_file and paths_and_diffs[-1][0] not in included_paths:
            partial_inclusion_path = paths_and_diffs[-1][0]
            truncation_marker = "\n\n(Truncated due to length limit.)"
            token_budget -= self.token_counter(truncation_marker)
            truncated_last_diff = self.truncate_to_budget(
                diffs[partial_inclusion_path]["content"], token_budget
            )
            token_budget -= self.token_counter(truncated_last_diff)
            full_diff += truncated_last_diff
            full_diff += truncation_marker
            timings["Diff truncation"] = time.time() - start_time
        timings.update(inner_timings)
        return full_diff, timings

    def get_prompt_legacy(self, handle: int | str, token_budget=12 * 1_024):
        prompt = ""
        prompt += self.get_commit_diff(handle, token_budget)[0]
        latest_commits = self.get_recent_commits(handle, 32)
        for commit in latest_commits:
            prompt += f"{commit['message']}\n"

        left, right = 0, len(prompt)
        while left < right:
            mid = (left + right + 1) // 2
            if self.token_counter(prompt[:mid]) <= token_budget:
                left = mid
            else:
                right = mid - 1

        return prompt[:left]

    def get_summary_prompt(self, handle: int | str, token_budget=None):
        token_budget = token_budget or self.INFINITE

        prompt = ""
        header = "Below is the list of all changed files:\n\n"
        prompt += header
        token_budget -= self.token_counter(header)
        footer = "\n\n"
        token_budget -= self.token_counter(footer)
        if token_budget < 0:
            return ""

        commit = self.get_commit(handle)
        for path, diff in commit["diffs"].items():
            file_summary_prompt = f"{path}: {diff['insertions']} insertions, {diff['deletions']} deletions\n"
            prompt += file_summary_prompt
            token_budget -= self.token_counter(file_summary_prompt)
            if token_budget < 0:
                break
        prompt += footer
        return prompt

    def get_relevant_message_prompt(
        self, handle: int | str, limit=3, token_budget=None
    ):
        token_budget = token_budget or self.INFINITE

        relevant_commits = self.get_relevant_commits(handle, limit)
        if not relevant_commits:
            return "", set()

        prompt = ""
        header = (
            "Below are the immediately preceding commit messages by the current commit author."
            " They might be relevant for understanding the context and intent of the current commit."
            " If the messages indicate a stack style and the current diffs look relevant, increment the stack counter by 1.\n\n"
        )
        prompt += header
        token_budget -= self.token_counter(header)
        footer = "\n\n\n"
        token_budget -= self.token_counter(footer)
        if token_budget < 0:
            return "", set()

        handles = []
        for i, commit in enumerate(relevant_commits):
            handles.append(commit["current_hash"])
            label = "HEAD" if i == 0 else f"HEAD~{i}"
            current_prompt = f"Commit message for {label}:\n"
            current_prompt += f"{commit['message']}\n\n\n"
            token_budget -= self.token_counter(current_prompt)
            if token_budget < 0:
                break
            prompt += current_prompt

        prompt += footer
        return prompt, set(handles)

    def get_example_message_prompt(
        self,
        commits: list[dict[str, dict]],
        example_type="recent",
        token_budget=None,
    ) -> tuple[str, dict]:
        token_budget = token_budget or self.INFINITE
        timings = {}
        start_time = time.time()

        prompt = ""
        if example_type == "recent":
            header = "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n\n\n"
        elif example_type == "similar":
            header = "Below are example commit messages from the same repositories for similar code changes, to illustrate the commit message style and conventions to follow:\n\n\n"
        else:
            raise ValueError(f"Invalid type: {example_type}")

        prompt += header
        token_budget -= self.token_counter(header)
        footer = "Above are all the commit message examples.\n\n\n"
        token_budget -= self.token_counter(footer)
        timings["Example header and footer"] = time.time() - start_time
        start_time = time.time()
        if token_budget < 0:
            return "", timings

        timings["Example get commits"] = time.time() - start_time
        start_time = time.time()

        included_count = 0
        for commit in commits:
            current_prompt = f"Commit message example {included_count}:\n"
            current_prompt += f"{commit['message']}\n\n\n"
            current_token_count = self.token_counter(current_prompt)
            if current_token_count > token_budget:
                continue
            prompt += current_prompt
            token_budget -= current_token_count
            included_count += 1
        timings["Example adding"] = time.time() - start_time

        prompt += footer
        return prompt, timings

    def get_diff_prompt(
        self,
        handle: int | str,
        sort_paths_by_size,
        count_diffs_by_hunk,
        allow_truncated_last_file,
        summary_v2_threshold,
        read_from_disk=False,
        diff_line_limit=None,
        token_budget=None,
        add_header=True,
        add_footer=True,
    ) -> tuple[str, dict]:
        token_budget = token_budget or self.INFINITE
        print(f"{token_budget=}")
        start_time = time.time()

        prompt = ""
        header = "Below are the per-file diffs of the commit:\n\n" if add_header else ""
        prompt += header
        token_budget -= self.token_counter(header)
        footer = "\n\n\n" if add_footer else ""
        token_budget -= self.token_counter(footer)
        if token_budget < 0:
            return "", {}

        core_diff_prompt, timings = self.get_commit_diff(
            handle=handle,
            token_budget=token_budget,
            sort_paths_by_size=sort_paths_by_size,
            count_diffs_by_hunk=count_diffs_by_hunk,
            allow_truncated_last_file=allow_truncated_last_file,
            summary_v2_threshold=summary_v2_threshold,
            read_from_disk=read_from_disk,
            diff_line_limit=diff_line_limit,
        )
        prompt += core_diff_prompt
        token_budget -= self.token_counter(core_diff_prompt)
        prompt += footer
        timings["Diff get_diff_prompt"] = time.time() - start_time
        return prompt, timings

    def get_prompt(
        self,
        handle: int | str,
        summary=False,
        diff=True,
        example_commit_count=32,
        sort_paths_by_size=False,
        count_diffs_by_hunk=False,
        allow_truncated_last_file=False,
        summary_v2_threshold=None,
        trailing_instructions="",
        message_soft_budget=None,
        example_type="recent",
        similar_search_range=1_024,
        ratio_threshold=0.35,
        git_structure_depth_limit=None,
        git_structure_count_type="line",
        descriptor_path_source="repo",
        relevant_message_limit=3,
        relevant_message_token_budget=1_024,
        read_from_disk=False,
        diff_line_limit=None,
        token_budget=12 * 1_024,
    ):
        message_soft_budget = message_soft_budget or self.INFINITE
        current_budget = int(token_budget * 0.995)  # Error buffer

        timings = {}
        start_time = time.time()
        total_start_time = start_time

        if trailing_instructions:
            trailing_instructions = "\n\n\n" + trailing_instructions
        trailing_instruction_token_count = self.token_counter(trailing_instructions)
        current_budget -= trailing_instruction_token_count
        timings["Trailing instructions"] = time.time() - start_time
        start_time = time.time()

        relevant_message_prompt, relevant_commit_handles = (
            self.get_relevant_message_prompt(
                handle,
                limit=relevant_message_limit,
                token_budget=min(relevant_message_token_budget, current_budget),
            )
        )
        relevant_message_token_count = self.token_counter(relevant_message_prompt)
        current_budget -= relevant_message_token_count
        timings["Relevant message"] = time.time() - start_time
        start_time = time.time()

        example_commits, example_commit_timings = self.get_example_commits(
            handle=handle,
            example_type=example_type,
            example_commit_count=example_commit_count,
            relevant_commit_handles=relevant_commit_handles,
            similar_search_range=similar_search_range,
            ratio_threshold=ratio_threshold,
            git_structure_depth_limit=git_structure_depth_limit,
            git_structure_count_type=git_structure_count_type,
            descriptor_path_source=descriptor_path_source,
        )
        timings.update(example_commit_timings)
        example_message_prompt, example_message_timings = (
            self.get_example_message_prompt(
                example_commits,
                example_type=example_type,
                token_budget=min(
                    message_soft_budget - relevant_message_token_count, current_budget
                ),
            )
        )
        example_message_token_count = self.token_counter(example_message_prompt)
        current_budget -= example_message_token_count
        timings["Example message"] = time.time() - start_time
        start_time = time.time()
        # print(f"Initial example message token count: {example_message_token_count}")

        summary_prompt = ""
        summary_token_count = 0
        if summary and current_budget > 0:
            summary_prompt = self.get_summary_prompt(handle, current_budget)
            summary_token_count = self.token_counter(summary_prompt)
            current_budget -= summary_token_count
            timings["Summary"] = time.time() - start_time
            start_time = time.time()

        diff_prompt = ""
        diff_token_count = 0
        diff_timings = {}
        print(f"{current_budget=}")
        if diff and current_budget > 0:
            diff_prompt, diff_timings = self.get_diff_prompt(
                handle,
                sort_paths_by_size,
                count_diffs_by_hunk,
                allow_truncated_last_file,
                summary_v2_threshold,
                read_from_disk,
                diff_line_limit,
                current_budget,
            )
            diff_token_count = self.token_counter(diff_prompt)
            current_budget -= diff_token_count
            timings["Diff"] = time.time() - start_time
            start_time = time.time()

        # Re-expand message section to fill the remaining budget
        # print(
        #     f"Final example message budget: {token_budget - trailing_instruction_token_count - summary_token_count - diff_token_count}"
        # )
        example_commits, example_commits_timings = self.get_example_commits(
            handle=handle,
            example_type=example_type,
            example_commit_count=example_commit_count,
            relevant_commit_handles=relevant_commit_handles,
            similar_search_range=similar_search_range,
            ratio_threshold=ratio_threshold,
            git_structure_depth_limit=git_structure_depth_limit,
            git_structure_count_type=git_structure_count_type,
            descriptor_path_source=descriptor_path_source,
        )
        timings.update(example_commits_timings)
        example_message_prompt, final_example_message_timings = (
            self.get_example_message_prompt(
                example_commits,
                example_type=example_type,
                token_budget=(
                    token_budget
                    - trailing_instruction_token_count
                    - relevant_message_token_count
                    - summary_token_count
                    - diff_token_count
                ),
            )
        )
        example_message_token_count = self.token_counter(example_message_prompt)
        timings["Final example message"] = time.time() - start_time
        # print(f"Final example message token count: {example_message_token_count}")
        assert (
            trailing_instruction_token_count
            + relevant_message_token_count
            + summary_token_count
            + diff_token_count
            + example_message_token_count
            <= token_budget + 50
        ), token_budget - (
            trailing_instruction_token_count
            + relevant_message_token_count
            + summary_token_count
            + diff_token_count
            + example_message_token_count
        )
        # print(
        #     "Remaining budget: ",
        #     token_budget
        #     - (
        #         trailing_instruction_token_count
        #         + relevant_message_token_count
        #         + summary_token_count
        #         + diff_token_count
        #         + example_message_token_count
        #     ),
        # )

        metadata = {
            "relevant_message_prompt": relevant_message_prompt,
            "trailing_instruction_token_count": trailing_instruction_token_count,
            "relevant_message_token_count": relevant_message_token_count,
            "summary_token_count": summary_token_count,
            "diff_token_count": diff_token_count,
            "example_message_token_count": example_message_token_count,
            **timings,
            **diff_timings,
            **example_message_timings,
            **{
                f"Final {k.lower()}": v
                for k, v in final_example_message_timings.items()
            },
            "Total": time.time() - total_start_time,
        }

        return (
            summary_prompt
            + diff_prompt
            + relevant_message_prompt
            + example_message_prompt
            + trailing_instructions
        ), metadata

    def get_changed_file_stats(self, diffs: dict[str, dict]):
        substat_dicts = {}
        for path, diff in diffs.items():
            change_type = diff["change_type"][0] if "change_type" in diff else "X"
            if change_type not in substat_dicts:
                substat_dicts[change_type] = {
                    "changed_file_count": 0,
                    "per_file_change_stats": [],
                }
            substat_dicts[change_type]["changed_file_count"] += 1
            substat_dicts[change_type]["per_file_change_stats"].append(
                PerFileChangeStats(
                    path=path,
                    insertion_count=diff["insertions"],
                    deletion_count=diff["deletions"],
                    old_path=diff["old_path"],
                )
            )
        for substat_dict in substat_dicts.values():
            substat_dict["per_file_change_stats"].sort(
                key=lambda x: x.insertion_count + x.deletion_count,
                reverse=True,
            )
            if len(substat_dict["per_file_change_stats"]) > 10:
                substat_dict["per_file_change_stats_head"] = substat_dict[
                    "per_file_change_stats"
                ][:5]
                substat_dict["per_file_change_stats_tail"] = substat_dict[
                    "per_file_change_stats"
                ][5:]
            else:
                substat_dict["per_file_change_stats_head"] = substat_dict[
                    "per_file_change_stats"
                ]
                substat_dict["per_file_change_stats_tail"] = []
        changed_file_stats = ChangedFileStats(
            insertion_count=sum(
                diff["insertions"] for diff in diffs.values() if "insertions" in diff
            ),
            deletion_count=sum(
                diff["deletions"] for diff in diffs.values() if "deletions" in diff
            ),
            added_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["A"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["A"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["A"][
                    "per_file_change_stats_tail"
                ],
            )
            if "A" in substat_dicts
            else PerTypeChangedFileStats(),
            broken_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["B"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["B"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["B"][
                    "per_file_change_stats_tail"
                ],
            )
            if "B" in substat_dicts
            else PerTypeChangedFileStats(),
            copied_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["C"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["C"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["C"][
                    "per_file_change_stats_tail"
                ],
            )
            if "C" in substat_dicts
            else PerTypeChangedFileStats(),
            deleted_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["D"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["D"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["D"][
                    "per_file_change_stats_tail"
                ],
            )
            if "D" in substat_dicts
            else PerTypeChangedFileStats(),
            modified_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["M"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["M"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["M"][
                    "per_file_change_stats_tail"
                ],
            )
            if "M" in substat_dicts
            else PerTypeChangedFileStats(),
            renamed_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["R"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["R"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["R"][
                    "per_file_change_stats_tail"
                ],
            )
            if "R" in substat_dicts
            else PerTypeChangedFileStats(),
            unmerged_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["U"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["U"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["U"][
                    "per_file_change_stats_tail"
                ],
            )
            if "U" in substat_dicts
            else PerTypeChangedFileStats(),
            unknown_file_stats=PerTypeChangedFileStats(
                changed_file_count=substat_dicts["X"]["changed_file_count"],
                per_file_change_stats_head=substat_dicts["X"][
                    "per_file_change_stats_head"
                ],
                per_file_change_stats_tail=substat_dicts["X"][
                    "per_file_change_stats_tail"
                ],
            )
            if "X" in substat_dicts
            else PerTypeChangedFileStats(),
        )
        return changed_file_stats

    def get_prompt_formatter_inputs(
        self,
        handle: int | str,
        summary=False,
        diff=True,
        latest_commit_count=32,
        sort_paths_by_size=False,
        count_diffs_by_hunk=False,
        allow_truncated_last_file=False,
        summary_v2_threshold=None,
        trailing_instructions="",
        message_soft_budget=None,
        example_type="recent",
        similar_search_range=1_024,
        ratio_threshold=0.35,
        git_structure_depth_limit=None,
        git_structure_count_type="line",
        descriptor_path_source="repo",
        relevant_message_limit=3,
        relevant_message_token_budget=1_024,
        read_from_disk=False,
        diff_line_limit=None,
        diff_v2=False,
        token_budget=12 * 1_024,
    ):
        del summary
        del diff
        del trailing_instructions
        del relevant_message_token_budget
        del token_budget
        del summary_v2_threshold
        message_soft_budget = message_soft_budget or 3 * 1_024

        timings = {}
        start_time = time.time()
        diffs, get_diffs_timings = self._get_diffs(
            handle, read_from_disk, diff_line_limit
        )
        timings.update(get_diffs_timings)
        print("Get index and diffs")
        timings["Get index and diffs"] = time.time() - start_time
        start_time = time.time()

        changed_file_stats = self.get_changed_file_stats(diffs)
        print("Get changed file stats")
        timings["Get changed file stats"] = time.time() - start_time
        start_time = time.time()

        diff, diff_timings = self.get_diff_prompt(
            handle=handle,
            sort_paths_by_size=sort_paths_by_size,
            count_diffs_by_hunk=count_diffs_by_hunk,
            allow_truncated_last_file=allow_truncated_last_file,
            summary_v2_threshold=None,
            read_from_disk=read_from_disk,
            diff_line_limit=diff_line_limit,
            add_header=False,
            add_footer=False,
        )
        print(f"{self.token_counter(diff)=}")
        print("Get diff")
        timings["Get diff"] = time.time() - start_time
        start_time = time.time()

        file_diffs = []
        for diff_path, diff_dict in diffs.items():
            file_diffs.append(
                FileDiff(
                    path=diff_path,
                    content=diff_dict["content"],
                )
            )

        timings.update(diff_timings)
        relevant_commit_messages = [
            commit["message"]
            for commit in self.get_relevant_commits(
                handle=handle, limit=relevant_message_limit
            )
        ]
        print("Get relevant commit messages")
        timings["Get relevant commit messages"] = time.time() - start_time
        start_time = time.time()

        example_commits, _ = self.get_example_commits(
            handle=handle,
            example_type=example_type,
            example_commit_count=latest_commit_count,
            relevant_commit_handles=set(),
            similar_search_range=similar_search_range,
            ratio_threshold=ratio_threshold,
            git_structure_depth_limit=git_structure_depth_limit,
            git_structure_count_type=git_structure_count_type,
            descriptor_path_source=descriptor_path_source,
        )
        example_commit_messages = [commit["message"] for commit in example_commits]
        print("Get example commit messages")
        timings["Get example commit messages"] = time.time() - start_time
        start_time = time.time()

        return (
            changed_file_stats,
            diff if not diff_v2 else None,
            file_diffs if diff_v2 else None,
            relevant_commit_messages,
            example_commit_messages,
            timings,
        )

    def get_prompt_using_formatter(
        self,
        handle: int | str,
        summary=False,
        diff=True,
        latest_commit_count=32,
        sort_paths_by_size=False,
        count_diffs_by_hunk=False,
        allow_truncated_last_file=False,
        summary_v2_threshold=None,
        trailing_instructions="",
        message_soft_budget=None,
        example_type="recent",
        similar_search_range=1_024,
        ratio_threshold=0.35,
        git_structure_depth_limit=None,
        git_structure_count_type="line",
        descriptor_path_source="repo",
        relevant_message_limit=3,
        relevant_message_token_budget=1_024,
        read_from_disk=False,
        diff_line_limit=None,
        diff_budget=None,
        diff_v2=False,
        system_prompt_v2=None,
        token_budget=12 * 1_024,
    ):
        timings = {}
        start_time = time.time()
        summary_v2_threshold = summary_v2_threshold or self.INFINITE
        message_soft_budget = message_soft_budget or 3 * 1_024
        if diff_budget is None:
            assert message_soft_budget != -1
            diff_budget = token_budget - message_soft_budget
        formatter = GenerateCommitMessagePromptFormatter(
            token_counter=RoughTokenCounter(),
            token_apportionment=GenerateCommitMessageTokenApportionment(
                changed_files_summary_line_threshold=summary_v2_threshold,
                diff_len=diff_budget,
                commit_message_soft_budget=message_soft_budget,
                relevant_message_len=relevant_message_token_budget,
                max_prompt_len=token_budget,
                path_len=0,
                message_len=0,
                chat_history_len=0,
                prefix_len=0,
                selected_code_len=0,
                suffix_len=0,
            ),
        )
        if system_prompt_v2 is not None:
            formatter.system_prompt = system_prompt_v2
        print("Get formatter")
        timings["Get formatter"] = time.time() - start_time
        start_time = time.time()

        (
            changed_file_stats,
            diff,
            diffs,
            relevant_commit_messages,
            example_commit_messages,
            inner_timings,
        ) = self.get_prompt_formatter_inputs(
            handle=handle,
            summary=summary,
            diff=diff,
            latest_commit_count=latest_commit_count,
            sort_paths_by_size=sort_paths_by_size,
            count_diffs_by_hunk=count_diffs_by_hunk,
            allow_truncated_last_file=allow_truncated_last_file,
            summary_v2_threshold=summary_v2_threshold,
            trailing_instructions=trailing_instructions,
            message_soft_budget=message_soft_budget,
            example_type=example_type,
            similar_search_range=similar_search_range,
            ratio_threshold=ratio_threshold,
            git_structure_depth_limit=git_structure_depth_limit,
            git_structure_count_type=git_structure_count_type,
            descriptor_path_source=descriptor_path_source,
            relevant_message_limit=relevant_message_limit,
            relevant_message_token_budget=relevant_message_token_budget,
            read_from_disk=read_from_disk,
            diff_line_limit=diff_line_limit,
            diff_v2=diff_v2,
            token_budget=token_budget,
        )
        print("Get formatter inputs")
        timings["Get formatter inputs"] = time.time() - start_time
        timings.update(inner_timings)
        start_time = time.time()

        prompt_output = formatter.format_prompt(
            ChatPromptInput(
                message="",
                path="",
                prefix="",
                selected_code="",
                suffix="",
                chat_history=[],
                prefix_begin=0,
                suffix_end=0,
                retrieved_chunks=[],
                changed_file_stats=changed_file_stats,
                diff=diff,
                diffs=diffs,
                relevant_commit_messages=relevant_commit_messages,
                example_commit_messages=example_commit_messages,
            )
        )
        print("Format prompt")
        timings["Format prompt"] = time.time() - start_time
        return prompt_output, timings


class MultiGitHistory:
    def __init__(
        self,
        multi_repo_commits: dict[
            str, tuple[list[dict], dict[str, list], GitStructure, Repo]
        ],
        token_counter=None,
        system_prompt="",
    ):
        self.git_histories = {
            label: GitHistory(
                commits, eval_set, git_structure, repo, token_counter, system_prompt
            )
            for label, (
                commits,
                eval_set,
                git_structure,
                repo,
            ) in multi_repo_commits.items()
        }
        self.token_counter = token_counter or (lambda _: 0)

    @classmethod
    def from_jsons(
        cls,
        paths_and_encodings: dict[
            str,
            tuple[
                str,
                str,
                str,
                str,
                str | None,
            ],
        ],
        token_counter=None,
        system_prompt="",
    ):
        multi_repo_commits = {}
        commit_caches = {}
        for label, (
            commits_path,
            eval_set_path,
            git_structure_path,
            repo_path,
            encoding,
        ) in paths_and_encodings.items():
            try:
                encoding = encoding or detect_file_encoding(commits_path)
                if commits_path in commit_caches:
                    commits = commit_caches[commits_path]
                else:
                    with open(commits_path, "r", encoding=encoding) as f:
                        commits = json.load(f)
                with open(eval_set_path, "r") as f:
                    eval_set = json.load(f)
                git_structure = GitStructure.from_json(git_structure_path)
                repo = Repo(repo_path)
                multi_repo_commits[label] = commits, eval_set, git_structure, repo
                commit_caches[commits_path] = commits
                # print(f"Loaded {label} with {len(commits)} commits")
            except Exception as e:
                print(f"Failed and skipping loading {label}: {e}")
                raise e
        return cls(multi_repo_commits, token_counter, system_prompt)

    def add_repo(
        self,
        label: str,
        commits: list[dict],
        eval_set: dict[str, list],
        git_structure: GitStructure,
        repo: Repo,
    ):
        self.git_histories[label] = GitHistory(
            commits, eval_set, git_structure, repo, self.token_counter
        )

    def load_repo(
        self,
        label: str,
        commits_path: str,
        eval_set_path: str,
        git_structure_path: str,
        repo_path: str,
        encoding: str | None = None,
    ):
        encoding = encoding or detect_file_encoding(commits_path)
        with open(commits_path, "r", encoding=encoding) as f:
            commits = json.load(f)
        with open(eval_set_path, "r") as f:
            eval_set = json.load(f)
        git_structure = GitStructure.from_json(git_structure_path)
        repo = Repo(repo_path)
        self.add_repo(label, commits, eval_set, git_structure, repo)

    def get_eval_set(self, label: str):
        return self.git_histories[label].eval_set

    def set_eval_set(self, label: str, eval_set: dict[str, list]):
        assert label in self.git_histories
        self.git_histories[label].eval_set = eval_set

    def set_eval_sets(self, eval_sets: dict[str, dict[str, list]]):
        for label, git_history in self.git_histories.items():
            if label in eval_sets:
                git_history.eval_set = eval_sets[label]

    def load_eval_set(self, label: str, eval_set_path: str):
        with open(eval_set_path, "r") as f:
            eval_set = json.load(f)
        self.set_eval_set(label, eval_set)

    def load_eval_sets(self, eval_set_paths: dict[str, str]):
        for label, eval_set_path in eval_set_paths.items():
            self.load_eval_set(label, eval_set_path)

    def get_eval_iter(self):
        for label, git_history in self.git_histories.items():
            for reason, index, commit in git_history.get_eval_iter():
                yield label, reason, index, commit

    def get_eval_example(self, label: str, reason: str, index: int):
        return self.git_histories[label].get_eval_example(reason, index)

    def reload_token_counter(self, token_counter):
        self.token_counter = token_counter
        for git_history in self.git_histories.values():
            git_history.token_counter = token_counter

    def get_lengths(self):
        return {
            label: git_history.get_length()
            for label, git_history in self.git_histories.items()
        }

    def get_labels(self):
        return list(self.git_histories.keys())

    def print_eval_set(self, label: str, indent=0):
        git_history = self.git_histories[label]
        git_history.print_eval_set(indent)

    def print_eval_sets(self, indent=0):
        for label, git_history in self.git_histories.items():
            print(f"{' ' * indent}{label}:")
            git_history.print_eval_set(indent + 4)

    def _get_index_from_handle(self, handle: tuple[str, int] | str):
        if isinstance(handle, tuple):
            return handle
        i = -1
        label = ""
        for label, git_history in self.git_histories.items():
            i = git_history.get_commit_index(handle)
            if i != -1:
                break
        return label, i

    def get_label(self, handle: tuple[str, int] | str):
        label, _ = self._get_index_from_handle(handle)
        return label

    def get_git_history(self, handle: tuple[str, int] | str):
        label = self.get_label(handle)
        return self.git_histories[label] if label else None

    def get_commit_index(self, hash_: str):
        git_history = self.get_git_history(hash_)
        if git_history:
            return git_history.get_commit_index(hash_)
        raise ValueError(f"Hash {hash_} not found in any of the repositories.")

    def get_commit(self, handle: tuple[str, int] | str):
        git_history = self.get_git_history(handle)
        if not git_history:
            raise ValueError(f"Handle {handle} not found in any of the repositories.")

        if isinstance(handle, tuple):
            return git_history.get_commit(handle[1])
        return git_history.get_commit(handle)

    def get_random_commit(self):
        git_histories = list(self.git_histories.values())
        return random.choice(git_histories).get_random_commit()

    def get_commit_message(self, handle: tuple[str, int] | str):
        commit = self.get_commit(handle)
        return commit["message"]

    def get_commit_diff(self, handle: tuple[str, int] | str):
        commit = self.get_commit(handle)
        return commit["diff"]

    def get_prompt_legacy(self, handle: tuple[str, int] | str, token_budget: int):
        git_history = self.get_git_history(handle)
        if not git_history:
            raise ValueError(f"Handle {handle} not found in any of the repositories.")

        inner_handle = self._get_index_from_handle(handle)[1]
        return git_history.get_prompt_legacy(inner_handle, token_budget)

    def get_prompt(
        self,
        handle: tuple[str, int] | str,
        summary=False,
        diff=True,
        latest_commit_count=32,
        sort_paths_by_size=False,
        count_diffs_by_hunk=False,
        allow_truncated_last_file=False,
        summary_v2_threshold=None,
        trailing_instructions="",
        message_soft_budget=None,
        example_type="recent",
        similar_search_range=1_024,
        ratio_threshold=0.35,
        git_structure_depth_limit=None,
        git_structure_count_type="line",
        descriptor_path_source="repo",
        relevant_message_limit=3,
        relevant_message_token_budget=1_024,
        read_from_disk=False,
        diff_line_limit=None,
        token_budget=12 * 1_024,
    ):
        git_history = self.get_git_history(handle)
        if not git_history:
            raise ValueError(f"Handle {handle} not found in any of the repositories.")

        inner_handle = self._get_index_from_handle(handle)[1]
        return git_history.get_prompt(
            handle=inner_handle,
            summary=summary,
            diff=diff,
            example_commit_count=latest_commit_count,
            sort_paths_by_size=sort_paths_by_size,
            count_diffs_by_hunk=count_diffs_by_hunk,
            allow_truncated_last_file=allow_truncated_last_file,
            summary_v2_threshold=summary_v2_threshold,
            trailing_instructions=trailing_instructions,
            message_soft_budget=message_soft_budget,
            example_type=example_type,
            similar_search_range=similar_search_range,
            ratio_threshold=ratio_threshold,
            git_structure_depth_limit=git_structure_depth_limit,
            git_structure_count_type=git_structure_count_type,
            descriptor_path_source=descriptor_path_source,
            relevant_message_limit=relevant_message_limit,
            relevant_message_token_budget=relevant_message_token_budget,
            read_from_disk=read_from_disk,
            diff_line_limit=diff_line_limit,
            token_budget=token_budget,
        )

    def get_prompt_using_formatter(
        self,
        handle: tuple[str, int] | str,
        summary=False,
        diff=True,
        latest_commit_count=32,
        sort_paths_by_size=False,
        count_diffs_by_hunk=False,
        allow_truncated_last_file=False,
        summary_v2_threshold=None,
        trailing_instructions="",
        message_soft_budget=None,
        example_type="recent",
        similar_search_range=1_024,
        ratio_threshold=0.35,
        git_structure_depth_limit=None,
        git_structure_count_type="line",
        descriptor_path_source="repo",
        relevant_message_limit=3,
        relevant_message_token_budget=1_024,
        read_from_disk=False,
        diff_line_limit=None,
        diff_budget=None,
        diff_v2=False,
        system_prompt_v2=None,
        token_budget=12 * 1_024,
    ):
        timings = {}
        start_time = time.time()
        git_history = self.get_git_history(handle)
        if not git_history:
            raise ValueError(f"Handle {handle} not found in any of the repositories.")
        timings["Get git history"] = time.time() - start_time
        start_time = time.time()

        inner_handle = self._get_index_from_handle(handle)[1]
        timings["Get inner handle"] = time.time() - start_time
        start_time = time.time()
        prompt_output, inner_timings = git_history.get_prompt_using_formatter(
            inner_handle,
            summary=summary,
            diff=diff,
            latest_commit_count=latest_commit_count,
            sort_paths_by_size=sort_paths_by_size,
            count_diffs_by_hunk=count_diffs_by_hunk,
            allow_truncated_last_file=allow_truncated_last_file,
            summary_v2_threshold=summary_v2_threshold,
            trailing_instructions=trailing_instructions,
            message_soft_budget=message_soft_budget,
            example_type=example_type,
            similar_search_range=similar_search_range,
            ratio_threshold=ratio_threshold,
            git_structure_depth_limit=git_structure_depth_limit,
            git_structure_count_type=git_structure_count_type,
            descriptor_path_source=descriptor_path_source,
            relevant_message_limit=relevant_message_limit,
            relevant_message_token_budget=relevant_message_token_budget,
            read_from_disk=read_from_disk,
            diff_line_limit=diff_line_limit,
            diff_budget=diff_budget,
            diff_v2=diff_v2,
            system_prompt_v2=system_prompt_v2,
            token_budget=token_budget,
        )
        timings.update(inner_timings)
        return prompt_output, timings

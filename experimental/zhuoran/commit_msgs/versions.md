# Commit message data versions

## Commit data

- V0
- V1
    - Correct file-level diff stats
- V2
    - New commit range
- V3
    - Revert to V1 commit range
        - Full set is not completed and also did not revert to V1 commit range
    - Add change type
- V4
    - Add old_path
    - Fully revert to V1 commit range
- V5
    - Use UTF-16

## Evaluation set

- V0
- V1
    - 5 commits per folder => 30 per folder
- V2
    - Post-inject full_set_index
- V3
    - Latest folder commits => random folder commits
- V4
    - Add native split_index
- V5
    - 5 smallest and 5 largest commits => 30 each

# Latency optimization

- V0
- V1
    - Structure depth limit to 2
- V2
    - Limit diff sorting to 1000 lines
- V3
    - Remove duplicate similarity searches
- V4
    - Use file count instead of line count
- V5
    - Dumb truncation for large diffs
    - Diff line limit to 5000
    - Use native Git to get diffs

# Git structure

- V0
- V1
    - From having both file and line ratios to one of them

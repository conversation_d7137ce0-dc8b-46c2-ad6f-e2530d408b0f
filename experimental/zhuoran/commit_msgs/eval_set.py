import json
import random
from collections import Counter, defaultdict
from pathlib import Path

from experimental.zhuoran.commit_msgs.git_history import GitHist<PERSON>


def get_diff_stats(commit):
    total_insertions = 0
    total_deletions = 0
    for diff in commit["diffs"].values():
        total_insertions += diff["insertions"]
        total_deletions += diff["deletions"]
    return total_insertions, total_deletions


def get_folder_percentages(commit):
    file_counts = Counter()
    for file_path in commit["diffs"].keys():
        top_level_folder = Path(file_path).parts[0]
        file_counts[top_level_folder] += 1

    total_files = sum(file_counts.values())
    percentages = [
        (folder, count / total_files * 100) for folder, count in file_counts.items()
    ]
    return sorted(percentages, key=lambda x: x[1], reverse=True)


def make_eval_split(eval_subset, reason):
    eval_split = []
    for index, commit in eval_subset:
        insertions, deletions = get_diff_stats(commit)
        folder_percentages = get_folder_percentages(commit)

        eval_split.append(
            {
                "commit_index": index,
                "diff_stats": {
                    "insertions": insertions,
                    "deletions": deletions,
                    "total_changes": insertions + deletions,
                },
                "folder_percentages": folder_percentages,
                "reason": reason,
            }
        )
    return eval_split


def make_eval_set(commits_path, eval_set_path, encoding=None):
    # Load the commits
    git_history = GitHistory.from_json(commits_path, encoding=encoding)

    # 1. Top-30 largest-diff commits
    largest_diff_commits = sorted(
        enumerate(git_history.commits),
        key=lambda c: sum(get_diff_stats(c[1])),
        reverse=True,
    )[:30]

    # 2. Top-30 smallest-diff commits (excluding commits with no changes)
    smallest_diff_commits = sorted(
        [
            (i, c)
            for i, c in enumerate(git_history.commits)
            if sum(get_diff_stats(c)) > 0
        ],
        key=lambda c: sum(get_diff_stats(c[1])),
    )[:30]

    # 3. 30 random commits that touch each top-level subfolder
    folder_random_commits = {}
    for i, commit in enumerate(git_history.commits):
        for file_path in commit["diffs"].keys():
            path_parts = Path(file_path).parts
            if len(path_parts) < 2:
                continue
            top_level_folder = path_parts[0]
            if top_level_folder not in folder_random_commits:
                folder_random_commits[top_level_folder] = []
            folder_random_commits[top_level_folder].append((i, commit))

    # Randomly select 30 commits for each folder
    for folder in folder_random_commits:
        if len(folder_random_commits[folder]) > 30:
            folder_random_commits[folder] = random.sample(
                folder_random_commits[folder], 30
            )

    eval_set = []

    # Create the evaluation set
    eval_set = defaultdict(list)
    eval_set["largest_diff"] = make_eval_split(largest_diff_commits, "largest_diff")
    eval_set["smallest_diff"] = make_eval_split(smallest_diff_commits, "smallest_diff")
    for folder, commits in folder_random_commits.items():
        eval_set[folder] = make_eval_split(commits, folder)

    # Add the split index
    for reason, split in eval_set.items():
        for split_index, example in enumerate(split):
            example["split_index"] = split_index

    # Print the evaluation set
    print(f"Total evaluation items: {sum(len(eval_set[key]) for key in eval_set)}")
    for key in eval_set:
        print(f"Reason: {key}")
        for item in eval_set[key]:
            print(f"\nCommit index: {item['commit_index']}")
            print(
                f"Diff stats: +{item['diff_stats']['insertions']}, -{item['diff_stats']['deletions']}, total: {item['diff_stats']['total_changes']}"
            )
            print("Folder percentages:")
            for folder, percentage in item["folder_percentages"]:
                print(f"  {folder}: {percentage:.2f}%")
        print("\n\n")

    with open(eval_set_path, "w") as f:
        json.dump(eval_set, f, indent=4)


if __name__ == "__main__":
    make_eval_set(
        commits_path="/home/<USER>/zhuoran/commit_msgs_spark/commits_v5.json",
        eval_set_path="/home/<USER>/zhuoran/commit_msgs_spark/eval_set_v5.json",
        encoding="utf-16",
    )

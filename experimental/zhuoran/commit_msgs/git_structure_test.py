import random
from typing import List

import pytest

from experimental.zhuoran.commit_msgs.git_structure import GitNode, GitStructure


def generate_random_tree(max_depth: int, max_children: int) -> GitNode:
    def create_node(path: str, depth: int) -> GitNode:
        node = GitNode(path=path)
        node.ratio = random.random()
        if depth < max_depth:
            num_children = random.randint(0, max_children)
            for i in range(num_children):
                child_path = f"{path}/child_{i}"
                node.children[f"child_{i}"] = create_node(child_path, depth + 1)
        return node

    return create_node("", 0)


def compare_list_outputs(ref_list: List[str], target_list: List[str], git_structure):
    assert len(ref_list) == len(target_list), git_structure.get_tree(ratio_threshold=0)
    for item1, item2 in zip(ref_list, target_list):
        assert item1 == item2, git_structure.get_tree(ratio_threshold=0)


@pytest.mark.parametrize("test_id", range(100))  # Run 100 test cases
def test_get_tree_as_list_regression(test_id):
    max_depth = random.randint(1, 10)
    max_children = random.randint(1, 5)
    root = generate_random_tree(max_depth, max_children)
    git_structure = GitStructure(root)

    # Test cases
    test_cases = [
        (None, 0.5),  # Default ratio_threshold
        (
            random.randint(1, max_depth),
            0.5,
        ),  # Random max_level, default ratio_threshold
        (None, random.random()),  # Random ratio_threshold
        (random.randint(1, max_depth), random.random()),  # Both random
    ]

    for max_level, ratio_threshold in test_cases:
        assert ratio_threshold is not None
        v1_output = git_structure.get_tree_as_list_v1(
            max_level=max_level, ratio_threshold=ratio_threshold
        )
        original_output = git_structure.get_tree_as_list(
            max_level=max_level, ratio_threshold=ratio_threshold
        )
        compare_list_outputs(
            ref_list=original_output, target_list=v1_output, git_structure=git_structure
        )

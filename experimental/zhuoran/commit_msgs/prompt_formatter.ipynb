{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'augment_recent': 12500,\n", " 'augment': 12500,\n", " 'angular': 21072,\n", " 'pytorch': 10276,\n", " 'linux': 1000,\n", " 'wine': 6700,\n", " 'beauty_net': 178}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from base.prompt_format_chat.lib.token_counter import RoughTokenCounter\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from experimental.zhuoran.commit_msgs.git_history import MultiGitHistory\n", "import time\n", "\n", "\n", "class TokenCounterWrapper:\n", "    def __init__(self, inner_counter):\n", "        self.inner_counter = inner_counter\n", "        self.token_count = 0\n", "        self.char_count = 0\n", "        self.ratios = []\n", "\n", "    def count_tokens(self, prompt_chars: str) -> int:\n", "        token_count = self.inner_counter.count_tokens(prompt_chars)\n", "        self.token_count += token_count\n", "        self.char_count += len(prompt_chars)\n", "        if len(prompt_chars) > 0:\n", "            self.ratios.append(token_count / len(prompt_chars))\n", "        return token_count\n", "\n", "\n", "claude_token_counter = ClaudeTokenCounter()\n", "wrapped_token_counter = TokenCounterWrapper(ClaudeTokenCounter())\n", "rough_token_counter = RoughTokenCounter()\n", "token_counter = rough_token_counter\n", "\n", "mgh = MultiGitHistory.from_jsons(\n", "    {\n", "        \"augment_recent\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/eval_set_v1_new_keys.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json\",\n", "            \"/home/<USER>/augment/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"augment\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json\",\n", "            \"/home/<USER>/augment/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"angular\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_angular/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_angular/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_angular/git_structure_v1.json\",\n", "            \"/home/<USER>/angular/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"pytorch\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_pytorch/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_pytorch/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_pytorch/git_structure_v1.json\",\n", "            \"/home/<USER>/pytorch/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"linux\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_linux/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_linux/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_linux/git_structure_v1.json\",\n", "            \"/home/<USER>/linux/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"wine\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_wine/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_wine/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_wine/git_structure_v1.json\",\n", "            \"/home/<USER>/wine/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"beauty_net\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs_beauty_net/commits_v4.json\",\n", "            \"/mnt/efs/augment/user/zhu<PERSON>/commit_msgs_beauty_net/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs_beauty_net/git_structure_v1.json\",\n", "            \"/home/<USER>/beauty-net/\",\n", "            \"utf-8\",\n", "        ),\n", "    },\n", "    token_counter.count_tokens,\n", ")\n", "mgh.get_lengths()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Angular\tLargest\t2\n", "# Angular\tPackages\t1\n", "# Angular\tPackages\t2\n", "# Angular\tPackages\t3\n", "# Angular\tPackages\t4\n", "# PyTorch\tDocs\t0\n", "# PyTorch\tTorch\t0\n", "# PyTorch\tTorch\t1\n", "# PyTorch\tTorch\t2\n", "# PyTorch\tTorch\t3\n", "# Linux\tLargest\t2\n", "# Linux\tKernel\t0\n", "# Linux\tMM\t0\n", "# Linux\tInit\t2\n", "# Wine\tServer\t1\n", "# Wine\tServer\t5\n", "# Wine\tServer\t7\n", "# BeautyNet\tBeauty\t0\n", "# Augment (recent)\tModels\t1\n", "# Augment (recent)\tExperimental\t0\n", "# Augment (recent)\tTools\t3\n", "# Augment (recent)\tSmallest\t3\n", "# Augment (recent)\tLargest\t4\n", "# Augment (recent)\tLargest\t0\n", "# Augment (recent)\tServices\t0\n", "core_eval_set = [\n", "    (\"angular\", \"largest_diff\", 2),\n", "    (\"angular\", \"packages\", 1),\n", "    (\"angular\", \"packages\", 2),\n", "    (\"angular\", \"packages\", 3),\n", "    (\"angular\", \"packages\", 4),\n", "    (\"pytorch\", \"docs\", 0),\n", "    (\"pytorch\", \"torch\", 0),\n", "    (\"pytorch\", \"torch\", 1),\n", "    (\"pytorch\", \"torch\", 2),\n", "    (\"pytorch\", \"torch\", 3),\n", "    (\"linux\", \"largest_diff\", 2),\n", "    (\"linux\", \"kernel\", 0),\n", "    (\"linux\", \"mm\", 0),\n", "    (\"linux\", \"init\", 2),\n", "    (\"wine\", \"server\", 1),\n", "    (\"wine\", \"server\", 5),\n", "    (\"wine\", \"server\", 7),\n", "    (\"beauty_net\", \"beauty\", 0),\n", "    (\"augment_recent\", \"models\", 1),\n", "    (\"augment_recent\", \"experimental\", 0),\n", "    (\"augment_recent\", \"tools\", 3),\n", "    (\"augment_recent\", \"smallest_diff\", 3),\n", "    (\"augment_recent\", \"largest_diff\", 4),\n", "    (\"augment_recent\", \"largest_diff\", 0),\n", "    (\"augment_recent\", \"services\", 0),\n", "]\n", "core_eval_set_commits = [\n", "    mgh.get_commit((example[0], mgh.get_eval_example(*example)[\"commit_index\"]))\n", "    for example in core_eval_set\n", "]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["TRAILING_INSTRUCTIONS = \"\"\"Do not include boilerplate text like \"Here is the commit message:\" in your response..\"\"\""]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["augment_recent\n"]}], "source": ["# commit = mgh.get_commit(\"be602cde657ee43d23adbf309be6d700d0106dc9\")\n", "commit = mgh.get_commit(\"644f2c9452f6ca6e48222091619dbabec6537779\")\n", "print(mgh.get_label(commit[\"current_hash\"]))"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Below are the per-file diffs of the commit:\n", "\n", "\n", "\n", "diff --git a/clients/vscode/src/main-panel/action-cards/actions-model.ts b/clients/vscode/src/main-panel/action-cards/actions-model.ts\n", "index 16f324899..897ad939a 100644\n", "--- a/clients/vscode/src/main-panel/action-cards/actions-model.ts\n", "+++ b/clients/vscode/src/main-panel/action-cards/actions-model.ts\n", "@@ -158,7 +158,6 @@ const initialDerivedStates = new Map<DerivedStateName, DerivedState>([\n", "                 },\n", "                 // { name: SystemStateName.hasMovedExtensionAside, status: SystemStatus.complete },\n", "                 { name: SystemStateName.workspacePopulated, status: SystemStatus.complete },\n", "-                { name: SystemStateName.summaryReady, status: SystemStatus.complete },\n", "             ],\n", "         },\n", "     ],\n", "\n", "\n", "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n", "\n", "\n", "Commit message example 0:\n", "infra/grafana: Stand up in gcp-core0\n", "\n", " - Use the kubecfg helm wrapper (this was the last full helm chart)\n", " - Replace `grafana.r.augmentcode.com` which was a PoC running in old CW\n", "   - But the deployment is simpler because we can now use an ingress\n", " - As before, use oauth2 with <EMAIL> members\n", "   automatically configured as site-wide admins.\n", " - Only the CoreWeave Prometheus and Devex Metrics (the NEW one in\n", "   gcp-core0) are currently added as datasources.\n", "   - <PERSON><PERSON><PERSON>, determined, and loki are excluded for now\n", " - Start with the json dashboards I'd expected out of the origina CW\n", "   grafana.\n", "\n", "I need to remember how I did the original exports. Rather than doing a\n", "DB migration, I'd like to stick to this git-first approach and check in\n", "exports of dashboards, alerts, and notifications from the original CW\n", "grafana.\n", "\n", "\n", "\n", "Commit message example 1:\n", "Don't redirect augmentcode.com to staging (#13990)\n", "\n", "For the play-test tomorrow.\n", "\n", "\n", "Commit message example 2:\n", "flags for dry test (#14047)\n", "\n", "Co-authored-by: <PERSON><PERSON> <<EMAIL>>\n", "\n", "\n", "Commit message example 3:\n", "[intellij] fixed NPE (#14043)\n", "\n", "\n", "\n", "\n", "Commit message example 4:\n", "Ignore alerts in cnrm-system namespace (#14037)\n", "\n", "This looks to be related to config-connector, so should be managed by\n", "GKE\n", "\n", "\n", "Commit message example 5:\n", "Round-robin load-balancing with sticky sessions for next-edit inference requests (#14041)\n", "\n", "Toggle the feature of the inference host client to have each next-edit\n", "host route all inference requests for a given request_session_id to a\n", "single inference host for a period of time.\n", "\n", "With that in place, we can enable round-robin selection of gRPC\n", "sub-channels without losing the benefits of caching on the\n", "inference host.\n", "\n", "Tested the config in dev deploy.\n", "\n", "\n", "Commit message example 6:\n", "fix (#14035)\n", "\n", "\n", "\n", "\n", "Commit message example 7:\n", "AU-4847: Make summary show with syncing status done (#13969)\n", "\n", "This updates the onboarding model to show the summary message once the\n", "syncing is \"done\" the same as the progress bar and status bar.\n", "\n", "As part of this PR I've added tests, noticed a number of misconceptions\n", "with the previous implementation that I've hopefully corrected.\n", "\n", "\n", "https://github.com/user-attachments/assets/68f403c7-4d7a-4247-a2db-9e9bd6522073\n", "\n", "\n", "Commit message example 8:\n", "AU-4874 Adding e2e test for share service (#14013)\n", "\n", "Adding an end-to-end test for the Share service.\n", "\n", "Test:\n", "https://test-viewer.us-central1.dev.augmentcode.com/run/01929ba9-7f32-758e-1612-5ddc49fcc069\n", "\n", "\n", "Commit message example 9:\n", "Use ModelArgs.load_from_dict to load. (#14019)\n", "\n", "We used to call `ModelArgs.schema().load(params)` directly, but this is not backward compatible with old `params` and we need to remember to call `fix_model_arg_params`, which I didn't know existed. This PR slightly improves the situation by defining a new static method `ModelArgs.load_from_dict` and encourage its uses.\n", "\n", "\n", "Commit message example 10:\n", "Scale to fewer indexers (#14032)\n", "\n", "Now that we have better indexer parallelism, we shouldn't\n", "need to go as high.\n", "\n", "Testing done: None\n", "\n", "\n", "Commit message example 11:\n", "remove pre_attention_kernel_fusion flag from inference server (#13840)\n", "\n", "Just a minor cleanup.\n", "\n", "\n", "Commit message example 12:\n", "Next edit host cleanup: encapsulate request parameters (#14001)\n", "\n", "\n", "\n", "\n", "Commit message example 13:\n", "Add view for noncustomer tenants (#14022)\n", "\n", "Currently when we want to write a query that filters out\n", "non-customer tenants (e.g., pentest), we do so by joining on\n", "the customers_request_metadata view. We have tenant_id in every\n", "table, though, so as long as we have the list of tenants to filter\n", "out we can do this without the join (depending on the event;\n", "sometimes you also need to filter by user-agent).\n", "\n", "\n", "Commit message example 14:\n", "Use eldenv4c everywhere in prod as default (#14006)\n", "\n", "This PR makes the new eldenv4-0c model default in prod. It has already\n", "been default in vanguard for 24 hours without issue.\n", "\n", "The changes are:\n", "- Uses smart ethanol, an ethanol retriever with smart chunking\n", "- Trained on more tests\n", "\n", "\n", "Commit message example 15:\n", "Add instruction/smart-paste to dev-deploy (#13881)\n", "\n", "\n", "\n", "\n", "Commit message example 16:\n", "save requests to disk (#14023)\n", "\n", "Co-authored-by: <PERSON><PERSON> <<EMAIL>>\n", "\n", "\n", "Commit message example 17:\n", "Deploy EldenV7-0 (RLHF models) (#13921)\n", "\n", "This is model is an RLHF-ed model on top of Elden V3, and got 1% token\n", "accuracy improvements over multiple hindsight datasets.\n", "- Has tested in dev-deploy.\n", "- Has synced the checkpoint\n", "\n", "\n", "Commit message example 18:\n", "Assign tenant ids to very old tenants in BigQuery (#14011)\n", "\n", "This commit assigns arbitrary tenant ids to very old tenants (who\n", "were deleted before the namespace sharding work) in BigQuery.\n", "This will allow us to simplify some queries.\n", "\n", "\n", "Commit message example 19:\n", "wip wordpress scraping; not actually implemented yet (#13918)\n", "\n", "\n", "\n", "\n", "Commit message example 20:\n", "[intellij] upgrade Gradle plugin to 2.0 (#14000)\n", "\n", "This will help us in the future to support 2024.2+ versions. It's\n", "mandatory to use a new version going forward.\n", "\n", "\n", "Commit message example 21:\n", "add eval experimental stuff for docsets (#14012)\n", "\n", "\n", "\n", "\n", "Commit message example 22:\n", "Tonic - set keepalive parameters (#13982)\n", "\n", "Most useful for long-lived streams where client is listening for\n", "notifications.\n", "\n", "Testing done: verified with wireshark and ping client that HTTP/2 keepalives are being sent.\n", "\n", "\n", "Commit message example 23:\n", "Graceful shutdown for Rust services (#14015)\n", "\n", "- Plumb sigterm into Tonic\n", " - wait for all servers to shut down in response to sigterm, not\n", "   just the first one.\n", "\n", "Testing done: dev deploy of all affected services and rollout restart of api proxy\n", "\n", "\n", "Commit message example 24:\n", "protobuf with typescript generation (#14009)\n", "\n", "### TL;DR\n", "\n", "Updated BUILD file and TypeScript proto library generation in Bazel\n", "\n", "### What changed?\n", "\n", "- In `services/customer/frontend/BUILD`, changed `deps` to `data` for\n", "the `js_library` rule.\n", "- In `tools/bzl/typescript.bzl`, refactored the `ts_proto_library`\n", "function:\n", "  - Removed `files_to_copy` and `copy_files` parameters\n", "  - Added `visibility` parameter\n", "  - Created separate targets for JavaScript and TypeScript files\n", "- Introduced a filegroup to combine both JavaScript and TypeScript\n", "outputs\n", "\n", "### How to test?\n", "\n", "1. Rebuild the affected targets in the `services/customer/frontend`\n", "directory\n", "2. Verify that the TypeScript proto libraries are generated correctly\n", "3. Ensure that the frontend builds and runs without errors\n", "4. Check that the visibility of the generated proto libraries is set\n", "correctly\n", "\n", "### Why make this change?\n", "\n", "This change improves the Bazel build process for TypeScript proto\n", "libraries by:\n", "1. Correctly specifying dependencies as `data` instead of `deps` in the\n", "frontend BUILD file\n", "2. Simplifying the `ts_proto_library` function and improving its\n", "flexibility\n", "3. Separating JavaScript and TypeScript outputs for better control over\n", "generated files\n", "4. Allowing for customizable visibility of the generated proto libraries\n", "\n", "These improvements should lead to more efficient builds and better\n", "management of TypeScript proto libraries in the project.\n", "\n", "\n", "Commit message example 25:\n", "Enable flywheel for vanguard (#14018)\n", "\n", "\n", "\n", "\n", "Commit message example 26:\n", "support tagging files in inline chat (#13991)\n", "\n", "I put together this draft in order to see how hard it is to include\n", "fuzzy search for mentions in the inline chat. It works, but it's a mess.\n", "\n", "\n", "Commit message example 27:\n", "[Context] Move context to the flywheel flag (#13989)\n", "\n", "Move new context UI to be gated behind the flywheel flag\n", "\n", "\n", "Commit message example 28:\n", "[Chat] Dynamic codeblock actions update with confirmation (#13705)\n", "\n", "Fixes\n", "https://linear.app/augmentcode/issue/AU-4756/remove-create-button-when-theres-no-file-header\n", "Note that the linear ticket advocates for a slightly different behavior\n", "-- we always have a primary action, but if the primary action doesn't\n", "apply, the user can still select any of the other actions from the menu\n", "\n", "This PR does three things:\n", "- Makes the logic for when smart paste is active much clearer and\n", "explicit\n", "- Makes it so if metadata is returned, we ONLY show one of apply or\n", "create\n", "- Adds a confirmation modal if there is no metadata to apply to\n", "\n", "\n", "\n", "https://github.com/user-attachments/assets/2e4e525d-dc99-4b69-9465-ba6be055fbfa\n", "\n", "\n", "Commit message example 29:\n", "Set the bigtable client limit to 256 MB (#13959)\n", "\n", "The go client says in\n", "\n", "https://github.com/googleapis/google-cloud-go/commit/c750310a90c16b6f633ad076ffeb299664f63774\n", "\"Use correct value for message size\"\n", "and \"Set the max size to correspond to server-side limits\"\n", "setting it to 1<<28.\n", "\n", "The official Java client says\n", "(https://github.com/googleapis/java-bigtable/blob/main/google-cloud-bigtable/src/main/java/com/google/cloud/bigtable/data/v2/stub/EnhancedBigtableStubSettings.java#L99)\n", "```\n", "// The largest message that can be received is a 256 MB ReadRowsResponse.\n", "  private static final int MAX_MESSAGE_SIZE = 256 * 1024 * 1024;\n", "```\n", "\n", "Python has at\n", "https://github.com/googleapis/python-bigtable/blob/main/google/cloud/bigtable/client.py\n", "```\n", "_GRPC_CHANNEL_OPTIONS = (\n", "    (\"grpc.max_send_message_length\", -1),\n", "    (\"grpc.max_receive_message_length\", -1),\n", "    (\"grpc.keepalive_time_ms\", 30000),\n", "    (\"grpc.keepalive_timeout_ms\", 10000),\n", ")\n", "\n", "```\n", "\n", "So, let's do the same.\n", "\n", "This sets the limits for the bigtable client in bigtable proxy to 256 MB\n", "and each client of bigtable proxy to 256 MB.\n", "\n", "\n", "Commit message example 30:\n", "Adding support for keybinding icons in markdown with custom font (#13988)\n", "\n", "I'm adding a custom font I made with <PERSON><PERSON> out of our existing\n", "icons. These get added to our package.json and are available throughout\n", "the app anywhere these types of icons are allowed. I use them in place\n", "of the key bindings in the Next Edit hover now.\n", "\n", "Note that we could not use the existing SVGs in the hover because, while\n", "it can support images in markdown, whenever we put an SVG into markdown\n", "it triggers some kind of async rendering and causes vscode to add the\n", "code-hover-contents class which removes the display:inline-block from\n", "all of the elements of the hover -- breaking our layout in other ways.\n", "\n", "\n", "Commit message example 31:\n", "Update feedback slackbot chat channel (#13968)\n", "\n", "We're holding off on completions and next edit because the augment\n", "history panel can generate multiple feedback events if the rating or\n", "send feedback buttons are pressed multiple times.\n", "\n", "\n", "Above are all the commit message examples.\n", "\n", "\n", "\n", "\n", "\n", "Do not include boilerplate text like \"Here is the commit message:\" in your response..\n"]}], "source": ["start_time = time.time()\n", "prompt, metadata = mgh.get_prompt(\n", "    commit[\"current_hash\"],\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=False,\n", "    summary_v2_threshold=900,\n", "    trailing_instructions=TRAILING_INSTRUCTIONS,\n", "    message_soft_budget=1024 * 3,\n", "    example_type=\"recent\",\n", "    similar_search_range=1000,\n", "    ratio_threshold=0.7,\n", "    git_structure_depth_limit=2,\n", "    git_structure_count_type=\"file\",\n", "    descriptor_path_source=\"commit\",\n", "    relevant_message_limit=3,\n", "    read_from_disk=True,\n", "    diff_line_limit=5000,\n", "    token_budget=1024 * 12,\n", ")\n", "print(prompt)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Below are the per-file diffs of the commit:\n", "\n", "diff --git a/clients/vscode/src/main-panel/action-cards/actions-model.ts b/clients/vscode/src/main-panel/action-cards/actions-model.ts\n", "index 16f324899..897ad939a 100644\n", "--- a/clients/vscode/src/main-panel/action-cards/actions-model.ts\n", "+++ b/clients/vscode/src/main-panel/action-cards/actions-model.ts\n", "@@ -158,7 +158,6 @@ const initialDerivedStates = new Map<DerivedStateName, DerivedState>([\n", "                 },\n", "                 // { name: SystemStateName.hasMovedExtensionAside, status: SystemStatus.complete },\n", "                 { name: SystemStateName.workspacePopulated, status: SystemStatus.complete },\n", "-                { name: SystemStateName.summaryReady, status: SystemStatus.complete },\n", "             ],\n", "         },\n", "     ],\n", "\n", "\n", "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n", "\n", "\n", "Commit message example 0:\n", "infra/grafana: Stand up in gcp-core0\n", "\n", " - Use the kubecfg helm wrapper (this was the last full helm chart)\n", " - Replace `grafana.r.augmentcode.com` which was a PoC running in old CW\n", "   - But the deployment is simpler because we can now use an ingress\n", " - As before, use oauth2 with <EMAIL> members\n", "   automatically configured as site-wide admins.\n", " - Only the CoreWeave Prometheus and Devex Metrics (the NEW one in\n", "   gcp-core0) are currently added as datasources.\n", "   - <PERSON><PERSON><PERSON>, determined, and loki are excluded for now\n", " - Start with the json dashboards I'd expected out of the origina CW\n", "   grafana.\n", "\n", "I need to remember how I did the original exports. Rather than doing a\n", "DB migration, I'd like to stick to this git-first approach and check in\n", "exports of dashboards, alerts, and notifications from the original CW\n", "grafana.\n", "\n", "\n", "\n", "Commit message example 1:\n", "Don't redirect augmentcode.com to staging (#13990)\n", "\n", "For the play-test tomorrow.\n", "\n", "\n", "Commit message example 2:\n", "flags for dry test (#14047)\n", "\n", "Co-authored-by: <PERSON><PERSON> <<EMAIL>>\n", "\n", "\n", "Commit message example 3:\n", "[intellij] fixed NPE (#14043)\n", "\n", "\n", "\n", "\n", "Commit message example 4:\n", "Ignore alerts in cnrm-system namespace (#14037)\n", "\n", "This looks to be related to config-connector, so should be managed by\n", "GKE\n", "\n", "\n", "Commit message example 5:\n", "Round-robin load-balancing with sticky sessions for next-edit inference requests (#14041)\n", "\n", "Toggle the feature of the inference host client to have each next-edit\n", "host route all inference requests for a given request_session_id to a\n", "single inference host for a period of time.\n", "\n", "With that in place, we can enable round-robin selection of gRPC\n", "sub-channels without losing the benefits of caching on the\n", "inference host.\n", "\n", "Tested the config in dev deploy.\n", "\n", "\n", "Commit message example 6:\n", "fix (#14035)\n", "\n", "\n", "\n", "\n", "Commit message example 7:\n", "AU-4847: Make summary show with syncing status done (#13969)\n", "\n", "This updates the onboarding model to show the summary message once the\n", "syncing is \"done\" the same as the progress bar and status bar.\n", "\n", "As part of this PR I've added tests, noticed a number of misconceptions\n", "with the previous implementation that I've hopefully corrected.\n", "\n", "\n", "https://github.com/user-attachments/assets/68f403c7-4d7a-4247-a2db-9e9bd6522073\n", "\n", "\n", "Commit message example 8:\n", "AU-4874 Adding e2e test for share service (#14013)\n", "\n", "Adding an end-to-end test for the Share service.\n", "\n", "Test:\n", "https://test-viewer.us-central1.dev.augmentcode.com/run/01929ba9-7f32-758e-1612-5ddc49fcc069\n", "\n", "\n", "Commit message example 9:\n", "Use ModelArgs.load_from_dict to load. (#14019)\n", "\n", "We used to call `ModelArgs.schema().load(params)` directly, but this is not backward compatible with old `params` and we need to remember to call `fix_model_arg_params`, which I didn't know existed. This PR slightly improves the situation by defining a new static method `ModelArgs.load_from_dict` and encourage its uses.\n", "\n", "\n", "Commit message example 10:\n", "Scale to fewer indexers (#14032)\n", "\n", "Now that we have better indexer parallelism, we shouldn't\n", "need to go as high.\n", "\n", "Testing done: None\n", "\n", "\n", "Commit message example 11:\n", "remove pre_attention_kernel_fusion flag from inference server (#13840)\n", "\n", "Just a minor cleanup.\n", "\n", "\n", "Commit message example 12:\n", "Next edit host cleanup: encapsulate request parameters (#14001)\n", "\n", "\n", "\n", "\n", "Commit message example 13:\n", "Add view for noncustomer tenants (#14022)\n", "\n", "Currently when we want to write a query that filters out\n", "non-customer tenants (e.g., pentest), we do so by joining on\n", "the customers_request_metadata view. We have tenant_id in every\n", "table, though, so as long as we have the list of tenants to filter\n", "out we can do this without the join (depending on the event;\n", "sometimes you also need to filter by user-agent).\n", "\n", "\n", "Commit message example 14:\n", "Use eldenv4c everywhere in prod as default (#14006)\n", "\n", "This PR makes the new eldenv4-0c model default in prod. It has already\n", "been default in vanguard for 24 hours without issue.\n", "\n", "The changes are:\n", "- Uses smart ethanol, an ethanol retriever with smart chunking\n", "- Trained on more tests\n", "\n", "\n", "Commit message example 15:\n", "Add instruction/smart-paste to dev-deploy (#13881)\n", "\n", "\n", "\n", "\n", "Commit message example 16:\n", "save requests to disk (#14023)\n", "\n", "Co-authored-by: <PERSON><PERSON> <<EMAIL>>\n", "\n", "\n", "Commit message example 17:\n", "Deploy EldenV7-0 (RLHF models) (#13921)\n", "\n", "This is model is an RLHF-ed model on top of Elden V3, and got 1% token\n", "accuracy improvements over multiple hindsight datasets.\n", "- Has tested in dev-deploy.\n", "- Has synced the checkpoint\n", "\n", "\n", "Commit message example 18:\n", "Assign tenant ids to very old tenants in BigQuery (#14011)\n", "\n", "This commit assigns arbitrary tenant ids to very old tenants (who\n", "were deleted before the namespace sharding work) in BigQuery.\n", "This will allow us to simplify some queries.\n", "\n", "\n", "Commit message example 19:\n", "wip wordpress scraping; not actually implemented yet (#13918)\n", "\n", "\n", "\n", "\n", "Commit message example 20:\n", "[intellij] upgrade Gradle plugin to 2.0 (#14000)\n", "\n", "This will help us in the future to support 2024.2+ versions. It's\n", "mandatory to use a new version going forward.\n", "\n", "\n", "Commit message example 21:\n", "add eval experimental stuff for docsets (#14012)\n", "\n", "\n", "\n", "\n", "Commit message example 22:\n", "Tonic - set keepalive parameters (#13982)\n", "\n", "Most useful for long-lived streams where client is listening for\n", "notifications.\n", "\n", "Testing done: verified with wireshark and ping client that HTTP/2 keepalives are being sent.\n", "\n", "\n", "Commit message example 23:\n", "Graceful shutdown for Rust services (#14015)\n", "\n", "- Plumb sigterm into Tonic\n", " - wait for all servers to shut down in response to sigterm, not\n", "   just the first one.\n", "\n", "Testing done: dev deploy of all affected services and rollout restart of api proxy\n", "\n", "\n", "Commit message example 24:\n", "protobuf with typescript generation (#14009)\n", "\n", "### TL;DR\n", "\n", "Updated BUILD file and TypeScript proto library generation in Bazel\n", "\n", "### What changed?\n", "\n", "- In `services/customer/frontend/BUILD`, changed `deps` to `data` for\n", "the `js_library` rule.\n", "- In `tools/bzl/typescript.bzl`, refactored the `ts_proto_library`\n", "function:\n", "  - Removed `files_to_copy` and `copy_files` parameters\n", "  - Added `visibility` parameter\n", "  - Created separate targets for JavaScript and TypeScript files\n", "- Introduced a filegroup to combine both JavaScript and TypeScript\n", "outputs\n", "\n", "### How to test?\n", "\n", "1. Rebuild the affected targets in the `services/customer/frontend`\n", "directory\n", "2. Verify that the TypeScript proto libraries are generated correctly\n", "3. Ensure that the frontend builds and runs without errors\n", "4. Check that the visibility of the generated proto libraries is set\n", "correctly\n", "\n", "### Why make this change?\n", "\n", "This change improves the Bazel build process for TypeScript proto\n", "libraries by:\n", "1. Correctly specifying dependencies as `data` instead of `deps` in the\n", "frontend BUILD file\n", "2. Simplifying the `ts_proto_library` function and improving its\n", "flexibility\n", "3. Separating JavaScript and TypeScript outputs for better control over\n", "generated files\n", "4. Allowing for customizable visibility of the generated proto libraries\n", "\n", "These improvements should lead to more efficient builds and better\n", "management of TypeScript proto libraries in the project.\n", "\n", "\n", "Commit message example 25:\n", "Enable flywheel for vanguard (#14018)\n", "\n", "\n", "\n", "\n", "Commit message example 26:\n", "support tagging files in inline chat (#13991)\n", "\n", "I put together this draft in order to see how hard it is to include\n", "fuzzy search for mentions in the inline chat. It works, but it's a mess.\n", "\n", "\n", "Commit message example 27:\n", "[Context] Move context to the flywheel flag (#13989)\n", "\n", "Move new context UI to be gated behind the flywheel flag\n", "\n", "\n", "Commit message example 28:\n", "[Chat] Dynamic codeblock actions update with confirmation (#13705)\n", "\n", "Fixes\n", "https://linear.app/augmentcode/issue/AU-4756/remove-create-button-when-theres-no-file-header\n", "Note that the linear ticket advocates for a slightly different behavior\n", "-- we always have a primary action, but if the primary action doesn't\n", "apply, the user can still select any of the other actions from the menu\n", "\n", "This PR does three things:\n", "- Makes the logic for when smart paste is active much clearer and\n", "explicit\n", "- Makes it so if metadata is returned, we ONLY show one of apply or\n", "create\n", "- Adds a confirmation modal if there is no metadata to apply to\n", "\n", "\n", "\n", "https://github.com/user-attachments/assets/2e4e525d-dc99-4b69-9465-ba6be055fbfa\n", "\n", "\n", "Commit message example 29:\n", "Set the bigtable client limit to 256 MB (#13959)\n", "\n", "The go client says in\n", "\n", "https://github.com/googleapis/google-cloud-go/commit/c750310a90c16b6f633ad076ffeb299664f63774\n", "\"Use correct value for message size\"\n", "and \"Set the max size to correspond to server-side limits\"\n", "setting it to 1<<28.\n", "\n", "The official Java client says\n", "(https://github.com/googleapis/java-bigtable/blob/main/google-cloud-bigtable/src/main/java/com/google/cloud/bigtable/data/v2/stub/EnhancedBigtableStubSettings.java#L99)\n", "```\n", "// The largest message that can be received is a 256 MB ReadRowsResponse.\n", "  private static final int MAX_MESSAGE_SIZE = 256 * 1024 * 1024;\n", "```\n", "\n", "Python has at\n", "https://github.com/googleapis/python-bigtable/blob/main/google/cloud/bigtable/client.py\n", "```\n", "_GRPC_CHANNEL_OPTIONS = (\n", "    (\"grpc.max_send_message_length\", -1),\n", "    (\"grpc.max_receive_message_length\", -1),\n", "    (\"grpc.keepalive_time_ms\", 30000),\n", "    (\"grpc.keepalive_timeout_ms\", 10000),\n", ")\n", "\n", "```\n", "\n", "So, let's do the same.\n", "\n", "This sets the limits for the bigtable client in bigtable proxy to 256 MB\n", "and each client of bigtable proxy to 256 MB.\n", "\n", "\n", "Commit message example 30:\n", "Adding support for keybinding icons in markdown with custom font (#13988)\n", "\n", "I'm adding a custom font I made with <PERSON><PERSON> out of our existing\n", "icons. These get added to our package.json and are available throughout\n", "the app anywhere these types of icons are allowed. I use them in place\n", "of the key bindings in the Next Edit hover now.\n", "\n", "Note that we could not use the existing SVGs in the hover because, while\n", "it can support images in markdown, whenever we put an SVG into markdown\n", "it triggers some kind of async rendering and causes vscode to add the\n", "code-hover-contents class which removes the display:inline-block from\n", "all of the elements of the hover -- breaking our layout in other ways.\n", "\n", "\n", "Commit message example 31:\n", "Update feedback slackbot chat channel (#13968)\n", "\n", "We're holding off on completions and next edit because the augment\n", "history panel can generate multiple feedback events if the rating or\n", "send feedback buttons are pressed multiple times.\n", "\n", "\n", "Above are all the commit message examples.\n", "\n", "\n", "Do not include boilerplate text like \"Here is the commit message:\" in your response..\n"]}], "source": ["start_time = time.time()\n", "prompt_output, timings = mgh.get_prompt_using_formatter(\n", "    commit[\"current_hash\"],\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=False,\n", "    summary_v2_threshold=900,\n", "    trailing_instructions=TRAILING_INSTRUCTIONS,\n", "    message_soft_budget=1024 * 3,\n", "    example_type=\"similar\",\n", "    similar_search_range=1000,\n", "    ratio_threshold=0.7,\n", "    git_structure_depth_limit=2,\n", "    git_structure_count_type=\"file\",\n", "    descriptor_path_source=\"commit\",\n", "    relevant_message_limit=3,\n", "    read_from_disk=True,\n", "    diff_line_limit=5000,\n", "    token_budget=1024 * 12,\n", ")\n", "print(prompt_output.message)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Get formatter inputs: 0.02s\n", "Get diff: 0.02s\n", "Diff read: 0.02s\n", "Diff get_diff_prompt: 0.02s\n"]}], "source": ["for key, value in timings.items():\n", "    if value < 0.01:\n", "        continue\n", "    print(f\"{key}: {value:.2f}s\")"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"ename": "AssertionError", "evalue": "Strings are not equal: \n[[Expected]]\nBelow·are·the·per-file·diffs·of·the·commit:↩\n↩\n↩\n↩\ndiff·--git·a/clients/vscode/src/main-panel/action-cards/actions-model.ts·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\nindex·16f324899..897ad939a·100644↩\n---·a/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n+++·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n@@·-158,7·+158,6·@@·const·initialDerivedStates·=·new·Map<DerivedStateName,·DerivedState>([↩\n·················},↩\n·················//·{·name:·SystemStateName.hasMovedExtensionAside,·status:·SystemStatus.complete·},↩\n·················{·name:·SystemStateName.workspacePopulated,·status:·SystemStatus.complete·},↩\n-················{·name:·SystemStateName.summaryReady,·status:·SystemStatus.complete·},↩\n·············],↩\n·········},↩\n·····],↩\n↩\n↩\nBelow·are·the·commit·message·examples·from·the·same·repository,·to·illustrate·the·commit·message·style·and·conventions·to·follow:↩\n↩\n↩\nCommit·message·example·0:↩\ninfra/grafana:·Stand·up·in·gcp-core0↩\n↩\n·-·Use·the·kubecfg·helm·wrapper·(this·was·the·last·full·helm·chart)↩\n·-·Replace·`grafana.r.augmentcode.com`·which·was·a·PoC·running·in·old·CW↩\n···-·But·the·deployment·is·simpler·because·we·can·now·use·an·ingress↩\n·-·As·before,·use·oauth2·with·<EMAIL>·members↩\n···automatically·configured·as·site-wide·admins.↩\n·-·Only·the·CoreWeave·Prometheus·and·Devex·Metrics·(the·NEW·one·in↩\n···gcp-core0)·are·currently·added·as·datasources.↩\n···-·Metastore,·determined,·and·loki·are·excluded·for·now↩\n·-·Start·with·the·json·dashboards·I'd·expected·out·of·the·origina·CW↩\n···grafana.↩\n↩\nI·need·to·remember·how·I·did·the·original·exports.·Rather·than·doing·a↩\nDB·migration,·I'd·like·to·stick·to·this·git-first·approach·and·check·in↩\nexports·of·dashboards,·alerts,·and·notifications·from·the·original·CW↩\ngrafana.↩\n↩\n↩\n↩\nCommit·message·example·1:↩\nDon't·redirect·augmentcode.com·to·staging·(#13990)↩\n↩\nFor·the·play-test·tomorrow.↩\n↩\n↩\nCommit·message·example·2:↩\nflags·for·dry·test·(#14047)↩\n↩\nCo-authored-by:·Devang·Jhabakh·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·3:↩\n[intellij]·fixed·NPE·(#14043)↩\n↩\n↩\n↩\n↩\nCommit·message·example·4:↩\nIgnore·alerts·in·cnrm-system·namespace·(#14037)↩\n↩\nThis·looks·to·be·related·to·config-connector,·so·should·be·managed·by\\r↩\nGKE↩\n↩\n↩\nCommit·message·example·5:↩\nRound-robin·load-balancing·with·sticky·sessions·for·next-edit·inference·requests·(#14041)↩\n↩\nToggle·the·feature·of·the·inference·host·client·to·have·each·next-edit↩\nhost·route·all·inference·requests·for·a·given·request_session_id·to·a↩\nsingle·inference·host·for·a·period·of·time.↩\n↩\nWith·that·in·place,·we·can·enable·round-robin·selection·of·gRPC↩\nsub-channels·without·losing·the·benefits·of·caching·on·the↩\ninference·host.↩\n↩\nTested·the·config·in·dev·deploy.↩\n↩\n↩\nCommit·message·example·6:↩\nfix·(#14035)↩\n↩\n↩\n↩\n↩\nCommit·message·example·7:↩\nAU-4847:·Make·summary·show·with·syncing·status·done·(#13969)↩\n↩\nThis·updates·the·onboarding·model·to·show·the·summary·message·once·the\\r↩\nsyncing·is·\"done\"·the·same·as·the·progress·bar·and·status·bar.\\r↩\n\\r↩\nAs·part·of·this·PR·I've·added·tests,·noticed·a·number·of·misconceptions\\r↩\nwith·the·previous·implementation·that·I've·hopefully·corrected.\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/68f403c7-4d7a-4247-a2db-9e9bd6522073↩\n↩\n↩\nCommit·message·example·8:↩\nAU-4874·Adding·e2e·test·for·share·service·(#14013)↩\n↩\nAdding·an·end-to-end·test·for·the·Share·service.↩\n↩\nTest:↩\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929ba9-7f32-758e-1612-5ddc49fcc069↩\n↩\n↩\nCommit·message·example·9:↩\nUse·ModelArgs.load_from_dict·to·load.·(#14019)↩\n↩\nWe·used·to·call·`ModelArgs.schema().load(params)`·directly,·but·this·is·not·backward·compatible·with·old·`params`·and·we·need·to·remember·to·call·`fix_model_arg_params`,·which·I·didn't·know·existed.·This·PR·slightly·improves·the·situation·by·defining·a·new·static·method·`ModelArgs.load_from_dict`·and·encourage·its·uses.↩\n↩\n↩\nCommit·message·example·10:↩\nScale·to·fewer·indexers·(#14032)↩\n↩\nNow·that·we·have·better·indexer·parallelism,·we·shouldn't↩\nneed·to·go·as·high.↩\n↩\nTesting·done:·None↩\n↩\n↩\nCommit·message·example·11:↩\nremove·pre_attention_kernel_fusion·flag·from·inference·server·(#13840)↩\n↩\nJust·a·minor·cleanup.↩\n↩\n↩\nCommit·message·example·12:↩\nNext·edit·host·cleanup:·encapsulate·request·parameters·(#14001)↩\n↩\n↩\n↩\n↩\nCommit·message·example·13:↩\nAdd·view·for·noncustomer·tenants·(#14022)↩\n↩\nCurrently·when·we·want·to·write·a·query·that·filters·out↩\nnon-customer·tenants·(e.g.,·pentest),·we·do·so·by·joining·on↩\nthe·customers_request_metadata·view.·We·have·tenant_id·in·every↩\ntable,·though,·so·as·long·as·we·have·the·list·of·tenants·to·filter↩\nout·we·can·do·this·without·the·join·(depending·on·the·event;↩\nsometimes·you·also·need·to·filter·by·user-agent).↩\n↩\n↩\nCommit·message·example·14:↩\nUse·eldenv4c·everywhere·in·prod·as·default·(#14006)↩\n↩\nThis·PR·makes·the·new·eldenv4-0c·model·default·in·prod.·It·has·already\\r↩\nbeen·default·in·vanguard·for·24·hours·without·issue.\\r↩\n\\r↩\nThe·changes·are:\\r↩\n-·Uses·smart·ethanol,·an·ethanol·retriever·with·smart·chunking\\r↩\n-·Trained·on·more·tests↩\n↩\n↩\nCommit·message·example·15:↩\nAdd·instruction/smart-paste·to·dev-deploy·(#13881)↩\n↩\n↩\n↩\n↩\nCommit·message·example·16:↩\nsave·requests·to·disk·(#14023)↩\n↩\nCo-authored-by:·Lior·Neumann·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·17:↩\nDeploy·EldenV7-0·(RLHF·models)·(#13921)↩\n↩\nThis·is·model·is·an·RLHF-ed·model·on·top·of·Elden·V3,·and·got·1%·token\\r↩\naccuracy·improvements·over·multiple·hindsight·datasets.\\r↩\n-·Has·tested·in·dev-deploy.\\r↩\n-·Has·synced·the·checkpoint↩\n↩\n↩\nCommit·message·example·18:↩\nAssign·tenant·ids·to·very·old·tenants·in·BigQuery·(#14011)↩\n↩\nThis·commit·assigns·arbitrary·tenant·ids·to·very·old·tenants·(who↩\nwere·deleted·before·the·namespace·sharding·work)·in·BigQuery.↩\nThis·will·allow·us·to·simplify·some·queries.↩\n↩\n↩\nCommit·message·example·19:↩\nwip·wordpress·scraping;·not·actually·implemented·yet·(#13918)↩\n↩\n↩\n↩\n↩\nCommit·message·example·20:↩\n[intellij]·upgrade·Gradle·plugin·to·2.0·(#14000)↩\n↩\nThis·will·help·us·in·the·future·to·support·2024.2+·versions.·It's\\r↩\nmandatory·to·use·a·new·version·going·forward.↩\n↩\n↩\nCommit·message·example·21:↩\nadd·eval·experimental·stuff·for·docsets·(#14012)↩\n↩\n↩\n↩\n↩\nCommit·message·example·22:↩\nTonic·-·set·keepalive·parameters·(#13982)↩\n↩\nMost·useful·for·long-lived·streams·where·client·is·listening·for↩\nnotifications.↩\n↩\nTesting·done:·verified·with·wireshark·and·ping·client·that·HTTP/2·keepalives·are·being·sent.↩\n↩\n↩\nCommit·message·example·23:↩\nGraceful·shutdown·for·Rust·services·(#14015)↩\n↩\n-·Plumb·sigterm·into·Tonic↩\n·-·wait·for·all·servers·to·shut·down·in·response·to·sigterm,·not↩\n···just·the·first·one.↩\n↩\nTesting·done:·dev·deploy·of·all·affected·services·and·rollout·restart·of·api·proxy↩\n↩\n↩\nCommit·message·example·24:↩\nprotobuf·with·typescript·generation·(#14009)↩\n↩\n###·TL;DR↩\n↩\nUpdated·BUILD·file·and·TypeScript·proto·library·generation·in·Bazel↩\n↩\n###·What·changed?↩\n↩\n-·In·`services/customer/frontend/BUILD`,·changed·`deps`·to·`data`·for↩\nthe·`js_library`·rule.↩\n-·In·`tools/bzl/typescript.bzl`,·refactored·the·`ts_proto_library`↩\nfunction:↩\n··-·Removed·`files_to_copy`·and·`copy_files`·parameters↩\n··-·Added·`visibility`·parameter↩\n··-·Created·separate·targets·for·JavaScript·and·TypeScript·files↩\n-·Introduced·a·filegroup·to·combine·both·JavaScript·and·TypeScript↩\noutputs↩\n↩\n###·How·to·test?↩\n↩\n1.·Rebuild·the·affected·targets·in·the·`services/customer/frontend`↩\ndirectory↩\n2.·Verify·that·the·TypeScript·proto·libraries·are·generated·correctly↩\n3.·Ensure·that·the·frontend·builds·and·runs·without·errors↩\n4.·Check·that·the·visibility·of·the·generated·proto·libraries·is·set↩\ncorrectly↩\n↩\n###·Why·make·this·change?↩\n↩\nThis·change·improves·the·Bazel·build·process·for·TypeScript·proto↩\nlibraries·by:↩\n1.·Correctly·specifying·dependencies·as·`data`·instead·of·`deps`·in·the↩\nfrontend·BUILD·file↩\n2.·Simplifying·the·`ts_proto_library`·function·and·improving·its↩\nflexibility↩\n3.·Separating·JavaScript·and·TypeScript·outputs·for·better·control·over↩\ngenerated·files↩\n4.·Allowing·for·customizable·visibility·of·the·generated·proto·libraries↩\n↩\nThese·improvements·should·lead·to·more·efficient·builds·and·better↩\nmanagement·of·TypeScript·proto·libraries·in·the·project.↩\n↩\n↩\nCommit·message·example·25:↩\nEnable·flywheel·for·vanguard·(#14018)↩\n↩\n↩\n↩\n↩\nCommit·message·example·26:↩\nsupport·tagging·files·in·inline·chat·(#13991)↩\n↩\nI·put·together·this·draft·in·order·to·see·how·hard·it·is·to·include\\r↩\nfuzzy·search·for·mentions·in·the·inline·chat.·It·works,·but·it's·a·mess.↩\n↩\n↩\nCommit·message·example·27:↩\n[Context]·Move·context·to·the·flywheel·flag·(#13989)↩\n↩\nMove·new·context·UI·to·be·gated·behind·the·flywheel·flag↩\n↩\n↩\nCommit·message·example·28:↩\n[Chat]·Dynamic·codeblock·actions·update·with·confirmation·(#13705)↩\n↩\nFixes\\r↩\nhttps://linear.app/augmentcode/issue/AU-4756/remove-create-button-when-theres-no-file-header\\r↩\nNote·that·the·linear·ticket·advocates·for·a·slightly·different·behavior\\r↩\n--·we·always·have·a·primary·action,·but·if·the·primary·action·doesn't\\r↩\napply,·the·user·can·still·select·any·of·the·other·actions·from·the·menu\\r↩\n\\r↩\nThis·PR·does·three·things:\\r↩\n-·Makes·the·logic·for·when·smart·paste·is·active·much·clearer·and\\r↩\nexplicit\\r↩\n-·Makes·it·so·if·metadata·is·returned,·we·ONLY·show·one·of·apply·or\\r↩\ncreate\\r↩\n-·Adds·a·confirmation·modal·if·there·is·no·metadata·to·apply·to\\r↩\n\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/2e4e525d-dc99-4b69-9465-ba6be055fbfa↩\n↩\n↩\nCommit·message·example·29:↩\nSet·the·bigtable·client·limit·to·256·MB·(#13959)↩\n↩\nThe·go·client·says·in\\r↩\n\\r↩\nhttps://github.com/googleapis/google-cloud-go/commit/c750310a90c16b6f633ad076ffeb299664f63774\\r↩\n\"Use·correct·value·for·message·size\"\\r↩\nand·\"Set·the·max·size·to·correspond·to·server-side·limits\"\\r↩\nsetting·it·to·1<<28.\\r↩\n\\r↩\nThe·official·Java·client·says\\r↩\n(https://github.com/googleapis/java-bigtable/blob/main/google-cloud-bigtable/src/main/java/com/google/cloud/bigtable/data/v2/stub/EnhancedBigtableStubSettings.java#L99)\\r↩\n```\\r↩\n//·The·largest·message·that·can·be·received·is·a·256·MB·ReadRowsResponse.\\r↩\n··private·static·final·int·MAX_MESSAGE_SIZE·=·256·*·1024·*·1024;\\r↩\n```\\r↩\n\\r↩\nPython·has·at\\r↩\nhttps://github.com/googleapis/python-bigtable/blob/main/google/cloud/bigtable/client.py\\r↩\n```\\r↩\n_GRPC_CHANNEL_OPTIONS·=·(\\r↩\n····(\"grpc.max_send_message_length\",·-1),\\r↩\n····(\"grpc.max_receive_message_length\",·-1),\\r↩\n····(\"grpc.keepalive_time_ms\",·30000),\\r↩\n····(\"grpc.keepalive_timeout_ms\",·10000),\\r↩\n)\\r↩\n\\r↩\n```\\r↩\n\\r↩\nSo,·let's·do·the·same.\\r↩\n\\r↩\nThis·sets·the·limits·for·the·bigtable·client·in·bigtable·proxy·to·256·MB\\r↩\nand·each·client·of·bigtable·proxy·to·256·MB.↩\n↩\n↩\nCommit·message·example·30:↩\nAdding·support·for·keybinding·icons·in·markdown·with·custom·font·(#13988)↩\n↩\nI'm·adding·a·custom·font·I·made·with·Fantasticon·out·of·our·existing\\r↩\nicons.·These·get·added·to·our·package.json·and·are·available·throughout\\r↩\nthe·app·anywhere·these·types·of·icons·are·allowed.·I·use·them·in·place\\r↩\nof·the·key·bindings·in·the·Next·Edit·hover·now.\\r↩\n\\r↩\nNote·that·we·could·not·use·the·existing·SVGs·in·the·hover·because,·while\\r↩\nit·can·support·images·in·markdown,·whenever·we·put·an·SVG·into·markdown\\r↩\nit·triggers·some·kind·of·async·rendering·and·causes·vscode·to·add·the\\r↩\ncode-hover-contents·class·which·removes·the·display:inline-block·from\\r↩\nall·of·the·elements·of·the·hover·--·breaking·our·layout·in·other·ways.↩\n↩\n↩\nCommit·message·example·31:↩\nUpdate·feedback·slackbot·chat·channel·(#13968)↩\n↩\nWe're·holding·off·on·completions·and·next·edit·because·the·augment↩\nhistory·panel·can·generate·multiple·feedback·events·if·the·rating·or↩\nsend·feedback·buttons·are·pressed·multiple·times.↩\n↩\n↩\nAbove·are·all·the·commit·message·examples.↩\n↩\n↩\n↩\n↩\n↩\nDo·not·include·boilerplate·text·like·\"Here·is·the·commit·message:\"·in·your·response..\n[[Actual]]\nBelow·are·the·per-file·diffs·of·the·commit:↩\n↩\ndiff·--git·a/clients/vscode/src/main-panel/action-cards/actions-model.ts·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\nindex·16f324899..897ad939a·100644↩\n---·a/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n+++·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n@@·-158,7·+158,6·@@·const·initialDerivedStates·=·new·Map<DerivedStateName,·DerivedState>([↩\n·················},↩\n·················//·{·name:·SystemStateName.hasMovedExtensionAside,·status:·SystemStatus.complete·},↩\n·················{·name:·SystemStateName.workspacePopulated,·status:·SystemStatus.complete·},↩\n-················{·name:·SystemStateName.summaryReady,·status:·SystemStatus.complete·},↩\n·············],↩\n·········},↩\n·····],↩\n↩\n↩\nBelow·are·the·commit·message·examples·from·the·same·repository,·to·illustrate·the·commit·message·style·and·conventions·to·follow:↩\n↩\n↩\nCommit·message·example·0:↩\ninfra/grafana:·Stand·up·in·gcp-core0↩\n↩\n·-·Use·the·kubecfg·helm·wrapper·(this·was·the·last·full·helm·chart)↩\n·-·Replace·`grafana.r.augmentcode.com`·which·was·a·PoC·running·in·old·CW↩\n···-·But·the·deployment·is·simpler·because·we·can·now·use·an·ingress↩\n·-·As·before,·use·oauth2·with·<EMAIL>·members↩\n···automatically·configured·as·site-wide·admins.↩\n·-·Only·the·CoreWeave·Prometheus·and·Devex·Metrics·(the·NEW·one·in↩\n···gcp-core0)·are·currently·added·as·datasources.↩\n···-·Metastore,·determined,·and·loki·are·excluded·for·now↩\n·-·Start·with·the·json·dashboards·I'd·expected·out·of·the·origina·CW↩\n···grafana.↩\n↩\nI·need·to·remember·how·I·did·the·original·exports.·Rather·than·doing·a↩\nDB·migration,·I'd·like·to·stick·to·this·git-first·approach·and·check·in↩\nexports·of·dashboards,·alerts,·and·notifications·from·the·original·CW↩\ngrafana.↩\n↩\n↩\n↩\nCommit·message·example·1:↩\nDon't·redirect·augmentcode.com·to·staging·(#13990)↩\n↩\nFor·the·play-test·tomorrow.↩\n↩\n↩\nCommit·message·example·2:↩\nflags·for·dry·test·(#14047)↩\n↩\nCo-authored-by:·Devang·Jhabakh·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·3:↩\n[intellij]·fixed·NPE·(#14043)↩\n↩\n↩\n↩\n↩\nCommit·message·example·4:↩\nIgnore·alerts·in·cnrm-system·namespace·(#14037)↩\n↩\nThis·looks·to·be·related·to·config-connector,·so·should·be·managed·by\\r↩\nGKE↩\n↩\n↩\nCommit·message·example·5:↩\nRound-robin·load-balancing·with·sticky·sessions·for·next-edit·inference·requests·(#14041)↩\n↩\nToggle·the·feature·of·the·inference·host·client·to·have·each·next-edit↩\nhost·route·all·inference·requests·for·a·given·request_session_id·to·a↩\nsingle·inference·host·for·a·period·of·time.↩\n↩\nWith·that·in·place,·we·can·enable·round-robin·selection·of·gRPC↩\nsub-channels·without·losing·the·benefits·of·caching·on·the↩\ninference·host.↩\n↩\nTested·the·config·in·dev·deploy.↩\n↩\n↩\nCommit·message·example·6:↩\nfix·(#14035)↩\n↩\n↩\n↩\n↩\nCommit·message·example·7:↩\nAU-4847:·Make·summary·show·with·syncing·status·done·(#13969)↩\n↩\nThis·updates·the·onboarding·model·to·show·the·summary·message·once·the\\r↩\nsyncing·is·\"done\"·the·same·as·the·progress·bar·and·status·bar.\\r↩\n\\r↩\nAs·part·of·this·PR·I've·added·tests,·noticed·a·number·of·misconceptions\\r↩\nwith·the·previous·implementation·that·I've·hopefully·corrected.\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/68f403c7-4d7a-4247-a2db-9e9bd6522073↩\n↩\n↩\nCommit·message·example·8:↩\nAU-4874·Adding·e2e·test·for·share·service·(#14013)↩\n↩\nAdding·an·end-to-end·test·for·the·Share·service.↩\n↩\nTest:↩\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929ba9-7f32-758e-1612-5ddc49fcc069↩\n↩\n↩\nCommit·message·example·9:↩\nUse·ModelArgs.load_from_dict·to·load.·(#14019)↩\n↩\nWe·used·to·call·`ModelArgs.schema().load(params)`·directly,·but·this·is·not·backward·compatible·with·old·`params`·and·we·need·to·remember·to·call·`fix_model_arg_params`,·which·I·didn't·know·existed.·This·PR·slightly·improves·the·situation·by·defining·a·new·static·method·`ModelArgs.load_from_dict`·and·encourage·its·uses.↩\n↩\n↩\nCommit·message·example·10:↩\nScale·to·fewer·indexers·(#14032)↩\n↩\nNow·that·we·have·better·indexer·parallelism,·we·shouldn't↩\nneed·to·go·as·high.↩\n↩\nTesting·done:·None↩\n↩\n↩\nCommit·message·example·11:↩\nremove·pre_attention_kernel_fusion·flag·from·inference·server·(#13840)↩\n↩\nJust·a·minor·cleanup.↩\n↩\n↩\nCommit·message·example·12:↩\nNext·edit·host·cleanup:·encapsulate·request·parameters·(#14001)↩\n↩\n↩\n↩\n↩\nCommit·message·example·13:↩\nAdd·view·for·noncustomer·tenants·(#14022)↩\n↩\nCurrently·when·we·want·to·write·a·query·that·filters·out↩\nnon-customer·tenants·(e.g.,·pentest),·we·do·so·by·joining·on↩\nthe·customers_request_metadata·view.·We·have·tenant_id·in·every↩\ntable,·though,·so·as·long·as·we·have·the·list·of·tenants·to·filter↩\nout·we·can·do·this·without·the·join·(depending·on·the·event;↩\nsometimes·you·also·need·to·filter·by·user-agent).↩\n↩\n↩\nCommit·message·example·14:↩\nUse·eldenv4c·everywhere·in·prod·as·default·(#14006)↩\n↩\nThis·PR·makes·the·new·eldenv4-0c·model·default·in·prod.·It·has·already\\r↩\nbeen·default·in·vanguard·for·24·hours·without·issue.\\r↩\n\\r↩\nThe·changes·are:\\r↩\n-·Uses·smart·ethanol,·an·ethanol·retriever·with·smart·chunking\\r↩\n-·Trained·on·more·tests↩\n↩\n↩\nCommit·message·example·15:↩\nAdd·instruction/smart-paste·to·dev-deploy·(#13881)↩\n↩\n↩\n↩\n↩\nCommit·message·example·16:↩\nsave·requests·to·disk·(#14023)↩\n↩\nCo-authored-by:·Lior·Neumann·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·17:↩\nDeploy·EldenV7-0·(RLHF·models)·(#13921)↩\n↩\nThis·is·model·is·an·RLHF-ed·model·on·top·of·Elden·V3,·and·got·1%·token\\r↩\naccuracy·improvements·over·multiple·hindsight·datasets.\\r↩\n-·Has·tested·in·dev-deploy.\\r↩\n-·Has·synced·the·checkpoint↩\n↩\n↩\nCommit·message·example·18:↩\nAssign·tenant·ids·to·very·old·tenants·in·BigQuery·(#14011)↩\n↩\nThis·commit·assigns·arbitrary·tenant·ids·to·very·old·tenants·(who↩\nwere·deleted·before·the·namespace·sharding·work)·in·BigQuery.↩\nThis·will·allow·us·to·simplify·some·queries.↩\n↩\n↩\nCommit·message·example·19:↩\nwip·wordpress·scraping;·not·actually·implemented·yet·(#13918)↩\n↩\n↩\n↩\n↩\nCommit·message·example·20:↩\n[intellij]·upgrade·Gradle·plugin·to·2.0·(#14000)↩\n↩\nThis·will·help·us·in·the·future·to·support·2024.2+·versions.·It's\\r↩\nmandatory·to·use·a·new·version·going·forward.↩\n↩\n↩\nCommit·message·example·21:↩\nadd·eval·experimental·stuff·for·docsets·(#14012)↩\n↩\n↩\n↩\n↩\nCommit·message·example·22:↩\nTonic·-·set·keepalive·parameters·(#13982)↩\n↩\nMost·useful·for·long-lived·streams·where·client·is·listening·for↩\nnotifications.↩\n↩\nTesting·done:·verified·with·wireshark·and·ping·client·that·HTTP/2·keepalives·are·being·sent.↩\n↩\n↩\nCommit·message·example·23:↩\nGraceful·shutdown·for·Rust·services·(#14015)↩\n↩\n-·Plumb·sigterm·into·Tonic↩\n·-·wait·for·all·servers·to·shut·down·in·response·to·sigterm,·not↩\n···just·the·first·one.↩\n↩\nTesting·done:·dev·deploy·of·all·affected·services·and·rollout·restart·of·api·proxy↩\n↩\n↩\nCommit·message·example·24:↩\nprotobuf·with·typescript·generation·(#14009)↩\n↩\n###·TL;DR↩\n↩\nUpdated·BUILD·file·and·TypeScript·proto·library·generation·in·Bazel↩\n↩\n###·What·changed?↩\n↩\n-·In·`services/customer/frontend/BUILD`,·changed·`deps`·to·`data`·for↩\nthe·`js_library`·rule.↩\n-·In·`tools/bzl/typescript.bzl`,·refactored·the·`ts_proto_library`↩\nfunction:↩\n··-·Removed·`files_to_copy`·and·`copy_files`·parameters↩\n··-·Added·`visibility`·parameter↩\n··-·Created·separate·targets·for·JavaScript·and·TypeScript·files↩\n-·Introduced·a·filegroup·to·combine·both·JavaScript·and·TypeScript↩\noutputs↩\n↩\n###·How·to·test?↩\n↩\n1.·Rebuild·the·affected·targets·in·the·`services/customer/frontend`↩\ndirectory↩\n2.·Verify·that·the·TypeScript·proto·libraries·are·generated·correctly↩\n3.·Ensure·that·the·frontend·builds·and·runs·without·errors↩\n4.·Check·that·the·visibility·of·the·generated·proto·libraries·is·set↩\ncorrectly↩\n↩\n###·Why·make·this·change?↩\n↩\nThis·change·improves·the·Bazel·build·process·for·TypeScript·proto↩\nlibraries·by:↩\n1.·Correctly·specifying·dependencies·as·`data`·instead·of·`deps`·in·the↩\nfrontend·BUILD·file↩\n2.·Simplifying·the·`ts_proto_library`·function·and·improving·its↩\nflexibility↩\n3.·Separating·JavaScript·and·TypeScript·outputs·for·better·control·over↩\ngenerated·files↩\n4.·Allowing·for·customizable·visibility·of·the·generated·proto·libraries↩\n↩\nThese·improvements·should·lead·to·more·efficient·builds·and·better↩\nmanagement·of·TypeScript·proto·libraries·in·the·project.↩\n↩\n↩\nCommit·message·example·25:↩\nEnable·flywheel·for·vanguard·(#14018)↩\n↩\n↩\n↩\n↩\nCommit·message·example·26:↩\nsupport·tagging·files·in·inline·chat·(#13991)↩\n↩\nI·put·together·this·draft·in·order·to·see·how·hard·it·is·to·include\\r↩\nfuzzy·search·for·mentions·in·the·inline·chat.·It·works,·but·it's·a·mess.↩\n↩\n↩\nCommit·message·example·27:↩\n[Context]·Move·context·to·the·flywheel·flag·(#13989)↩\n↩\nMove·new·context·UI·to·be·gated·behind·the·flywheel·flag↩\n↩\n↩\nCommit·message·example·28:↩\n[Chat]·Dynamic·codeblock·actions·update·with·confirmation·(#13705)↩\n↩\nFixes\\r↩\nhttps://linear.app/augmentcode/issue/AU-4756/remove-create-button-when-theres-no-file-header\\r↩\nNote·that·the·linear·ticket·advocates·for·a·slightly·different·behavior\\r↩\n--·we·always·have·a·primary·action,·but·if·the·primary·action·doesn't\\r↩\napply,·the·user·can·still·select·any·of·the·other·actions·from·the·menu\\r↩\n\\r↩\nThis·PR·does·three·things:\\r↩\n-·Makes·the·logic·for·when·smart·paste·is·active·much·clearer·and\\r↩\nexplicit\\r↩\n-·Makes·it·so·if·metadata·is·returned,·we·ONLY·show·one·of·apply·or\\r↩\ncreate\\r↩\n-·Adds·a·confirmation·modal·if·there·is·no·metadata·to·apply·to\\r↩\n\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/2e4e525d-dc99-4b69-9465-ba6be055fbfa↩\n↩\n↩\nCommit·message·example·29:↩\nSet·the·bigtable·client·limit·to·256·MB·(#13959)↩\n↩\nThe·go·client·says·in\\r↩\n\\r↩\nhttps://github.com/googleapis/google-cloud-go/commit/c750310a90c16b6f633ad076ffeb299664f63774\\r↩\n\"Use·correct·value·for·message·size\"\\r↩\nand·\"Set·the·max·size·to·correspond·to·server-side·limits\"\\r↩\nsetting·it·to·1<<28.\\r↩\n\\r↩\nThe·official·Java·client·says\\r↩\n(https://github.com/googleapis/java-bigtable/blob/main/google-cloud-bigtable/src/main/java/com/google/cloud/bigtable/data/v2/stub/EnhancedBigtableStubSettings.java#L99)\\r↩\n```\\r↩\n//·The·largest·message·that·can·be·received·is·a·256·MB·ReadRowsResponse.\\r↩\n··private·static·final·int·MAX_MESSAGE_SIZE·=·256·*·1024·*·1024;\\r↩\n```\\r↩\n\\r↩\nPython·has·at\\r↩\nhttps://github.com/googleapis/python-bigtable/blob/main/google/cloud/bigtable/client.py\\r↩\n```\\r↩\n_GRPC_CHANNEL_OPTIONS·=·(\\r↩\n····(\"grpc.max_send_message_length\",·-1),\\r↩\n····(\"grpc.max_receive_message_length\",·-1),\\r↩\n····(\"grpc.keepalive_time_ms\",·30000),\\r↩\n····(\"grpc.keepalive_timeout_ms\",·10000),\\r↩\n)\\r↩\n\\r↩\n```\\r↩\n\\r↩\nSo,·let's·do·the·same.\\r↩\n\\r↩\nThis·sets·the·limits·for·the·bigtable·client·in·bigtable·proxy·to·256·MB\\r↩\nand·each·client·of·bigtable·proxy·to·256·MB.↩\n↩\n↩\nCommit·message·example·30:↩\nAdding·support·for·keybinding·icons·in·markdown·with·custom·font·(#13988)↩\n↩\nI'm·adding·a·custom·font·I·made·with·Fantasticon·out·of·our·existing\\r↩\nicons.·These·get·added·to·our·package.json·and·are·available·throughout\\r↩\nthe·app·anywhere·these·types·of·icons·are·allowed.·I·use·them·in·place\\r↩\nof·the·key·bindings·in·the·Next·Edit·hover·now.\\r↩\n\\r↩\nNote·that·we·could·not·use·the·existing·SVGs·in·the·hover·because,·while\\r↩\nit·can·support·images·in·markdown,·whenever·we·put·an·SVG·into·markdown\\r↩\nit·triggers·some·kind·of·async·rendering·and·causes·vscode·to·add·the\\r↩\ncode-hover-contents·class·which·removes·the·display:inline-block·from\\r↩\nall·of·the·elements·of·the·hover·--·breaking·our·layout·in·other·ways.↩\n↩\n↩\nCommit·message·example·31:↩\nUpdate·feedback·slackbot·chat·channel·(#13968)↩\n↩\nWe're·holding·off·on·completions·and·next·edit·because·the·augment↩\nhistory·panel·can·generate·multiple·feedback·events·if·the·rating·or↩\nsend·feedback·buttons·are·pressed·multiple·times.↩\n↩\n↩\nAbove·are·all·the·commit·message·examples.↩\n↩\n↩\nDo·not·include·boilerplate·text·like·\"Here·is·the·commit·message:\"·in·your·response..\n[[Diff against expected]]\n--- \n+++ \n@@ -1,6 +1,4 @@\n Below·are·the·per-file·diffs·of·the·commit:↩\n-↩\n-↩\n ↩\n diff·--git·a/clients/vscode/src/main-panel/action-cards/actions-model.ts·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n index·16f324899..897ad939a·100644↩\n@@ -368,7 +366,4 @@\n Above·are·all·the·commit·message·examples.↩\n ↩\n ↩\n-↩\n-↩\n-↩\n Do·not·include·boilerplate·text·like·\"Here·is·the·commit·message:\"·in·your·response..\\ No newline\n\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[58], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtest_utils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtesting_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m assert_str_eq\n\u001b[0;32m----> 3\u001b[0m \u001b[43massert_str_eq\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt_output\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmessage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/augment/base/test_utils/testing_utils.py:158\u001b[0m, in \u001b[0;36massert_str_eq\u001b[0;34m(actual, expect, message)\u001b[0m\n\u001b[1;32m    150\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAssertionError\u001b[39;00m(\n\u001b[1;32m    151\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStrings are not equal: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmessage()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    152\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m[[Expected]]\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mexpect\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    153\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m[[Actual]]\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mactual\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    154\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m[[Diff against expected]]\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mdiff\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    155\u001b[0m         )\n\u001b[1;32m    156\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mAssertionError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    157\u001b[0m         \u001b[38;5;66;03m# drop `assert_str_eq` from the stack trace to improve readability\u001b[39;00m\n\u001b[0;32m--> 158\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m _drop_stack_frame(e)\n\u001b[1;32m    159\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "Cell \u001b[0;32mIn[58], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtest_utils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtesting_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m assert_str_eq\n\u001b[0;32m----> 3\u001b[0m \u001b[43massert_str_eq\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprompt_output\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmessage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mAssertionError\u001b[0m: Strings are not equal: \n[[Expected]]\nBelow·are·the·per-file·diffs·of·the·commit:↩\n↩\n↩\n↩\ndiff·--git·a/clients/vscode/src/main-panel/action-cards/actions-model.ts·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\nindex·16f324899..897ad939a·100644↩\n---·a/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n+++·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n@@·-158,7·+158,6·@@·const·initialDerivedStates·=·new·Map<DerivedStateName,·DerivedState>([↩\n·················},↩\n·················//·{·name:·SystemStateName.hasMovedExtensionAside,·status:·SystemStatus.complete·},↩\n·················{·name:·SystemStateName.workspacePopulated,·status:·SystemStatus.complete·},↩\n-················{·name:·SystemStateName.summaryReady,·status:·SystemStatus.complete·},↩\n·············],↩\n·········},↩\n·····],↩\n↩\n↩\nBelow·are·the·commit·message·examples·from·the·same·repository,·to·illustrate·the·commit·message·style·and·conventions·to·follow:↩\n↩\n↩\nCommit·message·example·0:↩\ninfra/grafana:·Stand·up·in·gcp-core0↩\n↩\n·-·Use·the·kubecfg·helm·wrapper·(this·was·the·last·full·helm·chart)↩\n·-·Replace·`grafana.r.augmentcode.com`·which·was·a·PoC·running·in·old·CW↩\n···-·But·the·deployment·is·simpler·because·we·can·now·use·an·ingress↩\n·-·As·before,·use·oauth2·with·<EMAIL>·members↩\n···automatically·configured·as·site-wide·admins.↩\n·-·Only·the·CoreWeave·Prometheus·and·Devex·Metrics·(the·NEW·one·in↩\n···gcp-core0)·are·currently·added·as·datasources.↩\n···-·Metastore,·determined,·and·loki·are·excluded·for·now↩\n·-·Start·with·the·json·dashboards·I'd·expected·out·of·the·origina·CW↩\n···grafana.↩\n↩\nI·need·to·remember·how·I·did·the·original·exports.·Rather·than·doing·a↩\nDB·migration,·I'd·like·to·stick·to·this·git-first·approach·and·check·in↩\nexports·of·dashboards,·alerts,·and·notifications·from·the·original·CW↩\ngrafana.↩\n↩\n↩\n↩\nCommit·message·example·1:↩\nDon't·redirect·augmentcode.com·to·staging·(#13990)↩\n↩\nFor·the·play-test·tomorrow.↩\n↩\n↩\nCommit·message·example·2:↩\nflags·for·dry·test·(#14047)↩\n↩\nCo-authored-by:·Devang·Jhabakh·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·3:↩\n[intellij]·fixed·NPE·(#14043)↩\n↩\n↩\n↩\n↩\nCommit·message·example·4:↩\nIgnore·alerts·in·cnrm-system·namespace·(#14037)↩\n↩\nThis·looks·to·be·related·to·config-connector,·so·should·be·managed·by\\r↩\nGKE↩\n↩\n↩\nCommit·message·example·5:↩\nRound-robin·load-balancing·with·sticky·sessions·for·next-edit·inference·requests·(#14041)↩\n↩\nToggle·the·feature·of·the·inference·host·client·to·have·each·next-edit↩\nhost·route·all·inference·requests·for·a·given·request_session_id·to·a↩\nsingle·inference·host·for·a·period·of·time.↩\n↩\nWith·that·in·place,·we·can·enable·round-robin·selection·of·gRPC↩\nsub-channels·without·losing·the·benefits·of·caching·on·the↩\ninference·host.↩\n↩\nTested·the·config·in·dev·deploy.↩\n↩\n↩\nCommit·message·example·6:↩\nfix·(#14035)↩\n↩\n↩\n↩\n↩\nCommit·message·example·7:↩\nAU-4847:·Make·summary·show·with·syncing·status·done·(#13969)↩\n↩\nThis·updates·the·onboarding·model·to·show·the·summary·message·once·the\\r↩\nsyncing·is·\"done\"·the·same·as·the·progress·bar·and·status·bar.\\r↩\n\\r↩\nAs·part·of·this·PR·I've·added·tests,·noticed·a·number·of·misconceptions\\r↩\nwith·the·previous·implementation·that·I've·hopefully·corrected.\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/68f403c7-4d7a-4247-a2db-9e9bd6522073↩\n↩\n↩\nCommit·message·example·8:↩\nAU-4874·Adding·e2e·test·for·share·service·(#14013)↩\n↩\nAdding·an·end-to-end·test·for·the·Share·service.↩\n↩\nTest:↩\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929ba9-7f32-758e-1612-5ddc49fcc069↩\n↩\n↩\nCommit·message·example·9:↩\nUse·ModelArgs.load_from_dict·to·load.·(#14019)↩\n↩\nWe·used·to·call·`ModelArgs.schema().load(params)`·directly,·but·this·is·not·backward·compatible·with·old·`params`·and·we·need·to·remember·to·call·`fix_model_arg_params`,·which·I·didn't·know·existed.·This·PR·slightly·improves·the·situation·by·defining·a·new·static·method·`ModelArgs.load_from_dict`·and·encourage·its·uses.↩\n↩\n↩\nCommit·message·example·10:↩\nScale·to·fewer·indexers·(#14032)↩\n↩\nNow·that·we·have·better·indexer·parallelism,·we·shouldn't↩\nneed·to·go·as·high.↩\n↩\nTesting·done:·None↩\n↩\n↩\nCommit·message·example·11:↩\nremove·pre_attention_kernel_fusion·flag·from·inference·server·(#13840)↩\n↩\nJust·a·minor·cleanup.↩\n↩\n↩\nCommit·message·example·12:↩\nNext·edit·host·cleanup:·encapsulate·request·parameters·(#14001)↩\n↩\n↩\n↩\n↩\nCommit·message·example·13:↩\nAdd·view·for·noncustomer·tenants·(#14022)↩\n↩\nCurrently·when·we·want·to·write·a·query·that·filters·out↩\nnon-customer·tenants·(e.g.,·pentest),·we·do·so·by·joining·on↩\nthe·customers_request_metadata·view.·We·have·tenant_id·in·every↩\ntable,·though,·so·as·long·as·we·have·the·list·of·tenants·to·filter↩\nout·we·can·do·this·without·the·join·(depending·on·the·event;↩\nsometimes·you·also·need·to·filter·by·user-agent).↩\n↩\n↩\nCommit·message·example·14:↩\nUse·eldenv4c·everywhere·in·prod·as·default·(#14006)↩\n↩\nThis·PR·makes·the·new·eldenv4-0c·model·default·in·prod.·It·has·already\\r↩\nbeen·default·in·vanguard·for·24·hours·without·issue.\\r↩\n\\r↩\nThe·changes·are:\\r↩\n-·Uses·smart·ethanol,·an·ethanol·retriever·with·smart·chunking\\r↩\n-·Trained·on·more·tests↩\n↩\n↩\nCommit·message·example·15:↩\nAdd·instruction/smart-paste·to·dev-deploy·(#13881)↩\n↩\n↩\n↩\n↩\nCommit·message·example·16:↩\nsave·requests·to·disk·(#14023)↩\n↩\nCo-authored-by:·Lior·Neumann·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·17:↩\nDeploy·EldenV7-0·(RLHF·models)·(#13921)↩\n↩\nThis·is·model·is·an·RLHF-ed·model·on·top·of·Elden·V3,·and·got·1%·token\\r↩\naccuracy·improvements·over·multiple·hindsight·datasets.\\r↩\n-·Has·tested·in·dev-deploy.\\r↩\n-·Has·synced·the·checkpoint↩\n↩\n↩\nCommit·message·example·18:↩\nAssign·tenant·ids·to·very·old·tenants·in·BigQuery·(#14011)↩\n↩\nThis·commit·assigns·arbitrary·tenant·ids·to·very·old·tenants·(who↩\nwere·deleted·before·the·namespace·sharding·work)·in·BigQuery.↩\nThis·will·allow·us·to·simplify·some·queries.↩\n↩\n↩\nCommit·message·example·19:↩\nwip·wordpress·scraping;·not·actually·implemented·yet·(#13918)↩\n↩\n↩\n↩\n↩\nCommit·message·example·20:↩\n[intellij]·upgrade·Gradle·plugin·to·2.0·(#14000)↩\n↩\nThis·will·help·us·in·the·future·to·support·2024.2+·versions.·It's\\r↩\nmandatory·to·use·a·new·version·going·forward.↩\n↩\n↩\nCommit·message·example·21:↩\nadd·eval·experimental·stuff·for·docsets·(#14012)↩\n↩\n↩\n↩\n↩\nCommit·message·example·22:↩\nTonic·-·set·keepalive·parameters·(#13982)↩\n↩\nMost·useful·for·long-lived·streams·where·client·is·listening·for↩\nnotifications.↩\n↩\nTesting·done:·verified·with·wireshark·and·ping·client·that·HTTP/2·keepalives·are·being·sent.↩\n↩\n↩\nCommit·message·example·23:↩\nGraceful·shutdown·for·Rust·services·(#14015)↩\n↩\n-·Plumb·sigterm·into·Tonic↩\n·-·wait·for·all·servers·to·shut·down·in·response·to·sigterm,·not↩\n···just·the·first·one.↩\n↩\nTesting·done:·dev·deploy·of·all·affected·services·and·rollout·restart·of·api·proxy↩\n↩\n↩\nCommit·message·example·24:↩\nprotobuf·with·typescript·generation·(#14009)↩\n↩\n###·TL;DR↩\n↩\nUpdated·BUILD·file·and·TypeScript·proto·library·generation·in·Bazel↩\n↩\n###·What·changed?↩\n↩\n-·In·`services/customer/frontend/BUILD`,·changed·`deps`·to·`data`·for↩\nthe·`js_library`·rule.↩\n-·In·`tools/bzl/typescript.bzl`,·refactored·the·`ts_proto_library`↩\nfunction:↩\n··-·Removed·`files_to_copy`·and·`copy_files`·parameters↩\n··-·Added·`visibility`·parameter↩\n··-·Created·separate·targets·for·JavaScript·and·TypeScript·files↩\n-·Introduced·a·filegroup·to·combine·both·JavaScript·and·TypeScript↩\noutputs↩\n↩\n###·How·to·test?↩\n↩\n1.·Rebuild·the·affected·targets·in·the·`services/customer/frontend`↩\ndirectory↩\n2.·Verify·that·the·TypeScript·proto·libraries·are·generated·correctly↩\n3.·Ensure·that·the·frontend·builds·and·runs·without·errors↩\n4.·Check·that·the·visibility·of·the·generated·proto·libraries·is·set↩\ncorrectly↩\n↩\n###·Why·make·this·change?↩\n↩\nThis·change·improves·the·Bazel·build·process·for·TypeScript·proto↩\nlibraries·by:↩\n1.·Correctly·specifying·dependencies·as·`data`·instead·of·`deps`·in·the↩\nfrontend·BUILD·file↩\n2.·Simplifying·the·`ts_proto_library`·function·and·improving·its↩\nflexibility↩\n3.·Separating·JavaScript·and·TypeScript·outputs·for·better·control·over↩\ngenerated·files↩\n4.·Allowing·for·customizable·visibility·of·the·generated·proto·libraries↩\n↩\nThese·improvements·should·lead·to·more·efficient·builds·and·better↩\nmanagement·of·TypeScript·proto·libraries·in·the·project.↩\n↩\n↩\nCommit·message·example·25:↩\nEnable·flywheel·for·vanguard·(#14018)↩\n↩\n↩\n↩\n↩\nCommit·message·example·26:↩\nsupport·tagging·files·in·inline·chat·(#13991)↩\n↩\nI·put·together·this·draft·in·order·to·see·how·hard·it·is·to·include\\r↩\nfuzzy·search·for·mentions·in·the·inline·chat.·It·works,·but·it's·a·mess.↩\n↩\n↩\nCommit·message·example·27:↩\n[Context]·Move·context·to·the·flywheel·flag·(#13989)↩\n↩\nMove·new·context·UI·to·be·gated·behind·the·flywheel·flag↩\n↩\n↩\nCommit·message·example·28:↩\n[Chat]·Dynamic·codeblock·actions·update·with·confirmation·(#13705)↩\n↩\nFixes\\r↩\nhttps://linear.app/augmentcode/issue/AU-4756/remove-create-button-when-theres-no-file-header\\r↩\nNote·that·the·linear·ticket·advocates·for·a·slightly·different·behavior\\r↩\n--·we·always·have·a·primary·action,·but·if·the·primary·action·doesn't\\r↩\napply,·the·user·can·still·select·any·of·the·other·actions·from·the·menu\\r↩\n\\r↩\nThis·PR·does·three·things:\\r↩\n-·Makes·the·logic·for·when·smart·paste·is·active·much·clearer·and\\r↩\nexplicit\\r↩\n-·Makes·it·so·if·metadata·is·returned,·we·ONLY·show·one·of·apply·or\\r↩\ncreate\\r↩\n-·Adds·a·confirmation·modal·if·there·is·no·metadata·to·apply·to\\r↩\n\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/2e4e525d-dc99-4b69-9465-ba6be055fbfa↩\n↩\n↩\nCommit·message·example·29:↩\nSet·the·bigtable·client·limit·to·256·MB·(#13959)↩\n↩\nThe·go·client·says·in\\r↩\n\\r↩\nhttps://github.com/googleapis/google-cloud-go/commit/c750310a90c16b6f633ad076ffeb299664f63774\\r↩\n\"Use·correct·value·for·message·size\"\\r↩\nand·\"Set·the·max·size·to·correspond·to·server-side·limits\"\\r↩\nsetting·it·to·1<<28.\\r↩\n\\r↩\nThe·official·Java·client·says\\r↩\n(https://github.com/googleapis/java-bigtable/blob/main/google-cloud-bigtable/src/main/java/com/google/cloud/bigtable/data/v2/stub/EnhancedBigtableStubSettings.java#L99)\\r↩\n```\\r↩\n//·The·largest·message·that·can·be·received·is·a·256·MB·ReadRowsResponse.\\r↩\n··private·static·final·int·MAX_MESSAGE_SIZE·=·256·*·1024·*·1024;\\r↩\n```\\r↩\n\\r↩\nPython·has·at\\r↩\nhttps://github.com/googleapis/python-bigtable/blob/main/google/cloud/bigtable/client.py\\r↩\n```\\r↩\n_GRPC_CHANNEL_OPTIONS·=·(\\r↩\n····(\"grpc.max_send_message_length\",·-1),\\r↩\n····(\"grpc.max_receive_message_length\",·-1),\\r↩\n····(\"grpc.keepalive_time_ms\",·30000),\\r↩\n····(\"grpc.keepalive_timeout_ms\",·10000),\\r↩\n)\\r↩\n\\r↩\n```\\r↩\n\\r↩\nSo,·let's·do·the·same.\\r↩\n\\r↩\nThis·sets·the·limits·for·the·bigtable·client·in·bigtable·proxy·to·256·MB\\r↩\nand·each·client·of·bigtable·proxy·to·256·MB.↩\n↩\n↩\nCommit·message·example·30:↩\nAdding·support·for·keybinding·icons·in·markdown·with·custom·font·(#13988)↩\n↩\nI'm·adding·a·custom·font·I·made·with·Fantasticon·out·of·our·existing\\r↩\nicons.·These·get·added·to·our·package.json·and·are·available·throughout\\r↩\nthe·app·anywhere·these·types·of·icons·are·allowed.·I·use·them·in·place\\r↩\nof·the·key·bindings·in·the·Next·Edit·hover·now.\\r↩\n\\r↩\nNote·that·we·could·not·use·the·existing·SVGs·in·the·hover·because,·while\\r↩\nit·can·support·images·in·markdown,·whenever·we·put·an·SVG·into·markdown\\r↩\nit·triggers·some·kind·of·async·rendering·and·causes·vscode·to·add·the\\r↩\ncode-hover-contents·class·which·removes·the·display:inline-block·from\\r↩\nall·of·the·elements·of·the·hover·--·breaking·our·layout·in·other·ways.↩\n↩\n↩\nCommit·message·example·31:↩\nUpdate·feedback·slackbot·chat·channel·(#13968)↩\n↩\nWe're·holding·off·on·completions·and·next·edit·because·the·augment↩\nhistory·panel·can·generate·multiple·feedback·events·if·the·rating·or↩\nsend·feedback·buttons·are·pressed·multiple·times.↩\n↩\n↩\nAbove·are·all·the·commit·message·examples.↩\n↩\n↩\n↩\n↩\n↩\nDo·not·include·boilerplate·text·like·\"Here·is·the·commit·message:\"·in·your·response..\n[[Actual]]\nBelow·are·the·per-file·diffs·of·the·commit:↩\n↩\ndiff·--git·a/clients/vscode/src/main-panel/action-cards/actions-model.ts·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\nindex·16f324899..897ad939a·100644↩\n---·a/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n+++·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n@@·-158,7·+158,6·@@·const·initialDerivedStates·=·new·Map<DerivedStateName,·DerivedState>([↩\n·················},↩\n·················//·{·name:·SystemStateName.hasMovedExtensionAside,·status:·SystemStatus.complete·},↩\n·················{·name:·SystemStateName.workspacePopulated,·status:·SystemStatus.complete·},↩\n-················{·name:·SystemStateName.summaryReady,·status:·SystemStatus.complete·},↩\n·············],↩\n·········},↩\n·····],↩\n↩\n↩\nBelow·are·the·commit·message·examples·from·the·same·repository,·to·illustrate·the·commit·message·style·and·conventions·to·follow:↩\n↩\n↩\nCommit·message·example·0:↩\ninfra/grafana:·Stand·up·in·gcp-core0↩\n↩\n·-·Use·the·kubecfg·helm·wrapper·(this·was·the·last·full·helm·chart)↩\n·-·Replace·`grafana.r.augmentcode.com`·which·was·a·PoC·running·in·old·CW↩\n···-·But·the·deployment·is·simpler·because·we·can·now·use·an·ingress↩\n·-·As·before,·use·oauth2·with·<EMAIL>·members↩\n···automatically·configured·as·site-wide·admins.↩\n·-·Only·the·CoreWeave·Prometheus·and·Devex·Metrics·(the·NEW·one·in↩\n···gcp-core0)·are·currently·added·as·datasources.↩\n···-·Metastore,·determined,·and·loki·are·excluded·for·now↩\n·-·Start·with·the·json·dashboards·I'd·expected·out·of·the·origina·CW↩\n···grafana.↩\n↩\nI·need·to·remember·how·I·did·the·original·exports.·Rather·than·doing·a↩\nDB·migration,·I'd·like·to·stick·to·this·git-first·approach·and·check·in↩\nexports·of·dashboards,·alerts,·and·notifications·from·the·original·CW↩\ngrafana.↩\n↩\n↩\n↩\nCommit·message·example·1:↩\nDon't·redirect·augmentcode.com·to·staging·(#13990)↩\n↩\nFor·the·play-test·tomorrow.↩\n↩\n↩\nCommit·message·example·2:↩\nflags·for·dry·test·(#14047)↩\n↩\nCo-authored-by:·Devang·Jhabakh·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·3:↩\n[intellij]·fixed·NPE·(#14043)↩\n↩\n↩\n↩\n↩\nCommit·message·example·4:↩\nIgnore·alerts·in·cnrm-system·namespace·(#14037)↩\n↩\nThis·looks·to·be·related·to·config-connector,·so·should·be·managed·by\\r↩\nGKE↩\n↩\n↩\nCommit·message·example·5:↩\nRound-robin·load-balancing·with·sticky·sessions·for·next-edit·inference·requests·(#14041)↩\n↩\nToggle·the·feature·of·the·inference·host·client·to·have·each·next-edit↩\nhost·route·all·inference·requests·for·a·given·request_session_id·to·a↩\nsingle·inference·host·for·a·period·of·time.↩\n↩\nWith·that·in·place,·we·can·enable·round-robin·selection·of·gRPC↩\nsub-channels·without·losing·the·benefits·of·caching·on·the↩\ninference·host.↩\n↩\nTested·the·config·in·dev·deploy.↩\n↩\n↩\nCommit·message·example·6:↩\nfix·(#14035)↩\n↩\n↩\n↩\n↩\nCommit·message·example·7:↩\nAU-4847:·Make·summary·show·with·syncing·status·done·(#13969)↩\n↩\nThis·updates·the·onboarding·model·to·show·the·summary·message·once·the\\r↩\nsyncing·is·\"done\"·the·same·as·the·progress·bar·and·status·bar.\\r↩\n\\r↩\nAs·part·of·this·PR·I've·added·tests,·noticed·a·number·of·misconceptions\\r↩\nwith·the·previous·implementation·that·I've·hopefully·corrected.\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/68f403c7-4d7a-4247-a2db-9e9bd6522073↩\n↩\n↩\nCommit·message·example·8:↩\nAU-4874·Adding·e2e·test·for·share·service·(#14013)↩\n↩\nAdding·an·end-to-end·test·for·the·Share·service.↩\n↩\nTest:↩\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929ba9-7f32-758e-1612-5ddc49fcc069↩\n↩\n↩\nCommit·message·example·9:↩\nUse·ModelArgs.load_from_dict·to·load.·(#14019)↩\n↩\nWe·used·to·call·`ModelArgs.schema().load(params)`·directly,·but·this·is·not·backward·compatible·with·old·`params`·and·we·need·to·remember·to·call·`fix_model_arg_params`,·which·I·didn't·know·existed.·This·PR·slightly·improves·the·situation·by·defining·a·new·static·method·`ModelArgs.load_from_dict`·and·encourage·its·uses.↩\n↩\n↩\nCommit·message·example·10:↩\nScale·to·fewer·indexers·(#14032)↩\n↩\nNow·that·we·have·better·indexer·parallelism,·we·shouldn't↩\nneed·to·go·as·high.↩\n↩\nTesting·done:·None↩\n↩\n↩\nCommit·message·example·11:↩\nremove·pre_attention_kernel_fusion·flag·from·inference·server·(#13840)↩\n↩\nJust·a·minor·cleanup.↩\n↩\n↩\nCommit·message·example·12:↩\nNext·edit·host·cleanup:·encapsulate·request·parameters·(#14001)↩\n↩\n↩\n↩\n↩\nCommit·message·example·13:↩\nAdd·view·for·noncustomer·tenants·(#14022)↩\n↩\nCurrently·when·we·want·to·write·a·query·that·filters·out↩\nnon-customer·tenants·(e.g.,·pentest),·we·do·so·by·joining·on↩\nthe·customers_request_metadata·view.·We·have·tenant_id·in·every↩\ntable,·though,·so·as·long·as·we·have·the·list·of·tenants·to·filter↩\nout·we·can·do·this·without·the·join·(depending·on·the·event;↩\nsometimes·you·also·need·to·filter·by·user-agent).↩\n↩\n↩\nCommit·message·example·14:↩\nUse·eldenv4c·everywhere·in·prod·as·default·(#14006)↩\n↩\nThis·PR·makes·the·new·eldenv4-0c·model·default·in·prod.·It·has·already\\r↩\nbeen·default·in·vanguard·for·24·hours·without·issue.\\r↩\n\\r↩\nThe·changes·are:\\r↩\n-·Uses·smart·ethanol,·an·ethanol·retriever·with·smart·chunking\\r↩\n-·Trained·on·more·tests↩\n↩\n↩\nCommit·message·example·15:↩\nAdd·instruction/smart-paste·to·dev-deploy·(#13881)↩\n↩\n↩\n↩\n↩\nCommit·message·example·16:↩\nsave·requests·to·disk·(#14023)↩\n↩\nCo-authored-by:·Lior·Neumann·<<EMAIL>>↩\n↩\n↩\nCommit·message·example·17:↩\nDeploy·EldenV7-0·(RLHF·models)·(#13921)↩\n↩\nThis·is·model·is·an·RLHF-ed·model·on·top·of·Elden·V3,·and·got·1%·token\\r↩\naccuracy·improvements·over·multiple·hindsight·datasets.\\r↩\n-·Has·tested·in·dev-deploy.\\r↩\n-·Has·synced·the·checkpoint↩\n↩\n↩\nCommit·message·example·18:↩\nAssign·tenant·ids·to·very·old·tenants·in·BigQuery·(#14011)↩\n↩\nThis·commit·assigns·arbitrary·tenant·ids·to·very·old·tenants·(who↩\nwere·deleted·before·the·namespace·sharding·work)·in·BigQuery.↩\nThis·will·allow·us·to·simplify·some·queries.↩\n↩\n↩\nCommit·message·example·19:↩\nwip·wordpress·scraping;·not·actually·implemented·yet·(#13918)↩\n↩\n↩\n↩\n↩\nCommit·message·example·20:↩\n[intellij]·upgrade·Gradle·plugin·to·2.0·(#14000)↩\n↩\nThis·will·help·us·in·the·future·to·support·2024.2+·versions.·It's\\r↩\nmandatory·to·use·a·new·version·going·forward.↩\n↩\n↩\nCommit·message·example·21:↩\nadd·eval·experimental·stuff·for·docsets·(#14012)↩\n↩\n↩\n↩\n↩\nCommit·message·example·22:↩\nTonic·-·set·keepalive·parameters·(#13982)↩\n↩\nMost·useful·for·long-lived·streams·where·client·is·listening·for↩\nnotifications.↩\n↩\nTesting·done:·verified·with·wireshark·and·ping·client·that·HTTP/2·keepalives·are·being·sent.↩\n↩\n↩\nCommit·message·example·23:↩\nGraceful·shutdown·for·Rust·services·(#14015)↩\n↩\n-·Plumb·sigterm·into·Tonic↩\n·-·wait·for·all·servers·to·shut·down·in·response·to·sigterm,·not↩\n···just·the·first·one.↩\n↩\nTesting·done:·dev·deploy·of·all·affected·services·and·rollout·restart·of·api·proxy↩\n↩\n↩\nCommit·message·example·24:↩\nprotobuf·with·typescript·generation·(#14009)↩\n↩\n###·TL;DR↩\n↩\nUpdated·BUILD·file·and·TypeScript·proto·library·generation·in·Bazel↩\n↩\n###·What·changed?↩\n↩\n-·In·`services/customer/frontend/BUILD`,·changed·`deps`·to·`data`·for↩\nthe·`js_library`·rule.↩\n-·In·`tools/bzl/typescript.bzl`,·refactored·the·`ts_proto_library`↩\nfunction:↩\n··-·Removed·`files_to_copy`·and·`copy_files`·parameters↩\n··-·Added·`visibility`·parameter↩\n··-·Created·separate·targets·for·JavaScript·and·TypeScript·files↩\n-·Introduced·a·filegroup·to·combine·both·JavaScript·and·TypeScript↩\noutputs↩\n↩\n###·How·to·test?↩\n↩\n1.·Rebuild·the·affected·targets·in·the·`services/customer/frontend`↩\ndirectory↩\n2.·Verify·that·the·TypeScript·proto·libraries·are·generated·correctly↩\n3.·Ensure·that·the·frontend·builds·and·runs·without·errors↩\n4.·Check·that·the·visibility·of·the·generated·proto·libraries·is·set↩\ncorrectly↩\n↩\n###·Why·make·this·change?↩\n↩\nThis·change·improves·the·Bazel·build·process·for·TypeScript·proto↩\nlibraries·by:↩\n1.·Correctly·specifying·dependencies·as·`data`·instead·of·`deps`·in·the↩\nfrontend·BUILD·file↩\n2.·Simplifying·the·`ts_proto_library`·function·and·improving·its↩\nflexibility↩\n3.·Separating·JavaScript·and·TypeScript·outputs·for·better·control·over↩\ngenerated·files↩\n4.·Allowing·for·customizable·visibility·of·the·generated·proto·libraries↩\n↩\nThese·improvements·should·lead·to·more·efficient·builds·and·better↩\nmanagement·of·TypeScript·proto·libraries·in·the·project.↩\n↩\n↩\nCommit·message·example·25:↩\nEnable·flywheel·for·vanguard·(#14018)↩\n↩\n↩\n↩\n↩\nCommit·message·example·26:↩\nsupport·tagging·files·in·inline·chat·(#13991)↩\n↩\nI·put·together·this·draft·in·order·to·see·how·hard·it·is·to·include\\r↩\nfuzzy·search·for·mentions·in·the·inline·chat.·It·works,·but·it's·a·mess.↩\n↩\n↩\nCommit·message·example·27:↩\n[Context]·Move·context·to·the·flywheel·flag·(#13989)↩\n↩\nMove·new·context·UI·to·be·gated·behind·the·flywheel·flag↩\n↩\n↩\nCommit·message·example·28:↩\n[Chat]·Dynamic·codeblock·actions·update·with·confirmation·(#13705)↩\n↩\nFixes\\r↩\nhttps://linear.app/augmentcode/issue/AU-4756/remove-create-button-when-theres-no-file-header\\r↩\nNote·that·the·linear·ticket·advocates·for·a·slightly·different·behavior\\r↩\n--·we·always·have·a·primary·action,·but·if·the·primary·action·doesn't\\r↩\napply,·the·user·can·still·select·any·of·the·other·actions·from·the·menu\\r↩\n\\r↩\nThis·PR·does·three·things:\\r↩\n-·Makes·the·logic·for·when·smart·paste·is·active·much·clearer·and\\r↩\nexplicit\\r↩\n-·Makes·it·so·if·metadata·is·returned,·we·ONLY·show·one·of·apply·or\\r↩\ncreate\\r↩\n-·Adds·a·confirmation·modal·if·there·is·no·metadata·to·apply·to\\r↩\n\\r↩\n\\r↩\n\\r↩\nhttps://github.com/user-attachments/assets/2e4e525d-dc99-4b69-9465-ba6be055fbfa↩\n↩\n↩\nCommit·message·example·29:↩\nSet·the·bigtable·client·limit·to·256·MB·(#13959)↩\n↩\nThe·go·client·says·in\\r↩\n\\r↩\nhttps://github.com/googleapis/google-cloud-go/commit/c750310a90c16b6f633ad076ffeb299664f63774\\r↩\n\"Use·correct·value·for·message·size\"\\r↩\nand·\"Set·the·max·size·to·correspond·to·server-side·limits\"\\r↩\nsetting·it·to·1<<28.\\r↩\n\\r↩\nThe·official·Java·client·says\\r↩\n(https://github.com/googleapis/java-bigtable/blob/main/google-cloud-bigtable/src/main/java/com/google/cloud/bigtable/data/v2/stub/EnhancedBigtableStubSettings.java#L99)\\r↩\n```\\r↩\n//·The·largest·message·that·can·be·received·is·a·256·MB·ReadRowsResponse.\\r↩\n··private·static·final·int·MAX_MESSAGE_SIZE·=·256·*·1024·*·1024;\\r↩\n```\\r↩\n\\r↩\nPython·has·at\\r↩\nhttps://github.com/googleapis/python-bigtable/blob/main/google/cloud/bigtable/client.py\\r↩\n```\\r↩\n_GRPC_CHANNEL_OPTIONS·=·(\\r↩\n····(\"grpc.max_send_message_length\",·-1),\\r↩\n····(\"grpc.max_receive_message_length\",·-1),\\r↩\n····(\"grpc.keepalive_time_ms\",·30000),\\r↩\n····(\"grpc.keepalive_timeout_ms\",·10000),\\r↩\n)\\r↩\n\\r↩\n```\\r↩\n\\r↩\nSo,·let's·do·the·same.\\r↩\n\\r↩\nThis·sets·the·limits·for·the·bigtable·client·in·bigtable·proxy·to·256·MB\\r↩\nand·each·client·of·bigtable·proxy·to·256·MB.↩\n↩\n↩\nCommit·message·example·30:↩\nAdding·support·for·keybinding·icons·in·markdown·with·custom·font·(#13988)↩\n↩\nI'm·adding·a·custom·font·I·made·with·Fantasticon·out·of·our·existing\\r↩\nicons.·These·get·added·to·our·package.json·and·are·available·throughout\\r↩\nthe·app·anywhere·these·types·of·icons·are·allowed.·I·use·them·in·place\\r↩\nof·the·key·bindings·in·the·Next·Edit·hover·now.\\r↩\n\\r↩\nNote·that·we·could·not·use·the·existing·SVGs·in·the·hover·because,·while\\r↩\nit·can·support·images·in·markdown,·whenever·we·put·an·SVG·into·markdown\\r↩\nit·triggers·some·kind·of·async·rendering·and·causes·vscode·to·add·the\\r↩\ncode-hover-contents·class·which·removes·the·display:inline-block·from\\r↩\nall·of·the·elements·of·the·hover·--·breaking·our·layout·in·other·ways.↩\n↩\n↩\nCommit·message·example·31:↩\nUpdate·feedback·slackbot·chat·channel·(#13968)↩\n↩\nWe're·holding·off·on·completions·and·next·edit·because·the·augment↩\nhistory·panel·can·generate·multiple·feedback·events·if·the·rating·or↩\nsend·feedback·buttons·are·pressed·multiple·times.↩\n↩\n↩\nAbove·are·all·the·commit·message·examples.↩\n↩\n↩\nDo·not·include·boilerplate·text·like·\"Here·is·the·commit·message:\"·in·your·response..\n[[Diff against expected]]\n--- \n+++ \n@@ -1,6 +1,4 @@\n Below·are·the·per-file·diffs·of·the·commit:↩\n-↩\n-↩\n ↩\n diff·--git·a/clients/vscode/src/main-panel/action-cards/actions-model.ts·b/clients/vscode/src/main-panel/action-cards/actions-model.ts↩\n index·16f324899..897ad939a·100644↩\n@@ -368,7 +366,4 @@\n Above·are·all·the·commit·message·examples.↩\n ↩\n ↩\n-↩\n-↩\n-↩\n Do·not·include·boilerplate·text·like·\"Here·is·the·commit·message:\"·in·your·response..\\ No newline\n\n"]}], "source": ["from base.test_utils.testing_utils import assert_str_eq\n", "\n", "assert_str_eq(prompt_output.message, prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test writing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The commit contains the following changes:\n", "Modified 15 files:\n", "    M +1 -0 modified_file0.py\n", "    M +2 -1 modified_file1.py\n", "    M +3 -2 modified_file2.py\n", "    M +4 -3 modified_file3.py\n", "    M +5 -4 modified_file4.py\n", "Added 1 files:\n", "    A +16 -0 added_file0.py\n", "\n", "\n", "Below are the per-file diffs of the commit:\n", "\n", "DIFF HERE\n", "\n", "\n", "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n", "\n", "\n", "Commit message example 0:\n", "Example commit message 0\n", "\n", "\n", "Above are all the commit message examples.\n", "\n", "\n", "Do not include boilerplate text like \"Here is the commit message:\" in your response..\n"]}], "source": ["from base.prompt_format_chat.generate_commit_message_prompt_formatter import (\n", "    GenerateCommitMessagePromptFormatterV2,\n", "    GenerateCommitMessageTokenApportionment,\n", ")\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "from base.prompt_format_chat.prompt_formatter import ChangedFileStats\n", "from base.prompt_format_chat.prompt_formatter import PerTypeChangedFileStats\n", "from base.prompt_format_chat.prompt_formatter import PerFileChangeStats\n", "from base.prompt_format_chat.prompt_formatter import GenerateCommitMessagePromptInput\n", "from base.prompt_format_chat.lib.token_counter import RoughTokenCounter\n", "\n", "token_counter = RoughTokenCounter()\n", "token_apportionment = GenerateCommitMessageTokenApportionment(\n", "    path_len=0,\n", "    message_len=0,\n", "    chat_history_len=0,\n", "    prefix_len=0,\n", "    selected_code_len=0,\n", "    suffix_len=0,\n", "    changed_files_summary_line_threshold=0,\n", "    diff_len=9_216,\n", "    commit_message_len=3_072,\n", "    relevant_message_len=1_024,\n", "    max_prompt_len=12_288,\n", ")\n", "formatter = GenerateCommitMessagePromptFormatterV2(token_counter, token_apportionment)\n", "\n", "\n", "prompt_input = ChatPromptInput(\n", "    message=\"\",\n", "    path=\"\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", "    changed_file_stats=ChangedFileStats(\n", "        added_file_stats=PerTypeChangedFileStats(\n", "            changed_file_count=1,\n", "            per_file_change_stats_head=[\n", "                PerFileChangeStats(\n", "                    path=\"added_file0.py\",\n", "                    insertion_count=16,\n", "                    deletion_count=0,\n", "                    old_path=\"\",\n", "                )\n", "            ],\n", "        ),\n", "        modified_file_stats=PerTypeChangedFileStats(\n", "            changed_file_count=15,\n", "            per_file_change_stats_head=[\n", "                PerFileChangeStats(\n", "                    path=f\"modified_file{i}.py\",\n", "                    insertion_count=i + 1,\n", "                    deletion_count=i,\n", "                    old_path=\"\",\n", "                )\n", "                for i in range(5)\n", "            ],\n", "            per_file_change_stats_tail=[\n", "                PerFileChangeStats(\n", "                    path=f\"modified_file{i}.py\",\n", "                    insertion_count=i + 1,\n", "                    deletion_count=i,\n", "                    old_path=\"\",\n", "                )\n", "                for i in range(10, 15)\n", "            ],\n", "        ),\n", "    ),\n", "    diff=\"\"\"DIFF HERE\"\"\",\n", "    generated_commit_message_prompt_input=GenerateCommitMessagePromptInput(\n", "        relevant_commit_messages=[],\n", "        example_commit_messages=[\"Example commit message 0\"],\n", "    ),\n", ")\n", "\n", "prompt_output = formatter.format_prompt(prompt_input)\n", "print(prompt_output.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary V2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1000 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1000/1000 [02:32<00:00,  6.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["0.031062124248496994\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "\n", "has_summary_v2 = []\n", "total_changed_line_counts = []\n", "for _ in tqdm(range(1000)):\n", "    try:\n", "        commit = mgh.get_random_commit()\n", "        prompt, metadata = mgh.get_prompt(\n", "            commit[\"current_hash\"],\n", "            summary=False,\n", "            latest_commit_count=32,\n", "            sort_paths_by_size=True,\n", "            count_diffs_by_hunk=True,\n", "            summary_v2_threshold=900,\n", "            trailing_instructions=TRAILING_INSTRUCTIONS,\n", "            message_soft_budget=1024 * 3,\n", "            relevant_message_limit=0,\n", "            diff_line_limit=5000,\n", "            token_budget=1024 * 12,\n", "        )\n", "        has_summary_v2.append(\n", "            \"SUMMARY SHOULD BE HERE\" in prompt\n", "            or \"The commit contains the following changes:\" in prompt\n", "        )\n", "        total_changed_line_counts.append(metadata[\"total_changed_line_count\"])\n", "    except Exception:\n", "        continue\n", "\n", "print(sum(has_summary_v2) / len(has_summary_v2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1894749/298023023.py:21: RuntimeWarning: divide by zero encountered in log10\n", "  np.log10(min(min(summary_true), min(summary_false))),\n", "/opt/conda/lib/python3.11/site-packages/numpy/core/function_base.py:158: RuntimeWarning: invalid value encountered in multiply\n", "  y *= step\n", "/opt/conda/lib/python3.11/site-packages/numpy/core/function_base.py:168: RuntimeWarning: invalid value encountered in add\n", "  y += start\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA2IAAAIoCAYAAAALG1gsAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACN8klEQVR4nOzdd3gU5dfG8XsT0kNCSUggtNCrVOkIAhoEQhERkRoCFooFQQWRqiIiVZGmgAiIoogogiBFkaJUUUCULgFCT2ghIZn3j7zZH0sSSMKWbPL9XBeu++wzZ87M7E7m7DwzazIMwxAAAAAAwG5cHJ0AAAAAAOQ2FGIAAAAAYGcUYgAAAABgZxRiAAAAAGBnFGIAAAAAYGcUYgAAAABgZxRiAAAAAGBnFGIAAAAAYGcUYgAAAABgZxRigB2MGjVKJpPJLvNq2rSpmjZtan6+ceNGmUwmffXVV3aZf69evVSyZEm7zCurrl69qj59+ig4OFgmk0kvvfSSVeKmbOfz589bJV52Zc1tbM/PBu7PsWPHZDKZNH/+/Az3ff/9922f2H26c59pTSn7340bN2a4r7321QAcj0IMyKT58+fLZDKZ/3l6eqpIkSIKCwvTtGnTdOXKFavM59SpUxo1apT27NljlXjWlJ1zy4h33nlH8+fP1/PPP6/PPvtM3bt3v2v/xMREzZs3T02bNlWBAgXk4eGhkiVLKiIiQjt27LBT1s6nadOmqlKliqPTuCtn2Lb79+/XqFGjdOzYMUenksoPP/ygUaNGOToNp7J48WJNmTLF0WlYuH79ukaNGpWhgjHFsWPHFBERodKlS8vT01PBwcF66KGHNHLkSNslCuQwFGJAFo0ZM0afffaZZsyYoYEDB0qSXnrpJVWtWlV79+616Dt8+HDduHEjU/FPnTql0aNHZ7rYWbNmjdasWZOpaTLrbrnNmTNHBw8etOn879f69etVr149jRw5Ut26dVOtWrXS7Xvjxg21adNGvXv3lmEYGjZsmGbMmKEePXpo69atqlOnjk6ePGnH7HOWrHw2rMVZtu3+/fs1evRohxdiJUqU0I0bNyy+uPjhhx80evRoB2aVvT300EO6ceOGHnroIXNbdi3ERo8eneFC7NChQ6pRo4Z+/PFHdenSRR9++KH69++vggULavz48bZNFshB8jg6AcBZPfbYY6pdu7b5+dChQ7V+/Xq1adNGbdu21YEDB+Tl5SVJypMnj/Lkse3H7fr16/L29pa7u7tN53Mvbm5uDp1/Rpw9e1aVKlXKUN8hQ4Zo9erVmjx5cqohjCNHjtTkyZNtkGHuYY/PRnrYtpmTMgIAGefi4pIj19nkyZN19epV7dmzRyVKlLB47ezZsw7KyrFS/gYDmcEZMcCKmjVrpjfffFPHjx/XwoULze1pXQezdu1aNWrUSPny5ZOvr6/Kly+vYcOGSUq+VuDBBx+UJEVERJiHQaZcm5Ey5Gvnzp166KGH5O3tbZ42vesdEhMTNWzYMAUHB8vHx0dt27bVf//9Z9GnZMmS6tWrV6ppb495r9zSun7o2rVreuWVV1SsWDF5eHiofPnyev/992UYhkU/k8mkAQMGaPny5apSpYo8PDxUuXJlrV69Ou0VfoezZ88qMjJSQUFB8vT0VLVq1fTpp5+aX0+5BuPo0aNauXKlOff0zjScPHlSs2bN0iOPPJLmdWSurq4aPHiwihYtatF++fJl9erVS/ny5ZO/v78iIiJ0/fp1iz7z5s1Ts2bNVKhQIXl4eKhSpUqaMWNGqnmULFlSbdq00a+//qo6derI09NTpUqV0oIFC1L13bt3r5o0aSIvLy8VLVpUb731lubNm5fmMq5atUqNGzeWj4+P8ubNq9atW2vfvn2pYqZsC09PT1WpUkXffPNNmusqq9L6bGTmfRAVFaXevXsrKCjI3G/u3Ln3nG9Wtu3u3bv12GOPyc/PT76+vmrevLm2bdt2z+WR/jek+fbtkJFtO3/+fHXq1EmS9PDDD5vfsylnLnbs2KGwsDAFBATIy8tLoaGh6t27912XfdCgQSpYsKDF52/gwIEymUyaNm2auS06Olomk8n8vrzzGrFevXpp+vTpkmQxXPtOs2fPVunSpeXh4aEHH3xQ27dvv2t+knTx4kUNHjxYVatWla+vr/z8/PTYY4/pjz/+sOiX8pn+8ssv9fbbb6to0aLy9PRU8+bNdejQoXRz8fLyUp06dbRp06Z75iJJjz/+uGrWrGnRFh4eLpPJpBUrVpjbfvvtN5lMJq1atcoiv5Tt1bRpU61cuVLHjx83r68795dJSUkZWpalS5eqVq1a8vLyUkBAgLp166aoqCiLPun9Pbh9P33s2DEFBgZKkkaPHm3O625DTg8fPqyiRYumKsIkqVChQhbP04t159+blM/Ir7/+qhdeeEGBgYHKly+fnn32WcXHx+vy5cvq0aOH8ufPr/z58+vVV1+1eA/ffl3i9OnTVapUKXl7e+vRRx/Vf//9J8MwNHbsWBUtWlReXl5q166dLl68aJHTt99+q9atW6tIkSLy8PBQ6dKlNXbsWCUmJqZar2n9De7Zs6cCAgKUkJCQankfffRRlS9fPt11ityJM2KAlXXv3l3Dhg3TmjVr1Ldv3zT77Nu3T23atNEDDzygMWPGyMPDQ4cOHdLmzZslSRUrVtSYMWM0YsQIPfPMM2rcuLEkqUGDBuYYFy5c0GOPPaannnpK3bp1U1BQ0F3zevvtt2UymfTaa6/p7NmzmjJlilq0aKE9e/aYz9xlREZyu51hGGrbtq02bNigyMhIVa9eXT/++KOGDBmiqKioVGcdfv31Vy1btkz9+vVT3rx5NW3aNHXs2FEnTpxQwYIF083rxo0batq0qQ4dOqQBAwYoNDRUS5cuVa9evXT58mW9+OKLqlixoj777DO9/PLLKlq0qF555RVJMh+E3GnVqlW6devWPa8hu9OTTz6p0NBQjRs3Trt27dLHH3+sQoUKWQzZmTFjhipXrqy2bdsqT548+u6779SvXz8lJSWpf//+FvEOHTqkJ554QpGRkerZs6fmzp2rXr16qVatWqpcubKk5IIk5UB96NCh8vHx0ccffywPD49U+X322Wfq2bOnwsLCNH78eF2/fl0zZsxQo0aNtHv3bvMB2po1a9SxY0dVqlRJ48aN04ULFxQREZGq8LSFjLwPoqOjVa9ePXPhFhgYqFWrVikyMlKxsbF3vQlLZrftvn371LhxY/n5+enVV1+Vm5ubZs2apaZNm+rnn39W3bp1s7Sc99q2Dz30kF544QVNmzZNw4YNU8WKFSUlfw7Pnj2rRx99VIGBgXr99deVL18+HTt2TMuWLbvrPBs3bqzJkydr37595mv4Nm3aJBcXF23atEkvvPCCuU2SxbC62z377LM6deqU1q5dq88++yzNPosXL9aVK1f07LPPymQy6b333tPjjz+uI0eO3PXs+ZEjR7R8+XJ16tRJoaGhio6O1qxZs9SkSRPt379fRYoUsej/7rvvysXFRYMHD1ZMTIzee+89de3aVb/99pu5zyeffKJnn31WDRo00EsvvaQjR46obdu2KlCggIoVK3bPdfbtt98qNjZWfn5+MgxDmzdvNq+ztm3bWqzHhg0bphnnjTfeUExMjE6ePGne9/n6+mZ6WebPn6+IiAg9+OCDGjdunKKjozV16lRt3rxZu3fvVr58+e66PLcLDAzUjBkz9Pzzz6tDhw56/PHHJUkPPPBAutOUKFFCP/30k9avX69mzZpleF4ZMXDgQAUHB2v06NHatm2bZs+erXz58mnLli0qXry43nnnHf3www+aMGGCqlSpoh49elhMv2jRIsXHx2vgwIG6ePGi3nvvPT355JNq1qyZNm7cqNdee02HDh3SBx98oMGDB1t8cTN//nz5+vpq0KBB8vX11fr16zVixAjFxsZqwoQJFvNJ62+wj4+PFixYoB9//FFt2rQx9z1z5ozWr1/P9XNIzQCQKfPmzTMkGdu3b0+3j7+/v1GjRg3z85EjRxq3f9wmT55sSDLOnTuXbozt27cbkox58+aleq1JkyaGJGPmzJlpvtakSRPz8w0bNhiSjJCQECM2Ntbc/uWXXxqSjKlTp5rbSpQoYfTs2fOeMe+WW8+ePY0SJUqYny9fvtyQZLz11lsW/Z544gnDZDIZhw4dMrdJMtzd3S3a/vjjD0OS8cEHH6Sa1+2mTJliSDIWLlxobouPjzfq169v+Pr6Wix7iRIljNatW981nmEYxssvv2xIMnbv3n3Pvobxv+3cu3dvi/YOHToYBQsWtGi7fv16qunDwsKMUqVKWbSVKFHCkGT88ssv5razZ88aHh4exiuvvGJuGzhwoGEymSxyvXDhglGgQAFDknH06FHDMAzjypUrRr58+Yy+fftazOfMmTOGv7+/RXv16tWNwoULG5cvXza3rVmzxpBksY3T06RJE6Ny5cp37XPnZ8MwMv4+iIyMNAoXLmycP3/eYvqnnnrK8Pf3T3Mdp8jstm3fvr3h7u5uHD582Nx26tQpI2/evMZDDz101+UxjP/tN1K2g2FkfNsuXbrUkGRs2LDBIuY333xzz31RWs6ePWtIMj766CPDMAzj8uXLhouLi9GpUycjKCjI3O+FF14wChQoYCQlJRmGYRhHjx5N9bnv379/msub0rdgwYLGxYsXze3ffvutIcn47rvv7ppjXFyckZiYmCqmh4eHMWbMGHNbyv6tYsWKxs2bN83tU6dONSQZf/75p2EYyfuCQoUKGdWrV7foN3v2bEOSxf4tLSn7vB9++MEwDMPYu3evIcno1KmTUbduXXO/tm3bWuz7U/K7fdu1bt06zc9PZpelSpUqxo0bN8z9vv/+e0OSMWLECHPbnfvuFHfup8+dO2dIMkaOHHnX9ZDir7/+Mry8vAxJRvXq1Y0XX3zRWL58uXHt2rVUfdOLe+ffm5TPSFhYmPk9ZxiGUb9+fcNkMhnPPfecue3WrVtG0aJFLZYt5T0XGBhosc8aOnSoIcmoVq2akZCQYG7v0qWL4e7ubsTFxZnb0tpnPPvss4a3t7dFv/T+BicmJhpFixY1OnfubNE+adIkw2QyGUeOHEkVH7kbQxMBG/D19b3r3RNTvq389ttvlZSUlKV5eHh4KCIiIsP9e/Toobx585qfP/HEEypcuLB++OGHLM0/o3744Qe5urqav2VP8corr8gwDPMQnhQtWrRQ6dKlzc8feOAB+fn56ciRI/ecT3BwsLp06WJuc3Nz0wsvvKCrV6/q559/znTusbGxkmSx3jLiueees3jeuHFjXbhwwRxPksVZyJiYGJ0/f15NmjTRkSNHFBMTYzF9pUqVzGcepeRvsMuXL2+xTlavXq369eurevXq5rYCBQqoa9euFrHWrl2ry5cvq0uXLjp//rz5n6urq+rWrasNGzZIkk6fPq09e/aoZ8+e8vf3N0//yCOPZPj6uvtxr/eBYRj6+uuvFR4eLsMwLJYlLCxMMTEx2rVrV7rxM7NtExMTtWbNGrVv316lSpUytxcuXFhPP/20fv31V4ttmxkZ2bbpSdmPfP/992kOhUpPYGCgKlSooF9++UWStHnzZrm6umrIkCGKjo7Wv//+Kyn57E6jRo3u6+cFOnfurPz585ufpyzrvZbPw8NDLi7JhyiJiYm6cOGCeQh3Wts1IiLC4vrYO+ezY8cOnT17Vs8995xFv169elm8v9NTo0YN+fr6mtfZpk2bVLRoUfXo0UO7du3S9evXZRiGfv31V4vtmRUZXZZ+/fpZXH/WunVrVahQQStXrryv+WdE5cqVtWfPHnXr1k3Hjh3T1KlT1b59ewUFBWnOnDn3FTsyMtLiPVe3bl0ZhqHIyEhzm6urq2rXrp3m+6hTp04W2zTlbHW3bt0srketW7eu4uPjLYZz3r5fvnLlis6fP6/GjRvr+vXr+vvvvy3mk9bfYBcXF3Xt2lUrVqywOAZYtGiRGjRooNDQ0AyvB+QOFGKADVy9evWuB3idO3dWw4YN1adPHwUFBempp57Sl19+mamiLCQkJFM35ihbtqzFc5PJpDJlytj8TmzHjx9XkSJFUq2PlCFWx48ft2gvXrx4qhj58+fXpUuX7jmfsmXLmg/e7jWfjPDz85OkTP8kwZ3LkHIgevsybN68WS1atJCPj4/y5cunwMBA83V+dxZiGVknx48fV5kyZVL1u7Mt5SC7WbNmCgwMtPi3Zs0a84X2KevrzveNJLtc53CvZT537pwuX76s2bNnp1qOlIOju900IDPb9ty5c7p+/Xqay12xYkUlJSWlut4yo7L6fpekJk2aqGPHjho9erQCAgLUrl07zZs3Tzdv3rzntI0bNzYPPdy0aZNq166t2rVrq0CBAtq0aZNiY2P1xx9/3HdRkZHPQlqSkpI0efJklS1bVh4eHgoICFBgYKD27t2b6vORkfmk9352c3OzKK7T4+rqqvr161uss8aNG6tRo0ZKTEzUtm3btH//fl28eNHm6yxlWdJ6P1aoUCFL+7qsKFeunD777DOdP39ee/fu1TvvvKM8efLomWee0U8//ZTluHcuf0pRdefwUX9//zTfR5mZXrJ8L+7bt08dOnSQv7+//Pz8FBgYqG7duklKvV9O729wjx49dOPGDfP1tAcPHtTOnTszPcQduQPXiAFWdvLkScXExKR5UJzCy8tLv/zyizZs2KCVK1dq9erV+uKLL9SsWTOtWbNGrq6u95xPZq7ryqj0vvlOTEzMUE7WkN58jDtu7GEPFSpUkCT9+eefFmea7uVey3D48GE1b95cFSpU0KRJk1SsWDG5u7vrhx9+0OTJk1MV5NZcJymxP/vsMwUHB6d63VF3MLzTvZY5ZTm6deumnj17ptn3bte4ZHXb3svdPkNpuZ9tm/Ljv9u2bdN3332nH3/8Ub1799bEiRO1bdu2VNce3a5Ro0aaM2eOjhw5Yi4qTCaTGjVqpE2bNqlIkSJKSkq676Iiq8v3zjvv6M0331Tv3r01duxYFShQQC4uLnrppZfS/MLKHvuNRo0a6e2331ZcXJw2bdqkN954Q/ny5VOVKlW0adMm83W6jlpnaTGZTGlOl977MStcXV1VtWpVVa1aVfXr19fDDz+sRYsWqUWLFnedLrOfibTa01q2zEx/e4zLly+rSZMm8vPz05gxY8y/j7Zr1y699tprqd536f0NrlSpkmrVqqWFCxeqR48eWrhwodzd3fXkk0+m2R+5W/b4iwvkICkXrYeFhd21n4uLi5o3b67mzZtr0qRJeuedd/TGG29ow4YNatGixX0NB0pLypmQFIZh6NChQxYHq/nz59fly5dTTXv8+HGLb40zk1vKRd1XrlyxOCuWMswjrbtuZUWJEiW0d+9eJSUlWZwVu5/5PPbYY3J1ddXChQut+m3md999p5s3b2rFihUW396mDAvMihIlSqR5Z7U721KG+xUqVOiuB0op6+vO942kbPE7cYGBgcqbN68SExPvecCXlsxs28DAQHl7e6e53H///bdcXFzM37annL24fPmyxQ0T7ucsxb0+b/Xq1VO9evX09ttva/HixeratauWLFmiPn36pDtNSrGwdu1abd++Xa+//rqk5BtzzJgxQ0WKFJGPj89df2MvI7ll1VdffaWHH35Yn3zyiUX75cuXFRAQkOl4t7+fb7+5REJCgo4ePapq1ardM0bjxo0VHx+vzz//XFFRUeZ1+NBDD5kLsXLlyt3zxkn3u85SluXgwYOpbpRx8OBBi31d/vz50xy+d+f70VrbMeUnXU6fPm2Rw51/V+Lj4y36ZAcbN27UhQsXtGzZMosb1Bw9ejTTsXr06KFBgwbp9OnTWrx4sVq3bm0xRBdIwdBEwIrWr1+vsWPHKjQ0NNW1Obe785a5kszfyqcMK/Lx8ZGkNAujrFiwYIHFMKyvvvpKp0+f1mOPPWZuK126tLZt26b4+Hhz2/fff59q2FVmcmvVqpUSExP14YcfWrRPnjxZJpPJYv73o1WrVjpz5oy++OILc9utW7f0wQcfyNfXV02aNMl0zGLFiqlv375as2aNPvjgg1SvJyUlaeLEiZn+0d+Ub2Zv/zY3JiZG8+bNy3SOKcLCwrR161aLH9m+ePGiFi1alKqfn5+f3nnnnTSvKzp37pyk5Oufqlevrk8//dRiSM7atWu1f//+LOdpLa6ururYsaO+/vpr/fXXX6leT1mO9GRm27q6uurRRx/Vt99+azGUNzo6WosXL1ajRo3MQx1TCt2Ua4mk5J9vuP1nFDIrvc/bpUuXUp0RuHM/kp7Q0FCFhIRo8uTJSkhIMN/lr3Hjxjp8+LC++uor1atX755nSK29n0rh6uqaatmWLl2a6vbsGVW7dm0FBgZq5syZFvu3+fPnZzj3unXrys3NTePHj1eBAgXMdyxt3Lixtm3bpp9//jlDZ8N8fHzSHF6ZUbVr11ahQoU0c+ZMi+28atUqHThwQK1btza3lS5dWn///bfF5+GPP/4w36E3RcrvX2V0XWzatCnN/UfKNce3D5ssXbq0xedBSv4ZAWuelbOGtPbL8fHx+uijjzIdq0uXLjKZTHrxxRd15MgR8/BG4E6cEQOyaNWqVfr7779169YtRUdHa/369Vq7dq1KlCihFStW3PVHPMeMGaNffvlFrVu3VokSJXT27Fl99NFHKlq0qBo1aiQp+Y9Xvnz5NHPmTOXNm1c+Pj6qW7duli/2LVCggBo1aqSIiAhFR0drypQpKlOmjMUt9vv06aOvvvpKLVu21JNPPqnDhw9r4cKFFjdNyGxu4eHhevjhh/XGG2/o2LFjqlatmtasWaNvv/1WL730UqrYWfXMM89o1qxZ6tWrl3bu3KmSJUvqq6++0ubNmzVlypRM33AjxcSJE3X48GG98MILWrZsmdq0aaP8+fPrxIkTWrp0qf7++2899dRTmYr56KOPyt3dXeHh4Xr22Wd19epVzZkzR4UKFcryt8SvvvqqFi5cqEceeUQDBw40376+ePHiunjxovkbbz8/P82YMUPdu3dXzZo19dRTTykwMFAnTpzQypUr1bBhQ3PRPG7cOLVu3VqNGjVS7969dfHiRX3wwQeqXLmyrl69mqG8zp07p7feeitV+72+rMiId999Vxs2bFDdunXVt29fVapUSRcvXtSuXbv0008/pfmFx+0ys23feust82//9evXT3ny5NGsWbN08+ZNvffee+aYjz76qIoXL67IyEgNGTJErq6umjt3rnkdZ0X16tXl6uqq8ePHKyYmRh4eHmrWrJkWL16sjz76SB06dFDp0qV15coVzZkzR35+fmrVqtU94zZu3FhLlixR1apVzd/W16xZUz4+Pvrnn3/09NNP3zNGyhmzF154QWFhYXJ1dc305yEtbdq00ZgxYxQREaEGDRrozz//1KJFizJ0PVda3Nzc9NZbb+nZZ59Vs2bN1LlzZx09elTz5s3LcExvb2/VqlVL27ZtM/+GmJR8RuzatWu6du1ahgqxWrVq6YsvvtCgQYP04IMPytfXV+Hh4ZlalvHjxysiIkJNmjRRly5dzLevL1mypF5++WVz3969e2vSpEkKCwtTZGSkzp49q5kzZ6py5cqpbh5UqVIlffHFFypXrpwKFCigKlWqmH/e4E7jx4/Xzp079fjjj5tHVezatUsLFixQgQIFLH46ok+fPnruuefUsWNHPfLII/rjjz/0448/ZunMpi01aNBA+fPnV8+ePfXCCy/IZDLps88+y9KQ0MDAQLVs2VJLly5Vvnz5LIpjwII9b9EI5AQpt9hN+efu7m4EBwcbjzzyiDF16lSL26SnuPOW1uvWrTPatWtnFClSxHB3dzeKFClidOnSxfjnn38spvv222+NSpUqGXny5LG4bfTdbgue3u3rP//8c2Po0KFGoUKFDC8vL6N169bG8ePHU00/ceJEIyQkxPDw8DAaNmxo7NixI81bIKeX2523RTaM5Fumv/zyy0aRIkUMNzc3o2zZssaECRMsblFsGMm3Oe7fv3+qnNK7rf6doqOjjYiICCMgIMBwd3c3qlatmuYt9jN6+/oUt27dMj7++GOjcePGhr+/v+Hm5maUKFHCiIiIsLj9ecp2vvNnCdK6dfmKFSuMBx54wPD09DRKlixpjB8/3pg7d26atzhPK9e0tsnu3buNxo0bGx4eHkbRokWNcePGGdOmTTMkGWfOnLHou2HDBiMsLMzw9/c3PD09jdKlSxu9evUyduzYYdHv66+/NipWrGh4eHgYlSpVMpYtW5bmNk5Lyi2e0/rXvHlzi3V2u8y8D6Kjo43+/fsbxYoVM9zc3Izg4GCjefPmxuzZs++Zn2FkfNsahmHs2rXLCAsLM3x9fQ1vb2/j4YcfNrZs2ZIq5s6dO426desa7u7uRvHixY1Jkyale/v6jG7bOXPmGKVKlTJcXV3Nt0PftWuX0aVLF6N48eKGh4eHUahQIaNNmzaptmF6pk+fbkgynn/+eYv2Fi1aGJKMdevWWbSndfv6W7duGQMHDjQCAwMNk8lk3pYpfSdMmJBqvsrAbdLj4uKMV155xShcuLDh5eVlNGzY0Ni6dWu6+7elS5feM1fDMIyPPvrICA0NNTw8PIzatWsbv/zyS7q3eE/LkCFDDEnG+PHjLdrLlCljSLL4eYPb87v99vVXr141nn76aSNfvnwWPwWR2WX54osvjBo1ahgeHh5GgQIFjK5duxonT55MlfPChQuNUqVKGe7u7kb16tWNH3/8Mc3P8JYtW4xatWoZ7u7u99xGmzdvNvr3729UqVLF/LkpXry40atXr1TrIDEx0XjttdeMgIAAw9vb2wgLCzMOHTqU7u3r7/w5hvT2qz179jR8fHxSrac733Pprde05rd582ajXr16hpeXl1GkSBHj1VdfNX788cdU2zAjP82R8hMxzzzzzF37IXczGYYDroAHANjFSy+9pFmzZunq1at2u+EKAOR23377rdq3b69ffvnlvm/ggpyLQgwAcogbN25Y3MnrwoULKleunGrWrKm1a9c6MDMAyF3atGmjAwcO6NChQza7qQ2cH9eIAUAOUb9+fTVt2lQVK1ZUdHS0PvnkE8XGxurNN990dGoAkCssWbJEe/fu1cqVKzV16lSKMNwVZ8QAIIcYNmyYvvrqK508eVImk0k1a9bUyJEjs3R7dwBA5plMJvn6+qpz586aOXNmtvltRmRPFGIAAAAAYGf8jhgAAAAA2BmFGAAAAADYGQNXrSApKUmnTp1S3rx5uSgTAAAAyMUMw9CVK1dUpEgRubikf96LQswKTp06pWLFijk6DQAAAADZxH///aeiRYum+zqFmBXkzZtXknTixAn5+/tbJaZhGIqJiZG/v3/mzrJ9V0G6cVryKiyF/31/sayRDxwqt283Z13+7JS3I3Kx9TxtFd9acdln515sN+dcB9kt59y4365QQTp9WipcWPr7b+vEvJ/8YmNjVaxYMXONkB4KMStI2Xh+fn7y8/OzSkzDMGQYhvz8/DL35vB2kUySvFyk/88ly7GskQ8cKrdvN2dd/uyUtyNysfU8bRXfWnHZZ+debDfnXAfZLefcuN9OGf3n8r/D3/uOaY387hWXm3UAAAAAgJ1RiAEAAACAnVGIAQAAAICdcY1YTtNyu2QkSiZXR2cCAAAA2Nz27VJiouTqZIe/FGI5jVdhR2cAAAAA2E1hJz38ZWgiAAAAANgZhRgAAAAA2BlDE3OaQ7OlhKuSm69U5hlHZwMAAADY1OzZ0tWrkq+v9IwTHf5SiOU0f46RbkRJXiEUYgAAAMjxxoyRoqKkkBDnKsQYmggAAAAAdkYhBgAAAAB2RiEGAAAAAHZGIQYAAAAAdkYhBgAAAAB2RiEGAAAAAHZGIQYAAAAAdkYhBgAAAAB2xg865zR+5SR3f8kzyNGZAAAAADZXrpzk7y8FOdnhL4VYTtN8vaMzAAAAAOxmvZMe/jI0EQAAAADsjEIMAAAAAOyMQgwAAAAA7IxrxHKazV2lm+cljwCp4SJHZwMAAADYVNeu0vnzUkCAtMiJDn8pxHKasz9LN6IkrxBHZwIAAADY3M8/S1FRUoiTHf4yNBEAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjB90zmnK9JXiYyR3f0dnAgAAANhc375STIzk72SHvxRiOU3VkY7OAAAAALCbkU56+MvQRAAAAACwMwoxAAAAALAzCjEAAAAAsDMKsZzmm6LSYlPyIwAAAJDDFS0qmUzJj86EQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjEIMAAAAAOwsj6MTgJU1WCgl3pRcPRydCQAAAGBzCxdKN29KHk52+EshltMENXV0BgAAAIDdNG3q6AyyhqGJAAAAAGBnFGIAAAAAYGcMTcxpojf+7xoxhikCAAAgh9u48X/XiDnTMEUKsZxmSzfpRpTkFSJ1OOnobAAAAACb6tZNioqSQkKkk050+MvQRAAAAACwMwoxAAAAALAzCjEAAAAAsDOnK8SmT5+ukiVLytPTU3Xr1tXvv/9+1/5Lly5VhQoV5OnpqapVq+qHH35It+9zzz0nk8mkKVOmWDlrAAAAAPgfpyrEvvjiCw0aNEgjR47Url27VK1aNYWFhens2bNp9t+yZYu6dOmiyMhI7d69W+3bt1f79u31119/per7zTffaNu2bSpSpIitFwMAAABALudUhdikSZPUt29fRUREqFKlSpo5c6a8vb01d+7cNPtPnTpVLVu21JAhQ1SxYkWNHTtWNWvW1IcffmjRLyoqSgMHDtSiRYvk5uZmj0UBAAAAkIs5ze3r4+PjtXPnTg0dOtTc5uLiohYtWmjr1q1pTrN161YNGjTIoi0sLEzLly83P09KSlL37t01ZMgQVa5cOUO53Lx5Uzdv3jQ/j42NlSQZhiHDMDK6SHeVEisr8UySjOQg9x3LGvnAcXL7dnPW5c9OeTsiF1vP01bxrRWXfXbuxXZzznWQ3XLOvfvt5CPgjKZgzZzvjJXRmE5TiJ0/f16JiYkKCgqyaA8KCtLff/+d5jRnzpxJs/+ZM2fMz8ePH688efLohRdeyHAu48aN0+jRo1O1x8TEZDjGvRiGoatXr0qSTCZThqfzM4zkt6FhKPb/88lqLGvkA8fK7dvNWZc/O+XtiFxsPU9bxbdWXPbZuRfbzTnXQXbLOTfutw3DT5JJhmEoJibWKjHvJ7+UkzT34jSFmC3s3LlTU6dO1a5duzK1AYYOHWpxpi02NlbFihWTv7+//P39rZJbSiXt7++fuTfH//c1mUzmXLIcyxr5wKFy+3Zz1uXPTnk7Ihdbz9NW8a0Vl3127sV2c851kN1yzo377ZSm249/7zfm/eSX0XhOU4gFBATI1dVV0dHRFu3R0dEKDg5Oc5rg4OC79t+0aZPOnj2r4sWLm19PTEzUK6+8oilTpujYsWNpxvXw8JCHh0eq9sys+IxIiZepmB2Sf078zimyFMsGMWB/uX27OevyZ6e8HZGLredpq/jWiss+O/diuznnOshuOee2/fbJk+ZeVot5P/llNJ7T3KzD3d1dtWrV0rp168xtSUlJWrdunerXr5/mNPXr17foL0lr16419+/evbv27t2rPXv2mP8VKVJEQ4YM0Y8//mi7hQEAAACQqznNGTFJGjRokHr27KnatWurTp06mjJliq5du6aIiAhJUo8ePRQSEqJx48ZJkl588UU1adJEEydOVOvWrbVkyRLt2LFDs2fPliQVLFhQBQsWtJiHm5ubgoODVb58efsuHAAAAIBcw6kKsc6dO+vcuXMaMWKEzpw5o+rVq2v16tXmG3KcOHFCLi7/O8nXoEEDLV68WMOHD9ewYcNUtmxZLV++XFWqVHHUIgAAAACAcxVikjRgwAANGDAgzdc2btyYqq1Tp07q1KlThuOnd12Y0/hztBQfI7n7S1VHOjobAAAAwKZGj5ZiYiR/f2mkEx3+Ol0hhns4NEe6ESV5hVCIAQAAIMebM0eKipJCQpyrEHOam3UAAAAAQE5BIQYAAAAAdkYhBgAAAAB2RiEGAAAAAHZGIQYAAAAAdkYhBgAAAAB2RiEGAAAAAHZGIQYAAAAAdsYPOuc0hZpIN89LHgGOzgQAAACwuSZNpPPnpQAnO/ylEMtpGi5ydAYAAACA3Sxy0sNfhiYCAAAAgJ1RiAEAAACAnVGIAQAAAICdcY1YTrOumRQXLXkGSc3XOzobAAAAwKaaNZOio6WgIGm9Ex3+UojlNLH/SDeipPgYR2cCAAAA2Nw//0hRUVKMkx3+MjQRAAAAAOyMQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IwfdM5pqo6QEq5Kbr6OzgQAAACwuREjpKtXJV8nO/ylEMtpyjzj6AwAAAAAu3nGSQ9/GZoIAAAAAHZGIQYAAAAAdsbQxJzmxmnJSJRMrpJXYUdnAwAAANjU6dNSYqLk6ioVdqLDXwqxnGb1g9KNKMkrROpw0tHZAAAAADb14INSVJQUEiKddKLDX4YmAgAAAICdUYgBAAAAgJ1RiAEAAACAnVGIAQAAAICdUYgBAAAAgJ1RiAEAAACAnVGIAQAAAICdUYgBAAAAgJ1RiAEAAACAneVxdAKwsubrpKRbkgubFgAAADnfunXSrVtSHic7/HWydHFPfuUdnQEAAABgN+Wd9PCXoYkAAAAAYGcUYgAAAABgZwxNzGmOLZZuXZfyeEsln3Z0NgAAAIBNLV4sXb8ueXtLTzvR4S+FWE6z+1XpRpTkFUIhBgAAgBzv1VelqCgpJMS5CjGGJgIAAACAnVGIAQAAAICdUYgBAAAAgJ1RiAEAAACAnVGIAQAAAICdUYgBAAAAgJ1RiAEAAACAnVGIAQAAAICd8YPOOY1XsOUjAAAAkIMFB1s+OgsKsZym5Q5HZwAAAADYzQ4nPfxlaCIAAAAA2BmFGAAAAADYGYUYAAAAANgZ14jlNL8/K928KHkUkOrMcnQ2AAAAgE09+6x08aJUoIA0y4kOfynEcpqoldKNKMkrxNGZAAAAADa3cqUUFSWFONnhL0MTAQAAAMDOKMQAAAAAwM4oxAAAAADAzijEAAAAAMDOKMQAAAAAwM4oxAAAAADAzijEAAAAAMDOKMQAAAAAwM74QeecpmQXKf6S5J7f0ZkAAAAANteli3TpkpTfyQ5/KcRymhoTHJ0BAAAAYDcTnPTwl6GJAAAAAGBnFGIAAAAAYGcUYgAAAABgZ05XiE2fPl0lS5aUp6en6tatq99///2u/ZcuXaoKFSrI09NTVatW1Q8//GB+LSEhQa+99pqqVq0qHx8fFSlSRD169NCpU6dsvRi2830F6Uu/5EcAAAAgh6tQQfLzS350Jk5ViH3xxRcaNGiQRo4cqV27dqlatWoKCwvT2bNn0+y/ZcsWdenSRZGRkdq9e7fat2+v9u3b66+//pIkXb9+Xbt27dKbb76pXbt2admyZTp48KDatm1rz8WyroSr0q0ryY8AAABADnf1qnTlSvKjM3GqQmzSpEnq27evIiIiVKlSJc2cOVPe3t6aO3dumv2nTp2qli1basiQIapYsaLGjh2rmjVr6sMPP5Qk+fv7a+3atXryySdVvnx51atXTx9++KF27typEydO2HPRAAAAAOQiTnP7+vj4eO3cuVNDhw41t7m4uKhFixbaunVrmtNs3bpVgwYNsmgLCwvT8uXL051PTEyMTCaT8uXLl26fmzdv6ubNm+bnsbGxkiTDMGQYRgaW5t5SYmUlnkmSkRzkvmNZIx84Tm7fbs66/Nkpb0fkYut52iq+teKyz8692G7OuQ6yW865d7+dfASc0RSsmfOdsTIa02kKsfPnzysxMVFBQUEW7UFBQfr777/TnObMmTNp9j9z5kya/ePi4vTaa6+pS5cu8vPzSzeXcePGafTo0anaY2Ji7rUYGWYYhq7+//lVk8mU4en8DCP5bWgYiv3/fLIayxr5wLFy+3Zz1uXPTnk7Ihdbz9NW8a0Vl3127sV2c851kN1yzo37bcPwk2SSYRiKiYm1Ssz7yS/lJM29OE0hZmsJCQl68sknZRiGZsyYcde+Q4cOtTjTFhsbq2LFisnf31/+/v5WySelkvb398/cm+P/+5pMJnMuWY5ljXzgULl9uznr8menvB2Ri63naav41orLPjv3Yrs55zrIbjnnxv12StPtx7/3G/N+8stoPKcpxAICAuTq6qro6GiL9ujoaAUHB6c5TXBwcIb6pxRhx48f1/r16+96NkySPDw85OHhkao9Mys+I1LiZSWmKTmAVWJZMwbsL7dvN2dd/uyUtyNysfU8bRXfWnHZZ+debDfnXAfZLefcu982KTOzt2bOt8fKaDynuVmHu7u7atWqpXXr1pnbkpKStG7dOtWvXz/NaerXr2/RX5LWrl1r0T+lCPv333/1008/qWDBgrZZAAAAAAD4f05zRkySBg0apJ49e6p27dqqU6eOpkyZomvXrikiIkKS1KNHD4WEhGjcuHGSpBdffFFNmjTRxIkT1bp1ay1ZskQ7duzQ7NmzJSUXYU888YR27dql77//XomJiebrxwoUKCB3d3fHLCgAAACAHM2pCrHOnTvr3LlzGjFihM6cOaPq1atr9erV5htynDhxQi4u/zvJ16BBAy1evFjDhw/XsGHDVLZsWS1fvlxVqlSRJEVFRWnFihWSpOrVq1vMa8OGDWratKldlgsAAABA7uJUhZgkDRgwQAMGDEjztY0bN6Zq69Spkzp16pRm/5IlS2abW41aTZ2ZUuINydXL0ZkAAAAANjdzpnTjhuTlZIe/TleI4R5C2jg6AwAAAMBu2jjp4a/T3KwDAAAAAHIKCjEAAAAAsDOGJuY0F3dKifGSq7tUoJajswEAAABsaudOKT5ecneXajnR4S+FWE7zczvpRpTkFSJ1OOnobAAAAACbatdOioqSQkKkk050+MvQRAAAAACwMwoxAAAAALAzCjEAAAAAsDMKMQAAAACwMwoxAAAAALAzCjEAAAAAsDMKMQAAAACwMwoxAAAAALAzCjEAAAAAsLM8jk4AVtbmgCRDksnRmQAAAAA2d+CAZBiSyckOfynEchq3vI7OAAAAALCbvE56+MvQRAAAAACwMwoxAAAAALAzhibmNAcmSQmxkpufVHGQo7MBAAAAbGrSJCk2VvLzkwY50eEvhVhO8/ck6UaU5BVCIQYAAIAcb9IkKSpKCglxrkKMoYkAAAAAYGcUYgAAAABgZxRiAAAAAGBnFGIAAAAAYGcUYgAAAABgZxRiAAAAAGBnFGIAAAAAYGcUYgAAAABgZ/ygc05ToKYUV0zyDHR0JgAAAIDN1awpFSsmBTrZ4S+FWE7TZIWjMwAAAADsZoWTHv4yNBEAAAAA7IxCDAAAAADsjEIMAAAAAOyMa8Rymp/bSnHnkm/WwfViAAAAyOHatpXOnUu+WYczXS9GIZbTXNwl3YiSvEIcnQkAAABgc7t2SVFRUoiTHf4yNBEAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjB90zmkqDJISYiU3P0dnAgAAANjcoEFSbKzk52SHv1kqxI4cOaJSpUpZOxdYQ8VBjs4AAAAAsJtBTnr4m6WhiWXKlNHDDz+shQsXKi4uzto5AQAAAECOlqVCbNeuXXrggQc0aNAgBQcH69lnn9Xvv/9u7dwAAAAAIEfKUiFWvXp1TZ06VadOndLcuXN1+vRpNWrUSFWqVNGkSZN07tw5a+eJjEq4knyNWMIVR2cCAAAA2NyVK8nXiF1xssPf+7prYp48efT4449r6dKlGj9+vA4dOqTBgwerWLFi6tGjh06fPm2tPJFR31eUlvonPwIAAAA5XMWKkr9/8qMzua9CbMeOHerXr58KFy6sSZMmafDgwTp8+LDWrl2rU6dOqV27dtbKEwAAAAByjCzdNXHSpEmaN2+eDh48qFatWmnBggVq1aqVXFyS67rQ0FDNnz9fJUuWtGauAAAAAJAjZKkQmzFjhnr37q1evXqpcOHCafYpVKiQPvnkk/tKDgAAAAByoiwVYv/+++89+7i7u6tnz55ZCQ8AAAAAOVqWrhGbN2+eli5dmqp96dKl+vTTT+87KQAAAADIybJUiI0bN04BAQGp2gsVKqR33nnnvpMCAAAAgJwsS4XYiRMnFBoamqq9RIkSOnHixH0nBQAAAAA5WZYKsUKFCmnv3r2p2v/44w8VLFjwvpMCAAAAgJwsS4VYly5d9MILL2jDhg1KTExUYmKi1q9frxdffFFPPfWUtXMEAAAAgBwlS3dNHDt2rI4dO6bmzZsrT57kEElJSerRowfXiDlak2+lxHjJ1d3RmQAAAAA29+23Uny85O5kh79ZKsTc3d31xRdfaOzYsfrjjz/k5eWlqlWrqkSJEtbOD5lVoJajMwAAAADsppaTHv5mqRBLUa5cOZUrV85auQAAAABArpClQiwxMVHz58/XunXrdPbsWSUlJVm8vn79eqskBwAAAAA5UZYKsRdffFHz589X69atVaVKFZlMJmvnhayK+l5KvCG5ekkhbRydDQAAAGBT338v3bgheXlJbZzo8DdLhdiSJUv05ZdfqlWrVtbOB/fr9+ekG1GSV4jU4aSjswEAAABs6rnnpKgoKSREOulEh79Zun29u7u7ypQpY+1cAAAAACBXyFIh9sorr2jq1KkyDMPa+QAAAABAjpeloYm//vqrNmzYoFWrVqly5cpyc3OzeH3ZsmVWSQ4AAAAAcqIsFWL58uVThw4drJ0LAAAAAOQKWSrE5s2bZ+08AAAAACDXyNI1YpJ069Yt/fTTT5o1a5auXLkiSTp16pSuXr1qteQAAAAAICfK0hmx48ePq2XLljpx4oRu3rypRx55RHnz5tX48eN18+ZNzZw509p5AgAAAECOkaUzYi+++KJq166tS5cuycvLy9zeoUMHrVu3zmrJAQAAAEBOlKUzYps2bdKWLVvk7u5u0V6yZElFRUVZJTFkkZuvlJA3+REAAADI4Xx9pbx5kx+dSZbOiCUlJSkxMTFV+8mTJ5U3b977Tupupk+frpIlS8rT01N169bV77//ftf+S5cuVYUKFeTp6amqVavqhx9+sHjdMAyNGDFChQsXlpeXl1q0aKF///3XlotgW23+lp6MTX4EAAAAcri//5ZiY5MfnUmWCrFHH31UU6ZMMT83mUy6evWqRo4cqVatWlkrt1S++OILDRo0SCNHjtSuXbtUrVo1hYWF6ezZs2n237Jli7p06aLIyEjt3r1b7du3V/v27fXXX3+Z+7z33nuaNm2aZs6cqd9++00+Pj4KCwtTXFyczZYDAAAAQO6WpUJs4sSJ2rx5sypVqqS4uDg9/fTT5mGJ48ePt3aOZpMmTVLfvn0VERGhSpUqaebMmfL29tbcuXPT7D916lS1bNlSQ4YMUcWKFTV27FjVrFlTH374oaTks2FTpkzR8OHD1a5dOz3wwANasGCBTp06peXLl9tsOQAAAADkblm6Rqxo0aL6448/tGTJEu3du1dXr15VZGSkunbtanHzDmuKj4/Xzp07NXToUHObi4uLWrRooa1bt6Y5zdatWzVo0CCLtrCwMHORdfToUZ05c0YtWrQwv+7v76+6detq69ateuqpp9KMe/PmTd28edP8PDY2VlJyYWcYRpaW704psawRzxqxrJkP7Ce3bzdnXf7slLcjcrH1PG0V31px2WfnXmw351wH2S1n9tv2j3lnrIzGzFIhJkl58uRRt27dsjp5pp0/f16JiYkKCgqyaA8KCtLf6QwIPXPmTJr9z5w5Y349pS29PmkZN26cRo8enao9Jibm3guSQYZhmH+TzWQyZXg6zwNvypQQI8PNX3EVx95XLGvkA8fK7dvNWZc/O+XtiFxsPU9bxbdWXPbZuRfbzTnXQXbLOTfut99801MxMSb5+xsaOzZjlxdZM+c7Y6WcpLmXLBViCxYsuOvrPXr0yEpYpzF06FCLM22xsbEqVqyY/P395e/vb5V5pFTS/v7+mXtznPlGphtRMrxC5FFv2v3FskY+cKjcvt2cdfmzU96OyMXW87RVfGvFZZ+de7HdnHMdZLecc+N++5tvpKgok0JCDE2b5mGVmPeTX0bjZakQe/HFFy2eJyQk6Pr163J3d5e3t7dNCrGAgAC5uroqOjraoj06OlrBwcFpThMcHHzX/imP0dHRKly4sEWf6tWrp5uLh4eHPDxSb+TMrPiMSImXlZim5ABWiWXNGLC/3L7dnHX5s1PejsjF1vO0VXxrxWWfnXux3ZxzHWS3nHPvftukzMzemjnfHiuj8bJ0s45Lly5Z/Lt69aoOHjyoRo0a6fPPP89KyHtyd3dXrVq1LH4wOikpSevWrVP9+vXTnKZ+/fqpfmB67dq15v6hoaEKDg626BMbG6vffvst3ZgAAAAAcL+yfI3YncqWLat3331X3bp1S/earfs1aNAg9ezZU7Vr11adOnU0ZcoUXbt2TREREZKSh0SGhIRo3LhxkpLP3DVp0kQTJ05U69attWTJEu3YsUOzZ8+WlFy5vvTSS3rrrbdUtmxZhYaG6s0331SRIkXUvn17mywDAAAAAFitEJOSb+Bx6tQpa4a00LlzZ507d04jRozQmTNnVL16da1evdp8s40TJ07IxeV/J/kaNGigxYsXa/jw4Ro2bJjKli2r5cuXq0qVKuY+r776qq5du6ZnnnlGly9fVqNGjbR69Wp5enrabDkAAAAA5G5ZKsRWrFhh8dwwDJ0+fVoffvihGjZsaJXE0jNgwAANGDAgzdc2btyYqq1Tp07q1KlTuvFMJpPGjBmjMWPGWCtFAAAAALirLBVidw7bM5lMCgwMVLNmzTRx4kRr5AUAAAAAOVaWCrGkpCRr5wEAAAAAuUaW7poIAAAAAMi6LJ0Ru/3HjO9l0qRJWZkFsiqktXTzouRRwNGZAAAAADbXurV08aJUwMkOf7NUiO3evVu7d+9WQkKCypcvL0n6559/5Orqqpo1a5r7ZZcftctV6sxydAYAAACA3cxy0sPfLBVi4eHhyps3rz799FPlz59fUvKPPEdERKhx48Z65ZVXrJokAAAAAOQkWbpGbOLEiRo3bpy5CJOk/Pnz66233uKuiQAAAABwD1kqxGJjY3Xu3LlU7efOndOVK1fuOykAAAAAyMmyVIh16NBBERERWrZsmU6ePKmTJ0/q66+/VmRkpB5//HFr54jMWF1b+qZo8iMAAACQw9WuLRUtmvzoTLJ0jdjMmTM1ePBgPf3000pISEgOlCePIiMjNWHCBKsmiEy6cUa6EeXoLAAAAAC7OHNGinLCw98sFWLe3t766KOPNGHCBB0+fFiSVLp0afn4+Fg1OQAAAADIie7rB51Pnz6t06dPq2zZsvLx8ZFhGNbKCwAAAAByrCwVYhcuXFDz5s1Vrlw5tWrVSqdPn5YkRUZGcut6AAAAALiHLBViL7/8stzc3HTixAl5e3ub2zt37qzVq1dbLTkAAAAAyImydI3YmjVr9OOPP6po0aIW7WXLltXx48etkhgAAAAA5FRZOiN27do1izNhKS5evCgPD4/7TgoAAAAAcrIsFWKNGzfWggULzM9NJpOSkpL03nvv6eGHH7ZacgAAAACQE2VpaOJ7772n5s2ba8eOHYqPj9err76qffv26eLFi9q8ebO1cwQAAACAHCVLhViVKlX0zz//6MMPP1TevHl19epVPf744+rfv78KFy5s7RyRGTXek25dl/KkHjoKAAAA5DTvvSddvy6lceVUtpbpQiwhIUEtW7bUzJkz9cYbb9giJ9yPkk87OgMAAADAbp520sPfTF8j5ubmpr1799oiFwAAAADIFbJ0s45u3brpk08+sXYuAAAAAJArZOkasVu3bmnu3Ln66aefVKtWLfn4+Fi8PmnSJKskhyyIPSgl3ZJc8kh+5R2dDQAAAGBTBw9Kt25JefJI5Z3o8DdThdiRI0dUsmRJ/fXXX6pZs6Yk6Z9//rHoYzKZrJcdMm9dc+lGlOQVInU46ehsAAAAAJtq3lyKipJCQqSTTnT4m6lCrGzZsjp9+rQ2bNggSercubOmTZumoKAgmyQHAAAAADlRpq4RMwzD4vmqVat07do1qyYEAAAAADldlm7WkeLOwgwAAAAAcG+ZKsRMJlOqa8C4JgwAAAAAMidT14gZhqFevXrJw8NDkhQXF6fnnnsu1V0Tly1bZr0MAQAAACCHyVQh1rNnT4vn3bp1s2oyAAAAAJAbZKoQmzdvnq3yAAAAAIBc475u1gEAAAAAyDwKMQAAAACws0wNTYQTaLldMhIlk6ujMwEAAABsbvt2KTFRcnWyw18KsZzGq7CjMwAAAADsprCTHv4yNBEAAAAA7IxCDAAAAADsjKGJOc2h2VLCVcnNVyrzjKOzAQAAAGxq9mzp6lXJ11d6xokOfynEcpo/x0g3oiSvEAoxAAAA5HhjxkhRUVJIiHMVYgxNBAAAAAA7oxADAAAAADujEAMAAAAAO6MQAwAAAAA7oxADAAAAADujEAMAAAAAO6MQAwAAAAA7oxADAAAAADvjB51zGr9ykru/5Bnk6EwAAAAAmytXTvL3l4Kc7PCXQiynab7e0RkAAAAAdrPeSQ9/GZoIAAAAAHZGIQYAAAAAdkYhBgAAAAB2xjViOc3mrtLN85JHgNRwkaOzAQAAAGyqa1fp/HkpIEBa5ESHvxRiOc3Zn6UbUZJXiKMzAQAAAGzu55+lqCgpxMkOfxmaCAAAAAB2RiEGAAAAAHZGIQYAAAAAdkYhBgAAAAB2RiEGAAAAAHZGIQYAAAAAdkYhBgAAAAB2RiEGAAAAAHbGDzrnNGX6SvExkru/ozMBAAAAbK5vXykmRvJ3ssNfCrGcpupIR2cAAAAA2M1IJz38ZWgiAAAAANgZhRgAAAAA2BmFGAAAAADYGYVYTvNNUWmxKfkRAAAAyOGKFpVMpuRHZ0IhBgAAAAB2RiEGAAAAAHZGIQYAAAAAdkYhBgAAAAB2RiEGAAAAAHbmNIXYxYsX1bVrV/n5+SlfvnyKjIzU1atX7zpNXFyc+vfvr4IFC8rX11cdO3ZUdHS0+fU//vhDXbp0UbFixeTl5aWKFStq6tSptl4UAAAAALmc0xRiXbt21b59+7R27Vp9//33+uWXX/TMM8/cdZqXX35Z3333nZYuXaqff/5Zp06d0uOPP25+fefOnSpUqJAWLlyoffv26Y033tDQoUP14Ycf2npxAAAAAORieRydQEYcOHBAq1ev1vbt21W7dm1J0gcffKBWrVrp/fffV5EiRVJNExMTo08++USLFy9Ws2bNJEnz5s1TxYoVtW3bNtWrV0+9e/e2mKZUqVLaunWrli1bpgEDBth+wQAAAADkSk5RiG3dulX58uUzF2GS1KJFC7m4uOi3335Thw4dUk2zc+dOJSQkqEWLFua2ChUqqHjx4tq6davq1auX5rxiYmJUoECBu+Zz8+ZN3bx50/w8NjZWkmQYhgzDyNSypSclVlbimSQZyUHuO5Y18oHj5Pbt5qzLn53ydkQutp6nreJbKy777NyL7eac6yC75Zx799vJR8AZTcGaOd8ZK6MxnaIQO3PmjAoVKmTRlidPHhUoUEBnzpxJdxp3d3fly5fPoj0oKCjdabZs2aIvvvhCK1euvGs+48aN0+jRo1O1x8TE3HW6zDAMw3wNnMlkyvB0eR6YKSXdlFw8dOv/88lqLGvkA8fK7dvNWZc/O+XtiFxsPU9bxbdWXPbZuRfbzTnXQXbLOTfut2fOzKObNyUPDykm5pZVYt5Pfiknae7FoYXY66+/rvHjx9+1z4EDB+ySy19//aV27dpp5MiRevTRR+/ad+jQoRo0aJD5eWxsrIoVKyZ/f3/5+/tbJZ+UStrf3z9zbw7/1taLZeUYsL/cvt2cdfmzU96OyMXW87RVfGvFZZ+de7HdnHMdZLecc+N+u3Xqw9/7jnk/sTIaz6GF2CuvvKJevXrdtU+pUqUUHByss2fPWrTfunVLFy9eVHBwcJrTBQcHKz4+XpcvX7Y4KxYdHZ1qmv3796t58+Z65plnNHz48Hvm7eHhIQ8Pj1TtmVnxGZESzxoxrRHLmvnAfnL7dnPW5c9OeTsiF1vP01bxrRWXfXbuxXZzznWQ3XJmv23/mLfHcopCLDAwUIGBgffsV79+fV2+fFk7d+5UrVq1JEnr169XUlKS6tatm+Y0tWrVkpubm9atW6eOHTtKkg4ePKgTJ06ofv365n779u1Ts2bN1LNnT7399ttWWCoAAAAAuDunuEasYsWKatmypfr27auZM2cqISFBAwYM0FNPPWW+Y2JUVJSaN2+uBQsWqE6dOvL391dkZKQGDRqkAgUKyM/PTwMHDlT9+vXNN+r466+/1KxZM4WFhWnQoEHma8dcXV0zVCBmS9EbpcSbkquHFNTU0dkAAAAANrVxo8zXiDVt6uhsMs4pCjFJWrRokQYMGKDmzZvLxcVFHTt21LRp08yvJyQk6ODBg7p+/bq5bfLkyea+N2/eVFhYmD766CPz61999ZXOnTunhQsXauHCheb2EiVK6NixY3ZZLqvb0k26ESV5hUgdTjo6GwAAAMCmunWToqKkkBDppBMd/jpNIVagQAEtXrw43ddLliyZ6laRnp6emj59uqZPn57mNKNGjdKoUaOsmSYAAAAA3JOLoxMAAAAAgNyGQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7IxCDAAAAADsjEIMAAAAAOyMQgwAAAAA7MxpftAZGdTBiX5OHAAAALhPJ5308JczYgAAAABgZxRiAAAAAGBnFGIAAAAAYGdcI5bT/Dlaio+R3P2lqiMdnQ0AAABgU6NHSzExkr+/NNKJDn8pxHKaQ3OkG1GSVwiFGAAAAHK8OXOkqCgpJMS5CjGGJgIAAACAnVGIAQAAAICdUYgBAAAAgJ1RiAEAAACAnVGIAQAAAICdUYgBAAAAgJ1RiAEAAACAnVGIAQAAAICd8YPOOU2hJtLN85JHgKMzAQAAAGyuSRPp/HkpwMkOfynEcpqGixydAQAAAGA3i5z08JehiQAAAABgZxRiAAAAAGBnFGIAAAAAYGdcI5bTrGsmxUVLnkFS8/WOzgYAAACwqWbNpOhoKShIWu9Eh78UYjlN7D/SjSgpPsbRmQAAAAA2988/UlSUFONkh78MTQQAAAAAO6MQAwAAAAA7oxADAAAAADujEAMAAAAAO6MQAwAAAAA7oxADAAAAADujEAMAAAAAO6MQAwAAAAA74wedc5qqI6SEq5Kbr6MzAQAAAGxuxAjp6lXJ18kOfynEcpoyzzg6AwAAAMBunnHSw1+GJgIAAACAnVGIAQAAAICdMTQxp7lxWjISJZOr5FXY0dkAAAAANnX6tJSYKLm6SoWd6PCXQiynWf2gdCNK8gqROpx0dDYAAACATT34oBQVJYWESCed6PCXoYkAAAAAYGecEQMAJ5eUlKT4+HibxDYMQ/Hx8YqLi5PJZLLJPOw9T1vFt1Zca8RxxHbLbtzd3eXiwvfNALIvCjEAcGLx8fE6evSokpKSbDaPpKQkXbhwwWbxHTFPW8W3VlxrxHHEdstOXFxcFBoaKnd3d0enAgBpohADACdlGIZOnz4tV1dXFStWzCbf/huGocTERLm6utr1jJgt52mr+NaKa404jthu2UlSUpJOnTql06dPq3jx4rlyHQDI/ijEAMBJ3bp1S9evX1eRIkXk7e1tk3lQiNk/LoWYdQQGBurUqVO6deuW3NzcHJ0OAKTC4GkAcFKJiYmSxNArIA0pn4uUzwkAZDcUYgDg5HLrGQ/gbvhcAMjuKMQAAAAAwM4oxAAAAADAzrhZR07TfJ2UdEtyYdMCuVXk/O1Wi2Uo+cYPJpNJ6Q30+qTXg5mK2atXL12+fFnLly+3aN+4caMefvhhXbx4UXnz5s1SvvfyzTffaPz48Tpw4ICSkpJUvHhxPfLII5oyZYpN5gcAsL1166Rbt6Q8Tnb462Tp4p78yjs6AwDIltatW6fOnTvrrbfeUuvWrZUnTx4dOHBAa9eudXRqVpWQkKA8znY0AgD3obyTHv4yNBEAkC1duHBBXbp0UUhIiLy9vVW1alV9/vnnFn2++uorVa1aVV5eXipYsKBatGiha9eupRnvu+++U8OGDTVkyBCVL19e5cqVU/v27TV9+nRzn169eql9+/YW07300ktq2rSp+XnTpk01cOBAvfTSS8qfP7+CgoI0Z84cXbt2TZGRkfLz81OZMmW0atUq8zQbN26UyWTSjz/+qBo1asjLy0vNmjXT2bNntWrVKlWsWFF+fn56+umndf36dfN0q1evVqNGjZQvXz4VLFhQbdq00eHDh82vHzt2TCaTSV988YWaNGkiT09PzZ49W/7+/vr6668tlmP58uXy8fHRlStXMrwNAAC2QyEGAMiW4uLiVKtWLa1cuVJ//fWXnnnmGXXv3l2///67JOn06dPq0qWLevfurQMHDmjjxo16/PHHZRhGmvGCg4O1b98+/fXXX/ed26effqqAgAD9/vvvGjhwoJ5//nk9+eSTql+/vnbu3KlHH31U3bt3tyiqJGnUqFH68MMPtWXLFv3333968sknNWXKFC1evFgrV67UmjVr9MEHH5j7X7t2TYMGDdKOHTu0bt06ubi4qEOHDkpKSrKI+/rrr+vFF1/UgQMH9Pjjj6tz58769NNPLfrMmzdPTzzxhM2GfQIAMoexCznNscXSretSHm+p5NOOzgYA0vT999/L19fXou3O33sKCQnR4MGDzc8HDhyoH3/8UV9++aXq1Kmj06dP69atW3r88cdVokQJSVLVqlXTnefAgQO1adMmPfDAAypRooTq1aunRx99VF27dpWHh0em8q9WrZqGDx8uSRo6dKjeffddBQQEqE+fPnJ1ddWIESM0Y8YM7d27V/Xq1TNP99Zbb6lhw4aSpMjISA0dOlSHDx9WqVKlJElPPPGENm7caF7ujh07Wsx37ty5CgwM1P79+1WlShVz+0svvaTHH3/c/LxPnz5q2LChTp8+rSJFiujs2bP64Ycf9NNPP2VqOQHAGSxeLF2/Lnl7S0870eEvZ8Rymt2vSr/3TX4EgGzq4Ycf1p49eyz+ffzxxxZ9EhMTNXbsWFWtWlUFChSQr6+vfvzxR504cUJScjHUvHlzVa1aVZ06ddKcOXN06dKldOfp4+OjlStX6t9//9XQoUPl4+OjV155RXXq1El15upeHnjgAfP/u7q6qmDBghaFUVBQkCTp7Nmz6U4XFBQkb29vcxGW0nb7NP/++6+6dOmiUqVKyc/PTyVLlpQk8zpIUbt2bYvnderUUaVKlcxnxRYuXKgSJUrooYceytRyAoAzePVVqW/f5EdnQiEGALA7Hx8flSlTxuJfSEiIRZ8JEyZo6tSpeu2117Rhwwbt2bNHYWFhio+Pl5RcAK1du1arVq1SpUqV9MEHH6h8+fI6evToXeddunRpRUZG6uOPP9auXbu0f/9+ffHFF5IkFxeXVEMbExISUsVwc3OzeG4ymSzaUn5M+M4hhHf2SSvO7dOEh4fr4sWLmjNnjn777Tf99ttvkmReByl8fHxS5di7d29zITZv3jxFRETwI8cAkI1QiAEAsqUtW7aoXbt26tatm6pVq6ZSpUrpn3/+sehjMpnUsGFDjR49Wrt375a7u7u++eabDM+jZMmS8vb2Nt/gIzAwUKdPn7bos2fPnvtelqy4cOGCDh48qOHDh6t58+aqWLHiXc/43alr1646fvy4pk2bpv3796tnz542zBYAkFlcIwYAyJbKlCmjr7/+Wlu2bFH+/Pk1adIkRUdHq1KlSpKk3377TevWrdOjjz6qQoUK6bffftO5c+dUsWLFNOONGjVK169f12OPPaaiRYvqypUr+uCDD5SQkKBHHnlEktSsWTNNmDBBCxYsUP369bVw4UL99ddfqlGjht2WO0X+/PlVsGBBzZ49W4ULF9aJEyf0+uuvZ2r6xx9/XEOGDNGjjz6qokWL2jBbAEBmcUYMAJAtDR8+XDVr1lRYWJiaNm2q4OBgi1vL+/n56ZdfflGrVq1Urlw5DR8+XBMnTtRjjz2WZrwmTZroyJEj6tmzp6pUqaJWrVrpzJkzWrNmjcr//4/QhIWF6c0339Srr76qBx98UFeuXFGPHj3ssbipuLi4aMmSJdq5c6eqVKmil19+WRMmTMhUjN69eys+Pl69e/e2UZYAgKwyGend5xcZFhsbK39/f12+fFn+/v5WiWkYhmJiYuTv75+5Mf3fFJVuREleIVKHk/cXyxr5wKFy+3Zz1uXPaN5xcXE6evSoQkND5enpabNcEhMT5erqard1aOt52iq+teJaI05KjMWLF2vQoEE6deqU3N3ds5yTM7LH58PanHWfZU3OuA6yW86OyMfW87xX/KJFpagoKSREOnnSOjHvJ7+U2iAmJkZ+fn7pTsfQRAAAcpjr16/r5MmTGj9+vJ599tlcV4QBgDNgaCIAADnMe++9pypVqig4OFhDhw51dDoAgDRQiAEAkMOMGjVKN27c0E8//ZTqh7MBANkDQxNzGq9gy0cAAAAgBwsOtnx0FhRiOU3LHY7OAAAAALCbHU56+MvQRAAAAACwMwoxAAAAALAzCjEAAAAAsDOuEctpfn9WunlR8igg1Znl6GwAAAAAm3r2WeniRalAAWmWEx3+UojlNFErpRtRkleIozMBAAAAbG7lSikqSgpxssNfpynELl68qIEDB+q7776Ti4uLOnbsqKlTp97191Hi4uL0yiuvaMmSJbp586bCwsL00UcfKSgoKFXfCxcuqFq1aoqKitKlS5eUL18+Gy4NANjQ4s5WDGbIxZBkUsp/Unv6i0xF7NWrlz799FONGzdOr7/+url9+fLl6tChg5KSkrKcbWJioiZMmKD58+fr+PHj8vLyUtmyZdW3b1/16dMny3EBALA2p7lGrGvXrtq3b5/Wrl2r77//Xr/88oueeeaZu07z8ssv67vvvtPSpUv1888/69SpU3r88cfT7BsZGakHHnjAFqkDAO7g6emp8ePH69KlS1aNO3r0aE2ePFljx47V/v37tWHDBj3zzDO6fPmyVefjSImJifdVrAIAsgenKMQOHDig1atX6+OPP1bdunXVqFEjffDBB1qyZIlOnTqV5jQxMTH65JNPNGnSJDVr1ky1atXSvHnztGXLFm3bts2i74wZM3T58mUNHjzYHosDALleixYtFBwcrHHjxt2139dff63KlSvLw8NDJUuW1MSJE+/af8WKFerXr586deqk0NBQVatWTZGRkRb799DQUE2dOtViuurVq2vUqFHm5yaTSbNmzVKbNm3k7e2tihUrauvWrTp06JCaNm0qHx8fNWjQQIcPHzZPM2rUKNWqVUtz585V8eLF5evrq379+ikxMVHvvfeegoODVahQIb399tsW8540aZKqVq0qHx8fFStWTP369dPVq1fNr8+fP1/58uXTihUrVKlSJXl4eOjXX3+Vm5ubzpw5YxHrpZdeUuPGje+6jgAA2YNTDE3cunWr8uXLp9q1a5vbWrRoIRcXF/3222/q0KFDqml27typhIQEtWjRwtxWoUIFFS9eXFu3blW9evUkSfv379eYMWP022+/6ciRIxnK5+bNm7p586b5eWxsrCTJMAwZhpGlZbxTSqysxDNJMpKD3Hcsa+QDx8nt281Zlz+jeRt3fMZve8U2eaUXNwvr19XVVW+//ba6du2qgQMHqmjRoqmWd+fOnXryySc1cuRIde7cWVu2bFH//v1VoEAB9erVK824wcHBWr9+vZ5//nkFBgbeM4/b53nnehw7dqwmTpyoiRMn6vXXX9fTTz+tUqVK6fXXX1fx4sUVGRmpAQMG6IcffjBPc+TIEa1evVqrVq3S4cOH1alTJx05ckRly5bVxo0btWXLFkVGRqp58+aqW7eupOSib+rUqQoNDdWRI0fUv39/vf7665oxY4Y5p+vXr2v8+PGaM2eOChYsqGLFiqlUqVJasGCBhgwZIklKSEjQokWLNH78+HSXMTdJ//ORfTnrPsuanHEdZLecHZGPreeZsfjJR8AZTcGaOd8ZK6MxnaIQO3PmjAoVKmTRlidPHhUoUCDVt4G3T+Pu7p7qWq+goCDzNDdv3lSXLl00YcIEFS9ePMOF2Lhx4zR69OhU7TExMRmaPiMMwzB/I2oypXNdRhr8DCP5bWgYiv3/fLIayxr5wLFy+3Zz1uXPaN7x8fFKSkpSYmKiEhMTze0u1v47aBgy0rs+TFLSbfPOiKSkJBmGobZt26patWoaMWKE5syZYx5ulzL0buLEiWrWrJmGDRsmSSpdurT27dun999/X927d08z9nvvvaennnpKhQsXVqVKlVS/fn21bdtWLVu2vGORDIt1lvIH9Pa2nj17qmPHjpKkV155RY0bN9awYcPMX/ANGDBAffr0MU9jGIaSkpI0e/Zs5c2bV+XLl1fTpk118OBBrVixQi4uLipTpozee+89rV+/3vzl4sCBA83zLFasmEaPHq3+/fvrww8/NK+vhIQETZs2TdWqVTP3jYiI0Pz58zVo0CBJ0rfffqu4uDh17NiR4Yv63/voypUrFl+eZmfOus+yJmdcB9ktZ0fkY+t53iu+YfhJMskwDMXExFol5v3kl3KS5l4cWoi9/vrrqb65u9OBAwdsNv+hQ4eqYsWK6tatW6anS/nDJyWfEStWrJj8/f3l7+9vldxSKml/f//MvTn+v6/JZDLnkuVY1sgHDpXbt5uzLn9G846Li9OFCxfk6uoqV1fX/71g5UU1ZNLdVp/FvDPAxcVFJpNJrq6uGj9+vJo3b64hQ4bIxcXFIt7BgwfVtm1bi/iNGjXStGnT0p1v1apV9eeff2rnzp3avHmzNm3apPbt26tnz576+OOPzf1S5n/78zvbqlWrZn5epEiRVG2FCxdWXFycrl27Jj8/P5lMJpUsWdLiC8Dg4GDlyZNHbm5u5ragoCCdO3fOHOenn37Su+++q7///luxsbG6deuW4uLidPPmTXl7e8vFxUXu7u6qUaOGxfshIiJCI0aM0Pbt21WvXj199tln6tSpk/z8/Mx9MrttchJXV1e5uLgob9688vT0dHQ6GeKs+yxrcsZ1kN1ydkQ+tp7nveKnNN1+/Hu/Me8nv4zGc2gh9sorr6Q7vCRFqVKlFBwcrLNnz1q037p1SxcvXlRwcHCa0wUHBys+Pl6XL1+2+KMYHR1tnmb9+vX6888/9dVXX0n630oMCAjQG2+8keZZL0ny8PCQh4dHqvbMrPiMSImXlZjJNzj733T3E8uaMWB/uX27OevyZyTvlNdS97Pest4+HNGUXtwsrluTyaQmTZooLCxMw4YNS/PvwZ3Llv4y/4+rq6vq1KmjOnXq6OWXX9bChQvVvXt3DR8+XKGhoXJxcTHv71NiJCQkpIrp7u5ufp5SJKbVZhiGuS1Pnjyp8nRzc0u1DCnTHDt2TOHh4Xr++ef19ttvq0CBAtq0aZP69Omj+Ph4+fj4yGQyycvLyzy/FEFBQQoPD9f8+fNVqlQprVq1Shs3bjTHv3Od5TYZea9kR866z7ImZ1wH2S1nR+Rj63lmLP7dvzjMWszMx3KKQiwwMDBDY/jr16+vy5cva+fOnapVq5ak5CIqKSnJPMb+TrVq1ZKbm5vWrVtnHlpy8OBBnThxQvXr15eUfBH4jRs3zNNs375dvXv31qZNm1S6dOn7XTwAQAa8++67ql69usqXL2/RXrFiRW3evNmibfPmzSpXrlymzvRUqlRJknTt2jVJyX97bh/WHhsbq6NHj2Y1/fuyc+dO8zDMlELriy8y/nMAffr0UZcuXVS0aFGVLl1aDRs2tFWqAAArc4prxCpWrKiWLVuqb9++mjlzphISEjRgwAA99dRT5uEiUVFRat68uRYsWKA6derI399fkZGRGjRokAoUKCA/Pz8NHDhQ9evXN9+o485i6/z58+b5Oe3viJXsIsVfktzzOzoTAMiQqlWrqmvXruYhhykGDRqkOnXqaOzYsercubO2bt2qDz/8UB999FG6sZ544gk1bNhQDRo0UHBwsI4ePaqhQ4eqXLlyqlChgiTp4Ycf1qeffqq2bdsqf/78GjFihMOG8JUpU0YJCQn64IMPFB4ers2bN2vWrFkZnj4sLEx+fn566623NGbMGBtmCgDZV5cu0qVLUn4nO/x1itvXS9KiRYtUoUIFNW/eXK1atVKjRo00e/Zs8+sJCQk6ePCgrl+/bm6bPHmy2rRpo44dO+qhhx5ScHCwli1b5oj07afGBKnux8mPAOAkxowZk+rmEjVr1tSXX36pJUuWqEqVKhoxYoTGjBlz1yHtYWFh+u677xQeHq5y5cqpZ8+eqlChgtasWWMeNjh06FA99NBDCg8PV+vWrdW+fXuHjYKoVq2aJk2apPHjx6tKlSpatGiR3nnnnQxP7+Liol69eikxMVE9evSwYaYAkH1NmCB9/HHyozMxGdnlXptOLDY2Vv7+/rp8+bJVb9YRExNjtQsI7zeWNfOB/eT27easy5/RvOPi4nT06FGFhoba7GYEKXcSdHV1tetF37acp63iWytuZuNERkbq3LlzWrFihdVzcWb2+HxYm7Pus6zJGddBdsvZEfnYep62iG/LY+2U2iAmJsbiBkp3coqhiQAAwFJMTIz+/PNPLV682KIIAwA4BwoxAACcULt27fT777/rueee0yOPPOLodAAAmUQhltN8X0G6fkryLiK1+dvR2QAAbGTjxo2OTgEAsoUKFaRTp6QiRaS/nejw12lu1oEMSrgq3bqS/AgAAADkcFevSleuJD86EwoxAAAAALAzCjEAAAAAsDMKMQAAAACwMwoxAAAAALAzCjEAAAAAsDMKMQAA7uKhhx7S4sWLHZ0GbrN69WpVr15dSUlJjk4FALKMQgwAYFe9evWSyWTSc889l+q1/v37y8XFRb1793ZAZqmtWLFC0dHReuqpp8xtJUuW1JQpU1L1HTVqlGrVqmWzXBITE/Xuu++qQoUK8vLyUoECBVS3bl19/PHHNptndtWyZUu5ublp0aJFjk4FALKMQgwAYHfFihXTkiVLdOPGDXNbXFycFi9erOLFizswM0vTpk1TRESEXFwc/+dy9OjRmjx5ssaOHav9+/drw4YNeuaZZ3T58mVHp2Y1iYmJGT7L1atXL02bNs3GGQGA7Tj+Lwusq85MqdGXyY8AkE3VrFlTxYoV07Jly8xty5YtU/HixVWjRg2LvklJSRo3bpxCQ0Pl5eWlatWq6auvvjK/npiYqMjISPPr5cuX19SpUy1i9OrVS+3bt9f777+vIkWKKCgoSP3791dCQkK6OZ47d07r169XeHh4lpZx+/bteuSRRxQQECB/f381adJEu3btMr9uGIZGjRql4sWLy8PDQ0WKFNELL7yQbrwVK1aoX79+6tSpk0JDQ1WtWjVFRkZq8ODB5j5pna2rXr26Ro0aZX5uMpk0a9YstWnTRt7e3qpYsaK2bt2qQ4cOqWnTpvLx8VGDBg10+PBh8zSjRo1S9erVNXfuXBUvXly+vr7q16+fEhMT9d577yk4OFiFChXS22+/bTHvSZMmqWrVqvLx8VGxYsXUr18/Xb3tF1fnz5+vfPnyacWKFapUqZI8PDz066+/ys3NTWfOnLGI9dJLL6lx48bm5+Hh4dqxY4dFngByp5kzpS+/TH50JhRiOU1IG6l4p+RHAMjGevfurXnz5pmfz507VxEREan6jRs3TgsWLNDMmTO1b98+vfzyy+rWrZt+/vlnScmFWtGiRbV06VLt379fI0aM0LBhw/Tll19axNmwYYMOHz6s9evXa+7cufr00081f/78dPP79ddfzYVKVly5ckU9e/bUr7/+qm3btqls2bJq1aqVrly5Ikn6+uuvNXnyZM2aNUv//vuvli9frqpVq6YbLzg4WOvXr9e5c+eylM/txo4dqx49emjPnj2qUKGCnn76aT377LMaOnSoduzYIcMwNGDAAItpDh8+rFWrVmn16tX6/PPP9cknn6h169Y6efKkfv75Z40fP17Dhw/Xb7/9Zp7GxcVF06ZN0759+/Tpp59q/fr1evXVVy3iXr9+XePHj9fHH3+sffv2qXbt2ipVqpQ+++wzc5+EhAQtWrTIYshq8eLFFRQUpE2bNt33+gDg3Nq0kTp1Sn50KgbuW0xMjCHJuHz5stViJiUlGZcuXTKSkpKyRSxr5gP7ye3bzVmXP6N537hxw9i/f79x48YNi/aJEw0jJOTe/8LDU8cMD7+zX9Jt//7XPnFi1pevZ8+eRrt27YyzZ88aHh4exrFjx4xjx44Znp6exrlz54x27doZ3bt3N5KSkoy4uDjD29vb2LJli0WMyMhIo0uXLunOo3///kbHjh0t5lmiRAnj1q1bRlJSkpGQkGB06tTJ6Ny5c7oxJk+ebJQqVSpVe4kSJQx3d3fDx8fH4p+bm5vxwAMPpLvdEhMTjbx58xrfffedYRiGMXHiRKNcuXJGfHy8Rb+U/O6Ms2/fPqNixYqGi4uLUbVqVePZZ581fvjhh1S5TZ482SJGtWrVjJEjR5r7SDKGDx9ufr5161ZDkvHJJ5+Y2z7//HPD09PT/HzkyJGGt7e3ERsba24LCwszSpYsaSQmJprbypcvb4wbNy7N5TcMw1i6dKlRsGBB8/N58+YZkow9e/ZY9Bs/frxRsWJF8/Ovv/7a8PX1Na5evWrRr0aNGsaoUaPSnFd6n4/szFn3WdbkjOsgu+XsiHxsPU9bxLflsXZKbRATE3PX6fI4tAoEAFhdbKwUFXXvfsWKpW47d+7OaU3pzuN+BQYGqnXr1po/f74Mw1Dr1q0VEBBg0efQoUO6fv26HnnkEYv2+Ph4iyGM06dP19y5c3XixAnduHFD8fHxql69usU0lStXlqurqwzDkJR8humvv/5KN78bN27I09MzzdeGDBmiXr16WbRNnTpVv/zyi/l5dHS0hg8fro0bN+rs2bNKTEzU9evXdeLECUlSp06dNGXKFJUqVUotW7ZUq1atFB4eLldX1zTnWalSJf3111/auXOnNm/erF9++UXh4eHq1atXpm/Y8cADD5j/PygoSJIszsYFBQUpLi5OsbGx8vPzk5Q87DFv3rwWfVxdXS2unwsKCtLZs2fNz3/66SeNGzdOf//9t2JjY3Xr1i3FxcXp+vXr8vb2liS5u7tb5CMlDyUdPny4tm3bpnr16mn+/Pl68skn5ePjY9HPy8tL169fz9SyA0B2QSGW01zcKSXGS67uUgHb3b0LQPbl5yeFhNy7X2Bg2m2W0xq3/f//irL/Pza/b7179zYPgZs+fXqq11OuJ1q5cqVC7lgoDw8PSdKSJUs0ePBgTZw4UfXr11fevHk1YcIEiyFykuTm5mbx3GQy3fXGEAEBAbp06VK6r5UpU8airUCBAhbPe/bsqQsXLmjq1KkqUaKEPDw8VL9+fcXHx0tKvmHJwYMH9dNPP2nt2rXq16+fJkyYoI0bN6Z7cxAXFxc9+OCDevDBB/XSSy9p4cKF6t69u9544w2FhobKxcXFXGimSOs6uNvXhclkSrft9vWT1vq72zo9duyY2rRpo+eff15vv/22ChQooF9//VWRkZGKj483F2JeXl7m+aUoVKiQwsPDNW/ePIWGhmrVqlXauHFjquW4ePGiAtN6IwPIVXbulOLjJXd3yYY3r7U6CrGc5ud20o0oyStE6nDS0dkAcIBBg5L/ZcWKFZbPDSP5Zhiurq4ypX1y7L60bNlS8fHxMplMCgsLS/V6yg0cTpw4oSZNmqQZY/PmzWrQoIH69etnbrPGDRxq1KihM2fO6NKlS8qfP3+mp9+8ebM++ugjtWrVSpL033//6fz58xZ9vLy8FB4ervDwcPXv318VKlTQn3/+qWrVqmVoHpUqVZIkXbt2TVLyWcbTp0+bX4+NjdXRo0cznbs17Ny5U0lJSZo4caK5sLzzur276dOnj7p06aKiRYuqdOnSatiwocXrcXFxOnz4cKqbuwDIfdq1Sx7NERIinXSiw18KMQCAw7i6uurAgQPm/79T3rx5NXjwYL388stKSkpSo0aNFBMTo82bN8vPz089e/ZU2bJltWDBAv34448KDQ3VZ599pu3btys0NPS+cqtRo4YCAgK0efNmtcnCFeBly5bVZ599ptq1ays2NlZDhgyRl5eX+fX58+crMTFRdevWlbe3txYuXCgvLy+VKFEizXhPPPGEGjZsqAYNGig4OFhHjx7V0KFDVa5cOVWoUEGS1KxZM82fP19t2rRR3rx5NWbMmHSHOtpamTJllJCQoA8++EDh4eHavHmzZmbilmZhYWHy8/PTW2+9pTFjxqR6fdu2beazjADgjLhrIgDAofz8/MzXIaVl7NixevPNNzVu3DhVrFhRLVu21MqVK82F1rPPPqvHH39cnTt3Vt26dXXhwgWLs2NZ5erqqoiIiCz/aPAnn3yiS5cuqWbNmurevbteeOEFFSpUyPx6vnz5NGfOHDVs2FAPPPCAfvrpJ3333XcqWLBgmvHCwsL03XffKTw8XOXKlVPPnj1VoUIFrVmzRnnyJH+vOnToUDVp0kTh4eFq166d2rVrp9KlS2cp//tVrVo1TZo0SePHj1eVKlW0aNEijRs3LsPTu7i4qFevXkpMTFSPHj1Svf7555+ra9eu5iGOAOBsTMadg8mRabGxsfL399fly5fl7+9vlZiGYSgmJkb+/v6pxs7f1TdFUw1NzHIsa+QDh8rt281Zlz+jecfFxeno0aMKDQ1N96YS1sjlf0MT7bMObT3PzMQ/c+aMKleurF27dqV7piorcW0dxxHbzRYiIyN17tw5rbhjzOz58+dVvnx57dixI90zn/b4fFibs+6zrMkZ10F2y9kR+dh6nveKX7Ro5ocmWjPnO2Ol1AYxMTF3/aKRoYkAAKQjODhYn3zyiU6cOHHPQgzWExMToz///FOLFy9OVYRJyTcC+eijj+57+CkAOBKFGAAAd9G+fXtHp5DrtGvXTr///ruee+65VD9dIEm1a9dW7dq1HZAZAFgPhRgAAMhW0rpVPQDkNNysAwAAAADsjEIMAAAAAOyMQgwAAAAA7IxrxHKaNgckGZIcf/tUAAAAwNYOHJAMQ8oGvx6QKRRiOY1bXkdnAAAAANhNXic9/GVoIgAAAADYGYUYAAB38dBDD2nx4sWOTiPXef311zVw4EBHpwEANkMhltMcmCTtHZX8CADZUK9evWQymfTcc8+leq1///5ycXFR7969HZBZaitWrFB0dLSeeuopc1vJkiVlMpm0bds2i74vvfSSmjdvfl/zO3funJ5//nmVKlVKnp6eCg4OVlhYmDZv3nxfcZ3R4MGD9emnn+rIkSOOTgVANjdpkjRqVPKjM6EQy2n+niT9NTr5EQCyqWLFimnJkiW6ceOGuS0uLk6LFy9W8eLFHZiZpWnTpikiIkIuLpZ/Lj09PfXaa69ZfX4dO3bUnj17NHfuXB08eFArVqxQ06ZNdeHCBavPy1Hi4+Mz1C8gIEBhYWGaMWOGjTMC4OwmTZJGj6YQAwDgnmrWrKlixYpp2bJl5rZly5apePHiqlGjhkXfpKQkjRs3TqGhofLy8lK1atX01VdfmV9PTExUZGSk+fXy5ctr6tSpFjF69eql9u3b6/3331eRIkUUFBSk/v37KyEhId0cz507p/Xr1ys8PDzVa88884y2bdumH374Id3pk5KSNGbMGBUtWlQeHh6qXr26Vq9enW7/y5cva9OmTXr33XfVtGlTlShRQnXq1NHQoUPVtm1bSdKxY8dkMpm0Z88ei+lMJpM2btwoSdq4caNcXFy0Zs0a1axZU15eXmrWrJnOnj2rVatWqWLFivLz89PTTz+t69evm+M0bdpUAwcO1EsvvaT8+fMrKChIc+bM0bVr1xQREaG8efOqTJkyWrVqVZbW/dtvv60iRYqofPnyGjNmjKpUqZJqHVSvXl1vvvmm+Xl4eLiWLFmS7joDAGdGIQYAcIjevXtr3rx55udz585VREREqn7jxo3TggULNHPmTO3bt08vv/yyunXrpp9//llScsFTtGhRLV26VPv379eIESM0bNgwffnllxZxNmzYoMOHD2v9+vWaO3euPv30U82fPz/d/H799Vd5e3urYsWKqV4LDQ3Vc889p6FDhyopKSnN6adOnaqJEyfq/fff1969exUWFqa2bdvq33//TbO/r6+vfH19tXz5ct28eTPdvDJqzJgx+uCDD7Rlyxb9999/evLJJzVlyhQtXrxYK1eu1Jo1a/TBBx9YTPPpp58qICBAv//+uwYOHKjnn39enTp1UoMGDbRr1y49+uij6t69u7mAy+i6X7dunQ4ePKi1a9fq+++/V+/evXXgwAFt377d3Gf37t3au3evxXugTp06OnnypI4dO3bf6wMAshtuXw8AOc2BSRkbnlygptRkhWXbz22li7ssmlzTmrbCIKnioCynKEndunXT0KFDdfz4cUnS5s2btWTJEvOZHUm6efOm3nnnHf3000+qX7++JKlUqVL69ddfNWvWLDVp0kRubm4aPXq0eZrQ0FBt3bpVX375pZ588klze/78+fXhhx/KxcVFZcuWVevWrbVu3Tr17ds3zfyOHz+uoKCgVMMSUwwfPlzz5s3TokWL1L1791Svv//++3rttdfM15eNHz9eGzZs0JQpUzR9+vRU/fPkyaP58+erb9++mjVrlmrWrKkmTZroqaee0gMPPHCPtZnamDFj1LBhQ5lMJkVGRmro0KE6fPiwSpUqJUl64okntGHDBoshltWqVdPw4cMlSUOHDtW7776rgIAA8zoaMWKEZsyYob1796pevXoZXvc+Pj76+OOP5e7ubm4LCwvTvHnz9OCDD0qS5s2bpyZNmpjzk6QiRYpISt4WJUuWzPQ6AIDsjDNiAJDTJMRKN6Lu/S/uXOpp485Z9DHd9s9i2oTY+04zMDBQrVu31vz58zVv3jy1bt1aAQEBFn0OHTqk69ev65FHHjGfMfL19dWCBQt0+PBhc7/p06erVq1aCgwMlK+vr2bPnq0TJ05YxKpcubJcXf9XVgYHB+vs2bPp5nfjxg15enreNf/BgwdrxIgRqa57io2N1alTp9SwYUOL9oYNG+rAgQPpxuzYsaOioqL0zTffKCwsTBs3blTNmjXveuYuPVWrVjX/f1BQkLy9vS2KnKCgoFTLf3vB5+rqqoIFC6aKI8liuoys+6pVq1oUYZLUt29fff7554qLi1N8fLwWL16c6iYtXl5ekmQxhBIAcgrOiAFATuPmJ3mF3LufZ2DabbdNa9z2kunOeVhB7969NWDAAElK8yzR1atXJUkrV65USIjlMnl4eEiSlixZosGDB2vixImqX7++8ubNqwkTJui3336z6O/m5mbx3GQypTusUEq+WcSlS5fumv+gQYP00Ucf6aOPPrprv8zw9PRUixYtFBYWphEjRqhPnz4aOXKkevXqZT47Zxj/2zLpXed2+/KaTKYMLX9afe6MI8k8XUbXvY+PT6r8wsPD5eHhoW+++Ubu7u5KSEjQE088YdHn4sWLkpKLXgDIaSjEACCnqXgfwwbvHKpoGEpMTEw+k2QypT3NfWjZsqXi4+NlMpkUFhaW6vVKlSrJw8NDJ06cUJMmTdKMsXnzZjVo0ED9+vUzt91+tiyratSooTNnzujSpUvKnz9/mn18fX315ptvatSoURY39fDz81ORIkW0efNmi7w3b96sOnXqZCqPSpUqafny5ZL+V5CcPn3afFOT22/cYW/3s+7z5Mmjnj17at68eXJ3d9dTTz1lPgOW4q+//pKbm5sqV65s1bwBIDugEAMAOIyrq6t5qN7twwZT5M2bV4MHD9bLL7+spKQkNWrUSDExMdq8ebP8/PzUs2dPlS1bVgsWLNCPP/6o0NBQffbZZ9q+fbtCQ0PvK7caNWooICBAmzdvVps2bdLt98wzz2jy5Mn6/PPPLYqsIUOGaOTIkSpdurSqV6+uefPmac+ePVq0aFGacS5cuKBOnTopIiJClStXVr58+bRz50699957ateunaTkoXr16tXTu+++q9DQUJ09e9Z8TZcj3O+679Onj/lmKGn9VtqmTZvUuHHjVAUaAOQEFGIAAIfy87v7MMexY8cqMDBQ48aN05EjR5QvXz7VrFlTw4YNkyQ9++yz2r17tzp37iyTyaQuXbqoX79+FrdZzwpXV1dFRERo0aJFdy3E3NzcNHbsWD399NMW7S+88IJiYmL0yiuv6OzZs6pUqZJWrFihsmXLphnH19dXdevW1ZQpU3T48GElJCSoWLFi6tu3r3lZpeS7S0ZGRqpWrVoqX7683nvvPT366KP3taxZdb/rvmzZsmrQoIEuXryounXrpnp9yZIlGjVqlJWzBoDswWTcPtAcWRIbGyt/f39dvnxZ/v7+VolpGIZiYmLk7+9vHpOfIT+*****************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Separate the data into two groups\n", "summary_true = [\n", "    count\n", "    for count, has_summary in zip(total_changed_line_counts, has_summary_v2)\n", "    if has_summary\n", "]\n", "summary_false = [\n", "    count\n", "    for count, has_summary in zip(total_changed_line_counts, has_summary_v2)\n", "    if not has_summary\n", "]\n", "\n", "# Create the plot\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Use log scale for bin edges\n", "bins = np.logspace(\n", "    np.log10(min(min(summary_true), min(summary_false))),\n", "    np.log10(max(max(summary_true), max(summary_false))),\n", "    num=30,\n", ")\n", "\n", "plt.hist(\n", "    [summary_true, summary_false],\n", "    label=[\"Has Summary\", \"No Summary\"],\n", "    bins=bins,\n", "    alpha=0.7,\n", ")\n", "\n", "plt.xscale(\"log\")  # Set x-axis to logarithmic scale\n", "plt.xlabel(\"Total Changed Line Count (log scale)\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Distribution of Changed Line Counts with and without Summary\")\n", "plt.legend()\n", "\n", "# Add a vertical line for the mean of each distribution\n", "plt.axvline(\n", "    np.mean(summary_true),\n", "    color=\"blue\",\n", "    linestyle=\"dashed\",\n", "    linewidth=2,\n", "    label=\"Mean (Has Summary)\",\n", ")\n", "plt.axvline(\n", "    np.mean(summary_false),\n", "    color=\"orange\",\n", "    linestyle=\"dashed\",\n", "    linewidth=2,\n", "    label=\"Mean (No Summary)\",\n", ")\n", "\n", "plt.legend()\n", "plt.grid(True, which=\"both\", ls=\"-\", alpha=0.2)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. Min of pos: 1012\n", "2. <PERSON> of neg: 896\n", "3. Percentile in pos for max of neg: 0.00\n", "4. Percentile in neg for min of pos: 100.00\n"]}], "source": ["import numpy as np\n", "\n", "\n", "def get_percentile_rank(array, value):\n", "    \"\"\"\n", "    Calculate the percentile rank of a value in an array.\n", "\n", "    Parameters:\n", "        array: numpy array or list of numbers\n", "        value: the value to find the percentile rank for\n", "\n", "    Returns:\n", "        float: percentile rank (0-100)\n", "    \"\"\"\n", "    array = np.asarray(array)\n", "    # Count values less than our target value\n", "    less = np.sum(array < value)\n", "    # Count values equal to our target value\n", "    equal = np.sum(array == value)\n", "\n", "    # Calculate percentile using midpoint of ties\n", "    percentile = (less + 0.5 * equal) / len(array) * 100\n", "\n", "    return percentile\n", "\n", "\n", "# Convert to numpy arrays for easier computation\n", "pos = np.array(summary_true)\n", "neg = np.array(summary_false)\n", "\n", "# 1. Min of pos\n", "min_pos = np.min(pos)\n", "\n", "# 2. <PERSON> of neg\n", "max_neg = np.max(neg)\n", "\n", "# 3. Percentile of the max neg values among pos values\n", "percentile_max_neg_in_pos = get_percentile_rank(pos, max_neg)\n", "\n", "# 4. Percentile of the min pos values among neg values\n", "percentile_min_pos_in_neg = get_percentile_rank(neg, min_pos)\n", "\n", "\n", "print(f\"1. Min of pos: {min_pos}\")\n", "print(f\"2. Max of neg: {max_neg}\")\n", "print(f\"3. Percentile in pos for max of neg: {percentile_max_neg_in_pos:.2f}\")\n", "print(f\"4. Percentile in neg for min of pos: {percentile_min_pos_in_neg:.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
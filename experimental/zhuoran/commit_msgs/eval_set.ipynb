{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Pick eval set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total evaluation items: 70\n", "Reason: largest_diff\n", "\n", "Commit index: 8233\n", "Diff stats: +0, -4854783036, total: 4854783036\n", "Folder percentages:\n", "  third_party: 93.66%\n", "  models: 4.91%\n", "  base: 0.96%\n", "  services: 0.36%\n", "  tools: 0.12%\n", "\n", "Commit index: 12404\n", "Diff stats: +488057424, -395575646, total: 883633070\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 8730\n", "Diff stats: +289498859, -0, total: 289498859\n", "Folder percentages:\n", "  third_party: 100.00%\n", "\n", "Commit index: 10805\n", "Diff stats: +153615, -273384720, total: 273538335\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 12405\n", "Diff stats: +14250, -154927140, total: 154941390\n", "Folder percentages:\n", "  research: 93.86%\n", "  third_party: 5.09%\n", "  tools: 0.53%\n", "  base: 0.35%\n", "  services: 0.18%\n", "\n", "\n", "\n", "Reason: smallest_diff\n", "\n", "Commit index: 219\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 236\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 242\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 336\n", "Diff stats: +0, -1, total: 1\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 425\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "\n", "\n", "Reason: clients_latest\n", "\n", "Commit index: 0\n", "Diff stats: +21, -875, total: 896\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 0\n", "Diff stats: +21, -875, total: 896\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 0\n", "Diff stats: +21, -875, total: 896\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 0\n", "Diff stats: +21, -875, total: 896\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 0\n", "Diff stats: +21, -875, total: 896\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "\n", "\n", "Reason: deploy_latest\n", "\n", "Commit index: 1\n", "Diff stats: +14, -0, total: 14\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 4\n", "Diff stats: +4, -3, total: 7\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 6\n", "Diff stats: +96, -114, total: 210\n", "Folder percentages:\n", "  services: 66.67%\n", "  deploy: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 8\n", "Diff stats: +32, -14, total: 46\n", "Folder percentages:\n", "  deploy: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 20\n", "Diff stats: +7, -0, total: 7\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "\n", "\n", "Reason: base_latest\n", "\n", "Commit index: 2\n", "Diff stats: +14, -2, total: 16\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 13\n", "Diff stats: +4, -4, total: 8\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 13\n", "Diff stats: +4, -4, total: 8\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 23\n", "Diff stats: +29, -71, total: 100\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 24\n", "Diff stats: +222, -290, total: 512\n", "Folder percentages:\n", "  base: 50.00%\n", "  services: 50.00%\n", "\n", "\n", "\n", "Reason: data_latest\n", "\n", "Commit index: 3\n", "Diff stats: +605, -0, total: 605\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "Commit index: 3\n", "Diff stats: +605, -0, total: 605\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "Commit index: 3\n", "Diff stats: +605, -0, total: 605\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "Commit index: 3\n", "Diff stats: +605, -0, total: 605\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "Commit index: 3\n", "Diff stats: +605, -0, total: 605\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "\n", "\n", "Reason: services_latest\n", "\n", "Commit index: 6\n", "Diff stats: +96, -114, total: 210\n", "Folder percentages:\n", "  services: 66.67%\n", "  deploy: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 6\n", "Diff stats: +96, -114, total: 210\n", "Folder percentages:\n", "  services: 66.67%\n", "  deploy: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 6\n", "Diff stats: +96, -114, total: 210\n", "Folder percentages:\n", "  services: 66.67%\n", "  deploy: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 6\n", "Diff stats: +96, -114, total: 210\n", "Folder percentages:\n", "  services: 66.67%\n", "  deploy: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 7\n", "Diff stats: +77, -42, total: 119\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "\n", "\n", "Reason: tools_latest\n", "\n", "Commit index: 6\n", "Diff stats: +96, -114, total: 210\n", "Folder percentages:\n", "  services: 66.67%\n", "  deploy: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 8\n", "Diff stats: +32, -14, total: 46\n", "Folder percentages:\n", "  deploy: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 9\n", "Diff stats: +26, -8, total: 34\n", "Folder percentages:\n", "  services: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 12\n", "Diff stats: +2, -1, total: 3\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 49\n", "Diff stats: +0, -20, total: 20\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "\n", "\n", "Reason: research_latest\n", "\n", "Commit index: 10\n", "Diff stats: +8, -78, total: 86\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 10\n", "Diff stats: +8, -78, total: 86\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 16\n", "Diff stats: +18, -34, total: 52\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 16\n", "Diff stats: +18, -34, total: 52\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 17\n", "Diff stats: +531, -0, total: 531\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "\n", "\n", "Reason: experimental_latest\n", "\n", "Commit index: 19\n", "Diff stats: +1428, -32473, total: 33901\n", "Folder percentages:\n", "  experimental: 71.43%\n", "  research: 28.57%\n", "\n", "Commit index: 19\n", "Diff stats: +1428, -32473, total: 33901\n", "Folder percentages:\n", "  experimental: 71.43%\n", "  research: 28.57%\n", "\n", "Commit index: 19\n", "Diff stats: +1428, -32473, total: 33901\n", "Folder percentages:\n", "  experimental: 71.43%\n", "  research: 28.57%\n", "\n", "Commit index: 19\n", "Diff stats: +1428, -32473, total: 33901\n", "Folder percentages:\n", "  experimental: 71.43%\n", "  research: 28.57%\n", "\n", "Commit index: 19\n", "Diff stats: +1428, -32473, total: 33901\n", "Folder percentages:\n", "  experimental: 71.43%\n", "  research: 28.57%\n", "\n", "\n", "\n", "Reason: .github_latest\n", "\n", "Commit index: 179\n", "Diff stats: +9288, -10584, total: 19872\n", "Folder percentages:\n", "  research: 33.33%\n", "  tools: 33.33%\n", "  experimental: 27.78%\n", "  .github: 5.56%\n", "\n", "Commit index: 287\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 357\n", "Diff stats: +273, -210, total: 483\n", "Folder percentages:\n", "  clients: 71.43%\n", "  .github: 28.57%\n", "\n", "Commit index: 357\n", "Diff stats: +273, -210, total: 483\n", "Folder percentages:\n", "  clients: 71.43%\n", "  .github: 28.57%\n", "\n", "Commit index: 952\n", "Diff stats: +0, -162, total: 162\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "\n", "\n", "Reason: models_latest\n", "\n", "Commit index: 603\n", "Diff stats: +8151, -104, total: 8255\n", "Folder percentages:\n", "  services: 53.85%\n", "  deploy: 23.08%\n", "  models: 23.08%\n", "\n", "Commit index: 603\n", "Diff stats: +8151, -104, total: 8255\n", "Folder percentages:\n", "  services: 53.85%\n", "  deploy: 23.08%\n", "  models: 23.08%\n", "\n", "Commit index: 603\n", "Diff stats: +8151, -104, total: 8255\n", "Folder percentages:\n", "  services: 53.85%\n", "  deploy: 23.08%\n", "  models: 23.08%\n", "\n", "Commit index: 683\n", "Diff stats: +86, -26, total: 112\n", "Folder percentages:\n", "  base: 50.00%\n", "  models: 50.00%\n", "\n", "Commit index: 2385\n", "Diff stats: +2960, -660, total: 3620\n", "Folder percentages:\n", "  services: 75.00%\n", "  base: 20.00%\n", "  models: 5.00%\n", "\n", "\n", "\n", "Reason: third_party_latest\n", "\n", "Commit index: 1353\n", "Diff stats: +5688, -4812, total: 10500\n", "Folder percentages:\n", "  services: 58.33%\n", "  third_party: 25.00%\n", "  Cargo.lock: 8.33%\n", "  Cargo.toml: 8.33%\n", "\n", "Commit index: 1353\n", "Diff stats: +5688, -4812, total: 10500\n", "Folder percentages:\n", "  services: 58.33%\n", "  third_party: 25.00%\n", "  Cargo.lock: 8.33%\n", "  Cargo.toml: 8.33%\n", "\n", "Commit index: 1353\n", "Diff stats: +5688, -4812, total: 10500\n", "Folder percentages:\n", "  services: 58.33%\n", "  third_party: 25.00%\n", "  Cargo.lock: 8.33%\n", "  Cargo.toml: 8.33%\n", "\n", "Commit index: 1739\n", "Diff stats: +70219, -14706, total: 84925\n", "Folder percentages:\n", "  third_party: 88.37%\n", "  services: 4.65%\n", "  Cargo.lock: 2.33%\n", "  Cargo.toml: 2.33%\n", "  MODULE.bazel: 2.33%\n", "\n", "Commit index: 1739\n", "Diff stats: +70219, -14706, total: 84925\n", "Folder percentages:\n", "  third_party: 88.37%\n", "  services: 4.65%\n", "  Cargo.lock: 2.33%\n", "  Cargo.toml: 2.33%\n", "  MODULE.bazel: 2.33%\n", "\n", "\n", "\n", "Reason: .vscode_latest\n", "\n", "Commit index: 2815\n", "Diff stats: +6, -6, total: 12\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 2954\n", "Diff stats: +2, -0, total: 2\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 3176\n", "Diff stats: +15, -2, total: 17\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 4726\n", "Diff stats: +1148, -616, total: 1764\n", "Folder percentages:\n", "  research: 85.71%\n", "  .vscode: 7.14%\n", "  pyrightconfig.ci.json: 7.14%\n", "\n", "Commit index: 7733\n", "Diff stats: +9042, -17271042, total: 17280084\n", "Folder percentages:\n", "  research: 98.91%\n", "  services: 0.24%\n", "  .flake8: 0.12%\n", "  .github: 0.12%\n", "  .pre-commit-config.yaml: 0.12%\n", "  .vscode: 0.12%\n", "  pyproject.toml: 0.12%\n", "  pyrightconfig.ci.json: 0.12%\n", "  tools: 0.12%\n", "\n", "\n", "\n"]}], "source": ["import json\n", "from collections import Counter, defaultdict\n", "from pathlib import Path\n", "\n", "from experimental.zhuoran.commit_msgs.prompt_builder import GitHistory\n", "\n", "# Load the commits\n", "git_history = GitHistory.from_json(\n", "    \"/mnt/efs/augment/user/zhuoran/commit_msgs/commits.json\"\n", ")\n", "\n", "\n", "def get_diff_stats(commit):\n", "    total_insertions = 0\n", "    total_deletions = 0\n", "    for diff in commit[\"diffs\"].values():\n", "        total_insertions += diff[\"insertions\"]\n", "        total_deletions += diff[\"deletions\"]\n", "    return total_insertions, total_deletions\n", "\n", "\n", "def get_folder_percentages(commit):\n", "    file_counts = Counter()\n", "    for file_path in commit[\"diffs\"].keys():\n", "        top_level_folder = Path(file_path).parts[0]\n", "        file_counts[top_level_folder] += 1\n", "\n", "    total_files = sum(file_counts.values())\n", "    percentages = [\n", "        (folder, count / total_files * 100) for folder, count in file_counts.items()\n", "    ]\n", "    return sorted(percentages, key=lambda x: x[1], reverse=True)\n", "\n", "\n", "# 1. Top-5 largest-diff commits\n", "largest_diff_commits = sorted(\n", "    enumerate(git_history.commits),\n", "    key=lambda c: sum(get_diff_stats(c[1])),\n", "    reverse=True,\n", ")[:5]\n", "\n", "# 2. Top-5 smallest-diff commits (excluding commits with no changes)\n", "smallest_diff_commits = sorted(\n", "    [(i, c) for i, c in enumerate(git_history.commits) if sum(get_diff_stats(c)) > 0],\n", "    key=lambda c: sum(get_diff_stats(c[1])),\n", ")[:5]\n", "\n", "# 3. 5 latest commits that touch each top-level subfolder\n", "folder_latest_commits = {}\n", "for i, commit in enumerate(git_history.commits):\n", "    for file_path in commit[\"diffs\"].keys():\n", "        path_parts = Path(file_path).parts\n", "        if len(path_parts) < 2:\n", "            continue\n", "        top_level_folder = path_parts[0]\n", "        if top_level_folder not in folder_latest_commits:\n", "            folder_latest_commits[top_level_folder] = []\n", "        if len(folder_latest_commits[top_level_folder]) < 5:\n", "            folder_latest_commits[top_level_folder].append((i, commit))\n", "\n", "eval_set = []\n", "\n", "\n", "def make_eval_split(eval_subset, reason):\n", "    eval_split = []\n", "    for index, commit in eval_subset:\n", "        insertions, deletions = get_diff_stats(commit)\n", "        folder_percentages = get_folder_percentages(commit)\n", "        if reason == \"folder_latest\":\n", "            reason += f\"_{folder_percentages[0][0]}_latest\"\n", "\n", "        eval_split.append(\n", "            {\n", "                \"commit_index\": index,\n", "                \"diff_stats\": {\n", "                    \"insertions\": insertions,\n", "                    \"deletions\": deletions,\n", "                    \"total_changes\": insertions + deletions,\n", "                },\n", "                \"folder_percentages\": folder_percentages,\n", "                \"reason\": reason,\n", "            }\n", "        )\n", "    return eval_split\n", "\n", "\n", "eval_set = defaultdict(list)\n", "eval_set[\"largest_diff\"] = make_eval_split(largest_diff_commits, \"largest_diff\")\n", "eval_set[\"smallest_diff\"] = make_eval_split(smallest_diff_commits, \"smallest_diff\")\n", "for folder, commits in folder_latest_commits.items():\n", "    eval_set[f\"{folder}_latest\"] = make_eval_split(commits, f\"folder_latest_{folder}\")\n", "\n", "# Print the evaluation set\n", "print(f\"Total evaluation items: {sum(len(eval_set[key]) for key in eval_set)}\")\n", "for key in eval_set:\n", "    print(f\"Reason: {key}\")\n", "    for item in eval_set[key]:\n", "        print(f\"\\nCommit index: {item['commit_index']}\")\n", "        print(\n", "            f\"Diff stats: +{item['diff_stats']['insertions']}, -{item['diff_stats']['deletions']}, total: {item['diff_stats']['total_changes']}\"\n", "        )\n", "        print(\"Folder percentages:\")\n", "        for folder, percentage in item[\"folder_percentages\"]:\n", "            print(f\"  {folder}: {percentage:.2f}%\")\n", "    print(\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_ful;.json\", \"w\") as f:\n", "    json.dump(eval_set, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "eval_set_full = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_full.json\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff\n", "smallest_diff\n", "clients_latest\n", "deploy_latest\n", "base_latest\n", "data_latest\n", "services_latest\n", "tools_latest\n", "research_latest\n", "experimental_latest\n", ".github_latest\n", "models_latest\n", "third_party_latest\n", ".vscode_latest\n"]}], "source": ["for key in eval_set_full:\n", "    print(key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "metadata": {}, "output_type": "display_data"}], "source": ["reason = \"largest_diff\"\n", "split = eval_set_full[reason]\n", "len(split)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'commit_index': 8233,\n", " 'diff_stats': {'insertions': 0,\n", "  'deletions': 4854783036,\n", "  'total_changes': 4854783036},\n", " 'folder_percentages': [['third_party', 93.65649311789348],\n", "  ['models', 4.90724117295033],\n", "  ['base', 0.9575104727707959],\n", "  ['services', 0.3590664272890485],\n", "  ['tools', 0.11968880909634949]],\n", " 'reason': 'largest_diff'}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["split[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Token counting (9M/1000 commits)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from tqdm import tqdm\n", "\n", "# Initialize the <PERSON> token counter\n", "token_counter = ClaudeTokenCounter()\n", "\n", "git_history = GitHistory.from_json(\"/home/<USER>/zhuoran/commit_msgs/commits.json\")\n", "\n", "# Initialize variables\n", "total_tokens = 0\n", "token_counts = []\n", "\n", "# Iterate through all commits\n", "for i in tqdm(range(git_history.get_length())):\n", "    # Get the prompt for the current commit\n", "    prompt = git_history.get_prompt(i)\n", "\n", "    # Count tokens for this prompt\n", "    token_count = token_counter.count_tokens(prompt)\n", "\n", "    # Add to total and list of counts\n", "    total_tokens += token_count\n", "    token_counts.append(token_count)\n", "\n", "# Print results\n", "print(f\"Total tokens across all commits: {total_tokens}\")\n", "print(f\"Average tokens per commit: {total_tokens / git_history.get_length():.2f}\")\n", "\n", "# Calculate and print percentiles\n", "import numpy as np\n", "\n", "percentiles = [0, 10, 25, 50, 75, 90, 100]\n", "token_percentiles = np.percentile(token_counts, percentiles)\n", "\n", "print(\"\\nToken count percentiles:\")\n", "print(\"========================\")\n", "for p, value in zip(percentiles, token_percentiles):\n", "    print(f\"{p:3d}th percentile: {value:.0f} tokens\")\n", "\n", "# Optional: Plot a histogram of token counts\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(token_counts, bins=50, edgecolor=\"black\")\n", "plt.title(\"Distribution of Token Counts per Commit\")\n", "plt.xlabel(\"Token Count\")\n", "plt.ylabel(\"Frequency\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Overlap thresh. determination (32%)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 12500/12500 [00:01<00:00, 7922.97it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Average overlap rate: 5.17%\n", "Median overlap rate: 4.00%\n", "Min overlap rate: 0.00%\n", "Max overlap rate: 53.00%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from thefuzz import fuzz\n", "from tqdm import tqdm\n", "\n", "# Assuming you have already created a GitHistory object named 'git_history'\n", "# If not, uncomment and modify the following line:\n", "git_history = GitHistory.from_json(\"/home/<USER>/zhuoran/commit_msgs/commits.json\")\n", "\n", "total_commits = git_history.get_length()\n", "overlap_rates = []\n", "\n", "for i in tqdm(range(total_commits)):\n", "    current_commit_msg = git_history.get_commit_message(i)\n", "\n", "    # Get the 32 preceding commits (or fewer if not available)\n", "    start_index = max(0, i - 32)\n", "    preceding_commits = git_history.get_past_n_commits(start_index, 32)\n", "\n", "    # Concatenate the messages of the preceding commits\n", "    preceding_msgs = \" \".join([commit[\"message\"] for commit in preceding_commits])\n", "\n", "    # Calculate the fuzzy overlap rate\n", "    overlap_rate = fuzz.ratio(current_commit_msg, preceding_msgs)\n", "\n", "    overlap_rates.append(overlap_rate)\n", "\n", "# Calculate and print some statistics\n", "import numpy as np\n", "\n", "print(f\"Average overlap rate: {np.mean(overlap_rates):.2f}%\")\n", "print(f\"Median overlap rate: {np.median(overlap_rates):.2f}%\")\n", "print(f\"Min overlap rate: {np.min(overlap_rates):.2f}%\")\n", "print(f\"Max overlap rate: {np.max(overlap_rates):.2f}%\")\n", "\n", "# Optional: Plot a histogram of overlap rates\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(overlap_rates, bins=20, edgecolor=\"black\")\n", "plt.title(\"Distribution of Fuzzy Overlap Rates\")\n", "plt.xlabel(\"Overlap Rate (%)\")\n", "plt.ylabel(\"Frequency\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Percentiles of overlap rates:\n", "============================\n", "0.00th percentile: 0.00%\n", "5.00th percentile: 1.00%\n", "10.00th percentile: 1.00%\n", "15.00th percentile: 1.00%\n", "20.00th percentile: 1.00%\n", "25.00th percentile: 1.00%\n", "30.00th percentile: 2.00%\n", "35.00th percentile: 2.00%\n", "40.00th percentile: 3.00%\n", "45.00th percentile: 3.00%\n", "50.00th percentile: 4.00%\n", "55.00th percentile: 4.00%\n", "60.00th percentile: 5.00%\n", "65.00th percentile: 5.00%\n", "70.00th percentile: 6.00%\n", "75.00th percentile: 7.00%\n", "80.00th percentile: 8.00%\n", "85.00th percentile: 10.00%\n", "90.00th percentile: 12.00%\n", "95.00th percentile: 16.00%\n", "96.00th percentile: 17.00%\n", "97.00th percentile: 18.00%\n", "98.00th percentile: 21.00%\n", "99.00th percentile: 25.00%\n", "99.50th percentile: 27.00%\n", "99.80th percentile: 30.00%\n", "99.90th percentile: 32.00%\n", "100.00th percentile: 53.00%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "\n", "percentiles = list(range(0, 96, 5)) + [96, 97, 98, 99, 99.5, 99.8, 99.9, 100]\n", "overlap_percentiles = np.percentile(overlap_rates, percentiles)\n", "\n", "print(\"Percentiles of overlap rates:\")\n", "print(\"============================\")\n", "for p, value in zip(percentiles, overlap_percentiles):\n", "    print(f\"{p:.02f}th percentile: {value:.2f}%\")\n", "\n", "# Optional: Plot a cumulative distribution function (CDF)\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(overlap_percentiles, percentiles, marker=\"o\")\n", "plt.title(\"Cumulative Distribution Function of Overlap Rates\")\n", "plt.xlabel(\"Overlap Rate (%)\")\n", "plt.ylabel(\"Percentile\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Full set"]}, {"cell_type": "code", "execution_count": 221, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.prompt_builder import GitHistory\n", "\n", "git_history = GitHistory.from_json(\n", "    \"/mnt/efs/augment/user/zhuoran/commit_msgs/commits.json\"\n", ")"]}, {"cell_type": "code", "execution_count": 240, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total evaluation items: 330\n", "Reason: largest_diff\n", "\n", "Commit index: 8233\n", "Diff stats: +0, -4854783036, total: 4854783036\n", "Folder percentages:\n", "  third_party: 93.66%\n", "  models: 4.91%\n", "  base: 0.96%\n", "  services: 0.36%\n", "  tools: 0.12%\n", "\n", "Commit index: 12404\n", "Diff stats: +488057424, -395575646, total: 883633070\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 8730\n", "Diff stats: +289498859, -0, total: 289498859\n", "Folder percentages:\n", "  third_party: 100.00%\n", "\n", "Commit index: 10805\n", "Diff stats: +153615, -273384720, total: 273538335\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 12405\n", "Diff stats: +14250, -154927140, total: 154941390\n", "Folder percentages:\n", "  research: 93.86%\n", "  third_party: 5.09%\n", "  tools: 0.53%\n", "  base: 0.35%\n", "  services: 0.18%\n", "\n", "\n", "\n", "Reason: smallest_diff\n", "\n", "Commit index: 219\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 236\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 242\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 336\n", "Diff stats: +0, -1, total: 1\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 425\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "\n", "\n", "Reason: clients_latest\n", "\n", "Commit index: 0\n", "Diff stats: +21, -875, total: 896\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 5\n", "Diff stats: +76, -0, total: 76\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 15\n", "Diff stats: +9, -1, total: 10\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 18\n", "Diff stats: +26, -1, total: 27\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 22\n", "Diff stats: +26, -28, total: 54\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 32\n", "Diff stats: +237, -93, total: 330\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 41\n", "Diff stats: +20, -10, total: 30\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 42\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 43\n", "Diff stats: +238, -21, total: 259\n", "Folder percentages:\n", "  clients: 71.43%\n", "  services: 28.57%\n", "\n", "Commit index: 57\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 66\n", "Diff stats: +1, -4, total: 5\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 67\n", "Diff stats: +46, -6, total: 52\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 70\n", "Diff stats: +8, -2, total: 10\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 71\n", "Diff stats: +14, -14, total: 28\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 81\n", "Diff stats: +800, -870, total: 1670\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 82\n", "Diff stats: +25, -7, total: 32\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 83\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 85\n", "Diff stats: +118, -6, total: 124\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 86\n", "Diff stats: +34, -6, total: 40\n", "Folder percentages:\n", "  clients: 50.00%\n", "  services: 50.00%\n", "\n", "Commit index: 88\n", "Diff stats: +75, -3, total: 78\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 90\n", "Diff stats: +414, -258, total: 672\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 92\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 93\n", "Diff stats: +1332, -132, total: 1464\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 94\n", "Diff stats: +1480, -285, total: 1765\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 95\n", "Diff stats: +210, -10, total: 220\n", "Folder percentages:\n", "  clients: 60.00%\n", "  services: 40.00%\n", "\n", "Commit index: 108\n", "Diff stats: +2, -0, total: 2\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 109\n", "Diff stats: +28, -18, total: 46\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 110\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 112\n", "Diff stats: +46, -10, total: 56\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "Commit index: 113\n", "Diff stats: +112, -8, total: 120\n", "Folder percentages:\n", "  clients: 100.00%\n", "\n", "\n", "\n", "Reason: deploy_latest\n", "\n", "Commit index: 1\n", "Diff stats: +14, -0, total: 14\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 4\n", "Diff stats: +4, -3, total: 7\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 6\n", "Diff stats: +96, -114, total: 210\n", "Folder percentages:\n", "  services: 66.67%\n", "  deploy: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 8\n", "Diff stats: +32, -14, total: 46\n", "Folder percentages:\n", "  deploy: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 20\n", "Diff stats: +7, -0, total: 7\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 27\n", "Diff stats: +255, -90, total: 345\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 30\n", "Diff stats: +133, -308, total: 441\n", "Folder percentages:\n", "  services: 57.14%\n", "  deploy: 42.86%\n", "\n", "Commit index: 69\n", "Diff stats: +11, -0, total: 11\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 73\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 79\n", "Diff stats: +1332, -108, total: 1440\n", "Folder percentages:\n", "  services: 88.89%\n", "  deploy: 11.11%\n", "\n", "Commit index: 84\n", "Diff stats: +10, -0, total: 10\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 98\n", "Diff stats: +10, -0, total: 10\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 103\n", "Diff stats: +0, -43, total: 43\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 123\n", "Diff stats: +928, -88, total: 1016\n", "Folder percentages:\n", "  services: 62.50%\n", "  tools: 25.00%\n", "  deploy: 12.50%\n", "\n", "Commit index: 150\n", "Diff stats: +154, -35, total: 189\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 170\n", "Diff stats: +5533, -1012, total: 6545\n", "Folder percentages:\n", "  services: 72.73%\n", "  deploy: 18.18%\n", "  tools: 9.09%\n", "\n", "Commit index: 204\n", "Diff stats: +10, -0, total: 10\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 295\n", "Diff stats: +13498, -14331, total: 27829\n", "Folder percentages:\n", "  services: 64.71%\n", "  deploy: 29.41%\n", "  base: 5.88%\n", "\n", "Commit index: 303\n", "Diff stats: +770, -0, total: 770\n", "Folder percentages:\n", "  services: 85.71%\n", "  deploy: 14.29%\n", "\n", "Commit index: 317\n", "Diff stats: +6, -6, total: 12\n", "Folder percentages:\n", "  deploy: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 322\n", "Diff stats: +2522, -702, total: 3224\n", "Folder percentages:\n", "  services: 84.62%\n", "  base: 7.69%\n", "  deploy: 7.69%\n", "\n", "Commit index: 323\n", "Diff stats: +4, -1, total: 5\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 387\n", "Diff stats: +944, -440, total: 1384\n", "Folder percentages:\n", "  services: 62.50%\n", "  deploy: 25.00%\n", "  tools: 12.50%\n", "\n", "Commit index: 402\n", "Diff stats: +70, -0, total: 70\n", "Folder percentages:\n", "  deploy: 50.00%\n", "  services: 50.00%\n", "\n", "Commit index: 428\n", "Diff stats: +11, -0, total: 11\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 458\n", "Diff stats: +544, -232, total: 776\n", "Folder percentages:\n", "  services: 87.50%\n", "  deploy: 12.50%\n", "\n", "Commit index: 479\n", "Diff stats: +54, -48, total: 102\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 509\n", "Diff stats: +2, -3, total: 5\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 512\n", "Diff stats: +10, -0, total: 10\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "Commit index: 521\n", "Diff stats: +16, -0, total: 16\n", "Folder percentages:\n", "  deploy: 100.00%\n", "\n", "\n", "\n", "Reason: base_latest\n", "\n", "Commit index: 2\n", "Diff stats: +14, -2, total: 16\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 13\n", "Diff stats: +4, -4, total: 8\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 23\n", "Diff stats: +29, -71, total: 100\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 51\n", "Diff stats: +13, -16, total: 29\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 87\n", "Diff stats: +388, -676, total: 1064\n", "Folder percentages:\n", "  services: 75.00%\n", "  base: 25.00%\n", "\n", "Commit index: 102\n", "Diff stats: +2755, -5, total: 2760\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 137\n", "Diff stats: +2, -1, total: 3\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 182\n", "Diff stats: +12360, -4320, total: 16680\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 183\n", "Diff stats: +5572, -1295, total: 6867\n", "Folder percentages:\n", "  base: 85.71%\n", "  services: 14.29%\n", "\n", "Commit index: 210\n", "Diff stats: +2, -0, total: 2\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 217\n", "Diff stats: +16, -0, total: 16\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 250\n", "Diff stats: +2520, -1206, total: 3726\n", "Folder percentages:\n", "  services: 66.67%\n", "  base: 33.33%\n", "\n", "Commit index: 296\n", "Diff stats: +297, -135, total: 432\n", "Folder percentages:\n", "  services: 77.78%\n", "  base: 11.11%\n", "  clients: 11.11%\n", "\n", "Commit index: 299\n", "Diff stats: +9, -3, total: 12\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 329\n", "Diff stats: +26, -20, total: 46\n", "Folder percentages:\n", "  base: 50.00%\n", "  services: 50.00%\n", "\n", "Commit index: 343\n", "Diff stats: +2, -2, total: 4\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 348\n", "Diff stats: +126, -216, total: 342\n", "Folder percentages:\n", "  base: 77.78%\n", "  services: 22.22%\n", "\n", "Commit index: 375\n", "Diff stats: +6723, -3267, total: 9990\n", "Folder percentages:\n", "  services: 66.67%\n", "  tools: 25.93%\n", "  base: 7.41%\n", "\n", "Commit index: 394\n", "Diff stats: +2, -2, total: 4\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 396\n", "Diff stats: +18166, -6882, total: 25048\n", "Folder percentages:\n", "  services: 66.13%\n", "  tools: 29.03%\n", "  base: 4.84%\n", "\n", "Commit index: 397\n", "Diff stats: +6, -4, total: 10\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 404\n", "Diff stats: +2795, -377, total: 3172\n", "Folder percentages:\n", "  services: 84.62%\n", "  base: 15.38%\n", "\n", "Commit index: 409\n", "Diff stats: +240, -144, total: 384\n", "Folder percentages:\n", "  base: 50.00%\n", "  services: 50.00%\n", "\n", "Commit index: 424\n", "Diff stats: +84, -133, total: 217\n", "Folder percentages:\n", "  services: 85.71%\n", "  base: 14.29%\n", "\n", "Commit index: 427\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 432\n", "Diff stats: +27, -3, total: 30\n", "Folder percentages:\n", "  base: 100.00%\n", "\n", "Commit index: 434\n", "Diff stats: +6916, -325, total: 7241\n", "Folder percentages:\n", "  services: 61.54%\n", "  base: 38.46%\n", "\n", "Commit index: 443\n", "Diff stats: +582, -276, total: 858\n", "Folder percentages:\n", "  clients: 66.67%\n", "  base: 33.33%\n", "\n", "Commit index: 463\n", "Diff stats: +948, -6, total: 954\n", "Folder percentages:\n", "  services: 66.67%\n", "  base: 33.33%\n", "\n", "Commit index: 466\n", "Diff stats: +14322, -273, total: 14595\n", "Folder percentages:\n", "  services: 71.43%\n", "  base: 9.52%\n", "  Cargo.lock: 4.76%\n", "  Cargo.toml: 4.76%\n", "  MODULE.bazel: 4.76%\n", "  tools: 4.76%\n", "\n", "\n", "\n", "Reason: data_latest\n", "\n", "Commit index: 3\n", "Diff stats: +605, -0, total: 605\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "Commit index: 232\n", "Diff stats: +690, -0, total: 690\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "Commit index: 255\n", "Diff stats: +8493, -0, total: 8493\n", "Folder percentages:\n", "  data: 100.00%\n", "\n", "Commit index: 435\n", "Diff stats: +2282, -2464, total: 4746\n", "Folder percentages:\n", "  clients: 35.71%\n", "  services: 28.57%\n", "  data: 21.43%\n", "  research: 14.29%\n", "\n", "Commit index: 10391\n", "Diff stats: +16080, -12261, total: 28341\n", "Folder percentages:\n", "  base: 31.34%\n", "  third_party: 23.88%\n", "  tools: 19.40%\n", "  services: 11.94%\n", "  deploy: 4.48%\n", "  models: 4.48%\n", "  clients: 2.99%\n", "  data: 1.49%\n", "\n", "\n", "\n", "Reason: services_latest\n", "\n", "Commit index: 7\n", "Diff stats: +77, -42, total: 119\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 11\n", "Diff stats: +3, -2, total: 5\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 14\n", "Diff stats: +2, -1, total: 3\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 21\n", "Diff stats: +140, -134, total: 274\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 24\n", "Diff stats: +222, -290, total: 512\n", "Folder percentages:\n", "  base: 50.00%\n", "  services: 50.00%\n", "\n", "Commit index: 25\n", "Diff stats: +1, -31, total: 32\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 26\n", "Diff stats: +189, -3, total: 192\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 28\n", "Diff stats: +672, -0, total: 672\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 29\n", "Diff stats: +192, -138, total: 330\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 31\n", "Diff stats: +416, -228, total: 644\n", "Folder percentages:\n", "  services: 75.00%\n", "  base: 25.00%\n", "\n", "Commit index: 40\n", "Diff stats: +154, -10, total: 164\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 44\n", "Diff stats: +6, -2, total: 8\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 45\n", "Diff stats: +6, -3, total: 9\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 46\n", "Diff stats: +28, -8, total: 36\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 47\n", "Diff stats: +2, -1, total: 3\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 48\n", "Diff stats: +3, -0, total: 3\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 53\n", "Diff stats: +8848, -210, total: 9058\n", "Folder percentages:\n", "  services: 85.71%\n", "  base: 14.29%\n", "\n", "Commit index: 54\n", "Diff stats: +20, -0, total: 20\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 55\n", "Diff stats: +5, -2, total: 7\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 56\n", "Diff stats: +4, -1, total: 5\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 58\n", "Diff stats: +40, -29, total: 69\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 60\n", "Diff stats: +89, -4, total: 93\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 61\n", "Diff stats: +2280, -276, total: 2556\n", "Folder percentages:\n", "  services: 66.67%\n", "  base: 33.33%\n", "\n", "Commit index: 62\n", "Diff stats: +30, -0, total: 30\n", "Folder percentages:\n", "  services: 66.67%\n", "  base: 33.33%\n", "\n", "Commit index: 63\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 64\n", "Diff stats: +1710, -660, total: 2370\n", "Folder percentages:\n", "  services: 50.00%\n", "  MODULE.bazel: 16.67%\n", "  go.mod: 16.67%\n", "  go.sum: 16.67%\n", "\n", "Commit index: 65\n", "Diff stats: +146, -94, total: 240\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 72\n", "Diff stats: +978, -0, total: 978\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "Commit index: 74\n", "Diff stats: +485, -105, total: 590\n", "Folder percentages:\n", "  services: 80.00%\n", "  Cargo.lock: 20.00%\n", "\n", "Commit index: 75\n", "Diff stats: +2596, -627, total: 3223\n", "Folder percentages:\n", "  services: 100.00%\n", "\n", "\n", "\n", "Reason: tools_latest\n", "\n", "Commit index: 9\n", "Diff stats: +26, -8, total: 34\n", "Folder percentages:\n", "  services: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 12\n", "Diff stats: +2, -1, total: 3\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 49\n", "Diff stats: +0, -20, total: 20\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 59\n", "Diff stats: +111, -42, total: 153\n", "Folder percentages:\n", "  services: 66.67%\n", "  tools: 33.33%\n", "\n", "Commit index: 68\n", "Diff stats: +1704, -88, total: 1792\n", "Folder percentages:\n", "  clients: 62.50%\n", "  services: 25.00%\n", "  tools: 12.50%\n", "\n", "Commit index: 77\n", "Diff stats: +792, -12, total: 804\n", "Folder percentages:\n", "  services: 75.00%\n", "  tools: 25.00%\n", "\n", "Commit index: 100\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 119\n", "Diff stats: +4, -4, total: 8\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 122\n", "Diff stats: +5, -0, total: 5\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 129\n", "Diff stats: +0, -2, total: 2\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 130\n", "Diff stats: +96, -0, total: 96\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 134\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 138\n", "Diff stats: +306, -36, total: 342\n", "Folder percentages:\n", "  research: 50.00%\n", "  tools: 33.33%\n", "  MODULE.bazel.lock: 16.67%\n", "\n", "Commit index: 140\n", "Diff stats: +7467, -4522, total: 11989\n", "Folder percentages:\n", "  services: 78.95%\n", "  base: 15.79%\n", "  tools: 5.26%\n", "\n", "Commit index: 145\n", "Diff stats: +4048, -363, total: 4411\n", "Folder percentages:\n", "  tools: 90.91%\n", "  services: 9.09%\n", "\n", "Commit index: 146\n", "Diff stats: +4250, -50, total: 4300\n", "Folder percentages:\n", "  clients: 70.00%\n", "  services: 20.00%\n", "  tools: 10.00%\n", "\n", "Commit index: 157\n", "Diff stats: +5, -0, total: 5\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 161\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 162\n", "Diff stats: +5, -2, total: 7\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 165\n", "Diff stats: +13, -0, total: 13\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 168\n", "Diff stats: +170, -460, total: 630\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 169\n", "Diff stats: +0, -3, total: 3\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 172\n", "Diff stats: +2, -2, total: 4\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 179\n", "Diff stats: +9288, -10584, total: 19872\n", "Folder percentages:\n", "  research: 33.33%\n", "  tools: 33.33%\n", "  experimental: 27.78%\n", "  .github: 5.56%\n", "\n", "Commit index: 181\n", "Diff stats: +10, -4, total: 14\n", "Folder percentages:\n", "  services: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 191\n", "Diff stats: +26, -18, total: 44\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 222\n", "Diff stats: +9, -1, total: 10\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 225\n", "Diff stats: +15, -0, total: 15\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 244\n", "Diff stats: +5, -7, total: 12\n", "Folder percentages:\n", "  tools: 100.00%\n", "\n", "Commit index: 248\n", "Diff stats: +924, -792, total: 1716\n", "Folder percentages:\n", "  services: 90.91%\n", "  tools: 9.09%\n", "\n", "\n", "\n", "Reason: research_latest\n", "\n", "Commit index: 10\n", "Diff stats: +8, -78, total: 86\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 16\n", "Diff stats: +18, -34, total: 52\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 17\n", "Diff stats: +531, -0, total: 531\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 33\n", "Diff stats: +38, -1, total: 39\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 34\n", "Diff stats: +58797494, -58659811, total: 117457305\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 35\n", "Diff stats: +4, -0, total: 4\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 36\n", "Diff stats: +9, -7, total: 16\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 37\n", "Diff stats: +0, -132, total: 132\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 38\n", "Diff stats: +6986, -21, total: 7007\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 39\n", "Diff stats: +38280, -38280, total: 76560\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 50\n", "Diff stats: +12, -48, total: 60\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 52\n", "Diff stats: +152, -216, total: 368\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 91\n", "Diff stats: +156, -36, total: 192\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 149\n", "Diff stats: +610, -112, total: 722\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 153\n", "Diff stats: +2, -1, total: 3\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 159\n", "Diff stats: +885, -35, total: 920\n", "Folder percentages:\n", "  base: 60.00%\n", "  research: 40.00%\n", "\n", "Commit index: 186\n", "Diff stats: +3, -0, total: 3\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 253\n", "Diff stats: +40, -25, total: 65\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 254\n", "Diff stats: +1, -2, total: 3\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 259\n", "Diff stats: +15, -12, total: 27\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 271\n", "Diff stats: +742, -784, total: 1526\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 276\n", "Diff stats: +1386, -1364, total: 2750\n", "Folder percentages:\n", "  clients: 63.64%\n", "  services: 18.18%\n", "  base: 9.09%\n", "  research: 9.09%\n", "\n", "Commit index: 300\n", "Diff stats: +2, -2, total: 4\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 309\n", "Diff stats: +24, -24, total: 48\n", "Folder percentages:\n", "  tools: 50.00%\n", "  MODULE.bazel.lock: 25.00%\n", "  research: 25.00%\n", "\n", "Commit index: 321\n", "Diff stats: +16, -2, total: 18\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 325\n", "Diff stats: +24, -24, total: 48\n", "Folder percentages:\n", "  tools: 50.00%\n", "  MODULE.bazel.lock: 25.00%\n", "  research: 25.00%\n", "\n", "Commit index: 337\n", "Diff stats: +1111201, -1746927, total: 2858128\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 371\n", "Diff stats: +128, -8, total: 136\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "Commit index: 406\n", "Diff stats: +180, -312, total: 492\n", "Folder percentages:\n", "  research: 66.67%\n", "  experimental: 33.33%\n", "\n", "Commit index: 419\n", "Diff stats: +114, -9, total: 123\n", "Folder percentages:\n", "  research: 100.00%\n", "\n", "\n", "\n", "Reason: experimental_latest\n", "\n", "Commit index: 19\n", "Diff stats: +1428, -32473, total: 33901\n", "Folder percentages:\n", "  experimental: 71.43%\n", "  research: 28.57%\n", "\n", "Commit index: 78\n", "Diff stats: +1770, -990, total: 2760\n", "Folder percentages:\n", "  base: 90.00%\n", "  experimental: 10.00%\n", "\n", "Commit index: 80\n", "Diff stats: +588, -336, total: 924\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 89\n", "Diff stats: +5753, -264, total: 6017\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 121\n", "Diff stats: +387, -225, total: 612\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 133\n", "Diff stats: +20595, -0, total: 20595\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 152\n", "Diff stats: +600, -0, total: 600\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 154\n", "Diff stats: +210, -0, total: 210\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 155\n", "Diff stats: +300, -294, total: 594\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 158\n", "Diff stats: +164, -42, total: 206\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 178\n", "Diff stats: +37114, -16764, total: 53878\n", "Folder percentages:\n", "  research: 77.27%\n", "  experimental: 18.18%\n", "  .secrets.baseline: 4.55%\n", "\n", "Commit index: 190\n", "Diff stats: +2064, -0, total: 2064\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 205\n", "Diff stats: +608, -336, total: 944\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 206\n", "Diff stats: +379, -0, total: 379\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 207\n", "Diff stats: +4, -1, total: 5\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 216\n", "Diff stats: +50, -17, total: 67\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 218\n", "Diff stats: +1025, -65, total: 1090\n", "Folder percentages:\n", "  experimental: 80.00%\n", "  research: 20.00%\n", "\n", "Commit index: 219\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 224\n", "Diff stats: +3543, -0, total: 3543\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 228\n", "Diff stats: +126, -0, total: 126\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 251\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 257\n", "Diff stats: +524, -0, total: 524\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 264\n", "Diff stats: +12645, -3726, total: 16371\n", "Folder percentages:\n", "  research: 88.89%\n", "  experimental: 11.11%\n", "\n", "Commit index: 267\n", "Diff stats: +22194, -23346, total: 45540\n", "Folder percentages:\n", "  research: 61.11%\n", "  experimental: 38.89%\n", "\n", "Commit index: 313\n", "Diff stats: +1440, -0, total: 1440\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 318\n", "Diff stats: +28, -0, total: 28\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 332\n", "Diff stats: +0, -3, total: 3\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 333\n", "Diff stats: +54, -17, total: 71\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "Commit index: 346\n", "Diff stats: +374, -308, total: 682\n", "Folder percentages:\n", "  research: 90.91%\n", "  experimental: 9.09%\n", "\n", "Commit index: 353\n", "Diff stats: +56, -7, total: 63\n", "Folder percentages:\n", "  experimental: 100.00%\n", "\n", "\n", "\n", "Reason: .github_latest\n", "\n", "Commit index: 287\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 357\n", "Diff stats: +273, -210, total: 483\n", "Folder percentages:\n", "  clients: 71.43%\n", "  .github: 28.57%\n", "\n", "Commit index: 952\n", "Diff stats: +0, -162, total: 162\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 1092\n", "Diff stats: +6, -6, total: 12\n", "Folder percentages:\n", "  .github: 50.00%\n", "  research: 50.00%\n", "\n", "Commit index: 1298\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 1427\n", "Diff stats: +0, -56, total: 56\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 1549\n", "Diff stats: +332370, -48150, total: 380520\n", "Folder percentages:\n", "  research: 53.33%\n", "  experimental: 41.11%\n", "  base: 2.22%\n", "  .github: 1.11%\n", "  .pre-commit-config.yaml: 1.11%\n", "  tools: 1.11%\n", "\n", "Commit index: 2801\n", "Diff stats: +38, -24, total: 62\n", "Folder percentages:\n", "  .github: 50.00%\n", "  tools: 50.00%\n", "\n", "Commit index: 2852\n", "Diff stats: +9, -4, total: 13\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 2854\n", "Diff stats: +1508, -16, total: 1524\n", "Folder percentages:\n", "  tools: 50.00%\n", "  .github: 25.00%\n", "  experimental: 25.00%\n", "\n", "Commit index: 2993\n", "Diff stats: +12, -10, total: 22\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 3034\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 3082\n", "Diff stats: +366177, -309225, total: 675402\n", "Folder percentages:\n", "  tools: 38.10%\n", "  .github: 14.29%\n", "  clients: 9.52%\n", "  services: 9.52%\n", "  MODULE.bazel: 4.76%\n", "  MODULE.bazel.lock: 4.76%\n", "  deploy: 4.76%\n", "  package.json: 4.76%\n", "  pnpm-lock.yaml: 4.76%\n", "  research: 4.76%\n", "\n", "Commit index: 3124\n", "Diff stats: +306264, -362607, total: 668871\n", "Folder percentages:\n", "  tools: 38.10%\n", "  .github: 14.29%\n", "  clients: 9.52%\n", "  services: 9.52%\n", "  MODULE.bazel: 4.76%\n", "  MODULE.bazel.lock: 4.76%\n", "  deploy: 4.76%\n", "  package.json: 4.76%\n", "  pnpm-lock.yaml: 4.76%\n", "  research: 4.76%\n", "\n", "Commit index: 3150\n", "Diff stats: +365232, -308889, total: 674121\n", "Folder percentages:\n", "  tools: 38.10%\n", "  .github: 14.29%\n", "  clients: 9.52%\n", "  services: 9.52%\n", "  MODULE.bazel: 4.76%\n", "  MODULE.bazel.lock: 4.76%\n", "  deploy: 4.76%\n", "  package.json: 4.76%\n", "  pnpm-lock.yaml: 4.76%\n", "  research: 4.76%\n", "\n", "Commit index: 3219\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 3595\n", "Diff stats: +20, -8, total: 28\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 3632\n", "Diff stats: +132, -393, total: 525\n", "Folder percentages:\n", "  clients: 66.67%\n", "  .github: 33.33%\n", "\n", "Commit index: 3673\n", "Diff stats: +12, -4, total: 16\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 3679\n", "Diff stats: +12, -4, total: 16\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 3770\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 3851\n", "Diff stats: +44, -44, total: 88\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 4022\n", "Diff stats: +75, -75, total: 150\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 4048\n", "Diff stats: +2, -1, total: 3\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 4141\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 4144\n", "Diff stats: +2849, -0, total: 2849\n", "Folder percentages:\n", "  tools: 85.71%\n", "  .github: 14.29%\n", "\n", "Commit index: 4158\n", "Diff stats: +870, -15, total: 885\n", "Folder percentages:\n", "  .github: 60.00%\n", "  clients: 40.00%\n", "\n", "Commit index: 4331\n", "Diff stats: +1, -3, total: 4\n", "Folder percentages:\n", "  .github: 100.00%\n", "\n", "Commit index: 4471\n", "Diff stats: +4, -2, total: 6\n", "Folder percentages:\n", "  .github: 50.00%\n", "  .pre-commit-config.yaml: 50.00%\n", "\n", "Commit index: 4590\n", "Diff stats: +372, -4, total: 376\n", "Folder percentages:\n", "  research: 75.00%\n", "  .github: 25.00%\n", "\n", "\n", "\n", "Reason: models_latest\n", "\n", "Commit index: 603\n", "Diff stats: +8151, -104, total: 8255\n", "Folder percentages:\n", "  services: 53.85%\n", "  deploy: 23.08%\n", "  models: 23.08%\n", "\n", "Commit index: 683\n", "Diff stats: +86, -26, total: 112\n", "Folder percentages:\n", "  base: 50.00%\n", "  models: 50.00%\n", "\n", "Commit index: 2385\n", "Diff stats: +2960, -660, total: 3620\n", "Folder percentages:\n", "  services: 75.00%\n", "  base: 20.00%\n", "  models: 5.00%\n", "\n", "Commit index: 2644\n", "Diff stats: +156, -6, total: 162\n", "Folder percentages:\n", "  services: 66.67%\n", "  models: 33.33%\n", "\n", "Commit index: 2732\n", "Diff stats: +1856, -8, total: 1864\n", "Folder percentages:\n", "  services: 62.50%\n", "  models: 37.50%\n", "\n", "Commit index: 2744\n", "Diff stats: +148, -24, total: 172\n", "Folder percentages:\n", "  models: 100.00%\n", "\n", "Commit index: 2762\n", "Diff stats: +10846, -1122, total: 11968\n", "Folder percentages:\n", "  base: 47.06%\n", "  services: 35.29%\n", "  models: 17.65%\n", "\n", "Commit index: 2786\n", "Diff stats: +13520, -8700, total: 22220\n", "Folder percentages:\n", "  experimental: 40.00%\n", "  research: 35.00%\n", "  base: 20.00%\n", "  models: 5.00%\n", "\n", "Commit index: 2816\n", "Diff stats: +2780, -160, total: 2940\n", "Folder percentages:\n", "  models: 60.00%\n", "  base: 20.00%\n", "  experimental: 10.00%\n", "  tools: 10.00%\n", "\n", "Commit index: 3035\n", "Diff stats: +189, -81, total: 270\n", "Folder percentages:\n", "  services: 55.56%\n", "  models: 33.33%\n", "  base: 11.11%\n", "\n", "Commit index: 3060\n", "Diff stats: +81, -189, total: 270\n", "Folder percentages:\n", "  services: 55.56%\n", "  models: 33.33%\n", "  base: 11.11%\n", "\n", "Commit index: 3084\n", "Diff stats: +189, -81, total: 270\n", "Folder percentages:\n", "  services: 55.56%\n", "  models: 33.33%\n", "  base: 11.11%\n", "\n", "Commit index: 3129\n", "Diff stats: +3840, -672, total: 4512\n", "Folder percentages:\n", "  services: 62.50%\n", "  models: 25.00%\n", "  base: 12.50%\n", "\n", "Commit index: 3668\n", "Diff stats: +10908, -4455, total: 15363\n", "Folder percentages:\n", "  services: 70.37%\n", "  models: 14.81%\n", "  MODULE.bazel: 3.70%\n", "  base: 3.70%\n", "  go.mod: 3.70%\n", "  go.sum: 3.70%\n", "\n", "Commit index: 3774\n", "Diff stats: +658, -406, total: 1064\n", "Folder percentages:\n", "  models: 57.14%\n", "  services: 42.86%\n", "\n", "Commit index: 3798\n", "Diff stats: +406, -658, total: 1064\n", "Folder percentages:\n", "  models: 57.14%\n", "  services: 42.86%\n", "\n", "Commit index: 3949\n", "Diff stats: +2128, -924, total: 3052\n", "Folder percentages:\n", "  base: 42.86%\n", "  research: 35.71%\n", "  models: 14.29%\n", "  experimental: 7.14%\n", "\n", "Commit index: 4134\n", "Diff stats: +354, -0, total: 354\n", "Folder percentages:\n", "  models: 100.00%\n", "\n", "Commit index: 4799\n", "Diff stats: +150, -48, total: 198\n", "Folder percentages:\n", "  base: 33.33%\n", "  models: 33.33%\n", "  services: 16.67%\n", "  tools: 16.67%\n", "\n", "Commit index: 4843\n", "Diff stats: +392, -16, total: 408\n", "Folder percentages:\n", "  models: 50.00%\n", "  services: 50.00%\n", "\n", "Commit index: 4925\n", "Diff stats: +496, -160, total: 656\n", "Folder percentages:\n", "  models: 75.00%\n", "  services: 25.00%\n", "\n", "Commit index: 5217\n", "Diff stats: +1118950, -1132950, total: 2251900\n", "Folder percentages:\n", "  tools: 35.43%\n", "  base: 24.57%\n", "  services: 23.43%\n", "  deploy: 13.14%\n", "  models: 1.71%\n", "  .clang-format: 0.57%\n", "  clients: 0.57%\n", "  yamlfmt.yaml: 0.57%\n", "\n", "Commit index: 6481\n", "Diff stats: +2574, -171, total: 2745\n", "Folder percentages:\n", "  services: 88.89%\n", "  models: 11.11%\n", "\n", "Commit index: 6659\n", "Diff stats: +543, -0, total: 543\n", "Folder percentages:\n", "  models: 100.00%\n", "\n", "Commit index: 7765\n", "Diff stats: +74576, -74813, total: 149389\n", "Folder percentages:\n", "  services: 64.56%\n", "  models: 31.65%\n", "  base: 2.53%\n", "  tools: 1.27%\n", "\n", "Commit index: 7983\n", "Diff stats: +10296, -364, total: 10660\n", "Folder percentages:\n", "  base: 53.85%\n", "  models: 30.77%\n", "  research: 15.38%\n", "\n", "Commit index: 7984\n", "Diff stats: +132, -12, total: 144\n", "Folder percentages:\n", "  services: 50.00%\n", "  base: 33.33%\n", "  models: 16.67%\n", "\n", "Commit index: 8233\n", "Diff stats: +0, -4854783036, total: 4854783036\n", "Folder percentages:\n", "  third_party: 93.66%\n", "  models: 4.91%\n", "  base: 0.96%\n", "  services: 0.36%\n", "  tools: 0.12%\n", "\n", "Commit index: 8440\n", "Diff stats: +1802, -340, total: 2142\n", "Folder percentages:\n", "  models: 41.18%\n", "  services: 41.18%\n", "  experimental: 8.82%\n", "  base: 5.88%\n", "  research: 2.94%\n", "\n", "Commit index: 8567\n", "Diff stats: +1610, -575, total: 2185\n", "Folder percentages:\n", "  base: 34.78%\n", "  services: 30.43%\n", "  models: 21.74%\n", "  experimental: 8.70%\n", "  research: 4.35%\n", "\n", "\n", "\n", "Reason: third_party_latest\n", "\n", "Commit index: 1353\n", "Diff stats: +5688, -4812, total: 10500\n", "Folder percentages:\n", "  services: 58.33%\n", "  third_party: 25.00%\n", "  Cargo.lock: 8.33%\n", "  Cargo.toml: 8.33%\n", "\n", "Commit index: 1739\n", "Diff stats: +70219, -14706, total: 84925\n", "Folder percentages:\n", "  third_party: 88.37%\n", "  services: 4.65%\n", "  Cargo.lock: 2.33%\n", "  Cargo.toml: 2.33%\n", "  MODULE.bazel: 2.33%\n", "\n", "Commit index: 1975\n", "Diff stats: +1610, -574, total: 2184\n", "Folder percentages:\n", "  services: 57.14%\n", "  third_party: 28.57%\n", "  Cargo.lock: 7.14%\n", "  base: 7.14%\n", "\n", "Commit index: 2219\n", "Diff stats: +2870, -245, total: 3115\n", "Folder percentages:\n", "  services: 71.43%\n", "  third_party: 28.57%\n", "\n", "Commit index: 2338\n", "Diff stats: +22701, -22471, total: 45172\n", "Folder percentages:\n", "  services: 60.87%\n", "  third_party: 21.74%\n", "  Cargo.lock: 4.35%\n", "  Cargo.toml: 4.35%\n", "  base: 4.35%\n", "  tools: 4.35%\n", "\n", "Commit index: 2506\n", "Diff stats: +7120, -1410, total: 8530\n", "Folder percentages:\n", "  services: 50.00%\n", "  MODULE.bazel: 10.00%\n", "  go.mod: 10.00%\n", "  go.sum: 10.00%\n", "  third_party: 10.00%\n", "  tools: 10.00%\n", "\n", "Commit index: 2522\n", "Diff stats: +6440, -0, total: 6440\n", "Folder percentages:\n", "  clients: 60.00%\n", "  third_party: 40.00%\n", "\n", "Commit index: 3135\n", "Diff stats: +14246, -1292, total: 15538\n", "Folder percentages:\n", "  services: 88.24%\n", "  third_party: 5.88%\n", "  tools: 5.88%\n", "\n", "Commit index: 3471\n", "Diff stats: +20, -4, total: 24\n", "Folder percentages:\n", "  third_party: 100.00%\n", "\n", "Commit index: 4735\n", "Diff stats: +3612, -1246, total: 4858\n", "Folder percentages:\n", "  clients: 71.43%\n", "  .bazelrc: 7.14%\n", "  package.json: 7.14%\n", "  pnpm-lock.yaml: 7.14%\n", "  third_party: 7.14%\n", "\n", "Commit index: 4895\n", "Diff stats: +50232, -704, total: 50936\n", "Folder percentages:\n", "  tools: 62.50%\n", "  MODULE.bazel: 12.50%\n", "  MODULE.bazel.lock: 12.50%\n", "  third_party: 12.50%\n", "\n", "Commit index: 4905\n", "Diff stats: +57575, -20874, total: 78449\n", "Folder percentages:\n", "  research: 73.47%\n", "  tools: 16.33%\n", "  base: 4.08%\n", "  MODULE.bazel.lock: 2.04%\n", "  deploy: 2.04%\n", "  third_party: 2.04%\n", "\n", "Commit index: 4943\n", "Diff stats: +12578, -190, total: 12768\n", "Folder percentages:\n", "  tools: 94.74%\n", "  third_party: 5.26%\n", "\n", "Commit index: 5565\n", "Diff stats: +3753, -198, total: 3951\n", "Folder percentages:\n", "  tools: 55.56%\n", "  .trivy_allow_list.yaml: 11.11%\n", "  MODULE.bazel: 11.11%\n", "  deploy: 11.11%\n", "  third_party: 11.11%\n", "\n", "Commit index: 5895\n", "Diff stats: +816, -252, total: 1068\n", "Folder percentages:\n", "  services: 75.00%\n", "  third_party: 25.00%\n", "\n", "Commit index: 5903\n", "Diff stats: +5853960, -8228250, total: 14082210\n", "Folder percentages:\n", "  tools: 33.33%\n", "  services: 32.85%\n", "  third_party: 15.46%\n", "  base: 6.28%\n", "  deploy: 4.83%\n", "  .bazel_disk_version: 0.48%\n", "  .bazelrc: 0.48%\n", "  .bazelversion: 0.48%\n", "  BUILD: 0.48%\n", "  Cargo.Bazel.lock: 0.48%\n", "  Cargo.lock: 0.48%\n", "  Cargo.toml: 0.48%\n", "  MODULE.bazel: 0.48%\n", "  MODULE.bazel.lock: 0.48%\n", "  WORKSPACE: 0.48%\n", "  experimental: 0.48%\n", "  go.mod: 0.48%\n", "  go.sum: 0.48%\n", "  package.json: 0.48%\n", "  pnpm-lock.yaml: 0.48%\n", "\n", "Commit index: 6249\n", "Diff stats: +123984, -159012, total: 282996\n", "Folder percentages:\n", "  tools: 52.38%\n", "  services: 43.65%\n", "  third_party: 1.59%\n", "  .bazelrc: 0.79%\n", "  WORKSPACE: 0.79%\n", "  base: 0.79%\n", "\n", "Commit index: 6836\n", "Diff stats: +13662, -1034, total: 14696\n", "Folder percentages:\n", "  services: 77.27%\n", "  tools: 9.09%\n", "  Cargo.Bazel.lock: 4.55%\n", "  Cargo.lock: 4.55%\n", "  third_party: 4.55%\n", "\n", "Commit index: 6907\n", "Diff stats: +17206, -3262, total: 20468\n", "Folder percentages:\n", "  clients: 64.29%\n", "  third_party: 35.71%\n", "\n", "Commit index: 6944\n", "Diff stats: +2288, -585, total: 2873\n", "Folder percentages:\n", "  tools: 84.62%\n", "  WORKSPACE: 7.69%\n", "  third_party: 7.69%\n", "\n", "Commit index: 7184\n", "Diff stats: +1600, -430, total: 2030\n", "Folder percentages:\n", "  services: 60.00%\n", "  third_party: 20.00%\n", "  Cargo.Bazel.lock: 10.00%\n", "  Cargo.lock: 10.00%\n", "\n", "Commit index: 7338\n", "Diff stats: +458718, -305994, total: 764712\n", "Folder percentages:\n", "  third_party: 41.03%\n", "  services: 24.36%\n", "  tools: 17.95%\n", "  base: 10.26%\n", "  .bazelrc: 1.28%\n", "  Cargo.Bazel.lock: 1.28%\n", "  Cargo.lock: 1.28%\n", "  Cargo.toml: 1.28%\n", "  deploy: 1.28%\n", "\n", "Commit index: 8272\n", "Diff stats: +414, -42, total: 456\n", "Folder percentages:\n", "  base: 50.00%\n", "  third_party: 50.00%\n", "\n", "Commit index: 8274\n", "Diff stats: +2835, -63, total: 2898\n", "Folder percentages:\n", "  base: 55.56%\n", "  third_party: 22.22%\n", "  experimental: 11.11%\n", "  research: 11.11%\n", "\n", "Commit index: 8314\n", "Diff stats: +96, -0, total: 96\n", "Folder percentages:\n", "  third_party: 100.00%\n", "\n", "Commit index: 8652\n", "Diff stats: +1872, -28, total: 1900\n", "Folder percentages:\n", "  third_party: 100.00%\n", "\n", "Commit index: 8730\n", "Diff stats: +289498859, -0, total: 289498859\n", "Folder percentages:\n", "  third_party: 100.00%\n", "\n", "Commit index: 8827\n", "Diff stats: +123, -6, total: 129\n", "Folder percentages:\n", "  third_party: 100.00%\n", "\n", "Commit index: 8955\n", "Diff stats: +31616, -29692, total: 61308\n", "Folder percentages:\n", "  services: 65.38%\n", "  base: 15.38%\n", "  Cargo.Bazel.lock: 3.85%\n", "  Cargo.lock: 3.85%\n", "  Cargo.toml: 3.85%\n", "  third_party: 3.85%\n", "  tools: 3.85%\n", "\n", "Commit index: 9237\n", "Diff stats: +174, -3, total: 177\n", "Folder percentages:\n", "  services: 66.67%\n", "  third_party: 33.33%\n", "\n", "\n", "\n", "Reason: .vscode_latest\n", "\n", "Commit index: 2815\n", "Diff stats: +6, -6, total: 12\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 2954\n", "Diff stats: +2, -0, total: 2\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 3176\n", "Diff stats: +15, -2, total: 17\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 4726\n", "Diff stats: +1148, -616, total: 1764\n", "Folder percentages:\n", "  research: 85.71%\n", "  .vscode: 7.14%\n", "  pyrightconfig.ci.json: 7.14%\n", "\n", "Commit index: 7733\n", "Diff stats: +9042, -17271042, total: 17280084\n", "Folder percentages:\n", "  research: 98.91%\n", "  services: 0.24%\n", "  .flake8: 0.12%\n", "  .github: 0.12%\n", "  .pre-commit-config.yaml: 0.12%\n", "  .vscode: 0.12%\n", "  pyproject.toml: 0.12%\n", "  pyrightconfig.ci.json: 0.12%\n", "  tools: 0.12%\n", "\n", "Commit index: 7897\n", "Diff stats: +72630, -945, total: 73575\n", "Folder percentages:\n", "  services: 62.96%\n", "  tools: 11.11%\n", "  experimental: 7.41%\n", "  .gitignore: 3.70%\n", "  .vscode: 3.70%\n", "  BUILD: 3.70%\n", "  go.mod: 3.70%\n", "  go.sum: 3.70%\n", "\n", "Commit index: 8545\n", "Diff stats: +23, -0, total: 23\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 8555\n", "Diff stats: +1, -1, total: 2\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 9329\n", "Diff stats: +199407, -7995, total: 207402\n", "Folder percentages:\n", "  services: 74.36%\n", "  experimental: 5.13%\n", "  research: 5.13%\n", "  .vscode: 2.56%\n", "  Cargo.Bazel.lock: 2.56%\n", "  Cargo.lock: 2.56%\n", "  Cargo.toml: 2.56%\n", "  base: 2.56%\n", "  tools: 2.56%\n", "\n", "Commit index: 9945\n", "Diff stats: +536, -56, total: 592\n", "Folder percentages:\n", "  base: 75.00%\n", "  .vscode: 12.50%\n", "  pyproject.toml: 12.50%\n", "\n", "Commit index: 10063\n", "Diff stats: +1, -0, total: 1\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 10160\n", "Diff stats: +18, -9, total: 27\n", "Folder percentages:\n", "  .gitignore: 33.33%\n", "  .vscode: 33.33%\n", "  tools: 33.33%\n", "\n", "Commit index: 10410\n", "Diff stats: +945, -21, total: 966\n", "Folder percentages:\n", "  tools: 71.43%\n", "  .gitignore: 14.29%\n", "  .vscode: 14.29%\n", "\n", "Commit index: 10780\n", "Diff stats: +5, -2, total: 7\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "Commit index: 11676\n", "Diff stats: +22, -0, total: 22\n", "Folder percentages:\n", "  .vscode: 100.00%\n", "\n", "\n", "\n"]}], "source": ["import json\n", "from collections import defaultdict\n", "from pathlib import Path\n", "\n", "\n", "def get_diff_stats(commit):\n", "    total_insertions = 0\n", "    total_deletions = 0\n", "    for diff in commit[\"diffs\"].values():\n", "        total_insertions += diff[\"insertions\"]\n", "        total_deletions += diff[\"deletions\"]\n", "    return total_insertions, total_deletions\n", "\n", "\n", "def get_folder_percentages(commit):\n", "    file_counts = Counter()\n", "    for file_path in commit[\"diffs\"].keys():\n", "        top_level_folder = Path(file_path).parts[0]\n", "        file_counts[top_level_folder] += 1\n", "\n", "    total_files = sum(file_counts.values())\n", "    percentages = [\n", "        (folder, count / total_files * 100) for folder, count in file_counts.items()\n", "    ]\n", "    return sorted(percentages, key=lambda x: x[1], reverse=True)\n", "\n", "\n", "# 1. Top-5 largest-diff commits\n", "largest_diff_commits = sorted(\n", "    enumerate(git_history.commits),\n", "    key=lambda c: sum(get_diff_stats(c[1])),\n", "    reverse=True,\n", ")[:5]\n", "\n", "# 2. Top-5 smallest-diff commits (excluding commits with no changes)\n", "smallest_diff_commits = sorted(\n", "    [(i, c) for i, c in enumerate(git_history.commits) if sum(get_diff_stats(c)) > 0],\n", "    key=lambda c: sum(get_diff_stats(c[1])),\n", ")[:5]\n", "\n", "# 3. 5 latest commits that touch each top-level subfolder\n", "folder_latest_commits = {}\n", "for i, commit in enumerate(git_history.commits):\n", "    top_level_folders = set()\n", "    for file_path in commit[\"diffs\"].keys():\n", "        path_parts = Path(file_path).parts\n", "        if len(path_parts) >= 2:\n", "            top_level_folders.add(path_parts[0])\n", "    for top_level_folder in top_level_folders:\n", "        if top_level_folder not in folder_latest_commits:\n", "            folder_latest_commits[top_level_folder] = []\n", "        if len(folder_latest_commits[top_level_folder]) < 30:\n", "            folder_latest_commits[top_level_folder].append((i, commit))\n", "            break\n", "\n", "eval_set = []\n", "\n", "\n", "def make_eval_split(eval_subset, reason):\n", "    eval_split = []\n", "    for index, commit in eval_subset:\n", "        insertions, deletions = get_diff_stats(commit)\n", "        folder_percentages = get_folder_percentages(commit)\n", "        if reason == \"folder_latest\":\n", "            reason += f\"_{folder_percentages[0][0]}_latest\"\n", "\n", "        eval_split.append(\n", "            {\n", "                \"commit_index\": index,\n", "                \"diff_stats\": {\n", "                    \"insertions\": insertions,\n", "                    \"deletions\": deletions,\n", "                    \"total_changes\": insertions + deletions,\n", "                },\n", "                \"folder_percentages\": folder_percentages,\n", "                \"reason\": reason,\n", "            }\n", "        )\n", "    return eval_split\n", "\n", "\n", "eval_set = defaultdict(list)\n", "eval_set[\"largest_diff\"] = make_eval_split(largest_diff_commits, \"largest_diff\")\n", "eval_set[\"smallest_diff\"] = make_eval_split(smallest_diff_commits, \"smallest_diff\")\n", "for folder, commits in folder_latest_commits.items():\n", "    eval_set[f\"{folder}_latest\"] = make_eval_split(commits, f\"folder_latest_{folder}\")\n", "\n", "# Print the evaluation set\n", "print(f\"Total evaluation items: {sum(len(eval_set[key]) for key in eval_set)}\")\n", "for key in eval_set:\n", "    print(f\"Reason: {key}\")\n", "    for item in eval_set[key]:\n", "        print(f\"\\nCommit index: {item['commit_index']}\")\n", "        print(\n", "            f\"Diff stats: +{item['diff_stats']['insertions']}, -{item['diff_stats']['deletions']}, total: {item['diff_stats']['total_changes']}\"\n", "        )\n", "        print(\"Folder percentages:\")\n", "        for folder, percentage in item[\"folder_percentages\"]:\n", "            print(f\"  {folder}: {percentage:.2f}%\")\n", "    print(\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": 241, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_full.json\", \"w\") as f:\n", "    json.dump(eval_set, f, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Review"]}, {"cell_type": "code", "execution_count": 247, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "all_commits = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits.json\"))"]}, {"cell_type": "code", "execution_count": 242, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "eval_set_full = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_full.json\"))"]}, {"cell_type": "code", "execution_count": 284, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff:\n", "  [list of 5 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 5 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "smallest_diff:\n", "  [list of 5 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "clients_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "deploy_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "base_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "data_latest:\n", "  [list of 5 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "services_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "tools_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 2 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "research_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "experimental_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 2 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", ".github_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "models_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 3 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "third_party_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 4 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", ".vscode_latest:\n", "  [list of 15 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n"]}], "source": ["def print_schema(data, indent=0):\n", "    if isinstance(data, dict):\n", "        for key, value in data.items():\n", "            print(\"  \" * indent + f\"{key}:\")\n", "            print_schema(value, indent + 1)\n", "    elif isinstance(data, list) and len(data) > 0:\n", "        print(\"  \" * indent + f\"[list of {len(data)} items]\")\n", "        print_schema(data[0], indent + 1)\n", "    else:\n", "        print(\"  \" * indent + f\"({type(data).__name__})\")\n", "\n", "\n", "print_schema(eval_set_full)"]}, {"cell_type": "code", "execution_count": 292, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated schema of the first item:\n", "largest_diff:\n", "  [list of 5 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 5 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "smallest_diff:\n", "  [list of 5 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "clients_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "deploy_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "base_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "data_latest:\n", "  [list of 5 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "services_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "tools_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 2 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "research_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "experimental_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 2 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", ".github_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "models_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 3 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", "third_party_latest:\n", "  [list of 30 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 4 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n", ".vscode_latest:\n", "  [list of 15 items]\n", "    commit_index:\n", "      (int)\n", "    diff_stats:\n", "      insertions:\n", "        (int)\n", "      deletions:\n", "        (int)\n", "      total_changes:\n", "        (int)\n", "    folder_percentages:\n", "      [list of 1 items]\n", "        [list of 2 items]\n", "          (str)\n", "    reason:\n", "      (str)\n", "    full_set_index:\n", "      (int)\n"]}], "source": ["# Add full_set_index to each item in eval_set_full\n", "for split in eval_set_full.values():\n", "    for index, item in enumerate(split):\n", "        item[\"full_set_index\"] = index\n", "\n", "\n", "# Verify the change by printing the schema of the first item\n", "def print_schema(data, indent=0):\n", "    if isinstance(data, dict):\n", "        for key, value in data.items():\n", "            print(\"  \" * indent + f\"{key}:\")\n", "            print_schema(value, indent + 1)\n", "    elif isinstance(data, list) and len(data) > 0:\n", "        print(\"  \" * indent + f\"[list of {len(data)} items]\")\n", "        print_schema(data[0], indent + 1)\n", "    else:\n", "        print(\"  \" * indent + f\"({type(data).__name__})\")\n", "\n", "\n", "print(\"Updated schema of the first item:\")\n", "print_schema(eval_set_full)\n", "\n", "with open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_full.json\", \"w\") as f:\n", "    json.dump(eval_set_full, f, indent=4)"]}, {"cell_type": "code", "execution_count": 243, "metadata": {}, "outputs": [], "source": ["def print_size(dataset):\n", "    for key in dataset:\n", "        print(key, len(dataset[key]))\n", "    print(\"Total size:\", sum(len(split) for split in dataset.values()))\n", "\n", "\n", "def get_subsplit(reason, indices):\n", "    return [eval_set_full[reason][i] for i in indices]"]}, {"cell_type": "code", "execution_count": 244, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff 5\n", "smallest_diff 5\n", "clients_latest 30\n", "deploy_latest 30\n", "base_latest 30\n", "data_latest 5\n", "services_latest 30\n", "tools_latest 30\n", "research_latest 30\n", "experimental_latest 30\n", ".github_latest 30\n", "models_latest 30\n", "third_party_latest 30\n", ".vscode_latest 15\n", "Total size: 330\n"]}], "source": ["print_size(eval_set_full)"]}, {"cell_type": "code", "execution_count": 279, "metadata": {}, "outputs": [{"data": {"text/plain": ["15"]}, "execution_count": 279, "metadata": {}, "output_type": "execute_result"}], "source": ["reason = \".vscode_latest\"\n", "split = eval_set_full[reason]\n", "len(split)"]}, {"cell_type": "code", "execution_count": 282, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["04e401c8bb01acf6ea3d91ac5626ed0f9c703eb6\n", "Further strengthen the file exclusion config. (#11345)\n", "\n", "\n", "{\n", "    \"commit_index\": 2815,\n", "    \"diff_stats\": {\n", "        \"insertions\": 6,\n", "        \"deletions\": 6,\n", "        \"total_changes\": 12\n", "    },\n", "    \"folder_percentages\": [\n", "        [\n", "            \".vscode\",\n", "            100.0\n", "        ]\n", "    ],\n", "    \"reason\": \"folder_latest_.vscode\"\n", "}\n"]}], "source": ["split_index = 0\n", "example = split[split_index]\n", "commit = all_commits[example[\"commit_index\"]]\n", "print(commit[\"current_hash\"])\n", "print(commit[\"message\"])\n", "print(json.dumps(example, indent=4))"]}, {"cell_type": "code", "execution_count": 283, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [".vscode/settings.json 6 6 1\n", "diff --git a/.vscode/settings.json b/.vscode/settings.json\n", "index e1acdfc4f..5c9aa4295 100644\n", "--- a/.vscode/settings.json\n", "+++ b/.vscode/settings.json\n", "@@ -17,7 +17,7 @@\n", "     ],\n", "     \"python.analysis.exclude\": [\n", "         \"base/datasets/stubs\",\n", "-        \"clients/.bazel_output\",\n", "+        \"**/.bazel_output/**\",\n", "         \"**/node_modules/**\", // we do get python code in node_modules!\n", "     ],\n", "     \"python.analysis.typeCheckingMode\": \"standard\",\n", "@@ -42,10 +42,10 @@\n", "         \"**/.pytest_cache\": true,\n", "         \"**/*.egg-info\": true,\n", "         \"**/node_modules\": true,\n", "-        \"bazel-augment\": true,\n", "-        \"bazel-bin\": true,\n", "-        \"bazel-out\": true,\n", "-        \"bazel-testlogs\": true,\n", "-        \"clients/.bazel_output\": true,\n", "+        \"**/bazel-augment\": true,\n", "+        \"**/bazel-bin\": true,\n", "+        \"**/bazel-out\": true,\n", "+        \"**/bazel-testlogs\": true,\n", "+        \"**/.bazel_output\": true,\n", "     },\n", " }\n", "\n"]}], "source": ["for file, diff in commit[\"diffs\"].items():\n", "    print(file, diff[\"insertions\"], diff[\"deletions\"], diff[\"files\"])\n", "    print(diff[\"content\"])\n", "    print()"]}, {"cell_type": "code", "execution_count": 250, "metadata": {}, "outputs": [], "source": ["# from tqdm import tqdm\n", "\n", "# with open(\"/home/<USER>/zhuoran/commit_msgs/example.diff\", \"w\") as f:\n", "#     for file, diff in tqdm(commit[\"diffs\"].items(), desc=\"Processing diffs\"):\n", "#         print(file, diff[\"insertions\"], diff[\"deletions\"], diff[\"files\"], file=f)\n", "#         print(diff[\"content\"], file=f)\n", "#         print(file=f)"]}, {"cell_type": "code", "execution_count": 294, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff 3\n", "smallest_diff 3\n", "clients_latest 2\n", "deploy_latest 2\n", "base_latest 3\n", "data_latest 1\n", "services_latest 1\n", "tools_latest 1\n", "research_latest 2\n", "experimental_latest 2\n", ".github_latest 1\n", "models_latest 1\n", "third_party_latest 1\n", ".vscode_latest 1\n", "Total size: 24\n"]}], "source": ["eval_set_medium = {\n", "    \"largest_diff\": get_subsplit(\"largest_diff\", [0, 1, 4]),\n", "    \"smallest_diff\": get_subsplit(\"smallest_diff\", [2, 3, 4]),\n", "    \"clients_latest\": get_subsplit(\"clients_latest\", [3, 1]),\n", "    \"deploy_latest\": get_subsplit(\"deploy_latest\", [3, 2]),\n", "    \"base_latest\": get_subsplit(\"base_latest\", [3, 2, 1]),\n", "    \"data_latest\": get_subsplit(\"data_latest\", [0]),\n", "    \"services_latest\": get_subsplit(\"services_latest\", [0]),\n", "    \"tools_latest\": get_subsplit(\"tools_latest\", [3]),\n", "    \"research_latest\": get_subsplit(\"research_latest\", [13, 11]),\n", "    \"experimental_latest\": get_subsplit(\"experimental_latest\", [0, 1]),\n", "    \".github_latest\": get_subsplit(\".github_latest\", [1]),\n", "    \"models_latest\": get_subsplit(\"models_latest\", [1]),\n", "    \"third_party_latest\": get_subsplit(\"third_party_latest\", [1]),\n", "    \".vscode_latest\": get_subsplit(\".vscode_latest\", [0]),\n", "}\n", "print_size(eval_set_medium)"]}, {"cell_type": "code", "execution_count": 296, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff 2\n", "smallest_diff 2\n", "clients_latest 1\n", "deploy_latest 1\n", "base_latest 1\n", "data_latest 1\n", "services_latest 1\n", "tools_latest 1\n", "research_latest 1\n", "experimental_latest 1\n", "models_latest 1\n", "Total size: 13\n"]}], "source": ["eval_set_small = {\n", "    \"largest_diff\": get_subsplit(\"largest_diff\", [0, 4]),\n", "    \"smallest_diff\": get_subsplit(\"smallest_diff\", [3, 4]),\n", "    \"clients_latest\": get_subsplit(\"clients_latest\", [3]),\n", "    \"deploy_latest\": get_subsplit(\"deploy_latest\", [3]),\n", "    \"base_latest\": get_subsplit(\"base_latest\", [3]),\n", "    \"data_latest\": get_subsplit(\"data_latest\", [0]),\n", "    \"services_latest\": get_subsplit(\"services_latest\", [0]),\n", "    \"tools_latest\": get_subsplit(\"tools_latest\", [3]),\n", "    \"research_latest\": get_subsplit(\"research_latest\", [13]),\n", "    \"experimental_latest\": get_subsplit(\"experimental_latest\", [0]),\n", "    \"models_latest\": get_subsplit(\"models_latest\", [1]),\n", "}\n", "print_size(eval_set_small)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Match"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["commits_v1 = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_v1.json\"))\n", "commits_v3_tmp1 = json.load(\n", "    open(\"/home/<USER>/zhuoran/commit_msgs/commits_v3_tmp1.json\")\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["149"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["offset = 0\n", "ref_commit = commits_v1[0]\n", "for index, commit in enumerate(commits_v3_tmp1):\n", "    if commit[\"current_hash\"] == ref_commit[\"current_hash\"]:\n", "        offset = index\n", "        break\n", "\n", "offset"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Commit pair 0 matches.\n", "Commit pair 1 matches.\n", "Commit pair 2 matches.\n", "Commit pair 3 matches.\n", "Commit pair 4 matches.\n", "Commit pair 5 matches.\n", "<PERSON><PERSON><PERSON> pair 6 matches.\n", "Commit pair 7 matches.\n", "Commit pair 8 matches.\n", "Commit pair 9 matches.\n", "<PERSON>mmit pair 10 matches.\n", "<PERSON>mmit pair 11 matches.\n", "Commit pair 12 matches.\n", "Commit pair 13 matches.\n", "Commit pair 14 matches.\n", "Commit pair 15 matches.\n", "Commit pair 16 matches.\n", "Commit pair 17 matches.\n", "Commit pair 18 matches.\n", "<PERSON>mmit pair 19 matches.\n", "Commit pair 20 matches.\n", "Commit pair 21 matches.\n", "<PERSON>mmit pair 22 matches.\n", "<PERSON>mmit pair 23 matches.\n", "Commit pair 24 matches.\n", "<PERSON>mmit pair 25 matches.\n", "<PERSON><PERSON><PERSON> pair 26 matches.\n", "<PERSON><PERSON><PERSON> pair 27 matches.\n", "<PERSON>mmit pair 28 matches.\n", "<PERSON><PERSON><PERSON> pair 29 matches.\n", "<PERSON>mmit pair 30 matches.\n", "<PERSON><PERSON><PERSON> pair 31 matches.\n", "<PERSON><PERSON><PERSON> pair 32 matches.\n", "<PERSON><PERSON><PERSON> pair 33 matches.\n", "<PERSON><PERSON><PERSON> pair 34 matches.\n", "<PERSON><PERSON><PERSON> pair 35 matches.\n", "<PERSON><PERSON><PERSON> pair 36 matches.\n", "<PERSON><PERSON><PERSON> pair 37 matches.\n", "<PERSON><PERSON><PERSON> pair 38 matches.\n", "<PERSON><PERSON><PERSON> pair 39 matches.\n", "<PERSON>mmit pair 40 matches.\n", "<PERSON>mm<PERSON> pair 41 matches.\n", "<PERSON>mm<PERSON> pair 42 matches.\n", "<PERSON><PERSON><PERSON> pair 43 matches.\n", "<PERSON>mmit pair 44 matches.\n", "<PERSON>mm<PERSON> pair 45 matches.\n", "<PERSON>mm<PERSON> pair 46 matches.\n", "<PERSON>mmit pair 47 matches.\n", "<PERSON>mm<PERSON> pair 48 matches.\n", "<PERSON><PERSON><PERSON> pair 49 matches.\n", "<PERSON>mmit pair 50 matches.\n", "<PERSON><PERSON><PERSON> pair 51 matches.\n", "<PERSON><PERSON><PERSON> pair 52 matches.\n", "<PERSON>mm<PERSON> pair 53 matches.\n", "<PERSON><PERSON><PERSON> pair 54 matches.\n", "<PERSON><PERSON><PERSON> pair 55 matches.\n", "<PERSON><PERSON><PERSON> pair 56 matches.\n", "<PERSON><PERSON><PERSON> pair 57 matches.\n", "<PERSON>mm<PERSON> pair 58 matches.\n", "<PERSON><PERSON><PERSON> pair 59 matches.\n", "<PERSON>mmit pair 60 matches.\n", "<PERSON><PERSON><PERSON> pair 61 matches.\n", "<PERSON>mm<PERSON> pair 62 matches.\n", "<PERSON><PERSON><PERSON> pair 63 matches.\n", "<PERSON><PERSON><PERSON> pair 64 matches.\n", "<PERSON><PERSON><PERSON> pair 65 matches.\n", "<PERSON><PERSON><PERSON> pair 66 matches.\n", "<PERSON><PERSON><PERSON> pair 67 matches.\n", "<PERSON>mm<PERSON> pair 68 matches.\n", "<PERSON>mmit pair 69 matches.\n", "<PERSON>mmit pair 70 matches.\n", "<PERSON>mm<PERSON> pair 71 matches.\n", "<PERSON>mmit pair 72 matches.\n", "<PERSON>mm<PERSON> pair 73 matches.\n", "<PERSON><PERSON><PERSON> pair 74 matches.\n", "<PERSON>mmit pair 75 matches.\n", "<PERSON>mmit pair 76 matches.\n", "<PERSON>mmit pair 77 matches.\n", "Commit pair 78 matches.\n", "<PERSON>mmit pair 79 matches.\n", "Commit pair 80 matches.\n", "<PERSON>mm<PERSON> pair 81 matches.\n", "<PERSON>mmit pair 82 matches.\n", "Commit pair 83 matches.\n", "Commit pair 84 matches.\n", "Commit pair 85 matches.\n", "Commit pair 86 matches.\n", "Commit pair 87 matches.\n", "Commit pair 88 matches.\n", "Commit pair 89 matches.\n", "Commit pair 90 matches.\n", "<PERSON>mmit pair 91 matches.\n", "Commit pair 92 matches.\n", "Commit pair 93 matches.\n", "Commit pair 94 matches.\n", "Commit pair 95 matches.\n", "Commit pair 96 matches.\n", "Commit pair 97 matches.\n", "Commit pair 98 matches.\n", "<PERSON>mmit pair 99 matches.\n", "<PERSON>mmit pair 100 matches.\n", "<PERSON>mm<PERSON> pair 101 matches.\n", "<PERSON>mm<PERSON> pair 102 matches.\n", "Commit pair 103 matches.\n", "<PERSON><PERSON><PERSON> pair 104 matches.\n", "<PERSON>mm<PERSON> pair 105 matches.\n", "<PERSON>mm<PERSON> pair 106 matches.\n", "<PERSON>mm<PERSON> pair 107 matches.\n", "<PERSON>mmit pair 108 matches.\n", "<PERSON>mmit pair 109 matches.\n", "<PERSON>mm<PERSON> pair 110 matches.\n", "<PERSON><PERSON><PERSON> pair 111 matches.\n", "<PERSON>mmit pair 112 matches.\n", "<PERSON>mm<PERSON> pair 113 matches.\n", "<PERSON>mm<PERSON> pair 114 matches.\n", "<PERSON>mm<PERSON> pair 115 matches.\n", "<PERSON><PERSON><PERSON> pair 116 matches.\n", "<PERSON>mm<PERSON> pair 117 matches.\n", "<PERSON>mmit pair 118 matches.\n", "<PERSON><PERSON><PERSON> pair 119 matches.\n", "<PERSON>mmit pair 120 matches.\n", "<PERSON>mm<PERSON> pair 121 matches.\n", "<PERSON>mmit pair 122 matches.\n", "<PERSON>mmit pair 123 matches.\n", "<PERSON>mmit pair 124 matches.\n", "<PERSON>mmit pair 125 matches.\n", "<PERSON>mmit pair 126 matches.\n", "<PERSON>mm<PERSON> pair 127 matches.\n", "<PERSON>mm<PERSON> pair 128 matches.\n", "<PERSON>mm<PERSON> pair 129 matches.\n", "<PERSON>mmit pair 130 matches.\n", "<PERSON>mm<PERSON> pair 131 matches.\n", "<PERSON>mmit pair 132 matches.\n", "<PERSON><PERSON><PERSON> pair 133 matches.\n", "<PERSON>mm<PERSON> pair 134 matches.\n", "<PERSON>mm<PERSON> pair 135 matches.\n", "<PERSON>mm<PERSON> pair 136 matches.\n", "<PERSON>mm<PERSON> pair 137 matches.\n", "<PERSON>mmit pair 138 matches.\n", "<PERSON>mm<PERSON> pair 139 matches.\n", "<PERSON>mmit pair 140 matches.\n", "<PERSON>mmit pair 141 matches.\n", "<PERSON>mmit pair 142 matches.\n", "<PERSON>mm<PERSON> pair 143 matches.\n", "<PERSON>mmit pair 144 matches.\n", "<PERSON>mm<PERSON> pair 145 matches.\n", "<PERSON>mmit pair 146 matches.\n", "<PERSON>mm<PERSON> pair 147 matches.\n", "<PERSON>mmit pair 148 matches.\n", "<PERSON>mm<PERSON> pair 149 matches.\n", "<PERSON>mm<PERSON> pair 150 matches.\n", "<PERSON>mm<PERSON> pair 151 matches.\n", "<PERSON><PERSON><PERSON> pair 152 matches.\n", "<PERSON><PERSON><PERSON> pair 153 matches.\n", "<PERSON><PERSON><PERSON> pair 154 matches.\n", "<PERSON><PERSON><PERSON> pair 155 matches.\n", "<PERSON>mm<PERSON> pair 156 matches.\n", "<PERSON>mm<PERSON> pair 157 matches.\n", "<PERSON>mm<PERSON> pair 158 matches.\n", "<PERSON>mmit pair 159 matches.\n", "<PERSON>mmit pair 160 matches.\n", "<PERSON>mm<PERSON> pair 161 matches.\n", "<PERSON>mm<PERSON> pair 162 matches.\n", "<PERSON>mm<PERSON> pair 163 matches.\n", "<PERSON><PERSON><PERSON> pair 164 matches.\n", "<PERSON><PERSON><PERSON> pair 165 matches.\n", "<PERSON>mm<PERSON> pair 166 matches.\n", "<PERSON><PERSON><PERSON> pair 167 matches.\n", "<PERSON><PERSON><PERSON> pair 168 matches.\n", "<PERSON><PERSON><PERSON> pair 169 matches.\n", "<PERSON><PERSON><PERSON> pair 170 matches.\n", "<PERSON><PERSON><PERSON> pair 171 matches.\n", "<PERSON><PERSON><PERSON> pair 172 matches.\n", "<PERSON><PERSON><PERSON> pair 173 matches.\n", "<PERSON>mm<PERSON> pair 174 matches.\n", "<PERSON>mmit pair 175 matches.\n", "<PERSON>mm<PERSON> pair 176 matches.\n", "<PERSON>mmit pair 177 matches.\n", "<PERSON>mm<PERSON> pair 178 matches.\n", "<PERSON>mmit pair 179 matches.\n", "Commit pair 180 matches.\n", "<PERSON>mm<PERSON> pair 181 matches.\n", "Commit pair 182 matches.\n", "<PERSON>mmit pair 183 matches.\n", "Commit pair 184 matches.\n", "<PERSON>mm<PERSON> pair 185 matches.\n", "<PERSON>mmit pair 186 matches.\n", "<PERSON>mmit pair 187 matches.\n", "Commit pair 188 matches.\n", "<PERSON>mmit pair 189 matches.\n", "<PERSON>mmit pair 190 matches.\n", "<PERSON>mm<PERSON> pair 191 matches.\n", "<PERSON>mmit pair 192 matches.\n", "<PERSON>mmit pair 193 matches.\n", "<PERSON>mm<PERSON> pair 194 matches.\n", "<PERSON>mm<PERSON> pair 195 matches.\n", "<PERSON>mmit pair 196 matches.\n", "<PERSON>mmit pair 197 matches.\n", "<PERSON>mm<PERSON> pair 198 matches.\n", "<PERSON>mmit pair 199 matches.\n", "Commit pair 200 matches.\n", "<PERSON>mmit pair 201 matches.\n", "Commit pair 202 matches.\n", "Commit pair 203 matches.\n", "<PERSON>mmit pair 204 matches.\n", "Commit pair 205 matches.\n", "Commit pair 206 matches.\n", "<PERSON>mmit pair 207 matches.\n", "Commit pair 208 matches.\n", "<PERSON>mmit pair 209 matches.\n", "<PERSON>mmit pair 210 matches.\n", "<PERSON>mmit pair 211 matches.\n", "Commit pair 212 matches.\n", "Commit pair 213 matches.\n", "<PERSON>mmit pair 214 matches.\n", "<PERSON>mmit pair 215 matches.\n", "<PERSON>mmit pair 216 matches.\n", "<PERSON>mmit pair 217 matches.\n", "<PERSON>mmit pair 218 matches.\n", "Commit pair 219 matches.\n", "<PERSON>mmit pair 220 matches.\n", "<PERSON><PERSON><PERSON> pair 221 matches.\n", "Commit pair 222 matches.\n", "Commit pair 223 matches.\n", "<PERSON>mmit pair 224 matches.\n", "Commit pair 225 matches.\n", "<PERSON>mmit pair 226 matches.\n", "Commit pair 227 matches.\n", "Commit pair 228 matches.\n", "<PERSON>mmit pair 229 matches.\n", "<PERSON>mmit pair 230 matches.\n", "<PERSON>mmit pair 231 matches.\n", "<PERSON>mmit pair 232 matches.\n", "<PERSON>mmit pair 233 matches.\n", "Commit pair 234 matches.\n", "<PERSON>mmit pair 235 matches.\n", "<PERSON>mm<PERSON> pair 236 matches.\n", "Commit pair 237 matches.\n", "<PERSON>mmit pair 238 matches.\n", "<PERSON>mmit pair 239 matches.\n", "Commit pair 240 matches.\n", "Commit pair 241 matches.\n", "<PERSON>mmit pair 242 matches.\n", "<PERSON>mmit pair 243 matches.\n", "<PERSON>mmit pair 244 matches.\n", "<PERSON>mmit pair 245 matches.\n", "<PERSON>mmit pair 246 matches.\n", "<PERSON>mm<PERSON> pair 247 matches.\n", "<PERSON>mmit pair 248 matches.\n", "<PERSON>mmit pair 249 matches.\n", "Commit pair 250 matches.\n", "<PERSON>mmit pair 251 matches.\n", "Commit pair 252 matches.\n", "<PERSON>mmit pair 253 matches.\n", "<PERSON>mmit pair 254 matches.\n", "<PERSON>mmit pair 255 matches.\n", "<PERSON>mmit pair 256 matches.\n", "<PERSON>mmit pair 257 matches.\n", "<PERSON>mm<PERSON> pair 258 matches.\n", "Commit pair 259 matches.\n", "<PERSON>mmit pair 260 matches.\n", "<PERSON>mmit pair 261 matches.\n", "<PERSON>mmit pair 262 matches.\n", "Commit pair 263 matches.\n", "<PERSON>mmit pair 264 matches.\n", "<PERSON>mmit pair 265 matches.\n", "Commit pair 266 matches.\n", "Commit pair 267 matches.\n", "Commit pair 268 matches.\n", "Commit pair 269 matches.\n", "<PERSON>mmit pair 270 matches.\n", "Commit pair 271 matches.\n", "<PERSON>mmit pair 272 matches.\n", "Commit pair 273 matches.\n", "Commit pair 274 matches.\n", "<PERSON>mmit pair 275 matches.\n", "Commit pair 276 matches.\n", "Commit pair 277 matches.\n", "<PERSON><PERSON><PERSON> pair 278 matches.\n", "Commit pair 279 matches.\n", "Commit pair 280 matches.\n", "<PERSON>mmit pair 281 matches.\n", "Commit pair 282 matches.\n", "Commit pair 283 matches.\n", "Commit pair 284 matches.\n", "Commit pair 285 matches.\n", "<PERSON><PERSON><PERSON> pair 286 matches.\n", "<PERSON>mm<PERSON> pair 287 matches.\n", "Commit pair 288 matches.\n", "Commit pair 289 matches.\n", "<PERSON>mmit pair 290 matches.\n", "Commit pair 291 matches.\n", "Commit pair 292 matches.\n", "Commit pair 293 matches.\n", "Commit pair 294 matches.\n", "<PERSON>mmit pair 295 matches.\n", "Commit pair 296 matches.\n", "Commit pair 297 matches.\n", "Commit pair 298 matches.\n", "Commit pair 299 matches.\n", "Commit pair 300 matches.\n", "<PERSON>mmit pair 301 matches.\n", "<PERSON>mmit pair 302 matches.\n", "<PERSON>mmit pair 303 matches.\n", "Commit pair 304 matches.\n", "<PERSON>mmit pair 305 matches.\n", "Commit pair 306 matches.\n", "Commit pair 307 matches.\n", "Commit pair 308 matches.\n", "Commit pair 309 matches.\n", "Commit pair 310 matches.\n", "Commit pair 311 matches.\n", "Commit pair 312 matches.\n", "Commit pair 313 matches.\n", "Commit pair 314 matches.\n", "Commit pair 315 matches.\n", "Commit pair 316 matches.\n", "Commit pair 317 matches.\n", "Commit pair 318 matches.\n", "Commit pair 319 matches.\n", "Commit pair 320 matches.\n", "<PERSON>mmit pair 321 matches.\n", "<PERSON>mm<PERSON> pair 322 matches.\n", "Commit pair 323 matches.\n", "Commit pair 324 matches.\n", "Commit pair 325 matches.\n", "Commit pair 326 matches.\n", "Commit pair 327 matches.\n", "Commit pair 328 matches.\n", "<PERSON>mmit pair 329 matches.\n", "Commit pair 330 matches.\n", "Commit pair 331 matches.\n", "Commit pair 332 matches.\n", "<PERSON>mmit pair 333 matches.\n", "Commit pair 334 matches.\n", "<PERSON>mmit pair 335 matches.\n", "Commit pair 336 matches.\n", "Commit pair 337 matches.\n", "Commit pair 338 matches.\n", "Commit pair 339 matches.\n", "Commit pair 340 matches.\n", "Commit pair 341 matches.\n", "Commit pair 342 matches.\n", "Commit pair 343 matches.\n", "Commit pair 344 matches.\n", "Commit pair 345 matches.\n", "Commit pair 346 matches.\n", "Commit pair 347 matches.\n", "Commit pair 348 matches.\n", "Commit pair 349 matches.\n", "Commit pair 350 matches.\n", "Commit pair 351 matches.\n", "Commit pair 352 matches.\n", "Commit pair 353 matches.\n", "Commit pair 354 matches.\n", "Commit pair 355 matches.\n", "Commit pair 356 matches.\n", "Commit pair 357 matches.\n", "Commit pair 358 matches.\n", "Commit pair 359 matches.\n", "Commit pair 360 matches.\n", "Commit pair 361 matches.\n", "Commit pair 362 matches.\n", "Commit pair 363 matches.\n", "Commit pair 364 matches.\n", "Commit pair 365 matches.\n", "Commit pair 366 matches.\n", "Commit pair 367 matches.\n", "Commit pair 368 matches.\n", "Commit pair 369 matches.\n", "Commit pair 370 matches.\n", "Commit pair 371 matches.\n", "Commit pair 372 matches.\n", "Commit pair 373 matches.\n", "Commit pair 374 matches.\n", "Commit pair 375 matches.\n", "Commit pair 376 matches.\n", "Commit pair 377 matches.\n", "Commit pair 378 matches.\n", "Commit pair 379 matches.\n", "Commit pair 380 matches.\n", "Commit pair 381 matches.\n", "Commit pair 382 matches.\n", "Commit pair 383 matches.\n", "Commit pair 384 matches.\n", "<PERSON>mmit pair 385 matches.\n", "Commit pair 386 matches.\n", "Commit pair 387 matches.\n", "Commit pair 388 matches.\n", "Commit pair 389 matches.\n", "Commit pair 390 matches.\n", "Commit pair 391 matches.\n", "Commit pair 392 matches.\n", "Commit pair 393 matches.\n", "Commit pair 394 matches.\n", "<PERSON>mmit pair 395 matches.\n", "Commit pair 396 matches.\n", "Commit pair 397 matches.\n", "Commit pair 398 matches.\n", "Commit pair 399 matches.\n", "Commit pair 400 matches.\n", "Commit pair 401 matches.\n", "Commit pair 402 matches.\n", "Commit pair 403 matches.\n", "Commit pair 404 matches.\n", "Commit pair 405 matches.\n", "Commit pair 406 matches.\n", "Commit pair 407 matches.\n", "Commit pair 408 matches.\n", "Commit pair 409 matches.\n", "Commit pair 410 matches.\n", "Commit pair 411 matches.\n", "Commit pair 412 matches.\n", "Commit pair 413 matches.\n", "Commit pair 414 matches.\n", "<PERSON>mmit pair 415 matches.\n", "Commit pair 416 matches.\n", "Commit pair 417 matches.\n", "Commit pair 418 matches.\n", "Commit pair 419 matches.\n", "Commit pair 420 matches.\n", "Commit pair 421 matches.\n", "Commit pair 422 matches.\n", "Commit pair 423 matches.\n", "Commit pair 424 matches.\n", "Commit pair 425 matches.\n", "Commit pair 426 matches.\n", "Commit pair 427 matches.\n", "Commit pair 428 matches.\n", "Commit pair 429 matches.\n", "Commit pair 430 matches.\n", "Commit pair 431 matches.\n", "Commit pair 432 matches.\n", "Commit pair 433 matches.\n", "Commit pair 434 matches.\n", "Commit pair 435 matches.\n", "Commit pair 436 matches.\n", "Commit pair 437 matches.\n", "Commit pair 438 matches.\n", "Commit pair 439 matches.\n", "Commit pair 440 matches.\n", "Commit pair 441 matches.\n", "Commit pair 442 matches.\n", "Commit pair 443 matches.\n", "Commit pair 444 matches.\n", "Commit pair 445 matches.\n", "Commit pair 446 matches.\n", "Commit pair 447 matches.\n", "Commit pair 448 matches.\n", "Commit pair 449 matches.\n", "Commit pair 450 matches.\n", "Commit pair 451 matches.\n", "Commit pair 452 matches.\n", "Commit pair 453 matches.\n", "Commit pair 454 matches.\n", "Commit pair 455 matches.\n", "Commit pair 456 matches.\n", "Commit pair 457 matches.\n", "Commit pair 458 matches.\n", "Commit pair 459 matches.\n", "Commit pair 460 matches.\n", "Commit pair 461 matches.\n", "Commit pair 462 matches.\n", "Commit pair 463 matches.\n", "Commit pair 464 matches.\n", "Commit pair 465 matches.\n", "Commit pair 466 matches.\n", "Commit pair 467 matches.\n", "Commit pair 468 matches.\n", "Commit pair 469 matches.\n", "Commit pair 470 matches.\n", "Commit pair 471 matches.\n", "Commit pair 472 matches.\n", "Commit pair 473 matches.\n", "Commit pair 474 matches.\n", "Commit pair 475 matches.\n", "Commit pair 476 matches.\n", "Commit pair 477 matches.\n", "Commit pair 478 matches.\n", "Commit pair 479 matches.\n", "Commit pair 480 matches.\n", "Commit pair 481 matches.\n", "Commit pair 482 matches.\n", "Commit pair 483 matches.\n", "Commit pair 484 matches.\n", "Commit pair 485 matches.\n", "Commit pair 486 matches.\n", "Commit pair 487 matches.\n", "Commit pair 488 matches.\n", "Commit pair 489 matches.\n", "Commit pair 490 matches.\n", "Commit pair 491 matches.\n", "Commit pair 492 matches.\n", "Commit pair 493 matches.\n", "Commit pair 494 matches.\n", "Commit pair 495 matches.\n", "Commit pair 496 matches.\n", "Commit pair 497 matches.\n", "Commit pair 498 matches.\n", "Commit pair 499 matches.\n", "Commit pair 500 matches.\n", "Commit pair 501 matches.\n", "Commit pair 502 matches.\n", "Commit pair 503 matches.\n", "Commit pair 504 matches.\n", "Commit pair 505 matches.\n", "Commit pair 506 matches.\n", "Commit pair 507 matches.\n", "Commit pair 508 matches.\n", "Commit pair 509 matches.\n", "Commit pair 510 matches.\n", "Commit pair 511 matches.\n", "Commit pair 512 matches.\n", "Commit pair 513 matches.\n", "Commit pair 514 matches.\n", "Commit pair 515 matches.\n", "Commit pair 516 matches.\n", "Commit pair 517 matches.\n", "Commit pair 518 matches.\n", "Commit pair 519 matches.\n", "Commit pair 520 matches.\n", "Commit pair 521 matches.\n", "Commit pair 522 matches.\n", "Commit pair 523 matches.\n", "Commit pair 524 matches.\n", "Commit pair 525 matches.\n", "Commit pair 526 matches.\n", "Commit pair 527 matches.\n", "Commit pair 528 matches.\n", "Commit pair 529 matches.\n", "Commit pair 530 matches.\n", "Commit pair 531 matches.\n", "Commit pair 532 matches.\n", "Commit pair 533 matches.\n", "Commit pair 534 matches.\n", "Commit pair 535 matches.\n", "Commit pair 536 matches.\n", "Commit pair 537 matches.\n", "Commit pair 538 matches.\n", "Commit pair 539 matches.\n", "Commit pair 540 matches.\n", "Commit pair 541 matches.\n", "Commit pair 542 matches.\n", "Commit pair 543 matches.\n", "Commit pair 544 matches.\n", "Commit pair 545 matches.\n", "Commit pair 546 matches.\n", "Commit pair 547 matches.\n", "Commit pair 548 matches.\n", "Commit pair 549 matches.\n", "Commit pair 550 matches.\n", "Commit pair 551 matches.\n", "Commit pair 552 matches.\n", "Commit pair 553 matches.\n", "Commit pair 554 matches.\n", "Commit pair 555 matches.\n", "Commit pair 556 matches.\n", "Commit pair 557 matches.\n", "Commit pair 558 matches.\n", "Commit pair 559 matches.\n", "Commit pair 560 matches.\n", "Commit pair 561 matches.\n", "Commit pair 562 matches.\n", "Commit pair 563 matches.\n", "Commit pair 564 matches.\n", "Commit pair 565 matches.\n", "Commit pair 566 matches.\n", "Commit pair 567 matches.\n", "Commit pair 568 matches.\n", "Commit pair 569 matches.\n", "Commit pair 570 matches.\n", "Commit pair 571 matches.\n", "Commit pair 572 matches.\n", "Commit pair 573 matches.\n", "Commit pair 574 matches.\n", "Commit pair 575 matches.\n", "Commit pair 576 matches.\n", "Commit pair 577 matches.\n", "Commit pair 578 matches.\n", "Commit pair 579 matches.\n", "Commit pair 580 matches.\n", "Commit pair 581 matches.\n", "Commit pair 582 matches.\n", "Commit pair 583 matches.\n", "Commit pair 584 matches.\n", "Commit pair 585 matches.\n", "Commit pair 586 matches.\n", "Commit pair 587 matches.\n", "Commit pair 588 matches.\n", "Commit pair 589 matches.\n", "Commit pair 590 matches.\n", "Commit pair 591 matches.\n", "Commit pair 592 matches.\n", "Commit pair 593 matches.\n", "Commit pair 594 matches.\n", "<PERSON>mmit pair 595 matches.\n", "Commit pair 596 matches.\n", "Commit pair 597 matches.\n", "Commit pair 598 matches.\n", "Commit pair 599 matches.\n", "Commit pair 600 matches.\n", "Commit pair 601 matches.\n", "Commit pair 602 matches.\n", "Commit pair 603 matches.\n", "Commit pair 604 matches.\n", "Commit pair 605 matches.\n", "Commit pair 606 matches.\n", "Commit pair 607 matches.\n", "Commit pair 608 matches.\n", "Commit pair 609 matches.\n", "Commit pair 610 matches.\n", "Commit pair 611 matches.\n", "Commit pair 612 matches.\n", "Commit pair 613 matches.\n", "Commit pair 614 matches.\n", "Commit pair 615 matches.\n", "Commit pair 616 matches.\n", "Commit pair 617 matches.\n", "Commit pair 618 matches.\n", "Commit pair 619 matches.\n", "Commit pair 620 matches.\n", "Commit pair 621 matches.\n", "Commit pair 622 matches.\n", "Commit pair 623 matches.\n", "Commit pair 624 matches.\n", "Commit pair 625 matches.\n", "Commit pair 626 matches.\n", "Commit pair 627 matches.\n", "Commit pair 628 matches.\n", "Commit pair 629 matches.\n", "Commit pair 630 matches.\n", "Commit pair 631 matches.\n", "Commit pair 632 matches.\n", "Commit pair 633 matches.\n", "Commit pair 634 matches.\n", "Commit pair 635 matches.\n", "Commit pair 636 matches.\n", "Commit pair 637 matches.\n", "Commit pair 638 matches.\n", "Commit pair 639 matches.\n", "Commit pair 640 matches.\n", "Commit pair 641 matches.\n", "Commit pair 642 matches.\n", "Commit pair 643 matches.\n", "Commit pair 644 matches.\n", "Commit pair 645 matches.\n", "Commit pair 646 matches.\n", "Commit pair 647 matches.\n", "Commit pair 648 matches.\n", "Commit pair 649 matches.\n", "Commit pair 650 matches.\n", "Commit pair 651 matches.\n", "Commit pair 652 matches.\n", "Commit pair 653 matches.\n", "Commit pair 654 matches.\n", "Commit pair 655 matches.\n", "Commit pair 656 matches.\n", "Commit pair 657 matches.\n", "Commit pair 658 matches.\n", "Commit pair 659 matches.\n", "Commit pair 660 matches.\n", "Commit pair 661 matches.\n", "Commit pair 662 matches.\n", "Commit pair 663 matches.\n", "Commit pair 664 matches.\n", "Commit pair 665 matches.\n", "Commit pair 666 matches.\n", "Commit pair 667 matches.\n", "Commit pair 668 matches.\n", "Commit pair 669 matches.\n", "Commit pair 670 matches.\n", "Commit pair 671 matches.\n", "Commit pair 672 matches.\n", "Commit pair 673 matches.\n", "Commit pair 674 matches.\n", "Commit pair 675 matches.\n", "Commit pair 676 matches.\n", "Commit pair 677 matches.\n", "Commit pair 678 matches.\n", "Commit pair 679 matches.\n", "Commit pair 680 matches.\n", "Commit pair 681 matches.\n", "Commit pair 682 matches.\n", "Commit pair 683 matches.\n", "Commit pair 684 matches.\n", "Commit pair 685 matches.\n", "Commit pair 686 matches.\n", "Commit pair 687 matches.\n", "Commit pair 688 matches.\n", "Commit pair 689 matches.\n", "Commit pair 690 matches.\n", "Commit pair 691 matches.\n", "Commit pair 692 matches.\n", "Commit pair 693 matches.\n", "Commit pair 694 matches.\n", "Commit pair 695 matches.\n", "Commit pair 696 matches.\n", "Commit pair 697 matches.\n", "Commit pair 698 matches.\n", "Commit pair 699 matches.\n", "Commit pair 700 matches.\n", "Commit pair 701 matches.\n", "Commit pair 702 matches.\n", "Commit pair 703 matches.\n", "Commit pair 704 matches.\n", "Commit pair 705 matches.\n", "Commit pair 706 matches.\n", "Commit pair 707 matches.\n", "Commit pair 708 matches.\n", "Commit pair 709 matches.\n", "Commit pair 710 matches.\n", "Commit pair 711 matches.\n", "Commit pair 712 matches.\n", "Commit pair 713 matches.\n", "Commit pair 714 matches.\n", "Commit pair 715 matches.\n", "Commit pair 716 matches.\n", "Commit pair 717 matches.\n", "Commit pair 718 matches.\n", "Commit pair 719 matches.\n", "Commit pair 720 matches.\n", "Commit pair 721 matches.\n", "Commit pair 722 matches.\n", "Commit pair 723 matches.\n", "Commit pair 724 matches.\n", "Commit pair 725 matches.\n", "Commit pair 726 matches.\n", "Commit pair 727 matches.\n", "Commit pair 728 matches.\n", "Commit pair 729 matches.\n", "Commit pair 730 matches.\n", "Commit pair 731 matches.\n", "Commit pair 732 matches.\n", "Commit pair 733 matches.\n", "Commit pair 734 matches.\n", "Commit pair 735 matches.\n", "Commit pair 736 matches.\n", "Commit pair 737 matches.\n", "Commit pair 738 matches.\n", "Commit pair 739 matches.\n", "Commit pair 740 matches.\n", "Commit pair 741 matches.\n", "Commit pair 742 matches.\n", "Commit pair 743 matches.\n", "Commit pair 744 matches.\n", "Commit pair 745 matches.\n", "Commit pair 746 matches.\n", "Commit pair 747 matches.\n", "Commit pair 748 matches.\n", "Commit pair 749 matches.\n", "Commit pair 750 matches.\n", "Commit pair 751 matches.\n", "Commit pair 752 matches.\n", "Commit pair 753 matches.\n", "Commit pair 754 matches.\n", "Commit pair 755 matches.\n", "Commit pair 756 matches.\n", "Commit pair 757 matches.\n", "Commit pair 758 matches.\n", "Commit pair 759 matches.\n", "Commit pair 760 matches.\n", "Commit pair 761 matches.\n", "Commit pair 762 matches.\n", "Commit pair 763 matches.\n", "Commit pair 764 matches.\n", "Commit pair 765 matches.\n", "Commit pair 766 matches.\n", "Commit pair 767 matches.\n", "Commit pair 768 matches.\n", "Commit pair 769 matches.\n", "Commit pair 770 matches.\n", "Commit pair 771 matches.\n", "Commit pair 772 matches.\n", "Commit pair 773 matches.\n", "Commit pair 774 matches.\n", "Commit pair 775 matches.\n", "Commit pair 776 matches.\n", "Commit pair 777 matches.\n", "Commit pair 778 matches.\n", "Commit pair 779 matches.\n", "Commit pair 780 matches.\n", "Commit pair 781 matches.\n", "Commit pair 782 matches.\n", "Commit pair 783 matches.\n", "Commit pair 784 matches.\n", "Commit pair 785 matches.\n", "Commit pair 786 matches.\n", "Commit pair 787 matches.\n", "Commit pair 788 matches.\n", "Commit pair 789 matches.\n", "Commit pair 790 matches.\n", "Commit pair 791 matches.\n", "Commit pair 792 matches.\n", "Commit pair 793 matches.\n", "Commit pair 794 matches.\n", "Commit pair 795 matches.\n", "Commit pair 796 matches.\n", "Commit pair 797 matches.\n", "Commit pair 798 matches.\n", "Commit pair 799 matches.\n", "Commit pair 800 matches.\n", "Commit pair 801 matches.\n", "Commit pair 802 matches.\n", "Commit pair 803 matches.\n", "Commit pair 804 matches.\n", "Commit pair 805 matches.\n", "Commit pair 806 matches.\n", "Commit pair 807 matches.\n", "Commit pair 808 matches.\n", "Commit pair 809 matches.\n", "Commit pair 810 matches.\n", "Commit pair 811 matches.\n", "Commit pair 812 matches.\n", "Commit pair 813 matches.\n", "Commit pair 814 matches.\n", "Commit pair 815 matches.\n", "Commit pair 816 matches.\n", "Commit pair 817 matches.\n", "Commit pair 818 matches.\n", "Commit pair 819 matches.\n", "Commit pair 820 matches.\n", "Commit pair 821 matches.\n", "Commit pair 822 matches.\n", "Commit pair 823 matches.\n", "Commit pair 824 matches.\n", "Commit pair 825 matches.\n", "Commit pair 826 matches.\n", "Commit pair 827 matches.\n", "Commit pair 828 matches.\n", "Commit pair 829 matches.\n", "Commit pair 830 matches.\n", "Commit pair 831 matches.\n", "Commit pair 832 matches.\n", "Commit pair 833 matches.\n", "Commit pair 834 matches.\n", "Commit pair 835 matches.\n", "Commit pair 836 matches.\n", "Commit pair 837 matches.\n", "Commit pair 838 matches.\n", "Commit pair 839 matches.\n", "Commit pair 840 matches.\n", "Commit pair 841 matches.\n", "Commit pair 842 matches.\n", "Commit pair 843 matches.\n", "Commit pair 844 matches.\n", "Commit pair 845 matches.\n", "Commit pair 846 matches.\n", "Commit pair 847 matches.\n", "Commit pair 848 matches.\n", "Commit pair 849 matches.\n", "Commit pair 850 matches.\n", "Commit pair 851 matches.\n", "Commit pair 852 matches.\n", "Commit pair 853 matches.\n", "Commit pair 854 matches.\n", "Commit pair 855 matches.\n", "Commit pair 856 matches.\n", "Commit pair 857 matches.\n", "Commit pair 858 matches.\n", "Commit pair 859 matches.\n", "Commit pair 860 matches.\n", "Commit pair 861 matches.\n", "Commit pair 862 matches.\n", "Commit pair 863 matches.\n", "Commit pair 864 matches.\n", "Commit pair 865 matches.\n", "Commit pair 866 matches.\n", "Commit pair 867 matches.\n", "Commit pair 868 matches.\n", "Commit pair 869 matches.\n", "Commit pair 870 matches.\n", "Commit pair 871 matches.\n", "Commit pair 872 matches.\n", "Commit pair 873 matches.\n", "Commit pair 874 matches.\n", "Commit pair 875 matches.\n", "Commit pair 876 matches.\n", "Commit pair 877 matches.\n", "Commit pair 878 matches.\n", "Commit pair 879 matches.\n", "Commit pair 880 matches.\n", "Commit pair 881 matches.\n", "Commit pair 882 matches.\n", "Commit pair 883 matches.\n", "Commit pair 884 matches.\n", "Commit pair 885 matches.\n", "Commit pair 886 matches.\n", "Commit pair 887 matches.\n", "Commit pair 888 matches.\n", "Commit pair 889 matches.\n", "Commit pair 890 matches.\n", "Commit pair 891 matches.\n", "Commit pair 892 matches.\n", "Commit pair 893 matches.\n", "Commit pair 894 matches.\n", "Commit pair 895 matches.\n", "Commit pair 896 matches.\n", "Commit pair 897 matches.\n", "Commit pair 898 matches.\n", "Commit pair 899 matches.\n", "Commit pair 900 matches.\n", "Commit pair 901 matches.\n", "Commit pair 902 matches.\n", "Commit pair 903 matches.\n", "Commit pair 904 matches.\n", "Commit pair 905 matches.\n", "Commit pair 906 matches.\n", "Commit pair 907 matches.\n", "Commit pair 908 matches.\n", "Commit pair 909 matches.\n", "Commit pair 910 matches.\n", "Commit pair 911 matches.\n", "Commit pair 912 matches.\n", "Commit pair 913 matches.\n", "Commit pair 914 matches.\n", "Commit pair 915 matches.\n", "Commit pair 916 matches.\n", "Commit pair 917 matches.\n", "Commit pair 918 matches.\n", "Commit pair 919 matches.\n", "Commit pair 920 matches.\n", "Commit pair 921 matches.\n", "Commit pair 922 matches.\n", "Commit pair 923 matches.\n", "Commit pair 924 matches.\n", "Commit pair 925 matches.\n", "Commit pair 926 matches.\n", "Commit pair 927 matches.\n", "Commit pair 928 matches.\n", "Commit pair 929 matches.\n", "Commit pair 930 matches.\n", "Commit pair 931 matches.\n", "Commit pair 932 matches.\n", "Commit pair 933 matches.\n", "Commit pair 934 matches.\n", "Commit pair 935 matches.\n", "Commit pair 936 matches.\n", "Commit pair 937 matches.\n", "Commit pair 938 matches.\n", "Commit pair 939 matches.\n", "Commit pair 940 matches.\n", "Commit pair 941 matches.\n", "Commit pair 942 matches.\n", "Commit pair 943 matches.\n", "Commit pair 944 matches.\n", "Commit pair 945 matches.\n", "Commit pair 946 matches.\n", "Commit pair 947 matches.\n", "Commit pair 948 matches.\n", "Commit pair 949 matches.\n", "Commit pair 950 matches.\n", "Commit pair 951 matches.\n", "Commit pair 952 matches.\n", "Commit pair 953 matches.\n", "Commit pair 954 matches.\n", "Commit pair 955 matches.\n", "Commit pair 956 matches.\n", "Commit pair 957 matches.\n", "Commit pair 958 matches.\n", "Commit pair 959 matches.\n", "Commit pair 960 matches.\n", "Commit pair 961 matches.\n", "Commit pair 962 matches.\n", "Commit pair 963 matches.\n", "Commit pair 964 matches.\n", "Commit pair 965 matches.\n", "Commit pair 966 matches.\n", "Commit pair 967 matches.\n", "Commit pair 968 matches.\n", "Commit pair 969 matches.\n", "Commit pair 970 matches.\n", "Commit pair 971 matches.\n", "Commit pair 972 matches.\n", "Commit pair 973 matches.\n", "Commit pair 974 matches.\n", "Commit pair 975 matches.\n", "Commit pair 976 matches.\n", "Commit pair 977 matches.\n", "Commit pair 978 matches.\n", "Commit pair 979 matches.\n", "Commit pair 980 matches.\n", "Commit pair 981 matches.\n", "Commit pair 982 matches.\n", "Commit pair 983 matches.\n", "Commit pair 984 matches.\n", "Commit pair 985 matches.\n", "Commit pair 986 matches.\n", "Commit pair 987 matches.\n", "Commit pair 988 matches.\n", "Commit pair 989 matches.\n", "Commit pair 990 matches.\n", "Commit pair 991 matches.\n", "Commit pair 992 matches.\n", "Commit pair 993 matches.\n", "Commit pair 994 matches.\n", "Commit pair 995 matches.\n", "Commit pair 996 matches.\n", "Commit pair 997 matches.\n", "Commit pair 998 matches.\n", "Commit pair 999 matches.\n", "Commit pair 1000 matches.\n", "Commit pair 1001 matches.\n", "Commit pair 1002 matches.\n", "Commit pair 1003 matches.\n", "Commit pair 1004 matches.\n", "Commit pair 1005 matches.\n", "Commit pair 1006 matches.\n", "Commit pair 1007 matches.\n", "Commit pair 1008 matches.\n", "Commit pair 1009 matches.\n", "Commit pair 1010 matches.\n", "Commit pair 1011 matches.\n", "Commit pair 1012 matches.\n", "Commit pair 1013 matches.\n", "Commit pair 1014 matches.\n", "Commit pair 1015 matches.\n", "Commit pair 1016 matches.\n", "Commit pair 1017 matches.\n", "Commit pair 1018 matches.\n", "Commit pair 1019 matches.\n", "Commit pair 1020 matches.\n", "Commit pair 1021 matches.\n", "Commit pair 1022 matches.\n", "Commit pair 1023 matches.\n", "Commit pair 1024 matches.\n", "Commit pair 1025 matches.\n", "Commit pair 1026 matches.\n", "Commit pair 1027 matches.\n", "Commit pair 1028 matches.\n", "Commit pair 1029 matches.\n", "Commit pair 1030 matches.\n", "Commit pair 1031 matches.\n", "Commit pair 1032 matches.\n", "Commit pair 1033 matches.\n", "Commit pair 1034 matches.\n", "Commit pair 1035 matches.\n", "Commit pair 1036 matches.\n", "Commit pair 1037 matches.\n", "Commit pair 1038 matches.\n", "Commit pair 1039 matches.\n", "Commit pair 1040 matches.\n", "Commit pair 1041 matches.\n", "Commit pair 1042 matches.\n", "Commit pair 1043 matches.\n", "Commit pair 1044 matches.\n", "Commit pair 1045 matches.\n", "Commit pair 1046 matches.\n", "Commit pair 1047 matches.\n", "Commit pair 1048 matches.\n", "Commit pair 1049 matches.\n", "Commit pair 1050 matches.\n", "Commit pair 1051 matches.\n", "Commit pair 1052 matches.\n", "Commit pair 1053 matches.\n", "Commit pair 1054 matches.\n", "Commit pair 1055 matches.\n", "Commit pair 1056 matches.\n", "Commit pair 1057 matches.\n", "Commit pair 1058 matches.\n", "Commit pair 1059 matches.\n", "Commit pair 1060 matches.\n", "Commit pair 1061 matches.\n", "Commit pair 1062 matches.\n", "Commit pair 1063 matches.\n", "Commit pair 1064 matches.\n", "Commit pair 1065 matches.\n", "Commit pair 1066 matches.\n", "Commit pair 1067 matches.\n", "Commit pair 1068 matches.\n", "Commit pair 1069 matches.\n", "Commit pair 1070 matches.\n", "Commit pair 1071 matches.\n", "Commit pair 1072 matches.\n", "Commit pair 1073 matches.\n", "Commit pair 1074 matches.\n", "Commit pair 1075 matches.\n", "Commit pair 1076 matches.\n", "Commit pair 1077 matches.\n", "Commit pair 1078 matches.\n", "Commit pair 1079 matches.\n", "Commit pair 1080 matches.\n", "Commit pair 1081 matches.\n", "Commit pair 1082 matches.\n", "Commit pair 1083 matches.\n", "Commit pair 1084 matches.\n", "Commit pair 1085 matches.\n", "Commit pair 1086 matches.\n", "Commit pair 1087 matches.\n", "Commit pair 1088 matches.\n", "Commit pair 1089 matches.\n", "Commit pair 1090 matches.\n", "Commit pair 1091 matches.\n", "Commit pair 1092 matches.\n", "Commit pair 1093 matches.\n", "Commit pair 1094 matches.\n", "Commit pair 1095 matches.\n", "Commit pair 1096 matches.\n", "Commit pair 1097 matches.\n", "Commit pair 1098 matches.\n", "Commit pair 1099 matches.\n", "Commit pair 1100 matches.\n", "Commit pair 1101 matches.\n", "Commit pair 1102 matches.\n", "Commit pair 1103 matches.\n", "Commit pair 1104 matches.\n", "Commit pair 1105 matches.\n", "Commit pair 1106 matches.\n", "Commit pair 1107 matches.\n", "Commit pair 1108 matches.\n", "Commit pair 1109 matches.\n", "Commit pair 1110 matches.\n", "Commit pair 1111 matches.\n", "Commit pair 1112 matches.\n", "Commit pair 1113 matches.\n", "Commit pair 1114 matches.\n", "Commit pair 1115 matches.\n", "Commit pair 1116 matches.\n", "Commit pair 1117 matches.\n", "Commit pair 1118 matches.\n", "Commit pair 1119 matches.\n", "Commit pair 1120 matches.\n", "Commit pair 1121 matches.\n", "Commit pair 1122 matches.\n", "Commit pair 1123 matches.\n", "Commit pair 1124 matches.\n", "Commit pair 1125 matches.\n", "Commit pair 1126 matches.\n", "Commit pair 1127 matches.\n", "Commit pair 1128 matches.\n", "Commit pair 1129 matches.\n", "Commit pair 1130 matches.\n", "Commit pair 1131 matches.\n", "Commit pair 1132 matches.\n", "Commit pair 1133 matches.\n", "Commit pair 1134 matches.\n", "Commit pair 1135 matches.\n", "Commit pair 1136 matches.\n", "Commit pair 1137 matches.\n", "Commit pair 1138 matches.\n", "Commit pair 1139 matches.\n", "Commit pair 1140 matches.\n", "Commit pair 1141 matches.\n", "Commit pair 1142 matches.\n", "Commit pair 1143 matches.\n", "Commit pair 1144 matches.\n", "Commit pair 1145 matches.\n", "Commit pair 1146 matches.\n", "Commit pair 1147 matches.\n", "Commit pair 1148 matches.\n", "Commit pair 1149 matches.\n", "Commit pair 1150 matches.\n", "Commit pair 1151 matches.\n", "Commit pair 1152 matches.\n", "Commit pair 1153 matches.\n", "Commit pair 1154 matches.\n", "Commit pair 1155 matches.\n", "Commit pair 1156 matches.\n", "Commit pair 1157 matches.\n", "Commit pair 1158 matches.\n", "Commit pair 1159 matches.\n", "Commit pair 1160 matches.\n", "Commit pair 1161 matches.\n", "Commit pair 1162 matches.\n", "Commit pair 1163 matches.\n", "Commit pair 1164 matches.\n", "Commit pair 1165 matches.\n", "Commit pair 1166 matches.\n", "Commit pair 1167 matches.\n", "Commit pair 1168 matches.\n", "Commit pair 1169 matches.\n", "Commit pair 1170 matches.\n", "Commit pair 1171 matches.\n", "Commit pair 1172 matches.\n", "Commit pair 1173 matches.\n", "Commit pair 1174 matches.\n", "Commit pair 1175 matches.\n", "Commit pair 1176 matches.\n", "Commit pair 1177 matches.\n", "Commit pair 1178 matches.\n", "Commit pair 1179 matches.\n", "Commit pair 1180 matches.\n", "Commit pair 1181 matches.\n", "Commit pair 1182 matches.\n", "Commit pair 1183 matches.\n", "Commit pair 1184 matches.\n", "Commit pair 1185 matches.\n", "Commit pair 1186 matches.\n", "Commit pair 1187 matches.\n", "Commit pair 1188 matches.\n", "Commit pair 1189 matches.\n", "Commit pair 1190 matches.\n", "Commit pair 1191 matches.\n", "Commit pair 1192 matches.\n", "Commit pair 1193 matches.\n", "Commit pair 1194 matches.\n", "Commit pair 1195 matches.\n", "Commit pair 1196 matches.\n", "Commit pair 1197 matches.\n", "Commit pair 1198 matches.\n", "Commit pair 1199 matches.\n", "Commit pair 1200 matches.\n", "Commit pair 1201 matches.\n", "Commit pair 1202 matches.\n", "Commit pair 1203 matches.\n", "Commit pair 1204 matches.\n", "Commit pair 1205 matches.\n", "Commit pair 1206 matches.\n", "Commit pair 1207 matches.\n", "Commit pair 1208 matches.\n", "Commit pair 1209 matches.\n", "Commit pair 1210 matches.\n", "Commit pair 1211 matches.\n", "Commit pair 1212 matches.\n", "Commit pair 1213 matches.\n", "Commit pair 1214 matches.\n", "Commit pair 1215 matches.\n", "Commit pair 1216 matches.\n", "Commit pair 1217 matches.\n", "Commit pair 1218 matches.\n", "Commit pair 1219 matches.\n", "Commit pair 1220 matches.\n", "Commit pair 1221 matches.\n", "Commit pair 1222 matches.\n", "Commit pair 1223 matches.\n", "Commit pair 1224 matches.\n", "Commit pair 1225 matches.\n", "Commit pair 1226 matches.\n", "Commit pair 1227 matches.\n", "Commit pair 1228 matches.\n", "Commit pair 1229 matches.\n", "Commit pair 1230 matches.\n", "Commit pair 1231 matches.\n", "Commit pair 1232 matches.\n", "Commit pair 1233 matches.\n", "Commit pair 1234 matches.\n", "Commit pair 1235 matches.\n", "Commit pair 1236 matches.\n", "Commit pair 1237 matches.\n", "Commit pair 1238 matches.\n", "Commit pair 1239 matches.\n", "Commit pair 1240 matches.\n", "Commit pair 1241 matches.\n", "Commit pair 1242 matches.\n", "Commit pair 1243 matches.\n", "Commit pair 1244 matches.\n", "Commit pair 1245 matches.\n", "Commit pair 1246 matches.\n", "Commit pair 1247 matches.\n", "Commit pair 1248 matches.\n", "Commit pair 1249 matches.\n", "Commit pair 1250 matches.\n", "Commit pair 1251 matches.\n", "Commit pair 1252 matches.\n", "Commit pair 1253 matches.\n", "Commit pair 1254 matches.\n", "Commit pair 1255 matches.\n", "Commit pair 1256 matches.\n", "Commit pair 1257 matches.\n", "Commit pair 1258 matches.\n", "Commit pair 1259 matches.\n", "Commit pair 1260 matches.\n", "Commit pair 1261 matches.\n", "Commit pair 1262 matches.\n", "Commit pair 1263 matches.\n", "Commit pair 1264 matches.\n", "Commit pair 1265 matches.\n", "Commit pair 1266 matches.\n", "Commit pair 1267 matches.\n", "Commit pair 1268 matches.\n", "Commit pair 1269 matches.\n", "Commit pair 1270 matches.\n", "Commit pair 1271 matches.\n", "Commit pair 1272 matches.\n", "Commit pair 1273 matches.\n", "Commit pair 1274 matches.\n", "Commit pair 1275 matches.\n", "Commit pair 1276 matches.\n", "Commit pair 1277 matches.\n", "Commit pair 1278 matches.\n", "Commit pair 1279 matches.\n", "Commit pair 1280 matches.\n", "Commit pair 1281 matches.\n", "Commit pair 1282 matches.\n", "Commit pair 1283 matches.\n", "Commit pair 1284 matches.\n", "Commit pair 1285 matches.\n", "Commit pair 1286 matches.\n", "Commit pair 1287 matches.\n", "Commit pair 1288 matches.\n", "Commit pair 1289 matches.\n", "Commit pair 1290 matches.\n", "Commit pair 1291 matches.\n", "Commit pair 1292 matches.\n", "Commit pair 1293 matches.\n", "Commit pair 1294 matches.\n", "Commit pair 1295 matches.\n", "Commit pair 1296 matches.\n", "Commit pair 1297 matches.\n", "Commit pair 1298 matches.\n", "Commit pair 1299 matches.\n", "Commit pair 1300 matches.\n", "Commit pair 1301 matches.\n", "Commit pair 1302 matches.\n", "Commit pair 1303 matches.\n", "Commit pair 1304 matches.\n", "Commit pair 1305 matches.\n", "Commit pair 1306 matches.\n", "Commit pair 1307 matches.\n", "Commit pair 1308 matches.\n", "Commit pair 1309 matches.\n", "Commit pair 1310 matches.\n", "Commit pair 1311 matches.\n", "Commit pair 1312 matches.\n", "Commit pair 1313 matches.\n", "Commit pair 1314 matches.\n", "Commit pair 1315 matches.\n", "Commit pair 1316 matches.\n", "Commit pair 1317 matches.\n", "Commit pair 1318 matches.\n", "Commit pair 1319 matches.\n", "Commit pair 1320 matches.\n", "Commit pair 1321 matches.\n", "Commit pair 1322 matches.\n", "Commit pair 1323 matches.\n", "Commit pair 1324 matches.\n", "Commit pair 1325 matches.\n", "Commit pair 1326 matches.\n", "Commit pair 1327 matches.\n", "Commit pair 1328 matches.\n", "Commit pair 1329 matches.\n", "Commit pair 1330 matches.\n", "Commit pair 1331 matches.\n", "Commit pair 1332 matches.\n", "Commit pair 1333 matches.\n", "Commit pair 1334 matches.\n", "Commit pair 1335 matches.\n", "Commit pair 1336 matches.\n", "Commit pair 1337 matches.\n", "Commit pair 1338 matches.\n", "Commit pair 1339 matches.\n", "Commit pair 1340 matches.\n", "Commit pair 1341 matches.\n", "Commit pair 1342 matches.\n", "Commit pair 1343 matches.\n", "Commit pair 1344 matches.\n", "Commit pair 1345 matches.\n", "Commit pair 1346 matches.\n", "Commit pair 1347 matches.\n", "Commit pair 1348 matches.\n", "Commit pair 1349 matches.\n", "Commit pair 1350 matches.\n"]}], "source": ["for index in range(len(commits_v1)):\n", "    if index < len(commits_v3_tmp1) - offset:\n", "        assert (\n", "            commits_v1[index][\"current_hash\"]\n", "            == commits_v3_tmp1[index + offset][\"current_hash\"]\n", "        ), index\n", "        print(f\"Commit pair {index} matches.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8118e2e066d97afdbf6395ab3cd9a5066cbce823\n", "8118e2e066d97afdbf6395ab3cd9a5066cbce823\n", "8118e2e066d97afdbf6395ab3cd9a5066cbce823\n"]}], "source": ["commits_v1 = commits_v1\n", "commits_1000_v1 = json.load(\n", "    open(\"/home/<USER>/zhuoran/commit_msgs/commits_1000_v1.json\")\n", ")\n", "commits_10_v1 = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_10_v1.json\"))\n", "\n", "print(commits_v1[0][\"current_hash\"])\n", "print(commits_1000_v1[0][\"current_hash\"])\n", "print(commits_10_v1[0][\"current_hash\"])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8118e2e066d97afdbf6395ab3cd9a5066cbce823\n", "8118e2e066d97afdbf6395ab3cd9a5066cbce823\n", "8118e2e066d97afdbf6395ab3cd9a5066cbce823\n", "8118e2e066d97afdbf6395ab3cd9a5066cbce823\n"]}], "source": ["commits_10_v0 = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_10_v0.json\"))\n", "commits_10_v1 = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_10_v1.json\"))\n", "commits_10_v2 = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_10_v2.json\"))\n", "commits_10_v3 = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_10_v3.json\"))\n", "\n", "print(commits_10_v0[0][\"current_hash\"])\n", "print(commits_10_v1[0][\"current_hash\"])\n", "print(commits_10_v2[0][\"current_hash\"])\n", "print(commits_10_v3[0][\"current_hash\"])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10\n"]}], "source": ["success = 0\n", "for commit_v0, commit_v1, commit_v2, commit_v3 in zip(\n", "    commits_10_v0, commits_10_v1, commits_10_v2, commits_10_v3\n", "):\n", "    assert (\n", "        commit_v0[\"current_hash\"]\n", "        == commit_v1[\"current_hash\"]\n", "        == commit_v2[\"current_hash\"]\n", "        == commit_v3[\"current_hash\"]\n", "    )\n", "    success += 1\n", "print(success)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8118e2e066d97afdbf6395ab3cd9a5066cbce823\n", "8118e2e066d97afdbf6395ab3cd9a5066cbce823\n"]}], "source": ["commits_v1 = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/commits_v1.json\"))\n", "commits_v4_tmp = json.load(\n", "    open(\"/home/<USER>/zhuoran/commit_msgs/commits_v4_tmp.json\")\n", ")\n", "\n", "print(commits_v1[0][\"current_hash\"])\n", "print(commits_v4_tmp[0][\"current_hash\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Patch"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "eval_set = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_v1.json\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["new_eval_set = {}\n", "for key, split in eval_set.items():\n", "    if key.endswith(\"_latest\"):\n", "        key = key[:-7]\n", "    new_eval_set[key] = split"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['largest_diff', 'smallest_diff', 'clients', 'deploy', 'base', 'data', 'services', 'tools', 'research', 'experimental', '.github', 'models', 'third_party', '.vscode'])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["new_eval_set.keys()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_v1_new_keys.json\", \"w\") as f:\n", "    json.dump(new_eval_set, f, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
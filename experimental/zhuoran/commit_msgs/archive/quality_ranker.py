# Schema:
# [
# {
#   "current_hash": "8118e2e066d97afdbf6395ab3cd9a5066cbce823",
#   "parent_hashes": [
#     "9276699e9a822bc7dca1e5f1e5c0a1641f795e7f"
#   ],
#   "author": {
#     "name": "<PERSON>",
#     "email": "<EMAIL>"
#   },
#   "authored_date": "2024-10-23T23:30:08",
#   "committer": {
#     "name": "GitHub",
#     "email": "<EMAIL>"
#   },
#   "committed_date": "2024-10-23T23:30:08",
#   "message": "Remove \"Explain issues using Augment\" (#14344)\n\nThis feature is too strongly overlapping with \"Fix using Augment\" and rarely in use.",
#   "diffs": {
#     "clients/common/webviews/src/apps/chat/types/slash-command-list.ts": {
#       "insertions": 3,
#       "deletions": 125,
#       "lines": 128,
#       "files": 7,
#       "content": "diff --git a/clients/common/webviews/src/apps/chat/types/slash-command-list.ts b/clients/common/webviews/src/apps/chat/types/slash-command-list.ts\nindex 3c324ec04..3e7f2e60f 100644\n--- a/clients/common/webviews/src/apps/chat/types/slash-command-list.ts\n+++ b/clients/common/webviews/src/apps/chat/types/slash-command-list.ts\n@@ -97,16 +97,6 @@ async function formatFixPrompt(\n   return prompt;\n }\n \n-async function formatExplainIssuesPrompt(\n-  _input: string,\n-  chatModel: ChatModel,\n-): Promise<string> {\n-  const diagnostics = await getDiagnostics(chatModel);\n-  let prompt = \"Explain issues in the selected code.\";\n-  prompt += formatDiagnostics(diagnostics);\n-  return prompt;\n-}\n-\n export interface SlashCommand extends ISlashCommandOptionData {\n   requiresInput: boolean;\n   requiresEditorSelection: boolean;\n@@ -139,17 +129,6 @@ export const slashCommandList: SlashCommand[] = [\n     visible: true,\n     promptFactory: async () => `Explain the selected code`,\n   },\n-  {\n-    id: \"explain_issues\",\n-    label: \"/explain_issues\",\n-    description: \"in the selection\",\n-    requiresInput: false,\n-    requiresEditorSelection: true,\n-    placeholderText: \"\",\n-    selectionReminderMessage: \"Select code in the editor to explain.\",\n-    visible: false,\n-    promptFactory: formatExplainIssuesPrompt,\n-  },\n   {\n     id: \"fix\",\n     label: \"/fix\",",
#       "header": "diff --git a/clients/common/webviews/src/apps/chat/types/slash-command-list.ts b/clients/common/webviews/src/apps/chat/types/slash-command-list.ts\nindex 3c324ec04..3e7f2e60f 100644\n--- a/clients/common/webviews/src/apps/chat/types/slash-command-list.ts\n+++ b/clients/common/webviews/src/apps/chat/types/slash-command-list.ts",
#       "hunks": [
#         {
#           "insertions": 0,
#           "deletions": 10,
#           "content": "@@ -97,16 +97,6 @@ async function formatFixPrompt(\n   return prompt;\n }\n \n-async function formatExplainIssuesPrompt(\n-  _input: string,\n-  chatModel: ChatModel,\n-): Promise<string> {\n-  const diagnostics = await getDiagnostics(chatModel);\n-  let prompt = \"Explain issues in the selected code.\";\n-  prompt += formatDiagnostics(diagnostics);\n-  return prompt;\n-}\n-\n export interface SlashCommand extends ISlashCommandOptionData {\n   requiresInput: boolean;\n   requiresEditorSelection: boolean;"
#         },
#         {
#           "insertions": 0,
#           "deletions": 11,
#           "content": "@@ -139,17 +129,6 @@ export const slashCommandList: SlashCommand[] = [\n     visible: true,\n     promptFactory: async () => `Explain the selected code`,\n   },\n-  {\n-    id: \"explain_issues\",\n-    label: \"/explain_issues\",\n-    description: \"in the selection\",\n-    requiresInput: false,\n-    requiresEditorSelection: true,\n-    placeholderText: \"\",\n-    selectionReminderMessage: \"Select code in the editor to explain.\",\n-    visible: false,\n-    promptFactory: formatExplainIssuesPrompt,\n-  },\n   {\n     id: \"fix\",\n     label: \"/fix\","
#         }
#       ]
#     },
#     "clients/vscode/package.json": {
#       "insertions": 3,
#       "deletions": 125,
#       "lines": 128,
#       "files": 7,
#       "content": "diff --git a/clients/vscode/package.json b/clients/vscode/package.json\nindex 5d72adc23..b3f43d35c 100644\n--- a/clients/vscode/package.json\n+++ b/clients/vscode/package.json\n@@ -255,11 +255,6 @@\n                 \"command\": \"vscode-augment.chat.slash.explain\",\n                 \"title\": \"Explain using Augment\"\n             },\n-            {\n-                \"category\": \"Augment\",\n-                \"command\": \"vscode-augment.chat.slash.explain_issues\",\n-                \"title\": \"Explain issues using Augment\"\n-            },\n             {\n                 \"category\": \"Augment\",\n                 \"command\": \"vscode-augment.chat.slash.test\",\n@@ -899,10 +894,6 @@\n                     \"command\": \"vscode-augment.chat.slash.fix\",\n                     \"when\": \"vscode-augment.enableDebugFeatures\"\n                 },\n-                {\n-                    \"command\": \"vscode-augment.chat.slash.explain_issues\",\n-                    \"when\": \"vscode-augment.enableDebugFeatures\"\n-                },\n                 {\n                     \"command\": \"vscode-augment.focusAugmentPanel\",\n                     \"when\": \"vscode-augment.enableDebugFeatures || vscode-augment.isLoggedIn\"",
#       "header": "diff --git a/clients/vscode/package.json b/clients/vscode/package.json\nindex 5d72adc23..b3f43d35c 100644\n--- a/clients/vscode/package.json\n+++ b/clients/vscode/package.json",
#       "hunks": [
#         {
#           "insertions": 0,
#           "deletions": 5,
#           "content": "@@ -255,11 +255,6 @@\n                 \"command\": \"vscode-augment.chat.slash.explain\",\n                 \"title\": \"Explain using Augment\"\n             },\n-            {\n-                \"category\": \"Augment\",\n-                \"command\": \"vscode-augment.chat.slash.explain_issues\",\n-                \"title\": \"Explain issues using Augment\"\n-            },\n             {\n                 \"category\": \"Augment\",\n                 \"command\": \"vscode-augment.chat.slash.test\","
#         },
#         {
#           "insertions": 0,
#           "deletions": 4,
#           "content": "@@ -899,10 +894,6 @@\n                     \"command\": \"vscode-augment.chat.slash.fix\",\n                     \"when\": \"vscode-augment.enableDebugFeatures\"\n                 },\n-                {\n-                    \"command\": \"vscode-augment.chat.slash.explain_issues\",\n-                    \"when\": \"vscode-augment.enableDebugFeatures\"\n-                },\n                 {\n                     \"command\": \"vscode-augment.focusAugmentPanel\",\n                     \"when\": \"vscode-augment.enableDebugFeatures || vscode-augment.isLoggedIn\""
#         }
#       ]
#     },
#     "clients/vscode/src/code-actions/slash-commands.ts": {
#       "insertions": 3,
#       "deletions": 125,
#       "lines": 128,
#       "files": 7,
#       "content": "diff --git a/clients/vscode/src/code-actions/slash-commands.ts b/clients/vscode/src/code-actions/slash-commands.ts\nindex e8f3ede65..a2fa922fa 100644\n--- a/clients/vscode/src/code-actions/slash-commands.ts\n+++ b/clients/vscode/src/code-actions/slash-commands.ts\n@@ -1,6 +1,6 @@\n import * as vscode from \"vscode\";\n \n-import { SlashExplainIssuesCommand, SlashFixCommand } from \"../commands/slash-command\";\n+import { SlashFixCommand } from \"../commands/slash-command\";\n \n export class SlashFixCommandProvider implements vscode.CodeActionProvider {\n     public provideCodeActions(\n@@ -34,36 +34,3 @@ export class SlashFixCommandProvider implements vscode.CodeActionProvider {\n         return codeAction;\n     }\n }\n-\n-export class SlashExplainIssuesCommandProvider implements vscode.CodeActionProvider {\n-    public provideCodeActions(\n-        _document: vscode.TextDocument,\n-        _range: vscode.Range | vscode.Selection,\n-        context: vscode.CodeActionContext,\n-        _token: vscode.CancellationToken\n-    ): Promise<vscode.CodeAction[] | undefined> {\n-        const diagnostics = context.diagnostics;\n-        if (diagnostics.length === 0) {\n-            return Promise.resolve(undefined);\n-        }\n-\n-        const codeAction = new vscode.CodeAction(\n-            \"Explain issues using Augment\",\n-            vscode.CodeActionKind.QuickFix\n-        );\n-        codeAction.command = {\n-            command: SlashExplainIssuesCommand.commandID,\n-            title: \"Explain issues using Augment\",\n-            arguments: [diagnostics],\n-        };\n-\n-        return Promise.resolve([codeAction]);\n-    }\n-\n-    public resolveCodeAction(\n-        codeAction: vscode.CodeAction,\n-        _token: vscode.CancellationToken\n-    ): vscode.CodeAction {\n-        return codeAction;\n-    }\n-}",
#       "header": "diff --git a/clients/vscode/src/code-actions/slash-commands.ts b/clients/vscode/src/code-actions/slash-commands.ts\nindex e8f3ede65..a2fa922fa 100644\n--- a/clients/vscode/src/code-actions/slash-commands.ts\n+++ b/clients/vscode/src/code-actions/slash-commands.ts",
#       "hunks": [
#         {
#           "insertions": 1,
#           "deletions": 1,
#           "content": "@@ -1,6 +1,6 @@\n import * as vscode from \"vscode\";\n \n-import { SlashExplainIssuesCommand, SlashFixCommand } from \"../commands/slash-command\";\n+import { SlashFixCommand } from \"../commands/slash-command\";\n \n export class SlashFixCommandProvider implements vscode.CodeActionProvider {\n     public provideCodeActions("
#         },
#         {
#           "insertions": 0,
#           "deletions": 33,
#           "content": "@@ -34,36 +34,3 @@ export class SlashFixCommandProvider implements vscode.CodeActionProvider {\n         return codeAction;\n     }\n }\n-\n-export class SlashExplainIssuesCommandProvider implements vscode.CodeActionProvider {\n-    public provideCodeActions(\n-        _document: vscode.TextDocument,\n-        _range: vscode.Range | vscode.Selection,\n-        context: vscode.CodeActionContext,\n-        _token: vscode.CancellationToken\n-    ): Promise<vscode.CodeAction[] | undefined> {\n-        const diagnostics = context.diagnostics;\n-        if (diagnostics.length === 0) {\n-            return Promise.resolve(undefined);\n-        }\n-\n-        const codeAction = new vscode.CodeAction(\n-            \"Explain issues using Augment\",\n-            vscode.CodeActionKind.QuickFix\n-        );\n-        codeAction.command = {\n-            command: SlashExplainIssuesCommand.commandID,\n-            title: \"Explain issues using Augment\",\n-            arguments: [diagnostics],\n-        };\n-\n-        return Promise.resolve([codeAction]);\n-    }\n-\n-    public resolveCodeAction(\n-        codeAction: vscode.CodeAction,\n-        _token: vscode.CancellationToken\n-    ): vscode.CodeAction {\n-        return codeAction;\n-    }\n-}"
#         }
#       ]
#     },
#     "clients/vscode/src/command-manager-init.ts": {
#       "insertions": 3,
#       "deletions": 125,
#       "lines": 128,
#       "files": 7,
#       "content": "diff --git a/clients/vscode/src/command-manager-init.ts b/clients/vscode/src/command-manager-init.ts\nindex cb6d1d599..5a404457a 100644\n--- a/clients/vscode/src/command-manager-init.ts\n+++ b/clients/vscode/src/command-manager-init.ts\n@@ -56,12 +56,7 @@ import { ShowAugmentCommands } from \"./commands/show-menu\";\n import { ShowNextEditPanelCommand } from \"./commands/show-next-edit-panel\";\n import { ShowSidebarChatCommand } from \"./commands/show-sidebar-chat\";\n import { ShowSidebarWorkspaceContextCommand } from \"./commands/show-sidebar-workspace-context\";\n-import {\n-    SlashExplainCommand,\n-    SlashExplainIssuesCommand,\n-    SlashFixCommand,\n-    SlashTestCommand,\n-} from \"./commands/slash-command\";\n+import { SlashExplainCommand, SlashFixCommand, SlashTestCommand } from \"./commands/slash-command\";\n import { StatusBarClick } from \"./commands/status-bar-click\";\n import { ToggleCompletionsCommand } from \"./commands/toggle-completions\";\n import { RecentCompletions } from \"./completions/recent-completions\";\n@@ -131,12 +126,6 @@ export function initCommandManager(\n             chatExtensionEvent,\n             syncingEnabledTracker\n         ),\n-        new SlashExplainIssuesCommand(\n-            extension,\n-            configListener,\n-            chatExtensionEvent,\n-            syncingEnabledTracker\n-        ),\n         new SlashTestCommand(extension, configListener, chatExtensionEvent, syncingEnabledTracker),\n     ]);\n ",
#       "header": "diff --git a/clients/vscode/src/command-manager-init.ts b/clients/vscode/src/command-manager-init.ts\nindex cb6d1d599..5a404457a 100644\n--- a/clients/vscode/src/command-manager-init.ts\n+++ b/clients/vscode/src/command-manager-init.ts",
#       "hunks": [
#         {
#           "insertions": 1,
#           "deletions": 6,
#           "content": "@@ -56,12 +56,7 @@ import { ShowAugmentCommands } from \"./commands/show-menu\";\n import { ShowNextEditPanelCommand } from \"./commands/show-next-edit-panel\";\n import { ShowSidebarChatCommand } from \"./commands/show-sidebar-chat\";\n import { ShowSidebarWorkspaceContextCommand } from \"./commands/show-sidebar-workspace-context\";\n-import {\n-    SlashExplainCommand,\n-    SlashExplainIssuesCommand,\n-    SlashFixCommand,\n-    SlashTestCommand,\n-} from \"./commands/slash-command\";\n+import { SlashExplainCommand, SlashFixCommand, SlashTestCommand } from \"./commands/slash-command\";\n import { StatusBarClick } from \"./commands/status-bar-click\";\n import { ToggleCompletionsCommand } from \"./commands/toggle-completions\";\n import { RecentCompletions } from \"./completions/recent-completions\";"
#         },
#         {
#           "insertions": 0,
#           "deletions": 6,
#           "content": "@@ -131,12 +126,6 @@ export function initCommandManager(\n             chatExtensionEvent,\n             syncingEnabledTracker\n         ),\n-        new SlashExplainIssuesCommand(\n-            extension,\n-            configListener,\n-            chatExtensionEvent,\n-            syncingEnabledTracker\n-        ),\n         new SlashTestCommand(extension, configListener, chatExtensionEvent, syncingEnabledTracker),\n     ]);"
#         }
#       ]
#     },
#     "clients/vscode/src/commands/slash-command.ts": {
#       "insertions": 3,
#       "deletions": 125,
#       "lines": 128,
#       "files": 7,
#       "content": "diff --git a/clients/vscode/src/commands/slash-command.ts b/clients/vscode/src/commands/slash-command.ts\nindex 477ae5c19..ebbeade15 100644\n--- a/clients/vscode/src/commands/slash-command.ts\n+++ b/clients/vscode/src/commands/slash-command.ts\n@@ -161,34 +161,6 @@ export class SlashExplainCommand extends SlashActionCommand {\n     }\n }\n \n-export class SlashExplainIssuesCommand extends SlashActionCommand {\n-    public static readonly commandID = \"vscode-augment.chat.slash.explain_issues\";\n-\n-    commandID = SlashExplainIssuesCommand.commandID;\n-\n-    constructor(\n-        extension: AugmentExtension,\n-        configListener: AugmentConfigListener,\n-        chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,\n-        syncingEnabledTracker: SyncingEnabledTracker\n-    ) {\n-        super(\n-            extension,\n-            configListener,\n-            chatExtensionEvent,\n-            syncingEnabledTracker,\n-            \"Explain issues using Augment\",\n-            false\n-        );\n-    }\n-\n-    async run(diagnostics: vscode.Diagnostic[]) {\n-        await this.focusAndShowChatPanel(\"Explain Issues\");\n-        this.updateSelectionToCoverDiagnostics(diagnostics);\n-        this._chatExtensionEvent.fire(ChatExtensionMessage.runSlashExplainIssues);\n-    }\n-}\n-\n export class SlashTestCommand extends SlashActionCommand {\n     public static readonly commandID = \"vscode-augment.chat.slash.test\";\n ",
#       "header": "diff --git a/clients/vscode/src/commands/slash-command.ts b/clients/vscode/src/commands/slash-command.ts\nindex 477ae5c19..ebbeade15 100644\n--- a/clients/vscode/src/commands/slash-command.ts\n+++ b/clients/vscode/src/commands/slash-command.ts",
#       "hunks": [
#         {
#           "insertions": 0,
#           "deletions": 28,
#           "content": "@@ -161,34 +161,6 @@ export class SlashExplainCommand extends SlashActionCommand {\n     }\n }\n \n-export class SlashExplainIssuesCommand extends SlashActionCommand {\n-    public static readonly commandID = \"vscode-augment.chat.slash.explain_issues\";\n-\n-    commandID = SlashExplainIssuesCommand.commandID;\n-\n-    constructor(\n-        extension: AugmentExtension,\n-        configListener: AugmentConfigListener,\n-        chatExtensionEvent: vscode.EventEmitter<ChatExtensionMessage>,\n-        syncingEnabledTracker: SyncingEnabledTracker\n-    ) {\n-        super(\n-            extension,\n-            configListener,\n-            chatExtensionEvent,\n-            syncingEnabledTracker,\n-            \"Explain issues using Augment\",\n-            false\n-        );\n-    }\n-\n-    async run(diagnostics: vscode.Diagnostic[]) {\n-        await this.focusAndShowChatPanel(\"Explain Issues\");\n-        this.updateSelectionToCoverDiagnostics(diagnostics);\n-        this._chatExtensionEvent.fire(ChatExtensionMessage.runSlashExplainIssues);\n-    }\n-}\n-\n export class SlashTestCommand extends SlashActionCommand {\n     public static readonly commandID = \"vscode-augment.chat.slash.test\";"
#         }
#       ]
#     },
#     "clients/vscode/src/extension.ts": {
#       "insertions": 3,
#       "deletions": 125,
#       "lines": 128,
#       "files": 7,
#       "content": "diff --git a/clients/vscode/src/extension.ts b/clients/vscode/src/extension.ts\nindex 2b7ac73da..4761fc63c 100644\n--- a/clients/vscode/src/extension.ts\n+++ b/clients/vscode/src/extension.ts\n@@ -25,10 +25,7 @@ import {\n import { AuthSessionStore } from \"./auth/auth-session-store\";\n import { OAuthFlow } from \"./auth/oauth-flow\";\n import ChatModel, { ChatRequest } from \"./chat/chat-model\";\n-import {\n-    SlashExplainIssuesCommandProvider,\n-    SlashFixCommandProvider,\n-} from \"./code-actions/slash-commands\";\n+import { SlashFixCommandProvider } from \"./code-actions/slash-commands\";\n import { CodeEditManager } from \"./code-edit\";\n import { AugmentInstruction } from \"./code-edit-types\";\n import { ShortCutCodeLensProvider } from \"./code-lens/short-cut\";\n@@ -663,7 +660,6 @@ export class AugmentExtension\n             let hotkeyHintsState: vscode.Disposable | undefined;\n             let emptyLineHintsState: vscode.Disposable | undefined;\n             let slashFixCodeActionState: vscode.Disposable | undefined;\n-            let slashExplainIssuesCodeActionState: vscode.Disposable | undefined;\n             let chatCodeActionState: vscode.Disposable | undefined;\n \n             const processConfigChange = (\n@@ -676,7 +672,6 @@ export class AugmentExtension\n                 emptyLineHintsState?.dispose();\n                 hotkeyHintsState?.dispose();\n                 slashFixCodeActionState?.dispose();\n-                slashExplainIssuesCodeActionState?.dispose();\n                 chatCodeActionState?.dispose();\n \n                 if (!newConfig.completions.enableAutomaticCompletions) {\n@@ -713,13 +708,6 @@ export class AugmentExtension\n                         new SlashFixCommandProvider()\n                     );\n                     this.disposeOnDisable.push(slashFixCodeActionState);\n-\n-                    slashExplainIssuesCodeActionState =\n-                        vscode.languages.registerCodeActionsProvider(\n-                            \"*\",\n-                            new SlashExplainIssuesCommandProvider()\n-                        );\n-                    this.disposeOnDisable.push(slashExplainIssuesCodeActionState);\n                 }\n \n                 if (",
#       "header": "diff --git a/clients/vscode/src/extension.ts b/clients/vscode/src/extension.ts\nindex 2b7ac73da..4761fc63c 100644\n--- a/clients/vscode/src/extension.ts\n+++ b/clients/vscode/src/extension.ts",
#       "hunks": [
#         {
#           "insertions": 1,
#           "deletions": 4,
#           "content": "@@ -25,10 +25,7 @@ import {\n import { AuthSessionStore } from \"./auth/auth-session-store\";\n import { OAuthFlow } from \"./auth/oauth-flow\";\n import ChatModel, { ChatRequest } from \"./chat/chat-model\";\n-import {\n-    SlashExplainIssuesCommandProvider,\n-    SlashFixCommandProvider,\n-} from \"./code-actions/slash-commands\";\n+import { SlashFixCommandProvider } from \"./code-actions/slash-commands\";\n import { CodeEditManager } from \"./code-edit\";\n import { AugmentInstruction } from \"./code-edit-types\";\n import { ShortCutCodeLensProvider } from \"./code-lens/short-cut\";"
#         },
#         {
#           "insertions": 0,
#           "deletions": 1,
#           "content": "@@ -663,7 +660,6 @@ export class AugmentExtension\n             let hotkeyHintsState: vscode.Disposable | undefined;\n             let emptyLineHintsState: vscode.Disposable | undefined;\n             let slashFixCodeActionState: vscode.Disposable | undefined;\n-            let slashExplainIssuesCodeActionState: vscode.Disposable | undefined;\n             let chatCodeActionState: vscode.Disposable | undefined;\n \n             const processConfigChange = ("
#         },
#         {
#           "insertions": 0,
#           "deletions": 1,
#           "content": "@@ -676,7 +672,6 @@ export class AugmentExtension\n                 emptyLineHintsState?.dispose();\n                 hotkeyHintsState?.dispose();\n                 slashFixCodeActionState?.dispose();\n-                slashExplainIssuesCodeActionState?.dispose();\n                 chatCodeActionState?.dispose();\n \n                 if (!newConfig.completions.enableAutomaticCompletions) {"
#         },
#         {
#           "insertions": 0,
#           "deletions": 7,
#           "content": "@@ -713,13 +708,6 @@ export class AugmentExtension\n                         new SlashFixCommandProvider()\n                     );\n                     this.disposeOnDisable.push(slashFixCodeActionState);\n-\n-                    slashExplainIssuesCodeActionState =\n-                        vscode.languages.registerCodeActionsProvider(\n-                            \"*\",\n-                            new SlashExplainIssuesCommandProvider()\n-                        );\n-                    this.disposeOnDisable.push(slashExplainIssuesCodeActionState);\n                 }\n \n                 if ("
#         }
#       ]
#     },
#     "clients/vscode/src/main-panel/apps/chat-webview-app.ts": {
#       "insertions": 3,
#       "deletions": 125,
#       "lines": 128,
#       "files": 7,
#       "content": "diff --git a/clients/vscode/src/main-panel/apps/chat-webview-app.ts b/clients/vscode/src/main-panel/apps/chat-webview-app.ts\nindex 353e8d67a..e1e16b8e4 100644\n--- a/clients/vscode/src/main-panel/apps/chat-webview-app.ts\n+++ b/clients/vscode/src/main-panel/apps/chat-webview-app.ts\n@@ -78,7 +78,6 @@ import { MainPanelAppController } from \"../main-panel-app-controller\";\n \n export enum ChatExtensionMessage {\n     runSlashFix = \"runSlashFix\",\n-    runSlashExplainIssues = \"runSlashExplainIssues\",\n     runSlashExplain = \"runSlashExplain\",\n     runSlashTest = \"runSlashTest\",\n }\n@@ -1537,13 +1536,6 @@ Do not make any additional changes, but ensure that all other code outside of th\n                 });\n                 break;\n             }\n-            case ChatExtensionMessage.runSlashExplainIssues: {\n-                await this._webview?.postMessage({\n-                    type: WebViewMessageType.runSlashCommand,\n-                    data: \"explain_issues\",\n-                });\n-                break;\n-            }\n             case ChatExtensionMessage.runSlashTest: {\n                 await this._webview?.postMessage({\n                     type: WebViewMessageType.runSlashCommand,",
#       "header": "diff --git a/clients/vscode/src/main-panel/apps/chat-webview-app.ts b/clients/vscode/src/main-panel/apps/chat-webview-app.ts\nindex 353e8d67a..e1e16b8e4 100644\n--- a/clients/vscode/src/main-panel/apps/chat-webview-app.ts\n+++ b/clients/vscode/src/main-panel/apps/chat-webview-app.ts",
#       "hunks": [
#         {
#           "insertions": 0,
#           "deletions": 1,
#           "content": "@@ -78,7 +78,6 @@ import { MainPanelAppController } from \"../main-panel-app-controller\";\n \n export enum ChatExtensionMessage {\n     runSlashFix = \"runSlashFix\",\n-    runSlashExplainIssues = \"runSlashExplainIssues\",\n     runSlashExplain = \"runSlashExplain\",\n     runSlashTest = \"runSlashTest\",\n }"
#         },
#         {
#           "insertions": 0,
#           "deletions": 7,
#           "content": "@@ -1537,13 +1536,6 @@ Do not make any additional changes, but ensure that all other code outside of th\n                 });\n                 break;\n             }\n-            case ChatExtensionMessage.runSlashExplainIssues: {\n-                await this._webview?.postMessage({\n-                    type: WebViewMessageType.runSlashCommand,\n-                    data: \"explain_issues\",\n-                });\n-                break;\n-            }\n             case ChatExtensionMessage.runSlashTest: {\n                 await this._webview?.postMessage({\n                     type: WebViewMessageType.runSlashCommand,"
#         }
#       ]
#     }
#   }
# },
# {
#   "current_hash": "9276699e9a822bc7dca1e5f1e5c0a1641f795e7f",
#   "parent_hashes": [
#     "958a913db40bff43249f660ae8a45dd2fb79c89c"
#   ],
#   "author": {
#     "name": "Jacqueline Speiser",
#     "email": "<EMAIL>"
#   },
#   "authored_date": "2024-10-23T23:24:48",
#   "committer": {
#     "name": "GitHub",
#     "email": "<EMAIL>"
#   },
#   "committed_date": "2024-10-23T23:24:48",
#   "message": "Give Alyah and Marty support site access (#14390)\n\nNeeded for adding candidates who want to try Augment to discovery.",
#   "diffs": {
#     "deploy/common/eng.jsonnet": {
#       "insertions": 14,
#       "deletions": 0,
#       "lines": 14,
#       "files": 1,
#       "content": "diff --git a/deploy/common/eng.jsonnet b/deploy/common/eng.jsonnet\nindex a50721f0f..4eede9569 100644\n--- a/deploy/common/eng.jsonnet\n+++ b/deploy/common/eng.jsonnet\n@@ -15,6 +15,13 @@\n     // Full PII access needed for managing customer relations.\n     piiAccess: 'full',\n   },\n+  {\n+    fullname: 'Alyah Sablan',\n+    username: 'alyah',\n+    gcp_access: null,\n+    github: null,\n+    piiAccess: 'masked',\n+  },\n   {\n     fullname: 'Andre Chang',\n     username: 'andre',\n@@ -303,6 +310,13 @@\n     github: 'MarkusRabe',\n     piiAccess: 'masked',\n   },\n+  {\n+    fullname: 'Chris Marty',\n+    username: 'marty',\n+    gcp_access: null,\n+    github: null,\n+    piiAccess: 'masked',\n+  },\n   {\n     fullname: 'Matt Gaunt-Seo',\n     username: 'matt',",
#       "header": "diff --git a/deploy/common/eng.jsonnet b/deploy/common/eng.jsonnet\nindex a50721f0f..4eede9569 100644\n--- a/deploy/common/eng.jsonnet\n+++ b/deploy/common/eng.jsonnet",
#       "hunks": [
#         {
#           "insertions": 7,
#           "deletions": 0,
#           "content": "@@ -15,6 +15,13 @@\n     // Full PII access needed for managing customer relations.\n     piiAccess: 'full',\n   },\n+  {\n+    fullname: 'Alyah Sablan',\n+    username: 'alyah',\n+    gcp_access: null,\n+    github: null,\n+    piiAccess: 'masked',\n+  },\n   {\n     fullname: 'Andre Chang',\n     username: 'andre',"
#         },
#         {
#           "insertions": 7,
#           "deletions": 0,
#           "content": "@@ -303,6 +310,13 @@\n     github: 'MarkusRabe',\n     piiAccess: 'masked',\n   },\n+  {\n+    fullname: 'Chris Marty',\n+    username: 'marty',\n+    gcp_access: null,\n+    github: null,\n+    piiAccess: 'masked',\n+  },\n   {\n     fullname: 'Matt Gaunt-Seo',\n     username: 'matt',"
#         }
#       ]
#     }
#   }
# },

import json
import time

from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from experimental.zhuoran.commit_msgs.utils import detect_file_encoding

REGION = "us-east5"
PROJECT_ID = "augment-387916"
MODEL_NAME = "claude-3-5-sonnet@20240620"
TEMPERAURE = 0
MAX_OUTPUT_TOKENS = 1024 * 8

vertex_ai_client = AnthropicVertexAiClient(
    project_id=PROJECT_ID,
    region=REGION,
    model_name=MODEL_NAME,
    temperature=TEMPERAURE,
    max_output_tokens=MAX_OUTPUT_TOKENS,
)


def run_claude(message, system_message):
    response = vertex_ai_client.client.messages.create(
        model=MODEL_NAME,
        max_tokens=MAX_OUTPUT_TOKENS,
        messages=[{"role": "user", "content": message}],
        system=system_message,
        temperature=TEMPERAURE,
    )
    return response.content[0].text


def extract_nth_commit_messages(file_path, result_path, n, system_message):
    encoding = detect_file_encoding(file_path)
    with open(file_path, "r", encoding=encoding) as f:
        commits = json.load(f)

    with open(result_path, "w") as f:
        f.write("")

    for i in range(0, len(commits)):
        if i % n != 0:
            continue
        print(f"Commit {i + 1}:")
        print(commits[i]["message"])
        text = run_claude(commits[i]["message"], system_message)
        print(f"Claude's evaluation: {text}")
        print("-" * 50)
        with open(result_path, "a") as f:
            print(i, commits[i]["current_hash"], text, file=f)

        time.sleep(60)


if __name__ == "__main__":
    file_path = "/home/<USER>/zhuoran/commit_msgs/commits.json"
    result_path = "/home/<USER>/zhuoran/commit_msgs/qualities.txt"
    n = 100  # Change this value to extract every nth commit
    system_message = "You are a commit message evaluator. Rate the commit message's quality, on a scale from 0 to 10. Reply with a single number and nothing else."
    extract_nth_commit_messages(file_path, result_path, n, system_message)

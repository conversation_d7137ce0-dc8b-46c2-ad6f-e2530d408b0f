import json
from pathlib import Path

from anthropic import Anthropic
from tqdm import tqdm

# Initialize the Anthropic client
anthro = Anthropic()

# Load the JSON file
file_path = Path("/home/<USER>/zhuoran/commit_msgs/commits.json")
with file_path.open("r") as f:
    commits = json.load(f)

# Process each commit
token_counts = {}
# Clear the file
with open("/home/<USER>/zhuoran/commit_msgs/token_counts_full.json", "w") as f:
    f.write("")
for commit in tqdm(commits, desc="Processing commits"):
    try:
        # Concatenate all diffs
        all_diffs = ""
        for file_diff in commit["diffs"].values():
            all_diffs += file_diff["content"] + "\n"

        # Count tokens for the concatenated diff using Anthrop<PERSON>'s counter
        print(commit["current_hash"], len(all_diffs))
        total_tokens = anthro.count_tokens(all_diffs)

        token_counts[commit["current_hash"]] = total_tokens
        with open("/home/<USER>/zhuoran/commit_msgs/token_counts_full.json", "a") as f:
            print(commit["current_hash"], total_tokens, file=f)
            print(commit["current_hash"], total_tokens)
    except Exception as e:
        print(e)
        raise e

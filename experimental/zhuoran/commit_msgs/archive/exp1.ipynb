{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["system_message = \"\"\"You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "```python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "```yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "```\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "```ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "```\n", "</augment_code_snippet>\n", "\"\"\"\n", "\n", "current_message = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff and commit history.\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes.\n", "- Utilizes the commit message history in order to understand, stylistically, how to best create a commit message that fits into the users style.\n", "- Is not unnecessarily verbose. Scales only with the complexity of the diff message given.\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions.\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like.\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes.\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "- Most importantly emphasizes the 'why' of the change, its benefits, or the problem it addresses rather than only the 'what' that changed.\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up! Below is the diff, followed by\n", "a sample of some commit messages from the user's commit history.\n", "\n", "diff --git a/clients/vscode/src/next-edit/background-edits.ts b/clients/vscode/src/next-edit/background-edits.ts\n", "index 1c0ea6608..de82ee25c 100644\n", "--- a/clients/vscode/src/next-edit/background-edits.ts\n", "+++ b/clients/vscode/src/next-edit/background-edits.ts\n", "@@ -15,6 +15,7 @@ import { KeybindingWatcher } from \"../utils/keybindings\";\n", " import { isNotebookUri } from \"../utils/notebook\";\n", " import { Observable } from \"../utils/observable\";\n", " import { delayMs } from \"../utils/promise-utils\";\n", "+import { LineRange } from \"../utils/ranges\";\n", " import { toLineRange } from \"../utils/ranges-vscode\";\n", " import { msecToReadableString } from \"../utils/time\";\n", " import { APIStatus } from \"../utils/types\";\n", "@@ -46,6 +47,11 @@ class AcceptanceInfo {\n", "     constructor(public readonly suggestion: EditSuggestion) {}\n", " }\n", " \n", "+type LastVisibleRanges = {\n", "+    uri: vscode.Uri;\n", "+    visibleRanges: readonly LineRange[];\n", "+};\n", "+\n", " /**\n", "  * Manages state for the next edit background suggestions.\n", "  *\n", "@@ -166,6 +172,11 @@ export class BackgroundNextEdits extends DisposableService {\n", "         BackgroundNextEdits._maxNumberRecentSuggestions\n", "     );\n", " \n", "+    /**\n", "+     * The last visible ranges we saw.\n", "+     */\n", "+    private _lastVisibleRanges: LastVisibleRanges | undefined;\n", "+\n", "     constructor(\n", "         context: vscode.ExtensionContext,\n", "         public workspaceManager: WorkspaceManager,\n", "@@ -308,7 +319,17 @@ export class BackgroundNextEdits extends DisposableService {\n", "             vscode.window.onDidChangeTextEditorVisibleRanges((event) => {\n", "                 if (\n", "                     this.workspaceManager.safeResolvePathName(event.textEditor.document.uri) &&\n", "-                    event.textEditor === vscode.window.activeTextEditor\n", "+                    event.textEditor === vscode.window.activeTextEditor &&\n", "+                    // Don't hide the bottom decorations if the line range didn't change,\n", "+                    // e.g., if only the characters changed.\n", "+                    (!this._lastVisibleRanges ||\n", "+                        this._lastVisibleRanges.uri.fsPath !==\n", "+                            event.textEditor.document.uri.fsPath ||\n", "+                        this._lastVisibleRanges.visibleRanges.length !==\n", "+                            event.visibleRanges.length ||\n", "+                        this._lastVisibleRanges.visibleRanges.some(\n", "+                            (range, index) => !range.equals(toLineRange(event.visibleRanges[index]))\n", "+                        ))\n", "                 ) {\n", "                     this._drawDecorations();\n", "                     // Make sure we track bottom decoration state.\n", "@@ -316,6 +337,10 @@ export class BackgroundNextEdits extends DisposableService {\n", "                         this._decorationManager.shouldDrawBottomDecorations.value = false;\n", "                         debouncedSetBottomDecorations();\n", "                     }\n", "+                    this._lastVisibleRanges = {\n", "+                        uri: event.textEditor.document.uri,\n", "+                        visibleRanges: event.visibleRanges.map((r) => toLineRange(r)),\n", "+                    };\n", "                 }\n", "             })\n", "         );\n", "\n", "\n", "move collect_repo_commits to research data (#14254)\n", "\n", "Add <PERSON><PERSON><PERSON> to `eng.jsonnet` (#14358)\n", "\n", "Adding myself to `eng.jsonnet` for permission to access services.\n", "\n", "---------\n", "\n", "Co-authored-by: <PERSON><PERSON><PERSON> <<EMAIL>>\n", "Don't use decorator based tracing in next_edit_host (#14321)\n", "\n", "It doesn't play well with generator functions which we now use heavily.\n", "[intellij] reload styles when display changes (#14357)\n", "\n", "Also made the initial zoom level setting more concise.\n", "Simplify parsing of Feature Flags in augment_client (#14110)\n", "\n", "This wasn't possible before the feature flags in client were matched\n", "with the ones in the server\n", "Make it easier to register frontend feature flags (#14340)\n", "\n", "The goal is to define them in two places in the backend:\n", "\n", "- the public_api.proto\n", "- the get_feature_flags function\n", "\n", "This is meant to be RFC. Is this a viable approach?\n", "Make the E2E feature flag test less brittle (#14356)\n", "\n", "if a person overwrites (and they can and will) feature flags in a\n", "namespace, the test will fail. instead just check the presense of\n", "the keys.\n", "\n", "Honestly: I would also be open to remove the complete test, but\n", "maybe this test makes sense.\n", "Skip requests with 0 reconstructed file changes. (#13951)\n", "\n", "We already skip requests that don't contain any editing events in the front end. This PR adds the logic to the back end to also stop the requests early if the list of file changes is empty after granular diff reconstruction. It also records an event to a new `_next_edit_diff_events_problems` counter when such cases happen.\n", "Allow prod tenants to be added as separate files (#14262)\n", "\n", "Testing done: tenant_config_kubecfg diff revealed aitutor-turing\n", "and govtechsg still there\n", "Use new Claude Sonnet 3.5 model for chat (#14345)\n", "\n", "Use new Claude-Sonnet 3.5 model in production. We take two existing\n", "models -- v3, v4 -- update the sonnet model to\n", "`claude-3-5-sonnet-v2@20241022` and get v5 and v6 respectively:\n", "\n", "v3 (default for customers) => v5\n", "v4 (default for dogfood) => v6\n", "github processor: Update registerRepoRequests metric to include a reason (#14293)\n", "\n", "Make sure we are actually incrementing this metric on every `registerRepo` request and add a reason field so we can see why this may be happening often\n", "[emb_idx] Remove RobustSignatureChunker option (#14098)\n", "\n", "Subprocess chunker has been going strong for almost a week.\n", "Improve next edit e2e test. (#14299)\n", "\n", "This PR changes the next edit e2e test to be a more meaningful example that tests the model's ability to finish a renaming change. It also updates the test config to use the raven v4 model, which is our current new default model.\n", "\n", "e2e test run: https://test-viewer.us-central1.dev.augmentcode.com/run/0192b5cf-91b5-991c-9d34-567e3da24a44\n", "Sort and organize /command diagnostics by line ranges (#14271)\n", "\n", "Show line numbers and sort and organize diagnostic messages by the line ranges in `/fix` an `/explain_issues` commands.\n", "\n", "Now:\n", "```\n", "Fix the selected code. The IDE reports the following issues:\n", "\n", "• L6: \"(\" was not closed\n", "• L11:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. \"get_stable_re\" is unknown import symbol\n", "    3. \"lease_ref\" is not defined\n", "    4. \"get_stable_re\" is not accessed\n", "    5. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Expected ',', found name\n", "• L11-12: Expression value is unused\n", "• L12:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. \"versions_fro\" is not defined\n", "    3. \"m_vsce_data\" is not defined\n", "    4. Expression value is unused\n", "• L13:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. Expected expression\n", "```\n", "\n", "Before:\n", "```\n", "Fix the selected code. The IDE reports the following issues:\n", "\n", "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Expected ',', found name\n", "2. \"(\" was not closed\n", "3. Statements must be separated by newlines or semicolons\n", "4. Statements must be separated by newlines or semicolons\n", "5. Statements must be separated by newlines or semicolons\n", "6. Expected expression\n", "7. \"get_stable_re\" is unknown import symbol\n", "8. \"lease_ref\" is not defined\n", "9. \"versions_fr\" is not defined\n", "10. Expression value is unused\n", "11. \"om_vsce_data\" is not defined\n", "12. Expression value is unused\n", "13. \"get_stable_re\" is not accessed\n", "```\n", "infra/grafana: Provision Contact Points (slack #infra-alerts)\n", "\n", "We just have the one contact point. The alerts sidecar handles all of\n", "alerting rules, contact points, and notification policies. Alerting\n", "rules can be a ConfigMap but Contact Points needs to be a secret because\n", "it contains tokens/keys.\n", "infra/grafana: jb update\n", "\n", "Update the vendored grafonnet\n", "infra/grafana: Add `make pull` (no updates)\n", "\n", "Add a target for the command used to get the chart `.tgz`. We currently\n", "already have the latest.\n", "infra/grafana: Update dashboard sidecar data\n", "\n", "This could probably be automated but doesn't seem worth it.\n", "infra/grafana: Kill-and-fill export for deletions\n", "infra/grafana: Add export.sh and re-export dashboards\n", "\n", "Last time I did this, I did some manual work to update datasources,\n", "access a subpath of the response, etc. Now everything is in a script.\n", "\n", "As noted in the script:\n", "\n", "TODO(mattm): This script is currently designed to export from the old Grafana to git for use in the new Grafana. Update\n", "it to use as a way to export manually updated dashboards from the new Grafana back into git.\n", "infra/grafana: Rename previously exported dashboards\n", "\n", "Remove some of the manual/human naming cleanup I'd done last time. We're\n", "going to re-export from a script, this will help avoid some diff noise.\n", "github processor: re-register repo when too many files changed (#14225)\n", "\n", "if more than 300 files are changed in a push or a push contains more than 250 commits, `CompareCommits` only gives us the first chunk.\n", "\n", "The api call only paginates based on commits, so instead of trying to paginate, we will re-register the repo.\n", "\n", "This should also move staging back along to normal, accepting new commits (AU-5011). We assume this will happen pretty rarely.\n", "Apply threshold filter to suggestion changed events (#14352)\n", "\n", "We already apply the threshold (when applicable) to the methods used to\n", "get suggestions from the SuggestionManager. Let's also apply it to the\n", "suggestion changed events.\n", "\n", "Specifically, this ensures that the global panel, which uses the latter\n", "not the former, doesn't see filtered things. (I thought about instead\n", "changing the panel to call one of the methods that already works, but\n", "this is more consistent.)\n", "\n", "I also had to muck around with readonlys a bit to get it to compile.\n", "Good old const.\n", "\n", "---------\n", "\n", "Co-authored-by: <PERSON> <<EMAIL>>\n", "Fix next edits in untitled files (#14332)\n", "\n", "I accidentally broke this when I disabled suggestions for files that\n", "were deleted.\n", "Add feature flag for chat entry decoration (#14320)\n", "\n", "Add a new feature flag for the chat entry decoration so that it can be\n", "enabled on prod.\n", "\n", "(I'll add two separate flags later for the hover panel and context menu)\n", "github-processor: do not log patch errors (#14324)\n", "\n", "These probably have confidential info (filenames)\n", "Relax hunk size filtering logic. (#14348)\n", "\n", "Following [Slack discussion](https://augment-wic8570.slack.com/archives/C07C2PUJF4N/p1729637052086779), this relaxes the hunk size filtering logic from \"filtering any hunk whose before code contains more than 5 lines\" to \"filtering any hunk that removes more than 5 lines\".\n", "Pod Disruption budget prevents maintenance (#14350)\n", "\n", "minAvailable was 1 but replicas was 1 too, which prevents GKE\n", "from doing maintenance.\n", "\n", "Testing done: kubecfg tests that run as part of precommit\n", "Disable test_get_feature_flags to get post-commit green (#14351)\n", "\n", "Propagate auth fields into tenants k8s objects (#14334)\n", "\n", "Missed this the first time around.\n", "\n", "Testing done: kubectl diff against production shows fields are now added.\n", "[auth] Remove two feature flags from flags.yaml (#14181)\n", "\n", "Remove ground_truth_span misc. (#13960)\n", "\n", "# Changes\n", "See #13839 for the overall strategy for removing ground_truth_span.\n", "\n", "Three miscellaneous `ground_truth_span` removals:\n", "* Remove from `pinocchio_instruct.py`. I cannot find any code reference anywhere in code edits that uses `ground_truth_span`.\n", "* Update model_server to use `cursor_position`. I'm not sure why the comment claims that `prefix_begin` is more accurate, this is almost certainly no longer true, because `prefix_begin` now refers to the blob (possibly out of date), and not the current file.\n", "* Remove from `attach_sys_to_nodes.py` and slap a NOTE at the top of file, since this code is already broken (doesn't do `add_docs` with ground truth removed. Discussed with @D-X-Y and he preferred to keep it in `eval` for now rather than remove or move it to `experimental`.\n", "\n", "# Tests\n", "* CI/CD\n", "\"\"\"\n", "retrieval_message = \"\"\"Below are some relevant files from my project.\n", "\n", "Here is an excerpt from the file `services/api_proxy/server/src/handlers_next_edit.rs`:\n", "\n", "```\n", "use std::convert::TryFrom;\n", "\n", "use crate::api_auth::User;\n", "use crate::augment::model_instance_config::ModelType;\n", "use crate::base::diff_utils::{GranularEditEvent, SingleEdit};\n", "use crate::content_manager_util::ContentManagerClient;\n", "use crate::generation_clients::Client;\n", "use crate::handler_utils::{convert_blobs_and_names, get_model, request_context_from_req};\n", "use crate::handlers::{\n", "    convert_replacement_text, streaming_http_response_from_receiver, <PERSON>point<PERSON><PERSON><PERSON>, <PERSON><PERSON>,\n", "};\n", "use crate::model_registry;\n", "use crate::next_edit::{self, NextEditRequest, NextEditResponse};\n", "\n", "use crate::public_api_proto;\n", "use tracing_actix_web::RootSpan;\n", "\n", "use actix_web::{web, HttpRequest, HttpResponse};\n", "use model_registry::ModelRegistry;\n", "use request_context::{RequestContext, TenantInfo};\n", "use request_insight_publisher::request_insight;\n", "use serde::{Deserialize, Deserializer};\n", "use tokio::sync::mpsc::Receiver;\n", "\n", "pub const MODEL_FLAG: feature_flags::StringFlag =\n", "    feature_flags::StringFlag::new(\"next_edit_model\", \"\");\n", "\n", "...\n", "        }\n", "    }\n", "}\n", "\n", "impl<MR: ModelReg<PERSON>ry, CNC: ContentManagerClient>\n", "    EndpointHandler<public_api_proto::NextEditRequest> for <PERSON><PERSON><MR, CNC>\n", "{\n", "    async fn handle(\n", "        &self,\n", "        req: &HttpRequest,\n", "        front_next_edit_request: public_api_proto::NextEditRequest,\n", "        root_span: RootSpan,\n", "    ) -> Result<HttpResponse, tonic::Status> {\n", "        tracing::info!(\n", "            \"next_edit request model={:?}\",\n", "            front_next_edit_request.model\n", "        );\n", "\n", "        let (user, tenant_info, request_context) = request_context_from_req(req)?;\n", "        root_span.record(\"tenant_name\", &tenant_info.tenant_name);\n", "\n", "        let feature_flags = self.get_feature_flags(&user, &tenant_info)?;\n", "        if CB_NEXT_EDIT.get_from(&feature_flags) {\n", "            return Err(tonic::Status::resource_exhausted(\"Circuit breaker is open\"));\n", "        }\n", "\n", "        let user_agent: &str = req\n", "            .headers()\n", "            .get(\"user-agent\")\n", "            .map(|h| h.to_str().unwrap_or(\"not-utf8\"))\n", "...\n", "                _ => return Err(tonic::Status::internal(\"Model is not a next edit model\")),\n", "            };\n", "            let next_edit_request: NextEditRequest =\n", "                (front_next_edit_request, &mi.name).try_into()?;\n", "\n", "            let result = com.next_edit(&request_context, next_edit_request).await?;\n", "            let result = public_api_proto::NextEditResponse::from(result);\n", "            Ok(HttpResponse::Ok().json(result))\n", "        })\n", "        .await\n", "    }\n", "}\n", "\n", "pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {\n", "    MODEL_FLAG\n", "        .register(registry)\n", "        .expect(\"Registering next edit MODEL_FLAG\");\n", "}\n", "\n", "async fn next_edit_stream<MR: ModelRegistry, CNC: ContentManagerClient>(\n", "    data: web::Data<Handler<MR, CNC>>,\n", "    req: &HttpRequest,\n", "    user: &User,\n", "    tenant_info: &TenantInfo,\n", "    request_context: &RequestContext,\n", "    front_next_edit_request: public_api_proto::NextEditRequest,\n", "    root_span: RootSpan,\n", ") -> Result<Receiver<tonic::Result<next_edit::NextEditResponse>>, tonic::Status> {\n", "    tracing::info!(\n", "        \"next_edit request model={:?}\",\n", "        front_next_edit_request.model,\n", "    );\n", "    root_span.record(\"tenant_name\", &tenant_info.tenant_name.clone());\n", "\n", "    let feature_flags = data.get_feature_flags(user, tenant_info)?;\n", "    if CB_NEXT_EDIT.get_from(&feature_flags) {\n", "        return Err(tonic::Status::resource_exhausted(\"Circuit breaker is open\"));\n", "    }\n", "\n", "    let user_agent = req\n", "        .headers()\n", "        .get(\"user-agent\")\n", "        .map(|h| h.to_str().unwrap_or(\"not-utf8\"))\n", "        .unwrap_or(\"\");\n", "\n", "    data.request_insight_publisher\n", "        .record_request_metadata(\n", "            request_context,\n", "            tenant_info,\n", "            request_insight::RequestMetadata {\n", "                request_type: request_insight::RequestType::NextEdit.into(),\n", "                session_id: request_context.request_session_id().to_string(),\n", "                user_id: user.user_id.to_string(),\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/extension.ts`:\n", "\n", "```\n", "...\n", "    }\n", "\n", "    public nextEditCanResetCooldown() {\n", "        return (\n", "            isNextEditBackgroundEnabled(\n", "                this._augmentConfigListener.config,\n", "                this._extensionVersion\n", "            ) &&\n", "            this._backgroundNextEdit !== undefined &&\n", "            this._backgroundNextEdit.isInCooldown()\n", "        );\n", "    }\n", "\n", "    public nextEditBackgroundSuggestionsEnabled() {\n", "        return isNextEditBackgroundEnabled(\n", "            this._augmentConfigListener.config,\n", "            this.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? \"\"\n", "        );\n", "    }\n", "\n", "    public setNextEditBackgroundSuggestionsEnabledOrDisabled(enabled: boolean) {\n", "        if (!this.nextEditBackgroundSuggestionsEnabled()) {\n", "            return; // no-op\n", "        }\n", "\n", "        this._backgroundNextEdit?.setNextEditBackgroundSuggestionsEnabledOrDisabled(enabled);\n", "    }\n", "\n", "    public async updateStatusTrace() {\n", "        this._statusTrace?.dispose();\n", "...\n", "\n", "    // Register this extension as a text document provider for our content scheme.\n", "    context.subscriptions.push(\n", "        vscode.workspace.registerTextDocumentContentProvider(\n", "            AugmentExtension.contentScheme,\n", "            extension\n", "        )\n", "    );\n", "\n", "    const syncContextFromConfig = () => {\n", "        const c: AugmentConfig = configListener.config;\n", "        setContextBatch({\n", "            /* eslint-disable @typescript-eslint/naming-convention */\n", "            \"vscode-augment.enableDebugFeatures\": c.enableDebugFeatures,\n", "            \"vscode-augment.enableReviewerWorkflows\": c.enableReviewerWorkflows,\n", "            \"vscode-augment.enableNextEdit\": isNextEditEnabled(\n", "                configListener.config,\n", "                extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? \"\"\n", "            ),\n", "            \"vscode-augment.enableNextEditBackgroundSuggestions\": isNextEditBackgroundEnabled(\n", "                configListener.config,\n", "                extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? \"\"\n", "            ),\n", "            /* eslint-enable @typescript-eslint/naming-convention */\n", "        });\n", "    };\n", "    syncContextFromConfig();\n", "    context.subscriptions.push(configListener.onDidChange(syncContextFromConfig));\n", "\n", "    // Update MONITOR_FLAGS if you need to monitor flags other than the listed flags\n", "    const MONITORED_FLAGS: Array<keyof FeatureFlags> = [\n", "        \"enableWorkspaceManagerUi\",\n", "        \"enableSmartPaste\",\n", "        \"enableSmartPasteMinVersion\",\n", "        \"enableInstructions\",\n", "    ];\n", "    const syncContextFromFlags = () => {\n", "        if (!extension) {\n", "            return;\n", "        }\n", "        const f: FeatureFlags = extension.featureFlagManager.currentFlags;\n", "        setContextBatch({\n", "            /* eslint-disable @typescript-eslint/naming-convention */\n", "            \"vscode-augment.workspace-manager-ui.enabled\": f.enableWorkspaceManagerUi,\n", "            \"vscode-augment.internal-dv.enabled\":\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/next-edit/background-edits.ts`:\n", "\n", "```\n", "...\n", "import { toLineRange } from \"../utils/ranges-vscode\";\n", "import { msecToReadableString } from \"../utils/time\";\n", "import { APIStatus } from \"../utils/types\";\n", "import { WorkspaceManager } from \"../workspace/workspace-manager\";\n", "import { BackgroundDecorationManager } from \"./background-decoration-manager\";\n", "import { BackgroundNextEditsCodeActionProvider } from \"./code-action-provider\";\n", "import { BackgroundNextEditsHoverProvider } from \"./hover-provider\";\n", "import {\n", "    ChangeType,\n", "    IEditSuggestion,\n", "    NextEditMode,\n", "    NextEditScope,\n", "    NextEditSessionEventName,\n", "    NextEditSessionEventSource,\n", "    SuggestionState,\n", "} from \"./next-edit-types\";\n", "import { INextEditRequestManager } from \"./request-manager\";\n", "import { RequestState } from \"./request-state\";\n", "import { EditSuggestion, SuggestionChangedEvent, SuggestionManager } from \"./suggestion-manager\";\n", "\n", "export enum NextMode {\n", "    next,\n", "    closest,\n", "}\n", "\n", "class AcceptanceInfo {\n", "    public accepted: boolean = false;\n", "    public readonly becameNextAt: Date = new Date();\n", "...\n", "     * If an active suggestion is set, it should be the same as the next suggestion.\n", "     */\n", "    public activeSuggestion: Observable<EditSuggestion | undefined>;\n", "\n", "    /**\n", "     * Tracks whether or not recent next suggestions were accepted.\n", "     */\n", "    private _recentAcceptanceInfos: RingBuffer<AcceptanceInfo> = new RingBuffer(\n", "        BackgroundNextEdits._maxNumberRecentSuggestions\n", "    );\n", "\n", "    constructor(\n", "        context: vscode.ExtensionContext,\n", "        public workspaceManager: WorkspaceManager,\n", "        private _nextEditResolutionReporter: NextEditResolutionReporter,\n", "        private _nextEditSessionEventReporter: NextEditSessionEventReporter,\n", "        private _keybindingWatcher: KeybindingWatcher,\n", "        private _configListener: AugmentConfigListener,\n", "        private _suggestionManager: SuggestionManager,\n", "        private _requestManager: INextEditRequestManager,\n", "        private _stateController: StateController\n", "    ) {\n", "        super();\n", "...\n", "                        new Date().getTime() - last.becameNextAt.getTime() <\n", "                            BackgroundNextEdits._minNextDurationForAcceptanceCountMs\n", "                    ) {\n", "                        this._recentAcceptanceInfos.shiftRight(1);\n", "                    }\n", "                    if (suggestion && !suggestion.equals(last?.suggestion)) {\n", "                        // This is a new next suggestion, so track it.\n", "                        this._recentAcceptanceInfos.addItem(new AcceptanceInfo(suggestion));\n", "                    }\n", "                    // Tell the suggestion manager whether or not to use the threshold.\n", "                    this._suggestionManager.setUseThreshold(\n", "                        this._getRecentAcceptanceRate() <\n", "                            BackgroundNextEdits._editingScoreThresholdMaxAcceptanceRate\n", "                    );\n", "\n", "                    this._drawDecorations();\n", "                })\n", "            )\n", "        );\n", "        this.addDisposable(\n", "            new vscode.Disposable(\n", "...\n", "            return;\n", "        }\n", "        this._hoverProvider.hideHover();\n", "        return this.open(nextSuggestion);\n", "    }\n", "\n", "    public hasSuggestions() {\n", "        return this._suggestionManager.getActiveSuggestions().some(isFreshChange);\n", "    }\n", "\n", "    public cooldown() {\n", "        const editor = vscode.window.activeTextEditor;\n", "        if (!editor) {\n", "            return;\n", "        }\n", "        const current = this.activeSuggestion.value;\n", "        this._requestManager.cancelAll();\n", "        this._decorationManager.decorate([], {});\n", "        this._cooldownUntil = Date.now() + BackgroundNextEdits._cooldownTimeMs;\n", "        void vscode.commands.executeCommand(\n", "            \"setContext\",\n", "            \"vscode-augment.nextEdit.inCooldown\",\n", "            true\n", "        );\n", "        const keybinding = this._keybindingWatcher.getKeybindingForCommand(\n", "            NextEditResetCooldownCommand.commandID\n", "        );\n", "        // Let the user know that they're in cooldown.\n", "        void vscode.window.showInformationMessage(\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/next_edit_host/server/post_processing.py`:\n", "\n", "```\n", "\\\"\\\"\\\"Post-processing library for the next edit host.\\\"\\\"\\\"\n", "\n", "import re\n", "from dataclasses import dataclass\n", "from typing import Sequence\n", "\n", "import opentelemetry.trace\n", "import structlog\n", "import xgboost as xgb\n", "from dataclasses_json import dataclass_json\n", "\n", "from base.caching.lru_cache import lru_cache\n", "from base.diff_utils import changes, str_diff\n", "from base.diff_utils.diff_utils import File\n", "from base.diff_utils.str_diff import DiffSpan, NoopSpan\n", "from base.next_edit_filter.extract_next_edit_filter_features import FeatureExtractorV1\n", "from base.ranges.range_types import CharRange\n", "from base.tokenizers import NextEditGenSpecialTokens, Tokenizer\n", "\n", "log = structlog.get_logger()\n", "tracer = opentelemetry.trace.get_tracer(__name__)\n", "\n", "_TODO_PATTERN = re.compile(r\"\\bTODO\\b\", re.IGNORECASE)\n", "\n", "\n", "def is_exact_undo_prev_spans(\n", "    prev_spans_ranges: Sequence[tuple[<PERSON>ffS<PERSON>, Char<PERSON>ang<PERSON>]],\n", "    suggestion_spans_ranges: Sequence[tuple[<PERSON>ff<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON>]],\n", ") -> bool:\n", "    \\\"\\\"\\\"Checks if the suggested change exactly undoes some previous change.\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/next-edit/suggestion-manager.ts`:\n", "\n", "```\n", "/**\n", " * `SuggestionManager` manages a collection of next edit suggestions to keep track\n", " * of acceptances and rejections, and to keep them consistent with text edit events.\n", " *\n", " * This manager is used by the different views (background, diff-view, global, etc.).\n", " */\n", "import * as vscode from \"vscode\";\n", "\n", "import { EphemeralObservable } from \"../utils/ephemeral-flag\";\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange } from \"../utils/ranges\";\n", "import { QualifiedPathName } from \"../workspace/qualified-path-name\";\n", "import {\n", "    AnnotatedOneLineDiffSpan,\n", "    ChangeType,\n", "    <PERSON>ff<PERSON><PERSON>,\n", "    IEditSuggestion,\n", "    NextEditMode,\n", "    NextEditResult,\n", "    NextEditScope,\n", "    SuggestionState,\n", "    UUID,\n", "} from \"./next-edit-types\";\n", "\n", "/** Describes a single suggestion.  */\n", "export class EditSuggestion implements IEditSuggestion {\n", "    /** The type of change this suggestion represents. */\n", "    public readonly changeType: ChangeType;\n", "    /** The range that will be highlighted. */\n", "    // TODO: Use this for decoration.\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/next-edit/code-action-provider.ts`:\n", "\n", "```\n", "import * as vscode from \"vscode\";\n", "\n", "import { NextEditAcceptAndNextCommand } from \"../commands/next-edit\";\n", "import { EditSuggestion } from \"./suggestion-manager\";\n", "\n", "export class BackgroundNextEditsCodeActionProvider implements vscode.CodeActionProvider {\n", "    public suggestions: EditSuggestion[] = [];\n", "\n", "    public provideCodeActions(\n", "        _document: vscode.TextDocument,\n", "        range: vscode.Range | vscode.Selection,\n", "        _context: vscode.CodeActionContext,\n", "        _token: vscode.CancellationToken\n", "    ): vscode.CodeAction[] | undefined {\n", "        const suggestion = this.suggestions.find(\n", "            (s) =>\n", "                s.lineRange.intersection({ start: range.start.line, stop: range.end.line }) !==\n", "                undefined\n", "        );\n", "        if (!suggestion) {\n", "            return;\n", "        }\n", "        const description = suggestion.result.changeDescription || \"Augment: View next edit\";\n", "\n", "        const action = new vscode.CodeAction(description, vscode.CodeActionKind.QuickFix);\n", "        action.command = {\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/next-edit.ts`:\n", "\n", "```\n", "...\n", "    yolo<PERSON>n<PERSON><PERSON>,\n", "}\n", "\n", "function _areSameLocations(a: PathAndRange, b: PathAndRange): boolean {\n", "    return a.path === b.path && a.range.start === b.range.start && a.range.stop === b.range.stop;\n", "}\n", "\n", "/**\n", " *\n", " * @param config\n", " * @param minVersion - from launch darkly feature flags. (vscode-next-edit-min-version)\n", " * @returns\n", " */\n", "export function isNextEditEnabled(config: AugmentConfig, minVersion: string): boolean {\n", "    return (\n", "        config.nextEdit.enabled ??\n", "        environment.isExtensionVersionGte(minVersion, { acceptDebugBuid: true }) ??\n", "        config.enableDebugFeatures\n", "    );\n", "}\n", "\n", "export function isNextEditBackgroundEnabled(config: AugmentConfig, minVersion: string): boolean {\n", "    return (\n", "        config.enableBackgroundSuggestions &&\n", "        config.nextEdit.backgroundEnabled &&\n", "        isNextEditEnabled(config, minVersion)\n", "    );\n", "}\n", "\n", "export function isNextEditVCSEnabled(config: AugmentConfig, minVersion: string): boolean {\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/next-edit/hover-provider.ts`:\n", "\n", "```\n", "import * as vscode from \"vscode\";\n", "\n", "import { AugmentConfigListener } from \"../augment-config-listener\";\n", "import { NextEditAcceptAndNextCommand, NextEditRejectCommand } from \"../commands/next-edit\";\n", "import { getLogger } from \"../logging\";\n", "import { NextEditSessionEventReporter } from \"../metrics/next-edit-session-event-reporter\";\n", "import { DisposableService } from \"../utils/disposable-service\";\n", "import { KeybindingWatcher } from \"../utils/keybindings\";\n", "import { getKeybindingMarkdownIcons } from \"../utils/keyboard-icons\";\n", "import { Observable } from \"../utils/observable\";\n", "import { DiffColors, renderFullDiff } from \"./diff-renderer\";\n", "import {\n", "    ChangeType,\n", "    NextEditSessionEventName,\n", "    NextEditSessionEventSource,\n", "    SuggestionState,\n", "} from \"./next-edit-types\";\n", "import { EditSuggestion, SuggestionManager } from \"./suggestion-manager\";\n", "\n", "const estimatedWidthOfHeaderInFixedWidthChars = 49;\n", "\n", "export class BackgroundNextEditsHoverProvider\n", "    extends DisposableService\n", "    implements vscode.HoverProvider\n", "{\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/yangguang/vscode-ui-prototype/README.md`:\n", "\n", "```\n", "# AugmentCode UI prototype\n", "\n", "This is a VS code extension for experimenting UI features for Augment. This is a *prototype*, for exploring and demoing UI features. It may not be reliable, and most edge cases may fail.\n", "\n", "## Demo features\n", "\n", "### Pick a model/API\n", "\n", "Go to extension settings (from the marketplace page, or vscode settings, then choose \"Extensions > Augment UI prototype\" in the settings tree on the left), set the model/API per the instruction.\n", "\n", "**Only tested all features on `openai`, some newer features may not work on other API choieces.**\n", "\n", "### Main feature: Tasks & Scratchpad\n", "\n", "To trigger the feature:\n", "\n", "- Open the sidebar only\n", "  - Click on the Augment head icon on vscode left sidebar.\n", "  - Keyboard shortcut: `shift+command+W`\n", "  - Then you can type `/sp` to start a scratchpad, or chat in the sidebar, click on the *edit* icon on any code block.\n", "- Open the sidebar, and also start a scratchpad directly.\n", "  - Select a piece of code, then right click \"Augment - start a scratchpad\"\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/yangguang/vscode-ui-prototype/src/lib/store.ts`:\n", "\n", "```\n", "...\n", "export const additionLineDecorationType = vscode.window.createTextEditorDecorationType({\n", "    backgroundColor: 'rgba(0, 255, 0, 0.1)',\n", "    isWholeLine: true,\n", "});\n", "\n", "export const editStateStore = {\n", "    vscodeGlobalState: null,  // A vscode level storage, used to save user's tasks history.\n", "\n", "    // UI states: mainly used when we get info about it in one place, then need to use that in another place.\n", "    // e.g. diff lines when rendering code, but need to know the lines when clicking codelense.\n", "\n", "    isGeneratingCode: false,\n", "    codeSelected: '',\n", "    query: '',\n", "    modifiedCode: '',\n", "    addedLines: [],\n", "    removedLines: [],\n", "    allDiffBgDecorations: [removalDecorationType, removalLineDecorationType, additionDecorationType, additionLineDecorationType],\n", "    gutterDecorations: [],\n", "};\n", "\n", "const STORE_TASKS_KEY = 'TASKS';\n", "export function saveTasksToVscodeStore(tasks: Task[]) {\n", "    if (!editStateStore.vscodeGlobalState) {\n", "        console.warn('Trying to save to vscode global state, but reference is not saved.');\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/next-edit/background-decoration-manager.ts`:\n", "\n", "```\n", "import assert from \"assert\";\n", "import * as vscode from \"vscode\";\n", "\n", "import { AugmentConfigListener } from \"../augment-config-listener\";\n", "import { NextEditAcceptAndNextCommand } from \"../commands/next-edit\";\n", "import { getLogger } from \"../logging\";\n", "import { NextEditSessionEventReporter } from \"../metrics/next-edit-session-event-reporter\";\n", "import { buildGhostDecoration } from \"../utils/decoration-types\";\n", "import { KeybindingWatcher } from \"../utils/keybindings\";\n", "import { getKeybindingDecor } from \"../utils/keyboard/keyboard-decorations\";\n", "import { endOfLineRange, startOfLineRange } from \"../utils/line-utils\";\n", "import { Observable } from \"../utils/observable\";\n", "import { LineRange } from \"../utils/ranges\";\n", "import { toVSCodeRange } from \"../utils/ranges-vscode\";\n", "import { truncate } from \"../utils/strings\";\n", "import { WorkspaceManager } from \"../workspace/workspace-manager\";\n", "import { DecorationCollection, DecorationManager } from \"./decoration-manager\";\n", "import {\n", "    ChangeType,\n", "    NextEditSessionEventName,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/grpc_debug/Readme.md`:\n", "\n", "```\n", "# gRPC Debug\n", "\n", "This service is used to debug gRPC services.\n", "It provides a gRPC service to get a list of endpoints, services and methods.\n", "It also allows to invoke methods based on JSON input and JSON output.\n", "\n", "It is exposed via the support UI as web UI and via a HTTP/JSON interface.\n", "\n", "This is a tool for debugging and development. It is under no circumstances\n", "a tool to build production systems on top.\n", "\n", "## HTTP/JSON interface\n", "\n", "The following endpoints are available:\n", "\n", "* /grpc-debug/endpoints: Get the list of endpoints.\n", "* /grpc-debug/services: Get the list of services.\n", "* /grpc-debug/methods: Get the list of methods for a service.\n", "* /grpc-debug/invoke: Invoke a method.\n", "\n", "Add `Authorization: Bearer <token>` to the request to authenticate.\n", "The token can be a service token or an api token.\n", "The service token can be generated via `bazel run //services/token_exchange/util -- --tenant-name augment --cloud GCP_US_CENTRAL1_DEV --shard-namespace [NAMESPACE]`\n", "\n", "see `demo.ipynb` for an example of how to use the service.\n", "\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/main-panel/apps/chat-webview-app.ts`:\n", "\n", "```\n", "/* eslint-disable @typescript-eslint/no-unsafe-call */\n", "import debounce from \"lodash/debounce\";\n", "import throttle from \"lodash/throttle\";\n", "import * as vscode from \"vscode\";\n", "\n", "import {\n", "    APIServer,\n", "    <PERSON><PERSON><PERSON>,\n", "    ChatInstructionStreamResult,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    Model,\n", "} from \"../../augment-api\";\n", "import { AugmentConfigListener } from \"../../augment-config-listener\";\n", "import ChatModel, { PreferenceState } from \"../../chat/chat-model\";\n", "import { AugmentChatEntry, Exchange } from \"../../chat/chat-types\";\n", "import { editorToFileRangesSelected } from \"../../chat/data-transform-utils\";\n", "import { ISmartPasteCacheContext, SmartPasteCache } from \"../../chat/smart-paste-cache\";\n", "import { FocusAugmentPanel } from \"../../commands/focus-augment-panel\";\n", "import { APIError, getErrmsg } from \"../../exceptions\";\n", "import { FeatureFlagManager } from \"../../feature-flags\";\n", "import { getLogger } from \"../../logging\";\n", "import { type ClientMetricsReporter } from \"../../metrics/client-metrics-reporter\";\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/workspace/workspace-manager.ts`:\n", "\n", "```\n", "import { cloneDeep } from \"lodash\";\n", "import * as vscode from \"vscode\";\n", "\n", "import { APIServer, Blobs, Language } from \"../augment-api\";\n", "import { AugmentConfigListener } from \"../augment-config-listener\";\n", "import { CompletionServer } from \"../completion-server\";\n", "import { getErrmsg } from \"../exceptions\";\n", "import { FeatureFlagManager } from \"../feature-flags\";\n", "import { newFileReader } from \"../file-reader\";\n", "import { type AugmentLogger, getLogger } from \"../logging\";\n", "import { ActionsModel } from \"../main-panel/action-cards/actions-model\";\n", "import { isNextEditEnabled, isNextEditVCSEnabled } from \"../next-edit\";\n", "import { FileEditEvent } from \"../next-edit/file-edit-events\";\n", "import { FileEditManager } from \"../next-edit/file-edit-events/file-edit-manager\";\n", "import { sha256 } from \"../sha\";\n", "import { StatusTrace } from \"../status-trace\";\n", "import { DisposableCollection, DisposableService } from \"../utils/disposable-service\";\n", "import { statFileSync } from \"../utils/fs-utils\";\n", "import {\n", "    AcceptPathImplicit,\n", "    IgnoreSource,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/command-manager-init.ts`:\n", "\n", "```\n", "...\n", "        new NextEditToggleBackgroundSuggestions(extension, configListener, syncingEnabledTracker),\n", "        new NextEditToggleShowAllHighlightsCommand(\n", "            extension,\n", "            config<PERSON><PERSON><PERSON>,\n", "            syncingEnabledTracker\n", "        ),\n", "        // The commands below aren't shown in action panel.\n", "        new NextEditAcceptCommand(extension, configListener, syncingEnabledTracker),\n", "        new NextEditRejectCommand(extension, configListener, syncingEnabledTracker),\n", "        new NextEditNextDeprecatedCommand(extension, configListener, syncingEnabledTracker),\n", "        new NextEditGenerationCommand(extension, configListener, syncingEnabledTracker),\n", "        new NextEditGenerationYoloInPlaceCommand(extension, configListener, syncingEnabledTracker),\n", "    ]);\n", "\n", "    // For general commands used for debugging\n", "    commandManager.registerGroup(\"Debug\", [\n", "        new ExtensionStatusCommand(extension, configListener),\n", "        new InternalWorkspaceContextCommand(extension, configListener),\n", "    ]);\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/next-edit/suggestion-manager-impl.ts`:\n", "\n", "```\n", "/**\n", " * `SuggestionManager` manages a collection of next edit suggestions to keep track\n", " * of acceptances and rejections, and to keep them consistent with text edit events.\n", " *\n", " * This manager is used by the different views (background, diff-view, global, etc.).\n", " */\n", "import { partition } from \"lodash\";\n", "import * as vscode from \"vscode\";\n", "\n", "import { getLogger } from \"../logging\";\n", "import { NextEditSessionEventReporter } from \"../metrics/next-edit-session-event-reporter\";\n", "import { DisposableService } from \"../utils/disposable-service\";\n", "import { EphemeralObservable } from \"../utils/ephemeral-flag\";\n", "import { WorkspaceManager } from \"../workspace/workspace-manager\";\n", "import {\n", "    ChangeType,\n", "    NextEditSessionEventName,\n", "    NextEditSessionEventSource,\n", "    SuggestionState,\n", "} from \"./next-edit-types\";\n", "import { EditSuggestion, SuggestionChangedEvent, SuggestionManager } from \"./suggestion-manager\";\n", "import {\n", "    Accepted,\n", "    Invalidated,\n", "    SuggestionUpdate,\n", "    Unchanged,\n", "    Updated,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/src/commands/next-edit.ts`:\n", "\n", "```\n", "...\n", "        syncingEnabledTracker: SyncingEnabledTracker\n", "    ) {\n", "        super(extension, configListener, syncingEnabledTracker, undefined);\n", "    }\n", "\n", "    canRun(): boolean {\n", "        return super.canRun() && this._extension.nextEditCanResetCooldown();\n", "    }\n", "\n", "    run() {\n", "        this._extension.nextEditResetCooldown();\n", "    }\n", "}\n", "\n", "export class NextEditToggleBackgroundSuggestions extends AbstractNextEditCommand {\n", "    type = CommandType.public;\n", "    public static readonly commandID = \"vscode-augment.next-edit.toggle-bg\";\n", "    public static readonly backgroundSuggestionsConfigKey = \"enableBackgroundSuggestions\";\n", "\n", "    commandID = NextEditToggleBackgroundSuggestions.commandID;\n", "\n", "    constructor(\n", "        extension: AugmentExtension,\n", "        configListener: AugmentConfigListener,\n", "        syncingEnabledTracker: SyncingEnabledTracker\n", "    ) {\n", "        super(\n", "            extension,\n", "            config<PERSON><PERSON><PERSON>,\n", "            syncingEnabledTracker,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/yangguang/vscode-ui-prototype/src/features/codelens.ts`:\n", "\n", "```\n", "...\n", "export const suggestionCodeLensProvider = new AugmentCodeLensProvider([\n", "    {title: `Performance suggestion ℹ`, command: \"extension.suggestionMessage\"},\n", "]);\n", "\n", "export default function () {\n", "    const runningCodeLensDisposable = vscode.languages.registerCodeLensProvider(\"*\", runningCodeLensProvider);\n", "    const diffsCodeLensDisposable = vscode.languages.registerCodeLensProvider(\"*\", diffViewerCodeLensProvider);\n", "    const suggestCodeLensDisposable = vscode.languages.registerCodeLensProvider(\"*\", suggestionCodeLensProvider);\n", "\n", "    const acceptDisposable = vscode.commands.registerCommand('extension.acceptCodeChange', () => {\n", "        try {\n", "            const activeEditor = vscode.window.activeTextEditor;\n", "            if (!activeEditor) {\n", "                return;\n", "            }\n", "            clearAllDecorations();\n", "            deleteLines(activeEditor, editStateStore.removedLines);\n", "        } catch (e) {\n", "            vscode.window.showErrorMessage(`Error: ${e.message}`);\n", "        }\n", "    });\n", "...\n", "```\n", "\"\"\"\n", "\n", "retrieval_reply = \"Understood. I'll refer to the excerpts for context, and ignore them for general questions.\""]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-10-26 21:09:57\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "# PROJECT_ID = \"system-services-prod\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "\n", "vertex_ai_client = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Improve next edit suggestion filtering and performance\n", "\n", "This commit enhances the filtering logic for next edit suggestions and optimizes performance. Key changes include:\n", "\n", "- Relax hunk size filtering to only exclude hunks removing more than 5 lines\n", "- Skip processing requests with no reconstructed file changes\n", "- Apply threshold filter to suggestion changed events for consistency\n", "- Optimize visible range tracking to reduce unnecessary redraws\n", "\n", "These improvements should result in more relevant suggestions and better overall responsiveness.\n"]}], "source": ["response = vertex_ai_client.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=[\n", "        # {\"role\": \"user\", \"content\": retrieval_message},\n", "        # {\"role\": \"assistant\", \"content\": retrieval_reply},\n", "        {\"role\": \"user\", \"content\": current_message},\n", "    ],\n", "    system=system_message,\n", "    temperature=TEMPERAURE,\n", ")\n", "text = response.content[0].text\n", "print(text)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Improve next edit suggestion filtering and visibility\n", "\n", "Refine suggestion filtering logic to avoid unnecessary updates and improve performance. Only redraw decorations when visible ranges change significantly. Apply threshold filter consistently across suggestion changed events and manager methods.\n"]}], "source": ["response = vertex_ai_client.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": retrieval_message},\n", "        {\"role\": \"assistant\", \"content\": retrieval_reply},\n", "        {\"role\": \"user\", \"content\": current_message},\n", "    ],\n", "    system=system_message,\n", "    temperature=TEMPERAURE,\n", ")\n", "text = response.content[0].text\n", "print(text)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from functools import lru_cache\n", "from typing import Iterator\n", "from typing_extensions import override\n", "import structlog\n", "\n", "\n", "import vertexai\n", "from vertexai.preview.tokenization import get_tokenizer_for_model\n", "from vertexai.generative_models import (\n", "    Content,\n", "    GenerationConfig,\n", "    GenerativeModel,\n", "    <PERSON>rm<PERSON>ate<PERSON><PERSON>,\n", "    <PERSON>rmBlockThreshold,\n", "    Part,\n", ")\n", "\n", "from base.third_party_clients.third_party_model_client import (\n", "    ThirdPartyModelClient,\n", "    ThirdPartyModelResponse,\n", ")\n", "\n", "\n", "vertexai.init(project=PROJECT_ID, location=REGION)\n", "generation_config = GenerationConfig(\n", "    candidate_count=1,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "SAFETY_SETTINGS = {\n", "    HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,\n", "    HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,\n", "    HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,\n", "    HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,\n", "    HarmCategory.HARM_CATEGORY_UNSPECIFIED: HarmBlockThreshold.BLOCK_NONE,\n", "}"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["history = [\n", "    Content(role=\"user\", parts=[Part.from_text(retrieval_message)]),\n", "    Content(role=\"assistant\", parts=[Part.from_text(retrieval_reply)]),\n", "    # Content(role=\"user\", parts=[Part.from_text(current_message)]),\n", "]\n", "model = GenerativeModel(\n", "    model_name=MODEL_NAME,\n", "    generation_config=generation_config,\n", "    safety_settings=SAFETY_SETTINGS,\n", "    system_instruction=system_message,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["chat = model.start_chat(history=history)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response_iterator = chat.create(\n", "    [Part.from_text(current_message)],\n", "    stream=True,\n", "    generation_config=generation_config,\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["response_iterator = chat.send_message(\n", "    [Part.from_text(current_message)],\n", "    stream=True,\n", "    generation_config=generation_config,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"ename": "ResourceExhausted", "evalue": "429 Quota exceeded for aiplatform.googleapis.com/generate_content_requests_per_minute_per_project_per_base_model with base model: anthropic-claude-3-5-sonnet. Please submit a quota increase request. https://cloud.google.com/vertex-ai/docs/generative-ai/quotas-genai.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m_MultiThreadedRendezvous\u001b[0m                  Traceback (most recent call last)", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/api_core/grpc_helpers.py:170\u001b[0m, in \u001b[0;36m_wrap_stream_errors.<locals>.error_remapped_callable\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    169\u001b[0m     prefetch_first \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mgetattr\u001b[39m(callable_, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_prefetch_first_result_\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m--> 170\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_StreamingResponseIterator\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    171\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresult\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprefetch_first_result\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprefetch_first\u001b[49m\n\u001b[1;32m    172\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    173\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m grpc\u001b[38;5;241m.\u001b[39mRpcError \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/api_core/grpc_helpers.py:92\u001b[0m, in \u001b[0;36m_StreamingResponseIterator.__init__\u001b[0;34m(self, wrapped, prefetch_first_result)\u001b[0m\n\u001b[1;32m     91\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m prefetch_first_result:\n\u001b[0;32m---> 92\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_stored_first_result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_wrapped\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     93\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[1;32m     94\u001b[0m     \u001b[38;5;66;03m# It is possible the wrapped method isn't an iterable (a grpc.Call\u001b[39;00m\n\u001b[1;32m     95\u001b[0m     \u001b[38;5;66;03m# for instance). If this happens don't store the first result.\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/grpc/_channel.py:543\u001b[0m, in \u001b[0;36m_Rendezvous.__next__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    542\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__next__\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 543\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_next\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/grpc/_channel.py:969\u001b[0m, in \u001b[0;36m_MultiThreadedRendezvous._next\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    968\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state\u001b[38;5;241m.\u001b[39mcode \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 969\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n", "\u001b[0;31m_MultiThreadedRendezvous\u001b[0m: <_MultiThreadedRendezvous of RPC that terminated with:\n\tstatus = StatusCode.RESOURCE_EXHAUSTED\n\tdetails = \"Quota exceeded for aiplatform.googleapis.com/generate_content_requests_per_minute_per_project_per_base_model with base model: anthropic-claude-3-5-sonnet. Please submit a quota increase request. https://cloud.google.com/vertex-ai/docs/generative-ai/quotas-genai.\"\n\tdebug_error_string = \"UNKNOWN:Error received from peer ipv4:*************:443 {grpc_message:\"Quota exceeded for aiplatform.googleapis.com/generate_content_requests_per_minute_per_project_per_base_model with base model: anthropic-claude-3-5-sonnet. Please submit a quota increase request. https://cloud.google.com/vertex-ai/docs/generative-ai/quotas-genai.\", grpc_status:8, created_time:\"2024-10-26T21:03:02.098465497+00:00\"}\"\n>", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mResourceExhausted\u001b[0m                         Traceback (most recent call last)", "Cell \u001b[0;32mIn[9], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mresponse_iterator\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m      2\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mprint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mparts\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtext\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/vertexai/generative_models/_generative_models.py:1417\u001b[0m, in \u001b[0;36mChatSession._send_message_streaming\u001b[0;34m(self, content, generation_config, safety_settings, tools)\u001b[0m\n\u001b[1;32m   1415\u001b[0m chunks \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m   1416\u001b[0m full_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m-> 1417\u001b[0m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m   1418\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunks\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mappend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1419\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# By default we're not adding incomplete interactions to history.\u001b[39;49;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/vertexai/generative_models/_generative_models.py:781\u001b[0m, in \u001b[0;36m_GenerativeModel._generate_content_streaming\u001b[0;34m(self, contents, generation_config, safety_settings, tools, tool_config)\u001b[0m\n\u001b[1;32m    756\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Generates content.\u001b[39;00m\n\u001b[1;32m    757\u001b[0m \n\u001b[1;32m    758\u001b[0m \u001b[38;5;124;03mArgs:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    772\u001b[0m \u001b[38;5;124;03m    A stream of GenerationResponse objects\u001b[39;00m\n\u001b[1;32m    773\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    774\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_request(\n\u001b[1;32m    775\u001b[0m     contents\u001b[38;5;241m=\u001b[39mcontents,\n\u001b[1;32m    776\u001b[0m     generation_config\u001b[38;5;241m=\u001b[39mgeneration_config,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    779\u001b[0m     tool_config\u001b[38;5;241m=\u001b[39mtool_config,\n\u001b[1;32m    780\u001b[0m )\n\u001b[0;32m--> 781\u001b[0m response_stream \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_prediction_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstream_generate_content\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    782\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\n\u001b[1;32m    783\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    784\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m response_stream:\n\u001b[1;32m    785\u001b[0m     \u001b[38;5;28;01myield\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_parse_response(chunk)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/cloud/aiplatform_v1/services/prediction_service/client.py:2250\u001b[0m, in \u001b[0;36mPredictionServiceClient.stream_generate_content\u001b[0;34m(self, request, model, contents, retry, timeout, metadata)\u001b[0m\n\u001b[1;32m   2247\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_universe_domain()\n\u001b[1;32m   2249\u001b[0m \u001b[38;5;66;03m# Send the request.\u001b[39;00m\n\u001b[0;32m-> 2250\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mrpc\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   2251\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2252\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretry\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretry\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2253\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2254\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2255\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2257\u001b[0m \u001b[38;5;66;03m# Done; return the response.\u001b[39;00m\n\u001b[1;32m   2258\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py:131\u001b[0m, in \u001b[0;36m_GapicCallable.__call__\u001b[0;34m(self, timeout, retry, compression, *args, **kwargs)\u001b[0m\n\u001b[1;32m    128\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_compression \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    129\u001b[0m     kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcompression\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m compression\n\u001b[0;32m--> 131\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapped_func\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/google/api_core/grpc_helpers.py:174\u001b[0m, in \u001b[0;36m_wrap_stream_errors.<locals>.error_remapped_callable\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    170\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _StreamingResponseIterator(\n\u001b[1;32m    171\u001b[0m         result, prefetch_first_result\u001b[38;5;241m=\u001b[39mprefetch_first\n\u001b[1;32m    172\u001b[0m     )\n\u001b[1;32m    173\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m grpc\u001b[38;5;241m.\u001b[39mRpcError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[0;32m--> 174\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exceptions\u001b[38;5;241m.\u001b[39mfrom_grpc_error(exc) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mexc\u001b[39;00m\n", "\u001b[0;31mResourceExhausted\u001b[0m: 429 Quota exceeded for aiplatform.googleapis.com/generate_content_requests_per_minute_per_project_per_base_model with base model: anthropic-claude-3-5-sonnet. Please submit a quota increase request. https://cloud.google.com/vertex-ai/docs/generative-ai/quotas-genai."]}], "source": ["for response in response_iterator:\n", "    print(response.parts[0].text)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Improve next edit suggestion filtering and visibility\n", "\n", "Refine suggestion filtering logic to prevent unnecessary updates and improve performance. Only redraw decorations when visible ranges change significantly. Apply threshold filter consistently across suggestion changed events and manager methods."]}], "source": ["from anthropic import AnthropicVertex\n", "\n", "\n", "client = AnthropicVertex(\n", "    region=REGION,\n", "    project_id=PROJECT_ID,\n", ")\n", "\n", "formatted_messages = [\n", "    {\"role\": \"user\", \"content\": retrieval_message},\n", "    {\"role\": \"assistant\", \"content\": retrieval_reply},\n", "    {\"role\": \"user\", \"content\": current_message},\n", "]\n", "prefill = None\n", "with client.messages.stream(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    temperature=TEMPERAURE,\n", "    system=system_message,\n", "    messages=formatted_messages,\n", ") as message_stream:\n", "    for text in message_stream.text_stream:\n", "        print(text, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
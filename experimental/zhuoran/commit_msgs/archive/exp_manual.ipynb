{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "gcloud auth login\n", "gcloud auth application-default login\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "\n", "token_counter = ClaudeTokenCounter()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.prompt_builder import GitHistory\n", "\n", "gh = GitHistory.from_json(\n", "    \"/home/<USER>/zhuoran/commit_msgs/commits.json\", token_counter.count_tokens\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "eval_set = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_full.json\"))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff: 5\n", "smallest_diff: 5\n", "clients_latest: 30\n", "deploy_latest: 30\n", "base_latest: 30\n", "data_latest: 5\n", "services_latest: 30\n", "tools_latest: 30\n", "research_latest: 30\n", "experimental_latest: 30\n", ".github_latest: 30\n", "models_latest: 30\n", "third_party_latest: 30\n", ".vscode_latest: 15\n"]}], "source": ["# Medium eval set\n", "# largest_diff: 0, 4, 1\n", "# smallest_diff: 3, 4, 2\n", "# clients_latest: 3, 1\n", "# deploy_latest: 3, 2\n", "# base_latest: 3, 2, 1\n", "# data_latest: 0\n", "# services_latest: 0\n", "# tools_latest: 3\n", "# research_latest: 13, 11\n", "# experimental_latest: 0, 1\n", "# .github_latest: 1\n", "# models_latest: 1\n", "# third_party_latest: 1\n", "# .vscode_latest: 0\n", "for reason in eval_set:\n", "    print(f\"{reason}: {len(eval_set[reason])}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'commit_index': 8233,\n", " 'diff_stats': {'insertions': 0,\n", "  'deletions': 4854783036,\n", "  'total_changes': 4854783036},\n", " 'folder_percentages': [['third_party', 93.65649311789348],\n", "  ['models', 4.90724117295033],\n", "  ['base', 0.9575104727707959],\n", "  ['services', 0.3590664272890485],\n", "  ['tools', 0.11968880909634949]],\n", " 'reason': 'largest_diff',\n", " 'full_set_index': 0}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["example = eval_set[\"largest_diff\"][0]\n", "index = example[\"commit_index\"]\n", "example"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["'4a728957135d5d0c84ab3610b9a4683231bd163a'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["commit = gh.get_commit(index)\n", "commit[\"current_hash\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Init exp"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["commit_index = 6"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.prompt_builder import GitHistory\n", "\n", "\n", "gh = GitHistory.from_json(\"/home/<USER>/zhuoran/commit_msgs/10_commits.json\")\n", "print(gh.get_prompt(commit_index, diff=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text, response, prompt_output = run_claude(\n", "    make_message_only_input(gh.get_prompt(commit_index, diff=True)),\n", "    stpf,\n", ")\n", "print(text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt_output.message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Overlap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from thefuzz import fuzz\n", "from tqdm import tqdm\n", "import heapq\n", "\n", "git_history = GitHistory.from_json(\n", "    \"/home/<USER>/zhuoran/commit_msgs/1000_commits.json\"\n", ")\n", "\n", "# Initialize variables\n", "scores = []\n", "top_overlaps = []  # Will store tuples of (score, commit_id)\n", "\n", "# Process every 10th commit\n", "for i in tqdm(range(0, git_history.get_length(), 10)):\n", "    # Get the prompt for the current commit\n", "    prompt = git_history.get_prompt(i)\n", "    no_diff_prompt = git_history.get_prompt(i, diff=False)\n", "\n", "    # Call Claude API\n", "    input_ = make_message_only_input(prompt)\n", "    claude_response = run_claude(input_, stpf)\n", "\n", "    # Calculate fuzzy match score\n", "    score = fuzz.ratio(no_diff_prompt, claude_response[0])\n", "    scores.append(score)\n", "\n", "    # Update top 10 highest overlapping commits\n", "    if len(top_overlaps) < 10:\n", "        heapq.heappush(top_overlaps, (score, i))\n", "    elif score > top_overlaps[0][0]:\n", "        heapq.heappop(top_overlaps)\n", "        heapq.heappush(top_overlaps, (score, i))\n", "    time.sleep(5)\n", "\n", "# Sort top_overlaps in descending order of score\n", "top_overlaps.sort(reverse=True)\n", "\n", "# Print results\n", "print(f\"Processed {len(scores)} commits\")\n", "print(f\"Average overlap score: {sum(scores) / len(scores):.2f}\")\n", "print(f\"Min overlap score: {min(scores)}\")\n", "print(f\"Max overlap score: {max(scores)}\")\n", "\n", "print(\"\\nTop 10 highest overlapping commits:\")\n", "for score, commit_id in top_overlaps:\n", "    print(f\"Commit ID: {commit_id}, Score: {score}\")\n", "\n", "# Optional: Plot a histogram of scores\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(scores, bins=20, edgecolor=\"black\")\n", "plt.title(\"Distribution of Fuzzy Match Scores\")\n", "plt.xlabel(\"Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.show()\n", "\n", "# Optional: Calculate and print percentiles\n", "import numpy as np\n", "\n", "percentiles = [0, 10, 25, 50, 75, 90, 100]\n", "score_percentiles = np.percentile(scores, percentiles)\n", "\n", "print(\"\\nScore percentiles:\")\n", "print(\"==================\")\n", "for p, value in zip(percentiles, score_percentiles):\n", "    print(f\"{p:3d}th percentile: {value:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Temp"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Add ability to share chat conversations from VSCode\n", "Add ability to share chat conversations from VSCode\n", "Add i0-vanguard0 to model environment list\n", "Add i0-vanguard0 to model environment list\n"]}], "source": ["oo_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=OUTER_SYSTEM_PROMPT,\n", "    token_apportionment=token_apportionment,\n", ")\n", "on_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=OUTER_SYSTEM_PROMPT,\n", "    token_apportionment=new_token_apportionment,\n", ")\n", "no_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=NEW_SYSTEM_PROMPT,\n", "    token_apportionment=token_apportionment,\n", ")\n", "nn_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=NEW_SYSTEM_PROMPT,\n", "    token_apportionment=new_token_apportionment,\n", ")\n", "# text, response, prompt_output, messages = run_claude(input_, x_stpf)\n", "oo_text, oo_response, oo_prompt_output, oo_messages = run_claude(input_, oo_stpf)\n", "on_text, on_response, on_prompt_output, on_messages = run_claude(input_, on_stpf)\n", "no_text, no_response, no_prompt_output, no_messages = run_claude(input_, no_stpf)\n", "nn_text, nn_response, nn_prompt_output, nn_messages = run_claude(input_, nn_stpf)\n", "print(oo_text)\n", "print(on_text)\n", "print(no_text)\n", "print(nn_text)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "True\n"]}], "source": ["print(oo_messages)\n", "print(on_messages)\n", "print(no_messages)\n", "print(nn_messages)\n", "print(oo_messages == on_messages == no_messages == nn_messages)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n", "StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n", "StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n", "StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n"]}], "source": ["print(oo_prompt_output)\n", "print(on_prompt_output)\n", "print(no_prompt_output)\n", "print(nn_prompt_output)"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["chat_history True\n", "message True\n", "retrieved_chunks_in_prompt True\n", "system_prompt False\n", "tools True\n", "workspace_file_chunks True\n"]}], "source": ["#  'chat_history',\n", "#  'message',\n", "#  'retrieved_chunks_in_prompt',\n", "#  'system_prompt',\n", "#  'tools',\n", "#  'workspace_file_chunks']\n", "print(\n", "    \"chat_history\",\n", "    oo_prompt_output.chat_history\n", "    == on_prompt_output.chat_history\n", "    == no_prompt_output.chat_history\n", "    == nn_prompt_output.chat_history,\n", ")\n", "print(\n", "    \"message\",\n", "    oo_prompt_output.message\n", "    == on_prompt_output.message\n", "    == no_prompt_output.message\n", "    == nn_prompt_output.message,\n", ")\n", "print(\n", "    \"retrieved_chunks_in_prompt\",\n", "    oo_prompt_output.retrieved_chunks_in_prompt\n", "    == on_prompt_output.retrieved_chunks_in_prompt\n", "    == no_prompt_output.retrieved_chunks_in_prompt\n", "    == nn_prompt_output.retrieved_chunks_in_prompt,\n", ")\n", "print(\n", "    \"system_prompt\",\n", "    oo_prompt_output.system_prompt\n", "    == on_prompt_output.system_prompt\n", "    == no_prompt_output.system_prompt\n", "    == nn_prompt_output.system_prompt,\n", ")\n", "print(\n", "    \"tools\",\n", "    oo_prompt_output.tools\n", "    == on_prompt_output.tools\n", "    == no_prompt_output.tools\n", "    == nn_prompt_output.tools,\n", ")\n", "print(\n", "    \"workspace_file_chunks\",\n", "    oo_prompt_output.workspace_file_chunks()\n", "    == on_prompt_output.workspace_file_chunks()\n", "    == no_prompt_output.workspace_file_chunks()\n", "    == nn_prompt_output.workspace_file_chunks(),\n", ")"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by Anthrop<PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n", "<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by Anthrop<PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n", "<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n", "<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n"]}], "source": ["print(oo_prompt_output.workspace_file_chunks)\n", "print(on_prompt_output.workspace_file_chunks)\n", "print(no_prompt_output.workspace_file_chunks)\n", "print(nn_prompt_output.workspace_file_chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
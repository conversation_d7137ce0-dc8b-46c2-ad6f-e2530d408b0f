{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay Chat requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Commit message replay"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["OUTER_SYSTEM_PROMPT = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "```python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "```yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "```\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "```ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "```\n", "</augment_code_snippet>\"\"\""]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up!\"\"\""]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-10-26 19:51:46\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["```python\n", "print(\"Hello, <PERSON>!\")\n", "```\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "dialog = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"Write a hello world in Python. Return a code block only.\",\n", "    },\n", "]\n", "\n", "response = ANTHROPIC_CLIENT.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=dialog,\n", "    temperature=TEMPERAURE,\n", ")\n", "print(response.content[0].text)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    ChatPromptInput,\n", ")\n", "\n", "\n", "def run_claude(\n", "    chat_prompt_input: ChatPromptInput,\n", "    prompt_formatter: ChatPromptFormatter,\n", "    tools=None,\n", "):\n", "    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "    messages = []\n", "    for exchange in prompt_output.chat_history:\n", "        messages.extend(\n", "            [\n", "                {\"role\": \"user\", \"content\": exchange.request_message},\n", "                {\"role\": \"assistant\", \"content\": exchange.response_text},\n", "            ]\n", "        )\n", "    messages.append({\"role\": \"user\", \"content\": prompt_output.message})\n", "    if tools is None:\n", "        response = ANTHROPIC_CLIENT.client.messages.create(\n", "            model=MODEL_NAME,\n", "            max_tokens=MAX_OUTPUT_TOKENS,\n", "            messages=messages,\n", "            system=prompt_output.system_prompt,\n", "            temperature=TEMPERAURE,\n", "        )\n", "    else:\n", "        response = ANTHROPIC_CLIENT.client.messages.create(\n", "            model=MODEL_NAME,\n", "            max_tokens=MAX_OUTPUT_TOKENS,\n", "            messages=messages,\n", "            system=prompt_output.system_prompt,\n", "            temperature=TEMPERAURE,\n", "            tools=tools,\n", "        )\n", "    return response.content[0].text, response, prompt_output, messages"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.generate_commit_message_prompt_formatter import (\n", "    GenerateCommitMessagePromptFormatter,\n", ")\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", "    StructuredStraightThroughPromptFormatter,\n", ")\n", "\n", "\n", "token_apportionment = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=0,  # 2000,\n", "    retrieval_len_for_user_guided=0,  # 3000,\n", "    retrieval_len=0,  # -1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 128,  # 12k for prompt\n", ")\n", "\n", "token_counter = ClaudeTokenCounter()\n", "\n", "inner_formatter = StructuredBinksPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    token_apportionment=token_apportionment,\n", ")\n", "prompt_formatter = GenerateCommitMessagePromptFormatter(inner_formatter)\n", "stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=OUTER_SYSTEM_PROMPT,\n", "    token_apportionment=token_apportionment,\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from research.core.chat_prompt_input import ResearchChatPromptInput\n", "\n", "\n", "def make_message_only_input(message):\n", "    return ResearchChatPromptInput(\n", "        path=\"\",\n", "        prefix=\"\",\n", "        selected_code=\"\",\n", "        suffix=\"\",\n", "        message=message,\n", "        chat_history=[],\n", "        prefix_begin=0,\n", "        suffix_end=0,\n", "        retrieved_chunks=[],\n", "        doc_ids=[],\n", "        user_guided_blobs=[],\n", "        context_code_exchange_request_id=None,\n", "    )"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! I'm <PERSON><PERSON>, an AI code assistant. How can I help you with your software development tasks today? Do you have any specific questions about your code or project that I can assist you with?\n"]}], "source": ["x_model_input = make_message_only_input(\"Yo\")\n", "text, response, prompt_output, messages = run_claude(x_model_input, stpf)\n", "print(text)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Yo\n"]}], "source": ["print(prompt_output.message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Exp"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.prompt_builder import GitHistory\n", "\n", "gh = GitHistory.from_json(\n", "    \"/home/<USER>/zhuoran/commit_msgs/commits.json\", token_counter.count_tokens\n", ")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "eval_set = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_full.json\"))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff: 5\n", "smallest_diff: 5\n", "clients_latest: 30\n", "deploy_latest: 30\n", "base_latest: 30\n", "data_latest: 5\n", "services_latest: 30\n", "tools_latest: 30\n", "research_latest: 30\n", "experimental_latest: 30\n", ".github_latest: 30\n", "models_latest: 30\n", "third_party_latest: 30\n", ".vscode_latest: 15\n"]}], "source": ["# Medium eval set\n", "# largest_diff: 0, 4, 1\n", "# smallest_diff: 3, 4, 2\n", "# clients_latest: 3, 1\n", "# deploy_latest: 3, 2\n", "# base_latest: 3, 2, 1\n", "# data_latest: 0\n", "# services_latest: 0\n", "# tools_latest: 3\n", "# research_latest: 13, 11\n", "# experimental_latest: 0, 1\n", "# .github_latest: 1\n", "# models_latest: 1\n", "# third_party_latest: 1\n", "# .vscode_latest: 0\n", "for reason in eval_set:\n", "    print(f\"{reason}: {len(eval_set[reason])}\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'commit_index': 18,\n", " 'diff_stats': {'insertions': 26, 'deletions': 1, 'total_changes': 27},\n", " 'folder_percentages': [['clients', 100.0]],\n", " 'reason': 'folder_latest_clients',\n", " 'full_set_index': 3}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["example = eval_set[\"clients_latest\"][3]\n", "index = example[\"commit_index\"]\n", "example"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["18\n"]}], "source": ["print(index)\n", "commit = gh.get_commit(index)\n", "# commit"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["diff --git a/clients/vscode/src/next-edit/background-edits.ts b/clients/vscode/src/next-edit/background-edits.ts\n", "index 1c0ea6608..de82ee25c 100644\n", "--- a/clients/vscode/src/next-edit/background-edits.ts\n", "+++ b/clients/vscode/src/next-edit/background-edits.ts\n", "@@ -15,6 +15,7 @@ import { KeybindingWatcher } from \"../utils/keybindings\";\n", " import { isNotebookUri } from \"../utils/notebook\";\n", " import { Observable } from \"../utils/observable\";\n", " import { delayMs } from \"../utils/promise-utils\";\n", "+import { LineRange } from \"../utils/ranges\";\n", " import { toLineRange } from \"../utils/ranges-vscode\";\n", " import { msecToReadableString } from \"../utils/time\";\n", " import { APIStatus } from \"../utils/types\";\n", "@@ -46,6 +47,11 @@ class AcceptanceInfo {\n", "     constructor(public readonly suggestion: EditSuggestion) {}\n", " }\n", " \n", "+type LastVisibleRanges = {\n", "+    uri: vscode.Uri;\n", "+    visibleRanges: readonly LineRange[];\n", "+};\n", "+\n", " /**\n", "  * Manages state for the next edit background suggestions.\n", "  *\n", "@@ -166,6 +172,11 @@ export class BackgroundNextEdits extends DisposableService {\n", "         BackgroundNextEdits._maxNumberRecentSuggestions\n", "     );\n", " \n", "+    /**\n", "+     * The last visible ranges we saw.\n", "+     */\n", "+    private _lastVisibleRanges: LastVisibleRanges | undefined;\n", "+\n", "     constructor(\n", "         context: vscode.ExtensionContext,\n", "         public workspaceManager: WorkspaceManager,\n", "@@ -308,7 +319,17 @@ export class BackgroundNextEdits extends DisposableService {\n", "             vscode.window.onDidChangeTextEditorVisibleRanges((event) => {\n", "                 if (\n", "                     this.workspaceManager.safeResolvePathName(event.textEditor.document.uri) &&\n", "-                    event.textEditor === vscode.window.activeTextEditor\n", "+                    event.textEditor === vscode.window.activeTextEditor &&\n", "+                    // Don't hide the bottom decorations if the line range didn't change,\n", "+                    // e.g., if only the characters changed.\n", "+                    (!this._lastVisibleRanges ||\n", "+                        this._lastVisibleRanges.uri.fsPath !==\n", "+                            event.textEditor.document.uri.fsPath ||\n", "+                        this._lastVisibleRanges.visibleRanges.length !==\n", "+                            event.visibleRanges.length ||\n", "+                        this._lastVisibleRanges.visibleRanges.some(\n", "+                            (range, index) => !range.equals(toLineRange(event.visibleRanges[index]))\n", "+                        ))\n", "                 ) {\n", "                     this._drawDecorations();\n", "                     // Make sure we track bottom decoration state.\n", "@@ -316,6 +337,10 @@ export class BackgroundNextEdits extends DisposableService {\n", "                         this._decorationManager.shouldDrawBottomDecorations.value = false;\n", "                         debouncedSetBottomDecorations();\n", "                     }\n", "+                    this._lastVisibleRanges = {\n", "+                        uri: event.textEditor.document.uri,\n", "+                        visibleRanges: event.visibleRanges.map((r) => toLineRange(r)),\n", "+                    };\n", "                 }\n", "             })\n", "         );\n"]}], "source": ["print(gh.get_commit_diff(index, 1e10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12653\n", "You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up!\n", "\n", "diff --git a/clients/vscode/src/next-edit/background-edits.ts b/clients/vscode/src/next-edit/background-edits.ts\n", "index 1c0ea6608..de82ee25c 100644\n", "--- a/clients/vscode/src/next-edit/background-edits.ts\n", "+++ b/clients/vscode/src/next-edit/background-edits.ts\n", "@@ -15,6 +15,7 @@ import { KeybindingWatcher } from \"../utils/keybindings\";\n", " import { isNotebookUri } from \"../utils/notebook\";\n", " import { Observable } from \"../utils/observable\";\n", " import { delayMs } from \"../utils/promise-utils\";\n", "+import { LineRange } from \"../utils/ranges\";\n", " import { toLineRange } from \"../utils/ranges-vscode\";\n", " import { msecToReadableString } from \"../utils/time\";\n", " import { APIStatus } from \"../utils/types\";\n", "@@ -46,6 +47,11 @@ class AcceptanceInfo {\n", "     constructor(public readonly suggestion: EditSuggestion) {}\n", " }\n", " \n", "+type LastVisibleRanges = {\n", "+    uri: vscode.Uri;\n", "+    visibleRanges: readonly LineRange[];\n", "+};\n", "+\n", " /**\n", "  * Manages state for the next edit background suggestions.\n", "  *\n", "@@ -166,6 +172,11 @@ export class BackgroundNextEdits extends DisposableService {\n", "         BackgroundNextEdits._maxNumberRecentSuggestions\n", "     );\n", " \n", "+    /**\n", "+     * The last visible ranges we saw.\n", "+     */\n", "+    private _lastVisibleRanges: LastVisibleRanges | undefined;\n", "+\n", "     constructor(\n", "         context: vscode.ExtensionContext,\n", "         public workspaceManager: WorkspaceManager,\n", "@@ -308,7 +319,17 @@ export class BackgroundNextEdits extends DisposableService {\n", "             vscode.window.onDidChangeTextEditorVisibleRanges((event) => {\n", "                 if (\n", "                     this.workspaceManager.safeResolvePathName(event.textEditor.document.uri) &&\n", "-                    event.textEditor === vscode.window.activeTextEditor\n", "+                    event.textEditor === vscode.window.activeTextEditor &&\n", "+                    // Don't hide the bottom decorations if the line range didn't change,\n", "+                    // e.g., if only the characters changed.\n", "+                    (!this._lastVisibleRanges ||\n", "+                        this._lastVisibleRanges.uri.fsPath !==\n", "+                            event.textEditor.document.uri.fsPath ||\n", "+                        this._lastVisibleRanges.visibleRanges.length !==\n", "+                            event.visibleRanges.length ||\n", "+                        this._lastVisibleRanges.visibleRanges.some(\n", "+                            (range, index) => !range.equals(toLineRange(event.visibleRanges[index]))\n", "+                        ))\n", "                 ) {\n", "                     this._drawDecorations();\n", "                     // Make sure we track bottom decoration state.\n", "@@ -316,6 +337,10 @@ export class BackgroundNextEdits extends DisposableService {\n", "                         this._decorationManager.shouldDrawBottomDecorations.value = false;\n", "                         debouncedSetBottomDecorations();\n", "                     }\n", "+                    this._lastVisibleRanges = {\n", "+                        uri: event.textEditor.document.uri,\n", "+                        visibleRanges: event.visibleRanges.map((r) => toLineRange(r)),\n", "+                    };\n", "                 }\n", "             })\n", "         );move collect_repo_commits to research data (#14254)\n", "\n", "\n", "Add <PERSON><PERSON><PERSON> to `eng.jsonnet` (#14358)\n", "\n", "Adding myself to `eng.jsonnet` for permission to access services.\n", "\n", "---------\n", "\n", "Co-authored-by: <PERSON><PERSON><PERSON> <<EMAIL>>\n", "Don't use decorator based tracing in next_edit_host (#14321)\n", "\n", "It doesn't play well with generator functions which we now use heavily.\n", "[intellij] reload styles when display changes (#14357)\n", "\n", "Also made the initial zoom level setting more concise.\n", "Simplify parsing of Feature Flags in augment_client (#14110)\n", "\n", "This wasn't possible before the feature flags in client were matched\n", "with the ones in the server\n", "Make it easier to register frontend feature flags (#14340)\n", "\n", "The goal is to define them in two places in the backend:\n", "\n", "- the public_api.proto\n", "- the get_feature_flags function\n", "\n", "This is meant to be RFC. Is this a viable approach?\n", "Make the E2E feature flag test less brittle (#14356)\n", "\n", "if a person overwrites (and they can and will) feature flags in a\n", "namespace, the test will fail. instead just check the presense of\n", "the keys.\n", "\n", "Honestly: I would also be open to remove the complete test, but\n", "maybe this test makes sense.\n", "Skip requests with 0 reconstructed file changes. (#13951)\n", "\n", "We already skip requests that don't contain any editing events in the front end. This PR adds the logic to the back end to also stop the requests early if the list of file changes is empty after granular diff reconstruction. It also records an event to a new `_next_edit_diff_events_problems` counter when such cases happen.\n", "Allow prod tenants to be added as separate files (#14262)\n", "\n", "Testing done: tenant_config_kubecfg diff revealed aitutor-turing\n", "and govtechsg still there\n", "Use new Claude Sonnet 3.5 model for chat (#14345)\n", "\n", "Use new Claude-Sonnet 3.5 model in production. We take two existing\n", "models -- v3, v4 -- update the sonnet model to\n", "`claude-3-5-sonnet-v2@20241022` and get v5 and v6 respectively:\n", "\n", "v3 (default for customers) => v5\n", "v4 (default for dogfood) => v6\n", "github processor: Update registerRepoRequests metric to include a reason (#14293)\n", "\n", "Make sure we are actually incrementing this metric on every `registerRepo` request and add a reason field so we can see why this may be happening often\n", "[emb_idx] Remove RobustSignatureChunker option (#14098)\n", "\n", "Subprocess chunker has been going strong for almost a week.\n", "Improve next edit e2e test. (#14299)\n", "\n", "This PR changes the next edit e2e test to be a more meaningful example that tests the model's ability to finish a renaming change. It also updates the test config to use the raven v4 model, which is our current new default model.\n", "\n", "e2e test run: https://test-viewer.us-central1.dev.augmentcode.com/run/0192b5cf-91b5-991c-9d34-567e3da24a44\n", "Sort and organize /command diagnostics by line ranges (#14271)\n", "\n", "Show line numbers and sort and organize diagnostic messages by the line ranges in `/fix` an `/explain_issues` commands.\n", "\n", "Now:\n", "```\n", "Fix the selected code. The IDE reports the following issues:\n", "\n", "• L6: \"(\" was not closed\n", "• L11:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. \"get_stable_re\" is unknown import symbol\n", "    3. \"lease_ref\" is not defined\n", "    4. \"get_stable_re\" is not accessed\n", "    5. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Expected ',', found name\n", "• L11-12: Expression value is unused\n", "• L12:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. \"versions_fro\" is not defined\n", "    3. \"m_vsce_data\" is not defined\n", "    4. Expression value is unused\n", "• L13:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. Expected expression\n", "```\n", "\n", "Before:\n", "```\n", "Fix the selected code. The IDE reports the following issues:\n", "\n", "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Expected ',', found name\n", "2. \"(\" was not closed\n", "3. Statements must be separated by newlines or semicolons\n", "4. Statements must be separated by newlines or semicolons\n", "5. Statements must be separated by newlines or semicolons\n", "6. Expected expression\n", "7. \"get_stable_re\" is unknown import symbol\n", "8. \"lease_ref\" is not defined\n", "9. \"versions_fr\" is not defined\n", "10. Expression value is unused\n", "11. \"om_vsce_data\" is not defined\n", "12. Expression value is unused\n", "13. \"get_stable_re\" is not accessed\n", "```\n", "infra/grafana: Provision Contact Points (slack #infra-alerts)\n", "\n", "We just have the one contact point. The alerts sidecar handles all of\n", "alerting rules, contact points, and notification policies. Alerting\n", "rules can be a ConfigMap but Contact Points needs to be a secret because\n", "it contains tokens/keys.\n", "\n", "infra/grafana: jb update\n", "\n", "Update the vendored grafonnet\n", "\n", "infra/grafana: Add `make pull` (no updates)\n", "\n", "Add a target for the command used to get the chart `.tgz`. We currently\n", "already have the latest.\n", "\n", "infra/grafana: Update dashboard sidecar data\n", "\n", "This could probably be automated but doesn't seem worth it.\n", "\n", "infra/grafana: Kill-and-fill export for deletions\n", "\n", "infra/grafana: Add export.sh and re-export dashboards\n", "\n", "Last time I did this, I did some manual work to update datasources,\n", "access a subpath of the response, etc. Now everything is in a script.\n", "\n", "As noted in the script:\n", "\n", "TODO(mattm): This script is currently designed to export from the old Grafana to git for use in the new Grafana. Update\n", "it to use as a way to export manually updated dashboards from the new Grafana back into git.\n", "\n", "infra/grafana: Rename previously exported dashboards\n", "\n", "Remove some of the manual/human naming cleanup I'd done last time. We're\n", "going to re-export from a script, this will help avoid some diff noise.\n", "\n", "github processor: re-register repo when too many files changed (#14225)\n", "\n", "if more than 300 files are changed in a push or a push contains more than 250 commits, `CompareCommits` only gives us the first chunk.\n", "\n", "The api call only paginates based on commits, so instead of trying to paginate, we will re-register the repo.\n", "\n", "This should also move staging back along to normal, accepting new commits (AU-5011). We assume this will happen pretty rarely.\n", "Apply threshold filter to suggestion changed events (#14352)\n", "\n", "We already apply the threshold (when applicable) to the methods used to\n", "get suggestions from the SuggestionManager. Let's also apply it to the\n", "suggestion changed events.\n", "\n", "Specifically, this ensures that the global panel, which uses the latter\n", "not the former, doesn't see filtered things. (I thought about instead\n", "changing the panel to call one of the methods that already works, but\n", "this is more consistent.)\n", "\n", "I also had to muck around with readonlys a bit to get it to compile.\n", "Good old const.\n", "\n", "---------\n", "\n", "Co-authored-by: <PERSON> <<EMAIL>>\n", "Fix next edits in untitled files (#14332)\n", "\n", "I accidentally broke this when I disabled suggestions for files that\n", "were deleted.\n", "Add feature flag for chat entry decoration (#14320)\n", "\n", "Add a new feature flag for the chat entry decoration so that it can be\n", "enabled on prod.\n", "\n", "(I'll add two separate flags later for the hover panel and context menu)\n", "github-processor: do not log patch errors (#14324)\n", "\n", "These probably have confidential info (filenames)\n", "Relax hunk size filtering logic. (#14348)\n", "\n", "Following [Slack discussion](https://augment-wic8570.slack.com/archives/C07C2PUJF4N/p1729637052086779), this relaxes the hunk size filtering logic from \"filtering any hunk whose before code contains more than 5 lines\" to \"filtering any hunk that removes more than 5 lines\".\n", "Pod Disruption budget prevents maintenance (#14350)\n", "\n", "minAvailable was 1 but replicas was 1 too, which prevents GKE\n", "from doing maintenance.\n", "\n", "Testing done: kubecfg tests that run as part of precommit\n", "Disable test_get_feature_flags to get post-commit green (#14351)\n", "\n", "\n", "Propagate auth fields into tenants k8s objects (#14334)\n", "\n", "Missed this the first time around.\n", "\n", "Testing done: kubectl diff against production shows fields are now added.\n", "[auth] Remove two feature flags from flags.yaml (#14181)\n", "\n", "\n", "Remove ground_truth_span misc. (#13960)\n", "\n", "# Changes\n", "See #13839 for the overall strategy for removing ground_truth_span.\n", "\n", "Three miscellaneous `ground_truth_span` removals:\n", "* Remove from `pinocchio_instruct.py`. I cannot find any code reference anywhere in code edits that uses `ground_truth_span`.\n", "* Update model_server to use `cursor_position`. I'm not sure why the comment claims that `prefix_begin` is more accurate, this is almost certainly no longer true, because `prefix_begin` now refers to the blob (possibly out of date), and not the current file.\n", "* Remove from `attach_sys_to_nodes.py` and slap a NOTE at the top of file, since this code is already broken (doesn't do `add_docs` with ground truth removed. Discussed with @D-X-Y and he preferred to keep it in `eval` for now rather than remove or move it to `experimental`.\n", "\n", "# Tests\n", "* CI/CD\n", "\n"]}], "source": ["prompt = SYSTEM_PROMPT + \"\\n\\n\" + gh.get_prompt_legacy(index)\n", "print(len(prompt))\n", "print(prompt)\n", "input_ = make_message_only_input(prompt)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from copy import copy\n", "\n", "\n", "NEW_SYSTEM_PROMPT = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up!\"\"\"\n", "new_token_apportionment = copy(token_apportionment)\n", "new_token_apportionment.max_prompt_len = 1024 * 128\n", "new_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=NEW_SYSTEM_PROMPT,\n", "    token_apportionment=new_token_apportionment,\n", ")\n", "new_prompt = gh.get_prompt(\n", "    index, summary=False, latest_commit_count=32, token_budget=1024 * 12\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12853\n", "Below are the per-file diffs of the commit:\n", "\n", "diff --git a/clients/vscode/src/next-edit/background-edits.ts b/clients/vscode/src/next-edit/background-edits.ts\n", "index 1c0ea6608..de82ee25c 100644\n", "--- a/clients/vscode/src/next-edit/background-edits.ts\n", "+++ b/clients/vscode/src/next-edit/background-edits.ts\n", "@@ -15,6 +15,7 @@ import { KeybindingWatcher } from \"../utils/keybindings\";\n", " import { isNotebookUri } from \"../utils/notebook\";\n", " import { Observable } from \"../utils/observable\";\n", " import { delayMs } from \"../utils/promise-utils\";\n", "+import { LineRange } from \"../utils/ranges\";\n", " import { toLineRange } from \"../utils/ranges-vscode\";\n", " import { msecToReadableString } from \"../utils/time\";\n", " import { APIStatus } from \"../utils/types\";\n", "@@ -46,6 +47,11 @@ class AcceptanceInfo {\n", "     constructor(public readonly suggestion: EditSuggestion) {}\n", " }\n", " \n", "+type LastVisibleRanges = {\n", "+    uri: vscode.Uri;\n", "+    visibleRanges: readonly LineRange[];\n", "+};\n", "+\n", " /**\n", "  * Manages state for the next edit background suggestions.\n", "  *\n", "@@ -166,6 +172,11 @@ export class BackgroundNextEdits extends DisposableService {\n", "         BackgroundNextEdits._maxNumberRecentSuggestions\n", "     );\n", " \n", "+    /**\n", "+     * The last visible ranges we saw.\n", "+     */\n", "+    private _lastVisibleRanges: LastVisibleRanges | undefined;\n", "+\n", "     constructor(\n", "         context: vscode.ExtensionContext,\n", "         public workspaceManager: WorkspaceManager,\n", "@@ -308,7 +319,17 @@ export class BackgroundNextEdits extends DisposableService {\n", "             vscode.window.onDidChangeTextEditorVisibleRanges((event) => {\n", "                 if (\n", "                     this.workspaceManager.safeResolvePathName(event.textEditor.document.uri) &&\n", "-                    event.textEditor === vscode.window.activeTextEditor\n", "+                    event.textEditor === vscode.window.activeTextEditor &&\n", "+                    // Don't hide the bottom decorations if the line range didn't change,\n", "+                    // e.g., if only the characters changed.\n", "+                    (!this._lastVisibleRanges ||\n", "+                        this._lastVisibleRanges.uri.fsPath !==\n", "+                            event.textEditor.document.uri.fsPath ||\n", "+                        this._lastVisibleRanges.visibleRanges.length !==\n", "+                            event.visibleRanges.length ||\n", "+                        this._lastVisibleRanges.visibleRanges.some(\n", "+                            (range, index) => !range.equals(toLineRange(event.visibleRanges[index]))\n", "+                        ))\n", "                 ) {\n", "                     this._drawDecorations();\n", "                     // Make sure we track bottom decoration state.\n", "@@ -316,6 +337,10 @@ export class BackgroundNextEdits extends DisposableService {\n", "                         this._decorationManager.shouldDrawBottomDecorations.value = false;\n", "                         debouncedSetBottomDecorations();\n", "                     }\n", "+                    this._lastVisibleRanges = {\n", "+                        uri: event.textEditor.document.uri,\n", "+                        visibleRanges: event.visibleRanges.map((r) => toLineRange(r)),\n", "+                    };\n", "                 }\n", "             })\n", "         );\n", "\n", "\n", "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n", "\n", "\n", "Commit message example 0:\n", "move collect_repo_commits to research data (#14254)\n", "\n", "\n", "\n", "\n", "Commit message example 1:\n", "Add <PERSON><PERSON><PERSON> to `eng.jsonnet` (#14358)\n", "\n", "Adding myself to `eng.jsonnet` for permission to access services.\n", "\n", "---------\n", "\n", "Co-authored-by: <PERSON><PERSON><PERSON> <<EMAIL>>\n", "\n", "\n", "Commit message example 2:\n", "Don't use decorator based tracing in next_edit_host (#14321)\n", "\n", "It doesn't play well with generator functions which we now use heavily.\n", "\n", "\n", "Commit message example 3:\n", "[intellij] reload styles when display changes (#14357)\n", "\n", "Also made the initial zoom level setting more concise.\n", "\n", "\n", "Commit message example 4:\n", "Simplify parsing of Feature Flags in augment_client (#14110)\n", "\n", "This wasn't possible before the feature flags in client were matched\n", "with the ones in the server\n", "\n", "\n", "Commit message example 5:\n", "Make it easier to register frontend feature flags (#14340)\n", "\n", "The goal is to define them in two places in the backend:\n", "\n", "- the public_api.proto\n", "- the get_feature_flags function\n", "\n", "This is meant to be RFC. Is this a viable approach?\n", "\n", "\n", "Commit message example 6:\n", "Make the E2E feature flag test less brittle (#14356)\n", "\n", "if a person overwrites (and they can and will) feature flags in a\n", "namespace, the test will fail. instead just check the presense of\n", "the keys.\n", "\n", "Honestly: I would also be open to remove the complete test, but\n", "maybe this test makes sense.\n", "\n", "\n", "Commit message example 7:\n", "Skip requests with 0 reconstructed file changes. (#13951)\n", "\n", "We already skip requests that don't contain any editing events in the front end. This PR adds the logic to the back end to also stop the requests early if the list of file changes is empty after granular diff reconstruction. It also records an event to a new `_next_edit_diff_events_problems` counter when such cases happen.\n", "\n", "\n", "Commit message example 8:\n", "Allow prod tenants to be added as separate files (#14262)\n", "\n", "Testing done: tenant_config_kubecfg diff revealed aitutor-turing\n", "and govtechsg still there\n", "\n", "\n", "Commit message example 9:\n", "Use new Claude Sonnet 3.5 model for chat (#14345)\n", "\n", "Use new Claude-Sonnet 3.5 model in production. We take two existing\n", "models -- v3, v4 -- update the sonnet model to\n", "`claude-3-5-sonnet-v2@20241022` and get v5 and v6 respectively:\n", "\n", "v3 (default for customers) => v5\n", "v4 (default for dogfood) => v6\n", "\n", "\n", "Commit message example 10:\n", "github processor: Update registerRepoRequests metric to include a reason (#14293)\n", "\n", "Make sure we are actually incrementing this metric on every `registerRepo` request and add a reason field so we can see why this may be happening often\n", "\n", "\n", "Commit message example 11:\n", "[emb_idx] Remove RobustSignatureChunker option (#14098)\n", "\n", "Subprocess chunker has been going strong for almost a week.\n", "\n", "\n", "Commit message example 12:\n", "Improve next edit e2e test. (#14299)\n", "\n", "This PR changes the next edit e2e test to be a more meaningful example that tests the model's ability to finish a renaming change. It also updates the test config to use the raven v4 model, which is our current new default model.\n", "\n", "e2e test run: https://test-viewer.us-central1.dev.augmentcode.com/run/0192b5cf-91b5-991c-9d34-567e3da24a44\n", "\n", "\n", "Commit message example 13:\n", "Sort and organize /command diagnostics by line ranges (#14271)\n", "\n", "Show line numbers and sort and organize diagnostic messages by the line ranges in `/fix` an `/explain_issues` commands.\n", "\n", "Now:\n", "```\n", "Fix the selected code. The IDE reports the following issues:\n", "\n", "• L6: \"(\" was not closed\n", "• L11:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. \"get_stable_re\" is unknown import symbol\n", "    3. \"lease_ref\" is not defined\n", "    4. \"get_stable_re\" is not accessed\n", "    5. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Expected ',', found name\n", "• L11-12: Expression value is unused\n", "• L12:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. \"versions_fro\" is not defined\n", "    3. \"m_vsce_data\" is not defined\n", "    4. Expression value is unused\n", "• L13:\n", "    1. Statements must be separated by newlines or semicolons\n", "    2. Expected expression\n", "```\n", "\n", "Before:\n", "```\n", "Fix the selected code. The IDE reports the following issues:\n", "\n", "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: Expected ',', found name\n", "2. \"(\" was not closed\n", "3. Statements must be separated by newlines or semicolons\n", "4. Statements must be separated by newlines or semicolons\n", "5. Statements must be separated by newlines or semicolons\n", "6. Expected expression\n", "7. \"get_stable_re\" is unknown import symbol\n", "8. \"lease_ref\" is not defined\n", "9. \"versions_fr\" is not defined\n", "10. Expression value is unused\n", "11. \"om_vsce_data\" is not defined\n", "12. Expression value is unused\n", "13. \"get_stable_re\" is not accessed\n", "```\n", "\n", "\n", "Commit message example 14:\n", "infra/grafana: Provision Contact Points (slack #infra-alerts)\n", "\n", "We just have the one contact point. The alerts sidecar handles all of\n", "alerting rules, contact points, and notification policies. Alerting\n", "rules can be a ConfigMap but Contact Points needs to be a secret because\n", "it contains tokens/keys.\n", "\n", "\n", "\n", "Commit message example 15:\n", "infra/grafana: jb update\n", "\n", "Update the vendored grafonnet\n", "\n", "\n", "\n", "Commit message example 16:\n", "infra/grafana: Add `make pull` (no updates)\n", "\n", "Add a target for the command used to get the chart `.tgz`. We currently\n", "already have the latest.\n", "\n", "\n", "\n", "Commit message example 17:\n", "infra/grafana: Update dashboard sidecar data\n", "\n", "This could probably be automated but doesn't seem worth it.\n", "\n", "\n", "\n", "Commit message example 18:\n", "infra/grafana: Kill-and-fill export for deletions\n", "\n", "\n", "\n", "Commit message example 19:\n", "infra/grafana: Add export.sh and re-export dashboards\n", "\n", "Last time I did this, I did some manual work to update datasources,\n", "access a subpath of the response, etc. Now everything is in a script.\n", "\n", "As noted in the script:\n", "\n", "TODO(mattm): This script is currently designed to export from the old Grafana to git for use in the new Grafana. Update\n", "it to use as a way to export manually updated dashboards from the new Grafana back into git.\n", "\n", "\n", "\n", "Commit message example 20:\n", "infra/grafana: Rename previously exported dashboards\n", "\n", "Remove some of the manual/human naming cleanup I'd done last time. We're\n", "going to re-export from a script, this will help avoid some diff noise.\n", "\n", "\n", "\n", "Commit message example 21:\n", "github processor: re-register repo when too many files changed (#14225)\n", "\n", "if more than 300 files are changed in a push or a push contains more than 250 commits, `CompareCommits` only gives us the first chunk.\n", "\n", "The api call only paginates based on commits, so instead of trying to paginate, we will re-register the repo.\n", "\n", "This should also move staging back along to normal, accepting new commits (AU-5011). We assume this will happen pretty rarely.\n", "\n", "\n", "Commit message example 22:\n", "Apply threshold filter to suggestion changed events (#14352)\n", "\n", "We already apply the threshold (when applicable) to the methods used to\n", "get suggestions from the SuggestionManager. Let's also apply it to the\n", "suggestion changed events.\n", "\n", "Specifically, this ensures that the global panel, which uses the latter\n", "not the former, doesn't see filtered things. (I thought about instead\n", "changing the panel to call one of the methods that already works, but\n", "this is more consistent.)\n", "\n", "I also had to muck around with readonlys a bit to get it to compile.\n", "Good old const.\n", "\n", "---------\n", "\n", "Co-authored-by: <PERSON> <<EMAIL>>\n", "\n", "\n", "Commit message example 23:\n", "Fix next edits in untitled files (#14332)\n", "\n", "I accidentally broke this when I disabled suggestions for files that\n", "were deleted.\n", "\n", "\n", "Commit message example 24:\n", "Add feature flag for chat entry decoration (#14320)\n", "\n", "Add a new feature flag for the chat entry decoration so that it can be\n", "enabled on prod.\n", "\n", "(I'll add two separate flags later for the hover panel and context menu)\n", "\n", "\n", "Commit message example 25:\n", "github-processor: do not log patch errors (#14324)\n", "\n", "These probably have confidential info (filenames)\n", "\n", "\n", "Commit message example 26:\n", "Relax hunk size filtering logic. (#14348)\n", "\n", "Following [Slack discussion](https://augment-wic8570.slack.com/archives/C07C2PUJF4N/p1729637052086779), this relaxes the hunk size filtering logic from \"filtering any hunk whose before code contains more than 5 lines\" to \"filtering any hunk that removes more than 5 lines\".\n", "\n", "\n", "Commit message example 27:\n", "Pod Disruption budget prevents maintenance (#14350)\n", "\n", "minAvailable was 1 but replicas was 1 too, which prevents GKE\n", "from doing maintenance.\n", "\n", "Testing done: kubecfg tests that run as part of precommit\n", "\n", "\n", "Commit message example 28:\n", "Disable test_get_feature_flags to get post-commit green (#14351)\n", "\n", "\n", "\n", "\n", "Commit message example 29:\n", "Propagate auth fields into tenants k8s objects (#14334)\n", "\n", "Missed this the first time around.\n", "\n", "Testing done: kubectl diff against production shows fields are now added.\n", "\n", "\n", "Commit message example 30:\n", "[auth] Remove two feature flags from flags.yaml (#14181)\n", "\n", "\n", "\n", "\n", "Commit message example 31:\n", "Remove ground_truth_span misc. (#13960)\n", "\n", "# Changes\n", "See #13839 for the overall strategy for removing ground_truth_span.\n", "\n", "Three miscellaneous `ground_truth_span` removals:\n", "* Remove from `pinocchio_instruct.py`. I cannot find any code reference anywhere in code edits that uses `ground_truth_span`.\n", "* Update model_server to use `cursor_position`. I'm not sure why the comment claims that `prefix_begin` is more accurate, this is almost certainly no longer true, because `prefix_begin` now refers to the blob (possibly out of date), and not the current file.\n", "* Remove from `attach_sys_to_nodes.py` and slap a NOTE at the top of file, since this code is already broken (doesn't do `add_docs` with ground truth removed. Discussed with @D-X-Y and he preferred to keep it in `eval` for now rather than remove or move it to `experimental`.\n", "\n", "# Tests\n", "* CI/CD\n", "\n", "\n", "Above are all the commit message examples.\n", "\n", "\n", "\n"]}], "source": ["print(len(new_prompt))\n", "print(new_prompt)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["3654"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["token_counter.count_tokens(new_prompt)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimize visible range change detection for bottom decorations\n"]}], "source": ["new_input = make_message_only_input(new_prompt)\n", "new_text, new_response, new_prompt_output, new_messages = run_claude(\n", "    new_input, new_stpf\n", ")\n", "print(new_text)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# print(new_prompt_output.message)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Refactor background edit suggestions\n", "\n", "- Optimize visible range tracking to reduce unnecessary redraws\n", "- Improve handling of bottom decorations when scrolling\n", "- Add logic to skip processing empty diffs after reconstruction\n"]}], "source": ["text, response, prompt_output, messages = run_claude(input_, stpf)\n", "print(text)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fix bottom decoration flickering on accepts (#14355)\n", "\n", "Currently, when you accept a suggestion, we immediately show the bottom\n", "decoration. After a few seconds, it gets hidden, and then it comes back\n", "again after a few seconds. This is annoying.\n", "\n", "It seems like what's happening is that when we first show the decoration\n", "that causes us to get a visible ranges changed event because the end\n", "character of the last line changed (presumably because of the box we\n", "draw to hide stuff). This makes us hide it until the debouncing\n", "finishes.\n", "\n", "This commit fixes the issue by tracking the last line range and only\n", "hiding the decoration when that changed. Specifically, this won't\n", "trigger on character-only visible range changes.\n"]}], "source": ["print(commit[\"message\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Init exp"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["commit_index = 6"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.prompt_builder import GitHistory\n", "\n", "\n", "gh = GitHistory.from_json(\"/home/<USER>/zhuoran/commit_msgs/10_commits.json\")\n", "print(gh.get_prompt(commit_index, diff=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text, response, prompt_output = run_claude(\n", "    make_message_only_input(gh.get_prompt(commit_index, diff=True)),\n", "    stpf,\n", ")\n", "print(text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt_output.message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Overlap"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "from thefuzz import fuzz\n", "from tqdm import tqdm\n", "import heapq\n", "\n", "git_history = GitHistory.from_json(\n", "    \"/home/<USER>/zhuoran/commit_msgs/1000_commits.json\"\n", ")\n", "\n", "# Initialize variables\n", "scores = []\n", "top_overlaps = []  # Will store tuples of (score, commit_id)\n", "\n", "# Process every 10th commit\n", "for i in tqdm(range(0, git_history.get_length(), 10)):\n", "    # Get the prompt for the current commit\n", "    prompt = git_history.get_prompt(i)\n", "    no_diff_prompt = git_history.get_prompt(i, diff=False)\n", "\n", "    # Call Claude API\n", "    input_ = make_message_only_input(prompt)\n", "    claude_response = run_claude(input_, stpf)\n", "\n", "    # Calculate fuzzy match score\n", "    score = fuzz.ratio(no_diff_prompt, claude_response[0])\n", "    scores.append(score)\n", "\n", "    # Update top 10 highest overlapping commits\n", "    if len(top_overlaps) < 10:\n", "        heapq.heappush(top_overlaps, (score, i))\n", "    elif score > top_overlaps[0][0]:\n", "        heapq.heappop(top_overlaps)\n", "        heapq.heappush(top_overlaps, (score, i))\n", "    time.sleep(5)\n", "\n", "# Sort top_overlaps in descending order of score\n", "top_overlaps.sort(reverse=True)\n", "\n", "# Print results\n", "print(f\"Processed {len(scores)} commits\")\n", "print(f\"Average overlap score: {sum(scores) / len(scores):.2f}\")\n", "print(f\"Min overlap score: {min(scores)}\")\n", "print(f\"Max overlap score: {max(scores)}\")\n", "\n", "print(\"\\nTop 10 highest overlapping commits:\")\n", "for score, commit_id in top_overlaps:\n", "    print(f\"Commit ID: {commit_id}, Score: {score}\")\n", "\n", "# Optional: Plot a histogram of scores\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(scores, bins=20, edgecolor=\"black\")\n", "plt.title(\"Distribution of Fuzzy Match Scores\")\n", "plt.xlabel(\"Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.show()\n", "\n", "# Optional: Calculate and print percentiles\n", "import numpy as np\n", "\n", "percentiles = [0, 10, 25, 50, 75, 90, 100]\n", "score_percentiles = np.percentile(scores, percentiles)\n", "\n", "print(\"\\nScore percentiles:\")\n", "print(\"==================\")\n", "for p, value in zip(percentiles, score_percentiles):\n", "    print(f\"{p:3d}th percentile: {value:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Temp"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Add ability to share chat conversations from VSCode\n", "Add ability to share chat conversations from VSCode\n", "Add i0-vanguard0 to model environment list\n", "Add i0-vanguard0 to model environment list\n"]}], "source": ["oo_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=OUTER_SYSTEM_PROMPT,\n", "    token_apportionment=token_apportionment,\n", ")\n", "on_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=OUTER_SYSTEM_PROMPT,\n", "    token_apportionment=new_token_apportionment,\n", ")\n", "no_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=NEW_SYSTEM_PROMPT,\n", "    token_apportionment=token_apportionment,\n", ")\n", "nn_stpf = StructuredStraightThroughPromptFormatter.create(\n", "    token_counter=token_counter,\n", "    system_prompt_factory=NEW_SYSTEM_PROMPT,\n", "    token_apportionment=new_token_apportionment,\n", ")\n", "# text, response, prompt_output, messages = run_claude(input_, x_stpf)\n", "oo_text, oo_response, oo_prompt_output, oo_messages = run_claude(input_, oo_stpf)\n", "on_text, on_response, on_prompt_output, on_messages = run_claude(input_, on_stpf)\n", "no_text, no_response, no_prompt_output, no_messages = run_claude(input_, no_stpf)\n", "nn_text, nn_response, nn_prompt_output, nn_messages = run_claude(input_, nn_stpf)\n", "print(oo_text)\n", "print(on_text)\n", "print(no_text)\n", "print(nn_text)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "[{'role': 'user', 'content': 'You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n'}]\n", "True\n"]}], "source": ["print(oo_messages)\n", "print(on_messages)\n", "print(no_messages)\n", "print(nn_messages)\n", "print(oo_messages == on_messages == no_messages == nn_messages)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n", "StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n", "StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n", "StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)\n"]}], "source": ["print(oo_prompt_output)\n", "print(on_prompt_output)\n", "print(no_prompt_output)\n", "print(nn_prompt_output)"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["chat_history True\n", "message True\n", "retrieved_chunks_in_prompt True\n", "system_prompt False\n", "tools True\n", "workspace_file_chunks True\n"]}], "source": ["#  'chat_history',\n", "#  'message',\n", "#  'retrieved_chunks_in_prompt',\n", "#  'system_prompt',\n", "#  'tools',\n", "#  'workspace_file_chunks']\n", "print(\n", "    \"chat_history\",\n", "    oo_prompt_output.chat_history\n", "    == on_prompt_output.chat_history\n", "    == no_prompt_output.chat_history\n", "    == nn_prompt_output.chat_history,\n", ")\n", "print(\n", "    \"message\",\n", "    oo_prompt_output.message\n", "    == on_prompt_output.message\n", "    == no_prompt_output.message\n", "    == nn_prompt_output.message,\n", ")\n", "print(\n", "    \"retrieved_chunks_in_prompt\",\n", "    oo_prompt_output.retrieved_chunks_in_prompt\n", "    == on_prompt_output.retrieved_chunks_in_prompt\n", "    == no_prompt_output.retrieved_chunks_in_prompt\n", "    == nn_prompt_output.retrieved_chunks_in_prompt,\n", ")\n", "print(\n", "    \"system_prompt\",\n", "    oo_prompt_output.system_prompt\n", "    == on_prompt_output.system_prompt\n", "    == no_prompt_output.system_prompt\n", "    == nn_prompt_output.system_prompt,\n", ")\n", "print(\n", "    \"tools\",\n", "    oo_prompt_output.tools\n", "    == on_prompt_output.tools\n", "    == no_prompt_output.tools\n", "    == nn_prompt_output.tools,\n", ")\n", "print(\n", "    \"workspace_file_chunks\",\n", "    oo_prompt_output.workspace_file_chunks()\n", "    == on_prompt_output.workspace_file_chunks()\n", "    == no_prompt_output.workspace_file_chunks()\n", "    == nn_prompt_output.workspace_file_chunks(),\n", ")"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by Anthrop<PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n", "<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by Anthrop<PERSON>.\\nYour role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\\nThanks to Augment Code\\'s enhancements, you have access to additional information about the user\\'s project, including relevant code excerpts, documentation, and user actions such as selected code.\\n\\nWhen answering the developer\\'s questions, please follow these guidelines:\\n\\n- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\\n- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\\n- When referencing a file in your response, always include the FULL file path.\\n- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\\n- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\\n\\nMUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\\n\\n1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\\n\\n<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\\n```python\\nclass AbstractTokenizer():\\n    def __init__(self, name):\\n        self.name = name\\n\\n    ...\\n```\\n</augment_code_snippet>\\n\\n2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\\n```yaml\\napp:\\n  name: MyWebApp\\n  version: 1.3.0\\n\\ndatabase:\\n  host: new-db.example.com\\n  port: 5432\\n```\\n</augment_code_snippet>\\n\\n3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\\n\\n<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\\n```ruby\\ndef main\\n  puts \"Hello, world!\"\\nend\\n```\\n</augment_code_snippet>', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n", "<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n", "<bound method StructuredChatPromptOutput.workspace_file_chunks of StructuredChatPromptOutput(system_prompt='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!', chat_history=[], message='You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\\n- Strictly synthesizes meaningful information from the provided code diff\\n- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\\n- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\\n- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\\n- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\\n- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\\nFollow the user\\'s instructions carefully, don\\'t repeat yourself, don\\'t include the code in the output, or make anything up!\\n\\ndiff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\\nindex 275e81550..2b82b86c7 100644\\n--- a/tools/feature_flags/flags.yaml\\n+++ b/tools/feature_flags/flags.yaml\\n@@ -35,6 +35,7 @@ model:\\n             - dogfood\\n             - dogfood-shard\\n             - shv-staging\\n+            - i0-vanguard0\\n           return_value: eldenv4-0c-15b\\n         - return_value: eldenv3-15b\\n     test:uses us endpoint in dev as there is no eu endpoint (#13958)\\n\\n### <AUTHOR> <EMAIL>\\nAdd more error logging in github processor and state (#13925)\\n\\nTo make it easier to debug errors when we see them\\nBetter approach to allowing parallel retrieval in next edit (#13946)\\n\\nWalk back the #13818 change that removed the semaphore completely. Instead allow 2 threads to enter the semaphore at once. This should give us most of the intended benefit without running into timeouts in extreme latency cases (like dev deploy).\\nAdd a reward token to StarCoder2Tokenizer (#13916)\\n\\n\\nAdd ability to share chat conversations from VSCode\\n (#13056)\\n\\nAdd ability to share chat conversations from the VSCode client.\\n\\nThe chat sharing service is enabled through a backend feature flag called `enable_share_service`.\\nWhen this flag is set to true, a new Share icon will appear for each chat conversation, including the current chat session. Clicking on the share icon will send the conversation to the backend service, and a link is copied into the clipboard. \\n\\nWhen the flag is set to false, the share icon does not appear.\\n\\nSharing the same conversation multiple times will always resolve to the same link. This detection is currently caught in the backend. Adding at least one new exchange to a previously saved conversation will be treated as a new conversation with a new link.\\nimpose an overflowable budget limit on explicit external context and a minimum budget for in repo retrieval (#13127)\\n\\n(edited)\\r\\n- set a separate budget for external context retrieval that is\\r\\noptionally overflowing on top of regular retrieval budget.\\r\\n- this will include all external sources, which today includes docsets\\r\\n- when any sources are explicitly requested, the total budget is 5000;\\r\\nwhen sources are added implicitly, the budget is 2500\\r\\n- in dense retrieval, no longer filter by max number of results when\\r\\nmerging docset and repo retrieval. We cannot filter here because we\\r\\ndon\\'t know the tokenizer or how prompts will be formatted.\\r\\n- even though the budget is separate, we do not allow any irrelevant\\r\\nexternal chunks. that is, any chunk ranked lower than the last chunk in\\r\\nfrom the workspace files will be cut off. so these budgets are maximum,\\r\\nand can be 0 when there are no relevant chunks from external sources.\\r\\n\\r\\n\\nMove data/file-ext into clients (#13923)\\n\\nThis list of file extensions is really only used for vscode unit\\ntests. Let\\'s move it under `clients` so we can use `data` for\\nsomething else.\\nRevert \"AU-4774: Iterable location search\" (#13935)\\n\\nReverts augmentcode/augment#13645\\r\\n\\r\\nSeems to cause workspace mode requests to fail to return suggestions.\\nAdd `editing_score_threshold` to the public_api proto (#13929)\\n\\nTested: \\r\\n\\r\\nDev deploy + query via the python API\\nEnable new smart paste flag (min_version) for vanguard (#13941)\\n\\n\\n[Diff View] Update diff view styles (#13895)\\n\\n\\nupdate versions to fix -- Compiler option \\'extends\\' requires a value of type (#13917)\\n\\nUpgraded things to make the error below go away.\\n\\n\\n![image.png](https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/NLwk9WgJdDi7duRy7FdU/432a2f2a-d9ba-44f1-ba75-883f96e7593b.png)\\n\\n\\n\\n- \"typescript\": ^5.5.3\\n- \"@typescript-eslint/eslint-plugin\": \"^7.18.0\",\\n- \"@typescript-eslint/parser\": \"^7.18.0\",\\n-  \"eslint\": \"^8.57.0\",\\n\\n---------\\n\\nCo-authored-by: aswink <<EMAIL>>\\nMake onSuggestionsChanged event handling synchronous. (#13897)\\n\\nThis commit notifies listeners to `onSuggestionsChange` immediately and\\nsynchronously. This allows us to (1) move the event tracking entirely to\\n`suggestion-manager-impl` and sets us up for future\\n`suggestionJustChanged` events.\\nAdded some utility guards (#13733)\\n\\n### TL;DR\\n\\nIntroduced new type guards and improved error handling in the API\\nutility.\\n\\n### Why not Zod or Joi or some library...\\nMost of the validation libraries are concerned with validation, which of\\ncourse they should be, sometimes that is a lot to check if an object has\\na property. These are guards, so their utility is more limited\\n(synchronous, no custom error messages), but they are also faster at\\nrun, compile and type checking time. I think its likely worth having\\nboth. The `has` function is super useful in its own. If there is desire\\nI can move them out of the project to someplace common.\\n\\nAlso lodash gives me nightmares. \\n\\n### What changed?\\n\\n- Replaced the `has` function with more specific type guards in\\n`api.ts`.\\n- Created a new `guards.ts` file with various type guard functions and\\nutilities.\\n- Implemented `hasShape`, `isString`, `isNumber`, `optional`, and\\n`assertGuard` functions.\\n- Added a custom `GuardError` class for better error handling.\\n- Removed the `type.ts` file and moved its functionality to `guards.ts`.\\n\\n### How to test?\\n\\n1. Test the API calls in `api.ts` to ensure they still work correctly\\nwith the new error handling.\\n2. Create unit tests for the new guard functions in `guards.ts`.\\n3. Verify that the `getAuthenticatedApiData` function in `api.ts`\\ncorrectly handles 401 errors using the new `errorShape` guard.\\n\\n### Why make this change?\\n\\nThis change improves type safety and error handling in the codebase. The\\nnew guard functions provide more precise type checking, making it easier\\nto catch and handle errors early in the development process. The\\n`hasShape` function, in particular, allows for more complex object shape\\nvalidation, enhancing the overall robustness of the code.\\nFix bad multi-edit events. (#13725)\\n\\nThere are two ways we can generate a multi-edit event:\\n\\n__Type1__: If the user has enabled multi-cursor and typed some changes. In such cases, all edits in the event will have identical contents but different ranges.\\n__Type2__: If the edit is produced by some external events such as file changes or accepting model suggestions.\\n\\nPreviously, we always try to merge adjacent multi-edit events. But this turns out to be not safe because merging type 2 events may produce a bad event with overlapping edits. We also don\\'t need to merge type 2 events since they tend to be big already. We really only care about merging type 1 events. \\nThis PR changes the merging logic to only merge with type 1 events. It also adds additional checks to abandon the merging if bad edits are produced, which should hopefully not happen anymore, but who knows.\\nAdd new event when completions are resolved. (#13792)\\n\\nThis commit introduces a new `onCompletionAccepted` event that is fired\\nwhen a completion is resolved. We plan to use it as a cue to immediately\\nstart a next edit request.\\nAdd github app prod secret (#13909)\\n\\nThis seals the github app prod secret.\\nRevert \"Revert the share table addition in tenant_gc (#13901)\" (#13919)\\n\\nThis reverts commit 2f36307f2e53152809e7291a90102a1b5edbac74.\\n\\nThis effectively reapplies adding the \"share\" table to\\ntenant_gc, since Dirk fixed the test.\\n\\nTests:\\nhttps://test-viewer.us-central1.dev.augmentcode.com/run/01929681-6d72-246c-5e71-b4569ba993e4\\nGetting tenant cloud to determine if in eu and using eu endpoints (#13665)\\n\\n# TLDR;\\nMakes request-insights-service EU aware. The infrastructure can be used\\nfor other services that have different endpoints depending on region.\\n\\n# Why\\nTo allow the customer-ui to serve EU customers requests insight data.\\n\\n# What\\n\\n- Added tenant watcher client to fetch tenant information using ts_proto\\nin Bazel\\n- Implemented region-aware transport for request insights analytics\\n- Updated configuration to include EU endpoint for request insights\\nanalytics\\n- Modified API utility to pass tenant and shard information to context\\n- Added port forwarding for tenant-central-service in development setup\\n- Updated GRPC client configurations to use centralized transport\\noptions\\n- Refactored Config class to include new endpoints and transport\\ncreation methods\\n- Added caching mechanism for tenant information in\\nTenantWatcherCachingClient\\n- Updated BUILD files to include new dependencies and ts_proto libraries\\n\\n# Testing\\n1 - Log into customer-ui from a non-eu cloud provider.\\n2 - Check the logs to ensure the correct transport was used.\\nMove some logic from WorkspaceManager to ExternalSourceFolderRecorder (#13819)\\n\\nThis PR moves the logic for persisting the set of external source folders from `WorkspaceManager` to a new class, `ExternalSourceFolderRecorder`. This changes makes WorkspaceManager a tiny bit smaller and simpler, and it also makes testing easier, since this functionality can be mocked.\\nPolish Syncing Status Progress Bar (#13891)\\n\\n\\n[Chat] Add min smart paste version feature flags (#13902)\\n\\nAdds a feature flag for the minimum smart paste version of the extension\\nUpdate Experimental Codes (#13915)\\n\\n\\nTombstone failed github/slack deployments (#13912)\\n\\nThis should stop the alert spam we are getting from these. Also remove\\r\\nthe broken github deployments that I forgot to do earlier.\\r\\n\\r\\nTested by running `bazel run //tools/deploy_runner:check_tombstone --\\r\\n--check-all-app-deployments`, which found deployments for all four new\\r\\ntombstones.\\nAssorted fixes for tenant watcher client stuck in auth-query (#13871)\\n\\n* Add an alert if this happens again\\n* Set a keepalive on the tenant watcher client\\n* Set some keepalive parameters on the tenant watcher server\\n* Add some more logging in tenant watcher and auth-query around the\\nrelevant code paths\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609) (#13904)\\n\\nReapply \"Add github state and settings to tenant gc (#13525)\" (#13609)\\r\\n\\r\\nThis reverts commit f851830e476a014d99c9d7c02c637dd9f01b502f.\\r\\n\\r\\nfix\\nclean up autofix code in experimental (#13498)\\n\\n\\n[Smartpaste] Overlap filtering for streaming  (#13890)\\n\\nThis PR adds logic that detects overlapping lines on the boundaries of\\r\\nreplaced code.\\r\\n\\r\\nTesting done:\\r\\n- Manual testing\\r\\n- Added tests to `diff-by-line.test.ts`\\nSet feedback slackbot endpoint based on env (#13858)\\n\\nI wanted to get the endpoint update out quick but we should actually be\\nsetting it to point to a bot deployed to your dev namespace during\\ndevelopment.\\n', retrieved_chunks_in_prompt=[], tools=None)>\n"]}], "source": ["print(oo_prompt_output.workspace_file_chunks)\n", "print(on_prompt_output.workspace_file_chunks)\n", "print(no_prompt_output.workspace_file_chunks)\n", "print(nn_prompt_output.workspace_file_chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
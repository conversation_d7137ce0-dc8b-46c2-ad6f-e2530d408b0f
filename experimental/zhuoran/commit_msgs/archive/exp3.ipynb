{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "gcloud auth login\n", "gcloud auth application-default login\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-10-29 06:23:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["```python\n", "print(\"Hello, <PERSON>!\")\n", "```\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "vertex_ai_client = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "\n", "def run_claude(message, system_message):\n", "    response = vertex_ai_client.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    )\n", "    return response.content[0].text, response\n", "\n", "\n", "text, response = run_claude(\n", "    \"Write a hello world in Python. Return a code block only.\", \"\"\n", ")\n", "print(text)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12500\n"]}], "source": ["from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from experimental.zhuoran.commit_msgs.git_history import GitHistory\n", "\n", "\n", "token_counter = ClaudeTokenCounter()\n", "gh = GitHistory.from_json(\n", "    \"/home/<USER>/zhuoran/commit_msgs/commits_v4.json\",\n", "    token_counter.count_tokens,\n", "    \"utf-8\",\n", ")\n", "print(len(gh.commits))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "eval_set = json.load(open(\"/home/<USER>/zhuoran/commit_msgs/eval_set_v2.json\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["largest_diff: 30\n", "smallest_diff: 30\n", ".github: 2\n", "beauty: 30\n", "scripts: 8\n"]}], "source": ["# Medium eval set\n", "# largest_diff: 0, 4, 1\n", "# smallest_diff: 3, 4, 2\n", "# clients_latest: 3, 1\n", "# deploy_latest: 3, 2\n", "# base_latest: 3, 2, 1\n", "# data_latest: 0\n", "# services_latest: 0\n", "# tools_latest: 3\n", "# research_latest: 13, 11\n", "# experimental_latest: 0, 1\n", "# .github_latest: 1\n", "# models_latest: 1\n", "# third_party_latest: 1\n", "# .vscode_latest: 0\n", "for reason in eval_set:\n", "    print(f\"{reason}: {len(eval_set[reason])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Individual eval"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'commit_index': 59,\n", " 'diff_stats': {'insertions': 111, 'deletions': 42, 'total_changes': 153},\n", " 'folder_percentages': [['services', 66.66666666666666],\n", "  ['tools', 33.33333333333333]],\n", " 'reason': 'folder_latest_tools',\n", " 'full_set_index': 3}"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["example = eval_set[\"tools_latest\"][3]\n", "index = example[\"commit_index\"]\n", "example"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["TRAILING_INSTRUCTIONS = \"\"\"Do not include boilerplate text like \"Here is the commit message:\" in your response..\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Input prep"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["59\n"]}], "source": ["commit = gh.get_commit(index)\n", "print(index)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["# print(gh.get_commit_diff(index, 15_000))"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["prompts = {}"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["prompts[\"default\"] = gh.get_prompt(\n", "    index,\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=True,\n", "    # summary_v2_threshold=900,\n", "    token_budget=1024 * 12,\n", ")"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "services/api_proxy/server/src/handlers.rs: M\n", "services/api_proxy/server/src/model_registry.rs: M\n", "tools/feature_flags/flags.yaml: M\n"]}], "source": ["prompts[\"summary_v2\"] = gh.get_prompt(\n", "    index,\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=True,\n", "    summary_v2_threshold=0,\n", "    token_budget=1024 * 12,\n", ")"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["prompts[\"trailing_instructions\"] = gh.get_prompt(\n", "    index,\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=True,\n", "    trailing_instructions=TRAILING_INSTRUCTIONS,\n", "    token_budget=1024 * 12,\n", ")"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "services/api_proxy/server/src/handlers.rs: M\n", "services/api_proxy/server/src/model_registry.rs: M\n", "tools/feature_flags/flags.yaml: M\n"]}], "source": ["prompts[\"summary_v2_trailing_instructions\"] = gh.get_prompt(\n", "    index,\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=True,\n", "    summary_v2_threshold=900,\n", "    trailing_instructions=TRAILING_INSTRUCTIONS,\n", "    token_budget=1024 * 12,\n", ")"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["# prompts[\"unsorted\"] = gh.get_prompt(\n", "#     index,\n", "#     summary=False,\n", "#     latest_commit_count=32,\n", "#     sort_paths_by_size=False,\n", "#     count_diffs_by_hunk=True,\n", "#     token_budget=1024 * 12,\n", "# )"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [], "source": ["# prompts[\"v1\"] = gh.get_prompt_legacy(index)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Below are the per-file diffs of the commit:\n", "\n", "diff --git a/services/api_proxy/server/src/handlers.rs b/services/api_proxy/server/src/handlers.rs\n", "index 5a39cad3d..c59b83774 100644\n", "--- a/services/api_proxy/server/src/handlers.rs\n", "+++ b/services/api_proxy/server/src/handlers.rs\n", "@@ -17,7 +17,7 @@ use request_insight_publisher::request_insight;\n", " use request_insight_publisher::to_tenant_info_proto;\n", " use secrecy::{ExposeS<PERSON><PERSON>, SecretString, SecretVec};\n", " use serde::Serialize;\n", "-use sha2::Sha256;\n", "+use sha2::{Digest, Sha256};\n", " use tokio::sync::mpsc::Receiver;\n", " use tokio_stream::{wrappers::ReceiverStream, StreamExt};\n", " use tracing_actix_web::RootSpan;\n", "@@ -854,7 +854,10 @@ impl<MR: ModelRegistry, CNC: ContentManagerClient> Handler<MR, CNC> {\n", "             return Err(tonic::Status::resource_exhausted(\"Circuit breaker is open\"));\n", "         }\n", " \n", "-        let models = self.model_registry.get_models().await;\n", "+        let mut models = self.model_registry.get_models().await;\n", "+        for model in &mut models {\n", "+            model.name = hex::encode(Sha256::digest(model.name.as_bytes()));\n", "+        }\n", "         let mut response: public_api_proto::GetModelsResponse = models.into();\n", " \n", "         let enable_chat = ENABLE_CHAT_FLAG.get_from(&feature_flags);\n", "@@ -2834,6 +2837,7 @@ mod tests {\n", "         };\n", " \n", "         let req = setup_req();\n", "+\n", "         let root_span = new_root_span();\n", "         let resp = handle_api_auth(\n", "             app_state.clone(),\n", "@@ -4353,9 +4357,12 @@ mod tests {\n", "         let response = run_get_models().await;\n", "         #[allow(deprecated)]\n", "         let expected = public_api_proto::GetModelsResponse {\n", "-            default_model: Some(\"model1\".to_string()),\n", "+            default_model: Some(\n", "+                \"22ff0efe270b305371c97346e0619c987cfd10a5bb7c3acfcd7c92790a9ca91c\".to_string(),\n", "+            ),\n", "             models: vec![public_api_proto::Model {\n", "-                name: \"model1\".to_string(),\n", "+                name: \"22ff0efe270b305371c97346e0619c987cfd10a5bb7c3acfcd7c92790a9ca91c\"\n", "+                    .to_string(),\n", "                 suggested_prefix_char_count: DEFAULT_PREFIX_CHAR_COUNT,\n", "                 suggested_suffix_char_count: DEFAULT_SUFFIX_CHAR_COUNT,\n", "                 max_memorize_size_bytes: 131072,diff --git a/services/api_proxy/server/src/model_registry.rs b/services/api_proxy/server/src/model_registry.rs\n", "index 97ff08ddf..01fb4e9fe 100644\n", "--- a/services/api_proxy/server/src/model_registry.rs\n", "+++ b/services/api_proxy/server/src/model_registry.rs\n", "@@ -13,6 +13,7 @@ use crate::generation_clients::Client;\n", " use crate::generation_clients::ClientFactory;\n", " use async_lock::Mutex;\n", " use async_trait::async_trait;\n", "+use sha2::{Digest, Sha256};\n", " use std::collections::HashMap;\n", " use std::sync::Arc;\n", " \n", "@@ -25,10 +26,10 @@ pub struct Model {\n", " /// A model registry maintains the different models adn the modelhost endpoints for\n", " /// inference and memorization.\n", " #[async_trait]\n", "-pub trait ModelRegistry {\n", "+pub trait ModelRegistry: Send + Sync {\n", "     async fn get_model(\n", "         &self,\n", "-        model_name: &str,\n", "+        model_name_or_hash: &str,\n", "     ) -> Result<(Client, ModelInstanceConfig), tonic::Status>;\n", " \n", "     async fn get_default_model(\n", "@@ -118,12 +119,22 @@ impl DynamicModelRegistry {\n", " impl ModelRegistry for DynamicModelRegistry {\n", "     async fn get_model(\n", "         &self,\n", "-        model_name: &str,\n", "+        model_name_or_hash: &str, // Could be model_name or a SHA256 of model_name\n", "     ) -> Result<(Client, ModelInstanceConfig), tonic::Status> {\n", "-        match self.clients.lock().await.get(model_name) {\n", "-            None => Err(tonic::Status::not_found(\"Model not found\")),\n", "-            Some(entry) => Ok(entry.clone()),\n", "+        let candidate_names: Vec<String> = {\n", "+            let clients = self.clients.lock().await;\n", "+            clients.keys().cloned().collect()\n", "+        };\n", "+        for candidate_name in candidate_names {\n", "+            let candidate_name_hash = hex::encode(Sha256::digest(candidate_name.as_bytes()));\n", "+            if model_name_or_hash == candidate_name || model_name_or_hash == candidate_name_hash {\n", "+                match self.clients.lock().await.get(&candidate_name) {\n", "+                    Some(entry) => return Ok(entry.clone()),\n", "+                    None => return Err(tonic::Status::not_found(\"Model not found\")),\n", "+                }\n", "+            }\n", "         }\n", "+        Err(tonic::Status::not_found(\"Model not found\"))\n", "     }\n", " \n", "     async fn get_default_model(\n", "@@ -249,6 +260,7 @@ pub mod tests {\n", "     use async_trait::async_trait;\n", "     use lazy_static::lazy_static;\n", "     use request_context::RequestContext;\n", "+    use sha2::{Digest, Sha256};\n", "     use tokio::sync::mpsc::{channel, Receiver};\n", " \n", "     #[derive(<PERSON><PERSON>)]\n", "@@ -835,6 +847,8 @@ pub mod tests {\n", "                 .name,\n", "             model_name\n", "         );\n", "+        let hashed_model_name = hex::encode(Sha256::digest(model_name.as_bytes()));\n", "+        assert!(registry.get_model(&hashed_model_name).await.is_ok());\n", "     }\n", " \n", "     #[actix_web::test]diff --git a/tools/feature_flags/flags.yaml b/tools/feature_flags/flags.yaml\n", "index e8b9ff183..8f4c53138 100644\n", "--- a/tools/feature_flags/flags.yaml\n", "+++ b/tools/feature_flags/flags.yaml\n", "@@ -130,8 +130,9 @@ chat_model:\n", "         - {tenant_name: dogfood-shard, return_value: claude-sonnet-3-5-16k-v4-chat}\n", "         - return_value: \"claude-sonnet-3-5-16k-v3-chat\"\n", " additional_chat_models:\n", "+  # 3c11005cfc8519710830f3083e864ef82866c11bf9a69227ba3a7aed7427d4d0 -> claude-sonnet-3-5-128k-chat\n", "   sync: true\n", "-  description: \"Add secondary models to chat\"\n", "+  description: \"Add secondary models to chat - PUBLIC - use sha256 hash of model name\"\n", "   default_return_value: \"\"\n", "   envs:\n", "     production:\n", "@@ -139,9 +140,10 @@ additional_chat_models:\n", "         - namespace:\n", "             - dogfood\n", "             - shv-staging\n", "-          return_value: \"{'Overview': 'claude-sonnet-3-5-128k-chat', 'Default': null}\"\n", "-        - {tenant_name: dogfood-shard, return_value: \"{'Overview': 'claude-sonnet-3-5-128k-chat', 'Default':\n", "-            null}\"}\n", "+          return_value: \"{'Overview': '3c11005cfc8519710830f3083e864ef82866c11bf9a69227ba3a7aed7427d4d0',\n", "+            'Default': null}\"\n", "+        - {tenant_name: dogfood-shard, return_value: \"{'Overview': '3c11005cfc8519710830f3083e864ef82866c11bf9a69227ba3a7aed7427d4d0',\n", "+            'Default': null}\"}\n", "         - return_value: \"\"\n", " enable_chat:\n", "   sync: true\n", "\n", "\n", "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n", "\n", "\n", "Commit message example 0:\n", "Add metrics to slack api (#14256)\n", "\n", "\n", "\n", "\n", "Commit message example 1:\n", "github-processor: augmentignore support for UploadDiff (#14057)\n", "\n", "Also handle .augmentignore files when uploading diffs. We check if the\n", "repo has an augment<PERSON>ore file, and if so we add all files in that to\n", "the exclude list when applying the patch via git. If the augmentignore\n", "file itself changes, we reupload the repo from scratch.\n", "\n", "One downside is that for repos with no augmentignore file, we will waste\n", "a DownloadContent API call to GitHub on every push we process. We could\n", "reduce this with an in-memory cache if this turns out to be a problem?\n", "\n", "I will have to rework all of this in a follow up anyway, to correctly\n", "handle augmentignore files in subdirectories, so leaving the flag in\n", "that only enables the new behavior for unit tests.\n", "\n", "\n", "Commit message example 2:\n", "Make share_proto_py_proto visible to base/datasets (#14326)\n", "\n", "# Changes\n", "* As title, similar to https://github.com/augmentcode/augment/pull/13124\n", "* Add a comment + TODO so that people stop tripping on this in the short-term.\n", "\n", "# Tests\n", "CI/CD\n", "\n", "\n", "Commit message example 3:\n", "preemptively scale up inference host replicas for launch (#14325)\n", "\n", "Adds 4 additional inference hosts for completion main model. 16\n", "additional GPUs needed. This should scale to about 3k daily active\n", "users. Possibly more.\n", "\n", "![Screenshot 2024-10-22 at 4 20 03\n", "PM](https://github.com/user-attachments/assets/87fd407e-f5b3-411a-a321-8aa3b17c5a2a)\n", "\n", "\n", "Commit message example 4:\n", "v2: github-processor: augmentignore support for RegisterRepos (#14294)\n", "\n", "Try submitting this again after confirming that e2e tests still pass\n", "with this change.\n", "\n", "Add augmentignore support for RegisterRepos, but gated behind a flag.\n", "Right now the flag is only enabled in unit tests. Once diffs also\n", "support augment<PERSON>ore, we can remove the flag.\n", "\n", "Also refactored the unit tests a little in an attempt to factor out some\n", "common test setup\n", "\n", "\n", "Commit message example 5:\n", "Properly handle missing expected blob names. (#14312)\n", "\n", "Due to an issue with the front end default behavior, the front end will only send non-empty `expected_blob_name` in replacement_texts when `enableDebugFeatures` are true. Hence most of our users' extensions are not sending in any expected_blob_names. In the meantime, there is a bug in the backend that checks `expected_blob_name` against `None` instead of checking if it's an empty string. So these empty blob names are not considered as missing but instead used to check if reconstruction has succeeded, which it never will, causing index-to-current updates to fail.\n", "\n", "This PR also improves the logging in `_process_recent_changes` to further improve the visibility of other types of reconstruction failures since they are crucial to model quality.\n", "\n", "\n", "Commit message example 6:\n", "Always send expected blob names with recent changes (#14314)\n", "\n", "When I first added these in #10000 (yay!), we decided to guard this\n", "behind debug features in case it caused poor performance. We haven't had\n", "any complaints in dogfood, so it's presumably safe to remove it.\n", "\n", "On the other hand, next edit relies on this behavior pretty heavily, as\n", "it needs these names to reconstruct files. Not having them seems like it\n", "often leads to bad prompts and bad responses. This is already affecting\n", "Vanguard and we'll (hopefully) soon launch even more, so we should fix\n", "this.\n", "\n", "\n", "Commit message example 7:\n", "Truncate change descriptions in the frontend (#14318)\n", "\n", "Currently the backend truncates change descriptions. However, now that\n", "we're experimenting with replacing some right text with bottom text, we\n", "should move that truncation into the frontend. This allows us to\n", "truncate right text more than bottom text (since the latter has more\n", "space than the former).\n", "\n", "Once we make this change, we can modify the backend so it stops\n", "truncating.\n", "\n", "\n", "Commit message example 8:\n", "Add back-end feature flags for syncing permission parameters (#14243)\n", "\n", "This PR adds back-end feature flags for the parameters that control folder syncing permissions in the vscode extension. It also adds unit tests to verify that the WorkspaceManager honors them correctly.\n", "\n", "\n", "Commit message example 9:\n", "Add tenant for brainvoy (#14276)\n", "\n", "\n", "\n", "\n", "Commit message example 10:\n", "Early exit and don't log when there are no edit events. (#14319)\n", "\n", "We currently produce noisy `[undefined/undefined]` logs when the user\n", "has no edit events. This is caused because `next-edit-api.ts` returns\n", "early with a ok status, but `request-manager-impl.ts` falsely assumes\n", "that any ok response also has a suggestion.\n", "\n", "We also avoid reporting client metrics in this case.\n", "\n", "\n", "Commit message example 11:\n", "switching render order of summary and actions (#14316)\n", "\n", "The summary overrode the priority of the /actions as a card, thereby not\n", "allowing the /actions to show up anymore once a summary was shown. We\n", "should probably separate this logic further. Show summary also should\n", "not need to be an action now that we don't show the button to show the\n", "summaries anymore. Will shift it to a webview message in a future PR.\n", "\n", "Below is a demo showing /actions back in... /action\n", "\n", "\n", "https://github.com/user-attachments/assets/c493eee9-c163-4e32-8615-35c00913e582\n", "\n", "Co-authored-by: <PERSON><PERSON> <<EMAIL>>\n", "\n", "\n", "Commit message example 12:\n", "AU-5017 Add Analytics tables for Share Service (#14260)\n", "\n", "Add RI Analytics tables for the Share service events.\n", "\n", "\n", "Commit message example 13:\n", "Give <PERSON> GCP access (#14296)\n", "\n", "So he can deploy tenant manager into a dev namespace\n", "and troubleshoot it.\n", "\n", "\n", "Commit message example 14:\n", "Add flag to use pull instead of streaming pull (#14273)\n", "\n", "Flag must be set at content manager startup but it's a dynamic\n", "feature flag.\n", "\n", "Testing done: ran manually in my dev deploy with pull size == 16,\n", "indexing augment, saw many fewer expired acks\n", "\n", "\n", "Commit message example 15:\n", "AU-4872 Integrating RI into service (#14164)\n", "\n", "Add RequestInsight events for GetChat and SaveChat, the public api for sharing chat conversations.\n", "\n", "GetChat and SaveChat will show up under `Share` events on the support page. You have to examine the \"Raw\" tab to see the content of the events. There is not yet a friendly view of these events.\n", "\n", "This change does not include RI analytics handling.\n", "\n", "Additional testing:\n", "`bazel run //services/share/test:share_test`\n", "`bazel run //services/share/server:server_test`\n", "\n", "\n", "Commit message example 16:\n", "github state: Don't log full blobs (#14313)\n", "\n", "This is spammy, the number of added/deleted blobs is enough, we don't\n", "need the bytes in our logs.\n", "\n", "Also up the retry counts for the github e2e test, in case processing +\n", "indexing take a while. Hopefully this makes the github test less flaky.\n", "\n", "\n", "Commit message example 17:\n", "Add Github promotion in Slackbot messages (#14255)\n", "\n", "Add a message encouraging users to install the Github app if they\n", "don't already have it installed.\n", "\n", "\n", "Commit message example 18:\n", "[Fastforward] Output scales for fp8 linear layers (#13844)\n", "\n", "This PR is a resuscitation of #11778. As before, the goal is to make\n", "scale factors available for operations that consume the output from an\n", "fp8 matmul, but don't themselves have a module representation to store\n", "that scale factor. (The most obvious application is fp8 all_reduce.)\n", "\n", "The main update here is that we now allow checkpoints to _not_ have\n", "these scale factors present. Currently we don't use the scale factors\n", "anywhere, so models will have 1s in the relevant weights.\n", "\n", "\n", "Commit message example 19:\n", "Add metrics to the github api (#14250)\n", "\n", "\n", "\n", "\n", "Commit message example 20:\n", "exps (#14307)\n", "\n", "\n", "\n", "\n", "Commit message example 21:\n", "single turn code instructions (#14108)\n", "\n", "<div class='graphite__hidden'>\n", "          <div>🎥 Video uploaded on Graphite:</div>\n", "            <a href=\"https://app.graphite.dev/media/video/n9Z10DvIGrAl9QlvPp49/d8e52f30-8617-4328-8a3a-3984fd176610.mp4\">\n", "              <img src=\"https://app.graphite.dev/api/v1/graphite/video/thumbnail/n9Z10DvIGrAl9QlvPp49/d8e52f30-8617-4328-8a3a-3984fd176610.mp4\">\n", "            </a>\n", "          </div>\n", "<video src=\"https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/n9Z10DvIGrAl9QlvPp49/d8e52f30-8617-4328-8a3a-3984fd176610.mp4\">Export-1729291285202.mp4</video>\n", "\n", "<div class='graphite__hidden'>\n", "          <div>🎥 Video uploaded on Graphite:</div>\n", "            <a href=\"https://app.graphite.dev/media/video/n9Z10DvIGrAl9QlvPp49/e0c8d931-d8fc-4bd6-926d-959fa2a91e70.mp4\">\n", "              <img src=\"https://app.graphite.dev/api/v1/graphite/video/thumbnail/n9Z10DvIGrAl9QlvPp49/e0c8d931-d8fc-4bd6-926d-959fa2a91e70.mp4\">\n", "            </a>\n", "          </div>\n", "<video src=\"https://graphite-user-uploaded-assets-prod.s3.amazonaws.com/n9Z10DvIGrAl9QlvPp49/e0c8d931-d8fc-4bd6-926d-959fa2a91e70.mp4\">DiffViewKeybindingUpdate.mp4</video>\n", "\n", "\n", "Commit message example 22:\n", "[Chat] File resolution non-blocking (#14261)\n", "\n", "Makes the file resolution in chat requests non-blocking\n", "\n", "\n", "Commit message example 23:\n", "Reduce debounce to 300ms. (#14300)\n", "\n", "We now have a better handle on how much debounce affects latency and\n", "load, and think we are in a better place to drop debounce to 300ms.\n", "\n", "\n", "Commit message example 24:\n", "add tenant for plutis (#14301)\n", "\n", "\n", "\n", "\n", "Commit message example 25:\n", "Add slash commands to context menu - AU-5033 (#14289)\n", "\n", "When the user selects multiple lines, the context menu now has a new\n", "\"Send to Augment\" submenu which contains the existing slash commands to\n", "open the chat with the selection.\n", "\n", "This uses the existing `SlashActionCommand` classes and VS Code passes\n", "in a ` Uri` instead of `Diagnostic[]` so a helper function was added to\n", "assist with getting the diagnostics in the selection.\n", "\n", "\n", "https://github.com/user-attachments/assets/c4a466d8-18e3-483a-9969-9c0333e9c92b\n", "\n", "\n", "Commit message example 26:\n", "Tweak next_edit_bg client metrics to be more helpful (#14297)\n", "\n", "- Count \"all of the locations, but fewer than 4\" toward the sufficient_noops bucket\n", "- If we reached either a change or sufficient_noops don't count toward error / cancel\n", "\n", "\n", "Commit message example 27:\n", "Move api proxy feature flags to generated code (#14295)\n", "\n", "This is part of an effort to reduce the work needed\n", "to wire dynamic feature flags to the client.\n", "\n", "\n", "Commit message example 28:\n", "[Chat] Maintain focus on new convo (#14283)\n", "\n", "Maintains focus on input when creating a new conversation \n", "\n", "Fixes\n", "https://linear.app/augmentcode/issue/AU-5012/move-focus-to-next-field-after-creating-new-chat\n", "\n", "\n", "\n", "https://github.com/user-attachments/assets/e6cf471a-151a-4712-add8-bb4a3f901d98\n", "\n", "\n", "Commit message example 29:\n", "Add misc configs (#14298)\n", "\n", "\n", "\n", "\n", "Commit message example 30:\n", "Pass diagnostics, not just messages, to /command web views (#14232)\n", "\n", "After this change, the `/command` system now passes `Diagnostic`s, not just diagnostic message of type `string` to the web view, which enables potential more informative prompts for `/command`s.\n", "\n", "\n", "Commit message example 31:\n", "some minor fixes to let spark work on gcp-us1 (#14220)\n", "\n", "Some minor updates to k8s_session to work on gcp-us1\n", "- make sure it can find the `spark_env` mounted drive and transfer\n", "dependencies\n", "- mount all the drives in `node_mounts.yaml` for the environment on gcp\n", "and turn on gcs fuse in the executor pod template.\n", "- behavior on cw should be unchanged.\n", "\n", "\n", "Above are all the commit message examples.\n", "\n", "\n", "\n"]}], "source": ["print(prompts[\"default\"])"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [{"data": {"text/plain": ["5650"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["token_counter.count_tokens(prompts[\"default\"])"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["V1_SYSTEM_PROMPT = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up!\"\"\""]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["PREV_SYSTEM_PROMPT = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "- When seeing both feature changes and documentation/style/refactoring changes, emphasize the feature changes.\n", "- Mimick the length and style of the example commit messages the user provides, e.g. whether using bullets, numbered lists, or paragraphs. Do not be overly concise (e.g. always producing a single line).\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up! Reply with the commit message and the commit message ONLY, WITH NO EXTRA TEXT.\"\"\""]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "- When seeing both feature changes and documentation/style/refactoring changes, emphasize the feature changes.\n", "- Mimick the length and style of the example commit messages the user provides, e.g. whether using bullets, numbered lists, or paragraphs. Do not be overly concise (e.g. always producing a single line).\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up! Reply with the commit message and the commit message ONLY, WITH NO EXTRA TEXT.\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["05b638e99ca959e4e41a61de34269f55ea1d7fb3\n"]}], "source": ["print(commit[\"current_hash\"])"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AU-4973 - get-models - all model names are sha256 to obfuscate internals (#14322)\n", "\n", "To get this out quickly before launch, `get_model` in `model_registry`\n", "will search by either name or hash for now. We can refine this behavior\n", "later as needed\n", "\n", "Verified on dev that basic functionality (completions, next edit, chat)\n", "still works with obfuscated names\n", "\n", "![Screenshot 2024-10-22 at 15 49\n", "43](https://github.com/user-attachments/assets/339f602a-b859-4eb1-8735-d5983f2428d4)\n"]}], "source": ["print(commit[\"message\"])"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================== default ====================\n", "Hash model names in GetModels response\n", "\n", "- Modify GetModels handler to hash model names using SHA256\n", "- Update ModelRegistry to support lookup by both name and hash\n", "- Adjust feature flag for additional chat models to use hashed names\n", "- Update tests to account for hashed model names\n", "\n", "==================== summary_v2 ====================\n", "Hash model names for privacy and add support for hashed model lookups\n", "\n", "- Implement SHA256 hashing of model names in GetModels response\n", "- Update ModelRegistry to support lookups by both model name and hash\n", "- Modify feature flag to use hashed model name for additional chat models\n", "- Add unit tests to verify hashed model lookup functionality\n", "\n", "==================== trailing_instructions ====================\n", "Hash model names in GetModels response\n", "\n", "Add SHA256 hashing for model names in the GetModels API response to obscure actual model names. Update ModelRegistry to support lookup by both original and hashed names. Adjust tests and feature flags to use hashed model names.\n", "\n", "==================== summary_v2_trailing_instructions ====================\n", "Hash model names in GetModels response\n", "\n", "Add SHA256 hashing for model names in the GetModels API response to obscure actual model names. Update ModelRegistry to support lookup by both original and hashed names. Adjust tests and feature flags to use hashed model names.\n", "\n"]}], "source": ["texts = {}\n", "responses = {}\n", "for key, prompt in prompts.items():\n", "    text, response = run_claude(prompt, SYSTEM_PROMPT)\n", "    texts[key] = text\n", "    responses[key] = response\n", "    print(\"==\" * 10, key, \"==\" * 10)\n", "    print(text)\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
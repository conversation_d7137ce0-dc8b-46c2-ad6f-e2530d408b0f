{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.git_structure import GitStructure\n", "\n", "git_structure = GitStructure.from_json(\n", "    \"/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json\"\n", ")\n", "tree_list = git_structure.get_tree_as_list(ratio_threshold=0.3)\n", "print(len(tree_list))\n", "tree_list\n", "# [node[\"path\"] for node in tree_list]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "git_structure_paths = [\n", "    \"/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_angular/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_atom/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_beauty_net/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_doomemacs/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_linux/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_openbsd/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_pytorch/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_sqlite/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_wine/git_structure_v1.json\",\n", "]\n", "thresholds = [0.0, 0.1, 0.2, 0.3, 0.35, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]\n", "\n", "results = []\n", "\n", "for threshold in thresholds:\n", "    for path in git_structure_paths:\n", "        git_structure = GitStructure.from_json(path)\n", "        tree_list = git_structure.get_tree_as_list_v1(\n", "            ratio_threshold=threshold,\n", "            add_parent_paths=False,\n", "        )\n", "\n", "        # Extract the project name from the path\n", "        project_name = path.split(\"/\")[-2].split(\".\")[0]\n", "\n", "        # Append the result to our list\n", "        results.append(\n", "            {\n", "                \"Project\": project_name,\n", "                \"Line Threshold\": threshold,\n", "                \"Tree List Length\": len(tree_list),\n", "                \"Tree List\": tree_list,\n", "            }\n", "        )\n", "\n", "df = pd.DataFrame(results)\n", "pivot_df = df.pivot(\n", "    index=\"Project\", columns=\"Line Threshold\", values=\"Tree List Length\"\n", ")\n", "pivot_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_name = \"angular\"\n", "project_name = f\"commit_msgs_{repo_name}\" if repo_name else \"commit_msgs\"\n", "for result in results:\n", "    show = True\n", "    # if result[\"Project\"] != project_name:\n", "    #     show = False\n", "    if result[\"Line Threshold\"] != 0.7:\n", "        show = False\n", "    if show:\n", "        print(result[\"Project\"])\n", "        print(result[\"Tree List\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "git_structure_paths = [\n", "    \"/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_angular/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_atom/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_beauty_net/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_doomemacs/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_linux/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_openbsd/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_pytorch/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_sqlite/git_structure_v1.json\",\n", "    \"/home/<USER>/zhuoran/commit_msgs_wine/git_structure_v1.json\",\n", "]\n", "node_limits = [16, 32]\n", "\n", "results = []\n", "\n", "for node_limit in node_limits:\n", "    for path in git_structure_paths:\n", "        git_structure = GitStructure.from_json(path)\n", "        tree_list = git_structure.get_tree_as_list_v1(\n", "            node_limit=node_limit,\n", "            node_ratio_threshold=0.01,\n", "            max_level=2,\n", "            # add_parent_paths=False,\n", "        )\n", "\n", "        # Extract the project name from the path\n", "        project_name = path.split(\"/\")[-2].split(\".\")[0]\n", "\n", "        # Append the result to our list\n", "        results.append(\n", "            {\n", "                \"Project\": project_name,\n", "                \"Node Limit\": node_limit,\n", "                \"Tree List Length\": len(tree_list),\n", "                \"Tree List\": tree_list,\n", "            }\n", "        )\n", "\n", "df = pd.DataFrame(results)\n", "pivot_df = df.pivot(index=\"Project\", columns=\"Node Limit\", values=\"Tree List Length\")\n", "pivot_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_name = \"wine\"\n", "project_name = f\"commit_msgs_{repo_name}\" if repo_name else \"commit_msgs\"\n", "for result in results:\n", "    show = True\n", "    if result[\"Project\"] != project_name:\n", "        show = False\n", "    # if result[\"Node Limit\"] != 16:\n", "    #     show = False\n", "    if show:\n", "        total_ratio = 0\n", "        print(result[\"Project\"], result[\"Node Limit\"])\n", "        for path, ratio in sorted(\n", "            result[\"Tree List\"], key=lambda x: x[1], reverse=True\n", "        ):\n", "            print(f\"{ratio:.2%}: {path}\")\n", "            total_ratio += ratio\n", "        print(f\"{total_ratio:.2%}: total\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["git_structure.root.children"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["git_structure.root.children[\"experimental\"].children"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["git_structure.root.children[\"experimental\"].children[\"zhuoran\"].children"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(git_structure.root.children[\"experimental\"].children[\"zhuoran\"].ratio)\n", "for child in (\n", "    git_structure.root.children[\"experimental\"].children[\"zhuoran\"].children.values()\n", "):\n", "    print(child.ratio)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(git_structure.root.children[\"experimental\"].children[\"zhuoran\"].count)\n", "for child in (\n", "    git_structure.root.children[\"experimental\"].children[\"zhuoran\"].children.values()\n", "):\n", "    print(child.count)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.commit_msgs.git_structure import GitStructureExporter\n", "\n", "exporter = GitStructureExporter(\n", "    \"/home/<USER>/augment/\",\n", "    2,\n", "    \"file\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["api_tracked_files = exporter.api_tracked_files\n", "subprocess_tracked_files = exporter.subprocess_tracked_files\n", "\n", "len(api_tracked_files), len(subprocess_tracked_files)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for api_tracked_file, subprocess_tracked_file in zip(\n", "    sorted(api_tracked_files), sorted(subprocess_tracked_files)\n", "):\n", "    if api_tracked_file != subprocess_tracked_file:\n", "        print(type(api_tracked_file), type(subprocess_tracked_file))\n", "        print(api_tracked_file)\n", "        print(subprocess_tracked_file)\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from difflib import get_close_matches\n", "\n", "pass_count = 0\n", "fail_count = 0\n", "close_matches = []\n", "for api_tracked_file in api_tracked_files:\n", "    if api_tracked_file not in subprocess_tracked_files:\n", "        print(f\"File not in subprocess_tracked_files: {api_tracked_file}\")\n", "        closest_match = get_close_matches(\n", "            api_tracked_file, subprocess_tracked_files, n=1, cutoff=0.6\n", "        )\n", "        if closest_match:\n", "            print(f\"Closest match: {closest_match[0]}\")\n", "            close_matches.append((api_tracked_file, closest_match[0]))\n", "        else:\n", "            print(\"No close match found\")\n", "        print()  # Add a blank line for better readability\n", "        fail_count += 1\n", "    else:\n", "        pass_count += 1\n", "\n", "pass_count, fail_count"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"research/gpt-neox/augment_configs/codegen-H/lr/8e−5.yml\" == \"research/gpt-neox/augment_configs/codegen-H/lr/8e−5.yml\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for c0, c1 in zip(\n", "    close_matches[0][0],\n", "    close_matches[0][1],\n", "):\n", "    if c0 != c1:\n", "        print(c0, c1)\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f\"\\\\u{ord(c0):04x}\", f\"\\\\u{ord(c1):04x}\""]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
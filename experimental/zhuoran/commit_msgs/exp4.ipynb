{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "gcloud auth login\n", "gcloud auth application-default login\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-11-24 07:25:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["```python\n", "print(\"Hello, <PERSON>!\")\n", "```\n", "\n", "Streaming:\n", "```python\n", "print\n", "(\"Hello, <PERSON><PERSON>l\n", "d!\")\n", "```\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "# MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "vertex_ai_client = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "\n", "def run_claude(message_or_prompt, system_message):\n", "    if isinstance(message_or_prompt, str):\n", "        message = message_or_prompt\n", "    else:\n", "        message = message_or_prompt.message\n", "        system_message = message_or_prompt.system_prompt\n", "    response = vertex_ai_client.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    )\n", "    return response.content[0].text, response\n", "\n", "\n", "def run_claude_stream(message, system_message):\n", "    with vertex_ai_client.client.messages.stream(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    ) as stream:\n", "        for text in stream.text_stream:\n", "            yield text\n", "\n", "\n", "text, response = run_claude(\n", "    \"Write a hello world in Python. Return a code block only.\", \"\"\n", ")\n", "print(text)\n", "print()\n", "\n", "print(\"Streaming:\")\n", "for text in run_claude_stream(\n", "    \"Write a hello world in Python. Return a code block only.\", \"\"\n", "):\n", "    print(text)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'augment_recent': 12500,\n", " 'augment': 12500,\n", " 'angular': 21072,\n", " 'pytorch': 10276,\n", " 'linux': 1000,\n", " 'wine': 6700,\n", " 'beauty_net': 178,\n", " 'spark': 6480}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from base.prompt_format_chat.lib.token_counter import RoughTokenCounter\n", "from experimental.zhuoran.commit_msgs.git_history import MultiGitHistory\n", "\n", "token_counter = RoughTokenCounter()\n", "mgh = MultiGitHistory.from_jsons(\n", "    {\n", "        \"augment_recent\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/augment/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/augment/eval_set_v1_new_keys.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/augment/git_structure_v1.json\",\n", "            \"/home/<USER>/augment/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"augment\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/augment/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/augment/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/augment/git_structure_v1.json\",\n", "            \"/home/<USER>/augment/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"angular\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/angular/commits_v4.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/angular/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/angular/git_structure_v1.json\",\n", "            \"/home/<USER>/angular/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"pytorch\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/pytorch/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/pytorch/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/pytorch/git_structure_v1.json\",\n", "            \"/home/<USER>/pytorch/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"linux\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/linux/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/linux/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/linux/git_structure_v1.json\",\n", "            \"/home/<USER>/linux/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"wine\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/wine/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/wine/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/wine/git_structure_v1.json\",\n", "            \"/home/<USER>/wine/\",\n", "            \"utf-16\",\n", "        ),\n", "        \"beauty_net\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/beauty_net/commits_v4.json\",\n", "            \"/mnt/efs/augment/user/zhuoran/commit_msgs/beauty_net/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/beauty_net/git_structure_v1.json\",\n", "            \"/home/<USER>/beauty-net/\",\n", "            \"utf-8\",\n", "        ),\n", "        \"spark\": (\n", "            \"/home/<USER>/zhuoran/commit_msgs/spark/commits_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/spark/eval_set_v5.json\",\n", "            \"/home/<USER>/zhuoran/commit_msgs/spark/git_structure_v1.json\",\n", "            \"/home/<USER>/spark/\",\n", "            \"utf-16\",\n", "        ),\n", "    },\n", "    token_counter.count_tokens,\n", ")\n", "mgh.get_lengths()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["augment_recent:\n", "    largest_diff: 5\n", "    smallest_diff: 5\n", "    .github: 30\n", "    .vscode: 15\n", "    base: 30\n", "    clients: 30\n", "    data: 5\n", "    deploy: 30\n", "    experimental: 30\n", "    models: 30\n", "    research: 30\n", "    services: 30\n", "    third_party: 30\n", "    tools: 30\n", "augment:\n", "    largest_diff: 30\n", "    smallest_diff: 30\n", "    .github: 30\n", "    .vscode: 15\n", "    base: 30\n", "    clients: 30\n", "    data: 30\n", "    deploy: 30\n", "    experimental: 30\n", "    models: 30\n", "    research: 30\n", "    services: 30\n", "    third_party: 30\n", "    tools: 30\n", "angular:\n", "    largest_diff: 30\n", "    smallest_diff: 30\n", "    .buildkite: 30\n", "    .<PERSON>ci: 30\n", "    .codefresh: 29\n", "    .devcontainer: 16\n", "    .github: 30\n", "    .husky: 11\n", "    .ng-dev: 30\n", "    .vscode: 30\n", "    .yarn: 12\n", "    adev: 30\n", "    aio: 30\n", "    contributing-docs: 21\n", "    cypress: 30\n", "    dev-infra: 30\n", "    devtools: 30\n", "    docs: 30\n", "    e2e: 10\n", "    goldens: 30\n", "    integration: 30\n", "    modules: 30\n", "    packages: 30\n", "    projects: 30\n", "    scripts: 30\n", "    src: 30\n", "    third_party: 30\n", "    tools: 30\n", "pytorch:\n", "    largest_diff: 30\n", "    smallest_diff: 30\n", "    .ci: 30\n", "    .<PERSON>ci: 30\n", "    .devcontainer: 3\n", "    .github: 30\n", "    .vscode: 2\n", "    android: 11\n", "    aten: 30\n", "    benchmarks: 30\n", "    binaries: 30\n", "    c10: 30\n", "    caffe2: 30\n", "    cmake: 30\n", "    docs: 30\n", "    functorch: 30\n", "    ios: 21\n", "    modules: 30\n", "    mypy_plugins: 3\n", "    scripts: 30\n", "    test: 30\n", "    third_party: 30\n", "    tools: 30\n", "    torch: 30\n", "    torchgen: 30\n", "linux:\n", "    largest_diff: 30\n", "    smallest_diff: 30\n", "    Documentation: 30\n", "    arch: 30\n", "    block: 15\n", "    crypto: 30\n", "    drivers: 30\n", "    fs: 30\n", "    include: 30\n", "    init: 4\n", "    io_uring: 13\n", "    kernel: 30\n", "    lib: 30\n", "    mm: 30\n", "    net: 30\n", "    rust: 9\n", "    scripts: 21\n", "    security: 25\n", "    sound: 30\n", "    tools: 30\n", "    virt: 5\n", "wine:\n", "    largest_diff: 30\n", "    smallest_diff: 30\n", "    dlls: 30\n", "    documentation: 30\n", "    fonts: 1\n", "    include: 30\n", "    libs: 30\n", "    loader: 30\n", "    nls: 10\n", "    po: 30\n", "    programs: 30\n", "    server: 30\n", "    tools: 30\n", "beauty_net:\n", "    largest_diff: 30\n", "    smallest_diff: 30\n", "    .github: 2\n", "    beauty: 30\n", "    scripts: 8\n", "spark:\n", "    largest_diff: 30\n", "    smallest_diff: 30\n", "    .github: 30\n", "    R: 30\n", "    assembly: 22\n", "    bin: 9\n", "    binder: 11\n", "    build: 8\n", "    common: 30\n", "    conf: 4\n", "    connect: 30\n", "    connector: 30\n", "    core: 30\n", "    data: 5\n", "    dev: 30\n", "    docs: 30\n", "    examples: 30\n", "    graphx: 30\n", "    hadoop-cloud: 30\n", "    launcher: 30\n", "    licenses: 8\n", "    licenses-binary: 16\n", "    mllib: 30\n", "    mllib-local: 29\n", "    project: 30\n", "    python: 30\n", "    repl: 30\n", "    resource-managers: 30\n", "    sbin: 14\n", "    sql: 30\n", "    streaming: 30\n", "    tools: 8\n", "    ui-test: 9\n"]}], "source": ["mgh.print_eval_sets()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Eval set stats for retrieval"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# augment_recent:\n", "#     largest_diff: 5\n", "#     smallest_diff: 5\n", "#     .github: 30\n", "#     .vscode: 15\n", "#     base: 30\n", "#     clients: 30\n", "#     data: 5\n", "#     deploy: 30\n", "#     experimental: 30\n", "#     models: 30\n", "#     research: 30\n", "#     services: 30\n", "#     third_party: 30\n", "#     tools: 30\n", "# augment:\n", "#     largest_diff: 30\n", "#     smallest_diff: 30\n", "#     .github: 30\n", "#     .vscode: 15\n", "#     base: 30\n", "#     clients: 30\n", "#     data: 30\n", "#     deploy: 30\n", "#     experimental: 30\n", "#     models: 30\n", "#     research: 30\n", "#     services: 30\n", "#     third_party: 30\n", "#     tools: 30\n", "# angular:\n", "#     largest_diff: 30\n", "#     smallest_diff: 30\n", "#     .buildkite: 30\n", "#     .<PERSON>ci: 30\n", "#     .codefresh: 29\n", "#     .devcontainer: 16\n", "#     .github: 30\n", "#     .husky: 11\n", "#     .ng-dev: 30\n", "#     .vscode: 30\n", "#     .yarn: 12\n", "#     adev: 30\n", "#     aio: 30\n", "#     contributing-docs: 21\n", "#     cypress: 30\n", "#     dev-infra: 30\n", "#     devtools: 30\n", "#     docs: 30\n", "#     e2e: 10\n", "#     goldens: 30\n", "#     integration: 30\n", "#     modules: 30\n", "#     packages: 30\n", "#     projects: 30\n", "#     scripts: 30\n", "#     src: 30\n", "#     third_party: 30\n", "#     tools: 30\n", "# pytorch:\n", "#     largest_diff: 30\n", "#     smallest_diff: 30\n", "#     .ci: 30\n", "#     .<PERSON>ci: 30\n", "#     .devcontainer: 3\n", "#     .github: 30\n", "#     .vscode: 2\n", "#     android: 11\n", "#     aten: 30\n", "#     benchmarks: 30\n", "#     binaries: 30\n", "#     c10: 30\n", "#     caffe2: 30\n", "#     cmake: 30\n", "#     docs: 30\n", "#     functorch: 30\n", "#     ios: 21\n", "#     modules: 30\n", "#     mypy_plugins: 3\n", "#     scripts: 30\n", "#     test: 30\n", "#     third_party: 30\n", "#     tools: 30\n", "#     torch: 30\n", "#     torchgen: 30\n", "# linux:\n", "#     largest_diff: 30\n", "#     smallest_diff: 30\n", "#     Documentation: 30\n", "#     arch: 30\n", "#     block: 15\n", "#     crypto: 30\n", "#     drivers: 30\n", "#     fs: 30\n", "#     include: 30\n", "#     init: 4\n", "#     io_uring: 13\n", "#     kernel: 30\n", "#     lib: 30\n", "#     mm: 30\n", "#     net: 30\n", "#     rust: 9\n", "#     scripts: 21\n", "#     security: 25\n", "#     sound: 30\n", "#     tools: 30\n", "#     virt: 5\n", "# wine:\n", "#     largest_diff: 30\n", "#     smallest_diff: 30\n", "#     dlls: 30\n", "#     documentation: 30\n", "#     fonts: 1\n", "#     include: 30\n", "#     libs: 30\n", "#     loader: 30\n", "#     nls: 10\n", "#     po: 30\n", "#     programs: 30\n", "#     server: 30\n", "#     tools: 30\n", "# beauty_net:\n", "#     largest_diff: 30\n", "#     smallest_diff: 30\n", "#     .github: 2\n", "#     beauty: 30\n", "#     scripts: 8\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Core eval sets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Improvements\n", "    - Large\n", "        - Augment (recent), experimental, 0\n", "        - Augment (recent), largest, 4\n", "        - Angular, largest, 0\n", "    - Factuality\n", "        - Augment (recent), smallest, 3\n", "    - Style\n", "        - Angular, packages, 0\n", "        - Wine, server, 1\n", "        - BeautyNet, beauty, 0\n", "- Bad\n", "    - Large\n", "        - Augment (recent), largest, 0\n", "    - Factuality\n", "        - Augment (recent), services, 0\n", "        - Linux, MM, 0\n", "    - Fixes vs. improvements/refactors\n", "        - Angular, packages, 3\n", "    - Style\n", "        - Wine, server, 7\n", "        - Linux, init, 2"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Angular\tLargest\t2\n", "# Angular\tPackages\t1\n", "# Angular\tPackages\t2\n", "# Angular\tPackages\t3\n", "# Angular\tPackages\t4\n", "# PyTorch\tDocs\t0\n", "# PyTorch\tTorch\t0\n", "# PyTorch\tTorch\t1\n", "# PyTorch\tTorch\t2\n", "# PyTorch\tTorch\t3\n", "# Linux\tLargest\t2\n", "# Linux\tKernel\t0\n", "# Linux\tMM\t0\n", "# Linux\tInit\t2\n", "# Wine\tServer\t1\n", "# Wine\tServer\t5\n", "# Wine\tServer\t7\n", "# BeautyNet\tBeauty\t0\n", "# Augment (recent)\tModels\t1\n", "# Augment (recent)\tExperimental\t0\n", "# Augment (recent)\tTools\t3\n", "# Augment (recent)\tSmallest\t3\n", "# Augment (recent)\tLargest\t4\n", "# Augment (recent)\tLargest\t0\n", "# Augment (recent)\tServices\t0\n", "core_eval_set = [\n", "    (\"angular\", \"largest_diff\", 2),\n", "    (\"angular\", \"packages\", 1),\n", "    (\"angular\", \"packages\", 2),\n", "    (\"angular\", \"packages\", 3),\n", "    (\"angular\", \"packages\", 4),\n", "    (\"pytorch\", \"docs\", 0),\n", "    (\"pytorch\", \"torch\", 0),\n", "    (\"pytorch\", \"torch\", 1),\n", "    (\"pytorch\", \"torch\", 2),\n", "    (\"pytorch\", \"torch\", 3),\n", "    (\"linux\", \"largest_diff\", 2),\n", "    (\"linux\", \"kernel\", 0),\n", "    (\"linux\", \"mm\", 0),\n", "    (\"linux\", \"init\", 2),\n", "    (\"wine\", \"server\", 1),\n", "    (\"wine\", \"server\", 5),\n", "    (\"wine\", \"server\", 7),\n", "    (\"beauty_net\", \"beauty\", 0),\n", "    (\"augment_recent\", \"models\", 1),\n", "    (\"augment_recent\", \"experimental\", 0),\n", "    (\"augment_recent\", \"tools\", 3),\n", "    (\"augment_recent\", \"smallest_diff\", 3),\n", "    (\"augment_recent\", \"largest_diff\", 4),\n", "    (\"augment_recent\", \"largest_diff\", 0),\n", "    (\"augment_recent\", \"services\", 0),\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Individual eval"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["enable_similar_commits = True\n", "enable_relevant_messages = True"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["repo_name = \"angular\"\n", "# mgh.print_eval_set(repo_name)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["eval_set = mgh.get_eval_set(repo_name)\n", "assert eval_set\n", "example = eval_set[\"largest_diff\"][2]\n", "index = example[\"commit_index\"]\n", "# example"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Input prep"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["build: update to yarn@1.21.1 (#34384)\n", "\n", "This updates yarn throughout the monorepo for both build and CI.\n", "\n", "PR Close #34384\n", "\n"]}], "source": ["commit = mgh.get_commit((repo_name, index))\n", "print(commit[\"message\"])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# print(gh.get_commit_diff(index, 15_000))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["PREV_TRAILING_INSTRUCTIONS = \"\"\"Do not include boilerplate text like \"Here is the commit message:\" in your response..\"\"\""]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["TRAILING_INSTRUCTIONS = \"\"\"Do not include boilerplate text like \"Here is the commit message:\" in your response..\"\"\""]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["PREV_TRAILING_INSTRUCTIONS == TRAILING_INSTRUCTIONS"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["PREV_SYSTEM_PROMPT = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "- When seeing both feature changes and documentation/style/refactoring changes, emphasize the feature changes.\n", "- Mimick the length and style of the example commit messages the user provides (e.g. whether using bullets, numbered lists, or paragraphs; the existence and format of a summary header and the information the header describes (types of changes, components, areas)). Do not be overly concise (e.g. always producing a single line).\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up! Reply with the commit message and the commit message ONLY, WITH NO EXTRA TEXT.\"\"\""]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:\n", "- Strictly synthesizes meaningful information from the provided code diff\n", "- Utilizes any additional user-provided context to comprehend the rationale behind the code changes\n", "- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions\n", "- Avoids unnecessary phrases such as \"this commit\", \"this change\", and the like\n", "- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes\n", "- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.\n", "- When seeing both feature changes and documentation/style/refactoring changes, emphasize the feature changes.\n", "- Mimick the length and style of the example commit messages the user provides (e.g. whether using bullets, numbered lists, or paragraphs; the existence and format of a summary header and the information the header describes (types of changes, components, areas)). Do not be overly concise (e.g. always producing a single line).\n", "Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up! Reply with the commit message and the commit message ONLY, WITH NO EXTRA TEXT.\"\"\""]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["PREV_SYSTEM_PROMPT == SYSTEM_PROMPT"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["prompts = {}\n", "metadatas = {}"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# label = \"research\"\n", "# prompts[label], metadatas[label] = mgh.get_prompt(\n", "#     (repo_name, index),\n", "#     summary=False,\n", "#     latest_commit_count=32,\n", "#     sort_paths_by_size=True,\n", "#     count_diffs_by_hunk=False,\n", "#     read_from_disk=True,\n", "#     summary_v2_threshold=900,\n", "#     trailing_instructions=TRAILING_INSTRUCTIONS,\n", "#     message_soft_budget=1024 * 3,\n", "#     relevant_message_limit=0,\n", "#     diff_line_limit=5000,\n", "#     token_budget=1024 * 12,\n", "# )"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# label = \"prompt_formatter\"\n", "# prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(\n", "#     (repo_name, index),\n", "#     summary=False,\n", "#     latest_commit_count=32,\n", "#     sort_paths_by_size=True,\n", "#     count_diffs_by_hunk=False,\n", "#     read_from_disk=True,\n", "#     summary_v2_threshold=1500,\n", "#     trailing_instructions=TRAILING_INSTRUCTIONS,\n", "#     message_soft_budget=1024 * 3,\n", "#     relevant_message_limit=0,\n", "#     diff_line_limit=5000,\n", "#     diff_budget=1024 * 9,\n", "#     token_budget=1024 * 12,\n", "# )"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Get formatter\n", "Get index and diffs\n", "Get changed file stats\n", "token_budget=1**********000000159028911097599180468360808563945281389781327557747838772170381060813469985856815104\n", "self.token_counter(diff)=62642\n", "Get diff\n", "Get relevant commit messages\n", "Get example commit messages\n", "Get formatter inputs\n", "self.token_counter.count_tokens(changed_files_prompt)=0, self.token_counter.count_tokens(diff_prompt)=1664, self.token_counter.count_tokens(relevant_message_prompt)=0, self.token_counter.count_tokens(example_message_prompt)=5873, self.token_counter.count_tokens(self.trailing_instructions)=28\n", "self.token_counter.count_tokens(message)=7565\n", "Format prompt\n"]}], "source": ["label = \"new_dogfood\"\n", "prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(\n", "    (repo_name, index),\n", "    summary=False,\n", "    latest_commit_count=32,\n", "    sort_paths_by_size=True,\n", "    count_diffs_by_hunk=False,\n", "    read_from_disk=True,\n", "    summary_v2_threshold=1500,\n", "    trailing_instructions=TRAILING_INSTRUCTIONS,\n", "    message_soft_budget=1024 * 3,\n", "    relevant_message_limit=0,\n", "    diff_line_limit=5000,\n", "    diff_budget=1024 * 9,\n", "    diff_v2=True,\n", "    system_prompt_v2=SYSTEM_PROMPT,\n", "    token_budget=1024 * 12,\n", ")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["7565"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["token_counter.count_tokens(prompts[\"new_dogfood\"].message)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# label = \"v7\"\n", "# prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(\n", "#     (repo_name, index),\n", "#     summary=False,\n", "#     latest_commit_count=32,\n", "#     sort_paths_by_size=True,\n", "#     count_diffs_by_hunk=False,\n", "#     read_from_disk=True,\n", "#     summary_v2_threshold=1500,\n", "#     trailing_instructions=TRAILING_INSTRUCTIONS,\n", "#     message_soft_budget=1024 * 3,\n", "#     relevant_message_limit=0,\n", "#     diff_line_limit=5000,\n", "#     diff_budget=1024 * 9,\n", "#     diff_v2=True,\n", "#     system_prompt_v2=V7,\n", "#     token_budget=1024 * 12,\n", "# )"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Get formatter\n", "Get index and diffs\n", "Get changed file stats\n", "token_budget=1**********000000159028911097599180468360808563945281389781327557747838772170381060813469985856815104\n", "self.token_counter(diff)=62642\n", "Get diff\n", "Get relevant commit messages\n", "Get example commit messages\n", "Get formatter inputs\n", "self.token_counter.count_tokens(changed_files_prompt)=0, self.token_counter.count_tokens(diff_prompt)=1664, self.token_counter.count_tokens(relevant_message_prompt)=0, self.token_counter.count_tokens(example_message_prompt)=5873, self.token_counter.count_tokens(self.trailing_instructions)=28\n", "self.token_counter.count_tokens(message)=7565\n", "Format prompt\n"]}], "source": ["if enable_relevant_messages:\n", "    label = \"relevant_messages\"\n", "    prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(\n", "        (repo_name, index),\n", "        summary=False,\n", "        latest_commit_count=32,\n", "        sort_paths_by_size=True,\n", "        count_diffs_by_hunk=False,\n", "        read_from_disk=True,\n", "        summary_v2_threshold=1500,\n", "        trailing_instructions=TRAILING_INSTRUCTIONS,\n", "        message_soft_budget=1024 * 3,\n", "        relevant_message_limit=3,\n", "        diff_line_limit=5000,\n", "        diff_budget=1024 * 9,\n", "        diff_v2=True,\n", "        system_prompt_v2=SYSTEM_PROMPT,\n", "        token_budget=1024 * 12,\n", "    )"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Get formatter\n", "Get index and diffs\n", "Get changed file stats\n", "token_budget=1**********000000159028911097599180468360808563945281389781327557747838772170381060813469985856815104\n", "self.token_counter(diff)=62642\n", "Get diff\n", "Get relevant commit messages\n", "Get example commit messages\n", "Get formatter inputs\n", "self.token_counter.count_tokens(changed_files_prompt)=0, self.token_counter.count_tokens(diff_prompt)=1664, self.token_counter.count_tokens(relevant_message_prompt)=0, self.token_counter.count_tokens(example_message_prompt)=3601, self.token_counter.count_tokens(self.trailing_instructions)=28\n", "self.token_counter.count_tokens(message)=5294\n", "Format prompt\n"]}], "source": ["if enable_similar_commits:\n", "    label = \"similar_commits\"\n", "    prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(\n", "        (repo_name, index),\n", "        summary=False,\n", "        latest_commit_count=32,\n", "        sort_paths_by_size=True,\n", "        count_diffs_by_hunk=False,\n", "        read_from_disk=True,\n", "        summary_v2_threshold=1500,\n", "        trailing_instructions=TRAILING_INSTRUCTIONS,\n", "        message_soft_budget=1024 * 3,\n", "        example_type=\"similar\",\n", "        similar_search_range=1000,\n", "        ratio_threshold=0.7,\n", "        git_structure_depth_limit=2,\n", "        git_structure_count_type=\"file\",\n", "        descriptor_path_source=\"commit\",\n", "        relevant_message_limit=0,\n", "        diff_line_limit=5000,\n", "        diff_budget=1024 * 9,\n", "        diff_v2=True,\n", "        system_prompt_v2=SYSTEM_PROMPT,\n", "        token_budget=1024 * 12,\n", "    )"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Get formatter\n", "Get index and diffs\n", "Get changed file stats\n", "token_budget=1**********000000159028911097599180468360808563945281389781327557747838772170381060813469985856815104\n", "self.token_counter(diff)=62642\n", "Get diff\n", "Get relevant commit messages\n", "Get example commit messages\n", "Get formatter inputs\n", "self.token_counter.count_tokens(changed_files_prompt)=0, self.token_counter.count_tokens(diff_prompt)=1664, self.token_counter.count_tokens(relevant_message_prompt)=0, self.token_counter.count_tokens(example_message_prompt)=3601, self.token_counter.count_tokens(self.trailing_instructions)=28\n", "self.token_counter.count_tokens(message)=5294\n", "Format prompt\n"]}], "source": ["if enable_similar_commits and enable_relevant_messages:\n", "    label = \"similar_commits_relevant_messages\"\n", "    prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(\n", "        (repo_name, index),\n", "        summary=False,\n", "        latest_commit_count=32,\n", "        sort_paths_by_size=True,\n", "        count_diffs_by_hunk=False,\n", "        read_from_disk=True,\n", "        summary_v2_threshold=1500,\n", "        trailing_instructions=TRAILING_INSTRUCTIONS,\n", "        message_soft_budget=1024 * 3,\n", "        example_type=\"similar\",\n", "        similar_search_range=1000,\n", "        ratio_threshold=0.7,\n", "        git_structure_depth_limit=2,\n", "        git_structure_count_type=\"file\",\n", "        descriptor_path_source=\"commit\",\n", "        relevant_message_limit=3,\n", "        diff_line_limit=5000,\n", "        diff_budget=1024 * 9,\n", "        diff_v2=True,\n", "        system_prompt_v2=SYSTEM_PROMPT,\n", "        token_budget=1024 * 12,\n", "    )"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# prompts[\"v1\"] = mgh.get_prompt_legacy((repo_name, index), token_budget=1024 * 12)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["if (\n", "    \"relevant_messages\" in prompts\n", "    and prompts[\"new_dogfood\"] == prompts[\"relevant_messages\"]\n", "):\n", "    del prompts[\"relevant_messages\"]\n", "    del metadatas[\"relevant_messages\"]\n", "if (\n", "    \"similar_commits\" in prompts\n", "    and \"similar_commits_relevant_messages\" in prompts\n", "    and prompts[\"similar_commits\"] == prompts[\"similar_commits_relevant_messages\"]\n", "):\n", "    del prompts[\"similar_commits_relevant_messages\"]\n", "    del metadatas[\"similar_commits_relevant_messages\"]\n", "# assert metadatas[\"default\"][\"relevant_message_token_count\"] == 0\n", "# if \"similar_commits\" in prompts:\n", "#     assert metadatas[\"similar_commits\"][\"relevant_message_token_count\"] == 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3bbd12d56090e37b1deea0f051564521a98d811e\n"]}], "source": ["print(commit[\"current_hash\"])"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["build: update to yarn@1.21.1 (#34384)\n", "\n", "This updates yarn throughout the monorepo for both build and CI.\n", "\n", "PR Close #34384\n", "\n"]}], "source": ["print(commit[\"message\"])"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================== new_dogfood ====================\n", "build: update yarn to version 1.21.1\n", "\n", "Update yarn version to 1.21.1 across the project. Remove outdated comments about Windows regression. Update engine requirements in package.json files.\n", "\n", "==================== similar_commits ====================\n", "build: update to yarn@1.21.1\n", "\n", "Update yarn version to 1.21.1 throughout the monorepo for both build and CI. Adjust version constraints in package.json files and update Bazel workspace configuration.\n", "\n"]}], "source": ["texts = {}\n", "responses = {}\n", "for key, prompt in prompts.items():\n", "    text, response = run_claude(prompt, SYSTEM_PROMPT)\n", "    texts[key] = text\n", "    responses[key] = response\n", "    print(\"=\" * 20, key, \"=\" * 20)\n", "    print(text)\n", "    print()\n", "# key = \"default\"\n", "# text, response = run_claude(prompts[key], V7)\n", "# texts[key] = text\n", "# responses[key] = response\n", "# print(\"=\" * 20, \"v7\", \"=\" * 20)\n", "# print(text)\n", "# print()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7565\n", "Below are the per-file diffs of the commit:\n", "\n", "diff --git a/WORKSPACE b/WORKSPACE\n", "index 8f975b9027..c88ae3dad2 100644\n", "--- a/WORKSPACE\n", "+++ b/WORKSPACE\n", "@@ -41,17 +41,9 @@ node_repositories(\n", "     node_version = \"10.16.0\",\n", "     package_json = [\"//:package.json\"],\n", "     yarn_repositories = {\n", "-        \"1.17.3\": (\"yarn-v1.17.3.tar.gz\", \"yarn-v1.17.3\", \"e3835194409f1b3afa1c62ca82f561f1c29d26580c9e220c36866317e043c6f3\"),\n", "+        \"1.21.1\": (\"yarn-v1.21.1.tar.gz\", \"yarn-v1.21.1\", \"d1d9f4a0f16f5ed484e814afeb98f39b82d4728c6c8beaafb5abc99c02db6674\"),\n", "     },\n", "-    # yarn 1.13.0 under <PERSON><PERSON> has a regression on Windows that causes build errors on rebuilds:\n", "-    # ```\n", "-    # ERROR: Source forest creation failed: C:/.../fyuc5c3n/execroot/angular/external (Directory not empty)\n", "-    # ```\n", "-    # See https://github.com/angular/angular/pull/29431 for more information.\n", "-    # It possible that versions of yarn past 1.13.0 do not have this issue, however, before\n", "-    # advancing this version we need to test manually on Windows that the above error does not\n", "-    # happen as the issue is not caught by CI.\n", "-    yarn_version = \"1.17.3\",\n", "+    yarn_version = \"1.21.1\",\n", " )\n", " \n", " yarn_install(diff --git a/aio/package.json b/aio/package.json\n", "index 53752720e6..6056868c40 100644\n", "--- a/aio/package.json\n", "+++ b/aio/package.json\n", "@@ -83,7 +83,7 @@\n", "   \"//engines-comment\": \"Keep this in sync with /package.json and /aio/tools/examples/shared/package.json\",\n", "   \"engines\": {\n", "     \"node\": \">=10.9.0 <13.0.0\",\n", "-    \"yarn\": \">=1.17.3 <2\"\n", "+    \"yarn\": \">=1.21.1 <2\"\n", "   },\n", "   \"private\": true,\n", "   \"dependencies\": {diff --git a/aio/tools/examples/shared/package.json b/aio/tools/examples/shared/package.json\n", "index 186ad400c3..7c0b589272 100644\n", "--- a/aio/tools/examples/shared/package.json\n", "+++ b/aio/tools/examples/shared/package.json\n", "@@ -13,7 +13,7 @@\n", "   \"//engines-comment\": \"Keep this in sync with /package.json and /aio/package.json\",\n", "   \"engines\": {\n", "     \"node\": \">=10.9.0 <13.0.0\",\n", "-    \"yarn\": \">=1.17.3 <2\"\n", "+    \"yarn\": \">=1.21.1 <2\"\n", "   },\n", "   \"keywords\": [],\n", "   \"author\": \"\",\n", "@@ -58,8 +58,8 @@\n", "     \"@types/express\": \"^4.0.35\",\n", "     \"@types/jasmine\": \"~2.8.8\",\n", "     \"@types/jasminewd2\": \"^2.0.4\",\n", "-    \"@types/jquery\":\"3.3.28\",\n", "-    \"@types/node\":\"~12.12.14\",\n", "+    \"@types/jquery\": \"3.3.28\",\n", "+    \"@types/node\": \"~12.12.14\",\n", "     \"canonical-path\": \"1.0.0\",\n", "     \"concurrently\": \"^3.0.0\",\n", "     \"http-server\": \"^0.11.1\",diff --git a/package.json b/package.json\n", "index a3b619c9f9..60dc44aa23 100644\n", "--- a/package.json\n", "+++ b/package.json\n", "@@ -9,7 +9,7 @@\n", "   \"//engines-comment\": \"Keep this in sync with /aio/package.json and /aio/tools/examples/shared/package.json\",\n", "   \"engines\": {\n", "     \"node\": \">=10.9.0 <13.0.0\",\n", "-    \"yarn\": \">=1.17.3 <2\"\n", "+    \"yarn\": \">=1.21.1 <2\"\n", "   },\n", "   \"repository\": {\n", "     \"type\": \"git\",diff --git a/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel b/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel\n", "index f6d5e39d78..e86e5ca0c4 100644\n", "--- a/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel\n", "+++ b/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel\n", "@@ -1,2 +1,2 @@\n", "-# Fetched from https://github.com/yarnpkg/yarn/releases/download/v1.13.0/yarn-v1.13.0.tar.gz\n", "+# Fetched from https://github.com/yarnpkg/yarn/releases/download/v1.21.1/yarn-v1.21.1.tar.gz\n", " licenses([\"notice\"])diff --git a/third_party/github.com/yarnpkg/yarn/releases/download/v1.17.3/LICENSE b/third_party/github.com/yarnpkg/yarn/releases/download/v1.17.3/LICENSE\n", "deleted file mode 100644\n", "index e32914ea02..**********\n", "--- a/third_party/github.com/yarnpkg/yarn/releases/download/v1.17.3/LICENSE\n", "+++ /dev/null\n", "@@ -1,26 +0,0 @@\n", "-BSD 2-Clause License\n", "-\n", "-For Yarn software\n", "-\n", "-Copyright (c) 2016-present, Yarn Contributors. All rights reserved.\n", "-\n", "-Redistribution and use in source and binary forms, with or without modification,\n", "-are permitted provided that the following conditions are met:\n", "-\n", "- * Redistributions of source code must retain the above copyright notice, this\n", "-   list of conditions and the following disclaimer.\n", "-\n", "- * Redistributions in binary form must reproduce the above copyright notice,\n", "-   this list of conditions and the following disclaimer in the documentation\n", "-   and/or other materials provided with the distribution.\n", "-\n", "-THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n", "-ANY EXPRESS OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n", "-WARRANTIES OF MERCHANT<PERSON><PERSON><PERSON><PERSON> AND FITNESS FOR A PARTICULAR PURPOSE ARE\n", "-DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\n", "-ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n", "-(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n", "-LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n", "-ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n", "-(INCLUDING NEG<PERSON>IGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n", "-SOFTWARE, <PERSON><PERSON><PERSON> IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "\n", "\n", "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n", "\n", "\n", "Commit message example 0:\n", "ci: use local, vendored yarn in Windows CI jobs (#34384)\n", "\n", "We keep a version of yarn in the repo, at\n", "`third_party/github.com/yarnpkg/`. All CI jobs should use that version\n", "for consistency (and easier updates).\n", "\n", "Previously, the Windows jobs did not use the local version. They used\n", "the version that came pre-installed on the docker image that we used.\n", "This made it more difficult to update the yarn version (something that\n", "we might want to do independently of updating other dependencies, such\n", "as Node.js).\n", "\n", "This commit fixes this by setting up the Windows CI jobs to also use the\n", "local, vendored version of yarn.\n", "\n", "PR Close #34384\n", "\n", "\n", "\n", "Commit message example 1:\n", "ci: avoid hard-coding path to local yarn executable (#34384)\n", "\n", "We keep a version of yarn in the repo, at\n", "`third_party/github.com/yarnpkg/`. All CI jobs (including Windows ones)\n", "should use that version for consistency (and easier updates). The path\n", "to the actual `yarn.js` script, however, changes depending on the\n", "version (e.g. `third_party/github.com/yarnpkg/v1.21.1/...`).\n", "(NOTE: The Windows jobs are currently not using this local version, but\n", "that should be fixed in a subsequent commit.)\n", "\n", "Previously, when updating the local version of yarn, we would\n", "potentially have to update the path in several places.\n", "\n", "This commit addresses the problem by adding a Node.js script that infers\n", "the correct path. The script can be used in all places where we need to\n", "use the local version of yarn (including both Linux and Windows CI\n", "jobs), thus eliminating the need to update the path in several places.\n", "\n", "PR Close #34384\n", "\n", "\n", "\n", "Commit message example 2:\n", "ci: remove unused variable from `.circleci/env.sh` (#34384)\n", "\n", "Since #32537, the `.circleci/get-commit-range.js` script is no longer\n", "used in `.circleci/env.sh`. This commit removes the now unused local\n", "variable to the script's path.\n", "\n", "PR Close #34384\n", "\n", "\n", "\n", "Commit message example 3:\n", "fix(ivy): incorrect injectable name logged in warning message on IE (#34305)\n", "\n", "When we log DI errors we get the name of the provider via `SomeClass.name`. In IE functions that inherit from other functions don't have their own `name`, but they take the `name` from the lowest parent in the chain, before `Function`. I've added some changes to fall back to parsing out the function name from the function's string form.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 4:\n", "fix(ivy): inheriting injectable definition from undecorated class not working on IE10 in JIT mode (#34305)\n", "\n", "The way definitions are added in JIT mode is through `Object.defineProperty`, but the problem is that in IE10 properties defined through `defineProperty` won't be inherited which means that inheriting injectable definitions no longer works. These changes add a workaround only for JIT mode where we define a fallback method for retrieving the definition. This isn't ideal, but it should only be required until v10 where we'll no longer support inheriting injectable definitions from undecorated classes.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 5:\n", "fix(ivy): inheritance in JIT mode not working correctly on IE10 (#34305)\n", "\n", "Fixes the metadata and lifecycle hook inheritance not working properly in IE10, because we weren't accessing things correctly.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 6:\n", "fix(ivy): avoid using __proto__ when reading metadata in JIT mode (#34305)\n", "\n", "In JIT mode we use `__proto__` when reading constructor parameter metadata, however it's not supported on IE10. These changes switch to using `Object.getPrototypeOf` instead.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 7:\n", "test(ivy): account for inconsistent attribute order (#34305)\n", "\n", "We've got some tests that assert that the generate DOM looks correct. The problem is that IE changes the attribute order in `innerHTML` which caused the tests to fail. I've reworked the relevant tests not to assert directly against `innerHTML`.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 8:\n", "fix(ivy): unknown property and element checks not working correctly in IE (#34305)\n", "\n", "We have a couple of cases where we use something like `typeof Node === 'function'` to figure out whether we're in a worker context. This works in most browsers, but IE returns `object` instead of `function`. I've updated all the usages to account for it.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 9:\n", "fix(ivy): inconsistent attribute casing in DebugNode.attributes on IE (#34305)\n", "\n", "In `DebugElement.attributes` we return all of the attributes from the underlying DOM node. Most browsers change the attribute names to lower case, but IE preserves the case and since we use camel-cased attributes, the return value was inconsitent. I've changed it to always lower case the attribute names.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 10:\n", "fix(ivy): i18n instructions thrown off by sanitizer in IE11 (#34305)\n", "\n", "While sanitizing on browsers that don't support the `template` element (pretty much only IE), we create an inert document and we insert content into it via `document.body.innerHTML = unsafeHTML`. The problem is that IE appears to parse the HTML passed to `innerHTML` differently, depending on whether the element has been inserted into a document or not. In particular, it seems to split some strings into multiple text nodes, which would've otherwise been a single node. This ended up throwing off some of the i18n code down the line and causing a handful of failures. I've worked around it by creating a new inert `body` element into which the HTML would be inserted.\n", "\n", "PR Close #34305\n", "\n", "\n", "\n", "Commit message example 11:\n", "Revert \"build: update to yarn@1.21.1\" (#34402)\n", "\n", "This reverts commit f029af50820765019413fa319330830306b80d6a while we investigate\n", "some failures on master on Circle CI. Currently the Windows tests and the\n", "\"test-ivy-aot\" jobs are red because of incompatible yarn versions.\n", "\n", "PR Close #34402\n", "\n", "\n", "\n", "Commit message example 12:\n", "docs(ivy): document breaking changes for DebugElement classes and attributes (#34328)\n", "\n", "PR Close #34328\n", "\n", "\n", "\n", "Commit message example 13:\n", "refactor(ivy): remove usage of Proxy for IE10/11 compatibility (#34328)\n", "\n", "PR Close #34328\n", "\n", "\n", "\n", "Commit message example 14:\n", "build: update to yarn@1.21.1 (#34384)\n", "\n", "This updates yarn throughout the monorepo for both build and CI.\n", "\n", "PR Close #34384\n", "\n", "\n", "\n", "Commit message example 15:\n", "refactor(ivy): don't include removed classes in the styling debug (#34375)\n", "\n", "This is mostly done to allign behaviour with DebugElement.classes and remove\n", "Proxy usage (not supported in IE10/11).\n", "\n", "PR Close #34375\n", "\n", "\n", "\n", "Commit message example 16:\n", "docs: add docs about inputs and strictNullChecks to template typecheck (#34194)\n", "\n", "PR Close #34194\n", "\n", "\n", "\n", "Commit message example 17:\n", "perf(ivy): eagerly parse the template twice during analysis (#34334)\n", "\n", "A quirk of the Angular template parser is that when parsing templates in the\n", "\"default\" mode, with options specified by the user, the source mapping\n", "information in the template AST may be inaccurate. As a result, the compiler\n", "parses the template twice: once for \"emit\" and once to produce an AST with\n", "accurate sourcemaps for diagnostic production.\n", "\n", "Previously, only the first parse was performed during analysis. The second\n", "parse occurred during the template type-checking phase, just in time to\n", "produce the template type-checking file.\n", "\n", "However, with the reuse of analysis results during incremental builds, it\n", "makes more sense to do the diagnostic parse eagerly during analysis so that\n", "the work isn't unnecessarily repeated in subsequent builds. This commit\n", "refactors the `ComponentDecorator<PERSON><PERSON><PERSON>` to do both parses eagerly, which\n", "actually cleans up some complexity around template parsing as well.\n", "\n", "PR Close #34334\n", "\n", "\n", "\n", "Commit message example 18:\n", "perf(compiler): speed up i18n digest computations (#34332)\n", "\n", "Avoids the usage of array destructuring, as it introduces calls to\n", "a `__values` helper function in ES5 that has a relatively high\n", "performance impact. This shaves off roughly 130ms of CPU time for a\n", "large compilation with big templates that uses i18n.\n", "\n", "PR Close #34332\n", "\n", "\n", "\n", "Commit message example 19:\n", "perf(compiler): use a shared interpolation regex (#34332)\n", "\n", "The template parser has a certain interpolation config associated with\n", "it and builds a regular expression each time it needs to extract the\n", "interpolations from an input string. Since the interpolation config is\n", "typically the default of `{{` and `}}`, the regular expression doesn't\n", "have to be recreated each time. Therefore, this commit creates only a\n", "single regular expression instance that is used for the default\n", "configuration.\n", "\n", "In a large compilation unit with big templates, computing the regular\n", "expression took circa 275ms. This change reduces this to effectively\n", "zero.\n", "\n", "PR Close #34332\n", "\n", "\n", "\n", "Commit message example 20:\n", "perf(compiler): optimize cloning cursors state (#34332)\n", "\n", "On a large compilation unit with big templates, the total time spent in\n", "the `PlainCharacterCursor` constructor was 470ms. This commit applies\n", "two optimizations to reduce this time:\n", "\n", "1. Avoid the object spread operator within the constructor, as the\n", "generated `__assign` helper in the emitted UMD bundle (ES5) does not\n", "optimize well compared to a hardcoded object literal. This results in a\n", "significant performance improvement. Because of the straight-forward\n", "object literal, the VM is now much better able to optimize the memory\n", "allocations which makes a significant difference as the\n", "`PlainCharacterCursor` constructor is called in tight loops.\n", "\n", "2. Reduce the number of `CharacterCursor` clones. Although cloning\n", "itself is now much faster because of the optimization above, several\n", "clone operations were not necessary.\n", "\n", "Combined, these changes reduce the total time spent in the\n", "`PlainCharacterCursor` constructor to just 10ms.\n", "\n", "PR Close #34332\n", "\n", "\n", "\n", "Commit message example 21:\n", "perf(ivy): use module resolution cache (#34332)\n", "\n", "During TypeScript module resolution, a lot of filesystem requests are\n", "done. This is quite an expensive operation, so a module resolution cache\n", "can be used to speed up the process significantly.\n", "\n", "This commit lets the Ivy compiler perform all module resolution with a\n", "module resolution cache. Note that the module resolution behavior can be\n", "changed with a custom compiler host, in which case that custom host\n", "implementation is responsible for caching. In the case of the Angular\n", "CLI a custom compiler host with proper module resolution caching is\n", "already in place, so the CLI already has this optimization.\n", "\n", "PR Close #34332\n", "\n", "\n", "\n", "Commit message example 22:\n", "perf(ivy): cache export scopes extracted from declaration files (#34332)\n", "\n", "The export scope of NgModules from external compilations units, as\n", "present in .d.ts declarations, does not change during a compilation so\n", "can be easily shared. There was already a cache but the computed export\n", "scope was not actually stored there. This commit fixes that.\n", "\n", "PR Close #34332\n", "\n", "\n", "\n", "Commit message example 23:\n", "perf(ivy): share instances of `DomElementSchemaRegistry` (#34332)\n", "\n", "To create a binding parser, an instance of `ElementSchemaRegistry` is\n", "required. Prior to this change, each time a new binding parser was\n", "created a new instance of `DomElementSchemaRegistry` would be\n", "instantiated. This is an expensive operation that takes roughly 1ms per\n", "instantiation, so it is key that multiple allocations are avoided.\n", "\n", "By sharing a single `DomElementSchemaRegistry`, we avoid two such\n", "allocations, i.e. save ~2ms, per component template.\n", "\n", "PR Close #34332\n", "\n", "\n", "\n", "Commit message example 24:\n", "test(core): fix schematics calls to run synchronously (#34364)\n", "\n", "Previously the calls to run the schematics were not being properly\n", "or consistently awaited in the tests. While this currently does not\n", "affect the tests' performance, this fix corrects the syntax and\n", "adds stability for future changes.\n", "\n", "PR Close #34364\n", "\n", "\n", "\n", "Commit message example 25:\n", "build: remove unused webpack npm dependency (#34366)\n", "\n", "We no longer need it.\n", "\n", "PR Close #34366\n", "\n", "\n", "\n", "Commit message example 26:\n", "fix(ivy): generate a better error for template var writes (#34339)\n", "\n", "In Ivy it's illegal for a template to write to a template variable. So the\n", "template:\n", "\n", "```html\n", "<ng-template let-somevar>\n", "  <button (click)=\"somevar = 3\">Set var to 3</button>\n", "</ng-template>\n", "```\n", "\n", "is erroneous and previously would fail to compile with an assertion error\n", "from the `TemplateDefinitionBuilder`. This error wasn't particularly user-\n", "friendly, though, as it lacked the context of which template or where the\n", "error occurred.\n", "\n", "In this commit, a new check in template type-checking is added which detects\n", "such erroneous writes and produces a true diagnostic with the appropriate\n", "context information.\n", "\n", "Closes #33674\n", "\n", "PR Close #34339\n", "\n", "\n", "\n", "Commit message example 27:\n", "perf(ivy): reuse prior analysis work during incremental builds (#34288)\n", "\n", "Previously, the compiler performed an incremental build by analyzing and\n", "resolving all classes in the program (even unchanged ones) and then using\n", "the dependency graph information to determine which .js files were stale and\n", "needed to be re-emitted. This algorithm produced \"correct\" rebuilds, but the\n", "cost of re-analyzing the entire program turned out to be higher than\n", "anticipated, especially for component-heavy compilations.\n", "\n", "To achieve performant rebuilds, it is necessary to reuse previous analysis\n", "results if possible. Doing this safely requires knowing when prior work is\n", "viable and when it is stale and needs to be re-done.\n", "\n", "The new algorithm implemented by this commit is such:\n", "\n", "1) Each incremental build starts with knowledge of the last known good\n", "   dependency graph and analysis results from the last successful build,\n", "   plus of course information about the set of files changed.\n", "\n", "2) The previous dependency graph's information is used to determine the\n", "   set of source files which have \"logically\" changed. A source file is\n", "   considered logically changed if it or any of its dependencies have\n", "   physically changed (on disk) since the last successful compilation. Any\n", "   logically unchanged dependencies have their dependency information copied\n", "   over to the new dependency graph.\n", "\n", "3) During the `TraitCompiler`'s loop to consider all source files in the\n", "   program, if a source file is logically unchanged then its previous\n", "   analyses are \"adopted\" (and their 'register' steps are run). If the file\n", "   is logically changed, then it is re-analyzed as usual.\n", "\n", "4) Then, incremental build proceeds as before, with the new dependency graph\n", "   being used to determine the set of files which require re-emitting.\n", "\n", "This analysis reuse avoids template parsing operations in many circumstances\n", "and significantly reduces the time it takes ngtsc to rebuild a large\n", "application.\n", "\n", "Future work will increase performance even more, by tackling a variety of\n", "other opportunities to reuse or avoid work.\n", "\n", "PR Close #34288\n", "\n", "\n", "\n", "Commit message example 28:\n", "refactor(ivy): move analysis side effects into a register phase (#34288)\n", "\n", "Previously 'analyze' in the various `DecoratorHandler`s not only extracts\n", "information from the decorators on the classes being analyzed, but also has\n", "several side effects within the compiler:\n", "\n", "* it can register metadata about the types involved in global metadata\n", "  trackers.\n", "* it can register information about which .ngfactory symbols are actually\n", "  needed.\n", "\n", "In this commit, these side-effects are moved into a new 'register' phase,\n", "which runs after the 'analyze' step. Currently this is a no-op refactoring\n", "as 'register' is always called directly after 'analyze'. In the future this\n", "opens the door for re-use of prior analysis work (with only 'register' being\n", "called, to apply the above side effects).\n", "\n", "Also as part of this refactoring, the reification of NgModule scope\n", "information into the incremental dependency graph is moved to the\n", "`NgtscProgram` instead of the `TraitCompiler` (which now only manages trait\n", "compilation and does not have other side effects).\n", "\n", "PR Close #34288\n", "\n", "\n", "\n", "Commit message example 29:\n", "refactor(ivy): formalize the compilation process for matched handlers (#34288)\n", "\n", "Prior to this commit, the `IvyCompilation` tracked the state of each matched\n", "`Decorator<PERSON><PERSON>ler` on each class in the `ts.Program`, and how they\n", "progressed through the compilation process. This tracking was originally\n", "simple, but had grown more complicated as the compiler evolved. The state of\n", "each specific \"target\" of compilation was determined by the nullability of\n", "a number of fields on the object which tracked it.\n", "\n", "This commit formalizes the process of compilation of each matched handler\n", "into a new \"trait\" concept. A trait is some aspect of a class which gets\n", "created when a `DecoratorHandler` matches the class. It represents an Ivy\n", "aspect that needs to go through the compilation process.\n", "\n", "Traits begin in a \"pending\" state and undergo transitions as various steps\n", "of compilation take place. The `IvyCompilation` class is renamed to the\n", "`TraitCompiler`, which manages the state of all of the traits in the active\n", "program.\n", "\n", "Making the trait concept explicit will support future work to incrementalize\n", "the expensive analysis process of compilation.\n", "\n", "PR Close #34288\n", "\n", "\n", "\n", "Commit message example 30:\n", "style(common): remove unnecessary jsdoc type (#34369)\n", "\n", "These types cause the compiler to give warnings,\n", "which add noise to compilation logs.\n", "\n", "PR Close #34369\n", "\n", "\n", "\n", "Commit message example 31:\n", "fix(ngcc): render UMD imports even if no prior imports (#34353)\n", "\n", "Previously the UMD rendering formatter assumed that\n", "there would already be import (and an export) arguments\n", "to the UMD factory function.\n", "\n", "This commit adds support for this corner case.\n", "\n", "Fixes #34138\n", "\n", "PR Close #34353\n", "\n", "\n", "\n", "Above are all the commit message examples.\n", "\n", "\n", "Do not include boilerplate text like \"Here is the commit message:\" in your response..\n"]}], "source": ["label = \"new_dogfood\"\n", "print(token_counter.count_tokens(prompts[label].message))\n", "print(prompts[label].message)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5294\n", "Below are the per-file diffs of the commit:\n", "\n", "diff --git a/WORKSPACE b/WORKSPACE\n", "index 8f975b9027..c88ae3dad2 100644\n", "--- a/WORKSPACE\n", "+++ b/WORKSPACE\n", "@@ -41,17 +41,9 @@ node_repositories(\n", "     node_version = \"10.16.0\",\n", "     package_json = [\"//:package.json\"],\n", "     yarn_repositories = {\n", "-        \"1.17.3\": (\"yarn-v1.17.3.tar.gz\", \"yarn-v1.17.3\", \"e3835194409f1b3afa1c62ca82f561f1c29d26580c9e220c36866317e043c6f3\"),\n", "+        \"1.21.1\": (\"yarn-v1.21.1.tar.gz\", \"yarn-v1.21.1\", \"d1d9f4a0f16f5ed484e814afeb98f39b82d4728c6c8beaafb5abc99c02db6674\"),\n", "     },\n", "-    # yarn 1.13.0 under <PERSON><PERSON> has a regression on Windows that causes build errors on rebuilds:\n", "-    # ```\n", "-    # ERROR: Source forest creation failed: C:/.../fyuc5c3n/execroot/angular/external (Directory not empty)\n", "-    # ```\n", "-    # See https://github.com/angular/angular/pull/29431 for more information.\n", "-    # It possible that versions of yarn past 1.13.0 do not have this issue, however, before\n", "-    # advancing this version we need to test manually on Windows that the above error does not\n", "-    # happen as the issue is not caught by CI.\n", "-    yarn_version = \"1.17.3\",\n", "+    yarn_version = \"1.21.1\",\n", " )\n", " \n", " yarn_install(diff --git a/aio/package.json b/aio/package.json\n", "index 53752720e6..6056868c40 100644\n", "--- a/aio/package.json\n", "+++ b/aio/package.json\n", "@@ -83,7 +83,7 @@\n", "   \"//engines-comment\": \"Keep this in sync with /package.json and /aio/tools/examples/shared/package.json\",\n", "   \"engines\": {\n", "     \"node\": \">=10.9.0 <13.0.0\",\n", "-    \"yarn\": \">=1.17.3 <2\"\n", "+    \"yarn\": \">=1.21.1 <2\"\n", "   },\n", "   \"private\": true,\n", "   \"dependencies\": {diff --git a/aio/tools/examples/shared/package.json b/aio/tools/examples/shared/package.json\n", "index 186ad400c3..7c0b589272 100644\n", "--- a/aio/tools/examples/shared/package.json\n", "+++ b/aio/tools/examples/shared/package.json\n", "@@ -13,7 +13,7 @@\n", "   \"//engines-comment\": \"Keep this in sync with /package.json and /aio/package.json\",\n", "   \"engines\": {\n", "     \"node\": \">=10.9.0 <13.0.0\",\n", "-    \"yarn\": \">=1.17.3 <2\"\n", "+    \"yarn\": \">=1.21.1 <2\"\n", "   },\n", "   \"keywords\": [],\n", "   \"author\": \"\",\n", "@@ -58,8 +58,8 @@\n", "     \"@types/express\": \"^4.0.35\",\n", "     \"@types/jasmine\": \"~2.8.8\",\n", "     \"@types/jasminewd2\": \"^2.0.4\",\n", "-    \"@types/jquery\":\"3.3.28\",\n", "-    \"@types/node\":\"~12.12.14\",\n", "+    \"@types/jquery\": \"3.3.28\",\n", "+    \"@types/node\": \"~12.12.14\",\n", "     \"canonical-path\": \"1.0.0\",\n", "     \"concurrently\": \"^3.0.0\",\n", "     \"http-server\": \"^0.11.1\",diff --git a/package.json b/package.json\n", "index a3b619c9f9..60dc44aa23 100644\n", "--- a/package.json\n", "+++ b/package.json\n", "@@ -9,7 +9,7 @@\n", "   \"//engines-comment\": \"Keep this in sync with /aio/package.json and /aio/tools/examples/shared/package.json\",\n", "   \"engines\": {\n", "     \"node\": \">=10.9.0 <13.0.0\",\n", "-    \"yarn\": \">=1.17.3 <2\"\n", "+    \"yarn\": \">=1.21.1 <2\"\n", "   },\n", "   \"repository\": {\n", "     \"type\": \"git\",diff --git a/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel b/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel\n", "index f6d5e39d78..e86e5ca0c4 100644\n", "--- a/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel\n", "+++ b/third_party/github.com/yarnpkg/yarn/releases/BUILD.bazel\n", "@@ -1,2 +1,2 @@\n", "-# Fetched from https://github.com/yarnpkg/yarn/releases/download/v1.13.0/yarn-v1.13.0.tar.gz\n", "+# Fetched from https://github.com/yarnpkg/yarn/releases/download/v1.21.1/yarn-v1.21.1.tar.gz\n", " licenses([\"notice\"])diff --git a/third_party/github.com/yarnpkg/yarn/releases/download/v1.17.3/LICENSE b/third_party/github.com/yarnpkg/yarn/releases/download/v1.17.3/LICENSE\n", "deleted file mode 100644\n", "index e32914ea02..**********\n", "--- a/third_party/github.com/yarnpkg/yarn/releases/download/v1.17.3/LICENSE\n", "+++ /dev/null\n", "@@ -1,26 +0,0 @@\n", "-BSD 2-Clause License\n", "-\n", "-For Yarn software\n", "-\n", "-Copyright (c) 2016-present, Yarn Contributors. All rights reserved.\n", "-\n", "-Redistribution and use in source and binary forms, with or without modification,\n", "-are permitted provided that the following conditions are met:\n", "-\n", "- * Redistributions of source code must retain the above copyright notice, this\n", "-   list of conditions and the following disclaimer.\n", "-\n", "- * Redistributions in binary form must reproduce the above copyright notice,\n", "-   this list of conditions and the following disclaimer in the documentation\n", "-   and/or other materials provided with the distribution.\n", "-\n", "-THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n", "-ANY EXPRESS OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n", "-WARRANTIES OF MERCHANT<PERSON><PERSON><PERSON><PERSON> AND FITNESS FOR A PARTICULAR PURPOSE ARE\n", "-DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\n", "-ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n", "-(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n", "-LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n", "-ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n", "-(INCLUDING NEG<PERSON>IGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n", "-SOFTWARE, <PERSON><PERSON><PERSON> IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "\n", "\n", "Below are the commit message examples from the same repository, to illustrate the commit message style and conventions to follow:\n", "\n", "\n", "Commit message example 0:\n", "build: update to yarn@1.21.1 (#34384)\n", "\n", "This updates yarn throughout the monorepo for both build and CI.\n", "\n", "PR Close #34384\n", "\n", "\n", "\n", "Commit message example 1:\n", "Revert \"build: update to yarn@1.21.1\" (#34402)\n", "\n", "This reverts commit f029af50820765019413fa319330830306b80d6a while we investigate\n", "some failures on master on Circle CI. Currently the Windows tests and the\n", "\"test-ivy-aot\" jobs are red because of incompatible yarn versions.\n", "\n", "PR Close #34402\n", "\n", "\n", "\n", "Commit message example 2:\n", "build: bump maximum allowed yarn version (#33430)\n", "\n", "Currently this repo allows Yarn between 1.17.3 and 1.18.0, whereas the components repo requires a minimum of 1.19.1 which makes it annoying to switch between repositories. These changes bump the maximum allowed Yarn version.\n", "\n", "PR Close #33430\n", "\n", "\n", "\n", "Commit message example 3:\n", "build: support yarn 1.19.x (#34143)\n", "\n", "PR Close #34143\n", "\n", "\n", "\n", "Commit message example 4:\n", "build: set upper version limit for yarn to <2 (#34236)\n", "\n", "Rather than bumping up the allowed version of yarn on each release\n", "we should instead just allow for anything within the major version\n", "1 range.\n", "\n", "PR Close #34236\n", "\n", "\n", "\n", "Commit message example 5:\n", "build(docs-infra): upgrade cli command docs sources to 94d07909c (#34325)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/6c6ad8661...94d07909c):\n", "\n", "**Modified**\n", "- help/deploy.json\n", "\n", "##\n", "\n", "PR Close #34325\n", "\n", "\n", "\n", "Commit message example 6:\n", "build(docs-infra): upgrade cli command docs sources to 6c6ad8661 (#34082)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/8b71bccf9...6c6ad8661):\n", "\n", "**Modified**\n", "- help/test.json\n", "\n", "##\n", "\n", "PR Close #34082\n", "\n", "\n", "\n", "Commit message example 7:\n", "build(docs-infra): upgrade cli command docs sources to 8b71bccf9 (#33869)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/3bcf5b5e2...8b71bccf9):\n", "\n", "**Modified**\n", "- help/doc.json\n", "- help/update.json\n", "\n", "##\n", "\n", "PR Close #33869\n", "\n", "\n", "\n", "Commit message example 8:\n", "build(docs-infra): upgrade cli command docs sources to 3bcf5b5e2 (#33683)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/e598c8e89...3bcf5b5e2):\n", "\n", "**Modified**\n", "- help/update.json\n", "\n", "##\n", "\n", "PR Close #33683\n", "\n", "\n", "\n", "Commit message example 9:\n", "build(docs-infra): upgrade cli command docs sources to e598c8e89 (#33624)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/7ecec963d...e598c8e89):\n", "\n", "**Modified**\n", "- help/generate.json\n", "\n", "##\n", "\n", "PR Close #33624\n", "\n", "\n", "\n", "Commit message example 10:\n", "build(docs-infra): upgrade cli command docs sources to 7ecec963d (#33420)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/57e36893c...7ecec963d):\n", "\n", "**Modified**\n", "- help/new.json\n", "\n", "##\n", "\n", "PR Close #33420\n", "\n", "\n", "\n", "Commit message example 11:\n", "build(docs-infra): upgrade cli command docs sources to 57e36893c (#33349)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/38635d2d9...57e36893c):\n", "\n", "**Modified**\n", "- help/generate.json\n", "- help/update.json\n", "\n", "##\n", "\n", "PR Close #33349\n", "\n", "\n", "\n", "Commit message example 12:\n", "build(docs-infra): upgrade cli command docs sources to 38635d2d9 (#33098)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/a0ecddbf1...38635d2d9):\n", "\n", "**Modified**\n", "- help/build.json\n", "- help/xi18n.json\n", "\n", "##\n", "\n", "PR Close #33098\n", "\n", "\n", "\n", "Commit message example 13:\n", "build(docs-infra): upgrade cli command docs sources to a0ecddbf1 (#33081)\n", "\n", "Updating [angular#master](https://github.com/angular/angular/tree/master) from [cli-builds#master](https://github.com/angular/cli-builds/tree/master).\n", "\n", "##\n", "Relevant changes in [commit range](https://github.com/angular/cli-builds/compare/0a36071b8...a0ecddbf1):\n", "\n", "**Modified**\n", "- help/generate.json\n", "\n", "##\n", "\n", "PR Close #33081\n", "\n", "\n", "\n", "Commit message example 14:\n", "build: bump check version requirements for bazel and rules_nodejs (#33966)\n", "\n", "PR Close #33966\n", "\n", "\n", "\n", "Commit message example 15:\n", "fix: use full cldr data to support all locales (#33682)\n", "\n", "switching to cldr-data package resulted in\n", "loss of some locales, since by default only core locales are loaded.\n", "This PR adds a flag to tell cldr-data to use full locale coverage\n", "\n", "fixes: #33681\n", "\n", "PR Close #33682\n", "\n", "\n", "\n", "Commit message example 16:\n", "build(docs-infra): sync examples node engine constraints with aio (#33877)\n", "\n", "PR Close #33877\n", "\n", "\n", "\n", "Commit message example 17:\n", "build: add non-ivy test script to package.json (#33314)\n", "\n", "This makes it easier to run non-ivy tests locally.\n", "\n", "PR Close #33314\n", "\n", "\n", "\n", "Commit message example 18:\n", "build: update bazel to v1.1.0 (#33813)\n", "\n", "Updates <PERSON>zel to the latest stable version. Bazel 1.1.0\n", "supposedly fixes a permission bug in Windows. Hence we\n", "should try to update and see if that fixes the bug.\n", "\n", "It's generally good to be up to date. See potential bug\n", "fix commit:\n", "https://github.com/bazelbuild/bazel/commit/618e5a28f7f735c37724377b15775a4975349c74.\n", "\n", "PR Close #33813\n", "\n", "\n", "\n", "Commit message example 19:\n", "release: cut the v9.0.0-rc.1 release\n", "\n", "\n", "\n", "Commit message example 20:\n", "release: cut the v9.0.0-rc.0 release\n", "\n", "\n", "\n", "Commit message example 21:\n", "release: cut the v9.0.0-next.15 release\n", "\n", "\n", "\n", "Commit message example 22:\n", "release: cut the v9.0.0-next.14 release\n", "\n", "\n", "\n", "Commit message example 23:\n", "release: cut the v9.0.0-next.13 release\n", "\n", "\n", "\n", "Commit message example 24:\n", "release: cut the v9.0.0-next.12 release\n", "\n", "\n", "\n", "Commit message example 25:\n", "release: cut the v9.0.0-next.11 release\n", "\n", "\n", "\n", "Commit message example 26:\n", "release: cut the v9.0.0-next.10 release\n", "\n", "\n", "\n", "Commit message example 27:\n", "build(docs-infra): ensure `setup-local` and similar scripts build local packages (#33206)\n", "\n", "The `setup-local` scripts (and others that are based on it, such as\n", "`setup-local-viewengine`), mainly does two things: Replace the Angular\n", "packages with the locally built ones for `aio/` and the docs examples\n", "(`aio/tools/examples/shared/`). It does this by calling two other npm\n", "scripts: `aio-use-local` and `example-use-local` respectively.\n", "\n", "For these scripts to work, the local Angular packages must be already\n", "built (via `scripts/build-packages-dist.sh`). In order to make it easier\n", "for people to test against local packages, the scripts support a\n", "`--build-packages` option, that (if passed) will result in building the\n", "local packages as well.\n", "\n", "Given that the same local packages are used for both `aio/` and the\n", "examples, we only need to build the packages once. Also, to speed up\n", "execution on CI, we do not need to build the packages there, because the\n", "packages would have been built already in a previous CI job.\n", "\n", "However, the various setup npm scripts were not implemented correctly to\n", "meet these requirements. Specifically, when running locally,\n", "`aio-use-local` would build the packages, while `example-use-local`\n", "would not (it was supposed to use the already built packages from\n", "`aio-use-local`). The `example-use-local` script, though, was configured\n", "to run before `aio-use-local`. As a result, the packages were not built,\n", "by the time `example-use-local` needed them, which would cause an error.\n", "\n", "This commit fixes it by ensuring that `aio-use-local` (which builds the\n", "local Angular packages) runs before `example-use-local`, so that the\n", "latter can use the same packages already built by the former.\n", "\n", "PR Close #33206\n", "\n", "\n", "\n", "Commit message example 28:\n", "revert: build(docs-infra): ensure `setup-local` and similar scripts build local packages (#33216)\n", "\n", "This reverts commit 9098a4018795031f3c516a4e17e1ee9599e02c9b.\n", "\n", "PR Close #33216\n", "\n", "\n", "\n", "Commit message example 29:\n", "build(docs-infra): ensure `setup-local` and similar scripts build local packages (#33206)\n", "\n", "The `setup-local` scripts (and others that are based on it, such as\n", "`setup-local-viewengine`), mainly does two things: Replace the Angular\n", "packages with the locally built ones for `aio/` and the docs examples\n", "(`aio/tools/examples/shared/`). It does this by calling two other npm\n", "scripts: `aio-use-local` and `example-use-local` respectively.\n", "\n", "For these scripts to work, the local Angular packages must be already\n", "built (via `scripts/build-packages-dist.sh`). In order to make it easier\n", "for people to test against local packages, the scripts support a\n", "`--build-packages` option, that (if passed) will result in building the\n", "local packages as well.\n", "\n", "Given that the same local packages are used for both `aio/` and the\n", "examples, we only need to build the packages once. Also, to speed up\n", "execution on CI, we do not need to build the packages there, because the\n", "packages would have been built already in a previous CI job.\n", "\n", "However, the various setup npm scripts were not implemented correctly to\n", "meet these requirements. Specifically, when running locally,\n", "`aio-use-local` would build the packages, while `example-use-local`\n", "would not (it was supposed to use the already built packages from\n", "`aio-use-local`). The `example-use-local` script, though, was configured\n", "to run before `aio-use-local`. As a result, the packages were not built,\n", "by the time `example-use-local` needed them, which would cause an error.\n", "\n", "This commit fixes it by ensuring that `aio-use-local` (which builds the\n", "local Angular packages) runs before `example-use-local`, so that the\n", "latter can use the same packages already built by the former.\n", "\n", "PR Close #33206\n", "\n", "\n", "\n", "Commit message example 30:\n", "fix(bazel): update to tsickle 0.37.1 to fix peerDep warnings (#33788)\n", "\n", "tsickle 0.37.1 is compatible with typescript 3.6, so we should use it and fix peerDep warnings from npm/yarn.\n", "\n", "PR Close #33788\n", "\n", "\n", "\n", "Commit message example 31:\n", "build: update @schematics/angular to 9.0.0-rc.3 (#33955)\n", "\n", "PR Close #33955\n", "\n", "\n", "\n", "Above are all the commit message examples.\n", "\n", "\n", "Do not include boilerplate text like \"Here is the commit message:\" in your response..\n"]}], "source": ["label = \"similar_commits\"\n", "print(token_counter.count_tokens(prompts[label].message))\n", "print(prompts[label].message)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0460624694824219\n", "buil\n", "\n", "1.1073665618896484\n", "d: update yarn to\n", "\n", "1.1869149208068848\n", " version 1.21\n", "\n", "1.2496087551116943\n", ".1\n", "\n", "Update\n", "\n", "1.3458781242370605\n", " yarn version to 1.\n", "\n", "1.4251959323883057\n", "21.1 across\n", "\n", "1.4890952110290527\n", " the project. Remove\n", "\n", "1.5524280071258545\n", " outdated comments about\n", "\n", "1.616262435913086\n", " Windows regression. Update\n", "\n", "1.7280852794647217\n", " engine requirements in package.json files\n", "\n", "1.8076088428497314\n", ".\n", "\n"]}], "source": ["import time\n", "\n", "start_time = time.time()\n", "key = \"new_dogfood\"\n", "for text in run_claude_stream(prompts[key].message, SYSTEM_PROMPT):\n", "    print(time.time() - start_time)\n", "    print(text)\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
#!/usr/bin/env python
# coding: utf-8

# # Setup
# ```bash
# pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler
# gcloud auth login
# gcloud auth application-default login
# bazel run //tools/generate_proto_typestubs
# ```

# In[1]:


get_ipython().run_line_magic("load_ext", "autoreload")
get_ipython().run_line_magic("autoreload", "2")


# In[2]:


from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient

REGION = "us-east5"
PROJECT_ID = "augment-387916"
MODEL_NAME = "claude-3-5-sonnet@20240620"
# MODEL_NAME = "claude-3-5-sonnet-v2@20241022"
TEMPERAURE = 0
MAX_OUTPUT_TOKENS = 1024 * 8

vertex_ai_client = AnthropicVertexAiClient(
    project_id=PROJECT_ID,
    region=REGION,
    model_name=MODEL_NAME,
    temperature=TEMPERAURE,
    max_output_tokens=MAX_OUTPUT_TOKENS,
)


def run_claude(message_or_prompt, system_message):
    if isinstance(message_or_prompt, str):
        message = message_or_prompt
    else:
        message = message_or_prompt.message
        system_message = message_or_prompt.system_prompt
    response = vertex_ai_client.client.messages.create(
        model=MODEL_NAME,
        max_tokens=MAX_OUTPUT_TOKENS,
        messages=[{"role": "user", "content": message}],
        system=system_message,
        temperature=TEMPERAURE,
    )
    return response.content[0].text, response


def run_claude_stream(message, system_message):
    with vertex_ai_client.client.messages.stream(
        model=MODEL_NAME,
        max_tokens=MAX_OUTPUT_TOKENS,
        messages=[{"role": "user", "content": message}],
        system=system_message,
        temperature=TEMPERAURE,
    ) as stream:
        for text in stream.text_stream:
            yield text


text, response = run_claude(
    "Write a hello world in Python. Return a code block only.", ""
)
print(text)
print()

print("Streaming:")
for text in run_claude_stream(
    "Write a hello world in Python. Return a code block only.", ""
):
    print(text)


# In[3]:


from base.prompt_format_chat.lib.token_counter import RoughTokenCounter
from experimental.zhuoran.commit_msgs.git_history import MultiGitHistory

token_counter = RoughTokenCounter()
mgh = MultiGitHistory.from_jsons(
    {
        "augment_recent": (
            "/home/<USER>/zhuoran/commit_msgs/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs/eval_set_v1_new_keys.json",
            "/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json",
            "/home/<USER>/augment/",
            "utf-8",
        ),
        "augment": (
            "/home/<USER>/zhuoran/commit_msgs/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs/git_structure_v1.json",
            "/home/<USER>/augment/",
            "utf-8",
        ),
        "angular": (
            "/home/<USER>/zhuoran/commit_msgs_angular/commits_v4.json",
            "/home/<USER>/zhuoran/commit_msgs_angular/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_angular/git_structure_v1.json",
            "/home/<USER>/angular/",
            "utf-8",
        ),
        "pytorch": (
            "/home/<USER>/zhuoran/commit_msgs_pytorch/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_pytorch/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_pytorch/git_structure_v1.json",
            "/home/<USER>/pytorch/",
            "utf-16",
        ),
        "linux": (
            "/home/<USER>/zhuoran/commit_msgs_linux/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_linux/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_linux/git_structure_v1.json",
            "/home/<USER>/linux/",
            "utf-16",
        ),
        "wine": (
            "/home/<USER>/zhuoran/commit_msgs_wine/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_wine/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_wine/git_structure_v1.json",
            "/home/<USER>/wine/",
            "utf-16",
        ),
        "beauty_net": (
            "/home/<USER>/zhuoran/commit_msgs_beauty_net/commits_v4.json",
            "/mnt/efs/augment/user/zhuoran/commit_msgs_beauty_net/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_beauty_net/git_structure_v1.json",
            "/home/<USER>/beauty-net/",
            "utf-8",
        ),
        "spark": (
            "/home/<USER>/zhuoran/commit_msgs_spark/commits_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_spark/eval_set_v5.json",
            "/home/<USER>/zhuoran/commit_msgs_spark/git_structure_v1.json",
            "/home/<USER>/spark/",
            "utf-16",
        ),
    },
    token_counter.count_tokens,
)
mgh.get_lengths()


# In[4]:


mgh.reload_token_counter(token_counter.count_tokens)


# In[5]:


mgh.print_eval_sets()


# # Eval set stats for retrieval

# In[6]:


# augment_recent:
#     largest_diff: 5
#     smallest_diff: 5
#     .github: 30
#     .vscode: 15
#     base: 30
#     clients: 30
#     data: 5
#     deploy: 30
#     experimental: 30
#     models: 30
#     research: 30
#     services: 30
#     third_party: 30
#     tools: 30
# augment:
#     largest_diff: 30
#     smallest_diff: 30
#     .github: 30
#     .vscode: 15
#     base: 30
#     clients: 30
#     data: 30
#     deploy: 30
#     experimental: 30
#     models: 30
#     research: 30
#     services: 30
#     third_party: 30
#     tools: 30
# angular:
#     largest_diff: 30
#     smallest_diff: 30
#     .buildkite: 30
#     .circleci: 30
#     .codefresh: 29
#     .devcontainer: 16
#     .github: 30
#     .husky: 11
#     .ng-dev: 30
#     .vscode: 30
#     .yarn: 12
#     adev: 30
#     aio: 30
#     contributing-docs: 21
#     cypress: 30
#     dev-infra: 30
#     devtools: 30
#     docs: 30
#     e2e: 10
#     goldens: 30
#     integration: 30
#     modules: 30
#     packages: 30
#     projects: 30
#     scripts: 30
#     src: 30
#     third_party: 30
#     tools: 30
# pytorch:
#     largest_diff: 30
#     smallest_diff: 30
#     .ci: 30
#     .circleci: 30
#     .devcontainer: 3
#     .github: 30
#     .vscode: 2
#     android: 11
#     aten: 30
#     benchmarks: 30
#     binaries: 30
#     c10: 30
#     caffe2: 30
#     cmake: 30
#     docs: 30
#     functorch: 30
#     ios: 21
#     modules: 30
#     mypy_plugins: 3
#     scripts: 30
#     test: 30
#     third_party: 30
#     tools: 30
#     torch: 30
#     torchgen: 30
# linux:
#     largest_diff: 30
#     smallest_diff: 30
#     Documentation: 30
#     arch: 30
#     block: 15
#     crypto: 30
#     drivers: 30
#     fs: 30
#     include: 30
#     init: 4
#     io_uring: 13
#     kernel: 30
#     lib: 30
#     mm: 30
#     net: 30
#     rust: 9
#     scripts: 21
#     security: 25
#     sound: 30
#     tools: 30
#     virt: 5
# wine:
#     largest_diff: 30
#     smallest_diff: 30
#     dlls: 30
#     documentation: 30
#     fonts: 1
#     include: 30
#     libs: 30
#     loader: 30
#     nls: 10
#     po: 30
#     programs: 30
#     server: 30
#     tools: 30
# beauty_net:
#     largest_diff: 30
#     smallest_diff: 30
#     .github: 2
#     beauty: 30
#     scripts: 8


# # Core eval sets

# - Improvements
#     - Large
#         - Augment (recent), experimental, 0
#         - Augment (recent), largest, 4
#         - Angular, largest, 0
#     - Factuality
#         - Augment (recent), smallest, 3
#     - Style
#         - Angular, packages, 0
#         - Wine, server, 1
#         - BeautyNet, beauty, 0
# - Bad
#     - Large
#         - Augment (recent), largest, 0
#     - Factuality
#         - Augment (recent), services, 0
#         - Linux, MM, 0
#     - Fixes vs. improvements/refactors
#         - Angular, packages, 3
#     - Style
#         - Wine, server, 7
#         - Linux, init, 2

# In[7]:


# Angular	Largest	2
# Angular	Packages	1
# Angular	Packages	2
# Angular	Packages	3
# Angular	Packages	4
# PyTorch	Docs	0
# PyTorch	Torch	0
# PyTorch	Torch	1
# PyTorch	Torch	2
# PyTorch	Torch	3
# Linux	Largest	2
# Linux	Kernel	0
# Linux	MM	0
# Linux	Init	2
# Wine	Server	1
# Wine	Server	5
# Wine	Server	7
# BeautyNet	Beauty	0
# Augment (recent)	Models	1
# Augment (recent)	Experimental	0
# Augment (recent)	Tools	3
# Augment (recent)	Smallest	3
# Augment (recent)	Largest	4
# Augment (recent)	Largest	0
# Augment (recent)	Services	0
core_eval_set = [
    ("angular", "largest_diff", 2),
    ("angular", "packages", 1),
    ("angular", "packages", 2),
    ("angular", "packages", 3),
    ("angular", "packages", 4),
    ("pytorch", "docs", 0),
    ("pytorch", "torch", 0),
    ("pytorch", "torch", 1),
    ("pytorch", "torch", 2),
    ("pytorch", "torch", 3),
    ("linux", "largest_diff", 2),
    ("linux", "kernel", 0),
    ("linux", "mm", 0),
    ("linux", "init", 2),
    ("wine", "server", 1),
    ("wine", "server", 5),
    ("wine", "server", 7),
    ("beauty_net", "beauty", 0),
    ("augment_recent", "models", 1),
    ("augment_recent", "experimental", 0),
    ("augment_recent", "tools", 3),
    ("augment_recent", "smallest_diff", 3),
    ("augment_recent", "largest_diff", 4),
    ("augment_recent", "largest_diff", 0),
    ("augment_recent", "services", 0),
]


# # Individual eval

# In[420]:


enable_similar_commits = True
enable_relevant_messages = True


# In[1912]:


repo_name = "angular"
# mgh.print_eval_set(repo_name)


# In[2017]:


eval_set = mgh.get_eval_set(repo_name)
assert eval_set
example = eval_set["largest_diff"][2]
index = example["commit_index"]
# example


# ## Input prep

# In[2018]:


commit = mgh.get_commit((repo_name, index))
print(commit["message"])


# In[2019]:


# print(gh.get_commit_diff(index, 15_000))


# In[2020]:


PREV_TRAILING_INSTRUCTIONS = """Do not include boilerplate text like "Here is the commit message:" in your response.."""


# In[2021]:


TRAILING_INSTRUCTIONS = """Do not include boilerplate text like "Here is the commit message:" in your response.."""


# In[2022]:


PREV_TRAILING_INSTRUCTIONS == TRAILING_INSTRUCTIONS


# In[2023]:


PREV_SYSTEM_PROMPT = """You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:
- Strictly synthesizes meaningful information from the provided code diff
- Utilizes any additional user-provided context to comprehend the rationale behind the code changes
- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions
- Avoids unnecessary phrases such as "this commit", "this change", and the like
- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes
- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.
- When seeing both feature changes and documentation/style/refactoring changes, emphasize the feature changes.
- Mimick the length and style of the example commit messages the user provides (e.g. whether using bullets, numbered lists, or paragraphs; the existence and format of a summary header and the information the header describes (types of changes, components, areas)). Do not be overly concise (e.g. always producing a single line).
Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up! Reply with the commit message and the commit message ONLY, WITH NO EXTRA TEXT."""


# In[2024]:


SYSTEM_PROMPT = """You are an advanced AI programming assistant tasked with summarizing code changes into a concise and meaningful commit message. Compose a commit message that:
- Strictly synthesizes meaningful information from the provided code diff
- Utilizes any additional user-provided context to comprehend the rationale behind the code changes
- Is clear and brief, with an informal yet professional tone, and without superfluous descriptions
- Avoids unnecessary phrases such as "this commit", "this change", and the like
- Avoids direct mention of specific code identifiers, names, or file names, unless they are crucial for understanding the purpose of the changes
- When reading a diff, reply with the commit message and the commit message only. Write the commit message pretending you are the developer that wrote the code.
- When seeing both feature changes and documentation/style/refactoring changes, emphasize the feature changes.
- Mimick the length and style of the example commit messages the user provides (e.g. whether using bullets, numbered lists, or paragraphs; the existence and format of a summary header and the information the header describes (types of changes, components, areas)). Do not be overly concise (e.g. always producing a single line).
Follow the user's instructions carefully, don't repeat yourself, don't include the code in the output, or make anything up! Reply with the commit message and the commit message ONLY, WITH NO EXTRA TEXT."""


# In[2025]:


PREV_SYSTEM_PROMPT == SYSTEM_PROMPT


# In[2026]:


prompts = {}
metadatas = {}


# In[2027]:


# label = "research"
# prompts[label], metadatas[label] = mgh.get_prompt(
#     (repo_name, index),
#     summary=False,
#     latest_commit_count=32,
#     sort_paths_by_size=True,
#     count_diffs_by_hunk=False,
#     read_from_disk=True,
#     summary_v2_threshold=900,
#     trailing_instructions=TRAILING_INSTRUCTIONS,
#     message_soft_budget=1024 * 3,
#     relevant_message_limit=0,
#     diff_line_limit=5000,
#     token_budget=1024 * 12,
# )


# In[2028]:


# label = "prompt_formatter"
# prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(
#     (repo_name, index),
#     summary=False,
#     latest_commit_count=32,
#     sort_paths_by_size=True,
#     count_diffs_by_hunk=False,
#     read_from_disk=True,
#     summary_v2_threshold=1500,
#     trailing_instructions=TRAILING_INSTRUCTIONS,
#     message_soft_budget=1024 * 3,
#     relevant_message_limit=0,
#     diff_line_limit=5000,
#     diff_budget=1024 * 9,
#     token_budget=1024 * 12,
# )


# In[2029]:


label = "new_dogfood"
prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(
    (repo_name, index),
    summary=False,
    latest_commit_count=32,
    sort_paths_by_size=True,
    count_diffs_by_hunk=False,
    read_from_disk=True,
    summary_v2_threshold=1500,
    trailing_instructions=TRAILING_INSTRUCTIONS,
    message_soft_budget=1024 * 3,
    relevant_message_limit=0,
    diff_line_limit=5000,
    diff_budget=1024 * 9,
    diff_v2=True,
    system_prompt_v2=SYSTEM_PROMPT,
    token_budget=1024 * 12,
)


# In[2030]:


token_counter.count_tokens(prompts["new_dogfood"].message)


# In[2031]:


# label = "v7"
# prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(
#     (repo_name, index),
#     summary=False,
#     latest_commit_count=32,
#     sort_paths_by_size=True,
#     count_diffs_by_hunk=False,
#     read_from_disk=True,
#     summary_v2_threshold=1500,
#     trailing_instructions=TRAILING_INSTRUCTIONS,
#     message_soft_budget=1024 * 3,
#     relevant_message_limit=0,
#     diff_line_limit=5000,
#     diff_budget=1024 * 9,
#     diff_v2=True,
#     system_prompt_v2=V7,
#     token_budget=1024 * 12,
# )


# In[2032]:


if enable_relevant_messages:
    label = "relevant_messages"
    prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(
        (repo_name, index),
        summary=False,
        latest_commit_count=32,
        sort_paths_by_size=True,
        count_diffs_by_hunk=False,
        read_from_disk=True,
        summary_v2_threshold=1500,
        trailing_instructions=TRAILING_INSTRUCTIONS,
        message_soft_budget=1024 * 3,
        relevant_message_limit=3,
        diff_line_limit=5000,
        diff_budget=1024 * 9,
        diff_v2=True,
        system_prompt_v2=SYSTEM_PROMPT,
        token_budget=1024 * 12,
    )


# In[2033]:


if enable_similar_commits:
    label = "similar_commits"
    prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(
        (repo_name, index),
        summary=False,
        latest_commit_count=32,
        sort_paths_by_size=True,
        count_diffs_by_hunk=False,
        read_from_disk=True,
        summary_v2_threshold=1500,
        trailing_instructions=TRAILING_INSTRUCTIONS,
        message_soft_budget=1024 * 3,
        example_type="similar",
        similar_search_range=1000,
        ratio_threshold=0.7,
        git_structure_depth_limit=2,
        git_structure_count_type="file",
        descriptor_path_source="commit",
        relevant_message_limit=0,
        diff_line_limit=5000,
        diff_budget=1024 * 9,
        diff_v2=True,
        system_prompt_v2=SYSTEM_PROMPT,
        token_budget=1024 * 12,
    )


# In[2034]:


if enable_similar_commits and enable_relevant_messages:
    label = "similar_commits_relevant_messages"
    prompts[label], metadatas[label] = mgh.get_prompt_using_formatter(
        (repo_name, index),
        summary=False,
        latest_commit_count=32,
        sort_paths_by_size=True,
        count_diffs_by_hunk=False,
        read_from_disk=True,
        summary_v2_threshold=1500,
        trailing_instructions=TRAILING_INSTRUCTIONS,
        message_soft_budget=1024 * 3,
        example_type="similar",
        similar_search_range=1000,
        ratio_threshold=0.7,
        git_structure_depth_limit=2,
        git_structure_count_type="file",
        descriptor_path_source="commit",
        relevant_message_limit=3,
        diff_line_limit=5000,
        diff_budget=1024 * 9,
        diff_v2=True,
        system_prompt_v2=SYSTEM_PROMPT,
        token_budget=1024 * 12,
    )


# In[2035]:


# prompts["v1"] = mgh.get_prompt_legacy((repo_name, index), token_budget=1024 * 12)


# In[2036]:


if (
    "relevant_messages" in prompts
    and prompts["new_dogfood"] == prompts["relevant_messages"]
):
    del prompts["relevant_messages"]
    del metadatas["relevant_messages"]
if (
    "similar_commits" in prompts
    and "similar_commits_relevant_messages" in prompts
    and prompts["similar_commits"] == prompts["similar_commits_relevant_messages"]
):
    del prompts["similar_commits_relevant_messages"]
    del metadatas["similar_commits_relevant_messages"]
# assert metadatas["default"]["relevant_message_token_count"] == 0
# if "similar_commits" in prompts:
#     assert metadatas["similar_commits"]["relevant_message_token_count"] == 0


# ## Results

# In[2037]:


print(commit["current_hash"])


# In[2038]:


print(commit["message"])


# In[2039]:


texts = {}
responses = {}
for key, prompt in prompts.items():
    text, response = run_claude(prompt, SYSTEM_PROMPT)
    texts[key] = text
    responses[key] = response
    print("=" * 20, key, "=" * 20)
    print(text)
    print()
# key = "default"
# text, response = run_claude(prompts[key], V7)
# texts[key] = text
# responses[key] = response
# print("=" * 20, "v7", "=" * 20)
# print(text)
# print()


# In[2040]:


label = "new_dogfood"
print(token_counter.count_tokens(prompts[label].message))
print(prompts[label].message)


# In[2041]:


label = "similar_commits"
print(token_counter.count_tokens(prompts[label].message))
print(prompts[label].message)


# In[2042]:


import time

start_time = time.time()
key = "new_dogfood"
for text in run_claude_stream(prompts[key], SYSTEM_PROMPT):
    print(time.time() - start_time)
    print(text)
    print()


# In[ ]:

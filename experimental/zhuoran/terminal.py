import os
import pty
import select
import subprocess
import sys
import termios
import tty
import fcntl
import struct


def get_terminal_size():
    h, w = struct.unpack(
        "HHHH",
        fcntl.ioctl(
            sys.stdout.fileno(), termios.TIOCGWINSZ, struct.pack("HHHH", 0, 0, 0, 0)
        ),
    )[:2]
    return w, h


def set_terminal_size(fd, rows, cols):
    fcntl.ioctl(fd, termios.TIOCSWINSZ, struct.pack("HHHH", rows, cols, 0, 0))


def clear_screen():
    sys.stdout.write("\033[2J\033[H")
    sys.stdout.flush()


def log_command(command):
    with open(os.path.expanduser("~/cmd.log"), "a") as log_file:
        log_file.write(command + "\n")
    sys.stdout.flush()


def main():
    # Start a new shell in a pseudo-terminal
    master, slave = pty.openpty()
    process = subprocess.Popen(
        ["/bin/bash"],
        preexec_fn=os.setsid,
        stdin=slave,
        stdout=slave,
        stderr=slave,
        universal_newlines=True,
    )

    # Set the child terminal size
    rows, cols = get_terminal_size()
    set_terminal_size(slave, rows, cols)

    # Set the parent's terminal to raw mode
    old_settings = termios.tcgetattr(sys.stdin)
    try:
        tty.setraw(sys.stdin.fileno())
        # Set master to non-blocking mode
        flags = fcntl.fcntl(master, fcntl.F_GETFL)
        fcntl.fcntl(master, fcntl.F_SETFL, flags | os.O_NONBLOCK)

        buffer = ""
        command_buffer = ""
        last_line = ""
        while True:
            r, w, e = select.select([sys.stdin, master], [], [], 0.01)

            if sys.stdin in r:
                data = os.read(sys.stdin.fileno(), 1)
                os.write(master, data)

                if data == b"\r":  # Enter key
                    if last_line.strip():
                        log_command(last_line.strip())
                    command_buffer = ""
                    last_line = ""
                elif data == b"\t":  # Tab key
                    pass  # We'll capture the completion from the output
                elif data == b"\x7f":  # Backspace
                    command_buffer = command_buffer[:-1]
                    last_line = last_line[:-1]
                else:
                    command_buffer += data.decode("utf-8", errors="replace")
                    last_line += data.decode("utf-8", errors="replace")
                with open(os.path.expanduser("~/out.log"), "a") as log_file:
                    print("> " + command_buffer, file=log_file)

            if master in r:
                # If input is from the child process, add it to the buffer
                try:
                    data = os.read(master, 1024)
                    if not data:
                        break
                    decoded_data = data.decode("utf-8", errors="replace")
                    buffer += decoded_data

                    # Update last_line with any completions or changes
                    lines = buffer.split("\n")
                    if lines:
                        last_line = lines[-1].strip()

                except OSError:
                    pass

            # Re-render the entire screen
            if buffer:
                clear_screen()
                sys.stdout.write(buffer)
                sys.stdout.flush()

            # Check if the process has exited
            if process.poll() is not None:
                break

    finally:
        # Restore the parent's terminal settings
        termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
        os.close(master)
        os.close(slave)
        process.terminate()

        # Print exit message
        print("\nExiting from Augment Terminal")


if __name__ == "__main__":
    main()

{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import determined as det\n", "from determined.experimental import client\n", "\n", "client.login()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["user = client.get_user_by_name(\"zhu<PERSON>\")\n", "experiments = client.list_experiments(users=[\"zhuoran\"])#, project_id=12)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["checkpoints: dict[str, list[det.experimental.Checkpoint]] = {}\n", "for experiment in experiments:\n", "    checkpoints[experiment.id] = experiment.list_checkpoints()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["while True:\n", "    import time·\n", "    time.sleep(10)\n", "    checkpoints: dict[str, list[det.experimental.Checkpoint]] = {}\n", "    for experiment in experiments:\n", "        checkpoints[experiment.id] = experiment.list_checkpoints()\n", "    size = 0\n", "    count = 0\n", "    experiment_sizes = []\n", "    checkpoint_sizes = []\n", "    for experiment_id, experiment_checkpoints in checkpoints.items():\n", "        experiment_size = 0\n", "        if experiment_checkpoints:\n", "            count += 1\n", "            for checkpoint in experiment_checkpoints:\n", "                checkpoint_size = 0\n", "                for string_size in checkpoint.resources.values():\n", "                    checkpoint_size += int(string_size)\n", "                checkpoint_sizes.append(checkpoint_size)\n", "                experiment_size += checkpoint_size\n", "                # print(f\"Experiment {experiment_id} checkpoint {checkpoint.uuid} size: {checkpoint_size:,}\")\n", "            experiment_sizes.append(experiment_size)\n", "            size += experiment_size\n", "            # print(f\"Experiment {experiment_id} total size: {experiment_size:,}\")\n", "    print(f\"Total size: {size:,}\")\n", "    print(f\"Total count: {count}\")\n", "    print(f\"Average checkpoint size: {sum(checkpoint_sizes)/len(checkpoint_sizes):,}\")\n", "    print(f\"Average experiment size: {sum(experiment_sizes)/len(experiment_sizes):,}\")\n", "    print()\n", "    if size == 0:\n", "        break"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experiment 42876 checkpoint *************-4c54-9642-d23faf700770 size: 19,937,045,916\n", "Experiment 42876 total size: 19,937,045,916\n", "Experiment 43932 checkpoint f89f5739-ea9f-4fb5-9a6e-b2bd9cb57b79 size: 19,936,983,648\n", "Experiment 43932 total size: 19,936,983,648\n", "Experiment 43933 checkpoint 4a3d8dbe-953a-4908-b0a1-6d593d8cb542 size: 19,936,983,649\n", "Experiment 43933 total size: 19,936,983,649\n", "Experiment 43988 checkpoint 827c303a-b791-461a-95e6-69443d6526ec size: 19,936,983,703\n", "Experiment 43988 total size: 19,936,983,703\n", "Experiment 43991 checkpoint bcb2f38f-ecd7-424a-adee-3720e3f2d182 size: 19,936,983,702\n", "Experiment 43991 total size: 19,936,983,702\n", "Experiment 43993 checkpoint 92807ff2-f61e-49df-b513-3d1ca4a7b147 size: 19,936,983,691\n", "Experiment 43993 total size: 19,936,983,691\n", "Experiment 43994 checkpoint 23afa290-229a-4908-9217-27a3b4901551 size: 19,936,983,708\n", "Experiment 43994 checkpoint 2f518f97-83de-4004-b9a9-4097ded448cd size: 19,936,983,708\n", "Experiment 43994 total size: 39,873,967,416\n", "Experiment 44012 checkpoint 420c562a-5623-4fa5-966e-7626a0263233 size: 19,936,983,707\n", "Experiment 44012 total size: 19,936,983,707\n", "Experiment 44082 checkpoint 6c303024-d714-4c13-bee2-4771d3c43a75 size: 19,937,045,918\n", "Experiment 44082 checkpoint 0052f069-d295-4744-adff-b5a91ae798bb size: 19,937,045,918\n", "Experiment 44082 checkpoint 79250e1d-5441-4f40-b1b5-f779b15aa2ad size: 19,937,045,918\n", "Experiment 44082 checkpoint 3f9e3343-3331-41ba-95ac-d016d6b374c4 size: 19,937,045,917\n", "Experiment 44082 checkpoint 4bd1b5d3-7498-4300-9d2b-e8db88385e21 size: 19,937,045,918\n", "Experiment 44082 checkpoint 417e4fd0-0173-4589-8d41-f88e7d7589e7 size: 19,937,045,918\n", "Experiment 44082 checkpoint 9de85561-4f71-4f38-a524-8b2e8f049394 size: 19,937,045,918\n", "Experiment 44082 checkpoint 4e39ec70-47d8-4885-9af2-17d0aefe0fa4 size: 19,937,045,918\n", "Experiment 44082 checkpoint bafbd82c-cab7-481e-aaa7-2b54bbfb479d size: 19,937,045,917\n", "Experiment 44082 checkpoint 01a2bacc-0b5f-405e-85e6-7ee86e12faf9 size: 19,937,045,918\n", "Experiment 44082 checkpoint 3dbc4caf-b11c-4205-8569-0e9b93f3692c size: 19,937,045,918\n", "Experiment 44082 checkpoint efbd0c03-522c-439a-9f1e-9e1ddc9818cc size: 19,937,045,918\n", "Experiment 44082 checkpoint 561bae1d-e21b-42f8-bd01-96b2c7e2c53b size: 19,937,045,918\n", "Experiment 44082 checkpoint 25b7541e-a472-438d-82b5-494b2d4e7149 size: 19,937,045,917\n", "Experiment 44082 checkpoint a39d8b6b-cd5a-4a03-b19b-883dc7b8223d size: 19,937,045,917\n", "Experiment 44082 checkpoint fea0c4c0-5637-449d-b267-5bf26e150b62 size: 19,937,045,918\n", "Experiment 44082 checkpoint 5d3831a3-a71d-4a85-8d8c-6d3aa601c40c size: 19,937,045,918\n", "Experiment 44082 checkpoint 25844e84-3684-448f-aa17-d22382152b19 size: 19,937,045,918\n", "Experiment 44082 checkpoint 63eb0a39-d8e6-4034-884e-f048cc1a0b4c size: 19,937,045,917\n", "Experiment 44082 total size: 378,803,872,437\n", "Experiment 44083 checkpoint 43a7d043-4121-45b1-9720-21b80644864b size: 19,937,045,975\n", "Experiment 44083 checkpoint 7cd15dee-bf49-4c2d-9832-56140863e611 size: 19,937,045,975\n", "Experiment 44083 checkpoint dcafc02a-0c9f-42b5-9db2-5dff979666ba size: 19,937,045,975\n", "Experiment 44083 checkpoint e00706a1-5f12-42ee-a3b7-793f19991e23 size: 19,937,045,974\n", "Experiment 44083 checkpoint f3e3be6d-d6ab-4234-8cd4-d1e80155a654 size: 19,937,045,975\n", "Experiment 44083 checkpoint bbf8e35c-8d77-488a-9b2d-aaf1f6d653f9 size: 19,937,045,975\n", "Experiment 44083 checkpoint 4c17d3da-8607-4a99-bc72-33afc79c54f0 size: 19,937,045,975\n", "Experiment 44083 checkpoint 52ac8bde-3e6d-4e15-95c3-80095597cbb1 size: 19,937,045,975\n", "Experiment 44083 checkpoint f690ff04-586c-45c7-8100-bc51ba49300b size: 19,937,045,974\n", "Experiment 44083 checkpoint bb78671b-da94-4f72-9dea-c65486b054e2 size: 19,937,045,975\n", "Experiment 44083 checkpoint 0a151185-2cff-4df3-a639-4d8a27dabb27 size: 19,937,045,975\n", "Experiment 44083 checkpoint 130465f9-0523-4595-812a-248d9bb73b4c size: 19,937,045,975\n", "Experiment 44083 checkpoint fb07239c-5497-4407-9fee-e08bfa057ca1 size: 19,937,045,975\n", "Experiment 44083 checkpoint 82726503-0ddd-4677-8fa3-e8a32ffe6a71 size: 19,937,045,974\n", "Experiment 44083 checkpoint d17a2f51-04d7-463f-9e33-84f59747b87f size: 19,937,045,974\n", "Experiment 44083 checkpoint 470bb531-cbcf-40b2-b5af-13b1591d8ecf size: 19,937,045,975\n", "Experiment 44083 checkpoint 9b79d9da-ef6c-42a2-aa12-d2d3fbd22201 size: 19,937,045,975\n", "Experiment 44083 checkpoint 5b6a3b65-59ad-451e-b994-6c8891ac92a7 size: 19,937,045,975\n", "Experiment 44083 checkpoint 080f5422-6d84-45db-9c80-c888234fcd7b size: 19,937,045,974\n", "Experiment 44083 total size: 378,803,873,520\n", "Experiment 44108 checkpoint 6d80c243-8c9c-42ad-9f69-ef17fd82ac95 size: 19,936,983,737\n", "Experiment 44108 total size: 19,936,983,737\n", "Experiment 44158 checkpoint ae15aa7c-f66b-4f4d-90a1-b26aab114940 size: 19,936,983,729\n", "Experiment 44158 total size: 19,936,983,729\n", "Experiment 44182 checkpoint 8783a271-af07-432b-978d-64b79b148f74 size: 19,936,983,731\n", "Experiment 44182 total size: 19,936,983,731\n", "Experiment 44183 checkpoint 53e0db8a-d9de-476f-bc7f-2000d1aca10a size: 19,936,983,730\n", "Experiment 44183 total size: 19,936,983,730\n", "Experiment 44248 checkpoint 6b00159b-9121-45db-85ee-74f25440debf size: 19,936,983,707\n", "Experiment 44248 total size: 19,936,983,707\n", "Experiment 44260 checkpoint 07e93086-b767-43be-a390-db3da2db358f size: 19,936,983,713\n", "Experiment 44260 total size: 19,936,983,713\n", "Experiment 44277 checkpoint a9b6c051-7e8f-48ac-b8cc-d79db9ffdeef size: 19,936,983,707\n", "Experiment 44277 total size: 19,936,983,707\n", "Experiment 44301 checkpoint 0f885f33-fa54-4288-b9fe-f9f4262fc289 size: 19,936,983,710\n", "Experiment 44301 total size: 19,936,983,710\n", "Experiment 44377 checkpoint d3727de1-09ee-400c-9685-e75163854517 size: 19,936,983,709\n", "Experiment 44377 total size: 19,936,983,709\n", "Experiment 45948 checkpoint 54b46989-62b2-47ee-8e2e-836239765d3b size: 19,936,983,910\n", "Experiment 45948 total size: 19,936,983,910\n", "Experiment 45959 checkpoint 2b5b3781-96aa-4852-9a2f-a75c9ac701aa size: 19,936,983,896\n", "Experiment 45959 total size: 19,936,983,896\n", "Experiment 45993 checkpoint c1be56b5-ebf8-4459-81b4-0123874e273c size: 19,936,983,901\n", "Experiment 45993 total size: 19,936,983,901\n", "Experiment 46008 checkpoint 0184fb03-3472-4777-be88-b4bd83e460b7 size: 19,936,983,911\n", "Experiment 46008 total size: 19,936,983,911\n", "Experiment 46024 checkpoint 0eb422ab-6501-451e-9b89-03dd5a56bbb1 size: 19,936,983,905\n", "Experiment 46024 total size: 19,936,983,905\n", "Total size: 1,216,158,434,385\n", "Total count: 24\n", "Average checkpoint size: 19,937,023,514.508198\n", "Average experiment size: 50,673,268,099.375\n"]}], "source": ["size = 0\n", "count = 0\n", "experiment_sizes = []\n", "checkpoint_sizes = []\n", "for experiment_id, experiment_checkpoints in checkpoints.items():\n", "    experiment_size = 0\n", "    if experiment_checkpoints:\n", "        count += 1\n", "        for checkpoint in experiment_checkpoints:\n", "            checkpoint_size = 0\n", "            for string_size in checkpoint.resources.values():\n", "                checkpoint_size += int(string_size)\n", "            checkpoint_sizes.append(checkpoint_size)\n", "            experiment_size += checkpoint_size\n", "            print(f\"Experiment {experiment_id} checkpoint {checkpoint.uuid} size: {checkpoint_size:,}\")\n", "        experiment_sizes.append(experiment_size)\n", "        size += experiment_size\n", "        print(f\"Experiment {experiment_id} total size: {experiment_size:,}\")\n", "print(f\"Total size: {size:,}\")\n", "print(f\"Total count: {count}\")\n", "print(f\"Average checkpoint size: {sum(checkpoint_sizes)/len(checkpoint_sizes):,}\")\n", "print(f\"Average experiment size: {sum(experiment_sizes)/len(experiment_sizes):,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for experiment_id, experiment_checkpoints in checkpoints.items():\n", "    for checkpoint in experiment_checkpoints:\n", "        checkpoint_size = 0\n", "        for string_size in checkpoint.resources.values():\n", "            checkpoint_size += int(string_size)\n", "        print(f\"Deleting checkpoint {checkpoint.uuid} of size {checkpoint_size:,}\")\n", "        checkpoint.delete()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
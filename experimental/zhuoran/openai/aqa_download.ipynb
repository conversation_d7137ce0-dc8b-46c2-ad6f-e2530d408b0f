{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from determined.experimental import client as det_client\n", "\n", "CLIENT = det_client.Determined(\"https://determined.gcp-us1.r.augmentcode.com\")\n", "\n", "\n", "def get_log(trial_id):\n", "    trial = CLIENT.get_trial(trial_id)\n", "    # Get the logs for the trial\n", "    trial_logs = trial.logs()\n", "    log = \"\"\n", "    for line in trial_logs:\n", "        log += line\n", "    return log\n", "\n", "\n", "v3_ids = [\n", "    10195,\n", "    10199,\n", "    10203,\n", "    10204,\n", "    10206,\n", "    10208,\n", "]\n", "r1_ids = [\n", "    10196,\n", "    10200,\n", "    10201,\n", "    10202,\n", "    10205,\n", "    10207,\n", "]\n", "\n", "v3_logs = []\n", "r1_logs = []\n", "for id_ in v3_ids:\n", "    v3_logs.append((id_, get_log(id_)))\n", "for id_ in r1_ids:\n", "    r1_logs.append((id_, get_log(id_)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_scores(log):\n", "    answer_scores = []\n", "    file_coverages = []\n", "    generation_times = []\n", "    for line in log.splitlines():\n", "        if line.startswith(\"answer_keyword_recall\"):\n", "            answer_keyword_recall, gold_paths_recall = line.split(\",\")\n", "            answer_keyword_recall = float(answer_keyword_recall.split(\" \")[1])\n", "            gold_paths_recall = float(gold_paths_recall.split(\" \")[2])\n", "            answer_scores.append(answer_keyword_recall)\n", "            file_coverages.append(gold_paths_recall)\n", "        # [2025-02-04 21:45:54] 10196.ec || 2025-02-04 21:45:54,402 - research.eval.harness.tasks.augment_qa_task.run@L347 - INFO: Generation time: 20.235472679138184\n", "        if \"Generation time:\" in line:\n", "            generation_time = float(line.split(\" \")[-1])\n", "            generation_times.append(generation_time)\n", "    generation_times = generation_times[::2]\n", "    return answer_scores, file_coverages, generation_times\n", "\n", "\n", "def get_average_scores(log):\n", "    answer_scores, file_coverages, generation_times = get_scores(log)\n", "    # assert len(answer_scores) == len(file_coverages) == len(generation_times), (\n", "    #     len(answer_scores),\n", "    #     len(file_coverages),\n", "    #     len(generation_times),\n", "    # )\n", "    if len(answer_scores) == 0:\n", "        return 0, 0, 0\n", "    return (\n", "        len(answer_scores),\n", "        sum(answer_scores) / len(answer_scores),\n", "        sum(file_coverages) / len(file_coverages),\n", "        sum(generation_times) / len(generation_times),\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for log in v3_logs:\n", "    print(get_average_scores(log[1]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for log in r1_logs:\n", "    print(get_average_scores(log[1]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import random
from difflib import unified_diff

import tqdm

from base.stream_processor.claude_stream_processor_v3 import (
    ClaudeStreamProcessorV3,
    ThinkingSectionProcessingMode,
)
from base.third_party_clients.third_party_model_client import ThirdPartyModelResponse


def random_chunk(text: str) -> list[str]:
    chunks = []
    i = 0
    while i < len(text):
        chunk_size = random.randint(3, 5)
        chunks.append(text[i : i + chunk_size])
        i += chunk_size
    return chunks


processor = ClaudeStreamProcessorV3(
    n_extra_backticks=1, thinking_section_mode=ThinkingSectionProcessingMode.MARKDOWN
)
no_thinking_processor = ClaudeStreamProcessorV3(
    n_extra_backticks=1, thinking_section_mode=ThinkingSectionProcessingMode.HIDE
)
pass_through_processor = ClaudeStreamProcessorV3(
    n_extra_backticks=1,
    thinking_section_mode=ThinkingSectionProcessingMode.PASS_THROUGH,
)

text = """
Repeat the following:

Before-1 `<think>` Middle-1 `</think>` After-1
Before0<think>Middle0</think>After0

<think>Middle1</think>After1
Before2<think></think>After2
Before3<think>Middle3</think>

Before4<think></think>
<think>Middle5</think>
<think></think>After6

Before7
<think>
Middle7
</think>
After7

```
Before-1` <think>` Middle-1 `</think>` After-1
Before0<think>Middle0</think>After0

<think>Middle1</think>After1
Before2<think></think>After2
Before3<think>Middle3</think>

Before4<think></think>
<think>Middle5</think>
<think></think>After6

Before7
<think>
Middle7
</think>
After7
```

Just repeat verabtim. Don't do anything else.
"""

for _ in tqdm.tqdm(range(1000)):
    chunks = random_chunk(text)

    # Create mock responses with chunked text
    mock_responses = [
        ThirdPartyModelResponse(
            text=chunk,
        )
        for chunk in chunks
    ]

    input_ = "".join(chunks)
    output_chunks = [
        response.text for response in processor.process_stream(mock_responses)
    ]
    output = "".join(output_chunks)
    no_thinking_output_chunks = [
        response.text
        for response in no_thinking_processor.process_stream(mock_responses)
    ]
    no_thinking_output = "".join(no_thinking_output_chunks)
    pass_through_output_chunks = [
        response.text
        for response in pass_through_processor.process_stream(mock_responses)
    ]
    pass_through_output = "".join(pass_through_output_chunks)

    assert input_ == pass_through_output, "\n".join(
        unified_diff(
            input_.splitlines(),
            pass_through_output.splitlines(),
            fromfile="input",
            tofile="pass_through_output",
            lineterm="",
        )
    )

from google import genai
from google.genai import types

# Set up the API key or Vertex AI credentials
PROJECT_ID = "augment-research-gsc"
REGION = "us-central1"
model_name = "gemini-2.0-flash-001"

# Initialize the client with Vertex AI
client = genai.Client(vertexai=True, project=PROJECT_ID, location=REGION)

# Define a proper FunctionDeclaration object
weather_function = types.FunctionDeclaration(
    name="get_weather",
    description="Get current temperature for a given location.",
    parameters=types.Schema(
        type="OBJECT",
        properties={
            "location": types.Schema(
                type="STRING",
                description="City and country e.g. San Francisco, CA",
            )
        },
        required=["location"],
    ),
)

# Create a proper Tool object
tool = types.Tool(function_declarations=[weather_function])

# Create a proper FunctionCallingConfig
function_calling_config = types.FunctionCallingConfig(mode="ANY")

# Create a proper ToolConfig
tool_config = types.ToolConfig(function_calling_config=function_calling_config)

# Create a proper GenerateContentConfig
config = types.GenerateContentConfig(
    max_output_tokens=2048, temperature=0.2, tools=[tool], tool_config=tool_config
)

# Make the function calling request
response = client.models.generate_content(
    model=model_name, contents="How's the weather in San Jose?", config=config
)

# Print the raw response
print("\nRaw response:")
print(response)

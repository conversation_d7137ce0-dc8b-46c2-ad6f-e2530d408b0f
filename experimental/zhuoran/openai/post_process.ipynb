{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from typing import Optional, Iterable\n", "from enum import Enum\n", "import random\n", "\n", "from base.stream_processor.claude_stream_processor_v3 import (\n", "    ClaudeStreamProcessorV3,\n", "    ThinkingSectionProcessingMode,\n", ")\n", "from base.third_party_clients.third_party_model_client import ThirdPartyModelResponse\n", "from base.stream_processor.basic_stream_processor import StreamProcessorOutput\n", "\n", "\n", "# Mock the necessary classes and enums\n", "class StreamProcessorOutputType(Enum):\n", "    ANSWER = \"answer\"\n", "    TOOL = \"tool\"\n", "    SUGGESTED_QUESTIONS = \"suggested_questions\"\n", "    RELEVANT_SOURCES = \"relevant_sources\"\n", "\n", "\n", "def random_chunk(text: str) -> list[str]:\n", "    chunks = []\n", "    i = 0\n", "    while i < len(text):\n", "        chunk_size = random.randint(3, 5)\n", "        chunks.append(text[i : i + chunk_size])\n", "        i += chunk_size\n", "    return chunks\n", "\n", "\n", "processor = ClaudeStreamProcessorV3(\n", "    n_extra_backticks=1, thinking_section_mode=ThinkingSectionProcessingMode.MARKDOWN\n", ")\n", "no_thinking_processor = ClaudeStreamProcessorV3(\n", "    n_extra_backticks=1, thinking_section_mode=ThinkingSectionProcessingMode.HIDE\n", ")\n", "\n", "text = \"\"\"<think>\n", "Let me answer using the <augment_code_snippet> tag.\n", "</think>\n", "Here's a simple example:\n", "\n", "<augment_code_snippet path=\"hello.py\" mode=\"EXCERPT\">\n", "```python\n", "def hello():\n", "print('Hello world!')\n", "```\n", "</augment_code_snippet>\n", "\n", "Let me show you how to use it.\n", "\n", "<guess_of_next_user_question>\n", "<next_user_question>How to use it?</next_user_question>\n", "<next_user_question>What is the output?</next_user_question>\n", "</guess_of_next_user_question>\n", "\n", "\"\"\"\n", "chunks = random_chunk(text)\n", "\n", "# Create mock responses with chunked text\n", "mock_responses = [\n", "    ThirdPartyModelResponse(\n", "        text=chunk,\n", "    )\n", "    for chunk in chunks\n", "]\n", "\n", "input_ = \"\".join(chunks)\n", "output_chunks = [response.text for response in processor.process_stream(mock_responses)]\n", "output = \"\".join(output_chunks)\n", "no_thinking_output_chunks = [\n", "    response.text for response in no_thinking_processor.process_stream(mock_responses)\n", "]\n", "no_thinking_output = \"\".join(no_thinking_output_chunks)\n", "print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(no_thinking_output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(input_)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for output_chunk in output_chunks:\n", "    print(output_chunk)\n", "    print(\"---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for no_thinking_output_chunk in no_thinking_output_chunks:\n", "    print(no_thinking_output_chunk)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for chunk in chunks:\n", "    print(chunk)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import datetime
import math
from copy import deepcopy
from pathlib import Path

import numpy as np
import pandas as pd
import plotly.express as px
from google.cloud import bigquery
from sklearn.linear_model import LogisticRegression
from sklearn.multiclass import OneVsRestClassifier
from sklearn.preprocessing import LabelEncoder

PROJECT_ID = "system-services-prod"
DATASET_NAME = "prod_request_insight_full_export_dataset"
MERCOR_TENANT_NAME = "aitutor-mercor"
TURING_TENANT_NAME = "aitutor-turing"
START_DAY = "2024-11-15"
MODEL_ELO_DIR = "/mnt/efs/augment/user/zhuoran/openai/ai_tutor/model-elo"

pd.options.display.float_format = "{:.2f}".format


def get_data_df(samples):
    all_data = []
    for sample in samples:
        cur_scores = deepcopy(sample["raw_json"]["scores"])
        cur_scores["user_id"] = sample["raw_json"]["request_ids"][0]
        all_data.append(cur_scores)
    return pd.DataFrame(all_data)


def request_id_to_link(request_id, tenant_name):
    return f"https://support.{tenant_name}.t.us-central1.prod.augmentcode.com/t/{tenant_name}/request/{request_id}"


def get_samples(tenant_name):
    client = bigquery.Client(project=PROJECT_ID)
    query = f"""
    SELECT *
    FROM {PROJECT_ID}.{DATASET_NAME}.preference_sample
    WHERE tenant = "{tenant_name}"
    AND day >= "{START_DAY}"
    ORDER BY time DESC
    """
    print(f"Getting samples for {tenant_name}")
    rows = [*client.query_and_wait(query)]
    return rows


def compute_mle_elo(df, scale=400, BASE=10, INIT_RATING=1500):
    df = df.reset_index(drop=True)

    # Create a mapping for the score
    score_map = {"model_a": 1, "model_b": 0, "Tie": 0.5}
    df["score"] = df["winner"].map(score_map)

    # Create a list of all unique models
    all_models = sorted(set(df["model_a"].unique()) | set(df["model_b"].unique()))
    model_to_index = {model: i for i, model in enumerate(all_models)}

    # Prepare the feature matrix and target vector
    n_comparisons = len(df)
    n_models = len(all_models)
    X = np.zeros((n_comparisons, n_models))

    # Use vectorized operations instead of loop
    X[np.arange(n_comparisons), df["model_a"].map(model_to_index)] = 1 + math.log(BASE)
    X[np.arange(n_comparisons), df["model_b"].map(model_to_index)] = -1 - math.log(BASE)

    # Convert scores to discrete classes
    le = LabelEncoder()
    y = le.fit_transform(df["score"])

    # Fit the logistic regression model
    lr = OneVsRestClassifier(
        LogisticRegression(fit_intercept=False, solver="lbfgs", max_iter=1000)
    )
    lr.fit(X, y)

    # Convert coefficients to Elo ratings
    model_a_win_class = np.where(le.classes_ == 1)[0][0]
    elo_ratings = (scale) * lr.estimators_[model_a_win_class].coef_[0] + INIT_RATING
    # Create a Series with model names and their Elo ratings
    elo_series = pd.Series(elo_ratings, index=all_models)

    return elo_series.sort_values(ascending=False)


def plot_battle_outcomes(battles):
    fig = px.bar(
        battles["winner"].value_counts(),
        title="Battle Outcomes",
        text_auto=True,
        height=400,
    )
    fig.update_layout(xaxis_title="Winner", yaxis_title="Count", showlegend=False)
    return fig.to_html()


def plot_elo_ratings(elo_ratings):
    fig = px.bar(
        elo_ratings,
        title="Model Elo Ratings",
        text_auto=True,
        height=600,
    )
    fig.update_layout(xaxis_title="Model", yaxis_title="Elo Rating", showlegend=False)
    fig.update_xaxes(tickangle=45)
    return fig.to_html()


def plot_battle_count_matrix(battles):
    ptbl = pd.pivot_table(
        battles, index="model_a", columns="model_b", aggfunc="size", fill_value=0
    )
    battle_counts = ptbl + ptbl.T
    ordering = battle_counts.sum().sort_values(ascending=False).index
    fig = px.imshow(
        battle_counts.loc[ordering, ordering],
        title="Battle Count Matrix",
        text_auto=True,
        height=800,
        width=800,
    )
    fig.update_layout(
        xaxis_title="Model B",
        yaxis_title="Model A",
        xaxis_side="top",
        title_y=0.07,
        title_x=0.5,
    )
    fig.update_traces(
        hovertemplate="Model A: %{y}<br>Model B: %{x}<br>Count: %{z}<extra></extra>"
    )
    return fig.to_html()


def main():
    mercor_samples = get_samples(MERCOR_TENANT_NAME)
    turing_samples = get_samples(TURING_TENANT_NAME)
    samples = mercor_samples + turing_samples

    print(f"Loaded {len(samples)} samples")

    # Create battles dataframe
    battles = pd.DataFrame(
        [
            {
                "overallRating": sample["raw_json"]["scores"]["overallRating"],
                "formattingRating": sample["raw_json"]["scores"]["formattingRating"],
                "instructionFollowingRating": sample["raw_json"]["scores"][
                    "instructionFollowingRating"
                ],
                "isHighQuality": sample["raw_json"]["scores"]["isHighQuality"],
                "user_id": sample["raw_json"]["request_ids"][0],
                "model_a": sample["raw_json"]["feedback"]
                .split("MODEL_IDS_START_LABEL")[1]
                .split("MODEL_IDS_END_LABEL")[0]
                .strip()
                .split("\n")[0]
                .strip(),
                "model_b": sample["raw_json"]["feedback"]
                .split("MODEL_IDS_START_LABEL")[1]
                .split("MODEL_IDS_END_LABEL")[0]
                .strip()
                .split("\n")[1]
                .strip(),
                "winner": "model_a"
                if sample["raw_json"]["scores"]["overallRating"] < 0
                else "model_b"
                if sample["raw_json"]["scores"]["overallRating"] > 0
                else "Tie",
                "datetime": sample["time"],
            }
            for sample in samples
            if "MODEL_IDS_START_LABEL" in sample["raw_json"]["feedback"]
        ]
    )

    print(f"Total battles: {len(battles)}")
    print("\nBattle outcomes:")
    print(battles["winner"].value_counts())

    # Compute Elo ratings
    elo_ratings = compute_mle_elo(battles)
    print("\nElo ratings:")
    print(elo_ratings)

    # Create plots
    battle_outcomes_plot = plot_battle_outcomes(battles)
    elo_ratings_plot = plot_elo_ratings(elo_ratings)
    battle_count_matrix = plot_battle_count_matrix(battles)

    # Create HTML report
    test_html = f"""
    <h1>Model Elo Ratings</h1>
    <p>Found {len(samples)} samples, {len(battles)} battles</p>

    <h2>Battle Outcomes</h2>
    <pre>{battles["winner"].value_counts().to_string()}</pre>
    {battle_outcomes_plot}

    <h2>Elo Ratings</h2>
    <pre>{elo_ratings.to_string()}</pre>
    {elo_ratings_plot}

    <h2>Battle Count Matrix</h2>
    {battle_count_matrix}
    """
    test_path = (
        Path(MODEL_ELO_DIR) / f"test_{datetime.datetime.now().strftime('%m-%d')}.html"
    )
    test_path.write_text(test_html)
    print(f"\nTest HTML report generated at {test_path}")


if __name__ == "__main__":
    main()

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base64\n", "import json\n", "from typing import Any\n", "\n", "from base.prompt_format.common import (\n", "    ChatRequestImage,\n", "    ChatRequestNode,\n", "    ChatRequestNodeType,\n", "    ChatRequestText,\n", "    ChatRequestToolResult,\n", "    ChatResultNode,\n", "    ChatResultNodeType,\n", "    ChatResultToolUse,\n", "    Exchange,\n", "    ImageFormatType,\n", "    ToolDefinition,\n", ")\n", "\n", "IMAGE_PATH = \"/mnt/efs/augment/user/zhuoran/images/ucb_1k.jpg\"\n", "MODEL_CALLER = \"client-trial-notebook\"\n", "\n", "\n", "def load_image_as_base64(image_path: str) -> str:\n", "    \"\"\"Load an image file and convert it to base64 encoding.\"\"\"\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "\n", "\n", "def create_function_result(\n", "    tool_use_id: str, content: Any, is_error: bool = False\n", ") -> ChatRequestNode:\n", "    \"\"\"Create a tool result node for a function call.\"\"\"\n", "    # Convert content to JSON string if it's not already a string\n", "    if not isinstance(content, str):\n", "        content = json.dumps(content)\n", "\n", "    return ChatRequestNode(\n", "        id=1,\n", "        type=ChatRequestNodeType.TOOL_RESULT,\n", "        text_node=None,\n", "        tool_result_node=ChatRequestToolResult(\n", "            tool_use_id=tool_use_id,\n", "            content=content,\n", "            is_error=is_error,\n", "        ),\n", "        image_node=None,\n", "    )\n", "\n", "\n", "def create_image_node(\n", "    image_path: str, format_type: ImageFormatType = ImageFormatType.PNG\n", ") -> ChatRequestNode:\n", "    \"\"\"Create an image node from an image file.\"\"\"\n", "    base64_image = load_image_as_base64(image_path)\n", "    return ChatRequestNode(\n", "        id=2,\n", "        type=ChatRequestNodeType.IMAGE,\n", "        text_node=None,\n", "        tool_result_node=None,\n", "        image_node=ChatRequestImage(\n", "            image_data=base64_image,\n", "            format=format_type,\n", "        ),\n", "    )\n", "\n", "\n", "def create_text_node(content: str) -> ChatRequestNode:\n", "    \"\"\"Create a text node with the given content.\"\"\"\n", "    return ChatRequestNode(\n", "        id=0,\n", "        type=ChatRequestNodeType.TEXT,\n", "        text_node=ChatRequestText(content=content),\n", "        tool_result_node=None,\n", "        image_node=None,\n", "    )\n", "\n", "\n", "TOOL_DEFINITIONS = [\n", "    ToolDefinition(\n", "        name=\"get_weather\",\n", "        description=\"Get the current weather for a location\",\n", "        input_schema_json=json.dumps(\n", "            {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"location\": {\"type\": \"string\", \"description\": \"The city name\"},\n", "                    \"units\": {\n", "                        \"type\": \"string\",\n", "                        \"enum\": [\"celsius\", \"fahrenheit\"],\n", "                        \"description\": \"Temperature units\",\n", "                    },\n", "                },\n", "                \"required\": [\"location\"],\n", "            }\n", "        ),\n", "    ),\n", "]\n", "\n", "\n", "def get_chat_history(\n", "    image_support: bool = True, tool_support: bool = True\n", ") -> list[Exchange]:\n", "    chat_history = []\n", "    if tool_support:\n", "        chat_history += [\n", "            Exchange(\n", "                request_message=\"What's the weather like in current location today?\",\n", "                response_text=[\n", "                    ChatResultNode(\n", "                        id=0,\n", "                        type=ChatResultNodeType.RAW_RESPONSE,\n", "                        content=\"I'll check the current weather in current location for you.\",\n", "                    ),\n", "                    ChatResultNode(\n", "                        id=1,\n", "                        type=ChatResultNodeType.TOOL_USE,\n", "                        content=\"\",\n", "                        tool_use=ChatResultToolUse(\n", "                            name=\"get_weather\",\n", "                            input={\"location\": \"current location\", \"units\": \"celsius\"},\n", "                            tool_use_id=\"weather_tool_1\",\n", "                        ),\n", "                    ),\n", "                ],\n", "            ),\n", "            Exchange(\n", "                request_message=[\n", "                    create_function_result(\n", "                        tool_use_id=\"weather_tool_1\",\n", "                        content=\"{'temperature': -20,}\",\n", "                    ),\n", "                ],\n", "                response_text=\"The weather in current location is -20°C.\",\n", "            ),\n", "        ]\n", "    else:\n", "        chat_history += [\n", "            Exchange(\n", "                request_message=\"What's the weather like in current location today?\",\n", "                response_text=\"The weather in current location is -20°C.\",\n", "            ),\n", "        ]\n", "    if image_support:\n", "        chat_history.append(\n", "            Exchange(\n", "                request_message=[\n", "                    create_text_node(\"What's the environment in this image?\"),\n", "                    create_image_node(IMAGE_PATH, ImageFormatType.JPEG),\n", "                ],\n", "                response_text=\"It is a conference room.\",\n", "            )\n", "        )\n", "    else:\n", "        chat_history.append(\n", "            Exchange(\n", "                request_message=\"What's in the image?\",\n", "                response_text=\"The alt-text shows 4 people in a conference room, dressed in puffer jackets.\",\n", "            )\n", "        )\n", "    return chat_history\n", "\n", "\n", "def get_current_message() -> list[ChatRequestNode]:\n", "    return [\n", "        create_text_node(\n", "            \"How many people are in the previous image? Are their clothes appropriate for the weather if outside?\"\n", "        ),\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient\n", "from research.environments import get_eng_secret\n", "\n", "\n", "client = AnthropicDirectClient(\n", "    api_key=get_eng_secret(\"seal-research-anthropic-key\"),\n", "    model_name=\"claude-3-7-sonnet-20250219\",\n", "    temperature=0,\n", "    max_output_tokens=1024,\n", ")\n", "image_support = True\n", "tool_support = True\n", "\n", "stream = client.generate_response_stream(\n", "    model_caller=MODEL_CALLER,\n", "    system_prompt=\"You are a helpful assistant that answers extremely concisely, and nevers says 1 more word not directly asked.\",\n", "    cur_message=get_current_message(),\n", "    chat_history=get_chat_history(image_support, tool_support),\n", "    tool_definitions=TOOL_DEFINITIONS,\n", "    yield_final_parameters=True,\n", ")\n", "\n", "text = \"\"\n", "tool_uses = []\n", "final_parameters = None\n", "try:\n", "    for response in stream:\n", "        if response.final_parameters:\n", "            final_parameters = response.final_parameters\n", "        elif response.tool_use:\n", "            tool_uses.append(response.tool_use)\n", "        else:\n", "            text += response.text\n", "except Exception as e:\n", "    print(e)\n", "\n", "print(final_parameters.keys())\n", "print(text)\n", "print(tool_uses)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.google_genai_client import GoogleGenaiClient\n", "\n", "client = GoogleGenaiClient(\n", "    project_id=\"augment-research-gsc\",\n", "    region=\"us-central1\",\n", "    model_name=\"gemini-2.5-pro-exp-03-25\",\n", "    temperature=0.7,\n", "    max_output_tokens=1024,\n", ")\n", "image_support = False\n", "tool_support = True\n", "\n", "stream = client.generate_response_stream(\n", "    model_caller=MODEL_CALLER,\n", "    system_prompt=\"You are a helpful assistant that answers extremely concisely, and nevers says 1 more word not directly asked.\",\n", "    cur_message=get_current_message(),\n", "    chat_history=get_chat_history(image_support, tool_support),\n", "    tool_definitions=TOOL_DEFINITIONS,\n", "    yield_final_parameters=True,\n", ")\n", "\n", "text = \"\"\n", "tool_uses = []\n", "final_parameters = None\n", "try:\n", "    for response in stream:\n", "        if response.final_parameters:\n", "            final_parameters = response.final_parameters\n", "        elif response.tool_use:\n", "            tool_uses.append(response.tool_use)\n", "        else:\n", "            text += response.text\n", "except Exception as e:\n", "    print(e)\n", "\n", "print(final_parameters.keys())\n", "print(text)\n", "print(tool_uses)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.openai_client import OpenAIClient\n", "\n", "\n", "client = OpenAIClient(\n", "    openai_api_key=open(\"/mnt/efs/augment/user/zhuoran/.tokens/openai\").read().strip(),\n", "    xai_api_key=\"\",\n", "    model_name=\"gpt-4.1-2025-04-14\",\n", "    temperature=0,\n", "    max_output_tokens=1024,\n", ")\n", "image_support = True\n", "tool_support = True\n", "\n", "stream = client.generate_response_stream(\n", "    model_caller=MODEL_CALLER,\n", "    system_prompt=\"You are a helpful assistant that answers extremely concisely, and nevers says 1 more word not directly asked.\",\n", "    cur_message=get_current_message(),\n", "    chat_history=get_chat_history(image_support, tool_support),\n", "    tool_definitions=TOOL_DEFINITIONS,\n", "    yield_final_parameters=True,\n", ")\n", "\n", "text = \"\"\n", "tool_uses = []\n", "final_parameters = None\n", "try:\n", "    for response in stream:\n", "        if response.final_parameters:\n", "            final_parameters = response.final_parameters\n", "        elif response.tool_use:\n", "            tool_uses.append(response.tool_use)\n", "        else:\n", "            text += response.text\n", "except Exception as e:\n", "    print(e)\n", "\n", "print(final_parameters.keys())\n", "print(text)\n", "print(tool_uses)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
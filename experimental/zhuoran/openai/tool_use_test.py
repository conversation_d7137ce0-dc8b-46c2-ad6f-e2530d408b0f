import json

from base.prompt_format.common import (
    ChatRequestN<PERSON>,
    ChatRequestNodeType,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
)
from base.third_party_clients.fireworks_client import FireworksClient
from base.third_party_clients.third_party_model_client import (
    ToolDefinition,
)


def test_streaming_with_tools():
    input_schema = {
        "type": "object",
        "properties": {"debug": {"type": "boolean"}},
        "required": ["debug"],
    }
    tool_definitions = [
        ToolDefinition(
            name="get_date",
            description="Get the current date in YYYY-MM-DD format",
            input_schema_json=json.dumps(input_schema),
        )
    ]

    # client = AnthropicVertexAiClient(
    #     project_id="augment-387916",
    #     region="us-east5",
    #     model_name="claude-3-5-sonnet-v2@********",
    #     temperature=0.7,
    #     max_output_tokens=1024,
    # )
    fireworks_api_key = (
        open("/mnt/efs/augment/user/zhuoran/.tokens/fireworks").read().strip()
    )
    client = FireworksClient(
        api_key=fireworks_api_key,
        model_name="accounts/fireworks/models/claude-v2",
        temperature=0,
        max_output_tokens=8192,
    )

    system_prompt = "You are a helpful assistant that MUST use tools when available. You MUST use the get_date tool when asked about dates. Always format dates as 'The date is YYYY-MM-DD'."
    cur_message = "What's today's date?"
    chat_history = [
        Exchange(
            request_message="What's your purpose?",
            response_text="I'm here to help!",
        ),
    ]

    response_stream = client.generate_response_stream(
        cur_message=cur_message,
        system_prompt=system_prompt,
        chat_history=chat_history,
        tool_definitions=tool_definitions,
        tool_choice={"type": "tool", "name": "get_date"},
    )
    chunks = [chunk for chunk in response_stream]

    assert len(chunks) == 1
    assert not chunks[0].text

    tool_use = chunks[0].tool_use
    assert tool_use
    print(type(tool_use))
    assert tool_use.tool_name == "get_date"

    chat_history.append(
        Exchange(
            request_message=cur_message,
            response_text=[
                ChatResultNode(
                    id=1,
                    type=ChatResultNodeType.TOOL_USE,
                    content="",
                    tool_use=ChatResultToolUse(
                        name=tool_use.tool_name,
                        input=tool_use.input,
                        tool_use_id=tool_use.tool_use_id,
                    ),
                )
            ],
        )
    )

    response_stream = client.generate_response_stream(
        cur_message=[
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id=tool_use.tool_use_id,
                    content="2011-12-16",
                    is_error=False,
                ),
            )
        ],
        system_prompt=system_prompt,
        chat_history=chat_history,
        tool_definitions=tool_definitions,
    )
    chunks = [chunk for chunk in response_stream]
    assert chunks

    text = "".join([chunk.text for chunk in chunks])
    assert "2011-12-16" in text

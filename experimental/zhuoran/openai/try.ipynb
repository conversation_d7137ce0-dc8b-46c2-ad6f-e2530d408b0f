{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import random\n", "import string\n", "\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "client = AnthropicVertexAiClient(\n", "    region=REGION,\n", "    project_id=PROJECT_ID,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "start_time = time.time()\n", "\n", "tools = [\n", "    {\n", "        \"name\": \"find_secret\",\n", "        \"description\": \"Find the secret given a secret key.\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"key\": {\"type\": \"string\"},\n", "            },\n", "            \"required\": [\"key\"],\n", "        },\n", "    }\n", "]\n", "\n", "# Random key of length 10000\n", "key = \"\".join(\n", "    random.choice(string.ascii_lowercase + string.digits) for _ in range(9999)\n", ")\n", "\n", "with client.client.messages.stream(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"What's the secret for {key}\",\n", "        },\n", "    ],\n", "    tools=tools,\n", "    system=\"\",\n", "    temperature=TEMPERAURE,\n", ") as stream:\n", "    for chunk in stream:\n", "        current_time = time.time()\n", "        delta = current_time - start_time\n", "        if delta > 0.01:\n", "            print(f\"{delta * 1000:.1f}ms:\", end=\" \")\n", "            start_time = current_time\n", "            print(chunk)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "\n", "from research.environments import get_eng_secret\n", "\n", "\n", "anthropic_api_key = get_eng_secret(\"seal-research-anthropic-key\")\n", "client = anthropic.Anthropic(api_key=anthropic_api_key, max_retries=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"Write a hello world program in Python.\",\n", "    },\n", "]\n", "response = client.messages.create(  # type: ignore\n", "    max_tokens=64_000,\n", "    messages=messages,\n", "    model=\"claude-linen-eap\",\n", "    temperature=1,\n", "    # extra_body={\n", "    #     \"thinking\": {\n", "    #         \"type\": \"enabled\",\n", "    #         \"budget_tokens\": 32_000,\n", "    #     },\n", "    # },\n", "    stream=True,\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunks = [chunk for chunk in response]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = -1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic import TextEvent\n", "\n", "index += 1\n", "chunk = chunks[index]\n", "print(chunk)\n", "print(isinstance(chunk, TextEvent))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response.content[0].text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Grok"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import openai\n", "\n", "grok_api_key = \"************************************************************************************\"\n", "\n", "client = openai.OpenAI(\n", "    base_url=\"https://api.x.ai/v1\",\n", "    api_key=grok_api_key,\n", ")\n", "response = client.chat.completions.create(  # type: ignore\n", "    model=\"grok-2\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"Write a hello world program in Python.\"},\n", "    ],\n", "    stream=True,\n", ")\n", "for chunk in response:\n", "    print(chunk.choices[0].delta.content, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai import BadRequestError, NotFoundError\n", "\n", "function_spec = {\n", "    \"name\": \"write_hello_world\",\n", "    \"description\": \"Writes a hello world program.\",\n", "}\n", "\n", "for model in [\n", "    \"grok-2-1212\",\n", "]:\n", "    print(f'\"{model}\": {{')\n", "    for feature, system_role, streaming, kwargs, additional_messages in [\n", "        (\"system_prompt\", \"system\", False, {}, []),\n", "        (\"streaming\", \"user\", True, {}, []),\n", "        (\"temperature\", \"user\", False, {\"temperature\": 0.5}, []),\n", "        (\n", "            \"function_calling\",\n", "            \"user\",\n", "            False,\n", "            {\"functions\": [function_spec]},\n", "            [\n", "                {\n", "                    \"role\": \"function\",\n", "                    \"name\": \"write_hello_world\",\n", "                    \"content\": \"print('Hello, world!')\",\n", "                }\n", "            ],\n", "        ),\n", "    ]:\n", "        try:\n", "            response = client.chat.completions.create(  # type: ignore\n", "                model=model,\n", "                messages=[\n", "                    {\"role\": system_role, \"content\": \"You are a helpful assistant.\"},\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": \"Write a hello world program in Python.\",\n", "                    },\n", "                    *additional_messages,\n", "                ],\n", "                stream=streaming,\n", "                **kwargs,\n", "            )\n", "            print(f'    \"{feature}\": True,')\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NotFoundError):\n", "            print(f'    \"{feature}\": False,')\n", "    print(\"},\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# OpenAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import openai\n", "\n", "openai_api_key = open(\"/mnt/efs/augment/user/zhuoran/.tokens/openai\").read().strip()\n", "client = openai.OpenAI(api_key=openai_api_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai import BadRequestError, NotFoundError\n", "\n", "function_spec = {\n", "    \"name\": \"write_hello_world\",\n", "    \"description\": \"Writes a hello world program.\",\n", "}\n", "\n", "for model in [\n", "    # \"gpt-4o-2024-08-06\",\n", "    # \"gpt-4o-2024-11-20\",\n", "    # \"o1-preview-2024-09-12\",\n", "    # \"o1-mini-2024-09-12\",\n", "    # \"o1-2024-12-17\",\n", "    # \"o3-mini-2025-01-31\",\n", "    \"o3-2025-04-16\",\n", "    \"o4-mini-2025-04-16\",\n", "    # \"chatgpt-4o-latest\",\n", "    # \"gpt-4.5-preview-2025-02-27\",\n", "]:\n", "    print(f'\"{model}\": {{')\n", "    for feature, system_role, streaming, kwargs, additional_messages in [\n", "        (\"system_prompt\", \"system\", False, {}, []),\n", "        (\"streaming\", \"user\", True, {}, []),\n", "        (\"temperature\", \"user\", False, {\"temperature\": 0.5}, []),\n", "        (\n", "            \"function_calling\",\n", "            \"user\",\n", "            False,\n", "            {\"functions\": [function_spec]},\n", "            [\n", "                {\n", "                    \"role\": \"function\",\n", "                    \"name\": \"write_hello_world\",\n", "                    \"content\": \"print('Hello, world!')\",\n", "                }\n", "            ],\n", "        ),\n", "    ]:\n", "        try:\n", "            response = client.chat.completions.create(  # type: ignore\n", "                model=model,\n", "                messages=[\n", "                    {\"role\": system_role, \"content\": \"You are a helpful assistant.\"},\n", "                    {\n", "                        \"role\": \"user\",\n", "                        \"content\": \"Write a hello world program in Python.\",\n", "                    },\n", "                    *additional_messages,\n", "                ],\n", "                stream=streaming,\n", "                **kwargs,\n", "            )\n", "            print(f'    \"{feature}\": True,')\n", "        except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NotFoundError):\n", "            print(f'    \"{feature}\": False,')\n", "    print(\"},\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "def get_time():\n", "    return \"12:16\"\n", "\n", "\n", "function_spec = {\n", "    \"name\": \"get_time\",\n", "    \"description\": \"Fetches the current time.\",\n", "}\n", "\n", "model_name = \"o1-preview\"\n", "response = client.chat.completions.create(\n", "    model=model_name,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"You are a helpful assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"What's the time now?\"},\n", "    ],\n", "    functions=[function_spec],  # type: ignore\n", ")\n", "\n", "# # Check if a function call is needed\n", "# if response.choices[0].finish_reason == \"function_call\":\n", "#     function_name = response.choices[0].message.function_call.name\n", "#     arguments = json.loads(response.choices[0].message.function_call.arguments)\n", "\n", "#     if function_name == \"get_time\":\n", "#         result = get_time()\n", "#         # Send the result back to the model\n", "#         follow_up_response = client.chat.completions.create(\n", "#             model=\"o1\",\n", "#             messages=[\n", "#                 {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "#                 {\n", "#                     \"role\": \"user\",\n", "#                     \"content\": \"What's the time now?\",\n", "#                 },\n", "#                 {\"role\": \"function\", \"name\": function_name, \"content\": result},\n", "#             ],\n", "#         )\n", "#         print(follow_up_response.choices[0].message.content)\n", "# else:\n", "#     print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Gemini"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import io\n", "\n", "from google.genai import Client\n", "from google.genai.types import (\n", "    Content,\n", "    FunctionCall,\n", "    GenerateContentConfig,\n", "    Part,\n", "    ThinkingConfig,\n", ")\n", "from PIL import Image\n", "\n", "\n", "def make_fake_image_bytes():\n", "    img = Image.new(\"RGB\", (60, 30), color=\"black\")\n", "    buffered = io.BytesIO()\n", "    img.save(buffered, format=\"JPEG\")\n", "    return buffered.getvalue()\n", "\n", "\n", "def format_preexisting_chat_history():\n", "    image_data = make_fake_image_bytes()\n", "\n", "    history = [\n", "        Content(\n", "            role=\"user\",\n", "            parts=[Part.from_text(text=\"What's the weather like in San Jose?\")],\n", "        ),\n", "        Content(\n", "            role=\"model\",\n", "            parts=[\n", "                Part(\n", "                    function_call=FunctionCall(\n", "                        name=\"get_weather\",\n", "                        args={\n", "                            \"location\": \"San Jose, CA\",\n", "                        },\n", "                    )\n", "                )\n", "            ],\n", "        ),\n", "        Content(\n", "            role=\"function\",\n", "            parts=[\n", "                Part.from_function_response(\n", "                    name=\"get_weather\",\n", "                    response={\n", "                        \"content\": {\n", "                            \"temperature\": \"70°F\",\n", "                            \"condition\": \"<PERSON>\",\n", "                        }\n", "                    },\n", "                )\n", "            ],\n", "        ),\n", "        Content(\n", "            role=\"model\",\n", "            parts=[\n", "                Part.from_text(\n", "                    text=\"The weather in San Jose is currently sunny with a temperature of 70°F.\"\n", "                )\n", "            ],\n", "        ),\n", "        Content(\n", "            role=\"user\",\n", "            parts=[\n", "                Part.from_text(text=\"What is in this picture?\"),\n", "                Part.from_bytes(data=image_data, mime_type=\"image/jpeg\"),\n", "            ],\n", "        ),\n", "        Content(\n", "            role=\"model\",\n", "            parts=[Part.from_text(text=\"The picture is black.\")],\n", "        ),\n", "    ]\n", "    return history\n", "\n", "\n", "PROJECT_ID = \"augment-research-gsc\"\n", "REGION = \"us-central1\"\n", "model_name = \"gemini-2.5-pro-preview-05-06\"\n", "\n", "client = Client(vertexai=True, project=PROJECT_ID, location=REGION)\n", "\n", "history_list = format_preexisting_chat_history()\n", "\n", "config = GenerateContentConfig(\n", "    max_output_tokens=2048,\n", "    temperature=0.2,\n", "    thinking_config=ThinkingConfig(include_thoughts=True),\n", ")\n", "\n", "iterator = client.models.generate_content_stream(\n", "    model=model_name,\n", "    contents=history_list  # type: ignore\n", "    + [\n", "        Content(\n", "            role=\"user\",\n", "            parts=[\n", "                Part.from_text(text=\"Write a long story of Dinosaurs.\"),\n", "                Part.from_text(\n", "                    text=\"Then, summarize the our conversation again, very concisely.\"\n", "                ),\n", "                Part.from_text(text=\"Think before you answer.\"),\n", "            ],\n", "        )\n", "    ],\n", "    # + [\n", "    #     Content(\n", "    #         role=\"model\",\n", "    #         parts=[\n", "    #             Part.from_text(text=\"In Jurassic World: Camp Cretaceous,\"),\n", "    #         ],\n", "    #     )\n", "    # ],\n", "    config=config,\n", ")\n", "chunks = []\n", "for chunk in iterator:\n", "    print(chunk)\n", "    chunks.append(chunk)\n", "    # print(chunk.candidates[0].content.parts[0].text, end=\"\")  # type: ignore"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import get_attrs\n", "\n", "# chunk = chunks[-1]\n", "# print(get_attrs(chunk))\n", "# chunk\n", "response_ids = set()\n", "for chunk in chunks:\n", "    assert \"response_id\" in get_attrs(chunk)\n", "    response_ids.add(chunk.response_id)\n", "response_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.genai import Client\n", "from google.genai.types import (\n", "    Content,\n", "    GenerateContentConfig,\n", "    Part,\n", "    ThinkingConfig,\n", ")\n", "\n", "\n", "PROJECT_ID = \"augment-research-gsc\"\n", "REGION = \"us-central1\"\n", "model_name = \"gemini-2.5-pro-preview-03-25\"\n", "\n", "client = Client(vertexai=True, project=PROJECT_ID, location=REGION)\n", "\n", "config = GenerateContentConfig(\n", "    max_output_tokens=2048,\n", "    temperature=0.2,\n", "    thinking_config=ThinkingConfig(include_thoughts=True, thinking_budget=1000),\n", ")\n", "\n", "iterator = client.models.generate_content_stream(\n", "    model=model_name,\n", "    contents=[\n", "        Content(\n", "            role=\"user\",\n", "            parts=[\n", "                Part.from_text(text=\"How many R's are there in strawberry?\"),\n", "            ],\n", "        )\n", "    ],\n", "    config=config,\n", ")\n", "for chunk in iterator:\n", "    print(chunk)\n", "    # print(chunk.candidates[0].content.parts[0].text, end=\"\")  # type: ignore"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.genai import Client\n", "from google.genai.types import (\n", "    Content,\n", "    GenerateContentConfig,\n", "    Part,\n", "    FunctionDeclaration,\n", "    <PERSON><PERSON><PERSON>,\n", ")\n", "\n", "\n", "PROJECT_ID = \"augment-research-gsc\"\n", "REGION = \"us-central1\"\n", "model_name = \"gemini-2.5-pro-exp-03-25\"\n", "\n", "client = Client(vertexai=True, project=PROJECT_ID, location=REGION)\n", "\n", "history_list = format_preexisting_chat_history()\n", "\n", "config = GenerateContentConfig(\n", "    max_output_tokens=2048,\n", "    temperature=0.2,\n", "    tools=[\n", "        FunctionDeclaration(\n", "            name=\"get_weather\",\n", "            description=\"Get current temperature for a given location.\",\n", "            parameters=Schema(\n", "                type=\"OBJECT\",\n", "                properties={\n", "                    \"location\": {\n", "                        \"type\": \"STRING\",\n", "                        \"description\": \"City and country e.g. Bogotá, Colombia\",\n", "                    }\n", "                },\n", "                required=[\"location\"],\n", "            ),\n", "        )\n", "    ],\n", ")\n", "\n", "iterator = client.models.generate_content_stream(\n", "    model=model_name,\n", "    contents=[\n", "        Content(\n", "            role=\"user\",\n", "            parts=[\n", "                Part.from_text(\"How's the weather in San Jose?\"),\n", "            ],\n", "        )\n", "    ],\n", "    config=config,\n", ")\n", "for chunk in iterator:\n", "    # print(chunk)\n", "    print(chunk.candidates[0].content.parts[0].text, end=\"\")  # type: ignore"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Augment clients"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient\n", "from base.augment_client.client import AugmentModelClient\n", "\n", "client = AugmentClient(\n", "    url=\"https://staging-shard-0.api.augmentcode.com\",\n", "    token=os.environ[\"AUGMENT_TOKEN\"],\n", ")\n", "model_client = AugmentModelClient(client, \"\")\n", "stream = model_client.chat_stream(\n", "    selected_code=\"\",\n", "    message=\"Write a quick sort, be brief\",\n", "    prefix=\"\",\n", "    suffix=\"\",\n", "    path=\"\",\n", "    user_guidelines=\"Answer in Chinese\",\n", ")\n", "for r in stream:\n", "    print(r.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "openai_api_key = open(\"/mnt/efs/augment/user/zhuoran/.tokens/openai\").read().strip()\n", "client = OpenAI(api_key=openai_api_key)\n", "\n", "tools = [\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_a\",\n", "            \"description\": \"Get the value of a.\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {},\n", "                \"required\": [],\n", "                \"additionalProperties\": <PERSON><PERSON><PERSON>,\n", "            },\n", "            \"strict\": True,\n", "        },\n", "    },\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_b\",\n", "            \"description\": \"Get the value of b.\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {},\n", "                \"required\": [],\n", "                \"additionalProperties\": <PERSON><PERSON><PERSON>,\n", "            },\n", "            \"strict\": True,\n", "        },\n", "    },\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"git\",\n", "            \"description\": \"Use git.\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"args\": {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"command\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"The git command to run.\",\n", "                            },\n", "                            \"args\": {\n", "                                \"type\": \"array\",\n", "                                \"items\": {\"type\": \"string\"},\n", "                                \"description\": \"The arguments to pass to the git command.\",\n", "                            },\n", "                        },\n", "                        \"description\": \"Arguments to pass to git.\",\n", "                    }\n", "                },\n", "                \"required\": [\"args\"],\n", "            },\n", "        },\n", "    },\n", "]\n", "\n", "response = client.chat.completions.create(  # type: ignore\n", "    model=\"o3-mini-2025-01-31\",\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"You are a helpful assistant.\",\n", "        },\n", "        {\"role\": \"user\", \"content\": \"What's the sum of a and b?\"},\n", "    ],\n", "    # stream=False,\n", "    stream=True,\n", "    temperature=1,\n", "    tools=tools,\n", "    # tool_choice={\"type\": \"function\", \"function\": {\"name\": \"get_a\"}},\n", "    # tools=None,\n", ")\n", "response_list = list(response)\n", "response_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for r in response_list:\n", "    print(r.choices[0].delta.tool_calls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "\n", "openai_api_key = open(\"/mnt/efs/augment/user/zhuoran/.tokens/openai\").read().strip()\n", "client = OpenAI(api_key=openai_api_key)\n", "\n", "tools = [\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_weather\",\n", "            \"description\": \"Get current temperature for a given location.\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"location\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"City and country e.g. Bogotá, Colombia\",\n", "                    }\n", "                },\n", "                \"required\": [\"location\"],\n", "                \"additionalProperties\": <PERSON><PERSON><PERSON>,\n", "            },\n", "            \"strict\": True,\n", "        },\n", "    },\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"get_location\",\n", "            \"description\": \"Get current location.\",\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": {},\n", "                \"required\": [],\n", "                \"additionalProperties\": <PERSON><PERSON><PERSON>,\n", "            },\n", "            \"strict\": True,\n", "        },\n", "    },\n", "]\n", "\n", "response = client.chat.completions.create(  # type: ignore\n", "    model=\"o3-mini-2025-01-31\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"How is the weather here?\"},\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": \"\",\n", "            \"tool_calls\": [\n", "                {\n", "                    \"type\": \"function\",\n", "                    \"id\": \"call_FMCknos0j6MWKIcSkpQ2Y5uy\",\n", "                    \"function\": {\n", "                        \"name\": \"get_weather\",\n", "                        \"arguments\": '{\"location\": \"Bogotá, Colombia\"}',\n", "                    },\n", "                }\n", "            ],\n", "        },\n", "        {\n", "            \"role\": \"tool\",\n", "            \"tool_call_id\": \"call_FMCknos0j6MWKIcSkpQ2Y5uy\",\n", "            # \"name\": \"get_weather\",\n", "            \"content\": \"It's 32 degrees Celsius.\",\n", "        },\n", "        # {\"role\": \"assistant\", \"content\": \"The current weather in Bogotá, Colombia is\"},\n", "    ],\n", "    # stream=False,\n", "    stream=True,\n", "    temperature=1,\n", "    tools=tools,\n", "    # tools=None,\n", ")\n", "response_list = list(response)\n", "response_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for r in response_list:\n", "    print(r.choices[0].delta.tool_calls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Augment clients"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.vertexai_client import VertexAiClient\n", "\n", "client = VertexAiClient(\n", "    project_id=\"augment-research-gsc\",\n", "    region=\"us-central1\",\n", "    model_name=\"gemini-2.5-pro-exp-03-25\",\n", "    temperature=0.7,\n", "    max_output_tokens=1024,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stream = client.generate_response_stream(\n", "    model_caller=\"trial-notebook\",\n", "    messages=[],\n", "    system_prompt=\"You are a helpful assistant.\",\n", "    cur_message=\"Write a hello world program in Python.\",\n", ")\n", "for r in stream:\n", "    print(r.text, r.replace_text_response, r.tool_use)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# DeepSeek"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "fireworks_api_key = (\n", "    open(\"/mnt/efs/augment/user/zhuoran/.tokens/fireworks\").read().strip()\n", ")\n", "\n", "url = \"https://api.fireworks.ai/inference/v1/chat/completions\"\n", "payload = {\n", "    # \"model\": \"accounts/fireworks/models/deepseek-v3\",\n", "    # \"model\": \"accounts/fireworks/models/llama-v3p3-70b-instruct\",\n", "    \"model\": \"accounts/fireworks/models/deepseek-r1\",\n", "    \"max_tokens\": 16384,\n", "    \"top_p\": 1,\n", "    \"top_k\": 40,\n", "    \"presence_penalty\": 0,\n", "    \"frequency_penalty\": 0,\n", "    \"temperature\": 0.6,\n", "    \"messages\": [\n", "        {\"role\": \"user\", \"content\": \"What's the date today?\"},\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": \"The date is 2023-10-10.\",\n", "        },\n", "    ],\n", "    \"tools\": [\n", "        {\n", "            \"type\": \"function\",\n", "            \"function\": {\n", "                \"name\": \"get_current_date\",\n", "                \"description\": \"Get the current date\",\n", "                \"parameters\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {},\n", "                },\n", "            },\n", "        }\n", "    ],\n", "    # \"stream\": True,\n", "}\n", "headers = {\n", "    \"Accept\": \"application/json\",\n", "    \"Content-Type\": \"application/json\",\n", "    \"Authorization\": f\"Bearer {fireworks_api_key}\",\n", "}\n", "response = requests.request(\"POST\", url, headers=headers, data=json.dumps(payload))\n", "# print(response.json()[\"choices\"][0][\"message\"][\"content\"])\n", "response.json()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response.request.__dir__()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from fireworks.client import Fireworks\n", "\n", "client = Fireworks(api_key=fireworks_api_key)\n", "response = client.chat.completions.create(\n", "    model=\"accounts/fireworks/models/deepseek-v3\",\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What functions are available for you to call?\",\n", "        }\n", "    ],\n", "    tools=[\n", "        {\n", "            \"type\": \"function\",\n", "            \"function\": {\n", "                \"name\": \"get_current_date\",\n", "                \"description\": \"Get the current date\",\n", "                \"parameters\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {},\n", "                },\n", "            },\n", "        }\n", "    ],\n", ")\n", "# print(response.choices[0].message.content)\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response.json()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "openai_api_key = open(\"/mnt/efs/augment/user/zhuoran/.tokens/openai\").read().strip()\n", "url = \"https://api.openai.com/v1/chat/completions\"\n", "payload = {\n", "    \"model\": \"gpt-4o\",\n", "    \"max_tokens\": 16384,\n", "    \"top_p\": 1,\n", "    \"presence_penalty\": 0,\n", "    \"frequency_penalty\": 0,\n", "    \"temperature\": 0.6,\n", "    \"messages\": [{\"role\": \"user\", \"content\": \"Write a hello world in Python\"}],\n", "}\n", "headers = {\n", "    \"Accept\": \"application/json\",\n", "    \"Content-Type\": \"application/json\",\n", "    \"Authorization\": f\"Bearer {openai_api_key}\",\n", "}\n", "response = requests.request(\"POST\", url, headers=headers, data=json.dumps(payload))\n", "print(response.json()[\"choices\"][0][\"message\"][\"content\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "url = \"https://api.openai.com/v1/chat/completions\"\n", "payload = {\n", "    \"model\": \"gpt-4o\",\n", "    \"max_tokens\": 16384,\n", "    \"top_p\": 1,\n", "    \"presence_penalty\": 0,\n", "    \"frequency_penalty\": 0,\n", "    \"temperature\": 0.6,\n", "    \"messages\": [{\"role\": \"user\", \"content\": \"Write a hello world in Python\"}],\n", "    \"stream\": True,  # Enable streaming\n", "}\n", "headers = {\n", "    \"Accept\": \"application/json\",\n", "    \"Content-Type\": \"application/json\",\n", "    \"Authorization\": f\"Bearer {openai_api_key}\",\n", "}\n", "\n", "# Make streaming request\n", "response = requests.request(\n", "    \"POST\", url, headers=headers, data=json.dumps(payload), stream=True\n", ")\n", "\n", "buffer = \"\"\n", "# Process the stream chunk by chunk\n", "for chunk in response.iter_content(chunk_size=1):\n", "    if chunk:\n", "        buffer += chunk.decode(\"utf-8\")\n", "        if buffer.endswith(\"\\n\"):\n", "            lines = buffer.split(\"\\n\")\n", "            for line in lines[:-1]:  # Process all complete lines\n", "                if line.startswith(\"data: \"):\n", "                    if line == \"data: [DONE]\":\n", "                        break\n", "                    try:\n", "                        json_data = json.loads(line[6:])  # Skip \"data: \" prefix\n", "                        content = json_data[\"choices\"][0][\"delta\"].get(\"content\", \"\")\n", "                        if content:\n", "                            print(content, end=\"\\n\", flush=True)\n", "                    except json.JSONDecodeError:\n", "                        continue\n", "            buffer = lines[-1]  # Keep the incomplete line\n", "\n", "# Process any remaining buffer content\n", "if buffer:\n", "    if buffer.startswith(\"data: \") and buffer != \"data: [DONE]\":\n", "        try:\n", "            json_data = json.loads(buffer[6:])\n", "            content = json_data[\"choices\"][0][\"delta\"].get(\"content\", \"\")\n", "            if content:\n", "                print(content, end=\"\\n\", flush=True)\n", "        except json.JSONDecodeError:\n", "            pass\n", "\n", "print()  # Final newline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "url = \"https://api.openai.com/v1/chat/completions\"\n", "payload = {\n", "    \"model\": \"gpt-4o\",\n", "    \"max_tokens\": 16384,\n", "    \"top_p\": 1,\n", "    \"presence_penalty\": 0,\n", "    \"frequency_penalty\": 0,\n", "    \"temperature\": 0.6,\n", "    \"messages\": [{\"role\": \"user\", \"content\": \"What's today's date?\"}],\n", "    \"tools\": [\n", "        {\n", "            \"type\": \"function\",\n", "            \"function\": {\n", "                \"name\": \"get_current_date\",\n", "                \"description\": \"Get the current date\",\n", "                \"parameters\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {},\n", "                },\n", "            },\n", "        }\n", "    ],\n", "}\n", "headers = {\n", "    \"Accept\": \"application/json\",\n", "    \"Content-Type\": \"application/json\",\n", "    \"Authorization\": f\"Bearer {openai_api_key}\",\n", "}\n", "response = requests.request(\"POST\", url, headers=headers, data=json.dumps(payload))\n", "# print(response.json()[\"choices\"][0][\"message\"][\"content\"])\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.fireworks_client import FireworksClient\n", "\n", "fireworks_client = FireworksClient(\n", "    api_key=fireworks_api_key,\n", "    model_name=\"accounts/fireworks/models/deepseek-v3\",\n", "    temperature=0,\n", "    max_output_tokens=8192,\n", ")\n", "response_iterator = fireworks_client.generate_response_stream(\n", "    cur_message=\"Write a hello world in Python.\",\n", ")\n", "text = \"\"\n", "for chunk in response_iterator:\n", "    print(chunk.text, end=\"\\n\", flush=True)\n", "    text += chunk.text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient\n", "\n", "anthropic_client = AnthropicDirectClient(\n", "    api_key=\"<API_KEY>\",\n", "    model_name=\"claude-3-5-sonnet-v2@********\",\n", "    temperature=0,\n", "    max_output_tokens=8192,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Any, Dict\n", "from unittest.mock import MagicMock\n", "\n", "import pytest\n", "import json\n", "\n", "from base.prompt_format.common import Exchange\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from base.third_party_clients.third_party_model_client import (\n", "    ToolDefinition,\n", "    ToolUseResponse,\n", ")\n", "\n", "\n", "def date_tool(args: Dict[str, Any]) -> str:\n", "    \"\"\"A simple tool that returns a fixed date.\"\"\"\n", "    return \"2011-12-16\"\n", "\n", "\n", "input_schema = {\n", "    \"type\": \"object\",\n", "    \"properties\": {\"debug\": {\"type\": \"boolean\"}},\n", "    \"required\": [\"debug\"],\n", "}\n", "tool_definitions = [\n", "    ToolDefinition(\n", "        name=\"get_date\",\n", "        description=\"Get the current date in YYYY-MM-DD format\",\n", "        input_schema_json=json.dumps(input_schema),\n", "    )\n", "]\n", "\n", "client = AnthropicVertexAiClient(\n", "    project_id=\"augment-387916\",\n", "    region=\"us-east5\",\n", "    model_name=\"claude-3-5-sonnet-v2@********\",\n", "    temperature=0.7,\n", "    max_output_tokens=1024,\n", ")\n", "\n", "system_prompt = \"You are a helpful assistant that can use tools. Always format dates as 'The date is MM-DD-YY'.\"\n", "cur_message = \"What's today's date?\"\n", "chat_history = [\n", "    Exchange(\n", "        request_message=\"What's your purpose?\",\n", "        response_text=\"I'm here to help!\",\n", "    ),\n", "]\n", "\n", "responses = []\n", "tool_response = None\n", "tool_used = False\n", "response_stream = client.generate_response_stream(\n", "    cur_message=cur_message,\n", "    system_prompt=system_prompt,\n", "    chat_history=chat_history,\n", "    tool_definitions=tool_definitions,\n", "    tool_choice={\"type\": \"tool\", \"name\": \"get_date\"},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunks = [chunk for chunk in response_stream]\n", "chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import (\n", "    ChatResultNode,\n", "    ChatResultNodeType,\n", "    ChatResultToolUse,\n", "    ChatRequestNode,\n", "    ChatRequestNodeType,\n", "    ChatRequestToolResult,\n", "    Exchange,\n", ")\n", "\n", "assert len(list(chunks)) == 1\n", "\n", "tool_use = chunks[0].tool_use\n", "\n", "assert tool_use\n", "assert tool_use.tool_name == \"get_date\"\n", "\n", "chat_history.append(\n", "    Exchange(\n", "        request_message=cur_message,\n", "        response_text=[\n", "            ChatResultNode(\n", "                id=1,\n", "                type=ChatResultNodeType.TOOL_USE,\n", "                content=\"\",\n", "                tool_use=ChatResultToolUse(\n", "                    name=tool_use.tool_name,\n", "                    input=tool_use.input,\n", "                    tool_use_id=tool_use.tool_use_id,\n", "                ),\n", "            )\n", "        ],\n", "    )\n", ")\n", "\n", "response_stream = client.generate_response_stream(\n", "    cur_message=[\n", "        ChatRequestNode(\n", "            id=1,\n", "            type=ChatRequestNodeType.TOOL_RESULT,\n", "            text_node=None,\n", "            tool_result_node=ChatRequestToolResult(\n", "                tool_use_id=tool_use.tool_use_id,\n", "                content=\"2011-12-16\",\n", "                is_error=False,\n", "            ),\n", "        )\n", "    ],\n", "    system_prompt=system_prompt,\n", "    chat_history=chat_history,\n", "    tool_definitions=tool_definitions,\n", ")\n", "\n", "chunks = [chunk for chunk in response_stream]\n", "chunks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Research LLMs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import OpenAIDirectClient\n", "\n", "client = OpenAIDirectClient(\"gpt-4o\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import TextPrompt\n", "\n", "client.generate(\n", "    messages=[[TextPrompt(\"What's today's date?\")]],\n", "    max_tokens=1024,\n", "    # system_prompt=\"You are a helpful assistant.\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
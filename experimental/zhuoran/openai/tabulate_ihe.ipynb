{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "\n", "def reset_display():\n", "    pd.reset_option(\"display.max_rows\")\n", "    pd.reset_option(\"display.max_columns\")\n", "    pd.reset_option(\"display.width\")\n", "    pd.reset_option(\"display.max_colwidth\")\n", "\n", "\n", "def set_unlimited_display():\n", "    pd.set_option(\"display.max_rows\", None)\n", "    pd.set_option(\"display.max_columns\", None)\n", "    pd.set_option(\"display.width\", None)\n", "    pd.set_option(\"display.max_colwidth\", None)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import os\n", "\n", "result_paths = glob.glob(\"/mnt/efs/augment/user/zhuoran/summaries/humaneval/*.json\")\n", "\n", "for path in result_paths:\n", "    print(os.path.basename(path))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "results_data = []\n", "for path in result_paths:\n", "    label = os.path.splitext(os.path.basename(path))[0]\n", "    with open(path) as file:\n", "        result = json.load(file)\n", "    run_result = list(result[\"runs\"].values())[0]\n", "    if \"metrics\" not in run_result[\"results\"]:\n", "        continue\n", "    metrics = run_result[\"results\"][\"metrics\"]\n", "    # Add label to metrics dictionary\n", "    metrics[\"label\"] = label\n", "    del metrics[\"samples\"]\n", "    # metrics[\"html_report_url\"] = run_result[\"results\"][\"html_report_url\"]\n", "    results_data.append(metrics)\n", "\n", "# Create DataFrame from the collected data\n", "df = pd.DataFrame(results_data)\n", "# Reorder columns to put label first\n", "cols = [\"label\"] + [col for col in df.columns if col != \"label\"]\n", "df = df[cols]\n", "reset_display()\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract base label by removing the _x suffix\n", "df[\"base_label\"] = df[\"label\"].str.replace(r\"_\\d+$\", \"\", regex=True)\n", "\n", "# Group by base_label and calculate mean, std, and count\n", "grouped_df = df.groupby(\"base_label\").agg(\n", "    {\n", "        \"label\": \"count\",\n", "        **{\n", "            col: [\"mean\", \"std\"]\n", "            for col in df.columns\n", "            if col not in [\"label\", \"base_label\"]\n", "        },\n", "    }\n", ")\n", "\n", "# Flatten column names\n", "grouped_df.columns = [\n", "    f\"{col[0]} {col[1]}\" if col[1] != \"count\" else \"count\" for col in grouped_df.columns\n", "]\n", "\n", "# Reset index to make base_label a regular column\n", "grouped_df = grouped_df.reset_index()\n", "\n", "# Filter out columns that are all zeros\n", "non_zero_cols = [\"base_label\", \"count\"] + [\n", "    col\n", "    for col in grouped_df.columns\n", "    if col not in [\"base_label\", \"count\"] and not (grouped_df[col] == 0).all()\n", "]\n", "grouped_df = grouped_df[non_zero_cols]\n", "\n", "reset_display()\n", "grouped_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def combine_means_and_stds(data_frame):\n", "    # Find pairs of \"xxx mean\" and \"xxx std\" columns\n", "    mean_std_pairs = {}\n", "    new_data_frame = data_frame.copy()\n", "    for col in new_data_frame.columns:\n", "        if col.endswith(\" mean\"):\n", "            base_name = col[:-5]  # Remove \" mean\" suffix\n", "            if f\"{base_name} std\" in new_data_frame.columns:\n", "                mean_std_pairs[base_name] = (col, f\"{base_name} std\")\n", "            else:\n", "                new_data_frame[f\"{base_name} std\"] = 0\n", "                mean_std_pairs[base_name] = (col, f\"{base_name} std\")\n", "\n", "    # Create new columns with combined mean±std format\n", "    for base_name, (mean_col, std_col) in mean_std_pairs.items():\n", "        if base_name == \"generation time\":\n", "            new_data_frame[base_name] = new_data_frame.apply(\n", "                lambda row: f\"{row[mean_col]:.01f}\"\n", "                if row[std_col] == 0\n", "                else f\"{row[mean_col]:.01f}±{row[std_col]:.01f}\",\n", "                axis=1,\n", "            )\n", "        else:\n", "            new_data_frame[base_name] = new_data_frame.apply(\n", "                lambda row: f\"{row[mean_col]:.03f}\"\n", "                if row[std_col] == 0\n", "                else f\"{row[mean_col]:.03f}±{row[std_col]:.03f}\",\n", "                axis=1,\n", "            )\n", "    columns_to_drop = [col for pair in mean_std_pairs.values() for col in pair]\n", "    new_data_frame = new_data_frame.drop(columns=columns_to_drop)\n", "\n", "    # Extract Base model label (everything up to the last underscore before 'v' or 'r')\n", "    new_data_frame[\"Base model label\"] = new_data_frame[\"base_label\"].str.extract(\n", "        r\"^(.*?)(?:_(?=\\d+k|v\\d+|r\\d+))\"\n", "    )\n", "\n", "    # Extract Context length\n", "    new_data_frame[\"Context length\"] = (\n", "        new_data_frame[\"base_label\"].str.extract(r\"_(\\d+)k\").fillna(\"16\")\n", "    )\n", "\n", "    # Extract Version\n", "    new_data_frame[\"Version\"] = new_data_frame[\"base_label\"].str.extract(r\"v([\\d.-]+)\")\n", "    # Replace hyphens with dots\n", "    new_data_frame[\"Version\"] = new_data_frame[\"Version\"].str.replace(\"-\", \".\")\n", "    new_data_frame[\"Version\"] = pd.to_numeric(\n", "        new_data_frame[\"Version\"], errors=\"coerce\"\n", "    )\n", "\n", "    # Extract Round\n", "    new_data_frame[\"Round\"] = (\n", "        new_data_frame[\"base_label\"].str.extract(r\"r(\\d+)\").astype(int)\n", "    )\n", "    return new_data_frame\n", "\n", "\n", "def rename_columns(data_frame):\n", "    name_mapping = {\n", "        \"base_label\": \"Label\",\n", "        \"count\": \"Trials\",\n", "        \"keywords recall in answer\": \"Answer score\",\n", "        \"keywords recall in retrievals\": \"Retrieval score\",\n", "        \"gold paths recall\": \"File coverage\",\n", "        \"generation time\": \"Generation time\",\n", "        \"pass_at_1\": \"Pass@1\",\n", "    }\n", "\n", "    new_data_frame = data_frame.copy()\n", "    new_data_frame = new_data_frame.rename(columns=name_mapping)\n", "\n", "    new_column_order = [\n", "        \"Label\",\n", "        \"Base model label\",\n", "        \"Context length\",\n", "        \"Version\",\n", "        \"Round\",\n", "        \"Trials\",\n", "    ] + [\n", "        col\n", "        for col in new_data_frame.columns\n", "        if col\n", "        not in [\n", "            \"Label\",\n", "            \"Base model label\",\n", "            \"Context length\",\n", "            \"Version\",\n", "            \"Round\",\n", "            \"Trials\",\n", "        ]\n", "    ]\n", "    new_data_frame = new_data_frame[new_column_order]\n", "    return new_data_frame\n", "\n", "\n", "combined_df = combine_means_and_stds(grouped_df)\n", "\n", "\n", "renamed_df = rename_columns(combined_df)\n", "\n", "reset_display()\n", "# set_unlimited_display()\n", "renamed_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def add_attributes(data_frame):\n", "    attribute_mapping = {\n", "        \"claude\": {\n", "            1: (1, \"<PERSON> 3.5 Sonnet\", \"1.18.2\"),\n", "            1.1: (1, \"<PERSON> 3.5 Sonnet\", \"1.18.3\"),\n", "            2: (2, \"<PERSON> 3.5 Sonnet\", \"1.18.2\"),\n", "            2.1: (2, \"<PERSON> 3.5 Sonnet\", \"1.18.3\"),\n", "            3: (3, \"<PERSON> 3.5 Sonnet\", \"1.18.2\"),\n", "            3.1: (3, \"<PERSON> 3.5 Sonnet\", \"1.18.3\"),\n", "            4: (4, \"<PERSON> 3.5 Sonnet\", \"1.18.2\"),\n", "            4.1: (4, \"<PERSON> 3.5 Sonnet\", \"1.18.2\"),\n", "            4.2: (4, \"<PERSON> 3.5 Sonnet\", \"1.18.3\"),\n", "            5: (3, \"Claude 3.5 Sonnet V2\", \"1.18.2\"),\n", "            5.1: (3, \"Claude 3.5 Sonnet V2\", \"1.18.2\"),\n", "            5.2: (3, \"Claude 3.5 Sonnet V2\", \"1.18.3\"),\n", "            6: (4, \"Claude 3.5 Sonnet V2\", \"1.18.2\"),\n", "            6.1: (4, \"<PERSON> 3.5 Sonnet V2\", \"1.18.2\"),\n", "            6.2: (4, \"<PERSON> 3.5 Sonnet V2\", \"1.18.3\"),\n", "            7: (7, \"Claude 3.5 Sonnet V2\", \"1.18.2\"),\n", "            7.1: (7, \"Claude 3.5 Sonnet V2\", \"1.18.3\"),\n", "            8: (8, \"Claude 3.5 Sonnet V2\", \"1.18.2\"),\n", "            8.1: (8, \"Claude 3.5 Sonnet V2\", \"1.18.3\"),\n", "            9: (8, \"Claude 3.5 Sonnet V2\", \"1.18.2\"),\n", "            9.1: (8, \"Claude 3.5 Sonnet V2\", \"1.18.3\"),\n", "            10: (4, \"<PERSON> 3.5 Sonnet\", \"1.18.3\"),\n", "            11: (11, \"Claude 3.5 Sonnet V2\", \"1.18.3\"),\n", "            11.1: (11.1, \"Claude 3.5 Sonnet V2\", \"1.18.3\"),\n", "        },\n", "        \"o1\": {\n", "            1: (1, \"o1 preview\", \"1.18.2\"),\n", "            1.1: (1, \"o1 preview\", \"1.18.3\"),\n", "            2: (3, \"o1 preview\", \"1.18.2\"),\n", "            2.1: (3, \"o1 preview\", \"1.18.3\"),\n", "            3: (4, \"o1 preview\", \"1.18.2\"),\n", "            3.1: (4, \"o1 preview\", \"1.18.3\"),\n", "            4: (7, \"o1 preview\", \"1.18.2\"),\n", "            4.1: (7, \"o1 preview\", \"1.18.3\"),\n", "            5: (8, \"o1 preview\", \"1.18.2\"),\n", "            5.1: (8, \"o1 preview\", \"1.18.3\"),\n", "            6: (4, \"o1\", \"1.18.3\"),\n", "        },\n", "        \"o1_mini\": {\n", "            1: (1, \"o1 mini\", \"1.18.2\"),\n", "            1.1: (1, \"o1 mini\", \"1.18.3\"),\n", "            2: (3, \"o1 mini\", \"1.18.2\"),\n", "            2.1: (3, \"o1 mini\", \"1.18.3\"),\n", "            3: (4, \"o1 mini\", \"1.18.2\"),\n", "            3.1: (4, \"o1 mini\", \"1.18.3\"),\n", "            4: (7, \"o1 mini\", \"1.18.2\"),\n", "            4.1: (7, \"o1 mini\", \"1.18.3\"),\n", "            5: (8, \"o1 mini\", \"1.18.2\"),\n", "            5.1: (8, \"o1 mini\", \"1.18.3\"),\n", "        },\n", "        \"gpt4o\": {\n", "            1: (1, \"GPT-4o (2024-08-06)\", \"1.18.2\"),\n", "            1.1: (1, \"GPT-4o (2024-08-06)\", \"1.18.3\"),\n", "            2: (1, \"GPT-4o (2024-11-20)\", \"1.18.2\"),\n", "            2.1: (1, \"GPT-4o (2024-11-20)\", \"1.18.3\"),\n", "            3: (3, \"GPT-4o (2024-11-20)\", \"1.18.2\"),\n", "            3.1: (3, \"GPT-4o (2024-11-20)\", \"1.18.3\"),\n", "        },\n", "        \"gemini\": {\n", "            1: (1, \"Gemini 2.0 Flash\", \"1.18.2\"),\n", "            1.1: (1, \"Gemini 2.0 Flash\", \"1.18.3\"),\n", "            2: (3, \"Gemini 2.0 Flash\", \"1.18.2\"),\n", "            2.1: (3, \"Gemini 2.0 Flash\", \"1.18.3\"),\n", "        },\n", "    }\n", "\n", "    def get_attributes(row):\n", "        base_model = row[\"Base model label\"]\n", "        version = row[\"Version\"]\n", "        if base_model in attribute_mapping and version in attribute_mapping[base_model]:\n", "            return attribute_mapping[base_model][version]\n", "        return None, None, None\n", "\n", "    data_frame[[\"Prompt formatter\", \"Base model\", \"Retriever\"]] = data_frame.apply(\n", "        get_attributes, axis=1, result_type=\"expand\"\n", "    )\n", "\n", "    columns = data_frame.columns.tolist()\n", "    new_order = [\n", "        \"Label\",\n", "        \"Base model label\",\n", "        \"Base model\",\n", "        \"Version\",\n", "        \"Prompt formatter\",\n", "        \"Retriever\",\n", "    ] + [\n", "        col\n", "        for col in columns\n", "        if col\n", "        not in [\n", "            \"Label\",\n", "            \"Base model label\",\n", "            \"Prompt formatter\",\n", "            \"Base model\",\n", "            \"Retriever\",\n", "            \"Version\",\n", "        ]\n", "    ]\n", "    data_frame = data_frame[new_order]\n", "    return data_frame\n", "\n", "\n", "attributed_df = add_attributes(renamed_df)\n", "reset_display()\n", "attributed_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attributed_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, HTML\n", "\n", "\n", "def filter_old_runs(data_frame, round_denylist=None):\n", "    assert (\n", "        not data_frame[[\"Base model\", \"Prompt formatter\", \"Retriever\"]]\n", "        .isnull()\n", "        .values.any()\n", "    ), f\"Missing attributes in {data_frame[data_frame[['Base model', 'Prompt formatter', 'Retriever']].isnull().all(axis=1)]}\"\n", "\n", "    round_denylist = round_denylist or []\n", "\n", "    grouped = data_frame.groupby([\"Base model\", \"Version\", \"Context length\"])\n", "\n", "    def filter_group(group):\n", "        max_trials = group[\"Trials\"].max()\n", "        max_trial_rows = group[group[\"Trials\"] == max_trials]\n", "        valid_rows = max_trial_rows[~max_trial_rows[\"Round\"].isin(round_denylist)]\n", "        if valid_rows.empty:\n", "            return pd.DataFrame()\n", "        latest_round = valid_rows[\"Round\"].max()\n", "        return valid_rows[valid_rows[\"Round\"] == latest_round]\n", "\n", "    latest_run_data_frame = grouped.apply(filter_group).reset_index(drop=True)\n", "    return latest_run_data_frame\n", "\n", "\n", "def filter_minor_versions(data_frame):\n", "    data_frame[\"Version\"] = pd.to_numeric(data_frame[\"Version\"], errors=\"coerce\")\n", "    data_frame[\"Major_Version\"] = data_frame[\"Version\"].apply(lambda x: int(x))\n", "\n", "    def keep_latest_minor(group):\n", "        return group.loc[group[\"Version\"].idxmax()]\n", "\n", "    major_only_data_frame = (\n", "        data_frame.groupby([\"Base model\", \"Major_Version\", \"Context length\"])\n", "        .apply(keep_latest_minor)\n", "        .reset_index(drop=True)\n", "    )\n", "    major_only_data_frame = major_only_data_frame.drop(columns=[\"Major_Version\"])\n", "\n", "    return major_only_data_frame\n", "\n", "\n", "def beautify(data_frame):\n", "    beautiful_data_frame = data_frame.copy()\n", "    # Convert Version back to string without .0 for whole numbers\n", "    beautiful_data_frame[\"Version\"] = beautiful_data_frame[\"Version\"].apply(\n", "        lambda x: f\"{x:.1f}\".rstrip(\"0\").rstrip(\".\")\n", "    )\n", "    beautiful_data_frame[\"Prompt formatter\"] = beautiful_data_frame[\n", "        \"Prompt formatter\"\n", "    ].apply(lambda x: f\"{x:.0f}\" if x.is_integer() else f\"{x:.1f}\")\n", "    beautiful_data_frame[\"Context length\"] = beautiful_data_frame[\n", "        \"Context length\"\n", "    ].apply(lambda x: f\"{x}K\")\n", "    beautiful_data_frame[\"Round\"] = beautiful_data_frame[\"Round\"].apply(\n", "        lambda x: f\"{x:.0f}\"\n", "    )\n", "    beautiful_data_frame[\"Trials\"] = beautiful_data_frame[\"Trials\"].apply(\n", "        lambda x: f\"{x:.0f}\"\n", "    )\n", "    return beautiful_data_frame\n", "\n", "\n", "filtered_df = filter_old_runs(\n", "    attributed_df,\n", "    round_denylist=[\n", "        # 10,\n", "    ],\n", ")\n", "# filtered_df = filter_minor_versions(filtered_df)\n", "# filtered_df = filtered_df[filtered_df[\"Prompt formatter\"].isin([1, 3, 4, 7, 8])]\n", "filtered_df = filtered_df[\n", "    filtered_df[\"Base model label\"].isin(\n", "        [\n", "            \"claude\",\n", "            \"o1\",\n", "            \"o1_mini\",\n", "            \"gpt4o\",\n", "        ]\n", "    )\n", "]\n", "filtered_df = filtered_df[\n", "    filtered_df[\"Context length\"].isin(\n", "        [\n", "            \"16\",\n", "            # \"128\",\n", "        ]\n", "    )\n", "]\n", "filtered_df = filtered_df[filtered_df[\"Retriever\"].isin([\"1.18.3\"])]\n", "\n", "sorting_criteria = [\n", "    (\"Base model label\", True),\n", "    # (\"Prompt formatter\", False),\n", "    # (\"Context length\", True),\n", "    # (\"Retriever\", False),\n", "    # (\"Version\", False),\n", "    (\"Pass@1\", False),\n", "]\n", "filtered_df = filtered_df.sort_values(\n", "    by=[key for key, _ in sorting_criteria],  # type: ignore\n", "    ascending=[ascending for _, ascending in sorting_criteria],\n", ")\n", "beautiful_df = beautify(filtered_df)\n", "beautiful_df = beautiful_df.drop(\n", "    columns=[\n", "        \"Label\",\n", "        \"Base model label\",\n", "        \"Context length\",\n", "        \"Round\",\n", "        \"Trials\",\n", "        \"Retriever\",\n", "    ]\n", ")\n", "set_unlimited_display()\n", "display(HTML(beautiful_df.to_html(index=False)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "results_data = []\n", "for path in result_paths:\n", "    label = os.path.splitext(os.path.basename(path))[0]\n", "    with open(path) as file:\n", "        result = json.load(file)\n", "    run_result = list(result[\"runs\"].values())[0]\n", "    if \"html_report_url\" not in run_result[\"results\"]:\n", "        print(label, \"no html_report_url\")\n", "        continue\n", "    print(label, run_result[\"results\"][\"html_report_url\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "results_data = []\n", "for path in result_paths:\n", "    label = os.path.splitext(os.path.basename(path))[0]\n", "    with open(path) as file:\n", "        result = json.load(file)\n", "    run_result = list(result[\"runs\"].values())[0]\n", "    if \"artifact\" not in run_result[\"results\"]:\n", "        print(label, \"no artifact\")\n", "        continue\n", "    print(label, run_result[\"results\"][\"artifact\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
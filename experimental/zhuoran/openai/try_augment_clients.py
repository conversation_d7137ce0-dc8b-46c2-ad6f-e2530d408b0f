import base64
import json
from typing import Any

from base.prompt_format.common import (
    ChatRequestImage,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    ImageFormatType,
    ToolDefinition,
)
from base.third_party_clients.openai_client import OpenAIClient

IMAGE_PATH = "/mnt/efs/augment/user/zhuoran/images/ucb_1k.jpg"
MODEL_CALLER = "client-trial-notebook"


def load_image_as_base64(image_path: str) -> str:
    """Load an image file and convert it to base64 encoding."""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def create_function_result(
    tool_use_id: str, content: Any, is_error: bool = False
) -> ChatRequestNode:
    """Create a tool result node for a function call."""
    # Convert content to JSON string if it's not already a string
    if not isinstance(content, str):
        content = json.dumps(content)

    return ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id=tool_use_id,
            content=content,
            is_error=is_error,
        ),
        image_node=None,
    )


def create_image_node(
    image_path: str, format_type: ImageFormatType = ImageFormatType.PNG
) -> ChatRequestNode:
    """Create an image node from an image file."""
    base64_image = load_image_as_base64(image_path)
    return ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.IMAGE,
        text_node=None,
        tool_result_node=None,
        image_node=ChatRequestImage(
            image_data=base64_image,
            format=format_type,
        ),
    )


def create_text_node(content: str) -> ChatRequestNode:
    """Create a text node with the given content."""
    return ChatRequestNode(
        id=0,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content=content),
        tool_result_node=None,
        image_node=None,
    )


TOOL_DEFINITIONS = [
    ToolDefinition(
        name="get_weather",
        description="Get the current weather for a location",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "location": {"type": "string", "description": "The city name"},
                    "units": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "Temperature units",
                    },
                },
                "required": ["location"],
            }
        ),
    ),
]


def get_chat_history(
    image_support: bool = True, tool_support: bool = True
) -> list[Exchange]:
    chat_history = []
    if tool_support:
        chat_history += [
            Exchange(
                request_message="What's the weather like in current location today?",
                response_text=[
                    ChatResultNode(
                        id=0,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="I'll check the current weather in current location for you.",
                    ),
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            name="get_weather",
                            input={"location": "current location", "units": "celsius"},
                            tool_use_id="weather_tool_1",
                        ),
                    ),
                ],
            ),
            Exchange(
                request_message=[
                    create_function_result(
                        tool_use_id="weather_tool_1",
                        content="{'temperature': -20,}",
                    ),
                ],
                response_text="The weather in current location is -20°C.",
            ),
        ]
    else:
        chat_history += [
            Exchange(
                request_message="What's the weather like in current location today?",
                response_text="The weather in current location is -20°C.",
            ),
        ]
    if image_support:
        chat_history.append(
            Exchange(
                request_message=[
                    create_text_node("What's the environment in this image?"),
                    create_image_node(IMAGE_PATH, ImageFormatType.JPEG),
                ],
                response_text="It is a conference room.",
            )
        )
    else:
        chat_history.append(
            Exchange(
                request_message="What's in the image?",
                response_text="The alt-text shows 4 people in a conference room, dressed in T-shirts and shorts.",
            )
        )
    return chat_history


def get_current_message() -> list[ChatRequestNode]:
    return [
        create_text_node(
            "How many people are in the previous image? Are their clothes appropriate for the weather if outside?"
        ),
    ]


client = OpenAIClient(
    openai_api_key=open("/mnt/efs/augment/user/zhuoran/.tokens/openai").read().strip(),
    xai_api_key="",
    model_name="gpt-4.1-2025-04-14",
    temperature=0,
    max_output_tokens=1024,
)
image_support = True
tool_support = True

stream = client.generate_response_stream(
    model_caller=MODEL_CALLER,
    system_prompt="You are a helpful assistant that answers extremely concisely, and nevers says 1 more word not directly asked.",
    cur_message=get_current_message(),
    # chat_history=get_chat_history(image_support, tool_support),
    # tool_definitions=TOOL_DEFINITIONS,
    yield_final_parameters=True,
)

text = ""
tool_uses = []
final_parameters = None
try:
    for response in stream:
        if response.final_parameters:
            final_parameters = response.final_parameters
        elif response.tool_use:
            tool_uses.append(response.tool_use)
        else:
            text += response.text
except Exception as e:
    print(e)

print(final_parameters.keys())
print(text)
print(tool_uses)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import glob\n", "import os\n", "\n", "result_paths = glob.glob(\"/mnt/efs/augment/user/zhuoran/summaries/openai/*.json\")\n", "\n", "for path in result_paths:\n", "    print(os.path.basename(path))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.openai.tabulation_utils import (\n", "    add_attributes,\n", "    combine_means_and_stds,\n", "    rename_columns,\n", "    group_rows,\n", "    read_results,\n", "    reset_display,\n", "    set_unlimited_display,\n", "    filter_old_runs,\n", "    filter_minor_versions,\n", "    beautify,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = read_results(result_paths)\n", "reset_display()\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["grouped_df = group_rows(df)\n", "reset_display()\n", "grouped_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["combined_df = combine_means_and_stds(grouped_df)\n", "renamed_df = rename_columns(combined_df)\n", "print(renamed_df[\"Base model label\"].unique())\n", "\n", "reset_display()\n", "renamed_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attributed_df = add_attributes(renamed_df)\n", "reset_display()\n", "attributed_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["attributed_df[attributed_df[\"Base model\"].isnull()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import display, HTML\n", "\n", "\n", "filtered_df = filter_old_runs(\n", "    attributed_df,\n", "    round_denylist=[\n", "        10,\n", "        # 12,\n", "    ],\n", ")\n", "# filtered_df = filter_minor_versions(filtered_df)\n", "# filtered_df = filtered_df[filtered_df[\"Prompt formatter\"].isin([1, 3, 4, 7, 8])]\n", "filtered_df = filtered_df[\n", "    filtered_df[\"Base model label\"].isin(\n", "        [\n", "            \"claude\",\n", "            # \"wool\",\n", "            # \"flannel\",\n", "            \"linen\",\n", "            \"o1_mini\",\n", "            \"o1\",\n", "            \"o3_mini\",\n", "            \"o3\",\n", "            \"o4_mini\",\n", "            # \"gpt4o_chatgpt\",\n", "            # \"gpt4o\",\n", "            \"gpt4_1\",\n", "            \"gpt4_1_mini\",\n", "            # \"gpt4_1_nano\",\n", "            # \"gpt4-5\",\n", "            # \"gemini\",\n", "            \"gemini2_5_pro\",\n", "            # \"deepseek_r1\",\n", "            \"deepseek_v3\",\n", "            # \"grok2\",\n", "            # \"grok3\",\n", "            # \"grok3_mini\",\n", "            # \"qwen2-5\",\n", "            # \"qwq\",\n", "            # \"llama4_scout\",\n", "            # \"llama4_maverick\",\n", "        ]\n", "    )\n", "]\n", "# filtered_df = filtered_df[filtered_df[\"Context length\"].isin([\"16\"])]\n", "filtered_df = filtered_df[\n", "    filtered_df[\"Retriever\"].isin(\n", "        [\n", "            \"1.18.3\",\n", "            \"2.2\",\n", "            4,\n", "        ]\n", "    )\n", "]\n", "filtered_df = filtered_df[filtered_df[\"Round\"].isin([11])]\n", "# filtered_df = filtered_df[filtered_df[\"Prompt formatter\"].isin([3, 3.1])]\n", "filtered_df = filtered_df[\n", "    ~filtered_df[\"Version\"].isin([14.2, 14.3, 14.4, 17.01, 17.02])\n", "]\n", "filtered_df = filtered_df[filtered_df[\"Retriever\"].isin([4])]\n", "\n", "\n", "sorting_criteria = [\n", "    # (\"Base model label\", False),\n", "    # (\"Prompt formatter\", False),\n", "    # (\"Version\", False),\n", "    (\"Answer score\", False),\n", "]\n", "filtered_df = filtered_df.sort_values(\n", "    by=[key for key, _ in sorting_criteria],  # type: ignore\n", "    ascending=[ascending for _, ascending in sorting_criteria],\n", ")\n", "beautiful_df = beautify(filtered_df)\n", "beautiful_df = beautiful_df.drop(\n", "    columns=[\n", "        \"Label\",\n", "        \"Base model label\",\n", "        \"Context length\",\n", "        # \"Retriever\",\n", "        # \"Router\",\n", "        \"Round\",\n", "        # \"Trials\",\n", "    ]\n", ")\n", "set_unlimited_display()\n", "display(HTML(beautiful_df.to_html(index=False)))\n", "\n", "with open(\"/home/<USER>/results.csv\", \"w\") as f:\n", "    f.write(beautiful_df.to_csv(index=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "results_data = []\n", "for path in result_paths:\n", "    label = os.path.splitext(os.path.basename(path))[0]\n", "    with open(path) as file:\n", "        result = json.load(file)\n", "    run_result = list(result[\"runs\"].values())[0]\n", "    if \"html_report_url\" not in run_result[\"results\"]:\n", "        print(label, \"no html_report_url\")\n", "        continue\n", "    print(label, run_result[\"results\"][\"html_report_url\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "results_data = []\n", "for path in result_paths:\n", "    label = os.path.splitext(os.path.basename(path))[0]\n", "    with open(path) as file:\n", "        result = json.load(file)\n", "    run_result = list(result[\"runs\"].values())[0]\n", "    if \"artifact\" not in run_result[\"results\"]:\n", "        print(label, \"no artifact\")\n", "        continue\n", "    print(label, run_result[\"results\"][\"artifact\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
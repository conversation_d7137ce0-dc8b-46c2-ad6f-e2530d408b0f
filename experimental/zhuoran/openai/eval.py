import os
import subprocess
import tempfile

import yaml

models = []
# models.append(("deepseek_v3_v1_c4_p2", "deepseek-v3-16k-v1-fireworks-c4-p2-chat"))
# models.append(("deepseek_v3_v2_c4_p2", "deepseek-v3-16k-v2-fireworks-c4-p2-chat"))
# models.append(("deepseek_r1_v1_c4_p2", "deepseek-r1-16k-v1-fireworks-c4-p2-chat"))
# models.append(("deepseek_r1_v2_c4_p2", "deepseek-r1-16k-v2-fireworks-c4-p2-chat"))
# models.append(("claude_v" + str(version), f"claude-sonnet-16k-v{version}-chat"))
# models.append(("claude_v17-02", "claude-sonnet-v17-c4-p2-chat"))
# models.append(("linen_v17_c4_p2", "claude-linen-v17-direct-c4-p2-chat"))
# models.append(("gpt4o_chatgpt_v1", "gpt4o-chatgpt-latest-v1-c4-p2-chat"))
# models.append(("claude_v11-4_c4_p2", "claude-sonnet-3-5-16k-v11-4-c4-p2-chat"))
# models.append(("gemini2_5_pro_v1_c4_p2", "gemini-2-5-pro-v1-c4-p2-chat"))
# models.append(("gemini2_5_pro_v2_c4_p2", "gemini-2-5-pro-v2-c4-p2-chat"))
# models.append(("gemini2_5_pro_v3_c4_p2", "gemini-2-5-pro-v3-c4-p2-chat"))
# models.append(("gemini2_5_pro_v4_c4_p2", "gemini-2-5-pro-v4-c4-p2-chat"))
models.append(("gemini2_5_pro_v5_c4_p2", "gemini-2-5-pro-v4-c4-p2-chat"))
models.append(("gemini2_5_pro_v6_c4_p2", "gemini-2-5-pro-v4-c4-p2-chat"))
# models.append(("gemini2_5_flash_v1_c4_p2", "gemini-2-5-flash-v1-c4-p2-chat"))
# models.append(("gemini2_5_flash_v2_c4_p2", "gemini-2-5-flash-v2-c4-p2-chat"))
# models.append(("o1_pro_v1_c4_p2", "o1-pro-v1-c4-p2-chat"))
# models.append(("o3_v1_c4_p2", "o3-v1-c4-p2-chat"))
# models.append(("o4_mini_v1_c4_p2", "o4-mini-v1-c4-p2-chat"))
# models.append(("llama4_scout_v1_c4_p2", "llama4-scout-v1-fireworks-c4-p2-chat"))
# models.append(("llama4_maverick_v1_c4_p2", "llama4-maverick-v1-fireworks-c4-p2-chat"))
# models.append(("grok3_v1_c4_p2", "grok3-16k-v1-c4-p2-chat"))
# models.append(("grok3_mini_v1_c4_p2", "grok3-mini-16k-v1-c4-p2-chat"))
# models.append(("gpt4_1_16k_v1_c4_p2", "gpt4-1-v1-c4-p2-chat"))
# models.append(("gpt4_1_mini_v1_c4_p2", "gpt4-1-mini-v1-c4-p2-chat"))
# models.append(("gpt4_1_nano_v1_c4_p2", "gpt4-1-nano-v1-c4-p2-chat"))


tasks = [
    "augment_qa",
    # "instruct_humaneval",
]


def get_task_setups(task):
    if task == "augment_qa":
        template_path = "experimental/zhuoran/openai/template.yaml"
        task_shortname = ""
    elif task == "augment_qa_named":
        template_path = "experimental/zhuoran/openai/template.yaml"
        task_shortname = "_aqa"
    else:
        template_path = "experimental/zhuoran/openai/template_ihe.yaml"
        task_shortname = "_ihe"

    with open(template_path, "r") as file:
        template = yaml.safe_load(file)
    return template, task_shortname


for repitition in range(5):
    for task in tasks:
        template, task_shortname = get_task_setups(task)
        for name, model in models:
            template["system"]["model_name"] = model
            # template["system"]["client"]["url"] = (
            #     "https://staging-shard-0.api.augmentcode.com"
            # )
            # template["system"]["chat"]["retry_sleep_seconds"] = 60
            template["determined"]["name"] = f"{name}{task_shortname}_r11_{repitition}"

            # Write the config
            output_dir = tempfile.mkdtemp()
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, f"{name}.yaml")

            with open(output_path, "w") as file:
                yaml.dump(template, file, default_flow_style=False)

            print(f"Config has been written to {output_path}")

            with open(output_path, "r") as file:
                config = yaml.safe_load(file)

            print(yaml.dump(config))

            if task == "augment_qa":
                summary_directory = "/mnt/efs/augment/user/zhuoran/summaries/openai"
            else:
                summary_directory = "/mnt/efs/augment/user/zhuoran/summaries/humaneval"

            command = [
                "python",
                "/home/<USER>/augment/research/eval/eval.py",
                output_path,
                "--summary_path",
                f"{summary_directory}/{template['determined']['name']}.json",
            ]
            # command += ["--override_summary"]
            # command += ["--wait_for_completion"]
            subprocess.Popen(command, start_new_session=True)
    #         break
    #     break
    # break

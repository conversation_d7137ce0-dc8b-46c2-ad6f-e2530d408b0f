{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REQUEST_ID = \"\"\"\n", "e252a6b0-d823-4408-ac62-43abc264a35b\n", "\"\"\".strip()\n", "REQUEST_ID"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_infra import get_input_and_documents\n", "\n", "chat_prompt_input, _ = get_input_and_documents(REQUEST_ID)\n", "chat_prompt_input.message"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput\n", "from base.third_party_clients.anthropic_direct_client import ToolDefinition\n", "\n", "\n", "def get_final_parameters(\n", "    prompt_output: StructuredChatPromptOutput, tool_definitions=None\n", "):\n", "    assert isinstance(prompt_output, StructuredChatPromptOutput)\n", "\n", "    tool_definitions = tool_definitions or []\n", "    if prompt_output.retrieval_as_tool:\n", "        tool_definitions.append(\n", "            ToolDefinition(\n", "                \"codebase_retrieval\",\n", "                \"Retrieve relevant chunks of code from the codebase using a user query. This tool can be used only once in a conversation. Do not use this tool if it is already used.\",\n", "                json.dumps(\n", "                    {\n", "                        \"type\": \"object\",\n", "                        \"properties\": {\n", "                            \"query\": {\n", "                                \"type\": \"string\",\n", "                                \"description\": \"The query to search for.\",\n", "                            },\n", "                        },\n", "                        \"required\": [\"query\"],\n", "                    }\n", "                ),\n", "            )\n", "        )\n", "    return {\n", "        \"cur_message\": prompt_output.message,\n", "        \"chat_history\": list(prompt_output.chat_history),\n", "        \"system_prompt\": prompt_output.system_prompt,\n", "        \"tool_definitions\": tool_definitions,\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import TOKEN_APPORTIONMENT\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from base.prompt_format_chat.lib.system_prompts import get_claude_prompt_formatter_v11\n", "\n", "token_counter = ClaudeTokenCounter()\n", "\n", "prompt_formatter = StructuredBinksPromptFormatter.create(\n", "    token_counter,\n", "    TOKEN_APPORTIONMENT,\n", "    system_prompt_factory=get_claude_prompt_formatter_v11,\n", "    retrieval_section_version=4,\n", "    retrieval_as_tool=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import render_prompt\n", "\n", "prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "rendered_prompt = render_prompt(prompt_output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(rendered_prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient\n", "\n", "final_parameters = get_final_parameters(prompt_output)\n", "anthropic_client = AnthropicDirectClient(\"\", \"\", 0, 1024)\n", "g = anthropic_client.generate_response_stream(**final_parameters)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for gg in g:\n", "    print(gg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic.types import ToolParam\n", "\n", "from research.tools.chat_replay.replay_utils import get_vertex_ai_client, MODEL_NAMES\n", "\n", "vertex_ai_client = get_vertex_ai_client()\n", "\n", "vertex_ai_client.client.messages.create(\n", "    model=MODEL_NAMES[2],\n", "    max_tokens=1024,\n", "    messages=[\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"tool_use\",\n", "                    \"id\": \"0\",\n", "                    \"name\": \"codebase_retrieval\",\n", "                    \"input\": {\"query\": \"Find the value of SECRET.\"},\n", "                },\n", "            ],\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"tool_result\",\n", "                    \"tool_use_id\": \"0\",\n", "                    \"content\": \"SECRET = 1216\",\n", "                },\n", "            ],\n", "        },\n", "        {\"role\": \"user\", \"content\": \"What is the value of SECRET?\"},\n", "    ],\n", "    tools=[\n", "        ToolParam(\n", "            name=\"codebase_retrieval\",\n", "            description=\"Retrieve relevant chunks of code from the codebase using a user query. This tool can be used only once in a conversation. Do not use this tool if it is already used.\",\n", "            input_schema={\n", "                \"type\": \"object\",\n", "                \"properties\": {\n", "                    \"query\": {\n", "                        \"type\": \"string\",\n", "                        \"description\": \"The query to search for.\",\n", "                    },\n", "                },\n", "                \"required\": [\"query\"],\n", "            },\n", "        ),\n", "    ],\n", "    tool_choice={\"type\": \"none\"},\n", "    # system=system_message,\n", "    # temperature=TEMPERAURE,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from base.datasets.tenants import (\n", "    DOGFOOD_SHARD,\n", "    VANGUARD,\n", "    AITUTOR_MERCOR,\n", "    AITUTOR_TURING,\n", ")\n", "\n", "TENANT = DOGFOOD_SHARD\n", "START_DATE = \"2024-01-01\"\n", "\n", "name = f\"{TENANT.name}_since_{START_DATE.replace('-', '')}\"\n", "\n", "OUTPUT_DIR = Path(\"/mnt/efs/augment/user/zhuoran/retrieval_tool\") / name\n", "OUTPUT_PATH = OUTPUT_DIR / \"stats.csv\"\n", "\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "if OUTPUT_PATH.exists():\n", "    raise ValueError(f\"Output path {OUTPUT_PATH} already exists, skipping\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from typing import Iterator\n", "\n", "from google.cloud import bigquery\n", "\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "\n", "TENANT_NAME_TO_DATASET = {\n", "    \"dogfood-shard\": \"system-services-prod.staging_request_insight_full_export_dataset\",\n", "    # \"dogfood-shard\": \"us_staging_request_insight_analytics_dataset\",\n", "    # \"aitutor-mercor\": \"us_prod_request_insight_analytics_dataset\",\n", "    # \"aitutor-turing\": \"us_prod_request_insight_analytics_dataset\",\n", "    # \"i0-vanguard0\": \"us_prod_request_insight_analytics_dataset\",\n", "    # \"i1-vanguard0\": \"us_prod_request_insight_analytics_dataset\",\n", "}\n", "\n", "if OUTPUT_PATH.exists():\n", "    print(f\"Loading existing results from {OUTPUT_PATH}\")\n", "    df = pd.read_csv(OUTPUT_PATH)\n", "else:\n", "    print(\"Running BigQuery to generate results...\")\n", "\n", "    query = f\"\"\"\\\n", "    WITH request AS (\n", "    SELECT *\n", "    FROM `{TENANT_NAME_TO_DATASET[TENANT.name]}.request_event`\n", "    WHERE time >= \"{START_DATE}\"\n", "    ),\n", "\n", "    chat_host_request AS (\n", "    SELECT\n", "    request_id,\n", "    JSON_VALUE(raw_json, \"$.request.model_name\") as model_name\n", "    FROM request\n", "    WHERE event_type = 'chat_host_request'\n", "    ),\n", "\n", "    request_metadata AS (\n", "    SELECT *\n", "    FROM request\n", "    WHERE event_type = 'request_metadata'\n", "    AND JSON_VALUE(raw_json, \"$.user_id\") NOT IN (\"health-check-1\", \"review-edit-bot\", \"eval-determined-bot\")\n", "    AND JSON_VALUE(raw_json, \"$.user_agent\") NOT IN (\"api_proxy_client/0 (Python)\")\n", "    ),\n", "\n", "    chat_host_response_raw AS (\n", "    SELECT\n", "    request_id,\n", "    JSON_VALUE(raw_json, \"$.response.text\") as response_text,\n", "    ARRAY_LENGTH(REGEXP_EXTRACT_ALL(JSON_VALUE(raw_json, \"$.response.text\"), r'<augment_code_snippet')) as open_tags,\n", "    ARRAY_LENGTH(REGEXP_EXTRACT_ALL(JSON_VALUE(raw_json, \"$.response.text\"), r'</augment_code_snippet')) as close_tags\n", "    FROM request\n", "    WHERE event_type = 'chat_host_response'\n", "    ),\n", "\n", "    chat_host_response AS (\n", "    SELECT\n", "    request_id,\n", "    open_tags > 0 as have_xml_tags,\n", "    (open_tags - close_tags) > 0 as have_mismatched_xml_tags\n", "    FROM chat_host_response_raw\n", "    )\n", "\n", "    SELECT\n", "    DATE_TRUNC(DATE(request_metadata.time), WEEK) as week,\n", "    chat_host_request.model_name,\n", "    COUNT(*) as total_responses,\n", "    COUNTIF(chat_host_response.have_xml_tags) as responses_with_tags,\n", "    COUNTIF(chat_host_response.have_mismatched_xml_tags) as responses_with_mismatched_tags,\n", "    ROUND(100.0 * COUNTIF(chat_host_response.have_xml_tags) / COUNT(*), 2) as percent_with_tags,\n", "    ROUND(100.0 * COUNTIF(chat_host_response.have_mismatched_xml_tags) / NULLIF(COUNTIF(chat_host_response.have_xml_tags), 0), 2) as percent_mismatched_of_tagged\n", "    FROM request_metadata\n", "    JOIN chat_host_request\n", "    ON chat_host_request.request_id = request_metadata.request_id\n", "    JOIN chat_host_response\n", "    ON chat_host_response.request_id = request_metadata.request_id\n", "    GROUP BY week, model_name\n", "    ORDER BY model_name, week\n", "    \"\"\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=TENANT.project_id, credentials=gcp_creds)\n", "\n", "    # Execute query and get results as DataFrame\n", "    df = bigquery_client.query(query).to_dataframe()\n", "\n", "    # Save to CSV\n", "    df.to_csv(OUTPUT_PATH, index=False)\n", "    print(f\"Saved results to {OUTPUT_PATH}\")\n", "\n", "# Display summary\n", "print(\"\\nDataFrame shape:\", df.shape)\n", "print(\"\\nFirst few rows:\")\n", "print(df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["THRESHOLD = 500\n", "\n", "# Filter out rows with less than THRESHOLD tagged responses\n", "df_filtered = df[df[\"responses_with_tags\"] >= THRESHOLD].copy()\n", "\n", "# Get unique model names\n", "unique_models = df_filtered[\"model_name\"].dropna().unique()\n", "print(\"\\nUnique models in the dataset:\")\n", "for model in sorted(unique_models):\n", "    print(f\"- {model}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def is_new_claude(model_name: str) -> bool:\n", "    if (\n", "        \"-v2-\" in model_name\n", "        or \"-v3-\" in model_name\n", "        or \"-v4-\" in model_name\n", "        or \"-v10-\" in model_name\n", "    ):\n", "        return False\n", "    return True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Convert week to datetime for better x-axis formatting\n", "df_filtered[\"week\"] = pd.to_datetime(df_filtered[\"week\"])\n", "\n", "# Create figure with two subplots side by side\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8))\n", "\n", "# Plot 1: Mismatched Tags Percentage\n", "for model in df_filtered[\"model_name\"].unique():\n", "    model_data = df_filtered[df_filtered[\"model_name\"] == model]\n", "    is_new = is_new_claude(model)\n", "    linestyle = \"-\" if is_new else \"--\"\n", "    label = model if is_new else f\"{model} (<PERSON> V1)\"\n", "    ax1.plot(\n", "        model_data[\"week\"],\n", "        model_data[\"percent_mismatched_of_tagged\"],\n", "        marker=\"o\",\n", "        label=label,\n", "        linewidth=2,\n", "        linestyle=linestyle,\n", "    )\n", "\n", "ax1.grid(True, linestyle=\"--\", alpha=0.7)\n", "ax1.set_xlabel(\"Week\")\n", "ax1.set_ylabel(\"Mismatched Tags (%)\")\n", "ax1.set_title(\"Percentage of Responses with Mismatched Tags\")\n", "ax1.tick_params(axis=\"x\", rotation=45)\n", "\n", "# Plot 2: Percentage with Tags\n", "for model in df_filtered[\"model_name\"].unique():\n", "    model_data = df_filtered[df_filtered[\"model_name\"] == model]\n", "    is_new = is_new_claude(model)\n", "    linestyle = \"-\" if is_new else \"--\"\n", "    label = model if is_new else f\"{model} (<PERSON> V1)\"\n", "    ax2.plot(\n", "        model_data[\"week\"],\n", "        model_data[\"percent_with_tags\"],\n", "        marker=\"o\",\n", "        label=label,\n", "        linewidth=2,\n", "        linestyle=linestyle,\n", "    )\n", "\n", "ax2.grid(True, linestyle=\"--\", alpha=0.7)\n", "ax2.set_xlabel(\"Week\")\n", "ax2.set_ylabel(\"Responses with Tags (%)\")\n", "ax2.set_title(\"Percentage of Responses with Tags\")\n", "ax2.tick_params(axis=\"x\", rotation=45)\n", "\n", "# Add legend to the right of the second subplot\n", "lines, labels = ax2.get_legend_handles_labels()\n", "fig.legend(lines, labels, bbox_to_anchor=(1.02, 0.5), loc=\"center left\")\n", "\n", "plt.suptitle(\n", "    f\"Response Statistics Over Time (filtered for >{THRESHOLD} responses with XML tags)\",\n", "    y=1.05,\n", ")\n", "\n", "# Adjust layout to prevent overlapping\n", "plt.tight_layout()\n", "\n", "# Show the plot\n", "plt.show()\n", "\n", "# Print summary statistics for both metrics\n", "print(\"\\nSummary statistics for filtered data:\")\n", "summary = df_filtered.groupby(\"model_name\").agg(\n", "    {\n", "        \"percent_mismatched_of_tagged\": [\"mean\", \"min\", \"max\"],\n", "        \"percent_with_tags\": [\"mean\", \"min\", \"max\"],\n", "    }\n", ")\n", "print(summary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
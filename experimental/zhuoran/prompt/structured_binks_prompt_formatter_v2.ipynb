{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8192\n", "6144\n", "12288\n", "8170\n", "8006.************\n"]}], "source": ["import dataclasses\n", "\n", "from base.prompt_format_chat.structured_binks_prompt_formatter_v2 import (\n", "    StructuredBinksPromptFormatterV2,\n", ")\n", "from base.prompt_format_chat.structured_binks_prompt_formatter_v2_test import (\n", "    CHAT_PROMPT_INPUT,\n", "    TOKEN_APPORTIONMENT,\n", "    _get_chat_history_with_tool_calls,\n", "    _get_system_prompt,\n", "    _get_user_guided_retrieved_chunks_single_file,\n", "    RETRIEVED_CHUNKS,\n", ")\n", "from base.prompt_format_chat.lib.token_counter import RoughTokenCounter\n", "\n", "token_counter = RoughTokenCounter()\n", "prompt_formatter = StructuredBinksPromptFormatterV2.create(\n", "    token_counter, TOKEN_APPORTIONMENT, _get_system_prompt\n", ")\n", "chat_history = _get_chat_history_with_tool_calls()\n", "general_chunks = RETRIEVED_CHUNKS\n", "user_guided_chunks = _get_user_guided_retrieved_chunks_single_file()\n", "chat_prompt_input = dataclasses.replace(\n", "    CHAT_PROMPT_INPUT,\n", "    chat_history=chat_history,\n", "    retrieved_chunks=general_chunks,\n", "    # retrieved_chunks=user_guided_chunks,\n", "    # retrieved_chunks=general_chunks + user_guided_chunks,\n", ")\n", "print(TOKEN_APPORTIONMENT.retrieval_len)\n", "print(TOKEN_APPORTIONMENT.retrieval_len_per_each_user_guided_file)\n", "print(TOKEN_APPORTIONMENT.retrieval_len_for_user_guided)\n", "prompt = prompt_formatter.format_prompt(chat_prompt_input)\n", "print(token_counter.count_tokens_in_request(prompt.chat_history[0].request_message))\n", "print(\n", "    token_counter.count_tokens_in_request(prompt.chat_history[0].request_message) * 0.98\n", ")\n", "# print(prompt.system_prompt)\n", "# print(prompt.message)\n", "# for exchange in prompt.chat_history:\n", "#     print(exchange.request_message)\n", "#     print(exchange.response_text)\n", "# print(prompt.tools)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9999 1\n"]}], "source": ["from base.prompt_format_chat.structured_binks_prompt_formatter_v2_test import (\n", "    _create_large_text,\n", "    _verify_token_count,\n", ")\n", "from base.prompt_format_chat.structured_binks_prompt_formatter_v2 import (\n", "    split_short_term_chat_history,\n", ")\n", "\n", "token_counter = RoughTokenCounter()\n", "prompt_formatter = StructuredBinksPromptFormatterV2.create(\n", "    token_counter, TOKEN_APPORTIONMENT, _get_system_prompt\n", ")\n", "\n", "# Create a large current message\n", "large_current_message = _create_large_text(\"This is a large current message: \")\n", "\n", "# Create a large selected code\n", "large_selected_code = _create_large_text(\"// This is a large selected code section:\\n\")\n", "\n", "# Verify token counts\n", "_verify_token_count(token_counter, large_current_message, 25000, 35000)\n", "_verify_token_count(token_counter, large_selected_code, 25000, 35000)\n", "\n", "# Create a chat prompt input with both large components\n", "chat_prompt_input = dataclasses.replace(\n", "    CHAT_PROMPT_INPUT,\n", "    message=large_current_message,\n", "    selected_code=large_selected_code,\n", "    context_code_exchange_request_id=\"new\",  # Mark as newly selected code\n", ")\n", "\n", "earlier_history, short_term_history = split_short_term_chat_history(\n", "    chat_prompt_input.chat_history\n", ")\n", "print(len(earlier_history), len(short_term_history))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
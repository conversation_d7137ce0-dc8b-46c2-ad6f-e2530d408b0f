{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests without redoing retrieval\n", "\n", "This notebook includes an example of how to replay a Chat request with its exactly original model inputs, without redoing retrieval.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REQUEST_ID = \"\"\"\n", "f4035832-ec8f-44ca-ae76-328f34558182\n", "\"\"\".strip()\n", "REQUEST_ID"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replay from <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import decode_prompt\n", "\n", "with open(\"/home/<USER>/t.md\", \"r\") as f:\n", "    prompt_output = decode_prompt(f.read())\n", "print(prompt_output.message)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "\n", "from research.tools.chat_replay.replay_utils import (\n", "    TOKEN_APPORTIONMENT,\n", "    run_claude,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "from base.prompt_format_chat.lib import system_prompts\n", "from research.tools.chat_replay.replay_utils import (\n", "    fix_prompt_output,\n", ")\n", "\n", "# system_prompt = system_prompts.CLAUDE_CODEBLOCKS_XML_SYSTEM_PROMPT\n", "system_prompt = system_prompts.CLAUDE_SYSTEM_PROMPT_V11\n", "prompt_output = dataclasses.replace(prompt_output, system_prompt=system_prompt)\n", "prompt_output = fix_prompt_output(prompt_output, backtick_count=4)\n", "response, _ = run_claude(prompt_output, base_model_version=2)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import count_tags\n", "\n", "count_tags(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replay from RI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_infra import get_input_and_documents\n", "\n", "chat_prompt_input, _ = get_input_and_documents(REQUEST_ID)\n", "chat_prompt_input.message"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    \"binks-claude-v11-1\", TOKEN_APPORTIONMENT\n", ")\n", "prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "response, _ = run_claude(prompt_output, base_model_version=2)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    \"binks-claude-v11-1\", TOKEN_APPORTIONMENT\n", ")\n", "prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "response, _ = run_claude(prompt_output, base_model_version=1)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    \"binks-claude-v4\", TOKEN_APPORTIONMENT\n", ")\n", "prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "response, _ = run_claude(prompt_output, base_model_version=2)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    \"binks-claude-v4\", TOKEN_APPORTIONMENT\n", ")\n", "prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "response, _ = run_claude(prompt_output, base_model_version=1)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
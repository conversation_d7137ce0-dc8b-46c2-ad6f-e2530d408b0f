{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay Chat requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["REQUEST_ID = \"\"\"\n", "f4035832-ec8f-44ca-ae76-328f34558182\n", "\"\"\".strip()\n", "REQUEST_ID"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_infra import get_input_and_documents\n", "\n", "chat_prompt_input, documents = get_input_and_documents(REQUEST_ID)\n", "chat_prompt_input.message"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.systems.remote_chat_system import RemoteChatSystem\n", "from research.core.artifacts import collect_artifacts\n", "\n", "\n", "def generate_chat_response(url, model_name, input_, documents):\n", "    remote_config = {\n", "        \"client\": {\"url\": url},\n", "        \"model_name\": model_name,\n", "    }\n", "    remote_chat = RemoteChatSystem.from_yaml_config(\n", "        remote_config,\n", "    )\n", "    remote_chat.load()\n", "    remote_chat.add_docs(documents)\n", "\n", "    with collect_artifacts() as collector_manager:\n", "        chat_response = remote_chat.generate(input_)\n", "        artifacts = collector_manager.get_artifacts()\n", "    request_id = artifacts[0][\"request_id\"]\n", "    return chat_response, request_id"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["responses = {}\n", "request_ids = {}"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["DOGFOOD_SHARD_URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "DOGFOOD_SHARD_RI_LINK_TEMPLATE = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"v10\"\n", "responses[label], request_ids[label] = generate_chat_response(\n", "    DOGFOOD_SHARD_URL, \"claude-sonnet-3-5-16k-v10-chat\", chat_prompt_input, documents\n", ")\n", "print(DOGFOOD_SHARD_RI_LINK_TEMPLATE.format(request_id=request_ids[label]))\n", "print()\n", "print(responses[label].generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"v11_1\"\n", "responses[label], request_ids[label] = generate_chat_response(\n", "    DOGFOOD_SHARD_URL, \"claude-sonnet-3-5-16k-v11-1-chat\", chat_prompt_input, documents\n", ")\n", "print(DOGFOOD_SHARD_RI_LINK_TEMPLATE.format(request_id=request_ids[label]))\n", "print()\n", "print(responses[label].generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label = \"v11\"\n", "responses[label], request_ids[label] = generate_chat_response(\n", "    DOGFOOD_SHARD_URL, \"claude-sonnet-3-5-16k-v11-chat\", chat_prompt_input, documents\n", ")\n", "print(DOGFOOD_SHARD_RI_LINK_TEMPLATE.format(request_id=request_ids[label]))\n", "print()\n", "print(responses[label].generated_text)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["DEV_URL = \"https://dev-zhuoran.us-central.api.augmentcode.com/\"\n", "DEV_RI_LINK_TEMPLATE = \"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{request_id}\""]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["label = \"o1_mini_16k_v3_1\"\n", "responses[label], request_ids[label] = generate_chat_response(\n", "    DEV_URL, \"o1-mini-16k-v3-1-chat\", chat_prompt_input, documents\n", ")\n", "print(DEV_RI_LINK_TEMPLATE.format(request_id=request_ids[label]))\n", "print()\n", "print(responses[label].generated_text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
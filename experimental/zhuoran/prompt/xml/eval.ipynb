{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["99\n"]}], "source": ["import glob\n", "import json\n", "\n", "from experimental.zhuoran.replay_utils import (\n", "    chat_prompt_input_from_dict,\n", ")\n", "\n", "\n", "data_paths = glob.glob(\"/mnt/efs/augment/user/zhuoran/prompt/xml/data/*.json\")\n", "chat_prompt_inputs = []\n", "for path in data_paths:\n", "    with open(path) as file:\n", "        chat_prompt_dict = json.load(file)\n", "    chat_prompt_input = chat_prompt_input_from_dict(chat_prompt_dict)\n", "    chat_prompt_inputs.append(chat_prompt_input)\n", "\n", "print(len(chat_prompt_inputs))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "from experimental.zhuoran.replay_utils import (\n", "    count_tags,\n", "    run_claude,\n", "    fix_input,\n", ")\n", "\n", "\n", "def run_example(\n", "    chat_prompt_input,\n", "    prompt_formatter,\n", "    backtick_count,\n", "    base_model_version=2,\n", "    system_prompt=\"\",\n", "):\n", "    chat_prompt_input = fix_input(chat_prompt_input, backtick_count)\n", "    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "    if system_prompt:\n", "        print(\"Replacing system prompt\")\n", "        prompt_output = dataclasses.replace(prompt_output, system_prompt=system_prompt)\n", "    print(prompt_output.message.splitlines()[-1])\n", "    response, _ = run_claude(prompt_output, base_model_version=base_model_version)\n", "    opening_count, closing_count = count_tags(response)\n", "    print(f\"{opening_count}, {closing_count}\")\n", "    return response, prompt_output, opening_count, closing_count"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You can update to remove the logic for checking tenant age\n", "\u001b[2m2025-01-08 09:01:27\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet-v2@20241022\u001b[0m\n", "1, 1\n"]}], "source": ["from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from experimental.zhuoran.replay_utils import TOKEN_APPORTIONMENT\n", "\n", "prompt_formatter_name = \"binks-claude-v11\"\n", "retrieval_section_version = 2\n", "backtick_count = 4\n", "base_model_version = 2\n", "prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    prompt_formatter_name,  # type: ignore\n", "    TOKEN_APPORTIONMENT,\n", "    retrieval_section_version,\n", "    backtick_count,\n", ")\n", "for i in range(len(chat_prompt_inputs)):\n", "    response, prompt_output, opening_count, closing_count = run_example(\n", "        chat_prompt_input=chat_prompt_inputs[i],\n", "        prompt_formatter=prompt_formatter,\n", "        backtick_count=backtick_count,\n", "        base_model_version=base_model_version,\n", "    )\n", "    # print(response)\n", "    break"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).\n", "- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: \"My search failed to locate the mentioned information.\" Avoid mentioning access limitations or mentioning \"provided excerpts\". Then, encourage the user to share more details or, alternatively, attach the relevant files using the \"@\" syntax in the chat (e.g., \"@path/to/file.py\").\n", "- Do not apologize.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "{backticks}python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "{backticks}\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "{backticks}yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "{backticks}\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "{backticks}ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "{backticks}\n", "</augment_code_snippet>\"\"\""]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Replacing system prompt\n", "You can update to remove the logic for checking tenant age\n", "1, 0\n"]}], "source": ["prompt_formatter_name = \"binks-claude-v10\"\n", "retrieval_section_version = 2\n", "backtick_count = 4\n", "base_model_version = 2\n", "prompt_formatter = get_structured_chat_prompt_formatter_by_name(\n", "    prompt_formatter_name,  # type: ignore\n", "    TOKEN_APPORTIONMENT,\n", "    retrieval_section_version,\n", "    backtick_count,\n", ")\n", "chat_prompt_input = chat_prompt_inputs[0]\n", "response, prompt_output, opening_count, closing_count = run_example(\n", "    chat_prompt_input=chat_prompt_input,\n", "    prompt_formatter=prompt_formatter,\n", "    backtick_count=backtick_count,\n", "    base_model_version=base_model_version,\n", "    system_prompt=SYSTEM_PROMPT.format(backticks=\"`\" * backtick_count),\n", ")\n", "# print(response)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------\n", "system_prompt:\n", "----------------------------------------\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).\n", "- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: \"My search failed to locate the mentioned information.\" Avoid mentioning access limitations or mentioning \"provided excerpts\". Then, encourage the user to share more details or, alternatively, attach the relevant files using the \"@\" syntax in the chat (e.g., \"@path/to/file.py\").\n", "- Do not apologize.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "````python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "````\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "````yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "````\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "````ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "````\n", "</augment_code_snippet>\n", "\n", "----------------------------------------\n", "current message:\n", "----------------------------------------\n", "I have the file `PotentialPowerUser.sql` open and has selected part of the code.\n", "\n", "Here is the full file:\n", "\n", "<augment_code_snippet path=\"PotentialPowerUser.sql\" mode=\"EXCERPT\">\n", "````\n", "[START SELECTED REGION]\n", "...\n", "[selected code goes here]\n", "...\n", "[END SELECTED REGION]\n", "\n", "\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is the selected code:\n", "\n", "<augment_code_snippet path=\"PotentialPowerUser.sql\" mode=\"EXCERPT\">\n", "````\n", "WITH tenant_first_activity AS (\n", "    -- Get the first activity date for each tenant\n", "    SELECT\n", "        tenant,\n", "        MIN(DATE(time, \"America/Los_Angeles\")) as first_activity_date\n", "    FROM us_prod_request_insight_analytics_dataset.customers_request_metadata\n", "    GROUP BY tenant\n", "),\n", "new_tenants AS (\n", "    -- Modified to only look at tenants created in last 30 days\n", "    SELECT tenant\n", "    FROM tenant_first_activity\n", "    WHERE first_activity_date > DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)\n", "),\n", "all_interactions AS (\n", "    -- Combine both completion and chat interactions for session analysis\n", "    SELECT \n", "        user_id,\n", "        tenant,\n", "        time as interaction_time,\n", "        'completion' as interaction_type\n", "    FROM us_prod_request_insight_analytics_dataset.completion_request\n", "    WHERE DATE(time, \"America/Los_Angeles\") >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)\n", "    AND tenant IN (SELECT tenant FROM new_tenants)\n", "    UNION ALL\n", "    SELECT \n", "        user_id,\n", "        tenant,\n", "        time as interaction_time,\n", "        'chat' as interaction_type\n", "    FROM us_prod_request_insight_analytics_dataset.customers_request_metadata\n", "    WHERE request_type = 'CHAT'\n", "    AND DATE(time, \"America/Los_Angeles\") >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)\n", "    AND tenant IN (SELECT tenant FROM new_tenants)\n", "),\n", "daily_usage_stats AS (\n", "    SELECT\n", "        user_id,\n", "        tenant,\n", "        -- Regular usage metrics\n", "        COUNT(DISTINCT usage_date) as days_active,\n", "        ROUND(COUNT(*) / COUNT(DISTINCT usage_date), 2) as avg_interactions_per_active_day,\n", "        -- Coefficient of variation (standard deviation / mean)\n", "        ROUND(STDDEV(daily_count) / NULLIF(AVG(daily_count), 0) * 100, 2) as usage_variation_coefficient,\n", "        -- Percentage of days active - Fix the calculation\n", "        ROUND(COUNT(DISTINCT usage_date) / \n", "              NULLIF(DATE_DIFF(MAX(usage_date), MIN(usage_date), DAY) + 1, 0) * 100, 2) as active_days_percentage\n", "    FROM (\n", "        SELECT\n", "            user_id,\n", "            tenant,\n", "            DATE(interaction_time, \"America/Los_Angeles\") as usage_date,\n", "            COUNT(*) as daily_count\n", "        FROM all_interactions\n", "        GROUP BY user_id, tenant, DATE(interaction_time, \"America/Los_Angeles\")\n", "    )\n", "    GROUP BY user_id, tenant\n", "),\n", "engagement_intensity AS (\n", "    SELECT\n", "        user_id,\n", "        tenant,\n", "        -- Intensity metrics\n", "        AVG(CASE \n", "            WHEN is_work_hours = 1 THEN daily_count \n", "        END) as avg_work_hours_intensity,\n", "        AVG(CASE \n", "            WHEN is_work_hours = 0 THEN daily_count \n", "        END) as avg_non_work_hours_intensity,\n", "        -- Work hours ratio refined\n", "        ROUND(SUM(CASE WHEN is_work_hours = 1 THEN daily_count ELSE 0 END) / \n", "              NULLIF(SUM(daily_count), 0) * 100, 2) as work_hours_usage_ratio\n", "    FROM (\n", "        SELECT\n", "            user_id,\n", "            tenant,\n", "            DATE(interaction_time, \"America/Los_Angeles\") as usage_date,\n", "            CASE \n", "                WHEN EXTRACT(HOUR FROM TIMESTAMP(FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', interaction_time), \"America/Los_Angeles\")) BETWEEN 9 AND 16 \n", "                    AND EXTRACT(DAYOFWEEK FROM TIMESTAMP(FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', interaction_time), \"America/Los_Angeles\")) BETWEEN 2 AND 6\n", "                THEN 1 ELSE 0 \n", "            END as is_work_hours,\n", "            COUNT(*) as daily_count\n", "        FROM all_interactions\n", "        GROUP BY user_id, tenant, usage_date, is_work_hours\n", "    )\n", "    GROUP BY user_id, tenant\n", "),\n", "user_patterns AS (\n", "    SELECT\n", "        user_id,\n", "        tenant,\n", "        -- Work hours usage (9AM-5PM Pacific Time, Monday-Friday)\n", "        COUNTIF(EXTRACT(HOUR FROM TIMESTAMP(FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', interaction_time), \"America/Los_Angeles\")) BETWEEN 9 AND 16 \n", "                AND EXTRACT(DAY<PERSON>FWEEK FROM TIMESTAMP(FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', interaction_time), \"America/Los_Angeles\")) BETWEEN 2 AND 6) as work_hours_usage,\n", "        COUNT(*) as total_usage,\n", "        -- Session analysis (30 min gap)\n", "        COUNT(DISTINCT CASE \n", "            WHEN session_start = 1 THEN session_date\n", "        END) as distinct_sessions,\n", "        -- Average time between interactions\n", "        AVG(CASE \n", "            WHEN time_diff < 480 THEN time_diff\n", "        END) as avg_time_between_interactions\n", "    FROM (\n", "        SELECT \n", "            user_id,\n", "            tenant,\n", "            interaction_time,\n", "            DATE(TIMESTAMP(FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', interaction_time), \"America/Los_Angeles\")) as session_date,\n", "            CASE \n", "                WHEN TIMESTAMP_DIFF(interaction_time, LAG(interaction_time) OVER (PARTITION BY user_id, tenant ORDER BY interaction_time), MINUTE) >= 30 \n", "                OR LAG(interaction_time) OVER (PARTITION BY user_id, tenant ORDER BY interaction_time) IS NULL \n", "                THEN 1\n", "                ELSE 0\n", "            END as session_start,\n", "            TIMESTAMP_DIFF(interaction_time, LAG(interaction_time) OVER (PARTITION BY user_id, tenant ORDER BY interaction_time), MINUTE) as time_diff\n", "        FROM all_interactions\n", "    )\n", "    GROUP BY user_id, tenant\n", "),\n", "completion_stats AS (\n", "    -- First get all completion resolutions\n", "    SELECT \n", "        cr.user_id,\n", "        cr.tenant,\n", "        cr.request_id,\n", "        cr.time as request_time,\n", "        res.accepted,\n", "        CASE WHEN res.accepted = TRUE THEN 1 ELSE 0 END as is_accepted\n", "    FROM us_prod_request_insight_analytics_dataset.completion_request cr\n", "    INNER JOIN us_prod_request_insight_analytics_dataset.completion_resolution res\n", "        ON cr.request_id = res.request_id\n", "    WHERE DATE(cr.time, \"America/Los_Angeles\") >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)\n", "    AND cr.tenant IN (SELECT tenant FROM new_tenants)\n", "),\n", "user_completion_stats AS (\n", "    -- Aggregate completion stats per user\n", "    SELECT\n", "        user_id,\n", "        tenant,\n", "        COUNT(*) as total_completion_requests,\n", "        SUM(is_accepted) as accepted_completions,\n", "        COUNT(*) - SUM(is_accepted) as rejected_completions,\n", "        MIN(DATE(request_time, \"America/Los_Angeles\")) as first_completion_date,\n", "        MIN(CASE WHEN running_accepted_count >= 100 THEN DATE(request_time, \"America/Los_Angeles\") END) as date_100_completions\n", "    FROM (\n", "        SELECT \n", "            *,\n", "            SUM(is_accepted) OVER (PARTITION BY user_id, tenant ORDER BY request_time) as running_accepted_count\n", "        FROM completion_stats\n", "    )\n", "    GROUP BY user_id, tenant\n", "),\n", "chat_stats AS (\n", "    SELECT \n", "        user_id,\n", "        tenant,\n", "        COUNT(DISTINCT request_id) as chat_count,\n", "        MIN(DATE(time, \"America/Los_Angeles\")) as first_chat_date,\n", "        MIN(CASE WHEN running_chat_count >= 100 THEN DATE(time, \"America/Los_Angeles\") END) as date_100_chats\n", "    FROM (\n", "        SELECT \n", "            *,\n", "            COUNT(*) OVER (PARTITION BY user_id, tenant ORDER BY time) as running_chat_count\n", "        FROM us_prod_request_insight_analytics_dataset.customers_request_metadata\n", "        WHERE request_type = 'CHAT'\n", "        AND DATE(time, \"America/Los_Angeles\") >= DATE_SUB(CURRENT_DATE(), INTERVAL 90 DAY)\n", "        AND tenant IN (SELECT tenant FROM new_tenants)\n", "    )\n", "    GROUP BY tenant, user_id\n", "),\n", "user_stats AS (\n", "    SELECT \n", "        cs.tenant,\n", "        m.shard_namespace,\n", "        cs.user_id,\n", "        cs.total_completion_requests,\n", "        cs.accepted_completions,\n", "        cs.rejected_completions,\n", "        cs.first_completion_date,\n", "        cs.date_100_completions,\n", "        COALESCE(chat.chat_count, 0) as chat_count,\n", "        chat.first_chat_date,\n", "        chat.date_100_chats,\n", "        up.work_hours_usage,\n", "        up.total_usage,\n", "        up.distinct_sessions,\n", "        up.avg_time_between_interactions,\n", "        dus.days_active,\n", "        dus.avg_interactions_per_active_day,\n", "        dus.usage_variation_coefficient,\n", "        dus.active_days_percentage,\n", "        ei.avg_work_hours_intensity,\n", "        ei.avg_non_work_hours_intensity,\n", "        ei.work_hours_usage_ratio,\n", "        CASE \n", "            WHEN cs.first_completion_date IS NOT NULL THEN\n", "                cs.accepted_completions / NULLIF(DATE_DIFF(CURRENT_DATE(), cs.first_completion_date, DAY), 0)\n", "        END as daily_completion_rate,\n", "        CASE \n", "            WHEN chat.first_chat_date IS NOT NULL THEN\n", "                chat.chat_count / NULLIF(DATE_DIFF(CURRENT_DATE(), chat.first_chat_date, DAY), 0)\n", "        END as daily_chat_rate,\n", "        -- Calculate the earliest interaction date\n", "        LEAST(\n", "            COALESCE(cs.first_completion_date, DATE('9999-12-31')),\n", "            COALESCE(chat.first_chat_date, DATE('9999-12-31'))\n", "        ) as first_interaction_date\n", "    FROM user_completion_stats cs\n", "    JOIN (\n", "        SELECT DISTINCT user_id, tenant, shard_namespace\n", "        FROM us_prod_request_insight_analytics_dataset.customers_request_metadata\n", "        WHERE shard_namespace LIKE 'e_%'\n", "        AND REGEXP_CONTAINS(shard_namespace, r'e[1-9]|e1[0-1]')\n", "    ) m ON cs.user_id = m.user_id AND cs.tenant = m.tenant\n", "    LEFT JOIN chat_stats chat \n", "        ON cs.user_id = chat.user_id AND cs.tenant = chat.tenant\n", "    LEFT JOIN user_patterns up\n", "        ON cs.user_id = up.user_id AND cs.tenant = up.tenant\n", "    LEFT JOIN daily_usage_stats dus\n", "        ON cs.user_id = dus.user_id AND cs.tenant = dus.tenant\n", "    LEFT JOIN engagement_intensity ei\n", "        ON cs.user_id = ei.user_id AND cs.tenant = ei.tenant\n", "    WHERE m.shard_namespace LIKE 'e_%'\n", "    AND REGEXP_CONTAINS(m.shard_namespace, r'e[1-9]|e1[0-1]')\n", "    -- Updated filter to ensure first interaction of any type was within last 14 days\n", "    AND LEAST(\n", "        COALESCE(cs.first_completion_date, DATE('9999-12-31')),\n", "        COALESCE(chat.first_chat_date, DATE('9999-12-31'))\n", "    ) > DATE_SUB(CURRENT_DATE(), INTERVAL 14 DAY)\n", ")\n", "SELECT DISTINCT\n", "    tenant as tenant_name,\n", "    shard_namespace,\n", "    user_id,\n", "    first_interaction_date,\n", "    accepted_completions,\n", "    chat_count,\n", "    ROUND(daily_completion_rate, 2) as completions_per_day,\n", "    ROUND(daily_chat_rate, 2) as chats_per_day,\n", "    -- Project days to reach 100\n", "    CASE \n", "        WHEN daily_completion_rate > 0 THEN\n", "            ROUND((100 - accepted_completions) / daily_completion_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_completions,\n", "    CASE \n", "        WHEN daily_chat_rate > 0 THEN\n", "            ROUND((100 - chat_count) / daily_chat_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_chats,\n", "    -- Usage pattern metrics\n", "    ROUND(SAFE_DIVIDE(work_hours_usage, NULLIF(total_usage, 0)) * 100, 2) as work_hours_percentage,\n", "    distinct_sessions as total_sessions,\n", "    ROUND(SAFE_DIVIDE((accepted_completions + chat_count), NULLIF(distinct_sessions, 0)), 1) as interactions_per_session,\n", "    ROUND(avg_time_between_interactions, 1) as avg_minutes_between_interactions,\n", "    -- Usage consistency metrics\n", "    days_active,\n", "    ROUND(SAFE_DIVIDE((accepted_completions + chat_count), NULLIF(days_active, 0)), 1) as avg_interactions_per_active_day,\n", "    usage_variation_coefficient as daily_usage_variation_percent,\n", "    active_days_percentage as percent_days_active,\n", "    -- Engagement intensity metrics\n", "    ROUND(COALESCE(avg_work_hours_intensity, 0), 1) as avg_interactions_work_hours,\n", "    ROUND(COALESCE(avg_non_work_hours_intensity, 0), 1) as avg_interactions_non_work_hours,\n", "    COALESCE(work_hours_usage_ratio, 0) as percent_work_hours_usage\n", "FROM user_stats\n", "WHERE \n", "    -- Filter for users projected to reach milestones quickly OR who have already hit them\n", "    (\n", "        -- Will reach or has reached 100 completions in 11 days or less\n", "        (\n", "            daily_completion_rate > 0 \n", "            AND accepted_completions > 0\n", "            AND (\n", "                accepted_completions >= 100 \n", "                OR (100 - accepted_completions) / daily_completion_rate <= 11\n", "            )\n", "        )\n", "        OR\n", "        -- Will reach or has reached 100 chats in 15 days or less\n", "        (\n", "            daily_chat_rate > 0 \n", "            AND chat_count > 0\n", "            AND (\n", "                chat_count >= 100\n", "                OR (100 - chat_count) / daily_chat_rate <= 15\n", "            )\n", "        )\n", "    )\n", "    -- Ensure we have some meaningful activity\n", "    AND (accepted_completions > 0 OR chat_count > 0)\n", "ORDER BY GREATEST(\n", "    COALESCE(completions_per_day, 0),\n", "    COALESCE(chats_per_day, 0)\n", ") DESC\n", "LIMIT 50;\n", "````\n", "</augment_code_snippet>\n", "\n", "You can update to remove the logic for checking tenant age\n", "\n", "----------------------------------------\n", "chat history (from most recent to least recent):\n", "----------------------------------------\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Below are some relevant files from my project.\n", "\n", "Here is an excerpt from the file `deploy/tenants/tenants_test.jsonnet`:\n", "\n", "<augment_code_snippet path=\"deploy/tenants/tenants_test.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "...\n", "  'viaduct-eu',\n", "  'webflow',\n", "  'zenchef',\n", "];\n", "\n", "local validateTenant(t) =\n", "  assert std.length(t.namespace) < 16 : 'namespace must be less than 16 characters';\n", "  assert startsWithLetter(t.namespace) : 'namespace must start with a letter';\n", "  assert startsWithLetter(t.name) : 'name must start with a letter';\n", "  if t.tenantFlags.multiTenantAllowed then\n", "    assert lib.contains([ns.namespace for ns in shardNamespaces], t.namespace) : 'namespace must be in shard namespaces';\n", "\n", "    if t.tenantFlags.supportTenant then\n", "      assert t.namespace == t.name : 'name and namespace must be equal';\n", "      true\n", "    else\n", "      true\n", "  else\n", "    assert t.tenantFlags.supportTenant == false : 'supportTenant must be false in legacy tenants';\n", "    assert t.name == t.namespace : 'name and namespace must be equal';\n", "    assert lib.contains(legacyNamespaces, t.namespace) : 'namespace must be in legacy namespaces';\n", "    true;\n", "\n", "local hasSupportTenant(ns) =\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/central/server/auth_servicer.py`:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/auth_servicer.py\" mode=\"EXCERPT\">\n", "````\n", "...\n", "        self.audit_logger = audit_logger\n", "\n", "    # TODO: use a service token instead of this check\n", "    def auth_check(  # pylint: disable=dangerous-default-value\n", "        self,\n", "        context: Servicer<PERSON><PERSON>xt,\n", "        tenant_id: str,\n", "        clients: list[str] = [],\n", "    ) -> tenant_watcher_pb2.Tenant:\n", "        auth_info = get_auth_info_from_grpc_context(context)\n", "\n", "        if tenant_id == \"\":\n", "            context.abort(grpc.StatusCode.INVALID_ARGUMENT, \"Invalid tenant ID\")\n", "            return tenant_watcher_pb2.Tenant()\n", "\n", "        # Do we also want to validate auth_info.shard_namespace?\n", "        if tenant_id != auth_info.tenant_id:\n", "            logging.error(\n", "                \"Tenant ID mismatch: %s != %s\", tenant_id, auth_info.tenant_id\n", "            )\n", "            context.abort(grpc.StatusCode.PERMISSION_DENIED, \"Operation not allowed\")\n", "            return tenant_watcher_pb2.Tenant()\n", "\n", "        tenant = self.tenant_map.get_tenant_by_id(tenant_id=tenant_id)\n", "        if tenant is None:\n", "...\n", "        tenant = self.auth_check(context, request.tenant_id, [\"auth-tenant-svc\"])\n", "\n", "        if request.email == \"\":\n", "            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)\n", "            context.set_details(\"Invalid request\")\n", "            return ValidateAccessResponse()\n", "\n", "        email_tenant = self.tenant_map.get_tenant_for_email_address(request.email)\n", "\n", "        if email_tenant is None:\n", "            logging.info(\"No tenant found for e-mail address\")\n", "            return ValidateAccessResponse(allowed=False)\n", "\n", "        if email_tenant.name != tenant.name:\n", "            logging.info(\"E-mail address does not belong to tenant %s\", tenant.name)\n", "            return ValidateAccessResponse(allowed=False)\n", "\n", "        if (\n", "            request.idp_user_id != \"\"\n", "            and len(email_tenant.allowed_identity_providers) > 0\n", "        ):\n", "            if not any(\n", "                request.idp_user_id.startswith(idp + \"|\")\n", "                for idp in email_tenant.allowed_identity_providers\n", "            ):\n", "                logging.info(\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/tenant_gc/main.go`:\n", "\n", "<augment_code_snippet path=\"services/tenant_gc/main.go\" mode=\"EXCERPT\">\n", "````\n", "...\n", "\t\treturn false\n", "\t}\n", "\tdeletedAt, err := time.Parse(time.RFC3339, tenant.Spec.DeletedAt)\n", "\tif err != nil {\n", "\t\tlog.Error().Err(err).Msg(\"Failed to parse deleted_at\")\n", "\t\treturn false\n", "\t}\n", "\tlog.Info().Msgf(\"Tenant %s has been deleted for %s\", tenant.Name, time.Since(deletedAt))\n", "\treturn time.Since(deletedAt) > ttl\n", "}\n", "\n", "func (gc *TenantGc) deleteTenant(tenant *crd.Tenant) error {\n", "\tlog.Info().Msgf(\"Deleting tenant %s\", tenant.Name)\n", "\n", "\t// Delete rows from bigtable-proxy-managed tables\n", "\tfor _, table := range gc.config.Tables {\n", "\t\t// add_tenant_id_to_key is the source of truth for this behavior\n", "\t\trowKeyPrefix := tenant.Spec.TenantId + \"#\"\n", "\t\tlog.Info().Msgf(\"Deleting rows with prefix %s from table %s\", rowKeyPrefix, table.TableName)\n", "\n", "\t\tif gc.config.DryRun {\n", "\t\t\tlog.Info().Msg(\"Dry run, skipping delete\")\n", "\t\t\tcontinue\n", "\t\t}\n", "\n", "\t\tif err := gc.btAdminClient.DropRowRange(gc.ctx, table.TableName, rowKeyPrefix); err != nil {\n", "\t\t\tlog.Error().Err(err).Msgf(\"Failed to delete rows from table %s\", table.TableName)\n", "\t\t\treturn err\n", "\t\t}\n", "\t}\n", "...\n", "\n", "func (gc *TenantGc) processTenants() error {\n", "\tlog.Info().Msg(\"Processing tenants\")\n", "\ttenants, err := gc.informer.List()\n", "\tif err != nil {\n", "\t\treturn err\n", "\t}\n", "\n", "\tfor _, tenant := range tenants {\n", "\t\tif !gc.checkTenantForDeletion(&tenant, gc.config.DeleteTTL.ToDuration()) {\n", "\t\t\tcontinue\n", "\t\t}\n", "\t\tif err := gc.deleteTenant(&tenant); err != nil {\n", "\t\t\treturn err\n", "\t\t}\n", "\t}\n", "\treturn nil\n", "}\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/request_insight/analytics/analytics_server.go`:\n", "\n", "<augment_code_snippet path=\"services/request_insight/analytics/analytics_server.go\" mode=\"EXCERPT\">\n", "````\n", "...\n", "\tif req.TenantId == \"\" || req.DateFilters == nil {\n", "\t\treturn nil, status.Error(\n", "\t\t\tcodes.InvalidArgument, \"tenant_id and date_filters are required\",\n", "\t\t)\n", "\t} else if err := checkDateFilters(req.DateFilters); err != nil {\n", "\t\treturn nil, err\n", "\t}\n", "\tfor _, requestType := range req.RequestTypes {\n", "\t\tif requestType == pb.GetActiveUsersRequest_UNKNOWN {\n", "\t\t\treturn nil, status.Error(codes.InvalidArgument, \"invalid request type\")\n", "\t\t}\n", "\t}\n", "\n", "\tdateFilters := req.DateFilters\n", "\tnumDays := daysBetween(dateFilters)\n", "\tdaysRequested.WithLabelValues(\"GetActiveUsers\").Observe(float64(numDays))\n", "\tlog.Info().Msgf(\"GetActiveUsers: requested %d days for tenant %s\", numDays, req.TenantId)\n", "\n", "\t// Check that the caller is authorized to access the requested tenant's data.\n", "\terr := s.checkAuth<PERSON><PERSON>ms(ctx, req.TenantId)\n", "\tif err != nil {\n", "\t\treturn nil, err\n", "\t}\n", "\n", "\t// Make sure we have a timezone. For simplicity, we've decided to use the same timezone for all\n", "\t// customers for now, so it's expected that callers won't provide this.\n", "\ttimezone := defaultTimeZone\n", "...\n", "// Returns an error that can be returned directly to the caller if provided gRPC context does not\n", "// contain auth claims that give access to the provided tenant id.\n", "func (s analyticsServer) checkAuthClaims(ctx context.Context, tenantId string) error {\n", "\tif !s.authEnabled {\n", "\t\treturn nil\n", "\t}\n", "\n", "\tauthClaims, ok := auth.GetAugmentClaims(ctx)\n", "\tif !ok {\n", "\t\tlog.Error().Msgf(\"Failed to get auth claims from context\")\n", "\t\treturn status.Error(codes.PermissionDenied, \"Access denied\")\n", "\t} else if authClaims.TenantID != tenantId {\n", "\t\tlog.<PERSON><PERSON><PERSON>().Msgf(\n", "\t\t\t\"Auth claims give permission for tenant %s, but request has tenant %s\",\n", "\t\t\tauthClaims.TenantID, tenantId,\n", "\t\t)\n", "\t\treturn status.Error(codes.PermissionDenied, \"Access denied\")\n", "\t}\n", "\n", "\treturn nil\n", "}\n", "\n", "// Convert a proto date to a civil date.\n", "func toCivilDate(pbDate *pb.Date) civil.Date {\n", "\treturn civil.Date{\n", "\t\tYear:  int(pbDate.Year),\n", "\t\tMonth: time.Month(pbDate.Month),\n", "\t\tDay:   int(pbDate.Day),\n", "\t}\n", "}\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/central/server/auth_servicer.go`:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/auth_servicer.go\" mode=\"EXCERPT\">\n", "````\n", "...\n", "\n", "func NewAuthGrpcServer(\n", "\tfeatureFlagHandle featureflags.FeatureFlagHandle,\n", "\tdaoFactory *DAOFactory,\n", "\ttenantMap *TenantMap,\n", "\tusingMtls bool,\n", "\tauditLogger *audit.AuditLogger,\n", ") *AuthGrpcServer {\n", "\treturn &AuthGrpcServer{\n", "\t\tdaoFactory:  daoFactory,\n", "\t\ttenantMap:   tenantMap,\n", "\t\tusingMtls:   usingMtls,\n", "\t\tauditLogger: auditLogger,\n", "\t}\n", "}\n", "\n", "// auth<PERSON>heck verifies the caller has permission to access the specified tenant\n", "func (s *AuthGrpcServer) authCheck(ctx context.Context, tenantID string, clients []string) (*tenant_watcher_pb2.Tenant, error) {\n", "\tif tenantID == \"\" {\n", "\t\treturn nil, status.Error(codes.InvalidArgument, \"Invalid tenant ID\")\n", "\t}\n", "\n", "\tauthInfo := grpcauth.GetAuthInfoFromGrpcContext(ctx)\n", "\tif authInfo == nil {\n", "\t\treturn nil, status.Error(codes.Unknown, \"Invalid context\")\n", "\t}\n", "\n", "\tif tenantID != authInfo.TenantID {\n", "\t\tlog.Error().Msgf(\"Tenant ID mismatch: %s != %s\", tenantID, authInfo.TenantID)\n", "\t\treturn nil, status.Error(codes.PermissionDenied, \"Operation not allowed\")\n", "...\n", "\t}\n", "\n", "\tif emailTenant.Name != tenant.Name {\n", "\t\tlog.Info().Msgf(\"E-mail address does not belong to tenant %s\", tenant.Name)\n", "\t\treturn &authpb.ValidateAccessResponse{\n", "\t\t\tAllowed: false,\n", "\t\t}, nil\n", "\t}\n", "\n", "\tif req.IdpUserId != \"\" && len(emailTenant.AllowedIdentityProviders) > 0 {\n", "\t\tallowed := false\n", "\t\tfor _, idp := range emailTenant.AllowedIdentityProviders {\n", "\t\t\tif strings.HasPrefix(req.IdpUserId, idp+\"|\") {\n", "\t\t\t\tallowed = true\n", "\t\t\t\tbreak\n", "\t\t\t}\n", "\t\t}\n", "\t\tif !allowed {\n", "\t\t\tlog.Info().Msgf(\"Identity provider %s not allowed in tenant %s\",\n", "\t\t\t\tstrings.Split(req.IdpUserId, \"|\")[0], tenant.Name)\n", "\t\t\treturn &authpb.ValidateAccessResponse{Allowed: false}, nil\n", "\t\t}\n", "\t}\n", "\n", "\tuserDAO := s.daoFactory.GetUserDAO()\n", "\tusers, err := userDAO.FindAll(ctx)\n", "\tif err != nil {\n", "\t\treturn nil, err\n", "\t}\n", "\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/customer/frontend/app/utils/api.ts`:\n", "\n", "<augment_code_snippet path=\"services/customer/frontend/app/utils/api.ts\" mode=\"EXCERPT\">\n", "````\n", "import { json } from \"@remix-run/node\";\n", "import { parse } from \"date-fns\";\n", "import { toZonedTime } from \"date-fns-tz\";\n", "import { logger } from \"@augment-internal/logging\";\n", "import { createContextValues, type CallOptions } from \"@connectrpc/connect\";\n", "import { hasShape, isNumber } from \"./guards\";\n", "import {\n", "  DEFAULT_TIMEZONE,\n", "  type YearMonthDay,\n", "  convertDateToYearMonthDay,\n", "} from \"./date\";\n", "import type { User } from \"../.server/auth\";\n", "import type { DateFilters } from \"~services/request_insight/analytics/request_insight_analytics_pb\";\n", "\n", "import { getTokenExchangeClient } from \"../.server/grpc/token-exchange\";\n", "import { cachingClient as RIAClient } from \"../.server/grpc/request-insights-analytics\";\n", "import {\n", "  SHARD_KEY,\n", "  TENANT_ID_KEY,\n", "} from \"../.server/grpc/tenant-shard-aware-transport\";\n", "\n", "const errorShape = hasShape({\n", "  status: isNumber,\n", "});\n", "function validateDate(date: string) {\n", "  try {\n", "    const yearMonthDay: YearMonthDay = JSON.parse(date);\n", "    parse(\n", "      `${yearMonthDay.year}-${yearMonthDay.month}-${yearMonthDay.day}`,\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Understood. I'll refer to the excerpts for context, and ignore them for general questions.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Is percentage of days active logic accurate? Im seeing a lot of percentages over 100\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the daily usage stats calculation, there's an issue with the active days percentage calculation. Let's analyze and fix it:\n", "\n", "<augment_code_snippet path=\"PotentialPowerUser.sql\" mode=\"EDIT\">\n", "````sql\n", "daily_usage_stats AS (\n", "    SELECT\n", "        user_id,\n", "        tenant,\n", "        -- Regular usage metrics\n", "        COUNT(DISTINCT usage_date) as days_active,\n", "        ROUND(COUNT(*) / COUNT(DISTINCT usage_date), 2) as avg_interactions_per_active_day,\n", "        -- Coefficient of variation (standard deviation / mean)\n", "        ROUND(STDDEV(daily_count) / NULLIF(AVG(daily_count), 0) * 100, 2) as usage_variation_coefficient,\n", "        -- Fix: Calculate percentage of days active correctly\n", "        ROUND(\n", "            COUNT(DISTINCT usage_date) / \n", "            NULLIF(\n", "                DATE_DIFF(\n", "                    CURRENT_DATE(),\n", "                    MIN(usage_date),\n", "                    DAY\n", "                ) + 1,  -- Add 1 to include both start and end date\n", "                0\n", "            ) * 100,\n", "            2\n", "        ) as active_days_percentage\n", "    FROM (\n", "        SELECT\n", "            user_id,\n", "            tenant,\n", "            DATE(interaction_time, \"America/Los_Angeles\") as usage_date,\n", "            COUNT(*) as daily_count\n", "        FROM all_interactions\n", "        GROUP BY user_id, tenant, DATE(interaction_time, \"America/Los_Angeles\")\n", "    )\n", "    GROUP BY user_id, tenant\n", ")\n", "````\n", "</augment_code_snippet>\n", "\n", "The issue was in the original calculation:\n", "```sql\n", "ROUND(COUNT(DISTINCT usage_date) / \n", "      NULLIF(GREATEST(DATE_DIFF(MAX(usage_date), MIN(usage_date), DAY), 1), 0) * 100, 2)\n", "```\n", "\n", "Problems with the original:\n", "1. Using `MAX(usage_date) - MIN(usage_date)` only counts the span between first and last usage, which could be shorter than the actual time period\n", "2. Using `GREATEST(..., 1)` could artificially reduce the denominator, leading to percentages over 100%\n", "\n", "The fix:\n", "1. Uses `CURRENT_DATE() - MIN(usage_date)` to get the full time span from first usage until today\n", "2. Adds 1 to include both the start and end date in the count\n", "3. Still uses `NULLIF` to prevent division by zero\n", "\n", "This will now correctly show the percentage of days the user has been active since their first interaction, and the percentage should never exceed 100%.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    You can also remove the logic for unique_languages\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the provided excerpts, I don't actually see any `unique_languages` logic in the current SQL file. The tech adoption metrics were already removed in our previous changes. Could you point me to where you're seeing the `unique_languages` logic that needs to be removed? Or if you'd like me to review the entire file for any remaining language-related logic, please let me know.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Just to confirm, this is only grabbing users that had their first usage less than 15 days ago correct?\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the WHERE clause in the `user_stats` CTE, yes, it's filtering for users who started less than 14 days ago (not 15):\n", "\n", "```sql\n", "WHERE m.shard_namespace LIKE 'e_%'\n", "AND REGEXP_CONTAINS(m.shard_namespace, r'e[1-9]|e1[0-1]')\n", "-- Add filter for users who started less than 2 weeks ago\n", "AND (\n", "    (cs.first_completion_date IS NOT NULL AND DATE_DIFF(CURRENT_DATE(), cs.first_completion_date, DAY) <= 14)\n", "    OR (chat.first_chat_date IS NOT NULL AND DATE_DIFF(CURRENT_DATE(), chat.first_chat_date, DAY) <= 14)\n", ")\n", "```\n", "\n", "The query looks at users who:\n", "1. Had their first completion within the last 14 days OR\n", "2. Had their first chat within the last 14 days\n", "\n", "So yes, this is specifically looking at new users who started using the product in the last two weeks (14 days), not 15 days.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    We need to update that. The earliest interaction (whether it's chat or completions) should be 14 days. No activity should have occurred before that\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    I'll modify the logic to ensure we're only looking at users whose very first interaction (whether chat or completion) was within the last 14 days. Here's the fix:\n", "\n", "<augment_code_snippet path=\"PotentialPowerUser.sql\" mode=\"EDIT\">\n", "````sql\n", "user_stats AS (\n", "    SELECT \n", "        cs.tenant,\n", "        m.shard_namespace,\n", "        cs.user_id,\n", "        cs.total_completion_requests,\n", "        cs.accepted_completions,\n", "        cs.rejected_completions,\n", "        cs.first_completion_date,\n", "        cs.date_100_completions,\n", "        COALESCE(chat.chat_count, 0) as chat_count,\n", "        chat.first_chat_date,\n", "        chat.date_100_chats,\n", "        up.work_hours_usage,\n", "        up.total_usage,\n", "        up.distinct_sessions,\n", "        up.avg_time_between_interactions,\n", "        -- Usage consistency metrics\n", "        dus.days_active,\n", "        dus.avg_interactions_per_active_day,\n", "        dus.usage_variation_coefficient,\n", "        dus.active_days_percentage,\n", "        -- Engagement intensity metrics\n", "        ei.avg_work_hours_intensity,\n", "        ei.avg_non_work_hours_intensity,\n", "        ei.work_hours_usage_ratio,\n", "        -- Calculate daily rates for projections\n", "        CASE \n", "            WHEN cs.first_completion_date IS NOT NULL THEN\n", "                cs.accepted_completions / NULLIF(DATE_DIFF(CURRENT_DATE(), cs.first_completion_date, DAY), 0)\n", "        END as daily_completion_rate,\n", "        CASE \n", "            WHEN chat.first_chat_date IS NOT NULL THEN\n", "                chat.chat_count / NULLIF(DATE_DIFF(CURRENT_DATE(), chat.first_chat_date, DAY), 0)\n", "        END as daily_chat_rate,\n", "        -- Add earliest interaction date\n", "        LEAST(\n", "            COALESCE(cs.first_completion_date, DATE('9999-12-31')),\n", "            COALESCE(chat.first_chat_date, DATE('9999-12-31'))\n", "        ) as first_interaction_date\n", "    FROM user_completion_stats cs\n", "    JOIN (\n", "        SELECT DISTINCT user_id, tenant, shard_namespace\n", "        FROM us_prod_request_insight_analytics_dataset.customers_request_metadata\n", "        WHERE shard_namespace LIKE 'e_%'\n", "        AND REGEXP_CONTAINS(shard_namespace, r'e[1-9]|e1[0-1]')\n", "    ) m ON cs.user_id = m.user_id AND cs.tenant = m.tenant\n", "    LEFT JOIN chat_stats chat \n", "        ON cs.user_id = chat.user_id AND cs.tenant = chat.tenant\n", "    LEFT JOIN user_patterns up\n", "        ON cs.user_id = up.user_id AND cs.tenant = up.tenant\n", "    LEFT JOIN daily_usage_stats dus\n", "        ON cs.user_id = dus.user_id AND cs.tenant = dus.tenant\n", "    LEFT JOIN engagement_intensity ei\n", "        ON cs.user_id = ei.user_id AND cs.tenant = ei.tenant\n", "    WHERE m.shard_namespace LIKE 'e_%'\n", "    AND REGEXP_CONTAINS(m.shard_namespace, r'e[1-9]|e1[0-1]')\n", ")\n", "SELECT DISTINCT\n", "    tenant as tenant_name,\n", "    shard_namespace,\n", "    user_id,\n", "    accepted_completions,\n", "    chat_count,\n", "    daily_completion_rate,\n", "    daily_chat_rate,\n", "    ROUND(daily_completion_rate, 2) as completions_per_day,\n", "    ROUND(daily_chat_rate, 2) as chats_per_day,\n", "    first_interaction_date,\n", "    -- Project days to reach 100\n", "    CASE \n", "        WHEN daily_completion_rate > 0 THEN\n", "            ROUND((100 - accepted_completions) / daily_completion_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_completions,\n", "    CASE \n", "        WHEN daily_chat_rate > 0 THEN\n", "            ROUND((100 - chat_count) / daily_chat_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_chats,\n", "    -- Usage pattern metrics\n", "    ROUND(SAFE_DIVIDE(work_hours_usage, NULLIF(total_usage, 0)) * 100, 2) as work_hours_percentage,\n", "    distinct_sessions as total_sessions,\n", "    ROUND(SAFE_DIVIDE((accepted_completions + chat_count), NULLIF(distinct_sessions, 0)), 1) as interactions_per_session,\n", "    ROUND(avg_time_between_interactions, 1) as avg_minutes_between_interactions,\n", "    -- Usage consistency metrics\n", "    days_active,\n", "    ROUND(SAFE_DIVIDE((accepted_completions + chat_count), NULLIF(days_active, 0)), 1) as avg_interactions_per_active_day,\n", "    usage_variation_coefficient as daily_usage_variation_percent,\n", "    active_days_percentage as percent_days_active,\n", "    -- Engagement intensity metrics\n", "    ROUND(COALESCE(avg_work_hours_intensity, 0), 1) as avg_interactions_work_hours,\n", "    ROUND(COALESCE(avg_non_work_hours_intensity, 0), 1) as avg_interactions_non_work_hours,\n", "    COALESCE(work_hours_usage_ratio, 0) as percent_work_hours_usage\n", "FROM user_stats\n", "WHERE \n", "    -- Ensure first interaction was within last 14 days\n", "    DATE_DIFF(CURRENT_DATE(), first_interaction_date, DAY) <= 14\n", "    AND (\n", "        -- Filter for users who either:\n", "        -- 1. Have already hit the milestones OR\n", "        -- 2. Are projected to reach milestones quickly\n", "        (\n", "            -- Already hit milestones\n", "            accepted_completions >= 100\n", "            OR chat_count >= 100\n", "            OR\n", "            -- Will reach 100 completions in 11 days or less\n", "            (\n", "                daily_completion_rate > 0 \n", "                AND accepted_completions > 0\n", "                AND (100 - accepted_completions) / daily_completion_rate <= 11\n", "            )\n", "            OR\n", "            -- Will reach 100 chats in 15 days or less\n", "            (\n", "                daily_chat_rate > 0 \n", "                AND chat_count > 0\n", "                AND (100 - chat_count) / daily_chat_rate <= 15\n", "            )\n", "        )\n", "        -- Ensure we have some meaningful activity\n", "        AND (accepted_completions > 0 OR chat_count > 0)\n", "    )\n", "ORDER BY GREATEST(\n", "    COALESCE(daily_completion_rate, 0),\n", "    COALESCE(daily_chat_rate, 0)\n", ") DESC\n", "LIMIT 50;\n", "````\n", "</augment_code_snippet>\n", "\n", "Key changes:\n", "1. Added `first_interaction_date` calculation in the `user_stats` CTE using `LEAST()` to get the earliest date between first completion and first chat\n", "2. Moved the 14-day filter to the final WHERE clause using `first_interaction_date`\n", "3. Added `first_interaction_date` to the SELECT statement for visibility\n", "4. Removed the separate date checks from the `user_stats` CTE\n", "\n", "This ensures we're only looking at users whose very first interaction (whether chat or completion) was within the last 14 days, and no earlier activity is considered.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Let's also add logic to display the date of first usage per user\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    I'll add the first interaction date to the final SELECT statement. It's already calculated in the `user_stats` CTE as `first_interaction_date`, we just need to include it in the output:\n", "\n", "<augment_code_snippet path=\"PotentialPowerUser.sql\" mode=\"EDIT\">\n", "````sql\n", "SELECT DISTINCT\n", "    tenant as tenant_name,\n", "    shard_namespace,\n", "    user_id,\n", "    first_interaction_date,  -- Added this line\n", "    accepted_completions,\n", "    chat_count,\n", "    daily_completion_rate,\n", "    daily_chat_rate,\n", "    ROUND(daily_completion_rate, 2) as completions_per_day,\n", "    ROUND(daily_chat_rate, 2) as chats_per_day,\n", "    -- Project days to reach 100\n", "    CASE \n", "        WHEN daily_completion_rate > 0 THEN\n", "            ROUND((100 - accepted_completions) / daily_completion_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_completions,\n", "    CASE \n", "        WHEN daily_chat_rate > 0 THEN\n", "            ROUND((100 - chat_count) / daily_chat_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_chats,\n", "    -- Rest of the metrics remain the same...\n", "````\n", "</augment_code_snippet>\n", "\n", "I've added `first_interaction_date` near the top of the SELECT statement, right after the identifying information (tenant, shard, user_id) and before the usage metrics. This will show the date when the user first interacted with the system (whether via chat or completion, whichever came first).\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    We are also duplicating displays for daily completions and daily chats. We only need one column for each\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Yes, we're showing both the raw and rounded versions. Let's clean that up and keep only the rounded versions:\n", "\n", "<augment_code_snippet path=\"PotentialPowerUser.sql\" mode=\"EDIT\">\n", "````sql\n", "SELECT DISTINCT\n", "    tenant as tenant_name,\n", "    shard_namespace,\n", "    user_id,\n", "    first_interaction_date,\n", "    accepted_completions,\n", "    chat_count,\n", "    ROUND(daily_completion_rate, 2) as completions_per_day,  -- Keep only rounded version\n", "    ROUND(daily_chat_rate, 2) as chats_per_day,  -- Keep only rounded version\n", "    -- Project days to reach 100\n", "    CASE \n", "        WHEN daily_completion_rate > 0 THEN\n", "            ROUND((100 - accepted_completions) / daily_completion_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_completions,\n", "    CASE \n", "        WHEN daily_chat_rate > 0 THEN\n", "            ROUND((100 - chat_count) / daily_chat_rate)\n", "        ELSE NULL\n", "    END as projected_days_to_100_chats,\n", "````\n", "</augment_code_snippet>\n", "\n", "Removed:\n", "- `daily_completion_rate` (raw)\n", "- `daily_chat_rate` (raw)\n", "\n", "Kept:\n", "- `completions_per_day` (rounded to 2 decimals)\n", "- `chats_per_day` (rounded to 2 decimals)\n", "\n", "This makes the output cleaner and removes the redundant columns.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    ORDER BY clause expression references column daily_completion_rate which is not visible after SELECT DISTINCT at [294:14]\n", "\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    The error occurs because we're trying to ORDER BY using the raw `daily_completion_rate` which isn't in our final SELECT list (we only have the rounded version as `completions_per_day`). Let's fix this by using the rounded columns in the ORDER BY:\n", "\n", "<augment_code_snippet path=\"PotentialPowerUser.sql\" mode=\"EDIT\">\n", "````sql\n", "ORDER BY GREATEST(\n", "    COALESCE(completions_per_day, 0),\n", "    COALESCE(chats_per_day, 0)\n", ") DESC\n", "````\n", "</augment_code_snippet>\n", "\n", "This change makes the ORDER BY clause consistent with our SELECT list, using the rounded values instead of the raw rates. The sorting will still work the same way, just using the rounded numbers that are actually visible in the output.\n", "\n", "\n"]}], "source": ["from experimental.zhuoran.replay_utils import (\n", "    render_prompt,\n", ")\n", "\n", "print(render_prompt(prompt_output))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Setup commands:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "gcloud auth login\n", "gcloud auth application-default login\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).\n", "- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: \"My search failed to locate the mentioned information.\" Avoid mentioning access limitations or mentioning \"provided excerpts\". Then, encourage the user to share more details or, alternatively, attach the relevant files using the \"@\" syntax in the chat (e.g., \"@path/to/file.py\").\n", "- Do not apologize.\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "````python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "````\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "````yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "````\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "````ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "````\n", "</augment_code_snippet>\n", "\n", "4. General code blocks: Always use `path=\"\"` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"\" mode=\"EXCERPT\">\n", "````bash\n", "cd my_project\n", "python main.py\n", "````\n", "</augment_code_snippet>\n", "\n", "5. Markdown excerpts: treat them the same way as other code blocks. Note that all Augment codeblocks are wrapped in 4 backticks specifically to allow escaping of codeblocks within codeblocks. Example:\n", "\n", "<augment_code_snippet path=\"research/README.md\" mode=\"EXCERPT\">\n", "````markdown\n", "# Research subdirectory\n", "\n", "This directory contains research-related code and documentation. To install dependencies, run\n", "```bash\n", "pip install -r requirements.txt\n", "```\n", "\n", "````\n", "</augment_code_snippet>\n", "\n", "6. MOST IMPORTANT: Please remember to ALWAYS close the code snippet with the closing tag: </augment_code_snippet>. It is easy. Whenever you see\n", "\n", "`````markdown\n", "````\n", "`````\n", "\n", "just put </augment_code_snippet> on the next line and make it\n", "\n", "`````markdown\n", "```\n", "</augment_code_snippet>\n", "`````\n", "\n", "THIS IS YOUR SINGLE MOST IMPORTANT JOB. ALWAYS CLOSE THE CODE SNIPPET WITH THE CLOSING TAG: </augment_code_snippet>. ALWAYS!\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2, 3, 4, 8, 14, 15, 20, 23, 26, 27, 34, 35, 36, 39, 40, 46, 53, 54, 58, 69, 70, 73, 76, 77, 84, 89, 93, 94\n", "with open(\"/mnt/efs/augment/user/zhuoran/prompt/xml/data/002.md\", \"r\") as f:\n", "    prompt_string = f.read()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    decode_prompt,\n", ")\n", "\n", "prompt_output = decode_prompt(prompt_string)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "prompt_output = dataclasses.replace(prompt_output, system_prompt=SYSTEM_PROMPT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    fix_prompt_output,\n", ")\n", "\n", "prompt_output = fix_prompt_output(prompt_output, backtick_count=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    render_prompt,\n", ")\n", "\n", "print(render_prompt(prompt_output))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    run_claude,\n", "    count_tags,\n", ")\n", "\n", "response, raw_response = run_claude(prompt_output)\n", "print(count_tags(response))\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for example_index in [\n", "    2,\n", "    3,\n", "    4,\n", "    8,\n", "    14,\n", "    15,\n", "    20,\n", "    23,\n", "    26,\n", "    27,\n", "    34,\n", "    35,\n", "    36,\n", "    39,\n", "    40,\n", "    46,\n", "    53,\n", "    54,\n", "    58,\n", "    69,\n", "    70,\n", "    73,\n", "    76,\n", "    77,\n", "    84,\n", "    89,\n", "    93,\n", "    94,\n", "]:\n", "    with open(\n", "        f\"/mnt/efs/augment/user/zhuoran/prompt/data/{example_index:03d}.md\", \"r\"\n", "    ) as f:\n", "        prompt_string = f.read()\n", "    prompt_output = decode_prompt(prompt_string)\n", "    prompt_output = fix_prompt_output(prompt_output, backtick_count=3)\n", "    new_prompt_output = dataclasses.replace(prompt_output, system_prompt=SYSTEM_PROMPT)\n", "    new_prompt_output = fix_prompt_output(new_prompt_output, backtick_count=3)\n", "    response, _ = run_claude(prompt_output, retry_limit=10)\n", "    new_response, _ = run_claude(new_prompt_output)\n", "    print(f\"{example_index:03d}: {count_tags(response)} -> {count_tags(new_response)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for example_index in [\n", "    2,\n", "    3,\n", "    4,\n", "    8,\n", "    14,\n", "    15,\n", "    20,\n", "    23,\n", "    26,\n", "    27,\n", "    34,\n", "    35,\n", "    36,\n", "    39,\n", "    40,\n", "    46,\n", "    53,\n", "    54,\n", "    58,\n", "    69,\n", "    70,\n", "    73,\n", "    76,\n", "    77,\n", "    84,\n", "    89,\n", "    93,\n", "    94,\n", "]:\n", "    with open(\n", "        f\"/mnt/efs/augment/user/zhuoran/prompt/data/{example_index:03d}.md\", \"r\"\n", "    ) as f:\n", "        prompt_string = f.read()\n", "    prompt_output = decode_prompt(prompt_string)\n", "    prompt_output = fix_prompt_output(prompt_output, backtick_count=4)\n", "    response, _ = run_claude(prompt_output, retry_limit=10)\n", "    print(f\"{example_index:03d}: {count_tags(response)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import (\n", "    decode_prompt,\n", "    fix_prompt_output,\n", "    run_claude,\n", "    count_tags,\n", ")\n", "\n", "with open(\"/home/<USER>/t.md\", \"r\") as f:\n", "    prompt_string = f.read()\n", "prompt_output = decode_prompt(prompt_string)\n", "prompt_output = fix_prompt_output(prompt_output, backtick_count=4)\n", "response, _ = run_claude(prompt_output, retry_limit=10)\n", "print(count_tags(response))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
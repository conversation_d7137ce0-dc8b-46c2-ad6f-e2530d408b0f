from collections import defaultdict

from experimental.zhuoran.prompt.xml.xml_replay_infra import generate_tests
from experimental.zhuoran.prompt.xml.request_ids import (
    MORE_REQUEST_IDS,
    REQUEST_IDS,
)

all_results = defaultdict(lambda: defaultdict(defaultdict))
retrieval_section_version = 1
prompt_formatter_name = "binks-claude-v8"
test_type = "history"
generate_tests(
    MORE_REQUEST_IDS,
    # REQUEST_IDS,
    "/mnt/efs/augment/user/zhuoran/prompt/data1",
    # "/mnt/efs/augment/user/zhuoran/prompt/data_small",
    prompt_formatter_name,
    retrieval_section_version,
    base_model_version=2,
    backtick_count=3,
    start_example_index=0,
    start_data_index=0,
    sleep_time=10,
)

{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["UNCLOSED = \"\"\"For paginated queries, you'll want to handle the cache in a way that properly merges new pages with existing data. Here's how to properly implement a cached paginated query for getUserAttributeUserAssignments:\n", "\n", "<augment_code_snippet path=\"packages/slate/src/api/attributes.ts\" mode=\"EDIT\">\n", "```typescript\n", "export function useUserAttributeUserAssignments(\n", "  userAttributeId: string,\n", "  pageSize: number = 25\n", ") {\n", "  const { data, loading, error, onLoadMore, networkStatus } = useFetchMore({\n", "    query: GetUserAttributeUserAssignmentsDocument,\n", "    variables: {\n", "      userAttributeId,\n", "      limit: pageSize\n", "    },\n", "    resultSelector: d =>\n", "      d?.organization.userAttribute?.userAssignments ?? {\n", "        entries: [],\n", "        nextPage: null,\n", "        hasMore: false,\n", "      },\n", "    options: {\n", "      // Use cache-and-network to show cached data immediately while fetching update\n", "      fetchPolicy: 'cache-and-network',\n", "      // Important for pagination to work properly\n", "      notifyOnNetworkStatusChange: true\n", "    }\n", "  });\n", "\n", "  const loadNextPage = React.useCallback(() => {\n", "    if (data?.hasMore) {\n", "      return onLoadMore({\n", "        variables: {\n", "          userAttributeId,\n", "          pageToken: data.nextPage,\n", "          limit: pageSize\n", "        },\n", "        // Merge function to combine existing and new data\n", "        updateQuery: (prev, { fetchMoreResult }) => {\n", "          if (!fetchMoreResult) return prev;\n", "          \n", "          return {\n", "            organization: {\n", "              ...prev.organization,\n", "              userAttribute: {\n", "                ...prev.organization.userAttribute,\n", "                userAssignments: {\n", "                  entries: [\n", "                    ...prev.organization.userAttribute.userAssignments.entries,\n", "                    ...fetchMoreResult.organization.userAttribute.userAssignments.entries\n", "                  ],\n", "                  nextPage: fetchMoreResult.organization.userAttribute.userAssignments.nextPage,\n", "                  hasMore: fetchMoreResult.organization.userAttribute.userAssignments.hasMore\n", "                }\n", "              }\n", "            }\n", "          };\n", "        }\n", "      });\n", "    }\n", "    return Promise.resolve();\n", "  }, [data, onLoadMore, userAttributeId, pageSize]);\n", "\n", "  return {\n", "    userAssignments: data?.organization.userAttribute?.userAssignments,\n", "    loading,\n", "    error,\n", "    loadNextPage,\n", "    networkStatus,\n", "    hasMore: data?.organization.userAttribute?.userAssignments.hasMore ?? false\n", "  };\n", "}\n", "```\n", "\n", "Key points in this implementation:\n", "\n", "1. Uses `useFetchMore` hook which is already set up in your project for pagination\n", "2. Sets `fetchPolicy: 'cache-and-network'` to show cached data while fetching updates\n", "3. Sets `notifyOnNetworkStatusChange: true` to properly handle loading states during pagination\n", "4. Provides a `loadNextPage` function that:\n", "   - Checks if there's more data to load\n", "   - Uses the `nextPage` token for pagination\n", "   - <PERSON><PERSON><PERSON> merges new data with existing cached data\n", "5. Returns both the paginated data and pagination controls\n", "\n", "To use this hook:\n", "\n", "```typescript\n", "const {\n", "  userAssignments,\n", "  loading,\n", "  error,\n", "  loadNextPage,\n", "  hasMore\n", "} = useUserAttributeUserAssignments(userAttributeId);\n", "\n", "// In your component:\n", "const handleLoadMore = () => {\n", "  if (hasMore && !loading) {\n", "    loadNextPage();\n", "  }\n", "};\n", "```\n", "\n", "This implementation ensures that:\n", "- The cache is properly maintained\n", "- New pages are merged with existing data\n", "- Loading states are properly handled\n", "- The UI can show both cached data and fresh data appropriately\n", "\n", "The `useFetchMore` hook you're already using handles a lot of the complexity, but this implementation adds proper typing and cache handling specific to the user attributes use case.\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import AnthropicDirectClient\n", "\n", "# model_name = \"claude-3-5-sonnet-v2@20241022\"\n", "model_name = \"claude-3-5-sonnet@20240620\"\n", "\n", "\n", "# client = AnthropicVertexClient(model_name=\"claude-3-5-sonnet-v2@20241022\")\n", "client = AnthropicDirectClient(model_name=model_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import run_claude\n", "\n", "response, _ = run_claude(\n", "    UNCLOSED,\n", "    \"\"\"Reproduce whatever the user writes in the message. Do not alter or add a single character, except for fixing unclosed code blocks:\n", "- Each code block must start with <augment_code_snippet ...> + ```` and end with </augment_code_snippet> + ````.\n", "- Fix any unclosed code blocks in the provided message.\n", "- Any code block must be surrounded by ````s, not ```s, but the code block main internally contain ```s. Fix the number of `s as needed.\n", "- For any `s, you must carefully examine if it is internally in a code block, or the closing marker of a code block. If it is a closing marker, you must add the closing </augment_code_snippet> tag.\n", "- Do not guess the metadata of the code block if it is not provided. It is okay to  have <augment_code_snippet> without any metadata.\n", "\"\"\",\n", "    base_model_version=\"haiku3.5\",\n", "    client_type=\"anthropic\",\n", ")\n", "print(response)\n", "\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(UNCLOSED)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(response)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
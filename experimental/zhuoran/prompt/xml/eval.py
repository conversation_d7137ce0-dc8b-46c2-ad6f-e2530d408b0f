import glob
import json

from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name
from experimental.zhuoran.prompt.xml.utils import (
    POSTPROCESSING_PROMPT,
    get_example_result,
)
from research.tools.chat_replay.replay_utils import (
    TOKEN_APPORTIONMENT,
    chat_prompt_input_from_dict,
    fix_input,
    run_claude,
)

data_paths = glob.glob("/mnt/efs/augment/user/zhuoran/prompt/xml/data/*.json")
chat_prompt_inputs = []
for path in data_paths:
    with open(path) as file:
        chat_prompt_dict = json.load(file)
        chat_prompt_dict.pop("request_id", None)
    chat_prompt_input = chat_prompt_input_from_dict(chat_prompt_dict)
    chat_prompt_inputs.append(chat_prompt_input)

eval_setups = {
    # model_name: base_model_version, prompt_formatter_name, retrieval_section_version, backtick_count
    "old_claude": ("sonnet3.5", "binks-claude-v4", 2, 3),
    "v8": ("sonnet3.5-v2", "binks-claude-v8", 2, 3),
    "v11v_bt4": ("sonnet3.5-v2", "binks-claude-v11-var", 3, 4),
    "v11v_bt4_v2": ("sonnet3.5-v2", "binks-claude-v11-var", 2, 4),
}
prompt_formatters = {}
for model_name, (
    base_model_version,
    prompt_formatter_name,
    retrieval_section_version,
    backtick_count,
) in eval_setups.items():
    prompt_formatter = get_structured_chat_prompt_formatter_by_name(
        prompt_formatter_name,  # type: ignore
        TOKEN_APPORTIONMENT,
        retrieval_section_version,
        backtick_count,
    )
    prompt_formatters[model_name] = prompt_formatter
postprocessing_models = {
    "h3": "haiku3",
    "h35": "haiku3.5",
    "s35": "sonnet3.5",
    "s352": "sonnet3.5-v2",
}


print("Finished eval setup")
results = []
for example_index, chat_prompt_input in enumerate(chat_prompt_inputs):
    print("=" * 40)
    print(chat_prompt_input.message.splitlines()[-1])
    example_results = {}
    for model_name, (
        base_model_version,
        prompt_formatter_name,
        retrieval_section_version,
        backtick_count,
    ) in eval_setups.items():
        prompt_formatter = prompt_formatters[model_name]
        chat_prompt_input = fix_input(chat_prompt_input, backtick_count)
        prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
        response, _ = run_claude(
            message_or_prompt=prompt_output,
            base_model_version=base_model_version,
            retry_limit=50,
        )
        example_results[model_name] = get_example_result(
            response, model_name, chat_prompt_input, prompt_output
        )
        for (
            postprocessing_model_name,
            postprocessing_model,
        ) in postprocessing_models.items():
            postprocessed_model_name = f"{model_name}_{postprocessing_model_name}"
            fixed_response, _ = run_claude(
                message_or_prompt=response + "\n" + POSTPROCESSING_PROMPT,
                base_model_version=postprocessing_model,
                client_type="anthropic",
                retry_limit=50,
            )
            example_results[postprocessed_model_name] = get_example_result(
                fixed_response,
                postprocessed_model_name,
                chat_prompt_input,
                prompt_output,
                example_results[model_name],
            )
    results.append(example_results)
    print(flush=True)
    print(f"Accuracies up to {example_index}/{len(chat_prompt_inputs)}")
    for model_name in results[0].keys():
        print(
            f"{model_name}:"
            f" {sum([r[model_name]['accuracy'] for r in results]) / len(results):.1%}"
        )
    print(flush=True)
    print(f"No extra change up ratios up to {example_index}/{len(chat_prompt_inputs)}")
    for model_name in results[0].keys():
        print(
            f"{model_name}:"
            f" {sum([r[model_name]['no_extra_change'] for r in results]) / len(results):.1%}"
        )
    print(flush=True)
    print(f"Average extra diff lines up to {example_index}/{len(chat_prompt_inputs)}")
    for model_name in results[0].keys():
        print(
            f"{model_name}:"
            f" {sum([r[model_name]['extra_diff_line_count'] for r in results]) / len(results):.1f}"
        )
    with open("/mnt/efs/augment/user/zhuoran/prompt/eval9.json", "w") as f:
        json.dump(results, f, indent=4)
    print(flush=True)

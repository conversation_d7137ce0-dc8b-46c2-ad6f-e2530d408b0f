import json

from experimental.zhuoran.prompt.xml.xml_replay_infra import get_input_and_documents
from experimental.zhuoran.chat_replay.replay_utils import (
    chat_prompt_dict_from_input,
    count_tags,
    fix_input,
    generate_chat_response,
)
from experimental.zhuoran.prompt.xml.request_ids import (
    FIRST_FAIL_REQUEST_IDS,
)

DOGFOOD_SHARD_URL = "https://staging-shard-0.api.augmentcode.com/"
DOGFOOD_SHARD_RI_LINK_TEMPLATE = "https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}"
DEV_URL = "https://dev-zhuoran.us-central.api.augmentcode.com/"
DEV_RI_LINK_TEMPLATE = "https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{request_id}"

# URL, RI_LINK_TEMPLATE = DOGFOOD_SHARD_URL, DOGFOOD_SHARD_RI_LINK_TEMPLATE
URL, RI_LINK_TEMPLATE = DEV_URL, DEV_RI_LINK_TEMPLATE


deployment_setups = {
    # label: model_name, backtick_count
    "v9.1": ("claude-sonnet-3-5-16k-v9-1-chat", 3),
    "v11": ("claude-sonnet-3-5-16k-v11-chat", 4),
    "v11.1": ("claude-sonnet-3-5-16k-v11-1-chat", 4),
}

results = []
accuracies = {model_name: [] for model_name in deployment_setups}

for example_index, request_id in enumerate(FIRST_FAIL_REQUEST_IDS):
    print("=" * 40)
    chat_prompt_input, documents = get_input_and_documents(request_id)
    print(chat_prompt_input.message.splitlines()[-1])
    print(f"Example {example_index}/{len(FIRST_FAIL_REQUEST_IDS)}")

    example_results = {}
    for label, (model_name, backtick_count) in deployment_setups.items():
        fixed_chat_prompt_input = fix_input(chat_prompt_input, backtick_count)
        response, new_request_id = generate_chat_response(
            URL,
            model_name,
            documents,
            fixed_chat_prompt_input,
            retry_limit=100,
            raw=True,
        )
        opening_count, closing_count = count_tags(response)
        accuracies[label].append(1 if opening_count == closing_count else 0)
        example_results[label] = {
            "opening_count": opening_count,
            "closing_count": closing_count,
            "response": response,
            "prompt_input": chat_prompt_dict_from_input(chat_prompt_input),
            "new_request_id": new_request_id,
        }
        print(
            f"{label}: {opening_count}, {closing_count}, {RI_LINK_TEMPLATE.format(request_id=new_request_id)}",
            flush=True,
        )
    print(flush=True)
    print(f"Accuracies up to {example_index}/{len(FIRST_FAIL_REQUEST_IDS)}")
    for label, accuracy in accuracies.items():
        print(f"{label}: {sum(accuracy) / len(accuracy):.1%}")
    with open("/mnt/efs/augment/user/zhuoran/prompt/eval_results.json", "w") as f:
        json.dump(results, f, indent=4)
    print(flush=True)

import difflib

from research.tools.chat_replay.replay_utils import (
    chat_prompt_dict_from_input,
    count_tags,
    render_prompt,
)

POSTPROCESSING_PROMPT = """Reproduce whatever I wrote above in the message back to me. Do not alter or add a single character, except for fixing unclosed code blocks:
- Each code block must start with <augment_code_snippet ...>\n``` and end with ```\n</augment_code_snippet>.
- Fix any unclosed code blocks in the provided message. Each <augment_code_snippet> must have a closing </augment_code_snippet>.
- For any `s, you must carefully examine if it is internally in a code block, or the closing marker of a code block. If it is a closing marker, you must add the closing </augment_code_snippet> tag.
- Do not guess the metadata of the code block if it is not provided. It is okay to  have <augment_code_snippet> without any metadata.
- DO NOT add, remove, or alter a single characters other than the ones mentioned above.
"""


def count_diff_lines(a: str, b: str) -> int:
    """Count the number of changed lines between two strings.

    Args:
        a: The first string to compare
        b: The second string to compare

    Returns:
        The number of changed lines (additions and deletions)
    """
    diff = list(
        difflib.unified_diff(
            a.splitlines(keepends=True),
            b.splitlines(keepends=True),
            fromfile="before",
            tofile="after",
        )
    )

    return sum(
        1
        for line in diff
        if (line.startswith("+") or line.startswith("-"))
        and not line.startswith("--- ")
        and not line.startswith("+++ ")
        and not line.startswith("@@")
    )


def get_example_result(
    response, model_name, chat_prompt_input, prompt_output, reference_results=None
):
    opening_count, closing_count = count_tags(response)
    accuracy = 1 if opening_count == closing_count else 0
    print(f"{model_name}: {opening_count}, {closing_count}", flush=True)
    extra_diff_line_count = 0
    no_extra_change = 1
    if reference_results:
        diff_line_count = count_diff_lines(reference_results["response"], response)
        diff_tag_count = abs(reference_results["opening_count"] - opening_count) + abs(
            reference_results["closing_count"] - closing_count
        )
        extra_diff_line_count = diff_line_count - diff_tag_count
        no_extra_change = 1 if extra_diff_line_count == 0 else 0
    return {
        "opening_count": opening_count,
        "closing_count": closing_count,
        "accuracy": accuracy,
        "extra_diff_line_count": extra_diff_line_count,
        "no_extra_change": no_extra_change,
        "response": response,
        "prompt_input": chat_prompt_dict_from_input(chat_prompt_input),
        "prompt_output": render_prompt(prompt_output),
    }

{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def parse_eval_output(stdout: str) -> dict[str, dict[str, int | float]]:\n", "    results = []\n", "    example_results = {}\n", "    skip_next = False\n", "\n", "    for line in stdout.split(\"\\n\"):\n", "        if skip_next:\n", "            skip_next = False\n", "            continue\n", "        elif \"initialized for model claude-3-5-sonnet\" in line:\n", "            continue\n", "        elif line.strip() == \"\":\n", "            continue\n", "        elif line.startswith(\"=\" * 40):\n", "            # print(example_results)\n", "            if example_results:\n", "                results.append(example_results)\n", "            example_results = {}\n", "            skip_next = True\n", "        elif line.startswith(\"Accuracies up to\"):\n", "            continue\n", "        elif \":\" in line:\n", "            # print(example_results)\n", "            parts = line.split(\":\")\n", "            model = parts[0].strip()\n", "            if \"Error running message\" in line:\n", "                continue\n", "            if len(parts) == 2 and \",\" in parts[1]:\n", "                counts = parts[1].strip().split(\",\")\n", "                opening_count = int(counts[0].strip())\n", "                closing_count = int(counts[1].strip())\n", "                example_results[model] = {}\n", "                example_results[model][\"opening_count\"] = opening_count\n", "                example_results[model][\"closing_count\"] = closing_count\n", "            elif len(parts) == 2 and \"%\" in parts[1]:\n", "                accuracy = float(parts[1].strip().rstrip(\"%\")) / 100\n", "                if model in example_results:\n", "                    example_results[model][\"accuracy\"] = accuracy\n", "\n", "    # Add the last example if it exists\n", "    if example_results:\n", "        results.append(example_results)\n", "\n", "    return results\n", "\n", "\n", "# Example usage:\n", "with open(\"/home/<USER>/zhuoran/prompt/xml/eval_results/t.txt\", \"r\") as f:\n", "    stdout_content = f.read()\n", "\n", "\n", "parsed_results = parse_eval_output(stdout_content)\n", "# print(parsed_results)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["99"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(parsed_results)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3, 7, 22, 23, 27, 34, 39, 40, 46, 53, 54, 77, 82, 84, 89, 93, 94, "]}], "source": ["for index, result in enumerate(parsed_results):\n", "    v10_correct = result[\"v11\"][\"opening_count\"] == result[\"v11\"][\"closing_count\"]\n", "    bt4_correct = result[\"bt4\"][\"opening_count\"] == result[\"bt4\"][\"closing_count\"]\n", "    if (not v10_correct) and bt4_correct:\n", "        print(index, end=\", \")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2, 10, 11, 18, 19, 62, "]}], "source": ["for index, result in enumerate(parsed_results):\n", "    v10_correct = result[\"v11\"][\"opening_count\"] == result[\"v11\"][\"closing_count\"]\n", "    bt4_correct = result[\"bt4\"][\"opening_count\"] == result[\"bt4\"][\"closing_count\"]\n", "    if v10_correct and (not bt4_correct):\n", "        print(index, end=\", \")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/efs/augment/user/zhuoran/prompt/xml/data/002.md\n"]}], "source": ["print(f\"/mnt/efs/augment/user/zhuoran/prompt/xml/data/{2:03d}.md\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["99"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "\n", "with open(\"/mnt/efs/augment/user/zhuoran/prompt/xml/eval_results.json\") as f:\n", "    results = json.load(f)\n", "len(results)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['opening_count', 'closing_count', 'response', 'prompt_input', 'prompt_output'])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["results[0][\"v8\"].keys()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["raw_reults = results"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["99"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["condensed_results = []\n", "prompt_outputs = set()\n", "for result in results:\n", "    bt4_v2_correct = (\n", "        result[\"bt4_v2\"][\"opening_count\"] == result[\"bt4_v2\"][\"closing_count\"]\n", "    )\n", "    v12_bt4_v2_correct = (\n", "        result[\"v12_bt4_v2\"][\"opening_count\"] == result[\"v12_bt4_v2\"][\"closing_count\"]\n", "    )\n", "    prompt_input = result[\"v8\"][\"prompt_input\"]\n", "    prompt_output = result[\"v8\"][\"prompt_output\"]\n", "    if prompt_output not in prompt_outputs:\n", "        condensed_results.append(result)\n", "        prompt_outputs.add(prompt_output)\n", "results = condensed_results\n", "len(results)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3, 12, 22, 26, 35, 40, 46, 54, 70, 86, 95, "]}], "source": ["for index, result in enumerate(results):\n", "    bt4_v2_correct = (\n", "        result[\"bt4_v2\"][\"opening_count\"] == result[\"bt4_v2\"][\"closing_count\"]\n", "    )\n", "    v12_bt4_v2_correct = (\n", "        result[\"v12_bt4_v2\"][\"opening_count\"] == result[\"v12_bt4_v2\"][\"closing_count\"]\n", "    )\n", "    if bt4_v2_correct and (not v12_bt4_v2_correct):\n", "        print(index, end=\", \")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------\n", "system_prompt:\n", "----------------------------------------\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).\n", "- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: \"My search failed to locate the mentioned information.\" Avoid mentioning access limitations or mentioning \"provided excerpts\". Then, encourage the user to share more details or, alternatively, attach the relevant files using the \"@\" syntax in the chat (e.g., \"@path/to/file.py\").\n", "- Do not apologize.\n", "- At the end of every answer, you should write your 2 best guesses of what user will ask next. Enclose them in:\n", "<guess_of_next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "</guess_of_next_user_question>\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "````python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "````\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "````yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "````\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "````ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "````\n", "</augment_code_snippet>\n", "\n", "----------------------------------------\n", "current message:\n", "----------------------------------------\n", "Would this be the monitoring rules?\n", "\n", "----------------------------------------\n", "chat history (from most recent to least recent):\n", "----------------------------------------\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Below are some relevant files from my project.\n", "\n", "Here is an excerpt from the file `tools/monitoring/Readme.md`:\n", "\n", "<augment_code_snippet path=\"tools/monitoring/Readme.md\" mode=\"EXCERPT\">\n", "````\n", "# Monitoring\n", "\n", "This directory contains the observability and monitoring systems. Namely:\n", "  - Google Managed Prometheus for metrics\n", "  - Google Cloud Logging for logs\n", "  - <PERSON><PERSON><PERSON> for traces, moving to Google Cloud Trace\n", "  - Grafana for dashboards\n", "  - Google Cloud Monitoring for alerting\n", "  - Pagerduty for routing alerts to people\n", "\n", "See [https://www.notion.so/Links-c251670205db4b788cc9a8a3f9a67610] for links to the various systems.\n", "\n", "## Prometheus\n", "\n", "[Prometheus](https://prometheus.io/) is a system to collect metrics from Kubernetes pods and services.\n", "This includes general metrics (e.g. CPU utilization) and application-specific metrics.\n", "\n", "If configured for a pod, system will (in regular intervals) fetch the metrics from an endpoint `/metrics`.\n", "\n", "The ClusterPodMonitoring rule defines which pods get configured for metrics collection by GKE. https://cloud.google.com/stackdriver/docs/managed-prometheus/setup-managed#gmp-pod-monitoring\n", "\n", "However, it the metrics will usually be configured via Grafana (see below).\n", "\n", "## <PERSON><PERSON>\n", "\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/deploy/Readme.md`:\n", "\n", "<augment_code_snippet path=\"tools/deploy/Readme.md\" mode=\"EXCERPT\">\n", "````\n", "# Eng OAuth Proxy\n", "\n", "The eng oauth proxy is used to protect sites under `eng.augmentcode.com` via Github OAuth.\n", "\n", "The app is available at [https://github.com/organizations/augmentcode/settings/applications/2097870].\n", "\n", "Use `tools/monitoring/grafana.jsonnet` as an example how to setup the ingress configuration.\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/tenant/server/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/auth/tenant/server/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  local spec = {\n", "    displayName: 'Auth Tenant Request Errors',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'error' },\n", "      query: |||\n", "        sum by (namespace,cluster)(increase(flask_http_request_total{status=~\"^50[0-9]$\", pod=~\"auth-tenant-.*\"}[30m])) > 1\n", "      |||,\n", "    },\n", "  };\n", "\n", "  local errorsAtLoadBalancerSpec = {\n", "    displayName: 'Auth Tenant 5xx measured at load balancer',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'warning' },\n", "      query: |||\n", "        sum by (namespace)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource=\"https_lb_rule\",target_proxy_name=~\".*auth-tenant.*\",response_code_class=\"500\"}[5m])) > 1\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/monitoring/managed_prometheus.jsonnet`:\n", "\n", "<augment_code_snippet path=\"tools/monitoring/managed_prometheus.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local cloudInfoLib = import 'deploy/common/cloud_info.jsonnet';\n", "local lib = import 'deploy/common/lib.jsonnet';\n", "local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';\n", "function(cloud)\n", "  local serviceAccount = gcpLib.createServiceAccount(app='gmp-frontend', cloud=cloud, env='PROD', namespace='monitoring', iam=true);\n", "  local frontend = if cloud != 'GCP_US_CENTRAL1_GSC_PROD' then [\n", "    {\n", "      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',\n", "      kind: 'IAMPartialPolicy',\n", "      metadata: {\n", "        name: 'gmp-frontend-monitoring-viewer',\n", "        namespace: 'monitoring',\n", "      },\n", "      spec: {\n", "        resourceRef: {\n", "          kind: 'Project',\n", "          external: cloudInfoLib[cloud].projectId,\n", "        },\n", "        bindings: [\n", "          {\n", "            role: 'roles/monitoring.viewer',\n", "            members: [\n", "              {\n", "                member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress,\n", "              },\n", "            ],\n", "          },\n", "        ],\n", "      },\n", "    },\n", "...\n", "      metadata: {\n", "        name: 'prom-kubecfg',\n", "      },\n", "      spec: {\n", "        selector: {\n", "          matchLabels: {\n", "            'app.kubernetes.io/managed-by': 'kubecfg',\n", "          },\n", "        },\n", "        endpoints: [\n", "          {\n", "            port: 9090,\n", "            interval: '60s',\n", "          },\n", "        ],\n", "      },\n", "    },\n", "    // Add a second rule to get metrics for pods that are not managed by\n", "    // kubecfg, which is generally rare\n", "    {\n", "      apiVersion: 'monitoring.googleapis.com/v1',\n", "      kind: 'ClusterPodMonitoring',\n", "      metadata: {\n", "        name: 'prom-manual',\n", "      },\n", "      spec: {\n", "        selector: {\n", "          matchLabels: {\n", "            'augmentcode.com/enable-prometheus-scraping': 'true',\n", "          },\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/central/server/BUILD`:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/BUILD\" mode=\"EXCERPT\">\n", "````\n", "...\n", "        \"//deploy/common:dynamic-feature-flags-lib\",\n", "        \"//deploy/common:grpc-lib\",\n", "        \"//deploy/common:lib\",\n", "        \"//deploy/common:node-lib\",\n", "        \"//deploy/common:telemetry-lib\",\n", "        \"//deploy/gcp:gcp-lib\",\n", "        \"//services/deploy:endpoints\",\n", "    ],\n", ")\n", "\n", "kubecfg(\n", "    name = \"kubecfg_monitoring\",\n", "    src = \"monitoring.jsonnet\",\n", "    cluster_wide = True,\n", "    visibility = [\n", "        \"//services/auth/central:__subpackages__\",\n", "    ],\n", "    deps = [\n", "        \"//deploy/gcp:monitoring-lib\",\n", "    ],\n", ")\n", "\n", "py_library(\n", "    name = \"server_library\",\n", "    srcs = [\n", "        \"app.py\",\n", "        \"auth_client.py\",\n", "        \"auth_dao.py\",\n", "        \"auth_servicer.py\",\n", "        \"bigtable_connector.py\",\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/request_insight/bigtable_exporter/deploy_lib.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/request_insight/bigtable_exporter/deploy_lib.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "...\n", "        apiVersion: 'apps/v1',\n", "        kind: 'Deployment',\n", "        name: app<PERSON><PERSON>,\n", "      },\n", "      minReplicaCount: minReplicas[env],\n", "      maxReplicaCount: maxReplicas[env],\n", "      triggers: [\n", "        {\n", "          type: 'prometheus',\n", "          metadata: {\n", "            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',\n", "            metricName: 'pubsub_googleapis_com:subscription_num_undelivered_messages',\n", "            threshold: '1000',\n", "            query: 'sum(avg_over_time(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource=\"pubsub_subscription\",subscription_id=\"%s\"}[1m]))' % config.subscription_name,\n", "          },\n", "        },\n", "      ],\n", "    },\n", "  };\n", "\n", "  lib.flatten([\n", "    configMap.objects,\n", "    serviceAccount.objects,\n", "    clientCert.objects,\n", "    centralClientCert.objects,\n", "    deployment,\n", "    scaledObject,\n", "    bigtable.objects,\n", "    gcpObjects,\n", "  ])\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/auth0/revoker/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/auth/auth0/revoker/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  local parseErrorsSpec = {\n", "    displayName: 'Auth0 Revoker parse errors',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'warning' },\n", "      query: |||\n", "        sum by (namespace)(increase(au_auth0_revoker_messages_parse_errors[60m])) > 3\n", "      |||,\n", "    },\n", "  };\n", "  local revokeUserErrorsSpec = {\n", "    displayName: 'Auth0 Revoker revoke user errors',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'warning' },\n", "      query: |||\n", "        sum by (namespace)(increase(au_auth0_revoker_revoke_user_errors[60m])) > 2\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "    monitoringLib.alertPolicy(cloud, parseErrorsSpec, 'auth0-revoker-parse-errors', 'Parse errors'),\n", "    monitoringLib.alertPolicy(cloud, revokeUserErrorsSpec, 'auth0-revoker-revoke-user-errors', 'Revoke user errors'),\n", "  ]\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/central/server/deploy.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/deploy.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "...\n", "        name: '//services/auth/central/server:image',\n", "        dst: 'auth-central',\n", "      },\n", "      args: [\n", "        '--config',\n", "        '/config/config.json',\n", "      ],\n", "      ports: [\n", "        {\n", "          containerPort: 5000,\n", "          name: 'http-svc',\n", "        },\n", "      ],\n", "      env: lib.flatten([\n", "        {\n", "          name: 'PROMETHEUS_MULTIPROC_DIR',\n", "          value: '/tmp/prometheus_multiproc_dir',\n", "        },\n", "        telemetryLib.telemetryEnv('auth-central', telemetryLib.collectorUri(env, namespace, cloud)),\n", "      ]),\n", "      volumeMounts: [\n", "        {\n", "          name: 'auth-central-config',\n", "          mountPath: '/config',\n", "          readOnly: true,\n", "        },\n", "        {\n", "          name: 'prometheus-multiproc-dir',\n", "          mountPath: '/tmp/prometheus_multiproc_dir',\n", "        },\n", "...\n", "    };\n", "\n", "  local auth_grpc_container =\n", "    {\n", "      name: 'auth-central-grpc',\n", "      target: {\n", "        name: '//services/auth/central/server/go:image',\n", "        dst: 'auth-central-grpc',\n", "      },\n", "      args: [\n", "        '--config',\n", "        '/config/config.json',\n", "      ],\n", "      ports: [\n", "        {\n", "          containerPort: 50051,\n", "          name: 'grpc-svc',\n", "        },\n", "      ],\n", "      env: lib.flatten([\n", "        {\n", "          name: 'PROMETHEUS_MULTIPROC_DIR',\n", "          value: '/tmp/prometheus_multiproc_dir',\n", "        },\n", "        telemetryLib.telemetryEnv('auth-central-grpc', telemetryLib.collectorUri(env, namespace, cloud)),\n", "      ]),\n", "      volumeMounts: [\n", "        {\n", "          name: 'auth-central-config',\n", "          mountPath: '/config',\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/monitoring/BUILD`:\n", "\n", "<augment_code_snippet path=\"tools/monitoring/BUILD\" mode=\"EXCERPT\">\n", "````\n", "...\n", "\n", "kubecfg(\n", "    name = \"monitoring_kubecfg\",\n", "    src = \"monitoring.jsonnet\",\n", "    cluster_wide = True,\n", "    deps = [\n", "        \"//deploy/common:cloud_info\",\n", "        \"//deploy/gcp:monitoring-lib\",\n", "    ],\n", ")\n", "\n", "kubecfg(\n", "    name = \"kubecfg_managed_prometheus\",\n", "    src = \"managed_prometheus.jsonnet\",\n", "    cluster_wide = True,\n", "    deps = [\n", "        \"//deploy/common:cloud_info\",\n", "        \"//deploy/common:lib\",\n", "        \"//deploy/gcp:gcp-lib\",\n", "    ],\n", ")\n", "\n", "kubecfg(\n", "    name = \"kubecfg_grafana\",\n", "    src = \"grafana.jsonnet\",\n", "    cluster_wide = True,\n", "    deps = [\n", "        \"//deploy/common:cloud_info\",\n", "        \"//deploy/common:lib\",\n", "        \"//deploy/common:node-lib\",\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/central/METADATA.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/auth/central/METADATA.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "...\n", "      name: 'auth-central-monitoring',\n", "      kubecfg: {\n", "        target: '//services/auth/central/server:kubecfg_monitoring',\n", "        task: [\n", "          {\n", "            cloud: 'ALL_LEADS',\n", "          },\n", "        ],\n", "      },\n", "      health: {\n", "        tier: 'TIER_1_A',\n", "        experts: {\n", "          users: ['moogi', 'costa'],\n", "          slack_channel: '#system-services',\n", "        },\n", "      },\n", "    },\n", "  ],\n", "}\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/tenant_manager/processor/BUILD`:\n", "\n", "<augment_code_snippet path=\"tools/tenant_manager/processor/BUILD\" mode=\"EXCERPT\">\n", "````\n", "...\n", "    name = \"kubecfg\",\n", "    src = \"deploy.jsonnet\",\n", "    cloud = [\n", "        \"GCP_US_CENTRAL1_DEV\",\n", "    ],\n", "    data = [\n", "        \":image\",\n", "    ],\n", "    deps = [\n", "        \"//deploy/common:cert-lib\",\n", "        \"//deploy/common:config-map-lib\",\n", "        \"//deploy/common:dynamic-feature-flags-lib\",\n", "        \"//deploy/common:lib\",\n", "        \"//deploy/common:node-lib\",\n", "        \"//deploy/common:telemetry-lib\",\n", "        \"//deploy/gcp:gcp-lib\",\n", "        \"//services/lib/pubsub:pubsub-lib\",\n", "        \"//tools/deploy:github_pr_token_lib\",\n", "    ],\n", ")\n", "\n", "kubecfg(\n", "    name = \"kubecfg_monitoring\",\n", "    src = \"monitoring.jsonnet\",\n", "    cluster_wide = True,\n", "    visibility = [\n", "        \"//services/auth/central:__subpackages__\",\n", "    ],\n", "    deps = [\n", "        \"//deploy/gcp:monitoring-lib\",\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/query/server/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/auth/query/server/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "\n", "function(cloud)\n", "  local tenantNotFoundSpec = {\n", "    displayName: 'Auth Query Tenant Not Found Errors',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '300s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'warning' },\n", "      query: |||\n", "        sum by (namespace,cluster) (increase(au_auth_query_tenant_not_found_count[5m])) > 10\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "    monitoringLib.alertPolicy(\n", "      cloud,\n", "      tenantNotFoundSpec,\n", "      'auth-query-tenant-not-found',\n", "      'High number of Tenant Not Found errors in Auth Query service'\n", "    ),\n", "  ]\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/auth0/webhook/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/auth/auth0/webhook/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  local errorsAtLoadBalancerSpec = {\n", "    displayName: 'Auth Webhook 5xx measured at load balancer',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'warning' },\n", "      query: |||\n", "        sum by (namespace)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource=\"https_lb_rule\",target_proxy_name=~\".*auth-webhook.*\",response_code_class=\"500\"}[5m])) > 3\n", "      |||,\n", "    },\n", "  };\n", "\n", "  local errorsAtServiceSpec = {\n", "    displayName: 'Auth Webhook errors measured at service',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'warning' },\n", "      query: |||\n", "        sum by (namespace)(increase(au_auth0_webhook_requests{status=~\"^40[013]|50[0-9]$\", pod=~\"auth-webhook-.*\"}[5m])) > 3\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/monitoring/METADATA.jsonnet`:\n", "\n", "<augment_code_snippet path=\"tools/monitoring/METADATA.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details\n", "{\n", "  deployment: [\n", "    {\n", "      name: 'central-monitoring',\n", "      kubecfg: {\n", "        target: '//tools/monitoring:monitoring_kubecfg',\n", "        task: [\n", "          {\n", "            cloud: 'ALL_LEADS',\n", "          },\n", "        ],\n", "      },\n", "    },\n", "    {\n", "      name: 'dcgm-exporter',\n", "      kubecfg: {\n", "        target: '//tools/monitoring:kubecfg_dcgm_exporter',\n", "        task: [\n", "          {\n", "            cloud: 'ALL_GCP',\n", "          },\n", "        ],\n", "      },\n", "    },\n", "    {\n", "      name: 'gra<PERSON>a',\n", "      kubecfg: {\n", "        target: '//tools/monitoring:kubecfg_grafana',\n", "        task: [\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/bazel_runner/server/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"tools/bazel_runner/server/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  // this is a cluster role as kube_job_status_start_time is a cluster level metric\n", "  local testInfraSpec = {\n", "    displayName: 'Test Infra Errors',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '600s',\n", "      evaluationInterval: '300s',\n", "      labels: { severity: 'info' },\n", "      query: |||\n", "        sum(increase(bazel_runner_run_finished_total{state=\"ERROR\"}[1h])) > 4\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "    monitoringLib.alertPolicy(cloud, testInfraSpec, 'bazel-runner-test-infrastructure-errors', 'Tests are failing with \"Test Infrastructure\" errors'),\n", "  ]\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/query/server/src/metrics.rs`:\n", "\n", "<augment_code_snippet path=\"services/auth/query/server/src/metrics.rs\" mode=\"EXCERPT\">\n", "````\n", "use lazy_static::lazy_static;\n", "use prometheus::{\n", "    register_histogram_vec, register_int_counter, register_int_gauge_vec, HistogramVec, IntCounter,\n", "    IntGaugeVec,\n", "};\n", "\n", "lazy_static! {\n", "    /// Histogram of request latencies to auth query endpoints. This can also be used to\n", "    /// calculate throughput because Prometheus histograms keep a count.\n", "    pub static ref RESPONSE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(\n", "        // Keep this in sync with base/python/grpc/metrics.py please\n", "        \"au_rpc_latency_histogram\",\n", "        \"Histogram of RPC latencies\",\n", "        &[\"service\", \"endpoint\", \"status_code\", \"request_source\", \"tenant_name\"]\n", "    )\n", "    .expect(\"metric can be created\");\n", "\n", "    /// The number of currently active requests\n", "    pub static ref ACTIVE_REQUESTS_COLLECTOR: IntGaugeVec = register_int_gauge_vec!(\n", "        // Keep this in sync with base/python/grpc/metrics.py please\n", "        \"au_active_requests_gauge\",\n", "        \"The number of currently active requests\",\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/tenant/server/METADATA.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/auth/tenant/server/METADATA.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "...\n", "          for tenant in cloudInfo.centralNamespaces\n", "          if tenant.cloud != 'GCP_US_CENTRAL1_GSC_PROD'\n", "        ],\n", "      },\n", "      health: {\n", "        tier: 'TIER_1_A',\n", "        experts: {\n", "          users: ['moogi', 'costa'],\n", "          slack_channel: '#system-services',\n", "        },\n", "      },\n", "      priority: 1,  // Try to run this early since it's a central dependency of other deploys\n", "    },\n", "    {\n", "      name: 'auth-tenant-monitoring',\n", "      kubecfg: {\n", "        target: '//services/auth/tenant/server:kubecfg_monitoring',\n", "        task: [\n", "          {\n", "            cloud: 'ALL_LEADS',\n", "          },\n", "        ],\n", "      },\n", "      health: {\n", "        tier: 'TIER_1_A',\n", "        experts: {\n", "          users: ['moogi', 'costa'],\n", "          slack_channel: '#system-services',\n", "        },\n", "      },\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `experimental/mattm/table.txt`:\n", "\n", "<augment_code_snippet path=\"experimental/mattm/table.txt\" mode=\"EXCERPT\">\n", "````\n", "# Notes on how we break down what runs and where.\n", "\n", "augi-console                | main interactive, can be all interactive, batch, etc\n", "cceval-prober-dogfood       | main interactive\n", "expose-proxy                | main interactive\n", "hindsight-prober-dogfood    | main interactive\n", "webserver                   | main interactive\n", "metastore (\"spark guys\")    | main interactive\n", "gh-runner                   | main interactive\n", "->ci-worker                 | main interactive\n", "\n", "determined                  | batch\n", "\n", "jumphost                    | interactive, batch\n", "augi-user-sa-sync           | interactive, batch?, not core\n", "\n", "grafana.helm                | core\n", "ssh-keys-syncer             | core\n", "userauth (UI)               | core\n", "config-connector            | core, maybe all\n", "devex metrics               | core\n", "harbor (master)             | core\n", "harbor (leaf)               | non-core, non-gcp\n", "\n", "external-dns                | all\n", "cert-manager                | all\n", "ingress-nginx               | all\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/deploy_runner/deploy_job/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"tools/deploy_runner/deploy_job/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  // this is a cluster role as kube_job_status_start_time is a cluster level metric\n", "  local deploySpec = {\n", "    displayName: 'Deploy Failed',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '180s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'info' },\n", "      query: |||\n", "        clamp_max(\n", "          # Get start time of all deploy jobs\n", "          max(kube_job_status_start_time{job_name=~\"deploy-[0-9]+\"}) by(exported_namespace, job_name, cluster)\n", "          # Get the latest deploy job\n", "          == on(exported_namespace) group_left() max(kube_job_status_start_time{job_name=~\"deploy-[0-9]+\"}) by(exported_namespace)\n", "        , 1)\n", "        # Add the failure state of the job\n", "        * on(job_name, cluster) group_left() (sum by(job_name, cluster) (kube_job_status_failed) != 0)\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "    monitoringLib.alertPolicy(cloud, deploySpec, 'deploy-job-alerts', |||\n", "      Deploy job %s unsuccessful on %s.\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `experimental/marcmac/gcp/helm/determined-0.36.0/values-gcp-sing.yaml`:\n", "\n", "<augment_code_snippet path=\"experimental/marcmac/gcp/helm/determined-0.36.0/values-gcp-sing.yaml\" mode=\"EXCERPT\">\n", "````\n", "...\n", "  # Configure default Docker images for all GPU tasks (experiments, notebooks, commands) and\n", "  # CPU tasks (CPU notebooks, TensorBoards, zero-slot commands). If a Docker image is defined\n", "  # for an individual task, that image will replace the default one that is defined here.\n", "  # If specifying a default image, both GPU and CPU default images must be defined.\n", "  # cpuImage:\n", "  # gpuImage:\n", "\n", "\n", "  # logPolicies:\n", "  #  - pattern: test\n", "  #    action:\n", "  #      type: exclude_node\n", "\n", "  # Configure an optional inline script that will be executed as part of the task setup process.\n", "  # startupHook: echo \"hello from master startup hook\"\n", "\n", "## Configure whether we collect anonymous information about the usage of Determined.\n", "telemetry:\n", "  enabled: true\n", "\n", "## Configure Prometheus endpoints for monitoring.\n", "observability:\n", "  enable_prometheus: true\n", "\n", "## A user-friendly name to identify this cluster by.\n", "clusterName: determined-gcp-sing\n", "\n", "## Specifies the duration in seconds before idle\n", "## TensorBoard instances are automatically terminated.\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/support/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/support/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  local spec = {\n", "    displayName: 'Support UI Request Errors',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'info' },\n", "      query: |||\n", "        sum by (namespace,cluster)(increase(flask_http_request_total{status!~\"^200$|^403$|^404$\", pod=~\"support-ui-.*\"}[30m])) > 10\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "    monitoringLib.alertPolicy(cloud, spec, 'support-ui-request-errors', 'HTTP requests to support UI return errors'),\n", "  ]\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/feature_flags/syncer/Readme.md`:\n", "\n", "<augment_code_snippet path=\"tools/feature_flags/syncer/Readme.md\" mode=\"EXCERPT\">\n", "````\n", "...\n", "The syncer should run in production as it modifies production settings. However, most of our CI\n", "system is currently in dev. The initial version will run in dev. AU-2486 tracks migrating the feature flags syncer to production.\n", "\n", "### Authorization\n", "\n", "#### Syncer gRPC service\n", "\n", "gRPC exposed in cluster. Anyone inside cluster can connect.\n", "\n", "The syncer does not currently support authorization. Any one who can connect to the syncer\n", "can issue a gRPC to the syncer can kick off a sync job. This could lead to resource exhaustion in Github.\n", "\n", "#### Prometheus scraping\n", "\n", "Doesn't do Prometheus yet...\n", "\n", "HTTP exposed in cluster. Anyone inside cluster can connect and read metrics.\n", "\n", "### Authentication\n", "\n", "#### Syncer gRPC service\n", "\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `tools/tenant_manager/webhook/BUILD`:\n", "\n", "<augment_code_snippet path=\"tools/tenant_manager/webhook/BUILD\" mode=\"EXCERPT\">\n", "````\n", "...\n", "\n", "kubecfg(\n", "    name = \"kubecfg\",\n", "    src = \"deploy.jsonnet\",\n", "    data = [\n", "        \":image\",\n", "    ],\n", "    deps = [\n", "        \"//deploy/common:cert-lib\",\n", "        \"//deploy/common:cloud_info\",\n", "        \"//deploy/common:config-map-lib\",\n", "        \"//deploy/common:lib\",\n", "        \"//deploy/common:node-lib\",\n", "        \"//deploy/common:telemetry-lib\",\n", "        \"//deploy/gcp:gcp-lib\",\n", "        \"//services/lib/pubsub:pubsub-lib\",\n", "    ],\n", ")\n", "\n", "kubecfg(\n", "    name = \"kubecfg_monitoring\",\n", "    src = \"monitoring.jsonnet\",\n", "    cluster_wide = True,\n", "    visibility = [\n", "        \"//services/auth/central:__subpackages__\",\n", "    ],\n", "    deps = [\n", "        \"//deploy/gcp:monitoring-lib\",\n", "    ],\n", ")\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/api_proxy/health_check/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/api_proxy/health_check/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  local minutes = 2;\n", "  local healthMetric = 'api_proxy_health_check_status_total';\n", "  local healthSpec = {\n", "    displayName: 'API Proxy health below 90%',\n", "    conditionPrometheusQueryLanguage: {\n", "      // Intentionally longer duration than the aggregation period to avoid alerting on blips\n", "      duration: '300s',\n", "      evaluationInterval: '30s',  // match default health check interval\n", "      labels: { severity: 'error' },\n", "      // The join to kube_namespace_created implements a 5-hour grace period for new tenants.\n", "      // TODO: what's the actual cause of observed lags in new tenant health?\n", "      query: |||\n", "        sum by (namespace, cluster) (increase(%(metric)s{status=\"success\"}[%(minutes)sm]))\n", "        / sum by (namespace, cluster)(increase(%(metric)s{}[%(minutes)sm])) < 0.9\n", "        and on (namespace, cluster) kube_namespace_created + 18000 < time()\n", "      ||| % { metric: healthMetric, minutes: minutes },\n", "    },\n", "  };\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/chat_host/server/monitoring.jsonnet`:\n", "\n", "<augment_code_snippet path=\"services/chat_host/server/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "\n", "function(cloud)\n", "  local vertexAiHighUsage(req_min_threshold, duration_minutes, severity) = {\n", "    displayName: 'Vertex AI usage is above %s req/m for the last %s minutes' % [req_min_threshold, duration_minutes],\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '%ss' % (duration_minutes * 60),\n", "      evaluationInterval: '60s',\n", "      labels: { severity: severity },\n", "      query: |||\n", "        sum(increase(aiplatform_googleapis_com:prediction_online_prediction_count[1m])) by (endpoint_id) > %s\n", "      ||| % [req_min_threshold],\n", "    },\n", "  };\n", "  local vertexAiHighUsageWarning = vertexAiHighUsage(50, 2, 'warning');\n", "  local vertexAiHighUsageError = vertexAiHighUsage(100, 1, 'error');\n", "\n", "  [\n", "    // Warn if vertex ai usage is above 50 req/m during the last 2 minutes for any endpoint (model), error if over 100 req/m during the last minute.\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/auth/auth0/webhook/BUILD`:\n", "\n", "<augment_code_snippet path=\"services/auth/auth0/webhook/BUILD\" mode=\"EXCERPT\">\n", "````\n", "...\n", "\n", "kubecfg(\n", "    name = \"kubecfg\",\n", "    src = \"deploy.jsonnet\",\n", "    data = [\n", "        \":image\",\n", "    ],\n", "    deps = [\n", "        \"//deploy/common:cert-lib\",\n", "        \"//deploy/common:cloud_info\",\n", "        \"//deploy/common:config-map-lib\",\n", "        \"//deploy/common:lib\",\n", "        \"//deploy/common:node-lib\",\n", "        \"//deploy/common:telemetry-lib\",\n", "        \"//deploy/gcp:gcp-lib\",\n", "        \"//services/deploy:endpoints\",\n", "        \"//services/lib/pubsub:pubsub-lib\",\n", "    ],\n", ")\n", "\n", "kubecfg(\n", "    name = \"kubecfg_monitoring\",\n", "    src = \"monitoring.jsonnet\",\n", "    cluster_wide = True,\n", "    visibility = [\n", "        \"//services/auth/central:__subpackages__\",\n", "    ],\n", "    deps = [\n", "        \"//deploy/gcp:monitoring-lib\",\n", "    ],\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "Here is an excerpt from the file `services/request_insight/BUILD`:\n", "\n", "<augment_code_snippet path=\"services/request_insight/BUILD\" mode=\"EXCERPT\">\n", "````\n", "...\n", ")\n", "\n", "# Used in core_deploy.jsonnet to deploy the RI pub/sub topic and config alongside\n", "# request-insight-core in dev deployments.\n", "kubecfg_library(\n", "    name = \"pubsub_lib\",\n", "    srcs = [\"pubsub_deploy.jsonnet\"],\n", "    visibility = [\"//services/request_insight:__subpackages__\"],\n", "    deps = [\n", "        \"//deploy/common:cloud_info\",\n", "        \"//services/request_insight/publisher:publisher_lib\",\n", "    ],\n", ")\n", "\n", "kubecfg(\n", "    name = \"monitoring_kubecfg\",\n", "    src = \"monitoring.jsonnet\",\n", "    cluster_wide = True,\n", "    deps = [\n", "        \"//deploy/gcp:monitoring-lib\",\n", "    ],\n", ")\n", "...\n", "````\n", "</augment_code_snippet>\n", "\n", "I have the file `services/auth/central/server/monitoring.jsonnet` open. Here is an excerpt from the file:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/monitoring.jsonnet\" mode=\"EXCERPT\">\n", "````\n", "local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';\n", "function(cloud)\n", "  local spec = {\n", "    displayName: 'Auth Central Request Errors',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'error' },\n", "      query: |||\n", "        sum by (namespace,cluster)(increase(flask_http_request_total{status=~\"^50[0-9]$\", pod=~\"auth-central-.*\"}[30m])) > 1\n", "      |||,\n", "    },\n", "  };\n", "\n", "  local errorsAtLoadBalancerSpec = {\n", "    displayName: 'Auth Central 5xx measured at load balancer',\n", "    conditionPrometheusQueryLanguage: {\n", "      duration: '120s',\n", "      evaluationInterval: '60s',\n", "      labels: { severity: 'warning' },\n", "      query: |||\n", "        sum by (namespace)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource=\"https_lb_rule\",target_proxy_name=~\".*auth-central.*\",response_code_class=\"500\"}[5m])) > 3\n", "      |||,\n", "    },\n", "  };\n", "\n", "  [\n", "    monitoringLib.alertPolicy(cloud, spec, 'auth-central-request-errors', 'HTTP requests to auth central return errors'),\n", "    monitoringLib.alertPolicy(cloud, errorsAtLoadBalancerSpec, 'auth-central-5xx-errors', '5xx errors measured at load balancer'),\n", "  ]\n", "\n", "````\n", "</augment_code_snippet>\n", "\n", "\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Understood. I'll refer to the excerpts for context, and ignore them for general questions.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Fix the selected code. The IDE does not report any issues on the selected code.\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the code, there are a few issues that need to be fixed:\n", "\n", "1. The `ctx` variable is used but not defined\n", "2. The client connection creation has incorrect syntax and needs error handling\n", "3. The connection needs to be closed properly\n", "\n", "Here's the fixed code:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/grpc_server_test.go\" mode=\"EDIT\">\n", "````go\n", "    clientOpts := []grpc.DialOption{\n", "        grpc.WithContextDialer(func(context.Context, string) (net.Conn, error) {\n", "            return lis.Dial()\n", "        }),\n", "        grpc.WithTransportCredentials(insecure.NewCredentials()),\n", "    }\n", "    \n", "    ctx := context.Background()\n", "    conn, err := grpc.DialContext(ctx, \"bufnet\", clientOpts...)\n", "    if err != nil {\n", "        t.<PERSON>(\"Failed to dial bufnet: %v\", err)\n", "    }\n", "    defer conn.<PERSON>()\n", "    \n", "    client := proto.NewAuthServiceClient(conn)\n", "````\n", "</augment_code_snippet>\n", "\n", "The changes made:\n", "1. Added `ctx` definition using `context.Background()`\n", "2. Fixed the client connection creation using `grpc.DialContext`\n", "3. Added error handling for the connection\n", "4. <PERSON><PERSON><PERSON> initialized the client with the connection\n", "5. Changed `defer client.Close()` to `defer conn.Close()` since we need to close the connection, not the client\n", "\n", "These changes make the code more robust and correct while following gRPC best practices.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Is this right?\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the file and the selected code, the import statement appears to be incorrect. Based on the package structure shown in other test files and the context, the import path should be:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/grpc_server_test.go\" mode=\"EDIT\">\n", "````go\n", "\t\"github.com/augmentcode/augment/services/auth/central/server\"\n", "````\n", "</augment_code_snippet>\n", "\n", "This change:\n", "1. Removes the alias `server` since it's not used in the test file\n", "2. Corrects the import path to match the package structure (removing `grpc_server` and using `server` instead)\n", "\n", "The fix aligns with the project structure shown in other files like `auth_central_test_setup.py` and the package organization.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Does this look good for deploying a go grpc service container alongside the python auth container in the same pod?\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Yes, the configuration looks correct for deploying both a Go gRPC service and a Python auth service in the same pod. Here are the key components that make this work:\n", "\n", "1. The pod specification includes both containers:\n", "```j<PERSON>net\n", "containers: [\n", "  auth_container,        // Python auth service\n", "  auth_grpc_container,   // Go gRPC service\n", "  nginx_container        // Nginx reverse proxy\n", "]\n", "```\n", "\n", "2. The Go gRPC container (`auth_grpc_container`) is properly configured with:\n", "- Correct image target (`//services/auth/central/server/go:image`)\n", "- gRPC port (50051)\n", "- Health checks using gRPC health protocol\n", "- Shared volumes for configuration and certificates\n", "- Appropriate resource limits\n", "\n", "3. The services are properly exposed:\n", "- REST service through `restService` for the Python auth container\n", "- gRPC service through `grpcService` and `globalGrpcService` for the Go container\n", "\n", "4. Both containers share:\n", "- Common configuration through `auth-central-config` ConfigMap\n", "- Certificate volumes for mTLS\n", "- Prometheus metrics directory\n", "- Dynamic feature flags\n", "\n", "The setup follows microservices best practices while keeping related services in the same pod for efficient communication and resource sharing.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Any adjustments needed for metrics collection?\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the deployment configuration, the metrics setup looks mostly good, but there are a few recommended adjustments for better metrics collection:\n", "\n", "1. Add Prometheus annotations to both containers:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/deploy.jsonnet\" mode=\"EDIT\">\n", "````j<PERSON>net\n", "local auth_container = {\n", "  // ... existing config ...\n", "  metadata: {\n", "    annotations: {\n", "      'prometheus.io/scrape': 'true',\n", "      'prometheus.io/port': '9090',\n", "      'prometheus.io/path': '/metrics'\n", "    }\n", "  }\n", "};\n", "\n", "local auth_grpc_container = {\n", "  // ... existing config ...\n", "  metadata: {\n", "    annotations: {\n", "      'prometheus.io/scrape': 'true',\n", "      'prometheus.io/port': '9091',  // Using the grpc metrics port defined in config\n", "      'prometheus.io/path': '/metrics'\n", "    }\n", "  }\n", "};\n", "````\n", "</augment_code_snippet>\n", "\n", "2. Add explicit ports for metrics in the container specs:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/deploy.jsonnet\" mode=\"EDIT\">\n", "````j<PERSON>net\n", "ports: [\n", "  // ... existing ports ...\n", "  {\n", "    containerPort: 9090,\n", "    name: 'metrics',\n", "  },\n", "],\n", "````\n", "</augment_code_snippet>\n", "\n", "3. Add readiness checks for metrics endpoints:\n", "\n", "<augment_code_snippet path=\"services/auth/central/server/deploy.jsonnet\" mode=\"EDIT\">\n", "````j<PERSON>net\n", "readinessProbe: {\n", "  httpGet: {\n", "    path: '/metrics',\n", "    port: 9090\n", "  },\n", "  initialDelaySeconds: 10,\n", "  periodSeconds: 30,\n", "} + grpcLib.grpcHealthCheck(...),  // Merge with existing health checks\n", "````\n", "</augment_code_snippet>\n", "\n", "These changes will ensure:\n", "- Prometheus can discover and scrape metrics from both containers\n", "- Metrics endpoints are properly exposed\n", "- Service readiness includes metrics availability\n", "- Both Python and Go services share the same Prometheus multiproc directory for proper metrics aggregation\n", "\n", "The existing configuration for `PROMETHEUS_MULTIPROC_DIR` and volume mounts looks correct.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Why wouldn't the port have been in the container definition for auth_container before?\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the configuration, the metrics port (9090) is actually already defined in the config.json:\n", "\n", "```j<PERSON>net\n", "prometheus_bind_address: '0.0.0.0:9090',\n", "prometheus_grpc_bind_address: '0.0.0.0:9091',\n", "```\n", "\n", "I was incorrect to suggest adding it to the container ports. The ports section in the container definition already has what's needed:\n", "\n", "```j<PERSON>net\n", "ports: [\n", "  {\n", "    containerPort: 5000,\n", "    name: 'http-svc',\n", "  },\n", "],\n", "```\n", "\n", "The Prometheus metrics ports don't need to be explicitly defined in the container ports because:\n", "1. They're internal to the pod\n", "2. They're already configured in the application config\n", "3. The metrics scraping works through the pod's network namespace\n", "\n", "I should not have suggested that change. The current configuration is correct as is.\n", "\n", "    ----------------------------------------\n", "    user:\n", "    ----------------------------------------\n", "    Where is the prometheus scraper?\n", "\n", "    ----------------------------------------\n", "    assistant:\n", "    ----------------------------------------\n", "    Looking at the provided files, the Prometheus scraping configuration is managed through Google Managed Prometheus (GMP) as mentioned in `tools/monitoring/Readme.md`:\n", "\n", "```markdown\n", "# Monitoring\n", "\n", "This directory contains the observability and monitoring systems. Namely:\n", "  - Google Managed Prometheus for metrics\n", "  - Google Cloud Logging for logs\n", "...\n", "\n", "## Prometheus\n", "\n", "[Prometheus](https://prometheus.io/) is a system to collect metrics from Kubernetes pods and services.\n", "...\n", "The ClusterPodMonitoring rule defines which pods get configured for metrics collection by GKE. https://cloud.google.com/stackdriver/docs/managed-prometheus/setup-managed#gmp-pod-monitoring\n", "```\n", "\n", "The actual scraping configuration would be defined in a `ClusterPodMonitoring` or `PodMonitoring` custom resource in your Kubernetes cluster. However, this file is not shown in the provided excerpts. You would typically find it in your Kubernetes manifests, likely in a monitoring or observability directory.\n", "\n", "To see the actual scraping configuration, you could:\n", "1. Check for files containing `PodMonitoring` or `ClusterPodMonitoring` resources\n", "2. Or use kubectl to view the existing monitoring configurations:\n", "```bash\n", "kubectl get podmonitorings -A\n", "kubectl get clusterpodmonitorings -A\n", "```\n", "\n", "Would you like me to help you locate or create the appropriate monitoring configuration?\n", "\n", "\n"]}], "source": ["index = 3\n", "print(results[index][\"v12_bt4_v2\"][\"prompt_output\"])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Yes, looking at `tools/monitoring/managed_prometheus.jsonnet`, this is where the Prometheus scraping rules are defined:\n", "\n", "<augment_code_snippet path=\"tools/monitoring/managed_prometheus.jsonnet\" mode=\"EXCERPT\">\n", "````j<PERSON>net\n", "    {\n", "      apiVersion: 'monitoring.googleapis.com/v1',\n", "      kind: 'ClusterPodMonitoring',\n", "      metadata: {\n", "        name: 'prom-kubecfg',\n", "      },\n", "      spec: {\n", "        selector: {\n", "          matchLabels: {\n", "            'app.kubernetes.io/managed-by': 'kubecfg',\n", "          },\n", "        },\n", "        endpoints: [\n", "          {\n", "            port: 9090,\n", "            interval: '60s',\n", "          },\n", "        ],\n", "      },\n", "    },\n", "    // Add a second rule to get metrics for pods that are not managed by\n", "    // kubecfg, which is generally rare\n", "    {\n", "      apiVersion: 'monitoring.googleapis.com/v1',\n", "      kind: 'ClusterPodMonitoring',\n", "      metadata: {\n", "        name: 'prom-manual',\n", "      },\n", "      spec: {\n", "        selector: {\n", "          matchLabels: {\n", "            'augmentcode.com/enable-prometheus-scraping': 'true',\n", "          },\n", "````\n", "</augment_code_snippet>\n", "\n", "This shows two `ClusterPodMonitoring` rules:\n", "1. `prom-kubecfg`: Scrapes metrics from pods managed by kubecfg (which includes your auth service)\n", "2. `prom-manual`: Scrapes metrics from pods explicitly labeled with `augmentcode.com/enable-prometheus-scraping: \"true\"`\n", "\n", "Since your auth service is managed by kubecfg (as seen in the BUILD files), it will be picked up by the first rule. The scraper will collect metrics from port 9090 every 60 seconds.\n"]}], "source": ["print(results[index][\"bt4_v2\"][\"response\"])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Yes, looking at `tools/monitoring/managed_prometheus.jsonnet`, this is where the Prometheus scraping rules are defined:\n", "\n", "<augment_code_snippet path=\"tools/monitoring/managed_prometheus.jsonnet\" mode=\"EXCERPT\">\n", "````j<PERSON>net\n", "    {\n", "      apiVersion: 'monitoring.googleapis.com/v1',\n", "      kind: 'ClusterPodMonitoring',\n", "      metadata: {\n", "        name: 'prom-kubecfg',\n", "      },\n", "      spec: {\n", "        selector: {\n", "          matchLabels: {\n", "            'app.kubernetes.io/managed-by': 'kubecfg',\n", "          },\n", "        },\n", "        endpoints: [\n", "          {\n", "            port: 9090,\n", "            interval: '60s',\n", "          },\n", "        ],\n", "      },\n", "    },\n", "    // Add a second rule to get metrics for pods that are not managed by\n", "    // kubecfg, which is generally rare\n", "    {\n", "      apiVersion: 'monitoring.googleapis.com/v1',\n", "      kind: 'ClusterPodMonitoring',\n", "      metadata: {\n", "        name: 'prom-manual',\n", "      },\n", "      spec: {\n", "        selector: {\n", "          matchLabels: {\n", "            'augmentcode.com/enable-prometheus-scraping': 'true',\n", "          },\n", "````\n", "\n", "There are two rules:\n", "1. `prom-kubecfg`: Scrapes metrics from pods managed by kubecfg (which includes your auth service)\n", "2. `prom-manual`: Scrapes metrics from pods explicitly labeled with `augmentcode.com/enable-prometheus-scraping: \"true\"`\n", "\n", "Since your auth service is managed by kubecfg (as seen in the BUILD files), it will be picked up by the first rule. The scraper will look for metrics on port 9090 with a 60-second interval.\n", "\n", "<guess_of_next_user_question>\n", "    <next_user_question>Do we need to add any labels to our auth service deployment for this to work?</next_user_question>\n", "    <next_user_question>Should we add a separate monitoring rule for the gRPC metrics on port 9091?</next_user_question>\n", "</guess_of_next_user_question>\n"]}], "source": ["print(results[index][\"v12_bt4_v2\"][\"response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import dataclasses
import json
import time

from base.prompt_format.common import Exchange
from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name
from research.tools.chat_replay.replay_infra import (
    get_input_and_documents,
)
from research.tools.chat_replay.replay_utils import (
    TOKEN_APPORTIONMENT,
    chat_prompt_dict_from_input,
    count_tags,
    fix_input,
    render_prompt,
    run_claude,
)


def build_teacher_forced_prompt_outputs(
    request_id, prompt_formatter, further_fix_input=lambda x: x
):
    chat_prompt_input, _ = get_input_and_documents(request_id)
    request_ids = [request_id]
    for exchange in chat_prompt_input.chat_history:
        request_ids.append(exchange.request_id)

    for request_id in request_ids:
        current_chat_prompt_input, _ = get_input_and_documents(request_id)
        current_chat_prompt_input = fix_input(current_chat_prompt_input)
        current_chat_prompt_input = further_fix_input(current_chat_prompt_input)
        prompt_output = prompt_formatter.format_prompt(current_chat_prompt_input)
        yield prompt_output


def test_teacher_forced(
    request_id, prompt_formatter, base_model_version="s", further_fix_input=lambda x: x
):
    for prompt_output in build_teacher_forced_prompt_outputs(
        request_id, prompt_formatter, further_fix_input
    ):
        response, _ = run_claude(prompt_output, base_model_version=base_model_version)
        opening_count, closing_count = count_tags(response)
        yield {
            "request_id": request_id,
            "opening_count": opening_count,
            "closing_count": closing_count,
            "response": response,
            "prompt_output": render_prompt(prompt_output),
        }


def test_teacher_forced_legacy(
    request_id, prompt_formatter, base_model_version=2, further_fix_input=lambda x: x
):
    chat_prompt_input, _ = get_input_and_documents(request_id)
    request_ids = [request_id]
    for exchange in chat_prompt_input.chat_history:
        request_ids.append(exchange.request_id)

    for request_id in request_ids:
        current_chat_prompt_input, _ = get_input_and_documents(request_id)
        current_chat_prompt_input = fix_input(current_chat_prompt_input)
        current_chat_prompt_input = further_fix_input(current_chat_prompt_input)
        prompt_output = prompt_formatter.format_prompt(current_chat_prompt_input)
        response, _ = run_claude(prompt_output, base_model_version=base_model_version)
        opening_count, closing_count = count_tags(response)
        yield {
            "request_id": request_id,
            "opening_count": opening_count,
            "closing_count": closing_count,
            "response": response,
            "prompt_output": render_prompt(prompt_output),
        }


def test_autoregressively(
    request_id,
    prompt_formatter,
    base_model_version="sonnet3.5-v2",
    skip_on_data_error=False,
):
    try:
        chat_prompt_input, _ = get_input_and_documents(request_id)
    except Exception as e:
        if skip_on_data_error:
            print(f"Skipping due to data error on {request_id}: {e}")
            return
        else:
            raise e
    request_ids = []
    for exchange in chat_prompt_input.chat_history:
        request_ids.append(exchange.request_id)
    request_ids.append(request_id)

    chat_history = []
    for message_index, request_id in enumerate(request_ids):
        print(f"   Processing message {message_index}/{len(request_ids)}")
        try:
            current_chat_prompt_input, _ = get_input_and_documents(request_id)
        except Exception as e:
            if skip_on_data_error:
                print(f"Skipping due to data error on {request_id}: {e}")
                return
            else:
                raise e
        current_chat_prompt_input = dataclasses.replace(
            current_chat_prompt_input, chat_history=chat_history
        )
        prompt_output = prompt_formatter.format_prompt(current_chat_prompt_input)
        response = None
        while response is None:
            try:
                response, _ = run_claude(
                    prompt_output, base_model_version=base_model_version
                )
            except Exception as e:
                print(f"Error running message {request_id}: {e}")
                time.sleep(60)
        opening_count, closing_count = count_tags(response)
        yield {
            "request_id": request_id,
            "opening_count": opening_count,
            "closing_count": closing_count,
            "response": response,
            "prompt_output": render_prompt(prompt_output),
            "prompt_input": chat_prompt_dict_from_input(current_chat_prompt_input),
        }
        chat_history.append(
            Exchange(
                request_message=current_chat_prompt_input.message,
                response_text=response,
            )
        )


def test_directly(
    request_id, prompt_formatter, base_model_version=2, further_fix_input=lambda x: x
):
    chat_prompt_input, _ = get_input_and_documents(request_id)
    chat_prompt_input = fix_input(chat_prompt_input)
    chat_prompt_input = further_fix_input(chat_prompt_input)
    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
    response, _ = run_claude(prompt_output, base_model_version=base_model_version)
    opening_count, closing_count = count_tags(response)
    return {
        "opening_count": opening_count,
        "closing_count": closing_count,
        "response": response,
        "prompt_output": render_prompt(prompt_output),
    }


def test_iteratively(
    request_id,
    prompt_formatter_name,
    retrieval_section_version,
    base_model_version=2,
    backtick_count=4,
    test_type="history",
):
    prompt_formatter = get_structured_chat_prompt_formatter_by_name(
        prompt_formatter_name,
        TOKEN_APPORTIONMENT,
        retrieval_section_version,
        backtick_count,
    )
    direct_result = test_directly(request_id, prompt_formatter)
    if test_type == "history":
        autoregressive_iterator = test_autoregressively(
            request_id, prompt_formatter, base_model_version
        )
        autoregressive_results = []
        total_opening_count_ar = 0
        total_closing_count_ar = 0
        for real_ar_result in autoregressive_iterator:
            total_opening_count_ar += real_ar_result["opening_count"]
            total_closing_count_ar += real_ar_result["closing_count"]
            autoregressive_results.append(real_ar_result)
            yield {
                "total_opening_count_ar": total_opening_count_ar,
                "total_closing_count_ar": total_closing_count_ar,
                **direct_result,
                "autoregressive": autoregressive_results,
            }
    elif test_type == "direct_only":
        yield direct_result
    else:
        raise ValueError(f"Unknown test type: {test_type}")


def generate_tests(
    request_ids,
    data_directory: str,
    prompt_formatter_name,
    retrieval_section_version,
    base_model_version=2,
    backtick_count=4,
    start_example_index=0,
    start_data_index=0,
    sleep_time=0,
):
    prompt_formatter = get_structured_chat_prompt_formatter_by_name(
        prompt_formatter_name,
        TOKEN_APPORTIONMENT,
        retrieval_section_version,
        backtick_count,
    )
    processed_request_ids = set()
    data_index = start_data_index
    for example_index, request_id in enumerate(request_ids):
        if example_index < start_example_index:
            continue
        print(f"Processing example {example_index}/{len(request_ids)}")
        if request_id in processed_request_ids:
            continue
        autoregressive_iterator = test_autoregressively(
            request_id, prompt_formatter, base_model_version, skip_on_data_error=True
        )
        for message_index, result in enumerate(autoregressive_iterator):
            if message_index < 15:
                continue
            processed_request_ids.add(result["request_id"])
            opening_count = result["opening_count"]
            closing_count = result["closing_count"]
            if opening_count > closing_count:
                print(
                    f"==== Collecting example {data_index} at ({example_index}, {message_index}) ===="
                )
                with open(f"{data_directory}/{data_index:03d}.json", "w") as f:
                    result["prompt_input"]["request_id"] = result["request_id"]
                    json.dump(result["prompt_input"], f, indent=4)
                with open(f"{data_directory}/{data_index:03d}.md", "w") as f:
                    f.write(result["prompt_output"])
                data_index += 1
            time.sleep(sleep_time)

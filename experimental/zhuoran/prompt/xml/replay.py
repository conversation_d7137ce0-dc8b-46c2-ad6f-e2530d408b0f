import json
from collections import defaultdict

from experimental.zhuoran.prompt.xml.xml_replay_infra import (
    results_to_printable,
    test_iteratively,
)
from experimental.zhuoran.prompt.xml.request_ids import REQUEST_IDS


def further_fix_input(chat_prompt_input):
    return chat_prompt_input


all_results = defaultdict(lambda: defaultdict(defaultdict))
retrieval_section_version = 2
prompt_formatter_name = "binks-claude-v10"
test_type = "history"
for index, request_id in enumerate(REQUEST_IDS):
    for results in test_iteratively(
        request_id,
        prompt_formatter_name,
        retrieval_section_version,
        base_model_version=2,
        test_type=test_type,
        backtick_count=4,
    ):
        print(
            f"{prompt_formatter_name} (ret v{retrieval_section_version}) {index}/{len(REQUEST_IDS)}:"
        )
        all_results[retrieval_section_version][prompt_formatter_name][index] = results
        results_to_print = results_to_printable(results)
        print(json.dumps(results_to_print, indent=4))
        print()
        with open(
            "/mnt/efs/augment/user/zhuoran/prompt/replay_results/results7.json",
            "w",
        ) as f:
            json.dump(all_results, f, indent=4)

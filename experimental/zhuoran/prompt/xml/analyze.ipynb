{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_wrong_examples(simple_results):\n", "    for model_name, model_results in simple_results.items():\n", "        print(model_name)\n", "        wrong_count = 0\n", "        for index, index_results in model_results.items():\n", "            opening_count = index_results[\"total_opening_count_ar\"]\n", "            closing_count = index_results[\"total_closing_count_ar\"]\n", "            if opening_count > closing_count:\n", "                wrong_count += 1\n", "                print(f\"{index}: ({opening_count}, {closing_count})\")\n", "        print(f\"Errors: {wrong_count}/{len(model_results)}\")\n", "        print()\n", "\n", "\n", "def print_wrong_messages(simple_results):\n", "    for model_name, model_results in simple_results.items():\n", "        print(\"=\" * 20)\n", "        print(model_name)\n", "        model_wrong_example_count = 0\n", "        model_wrong_message_count = 0\n", "        model_message_count = 0\n", "        for index, index_results in model_results.items():\n", "            example_wrong_message_count = 0\n", "            for message_index, ar_results in enumerate(index_results[\"autoregressive\"]):\n", "                opening_count = ar_results[\"opening_count\"]\n", "                closing_count = ar_results[\"closing_count\"]\n", "                if opening_count > closing_count:\n", "                    example_wrong_message_count += 1\n", "                    print(\n", "                        f\"{index}, {message_index}: ({opening_count}, {closing_count})\"\n", "                    )\n", "            example_message_count = len(index_results[\"autoregressive\"])\n", "            model_message_count += example_message_count\n", "            if example_wrong_message_count > 0:\n", "                model_wrong_example_count += 1\n", "                model_wrong_message_count += example_wrong_message_count\n", "                print(\n", "                    f\"Example {index}: {example_wrong_message_count}/{example_message_count} wrong messages\"\n", "                )\n", "                print()\n", "        print(\n", "            f\"Model {model_name}: {model_wrong_example_count}/{len(model_results)} wrong examples\"\n", "        )\n", "        print(\n", "            f\"Model {model_name}: {model_wrong_message_count}/{model_message_count} wrong messages\"\n", "        )\n", "        print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "simplified_results = {}\n", "for model in [\n", "    # \"results_backtick3r1\",\n", "    \"results_v8\",\n", "    \"old_claude3\",\n", "]:\n", "    with open(\n", "        f\"/mnt/efs/augment/user/zhuoran/prompt/replay_results/{model}.json\", \"r\"\n", "    ) as f:\n", "        model_results = json.load(f)\n", "\n", "    if model.startswith(\"results_v8\"):\n", "        simplified_results[model] = model_results[\"1\"][\"binks-claude-v8\"]\n", "    elif model.startswith(\"results\"):\n", "        simplified_results[model] = model_results[\"2\"][\"binks-claude-v10\"]\n", "    elif model.startswith(\"old_claude_v10\"):\n", "        simplified_results[model] = model_results[\"2\"][\"binks-claude-v10\"]\n", "    elif model.startswith(\"old_claude\"):\n", "        simplified_results[model] = model_results[\"1\"][\"binks-claude-v4\"]\n", "\n", "print_wrong_messages(simplified_results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "simplified_results = {}\n", "for model in [\n", "    \"results6\",\n", "    \"results7\",\n", "    \"results_backtick3\",\n", "    \"results_backtick3r1\",\n", "    \"results_backtick5\",\n", "    \"results_backtick5r1\",\n", "    # \"results_backtick10r1\",\n", "    # \"results_backtick10r2\",\n", "    \"results_v8\",\n", "    \"old_claude3\",\n", "    \"old_claude_v10r2\",\n", "]:\n", "    with open(\n", "        f\"/mnt/efs/augment/user/zhuoran/prompt/replay_results/{model}.json\", \"r\"\n", "    ) as f:\n", "        model_results = json.load(f)\n", "\n", "    if model.startswith(\"results_v8\"):\n", "        simplified_results[model] = model_results[\"1\"][\"binks-claude-v8\"]\n", "    elif model.startswith(\"results\"):\n", "        simplified_results[model] = model_results[\"2\"][\"binks-claude-v10\"]\n", "    elif model.startswith(\"old_claude_v10\"):\n", "        simplified_results[model] = model_results[\"2\"][\"binks-claude-v10\"]\n", "    elif model.startswith(\"old_claude\"):\n", "        simplified_results[model] = model_results[\"1\"][\"binks-claude-v4\"]\n", "\n", "print_wrong_messages(simplified_results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for model, results in simplified_results.items():\n", "#     print(model)\n", "#     for index, result in results.items():\n", "#         for ar_result in result[\"autoregressive\"]:\n", "#             if len(ar_result[\"response\"]) == 0:\n", "#                 print(\n", "#                     model, index, ar_result[\"opening_count\"], ar_result[\"closing_count\"]\n", "#                 )\n", "#     print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["single_results = simplified_results[\"results_backtick5r1\"][\"0\"]\n", "ar_results = single_results[\"autoregressive\"]\n", "for index, ar_result in enumerate(ar_results):\n", "    if ar_result[\"opening_count\"] > ar_result[\"closing_count\"]:\n", "        print(index, ar_result[\"opening_count\"], ar_result[\"closing_count\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = ar_results[9]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result[\"response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result[\"prompt_output\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Eval results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"/mnt/efs/augment/user/zhuoran/prompt/eval_first7.json\") as f:\n", "    results = json.load(f)\n", "len(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[0][\"v8\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 9\n", "base_label = \"v8\"\n", "a = results[index][base_label][\"response\"]\n", "b = results[index][f\"{base_label}_h35\"][\"response\"]\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(a)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(b)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["POSTPROCESSING_PROMPT = \"\"\"Reproduce whatever the user writes in the message back to the user. Do not alter or add a single character, except for fixing unclosed code blocks:\n", "- Each code block must start with <augment_code_snippet ...>\\n``` and end with ```\\\\n</augment_code_snippet>.\n", "- Fix any unclosed code blocks in the provided message. Each <augment_code_snippet> must have a closing </augment_code_snippet>.\n", "- For any `s, you must carefully examine if it is internally in a code block, or the closing marker of a code block. If it is a closing marker, you must add the closing </augment_code_snippet> tag.\n", "- Do not guess the metadata of the code block if it is not provided. It is okay to  have <augment_code_snippet> without any metadata.\n", "- DO NOT add, remove, or alter a single characters other than the ones mentioned above.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import run_claude\n", "\n", "# from experimental.zhuoran.prompt.xml.utils import POSTPROCESSING_PROMPT\n", "index = 0\n", "a = results[index][\"v8\"][\"response\"]\n", "c, _ = run_claude(\n", "    a\n", "    + \"\"\"Reproduce whatever I wrote above in the message back to the user. Do not alter or add a single character, except for fixing unclosed code blocks:\n", "- Each code block must start with <augment_code_snippet ...>\\n``` and end with ```\\\\n</augment_code_snippet>.\n", "- Fix any unclosed code blocks in the provided message. Each <augment_code_snippet> must have a closing </augment_code_snippet>.\n", "- For any `s, you must carefully examine if it is internally in a code block, or the closing marker of a code block. If it is a closing marker, you must add the closing </augment_code_snippet> tag.\n", "- Do not guess the metadata of the code block if it is not provided. It is okay to  have <augment_code_snippet> without any metadata.\n", "- DO NOT add, remove, or alter a single characters other than the ones mentioned above.\n", "\"\"\",\n", "    # system_message=POSTPROCESSING_PROMPT,\n", "    client_type=\"anthropic\",\n", "    base_model_version=\"sonnet3.5\",\n", ")\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(a)\n", "with open(\"/home/<USER>/c.md\", \"w\") as f:\n", "    f.write(c)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "\n", "\n", "def count_diff_lines(a: str, b: str) -> int:\n", "    \"\"\"Count the number of changed lines between two strings.\n", "\n", "    Args:\n", "        a: The first string to compare\n", "        b: The second string to compare\n", "\n", "    Returns:\n", "        The number of changed lines (additions and deletions)\n", "    \"\"\"\n", "    diff = list(\n", "        difflib.unified_diff(\n", "            a.splitlines(keepends=True),\n", "            b.splitlines(keepends=True),\n", "            fromfile=\"before\",\n", "            tofile=\"after\",\n", "        )\n", "    )\n", "\n", "    return sum(\n", "        1\n", "        for line in diff\n", "        if (line.startswith(\"+\") or line.startswith(\"-\"))\n", "        and not line.startswith(\"--- \")\n", "        and not line.startswith(\"+++ \")\n", "        and not line.startswith(\"@@\")\n", "    )\n", "\n", "\n", "print(f\"\\nTotal changed lines: {count_diff_lines(a, c)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
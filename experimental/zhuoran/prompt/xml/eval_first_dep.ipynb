{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing replay infra\n", "Finished initializing replay infra\n"]}], "source": ["from experimental.zhuoran.replay_infra import get_input_and_documents\n", "from experimental.zhuoran.replay_utils import (\n", "    count_tags,\n", "    fix_input,\n", "    generate_chat_response,\n", ")\n", "from experimental.zhuoran.prompt.xml.request_ids import FIRST_FAIL_REQUEST_IDS\n", "\n", "DOGFOOD_SHARD_URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "DOGFOOD_SHARD_RI_LINK_TEMPLATE = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}\"\n", "DEV_URL = \"https://dev-zhuoran.us-central.api.augmentcode.com/\"\n", "DEV_RI_LINK_TEMPLATE = \"https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/{request_id}\"\n", "\n", "# URL, RI_LINK_TEMPLATE = DOGFOOD_SHARD_URL, DOGFOOD_SHARD_RI_LINK_TEMPLATE\n", "URL, RI_LINK_TEMPLATE = DEV_URL, DEV_RI_LINK_TEMPLATE\n", "\n", "\n", "deployment_setups = {\n", "    # label: model_name, backtick_count\n", "    \"v9.1\": (\"claude-sonnet-3-5-16k-v9-1-chat\", 3),\n", "    \"v10\": (\"claude-sonnet-3-5-16k-v10-chat\", 3),\n", "    \"v11\": (\"claude-sonnet-3-5-16k-v11-chat\", 4),\n", "    \"v11.1\": (\"claude-sonnet-3-5-16k-v11-1-chat\", 4),\n", "}\n", "\n", "results = []\n", "accuracies = {model_name: [] for model_name in deployment_setups}"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["find the chunk class in services or base\n", "2 2\n", "https://support.dev-zhuoran.t.us-central1.dev.augmentcode.com/t/augment/request/58ab73b0-1445-4164-9c91-ebfaaf6abb88\n", "Looking through the provided files, there are several chunk-related classes, but the main chunk definitions appear in two places:\n", "\n", "1. In `services/embeddings_indexer/chunk.proto`:\n", "<augment_code_snippet path=\"services/embeddings_indexer/chunk.proto\" mode=\"EXCERPT\">\n", "```proto\n", "message Chunk {\n", "  // text of the chunk\n", "  string text = 1 [debug_redact = true];\n", "  // path of the containing document\n", "  string path = 2 [debug_redact = true];\n", "  // offset in characters from the beginning of the document\n", "  int32 char_offset = 3;\n", "  int32 length = 4;\n", "  int32 line_offset = 5;\n", "  int32 length_in_lines = 6;\n", "\n", "  oneof metadata {\n", "    DocumentationMetadata documentation_metadata = 7;\n", "  }\n", "\n", "  string header = 8 [debug_redact = true];\n", "}\n", "```\n", "</augment_code_snippet>\n", "\n", "2. In `base/retrieval/chunking/line_based_chunking.py`:\n", "<augment_code_snippet path=\"base/retrieval/chunking/line_based_chunking.py\" mode=\"EXCERPT\">\n", "```python\n", "class LineChunkContents(NamedTuple):\n", "    \"\"\"The core contents of a line chunk.\"\"\"\n", "\n", "    text: str\n", "    line_offset: int\n", "    char_offset: int\n", "    length_in_lines: int\n", "    header: str = \"\"\n", "\n", "    def crange(self) -> <PERSON><PERSON><PERSON><PERSON><PERSON>:\n", "        return Char<PERSON><PERSON><PERSON>(self.char_offset, self.char_offset + len(self.text))\n", "\n", "    def lrange(self) -> <PERSON><PERSON><PERSON><PERSON><PERSON>:\n", "        return CharRange(self.line_offset, self.line_offset + self.length_in_lines)\n", "```\n", "</augment_code_snippet>\n", "\n", "The `LineChunkContents` class in `base/retrieval/chunking/line_based_chunking.py` appears to be the core implementation used for chunking text in the codebase, while the protobuf message in `services/embeddings_indexer/chunk.proto` is used for serialization and RPC communication.\n"]}], "source": ["request_id = FIRST_FAIL_REQUEST_IDS[1]\n", "model_name = \"claude-sonnet-3-5-16k-v9-1-chat\"\n", "backtick_count = 3\n", "\n", "chat_prompt_input, documents = get_input_and_documents(request_id)\n", "print(chat_prompt_input.message.splitlines()[-1])\n", "\n", "example_results = {}\n", "fixed_chat_prompt_input = fix_input(chat_prompt_input, backtick_count)\n", "response, new_request_id = generate_chat_response(\n", "    URL,\n", "    model_name,\n", "    documents,\n", "    fixed_chat_prompt_input,\n", "    retry_limit=100,\n", "    raw=True,\n", ")\n", "opening_count, closing_count = count_tags(response)\n", "print(opening_count, closing_count)\n", "print(RI_LINK_TEMPLATE.format(request_id=new_request_id))\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
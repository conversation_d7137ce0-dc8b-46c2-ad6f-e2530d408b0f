{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "gcloud auth login\n", "gcloud auth application-default login\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-12-09 19:53:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet-v2@20241022\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["```python\n", "print(\"Hello, <PERSON>!\")\n", "```\n", "\n", "Streaming:\n", "```\n", "python\n", "print(\"\n", "Hello, <PERSON>!\")\n", "```\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "# MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "vertex_ai_client = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "\n", "def run_claude(message_or_prompt, system_message):\n", "    if isinstance(message_or_prompt, str):\n", "        message = message_or_prompt\n", "    else:\n", "        message = message_or_prompt.message\n", "        system_message = message_or_prompt.system_prompt\n", "    response = vertex_ai_client.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    )\n", "    return response.content[0].text, response\n", "\n", "\n", "def run_claude_stream(message, system_message):\n", "    with vertex_ai_client.client.messages.stream(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    ) as stream:\n", "        for text in stream.text_stream:\n", "            yield text\n", "\n", "\n", "text, response = run_claude(\n", "    \"Write a hello world in Python. Return a code block only.\", \"\"\n", ")\n", "print(text)\n", "print()\n", "\n", "print(\"Streaming:\")\n", "for text in run_claude_stream(\n", "    \"Write a hello world in Python. Return a code block only.\", \"\"\n", "):\n", "    print(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON>ple 33"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"\\\n", "----------------------------------------\n", "You are Augment, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "- At the end of every answer, you should write your 2 best guesses of what user will ask next. Enclose them in:\n", "<guess_of_next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "    <next_user_question>...</next_user_question>\n", "</guess_of_next_user_question>\n", "\n", "MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:\n", "\n", "1. Excerpts from existing files: Always include both `path=` and `mode=\"EXCERPT\"`. Example:\n", "\n", "<augment_code_snippet path=\"foo/bar.py\" mode=\"EXCERPT\">\n", "```python\n", "class AbstractTokenizer():\n", "    def __init__(self, name):\n", "        self.name = name\n", "\n", "    ...\n", "```\n", "</augment_code_snippet>\n", "\n", "2. Proposed edits: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"config/app_config.yaml\" mode=\"EDIT\">\n", "```yaml\n", "app:\n", "  name: MyWebApp\n", "  version: 1.3.0\n", "\n", "database:\n", "  host: new-db.example.com\n", "  port: 5432\n", "```\n", "</augment_code_snippet>\n", "\n", "3. New code or text: Always include `path=` and use `mode=\"EDIT\"`. Example:\n", "\n", "<augment_code_snippet path=\"hello/world.rb\" mode=\"EDIT\">\n", "```ruby\n", "def main\n", "  puts \"Hello, world!\"\n", "end\n", "```\n", "</augment_code_snippet>\n", "\"\"\"\n", "current_message = \"\"\"\\\n", "Where is the number retrieved chunks we ask the embeddings search for configured in jsonnet\"\"\"\n", "chat_history = [\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": \"\"\"\\\n", "Below are some relevant files from my project.\n", "\n", "Here is an excerpt from the file `services/deploy/methanol_0416.4_config.jsonnet`:\n", "\n", "```\n", "local constants = import 'services/deploy/constants.jsonnet';\n", "local modelConfig = {\n", "  name: 'methanol-0416-4',\n", "  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/methanol/methanol_0416.4_1250/global_step1250/',\n", "  model_type: 'EMBEDDING',\n", "  model_arch: {\n", "    arch_type: 'STARCODER',\n", "    num_layers: 24,\n", "    vocab_size: 51200,\n", "    emb_dim: 2048,\n", "    num_heads: 16,\n", "    head_dim: 128,\n", "    norm_eps: 1e-5,\n", "  },\n", "  embedding: {\n", "    tokenizer_name: 'rogue',\n", "    query_prompt_formatter_name: 'ethanol6.16.1-query-embedding',\n", "    key_prompt_formatter_name: 'passthrough-document',\n", "  },\n", "  chunking: {\n", "    name: 'signature',\n", "    config: {},\n", "  },\n", "  round_sizes: [256, 512, 1024, 2048],\n", "};\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "modelDeployment.embeddingModelConfig(\n", "  modelConfig=modelConfig,\n", "  transformationKey='dr-%s-1024char' % (std.asciiLower(modelConfig.name)),\n", "  chunkOrigin=constants.chunkOrigin.DENSE_SIGNATURE,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/chatanol1-11_config.jsonnet`:\n", "\n", "```\n", "local constants = import 'services/deploy/constants.jsonnet';\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "\n", "local name = 'chatanol1-11';\n", "local modelConfig = {\n", "  name: 'chatanol1-11',\n", "  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-11/global_step250/',\n", "  model_type: 'EMBEDDING',\n", "  model_arch: {\n", "    arch_type: 'STARCODER',\n", "    num_layers: 24,\n", "    vocab_size: 51200,\n", "    emb_dim: 2048,\n", "    num_heads: 16,\n", "    head_dim: 128,\n", "    norm_eps: 1e-5,\n", "  },\n", "  embedding: {\n", "    tokenizer_name: 'rogue',\n", "    query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',\n", "    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',\n", "  },\n", "  round_sizes: [256, 512, 1024, 2048],\n", "};\n", "modelDeployment.embeddingModelConfig(\n", "  modelConfig=modelConfig { name: name },\n", "  transformationKey='dr-%s-30line-1024char' % (std.ascii<PERSON>ower(name)),\n", "  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,\n", ")\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/chatanol1-18-hybrid_config.jsonnet`:\n", "\n", "```\n", "local constants = import 'services/deploy/constants.jsonnet';\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "\n", "local name = 'chatanol1-18-hybrid';\n", "local modelConfig = {\n", "  name: 'chatanol1-18-hybrid',\n", "  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-18.hybrid/global_step1468',\n", "  model_type: 'EMBEDDING',\n", "  model_arch: {\n", "    arch_type: 'STARCODER',\n", "    num_layers: 24,\n", "    vocab_size: 51200,\n", "    emb_dim: 2048,\n", "    num_heads: 16,\n", "    head_dim: 128,\n", "    norm_eps: 1e-5,\n", "  },\n", "  embedding: {\n", "    tokenizer_name: 'rogue',\n", "    query_prompt_formatter_name: 'chatanol6',\n", "    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',\n", "  },\n", "  round_sizes: [256, 512, 1024, 2048],\n", "};\n", "modelDeployment.embeddingModelConfig(\n", "  modelConfig=modelConfig { name: name },\n", "  transformationKey='dr-%s-30line-1024char' % (std.ascii<PERSON>ower(name)),\n", "  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,\n", ")\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/pyarrow/pipelines/the-stack.2023-02-04.retrieval/cfg.py`:\n", "\n", "```\n", "...\n", "\n", "@dataclass(frozen=True)\n", "class RetrievalConfig:\n", "    \\\"\\\"\\\"Configuration for retrieval.\\\"\\\"\\\"\n", "\n", "    # Probability a slice is chosen for the working set.\n", "    prob_cache: float = 0.9\n", "    # Probability a prompt is transformed for fim\n", "    prob_fim: float = 0.5\n", "    # Max number of tokens for a file path\n", "    max_path: int = 20\n", "    # Min number of tokens in a chunk for retrieval, excluding path\n", "    min_chunk: int = 64\n", "    # Max number of tokens in a chunk for retrieval, including path\n", "    max_chunk: int = 256  # inclusive\n", "    # Min number of tokens chosen for the prompt. The max number is\n", "    # the sample size (seq_length)\n", "    min_prompt: int = 64\n", "    # Ngram size to use for performing jaccard similarity.\n", "    ngram: int = 20\n", "    # Similarity threshold at which retrieved documents are considered\n", "    # to overlap with the prompt.\n", "    jaccard_threshold: float = 0.5\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/chatanol1-16-3_config.jsonnet`:\n", "\n", "```\n", "local constants = import 'services/deploy/constants.jsonnet';\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "\n", "local name = 'chatanol1-16-3';\n", "local modelConfig = {\n", "  name: 'chatanol1-16-3',\n", "  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-16-3/global_step1069/',\n", "  model_type: 'EMBEDDING',\n", "  model_arch: {\n", "    arch_type: 'STARCODER',\n", "    num_layers: 24,\n", "    vocab_size: 51200,\n", "    emb_dim: 2048,\n", "    num_heads: 16,\n", "    head_dim: 128,\n", "    norm_eps: 1e-5,\n", "  },\n", "  embedding: {\n", "    tokenizer_name: 'rogue',\n", "    query_prompt_formatter_name: 'chatanol6',\n", "    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',\n", "  },\n", "  round_sizes: [256, 512, 1024, 2048],\n", "};\n", "modelDeployment.embeddingModelConfig(\n", "  modelConfig=modelConfig { name: name },\n", "  transformationKey='dr-%s-30line-1024char' % (std.ascii<PERSON>ower(name)),\n", "  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,\n", ")\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/vzhao/airflow/dags/ppl/ethanol_rag_data.py`:\n", "\n", "```\n", "...\n", "# fmt: off\n", "CONFIG = dict(\n", "    allowed_languages=[\"python\", \"go\", \"java\", \"javascript\", \"rust\", \"typescript\"],\n", "    random_seed=74912,\n", "\n", "    # Sampling Repositories.\n", "    repo_min_size=200000,\n", "    repo_max_size=5000000,\n", "    # limit_repos=35000,\n", "    limit_repos=100,\n", "    downsample_small=True,\n", "\n", "    # FIM sampler Configs.\n", "    every_n_lines=150,\n", "    max_problems_per_file=4,\n", "    max_prefix_chars=8000,\n", "    max_suffix_chars=8000,\n", "\n", "    # Retrieval Configs.\n", "    num_retrieved_chunks=40,\n", "    retriever={\n", "        \"name\": \"ethanol\",\n", "        \"chunker\": \"line_level\",\n", "        \"max_chunk\": 40,\n", "        \"max_query_lines\": 20,\n", "    },\n", "\n", "    # Prompt Formatter Configs.\n", "    max_prefix_tokens=1280,\n", "    max_suffix_tokens=768,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/binks_33B_FP8_v1_chat_deploy.jsonnet`:\n", "\n", "```\n", "local grpcLib = import 'deploy/common/grpc-lib.jsonnet';\n", "local embedderConfig = (import 'services/deploy/ethanol6_04_1_v3_config.jsonnet') {\n", "  modelConfig+: {\n", "    embedding+: {\n", "      query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',\n", "    },\n", "  },\n", "};\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "\n", "function(env, namespace, namespace_config, cloud, namespace_config, filter=null)\n", "  local name = 'binks-33B-FP8-v1-chat';\n", "  local modelConfig = {\n", "    name: name,\n", "    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/deepseek/fastforward/coder-33b-instruct-fp8',  // pragma: allowlist secret\n", "    model_type: 'CHAT',\n", "    inference: {\n", "      tokenizer_name: 'deepseek_coder_instruct',\n", "      prompt_formatter_name: 'binks',\n", "      token_apportionment: {\n", "        prefix_len: 1536,\n", "        suffix_len: 1024,\n", "        path_len: 256,\n", "        message_len: 512,\n", "        selected_code_len: 2560,  // Cannot coexist with prefix/suffix\n", "        chat_history_len: 2048,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/vzhao/airflow/dags/ppl/sample_ppl_data.py`:\n", "\n", "```\n", "...\n", "    limit_repos=100,\n", "    downsample_small=True,\n", "\n", "    # FIM sampler Configs.\n", "    every_n_lines=150,\n", "    max_problems_per_file=4,\n", "    max_prefix_chars=8000,\n", "    max_suffix_chars=8000,\n", "\n", "    # Retrieval Configs.\n", "    num_retrieved_chunks=40,\n", "    retriever={\n", "        \"name\": \"ethanol\",\n", "        \"chunker\": \"line_level\",\n", "        \"max_chunk\": 40,\n", "        \"max_query_lines\": 20,\n", "    },\n", "\n", "    # Prompt Formatter Configs.\n", "    max_prefix_tokens=1280,\n", "    max_suffix_tokens=768,\n", "    max_retrieved_chunk_tokens=-1,\n", "    max_prompt_tokens=3838,\n", "    max_target_tokens=256,\n", "    retrieval_dropout=0.0,\n", "    preamble=\"\",\n", "    prepend_path_to_retrieved=True,\n", "    # \"add_retrieval_after_context\": False,\n", "    add_retrieval_after_context=True,\n", "    only_truncate_true_prefix=True,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/embeddings_indexer/deploy_lib.jsonnet`:\n", "\n", "```\n", "...\n", "  local services = grpcLib.grpcService(appName=name, namespace=namespace);\n", "  local dynamicFeatureFlags = dynamicFeatureFlagsLib(env=env, namespace=namespace, appName=name, cloud=cloud);\n", "  local tokenExchangeEndpoint = endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace);\n", "  local config = {\n", "    port: 50051,\n", "    content_manager_endpoint: 'content-manager-svc:50051',\n", "    embedder_endpoint: '%s:50051' % embedderServiceName,\n", "    token_exchange_endpoint: tokenExchangeEndpoint,\n", "    tokenizer_name: modelConfig.embedding.tokenizer_name,\n", "    model_name: modelConfig.name,\n", "    chunking: std.get(modelConfig, 'chunking', default={\n", "      name: 'line_level',\n", "      config: {\n", "        max_lines_per_chunk: 30,\n", "        max_chunk_size: 1024,\n", "      },\n", "    }),\n", "    embedding_prompt_formatter_name: modelConfig.embedding.key_prompt_formatter_name,\n", "    transformation_key: <PERSON><PERSON><PERSON>,\n", "    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/completion_host/single_model_server/retriever_factory.py`:\n", "\n", "```\n", "...\n", "        client_options.GrpcClientOptions(load_balancing=\"headless\" in endpoint)\n", "    )\n", "    return RerankerClient.create_for_endpoint(\n", "        endpoint, credentials=credentials, options=options\n", "    )\n", "\n", "\n", "@dataclass_json\n", "@dataclass\n", "class DenseRetrievalConfig:\n", "    \\\"\\\"\\\"Dense retrieval configuration.\\\"\\\"\\\"\n", "\n", "    max_retrieval_results: int\n", "    \\\"\\\"\\\"The maximal number of chunks the retriever should retrieve.\\\"\\\"\\\"\n", "\n", "    cache_memory_limit_gb: int\n", "    \\\"\\\"\\\"Size of the retrieval chunk cache.\\\"\\\"\\\"\n", "\n", "    embedder_endpoint: str\n", "    \\\"\\\"\\\"Endpoint (host:port) of the embedder to use for Dense Retrieval.\\\"\\\"\\\"\n", "\n", "    embedder_mtls: bool\n", "    \\\"\\\"\\\"Whether to use mutual TLS for the embedder client.\\\"\\\"\\\"\n", "\n", "    embedder_client_ca_path: str\n", "    \\\"\\\"\\\"Path to the CA certificate for the embedder client.\\\"\\\"\\\"\n", "\n", "    embedder_client_cert_path: str\n", "    \\\"\\\"\\\"Path to the client certificate for the embedder client.\\\"\\\"\\\"\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/vzhao/eval/tasks/multiline/ethanol_plus/default_diffb1m_16b_alphal_fixtoken_12-7.yml`:\n", "\n", "```\n", "# Default Template to evaluate dense retriever.\n", "\n", "determined:\n", "  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml\n", "  name: diffb1m_16b, 6pos_32total_v0.1_filepath, 40LineChunk, repoeval_2-3lines\n", "  workspace: Dev\n", "  project: vzhao-eval\n", "import_modules: experimental.igor.systems.ethanol\n", "system:\n", "  name: basic_rag\n", "  model:\n", "    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken\n", "    name: rogue\n", "    prompt:\n", "      max_prefix_tokens: 1280\n", "      max_prompt_tokens: 3816\n", "      max_retrieved_chunk_tokens: -1\n", "      max_suffix_tokens: 768\n", "      # NOTE: Change this to control the max number of retrieved chunks in the prompt.\n", "      max_number_chunks: 32\n", "  generation_options:\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "    max_generated_tokens: 280\n", "  retriever:\n", "    scorer:\n", "      name: diff_boykin\n", "      checkpoint: null\n", "    chunker:\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/rag/notebooks/inspect-fim-data-v2.ipynb`:\n", "\n", "```\n", "...\n", "    \"    \\\"text\\\",\\n\",\n", "    \"    \\\"yaml\\\",\\n\",\n", "    \"    \\\"json\\\",\\n\",\n", "    \"    \\\"xml\\\",\\n\",\n", "    \"    \\\"jsonnet\\\",\\n\",\n", "    \"]\\n\",\n", "    \"RETRIEVAL_LANGUAGES = (\\n\",\n", "    \"    REPO_LANGUAGES + [\\\"sql\\\", \\\"markdown\\\"] + additional_retrieval_languages\\n\",\n", "    \")\\n\",\n", "    \"config_retrieval = rogue_stages.RogueRetrievalConfig(\\n\",\n", "    \"    repo_languages=REPO_LANGUAGES,\\n\",\n", "    \"    sample_languages=SAMPLE_LANGUAGES,\\n\",\n", "    \"    retrieval_languages=RETRIEVAL_LANGUAGES,\\n\",\n", "    \"    num_retrieved_chunks=40,\\n\",\n", "    \"    scorer_config={\\n\",\n", "    \"        \\\"name\\\": \\\"ethanol\\\",\\n\",\n", "    \"        \\\"checkpoint_path\\\": \\\"ethanol/ethanol6-04.1\\\",\\n\",\n", "    \"    },\\n\",\n", "    \"    chunker_config={\\n\",\n", "    \"        \\\"name\\\": \\\"line_level\\\",\\n\",\n", "    \"        \\\"max_lines_per_chunk\\\": 30,\\n\",\n", "    \"        \\\"include_scope_annotation\\\": False,\\n\",\n", "    \"    },\\n\",\n", "    \"    query_config={\\n\",\n", "    \"        \\\"name\\\": \\\"ethanol6_query\\\",\\n\",\n", "    \"        \\\"max_tokens\\\": 1023,\\n\",\n", "    \"        \\\"add_path\\\": True,\\n\",\n", "    \"    },\\n\",\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/rag/elden/20240701_elden.py`:\n", "\n", "```\n", "...\n", "    small_downsampled_probability: float\n", "    small_downsample_char_threshold: int\n", "    small_filter_char_threshold: int\n", "    random_seed: int\n", "    num_retrieved_chunks: int\n", "\n", "\n", "CONFIG = ConfigType(\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=500,\n", "    random_seed=74912,\n", "    num_retrieved_chunks=128,\n", ")\n", "\n", "# Config for StarEthanol. StarEthanol is used for line chunk retrieval.\n", "SETHANOL_CONFIG = dict(\n", "    chunker={\n", "        \"max_lines_per_chunk\": 40,\n", "        \"name\": \"line_level\",\n", "    },\n", "    document_formatter={\n", "        \"add_path\": True,\n", "        \"add_prefix\": <PERSON>als<PERSON>,\n", "        \"add_suffix\": False,\n", "        \"max_tokens\": 999,\n", "        \"name\": \"ethanol6_document\",\n", "    },\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/eval/configs/inficoder_eval_task.yml`:\n", "\n", "```\n", "system:\n", "  name: \"basic_rag\"\n", "  model:\n", "    name: \"fastbackward_deepseek_instruct\"\n", "    checkpoint_path: \"/mnt/efs/augment/checkpoints/yury/binks/binks_v2_50781/\"\n", "    model_parallel_size: 4\n", "    seq_length: 16384\n", "    override_prompt_formatter:\n", "      name: \"chat_template\"\n", "      template_file: \"/mnt/efs/augment/user/yuri/deepseek_instruct_with_retrieval_template.txt\"\n", "      tokenizer_name: \"deepseekcoderinstructtokenizer\"\n", "      lines_in_prefix_suffix: 0\n", "      lines_in_prefix_suffix_empty_selection: 75\n", "      last_n_turns_in_chat_history: 10\n", "  generation_options:\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "    max_generated_tokens: 1024\n", "  retriever:\n", "    scorer:\n", "      name: \"ethanol\"\n", "      checkpoint_path: \"ethanol/ethanol6-16.1\"\n", "    chunker:\n", "      name: \"line_level\"\n", "      max_lines_per_chunk: 30\n", "    query_formatter:\n", "      name: \"ethanol6_query_simple_chat\"\n", "      max_tokens: 1023\n", "      add_path: True\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/eval/configs/chat-eval-task.yml`:\n", "\n", "```\n", "system:\n", "  name: \"basic_rag\"\n", "  model:\n", "    name: \"fastbackward_deepseek_instruct\"\n", "    checkpoint_path: \"/mnt/efs/augment/checkpoints/yury/binks/binks_v2_50781/\"\n", "    model_parallel_size: 4\n", "    seq_length: 16384\n", "    override_prompt_formatter:\n", "      name: \"chat_template\"\n", "      template_file: \"/mnt/efs/augment/user/yuri/deepseek_instruct_with_retrieval_template.txt\"\n", "      tokenizer_name: \"deepseekcoderinstructtokenizer\"\n", "      lines_in_prefix_suffix: 0\n", "      lines_in_prefix_suffix_empty_selection: 75\n", "      last_n_turns_in_chat_history: 10\n", "  generation_options:\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "    max_generated_tokens: 1024\n", "  retriever:\n", "    scorer:\n", "      name: \"ethanol\"\n", "      checkpoint_path: \"ethanol/ethanol6-16.1\"\n", "    chunker:\n", "      name: \"line_level\"\n", "      max_lines_per_chunk: 30\n", "    query_formatter:\n", "      name: \"ethanol6_query_simple_chat\"\n", "      max_tokens: 1023\n", "      add_path: True\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/rag/exps/gen-unit-test-v2.py`:\n", "\n", "```\n", "...\n", "        every_n_lines=50,\n", "        max_problems_per_file=5,\n", "        small_downsampled_probability=0.1,\n", "        small_downsample_char_threshold=1500,\n", "        small_filter_char_threshold=50,\n", "        random_seed=104,\n", "    )\n", "    config_retrieval = rogue_stages.RogueRetrievalConfig(\n", "        repo_languages=REPO_LANGUAGES,\n", "        sample_languages=SAMPLE_LANGUAGES,\n", "        retrieval_languages=RETRIEVAL_LANGUAGES,\n", "        num_retrieved_chunks=40,\n", "        scorer_config={\n", "            \"name\": \"ethanol\",\n", "            \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "        },\n", "        chunker_config={\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 30,\n", "            \"include_scope_annotation\": <PERSON>als<PERSON>,\n", "        },\n", "        query_config={\n", "            \"name\": \"ethanol6_query\",\n", "            \"max_tokens\": 1023,\n", "            \"add_path\": True,\n", "        },\n", "        document_config={\n", "            \"name\": \"ethanol6_document\",\n", "            \"max_tokens\": 999,\n", "            \"add_path\": True,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/vzhao/eval/tasks/multiline/ethanol_plus/dffb1m_16b_ethonal_plus_v0.1.yml`:\n", "\n", "```\n", "...\n", "determined:\n", "  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml\n", "  name: diffb1m_16b, 6pos_32total_v0.1, <PERSON><PERSON><PERSON><PERSON>, 40<PERSON><PERSON><PERSON><PERSON><PERSON>, repoeval_2-3lines\n", "  workspace: Dev\n", "  project: vzhao-eval\n", "system:\n", "  name: basic_rag\n", "  model:\n", "    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken\n", "    name: rogue\n", "    prompt:\n", "      max_prefix_tokens: 1280\n", "      max_prompt_tokens: 3816\n", "      max_retrieved_chunk_tokens: -1\n", "      max_suffix_tokens: 768\n", "      # NOTE: Change this to control the max number of retrieved chunks in the prompt.\n", "      max_number_chunks: 32\n", "  generation_options:\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "    max_generated_tokens: 280\n", "  retriever:\n", "    scorer:\n", "      name: contrieve_350m\n", "      checkpoint: ethanol_plus/6pos_32total_v0.1\n", "    chunker:\n", "      name: line_level\n", "      max_lines_per_chunk: 40\n", "    query_formatter:\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/binks_1B_BF16_v1_chat_deploy.jsonnet`:\n", "\n", "```\n", "local grpcLib = import 'deploy/common/grpc-lib.jsonnet';\n", "local embedderConfig = (import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet') {\n", "  modelConfig+: {\n", "    embedding+: {\n", "      query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',\n", "    },\n", "  },\n", "};\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "\n", "function(env, namespace, namespace_config, cloud, namespace_config, filter=null)\n", "  local name = 'binks-1B-BF16-v1-chat';\n", "  local modelConfig = {\n", "    name: 'binks-v1-1B-FB16',\n", "    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/deepseek/fastforward/coder-1.3b-instruct',  // pragma: allowlist secret\n", "    model_type: 'CHAT',\n", "    inference: {\n", "      tokenizer_name: 'deepseek_coder_instruct',\n", "      prompt_formatter_name: 'binks',\n", "      token_apportionment: {\n", "        prefix_len: 1536,\n", "        suffix_len: 1024,\n", "        path_len: 256,\n", "        message_len: 512,\n", "        selected_code_len: 4096,\n", "        chat_history_len: 4096,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/binks_1B_BF16_v1_gpu2_chat_deploy.jsonnet`:\n", "\n", "```\n", "local grpcLib = import 'deploy/common/grpc-lib.jsonnet';\n", "local embedderConfig = (import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet') {\n", "  modelConfig+: {\n", "    embedding+: {\n", "      query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',\n", "    },\n", "  },\n", "};\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "\n", "function(env, namespace, namespace_config, cloud, namespace_config, filter=null)\n", "  local name = 'binks-1B-BF16-v1-gpu2-chat';\n", "  local modelConfig = {\n", "    name: 'binks-v1-1B-FB16-gpu2',\n", "    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/deepseek/fastforward/coder-1.3b-instruct',  // pragma: allowlist secret\n", "    model_type: 'CHAT',\n", "    inference: {\n", "      tokenizer_name: 'deepseek_coder_instruct',\n", "      prompt_formatter_name: 'binks',\n", "      token_apportionment: {\n", "        prefix_len: 1536,\n", "        suffix_len: 1024,\n", "        path_len: 256,\n", "        message_len: 512,\n", "        selected_code_len: 4096,\n", "        chat_history_len: 4096,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/starethanol6_16_1_proj512_config.jsonnet`:\n", "\n", "```\n", "local constants = import 'services/deploy/constants.jsonnet';\n", "local modelConfig = {\n", "  name: 'starethanol6-16-1-proj512',\n", "  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/',\n", "  model_type: 'EMBEDDING',\n", "  model_arch: {\n", "    arch_type: 'STARCODER',\n", "    num_layers: 24,\n", "    vocab_size: 51200,\n", "    emb_dim: 2048,\n", "    num_heads: 16,\n", "    head_dim: 128,\n", "    norm_eps: 1e-5,\n", "  },\n", "  embedding: {\n", "    tokenizer_name: 'rogue',\n", "    query_prompt_formatter_name: 'ethanol6.16.1-query-embedding',\n", "    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',\n", "  },\n", "  round_sizes: [256, 512, 1024, 2048],\n", "};\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "modelDeployment.embeddingModelConfig(\n", "  modelConfig=modelConfig,\n", "  // The v3 suffix is to make use re-index all blobs after fixing a series of bugs in\n", "  // the embedding model.\n", "  transformationKey='dr-%s-30line-1024char-v3' % (std.asciiLower(modelConfig.name)),\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/deploy/roguesl_v2_16b_fp8_ethanol6_04_1_deploy.jsonnet`:\n", "\n", "```\n", "// Rogue stateless caching 16b model finetuned on more languages and with dense dropout.\n", "local embedderConfig = import 'services/deploy/ethanol6_04_1_v3_config.jsonnet';\n", "local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';\n", "\n", "function(env, namespace, cloud, namespace_config, filter=null)\n", "  local modelConfig =\n", "    local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);\n", "    {\n", "      name: 'roguesl-v2-16b',\n", "      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/roguesl/16b_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/checkpoint_fp8.pth',  // pragma: allowlist secret\n", "      model_type: 'INFERENCE',\n", "      inference: {\n", "        tokenizer_name: 'rogue',\n", "        prompt_formatter_name: 'rogue_sl',\n", "        apportionment_config: {\n", "          max_content_len: 4 * 1024,\n", "          input_fraction: 0.5,\n", "          prefix_fraction: 0.75,\n", "          max_path_tokens: 50,\n", "        },\n", "        prompt_formatter_config: {\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25-scopechunk.yml`:\n", "\n", "```\n", "# Launch Commands:\n", "# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25-scopechunk.yml\n", "#\n", "\n", "system:\n", "  name: basic_rag\n", "  model:\n", "    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2\n", "    name: rogue\n", "    prompt:\n", "      max_prefix_tokens: 1024\n", "      max_suffix_tokens: 1024\n", "      max_retrieved_chunk_tokens: -1\n", "      max_prompt_tokens: 3816\n", "  generation_options:\n", "    max_generated_tokens: 280\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "  retriever:\n", "    name: bm25\n", "    chunker:\n", "      name: scope_aware\n", "      max_lines_per_chunk: 40\n", "      parse_errored_root: true\n", "    query_formatter:\n", "      name: simple_query\n", "      max_lines: 20\n", "    max_query_lines: 20\n", "  experimental:\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/spark/pipelines/configs/make_rag_samples.yaml`:\n", "\n", "```\n", "...\n", "    len_middle_max: 304  # Set so that we are guaranteed < 304 tokens\n", "    # Sample middles that end in middle of syntactic leaf for leaves above this length\n", "    min_leaf_len_for_insert: 100\n", "\n", "    # Char length of prefix/suffix saved for possible use in prompt\n", "    len_prefix_max: 8000  # 2000 tokens * 4 to be on the safe side\n", "    len_suffix_max: 4000  # 1000 tokens * 4 to be on the safe side\n", "\n", "    repo_min_size: null\n", "    repo_max_size: null\n", "\n", "    # Retrieval settings\n", "    chunker: scope_aware\n", "    retriever_name: bm25\n", "    num_retrieved_chunks: 25\n", "    random_seed: 74912\n", "\n", "\n", "spark:\n", "  app_name: \"MakeRagSamples\"\n", "  log_level: WARN\n", "  # master_url: \"k8s://https://k8s.ord1.coreweave.com:443\"\n", "  master_url: \"spark://michiel-dataprocess:7077\"\n", "  # TODO we're giving every executor lots of memory, see if we can reduce it\n", "  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)\n", "  #   + driver_mem + driver_mem_overhead\n", "\n", "  spark.executor.memory: \"10g\"\n", "  spark.executor.memoryOverhead: \"6g\"\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/michiel/configs/eval/old/multi23/ender_ethanol_2kconrdense.yml`:\n", "\n", "```\n", "#\n", "# This file contains an example of an evaluation config\n", "#\n", "\n", "# Sections:\n", "#   Systems - specify the system configuration to evaluate\n", "#   Tasks - specify the evaluation tasks for each system\n", "#   Podspec - overide the default podspec, if necessary\n", "#   Determined - name, workspace, project in the determined UI.\n", "\n", "system:\n", "  name: ender_sys\n", "  model:\n", "    name: ender_fastforward\n", "    model_path: ender/16b_multieth61m_notodo_noinline_5knosig\n", "    prompt:\n", "      max_prefix_tokens: 1280\n", "      max_suffix_tokens: 768\n", "      max_signature_tokens: 0\n", "      max_prompt_tokens: 5120\n", "      max_retrieved_chunk_tokens: -1\n", "  generation_options:\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "    max_generated_tokens: 280\n", "  dense_retriever:\n", "    scorer:\n", "      name: ethanol\n", "      checkpoint_path: ethanol/ethanol6-04.1\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/rag/elden/20240704_elden.py`:\n", "\n", "```\n", "...\n", "GLOBAL_TASK_INFO_URI = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/task_info\"\n", "\n", "\n", "SAMPLE_LANGUAGES = constants.SAMPLE_LANGUAGES\n", "RETRIEVAL_LANGUAGES = constants.RETRIEVAL_LANGUAGES\n", "\n", "\n", "# Uses `TypedDict` to satisfy type checker. However, it complicates the code as `CONFIG`\n", "# could simply be a normal `dict`. I don't find a way to satisfy the type checker\n", "# without using `TypedDict`.\n", "class ConfigType(TypedDict):\n", "    max_problems_per_file: int\n", "    small_downsampled_probability: float\n", "    small_downsample_char_threshold: int\n", "    small_filter_char_threshold: int\n", "    random_seed: int\n", "    num_retrieved_chunks: int\n", "\n", "\n", "CONFIG = ConfigType(\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=500,\n", "    random_seed=74912,\n", "    num_retrieved_chunks=128,\n", ")\n", "\n", "\n", "def fim_stage(category: str):\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25.yml`:\n", "\n", "```\n", "# Launch Commands:\n", "# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25.yml\n", "# - python research/eval/harness/launch_harness.py experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25.yml --output /mnt/efs/augment/user/dxy/results-2023-09 --prefix rogue-bm25\n", "#\n", "\n", "system:\n", "  name: basic_rag\n", "  model:\n", "    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2\n", "    name: rogue\n", "    prompt:\n", "      max_prefix_tokens: 1024\n", "      max_suffix_tokens: 1024\n", "      max_retrieved_chunk_tokens: -1\n", "      max_prompt_tokens: 3816\n", "  generation_options:\n", "    max_generated_tokens: 280\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "  retriever:\n", "    name: bm25\n", "    chunker:\n", "      name: line_level\n", "      max_lines_per_chunk: 40\n", "    query_formatter:\n", "      name: simple_query\n", "      max_lines: 20\n", "    max_query_lines: 20\n", "  experimental:\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/eval/tests/harness/test_hydra.yml`:\n", "\n", "```\n", "#\n", "# This file contains an example of an evaluation config\n", "#\n", "\n", "# Sections:\n", "#   Systems - specify the system configuration to evaluate\n", "#   Tasks - specify the evaluation tasks for each system\n", "#   Podspec - overide the default podspec, if necessary\n", "#   Determined - name, workspace, project in the determined UI.\n", "\n", "systems:\n", "  - name: basic_rag\n", "    model:\n", "      name: starcoderbase_1b\n", "      prompt:\n", "        max_prefix_tokens: 1024\n", "        max_suffix_tokens: 0\n", "        max_prompt_tokens: 1768\n", "        fill_to_context_window: True\n", "      # Model does validation of inputs so only want arguments the model recognizes\n", "    retriever:\n", "      name: bm25\n", "      chunker: line_level\n", "      max_chunk: 20\n", "    generation_options:\n", "      max_generated_tokens: 280\n", "    experimental:\n", "      remove_suffix: False\n", "\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/arun/pinocchio/ender-inline.yaml`:\n", "\n", "```\n", "#\n", "# This file contains an EXAMPLE of an evaluation config for pinocchio, and not the\n", "# recommended configuration.\n", "# PLEASE SEE https://www.notion.so/Pinocchio-Task-Request-Insight-db35e010a18c4c3493c410f70de9d639\n", "# for the latest and greatest configurations.\n", "#\n", "\n", "# Sections:\n", "#   System - specify the system configuration to evaluate\n", "#   Task - specify the evaluation tasks for each system\n", "#   Podspec - overide the default podspec, if necessary\n", "#   Determined - name, workspace, project in the determined UI.\n", "\n", "import_modules: experimental.igor.systems.ethanol\n", "system:\n", "  dense_retriever:\n", "    chunker:\n", "      max_lines_per_chunk: 30\n", "      name: line_level\n", "    document_formatter:\n", "      add_path: true\n", "      max_tokens: 999\n", "      name: ethanol6_document\n", "    query_formatter:\n", "      add_path: true\n", "      add_suffix: true\n", "      max_tokens: 1023\n", "      name: ethanol6_query\n", "      prefix_ratio: 0.9\n", "    scorer:\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/jeff/configs/cceval-starcoder1b-hf.yml`:\n", "\n", "```\n", "system:\n", "    name: basic_rag\n", "\n", "    experimental:\n", "      remove_suffix: false\n", "      retriever_top_k: 25\n", "      trim_on_dedent: false\n", "\n", "    model:\n", "        name: starcoderbase_1b_hf\n", "        prompt:\n", "            max_prefix_tokens: 2048\n", "            max_suffix_tokens: 2048\n", "            max_prompt_tokens: 8142\n", "            retrieval_layout_style: \"comment2\"\n", "            max_number_chunks: 5\n", "\n", "    retriever:\n", "        name: bm25\n", "        # name: null\n", "        chunker:\n", "            name: line_level\n", "            max_lines_per_chunk: 10\n", "        max_query_lines: 10\n", "\n", "    generation_options:\n", "        temperature: 0\n", "        top_k: 0\n", "        top_p: 0\n", "        max_generated_tokens: 50\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25-scopechunk.yml`:\n", "\n", "```\n", "# Launch Commands:\n", "# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25-scopechunk.yml\n", "#\n", "\n", "system:\n", "  name: basic_rag\n", "  model:\n", "    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2\n", "    name: rogue\n", "    prompt:\n", "      max_prefix_tokens: 1024\n", "      max_suffix_tokens: 1024\n", "      max_retrieved_chunk_tokens: -1\n", "      max_prompt_tokens: 3816\n", "  generation_options:\n", "    max_generated_tokens: 280\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "  retriever:\n", "    name: bm25\n", "    chunker:\n", "      name: scope_aware\n", "      max_lines_per_chunk: 40\n", "      parse_errored_root: true\n", "    query_formatter:\n", "      name: simple_query\n", "      max_lines: 20\n", "    max_query_lines: 20\n", "  experimental:\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/jeff/configs/cceval-starcoder1b-neox.yml`:\n", "\n", "```\n", "system:\n", "    name: basic_rag\n", "\n", "    experimental:\n", "      remove_suffix: false\n", "      retriever_top_k: 25\n", "      trim_on_dedent: false\n", "\n", "    model:\n", "        name: starcoderbase_1b\n", "        prompt:\n", "            max_prefix_tokens: 2048\n", "            max_suffix_tokens: 2048\n", "            max_prompt_tokens: 8142\n", "            retrieval_layout_style: \"comment2\"\n", "            max_number_chunks: 5\n", "\n", "    retriever:\n", "        name: bm25\n", "        # name: null\n", "        chunker:\n", "            name: line_level\n", "            max_lines_per_chunk: 10\n", "        max_query_lines: 10\n", "\n", "    generation_options:\n", "        temperature: 0\n", "        top_k: 0\n", "        top_p: 0\n", "        max_generated_tokens: 50\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/next_edits/eval_configs/run_evals_chunksize.py`:\n", "\n", "```\n", "...\n", "            \"name\": \"next_edit_location_query\",\n", "            \"tokenizer\": \"starcoder\",\n", "            \"diff_context_lines\": 15,\n", "        }\n", "    },\n", "}\n", "CHUNKING_CONFIG = {\n", "    \"lines30-0\": {\n", "        \"system.retriever.chunker\": {\n", "            \"max_lines_per_chunk\": 30,\n", "            \"overlap_lines\": 0,\n", "        }\n", "    },\n", "    \"lines30-5\": {\n", "        \"system.retriever.chunker\": {\n", "            \"max_lines_per_chunk\": 30,\n", "            \"overlap_lines\": 5,\n", "        }\n", "    },\n", "    \"lines60-10\": {\n", "        \"system.retriever.chunker\": {\n", "            \"max_lines_per_chunk\": 60,\n", "            \"overlap_lines\": 10,\n", "        }\n", "    },\n", "    \"lines90-15\": {\n", "        \"system.retriever.chunker\": {\n", "            \"max_lines_per_chunk\": 90,\n", "            \"overlap_lines\": 15,\n", "        }\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25.yml`:\n", "\n", "```\n", "# Launch Commands:\n", "# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25.yml\n", "#\n", "\n", "system:\n", "  name: basic_rag\n", "  model:\n", "    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2\n", "    name: rogue\n", "    prompt:\n", "      max_prefix_tokens: 1024\n", "      max_suffix_tokens: 1024\n", "      max_retrieved_chunk_tokens: -1\n", "      max_prompt_tokens: 3816\n", "  generation_options:\n", "    max_generated_tokens: 280\n", "    temperature: 0\n", "    top_k: 0\n", "    top_p: 0\n", "  retriever:\n", "    name: bm25\n", "    chunker:\n", "      name: line_level\n", "      max_lines_per_chunk: 40\n", "    query_formatter:\n", "      name: simple_query\n", "      max_lines: 20\n", "    max_query_lines: 20\n", "  experimental:\n", "    retriever_top_k: 256\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/rag/exps/gen-normal-v2.py`:\n", "\n", "```\n", "...\n", "        random_seed=104,\n", "    )\n", "    config_retrieval = rogue_stages.RogueRetrievalConfig(\n", "        repo_languages=REPO_LANGUAGES,\n", "        sample_languages=SAMPLE_LANGUAGES,\n", "        retrieval_languages=RETRIEVAL_LANGUAGES,\n", "        num_retrieved_chunks=40,\n", "        scorer_config={\n", "            \"name\": \"ethanol\",\n", "            \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "        },\n", "        chunker_config={\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 30,\n", "            \"include_scope_annotation\": <PERSON>als<PERSON>,\n", "        },\n", "        query_config={\n", "            \"name\": \"ethanol6_query\",\n", "            \"max_tokens\": 1023,\n", "            \"add_path\": True,\n", "        },\n", "        document_config={\n", "            \"name\": \"ethanol6_document\",\n", "            \"max_tokens\": 999,\n", "            \"add_path\": True,\n", "        },\n", "        random_seed=74912,\n", "    )\n", "    urls = {\n", "        \"raw_stack\": \"s3a://the-stack-processed/by-repo-3\",\n", "...\n", "```\n", "\n", "\n", "\"\"\",\n", "    },\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"\"\"\\\n", "Understood. I'll refer to the excerpts for context, and ignore them for general questions.\"\"\",\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
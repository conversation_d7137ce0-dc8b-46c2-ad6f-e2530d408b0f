import json
import time
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor

from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from experimental.zhuoran.prompt.reproducibility import aqa33 as sample

sample_name = "aqa33"
repitition_count = 100

system_prompt = sample.system_prompt
current_message = sample.current_message
chat_history = sample.chat_history

REGION = "us-east5"
PROJECT_ID = "augment-387916"
TEMPERAURE = 0
MAX_OUTPUT_TOKENS = 1024 * 8
BASE_CLAUDE_VERSION = 2

if BASE_CLAUDE_VERSION == 1:
    MODEL_NAME = "claude-3-5-sonnet@20240620"
    suffix = "_v1"
else:
    MODEL_NAME = "https://www.notion.so/Claude-reproducibility-report-external-17abba10175a80f9a9c5fa5feca1ec70"
    suffix = ""


vertex_ai_client = AnthropicVertexAiClient(
    project_id=PROJECT_ID,
    region=REGION,
    model_name=MODEL_NAME,
    temperature=TEMPERAURE,
    max_output_tokens=MAX_OUTPUT_TOKENS,
)


def run_claude(current_message, chat_history, system_message):
    response = vertex_ai_client.client.messages.create(
        model=MODEL_NAME,
        max_tokens=MAX_OUTPUT_TOKENS,
        messages=[
            *chat_history,
            {"role": "user", "content": current_message},
        ],
        system=system_message,
        temperature=TEMPERAURE,
    )
    return response.content[0].text, response


def run_claude_stream(current_message, chat_history, system_message):
    with vertex_ai_client.client.messages.stream(
        model=MODEL_NAME,
        max_tokens=MAX_OUTPUT_TOKENS,
        messages=[
            *chat_history,
            {"role": "user", "content": current_message},
        ],
        system=system_message,
        temperature=TEMPERAURE,
    ) as stream:
        for text in stream.text_stream:
            yield text


def collect_claude_stream(current_message, chat_history, system_message, sleep_time=0):
    response = "".join(run_claude_stream(current_message, chat_history, system_message))
    if sleep_time:
        time.sleep(sleep_time)
    return response, None


print("Hello world test:")
print(
    collect_claude_stream(
        "Write a hello world in Python. Return a code block only.", [], ""
    )
)


class Collector:
    def __init__(self, sample_name, label, suffix):
        self.sample_name = sample_name
        self.label = label
        self.suffix = suffix
        self.unique_texts = []
        self.results = []

    @property
    def result_path(self):
        return f"/mnt/efs/augment/user/zhuoran/prompt/reproducibility/{self.sample_name}_{self.label}{self.suffix}.json"

    def collect(self, start_time, text, response):
        if text not in self.unique_texts:
            self.unique_texts.append(text)
        text_version = next(
            (j for j, t in enumerate(self.unique_texts) if t == text), None
        )
        self.results.append((start_time, text, response, text_version))
        print(
            f"{len(self.unique_texts)} unique texts for {len(self.results)} runs, current version {text_version}"
        )
        with open(self.result_path, "w") as f:
            results_to_save = [
                {"start_time": start_time, "text": text, "text_version": text_version}
                for start_time, text, _, text_version in self.results
            ]
            json.dump(results_to_save, f, indent=4)


parallel = False
executor = ThreadPoolExecutor(max_workers=repitition_count if parallel else 1)
results = []
for i in range(repitition_count):
    start_time = time.time()
    future = executor.submit(
        collect_claude_stream,
        current_message,
        chat_history,
        system_prompt,
        sleep_time=10,
    )
    results.append((start_time, future))

collector = Collector(sample_name, "stream", suffix + "_r1")
for start_time, maybe_future in results:
    text, response = maybe_future.result()
    collector.collect(start_time, text, response)
print(f"Saved to {collector.result_path}")

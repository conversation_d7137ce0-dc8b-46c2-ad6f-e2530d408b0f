system_prompt = """\
You are Aug<PERSON>, an AI code assistant developed by Augment Code, based on the Claude model created by <PERSON><PERSON><PERSON>.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.
Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.
- When referencing a file in your response, always include the FULL file path.
- When referencing classes, functions, variables or files in your response, always wrap them in backticks (``).
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.
- At the end of every answer, you should write your 2 best guesses of what user will ask next. Enclose them in:
<guess_of_next_user_question>
    <next_user_question>...</next_user_question>
    <next_user_question>...</next_user_question>
</guess_of_next_user_question>

MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:

1. Excerpts from existing files: Always include both `path=` and `mode="EXCERPT"`. Example:

<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
```python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
```
</augment_code_snippet>

2. Proposed edits: Always include `path=` and use `mode="EDIT"`. Example:

<augment_code_snippet path="config/app_config.yaml" mode="EDIT">
```yaml
app:
  name: MyWebApp
  version: 1.3.0

database:
  host: new-db.example.com
  port: 5432
```
</augment_code_snippet>

3. New code or text: Always include `path=` and use `mode="EDIT"`. Example:

<augment_code_snippet path="hello/world.rb" mode="EDIT">
```ruby
def main
  puts "Hello, world!"
end
```
</augment_code_snippet>
"""
current_message = """\
Where is the number retrieved chunks we ask the embeddings search for configured in jsonnet"""
chat_history = [
    {
        "role": "user",
        "content": """\
Below are some relevant files from my project.

Here is an excerpt from the file `services/deploy/methanol_0416.4_config.jsonnet`:

```
local constants = import 'services/deploy/constants.jsonnet';
local modelConfig = {
  name: 'methanol-0416-4',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/methanol/methanol_0416.4_1250/global_step1250/',
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'ethanol6.16.1-query-embedding',
    key_prompt_formatter_name: 'passthrough-document',
  },
  chunking: {
    name: 'signature',
    config: {},
  },
  round_sizes: [256, 512, 1024, 2048],
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig,
  transformationKey='dr-%s-1024char' % (std.asciiLower(modelConfig.name)),
  chunkOrigin=constants.chunkOrigin.DENSE_SIGNATURE,
...
```

Here is an excerpt from the file `services/deploy/chatanol1-11_config.jsonnet`:

```
local constants = import 'services/deploy/constants.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local name = 'chatanol1-11';
local modelConfig = {
  name: 'chatanol1-11',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-11/global_step250/',
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  round_sizes: [256, 512, 1024, 2048],
};
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig { name: name },
  transformationKey='dr-%s-30line-1024char' % (std.asciiLower(name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)
...
```

Here is an excerpt from the file `services/deploy/chatanol1-18-hybrid_config.jsonnet`:

```
local constants = import 'services/deploy/constants.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local name = 'chatanol1-18-hybrid';
local modelConfig = {
  name: 'chatanol1-18-hybrid',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-18.hybrid/global_step1468',
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'chatanol6',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  round_sizes: [256, 512, 1024, 2048],
};
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig { name: name },
  transformationKey='dr-%s-30line-1024char' % (std.asciiLower(name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)
...
```

Here is an excerpt from the file `research/data/pyarrow/pipelines/the-stack.2023-02-04.retrieval/cfg.py`:

```
...

@dataclass(frozen=True)
class RetrievalConfig:
    \"\"\"Configuration for retrieval.\"\"\"

    # Probability a slice is chosen for the working set.
    prob_cache: float = 0.9
    # Probability a prompt is transformed for fim
    prob_fim: float = 0.5
    # Max number of tokens for a file path
    max_path: int = 20
    # Min number of tokens in a chunk for retrieval, excluding path
    min_chunk: int = 64
    # Max number of tokens in a chunk for retrieval, including path
    max_chunk: int = 256  # inclusive
    # Min number of tokens chosen for the prompt. The max number is
    # the sample size (seq_length)
    min_prompt: int = 64
    # Ngram size to use for performing jaccard similarity.
    ngram: int = 20
    # Similarity threshold at which retrieved documents are considered
    # to overlap with the prompt.
    jaccard_threshold: float = 0.5
...
```

Here is an excerpt from the file `services/deploy/chatanol1-16-3_config.jsonnet`:

```
local constants = import 'services/deploy/constants.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local name = 'chatanol1-16-3';
local modelConfig = {
  name: 'chatanol1-16-3',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-16-3/global_step1069/',
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'chatanol6',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  round_sizes: [256, 512, 1024, 2048],
};
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig { name: name },
  transformationKey='dr-%s-30line-1024char' % (std.asciiLower(name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)
...
```

Here is an excerpt from the file `experimental/vzhao/airflow/dags/ppl/ethanol_rag_data.py`:

```
...
# fmt: off
CONFIG = dict(
    allowed_languages=["python", "go", "java", "javascript", "rust", "typescript"],
    random_seed=74912,

    # Sampling Repositories.
    repo_min_size=200000,
    repo_max_size=5000000,
    # limit_repos=35000,
    limit_repos=100,
    downsample_small=True,

    # FIM sampler Configs.
    every_n_lines=150,
    max_problems_per_file=4,
    max_prefix_chars=8000,
    max_suffix_chars=8000,

    # Retrieval Configs.
    num_retrieved_chunks=40,
    retriever={
        "name": "ethanol",
        "chunker": "line_level",
        "max_chunk": 40,
        "max_query_lines": 20,
    },

    # Prompt Formatter Configs.
    max_prefix_tokens=1280,
    max_suffix_tokens=768,
...
```

Here is an excerpt from the file `services/deploy/binks_33B_FP8_v1_chat_deploy.jsonnet`:

```
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local embedderConfig = (import 'services/deploy/ethanol6_04_1_v3_config.jsonnet') {
  modelConfig+: {
    embedding+: {
      query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',
    },
  },
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config, filter=null)
  local name = 'binks-33B-FP8-v1-chat';
  local modelConfig = {
    name: name,
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/deepseek/fastforward/coder-33b-instruct-fp8',  // pragma: allowlist secret
    model_type: 'CHAT',
    inference: {
      tokenizer_name: 'deepseek_coder_instruct',
      prompt_formatter_name: 'binks',
      token_apportionment: {
        prefix_len: 1536,
        suffix_len: 1024,
        path_len: 256,
        message_len: 512,
        selected_code_len: 2560,  // Cannot coexist with prefix/suffix
        chat_history_len: 2048,
...
```

Here is an excerpt from the file `experimental/vzhao/airflow/dags/ppl/sample_ppl_data.py`:

```
...
    limit_repos=100,
    downsample_small=True,

    # FIM sampler Configs.
    every_n_lines=150,
    max_problems_per_file=4,
    max_prefix_chars=8000,
    max_suffix_chars=8000,

    # Retrieval Configs.
    num_retrieved_chunks=40,
    retriever={
        "name": "ethanol",
        "chunker": "line_level",
        "max_chunk": 40,
        "max_query_lines": 20,
    },

    # Prompt Formatter Configs.
    max_prefix_tokens=1280,
    max_suffix_tokens=768,
    max_retrieved_chunk_tokens=-1,
    max_prompt_tokens=3838,
    max_target_tokens=256,
    retrieval_dropout=0.0,
    preamble="",
    prepend_path_to_retrieved=True,
    # "add_retrieval_after_context": False,
    add_retrieval_after_context=True,
    only_truncate_true_prefix=True,
...
```

Here is an excerpt from the file `services/embeddings_indexer/deploy_lib.jsonnet`:

```
...
  local services = grpcLib.grpcService(appName=name, namespace=namespace);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib(env=env, namespace=namespace, appName=name, cloud=cloud);
  local tokenExchangeEndpoint = endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace);
  local config = {
    port: 50051,
    content_manager_endpoint: 'content-manager-svc:50051',
    embedder_endpoint: '%s:50051' % embedderServiceName,
    token_exchange_endpoint: tokenExchangeEndpoint,
    tokenizer_name: modelConfig.embedding.tokenizer_name,
    model_name: modelConfig.name,
    chunking: std.get(modelConfig, 'chunking', default={
      name: 'line_level',
      config: {
        max_lines_per_chunk: 30,
        max_chunk_size: 1024,
      },
    }),
    embedding_prompt_formatter_name: modelConfig.embedding.key_prompt_formatter_name,
    transformation_key: transformationKey,
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
...
```

Here is an excerpt from the file `services/completion_host/single_model_server/retriever_factory.py`:

```
...
        client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
    )
    return RerankerClient.create_for_endpoint(
        endpoint, credentials=credentials, options=options
    )


@dataclass_json
@dataclass
class DenseRetrievalConfig:
    \"\"\"Dense retrieval configuration.\"\"\"

    max_retrieval_results: int
    \"\"\"The maximal number of chunks the retriever should retrieve.\"\"\"

    cache_memory_limit_gb: int
    \"\"\"Size of the retrieval chunk cache.\"\"\"

    embedder_endpoint: str
    \"\"\"Endpoint (host:port) of the embedder to use for Dense Retrieval.\"\"\"

    embedder_mtls: bool
    \"\"\"Whether to use mutual TLS for the embedder client.\"\"\"

    embedder_client_ca_path: str
    \"\"\"Path to the CA certificate for the embedder client.\"\"\"

    embedder_client_cert_path: str
    \"\"\"Path to the client certificate for the embedder client.\"\"\"

...
```

Here is an excerpt from the file `experimental/vzhao/eval/tasks/multiline/ethanol_plus/default_diffb1m_16b_alphal_fixtoken_12-7.yml`:

```
# Default Template to evaluate dense retriever.

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b, 6pos_32total_v0.1_filepath, 40LineChunk, repoeval_2-3lines
  workspace: Dev
  project: vzhao-eval
import_modules: experimental.igor.systems.ethanol
system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: diff_boykin
      checkpoint: null
    chunker:
...
```

Here is an excerpt from the file `experimental/dxy/rag/notebooks/inspect-fim-data-v2.ipynb`:

```
...
    "    \"text\",\n",
    "    \"yaml\",\n",
    "    \"json\",\n",
    "    \"xml\",\n",
    "    \"jsonnet\",\n",
    "]\n",
    "RETRIEVAL_LANGUAGES = (\n",
    "    REPO_LANGUAGES + [\"sql\", \"markdown\"] + additional_retrieval_languages\n",
    ")\n",
    "config_retrieval = rogue_stages.RogueRetrievalConfig(\n",
    "    repo_languages=REPO_LANGUAGES,\n",
    "    sample_languages=SAMPLE_LANGUAGES,\n",
    "    retrieval_languages=RETRIEVAL_LANGUAGES,\n",
    "    num_retrieved_chunks=40,\n",
    "    scorer_config={\n",
    "        \"name\": \"ethanol\",\n",
    "        \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n",
    "    },\n",
    "    chunker_config={\n",
    "        \"name\": \"line_level\",\n",
    "        \"max_lines_per_chunk\": 30,\n",
    "        \"include_scope_annotation\": False,\n",
    "    },\n",
    "    query_config={\n",
    "        \"name\": \"ethanol6_query\",\n",
    "        \"max_tokens\": 1023,\n",
    "        \"add_path\": True,\n",
    "    },\n",
...
```

Here is an excerpt from the file `research/data/rag/elden/20240701_elden.py`:

```
...
    small_downsampled_probability: float
    small_downsample_char_threshold: int
    small_filter_char_threshold: int
    random_seed: int
    num_retrieved_chunks: int


CONFIG = ConfigType(
    every_n_lines=200,
    max_problems_per_file=5,
    small_downsampled_probability=0.1,
    small_downsample_char_threshold=1500,
    small_filter_char_threshold=500,
    random_seed=74912,
    num_retrieved_chunks=128,
)

# Config for StarEthanol. StarEthanol is used for line chunk retrieval.
SETHANOL_CONFIG = dict(
    chunker={
        "max_lines_per_chunk": 40,
        "name": "line_level",
    },
    document_formatter={
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "max_tokens": 999,
        "name": "ethanol6_document",
    },
...
```

Here is an excerpt from the file `research/eval/configs/inficoder_eval_task.yml`:

```
system:
  name: "basic_rag"
  model:
    name: "fastbackward_deepseek_instruct"
    checkpoint_path: "/mnt/efs/augment/checkpoints/yury/binks/binks_v2_50781/"
    model_parallel_size: 4
    seq_length: 16384
    override_prompt_formatter:
      name: "chat_template"
      template_file: "/mnt/efs/augment/user/yuri/deepseek_instruct_with_retrieval_template.txt"
      tokenizer_name: "deepseekcoderinstructtokenizer"
      lines_in_prefix_suffix: 0
      lines_in_prefix_suffix_empty_selection: 75
      last_n_turns_in_chat_history: 10
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024
  retriever:
    scorer:
      name: "ethanol"
      checkpoint_path: "ethanol/ethanol6-16.1"
    chunker:
      name: "line_level"
      max_lines_per_chunk: 30
    query_formatter:
      name: "ethanol6_query_simple_chat"
      max_tokens: 1023
      add_path: True
...
```

Here is an excerpt from the file `research/eval/configs/chat-eval-task.yml`:

```
system:
  name: "basic_rag"
  model:
    name: "fastbackward_deepseek_instruct"
    checkpoint_path: "/mnt/efs/augment/checkpoints/yury/binks/binks_v2_50781/"
    model_parallel_size: 4
    seq_length: 16384
    override_prompt_formatter:
      name: "chat_template"
      template_file: "/mnt/efs/augment/user/yuri/deepseek_instruct_with_retrieval_template.txt"
      tokenizer_name: "deepseekcoderinstructtokenizer"
      lines_in_prefix_suffix: 0
      lines_in_prefix_suffix_empty_selection: 75
      last_n_turns_in_chat_history: 10
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024
  retriever:
    scorer:
      name: "ethanol"
      checkpoint_path: "ethanol/ethanol6-16.1"
    chunker:
      name: "line_level"
      max_lines_per_chunk: 30
    query_formatter:
      name: "ethanol6_query_simple_chat"
      max_tokens: 1023
      add_path: True
...
```

Here is an excerpt from the file `experimental/dxy/rag/exps/gen-unit-test-v2.py`:

```
...
        every_n_lines=50,
        max_problems_per_file=5,
        small_downsampled_probability=0.1,
        small_downsample_char_threshold=1500,
        small_filter_char_threshold=50,
        random_seed=104,
    )
    config_retrieval = rogue_stages.RogueRetrievalConfig(
        repo_languages=REPO_LANGUAGES,
        sample_languages=SAMPLE_LANGUAGES,
        retrieval_languages=RETRIEVAL_LANGUAGES,
        num_retrieved_chunks=40,
        scorer_config={
            "name": "ethanol",
            "checkpoint_path": "ethanol/ethanol6-04.1",
        },
        chunker_config={
            "name": "line_level",
            "max_lines_per_chunk": 30,
            "include_scope_annotation": False,
        },
        query_config={
            "name": "ethanol6_query",
            "max_tokens": 1023,
            "add_path": True,
        },
        document_config={
            "name": "ethanol6_document",
            "max_tokens": 999,
            "add_path": True,
...
```

Here is an excerpt from the file `experimental/vzhao/eval/tasks/multiline/ethanol_plus/dffb1m_16b_ethonal_plus_v0.1.yml`:

```
...
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: diffb1m_16b, 6pos_32total_v0.1, LongQuery, 40LineChunk, repoeval_2-3lines
  workspace: Dev
  project: vzhao-eval
system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
      # NOTE: Change this to control the max number of retrieved chunks in the prompt.
      max_number_chunks: 32
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: contrieve_350m
      checkpoint: ethanol_plus/6pos_32total_v0.1
    chunker:
      name: line_level
      max_lines_per_chunk: 40
    query_formatter:
...
```

Here is an excerpt from the file `services/deploy/binks_1B_BF16_v1_chat_deploy.jsonnet`:

```
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local embedderConfig = (import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet') {
  modelConfig+: {
    embedding+: {
      query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',
    },
  },
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config, filter=null)
  local name = 'binks-1B-BF16-v1-chat';
  local modelConfig = {
    name: 'binks-v1-1B-FB16',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/deepseek/fastforward/coder-1.3b-instruct',  // pragma: allowlist secret
    model_type: 'CHAT',
    inference: {
      tokenizer_name: 'deepseek_coder_instruct',
      prompt_formatter_name: 'binks',
      token_apportionment: {
        prefix_len: 1536,
        suffix_len: 1024,
        path_len: 256,
        message_len: 512,
        selected_code_len: 4096,
        chat_history_len: 4096,
...
```

Here is an excerpt from the file `services/deploy/binks_1B_BF16_v1_gpu2_chat_deploy.jsonnet`:

```
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local embedderConfig = (import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet') {
  modelConfig+: {
    embedding+: {
      query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',
    },
  },
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config, filter=null)
  local name = 'binks-1B-BF16-v1-gpu2-chat';
  local modelConfig = {
    name: 'binks-v1-1B-FB16-gpu2',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/deepseek/fastforward/coder-1.3b-instruct',  // pragma: allowlist secret
    model_type: 'CHAT',
    inference: {
      tokenizer_name: 'deepseek_coder_instruct',
      prompt_formatter_name: 'binks',
      token_apportionment: {
        prefix_len: 1536,
        suffix_len: 1024,
        path_len: 256,
        message_len: 512,
        selected_code_len: 4096,
        chat_history_len: 4096,
...
```

Here is an excerpt from the file `services/deploy/starethanol6_16_1_proj512_config.jsonnet`:

```
local constants = import 'services/deploy/constants.jsonnet';
local modelConfig = {
  name: 'starethanol6-16-1-proj512',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/',
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'ethanol6.16.1-query-embedding',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  round_sizes: [256, 512, 1024, 2048],
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig,
  // The v3 suffix is to make use re-index all blobs after fixing a series of bugs in
  // the embedding model.
  transformationKey='dr-%s-30line-1024char-v3' % (std.asciiLower(modelConfig.name)),
...
```

Here is an excerpt from the file `services/deploy/roguesl_v2_16b_fp8_ethanol6_04_1_deploy.jsonnet`:

```
// Rogue stateless caching 16b model finetuned on more languages and with dense dropout.
local embedderConfig = import 'services/deploy/ethanol6_04_1_v3_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

function(env, namespace, cloud, namespace_config, filter=null)
  local modelConfig =
    local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
    {
      name: 'roguesl-v2-16b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/roguesl/16b_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/checkpoint_fp8.pth',  // pragma: allowlist secret
      model_type: 'INFERENCE',
      inference: {
        tokenizer_name: 'rogue',
        prompt_formatter_name: 'rogue_sl',
        apportionment_config: {
          max_content_len: 4 * 1024,
          input_fraction: 0.5,
          prefix_fraction: 0.75,
          max_path_tokens: 50,
        },
        prompt_formatter_config: {
...
```

Here is an excerpt from the file `experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25-scopechunk.yml`:

```
# Launch Commands:
# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25-scopechunk.yml
#

system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
    name: rogue
    prompt:
      max_prefix_tokens: 1024
      max_suffix_tokens: 1024
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: bm25
    chunker:
      name: scope_aware
      max_lines_per_chunk: 40
      parse_errored_root: true
    query_formatter:
      name: simple_query
      max_lines: 20
    max_query_lines: 20
  experimental:
...
```

Here is an excerpt from the file `research/data/spark/pipelines/configs/make_rag_samples.yaml`:

```
...
    len_middle_max: 304  # Set so that we are guaranteed < 304 tokens
    # Sample middles that end in middle of syntactic leaf for leaves above this length
    min_leaf_len_for_insert: 100

    # Char length of prefix/suffix saved for possible use in prompt
    len_prefix_max: 8000  # 2000 tokens * 4 to be on the safe side
    len_suffix_max: 4000  # 1000 tokens * 4 to be on the safe side

    repo_min_size: null
    repo_max_size: null

    # Retrieval settings
    chunker: scope_aware
    retriever_name: bm25
    num_retrieved_chunks: 25
    random_seed: 74912


spark:
  app_name: "MakeRagSamples"
  log_level: WARN
  # master_url: "k8s://https://k8s.ord1.coreweave.com:443"
  master_url: "spark://michiel-dataprocess:7077"
  # TODO we're giving every executor lots of memory, see if we can reduce it
  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead

  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
...
```

Here is an excerpt from the file `experimental/michiel/configs/eval/old/multi23/ender_ethanol_2kconrdense.yml`:

```
#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

system:
  name: ender_sys
  model:
    name: ender_fastforward
    model_path: ender/16b_multieth61m_notodo_noinline_5knosig
    prompt:
      max_prefix_tokens: 1280
      max_suffix_tokens: 768
      max_signature_tokens: 0
      max_prompt_tokens: 5120
      max_retrieved_chunk_tokens: -1
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  dense_retriever:
    scorer:
      name: ethanol
      checkpoint_path: ethanol/ethanol6-04.1
...
```

Here is an excerpt from the file `research/data/rag/elden/20240704_elden.py`:

```
...
GLOBAL_TASK_INFO_URI = "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/task_info"


SAMPLE_LANGUAGES = constants.SAMPLE_LANGUAGES
RETRIEVAL_LANGUAGES = constants.RETRIEVAL_LANGUAGES


# Uses `TypedDict` to satisfy type checker. However, it complicates the code as `CONFIG`
# could simply be a normal `dict`. I don't find a way to satisfy the type checker
# without using `TypedDict`.
class ConfigType(TypedDict):
    max_problems_per_file: int
    small_downsampled_probability: float
    small_downsample_char_threshold: int
    small_filter_char_threshold: int
    random_seed: int
    num_retrieved_chunks: int


CONFIG = ConfigType(
    max_problems_per_file=5,
    small_downsampled_probability=0.1,
    small_downsample_char_threshold=1500,
    small_filter_char_threshold=500,
    random_seed=74912,
    num_retrieved_chunks=128,
)


def fim_stage(category: str):
...
```

Here is an excerpt from the file `experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25.yml`:

```
# Launch Commands:
# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25.yml
# - python research/eval/harness/launch_harness.py experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25.yml --output /mnt/efs/augment/user/dxy/results-2023-09 --prefix rogue-bm25
#

system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
    name: rogue
    prompt:
      max_prefix_tokens: 1024
      max_suffix_tokens: 1024
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: bm25
    chunker:
      name: line_level
      max_lines_per_chunk: 40
    query_formatter:
      name: simple_query
      max_lines: 20
    max_query_lines: 20
  experimental:
...
```

Here is an excerpt from the file `research/eval/tests/harness/test_hydra.yml`:

```
#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      name: starcoderbase_1b
      prompt:
        max_prefix_tokens: 1024
        max_suffix_tokens: 0
        max_prompt_tokens: 1768
        fill_to_context_window: True
      # Model does validation of inputs so only want arguments the model recognizes
    retriever:
      name: bm25
      chunker: line_level
      max_chunk: 20
    generation_options:
      max_generated_tokens: 280
    experimental:
      remove_suffix: False


...
```

Here is an excerpt from the file `experimental/arun/pinocchio/ender-inline.yaml`:

```
#
# This file contains an EXAMPLE of an evaluation config for pinocchio, and not the
# recommended configuration.
# PLEASE SEE https://www.notion.so/Pinocchio-Task-Request-Insight-db35e010a18c4c3493c410f70de9d639
# for the latest and greatest configurations.
#

# Sections:
#   System - specify the system configuration to evaluate
#   Task - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

import_modules: experimental.igor.systems.ethanol
system:
  dense_retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    query_formatter:
      add_path: true
      add_suffix: true
      max_tokens: 1023
      name: ethanol6_query
      prefix_ratio: 0.9
    scorer:
...
```

Here is an excerpt from the file `experimental/jeff/configs/cceval-starcoder1b-hf.yml`:

```
system:
    name: basic_rag

    experimental:
      remove_suffix: false
      retriever_top_k: 25
      trim_on_dedent: false

    model:
        name: starcoderbase_1b_hf
        prompt:
            max_prefix_tokens: 2048
            max_suffix_tokens: 2048
            max_prompt_tokens: 8142
            retrieval_layout_style: "comment2"
            max_number_chunks: 5

    retriever:
        name: bm25
        # name: null
        chunker:
            name: line_level
            max_lines_per_chunk: 10
        max_query_lines: 10

    generation_options:
        temperature: 0
        top_k: 0
        top_p: 0
        max_generated_tokens: 50
...
```

Here is an excerpt from the file `experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25-scopechunk.yml`:

```
# Launch Commands:
# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25-scopechunk.yml
#

system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
    name: rogue
    prompt:
      max_prefix_tokens: 1024
      max_suffix_tokens: 1024
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: bm25
    chunker:
      name: scope_aware
      max_lines_per_chunk: 40
      parse_errored_root: true
    query_formatter:
      name: simple_query
      max_lines: 20
    max_query_lines: 20
  experimental:
...
```

Here is an excerpt from the file `experimental/jeff/configs/cceval-starcoder1b-neox.yml`:

```
system:
    name: basic_rag

    experimental:
      remove_suffix: false
      retriever_top_k: 25
      trim_on_dedent: false

    model:
        name: starcoderbase_1b
        prompt:
            max_prefix_tokens: 2048
            max_suffix_tokens: 2048
            max_prompt_tokens: 8142
            retrieval_layout_style: "comment2"
            max_number_chunks: 5

    retriever:
        name: bm25
        # name: null
        chunker:
            name: line_level
            max_lines_per_chunk: 10
        max_query_lines: 10

    generation_options:
        temperature: 0
        top_k: 0
        top_p: 0
        max_generated_tokens: 50
...
```

Here is an excerpt from the file `experimental/next_edits/eval_configs/run_evals_chunksize.py`:

```
...
            "name": "next_edit_location_query",
            "tokenizer": "starcoder",
            "diff_context_lines": 15,
        }
    },
}
CHUNKING_CONFIG = {
    "lines30-0": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 30,
            "overlap_lines": 0,
        }
    },
    "lines30-5": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 30,
            "overlap_lines": 5,
        }
    },
    "lines60-10": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 60,
            "overlap_lines": 10,
        }
    },
    "lines90-15": {
        "system.retriever.chunker": {
            "max_lines_per_chunk": 90,
            "overlap_lines": 15,
        }
...
```

Here is an excerpt from the file `experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25.yml`:

```
# Launch Commands:
# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-fine-grained-2023-09-22/rogue-bm25.yml
#

system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
    name: rogue
    prompt:
      max_prefix_tokens: 1024
      max_suffix_tokens: 1024
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: bm25
    chunker:
      name: line_level
      max_lines_per_chunk: 40
    query_formatter:
      name: simple_query
      max_lines: 20
    max_query_lines: 20
  experimental:
    retriever_top_k: 256
...
```

Here is an excerpt from the file `experimental/dxy/rag/exps/gen-normal-v2.py`:

```
...
        random_seed=104,
    )
    config_retrieval = rogue_stages.RogueRetrievalConfig(
        repo_languages=REPO_LANGUAGES,
        sample_languages=SAMPLE_LANGUAGES,
        retrieval_languages=RETRIEVAL_LANGUAGES,
        num_retrieved_chunks=40,
        scorer_config={
            "name": "ethanol",
            "checkpoint_path": "ethanol/ethanol6-04.1",
        },
        chunker_config={
            "name": "line_level",
            "max_lines_per_chunk": 30,
            "include_scope_annotation": False,
        },
        query_config={
            "name": "ethanol6_query",
            "max_tokens": 1023,
            "add_path": True,
        },
        document_config={
            "name": "ethanol6_document",
            "max_tokens": 999,
            "add_path": True,
        },
        random_seed=74912,
    )
    urls = {
        "raw_stack": "s3a://the-stack-processed/by-repo-3",
...
```


""",
    },
    {
        "role": "assistant",
        "content": """\
Understood. I'll refer to the excerpts for context, and ignore them for general questions.""",
    },
]

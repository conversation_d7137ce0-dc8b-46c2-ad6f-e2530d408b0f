{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data loading"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import zstandard as zstd\n", "\n", "file_paths = {\n", "    \"claude_v7_r3_0\": \"/mnt/efs/augment/eval/jobs/WwmXRr5z/000__RemoteChatSystem_eezo/samples.jsonl.zst\",\n", "    \"claude_v7_r3_1\": \"/mnt/efs/augment/eval/jobs/7bzBtLub/000__RemoteChatSystem_aghs/samples.jsonl.zst\",\n", "    \"claude_v7_r3_2\": \"/mnt/efs/augment/eval/jobs/PQAcdNZp/000__RemoteChatSystem_i96v/samples.jsonl.zst\",\n", "    \"claude_v7_r3_3\": \"/mnt/efs/augment/eval/jobs/YvVvHVH6/000__RemoteChatSystem_rmpj/samples.jsonl.zst\",\n", "    \"claude_v7_r3_4\": \"/mnt/efs/augment/eval/jobs/mq89Cn2v/000__RemoteChatSystem_x2f3/samples.jsonl.zst\",\n", "}\n", "\n", "results = {}\n", "for label, file_path in file_paths.items():\n", "    with open(file_path, \"rb\") as fh:\n", "        dctx = zstd.ZstdDecompressor()\n", "        with dctx.stream_reader(fh) as reader:\n", "            text = reader.read().decode(\"utf-8\")\n", "\n", "    data = []\n", "    for line in text.splitlines():\n", "        if line.strip():  # Skip empty lines\n", "            data.append(json.loads(line))\n", "    results[label] = data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[\"claude_v7_r3_0\"][0][\"result\"][\"extra_output\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pprint import pprint\n", "\n", "\n", "def format_schema(schema, indent=0):\n", "    if isinstance(schema, dict):\n", "        result = \"{\\n\"\n", "        for i, (key, value) in enumerate(schema.items()):\n", "            result += \" \" * (indent + 4) + f\"'{key}': \"\n", "            result += format_schema(value, indent + 4)\n", "            if i < len(schema) - 1:\n", "                result += \",\"\n", "            result += \"\\n\"\n", "        result += \" \" * indent + \"}\"\n", "        return result\n", "    elif isinstance(schema, list):\n", "        return f\"[{format_schema(schema[0], indent)}]\"\n", "    else:\n", "        return f\"'{schema}'\"\n", "\n", "\n", "def get_schema(obj):\n", "    if isinstance(obj, dict):\n", "        return {key: get_schema(value) for key, value in obj.items()}\n", "    elif isinstance(obj, list) and obj:\n", "        return [get_schema(obj[0])]  # Assume all items in list have same schema\n", "    else:\n", "        return type(obj).__name__\n", "\n", "\n", "# Get and print the schema\n", "schema = get_schema(results[\"claude_v7_r3_0\"][0])\n", "print(format_schema(schema))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results[\"claude_v7_r3_0\"][0][\"result\"][\"prompt_tokens\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_generated_text(index, run_index):\n", "    return results[f\"claude_v7_r3_{run_index}\"][index][\"result\"][\"generated_text\"]\n", "\n", "\n", "def get_request_id(index, run_index):\n", "    return results[f\"claude_v7_r3_{run_index}\"][index][\"result\"][\"extra_output\"][\n", "        \"additional_info\"\n", "    ][\"request_id\"]\n", "\n", "\n", "def get_version_count(index):\n", "    return len(\n", "        set(\n", "            results[f\"claude_v7_r3_{run_index}\"][index][\"result\"][\"generated_text\"]\n", "            for run_index in range(5)\n", "        )\n", "    )\n", "\n", "\n", "get_generated_text(0, 0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "from collections import Counter\n", "import pandas as pd\n", "\n", "inconsistency_count = 0\n", "total_max_diff = 0\n", "table = []\n", "for index, sample_results in enumerate(zip(*results.values())):\n", "    generated_texts = Counter()\n", "    texts_list = []\n", "    row = {}\n", "    for run_index, sample_result in enumerate(sample_results):\n", "        generated_text = sample_result[\"result\"][\"generated_text\"]\n", "        generated_texts[generated_text] += 1\n", "        texts_list.append(generated_text)\n", "\n", "    row[\"version_count\"] = len(generated_texts)\n", "    if len(generated_texts) > 1:\n", "        inconsistency_count += 1\n", "        # Calculate maximum difference ratio between any pair of texts\n", "        max_diff = 0\n", "        max_diff_pair = None\n", "        for i in range(len(texts_list)):\n", "            for j in range(i + 1, len(texts_list)):\n", "                similarity = difflib.SequenceMatcher(\n", "                    None, texts_list[i], texts_list[j]\n", "                ).ratio()\n", "                diff = 1 - similarity\n", "                if diff > max_diff:\n", "                    max_diff = diff\n", "                    max_diff_pair = (i, j)\n", "        total_max_diff = max(total_max_diff, max_diff)\n", "        row[\"max_diff\"] = max_diff\n", "        if max_diff > 0:\n", "            # print(\n", "            #     f\"Sample {index}: {len(generated_texts)} versions, max diff: {max_diff:.3f} between: {max_diff_pair}\"\n", "            # )\n", "            pass\n", "    else:\n", "        row[\"max_diff\"] = 0\n", "        # print(f\"Sample {index}: {len(generated_texts)} versions, no diff\")\n", "    row[\"max_count\"] = max(generated_texts.values())\n", "    row[\"max_frequency\"] = max(generated_texts.values()) / 5\n", "    table.append(row)\n", "\n", "print(\n", "    f\"Number of inconsistent samples: {inconsistency_count}, max diff: {total_max_diff:.3f}\"\n", ")\n", "\n", "\n", "df = pd.DataFrame(table)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Figures"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Create the histogram\n", "n, bins, patches = plt.hist(\n", "    df[\"version_count\"],\n", "    bins=range(1, df[\"version_count\"].max() + 2),\n", "    align=\"left\",\n", "    rwidth=0.8,\n", ")\n", "\n", "# Add count labels on top of each bar\n", "for i, v in enumerate(n):\n", "    plt.text(bins[i], v, str(int(v)), ha=\"center\", va=\"bottom\")\n", "\n", "plt.title(\"Histogram of Version Counts\")\n", "plt.xlabel(\"Number of Versions\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(False)\n", "plt.xticks(range(1, df[\"version_count\"].max() + 1))\n", "\n", "# Adjust the top of the plot to make room for annotations\n", "plt.ylim(top=plt.ylim()[1] * 1.1)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Create the histogram\n", "n, bins, patches = plt.hist(df[\"max_diff\"], bins=20, edgecolor=\"#4c72b0\")\n", "\n", "# Add count labels on top of each bar\n", "for i, v in enumerate(n):\n", "    plt.text(\n", "        bins[i] + (bins[i + 1] - bins[i]) / 2, v, str(int(v)), ha=\"center\", va=\"bottom\"\n", "    )\n", "\n", "plt.title(\"Histogram of Maximum Differences\")\n", "plt.xlabel(\"Maximum Difference\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(False)\n", "\n", "# Adjust the top of the plot to make room for annotations\n", "plt.ylim(top=plt.ylim()[1] * 1.1)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Create the histogram\n", "n, bins, patches = plt.hist(\n", "    df[\"max_count\"],\n", "    bins=range(1, df[\"max_count\"].max() + 2),\n", "    align=\"left\",\n", "    rwidth=0.8,\n", ")\n", "\n", "# Add count labels on top of each bar\n", "for i, v in enumerate(n):\n", "    plt.text(bins[i], v, str(int(v)), ha=\"center\", va=\"bottom\")\n", "\n", "plt.title(\"Histogram of Top-1 Version's Appearances\")\n", "plt.xlabel(\"Appearances of Top-1 Version\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(False)\n", "plt.xticks(range(1, df[\"max_count\"].max() + 1))\n", "\n", "# Adjust the top of the plot to make room for annotations\n", "plt.ylim(top=plt.ylim()[1] * 1.1)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Create the histogram\n", "n, bins, patches = plt.hist(\n", "    df[\"max_frequency\"],\n", "    bins=5,  # Use 20 bins for better distribution visualization\n", "    edgecolor=\"#4c72b0\",\n", "    rwidth=0.8,\n", ")\n", "\n", "# Add count labels on top of each bar\n", "for i, v in enumerate(n):\n", "    plt.text((bins[i] + bins[i + 1]) / 2, v, str(int(v)), ha=\"center\", va=\"bottom\")\n", "\n", "plt.title(\"Histogram of Top-1 Frequency\")\n", "plt.xlabel(\"Top-1 Frequency\")\n", "plt.ylabel(\"Count\")\n", "plt.grid(False)\n", "\n", "# Format x-axis ticks to show percentages\n", "plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f\"{x:.0%}\"))\n", "\n", "# Adjust the top of the plot to make room for annotations\n", "plt.ylim(top=plt.ylim()[1] * 1.1)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Further experiments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(get_generated_text(40, 0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 33\n", "\n", "for i in range(5):\n", "    for j in range(i + 1, 5):\n", "        text_i = get_generated_text(index, i)\n", "        text_j = get_generated_text(index, j)\n", "        similarity = difflib.SequenceMatcher(None, text_i, text_j).ratio()\n", "        print(f\"Dissimilarity between {i} and {j}: {1 - similarity:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["get_version_count(33)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/zhuoran/prompt/reproducibility/aqa40_parallel.json\", \"r\"\n", ") as f:\n", "    example_results = json.load(f)\n", "text_versions = [result[\"text_version\"] for result in example_results]\n", "text_versions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "example_df = pd.DataFrame(example_results)\n", "plt.figure(figsize=(10, 6))\n", "\n", "# Create the histogram\n", "n, bins, patches = plt.hist(\n", "    example_df[\"text_version\"],\n", "    bins=range(example_df[\"text_version\"].max() + 2),\n", "    align=\"left\",\n", "    rwidth=0.8,\n", ")\n", "\n", "# Add count labels on top of each bar\n", "for i, v in enumerate(n):\n", "    plt.text(bins[i], v, str(int(v)), ha=\"center\", va=\"bottom\")\n", "\n", "plt.title(\"Histogram of Text Versions\")\n", "plt.xlabel(\"Text Version\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(False)\n", "plt.xticks(range(1, example_df[\"text_version\"].max() + 1))\n", "\n", "# Adjust the top of the plot to make room for annotations\n", "plt.ylim(top=plt.ylim()[1] * 1.1)\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "import math\n", "\n", "\n", "def calculate_metrics(values):\n", "    # Count frequencies\n", "    counts = Counter(values)\n", "    total = len(values)\n", "\n", "    # Calculate entropy using <PERSON>'s formula\n", "    entropy = 0\n", "    for count in counts.values():\n", "        p = count / total\n", "        entropy -= p * math.log2(p)\n", "\n", "    # Get top-1 probability and number of options\n", "    top1_count = counts.most_common(1)[0][1]\n", "    top1_prob = top1_count / total\n", "    num_options = len(counts)\n", "\n", "    return {\"entropy\": entropy, \"top1_prob\": top1_prob, \"num_options\": num_options}\n", "\n", "\n", "metrics = calculate_metrics(text_versions)\n", "print(f\"Entropy: {metrics['entropy']:.4f} bits\")\n", "print(f\"Top-1 probability: {metrics['top1_prob']:.4f}\")\n", "print(f\"Number of unique options: {metrics['num_options']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compute pairwise diff distances between all texts\n", "distances = []\n", "unique_texts = {result[\"text\"] for result in example_results}\n", "texts_list = list(unique_texts)\n", "for i in range(len(texts_list)):\n", "    for j in range(i + 1, len(texts_list)):\n", "        similarity = difflib.SequenceMatcher(None, texts_list[i], texts_list[j]).ratio()\n", "        diff_distance = 1 - similarity\n", "        distances.append({\"text1_idx\": i, \"text2_idx\": j, \"distance\": diff_distance})\n", "\n", "# Sort by distance to see the most different pairs\n", "distances.sort(key=lambda x: x[\"distance\"], reverse=True)\n", "\n", "# Print the results\n", "for d in distances:\n", "    print(\n", "        f\"Distance between text {d['text1_idx']} and {d['text2_idx']}: {d['distance']:.3f}\"\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
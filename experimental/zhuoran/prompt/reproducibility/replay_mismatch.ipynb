{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import storage\n", "\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache\n", "from base.datasets.gcs_client import GCSRequestInsightFetcher\n", "from base.datasets.tenants import DOGFOOD_SHARD\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from experimental.zhuoran.utils import persistent_cache\n", "from research.core.artifacts import collect_artifacts\n", "from research.core.chat_prompt_input import ResearchChatPromptInput\n", "from research.core.types import Document\n", "from research.eval.harness.systems.remote_chat_system import RemoteChatSystem\n", "\n", "REQUEST_ID = \"e10bc21b-0fc6-4f59-89cb-c98baacb155f\"\n", "\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=2000,\n", "    retrieval_len_for_user_guided=3000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", "    inject_current_file_into_retrievals=True,\n", ")\n", "TOKEN_COUNTER = ClaudeTokenCounter()\n", "\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "VERTEX_AI_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "DOGFOOD_SHARD_URL = \"https://staging-shard-0.api.augmentcode.com/\"\n", "DOGFOOD_SHARD_RI_LINK_TEMPLATE = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/{request_id}\"\n", "\n", "\n", "def get_caches(tenant):\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)\n", "    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)\n", "    blob_cache_size_bytes = 2**30\n", "    blob_cache_num_threads = 32\n", "    blob_cache = GCSBlobCache(\n", "        blob_bucket,\n", "        tenant.blob_bucket_prefix,\n", "        max_size_bytes=blob_cache_size_bytes,\n", "        num_threads=blob_cache_num_threads,\n", "    )\n", "    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)\n", "    checkpoint_cache = GCSCheckpointCache(\n", "        checkpoint_bucket,\n", "        tenant.checkpoint_bucket_prefix,\n", "        blob_cache_size_bytes,\n", "        num_threads=blob_cache_num_threads,\n", "    )\n", "    return blob_cache, checkpoint_cache\n", "\n", "\n", "BLOB_CACHE, CHECKPOINT_CACHE = get_caches(DOGFOOD_SHARD)\n", "\n", "\n", "def run_claude(message_or_prompt, system_message=\"\"):\n", "    if isinstance(message_or_prompt, str):\n", "        message = message_or_prompt\n", "    else:\n", "        assert not system_message\n", "        message = message_or_prompt.message\n", "        system_message = message_or_prompt.system_prompt\n", "    response = VERTEX_AI_CLIENT.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    )\n", "    return response.content[0].text, response\n", "\n", "\n", "def get_chat_host_request(request_id: str, tenant_name: str):\n", "    results = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_requests(\n", "        request_ids=[request_id]\n", "    )\n", "    results = list(results)\n", "    len(results)\n", "    result = results[0]\n", "    request = None\n", "    for event in result.events:\n", "        if event.HasField(\"chat_host_request\"):\n", "            request = event.chat_host_request\n", "            break\n", "    assert request\n", "    return request\n", "\n", "\n", "def get_documents_and_blob_names(\n", "    blobs: dict, blob_cache: GCSBlobCache, checkpoint_cache: GCSCheckpointCache\n", ") -> tuple[list[Document], list[str]]:\n", "    documents = []\n", "    if blobs is not None:\n", "        checkpoint_id = blobs.baseline_checkpoint_id\n", "        if checkpoint_id is None:\n", "            checkpoint_blob_names = set()\n", "        else:\n", "            cached_checkpoint = checkpoint_cache.get([checkpoint_id])[0]\n", "            if cached_checkpoint is None:\n", "                checkpoint_blob_names = set()\n", "            else:\n", "                checkpoint_blob_names = set(\n", "                    checkpoint_cache.get([checkpoint_id])[0].blob_names\n", "                )\n", "        added_blob_names = {blob.hex() for blob in blobs.added}\n", "        deleted_blob_names = {blob.hex() for blob in blobs.deleted}\n", "        blob_names = sorted(\n", "            list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)\n", "        )\n", "        blobs = blob_cache.get(blob_names)\n", "\n", "        documents = [\n", "            Document(id=blob_name, text=blob.content, path=str(blob.path))\n", "            for blob_name, blob in zip(blob_names, blobs)\n", "            if blob is not None\n", "        ]\n", "    return documents, blob_names\n", "\n", "\n", "def chat_history_proto_to_dict(chat_history_proto):\n", "    return [\n", "        {\n", "            \"request_message\": exchange.request_message,\n", "            \"response_text\": exchange.response_text,\n", "            \"request_id\": exchange.request_id,\n", "        }\n", "        for exchange in chat_history_proto\n", "    ]\n", "\n", "\n", "def chat_history_request_to_dict(chat_history_request):\n", "    return [\n", "        {\n", "            \"request_message\": exchange.request_message,\n", "            \"response_text\": exchange.response_text,\n", "            \"request_id\": exchange.request_id,\n", "        }\n", "        for exchange in chat_history_request\n", "    ]\n", "\n", "\n", "def chat_history_dict_to_request(chat_history_dict):\n", "    return [\n", "        Exchange(\n", "            request_message=exchange[\"request_message\"],\n", "            response_text=exchange[\"response_text\"],\n", "            request_id=exchange[\"request_id\"],\n", "        )\n", "        for exchange in chat_history_dict\n", "    ]\n", "\n", "\n", "def retrieval_proto_to_dict(retrieval_proto):\n", "    return [\n", "        {\n", "            \"text\": chunk.text,\n", "            \"path\": chunk.path,\n", "            \"unique_id\": chunk.blob_name + \"-\" + str(chunk.chunk_index),\n", "            \"origin\": chunk.origin,\n", "            \"char_start\": chunk.char_offset,\n", "            \"char_end\": chunk.char_end,\n", "            \"blob_name\": chunk.blob_name,\n", "        }\n", "        for chunk in retrieval_proto\n", "    ]\n", "\n", "\n", "def retrieval_dict_to_request(retrieval_dict):\n", "    return [\n", "        PromptChunk(\n", "            text=chunk[\"text\"],\n", "            path=chunk[\"path\"],\n", "            unique_id=chunk[\"unique_id\"],\n", "            origin=chunk[\"origin\"],\n", "            char_start=chunk[\"char_start\"],\n", "            char_end=chunk[\"char_end\"],\n", "            blob_name=chunk[\"blob_name\"],\n", "        )\n", "        for chunk in retrieval_dict\n", "    ]\n", "\n", "\n", "def retrieval_request_to_dict(retrieval_request):\n", "    return [\n", "        {\n", "            \"text\": chunk.text,\n", "            \"path\": chunk.path,\n", "            \"unique_id\": chunk.unique_id,\n", "            \"origin\": chunk.origin,\n", "            \"char_start\": chunk.char_start,\n", "            \"char_end\": chunk.char_end,\n", "            \"blob_name\": chunk.blob_name,\n", "        }\n", "        for chunk in retrieval_request\n", "    ]\n", "\n", "\n", "def build_retrievals(request) -> list[PromptChunk]:\n", "    prompt_chunks = []\n", "    for chunk in request.retrieved_chunks:\n", "        char_offset = chunk.char_offset\n", "        chunk_index = str(chunk.chunk_index)\n", "        prompt_chunk = PromptChunk(\n", "            text=chunk.text,\n", "            path=chunk.path,\n", "            unique_id=chunk.blob_name + \"-\" + chunk_index,\n", "            origin=chunk.origin,\n", "            char_start=char_offset,\n", "            char_end=chunk.char_end,\n", "            blob_name=chunk.blob_name,\n", "        )\n", "        prompt_chunks.append(prompt_chunk)\n", "    return prompt_chunks\n", "\n", "\n", "@persistent_cache(\"/mnt/efs/augment/user/zhuoran/prompt/replay_cache.jsonl\")\n", "def get_chat_prompt_and_document_jsons(request_id: str) -> tuple[dict, list[dict]]:\n", "    full_request = get_chat_host_request(request_id, DOGFOOD_SHARD.name)\n", "    documents, blob_names = get_documents_and_blob_names(\n", "        full_request.request.blobs[0], BLOB_CACHE, CHECKPOINT_CACHE\n", "    )\n", "    request = full_request.request\n", "    selected_code = request.selected_code\n", "    prefix = request.prefix\n", "    suffix = request.suffix\n", "    full_file = prefix + selected_code + suffix\n", "\n", "    chat_prompt_dict = {\n", "        \"message\": request.message,\n", "        \"path\": request.path,\n", "        \"prefix\": prefix,\n", "        \"selected_code\": selected_code,\n", "        \"suffix\": suffix,\n", "        \"chat_history\": chat_history_proto_to_dict(request.chat_history),\n", "        \"prefix_begin\": 0,\n", "        \"suffix_end\": len(full_file),\n", "        \"retrieved_chunks\": retrieval_proto_to_dict(full_request.retrieved_chunks),\n", "        \"context_code_exchange_request_id\": request.context_code_exchange_request_id,\n", "        \"user_guided_blobs\": [\n", "            user_guided_blob for user_guided_blob in request.user_guided_blobs\n", "        ],\n", "        \"doc_ids\": blob_names,\n", "    }\n", "    document_dicts = documents_to_dicts(documents)\n", "    return chat_prompt_dict, document_dicts\n", "\n", "\n", "def chat_prompt_input_from_dict(chat_prompt_dict: dict) -> ResearchChatPromptInput:\n", "    return ResearchChatPromptInput(\n", "        message=chat_prompt_dict[\"message\"],\n", "        path=chat_prompt_dict[\"path\"],\n", "        prefix=chat_prompt_dict[\"prefix\"],\n", "        selected_code=chat_prompt_dict[\"selected_code\"],\n", "        suffix=chat_prompt_dict[\"suffix\"],\n", "        chat_history=chat_history_dict_to_request(chat_prompt_dict[\"chat_history\"]),\n", "        prefix_begin=chat_prompt_dict[\"prefix_begin\"],\n", "        suffix_end=chat_prompt_dict[\"suffix_end\"],\n", "        retrieved_chunks=retrieval_dict_to_request(\n", "            chat_prompt_dict[\"retrieved_chunks\"]\n", "        ),\n", "        context_code_exchange_request_id=chat_prompt_dict[\n", "            \"context_code_exchange_request_id\"\n", "        ],\n", "        user_guided_blobs=chat_prompt_dict[\"user_guided_blobs\"],\n", "        doc_ids=chat_prompt_dict[\"doc_ids\"],\n", "    )\n", "\n", "\n", "def chat_prompt_dict_from_input(chat_prompt_input: ResearchChatPromptInput) -> dict:\n", "    return {\n", "        \"message\": chat_prompt_input.message,\n", "        \"path\": chat_prompt_input.path,\n", "        \"prefix\": chat_prompt_input.prefix,\n", "        \"selected_code\": chat_prompt_input.selected_code,\n", "        \"suffix\": chat_prompt_input.suffix,\n", "        \"chat_history\": chat_history_request_to_dict(chat_prompt_input.chat_history),\n", "        \"prefix_begin\": chat_prompt_input.prefix_begin,\n", "        \"suffix_end\": chat_prompt_input.suffix_end,\n", "        \"retrieved_chunks\": retrieval_request_to_dict(\n", "            chat_prompt_input.retrieved_chunks\n", "        ),\n", "        \"context_code_exchange_request_id\": chat_prompt_input.context_code_exchange_request_id,\n", "        \"user_guided_blobs\": chat_prompt_input.user_guided_blobs,\n", "        \"doc_ids\": chat_prompt_input.doc_ids,\n", "    }\n", "\n", "\n", "def documents_from_dicts(document_dicts: list[dict]) -> list[Document]:\n", "    return [\n", "        Document(\n", "            id=doc_dict[\"id\"],\n", "            text=doc_dict[\"text\"],\n", "            path=doc_dict[\"path\"],\n", "            meta=doc_dict[\"meta\"],\n", "        )\n", "        for doc_dict in document_dicts\n", "    ]\n", "\n", "\n", "def documents_to_dicts(documents: list[Document]) -> list[dict]:\n", "    return [\n", "        {\"id\": doc.id, \"text\": doc.text, \"path\": doc.path, \"meta\": doc.meta}\n", "        for doc in documents\n", "    ]\n", "\n", "\n", "def get_input_and_documents(request_id):\n", "    chat_prompt_dict, document_dicts = get_chat_prompt_and_document_jsons(request_id)\n", "    chat_prompt_input = chat_prompt_input_from_dict(chat_prompt_dict)\n", "    documents = documents_from_dicts(document_dicts)\n", "    return chat_prompt_input, documents\n", "\n", "\n", "def get_prompt_formatter(prompt_formatter_name: str):\n", "    return get_structured_chat_prompt_formatter_by_name(\n", "        prompt_formatter_name, TOKEN_APPORTIONMENT\n", "    )\n", "\n", "\n", "def count_tags(text: str):\n", "    opening_count = text.count(\"<augment_code_snippet\")\n", "    closing_count = text.count(\"</augment_code_snippet>\")\n", "    return opening_count, closing_count\n", "\n", "\n", "def generate_chat_response(url, model_name, documents, model_input):\n", "    remote_config = {\n", "        \"client\": {\"url\": url},\n", "        \"model_name\": model_name,\n", "    }\n", "    remote_chat = RemoteChatSystem.from_yaml_config(\n", "        remote_config,\n", "    )\n", "    remote_chat.load()\n", "    remote_chat.add_docs(documents)\n", "\n", "    with collect_artifacts() as collector_manager:\n", "        chat_response = remote_chat.generate(model_input)\n", "        artifacts = collector_manager.get_artifacts()\n", "    request_id = artifacts[0][\"request_id\"]\n", "    return chat_response, request_id\n", "\n", "\n", "def get_remote_tag_counts(request_id, model_name, remote_url, link_template):\n", "    chat_prompt_input, documents = get_input_and_documents(request_id)\n", "    try:\n", "        chat_response, new_request_id = generate_chat_response(\n", "            remote_url, model_name, documents, chat_prompt_input\n", "        )\n", "        generated_text = chat_response.generated_text\n", "        opening_count, closing_count = count_tags(generated_text)\n", "    except Exception:\n", "        opening_count = -1\n", "        closing_count = -1\n", "        new_request_id = None\n", "        generated_text = None\n", "        print(f\"Failed to generate response for {request_id}\")\n", "    return {\n", "        \"request_id\": request_id,\n", "        \"new_request_id\": new_request_id,\n", "        \"opening_count\": opening_count,\n", "        \"closing_count\": closing_count,\n", "        \"generated_text\": generated_text,\n", "    }\n", "\n", "\n", "model_name = \"claude-sonnet-3-5-16k-v8-chat\"\n", "remote_url = DOGFOOD_SHARD_URL\n", "link_template = DOGFOOD_SHARD_RI_LINK_TEMPLATE\n", "remote_result = get_remote_tag_counts(REQUEST_ID, model_name, remote_url, link_template)\n", "\n", "print(f\"{remote_result['opening_count']=}, {remote_result['closing_count']=}\")\n", "print(remote_result[\"generated_text\"])\n", "print(link_template.format(request_id=remote_result[\"new_request_id\"]))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import dataclasses
import re

from base.prompt_format.common import try_get_response_message_as_text
from base.prompt_format_chat.prompt_formatter import (
    Exchange,
    StructuredChatPromptOutput,
)
from base.third_party_clients.google_genai_client import ResponseMessage

PROBING_PROMPT = """Please answer the following questions:

1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.
2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?

Your task is to <PERSON><PERSON><PERSON><PERSON> answer the question above without taking any other action.

Your answer must end with a clear Yes-No for each question, in the following format:
<answer id=1>Yes</answer>
<answer id=2>No</answer>
""".strip()

STANDALONE_PROBING_PROMPT = """The previous message is a single turn in an AI agent's execution trajectory. Please answer the following questions:

1. Did the model ask a user to perform an action in the previous turn, which it could do itself? Asking for user permission is not the same as asking a user to perform an action.
2. Did the model say it is going to perform an action in the previous turn, but did not actually perform the action (most typically, calling a function)?

Your task is to <PERSON><PERSON><PERSON><PERSON> answer the question above without taking any other action.

Your answer must end with a clear Yes-No for each question, in the following format:
<answer id=1>Yes</answer>
<answer id=2>No</answer>
""".strip()

EMPTY_RESPONSE_REMEDY_PROMPT = "Continue."

LAZY_DELEGATION_REMEDY_PROMPT = """Perform the action yourself, instead of asking the user to perform it. Perform the exact action(s) you asked the user to perform."""

LACK_OF_ACTION_REMEDY_PROMPT = "Perform the action you said you would perform in the previous turn by calling the appropriate function."


def count_tool_calls(response: ResponseMessage) -> int:
    """Returns the number of tool calls in the response."""
    return 0


def run_model(
    base_model_version: str, prompt_output: StructuredChatPromptOutput
) -> ResponseMessage:
    """Runs the model on the prompt output."""
    return ""


def get_probing_answers(response) -> tuple[bool, bool]:
    """Returns the probing answers from the response.

    The probing answers are expected to be in the format:
    <answer id=1>Yes</answer>
    <answer id=2>No</answer>

    Args:
        response: The response from the model.

    Returns:
        A tuple of two booleans, representing the answers to the two probing questions.
    """
    text = try_get_response_message_as_text(response)
    pattern = r"<answer id=(\d+)>(.*?)</answer>"
    matches = re.findall(pattern, text, re.DOTALL)
    answers = {
        int(answer_id): answer_value.strip() for answer_id, answer_value in matches
    }
    return answers.get(1, "No") == "Yes", answers.get(2, "No") == "Yes"


def assess_response(
    response: ResponseMessage, prompt_output: StructuredChatPromptOutput
):
    """Assesses the response and returns the issue type."""
    if not response:
        return "empty_response"
    if count_tool_calls(response):
        return "no_issue"

    probing_prompt_output = StructuredChatPromptOutput(
        system_prompt="",
        chat_history=list(prompt_output.chat_history)[-1:],
        message=STANDALONE_PROBING_PROMPT,
        retrieved_chunks_in_prompt=[],
    )

    probing_response = run_model(
        base_model_version="gemini2.5-flash",
        prompt_output=probing_prompt_output,
    )
    answer1, answer2 = get_probing_answers(probing_response)
    if answer1:
        return "lazy_delegation"
    if answer2:
        return "lack_of_action"
    return "no_issue"


def get_remedy_prompt(
    issue_type: str,
    prompt_output: StructuredChatPromptOutput,
    response: ResponseMessage,
) -> StructuredChatPromptOutput:
    """Returns the remedy prompt for the issue type."""
    remedy_message = None
    if issue_type == "empty_response":
        remedy_message = EMPTY_RESPONSE_REMEDY_PROMPT
    elif issue_type == "lazy_delegation":
        remedy_message = LAZY_DELEGATION_REMEDY_PROMPT
    elif issue_type == "lack_of_action":
        remedy_message = LACK_OF_ACTION_REMEDY_PROMPT
    else:
        raise ValueError(f"Invalid issue type: {issue_type}")

    if remedy_message is None:
        raise ValueError(f"Invalid issue type: {issue_type}")
    return dataclasses.replace(
        prompt_output,
        chat_history=list(prompt_output.chat_history)
        + [
            Exchange(
                request_message=prompt_output.message,
                response_text=response,
            )
        ],
        message=remedy_message,
    )


def remedy_response(
    prompt_output: StructuredChatPromptOutput,
    response: ResponseMessage,
) -> tuple[StructuredChatPromptOutput, ResponseMessage]:
    """Remedies the response for the issue type."""
    issue_type = assess_response(response, prompt_output)
    if issue_type == "no_issue":
        return prompt_output, response
    remedy_prompt_output = get_remedy_prompt(issue_type, prompt_output, response)
    return (
        remedy_prompt_output,
        run_model(
            base_model_version="gemini2.5-flash", prompt_output=remedy_prompt_output
        ),
    )

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import decode_prompt, print_request\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/zhuoran/markdown_examples/gemini_agent/stop_before_edit.md\",\n", "    \"r\",\n", ") as f:\n", "    prompt_output = decode_prompt(f.read())\n", "\n", "print_request(prompt_output.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import print_chat_history\n", "\n", "print_chat_history(prompt_output.chat_history, text_limit=100, tool_limit=100)\n", "print(\"=\" * 81)\n", "print_request(prompt_output.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import TOOL_DEFINITIONS\n", "\n", "tool_definitions = [\n", "    tool_definition\n", "    for tool_definition in TOOL_DEFINITIONS\n", "    if tool_definition.name not in [\"git\", \"version-control\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import truncate_prompt_output\n", "\n", "truncated_prompt_output = truncate_prompt_output(prompt_output, 1000)\n", "print_chat_history(\n", "    truncated_prompt_output.chat_history[-1:], text_limit=1000, tool_limit=100\n", ")\n", "print(\"=\" * 81)\n", "# truncated_prompt_output.message = \"OK, now view\"\n", "print_request(truncated_prompt_output.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from research.tools.chat_replay.replay_utils import (\n", "    run_model,\n", "    print_response,\n", "    jsonify_final_parameters,\n", "    fix_tool_calls,\n", ")\n", "\n", "fixed_prompt_output = fix_tool_calls(truncated_prompt_output)\n", "response = run_model(\n", "    fixed_prompt_output,\n", "    tool_definitions=tool_definitions,\n", "    base_model_version=\"gemini2.5\",\n", "    client_type=\"genai\",\n", "    yield_final_parameters=True,\n", ")\n", "final_parameters = response[0].final_parameters\n", "print_response(response[1:], tool_limit=100, string_limit=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response[1:].__len__()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_parameters[\"messages\"][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import (\n", "    ChatRequestNode,\n", "    ChatRequestNodeType,\n", "    ChatResultNode,\n", "    ChatResultNodeType,\n", "    ChatRequestText,\n", ")\n", "\n", "for index, exchange in enumerate(fixed_prompt_output.chat_history):\n", "    print(f\"{index:02d}:\")\n", "    if isinstance(exchange.request_message, str):\n", "        request_message = [\n", "            ChatRequestNode(\n", "                id=0,\n", "                type=ChatRequestNodeType.TEXT,\n", "                text_node=ChatRequestText(content=exchange.request_message),\n", "                tool_result_node=None,\n", "            )\n", "        ]\n", "    else:\n", "        request_message = exchange.request_message\n", "    print(\"    \", end=\"\")\n", "    for request_node in request_message:\n", "        if request_node.type == ChatRequestNodeType.TOOL_RESULT:\n", "            print(f\"Result ({request_node.tool_result_node.tool_use_id})\", end=\", \")\n", "        else:\n", "            print(f\"{request_node.type}\", end=\", \")\n", "\n", "    if isinstance(exchange.response_text, str):\n", "        response_text = [\n", "            ChatResultNode(\n", "                id=0,\n", "                type=ChatResultNodeType.RAW_RESPONSE,\n", "                content=exchange.response_text,\n", "                tool_use=None,\n", "            )\n", "        ]\n", "    else:\n", "        response_text = exchange.response_text\n", "    print()\n", "    print(\"    \", end=\"\")\n", "    for response_node in response_text:\n", "        if response_node.type == ChatResultNodeType.TOOL_USE:\n", "            print(\n", "                f\"{response_node.tool_use.name} ({response_node.tool_use.tool_use_id})\",\n", "                end=\", \",\n", "            )\n", "        else:\n", "            print(f\"{response_node.type}\", end=\", \")\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
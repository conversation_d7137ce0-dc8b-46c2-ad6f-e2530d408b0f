from pathlib import Path

from experimental.zhuoran.sentry.utils import (
    Qwen25CoderTokenizer,
    generate_training_data,
)

INPUT_SAMPLES = "/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl"
OUTPUT_DIR = "/home/<USER>/sentry_data/v4"
MAX_TOKEN_BUDGET = 32768 + 1
FUZZ_PROBABILITIES = {
    "unclosed": 0.3,
    "random_unclosed": 0.1,
    "no_backticks": 0.05,
    "random_no_backticks": 0.05,
    "inner_tag": 0.1,
    "ending": 0.05,
    "random_ending": 0.05,
}

Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
tokenizer = Qwen25CoderTokenizer()
generate_training_data(
    input_path=INPUT_SAMPLES,
    output_dir=OUTPUT_DIR,
    tokenizer=tokenizer,
    max_token_budget=MAX_TOKEN_BUDGET,
    process_count=24,
    fuzz_probabilities=FUZZ_PROBABILITIES,
    backtick_count=3.25,
)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import subprocess\n", "import shutil\n", "import os\n", "from pathlib import Path\n", "\n", "from research.tools.ckp_converter.fbw2ffw_llama import main as fbw2ffw_fn\n", "\n", "checkpoint_id = \"\"\"\n", "\tedcae98d-dd3f-4a23-a56d-6511ca2eed91\n", "\"\"\".strip()\n", "version = \"v4\"\n", "\n", "\n", "def generate_checkpoint_commands(checkpoint_id: str, version: str) -> str:\n", "    commands = [\n", "        f\"bash research/utils/download_checkpoint.sh {checkpoint_id} sentry/{version}\",\n", "        f\"mkdir -p /home/<USER>/sentry_weights/{version}\",\n", "        f\"gsutil -m cp -r gs://gcp-us1-checkpoints/sentry/{version}/* /home/<USER>/sentry_weights/{version}/\",\n", "        f\"mv /home/<USER>/sentry_weights/{version}/ /mnt/efs/augment/checkpoints/sentry/{version}_fb -v\",\n", "        f\"python research/tools/ckp_converter/fbw2ffw_llama.py --input_ckpt_dir /mnt/efs/augment/checkpoints/sentry/{version}_fb --output_ckpt_dir /mnt/efs/augment/checkpoints/sentry/{version}_ff\",\n", "    ]\n", "    return \"\\n\".join(commands)\n", "\n", "\n", "print(generate_checkpoint_commands(checkpoint_id, version))\n", "\n", "# print(\"Downloading checkpoint {checkpoint_id} to sentry/{version}\")\n", "# subprocess.run(\n", "#     f\"bash research/utils/download_checkpoint.sh {checkpoint_id} sentry/{version}\",\n", "#     shell=True,\n", "#     check=False,\n", "# )\n", "\n", "# print(\"Copying checkpoint from GCP to /home/<USER>/sentry_weights/{version}\")\n", "# os.makedirs(f\"/home/<USER>/sentry_weights/{version}\", exist_ok=False)\n", "# subprocess.run(\n", "#     f\"gsutil -m cp -r gs://gcp-us1-checkpoints/sentry/{version}/* /home/<USER>/sentry_weights/{version}/\",\n", "#     shell=True,\n", "#     check=True,\n", "# )\n", "\n", "# print(\"Moving checkpoint to /mnt/efs/augment/checkpoints/sentry/{version}_fb\")\n", "# shutil.move(\n", "#     f\"/home/<USER>/sentry_weights/{version}/\",\n", "#     f\"/mnt/efs/augment/checkpoints/sentry/{version}_fb\",\n", "# )\n", "\n", "# print(\"Converting checkpoint to FastForward format\")\n", "# fbw2ffw_fn(\n", "#     Path(f\"/mnt/efs/augment/checkpoints/sentry/{version}_fb\"),\n", "#     Path(f\"/mnt/efs/augment/checkpoints/sentry/{version}_ff\"),\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.evaluate import (\n", "    get_model,\n", "    TOKENIZER,\n", "    GENERATION_OPTIONS,\n", ")\n", "\n", "model = get_model(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_attributes(object_):\n", "    attributes = []\n", "    for attr in dir(object_):\n", "        if not attr.startswith(\"_\"):\n", "            attributes.append(attr)\n", "    return attributes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.chat_replay.replay_utils import get_chat_host_response_text\n", "\n", "request_id = \"\"\"\n", "77f60cdf-49fc-4e9b-ab75-31e11c26fdb7\n", "\"\"\".strip()\n", "response_text = get_chat_host_response_text(request_id, \"dogfood-shard\")\n", "\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(response_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.evaluate import get_input_tokens\n", "\n", "with open(\"/home/<USER>/a.md\", \"r\") as f:\n", "    response_text = f.read()\n", "\n", "input_tokens = get_input_tokens(response_text, TOKENIZER)\n", "generation = model.raw_generate(input_tokens, GENERATION_OPTIONS)\n", "\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(generation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "path = \"/mnt/efs/augment/user/zhuoran/sentry/data/v3/validation\"\n", "\n", "validation_dataset = indexed_dataset.make_dataset(path, impl=\"mmap\", skip_warmup=True)\n", "print(len(validation_dataset))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example = validation_dataset[4]\n", "print(len(example))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "\n", "def extract_prefix_to_middle(text: str) -> str:\n", "    \"\"\"Extract text between <|fim_prefix|> and <|fim_middle|>.\"\"\"\n", "    try:\n", "        start = text.index(\"<|fim_prefix|>\") + len(\"<|fim_prefix|>\")\n", "        end = text.index(\"<|fim_middle|>\")\n", "        return text[start:end].strip()\n", "    except ValueError:\n", "        return \"\"\n", "\n", "\n", "def extract_middle_to_end(text: str) -> str:\n", "    \"\"\"Extract text between <|fim_middle|> and <|endoftext|>.\"\"\"\n", "    try:\n", "        start = text.index(\"<|fim_middle|>\") + len(\"<|fim_middle|>\")\n", "        end = text.index(\"<|endoftext|>\")\n", "        return text[start:end].strip()\n", "    except ValueError:\n", "        return \"\"\n", "\n", "\n", "def has_extra_backticks(text: str) -> bool:\n", "    \"\"\"Check if there are more than 2 sets of backticks between augment code snippet tags.\n", "\n", "    Args:\n", "        text: String containing augment code snippets\n", "\n", "    Returns:\n", "        True if any pair of adjacent augment code snippet tags contains more than\n", "        2 sets of backticks between them, False otherwise\n", "    \"\"\"\n", "    # Find all pairs of opening and closing tags\n", "    pattern = r\"<augment_code_snippet[^>]*>(.*?)</augment_code_snippet>\"\n", "    matches = re.finditer(pattern, text, re.DOTALL)\n", "\n", "    for match in matches:\n", "        content = match.group(1)\n", "        # Count sets of backticks (3 or more consecutive backticks)\n", "        backtick_sets = re.findall(r\"```+\", content)\n", "        if len(backtick_sets) > 2:\n", "            return True\n", "\n", "    return False\n", "\n", "\n", "are_same = []\n", "different_indices = []\n", "extra_backticks_indices = []\n", "for index, example in enumerate(validation_dataset):\n", "    example_list = truncate_to_marker(example, eos_token)\n", "    example_string = tokenizer.detokenize(example_list)\n", "    input_string = extract_prefix_to_middle(example_string)\n", "    output_string = extract_middle_to_end(example_string)\n", "    is_same = 1 if input_string == output_string else 0\n", "    extra_backticks = has_extra_backticks(output_string)\n", "    are_same.append(is_same)\n", "    if not is_same:\n", "        different_indices.append(index)\n", "        if extra_backticks:\n", "            extra_backticks_indices.append(index)\n", "\n", "print(len(validation_dataset), len(different_indices), len(extra_backticks_indices))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indirect_index = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = extra_backticks_indices[indirect_index]\n", "print(index)\n", "example = validation_dataset[index]\n", "example_list = truncate_to_marker(example, eos_token)\n", "example_string = tokenizer.detokenize(example_list)\n", "input_string = extract_prefix_to_middle(example_string)\n", "output_string = extract_middle_to_end(example_string)\n", "\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(input_string)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(output_string)\n", "indirect_index += 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# FF & AF"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.evaluate import get_results, get_model_results\n", "\n", "results = get_results(\"/mnt/efs/augment/user/zhuoran/prompt/eval_first7.json\")\n", "model_results = get_model_results(results, \"v11v_bt4_v2\")\n", "originally_paired_results = []\n", "originally_unpaired_results = []\n", "for result in model_results:\n", "    response = result[\"response\"]\n", "    original_opening_count = result[\"opening_count\"]\n", "    original_closing_count = result[\"closing_count\"]\n", "    originally_paired = original_opening_count == original_closing_count\n", "    if originally_paired:\n", "        originally_paired_results.append(result)\n", "    else:\n", "        originally_unpaired_results.append(result)\n", "\n", "print(len(originally_paired_results), len(originally_unpaired_results))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = originally_paired_results[index]\n", "response = result[\"response\"]\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(response)\n", "input_tokens = get_input_tokens(response, TOKENIZER)\n", "generation = model.raw_generate(input_tokens, GENERATION_OPTIONS)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(generation)\n", "index += 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Genereted results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/zhuoran/sentry/evals/v3/first_failures/v11v_bt4_v2.json\"\n", ") as f:\n", "    sentry_results = json.load(f)\n", "print(len(sentry_results))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["viewed_indices = set()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "index = random.randint(0, len(sentry_results) - 1)\n", "\n", "sentry_result = sentry_results[index]\n", "response = sentry_result[\"response\"]\n", "generation = sentry_result[\"generation\"]\n", "originally_paired = sentry_result[\"originally_paired\"]\n", "paired = sentry_result[\"paired\"]\n", "unchanged = sentry_result[\"unchanged\"]\n", "correct = sentry_result[\"correct\"]\n", "result_string = (\n", "    f\"{index+1:>3}/{len(sentry_results)}\"\n", "    f\" {'✅' if correct else '❌'}:\"\n", "    f\" paired {'T' if originally_paired else 'F'}=>{'T' if paired else 'F'},\"\n", "    f\" change {'N' if unchanged else 'Y'}\"\n", "    f\" (viewed: {len(viewed_indices)})\"\n", ")\n", "if index in viewed_indices:\n", "    result_string = \"[VIEWED] \" + result_string\n", "viewed_indices.add(index)\n", "print(result_string)\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(response)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(generation)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Training data analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "path = \"/mnt/efs/augment/user/zhuoran/sentry/data/v3/training\"\n", "\n", "training_dataset = indexed_dataset.make_dataset(path, impl=\"mmap\", skip_warmup=True)\n", "assert training_dataset\n", "print(len(training_dataset))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from experimental.zhuoran.sentry.evaluate import truncate_to_marker\n", "from experimental.zhuoran.sentry.evaluate import extract_prefix_to_middle\n", "from experimental.zhuoran.sentry.evaluate import extract_middle_to_end\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "eos_token = tokenizer.special_tokens.eos\n", "\n", "\n", "def has_ending(text: str) -> bool:\n", "    lines = text.splitlines()\n", "    for i, line in enumerate(lines):\n", "        if line.strip() == \"</augment_code_snippet>\":\n", "            if i == 0 or not lines[i - 1].rstrip().endswith(\"```\"):\n", "                return True\n", "    return False\n", "\n", "\n", "are_same = []\n", "have_ending = []\n", "different_indices = []\n", "ending_indices = []\n", "for index, example in enumerate(training_dataset):\n", "    example_list = truncate_to_marker(example, eos_token)\n", "    example_string = tokenizer.detokenize(example_list)\n", "    input_string = extract_prefix_to_middle(example_string)\n", "    output_string = extract_middle_to_end(example_string)\n", "    is_same = 1 if input_string == output_string else 0\n", "    are_same.append(is_same)\n", "    if not is_same:\n", "        different_indices.append(index)\n", "    has_ending_bool = has_ending(output_string)\n", "    have_ending.append(has_ending_bool)\n", "    if has_ending_bool:\n", "        ending_indices.append(index)\n", "\n", "    if index % 1000 == 0:\n", "        print(\n", "            index,\n", "            len(different_indices),\n", "            f\"{sum(are_same)/len(are_same):.1%}\",\n", "            len(ending_indices),\n", "            f\"{sum(have_ending)/len(have_ending):.1%}\",\n", "        )\n", "\n", "print(\n", "    len(training_dataset),\n", "    len(different_indices),\n", "    f\"{sum(are_same)/len(are_same):.1%}\",\n", "    len(ending_indices),\n", "    f\"{sum(have_ending)/len(have_ending):.1%}\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from experimental.zhuoran.sentry.evaluate import truncate_to_marker\n", "from experimental.zhuoran.sentry.evaluate import extract_prefix_to_middle\n", "from experimental.zhuoran.sentry.evaluate import extract_middle_to_end\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "eos_token = tokenizer.special_tokens.eos\n", "\n", "\n", "lengths = []\n", "for index, example in enumerate(training_dataset):\n", "    example_list = truncate_to_marker(example, eos_token)\n", "    example_string = tokenizer.detokenize(example_list)\n", "    input_string = extract_prefix_to_middle(example_string)\n", "    input_tokens = tokenizer.tokenize_safe(input_string)\n", "    lengths.append(len(input_tokens))\n", "\n", "    if index % 1000 == 0:\n", "        print(\n", "            index,\n", "            f\"{sum(lengths)/len(lengths):.1f}\",\n", "            f\"{max(lengths)}\",\n", "            f\"{min(lengths):}\",\n", "        )\n", "\n", "print(\n", "    index,\n", "    f\"{sum(lengths)/len(lengths):.1f}\",\n", "    f\"{max(lengths)}\",\n", "    f\"{min(lengths):}\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
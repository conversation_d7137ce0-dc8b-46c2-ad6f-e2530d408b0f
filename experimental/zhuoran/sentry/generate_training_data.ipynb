{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "# INPUT_SAMPLES = Path(\n", "#     \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/05_merged_responses/merged_smart_paste_responses.jsonl\"\n", "# )\n", "INPUT_SAMPLES = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl\"\n", ")\n", "\n", "OUTPUT_DIR = Path(\"/home/<USER>/sentry_data/v0\")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "MAX_TOKEN_BUDGET = 32768 + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import read_examples\n", "\n", "examples = read_examples(INPUT_SAMPLES, 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from experimental.zhuoran.sentry.utils import format_example\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "max_token_budget = 16384 + 1\n", "tokenized_examples = []\n", "\n", "for index, example in enumerate(examples[:10]):\n", "    tokenized_examples_from_input = format_example(example, tokenizer, \"unclosed\")\n", "    for tokenized_example in tokenized_examples_from_input:\n", "        # fim_middle_index = tokenized_example.index(tokenizer.special_tokens.fim_middle)\n", "        # before_fim_middle = tokenized_example[:fim_middle_index]\n", "        # after_fim_middle = tokenized_example[fim_middle_index + 1 :]\n", "        # if len(before_fim_middle) - 1 != len(after_fim_middle):\n", "        #     before_fim_middle_string = tokenizer.detokenize(before_fim_middle)\n", "        #     after_fim_middle_string = tokenizer.detokenize(after_fim_middle)\n", "        #     print(f\"{index}: {len(before_fim_middle)=}, {len(after_fim_middle)=}\")\n", "        #     print()\n", "        #     print(before_fim_middle_string)\n", "        #     print()\n", "        #     print(after_fim_middle_string)\n", "        #     print()\n", "        #     assert False\n", "        tokenized_examples.append(tokenized_example)\n", "print(f\"{len(tokenized_examples)=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import StructuredResponse\n", "\n", "structured_response = StructuredResponse.from_response(\n", "    \"Understood. I'll refer to the excerpts for context, and ignore them for general questions.\"\n", ")\n", "print(structured_response)\n", "rendered_response = structured_response.render(\"unclosed\")\n", "print(rendered_response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import generate_training_data\n", "\n", "generate_training_data(\n", "    input_path=INPUT_SAMPLES,\n", "    output_dir=OUTPUT_DIR,\n", "    tokenizer=tokenizer,\n", "    max_token_budget=MAX_TOKEN_BUDGET,\n", "    process_count=1,\n", "    limit=30,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import make_dataset\n", "\n", "training_dataset = make_dataset(\n", "    str(OUTPUT_DIR / \"training\"), impl=\"mmap\", skip_warmup=True\n", ")\n", "print(f\"Training dataset size: {len(training_dataset)}\")\n", "validation_dataset = make_dataset(\n", "    str(OUTPUT_DIR / \"validation\"), impl=\"mmap\", skip_warmup=True\n", ")\n", "print(f\"Validation dataset size: {len(validation_dataset)}\")\n", "\n", "\n", "tokens = ds[8]\n", "# tokens = ds[100]\n", "tokens = [abs(x) for x in tokens]\n", "string = tokenizer.detokenize(tokens).replace(\"<|padding|>\", \"\")\n", "with open(\"/home/<USER>/c.md\", \"w\") as f:\n", "    f.write(string)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
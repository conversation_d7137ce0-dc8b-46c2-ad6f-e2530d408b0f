{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl\",\n", "    \"r\",\n", ") as f:\n", "    lines = f.readlines()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Input structure"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "index = 0\n", "\n", "example = json.loads(lines[index])\n", "print(example.keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(example[\"aux\"].keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(example[\"aux\"][\"commit\"].keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(example[\"aux\"][\"commit\"][\"before_file\"].keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "\n", "before_contents = example[\"aux\"][\"commit\"][\"before_file\"][\"contents\"]\n", "after_contents = example[\"aux\"][\"commit\"][\"after_file\"][\"contents\"]\n", "\n", "diff = difflib.unified_diff(\n", "    before_contents.splitlines(keepends=True),\n", "    after_contents.splitlines(keepends=True),\n", "    n=5,\n", ")\n", "\n", "print(\"\".join(diff))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Convert"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import tqdm\n", "\n", "chat_response_strings = set()\n", "for line in tqdm.tqdm(lines):\n", "    example = json.loads(line)\n", "    chat_response_strings.add(example[\"aux\"][\"chat_response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/responses.json\", \"w\") as f:\n", "    json.dump(list(chat_response_strings), f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"/home/<USER>/responses.json\", \"r\") as f:\n", "    chat_response_strings = set(json.load(f))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False or \"```\".lstrip(\"`\"):\n", "    print(\"yes\")\n", "else:\n", "    print(\"no\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "from experimental.zhuoran.sentry.utils import structurize\n", "\n", "structured_responses = []\n", "for response in tqdm.tqdm(chat_response_strings):\n", "    structured_response = structurize(response)\n", "    if structured_response:\n", "        structured_responses.append(structured_response)\n", "\n", "print(f\"{len(structured_responses)} / {len(chat_response_strings)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["error_counts = {}\n", "language_counts = {}\n", "text_count = 0\n", "codeblock_count = 0\n", "for example_index, structured_response in enumerate(structured_responses):\n", "    for codeblock in structured_response:\n", "        if codeblock[\"type\"] == \"raw\":\n", "            response = codeblock[\"response\"]\n", "        elif codeblock[\"type\"] == \"error\":\n", "            error_counts[codeblock[\"error\"]] = (\n", "                error_counts.get(codeblock[\"error\"], 0) + 1\n", "            )\n", "            if codeblock[\"error\"] not in {\"Mismatching tags\", \"XML tag leak\"}:\n", "                print(f\"{codeblock['error']}: {example_index}\")\n", "        elif codeblock[\"type\"] == \"text\":\n", "            text_count += 1\n", "        elif codeblock[\"type\"] == \"codeblock\":\n", "            codeblock_count += 1\n", "            language_counts[codeblock[\"language\"]] = (\n", "                language_counts.get(codeblock[\"language\"], 0) + 1\n", "            )\n", "\n", "error_counts = dict(sorted(error_counts.items(), key=lambda x: x[1], reverse=True))\n", "language_counts = dict(\n", "    sorted(language_counts.items(), key=lambda x: x[1], reverse=True)\n", ")\n", "print(error_counts)\n", "print(language_counts)\n", "print(f\"text_count: {text_count}\")\n", "print(f\"codeblock_count: {codeblock_count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "index = 0\n", "print([node[\"type\"] for node in structured_responses[index]])\n", "for codeblock in structured_responses[index][:-1]:\n", "    print(json.dumps(codeblock, indent=4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import render_structured_response\n", "\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(render_structured_response(structured_responses[index]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(structured_responses[index][-1][\"response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(structured_responses[240628][-1][\"response\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "INPUT_SAMPLES = Path(\n", "    \"/mnt/efs/augment/user/yury/smart_paste/data_new/v2/03_chat_stage/smart_paste_inputs.jsonl\"\n", ")\n", "\n", "OUTPUT_DIR = Path(\"/home/<USER>/sentry_data/new\")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "MAX_TOKEN_BUDGET = 32768 + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import read_examples\n", "\n", "examples = read_examples(INPUT_SAMPLES, 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import StructuredResponse\n", "from base.prompt_format.common import try_get_response_message_as_text\n", "\n", "useful_index = None\n", "useful_round = None\n", "for example_index, example in enumerate(examples):\n", "    for conversation_round, exchange in enumerate(example.chat_history):\n", "        response_text = try_get_response_message_as_text(exchange.response_text)\n", "        structured_response = StructuredResponse.from_response(exchange.response_text)\n", "        if len(structured_response.get_codeblocks()) >= 3:\n", "            print(f\"{example_index} {conversation_round}\")\n", "            useful_index = example_index\n", "            useful_round = conversation_round\n", "            break\n", "    if useful_index is not None:\n", "        break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import try_get_response_message_as_text\n", "from experimental.zhuoran.sentry.utils import StructuredResponse\n", "\n", "example = examples[useful_index]  # type: ignore\n", "exchange = example.chat_history[useful_round]\n", "response_text = try_get_response_message_as_text(exchange.response_text)\n", "structured_response = StructuredResponse.from_response(exchange.response_text)\n", "\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(exchange.response_text)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(\n", "        structured_response.render(\n", "            # backticks=\"random\",\n", "            tag_is_outer=False,\n", "        )\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from experimental.zhuoran.sentry.utils import format_example\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "max_token_budget = 32768 + 1\n", "tokenized_examples = []\n", "\n", "for index, example in enumerate(examples[:10]):\n", "    tokenized_examples_from_input = format_example(example, tokenizer)\n", "    for tokenized_example in tokenized_examples_from_input:\n", "        # fim_middle_index = tokenized_example.index(tokenizer.special_tokens.fim_middle)\n", "        # before_fim_middle = tokenized_example[:fim_middle_index]\n", "        # after_fim_middle = tokenized_example[fim_middle_index + 1 :]\n", "        # if len(before_fim_middle) - 1 != len(after_fim_middle):\n", "        #     before_fim_middle_string = tokenizer.detokenize(before_fim_middle)\n", "        #     after_fim_middle_string = tokenizer.detokenize(after_fim_middle)\n", "        #     print(f\"{index}: {len(before_fim_middle)=}, {len(after_fim_middle)=}\")\n", "        #     print()\n", "        #     print(before_fim_middle_string)\n", "        #     print()\n", "        #     print(after_fim_middle_string)\n", "        #     print()\n", "        #     assert False\n", "        tokenized_examples.append(tokenized_example)\n", "print(f\"{len(tokenized_examples)=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import StructuredResponse\n", "\n", "structured_response = StructuredResponse.from_response(\n", "    \"Understood. I'll refer to the excerpts for context, and ignore them for general questions.\"\n", ")\n", "print(structured_response)\n", "rendered_response = structured_response.render(\"unclosed\")\n", "print(rendered_response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import generate_training_data\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "generate_training_data(\n", "    input_path=INPUT_SAMPLES,\n", "    output_dir=OUTPUT_DIR,\n", "    tokenizer=tokenizer,\n", "    max_token_budget=MAX_TOKEN_BUDGET,\n", "    process_count=1,\n", "    validation_min_size=1,\n", "    validation_ratio=0.1,\n", "    limit=100,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from megatron.data.indexed_dataset import make_dataset\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "OUTPUT_DIR = Path(\"/home/<USER>/sentry_data/v3\")\n", "training_dataset = make_dataset(\n", "    str(OUTPUT_DIR / \"training\"), impl=\"mmap\", skip_warmup=True\n", ")\n", "assert training_dataset\n", "print(f\"Training dataset size: {len(training_dataset)}\")\n", "validation_dataset = make_dataset(\n", "    str(OUTPUT_DIR / \"validation\"), impl=\"mmap\", skip_warmup=True\n", ")\n", "assert validation_dataset\n", "print(f\"Validation dataset size: {len(validation_dataset)}\")\n", "\n", "assert training_dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{index=}\")\n", "tokens = training_dataset[index]\n", "# tokens = ds[100]\n", "tokens = [abs(x) for x in tokens]\n", "string = tokenizer.detokenize(tokens).replace(\"<|padding|>\", \"\")\n", "\n", "input_string = string.split(\"<|fim_middle|>\")[0].replace(\"<|fim_prefix|>\", \"\").strip()\n", "output_string = string.split(\"<|fim_middle|>\")[1].replace(\"<|endoftext|>\", \"\").strip()\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(input_string)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(output_string)\n", "index += 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Base prompt formatter test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_postprocess.sentry_prompt_formatter import SentryPromptFormatter\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "formatter = SentryPromptFormatter(tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_input = ChatPromptInput(\n", "    message=\"Hello, how can I help you?\",\n", "    path=\"\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
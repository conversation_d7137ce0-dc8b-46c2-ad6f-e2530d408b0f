import json
import os
import re
from pathlib import Path

import numpy as np

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from research.models import GenerationOptions
from research.models.fastforward_llama_models import FastForwardQwen25Coder_7B
from research.tools.chat_replay.replay_utils import count_tags

os.environ["CUDA_VISIBLE_DEVICES"] = "0"

TOKENIZER = Qwen25CoderTokenizer()
GENERATION_OPTIONS = GenerationOptions(
    temperature=0,
    top_k=0,
    top_p=0,
    max_generated_tokens=4096,
    stop_tokens=[TOKENIZER.special_tokens.eos, TOKENIZER.special_tokens.padding],
)


def get_model(version: int = 2):
    if version == 1:
        model = FastForwardQwen25Coder_7B(
            checkpoint_path=Path("/mnt/efs/augment/checkpoints/sentry/v1_ff"),
            checkpoint_sha256="8000000000000000000000000000000000000000000000000000000000000000",
            sequence_length=16384,
        )
    elif version == 2:
        model = FastForwardQwen25Coder_7B(
            checkpoint_path=Path("/mnt/efs/augment/checkpoints/sentry/v2_ff"),
            checkpoint_sha256="43ad0de53222f422c0699da3141ad8e35449ade4bec224765797fbcafa977bb1",
            sequence_length=16384,
        )
    elif version == 3:
        model = FastForwardQwen25Coder_7B(
            checkpoint_path=Path("/mnt/efs/augment/checkpoints/sentry/v3_ff"),
            checkpoint_sha256="8565ada24f6bb4c884ff00bc7eff418a73d17f6a237ea7f99f0da5619bbb1006",
            sequence_length=16384,
        )
    elif version == 4:
        model = FastForwardQwen25Coder_7B(
            checkpoint_path=Path("/mnt/efs/augment/checkpoints/sentry/v4_ff"),
            checkpoint_sha256="0acf92816e6ba8ff989f65daf480ca395ab944175b308ff5c0f5022292603599",
            sequence_length=16384,
        )
    else:
        raise ValueError(f"Unknown version: {version}")
    model.load()
    return model


def get_results(path: str):
    with open(path) as f:
        results = json.load(f)
    return results


def get_model_results(results, model_name: str):
    model_results = [result[model_name] for result in results]
    return model_results


def truncate_to_marker(
    tokens: np.ndarray, end_marker: int, start_marker: int | None = None
) -> list[int]:
    """Truncate tokens array to end marker, optionally starting search from after start marker.

    Args:
        tokens: Array of token IDs
        end_marker: Token ID to truncate to (inclusive)
        start_marker: Optional token ID to start search from (exclusive)

    Returns:
        List of tokens up to and including the first end_marker found after start_marker
    """
    if start_marker is not None:
        start_idx = np.where(tokens == start_marker)[0]
        if len(start_idx) == 0:
            return []
        search_start = start_idx[0] + 1
        tokens = tokens[search_start:]

    marker_index = np.where(tokens == end_marker)[0]
    if len(marker_index) == 0:
        return []

    if start_marker is not None:
        return tokens[: marker_index[0] + 1].tolist()
    else:
        return tokens[: marker_index[0] + 1].tolist()


def extract_prefix_to_middle(text: str) -> str:
    """Extract text between <|fim_prefix|> and <|fim_middle|>."""
    try:
        start = text.index("<|fim_prefix|>") + len("<|fim_prefix|>")
        end = text.index("<|fim_middle|>")
        return text[start:end].strip()
    except ValueError:
        return ""


def extract_middle_to_end(text: str) -> str:
    """Extract text between <|fim_middle|> and <|endoftext|>."""
    try:
        start = text.index("<|fim_middle|>") + len("<|fim_middle|>")
        end = text.index("<|endoftext|>")
        return text[start:end].strip()
    except ValueError:
        return ""


def has_extra_backticks(text: str) -> bool:
    """Check if there are more than 2 sets of backticks between augment code snippet tags.

    Args:
        text: String containing augment code snippets

    Returns:
        True if any pair of adjacent augment code snippet tags contains more than
        2 sets of backticks between them, False otherwise
    """
    # Find all pairs of opening and closing tags
    pattern = r"<augment_code_snippet[^>]*>(.*?)</augment_code_snippet>"
    matches = re.finditer(pattern, text, re.DOTALL)

    for match in matches:
        content = match.group(1)
        # Count sets of backticks (3 or more consecutive backticks)
        backtick_sets = re.findall(r"```+", content)
        if len(backtick_sets) > 2:
            return True

    return False


def get_input_tokens(response, tokenizer):
    special_tokens = tokenizer.special_tokens
    tokens = tokenizer.tokenize_safe(response)
    input_tokens = (
        [special_tokens.fim_prefix, special_tokens.newline]
        + tokens
        + [
            special_tokens.newline,
            special_tokens.fim_middle,
            special_tokens.newline,
        ]
    )
    return input_tokens


if __name__ == "__main__":
    results = get_results("/mnt/efs/augment/user/zhuoran/prompt/eval9.json")
    eval_root = Path(
        "/mnt/efs/augment/user/zhuoran/sentry/evals/v4/autoregressive_failures"
    )
    eval_root.mkdir(parents=True, exist_ok=True)
    output_file = eval_root / "results.txt"
    print(f"Writing to {output_file}")
    model = get_model(4)
    for model_name in ["v11v_bt4_v2", "v8"]:
        model_results = get_model_results(results, model_name)
        sentry_results = []
        originally_correct_accuracies = []
        orriginally_incorrect_accuracies = []
        accuracies = []
        result_file = eval_root / f"{model_name}.json"
        for result in model_results:
            response = result["response"]
            original_opening_count = result["opening_count"]
            original_closing_count = result["closing_count"]
            originally_paired = original_opening_count == original_closing_count
            input_tokens = get_input_tokens(response, TOKENIZER)
            generation = model.raw_generate(input_tokens, GENERATION_OPTIONS)
            opening_count, closing_count = count_tags(generation)
            paired = opening_count == closing_count
            three_backtick_response = response.replace("````", "```")
            unchanged = three_backtick_response == generation
            correct = (originally_paired and unchanged) or (
                not originally_paired and paired
            )
            sentry_results.append(
                {
                    "response": response,
                    "generation": generation,
                    "originally_paired": originally_paired,
                    "paired": paired,
                    "unchanged": unchanged,
                    "correct": correct,
                }
            )
            if originally_paired:
                originally_correct_accuracies.append(int(correct))
            else:
                orriginally_incorrect_accuracies.append(int(correct))
            accuracies.append(int(correct))
            average_originally_correct_accuracy = (
                sum(originally_correct_accuracies) / len(originally_correct_accuracies)
                if len(originally_correct_accuracies) > 0
                else 0
            )
            average_originally_incorrect_accuracy = (
                sum(orriginally_incorrect_accuracies)
                / len(orriginally_incorrect_accuracies)
                if len(orriginally_incorrect_accuracies) > 0
                else 0
            )
            average_accuracy = sum(accuracies) / len(accuracies)
            result_string = (
                f"{model_name} {len(sentry_results):>3}/{len(model_results)}"
                f" {'✅' if correct else '❌'}:"
                f" paired {'T' if originally_paired else 'F'}=>{'T' if paired else 'F'},"
                f" change {'N' if unchanged else 'Y'};"
                f" cum. accs.: {average_accuracy:>6.1%},"
                f" {average_originally_correct_accuracy:>6.1%},"
                f" {average_originally_incorrect_accuracy:>6.1%}"
            )
            print(result_string, flush=True)
            with open(output_file, "a") as f:
                f.write(result_string + "\n")
            with open(result_file, "w") as f:
                json.dump(sentry_results, f, indent=4)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "\n", "from determined.common.api import authentication, bindings\n", "from determined.common.api._util import canonicalize_master_url\n", "\n", "# Set up the client\n", "master_url = canonicalize_master_url(\"https://determined.gcp-us1.r.augmentcode.com\")\n", "session = authentication.login_with_cache(master_url)\n", "\n", "# Get trial logs\n", "trial_id = 10465\n", "trial_logs = bindings.get_TrialLogs(session, trialId=trial_id)\n", "\n", "# Combine all logs into a single string\n", "log_string = \"\"\n", "for log in tqdm(trial_logs):\n", "    log_string += log.message + \"\\n\"\n", "print(\n", "    f\"Trial {trial_id} has {len(log_string.splitlines()):,} lines and {len(log_string):,} characters of logs.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import concurrent.futures\n", "from typing import List\n", "import math\n", "\n", "from research.tools.chat_replay.replay_utils import run_claude\n", "\n", "\n", "def process_logs_in_parallel(\n", "    log_string: str,\n", "    chunk_size: int = 300_000,\n", "    max_workers: int | None = None,\n", ") -> List[str]:\n", "    num_chunks = math.ceil(len(log_string) / chunk_size)\n", "    chunks = [\n", "        log_string[i * chunk_size : (i + 1) * chunk_size] for i in range(num_chunks)\n", "    ][:1]\n", "\n", "    max_workers = max_workers or len(chunks)\n", "\n", "    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:\n", "        futures = [\n", "            executor.submit(\n", "                run_claude,\n", "                chunk,\n", "                system_message=\"You are an AI assistant that helps people find information. You are given a log of a Python program. You are asked to find the actual error in the log.\",\n", "                retry_limit=3,\n", "            )\n", "            for chunk in chunks\n", "        ]\n", "        future_to_chunk = {future: i for i, future in enumerate(futures)}\n", "\n", "        # Collect results as they complete\n", "        results = [\"\"] * len(chunks)  # Pre-allocate list\n", "        for completion_index, future in enumerate(\n", "            concurrent.futures.as_completed(futures)\n", "        ):\n", "            print(f\"Received result for chunk {completion_index}/{len(chunks)}\")\n", "            chunk_index = future_to_chunk[future]\n", "            try:\n", "                result = future.result()[0]\n", "                results[chunk_index] = result\n", "            except Exception as e:\n", "                results[chunk_index] = f\"Error processing chunk {chunk_index}: {str(e)}\"\n", "\n", "    return results\n", "\n", "\n", "results = process_logs_in_parallel(log_string)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Result {index}/{len(results)}:\")\n", "print()\n", "print(results[index])\n", "index += 1"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
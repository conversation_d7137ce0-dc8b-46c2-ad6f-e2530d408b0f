determined:
  description: "Sentry training."
  workspace: Dev
  project: zhuoran

augment:
  project_group: finetuning
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/qwen25coder_7b.py

fastbackward_args:
  loss_mask_policy: fim
  batch_size: 1
  gradient_accumulation_steps: 4
  warmup_iters: 32
  learning_rate: 1e-5
  min_lr: 1e-6
  decay_lr: True
  max_epochs: 1
  # max_iters: 1000
  eval_interval: 50
  block_size: 16384
  use_activation_checkpointing: True

  train_data_path: /mnt/efs/augment/user/zhuoran/sentry/data/v4/training
  eval_data_path: /mnt/efs/augment/user/zhuoran/sentry/data/v4/validation

  checkpoint_optimizer_state: False
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B-Instruct

  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32
  model_parallel_size: 2
  use_sequence_parallel: True

  run_name: sentry_v4
  wandb_project: sentry

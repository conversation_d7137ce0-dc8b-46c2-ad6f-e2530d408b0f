{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import json\n", "\n", "# import tqdm\n", "\n", "# chat_response_strings = set()\n", "# for line in tqdm.tqdm(lines):\n", "#     example = json.loads(line)\n", "#     chat_response_strings.add(example[\"aux\"][\"chat_response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# with open(\"/home/<USER>/responses.json\", \"w\") as f:\n", "#     json.dump(list(chat_response_strings), f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"/home/<USER>/responses.json\", \"r\") as f:\n", "    chat_response_strings = set(json.load(f))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "from experimental.zhuoran.sentry.utils import StructuredResponse\n", "\n", "structured_responses = []\n", "for response in tqdm.tqdm(chat_response_strings):\n", "    structured_response = StructuredResponse.from_response(response)\n", "    if structured_response:\n", "        structured_responses.append(structured_response)\n", "\n", "print(f\"{len(structured_responses)} / {len(chat_response_strings)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import (\n", "    RawNode,\n", "    <PERSON><PERSON>r<PERSON><PERSON>,\n", "    TextNode,\n", "    CodeBlockNode,\n", ")\n", "\n", "error_counts = {}\n", "language_counts = {}\n", "text_count = 0\n", "codeblock_count = 0\n", "for example_index, structured_response in enumerate(structured_responses):\n", "    for node in structured_response.nodes:\n", "        if isinstance(node, RawNode):\n", "            response = node.response\n", "        elif isinstance(node, ErrorNode):\n", "            error_counts[node.error] = error_counts.get(node.error, 0) + 1\n", "            if node.error not in {\"Mismatching tags\", \"XML tag leak\"}:\n", "                print(f\"{node.error}: {example_index}\")\n", "        elif isinstance(node, TextNode):\n", "            text_count += 1\n", "        elif isinstance(node, CodeBlockNode):\n", "            codeblock_count += 1\n", "            language_counts[node.language] = language_counts.get(node.language, 0) + 1\n", "\n", "error_counts = dict(sorted(error_counts.items(), key=lambda x: x[1], reverse=True))\n", "language_counts = dict(\n", "    sorted(language_counts.items(), key=lambda x: x[1], reverse=True)\n", ")\n", "print(error_counts)\n", "print(language_counts)\n", "print(f\"text_count: {text_count}\")\n", "print(f\"codeblock_count: {codeblock_count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "index = 0\n", "print([node.type for node in structured_responses[index].nodes])\n", "for codeblock in structured_responses[index].nodes[:-1]:\n", "    print(codeblock.to_json(indent=4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(structured_responses[index].render(\"unclosed\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(structured_responses[index].nodes[-1].response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.zhuoran.sentry.utils import StructuredResponse\n", "\n", "index = 240627\n", "chat_response_string_list = list(chat_response_strings)\n", "chat_response_string = chat_response_string_list[index]\n", "structured_response = StructuredResponse.from_response(chat_response_string)\n", "rendered_response = structured_response.render(\"unclosed\")\n", "with open(\"/home/<USER>/a.md\", \"w\") as f:\n", "    f.write(chat_response_string)\n", "with open(\"/home/<USER>/b.md\", \"w\") as f:\n", "    f.write(rendered_response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["StructuredResponse.from_response([])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
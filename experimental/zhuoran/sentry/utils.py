"""Utils for Sentry post-processing."""

import json
import math
import multiprocessing
from copy import deepcopy
from dataclasses import dataclass, field
from pathlib import Path
from random import Random
from typing import Literal, Union

import dataclasses_json
import numpy as np
import torch
import tqdm
from dataclasses_json import dataclass_json
from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder

from base.prompt_format.common import ResponseMessage, try_get_response_message_as_text
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.tokenizers.tokenizer import Tokenizer
from experimental.yury.smart_paste_lib.types import ResearchSmartPastePromptInput

FUZZ_CONFIGS = {
    "none": {
        "closing_tag": "all",
        "backticks": "all",
        "tag_is_outer": True,
        "ending": "none",
    },
    "unclosed": {
        "closing_tag": "none",
        "backticks": "all",
        "tag_is_outer": True,
        "ending": "none",
    },
    "random_unclosed": {
        "closing_tag": "random",
        "backticks": "all",
        "tag_is_outer": True,
        "ending": "none",
    },
    "no_backticks": {
        "closing_tag": "all",
        "backticks": "none",
        "tag_is_outer": True,
        "ending": "none",
    },
    "random_no_backticks": {
        "closing_tag": "all",
        "backticks": "random",
        "tag_is_outer": True,
        "ending": "none",
    },
    "inner_tag": {
        "closing_tag": "none",
        "backticks": "all",
        "tag_is_outer": False,
        "ending": "none",
    },
    "ending": {
        "closing_tag": "all",
        "backticks": "all",
        "tag_is_outer": True,
        "ending": "all",
    },
    "random_ending": {
        "closing_tag": "all",
        "backticks": "all",
        "tag_is_outer": True,
        "ending": "random",
    },
}

DEFAULT_FUZZ_PROBABILITIES = {
    "unclosed": 0.3,
    "random_unclosed": 0.1,
    "no_backticks": 0.05,
    "random_no_backticks": 0.05,
    "inner_tag": 0.1,
    "ending": 0.05,
    "random_ending": 0.05,
}


ENDINGS = [
    ending
    for text in [
        "Additional implementation details below",
        "Rest of implementation continues",
        "subsequent code follows similar pattern",
        "Further method definitions and logic follow",
        "remaining code handles edge cases and cleanup",
        "rest of the implementation remains the same",
        "End of snippet",
        "More code omitted",
    ]
    for ending in (text, f"[{text}]")
]


@dataclass_json
@dataclass
class RawNode(dataclasses_json.DataClassJsonMixin):
    """Represents the original raw response."""

    response: str

    @property
    def type(self) -> Literal["raw"]:
        """Returns the type of the node."""
        return "raw"


@dataclass_json
@dataclass
class ErrorNode(dataclasses_json.DataClassJsonMixin):
    """Represents an error in parsing."""

    error: str
    info: str | None = None

    @property
    def type(self) -> Literal["error"]:
        """Returns the type of the node."""
        return "error"


@dataclass_json
@dataclass
class TextNode(dataclasses_json.DataClassJsonMixin):
    """Represents a text segment."""

    content: str

    @property
    def type(self) -> Literal["text"]:
        """Returns the type of the node."""
        return "text"

    def render(self) -> str:
        """Renders the text node."""
        return self.content


@dataclass_json
@dataclass
class CodeBlockNode(dataclasses_json.DataClassJsonMixin):
    """Represents a code block with metadata."""

    path: str
    mode: str
    language: str
    content: str

    @property
    def type(self) -> Literal["codeblock"]:
        """Returns the type of the node."""
        return "codeblock"

    def render(
        self,
        closing_tag: bool = True,
        has_backticks: bool = True,
        backtick_count: int = 3,
        tag_is_outer: bool = True,
        ending_string: str = "",
    ) -> str:
        """Renders the code block with optional closing tag."""
        rendering = ""
        rendering += "\n"
        if tag_is_outer:
            rendering += (
                f'<augment_code_snippet path="{self.path}" mode="{self.mode}">\n'
            )
        if has_backticks:
            rendering += f"{'`' * backtick_count}{self.language}\n"
        if not tag_is_outer:
            rendering += (
                f'<augment_code_snippet path="{self.path}" mode="{self.mode}">\n'
            )
        rendering += self.content
        rendering += "\n"
        if closing_tag and not tag_is_outer:
            rendering += "</augment_code_snippet>\n"
        if has_backticks:
            rendering += f"{'`' * backtick_count}\n"
        if ending_string:
            rendering += f"{ending_string}\n"
        if closing_tag and tag_is_outer:
            rendering += "</augment_code_snippet>\n"
        return rendering


ResponseNode = Union[RawNode, ErrorNode, TextNode, CodeBlockNode]


@dataclass_json
@dataclass
class StructuredResponse(dataclasses_json.DataClassJsonMixin):
    """Represents a structured response containing a sequence of nodes."""

    nodes: list[ResponseNode] = field(default_factory=list)

    def get_codeblocks(self) -> list[CodeBlockNode]:
        """Returns all code block nodes in the response."""
        return [node for node in self.nodes if isinstance(node, CodeBlockNode)]

    def has_codeblocks(self) -> bool:
        """Checks if the response contains any code blocks."""
        return len(self.get_codeblocks()) > 0

    def render(
        self,
        closing_tag: str = "all",
        backticks: str = "all",
        backtick_count: int | float = 3,
        tag_is_outer: bool = True,
        ending: str = "none",
    ) -> str:
        """Renders the structured response with optional fuzzing."""
        codeblock_count = len(self.get_codeblocks())
        if closing_tag == "all":
            closing_tags = [True] * codeblock_count
        elif closing_tag == "random":
            closing_tags = np.random.choice([True, False], size=codeblock_count)
        elif closing_tag == "none":
            closing_tags = [False] * codeblock_count
        else:
            raise ValueError(f"Unknown closing_tag: {closing_tag}")

        if backticks == "all":
            has_backticks = [True] * codeblock_count
        elif backticks == "random":
            has_backticks = np.random.choice([True, False], size=codeblock_count)
        elif backticks == "none":
            has_backticks = [False] * codeblock_count
        else:
            raise ValueError(f"Unknown backticks: {backticks}")

        if isinstance(backtick_count, int):
            backtick_counts = [backtick_count] * codeblock_count
        elif isinstance(backtick_count, float):
            backtick_counts = [
                np.random.choice(
                    [math.floor(backtick_count), math.ceil(backtick_count)],
                    p=[
                        math.ceil(backtick_count) - backtick_count,
                        backtick_count - math.floor(backtick_count),
                    ],
                )
                for _ in range(codeblock_count)
            ]

        if ending == "all":
            ending_strings = np.random.choice(ENDINGS, size=codeblock_count)
        elif ending == "random":
            ending_strings = [
                np.random.choice(ENDINGS) if np.random.random() < 0.5 else ""
                for _ in range(codeblock_count)
            ]
        elif ending == "none":
            ending_strings = [""] * codeblock_count
        else:
            raise ValueError(f"Unknown ending: {ending}")

        response_copy = deepcopy(self)

        rendered_response = ""
        codeblock_index = 0
        for node in response_copy.nodes:
            if isinstance(node, TextNode):
                rendered_response += node.render()
            elif isinstance(node, CodeBlockNode):
                rendered_response += node.render(
                    closing_tag=closing_tags[codeblock_index],
                    has_backticks=has_backticks[codeblock_index],
                    backtick_count=backtick_counts[codeblock_index],
                    tag_is_outer=tag_is_outer,
                    ending_string=ending_strings[codeblock_index],
                )
                codeblock_index += 1
        return rendered_response

    @classmethod
    def from_response(cls, response: ResponseMessage) -> "StructuredResponse":
        """Creates a StructuredResponse from a ResponseMessage.

        Parses the response text into a structured format, identifying code blocks and text segments.
        Code blocks must be properly formatted with augment_code_snippet tags.
        """
        response_text = try_get_response_message_as_text(response)
        if not response_text:
            return cls([])

        lines = response_text.splitlines()
        nodes: list[ResponseNode] = [RawNode(response=response_text)]

        openings = []
        closings = []
        for line_index, line in enumerate(lines):
            if line.startswith("<augment_code_snippet"):
                if line_index + 1 >= len(lines) or not lines[line_index + 1].startswith(
                    "```"
                ):
                    return cls([ErrorNode(error="Bad opening tag"), *nodes])
                backtick_count = len(lines[line_index + 1]) - len(
                    lines[line_index + 1].lstrip("`")
                )
                openings.append((line_index, backtick_count))
            elif line.endswith("</augment_code_snippet>"):
                if (
                    line_index - 1 < 0
                    or not lines[line_index - 1].endswith("```")
                    or lines[line_index - 1].rstrip("`")
                ):
                    return cls([ErrorNode(error="Bad closing tag"), *nodes])
                backtick_count = len(lines[line_index - 1])
                closings.append((line_index, backtick_count))
            elif "augment_code_snippet" in line:
                return cls([ErrorNode(error="XML tag leak"), *nodes])

        if len(openings) != len(closings):
            return cls(
                [
                    ErrorNode(
                        error="Mismatching tags",
                        info=f"{len(openings)} openings, {len(closings)} closings",
                    ),
                    *nodes,
                ]
            )

        previous_closing_index = -1
        for opening, closing in zip(openings, closings):
            opening_index, opening_backtick_count = opening
            closing_index, closing_backtick_count = closing
            if opening_backtick_count != closing_backtick_count:
                return cls(
                    [
                        ErrorNode(
                            error="Mismatching backticks",
                            info=f"{opening_backtick_count} opening, {closing_backtick_count} closing",
                        ),
                        *nodes,
                    ]
                )
            if closing_index - opening_index <= 2:
                return cls(
                    [
                        ErrorNode(
                            error="Mismatching tags",
                            info=f"Opening and closing tags are too close: {opening_index} - {closing_index}",
                        ),
                        *nodes,
                    ]
                )

            if opening_index - previous_closing_index > 1:
                nodes.append(
                    TextNode(
                        content="\n".join(
                            lines[previous_closing_index + 1 : opening_index]
                        )
                    )
                )
            nodes.append(
                CodeBlockNode(
                    path=lines[opening_index].split('path="')[1].split('"')[0],
                    mode=lines[opening_index].split('mode="')[1].split('"')[0],
                    language=lines[opening_index + 1].lstrip("`"),
                    content="\n".join(lines[opening_index + 2 : closing_index - 1]),
                )
            )
            previous_closing_index = closing_index
        if len(lines) - 1 - previous_closing_index > 0:
            nodes.append(
                TextNode(content="\n".join(lines[previous_closing_index + 1 :]))
            )

        nodes = nodes[1:] + nodes[:1]
        return cls(nodes)


def select_weighted_choice(
    random_number: float, probabilities: dict[str, float], default: str = "none"
) -> str:
    """Select a choice based on weighted probabilities.

    Args:
        random_val: Random value between 0 and 1
        probabilities: Mapping of choices to their probabilities
        default: Default choice if no probability threshold is met

    Returns:
        Selected choice based on the weighted probabilities
    """
    assert sum(probabilities.values()) <= 1, "Probabilities must sum to <= 1"
    assert 0 <= random_number <= 1, "Random value must be between 0 and 1"

    cumulative_prob = 0
    for choice, prob in probabilities.items():
        cumulative_prob += prob
        if random_number <= cumulative_prob:
            return choice
    return default


def format_example(
    example_input: ResearchSmartPastePromptInput,
    tokenizer: Tokenizer,
    fuzz_probabilities: dict[str, float],
    backtick_count: int | float = 3,
    random_seed: int = 1216,
) -> list[list[int]]:
    special_tokens = tokenizer.special_tokens

    for key in fuzz_probabilities.keys():
        assert key in FUZZ_CONFIGS, f"Unknown fuzz method: {key}"
    total_fuzz_probability = sum(fuzz_probabilities.values())
    assert total_fuzz_probability <= 1, total_fuzz_probability

    rng = Random(random_seed)
    np.random.seed(random_seed + 1216)
    formatted_examples = []
    for exchange in example_input.chat_history:
        response_text = try_get_response_message_as_text(exchange.response_text)
        if not response_text:
            continue

        structured_response = StructuredResponse.from_response(exchange.response_text)
        if not structured_response.has_codeblocks():
            continue

        random_number = rng.random()
        fuzz_method = select_weighted_choice(random_number, fuzz_probabilities)
        fuzzed_response = structured_response.render(
            **FUZZ_CONFIGS[fuzz_method], backtick_count=backtick_count
        )

        input_tokens = tokenizer.tokenize_safe(fuzzed_response)
        output_tokens = tokenizer.tokenize_safe(response_text)

        tokens = (
            [special_tokens.fim_prefix, special_tokens.newline]
            + input_tokens
            + [
                special_tokens.newline,
                special_tokens.fim_middle,
                special_tokens.newline,
            ]
            + output_tokens
            + [special_tokens.eos]
        )

        formatted_examples.append(tokens)

    return formatted_examples


def read_examples(
    input_path: str | Path, limit: int = 0
) -> list[ResearchSmartPastePromptInput]:
    examples = []
    input_path = Path(input_path)

    with input_path.open("r") as f:
        for line in tqdm.tqdm(f):
            datum = json.loads(line)
            prompt_input = ResearchSmartPastePromptInput.from_dict(datum)
            prompt_input.aux["index"] = len(examples)
            examples.append(prompt_input)
            if limit and len(examples) >= limit:
                break

    print(f"Loaded {len(examples)} samples")
    np.random.seed(31415)
    np.random.shuffle(examples)
    return examples


class Processor:
    def __init__(
        self,
        tokenizer: Tokenizer,
        max_token_budget: int,
        fuzz_probabilities: dict[str, float] | None = None,
        backtick_count: int | float = 3,
    ):
        self.tokenizer = tokenizer
        self.max_token_budget = max_token_budget
        self.fuzz_probabilities = fuzz_probabilities or DEFAULT_FUZZ_PROBABILITIES
        self.backtick_count = backtick_count

    def initialize(self):
        """Initialize processor-wide resources.."""
        Processor.tokenizer = Qwen25CoderTokenizer()
        Processor.max_token_budget = self.max_token_budget

    def __call__(self, example: ResearchSmartPastePromptInput):
        tokenized_examples = format_example(
            example,
            self.tokenizer,
            fuzz_probabilities=self.fuzz_probabilities,
            backtick_count=self.backtick_count,
            random_seed=example.aux["index"],
        )
        padded_examples = []
        for tokenized_example in tokenized_examples:
            if len(tokenized_example) < self.max_token_budget:
                tokenized_example.extend(
                    [-self.tokenizer.special_tokens.padding]
                    * (self.max_token_budget - len(tokenized_example))
                )
            assert len(tokenized_example) == self.max_token_budget
            padded_examples.append(tokenized_example)
        example_tensor = torch.tensor(padded_examples)
        return example_tensor


def pad(tokens: list[int], padding_token: int, max_token_budget: int):
    if len(tokens) < max_token_budget:
        tokens.extend([padding_token] * (max_token_budget - len(tokens)))
    assert len(tokens) == max_token_budget
    return tokens


def generate_training_data(
    input_path: str | Path,
    output_dir: str | Path,
    tokenizer: Tokenizer,
    max_token_budget: int,
    process_count: int = 24,
    validation_min_size: int = 1000,
    validation_ratio: float = 0.01,
    fuzz_probabilities: dict[str, float] | None = None,
    backtick_count: int | float = 3,
    limit: int = 0,
):
    output_dir = Path(output_dir)
    assert output_dir.is_dir()

    examples = read_examples(input_path, limit=limit)
    validation_size = max(int(validation_ratio * len(examples)), validation_min_size)

    processor = Processor(
        tokenizer=tokenizer,
        max_token_budget=max_token_budget,
        fuzz_probabilities=fuzz_probabilities,
        backtick_count=backtick_count,
    )

    validation_builder = MMapIndexedDatasetBuilder(
        str(output_dir / "validation") + ".bin", dtype=np.int32
    )
    training_builder = MMapIndexedDatasetBuilder(
        str(output_dir / "training") + ".bin", dtype=np.int32
    )

    validation_count = 0

    with multiprocessing.Pool(process_count, initializer=processor.initialize) as pool:
        pbar = tqdm.tqdm(pool.imap_unordered(processor, examples), total=len(examples))
        for padded_examples in pbar:
            for padded_example in padded_examples:
                if validation_count < validation_size:
                    validation_builder.add_item(padded_example)
                    validation_builder.end_document()
                    validation_count += 1
                else:
                    training_builder.add_item(padded_example)
                    training_builder.end_document()

    print(f"Finalizing {validation_count} validation samples")
    validation_builder.finalize(str(output_dir / "validation") + ".idx")
    print(f"Finalizing {len(examples) - validation_count} training samples")
    training_builder.finalize(str(output_dir / "training") + ".idx")

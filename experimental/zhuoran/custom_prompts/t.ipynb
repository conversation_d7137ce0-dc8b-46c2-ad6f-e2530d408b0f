{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import openai\n", "\n", "augment_client = openai.OpenAI(\n", "    api_key=\"********************************************************************************************************************************************************************\",\n", ")\n", "personal_client = openai.OpenAI(\n", "    api_key=\"********************************************************************************************************************************************************************\",\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def list_models(client):\n", "    model_ids = []\n", "    for model in client.models.list().data:\n", "        model_ids.append(model.id)\n", "    for model_id in sorted(model_ids):\n", "        print(model_id)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["babbage-002\n", "dall-e-2\n", "dall-e-3\n", "davinci-002\n", "gpt-3.5-turbo\n", "gpt-3.5-turbo-0125\n", "gpt-3.5-turbo-1106\n", "gpt-3.5-turbo-16k\n", "gpt-3.5-turbo-instruct\n", "gpt-3.5-turbo-instruct-0914\n", "gpt-4o-mini\n", "gpt-4o-mini-2024-07-18\n", "gpt-4o-realtime-preview\n", "gpt-4o-realtime-preview-2024-10-01\n", "o1-mini\n", "o1-mini-2024-09-12\n", "o1-preview\n", "o1-preview-2024-09-12\n", "omni-moderation-2024-09-26\n", "omni-moderation-latest\n", "text-embedding-3-large\n", "text-embedding-3-small\n", "text-embedding-ada-002\n", "tts-1\n", "tts-1-1106\n", "tts-1-hd\n", "tts-1-hd-1106\n", "whisper-1\n"]}], "source": ["list_models(augment_client)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["babbage-002\n", "chatgpt-4o-latest\n", "dall-e-2\n", "dall-e-3\n", "davinci-002\n", "gpt-3.5-turbo\n", "gpt-3.5-turbo-0125\n", "gpt-3.5-turbo-1106\n", "gpt-3.5-turbo-16k\n", "gpt-3.5-turbo-instruct\n", "gpt-3.5-turbo-instruct-0914\n", "gpt-4\n", "gpt-4-0125-preview\n", "gpt-4-0613\n", "gpt-4-1106-preview\n", "gpt-4-turbo\n", "gpt-4-turbo-2024-04-09\n", "gpt-4-turbo-preview\n", "gpt-4o\n", "gpt-4o-2024-05-13\n", "gpt-4o-2024-08-06\n", "gpt-4o-2024-11-20\n", "gpt-4o-audio-preview\n", "gpt-4o-audio-preview-2024-10-01\n", "gpt-4o-mini\n", "gpt-4o-mini-2024-07-18\n", "gpt-4o-realtime-preview\n", "gpt-4o-realtime-preview-2024-10-01\n", "o1-mini\n", "o1-mini-2024-09-12\n", "o1-preview\n", "o1-preview-2024-09-12\n", "omni-moderation-2024-09-26\n", "omni-moderation-latest\n", "text-embedding-3-large\n", "text-embedding-3-small\n", "text-embedding-ada-002\n", "tts-1\n", "tts-1-1106\n", "tts-1-hd\n", "tts-1-hd-1106\n", "whisper-1\n"]}], "source": ["list_models(personal_client)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"ename": "NotFoundError", "evalue": "Error code: 404 - {'error': {'message': 'The model `gpt-4o` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNotFoundError\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 6\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Dict, Any\n\u001b[1;32m      4\u001b[0m start_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[0;32m----> 6\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43maugment_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mchat\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompletions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# model=\"o1-preview\",\u001b[39;49;00m\n\u001b[1;32m      8\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mgpt-4o\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\n\u001b[1;32m     10\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\u001b[39;49;00m\n\u001b[1;32m     11\u001b[0m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrole\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43muser\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcontent\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mHello!\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     12\u001b[0m \u001b[43m    \u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m     13\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     15\u001b[0m latency \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime() \u001b[38;5;241m-\u001b[39m start_time\n\u001b[1;32m     17\u001b[0m \u001b[38;5;66;03m# Calculate cost based on token usage\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/openai/_utils/_utils.py:301\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    299\u001b[0m             msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    300\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[0;32m--> 301\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/openai/resources/chat/completions.py:598\u001b[0m, in \u001b[0;36mCompletions.create\u001b[0;34m(self, messages, model, frequency_penalty, function_call, functions, logit_bias, max_tokens, n, presence_penalty, response_format, seed, stop, stream, temperature, tool_choice, tools, top_p, user, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[1;32m    551\u001b[0m \u001b[38;5;129m@required_args\u001b[39m([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m    552\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcreate\u001b[39m(\n\u001b[1;32m    553\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    596\u001b[0m     timeout: \u001b[38;5;28mfloat\u001b[39m \u001b[38;5;241m|\u001b[39m httpx\u001b[38;5;241m.\u001b[39mTimeout \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m|\u001b[39m NotGiven \u001b[38;5;241m=\u001b[39m NOT_GIVEN,\n\u001b[1;32m    597\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ChatCompletion \u001b[38;5;241m|\u001b[39m Stream[ChatCompletionChunk]:\n\u001b[0;32m--> 598\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    599\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/chat/completions\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    600\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    601\u001b[0m \u001b[43m            \u001b[49m\u001b[43m{\u001b[49m\n\u001b[1;32m    602\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmessages\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    603\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmodel\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    604\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfrequency_penalty\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfrequency_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    605\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfunction_call\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunction_call\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    606\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfunctions\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mfunctions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    607\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mlogit_bias\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mlogit_bias\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    608\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmax_tokens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    609\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mn\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    610\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mpresence_penalty\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mpresence_penalty\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    611\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mresponse_format\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mresponse_format\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    612\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mseed\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    613\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstop\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    614\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstream\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    615\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtemperature\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    616\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtool_choice\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    617\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtools\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    618\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtop_p\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    619\u001b[0m \u001b[43m                \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43muser\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    620\u001b[0m \u001b[43m            \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    621\u001b[0m \u001b[43m            \u001b[49m\u001b[43mcompletion_create_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCompletionCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    622\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    623\u001b[0m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    624\u001b[0m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\n\u001b[1;32m    625\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    626\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mChatCompletion\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    627\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    628\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mChatCompletionChunk\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    629\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/openai/_base_client.py:1096\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[0;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1082\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[1;32m   1083\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1084\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1091\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1092\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[1;32m   1093\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[1;32m   1094\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[1;32m   1095\u001b[0m     )\n\u001b[0;32m-> 1096\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/openai/_base_client.py:856\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    847\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mrequest\u001b[39m(\n\u001b[1;32m    848\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    849\u001b[0m     cast_to: Type[ResponseT],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    854\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    855\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[0;32m--> 856\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    857\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    858\u001b[0m \u001b[43m        \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    859\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    860\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    861\u001b[0m \u001b[43m        \u001b[49m\u001b[43mremaining_retries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mremaining_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    862\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/openai/_base_client.py:908\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m    905\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mis_closed:\n\u001b[1;32m    906\u001b[0m         err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mread()\n\u001b[0;32m--> 908\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    909\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m httpx\u001b[38;5;241m.\u001b[39mTimeoutException \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m    910\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m response \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[0;31mNotFoundError\u001b[0m: Error code: 404 - {'error': {'message': 'The model `gpt-4o` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'param': None, 'code': 'model_not_found'}}"]}], "source": ["import time\n", "from typing import Dict, Any\n", "\n", "start_time = time.time()\n", "\n", "response = augment_client.chat.completions.create(\n", "    # model=\"o1-preview\",\n", "    model=\"gpt-4o\",\n", "    messages=[\n", "        # {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"Hello!\"},\n", "    ],\n", ")\n", "\n", "latency = time.time() - start_time\n", "\n", "# Calculate cost based on token usage\n", "usage = response.usage\n", "assert usage\n", "prompt_tokens = usage.prompt_tokens\n", "completion_tokens = usage.completion_tokens\n", "total_tokens = usage.total_tokens\n", "\n", "# Log metrics\n", "print(f\"Latency: {latency:.2f} seconds\")\n", "print(\"Token Usage:\")\n", "print(f\"  - Prompt tokens: {prompt_tokens}\")\n", "print(f\"  - Completion tokens: {completion_tokens}\")\n", "print(f\"  - Total tokens: {total_tokens}\")\n", "\n", "# If you want to calculate actual cost, you would need to multiply by the per-token rate\n", "# Example rates (you should adjust these based on your actual model pricing):\n", "# prompt_rate = 0.01    # $ per 1K tokens\n", "# completion_rate = 0.03 # $ per 1K tokens\n", "# total_cost = (prompt_tokens * prompt_rate + completion_tokens * completion_rate) / 1000\n", "# print(f\"Estimated cost: ${total_cost:.4f}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-12-05 02:19:15\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1minitialized for model claude-3-5-sonnet@20240620\u001b[0m\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "# MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "vertex_ai_client = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "\n", "def run_claude(message_or_prompt, system_message):\n", "    if isinstance(message_or_prompt, str):\n", "        message = message_or_prompt\n", "    else:\n", "        message = message_or_prompt.message\n", "        system_message = message_or_prompt.system_prompt\n", "    response = vertex_ai_client.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    )\n", "    return response.content[0].text, response\n", "\n", "\n", "def run_claude_stream(message, system_message):\n", "    with vertex_ai_client.client.messages.stream(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": message}],\n", "        system=system_message,\n", "        temperature=TEMPERAURE,\n", "    ) as stream:\n", "        for text in stream.text_stream:\n", "            yield text"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Latency: 1.57 seconds\n", "Token Usage:\n", "  - Prompt tokens: 9\n", "  - Completion tokens: 17\n", "  - Total tokens: 26\n", "Estimated cost: $0.0006\n"]}], "source": ["start_time = time.time()\n", "\n", "response = vertex_ai_client.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=[{\"role\": \"user\", \"content\": \"Yo\"}],\n", "    system=\"\",\n", "    temperature=TEMPERAURE,\n", ")\n", "\n", "latency = time.time() - start_time\n", "\n", "# Calculate cost based on token usage\n", "usage = response.usage\n", "prompt_tokens = usage.input_tokens\n", "completion_tokens = usage.output_tokens\n", "total_tokens = prompt_tokens + completion_tokens\n", "\n", "# Log metrics\n", "print(f\"Latency: {latency:.2f} seconds\")\n", "print(\"Token Usage:\")\n", "print(f\"  - Prompt tokens: {prompt_tokens}\")\n", "print(f\"  - Completion tokens: {completion_tokens}\")\n", "print(f\"  - Total tokens: {total_tokens}\")\n", "\n", "# Calculate cost (adjust rates based on your model's pricing)\n", "prompt_rate = 0.01  # $ per 1K tokens\n", "completion_rate = 0.03  # $ per 1K tokens\n", "total_cost = (prompt_tokens * prompt_rate + completion_tokens * completion_rate) / 1000\n", "print(f\"Estimated cost: ${total_cost:.4f}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello\n", "! How can I assist you today?\n", "Latency: 0.45 seconds\n", "Token Usage:\n", "  - Input tokens: 9\n", "  - Output tokens: 12\n", "  - Total tokens: 21\n", "Estimated cost: $0.0004\n"]}], "source": ["start_time = time.time()\n", "collected_text = []\n", "\n", "with vertex_ai_client.client.messages.stream(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=[{\"role\": \"user\", \"content\": \"Yo\"}],\n", "    system=\"\",\n", "    temperature=TEMPERAURE,\n", ") as stream:\n", "    for text in stream.text_stream:\n", "        collected_text.append(text)\n", "        print(text)\n", "\n", "latency = time.time() - start_time\n", "\n", "# Get usage statistics from the final message\n", "usage = stream.get_final_message().usage\n", "input_tokens = usage.input_tokens\n", "output_tokens = usage.output_tokens\n", "total_tokens = input_tokens + output_tokens\n", "\n", "# Log metrics\n", "print(f\"Latency: {latency:.2f} seconds\")\n", "print(\"Token Usage:\")\n", "print(f\"  - Input tokens: {input_tokens}\")\n", "print(f\"  - Output tokens: {output_tokens}\")\n", "print(f\"  - Total tokens: {total_tokens}\")\n", "\n", "# Calculate cost (adjust rates based on your model's pricing)\n", "input_rate = 0.01  # $ per 1K tokens\n", "output_rate = 0.03  # $ per 1K tokens\n", "total_cost = (input_tokens * input_rate + output_tokens * output_rate) / 1000\n", "print(f\"Estimated cost: ${total_cost:.4f}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
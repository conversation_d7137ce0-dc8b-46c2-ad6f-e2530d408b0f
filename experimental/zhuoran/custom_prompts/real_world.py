import os
import subprocess

import yaml

# Read the template
template_path = "experimental/zhuoran/custom_prompts/template.yaml"
with open(template_path, "r") as file:
    template = yaml.safe_load(file)

directory = "/mnt/efs/augment/user/zhuoran/custom_prompts/cursor"
for filename in os.listdir(directory):
    basename, extension = os.path.splitext(filename)
    if extension != ".md":
        continue
    # if basename != "all":
    #     continue

    template["system"]["model_name"] = "claude-sonnet-3-5-16k-v6-chat"
    template["task"]["user_guidelines_path"] = os.path.join(directory, filename)
    template["task"]["custom_prompt_as_user_message"] = True
    template["determined"]["name"] = f"custom_prompts_{basename}_r5"

    os.makedirs(directory, exist_ok=True)
    output_path = os.path.join(directory, f"{basename}.yaml")

    with open(output_path, "w") as file:
        yaml.dump(template, file, default_flow_style=False)

    print(f"Config has been written to {output_path}")

    with open(output_path, "r") as file:
        config = yaml.safe_load(file)

    print(yaml.dump(config))

    command = [
        "python",
        "/home/<USER>/augment/research/eval/eval.py",
        output_path,
        "--summary_path",
        f"/mnt/efs/augment/user/zhuoran/summaries/custom_prompts/{template['determined']['name']}.json",
    ]
    command += ["--wait_for_completion"]
    subprocess.run(command)

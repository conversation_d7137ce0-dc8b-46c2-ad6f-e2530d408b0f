import math
import os
import random
import string
import subprocess

import numpy as np
import yaml


def get_log_spaced_ints(start: int = 10, end: int = 4900, count: int = 10) -> list[int]:
    # Generate log-spaced numbers
    log_space = np.geomspace(start, end, count)
    # Convert to integers
    return [int(round(x)) for x in log_space]


def generate_random_string(length, character_set="ascii") -> str:
    if character_set == "ascii":
        characters = string.ascii_letters + string.digits + string.punctuation + " \n\t"
    elif character_set == "utf8":
        characters = "".join(chr(i) for i in range(0xFFFF) if chr(i).isprintable())
    else:
        raise ValueError(f"Invalid character set: {character_set}")

    return "".join(random.choice(characters) for _ in range(length))


random_prompts = [
    (f"random_prompt_ascii_{length}", generate_random_string(length * 3, "ascii"))
    for length in get_log_spaced_ints(count=5)
] + [
    (f"random_prompt_utf8_{length}", generate_random_string(length * 3, "utf8"))
    for length in get_log_spaced_ints(count=5)
]

# Read the template
template_path = "experimental/zhuoran/custom_prompts/template.yaml"
with open(template_path, "r") as file:
    template = yaml.safe_load(file)

for name, prompt in random_prompts:
    template["task"]["user_guidelines"] = prompt
    template["task"]["custom_prompt_as_user_message"] = True
    template["determined"]["name"] = f"custom_prompts_{name}_r5"
    if name == "random_prompt_ascii_10":
        continue

    # Write the config
    output_dir = "/mnt/efs/augment/user/zhuoran/custom_prompts/random_prompts"
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"{name}.yaml")

    with open(output_path, "w") as file:
        yaml.dump(template, file, default_flow_style=False)

    print(f"Config has been written to {output_path}")

    with open(output_path, "r") as file:
        config = yaml.safe_load(file)

    print(yaml.dump(config))

    command = [
        "python",
        "/home/<USER>/augment/research/eval/eval.py",
        output_path,
        "--summary_path",
        f"/mnt/efs/augment/user/zhuoran/summaries/custom_prompts/{template['determined']['name']}.json",
    ]
    # command += ["--wait_for_completion"]
    subprocess.run(command)

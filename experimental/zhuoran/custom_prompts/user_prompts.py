import os
import subprocess

import yaml

user_guidelines_list = [
    # # Length control
    # ("concise0", "Be concise and to-the-point in your answers."),
    # (
    #     "concise1",
    #     "Always be concise in your answers, but do not leave out any important information.",
    # ),
    # (
    #     "concise2",
    #     "BE AS CONCISE AS POSSIBLE, but do not leave out any important information.",
    # ),
    # (
    #     "detail0",
    #     "Be more elaborate in your answers.",
    # ),
    # (
    #     "detail1",
    #     "Be more detailed in your answers.",
    # ),
    # (
    #     "detail2",
    #     "Explain your answers in more detail.",
    # ),
    # (
    #     "detail3",
    #     "BE AS DETAILED AND ELABORATE AS POSSIBLE.",
    # ),
    # # Code vs. text
    # (
    #     "code_only",
    #     "Minimize the amount of natural language and focuse on generating code.",
    # ),
    # (
    #     "code_only1",
    #     "GIVE ME ONLY CODE AND NOTHING ELSE!",
    # ),
    # (
    #     "more_text",
    #     "In addition to any code you generate, explain the code or answer the question with more natural language.",
    # ),
    # (
    #     "more_text1",
    #     "GIVE ME A LOT OF NATURAL LANGUAGE DESCRIPTIONS OF THE CODE YOU GENERATE!",
    # ),
    # # Test libraries
    # (
    #     "lib_test_pytest",
    #     "For Python testing, always use pytest.",
    # ),
    # (
    #     "lib_test_unittest",
    #     "For Python testing, always use the unittest library.",
    # ),
    # (
    #     "lib_test_abseil",
    #     "For Python testing, always use Absel testing.",
    # ),
    # Context control
    # (
    #     "context0",
    #     "Do not retrieve from experimental/",
    # ),
    (
        "equality_sign",
        "===",
    ),
]

# Read the template
template_path = "experimental/zhuoran/custom_prompts/template.yaml"
with open(template_path, "r") as file:
    template = yaml.safe_load(file)

for name, prompt in user_guidelines_list:
    template["task"]["user_guidelines"] = prompt
    template["task"]["custom_prompt_as_user_message"] = False
    template["determined"]["name"] = f"custom_prompts_{name}_r5"

    # Write the config
    output_dir = "/mnt/efs/augment/user/zhuoran/custom_prompts/user_prompts"
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"{name}.yaml")

    with open(output_path, "w") as file:
        yaml.dump(template, file, default_flow_style=False)

    print(f"Config has been written to {output_path}")

    with open(output_path, "r") as file:
        config = yaml.safe_load(file)

    print(yaml.dump(config))

    command = [
        "python",
        "/home/<USER>/augment/research/eval/eval.py",
        output_path,
        "--summary_path",
        f"/mnt/efs/augment/user/zhuoran/summaries/custom_prompts/{template['determined']['name']}.json",
    ]
    command += ["--override_summary"]
    # command += ["--wait_for_completion"]
    subprocess.run(command)

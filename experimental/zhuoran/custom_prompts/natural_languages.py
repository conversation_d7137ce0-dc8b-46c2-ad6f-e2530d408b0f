import os
import subprocess

import yaml

languages = [
    "Chinese",
    "English",
    "Japanese",
    "Spanish",
    "German",
    "French",
    "Russian",
    "Portuguese",
    "Korean",
    "Arabic",
    "Brazilian Portuguese",
]

# Read the template
template_path = "experimental/zhuoran/custom_prompts/template.yaml"
with open(template_path, "r") as file:
    template = yaml.safe_load(file)

# Fill in the template
for language in languages:
    language_label = language.lower().replace(" ", "_")
    template["task"]["user_guidelines"] = f"Always answer in {language}."
    template["task"]["custom_prompt_as_user_message"] = True
    template["determined"]["name"] = f"custom_prompts_{language_label}_r5"

    # Write the config
    output_dir = "/mnt/efs/augment/user/zhuoran/custom_prompts/natural_languages"
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"{language_label}.yaml")

    with open(output_path, "w") as file:
        yaml.dump(template, file, default_flow_style=False)

    print(f"Config has been written to {output_path}")

    with open(output_path, "r") as file:
        config = yaml.safe_load(file)

    print(yaml.dump(config))

    command = [
        "python",
        "/home/<USER>/augment/research/eval/eval.py",
        output_path,
        "--summary_path",
        f"/mnt/efs/augment/user/zhuoran/summaries/custom_prompts/{template['determined']['name']}.json",
    ]
    command += ["--wait_for_completion"]
    subprocess.run(command)

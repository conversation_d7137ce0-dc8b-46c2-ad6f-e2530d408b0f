{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from scipy.optimize import minimize\n", "\n", "company_histogram = {\n", "    \"20-100\": 25053,\n", "    \"101-500\": 5193,\n", "    \"501-1000\": 567,\n", "    \"1001-2000\": 247,\n", "    \"2000+\": 232,\n", "}\n", "size_ranges = {\n", "    \"20-100\": (20, 100),\n", "    \"101-500\": (101, 500),\n", "    \"501-1000\": (501, 1000),\n", "    \"1001-2000\": (1001, 2000),\n", "    \"2000+\": (2000, 1000000),\n", "}\n", "\n", "\n", "def exp_cdf(x, lambda_param):\n", "    return 1 - np.exp(-lambda_param * x)\n", "\n", "\n", "def compute_expected_count(range_start, range_end, lambda_param, total_companies):\n", "    prob = exp_cdf(range_end, lambda_param) - exp_cdf(range_start, lambda_param)\n", "    return total_companies * prob\n", "\n", "\n", "def objective_function(lambda_param):\n", "    total_companies = sum(company_histogram.values())\n", "    error = 0\n", "    for range_name, count in company_histogram.items():\n", "        start, end = size_ranges[range_name]\n", "        expected = compute_expected_count(start, end, lambda_param, total_companies)\n", "        error += ((expected - count) / count) ** 2\n", "    return error\n", "\n", "\n", "# Find best fitting parameter\n", "total_companies = sum(company_histogram.values())\n", "result = minimize(objective_function, x0=0.01, method=\"<PERSON>elder-Mead\")\n", "best_lambda = result.x[0]\n", "\n", "print(f\"Best fitting exponential distribution parameter: λ = {best_lambda:.6f}\")\n", "print(\"\\nComparison of actual vs predicted counts:\")\n", "print(f\"{'Size Range':<12} {'Actual':<8} {'Predicted':<8}\")\n", "print(\"-\" * 30)\n", "for range_name, count in company_histogram.items():\n", "    start, end = size_ranges[range_name]\n", "    predicted = compute_expected_count(start, end, best_lambda, total_companies)\n", "    print(f\"{range_name:<12} {count:<8d} {int(predicted):<8d}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pareto_cdf(x, alpha, x_min=20):\n", "    return 1 - (x_min / x) ** alpha\n", "\n", "\n", "def compute_pareto_expected_count(\n", "    range_start, range_end, alpha, total_companies, x_min=20\n", "):\n", "    prob = pareto_cdf(range_end, alpha, x_min) - pareto_cdf(range_start, alpha, x_min)\n", "    return total_companies * prob\n", "\n", "\n", "def pareto_objective_function(alpha):\n", "    total_companies = sum(company_histogram.values())\n", "    error = 0\n", "    for range_name, count in company_histogram.items():\n", "        start, end = size_ranges[range_name]\n", "        expected = compute_pareto_expected_count(start, end, alpha, total_companies)\n", "        # Add weight to balance the fit between small and large companies\n", "        weight = np.sqrt(start)\n", "        error += weight * ((expected - count) / count) ** 2\n", "    return error\n", "\n", "\n", "# Find best fitting parameter for Pareto distribution\n", "result = minimize(pareto_objective_function, x0=1.5, method=\"<PERSON><PERSON>er<PERSON>Mead\")\n", "best_alpha = result.x[0]\n", "\n", "print(f\"Best fitting Pareto distribution parameter: α = {best_alpha:.6f}\")\n", "print(\"\\nComparison of actual vs predicted counts:\")\n", "print(f\"{'Size Range':<12} {'Actual':<8} {'Predicted':<8}\")\n", "print(\"-\" * 30)\n", "total_companies = sum(company_histogram.values())\n", "for range_name, count in company_histogram.items():\n", "    start, end = size_ranges[range_name]\n", "    predicted = compute_pareto_expected_count(start, end, best_alpha, total_companies)\n", "    print(f\"{range_name:<12} {count:<8d} {int(predicted):<8d}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_discrete_distribution(alpha, x_min, counts_per_range):\n", "    \"\"\"\n", "    Generate specific company sizes following Pareto distribution within each range\n", "    that sum to exactly the desired count for that range.\n", "    \"\"\"\n", "    all_companies = []\n", "\n", "    for (start, end), target_count in counts_per_range.items():\n", "        if target_count == 0:\n", "            continue\n", "\n", "        # Generate more points than needed to allow for rounding\n", "        oversample_factor = 2\n", "        n_points = int(target_count * oversample_factor)\n", "\n", "        # Generate uniform random numbers between 0 and 1\n", "        u = np.random.uniform(0, 1, n_points)\n", "\n", "        # Transform to Pareto distribution within the range\n", "        # Using inverse CDF: x = x_min / (1-u)^(1/alpha)\n", "        F_start = pareto_cdf(start, alpha, x_min)\n", "        F_end = pareto_cdf(end, alpha, x_min) if end < float(\"inf\") else 1\n", "\n", "        # Scale u to be between F_start and F_end\n", "        u_scaled = F_start + (F_end - F_start) * u\n", "\n", "        # Generate company sizes using inverse CDF\n", "        sizes = x_min / (1 - u_scaled) ** (1 / alpha)\n", "\n", "        # Round to integers and remove duplicates\n", "        sizes = np.unique(np.round(sizes))\n", "\n", "        # If we have too many points, randomly select exactly what we need\n", "        if len(sizes) > target_count:\n", "            sizes = np.random.choice(sizes, target_count, replace=False)\n", "        # If we have too few points, duplicate some points\n", "        elif len(sizes) < target_count:\n", "            additional_needed = target_count - len(sizes)\n", "            duplicates = np.random.choice(sizes, additional_needed)\n", "            sizes = np.concatenate([sizes, duplicates])\n", "\n", "        all_companies.extend(sizes)\n", "\n", "    return np.array(all_companies, dtype=int)\n", "\n", "\n", "# Generate the discrete distribution\n", "counts_per_range = {\n", "    (20, 100): 25053,\n", "    (101, 500): 5193,\n", "    (501, 1000): 567,\n", "    (1001, 2000): 247,\n", "    (2000, float(\"inf\")): 232,\n", "}\n", "\n", "companies = generate_discrete_distribution(\n", "    alpha=best_alpha, x_min=20, counts_per_range=counts_per_range\n", ")\n", "\n", "# Verify the distribution matches our targets\n", "print(\"Verification of generated distribution:\")\n", "print(f\"{'Size Range':<12} {'Target':<8} {'Generated':<8}\")\n", "print(\"-\" * 30)\n", "for range_name, (start, end) in size_ranges.items():\n", "    target = company_histogram[range_name]\n", "    generated = np.sum((companies >= start) & (companies < end))\n", "    print(f\"{range_name:<12} {target:<8d} {generated:<8d}\")\n", "\n", "print(f\"\\nTotal companies: {len(companies)}\")\n", "print(f\"Min size: {np.min(companies)}\")\n", "print(f\"Max size: {np.max(companies)}\")\n", "print(f\"Mean size: {np.mean(companies):.1f}\")\n", "print(f\"Median size: {np.median(companies):.1f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Size statistics per range:\")\n", "print(f\"{'Size Range':<12} {'Count':<8} {'Mean':<8} {'Median':<8} {'Total Size':<8}\")\n", "print(\"-\" * 60)\n", "\n", "for range_name, (start, end) in size_ranges.items():\n", "    mask = (companies >= start) & (companies < end)\n", "    companies_in_range = companies[mask]\n", "\n", "    count = len(companies_in_range)\n", "    mean_size = np.mean(companies_in_range)\n", "    median_size = np.median(companies_in_range)\n", "    total_size = np.sum(companies_in_range)\n", "\n", "    print(\n", "        f\"{range_name:<12} {count:<8d} {mean_size:8.1f} {median_size:8.1f} {total_size:10,d}\"\n", "    )\n", "\n", "# Also calculate overall statistics\n", "print(\"\\nOverall statistics:\")\n", "print(f\"Total companies: {len(companies):,}\")\n", "print(f\"Overall mean size: {np.mean(companies):.1f}\")\n", "print(f\"Overall median size: {np.median(companies):.1f}\")\n", "print(f\"Overall total size: {np.sum(companies):,}\")\n", "print(f\"Total employees: {np.sum(companies):,}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ACTIVATION_RATE = 0.34\n", "RETENTION_RATE = 0.50\n", "PRO_CONVERSION_RATE = 0.10"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_projections(signup_count):\n", "    activation_count = signup_count * ACTIVATION_RATE\n", "    retention_count = activation_count * RETENTION_RATE\n", "    pro_count = retention_count * PRO_CONVERSION_RATE\n", "    print(f\"{signup_count:,} signups\")\n", "    print(f\"-> {int(activation_count):,} activations\")\n", "    print(f\"-> {int(retention_count):,} retained users\")\n", "    print(f\"-> {int(pro_count):,} pro users\")\n", "    print(f\"-> ${pro_count * 30 * 12:,.2f} ARR\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_signup = 1417\n", "print_projections(daily_signup * 365)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["weekly_signup = 5006\n", "print_projections(weekly_signup * 52)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
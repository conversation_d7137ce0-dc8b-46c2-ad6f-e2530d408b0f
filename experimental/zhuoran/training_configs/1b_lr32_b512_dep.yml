determined:
  name: 1b_lr32_b512_dep
  description: null
  workspace: Dev
  project: zhuoran-pretrain

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/llama_1b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/zhuoran/experiments/
  n_kv_heads: 16
  learning_rate: 2e-3
  min_lr: 2e-4
  decay_lr: True
  max_iters: 47_501
  lr_decay_iters: 47_501
  eval_interval: 5000
  train_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training
  eval_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation
  model_vocab_size: 51200
  batch_size: 4
  gradient_accumulation_steps: 4
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: zhuoran-pretrain
  wandb_run_name: 1b_lr32_b512_dep
  restore_training_state_from_checkpoint: True
  checkpoint_dir: /mnt/efs/augment/user/zhuoran/experiments/1b_lr32_b512_dep/checkpoint_llama_iteration_45000

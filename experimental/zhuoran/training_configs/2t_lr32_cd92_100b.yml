determined:
  description: null
  workspace: Dev
  project: pretrain-1b-2T

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 64
  project_group: pretraining

fastbackward_configs:
 # Zero or more config files. Path relative to fastbackward base.
 - configs/llama_1b.py

fastbackward_args:
  run_name: 2t_lr32_cd92_100b

  n_kv_heads: 16
  warmup_iters: 0
  learning_rate: 2e-4
  min_lr: 2e-9
  decay_lr: True
  max_iters: 100000
  lr_decay_iters: 100000
  eval_interval: 10000
  train_data_path: /mnt/efs/spark-data/shared/aug-stack/datasets/2t_v0/training_0;/mnt/efs/spark-data/shared/aug-stack/datasets/2t_v0/training_1;/mnt/efs/spark-data/shared/aug-stack/datasets/2t_v0/training_2;/mnt/efs/spark-data/shared/aug-stack/datasets/2t_v0/training_3;/mnt/efs/spark-data/shared/aug-stack/datasets/2t_v0/training_4
  eval_data_path: /mnt/efs/spark-data/shared/aug-stack/datasets/2t_v0/validation
  cross_shuffle: false
  model_vocab_size: 51200
  batch_size: 4
  gradient_accumulation_steps: 1
  use_activation_checkpointing: False
  wandb_project: zhuoran-pretrain

  checkpoint_optimizer_state: True
  checkpoint_dir: /mnt/efs/spark-data/shared/determined_jobs/augstack-1b-2T-take2/checkpoint_llama_iteration_2000000
  restore_training_state_from_checkpoint: True

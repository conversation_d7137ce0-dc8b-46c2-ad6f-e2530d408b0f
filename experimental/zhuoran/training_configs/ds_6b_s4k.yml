determined:
  name: ds_6b_s4k
  description: null
  workspace: Dev
  project: zhuoran-pretrain

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 8
  project_group: pretraining

fastbackward_configs:
 - configs/deepseek_base_5.7mqa.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/zhuoran/experiments/
  learning_rate: 2e-3
  min_lr: 2e-4
  decay_lr: True
  max_iters: 10_001
  lr_decay_iters: 10_001
  eval_interval: 5_000
  train_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training
  eval_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation
  model_vocab_size: 32_256
  batch_size: 1
  gradient_accumulation_steps: 2
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: zhuoran-pretrain
  wandb_run_name: ds_6b_s4k
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-5.7bmqa-base

determined:
  description: null
  workspace: Dev
  project: zhuoran-pretrain

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32
  project_group: pretraining
  # 100 is high enough so Determined would not remove any checkpoint
  # Otherwise, it will remove older checkpoints.
  keep_last_n_checkpoints: 100

fastbackward_configs:
 - configs/llama_1b.py

fastbackward_args:
  run_name: 1b_lr32_dep_0324
  n_kv_heads: 16
  learning_rate: 2e-3
  min_lr: 2e-4
  decay_lr: True
  max_iters: 95_000
  lr_decay_iters: 95_000
  eval_interval: 5000
  train_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training
  eval_data_path: /mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation
  model_vocab_size: 51200
  batch_size: 4
  gradient_accumulation_steps: 2
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_project: zhuoran-pretrain

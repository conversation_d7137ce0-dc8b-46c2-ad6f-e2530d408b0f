determined:
  name: "1b"
  description: null
  workspace: Dev
  project: zhuoran-pretrain

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/llama_1b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/zhuoran/experiments/
  n_kv_heads: 16
  learning_rate: 3e-4
  min_lr: 3e-5
  decay_lr: True
  max_iters: 95_001
  lr_decay_iters: 95_001
  eval_interval: 5000
  train_data_path: /mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_fim0.5/dataset
  eval_data_path: /mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_fim0.5/validation_dataset
  model_vocab_size: 51200
  batch_size: 4
  gradient_accumulation_steps: 2
  use_activation_checkpointing: False
  checkpoint_optimizer_state: True
  wandb_log: True
  wandb_project: zhuoran-pretrain
  wandb_run_name: 1b

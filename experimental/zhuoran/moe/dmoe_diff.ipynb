{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n"]}], "source": ["import os\n", "from importlib import reload\n", "\n", "import torch\n", "from torch import distributed as dist\n", "from megablocks.layers import dmoe, mlp, glu\n", "from megablocks.layers.dmoe import Arguments, dMoE\n", "\n", "from research.fastbackward import fs_model_parallel as mpu\n", "\n", "reload(dmoe)\n", "reload(mlp)\n", "reload(glu)\n", "reload(mpu)\n", "\n", "os.environ['MASTER_ADDR'] = 'localhost'\n", "os.environ['MASTER_PORT'] = '12358'\n", "if not dist.is_initialized():\n", "    dist.init_process_group(backend=\"gloo\", rank=0, world_size=1)\n", "mpu.destroy_model_parallel()\n", "mpu.initialize_model_parallel(1)\n", "\n", "batch_size = 1\n", "seq_len = 2\n", "dim = 8\n", "hidden_dim = 128\n", "expert_count = 2\n", "\n", "megablocks_args = Arguments(\n", "    hidden_size=dim,  # Fbwd's dim is MegaBlocks' hidden_size\n", "    ffn_hidden_size=hidden_dim,  # And fbwd's hidden_dim is MB's ffn_hidden_size\n", "    bias=False,\n", "    return_bias=False,\n", "    activation_fn=torch.nn.functional.silu,\n", "    moe_num_experts=expert_count,\n", "    moe_top_k=1,\n", "    moe_loss_weight=0.05,\n", "    moe_normalize_expert_weights=1.0,\n", "    moe_jitter_eps=0.0,\n", "    mlp_type=\"glu\",\n", "    mlp_impl=\"sparse\",\n", "    # moe_expert_model_parallelism=True,\n", "    moe_expert_model_parallelism=False,\n", "    expert_parallel_group=None,\n", "    fp16=False,\n", "    bf16=True,\n", "    device=torch.device(\"cuda\"),\n", ")\n", "dmoe_ = dMoE(megablocks_args)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["y=tensor([[[-6.8283e-04,  4.8447e-04,  1.4782e-04, -1.8215e-04, -1.4343e-03,\n", "           1.1520e-03,  5.6076e-04, -1.3733e-04],\n", "         [-1.3065e-04,  4.3869e-05, -1.0824e-04, -5.3167e-05,  1.8215e-04,\n", "           4.2343e-04,  8.8215e-05,  4.8637e-04]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<ViewBackward0>)\n", "y0=tensor([[[-2.3270e-04,  1.3542e-04,  1.3411e-05, -2.0146e-05, -9.1553e-04,\n", "           5.3024e-04,  3.4332e-04,  2.3937e-04]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<ViewBackward0>)\n", "y1=tensor([[[-1.3065e-04,  4.3869e-05, -1.0824e-04, -5.3167e-05,  1.8215e-04,\n", "           4.2343e-04,  8.8215e-05,  4.8637e-04]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<ViewBackward0>)\n", "\n", "diff0=tensor([[[ 0.0005, -0.0003, -0.0001,  0.0002,  0.0005, -0.0006, -0.0002,\n", "           0.0004]]], device='cuda:0', dtype=torch.bfloat16,\n", "       grad_fn=<SubBackward0>)\n", "diff1=tensor([[[0., 0., 0., 0., 0., 0., 0., 0.]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<SubBackward0>)\n"]}], "source": ["# Running 2 tokens as 1 sequence and 2 sequences give different results\n", "x = torch.randn([batch_size, seq_len, dim], dtype=torch.bfloat16, device=torch.device(\"cuda\"))\n", "y = dmoe_(x)\n", "\n", "x0 = x[:, :seq_len // 2, :].contiguous()\n", "x1 = x[:, seq_len // 2:, :].contiguous()\n", "y0 = dmoe_(x0)\n", "y1 = dmoe_(x1)\n", "\n", "diff0 = y0 - y[:, :seq_len // 2, :]\n", "diff1 = y1 - y[:, seq_len // 2:, :]\n", "\n", "print(f\"{y=}\")\n", "print(f\"{y0=}\")\n", "print(f\"{y1=}\")\n", "print()\n", "print(f\"{diff0=}\")\n", "print(f\"{diff1=}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.allclose(y0, y[:, :seq_len // 2, :], atol=1e-4, rtol=1e-4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The current observation is that if the two tokens have the same routing, the outputs will be the same whether they go through `dMoE` as 1 sequence or 2 sequences."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
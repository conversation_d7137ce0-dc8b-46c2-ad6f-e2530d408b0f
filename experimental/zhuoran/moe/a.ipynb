{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Dist"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["from importlib import reload\n", "\n", "import torch\n", "\n", "from research.fastbackward.model import ModelArgs\n", "from research.fastbackward.checkpointing import utils\n", "from research.fastbackward.checkpointing.utils import (\n", "    merge_model_parallel_consolidated_checkpoints,\n", ")\n", "\n", "reload(utils)\n", "\n", "subdir = \"glu_e4_kv0_sp_tiny\"\n", "dp = 1\n", "mp = 2\n", "model_args = ModelArgs(\n", "    dim=32,\n", "    n_layers=2,\n", "    n_heads=8,\n", "    n_kv_heads=0,\n", "    vocab_size=8,\n", "    ffn_type=\"glu\",\n", "    bias=True,\n", "    norm_type=\"layernorm\",\n", "    expert_count=4,\n", "    moe_top_k=1,\n", "    use_sequence_parallel=False,\n", ")\n", "\n", "def load(subdir, dp, mp, suffix = \"\"):\n", "    if suffix:\n", "        suffix = f\"_{suffix}\"\n", "    dicts = []\n", "    for dr in range(dp):\n", "        dr_dicts = []\n", "        for mr in range(mp):\n", "            dr_dicts.append(torch.load(f\"/home/<USER>/pgrads/{subdir}/DP{dp}MP{mp}-{dr}{mr}{suffix}.pth\"))\n", "        dicts.append(dr_dicts)\n", "    return dicts\n", "\n", "\n", "def find_diff_dim(shape1, shape2):\n", "    if len(shape1) < len(shape2):\n", "        shape1 = [1] * (len(shape2) - len(shape1)) + list(shape1)\n", "    for i, (dim1, dim2) in enumerate(zip(shape1, shape2)):\n", "        if dim1 != dim2:\n", "            return i\n", "    return None  # shapes are identical"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False 5.04e-02 tok_embeddings.weight [4, 8] [4, 8]\n", "True 6.33e-06 layers.0.attention.wq.weight [8, 8] [8, 8]\n", "True 2.18e-06 layers.0.attention.wq.bias [8] [8]\n", "True 4.30e-06 layers.0.attention.wk.weight [8, 8] [8, 8]\n", "True 3.07e-06 layers.0.attention.wk.bias [8] [8]\n", "False 2.62e-03 layers.0.attention.wv.weight [8, 8] [8, 8]\n", "False 1.75e-03 layers.0.attention.wv.bias [8] [8]\n", "False 4.87e-03 layers.0.attention.wo.weight [8, 8] [8, 8]\n", "False 6.65e-02 layers.0.attention.wo.bias [8] [8]\n", "True 8.82e-11 layers.0.feed_forward.moe.router.layer.weight [4, 8] [4, 8]\n", "True 0.00e+00 layers.0.feed_forward.moe.experts.bias [8] [8]\n", "False 4.25e-03 layers.0.feed_forward.moe.experts.mlp.w1 [1024, 8] [1024, 8]\n", "False 4.49e-03 layers.0.feed_forward.moe.experts.mlp.w2 [1024, 8] [1024, 8]\n", "False 6.34e-03 layers.0.feed_forward.moe.experts.mlp.v1 [1024, 8] [1024, 8]\n", "True 3.76e-05 layers.0.attention_norm.weight [8] [8]\n", "True 5.34e-05 layers.0.attention_norm.bias [8] [8]\n", "False 4.56e-04 layers.0.ffn_norm.weight [8] [8]\n", "False 8.29e-04 layers.0.ffn_norm.bias [8] [8]\n", "True 5.51e-06 layers.1.attention.wq.weight [8, 8] [8, 8]\n", "True 3.09e-06 layers.1.attention.wq.bias [8] [8]\n", "True 4.94e-06 layers.1.attention.wk.weight [8, 8] [8, 8]\n", "True 3.48e-06 layers.1.attention.wk.bias [8] [8]\n", "False 3.49e-03 layers.1.attention.wv.weight [8, 8] [8, 8]\n", "False 1.18e-03 layers.1.attention.wv.bias [8] [8]\n", "False 3.11e-03 layers.1.attention.wo.weight [8, 8] [8, 8]\n", "False 3.24e-02 layers.1.attention.wo.bias [8] [8]\n", "True 1.20e-11 layers.1.feed_forward.moe.router.layer.weight [4, 8] [4, 8]\n", "True 0.00e+00 layers.1.feed_forward.moe.experts.bias [8] [8]\n", "False 5.09e-03 layers.1.feed_forward.moe.experts.mlp.w1 [1024, 8] [1024, 8]\n", "False 3.62e-03 layers.1.feed_forward.moe.experts.mlp.w2 [1024, 8] [1024, 8]\n", "False 7.36e-03 layers.1.feed_forward.moe.experts.mlp.v1 [1024, 8] [1024, 8]\n", "True 8.94e-05 layers.1.attention_norm.weight [8] [8]\n", "True 5.82e-05 layers.1.attention_norm.bias [8] [8]\n", "False 1.90e-03 layers.1.ffn_norm.weight [8] [8]\n", "False 9.07e-04 layers.1.ffn_norm.bias [8] [8]\n", "False 1.21e-03 norm.weight [8] [8]\n", "False 1.11e-04 norm.bias [8] [8]\n", "False 2.56e-02 output.weight [4, 8] [4, 8]\n", "===============================\n"]}], "source": ["# For gradients\n", "single = load(subdir, 1, 1)[0][0]\n", "parallel = load(subdir, dp, mp)\n", "parallel_merged = [merge_model_parallel_consolidated_checkpoints(model_args, d) for d in parallel]\n", "\n", "for dr, parallel_a_dr in enumerate(parallel_merged):\n", "    for k, v in single.items():\n", "        assert v.shape == parallel_a_dr[k].shape, (k, v.shape, parallel_a_dr[k].shape)\n", "        print(\n", "            torch.allclose(v, parallel_a_dr[k], rtol=1e-4, atol=1e-4),\n", "            f\"{(v - parallel_a_dr[k]).abs().max().item():.2e}\",\n", "            k,\n", "            list(v.shape),\n", "            list(parallel_a_dr[k].shape),\n", "        )\n", "    print(\"===============================\")"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True 0.00e+00 tok_embeddings.weight\n", "True 0.00e+00 layers.0.attention.wq.weight\n", "True 0.00e+00 layers.0.attention.wq.bias\n", "True 0.00e+00 layers.0.attention.wk.weight\n", "True 0.00e+00 layers.0.attention.wk.bias\n", "True 0.00e+00 layers.0.attention.wv.weight\n", "True 0.00e+00 layers.0.attention.wv.bias\n", "True 0.00e+00 layers.0.attention.wo.weight\n", "True 0.00e+00 layers.0.attention.wo.bias\n", "True 0.00e+00 layers.0.feed_forward.moe.router.layer.weight\n", "True 0.00e+00 layers.0.feed_forward.moe.experts.bias\n", "True 0.00e+00 layers.0.feed_forward.moe.experts.mlp.w1\n", "True 0.00e+00 layers.0.feed_forward.moe.experts.mlp.w2\n", "True 0.00e+00 layers.0.feed_forward.moe.experts.mlp.v1\n", "True 0.00e+00 layers.0.attention_norm.weight\n", "True 0.00e+00 layers.0.attention_norm.bias\n", "True 0.00e+00 layers.0.ffn_norm.weight\n", "True 0.00e+00 layers.0.ffn_norm.bias\n", "True 0.00e+00 layers.1.attention.wq.weight\n", "True 0.00e+00 layers.1.attention.wq.bias\n", "True 0.00e+00 layers.1.attention.wk.weight\n", "True 0.00e+00 layers.1.attention.wk.bias\n", "True 0.00e+00 layers.1.attention.wv.weight\n", "True 0.00e+00 layers.1.attention.wv.bias\n", "True 0.00e+00 layers.1.attention.wo.weight\n", "True 0.00e+00 layers.1.attention.wo.bias\n", "True 0.00e+00 layers.1.feed_forward.moe.router.layer.weight\n", "True 0.00e+00 layers.1.feed_forward.moe.experts.bias\n", "True 0.00e+00 layers.1.feed_forward.moe.experts.mlp.w1\n", "True 0.00e+00 layers.1.feed_forward.moe.experts.mlp.w2\n", "True 0.00e+00 layers.1.feed_forward.moe.experts.mlp.v1\n", "True 0.00e+00 layers.1.attention_norm.weight\n", "True 0.00e+00 layers.1.attention_norm.bias\n", "True 0.00e+00 layers.1.ffn_norm.weight\n", "True 0.00e+00 layers.1.ffn_norm.bias\n", "True 0.00e+00 norm.weight\n", "True 0.00e+00 norm.bias\n", "True 0.00e+00 output.weight\n", "===============================\n"]}], "source": ["# For weights\n", "single_w = load(subdir, 1, 1, \"weights\")[0][0]\n", "parallel_w = load(subdir, dp, mp, \"weights\")\n", "parallel_w_merged = [merge_model_parallel_consolidated_checkpoints(model_args, d) for d in parallel_w]\n", "\n", "for dr, parallel_a_dr in enumerate(parallel_w_merged):\n", "    for k, v in single_w.items():\n", "        assert v.shape == parallel_a_dr[k].shape, (k, v.shape, parallel_a_dr[k].shape)\n", "        print(\n", "            torch.allclose(v, parallel_a_dr[k], rtol=1e-4, atol=1e-4),\n", "            f\"{(v - parallel_a_dr[k]).abs().max().item():.2e}\",\n", "            k,\n", "        )\n", "    print(\"===============================\")\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        ...,\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.]], device='cuda:0')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["parallel_w = load(subdir, dp, mp, \"weights\")\n", "parallel_w[0][0][\"layers.0.feed_forward.moe.experts.mlp.v1\"] - parallel_w[1][0][\"layers.0.feed_forward.moe.experts.mlp.v1\"]"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True 0.00e+00 embed_tokens [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 0.00e+00 embed_tokens_sp [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 5.32e-03 layer_0 [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 6.20e-03 layer_1 [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 3.65e-01 norm [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 3.65e-01 norm_np [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 2.00e-02 output [2, 4, 4] [2, 4, 2] [2, 4, 2]\n", "True 0.00e+00 layer_0_attention_norm [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "True 9.31e-10 layer_0_attention [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "True 1.19e-07 layer_0_ffn_norm [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 5.32e-03 layer_0_ffn [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "True 0.00e+00 layer_0_attn_x [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "True 0.00e+00 layer_0_attn_x_sharded [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 0.00e+00 layer_0_attn_x_for_kv [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 0.00e+00 layer_0_attn_x_for_q [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 0.00e+00 layer_0_attn_x_for_k [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 0.00e+00 layer_0_attn_x_for_v [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 0.00e+00 layer_0_attn_xq [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "True 0.00e+00 layer_0_attn_xk [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "True 0.00e+00 layer_0_attn_xv [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "True 0.00e+00 layer_0_attn_xq_view [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "True 0.00e+00 layer_0_attn_xk_view [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "True 0.00e+00 layer_0_attn_xv_view [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "True 0.00e+00 layer_0_attn_xq_rotary [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "True 0.00e+00 layer_0_attn_xk_rotary [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "True 0.00e+00 layer_0_attn_attn_output [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "True 2.33e-10 layer_0_attn_final [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "True 0.00e+00 layer_0_attn_k_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "True 0.00e+00 layer_0_attn_k_parallel_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "True 0.00e+00 layer_0_attn_v_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "True 0.00e+00 layer_0_attn_v_parallel_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "True 1.19e-07 layer_0_moe_input [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 1.19e-07 layer_0_moe_casted_input [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 0.00e+00 layer_0_moe_scores [8, 4] [8, 4] [8, 4]\n", "True 0.00e+00 layer_0_moe_expert_weights [8, 1] [8, 1] [8, 1]\n", "True 0.00e+00 layer_0_moe_top_experts [8, 1] [8, 1] [8, 1]\n", "False 5.32e-03 layer_0_moe_experts [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "True 1.19e-07 layer_0_moe_pmlp_input [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 5.32e-03 layer_0_moe_pmlp_forward_fn [8, 8] [8, 8] [8, 8]\n", "True 0.00e+00 layer_0_moe_pmlp_tokens_per_expert [4] [4] [4]\n", "False 5.32e-03 layer_0_moe_pmlp_view [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 5.32e-03 layer_0_moe_pmlp_after_bias [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.48e-01 layer_1_attention_norm [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 5.51e-03 layer_1_attention [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 3.53e-01 layer_1_ffn_norm [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 6.20e-03 layer_1_ffn [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 3.48e-01 layer_1_attn_x [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 3.48e-01 layer_1_attn_x_sharded [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.48e-01 layer_1_attn_x_for_kv [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.48e-01 layer_1_attn_x_for_q [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.48e-01 layer_1_attn_x_for_k [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.48e-01 layer_1_attn_x_for_v [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 1.32e-02 layer_1_attn_xq [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "False 2.39e-02 layer_1_attn_xk [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "False 1.44e-02 layer_1_attn_xv [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "False 1.32e-02 layer_1_attn_xq_view [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "False 2.39e-02 layer_1_attn_xk_view [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "False 1.44e-02 layer_1_attn_xv_view [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "False 1.32e-02 layer_1_attn_xq_rotary [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "False 2.39e-02 layer_1_attn_xk_rotary [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "False 1.44e-02 layer_1_attn_attn_output [2, 4, 4, 2] [2, 4, 2, 2] [2, 4, 2, 2]\n", "False 2.60e-04 layer_1_attn_final [2, 4, 8] [2, 2, 8] [2, 2, 8]\n", "False 2.39e-02 layer_1_attn_k_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "False 2.39e-02 layer_1_attn_k_parallel_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "False 1.44e-02 layer_1_attn_v_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "False 1.44e-02 layer_1_attn_v_parallel_h [2, 4, 8] [2, 4, 4] [2, 4, 4]\n", "False 3.53e-01 layer_1_moe_input [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.53e-01 layer_1_moe_casted_input [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.03e-03 layer_1_moe_scores [8, 4] [8, 4] [8, 4]\n", "True 0.00e+00 layer_1_moe_expert_weights [8, 1] [8, 1] [8, 1]\n", "True 0.00e+00 layer_1_moe_top_experts [8, 1] [8, 1] [8, 1]\n", "False 4.44e-03 layer_1_moe_experts [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 3.53e-01 layer_1_moe_pmlp_input [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 4.44e-03 layer_1_moe_pmlp_forward_fn [8, 8] [8, 8] [8, 8]\n", "True 0.00e+00 layer_1_moe_pmlp_tokens_per_expert [4] [4] [4]\n", "False 4.44e-03 layer_1_moe_pmlp_view [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "False 4.44e-03 layer_1_moe_pmlp_after_bias [2, 4, 8] [2, 4, 8] [2, 4, 8]\n", "===============================\n"]}], "source": ["# For act\n", "single_a = load(subdir, 1, 1, \"act\")[0][0]\n", "parallel_a = load(subdir, dp, mp, \"act\")\n", "\n", "for dr, parallel_a_dr in enumerate(parallel_a):\n", "    if len(parallel_a) > 1:\n", "        # single_a_dr = {k: v[dr] for k, v in single_a.items()}\n", "        single_a_dr = {}\n", "        for k, v in single_a.items():\n", "            if (\n", "                k.endswith(\"moe_expert_weights\")\n", "                or k.endswith(\"moe_top_experts\")\n", "                or k.endswith(\"moe_scores\")\n", "                or k.endswith(\"pmlp_forward_fn\")\n", "                or k.endswith(\"pmlp_pdmlp_view\")\n", "                or k.endswith(\"pmlp_padded_scatter\")\n", "            ):\n", "                v = v.reshape(dp, -1, v.shape[-1])\n", "            if (\n", "                k.endswith(\"pmlp_expert_weights\")\n", "                or k.endswith(\"pmlp_top_experts\")\n", "                or k.endswith(\"pmlp_indices\")\n", "                or k.endswith(\"pmlp_bin_ids\")\n", "            ):\n", "                v = v.reshape(dp, -1)\n", "            if (\n", "                k.endswith(\"pmlp_tokens_per_expert\")\n", "                or k.endswith(\"pmlp_bins\")\n", "                or k.endswith(\"pmlp_padded_bins\")\n", "            ):\n", "                v = torch.stack([v] * dp)\n", "            single_a_dr[k] = v[dr]\n", "    else:\n", "        single_a_dr = single_a\n", "    for k, v in single_a_dr.items():\n", "        if v is None:\n", "            print(k, v)\n", "            continue\n", "        diff_dim = find_diff_dim(v.shape, parallel_a_dr[0][k].shape)\n", "        if diff_dim is None:\n", "            print(\n", "                torch.allclose(v, parallel_a_dr[0][k], rtol=1e-4, atol=1e-4),\n", "                f\"{(v - parallel_a_dr[0][k]).abs().max().item():.2e}\",\n", "                end=\" \",\n", "            )\n", "        else:\n", "            world_size = mp * dp\n", "            # print(k, v.shape, parallel_a_dr[0][k].shape)\n", "            repetition_size = parallel_a_dr[0][k].shape[diff_dim] * world_size // v.shape[diff_dim]\n", "            parallel_v = torch.cat(\n", "                [parallel_mr[k] for parallel_mr in parallel_a_dr][::repetition_size], dim=diff_dim\n", "            )\n", "            # assert v.shape == parallel_v.shape, (k, v.shape, parallel_v.shape, parallel_dr[0][k].shape)\n", "            print(\n", "                torch.allclose(v, parallel_v, rtol=1e-4, atol=1e-4),\n", "                f\"{(v - parallel_v).abs().max().item():.2e}\",\n", "                end=\" \",\n", "            )\n", "        print(\n", "            k,\n", "            list(v.shape),\n", "            *[list(parallel_mr[k].shape) for parallel_mr in parallel_a_dr],\n", "        )\n", "    print(\"===============================\")"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[2],\n", "         [0],\n", "         [3],\n", "         [2],\n", "         [0],\n", "         [0],\n", "         [0],\n", "         [2]]),\n", " tensor([[2],\n", "         [0],\n", "         [3],\n", "         [2]]),\n", " tensor([[0],\n", "         [0],\n", "         [0],\n", "         [2]]))"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_top_experts\"], parallel_a[0][0][\"layer_0_moe_top_experts\"], parallel_a[1][0][\"layer_0_moe_top_experts\"]"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([0, 0, 0, 0, 2, 2, 2, 3], dtype=torch.int32),\n", " tensor([0, 2, 2, 3], dtype=torch.int32),\n", " tensor([0, 0, 0, 2], dtype=torch.int32))"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_bin_ids\"], parallel_a[0][0][\"layer_0_moe_pmlp_bin_ids\"], parallel_a[1][0][\"layer_0_moe_pmlp_bin_ids\"]"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([1, 4, 5, 6, 0, 3, 7, 2], dtype=torch.int32),\n", " tensor([1, 0, 3, 2], dtype=torch.int32),\n", " tensor([0, 1, 2, 3], dtype=torch.int32))"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_indices\"], parallel_a[0][0][\"layer_0_moe_pmlp_indices\"], parallel_a[1][0][\"layer_0_moe_pmlp_indices\"]"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([4, 0, 3, 1], dtype=torch.int32),\n", " tensor([1, 0, 2, 1], dtype=torch.int32),\n", " tensor([3, 0, 1, 0], dtype=torch.int32))"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_tokens_per_expert\"], parallel_a[0][0][\"layer_0_moe_pmlp_tokens_per_expert\"], parallel_a[1][0][\"layer_0_moe_pmlp_tokens_per_expert\"]"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([128, 128, 256, 384], dtype=torch.int32),\n", " tensor([128, 128, 256, 384], dtype=torch.int32),\n", " tensor([128, 128, 256, 256], dtype=torch.int32))"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_padded_bins\"], parallel_a[0][0][\"layer_0_moe_pmlp_padded_bins\"], parallel_a[1][0][\"layer_0_moe_pmlp_padded_bins\"]"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([4, 4, 7, 8], dtype=torch.int32),\n", " tensor([1, 1, 3, 4], dtype=torch.int32),\n", " tensor([3, 3, 4, 4], dtype=torch.int32))"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_bins\"], parallel_a[0][0][\"layer_0_moe_pmlp_bins\"], parallel_a[1][0][\"layer_0_moe_pmlp_bins\"]"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[-0.6714,  1.7090,  1.5444,  ..., -0.7077, -0.2066, -0.8104],\n", "         [-0.6549,  1.6739,  1.5855,  ..., -0.7104, -0.2444, -0.7993],\n", "         [-0.6549,  1.6739,  1.5855,  ..., -0.7104, -0.2444, -0.7993],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000]]),\n", " tensor([[-0.6714,  1.7090,  1.5444,  ..., -0.7077, -0.2066, -0.8104],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000]]),\n", " tensor([[-0.6549,  1.6739,  1.5855,  ..., -0.7104, -0.2444, -0.7993],\n", "         [-0.6549,  1.6739,  1.5855,  ..., -0.7104, -0.2444, -0.7993],\n", "         [-0.6549,  1.6739,  1.5855,  ..., -0.7104, -0.2444, -0.7993],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  0.0000,  0.0000,  0.0000]]))"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_padded_gather\"], parallel_a[0][0][\"layer_0_moe_pmlp_padded_gather\"], parallel_a[1][0][\"layer_0_moe_pmlp_padded_gather\"]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[-0.6714,  1.7090,  1.5444,  0.1119, -0.9691, -0.7077, -0.2066, -0.8104],\n", "         [-0.6549,  1.6739,  1.5855,  0.1242, -0.9744, -0.7104, -0.2444, -0.7993],\n", "         [-0.6549,  1.6739,  1.5855,  0.1242, -0.9744, -0.7104, -0.2444, -0.7993],\n", "         [-0.6549,  1.6739,  1.5855,  0.1242, -0.9744, -0.7104, -0.2444, -0.7993],\n", "         [ 1.2427, -0.0616, -0.6649, -0.5974,  1.9476, -1.0955, -0.0833, -0.6876],\n", "         [-0.4050,  1.6038, -1.0997,  0.0474,  1.5975, -0.3381, -0.5724, -0.8335],\n", "         [ 1.2702, -0.0539, -0.5100, -0.5770,  1.9266, -1.1594, -0.1787, -0.7179],\n", "         [-0.6265, -1.1613,  0.2655,  1.5019, -1.6429,  0.7862,  0.2398,  0.6373]]),\n", " tensor([[-0.6714,  1.7090,  1.5444,  0.1119, -0.9691, -0.7077, -0.2066, -0.8104],\n", "         [ 1.2427, -0.0616, -0.6649, -0.5974,  1.9476, -1.0955, -0.0833, -0.6876],\n", "         [-0.4050,  1.6038, -1.0997,  0.0474,  1.5975, -0.3381, -0.5724, -0.8335],\n", "         [-0.6265, -1.1613,  0.2655,  1.5019, -1.6429,  0.7862,  0.2398,  0.6373]]),\n", " tensor([[-0.6549,  1.6739,  1.5855,  0.1242, -0.9744, -0.7104, -0.2444, -0.7993],\n", "         [-0.6549,  1.6739,  1.5855,  0.1242, -0.9744, -0.7104, -0.2444, -0.7993],\n", "         [-0.6549,  1.6739,  1.5855,  0.1242, -0.9744, -0.7104, -0.2444, -0.7993],\n", "         [ 1.2702, -0.0539, -0.5100, -0.5770,  1.9266, -1.1594, -0.1787, -0.7179]]))"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_gathered_features(a):\n", "    padded_gather = a[\"layer_0_moe_pmlp_padded_gather\"]\n", "    nonzero_indices = padded_gather.nonzero()\n", "    gathered_features = padded_gather[nonzero_indices[:, 0], nonzero_indices[:, 1]].view(-1, padded_gather.shape[-1])\n", "    return gathered_features\n", "\n", "get_gathered_features(single_a).shape, get_gathered_features(parallel_a[0][0]).shape, get_gathered_features(parallel_a[1][0]).shape\n", "# values\n", "get_gathered_features(single_a), get_gathered_features(parallel_a[0][0]), get_gathered_features(parallel_a[1][0])"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[  0],\n", "         [  1],\n", "         [  2],\n", "         [  3],\n", "         [128],\n", "         [129],\n", "         [130],\n", "         [256]]),\n", " tensor([[  0],\n", "         [128],\n", "         [129],\n", "         [256]]),\n", " tensor([[  0],\n", "         [  1],\n", "         [  2],\n", "         [128]]))"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_padded_gather\"].abs().sum(-1).nonzero(), parallel_a[0][0][\"layer_0_moe_pmlp_padded_gather\"].abs().sum(-1).nonzero(), parallel_a[1][0][\"layer_0_moe_pmlp_padded_gather\"].abs().sum(-1).nonzero()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([312, 2]), torch.<PERSON><PERSON>([296, 2]), torch.<PERSON><PERSON>([168, 2]))"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_pmlp_mlp\"].nonzero().shape, parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"].nonzero().shape, parallel_a[1][0][\"layer_0_moe_pmlp_mlp\"].nonzero().shape"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([384, 8])\n"]}, {"data": {"text/plain": ["(tensor([ 1.6344e-03,  2.2080e-03,  1.6813e-03,  1.2255e-03, -6.8620e-04,\n", "         -6.2380e-04,  2.2391e-03,  2.0081e-03,  4.9855e-04,  2.7459e-03,\n", "         -1.6028e-03, -1.1421e-04,  5.5780e-04, -2.6230e-04, -1.5680e-04,\n", "         -3.9797e-04, -1.7938e-03, -6.2986e-04, -3.0972e-03,  6.7024e-04,\n", "         -1.5510e-03,  2.2209e-05, -2.3140e-04,  2.7354e-04,  7.1404e-04,\n", "         -5.7543e-04,  2.7640e-04,  4.4630e-04, -6.5409e-04, -3.7339e-04,\n", "          6.3611e-04, -3.9315e-04,  1.9478e-04, -4.1539e-04,  2.0498e-04,\n", "          1.8989e-04,  4.4017e-35, -7.1939e-35,  1.5893e-33]),\n", " tensor([ 1.1868e-03, -6.8620e-04, -6.2380e-04,  2.1518e-03,  9.5857e-04,\n", "         -1.1381e-03,  6.9001e-04, -1.1421e-04,  5.5780e-04,  1.2280e-03,\n", "         -1.5889e-03, -1.0098e-03, -7.0908e-04, -9.3347e-04, -8.8521e-04,\n", "         -3.2834e-04, -1.8572e-03,  2.2209e-05, -2.3140e-04,  2.7354e-04,\n", "         -7.4545e-04,  7.4108e-04, -6.6878e-04,  2.7640e-04, -5.7014e-04,\n", "         -4.4233e-04, -3.4493e-04, -1.2087e-03,  5.0665e-04, -2.3009e-04,\n", "          1.9478e-04, -4.1539e-04,  2.0498e-04,  1.8989e-04,  4.5397e-08,\n", "         -1.8677e-07,  2.6007e-07]),\n", " tensor([ 2.2080e-03,  1.6813e-03,  1.2255e-03, -7.0725e-04,  1.0557e-04,\n", "          1.0821e-03, -1.5849e-04,  4.3954e-04, -1.8117e-04,  3.6685e-04,\n", "         -7.8405e-05,  4.5130e-04, -2.1894e-04, -1.1569e-03,  4.6731e-04,\n", "         -1.8758e-04,  1.7992e-05, -2.6585e-04,  3.8788e-08, -1.5498e-07,\n", "          6.0442e-07]))"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["def get_gathered_mlps(a):\n", "    padded_gather = a[\"layer_0_moe_pmlp_mlp\"]\n", "    nonzero_indices = padded_gather.nonzero()\n", "    gathered_features = padded_gather[nonzero_indices[:, 0], nonzero_indices[:, 1]].view(-1, padded_gather.shape[-1])\n", "    return gathered_features\n", "\n", "get_gathered_mlps(single_a).shape, get_gathered_mlps(parallel_a[0][0]).shape, get_gathered_mlps(parallel_a[1][0]).shape\n", "# values\n", "# get_gathered_features(single_a), get_gathered_features(parallel_a[0][0]), get_gathered_features(parallel_a[1][0])\n", "print(single_a[\"layer_0_moe_pmlp_mlp\"].shape)\n", "(single_a[\"layer_0_moe_pmlp_mlp\"].abs().sum(-1) > 0).sum(), (parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"].abs().sum(-1) > 0).sum(), (parallel_a[1][0][\"layer_0_moe_pmlp_mlp\"].abs().sum(-1) > 0).sum()\n", "(\n", "    sa := single_a[\"layer_0_moe_pmlp_mlp\"][single_a[\"layer_0_moe_pmlp_mlp\"].abs().sum(-1) > 0][:, 0],\n", "    pa1 := parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"][parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"].abs().sum(-1) > 0][:, 0],\n", "    pa2 := parallel_a[1][0][\"layer_0_moe_pmlp_mlp\"][parallel_a[1][0][\"layer_0_moe_pmlp_mlp\"].abs().sum(-1) > 0][:, 0],\n", ")\n", "# ((sa.view(-1, 1) - pa1.view(1, -1)) < 1e-15).sum(), sa.view(-1, 1) - pa1.view(1, -1)"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([384, 8]) torch.<PERSON><PERSON>([384, 8])\n", "tensor([[0, 0],\n", "        [1, 4],\n", "        [2, 5],\n", "        [3, 7]])\n", "tensor([[0, 1],\n", "        [0, 2],\n", "        [1, 1],\n", "        [1, 2],\n", "        [2, 3],\n", "        [3, 6]])\n", "tensor([], size=(0, 2), dtype=torch.int64)\n"]}], "source": ["print(single_a[\"layer_0_moe_pmlp_padded_gather\"].shape, parallel_a[0][0][\"layer_0_moe_pmlp_padded_gather\"].shape)\n", "# single_a[\"layer_0_moe_pmlp_padded_gather\"][:256], parallel_a[0][0][\"layer_0_moe_pmlp_padded_gather\"]\n", "single = single_a[\"layer_0_moe_pmlp_padded_gather\"]\n", "parallel0 = parallel_a[0][0][\"layer_0_moe_pmlp_padded_gather\"]\n", "parallel1 = parallel_a[1][0][\"layer_0_moe_pmlp_padded_gather\"]\n", "single_nonzero = single_a[\"layer_0_moe_pmlp_padded_gather\"].max(1).values.nonzero()\n", "parallel0_nonzero = parallel_a[0][0][\"layer_0_moe_pmlp_padded_gather\"].max(1).values.nonzero()\n", "parallel1_nonzero = parallel_a[1][0][\"layer_0_moe_pmlp_padded_gather\"].max(1).values.nonzero()\n", "# single_nonzero, parallel0_nonzero, parallel1_nonzero + 256\n", "single_good = single[single_nonzero]\n", "parallel0_good = parallel0[parallel0_nonzero]\n", "parallel1_good = parallel1[parallel1_nonzero]\n", "print((single_good.transpose(0, 1) == parallel0_good).all(-1).nonzero())\n", "print((single_good.transpose(0, 1) == parallel1_good).all(-1).nonzero())\n", "print((parallel0_good.transpose(0, 1) == parallel1_good).all(-1).nonzero())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([256, 8]) torch.<PERSON><PERSON>([256, 8])\n", "torch.<PERSON><PERSON>([26, 1]) torch.<PERSON><PERSON>([21, 1]) torch.<PERSON><PERSON>([7, 1])\n"]}], "source": ["print(single_a[\"layer_0_moe_pmlp_mlp\"].shape, parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"].shape)\n", "# single_a[\"layer_0_moe_pmlp_mlp\"][:256], parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"]\n", "single = single_a[\"layer_0_moe_pmlp_mlp\"]\n", "parallel0 = parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"]\n", "parallel1 = parallel_a[1][0][\"layer_0_moe_pmlp_mlp\"]\n", "# single.shape\n", "single_nonzero = single_a[\"layer_0_moe_pmlp_mlp\"].max(1).values.nonzero()\n", "parallel0_nonzero = parallel_a[0][0][\"layer_0_moe_pmlp_mlp\"].max(1).values.nonzero()\n", "parallel1_nonzero = parallel_a[1][0][\"layer_0_moe_pmlp_mlp\"].max(1).values.nonzero()\n", "print(single_nonzero.shape, parallel0_nonzero.shape, parallel1_nonzero.shape)\n", "single_good = single[single_nonzero]\n", "parallel0_good = parallel0[parallel0_nonzero]\n", "parallel1_good = parallel1[parallel1_nonzero]\n", "# print((single_good.transpose(0, 1) == parallel0_good).all(-1).nonzero())\n", "# print((single_good.transpose(0, 1) == parallel1_good).all(-1).nonzero())\n", "torch.set_printoptions(threshold=torch.inf)\n", "prec = 1e-9\n", "matching0 = (single_good.transpose(0, 1) - parallel0_good).max(-1).values < prec\n", "matching1 = (single_good.transpose(0, 1) - parallel1_good).max(-1).values < prec\n", "# print((single_good.transpose(0, 1) - parallel1_good).max(-1).values < 1e-4)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(2, 6), (3, 7), (4, 8), (5, 9), (6, 10), (7, 11), (8, 13), (9, 14), (10, 15), (11, 16), (12, 17), (13, 18), (14, 19), (15, 20), (16, 21), (17, 22)]\n", "16\n", "[(0, 2), (1, 3), (2, 4), (3, 5)]\n", "4\n"]}], "source": ["import networkx as nx\n", "import numpy as np\n", "\n", "def bool_tensor_matching(tensor):\n", "    rows, cols = tensor.shape\n", "    G = nx.Graph()\n", "    \n", "    # Add nodes for both dimensions\n", "    G.add_nodes_from(range(rows), bipartite=0)\n", "    G.add_nodes_from(range(rows, rows + cols), bipartite=1)\n", "    \n", "    # Add edges for True elements\n", "    for i in range(rows):\n", "        for j in range(cols):\n", "            if tensor[i, j]:\n", "                G.add_edge(i, rows + j)\n", "    \n", "    # Find the maximum bipartite matching\n", "    matching = nx.bipartite.maximum_matching(G, top_nodes=range(rows))\n", "    \n", "    # Extract the matching pairs\n", "    matched_pairs = [(u, v - rows) for u, v in matching.items() if u < rows]\n", "    return matched_pairs\n", "\n", "matching_pairs0 = bool_tensor_matching(matching0)\n", "print(matching_pairs0)\n", "print(matching_pairs0.__len__())\n", "matching_pairs1 = bool_tensor_matching(matching1)\n", "print(matching_pairs1)\n", "print(matching_pairs1.__len__())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([8, 8])\n", "torch.<PERSON><PERSON>([4, 8])\n", "torch.<PERSON><PERSON>([4, 8])\n", "0 0 1.59e-03\n", "0 1 1.16e-03\n", "0 2 1.69e-03\n", "0 3 2.47e-03\n", "1 0 3.68e-03\n", "1 1 2.82e-03\n", "1 2 3.74e-03\n", "1 3 3.69e-03\n", "2 0 3.05e-03\n", "2 1 3.48e-03\n", "2 2 3.64e-03\n", "2 3 3.49e-03\n", "3 0 1.10e-03\n", "3 1 1.43e-03\n", "3 2 8.04e-04\n", "3 3 0.00e+00\n", "4 0 2.15e-03\n", "4 1 2.74e-03\n", "4 2 2.34e-03\n", "4 3 1.98e-03\n", "5 0 2.12e-03\n", "5 1 1.69e-03\n", "5 2 1.66e-03\n", "5 3 1.69e-03\n", "6 0 1.89e-03\n", "6 1 2.02e-03\n", "6 2 1.36e-03\n", "6 3 9.51e-04\n", "7 0 1.21e-03\n", "7 1 1.24e-03\n", "7 2 1.15e-03\n", "7 3 8.69e-04\n"]}], "source": ["print(single_a[\"layer_0_moe_pmlp_forward_fn\"].shape)\n", "print(parallel_a[0][0][\"layer_0_moe_pmlp_forward_fn\"].shape)\n", "print(parallel_a[1][0][\"layer_0_moe_pmlp_forward_fn\"].shape)\n", "# print(single_a[\"layer_0_moe_pmlp_forward_fn\"])\n", "# print(parallel_a[0][0][\"layer_0_moe_pmlp_forward_fn\"])\n", "# print(parallel_a[1][0][\"layer_0_moe_pmlp_forward_fn\"])\n", "for i in range(8):\n", "    for j in range(4):\n", "        print(\n", "            i,\n", "            j,\n", "            f'''{(\n", "                single_a[\"layer_0_moe_pmlp_forward_fn\"][i]\n", "                - parallel_a[0][0][\"layer_0_moe_pmlp_forward_fn\"][j]\n", "            ).abs().max().item():.02e}''',\n", "        )\n", "# print(single_a[\"layer_0_moe_pmlp_forward_fn\"][:4] - parallel_a[0][0][\"layer_0_moe_pmlp_forward_fn\"])\n", "# print(single_a[\"layer_0_moe_pmlp_forward_fn\"][4:] - parallel_a[0][0][\"layer_0_moe_pmlp_forward_fn\"])"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0],\n", "        [0],\n", "        [0],\n", "        [1],\n", "        [0],\n", "        [0],\n", "        [1],\n", "        [0]])"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["single_a[\"layer_0_moe_top_experts\"]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[[ 2.7814e-04,  5.3516e-04, -5.4376e-05,  1.2674e-03, -2.6566e-05,\n", "          -8.1078e-04, -2.8458e-04, -1.0025e-03],\n", "         [ 1.2941e-03, -2.8741e-04, -1.1225e-03, -3.8132e-04, -5.5233e-04,\n", "           1.8203e-04,  3.2763e-04, -5.3678e-04],\n", "         [ 3.7889e-04,  1.2735e-03, -6.0441e-04, -1.6353e-03,  1.0461e-03,\n", "          -4.2597e-04, -5.3602e-04,  9.9848e-04],\n", "         [ 1.2066e-03, -2.6561e-04, -4.0526e-04, -1.1577e-04, -1.5755e-03,\n", "           1.1414e-03, -3.8779e-04, -1.6810e-03]]])\n", "tensor([[[-1.1810e-03, -5.8046e-04,  1.3844e-04, -1.1965e-03, -2.5445e-04,\n", "           1.1803e-03,  2.1758e-03, -1.9371e-03],\n", "         [-2.3416e-04, -6.3487e-04,  1.3547e-03,  1.7201e-04, -4.4433e-04,\n", "          -1.6128e-03, -1.4517e-03,  1.6414e-03],\n", "         [ 7.1381e-05, -5.1497e-04,  8.4053e-04, -6.3171e-04,  4.9099e-04,\n", "          -4.5097e-04, -4.4952e-04,  1.1382e-03],\n", "         [-4.0527e-04,  1.7102e-03, -6.6938e-04,  1.6084e-03,  6.6648e-04,\n", "           1.2097e-03,  1.6517e-03,  1.2501e-05]]])\n", "tensor([[[ 2.2712e-03,  1.5490e-03,  4.2415e-04,  2.2123e-03,  3.2822e-04,\n", "          -1.6539e-03, -2.3228e-03,  6.7075e-04],\n", "         [ 9.9769e-05, -1.4956e-03, -2.4474e-03, -2.8658e-03,  3.0728e-04,\n", "           5.6757e-04,  3.1639e-04, -2.0022e-03],\n", "         [ 2.1616e-04,  2.0600e-03, -1.9339e-03, -1.1265e-03,  9.0583e-05,\n", "          -2.3986e-04,  5.0782e-04, -2.9738e-04],\n", "         [ 1.1064e-03, -1.5933e-03,  4.1126e-05, -4.9495e-04, -2.0574e-03,\n", "          -1.7486e-04, -2.0281e-03, -1.1602e-03]]])\n", "tensor([[[ 8.1207e-04,  4.3339e-04,  6.1697e-04, -2.5156e-04,  1.0034e-04,\n", "           3.3722e-04,  1.3756e-04, -2.6386e-04],\n", "         [-1.4285e-03, -1.8430e-03,  2.9763e-05, -2.3125e-03,  4.1528e-04,\n", "          -1.2272e-03, -1.4629e-03,  1.7592e-04],\n", "         [-9.1351e-05,  2.7158e-04, -4.8895e-04, -1.2284e-04, -4.6453e-04,\n", "          -2.6486e-04,  5.9431e-04, -1.5764e-04],\n", "         [-5.0551e-04,  3.8251e-04, -2.2299e-04,  1.2292e-03,  1.8460e-04,\n", "          -1.0651e-04,  1.1460e-05,  5.3323e-04]]])\n", "torch.<PERSON><PERSON>([2, 4, 8]) torch.<PERSON><PERSON>([1, 4, 8])\n", "tensor([[0.5054, 0.4946],\n", "        [0.5054, 0.4946],\n", "        [0.5092, 0.4908],\n", "        [0.5058, 0.4942],\n", "        [0.5093, 0.4907],\n", "        [0.5063, 0.4937],\n", "        [0.5003, 0.4997],\n", "        [0.5091, 0.4909]]) tensor([[0.5054, 0.4946],\n", "        [0.5054, 0.4946],\n", "        [0.5092, 0.4908],\n", "        [0.5058, 0.4942]])\n", "tensor([[1, 3],\n", "        [1, 3],\n", "        [2, 1],\n", "        [1, 3],\n", "        [2, 1],\n", "        [1, 3],\n", "        [1, 3],\n", "        [2, 1]]) tensor([[1, 3],\n", "        [1, 3],\n", "        [2, 1],\n", "        [1, 3]])\n"]}], "source": ["# print(single_a[\"layer_0_moe_experts\"], parallel_a[0][0][\"layer_0_moe_experts\"], parallel_a[1][0][\"layer_0_moe_experts\"])\n", "print(single_a[\"layer_0_moe_experts\"][0] - parallel_a[0][0][\"layer_0_moe_experts\"])\n", "print(single_a[\"layer_0_moe_experts\"][1] - parallel_a[0][0][\"layer_0_moe_experts\"])\n", "print(single_a[\"layer_0_moe_experts\"][0] - parallel_a[1][0][\"layer_0_moe_experts\"])\n", "print(single_a[\"layer_0_moe_experts\"][1] - parallel_a[1][0][\"layer_0_moe_experts\"])\n", "print(single_a[\"layer_0_moe_experts\"].shape, parallel_a[0][0][\"layer_0_moe_experts\"].shape)\n", "print(single_a[\"layer_0_moe_expert_weights\"], parallel_a[0][0][\"layer_0_moe_expert_weights\"])\n", "print(single_a[\"layer_0_moe_top_experts\"], parallel_a[0][0][\"layer_0_moe_top_experts\"])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'NoneType' object is not subscriptable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[14], line 7\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m dr, parallel_dr \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(parallel_ag):\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(parallel_ag) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m----> 7\u001b[0m         single_ag_dr \u001b[38;5;241m=\u001b[39m \u001b[43m{\u001b[49m\u001b[43mk\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m[\u001b[49m\u001b[43mdr\u001b[49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mk\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mv\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msingle_ag\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitems\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m}\u001b[49m\n\u001b[1;32m      8\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m      9\u001b[0m         single_ag_dr \u001b[38;5;241m=\u001b[39m single_ag\n", "Cell \u001b[0;32mIn[14], line 7\u001b[0m, in \u001b[0;36m<dictcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m dr, parallel_dr \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(parallel_ag):\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(parallel_ag) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m----> 7\u001b[0m         single_ag_dr \u001b[38;5;241m=\u001b[39m {k: \u001b[43mv\u001b[49m\u001b[43m[\u001b[49m\u001b[43mdr\u001b[49m\u001b[43m]\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m single_ag\u001b[38;5;241m.\u001b[39mitems()}\n\u001b[1;32m      8\u001b[0m     \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[1;32m      9\u001b[0m         single_ag_dr \u001b[38;5;241m=\u001b[39m single_ag\n", "\u001b[0;31mTypeError\u001b[0m: 'NoneType' object is not subscriptable"]}], "source": ["# For act_grad\n", "single_ag = load(subdir, 1, 1, \"act_grad\")[0][0]\n", "parallel_ag = load(subdir, dp, mp, \"act_grad\")\n", "\n", "for dr, parallel_a_dr in enumerate(parallel_ag):\n", "    if len(parallel_ag) > 1:\n", "        single_ag_dr = {k: v[dr] for k, v in single_ag.items()}\n", "    else:\n", "        single_ag_dr = single_ag\n", "    for k, v in single_ag_dr.items():\n", "        if v.shape == parallel_a_dr[0][k].shape:\n", "            print(\n", "                torch.allclose(v, parallel_a_dr[0][k], rtol=1e-4, atol=1e-4),\n", "                f\"{(v - parallel_a_dr[0][k]).abs().max().item():.2e}\",\n", "                end=\" \",\n", "            )\n", "        else:\n", "            diff_dim = find_diff_dim(v.shape, parallel_a_dr[0][k].shape)\n", "            repetition_size = parallel_a_dr[0][k].shape[diff_dim] * 4 // v.shape[diff_dim]\n", "            parallel_v = torch.cat(\n", "                [parallel_mr[k] for parallel_mr in parallel_a_dr][::repetition_size], dim=diff_dim\n", "            )\n", "            assert v.shape == parallel_v.shape, (k, v.shape, parallel_v.shape, parallel_a_dr[0][k].shape)\n", "            print(\n", "                torch.allclose(v, parallel_v, rtol=1e-4, atol=1e-4),\n", "                f\"{(v - parallel_v).abs().max().item():.2e}\",\n", "                end=\" \",\n", "            )\n", "        print(\n", "            k,\n", "            list(v.shape),\n", "            *[list(parallel_mr[k].shape) for parallel_mr in parallel_a_dr],\n", "        )\n", "    print(\"===============================\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for k in [\"output\"]:#single_ag.keys():\n", "    print(\n", "        k,\n", "        f\"{(single_ag[k][0] - single_ag[k][1]).abs().max().item():.3e}\",\n", "        f\"{(single_ag[k][0] - parallel_ag[0][0][k]).abs().max().item():.3e}\",\n", "        f\"{(single_ag[k][1] - parallel_ag[1][0][k]).abs().max().item():.3e}\",\n", "        f\"{(parallel_ag[0][0][k] - parallel_ag[1][0][k]).abs().max().item():.3e}\",\n", "        f\"{(single_ag[k][0] + single_ag[k][1] - parallel_ag[0][0][k] - parallel_ag[1][0][k]).abs().max().item():.3e}\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for attn metadata\n", "import json\n", "dp1mp1am = json.load(open(f\"/home/<USER>/pgrads/{subdir}/DP1MP1-00_attn_metadata.json\"))\n", "dp1mp4_00am = json.load(open(f\"/home/<USER>/pgrads/{subdir}/DP1MP4-00_attn_metadata.json\"))\n", "dp1mp4_01am = json.load(open(f\"/home/<USER>/pgrads/{subdir}/DP1MP4-01_attn_metadata.json\"))\n", "dp1mp4_02am = json.load(open(f\"/home/<USER>/pgrads/{subdir}/DP1MP4-02_attn_metadata.json\"))\n", "dp1mp4_03am = json.load(open(f\"/home/<USER>/pgrads/{subdir}/DP1MP4-03_attn_metadata.json\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp1mp1am"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dp1mp4_00am"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print((dp1mp1ag[\"layer_1_attn_x_for_v\"] - dp1mp4_00ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_x_for_v\"] - dp1mp4_01ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_x_for_v\"] - dp1mp4_02ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_x_for_v\"] - dp1mp4_03ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_x_for_v\"] - dp1mp4_00ag[\"layer_1_attn_x_for_v\"] - dp1mp4_02ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_00ag[\"layer_1_attn_x_for_v\"] - dp1mp4_00ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_00ag[\"layer_1_attn_x_for_v\"] - dp1mp4_01ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_00ag[\"layer_1_attn_x_for_v\"] - dp1mp4_02ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_00ag[\"layer_1_attn_x_for_v\"] - dp1mp4_03ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_01ag[\"layer_1_attn_x_for_v\"] - dp1mp4_00ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_01ag[\"layer_1_attn_x_for_v\"] - dp1mp4_01ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_01ag[\"layer_1_attn_x_for_v\"] - dp1mp4_02ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_01ag[\"layer_1_attn_x_for_v\"] - dp1mp4_03ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_02ag[\"layer_1_attn_x_for_v\"] - dp1mp4_00ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_02ag[\"layer_1_attn_x_for_v\"] - dp1mp4_01ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_02ag[\"layer_1_attn_x_for_v\"] - dp1mp4_02ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_02ag[\"layer_1_attn_x_for_v\"] - dp1mp4_03ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_03ag[\"layer_1_attn_x_for_v\"] - dp1mp4_00ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_03ag[\"layer_1_attn_x_for_v\"] - dp1mp4_01ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_03ag[\"layer_1_attn_x_for_v\"] - dp1mp4_02ag[\"layer_1_attn_x_for_v\"]).abs().max().item())\n", "print((dp1mp4_03ag[\"layer_1_attn_x_for_v\"] - dp1mp4_03ag[\"layer_1_attn_x_for_v\"]).abs().max().item())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print((dp1mp1ag[\"layer_1_attn_v_h\"][:, :, :2] - dp1mp4_00ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "# print((dp1mp1ag[\"layer_1_attn_v_h\"][:, :, :2] - dp1mp4_01ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "# print((dp1mp1ag[\"layer_1_attn_v_h\"][:, :, 2:] - dp1mp4_02ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "# print((dp1mp1ag[\"layer_1_attn_v_h\"][:, :, 2:] - dp1mp4_03ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "# print((dp1mp1ag[\"layer_1_attn_v_h\"] - dp1mp4_00ag[\"layer_1_attn_v_h\"] - dp1mp4_02ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_v_h\"] - dp1mp4_00ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_v_h\"] - dp1mp4_01ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_v_h\"] - dp1mp4_02ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp1ag[\"layer_1_attn_v_h\"] - dp1mp4_03ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_00ag[\"layer_1_attn_v_h\"] - dp1mp4_00ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_00ag[\"layer_1_attn_v_h\"] - dp1mp4_01ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_00ag[\"layer_1_attn_v_h\"] - dp1mp4_02ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_00ag[\"layer_1_attn_v_h\"] - dp1mp4_03ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_01ag[\"layer_1_attn_v_h\"] - dp1mp4_00ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_01ag[\"layer_1_attn_v_h\"] - dp1mp4_01ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_01ag[\"layer_1_attn_v_h\"] - dp1mp4_02ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_01ag[\"layer_1_attn_v_h\"] - dp1mp4_03ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_02ag[\"layer_1_attn_v_h\"] - dp1mp4_00ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_02ag[\"layer_1_attn_v_h\"] - dp1mp4_01ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_02ag[\"layer_1_attn_v_h\"] - dp1mp4_02ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_02ag[\"layer_1_attn_v_h\"] - dp1mp4_03ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print()\n", "\n", "print((dp1mp4_03ag[\"layer_1_attn_v_h\"] - dp1mp4_00ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_03ag[\"layer_1_attn_v_h\"] - dp1mp4_01ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_03ag[\"layer_1_attn_v_h\"] - dp1mp4_02ag[\"layer_1_attn_v_h\"]).abs().max().item())\n", "print((dp1mp4_03ag[\"layer_1_attn_v_h\"] - dp1mp4_03ag[\"layer_1_attn_v_h\"]).abs().max().item())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Local"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Replacing existing component with name research.fastbackward.model.Transformer. Original component: _ComponentInfo(config_cls=<class 'types.TransformerConfig'>, component_cls=<class 'research.fastbackward.model.Transformer'>, builder=<function ComponentRegistry.autoregister.<locals>.builder_fn at 0x7f19d732d300>), new component: _ComponentInfo(config_cls=<class 'types.TransformerConfig'>, component_cls=<class 'research.fastbackward.model.Transformer'>, builder=<function ComponentRegistry.autoregister.<locals>.builder_fn at 0x7f19d732d260>).\n"]}, {"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "SparseMLP: self._num_rows_per_rank=1024, mpu.experts_per_rank(args)=4, mpu.features_per_rank(args)=256, mpu.get_weight_parallel_world_size(args)=1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "args.moe_expert_model_parallelism=False, 1\n", "SparseMLP: self._num_rows_per_rank=1024, mpu.experts_per_rank(args)=4, mpu.features_per_rank(args)=256, mpu.get_weight_parallel_world_size(args)=1\n", "ParallelMLP forward!!!!!\n", "args.moe_expert_model_parallelism=False, 1\n", "ParallelMLP forward!!!!!\n", "args.moe_expert_model_parallelism=False, 1\n"]}, {"data": {"text/plain": ["tensor([-0.0705, -0.3028, -0.0128,  ..., -0.0022,  0.0011,  0.0012],\n", "       device='cuda:0')"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"Tests to ensure the gradients are the same in all model-/data-parallel conditions.\"\"\"\n", "\n", "import os\n", "from importlib import reload\n", "\n", "import torch\n", "import torch.distributed as dist\n", "# /opt/conda/lib/python3.11/site-packages/megablocks/layers/dmoe.py\n", "from megablocks.layers import dmoe\n", "\n", "from research.fastbackward import fs_model_parallel as mpu\n", "from research.fastbackward import losses\n", "from research.fastbackward.checkpointing import checkpointing\n", "from research.fastbackward import model as fbwd_model\n", "\n", "reload(dmoe)\n", "reload(mpu)\n", "reload(losses)\n", "reload(checkpointing)\n", "reload(fbwd_model)\n", "\n", "os.environ['MASTER_ADDR'] = 'localhost'\n", "os.environ['MASTER_PORT'] = '12357'\n", "if not dist.is_initialized():\n", "    dist.init_process_group(backend=\"gloo\", rank=0, world_size=1)\n", "mpu.destroy_model_parallel()\n", "mpu.initialize_model_parallel(1)\n", "batch = 1\n", "seqlen = 4\n", "vocab_size = 4\n", "data = torch.randint(vocab_size, (batch, seqlen + 1), dtype=torch.int64, device=\"cpu\")\n", "x = data[:, :-1].contiguous()\n", "y = data[:, 1:].contiguous()\n", "vocab_size = 4\n", "model_args = fbwd_model.ModelArgs(\n", "    dim=8,\n", "    n_layers=2,\n", "    n_heads=4,\n", "    n_kv_heads=2,\n", "    vocab_size=vocab_size,\n", "    bias=True,\n", "    norm_type=\"layernorm\",\n", "    expert_count=4,\n", "    moe_top_k=2,\n", "    use_sequence_parallel=False,\n", "    use_activation_checkpointing=False,\n", ")\n", "model = fbwd_model.Transformer(model_args)\n", "checkpointer = checkpointing.CheckpointManager(mp_world_size=1, mp_rank=0)\n", "model.to(device=\"cuda\")\n", "model.train()\n", "\n", "_, flat_model_state = fbwd_model.configure_fsdp_optimizer(\n", "    model, weight_decay=1.0, learning_rate=0.1, betas=(0.9, 0.999)\n", ")\n", "\n", "total_batch = x.size(0)\n", "per_dp_batch = total_batch // mpu.get_data_parallel_world_size()\n", "batch_start = per_dp_batch * mpu.get_data_parallel_rank()\n", "batch_end = batch_start + per_dp_batch\n", "x = x[batch_start:batch_end, :].clone().cuda()\n", "y = y[batch_start:batch_end, :].clone().cuda()\n", "\n", "loss = losses.cross_entropy_loss(x, y, model(x))\n", "loss.backward(retain_graph=True)\n", "dist.all_reduce(\n", "    flat_model_state.grads(),\n", "    group=mpu.get_data_parallel_group(),\n", "    op=dist.ReduceOp.SUM,\n", ")\n", "# We can't use an AVG all_reduce because of gloo, so manually divide instead\n", "flat_model_state.grads().div_(mpu.get_data_parallel_world_size())"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParallelMLP forward!!!!!\n", "args.moe_expert_model_parallelism=False, 1\n", "ParallelMLP forward!!!!!\n", "args.moe_expert_model_parallelism=False, 1\n", "ParallelMLP forward!!!!!\n", "args.moe_expert_model_parallelism=False, 1\n"]}, {"data": {"text/plain": ["tensor([[[ 4.1356e-04, -3.3800e-04, -1.2261e-04,  4.8668e-04, -1.1237e-04,\n", "           2.0346e-05,  2.2866e-04, -7.2106e-04],\n", "         [ 8.4247e-04, -2.3125e-04,  2.2194e-04,  3.5880e-04,  1.1784e-04,\n", "          -1.4589e-04, -6.4578e-04,  1.4386e-04],\n", "         [ 3.3971e-04, -6.0308e-05, -2.5360e-04, -9.4049e-06, -3.5713e-04,\n", "           2.7607e-04,  1.9481e-04, -1.7921e-04],\n", "         [ 7.8989e-05,  3.9856e-05, -9.2743e-05, -4.9219e-04, -1.5479e-04,\n", "           2.5523e-04, -9.1562e-05, -5.7246e-06]]], device='cuda:0',\n", "       grad_fn=<SubBackward0>)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["dmoe0 = model.layers[0].feed_forward.moe\n", "input_ = torch.randn(1, 4, 8, device=\"cuda\")\n", "output = dmoe0(input_)\n", "\n", "input0 = input_[:, :2, :]\n", "input1 = input_[:, 2:, :]\n", "output0 = dmoe0(input0)\n", "output1 = dmoe0(input1)\n", "torch.cat([output0, output1], dim=1) - output"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Size([512, 8])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["dmoe_mlp0 = model.layers[0].feed_forward.moe.experts.mlp\n", "topo = model.layers[1].feed_forward.moe.experts.topo_saving\n", "input_ = torch.randn(4, 8, device=\"cuda\")\n", "output = dmoe_mlp0(input_, topo)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 1.1337e-03,  1.6045e-03,  2.0509e-04,  ..., -8.1031e-04,\n", "          1.2501e-03, -1.3736e-03],\n", "        [-9.5923e-05,  4.1944e-05, -8.5377e-04,  ...,  4.8296e-04,\n", "         -1.5234e-04, -5.9157e-04],\n", "        [-2.1941e-05,  1.0715e-04, -1.2634e-03,  ..., -2.3104e-03,\n", "          6.9131e-04, -3.0910e-04],\n", "        ...,\n", "        [ 1.2476e-03,  7.0346e-04,  2.0752e-04,  ...,  7.3009e-04,\n", "          4.1007e-04, -6.6210e-04],\n", "        [-1.6298e-03, -1.3731e-03,  2.5096e-03,  ...,  1.6757e-03,\n", "          1.8624e-03, -7.9239e-04],\n", "        [-9.8633e-04,  1.8469e-04, -2.7616e-03,  ..., -5.9937e-04,\n", "         -1.3593e-03, -3.0070e-04]], device='cuda:0', grad_fn=<SubBackward0>)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["input0 = input_[:2]\n", "input1 = input_[2:]\n", "output0 = dmoe_mlp0(input0, topo)\n", "output1 = dmoe_mlp0(input1, topo)\n", "output0 + output1 - output"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["topo = model.layers[1].feed_forward.moe.experts.topo_saving"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([0, 0, 1, 1, 2, 2, 3, 3], device='cuda:0', dtype=torch.int16),\n", " tensor([0, 1, 2, 3, 4, 5, 6, 7], device='cuda:0', dtype=torch.int16))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["topo.row_indices, topo.column_indices\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Lib"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prepared DBRX config\n"]}], "source": ["from __future__ import annotations\n", "\n", "import importlib\n", "\n", "import torch\n", "from megablocks.layers import arguments, common, dmoe\n", "\n", "from experimental.carl.moe import configuration_dbrx, modeling_dbrx\n", "\n", "importlib.reload(arguments)\n", "importlib.reload(dmoe)\n", "importlib.reload(configuration_dbrx)\n", "importlib.reload(modeling_dbrx)\n", "\n", "_BATCH_SIZE = 1\n", "_SEQ_LEN = 2\n", "_EXPERT_INDEX: int | None = 0\n", "_DTYPE = torch.float16\n", "_DMOE_MLP_IMPL = \"sparse\"\n", "\n", "\n", "def get_input():\n", "    x = torch.randn((_BATCH_SIZE, _SEQ_LEN, hidden_size), device=\"cuda\", dtype=_DTYPE)\n", "    return x\n", "\n", "\n", "def load_sd():\n", "    # State dict containing only the first MoE FFN layer. Weights are:\n", "    # - router.layer.weight\n", "    # - experts.mlp.{w1, v1, w2}\n", "    sd = torch.load(\"/mnt/efs/augment/user/carl/dbrx-single-ffn.pt\")\n", "    print(\"Loaded DBRX state dict from disk\")\n", "    return sd\n", "\n", "\n", "# Config for the released DBRX model\n", "hidden_size = 6144\n", "config = configuration_dbrx.DbrxFFNConfig(\n", "    ffn_hidden_size=10752,\n", "    moe_num_experts=16,\n", "    moe_top_k=1,\n", "    moe_jitter_eps=0.0,\n", "    moe_loss_weight=0.05,\n", ")\n", "print(\"Prepared DBRX config\")\n", "\n", "\n", "def load_dbrx(config: configuration_dbrx.DbrxFFNConfig, state_dict: dict):\n", "    ffn = modeling_dbrx.DbrxFFN(hidden_size, config)\n", "    print(\"Initialized DBRX layer\")\n", "    ffn.load_state_dict(state_dict)\n", "    ffn = ffn.to(device=\"cuda\", dtype=_DTYPE)\n", "    print(\"Loaded DBRX state dict into layer\")\n", "    return ffn\n", "\n", "\n", "def run_dbrx(x: torch.Tensor, ffn: modeling_dbrx.DbrxFFN, with_grad=False):\n", "    gradient_context_manager = torch.enable_grad if with_grad else torch.no_grad\n", "    with gradient_context_manager():\n", "        y_dbrx, dbrx_expert_weights = ffn(\n", "            x\n", "        )\n", "    print(\"DBRX\")\n", "    print(\n", "        y_dbrx.shape,\n", "        dbrx_expert_weights.shape,\n", "    )\n", "    print(\n", "        y_dbrx.dtype,\n", "        dbrx_expert_weights.dtype,\n", "    )\n", "    del ffn\n", "    return y_dbrx, dbrx_expert_weights\n", "\n", "\n", "def load_dmoe(config: configuration_dbrx.DbrxFFNConfig, state_dict: dict):\n", "    # Try to match the DBRX model config with the `dMoE` layer from megablocks\n", "    args = arguments.Arguments(\n", "        hidden_size=hidden_size,\n", "        ffn_hidden_size=config.ffn_hidden_size,\n", "        bias=False,\n", "        activation_fn=torch.nn.functional.silu,\n", "        moe_num_experts=config.moe_num_experts,\n", "        moe_top_k=config.moe_top_k,\n", "        moe_normalize_expert_weights=config.moe_normalize_expert_weights,\n", "        moe_loss_weight=config.moe_loss_weight,\n", "        moe_jitter_eps=config.moe_jitter_eps,\n", "        mlp_type=\"glu\",\n", "        mlp_impl=_DMOE_MLP_IMPL,  # Maybe?\n", "    )\n", "\n", "    moe = dmoe.dMoE(args)\n", "    moe.load_state_dict(state_dict)\n", "    moe = moe.to(device=\"cuda\", dtype=_DTYPE)\n", "    print(\"Loaded dMoE state dict into layer\")\n", "    return moe\n", "\n", "\n", "def run_dmoe(x: torch.Tensor, moe: dmoe.dMoE, with_grad=False):\n", "    gradient_context_manager = torch.enable_grad if with_grad else torch.no_grad\n", "    with gradient_context_manager():\n", "        y_dmoe, dmoe_scores, dmoe_expert_weights, dmoe_top_experts = moe(\n", "            x, _EXPERT_INDEX\n", "        )\n", "    print(\"dMoE\")\n", "    print(\n", "        y_dmoe.shape,\n", "        dmoe_scores.shape,\n", "        dmoe_expert_weights.shape,\n", "        dmoe_top_experts.shape,\n", "    )\n", "    print(\n", "        y_dmoe.dtype,\n", "        dmoe_scores.dtype,\n", "        dmoe_expert_weights.dtype,\n", "        dmoe_top_experts.dtype,\n", "    )\n", "    del moe\n", "    return y_dmoe, dmoe_scores, dmoe_expert_weights, dmoe_top_experts\n", "\n", "\n", "def l2_weght_decay_loss(y: torch.Tensor):\n", "    return torch.norm(y, p=2)\n", "\n", "\n", "def doe_forward(self, x, expert_index=None):\n", "    # NOTE: If we're going to cast the activations to lower precision\n", "    # do it before we permute the tokens to save bandwidth.\n", "    x = common.cast_if_autocast_enabled(x)\n", "\n", "    # Compute the expert scores and assignments.\n", "    scores, expert_weights, top_experts = self.router(x)\n", "    if expert_index is not None:\n", "        top_experts = torch.full_like(top_experts, expert_index)\n", "\n", "    # Compute the experts.\n", "    return (\n", "        self.experts(x, scores, expert_weights, top_experts),\n", "        scores,\n", "        expert_weights,\n", "        top_experts,\n", "    )\n", "\n", "\n", "dmoe.dMoE.forward = doe_forward"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare forward"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DBRX state dict from disk\n", "Initialized DBRX layer\n", "Loaded DBRX state dict into layer\n", "DBRX\n", "torch.<PERSON><PERSON>([1, 2, 6144]) torch.<PERSON><PERSON>([2, 16])\n", "torch.float16 torch.float16\n", "Loaded dMoE state dict into layer\n", "ParallelMLP forward!!!!!\n", "dMoE\n", "torch.<PERSON><PERSON>([1, 2, 6144]) torch.<PERSON><PERSON>([2, 16]) torch.<PERSON><PERSON>([2, 1]) torch.<PERSON><PERSON>([2, 1])\n", "torch.float16 torch.float16 torch.float16 torch.int64\n", "Diff\n", "tensor([[ 0.0000,  0.0000,  0.0005, -0.0010,  0.0010, -0.0005,  0.0005,  0.0000,\n", "          0.0000,  0.0000],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000, -0.0002,  0.0005,  0.0010,  0.0000,\n", "         -0.0005,  0.0010]], device='cuda:0', dtype=torch.float16)\n"]}], "source": ["sd = load_sd()\n", "x = get_input()\n", "moe = load_dmoe(config, sd)\n", "y_dmoe, dmoe_scores, dmoe_expert_weights, dmoe_top_experts = run_dmoe(\n", "    x, moe\n", ")\n", "del moe\n", "ffn = load_dbrx(config, sd)\n", "y_dbrx, dbrx_expert_weights = run_dbrx(\n", "    x, ffn\n", ")\n", "del ffn\n", "print(\"Diff\")\n", "print((y_dbrx - y_dmoe).view(-1, y_dbrx.shape[-1])[:, :10])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([1, 2, 6144]), torch.<PERSON><PERSON>([1, 2, 6144]))"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["y_dbrx.shape, y_dmoe.shape"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["(torch.float16, torch.float16)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["y_dbrx.dtype, y_dmoe.dtype"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["[' -0.00027-0.00098 (+366.797%, nan precs) @ (0, 0, 1031)',\n", " '  0.00033+0.00090 (+273.633%, nan precs) @ (0, 0, 1100)',\n", " ' -0.00053-0.00056 (+107.227%, nan precs) @ (0, 1, 5282)',\n", " '  0.00150+0.00110 (+73.584%, nan precs) @ (0, 0, 2979)',\n", " '  0.00137+0.00090 (+65.674%, nan precs) @ (0, 1, 4866)',\n", " ' -0.00238+0.00156 (-65.381%, nan precs) @ (0, 1, 2296)',\n", " ' -0.00089-0.00052 (+58.398%, nan precs) @ (0, 1, 3997)',\n", " ' -0.00132-0.00067 (+51.172%, nan precs) @ (0, 1, 775)',\n", " '  0.00176-0.00071 (-40.479%, nan precs) @ (0, 1, 1638)',\n", " '  0.00720+0.00183 (+25.415%, nan precs) @ (0, 1, 3118)',\n", " ' -0.00432-0.00098 (+22.681%, nan precs) @ (0, 0, 6073)',\n", " ' -0.00404-0.00081 (+20.117%, nan precs) @ (0, 1, 1322)',\n", " '  0.00739-0.00147 (-19.934%, nan precs) @ (0, 0, 4915)',\n", " '  0.00623+0.00121 (+19.336%, nan precs) @ (0, 1, 1540)',\n", " ' -0.00449-0.00077 (+17.090%, nan precs) @ (0, 0, 4394)',\n", " '  0.00330-0.00050 (-15.027%, nan precs) @ (0, 1, 3809)',\n", " '  0.00570-0.00084 (-14.783%, nan precs) @ (0, 1, 1820)',\n", " '  0.00228+0.00030 (+13.293%, nan precs) @ (0, 1, 2837)',\n", " '  0.00749-0.00098 (-13.086%, nan precs) @ (0, 1, 2216)',\n", " '  0.01530+0.00198 (+12.964%, nan precs) @ (0, 0, 5142)',\n", " '  0.00715-0.00091 (-12.744%, nan precs) @ (0, 1, 1020)',\n", " '  0.01086+0.00098 (+8.990%, nan precs) @ (0, 0, 326)',\n", " ' -0.01852+0.00163 (-8.813%, nan precs) @ (0, 1, 5852)',\n", " ' -0.01453+0.00127 (-8.771%, nan precs) @ (0, 1, 2436)',\n", " ' -0.00161-0.00014 (+8.746%, nan precs) @ (0, 1, 2681)',\n", " ' -0.02563+0.00217 (-8.453%, nan precs) @ (0, 0, 1249)',\n", " ' -0.01210+0.00099 (-8.197%, nan precs) @ (0, 0, 1869)',\n", " '  0.01080-0.00085 (-7.910%, nan precs) @ (0, 0, 4612)',\n", " ' -0.00732-0.00052 (+7.086%, nan precs) @ (0, 1, 5385)',\n", " '  0.02928+0.00203 (+6.934%, nan precs) @ (0, 1, 1994)',\n", " ' -0.02368+0.00162 (-6.830%, nan precs) @ (0, 1, 3761)',\n", " ' -0.01877-0.00124 (+6.586%, nan precs) @ (0, 0, 5186)',\n", " '  0.02069+0.00124 (+5.972%, nan precs) @ (0, 1, 5210)',\n", " ' -0.02252-0.00133 (+5.893%, nan precs) @ (0, 1, 6006)',\n", " ' -0.03317+0.00192 (-5.795%, nan precs) @ (0, 1, 687)',\n", " ' -0.00606+0.00035 (-5.731%, nan precs) @ (0, 0, 1880)',\n", " ' -0.00781+0.00044 (-5.618%, nan precs) @ (0, 0, 4022)',\n", " ' -0.01952-0.00107 (+5.472%, nan precs) @ (0, 0, 1488)',\n", " ' -0.03381+0.00183 (-5.414%, nan precs) @ (0, 1, 3521)',\n", " '  0.02390-0.00128 (-5.365%, nan precs) @ (0, 1, 3927)',\n", " '  0.00765+0.00041 (+5.338%, nan precs) @ (0, 0, 586)',\n", " ' -0.01231-0.00064 (+5.203%, nan precs) @ (0, 0, 837)',\n", " ' -0.00919+0.00040 (-4.401%, nan precs) @ (0, 1, 6011)',\n", " '  0.01146+0.00048 (+4.193%, nan precs) @ (0, 1, 3861)',\n", " ' -0.01038+0.00042 (-4.044%, nan precs) @ (0, 0, 4412)',\n", " '  0.01402+0.00056 (+4.028%, nan precs) @ (0, 0, 5797)',\n", " '  0.00902-0.00036 (-3.976%, nan precs) @ (0, 1, 2090)',\n", " '  0.00442-0.00018 (-3.973%, nan precs) @ (0, 0, 385)',\n", " '  0.01974+0.00078 (+3.940%, nan precs) @ (0, 0, 1948)',\n", " '  0.02124-0.00082 (-3.879%, nan precs) @ (0, 1, 1485)',\n", " ' -0.01419-0.00055 (+3.870%, nan precs) @ (0, 0, 362)',\n", " '  0.01668-0.00064 (-3.842%, nan precs) @ (0, 0, 4016)',\n", " '  0.00321-0.00012 (-3.806%, nan precs) @ (0, 0, 1257)',\n", " ' -0.01347+0.00051 (-3.793%, nan precs) @ (0, 1, 3633)',\n", " '  0.02786+0.00101 (+3.613%, nan precs) @ (0, 1, 5686)',\n", " ' -0.01533+0.00055 (-3.583%, nan precs) @ (0, 0, 467)',\n", " '  0.02097+0.00075 (+3.568%, nan precs) @ (0, 1, 403)',\n", " '  0.00681+0.00024 (+3.531%, nan precs) @ (0, 1, 3279)',\n", " '  0.01216+0.00043 (+3.513%, nan precs) @ (0, 1, 1771)',\n", " ' -0.02206+0.00076 (-3.458%, nan precs) @ (0, 0, 2677)',\n", " ' -0.03903+0.00134 (-3.439%, nan precs) @ (0, 1, 3739)',\n", " ' -0.02908-0.00099 (+3.409%, nan precs) @ (0, 1, 1880)',\n", " ' -0.01712-0.00056 (+3.299%, nan precs) @ (0, 0, 2498)',\n", " ' -0.00751+0.00023 (-3.099%, nan precs) @ (0, 0, 4887)',\n", " '  0.04062+0.00125 (+3.081%, nan precs) @ (0, 1, 1841)',\n", " ' -0.03290+0.00101 (-3.061%, nan precs) @ (0, 0, 5187)',\n", " '  0.02304+0.00070 (+3.046%, nan precs) @ (0, 1, 5607)',\n", " ' -0.00802-0.00024 (+3.044%, nan precs) @ (0, 1, 1656)',\n", " '  0.03827-0.00116 (-3.030%, nan precs) @ (0, 1, 3273)',\n", " '  0.04840+0.00146 (+3.026%, nan precs) @ (0, 1, 2381)',\n", " '  0.03053-0.00092 (-2.998%, nan precs) @ (0, 1, 4719)',\n", " ' -0.02446-0.00073 (+2.994%, nan precs) @ (0, 0, 5937)',\n", " ' -0.05261-0.00150 (+2.843%, nan precs) @ (0, 0, 2010)',\n", " '  0.01691+0.00047 (+2.798%, nan precs) @ (0, 0, 3877)',\n", " ' -0.05524+0.00150 (-2.707%, nan precs) @ (0, 1, 902)',\n", " ' -0.06088+0.00165 (-2.707%, nan precs) @ (0, 1, 2714)',\n", " ' -0.02687-0.00072 (+2.669%, nan precs) @ (0, 0, 2194)',\n", " '  0.03075+0.00081 (+2.631%, nan precs) @ (0, 1, 5554)',\n", " ' -0.03442-0.00089 (+2.571%, nan precs) @ (0, 1, 3326)',\n", " ' -0.04315-0.00110 (+2.547%, nan precs) @ (0, 1, 1826)',\n", " ' -0.02962-0.00073 (+2.473%, nan precs) @ (0, 1, 5297)',\n", " ' -0.06775+0.00165 (-2.432%, nan precs) @ (0, 0, 2627)',\n", " '  0.00946+0.00023 (+2.420%, nan precs) @ (0, 0, 1931)',\n", " '  0.04749+0.00113 (+2.377%, nan precs) @ (0, 1, 4997)',\n", " ' -0.02994+0.00070 (-2.345%, nan precs) @ (0, 0, 1988)',\n", " ' -0.01863-0.00043 (+2.293%, nan precs) @ (0, 0, 1759)',\n", " ' -0.04901-0.00110 (+2.242%, nan precs) @ (0, 1, 2839)',\n", " ' -0.04102-0.00092 (+2.232%, nan precs) @ (0, 1, 5396)',\n", " '  0.05472+0.00122 (+2.231%, nan precs) @ (0, 0, 573)',\n", " ' -0.05652-0.00125 (+2.214%, nan precs) @ (0, 0, 5192)',\n", " '  0.03403-0.00073 (-2.153%, nan precs) @ (0, 1, 4442)',\n", " '  0.06250+0.00134 (+2.148%, nan precs) @ (0, 0, 2626)',\n", " '  0.03448+0.00073 (+2.124%, nan precs) @ (0, 0, 2708)',\n", " ' -0.01439-0.00031 (+2.121%, nan precs) @ (0, 1, 845)',\n", " ' -0.05377+0.00113 (-2.100%, nan precs) @ (0, 0, 2229)',\n", " '  0.05240+0.00110 (+2.097%, nan precs) @ (0, 0, 3395)',\n", " '  0.09662+0.00201 (+2.084%, nan precs) @ (0, 0, 4580)',\n", " ' -0.06168+0.00128 (-2.078%, nan precs) @ (0, 0, 2120)',\n", " ' -0.01782+0.00037 (-2.055%, nan precs) @ (0, 0, 1992)',\n", " ' -0.02109+0.00043 (-2.026%, nan precs) @ (0, 0, 1406)',\n", " ' -0.01254+0.00025 (-2.008%, nan precs) @ (0, 1, 3550)',\n", " ' -0.05945-0.00119 (+2.002%, nan precs) @ (0, 1, 2369)',\n", " ' -0.02524+0.00050 (-1.996%, nan precs) @ (0, 1, 1792)',\n", " '  0.04184+0.00082 (+1.970%, nan precs) @ (0, 1, 2611)',\n", " '  0.04221-0.00082 (-1.952%, nan precs) @ (0, 0, 2075)',\n", " ' -0.06683+0.00128 (-1.918%, nan precs) @ (0, 0, 4600)',\n", " ' -0.07013-0.00134 (+1.915%, nan precs) @ (0, 1, 933)',\n", " '  0.04648+0.00089 (+1.904%, nan precs) @ (0, 0, 4030)',\n", " '  0.04355+0.00082 (+1.892%, nan precs) @ (0, 1, 6048)',\n", " ' -0.02361+0.00044 (-1.875%, nan precs) @ (0, 0, 2519)',\n", " '  0.04239-0.00079 (-1.872%, nan precs) @ (0, 1, 310)',\n", " '  0.07941-0.00146 (-1.845%, nan precs) @ (0, 0, 1554)',\n", " '  0.03189-0.00058 (-1.819%, nan precs) @ (0, 1, 3507)',\n", " ' -0.03555+0.00064 (-1.802%, nan precs) @ (0, 0, 2386)',\n", " ' -0.03571-0.00064 (+1.794%, nan precs) @ (0, 0, 2737)',\n", " ' -0.05792+0.00104 (-1.791%, nan precs) @ (0, 1, 1564)',\n", " ' -0.04956-0.00089 (+1.785%, nan precs) @ (0, 0, 5564)',\n", " '  0.01712+0.00031 (+1.782%, nan precs) @ (0, 1, 2454)',\n", " '  0.05316-0.00095 (-1.779%, nan precs) @ (0, 1, 3831)',\n", " '  0.01990-0.00035 (-1.764%, nan precs) @ (0, 1, 4695)',\n", " ' -0.01105+0.00019 (-1.726%, nan precs) @ (0, 1, 3383)',\n", " '  0.03055-0.00052 (-1.698%, nan precs) @ (0, 1, 4539)',\n", " '  0.05096+0.00085 (+1.677%, nan precs) @ (0, 1, 5055)',\n", " '  0.10077-0.00165 (-1.636%, nan precs) @ (0, 0, 911)',\n", " ' -0.06714+0.00110 (-1.636%, nan precs) @ (0, 0, 6133)',\n", " '  0.03970+0.00064 (+1.614%, nan precs) @ (0, 0, 2579)',\n", " ' -0.01102-0.00018 (+1.593%, nan precs) @ (0, 1, 5361)',\n", " ' -0.09271+0.00146 (-1.579%, nan precs) @ (0, 1, 4926)',\n", " ' -0.01956-0.00031 (+1.560%, nan precs) @ (0, 1, 676)',\n", " ' -0.03729+0.00058 (-1.555%, nan precs) @ (0, 1, 3316)',\n", " ' -0.03741-0.00058 (+1.550%, nan precs) @ (0, 0, 5169)',\n", " '  0.01700-0.00026 (-1.526%, nan precs) @ (0, 1, 3651)',\n", " '  0.12830+0.00195 (+1.522%, nan precs) @ (0, 1, 2820)',\n", " ' -0.06024-0.00092 (+1.520%, nan precs) @ (0, 1, 4326)',\n", " ' -0.02036-0.00031 (+1.499%, nan precs) @ (0, 0, 1921)',\n", " '  0.17969+0.00269 (+1.495%, nan precs) @ (0, 0, 5154)',\n", " '  0.04706+0.00070 (+1.492%, nan precs) @ (0, 0, 4425)',\n", " '  0.08728-0.00128 (-1.469%, nan precs) @ (0, 1, 2630)',\n", " '  0.02603-0.00038 (-1.466%, nan precs) @ (0, 1, 5104)',\n", " ' -0.07928-0.00116 (+1.463%, nan precs) @ (0, 1, 1730)',\n", " ' -0.05676-0.00082 (+1.452%, nan precs) @ (0, 1, 5316)',\n", " '  0.04419-0.00064 (-1.450%, nan precs) @ (0, 1, 5666)',\n", " ' -0.05493-0.00079 (+1.444%, nan precs) @ (0, 1, 5216)',\n", " '  0.06174+0.00089 (+1.434%, nan precs) @ (0, 1, 1963)',\n", " '  0.04074-0.00058 (-1.423%, nan precs) @ (0, 0, 2267)',\n", " ' -0.08179+0.00116 (-1.418%, nan precs) @ (0, 0, 2427)',\n", " ' -0.01724+0.00024 (-1.416%, nan precs) @ (0, 0, 3604)',\n", " '  0.03888-0.00055 (-1.413%, nan precs) @ (0, 1, 3527)',\n", " ' -0.05438-0.00076 (+1.403%, nan precs) @ (0, 1, 3540)',\n", " ' -0.07874+0.00110 (-1.395%, nan precs) @ (0, 1, 2404)',\n", " '  0.10089+0.00140 (+1.392%, nan precs) @ (0, 0, 2692)',\n", " ' -0.10321+0.00140 (-1.360%, nan precs) @ (0, 0, 485)',\n", " '  0.00449-0.00006 (-1.360%, nan precs) @ (0, 0, 2364)',\n", " ' -0.13513+0.00183 (-1.355%, nan precs) @ (0, 1, 547)',\n", " '  0.07666-0.00104 (-1.353%, nan precs) @ (0, 0, 2911)',\n", " '  0.04572+0.00061 (+1.335%, nan precs) @ (0, 0, 1857)',\n", " '  0.07391-0.00098 (-1.321%, nan precs) @ (0, 1, 4262)',\n", " ' -0.06104-0.00079 (+1.300%, nan precs) @ (0, 1, 3708)',\n", " ' -0.07996-0.00104 (+1.298%, nan precs) @ (0, 1, 778)',\n", " ' -0.08521-0.00110 (+1.289%, nan precs) @ (0, 1, 1275)',\n", " ' -0.08167+0.00104 (-1.270%, nan precs) @ (0, 1, 3749)',\n", " ' -0.07214+0.00092 (-1.269%, nan precs) @ (0, 1, 2723)',\n", " ' -0.04095+0.00052 (-1.266%, nan precs) @ (0, 1, 349)',\n", " ' -0.15613+0.00195 (-1.251%, nan precs) @ (0, 0, 2934)',\n", " ' -0.11267+0.00140 (-1.246%, nan precs) @ (0, 1, 231)',\n", " '  0.06378+0.00079 (+1.244%, nan precs) @ (0, 0, 4289)',\n", " '  0.09338-0.00116 (-1.242%, nan precs) @ (0, 1, 3291)',\n", " '  0.04453-0.00055 (-1.234%, nan precs) @ (0, 0, 3892)',\n", " ' -0.19836-0.00244 (+1.231%, nan precs) @ (0, 0, 52)',\n", " '  0.02994+0.00037 (+1.223%, nan precs) @ (0, 1, 2388)',\n", " '  0.01376+0.00017 (+1.219%, nan precs) @ (0, 0, 4102)',\n", " '  0.03262-0.00040 (-1.216%, nan precs) @ (0, 1, 1998)',\n", " ' -0.10577+0.00128 (-1.212%, nan precs) @ (0, 0, 3569)',\n", " '  0.04306-0.00052 (-1.205%, nan precs) @ (0, 0, 5900)',\n", " '  0.05341+0.00064 (+1.200%, nan precs) @ (0, 1, 1542)',\n", " '  0.11206-0.00134 (-1.199%, nan precs) @ (0, 0, 4698)',\n", " '  0.08667+0.00104 (+1.197%, nan precs) @ (0, 1, 4973)',\n", " ' -0.08728-0.00104 (+1.189%, nan precs) @ (0, 1, 4987)',\n", " '  0.04141+0.00049 (+1.179%, nan precs) @ (0, 1, 2799)',\n", " '  0.06006-0.00070 (-1.169%, nan precs) @ (0, 0, 3292)',\n", " '  0.02234-0.00026 (-1.161%, nan precs) @ (0, 1, 3379)',\n", " ' -0.05014-0.00058 (+1.157%, nan precs) @ (0, 1, 4808)',\n", " ' -0.09528-0.00110 (+1.153%, nan precs) @ (0, 0, 5858)',\n", " ' -0.08478+0.00098 (-1.152%, nan precs) @ (0, 1, 388)',\n", " '  0.06396-0.00073 (-1.145%, nan precs) @ (0, 1, 3377)',\n", " ' -0.04800-0.00055 (+1.144%, nan precs) @ (0, 0, 5516)',\n", " '  0.10681-0.00122 (-1.143%, nan precs) @ (0, 1, 4805)',\n", " '  0.04541+0.00052 (+1.142%, nan precs) @ (0, 1, 5829)',\n", " ' -0.10193-0.00116 (+1.138%, nan precs) @ (0, 0, 3779)',\n", " ' -0.10742-0.00122 (+1.136%, nan precs) @ (0, 0, 4839)',\n", " ' -0.12891-0.00146 (+1.136%, nan precs) @ (0, 0, 5348)',\n", " '  0.02437+0.00027 (+1.127%, nan precs) @ (0, 1, 1394)',\n", " ' -0.10956-0.00122 (+1.114%, nan precs) @ (0, 0, 3495)',\n", " '  0.04404+0.00049 (+1.109%, nan precs) @ (0, 0, 2560)',\n", " ' -0.04160-0.00046 (+1.100%, nan precs) @ (0, 0, 400)',\n", " ' -0.05853-0.00064 (+1.095%, nan precs) @ (0, 1, 5710)',\n", " '  0.05859-0.00064 (-1.094%, nan precs) @ (0, 1, 5579)',\n", " '  0.16772+0.00183 (+1.092%, nan precs) @ (0, 0, 6021)',\n", " '  0.10718+0.00116 (+1.082%, nan precs) @ (0, 0, 284)',\n", " ' -0.05081-0.00055 (+1.081%, nan precs) @ (0, 0, 2354)',\n", " '  0.10162-0.00110 (-1.081%, nan precs) @ (0, 1, 5086)',\n", " ' -0.05936-0.00064 (+1.080%, nan precs) @ (0, 1, 5980)',\n", " ' -0.06226+0.00067 (-1.079%, nan precs) @ (0, 1, 526)',\n", " '  0.03403+0.00037 (+1.077%, nan precs) @ (0, 1, 6032)',\n", " '  0.19312+0.00208 (+1.074%, nan precs) @ (0, 0, 560)',\n", " '  0.05429-0.00058 (-1.068%, nan precs) @ (0, 0, 5584)',\n", " '  0.09155+0.00098 (+1.067%, nan precs) @ (0, 1, 4846)',\n", " '  0.13879-0.00146 (-1.055%, nan precs) @ (0, 1, 5447)',\n", " '  0.03036+0.00032 (+1.055%, nan precs) @ (0, 1, 5922)',\n", " ' -0.06378-0.00067 (+1.053%, nan precs) @ (0, 1, 3340)',\n", " '  0.12231-0.00128 (-1.048%, nan precs) @ (0, 1, 4130)',\n", " ' -0.10510+0.00110 (-1.045%, nan precs) @ (0, 1, 3676)',\n", " '  0.09412-0.00098 (-1.038%, nan precs) @ (0, 0, 650)',\n", " ' -0.05609-0.00058 (+1.034%, nan precs) @ (0, 0, 6057)',\n", " ' -0.08960-0.00092 (+1.022%, nan precs) @ (0, 1, 4122)',\n", " '  0.05981-0.00061 (-1.020%, nan precs) @ (0, 1, 5347)',\n", " '  0.05090-0.00052 (-1.019%, nan precs) @ (0, 0, 60)',\n", " ' -0.07831-0.00079 (+1.013%, nan precs) @ (0, 0, 49)',\n", " '  0.09650-0.00098 (-1.012%, nan precs) @ (0, 1, 28)',\n", " '  0.13293-0.00134 (-1.010%, nan precs) @ (0, 0, 5330)',\n", " '  0.03937+0.00040 (+1.008%, nan precs) @ (0, 0, 2885)',\n", " '  0.05231-0.00052 (-0.992%, nan precs) @ (0, 1, 4845)',\n", " '  0.08026-0.00079 (-0.989%, nan precs) @ (0, 1, 4983)',\n", " '  0.17395+0.00171 (+0.983%, nan precs) @ (0, 0, 786)',\n", " ' -0.06836-0.00067 (+0.982%, nan precs) @ (0, 1, 4963)',\n", " ' -0.03745+0.00037 (-0.978%, nan precs) @ (0, 0, 187)',\n", " '  0.01487-0.00014 (-0.975%, nan precs) @ (0, 1, 4403)',\n", " '  0.18835+0.00183 (+0.972%, nan precs) @ (0, 0, 5795)',\n", " '  0.08813+0.00085 (+0.970%, nan precs) @ (0, 1, 2755)',\n", " ' -0.08203+0.00079 (-0.967%, nan precs) @ (0, 0, 4794)',\n", " '  0.06317+0.00061 (+0.966%, nan precs) @ (0, 0, 2656)',\n", " ' -0.08264+0.00079 (-0.960%, nan precs) @ (0, 1, 1302)',\n", " '  0.11658+0.00110 (+0.942%, nan precs) @ (0, 0, 2125)',\n", " '  0.02429+0.00023 (+0.942%, nan precs) @ (0, 1, 4673)',\n", " '  0.07782-0.00073 (-0.941%, nan precs) @ (0, 1, 1674)',\n", " '  0.20850-0.00195 (-0.937%, nan precs) @ (0, 1, 5615)',\n", " '  0.05902+0.00055 (+0.931%, nan precs) @ (0, 1, 3822)',\n", " ' -0.05908-0.00055 (+0.930%, nan precs) @ (0, 1, 1048)',\n", " ' -0.08551+0.00079 (-0.928%, nan precs) @ (0, 1, 324)',\n", " '  0.00834-0.00008 (-0.915%, nan precs) @ (0, 0, 581)',\n", " ' -0.08679+0.00079 (-0.914%, nan precs) @ (0, 0, 1006)',\n", " ' -0.10120+0.00092 (-0.905%, nan precs) @ (0, 1, 3130)',\n", " '  0.02870-0.00026 (-0.904%, nan precs) @ (0, 1, 2458)',\n", " ' -0.12170+0.00110 (-0.903%, nan precs) @ (0, 0, 4305)',\n", " '  0.05417-0.00049 (-0.901%, nan precs) @ (0, 1, 1874)',\n", " '  0.07568+0.00067 (+0.887%, nan precs) @ (0, 0, 2315)',\n", " ' -0.10474+0.00092 (-0.874%, nan precs) @ (0, 0, 2898)',\n", " '  0.05624-0.00049 (-0.868%, nan precs) @ (0, 0, 2375)',\n", " ' -0.10553-0.00092 (+0.867%, nan precs) @ (0, 1, 6020)',\n", " '  0.02289+0.00020 (+0.867%, nan precs) @ (0, 1, 3350)',\n", " ' -0.06348+0.00055 (-0.865%, nan precs) @ (0, 1, 3976)',\n", " '  0.14136+0.00122 (+0.864%, nan precs) @ (0, 0, 6007)',\n", " ' -0.15576+0.00134 (-0.862%, nan precs) @ (0, 0, 3821)',\n", " '  0.08502+0.00073 (+0.861%, nan precs) @ (0, 0, 139)',\n", " '  0.12061-0.00104 (-0.861%, nan precs) @ (0, 1, 3080)',\n", " '  0.09937-0.00085 (-0.860%, nan precs) @ (0, 1, 5976)',\n", " ' -0.09955+0.00085 (-0.858%, nan precs) @ (0, 0, 3736)',\n", " ' -0.07135-0.00061 (+0.855%, nan precs) @ (0, 1, 2000)',\n", " ' -0.01253+0.00011 (-0.853%, nan precs) @ (0, 0, 2602)',\n", " '  0.12177-0.00104 (-0.852%, nan precs) @ (0, 1, 5705)',\n", " '  0.09387+0.00079 (+0.845%, nan precs) @ (0, 1, 788)',\n", " ' -0.14539-0.00122 (+0.840%, nan precs) @ (0, 1, 1354)',\n", " ' -0.10907-0.00092 (+0.839%, nan precs) @ (0, 0, 132)',\n", " ' -0.08728-0.00073 (+0.839%, nan precs) @ (0, 0, 2834)',\n", " '  0.10181-0.00085 (-0.839%, nan precs) @ (0, 1, 4790)',\n", " '  0.05832-0.00049 (-0.837%, nan precs) @ (0, 0, 2812)',\n", " '  0.10236-0.00085 (-0.835%, nan precs) @ (0, 1, 3156)',\n", " '  0.23450-0.00195 (-0.833%, nan precs) @ (0, 0, 2843)',\n", " ' -0.13184+0.00110 (-0.833%, nan precs) @ (0, 0, 4661)',\n", " '  0.20593-0.00171 (-0.830%, nan precs) @ (0, 1, 175)',\n", " '  0.11041+0.00092 (+0.829%, nan precs) @ (0, 0, 1856)',\n", " ' -0.04791+0.00040 (-0.828%, nan precs) @ (0, 1, 5932)',\n", " ' -0.13354+0.00110 (-0.822%, nan precs) @ (0, 0, 1676)',\n", " ' -0.08942-0.00073 (+0.819%, nan precs) @ (0, 0, 2830)',\n", " ' -0.11938-0.00098 (+0.818%, nan precs) @ (0, 1, 156)',\n", " '  0.20886-0.00171 (-0.818%, nan precs) @ (0, 1, 3541)',\n", " ' -0.08966-0.00073 (+0.817%, nan precs) @ (0, 1, 2701)',\n", " ' -0.11249-0.00092 (+0.814%, nan precs) @ (0, 1, 1232)',\n", " '  0.18140+0.00146 (+0.807%, nan precs) @ (0, 0, 6025)',\n", " '  0.01892+0.00015 (+0.806%, nan precs) @ (0, 0, 2122)',\n", " '  0.18176-0.00146 (-0.806%, nan precs) @ (0, 0, 4576)',\n", " ' -0.12164+0.00098 (-0.803%, nan precs) @ (0, 1, 3632)',\n", " ' -0.11426-0.00092 (+0.801%, nan precs) @ (0, 0, 281)',\n", " ' -0.16833-0.00134 (+0.798%, nan precs) @ (0, 0, 5812)',\n", " '  0.02486-0.00020 (-0.798%, nan precs) @ (0, 1, 601)',\n", " ' -0.01056+0.00008 (-0.795%, nan precs) @ (0, 0, 469)',\n", " '  0.16895-0.00134 (-0.795%, nan precs) @ (0, 0, 5767)',\n", " '  0.08496+0.00067 (+0.790%, nan precs) @ (0, 1, 3570)',\n", " ' -0.08514-0.00067 (+0.789%, nan precs) @ (0, 1, 3964)',\n", " '  0.09296+0.00073 (+0.788%, nan precs) @ (0, 1, 1566)',\n", " '  0.13940-0.00110 (-0.788%, nan precs) @ (0, 1, 5033)',\n", " ' -0.11633-0.00092 (+0.787%, nan precs) @ (0, 1, 2763)',\n", " ' -0.06241+0.00049 (-0.783%, nan precs) @ (0, 0, 4738)',\n", " '  0.14099+0.00110 (+0.779%, nan precs) @ (0, 1, 976)',\n", " ' -0.17236+0.00134 (-0.779%, nan precs) @ (0, 0, 6078)',\n", " '  0.07092-0.00055 (-0.774%, nan precs) @ (0, 0, 2693)',\n", " '  0.09460+0.00073 (+0.774%, nan precs) @ (0, 1, 3904)',\n", " '  0.14233-0.00110 (-0.772%, nan precs) @ (0, 1, 2764)',\n", " ' -0.22253-0.00171 (+0.768%, nan precs) @ (0, 1, 5059)',\n", " '  0.10345+0.00079 (+0.767%, nan precs) @ (0, 0, 4495)',\n", " '  0.12817-0.00098 (-0.762%, nan precs) @ (0, 0, 5338)',\n", " '  0.09625+0.00073 (+0.761%, nan precs) @ (0, 1, 1338)',\n", " '  0.08862-0.00067 (-0.758%, nan precs) @ (0, 1, 3376)',\n", " ' -0.17773+0.00134 (-0.755%, nan precs) @ (0, 1, 1246)',\n", " '  0.11377+0.00085 (+0.751%, nan precs) @ (0, 1, 4733)',\n", " ' -0.17896-0.00134 (+0.750%, nan precs) @ (0, 0, 3854)',\n", " '  0.09052-0.00067 (-0.742%, nan precs) @ (0, 1, 220)',\n", " ' -0.05765-0.00043 (+0.741%, nan precs) @ (0, 0, 21)',\n", " ' -0.03714+0.00027 (-0.740%, nan precs) @ (0, 1, 3077)',\n", " ' -0.09912-0.00073 (+0.739%, nan precs) @ (0, 1, 4918)',\n", " '  0.21558+0.00159 (+0.736%, nan precs) @ (0, 0, 3880)',\n", " '  0.07507-0.00055 (-0.732%, nan precs) @ (0, 0, 407)',\n", " ' -0.09241-0.00067 (+0.727%, nan precs) @ (0, 1, 95)',\n", " '  0.10138-0.00073 (-0.723%, nan precs) @ (0, 1, 4737)',\n", " '  0.06787-0.00049 (-0.719%, nan precs) @ (0, 0, 811)',\n", " '  0.13660-0.00098 (-0.715%, nan precs) @ (0, 1, 5509)',\n", " '  0.08612-0.00061 (-0.709%, nan precs) @ (0, 1, 2335)',\n", " '  0.07764-0.00055 (-0.708%, nan precs) @ (0, 0, 5479)',\n", " ' -0.05615+0.00040 (-0.706%, nan precs) @ (0, 0, 5962)',\n", " '  0.04752+0.00034 (+0.706%, nan precs) @ (0, 1, 5651)',\n", " '  0.19043+0.00134 (+0.705%, nan precs) @ (0, 0, 2008)',\n", " ' -0.05670-0.00040 (+0.700%, nan precs) @ (0, 1, 1796)',\n", " '  0.21130+0.00146 (+0.693%, nan precs) @ (0, 1, 2110)',\n", " '  0.04852+0.00034 (+0.692%, nan precs) @ (0, 0, 4328)',\n", " ' -0.09723-0.00067 (+0.690%, nan precs) @ (0, 1, 6123)',\n", " ' -0.03979-0.00027 (+0.690%, nan precs) @ (0, 1, 4073)',\n", " '  0.09735-0.00067 (-0.690%, nan precs) @ (0, 0, 735)',\n", " '  0.21240-0.00146 (-0.690%, nan precs) @ (0, 0, 5899)',\n", " '  0.07983-0.00055 (-0.688%, nan precs) @ (0, 1, 3515)',\n", " '  0.05792+0.00040 (+0.685%, nan precs) @ (0, 1, 3367)',\n", " ' -0.14319+0.00098 (-0.682%, nan precs) @ (0, 0, 1916)',\n", " ' -0.05844+0.00040 (-0.679%, nan precs) @ (0, 1, 685)',\n", " ' -0.09900-0.00067 (+0.678%, nan precs) @ (0, 0, 4902)',\n", " ' -0.06317-0.00043 (+0.676%, nan precs) @ (0, 1, 1652)',\n", " '  0.19885+0.00134 (+0.675%, nan precs) @ (0, 0, 3015)',\n", " ' -0.23560-0.00159 (+0.674%, nan precs) @ (0, 0, 2770)',\n", " '  0.07251-0.00049 (-0.673%, nan precs) @ (0, 0, 248)',\n", " ' -0.09064+0.00061 (-0.673%, nan precs) @ (0, 1, 5469)',\n", " ' -0.07294+0.00049 (-0.669%, nan precs) @ (0, 0, 5011)',\n", " '  0.21875+0.00146 (+0.669%, nan precs) @ (0, 0, 5631)',\n", " ' -0.14612-0.00098 (+0.668%, nan precs) @ (0, 1, 5854)',\n", " '  0.05948-0.00040 (-0.667%, nan precs) @ (0, 1, 5040)',\n", " '  0.12878-0.00085 (-0.663%, nan precs) @ (0, 0, 5325)',\n", " '  0.11975+0.00079 (+0.663%, nan precs) @ (0, 0, 2644)',\n", " ' -0.03235-0.00021 (+0.660%, nan precs) @ (0, 0, 2430)',\n", " ' -0.05557+0.00037 (-0.659%, nan precs) @ (0, 0, 57)',\n", " '  0.11115-0.00073 (-0.659%, nan precs) @ (0, 1, 1520)',\n", " ' -0.18652-0.00122 (+0.655%, nan precs) @ (0, 1, 266)',\n", " ' -0.16833-0.00110 (+0.653%, nan precs) @ (0, 1, 1279)',\n", " '  0.18726+0.00122 (+0.652%, nan precs) @ (0, 1, 5742)',\n", " ' -0.16919-0.00110 (+0.649%, nan precs) @ (0, 0, 1419)',\n", " ' -0.22607+0.00146 (-0.648%, nan precs) @ (0, 1, 975)',\n", " ' -0.06616-0.00043 (+0.646%, nan precs) @ (0, 0, 3885)',\n", " '  0.05673+0.00037 (+0.645%, nan precs) @ (0, 1, 1981)',\n", " '  0.34131-0.00220 (-0.644%, nan precs) @ (0, 0, 4267)',\n", " ' -0.08582+0.00055 (-0.640%, nan precs) @ (0, 1, 3520)',\n", " ' -0.15332+0.00098 (-0.637%, nan precs) @ (0, 0, 1883)',\n", " '  0.02646-0.00017 (-0.634%, nan precs) @ (0, 1, 3029)',\n", " '  0.15491+0.00098 (+0.631%, nan precs) @ (0, 0, 285)',\n", " '  0.06775+0.00043 (+0.631%, nan precs) @ (0, 0, 5520)',\n", " '  0.06781-0.00043 (-0.630%, nan precs) @ (0, 0, 4910)',\n", " '  0.11652-0.00073 (-0.629%, nan precs) @ (0, 1, 3430)',\n", " ' -0.13599-0.00085 (+0.628%, nan precs) @ (0, 1, 6120)',\n", " '  0.21387+0.00134 (+0.628%, nan precs) @ (0, 0, 3805)',\n", " ' -0.17603-0.00110 (+0.624%, nan precs) @ (0, 0, 2272)',\n", " ' -0.11792+0.00073 (-0.621%, nan precs) @ (0, 0, 97)',\n", " '  0.19678-0.00122 (-0.620%, nan precs) @ (0, 1, 4566)',\n", " ' -0.17798-0.00110 (+0.617%, nan precs) @ (0, 0, 1868)',\n", " ' -0.09894-0.00061 (+0.617%, nan precs) @ (0, 1, 3092)',\n", " '  0.01118-0.00007 (-0.614%, nan precs) @ (0, 1, 1850)',\n", " '  0.14014+0.00085 (+0.610%, nan precs) @ (0, 1, 454)',\n", " '  0.05014+0.00031 (+0.609%, nan precs) @ (0, 1, 13)',\n", " '  0.16064+0.00098 (+0.608%, nan precs) @ (0, 0, 4969)',\n", " ' -0.18225+0.00110 (-0.603%, nan precs) @ (0, 0, 4096)',\n", " '  0.05087-0.00031 (-0.600%, nan precs) @ (0, 1, 5692)',\n", " ' -0.09167-0.00055 (+0.599%, nan precs) @ (0, 1, 5812)',\n", " ' -0.18384-0.00110 (+0.598%, nan precs) @ (0, 1, 1244)',\n", " ' -0.08173+0.00049 (-0.597%, nan precs) @ (0, 0, 730)',\n", " ' -0.04608+0.00027 (-0.596%, nan precs) @ (0, 1, 809)',\n", " ' -0.14368+0.00085 (-0.595%, nan precs) @ (0, 0, 4487)',\n", " ' -0.07214-0.00043 (+0.592%, nan precs) @ (0, 0, 1371)',\n", " ' -0.14563+0.00085 (-0.587%, nan precs) @ (0, 1, 3619)',\n", " '  0.33374+0.00195 (+0.585%, nan precs) @ (0, 1, 2563)',\n", " ' -0.33569+0.00195 (-0.582%, nan precs) @ (0, 0, 4735)',\n", " '  0.29395-0.00171 (-0.581%, nan precs) @ (0, 1, 3587)',\n", " ' -0.12634-0.00073 (+0.580%, nan precs) @ (0, 0, 4885)',\n", " ' -0.07373+0.00043 (-0.579%, nan precs) @ (0, 1, 5108)',\n", " ' -0.01584-0.00009 (+0.578%, nan precs) @ (0, 1, 3185)',\n", " ' -0.23254+0.00134 (-0.578%, nan precs) @ (0, 1, 1166)',\n", " '  0.23291+0.00134 (+0.576%, nan precs) @ (0, 0, 2833)',\n", " ' -0.19104+0.00110 (-0.575%, nan precs) @ (0, 0, 5662)',\n", " '  0.14893-0.00085 (-0.574%, nan precs) @ (0, 1, 591)',\n", " ' -0.21301-0.00122 (+0.573%, nan precs) @ (0, 0, 697)',\n", " ' -0.05862-0.00034 (+0.573%, nan precs) @ (0, 0, 3266)',\n", " ' -0.10675-0.00061 (+0.572%, nan precs) @ (0, 1, 2387)',\n", " '  0.21509-0.00122 (-0.568%, nan precs) @ (0, 0, 6054)',\n", " '  0.12939-0.00073 (-0.566%, nan precs) @ (0, 1, 4024)',\n", " ' -0.11877-0.00067 (+0.565%, nan precs) @ (0, 0, 3949)',\n", " '  0.25928-0.00146 (-0.565%, nan precs) @ (0, 0, 4595)',\n", " '  0.13037-0.00073 (-0.562%, nan precs) @ (0, 0, 5248)',\n", " '  0.05438-0.00031 (-0.561%, nan precs) @ (0, 0, 422)',\n", " '  0.30737-0.00171 (-0.556%, nan precs) @ (0, 0, 4855)',\n", " ' -0.26343-0.00146 (+0.556%, nan precs) @ (0, 0, 5215)',\n", " '  0.19751+0.00110 (+0.556%, nan precs) @ (0, 1, 1954)',\n", " ' -0.17566-0.00098 (+0.556%, nan precs) @ (0, 1, 5397)',\n", " ' -0.12085+0.00067 (-0.555%, nan precs) @ (0, 1, 6027)',\n", " '  0.19824-0.00110 (-0.554%, nan precs) @ (0, 0, 193)',\n", " ' -0.22095+0.00122 (-0.552%, nan precs) @ (0, 1, 1672)',\n", " '  0.17700+0.00098 (+0.552%, nan precs) @ (0, 1, 4663)',\n", " '  0.04453-0.00024 (-0.548%, nan precs) @ (0, 1, 282)',\n", " ' -0.31201-0.00171 (+0.548%, nan precs) @ (0, 0, 6052)',\n", " '  0.20117+0.00110 (+0.546%, nan precs) @ (0, 0, 4788)',\n", " '  0.13416+0.00073 (+0.546%, nan precs) @ (0, 0, 436)',\n", " '  0.08948+0.00049 (+0.546%, nan precs) @ (0, 1, 5069)',\n", " ' -0.24609+0.00134 (-0.546%, nan precs) @ (0, 0, 4729)',\n", " ' -0.20142-0.00110 (+0.546%, nan precs) @ (0, 1, 2886)',\n", " '  0.22400+0.00122 (+0.545%, nan precs) @ (0, 1, 5124)',\n", " '  0.20239+0.00110 (+0.543%, nan precs) @ (0, 0, 5679)',\n", " ' -0.15747+0.00085 (-0.542%, nan precs) @ (0, 1, 1597)',\n", " '  0.22522+0.00122 (+0.542%, nan precs) @ (0, 1, 5345)',\n", " '  0.13525+0.00073 (+0.542%, nan precs) @ (0, 0, 3449)',\n", " '  0.05078+0.00027 (+0.541%, nan precs) @ (0, 0, 751)',\n", " '  0.09033-0.00049 (-0.541%, nan precs) @ (0, 0, 149)',\n", " '  0.05646-0.00031 (-0.541%, nan precs) @ (0, 0, 1644)',\n", " ' -0.49731-0.00269 (+0.540%, nan precs) @ (0, 0, 247)',\n", " '  0.06241+0.00034 (+0.538%, nan precs) @ (0, 1, 5090)',\n", " ' -0.03406-0.00018 (+0.537%, nan precs) @ (0, 1, 2467)',\n", " '  0.25024-0.00134 (-0.537%, nan precs) @ (0, 1, 4010)',\n", " ' -0.15942-0.00085 (+0.536%, nan precs) @ (0, 1, 106)',\n", " '  0.13721-0.00073 (-0.534%, nan precs) @ (0, 1, 3442)',\n", " '  0.36694+0.00195 (+0.532%, nan precs) @ (0, 0, 2021)',\n", " ' -0.09192+0.00049 (-0.531%, nan precs) @ (0, 1, 2277)',\n", " '  0.06903-0.00037 (-0.531%, nan precs) @ (0, 0, 3118)',\n", " ' -0.18420+0.00098 (-0.530%, nan precs) @ (0, 0, 663)',\n", " ' -0.27930-0.00146 (+0.525%, nan precs) @ (0, 0, 4956)',\n", " '  0.37378-0.00195 (-0.523%, nan precs) @ (0, 0, 1516)',\n", " '  0.07025-0.00037 (-0.521%, nan precs) @ (0, 1, 1164)',\n", " ' -0.21106+0.00110 (-0.521%, nan precs) @ (0, 1, 1212)',\n", " ' -0.14075-0.00073 (+0.520%, nan precs) @ (0, 0, 2981)',\n", " ' -0.08215+0.00043 (-0.520%, nan precs) @ (0, 1, 1198)',\n", " '  0.23608+0.00122 (+0.517%, nan precs) @ (0, 1, 1539)',\n", " ' -0.08270+0.00043 (-0.517%, nan precs) @ (0, 0, 3333)',\n", " ' -0.19019+0.00098 (-0.513%, nan precs) @ (0, 1, 433)',\n", " ' -0.09509+0.00049 (-0.513%, nan precs) @ (0, 1, 5369)',\n", " ' -0.04767+0.00024 (-0.512%, nan precs) @ (0, 1, 108)',\n", " ' -0.23840-0.00122 (+0.512%, nan precs) @ (0, 0, 1698)',\n", " ' -0.14319-0.00073 (+0.512%, nan precs) @ (0, 1, 2233)',\n", " ' -0.14331+0.00073 (-0.511%, nan precs) @ (0, 0, 4847)',\n", " '  0.23926-0.00122 (-0.510%, nan precs) @ (0, 0, 2941)',\n", " '  0.14380+0.00073 (+0.509%, nan precs) @ (0, 0, 2903)',\n", " '  0.28760-0.00146 (-0.509%, nan precs) @ (0, 1, 5356)',\n", " ' -0.21606+0.00110 (-0.508%, nan precs) @ (0, 0, 4039)',\n", " '  0.07227-0.00037 (-0.507%, nan precs) @ (0, 1, 248)',\n", " '  0.08441-0.00043 (-0.506%, nan precs) @ (0, 0, 5930)',\n", " '  0.21753+0.00110 (+0.505%, nan precs) @ (0, 0, 2063)',\n", " ' -0.33911-0.00171 (+0.504%, nan precs) @ (0, 0, 4314)',\n", " ' -0.24243+0.00122 (-0.504%, nan precs) @ (0, 0, 3411)',\n", " ' -0.16992+0.00085 (-0.503%, nan precs) @ (0, 1, 5724)',\n", " '  0.29175+0.00146 (+0.502%, nan precs) @ (0, 1, 677)',\n", " ' -0.21887+0.00110 (-0.502%, nan precs) @ (0, 1, 3852)',\n", " ' -0.14685-0.00073 (+0.499%, nan precs) @ (0, 0, 5963)',\n", " '  0.07367+0.00037 (+0.497%, nan precs) @ (0, 0, 5177)',\n", " ' -0.17285-0.00085 (+0.494%, nan precs) @ (0, 1, 5581)',\n", " ' -0.09918+0.00049 (-0.492%, nan precs) @ (0, 1, 2501)',\n", " '  0.19971-0.00098 (-0.489%, nan precs) @ (0, 1, 2769)',\n", " '  0.25122+0.00122 (+0.486%, nan precs) @ (0, 0, 2006)',\n", " ' -0.25122+0.00122 (-0.486%, nan precs) @ (0, 1, 697)',\n", " '  0.12622+0.00061 (+0.484%, nan precs) @ (0, 1, 3578)',\n", " '  0.06317+0.00031 (+0.483%, nan precs) @ (0, 1, 5594)',\n", " ' -0.20264-0.00098 (+0.482%, nan precs) @ (0, 1, 609)',\n", " ' -0.35547+0.00171 (-0.481%, nan precs) @ (0, 0, 1947)',\n", " '  0.08905+0.00043 (+0.480%, nan precs) @ (0, 0, 2968)',\n", " '  0.25464-0.00122 (-0.480%, nan precs) @ (0, 0, 1394)',\n", " ' -0.25464-0.00122 (+0.480%, nan precs) @ (0, 0, 1873)',\n", " ' -0.45850-0.00220 (+0.479%, nan precs) @ (0, 1, 4341)',\n", " '  0.00719-0.00003 (-0.478%, nan precs) @ (0, 0, 1832)',\n", " '  0.04474-0.00021 (-0.478%, nan precs) @ (0, 1, 5202)',\n", " ' -0.25586-0.00122 (+0.477%, nan precs) @ (0, 0, 2715)',\n", " '  0.05762-0.00027 (-0.477%, nan precs) @ (0, 1, 4619)',\n", " ' -0.17957+0.00085 (-0.476%, nan precs) @ (0, 1, 2834)',\n", " '  0.20544+0.00098 (+0.475%, nan precs) @ (0, 0, 5276)',\n", " ' -0.18018+0.00085 (-0.474%, nan precs) @ (0, 1, 947)',\n", " ' -0.10303-0.00049 (+0.474%, nan precs) @ (0, 0, 6083)',\n", " '  0.25806-0.00122 (-0.473%, nan precs) @ (0, 0, 4655)',\n", " ' -0.20667-0.00098 (+0.473%, nan precs) @ (0, 1, 4098)',\n", " '  0.15503-0.00073 (-0.472%, nan precs) @ (0, 0, 5597)',\n", " '  0.09058-0.00043 (-0.472%, nan precs) @ (0, 0, 6131)',\n", " '  0.10388-0.00049 (-0.470%, nan precs) @ (0, 0, 2457)',\n", " ' -0.10400-0.00049 (+0.470%, nan precs) @ (0, 0, 4196)',\n", " ' -0.13013-0.00061 (+0.469%, nan precs) @ (0, 1, 1237)',\n", " ' -0.20850+0.00098 (-0.468%, nan precs) @ (0, 0, 5034)',\n", " '  0.18274-0.00085 (-0.468%, nan precs) @ (0, 0, 1297)',\n", " '  0.41772-0.00195 (-0.468%, nan precs) @ (0, 0, 3054)',\n", " ' -0.05234-0.00024 (+0.467%, nan precs) @ (0, 0, 5303)',\n", " '  0.11780+0.00055 (+0.466%, nan precs) @ (0, 1, 1329)',\n", " '  0.07861-0.00037 (-0.466%, nan precs) @ (0, 0, 1809)',\n", " '  0.26221+0.00122 (+0.465%, nan precs) @ (0, 0, 4128)',\n", " ' -0.07880+0.00037 (-0.465%, nan precs) @ (0, 0, 5213)',\n", " '  0.04602+0.00021 (+0.464%, nan precs) @ (0, 0, 4337)',\n", " ' -0.18420+0.00085 (-0.464%, nan precs) @ (0, 0, 286)',\n", " '  0.13220-0.00061 (-0.462%, nan precs) @ (0, 1, 4581)',\n", " ' -0.31812+0.00146 (-0.460%, nan precs) @ (0, 1, 1018)',\n", " '  0.23877+0.00110 (+0.460%, nan precs) @ (0, 0, 2654)',\n", " ' -0.53076-0.00244 (+0.460%, nan precs) @ (0, 1, 1784)',\n", " '  0.18665-0.00085 (-0.458%, nan precs) @ (0, 1, 5970)',\n", " ' -0.18677+0.00085 (-0.457%, nan precs) @ (0, 0, 2131)',\n", " '  0.10712-0.00049 (-0.456%, nan precs) @ (0, 1, 2511)',\n", " ' -0.16113-0.00073 (+0.455%, nan precs) @ (0, 1, 2783)',\n", " ' -0.13452+0.00061 (-0.454%, nan precs) @ (0, 1, 5866)',\n", " '  0.32349+0.00146 (+0.453%, nan precs) @ (0, 0, 2802)',\n", " ' -0.16223+0.00073 (-0.451%, nan precs) @ (0, 1, 948)',\n", " ' -0.32495-0.00146 (+0.451%, nan precs) @ (0, 0, 5873)',\n", " '  0.12219+0.00055 (+0.449%, nan precs) @ (0, 0, 2049)',\n", " '  0.24609+0.00110 (+0.446%, nan precs) @ (0, 0, 5104)',\n", " '  0.08209+0.00037 (+0.446%, nan precs) @ (0, 1, 4889)',\n", " ' -0.21924+0.00098 (-0.446%, nan precs) @ (0, 0, 191)',\n", " ' -0.21973+0.00098 (-0.444%, nan precs) @ (0, 1, 2809)',\n", " '  0.03436-0.00015 (-0.444%, nan precs) @ (0, 0, 372)',\n", " '  0.38501+0.00171 (+0.444%, nan precs) @ (0, 0, 6112)',\n", " ' -0.38525-0.00171 (+0.444%, nan precs) @ (0, 0, 5740)',\n", " '  0.22021+0.00098 (+0.444%, nan precs) @ (0, 1, 5565)',\n", " '  0.16528-0.00073 (-0.443%, nan precs) @ (0, 1, 3935)',\n", " '  0.24854-0.00110 (-0.442%, nan precs) @ (0, 1, 1334)',\n", " ' -0.19360+0.00085 (-0.441%, nan precs) @ (0, 0, 5697)',\n", " '  0.19385+0.00085 (+0.441%, nan precs) @ (0, 0, 1482)',\n", " '  0.05542-0.00024 (-0.441%, nan precs) @ (0, 1, 3069)',\n", " ' -0.09705+0.00043 (-0.440%, nan precs) @ (0, 1, 5421)',\n", " '  0.16711-0.00073 (-0.438%, nan precs) @ (0, 0, 6027)',\n", " ' -0.33447+0.00146 (-0.438%, nan precs) @ (0, 1, 3733)',\n", " ' -0.08380+0.00037 (-0.437%, nan precs) @ (0, 0, 3938)',\n", " '  0.19568+0.00085 (+0.437%, nan precs) @ (0, 1, 1274)',\n", " ' -0.28027+0.00122 (-0.436%, nan precs) @ (0, 0, 341)',\n", " '  0.14026+0.00061 (+0.435%, nan precs) @ (0, 1, 646)',\n", " ' -0.28149-0.00122 (+0.434%, nan precs) @ (0, 0, 1985)',\n", " '  0.28149-0.00122 (-0.434%, nan precs) @ (0, 0, 6064)',\n", " '  0.07043+0.00031 (+0.433%, nan precs) @ (0, 0, 3205)',\n", " ' -0.19714+0.00085 (-0.433%, nan precs) @ (0, 0, 5851)',\n", " '  0.28271+0.00122 (+0.432%, nan precs) @ (0, 0, 4331)',\n", " ' -0.33936+0.00146 (-0.432%, nan precs) @ (0, 1, 5772)',\n", " ' -0.14148+0.00061 (-0.431%, nan precs) @ (0, 1, 1735)',\n", " ' -0.04953-0.00021 (+0.431%, nan precs) @ (0, 1, 3624)',\n", " '  0.45361-0.00195 (-0.431%, nan precs) @ (0, 1, 3666)',\n", " '  0.19861+0.00085 (+0.430%, nan precs) @ (0, 0, 2279)',\n", " ' -0.17065+0.00073 (-0.429%, nan precs) @ (0, 0, 2817)',\n", " '  0.14246-0.00061 (-0.428%, nan precs) @ (0, 1, 3662)',\n", " ' -0.19971-0.00085 (+0.428%, nan precs) @ (0, 0, 3671)',\n", " ' -0.10010-0.00043 (+0.427%, nan precs) @ (0, 1, 1808)',\n", " '  0.20105-0.00085 (-0.425%, nan precs) @ (0, 0, 1074)',\n", " '  0.08618+0.00037 (+0.425%, nan precs) @ (0, 1, 1378)',\n", " ' -0.17261-0.00073 (+0.424%, nan precs) @ (0, 1, 2836)',\n", " '  0.10083-0.00043 (-0.424%, nan precs) @ (0, 0, 3172)',\n", " ' -0.17285+0.00073 (-0.424%, nan precs) @ (0, 1, 1045)',\n", " '  0.08649-0.00037 (-0.423%, nan precs) @ (0, 0, 2957)',\n", " '  0.10101+0.00043 (+0.423%, nan precs) @ (0, 1, 2535)',\n", " ' -0.29004+0.00122 (-0.421%, nan precs) @ (0, 0, 2203)',\n", " ' -0.05807+0.00024 (-0.420%, nan precs) @ (0, 1, 2032)',\n", " '  0.29077-0.00122 (-0.420%, nan precs) @ (0, 0, 2192)',\n", " '  0.20349-0.00085 (-0.420%, nan precs) @ (0, 0, 2849)',\n", " '  0.14624-0.00061 (-0.417%, nan precs) @ (0, 0, 355)',\n", " ' -0.35181-0.00146 (+0.417%, nan precs) @ (0, 0, 1269)',\n", " ' -0.41016+0.00171 (-0.417%, nan precs) @ (0, 1, 5761)',\n", " '  0.17676+0.00073 (+0.414%, nan precs) @ (0, 1, 2905)',\n", " '  0.35449+0.00146 (+0.413%, nan precs) @ (0, 1, 1824)',\n", " '  0.35522+0.00146 (+0.412%, nan precs) @ (0, 0, 2997)',\n", " '  0.20715-0.00085 (-0.412%, nan precs) @ (0, 1, 818)',\n", " '  0.20740+0.00085 (+0.412%, nan precs) @ (0, 1, 2421)',\n", " '  0.03708-0.00015 (-0.412%, nan precs) @ (0, 1, 6097)',\n", " ' -0.23767+0.00098 (-0.411%, nan precs) @ (0, 0, 4032)',\n", " ' -0.29810-0.00122 (+0.409%, nan precs) @ (0, 1, 579)',\n", " '  0.05972+0.00024 (+0.409%, nan precs) @ (0, 1, 3132)',\n", " '  0.05969-0.00024 (-0.409%, nan precs) @ (0, 1, 5975)',\n", " ' -0.04480-0.00018 (+0.409%, nan precs) @ (0, 1, 3941)',\n", " '  0.29956-0.00122 (-0.407%, nan precs) @ (0, 0, 4334)',\n", " '  0.30005+0.00122 (+0.407%, nan precs) @ (0, 0, 2604)',\n", " ' -0.07507-0.00031 (+0.407%, nan precs) @ (0, 1, 3625)',\n", " '  0.03751+0.00015 (+0.407%, nan precs) @ (0, 1, 5035)',\n", " ' -0.30078+0.00122 (-0.406%, nan precs) @ (0, 1, 4078)',\n", " ' -0.30273+0.00122 (-0.403%, nan precs) @ (0, 0, 2400)',\n", " ' -0.30347+0.00122 (-0.402%, nan precs) @ (0, 0, 1360)',\n", " '  0.18237+0.00073 (+0.402%, nan precs) @ (0, 1, 3636)',\n", " '  0.02667+0.00011 (+0.401%, nan precs) @ (0, 1, 4280)',\n", " '  0.01906-0.00008 (-0.400%, nan precs) @ (0, 0, 408)',\n", " '  0.21350-0.00085 (-0.400%, nan precs) @ (0, 1, 4466)',\n", " ' -0.30542+0.00122 (-0.400%, nan precs) @ (0, 0, 2084)',\n", " '  0.36719-0.00146 (-0.399%, nan precs) @ (0, 0, 5453)',\n", " ' -0.18347-0.00073 (+0.399%, nan precs) @ (0, 1, 5167)',\n", " '  0.42871-0.00171 (-0.399%, nan precs) @ (0, 1, 4598)',\n", " ' -0.02313-0.00009 (+0.396%, nan precs) @ (0, 0, 626)',\n", " '  0.09271-0.00037 (-0.395%, nan precs) @ (0, 1, 4764)',\n", " '  0.15503+0.00061 (+0.394%, nan precs) @ (0, 0, 4744)',\n", " ' -0.24902-0.00098 (+0.392%, nan precs) @ (0, 1, 3707)',\n", " '  0.31201+0.00122 (+0.391%, nan precs) @ (0, 1, 629)',\n", " '  0.37598-0.00146 (-0.390%, nan precs) @ (0, 0, 210)',\n", " ' -0.43848+0.00171 (-0.390%, nan precs) @ (0, 0, 874)',\n", " ' -0.25098-0.00098 (+0.389%, nan precs) @ (0, 0, 3431)',\n", " ' -0.25098-0.00098 (+0.389%, nan precs) @ (0, 1, 1392)',\n", " '  0.25171+0.00098 (+0.388%, nan precs) @ (0, 0, 5406)',\n", " ' -0.12598+0.00049 (-0.388%, nan precs) @ (0, 1, 716)',\n", " ' -0.22070-0.00085 (+0.387%, nan precs) @ (0, 1, 180)',\n", " '  0.25220-0.00098 (-0.387%, nan precs) @ (0, 1, 2016)',\n", " '  0.18933+0.00073 (+0.387%, nan precs) @ (0, 0, 5202)',\n", " ' -0.15796+0.00061 (-0.386%, nan precs) @ (0, 1, 391)',\n", " '  0.50781-0.00195 (-0.385%, nan precs) @ (0, 0, 701)',\n", " '  0.25415-0.00098 (-0.384%, nan precs) @ (0, 1, 6130)',\n", " ' -0.25464-0.00098 (+0.384%, nan precs) @ (0, 0, 3125)',\n", " '  0.06372-0.00024 (-0.383%, nan precs) @ (0, 0, 2463)',\n", " ' -0.11157-0.00043 (+0.383%, nan precs) @ (0, 1, 3359)',\n", " '  0.25537-0.00098 (-0.382%, nan precs) @ (0, 0, 4859)',\n", " '  0.12769+0.00049 (+0.382%, nan precs) @ (0, 1, 1561)',\n", " ' -0.22375+0.00085 (-0.382%, nan precs) @ (0, 0, 4845)',\n", " ' -0.38379+0.00146 (-0.382%, nan precs) @ (0, 1, 3579)',\n", " '  0.16003+0.00061 (+0.381%, nan precs) @ (0, 1, 3906)',\n", " '  0.19214+0.00073 (+0.381%, nan precs) @ (0, 0, 2969)',\n", " '  0.32031-0.00122 (-0.381%, nan precs) @ (0, 1, 1661)',\n", " ' -0.32153+0.00122 (-0.380%, nan precs) @ (0, 1, 3504)',\n", " '  0.38672+0.00146 (+0.379%, nan precs) @ (0, 1, 4464)',\n", " '  0.25854+0.00098 (+0.378%, nan precs) @ (0, 0, 570)',\n", " '  0.22632+0.00085 (+0.378%, nan precs) @ (0, 1, 2909)',\n", " '  0.51758+0.00195 (+0.377%, nan precs) @ (0, 0, 3851)',\n", " '  0.08087+0.00031 (+0.377%, nan precs) @ (0, 0, 4761)',\n", " '  0.16174+0.00061 (+0.377%, nan precs) @ (0, 1, 632)',\n", " '  0.22656+0.00085 (+0.377%, nan precs) @ (0, 1, 5477)',\n", " ' -0.26099+0.00098 (-0.374%, nan precs) @ (0, 1, 5523)',\n", " ' -0.09827-0.00037 (+0.373%, nan precs) @ (0, 1, 2539)',\n", " '  0.26196-0.00098 (-0.373%, nan precs) @ (0, 1, 4074)',\n", " ' -0.32812+0.00122 (-0.372%, nan precs) @ (0, 1, 2967)',\n", " ' -0.23059+0.00085 (-0.371%, nan precs) @ (0, 0, 2449)',\n", " ' -0.16479+0.00061 (-0.370%, nan precs) @ (0, 0, 5577)',\n", " ' -0.19824+0.00073 (-0.369%, nan precs) @ (0, 0, 5865)',\n", " ' -0.16528-0.00061 (+0.369%, nan precs) @ (0, 0, 6102)',\n", " ' -0.08270+0.00031 (-0.369%, nan precs) @ (0, 1, 825)',\n", " ' -0.19958+0.00073 (-0.367%, nan precs) @ (0, 1, 5492)',\n", " '  0.53369+0.00195 (+0.366%, nan precs) @ (0, 0, 3521)',\n", " '  0.11670+0.00043 (+0.366%, nan precs) @ (0, 1, 763)',\n", " ' -0.08350+0.00031 (-0.365%, nan precs) @ (0, 1, 4174)',\n", " '  0.16711-0.00061 (-0.365%, nan precs) @ (0, 0, 716)',\n", " '  0.06689-0.00024 (-0.365%, nan precs) @ (0, 0, 2416)',\n", " ' -0.20068-0.00073 (+0.365%, nan precs) @ (0, 1, 5598)',\n", " '  0.33496-0.00122 (-0.364%, nan precs) @ (0, 0, 3592)',\n", " '  0.05872+0.00021 (+0.364%, nan precs) @ (0, 1, 1502)',\n", " ' -0.40308-0.00146 (+0.363%, nan precs) @ (0, 0, 1188)',\n", " '  0.47388+0.00171 (+0.361%, nan precs) @ (0, 0, 5405)',\n", " ' -0.10156+0.00037 (-0.360%, nan precs) @ (0, 1, 5811)',\n", " ' -0.23743-0.00085 (+0.360%, nan precs) @ (0, 1, 3951)',\n", " ' -0.13599-0.00049 (+0.359%, nan precs) @ (0, 1, 564)',\n", " ' -0.27197-0.00098 (+0.359%, nan precs) @ (0, 1, 4765)',\n", " '  0.27222-0.00098 (-0.359%, nan precs) @ (0, 1, 2651)',\n", " '  0.54541+0.00195 (+0.358%, nan precs) @ (0, 0, 832)',\n", " '  0.10242-0.00037 (-0.358%, nan precs) @ (0, 1, 1147)',\n", " ' -0.34180+0.00122 (-0.357%, nan precs) @ (0, 1, 2855)',\n", " ' -0.34229+0.00122 (-0.357%, nan precs) @ (0, 1, 1178)',\n", " ' -0.27515+0.00098 (-0.355%, nan precs) @ (0, 1, 664)',\n", " '  0.20642+0.00073 (+0.355%, nan precs) @ (0, 1, 3243)',\n", " ' -0.12048-0.00043 (+0.355%, nan precs) @ (0, 1, 1717)',\n", " ' -0.41431-0.00146 (+0.354%, nan precs) @ (0, 1, 482)',\n", " ' -0.41455-0.00146 (+0.353%, nan precs) @ (0, 0, 5552)',\n", " ' -0.17297-0.00061 (+0.353%, nan precs) @ (0, 1, 4006)',\n", " ' -0.06921-0.00024 (+0.353%, nan precs) @ (0, 0, 5414)',\n", " '  0.24255-0.00085 (-0.352%, nan precs) @ (0, 0, 1068)',\n", " '  0.08661-0.00031 (-0.352%, nan precs) @ (0, 1, 1387)',\n", " '  0.10400+0.00037 (+0.352%, nan precs) @ (0, 0, 2965)',\n", " '  0.55566+0.00195 (+0.352%, nan precs) @ (0, 0, 1228)',\n", " '  0.27783-0.00098 (-0.352%, nan precs) @ (0, 1, 934)',\n", " ' -0.20862+0.00073 (-0.351%, nan precs) @ (0, 0, 222)',\n", " '  0.13928+0.00049 (+0.351%, nan precs) @ (0, 1, 2847)',\n", " ' -0.27881-0.00098 (+0.350%, nan precs) @ (0, 1, 1700)',\n", " '  0.27905+0.00098 (+0.350%, nan precs) @ (0, 0, 1238)',\n", " ' -0.55859-0.00195 (+0.350%, nan precs) @ (0, 0, 64)',\n", " ' -0.04370+0.00015 (-0.349%, nan precs) @ (0, 1, 4624)',\n", " '  0.17505-0.00061 (-0.349%, nan precs) @ (0, 0, 2585)',\n", " ' -0.06152-0.00021 (+0.347%, nan precs) @ (0, 1, 2981)',\n", " ' -0.56299+0.00195 (-0.347%, nan precs) @ (0, 0, 3717)',\n", " '  0.56299+0.00195 (+0.347%, nan precs) @ (0, 0, 4208)',\n", " '  0.10559-0.00037 (-0.347%, nan precs) @ (0, 1, 4432)',\n", " '  0.14099+0.00049 (+0.346%, nan precs) @ (0, 1, 3697)',\n", " ' -0.35474+0.00122 (-0.344%, nan precs) @ (0, 0, 3051)',\n", " ' -0.17737+0.00061 (-0.344%, nan precs) @ (0, 1, 587)',\n", " ' -0.14233+0.00049 (-0.343%, nan precs) @ (0, 0, 5987)',\n", " '  0.24927-0.00085 (-0.343%, nan precs) @ (0, 1, 493)',\n", " ' -0.28540+0.00098 (-0.342%, nan precs) @ (0, 0, 4395)',\n", " '  0.50146-0.00171 (-0.341%, nan precs) @ (0, 0, 2384)',\n", " ' -0.21631+0.00073 (-0.339%, nan precs) @ (0, 1, 3295)',\n", " ' -0.36230+0.00122 (-0.337%, nan precs) @ (0, 1, 2203)',\n", " '  0.58252+0.00195 (+0.335%, nan precs) @ (0, 0, 4339)',\n", " ' -0.03644+0.00012 (-0.335%, nan precs) @ (0, 0, 5592)',\n", " '  0.36475+0.00122 (+0.335%, nan precs) @ (0, 0, 5396)',\n", " ' -0.29199+0.00098 (-0.334%, nan precs) @ (0, 0, 1322)',\n", " '  0.29248+0.00098 (+0.334%, nan precs) @ (0, 0, 4382)',\n", " '  0.21936-0.00073 (-0.334%, nan precs) @ (0, 1, 3840)',\n", " ' -0.18286-0.00061 (+0.334%, nan precs) @ (0, 1, 912)',\n", " ' -0.10999-0.00037 (+0.333%, nan precs) @ (0, 1, 384)',\n", " ' -0.11005-0.00037 (+0.333%, nan precs) @ (0, 0, 5411)',\n", " ' -0.29346-0.00098 (+0.333%, nan precs) @ (0, 1, 3610)',\n", " '  0.29419-0.00098 (-0.332%, nan precs) @ (0, 1, 3172)',\n", " ' -0.29443-0.00098 (+0.332%, nan precs) @ (0, 1, 3615)',\n", " ' -0.29565+0.00098 (-0.330%, nan precs) @ (0, 1, 2349)',\n", " ' -0.18494+0.00061 (-0.330%, nan precs) @ (0, 0, 2126)',\n", " ' -0.11096-0.00037 (+0.330%, nan precs) @ (0, 0, 5207)',\n", " ' -0.14807-0.00049 (+0.330%, nan precs) @ (0, 1, 543)',\n", " ' -0.11121-0.00037 (+0.329%, nan precs) @ (0, 0, 5763)',\n", " ' -0.18652-0.00061 (+0.327%, nan precs) @ (0, 0, 1602)',\n", " '  0.22388+0.00073 (+0.327%, nan precs) @ (0, 0, 297)',\n", " '  0.18713-0.00061 (-0.326%, nan precs) @ (0, 1, 320)',\n", " ' -0.09357+0.00031 (-0.326%, nan precs) @ (0, 1, 4642)',\n", " ' -0.37524-0.00122 (+0.325%, nan precs) @ (0, 0, 1354)',\n", " ' -0.30029+0.00098 (-0.325%, nan precs) @ (0, 0, 3956)',\n", " ' -0.30078-0.00098 (+0.325%, nan precs) @ (0, 0, 229)',\n", " '  0.30103+0.00098 (+0.324%, nan precs) @ (0, 1, 5320)',\n", " '  0.37646+0.00122 (+0.324%, nan precs) @ (0, 0, 3766)',\n", " '  0.37817-0.00122 (-0.323%, nan precs) @ (0, 0, 4529)',\n", " '  0.07574+0.00024 (+0.322%, nan precs) @ (0, 0, 5702)',\n", " '  0.37964-0.00122 (-0.322%, nan precs) @ (0, 1, 377)',\n", " '  0.30420+0.00098 (+0.321%, nan precs) @ (0, 0, 2650)',\n", " ' -0.38086-0.00122 (+0.320%, nan precs) @ (0, 1, 1424)',\n", " '  0.30469-0.00098 (-0.320%, nan precs) @ (0, 1, 5704)',\n", " '  0.61035-0.00195 (-0.320%, nan precs) @ (0, 1, 5645)',\n", " '  0.38257-0.00122 (-0.319%, nan precs) @ (0, 0, 4912)',\n", " '  0.38403+0.00122 (+0.318%, nan precs) @ (0, 0, 5249)',\n", " '  0.38574+0.00122 (+0.316%, nan precs) @ (0, 1, 1993)',\n", " ' -0.19336-0.00061 (+0.316%, nan precs) @ (0, 1, 2652)',\n", " ' -0.38696+0.00122 (-0.315%, nan precs) @ (0, 1, 3536)',\n", " ' -0.11621-0.00037 (+0.315%, nan precs) @ (0, 0, 2887)',\n", " ' -0.19385-0.00061 (+0.315%, nan precs) @ (0, 0, 3027)',\n", " ' -0.46509-0.00146 (+0.315%, nan precs) @ (0, 0, 3553)',\n", " '  0.31006-0.00098 (-0.315%, nan precs) @ (0, 0, 4625)',\n", " '  0.23279+0.00073 (+0.315%, nan precs) @ (0, 1, 4508)',\n", " '  0.19434+0.00061 (+0.314%, nan precs) @ (0, 1, 453)',\n", " '  0.15564+0.00049 (+0.314%, nan precs) @ (0, 0, 925)',\n", " '  0.19495+0.00061 (+0.313%, nan precs) @ (0, 0, 1372)',\n", " '  0.39136-0.00122 (-0.312%, nan precs) @ (0, 0, 3687)',\n", " '  0.15662+0.00049 (+0.312%, nan precs) @ (0, 1, 4258)',\n", " '  0.19580+0.00061 (+0.312%, nan precs) @ (0, 0, 376)',\n", " '  0.39160+0.00122 (+0.312%, nan precs) @ (0, 1, 5422)',\n", " ' -0.39185-0.00122 (+0.311%, nan precs) @ (0, 0, 3040)',\n", " ' -0.31348-0.00098 (+0.311%, nan precs) @ (0, 0, 5975)',\n", " '  0.47119-0.00146 (-0.311%, nan precs) @ (0, 0, 5761)',\n", " ' -0.07861-0.00024 (+0.311%, nan precs) @ (0, 1, 1722)',\n", " '  0.39355+0.00122 (+0.310%, nan precs) @ (0, 0, 2106)',\n", " ' -0.23621-0.00073 (+0.310%, nan precs) @ (0, 0, 4925)',\n", " '  0.39404+0.00122 (+0.310%, nan precs) @ (0, 0, 4590)',\n", " ' -0.19702-0.00061 (+0.310%, nan precs) @ (0, 1, 5516)',\n", " '  0.39404-0.00122 (-0.310%, nan precs) @ (0, 1, 5647)',\n", " ' -0.15771+0.00049 (-0.310%, nan precs) @ (0, 0, 804)',\n", " ' -0.23657-0.00073 (+0.310%, nan precs) @ (0, 1, 278)',\n", " ' -0.39551-0.00122 (+0.309%, nan precs) @ (0, 1, 2883)',\n", " '  0.15881-0.00049 (-0.307%, nan precs) @ (0, 1, 2731)',\n", " '  0.04974+0.00015 (+0.307%, nan precs) @ (0, 0, 1452)',\n", " ' -0.39819-0.00122 (+0.307%, nan precs) @ (0, 0, 5287)',\n", " ' -0.23901-0.00073 (+0.307%, nan precs) @ (0, 0, 6022)',\n", " ' -0.11963+0.00037 (-0.306%, nan precs) @ (0, 0, 360)',\n", " ' -0.63867-0.00195 (+0.306%, nan precs) @ (0, 0, 5929)',\n", " '  0.07996+0.00024 (+0.305%, nan precs) @ (0, 1, 4)',\n", " '  0.24011+0.00073 (+0.305%, nan precs) @ (0, 0, 5158)',\n", " ' -0.16016-0.00049 (+0.305%, nan precs) @ (0, 0, 2246)',\n", " '  0.32056-0.00098 (-0.305%, nan precs) @ (0, 0, 2858)',\n", " '  0.32056-0.00098 (-0.305%, nan precs) @ (0, 1, 465)',\n", " '  0.20117-0.00061 (-0.303%, nan precs) @ (0, 1, 6045)',\n", " ' -0.64404-0.00195 (+0.303%, nan precs) @ (0, 0, 1972)',\n", " ' -0.32300+0.00098 (-0.302%, nan precs) @ (0, 0, 224)',\n", " ' -0.40527-0.00122 (+0.301%, nan precs) @ (0, 1, 4678)',\n", " '  0.04062-0.00012 (-0.301%, nan precs) @ (0, 0, 5788)',\n", " '  0.24451-0.00073 (-0.300%, nan precs) @ (0, 0, 2292)',\n", " ' -0.48901+0.00146 (-0.300%, nan precs) @ (0, 0, 2515)',\n", " ' -0.48901+0.00146 (-0.300%, nan precs) @ (0, 0, 5841)',\n", " ' -0.05096-0.00015 (+0.299%, nan precs) @ (0, 1, 3113)',\n", " '  0.24500-0.00073 (-0.299%, nan precs) @ (0, 1, 1989)',\n", " '  0.32690+0.00098 (+0.299%, nan precs) @ (0, 0, 2415)',\n", " ' -0.65381-0.00195 (+0.299%, nan precs) @ (0, 1, 194)',\n", " '  0.32886+0.00098 (+0.297%, nan precs) @ (0, 0, 1201)',\n", " '  0.32935-0.00098 (-0.297%, nan precs) @ (0, 1, 1585)',\n", " '  0.20581+0.00061 (+0.297%, nan precs) @ (0, 1, 4232)',\n", " '  0.16479+0.00049 (+0.296%, nan precs) @ (0, 1, 1907)',\n", " ' -0.16528-0.00049 (+0.295%, nan precs) @ (0, 0, 18)',\n", " '  0.16577-0.00049 (-0.294%, nan precs) @ (0, 0, 17)',\n", " '  0.24927+0.00073 (+0.294%, nan precs) @ (0, 1, 4774)',\n", " ' -0.16626+0.00049 (-0.294%, nan precs) @ (0, 1, 1925)',\n", " ' -0.16638-0.00049 (+0.294%, nan precs) @ (0, 1, 3254)',\n", " ' -0.41675+0.00122 (-0.293%, nan precs) @ (0, 0, 791)',\n", " '  0.33350+0.00098 (+0.293%, nan precs) @ (0, 0, 545)',\n", " ' -0.41699+0.00122 (-0.293%, nan precs) @ (0, 0, 1678)',\n", " '  0.16699+0.00049 (+0.292%, nan precs) @ (0, 0, 4356)',\n", " '  0.33423-0.00098 (-0.292%, nan precs) @ (0, 1, 4017)',\n", " '  0.41797-0.00122 (-0.292%, nan precs) @ (0, 0, 831)',\n", " ' -0.25098-0.00073 (+0.292%, nan precs) @ (0, 1, 3153)',\n", " ' -0.06274-0.00018 (+0.292%, nan precs) @ (0, 1, 5249)',\n", " '  0.33521-0.00098 (-0.291%, nan precs) @ (0, 1, 33)',\n", " '  0.20972+0.00061 (+0.291%, nan precs) @ (0, 1, 3235)',\n", " ' -0.50342+0.00146 (-0.291%, nan precs) @ (0, 1, 3679)',\n", " '  0.50342-0.00146 (-0.291%, nan precs) @ (0, 1, 4465)',\n", " '  0.84033+0.00244 (+0.290%, nan precs) @ (0, 0, 1071)',\n", " '  0.16809+0.00049 (+0.290%, nan precs) @ (0, 1, 1526)',\n", " '  0.21082+0.00061 (+0.290%, nan precs) @ (0, 1, 2972)',\n", " ' -0.42188-0.00122 (+0.289%, nan precs) @ (0, 0, 3169)',\n", " ' -0.25317-0.00073 (+0.289%, nan precs) @ (0, 0, 4324)',\n", " ' -0.84424-0.00244 (+0.289%, nan precs) @ (0, 0, 5818)',\n", " '  0.50732+0.00146 (+0.289%, nan precs) @ (0, 0, 4808)',\n", " '  0.16943+0.00049 (+0.288%, nan precs) @ (0, 0, 851)',\n", " '  0.50928+0.00146 (+0.288%, nan precs) @ (0, 0, 1732)',\n", " '  0.50928-0.00146 (-0.288%, nan precs) @ (0, 0, 4132)',\n", " '  0.67969-0.00195 (-0.287%, nan precs) @ (0, 0, 2072)',\n", " '  0.25488+0.00073 (+0.287%, nan precs) @ (0, 1, 878)',\n", " '  0.17004+0.00049 (+0.287%, nan precs) @ (0, 0, 721)',\n", " '  0.10632-0.00031 (-0.287%, nan precs) @ (0, 0, 5227)',\n", " '  0.21277+0.00061 (+0.287%, nan precs) @ (0, 0, 3742)',\n", " ' -0.21289+0.00061 (-0.287%, nan precs) @ (0, 1, 1333)',\n", " '  0.42627-0.00122 (-0.286%, nan precs) @ (0, 0, 3520)',\n", " ' -0.21362-0.00061 (+0.286%, nan precs) @ (0, 1, 5215)',\n", " ' -0.68506-0.00195 (+0.285%, nan precs) @ (0, 1, 5673)',\n", " ' -0.42847+0.00122 (-0.285%, nan precs) @ (0, 1, 1906)',\n", " '  0.51611-0.00146 (-0.284%, nan precs) @ (0, 1, 354)',\n", " '  0.43066-0.00122 (-0.283%, nan precs) @ (0, 0, 1180)',\n", " ' -0.21594+0.00061 (-0.283%, nan precs) @ (0, 0, 4598)',\n", " ' -0.25903-0.00073 (+0.283%, nan precs) @ (0, 1, 202)',\n", " '  0.25903-0.00073 (-0.283%, nan precs) @ (0, 1, 204)',\n", " ' -0.51807+0.00146 (-0.283%, nan precs) @ (0, 1, 5633)',\n", " ' -0.51904-0.00146 (+0.282%, nan precs) @ (0, 0, 4948)',\n", " '  0.25952+0.00073 (+0.282%, nan precs) @ (0, 1, 3059)',\n", " ' -0.43286-0.00122 (+0.282%, nan precs) @ (0, 0, 2794)',\n", " ' -0.43262-0.00122 (+0.282%, nan precs) @ (0, 1, 701)',\n", " '  0.43262-0.00122 (-0.282%, nan precs) @ (0, 1, 4609)',\n", " '  0.69336+0.00195 (+0.282%, nan precs) @ (0, 1, 5532)',\n", " '  0.69434+0.00195 (+0.281%, nan precs) @ (0, 0, 2033)',\n", " ' -0.43506-0.00122 (+0.281%, nan precs) @ (0, 1, 2124)',\n", " ' -0.26099+0.00073 (-0.281%, nan precs) @ (0, 1, 3862)',\n", " ' -0.13049+0.00037 (-0.281%, nan precs) @ (0, 1, 5820)',\n", " ' -0.26099+0.00073 (-0.281%, nan precs) @ (0, 1, 5991)',\n", " ' -0.06531-0.00018 (+0.280%, nan precs) @ (0, 1, 1350)',\n", " ' -0.26172-0.00073 (+0.280%, nan precs) @ (0, 0, 1700)',\n", " '  0.69873+0.00195 (+0.280%, nan precs) @ (0, 1, 1208)',\n", " '  0.13123+0.00037 (+0.279%, nan precs) @ (0, 0, 5253)',\n", " '  0.06561+0.00018 (+0.279%, nan precs) @ (0, 1, 5260)',\n", " '  0.70117-0.00195 (-0.278%, nan precs) @ (0, 0, 2045)',\n", " '  0.35059+0.00098 (+0.278%, nan precs) @ (0, 1, 74)',\n", " ' -0.26318-0.00073 (+0.278%, nan precs) @ (0, 0, 6070)',\n", " ' -0.21948-0.00061 (+0.278%, nan precs) @ (0, 0, 3636)',\n", " '  0.17566+0.00049 (+0.278%, nan precs) @ (0, 1, 333)',\n", " ' -0.43945-0.00122 (+0.278%, nan precs) @ (0, 1, 3825)',\n", " ' -0.52783-0.00146 (+0.278%, nan precs) @ (0, 0, 620)',\n", " '  0.44019+0.00122 (+0.277%, nan precs) @ (0, 1, 1380)',\n", " '  0.26465-0.00073 (-0.277%, nan precs) @ (0, 0, 2855)',\n", " ' -0.52979-0.00146 (+0.277%, nan precs) @ (0, 0, 5304)',\n", " '  0.13257+0.00037 (+0.276%, nan precs) @ (0, 1, 1113)',\n", " '  0.11060+0.00031 (+0.276%, nan precs) @ (0, 0, 5859)',\n", " ' -0.70752-0.00195 (+0.276%, nan precs) @ (0, 1, 4871)',\n", " ' -0.22156-0.00061 (+0.275%, nan precs) @ (0, 0, 4256)',\n", " '  0.53174+0.00146 (+0.275%, nan precs) @ (0, 0, 5161)',\n", " ' -0.02216-0.00006 (+0.275%, nan precs) @ (0, 1, 3448)',\n", " ' -0.35449+0.00098 (-0.275%, nan precs) @ (0, 1, 4779)',\n", " '  0.26611+0.00073 (+0.275%, nan precs) @ (0, 1, 4498)',\n", " ' -0.53271-0.00146 (+0.275%, nan precs) @ (0, 0, 567)',\n", " '  0.06665-0.00018 (-0.275%, nan precs) @ (0, 0, 823)',\n", " '  0.26685+0.00073 (+0.274%, nan precs) @ (0, 1, 841)',\n", " '  0.11133+0.00031 (+0.274%, nan precs) @ (0, 0, 3952)',\n", " '  0.44580+0.00122 (+0.274%, nan precs) @ (0, 0, 1739)',\n", " '  0.44580-0.00122 (-0.274%, nan precs) @ (0, 0, 4009)',\n", " '  0.53467-0.00146 (-0.274%, nan precs) @ (0, 1, 162)',\n", " ' -0.35669+0.00098 (-0.274%, nan precs) @ (0, 1, 5930)',\n", " '  0.53564-0.00146 (-0.274%, nan precs) @ (0, 0, 5446)',\n", " '  0.17883-0.00049 (-0.273%, nan precs) @ (0, 1, 2594)',\n", " '  1.07520-0.00293 (-0.273%, nan precs) @ (0, 0, 158)',\n", " ' -0.35864-0.00098 (+0.272%, nan precs) @ (0, 0, 1417)',\n", " ' -0.22412-0.00061 (+0.272%, nan precs) @ (0, 1, 1781)',\n", " ' -0.06744-0.00018 (+0.271%, nan precs) @ (0, 0, 5078)',\n", " '  0.27026-0.00073 (-0.271%, nan precs) @ (0, 0, 1730)',\n", " '  0.22522+0.00061 (+0.271%, nan precs) @ (0, 1, 2841)',\n", " '  0.03381+0.00009 (+0.271%, nan precs) @ (0, 1, 452)',\n", " '  0.54102+0.00146 (+0.271%, nan precs) @ (0, 1, 1737)',\n", " '  0.11304+0.00031 (+0.270%, nan precs) @ (0, 0, 2652)',\n", " ' -0.22607-0.00061 (+0.270%, nan precs) @ (0, 0, 5848)',\n", " '  0.54297+0.00146 (+0.270%, nan precs) @ (0, 0, 5562)',\n", " '  0.22693+0.00061 (+0.269%, nan precs) @ (0, 1, 3922)',\n", " ' -0.54492-0.00146 (+0.269%, nan precs) @ (0, 0, 3847)',\n", " '  0.45435+0.00122 (+0.269%, nan precs) @ (0, 0, 5074)',\n", " ' -0.54541+0.00146 (-0.269%, nan precs) @ (0, 0, 4083)',\n", " ' -0.27271+0.00073 (-0.269%, nan precs) @ (0, 1, 4029)',\n", " '  0.36377-0.00098 (-0.268%, nan precs) @ (0, 0, 1795)',\n", " '  0.45581-0.00122 (-0.268%, nan precs) @ (0, 0, 4720)',\n", " ' -0.22803+0.00061 (-0.268%, nan precs) @ (0, 1, 2378)',\n", " '  0.54785+0.00146 (+0.267%, nan precs) @ (0, 0, 6012)',\n", " ' -0.54785+0.00146 (-0.267%, nan precs) @ (0, 1, 3404)',\n", " '  0.27417-0.00073 (-0.267%, nan precs) @ (0, 0, 2851)',\n", " '  0.27417+0.00073 (+0.267%, nan precs) @ (0, 1, 3296)',\n", " ' -0.36694+0.00098 (-0.266%, nan precs) @ (0, 1, 4254)',\n", " ' -0.36743+0.00098 (-0.266%, nan precs) @ (0, 1, 6076)',\n", " '  0.91943+0.00244 (+0.266%, nan precs) @ (0, 1, 2512)',\n", " ' -0.55273+0.00146 (-0.265%, nan precs) @ (0, 0, 1923)',\n", " ' -0.23035-0.00061 (+0.265%, nan precs) @ (0, 1, 2889)',\n", " ' -0.73779-0.00195 (+0.265%, nan precs) @ (0, 0, 2407)',\n", " '  0.27661-0.00073 (-0.265%, nan precs) @ (0, 1, 1733)',\n", " '  0.55371+0.00146 (+0.265%, nan precs) @ (0, 0, 4369)',\n", " '  0.37012+0.00098 (+0.264%, nan precs) @ (0, 0, 5313)',\n", " ' -0.37183-0.00098 (+0.263%, nan precs) @ (0, 1, 5982)',\n", " '  0.55811-0.00146 (-0.262%, nan precs) @ (0, 0, 2345)',\n", " ' -0.37231-0.00098 (+0.262%, nan precs) @ (0, 1, 6139)',\n", " '  0.27954-0.00073 (-0.262%, nan precs) @ (0, 1, 3503)',\n", " '  0.11652-0.00031 (-0.262%, nan precs) @ (0, 0, 2707)',\n", " '  0.27979+0.00073 (+0.262%, nan precs) @ (0, 0, 5368)',\n", " '  0.46680-0.00122 (-0.261%, nan precs) @ (0, 0, 4253)',\n", " ' -0.46680-0.00122 (+0.261%, nan precs) @ (0, 1, 2088)',\n", " '  0.56104-0.00146 (-0.261%, nan precs) @ (0, 0, 509)',\n", " ' -0.74854-0.00195 (+0.261%, nan precs) @ (0, 0, 339)',\n", " ' -0.28076-0.00073 (+0.261%, nan precs) @ (0, 1, 5529)',\n", " '  0.11707-0.00031 (-0.261%, nan precs) @ (0, 1, 1336)',\n", " '  0.28125-0.00073 (-0.260%, nan precs) @ (0, 0, 3866)',\n", " ' -0.56250+0.00146 (-0.260%, nan precs) @ (0, 1, 4726)',\n", " ' -0.37549+0.00098 (-0.260%, nan precs) @ (0, 0, 462)',\n", " ' -0.05865+0.00015 (-0.260%, nan precs) @ (0, 0, 821)',\n", " ' -0.28149-0.00073 (+0.260%, nan precs) @ (0, 0, 5023)',\n", " ' -0.11731-0.00031 (+0.260%, nan precs) @ (0, 1, 4035)',\n", " '  0.28223+0.00073 (+0.260%, nan precs) @ (0, 0, 1967)',\n", " ' -0.18823+0.00049 (-0.259%, nan precs) @ (0, 1, 365)',\n", " '  0.18835-0.00049 (-0.259%, nan precs) @ (0, 0, 5113)',\n", " ' -0.47095-0.00122 (+0.259%, nan precs) @ (0, 1, 2451)',\n", " '  0.56494+0.00146 (+0.259%, nan precs) @ (0, 1, 5539)',\n", " '  0.23572+0.00061 (+0.259%, nan precs) @ (0, 0, 5863)',\n", " '  0.37793-0.00098 (-0.258%, nan precs) @ (0, 1, 967)',\n", " ' -0.28467-0.00073 (+0.257%, nan precs) @ (0, 1, 6040)',\n", " ' -0.37988+0.00098 (-0.257%, nan precs) @ (0, 1, 5346)',\n", " '  0.57031+0.00146 (+0.257%, nan precs) @ (0, 0, 506)',\n", " '  0.38013-0.00098 (-0.257%, nan precs) @ (0, 0, 4439)',\n", " ' -0.57031-0.00146 (+0.257%, nan precs) @ (0, 0, 6031)',\n", " '  0.38062+0.00098 (+0.257%, nan precs) @ (0, 0, 495)',\n", " ' -0.38086-0.00098 (+0.256%, nan precs) @ (0, 0, 3822)',\n", " ' -0.23865-0.00061 (+0.256%, nan precs) @ (0, 0, 2701)',\n", " ' -0.14331-0.00037 (+0.256%, nan precs) @ (0, 1, 444)',\n", " '  0.19104-0.00049 (-0.256%, nan precs) @ (0, 1, 4740)',\n", " ' -0.38232+0.00098 (-0.255%, nan precs) @ (0, 1, 370)',\n", " ' -0.28760+0.00073 (-0.255%, nan precs) @ (0, 0, 561)',\n", " '  0.38354-0.00098 (-0.255%, nan precs) @ (0, 1, 1139)',\n", " ' -0.28784-0.00073 (+0.254%, nan precs) @ (0, 0, 3120)',\n", " ' -0.38452+0.00098 (-0.254%, nan precs) @ (0, 0, 2894)',\n", " ' -0.57666-0.00146 (+0.254%, nan precs) @ (0, 0, 3064)',\n", " ' -0.38452-0.00098 (+0.254%, nan precs) @ (0, 1, 3025)',\n", " '  0.48096+0.00122 (+0.254%, nan precs) @ (0, 0, 2541)',\n", " ' -0.48071+0.00122 (-0.254%, nan precs) @ (0, 0, 4562)',\n", " '  0.57764-0.00146 (-0.254%, nan precs) @ (0, 0, 5135)',\n", " ' -0.28882-0.00073 (+0.254%, nan precs) @ (0, 1, 2659)',\n", " '  0.96338-0.00244 (-0.253%, nan precs) @ (0, 0, 29)',\n", " '  0.48169-0.00122 (-0.253%, nan precs) @ (0, 1, 3865)',\n", " ' -0.38550+0.00098 (-0.253%, nan precs) @ (0, 0, 5109)',\n", " '  0.28931-0.00073 (-0.253%, nan precs) @ (0, 0, 1358)',\n", " ' -0.14465+0.00037 (-0.253%, nan precs) @ (0, 0, 5844)',\n", " '  0.77295+0.00195 (+0.253%, nan precs) @ (0, 0, 4122)',\n", " ' -0.24146-0.00061 (+0.253%, nan precs) @ (0, 1, 4608)',\n", " '  0.58008+0.00146 (+0.253%, nan precs) @ (0, 1, 562)',\n", " '  0.29028-0.00073 (-0.252%, nan precs) @ (0, 1, 438)',\n", " '  0.19360+0.00049 (+0.252%, nan precs) @ (0, 0, 3677)',\n", " '  0.19373+0.00049 (+0.252%, nan precs) @ (0, 0, 3623)',\n", " '  0.24219-0.00061 (-0.252%, nan precs) @ (0, 1, 1606)',\n", " '  0.24268+0.00061 (+0.252%, nan precs) @ (0, 1, 1330)',\n", " ' -0.38843-0.00098 (+0.251%, nan precs) @ (0, 0, 3619)',\n", " '  0.29175-0.00073 (-0.251%, nan precs) @ (0, 1, 2316)',\n", " '  0.24316-0.00061 (-0.251%, nan precs) @ (0, 1, 5392)',\n", " '  0.14600+0.00037 (+0.251%, nan precs) @ (0, 1, 5065)',\n", " '  0.14648-0.00037 (-0.250%, nan precs) @ (0, 0, 4447)',\n", " ' -0.24402-0.00061 (+0.250%, nan precs) @ (0, 1, 2716)',\n", " '  0.14661+0.00037 (+0.250%, nan precs) @ (0, 0, 5160)',\n", " '  0.39111+0.00098 (+0.250%, nan precs) @ (0, 0, 4900)',\n", " ' -0.78223+0.00195 (-0.250%, nan precs) @ (0, 1, 3487)',\n", " ' -0.03674+0.00009 (-0.249%, nan precs) @ (0, 0, 4466)',\n", " ' -0.29419-0.00073 (+0.249%, nan precs) @ (0, 0, 4806)',\n", " '  0.29419+0.00073 (+0.249%, nan precs) @ (0, 0, 5766)',\n", " '  0.39282-0.00098 (-0.249%, nan precs) @ (0, 0, 1490)',\n", " '  0.39307+0.00098 (+0.249%, nan precs) @ (0, 0, 4392)',\n", " ' -0.19678+0.00049 (-0.248%, nan precs) @ (0, 0, 807)',\n", " ' -0.29565+0.00073 (-0.248%, nan precs) @ (0, 0, 202)',\n", " ' -0.14819+0.00037 (-0.247%, nan precs) @ (0, 0, 4359)',\n", " '  0.19763+0.00049 (+0.247%, nan precs) @ (0, 0, 3362)',\n", " '  0.19824-0.00049 (-0.246%, nan precs) @ (0, 1, 2813)',\n", " '  0.24817+0.00061 (+0.246%, nan precs) @ (0, 0, 2874)',\n", " '  0.39771+0.00098 (+0.245%, nan precs) @ (0, 1, 4954)',\n", " '  0.59863+0.00146 (+0.245%, nan precs) @ (0, 1, 1614)',\n", " ' -0.39941-0.00098 (+0.245%, nan precs) @ (0, 1, 6038)',\n", " '  0.39966+0.00098 (+0.244%, nan precs) @ (0, 0, 5423)',\n", " ' -0.30005+0.00073 (-0.244%, nan precs) @ (0, 0, 6045)',\n", " '  0.06256-0.00015 (-0.244%, nan precs) @ (0, 1, 4938)',\n", " '  0.60107+0.00146 (+0.244%, nan precs) @ (0, 0, 4293)',\n", " ' -0.60303+0.00146 (-0.243%, nan precs) @ (0, 0, 3288)',\n", " '  0.20129-0.00049 (-0.243%, nan precs) @ (0, 1, 4402)',\n", " '  0.40283+0.00098 (+0.242%, nan precs) @ (0, 1, 1624)',\n", " ' -0.30225+0.00073 (-0.242%, nan precs) @ (0, 0, 639)',\n", " ' -0.40381+0.00098 (-0.242%, nan precs) @ (0, 0, 3557)',\n", " '  0.30273+0.00073 (+0.242%, nan precs) @ (0, 1, 4575)',\n", " ' -0.40405-0.00098 (+0.242%, nan precs) @ (0, 0, 3080)',\n", " '  0.07574-0.00018 (-0.242%, nan precs) @ (0, 1, 1786)',\n", " '  0.40430+0.00098 (+0.241%, nan precs) @ (0, 0, 2078)',\n", " ' -0.81006+0.00195 (-0.241%, nan precs) @ (0, 0, 6134)',\n", " ' -0.60742+0.00146 (-0.241%, nan precs) @ (0, 1, 2602)',\n", " ' -0.15210+0.00037 (-0.241%, nan precs) @ (0, 1, 6062)',\n", " '  0.40625-0.00098 (-0.240%, nan precs) @ (0, 0, 65)',\n", " ' -0.30469+0.00073 (-0.240%, nan precs) @ (0, 1, 1926)',\n", " '  0.15259-0.00037 (-0.240%, nan precs) @ (0, 0, 1973)',\n", " ' -0.05093+0.00012 (-0.240%, nan precs) @ (0, 0, 205)',\n", " '  0.40723-0.00098 (-0.240%, nan precs) @ (0, 0, 4366)',\n", " '  0.30566+0.00073 (+0.240%, nan precs) @ (0, 0, 3074)',\n", " ' -0.61182+0.00146 (-0.239%, nan precs) @ (0, 0, 2971)',\n", " '  0.30664-0.00073 (-0.239%, nan precs) @ (0, 1, 822)',\n", " '  0.20447-0.00049 (-0.239%, nan precs) @ (0, 1, 1206)',\n", " '  0.40967+0.00098 (+0.238%, nan precs) @ (0, 0, 4810)',\n", " '  0.07678+0.00018 (+0.238%, nan precs) @ (0, 1, 4981)',\n", " ...]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["example_index = torch.arange(y_dbrx.shape[0]).view(-1, 1, 1).expand_as(y_dbrx)\n", "token_index = torch.arange(y_dbrx.shape[1]).view(1, -1, 1).expand_as(y_dbrx)\n", "channel_index = torch.arange(y_dbrx.shape[2]).view(1, 1, -1).expand_as(y_dbrx)\n", "# index = torch.stack([example_index, token_index, channel_index], dim=-1)\n", "example_indices = example_index.reshape(-1).cpu().numpy()\n", "token_indices = token_index.reshape(-1).cpu().numpy()\n", "channel_indices = channel_index.reshape(-1).cpu().numpy()\n", "\n", "abs_diff = (y_dmoe - y_dbrx)\n", "rel_diff = (abs_diff / y_dbrx)\n", "abs_diffs = abs_diff.view(-1).cpu()\n", "rel_diffs = rel_diff.view(-1).cpu()\n", "\n", "ys_dbrx = y_dbrx.view(-1).cpu()\n", "import numpy as np\n", "# prec_units = (torch.nextafter(y_dbrx, torch.tensor(float('inf'), device=y_dbrx.device)) - y_dbrx).view(-1).cpu()\n", "prec_units = torch.full_like(y_dbrx, torch.nan).view(-1).cpu()\n", "precs = abs_diffs / prec_units\n", "\n", "\n", "diffs = [\n", "    ((example_index, token_index, channel_index), y, prec, abs_diff, rel_diff)\n", "    for example_index, token_index, channel_index, abs_diff, rel_diff, y, prec in zip(\n", "        example_indices, token_indices, channel_indices, abs_diffs, rel_diffs, ys_dbrx, precs\n", "    )\n", "]\n", "diffs.sort(key=lambda x: -abs(x[-1]))\n", "[\n", "    f\"{y:9.5f}{abs_diff:+8.5f} ({rel_diff:+.3%}, {torch.round(prec, decimals=0)} precs) @ {index}\"\n", "    for index, y, prec, abs_diff, rel_diff in diffs\n", "    if abs(prec) < float('inf') or np.isnan(prec)\n", "]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Within 0 bit patterns: 0.000%\n", "Within 1 bit patterns: 0.000%\n", "Within 2 bit patterns: 0.000%\n", "Within 5 bit patterns: 0.000%\n", "Within 10 bit patterns: 0.000%\n", "Within 100 bit patterns: 0.000%\n"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Create a histogram plot with normalized values\n", "fp32_precs = precs.to(torch.float32).cpu().numpy().tolist()\n", "# n, bins, patches = plt.hist([prec for prec in fp32_precs if abs(prec) < float('inf') and abs(prec) >= 0], bins=2, density=True)\n", "n, bins, patches = plt.hist(fp32_precs, bins=20, density=False)\n", "print(f\"Within 0 bit patterns: {len([prec for prec in fp32_precs if abs(prec) <= 0]) / len(fp32_precs):.3%}\")\n", "print(f\"Within 1 bit patterns: {len([prec for prec in fp32_precs if abs(prec) <= 1]) / len(fp32_precs):.3%}\")\n", "print(f\"Within 2 bit patterns: {len([prec for prec in fp32_precs if abs(prec) <= 2]) / len(fp32_precs):.3%}\")\n", "print(f\"Within 5 bit patterns: {len([prec for prec in fp32_precs if abs(prec) <= 5]) / len(fp32_precs):.3%}\")\n", "print(f\"Within 10 bit patterns: {len([prec for prec in fp32_precs if abs(prec) <= 10]) / len(fp32_precs):.3%}\")\n", "print(f\"Within 100 bit patterns: {len([prec for prec in fp32_precs if abs(prec) <= 100]) / len(fp32_precs):.3%}\")\n", "\n", "# Set Y-axis to logarithmic scale\n", "# plt.yscale('log')\n", "\n", "# Convert Y-axis values to percentages\n", "# plt.ylabel('Percentage')\n", "# plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.0%}'.format(y)))\n", "\n", "# Add labels and title\n", "plt.xlabel('Precisions')\n", "plt.title('Histogram of errors (unit: float precision)')\n", "\n", "# Display the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor(0., device='cuda:0', dtype=torch.float16, grad_fn=<MaxBackward1>)\n", "tensor(0., device='cuda:0', dtype=torch.float16, grad_fn=<MaxBackward1>)\n"]}, {"data": {"text/plain": ["(<PERSON><PERSON><PERSON><PERSON>([172032, 6144]),\n", " <PERSON><PERSON><PERSON><PERSON>([172032, 6144]),\n", " <PERSON>.<PERSON><PERSON>([172032, 6144]))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["folder = \"/home/<USER>/dbrx/\"\n", "w1 = torch.load(folder + \"w1.pt\")\n", "w1_scaled = torch.load(folder + \"w1_scaled.pt\")\n", "w1_resolved = torch.load(folder + \"w1_resolved.pt\")\n", "print((w1 - w1_scaled).abs().max())\n", "print((w1 - w1_resolved).abs().max())\n", "v1 = torch.load(folder + \"v1.pt\")\n", "w2 = torch.load(folder + \"w2.pt\")\n", "w1.shape, v1.shape, w2.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["28.0"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["172032 / 6144"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True tensor(7159, device='cuda:0') tensor(5129, device='cuda:0')\n", "True tensor(0, device='cuda:0') tensor(32, device='cuda:0')\n", "True tensor(0, device='cuda:0') tensor(2, device='cuda:0')\n", "True tensor(0, device='cuda:0') tensor(2, device='cuda:0')\n"]}], "source": ["print(y_dbrx.shape == y_dmoe.shape, (y_dbrx != y_dmoe).sum(), (y_dbrx == y_dmoe).sum())\n", "print(dbrx_scores.shape == dmoe_scores.shape, (dbrx_scores != dmoe_scores).sum(), (dbrx_scores == dmoe_scores).sum())\n", "print(\n", "    dbrx_expert_weights.shape == dmoe_expert_weights.shape,\n", "    (dbrx_expert_weights != dmoe_expert_weights).sum(),\n", "    (dbrx_expert_weights == dmoe_expert_weights).sum(),\n", ")\n", "print(\n", "    dbrx_top_experts.shape == dmoe_top_experts.shape,\n", "    (dbrx_top_experts != dmoe_top_experts).sum(),\n", "    (dbrx_top_experts == dmoe_top_experts).sum(),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[9.5139e-03, 6.8398e-03, 1.2279e-05, 8.7500e-04, 2.0256e-03, 9.2850e-03,\n", "          3.3951e-03, 5.7831e-02, 7.0752e-01, 1.5059e-03, 1.4782e-05, 1.0474e-01,\n", "          6.7749e-03, 9.5444e-03, 7.6355e-02, 3.8452e-03],\n", "         [4.5419e-05, 2.2531e-04, 2.5749e-03, 4.5562e-04, 7.2050e-04, 2.1124e-04,\n", "          5.2643e-04, 4.1723e-06, 5.9605e-08, 7.9036e-05, 3.4828e-03, 8.6164e-04,\n", "          4.5815e-03, 1.3266e-03, 9.8340e-01, 1.3857e-03]], device='cuda:0',\n", "        dtype=torch.float16),\n", " tensor([[1.],\n", "         [1.]], device='cuda:0', dtype=torch.float16),\n", " tensor([[0],\n", "         [0]], device='cuda:0'))"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["dbrx_scores, dbrx_expert_weights, dbrx_top_experts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor(0.0039, device='cuda:0', dtype=torch.float16)\n", "tensor(0.0020, device='cuda:0', dtype=torch.float16)\n"]}], "source": ["for example_index in range(y_dbrx.shape[0]):\n", "    for token_index in range(y_dbrx.shape[1]):\n", "        index = example_index, token_index\n", "        print((y_dbrx[index] - y_dmoe[index]).abs().max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([ 0.0000,  0.0003,  0.0000,  ..., -0.0005,  0.0010,  0.0000],\n", "       device='cuda:0', dtype=torch.float16)\n", "tensor([ 0.7041, -2.1133, -0.6021,  ..., -0.1503,  2.1406, -0.3699],\n", "       device='cuda:0', dtype=torch.float16)\n", "tensor([-0.7041,  2.1133,  0.6011,  ...,  0.1499, -2.1406,  0.3701],\n", "       device='cuda:0', dtype=torch.float16)\n", "tensor([ 0.0000,  0.0000, -0.0010,  ...,  0.0001,  0.0000,  0.0002],\n", "       device='cuda:0', dtype=torch.float16)\n"]}], "source": ["print(y_dbrx[0, 0] - y_dmoe[0, 0])\n", "print(y_dbrx[0, 0] - y_dmoe[0, 1])\n", "print(y_dbrx[0, 1] - y_dmoe[0, 0])\n", "print(y_dbrx[0, 1] - y_dmoe[0, 1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare backward"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DBRX state dict from disk\n", "Initialized DBRX layer\n", "Loaded DBRX state dict into layer\n", "    DBRX expert 0 out: tensor([[-0.3496,  1.0938,  0.2734, -0.9844, -1.0859,  0.2812,  0.5703,  0.7188,\n", "          1.6016,  0.4883],\n", "        [-2.5938,  2.4219,  1.2031,  0.2285, -0.2500, -1.3359,  0.1650,  1.3281,\n", "         -1.3750,  0.9688]], device='cuda:0', dtype=torch.bfloat16,\n", "       grad_fn=<SliceBackward0>)\n", "DBRX\n", "torch.<PERSON><PERSON>([1, 2, 6144]) torch.<PERSON><PERSON>([2, 16]) torch.<PERSON><PERSON>([2, 1]) torch.<PERSON><PERSON>([2, 1])\n", "torch.bfloat16 torch.bfloat16 torch.bfloat16 torch.int64\n", "Loaded dMoE state dict into layer\n", "dMoE\n", "torch.<PERSON><PERSON>([1, 2, 6144]) torch.<PERSON><PERSON>([2, 16]) torch.<PERSON><PERSON>([2, 1]) torch.<PERSON><PERSON>([2, 1])\n", "torch.bfloat16 torch.bfloat16 torch.bfloat16 torch.int64\n", "Diff\n", "tensor(0., device='cuda:0', dtype=torch.bfloat16, grad_fn=<SubBackward0>)\n", "tensor([[0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        ...,\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.],\n", "        [0., 0., 0.,  ..., 0., 0., 0.]], dtype=torch.bfloat16)\n"]}], "source": ["sd = load_sd()\n", "x = get_input()\n", "ffn = load_dbrx(config, sd)\n", "y_dbrx, dbrx_scores, dbrx_expert_weights, dbrx_top_experts = run_dbrx(\n", "    x, ffn, with_grad=True\n", ")\n", "loss_dbrx = l2_weght_decay_loss(y_dbrx)\n", "loss_dbrx.backward()\n", "w1_grad_dbrx = ffn.experts.mlp.w1.grad.cpu()  # type: ignore\n", "del ffn\n", "\n", "moe = load_dmoe(config, sd)\n", "y_dmoe, dmoe_scores, dmoe_expert_weights, dmoe_top_experts = run_dmoe(\n", "    x, moe, with_grad=True\n", ")\n", "loss_dmoe = l2_weght_decay_loss(y_dmoe)\n", "loss_dmoe.backward()\n", "w1_grad_dmoe = moe.experts.mlp.w1.grad.cpu()  # type: ignore\n", "\n", "del moe\n", "print(\"Diff\")\n", "print(loss_dbrx - loss_dmoe)\n", "print((w1_grad_dbrx - w1_grad_dmoe).view(-1, y_dbrx.shape[-1])[:, :10])"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["(torch.<PERSON><PERSON>([172032, 6144]), torch.<PERSON><PERSON>([172032, 6144]))"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["w1_grad_dbrx.shape, w1_grad_dmoe.shape"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["(torch.bfloat16, torch.bfloat16)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["w1_grad_dbrx.dtype, w1_grad_dmoe.dtype"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Indexing\n", "Computing diffs\n"]}, {"ename": "TypeError", "evalue": "Got unsupported ScalarType BFloat16", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[28], line 16\u001b[0m\n\u001b[1;32m     13\u001b[0m w1s_dbrx \u001b[38;5;241m=\u001b[39m w1_grad_dbrx\u001b[38;5;241m.\u001b[39mview(\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m)\u001b[38;5;241m.\u001b[39mcpu()\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m     15\u001b[0m prec_units \u001b[38;5;241m=\u001b[39m (np\u001b[38;5;241m.\u001b[39mnextafter(\n\u001b[0;32m---> 16\u001b[0m     \u001b[43mw1s_dbrx\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mnumpy\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m,\n\u001b[1;32m     17\u001b[0m     np\u001b[38;5;241m.\u001b[39marray(\u001b[38;5;28mfloat\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124minf\u001b[39m\u001b[38;5;124m'\u001b[39m), dtype\u001b[38;5;241m=\u001b[39mw1s_dbrx\u001b[38;5;241m.\u001b[39mnumpy()\u001b[38;5;241m.\u001b[39mdtype),\n\u001b[1;32m     18\u001b[0m ) \u001b[38;5;241m-\u001b[39m w1s_dbrx\u001b[38;5;241m.\u001b[39mnumpy())\n\u001b[1;32m     19\u001b[0m \u001b[38;5;66;03m# prec_units = torch.full_like(w1s_dbrx, torch.nan).view(-1).cpu()\u001b[39;00m\n\u001b[1;32m     20\u001b[0m precs \u001b[38;5;241m=\u001b[39m abs_diffs \u001b[38;5;241m/\u001b[39m prec_units\n", "\u001b[0;31mTypeError\u001b[0m: Got unsupported ScalarType BFloat16"]}], "source": ["print(\"Indexing\")\n", "row_index = torch.arange(w1_grad_dbrx.shape[0]).view(-1, 1).expand_as(w1_grad_dbrx)\n", "column_index = torch.arange(w1_grad_dbrx.shape[1]).view(1, -1).expand_as(w1_grad_dbrx)\n", "row_indices = row_index.reshape(-1).cpu().numpy()\n", "column_indices = column_index.reshape(-1).cpu().numpy()\n", "\n", "print(\"Computing diffs\")\n", "abs_diff = (w1_grad_dmoe - w1_grad_dbrx)\n", "rel_diff = (abs_diff / w1_grad_dbrx)\n", "abs_diffs = abs_diff.view(-1).cpu()\n", "rel_diffs = rel_diff.view(-1).cpu()\n", "\n", "w1s_dbrx = w1_grad_dbrx.view(-1).cpu()\n", "import numpy as np\n", "prec_units = (np.nextafter(\n", "    w1s_dbrx.numpy(),\n", "    np.array(float('inf'), dtype=w1s_dbrx.numpy().dtype),\n", ") - w1s_dbrx.numpy())\n", "# prec_units = torch.full_like(w1s_dbrx, torch.nan).view(-1).cpu()\n", "precs = abs_diffs / prec_units\n", "\n", "print(\"Sampling diffs\")\n", "diff_count = 10_000\n", "row_indices = row_indices[:diff_count]\n", "column_indices = column_indices[:diff_count]\n", "abs_diffs = abs_diffs[:diff_count]\n", "rel_diffs = rel_diffs[:diff_count]\n", "w1s_dbrx = w1s_dbrx[:diff_count]\n", "precs = precs[:diff_count]\n", "\n", "print(\"Compiling diffs\")\n", "diffs = [\n", "    ((row_index, column_index), w1, prec, abs_diff, rel_diff)\n", "    for row_index, column_index, abs_diff, rel_diff, w1, prec in zip(\n", "        row_indices, column_indices, abs_diffs, rel_diffs, w1s_dbrx, precs\n", "    )\n", "]\n", "print(\"Sorting diffs\")\n", "diffs.sort(key=lambda x: -abs(x[-1]))\n", "[\n", "    f\"{y:9.5f}{abs_diff:+8.5f} ({rel_diff:+.3%}, {torch.round(prec, decimals=0)} precs) @ {index}\"\n", "    for index, y, prec, abs_diff, rel_diff in diffs\n", "    if abs(prec) < float('inf') or np.isnan(prec) and abs(y) > 0.01\n", "]"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sorting diffs\n"]}, {"data": {"text/plain": ["['  0.01590-0.00002 (-0.096%, -1.0 precs) @ (1, 2386)',\n", " ' -0.00782-0.00001 (+0.098%, -1.0 precs) @ (1, 2503)',\n", " '  0.00783+0.00001 (+0.097%, 1.0 precs) @ (1, 3745)',\n", " ' -0.00809-0.00001 (+0.094%, -1.0 precs) @ (1, 727)',\n", " '  0.00811-0.00001 (-0.094%, -1.0 precs) @ (1, 702)',\n", " '  0.00814+0.00001 (+0.094%, 1.0 precs) @ (1, 5)',\n", " '  0.00861+0.00001 (+0.089%, 1.0 precs) @ (1, 1911)',\n", " '  0.00865-0.00001 (-0.088%, -1.0 precs) @ (1, 2893)',\n", " '  0.00876+0.00001 (+0.087%, 1.0 precs) @ (1, 1266)',\n", " '  0.00881-0.00001 (-0.087%, -1.0 precs) @ (1, 1320)',\n", " ' -0.00889-0.00001 (+0.086%, -1.0 precs) @ (1, 1893)',\n", " ' -0.00890+0.00001 (-0.086%, 1.0 precs) @ (1, 958)',\n", " '  0.00899-0.00001 (-0.085%, -1.0 precs) @ (1, 2434)',\n", " '  0.00903+0.00001 (+0.084%, 1.0 precs) @ (1, 2079)',\n", " '  0.00936+0.00001 (+0.081%, 1.0 precs) @ (1, 1213)',\n", " '  0.00969+0.00001 (+0.079%, 1.0 precs) @ (1, 3833)',\n", " ' -0.00999-0.00001 (+0.076%, -1.0 precs) @ (1, 1420)',\n", " '  0.01041-0.00001 (-0.073%, -1.0 precs) @ (1, 2064)',\n", " ' -0.01057-0.00001 (+0.072%, -1.0 precs) @ (1, 3123)',\n", " ' -0.01060+0.00001 (-0.072%, 1.0 precs) @ (1, 790)',\n", " ' -0.01069-0.00001 (+0.071%, -1.0 precs) @ (1, 3178)',\n", " ' -0.01070-0.00001 (+0.071%, -1.0 precs) @ (1, 1091)',\n", " '  0.00393+0.00000 (+0.097%, 1.0 precs) @ (1, 2240)',\n", " '  0.00399-0.00000 (-0.096%, -1.0 precs) @ (1, 2552)',\n", " ' -0.00399+0.00000 (-0.096%, 1.0 precs) @ (1, 2622)',\n", " '  0.00400+0.00000 (+0.095%, 1.0 precs) @ (1, 3174)',\n", " ' -0.00400-0.00000 (+0.095%, -1.0 precs) @ (1, 3379)',\n", " '  0.00400+0.00000 (+0.095%, 1.0 precs) @ (1, 2861)',\n", " ' -0.00402-0.00000 (+0.095%, -1.0 precs) @ (1, 3620)',\n", " '  0.00404-0.00000 (-0.094%, -1.0 precs) @ (1, 3347)',\n", " '  0.00406+0.00000 (+0.094%, 1.0 precs) @ (1, 3315)',\n", " '  0.00407+0.00000 (+0.094%, 1.0 precs) @ (1, 477)',\n", " '  0.00409+0.00000 (+0.093%, 1.0 precs) @ (1, 381)',\n", " '  0.00410+0.00000 (+0.093%, 1.0 precs) @ (1, 509)',\n", " ' -0.00413-0.00000 (+0.092%, -1.0 precs) @ (1, 1814)',\n", " '  0.00416+0.00000 (+0.092%, 1.0 precs) @ (1, 1682)',\n", " ' -0.00417+0.00000 (-0.091%, 1.0 precs) @ (1, 2310)',\n", " ' -0.00421+0.00000 (-0.091%, 1.0 precs) @ (1, 341)',\n", " ' -0.00426-0.00000 (+0.090%, -1.0 precs) @ (1, 1049)',\n", " ' -0.00426+0.00000 (-0.089%, 1.0 precs) @ (1, 3087)',\n", " ' -0.00427-0.00000 (+0.089%, -1.0 precs) @ (1, 3060)',\n", " '  0.00431+0.00000 (+0.089%, 1.0 precs) @ (1, 330)',\n", " '  0.00431+0.00000 (+0.088%, 1.0 precs) @ (1, 671)',\n", " ' -0.00439-0.00000 (+0.087%, -1.0 precs) @ (1, 2260)',\n", " '  0.00444+0.00000 (+0.086%, 1.0 precs) @ (1, 3454)',\n", " ' -0.00461+0.00000 (-0.083%, 1.0 precs) @ (1, 1899)',\n", " ' -0.00465-0.00000 (+0.082%, -1.0 precs) @ (1, 677)',\n", " '  0.00465+0.00000 (+0.082%, 1.0 precs) @ (1, 296)',\n", " '  0.00468+0.00000 (+0.081%, 1.0 precs) @ (1, 421)',\n", " '  0.00471+0.00000 (+0.081%, 1.0 precs) @ (1, 1609)',\n", " '  0.00473+0.00000 (+0.081%, 1.0 precs) @ (1, 206)',\n", " '  0.00473-0.00000 (-0.081%, -1.0 precs) @ (1, 1608)',\n", " '  0.00477-0.00000 (-0.080%, -1.0 precs) @ (1, 516)',\n", " ' -0.00478+0.00000 (-0.080%, 1.0 precs) @ (1, 704)',\n", " ' -0.00490+0.00000 (-0.078%, 1.0 precs) @ (1, 2847)',\n", " '  0.00492-0.00000 (-0.078%, -1.0 precs) @ (1, 2381)',\n", " ' -0.00492+0.00000 (-0.078%, 1.0 precs) @ (1, 700)',\n", " ' -0.00492+0.00000 (-0.077%, 1.0 precs) @ (1, 425)',\n", " '  0.00493-0.00000 (-0.077%, -1.0 precs) @ (1, 328)',\n", " '  0.00493-0.00000 (-0.077%, -1.0 precs) @ (1, 673)',\n", " '  0.00494-0.00000 (-0.077%, -1.0 precs) @ (1, 957)',\n", " '  0.00498+0.00000 (+0.077%, 1.0 precs) @ (1, 1217)',\n", " '  0.00498+0.00000 (+0.077%, 1.0 precs) @ (1, 3091)',\n", " ' -0.00506-0.00000 (+0.075%, -1.0 precs) @ (1, 2965)',\n", " '  0.00507-0.00000 (-0.075%, -1.0 precs) @ (1, 1834)',\n", " '  0.00512+0.00000 (+0.075%, 1.0 precs) @ (1, 108)',\n", " '  0.00512+0.00000 (+0.075%, 1.0 precs) @ (1, 2392)',\n", " '  0.00518-0.00000 (-0.074%, -1.0 precs) @ (1, 281)',\n", " ' -0.00518-0.00000 (+0.074%, -1.0 precs) @ (1, 3664)',\n", " '  0.00520-0.00000 (-0.073%, -1.0 precs) @ (1, 34)',\n", " '  0.00525+0.00000 (+0.073%, 1.0 precs) @ (1, 88)',\n", " ' -0.00526-0.00000 (+0.073%, -1.0 precs) @ (1, 3486)',\n", " ' -0.00527+0.00000 (-0.072%, 1.0 precs) @ (1, 1535)',\n", " '  0.00528+0.00000 (+0.072%, 1.0 precs) @ (1, 952)',\n", " ' -0.00531-0.00000 (+0.072%, -1.0 precs) @ (1, 1923)',\n", " '  0.00532+0.00000 (+0.072%, 1.0 precs) @ (1, 2239)',\n", " '  0.00533+0.00000 (+0.072%, 1.0 precs) @ (1, 2390)',\n", " '  0.00534-0.00000 (-0.071%, -1.0 precs) @ (1, 2409)',\n", " ' -0.00537-0.00000 (+0.071%, -1.0 precs) @ (1, 1368)',\n", " '  0.00541+0.00000 (+0.071%, 1.0 precs) @ (1, 1554)',\n", " '  0.00545+0.00000 (+0.070%, 1.0 precs) @ (1, 1698)',\n", " ' -0.00546+0.00000 (-0.070%, 1.0 precs) @ (1, 1685)',\n", " '  0.00548-0.00000 (-0.070%, -1.0 precs) @ (1, 3400)',\n", " ' -0.00551-0.00000 (+0.069%, -1.0 precs) @ (1, 1393)',\n", " ' -0.00551-0.00000 (+0.069%, -1.0 precs) @ (1, 3553)',\n", " '  0.00555-0.00000 (-0.069%, -1.0 precs) @ (1, 1092)',\n", " ' -0.00556+0.00000 (-0.069%, 1.0 precs) @ (1, 7)',\n", " ' -0.00560-0.00000 (+0.068%, -1.0 precs) @ (1, 3670)',\n", " '  0.00566-0.00000 (-0.067%, -1.0 precs) @ (1, 1188)',\n", " ' -0.00572-0.00000 (+0.067%, -1.0 precs) @ (1, 831)',\n", " '  0.00574+0.00000 (+0.066%, 1.0 precs) @ (1, 3029)',\n", " '  0.00577+0.00000 (+0.066%, 1.0 precs) @ (1, 1945)',\n", " ' -0.00577-0.00000 (+0.066%, -1.0 precs) @ (1, 1962)',\n", " ' -0.00581-0.00000 (+0.066%, -1.0 precs) @ (1, 261)',\n", " '  0.00582+0.00000 (+0.066%, 1.0 precs) @ (1, 608)',\n", " ' -0.00584+0.00000 (-0.065%, 1.0 precs) @ (1, 738)',\n", " '  0.00585+0.00000 (+0.065%, 1.0 precs) @ (1, 3579)',\n", " ' -0.00589-0.00000 (+0.065%, -1.0 precs) @ (1, 1684)',\n", " ' -0.00592+0.00000 (-0.064%, 1.0 precs) @ (1, 734)',\n", " ' -0.00596-0.00000 (+0.064%, -1.0 precs) @ (1, 1507)',\n", " ' -0.00596-0.00000 (+0.064%, -1.0 precs) @ (1, 992)',\n", " ' -0.00598-0.00000 (+0.064%, -1.0 precs) @ (1, 1579)',\n", " '  0.00601+0.00000 (+0.063%, 1.0 precs) @ (1, 3771)',\n", " ' -0.00603-0.00000 (+0.063%, -1.0 precs) @ (1, 3144)',\n", " '  0.00604-0.00000 (-0.063%, -1.0 precs) @ (1, 146)',\n", " ' -0.00605-0.00000 (+0.063%, -1.0 precs) @ (1, 2322)',\n", " '  0.00606-0.00000 (-0.063%, -1.0 precs) @ (1, 547)',\n", " '  0.00616+0.00000 (+0.062%, 1.0 precs) @ (1, 1937)',\n", " ' -0.00617-0.00000 (+0.062%, -1.0 precs) @ (1, 163)',\n", " '  0.00619+0.00000 (+0.062%, 1.0 precs) @ (1, 880)',\n", " ' -0.00626-0.00000 (+0.061%, -1.0 precs) @ (1, 3017)',\n", " ' -0.00636-0.00000 (+0.060%, -1.0 precs) @ (1, 2099)',\n", " '  0.00642-0.00000 (-0.059%, -1.0 precs) @ (1, 3300)',\n", " '  0.00646-0.00000 (-0.059%, -1.0 precs) @ (1, 314)',\n", " ' -0.00648-0.00000 (+0.059%, -1.0 precs) @ (1, 2767)',\n", " '  0.00648+0.00000 (+0.059%, 1.0 precs) @ (1, 1024)',\n", " ' -0.00649+0.00000 (-0.059%, 1.0 precs) @ (1, 3560)',\n", " ' -0.00657-0.00000 (+0.058%, -1.0 precs) @ (1, 3396)',\n", " '  0.00658+0.00000 (+0.058%, 1.0 precs) @ (1, 1058)',\n", " ' -0.00661-0.00000 (+0.058%, -1.0 precs) @ (1, 1526)',\n", " ' -0.00662-0.00000 (+0.058%, -1.0 precs) @ (1, 1558)',\n", " ' -0.00667-0.00000 (+0.057%, -1.0 precs) @ (1, 1940)',\n", " ' -0.00671-0.00000 (+0.057%, -1.0 precs) @ (1, 2194)',\n", " ' -0.00675-0.00000 (+0.057%, -1.0 precs) @ (1, 53)',\n", " '  0.00674+0.00000 (+0.057%, 1.0 precs) @ (1, 822)',\n", " ' -0.00675+0.00000 (-0.057%, 1.0 precs) @ (1, 372)',\n", " '  0.00682+0.00000 (+0.056%, 1.0 precs) @ (1, 3237)',\n", " '  0.00685+0.00000 (+0.056%, 1.0 precs) @ (1, 2837)',\n", " '  0.00687-0.00000 (-0.056%, -1.0 precs) @ (1, 3855)',\n", " ' -0.00690-0.00000 (+0.055%, -1.0 precs) @ (1, 58)',\n", " ' -0.00692-0.00000 (+0.055%, -1.0 precs) @ (1, 2487)',\n", " ' -0.00696-0.00000 (+0.055%, -1.0 precs) @ (1, 3033)',\n", " ' -0.00705-0.00000 (+0.054%, -1.0 precs) @ (1, 1412)',\n", " '  0.00708+0.00000 (+0.054%, 1.0 precs) @ (1, 1081)',\n", " ' -0.00711+0.00000 (-0.054%, 1.0 precs) @ (1, 674)',\n", " '  0.00720+0.00000 (+0.053%, 1.0 precs) @ (1, 3496)',\n", " '  0.00727+0.00000 (+0.052%, 1.0 precs) @ (1, 755)',\n", " '  0.00729-0.00000 (-0.052%, -1.0 precs) @ (1, 711)',\n", " '  0.00740-0.00000 (-0.052%, -1.0 precs) @ (1, 378)',\n", " ' -0.00742+0.00000 (-0.051%, 1.0 precs) @ (1, 3369)',\n", " '  0.00747+0.00000 (+0.051%, 1.0 precs) @ (1, 904)',\n", " '  0.00747-0.00000 (-0.051%, -1.0 precs) @ (1, 956)',\n", " '  0.00759+0.00000 (+0.050%, 1.0 precs) @ (1, 2348)',\n", " '  0.00760-0.00000 (-0.050%, -1.0 precs) @ (1, 1625)',\n", " ' -0.00772-0.00000 (+0.049%, -1.0 precs) @ (1, 1599)',\n", " '  0.00776+0.00000 (+0.049%, 1.0 precs) @ (1, 2251)',\n", " ' -0.00102+0.00000 (-0.187%, 2.0 precs) @ (1, 3592)',\n", " ' -0.00114-0.00000 (+0.168%, -2.0 precs) @ (1, 3380)',\n", " ' -0.00127+0.00000 (-0.150%, 2.0 precs) @ (1, 1495)',\n", " '  0.00152-0.00000 (-0.125%, -2.0 precs) @ (1, 1767)',\n", " ' -0.00160-0.00000 (+0.119%, -2.0 precs) @ (1, 3132)',\n", " ' -0.00187-0.00000 (+0.102%, -2.0 precs) @ (1, 1041)',\n", " '  0.00187+0.00000 (+0.102%, 2.0 precs) @ (1, 757)',\n", " ' -0.00190+0.00000 (-0.101%, 2.0 precs) @ (1, 751)',\n", " ' -0.00196-0.00000 (+0.097%, -1.0 precs) @ (1, 970)',\n", " ' -0.00197+0.00000 (-0.097%, 1.0 precs) @ (1, 1634)',\n", " ' -0.00199+0.00000 (-0.096%, 1.0 precs) @ (1, 68)',\n", " ' -0.00199+0.00000 (-0.096%, 1.0 precs) @ (1, 2461)',\n", " '  0.00200-0.00000 (-0.096%, -1.0 precs) @ (1, 3158)',\n", " '  0.00201-0.00000 (-0.095%, -1.0 precs) @ (1, 2428)',\n", " '  0.00201-0.00000 (-0.095%, -1.0 precs) @ (1, 2945)',\n", " '  0.00201-0.00000 (-0.095%, -1.0 precs) @ (1, 2066)',\n", " ' -0.00202-0.00000 (+0.095%, -1.0 precs) @ (1, 1630)',\n", " '  0.00203-0.00000 (-0.094%, -1.0 precs) @ (1, 436)',\n", " '  0.00204-0.00000 (-0.094%, -1.0 precs) @ (1, 3485)',\n", " '  0.00205+0.00000 (+0.093%, 1.0 precs) @ (1, 1014)',\n", " ' -0.00206+0.00000 (-0.092%, 1.0 precs) @ (1, 3357)',\n", " '  0.00207+0.00000 (+0.092%, 1.0 precs) @ (1, 3611)',\n", " ' -0.00212-0.00000 (+0.090%, -1.0 precs) @ (1, 1647)',\n", " '  0.00213+0.00000 (+0.090%, 1.0 precs) @ (1, 1965)',\n", " ' -0.00215+0.00000 (-0.089%, 1.0 precs) @ (1, 2166)',\n", " ' -0.00217+0.00000 (-0.088%, 1.0 precs) @ (1, 2856)',\n", " ' -0.00227+0.00000 (-0.084%, 1.0 precs) @ (1, 3448)',\n", " ' -0.00227-0.00000 (+0.084%, -1.0 precs) @ (1, 3815)',\n", " '  0.00227-0.00000 (-0.084%, -1.0 precs) @ (1, 2065)',\n", " ' -0.00228+0.00000 (-0.084%, 1.0 precs) @ (1, 1544)',\n", " '  0.00228+0.00000 (+0.084%, 1.0 precs) @ (1, 932)',\n", " '  0.00229+0.00000 (+0.083%, 1.0 precs) @ (1, 1868)',\n", " '  0.00229+0.00000 (+0.083%, 1.0 precs) @ (1, 2884)',\n", " '  0.00231+0.00000 (+0.082%, 1.0 precs) @ (1, 3105)',\n", " ' -0.00234+0.00000 (-0.081%, 1.0 precs) @ (1, 382)',\n", " '  0.00236+0.00000 (+0.081%, 1.0 precs) @ (1, 824)',\n", " ' -0.00239-0.00000 (+0.080%, -1.0 precs) @ (1, 1610)',\n", " '  0.00239-0.00000 (-0.080%, -1.0 precs) @ (1, 2729)',\n", " ' -0.00239+0.00000 (-0.080%, 1.0 precs) @ (1, 2586)',\n", " '  0.00241-0.00000 (-0.079%, -1.0 precs) @ (1, 3668)',\n", " ' -0.00241+0.00000 (-0.079%, 1.0 precs) @ (1, 3090)',\n", " ' -0.00243-0.00000 (+0.079%, -1.0 precs) @ (1, 2804)',\n", " '  0.00243-0.00000 (-0.079%, -1.0 precs) @ (1, 3071)',\n", " ' -0.00244+0.00000 (-0.078%, 1.0 precs) @ (1, 774)',\n", " ' -0.00245+0.00000 (-0.078%, 1.0 precs) @ (1, 991)',\n", " ' -0.00245+0.00000 (-0.078%, 1.0 precs) @ (1, 2737)',\n", " ' -0.00245+0.00000 (-0.078%, 1.0 precs) @ (1, 1799)',\n", " '  0.00245-0.00000 (-0.078%, -1.0 precs) @ (1, 1405)',\n", " '  0.00246+0.00000 (+0.077%, 1.0 precs) @ (1, 2447)',\n", " '  0.00249-0.00000 (-0.077%, -1.0 precs) @ (1, 347)',\n", " '  0.00249+0.00000 (+0.077%, 1.0 precs) @ (1, 3002)',\n", " '  0.00252-0.00000 (-0.076%, -1.0 precs) @ (1, 1705)',\n", " '  0.00252+0.00000 (+0.076%, 1.0 precs) @ (1, 2494)',\n", " '  0.00253-0.00000 (-0.076%, -1.0 precs) @ (1, 1633)',\n", " ' -0.00253+0.00000 (-0.075%, 1.0 precs) @ (1, 2110)',\n", " ' -0.00253+0.00000 (-0.075%, 1.0 precs) @ (1, 623)',\n", " ' -0.00255+0.00000 (-0.075%, 1.0 precs) @ (1, 3159)',\n", " ' -0.00255-0.00000 (+0.075%, -1.0 precs) @ (1, 584)',\n", " '  0.00257+0.00000 (+0.074%, 1.0 precs) @ (1, 901)',\n", " ' -0.00260+0.00000 (-0.073%, 1.0 precs) @ (1, 1161)',\n", " '  0.00261-0.00000 (-0.073%, -1.0 precs) @ (1, 2084)',\n", " '  0.00262+0.00000 (+0.073%, 1.0 precs) @ (1, 568)',\n", " ' -0.00263-0.00000 (+0.073%, -1.0 precs) @ (1, 3841)',\n", " '  0.00263+0.00000 (+0.072%, 1.0 precs) @ (1, 2574)',\n", " ' -0.00264+0.00000 (-0.072%, 1.0 precs) @ (1, 3753)',\n", " ' -0.00264+0.00000 (-0.072%, 1.0 precs) @ (1, 2738)',\n", " '  0.00265+0.00000 (+0.072%, 1.0 precs) @ (1, 1653)',\n", " '  0.00265+0.00000 (+0.072%, 1.0 precs) @ (1, 1754)',\n", " ' -0.00266+0.00000 (-0.072%, 1.0 precs) @ (1, 2153)',\n", " '  0.00270-0.00000 (-0.071%, -1.0 precs) @ (1, 1490)',\n", " ' -0.00271-0.00000 (+0.070%, -1.0 precs) @ (1, 2662)',\n", " '  0.00274-0.00000 (-0.070%, -1.0 precs) @ (1, 3692)',\n", " '  0.00277+0.00000 (+0.069%, 1.0 precs) @ (1, 1728)',\n", " ' -0.00278+0.00000 (-0.069%, 1.0 precs) @ (1, 1)',\n", " '  0.00278-0.00000 (-0.069%, -1.0 precs) @ (1, 1187)',\n", " ' -0.00278+0.00000 (-0.069%, 1.0 precs) @ (1, 142)',\n", " ' -0.00279-0.00000 (+0.068%, -1.0 precs) @ (1, 706)',\n", " ' -0.00279-0.00000 (+0.068%, -1.0 precs) @ (1, 1468)',\n", " '  0.00279+0.00000 (+0.068%, 1.0 precs) @ (1, 1746)',\n", " ' -0.00279-0.00000 (+0.068%, -1.0 precs) @ (1, 2430)',\n", " '  0.00280-0.00000 (-0.068%, -1.0 precs) @ (1, 144)',\n", " '  0.00281+0.00000 (+0.068%, 1.0 precs) @ (1, 1312)',\n", " '  0.00281-0.00000 (-0.068%, -1.0 precs) @ (1, 3065)',\n", " ' -0.00282+0.00000 (-0.068%, 1.0 precs) @ (1, 3376)',\n", " ' -0.00284-0.00000 (+0.067%, -1.0 precs) @ (1, 3733)',\n", " '  0.00285-0.00000 (-0.067%, -1.0 precs) @ (1, 2583)',\n", " '  0.00285-0.00000 (-0.067%, -1.0 precs) @ (1, 2375)',\n", " '  0.00286-0.00000 (-0.067%, -1.0 precs) @ (1, 741)',\n", " ' -0.00287-0.00000 (+0.067%, -1.0 precs) @ (1, 80)',\n", " '  0.00287+0.00000 (+0.066%, 1.0 precs) @ (1, 2596)',\n", " '  0.00290-0.00000 (-0.066%, -1.0 precs) @ (1, 2606)',\n", " '  0.00289-0.00000 (-0.066%, -1.0 precs) @ (1, 3728)',\n", " ' -0.00290+0.00000 (-0.066%, 1.0 precs) @ (1, 2775)',\n", " '  0.00291+0.00000 (+0.066%, 1.0 precs) @ (1, 2002)',\n", " '  0.00292+0.00000 (+0.065%, 1.0 precs) @ (1, 3778)',\n", " '  0.00293+0.00000 (+0.065%, 1.0 precs) @ (1, 2225)',\n", " ' -0.00296-0.00000 (+0.064%, -1.0 precs) @ (1, 120)',\n", " ' -0.00297+0.00000 (-0.064%, 1.0 precs) @ (1, 1617)',\n", " '  0.00298+0.00000 (+0.064%, 1.0 precs) @ (1, 993)',\n", " ' -0.00298-0.00000 (+0.064%, -1.0 precs) @ (1, 1891)',\n", " '  0.00299-0.00000 (-0.064%, -1.0 precs) @ (1, 1751)',\n", " '  0.00299-0.00000 (-0.064%, -1.0 precs) @ (1, 1710)',\n", " '  0.00299+0.00000 (+0.064%, 1.0 precs) @ (1, 603)',\n", " '  0.00300-0.00000 (-0.064%, -1.0 precs) @ (1, 2639)',\n", " '  0.00300+0.00000 (+0.064%, 1.0 precs) @ (1, 3095)',\n", " ' -0.00302-0.00000 (+0.063%, -1.0 precs) @ (1, 3211)',\n", " ' -0.00305-0.00000 (+0.063%, -1.0 precs) @ (1, 1602)',\n", " ' -0.00310+0.00000 (-0.062%, 1.0 precs) @ (1, 2131)',\n", " ' -0.00311-0.00000 (+0.061%, -1.0 precs) @ (1, 962)',\n", " '  0.00311-0.00000 (-0.061%, -1.0 precs) @ (1, 2912)',\n", " '  0.00312+0.00000 (+0.061%, 1.0 precs) @ (1, 1116)',\n", " ' -0.00314-0.00000 (+0.061%, -1.0 precs) @ (1, 3742)',\n", " '  0.00314+0.00000 (+0.061%, 1.0 precs) @ (1, 3834)',\n", " '  0.00315+0.00000 (+0.061%, 1.0 precs) @ (1, 1240)',\n", " ' -0.00316+0.00000 (-0.060%, 1.0 precs) @ (1, 1626)',\n", " ' -0.00317-0.00000 (+0.060%, -1.0 precs) @ (1, 1037)',\n", " '  0.00318-0.00000 (-0.060%, -1.0 precs) @ (1, 3554)',\n", " ' -0.00318-0.00000 (+0.060%, -1.0 precs) @ (1, 242)',\n", " '  0.00318-0.00000 (-0.060%, -1.0 precs) @ (1, 157)',\n", " ' -0.00319+0.00000 (-0.060%, 1.0 precs) @ (1, 2978)',\n", " ' -0.00320+0.00000 (-0.060%, 1.0 precs) @ (1, 1801)',\n", " '  0.00322+0.00000 (+0.059%, 1.0 precs) @ (1, 404)',\n", " '  0.00323-0.00000 (-0.059%, -1.0 precs) @ (1, 3738)',\n", " '  0.00324-0.00000 (-0.059%, -1.0 precs) @ (1, 1278)',\n", " ' -0.00326+0.00000 (-0.058%, 1.0 precs) @ (1, 3716)',\n", " '  0.00327+0.00000 (+0.058%, 1.0 precs) @ (1, 512)',\n", " '  0.00329+0.00000 (+0.058%, 1.0 precs) @ (1, 787)',\n", " ' -0.00331-0.00000 (+0.058%, -1.0 precs) @ (1, 2292)',\n", " '  0.00331+0.00000 (+0.058%, 1.0 precs) @ (1, 2900)',\n", " ' -0.00334-0.00000 (+0.057%, -1.0 precs) @ (1, 2192)',\n", " '  0.00335+0.00000 (+0.057%, 1.0 precs) @ (1, 2891)',\n", " '  0.00335+0.00000 (+0.057%, 1.0 precs) @ (1, 3089)',\n", " '  0.00337-0.00000 (-0.057%, -1.0 precs) @ (1, 1798)',\n", " '  0.00338-0.00000 (-0.057%, -1.0 precs) @ (1, 3572)',\n", " ' -0.00338-0.00000 (+0.056%, -1.0 precs) @ (1, 676)',\n", " '  0.00339-0.00000 (-0.056%, -1.0 precs) @ (1, 2519)',\n", " ' -0.00339-0.00000 (+0.056%, -1.0 precs) @ (1, 1523)',\n", " '  0.00340-0.00000 (-0.056%, -1.0 precs) @ (1, 2380)',\n", " ' -0.00342+0.00000 (-0.056%, 1.0 precs) @ (1, 2798)',\n", " ' -0.00345-0.00000 (+0.055%, -1.0 precs) @ (1, 2972)',\n", " '  0.00347+0.00000 (+0.055%, 1.0 precs) @ (1, 1621)',\n", " ' -0.00349-0.00000 (+0.055%, -1.0 precs) @ (1, 2137)',\n", " ' -0.00349+0.00000 (-0.055%, 1.0 precs) @ (1, 3204)',\n", " ' -0.00351-0.00000 (+0.054%, -1.0 precs) @ (1, 3284)',\n", " ' -0.00351-0.00000 (+0.054%, -1.0 precs) @ (1, 2673)',\n", " ' -0.00354-0.00000 (+0.054%, -1.0 precs) @ (1, 1086)',\n", " '  0.00355-0.00000 (-0.054%, -1.0 precs) @ (1, 2691)',\n", " ' -0.00356-0.00000 (+0.054%, -1.0 precs) @ (1, 2007)',\n", " ' -0.00357-0.00000 (+0.053%, -1.0 precs) @ (1, 534)',\n", " '  0.00357+0.00000 (+0.053%, 1.0 precs) @ (1, 591)',\n", " '  0.00358-0.00000 (-0.053%, -1.0 precs) @ (1, 2846)',\n", " '  0.00359-0.00000 (-0.053%, -1.0 precs) @ (1, 2208)',\n", " ' -0.00359-0.00000 (+0.053%, -1.0 precs) @ (1, 3317)',\n", " '  0.00360-0.00000 (-0.053%, -1.0 precs) @ (1, 1527)',\n", " ' -0.00360-0.00000 (+0.053%, -1.0 precs) @ (1, 1029)',\n", " '  0.00360-0.00000 (-0.053%, -1.0 precs) @ (1, 2964)',\n", " ' -0.00361-0.00000 (+0.053%, -1.0 precs) @ (1, 767)',\n", " ' -0.00362-0.00000 (+0.053%, -1.0 precs) @ (1, 1071)',\n", " '  0.00362+0.00000 (+0.053%, 1.0 precs) @ (1, 2853)',\n", " ' -0.00363-0.00000 (+0.053%, -1.0 precs) @ (1, 690)',\n", " '  0.00363+0.00000 (+0.052%, 1.0 precs) @ (1, 2908)',\n", " ' -0.00368-0.00000 (+0.052%, -1.0 precs) @ (1, 327)',\n", " ' -0.00368-0.00000 (+0.052%, -1.0 precs) @ (1, 2329)',\n", " '  0.00370-0.00000 (-0.051%, -1.0 precs) @ (1, 3030)',\n", " '  0.00371-0.00000 (-0.051%, -1.0 precs) @ (1, 2595)',\n", " ' -0.00371-0.00000 (+0.051%, -1.0 precs) @ (1, 2947)',\n", " ' -0.00371+0.00000 (-0.051%, 1.0 precs) @ (1, 761)',\n", " '  0.00372+0.00000 (+0.051%, 1.0 precs) @ (1, 1849)',\n", " '  0.00373-0.00000 (-0.051%, -1.0 precs) @ (1, 2827)',\n", " '  0.00378+0.00000 (+0.050%, 1.0 precs) @ (1, 927)',\n", " ' -0.00383-0.00000 (+0.050%, -1.0 precs) @ (1, 2914)',\n", " ' -0.00384+0.00000 (-0.050%, 1.0 precs) @ (1, 241)',\n", " ' -0.00385-0.00000 (+0.050%, -1.0 precs) @ (1, 1676)',\n", " ' -0.00387-0.00000 (+0.049%, -1.0 precs) @ (1, 2235)',\n", " '  0.00387+0.00000 (+0.049%, 1.0 precs) @ (1, 2937)',\n", " ' -0.00390-0.00000 (+0.049%, -1.0 precs) @ (1, 486)',\n", " ' -0.00045+0.00000 (-0.320%, 6.0 precs) @ (1, 2424)',\n", " '  0.00053-0.00000 (-0.268%, -3.0 precs) @ (1, 2247)',\n", " '  0.00071-0.00000 (-0.201%, -3.0 precs) @ (1, 3656)',\n", " ' -0.00085+0.00000 (-0.167%, 3.0 precs) @ (1, 3709)',\n", " '  0.00032-0.00000 (-0.375%, -5.0 precs) @ (1, 1662)',\n", " ' -0.00046-0.00000 (+0.260%, -5.0 precs) @ (1, 2463)',\n", " '  0.00003-0.00000 (-3.430%, -19.0 precs) @ (1, 1170)',\n", " '  0.00013-0.00000 (-0.833%, -9.0 precs) @ (1, 2565)',\n", " '  0.00009+0.00000 (+1.075%, 16.0 precs) @ (1, 2123)',\n", " '  0.00019-0.00000 (-0.497%, -8.0 precs) @ (1, 3417)',\n", " '  0.00025+0.00000 (+0.387%, 4.0 precs) @ (1, 1882)',\n", " ' -0.00025-0.00000 (+0.374%, -4.0 precs) @ (1, 3338)',\n", " ' -0.00026-0.00000 (+0.371%, -4.0 precs) @ (1, 821)',\n", " '  0.00031-0.00000 (-0.311%, -4.0 precs) @ (1, 1077)',\n", " ' -0.00031+0.00000 (-0.306%, 4.0 precs) @ (1, 3598)',\n", " '  0.00038+0.00000 (+0.251%, 4.0 precs) @ (1, 1604)',\n", " ' -0.00043-0.00000 (+0.221%, -4.0 precs) @ (1, 3217)',\n", " '  0.00047+0.00000 (+0.205%, 4.0 precs) @ (1, 3693)',\n", " '  0.00048-0.00000 (-0.198%, -4.0 precs) @ (1, 1774)',\n", " '  0.00050+0.00000 (+0.190%, 2.0 precs) @ (1, 1406)',\n", " ' -0.00052-0.00000 (+0.185%, -2.0 precs) @ (0, 1522)',\n", " ' -0.00053-0.00000 (+0.181%, -2.0 precs) @ (1, 41)',\n", " '  0.00056+0.00000 (+0.171%, 2.0 precs) @ (0, 3252)',\n", " ' -0.00057-0.00000 (+0.169%, -2.0 precs) @ (0, 3711)',\n", " '  0.00057+0.00000 (+0.168%, 2.0 precs) @ (0, 172)',\n", " '  0.00057+0.00000 (+0.167%, 2.0 precs) @ (0, 2980)',\n", " '  0.00058+0.00000 (+0.164%, 2.0 precs) @ (0, 3077)',\n", " ' -0.00058-0.00000 (+0.164%, -2.0 precs) @ (0, 1205)',\n", " '  0.00059+0.00000 (+0.162%, 2.0 precs) @ (0, 3387)',\n", " '  0.00060-0.00000 (-0.159%, -2.0 precs) @ (1, 953)',\n", " '  0.00060+0.00000 (+0.159%, 2.0 precs) @ (0, 3461)',\n", " '  0.00060+0.00000 (+0.158%, 2.0 precs) @ (0, 4236)',\n", " ' -0.00060+0.00000 (-0.158%, 2.0 precs) @ (1, 1142)',\n", " '  0.00061+0.00000 (+0.157%, 2.0 precs) @ (1, 1569)',\n", " ' -0.00061-0.00000 (+0.157%, -2.0 precs) @ (0, 4488)',\n", " '  0.00061+0.00000 (+0.156%, 2.0 precs) @ (0, 5617)',\n", " ' -0.00062-0.00000 (+0.153%, -2.0 precs) @ (0, 1283)',\n", " ' -0.00063+0.00000 (-0.151%, 2.0 precs) @ (1, 3277)',\n", " ' -0.00064-0.00000 (+0.149%, -2.0 precs) @ (0, 4710)',\n", " ' -0.00065-0.00000 (+0.147%, -2.0 precs) @ (0, 4764)',\n", " '  0.00066+0.00000 (+0.145%, 2.0 precs) @ (0, 1764)',\n", " '  0.00067+0.00000 (+0.143%, 2.0 precs) @ (0, 2836)',\n", " '  0.00067+0.00000 (+0.142%, 2.0 precs) @ (0, 3079)',\n", " ' -0.00067-0.00000 (+0.142%, -2.0 precs) @ (1, 2319)',\n", " ' -0.00067+0.00000 (-0.141%, 2.0 precs) @ (1, 2808)',\n", " '  0.00068+0.00000 (+0.141%, 2.0 precs) @ (0, 4516)',\n", " '  0.00068+0.00000 (+0.140%, 2.0 precs) @ (1, 549)',\n", " ' -0.00069+0.00000 (-0.138%, 2.0 precs) @ (1, 2679)',\n", " ' -0.00070-0.00000 (+0.137%, -2.0 precs) @ (0, 2020)',\n", " ' -0.00070-0.00000 (+0.136%, -2.0 precs) @ (0, 5805)',\n", " '  0.00070+0.00000 (+0.136%, 2.0 precs) @ (0, 909)',\n", " '  0.00070+0.00000 (+0.136%, 2.0 precs) @ (0, 3397)',\n", " '  0.00071-0.00000 (-0.134%, -2.0 precs) @ (1, 1957)',\n", " ' -0.00072-0.00000 (+0.133%, -2.0 precs) @ (0, 1315)',\n", " ' -0.00072-0.00000 (+0.132%, -2.0 precs) @ (0, 946)',\n", " '  0.00072+0.00000 (+0.132%, 2.0 precs) @ (1, 1331)',\n", " '  0.00072+0.00000 (+0.132%, 2.0 precs) @ (0, 5300)',\n", " ' -0.00073-0.00000 (+0.130%, -2.0 precs) @ (0, 3122)',\n", " ' -0.00074-0.00000 (+0.129%, -2.0 precs) @ (0, 4601)',\n", " '  0.00074+0.00000 (+0.129%, 2.0 precs) @ (0, 4298)',\n", " '  0.00074+0.00000 (+0.129%, 2.0 precs) @ (0, 4785)',\n", " '  0.00074-0.00000 (-0.129%, -2.0 precs) @ (1, 1124)',\n", " '  0.00075+0.00000 (+0.128%, 2.0 precs) @ (0, 345)',\n", " '  0.00075-0.00000 (-0.127%, -2.0 precs) @ (1, 1291)',\n", " '  0.00075+0.00000 (+0.126%, 2.0 precs) @ (0, 2228)',\n", " '  0.00076-0.00000 (-0.126%, -2.0 precs) @ (1, 1586)',\n", " ' -0.00076+0.00000 (-0.126%, 2.0 precs) @ (1, 3235)',\n", " '  0.00076+0.00000 (+0.125%, 2.0 precs) @ (1, 2227)',\n", " '  0.00077+0.00000 (+0.124%, 2.0 precs) @ (0, 3722)',\n", " '  0.00078+0.00000 (+0.122%, 2.0 precs) @ (0, 3574)',\n", " ' -0.00079-0.00000 (+0.121%, -2.0 precs) @ (0, 950)',\n", " '  0.00079-0.00000 (-0.120%, -2.0 precs) @ (1, 3682)',\n", " ' -0.00080-0.00000 (+0.119%, -2.0 precs) @ (1, 1073)',\n", " '  0.00081+0.00000 (+0.117%, 2.0 precs) @ (0, 3730)',\n", " ' -0.00081-0.00000 (+0.117%, -2.0 precs) @ (0, 2531)',\n", " '  0.00082+0.00000 (+0.117%, 2.0 precs) @ (0, 2560)',\n", " ' -0.00083-0.00000 (+0.115%, -2.0 precs) @ (0, 5621)',\n", " ' -0.00084-0.00000 (+0.113%, -2.0 precs) @ (1, 883)',\n", " ' -0.00084+0.00000 (-0.113%, 2.0 precs) @ (1, 889)',\n", " ' -0.00084-0.00000 (+0.113%, -2.0 precs) @ (0, 3464)',\n", " ' -0.00085+0.00000 (-0.113%, 2.0 precs) @ (1, 2015)',\n", " ' -0.00085-0.00000 (+0.112%, -2.0 precs) @ (1, 2769)',\n", " ' -0.00085-0.00000 (+0.112%, -2.0 precs) @ (0, 1713)',\n", " ' -0.00085+0.00000 (-0.112%, 2.0 precs) @ (1, 3405)',\n", " ' -0.00086-0.00000 (+0.111%, -2.0 precs) @ (0, 5987)',\n", " '  0.00086+0.00000 (+0.111%, 2.0 precs) @ (0, 2791)',\n", " ' -0.00086-0.00000 (+0.111%, -2.0 precs) @ (1, 684)',\n", " ' -0.00086-0.00000 (+0.110%, -2.0 precs) @ (0, 4215)',\n", " ' -0.00086-0.00000 (+0.110%, -2.0 precs) @ (0, 5884)',\n", " '  0.00087-0.00000 (-0.109%, -2.0 precs) @ (1, 2508)',\n", " ' -0.00088-0.00000 (+0.108%, -2.0 precs) @ (0, 4053)',\n", " ' -0.00089-0.00000 (+0.107%, -2.0 precs) @ (1, 1042)',\n", " '  0.00090+0.00000 (+0.106%, 2.0 precs) @ (1, 3330)',\n", " ' -0.00090-0.00000 (+0.105%, -2.0 precs) @ (0, 1126)',\n", " ' -0.00091+0.00000 (-0.105%, 2.0 precs) @ (1, 656)',\n", " ' -0.00093-0.00000 (+0.102%, -2.0 precs) @ (0, 4676)',\n", " '  0.00094+0.00000 (+0.102%, 2.0 precs) @ (0, 4646)',\n", " ' -0.00095+0.00000 (-0.101%, 2.0 precs) @ (1, 658)',\n", " '  0.00096+0.00000 (+0.100%, 2.0 precs) @ (1, 1964)',\n", " '  0.00098+0.00000 (+0.098%, 1.0 precs) @ (1, 2543)',\n", " ' -0.00098-0.00000 (+0.097%, -1.0 precs) @ (1, 1288)',\n", " '  0.00098+0.00000 (+0.097%, 1.0 precs) @ (1, 929)',\n", " '  0.00099-0.00000 (-0.097%, -1.0 precs) @ (1, 856)',\n", " ' -0.00099+0.00000 (-0.096%, 1.0 precs) @ (1, 2073)',\n", " '  0.00099-0.00000 (-0.096%, -1.0 precs) @ (1, 664)',\n", " ' -0.00099-0.00000 (+0.096%, -1.0 precs) @ (1, 1667)',\n", " ' -0.00099+0.00000 (-0.096%, 1.0 precs) @ (1, 1939)',\n", " ' -0.00100+0.00000 (-0.096%, 1.0 precs) @ (1, 3644)',\n", " '  0.00100-0.00000 (-0.096%, -1.0 precs) @ (1, 654)',\n", " ' -0.00101+0.00000 (-0.094%, 1.0 precs) @ (1, 481)',\n", " '  0.00102+0.00000 (+0.094%, 1.0 precs) @ (1, 2554)',\n", " '  0.00102-0.00000 (-0.094%, -1.0 precs) @ (1, 1585)',\n", " ' -0.00102+0.00000 (-0.093%, 1.0 precs) @ (1, 156)',\n", " ' -0.00102-0.00000 (+0.093%, -1.0 precs) @ (0, 3189)',\n", " ' -0.00103+0.00000 (-0.092%, 1.0 precs) @ (1, 3788)',\n", " ' -0.00104-0.00000 (+0.092%, -1.0 precs) @ (1, 849)',\n", " ' -0.00104+0.00000 (-0.091%, 1.0 precs) @ (1, 896)',\n", " '  0.00105-0.00000 (-0.091%, -1.0 precs) @ (1, 663)',\n", " '  0.00105+0.00000 (+0.091%, 1.0 precs) @ (1, 713)',\n", " '  0.00105+0.00000 (+0.091%, 1.0 precs) @ (1, 772)',\n", " ' -0.00105-0.00000 (+0.091%, -1.0 precs) @ (1, 1986)',\n", " '  0.00105+0.00000 (+0.090%, 1.0 precs) @ (1, 781)',\n", " ' -0.00106-0.00000 (+0.090%, -1.0 precs) @ (1, 3401)',\n", " '  0.00106-0.00000 (-0.090%, -1.0 precs) @ (1, 1681)',\n", " ' -0.00107-0.00000 (+0.089%, -1.0 precs) @ (1, 924)',\n", " '  0.00107-0.00000 (-0.089%, -1.0 precs) @ (1, 1210)',\n", " ' -0.00107+0.00000 (-0.089%, 1.0 precs) @ (1, 1820)',\n", " ' -0.00107+0.00000 (-0.089%, 1.0 precs) @ (1, 1006)',\n", " '  0.00107+0.00000 (+0.089%, 1.0 precs) @ (1, 2747)',\n", " '  0.00107+0.00000 (+0.089%, 1.0 precs) @ (1, 153)',\n", " '  0.00108+0.00000 (+0.088%, 1.0 precs) @ (1, 926)',\n", " '  0.00108+0.00000 (+0.088%, 1.0 precs) @ (1, 569)',\n", " '  0.00108+0.00000 (+0.088%, 1.0 precs) @ (1, 3395)',\n", " ' -0.00109+0.00000 (-0.088%, 1.0 precs) @ (1, 2545)',\n", " '  0.00109+0.00000 (+0.088%, 1.0 precs) @ (1, 3576)',\n", " '  0.00109+0.00000 (+0.087%, 1.0 precs) @ (1, 828)',\n", " '  0.00110+0.00000 (+0.087%, 1.0 precs) @ (1, 2254)',\n", " ' -0.00110+0.00000 (-0.086%, 1.0 precs) @ (1, 1688)',\n", " '  0.00111+0.00000 (+0.086%, 1.0 precs) @ (1, 3165)',\n", " '  0.00111+0.00000 (+0.086%, 1.0 precs) @ (1, 2716)',\n", " '  0.00111+0.00000 (+0.086%, 1.0 precs) @ (1, 1290)',\n", " '  0.00111+0.00000 (+0.086%, 1.0 precs) @ (1, 667)',\n", " '  0.00111-0.00000 (-0.086%, -1.0 precs) @ (1, 3148)',\n", " ' -0.00112-0.00000 (+0.085%, -1.0 precs) @ (1, 3747)',\n", " ' -0.00112-0.00000 (+0.085%, -1.0 precs) @ (1, 649)',\n", " '  0.00113+0.00000 (+0.084%, 1.0 precs) @ (1, 3729)',\n", " ' -0.00114+0.00000 (-0.084%, 1.0 precs) @ (1, 925)',\n", " ' -0.00114-0.00000 (+0.084%, -1.0 precs) @ (1, 258)',\n", " ' -0.00114-0.00000 (+0.084%, -1.0 precs) @ (1, 2668)',\n", " ' -0.00114+0.00000 (-0.084%, 1.0 precs) @ (1, 978)',\n", " '  0.00115+0.00000 (+0.083%, 1.0 precs) @ (1, 1497)',\n", " ' -0.00116-0.00000 (+0.082%, -1.0 precs) @ (1, 1440)',\n", " '  0.00116+0.00000 (+0.082%, 1.0 precs) @ (1, 1248)',\n", " '  0.00117+0.00000 (+0.081%, 1.0 precs) @ (1, 3849)',\n", " '  0.00118-0.00000 (-0.081%, -1.0 precs) @ (1, 3772)',\n", " ' -0.00119+0.00000 (-0.080%, 1.0 precs) @ (1, 825)',\n", " ' -0.00119+0.00000 (-0.080%, 1.0 precs) @ (1, 1072)',\n", " ' -0.00119+0.00000 (-0.080%, 1.0 precs) @ (1, 552)',\n", " '  0.00120-0.00000 (-0.080%, -1.0 precs) @ (1, 483)',\n", " '  0.00120+0.00000 (+0.080%, 1.0 precs) @ (1, 3377)',\n", " '  0.00120-0.00000 (-0.079%, -1.0 precs) @ (1, 2368)',\n", " '  0.00121+0.00000 (+0.079%, 1.0 precs) @ (1, 895)',\n", " ' -0.00121-0.00000 (+0.079%, -1.0 precs) @ (1, 1576)',\n", " ' -0.00121+0.00000 (-0.079%, 1.0 precs) @ (1, 3804)',\n", " ' -0.00121+0.00000 (-0.078%, 1.0 precs) @ (1, 101)',\n", " ' -0.00122+0.00000 (-0.078%, 1.0 precs) @ (1, 2605)',\n", " ' -0.00122-0.00000 (+0.078%, -1.0 precs) @ (1, 2871)',\n", " '  0.00122+0.00000 (+0.078%, 1.0 precs) @ (1, 3197)',\n", " '  0.00123+0.00000 (+0.077%, 1.0 precs) @ (1, 1379)',\n", " '  0.00124-0.00000 (-0.077%, -1.0 precs) @ (1, 448)',\n", " '  0.00124+0.00000 (+0.077%, 1.0 precs) @ (1, 2524)',\n", " ' -0.00125+0.00000 (-0.076%, 1.0 precs) @ (1, 2647)',\n", " ' -0.00125-0.00000 (+0.076%, -1.0 precs) @ (1, 395)',\n", " '  0.00126+0.00000 (+0.075%, 1.0 precs) @ (1, 631)',\n", " '  0.00126+0.00000 (+0.075%, 1.0 precs) @ (1, 1448)',\n", " ' -0.00127+0.00000 (-0.075%, 1.0 precs) @ (1, 2221)',\n", " '  0.00129+0.00000 (+0.074%, 1.0 precs) @ (1, 1350)',\n", " ' -0.00129-0.00000 (+0.074%, -1.0 precs) @ (1, 1914)',\n", " ' -0.00129-0.00000 (+0.074%, -1.0 precs) @ (1, 523)',\n", " ' -0.00129-0.00000 (+0.074%, -1.0 precs) @ (1, 3481)',\n", " ' -0.00129+0.00000 (-0.074%, 1.0 precs) @ (1, 3298)',\n", " ' -0.00131-0.00000 (+0.073%, -1.0 precs) @ (1, 2048)',\n", " '  0.00132-0.00000 (-0.072%, -1.0 precs) @ (1, 3200)',\n", " ' -0.00132-0.00000 (+0.072%, -1.0 precs) @ (1, 1237)',\n", " ' -0.00133-0.00000 (+0.072%, -1.0 precs) @ (1, 969)',\n", " ' -0.00133-0.00000 (+0.072%, -1.0 precs) @ (1, 3292)',\n", " '  0.00136-0.00000 (-0.070%, -1.0 precs) @ (1, 566)',\n", " '  0.00136+0.00000 (+0.070%, 1.0 precs) @ (1, 350)',\n", " ' -0.00136+0.00000 (-0.070%, 1.0 precs) @ (1, 737)',\n", " '  0.00137-0.00000 (-0.070%, -1.0 precs) @ (1, 726)',\n", " '  0.00137-0.00000 (-0.070%, -1.0 precs) @ (1, 2238)',\n", " '  0.00137-0.00000 (-0.069%, -1.0 precs) @ (1, 2408)',\n", " ' -0.00137+0.00000 (-0.069%, 1.0 precs) @ (1, 3497)',\n", " '  0.00138+0.00000 (+0.069%, 1.0 precs) @ (1, 2949)',\n", " '  0.00138-0.00000 (-0.069%, -1.0 precs) @ (1, 3307)',\n", " '  0.00138-0.00000 (-0.069%, -1.0 precs) @ (1, 3313)',\n", " '  0.00140-0.00000 (-0.068%, -1.0 precs) @ (1, 3615)',\n", " '  0.00141+0.00000 (+0.068%, 1.0 precs) @ (1, 3501)',\n", " ' -0.00142+0.00000 (-0.067%, 1.0 precs) @ (1, 2852)',\n", " ' -0.00142-0.00000 (+0.067%, -1.0 precs) @ (1, 2486)',\n", " ' -0.00143-0.00000 (+0.067%, -1.0 precs) @ (1, 492)',\n", " ' -0.00143-0.00000 (+0.066%, -1.0 precs) @ (1, 3686)',\n", " ' -0.00144-0.00000 (+0.066%, -1.0 precs) @ (1, 1643)',\n", " ' -0.00144+0.00000 (-0.066%, 1.0 precs) @ (1, 556)',\n", " ' -0.00145+0.00000 (-0.066%, 1.0 precs) @ (1, 3346)',\n", " '  0.00145+0.00000 (+0.066%, 1.0 precs) @ (1, 2387)',\n", " ' -0.00146+0.00000 (-0.065%, 1.0 precs) @ (1, 2708)',\n", " ' -0.00146+0.00000 (-0.065%, 1.0 precs) @ (1, 1171)',\n", " ' -0.00146+0.00000 (-0.065%, 1.0 precs) @ (1, 3612)',\n", " '  0.00147-0.00000 (-0.065%, -1.0 precs) @ (1, 2098)',\n", " '  0.00147-0.00000 (-0.065%, -1.0 precs) @ (1, 2373)',\n", " '  0.00148+0.00000 (+0.065%, 1.0 precs) @ (1, 3051)',\n", " '  0.00149+0.00000 (+0.064%, 1.0 precs) @ (1, 1040)',\n", " ' -0.00149+0.00000 (-0.064%, 1.0 precs) @ (1, 2669)',\n", " ' -0.00150-0.00000 (+0.064%, -1.0 precs) @ (1, 1235)',\n", " ' -0.00150+0.00000 (-0.064%, 1.0 precs) @ (1, 2859)',\n", " ' -0.00151+0.00000 (-0.063%, 1.0 precs) @ (1, 109)',\n", " '  0.00153-0.00000 (-0.062%, -1.0 precs) @ (1, 3498)',\n", " ' -0.00154-0.00000 (+0.062%, -1.0 precs) @ (1, 335)',\n", " '  0.00154-0.00000 (-0.062%, -1.0 precs) @ (1, 1958)',\n", " '  0.00154+0.00000 (+0.062%, 1.0 precs) @ (1, 46)',\n", " '  0.00155-0.00000 (-0.062%, -1.0 precs) @ (1, 42)',\n", " ' -0.00156-0.00000 (+0.061%, -1.0 precs) @ (1, 2061)',\n", " ' -0.00156+0.00000 (-0.061%, 1.0 precs) @ (1, 1553)',\n", " '  0.00156+0.00000 (+0.061%, 1.0 precs) @ (1, 2839)',\n", " '  0.00156+0.00000 (+0.061%, 1.0 precs) @ (1, 2907)',\n", " '  0.00157+0.00000 (+0.061%, 1.0 precs) @ (1, 1494)',\n", " ' -0.00157+0.00000 (-0.061%, 1.0 precs) @ (1, 3328)',\n", " '  0.00157+0.00000 (+0.061%, 1.0 precs) @ (1, 3386)',\n", " ' -0.00158+0.00000 (-0.061%, 1.0 precs) @ (1, 955)',\n", " ' -0.00158-0.00000 (+0.060%, -1.0 precs) @ (1, 854)',\n", " '  0.00159-0.00000 (-0.060%, -1.0 precs) @ (1, 597)',\n", " '  0.00159+0.00000 (+0.060%, 1.0 precs) @ (1, 1670)',\n", " ' -0.00159-0.00000 (+0.060%, -1.0 precs) @ (1, 1700)',\n", " '  0.00159+0.00000 (+0.060%, 1.0 precs) @ (1, 1898)',\n", " '  0.00159+0.00000 (+0.060%, 1.0 precs) @ (1, 1282)',\n", " '  0.00160-0.00000 (-0.060%, -1.0 precs) @ (1, 648)',\n", " ' -0.00160-0.00000 (+0.060%, -1.0 precs) @ (1, 3701)',\n", " ' -0.00161+0.00000 (-0.059%, 1.0 precs) @ (1, 1088)',\n", " ' -0.00161-0.00000 (+0.059%, -1.0 precs) @ (1, 3224)',\n", " '  0.00162+0.00000 (+0.059%, 1.0 precs) @ (1, 1788)',\n", " '  0.00162+0.00000 (+0.059%, 1.0 precs) @ (1, 3371)',\n", " '  0.00162+0.00000 (+0.059%, 1.0 precs) @ (1, 453)',\n", " ' -0.00163+0.00000 (-0.059%, 1.0 precs) @ (1, 890)',\n", " ' -0.00163+0.00000 (-0.059%, 1.0 precs) @ (1, 1697)',\n", " ' -0.00163+0.00000 (-0.058%, 1.0 precs) @ (1, 2190)',\n", " '  0.00163+0.00000 (+0.058%, 1.0 precs) @ (1, 2414)',\n", " '  0.00164+0.00000 (+0.058%, 1.0 precs) @ (1, 1270)',\n", " '  0.00164+0.00000 (+0.058%, 1.0 precs) @ (1, 696)',\n", " ' -0.00164+0.00000 (-0.058%, 1.0 precs) @ (1, 3623)',\n", " '  0.00165-0.00000 (-0.058%, -1.0 precs) @ (1, 1215)',\n", " ' -0.00165-0.00000 (+0.058%, -1.0 precs) @ (1, 2850)',\n", " '  0.00165-0.00000 (-0.058%, -1.0 precs) @ (1, 3037)',\n", " '  0.00166-0.00000 (-0.057%, -1.0 precs) @ (1, 2656)',\n", " ' -0.00167+0.00000 (-0.057%, 1.0 precs) @ (1, 2091)',\n", " '  0.00168-0.00000 (-0.057%, -1.0 precs) @ (1, 2411)',\n", " ' -0.00168-0.00000 (+0.057%, -1.0 precs) @ (1, 3074)',\n", " '  0.00168+0.00000 (+0.057%, 1.0 precs) @ (1, 3484)',\n", " '  0.00169+0.00000 (+0.057%, 1.0 precs) @ (1, 3797)',\n", " '  0.00169-0.00000 (-0.056%, -1.0 precs) @ (1, 79)',\n", " '  0.00169-0.00000 (-0.056%, -1.0 precs) @ (1, 2713)',\n", " '  0.00170+0.00000 (+0.056%, 1.0 precs) @ (1, 1329)',\n", " '  0.00169+0.00000 (+0.056%, 1.0 precs) @ (1, 3687)',\n", " ' -0.00170-0.00000 (+0.056%, -1.0 precs) @ (1, 506)',\n", " '  0.00170+0.00000 (+0.056%, 1.0 precs) @ (1, 1926)',\n", " '  0.00171-0.00000 (-0.056%, -1.0 precs) @ (1, 1752)',\n", " ' -0.00171-0.00000 (+0.056%, -1.0 precs) @ (1, 2714)',\n", " ' -0.00171-0.00000 (+0.056%, -1.0 precs) @ (1, 2154)',\n", " ' -0.00172-0.00000 (+0.056%, -1.0 precs) @ (1, 3246)',\n", " '  0.00172-0.00000 (-0.055%, -1.0 precs) @ (1, 409)',\n", " ' -0.00172+0.00000 (-0.055%, 1.0 precs) @ (1, 1897)',\n", " ' -0.00173-0.00000 (+0.055%, -1.0 precs) @ (1, 2577)',\n", " ' -0.00173-0.00000 (+0.055%, -1.0 precs) @ (1, 2984)',\n", " '  0.00174+0.00000 (+0.055%, 1.0 precs) @ (1, 995)',\n", " '  0.00174+0.00000 (+0.055%, 1.0 precs) @ (1, 2720)',\n", " '  0.00175-0.00000 (-0.055%, -1.0 precs) @ (1, 1723)',\n", " '  0.00176-0.00000 (-0.054%, -1.0 precs) @ (1, 3835)',\n", " '  0.00176-0.00000 (-0.054%, -1.0 precs) @ (1, 646)',\n", " '  0.00176+0.00000 (+0.054%, 1.0 precs) @ (1, 2246)',\n", " '  0.00177-0.00000 (-0.054%, -1.0 precs) @ (1, 1157)',\n", " ' -0.00177+0.00000 (-0.054%, 1.0 precs) @ (1, 2541)',\n", " '  0.00177+0.00000 (+0.054%, 1.0 precs) @ (1, 2702)',\n", " '  0.00177-0.00000 (-0.054%, -1.0 precs) @ (1, 3783)',\n", " '  0.00177-0.00000 (-0.054%, -1.0 precs) @ (1, 2359)',\n", " ' -0.00178-0.00000 (+0.054%, -1.0 precs) @ (1, 216)',\n", " ' -0.00178+0.00000 (-0.054%, 1.0 precs) @ (1, 2476)',\n", " '  0.00178-0.00000 (-0.054%, -1.0 precs) @ (1, 3768)',\n", " ' -0.00179+0.00000 (-0.053%, 1.0 precs) @ (1, 2436)',\n", " '  0.00180-0.00000 (-0.053%, -1.0 precs) @ (1, 2167)',\n", " '  0.00180+0.00000 (+0.053%, 1.0 precs) @ (1, 1566)',\n", " ' -0.00181+0.00000 (-0.053%, 1.0 precs) @ (1, 984)',\n", " ' -0.00182-0.00000 (+0.052%, -1.0 precs) @ (1, 1199)',\n", " ' -0.00182+0.00000 (-0.052%, 1.0 precs) @ (1, 3816)',\n", " '  0.00182+0.00000 (+0.052%, 1.0 precs) @ (1, 214)',\n", " '  0.00183+0.00000 (+0.052%, 1.0 precs) @ (1, 3463)',\n", " ' -0.00183+0.00000 (-0.052%, 1.0 precs) @ (1, 1651)',\n", " '  0.00184+0.00000 (+0.052%, 1.0 precs) @ (1, 3648)',\n", " ' -0.00184+0.00000 (-0.052%, 1.0 precs) @ (1, 3690)',\n", " ' -0.00184-0.00000 (+0.052%, -1.0 precs) @ (1, 1847)',\n", " ' -0.00184+0.00000 (-0.052%, 1.0 precs) @ (1, 3104)',\n", " ' -0.00185+0.00000 (-0.052%, 1.0 precs) @ (1, 17)',\n", " '  0.00186+0.00000 (+0.051%, 1.0 precs) @ (1, 930)',\n", " '  0.00186-0.00000 (-0.051%, -1.0 precs) @ (1, 2576)',\n", " ' -0.00186+0.00000 (-0.051%, 1.0 precs) @ (1, 1851)',\n", " ' -0.00186+0.00000 (-0.051%, 1.0 precs) @ (1, 3482)',\n", " ' -0.00187-0.00000 (+0.051%, -1.0 precs) @ (1, 2657)',\n", " ' -0.00187-0.00000 (+0.051%, -1.0 precs) @ (1, 1232)',\n", " '  0.00187+0.00000 (+0.051%, 1.0 precs) @ (1, 1877)',\n", " ' -0.00188-0.00000 (+0.051%, -1.0 precs) @ (1, 1322)',\n", " ' -0.00188+0.00000 (-0.051%, 1.0 precs) @ (1, 1855)',\n", " '  0.00188-0.00000 (-0.051%, -1.0 precs) @ (1, 1648)',\n", " ' -0.00189+0.00000 (-0.050%, 1.0 precs) @ (1, 862)',\n", " ' -0.00191+0.00000 (-0.050%, 1.0 precs) @ (1, 178)',\n", " '  0.00191-0.00000 (-0.050%, -1.0 precs) @ (1, 177)',\n", " '  0.00191+0.00000 (+0.050%, 1.0 precs) @ (1, 1943)',\n", " '  0.00191+0.00000 (+0.050%, 1.0 precs) @ (1, 3848)',\n", " '  0.00192-0.00000 (-0.050%, -1.0 precs) @ (1, 2004)',\n", " ' -0.00192+0.00000 (-0.050%, 1.0 precs) @ (1, 3759)',\n", " '  0.00193+0.00000 (+0.049%, 1.0 precs) @ (1, 3348)',\n", " '  0.00193+0.00000 (+0.049%, 1.0 precs) @ (1, 2115)',\n", " '  0.00194-0.00000 (-0.049%, -1.0 precs) @ (1, 419)',\n", " '  0.00194+0.00000 (+0.049%, 1.0 precs) @ (1, 1101)',\n", " ' -0.00195-0.00000 (+0.049%, -1.0 precs) @ (1, 3667)',\n", " ' -0.00003-0.00000 (+2.707%, -15.0 precs) @ (1, 3042)',\n", " ' -0.00015-0.00000 (+0.556%, -7.0 precs) @ (1, 3131)',\n", " '  0.00018+0.00000 (+0.472%, 7.0 precs) @ (1, 1783)',\n", " '  0.00016-0.00000 (-0.458%, -6.0 precs) @ (1, 2314)',\n", " '  0.00018-0.00000 (-0.392%, -6.0 precs) @ (1, 2795)',\n", " ' -0.00022+0.00000 (-0.331%, 6.0 precs) @ (1, 36)',\n", " '  0.00026-0.00000 (-0.276%, -3.0 precs) @ (1, 2520)',\n", " '  0.00027+0.00000 (+0.266%, 3.0 precs) @ (1, 2347)',\n", " ' -0.00027-0.00000 (+0.263%, -3.0 precs) @ (1, 401)',\n", " ' -0.00027-0.00000 (+0.261%, -3.0 precs) @ (1, 3107)',\n", " '  0.00029-0.00000 (-0.247%, -3.0 precs) @ (1, 931)',\n", " '  0.00029-0.00000 (-0.243%, -3.0 precs) @ (1, 541)',\n", " ' -0.00030+0.00000 (-0.241%, 3.0 precs) @ (1, 161)',\n", " ' -0.00030+0.00000 (-0.236%, 3.0 precs) @ (1, 771)',\n", " ' -0.00031-0.00000 (+0.234%, -3.0 precs) @ (1, 2619)',\n", " '  0.00031+0.00000 (+0.229%, 3.0 precs) @ (1, 437)',\n", " '  0.00033-0.00000 (-0.219%, -3.0 precs) @ (1, 2133)',\n", " ' -0.00034-0.00000 (+0.212%, -3.0 precs) @ (1, 2139)',\n", " ' -0.00034+0.00000 (-0.209%, 3.0 precs) @ (1, 683)',\n", " ' -0.00038-0.00000 (+0.188%, -3.0 precs) @ (1, 450)',\n", " '  0.00039+0.00000 (+0.186%, 3.0 precs) @ (0, 4831)',\n", " '  0.00039-0.00000 (-0.184%, -3.0 precs) @ (1, 2036)',\n", " ' -0.00040-0.00000 (+0.180%, -3.0 precs) @ (0, 3714)',\n", " '  0.00043+0.00000 (+0.167%, 3.0 precs) @ (0, 5370)',\n", " ' -0.00045-0.00000 (+0.159%, -3.0 precs) @ (1, 39)',\n", " '  0.00045+0.00000 (+0.159%, 3.0 precs) @ (1, 1810)',\n", " '  0.00045+0.00000 (+0.158%, 3.0 precs) @ (1, 2715)',\n", " '  0.00047+0.00000 (+0.152%, 3.0 precs) @ (0, 1392)',\n", " ' -0.00047-0.00000 (+0.151%, -3.0 precs) @ (1, 2140)',\n", " '  0.00048-0.00000 (-0.149%, -3.0 precs) @ (1, 1661)',\n", " ' -0.00049-0.00000 (+0.147%, -3.0 precs) @ (0, 3286)',\n", " '  0.00011-0.00000 (-0.587%, -11.0 precs) @ (1, 3679)',\n", " '  0.00001-0.00000 (-7.355%, -10.0 precs) @ (1, 2963)',\n", " '  0.00002-0.00000 (-3.247%, -10.0 precs) @ (1, 3824)',\n", " ' -0.00013+0.00000 (-0.475%, 5.0 precs) @ (1, 661)',\n", " ' -0.00014+0.00000 (-0.426%, 5.0 precs) @ (1, 3332)',\n", " ' -0.00017+0.00000 (-0.360%, 5.0 precs) @ (1, 3813)',\n", " '  0.00022-0.00000 (-0.270%, -5.0 precs) @ (1, 1977)',\n", " '  0.00023-0.00000 (-0.263%, -5.0 precs) @ (1, 2363)',\n", " '  0.00024-0.00000 (-0.252%, -5.0 precs) @ (1, 1724)',\n", " '  0.00006+0.00000 (+0.829%, 9.0 precs) @ (1, 1993)',\n", " ' -0.00007+0.00000 (-0.795%, 9.0 precs) @ (1, 1525)',\n", " ' -0.00008+0.00000 (-0.700%, 9.0 precs) @ (1, 3517)',\n", " ' -0.00010-0.00000 (+0.513%, -9.0 precs) @ (1, 3852)',\n", " ' -0.00012+0.00000 (-0.454%, 9.0 precs) @ (1, 1327)',\n", " ' -0.00001+0.00000 (-7.690%, 8.0 precs) @ (1, 612)',\n", " '  0.00001-0.00000 (-7.410%, -8.0 precs) @ (1, 634)',\n", " '  0.00003+0.00000 (+1.569%, 8.0 precs) @ (1, 1155)',\n", " '  0.00007-0.00000 (-0.661%, -8.0 precs) @ (1, 891)',\n", " ' -0.00008-0.00000 (+0.630%, -8.0 precs) @ (1, 3821)',\n", " ' -0.00012+0.00000 (-0.388%, 4.0 precs) @ (1, 406)',\n", " ' -0.00013+0.00000 (-0.375%, 4.0 precs) @ (1, 811)',\n", " ' -0.00013+0.00000 (-0.359%, 4.0 precs) @ (1, 2889)',\n", " ' -0.00014+0.00000 (-0.346%, 4.0 precs) @ (1, 18)',\n", " '  0.00016+0.00000 (+0.304%, 4.0 precs) @ (1, 1300)',\n", " ' -0.00017-0.00000 (+0.287%, -4.0 precs) @ (1, 2665)',\n", " '  0.00017+0.00000 (+0.280%, 4.0 precs) @ (1, 2985)',\n", " ' -0.00017+0.00000 (-0.280%, 4.0 precs) @ (1, 2469)',\n", " ' -0.00017-0.00000 (+0.273%, -4.0 precs) @ (1, 2514)',\n", " ' -0.00018-0.00000 (+0.269%, -4.0 precs) @ (1, 2114)',\n", " '  0.00018-0.00000 (-0.265%, -4.0 precs) @ (1, 2237)',\n", " '  0.00019-0.00000 (-0.254%, -4.0 precs) @ (1, 137)',\n", " '  0.00021-0.00000 (-0.224%, -4.0 precs) @ (1, 666)',\n", " '  0.00021-0.00000 (-0.224%, -4.0 precs) @ (1, 3139)',\n", " '  0.00025+0.00000 (+0.194%, 2.0 precs) @ (0, 1412)',\n", " ' -0.00025+0.00000 (-0.193%, 2.0 precs) @ (1, 1938)',\n", " ' -0.00025-0.00000 (+0.193%, -2.0 precs) @ (0, 4763)',\n", " ' -0.00025-0.00000 (+0.192%, -2.0 precs) @ (0, 5366)',\n", " ' -0.00025-0.00000 (+0.192%, -2.0 precs) @ (0, 1213)',\n", " '  0.00025+0.00000 (+0.191%, 2.0 precs) @ (0, 792)',\n", " '  0.00025+0.00000 (+0.190%, 2.0 precs) @ (0, 4848)',\n", " ' -0.00025-0.00000 (+0.189%, -2.0 precs) @ (0, 99)',\n", " ' -0.00025-0.00000 (+0.188%, -2.0 precs) @ (0, 4459)',\n", " '  0.00025+0.00000 (+0.187%, 2.0 precs) @ (0, 4911)',\n", " '  0.00025+0.00000 (+0.187%, 2.0 precs) @ (0, 5135)',\n", " ' -0.00025-0.00000 (+0.187%, -2.0 precs) @ (0, 5335)',\n", " '  0.00026+0.00000 (+0.185%, 2.0 precs) @ (0, 379)',\n", " ' -0.00026-0.00000 (+0.184%, -2.0 precs) @ (0, 51)',\n", " '  0.00026+0.00000 (+0.184%, 2.0 precs) @ (0, 3507)',\n", " ' -0.00026-0.00000 (+0.183%, -2.0 precs) @ (0, 5244)',\n", " '  0.00026+0.00000 (+0.180%, 2.0 precs) @ (0, 14)',\n", " '  0.00026+0.00000 (+0.180%, 2.0 precs) @ (0, 4181)',\n", " ' -0.00027-0.00000 (+0.180%, -2.0 precs) @ (0, 3155)',\n", " '  0.00027+0.00000 (+0.179%, 2.0 precs) @ (0, 4166)',\n", " '  0.00027+0.00000 (+0.179%, 2.0 precs) @ (0, 5593)',\n", " '  0.00027+0.00000 (+0.178%, 2.0 precs) @ (0, 3767)',\n", " '  0.00027+0.00000 (+0.178%, 2.0 precs) @ (0, 4086)',\n", " ' -0.00027-0.00000 (+0.175%, -2.0 precs) @ (0, 3724)',\n", " ' -0.00027-0.00000 (+0.175%, -2.0 precs) @ (0, 982)',\n", " '  0.00027+0.00000 (+0.174%, 2.0 precs) @ (0, 3588)',\n", " ' -0.00027-0.00000 (+0.174%, -2.0 precs) @ (0, 5291)',\n", " ' -0.00027+0.00000 (-0.174%, 2.0 precs) @ (1, 78)',\n", " ' -0.00028-0.00000 (+0.173%, -2.0 precs) @ (0, 1623)',\n", " '  0.00028+0.00000 (+0.173%, 2.0 precs) @ (0, 1133)',\n", " '  0.00028+0.00000 (+0.172%, 2.0 precs) @ (0, 4559)',\n", " '  0.00028+0.00000 (+0.171%, 2.0 precs) @ (0, 2169)',\n", " ' -0.00028-0.00000 (+0.170%, -2.0 precs) @ (0, 5043)',\n", " ' -0.00028-0.00000 (+0.170%, -2.0 precs) @ (0, 2376)',\n", " '  0.00028+0.00000 (+0.170%, 2.0 precs) @ (0, 4766)',\n", " ' -0.00028-0.00000 (+0.170%, -2.0 precs) @ (0, 2337)',\n", " '  0.00028+0.00000 (+0.169%, 2.0 precs) @ (0, 1930)',\n", " '  0.00028+0.00000 (+0.169%, 2.0 precs) @ (0, 5460)',\n", " '  0.00029+0.00000 (+0.167%, 2.0 precs) @ (0, 2558)',\n", " '  0.00029+0.00000 (+0.167%, 2.0 precs) @ (0, 4567)',\n", " ' -0.00029-0.00000 (+0.167%, -2.0 precs) @ (0, 4938)',\n", " ' -0.00029-0.00000 (+0.167%, -2.0 precs) @ (0, 3237)',\n", " ' -0.00029-0.00000 (+0.166%, -2.0 precs) @ (0, 1085)',\n", " '  0.00029+0.00000 (+0.166%, 2.0 precs) @ (0, 2317)',\n", " '  0.00029+0.00000 (+0.166%, 2.0 precs) @ (0, 5052)',\n", " ' -0.00029+0.00000 (-0.166%, 2.0 precs) @ (1, 2671)',\n", " ' -0.00029-0.00000 (+0.166%, -2.0 precs) @ (0, 5815)',\n", " '  0.00029+0.00000 (+0.166%, 2.0 precs) @ (0, 5711)',\n", " '  0.00029+0.00000 (+0.165%, 2.0 precs) @ (0, 58)',\n", " ' -0.00029-0.00000 (+0.165%, -2.0 precs) @ (0, 5953)',\n", " '  0.00029+0.00000 (+0.163%, 2.0 precs) @ (0, 106)',\n", " '  0.00029+0.00000 (+0.163%, 2.0 precs) @ (0, 4380)',\n", " '  0.00029+0.00000 (+0.163%, 2.0 precs) @ (0, 3858)',\n", " '  0.00029+0.00000 (+0.162%, 2.0 precs) @ (0, 5738)',\n", " ' -0.00029-0.00000 (+0.162%, -2.0 precs) @ (0, 5822)',\n", " ' -0.00030-0.00000 (+0.161%, -2.0 precs) @ (0, 3969)',\n", " ' -0.00030-0.00000 (+0.161%, -2.0 precs) @ (0, 6023)',\n", " '  0.00030+0.00000 (+0.160%, 2.0 precs) @ (0, 3258)',\n", " ' -0.00030-0.00000 (+0.160%, -2.0 precs) @ (0, 3353)',\n", " ' -0.00030-0.00000 (+0.159%, -2.0 precs) @ (0, 1363)',\n", " '  0.00030+0.00000 (+0.159%, 2.0 precs) @ (0, 1498)',\n", " ' -0.00030-0.00000 (+0.158%, -2.0 precs) @ (0, 3053)',\n", " ' -0.00030+0.00000 (-0.157%, 2.0 precs) @ (1, 2936)',\n", " ' -0.00030-0.00000 (+0.157%, -2.0 precs) @ (0, 1707)',\n", " ' -0.00030-0.00000 (+0.157%, -2.0 precs) @ (0, 1989)',\n", " '  0.00030+0.00000 (+0.157%, 2.0 precs) @ (0, 826)',\n", " ' -0.00030-0.00000 (+0.157%, -2.0 precs) @ (0, 4668)',\n", " ' -0.00031-0.00000 (+0.156%, -2.0 precs) @ (0, 3069)',\n", " '  0.00031+0.00000 (+0.156%, 2.0 precs) @ (1, 1275)',\n", " '  0.00031+0.00000 (+0.156%, 2.0 precs) @ (0, 56)',\n", " ' -0.00031-0.00000 (+0.155%, -2.0 precs) @ (0, 5968)',\n", " '  0.00031+0.00000 (+0.155%, 2.0 precs) @ (0, 5155)',\n", " ' -0.00031-0.00000 (+0.155%, -2.0 precs) @ (0, 3808)',\n", " ' -0.00031-0.00000 (+0.154%, -2.0 precs) @ (0, 703)',\n", " '  0.00031+0.00000 (+0.153%, 2.0 precs) @ (0, 4150)',\n", " ' -0.00031-0.00000 (+0.153%, -2.0 precs) @ (0, 2484)',\n", " ' -0.00031-0.00000 (+0.153%, -2.0 precs) @ (0, 2193)',\n", " '  0.00031+0.00000 (+0.152%, 2.0 precs) @ (0, 1079)',\n", " '  0.00031+0.00000 (+0.152%, 2.0 precs) @ (0, 4172)',\n", " ' -0.00031-0.00000 (+0.152%, -2.0 precs) @ (0, 4337)',\n", " '  0.00031+0.00000 (+0.152%, 2.0 precs) @ (0, 1287)',\n", " '  0.00032+0.00000 (+0.151%, 2.0 precs) @ (0, 5236)',\n", " ' -0.00032-0.00000 (+0.151%, -2.0 precs) @ (0, 4740)',\n", " '  0.00032+0.00000 (+0.151%, 2.0 precs) @ (1, 601)',\n", " '  0.00032-0.00000 (-0.150%, -2.0 precs) @ (1, 3247)',\n", " ' -0.00032-0.00000 (+0.150%, -2.0 precs) @ (0, 4097)',\n", " '  0.00032+0.00000 (+0.149%, 2.0 precs) @ (0, 3178)',\n", " '  0.00032+0.00000 (+0.149%, 2.0 precs) @ (0, 3302)',\n", " ' -0.00032-0.00000 (+0.149%, -2.0 precs) @ (0, 6142)',\n", " ' -0.00032-0.00000 (+0.149%, -2.0 precs) @ (0, 2973)',\n", " '  0.00032+0.00000 (+0.149%, 2.0 precs) @ (0, 1540)',\n", " ' -0.00032-0.00000 (+0.149%, -2.0 precs) @ (0, 6113)',\n", " '  0.00032-0.00000 (-0.149%, -2.0 precs) @ (1, 1925)',\n", " '  0.00032+0.00000 (+0.149%, 2.0 precs) @ (0, 2472)',\n", " ' -0.00032-0.00000 (+0.149%, -2.0 precs) @ (0, 4674)',\n", " '  0.00032-0.00000 (-0.149%, -2.0 precs) @ (1, 1334)',\n", " '  0.00032-0.00000 (-0.148%, -2.0 precs) @ (1, 3098)',\n", " ' -0.00032-0.00000 (+0.148%, -2.0 precs) @ (0, 5546)',\n", " ' -0.00032-0.00000 (+0.148%, -2.0 precs) @ (0, 4437)',\n", " ' -0.00033-0.00000 (+0.146%, -2.0 precs) @ (0, 4904)',\n", " ' -0.00033-0.00000 (+0.146%, -2.0 precs) @ (0, 5317)',\n", " '  0.00033+0.00000 (+0.146%, 2.0 precs) @ (0, 29)',\n", " '  0.00033+0.00000 (+0.145%, 2.0 precs) @ (0, 5792)',\n", " '  0.00033+0.00000 (+0.145%, 2.0 precs) @ (0, 3365)',\n", " ' -0.00033-0.00000 (+0.144%, -2.0 precs) @ (0, 485)',\n", " ' -0.00033-0.00000 (+0.144%, -2.0 precs) @ (0, 2220)',\n", " ' -0.00033+0.00000 (-0.144%, 2.0 precs) @ (1, 2493)',\n", " '  0.00033+0.00000 (+0.143%, 2.0 precs) @ (0, 5156)',\n", " ' -0.00033-0.00000 (+0.143%, -2.0 precs) @ (0, 4570)',\n", " '  0.00033+0.00000 (+0.143%, 2.0 precs) @ (0, 4704)',\n", " '  0.00033+0.00000 (+0.143%, 2.0 precs) @ (0, 750)',\n", " ' -0.00033-0.00000 (+0.143%, -2.0 precs) @ (0, 3854)',\n", " '  0.00033+0.00000 (+0.143%, 2.0 precs) @ (0, 6062)',\n", " ' -0.00033-0.00000 (+0.143%, -2.0 precs) @ (0, 860)',\n", " '  0.00033+0.00000 (+0.143%, 2.0 precs) @ (0, 5055)',\n", " ' -0.00033-0.00000 (+0.143%, -2.0 precs) @ (0, 6061)',\n", " '  0.00033+0.00000 (+0.142%, 2.0 precs) @ (0, 5693)',\n", " '  0.00034+0.00000 (+0.142%, 2.0 precs) @ (0, 3345)',\n", " '  0.00034+0.00000 (+0.142%, 2.0 precs) @ (1, 3704)',\n", " '  0.00034+0.00000 (+0.142%, 2.0 precs) @ (0, 627)',\n", " ' -0.00034-0.00000 (+0.142%, -2.0 precs) @ (0, 2938)',\n", " '  0.00034+0.00000 (+0.142%, 2.0 precs) @ (0, 4692)',\n", " ' -0.00034-0.00000 (+0.142%, -2.0 precs) @ (0, 5869)',\n", " '  0.00034+0.00000 (+0.142%, 2.0 precs) @ (0, 2173)',\n", " ' -0.00034-0.00000 (+0.141%, -2.0 precs) @ (0, 653)',\n", " '  0.00034+0.00000 (+0.141%, 2.0 precs) @ (0, 1484)',\n", " '  0.00034+0.00000 (+0.141%, 2.0 precs) @ (0, 5626)',\n", " '  0.00034-0.00000 (-0.141%, -2.0 precs) @ (1, 2652)',\n", " '  0.00034+0.00000 (+0.141%, 2.0 precs) @ (0, 3537)',\n", " '  0.00034+0.00000 (+0.141%, 2.0 precs) @ (0, 902)',\n", " ' -0.00034-0.00000 (+0.140%, -2.0 precs) @ (0, 4286)',\n", " ' -0.00034-0.00000 (+0.140%, -2.0 precs) @ (0, 1980)',\n", " '  0.00034+0.00000 (+0.140%, 2.0 precs) @ (1, 2782)',\n", " '  0.00034+0.00000 (+0.140%, 2.0 precs) @ (0, 3240)',\n", " ' -0.00034+0.00000 (-0.140%, 2.0 precs) @ (1, 249)',\n", " ' -0.00034-0.00000 (+0.140%, -2.0 precs) @ (0, 1066)',\n", " ' -0.00034-0.00000 (+0.140%, -2.0 precs) @ (0, 3113)',\n", " '  0.00034+0.00000 (+0.139%, 2.0 precs) @ (0, 446)',\n", " '  0.00034+0.00000 (+0.139%, 2.0 precs) @ (0, 2217)',\n", " ' -0.00034-0.00000 (+0.139%, -2.0 precs) @ (0, 2394)',\n", " ' -0.00034-0.00000 (+0.139%, -2.0 precs) @ (0, 344)',\n", " ' -0.00035-0.00000 (+0.138%, -2.0 precs) @ (0, 2393)',\n", " '  0.00035+0.00000 (+0.138%, 2.0 precs) @ (0, 1055)',\n", " ' -0.00035-0.00000 (+0.138%, -2.0 precs) @ (0, 3219)',\n", " ' -0.00035-0.00000 (+0.138%, -2.0 precs) @ (0, 4400)',\n", " '  0.00035+0.00000 (+0.137%, 2.0 precs) @ (0, 2568)',\n", " '  0.00035+0.00000 (+0.137%, 2.0 precs) @ (0, 1293)',\n", " ' -0.00035-0.00000 (+0.137%, -2.0 precs) @ (0, 1765)',\n", " ' -0.00035+0.00000 (-0.137%, 2.0 precs) @ (1, 3630)',\n", " ' -0.00035-0.00000 (+0.136%, -2.0 precs) @ (0, 4969)',\n", " '  0.00035+0.00000 (+0.136%, 2.0 precs) @ (0, 2496)',\n", " '  0.00035+0.00000 (+0.136%, 2.0 precs) @ (0, 3642)',\n", " ' -0.00035-0.00000 (+0.136%, -2.0 precs) @ (0, 2348)',\n", " '  0.00035+0.00000 (+0.135%, 2.0 precs) @ (0, 2309)',\n", " ' -0.00035-0.00000 (+0.135%, -2.0 precs) @ (1, 3602)',\n", " ' -0.00035-0.00000 (+0.134%, -2.0 precs) @ (0, 3718)',\n", " '  0.00036+0.00000 (+0.134%, 2.0 precs) @ (0, 3972)',\n", " ' -0.00036-0.00000 (+0.133%, -2.0 precs) @ (0, 86)',\n", " '  0.00036+0.00000 (+0.133%, 2.0 precs) @ (0, 1827)',\n", " '  0.00036+0.00000 (+0.133%, 2.0 precs) @ (0, 5401)',\n", " '  0.00036+0.00000 (+0.133%, 2.0 precs) @ (0, 5277)',\n", " '  0.00036+0.00000 (+0.133%, 2.0 precs) @ (0, 589)',\n", " '  0.00036+0.00000 (+0.133%, 2.0 precs) @ (0, 3651)',\n", " ' -0.00036-0.00000 (+0.133%, -2.0 precs) @ (0, 1297)',\n", " ' -0.00036-0.00000 (+0.133%, -2.0 precs) @ (0, 2120)',\n", " '  0.00036+0.00000 (+0.132%, 2.0 precs) @ (0, 231)',\n", " ' -0.00036-0.00000 (+0.132%, -2.0 precs) @ (0, 3956)',\n", " '  0.00036+0.00000 (+0.132%, 2.0 precs) @ (0, 691)',\n", " '  0.00036+0.00000 (+0.131%, 2.0 precs) @ (0, 4440)',\n", " '  0.00036+0.00000 (+0.131%, 2.0 precs) @ (0, 4897)',\n", " ' -0.00036-0.00000 (+0.131%, -2.0 precs) @ (0, 3151)',\n", " ' -0.00036-0.00000 (+0.131%, -2.0 precs) @ (0, 4388)',\n", " ' -0.00037-0.00000 (+0.131%, -2.0 precs) @ (0, 3951)',\n", " '  0.00037+0.00000 (+0.130%, 2.0 precs) @ (0, 2356)',\n", " '  0.00037+0.00000 (+0.130%, 2.0 precs) @ (0, 443)',\n", " '  0.00037+0.00000 (+0.130%, 2.0 precs) @ (0, 1802)',\n", " '  0.00037+0.00000 (+0.130%, 2.0 precs) @ (0, 3997)',\n", " ' -0.00037-0.00000 (+0.130%, -2.0 precs) @ (0, 1034)',\n", " '  0.00037+0.00000 (+0.130%, 2.0 precs) @ (0, 1333)',\n", " ' -0.00037-0.00000 (+0.130%, -2.0 precs) @ (0, 4739)',\n", " ' -0.00037-0.00000 (+0.129%, -2.0 precs) @ (0, 1762)',\n", " ' -0.00037-0.00000 (+0.129%, -2.0 precs) @ (0, 5001)',\n", " '  0.00037+0.00000 (+0.129%, 2.0 precs) @ (0, 5866)',\n", " ' -0.00037-0.00000 (+0.129%, -2.0 precs) @ (0, 2369)',\n", " ' -0.00037-0.00000 (+0.128%, -2.0 precs) @ (0, 4888)',\n", " '  0.00037+0.00000 (+0.128%, 2.0 precs) @ (0, 48)',\n", " ' -0.00037-0.00000 (+0.128%, -2.0 precs) @ (0, 1330)',\n", " '  0.00037+0.00000 (+0.128%, 2.0 precs) @ (0, 2815)',\n", " '  0.00037+0.00000 (+0.128%, 2.0 precs) @ (0, 3137)',\n", " '  0.00037+0.00000 (+0.127%, 2.0 precs) @ (0, 5867)',\n", " ' -0.00037-0.00000 (+0.127%, -2.0 precs) @ (0, 2315)',\n", " '  0.00038+0.00000 (+0.127%, 2.0 precs) @ (0, 1880)',\n", " ' -0.00038-0.00000 (+0.127%, -2.0 precs) @ (0, 3316)',\n", " ' -0.00038-0.00000 (+0.126%, -2.0 precs) @ (1, 2128)',\n", " '  0.00038+0.00000 (+0.126%, 2.0 precs) @ (0, 3375)',\n", " ' -0.00038-0.00000 (+0.126%, -2.0 precs) @ (0, 4940)',\n", " '  0.00038+0.00000 (+0.126%, 2.0 precs) @ (0, 3430)',\n", " ' -0.00038-0.00000 (+0.126%, -2.0 precs) @ (0, 5202)',\n", " '  0.00038+0.00000 (+0.126%, 2.0 precs) @ (0, 749)',\n", " ' -0.00038-0.00000 (+0.126%, -2.0 precs) @ (0, 2134)',\n", " ' -0.00038-0.00000 (+0.126%, -2.0 precs) @ (0, 5493)',\n", " '  0.00038+0.00000 (+0.126%, 2.0 precs) @ (0, 4564)',\n", " ' -0.00038-0.00000 (+0.125%, -2.0 precs) @ (0, 2660)',\n", " '  0.00038+0.00000 (+0.125%, 2.0 precs) @ (1, 3443)',\n", " ' -0.00038-0.00000 (+0.125%, -2.0 precs) @ (0, 203)',\n", " ' -0.00038-0.00000 (+0.125%, -2.0 precs) @ (0, 3003)',\n", " ' -0.00038-0.00000 (+0.125%, -2.0 precs) @ (0, 5020)',\n", " '  0.00038+0.00000 (+0.125%, 2.0 precs) @ (0, 1198)',\n", " '  0.00038+0.00000 (+0.125%, 2.0 precs) @ (1, 1644)',\n", " '  0.00038+0.00000 (+0.125%, 2.0 precs) @ (0, 1420)',\n", " ' -0.00038-0.00000 (+0.125%, -2.0 precs) @ (0, 3324)',\n", " ' -0.00038-0.00000 (+0.124%, -2.0 precs) @ (0, 809)',\n", " ' -0.00038-0.00000 (+0.124%, -2.0 precs) @ (0, 2230)',\n", " ' -0.00038-0.00000 (+0.124%, -2.0 precs) @ (0, 3966)',\n", " ' -0.00038-0.00000 (+0.124%, -2.0 precs) @ (0, 4334)',\n", " '  0.00038+0.00000 (+0.124%, 2.0 precs) @ (0, 3232)',\n", " '  0.00038+0.00000 (+0.124%, 2.0 precs) @ (0, 4582)',\n", " ' -0.00038-0.00000 (+0.124%, -2.0 precs) @ (0, 6136)',\n", " '  0.00039+0.00000 (+0.124%, 2.0 precs) @ (0, 2921)',\n", " ' -0.00039-0.00000 (+0.124%, -2.0 precs) @ (0, 1619)',\n", " ' -0.00039-0.00000 (+0.124%, -2.0 precs) @ (0, 5400)',\n", " '  0.00039+0.00000 (+0.124%, 2.0 precs) @ (1, 3431)',\n", " '  0.00039+0.00000 (+0.123%, 2.0 precs) @ (0, 990)',\n", " ' -0.00039-0.00000 (+0.123%, -2.0 precs) @ (0, 633)',\n", " ' -0.00039-0.00000 (+0.123%, -2.0 precs) @ (0, 4708)',\n", " ' -0.00039-0.00000 (+0.123%, -2.0 precs) @ (0, 5616)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (0, 4121)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (0, 5717)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (0, 5889)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (0, 3265)',\n", " '  0.00039+0.00000 (+0.122%, 2.0 precs) @ (0, 1120)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (0, 1829)',\n", " '  0.00039+0.00000 (+0.122%, 2.0 precs) @ (0, 5061)',\n", " '  0.00039+0.00000 (+0.122%, 2.0 precs) @ (0, 2106)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (0, 1590)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (0, 5438)',\n", " ' -0.00039-0.00000 (+0.122%, -2.0 precs) @ (1, 3342)',\n", " ' -0.00039-0.00000 (+0.121%, -2.0 precs) @ (0, 5630)',\n", " ' -0.00039+0.00000 (-0.121%, 2.0 precs) @ (1, 2610)',\n", " ' -0.00039-0.00000 (+0.121%, -2.0 precs) @ (0, 2481)',\n", " ' -0.00039-0.00000 (+0.121%, -2.0 precs) @ (0, 3275)',\n", " ' -0.00039+0.00000 (-0.121%, 2.0 precs) @ (1, 2881)',\n", " '  0.00039+0.00000 (+0.121%, 2.0 precs) @ (0, 5294)',\n", " ' -0.00040-0.00000 (+0.121%, -2.0 precs) @ (0, 1318)',\n", " ' -0.00040-0.00000 (+0.120%, -2.0 precs) @ (0, 4163)',\n", " '  0.00040+0.00000 (+0.120%, 2.0 precs) @ (0, 6068)',\n", " '  0.00040+0.00000 (+0.120%, 2.0 precs) @ (0, 5026)',\n", " ' -0.00040-0.00000 (+0.120%, -2.0 precs) @ (0, 2276)',\n", " '  0.00040+0.00000 (+0.119%, 2.0 precs) @ (0, 5056)',\n", " '  0.00040+0.00000 (+0.119%, 2.0 precs) @ (0, 3123)',\n", " '  0.00040+0.00000 (+0.119%, 2.0 precs) @ (0, 4833)',\n", " ' -0.00040-0.00000 (+0.119%, -2.0 precs) @ (0, 291)',\n", " ' -0.00040-0.00000 (+0.119%, -2.0 precs) @ (0, 1289)',\n", " ' -0.00040-0.00000 (+0.119%, -2.0 precs) @ (0, 3782)',\n", " ' -0.00040-0.00000 (+0.119%, -2.0 precs) @ (0, 1892)',\n", " ' -0.00040-0.00000 (+0.119%, -2.0 precs) @ (0, 4110)',\n", " ' -0.00040-0.00000 (+0.119%, -2.0 precs) @ (0, 5063)',\n", " '  0.00040+0.00000 (+0.118%, 2.0 precs) @ (0, 717)',\n", " ' -0.00040-0.00000 (+0.118%, -2.0 precs) @ (0, 2623)',\n", " ' -0.00040-0.00000 (+0.118%, -2.0 precs) @ (0, 5106)',\n", " ' -0.00040-0.00000 (+0.118%, -2.0 precs) @ (0, 5911)',\n", " '  0.00040+0.00000 (+0.118%, 2.0 precs) @ (0, 5215)',\n", " ' -0.00041-0.00000 (+0.118%, -2.0 precs) @ (0, 1382)',\n", " ' -0.00041-0.00000 (+0.118%, -2.0 precs) @ (0, 3977)',\n", " '  0.00041+0.00000 (+0.117%, 2.0 precs) @ (0, 1091)',\n", " ' -0.00041-0.00000 (+0.117%, -2.0 precs) @ (0, 3600)',\n", " ' -0.00041-0.00000 (+0.117%, -2.0 precs) @ (0, 378)',\n", " ' -0.00041-0.00000 (+0.117%, -2.0 precs) @ (0, 221)',\n", " '  0.00041+0.00000 (+0.117%, 2.0 precs) @ (0, 5045)',\n", " '  0.00041+0.00000 (+0.117%, 2.0 precs) @ (0, 5955)',\n", " '  0.00041+0.00000 (+0.117%, 2.0 precs) @ (0, 4967)',\n", " '  0.00041+0.00000 (+0.117%, 2.0 precs) @ (0, 2847)',\n", " ' -0.00041-0.00000 (+0.117%, -2.0 precs) @ (0, 2460)',\n", " ' -0.00041-0.00000 (+0.117%, -2.0 precs) @ (0, 5603)',\n", " ' -0.00041-0.00000 (+0.117%, -2.0 precs) @ (0, 4468)',\n", " '  0.00041+0.00000 (+0.116%, 2.0 precs) @ (0, 140)',\n", " ' -0.00041-0.00000 (+0.116%, -2.0 precs) @ (0, 3678)',\n", " ' -0.00041-0.00000 (+0.116%, -2.0 precs) @ (0, 5657)',\n", " ' -0.00041-0.00000 (+0.116%, -2.0 precs) @ (0, 1949)',\n", " ' -0.00041-0.00000 (+0.116%, -2.0 precs) @ (0, 3925)',\n", " '  0.00041+0.00000 (+0.116%, 2.0 precs) @ (0, 2467)',\n", " '  0.00041+0.00000 (+0.116%, 2.0 precs) @ (0, 3127)',\n", " '  0.00041+0.00000 (+0.116%, 2.0 precs) @ (0, 1203)',\n", " ' -0.00041-0.00000 (+0.116%, -2.0 precs) @ (0, 6116)',\n", " ' -0.00041-0.00000 (+0.116%, -2.0 precs) @ (0, 392)',\n", " '  0.00041+0.00000 (+0.115%, 2.0 precs) @ (0, 1714)',\n", " ' -0.00041-0.00000 (+0.115%, -2.0 precs) @ (0, 4548)',\n", " '  0.00041+0.00000 (+0.115%, 2.0 precs) @ (0, 5926)',\n", " '  0.00041+0.00000 (+0.115%, 2.0 precs) @ (0, 179)',\n", " '  0.00041+0.00000 (+0.115%, 2.0 precs) @ (0, 852)',\n", " ' -0.00042-0.00000 (+0.115%, -2.0 precs) @ (0, 5391)',\n", " ...]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"Sorting diffs\")\n", "# Diff elements: index, Y, prec, abs_diff, rel_diff\n", "diffs.sort(key=lambda x: -abs(x[-2]))\n", "[\n", "    f\"{y:9.5f}{abs_diff:+8.5f} ({rel_diff:+.3%}, {torch.round(prec, decimals=0)} precs) @ {index}\"\n", "    for index, y, prec, abs_diff, rel_diff in diffs\n", "    if (abs(prec) < float('inf') or np.isnan(prec))# and abs(y) > 0.001\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## stochasticity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DBRX state dict from disk\n", "Loaded dMoE state dict into layer\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    dMoE out (<PERSON><PERSON><PERSON><PERSON>([256, 6144])): tensor([[-1.1094,  0.7031,  0.5625,  0.5815,  0.7891,  0.4133, -0.3477,  0.2515,\n", "          1.9639,  2.0645],\n", "        [ 0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,  0.0000,\n", "          0.0000,  0.0000]], device='cuda:0', dtype=torch.float16)\n", "0\n"]}], "source": ["# dMoE stochasticity\n", "input_ = get_input()\n", "sd = load_sd()\n", "moe = load_dmoe(config, sd)\n", "\n", "y_ref = None\n", "max_diff = 0\n", "for _ in range(100):\n", "    with torch.no_grad():\n", "        y_dmoe, dmoe_scores, dmoe_expert_weights, dmoe_top_experts = moe(\n", "            input_, None\n", "        )\n", "        if y_ref is None:\n", "            y_ref = y_dmoe\n", "        max_diff = max(max_diff, (y_dmoe - y_ref).abs().max())\n", "    print(max_diff)\n", "del moe\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DBRX state dict from disk\n", "Initialized DBRX layer\n", "Loaded DBRX state dict into layer\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n", "    DBRX expert 9 out: tensor([[-0.9180, -5.6367, -0.8477, -2.2969,  0.7563,  0.9326,  1.6992, -2.2109,\n", "          0.4487,  0.8955]], device='cuda:0', dtype=torch.float16)\n", "    DBRX expert 10 out: tensor([[-5.0430, -0.5713, -3.2012,  2.0547,  1.5732,  0.9629,  3.1758,  1.5889,\n", "         -5.6797,  3.0117]], device='cuda:0', dtype=torch.float16)\n", "0\n"]}], "source": ["# DBRX stochasticity\n", "input_ = get_input()\n", "sd = load_sd()\n", "ffn = load_dbrx(config, sd)\n", "\n", "y_ref = None\n", "max_diff = 0\n", "for _ in range(100):\n", "    with torch.no_grad():\n", "        y_dbrx, dbrx_scores, dbrx_expert_weights, dbrx_top_experts = ffn(\n", "            input_, None\n", "        )\n", "        if y_ref is None:\n", "            y_ref = y_dbrx\n", "        max_diff = max(max_diff, (y_dbrx - y_ref).abs().max())\n", "    print(max_diff)\n", "del ffn"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
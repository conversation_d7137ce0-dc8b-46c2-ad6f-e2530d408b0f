{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## dMoE"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "No CUDA GPUs are available", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 10\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m distributed \u001b[38;5;28;01mas\u001b[39;00m dist\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# /opt/conda/lib/python3.11/site-packages/megablocks/layers/dmoe.py\u001b[39;00m\n\u001b[0;32m---> 10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m dmoe, mlp, glu\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdmoe\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Arguments, dMoE\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastbackward\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m fs_model_parallel \u001b[38;5;28;01mas\u001b[39;00m mpu\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/megablocks/__init__.py:1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01md<PERSON>e\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmoe\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/megablocks/layers/dmoe.py:1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m common\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m moe\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m dmlp_registry\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/megablocks/layers/common.py:1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmegablocks\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01marguments\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Arguments\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdtype\u001b[39m(args : Arguments):\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/megablocks/layers/arguments.py:18\u001b[0m\n\u001b[1;32m     12\u001b[0m _ALLOWED_BITWIDTHS \u001b[38;5;241m=\u001b[39m (\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m4\u001b[39m, \u001b[38;5;241m8\u001b[39m)\n\u001b[1;32m     14\u001b[0m DEFAULT_ACTIVATION_FN \u001b[38;5;241m=\u001b[39m partial(F\u001b[38;5;241m.\u001b[39mgelu, approximate\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtanh\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     17\u001b[0m \u001b[38;5;129;43m@dataclasses\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdataclass\u001b[49m\n\u001b[0;32m---> 18\u001b[0m \u001b[38;5;28;43;01mclass\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;21;43;01mArguments\u001b[39;49;00m\u001b[43m:\u001b[49m\n\u001b[1;32m     19\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;66;43;03m# Model arguments.\u001b[39;49;00m\n\u001b[1;32m     20\u001b[0m \u001b[43m    \u001b[49m\u001b[43mhidden_size\u001b[49m\u001b[43m \u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mint\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1024\u001b[39;49m\n\u001b[1;32m     21\u001b[0m \u001b[43m    \u001b[49m\u001b[43mffn_hidden_size\u001b[49m\u001b[43m \u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mint\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m4096\u001b[39;49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/megablocks/layers/arguments.py:55\u001b[0m, in \u001b[0;36mArguments\u001b[0;34m()\u001b[0m\n\u001b[1;32m     53\u001b[0m fp16 : \u001b[38;5;28mbool\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m     54\u001b[0m bf16: \u001b[38;5;28mbool\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[0;32m---> 55\u001b[0m device : torch\u001b[38;5;241m.\u001b[39mdevice \u001b[38;5;241m=\u001b[39m \u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcuda\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcurrent_device\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     56\u001b[0m init_method : InitFn \u001b[38;5;241m=\u001b[39m  partial(torch\u001b[38;5;241m.\u001b[39mnn\u001b[38;5;241m.\u001b[39minit\u001b[38;5;241m.\u001b[39mnormal_, mean\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.0\u001b[39m, std\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.02\u001b[39m)\n\u001b[1;32m     57\u001b[0m output_layer_init_method : InitFn \u001b[38;5;241m=\u001b[39m init_method\n", "File \u001b[0;32m/usr/local/local_user_base/lib/python3.11/site-packages/torch/cuda/__init__.py:769\u001b[0m, in \u001b[0;36mcurrent_device\u001b[0;34m()\u001b[0m\n\u001b[1;32m    767\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcurrent_device\u001b[39m() \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mint\u001b[39m:\n\u001b[1;32m    768\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Returns the index of a currently selected device.\"\"\"\u001b[39;00m\n\u001b[0;32m--> 769\u001b[0m     \u001b[43m_lazy_init\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    770\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m torch\u001b[38;5;241m.\u001b[39m_C\u001b[38;5;241m.\u001b[39m_cuda_getDevice()\n", "File \u001b[0;32m/usr/local/local_user_base/lib/python3.11/site-packages/torch/cuda/__init__.py:298\u001b[0m, in \u001b[0;36m_lazy_init\u001b[0;34m()\u001b[0m\n\u001b[1;32m    296\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCUDA_MODULE_LOADING\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m os\u001b[38;5;241m.\u001b[39menviron:\n\u001b[1;32m    297\u001b[0m     os\u001b[38;5;241m.\u001b[39menviron[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCUDA_MODULE_LOADING\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLAZY\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 298\u001b[0m \u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_C\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_cuda_init\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    299\u001b[0m \u001b[38;5;66;03m# Some of the queued calls may reentrantly call _lazy_init();\u001b[39;00m\n\u001b[1;32m    300\u001b[0m \u001b[38;5;66;03m# we need to just return without initializing in that case.\u001b[39;00m\n\u001b[1;32m    301\u001b[0m \u001b[38;5;66;03m# However, we must not let any *other* threads in!\u001b[39;00m\n\u001b[1;32m    302\u001b[0m _tls\u001b[38;5;241m.\u001b[39mis_initializing \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[0;31mRuntimeError\u001b[0m: No CUDA GPUs are available"]}], "source": ["\"\"\"Tests to ensure the gradients are the same in all model-/data-parallel conditions.\"\"\"\n", "\n", "import copy\n", "import os\n", "from importlib import reload\n", "\n", "import torch\n", "from torch import distributed as dist\n", "# /opt/conda/lib/python3.11/site-packages/megablocks/layers/dmoe.py\n", "from megablocks.layers import dmoe, mlp, glu\n", "from megablocks.layers.dmoe import Arguments, dMoE\n", "\n", "from research.fastbackward import fs_model_parallel as mpu\n", "\n", "reload(dmoe)\n", "reload(mlp)\n", "reload(glu)\n", "reload(mpu)\n", "\n", "os.environ['MASTER_ADDR'] = 'localhost'\n", "os.environ['MASTER_PORT'] = '12358'\n", "if not dist.is_initialized():\n", "    dist.init_process_group(backend=\"gloo\", rank=0, world_size=1)\n", "mpu.destroy_model_parallel()\n", "mpu.initialize_model_parallel(1)\n", "\n", "# batch_size = 2\n", "# seq_len = 1_024\n", "# dim = 6_144\n", "# hidden_dim = 16_128\n", "batch_size = 1\n", "seq_len = 2\n", "dim = 8\n", "hidden_dim = 128\n", "expert_count = 2\n", "\n", "megablocks_args = Arguments(\n", "    hidden_size=dim,  # Fbwd's dim is MegaBlocks' hidden_size\n", "    ffn_hidden_size=hidden_dim,  # And fbwd's hidden_dim is MB's ffn_hidden_size\n", "    bias=False,\n", "    return_bias=False,\n", "    activation_fn=torch.nn.functional.silu,\n", "    moe_num_experts=expert_count,\n", "    moe_top_k=1,\n", "    moe_loss_weight=0.05,\n", "    moe_normalize_expert_weights=1.0,\n", "    moe_jitter_eps=0.0,\n", "    mlp_type=\"glu\",\n", "    mlp_impl=\"sparse\",\n", "    # moe_expert_model_parallelism=True,\n", "    moe_expert_model_parallelism=False,\n", "    expert_parallel_group=None,\n", "    fp16=False,\n", "    bf16=True,\n", "    device=torch.device(\"cuda\"),\n", ")\n", "dmoeo = dMoE(megablocks_args)\n", "dmoep = copy.deepcopy(dmoeo)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def split_to_match(a, b):\n", "    \"\"\"\n", "    Find the differing dimension between tensors a and b, and split a into chunks\n", "    so that each chunk has the same size as b along that dimension.\n", "    \"\"\"\n", "    for dim, (sa, sb) in enumerate(zip(a.shape, b.shape)):\n", "        if sa != sb:\n", "            break\n", "    else:\n", "        return [a]\n", "\n", "    chunk_size = sb\n", "    chunks = a.split(chunk_size, dim=dim)\n", "    return chunks"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParallelMLP forward!!!!!\n", "args.moe_expert_model_parallelism=False, 1\n", "ParallelMLP forward!!!!!\n", "args.moe_expert_model_parallelism=False, 1\n", "torch.<PERSON><PERSON>([2, 2])\n", "torch.Size([1, 2])\n", "torch.Size([1, 2])\n", "tensor([[[ 8.6308e-05,  1.8883e-04,  8.2016e-05,  1.8254e-06,  1.1206e-04,\n", "          -1.6785e-04, -7.9632e-05,  1.3638e-04],\n", "         [-2.9182e-04,  9.4891e-05, -6.7234e-05, -1.1253e-04,  5.1737e-05,\n", "          -3.5524e-05,  2.7657e-04, -2.8133e-05]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<ViewBackward0>)\n", "tensor([[[ 1.6975e-04,  1.2302e-04, -2.5177e-04,  6.0558e-05,  1.1921e-04,\n", "          -2.8229e-04,  1.7643e-04,  1.7762e-05]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<ViewBackward0>)\n", "tensor([[[ 4.6158e-04,  2.8133e-05, -1.8501e-04,  1.7357e-04,  6.7711e-05,\n", "          -2.4605e-04, -1.0014e-04,  4.5776e-05]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<SubBackward0>)\n"]}], "source": ["x = torch.randn([batch_size, seq_len, dim], dtype=torch.bfloat16, device=torch.device(\"cuda\"))\n", "# x[:, 0, :] = x[:, 1, :]\n", "y = dmoeo(x)\n", "\n", "x0 = x[:, :seq_len // 2, :].contiguous()\n", "x1 = x[:, seq_len // 2:, :].contiguous()\n", "\n", "y1 = dmoep(x1)\n", "\n", "y_nonzero = y.abs().sum(-1).nonzero()\n", "print(y_nonzero.shape)\n", "y1_nonzero = y1.abs().sum(-1).nonzero()\n", "print(y1_nonzero.shape)\n", "diff = y1 - y[:, 1:, :]\n", "diff_nonzero = diff.abs().sum(-1).nonzero()\n", "print(diff_nonzero.shape)\n", "\n", "print(y)\n", "print(y1)\n", "print(diff)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True router.layer.weight\n", "True experts.mlp.w1\n", "True experts.mlp.w2\n", "True experts.mlp.v1\n", "tensor([[[True, True, True, True, True, True, True, True]]], device='cuda:0')\n"]}], "source": ["for name in dmoeo.state_dict():\n", "    print((dmoeo.state_dict()[name] == dmoep.state_dict()[name]).all().item(), name)\n", "print(x1 == x[:, 1:, :])"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True input [1, 1, 8] [1, 1, 8] tensor([[[-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,\n", "           0.8281]]], device='cuda:0', dtype=torch.bfloat16) tensor([[[-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,\n", "           0.8281]]], device='cuda:0', dtype=torch.bfloat16)\n", "True casted_input [1, 1, 8] [1, 1, 8] tensor([[[-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,\n", "           0.8281]]], device='cuda:0', dtype=torch.bfloat16) tensor([[[-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,\n", "           0.8281]]], device='cuda:0', dtype=torch.bfloat16)\n", "True scores [1, 2] [1, 2] tensor([[0.4961, 0.5039]], device='cuda:0', dtype=torch.bfloat16,\n", "       grad_fn=<SplitBackward0>) tensor([[0.4961, 0.5039]], device='cuda:0', dtype=torch.bfloat16,\n", "       grad_fn=<SoftmaxBackward0>)\n", "True expert_weights [1, 1] [1, 1] tensor([[1.]], device='cuda:0', dtype=torch.bfloat16, grad_fn=<SplitBackward0>) tensor([[1.]], device='cuda:0', dtype=torch.bfloat16, grad_fn=<DivBackward0>)\n", "True top_experts [1, 1] [1, 1] tensor([[1]], device='cuda:0') tensor([[1]], device='cuda:0')\n", "False experts [1, 1, 8] [1, 1, 8] tensor([[[-2.9182e-04,  9.4891e-05, -6.7234e-05, -1.1253e-04,  5.1737e-05,\n", "          -3.5524e-05,  2.7657e-04, -2.8133e-05]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<SplitBackward0>) tensor([[[ 1.6975e-04,  1.2302e-04, -2.5177e-04,  6.0558e-05,  1.1921e-04,\n", "          -2.8229e-04,  1.7643e-04,  1.7762e-05]]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<ViewBackward0>)\n"]}], "source": ["for key, value in dmoeo.act_dict.items():\n", "    actp = dmoep.act_dict[key]\n", "    act = split_to_match(value, actp)[-1]\n", "    assert act.shape == actp.shape, (act.shape, actp.shape)\n", "    print(\n", "        (act == actp).all().item(),\n", "        key,\n", "        list(act.shape),\n", "        list(actp.shape),\n", "        act,\n", "        actp,\n", "    )"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True input torch.Size([1, 1, 8]) torch.<PERSON>ze([1, 1, 8])\n", "True expert_weights torch.Size([1]) torch.Size([1])\n", "True top_experts torch.Size([1]) torch.Size([1])\n", "True bin_ids torch.Size([1]) torch.Size([1])\n", "False indices torch.Size([1]) torch.Size([1])\n", "False tokens_per_expert torch.Size([2]) torch.Size([2])\n", "False padded_tokens_per_expert torch.Size([2]) torch.Size([2])\n", "False padded_bins torch.Size([2]) torch.Size([2])\n", "False bins torch.<PERSON><PERSON>([2]) torch.<PERSON><PERSON>([2])\n", "True pdmlp_view torch.Size([1, 8]) torch.Size([1, 8])\n", "True padded_gather torch.<PERSON><PERSON>([128, 8]) torch.<PERSON>ze([128, 8])\n", "False mlp torch.<PERSON><PERSON>([128, 8]) torch.<PERSON><PERSON>([128, 8])\n", "False padded_scatter torch.Size([1, 8]) torch.Size([1, 8])\n", "False forward_fn torch.Si<PERSON>([1, 8]) torch.Size([1, 8])\n", "False view torch.<PERSON><PERSON>([1, 1, 8]) torch.<PERSON><PERSON>([1, 1, 8])\n", "False after_bias torch.Size([1, 1, 8]) torch.Size([1, 1, 8])\n"]}], "source": ["for key, value in dmoeo.experts.act_dict.items():\n", "    actp = dmoep.experts.act_dict[key]\n", "    act = split_to_match(value, actp)[-1]\n", "    print(\n", "        (act == actp).all().item(),\n", "        key,\n", "        act.shape,\n", "        actp.shape,\n", "        # act,\n", "        # actp,\n", "    )\n"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["top_experts tensor([0, 1], device='cuda:0') tensor([1], device='cuda:0')\n", "bin_ids tensor([0, 1], device='cuda:0', dtype=torch.int32) tensor([1], device='cuda:0', dtype=torch.int32)\n", "indices tensor([0, 1], device='cuda:0', dtype=torch.int32) tensor([0], device='cuda:0', dtype=torch.int32)\n", "tokens_per_expert tensor([1, 1], device='cuda:0', dtype=torch.int32) tensor([0, 1], device='cuda:0', dtype=torch.int32)\n", "padded_tokens_per_expert tensor([128, 128], device='cuda:0', dtype=torch.int32) tensor([  0, 128], device='cuda:0', dtype=torch.int32)\n", "padded_bins tensor([128, 256], device='cuda:0', dtype=torch.int32) tensor([  0, 128], device='cuda:0', dtype=torch.int32)\n", "bins tensor([1, 2], device='cuda:0', dtype=torch.int32) tensor([0, 1], device='cuda:0', dtype=torch.int32)\n"]}], "source": ["# True top_experts torch.Size([1]) torch.Size([1])\n", "# True bin_ids torch.Size([1]) torch.Size([1])\n", "# False indices torch.Si<PERSON>([1]) torch.Size([1])\n", "# False tokens_per_expert torch.Size([2]) torch.Size([2])\n", "# False padded_tokens_per_expert torch.Size([2]) torch.Size([2])\n", "# False padded_bins torch.Size([2]) torch.Size([2])\n", "# False bins torch.<PERSON><PERSON>([2]) torch.<PERSON><PERSON>([2])\n", "print(\"top_experts\", dmoeo.experts.act_dict[\"top_experts\"], dmoep.experts.act_dict[\"top_experts\"])\n", "print(\"bin_ids\", dmoeo.experts.act_dict[\"bin_ids\"], dmoep.experts.act_dict[\"bin_ids\"])\n", "print(\"indices\", dmoeo.experts.act_dict[\"indices\"], dmoep.experts.act_dict[\"indices\"])\n", "print(\"tokens_per_expert\", dmoeo.experts.act_dict[\"tokens_per_expert\"], dmoep.experts.act_dict[\"tokens_per_expert\"])\n", "print(\"padded_tokens_per_expert\", dmoeo.experts.act_dict[\"padded_tokens_per_expert\"], dmoep.experts.act_dict[\"padded_tokens_per_expert\"])\n", "print(\"padded_bins\", dmoeo.experts.act_dict[\"padded_bins\"], dmoep.experts.act_dict[\"padded_bins\"])\n", "print(\"bins\", dmoeo.experts.act_dict[\"bins\"], dmoep.experts.act_dict[\"bins\"])"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('pdmlp_view', tensor([[-0.4512,  0.4082, -0.3770,  0.4355,  0.1396,  1.1250, -0.3828, -0.7109],\n", "        [-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,  0.8281]],\n", "       device='cuda:0', dtype=torch.bfloat16), tensor([[-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,  0.8281]],\n", "       device='cuda:0', dtype=torch.bfloat16))\n", "('padded_scatter', tensor([[ 8.6308e-05,  1.8883e-04,  8.2016e-05,  1.8254e-06,  1.1206e-04,\n", "         -1.6785e-04, -7.9632e-05,  1.3638e-04],\n", "        [-2.9182e-04,  9.4891e-05, -6.7234e-05, -1.1253e-04,  5.1737e-05,\n", "         -3.5524e-05,  2.7657e-04, -2.8133e-05]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<AliasBackward0>), tensor([[ 1.6975e-04,  1.2302e-04, -2.5177e-04,  6.0558e-05,  1.1921e-04,\n", "         -2.8229e-04,  1.7643e-04,  1.7762e-05]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<AliasBackward0>))\n"]}], "source": ["def pair_experts(fn, key):\n", "    return key, fn(dmoeo.experts.act_dict[key]), fn(dmoep.experts.act_dict[key])\n", "\n", "print(pair_experts(nonempty, \"pdmlp_view\"))\n", "# print(pair_experts(nonempty, \"padded_gather\"))\n", "# print(pair_experts(nonempty, \"mlp\"))\n", "print(pair_experts(nonempty, \"padded_scatter\"))"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["((256, 256), (128, 256))"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["dmoeo.experts.topo_saving.size(), dmoep.experts.topo_saving.size()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True x torch.<PERSON><PERSON>([128, 8]) torch.<PERSON><PERSON>([128, 8])\n", "True w1 torch.<PERSON><PERSON>([256, 8]) torch.<PERSON><PERSON>([256, 8])\n", "True v1 torch.<PERSON><PERSON>([256, 8]) torch.<PERSON><PERSON>([256, 8])\n", "True w2 torch.<PERSON><PERSON>([256, 8]) torch.<PERSON><PERSON>([256, 8])\n", "True w1_scale_grad torch.Size([256, 8]) torch.Size([256, 8])\n", "True v1_scale_grad torch.Size([256, 8]) torch.Size([256, 8])\n", "True w2_scale_grad torch.Size([256, 8]) torch.Size([256, 8])\n", "True w1_resolved torch.Si<PERSON>([256, 8]) torch.Size([256, 8])\n", "True v1_resolved torch.Si<PERSON>([256, 8]) torch.Size([256, 8])\n", "True w2_resolved torch.Size([256, 8]) torch.Size([256, 8])\n", "False x1 torch.<PERSON><PERSON>([1, 128, 128]) torch.<PERSON><PERSON>([1, 128, 128])\n", "False x2 torch.<PERSON><PERSON>([1, 128, 128]) torch.<PERSON><PERSON>([1, 128, 128])\n", "False activation_fn_out torch.Size([1, 128, 128]) torch.Size([1, 128, 128])\n", "True x1_out torch.Size([1, 128, 128]) torch.Size([1, 128, 128])\n", "False output torch.<PERSON><PERSON>([128, 8]) torch.<PERSON><PERSON>([128, 8])\n"]}], "source": ["for key, value in dmoeo.experts.mlp.act_dict.items():\n", "    valuep = dmoep.experts.mlp.act_dict[key]\n", "    if not isinstance(value, torch.Tensor):\n", "        value = value.data\n", "        valuep = valuep.data\n", "    actp = valuep\n", "    act = split_to_match(value, actp)[-1]\n", "    print(\n", "        (act == actp).all().item(),\n", "        key,\n", "        act.shape,\n", "        actp.shape,\n", "        # act,\n", "        # actp,\n", "    )\n"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('x', tensor([[-0.4512,  0.4082, -0.3770,  0.4355,  0.1396,  1.1250, -0.3828, -0.7109],\n", "        [-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,  0.8281]],\n", "       device='cuda:0', dtype=torch.bfloat16), tensor([[-0.4922,  0.4688,  1.1328,  0.8203, -0.3730,  0.2734,  0.5469,  0.8281]],\n", "       device='cuda:0', dtype=torch.bfloat16))\n", "('x1', tensor([[-0.0258,  0.0192,  0.0226,  0.0006, -0.0040, -0.0162,  0.0439,  0.0195],\n", "        [ 0.0361, -0.0264,  0.0036, -0.0165, -0.1138, -0.0437,  0.0098, -0.0120],\n", "        [-0.0032,  0.0361, -0.0264,  0.0036, -0.0165, -0.1138, -0.0437,  0.0098],\n", "        [-0.0288, -0.0032,  0.0361, -0.0264,  0.0036, -0.0165, -0.1138, -0.0437],\n", "        [-0.0138,  0.0253,  0.0134, -0.0100, -0.0476, -0.0615, -0.0659,  0.0096]],\n", "       device='cuda:0', dtype=torch.bfloat16, grad_fn=<SliceBackward0>), tensor([[-0.0138,  0.0253,  0.0134, -0.0100, -0.0476, -0.0615, -0.0659,  0.0096],\n", "        [-0.0000,  0.0000, -0.0000,  0.0000, -0.0000, -0.0000,  0.0000,  0.0000],\n", "        [-0.0000, -0.0000,  0.0000, -0.0000,  0.0000, -0.0000, -0.0000,  0.0000]],\n", "       device='cuda:0', dtype=torch.bfloat16, grad_fn=<SliceBackward0>))\n", "('x2', tensor([[ 2.4658e-02, -7.6172e-02, -1.3245e-02, -7.5989e-03, -1.3428e-02,\n", "         -3.5400e-02, -9.5825e-03, -3.0762e-02],\n", "        [-5.7861e-02, -1.8311e-02, -4.5166e-03, -1.6113e-02, -3.7354e-02,\n", "          3.6926e-03,  2.4986e-04, -1.3733e-02],\n", "        [ 1.4160e-02, -5.7861e-02, -1.8311e-02, -4.5166e-03, -1.6113e-02,\n", "         -3.7354e-02,  3.6926e-03,  2.4986e-04],\n", "        [ 4.2725e-03,  1.4160e-02, -5.7861e-02, -1.8311e-02, -4.5166e-03,\n", "         -1.6113e-02, -3.7354e-02,  3.6926e-03],\n", "        [-2.3560e-02,  1.6846e-02, -4.9744e-03,  4.8584e-02, -1.2634e-02,\n", "          3.3691e-02, -2.0752e-02,  2.7100e-02],\n", "        [ 1.2283e-03, -1.2894e-03,  1.2741e-03,  6.1417e-04,  1.3733e-03,\n", "          4.5395e-04, -1.2894e-03,  3.0060e-03],\n", "        [-1.1063e-03,  1.7242e-03, -1.4877e-03,  1.5068e-04, -1.6327e-03,\n", "          1.4725e-03,  1.4648e-03, -1.9550e-04],\n", "        [ 5.5313e-05, -9.1553e-04,  4.4861e-03, -2.2736e-03, -5.9509e-04,\n", "         -1.2512e-03,  1.8463e-03,  1.9073e-03]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<SliceBackward0>), tensor([[-0.0236,  0.0168, -0.0050,  0.0486, -0.0126,  0.0337, -0.0208,  0.0271]],\n", "       device='cuda:0', dtype=torch.bfloat16, grad_fn=<SliceBackward0>))\n", "('activation_fn_out', tensor([[-0.0127,  0.0097,  0.0114,  0.0003, -0.0020, -0.0081,  0.0225,  0.0099],\n", "        [ 0.0184, -0.0130,  0.0018, -0.0082, -0.0537, -0.0214,  0.0049, -0.0060],\n", "        [-0.0016,  0.0184, -0.0130,  0.0018, -0.0082, -0.0537, -0.0214,  0.0049],\n", "        [-0.0142, -0.0016,  0.0184, -0.0130,  0.0018, -0.0082, -0.0537, -0.0214],\n", "        [-0.0068,  0.0128,  0.0067, -0.0050, -0.0232, -0.0298, -0.0320,  0.0048]],\n", "       device='cuda:0', dtype=torch.bfloat16, grad_fn=<SliceBackward0>), tensor([[-0.0068,  0.0128,  0.0067, -0.0050, -0.0232, -0.0298, -0.0320,  0.0048],\n", "        [-0.0000,  0.0000, -0.0000,  0.0000, -0.0000, -0.0000,  0.0000,  0.0000],\n", "        [-0.0000, -0.0000,  0.0000, -0.0000,  0.0000, -0.0000, -0.0000,  0.0000]],\n", "       device='cuda:0', dtype=torch.bfloat16, grad_fn=<SliceBackward0>))\n", "('x1_out', tensor([[-3.1281e-04, -7.4005e-04, -1.5163e-04, -2.2501e-06,  2.6584e-05,\n", "          2.8610e-04, -2.1553e-04, -3.0327e-04],\n", "        [-1.0681e-03,  2.3842e-04, -8.0466e-06,  1.3161e-04,  1.9989e-03,\n", "         -7.8678e-05,  1.2293e-06,  8.1539e-05],\n", "        [-2.2531e-05, -1.0681e-03,  2.3842e-04, -8.0466e-06,  1.3161e-04,\n", "          1.9989e-03, -7.8678e-05,  1.2293e-06],\n", "        [-6.0797e-05, -2.2531e-05, -1.0681e-03,  2.3842e-04, -8.0466e-06,\n", "          1.3161e-04,  1.9989e-03, -7.8678e-05],\n", "        [ 1.6117e-04,  2.1553e-04, -3.3617e-05, -2.4128e-04,  2.9373e-04,\n", "         -1.0071e-03,  6.6376e-04,  1.3065e-04]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<SliceBackward0>), tensor([[ 1.6117e-04,  2.1553e-04, -3.3617e-05, -2.4128e-04,  2.9373e-04,\n", "         -1.0071e-03,  6.6376e-04,  1.3065e-04]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<SliceBackward0>))\n", "('output', tensor([[ 8.6308e-05,  1.8883e-04,  8.2016e-05,  1.8254e-06,  1.1206e-04,\n", "         -1.6785e-04, -7.9632e-05,  1.3638e-04],\n", "        [-1.3161e-04, -2.0266e-05,  1.6117e-04,  4.1962e-04, -1.2970e-04,\n", "          1.7548e-04,  4.8399e-05, -1.2064e-04],\n", "        [ 3.6812e-04, -1.2684e-04,  2.8038e-04,  1.8692e-04, -2.0885e-04,\n", "         -2.7061e-05,  2.0504e-05, -1.4591e-04],\n", "        [ 2.1696e-05, -4.5061e-05, -8.5235e-06,  1.5199e-05, -8.7738e-05,\n", "          1.2779e-04,  1.8692e-04, -2.6584e-05],\n", "        [-2.9182e-04,  9.4891e-05, -6.7234e-05, -1.1253e-04,  5.1737e-05,\n", "         -3.5524e-05,  2.7657e-04, -2.8133e-05],\n", "        [ 7.2956e-05,  2.7275e-04,  1.5831e-04, -4.7684e-05, -8.8692e-05,\n", "          1.9836e-04,  2.5392e-05, -2.3556e-04],\n", "        [-1.6117e-04,  2.5940e-04,  9.1553e-05, -2.1076e-04, -2.3365e-04,\n", "         -8.8692e-05, -1.0777e-04,  2.6321e-04],\n", "        [ 1.8024e-04,  1.6403e-04,  1.9264e-04,  8.5831e-05,  8.9169e-05,\n", "         -7.0095e-05, -1.3486e-06,  6.2943e-05],\n", "        [ 3.9577e-05, -4.7445e-05,  1.6689e-04,  1.0490e-04, -1.8001e-05,\n", "         -8.2493e-05, -1.8477e-05, -4.2439e-05],\n", "        [-1.4496e-04,  4.6015e-05, -2.3842e-04, -2.1744e-04,  7.1049e-05,\n", "         -1.6785e-04, -1.5450e-04, -9.9659e-05],\n", "        [ 1.8120e-05,  2.4605e-04,  6.1035e-05,  2.6345e-05, -2.1267e-04,\n", "         -6.8665e-05,  5.6744e-05, -3.7700e-06],\n", "        [ 3.3379e-05,  9.8228e-05, -2.4605e-04, -3.1471e-04, -2.8992e-04,\n", "         -2.8038e-04, -8.6308e-05, -1.8477e-05],\n", "        [-3.5095e-04, -2.0862e-05,  1.9073e-04, -2.1458e-04,  1.6117e-04,\n", "         -2.5153e-05, -1.8311e-04,  2.4605e-04],\n", "        [ 1.1444e-04, -1.1027e-05,  3.2043e-04,  1.9455e-04,  2.2888e-05,\n", "         -1.9550e-05, -4.4632e-04,  2.2984e-04],\n", "        [-5.7697e-05, -6.8665e-05,  1.3447e-04,  1.7166e-04, -1.8597e-04,\n", "          1.4210e-04, -2.8163e-06, -2.7895e-05],\n", "        [-1.0061e-04,  3.0994e-05, -3.0994e-05, -9.0122e-05,  3.7670e-05,\n", "          1.2755e-05,  6.8188e-05, -3.8147e-05],\n", "        [ 6.5804e-05, -3.0994e-05, -2.5749e-04,  1.8406e-04, -1.2589e-04,\n", "          1.1396e-04,  9.8705e-05, -6.4373e-05],\n", "        [-2.2221e-04,  9.8228e-05, -4.4107e-05, -5.1022e-05, -9.6798e-05,\n", "         -1.0443e-04,  1.4877e-04, -2.4128e-04],\n", "        [ 1.1206e-05,  2.1362e-04, -7.8082e-06, -1.4782e-04, -1.8787e-04,\n", "          2.5558e-04,  2.4796e-05,  1.6689e-04]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<AliasBackward0>), tensor([[ 1.6975e-04,  1.2302e-04, -2.5177e-04,  6.0558e-05,  1.1921e-04,\n", "         -2.8229e-04,  1.7643e-04,  1.7762e-05]], device='cuda:0',\n", "       dtype=torch.bfloat16, grad_fn=<AliasBackward0>))\n"]}], "source": ["import torch\n", "\n", "def nonempty(t):\n", "    if not isinstance(t, torch.Tensor):\n", "        t = t.data\n", "    t = t.reshape(-1, t.shape[-1])  # reshape to (-1, d)\n", "    t = t[(t != 0).any(dim=1)]  # remove all rows that are all 0\n", "    return t[..., :8]\n", "\n", "def nonzero(t):\n", "    if not isinstance(t, torch.Tensor):\n", "        t = t.data\n", "    return t.sum(-1).nonzero()\n", "\n", "def flat_nonzero(t):\n", "    if not isinstance(t, torch.Tensor):\n", "        t = t.data\n", "    return t.reshape(-1, t.shape[-1]).sum(-1).nonzero()\n", "\n", "def shape(t):\n", "    if not isinstance(t, torch.Tensor):\n", "        t = t.data\n", "    return list(t.shape)\n", "\n", "def pair(fn, key):\n", "    return key, fn(dmoeo.experts.mlp.act_dict[key]), fn(dmoep.experts.mlp.act_dict[key])\n", "\n", "\n", "keys = [\"x\", \"x1\", \"x2\", \"activation_fn_out\", \"x1_out\", \"output\"]\n", "for key in keys:\n", "    print(pair(nonempty, key))"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('x', tensor([[  0],\n", "        [128]], device='cuda:0'), tensor([[0]], device='cuda:0'))\n", "('x1', tensor([[  0,   0],\n", "        [  0, 125],\n", "        [  0, 126],\n", "        [  0, 127],\n", "        [  1,   0]], device='cuda:0'), tensor([[  0,   0],\n", "        [  0, 125],\n", "        [  0, 126]], device='cuda:0'))\n", "('x2', tensor([[  0,   0],\n", "        [  0, 125],\n", "        [  0, 126],\n", "        [  0, 127],\n", "        [  1,   0],\n", "        [  1, 125],\n", "        [  1, 126],\n", "        [  1, 127]], device='cuda:0'), tensor([[0, 0]], device='cuda:0'))\n", "('activation_fn_out', tensor([[  0,   0],\n", "        [  0, 125],\n", "        [  0, 126],\n", "        [  0, 127],\n", "        [  1,   0]], device='cuda:0'), tensor([[  0,   0],\n", "        [  0, 125],\n", "        [  0, 126]], device='cuda:0'))\n", "('x1_out', tensor([[  0,   0],\n", "        [  0, 125],\n", "        [  0, 126],\n", "        [  0, 127],\n", "        [  1,   0]], device='cuda:0'), tensor([[0, 0]], device='cuda:0'))\n", "('output', tensor([[  0],\n", "        [125],\n", "        [126],\n", "        [127],\n", "        [128],\n", "        [129],\n", "        [130],\n", "        [131],\n", "        [132],\n", "        [133],\n", "        [134],\n", "        [135],\n", "        [136],\n", "        [137],\n", "        [138],\n", "        [139],\n", "        [140],\n", "        [141],\n", "        [142]], device='cuda:0'), tensor([[0]], device='cuda:0'))\n"]}], "source": ["for key in keys:\n", "    # print(pair(shape, key))\n", "    print(pair(nonzero, key))\n", "    # print(pair(flat_nonzero, key))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MLP"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'd<PERSON><PERSON>' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mdmoeo\u001b[49m\n", "\u001b[0;31mNameError\u001b[0m: name 'dmoeo' is not defined"]}], "source": ["d<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
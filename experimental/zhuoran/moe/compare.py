from __future__ import annotations

import importlib

import torch
from megablocks.layers import arguments, common, dmoe

from experimental.zhuoran.moe import configuration_dbrx, modeling_dbrx

importlib.reload(arguments)
importlib.reload(dmoe)
importlib.reload(configuration_dbrx)
importlib.reload(modeling_dbrx)

_BATCH_SIZE = 1
_SEQ_LEN = 2
_EXPERT_INDEX: int | None = 0
_DTYPE = torch.bfloat16
_DMOE_MLP_IMPL = "grouped"


def get_input():
    x = torch.randn((_BATCH_SIZE, _SEQ_LEN, hidden_size), device="cuda", dtype=_DTYPE)
    return x


def load_sd():
    # State dict containing only the first MoE FFN layer. Weights are:
    # - router.layer.weight
    # - experts.mlp.{w1, v1, w2}
    sd = torch.load("/mnt/efs/augment/user/carl/dbrx-single-ffn.pt")
    print("Loaded DBRX state dict from disk")
    return sd


# Config for the released DBRX model
hidden_size = 6144
config = configuration_dbrx.DbrxFFNConfig(
    ffn_hidden_size=10752,
    moe_num_experts=16,
    moe_top_k=1,
    moe_jitter_eps=0.0,
    moe_loss_weight=0.05,
)
print("Prepared DBRX config")


def load_dbrx(config: configuration_dbrx.DbrxFFNConfig, state_dict: dict):
    ffn = modeling_dbrx.DbrxFFN(hidden_size, config)
    print("Initialized DBRX layer")
    ffn.load_state_dict(state_dict)
    ffn = ffn.to(device="cuda", dtype=_DTYPE)
    print("Loaded DBRX state dict into layer")
    return ffn


def run_dbrx(x: torch.Tensor, ffn: modeling_dbrx.DbrxFFN, with_grad=False):
    gradient_context_manager = torch.enable_grad if with_grad else torch.no_grad
    with gradient_context_manager():
        y_dbrx, dbrx_scores, dbrx_expert_weights, dbrx_top_experts = ffn(
            x, _EXPERT_INDEX
        )
    print("DBRX")
    print(
        y_dbrx.shape,
        dbrx_scores.shape,
        dbrx_expert_weights.shape,
        dbrx_top_experts.shape,
    )
    print(
        y_dbrx.dtype,
        dbrx_scores.dtype,
        dbrx_expert_weights.dtype,
        dbrx_top_experts.dtype,
    )
    del ffn
    return y_dbrx, dbrx_scores, dbrx_expert_weights, dbrx_top_experts


def load_dmoe(config: configuration_dbrx.DbrxFFNConfig, state_dict: dict):
    # Try to match the DBRX model config with the `dMoE` layer from megablocks
    args = arguments.Arguments(
        hidden_size=hidden_size,
        ffn_hidden_size=config.ffn_hidden_size,
        bias=False,
        activation_fn=torch.nn.functional.silu,
        moe_num_experts=config.moe_num_experts,
        moe_top_k=config.moe_top_k,
        moe_normalize_expert_weights=config.moe_normalize_expert_weights,
        moe_loss_weight=config.moe_loss_weight,
        moe_jitter_eps=config.moe_jitter_eps,
        mlp_type="glu",
        mlp_impl=_DMOE_MLP_IMPL,  # Maybe?
    )

    moe = dmoe.dMoE(args)
    moe.load_state_dict(state_dict)
    moe = moe.to(device="cuda", dtype=_DTYPE)
    print("Loaded dMoE state dict into layer")
    return moe


def run_dmoe(x: torch.Tensor, moe: dmoe.dMoE, with_grad=False):
    gradient_context_manager = torch.enable_grad if with_grad else torch.no_grad
    with gradient_context_manager():
        y_dmoe, dmoe_scores, dmoe_expert_weights, dmoe_top_experts = moe(
            x, _EXPERT_INDEX
        )
    print("dMoE")
    print(
        y_dmoe.shape,
        dmoe_scores.shape,
        dmoe_expert_weights.shape,
        dmoe_top_experts.shape,
    )
    print(
        y_dmoe.dtype,
        dmoe_scores.dtype,
        dmoe_expert_weights.dtype,
        dmoe_top_experts.dtype,
    )
    del moe
    return y_dmoe, dmoe_scores, dmoe_expert_weights, dmoe_top_experts


def l2_weght_decay_loss(y: torch.Tensor):
    return torch.norm(y, p=2)


def doe_forward(self, x, expert_index=None):
    # NOTE: If we're going to cast the activations to lower precision
    # do it before we permute the tokens to save bandwidth.
    x = common.cast_if_autocast_enabled(x)

    # Compute the expert scores and assignments.
    scores, expert_weights, top_experts = self.router(x)
    if expert_index is not None:
        top_experts = torch.full_like(top_experts, expert_index)

    # Compute the experts.
    return (
        self.experts(x, scores, expert_weights, top_experts),
        scores,
        expert_weights,
        top_experts,
    )


dmoe.dMoE.forward = doe_forward

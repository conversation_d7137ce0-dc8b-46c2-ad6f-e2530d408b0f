"""Scrape GitHub repo readme pages and construct graphs from the scrapes."""
import base64
import os
import re
import time
import traceback as tb
from dataclasses import dataclass
from typing import Any, Optional, Union
from urllib.parse import urlparse, urlunparse

import requests

ROOT = "sindresorhus/awesome"
if "GITHUB_API_KEY" not in os.environ:
    print("GITHUB_API_KEY environment variable is not set")
    API_TOKEN = None
else:
    print("GITHUB_API_KEY environment variable is set")
    API_TOKEN = os.environ["GITHUB_API_KEY"]


def cleanup_url(url):
    parsed_url = urlparse(url)
    # Reconstruct URL without query and fragment
    site = parsed_url.netloc
    path = parsed_url.path
    scheme = parsed_url.scheme
    # for github sites, truncate at repo level
    if site == "github.com" or ".github.com" in site:
        scheme = "https"
        site = "github.com"
        parts = path.split("/")
        if len(parts) >= 2 and parts[1] in (
            "topics",
            "showcases",
            "collections",
            "features",
            "team",
            "sponsors",
            "trending",
            "readme",
        ):
            # this is actually not a repo.
            return ""
        if len(parts) > 2:
            path = "/".join(parts[:3])
    cleaned_url = urlunparse((scheme, site, path, "", "", ""))
    return cleaned_url


PATTERNS = {
    "org": r"\[\[(http[s]?://[^\]]+)\](?:\[[^\]]*\])?",
    "markdown": r"\[.*?\]\((http[s]?://[^)]+)\)",
    "asciidoc": r"link:(http[s]?://[^\[]+)\[.*?\]",
    "textile": r'"[\w\s]+":(http[s]?://[^"]+)',
    "rst": r"`[^`]+` <(http[s]?://[^>]+)>",
    "pod": r"L<(http[s]?://[^>]+)>",
    "rdoc": r"\{[^\}]*\}\((http[s]?://[^\)]+)\)",
    "txt": r"((?:http|https)://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(?:/\S*)?)",
}

SUFFIXES = {
    "markdown": [
        ".md",
        ".md.txt",
        ".markdown",
        ".mkd",
        ".mkdn",
        ".mkdown",
        ".ron",
        ".mdown",
    ],
    "asciidoc": [".adoc", ".asciidoc", "asc"],
    "org": [".org"],
    "textile": [".textile"],
    "rst": [".rst", ".rest", ".rest.txt", ".rst.txt"],
    "rdoc": [".rdoc"],
    "pod": [".pod"],
    "txt": [".txt", ".text", "mess", "vin", "mediawiki"],
}


def extract_urls(text: str, re_pattern) -> list[str]:
    """Extract the URLs from a markdown file."""
    pattern = re.compile(re_pattern)
    matches = pattern.findall(text)
    # clean up the url and deduplicate
    deduped = set([cleanup_url(url) for url in matches])
    return [url for url in deduped if "://" in url]


@dataclass
class RepoReadme:
    """A GitHub repo readme page."""

    MAIN_BRANCH_CANDIDATES = ["main", "master"]
    FILENAME_CANDIDATES = ["README.md", "readme.md", "Readme.md", "README"]

    url: str
    raw_url: Optional[str] = None
    content: Optional[str] = None
    username: Optional[str] = None
    repo_name: Optional[str] = None
    full_repo_name: Optional[str] = None

    def __init__(
        self, url: str, raw_url: Optional[str] = None, content: Optional[str] = None
    ):
        self.url = url
        self.raw_url = raw_url
        self.content = content
        # this has to be a github repo url, and username and repo name must be present
        # otherwise it is a terminal node and we cannot get any children
        try:
            self.username, self.repo_name = RepoReadme.parse_repo_url(url)
        except ValueError:
            self.username = None
            self.repo_name = None
            self.full_repo_name = None
            return
        self.full_repo_name = f"{self.username}/{self.repo_name}"

    def get_readme_content_from_api(self) -> str:
        """Get the readme content of the repo from the GitHub API."""
        url = f"https://api.github.com/repos/{self.full_repo_name}/readme"
        headers = {
            "User-Agent": "My awesome github.  <EMAIL> my name is ABC",
        }
        if API_TOKEN:
            headers["Authorization"] = f"Bearer {API_TOKEN}"
        resp = requests.get(url, headers=headers, timeout=10)
        if resp.status_code == 404:
            print("This repo has no readme page")
            self.raw_url = None
            return ""
        resp.raise_for_status()
        doc = resp.json()
        self.raw_url = doc["download_url"]
        # decode base64 content from api response
        self.content = base64.b64decode(doc["content"]).decode("utf-8")
        time.sleep(2)
        return self.content

    def to_dict(self) -> dict[str, Union[str, None]]:
        """Serialize the repo to a dict."""
        return {
            "url": self.url,
            "raw_url": self.raw_url,
            "content": self.content,
            "username": self.username,
            "repo_name": self.repo_name,
            "full_repo_name": self.full_repo_name,
        }

    def get_readme_content_from_raw(self, branch: str, filename: str) -> str:
        """Get the readme content of the repo from `raw.githubusercontent.com`."""
        url = f"https://raw.githubusercontent.com/{self.full_repo_name}/{branch}/{filename}"
        resp = requests.get(url, timeout=10)
        time.sleep(0.1)
        resp.raise_for_status()
        self.raw_url = url
        self.content = resp.text
        return self.content

    def get_readme_content(self) -> str:
        """Get the readme content of the repo from `raw.githubusercontent.com`."""
        if self.content is not None:
            return self.content
        # try the usual; only use Rest API when all else fails
        for branch in self.MAIN_BRANCH_CANDIDATES:
            for filename in self.FILENAME_CANDIDATES:
                try:
                    return self.get_readme_content_from_raw(branch, filename)
                except Exception:  # pylint: disable=broad-except
                    time.sleep(0.6)
        # check if the repo exists at all
        repo_url = f"https://github.com/{self.full_repo_name}"
        resp = requests.get(repo_url, timeout=10)
        if resp.status_code == 404:
            print(f"Repo {self.full_repo_name} does not exist")
            self.raw_url = None
            return ""
        return self.get_readme_content_from_api()

    def extract_urls(self):
        """Extract links from the readme content.

        Note that it can be either AsciiDoc, Org, Textile, or Markdown."""
        content = self.get_readme_content()
        if not self.raw_url:
            return []
        name = self.raw_url.split("/")[-1]
        if "." not in name:
            name = name + ".txt"
        for lang, suffixes in SUFFIXES.items():
            for suffix in suffixes:
                if name.lower().endswith(suffix):
                    return extract_urls(content, PATTERNS[lang])
        raise ValueError(f"Unknown suffix for url {self.raw_url}")

    @staticmethod
    def is_valid_repo_url(url: str) -> bool:
        """Check if the url is a valid GitHub repo url."""
        try:
            username, repo_name = RepoReadme.parse_repo_url(url)
            return bool(username and repo_name)
        except ValueError:
            return False

    @staticmethod
    def parse_repo_url(url: str) -> tuple[str, str]:
        """Extract canonical parts of github urls."""
        if not url.startswith("https://github.com/"):
            raise ValueError("URL is not on github")
        path_parts = urlparse(url).path.split("/")
        if len(path_parts) < 3:
            raise ValueError("URL does not contain username and repo name")
        return path_parts[1], path_parts[2]


class Node:
    """A node can either be a github repo (i.e. its readme page) or an external website.

    Nodes are unique to urls.

    A repo can have childrens, but an external webpage link cannot.

    Mainly handles the serialization and deserialization, and the progress of the crawling.
    """

    url: str
    children: list["Node"]
    is_repo: bool
    is_crawled: bool
    repo: Optional[RepoReadme]

    def to_dict(self) -> dict[str, Any]:
        """Serialize the node to a dict."""
        return {
            "url": self.url,
            "is_repo": self.is_repo,
            "is_crawled": self.is_crawled,
            "repo": self.repo.to_dict() if self.repo else None,
            "children": [child.url for child in self.children],
        }

    # lookup all nodes by its url
    _instances: dict[str, "Node"] = {}

    # all uncrawled repo nodes
    _uncrawled: dict[str, "Node"] = {}

    def __new__(cls, url: str):
        """Create a new node, or return the existing one if it already exists."""
        if url in cls._instances:
            return cls._instances[url]
        else:
            node = super().__new__(cls)
            cls._instances[url] = node
            return node

    def __init__(self, url: str):
        """Create the current node now, and start creating children asynchronously."""
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.url = url
        self.children = []
        self.is_repo = RepoReadme.is_valid_repo_url(url)
        self.repo = None
        if self.is_repo:
            self.repo = RepoReadme(url)
            self.is_crawled = False
            self._uncrawled[self.url] = self
        else:
            self.is_crawled = True

    @classmethod
    def serialize_graph(cls) -> list[dict[str, Any]]:
        """Serialize the graph to a list of nodes."""
        return [node.to_dict() for node in cls._instances.values()]

    @classmethod
    def deserialize_graph(cls, node_list: list[dict[str, Any]]) -> None:
        """Deserialize the graph from a list of nodes.

        We need to run two passes, once to create all node objects,
        and the second time to create the connections.
        """
        for node_dict in node_list:
            node = cls(node_dict["url"])
            node.is_crawled = node_dict["is_crawled"]
            node.is_repo = node_dict["is_repo"]
            node.children = []
            repo_doc = node_dict["repo"]
            if repo_doc:
                node.repo = RepoReadme(
                    node.url, repo_doc["raw_url"], repo_doc["content"]
                )
            else:
                node.repo = None
        for node_dict in node_list:
            node = cls._instances[node_dict["url"]]
            node.children = [
                cls._instances[child_url] for child_url in node_dict["children"]
            ]
            if node.is_crawled and node.url in cls._uncrawled:
                del cls._uncrawled[node.url]

    def crawl(self):
        """Crawl the node, and get its children list."""
        if not self.is_repo or self.is_crawled:
            print("url is not a repo or is already crawled: ", self.url)
            return
        try:
            del self._uncrawled[self.url]
        except KeyError:
            print("url not in uncrawled list: ", self.url)
            tb.print_exc()
        self.children = [type(self)(child) for child in self.repo.extract_urls()]
        self.is_crawled = True

    @classmethod
    def crawl_one(cls):
        """Crawl one node, and return it."""
        if not cls._uncrawled:
            print("There is no more uncrawled node")
            return
        node = next(iter(cls._uncrawled.values()))
        try:
            node.crawl()
        except Exception as exc:  # pylint: disable=broad-except
            print("Error occured: ", exc)
            tb.print_exc()
        return node

    @classmethod
    def graph_size(cls):
        """Return the number of nodes in the graph."""
        return len(cls._instances)

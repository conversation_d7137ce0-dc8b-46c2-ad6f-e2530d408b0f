"""A little util to inspect the ID range in a gmane thread."""

import gzip
import json
import sys
from pathlib import Path

input_path = Path(sys.argv[1])
if input_path.suffix == ".gz":
    f = gzip.open(input_path, "rt")
else:
    f = input_path.open()
with f:
    data = [json.loads(line) for line in f]

max_id = -1
min_id = 1000000000000000
threads = 0
messages = 0
for entry in data:
    threads += 1
    for message in entry["messages"]:
        messages += 1
        meta = json.loads(message["meta"])
        my_id = int(meta["xref"].split(":")[-1])
        max_id = max(max_id, my_id)
        min_id = min(min_id, my_id)

print(f"Thread dump id range: {min_id} to {max_id}")
print(f"Total number of threads: {threads}. Total number of messages: {messages}")

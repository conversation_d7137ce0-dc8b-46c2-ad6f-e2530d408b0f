"""Utilities for external context evaluation."""

import os
import time
from typing import List

from base.augment_client.client import AugmentModelClient, ChatResponse
from experimental.xiaolei.external_context_eval.data import ExternalContextEvalData
from research.core.augment_secrets import get_k8s_secret


def get_api_token(
    secret_name: str = "eval-external-context-bot-token",
    namespace: str = "tenant-augment-eng",
):
    config_path = ""
    for path in os.environ.get("KUBECONFIG", "").split(":"):
        if os.path.exists(path):
            config_path = path
            break

    return get_k8s_secret(
        name=secret_name,
        field="api-token",
        namespace=namespace,
        config_path=config_path,
        retries=1,
    )


def get_eval_resp_msg(
    suggested_answer: str,
    context: List[str],
    model_resp: str,
) -> str:
    description = "Evaluate the Response against the provided answer and context info, focusing on accuracy, completeness, and attention to detail. Assign a score from 0 to 10, with 10 being perfect."
    resp_format = "Begin with the score followed by a period. Then provide a concise explanation of the reasoning behind the score."
    context_block = "* Context info:\n" + "\n".join(context) + "\n" if context else ""

    message = f"{description} {resp_format}\n\n* Answer:\n{suggested_answer}\n\n{context_block}\n* Response:\n{model_resp}\n"
    return message


def get_comp_resp_msg(
    suggested_answer: str,
    context: List[str],
    model_resp_a: str,
    model_resp_b: str,
) -> str:
    description = "Compare the two Responses against the provided answer and context, focusing on accuracy, completeness, and attention to detail. Assign a score between -5 and 5, with negative scores favoring Response A and positive scores favoring Response B. The absolute value of the score reflects the strength of the preference, with 0 indicating no difference."
    resp_format = "Begin with the score followed by a period. Then provide a concise explanation of the reasoning behind the score."
    print_context = "\n".join(context) if context else "None"
    message = f"{description} {resp_format}\n\n* Answer:\n{suggested_answer}\n\n* Context info:\n{print_context}\n\n* Response A:\n{model_resp_a}\n\n* Response B:\n{model_resp_b}\n"
    return message


def evaluate_data_point(
    chat_model: AugmentModelClient,
    eval_model: AugmentModelClient,
    dp: ExternalContextEvalData,
    blobs,
    user_guided_blobs,
    external_source_ids: List[str],
    verbose: bool = False,
    retries: int = 3,
    retry_sleep_secs: int = 2,
    retry_backoff_factor: int = 2,
    disable_auto_external_sources: bool = True,
) -> List[ChatResponse]:
    e = None
    for retry in range(retries):
        try:
            model_resp = chat_model.chat(
                selected_code=dp.selected_code,
                message=dp.message,
                prefix=dp.content,
                suffix="",
                path=dp.path,
                blobs=blobs,
                user_guided_blobs=user_guided_blobs,
                context_code_exchange_request_id="new",
                external_source_ids=external_source_ids,
                disable_auto_external_sources=disable_auto_external_sources,
            )
            break
        except Exception as e:
            print(f"Retry {retry+1}/{retries} failed: {e}")
            time.sleep(retry_sleep_secs * retry_backoff_factor**retry)
            retry_sleep_secs *= retry_backoff_factor
    else:
        print(f"Last retry failed: {e}")
        raise RuntimeError(
            f"Failed to get correct response from chat after {retries} retries"
        )

    if verbose:
        if external_source_ids:
            print("WITH external sources:")
            for src_id in external_source_ids:
                print(src_id)
            print("-" * 80)
        else:
            print("WITHOUT external sources:")
        print(model_resp.text)
        print("-" * 80)
        print(f"^ RI: {model_resp.request_id}")
        print("-" * 80)

    eval_resp_msg = get_eval_resp_msg(
        dp.answer,
        dp.context,
        model_resp.text,
    )
    eval_resp = eval_model.chat(
        selected_code="",
        message=eval_resp_msg,
        prefix="",
        suffix="",
        path="",
        disable_auto_external_sources=True,
    )

    return [model_resp, eval_resp]


def get_score_from_resp(resp: ChatResponse) -> int:
    score_text = resp.text.split(sep=".", maxsplit=2)[0].strip(" `^#*")
    if ":" in score_text:
        score_text = score_text.split(":")[1]
    return int(score_text)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "import json\n", "from base.augment_client.client import AugmentClient\n", "from experimental.xiaolei.external_context_eval.data import get_eval_dataset\n", "from experimental.xiaolei.external_context_eval.utils import get_api_token\n", "from research.infra.cfg.clusters import Clusters\n", "\n", "# Evaluation settings\n", "AUGMENT_URL = \"https://dev-xiaolei.us-central.api.augmentcode.com\"\n", "# AUGMENT_URL = \"https://staging-shard-0.api.augmentcode.com\"\n", "AUGMENT_TOKEN = get_api_token(namespace=Clusters.load_current().main_namespace)\n", "\n", "CHAT_MODEL_NAME = \"claude-sonnet-3-5-16k-chat\"\n", "EVAL_MODEL_NAME = \"gemini-1-5-flash-0827-16k-chat\"\n", "\n", "EVAL_IDS = []  # empty list means to evaluate all\n", "VERBOSE = False\n", "\n", "\n", "client = AugmentClient(\n", "    url=AUGMENT_URL,\n", "    token=AUGMENT_TOKEN,\n", "    user_agent=\"Augment-EvalHarness/external-context\",\n", ")\n", "\n", "chat_model = client.client_for_model(CHAT_MODEL_NAME)\n", "eval_model = client.client_for_model(EVAL_MODEL_NAME)\n", "\n", "print(\"Available models:\")\n", "model_names = sorted([m.name for m in client.get_models().models])\n", "for name in model_names:\n", "    print(f\"  - {name}\")\n", "print(\"=\" * 50)\n", "\n", "if CHAT_MODEL_NAME not in model_names:\n", "    raise ValueError(\"Chat model not found!\")\n", "\n", "if EVAL_MODEL_NAME not in model_names:\n", "    raise ValueError(\"Eval model not found!\")\n", "\n", "print(\"Available external source types:\")\n", "for src in client.list_external_source_types():\n", "    print(f\"  - {src}\")\n", "print(\"=\" * 50)\n", "\n", "eval_dataset = get_eval_dataset()\n", "total_count = len(eval_dataset)\n", "print(f\"There are {total_count} eval data points.\")\n", "\n", "counter = Counter([\"|\".join(dp.docsets) for dp in eval_dataset])\n", "for k, v in counter.most_common():\n", "    # print(f'\"docset://{k}\",')\n", "    print(f\"  - {k}: {v} ({v/total_count*100}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from base.augment_client.client import ChatResponse, ExternalSource, UploadContent\n", "import experimental.xiaolei.external_context_eval.utils as utils\n", "\n", "if EVAL_IDS:\n", "    ds = [dp for dp in eval_dataset if dp.id in EVAL_IDS]\n", "else:\n", "    ds = eval_dataset\n", "\n", "print(f\"Chat model = {CHAT_MODEL_NAME}\")\n", "print(f\"Eval model = {EVAL_MODEL_NAME}\")\n", "\n", "# each result is a [model_resp, eval_resp] sequence\n", "results_wo: List[List[ChatResponse]] = []\n", "results_implicit: List[List[ChatResponse]] = []\n", "results_w: List[List[ChatResponse]] = []\n", "scores_compare: List[int] = []\n", "\n", "with open(\n", "    f\"/home/<USER>/augment/experimental/xiaolei/external_context_eval/eval_result_{CHAT_MODEL_NAME}_eval_{EVAL_MODEL_NAME}_2.jsonl\",\n", "    \"w+\",\n", ") as f:\n", "    for data_point in ds:\n", "        if VERBOSE:\n", "            print(\"=\" * 80)\n", "            print(f\"[ID {data_point.id}] Question: {data_point.message}\")\n", "            print(\"-\" * 80)\n", "            print(f\"Suggested answer: {data_point.answer}\")\n", "            print(\"-\" * 80)\n", "\n", "        blob_name, blobs, user_guided_blobs = \"\", None, None\n", "        if data_point.content != \"\":\n", "            assert data_point.path != \"\"\n", "            upload_content = UploadContent(\n", "                content=data_point.content,\n", "                path_name=data_point.path,\n", "            )\n", "            blob_names = client.batch_upload(blobs=[upload_content])\n", "            assert len(blob_names) == 1\n", "            blobs = {\n", "                \"added_blobs\": [blob_names[0]],\n", "                \"deleted_blobs\": [],\n", "                \"checkpoint_id\": None,\n", "            }\n", "            user_guided_blobs = [blob_names[0]]\n", "\n", "        # without external source\n", "        model_resp_wo, eval_resp_wo = utils.evaluate_data_point(\n", "            chat_model,\n", "            eval_model,\n", "            data_point,\n", "            blobs,\n", "            user_guided_blobs,\n", "            external_source_ids=[],\n", "            disable_auto_external_sources=True,\n", "        )\n", "        results_wo.append([model_resp_wo, eval_resp_wo])\n", "\n", "        model_resp_implicit, eval_resp_implicit = utils.evaluate_data_point(\n", "            chat_model,\n", "            eval_model,\n", "            data_point,\n", "            blobs,\n", "            user_guided_blobs,\n", "            external_source_ids=[\"docset://dummy-docset\"],\n", "            disable_auto_external_sources=False,\n", "        )\n", "        results_implicit.append([model_resp_implicit, eval_resp_implicit])\n", "\n", "        # explicit docsets\n", "        model_resp_w, eval_resp_w = utils.evaluate_data_point(\n", "            chat_model,\n", "            eval_model,\n", "            data_point,\n", "            blobs,\n", "            user_guided_blobs,\n", "            external_source_ids=[f\"docset://{docset}\" for docset in data_point.docsets],\n", "        )\n", "        results_w.append([model_resp_w, eval_resp_w])\n", "\n", "        comp_msg = utils.get_comp_resp_msg(\n", "            data_point.answer, data_point.context, model_resp_wo.text, model_resp_w.text\n", "        )\n", "        eval_resp_comp = eval_model.chat(\n", "            selected_code=\"\", message=comp_msg, prefix=\"\", suffix=\"\", path=\"\"\n", "        )\n", "        scores_compare.append(utils.get_score_from_resp(eval_resp_comp))\n", "\n", "        score_wo = utils.get_score_from_resp(results_wo[-1][1])\n", "        score_w = utils.get_score_from_resp(results_w[-1][1])\n", "        score_implicit = utils.get_score_from_resp(results_implicit[-1][1])\n", "        print(\n", "            f\"{data_point.id}: w/o={score_wo} | w/={score_w} | implicit={score_implicit} | compare={scores_compare[-1]} | docset={data_point.docsets[0]}\"\n", "        )\n", "        f.write(\n", "            json.dumps(\n", "                {\n", "                    \"id\": data_point.id,\n", "                    \"question\": data_point.message,\n", "                    \"reference_answer\": data_point.answer,\n", "                    \"context\": data_point.context,\n", "                    \"response_without_docset\": results_wo[-1][0].text,\n", "                    \"response_with_docset\": results_w[-1][0].text,\n", "                    \"response_with_implicit_docset\": results_implicit[-1][0].text,\n", "                    \"docset\": data_point.docsets[0],\n", "                    \"score_with_vs_without\": scores_compare[-1],\n", "                    \"score_without_docset\": score_wo,\n", "                    \"score_with_docset\": score_w,\n", "                    \"score_with_implicit_docset\": score_implicit,\n", "                }\n", "            )\n", "            + \"\\n\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["average_score_wo = sum(utils.get_score_from_resp(r[1]) for r in results_wo) / len(\n", "    results_wo\n", ")\n", "average_score_w = sum(utils.get_score_from_resp(r[1]) for r in results_w) / len(\n", "    results_w\n", ")\n", "average_score_implicit = sum(\n", "    utils.get_score_from_resp(r[1]) for r in results_implicit\n", ") / len(results_implicit)\n", "average_score_compare = sum(scores_compare) / len(scores_compare)\n", "compare_higher = sum(1 for s in scores_compare if s > 0) / len(scores_compare)\n", "\n", "\n", "implicit_higher = sum(\n", "    1\n", "    for s1, s2 in zip(results_wo, results_implicit)\n", "    if utils.get_score_from_resp(s1[1]) < utils.get_score_from_resp(s2[1])\n", ") / len(results_wo)\n", "print(f\"Average score w/o docset: {average_score_wo}\")\n", "print(f\"Average score w/ docset: {average_score_w}\")\n", "print(f\"Average score w/ implicit docset: {average_score_implicit}\")\n", "print(f\"Average score compare: {average_score_compare}\")\n", "print(f\"With docset higher: {compare_higher}\")\n", "print(f\"Implicit docset higher: {implicit_higher}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["average_score_wo = sum(utils.get_score_from_resp(r[1]) for r in results_wo) / len(\n", "    results_wo\n", ")\n", "average_score_w = sum(utils.get_score_from_resp(r[1]) for r in results_w) / len(\n", "    results_w\n", ")\n", "average_score_implicit = sum(\n", "    utils.get_score_from_resp(r[1]) for r in results_implicit\n", ") / len(results_implicit)\n", "average_score_compare = sum(scores_compare) / len(scores_compare)\n", "compare_higher = sum(1 for s in scores_compare if s > 0) / len(scores_compare)\n", "\n", "\n", "implicit_higher = sum(\n", "    1\n", "    for s1, s2 in zip(results_wo, results_implicit)\n", "    if utils.get_score_from_resp(s1[1]) < utils.get_score_from_resp(s2[1])\n", ") / len(results_wo)\n", "print(f\"Average score w/o docset: {average_score_wo}\")\n", "print(f\"Average score w/ docset: {average_score_w}\")\n", "print(f\"Average score w/ implicit docset: {average_score_implicit}\")\n", "print(f\"Average score compare: {average_score_compare}\")\n", "print(f\"With docset higher: {compare_higher}\")\n", "print(f\"Implicit docset higher: {implicit_higher}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "c = Counter(scores_compare)\n", "print({key: value / len(scores_compare) for key, value in c.items()})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["average_score_wo = sum(utils.get_score_from_resp(r[1]) for r in results_wo) / len(\n", "    results_wo\n", ")\n", "average_score_w = sum(utils.get_score_from_resp(r[1]) for r in results_w) / len(\n", "    results_w\n", ")\n", "average_score_implicit = sum(\n", "    utils.get_score_from_resp(r[1]) for r in results_implicit\n", ") / len(results_implicit)\n", "average_score_compare = sum(scores_compare) / len(scores_compare)\n", "compare_higher = sum(1 for s in scores_compare if s > 0) / len(scores_compare)\n", "\n", "\n", "implicit_higher = sum(\n", "    1\n", "    for s1, s2 in zip(results_wo, results_implicit)\n", "    if utils.get_score_from_resp(s1[1]) < utils.get_score_from_resp(s2[1])\n", ") / len(results_wo)\n", "print(f\"Average score w/o docset: {average_score_wo}\")\n", "print(f\"Average score w/ docset: {average_score_w}\")\n", "print(f\"Average score w/ implicit docset: {average_score_implicit}\")\n", "print(f\"Average score compare: {average_score_compare}\")\n", "print(f\"With docset higher: {compare_higher}\")\n", "print(f\"Implicit docset higher: {implicit_higher}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# format for easy copyig to spreadsheet\n", "for id in range(len(results_wo)):\n", "    s1 = utils.get_score_from_resp(results_wo[id][1])\n", "    s2 = utils.get_score_from_resp(results_w[id][1])\n", "    s3 = scores_compare[id]\n", "    print(f\"{s1},{s2},{s3}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for id in range(len(results_wo)):\n", "    docset = eval_dataset[id].docsets[0]\n", "    print(f\"id={id+1} | docset={docset} | compare={scores_compare[id]}\")\n", "\n", "    rid = results_wo[id][0].request_id\n", "    score = utils.get_score_from_resp(results_wo[id][1])\n", "    print(f\"w/o={score} | request_id={rid}\")\n", "\n", "    rid = results_w[id][0].request_id\n", "    score = utils.get_score_from_resp(results_w[id][1])\n", "    print(f\"w/ ={score} | request_id={rid}\")\n", "    print(\"=\" * 80)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
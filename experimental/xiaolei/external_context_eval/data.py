"""The external context evaluation data & dataset."""

import json
from dataclasses import dataclass
from pathlib import Path
from typing import List

EVAL_DATASET_FILENAME = "dataset.json"
EVAL_DATASET_PATH = Path(__file__).parent / EVAL_DATASET_FILENAME


@dataclass
class ExternalContextEvalData:
    """One data point for external context evaluation."""

    id: int
    """The unique id of the data point."""

    message: str
    """The user message to the chat model, usually a question."""

    path: str
    """The mocked path of the single file in repo."""

    content: str
    """The mocked content of the file."""

    selected_code: str
    """The mocked selected code in the file."""

    original_rid: str
    """The original reported request id, usually contains hallucination."""

    docsets: List[str]
    """The list of docsets to use as external sources."""

    answer: str
    """The complete reference answer to use for evaluation."""

    context: List[str]
    """The list of context text useful for evaluation."""

    note: str
    """Additional note about the data point."""


def get_eval_dataset() -> List[ExternalContextEvalData]:
    json_data = json.load(open(EVAL_DATASET_PATH))
    ds = [ExternalContextEvalData(**dp) for dp in json_data]
    validate_eval_dataset(ds)
    return ds


def validate_eval_dataset(ds: List[ExternalContextEvalData]):
    ds_size = len(ds)
    assert ds_size > 0, "Empty dataset"
    assert len(set(dp.id for dp in ds)) == ds_size, "Duplicate ids"
    for dp in ds:
        assert dp.id > 0 and dp.id <= ds_size, f"Invalid id: {dp.id}"
        assert dp.message != "", f"Empty message in id {dp.id}"
        assert len(dp.docsets) == len(
            set(dp.docsets)
        ), f"Duplicate docsets in id {dp.id}"
        assert len(dp.context) == len(
            set(dp.context)
        ), f"Duplicate context in id {dp.id}"

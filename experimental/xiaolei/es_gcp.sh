# For running gmane scraping on GCP

code_path=/home/<USER>/augment/research/data/collection/nntp_scrapers

SERVER=eternal-september
for i in {1..100}; do
  echo "Iteration $i"
  python3 $code_path/nntp_scraper.py --storage-path ~/nntp/ --groups 1 --servers $SERVER --username augmentcode1 --password bettercode1
  gsutil -m mv ~/nntp/server\=$SERVER/*.gz "gs://nntp_scrape/date=$(date +%Y-%m-%d)/server=$SERVER/"
done
